{"_args": [["browserify-sign@4.0.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-sign@4.0.4", "_id": "browserify-sign@4.0.4", "_inBundle": false, "_integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "_location": "/browserify-sign", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-sign@4.0.4", "name": "browserify-sign", "escapedName": "browserify-sign", "rawSpec": "4.0.4", "saveSpec": null, "fetchSpec": "4.0.4"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-sign/-/browserify-sign-4.0.4.tgz", "_spec": "4.0.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "browser": "browser/index.js", "bugs": {"url": "https://github.com/crypto-browserify/browserify-sign/issues"}, "dependencies": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}, "description": "adds node crypto signing for browsers", "devDependencies": {"nyc": "^6.1.1", "standard": "^6.0.8", "tape": "^4.5.1"}, "files": ["browser", "index.js", "algos.js"], "homepage": "https://github.com/crypto-browserify/browserify-sign#readme", "license": "ISC", "main": "index.js", "name": "browserify-sign", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/browserify-sign.git"}, "scripts": {"coverage": "nyc npm run unit", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "version": "4.0.4"}