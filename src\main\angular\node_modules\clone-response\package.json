{"_args": [["clone-response@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "clone-response@1.0.2", "_id": "clone-response@1.0.2", "_inBundle": false, "_integrity": "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=", "_location": "/clone-response", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clone-response@1.0.2", "name": "clone-response", "escapedName": "clone-response", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/cacheable-request"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/clone-response/-/clone-response-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "dependencies": {"mimic-response": "^1.0.0"}, "description": "Clone a Node.js HTTP response stream", "devDependencies": {"ava": "^0.22.0", "coveralls": "^2.13.1", "create-test-server": "^2.0.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "homepage": "https://github.com/lukechilds/clone-response", "keywords": ["clone", "duplicate", "copy", "response", "HTTP", "stream"], "license": "MIT", "main": "src/index.js", "name": "clone-response", "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "1.0.2", "xo": {"extends": "xo-lukechilds"}}