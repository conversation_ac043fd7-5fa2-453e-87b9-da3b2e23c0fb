{"version": 3, "file": "animations.es5.js", "sources": ["../../../../../packages/platform-browser/animations/public_api.ts", "../../../../../packages/platform-browser/animations/src/animations.ts", "../../../../../packages/platform-browser/animations/src/private_export.ts", "../../../../../packages/platform-browser/animations/src/module.ts", "../../../../../packages/platform-browser/animations/src/providers.ts", "../../../../../packages/platform-browser/animations/src/animation_renderer.ts", "../../../../../packages/platform-browser/animations/src/animation_builder.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the animation package.\n */\nexport {BrowserAnimationsModule,NoopAnimationsModule,ɵBrowserAnimationBuilder,ɵBrowserAnimationFactory,ɵAnimationRenderer,ɵAnimationRendererFactory} from './src/animations';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {BrowserAnimationsModule, NoopAnimationsModule} from './module';\n\nexport {ɵBrowserAnimationBuilder,ɵBrowserAnimationFactory,ɵAnimationRenderer,ɵAnimationRendererFactory} from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory} from './animation_builder';\nexport {AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory} from './animation_renderer';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {BrowserModule} from '@angular/platform-browser';\n\nimport {BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS} from './providers';\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class BrowserAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationsModule.ctorParameters;\n}\n\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction NoopAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nNoopAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNoopAnimationsModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AnimationBuilder} from '@angular/animations';\nimport {AnimationDriver, ɵAnimationEngine as AnimationEngine, ɵAnimationStyleNormalizer as AnimationStyleNormalizer, ɵNoopAnimationDriver as NoopAnimationDriver, ɵWebAnimationsDriver as WebAnimationsDriver, ɵWebAnimationsStyleNormalizer as WebAnimationsStyleNormalizer, ɵsupportsWebAnimations as supportsWebAnimations} from '@angular/animations/browser';\nimport {Injectable, NgZone, Provider, RendererFactory2} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '@angular/platform-browser';\n\nimport {BrowserAnimationBuilder} from './animation_builder';\nimport {AnimationRendererFactory} from './animation_renderer';\nexport class InjectableAnimationEngine extends AnimationEngine {\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    super(driver, normalizer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: AnimationDriver, },\n{type: AnimationStyleNormalizer, },\n];\n}\n\nfunction InjectableAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nInjectableAnimationEngine.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nInjectableAnimationEngine.ctorParameters;\n}\n\n/**\n * @return {?}\n */\nexport function instantiateSupportedAnimationDriver() {\n  if (supportsWebAnimations()) {\n    return new WebAnimationsDriver();\n  }\n  return new NoopAnimationDriver();\n}\n/**\n * @return {?}\n */\nexport function instantiateDefaultStyleNormalizer() {\n  return new WebAnimationsStyleNormalizer();\n}\n/**\n * @param {?} renderer\n * @param {?} engine\n * @param {?} zone\n * @return {?}\n */\nexport function instantiateRendererFactory(\n    renderer: DomRendererFactory2, engine: AnimationEngine, zone: NgZone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst /** @type {?} */ SHARED_ANIMATION_PROVIDERS: Provider[] = [\n  {provide: AnimationBuilder, useClass: BrowserAnimationBuilder},\n  {provide: AnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer},\n  {provide: AnimationEngine, useClass: InjectableAnimationEngine}, {\n    provide: RendererFactory2,\n    useFactory: instantiateRendererFactory,\n    deps: [DomRendererFactory2, AnimationEngine, NgZone]\n  }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nexport const BROWSER_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useFactory: instantiateSupportedAnimationDriver},\n  ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nexport const BROWSER_NOOP_ANIMATIONS_PROVIDERS: Provider[] =\n    [{provide: AnimationDriver, useClass: NoopAnimationDriver}, ...SHARED_ANIMATION_PROVIDERS];\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTriggerMetadata} from '@angular/animations';\nimport {ɵAnimationEngine as AnimationEngine} from '@angular/animations/browser';\nimport {Injectable, NgZone, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2} from '@angular/core';\nexport class AnimationRendererFactory implements RendererFactory2 {\nprivate _currentId: number = 0;\nprivate _microtaskId: number = 1;\nprivate _animationCallbacksBuffer: [(e: any) => any, any][] = [];\nprivate _rendererCache = new Map<Renderer2, BaseAnimationRenderer>();\n/**\n * @param {?} delegate\n * @param {?} engine\n * @param {?} _zone\n */\nconstructor(\nprivate delegate: RendererFactory2,\nprivate engine: AnimationEngine,\nprivate _zone: NgZone) {\n    engine.onRemovalComplete = (element: any, delegate: Renderer2) => {\n      // Note: if an component element has a leave animation, and the component\n      // a host leave animation, the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      if (delegate && delegate.parentNode(element)) {\n        delegate.removeChild(element.parentNode, element);\n      }\n    };\n  }\n/**\n * @param {?} hostElement\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(hostElement: any, type: RendererType2): Renderer2 {\n    const /** @type {?} */ EMPTY_NAMESPACE_ID = '';\n\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const /** @type {?} */ delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let /** @type {?} */ renderer: BaseAnimationRenderer|undefined = this._rendererCache.get(delegate);\n      if (!renderer) {\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine);\n        // only cache this result when the base renderer is used\n        this._rendererCache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n\n    const /** @type {?} */ componentId = type.id;\n    const /** @type {?} */ namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n\n    this.engine.register(namespaceId, hostElement);\n    const /** @type {?} */ animationTriggers = /** @type {?} */(( type.data['animation'] as AnimationTriggerMetadata[]));\n    animationTriggers.forEach(\n        trigger => this.engine.registerTrigger(\n            componentId, namespaceId, hostElement, trigger.name, trigger));\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n/**\n * @return {?}\n */\nbegin() {\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n/**\n * @return {?}\n */\nprivate _scheduleCountTask() {\n    Zone.current.scheduleMicroTask('incremenet the animation microtask', () => this._microtaskId++);\n  }\n/**\n * @param {?} count\n * @param {?} fn\n * @param {?} data\n * @return {?}\n */\nscheduleListenerCallback(count: number, fn: (e: any) => any, data: any) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n/**\n * @return {?}\n */\nend() {\n    this._zone.runOutsideAngular(() => {\n      this._scheduleCountTask();\n      this.engine.flush(this._microtaskId);\n    });\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this.engine.whenRenderingDone(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n{type: AnimationEngine, },\n{type: NgZone, },\n];\n}\n\nfunction AnimationRendererFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRendererFactory.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAnimationRendererFactory.ctorParameters;\n/** @type {?} */\nAnimationRendererFactory.prototype._currentId;\n/** @type {?} */\nAnimationRendererFactory.prototype._microtaskId;\n/** @type {?} */\nAnimationRendererFactory.prototype._animationCallbacksBuffer;\n/** @type {?} */\nAnimationRendererFactory.prototype._rendererCache;\n/** @type {?} */\nAnimationRendererFactory.prototype.delegate;\n/** @type {?} */\nAnimationRendererFactory.prototype.engine;\n/** @type {?} */\nAnimationRendererFactory.prototype._zone;\n}\n\nexport class BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\n      protected namespaceId: string,\npublic delegate: Renderer2,\npublic engine: AnimationEngine) {\n    this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode !(n) : null;\n  }\n/**\n * @return {?}\n */\nget data() { return this.delegate.data; }\n\n  destroyNode: ((n: any) => void)|null;\n/**\n * @return {?}\n */\ndestroy(): void {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n  }\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string|null|undefined) {\n    return this.delegate.createElement(name, namespace);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string) { return this.delegate.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string) { return this.delegate.createText(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, true);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: any) { return this.delegate.selectRootElement(selectorOrNode); }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any) { return this.delegate.parentNode(node); }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any) { return this.delegate.nextSibling(node); }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string|null|undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string|null|undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { this.delegate.addClass(el, name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { this.delegate.removeClass(el, name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?=} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?=} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    this.delegate.setProperty(el, name, value);\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { this.delegate.setValue(node, value); }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction BaseAnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseAnimationRenderer.prototype.destroyNode;\n/** @type {?} */\nBaseAnimationRenderer.prototype.namespaceId;\n/** @type {?} */\nBaseAnimationRenderer.prototype.delegate;\n/** @type {?} */\nBaseAnimationRenderer.prototype.engine;\n}\n\nexport class AnimationRenderer extends BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} factory\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\npublic factory: AnimationRendererFactory, namespaceId: string, delegate: Renderer2,\n      engine: AnimationEngine) {\n    super(namespaceId, delegate, engine);\n    this.namespaceId = namespaceId;\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == '@') {\n      name = name.substr(1);\n      this.engine.setProperty(this.namespaceId, el, name, value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, eventName: string, callback: (event: any) => any):\n      () => void {\n    if (eventName.charAt(0) == '@') {\n      const /** @type {?} */ element = resolveElementFromTarget(target);\n      let /** @type {?} */ name = eventName.substr(1);\n      let /** @type {?} */ phase = '';\n      if (name.charAt(0) != '@') {  // transition-specific\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const /** @type {?} */ countId = ( /** @type {?} */((event as any)))['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction AnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRenderer.prototype.factory;\n}\n\n/**\n * @param {?} target\n * @return {?}\n */\nfunction resolveElementFromTarget(target: 'window' | 'document' | 'body' | any): any {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\n/**\n * @param {?} triggerName\n * @return {?}\n */\nfunction parseTriggerCallbackName(triggerName: string) {\n  const /** @type {?} */ dotIndex = triggerName.indexOf('.');\n  const /** @type {?} */ trigger = triggerName.substring(0, dotIndex);\n  const /** @type {?} */ phase = triggerName.substr(dotIndex + 1);\n  return [trigger, phase];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationBuilder, AnimationFactory, AnimationMetadata, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, sequence} from '@angular/animations';\nimport {Injectable, RendererFactory2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {AnimationRenderer} from './animation_renderer';\nexport class BrowserAnimationBuilder extends AnimationBuilder {\nprivate _nextAnimationId = 0;\nprivate _renderer: AnimationRenderer;\n/**\n * @param {?} rootRenderer\n */\nconstructor(rootRenderer: RendererFactory2) {\n    super();\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {animation: []}\n    } as RendererType2;\n    this._renderer = rootRenderer.createRenderer(document.body, typeData) as AnimationRenderer;\n  }\n/**\n * @param {?} animation\n * @return {?}\n */\nbuild(animation: AnimationMetadata|AnimationMetadata[]): AnimationFactory {\n    const /** @type {?} */ id = this._nextAnimationId.toString();\n    this._nextAnimationId++;\n    const /** @type {?} */ entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n];\n}\n\nfunction BrowserAnimationBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationBuilder.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationBuilder.ctorParameters;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._nextAnimationId;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._renderer;\n}\n\nexport class BrowserAnimationFactory extends AnimationFactory {\n/**\n * @param {?} _id\n * @param {?} _renderer\n */\nconstructor(private _id: string,\nprivate _renderer: AnimationRenderer) { super(); }\n/**\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(element: any, options?: AnimationOptions): AnimationPlayer {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\n\nfunction BrowserAnimationFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationFactory.prototype._id;\n/** @type {?} */\nBrowserAnimationFactory.prototype._renderer;\n}\n\nexport class RendererAnimationPlayer implements AnimationPlayer {\npublic parentPlayer: AnimationPlayer|null = null;\nprivate _started = false;\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} options\n * @param {?} _renderer\n */\nconstructor(\npublic id: string,\npublic element: any, options: AnimationOptions,\nprivate _renderer: AnimationRenderer) {\n    this._command('create', options);\n  }\n/**\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nprivate _listen(eventName: string, callback: (event: any) => any): () => void {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n/**\n * @param {?} command\n * @param {...?} args\n * @return {?}\n */\nprivate _command(command: string, ...args: any[]) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._listen('done', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._listen('start', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._listen('destroy', fn); }\n/**\n * @return {?}\n */\ninit(): void { this._command('init'); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\nplay(): void {\n    this._command('play');\n    this._started = true;\n  }\n/**\n * @return {?}\n */\npause(): void { this._command('pause'); }\n/**\n * @return {?}\n */\nrestart(): void { this._command('restart'); }\n/**\n * @return {?}\n */\nfinish(): void { this._command('finish'); }\n/**\n * @return {?}\n */\ndestroy(): void { this._command('destroy'); }\n/**\n * @return {?}\n */\nreset(): void { this._command('reset'); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._command('setPosition', p); }\n/**\n * @return {?}\n */\ngetPosition(): number { return 0; }\npublic totalTime = 0;\n}\n\nfunction RendererAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nRendererAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nRendererAnimationPlayer.prototype._started;\n/** @type {?} */\nRendererAnimationPlayer.prototype.totalTime;\n/** @type {?} */\nRendererAnimationPlayer.prototype.id;\n/** @type {?} */\nRendererAnimationPlayer.prototype.element;\n/** @type {?} */\nRendererAnimationPlayer.prototype._renderer;\n}\n\n/**\n * @param {?} renderer\n * @param {?} element\n * @param {?} id\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\nfunction issueAnimationCommand(\n    renderer: AnimationRenderer, element: any, id: string, command: string, args: any[]): any {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["DomRendererFactory2", "AnimationEngine"], "mappings": ";;;;;;AMAA,OAAA,EAAA,UAAA,EAAA,QAAA,EAAA,MAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,MAAA,eAAA,CAAA;;;;;;;;;;GAkBA;AACA;IAAA,mDAAA;IALA;;OAOA;IACA,iCAAA,YAAA;QAAA,YACA,iBAAA,SASA;QARA,KAAM,CAAN,gBAAsB,GAAtB,CAA0B,CAA1B;QACA,IAAA,QAAA,GAAA;YACQ,EAAR,EAAA,GAAA;YACA,aAAA,EAAA,iBAAA,CAAA,IAAA;;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,uCAAA,GAAA,UAAA,SAJe;QAKf,IAAA,gBAAA,CAAA,EAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,CAAA;;QAHA,IAAA,gBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,QAAA,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA;QAKA,qBAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,uBAAA,CAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,CAAA;;;CAtBA,CAAA,gBAAA;;IAoBA,EAAA,IAAA,EAAA,UAAA,EAAA;CAOA,CAAA;AACA;;;;;;AAsBA;IAAA,mDAAA;IAxBA;;;OAAA;;;;;;;IA+BA;;;;OAKA;;;;;CAZA,CAAA,gBAAA;;;;;;;OAhBA;IACA,iCAAA,EAAA,EAAA,OAAA,EAAqB,OAArB,EAAA,SAAA;QA6CS,IAAT,CAAA,EAAA,GAAA,EAAA,CAAA;QAEI,IAAI,CAAC,OAAT,GAAA,OAA0B,CAA1B;QACA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;;;IAxCG;;;;;;;;IAIA;;;;;;;;;;;IAyDH,CAAA;;;;;IAKA,wCAAA,GAAA,UAxDG,EAwDH,IAAA,IAAA,CAxDkC,OAwDlC,CAxDwC,MAwDxC,EAxDgD,EAAQ,CAwDxD,CAxDwD,CAAG,CAAC;;;;;IA6D5D,yCAAA,GAAA,UAAA,EA3DG,IA2DH,IAAA,CAAA,OAAA,CAAA,OAAA,EAAA,EA3DkD,CA2DlD,CA3D4D,CA2D5D,CA3D4D;;;;OA+D5D;;;;OAIA;;;;OAIA;IACA,4CAAA,GAAA,cAAA,MAAA,CAAA,IAjE0B,CAiE1B,QAAA,CAAA,CAAA,CAAA;IACA;;;;;QAKA,IAAA,CAAA,QAAA,GAAA,IAnEgC,CAmEhC;;;;OAIA;;;;OAIA;;;;OAIA;;;;OAIA;;;;;IAKA,uCAAA,GAAA,cAAA,IAAA,CAAA,QAAA,CAAA,OA9E+C,CA8E/C,CAAA,CAAA,CAAA;;;;OAIA;IAEA,6CAAA,GAAA,UAAA,CAAA,IAAA,IAAA,CAAA,QAAA,CAAA,aAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEA;;;;;;;;;;;;;GDrLA;;;;;;;;;;;;IAqBA;;;;OARA;IACA,kCAAA,QAAA,EAAA,MAAA,EAAA,KAAA;QACU,IAAV,CAAA,QAAA,GAAA,QAAA,CAAA;QACU,IAAV,CAAA,MAAA,GAAA,MAAA,CAAA;QASI,IAAJ,CAAA,KAAA,GAAA,KAAA,CAAA;;;;;QAKA,MAAA,CAAA,iBAAA,GAAA,UAA8B,OAA9B,EAAA,QAAA;YACA,yEAAA;YACA,iFAAA;YACA,kEAAA;YACA,iEAAA;;;;;;IAMA;;;;OAKA;IACA,iDAAA,GAAA,UAAA,WAAA,EAAA,IAAA;QAAA,iBAyBA;QAxBA,IAAA,gBAAA,CAAA,kBAT2D,GAS3D,EAAA,CAAA;QACA,4DAAA;QACA,mCAAA;;QAEA,EAAA,CAAA,CAAA,CAAA,WAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA;YACM,EAAN,CAAA,CAAA,CAAA,QAAA,CAAA,CATa,CAAS;gBAUtB,QAAA,GAAA,IAAA,qBAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;gBAEA,wDAAA;gBACA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,QAAA,EAT6B,QAAK,CASlC,CAAA;YACQ,CATC;YAWD,MAAR,CATU,QASV,CATiB;QAUb,CAAJ;QACI,IAAJ,gBAAA,CAAA,WAAA,GARmB,IAAA,CAAK,EAQxB,CAAA;QAGI,IAAJ,gBAAA,CAAA,WAAA,GAAA,IAAA,CAAA,EAAA,GAAA,GAAA,GAAA,IAAA,CAT8D,UAS9D,CAToE;QAUpE,IAAA,CAAA,UAAA,EAAA,CAAA;;;;QAIA,MAAA,CAAA,IAAA,iBAAA,CAAA,IAAA,EAAA,WAAA,EAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAEA;;;;QATA,CAAA;IAcA,CAAA;IACA;;;;;;;;;;;;OAWA;IAEA,2DAAA,GAAA,UAAA,KAAA,EAAA,EAAA,EAAA,IAAA;QAAA,iBAkBA;QAjBA,EAAA,CAAA,CAAM,KAAN,IAAA,CAAA,IAAA,KAAA,GAjB4B,IAAC,CAAI,YAiBjC,CAAA,CAAA,CAAA;YACA,IAAQ,CAAR,KAAA,CAAA,GAjBc,CAAK,cAiBnB,OAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,yBAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA;gBACA,KAAU,CAAV,KAAA,CAAA,GAAA,CAAA;oBACA,KAAA,CAAA,yBAAA,CAAA,OAAA,CAAA,UAAA,KAAA;wBACA,IAAA,aAAA,EAAA,eAAA,CAAA;wBACA,EAAA,CAAA,IAAA,CAAA,CAAA;oBAEA,CAAA,CAAA,CAAA;oBACA,KAAA,CAAA,yBAAA,GAAA,EAAA,CAAA;;;;QAIA,IAAA,CAAA,yBAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAEA;IACA,sCAAA,GAAA;QAAA;QACA,IAAM,CAAN,KAAA,CAAA,iBAAA,CAAA;YACA,KAAA,CAAA,kBAAA,EAAA,CAAA;YACA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA;;;;QAIA,CAAA;;IApBA;;OAuBA;;;;;IAnBA,EAAA,IAAA,EAAA,UAAA,EAAA;CAwBA,CAAA;AACA;;GAEA;AAGA,wBAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;;GAAA;;IA8BA;;;;OAIA;IACA,+BAAA,WAAA,EAAA,QAAA,EAAA,MAAA;;;;QApDG,IAAA,CAwDH,WAxDsB,GAwDtB,IAAA,CAAA,QAAA,CAAA,WAAA,GAAA,UAAA,CAAA,IAAA,OAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA;;IAOA,sBAAI,uCAAI;;;WADR;aACA,cAAA,MAAA,CAAA,IA1DwB,CA0DxB,QAAA,CAAA,IAAA,CAAA,CA1D8B,CA0D9B;;;OAAA;IACA;;;;;;;IAOA;;;;;;;IAOA,CAAA;;;;;IAKA,6CAAA,GAAA,UAAA,KAAA,IAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aApE+D,CAAK,KAoEpE,CAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;IAmBA;;;;;;;IAOA,CAAA;;;;;IAKA,iDAAA,GAAA,UAAA,cAAA,IAAA,MAAA,CAAA,IAAA,CAAA,QAvF+D,CAAC,iBAuFhE,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,0CAAA,GAAA,UA1FG,IA0FH,IAAA,MAAA,CAAA,IAAA,CA1FkC,QA0FlC,CA1FwC,UA0FxC,CAAA,IAAA,CAAA,CA1F6D,CAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAmHlE;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCA;;;;;;;;;;;OAYA;;;;;;;;;;;;;OAuBA;;;;;;;;;;;;OAWA;IA5JA,uCAAA,GAAA,UAAA,EAAA,EAAA,IAAA,EAAA,KAAA;QA6JA,EAAA,CAAA,CAAM,IAAI,CA5JC,MA4JX,CAAA,CA5JY,CAAQ,IA4JpB,GAAA,CAAA,CAAA,CAAA;YACA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;OAYA;IACA,kCAAA,GAAA,UAAA,MAAA,EAAA,SAjKsB,EAAE,QAiKxB;QAAA;QACA,EAAA,CAAA,CAAA,SAjKU,CAiKV,MAjKe,CAAM,CAiKrB,CAAA,IAAA,GAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,wBAAA,CAAA,MAAA,CAAA,CAAA;YACM,IAAN,gBAAA,CAAA,IAjK0B,GAiK1B,SAAA,CAAA,MAjKkD,CAiKlD,CAAA,CAAA,CAAA;YACA,IAAQ,gBAAR,CAAA,KAAA,GAAA,EAAA,CAAA;YACA,EAAA,CAAA,CAAQ,IAAI,CAjKC,MAiKb,CAjKc,CAAO,CAiKrB,IAAA,GAAA,CAAA,CAAA,CAAA;gBACA,mCAAA,EAAA,YAAA,EAAA,aAAA,CAAA;YACA,CAAA;YACA,MAAA,CAjKW,IAiKX,CAAA,MAAA,CAAA,MAAA,CAAA,IAjKiC,CAiKjC,WAAA,EAAA,OAjKmD,EAAU,IAiK7D,EAAA,KAAA,EAAA,UAAA,KAAA;gBACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,OAAA,CAAA,wBAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;YAEA,CAAA,CAAA,CAAA;;;;;IASA,wBAAA;AAAA,CAAA,yBAAA;AACA;;;GAGA;AACA,kCAAA,MAAA;IACA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAnLS;QAoLT,KAAA,MAnLa;YAoLb,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;QACA,KAAA,UAAA;YACA,MAAA,CAAA,QAAA,CAAA;QACA,KAAA,QAAA;;;;;AAKA,CAAA;AACA;;;GAGA;AACA,kCAAA,WAAA;;ID7ZA,IAAA,gBAAA,CAAA,OAAA,GAAA,WAAA,CAAA,SAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA;;;;;;;;;;;AAqBA;IAAA,qDAAA;IACA;;;OADA;IAIA,mCAAA,MAHQ,EAAW,UAGnB;eACA,kBAAA,MAAA,EAAA,UAAA,CAAA;;;CALA,CAAA,gBAAA;;IAIA,EAAA,IAAA,EAAA,UAAA,EAAA;CAMA,CAAA;AACA;;GAIA;;;IAaA,EAAA,IAAA,EAAA,yBAAA,GAAA;CACA,GAAA,CAAA;AACA;;GAEA;AACA;;;;IAIA,MAAA,CAAA,IAAA,oBAAA,EAAA,CAAA;AACA,CAAA;AACA;;;;;;;;;;;GAaA;AACA,oCAAA,QAAA,EAAA,MAjCsC,EAAY,IAiClD;IACE,MAAF,CAAA,IAjCYC,wBAiCZ,CAAA,QAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA,IAAA,0BAAA,GAAA;IACA,EAAA,OAAA,EAjCUD,gBAiCV,EAAA,QAAA,EAAA,uBAAA,EAAA;IACA,EAAA,OAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,iCAAA,EAAA;IACA,EAAA,OAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,EAAA;;;;;CAKA,CAAA;AACA;;;;;;oCAOA,CAAA;;;;;;;;;;;;;AD5EO;;GACP;AACA;IAAA;IACA,CAAA;IAAA,8BAAA;AAAA,CAAA,AADA,IACA;AACA,uBAAA,CAAA,UAAA,GAAA;;;;aAGA,EAAA,EAAA;CAKA,CAAA;;;GAaA;;AATO;;GAYP;AACA;IAAA;IACA,CAAA;IAAA,2BAAA;AAAA,CAAA,AADA,IACA;AACA,oBAAA,CAAA,UAAA,GAAA;;;;aARA,EAAA,EAAA;;ADxCA;;;;;;;;;;;;;;;;;GDYG;;;;;;;;;;;;GDAA;;;;;;;;;;"}