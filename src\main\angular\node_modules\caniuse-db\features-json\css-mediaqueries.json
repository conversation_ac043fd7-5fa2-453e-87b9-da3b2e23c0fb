{"title": "CSS3 Media Queries", "description": "Method of applying styles based on media information. Includes things like page and device dimensions", "spec": "http://www.w3.org/TR/css3-mediaqueries/", "status": "rec", "links": [{"url": "http://ie.microsoft.com/testdrive/HTML5/85CSS3_MediaQueries/", "title": "IE demo page with information"}, {"url": "http://webdesignerwall.com/tutorials/responsive-design-with-css3-media-queries", "title": "Media Queries tutorial"}, {"url": "https://github.com/scottjehl/Respond", "title": "Polyfill for IE"}, {"url": "https://www.webplatform.org/docs/css/atrules/@media", "title": "WebPlatform Docs"}], "bugs": [{"description": "Firefox (9 and previous?) is buggy with min-width media queries not being recognized, but the rules inside those being parsed and used."}, {"description": "Opera 12.1 and IE9 incorrectly include scrollbar width for media queries based on window width."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "p", "6": "p", "7": "p", "8": "p", "9": "y #1", "10": "y #1", "11": "y #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a #1 #2", "3.2": "a #1 #2", "4": "y #1", "5": "y #1", "5.1": "y #1", "6": "y #1", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "y #1", "4.0-4.1": "y #1", "4.2-4.3": "y #1", "5.0-5.1": "y #1", "6.0-6.1": "y #1", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "y #1", "2.2": "y #1", "2.3": "y #1", "3": "y #1", "4": "y #1", "4.1": "y #1", "4.2-4.3": "y #1", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y #1", "11": "y #1"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not support nested media queries", "2": "Partial support refers to only acknowledging different media rules on page reload"}, "usage_perc_y": 97.69, "usage_perc_a": 0.01, "ucprefix": false, "parent": "", "keywords": "@media", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}