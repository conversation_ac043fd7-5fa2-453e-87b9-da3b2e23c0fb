{"_args": [["@angular/platform-browser-dynamic@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/platform-browser-dynamic@4.2.5", "_id": "@angular/platform-browser-dynamic@4.2.5", "_inBundle": false, "_integrity": "sha1-gHbsSohcw6GiPF5UDECn/dP357I=", "_location": "/@angular/platform-browser-dynamic", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/platform-browser-dynamic@4.2.5", "name": "@angular/platform-browser-dynamic", "escapedName": "@angular%2fplatform-browser-dynamic", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/platform-browser-dynamic/-/platform-browser-dynamic-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - library for using Angular in a web browser with JIT compilation", "es2015": "./@angular/platform-browser-dynamic.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/platform-browser-dynamic.umd.js", "module": "./@angular/platform-browser-dynamic.es5.js", "name": "@angular/platform-browser-dynamic", "peerDependencies": {"@angular/core": "4.2.5", "@angular/common": "4.2.5", "@angular/compiler": "4.2.5", "@angular/platform-browser": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./platform-browser-dynamic.d.ts", "version": "4.2.5"}