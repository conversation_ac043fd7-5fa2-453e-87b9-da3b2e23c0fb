{"_args": [["array-differ@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "array-differ@1.0.0", "_id": "array-differ@1.0.0", "_inBundle": false, "_integrity": "sha1-7/UuN1gknTO+QCuLuOVkuytdQDE=", "_location": "/array-differ", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-differ@1.0.0", "name": "array-differ", "escapedName": "array-differ", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/maximatch"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-differ/-/array-differ-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/array-differ/issues"}, "description": "Create an array with values that are present in the first input array but not additional ones", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/array-differ#readme", "keywords": ["array", "difference", "diff", "differ", "filter", "exclude"], "license": "MIT", "name": "array-differ", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/array-differ.git"}, "scripts": {"test": "mocha"}, "version": "1.0.0"}