{"_args": [["configstore@2.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "configstore@2.1.0", "_id": "configstore@2.1.0", "_inBundle": false, "_integrity": "sha1-c3o6cDbpiGECqmCZ5HuzOrGroaE=", "_location": "/configstore", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "configstore@2.1.0", "name": "configstore", "escapedName": "configstore", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/update-notifier"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/configstore/-/configstore-2.1.0.tgz", "_spec": "2.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/yeoman/configstore/issues"}, "dependencies": {"dot-prop": "^3.0.0", "graceful-fs": "^4.1.2", "mkdirp": "^0.5.0", "object-assign": "^4.0.1", "os-tmpdir": "^1.0.0", "osenv": "^0.1.0", "uuid": "^2.0.1", "write-file-atomic": "^1.1.2", "xdg-basedir": "^2.0.0"}, "description": "Easily load and save config without having to think about where and how", "devDependencies": {"ava": "*", "path-exists": "^2.0.0", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/yeoman/configstore#readme", "keywords": ["config", "store", "storage", "conf", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "license": "BSD-2-<PERSON><PERSON>", "name": "configstore", "repository": {"type": "git", "url": "git+https://github.com/yeoman/configstore.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}