{"_args": [["code-point-at@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "code-point-at@1.1.0", "_id": "code-point-at@1.1.0", "_inBundle": false, "_integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "_location": "/code-point-at", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "code-point-at@1.1.0", "name": "code-point-at", "escapedName": "code-point-at", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/ansi-align/string-width", "/boxen/string-width", "/cliui/string-width", "/gauge/string-width", "/webpack-dev-server/string-width", "/webpack/string-width", "/widest-line/string-width", "/wrap-ansi/string-width"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/code-point-at/-/code-point-at-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/code-point-at/issues"}, "description": "ES2015 `String#codePointAt()` ponyfill", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/code-point-at#readme", "keywords": ["es2015", "ponyfill", "polyfill", "shim", "string", "str", "code", "point", "at", "codepoint", "unicode"], "license": "MIT", "name": "code-point-at", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/code-point-at.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}