[{"__symbolic": "module", "version": 3, "metadata": {"ProviderError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}]}}, "QueryWithId": {"__symbolic": "interface"}, "ProviderViewContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileDirectiveMetadata"}]}]}}, "ProviderElementContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ProviderViewContext"}, {"__symbolic": "reference", "name": "ProviderElementContext"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}], "afterElement": [{"__symbolic": "method"}], "_addQueryReadsTo": [{"__symbolic": "method"}], "_getQueriesFor": [{"__symbolic": "method"}], "_getOrCreateLocalProvider": [{"__symbolic": "method"}], "_getLocalDependency": [{"__symbolic": "method"}], "_getDependency": [{"__symbolic": "method"}]}}, "NgModuleProviderAnalyzer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileNgModuleMetadata"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileProviderMetadata"}]}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}], "parse": [{"__symbolic": "method"}], "_getOrCreateLocalProvider": [{"__symbolic": "method"}], "_getDependency": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ProviderError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}]}}, "QueryWithId": {"__symbolic": "interface"}, "ProviderViewContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileDirectiveMetadata"}]}]}}, "ProviderElementContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ProviderViewContext"}, {"__symbolic": "reference", "name": "ProviderElementContext"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_parser/template_ast", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}], "afterElement": [{"__symbolic": "method"}], "_addQueryReadsTo": [{"__symbolic": "method"}], "_getQueriesFor": [{"__symbolic": "method"}], "_getOrCreateLocalProvider": [{"__symbolic": "method"}], "_getLocalDependency": [{"__symbolic": "method"}], "_getDependency": [{"__symbolic": "method"}]}}, "NgModuleProviderAnalyzer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileNgModuleMetadata"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileProviderMetadata"}]}, {"__symbolic": "reference", "module": "./parse_util", "name": "ParseSourceSpan"}]}], "parse": [{"__symbolic": "method"}], "_getOrCreateLocalProvider": [{"__symbolic": "method"}], "_getDependency": [{"__symbolic": "method"}]}}}}]