[{"__symbolic": "module", "version": 3, "metadata": {"IVisitor": {"__symbolic": "interface"}, "serialize": {"__symbolic": "function", "parameters": ["nodes"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 46, "character": 19}}, "Node": {"__symbolic": "interface"}, "Declaration": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 30}]}], "visit": [{"__symbolic": "method"}]}}, "Doctype": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "Tag": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 73, "character": 43}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "CR": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Text"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"IVisitor": {"__symbolic": "interface"}, "serialize": {"__symbolic": "function", "parameters": ["nodes"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 46, "character": 19}}, "Node": {"__symbolic": "interface"}, "Declaration": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 30}]}], "visit": [{"__symbolic": "method"}]}}, "Doctype": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "Tag": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 73, "character": 43}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "CR": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Text"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}]}]}}}}]