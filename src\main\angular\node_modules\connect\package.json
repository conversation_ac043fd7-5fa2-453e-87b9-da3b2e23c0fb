{"_args": [["connect@3.6.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "connect@3.6.2", "_id": "connect@3.6.2", "_inBundle": false, "_integrity": "sha1-aU6NIGgb/kkCgsiriGvpjwn0L+c=", "_location": "/connect", "_phantomChildren": {"ms": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "connect@3.6.2", "name": "connect", "escapedName": "connect", "rawSpec": "3.6.2", "saveSpec": null, "fetchSpec": "3.6.2"}, "_requiredBy": ["/karma"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/connect/-/connect-3.6.2.tgz", "_spec": "3.6.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/senchalabs/connect/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "2.6.7", "finalhandler": "1.0.3", "parseurl": "~1.3.1", "utils-merge": "1.0.0"}, "description": "High performance middleware framework", "devDependencies": {"istanbul": "0.4.5", "mocha": "3.4.1", "supertest": "2.0.0"}, "engines": {"node": ">= 0.10.0"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/senchalabs/connect#readme", "keywords": ["framework", "web", "middleware", "connect", "rack"], "license": "MIT", "name": "connect", "repository": {"type": "git", "url": "git+https://github.com/senchalabs/connect.git"}, "scripts": {"test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "version": "3.6.2"}