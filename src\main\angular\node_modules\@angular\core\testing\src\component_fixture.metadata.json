[{"__symbolic": "module", "version": 3, "metadata": {"ComponentFixture": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ComponentRef", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 53, "character": 40, "context": {"typeName": "T"}}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, {"__symbolic": "reference", "name": "boolean"}]}], "_tick": [{"__symbolic": "method"}], "detectChanges": [{"__symbolic": "method"}], "checkNoChanges": [{"__symbolic": "method"}], "autoDetectChanges": [{"__symbolic": "method"}], "isStable": [{"__symbolic": "method"}], "whenStable": [{"__symbolic": "method"}], "_getRenderer": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ComponentFixture": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ComponentRef", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 53, "character": 40, "context": {"typeName": "T"}}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, {"__symbolic": "reference", "name": "boolean"}]}], "_tick": [{"__symbolic": "method"}], "detectChanges": [{"__symbolic": "method"}], "checkNoChanges": [{"__symbolic": "method"}], "autoDetectChanges": [{"__symbolic": "method"}], "isStable": [{"__symbolic": "method"}], "whenStable": [{"__symbolic": "method"}], "_getRenderer": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}]}}}}]