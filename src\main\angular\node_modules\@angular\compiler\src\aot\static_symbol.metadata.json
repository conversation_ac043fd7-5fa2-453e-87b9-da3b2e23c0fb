[{"__symbolic": "module", "version": 3, "metadata": {"StaticSymbol": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}], "assertNoMembers": [{"__symbolic": "method"}]}}, "StaticSymbolCache": {"__symbolic": "class", "members": {"get": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"StaticSymbol": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}], "assertNoMembers": [{"__symbolic": "method"}]}}, "StaticSymbolCache": {"__symbolic": "class", "members": {"get": [{"__symbolic": "method"}]}}}}]