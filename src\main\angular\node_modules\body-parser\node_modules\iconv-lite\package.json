{"_args": [["iconv-lite@0.4.15", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "iconv-lite@0.4.15", "_id": "iconv-lite@0.4.15", "_inBundle": false, "_integrity": "sha1-/iZaIYrGpXz+hUkn6dBMGYJe3es=", "_location": "/body-parser/iconv-lite", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "iconv-lite@0.4.15", "name": "iconv-lite", "escapedName": "iconv-lite", "rawSpec": "0.4.15", "saveSpec": null, "fetchSpec": "0.4.15"}, "_requiredBy": ["/body-parser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/iconv-lite/-/iconv-lite-0.4.15.tgz", "_spec": "0.4.15", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./extend-node": false, "./streams": false}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "description": "Convert character encodings in pure javascript.", "devDependencies": {"async": "*", "errto": "*", "iconv": "*", "istanbul": "*", "mocha": "*", "request": "*", "unorm": "*"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "license": "MIT", "main": "./lib/index.js", "name": "iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "typings": "./lib/index.d.ts", "version": "0.4.15"}