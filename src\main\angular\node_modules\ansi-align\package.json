{"_args": [["ansi-align@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ansi-align@1.1.0", "_id": "ansi-align@1.1.0", "_inBundle": false, "_integrity": "sha1-LwwWWIKXOa3V67FeawxuNCPwFro=", "_location": "/ansi-align", "_phantomChildren": {"code-point-at": "1.1.0", "number-is-nan": "1.0.1", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "ansi-align@1.1.0", "name": "ansi-align", "escapedName": "ansi-align", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/boxen"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ansi-align/-/ansi-align-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "nexdrew"}, "bugs": {"url": "https://github.com/nexdrew/ansi-align/issues"}, "dependencies": {"string-width": "^1.0.1"}, "description": "align-text with ANSI support for CLIs", "devDependencies": {"ava": "^0.15.2", "chalk": "^1.1.3", "coveralls": "^2.11.9", "nyc": "^6.4.4", "standard": "^7.1.2", "standard-version": "^2.3.0"}, "files": ["index.js"], "homepage": "https://github.com/nexdrew/ansi-align#readme", "keywords": ["ansi", "align", "cli", "center", "pad"], "license": "ISC", "main": "index.js", "name": "ansi-align", "repository": {"type": "git", "url": "git+https://github.com/nexdrew/ansi-align.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc ava"}, "version": "1.1.0"}