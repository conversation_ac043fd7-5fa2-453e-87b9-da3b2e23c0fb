{"_args": [["readable-stream@2.3.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "readable-stream@2.3.7", "_id": "readable-stream@2.3.7", "_inBundle": false, "_integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "_location": "/bl/readable-stream", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "readable-stream@2.3.7", "name": "readable-stream", "escapedName": "readable-stream", "rawSpec": "2.3.7", "saveSpec": null, "fetchSpec": "2.3.7"}, "_requiredBy": ["/bl"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/readable-stream/-/readable-stream-2.3.7.tgz", "_spec": "2.3.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "browser": {"util": false, "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./duplex.js": "./duplex-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "description": "Streams3, a user-land copy of the stream library from Node.js", "devDependencies": {"assert": "^1.4.0", "babel-polyfill": "^6.9.1", "buffer": "^4.9.0", "lolex": "^2.3.2", "nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0"}, "homepage": "https://github.com/nodejs/readable-stream#readme", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "main": "readable.js", "name": "readable-stream", "nyc": {"include": ["lib/**.js"]}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js"}, "version": "2.3.7"}