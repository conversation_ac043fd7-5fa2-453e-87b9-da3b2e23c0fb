[{"__symbolic": "module", "version": 3, "metadata": {"I18NHtmlParser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "MissingTranslationStrategy"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "parse": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"I18NHtmlParser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "MissingTranslationStrategy"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "parse": [{"__symbolic": "method"}]}}}}]