[{"__symbolic": "module", "version": 3, "metadata": {"TemplateAst": {"__symbolic": "interface"}, "TextAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundTextAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "AttrAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundElementPropertyAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 67, "character": 40, "context": {"typeName": "PropertyBindingType"}}, {"__symbolic": "reference", "module": "@angular/core", "name": "SecurityContext"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundEventAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ReferenceAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileTokenMetadata"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "VariableAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ElementAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundElementPropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ProviderAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "EmbeddedTemplateAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "VariableAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ProviderAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundDirectivePropertyAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "DirectiveAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileDirectiveSummary"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundDirectivePropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundElementPropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ProviderAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileTokenMetadata"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileProviderMetadata"}]}, {"__symbolic": "error", "message": "Could not resolve type", "line": 187, "character": 72, "context": {"typeName": "ProviderAstType"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../lifecycle_reflector", "name": "LifecycleHooks"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ProviderAstType": {"PublicService": 0, "PrivateService": 1, "Component": 2, "Directive": 3, "Builtin": 4}, "NgContentAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyBindingType": {"Property": 0, "Attribute": 1, "Class": 2, "Style": 3, "Animation": 4}, "QueryMatch": {"__symbolic": "interface"}, "TemplateAstVisitor": {"__symbolic": "interface"}, "NullTemplateVisitor": {"__symbolic": "class", "members": {"visitNgContent": [{"__symbolic": "method"}], "visitEmbeddedTemplate": [{"__symbolic": "method"}], "visitElement": [{"__symbolic": "method"}], "visitReference": [{"__symbolic": "method"}], "visitVariable": [{"__symbolic": "method"}], "visitEvent": [{"__symbolic": "method"}], "visitElementProperty": [{"__symbolic": "method"}], "visitAttr": [{"__symbolic": "method"}], "visitBoundText": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitDirective": [{"__symbolic": "method"}], "visitDirectiveProperty": [{"__symbolic": "method"}]}}, "RecursiveTemplateAstVisitor": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "NullTemplateVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitEmbeddedTemplate": [{"__symbolic": "method"}], "visitElement": [{"__symbolic": "method"}], "visitDirective": [{"__symbolic": "method"}], "visitChildren": [{"__symbolic": "method"}]}}, "templateVisitAll": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"TemplateAst": {"__symbolic": "interface"}, "TextAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundTextAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "AttrAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundElementPropertyAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 67, "character": 40, "context": {"typeName": "PropertyBindingType"}}, {"__symbolic": "reference", "module": "@angular/core", "name": "SecurityContext"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundEventAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ReferenceAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileTokenMetadata"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "VariableAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ElementAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundElementPropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ProviderAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "EmbeddedTemplateAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "AttrAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ReferenceAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "VariableAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "DirectiveAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ProviderAst"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "BoundDirectivePropertyAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "AST"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "DirectiveAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileDirectiveSummary"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundDirectivePropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundElementPropertyAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "BoundEventAst"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ProviderAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileTokenMetadata"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompileProviderMetadata"}]}, {"__symbolic": "error", "message": "Could not resolve type", "line": 187, "character": 72, "context": {"typeName": "ProviderAstType"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../lifecycle_reflector", "name": "LifecycleHooks"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ProviderAstType": {"PublicService": 0, "PrivateService": 1, "Component": 2, "Directive": 3, "Builtin": 4}, "NgContentAst": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyBindingType": {"Property": 0, "Attribute": 1, "Class": 2, "Style": 3, "Animation": 4}, "QueryMatch": {"__symbolic": "interface"}, "TemplateAstVisitor": {"__symbolic": "interface"}, "NullTemplateVisitor": {"__symbolic": "class", "members": {"visitNgContent": [{"__symbolic": "method"}], "visitEmbeddedTemplate": [{"__symbolic": "method"}], "visitElement": [{"__symbolic": "method"}], "visitReference": [{"__symbolic": "method"}], "visitVariable": [{"__symbolic": "method"}], "visitEvent": [{"__symbolic": "method"}], "visitElementProperty": [{"__symbolic": "method"}], "visitAttr": [{"__symbolic": "method"}], "visitBoundText": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitDirective": [{"__symbolic": "method"}], "visitDirectiveProperty": [{"__symbolic": "method"}]}}, "RecursiveTemplateAstVisitor": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "NullTemplateVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitEmbeddedTemplate": [{"__symbolic": "method"}], "visitElement": [{"__symbolic": "method"}], "visitDirective": [{"__symbolic": "method"}], "visitChildren": [{"__symbolic": "method"}]}}, "templateVisitAll": {"__symbolic": "function"}}}]