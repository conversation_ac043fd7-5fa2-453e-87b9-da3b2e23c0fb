{"version": 3, "file": "new.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/new.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,6BAA6B;AAC7B,+BAA+B;AAC/B,uCAAwC;AAExC,iCAAiC;AACjC,6CAA6C;AAC7C,8EAAyE;AACzE,6CAAsC;AAEtC,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAElC,MAAM,UAAU,GAAG,mBAAmB,CAAC;AACvC,MAAM,WAAW,GAAG,CAAC,IAAY,KAAK,sCAAsC,UAAU,MAAM,IAAI,GAAG,CAAC;AAEpG,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAChC,IAAI,EAAE,KAAK;IACX,OAAO,EAAE,CAAC,GAAG,CAAC;IACd,WAAW,EAAE,oEAAoE;IACjF,KAAK,EAAE,gBAAgB;IAEvB,gBAAgB,EAAE;QAChB;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,qBAAO,CAAA;;;OAGnB;SACF;QACD;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,sCAAsC;SACpD;QACD;YACE,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,gDAAgD;YAC7D,MAAM,EAAE,IAAI;SACb;QACD;YACE,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,2BAA2B;SACzC;QACD;YACE,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,qCAAqC;SACnD;QACD;YACE,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,2BAA2B;SACzC;QACD;YACE,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,0CAA0C;SACxD;QACD;YACE,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,WAAW,EAAE,0CAA0C;SACxD;QACD;YACE,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,qCAAqC,WAAW,CAAC,cAAc,CAAC,GAAG;SACjF;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,qBAAO,CAAA;;UAEhB,WAAW,CAAC,mBAAmB,CAAC;OACnC;SACF;QACD;YACE,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,qBAAO,CAAA;;UAEhB,WAAW,CAAC,gBAAgB,CAAC;OAChC;SACF;QACD;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,4BAA4B;SAC1C;QACD;YACE,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,8BAA8B;SAC5C;QACD;YACE,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,iCAAiC;SAC9C;QACD;YACC,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,8BAA8B;SAC3C;KACH;IAED,SAAS,EAAE,UAAU,WAAmB;QACtC,MAAM,CAAC,kBAAS,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC;IACrD,CAAC;IAED,GAAG,EAAE,UAAU,cAAmB,EAAE,OAAiB;QACnD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAEpC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CACnC,WAAW,IAAI,CAAC,IAAI,yDAAyD;gBAC7E,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9B,kCAAkC,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,2CAAmB,CAAC,WAAW,CAAC,CAAC;QACjC,cAAc,CAAC,IAAI,GAAG,WAAW,CAAC;QAClC,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1B,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAC3C,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QAErE,MAAM,WAAW,GAAG,IAAI,cAAW,CAAC;YAClC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC;SAChD,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC;QACpB,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1B,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE;iBAChC,IAAI,CAAC;gBACJ,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAClE,MAAM,IAAI,WAAW,CAAC,qBAAO,CAAA;0BACf,aAAa;aAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC;iBACnC,KAAK,CAAC,GAAG;gBACR,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;oBAC1B,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM,IAAI,WAAW,CAAC,qBAAO,CAAA;4BACf,aAAa;eAC1B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,MAAM,GAAG,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,CAAC,eAAe;aACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;IACtE,CAAC;CACF,CAAC,CAAC;AAGH,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AAC/B,kBAAe,UAAU,CAAC"}