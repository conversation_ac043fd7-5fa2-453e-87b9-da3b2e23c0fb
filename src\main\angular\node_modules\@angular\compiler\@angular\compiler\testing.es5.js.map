{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/compiler/testing/index.ts", "../../../../../packages/compiler/testing/src/testing.ts", "../../../../../packages/compiler/testing/src/metadata_overrider.ts", "../../../../../packages/compiler/testing/src/pipe_resolver_mock.ts", "../../../../../packages/compiler/testing/src/ng_module_resolver_mock.ts", "../../../../../packages/compiler/testing/src/directive_resolver_mock.ts", "../../../../../packages/compiler/testing/src/schema_registry_mock.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the compiler/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all APIs of the compiler package.\n *\n * <div class=\"callout is-critical\">\n *   <header>Unstable APIs</header>\n *   <p>\n *     All compiler apis are currently considered experimental and private!\n *   </p>\n *   <p>\n *     We expect the APIs in this package to keep on changing. Do not rely on them.\n *   </p>\n * </div>\n */\nexport * from './schema_registry_mock';\nexport * from './directive_resolver_mock';\nexport * from './ng_module_resolver_mock';\nexport * from './pipe_resolver_mock';\n\nimport {createPlatformFactory, ModuleWithComponentFactories, Injectable, CompilerOptions, COMPILER_OPTIONS, CompilerFactory, ComponentFactory, NgModuleFactory, Injector, NgModule, Component, Directive, Pipe, Type, PlatformRef, ɵstringify} from '@angular/core';\nimport {MetadataOverride, ɵTestingCompilerFactory as TestingCompilerFactory, ɵTestingCompiler as TestingCompiler} from '@angular/core/testing';\nimport {platformCoreDynamic, JitCompiler, DirectiveResolver, NgModuleResolver, PipeResolver, CompileMetadataResolver} from '@angular/compiler';\nimport {MockDirectiveResolver} from './directive_resolver_mock';\nimport {MockNgModuleResolver} from './ng_module_resolver_mock';\nimport {MockPipeResolver} from './pipe_resolver_mock';\nimport {MetadataOverrider} from './metadata_overrider';\n\n\nexport class TestingCompilerFactoryImpl implements TestingCompilerFactory {\n  constructor(private _compilerFactory: CompilerFactory) {}\n\n  createTestingCompiler(options: CompilerOptions[]): TestingCompiler {\n    const compiler = <JitCompiler>this._compilerFactory.createCompiler(options);\n    return new TestingCompilerImpl(\n        compiler, compiler.injector.get(MockDirectiveResolver),\n        compiler.injector.get(MockPipeResolver), compiler.injector.get(MockNgModuleResolver),\n        compiler.injector.get(CompileMetadataResolver));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: CompilerFactory, },\n];\n}\n\nexport class TestingCompilerImpl implements TestingCompiler {\n  private _overrider = new MetadataOverrider();\n  constructor(\n      private _compiler: JitCompiler, private _directiveResolver: MockDirectiveResolver,\n      private _pipeResolver: MockPipeResolver, private _moduleResolver: MockNgModuleResolver,\n      private _metadataResolver: CompileMetadataResolver) {}\n  get injector(): Injector { return this._compiler.injector; }\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    return this._compiler.compileModuleSync(moduleType);\n  }\n\n  compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    return this._compiler.compileModuleAsync(moduleType);\n  }\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    return this._compiler.compileModuleAndAllComponentsSync(moduleType);\n  }\n\n  compileModuleAndAllComponentsAsync<T>(moduleType: Type<T>):\n      Promise<ModuleWithComponentFactories<T>> {\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType);\n  }\n\n  getNgContentSelectors(component: Type<any>): string[] {\n    return this._compiler.getNgContentSelectors(component);\n  }\n\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> {\n    return this._compiler.getComponentFactory(component);\n  }\n\n  checkOverrideAllowed(type: Type<any>) {\n    if (this._compiler.hasAotSummary(type)) {\n      throw new Error(`${ɵstringify(type)} was AOT compiled, so its metadata cannot be changed.`);\n    }\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this.checkOverrideAllowed(ngModule);\n    const oldMetadata = this._moduleResolver.resolve(ngModule, false);\n    this._moduleResolver.setNgModule(\n        ngModule, this._overrider.overrideMetadata(NgModule, oldMetadata, override));\n  }\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.checkOverrideAllowed(directive);\n    const oldMetadata = this._directiveResolver.resolve(directive, false);\n    this._directiveResolver.setDirective(\n        directive, this._overrider.overrideMetadata(Directive, oldMetadata !, override));\n  }\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.checkOverrideAllowed(component);\n    const oldMetadata = this._directiveResolver.resolve(component, false);\n    this._directiveResolver.setDirective(\n        component, this._overrider.overrideMetadata(Component, oldMetadata !, override));\n  }\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.checkOverrideAllowed(pipe);\n    const oldMetadata = this._pipeResolver.resolve(pipe, false);\n    this._pipeResolver.setPipe(pipe, this._overrider.overrideMetadata(Pipe, oldMetadata, override));\n  }\n  loadAotSummaries(summaries: () => any[]) { this._compiler.loadAotSummaries(summaries); }\n  clearCache(): void { this._compiler.clearCache(); }\n  clearCacheFor(type: Type<any>) { this._compiler.clearCacheFor(type); }\n}\n\n/**\n * Platform for dynamic tests\n *\n * @experimental\n */\nexport const platformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef =\n    createPlatformFactory(platformCoreDynamic, 'coreDynamicTesting', [\n      {\n        provide: COMPILER_OPTIONS,\n        useValue: {\n          providers: [\n            MockPipeResolver,\n            {provide: PipeResolver, useExisting: MockPipeResolver},\n            MockDirectiveResolver,\n            {provide: DirectiveResolver, useExisting: MockDirectiveResolver},\n            MockNgModuleResolver,\n            {provide: NgModuleResolver, useExisting: MockNgModuleResolver},\n          ]\n        },\n        multi: true\n      },\n      {provide: TestingCompilerFactory, useClass: TestingCompilerFactoryImpl}\n    ]);\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵstringify as stringify} from '@angular/core';\nimport {MetadataOverride} from '@angular/core/testing';\n\ntype StringMap = {\n  [key: string]: any\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n      metadataClass: {new (options: T): C;}, oldMetadata: C, override: MetadataOverride<T>): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => props[prop] = (<any>oldMetadata)[prop]);\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (removeValue instanceof Array) {\n      removeValue.forEach(\n          (value: any) => { removeObjects.add(_propHashKey(prop, value, references)); });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (propValue instanceof Array) {\n      metadata[prop] = propValue.filter(\n          (value: any) => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && propValue instanceof Array) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  const replacer = (key: any, value: any) => {\n    if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, PipeResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, Pipe, Type} from '@angular/core';\n\n\nexport class MockPipeResolver extends PipeResolver {\n  private _pipes = new Map<Type<any>, Pipe>();\n\n  constructor(private _injector: Injector, refector: CompileReflector) { super(refector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(pipe: Type<any>) { this._compiler.clearCacheFor(pipe); }\n\n  /**\n   * Overrides the {@link Pipe} for a pipe.\n   */\n  setPipe(type: Type<any>, metadata: Pipe): void {\n    this._pipes.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link Pipe} for a pipe:\n   * - Set the {@link Pipe} to the overridden view when it exists or fallback to the\n   * default\n   * `PipeResolver`, see `setPipe`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): Pipe {\n    let metadata = this._pipes.get(type);\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound) !;\n    }\n    return metadata;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, NgModuleResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, NgModule, Type} from '@angular/core';\n\n\nexport class MockNgModuleResolver extends NgModuleResolver {\n  private _ngModules = new Map<Type<any>, NgModule>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  /**\n   * Overrides the {@link NgModule} for a module.\n   */\n  setNgModule(type: Type<any>, metadata: NgModule): void {\n    this._ngModules.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link NgModule} for a module:\n   * - Set the {@link NgModule} to the overridden view when it exists or fallback to the\n   * default\n   * `NgModuleResolver`, see `setNgModule`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): NgModule {\n    return this._ngModules.get(type) || super.resolve(type, throwIfNotFound) !;\n  }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CompileReflector, DirectiveResolver} from '@angular/compiler';\nimport {Compiler, Component, Directive, Injectable, Injector, Provider, Type, resolveForwardRef, ɵViewMetadata as ViewMetadata} from '@angular/core';\n\n\n\n/**\n * An implementation of {@link DirectiveResolver} that allows overriding\n * various properties of directives.\n */\n\nexport class MockDirectiveResolver extends DirectiveResolver {\n  private _directives = new Map<Type<any>, Directive>();\n  private _providerOverrides = new Map<Type<any>, any[]>();\n  private _viewProviderOverrides = new Map<Type<any>, any[]>();\n  private _views = new Map<Type<any>, ViewMetadata>();\n  private _inlineTemplates = new Map<Type<any>, string>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\n\n  resolve(type: Type<any>): Directive;\n  resolve(type: Type<any>, throwIfNotFound: true): Directive;\n  resolve(type: Type<any>, throwIfNotFound: boolean): Directive|null;\n  resolve(type: Type<any>, throwIfNotFound = true): Directive|null {\n    let metadata = this._directives.get(type) || null;\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound);\n    }\n    if (!metadata) {\n      return null;\n    }\n\n    const providerOverrides = this._providerOverrides.get(type);\n    const viewProviderOverrides = this._viewProviderOverrides.get(type);\n\n    let providers = metadata.providers;\n    if (providerOverrides != null) {\n      const originalViewProviders: Provider[] = metadata.providers || [];\n      providers = originalViewProviders.concat(providerOverrides);\n    }\n\n    if (metadata instanceof Component) {\n      let viewProviders = metadata.viewProviders;\n      if (viewProviderOverrides != null) {\n        const originalViewProviders: Provider[] = metadata.viewProviders || [];\n        viewProviders = originalViewProviders.concat(viewProviderOverrides);\n      }\n\n      let view = this._views.get(type) || metadata;\n      let animations = view.animations;\n      let templateUrl: string|undefined = view.templateUrl;\n\n      let inlineTemplate = this._inlineTemplates.get(type);\n      if (inlineTemplate) {\n        templateUrl = undefined;\n      } else {\n        inlineTemplate = view.template;\n      }\n\n      return new Component({\n        selector: metadata.selector,\n        inputs: metadata.inputs,\n        outputs: metadata.outputs,\n        host: metadata.host,\n        exportAs: metadata.exportAs,\n        moduleId: metadata.moduleId,\n        queries: metadata.queries,\n        changeDetection: metadata.changeDetection,\n        providers: providers,\n        viewProviders: viewProviders,\n        entryComponents: metadata.entryComponents,\n        template: inlineTemplate,\n        templateUrl: templateUrl,\n        animations: animations,\n        styles: view.styles,\n        styleUrls: view.styleUrls,\n        encapsulation: view.encapsulation,\n        interpolation: view.interpolation\n      });\n    }\n\n    return new Directive({\n      selector: metadata.selector,\n      inputs: metadata.inputs,\n      outputs: metadata.outputs,\n      host: metadata.host,\n      providers: providers,\n      exportAs: metadata.exportAs,\n      queries: metadata.queries\n    });\n  }\n\n  /**\n   * Overrides the {@link Directive} for a directive.\n   */\n  setDirective(type: Type<any>, metadata: Directive): void {\n    this._directives.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  setProvidersOverride(type: Type<any>, providers: Provider[]): void {\n    this._providerOverrides.set(type, providers);\n    this._clearCacheFor(type);\n  }\n\n  setViewProvidersOverride(type: Type<any>, viewProviders: Provider[]): void {\n    this._viewProviderOverrides.set(type, viewProviders);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Overrides the {@link ViewMetadata} for a component.\n   */\n  setView(component: Type<any>, view: ViewMetadata): void {\n    this._views.set(component, view);\n    this._clearCacheFor(component);\n  }\n  /**\n   * Overrides the inline template for a component - other configuration remains unchanged.\n   */\n  setInlineTemplate(component: Type<any>, template: string): void {\n    this._inlineTemplates.set(component, template);\n    this._clearCacheFor(component);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\nfunction flattenArray(tree: any[], out: Array<Type<any>|any[]>): void {\n  if (tree == null) return;\n  for (let i = 0; i < tree.length; i++) {\n    const item = resolveForwardRef(tree[i]);\n    if (Array.isArray(item)) {\n      flattenArray(item, out);\n    } else {\n      out.push(item);\n    }\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementSchemaRegistry} from '@angular/compiler';\nimport {SchemaMetadata, SecurityContext} from '@angular/core';\n\nexport class MockSchemaRegistry implements ElementSchemaRegistry {\n  constructor(\n      public existingProperties: {[key: string]: boolean},\n      public attrPropMapping: {[key: string]: string},\n      public existingElements: {[key: string]: boolean}, public invalidProperties: Array<string>,\n      public invalidAttributes: Array<string>) {}\n\n  hasProperty(tagName: string, property: string, schemas: SchemaMetadata[]): boolean {\n    const value = this.existingProperties[property];\n    return value === void 0 ? true : value;\n  }\n\n  hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean {\n    const value = this.existingElements[tagName.toLowerCase()];\n    return value === void 0 ? true : value;\n  }\n\n  allKnownElementNames(): string[] { return Object.keys(this.existingElements); }\n\n  securityContext(selector: string, property: string, isAttribute: boolean): SecurityContext {\n    return SecurityContext.NONE;\n  }\n\n  getMappedPropName(attrName: string): string { return this.attrPropMapping[attrName] || attrName; }\n\n  getDefaultComponentElementName(): string { return 'ng-component'; }\n\n  validateProperty(name: string): {error: boolean, msg?: string} {\n    if (this.invalidProperties.indexOf(name) > -1) {\n      return {error: true, msg: `Binding to property '${name}' is disallowed for security reasons`};\n    } else {\n      return {error: false};\n    }\n  }\n\n  validateAttribute(name: string): {error: boolean, msg?: string} {\n    if (this.invalidAttributes.indexOf(name) > -1) {\n      return {\n        error: true,\n        msg: `Binding to attribute '${name}' is disallowed for security reasons`\n      };\n    } else {\n      return {error: false};\n    }\n  }\n\n  normalizeAnimationStyleProperty(propName: string): string { return propName; }\n  normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string|number):\n      {error: string, value: string} {\n    return {error: null !, value: val.toString()};\n  }\n}\n"], "names": [], "mappings": ";;;;;GMAA;;;;;;;;;;GAeA;AAAA;IACA,4BAAA,kBAAA,EAAA,eAA6C,EAA7C,gBAAA,EAAA,iBAAA,EAAA,iBAAA;QAAA,IAAA,CAAA,kBAAA,GAAA,kBAAA,CAAA;QAEA,IAAA,CAAA,eAAA,GAAA,eAAA,CAAA;QACI,IAAJ,CAAA,gBAAA,GAAA,gBAAA,CAAA;QACI,IAAJ,CAAA,iBAA0B,GAA1B,iBAAA,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,iBAAA,CAAA;IAEE,CAAF;IACA,wCAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,OAAA;QACI,IAAJ,KAAA,GAAA,IAAA,CAAA,kBAAA,CAA0C,QAA1C,CAAA,CAAA;QACA,MAAA,CAAA,KAAA,KAAA,KAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;IA<PERSON>,CAAF;IAEE,uCAAF,GAAE,UAAF,OAAA,EAAA,WAAA;QACI,IAAJ,KAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA;QACA,MAAA,CAAA,KAAA,KAAA,KAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;IAEE,CAAF;IAEE,iDAAF,GAAE,cAAF,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA;IAEE,4CAAF,GAAE,UAAgB,QAAlB,EAAA,QAAA,EAAA,WAAA;QACI,MAAJ,CAAA,eAAA,CAAA,IAA+B,CAA/B;IACA,CAAA;IACA,8CAAA,GAAA,UAAA,QAAA,IAAA,MAAA,CAAA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,IAAA,QAAA,CAAA,CAAA,CAAA;IAAA,2DAAA,GAAA,cAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA;IACA,6CAAA,GAAA,UAAc,IAAd;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,EAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,0BAAA,IAAA,yCAAA,EAAA,CAAA;QAEA,CAAA;QACI,IAAI,CAAR,CAAA;YACM,MAAN,CAAa,EAAb,KAAA,EAAA,KAAA,EAAA,CAAA;QACA,CAAA;IACA,CAAA;IACA,8CAAA,GAAA,UAAA,IAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,MAAA,CAAA;gBACA,KAAc,EAAd,IAAA;gBACA,GAAA,EAAA,2BAAA,IAAA,yCAAA;aACA,CAAA;QAEA,CAAA;QACA,IAAA,CAAA,CAAA;YAEA,MAAA,CAAA,EAAA,KAAA,EAAyB,KAAzB,EAAgC,CAAhC;QACA,CAAA;IACA,CAAA;;ID9DA,yDAAA,GAAA,UAAA,aAAA,EAAA,gBAAA,EAAA,GAAA;;;;CCeA;;;;;;;GDEA;AAOA;;;GANA;AACA;IAAA,iDAAA;IACA,+BAAA,SAAA,EAAA,SAAA;QAAA,YACU,kBAAV,SAAA,CAAA,SAcA;QAbU,KAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAEA,KAAA,CAAA,WAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QAEc,KAAd,CAAA,kBAAA,GAAkD,IAAlD,GAAA,EAA2D,CAAC;QAE5D,KAAA,CAAA,sBAAA,GAAA,IAAA,GAAA,EAAgE,CAAhE;QAKA,KAAU,CAAV,MAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACI,KAAI,CAAR,gBAAA,GAAA,IAAA,GAAmC,EAAnC,CAAA;;IACA,CAAA;IACA,sBAAA,4CAAA;aAAA,cAAA,MAAsB,CAAC,IAAvB,CAAA,SAAqC,CAArC,GAAA,CAAA,QAAA,CAAA,CAAoD,CAAC,CAAC;;;OAAA;IACtD,8CAAA,GAAA,UAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,aAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;IACA,uCAAA,GAAA,UAAQ,IAAR,EAAA,eAAA;QAAA,gCAAA,EAAA,sBAAA;QACA,IAAM,QAAN,GAAiB,IAAjB,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YAEA,QAAA,GAAA,iBAAA,OAAA,YAAA,IAAA,EAAA,eAAsD,CAAtD,CAAA;QACI,CAAJ;QAEI,EAAJ,CAAA,CAAQ,CAAR,QAAiB,CAAjB,CAAA,CAAoB;YACZ,MAAR,CAAA,IAAA,CAAA;QACA,CAAA;QACA,IAAA,iBAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,qBAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QAEI,IAAI,SAAR,GAAA,QAA4B,CAA5B,SAAA,CAAuC;QACvC,EAAA,CAAA,CAAM,iBAAiB,IAAvB,IAAA,CAAA,CAAA,CAAkC;YAC5B,IAAN,qBAAA,GAAA,QAAA,CAAA,SAAA,IAAA,EAAA,CAAA;YACA,SAAA,GAAA,qBAAA,CAAkD,MAAlD,CAAA,iBAAA,CAAA,CAA4E;QAC5E,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,YAAA,SAAA,CAAA,CAAA,CAAA;YAEM,IAAI,aAAV,GAAA,QAAA,CAAA,aAAA,CAAA;YACM,EAAN,CAAA,CAAU,qBAAV,IAAA,IAAuC,CAAvC,CAAA,CAAA;gBACU,IAAV,qBAAA,GAA0D,QAA1D,CAAA,aAAA,IAAA,EAAA,CAAA;gBAEU,aAAV,GAAA,qBAAA,CAAgD,MAAhD,CAAA,qBAAA,CAAA,CAAA;YACM,CAAN;YACA,IAAQ,IAAR,GAAA,IAAmB,CAAnB,MAAA,CAAA,GAAA,CAA+B,IAA/B,CAAA,IAAA,QAAA,CAAA;YACA,IAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAAA;YAAA,IAAA,WAAA,GAAA,IAAA,CAAA,WAAA,CAAA;YACA,IAAQ,cAAc,GAAG,IAAI,CAAC,gBAA9B,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA;gBAEA,WAAA,GAAA,SAAA,CAAA;YACA,CAAA;YACA,IAAQ,CAAR,CAAA;gBACQ,cAAR,GAAyB,IAAzB,CAAA,QAAA,CAAA;YACA,CAAA;YACA,MAAA,CAAA,IAAA,SAAA,CAAA;gBACQ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,MAAR,EAAA,QAAA,CAAyB,MAAzB;gBACQ,OAAR,EAAA,QAAyB,CAAzB,OAAiC;gBACzB,IAAR,EAAA,QAAA,CAAA,IAAA;gBACQ,QAAR,EAAA,QAAA,CAAA,QAAA;gBACQ,QAAR,EAAA,QAAA,CAAA,QAAA;gBACQ,OAAR,EAAA,QAAA,CAAA,OAAA;gBACQ,eAAR,EAAA,QAAA,CAAA,eAAA;gBACQ,SAAR,EAAA,SAAA;gBACQ,aAAa,EAArB,aAAA;gBACQ,eAAe,EAAvB,QAAiC,CAAjC,eAAA;gBACQ,QAAR,EAAA,cAAA;gBACQ,WAAR,EAAqB,WAArB;gBACA,UAAA,EAAA,UAAA;gBACA,MAAA,EAAA,IAAA,CAAA,MAAA;gBAEA,SAAA,EAAA,IAAA,CAAA,SAAA;gBACA,aAAA,EAAyB,IAAzB,CAAA,aAAA;gBACA,aAAuB,EAAvB,IAA6B,CAA7B,aAAA;aACA,CAAA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,IAAA,SAAA,CAAA;YACM,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,MAAN,EAAA,QAAA,CAAuB,MAAvB;YACA,OAAA,EAAA,QAAA,CAAA,OAAA;YACA,IAAA,EAAA,QAAA,CAAA,IAAA;;;;SAKA,CAAA,CAAA;IACA,CAAA;IACA;;OAGA;IACA,4CAAA,GAAA,UAAA,IAAA,EAAA,QAA2B;QACvB,IAAI,CAAC,WAAT,CAAA,GAAwB,CAAxB,IAA6B,EAA7B,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;IAEE,CAAF;IACA,oDAAA,GAAA,UAAA,IAAA,EAAA,SAAoC;QAChC,IAAI,CAAC,kBAAT,CAA4B,GAA5B,CAAA,IAAA,EAAA,SAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;;;;QAKA,IAAU,CAAV,cAAkD,CAAlD,IAAA,CAAA,CAAA;IACA,CAAA;IACA;;;;;QAKA,IAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IACA;;;IAEA,iDAAA,GAAA,UAAA,SAAA,EAAA,QAAA;QACA,IAAQ,CAAR,gBAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA;;IAEO,4BAAP;AAAO,CAAP,AAvHA,CAAA,iBAAA,GAuHA;AACA,qBAAA,CAAA,UAAA,GAAA;IACA,EAAC,IAAI,EAAE,UAAP,EAAA;CACC,CAAC;;AD7IF,qBAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;GAaA;AAEA;IAAA,gDAAA;;yDAMA;;QADA,KAAA,CAAA,UAAA,GAAA,IAAiD,GAAjD,EAAA,CAAA;;IACA,CAAA;IACA;;;;;;;;;;;;OAeA;;;QACA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAiB,GAA0B,CAA3C,IAAA,CAAA,IAAA,iBAAA,OAAA,YAAA,IAAA,EAAA,eAAA,CAAA,CAAA;IACA,CAAA;IACA,sBAAA,2CAAA;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;;OAAA;;IAEO,2BAAP;AAAO,CAAP,AA3BA,CAAA,gBAAA,GA2BA;AACA,oBAAe,CAAf,UAAA,GAAA;IACA,EAAC,IAAI,EAAE,UAAP,EAAA;CACC,CAAC;;AD7CF,oBAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;GAaA;AAEA;IAAA,4CAAA;IAEE,0BAAF,SAAA,EAAA,QAAA;QAAE,YAEF,kBAAA,QAAA,CAAwC;;;;;IAKtC,sBAAF,uCAAA;aAAE,cAAF,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;;OAAA;IACA,yCAAA,GAAA,UAAe,IAAI,IAAnB,IAAA,CAAA,SAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;;;;;;;;;;OAaA;IACA,kCAAA,GAAA,UAAA,IAAA,EAAA,eAAA;QAAA,gCAAA,EAAA,sBAAA;QACA,IAAA,QAAA,GAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;YACA,QAAA,GAAA,iBAAA,OAAA,YAAA,IAAA,EAAA,eAAA,CAAA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,QAAA,CAAA;;IAEO,uBAAP;AAAO,CAAP,AA/BA,CAAA,YAAA,GA+BA;AACA,gBAAA,CAAA,UAAA,GAAA;IACA,EAAC,IAAI,EAAE,UAAP,EAAA;CACC,CAAC;;ADjDF,gBAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;GA4CA;;;;;IArBE,CAAF;IAEA;;;OAGA;IAEA,4CAAgB,GAAhB,UAAiB,aAAjB,EAAA,WAAA,EAAA,QAAA;QACA,IAAA,KAAA,GAAA,EAAkB,CAAC;QACnB,EAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAkB;YAClB,WAAA,CAAA,WAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,KAAA,CAAA,IAAA,CAAA,GAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA;YACQ,EAAR,CAAA,CAAA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBACA,MAAA,IAAoB,KAApB,CAA0B,+BAA1B,UAAA,CAAA,aAAA,CAAA,uBAAA,CAAA,CAAA;YACA,CAAA;YACQ,WAAR,CAAoB,KAApB,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,cAAA,CAAA,KAA4B,EAA5B,QAAA,CAAA,MAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA;YAEA,WAAA,CAAwB,KAAxB,EAAA,QAAA,CAAwD,GAAxD,CAAA,CAAA;QACA,CAAA;QACA,MAAA,CAAa,IAAI,aAAjB,CAAA,KAAA,CAAA,CAAA;IACA,CAAA;IACA,wBAAA;AAAA,CAAA,IAAA;AACA,wBAAkB,QACR,EADV,MAAA,EAAA,UAAA;IAEA,IAAA,aAAA,GAAA,IAAA,GAAA,EAAA,CAAA;4BAAA,IAAA;QACA,IAAA,WAAmB,GAAnB,MAAA,CAAA,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,WAAA,YAAA,KAAA,CAAA,CAAA,CAAA;YACA,WAAA,CAAA,OAAA,CAAA,UAAA,KAAA,IAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEA,CAAO;QACH,IAAJ,CAAA,CAAU;YACF,aAAR,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,WAAA,EAAA,UAAA,CAAA,CAAA,CAAA;QACA,CAAA;IAEA,CAAA;IAVA,GAAA,CAAA,CAAA,IAAA,IAAA,IAAA,MAAA,CAAA;gBAAA,IAAA;KAUA;4BAAA,IAAA;QACA,IAAA,SAAA,GAAA,QAA4B,CAA5B,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,YAAyB,KAAzB,CAAA,CAAA,CAAA;YACA,QAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,MAAA,CAAA,UAAA,KAAA,IAAA,OAAA,CAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,CAAK;QACL,IAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,QAAA,CAAA,IAAwC,CAAxC,GAAA,SAAA,CAAA;YACA,CAAA;QACI,CAAJ;IACA,CAAA;IAXA,GAAA,CAAA,CAAA,IAAA,IAAA,IAAA,QAAA,CAAA;gBAAA,IAAA;KAWA;AACA,CAAA;AACA,qBAAe,QAAQ,EAAvB,GAAA;IACA,GAAA,CAAA,CAAK,IAAL,IAAA,IAAA,GAAA,CAAA,CAAA,CAAA;QAAA,IAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,SAAA,GAAoB,QAApB,CAAA,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,IAAA,IAAA,IAAA,SAAA,YAAA,KAAA,CAAA,CAAA,CAAA;YACA,QAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA;QACA,CAAA;QAEA,IAAA,CAAA,CAAA;YACA,QAAA,CAAA,IAAA,CAAwB,GAAxB,QAAA,CAAA;QACI,CAAJ;IACA,CAAG;AACH,CAAC;AAED,qBAAA,QAAA,EAAA,GAAA;IACE,GAAF,CAAA,CAAA,IAAA,IAAA,IAAA,GAAA,CAA8B,CAA9B,CAAA;QACI,QAAJ,CAAA,IAAA,CAAA,GAAA,GAAA,CAAyB,IAAzB,CAAA,CAAA;IACA,CAAA;AACA,CAAA;AACA,sBAAA,QAAA,EAAA,SAAA,EAAA,UAAA;IACA,IAAA,QAAA,GAAA,UAAA,GAAA,EAAA,KAAA;QAEA,EAAA,CAAS,CAAT,OAAA,KAAA,KAAA,UAAA,CAAsC,CAAC,CAAvC;YACA,KAAA,GAAA,mBAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;QAEA,CAAA;QACM,MAAN,CAAA,KAAA,CAAA;IACE,CAAF,CAAA;IACA,MAAM,CAAN,QAAA,SAAqB,IAAI,CAAC,SAA1B,CAAA,SAA6C,EAAE,QAA/C,CAAA,CAAA;AACA,CAAA;AACA,6BAAA,GAAA,EAAA,UAAA;IACE,IAAF,EAAA,GAAW,UAAX,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QAGA,EAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,GAAA,gBAAA,EAAA,CAAA;QACA,UAA0B,CAA1B,GAAA,CAAA,GAAA,EAAA,EAAA,CAAA,CAAA;;IAEE,MAAM,CAAC,EAAT,CAAA;AACA,CAAA;AACA,qBAAA,GAAA;IACA,IAAA,KAAA,GAAA,EAAA,CAAA;IACA,uBAAA;;QAGM,EAAN,CAAA,CAAA,CAAW,IAAX,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACI,CAAJ;IACA,CAAA,CAAA,CAAA;IACA,UAAA;IACA,IAAA,KAAA,GAAQ,GAAR,CAAA;IACA,OAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,KAAA,CAAA,EAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,UAAA,SAAA;YACA,IAAA,IAAA,GAAA,MAAA,CAAA,wBAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAc,CAAC,SAAf,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,IAAA,IAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;;QDlIA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;GAwCA;AACA;IACA,oCAAA,gBAAA;QAIA,IAAA,CAAA,gBAAA,GAAA,gBAAA,CAAA;;IACA,0DAAA,GAAA,UAAA,OAAA;QACA,IAAA,QAAkB,GAAlB,IAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,mBAAA,CAAA,QAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,qBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,uBAAA,CAAA,CAAA,CAAA;;IAEO,iCAAP;AAAO,CAAP,AAVA,IAUA;AACA,0BAAA,CAAsB,UAAtB,GAAA;IACA,EAAA,IAAA,EAAA,UAAA,EAAA;CAGA,CAAA;AAEA,kBAAA;AACA,0BAAA,CAAA,cAAA,GAAA,cAAA,OAAA;IAAA,EAAA,IAAA,EAAA,eAAA,GAAA;CACA,EADA,CACA,CAAA;AAAA;IACA,6BAAA,SAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA;QAJU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAIA,IAAA,CAAA,kBAAA,GAAA,kBAAA,CAAA;QACM,IAAN,CAAA,aAAA,GAAA,aAAkD,CAAC;QAEnD,IAAA,CAAA,eAAA,GAAA,eAAA,CAAA;QACI,IAAJ,CAAA,iBAA0B,GAA1B,iBAAA,CAAA;QACA,IAAA,CAAA,UAAA,GAAA,IAAA,iBAAA,EAAA,CAAA;IAEE,CAAF;IACA,sBAAI,yCAAJ;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,CAAA;;;OAAA;IACA,+CAAA,GAAA,UAAA,UAAA;QACA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,CAA0D,UAA1D,CAAA,CAAA;IACA,CAAA;IACA,gDAAA,GAAA,UAAA,UAAA;QAEA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAA2D,UAA3D,CAAA,CAAA;IAEA,CAAA;IACA,+DAAA,GAAA,UAAA,UAAA;QAEA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,iCAAA,CAAA,UAAA,CAAA,CAAA;IACA,CAAA;IACA,gEAAA,GAAA,UAAA,UAAA;QAEA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,kCAAA,CAAA,UAAA,CAAA,CAAA;IACA,CAAA;IACA,mDAAA,GAAA,UAAA,SAAA;QAEA,MAAA,CAAA,IAAA,CAAA,SAAsC,CAAtC,qBAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IACA,iDAAA,GAAA,UAAA,SAAA;QACA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAG;IAED,kDAAF,GAAE,UAAF,IAAsC;QAClC,EAAJ,CAAA,CAAQ,IAAR,CAAA,SAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,IAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA,0DAAA,CAAA,CAAA;QACI,CAAJ;IAEA,CAAG;IACD,4CAAF,GAAE,UAAF,QAAA,EAAA,QAAA;QACI,IAAI,CAAC,oBAAoB,CAAC,QAA9B,CAAuC,CAAC;QACpC,IAAM,WAAW,GAAG,IAAI,CAAC,eAA7B,CAAA,OAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;QACI,IAAI,CAAC,eAAT,CAAA,WAAA,CAAA,QAAA,EAAA,IAAA,CACmB,UADnB,CAAA,gBAAA,CAAA,QAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IAEA,CAAG;IACD,+CAAiB,GAAjB,UAAkB,SAAoB,EAAE,QAAqC;QAC3E,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACrC,IAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAChC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IACzF,CAAG;IACD,+CAA4B,GAA5B,UAAF,SAAgE,EAAhE,QAAA;QACI,IAAI,CAAC,oBAAoB,CAAC,SAA9B,CAAA,CAAA;QACI,IAAM,WAAW,GAAG,IAAI,CAAC,kBAA7B,CAAA,OAAuD,CAAvD,SAAA,EAAA,KAAA,CAAA,CAAA;QACI,IAAI,CAAC,kBAAT,CAAA,YAAA,CAAyC,SAAzC,EAAoD,IAApD,CAAA,UAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IACA,CAAG;IACD,0CAAF,GAAE,UAAF,IAAmB,EAAnB,QAAA;QACA,IAAA,CAAA,oBAAqC,CAAC,IAAtC,CAAA,CAAA;QACA,IAAA,WAAA,GAAA,IAAA,CAAA,aAAA,CAAA,OAA+D,CAAC,IAAI,EAAE,KAAtE,CAAA,CAAA;QACA,IAAA,CAAA,aAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,IAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;;;;;;CA5DA;AAmEA;;;;GAKA;AACA,IAAA,0BAA4B,GAA5B,qBAAA,CAAA,mBAAA,EAAA,oBAAA,EAAA;IACA;QACA,OAAA,EAAA,gBAAA;QACA,QAAY,EAAC;YACb,SAAA,EAAA;gBACY,gBAAZ;gBACA,EAAA,OAAA,EAAA,YAAA,EAAA,WAAA,EAAA,gBAAA,EAAA;gBACA,qBAAA;gBACA,EAAA,OAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,qBAAA,EAAA;gBACA,oBAAA;gBACA,EAAA,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,oBAP6E,EAO7E;aACA;;QDhJA,KAAA,EAAA,IAAA;;;;;;;;;;GAYG;;;;;;;"}