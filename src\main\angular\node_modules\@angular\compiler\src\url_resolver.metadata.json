[{"__symbolic": "module", "version": 3, "metadata": {"createUrlResolverWithoutPackagePrefix": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "UrlResolver"}}}, "createOfflineCompileUrlResolver": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "UrlResolver"}, "arguments": ["."]}}, "DEFAULT_PACKAGE_URL_PROVIDER": {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PACKAGE_ROOT_URL"}, "useValue": "/"}, "UrlResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "PACKAGE_ROOT_URL"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "resolve": [{"__symbolic": "method"}]}}, "getUrlScheme": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"createUrlResolverWithoutPackagePrefix": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "UrlResolver"}}}, "createOfflineCompileUrlResolver": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "UrlResolver"}, "arguments": ["."]}}, "DEFAULT_PACKAGE_URL_PROVIDER": {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PACKAGE_ROOT_URL"}, "useValue": "/"}, "UrlResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "PACKAGE_ROOT_URL"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "resolve": [{"__symbolic": "method"}]}}, "getUrlScheme": {"__symbolic": "function"}}}]