{"version": 3, "file": "platform-browser-dynamic.umd.js", "sources": ["../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_cache.ts", "../../../../packages/platform-browser-dynamic/src/private_export.ts", "../../../../packages/platform-browser-dynamic/src/platform_providers.ts", "../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_impl.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n */\nexport class CachedResourceLoader extends ResourceLoader {\n  private _cache: {[url: string]: string};\n\n  constructor() {\n    super();\n    this._cache = (<any>global).$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n\n  get(url: string): Promise<string> {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return <Promise<any>>Promise.reject(\n          'CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nexport {ResourceLoaderImpl as ɵResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {ResourceLoader} from '@angular/compiler';\nimport {COMPILER_OPTIONS, PLATFORM_ID, Provider} from '@angular/core';\n\nimport {ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS as INTERNAL_BROWSER_PLATFORM_PROVIDERS} from '@angular/platform-browser';\n\nimport {ResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n\nexport const INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS: Provider[] = [\n  INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n  {\n    provide: COMPILER_OPTIONS,\n    useValue: {providers: [{provide: ResourceLoader, useClass: ResourceLoaderImpl}]},\n    multi: true\n  },\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ResourceLoader} from '@angular/compiler';\nimport {Injectable} from '@angular/core';\n\n\n\nexport class ResourceLoaderImpl extends ResourceLoader {\n  get(url: string): Promise<string> {\n    let resolve: (result: any) => void;\n    let reject: (error: any) => void;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function() {\n      // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n      // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n      // by IE10)\n      const response = xhr.response || xhr.responseText;\n\n      // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n      let status = xhr.status === 1223 ? 204 : xhr.status;\n\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function() { reject(`Failed to load ${url}`); };\n\n    xhr.send();\n    return promise;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["Resource<PERSON><PERSON>der", "ɵglobal", "tslib_1.__extends", "COMPILER_OPTIONS", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "Injectable"], "mappings": ";;;;;;AIAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;AD5JP;;;;;;;AAiBA,IAAA,kBAAA,IAAA,UAAA,MAAA,EAAA;IAAAE,SAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;IAAA,SAAA,kBAAA,GAAA;;KAuCA;IAtCA,kBAAA,CAAA,SAAA,CAAA,GAAA,GAAA,UAAA,GAAA,EAAA;QACA,IAAA,OAAA,CAAA;QACI,IAAJ,MAAA,CAAA;QACI,IAAJ,OAAA,GAAoB,IAApB,OAA+B,CAA/B,UAAA,GAAA,EAAA,GAAA,EAAA;YACQ,OAAR,GAAA,GAAA,CAAA;YAEQ,MAAM,GAAG,GAAjB,CAAA;;;;QAIA,GAAA,CAAM,YAAN,GAAA,MAA2B,CAA3B;;;;;;;YASA,IAAQ,MAAM,GAAG,GAAjB,CAAA,MAAA,KAAA,IAAoC,GAApC,GAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;;;YAKA,IAAA,MAAA,KAAA,CAAA,EAAA;gBAAA,MAAA,GAAA,QAAA,GAAA,GAAA,GAAA,CAAA,CAAA;aACA;YACA,IAAA,GAAA,IAAA,MAAA,IAAA,MAAA,IAAA,GAAA,EAAA;gBACA,OAAA,CAAA,QAAA,CAAA,CAAA;aAEA;iBAEA;gBACA,MAAkB,CAAC,iBAAnB,GAAA,GAAA,CAAA,CAAA;aACA;;QACA,GAAA,CAAA,OAAA,GAAA,YAAA,EAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,CAAA,EAAA,CAAA;QACA,GAAA,CAAQ,IAAR,EAAA,CAAA;QACA,OAAA,OAAA,CAAA;;IAEO,OAAP,kBAAA,CAAA;CAAA,CAvCAF,gCAAA,CAuCA,CAAA,CAAA;;IDxDA,EAAA,IAAA,EAAAK,wBAAA,EAAA;;;;;;;;;;;AAqBA,IAAA,2CAAA,GAAA;IACAD,6DAAA;IACE;QACF,OAAA,EAAAD,8BAAA;;QFxBA,KAAA,EAAA,IAAA;;;;;;;;;;;;;;;;;;AAyBA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAAAD,SAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;IACA,SAAA,oBAAA,GAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAMA;QAHM,KAAN,CAAA,MAAA,GAAAD,qBAAA,CAAA,cAAA,CAAA;QACI,IAAI,KAAI,CAAC,MAAM,IAAnB,IAAA,EAAA;YACM,MAAN,IAAA,KAAqB,CAArB,uEAAA,CAAA,CAAA;SACK;;KAAL;IACA,oBAAA,CAAA,SAAA,CAAA,GAAA,GAAA,UAAA,GAAA,EAAA;QAEA,IAAA,IAAA,CAAA,MAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;YACA,OAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;SACA;;YCrCA,OAAA,OAAA,CAAA,MAAA,CAAA,yDAAA,GAAA,GAAA,CAAA,CAAA;;;;CDyBA,CAAAD,gCAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}