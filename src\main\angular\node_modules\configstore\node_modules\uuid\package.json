{"_args": [["uuid@2.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "uuid@2.0.3", "_id": "uuid@2.0.3", "_inBundle": false, "_integrity": "sha1-Z+LoY3lyFVMN/zGOW/nc6/1Hsho=", "_location": "/configstore/uuid", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "uuid@2.0.3", "name": "uuid", "escapedName": "uuid", "rawSpec": "2.0.3", "saveSpec": null, "fetchSpec": "2.0.3"}, "_requiredBy": ["/configstore"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/uuid/-/uuid-2.0.3.tgz", "_spec": "2.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./rng.js": "./rng-browser.js"}, "bugs": {"url": "https://github.com/defunctzombie/node-uuid/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "devDependencies": {"mocha": "1.8.0"}, "homepage": "https://github.com/defunctzombie/node-uuid#readme", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "main": "./uuid.js", "name": "uuid", "repository": {"type": "git", "url": "git+https://github.com/defunctzombie/node-uuid.git"}, "scripts": {"test": "mocha test/test.js"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "version": "2.0.3"}