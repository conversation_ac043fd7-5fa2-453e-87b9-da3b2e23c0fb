<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Angular2 Npm Package Example</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"/>
  <script src="moment-with-locales.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.11/moment-timezone-with-data-2010-2020.js"></script>
  <style> #my-div .datetime-picker { background: #fff; } </style>
</head>

<body>
<my-app>Loading...</my-app>
<script>
  document.write('<base href="' + document.location + '" />');

  var script = document.createElement("script");
  script.src = location.pathname.replace('index.html', '') + 'build/app.js';
  (document.body).appendChild(script);
</script>
</body>
</html>
