{"title": "CSS Paged Media (@page)", "description": "CSS at-rule (`@page`) to define page-specific rules when printing web pages, such as margin per page and page dimensions.", "spec": "https://drafts.csswg.org/css-page-3/", "status": "wd", "links": [{"url": "http://www.htmluse.com/css-paged-media-page-rule/", "title": "CSS Paged media article"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/@page", "title": "Mozilla Developer Network (MDN) documentation - CSS @page"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=85062", "title": "WebKit support bug"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=286443", "title": "Firefox support bug"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/12839052-css-paged-media-module-level-3", "title": "Edge support request"}], "bugs": [{"description": "Presto-based Opera (<= 12.1) recognizes the `size` property, but seems to have buggy behavior in interpreting the dimensions."}], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "u"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "u", "4.1": "u", "4.2-4.3": "u", "4.4": "u", "4.4.3-4.4.4": "u", "56": "u"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "u", "37": "n #2"}, "and_chr": {"59": "y"}, "and_ff": {"54": "a #1"}, "ie_mob": {"10": "n #2", "11": "n #2"}, "and_uc": {"11.4": "n #2"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Currently no browsers appear to support the `marks` & `bleed` properties from the latest version of the specification.\r\n\r\n", "notes_by_num": {"1": "Does not support the `size` property", "2": "Does not appear to have a way to print web pages"}, "usage_perc_y": 59.65, "usage_perc_a": 11.16, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}