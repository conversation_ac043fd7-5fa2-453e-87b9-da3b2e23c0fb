{"title": "Crisp edges/pixelated images", "description": "Scales images with an algorithm that preserves edges and contrast, without smoothing colors or introducing blur. This is intended for images such as pixel art. Official values that accomplish this for the `image-rendering` property are `crisp-edges` and `pixelated`.", "spec": "http://dev.w3.org/csswg/css-images-3/#valdef-image-rendering-crisp-edges", "status": "unoff", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/image-rendering", "title": "Mozilla Developer Network (MDN) documentation - CSS Image rendering"}, {"url": "http://updates.html5rocks.com/2015/01/pixelated", "title": "HTML5Rocks article"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=856337", "title": "Firefox bug #856337: Implement image-rendering: pixelated"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=317991", "title": "Chrome bug #317991: Implement image-rendering:crisp-edges"}], "bugs": [{"description": "`image-rendering:-webkit-optimize-contrast;` and `-ms-interpolation-mode:nearest-neighbor` do not affect CSS images."}], "categories": ["CSS", "CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "a x #2 #5", "8": "a x #2 #5", "9": "a x #2 #5", "10": "a x #2 #5", "11": "a x #2 #5"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "y x #3", "4": "y x #3", "5": "y x #3", "6": "y x #3", "7": "y x #3", "8": "y x #3", "9": "y x #3", "10": "y x #3", "11": "y x #3", "12": "y x #3", "13": "y x #3", "14": "y x #3", "15": "y x #3", "16": "y x #3", "17": "y x #3", "18": "y x #3", "19": "y x #3", "20": "y x #3", "21": "y x #3", "22": "y x #3", "23": "y x #3", "24": "y x #3", "25": "y x #3", "26": "y x #3", "27": "y x #3", "28": "y x #3", "29": "y x #3", "30": "y x #3", "31": "y x #3", "32": "y x #3", "33": "y x #3", "34": "y x #3", "35": "y x #3", "36": "y x #3", "37": "y x #3", "38": "y x #3", "39": "y x #3", "40": "y x #3", "41": "y x #3", "42": "y x #3", "43": "y x #3", "44": "y x #3", "45": "y x #3", "46": "y x #3", "47": "y x #3", "48": "y x #3", "49": "y x #3", "50": "y x #3", "51": "y x #3", "52": "y x #3", "53": "y x #3", "54": "y x #3", "55": "y x #3", "56": "y x #3", "57": "y x #3"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "y #4", "42": "y #4", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4", "49": "y #4", "50": "y #4", "51": "y #4", "52": "y #4", "53": "y #4", "54": "y #4", "55": "y #4", "56": "y #4", "57": "y #4", "58": "y #4", "59": "y #4", "60": "y #4", "61": "y #4", "62": "y #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "a x #1", "6.1": "a x #3 #6", "7": "a x #3 #6", "7.1": "a x #3 #6", "8": "a x #3 #6", "9": "a x #3 #6", "9.1": "a x #3 #6", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "y x #3", "12": "y x #3", "12.1": "y x #3", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "y #4", "29": "y #4", "30": "y #4", "31": "y #4", "32": "y #4", "33": "y #4", "34": "y #4", "35": "y #4", "36": "y #4", "37": "y #4", "38": "y #4", "39": "y #4", "40": "y #4", "41": "y #4", "42": "y #4", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a x #1 #6", "6.0-6.1": "a x #1 #6", "7.0-7.1": "a x #3 #6", "8": "a x #3 #6", "8.1-8.4": "a x #3 #6", "9.0-9.2": "a x #3 #6", "9.3": "a x #3 #6", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y #4"}, "bb": {"7": "n", "10": "a x #1 #6"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "y x #3", "12.1": "y x #3", "37": "y #4"}, "and_chr": {"59": "y #4"}, "and_ff": {"54": "y x #3"}, "ie_mob": {"10": "a x #2 #5", "11": "a x #2 #5"}, "and_uc": {"11.4": "a x #1 #6"}, "samsung": {"4": "y #4", "5": "y #4"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "y #4"}}, "notes": "Note that prefixes apply to the value (e.g. `-moz-crisp-edges`), not the `image-rendering` property.", "notes_by_num": {"1": "Supported using the non-standard value `-webkit-optimize-contrast`", "2": "Internet Explorer accomplishes support using the non-standard declaration `-ms-interpolation-mode: nearest-neighbor`", "3": "Supports the `crisp-edges` value, but not `pixelated`.", "4": "Supports the `pixelated` value, but not `crisp-edges`.", "5": "Only works on `<img>`, not CSS backgrounds or `<canvas>`.", "6": "Only works on `<img>` and CSS backgrounds, _not_ `<canvas>`. "}, "usage_perc_y": 76.23, "usage_perc_a": 14.69, "ucprefix": false, "parent": "", "keywords": "image-rendering,crisp-edges", "ie_id": "imagerendering", "chrome_id": "5118058116939776", "firefox_id": "", "webkit_id": "", "shown": true}