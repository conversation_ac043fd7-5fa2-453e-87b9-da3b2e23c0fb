{"version": 3, "file": "version.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/version.ts"], "names": [], "mappings": ";;AAAA,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,6BAA6B;AAC7B,+CAA+C;AAC/C,+BAA+B;AAC/B,6CAA6C;AAG7C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;IACpC,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,8BAA8B;IAC3C,OAAO,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACjC,KAAK,EAAE,YAAY;IAEnB,gBAAgB,EAAE,CAAC;YACjB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,sCAAsC;SACpD,CAAC;IAEF,GAAG,EAAE,UAAU,OAAY;QACzB,IAAI,QAAQ,GAAQ,OAAO,CAAC,QAAQ,CAAC;QACrC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;QACnE,IAAI,OAAY,CAAC;QACjB,IAAI,CAAC;YACH,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACnB,OAAO,GAAG,SAAS,CAAC;QACtB,CAAC;QAED,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,QAAQ,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;QAEpD,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAEzC,IAAI,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC;QAC/B,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,SAAS,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,EAAE,GAAG,aAAa,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAC,GAAG,EAAE,SAAS,EAAC,CAAC,CAAC;gBAC1F,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1D,CAAC;YAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC;YAED,YAAY,GAAG,WAAW,GAAG,CAAC,OAAO,aAAa,SAAS,GAAG,CAAC;QACjE,CAAC;QACD,MAAM,MAAM,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC;QACvC,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YACrD,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClC,YAAY,IAAI,MAAM,CAAC;YACzB,CAAC;QACH,CAAC;QAED,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACZ,KAAK,CAAC,OAAO,CAAC,IAAI;gBAChB,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC;QACD,MAAM,QAAQ,GAAG;;;;;qBAKA,CAAC;QAClB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAEhD,GAAG,CAAC,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3D,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAED,qBAAqB,EAAE,UAAS,GAAQ,EAAE,MAAc;QACtD,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;aACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;aACjD,MAAM,CAAC,OAAO,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aACxD,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAEvD,MAAM,CAAC,OAAO,CAAC;IACjB,CAAC;IAED,UAAU,EAAE,UAAS,UAAkB;QACrC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CACpC,IAAI,CAAC,OAAO,CAAC,IAAI,EACjB,cAAc,EACd,UAAU,EACV,cAAc,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;QAC3B,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAED,YAAY,EAAE,UAAU,MAAc,EAAE,OAAe;QACrD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC,CAAC;AAGH,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;AACnC,kBAAe,cAAc,CAAC"}