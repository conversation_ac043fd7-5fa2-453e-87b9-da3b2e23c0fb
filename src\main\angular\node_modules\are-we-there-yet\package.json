{"_args": [["are-we-there-yet@1.1.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "are-we-there-yet@1.1.5", "_id": "are-we-there-yet@1.1.5", "_inBundle": false, "_integrity": "sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w==", "_location": "/are-we-there-yet", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "are-we-there-yet@1.1.5", "name": "are-we-there-yet", "escapedName": "are-we-there-yet", "rawSpec": "1.1.5", "saveSpec": null, "fetchSpec": "1.1.5"}, "_requiredBy": ["/npmlog"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz", "_spec": "1.1.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "http://re-becca.org"}, "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "description": "Keep track of the overall completion of many disparate processes", "devDependencies": {"standard": "^11.0.1", "tap": "^12.0.1"}, "files": ["index.js", "tracker-base.js", "tracker-group.js", "tracker-stream.js", "tracker.js", "CHANGES.md"], "homepage": "https://github.com/iarna/are-we-there-yet", "license": "ISC", "main": "index.js", "name": "are-we-there-yet", "repository": {"type": "git", "url": "git+https://github.com/iarna/are-we-there-yet.git"}, "scripts": {"test": "standard && tap test/*.js"}, "version": "1.1.5"}