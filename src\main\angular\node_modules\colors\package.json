{"_args": [["colors@1.1.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "colors@1.1.2", "_id": "colors@1.1.2", "_inBundle": false, "_integrity": "sha1-FopHAXVran9RoSzgyXv6KMCE7WM=", "_location": "/colors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "colors@1.1.2", "name": "colors", "escapedName": "colors", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/jasmine-spec-reporter", "/karma", "/svgo", "/tslint"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/colors/-/colors-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "Marak Squires"}, "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "description": "get colors in your node.js console", "engines": {"node": ">=0.1.90"}, "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "homepage": "https://github.com/Marak/colors.js", "keywords": ["ansi", "terminal", "colors"], "license": "MIT", "main": "lib", "name": "colors", "repository": {"type": "git", "url": "git+ssh://**************/Marak/colors.js.git"}, "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "version": "1.1.2"}