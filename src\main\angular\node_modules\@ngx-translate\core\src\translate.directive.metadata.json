[{"__symbolic": "module", "version": 3, "metadata": {"TranslateDirective": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[translate],[ngx-translate]"}]}], "members": {"translate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "translateParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./translate.service", "name": "TranslateService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "ngAfterViewChecked": [{"__symbolic": "method"}], "checkNodes": [{"__symbolic": "method"}], "updateValue": [{"__symbolic": "method"}], "getContent": [{"__symbolic": "method"}], "setContent": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslateDirective": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[translate],[ngx-translate]"}]}], "members": {"translate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "translateParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./translate.service", "name": "TranslateService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "ngAfterViewChecked": [{"__symbolic": "method"}], "checkNodes": [{"__symbolic": "method"}], "updateValue": [{"__symbolic": "method"}], "getContent": [{"__symbolic": "method"}], "setContent": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}}}]