{"version": 3, "file": "animations-browser.umd.js", "sources": ["../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../packages/animations/browser/src/dsl/animation_dsl_visitor.ts", "../../../../packages/animations/browser/src/dsl/animation_ast.ts", "../../../../packages/animations/browser/src/util.ts", "../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../packages/animations/browser/src/render/shared.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../shared';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\nexport class WebAnimationsDriver implements AnimationDriver {\n/**\n * @param {?} element\n * @param {?} selector\n * @return {?}\n */\nmatchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n/**\n * @param {?} elm1\n * @param {?} elm2\n * @return {?}\n */\ncontainsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n/**\n * @param {?} element\n * @param {?} selector\n * @param {?} multi\n * @return {?}\n */\nquery(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n/**\n * @param {?} element\n * @param {?} prop\n * @param {?=} defaultValue\n * @return {?}\n */\ncomputeStyle(element: any, prop: string, defaultValue?: string): string {\n    return /** @type {?} */(( ( /** @type {?} */((window.getComputedStyle(element) as any)))[prop] as string));\n  }\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @param {?=} previousPlayers\n * @return {?}\n */\nanimate(\n      element: any, keyframes: ɵStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = []): WebAnimationsPlayer {\n    const /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n    const /** @type {?} */ playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */(( <WebAnimationsPlayer[]>previousPlayers.filter(\n        player => { return player instanceof WebAnimationsPlayer; })));\n    return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n  }\n}\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n  return typeof Element !== 'undefined' && typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {copyStyles, eraseStyles, setStyles} from '../../util';\n\nimport {DOMAnimation} from './dom_animation';\nexport class WebAnimationsPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _player: DOMAnimation;\nprivate _duration: number;\nprivate _delay: number;\nprivate _initialized = false;\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finalKeyframe: {[key: string]: string | number};\npublic time = 0;\npublic parentPlayer: AnimationPlayer|null = null;\npublic previousStyles: {[styleName: string]: string | number};\npublic currentSnapshot: {[styleName: string]: string | number} = {};\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @param {?=} previousPlayers\n */\nconstructor(\npublic element: any,\npublic keyframes: {[key: string]: string | number}[],\npublic options: {[key: string]: string | number},\nprivate previousPlayers: WebAnimationsPlayer[] = []) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n\n    this.previousStyles = {};\n    previousPlayers.forEach(player => {\n      let styles = player.currentSnapshot;\n      Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n    });\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n/**\n * @return {?}\n */\nprivate _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const /** @type {?} */ keyframes = this.keyframes.map(styles => copyStyles(styles, false));\n    const /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n    if (previousStyleProps.length) {\n      let /** @type {?} */ startingKeyframe = keyframes[0];\n      let /** @type {?} */ missingStyleProps: string[] = [];\n      previousStyleProps.forEach(prop => {\n        if (!startingKeyframe.hasOwnProperty(prop)) {\n          missingStyleProps.push(prop);\n        }\n        startingKeyframe[prop] = this.previousStyles[prop];\n      });\n\n      if (missingStyleProps.length) {\n        const /** @type {?} */ self = this;\n        // tslint:disable-next-line\n        for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n          let /** @type {?} */ kf = keyframes[i];\n          missingStyleProps.forEach(function(prop) {\n            kf[prop] = _computeStyle(self.element, prop);\n          });\n        }\n      }\n    }\n\n    this._player = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n    this._player.addEventListener('finish', () => this._onFinish());\n  }\n/**\n * @return {?}\n */\nprivate _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this._player.pause();\n    }\n  }\n/**\n * \\@internal\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @return {?}\n */\n_triggerWebAnimation(element: any, keyframes: any[], options: any): DOMAnimation {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return /** @type {?} */(( element['animate'](keyframes, options) as DOMAnimation));\n  }\n/**\n * @return {?}\n */\nget domPlayer() { return this._player; }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nplay(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n    this._player.play();\n  }\n/**\n * @return {?}\n */\npause(): void {\n    this.init();\n    this._player.pause();\n  }\n/**\n * @return {?}\n */\nfinish(): void {\n    this.init();\n    this._onFinish();\n    this._player.finish();\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @return {?}\n */\nprivate _resetDomPlayerState() {\n    if (this._player) {\n      this._player.cancel();\n    }\n  }\n/**\n * @return {?}\n */\nrestart(): void {\n    this.reset();\n    this.play();\n  }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._resetDomPlayerState();\n      this._onFinish();\n      this._destroyed = true;\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._player.currentTime = p * this.time; }\n/**\n * @return {?}\n */\ngetPosition(): number { return this._player.currentTime / this.time; }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._delay + this._duration; }\n/**\n * @return {?}\n */\nbeforeDestroy() {\n    const /** @type {?} */ styles: {[key: string]: string | number} = {};\n    if (this.hasStarted()) {\n      Object.keys(this._finalKeyframe).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] =\n              this._finished ? this._finalKeyframe[prop] : _computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n}\n\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDoneFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onStartFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDestroyFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._player;\n/** @type {?} */\nWebAnimationsPlayer.prototype._duration;\n/** @type {?} */\nWebAnimationsPlayer.prototype._delay;\n/** @type {?} */\nWebAnimationsPlayer.prototype._initialized;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finished;\n/** @type {?} */\nWebAnimationsPlayer.prototype._started;\n/** @type {?} */\nWebAnimationsPlayer.prototype._destroyed;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finalKeyframe;\n/** @type {?} */\nWebAnimationsPlayer.prototype.time;\n/** @type {?} */\nWebAnimationsPlayer.prototype.parentPlayer;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousStyles;\n/** @type {?} */\nWebAnimationsPlayer.prototype.currentSnapshot;\n/** @type {?} */\nWebAnimationsPlayer.prototype.element;\n/** @type {?} */\nWebAnimationsPlayer.prototype.keyframes;\n/** @type {?} */\nWebAnimationsPlayer.prototype.options;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousPlayers;\n}\n\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element: any, prop: string): string {\n  return ( /** @type {?} */((<any>window.getComputedStyle(element))))[prop];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\nexport class AnimationEngine {\nprivate _transitionEngine: TransitionAnimationEngine;\nprivate _timelineEngine: TimelineAnimationEngine;\nprivate _triggerCache: {[key: string]: AnimationTrigger} = {};\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    this._transitionEngine = new TransitionAnimationEngine(driver, normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(driver, normalizer);\n\n    this._transitionEngine.onRemovalComplete =\n        (element: any, context: any) => { this.onRemovalComplete(element, context); }\n  }\n/**\n * @param {?} componentId\n * @param {?} namespaceId\n * @param {?} hostElement\n * @param {?} name\n * @param {?} metadata\n * @return {?}\n */\nregisterTrigger(\n      componentId: string, namespaceId: string, hostElement: any, name: string,\n      metadata: AnimationTriggerMetadata): void {\n    const /** @type {?} */ cacheKey = componentId + '-' + name;\n    let /** @type {?} */ trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const /** @type {?} */ errors: any[] = [];\n      const /** @type {?} */ ast = /** @type {?} */(( buildAnimationAst( /** @type {?} */((metadata as AnimationMetadata)), errors) as TriggerAst));\n      if (errors.length) {\n        throw new Error(\n            `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n      }\n      trigger = buildTrigger(name, ast);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\nonInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\nonRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} property\n * @param {?} value\n * @return {?}\n */\nsetProperty(namespaceId: string, element: any, property: string, value: any): boolean {\n    // @@property\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const /** @type {?} */ args = /** @type {?} */(( value as any[]));\n      this._timelineEngine.command(id, element, action, args);\n      return false;\n    }\n    return this._transitionEngine.trigger(namespaceId, element, property, value);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} eventName\n * @param {?} eventPhase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, eventName: string, eventPhase: string,\n      callback: (event: any) => any): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1): void { this._transitionEngine.flush(microtaskId); }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] {\n    return ( /** @type {?} */((this._transitionEngine.players as AnimationPlayer[])))\n        .concat( /** @type {?} */((this._timelineEngine.players as AnimationPlayer[])));\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this._transitionEngine.whenRenderingDone(); }\n}\n\nfunction AnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationEngine.prototype._transitionEngine;\n/** @type {?} */\nAnimationEngine.prototype._timelineEngine;\n/** @type {?} */\nAnimationEngine.prototype._triggerCache;\n/** @type {?} */\nAnimationEngine.prototype.onRemovalComplete;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_PLAYER_ARRAY: AnimationPlayer[] = [];\nconst /** @type {?} */ NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst /** @type {?} */ NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\nexport interface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nexport const /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n\nexport interface ElementAnimationState {\n  setForRemoval: any;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n}\nexport class StateValue {\npublic value: string;\npublic options: AnimationOptions;\n/**\n * @param {?} input\n */\nconstructor(input: any) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      const options = copyObj(input as any);\n      delete options['value'];\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n/**\n * @param {?} options\n * @return {?}\n */\nabsorbOptions(options: AnimationOptions) {\n    const /** @type {?} */ newParams = options.params;\n    if (newParams) {\n      const /** @type {?} */ oldParams = /** @type {?} */(( this.options.params));\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nfunction StateValue_tsickle_Closure_declarations() {\n/** @type {?} */\nStateValue.prototype.value;\n/** @type {?} */\nStateValue.prototype.options;\n}\n\n\nexport const /** @type {?} */ VOID_VALUE = 'void';\nexport const /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport const /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nexport class AnimationTransitionNamespace {\npublic players: TransitionAnimationPlayer[] = [];\nprivate _triggers: {[triggerName: string]: AnimationTrigger} = {};\nprivate _queue: QueueInstruction[] = [];\nprivate _elementListeners = new Map<any, TriggerListener[]>();\nprivate _hostClassName: string;\n/**\n * @param {?} id\n * @param {?} hostElement\n * @param {?} _engine\n */\nconstructor(\npublic id: string,\npublic hostElement: any,\nprivate _engine: TransitionAnimationEngine) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.hasOwnProperty(name)) {\n      throw new Error(\n          `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw new Error(\n          `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw new Error(\n          `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n    }\n\n    const /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n    const /** @type {?} */ data = {name, phase, callback};\n    listeners.push(data);\n\n    const /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n    if (!triggersWithStates.hasOwnProperty(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates[name] = null;\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const /** @type {?} */ index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers[name]) {\n          delete triggersWithStates[name];\n        }\n      });\n    };\n  }\n/**\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nregister(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers[name]) {\n      // throw\n      return false;\n    } else {\n      this._triggers[name] = ast;\n      return true;\n    }\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate _getTrigger(name: string) {\n    const /** @type {?} */ trigger = this._triggers[name];\n    if (!trigger) {\n      throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n    }\n    return trigger;\n  }\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} value\n * @param {?=} defaultToFallback\n * @return {?}\n */\ntrigger(element: any, triggerName: string, value: any, defaultToFallback: boolean = true):\n      TransitionAnimationPlayer|undefined {\n    const /** @type {?} */ trigger = this._getTrigger(triggerName);\n    const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = {});\n    }\n\n    let /** @type {?} */ fromState = triggersWithStates[triggerName];\n    const /** @type {?} */ toState = new StateValue(value);\n\n    const /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates[triggerName] = toState;\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    } else if (fromState === DELETED_STATE_VALUE) {\n      return player;\n    }\n\n    const /** @type {?} */ playersOnElement: TransitionAnimationPlayer[] =\n        getOrSetAsInMap(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new palyer)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n    let /** @type {?} */ isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push(\n        {element, triggerName, transition, fromState, toState, player, isFallbackTransition});\n\n    if (!isFallbackTransition) {\n      addClass(element, NG_ANIMATING_CLASSNAME);\n    }\n\n    player.onDone(() => {\n      removeClass(element, NG_ANIMATING_CLASSNAME);\n\n      let /** @type {?} */ index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const /** @type {?} */ players = this._engine.playersByElement.get(element);\n      if (players) {\n        let /** @type {?} */ index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nderegister(name: string) {\n    delete this._triggers[name];\n\n    this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n          element, listeners.filter(entry => { return entry.name != name; }));\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n/**\n * @param {?} rootElement\n * @param {?} context\n * @param {?=} animate\n * @return {?}\n */\nprivate _destroyInnerNodes(rootElement: any, context: any, animate: boolean = false) {\n    this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n      if (animate && containsClass(elm, this._hostClassName)) {\n        const /** @type {?} */ innerNs = this._engine.namespacesByHostElement.get(elm);\n\n        // special case for a host element with animations on the same element\n        if (innerNs) {\n          innerNs.removeNode(elm, context, true);\n        }\n\n        this.removeNode(elm, context, true);\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n  }\n/**\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(element: any, context: any, doNotRecurse?: boolean): void {\n    const /** @type {?} */ engine = this._engine;\n\n    if (!doNotRecurse && element.childElementCount) {\n      this._destroyInnerNodes(element, context, true);\n    }\n\n    const /** @type {?} */ triggerStates = engine.statesByElement.get(element);\n    if (triggerStates) {\n      const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n      Object.keys(triggerStates).forEach(triggerName => {\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers[triggerName]) {\n          const /** @type {?} */ player = this.trigger(element, triggerName, VOID_VALUE, false);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        engine.markElementAsRemoved(this.id, element, true, context);\n        optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n        return;\n      }\n    }\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let /** @type {?} */ containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const /** @type {?} */ currentPlayers =\n          engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue fowards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let /** @type {?} */ parent = element;\n        while (parent = parent.parentNode) {\n          const /** @type {?} */ triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    const /** @type {?} */ listeners = this._elementListeners.get(element);\n    if (listeners) {\n      const /** @type {?} */ visitedTriggers = new Set<string>();\n      listeners.forEach(listener => {\n        const /** @type {?} */ triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const /** @type {?} */ trigger = this._triggers[triggerName];\n        const /** @type {?} */ transition = trigger.fallbackTransition;\n        const /** @type {?} */ elementStates = /** @type {?} */(( engine.statesByElement.get(element)));\n        const /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n        const /** @type {?} */ toState = new StateValue(VOID_VALUE);\n        const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      // we do this after the flush has occurred such\n      // that the callbacks can be fired\n      engine.afterFlush(() => this.clearElementCache(element));\n      engine.destroyInnerAnimations(element);\n      engine._onRemovalComplete(element, context);\n    }\n  }\n/**\n * @param {?} element\n * @param {?} parent\n * @return {?}\n */\ninsertNode(element: any, parent: any): void { addClass(element, this._hostClassName); }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\ndrainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const /** @type {?} */ instructions: QueueInstruction[] = [];\n    this._queue.forEach(entry => {\n      const /** @type {?} */ player = entry.player;\n      if (player.destroyed) return;\n\n      const /** @type {?} */ element = entry.element;\n      const /** @type {?} */ listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const /** @type {?} */ baseEvent = makeAnimationEvent(\n                element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            ( /** @type {?} */((baseEvent as any)))['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const /** @type {?} */ d0 = a.transition.ast.depCount;\n      const /** @type {?} */ d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n/**\n * @param {?} context\n * @return {?}\n */\ndestroy(context: any) {\n    this.players.forEach(p => p.destroy());\n    this._destroyInnerNodes(this.hostElement, context);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nelementContainsData(element: any): boolean {\n    let /** @type {?} */ containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData =\n        (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n}\n\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionNamespace.prototype.players;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._triggers;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._queue;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._elementListeners;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._hostClassName;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.id;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.hostElement;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._engine;\n}\n\n\nexport interface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\nexport class TransitionAnimationEngine {\npublic players: TransitionAnimationPlayer[] = [];\npublic newHostElements = new Map<any, AnimationTransitionNamespace>();\npublic playersByElement = new Map<any, TransitionAnimationPlayer[]>();\npublic playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\npublic statesByElement = new Map<any, {[triggerName: string]: StateValue}>();\npublic totalAnimations = 0;\npublic totalQueuedPlayers = 0;\nprivate _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\nprivate _namespaceList: AnimationTransitionNamespace[] = [];\nprivate _flushFns: (() => any)[] = [];\nprivate _whenQuietFns: (() => any)[] = [];\npublic namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\npublic collectedEnterElements: any[] = [];\npublic collectedLeaveElements: any[] = [];\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\n_onRemovalComplete(element: any, context: any) { this.onRemovalComplete(element, context); }\n/**\n * @param {?} driver\n * @param {?} _normalizer\n */\nconstructor(public driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @return {?}\n */\nget queuedPlayers(): TransitionAnimationPlayer[] {\n    const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\ncreateNamespace(namespaceId: string, hostElement: any) {\n    const /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (hostElement.parentNode) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is apart of the animation code, it\n      // may or may not be inserted by a parent node that is an of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n/**\n * @param {?} ns\n * @param {?} hostElement\n * @return {?}\n */\nprivate _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const /** @type {?} */ limit = this._namespaceList.length - 1;\n    if (limit >= 0) {\n      let /** @type {?} */ found = false;\n      for (let /** @type {?} */ i = limit; i >= 0; i--) {\n        const /** @type {?} */ nextNamespace = this._namespaceList[i];\n        if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n          this._namespaceList.splice(i + 1, 0, ns);\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        this._namespaceList.splice(0, 0, ns);\n      }\n    } else {\n      this._namespaceList.push(ns);\n    }\n\n    this.namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} name\n * @param {?} trigger\n * @return {?}\n */\nregisterTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n\n    const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n      const /** @type {?} */ index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _fetchNamespace(id: string) { return this._namespaceLookup[id]; }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\ntrigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      this._fetchNamespace(namespaceId).trigger(element, name, value);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\ninsertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      this._fetchNamespace(namespaceId).insertNode(element, parent);\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ncollectEnterElement(element: any) { this.collectedEnterElements.push(element); }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(namespaceId: string, element: any, context: any, doNotRecurse?: boolean): void {\n    if (!isElementNode(element)) {\n      this._onRemovalComplete(element, context);\n      return;\n    }\n\n    const /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n    if (ns) {\n      ns.removeNode(element, context, doNotRecurse);\n    } else {\n      this.markElementAsRemoved(namespaceId, element, false, context);\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?=} hasAnimation\n * @param {?=} context\n * @return {?}\n */\nmarkElementAsRemoved(namespaceId: string, element: any, hasAnimation?: boolean, context?: any) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context, hasAnimation,\n      removedBeforeQueried: false\n    };\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, name: string, phase: string,\n      callback: (event: any) => boolean): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n/**\n * @param {?} entry\n * @param {?} subTimelines\n * @return {?}\n */\nprivate _buildInstruction(entry: QueueInstruction, subTimelines: ElementInstructionMap) {\n    return entry.transition.build(\n        this.driver, entry.element, entry.fromState.value, entry.toState.value,\n        entry.toState.options, subTimelines);\n  }\n/**\n * @param {?} containerElement\n * @return {?}\n */\ndestroyInnerAnimations(containerElement: any) {\n    let /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => {\n      const /** @type {?} */ players = this.playersByElement.get(element);\n      if (players) {\n        players.forEach(player => {\n          // special case for when an element is set for destruction, but hasn't started.\n          // in this situation we want to delay the destruction until the flush occurs\n          // so that any event listeners attached to the player are triggered.\n          if (player.queued) {\n            player.markedForDestroy = true;\n          } else {\n            player.destroy();\n          }\n        });\n      }\n      const /** @type {?} */ stateMap = this.statesByElement.get(element);\n      if (stateMap) {\n        Object.keys(stateMap).forEach(triggerName => stateMap[triggerName] = DELETED_STATE_VALUE);\n      }\n    });\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    if (elements.length) {\n      elements.forEach(element => {\n        const /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n          players.forEach(player => player.finish());\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprocessLeaveNode(element: any) {\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1) {\n    let /** @type {?} */ players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this._namespaceList.length &&\n        (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      players = this._flushAnimations(microtaskId);\n    } else {\n      for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n        const /** @type {?} */ element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const /** @type {?} */ quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\nprivate _flushAnimations(microtaskId: number): TransitionAnimationPlayer[] {\n    const /** @type {?} */ subTimelines = new ElementInstructionMap();\n    const /** @type {?} */ skippedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const /** @type {?} */ queuedInstructions: QueuedTransition[] = [];\n    const /** @type {?} */ queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const /** @type {?} */ allPreStyleElements = new Map<any, Set<string>>();\n    const /** @type {?} */ allPostStyleElements = new Map<any, Set<string>>();\n\n    const /** @type {?} */ bodyNode = getBodyNode();\n    const /** @type {?} */ allEnterNodes: any[] = this.collectedEnterElements.length ?\n        this.collectedEnterElements.filter(createIsRootFilterFn(this.collectedEnterElements)) :\n        [];\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      addClass(allEnterNodes[i], ENTER_CLASSNAME);\n    }\n\n    const /** @type {?} */ allLeaveNodes: any[] = [];\n    const /** @type {?} */ leaveNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n      const /** @type {?} */ element = this.collectedLeaveElements[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n      if (details && details.setForRemoval) {\n        addClass(element, LEAVE_CLASSNAME);\n        allLeaveNodes.push(element);\n        if (!details.hasAnimation) {\n          leaveNodesWithoutAnimations.push(element);\n        }\n      }\n    }\n\n    for (let /** @type {?} */ i = this._namespaceList.length - 1; i >= 0; i--) {\n      const /** @type {?} */ ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const /** @type {?} */ player = entry.player;\n\n        const /** @type {?} */ element = entry.element;\n        if (!bodyNode || !this.driver.containsElement(bodyNode, element)) {\n          player.destroy();\n          return;\n        }\n\n        const /** @type {?} */ instruction = this._buildInstruction(entry, subTimelines);\n        if (!instruction) return;\n\n        // if a unmatched transition is queued to go then it SHOULD NOT render\n        // an animation and cancel the previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub trigger\n        // then it will instruct the timeline builder to not add a player delay, but\n        // instead stretch the first keyframe gap up until the animation starts. The\n        // reason this is important is to prevent extra initialization styles from being\n        // required by the user in the animation.\n        instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n\n        subTimelines.append(element, instruction.timelines);\n\n        const /** @type {?} */ tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach(\n            element => getOrSetAsInMap(queriedElements, element, []).push(player));\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          if (props.length) {\n            let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPreStyleElements.get(element)));\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set<string>());\n            }\n            props.forEach(prop => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPostStyleElements.get(element)));\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set<string>());\n          }\n          props.forEach(prop => setVal.add(prop));\n        });\n      });\n    }\n\n    // these can only be detected here since we have a map of all the elements\n    // that have animations attached to them...\n    const /** @type {?} */ enterNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      const /** @type {?} */ element = allEnterNodes[i];\n      if (!subTimelines.has(element)) {\n        enterNodesWithoutAnimations.push(element);\n      }\n    }\n\n    const /** @type {?} */ allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    let /** @type {?} */ sortedParentElements: any[] = [];\n    queuedInstructions.forEach(entry => {\n      const /** @type {?} */ element = entry.element;\n      if (subTimelines.has(element)) {\n        sortedParentElements.unshift(element);\n        this._beforeAnimationBuild(\n            entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n\n    skippedPlayers.forEach(player => {\n      const /** @type {?} */ element = player.element;\n      const /** @type {?} */ previousPlayers =\n          this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(\n          prevPlayer => { getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer); });\n    });\n\n    allPreviousPlayersMap.forEach(players => players.forEach(player => player.destroy()));\n\n    // PRE STAGE: fill the ! styles\n    const /** @type {?} */ preStylesMap = allPreStyleElements.size ?\n        cloakAndComputeStyles(\n            this.driver, enterNodesWithoutAnimations, allPreStyleElements, PRE_STYLE) :\n        new Map<any, ɵStyleData>();\n\n    // POST STAGE: fill the * styles\n    const /** @type {?} */ postStylesMap = cloakAndComputeStyles(\n        this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n\n    const /** @type {?} */ rootPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ subPlayers: TransitionAnimationPlayer[] = [];\n    queuedInstructions.forEach(entry => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        const /** @type {?} */ innerPlayer = this._buildAnimation(\n            player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap,\n            postStylesMap);\n        player.setRealPlayer(innerPlayer);\n\n        let /** @type {?} */ parentHasPriority: any = null;\n        for (let /** @type {?} */ i = 0; i < sortedParentElements.length; i++) {\n          const /** @type {?} */ parent = sortedParentElements[i];\n          if (parent === element) break;\n          if (this.driver.containsElement(parent, element)) {\n            parentHasPriority = parent;\n            break;\n          }\n        }\n\n        if (parentHasPriority) {\n          const /** @type {?} */ parentPlayers = this.playersByElement.get(parentHasPriority);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        } else {\n          rootPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        subPlayers.push(player);\n      }\n    });\n\n    subPlayers.forEach(player => {\n      const /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.parentPlayer.onDestroy(() => player.destroy());\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let /** @type {?} */ i = 0; i < allLeaveNodes.length; i++) {\n      const /** @type {?} */ element = allLeaveNodes[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let /** @type {?} */ players: AnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n          let /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      if (players.length) {\n        removeNodesAfterAnimationDone(this, element, players);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const /** @type {?} */ index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    allEnterNodes.forEach(element => removeClass(element, ENTER_CLASSNAME));\n\n    return rootPlayers;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @return {?}\n */\nelementContainsData(namespaceId: string, element: any) {\n    let /** @type {?} */ containsData = false;\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlush(callback: () => any) { this._flushFns.push(callback); }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlushAnimationsDone(callback: () => any) { this._whenQuietFns.push(callback); }\n/**\n * @param {?} element\n * @param {?} isQueriedElement\n * @param {?=} namespaceId\n * @param {?=} triggerName\n * @param {?=} toStateValue\n * @return {?}\n */\nprivate _getPreviousPlayers(\n      element: string, isQueriedElement: boolean, namespaceId?: string, triggerName?: string,\n      toStateValue?: any): TransitionAnimationPlayer[] {\n    let /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const /** @type {?} */ isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @return {?}\n */\nprivate _beforeAnimationBuild(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>) {\n    // it's important to do this step before destroying the players\n    // so that the onDone callback below won't fire before this\n    eraseStyles(instruction.element, instruction.fromStyles);\n\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const /** @type {?} */ targetNameSpaceId: string|undefined =\n        instruction.isRemovalTransition ? undefined : namespaceId;\n    const /** @type {?} */ targetTriggerName: string|undefined =\n        instruction.isRemovalTransition ? undefined : triggerName;\n\n    instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      const /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n      const /** @type {?} */ previousPlayers = this._getPreviousPlayers(\n          element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const /** @type {?} */ realPlayer = /** @type {?} */(( player.getRealPlayer() as any));\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        players.push(player);\n      });\n    });\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @param {?} skippedPlayersMap\n * @param {?} preStylesMap\n * @param {?} postStylesMap\n * @return {?}\n */\nprivate _buildAnimation(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n      skippedPlayersMap: Map<any, AnimationPlayer[]>, preStylesMap: Map<any, ɵStyleData>,\n      postStylesMap: Map<any, ɵStyleData>): AnimationPlayer {\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const /** @type {?} */ allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ allConsumedElements = new Set<any>();\n    const /** @type {?} */ allSubElements = new Set<any>();\n    const /** @type {?} */ allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const /** @type {?} */ details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer();\n\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      let /** @type {?} */ previousPlayers: AnimationPlayer[] = EMPTY_PLAYER_ARRAY;\n      if (!allConsumedElements.has(element)) {\n        allConsumedElements.add(element);\n        const /** @type {?} */ _previousPlayers = allPreviousPlayersMap.get(element);\n        if (_previousPlayers) {\n          previousPlayers = _previousPlayers.map(p => p.getRealPlayer());\n        }\n      }\n      const /** @type {?} */ preStyles = preStylesMap.get(element);\n      const /** @type {?} */ postStyles = postStylesMap.get(element);\n      const /** @type {?} */ keyframes = normalizeKeyframes(\n          this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles,\n          postStyles);\n      const /** @type {?} */ player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach(player => {\n      getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(\n        element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n\n    return player;\n  }\n/**\n * @param {?} instruction\n * @param {?} keyframes\n * @param {?} previousPlayers\n * @return {?}\n */\nprivate _buildPlayer(\n      instruction: AnimationTimelineInstruction, keyframes: ɵStyleData[],\n      previousPlayers: AnimationPlayer[]): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n          instruction.element, keyframes, instruction.duration, instruction.delay,\n          instruction.easing, previousPlayers);\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer();\n  }\n}\n\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationEngine.prototype.players;\n/** @type {?} */\nTransitionAnimationEngine.prototype.newHostElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByQueriedElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.statesByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalAnimations;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalQueuedPlayers;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceLookup;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceList;\n/** @type {?} */\nTransitionAnimationEngine.prototype._flushFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype._whenQuietFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype.namespacesByHostElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedEnterElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedLeaveElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.onRemovalComplete;\n/** @type {?} */\nTransitionAnimationEngine.prototype.driver;\n/** @type {?} */\nTransitionAnimationEngine.prototype._normalizer;\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\nprivate _player: AnimationPlayer = new NoopAnimationPlayer();\nprivate _containsRealPlayer = false;\nprivate _queuedCallbacks: {[name: string]: (() => any)[]} = {};\nprivate _destroyed = false;\npublic parentPlayer: AnimationPlayer;\npublic markedForDestroy: boolean = false;\n/**\n * @param {?} namespaceId\n * @param {?} triggerName\n * @param {?} element\n */\nconstructor(public namespaceId: string,\npublic triggerName: string,\npublic element: any) {}\n/**\n * @return {?}\n */\nget queued() { return this._containsRealPlayer == false; }\n/**\n * @return {?}\n */\nget destroyed() { return this._destroyed; }\n/**\n * @param {?} player\n * @return {?}\n */\nsetRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    Object.keys(this._queuedCallbacks).forEach(phase => {\n      this._queuedCallbacks[phase].forEach(\n          callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks = {};\n    this._containsRealPlayer = true;\n  }\n/**\n * @return {?}\n */\ngetRealPlayer() { return this._player; }\n/**\n * @param {?} name\n * @param {?} callback\n * @return {?}\n */\nprivate _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n/**\n * @return {?}\n */\ninit(): void { this._player.init(); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this.queued ? false : this._player.hasStarted(); }\n/**\n * @return {?}\n */\nplay(): void { !this.queued && this._player.play(); }\n/**\n * @return {?}\n */\npause(): void { !this.queued && this._player.pause(); }\n/**\n * @return {?}\n */\nrestart(): void { !this.queued && this._player.restart(); }\n/**\n * @return {?}\n */\nfinish(): void { this._player.finish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._destroyed = true;\n    this._player.destroy();\n  }\n/**\n * @return {?}\n */\nreset(): void { !this.queued && this._player.reset(); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: any): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n/**\n * @return {?}\n */\ngetPosition(): number { return this.queued ? 0 : this._player.getPosition(); }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._player.totalTime; }\n}\n\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationPlayer.prototype._player;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._containsRealPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._queuedCallbacks;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.markedForDestroy;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.namespaceId;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.triggerName;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.element;\n}\n\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map: Map<any, any[]>| {[key: string]: any}, key: any, value: any) {\n  let /** @type {?} */ currentValues: any[]|null|undefined;\n  if (map instanceof Map) {\n    currentValues = map.get(key);\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        map.delete(key);\n      }\n    }\n  } else {\n    currentValues = map[key];\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        delete map[key];\n      }\n    }\n  }\n  return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value: any): string {\n  switch (typeof value) {\n    case 'boolean':\n      return value ? '1' : '0';\n    default:\n      return value != null ? value.toString() : null;\n  }\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element: any, value?: string) {\n  const /** @type {?} */ oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n/**\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(\n    driver: AnimationDriver, elements: any[], elementPropsMap: Map<any, Set<string>>,\n    defaultStyle: string): Map<any, ɵStyleData> {\n  const /** @type {?} */ cloakVals = elements.map(element => cloakElement(element));\n  const /** @type {?} */ valuesMap = new Map<any, ɵStyleData>();\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const /** @type {?} */ styles: ɵStyleData = {};\n    props.forEach(prop => {\n      const /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  elements.forEach((element, i) => cloakElement(element, cloakVals[i]));\n  return valuesMap;\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction createIsRootFilterFn(nodes: any): (node: any) => boolean {\n  const /** @type {?} */ nodeSet = new Set(nodes);\n  const /** @type {?} */ knownRootContainer = new Set();\n  let /** @type {?} */ isRoot: (node: any) => boolean;\n  isRoot = node => {\n    if (!node) return true;\n    if (nodeSet.has(node.parentNode)) return false;\n    if (knownRootContainer.has(node.parentNode)) return true;\n    if (isRoot(node.parentNode)) {\n      knownRootContainer.add(node);\n      return true;\n    }\n    return false;\n  };\n  return isRoot;\n}\n\nconst /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element: any, className: string): boolean {\n  if (element.classList) {\n    return element.classList.contains(className);\n  } else {\n    const /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n    return classes && classes[className];\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (!classes) {\n      classes = element[CLASSES_CACHE_KEY] = {};\n    }\n    classes[className] = true;\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (classes) {\n      delete classes[className];\n    }\n  }\n}\n/**\n * @return {?}\n */\nfunction getBodyNode(): any|null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(\n    engine: TransitionAnimationEngine, element: any, players: AnimationPlayer[]) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationMetadata, AnimationOptions, AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nexport class TimelineAnimationEngine {\nprivate _animations: {[id: string]: Ast} = {};\nprivate _playersById: {[id: string]: AnimationPlayer} = {};\npublic players: AnimationPlayer[] = [];\n/**\n * @param {?} _driver\n * @param {?} _normalizer\n */\nconstructor(private _driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @param {?} id\n * @param {?} metadata\n * @return {?}\n */\nregister(id: string, metadata: AnimationMetadata|AnimationMetadata[]) {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = buildAnimationAst(metadata, errors);\n    if (errors.length) {\n      throw new Error(\n          `Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    } else {\n      this._animations[id] = ast;\n    }\n  }\n/**\n * @param {?} i\n * @param {?} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nprivate _buildPlayer(\n      i: AnimationTimelineInstruction, preStyles: ɵStyleData,\n      postStyles?: ɵStyleData): AnimationPlayer {\n    const /** @type {?} */ element = i.element;\n    const /** @type {?} */ keyframes = normalizeKeyframes(\n        this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = this._animations[id];\n    let /** @type {?} */ instructions: AnimationTimelineInstruction[];\n\n    const /** @type {?} */ autoStylesMap = new Map<any, ɵStyleData>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n          this._driver, element, ast, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n        inst.postStyleProps.forEach(prop => styles[prop] = null);\n      });\n    } else {\n      errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw new Error(\n          `Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      Object.keys(styles).forEach(\n          prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n    });\n\n    const /** @type {?} */ players = instructions.map(i => {\n      const /** @type {?} */ styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, {}, styles);\n    });\n    const /** @type {?} */ player = optimizeGroupPlayer(players);\n    this._playersById[id] = player;\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\ndestroy(id: string) {\n    const /** @type {?} */ player = this._getPlayer(id);\n    player.destroy();\n    delete this._playersById[id];\n    const /** @type {?} */ index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _getPlayer(id: string): AnimationPlayer {\n    const /** @type {?} */ player = this._playersById[id];\n    if (!player) {\n      throw new Error(`Unable to find the timeline player referenced by ${id}`);\n    }\n    return player;\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(id: string, element: string, eventName: string, callback: (event: any) => any):\n      () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\ncommand(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, /** @type {?} */(( args[0] as AnimationMetadata | AnimationMetadata[])));\n      return;\n    }\n\n    if (command == 'create') {\n      const /** @type {?} */ options = /** @type {?} */(( (args[0] || {}) as AnimationOptions));\n      this.create(id, element, options);\n      return;\n    }\n\n    const /** @type {?} */ player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat( /** @type {?} */((args[0] as string))));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineAnimationEngine.prototype._animations;\n/** @type {?} */\nTimelineAnimationEngine.prototype._playersById;\n/** @type {?} */\nTimelineAnimationEngine.prototype.players;\n/** @type {?} */\nTimelineAnimationEngine.prototype._driver;\n/** @type {?} */\nTimelineAnimationEngine.prototype._normalizer;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\n\nimport {copyStyles} from '../util';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTransitionFactory} from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name: string, ast: TriggerAst): AnimationTrigger {\n  return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class AnimationTrigger {\npublic transitionFactories: AnimationTransitionFactory[] = [];\npublic fallbackTransition: AnimationTransitionFactory;\npublic states: {[stateName: string]: ɵStyleData} = {};\n/**\n * @param {?} name\n * @param {?} ast\n */\nconstructor(public name: string,\npublic ast: TriggerAst) {\n    ast.states.forEach(ast => {\n      const obj = this.states[ast.name] = {};\n      ast.style.styles.forEach(styleTuple => {\n        if (typeof styleTuple == 'object') {\n          copyStyles(styleTuple as ɵStyleData, false, obj);\n        }\n      });\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n/**\n * @return {?}\n */\nget containsQueries() { return this.ast.queryCount > 0; }\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatchTransition(currentState: any, nextState: any): AnimationTransitionFactory|null {\n    const /** @type {?} */ entry = this.transitionFactories.find(f => f.match(currentState, nextState));\n    return entry || null;\n  }\n}\n\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTrigger.prototype.transitionFactories;\n/** @type {?} */\nAnimationTrigger.prototype.fallbackTransition;\n/** @type {?} */\nAnimationTrigger.prototype.states;\n/** @type {?} */\nAnimationTrigger.prototype.name;\n/** @type {?} */\nAnimationTrigger.prototype.ast;\n}\n\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(\n    triggerName: string, states: {[stateName: string]: ɵStyleData}): AnimationTransitionFactory {\n  const /** @type {?} */ matchers = [(fromState: any, toState: any) => true];\n  const /** @type {?} */ animation = new SequenceAst([]);\n  const /** @type {?} */ transition = new TransitionAst(matchers, animation);\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj: {[key: string]: any}, key1: string, key2: string) {\n  if (obj.hasOwnProperty(key1)) {\n    if (!obj.hasOwnProperty(key2)) {\n      obj[key2] = obj[key1];\n    }\n  } else if (obj.hasOwnProperty(key2)) {\n    obj[key1] = obj[key2];\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {iteratorToArray, mergeAnimationOptions} from '../util';\n\nimport {TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {AnimationTransitionInstruction, createTransitionInstruction} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class AnimationTransitionFactory {\n/**\n * @param {?} _triggerName\n * @param {?} ast\n * @param {?} _stateStyles\n */\nconstructor(\nprivate _triggerName: string,\npublic ast: TransitionAst,\nprivate _stateStyles: {[stateName: string]: ɵStyleData}) {}\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatch(currentState: any, nextState: any): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n  }\n/**\n * @param {?} driver\n * @param {?} element\n * @param {?} currentState\n * @param {?} nextState\n * @param {?=} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuild(\n      driver: AnimationDriver, element: any, currentState: any, nextState: any,\n      options?: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTransitionInstruction|undefined {\n    const /** @type {?} */ animationOptions = mergeAnimationOptions(this.ast.options || {}, options || {});\n\n    const /** @type {?} */ backupStateStyles = this._stateStyles['*'] || {};\n    const /** @type {?} */ currentStateStyles = this._stateStyles[currentState] || backupStateStyles;\n    const /** @type {?} */ nextStateStyles = this._stateStyles[nextState] || backupStateStyles;\n\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ timelines = buildAnimationTimelines(\n        driver, element, this.ast.animation, currentStateStyles, nextStateStyles, animationOptions,\n        subInstructions, errors);\n\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n\n    const /** @type {?} */ preStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ postStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ queriedElements = new Set<any>();\n    timelines.forEach(tl => {\n      const /** @type {?} */ elm = tl.element;\n      const /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n      tl.preStyleProps.forEach(prop => preProps[prop] = true);\n\n      const /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n      tl.postStyleProps.forEach(prop => postProps[prop] = true);\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    const /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(\n        element, this._triggerName, currentState, nextState, nextState === 'void',\n        currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap,\n        postStyleMap);\n  }\n}\n\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionFactory.prototype._triggerName;\n/** @type {?} */\nAnimationTransitionFactory.prototype.ast;\n/** @type {?} */\nAnimationTransitionFactory.prototype._stateStyles;\n}\n\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(\n    matchFns: TransitionMatcherFn[], currentState: any, nextState: any): boolean {\n  return matchFns.some(fn => fn(currentState, nextState));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleData;\n  toState: string;\n  toStyles: ɵStyleData;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, {[prop: string]: boolean}>;\n  postStyleProps: Map<any, {[prop: string]: boolean}>;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @return {?}\n */\nexport function createTransitionInstruction(\n    element: any, triggerName: string, fromState: string, toState: string,\n    isRemovalTransition: boolean, fromStyles: ɵStyleData, toStyles: ɵStyleData,\n    timelines: AnimationTimelineInstruction[], queriedElements: any[],\n    preStyleProps: Map<any, {[prop: string]: boolean}>,\n    postStyleProps: Map<any, {[prop: string]: boolean}>): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n/**\n * @param {?} propertyName\n * @param {?} errors\n * @return {?}\n */\nnormalizePropertyName(propertyName: string, errors: string[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n/**\n * @param {?} userProvidedProperty\n * @param {?} normalizedProperty\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nnormalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    let /** @type {?} */ unit: string = '';\n    const /** @type {?} */ strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n\nconst /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap(\n    'width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys: string[]): {[key: string]: boolean} {\n  const /** @type {?} */ map: {[key: string]: boolean} = {};\n  keys.forEach(key => map[key] = true);\n  return map;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @experimental Animation support is experimental.\n */\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: string[]): string;\n  abstract normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string { return propertyName; }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {normalizeStyles} from '../util';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class Animation {\nprivate _animationAst: Ast;\n/**\n * @param {?} _driver\n * @param {?} input\n */\nconstructor(private _driver: AnimationDriver, input: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(input, errors);\n    if (errors.length) {\n      const errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    this._animationAst = ast;\n  }\n/**\n * @param {?} element\n * @param {?} startingStyles\n * @param {?} destinationStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuildTimelines(\n      element: any, startingStyles: ɵStyleData|ɵStyleData[],\n      destinationStyles: ɵStyleData|ɵStyleData[], options: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[] {\n    const /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */((\n                                                  <ɵStyleData>startingStyles));\n    const /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */((\n                                                    <ɵStyleData>destinationStyles));\n    const /** @type {?} */ errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ result = buildAnimationTimelines(\n        this._driver, element, this._animationAst, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    return result;\n  }\n}\n\nfunction Animation_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimation.prototype._animationAst;\n/** @type {?} */\nAnimation.prototype._driver;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationOptions, AnimationQueryOptions, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, AstVisitor, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTimelineInstruction, createTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(\n    driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData = {},\n    finalStyles: ɵStyleData = {}, options: AnimationOptions,\n    subInstructions?: ElementInstructionMap, errors: any[] = []): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n      driver, rootElement, ast, startingStyles, finalStyles, options, subInstructions, errors);\n}\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} startingStyles\n * @param {?} finalStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nbuildKeyframes(\n      driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData,\n      finalStyles: ɵStyleData, options: AnimationOptions, subInstructions?: ElementInstructionMap,\n      errors: any[] = []): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, errors, []);\n    context.options = options;\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    ast.visit(this, context);\n\n    // this checks to see if an actual animation happened\n    const /** @type {?} */ timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    if (timelines.length && Object.keys(finalStyles).length) {\n      const /** @type {?} */ tl = timelines[timelines.length - 1];\n      if (!tl.allowOnlyTimelineStyles()) {\n        tl.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n                              [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n    if (elementInstructions) {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n      const /** @type {?} */ endTime = this._visitSubInstructions(\n          elementInstructions, innerContext, /** @type {?} */(( innerContext.options as AnimateChildOptions)));\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} instructions\n * @param {?} context\n * @param {?} options\n * @return {?}\n */\nprivate _visitSubInstructions(\n      instructions: AnimationTimelineInstruction[], context: AnimationTimelineContext,\n      options: AnimateChildOptions): number {\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    let /** @type {?} */ furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const /** @type {?} */ instructionTimings =\n            context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime =\n            Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ subContextCount = context.subContextCount;\n    let /** @type {?} */ ctx = context;\n    const /** @type {?} */ options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode instanceof StyleAst) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const /** @type {?} */ delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => s.visit(this, ctx));\n\n      // this is here just incase the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ innerTimelines: TimelineBuilder[] = [];\n    let /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach(s => {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      s.visit(this, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(\n        timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if (ast instanceof DynamicTimingAst) {\n      const /** @type {?} */ strValue = context.params ?\n          interpolateParams(ast.value, context.params, context.errors) :\n          ast.value.toString();\n      return resolveTiming(strValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timings = context.currentAnimateTimings = this.visitTiming(ast.timings, context);\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const /** @type {?} */ style = ast.style;\n    if (style instanceof KeyframesAst) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle( /** @type {?} */((style as StyleAst)), context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timeline = context.currentTimeline;\n    const /** @type {?} */ timings = /** @type {?} */(( context.currentAnimateTimings));\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.getCurrentStyleProperties().length) {\n      timeline.forwardFrame();\n    }\n\n    const /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ startTime = ( /** @type {?} */((context.currentTimeline))).duration;\n    const /** @type {?} */ duration = currentAnimateTimings.duration;\n    const /** @type {?} */ innerContext = context.createSubContext();\n    const /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach(step => {\n      const /** @type {?} */ offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ options = /** @type {?} */(( (ast.options || {}) as AnimationQueryOptions));\n    const /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode instanceof StyleAst ||\n                  (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let /** @type {?} */ furthestTime = startTime;\n    const /** @type {?} */ elms = context.invokeQuery(\n        ast.selector, ast.originalSelector, ast.limit, ast.includeSelf,\n        options.optional ? true : false, context.errors);\n\n    context.currentQueryTotal = elms.length;\n    let /** @type {?} */ sameElementTimeline: TimelineBuilder|null = null;\n    elms.forEach((element, i) => {\n\n      context.currentQueryIndex = i;\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      ast.animation.visit(this, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ parentContext = /** @type {?} */(( context.parentContext));\n    const /** @type {?} */ tl = context.currentTimeline;\n    const /** @type {?} */ timings = ast.timings;\n    const /** @type {?} */ duration = Math.abs(timings.duration);\n    const /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n    let /** @type {?} */ delay = duration * context.currentQueryIndex;\n\n    let /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const /** @type {?} */ startingTime = timeline.currentTime;\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n        (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number; value: string | number;\n};\n\nconst /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */(( <Ast>{}));\nexport class AnimationTimelineContext {\npublic parentContext: AnimationTimelineContext|null = null;\npublic currentTimeline: TimelineBuilder;\npublic currentAnimateTimings: AnimateTimings|null = null;\npublic previousNode: Ast = DEFAULT_NOOP_PREVIOUS_NODE;\npublic subContextCount = 0;\npublic options: AnimationOptions = {};\npublic currentQueryIndex: number = 0;\npublic currentQueryTotal: number = 0;\npublic currentStaggerTime: number = 0;\n/**\n * @param {?} _driver\n * @param {?} element\n * @param {?} subInstructions\n * @param {?} errors\n * @param {?} timelines\n * @param {?=} initialTimeline\n */\nconstructor(\nprivate _driver: AnimationDriver,\npublic element: any,\npublic subInstructions: ElementInstructionMap,\npublic errors: any[],\npublic timelines: TimelineBuilder[], initialTimeline?: TimelineBuilder) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(element, 0);\n    timelines.push(this.currentTimeline);\n  }\n/**\n * @return {?}\n */\nget params() { return this.options.params; }\n/**\n * @param {?} options\n * @param {?=} skipIfExists\n * @return {?}\n */\nupdateOptions(options: AnimationOptions|null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const /** @type {?} */ newOptions = /** @type {?} */(( options as any));\n    let /** @type {?} */ optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      ( /** @type {?} */((optionsToUpdate as any))).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const /** @type {?} */ newParams = newOptions.params;\n    if (newParams) {\n      let /** @type {?} */ paramsToUpdate: {[name: string]: any} = /** @type {?} */(( optionsToUpdate.params));\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nprivate _copyOptions() {\n    const /** @type {?} */ options: AnimationOptions = {};\n    if (this.options) {\n      const /** @type {?} */ oldParams = this.options.params;\n      if (oldParams) {\n        const /** @type {?} */ params: {[name: string]: any} = options['params'] = {};\n        Object.keys(this.options.params).forEach(name => { params[name] = oldParams[name]; });\n      }\n    }\n    return options;\n  }\n/**\n * @param {?=} options\n * @param {?=} element\n * @param {?=} newTime\n * @return {?}\n */\ncreateSubContext(options: AnimationOptions|null = null, element?: any, newTime?: number):\n      AnimationTimelineContext {\n    const /** @type {?} */ target = element || this.element;\n    const /** @type {?} */ context = new AnimationTimelineContext(\n        this._driver, target, this.subInstructions, this.errors, this.timelines,\n        this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n/**\n * @param {?=} newTime\n * @return {?}\n */\ntransformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n/**\n * @param {?} instruction\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nappendInstructionToTimeline(\n      instruction: AnimationTimelineInstruction, duration: number|null,\n      delay: number|null): AnimateTimings {\n    const /** @type {?} */ updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const /** @type {?} */ builder = new SubTimelineBuilder(\n        instruction.element, instruction.keyframes, instruction.preStyleProps,\n        instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nincrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n/**\n * @param {?} selector\n * @param {?} originalSelector\n * @param {?} limit\n * @param {?} includeSelf\n * @param {?} optional\n * @param {?} errors\n * @return {?}\n */\ninvokeQuery(\n      selector: string, originalSelector: string, limit: number, includeSelf: boolean,\n      optional: boolean, errors: any[]): any[] {\n    let /** @type {?} */ results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {  // if :self is only used then the selector is empty\n      const /** @type {?} */ multi = limit != 1;\n      results.push(...this._driver.query(this.element, selector, multi));\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(\n          `\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n    }\n    return results;\n  }\n}\n\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTimelineContext.prototype.parentContext;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentTimeline;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationTimelineContext.prototype.previousNode;\n/** @type {?} */\nAnimationTimelineContext.prototype.subContextCount;\n/** @type {?} */\nAnimationTimelineContext.prototype.options;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryIndex;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryTotal;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentStaggerTime;\n/** @type {?} */\nAnimationTimelineContext.prototype._driver;\n/** @type {?} */\nAnimationTimelineContext.prototype.element;\n/** @type {?} */\nAnimationTimelineContext.prototype.subInstructions;\n/** @type {?} */\nAnimationTimelineContext.prototype.errors;\n/** @type {?} */\nAnimationTimelineContext.prototype.timelines;\n}\n\nexport class TimelineBuilder {\npublic duration: number = 0;\npublic easing: string|null;\nprivate _previousKeyframe: ɵStyleData = {};\nprivate _currentKeyframe: ɵStyleData = {};\nprivate _keyframes = new Map<number, ɵStyleData>();\nprivate _styleSummary: {[prop: string]: StyleAtTime} = {};\nprivate _localTimelineStyles: ɵStyleData;\nprivate _globalTimelineStyles: ɵStyleData;\nprivate _pendingStyles: ɵStyleData = {};\nprivate _backFill: ɵStyleData = {};\nprivate _currentEmptyStepKeyframe: ɵStyleData|null = null;\n/**\n * @param {?} element\n * @param {?} startTime\n * @param {?=} _elementTimelineStylesLookup\n */\nconstructor(\npublic element: any,\npublic startTime: number,\nprivate _elementTimelineStylesLookup?: Map<any, ɵStyleData>) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleData>();\n    }\n\n    this._localTimelineStyles = Object.create(this._backFill, {});\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element) !;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.getCurrentStyleProperties().length > 0;\n      default:\n        return true;\n    }\n  }\n/**\n * @return {?}\n */\ngetCurrentStyleProperties(): string[] { return Object.keys(this._currentKeyframe); }\n/**\n * @return {?}\n */\nget currentTime() { return this.startTime + this.duration; }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n/**\n * @param {?} element\n * @param {?=} currentTime\n * @return {?}\n */\nfork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n        element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n/**\n * @return {?}\n */\nprivate _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = /** @type {?} */(( this._keyframes.get(this.duration)));\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = Object.create(this._backFill, {});\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n/**\n * @return {?}\n */\nforwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nforwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} prop\n * @param {?} value\n * @return {?}\n */\nprivate _updateStyle(prop: string, value: string|number) {\n    this._localTimelineStyles[prop] = value;\n    this._globalTimelineStyles[prop] = value;\n    this._styleSummary[prop] = {time: this.currentTime, value};\n  }\n/**\n * @return {?}\n */\nallowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n/**\n * @param {?} easing\n * @return {?}\n */\napplyEmptyStep(easing: string|null) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    Object.keys(this._globalTimelineStyles).forEach(prop => {\n      this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n      this._currentKeyframe[prop] = AUTO_STYLE;\n    });\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n/**\n * @param {?} input\n * @param {?} easing\n * @param {?} errors\n * @param {?=} options\n * @return {?}\n */\nsetStyles(\n      input: (ɵStyleData|string)[], easing: string|null, errors: any[],\n      options?: AnimationOptions) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    const /** @type {?} */ params = (options && options.params) || {};\n    const /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n      this._pendingStyles[prop] = val;\n      if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n        this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n            this._globalTimelineStyles[prop] :\n            AUTO_STYLE;\n      }\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\napplyStylesToKeyframe() {\n    const /** @type {?} */ styles = this._pendingStyles;\n    const /** @type {?} */ props = Object.keys(styles);\n    if (props.length == 0) return;\n\n    this._pendingStyles = {};\n\n    props.forEach(prop => {\n      const /** @type {?} */ val = styles[prop];\n      this._currentKeyframe[prop] = val;\n    });\n\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      if (!this._currentKeyframe.hasOwnProperty(prop)) {\n        this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n      }\n    });\n  }\n/**\n * @return {?}\n */\nsnapshotCurrentStyles() {\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      const /** @type {?} */ val = this._localTimelineStyles[prop];\n      this._pendingStyles[prop] = val;\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\ngetFinalKeyframe() { return this._keyframes.get(this.duration); }\n/**\n * @return {?}\n */\nget properties() {\n    const /** @type {?} */ properties: string[] = [];\n    for (let /** @type {?} */ prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n/**\n * @param {?} timeline\n * @return {?}\n */\nmergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    Object.keys(timeline._styleSummary).forEach(prop => {\n      const /** @type {?} */ details0 = this._styleSummary[prop];\n      const /** @type {?} */ details1 = timeline._styleSummary[prop];\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const /** @type {?} */ preStyleProps = new Set<string>();\n    const /** @type {?} */ postStyleProps = new Set<string>();\n    const /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let /** @type {?} */ finalKeyframes: ɵStyleData[] = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n      Object.keys(finalKeyframe).forEach(prop => {\n        const /** @type {?} */ value = finalKeyframe[prop];\n        if (value == PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value == AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe['offset'] = time / this.duration;\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const /** @type {?} */ preProps: string[] = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const /** @type {?} */ postProps: string[] = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const /** @type {?} */ kf0 = finalKeyframes[0];\n      const /** @type {?} */ kf1 = copyObj(kf0);\n      kf0['offset'] = 0;\n      kf1['offset'] = 1;\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n        this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime,\n        this.easing, false);\n  }\n}\n\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineBuilder.prototype.duration;\n/** @type {?} */\nTimelineBuilder.prototype.easing;\n/** @type {?} */\nTimelineBuilder.prototype._previousKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._currentKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._keyframes;\n/** @type {?} */\nTimelineBuilder.prototype._styleSummary;\n/** @type {?} */\nTimelineBuilder.prototype._localTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._globalTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._pendingStyles;\n/** @type {?} */\nTimelineBuilder.prototype._backFill;\n/** @type {?} */\nTimelineBuilder.prototype._currentEmptyStepKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype.element;\n/** @type {?} */\nTimelineBuilder.prototype.startTime;\n/** @type {?} */\nTimelineBuilder.prototype._elementTimelineStylesLookup;\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\npublic timings: AnimateTimings;\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} timings\n * @param {?=} _stretchStartingKeyframe\n */\nconstructor(\npublic element: any,\npublic keyframes: ɵStyleData[],\npublic preStyleProps: string[],\npublic postStyleProps: string[], timings: AnimateTimings,\nprivate _stretchStartingKeyframe: boolean = false) {\n    super(element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean { return this.keyframes.length > 1; }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    let /** @type {?} */ keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const /** @type {?} */ newKeyframes: ɵStyleData[] = [];\n      const /** @type {?} */ totalTime = duration + delay;\n      const /** @type {?} */ startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n      newFirstKeyframe['offset'] = 0;\n      newKeyframes.push(newFirstKeyframe);\n\n      const /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n      oldFirstKeyframe['offset'] = roundOffset(startingGap);\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still renderered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const /** @type {?} */ limit = keyframes.length - 1;\n      for (let /** @type {?} */ i = 1; i <= limit; i++) {\n        let /** @type {?} */ kf = copyStyles(keyframes[i], false);\n        const /** @type {?} */ oldOffset = /** @type {?} */(( kf['offset'] as number));\n        const /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n        kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n        this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing,\n        true);\n  }\n}\n\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nSubTimelineBuilder.prototype.timings;\n/** @type {?} */\nSubTimelineBuilder.prototype.element;\n/** @type {?} */\nSubTimelineBuilder.prototype.keyframes;\n/** @type {?} */\nSubTimelineBuilder.prototype.preStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype.postStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input: (ɵStyleData | string)[], allStyles: ɵStyleData) {\n  const /** @type {?} */ styles: ɵStyleData = {};\n  let /** @type {?} */ allProperties: string[];\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || Object.keys(allStyles);\n      allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n    } else {\n      copyStyles( /** @type {?} */((token as ɵStyleData)), false, styles);\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nexport class ElementInstructionMap {\nprivate _map = new Map<any, AnimationTimelineInstruction[]>();\n/**\n * @param {?} element\n * @return {?}\n */\nconsume(element: any): AnimationTimelineInstruction[] {\n    let /** @type {?} */ instructions = this._map.get(element);\n    if (instructions) {\n      this._map.delete(element);\n    } else {\n      instructions = [];\n    }\n    return instructions;\n  }\n/**\n * @param {?} element\n * @param {?} instructions\n * @return {?}\n */\nappend(element: any, instructions: AnimationTimelineInstruction[]) {\n    let /** @type {?} */ existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nhas(element: any): boolean { return this._map.has(element); }\n/**\n * @return {?}\n */\nclear() { this._map.clear(); }\n}\n\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n/** @type {?} */\nElementInstructionMap.prototype._map;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: ɵStyleData[];\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string|null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(\n    element: any, keyframes: ɵStyleData[], preStyleProps: string[], postStyleProps: string[],\n    duration: number, delay: number, easing: string | null = null,\n    subTimeline: boolean = false): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay, easing, subTimeline\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, style, ɵStyleData} from '@angular/animations';\n\nimport {getOrSetAsInMap} from '../render/shared';\nimport {ENTER_SELECTOR, LEAVE_SELECTOR, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, copyObj, normalizeAnimationEntry, resolveTiming, validateStyleParams} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationDslVisitor, visitAnimationNode} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst /** @type {?} */ SELF_TOKEN = ':self';\nconst /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(\n    metadata: AnimationMetadata | AnimationMetadata[], errors: any[]): Ast {\n  return new AnimationAstBuilderVisitor().build(metadata, errors);\n}\n\nconst /** @type {?} */ LEAVE_TOKEN = ':leave';\nconst /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\nconst /** @type {?} */ ENTER_TOKEN = ':enter';\nconst /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst /** @type {?} */ ROOT_SELECTOR = '';\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nbuild(metadata: AnimationMetadata|AnimationMetadata[], errors: any[]): Ast {\n    const /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    return /** @type {?} */(( visitAnimationNode(this, normalizeAnimationEntry(metadata), context) as Ast));\n  }\n/**\n * @param {?} context\n * @return {?}\n */\nprivate _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = {};\n    context.collectedStyles[ROOT_SELECTOR] = {};\n    context.currentTime = 0;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(metadata: AnimationTriggerMetadata, context: AnimationAstBuilderContext):\n      TriggerAst {\n    let /** @type {?} */ queryCount = context.queryCount = 0;\n    let /** @type {?} */ depCount = context.depCount = 0;\n    const /** @type {?} */ states: StateAst[] = [];\n    const /** @type {?} */ transitions: TransitionAst[] = [];\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const /** @type {?} */ stateDef = /** @type {?} */(( def as AnimationStateMetadata));\n        const /** @type {?} */ name = stateDef.name;\n        name.split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const /** @type {?} */ transition = this.visitTransition( /** @type {?} */((def as AnimationTransitionMetadata)), context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(\n            'only state() and transition() definitions can sit inside of a trigger()');\n      }\n    });\n    const /** @type {?} */ ast = new TriggerAst(metadata.name, states, transitions);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = queryCount;\n    ast.depCount = depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    return new StateAst(metadata.name, this.visitStyle(metadata.styles, context));\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTransition(metadata: AnimationTransitionMetadata, context: AnimationAstBuilderContext):\n      TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n    const /** @type {?} */ ast = new TransitionAst(matchers, entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = context.queryCount;\n    ast.depCount = context.depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitSequence(metadata: AnimationSequenceMetadata, context: AnimationAstBuilderContext):\n      SequenceAst {\n    const /** @type {?} */ ast = new SequenceAst(metadata.steps.map(s => visitAnimationNode(this, s, context)));\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const /** @type {?} */ currentTime = context.currentTime;\n    let /** @type {?} */ furthestTime = 0;\n    const /** @type {?} */ steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const /** @type {?} */ innerAst = visitAnimationNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    const /** @type {?} */ ast = new GroupAst(steps);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(metadata: AnimationAnimateMetadata, context: AnimationAstBuilderContext):\n      AnimateAst {\n    const /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n\n    let /** @type {?} */ styles: StyleAst|KeyframesAst;\n    let /** @type {?} */ styleMetadata: AnimationMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styles = this.visitKeyframes( /** @type {?} */((styleMetadata as AnimationKeyframesSequenceMetadata)), context);\n    } else {\n      let /** @type {?} */ styleMetadata = /** @type {?} */(( metadata.styles as AnimationStyleMetadata));\n      let /** @type {?} */ isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const /** @type {?} */ newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const /** @type {?} */ styleAst = this.visitStyle(styleMetadata, context);\n      styleAst.isEmptyStep = isEmpty;\n      styles = styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return new AnimateAst(timingAst, styles);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nprivate _makeStyleAst(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext):\n      StyleAst {\n    const /** @type {?} */ styles: (ɵStyleData | string)[] = [];\n    if (Array.isArray(metadata.styles)) {\n      ( /** @type {?} */((metadata.styles as(ɵStyleData | string)[]))).forEach(styleTuple => {\n        if (typeof styleTuple == 'string') {\n          if (styleTuple == AUTO_STYLE) {\n            styles.push( /** @type {?} */((styleTuple as string)));\n          } else {\n            context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n          }\n        } else {\n          styles.push( /** @type {?} */((styleTuple as ɵStyleData)));\n        }\n      })\n    } else {\n      styles.push(metadata.styles);\n    }\n\n    let /** @type {?} */ collectedEasing: string|null = null;\n    styles.forEach(styleData => {\n      if (isObject(styleData)) {\n        const /** @type {?} */ styleMap = /** @type {?} */(( styleData as ɵStyleData));\n        const /** @type {?} */ easing = styleMap['easing'];\n        if (easing) {\n          collectedEasing = /** @type {?} */(( easing as string));\n          delete styleMap['easing'];\n        }\n      }\n    });\n    return new StyleAst(styles, collectedEasing, metadata.offset);\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nprivate _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const /** @type {?} */ timings = context.currentAnimateTimings;\n    let /** @type {?} */ endTime = context.currentTime;\n    let /** @type {?} */ startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple == 'string') return;\n\n      Object.keys(tuple).forEach(prop => {\n        const /** @type {?} */ collectedStyles = context.collectedStyles[ /** @type {?} */((context.currentQuerySelector))];\n        const /** @type {?} */ collectedEntry = collectedStyles[prop];\n        let /** @type {?} */ updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime &&\n              endTime <= collectedEntry.endTime) {\n            context.errors.push(\n                `The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles[prop] = {startTime, endTime};\n        }\n\n        if (context.options) {\n          validateStyleParams(tuple[prop], context.options, context.errors);\n        }\n      });\n    });\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(metadata: AnimationKeyframesSequenceMetadata, context: AnimationAstBuilderContext):\n      KeyframesAst {\n    if (!context.currentAnimateTimings) {\n      context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n      return new KeyframesAst([]);\n    }\n\n    const /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n\n    let /** @type {?} */ totalKeyframesWithOffsets = 0;\n    const /** @type {?} */ offsets: number[] = [];\n    let /** @type {?} */ offsetsOutOfOrder = false;\n    let /** @type {?} */ keyframesOutOfRange = false;\n    let /** @type {?} */ previousOffset: number = 0;\n\n    const /** @type {?} */ keyframes: StyleAst[] = metadata.steps.map(styles => {\n      const /** @type {?} */ style = this._makeStyleAst(styles, context);\n      let /** @type {?} */ offsetVal: number|null =\n          style.offset != null ? style.offset : consumeOffset(style.styles);\n      let /** @type {?} */ offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(`Please ensure that all keyframe offsets are in order`);\n    }\n\n    const /** @type {?} */ length = metadata.steps.length;\n    let /** @type {?} */ generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const /** @type {?} */ limit = length - 1;\n    const /** @type {?} */ currentTime = context.currentTime;\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n      const /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n    });\n\n    return new KeyframesAst(keyframes);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitReference(metadata: AnimationReferenceMetadata, context: AnimationAstBuilderContext):\n      ReferenceAst {\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ ast = new ReferenceAst(entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(metadata: AnimationAnimateChildMetadata, context: AnimationAstBuilderContext):\n      AnimateChildAst {\n    context.depCount++;\n    const /** @type {?} */ ast = new AnimateChildAst();\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(metadata: AnimationAnimateRefMetadata, context: AnimationAstBuilderContext):\n      AnimateRefAst {\n    const /** @type {?} */ animation = this.visitReference(metadata.animation, context);\n    const /** @type {?} */ ast = new AnimateRefAst(animation);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const /** @type {?} */ parentSelector = /** @type {?} */(( context.currentQuerySelector));\n    const /** @type {?} */ options = /** @type {?} */(( (metadata.options || {}) as AnimationQueryOptions));\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector =\n        parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n    getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    const /** @type {?} */ ast = new QueryAst(selector, options.limit || 0, !!options.optional, includeSelf, entry);\n    ast.originalSelector = metadata.selector;\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStagger(metadata: AnimationStaggerMetadata, context: AnimationAstBuilderContext):\n      StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(`stagger() can only be used inside of query()`);\n    }\n    const /** @type {?} */ timings = metadata.timings === 'full' ?\n        {duration: 0, delay: 0, easing: 'full'} :\n        resolveTiming(metadata.timings, context.errors, true);\n    const /** @type {?} */ animation =\n        visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    return new StaggerAst(timings, animation);\n  }\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  selector = selector.replace(ENTER_TOKEN_REGEX, ENTER_SELECTOR)\n                 .replace(LEAVE_TOKEN_REGEX, LEAVE_SELECTOR)\n                 .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n                 .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n                 .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any}|null {\n  return obj ? copyObj(obj) : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number; endTime: number;\n};\nexport class AnimationAstBuilderContext {\npublic queryCount: number = 0;\npublic depCount: number = 0;\npublic currentTransition: AnimationTransitionMetadata|null = null;\npublic currentQuery: AnimationQueryMetadata|null = null;\npublic currentQuerySelector: string|null = null;\npublic currentAnimateTimings: TimingAst|null = null;\npublic currentTime: number = 0;\npublic collectedStyles: {[selectorName: string]: {[propName: string]: StyleTimeTuple}} = {};\npublic options: AnimationOptions|null = null;\n/**\n * @param {?} errors\n */\nconstructor(public errors: any[]) {}\n}\n\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationAstBuilderContext.prototype.queryCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.depCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTransition;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuery;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuerySelector;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTime;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.collectedStyles;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.options;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.errors;\n}\n\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles: ɵStyleData | string | (ɵStyleData | string)[]): number|null {\n  if (typeof styles == 'string') return null;\n\n  let /** @type {?} */ offset: number|null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n        const /** @type {?} */ obj = /** @type {?} */(( styleTuple as ɵStyleData));\n        offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n        delete obj['offset'];\n      }\n    });\n  } else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n    const /** @type {?} */ obj = /** @type {?} */(( styles as ɵStyleData));\n    offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n    delete obj['offset'];\n  }\n  return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value: any): boolean {\n  return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: any[]) {\n  let /** @type {?} */ timings: AnimateTimings|null = null;\n  if (value.hasOwnProperty('duration')) {\n    timings = /** @type {?} */(( value as AnimateTimings));\n  } else if (typeof value == 'number') {\n    const /** @type {?} */ duration = resolveTiming( /** @type {?} */((value as number)), errors).duration;\n    return new TimingAst( /** @type {?} */((value as number)), 0, '');\n  }\n\n  const /** @type {?} */ strValue = /** @type {?} */(( value as string));\n  const /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    return new DynamicTimingAst(strValue);\n  }\n\n  timings = timings || resolveTiming(strValue, errors);\n  return new TimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = copyObj(options);\n    if (options['params']) {\n      options['params'] = /** @type {?} */(( normalizeParams(options['params'])));\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (fromState: any, toState: any) => boolean;\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(\n    transitionValue: string | TransitionMatcherFn, errors: string[]): TransitionMatcherFn[] {\n  const /** @type {?} */ expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    ( /** @type {?} */((<string>transitionValue)))\n        .split(/\\s*,\\s*/)\n        .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push( /** @type {?} */((<TransitionMatcherFn>transitionValue)));\n  }\n  return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(\n    eventStr: string, expressions: TransitionMatcherFn[], errors: string[]) {\n  if (eventStr[0] == ':') {\n    eventStr = parseAnimationAlias(eventStr, errors);\n  }\n  const /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n    return expressions;\n  }\n\n  const /** @type {?} */ fromState = match[1];\n  const /** @type {?} */ separator = match[2];\n  const /** @type {?} */ toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias: string, errors: string[]): string {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    default:\n      errors.push(`The transition alias value \"${alias}\" is not supported`);\n      return '* => *';\n  }\n}\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  return (fromState: any, toState: any): boolean => {\n    let /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? lhs === 'true' : lhs === 'false';\n    }\n    if (!rhsMatch && typeof toState === 'boolean') {\n      rhsMatch = toState ? rhs === 'true' : rhs === 'false';\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationQueryMetadata, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata} from '@angular/animations';\n\nexport interface AnimationDslVisitor {\n  visitTrigger(ast: AnimationTriggerMetadata, context: any): any;\n  visitState(ast: AnimationStateMetadata, context: any): any;\n  visitTransition(ast: AnimationTransitionMetadata, context: any): any;\n  visitSequence(ast: AnimationSequenceMetadata, context: any): any;\n  visitGroup(ast: AnimationGroupMetadata, context: any): any;\n  visitAnimate(ast: AnimationAnimateMetadata, context: any): any;\n  visitStyle(ast: AnimationStyleMetadata, context: any): any;\n  visitKeyframes(ast: AnimationKeyframesSequenceMetadata, context: any): any;\n  visitReference(ast: AnimationReferenceMetadata, context: any): any;\n  visitAnimateChild(ast: AnimationAnimateChildMetadata, context: any): any;\n  visitAnimateRef(ast: AnimationAnimateRefMetadata, context: any): any;\n  visitQuery(ast: AnimationQueryMetadata, context: any): any;\n  visitStagger(ast: AnimationStaggerMetadata, context: any): any;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitAnimationNode(\n    visitor: AnimationDslVisitor, node: AnimationMetadata, context: any) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger( /** @type {?} */((node as AnimationTriggerMetadata)), context);\n    case AnimationMetadataType.State:\n      return visitor.visitState( /** @type {?} */((node as AnimationStateMetadata)), context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition( /** @type {?} */((node as AnimationTransitionMetadata)), context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence( /** @type {?} */((node as AnimationSequenceMetadata)), context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup( /** @type {?} */((node as AnimationGroupMetadata)), context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate( /** @type {?} */((node as AnimationAnimateMetadata)), context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes( /** @type {?} */((node as AnimationKeyframesSequenceMetadata)), context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle( /** @type {?} */((node as AnimationStyleMetadata)), context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference( /** @type {?} */((node as AnimationReferenceMetadata)), context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild( /** @type {?} */((node as AnimationAnimateChildMetadata)), context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef( /** @type {?} */((node as AnimationAnimateRefMetadata)), context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery( /** @type {?} */((node as AnimationQueryMetadata)), context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger( /** @type {?} */((node as AnimationStaggerMetadata)), context);\n    default:\n      throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nconst /** @type {?} */ EMPTY_ANIMATION_OPTIONS: AnimationOptions = {};\n\nexport interface AstVisitor {\n  visitTrigger(ast: TriggerAst, context: any): any;\n  visitState(ast: StateAst, context: any): any;\n  visitTransition(ast: TransitionAst, context: any): any;\n  visitSequence(ast: SequenceAst, context: any): any;\n  visitGroup(ast: GroupAst, context: any): any;\n  visitAnimate(ast: AnimateAst, context: any): any;\n  visitStyle(ast: StyleAst, context: any): any;\n  visitKeyframes(ast: KeyframesAst, context: any): any;\n  visitReference(ast: ReferenceAst, context: any): any;\n  visitAnimateChild(ast: AnimateChildAst, context: any): any;\n  visitAnimateRef(ast: AnimateRefAst, context: any): any;\n  visitQuery(ast: QueryAst, context: any): any;\n  visitStagger(ast: StaggerAst, context: any): any;\n  visitTiming(ast: TimingAst, context: any): any;\n}\n/**\n * @abstract\n */\nexport abstract class Ast {\n/**\n * @abstract\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisit(ast: AstVisitor, context: any) {}\npublic options: AnimationOptions = EMPTY_ANIMATION_OPTIONS;\n/**\n * @return {?}\n */\nget params(): {[name: string]: any}|null { return this.options['params'] || null; }\n}\n\nfunction Ast_tsickle_Closure_declarations() {\n/** @type {?} */\nAst.prototype.options;\n}\n\nexport class TriggerAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} name\n * @param {?} states\n * @param {?} transitions\n */\nconstructor(public name: string,\npublic states: StateAst[],\npublic transitions: TransitionAst[]) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTrigger(this, context); }\n}\n\nfunction TriggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTriggerAst.prototype.queryCount;\n/** @type {?} */\nTriggerAst.prototype.depCount;\n/** @type {?} */\nTriggerAst.prototype.name;\n/** @type {?} */\nTriggerAst.prototype.states;\n/** @type {?} */\nTriggerAst.prototype.transitions;\n}\n\nexport class StateAst extends Ast {\n/**\n * @param {?} name\n * @param {?} style\n */\nconstructor(public name: string,\npublic style: StyleAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitState(this, context); }\n}\n\nfunction StateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStateAst.prototype.name;\n/** @type {?} */\nStateAst.prototype.style;\n}\n\nexport class TransitionAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} matchers\n * @param {?} animation\n */\nconstructor(\npublic matchers: ((fromState: string, toState: string) => boolean)[],\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTransition(this, context); }\n}\n\nfunction TransitionAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAst.prototype.queryCount;\n/** @type {?} */\nTransitionAst.prototype.depCount;\n/** @type {?} */\nTransitionAst.prototype.matchers;\n/** @type {?} */\nTransitionAst.prototype.animation;\n}\n\nexport class SequenceAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitSequence(this, context); }\n}\n\nfunction SequenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nSequenceAst.prototype.steps;\n}\n\nexport class GroupAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitGroup(this, context); }\n}\n\nfunction GroupAst_tsickle_Closure_declarations() {\n/** @type {?} */\nGroupAst.prototype.steps;\n}\n\nexport class AnimateAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} style\n */\nconstructor(public timings: TimingAst,\npublic style: StyleAst|KeyframesAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimate(this, context); }\n}\n\nfunction AnimateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateAst.prototype.timings;\n/** @type {?} */\nAnimateAst.prototype.style;\n}\n\nexport class StyleAst extends Ast {\npublic isEmptyStep = false;\n/**\n * @param {?} styles\n * @param {?} easing\n * @param {?} offset\n */\nconstructor(\npublic styles: (ɵStyleData|string)[],\npublic easing: string|null,\npublic offset: number|null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStyle(this, context); }\n}\n\nfunction StyleAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStyleAst.prototype.isEmptyStep;\n/** @type {?} */\nStyleAst.prototype.styles;\n/** @type {?} */\nStyleAst.prototype.easing;\n/** @type {?} */\nStyleAst.prototype.offset;\n}\n\nexport class KeyframesAst extends Ast {\n/**\n * @param {?} styles\n */\nconstructor(public styles: StyleAst[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitKeyframes(this, context); }\n}\n\nfunction KeyframesAst_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyframesAst.prototype.styles;\n}\n\nexport class ReferenceAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitReference(this, context); }\n}\n\nfunction ReferenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nReferenceAst.prototype.animation;\n}\n\nexport class AnimateChildAst extends Ast {\nconstructor() { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateChild(this, context); }\n}\nexport class AnimateRefAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: ReferenceAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateRef(this, context); }\n}\n\nfunction AnimateRefAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateRefAst.prototype.animation;\n}\n\nexport class QueryAst extends Ast {\npublic originalSelector: string;\n/**\n * @param {?} selector\n * @param {?} limit\n * @param {?} optional\n * @param {?} includeSelf\n * @param {?} animation\n */\nconstructor(\npublic selector: string,\npublic limit: number,\npublic optional: boolean,\npublic includeSelf: boolean,\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitQuery(this, context); }\n}\n\nfunction QueryAst_tsickle_Closure_declarations() {\n/** @type {?} */\nQueryAst.prototype.originalSelector;\n/** @type {?} */\nQueryAst.prototype.selector;\n/** @type {?} */\nQueryAst.prototype.limit;\n/** @type {?} */\nQueryAst.prototype.optional;\n/** @type {?} */\nQueryAst.prototype.includeSelf;\n/** @type {?} */\nQueryAst.prototype.animation;\n}\n\nexport class StaggerAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} animation\n */\nconstructor(public timings: AnimateTimings,\npublic animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStagger(this, context); }\n}\n\nfunction StaggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStaggerAst.prototype.timings;\n/** @type {?} */\nStaggerAst.prototype.animation;\n}\n\nexport class TimingAst extends Ast {\n/**\n * @param {?} duration\n * @param {?=} delay\n * @param {?=} easing\n */\nconstructor(\npublic duration: number,\npublic delay: number = 0,\npublic easing: string|null = null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction TimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTimingAst.prototype.duration;\n/** @type {?} */\nTimingAst.prototype.delay;\n/** @type {?} */\nTimingAst.prototype.easing;\n}\n\nexport class DynamicTimingAst extends TimingAst {\n/**\n * @param {?} value\n */\nconstructor(public value: string) { super(0, 0, ''); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction DynamicTimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nDynamicTimingAst.prototype.value;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationMetadata, AnimationOptions, sequence, ɵStyleData} from '@angular/animations';\n\nexport const /** @type {?} */ ONE_SECOND = 1000;\n\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const /** @type {?} */ matches = ( /** @type {?} */((value as string))).match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:  // ms or something else\n      return value;\n  }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(\n    timings: string | number | AnimateTimings, errors: any[], allowNegativeValues?: boolean) {\n  return timings.hasOwnProperty('duration') ? /** @type {?} */((\n      <AnimateTimings>timings)) :\n      parseTimeExpression( /** @type {?} */((<string|number>timings)), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(\n    exp: string | number, errors: string[], allowNegativeValues?: boolean): AnimateTimings {\n  const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let /** @type {?} */ duration: number;\n  let /** @type {?} */ delay: number = 0;\n  let /** @type {?} */ easing: string = '';\n  if (typeof exp === 'string') {\n    const /** @type {?} */ matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(`The provided timing value \"${exp}\" is invalid.`);\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const /** @type {?} */ delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n    }\n\n    const /** @type {?} */ easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = /** @type {?} */(( <number>exp));\n  }\n\n  if (!allowNegativeValues) {\n    let /** @type {?} */ containsErrors = false;\n    let /** @type {?} */ startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(`Duration values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(`Delay values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n    }\n  }\n\n  return {duration, delay, easing};\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(\n    obj: {[key: string]: any}, destination: {[key: string]: any} = {}): {[key: string]: any} {\n  Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n  return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles: ɵStyleData | ɵStyleData[]): ɵStyleData {\n  const /** @type {?} */ normalizedStyles: ɵStyleData = {};\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, false, normalizedStyles));\n  } else {\n    copyStyles(styles, false, normalizedStyles);\n  }\n  return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(\n    styles: ɵStyleData, readPrototype: boolean, destination: ɵStyleData = {}): ɵStyleData {\n  if (readPrototype) {\n    // we make use of a for-in loop so that the\n    // prototypically inherited properties are\n    // revealed from the backFill map\n    for (let /** @type {?} */ prop in styles) {\n      destination[prop] = styles[prop];\n    }\n  } else {\n    copyObj(styles, destination);\n  }\n  return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = styles[prop];\n    });\n  }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    });\n  }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps: AnimationMetadata | AnimationMetadata[]):\n    AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return /** @type {?} */(( steps as AnimationMetadata));\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(\n    value: string | number, options: AnimationOptions, errors: any[]) {\n  const /** @type {?} */ params = options.params || {};\n  if (typeof value !== 'string') return;\n\n  const /** @type {?} */ matches = value.toString().match(PARAM_REGEX);\n  if (matches) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(\n            `Unable to resolve the local animation param ${varName} in the given list of values`);\n      }\n    });\n  }\n}\n\nconst /** @type {?} */ PARAM_REGEX = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(\n    value: string | number, params: {[name: string]: any}, errors: any[]): string|number {\n  const /** @type {?} */ original = value.toString();\n  const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n    let /** @type {?} */ localVal = params[varName];\n    // this means that the value was never overidden by the data passed in by the user\n    if (!params.hasOwnProperty(varName)) {\n      errors.push(`Please provide a value for the animation param ${varName}`);\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator: any): any[] {\n  const /** @type {?} */ arr: any[] = [];\n  let /** @type {?} */ item = iterator.next();\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n  return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(\n    source: AnimationOptions, destination: AnimationOptions): AnimationOptions {\n  if (source.params) {\n    const /** @type {?} */ p0 = source.params;\n    if (!destination.params) {\n      destination.params = {};\n    }\n    const /** @type {?} */ p1 = destination.params;\n    Object.keys(p0).forEach(param => {\n      if (!p1.hasOwnProperty(param)) {\n        p1[param] = p0[param];\n      }\n    });\n  }\n  return destination;\n}\n\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\n\nimport {containsElement, invokeQuery, matchesElement} from './shared';\n\n\n/**\n * @experimental\n */\nexport class NoopAnimationDriver implements AnimationDriver {\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): AnimationPlayer {\n    return new NoopAnimationPlayer();\n  }\n}\n\n/**\n * @experimental\n */\nexport abstract class AnimationDriver {\n  static NOOP: AnimationDriver = new NoopAnimationDriver();\n\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing?: string|null, previousPlayers?: any[]): any;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["NoopAnimationPlayer", "ɵPRE_STYLE", "AUTO_STYLE", "PRE_STYLE", "style", "tslib_1.__extends", "ɵAnimationGroupPlayer"], "mappings": ";;;;;;AqBAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;;;;;;;;AD3IP,SAAA,mBAAA,CAAuB,OAAvB,EAAA;IACA,QAAA,OAAA,CAAA,MAAA;QACA,KAAA,CAAA;YACA,OAAA,IAAAA,uCAAA,EAAA,CAAA;QACA,KAAA,CAAA;YAEA,OAAA,OAAA,CAAA,CACI,CADJ,CAAA;QAIA;YACA,OAAA,IAAAM,yCAAA,CAAA,OAAA,CAAA,CAAA;KACA;CACA;AACA,SAAA,kBAAA,CAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA;IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAA,GAAA,EAAA,CAAA,EAAA;IAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAA,GAAA,EAAA,CAAA,EAAA;IACA,IAAA,MAAA,GAAA,EAAA,CAAA;IACA,IAAA,mBAAyB,GAAzB,EAAA,CAA+B;IAC/B,IAAI,cAAJ,GAAA,CAAA,CAAA,CAAA;IACA,IAAI,gBAAgB,GAApB,IAA2B,CAAC;IAC5B,SAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;QACA,IAAA,MAAA,GAAA,EAAA,CAAA,QAA4B,CAA5B,CAA8B;QAC9B,IAAA,YAAA,GAAA,MAA6BH,IAA7B,cAAA,CAAA;QACA,IAAA,kBAAA,GAAA,CAAA,YAAwC,IAAxC,gBAAA,KAAA,EAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;YAAA,IAAA,cAAA,GAAA,IAAA,CAAA;YACA,IAAQ,eAAe,GAAG,EAA1B,CAAA,IAAA,CAAA,CAAA;YACA,IAAA,eAAA,IAAAF,8BAAA,EAAA;gBAAA,eAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;aACA;iBACA,IAAA,eAAA,IAAAC,8BAAA,EAAA;gBACA,eAAA,GAAA,UAAA,CAAA,IAAA,CAAA,CAAA;aACA;iBACA,IAAA,IAAA,IAAA,QAAA,EAAA;gBACA,cAAA,GAAA,UAAA,CAAA,qBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;gBACA,eAAyB,GAAzB,UAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,cAAA,EAAA,EAAA,CAAA,IAAA,CAAA,EAAA,MAAA,CAAA,CAAA;aACA;YACA,kBAAA,CAAA,cAAyC,CAAC,GAA1C,eAAA,CAAA;SACA,CAAA,CAAA;QACA,IAAA,CAAA,YAAA,EAAA;YACA,mBAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,CAAA;SACA;QACI,gBACI,GADR,kBAAA,CAAA;QAEA,cAAA,GAAA,MAAA,CAAA;KAEA,CAAA,CAAA;IACA,IAAA,MAAA,CAAA,MAAA,EAAA;QAEA,IAAA,UACI,GADJ,OAAA,CAAA;QAGA,MAAA,IAAA,KAAA,CAAA,gDAAA,GAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;KACA;IACA,OAAA,mBAAA,CAAA;CACA;AACA,SAAA,cAAA,CAAA,MAAA,EAAA,SAAA,EAAA,KAAA,EAAA,QAAA,EAAA;IACA,QAAM,SAAN;QACA,KAAA,OAAA;YACA,MAAA,CAAA,OAAA,CAAA,YAAA,EAAA,OAAA,QAAA,CAAA,KAAA,IAAA,kBAAA,CAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACM,MAAM;QAEZ,KAAA,MAAA;YACA,MAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,QAAA,CAAA,KAAA,IAAA,kBAAA,CAAA,KAAA,EAAA,MAAA,EAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACA,MAAA;QAEA,KAAA,SAAA;YAEA,MAAgB,CAAhB,SAAA,CAAA,YAAA,EAAA,OAAA,QAAA,CAAA,KACkB,IADlB,kBAAA,CAAA,KAC8C,EAD9C,SAAA,EAAA,MACiE,CADjE,SAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YAGA,MAAyB;KACzB;CACA;AACA,SAAA,kBAAA,CAAA,CAAA,EAAA,SAAA,EAAA,SAAA,EAAA;IACE,IAAF,KAAA,GAAA,kBAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,OAAA,EAAA,SAAA,IAAA,CAAA,CAAA,SAAA,EAAA,SAAA,IAAA,SAAA,GAAA,CAAA,CAAA,SAAA,GAAA,SAAA,CAAA,CAAA;IACA,IAAA,IAAA,GAAA,CAAA,CAAA,OAAA,CAAA,CAAA;IAEA,IAAA,IAAA,IAAA,IAAA,EAAA;QAGA,KAAU,CAAV,OAAA,CAAmB,GAAnB,IAAA,CAAA;KACA;IAEA,OAAA,KAAA,CAAA;CAEA;AACA,SAAA,kBAAA,CAA0B,OAA1B,EAAA,WAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA;IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAA,GAAA,EAAA,CAAA,EAAA;IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAA,GAAA,CAAA,CAAA,EAAA;IACA,OAAA,EAAS,OAAO,EAAhB,OAAgB,EAAhB,WAAA,EAAA,WAAA,EAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,CAAA;CACA;AACA,SAAA,eAAA,CAAmB,GAAnB,EAAwB,GAAG,EAA3B,YAAyC,EAAzC;IACA,IAAA,KAAA,CAAA;IACA,IAAA,GAAA,YAAA,GAAA,EAAA;QAAA,KAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACI,IAAJ,CAAS,KAAT,EAAgB;YACR,GAAR,CAAA,GAAA,CAAgB,GAAhB,EAAA,KAAA,GAAA,YAAA,CAAA,CAAA;SACA;KACA;SACA;QACA,KAAA,GAAc,GAAd,CAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,EAAA;YAEA,KAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,YAAA,CAAA;SACA;KACA;IACE,OAAF,KAAc,CAAd;CACA;AACA,SAAA,oBAAA,CAAA,OAAA,EAAA;IAEI,IAAJ,YAAA,GAAA,OAA6E,CAA7E,OAAA,CAAA,GAAA,CAAA,CAAA;IACI,IAAJ,EAAY,GAAgD,OAA5D,CAAyE,SAAzE,CAA2F,CAA3F,EAAA,YAAA,CAAA,CAAA;IAEI,IAAM,MAAV,GAAA,OAAA,CAAA,MACqC,CADrC,YAAA,GAAA,CAAA,CAAA,CAAA;IAEM,OAAO,CAAb,EAAgB,EAAhB,MAAA,CAAA,CAAA;CACK;AAEL,IAAI,SAAJ,GAAA,UAAA,IAAA,EAAA,IAAA,EAAA,EAAA,OAAA,KAAA,CAAA,EAAA,CAAA;;AAEA,IAAE,MAAF,GAAW,UAAX,OAAwB,EAAE,QAA1B,EAAA,KAAA,EAAA;IAEE,OAAF,EAAA,CAAA;CACA,CAAA;AACA,IAAA,OAAA,OAAA,IAAA,WAAA,EAAA;;IACA,SAAA,GAAA,UAAA,IAAA,EAAA,IAAA,EAAA,EAAA,OAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;IACA,IAAI,OAAJ,CAAY,SAAS,CAArB,OAAA,EAAA;QACA,QAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,EAAA,OAAA,OAAA,CAAA,OAAA,CAAA,QAA8D,CAA9D,CAAA,EAAA,CAAA;KACA;SACA;QACA,IAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA;QACA,IAAA,IAAA,GAAA,KAAA,CAAA,eAAA,IAAA,KAAA,CAAA,kBAAA,IAAA,KAAA,CAAA,iBAAA;YAEA,KAAA,CAAA,gBAAA,IAA0D,KAA1D,CAAA,qBAAA,CAAA;QACI,IAAI,IAAR,EAAA;YACQ,QAAR,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,EAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA;SACA;KACA;IAAA,MAAA,GAAW,UAAX,OAAA,EAAA,QAAA,EAAA,KAAA,EAAA;QACA,IAAM,OAAN,GAAA,EAAkB,CAAlB;QACA,IAAM,KAAN,EAAa;YACb,OAAA,CAAA,IAAgB,CAAhB,KAAA,CAAA,OAAA,EAAoB,OAApB,CAAA,gBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;SACA;aACA;YACA,IAAA,GAAA,GAAA,OAAA,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;YACA,IAAA,GAAA,EAAA;gBACA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;aAEA;SACA;QACA,OAAA,OAAA,CAAA;;CDjKA;;;;;;;;;;;;;;AAsBA,IAAA,mBAAA,IAAA,YAAA;IAAA,SAAA,mBAAA,GAAA;;IACA,mBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,OAAsB,EAAtB,QAAgC,EAAhC;QACA,OAAA,cAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;KAEA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,IAAA,EAAA,IAAA,EAAA,EAA8B,OAA9B,eAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,EAAA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,KAAA,EAAA;QAEA,OAAA,WAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;KAGA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA;QACA,OAAA,YAAA,IAAA,EAAA,CAAA;;;;;KAKA,CAAA;;CAlBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;ADNA,IADa,UACb,GAAA,IAAA,CAAA;AACA,IADa,eACb,GAAA,UAAA,CAAA;AACA,IADa,eACb,GAAA,UAAA,CAAA;AACA,IADa,cACb,GAAA,WAAA,CAAA;;;;;AAKA,IAAA,qBAAA,GAAA,eAAA,CAAA;;;;;AAAA,SAAA,kBAAA,CAAA,KAAA,EAAA;IAME,IAAF,OAAA,KAAA,IAAA,QAJS;QAKT,OAAA,KAAA,CAAA;;;;;;;;;;;AAWA,SAAA,qBAAA,CAAA,KAAA,EAAA,IAAA,EAAA;IACA,QAAA,IAAA;QACA,KAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAAA,mBAAA,CAAA,GAAA,EAAA,MAAA,EAAA,mBAAA,EAAA;IACA,qBAAA,KAAA,GAAA,0EAAA,CAAA;IACA,qBAAA,QAAA,CAAA;IACA,qBAAA,KAAA,GAAA,CAAA,CAAA;IACA,qBAAA,MAlBwB,GAAG,EAkB3B,CAAA;IACA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;QAEI,qBAAJ,OAAA,GAlBqC,GAkBrC,CAAA,KAAA,CAlBsC,KAkBtC,CAAA,CAAA;QAEI,IAAJ,OAAA,KAAA,IAAA,EAAA;YACQ,MAAR,CAAA,IAAA,CAAA,8BAAA,GAAA,GAAA,GAAA,gBAAA,CAAA,CAAA;YACM,OAAN,EAAA,QAAA,EAAA,CAAA,EAAA,KAAA,EAlBc,CAAsB,EAkBpC,MAAA,EAlB0C,EAkB1C,EAAA,CAAA;SACK;QAED,QAAJ,GAAA,qBAlBU,CAkBV,UAlB8B,CAAC,OAkB/B,CAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,qBAAJ,UAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QACA,IAAM,UAAN,IAAA,IAlBe,EAkBf;YACA,KAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SACA;QAlBA,qBAAA,SAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QAmBI,IAAJ,SAAA,EAlBwB;YAmBxB,MAAA,GAAA,SAAA,CAAA;SAhBO;KAmBP;SACA;QACI,QAAJ,IAlBS,GAAU,CAAA,CAkBnB;KACA;IACA,IAAA,CAAA,mBAAA,EAAA;QACA,qBAAA,cAAA,GAAA,KAAA,CAAA;QACI,qBAAJ,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA;QACA,IAAM,QAAN,GAlBc,CAAI,EAkBlB;YACM,MAAN,CAAA,IAAA,CAAA,kEAAA,CAAA,CAAA;YACA,cAAA,GAAA,IAAA,CAAA;SACA;QACA,IAAM,KAAN,GAAA,CAAA,EAAA;YACA,MAAA,CAAA,IAAA,CAAA,+DAAA,CAAA,CAAA;YACA,cAAA,GAAA,IAAA,CAAA;SAEA;QACA,IAAA,cAAA,EAAA;;;;;;;;;;;;;;;CAeA;;;;;AArBA,SAAA,eAAA,CAAA,MAAA,EAAA;IA0BA,qBAzBuB,gBAyBvB,GAAA,EAAA,CAAA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,IAzBS,EAyBT,EAAA,OAAA,UAAA,CAAA,IAAA,EAAA,KAAA,EAAA,gBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;KACA;;;;;;;;;;;;AAaA,SAAA,UAAA,CAAA,MAAA,EAAA,aAAA,EAAA,WAAA,EAAA;IAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,EAAA,CAAA,EAAA;IACA,IAAA,aAAA,EAAiB;;;;QAGb,KAAJ,qBAAA,IAAA,IAAA,MAAA,EAAA;YACA,WAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,CAAA;SACA;KACA;;;;;;;;;;;AAWA,SAAA,SAAA,CAAA,OAAA,EAAA,MAAA,EAAA;IACA,IAAA,OAAA,CAAA,OAAA,CAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;;;;;;;;AAWA,SAAA,WAAA,CAAA,OAAA,EAAA,MAAA,EAAA;IACA,IAAA,OAAA,CAAA,OAAA,CAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;;CAKA;;;;;AAKA,SAAA,uBAAA,CAAA,KAAA,EAAA;IACE,IAAF,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA;QACA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA;;;;;;;;;;;;AAaA,SAAA,mBAAA,CAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA;IACA,qBAAA,MAAA,GAAA,OAAA,CAAA,MAAA,IAAA,EAAA,CAAA;IACA,IAAA,OAAA,KAAA,KAAA,QAAA;QACA,OAAA;IAEA,qBAAA,OAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;IACA,IAAA,OAAA,EAAA;QACA,OAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;YACA,IAAA,CAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,EAAA;gBAEA,MAAA,CAAA,IAAA,CAAA,8CAAA,GAAA,OAAA,GAAA,8BAAA,CAAA,CAAA;;;;;;;;;;;;AAaA,SAAA,iBAAA,CAAA,KAAA,EApDiB,MAoDjB,EApDgC,MAoDhC,EAAA;IACA,qBAAA,QAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA;IACA,qBAAA,GAAA,GAAA,QAAA,CAAA,OAAA,CAAA,WAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA;QACA,qBAAA,QAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA;;QAEA,IAAA,CAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,EAAA;;YAGA,QAAA,GAAA,EAAA,CApDgB;SAqDhB;;;;;CAKA;;;;;AAKA,SAAA,eAAA,CAAA,QAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IACE,qBAAF,IAAA,GAAA,QAAA,CAAA,IAAA,EAAA,CAAA;IACA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA;;;;;;;;;;;AAYA,SAAA,qBAAA,CAAA,MAAA,EAAA,WAAA,EAAA;IACA,IAAI,MAAJ,CAAA,MAAA,EAAA;QACI,qBA3DiB,IA2DrB,GAAA,MAAA,CAAA,MAAA,CAAA;QACA,IAAM,CAAN,WAAA,CAAA,MAAA,EAAA;YACA,WAAA,CA3DY,MAAQ,GA2DpB,EAAA,CA3DwB;SA4DxB;QACA,qBAAA,IAAA,GAAA,WAAA,CAAA,MAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;YACA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,KAAA,CAAA,EAAA;gBACA,IAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA;aAEA;;;;;AAKA,IAAA,gBAAA,GAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;ADpPA,IAAA,uBAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;IAaA,GAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;IARA,SAAA,UAAA,CAAA,IAAA,EAAA,MAAgC,EAAhC,WAAA,EAAA;QAAA,IAAA,KAAA,GACS,MADT,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAAA;QA2BA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;;;AA2BA,IAAA,QAAA,IAAA,UAAA,MAAA,EAAA;IAAAG,SAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAwBA,IAAA,aAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;;;;;IA5DA,SAAA,aAAA,CAAA,QAA4B,EAAE,SAA9B,EAAA;QAAA,IAAA,KAAA,GAgEA,MAhEA,CAAA,IAAA,CAAA,IAAA,CAgEA,IAhEA,IAAA,CAAA;;;;;;;;;;;;;IAwFA,OAAA,aAAA,CAAA;CAAA,CA5BA,GAAA,CA4BA,CAAA,CAAA;AA7EA,IAAA,WAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;IA+FA,OAAA,WAAA,CAAA;CAAA,CA/FA,GAAA,CA+FA,CAAA,CAAA;AAzFA,IAAA,QAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AA4GA,IAAA,UAAA,IAAA,UAAA,MAAA,EAAA;IAtGgDD,SAAhDA,CAAAA,UAAAA,EAAAA,MAAAA,CAAAA,CAsGA;;;;;;;;;;;;;;;;;;CAAA,CAtGgDA,GAsGhD,CAAA,CAAA,CAAA;;;;;;;;IAhGA,SAAA,QAAA,CAAA,MAAA,EAAS,MAAT,EAAuB,MAAvB,EAAA;QAAA,IAAA,KAAA,GA6HA,MA7HA,CAAA,IAAA,CAAA,IAAA,CA6HA,IA7HA,IAAA,CAAA;;;;;;;;;;;;;IAqJA,OAAA,QAAA,CAAA;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAzIA,IAAA,YAAA,IAAA,UAAA,MAAA,EAAA;IAAAC,SAAA,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;IA2JA,OAAA,YAAA,CAAA;CAAA,CA3JA,GAAA,CA2JA,CAAA,CAAA;AArJA,IAAA,YAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;IAgLA,OAAA,eAAA,CAAA;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AApKA,IAAA,aAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;;;;;;;;;;IAUA,SAAA,QAAA,CAAA,QAAa,EAAb,KAAA,EAAA,QAAA,EAAA,WAAA,EAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GAA0C,MAA1C,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;QAwLA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;AA6BA,IAAA,UAAA,IAAA,UAAA,MAAA,EAAA;IA7MqDA,SAArD,CAAA,UAAA,EAAA,MAAA,CAAA,CA6MA;;;;;;;;;;;;;;;;;;CAAA,CA7MqD,GA6MrD,CAAA,CAAA,CAAA;;;;;;;;IA4BA,SAAA,SAAA,CAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA;QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAA,CAAA,CAAA,EAAA;QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAAA,IAAA,CAAA,EAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;;;;;;;;;;;;IAsBA,OAAA,SAAA,CAAA;CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAhPA,IAAA,gBAAA,IAAA,UAAA,MAAA,EAAA;IAAAA,SAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;AD7GA,SAAA,kBAAA,CAAA,OAAA,EANsB,IAMtB,EAAA,OAAA,EAAA;IACA,QAAA,IAAA,CAAA,IAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,YAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,UAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,eAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,aAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,UAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,YAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,cAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,UAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,cAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,OAAA,OAAA,CAAA,iBAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA;YACA,OAAA,OAAA,CAAA,eAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA;YACA,OAAA,OAAA,CAAA,UAAA,mBAAA,IAAA,GAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA;;QD9DA;;;;;;;;;;;;;;;;;AAoBA,SAAA,mBAAA,CAAA,eAAA,EAAA,MALgD,EAKhD;IACA,qBAAA,WAAA,GAAA,EAAA,CAAA;IALA,IAAA,OAAA,eAAA,IAAA,QAAA,EAAA;QAMI,EAAJ,eAAA;aACA,KAAA,CAAA,SAAA,CAAA;aACA,OAAA,CAAA,UAAA,GAAA,EAAA,EAAA,OAAA,uBAAA,CAAA,GAAA,EAAA,WAAA,EAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA;KACA;;;;;;;;;;;;AAaA,SAAA,uBAAA,CAAA,QAVsC,EAAG,WAUzC,EAAA,MAAA,EAAA;IACA,IAAI,QAAJ,CAAA,CAAA,CAVY,IAUZ,GAAA,EAAA;QACI,QAAJ,GAAA,mBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;KACG;IAED,qBAVM,KAUR,GAAA,QAAA,CAVoB,KAUpB,CAAA,yCAAA,CAAA,CAAA;IACE,IAAF,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,uCAAA,GAAA,QAAA,GAAA,qBAAA,CAAA,CAAA;QACA,OAAa,WAAb,CAAA;KAEA;IACE,qBAVoB,SAUtB,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,qBAAA,SAAA,GAAA,KAAA,CAVsB,CAAoB,CAU1C,CAAA;IACA,qBAAA,OAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,WAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,SAAA,EAAA,OAAA,CAAA,CAAA,CAAA;;;;;;;;;;;AAWA,SAAA,mBAAA,CAAA,KAAA,EAAA,MAAA,EAAA;IACA,QAAA,KAAA;QACA,KAAA,QAAA;YACM,OAdO,WAcb,CAAA;QACA,KAAA,QAAA;YACA,OAAA,WAAA,CAAA;;;;;;;;;;;AAYA,SAAA,oBAAA,CAAA,GAAA,EAlB6B,GAAA,EAkB7B;IACA,OAAA,UAAA,SAAA,EAAA,OAAA,EAAA;QACI,qBAAJ,QAAA,GAAA,GAAA,IAAA,SAAA,IAAA,GAAA,IAAA,SAAA,CAAA;QACA,qBAAA,QAAA,GAAA,GAAA,IAlBmC,SAkBnC,IAAA,GAAA,IAAA,OAAA,CAAA;QACA,IAAA,CAAA,QAAA,IAAA,OAAA,SAAA,KAAA,SAAA,EAAA;YAEA,QAAA,GAlBW,SAkBX,GAlBuB,GAkBvB,KAAA,MAAA,GAAA,GAAA,KAAA,OAAA,CAAA;SACA;QACA,IAAA,CAAA,QAAA,IAAA,OAAA,OAAA,KAAA,SAAA,EAAA;;SDzFA;;;;;;;;;;;;;;;;;;AA+BA,SAAA,iBAAA,CAAA,QAAA,EAAA,MAAA,EAAA;IACA,OAAA,IAAA,0BAAA,EAAA,CAAA,KAAA,CA+BkD,QA/BlD,EAAA,MAAA,CAAA,CAAA;CACA;AACA,IAAA,WAAA,GAAA,QAAA,CAAA;;;;;;;KAqYA;;;;;;;;;;KA9VA,CAAA;;;;;IAnBA,0BAAA,CAAA,SAAA,CAAA,6BAAA,GAAA,UAAA,OAAA,EAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAoBa,QApBb,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,UAAA,GAAA,OAAA,CAAA,UAAA,GAAA,CAAA,CAAA;QACA,qBAAA,QAAA,GAAA,OAAA,CAAA,QAAA,GAAA,CAAA,CAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;QACA,qBAAA,WAAA,GAAA,EAAA,CAAA;QACA,QAAQ,CAAR,WAAA,CAAA,OAoBoB,CAAS,UAAA,GApB7B,EAAA;YACA,KAAA,CAAA,6BAAA,CAAA,OAAA,CAAA,CAAA;YACA,IAAA,GAAA,CAAU,IAAV,IAAA,CAAA,cAAA;gBACA,qBAAA,UAAA,IAAA,GAAA,CAAA,CAAA;gBACQ,qBAAR,IAAA,GAAA,UAAA,CAAA,IAAA,CAAA;gBACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;oBAoBA,UAAA,CAAsB,IAAtB,GAAA,CAAA,CAAA;oBAnBA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAoB2B,EApB3B,OAAA,CAAA,CAAA,CAAA;iBACA,CAAA,CAAA;gBACQ,UAAQ,CAAhB,IAAA,GAAA,IAAA,CAAA;aACA;iBACA,IAAA,GAAA,CAAA,IAAA,IAAA,CAAA,mBAAA;gBAoBA,qBAAA,UAAA,GAAA,KAAA,CAAA,eAAA,mBAAA,GAAA,GAAA,OAAA,CAAA,CAAA;gBAnBQ,UAAR,IAoBiB,UApBjB,CAAA,UAAA,CAAA;gBAEA,QAAA,IAAA,UAAA,CAAA,QAAA,CAAA;gBACA,WAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;aACA;iBACA;gBACA,OAAA,CAAA,MAAA,CAAA,IAoBqB,CAAW,yEApBhC,CAAA,CAAA;aACA;SACA,CAAA,CAAA;QACA,qBAAA,GAAA,GAAA,IAAA,UAAA,CAAA,QAAA,CAAA,IAAA,EAAA,MAAA,EAAA,WAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;IAoBA,0BAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,QAAA,EAAA,OAYgB,EAZhB;QACI,OAAJ,CAAA,UAAA,GAAA,CAAA,CAAA;QACI,OAAJ,CAAA,QAAA,GAAA,CAAA,CAAA;QACI,qBAYuB,KAZ3B,GAY4B,kBAZ5B,CAAA,IAAA,EAAA,uBAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACI,qBAAJ,QAAA,GAAA,mBAAA,CAAA,QAAA,CAAA,IAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,qBAAA,GAAA,GAAA,IAAA,aAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;IAsBA,0BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAIqB,WAJrB,GAAA,OAAA,CAAA,WAAA,CAAA;QACA,qBAAA,YAAA,GAAA,CAAA,CAAA;QACA,qBAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;YAEA,OAAA,CAAA,WAAA,GAAA,WAAA,CAAA;YACA,qBAAA,QAAA,GAAA,kBAAA,CAAA,KAAA,EAAA,IAAA,EAAA,OAAA,CAAA,CAAA;YAKQ,YAJR,GAAA,IAAA,CAAA,GAAA,CAAA,YAI4C,EAJ5C,OAIqD,CAJrD,WAAA,CAAA,CAAA;YACA,OAIe,QAJf,CAAA;SACA,CAAA,CAAA;;;;;;;;;;;IAaA,0BAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,QAAS,EAAT,OAAA,EAAA;QACA,qBAAA,SAAqB,GAArB,kBAAA,CAAA,QAAA,CAAA,OAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,OAAA,CAAA,qBAAA,GAAA,SAAA,CAAA;QAAA,qBAAA,MAAA,CAAA;QACA,qBAAA,aAAA,GAAA,QAAA,CAAA,MAAA,GAAA,QAAA,CAAA,MAAA,GAAAD,yBAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAM,aAAN,CAAA,IAAA,IAAA,CAAA,kBAAA;YACM,MAAN,GAAA,IAAA,CAAA,cAAA,mBAAA,aAAA,GAAA,OAAA,CAAA,CAAA;SACA;aACA;YACA,qBAAA,eAAA,IAAA,QAAA,CAAA,MAAA,CAAA,CAAA;YACA,qBAAuB,OAAvB,GAAA,KAAA,CAAA;YACA,IAAA,CAAS,eAAT,EAAA;gBACQ,OAAR,GAAA,IAAA,CAAA;gBACA,qBAAA,YAAA,GAAA,EAAA,CAAA;gBACA,IAAc,SAAd,CAAA,MAAA,EAAA;oBACA,YAAA,CAAA,QAAA,CAAA,GAAA,SAAA,CAAA,MAA6B,CAAU;iBACvC;gBACA,eAAe,GAAfA,yBAAA,CAAA,YAAA,CAAA,CAAA;aACA;YAEA,OAAA,CAAA,WAAA,IAAA,SAAoC,CAAK,QAAzC,GAAA,SAAA,CAAA,KAAA,CAAA;YACA,qBAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;YACA,QAAA,CAAA,WAAA,GAAA,OAAA,CAAA;;;;;;;;;;;;;;;;;;;;;IAsBA,0BAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;QACA,qBARmB,MAQnB,GAAA,EAAA,CAAA;QACA,IAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;YARA,EAAA,QAAA,CAAA,MAAA,GAAA,OAAA,CAAA,UAAA,UAAA,EAAA;gBASA,IAAA,OAAA,UAAA,IAAA,QAAA,EAAA;oBACA,IAAA,UAAA,IAAAF,8BAAA,EAAA;wBACA,MAAA,CAAA,IAAA,mBAAA,UAAA,EAAA,CAAA;qBARe;yBASf;wBACA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,kCAAA,GAAA,UAAA,GAAA,kBAAA,CAAA,CAAA;qBACA;iBACA;qBARA;oBASA,MAAA,CAAA,IAAA,mBAAA,UAAA,EAAA,CAAA;iBACA;aAEA,CAAA,CAAA;SACA;aACA;YACA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MARc,CAQd,CAAA;SACA;QACA,qBAAA,eAAA,GAAA,IAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;YACA,IAAA,QAAA,CAAA,SAAA,CARiB,EAQjB;gBACA,qBAAA,QAAA,IAAA,SAAA,CAAA,CAAA;gBACA,qBAAA,MAAA,GAAA,QAAA,CAAA,QAAA,CAAA,CAAA;gBACA,IAAA,MAAA,EAAA;oBACA,eAAA,IAAA,MAAA,CAAA,CAAA;oBACA,OAAA,QAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;;;IAWA,0BAAA,CAAA,SAAA,CAAA,iBAAe,GAAf,UAAA,GAZmB,EAYnB,OAAA,EAAA;QACA,qBAAA,OAAA,GAAA,OAAA,CAAA,qBAAA,CAAA;QAEI,qBAAJ,OAAA,GAAA,OAAA,CAAA,WAAA,CAAA;QACA,qBAAA,SAAA,GAAA,OAAA,CAAA,WAAA,CAAA;QAZA,IAAA,OAAA,IAAoC,SAApC,GAAA,CAAA,EAAA;YAcM,SAAN,IAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA,CAAA;SACA;QACA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;YACA,IAAQ,OAAR,KAAA,IAAA,QAAA;gBACQ,OAAR;YACA,MAAA,CAAA,IAAA,CAAc,KAAd,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBACA,qBAAA,eAAA,GAZyC,OAYzC,CAAA,eAAA,GAAA,OAAA,CAAA,oBAAA,GAAA,CAAA;gBACA,qBAZ2B,cAY3B,GAAA,eAAA,CAAA,IAAA,CAAA,CAAA;gBAEA,qBAAA,oBAAA,GAAA,IAAA,CAAA;gBACA,IAAA,cAAA,EAAA;;;;wBAKA,oBAAA,GAZqC,KAYrC,CAAA;qBACA;;;;oBAMY,SAAZ,GAAA,cAAA,CAAA,SAAA,CAAA;iBACA;gBACA,IAAA,oBAAA,EAAA;oBACA,eAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA;iBACA;gBACA,IAAA,OAAA,CAAA,OAAA,EAAA;;;;;;;;;;;IAaA,0BAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEI,IAAJ,CAAA,OAAA,CAAA,qBAAA,EAAA;YACA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,0DAAA,CAAA,CAAA;YACA,OAAA,IAAA,YAAA,CAAA,EAAA,CAAA,CAAA;SACA;QACI,qBAAJ,mBAAA,GAAA,CAAA,CAAA;QAEI,qBAAJ,yBAAA,GAhB4C,CAAK,CAgBjD;QACA,qBAAA,OAAA,GAAA,EAhBY,CAgBZ;QACA,qBAAA,iBAAA,GAAA,KAfgB,CAehB;QAEA,qBAAA,mBAAA,GAAA,KAAA,CAAA;QACA,qBAhBuB,cAgBvB,GAAA,CAAA,CAAA;QACA,qBAAA,SAAA,GAhBkC,QAgBlC,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,MAAA,EAAA;YACA,qBAAA,QAhBgC,GAgBhC,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;YACA,qBAAA,SAAA,GAAA,QAAA,CAAA,MAAA,IAAA,IAAA,GAAA,QAAA,CAAA,MAAA,GAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;YACM,qBAAN,MAAA,GAAA,CAAA,CAAA;YACM,IAAN,SAAA,IAAuB,IAAvB,EAAA;gBACA,yBAAA,EAAA,CAAA;gBACA,MAAA,GAhBmB,QAAQ,CAgB3B,MAAA,GAAA,SAAA,CAAA;aACA;YACA,mBAAA,GAAA,mBAAA,IAAA,MAAA,GAAA,CAAA,IAAA,MAAA,GAAA,CAAA,CAAA;YAEQ,iBAAR,GAAA,iBAAA,IAAA,MAAA,GAAA,cAAA,CAAA;YACM,cAhBS,GAgBf,MAAA,CAAA;YACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YAEQ,OAAR,QAAA,CAAA;SACA,CAAA,CAAA;QACA,IAAA,mBAAA,EAAA;YAEA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,6DAAA,CAAA,CAAA;SACA;QACI,IAAI,iBAAR,EAAA;YACM,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,sDAgB1B,CAAA,CAAA;SACK;QAhBL,qBAAA,MAAA,GAAA,QAAA,CAA+C,KAA/C,CAAA,MAAA,CAAA;QAiBA,qBAAA,eAAA,GAAA,CAAA,CAhBwB;QAiBxB,IAAA,yBAAA,GAAA,CAAA,IAAA,yBAAA,GAAA,MAAA,EAAA;YAEA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,uEAAA,CAAA,CAAA;SACA;aACA,IAAA,yBAAA,IAAA,CAAA,EAAA;YACA,eAAA,GAAA,mBAhB4B,IAgB5B,MAAA,GAAA,CAAA,CAAA,CAAA;SACA;QACA,qBAAA,KAAA,GAAA,MAAA,GAAA,CAAA,CAAA;QACA,qBAAA,WAAA,GAAA,OAAA,CAAA,WAAA,CAhBoC;QAiBpC,qBAhBe,qBAgBf,KAAA,OAAA,CAAA,qBAAA,EAAA,CAAA;QACA,qBAAA,eAAA,GAAA,qBAAA,CAAA,QAAA,CAAA;QACA,SAhBW,CAgBX,OAAA,CAAA,UAAA,EAAA,EAAA,CAAA,EAAA;YACM,qBAAN,MAAA,GAAA,eAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,CAAA,IAAA,eAAA,GAAA,CAAA,CAAA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YACA,qBAAA,qBAAA,GAAA,MAAA,GAAA,eAAA,CAAA;YAEA,OAhBe,CAgBf,WAhBe,GAgBf,WAAA,GAAA,qBAAA,CAAA,KAAA,GAAA,qBAAA,CAAA;YACA,qBAAA,CAAA,QAAA,GAAA,qBAAA,CAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,QAAA,EAAA,OAAA,EAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,UAhCU,GAgCV,UAhCU,QAAE,EAAS,OAgCrB,EAAA;QACI,qBAAJ,cAAA,KAAA,OAAA,CAAA,oBAAA,EAAA,CAAA;QACA,qBAAA,OAAA,KAAA,QAhCkC,CAgClC,OAAA,IAAA,EAAA,EAAA,CAhCwD;QAiCpD,OAAJ,CAAA,UAAA,EAAA,CAAA;QAEI,OAAJ,CAAA,YAAA,GAhCU,QAAQ,CAgClB;QACA,IAAA,EAAA,GAAA,iBAAA,CAAA,QAAA,CAAA,QAAA,CAAA,EAAW,QAAX,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,WAAA,GAAA,EAAA,CAAA,CAAA,CAhCgC,CAgChC;QACI,OAAO,CAhCC,oBAAC;YAkCb,cAAA,CAAA,MAAA,IAhCgB,cAgChB,GAAA,GAAA,GAhCuC,QAAQ,IAgC/C,QAhC2D,CAAA;QAiCvD,eAAJ,CAAA,OAhC2B,CAgC3B,eAAA,EAhC6C,OAgC7C,CAAA,oBAAA,EAAA,EAAA,CAAA,CAAA;QACI,qBAAJ,KAAA,GAAA,kBAhCqD,CAgCrD,IAAA,EAhCsD,uBAgCtD,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACI,OAhCO,CAgCX,YAAA,GAAA,IAAA,CAAA;QACA,OAAA,CAAA,oBAAA,GAAA,cAAA,CAAA;;;;;;;;;;;IAYA,0BAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,QApCmB,EAAG,OAAO,EAoC7B;QACA,IAAQ,CAAR,OAAA,CAAA,YAAA,EApC+B;YAqC/B,OAAA,CAAA,MAAA,CAAA,IApCU,CAoCV,8CAAA,CAAA,CAAA;SAEA;QACA,qBAAA,OAAA,GAAA,QAAA,CAAA,OAAA,KAAA,MAAA;YACA,EAAA,QAAA,EAAA,CAAA,EAAA,KAAA,EAAA,CAAA,EAAA,MAAA,EAAA,MAAA,EAAA;;;;;IAKA,OAAA,0BAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;AAMA,SAAA,iBAAA,CAAA,QAAA,EAAA;IACA,qBAAA,YAAA,GAAA,QAvC6C,CAAe,KAuC5D,CAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,KAAA,EAAA,EAAA,OAAA,KAAA,IAAA,UAAA,CAAA,EAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;IACA,IAAA,YAAA,EAAA;QACA,QAvCmB,GAuCnB,QAAA,CAAA,OAvC4C,CAuC5C,gBAAA,EAvC4C,EAuC5C,CAvCkE,CAuClE;KACA;IAEE,QAvCO,GAuCT,QAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,cAAA,CAAA;SACA,OAAA,CAAA,iBAAA,EAAA,cAAA,CAAA;;;;;CAKA;;;;;;;CAoBA;AA3CA,IAAA,0BAAA,IAAA,YAAA;;;;IANA,SAAA,0BAAA,CAAA,MAAA,EAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAA,CAAA;QACS,IAAT,CAAA,UAAA,GAAA,CAAA,CAAA;QACS,IAAT,CAAA,QAAA,GAAS,CAAT,CAAA;QACS,IAAT,CAAA,iBAAA,GAA2F,IAA3F,CAAA;QACS,IAAT,CAAA,YAAA,GAA+C,IAA/C,CAAA;QACA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA;QA4CA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA,WAAA,GAAA,CAAA,CAAA;;;;IA2BA,OAAA,0BAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;AAMA,SAAA,aAAA,CAtEmB,MAsEnB,EAAA;IACA,IAAA,OAAA,MAAA,IAtEmB,QAsEnB;QACA,OAAA,IAAA,CAAA;IACA,qBAtEiB,MAsEjB,GAAA,IAAA,CAAA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,UAAA,EAAA;YACA,IAAA,QAAA,CAAA,UAAA,CAAA,IAAA,UAAA,CAAA,cAAA,CAAA,QAAA,CAAA,EAAA;gBACA,qBAAA,GAAA,IAAA,UAAA,CAAA,CAAA;gBAtEA,MAAsB,GAAtB,UAAA,mBAAyC,GAAzC,CAAA,QAAkE,CAAlE,EAAA,CAAA;gBAuEA,OAAA,GAAA,CAAA,QAAA,CAAA,CAAA;aACA;SACA,CAAA,CAAA;KACG;SACH,IAAA,QAAA,CAAA,MAAA,CAAA,IAAA,MAAA,CAAA,cAAA,CAAA,QAAA,CAAA,EAAA;QACA,qBAAA,GAAA,IAAA,MAAA,CAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;AAjEA,SAAA,kBAAA,CAAA,KAAA,EAAA,MAAA,EAAA;IA8EA,qBAAA,OAAA,GAAA,IA7EU,CA6EV;IACA,IAAI,KAAJ,CAAA,cA7Ee,CA6Ef,UAAA,CAAA,EAAA;QACA,OAAA,IAAA,KAAA,CAAA,CAAA;KAEA;SACA,IAAA,OAAA,KAAA,IAAA,QAAA,EA7EoB;QA8Ed,qBAAN,QAAA,GAAA,aAAA,mBAAA,KAAA,GAAA,MAAA,CAAA,CAAA,QAAA,CAAA;QACI,OA7EO,IAAI,SA6Ef,mBAAA,KAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA;KACG;IAED,qBAAF,QAAA,IA7EqC,KA6ErC,CAAA,CAAA;IACE,qBAAF,SAAA,GAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,IA7EwD,CAAQ,UAAA,CA6EhE,EAAA,EAAA,OAAA,CA7EiE,CAAM,MA6EvE,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,EAAA,CAAA,CAAA;IACA,IAAA,SAAA,EAAA;;;;;CAKA;;;;;AAKA,SAAA,yBAAA,CAAA,OAAA,EAAA;IACA,IAAA,OAAA,EAAA;QAhFA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,CAAA;QAiFI,IAAJ,OAAA,CAhFc,QAgFd,CAAA,EAAA;YACA,OAAA,CAAA,QAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA;SACA;KACA;;QD3iBA,OAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;AA0CA,SAAA,yBAAA,CAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,cAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,WAAA,EAAA;IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAAA,IAAA,CAAA,EAAA;IAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,KAAA,CAAA,EAAA;IACA,OAAA;QACI,IAAJ,EAAA,CAAA;QACI,OAAJ,EAAA,OAAA;QACI,SAAS,EAAb,SAAa;QACb,aAAA,EAAA,aAAA;QACA,cAAA,EAAA,cAAA;;QDhDA,KAAA,EAAA,KAAA;;;;;;;;;;;;;;KAeA;;;;;IACA,qBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,OAAA,EAAA;QAIA,qBAHqB,YAGrB,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,YAAA,EAAA;YACA,IAAA,CAAA,IAAA,CAAA,MAHwB,CAGxB,OAAA,CAAA,CAAA;SACA;;;;;;;;;;;IAWA,qBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,OAAA,EAAA,YAAA,EAAA;QACA,qBAAA,oBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,qBAAA,CAAA,SAAA,CAAA,GAAA,GAAA,UAAA,OAAA,EAAA,EAAA,OAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDYA,+BAAA,CAAA,SAAA,CAAA,cA+Dc,GA/Dd,UAAA,MAAA,EAAA,WAAA,EAAA,GAAA,EAAA,cAAA,EAAA,WAAA,EAAA,OAAA,EAAA,eAAA,EAAA,MAAA,EAAA;QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAAA,EAAA,CAAA,EAAA;;QAGI,qBA+DM,OA/DV,GAAA,IAAA,wBAAA,CAAA,MA+DgD,EA/DhD,WA+DoE,EA/DpE,eA+DqE,EAAiB,MA/DtF,EAAA,EAAA,CAAA,CAAA;QACI,OAAJ,CAAA,OAAA,GAAA,OAAA,CAAA;QACA,OAAA,CAAA,eAAA,CAAA,SAAA,CAAA,CAAA,cAAA,CAAA,EA+DqC,IA/DrC,EA+DsC,OA/DtC,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;QACA,GAAA,CAAM,KA+DK,CA/DX,IAAA,EAAA,OAAA,CAAA,CAAA;;QAEA,qBAAA,SAAA,GAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA,UAAA,QAAA,EAAA,EAAA,OAAA,QAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,SAAA,CAAA,MAAA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,MAAA,EAAA;YAEA,qBAAA,EAAA,GAAA,SAAA,CAAA,SAAA,CA+DwD,MA/DxD,GA+DiE,CA/DjE,CAAA,CAAA;YAC8B,IAA9B,CAAA,EAAA,CAAA,uBAAA,EAAA,EAAA;gBACA,EAAA,CAAA,SAAA,CAAA,CAAA,WAAA,CAAA,EAAA,IAAA,EAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCA,+BAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA;QAEA,qBAAA,mBAAA,GAAA,OAAA,CAAA,eAAA,CAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;;;YAGA,qBAAA,SAAA,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;YACA,qBAAA,OAAA,GAAA,IAAA,CAAA,qBAAA,CAAA,mBAAA,EAAA,YAAA,oBAAA,YAAA,CAAA,OAAA,EAAA,CAAA;YACA,IAAA,SAAA,IAAA,OAAA,EAAA;;;;;;;;;;;;;IAaA,+BAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA;QACA,qBAAA,YAAA,GAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;IAeA,+BAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,YAAA,EAAA,OAAA,EAsCqB,OAtCrB,EAAA;QACI,qBAsCM,SAtCV,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACI,qBAAJ,YAAA,GAAA,SAAA,CAAA;;;QAIA,qBAAA,QAAA,GAAA,OAAA,CAAA,QAAA,IAAA,IAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,IAAA,CAAA;QACA,qBAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,IAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAsC2F,IAtC3F,CAAA;QACA,IAAA,QAAA,KAAA,CAAA,EAAA;YACA,YAAA,CAAA,OAAA,CAAA,UAAA,WAAA,EAAA;gBAEA,qBAAA,kBAAA,GAAA,OAAA,CAAA,2BAAA,CAAA,WAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;gBACA,YAAA;;;;;;;;;;;;;;;;;;;;;IAsBA,+BAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UA8BY,GA9BZ,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,eAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QAEA,qBA8BmB,GA9BnB,GAAA,OAAA,CAAA;QACA,qBAAA,OAAA,GAAA,GAAA,CAAA,OAAA,CAAA;QACA,IAAA,OAAA,KA8Bc,OA9Bd,CAAA,MAAA,IAAA,OAAA,CAAA,KAAA,CAAA,EAAA;YACA,GAAA,GAAA,OAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;YACA,GAAA,CAAA,wBAAA,EAAA,CAAA;YAEA,IAAQ,OAAR,CAAA,KAAA,IAAA,IAAA,EA8Bc;gBA7BN,IA8BI,GA9BZ,CAAA,YAAA,YAAA,QAAA,EAAA;oBACA,GAAA,CAAA,eAAA,CAAA,qBAAA,EAAA,CAAA;oBACA,GAAA,CAAA,YAAA,GAAA,0BAAA,CAAA;iBAEA;gBA+BU,qBA9BV,KAAA,GA8BsC,kBA9BtC,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;;aAGA;;;;;YAMA,GAAA,CAAQ,eAAR,CAAA,qBAAA,EAAA,CAAA;;;;YAKA,IAAA,GAAA,CAAA,eAAA,GAAA,eAAA,EAAA;;;;;;;;;;;IAYA,+BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,cAAA,GAAA,EAAA,CAAA;QACA,qBA0BqB,YA1BrB,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACA,qBAAA,KAAA,GAAA,GAAA,CAAA,OAAA,IAAA,GAAA,CAAA,OAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QAEA,GAAA,CAAM,KAAN,CAAA,OAAA,CA0BoB,UAAA,CA1BpB,EAAA;YACM,qBA0BqB,YA1B3B,GA0B4C,OA1B5C,CAAA,gBAAA,CAAA,GA0B0D,CAAe,OA1BzE,CAAA,CAAA;YACM,IAAN,KAAA,EAAA;gBACA,YAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;;;;YAKA,cAAA,CAAA,IACQ,CADR,YAAA,CAAA,eAAA,CAAA,CAAA;SAEA,CAAA,CAAA;;;;;;;;;;;;;IAaA,+BAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,GAsBa,EAtBb,OAAA,EAAA;QACA,IAAA,GAAA,YAAA,gBAAA,EAAA;YAsBA,qBAAA,QAAA,GAAA,OAAA,CAAA,MAAA;gBArBA,iBAAA,CAsBwB,GAtBxB,CAAA,KAsB6B,EAAS,OAAO,CAtB7C,MAAA,EAsBkD,OAtBlD,CAsBwD,MAAY,CAtBpE;gBACA,GAAA,CAAA,KAAA,CAAA,QAAA,EAAA,CAAA;YACA,OAAA,aAAA,CAAA,QAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;IAWA,+BAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,GAAc,EAAd,OAAA,EAAA;QACA,qBAAA,OAAA,GAAA,OAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;QAEI,qBAkBME,QAAA,GAAQ,OAlBlB,CAAA,eAAA,CAAA;QACI,IAAIA,OAAR,CAkBS,KAlBT,EAAA;YACM,OAAN,CAAA,aAAA,CAAA,OAAA,CAkBiC,KAlBjC,CAAA,CAkBiC;YAjBjC,QAAA,CAAA,qBAAA,EAAA,CAAA;SAkBA;QAjBA,qBAAA,QAAA,GAkBoC,GAlBpC,CAAA,KAkB6C,CAAC;QAjB9C,IAAM,QAAN,YAAA,YAAA,EAkBsBA;YAjBhB,IAAN,CAAA,cAAA,CAAA,QAAA,EAkBgB,OAlBhB,CAAA,CAAA;SACK;aAEL;YACA,OAAA,CAAA,aAAA,CAkB2B,OAlB3B,CAAA,QAAA,CAAA,CAAA;YACA,IAAA,CAAA,UAAA,mBAAA,QAAA,GAAA,OAAA,CAAA,CAAA;;;;;;;;;;;IAYA,+BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,GAAA,EAcU,OAdV,EAAA;QACA,qBAAA,QAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QACA,qBAAA,OAAA,KAAA,OAAA,CAAA,qBAAA,EAAA,CAAA;;;QAIA,IAAM,CAAN,OAAc,IAAd,QAAA,CAAA,yBAAA,EAAA,CAAA,MAAA,EAAA;YACA,QAAA,CAAA,YAAA,EAAA,CAAA;SAcA;QAbA,qBAcyB,MAdzB,GAAA,CAc8B,OAd9B,IAAA,OAcqD,CAdrD,MAAA,KAAA,GAc6D,CAAQ,MAdrE,CAcsE;QAbtE,IAAA,GAAA,CAAA,WAAA,EAAA;YAEA,QAAA,CAAA,cAc+B,CAd/B,MAAA,CAAA,CAAA;SACA;;;;;;;;;;;IAWA,+BAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,GAAA,EAAA,OAUU,EAVV;QACI,qBAUuB,qBAAA,KAV3B,OAAA,CAAA,qBAAA,EAAA,CAAA;QAEI,qBAUoB,SAVxB,GAAA,GAAA,OAAA,CAAA,eAAA,IAAA,QAAA,CAAA;QACA,qBAAA,QAAA,GAAA,qBAAA,CAAA,QAAA,CAAA;QACA,qBAAA,YAAA,GAAA,OAAA,CAAA,gBAAA,EAAA,CAAA;QACA,qBAAA,aAAA,GAUoC,YAVpC,CAUiD,eAVjD,CAUiE;QATjE,aAAA,CAAA,MAAA,GAAA,qBAAA,CAAA,MAAA,CAAA;QACA,GAUO,CAVP,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;YAIA,aAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,MAU6B,EAV7B,OAAA,CAAA,MAUwE,EAVxE,OAAA,CAAA,OAAA,CAAA,CAAA;;;;;QAMA,OAAA,CAAA,eAAA,CAAA,4BAAA,CAAA,aAAA,CAAA,CAAA;;;;;;;;;;;IAWA,+BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;;QAIA,qBAAA,SAAA,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACA,qBAAA,OAAA,KAAA,GAAA,CAAA,OAAA,IAAA,EAAA,EAMwD,CANxD;QACA,qBAAA,KAAA,GAAA,OAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QAEI,IAAJ,KAAA,KAAA,OAMQ,CANR,YAAA,YAMiC,QANjC;aACA,SAAA,IAAA,CAAA,IAMU,OAAO,CANjB,eAAA,CAAA,yBAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA;YAIA,OAAA,CAAA,eAAA,CAMgC,qBANhC,EAAA,CAAA;YACA,OAAA,CAAA,YAAA,GAAA,0BAAA,CAAA;SACA;QAEA,qBAAA,YAMkC,GANlC,SAAA,CAAA;QACA,qBAAA,IAMY,GANZ,OAAA,CAAA,WAM2B,CAAQ,GANnC,CAAA,QAAA,EAAA,GAMoD,CANpD,gBAAA,EAAA,GAM0E,CAN1E,KAAA,EAAA,GAAA,CAAA,WAAA,EAAA,OAAA,CAAA,QAAA,GAAA,IAAA,GAAA,KAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,OAAA,CAAU,iBAAV,GAAA,IAAA,CAAA,MAAA,CAAA;QACA,qBAMqB,mBAAe,GANpC,IAAA,CAAA;QACA,IAAA,CAAO,OAAP,CAAA,UAAA,OAAA,EAAA,CAAA,EAAA;YAEM,OAAN,CAAA,iBAAA,GAAA,CAAA,CAAA;YACA,qBAA2B,YAA3B,GAM8B,OAN9B,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,IAAA,KAAA,EAAA;gBAQU,YANV,CAAA,aAAA,CAAA,KAAA,CAAA,CAMgC;;;;aADhC;YAEM,GAAN,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;;;;YAKA,YAAA,CAAA,eAAA,CAAA,qBAAA,EAAA,CAAA;YACA,qBAAA,OAAA,GAAA,YAAA,CAAA,eAAA,CAAA,WAAA,CAAA;YAEQ,YAAR,GAAA,IAMS,CANT,GAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;SACA,CAAA,CAAA;QACA,OAAA,CAAA,iBAAA,GAM8B,CAN9B,CAAA;QACA,OAAA,CAAA,iBAAA,GAAA,CAAA,CAAA;QAEI,OAAO,CAMC,wBANZ,CAAA,YAAA,CAAA,CAAA;QACA,IAAA,mBAAA,EAAA;;;;;;;;;;;IAWA,+BAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,GAAA,EAAA,OAAA,EAAA;QACI,qBAAJ,aAAA,KAAA,OAEmC,CAFnC,aAAA,EAAA,CAEoC;QAAhC,qBAAJ,EAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QACI,qBAAJ,OAAA,GAAA,GAAA,CAAA,OAAA,CAAA;QACA,qBAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA;QACA,qBAEgB,OAFhB,GAAA,QAAA,IAAA,OAAA,CAAA,iBAAA,GAAA,CAAA,CAAA,CAAA;QACA,qBAAA,KAAA,GAAA,QAAA,GAAA,OAAA,CAAA,iBAAA,CAAA;QACA,qBAAA,kBAAA,GAAA,OAAA,CAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,OAAA,CAAA,MAAA,CAAA;QACA,QAAQ,kBAAR;YACA,KAAA,SAAA;gBACA,KAAA,GAAA,OAAA,GAAA,KAAA,CAAA;gBAEA,MAAA;YACQ,KAEC,MAFT;gBACA,KAEe,GAFf,aAAA,CAAA,kBAAA,CAAA;gBACA,MAAA;SAEA;QACI,qBAAJ,QAAA,GAEsC,OAFtC,CAAA,eAAA,CAAA;QACI,IAAJ,KAAA,EAAA;;;;;QAMI,OAAJ,CAAA,YAAA,GAAA,GAAA,CAAA;;;;;QAUA,aAAA,CAAA,kBAAA;;;;;;;;;;;;;;;IAgBA,SAAA,wBAAA,CAAA,OAAA,EAAa,OAAb,EAAa,eAAb,EAAA,MAAA,EAAA,SAAA,EAAA,eAAA,EAAA;QAbS,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QAES,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QACS,IAAT,CAAA,eAA6B,GAA7B,eAAA,CAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAS,CAAT;QACS,IAAT,CAAA,SAAA,GAAqC,SAArC,CAAA;QACS,IAAT,CAAA,aAAA,GAAA,IAAqC,CAAA;QAC5B,IAAT,CAAA,qBAAqC,GAArC,IAAA,CAAA;QACS,IAAT,CAAA,YAAA,GAAA,0BAAA,CAAA;QAaI,IAAI,CAAC,eAAe,GAAG,CAA3B,CAAA;QACI,IAAJ,CAAA,OAAA,GAAmB,EAAnB,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,CAAA,CAAA;;;;QALG,SASH,CAAA,IAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CATsC;;;;;;;;;;;;;;;;QAgDtC,IAAA,KAAA,GAAA,IAAA,CAAA;QA1BI,IAAI,CAAR,OAAA;YACM,OAAN;QACA,qBAAA,UAAA,IAAA,OAAA,CAAA,CAAA;QAEI,qBAAJ,eAAA,GAAA,IAAA,CAAA,OAAA,CAAA;;QAEA,IAAA,UAAA,CAAA,QAAA,IAAA,IAAA,EAAA;YAEA,EAAA,eAAA,GAAA,QAbU,GAAY,kBAAkB,CAaxC,UAAA,CAAA,QAAA,CAAA,CAAA;SACA;QACA,IAAM,UAAN,CAAA,KAAA,IAAA,IAAA,EAAA;YACM,eAAN,CAAA,KAb2B,GAa3B,kBAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;SACA;QACA,qBAAA,SAAA,GAAA,UAAA,CAAA,MAAA,CAAA;QAEA,IAAM,SAAN,EAbc;YAcd,qBAbc,gBAad,KAb+B,eAAe,CAAc,MAa5D,EAAA,CAAA;YACA,IAAA,CAAA,gBAAA,EAAA;gBACA,gBAAA,GAAA,IAAA,CAAA,OAAA,CAAA,MAAA,GAAA,EAAA,CAAA;aACO;YACP,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBACA,IAAA,CAAA,YAAA,IAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;;;;SAXA;KAgBA,CAAA;;;;IAIA,wBAAA,CAAA,SAAA,CAAA,YAAQ,GAAR,YAAA;QACA,qBAAA,OAAA,GAfiC,EAejC,CAAA;QACA,IAAA,IAAA,CAAA,OAAA,EAAA;YACA,qBAAA,WAAA,GAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA;YACA,IAAA,WAAA,EAAA;gBACA,qBAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;IAgBA,wBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,OAAA,EAAA,OAAA,EApB0C,OAoB1C,EAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,IAAA,CAAA,EAAA;QACI,qBAAJ,MApB2B,GAoB3B,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA;QAEI,qBAAJ,OAAA,GApBqC,IAoBrC,wBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,IAAA,CAAA,eAAA,EAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,IAAA,CAAA,CAAA,CAAA,CAAA;QACI,OAAO,CApBC,YAoBZ,GAAA,IAAA,CApBgC,YAoBhC,CAAA;QACI,OAAO,CApBC,qBAAqB,GAoBjC,IAAA,CAAA,qBAAA,CAAA;QACI,OAAJ,CAAA,OAAA,GAAA,IApByB,CAAE,YAoB3B,EAAA,CAAA;QACI,OApBO,CAoBX,aAAA,CAAA,OAAA,CAAA,CAAA;QACA,OAAA,CAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,wBAAA,CAAA,SAAA,CAAA,wBAAA,GAAA,UAAA,OAAA,EAAA;;;;;;;;;;;;IAcA,wBAAA,CAAA,SAAA,CAAA,2BAAA,GAAA,UAAA,WAAA,EAAA,QAAA,EAAA,KAAA,EAAA;QACI,qBA5BM,cAAc,GA4BxB;YAGQ,QAAR,EA5BU,QA4BV,IAAA,IAAA,GAAA,QAAA,GAAA,WAAA,CAAA,QAAA;YACA,KAAA,EAAA,IAAA,CAAA,eAAA,CAAA,WAAA,IAAA,KAAA,IAAA,IAAA,GAAA,KAAA,GAAA,CAAA,CAAA,GAAA,WAAA,CAAA,KAAA;YACA,MAAA,EAAA,EAAA;;;;;KAKA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,wBAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,KAAA,EAAA;;;;;;;;;;;;;;;IAiBA,wBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,QAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,WAAA,EAAA,QAAA,EAAA,MAAA,EAAA;QACA,qBAAA,OAAA,GAAA,EAAA,CAAA;QACA,IAAM,WAAN,EA1CmB;YA2CnB,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;SAEA;QACA,IAAM,QAAN,CAAA,MAAA,GAAA,CAAA,EAAA;YAEA,qBAAA,KAAA,GAAA,KAAA,IAAA,CAAA,CAAA;YACA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA,CAAA;SACA;QACA,IAAA,CAAA,QAAA,IAAA,OAAA,CAAA,MAAA,IAAA,CAAA,EAAA;YAEA,MAAA,CAAA,IAAA,CAAA,WAAA,GAAA,gBAAA,GAAA,6CAAA,GAAA,gBAAA,GAAA,sDAAA,CAAA,CAAA;;;;;;;;;;;;IAtCA,SAAA,eAAA,CAAA,OAAA,EAAA,SAAA,EAA6C,4BAA7C,EAAA;QACU,IAAV,CAAA,OAAA,GAAA,OAAA,CAAA;QACU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAkD;QACxC,IAAV,CAAA,4BAAA,GAAA,4BAAA,CAAA;QAGU,IAAV,CAAA,QAAA,GAAA,CAAA,CAAA;QACU,IAAV,CAAA,iBAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,gBAAA,GAAA,EAAA,CAAA;QAkFI,IAAI,CAAC,UAAT,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,IAAM,CAAN,aAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,cAAA,GAAA,EAAA,CAAA;QAEI,IAAI,CAAC,SAAT,GAAA,EAAA,CAAA;QACI,IAAI,CAAC,yBAAT,GAAqC,IAArC,CAAA;QACI,IAAI,CAAC,IAAI,CAAC,4BAAd,EAAA;YACM,IAAI,CAAC,4BAA4B,GAAvC,IAAA,GAAA,EAAA,CAAA;SACA;QACA,IAAA,CAAA,oBAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA;QACI,IAAI,CAAC,qBAAT,GAAA,IAAA,CAAA,4BAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,CAAA,IAAA,CAAA,qBAAA,EAAA;;;;QAIA,IAAA,CAAA,aAAA,EAAA,CAAA;KACA;;;;IAIA,eAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA;QACA,QAAA,IAAA,CAAA,UAAA,CAAA,IAAA;YACA,KAAA,CAAA;gBACA,OAAA,KAAA,CAAA;YACA,KAAA,CAAA;;;;SAIA;;;;;;IASA,MAAA,CAAA,cAAA,CAAA,eAAA,CAAA,SAAA,EAAA,aAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,QAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,KAAA,EAAA;;;;;QAMA,qBAAA,eAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,IAAA,CAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,MAAA,CAAA;QACA,IAAA,IAAA,CAAA,QAAA,IAAA,eAAA,EAAA;YAtFA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA;YAuFM,IAAI,eAAV,EAAA;gBACA,IAAA,CAAA,qBAAA,EAAA,CAAA;aACA;;;;;;;;;;;;;QA9EA,OAAA,IAAA,eAAA,CAAA,OAAA,EAAA,WAAA,IAAA,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,4BAAA,CAAA,CAAA;KA6FA,CAAA;;;;IAIA,eAAA,CAAA,SAAA,CAAA,aA5FU,GA4FV,YAAA;QACA,IAAM,IAAI,CA5FC,gBAAC,EA4FZ;YACM,IAAI,CA5FC,iBA4FX,GA5F+B,IA4F/B,CAAA,gBAAA,CAAA;SACK;QACL,IAAA,CAAA,gBAAA,KAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA;;;;SAIA;KACA,CAAA;;;;;;;KAOA,CAAA;;;;;;;;;;;;;;;;;;QAkBA,IAAA,CAAA,aAAA,CAvGG,IAuGH,CAvG8B,GAuG9B,EAAA,IAAA,EAvGqC,IAuGrC,CAAA,WAAA,EAAA,KAAA,EAAA,KAAA,EAAA,CAvG2C;;;;;IA4G3C,eAAA,CAAA,SAAA,CAAA,uBAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,yBAAA,KAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;QAgBA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;;;;;;;;;;IAgBA,eAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,MAAA,EAAA,MAAA,EAhHU,OAgHV,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAgBA;QAfI,IAAJ,MAAA,EAhHgB;YAiHV,IAAN,CAAA,iBAAA,CAhHY,QAgHZ,CAAA,GAAA,MAAA,CAAA;SACA;QACA,qBAAA,MAAA,GAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,KAAA,EAAA,CAAA;QACA,qBAhHuB,MAgHvB,GAAA,aAAA,CAAA,KAAA,EAAA,IAhHqC,CAAqB,qBAgH1D,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAY,MAAZ,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;YACA,qBAAA,GAAA,GAAA,iBAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA;YACA,KAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA;YACM,IAAI,CAhHC,KAgHX,CAAA,oBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;gBACA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,qBAAA,CAAA,cAAA,CAAA,IAAA,CAAA;oBACA,KAAA,CAAA,qBAAA,CAAA,IAAA,CAAA;;;;SAIA,CAAA,CAAA;KACA,CAAA;;;;IAIA,eAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAiBA;QAfI,qBAAJ,MAAA,GAAA,IAAA,CAAA,cAAA,CAAA;QACA,qBAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;QACA,IAAM,KAlHK,CAkHX,MAAA,IAAA,CAAA;YACA,OAAA;QAEI,IAAJ,CAAA,cAAA,GAAA,EAAA,CAAA;QACA,KAAA,CAAA,OAlHY,CAAI,UAAA,IAkHhB,EAAA;YACA,qBAAA,GAlH8B,GAkH9B,MAAA,CAAA,IAAA,CAAA,CAAA;YACA,KAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA;SACK,CAlHC,CAAC;QAmHP,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;SAIA,CAAA,CAAA;KACA,CAAA;;;;IAIA,eAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;SAIA,CAAA,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,UAxHoC,CAwHpC,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAI,eAAJ,CAAA,SAAA,EAAA,YAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;YACA,qBAAA,UAAA,GAAA,EAAA,CAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,4BAAA,GAAA,UA3Hc,QA2Hd,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAQA;QAPA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;YACA,qBAAA,QAAA,GAAA,KAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;YACA,qBAAA,QAAA,GAAA,QAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;;;;SAIA,CAAA,CAAA;KACA,CAAA;;;;IAKA,eAAA,CAAA,SAAA,CAAA,cAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACI,IAAI,CA7HC,qBA6HT,EAAA,CAAA;QACA,qBAAA,aAAA,GAAA,IA7H4B,GA6H5B,EAAA,CAAA;QACA,qBAAA,cAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IA7HoC,KAAK,CAAC,IA6H1C,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA;QACA,qBA7HqBD,cA6HrB,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAuB,QAAvB,EA7HiC,IA6HjC,EAAA;YACA,qBAAA,aAAA,GAAA,UAAA,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;YA7HA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,OAAwC,CAAxC,UAAA,IAAA,EAAA;gBA8HA,qBA7H6B,KAAK,GA6HlC,aAAA,CAAA,IAAA,CAAA,CAAA;gBACA,IAAA,KAAA,IAAAF,8BAAA,EAAA;oBACA,aAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;iBA5HW;qBA8HX,IAAA,KA7HsB,IA6HtBC,8BAAA,EA7HkC;oBA8HlC,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;iBACA;aACA,CAAA,CAAA;YAEA,IAAA,CAAA,OAAA,EAAA;gBACA,aAAA,CAAA,QAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA,QA7HgD,CA6HhD;;YAGQ,cAAR,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;SACA,CAAA,CAAA;QACA,qBAAA,QAAA,GAAA,aAAA,CAAA,IAAA,GAAA,eAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,GAAA,EAAA,CAAA;QACA,qBAAA,SAAA,GAAA,cAAA,CAAA,IAAA,GAAA,eAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,GAAA,EAAA,CAAA;;QAEA,IAAM,OAAN,EAAA;YACA,qBAAA,GAAA,GAAA,cAAA,CAAA,CAAA,CAAA,CAAA;YAEA,qBAAA,GAAA,GACQ,OADR,CAAA,GAAA,CA5Hc,CA4Hd;YAGA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA;YACA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA;YAEA,cAAA,GAAA,CAAA,GA7HC,EA6HD,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;;IAxHA,SAAA,kBAAA,CAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,cAAA,EAAA,OAAA,EAAA,wBAAA,EAAA;QAAA,IAAA,wBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,wBAAA,GAAA,KAAA,CAAA,EAAA;QAAA,IAAA,KAAA,GACc,MADd,CAAA,IAAA,CAAA,IAAA,EACA,OAAA,EAAA,OAAA,CAAA,KAAA,CAAc,IADd,IAAA,CAAA;QAwKI,KAAI,CAAC,OAAO,GAAG,OAAnB,CAAA;QACA,KAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;QAIA,KAAA,CAAA,OAAA,GAAA,EAAA,QAvKwC,EAuKxC,OAAA,CAAA,QAAA,EAAA,KAvKgE,EAuKhE,OAAA,CAAA,KAAA,EAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,CAAA;;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAzKQ,SAyKR,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA,CAAA;;;;IAIA,kBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,YAAA;QACA,qBAAA,SAAA,GAAA,IAAA,CAzKY,SAyKZ,CAAA;;QAGA,IAAM,IAAN,CAAA,wBAAA,IAAA,KAAA,EAAA;YACM,qBAAN,YAAA,GAAA,EAAA,CAAA;YACM,qBAAN,SAAA,GAzKyC,QAyKzC,GAAA,KAAA,CAAA;YAEM,qBAzKM,WAyKZ,GAAA,KAzK+B,GAyK/B,SAAA,CAAA;;YAEM,qBAAN,gBAAA,GAAA,UAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;YAuBA,qBAAA,KAAA,GAzKmC,SAyKnC,CAAA,MAAA,GAAA,CAAA,CAAA;YACA,KAAA,qBAzK8B,CAyK9B,GAAA,CAAA,EAAA,CAAA,IAAA,KAAA,EAAA,CAAA,EAAA,EAAA;gBACA,qBAAA,EAAA,GAAA,UAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;;gBAGA,qBAAA,cAAA,GAAA,KAAA,GAAA,SAAA,GAAA,QAAA,CAAA;gBACA,EAAA,CAAA,QAAA,CAAA,GAAA,WAAA,CAAA,cAAA,GAAA,SAAA,CAAA,CAAA;gBACA,YAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;aAEA;;YAGA,QAAA,GAAA,SAAA,CAAA;YAGA,KAAA,GAAA,CAAA,CAAA;YACA,MAAA,GAAA,EAAA,CAAA;YAEA,SAAA,GAAA,YAAA,CAAA;;;;;;;;;;;;;;;;;;;;;AAkCA,SAAA,aAAA,CAAA,KAjMsB,EAiMtB,SAAA,EAAA;IACA,qBAAA,MAjMqB,GAiMrB,EAjM6B,CAiM7B;IACA,qBAAA,aAAA,CAAA;IAjMA,KAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;QAkMA,IAAM,KAAN,KAAgB,GAAhB,EAAA;YACA,aAAA,GAAA,aAAA,IAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;YACA,aAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,MAAA,CAAA,IAAA,CAAA,GAAAA,8BAAA,CAAA,EAAA,CAAA,CAAA;SACA;aACA;;SDjjCA;;;;;;;;;;;AAwBA,IAAA,SAAA,IAAA,YAAA;;;;;IAIA,SAAA,SAAA,CAAA,OAAA,EAAA,KAAwB,EAAxB;QACA,IAAM,CAAN,OAAA,GAAA,OAAA,CAAA;QACA,IAAA,MAAA,GAAA,EAAA,CAAA;QACI,IAAJ,GAAA,GAAA,iBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;QACA,IAAA,MAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;IAmBA,SAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,OAAA,EAAA,cAAA,EAAA,iBAAA,EAXmB,OAWnB,EAAA,eAV+B,EAU/B;QAEI,qBAAJ,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,cAAA,CAAA,GAAA,eAAA,CAAA,cAAA,CAAA,IAAA,cAAA,CAAA,CAAA;QACA,qBAAA,IAXY,GAWZ,KAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,eAXiE,CAWjE,iBAAA,CAAA,IAAA,iBAAA,CAAA,CAAA;QACA,qBAAA,MAAA,GAXuB,EAAa,CAWpC;QACA,eAAA,GAAA,eAAA,IAAA,IAAA,qBAAA,EAAA,CAAA;QACI,qBAAJ,MAAA,GAAA,uBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAAA,CAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,OAAA,EAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QACA,IAAA,MAAA,CAAA,MAAA,EAAA;YACA,qBAAA,YAAA,GAAA,8BAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;SD3DA;;;;CCwBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;IDHA,IAAA,wBAAA,IAAA,YAAA;IAAA,SAAA,wBAAA,GAAA;KACA;IAAA,OAAA,wBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;AAOA,IAAA,4BAAA,IAAA,YAAA;IAAA,SAAA,4BAAA,GAAA;;;ID7BA,4BAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,oBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,MAAA,EAAA;;;;CC6BA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDMA,4BAAA,CAAA,SAAA,CAAA,mBAXe,GAWf,UAAA,oBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,MAAA,EAAA;QACA,qBAAA,IAAA,GAAA,EAAA,CAAA;QAXA,qBAAA,MAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA,IAAA,EAAA,CAAA;QAYA,IAAA,oBAAA,CAAA,kBAAA,CAAA,IAXc,KAWd,KAAA,CAAA,IAX8C,KAW9C,KAAA,GAAA,EAAA;YACA,IAAQ,OAAR,KAAA,KAAA,QAXiC,EAWjC;gBACA,IAAU,GAAV,IAXiB,CAWjB;aACA;iBACA;gBACA,qBAAA,iBAAA,GAAA,KAAA,CAAA,KAAA,CAAA,wBAAA,CAAA,CAAA;gBACA,IAAA,iBAAA,IAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,MAAA,IAAA,CAAA,EAAA;oBACA,MAAA,CAAA,IAAA,CAAA,sCAAA,GAAA,oBAAA,GAAA,GAAA,GAAA,KAAA,CAAA,CAAA;iBACA;aAEA;SAEA;;;;;;KAKA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;;;;;;IDtDA,qBAAA,GAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,SAAA,2BAAA,CAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,eAAA,EAAA,aAAA,EAAA,cAAA,EAAA;IACA,OAAA;QACI,IAAJ,EAAA,CAAA;QACI,OAAO,EAAX,OAAW;QACP,WAAJ,EAAA,WAAA;QACI,mBAAJ,EAAA,mBAAA;QACI,SAAJ,EAAA,SAAA;QACI,UAAJ,EAAA,UAAA;QACI,OAAJ,EAAA,OAAA;QACA,QAAA,EAAA,QAAA;QACA,SAAA,EAAA,SAAA;;QD3DA,aAAA,EAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwDA,0BAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,SAAA,EAAA,OAAA,EAAA,eAAA,EAAA;QACI,qBAlBM,gBAkBV,GAAA,qBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAjByB,IAAM,EAiB/B,EAAA,OAAA,IAAA,EAAA,CAAA,CAAA;QAII,qBAAJ,iBAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA;QACA,qBAAA,kBAAA,GAAA,IAAA,CAAA,YAAA,CAAA,YAAA,CAAA,IAlB2D,iBAkB3D,CAAA;QACA,qBAAA,eAAA,GAAA,IAAA,CAAA,YAAA,CAAA,SAAA,CAAA,IAAA,iBAAA,CAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;QAEI,qBAlBM,SAkBV,GAAA,uBAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA,GAAA,CAAA,SAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QACI,IAAJ,MAAA,CAAA,MAAA,EAAA;YACA,qBAAA,YAAA,GAAA,8BAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YACA,MAlBc,IAkBd,KAAA,CAlBuB,YAkBvB,CAAA,CAAA;SACA;QACA,qBAAA,WAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,qBAAA,YAAA,GAAA,IAAA,GAlBuC,EAkBvC,CAAA;QAEA,qBAAA,eAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,SAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;YAEM,qBAAN,GAAA,GAAA,EAAA,CAAA,OAAA,CAAA;YACA,qBAlByB,QAkBzB,GAAA,eAAA,CAAA,WAAA,EAAA,GAAA,EAAA,EAAA,CAAA,CAAA;YACA,EAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,QAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,CAAA;YACA,qBAAA,SAAA,GAAA,eAAA,CAAA,YAAA,EAAA,GAAA,EAAA,EAAA,CAAA,CAAA;YAEA,EAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,SAAA,CAAA,IAAA,CAAA,GAAA,IAlBgC,CAkBhC,EAlBgC,CAAgB,CAkBhD;YACA,IAAA,GAAA,KAAA,OAAA,EAAA;gBAIA,eAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;aACA;SAEA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDhEA,OAAA,IAAA,gBAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAAA;;;;;AAQA,IAAA,gBAAA,IAAA,YAAA;;;;;IAEA,SAAA,gBAAA,CAAA,IAAA,EAAA,GAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAM,CAAN,IAAA,GAAA,IAAkB,CAAlB;QACA,IAAM,CAAN,GAAU,GAAV,GAAgB,CAAhB;QACA,IAAA,CAAA,mBAAA,GAAA,EAA6B,CAA7B;QACA,IAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QACA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;YACA,IAAA,GAAA,GAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA;YACA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,UAAA,EAAA;gBAEA,IAAA,OAAA,UAAiC,IAAjC,QAAA,EAA+C;oBAC/C,UAA0B,CAAC,UAA3B,EAAA,KAA4C,EAA5C,GAAiD,CAAjD,CAAA;iBAEA;aACA,CAAA,CAAA;SACK,CAAC,CAAC;QAEH,iBAAJ,CAAA,IAAA,CAA2B,MAA3B,EAAA,MAAA,EAAA,GAAA,CAAA,CAAA;QACA,iBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,GAAA,CAAA,CAAA;;;;QAJG,IAQH,CAAA,kBAAA,GAAA,wBARuD,CAAE,IAQzD,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;IAYA,gBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,YAAA,EAAA,SAAA,EAAA;;;;;CAnCA,EAAA,CAAA,CAAA;;;;;;AA2DA,SAAA,wBAAA,CAAA,WAAA,EAAA,MAAA,EAAA;;;;;;;;;;;;AAYA,SAAA,iBAAA,CAAA,GAAA,EAAA,IAAA,EAAA,IAAA,EAAA;IAnCA,IAAA,GAAA,CAAA,cAAA,CAAA,IAAkB,CAAc,EAAhC;QAoCI,IAnCI,CAmCR,GAnCS,CAAI,cAmCb,CAAA,IAAA,CAAA,EAAA;YACA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;SACA;;SD5GA,IAAA,GAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;;;;;;;;;;;;AA6BA,IAAA,uBAAA,IAAA,YAAA;;;;;IALA,SAAA,uBAAA,CAAA,OAAsC,EAAA,WAAtC,EAAA;QAEA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;;;;;;;;;;;IAgBA,uBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,EAAA,EAAA,QAAA,EAAA;QARA,qBAAA,MAAA,GAAA,EAAA,CAAA;QASA,qBARwB,GAQxB,GAAA,iBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;QACA,IAAA,MAAA,CAAA,MAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAA,6DAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;IA4BA,uBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EAAA,EAlBS,OAkBT,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAqCA;QArCA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;QAEA,qBAAA,GAAA,GAlB4B,IAkB5B,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;QACA,qBAAA,YAAA,CAAA;QACA,qBAAA,aAlBoC,GAkBpC,IAAA,GAAA,EAAA,CAAA;QACA,IAAA,GAlBS,EAkBT;YACA,YAAA,GAAA,uBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;YAlBA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBAmBA,qBAAA,MAAA,GAAA,eAAA,CAAA,aAAA,EAAA,IAAA,CAAA,OAAA,EAAA,EAlByF,CAAC,CAkB1F;gBACA,IAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,MAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,CAAA;aACA,CAAA,CAAA;SAEA;aACA;YAEA,MAAA,CAAA,IAAA,CAAA,sEAAA,CAAA,CAAA;YAEA,YAAA,GAAA,EAlBmB,CAAO;SAmB1B;QAEA,IAAA,MAAA,CAAA,MAAA,EAAA;YAEA,MAAA,IAAA,KAAA,CAAA,8DAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;SACA;QACA,aAAA,CAAA,OAAA,CAAA,UAAA,MAlB+B,EAkB/B,OAAA,EAAA;YACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,MAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA,EAAAA,8BAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SACA,CAAA,CAAA;QACI,qBAAJ,OAlB4B,GAkB5B,YAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA;YACA,qBAlB2B,MAkB3B,GAlBwC,aAkBxC,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;YAEQ,OAAR,KAAA,CAAA,YAAA,CAAA,CAAA,EAAA,EAAA,EAAA,MAAA,CAAA,CAAA;SACA,CAAA,CAAA;QACA,qBAAA,MAAA,GAAA,mBAAA,CAAA,OAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,uBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,EAAR,EAAA;QACA,qBArBoB,MAAO,GAAM,IAqBjC,CAAA,UAAA,CAAA,EAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,EAAA,CAAA;QACA,OAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;;;;KAnBA,CAAA;;;;;IA6BA,uBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAxBW,EAwBX,EAAA;QACA,qBAAA,MAAA,GAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;IAcA,uBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;IAcA,uBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,EAAR,EAAA,OApCmB,EAoCnB,OAAA,EAAA,IAAA,EAAA;QACA,IAAM,OAAN,IAAA,UAAA,EApCY;YAqCN,IAAI,CApCC,QAoCX,CApCmB,EAAG,oBAoCtB,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,OAAO;SACR;QAED,IAAJ,OAAA,IAAA,QApCU,EAoCV;YACA,qBAAA,OAAA,KAAA,IAAA,CAAA,CAAA,CAAA,IAAA,EAAA,EAAA,CAAA;YACM,IAAN,CApCW,MAAA,CAoCX,EAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,OAAA;SACA;QACA,qBAAA,MAAA,GAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,CAAA;QACA,QAAQ,OApCO;YAqCf,KAAA,MAAA;gBACA,MAAA,CAAA,IAAA,EAAA,CAAA;gBACQ,MAAM;YACd,KAAA,OAAA;gBACA,MAAA,CAAA,KAAA,EAAA,CAAA;gBACQ,MAAM;YACd,KAAA,OAAA;gBACA,MAAA,CAAA,KAAA,EAAA,CAAA;gBACQ,MAAM;YACd,KAAA,SAAA;gBACA,MAAA,CApCW,OAoCX,EAAA,CAAA;gBACQ,MAAM;YACd,KAAA,QAAA;gBACA,MAAA,CAAA,MAAA,EAAA,CAAA;gBACQ,MAAM;YACd,KAAA,MAAA;gBACA,MAAA,CAAA,IAAA,EAAA,CAAA;gBACQ,MAAR;YACA,KAAA,aAAA;gBACA,MAAA,CAAA,WAAA,CAAA,UAAA,mBAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;gBACA,MAAA;YACA,KAAA,SAAA;;gBD/LA,MAAA;;;;CC6BA,EAAA,CAAA,CAAA;;;;;;;;ADHA,IAAA,kBAAsB,GAAtB,EAAA,CAAA;AACA,IAAA,kBAAA,GAAA;IACA,WAAA,EAAA,EAAA;IACE,aADa,EAAA,IACf;IACE,YAAF,EAAA,KADiB;IAEf,oBAAF,EAAA,KAAA;CACA,CAAA;AACA,IAAA,0BAAA,GAAA;IAkBA,WAAA,EAAA,EAAA;IAQA,aAAA,EAAA,IAAA;;;;AAMA,IAAA,YAAA,GAFqB,cAErB,CAAA;AACA,IAAA,UAAA,IAAA,YAAA;;;;IAIA,SAAA,UAAA,CAAA,KAAA,EAAA;QACA,IAAA,KAAa,GAAb,KAAqB,IAArB,KAA8B,CAA9B,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,KAAA,GAAA,KAAqB,GAArB,KAAiD,CAAjD,OAAA,CAAA,GAAA,KAAA,CAAA;QACA,IAAA,CAAA,KAAA,GAAA,qBAAA,CAAA,KAAA,CAAA,CAAA;QAAA,IAAA,KAAA,EAAA;YACM,IAAN,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA,CAAA;YACA,OAAA,OAAA,CAAA,OAAA,CAAA,CAAA;YACQ,IAAR,CAAa,OAAb,GAAA,OAA8B,CAA9B;SACA;aACA;YACA,IAAA,CAAA,OAAA,GAAA,EAAA,CAAA;;;;;KAKA;;;;;IAKA,UAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,OAAA,EAAA;QACA,qBAAA,SAAA,GAAA,OAAA,CAAA,MAAA,CAAA;QACA,IAAA,SAAA,EAAA;YACA,qBAAA,WAAA,KAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBACA,IAAA,WAAA,CAAA,IAAA,CAAA,IAAA,IAAA,EAAA;oBACA,WAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;iBAEA;aASA,CAAA,CAAA;SACA;KACA,CAAA;;CA1CA,EAAA,CAAA,CAAA;;;;;;;;;;IAiCA,SAAA,4BAAA,CAAA,EAAA,EAAA,WAAA,EAAA,OAAA,EAAA;QACU,IAAV,CAAA,EAAA,GAAA,EAAA,CAAA;QAEU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QAqBI,IAAI,CAAC,OAAT,GAAA,OAA0B,CAA1B;QACI,IAAJ,CAAA,OAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;IAeA,4BAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,OArBY,EAqBZ,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;YAEQ,MAAR,IAAA,KAAA,CAAA,oDAAA,GAAA,KAAA,GAAA,qCAAA,GAAA,IAAA,GAAA,mBAAA,CAAA,CAAA;SACA;QAEA,IAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;YAEA,MAAA,IAAA,KAAA,CAAA,8CAAA,GAAA,IAAA,GAAA,6CAAA,CAAA,CAAA;SACA;QACI,IAAJ,CAAA,mBAAA,CAAA,KAAA,CAAA,EAAA;YAEA,MAAA,IAAA,KAAA,CAAA,yCAAA,GAAA,KAAA,GAAA,iCAAA,GAAA,IArByF,GAqBzF,sBAAA,CAAA,CAAA;SACA;QACA,qBAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;QACA,qBAAA,IAAA,GAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EArB+C,QAqB/C,EAAA,QAAA,EArBqD,CAAK;QAsB1D,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,qBAAA,kBAAA,GAAA,eAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,kBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;;;;SAIA;QACA,OAAA,YAAA;;;;YAKA,KAAQ,CAAR,OAAA,CArBc,UAAK,CAAS,YAqB5B;gBACA,qBAAA,KAAA,GAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;gBACA,IAAA,KAAA,IAAA,CAAA,EAAA;oBACA,SAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;iBACA;gBACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA;;;;;;;;;;;IAfA,4BAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAW,IAAX,EAAA,GAAA,EAAA;QA0BA,IAAM,IAAI,CAzBC,SAAC,CAAS,IAAC,CAAI,EAyB1B;;YAEA,OAAA,KAAA,CAAA;SACA;;;;;KAvBA,CAAA;;;;;IAiCA,4BAAA,CAAA,SAAA,CAAA,WA5BW,GA4BX,UAAA,IAAA,EAAA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;IAeA,4BAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAM,OAAN,EAlCe,WAkCf,EAAA,KAAA,EAAA,iBAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CA2EA;QA3EA,IAAA,iBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,iBAAA,GAAA,IAAA,CAAA,EAAA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA,WAAA,CAAA,WAAA,CAlCqD,CAAY;QAmCjE,qBAAA,MAAA,GAlCoB,IAAgB,yBAkCpC,CAAA,IAAA,CAlCqE,EAAA,EAAI,WAkCzE,EAAA,OAAA,CAAA,CAAA;QACA,qBAAA,kBAAA,GAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,kBAAA,EAAA;YACA,QAAA,CAAA,OAAA,EAAA,oBAAA,CAAA,CAAA;YAEA,QAAA,CAAA,OAAA,EAAA,oBAAA,GAlC2B,GAkC3B,GAAA,WAAA,CAAA,CAAA;YACQ,IAAR,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,EAAA,kBAAA,GAAA,EAAA,CAAA,CAAA;SACA;QACA,qBAAA,SAAA,GAAA,kBAAA,CAAA,WAAA,CAAA,CAAA;QAEI,qBAAJ,OAlCwB,GAkCxB,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA;QAEI,qBAAJ,KAAA,GAAA,KAAA,IAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAM,CAAN,KAAA,IAAA,SAAA,EAAA;YACA,OAAA,CAAA,aAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA;SAlCA;QAmCA,kBAlCoB,CAkCpB,WAAA,CAAA,GAAA,OAAA,CAAA;QACA,IAAA,CAAA,SAAA,EAAA;YAEA,SAAA,GAAA,mBAAA,CAAA;SAEA;;;;;QAKA,gBAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;;;;;YAMA,IAAA,MAAA,CAAA,WAAA,IAAA,KAAA,CAAA,EAAA,IAlCQ,MAkCR,CAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,MAAA,EAAA;gBACA,MAAA,CAlCU,OAkCV,EAAA,CAAA;aACA;SAlCA,CAAA,CAAA;QAmCA,qBAAA,UAAA,GAAA,OAAA,CAAA,eAAA,CAAA,SAAA,CAAA,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;QACA,qBAAA,oBAAA,GAAA,KAAA,CAAA;QACA,IAAA,CAAA,UAAA,EAAA;YAEQ,IAAR,CAAA,iBAAA;gBACA,OAAA;YAGQ,UAAR,GAAA,OAAA,CAlCU,kBAkCV,CAAA;YACM,oBAAN,GAAA,IAAA,CAAA;SACK;QAED,IAAJ,CAAA,OAAA,CAlCY,kBAkCZ,EAAA,CAAA;QACA,IAAM,CAAN,MAAA,CAAA,IAlCkB,CAkClB,EAAA,OAAA,EAAA,OAAA,EAAA,WAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,CAAA,CAAA;QAEA,IAAM,CAAN,oBAlCU,EAkCV;YACM,QAAN,CAlCW,OAAW,EAkCtB,sBAAA,CAAA,CAAA;SACA;QACA,MAAA,CAAA,MAAA,CAAA,YAAA;YAEM,WAAN,CAAA,OAAA,EAAA,sBAAA,CAAA,CAlC4B;YAmCtB,qBAAN,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;YACA,IAAQ,KAAR,IAAA,CAAA,EAAA;gBACQ,KAAI,CAAZ,OAAA,CAlCqB,MAkCrB,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;aACA;YACA,qBAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,IAAA,OAAA,EAAA;gBACA,qBAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;gBAEA,IAlCU,OAAQ,IAkClB,CAAA,EAlCuB;oBAmCvB,OAAA,CAAA,MAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;iBAEA;aACA;;;;;KAKA,CAAA;;;;;IAQA,4BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAMA;QALA,OAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,4BAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAM,CAAN,OAAA,CAAA,eAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QACA,qBAAA,cAAA,GAAA,IAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;IAaA,4BAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,KAAA,CAAA,EAAA;QACA,IAAA,CAAA,OAAU,CAAV,MAAiB,CA7CC,KA6ClB,CAAA,WAAA,EAAA,mBAAA,EAAA,IAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;YACA,IAAA,OAAA,IAAA,aAAA,CAAA,GAAA,EAAA,KAAA,CAAA,cAAA,CAAA,EAAA;gBAEQ,qBAAR,OA7CsC,GA6CtC,KAAA,CAAA,OAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;;gBA5CA,IAAA,OAAA,EAAA;oBA8CY,OAAZ,CAAA,UA7Cc,CAAiB,GAAC,EAAI,OA6CpC,EAAA,IAAA,CAAA,CAAA;iBACA;gBACA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;aACA;;;;;;;;;;;;IAcA,4BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,OAAA,EAAA,OAlDU,EAkDV,YAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACI,qBAAJ,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA;QACA,IAAM,CAAN,YAAA,IAAA,OAAA,CAAA,iBAAA,EAAA;YACM,IAAN,CAAA,kBAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAlD0C,CAkD1C,CAAA;;;QAGA,IAAA,aAlDiB,EAkDjB;YACA,qBAAA,SAAA,GAAA,EAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAc,aAAd,CAAA,CAAA,OAAA,CAAA,UAAA,WAAA,EAAA;;;gBAGA,IAAA,KAAA,CAAA,SAAA,CAAA,WAAA,CAAA,EAAA;oBACA,qBAAA,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,EAAA,WAAA,EAAA,UAAA,EAAA,KAAA,CAAA,CAAA;oBAEA,IAlDkB,MAAC,EAAO;wBAmD1B,SAAA,CAAA,IAAA,CAAA,MAlDgB,CAAoB,CAkDpC;qBACA;iBACA;aACO,CAAP,CAAA;YACA,IAAA,SAAA,CAAA,MAAA,EAAA;;;gBAIA,OAAA;aACA;SACA;;;;;YAOM,qBAAN,cAlD2C,GAkD3C,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,MAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA;;;;;YAIA,IAAQ,cAAR,IAAA,cAAA,CAlDgC,MAkDhC,EAAA;gBACA,iCAAA,GAAA,IAAA,CAAA;aACA;iBACA;gBACA,qBAAA,MAAA,GAAA,OAAA,CAAA;gBACA,OAAA,MAAA,GAAA,MAAA,CAAA,UAAA,EAAA;oBACA,qBAAA,QAAA,GAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA;oBACA,IAAA,QAAA,EAAA;wBACA,iCAAA,GAAA,IAAA,CAAA;;;;;SAMA;;;;;QAKA,qBAAA,SAlDgC,GAkDhC,IAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QAlDA,IAAA,SAAA,EAAA;YAmDA,qBAlDyB,iBAkDzB,GAAA,IAAA,GAAA,EAAA,CAAA;YAEA,SAAA,CAAA,OAAA,CAAA,UAAA,QAAA,EAAA;gBACQ,qBAlDM,WAkDd,GAAA,QAAA,CAAA,IAAA,CAAA;gBACQ,IAAR,iBAAA,CAAA,GAlDc,CAkDd,WAAA,CAlDc;oBAmDd,OAAA;gBACQ,iBAAR,CAAA,GAAA,CAAA,WAAA,CAAA,CAlDwB;gBAmDhB,qBAlDM,OAkDd,GAAA,KAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAlD2B;gBAoDnB,qBAAR,UAlDwC,GAkDxC,OAAA,CAAA,kBAAA,CAAA;gBACQ,qBAAR,aAAA,KAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA,CAAA;gBACA,qBAAA,SAAA,GAAA,aAAA,CAAA,WAAA,CAAA,IAAA,mBAAA,CAAA;gBACA,qBAAA,OAAA,GAAA,IAAA,UAAA,CAAA,UAAA,CAAA,CAAA;gBACA,qBAAA,MAAA,GAAA,IAAA,yBAAA,CAAA,KAAA,CAAA,EAAA,EAAA,WAAA,EAAA,OAAA,CAAA,CAAA;gBACA,KAAU,CAAV,OAAA,CAAmB,kBAAnB,EAAA,CAAA;gBACA,KAAU,CAAV,MAAiB,CAAjB,IAAA,CAAA;oBACU,OAAV,EAAA,OAAA;oBACU,WAAV,EAAA,WAAA;oBACA,UAAA,EAAA,UAAA;oBACA,SAAA,EAAA,SAAA;oBACA,OAAA,EAAA,OAAA;;;iBAIA,CAAA,CAAA;aACA,CAAA,CAAA;SACK;;;;YAGC,MAAM,CAlDC,oBAkDb,CAlD8B,IAkD9B,CAAA,EAAA,EAAA,OAAA,EAlDoC,KAkDpC,EAAA,OAAA,CAAA,CAAA;SACA;aACA;;;;;;;;;;;;;IAaA,4BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,OAAA,EAAA,MAAA,EAAA,EAAA,QAAA,CAAA,OAAA,EAAA,IAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA;;;;;IArDA,4BAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,WAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAoGA;QAzCA,qBAAA,YAAA,GAAA,EAAA,CAAA;QACA,IAAM,CAAN,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;YACM,qBAAN,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;YACA,IAAQ,MAAR,CAAA,SAAA;gBACA,OAAA;YACA,qBAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAzD8B;YA2D9B,qBAzD0B,SAyD1B,GAAA,KAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,IAAA,SAAA,EAAA;gBACA,SAAA,CAAA,OAAA,CAAA,UAAA,QAAA,EAAA;oBACA,IAAA,QAAA,CAAA,IAAA,IAAA,KAAA,CAAA,WAAA,EAAA;wBACA,qBAAA,SAAA,GAAA,kBAAA,CAAA,OAAA,EAAA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;wBAEA,EAAA,SAAA,GAAA,OAAA,CAAA,GAAA,WAAA,CAAA;wBACA,cAAA,CAzDsB,KAyDtB,CAAA,MAAA,EAAA,QAAA,CAAA,KAAA,EAAA,SAAA,EAAA,QAAA,CAAA,QAAA,CAAA,CAAA;;;aAGA;YACA,IAAA,MAAA,CAAA,gBAAA,EAAA;gBACA,KAAA,CAAA,OAAA,CAAA,UAAA,CAAA,YAAA;;;oBAEA,MAAA,CAAA,OAAA,EAAA,CAAA;iBACA,CAAA,CAAA;aAvDS;iBA2DT;;;SAGA,CAAA,CAAA;QACA,IAAM,CAAN,MAAA,GAAA,EAAA,CAAA;QACA,OAAA,YAzDqB,CAyDrB,IAAA,CAzD2B,UAAA,CAyD3B,EAAA,CAAA,EAAA;;;YAGM,qBAAN,EAAA,GAAA,CAAA,CAAA,UAAA,CAAA,GAzDkD,CAAC,QAyDnD,CAzD4D;YA0D5D,qBAAA,EAAA,GAAA,CAAA,CAAA,UAAA,CAAA,GAAA,CAAA,QAAA,CAAA;YACA,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,EAAA;;;;;KAKA,CAAA;;;;;;;;KAQA,CAAA;;;;;IAIA,4BAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UA/De,OA+Df,EAAA;QACI,qBAAJ,YAAA,GAAA,KAAA,CAAA;QACA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA;YACA,YAAA,GAAA,IAAA,CAAA;QAEA,YAAA;;;;;;AAmDA,IAAA,yBAAA,IAAA,YAAA;;;;;IAzGA,SAAA,yBAAA,CAAA,MAAA,EAAA,WAAA,EAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAA,CAAA;QACS,IAAT,CAAA,WAAA,GAAA,WAA+B,CAA/B;QACS,IAAT,CAAA,OAAA,GAAA,EAAA,CAAA;QACS,IAAT,CAAA,eAAA,GAAS,IAAqB,GAA9B,EAAA,CAAA;QAEU,IAAV,CAAA,gBAAU,GAAiE,IAA3E,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,uBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,eAAwC,GAAxC,IAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,eAAA,GAAyC,CAAG,CAA5C;QAES,IAAT,CAAA,kBAAA,GAAA,CAAA,CAAS;QACA,IAAT,CAAA,gBAAA,GAAA,EAAA,CAAS;QACA,IAAT,CAAA,cAAA,GAAA,EAAA,CAAA;QAGS,IAAT,CAAA,SAAA,GAAA,EAAA,CAAA;QAIA,IAAA,CAAA,aAAA,GAAA,EAAA,CAAA;;;;;;;;;;;IA2FA,yBAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,OAAA,EAAA,OAAA,EAAA,EAxFiD,IAwFjD,CAAA,iBAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,EAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,yBAAA,CAAA,SAAA,EAAA,eAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;YACA,qBAAA,OAAA,GAAA,EAAA,CAAA;YACA,IAAA,CAAO,cAAP,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;gBACA,EAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;oBACA,IAAA,MAAA,CAAA,MAAA,EAAA;wBACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;IAlFA,yBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA;;;;SAgGA;;;;;;;;;;;;;;;;;;;;IAqBA,yBAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,EAAA,EAAA,WAAA,EAAA;QACA,qBAhGkB,KAgGlB,GAAA,IAAA,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,KAAA,IAAA,CAAA,EAAc;YACd,qBAhGuB,KAgGvB,GAAA,KAAA,CAAA;YACA,KAAA,qBAAA,CAAA,GAAA,KAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;gBACA,qBAAA,aAAA,GAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,IAAA,CAAA,MAAA,CAAA,eAAA,CAAA,aAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA;oBACA,IAhGkB,CAgGlB,cAAA,CAAA,MAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;oBACY,KAAZ,GAAA,IAAA,CAAA;oBACA,MAAA;iBACA;aAhGW;YAiGL,IAAI,CAhGC,KAgGX,EAAA;gBACA,IAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;aA9FS;SAiGT;aACA;;;;;;;;;;;IAWA,yBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA;QACA,qBAAA,EAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;;;;;;;;;;;;IAYA,yBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAAA,EAAA,IAAA,EAAA,OAAA,EAAA;;;;;;;;;;;IAYA,yBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAM,WAAN,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAlGA;QAmGA,IAAM,CAAN,WA7GkB;YA8GZ,OAAN;QACA,qBAAA,EAAA,GAAA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,CAAA;QACA,IAAA,CAAA,UAAA,CAAA,YAAA;YACA,KAAA,CAAA,uBAAA,CAAA,MAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA;YACA,OAAA,KAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;YAEQ,qBAAR,KAAA,GAAA,KA7G2C,CA6G3C,cA7GoD,CAAO,OA6G3D,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,KAAA,IAAA,CAAA,EAAA;;;;;KA3GA,CAAA;;;;;;;;;;;;;IA6HA,yBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA;QACA,IAAA,aAAA,CAAA,OAAA,CAAA,EAAA;;;;;;;;;;;;;IAaA,yBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA;QACI,IAAI,CAAR,aAAA,CAAA,OAAA,CAAA;YACM,OAAO;;;;;YAML,OAAR,CAAA,aAAA,GAAA,KAAA,CAAA;SACA;;;;QAKA,IAAM,WAAN,EAAA;YACA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,CAAA,UAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;SACA;;;;;KAKA,CAAA;;;;;;;;;;;;;IAcA,yBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,OAAA,EAAA,YAAA,EAAA;QACI,IAAI,CAAR,aAAA,CAAA,OAAA,CAAA,EAAA;YACM,IAAN,CAAA,kBArI6B,CAqI7B,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,OAAA;SArIA;QAsIA,qBAAA,EAAA,GAAA,WAAA,GAAA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA;QACA,IAAA,EAAA,EAAA;YACA,EAAA,CAAA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA,YAAA,CAAA,CAAA;;;;;;;;;;;;;IAaA,yBAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,OAAA,EAAA;QACA,IAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;QACA,OAAA,CAAA,YAAA,CAAA,GAAA;;;;;;;;;;;;;;IAgBA,yBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;KAeA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,gBAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAiCA;;;;YA7BA,IAAA,OAAA,EAAA;gBACA,OAAA,CAAY,OAzJO,CAyJnB,UAAA,MAAA,EAAA;;;;oBAGA,IAAA,MAAA,CAAA,MAAA,EAAA;wBACA,MAAA,CAAA,gBAAA,GAAA,IAAA,CAAA;qBACA;yBACA;wBAxJW,MAyJX,CAAA,OAAA,EAAA,CAAA;qBACA;iBACA,CAAA,CAAA;aACA;YAEQ,qBAAR,QAAA,GAAA,KAzJ6C,CAyJ7C,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAzJgD,IAAhD,QAAA,EAAA;gBA2JY,MAAZ,CAzJe,IAyJf,CAAA,QAzJ4B,CAAK,CAyJjC,OAAA,CAAA,UAAA,WAAA,EAAA,EAAA,OAAA,QAAA,CAAA,WAAA,CAzJ0E,GAyJ1E,mBAAA,CAAA,EAAA,CAAA,CAAA;aACA;SACA,CAAA,CAAA;QACA,IAAA,IAAQ,CAAR,uBAAA,CAAA,IAAA,IAzJwB,CAyJxB;YACA,OAAA;QACA,QAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAzJ0B,CAyJ1B,gBAzJ2C,EAyJ3C,qBAAA,EAAA,IAAA,CAAA,CAAA;QACA,IAAA,QAAA,CAAA,MAAA,EAAA;YACA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;gBACA,qBAAA,OAAA,GAAA,KAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,IAAA,OAAA,EAAA;;;;SAIA;KACA,CAAA;;;;IAxJA,yBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAoKA;QARA,OAAA,IAAA,OA3JkB,CA2JlB,UAAA,OAAA,EAAA;YACA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,EAAA;gBACA,OAAA,mBAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA;aACA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,OAAA,EAAA;QACA,qBAAA,OAAA,IAAA,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;QACA,IAAA,OAAA,IAAA,OAAA,CAAA,aA9JmB,EA8JnB;;YAEA,OAAA,CAAU,YAAV,CAAA,GAAA,kBAAA,CAAA;YACA,IAAA,OAAA,CAAA,WAAA,EAAA;gBACA,IAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,CAAA;gBACU,qBAAV,EAAA,GAAA,IAAA,CAAA,eAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA;gBACA,IAAA,EAAA,EAAA;oBACA,EAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAA,WAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAhIA;QAgIA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,CAAA,CAAA,CAAA,EAAA;QAEI,qBAAJ,OAAA,GAAA,EAAA,CAAA;QACA,IAAA,IAAA,CAjKU,eAiKV,CAAA,IAAA,EAAA;YACM,IAAN,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,EAjKsC,EAiKtC,OAAA,EAAA,EAAA,OAAA,KAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,CAAA,eAAA,CAAA,KAAA,EAAA,CAAA;SAjKA;QAkKA,IAAM,IAAN,CAAW,cAAX,CAAA,MAjKe;aAkKf,IAAA,CAAA,kBAAA,IAAA,IAAA,CAAA,sBAAA,CAAA,MAAA,CAAA,EAjKoD;YAkKpD,OAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;SACA;aACA;YAEQ,KAAR,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;gBACA,qBAAA,OAjKyC,GAiKzC,IAAA,CAAA,sBAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,OAjKyC,CAAA,CAAE;aAClC;SAkKT;QAEI,IAAI,CAAR,kBAjK2B,GAiK3B,CAAA,CAAA;;;;QAIA,IAAM,CAAN,SAAA,GAAA,EAAA,CAAA;QACA,IAAM,IAAI,CAjKC,aAAC,CAiKZ,MAAA,EAAA;;;;YA7JA,qBAAA,UAAA,GAAA,IAAA,CAAA,aAAA,CAAA;YAkKA,IAAQ,CAAR,aAAA,GAjKyB,EAAC,CAiK1B;YACA,IAAA,OAAA,CAAA,MAAA,EAAA;gBACA,mBAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,YAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;aACA;;;;;KA/JA,CAAA;;;;;IAyKA,yBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,WAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACI,qBApKM,YAoKV,GAAA,IApKU,qBAoKV,EAAA,CAAA;QACI,qBApKM,cAoKV,GAAA,EAAA,CApKU;QAsKN,qBApKM,iBAoKV,GAAA,IApKiC,GAoKjC,EAAA,CAAA;QACI,qBApKM,kBAoKV,GApKsC,EAoKtC,CAAA;QACA,qBAAA,eApKqC,GAoKrC,IAAA,GAAA,EAAA,CAAA;QACA,qBAAA,mBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;;;;YAKA,IAAA,CAAA,sBApKiB,CAoKjB,MAAA,CAAA,oBApK8C,CAAA,IAAK,CAoKnD,sBAAA,CAAA,CAAA;YACM,EAAN,CAAA;;;;QAKI,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,aAoKxB,CAAA,MAAA,EAAA,CAAA,EAAA,EApK8B;YAqKxB,QAAN,CAAA,aAAA,CApKY,CAoKZ,CAAA,EAAA,eAAA,CAAA,CAAA;SACA;QACA,qBAAA,aAAA,GAAA,EAAA,CApK4C;QAqK5C,qBAAA,2BAAA,GAAA,EAAA,CAAA;QACA,KAAA,qBApKuB,CAAI,GAoK3B,CAAA,EAAA,CApK4B,GAoK5B,IAAA,CAAA,sBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;YACA,qBAAA,OAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,CAAA,CAAA,CAAA;YACA,qBAAA,OAAA,IAAA,OApK2C,CAoK3C,YAAA,CAAA,CAAA,CAAA;YACA,IAAA,OAAA,IAAA,OAAA,CAAA,aAAA,EAAA;gBACA,QAAA,CAAA,OAAA,EAAA,eAAA,CAAA,CAAA;gBACA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;gBAEA,IAAA,CAAA,OAAA,CAAA,YAAA,EApKsB;oBAqKtB,2BAAA,CAAA,IAAA,CAAA,OApKqC,CAAC,CAAC;iBAqKvC;aACA;SAEA;QACA,KAAA,qBApKyB,CAoKzB,GApK2B,IAoK3B,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,EApKuD,CAoKvD,IAAA,CAAA,EAAA,CApKgE,EAoKhE,EAAA;YACA,qBAAA,EApK2B,GAoK3B,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;gBACA,qBAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;gBAEQ,qBApKM,OAoKd,GAAA,KAAA,CAAA,OAAA,CAAA;gBACQ,IAAI,CApKC,QAoKb,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,eAAA,CAAA,QAAA,EAAA,OAAA,CAAA,EAAA;oBApK0B,MAA1B,CAA0B,OAA1B,EAAA,CAAA;;;gBAwKQ,qBAAR,WAAA,GAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;gBACA,IAAU,CAAV,WAAA;oBACU,OApKO;;;gBAuKjB,IAAA,KAAA,CAAA,oBAAA,EAAA;;;;;;;;;;;gBAkBQ,WAAW,CApKC,SAoKpB,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA,EAAA,OAAA,EAAA,CAAA,uBAAA,GAAA,IAAA,CAAA,EAAA,CAAA,CAAA;gBACA,YAAA,CAAA,MAAA,CAAA,OApKgB,EAoKhB,WAAA,CApKwB,SAoKxB,CAAA,CAAA;gBACA,qBAAA,KAAA,GAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA;gBACA,kBAAA,CAAA,IAAA,CAAA,KApKgB,CAoKhB,CAAA;gBACA,WAAA,CAAgB,eAAhB,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,eAAA,CAAA,eAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA;gBACA,WAAA,CAAc,aAAd,CAAA,OAAA,CAAA,UApKmC,SAoKnC,EAAA,OAAA,EAAA;oBACA,qBAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;oBACA,IAAY,KAAK,CApKC,MAoKlB,EApK0B;wBAqK1B,qBAAA,QAAA,KAAA,mBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA,CAAA;wBACA,IAAA,CAAA,QAAA,EAAA;4BAlKoB,mBAoKpB,CAAA,GApK2C,CAAC,OAoK5C,EApK6C,QAoK7C,GApKuD,IAoKvD,GAAA,EAAA,CAAA,CAAA;yBACA;wBACA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,QApKoC,CAoKpC,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA;qBACA;iBACA,CAAA,CAAA;gBACA,WAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA,OAAA,EAAA;oBACU,qBAAV,KApKgC,GAoKhC,MApK4C,CAAI,IAoKhD,CAAA,SAAA,CAAA,CAAA;oBACA,qBAAA,MAAA,KAAA,oBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA,CAAA;oBACA,IAAA,CAAA,MAAA,EAAA;wBACA,oBAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,GAAA,IAAA,GAAA,EAAA,CAAA,CAAA;;;iBAIA,CAAA,CAAA;aACS,CAAT,CAAA;SACA;;;QAGA,qBAAA,2BAAA,GAAA,EAAA,CAAA;QACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;YAEA,qBAAA,OAAA,GAAA,aAAA,CApKsC,CAoKtC,CAAA,CApKsC;YAqKtC,IAAA,CAAA,YApKQ,CAoKR,GAAA,CAAA,OAAA,CAAA,EAAA;gBACA,2BAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;aACA;SACA;QACA,qBAAA,qBApKsC,GAoKtC,IAAA,GAAA,EAAA,CAAA;QACA,qBAAA,oBAnKkB,GAmKlB,EAAA,CAnKmB;QAqKnB,kBAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;YACA,qBAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA;YAEA,IAAA,YAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA;gBACA,oBAAA,CAAA,OAAA,CApKsB,OAAO,CAoK7B,CAAA;gBACA,KAAA,CAAA,qBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,WAAA,EAAA,KAAA,CAAA,WAAA,EAAA,qBAAA,CAAA,CAAA;aAEA;SAEK,CApKC,CAAC;QAsKH,cAAJ,CAAA,OApK0B,CAoK1B,UAAA,MApK2B,EAoK3B;;YAGA,qBAAA,eAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,KAAA,EAAA,MAAA,CAAA,WAAA,EAAA,MAAA,CAAA,WAAA,EAAA,IAAA,CAAA,CAAA;YACQ,eAAR,CAAA,OAAA,CAAA,UAAA,UAAA,EAAA,EAAA,eAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAnKqF,CAmKrF,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SAEA,CAAA,CAAA;;;QAMI,qBApKM,YAoKV,GAAA,mBAAA,CAAA,IAAA;YACA,qBAAA,CAAA,IAAA,CAAA,MAAA,EApKuD,2BAoKvD,EAAA,mBAAA,EAAAD,8BAAA,CAAA;YACA,IAAA,GAAA,EAAA,CAAA;;;;QAIA,qBAAA,UApKmC,GAoKnC,EAAA,CAAA;QACA,kBAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;YAGA,IAAA,OAAA,GAAA,KAAA,CAAA,OAAA,EAAA,MAAA,GAAA,KAAA,CAAA,MAAA,EApK6B,WAoK7B,GAAA,KAAA,CAAA,WApK8B,CAoK9B;;;YAIA,IAAA,YAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EApKgB;gBAqKhB,qBAAA,WAAA,GAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,WAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,aAAA,CAAA,CAAA;gBApKA,MAAA,CAAA,aAAA,CAAA,WAAA,CAAA,CAAA;gBAqKA,qBAAA,iBAAA,GAAA,IAAA,CAAA;gBACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CApKgC,GAoKhC,oBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;oBACA,qBAAA,MAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAAA,MAAA,KAAA,OAAA;wBACA,MAAA;oBAEY,IAAZ,KAAA,CAAA,MAAA,CAAA,eAAA,CAAA,MAAA,EAAA,OAAA,CAAA,EAAA;wBACA,iBAAA,GAAA,MAAA,CAAA;wBACc,MAAd;qBACA;iBACA;gBACA,IAAU,iBAAV,EApK0B;oBAqK1B,qBAAA,aAAA,GAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,iBAAA,CAAA,CAAA;oBApKA,IAAA,aAAA,IAAA,aAAA,CAAA,MAAA,EAAA;wBAqKA,MAAA,CAAqB,YApKO,GAoK5B,mBAAA,CAAA,aAAA,CAAA,CAAA;qBACA;oBACA,cAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;iBApKa;qBAqKb;oBACA,WAAA,CApKgB,IAoKhB,CAAA,MAAA,CAAA,CAAA;iBACA;aACO;iBACP;gBAEA,WApKuB,CAoKvB,OAAA,EAAA,WAAA,CAAA,UAAA,CAAA,CAAA;gBACA,MAAA,CAAA,SAAA,CAAA,YAAA,EAAA,OAAA,SAAA,CAAA,OAAA,EAAA,WAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA;gBACU,UAAV,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;aACA;SACA,CAAA,CAAA;QACA,UAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;YACA,qBAAA,iBAAA,GAAA,iBAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;;;;aAKA;SACA,CAAA,CAAA;;;;QAGA,cAAc,CApKC,OAAC,CAoKhB,UAAA,MAAA,EAAA;YACA,IAAA,MAAA,CAAA,YAAA,EAAA;gBACA,MAAA,CAAA,YAAA,CAAA,SAAA,CAAA,YAAA,EAAA,OAAA,MAAA,CAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;aAKS;SACT,CAAA,CAAA;;;;;YAMM,qBApKuB,OAoK7B,GAAA,aAAA,CAAA,CAAA,CAAA,CAAA;YApKA,qBAAA,OAAA,IAAA,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;;gBA2KU,SAAV;YACA,qBAAA,OAAA,GAAA,EAAA,CAAA;;;;YAKA,IAAQ,eAAR,CAAA,IAAA,EAAA;gBACQ,qBAAR,oBAAA,GAAA,eAAA,CAAA,GAAA,CApKkD,OAAY,CAoK9D,CAAA;gBACA,IAAU,oBAAV,IAAA,oBAAA,CAAA,MAAA,EApK+B;oBAqKrB,OAAV,CAAA,IAAA,CAAA,KAAA,CAAU,OAAV,EAAA,oBApKgC,CAoKhC,CAAA;iBACA;gBACA,qBAAA,oBAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,qBAAA,EAAA,IAAA,CAAA,CAAA;gBACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,oBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;oBACA,qBAAA,cAAA,GAAA,eAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IApKkB,cAoKlB,IAAA,cAAA,CAAA,MAAA,EAAA;wBACA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,cAAA,CAAA,CAAA;qBACA;iBApKa;aAqKb;YACA,IAAA,OAAA,CAAA,MAAA,EAAA;gBACA,6BAAA,CAAA,IAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA;aAEA;iBAnKW;gBAqKX,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;aACA;SAEA;QACA,WAAA,CAAY,OAAZ,CApKc,UAAA,MAoKd,EAAA;YACA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACM,MAAM,CApKC,MAAK,CAAE,YAoKpB;gBACA,MAAA,CAAA,OAAA,EAAA,CAAA;gBAEA,qBAAA,KAAA,GAAA,KAAA,CApKqC,OAoKrC,CApKkD,OAoKlD,CAAA,MAAA,CAAA,CAAA;gBAEA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;aACA,CAAA,CAAA;;;;;;;;;;;IAUA,yBAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,WAAA,EAxK+B,OAwK/B,EAAA;QAxKA,qBAAA,YAAA,GAAA,KAAA,CAAA;QAyKI,qBAAJ,OAAA,IAAA,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;QAxKA,IAAmD,OAAnD,IAAA,OAAA,CAAkE,aAAlE;YAyKQ,YAAR,GAAA,IAAA,CAxKc;QAAd,IAA2C,IAA3C,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA;YAyKA,YAAA,GAAA,IAAA,CAAA;QACA,IAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,QAAA,EAAA,EAAA,IAAA,CAAA,SA9KkD,CA8KlD,IA9KuD,CA8KvD,QAAA,CAAA,CAAA,EA9KwD,CA8KxD;;;;;;;;;;;;;;IAgBA,yBAAA,CAAA,SAAA,CAAA,mBAAe,GAAf,UAAA,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,WAAA,EAAA,YAAA,EAAA;QACA,qBAAA,OAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBAAA,EAAA;YArLA,qBAAA,qBAAA,GAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAsLM,IAAN,qBAAA,EAAA;gBACU,OAAV,GAAA,qBAAA,CAAA;aACA;SACA;aACA;YArLA,qBAAA,cAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAsLA,IAAA,cAAA,EAAA;gBArLA,qBAAA,oBAAA,GAAA,CAAA,YAAA,IAAA,YAAA,IAAA,UAAA,CAAA;gBAsLA,cAAA,CAAA,OAAA,CArLwB,UAAA,MAqLxB,EAAA;oBACA,IAAA,MAAA,CAAA,MAAA;wBACA,OAAA;oBACA,IAAA,CAAA,oBAAA,IAAA,MAAA,CAAA,WAAA,IAAA,WAAA;wBACA,OAAA;oBACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;iBACA,CAAA,CAAA;aArLA;SAsLA;QArLA,IAAA,WAAA,IAAqE,WAArE,EAAA;YAsLA,OAAA,GAAA,OAAA,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA;gBACA,IAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,WAAA;oBACA,OAAA,KAAA,CAAA;gBACA,IAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,WAAA;oBACA,OAAA,KAAA,CAAA;;;;;;;;;;;;IAeA,yBAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA,qBAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;;QAII,WAAJ,CAAA,WA1LU,CA0LV,OAAA,EAAA,WAAA,CAAA,UAzLoB,CAyLpB,CAAA;QAEI,qBA1LM,WA0LV,GAAA,WAAA,CAAA,WAAA,CAAA;QAGI,qBAAJ,WAAA,GAAA,WAAA,CAAA,OAAA,CAAA;;;QAGA,qBAAA,iBAAA,GAAA,WAAA,CAAA,mBA1LuC,GA0LvC,SAAA,GA1L0E,WA0L1E,CAAA;QACA,qBAAA,iBAAA,GAAA,WAAA,CAAA,mBAAA,GACiB,SADjB,GAAA,WAAA,CAAA;QAEA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,mBAAA,EAAA;YACA,qBAAA,OAAA,GAAA,mBAAA,CAAA,OAAA,CA1LmC;YA2LnC,qBAAA,gBAAA,GAAA,OAAA,KAAA,WAAA,CAAA;YACA,qBAAA,OAAA,GAAA,eAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;YACA,qBAAA,eAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA;YACA,eAAA,CA1LiB,OAAK,CAAM,UAAA,MA0L5B,EAAA;gBACA,qBAAA,UAAA,IAAA,MAAA,CAAA,aAAA,EAAA,CAAA,CAAA;gBACA,IAAA,UAAA,CAAA,aAAA,EAAA;oBACA,UAAA,CAAA,aAAA,EAAA,CAAA;;;;;;;;;;;;;;;IAoBA,yBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAlMU,EAkMV,WAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,aAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACI,qBAlMM,WAkMV,GAAA,WAAA,CAlMoC,WAkMpC,CAAA;QACI,qBAlMM,WAkMV,GAlMU,WAkMV,CAlMuC,OAkMvC,CAAA;;;;QAKA,qBAAA,mBAAA,GAlM8B,IAkM9B,GAAA,EAAA,CAAA;QACA,qBAAA,cAAA,GAAA,IAAA,GAlM8B,EAkM9B,CAAA;QAlMA,qBAAA,aAAA,GAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,mBAAA,EAAA;YAoMM,qBAlMM,OAkMZ,GAAA,mBAAA,CAAA,OAAA,CAAA;;YAEM,qBAAN,OAAA,GAAA,OAAA,CAAA,YAAA,CAAA,CAAA;YACA,IAAQ,OAAR,IAAA,OAAA,CAA2B,oBAA3B;gBACQ,OAAR,IAAAD,uCAAA,EAAA,CAAA;YACA,qBAAA,gBAAA,GAAA,OAAA,KAAA,WAAA,CAAA;YACA,qBAAA,eAAA,GAAA,kBAAA,CAAA;YACA,IAAA,CAAS,mBAAT,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA;gBACA,mBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,qBAAA,gBAAA,GAAA,qBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,EAAA;oBACA,eAlMY,GAkMZ,gBAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,CAlMwB,CAmMd,aAlMa,EAiMvB,CAAA,EAAA,CAAA,CAjMuB;iBAoMvB;;;YAIM,qBAlMK,UAkMX,GAAA,aAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,qBAlM2B,SAAS,GAkMpC,kBAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,WAAA,EAAA,OAAA,EAAA,mBAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA;YACA,qBAAA,MAAA,GAAA,KAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,SAAA,EAAA,eAAA,CAAA,CAAA;;;YAIA,IAAQ,mBAAR,CAAA,WAAA,IAlM2C,iBAkM3C,EAAA;gBACQ,cAAR,CAAA,GAlM0B,CAkM1B,OAAA,CAAA,CAAA;aACO;YAED,IAAN,gBAAA,EAAA;gBACA,qBAAA,aAAA,GAAA,IAAA,yBAAA,CAAA,WAAA,EAAA,WAAA,EAAA,OAAA,CAAA,CAAA;gBAEA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA;gBACA,iBAlM2B,CAkM3B,IAAA,CAAA,aAAA,CAAA,CAAA;aACA;YACA,OAAA,MAAA,CAAA;SAEA,CAAA,CAAA;QACI,iBAAJ,CAAA,OAAA,CAAA,UAAA,MAlMmB,EAkMnB;YACA,eAAA,CAAA,KAAA,CAAA,uBAAA,EAAA,MAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACM,MAAN,CAAA,MAAA,CAAA,YAAA,EAlM0B,OAAA,kBAkM1B,CAlM6C,KAkM7C,CAAA,uBAAA,EAAA,MAAA,CAAA,OAlMyF,EAAE,MAkM3F,CAAA,CAAA,EAAA,CAAA,CAAA;SACA,CAAA,CAAA;QACA,mBAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,QAAA,CAAA,OAAA,EAAA,sBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;;YAIA,mBACQ,CADR,OAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,WAAA,CAAA,OAAA,EAAA,sBAAA,CAAA,CAAA,EAAA,CAjMsE,CAAI;YAoM1E,SAlMW,CAAO,WAkMlB,EAAA,WAAA,CAAA,QAAA,CAAA,CAAA;SACA,CAAA,CAAA;;;;;;;;;;;;;QAkBI,IAAJ,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;YACA,OAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,OAAA,EAAA,SAAA,EAAA,WAAA,CAAA,QAAA,EAAA,WAAA,CAAA,KAAA,EAAA,WAAA,CAAA,MAAA,EAAA,eAAA,CAAA,CAAA;SACA;;;;;;CAhxBA,EAAA,CAAA,CAAA;;;;;;;IA6kBA,SAAA,yBAAA,CAAA,WAAA,EAAA,WAAgC,EAAhC,OAAA,EAAA;QAEU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QACU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QAGS,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QAEA,IAAA,CAAA,OAAA,GAAA,IAAAA,uCAAA,EAAA,CAAA;;;;QAEG,IAkPH,CAAA,gBAAA,GAAA,KAAA,CAAA;;;;;;;;;;IASA,MAAA,CAAA,cAAA,CAAA,yBAAA,CAAA,SAAA,EAAA,WAvPG,EAuPH;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,UAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,aAvPW,GAuPX,UAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEA,IAAA,IAAA,CAAA,mBAAA;YACQ,OAAR;QACI,IAAI,CAvPC,OAuPT,GAAA,MAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;;;;QAIA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;KAaA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,EAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,EAAA,EAAA;;;;QApQG,IAwQH,CAxQiB,OAwQjB,CAAA,SAxQ+B,CAwQ/B,EAxQqC,CAwQrC,CAxQsC;;;;;;;;;;;;;;;;;;;;;;;;;IAiStC,yBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,YAAA,EAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA;;;;;;QAMA,IAAA,CAAA,OAtRwB,CAsRxB,OAAA,EAtRkC,CAsRlC;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,YAAA,EAAA,CAzRG,IAyRH,CAAA,MAAA,IAAA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;SAQA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;AAkCA,SAAA,kBAAA,CAAA,GAAA,EAAA,GAxTyB,EAAO,KAwThC,EAAA;IACA,qBAAA,aAAA,CAxTc;IAyTd,IAAA,GAAA,YAAA,GAAA,EAAA;QACA,aAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACA,IAAM,aAAN,EAAA;YACA,IAAQ,aAAR,CAxToB,MAwTpB,EAAA;gBACA,qBAAA,KAAA,GAAA,aAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;gBACA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;aACA;YAxTA,IAAA,aAAA,CAAA,MAAA,IAAA,CAAA,EAAA;gBAyTA,GAAA,CAAA,MAAA,CAxToB,GAwTpB,CAxTyB,CAAG;aAyT5B;SACA;KACA;SACA;QACA,aAAA,GAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACA,IAAM,aAAN,EAAA;YACA,IAAQ,aAAR,CAxToB,MAwTpB,EAAA;gBACA,qBAAA,KAAA,GAAA,aAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;gBACA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;aACA;YACA,IAAA,aAAA,CAAA,MAAA,IAAA,CAAA,EAAA;gBACA,OAAA,GAAA,CAAA,GAAA,CAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,qBAAA,CAAA,KAAA,EAAA;IACA,QAAA,OAAA,KAAA;QACA,KAAA,SAAA;;;;;CAKA;;;;;;;CAOA;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,SAAA,qBAAA,CAAA,MAAA,EAAA,QAAA,EAAA,eAAA,EAAA,YAAA,EAAA;IACA,qBAAA,SAAA,GAAA,QAAA,CAAA,GAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,YAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CA3UwD,CA2UxD;;;QAIA,qBA3UoB,MA2UpB,GAAA,EA3UoC,CAAA;QA4UpC,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;YACA,qBAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA,EAAA,YAAA,CAAA,CAAA;;;YAGA,IAAA,CAAA,KAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;gBAEA,OAAA,CAAA,YAAA,CA3UmC,GA2UnC,0BAAA,CAAA;aACA;SACA,CAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,oBAAA,CAAA,KAAA,EAAA;IA/TA,qBAAA,OAAA,GAAA,IAAA,GAAA,CAAA,KAAA,CAAA,CAAA;IAgUA,qBAAA,kBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;IA/TA,qBAAmD,MAAnD,CAAA;IAgUA,MAAA,GAAA,UAAA,IAAA,EAAA;QA/TA,IAAiD,CAAjD,IAAA;YAgUQ,OA/TO,IAAC,CAAI;QAgUpB,IAAM,OAAN,CAAA,GAAA,CAAA,IAAA,CAAA,UA/T8B,CAAI;YAgU5B,OA/TO,KAAK,CA+TlB;QACA,IAAA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,UAAA,CAAA;YACA,OAAA,IAAA,CAAA;QACA,IAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA;YACA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,OAAA,IAAA,CAAA;SAEA;;;;;;;;;;;AAUA,SAAA,aAAA,CAAA,OAAA,EAAA,SApUoB,EAoUpB;IACA,IAAI,OApUO,CAoUX,SAAA,EAAA;QACA,OAAA,OAAA,CAAA,SAAA,CAAA,QAAA,CAAA,SAAA,CAAA,CAAA;KACA;;;;;;;;;;;AAUA,SAAA,QAAA,CAAA,OAAA,EAAA,SAxUQ,EAwUR;IACA,IAAI,OAAJ,CAAA,SAAA,EAAA;QACA,OAAA,CAAA,SAAA,CAAA,GAxUgB,CAAQ,SAwUxB,CAAA,CAAA;KACA;SACA;QACA,qBAAA,OAAA,GAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;QACA,IAAA,CAAA,OAAA,EAAA;;;;;;;;;;;AAUA,SAAA,WAAA,CAAA,OAAA,EAAA,SA5UkD,EA4UlD;IACA,IAAI,OAAJ,CAAA,SAAA,EAAA;QACA,OAAA,CAAA,SAAA,CA5Ua,MA4Ub,CAAA,SAAA,CAAA,CAAA;KACA;SACA;QACA,qBAAA,OAAA,GAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;;;;KAIA;CACA;;;;AAIA,SAAA,WAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;ADvpDA,IAAA,eAAA,IAAA,YAAA;;;;;IAIA,SAAA,eAAA,CAAA,MAAA,EAAA,UAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAQ,CAAC,aAAT,GAAmC,EAAnC,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,UAAA,OAAA,EAAA,OAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;IAgBA,eAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA,WAAA,EAPkB,IAOlB,EAAA,QAAA,EAAA;QACA,qBAPyB,QAOzB,GAAA,WAAA,GAAA,GAAA,GAAA,IAAA,CAAA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA,aAAA,CACsC,QADtC,CAAA,CAAA;QAEA,IAAA,CAAO,OAAP,EAAA;YACM,qBAPuB,MAAM,GAAA,EAAK,CAOxC;YACM,qBAAN,GAAA,IAAA,iBAAA,mBAAA,QAAA,GAAA,MAAA,CAAA,CAAA,CAAA;YACA,IAAA,MAAA,CAAA,MAAA,EAAA;gBACA,MAAA,IAAA,KAP2B,CAO3B,0BAAA,GAP4C,IAO5C,GAAA,0DAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;aACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgDA,eAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAU,WAAV,EAAA,OAAA,EAAA,QAAA,EAAA,KAAA,EAAA;;QAEA,IAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,EAAA;YACA,IAAA,EAAA,GAAA,oBAAA,CAAA,QAAA,CAAA,EAhCW,EAgCX,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,MAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;YACA,qBAAA,IAAA,IAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;IAgBA,eAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA;;QAEA,IAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,WAAA,EAAA;QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,CAAA,CAAA,CAAA,EAAA;QAAA,IAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;KAAA,CAAA;;;;;;;iBAMA,MAAA,mBA9CkD,IA8ClD,CAAA,eAAA,CAAA,OAAA,EAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;;;CAtHA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;IDbA,SAAA,mBAAA,CAAA,OAAU,EAAV,SAAA,EAAA,OAAA,EAAA,eAAA,EAAA;QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAA,GAAA,EAAA,CAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAwCA;QAvCU,IAAV,CAAA,OAAA,GAAA,OAAuC,CAAvC;QACU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAIU,IAAV,CAAA,OAAA,GAAA,OAAA,CAAA;QACU,IAAV,CAAA,eAAA,GAA4B,eAA5B,CAAA;QACU,IAAV,CAAA,UAAA,GAAA,EAAA,CAAqB;QACX,IAAV,CAAA,WAAA,GAAA,EAAA,CAAA;QAES,IAAT,CAAA,aAAA,GAAA,EAAA,CAAA;QAES,IAAT,CAAA,YAAS,GAAqC,KAAK,CAAnD;QAES,IAAT,CAAA,SAAA,GAAA,KAAA,CAAmE;QAW/D,IAAI,CAAC,QAAT,GAAA,KAAA,CAAA;QACI,IAAI,CAAC,UAAT,GAAA,KAAA,CAAA;QACI,IAAI,CAAC,IAAI,GAAG,CAAhB,CAAA;QAEI,IAAI,CAAC,YAAT,GAAA,IAA4B,CAAC;QACzB,IAAJ,CAAA,eAAA,GAA2B,EAA3B,CAAA;QACA,IAAM,CAAN,SAAgB,GAAG,OAAO,CAA1B,UAAA,CAAA,CAAA;QACA,IAAM,CAAN,MAAa,GAAb,OAAA,CAAwB,OAAxB,CAAA,IAAA,CAAA,CAAsC;QACtC,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,MAAA,CAAA;QACA,IAAA,CAAA,cAAA,GAAA,EAAA,CAAA;;;;SAHA,CAAA,CAAA;KAQA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,EAAA;;;;SAIA;KACA,CAAA;;;;;;QALA,IAAA,CAAA,yBAAA,EAAA,CAAA;KAYA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACI,IAAI,IAAR,CAAA,YAAA;YACM,OAAN;QACA,IAAM,CAAN,YAAA,GAAA,IAAA,CAXU;QAYV,qBAXyB,SAWzB,GAXkC,IAWlC,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,MAAA,EAAA,EAAA,OAAA,UAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,qBAAA,kBAAA,GAX+B,MAAmB,CAWlD,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA;QACA,IAAA,kBAAA,CAAA,MAA2B,EAA3B;YACA,qBAAA,kBAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;YACA,qBAXyB,mBAWzB,GAAA,EAAA,CAAA;YACA,kBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBAEU,IAAV,CAAA,kBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;oBACA,mBAAA,CAAA,IAAA,CAXc,IAWd,CAAA,CAAA;;gBAEQ,kBAAR,CAAA,IAAA,CAAA,GAAA,KAAA,CAXqB,cAWrB,CAAA,IAAA,CAAA,CAAA;aACA,CAAA,CAAA;YACA,IAAA,mBAAA,CAAA,MAAA,EAAA;gBACA,qBAAA,MAAA,GAAA,IAAA,CAAA;;oBAGA,qBAAA,EAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;oBACA,mBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;wBAEA,EAAA,CAXmB,IAWnB,CAAA,GAAA,aAXyB,CAAoB,MAAC,CAAI,OAAC,EAAQ,IAW3D,CAAA,CAAA;qBACA,CAAA,CAAA;iBACA,CAAA;;gBANA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;;iBAMA;aACA;;;;QATA,IAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA,EAAA,OAAA,KAAA,CAAA,SAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,YAAA;;QAeA,IAAA,IAAA,CAAA,MAAA,EAAA;YACA,IAAA,CAAA,oBAAA,EAAA,CAAA;;;;;;;;;;;;;;;;QAJG,QAoBH,OAAA,CAAA,SApB2B,CAAK,CAoBhC,SApByC,EAoBzC,OAAA,CAAA,EAAA;;IAKA,MAAA,CAAA,cAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAvBkC,OAuBlC,IAAA,CAvBwC,OAuBxC,CAvByD,EAAG;;;KAA5D,CAAA,CAA4D;;;;;IA4B5D,mBAAA,CAAA,SAAA,CAAA,OA1BG,GA0BH,UAAA,EAAA,EAAA,EAAA,IA1BsC,CA0BtC,WAAA,CAAA,IAAA,CA1BuD,EAAG,CA0B1D,CA1B2D,EA0B3D,CAAA;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EA7BG,EA6BH,EAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CA7B0C,EA6B1C,CAAA,CAAA,EA7B4D,CA6B5D;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,SA/BS,GA+BT,UAAA,EAAA,EAAA,EAAA,IAAA,CA/BU,aA+BV,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA;QACA,IAAA,CAAA,YAAA,EAAA,CAAA;QACI,IAAI,CA/BC,IA+BT,CAAA,UA/BwB,EA+BxB,EAAA;YACA,IAAA,CAAA,WAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;QAIA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA;KACA,CAAA;;;;;;QAMA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,CAAA;KACA,CAAA;;;;;;;QAOA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA;KACA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,YAAA;;;;QAnCA,IAAA,CAAA,QAAA,GAAG,KAAH,CAAA;KAwCA,CAAA;;;;;;;SAOA;KACA,CAAA;;;;;;QAMA,IAAA,CAAA,IAAA,EA3C0B,CA2C1B;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA,EAAA,OAAA,IA7C0B,CA6C1B,QAAA,CAAA,EAAA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA;QACA,IAAM,CAAN,IA7CW,CA6CX,UAAA,EA7CY;YA8CZ,IAAA,CAAA,oBAAA,EAAA,CAAA;YACA,IAAA,CAAA,SAAA,EAAA,CAAA;;;;;KAKA,CAAA;;;;;;;;;;IAaA,MAAA,CAAA,cAAA,CAAI,mBAAJ,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,MAtDqD,GAAG,IAsDxD,CAAA,SAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,aAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;QACA,IAAA,IAAA,CAAS,UAAT,EAAA,EAAA;YACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;gBACA,IAAA,IAAA,IAAA,QAAA,EAAA;oBACA,MAAA,CAAA,IAtD2B,CAsD3B;wBACA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,aAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;iBACA;aAEA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;KDpKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAbA,mBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,OAAR,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,eAAA,EAAA;QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAA,GAAA,EAAA,CAAA,EAAA;QACA,qBAAA,IA3B6B,GAAG,KA2BhC,IAAA,CAAA,GAAA,MAAA,GAAA,UAAA,CAAA;QACA,qBAAA,aAAA,GAAA,EAAA,QAAA,EAAA,QAAA,EAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA;;;QAKA,IAAA,MAAA,EAAA;YACA,aAAA,CAAA,QAAA,CAAA,GAAA,MAAA,CAAA;;;;KAIA,CAAA;IACA,OAAA,mBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;"}