{"_args": [["@angular/animations@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/animations@4.2.5", "_id": "@angular/animations@4.2.5", "_inBundle": false, "_integrity": "sha1-EjD2vGoY8quyPifx2a6e+v0aYDE=", "_location": "/@angular/animations", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/animations@4.2.5", "name": "@angular/animations", "escapedName": "@angular%2fanimations", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/animations/-/animations-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - animations integration with web-animationss", "es2015": "./@angular/animations.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/animations.umd.js", "module": "./@angular/animations.es5.js", "name": "@angular/animations", "peerDependencies": {"@angular/core": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./animations.d.ts", "version": "4.2.5"}