{"_args": [["concat-stream@1.6.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "concat-stream@1.6.2", "_id": "concat-stream@1.6.2", "_inBundle": false, "_integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "_location": "/concat-stream", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "concat-stream@1.6.2", "name": "concat-stream", "escapedName": "concat-stream", "rawSpec": "1.6.2", "saveSpec": null, "fetchSpec": "1.6.2"}, "_requiredBy": ["/extract-zip"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/concat-stream/-/concat-stream-1.6.2.tgz", "_spec": "1.6.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/maxogden/concat-stream/issues"}, "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "description": "writable stream that concatenates strings or binary data and calls a callback with the result", "devDependencies": {"tape": "^4.6.3"}, "engines": ["node >= 0.8"], "files": ["index.js"], "homepage": "https://github.com/maxogden/concat-stream#readme", "license": "MIT", "main": "index.js", "name": "concat-stream", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/concat-stream.git"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "tags": ["stream", "simple", "util", "utility"], "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.6.2"}