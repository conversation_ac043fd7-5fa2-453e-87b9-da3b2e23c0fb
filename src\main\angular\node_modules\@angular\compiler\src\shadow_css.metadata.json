[{"__symbolic": "module", "version": 3, "metadata": {"ShadowCss": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "shimCssText": [{"__symbolic": "method"}], "_insertDirectives": [{"__symbolic": "method"}], "_insertPolyfillDirectivesInCssText": [{"__symbolic": "method"}], "_insertPolyfillRulesInCssText": [{"__symbolic": "method"}], "_scopeCssText": [{"__symbolic": "method"}], "_extractUnscopedRulesFromCssText": [{"__symbolic": "method"}], "_convertColonHost": [{"__symbolic": "method"}], "_convertColonHostContext": [{"__symbolic": "method"}], "_convertColonRule": [{"__symbolic": "method"}], "_colonHostContextPartReplacer": [{"__symbolic": "method"}], "_colonHostPartReplacer": [{"__symbolic": "method"}], "_convertShadowDOMSelectors": [{"__symbolic": "method"}], "_scopeSelectors": [{"__symbolic": "method"}], "_scopeSelector": [{"__symbolic": "method"}], "_selectorNeedsScoping": [{"__symbolic": "method"}], "_makeScopeMatcher": [{"__symbolic": "method"}], "_applySelectorScope": [{"__symbolic": "method"}], "_applySimpleSelectorScope": [{"__symbolic": "method"}], "_applyStrictSelectorScope": [{"__symbolic": "method"}], "_insertPolyfillHostInCssText": [{"__symbolic": "method"}]}}, "CssRule": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "processRules": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"ShadowCss": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "shimCssText": [{"__symbolic": "method"}], "_insertDirectives": [{"__symbolic": "method"}], "_insertPolyfillDirectivesInCssText": [{"__symbolic": "method"}], "_insertPolyfillRulesInCssText": [{"__symbolic": "method"}], "_scopeCssText": [{"__symbolic": "method"}], "_extractUnscopedRulesFromCssText": [{"__symbolic": "method"}], "_convertColonHost": [{"__symbolic": "method"}], "_convertColonHostContext": [{"__symbolic": "method"}], "_convertColonRule": [{"__symbolic": "method"}], "_colonHostContextPartReplacer": [{"__symbolic": "method"}], "_colonHostPartReplacer": [{"__symbolic": "method"}], "_convertShadowDOMSelectors": [{"__symbolic": "method"}], "_scopeSelectors": [{"__symbolic": "method"}], "_scopeSelector": [{"__symbolic": "method"}], "_selectorNeedsScoping": [{"__symbolic": "method"}], "_makeScopeMatcher": [{"__symbolic": "method"}], "_applySelectorScope": [{"__symbolic": "method"}], "_applySimpleSelectorScope": [{"__symbolic": "method"}], "_applyStrictSelectorScope": [{"__symbolic": "method"}], "_insertPolyfillHostInCssText": [{"__symbolic": "method"}]}}, "CssRule": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "processRules": {"__symbolic": "function"}}}]