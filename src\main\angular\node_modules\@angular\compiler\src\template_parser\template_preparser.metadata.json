[{"__symbolic": "module", "version": 3, "metadata": {"preparseElement": {"__symbolic": "function"}, "PreparsedElementType": {"NG_CONTENT": 0, "STYLE": 1, "STYLESHEET": 2, "SCRIPT": 3, "OTHER": 4}, "PreparsedElement": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 68, "character": 19, "context": {"typeName": "PreparsedElementType"}}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "string"}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"preparseElement": {"__symbolic": "function"}, "PreparsedElementType": {"NG_CONTENT": 0, "STYLE": 1, "STYLESHEET": 2, "SCRIPT": 3, "OTHER": 4}, "PreparsedElement": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 68, "character": 19, "context": {"typeName": "PreparsedElementType"}}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "string"}]}]}}}}]