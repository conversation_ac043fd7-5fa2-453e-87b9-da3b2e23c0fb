[{"__symbolic": "module", "version": 3, "metadata": {"Summary": {"__symbolic": "interface"}, "SummaryResolver": {"__symbolic": "class", "arity": 1, "members": {"isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}]}}, "JitSummaryResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Summary": {"__symbolic": "interface"}, "SummaryResolver": {"__symbolic": "class", "arity": 1, "members": {"isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}]}}, "JitSummaryResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}]}}}}]