{"version": 3, "file": "typescript_symbols.js", "sourceRoot": "", "sources": ["../../../../../packages/compiler-cli/src/diagnostics/typescript_symbols.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAIH,uBAAyB;AACzB,2BAA6B;AAC7B,+BAAiC;AAEjC,qCAA0J;AAG1J,sCAAsC;AACtC,2CAA2C;AAC3C,IAAM,SAAS,GAAI,EAAU,CAAC,aAAa;IACvC,CAAC,UAAC,IAAa;QACV,OAAA,CAAC,CAAC,CAAE,EAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAI,EAAU,CAAC,aAAa,CAAC,OAAO,CAAC;IAAlF,CAAkF,CAAC;IACxF,CAAC,UAAC,IAAa,IAAK,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAI,EAAU,CAAC,SAAS,CAAC,OAAO,CAAC,EAA9C,CAA8C,CAAC,CAAC;AAExE,IAAM,eAAe,GAAI,EAAU,CAAC,WAAW;IAC3C,CAAC,UAAC,IAAa;QACV,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAI,EAAU,CAAC,SAAS,CAAC,MAAM;YACxC,IAAY,CAAC,WAAW,GAAI,EAAU,CAAC,WAAW,CAAC,SAAS,CAAC;IADjE,CACiE,CAAC;IACvE,CAAC,UAAC,IAAa,IAAK,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAI,EAAU,CAAC,SAAS,CAAC,SAAS,CAAC,EAAhD,CAAgD,CAAC,CAAC;AAQ1E,wBACI,OAAmB,EAAE,OAAuB,EAAE,MAAqB,EACnE,UAA6B;IAC/B,MAAM,CAAC,IAAI,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACzE,CAAC;AAJD,wCAIC;AAED,yBACI,OAAmB,EAAE,OAAuB,EAAE,YAA0B;IAE1E,IAAM,WAAW,GAAG,wBAAwB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACpE,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QAChB,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACpD,IAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,EAAC,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,OAAO,SAAA,EAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACnE,CAAC;AACH,CAAC;AATD,0CASC;AAED,wCACI,OAAmB,EAAE,OAAuB,EAAE,MAAqB,EACnE,WAAgC;IAClC,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,SAAA,EAAE,OAAO,SAAA,EAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC3E,CAAC;AALD,wEAKC;AAED,kCACI,OAAmB,EAAE,IAAkB;IACzC,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,UAAA,KAAK;YAClC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAClD,IAAM,gBAAgB,GAAG,KAA4B,CAAC;gBACtD,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,IAAI,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC9E,MAAM,CAAC,gBAAgB,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC,CAAqC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,SAAS,CAAC;AACnB,CAAC;AAfD,4DAeC;AAED,uBACI,MAAqB,EAAE,OAAmB,EAAE,OAAuB,EACnE,KAA2B;IAC7B,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,EAAE,EAAC,OAAO,SAAA,EAAE,OAAO,SAAA,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;AACjE,CAAC;AAJD,sCAIC;AAED;IAIE,+BACY,OAAmB,EAAU,OAAuB,EAAU,MAAqB,EACnF,UAA6B;QAD7B,YAAO,GAAP,OAAO,CAAY;QAAU,YAAO,GAAP,OAAO,CAAgB;QAAU,WAAM,GAAN,MAAM,CAAe;QACnF,eAAU,GAAV,UAAU,CAAmB;QALjC,cAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;IAKP,CAAC;IAE7C,2CAAW,GAAX,UAAY,MAAc,IAAiB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEzF,8CAAc,GAAd,UAAe,IAAiB;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAM,IAAI,GAAG,oBAAoB,CAC7B,IAAI,EAAE,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAC7E,MAAM;gBACF,IAAI,WAAW,CAAC,IAAI,EAAE,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAED,4CAAY,GAAZ;QAAa,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;QAC7B,sEAAsE;QACtE,IAAI,MAAM,GAAqB,SAAS,CAAC;QACzC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAClB,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;oBACvB,MAAM,GAAG,SAAS,CAAC;oBACnB,KAAK,CAAC;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;IAED,4CAAY,GAAZ,UAAa,IAAY,IAAY,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEnF,8CAAc,GAAd,UAAe,IAAY;QACzB,EAAE,CAAC,CAAC,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC;YAChC,IAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC7D,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChB,MAAM,CAAC,IAAI,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAED,kDAAkB,GAAlB,UAAmB,MAAc;QAC/B,EAAE,CAAC,CAAC,MAAM,YAAY,WAAW,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5F,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YAC7B,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAChE,EAAE,CAAC,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC,CAAC;gBAC9B,MAAM,CAAC,IAAI,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1D,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,wCAAQ,GAAR;QACE,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC/C,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAED,kDAAkB,GAAlB,UAAmB,IAAkB;QACnC,IAAM,OAAO,GAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;QAC/F,IAAM,UAAU,GAAG,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3D,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACf,IAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAC/D,EAAE,CAAC,CAAC,WAAW,CAAC;gBAAC,MAAM,CAAC,IAAI,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,6CAAa,GAAb,UAAc,IAAkB;QAC9B,IAAM,OAAO,GAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;QAC/F,IAAM,UAAU,GAAG,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAG,CAAC;QAC7D,MAAM,CAAC,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,iDAAiB,GAAjB,UAAkB,OAA4B;QAC5C,IAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;QACpC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,cAAc,CAAC,CAAC,CAAC,EAArB,CAAqB,CAAC,CAAC,CAAC;QACvD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAED,gDAAgB,GAAhB,UAAiB,YAA2B;QAC1C,IAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;QACpC,GAAG,CAAC,CAAsB,UAAY,EAAZ,6BAAY,EAAZ,0BAAY,EAAZ,IAAY;YAAjC,IAAM,WAAW,qBAAA;YACpB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;SACrC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAED,yCAAS,GAAT,UAAU,IAAY,EAAE,MAAc;QACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEO,yDAAyB,GAAjC,UAAkC,UAAqB;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7E,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;YAClD,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAS,EAAE,eAAe,CAAC,CAAC;QAE/D,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAChB,IAAM,sBAAsB,GAAG,WAAW,CAAC,YAAc,CAAC,CAAC,CAA2B,CAAC;YACvF,GAAG,CAAC,CAAoB,UAAiC,EAAjC,KAAA,sBAAsB,CAAC,UAAU,EAAjC,cAAiC,EAAjC,IAAiC;gBAApD,IAAM,SAAS,SAAA;gBAClB,IAAM,MAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAM,CAAC,CAAC;gBAC9D,EAAE,CAAC,CAAC,MAAI,CAAC,MAAQ,CAAC,IAAI,IAAI,aAAa,IAAI,eAAe,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;oBACjE,IAAM,aAAa,GAAG,MAAwB,CAAC;oBAC/C,EAAE,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC7C,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC/C,CAAC;gBACH,CAAC;aACF;QACH,CAAC;IACH,CAAC;IAEO,2CAAW,GAAnB,UAAoB,MAAc;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC;IAC7B,CAAC;IAEO,8CAAc,GAAtB,UAAuB,MAAc;QACnC,IAAI,IAAI,GAA0B,SAAS,CAAC;QAC5C,EAAE,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC,CAAC,CAAC;YAClC,IAAI,GAAG,MAAM,CAAC;QAChB,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC;YAC9C,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC;QACD,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IACH,4BAAC;AAAD,CAAC,AAvID,IAuIC;AAED,sBAAsB,IAAa;IACjC,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC5C,MAAM,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,sBAAsB,IAAa,EAAE,OAAoB;IACvD,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,gBAAgB,CAAC,CAAC,EAAE,OAAO,CAAC,EAAhC,CAAgC,CAAC,CAAC;AAC7E,CAAC;AAED,yBAAyB,IAAa,EAAE,OAAoB,EAAE,KAAe;IAE3E,0DAA0D;IAC1D,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;AACtF,CAAC;AAED;IACE,qBAAmB,MAAe,EAAS,OAAoB;QAA5C,WAAM,GAAN,MAAM,CAAS;QAAS,YAAO,GAAP,OAAO,CAAa;QAC7D,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,MAAM,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,sBAAI,6BAAI;aAAR;YACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAClC,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC;QAClD,CAAC;;;OAAA;IAED,sBAAI,6BAAI;aAAR,cAA8B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAE9C,sBAAI,iCAAQ;aAAZ,cAAyB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IAE/C,sBAAI,6BAAI;aAAR,cAA+B,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAElD,sBAAI,kCAAS;aAAb,cAAoC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAEvD,sBAAI,+BAAM;aAAV,cAAwB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAEtC,sBAAI,iCAAQ;aAAZ,cAA0B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAE7D,sBAAI,iCAAQ;aAAZ;YACE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;QAC7E,CAAC;;;OAAA;IAED,sBAAI,mCAAU;aAAd,cAA+B,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAExF,6BAAO,GAAP;QACE,MAAM,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED,gCAAU,GAAV,cAA4B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7E,qCAAe,GAAf,UAAgB,KAAe;QAC7B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,6BAAO,GAAP,UAAQ,QAAgB,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IACnE,kBAAC;AAAD,CAAC,AAzCD,IAyCC;AAED;IAKE,uBAAY,MAAiB,EAAU,OAAoB;QAApB,YAAO,GAAP,OAAO,CAAa;QACzD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACpE,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACxC,MAAM,CAAC;IACb,CAAC;IAED,sBAAI,+BAAI;aAAR,cAAqB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE/C,sBAAI,+BAAI;aAAR,cAA8B,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAE7E,sBAAI,mCAAQ;aAAZ,cAAyB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IAE/C,sBAAI,+BAAI;aAAR,cAA+B,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAEnF,sBAAI,oCAAS;aAAb,cAAoC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAEvF,sBAAI,iCAAM;aAAV;YACE,2DAA2D;YAC3D,MAAM,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;;;OAAA;IAED,sBAAI,mCAAQ;aAAZ,cAA0B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAE7D,sBAAI,mCAAQ;aAAZ,cAA0B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEzC,sBAAI,qCAAU;aAAd,cAA+B,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAE5E,+BAAO,GAAP;QACE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACjF,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/E,IAAM,WAAW,GAAG,IAAI,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YACxC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,kCAAU,GAAV,cAA4B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7E,uCAAe,GAAf,UAAgB,KAAe;QAC7B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,+BAAO,GAAP,UAAQ,QAAgB,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjE,sBAAY,iCAAM;aAAlB;YACE,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;YACxB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACV,IAAI,GAAG,IAAI,CAAC,OAAO;oBACf,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrF,CAAC;YACD,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;;;OAAA;IACH,oBAAC;AAAD,CAAC,AA7DD,IA6DC;AAED;IACE,wBAAoB,WAA8B;QAA9B,gBAAW,GAAX,WAAW,CAAmB;IAAG,CAAC;IAEtD,sBAAI,gCAAI;aAAR,cAAa,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE5C,sBAAI,gCAAI;aAAR,cAAa,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE5C,sBAAI,oCAAQ;aAAZ,cAAyB,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAEhD,sBAAI,qCAAS;aAAb,cAAoC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAEvD,sBAAI,gCAAI;aAAR,cAAa,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE5C,sBAAI,oCAAQ;aAAZ,cAA0B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;;;OAAA;IAElE,sBAAI,oCAAQ;aAAZ,cAA0B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEzC,sBAAI,kCAAM;aAAV,cAAwB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAEtC,sBAAI,sCAAU;aAAd,cAA+B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAEpE,gCAAO,GAAP,cAAyB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAElE,mCAAU,GAAV,cAA4B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAExE,wCAAe,GAAf,UAAgB,KAAe;QAC7B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,gCAAO,GAAP,UAAQ,QAAgB,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IACnE,qBAAC;AAAD,CAAC,AA9BD,IA8BC;AAED;IACE,0BAAoB,SAAuB,EAAU,OAAoB;QAArD,cAAS,GAAT,SAAS,CAAc;QAAU,YAAO,GAAP,OAAO,CAAa;IAAG,CAAC;IAE7E,sBAAI,uCAAS;aAAb;YACE,MAAM,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;;;OAAA;IAED,sBAAI,oCAAM;aAAV,cAAuB,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChG,uBAAC;AAAD,CAAC,AARD,IAQC;AAED;IACE,iCAAoB,SAAoB,EAAU,UAAkB;QAAhD,cAAS,GAAT,SAAS,CAAW;QAAU,eAAU,GAAV,UAAU,CAAQ;IAAG,CAAC;IAExE,sBAAI,8CAAS;aAAb,cAA+B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAEjE,sBAAI,2CAAM;aAAV,cAAuB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAClD,8BAAC;AAAD,CAAC,AAND,IAMC;AAED,IAAM,aAAa,GAA6C,mBAAmB,CAAC,KAAK,CAAC;IACtF,CAAC,UAAA,OAAO;QACN,IAAM,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAC;QAC5C,GAAG,CAAC,CAAiB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO;YAAvB,IAAM,MAAM,gBAAA;YACf,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACjC;QACD,MAAM,CAAkB,MAAc,CAAC;IACzC,CAAC,CAAC;IACF,CAAC,UAAA,OAAO;QACN,IAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,GAAG,CAAC,CAAiB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO;YAAvB,IAAM,MAAM,gBAAA;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;SAC9B;QACD,MAAM,CAAC,MAAwB,CAAC;IAClC,CAAC,CAAC,CAAC;AAEP,mBAAmB,WAAuC;IACxD,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;QAAC,MAAM,CAAC,EAAE,CAAC;IAE5B,IAAM,KAAK,GAAG,WAAkB,CAAC;IAEjC,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAgB,CAAC;IACnD,CAAC;IAED,IAAM,MAAM,GAAgB,EAAE,CAAC;IAE/B,IAAM,GAAG,GAAG,OAAO,KAAK,CAAC,cAAc,KAAK,UAAU;QAClD,UAAC,IAAY,IAAK,OAAA,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAA1B,CAA0B;QAC5C,UAAC,IAAY,IAAK,OAAA,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAb,CAAa,CAAC;IAEpC,GAAG,CAAC,CAAC,IAAM,MAAI,IAAI,KAAK,CAAC,CAAC,CAAC;QACzB,EAAE,CAAC,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAI,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IACD,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED;IAIE,4BAAY,OAA6C,EAAU,OAAoB;QAApB,YAAO,GAAP,OAAO,CAAa;QACrF,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,sBAAI,oCAAI;aAAR,cAAqB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAElD,gCAAG,GAAH,UAAI,GAAW;QACb,IAAM,MAAM,GAAG,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QACzD,MAAM,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;IACtE,CAAC;IAED,gCAAG,GAAH,UAAI,GAAW;QACb,IAAM,KAAK,GAAQ,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACjF,CAAC;IAED,mCAAM,GAAN;QAAA,iBAAwF;QAAnE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,aAAa,CAAC,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC,EAAlC,CAAkC,CAAC,CAAC;IAAC,CAAC;IAC1F,yBAAC;AAAD,CAAC,AA7BD,IA6BC;AAED;IAAA;QACU,QAAG,GAAG,IAAI,GAAG,EAAkB,CAAC;QAChC,YAAO,GAAa,EAAE,CAAC;IA2BjC,CAAC;IAzBC,sBAAI,gCAAI;aAAR,cAAqB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE5C,4BAAG,GAAH,UAAI,GAAW,IAAsB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEhE,4BAAG,GAAH,UAAI,MAAc;QAChB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAG,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,+BAAM,GAAN,UAAO,OAAiB;QACtB,GAAG,CAAC,CAAiB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO;YAAvB,IAAM,MAAM,gBAAA;YACf,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAClB;IACH,CAAC;IAED,4BAAG,GAAH,UAAI,GAAW,IAAa,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvD,+BAAM,GAAN;QACE,iFAAiF;QACjF,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACH,qBAAC;AAAD,CAAC,AA7BD,IA6BC;AAED;IACE,oBAAoB,KAA2B,EAAU,OAAoB;QAAzD,UAAK,GAAL,KAAK,CAAsB;QAAU,YAAO,GAAP,OAAO,CAAa;IAAG,CAAC;IAEjF,sBAAI,4BAAI;aAAR,cAAa,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAExC,wBAAG,GAAH,UAAI,GAAW;QACb,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,IAAI,GAAG,EAAhB,CAAgB,CAAC,CAAC;QACvD,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,wBAAG,GAAH,UAAI,GAAW,IAAa,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,IAAI,GAAG,EAAhB,CAAgB,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;IAEvF,2BAAM,GAAN;QAAA,iBAAyF;QAApE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,UAAU,CAAC,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,EAAlC,CAAkC,CAAC,CAAC;IAAC,CAAC;IAC3F,iBAAC;AAAD,CAAC,AAfD,IAeC;AAED;IAGE,oBAAoB,IAAwB,EAAU,OAAoB;QAAtD,SAAI,GAAJ,IAAI,CAAoB;QAAU,YAAO,GAAP,OAAO,CAAa;IAAG,CAAC;IAE9E,sBAAI,4BAAI;aAAR,cAAqB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAE7C,sBAAI,4BAAI;aAAR,cAA8B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAE9C,sBAAI,gCAAQ;aAAZ,cAAyB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IAE/C,sBAAI,4BAAI;aAAR,cAA+B,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAEnF,sBAAI,iCAAS;aAAb,cAAoC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAEvD,sBAAI,gCAAQ;aAAZ,cAA0B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAExC,sBAAI,gCAAQ;aAAZ,cAA0B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEzC,sBAAI,8BAAM;aAAV,cAAwB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAEtC,sBAAI,kCAAU;aAAd,cAA+B,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAExF,4BAAO,GAAP,cAAyB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEtD,+BAAU,GAAV,cAA4B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7E,oCAAe,GAAf,UAAgB,KAAe;QAC7B,IAAI,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAG,CAAC;QACpE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,EAAE,CAAC,CAAC,aAAa,YAAY,WAAW,CAAC,CAAC,CAAC;gBACzC,IAAI,UAAU,GAAsB,SAAS,CAAC;gBAC9C,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAClB,KAAK,OAAO;wBACV,MAAM,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;4BAC3B,KAAK,YAAY,CAAC;4BAClB,KAAK,SAAS,CAAC;4BACf,KAAK,cAAc;gCACjB,UAAU,GAAG,kBAAkB,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;gCAC1E,KAAK,CAAC;4BACR;gCACE,UAAU,GAAG,oBAAoB,CAAC,qBAAW,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gCACjE,KAAK,CAAC;wBACV,CAAC;wBACD,KAAK,CAAC;oBACR,KAAK,OAAO;wBACV,UAAU,GAAG,kBAAkB,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;wBAC/D,KAAK,CAAC;gBACV,CAAC;gBACD,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;oBACf,SAAS,GAAG,IAAI,uBAAuB,CACnC,SAAS,EAAE,IAAI,WAAW,CAAC,UAAU,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,CAAC,SAAS,CAAC;IACnB,CAAC;IAED,4BAAO,GAAP,UAAQ,QAAgB,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjE,sBAAY,8BAAM;aAAlB;YACE,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;YACxB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACV,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnE,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;oBAChB,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAG,CAAC;gBACpE,CAAC;gBACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACV,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,oBAAoB,CAAC,qBAAW,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YACD,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEO,oCAAe,GAAvB,UAAwB,IAAkB;QACxC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAEO,4CAAuB,GAA/B,UAAgC,WAAsB;QACpD,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAC5E,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,IAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACrD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACd,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AAxFD,IAwFC;AAED,kCAAkC,IAAkB,EAAE,OAAoB;IACxE,IAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChE,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACf,IAAM,YAAY,GAAI,UAAkB,CAAC,MAAM,IAAK,UAAkB,CAAC,MAAM,CAAC;QAC9E,IAAM,SAAO,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACjE,MAAM,CAAC,CAAC,SAAO,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAxB,CAAwB,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED;IAAA;IAMA,CAAC;IALC,sBAAI,4BAAI;aAAR,cAAqB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChC,wBAAG,GAAH,UAAI,GAAW,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IACxD,wBAAG,GAAH,UAAI,GAAW,IAAa,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3C,2BAAM,GAAN,cAAqB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAEnC,iBAAC;AAAD,CAAC,AAND;AAKS,mBAAQ,GAAG,IAAI,UAAU,EAAE,CAAC;AAGrC,sBAAsB,QAAgB;IACpC,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,OAAO,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAClD,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAAC,MAAM,CAAC,SAAS,CAAC;QAC/C,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,SAAS,KAAK,GAAG,CAAC;YAAC,KAAK,CAAC;QAC7B,GAAG,GAAG,SAAS,CAAC;IAClB,CAAC;AACH,CAAC;AAED,0BAA0B,IAAa;IACrC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;QAC/C,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACtE,CAAC;AAED,0CAA0C,IAAa;IACrD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QACtF,IAAI,GAAG,IAAI,CAAC,MAAQ,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,IAAI,CAAC;AACd,CAAC;AAED,8BAA8B,IAAa;IACzC,IAAI,GAAG,gCAAgC,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC;QACpD,IAAI,GAAG,IAAI,CAAC,MAAQ,CAAC;IACvB,CAAC;IAED,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAChE,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;QACpB,IAAI,GAAG,IAAI,CAAC,MAAQ,CAAC;IACvB,CAAC;IAED,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC1D,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAED,yBAAyB,CAAY;IACnC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,IAAI,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAC/D,CAAC;AAED,8BAA8B,IAAiB,EAAE,OAAoB;IACnE,IAAI,IAAa,CAAC;IAClB,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAChC,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACb,KAAK,qBAAW,CAAC,GAAG;YAClB,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CACzB;gBACZ,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,YAAY;gBAChC,UAAU,EAAW,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,EAAC;gBACtD,IAAI,EAAW,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,EAAC;aAChD,EACD,IAAI,CAAC,CAAC,CAAC;YACX,KAAK,CAAC;QACR,KAAK,qBAAW,CAAC,OAAO;YACtB,IAAI;gBACA,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAU,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,EAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5F,KAAK,CAAC;QACR,KAAK,qBAAW,CAAC,IAAI;YACnB,IAAI;gBACA,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAU,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,EAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5F,KAAK,CAAC;QACR,KAAK,qBAAW,CAAC,MAAM;YACrB,IAAM,OAAO,GAAY,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,cAAc,EAAC,CAAC;YAC9D,UAAU,CAAM,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,UAAU,EAAE,OAAO,EAAC,EAAE,IAAI,CAAC,CAAC;YACtF,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,CAAC;QACR,KAAK,qBAAW,CAAC,MAAM;YACrB,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAC5B,UAAU,CAAU,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,6BAA6B,EAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACpF,KAAK,CAAC;QACR,KAAK,qBAAW,CAAC,SAAS;YACxB,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CACzB;gBACZ,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,cAAc;gBAClC,UAAU,EAAW,EAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,cAAc,EAAC;aAC1D,EACD,IAAI,CAAC,CAAC,CAAC;YACX,KAAK,CAAC;QACR;YACE,MAAM,IAAI,KAAK,CAAC,4CAA0C,IAAI,SAAI,qBAAW,CAAC,IAAI,CAAG,CAAC,CAAC;IAC3F,CAAC;IACD,MAAM,CAAC,IAAI,CAAC;AACd,CAAC;AAED,oBAAuC,IAAO,EAAE,MAAe;IAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAvB,CAAuB,CAAC,CAAC;IACxD,MAAM,CAAC,IAAI,CAAC;AACd,CAAC;AAED,gBAAgB,IAAa;IAC3B,MAAM,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;AACtD,CAAC;AAED,gBAAgB,IAAU,EAAE,MAAe;IACzC,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC;QAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,EAAC,CAAC;AAC9D,CAAC;AAED,gBAAgB,UAAyB,EAAE,IAAY,EAAE,MAAc;IACrE,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC;QACnC,IAAM,UAAQ,GAAG,EAAE,CAAC,6BAA6B,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5E,IAAM,SAAS,GAAG,mBAAmB,IAAa;YAChD,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,IAAI,UAAQ,IAAI,IAAI,CAAC,GAAG,GAAG,UAAQ,CAAC,CAAC,CAAC;gBACvF,IAAM,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACpD,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,MAAM,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;QACtD,CAAC;IACH,CAAC;AACH,CAAC;AAED,gCAAgC,MAAiB;IAC/C,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;IACzC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACjB,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,UAAA,WAAW;YACjC,IAAM,UAAU,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/C,MAAM,CAAC;gBACL,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,IAAI,EAAE,EAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,WAAW,CAAC,MAAM,EAAE,EAAC;aACjE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,6BAA6B,IAAa;IACxC,OAAO,IAAI,EAAE,CAAC;QACZ,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB;gBACrC,MAAM,CAAC,IAAI,CAAC;YACd,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,MAAM,CAAC,SAAS,CAAC;QACrB,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,MAAQ,CAAC;IACvB,CAAC;AACH,CAAC;AAED,wBAAwB,MAAiB,EAAE,OAAoB;IAC7D,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1E,GAAG,CAAC,CAAsB,UAAmB,EAAnB,KAAA,MAAM,CAAC,YAAY,EAAnB,cAAmB,EAAnB,IAAmB;YAAxC,IAAM,WAAW,SAAA;YACpB,IAAM,QAAM,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAChD,EAAE,CAAC,CAAC,QAAM,CAAC,CAAC,CAAC;gBACX,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAM,CAAC,CAAC;gBACvD,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACT,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;SACF;IACH,CAAC;AACH,CAAC;AAED,4BAA4B,IAAa,EAAE,IAAY;IACrD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;QACpD,IAAM,aAAa,GAAe,IAAY,CAAC,aAAa,CAAC;QAC7D,EAAE,CAAC,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;AACH,CAAC;AAED,oBAAoB,IAAyB;IAC3C,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACT,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,qBAAW,CAAC,GAAG,CAAC;QACzB,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CACN,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM,CAAC,qBAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxE,MAAM,CAAC,qBAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,qBAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,qBAAW,CAAC,IAAI,CAAC;QAC1B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,mFAAmF;YACnF,IAAI,SAAS,GAAqB,IAAI,CAAC;YACvC,IAAM,SAAS,GAAG,IAAoB,CAAC;YACvC,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC/B,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,GAAG,CAAC,CAAkB,UAAe,EAAf,KAAA,SAAS,CAAC,KAAK,EAAf,cAAe,EAAf,IAAe;oBAAhC,IAAM,OAAO,SAAA;oBAChB,EAAE,CAAC,CAAC,SAAS,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBACrC,MAAM,CAAC,qBAAW,CAAC,KAAK,CAAC;oBAC3B,CAAC;iBACF;YACH,CAAC;YACD,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;gBACtB,MAAM,CAAC,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,qBAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,MAAM,CAAC,qBAAW,CAAC,KAAK,CAAC;AAC3B,CAAC;AAID,4BAA4B,WAA2B,EAAE,GAAW;IAClE,IAAM,KAAK,GAAG,WAAkB,CAAC;IACjC,IAAI,MAA2B,CAAC;IAEhC,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;QACpC,oBAAoB;QACpB,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,4BAA4B;QAC5B,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,mBAAmB,KAAyB;IAC1C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAF,CAAE,CAAC,GAAG,EAAE,CAAC;AACpD,CAAC;AAED,wBAAwB,CAAW,EAAE,CAAW;IAC9C,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAClD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,MAAM,CAAC,CAAC,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM,CAAC,CAAC,CAAC;AACX,CAAC;AAED,6BAA6B,GAAW,EAAE,IAAa;IACrD,IAAM,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;IAExC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC;QACjD,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AotSummaryResolver, CompileMetadataResolver, CompilePipeSummary, CompilerConfig, DEFAULT_INTERPOLATION_CONFIG, DirectiveNormalizer, DirectiveResolver, DomElementSchemaRegistry, HtmlParser, InterpolationConfig, NgAnalyzedModules, NgModuleResolver, ParseTreeResult, PipeResolver, ResourceLoader, StaticReflector, StaticSymbol, StaticSymbolCache, StaticSymbolResolver, SummaryResolver, analyzeNgModules, createOfflineCompileUrlResolver, extractProgramSymbols} from '@angular/compiler';\nimport {ViewEncapsulation, ɵConsole as Console} from '@angular/core';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport {BuiltinType, DeclarationKind, Definition, PipeInfo, Pipes, Signature, Span, Symbol, SymbolDeclaration, SymbolQuery, SymbolTable} from './symbols';\n\n\n// In TypeScript 2.1 these flags moved\n// These helpers work for both 2.0 and 2.1.\nconst isPrivate = (ts as any).ModifierFlags ?\n    ((node: ts.Node) =>\n         !!((ts as any).getCombinedModifierFlags(node) & (ts as any).ModifierFlags.Private)) :\n    ((node: ts.Node) => !!(node.flags & (ts as any).NodeFlags.Private));\n\nconst isReferenceType = (ts as any).ObjectFlags ?\n    ((type: ts.Type) =>\n         !!(type.flags & (ts as any).TypeFlags.Object &&\n            (type as any).objectFlags & (ts as any).ObjectFlags.Reference)) :\n    ((type: ts.Type) => !!(type.flags & (ts as any).TypeFlags.Reference));\n\ninterface TypeContext {\n  node: ts.Node;\n  program: ts.Program;\n  checker: ts.TypeChecker;\n}\n\nexport function getSymbolQuery(\n    program: ts.Program, checker: ts.TypeChecker, source: ts.SourceFile,\n    fetchPipes: () => SymbolTable): SymbolQuery {\n  return new TypeScriptSymbolQuery(program, checker, source, fetchPipes);\n}\n\nexport function getClassMembers(\n    program: ts.Program, checker: ts.TypeChecker, staticSymbol: StaticSymbol): SymbolTable|\n    undefined {\n  const declaration = getClassFromStaticSymbol(program, staticSymbol);\n  if (declaration) {\n    const type = checker.getTypeAtLocation(declaration);\n    const node = program.getSourceFile(staticSymbol.filePath);\n    return new TypeWrapper(type, {node, program, checker}).members();\n  }\n}\n\nexport function getClassMembersFromDeclaration(\n    program: ts.Program, checker: ts.TypeChecker, source: ts.SourceFile,\n    declaration: ts.ClassDeclaration) {\n  const type = checker.getTypeAtLocation(declaration);\n  return new TypeWrapper(type, {node: source, program, checker}).members();\n}\n\nexport function getClassFromStaticSymbol(\n    program: ts.Program, type: StaticSymbol): ts.ClassDeclaration|undefined {\n  const source = program.getSourceFile(type.filePath);\n  if (source) {\n    return ts.forEachChild(source, child => {\n      if (child.kind === ts.SyntaxKind.ClassDeclaration) {\n        const classDeclaration = child as ts.ClassDeclaration;\n        if (classDeclaration.name != null && classDeclaration.name.text === type.name) {\n          return classDeclaration;\n        }\n      }\n    }) as(ts.ClassDeclaration | undefined);\n  }\n\n  return undefined;\n}\n\nexport function getPipesTable(\n    source: ts.SourceFile, program: ts.Program, checker: ts.TypeChecker,\n    pipes: CompilePipeSummary[]): SymbolTable {\n  return new PipesTable(pipes, {program, checker, node: source});\n}\n\nclass TypeScriptSymbolQuery implements SymbolQuery {\n  private typeCache = new Map<BuiltinType, Symbol>();\n  private pipesCache: SymbolTable;\n\n  constructor(\n      private program: ts.Program, private checker: ts.TypeChecker, private source: ts.SourceFile,\n      private fetchPipes: () => SymbolTable) {}\n\n  getTypeKind(symbol: Symbol): BuiltinType { return typeKindOf(this.getTsTypeOf(symbol)); }\n\n  getBuiltinType(kind: BuiltinType): Symbol {\n    let result = this.typeCache.get(kind);\n    if (!result) {\n      const type = getBuiltinTypeFromTs(\n          kind, {checker: this.checker, node: this.source, program: this.program});\n      result =\n          new TypeWrapper(type, {program: this.program, checker: this.checker, node: this.source});\n      this.typeCache.set(kind, result);\n    }\n    return result;\n  }\n\n  getTypeUnion(...types: Symbol[]): Symbol {\n    // No API exists so return any if the types are not all the same type.\n    let result: Symbol|undefined = undefined;\n    if (types.length) {\n      result = types[0];\n      for (let i = 1; i < types.length; i++) {\n        if (types[i] != result) {\n          result = undefined;\n          break;\n        }\n      }\n    }\n    return result || this.getBuiltinType(BuiltinType.Any);\n  }\n\n  getArrayType(type: Symbol): Symbol { return this.getBuiltinType(BuiltinType.Any); }\n\n  getElementType(type: Symbol): Symbol|undefined {\n    if (type instanceof TypeWrapper) {\n      const elementType = getTypeParameterOf(type.tsType, 'Array');\n      if (elementType) {\n        return new TypeWrapper(elementType, type.context);\n      }\n    }\n  }\n\n  getNonNullableType(symbol: Symbol): Symbol {\n    if (symbol instanceof TypeWrapper && (typeof this.checker.getNonNullableType == 'function')) {\n      const tsType = symbol.tsType;\n      const nonNullableType = this.checker.getNonNullableType(tsType);\n      if (nonNullableType != tsType) {\n        return new TypeWrapper(nonNullableType, symbol.context);\n      } else if (nonNullableType == tsType) {\n        return symbol;\n      }\n    }\n    return this.getBuiltinType(BuiltinType.Any);\n  }\n\n  getPipes(): SymbolTable {\n    let result = this.pipesCache;\n    if (!result) {\n      result = this.pipesCache = this.fetchPipes();\n    }\n    return result;\n  }\n\n  getTemplateContext(type: StaticSymbol): SymbolTable|undefined {\n    const context: TypeContext = {node: this.source, program: this.program, checker: this.checker};\n    const typeSymbol = findClassSymbolInContext(type, context);\n    if (typeSymbol) {\n      const contextType = this.getTemplateRefContextType(typeSymbol);\n      if (contextType) return new SymbolWrapper(contextType, context).members();\n    }\n  }\n\n  getTypeSymbol(type: StaticSymbol): Symbol {\n    const context: TypeContext = {node: this.source, program: this.program, checker: this.checker};\n    const typeSymbol = findClassSymbolInContext(type, context) !;\n    return new SymbolWrapper(typeSymbol, context);\n  }\n\n  createSymbolTable(symbols: SymbolDeclaration[]): SymbolTable {\n    const result = new MapSymbolTable();\n    result.addAll(symbols.map(s => new DeclaredSymbol(s)));\n    return result;\n  }\n\n  mergeSymbolTable(symbolTables: SymbolTable[]): SymbolTable {\n    const result = new MapSymbolTable();\n    for (const symbolTable of symbolTables) {\n      result.addAll(symbolTable.values());\n    }\n    return result;\n  }\n\n  getSpanAt(line: number, column: number): Span|undefined {\n    return spanAt(this.source, line, column);\n  }\n\n  private getTemplateRefContextType(typeSymbol: ts.Symbol): ts.Symbol|undefined {\n    const type = this.checker.getTypeOfSymbolAtLocation(typeSymbol, this.source);\n    const constructor = type.symbol && type.symbol.members &&\n        getFromSymbolTable(type.symbol.members !, '__constructor');\n\n    if (constructor) {\n      const constructorDeclaration = constructor.declarations ![0] as ts.ConstructorTypeNode;\n      for (const parameter of constructorDeclaration.parameters) {\n        const type = this.checker.getTypeAtLocation(parameter.type !);\n        if (type.symbol !.name == 'TemplateRef' && isReferenceType(type)) {\n          const typeReference = type as ts.TypeReference;\n          if (typeReference.typeArguments.length === 1) {\n            return typeReference.typeArguments[0].symbol;\n          }\n        }\n      }\n    }\n  }\n\n  private getTsTypeOf(symbol: Symbol): ts.Type|undefined {\n    const type = this.getTypeWrapper(symbol);\n    return type && type.tsType;\n  }\n\n  private getTypeWrapper(symbol: Symbol): TypeWrapper|undefined {\n    let type: TypeWrapper|undefined = undefined;\n    if (symbol instanceof TypeWrapper) {\n      type = symbol;\n    } else if (symbol.type instanceof TypeWrapper) {\n      type = symbol.type;\n    }\n    return type;\n  }\n}\n\nfunction typeCallable(type: ts.Type): boolean {\n  const signatures = type.getCallSignatures();\n  return signatures && signatures.length != 0;\n}\n\nfunction signaturesOf(type: ts.Type, context: TypeContext): Signature[] {\n  return type.getCallSignatures().map(s => new SignatureWrapper(s, context));\n}\n\nfunction selectSignature(type: ts.Type, context: TypeContext, types: Symbol[]): Signature|\n    undefined {\n  // TODO: Do a better job of selecting the right signature.\n  const signatures = type.getCallSignatures();\n  return signatures.length ? new SignatureWrapper(signatures[0], context) : undefined;\n}\n\nclass TypeWrapper implements Symbol {\n  constructor(public tsType: ts.Type, public context: TypeContext) {\n    if (!tsType) {\n      throw Error('Internal: null type');\n    }\n  }\n\n  get name(): string {\n    const symbol = this.tsType.symbol;\n    return (symbol && symbol.name) || '<anonymous>';\n  }\n\n  get kind(): DeclarationKind { return 'type'; }\n\n  get language(): string { return 'typescript'; }\n\n  get type(): Symbol|undefined { return undefined; }\n\n  get container(): Symbol|undefined { return undefined; }\n\n  get public(): boolean { return true; }\n\n  get callable(): boolean { return typeCallable(this.tsType); }\n\n  get nullable(): boolean {\n    return this.context.checker.getNonNullableType(this.tsType) != this.tsType;\n  }\n\n  get definition(): Definition { return definitionFromTsSymbol(this.tsType.getSymbol()); }\n\n  members(): SymbolTable {\n    return new SymbolTableWrapper(this.tsType.getProperties(), this.context);\n  }\n\n  signatures(): Signature[] { return signaturesOf(this.tsType, this.context); }\n\n  selectSignature(types: Symbol[]): Signature|undefined {\n    return selectSignature(this.tsType, this.context, types);\n  }\n\n  indexed(argument: Symbol): Symbol|undefined { return undefined; }\n}\n\nclass SymbolWrapper implements Symbol {\n  private symbol: ts.Symbol;\n  private _tsType: ts.Type;\n  private _members: SymbolTable;\n\n  constructor(symbol: ts.Symbol, private context: TypeContext) {\n    this.symbol = symbol && context && (symbol.flags & ts.SymbolFlags.Alias) ?\n        context.checker.getAliasedSymbol(symbol) :\n        symbol;\n  }\n\n  get name(): string { return this.symbol.name; }\n\n  get kind(): DeclarationKind { return this.callable ? 'method' : 'property'; }\n\n  get language(): string { return 'typescript'; }\n\n  get type(): Symbol|undefined { return new TypeWrapper(this.tsType, this.context); }\n\n  get container(): Symbol|undefined { return getContainerOf(this.symbol, this.context); }\n\n  get public(): boolean {\n    // Symbols that are not explicitly made private are public.\n    return !isSymbolPrivate(this.symbol);\n  }\n\n  get callable(): boolean { return typeCallable(this.tsType); }\n\n  get nullable(): boolean { return false; }\n\n  get definition(): Definition { return definitionFromTsSymbol(this.symbol); }\n\n  members(): SymbolTable {\n    if (!this._members) {\n      if ((this.symbol.flags & (ts.SymbolFlags.Class | ts.SymbolFlags.Interface)) != 0) {\n        const declaredType = this.context.checker.getDeclaredTypeOfSymbol(this.symbol);\n        const typeWrapper = new TypeWrapper(declaredType, this.context);\n        this._members = typeWrapper.members();\n      } else {\n        this._members = new SymbolTableWrapper(this.symbol.members !, this.context);\n      }\n    }\n    return this._members;\n  }\n\n  signatures(): Signature[] { return signaturesOf(this.tsType, this.context); }\n\n  selectSignature(types: Symbol[]): Signature|undefined {\n    return selectSignature(this.tsType, this.context, types);\n  }\n\n  indexed(argument: Symbol): Symbol|undefined { return undefined; }\n\n  private get tsType(): ts.Type {\n    let type = this._tsType;\n    if (!type) {\n      type = this._tsType =\n          this.context.checker.getTypeOfSymbolAtLocation(this.symbol, this.context.node);\n    }\n    return type;\n  }\n}\n\nclass DeclaredSymbol implements Symbol {\n  constructor(private declaration: SymbolDeclaration) {}\n\n  get name() { return this.declaration.name; }\n\n  get kind() { return this.declaration.kind; }\n\n  get language(): string { return 'ng-template'; }\n\n  get container(): Symbol|undefined { return undefined; }\n\n  get type() { return this.declaration.type; }\n\n  get callable(): boolean { return this.declaration.type.callable; }\n\n  get nullable(): boolean { return false; }\n\n  get public(): boolean { return true; }\n\n  get definition(): Definition { return this.declaration.definition; }\n\n  members(): SymbolTable { return this.declaration.type.members(); }\n\n  signatures(): Signature[] { return this.declaration.type.signatures(); }\n\n  selectSignature(types: Symbol[]): Signature|undefined {\n    return this.declaration.type.selectSignature(types);\n  }\n\n  indexed(argument: Symbol): Symbol|undefined { return undefined; }\n}\n\nclass SignatureWrapper implements Signature {\n  constructor(private signature: ts.Signature, private context: TypeContext) {}\n\n  get arguments(): SymbolTable {\n    return new SymbolTableWrapper(this.signature.getParameters(), this.context);\n  }\n\n  get result(): Symbol { return new TypeWrapper(this.signature.getReturnType(), this.context); }\n}\n\nclass SignatureResultOverride implements Signature {\n  constructor(private signature: Signature, private resultType: Symbol) {}\n\n  get arguments(): SymbolTable { return this.signature.arguments; }\n\n  get result(): Symbol { return this.resultType; }\n}\n\nconst toSymbolTable: (symbols: ts.Symbol[]) => ts.SymbolTable = isTypescriptVersion('2.2') ?\n    (symbols => {\n      const result = new Map<string, ts.Symbol>();\n      for (const symbol of symbols) {\n        result.set(symbol.name, symbol);\n      }\n      return <ts.SymbolTable>(result as any);\n    }) :\n    (symbols => {\n      const result = <any>{};\n      for (const symbol of symbols) {\n        result[symbol.name] = symbol;\n      }\n      return result as ts.SymbolTable;\n    });\n\nfunction toSymbols(symbolTable: ts.SymbolTable | undefined): ts.Symbol[] {\n  if (!symbolTable) return [];\n\n  const table = symbolTable as any;\n\n  if (typeof table.values === 'function') {\n    return Array.from(table.values()) as ts.Symbol[];\n  }\n\n  const result: ts.Symbol[] = [];\n\n  const own = typeof table.hasOwnProperty === 'function' ?\n      (name: string) => table.hasOwnProperty(name) :\n      (name: string) => !!table[name];\n\n  for (const name in table) {\n    if (own(name)) {\n      result.push(table[name]);\n    }\n  }\n  return result;\n}\n\nclass SymbolTableWrapper implements SymbolTable {\n  private symbols: ts.Symbol[];\n  private symbolTable: ts.SymbolTable;\n\n  constructor(symbols: ts.SymbolTable|ts.Symbol[]|undefined, private context: TypeContext) {\n    symbols = symbols || [];\n\n    if (Array.isArray(symbols)) {\n      this.symbols = symbols;\n      this.symbolTable = toSymbolTable(symbols);\n    } else {\n      this.symbols = toSymbols(symbols);\n      this.symbolTable = symbols;\n    }\n  }\n\n  get size(): number { return this.symbols.length; }\n\n  get(key: string): Symbol|undefined {\n    const symbol = getFromSymbolTable(this.symbolTable, key);\n    return symbol ? new SymbolWrapper(symbol, this.context) : undefined;\n  }\n\n  has(key: string): boolean {\n    const table: any = this.symbolTable;\n    return (typeof table.has === 'function') ? table.has(key) : table[key] != null;\n  }\n\n  values(): Symbol[] { return this.symbols.map(s => new SymbolWrapper(s, this.context)); }\n}\n\nclass MapSymbolTable implements SymbolTable {\n  private map = new Map<string, Symbol>();\n  private _values: Symbol[] = [];\n\n  get size(): number { return this.map.size; }\n\n  get(key: string): Symbol|undefined { return this.map.get(key); }\n\n  add(symbol: Symbol) {\n    if (this.map.has(symbol.name)) {\n      const previous = this.map.get(symbol.name) !;\n      this._values[this._values.indexOf(previous)] = symbol;\n    }\n    this.map.set(symbol.name, symbol);\n    this._values.push(symbol);\n  }\n\n  addAll(symbols: Symbol[]) {\n    for (const symbol of symbols) {\n      this.add(symbol);\n    }\n  }\n\n  has(key: string): boolean { return this.map.has(key); }\n\n  values(): Symbol[] {\n    // Switch to this.map.values once iterables are supported by the target language.\n    return this._values;\n  }\n}\n\nclass PipesTable implements SymbolTable {\n  constructor(private pipes: CompilePipeSummary[], private context: TypeContext) {}\n\n  get size() { return this.pipes.length; }\n\n  get(key: string): Symbol|undefined {\n    const pipe = this.pipes.find(pipe => pipe.name == key);\n    if (pipe) {\n      return new PipeSymbol(pipe, this.context);\n    }\n  }\n\n  has(key: string): boolean { return this.pipes.find(pipe => pipe.name == key) != null; }\n\n  values(): Symbol[] { return this.pipes.map(pipe => new PipeSymbol(pipe, this.context)); }\n}\n\nclass PipeSymbol implements Symbol {\n  private _tsType: ts.Type;\n\n  constructor(private pipe: CompilePipeSummary, private context: TypeContext) {}\n\n  get name(): string { return this.pipe.name; }\n\n  get kind(): DeclarationKind { return 'pipe'; }\n\n  get language(): string { return 'typescript'; }\n\n  get type(): Symbol|undefined { return new TypeWrapper(this.tsType, this.context); }\n\n  get container(): Symbol|undefined { return undefined; }\n\n  get callable(): boolean { return true; }\n\n  get nullable(): boolean { return false; }\n\n  get public(): boolean { return true; }\n\n  get definition(): Definition { return definitionFromTsSymbol(this.tsType.getSymbol()); }\n\n  members(): SymbolTable { return EmptyTable.instance; }\n\n  signatures(): Signature[] { return signaturesOf(this.tsType, this.context); }\n\n  selectSignature(types: Symbol[]): Signature|undefined {\n    let signature = selectSignature(this.tsType, this.context, types) !;\n    if (types.length == 1) {\n      const parameterType = types[0];\n      if (parameterType instanceof TypeWrapper) {\n        let resultType: ts.Type|undefined = undefined;\n        switch (this.name) {\n          case 'async':\n            switch (parameterType.name) {\n              case 'Observable':\n              case 'Promise':\n              case 'EventEmitter':\n                resultType = getTypeParameterOf(parameterType.tsType, parameterType.name);\n                break;\n              default:\n                resultType = getBuiltinTypeFromTs(BuiltinType.Any, this.context);\n                break;\n            }\n            break;\n          case 'slice':\n            resultType = getTypeParameterOf(parameterType.tsType, 'Array');\n            break;\n        }\n        if (resultType) {\n          signature = new SignatureResultOverride(\n              signature, new TypeWrapper(resultType, parameterType.context));\n        }\n      }\n    }\n    return signature;\n  }\n\n  indexed(argument: Symbol): Symbol|undefined { return undefined; }\n\n  private get tsType(): ts.Type {\n    let type = this._tsType;\n    if (!type) {\n      const classSymbol = this.findClassSymbol(this.pipe.type.reference);\n      if (classSymbol) {\n        type = this._tsType = this.findTransformMethodType(classSymbol) !;\n      }\n      if (!type) {\n        type = this._tsType = getBuiltinTypeFromTs(BuiltinType.Any, this.context);\n      }\n    }\n    return type;\n  }\n\n  private findClassSymbol(type: StaticSymbol): ts.Symbol|undefined {\n    return findClassSymbolInContext(type, this.context);\n  }\n\n  private findTransformMethodType(classSymbol: ts.Symbol): ts.Type|undefined {\n    const classType = this.context.checker.getDeclaredTypeOfSymbol(classSymbol);\n    if (classType) {\n      const transform = classType.getProperty('transform');\n      if (transform) {\n        return this.context.checker.getTypeOfSymbolAtLocation(transform, this.context.node);\n      }\n    }\n  }\n}\n\nfunction findClassSymbolInContext(type: StaticSymbol, context: TypeContext): ts.Symbol|undefined {\n  const sourceFile = context.program.getSourceFile(type.filePath);\n  if (sourceFile) {\n    const moduleSymbol = (sourceFile as any).module || (sourceFile as any).symbol;\n    const exports = context.checker.getExportsOfModule(moduleSymbol);\n    return (exports || []).find(symbol => symbol.name == type.name);\n  }\n}\n\nclass EmptyTable implements SymbolTable {\n  get size(): number { return 0; }\n  get(key: string): Symbol|undefined { return undefined; }\n  has(key: string): boolean { return false; }\n  values(): Symbol[] { return []; }\n  static instance = new EmptyTable();\n}\n\nfunction findTsConfig(fileName: string): string|undefined {\n  let dir = path.dirname(fileName);\n  while (fs.existsSync(dir)) {\n    const candidate = path.join(dir, 'tsconfig.json');\n    if (fs.existsSync(candidate)) return candidate;\n    const parentDir = path.dirname(dir);\n    if (parentDir === dir) break;\n    dir = parentDir;\n  }\n}\n\nfunction isBindingPattern(node: ts.Node): node is ts.BindingPattern {\n  return !!node && (node.kind === ts.SyntaxKind.ArrayBindingPattern ||\n                    node.kind === ts.SyntaxKind.ObjectBindingPattern);\n}\n\nfunction walkUpBindingElementsAndPatterns(node: ts.Node): ts.Node {\n  while (node && (node.kind === ts.SyntaxKind.BindingElement || isBindingPattern(node))) {\n    node = node.parent !;\n  }\n\n  return node;\n}\n\nfunction getCombinedNodeFlags(node: ts.Node): ts.NodeFlags {\n  node = walkUpBindingElementsAndPatterns(node);\n\n  let flags = node.flags;\n  if (node.kind === ts.SyntaxKind.VariableDeclaration) {\n    node = node.parent !;\n  }\n\n  if (node && node.kind === ts.SyntaxKind.VariableDeclarationList) {\n    flags |= node.flags;\n    node = node.parent !;\n  }\n\n  if (node && node.kind === ts.SyntaxKind.VariableStatement) {\n    flags |= node.flags;\n  }\n\n  return flags;\n}\n\nfunction isSymbolPrivate(s: ts.Symbol): boolean {\n  return !!s.valueDeclaration && isPrivate(s.valueDeclaration);\n}\n\nfunction getBuiltinTypeFromTs(kind: BuiltinType, context: TypeContext): ts.Type {\n  let type: ts.Type;\n  const checker = context.checker;\n  const node = context.node;\n  switch (kind) {\n    case BuiltinType.Any:\n      type = checker.getTypeAtLocation(setParents(\n          <ts.Node><any>{\n            kind: ts.SyntaxKind.AsExpression,\n            expression: <ts.Node>{kind: ts.SyntaxKind.TrueKeyword},\n            type: <ts.Node>{kind: ts.SyntaxKind.AnyKeyword}\n          },\n          node));\n      break;\n    case BuiltinType.Boolean:\n      type =\n          checker.getTypeAtLocation(setParents(<ts.Node>{kind: ts.SyntaxKind.TrueKeyword}, node));\n      break;\n    case BuiltinType.Null:\n      type =\n          checker.getTypeAtLocation(setParents(<ts.Node>{kind: ts.SyntaxKind.NullKeyword}, node));\n      break;\n    case BuiltinType.Number:\n      const numeric = <ts.Node>{kind: ts.SyntaxKind.NumericLiteral};\n      setParents(<any>{kind: ts.SyntaxKind.ExpressionStatement, expression: numeric}, node);\n      type = checker.getTypeAtLocation(numeric);\n      break;\n    case BuiltinType.String:\n      type = checker.getTypeAtLocation(\n          setParents(<ts.Node>{kind: ts.SyntaxKind.NoSubstitutionTemplateLiteral}, node));\n      break;\n    case BuiltinType.Undefined:\n      type = checker.getTypeAtLocation(setParents(\n          <ts.Node><any>{\n            kind: ts.SyntaxKind.VoidExpression,\n            expression: <ts.Node>{kind: ts.SyntaxKind.NumericLiteral}\n          },\n          node));\n      break;\n    default:\n      throw new Error(`Internal error, unhandled literal kind ${kind}:${BuiltinType[kind]}`);\n  }\n  return type;\n}\n\nfunction setParents<T extends ts.Node>(node: T, parent: ts.Node): T {\n  node.parent = parent;\n  ts.forEachChild(node, child => setParents(child, node));\n  return node;\n}\n\nfunction spanOf(node: ts.Node): Span {\n  return {start: node.getStart(), end: node.getEnd()};\n}\n\nfunction shrink(span: Span, offset?: number) {\n  if (offset == null) offset = 1;\n  return {start: span.start + offset, end: span.end - offset};\n}\n\nfunction spanAt(sourceFile: ts.SourceFile, line: number, column: number): Span|undefined {\n  if (line != null && column != null) {\n    const position = ts.getPositionOfLineAndCharacter(sourceFile, line, column);\n    const findChild = function findChild(node: ts.Node): ts.Node | undefined {\n      if (node.kind > ts.SyntaxKind.LastToken && node.pos <= position && node.end > position) {\n        const betterNode = ts.forEachChild(node, findChild);\n        return betterNode || node;\n      }\n    };\n\n    const node = ts.forEachChild(sourceFile, findChild);\n    if (node) {\n      return {start: node.getStart(), end: node.getEnd()};\n    }\n  }\n}\n\nfunction definitionFromTsSymbol(symbol: ts.Symbol): Definition {\n  const declarations = symbol.declarations;\n  if (declarations) {\n    return declarations.map(declaration => {\n      const sourceFile = declaration.getSourceFile();\n      return {\n        fileName: sourceFile.fileName,\n        span: {start: declaration.getStart(), end: declaration.getEnd()}\n      };\n    });\n  }\n}\n\nfunction parentDeclarationOf(node: ts.Node): ts.Node|undefined {\n  while (node) {\n    switch (node.kind) {\n      case ts.SyntaxKind.ClassDeclaration:\n      case ts.SyntaxKind.InterfaceDeclaration:\n        return node;\n      case ts.SyntaxKind.SourceFile:\n        return undefined;\n    }\n    node = node.parent !;\n  }\n}\n\nfunction getContainerOf(symbol: ts.Symbol, context: TypeContext): Symbol|undefined {\n  if (symbol.getFlags() & ts.SymbolFlags.ClassMember && symbol.declarations) {\n    for (const declaration of symbol.declarations) {\n      const parent = parentDeclarationOf(declaration);\n      if (parent) {\n        const type = context.checker.getTypeAtLocation(parent);\n        if (type) {\n          return new TypeWrapper(type, context);\n        }\n      }\n    }\n  }\n}\n\nfunction getTypeParameterOf(type: ts.Type, name: string): ts.Type|undefined {\n  if (type && type.symbol && type.symbol.name == name) {\n    const typeArguments: ts.Type[] = (type as any).typeArguments;\n    if (typeArguments && typeArguments.length <= 1) {\n      return typeArguments[0];\n    }\n  }\n}\n\nfunction typeKindOf(type: ts.Type | undefined): BuiltinType {\n  if (type) {\n    if (type.flags & ts.TypeFlags.Any) {\n      return BuiltinType.Any;\n    } else if (\n        type.flags & (ts.TypeFlags.String | ts.TypeFlags.StringLike | ts.TypeFlags.StringLiteral)) {\n      return BuiltinType.String;\n    } else if (type.flags & (ts.TypeFlags.Number | ts.TypeFlags.NumberLike)) {\n      return BuiltinType.Number;\n    } else if (type.flags & (ts.TypeFlags.Undefined)) {\n      return BuiltinType.Undefined;\n    } else if (type.flags & (ts.TypeFlags.Null)) {\n      return BuiltinType.Null;\n    } else if (type.flags & ts.TypeFlags.Union) {\n      // If all the constituent types of a union are the same kind, it is also that kind.\n      let candidate: BuiltinType|null = null;\n      const unionType = type as ts.UnionType;\n      if (unionType.types.length > 0) {\n        candidate = typeKindOf(unionType.types[0]);\n        for (const subType of unionType.types) {\n          if (candidate != typeKindOf(subType)) {\n            return BuiltinType.Other;\n          }\n        }\n      }\n      if (candidate != null) {\n        return candidate;\n      }\n    } else if (type.flags & ts.TypeFlags.TypeParameter) {\n      return BuiltinType.Unbound;\n    }\n  }\n  return BuiltinType.Other;\n}\n\n\n\nfunction getFromSymbolTable(symbolTable: ts.SymbolTable, key: string): ts.Symbol|undefined {\n  const table = symbolTable as any;\n  let symbol: ts.Symbol|undefined;\n\n  if (typeof table.get === 'function') {\n    // TS 2.2 uses a Map\n    symbol = table.get(key);\n  } else {\n    // TS pre-2.2 uses an object\n    symbol = table[key];\n  }\n\n  return symbol;\n}\n\nfunction toNumbers(value: string | undefined): number[] {\n  return value ? value.split('.').map(v => +v) : [];\n}\n\nfunction compareNumbers(a: number[], b: number[]): -1|0|1 {\n  for (let i = 0; i < a.length && i < b.length; i++) {\n    if (a[i] > b[i]) return 1;\n    if (a[i] < b[i]) return -1;\n  }\n  return 0;\n}\n\nfunction isTypescriptVersion(low: string, high?: string): boolean {\n  const tsNumbers = toNumbers(ts.version);\n\n  return compareNumbers(toNumbers(low), tsNumbers) <= 0 &&\n      compareNumbers(toNumbers(high), tsNumbers) >= 0;\n}\n"]}