[{"__symbolic": "module", "version": 3, "metadata": {"PipeResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isPipe": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"PipeResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isPipe": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}]