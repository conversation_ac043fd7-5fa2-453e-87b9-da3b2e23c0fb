{"version": 3, "file": "expression_diagnostics.js", "sourceRoot": "", "sources": ["../../../../../packages/compiler-cli/src/diagnostics/expression_diagnostics.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAEH,8CAAiZ;AAEjZ,qDAAwG;AACxG,qCAA6G;AAiB7G,0CAAiD,IAA4B;IAE3E,IAAM,OAAO,GAAG,IAAI,4BAA4B,CAC5C,IAAI,EAAE,UAAC,IAAqB,EAAE,YAAqB;QACzC,OAAA,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IAA5C,CAA4C,CAAC,CAAC;IAC5D,2BAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;AAC7B,CAAC;AAPD,4EAOC;AAED,kCACI,KAAkB,EAAE,GAAQ,EAAE,KAAkB,EAChD,OAA0C;IAA1C,wBAAA,EAAA,YAA0C;IAC5C,IAAM,QAAQ,GAAG,IAAI,yBAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACpD,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAC7B,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC9B,CAAC;AAND,4DAMC;AAED,uBAAuB,IAA4B;IACjD,IAAM,MAAM,GAAwB,EAAE,CAAC;IAEvC,2BAA2B,UAA0B;gCACxC,SAAS;YAClB,IAAI,IAAI,GAAqB,SAAS,CAAC;YACvC,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,yBAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,CAAC;YACD,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC;gBACxD,IAAI,UAAU,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;aAC7D,CAAC,CAAC;QACL,CAAC;QAXD,GAAG,CAAC,CAAoB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;YAA7B,IAAM,SAAS,mBAAA;oBAAT,SAAS;SAWnB;IACH,CAAC;IAED,IAAM,OAAO,GAAG;QAAkB,2BAA2B;QAAzC;;QASpB,CAAC;QARC,uCAAqB,GAArB,UAAsB,GAAwB,EAAE,OAAY;YAC1D,iBAAM,qBAAqB,YAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC1C,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QACD,8BAAY,GAAZ,UAAa,GAAe,EAAE,OAAY;YACxC,iBAAM,YAAY,YAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACjC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QACH,cAAC;IAAD,CAAC,AATmB,CAAc,sCAA2B,EAS5D,CAAC;IAEF,2BAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAE5C,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,wBAAwB,IAA4B,EAAE,GAAgB;IACpE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClB,IAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;QACnC,MAAM,CAAC,CAAC;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE;oBACJ,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc;oBACnD,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,cAAc;iBAChD;aACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,4BACI,IAA4B,EAAE,IAAqB;IACrD,IAAM,MAAM,GAAwB,EAAE,CAAC;IAEvC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;IACxB,OAAO,OAAO,EAAE,CAAC;QACf,EAAE,CAAC,CAAC,OAAO,YAAY,8BAAmB,CAAC,CAAC,CAAC;oCAChC,QAAQ;gBACjB,IAAM,MAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAE3B,2CAA2C;gBAC3C,IAAM,OAAO,GACT,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAzD,CAAyD,CAAC;qBACjF,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,CAAC;gBAExB,wEAAwE;gBACxE,IAAI,IAAI,GAAqB,SAAS,CAAC;gBACvC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;oBACZ,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC1C,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;wBACV,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBACpB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wBACxC,EAAE,CAAC,CAAC,IAAI,KAAK,qBAAW,CAAC,GAAG,IAAI,IAAI,IAAI,qBAAW,CAAC,OAAO,CAAC,CAAC,CAAC;4BAC5D,oFAAoF;4BACpF,UAAU;4BACV,IAAI,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;wBAClD,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACV,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,QAAA;oBACJ,IAAI,EAAE,UAAU,EAAE,IAAI,MAAA,EAAE,IAAI,UAAU,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACpF,CAAC,CAAC;YACL,CAAC;YA7BD,GAAG,CAAC,CAAmB,UAAiB,EAAjB,KAAA,OAAO,CAAC,SAAS,EAAjB,cAAiB,EAAjB,IAAiB;gBAAnC,IAAM,QAAQ,SAAA;wBAAR,QAAQ;aA6BlB;QACH,CAAC;QACD,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,6BACI,IAAY,EAAE,IAA4B,EAAE,eAAoC;IAClF,mCAAmC;IACnC,IAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC;QACtD,IAAM,IAAI,GAAG,yBAAc,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC;IAC9C,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;QACnB,IAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,IAAI,SAAS,EAA5B,CAA4B,CAAC,CAAC;QACrF,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACnB,IAAM,WAAW,GAAG,IAAI,yBAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC5F,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChB,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBACtD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACX,MAAM,CAAC,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;AACpD,CAAC;AAED,6BAA6B,IAA4B,EAAE,YAAsB;IAC/E,IAAI,MAAM,GAAwB,EAAE,CAAC;IACrC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACjB,gGAAgG;QAChG,gBAAgB;QAChB,MAAM,GAAG,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC;IAClG,CAAC;IACD,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,4BACI,IAA4B,EAAE,IAAqB,EAAE,YAAqB;IAC5E,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;IAC1B,IAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjD,IAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IACvD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3D,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzD,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;IAC7F,CAAC;IACD,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAbD,gDAaC;AAED;IAA2C,gDAA2B;IAMpE,sCACY,IAA4B,EAC5B,kBAAiF;QAF7F,YAGE,iBAAO,SAER;QAJW,UAAI,GAAJ,IAAI,CAAwB;QAC5B,wBAAkB,GAAlB,kBAAkB,CAA+D;QAJ7F,iBAAW,GAA2B,EAAE,CAAC;QAMvC,KAAI,CAAC,IAAI,GAAG,IAAI,kBAAO,CAAc,EAAE,CAAC,CAAC;;IAC3C,CAAC;IAED,qDAAc,GAAd,UAAe,GAAiB,EAAE,OAAY;QAC5C,mFAAmF;QACnF,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YACpC,2BAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,qDAAc,GAAd,UAAe,GAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED,6DAAsB,GAAtB,UAAuB,GAA8B;QACnD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED,2DAAoB,GAApB,UAAqB,GAA4B;QAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED,iDAAU,GAAV,UAAW,GAAkB;QAC3B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7E,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED,oDAAa,GAAb,UAAc,GAAgB;QAC5B,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,EAAE,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3B,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAG,CAAC;YAC/E,EAAE,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC;oBAC9B,IAAI,CAAC,WAAW,CACZ,sDAAsD,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtF,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,IAAI,CAAC,WAAW,CACZ,4DAA0D,GAAG,CAAC,KAAK,MAAG,EACtE,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,mDAAY,GAAZ,UAAa,GAAe,EAAE,OAAY;QACxC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,iBAAM,YAAY,YAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED,4DAAqB,GAArB,UAAsB,GAAwB,EAAE,OAAY;QAC1D,IAAM,wBAAwB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEvD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEf,8CAA8C;QAC9C,IAAI,CAAC,gBAAgB;YACjB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,EAA5B,CAA4B,CAAG,CAAC;QAEnF,mBAAmB;QACnB,iBAAM,qBAAqB,YAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE1C,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACnD,CAAC;IAEO,6DAAsB,GAA9B,UAA+B,GAAgB;QAC7C,IAAM,IAAI,GAAG,mBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,EAAE,CAAC,CAAC,IAAI,YAAY,oBAAS,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAChD,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACzC,CAAC;QACD,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;IACrC,CAAC;IAEO,yDAAkB,GAA1B,UAA2B,GAAQ,EAAE,MAAc,EAAE,YAAqB;QAA1E,iBASC;QARC,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC/D,CAAA,KAAA,IAAI,CAAC,WAAW,CAAA,CAAC,IAAI,WAAI,wBAAwB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACvD,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC;YACJ,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YACvD,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,OAAO,EAAE,CAAC,CAAC,OAAO;SACnB,CAAC,EAJG,CAIH,CAAC,EAAE;;IACpC,CAAC;IAEO,2CAAI,GAAZ,UAAa,GAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE/C,0CAAG,GAAX,cAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAE1B,kDAAW,GAAnB,UAAoB,OAAe,EAAE,IAAoB;QACvD,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,EAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,gCAAc,CAAC,KAAK,EAAE,OAAO,SAAA,EAAC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAEO,oDAAa,GAArB,UAAsB,OAAe,EAAE,IAAU;QAC/C,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,EAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,gCAAc,CAAC,OAAO,EAAE,OAAO,SAAA,EAAC,CAAC,CAAC;IACzF,CAAC;IACH,mCAAC;AAAD,CAAC,AAxHD,CAA2C,sCAA2B,GAwHrE;AAED,8BAA8B,IAAyB;IACrD,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAChB,GAAG,CAAC,CAAc,UAAW,EAAX,KAAA,IAAI,CAAC,MAAM,EAAX,cAAW,EAAX,IAAW;YAAxB,IAAI,KAAK,SAAA;YACZ,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU;gBACrC,yBAAc,CAAC,KAAK,CAAC,KAAO,CAAC,UAAY,CAAC,IAAI,aAAa,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC;SACf;IACH,CAAC;IACD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAED,oBAAoB,IAAU,EAAE,MAAc;IAC5C,MAAM,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,EAAC,CAAC;AAC9D,CAAC;AAED,gBAAgB,UAA2B;IACzC,MAAM,CAAC,EAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,MAAM,EAAC,CAAC;AACtE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AST, AstPath, Attribute, BoundDirectivePropertyAst, BoundElementPropertyAst, BoundEventAst, BoundTextAst, CompileDirectiveSummary, CompileTypeMetadata, DirectiveAst, ElementAst, EmbeddedTemplateAst, Node, ParseSourceSpan, RecursiveTemplateAstVisitor, ReferenceAst, TemplateAst, TemplateAstPath, VariableAst, findNode, identifierName, templateVisitAll, tokenReference} from '@angular/compiler';\n\nimport {AstType, DiagnosticKind, ExpressionDiagnosticsContext, TypeDiagnostic} from './expression_type';\nimport {BuiltinType, Definition, Span, Symbol, SymbolDeclaration, SymbolQuery, SymbolTable} from './symbols';\n\nexport interface DiagnosticTemplateInfo {\n  fileName?: string;\n  offset: number;\n  query: SymbolQuery;\n  members: SymbolTable;\n  htmlAst: Node[];\n  templateAst: TemplateAst[];\n}\n\nexport interface ExpressionDiagnostic {\n  message: string;\n  span: Span;\n  kind: DiagnosticKind;\n}\n\nexport function getTemplateExpressionDiagnostics(info: DiagnosticTemplateInfo):\n    ExpressionDiagnostic[] {\n  const visitor = new ExpressionDiagnosticsVisitor(\n      info, (path: TemplateAstPath, includeEvent: boolean) =>\n                getExpressionScope(info, path, includeEvent));\n  templateVisitAll(visitor, info.templateAst);\n  return visitor.diagnostics;\n}\n\nexport function getExpressionDiagnostics(\n    scope: SymbolTable, ast: AST, query: SymbolQuery,\n    context: ExpressionDiagnosticsContext = {}): TypeDiagnostic[] {\n  const analyzer = new AstType(scope, query, context);\n  analyzer.getDiagnostics(ast);\n  return analyzer.diagnostics;\n}\n\nfunction getReferences(info: DiagnosticTemplateInfo): SymbolDeclaration[] {\n  const result: SymbolDeclaration[] = [];\n\n  function processReferences(references: ReferenceAst[]) {\n    for (const reference of references) {\n      let type: Symbol|undefined = undefined;\n      if (reference.value) {\n        type = info.query.getTypeSymbol(tokenReference(reference.value));\n      }\n      result.push({\n        name: reference.name,\n        kind: 'reference',\n        type: type || info.query.getBuiltinType(BuiltinType.Any),\n        get definition() { return getDefintionOf(info, reference); }\n      });\n    }\n  }\n\n  const visitor = new class extends RecursiveTemplateAstVisitor {\n    visitEmbeddedTemplate(ast: EmbeddedTemplateAst, context: any): any {\n      super.visitEmbeddedTemplate(ast, context);\n      processReferences(ast.references);\n    }\n    visitElement(ast: ElementAst, context: any): any {\n      super.visitElement(ast, context);\n      processReferences(ast.references);\n    }\n  };\n\n  templateVisitAll(visitor, info.templateAst);\n\n  return result;\n}\n\nfunction getDefintionOf(info: DiagnosticTemplateInfo, ast: TemplateAst): Definition|undefined {\n  if (info.fileName) {\n    const templateOffset = info.offset;\n    return [{\n      fileName: info.fileName,\n      span: {\n        start: ast.sourceSpan.start.offset + templateOffset,\n        end: ast.sourceSpan.end.offset + templateOffset\n      }\n    }];\n  }\n}\n\nfunction getVarDeclarations(\n    info: DiagnosticTemplateInfo, path: TemplateAstPath): SymbolDeclaration[] {\n  const result: SymbolDeclaration[] = [];\n\n  let current = path.tail;\n  while (current) {\n    if (current instanceof EmbeddedTemplateAst) {\n      for (const variable of current.variables) {\n        const name = variable.name;\n\n        // Find the first directive with a context.\n        const context =\n            current.directives.map(d => info.query.getTemplateContext(d.directive.type.reference))\n                .find(c => !!c);\n\n        // Determine the type of the context field referenced by variable.value.\n        let type: Symbol|undefined = undefined;\n        if (context) {\n          const value = context.get(variable.value);\n          if (value) {\n            type = value.type !;\n            let kind = info.query.getTypeKind(type);\n            if (kind === BuiltinType.Any || kind == BuiltinType.Unbound) {\n              // The any type is not very useful here. For special cases, such as ngFor, we can do\n              // better.\n              type = refinedVariableType(type, info, current);\n            }\n          }\n        }\n        if (!type) {\n          type = info.query.getBuiltinType(BuiltinType.Any);\n        }\n        result.push({\n          name,\n          kind: 'variable', type, get definition() { return getDefintionOf(info, variable); }\n        });\n      }\n    }\n    current = path.parentOf(current);\n  }\n\n  return result;\n}\n\nfunction refinedVariableType(\n    type: Symbol, info: DiagnosticTemplateInfo, templateElement: EmbeddedTemplateAst): Symbol {\n  // Special case the ngFor directive\n  const ngForDirective = templateElement.directives.find(d => {\n    const name = identifierName(d.directive.type);\n    return name == 'NgFor' || name == 'NgForOf';\n  });\n  if (ngForDirective) {\n    const ngForOfBinding = ngForDirective.inputs.find(i => i.directiveName == 'ngForOf');\n    if (ngForOfBinding) {\n      const bindingType = new AstType(info.members, info.query, {}).getType(ngForOfBinding.value);\n      if (bindingType) {\n        const result = info.query.getElementType(bindingType);\n        if (result) {\n          return result;\n        }\n      }\n    }\n  }\n\n  // We can't do better, return any\n  return info.query.getBuiltinType(BuiltinType.Any);\n}\n\nfunction getEventDeclaration(info: DiagnosticTemplateInfo, includeEvent?: boolean) {\n  let result: SymbolDeclaration[] = [];\n  if (includeEvent) {\n    // TODO: Determine the type of the event parameter based on the Observable<T> or EventEmitter<T>\n    // of the event.\n    result = [{name: '$event', kind: 'variable', type: info.query.getBuiltinType(BuiltinType.Any)}];\n  }\n  return result;\n}\n\nexport function getExpressionScope(\n    info: DiagnosticTemplateInfo, path: TemplateAstPath, includeEvent: boolean): SymbolTable {\n  let result = info.members;\n  const references = getReferences(info);\n  const variables = getVarDeclarations(info, path);\n  const events = getEventDeclaration(info, includeEvent);\n  if (references.length || variables.length || events.length) {\n    const referenceTable = info.query.createSymbolTable(references);\n    const variableTable = info.query.createSymbolTable(variables);\n    const eventsTable = info.query.createSymbolTable(events);\n    result = info.query.mergeSymbolTable([result, referenceTable, variableTable, eventsTable]);\n  }\n  return result;\n}\n\nclass ExpressionDiagnosticsVisitor extends RecursiveTemplateAstVisitor {\n  private path: TemplateAstPath;\n  private directiveSummary: CompileDirectiveSummary;\n\n  diagnostics: ExpressionDiagnostic[] = [];\n\n  constructor(\n      private info: DiagnosticTemplateInfo,\n      private getExpressionScope: (path: TemplateAstPath, includeEvent: boolean) => SymbolTable) {\n    super();\n    this.path = new AstPath<TemplateAst>([]);\n  }\n\n  visitDirective(ast: DirectiveAst, context: any): any {\n    // Override the default child visitor to ignore the host properties of a directive.\n    if (ast.inputs && ast.inputs.length) {\n      templateVisitAll(this, ast.inputs, context);\n    }\n  }\n\n  visitBoundText(ast: BoundTextAst): void {\n    this.push(ast);\n    this.diagnoseExpression(ast.value, ast.sourceSpan.start.offset, false);\n    this.pop();\n  }\n\n  visitDirectiveProperty(ast: BoundDirectivePropertyAst): void {\n    this.push(ast);\n    this.diagnoseExpression(ast.value, this.attributeValueLocation(ast), false);\n    this.pop();\n  }\n\n  visitElementProperty(ast: BoundElementPropertyAst): void {\n    this.push(ast);\n    this.diagnoseExpression(ast.value, this.attributeValueLocation(ast), false);\n    this.pop();\n  }\n\n  visitEvent(ast: BoundEventAst): void {\n    this.push(ast);\n    this.diagnoseExpression(ast.handler, this.attributeValueLocation(ast), true);\n    this.pop();\n  }\n\n  visitVariable(ast: VariableAst): void {\n    const directive = this.directiveSummary;\n    if (directive && ast.value) {\n      const context = this.info.query.getTemplateContext(directive.type.reference) !;\n      if (context && !context.has(ast.value)) {\n        if (ast.value === '$implicit') {\n          this.reportError(\n              'The template context does not have an implicit value', spanOf(ast.sourceSpan));\n        } else {\n          this.reportError(\n              `The template context does not defined a member called '${ast.value}'`,\n              spanOf(ast.sourceSpan));\n        }\n      }\n    }\n  }\n\n  visitElement(ast: ElementAst, context: any): void {\n    this.push(ast);\n    super.visitElement(ast, context);\n    this.pop();\n  }\n\n  visitEmbeddedTemplate(ast: EmbeddedTemplateAst, context: any): any {\n    const previousDirectiveSummary = this.directiveSummary;\n\n    this.push(ast);\n\n    // Find directive that refernces this template\n    this.directiveSummary =\n        ast.directives.map(d => d.directive).find(d => hasTemplateReference(d.type)) !;\n\n    // Process children\n    super.visitEmbeddedTemplate(ast, context);\n\n    this.pop();\n\n    this.directiveSummary = previousDirectiveSummary;\n  }\n\n  private attributeValueLocation(ast: TemplateAst) {\n    const path = findNode(this.info.htmlAst, ast.sourceSpan.start.offset);\n    const last = path.tail;\n    if (last instanceof Attribute && last.valueSpan) {\n      // Add 1 for the quote.\n      return last.valueSpan.start.offset + 1;\n    }\n    return ast.sourceSpan.start.offset;\n  }\n\n  private diagnoseExpression(ast: AST, offset: number, includeEvent: boolean) {\n    const scope = this.getExpressionScope(this.path, includeEvent);\n    this.diagnostics.push(...getExpressionDiagnostics(scope, ast, this.info.query, {\n                            event: includeEvent\n                          }).map(d => ({\n                                   span: offsetSpan(d.ast.span, offset + this.info.offset),\n                                   kind: d.kind,\n                                   message: d.message\n                                 })));\n  }\n\n  private push(ast: TemplateAst) { this.path.push(ast); }\n\n  private pop() { this.path.pop(); }\n\n  private reportError(message: string, span: Span|undefined) {\n    if (span) {\n      this.diagnostics.push(\n          {span: offsetSpan(span, this.info.offset), kind: DiagnosticKind.Error, message});\n    }\n  }\n\n  private reportWarning(message: string, span: Span) {\n    this.diagnostics.push(\n        {span: offsetSpan(span, this.info.offset), kind: DiagnosticKind.Warning, message});\n  }\n}\n\nfunction hasTemplateReference(type: CompileTypeMetadata): boolean {\n  if (type.diDeps) {\n    for (let diDep of type.diDeps) {\n      if (diDep.token && diDep.token.identifier &&\n          identifierName(diDep.token !.identifier !) == 'TemplateRef')\n        return true;\n    }\n  }\n  return false;\n}\n\nfunction offsetSpan(span: Span, amount: number): Span {\n  return {start: span.start + amount, end: span.end + amount};\n}\n\nfunction spanOf(sourceSpan: ParseSourceSpan): Span {\n  return {start: sourceSpan.start.offset, end: sourceSpan.end.offset};\n}"]}