[{"__symbolic": "module", "version": 3, "metadata": {"RouterUpgradeInitializer": {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_BOOTSTRAP_LISTENER"}, "multi": true, "useFactory": {"__symbolic": "reference", "name": "locationSyncBootstrapListener"}, "deps": [{"__symbolic": "reference", "module": "@angular/upgrade/static", "name": "UpgradeModule"}]}, "locationSyncBootstrapListener": {"__symbolic": "function", "parameters": ["ngUpgrade"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 48, "character": 9}}, "setUpLocationSync": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"RouterUpgradeInitializer": {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_BOOTSTRAP_LISTENER"}, "multi": true, "useFactory": {"__symbolic": "reference", "name": "locationSyncBootstrapListener"}, "deps": [{"__symbolic": "reference", "module": "@angular/upgrade/static", "name": "UpgradeModule"}]}, "locationSyncBootstrapListener": {"__symbolic": "function", "parameters": ["ngUpgrade"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 48, "character": 9}}, "setUpLocationSync": {"__symbolic": "function"}}}]