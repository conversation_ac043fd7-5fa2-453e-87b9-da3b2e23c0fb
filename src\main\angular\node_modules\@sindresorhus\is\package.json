{"_args": [["@sindresorhus/is@0.7.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@sindresorhus/is@0.7.0", "_id": "@sindresorhus/is@0.7.0", "_inBundle": false, "_integrity": "sha512-ONhaKPIufzzrlNbqtWFFd+jlnemX6lJAgq9ZeiZtS7I1PIf/la7CW4m83rTXRnVnsMbW2k56pGYu7AUFJD9Pow==", "_location": "/@sindresorhus/is", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@sindresorhus/is@0.7.0", "name": "@sindresorhus/is", "escapedName": "@sindresorhus%2fis", "scope": "@sindresorhus", "rawSpec": "0.7.0", "saveSpec": null, "fetchSpec": "0.7.0"}, "_requiredBy": ["/download/got"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@sindresorhus/is/-/is-0.7.0.tgz", "_spec": "0.7.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "description": "Type check values: `is.string('🦄') //=> true`", "devDependencies": {"@types/jsdom": "^2.0.31", "@types/node": "^8.0.47", "@types/tempy": "^0.1.0", "ava": "*", "del-cli": "^1.1.0", "jsdom": "^9.12.0", "tempy": "^0.2.1", "tslint": "^5.8.0", "tslint-xo": "^0.3.0", "typescript": "^2.6.1"}, "engines": {"node": ">=4"}, "files": ["dist"], "homepage": "https://github.com/sindresorhus/is#readme", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "license": "MIT", "main": "dist/index.js", "name": "@sindresorhus/is", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is.git"}, "scripts": {"build": "tsc", "lint": "tslint --format stylish --project .", "prepublish": "npm run build && del dist/tests", "test": "npm run lint && npm run build && ava dist/tests"}, "types": "dist/index.d.ts", "version": "0.7.0"}