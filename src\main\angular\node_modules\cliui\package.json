{"_args": [["cliui@3.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cliui@3.2.0", "_id": "cliui@3.2.0", "_inBundle": false, "_integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "_location": "/cliui", "_phantomChildren": {"code-point-at": "1.1.0", "number-is-nan": "1.0.1", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "cliui@3.2.0", "name": "cliui", "escapedName": "cliui", "rawSpec": "3.2.0", "saveSpec": null, "fetchSpec": "3.2.0"}, "_requiredBy": ["/webpack-dev-server/yargs"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cliui/-/cliui-3.2.0.tgz", "_spec": "3.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"chai": "^3.5.0", "chalk": "^1.1.2", "coveralls": "^2.11.8", "mocha": "^2.4.5", "nyc": "^6.4.0", "standard": "^6.0.8", "standard-version": "^2.1.2"}, "files": ["index.js"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "index.js", "name": "cliui", "repository": {"type": "git", "url": "git+ssh://**************/yargs/cliui.git"}, "scripts": {"coverage": "nyc --reporter=text-lcov mocha | coveralls", "pretest": "standard", "test": "nyc mocha", "version": "standard-version"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "3.2.0"}