{"_args": [["center-align@0.1.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "center-align@0.1.3", "_id": "center-align@0.1.3", "_inBundle": false, "_integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "_location": "/center-align", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "center-align@0.1.3", "name": "center-align", "escapedName": "center-align", "rawSpec": "0.1.3", "saveSpec": null, "fetchSpec": "0.1.3"}, "_requiredBy": ["/istanbul-reports/cliui", "/webpack/cliui"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/center-align/-/center-align-0.1.3.tgz", "_spec": "0.1.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/center-align/issues"}, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "description": "Center-align the text in a string.", "devDependencies": {"mocha": "^2.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "utils.js"], "homepage": "https://github.com/jonschlinkert/center-align", "keywords": ["align", "align-center", "center", "center-align", "right", "right-align", "text", "typography"], "license": "MIT", "main": "index.js", "name": "center-align", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/center-align.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"description": "", "list": ["align-text", "right-align", "justified", "word-wrap"]}}, "version": "0.1.3"}