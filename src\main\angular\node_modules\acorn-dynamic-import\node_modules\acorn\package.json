{"_args": [["acorn@4.0.13", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "acorn@4.0.13", "_id": "acorn@4.0.13", "_inBundle": false, "_integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=", "_location": "/acorn-dynamic-import/acorn", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "acorn@4.0.13", "name": "acorn", "escapedName": "acorn", "rawSpec": "4.0.13", "saveSpec": null, "fetchSpec": "4.0.13"}, "_requiredBy": ["/acorn-dynamic-import"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/acorn/-/acorn-4.0.13.tgz", "_spec": "4.0.13", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bin": {"acorn": "bin/acorn"}, "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "description": "ECMAScript parser", "devDependencies": {"rollup": "^0.34.1", "rollup-plugin-buble": "^0.11.0", "unicode-9.0.0": "^0.7.0"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/ternjs/acorn", "jsnext:main": "dist/acorn.es.js", "license": "MIT", "main": "dist/acorn.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}], "name": "acorn", "repository": {"type": "git", "url": "git+https://github.com/ternjs/acorn.git"}, "scripts": {"build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "build:bin": "rollup -c rollup/config.bin.js", "build:loose": "rollup -c rollup/config.loose.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "pretest": "npm run build", "test": "node test/run.js"}, "version": "4.0.13"}