{"version": 3, "file": "router-testing.umd.js", "sources": ["../../../../packages/router/testing/src/testing.ts", "../../../../packages/router/testing/src/router_testing_module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\nexport * from './router_testing_module';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {MockLocationStrategy, SpyLocation} from '@angular/common/testing';\nimport {Compiler, Injectable, Injector, ModuleWithProviders, NgModule, NgModuleFactory, NgModuleFactoryLoader, Optional} from '@angular/core';\nimport {ChildrenOutletContexts, NoPreloading, PreloadingStrategy, ROUTES, Route, Router, RouterModule, Routes, UrlHandlingStrategy, UrlSerializer, provideRoutes, ɵROUTER_PROVIDERS as ROUTER_PROVIDERS, ɵflatten as flatten} from '@angular/router';\n\n\n\n/**\n * @whatItDoes Allows to simulate the loading of ng modules in tests.\n *\n * @howToUse\n *\n * ```\n * const loader = TestBed.get(NgModuleFactoryLoader);\n *\n * @Component({template: 'lazy-loaded'})\n * class LazyLoadedComponent {}\n * @NgModule({\n *   declarations: [LazyLoadedComponent],\n *   imports: [RouterModule.forChild([{path: 'loaded', component: LazyLoadedComponent}])]\n * })\n *\n * class LoadedModule {}\n *\n * // sets up stubbedModules\n * loader.stubbedModules = {lazyModule: LoadedModule};\n *\n * router.resetConfig([\n *   {path: 'lazy', loadChildren: 'lazyModule'},\n * ]);\n *\n * router.navigateByUrl('/lazy/loaded');\n * ```\n *\n * @stable\n */\n\nexport class SpyNgModuleFactoryLoader implements NgModuleFactoryLoader {\n  /**\n   * @docsNotRequired\n   */\n  private _stubbedModules: {[path: string]: Promise<NgModuleFactory<any>>} = {};\n\n  /**\n   * @docsNotRequired\n   */\n  set stubbedModules(modules: {[path: string]: any}) {\n    const res: {[path: string]: any} = {};\n    for (const t of Object.keys(modules)) {\n      res[t] = this.compiler.compileModuleAsync(modules[t]);\n    }\n    this._stubbedModules = res;\n  }\n\n  /**\n   * @docsNotRequired\n   */\n  get stubbedModules(): {[path: string]: any} { return this._stubbedModules; }\n\n  constructor(private compiler: Compiler) {}\n\n  load(path: string): Promise<NgModuleFactory<any>> {\n    if (this._stubbedModules[path]) {\n      return this._stubbedModules[path];\n    } else {\n      return <any>Promise.reject(new Error(`Cannot find module ${path}`));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Compiler, },\n];\n}\n\n/**\n * Router setup factory function used for testing.\n *\n * @stable\n */\nexport function setupTestingRouter(\n    urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts, location: Location,\n    loader: NgModuleFactoryLoader, compiler: Compiler, injector: Injector, routes: Route[][],\n    urlHandlingStrategy?: UrlHandlingStrategy) {\n  const router = new Router(\n      null !, urlSerializer, contexts, location, injector, loader, compiler, flatten(routes));\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n  return router;\n}\n\n/**\n * @whatItDoes Sets up the router to be used for testing.\n *\n * @howToUse\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}])]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @description\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of {@link Location}, {@link LocationStrategy}, and {@link\n * NgModuleFactoryLoader}.\n *\n * @stable\n */\n\nexport class RouterTestingModule {\n  static withRoutes(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterTestingModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [RouterModule],\n  providers: [\n    ROUTER_PROVIDERS, {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n    {provide: NgModuleFactoryLoader, useClass: SpyNgModuleFactoryLoader}, {\n      provide: Router,\n      useFactory: setupTestingRouter,\n      deps: [\n        UrlSerializer, ChildrenOutletContexts, Location, NgModuleFactoryLoader, Compiler, Injector,\n        ROUTES, [UrlHandlingStrategy, new Optional()]\n      ]\n    },\n    {provide: PreloadingStrategy, useExisting: NoPreloading}, provideRoutes([])\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["PreloadingStrategy", "NoPreloading", "provideRoutes", "ROUTES", "UrlHandlingStrategy", "Optional", "UrlSerializer", "ChildrenOutletContexts", "Location", "NgModuleFactoryLoader", "Compiler", "Injector", "Router", "LocationStrategy", "MockLocationStrategy", "ɵROUTER_PROVIDERS", "SpyLocation", "RouterModule", "NgModule", "ROUTER_PROVIDERS", "ɵflatten", "Injectable"], "mappings": ";;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDA,SAAA,wBAAA,CAAA,QAAA,EAAA;QAkBA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;QAbM,IAAN,CAAA,eAAA,GAAA,EAAA,CAAA;KACA;IAIA,MAAA,CAAA,cAAA,CAAI,wBAAJ,CAAA,SAAA,EAAA,gBAAA,EAAA;;;;QAaA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,eAAA,CAAA,EAAA;;;;QAbA,GAAA,EAAA,UAAA,OAAA,EAAA;YACA,IAAA,GAAA,GAAA,EAAA,CAAA;;;;;YAKM,IAAN,CAAA,eAAA,GAAA,GAAuD,CAAvD;SAIA;;;KAAA,CAAA,CAAA;IAIA,wBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,EAAA;YACA,OAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,CAAA;;aACA;YACQ,OAAR,OAAA,CAAA,MAAA,CAAA,IAAA,KAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,CAAA,CAAA;SACA;;IAEO,OAAP,wBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,wBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAqB,wBAAA,EAAA;;;;;;;;;;;AAiBA,SAAA,kBAAA,CAAA,aAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,mBAAA,EAAA;IACA,IAAA,MAAA,GAAA,IAAAT,sBAAA,CAAA,IAAA,EAAA,aAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAAQ,wBAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCO,IAAP,mBAAA,IAAA,YAAA;IAAO,SAAP,mBAAA,GAAA;KAIAD;IAHA,mBAAA,CAAA,UAAA,GAAA,UAAkB,MAAM,EAAxB;QACA,OAAA,EAAA,QAAW,EAAX,mBAAA,EAAA,SAAA,EAAA,CAAAjB,6BAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;KACA,CAAA;IACAiB,OAAAA,mBAAAA,CAAAA;CAAAA,EAAAA,CAAAA,CAAAA;AACA,mBAAA,CAAI,UAAJ,GAAA;IACA,EAAA,IAAA,EAAAD,sBAAI,EAAC,IAAL,EAAA,CAAY;gBACZ,OAAA,EAAA,CAAAD,4BAAA,CAAqB;gBACrB,SAAA,EAAA;oBACAF,iCAAA,EAAA,EAAA,OAAA,EAAAP,wBAAA,EAAA,QAAA,EAAAQ,mCAAA,EAAA;oBACA,EAAA,OAAA,EAAAH,gCAAA,EAAA,QAAA,EAAAC,4CAAA,EAAA;oBACA,EAAA,OAAA,EAAAL,mCAAA,EAAA,QAA0C,EAA1C,wBAAA,EAAA,EAAA;wBACA,OAAA,EAAAG,sBAAA;wBACA,UAAA,EAAA,kBAAA;wBACA,IAAA,EAAA;4BACAN,6BAAA,EAAAC,sCAAA,EAAAC,wBAAA,EAAAC,mCAAA,EAAAC,sBAAA,EAAAC,sBAAA;4BACAR,sBAAA,EAAA,CAAAC,mCAAA,EAAA,IAAAC,sBAAA,EAAA,CAAA;yBACA;;oBAEA,EAAA,OAAA,EAAAL,kCAAA,EAAA,WAAA,EAAAC,4BAAA,EAAA,EAAAC,6BAAA,CAAA,EAAA,CAAA;;aDvJA,EAAA,EAAA;;;;;;;;;;;"}