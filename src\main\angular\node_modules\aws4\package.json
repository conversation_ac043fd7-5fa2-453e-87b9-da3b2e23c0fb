{"_args": [["aws4@1.6.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "aws4@1.6.0", "_id": "aws4@1.6.0", "_inBundle": false, "_integrity": "sha1-g+9cqGCysy5KDe7e6MdxudtXRx4=", "_location": "/aws4", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "aws4@1.6.0", "name": "aws4", "escapedName": "aws4", "rawSpec": "1.6.0", "saveSpec": null, "fetchSpec": "1.6.0"}, "_requiredBy": ["/request"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/aws4/-/aws4-1.6.0.tgz", "_spec": "1.6.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/mhart"}, "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "description": "Signs and prepares requests using AWS Signature Version 4", "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "homepage": "https://github.com/mhart/aws4#readme", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "license": "MIT", "main": "aws4.js", "name": "aws4", "repository": {"type": "git", "url": "git+https://github.com/mhart/aws4.git"}, "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "version": "1.6.0"}