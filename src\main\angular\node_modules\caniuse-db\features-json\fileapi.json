{"title": "File API", "description": "Method of manipulating file objects in web applications client-side, as well as programmatically selecting them and accessing their data.", "spec": "http://www.w3.org/TR/FileAPI/", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en/Using_files_from_web_applications", "title": "Mozilla Developer Network (MDN) documentation - Using Files"}, {"url": "https://www.webplatform.org/docs/apis/file", "title": "WebPlatform Docs"}, {"url": "https://github.com/moxiecode/moxie", "title": "Polyfill"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a #2", "11": "a #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "a #2", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "a #1 #2", "7": "a #1 #2", "8": "a #1 #2", "9": "a #1 #2", "10": "a #1 #2", "11": "a #1 #2", "12": "a #1 #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "a #2", "37": "a #2", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "a #1 #2", "6": "a #2", "6.1": "a #2", "7": "a #2", "7.1": "a #2", "8": "a #2", "9": "a #2", "9.1": "a #2", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "a #2", "11.5": "a #2", "11.6": "a #2", "12": "a #2", "12.1": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "a #2", "7.0-7.1": "a #2", "8": "a #2", "8.1-8.4": "a #2", "9.0-9.2": "a #2", "9.3": "a #2", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a #1 #2", "4": "a #1 #2", "4.1": "a #1 #2", "4.2-4.3": "a #1 #2", "4.4": "a #2", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "a #1 #2", "10": "a #2"}, "op_mob": {"10": "n", "11": "n", "11.1": "a #2", "11.5": "a #2", "12": "a #2", "12.1": "a #2", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "a #2"}, "and_uc": {"11.4": "a #2"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not have `FileReader` support. ", "2": "Does not support the `File` constructor"}, "usage_perc_y": 76.61, "usage_perc_a": 17.68, "ucprefix": false, "parent": "", "keywords": "FileReader", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}