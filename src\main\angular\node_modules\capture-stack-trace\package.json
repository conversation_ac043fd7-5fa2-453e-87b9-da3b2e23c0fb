{"_args": [["capture-stack-trace@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "capture-stack-trace@1.0.0", "_id": "capture-stack-trace@1.0.0", "_inBundle": false, "_integrity": "sha1-Sm+gc5nCa7pH8LJJa00PtAjFVQ0=", "_location": "/capture-stack-trace", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "capture-stack-trace@1.0.0", "name": "capture-stack-trace", "escapedName": "capture-stack-trace", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/create-error-class"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/capture-stack-trace/-/capture-stack-trace-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/capture-stack-trace/issues"}, "dependencies": {}, "description": "Error.captureStackTrace ponyfill", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/capture-stack-trace#readme", "keywords": ["Error", "captureStackTrace"], "license": "MIT", "name": "capture-stack-trace", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/capture-stack-trace.git"}, "scripts": {"test": "mocha"}, "version": "1.0.0"}