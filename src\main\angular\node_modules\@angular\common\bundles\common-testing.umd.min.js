/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core"),require("@angular/common")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/common"],factory):factory((global.ng=global.ng||{},global.ng.common=global.ng.common||{},global.ng.common.testing=global.ng.common.testing||{}),global.ng.core,global.ng.common)}(this,function(exports,_angular_core,_angular_common){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},SpyLocation=function(){function SpyLocation(){this.urlChanges=[],this._history=[new LocationState("","")],this._historyIndex=0,this._subject=new _angular_core.EventEmitter,this._baseHref="",this._platformStrategy=null}return SpyLocation.prototype.setInitialPath=function(url){this._history[this._historyIndex].path=url},SpyLocation.prototype.setBaseHref=function(url){this._baseHref=url},SpyLocation.prototype.path=function(){return this._history[this._historyIndex].path},SpyLocation.prototype.isCurrentPathEqualTo=function(path,query){void 0===query&&(query="");var givenPath=path.endsWith("/")?path.substring(0,path.length-1):path,currPath=this.path().endsWith("/")?this.path().substring(0,this.path().length-1):this.path();return currPath==givenPath+(query.length>0?"?"+query:"")},SpyLocation.prototype.simulateUrlPop=function(pathname){this._subject.emit({url:pathname,pop:!0})},SpyLocation.prototype.simulateHashChange=function(pathname){this.setInitialPath(pathname),this.urlChanges.push("hash: "+pathname),this._subject.emit({url:pathname,pop:!0,type:"hashchange"})},SpyLocation.prototype.prepareExternalUrl=function(url){return url.length>0&&!url.startsWith("/")&&(url="/"+url),this._baseHref+url},SpyLocation.prototype.go=function(path,query){void 0===query&&(query=""),path=this.prepareExternalUrl(path),this._historyIndex>0&&this._history.splice(this._historyIndex+1),this._history.push(new LocationState(path,query)),this._historyIndex=this._history.length-1;var locationState=this._history[this._historyIndex-1];if(locationState.path!=path||locationState.query!=query){var url=path+(query.length>0?"?"+query:"");this.urlChanges.push(url),this._subject.emit({url:url,pop:!1})}},SpyLocation.prototype.replaceState=function(path,query){void 0===query&&(query=""),path=this.prepareExternalUrl(path);var history=this._history[this._historyIndex];if(history.path!=path||history.query!=query){history.path=path,history.query=query;var url=path+(query.length>0?"?"+query:"");this.urlChanges.push("replace: "+url)}},SpyLocation.prototype.forward=function(){this._historyIndex<this._history.length-1&&(this._historyIndex++,this._subject.emit({url:this.path(),pop:!0}))},SpyLocation.prototype.back=function(){this._historyIndex>0&&(this._historyIndex--,this._subject.emit({url:this.path(),pop:!0}))},SpyLocation.prototype.subscribe=function(onNext,onThrow,onReturn){return this._subject.subscribe({next:onNext,error:onThrow,complete:onReturn})},SpyLocation.prototype.normalize=function(url){return null},SpyLocation}();SpyLocation.decorators=[{type:_angular_core.Injectable}],SpyLocation.ctorParameters=function(){return[]};var LocationState=function(){function LocationState(path,query){this.path=path,this.query=query}return LocationState}(),MockLocationStrategy=function(_super){function MockLocationStrategy(){var _this=_super.call(this)||this;return _this.internalBaseHref="/",_this.internalPath="/",_this.internalTitle="",_this.urlChanges=[],_this._subject=new _angular_core.EventEmitter,_this}return __extends(MockLocationStrategy,_super),MockLocationStrategy.prototype.simulatePopState=function(url){this.internalPath=url,this._subject.emit(new _MockPopStateEvent(this.path()))},MockLocationStrategy.prototype.path=function(includeHash){return void 0===includeHash&&(includeHash=!1),this.internalPath},MockLocationStrategy.prototype.prepareExternalUrl=function(internal){return internal.startsWith("/")&&this.internalBaseHref.endsWith("/")?this.internalBaseHref+internal.substring(1):this.internalBaseHref+internal},MockLocationStrategy.prototype.pushState=function(ctx,title,path,query){this.internalTitle=title;var url=path+(query.length>0?"?"+query:"");this.internalPath=url;var externalUrl=this.prepareExternalUrl(url);this.urlChanges.push(externalUrl)},MockLocationStrategy.prototype.replaceState=function(ctx,title,path,query){this.internalTitle=title;var url=path+(query.length>0?"?"+query:"");this.internalPath=url;var externalUrl=this.prepareExternalUrl(url);this.urlChanges.push("replace: "+externalUrl)},MockLocationStrategy.prototype.onPopState=function(fn){this._subject.subscribe({next:fn})},MockLocationStrategy.prototype.getBaseHref=function(){return this.internalBaseHref},MockLocationStrategy.prototype.back=function(){if(this.urlChanges.length>0){this.urlChanges.pop();var nextUrl=this.urlChanges.length>0?this.urlChanges[this.urlChanges.length-1]:"";this.simulatePopState(nextUrl)}},MockLocationStrategy.prototype.forward=function(){throw"not implemented"},MockLocationStrategy}(_angular_common.LocationStrategy);MockLocationStrategy.decorators=[{type:_angular_core.Injectable}],MockLocationStrategy.ctorParameters=function(){return[]};var _MockPopStateEvent=function(){function _MockPopStateEvent(newUrl){this.newUrl=newUrl,this.pop=!0,this.type="popstate"}return _MockPopStateEvent}();exports.SpyLocation=SpyLocation,exports.MockLocationStrategy=MockLocationStrategy,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=common-testing.umd.min.js.map
