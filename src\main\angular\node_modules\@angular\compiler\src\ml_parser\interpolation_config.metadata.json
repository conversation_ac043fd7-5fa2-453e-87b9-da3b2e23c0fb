[{"__symbolic": "module", "version": 3, "metadata": {"InterpolationConfig": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "DEFAULT_INTERPOLATION_CONFIG": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InterpolationConfig"}, "arguments": ["{{", "}}"]}}}, {"__symbolic": "module", "version": 1, "metadata": {"InterpolationConfig": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "DEFAULT_INTERPOLATION_CONFIG": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InterpolationConfig"}, "arguments": ["{{", "}}"]}}}]