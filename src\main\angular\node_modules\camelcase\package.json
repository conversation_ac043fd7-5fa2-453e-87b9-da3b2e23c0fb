{"_args": [["camelcase@2.1.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "camelcase@2.1.1", "_id": "camelcase@2.1.1", "_inBundle": false, "_integrity": "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=", "_location": "/camelcase", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "camelcase@2.1.1", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/boxen", "/camelcase-keys"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/camelcase/-/camelcase-2.1.1.tgz", "_spec": "2.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.1"}