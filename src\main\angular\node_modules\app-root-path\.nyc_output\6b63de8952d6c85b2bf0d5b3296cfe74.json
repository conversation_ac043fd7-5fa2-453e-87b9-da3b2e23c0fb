{"/Users/<USER>/Development/Libraries/node-app-root-path/lib/resolve.js": {"path": "/Users/<USER>/Development/Libraries/node-app-root-path/lib/resolve.js", "statementMap": {"1": {"start": {"line": 4, "column": 11}, "end": {"line": 4, "column": 26}}, "2": {"start": {"line": 7, "column": 18}, "end": {"line": 7, "column": 47}}, "3": {"start": {"line": 11, "column": 0}, "end": {"line": 15, "column": 1}}, "4": {"start": {"line": 12, "column": 1}, "end": {"line": 12, "column": 50}}, "5": {"start": {"line": 14, "column": 1}, "end": {"line": 14, "column": 64}}, "6": {"start": {"line": 16, "column": 25}, "end": {"line": 16, "column": 77}}, "7": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 18}}, "8": {"start": {"line": 22, "column": 0}, "end": {"line": 74, "column": 2}}, "9": {"start": {"line": 24, "column": 1}, "end": {"line": 26, "column": 2}}, "10": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 49}}, "11": {"start": {"line": 29, "column": 1}, "end": {"line": 34, "column": 2}}, "12": {"start": {"line": 30, "column": 17}, "end": {"line": 30, "column": 27}}, "13": {"start": {"line": 31, "column": 15}, "end": {"line": 31, "column": 39}}, "14": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 22}}, "15": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 46}}, "16": {"start": {"line": 36, "column": 16}, "end": {"line": 36, "column": 37}}, "17": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 28}}, "18": {"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 23}}, "19": {"start": {"line": 44, "column": 1}, "end": {"line": 48, "column": 4}}, "20": {"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, "21": {"start": {"line": 46, "column": 3}, "end": {"line": 46, "column": 26}}, "22": {"start": {"line": 52, "column": 22}, "end": {"line": 52, "column": 42}}, "23": {"start": {"line": 53, "column": 1}, "end": {"line": 59, "column": 2}}, "24": {"start": {"line": 54, "column": 14}, "end": {"line": 54, "column": 44}}, "25": {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, "26": {"start": {"line": 56, "column": 3}, "end": {"line": 56, "column": 26}}, "27": {"start": {"line": 57, "column": 3}, "end": {"line": 57, "column": 16}}, "28": {"start": {"line": 63, "column": 1}, "end": {"line": 65, "column": 2}}, "29": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 52}}, "30": {"start": {"line": 68, "column": 1}, "end": {"line": 70, "column": 2}}, "31": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 41}}, "32": {"start": {"line": 73, "column": 1}, "end": {"line": 73, "column": 20}}}, "fnMap": {"1": {"name": "resolve", "decl": {"start": {"line": 22, "column": 26}, "end": {"line": 22, "column": 33}}, "loc": {"start": {"line": 22, "column": 43}, "end": {"line": 74, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 22}}, "loc": {"start": {"line": 44, "column": 42}, "end": {"line": 48, "column": 2}}}}, "branchMap": {"1": {"loc": {"start": {"line": 11, "column": 0}, "end": {"line": 15, "column": 1}}, "type": "if", "locations": [{"start": {"line": 11, "column": 0}, "end": {"line": 15, "column": 1}}, {"start": {"line": 11, "column": 0}, "end": {"line": 15, "column": 1}}]}, "2": {"loc": {"start": {"line": 24, "column": 1}, "end": {"line": 26, "column": 2}}, "type": "if", "locations": [{"start": {"line": 24, "column": 1}, "end": {"line": 26, "column": 2}}, {"start": {"line": 24, "column": 1}, "end": {"line": 26, "column": 2}}]}, "3": {"loc": {"start": {"line": 29, "column": 1}, "end": {"line": 34, "column": 2}}, "type": "if", "locations": [{"start": {"line": 29, "column": 1}, "end": {"line": 34, "column": 2}}, {"start": {"line": 29, "column": 1}, "end": {"line": 34, "column": 2}}]}, "4": {"loc": {"start": {"line": 29, "column": 5}, "end": {"line": 29, "column": 90}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 5}, "end": {"line": 29, "column": 34}}, {"start": {"line": 29, "column": 38}, "end": {"line": 29, "column": 52}}, {"start": {"line": 29, "column": 56}, "end": {"line": 29, "column": 90}}]}, "5": {"loc": {"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, "type": "if", "locations": [{"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}, {"start": {"line": 45, "column": 2}, "end": {"line": 47, "column": 3}}]}, "6": {"loc": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 22}}, {"start": {"line": 45, "column": 26}, "end": {"line": 45, "column": 60}}]}, "7": {"loc": {"start": {"line": 53, "column": 1}, "end": {"line": 59, "column": 2}}, "type": "if", "locations": [{"start": {"line": 53, "column": 1}, "end": {"line": 59, "column": 2}}, {"start": {"line": 53, "column": 1}, "end": {"line": 59, "column": 2}}]}, "8": {"loc": {"start": {"line": 53, "column": 5}, "end": {"line": 53, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 5}, "end": {"line": 53, "column": 21}}, {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 64}}]}, "9": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}]}, "10": {"loc": {"start": {"line": 63, "column": 1}, "end": {"line": 65, "column": 2}}, "type": "if", "locations": [{"start": {"line": 63, "column": 1}, "end": {"line": 65, "column": 2}}, {"start": {"line": 63, "column": 1}, "end": {"line": 65, "column": 2}}]}, "11": {"loc": {"start": {"line": 63, "column": 5}, "end": {"line": 63, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 5}, "end": {"line": 63, "column": 20}}, {"start": {"line": 63, "column": 24}, "end": {"line": 63, "column": 43}}]}, "12": {"loc": {"start": {"line": 68, "column": 1}, "end": {"line": 70, "column": 2}}, "type": "if", "locations": [{"start": {"line": 68, "column": 1}, "end": {"line": 70, "column": 2}}, {"start": {"line": 68, "column": 1}, "end": {"line": 70, "column": 2}}]}, "13": {"loc": {"start": {"line": 68, "column": 5}, "end": {"line": 68, "column": 135}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 5}, "end": {"line": 68, "column": 20}}, {"start": {"line": 68, "column": 24}, "end": {"line": 68, "column": 70}}, {"start": {"line": 68, "column": 74}, "end": {"line": 68, "column": 135}}]}}, "s": {"1": 1, "2": 1, "3": 1, "4": 0, "5": 1, "6": 1, "7": 1, "8": 1, "9": 15, "10": 1, "11": 14, "12": 1, "13": 1, "14": 1, "15": 1, "16": 13, "17": 13, "18": 13, "19": 13, "20": 39, "21": 4, "22": 13, "23": 13, "24": 7, "25": 7, "26": 7, "27": 7, "28": 13, "29": 6, "30": 13, "31": 1, "32": 13}, "f": {"1": 15, "2": 39}, "b": {"1": [0, 1], "2": [1, 14], "3": [1, 13], "4": [14, 1, 1], "5": [4, 35], "6": [39, 36], "7": [7, 6], "8": [13, 9], "9": [7, 0], "10": [6, 7], "11": [13, 9], "12": [1, 12], "13": [13, 4, 1]}, "hash": "9b2ea0c96edd5dbb6b983adc8912f9cfac02ae24"}, "/Users/<USER>/Development/Libraries/node-app-root-path/lib/app-root-path.js": {"path": "/Users/<USER>/Development/Libraries/node-app-root-path/lib/app-root-path.js", "statementMap": {"1": {"start": {"line": 3, "column": 0}, "end": {"line": 30, "column": 2}}, "2": {"start": {"line": 4, "column": 12}, "end": {"line": 4, "column": 27}}, "3": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 38}}, "4": {"start": {"line": 6, "column": 19}, "end": {"line": 6, "column": 35}}, "5": {"start": {"line": 8, "column": 23}, "end": {"line": 27, "column": 2}}, "6": {"start": {"line": 10, "column": 3}, "end": {"line": 10, "column": 47}}, "7": {"start": {"line": 14, "column": 3}, "end": {"line": 14, "column": 57}}, "8": {"start": {"line": 18, "column": 3}, "end": {"line": 18, "column": 22}}, "9": {"start": {"line": 22, "column": 3}, "end": {"line": 22, "column": 49}}, "10": {"start": {"line": 23, "column": 3}, "end": {"line": 23, "column": 38}}, "11": {"start": {"line": 29, "column": 1}, "end": {"line": 29, "column": 24}}}, "fnMap": {"1": {"name": "(anonymous_1)", "decl": {"start": {"line": 3, "column": 17}, "end": {"line": 3, "column": 18}}, "loc": {"start": {"line": 3, "column": 35}, "end": {"line": 30, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 12}}, "loc": {"start": {"line": 9, "column": 34}, "end": {"line": 11, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 13, "column": 11}, "end": {"line": 13, "column": 12}}, "loc": {"start": {"line": 13, "column": 34}, "end": {"line": 15, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 23}, "end": {"line": 19, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 21, "column": 11}, "end": {"line": 21, "column": 12}}, "loc": {"start": {"line": 21, "column": 39}, "end": {"line": 24, "column": 3}}}}, "branchMap": {}, "s": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 3, "7": 1, "8": 4, "9": 2, "10": 2, "11": 1}, "f": {"1": 1, "2": 3, "3": 1, "4": 4, "5": 2}, "b": {}, "hash": "c31dd68444a27462b2d2f87e83bcc86f9acf840e"}}