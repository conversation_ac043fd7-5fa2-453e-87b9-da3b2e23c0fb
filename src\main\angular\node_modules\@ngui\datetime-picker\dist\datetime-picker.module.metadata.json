{"__symbolic": "module", "version": 1, "metadata": {"NguiDatetimePickerModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule"}, {"__symbolic": "reference", "module": "@angular/forms", "name": "FormsModule"}], "declarations": [{"__symbolic": "reference", "module": "./datetime-picker.component", "name": "NguiDatetimePickerComponent"}, {"__symbolic": "reference", "module": "./datetime-picker.directive", "name": "NguiDatetimePickerDirective"}], "exports": [{"__symbolic": "reference", "module": "./datetime-picker.component", "name": "NguiDatetimePickerComponent"}, {"__symbolic": "reference", "module": "./datetime-picker.directive", "name": "NguiDatetimePickerDirective"}], "entryComponents": [{"__symbolic": "reference", "module": "./datetime-picker.component", "name": "NguiDatetimePickerComponent"}], "providers": [{"__symbolic": "reference", "module": "./datetime", "name": "NguiDatetime"}]}]}]}}}