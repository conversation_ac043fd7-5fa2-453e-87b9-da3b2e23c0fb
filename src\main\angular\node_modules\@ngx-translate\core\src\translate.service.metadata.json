[{"__symbolic": "module", "version": 3, "metadata": {"USE_STORE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "OpaqueToken"}, "arguments": ["USE_STORE"]}, "TranslationChangeEvent": {"__symbolic": "interface"}, "LangChangeEvent": {"__symbolic": "interface"}, "DefaultLangChangeEvent": {"__symbolic": "interface"}, "TranslateService": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "USE_STORE"}]}]], "parameters": [{"__symbolic": "reference", "module": "./translate.store", "name": "TranslateStore"}, {"__symbolic": "reference", "module": "./translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "./translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "./missing-translation-handler", "name": "MissingTranslationHandler"}, {"__symbolic": "reference", "name": "boolean"}]}], "setDefaultLang": [{"__symbolic": "method"}], "getDefaultLang": [{"__symbolic": "method"}], "use": [{"__symbolic": "method"}], "retrieveTranslations": [{"__symbolic": "method"}], "getTranslation": [{"__symbolic": "method"}], "setTranslation": [{"__symbolic": "method"}], "getLangs": [{"__symbolic": "method"}], "addLangs": [{"__symbolic": "method"}], "updateLangs": [{"__symbolic": "method"}], "getParsedResult": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "instant": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "changeLang": [{"__symbolic": "method"}], "changeDefaultLang": [{"__symbolic": "method"}], "reloadLang": [{"__symbolic": "method"}], "resetLang": [{"__symbolic": "method"}], "getBrowserLang": [{"__symbolic": "method"}], "getBrowserCultureLang": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"USE_STORE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "OpaqueToken"}, "arguments": ["USE_STORE"]}, "TranslationChangeEvent": {"__symbolic": "interface"}, "LangChangeEvent": {"__symbolic": "interface"}, "DefaultLangChangeEvent": {"__symbolic": "interface"}, "TranslateService": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "USE_STORE"}]}]], "parameters": [{"__symbolic": "reference", "module": "./translate.store", "name": "TranslateStore"}, {"__symbolic": "reference", "module": "./translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "./translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "./missing-translation-handler", "name": "MissingTranslationHandler"}, {"__symbolic": "reference", "name": "boolean"}]}], "setDefaultLang": [{"__symbolic": "method"}], "getDefaultLang": [{"__symbolic": "method"}], "use": [{"__symbolic": "method"}], "retrieveTranslations": [{"__symbolic": "method"}], "getTranslation": [{"__symbolic": "method"}], "setTranslation": [{"__symbolic": "method"}], "getLangs": [{"__symbolic": "method"}], "addLangs": [{"__symbolic": "method"}], "updateLangs": [{"__symbolic": "method"}], "getParsedResult": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "instant": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "changeLang": [{"__symbolic": "method"}], "changeDefaultLang": [{"__symbolic": "method"}], "reloadLang": [{"__symbolic": "method"}], "resetLang": [{"__symbolic": "method"}], "getBrowserLang": [{"__symbolic": "method"}], "getBrowserCultureLang": [{"__symbolic": "method"}]}}}}]