{"title": "Custom protocol handling", "description": "Method of allowing a webpage to handle a given protocol using `navigator.registerProtocolHandler`. This allows certain URLs to be opened by a given web application, for example `mailto:` addresses can be opened by a webmail client.", "spec": "https://html.spec.whatwg.org/multipage/webappapis.html#custom-handlers", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Navigator/registerProtocolHandler", "title": "Mozilla Developer Network (MDN) documentation - Register protocol handler"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1", "58": "y #1", "59": "y #1", "60": "y #1", "61": "y #1", "62": "y #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "y #1", "12": "y #1", "12.1": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "y #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Supports protocols `mailto`, `mms`, `nntp`, `rtsp`, and `webcal` but requires custom protocols to start with `web+`"}, "usage_perc_y": 32.95, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}