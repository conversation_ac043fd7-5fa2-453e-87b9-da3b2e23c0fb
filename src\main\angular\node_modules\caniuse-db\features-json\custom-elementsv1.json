{"title": "Custom Elements v1", "description": "Method of defining new HTML tags.", "spec": "https://html.spec.whatwg.org/multipage/scripting.html#custom-elements", "status": "ls", "links": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=889230", "title": "Firefox tracking bug: Implement Custom Elements (from Web Components)"}, {"url": "http://status.modern.ie/customelements", "title": "IE Web Platform Status and Roadmap: Custom Elements"}, {"url": "https://developers.google.com/web/fundamentals/primers/customelements/", "title": "Google Developers - Custom elements v1: reusable web components"}, {"url": "https://github.com/webcomponents/webcomponentsjs/tree/v1/src/CustomElements/v1", "title": "customElements.define polyfill"}, {"url": "https://webkit.org/blog/7027/introducing-custom-elements/", "title": "WebKit Blog: Introducing Custom Elements"}], "bugs": [], "categories": ["DOM", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "p", "11": "p"}, "edge": {"12": "p", "13": "p", "14": "p", "15": "p", "16": "p"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p", "53": "p", "54": "p", "55": "p", "56": "p", "57": "p"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "p", "53": "p", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #1", "62": "a #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "a #1", "11": "a #1", "TP": "a #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "a #1", "11": "a #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "a #1"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "a #1"}, "and_ff": {"54": "p"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "a #1"}, "and_qq": {"1.2": "p"}, "baidu": {"7.12": "a #1"}}, "notes": "Chrome 36+/Opera 20+ implemented a previous version of Custom Elements (v0) that used `.registerElement()`. Other browsers are implementing v1, `window.customElements.define()`.", "notes_by_num": {"1": "Supports \"Autonomous custom elements\" but not \"Customized built-in elements\""}, "usage_perc_y": 0, "usage_perc_a": 61.8, "ucprefix": false, "parent": "", "keywords": "web components,custom elements", "ie_id": "customelements", "chrome_id": "4696261944934400", "firefox_id": "custom-elements", "webkit_id": "feature-custom-elements", "shown": true}