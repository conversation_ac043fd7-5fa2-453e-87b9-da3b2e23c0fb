[{"__symbolic": "module", "version": 3, "metadata": {"SplitInterpolation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "number"}]}]}]}}, "TemplateBindingParseResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "TemplateBinding"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "Parser": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./lexer", "name": "<PERSON><PERSON>"}]}], "parseAction": [{"__symbolic": "method"}], "parseBinding": [{"__symbolic": "method"}], "parseSimpleBinding": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}], "_parseBindingAst": [{"__symbolic": "method"}], "_parseQuote": [{"__symbolic": "method"}], "parseTemplateBindings": [{"__symbolic": "method"}], "parseInterpolation": [{"__symbolic": "method"}], "splitInterpolation": [{"__symbolic": "method"}], "wrapLiteralPrimitive": [{"__symbolic": "method"}], "_stripComments": [{"__symbolic": "method"}], "_commentStart": [{"__symbolic": "method"}], "_checkNoInterpolation": [{"__symbolic": "method"}], "_findInterpolationErrorColumn": [{"__symbolic": "method"}]}}, "_ParseAST": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./lexer", "name": "Token"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"__symbolic": "reference", "name": "number"}]}], "peek": [{"__symbolic": "method"}], "span": [{"__symbolic": "method"}], "advance": [{"__symbolic": "method"}], "optionalCharacter": [{"__symbolic": "method"}], "peekKeywordLet": [{"__symbolic": "method"}], "peekKeywordAs": [{"__symbolic": "method"}], "expectCharacter": [{"__symbolic": "method"}], "optionalOperator": [{"__symbolic": "method"}], "expectOperator": [{"__symbolic": "method"}], "expectIdentifierOrKeyword": [{"__symbolic": "method"}], "expectIdentifierOrKeywordOrString": [{"__symbolic": "method"}], "parseChain": [{"__symbolic": "method"}], "parsePipe": [{"__symbolic": "method"}], "parseExpression": [{"__symbolic": "method"}], "parseConditional": [{"__symbolic": "method"}], "parseLogicalOr": [{"__symbolic": "method"}], "parseLogicalAnd": [{"__symbolic": "method"}], "parseEquality": [{"__symbolic": "method"}], "parseRelational": [{"__symbolic": "method"}], "parseAdditive": [{"__symbolic": "method"}], "parseMultiplicative": [{"__symbolic": "method"}], "parsePrefix": [{"__symbolic": "method"}], "parseCallChain": [{"__symbolic": "method"}], "parsePrimary": [{"__symbolic": "method"}], "parseExpressionList": [{"__symbolic": "method"}], "parseLiteralMap": [{"__symbolic": "method"}], "parseAccessMemberOrMethodCall": [{"__symbolic": "method"}], "parseCallArguments": [{"__symbolic": "method"}], "expectTemplateBindingKey": [{"__symbolic": "method"}], "parseTemplateBindings": [{"__symbolic": "method"}], "error": [{"__symbolic": "method"}], "locationText": [{"__symbolic": "method"}], "skip": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"SplitInterpolation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "number"}]}]}]}}, "TemplateBindingParseResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "TemplateBinding"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "Parser": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./lexer", "name": "<PERSON><PERSON>"}]}], "parseAction": [{"__symbolic": "method"}], "parseBinding": [{"__symbolic": "method"}], "parseSimpleBinding": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}], "_parseBindingAst": [{"__symbolic": "method"}], "_parseQuote": [{"__symbolic": "method"}], "parseTemplateBindings": [{"__symbolic": "method"}], "parseInterpolation": [{"__symbolic": "method"}], "splitInterpolation": [{"__symbolic": "method"}], "wrapLiteralPrimitive": [{"__symbolic": "method"}], "_stripComments": [{"__symbolic": "method"}], "_commentStart": [{"__symbolic": "method"}], "_checkNoInterpolation": [{"__symbolic": "method"}], "_findInterpolationErrorColumn": [{"__symbolic": "method"}]}}, "_ParseAST": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./lexer", "name": "Token"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"__symbolic": "reference", "name": "number"}]}], "peek": [{"__symbolic": "method"}], "span": [{"__symbolic": "method"}], "advance": [{"__symbolic": "method"}], "optionalCharacter": [{"__symbolic": "method"}], "peekKeywordLet": [{"__symbolic": "method"}], "peekKeywordAs": [{"__symbolic": "method"}], "expectCharacter": [{"__symbolic": "method"}], "optionalOperator": [{"__symbolic": "method"}], "expectOperator": [{"__symbolic": "method"}], "expectIdentifierOrKeyword": [{"__symbolic": "method"}], "expectIdentifierOrKeywordOrString": [{"__symbolic": "method"}], "parseChain": [{"__symbolic": "method"}], "parsePipe": [{"__symbolic": "method"}], "parseExpression": [{"__symbolic": "method"}], "parseConditional": [{"__symbolic": "method"}], "parseLogicalOr": [{"__symbolic": "method"}], "parseLogicalAnd": [{"__symbolic": "method"}], "parseEquality": [{"__symbolic": "method"}], "parseRelational": [{"__symbolic": "method"}], "parseAdditive": [{"__symbolic": "method"}], "parseMultiplicative": [{"__symbolic": "method"}], "parsePrefix": [{"__symbolic": "method"}], "parseCallChain": [{"__symbolic": "method"}], "parsePrimary": [{"__symbolic": "method"}], "parseExpressionList": [{"__symbolic": "method"}], "parseLiteralMap": [{"__symbolic": "method"}], "parseAccessMemberOrMethodCall": [{"__symbolic": "method"}], "parseCallArguments": [{"__symbolic": "method"}], "expectTemplateBindingKey": [{"__symbolic": "method"}], "parseTemplateBindings": [{"__symbolic": "method"}], "error": [{"__symbolic": "method"}], "locationText": [{"__symbolic": "method"}], "skip": [{"__symbolic": "method"}]}}}}]