{"title": "text-decoration styling", "description": "Method of defining the type, style and color of lines in the text-decoration property. These can be defined as shorthand (e.g. `text-decoration: line-through dashed blue`) or as single properties (e.g. `text-decoration-color: blue`)", "spec": "http://www.w3.org/TR/css-text-decor-3/#line-decoration", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-decoration-style", "title": "Mozilla Developer Network (MDN) documentation - text-decoration-style"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-decoration-color", "title": "Mozilla Developer Network (MDN) documentation - text-decoration-color"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-decoration-line", "title": "Mozilla Developer Network (MDN) documentation - text-decoration-line"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6514536-text-decoration-styling", "title": "Microsoft Edge feature request on UserVoice"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-decoration-skip", "title": "MDN Documentation for text-decoration-skip"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=812990", "title": "Firefox implementation bug"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "a x #4", "7": "a x #4", "8": "a x #4", "9": "a x #4", "10": "a x #4", "11": "a x #4", "12": "a x #4", "13": "a x #4", "14": "a x #4", "15": "a x #4", "16": "a x #4", "17": "a x #4", "18": "a x #4", "19": "a x #4", "20": "a x #4", "21": "a x #4", "22": "a x #4", "23": "a x #4", "24": "a x #4", "25": "a x #4", "26": "a x #4", "27": "a x #4", "28": "a x #4", "29": "a x #4", "30": "a x #4", "31": "a x #4", "32": "a x #4", "33": "a x #4", "34": "a x #4", "35": "a x #4", "36": "a #4", "37": "a #4", "38": "a #4", "39": "a #4", "40": "a #4", "41": "a #4", "42": "a #4", "43": "a #4", "44": "a #4", "45": "a #4", "46": "a #4", "47": "a #4", "48": "a #4", "49": "a #4", "50": "a #4", "51": "a #4", "52": "a #4", "53": "a #4", "54": "a #4", "55": "a #4", "56": "a #4", "57": "a #4"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n x d #1", "27": "n x d #1", "28": "n x d #1", "29": "n x d #1", "30": "n x d #1", "31": "n x d #1", "32": "n x d #1", "33": "n x d #1", "34": "n x d #1", "35": "n x d #1", "36": "n x d #1", "37": "n x d #1", "38": "n x d #1", "39": "n x d #1", "40": "n x d #1", "41": "n x d #1", "42": "n x d #1", "43": "n x d #1", "44": "n x d #1", "45": "n x d #1", "46": "n x d #1", "47": "n x d #1", "48": "n x d #1", "49": "n x d #1", "50": "n x d #1", "51": "n x d #1", "52": "n x d #1", "53": "n x d #1", "54": "n x d #1", "55": "n x d #1", "56": "n x d #1", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "a x #2 #4", "8": "a x #2 #3", "9": "a x #2 #3", "9.1": "a x #2 #3", "10": "a x #2 #3", "10.1": "a x #2 #3", "11": "a x #2 #3", "TP": "a x #2 #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n x d #1", "36": "n x d #1", "37": "n x d #1", "38": "n x d #1", "39": "n x d #1", "40": "n x d #1", "41": "n x d #1", "42": "n x d #1", "43": "n x d #1", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "a x #2", "8.1-8.4": "a x #2", "9.0-9.2": "a x #2", "9.3": "a x #2", "10.0-10.2": "a x #2", "10.3": "a x #2", "11": "a x #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "y"}}, "notes": "All browsers support the CSS2 version of `text-decoration`, which matches only the `text-decoration-line` values (`underline`, etc.)", "notes_by_num": {"1": "Enabled in Chrome through the \"experimental Web Platform features\" flag in chrome://flags", "2": "Partial support refers to not supporting the `text-decoration-style` property.", "3": "Safari 8+ supports `-webkit-text-decoration-skip` with values `none` and `skip` (other values behave like `none` or `skip`)", "4": "Partial support refers to not supporting the `text-decoration-skip` property."}, "usage_perc_y": 52.13, "usage_perc_a": 18.05, "ucprefix": false, "parent": "", "keywords": "text-decoration-line,text-decoration-style,text-decoration-color,text-decoration-skip", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}