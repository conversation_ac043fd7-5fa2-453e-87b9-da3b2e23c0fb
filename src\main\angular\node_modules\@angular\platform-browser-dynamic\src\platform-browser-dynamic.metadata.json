[{"__symbolic": "module", "version": 3, "metadata": {"RESOURCE_CACHE_PROVIDER": [{"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "useClass": {"__symbolic": "reference", "module": "./resource_loader/resource_loader_cache", "name": "CachedResourceLoader"}}], "platformBrowserDynamic": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "platformCoreDynamic"}, "browserDynamic", {"__symbolic": "reference", "module": "./platform_providers", "name": "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"}]}}, "exports": [{"from": "./private_export"}, {"from": "./version", "export": ["VERSION"]}]}, {"__symbolic": "module", "version": 1, "metadata": {"RESOURCE_CACHE_PROVIDER": [{"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "useClass": {"__symbolic": "reference", "module": "./resource_loader/resource_loader_cache", "name": "CachedResourceLoader"}}], "platformBrowserDynamic": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "platformCoreDynamic"}, "browserDynamic", {"__symbolic": "reference", "module": "./platform_providers", "name": "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"}]}}, "exports": [{"from": "./private_export"}, {"from": "./version", "export": ["VERSION"]}]}]