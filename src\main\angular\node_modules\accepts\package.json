{"_args": [["accepts@1.3.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "accepts@1.3.3", "_id": "accepts@1.3.3", "_inBundle": false, "_integrity": "sha1-w8p0NJOGSMPg2cHjKN1otiLChMo=", "_location": "/accepts", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "accepts@1.3.3", "name": "accepts", "escapedName": "accepts", "rawSpec": "1.3.3", "saveSpec": null, "fetchSpec": "1.3.3"}, "_requiredBy": ["/compression", "/engine.io", "/express", "/serve-index"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/accepts/-/accepts-1.3.3.tgz", "_spec": "1.3.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-types": "~2.1.11", "negotiator": "0.6.1"}, "description": "Higher-level content negotiation", "devDependencies": {"istanbul": "0.4.3", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "name": "accepts", "repository": {"type": "git", "url": "git+https://github.com/jshttp/accepts.git"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.3.3"}