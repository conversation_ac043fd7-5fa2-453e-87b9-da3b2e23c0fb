/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core"),require("rxjs/Observable"),require("@angular/platform-browser")):"function"==typeof define&&define.amd?define(["exports","@angular/core","rxjs/Observable","@angular/platform-browser"],factory):factory((global.ng=global.ng||{},global.ng.http=global.ng.http||{}),global.ng.core,global.Rx,global.ng.platformBrowser)}(this,function(exports,_angular_core,rxjs_Observable,_angular_platformBrowser){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function normalizeMethodName(method){if("string"!=typeof method)return method;switch(method.toUpperCase()){case"GET":return RequestMethod.Get;case"POST":return RequestMethod.Post;case"PUT":return RequestMethod.Put;case"DELETE":return RequestMethod.Delete;case"OPTIONS":return RequestMethod.Options;case"HEAD":return RequestMethod.Head;case"PATCH":return RequestMethod.Patch}throw new Error('Invalid request method. The method "'+method+'" is not supported.')}function getResponseURL(xhr){return"responseURL"in xhr?xhr.responseURL:/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())?xhr.getResponseHeader("X-Request-URL"):null}function stringToArrayBuffer(input){for(var view=new Uint16Array(input.length),i=0,strLen=input.length;i<strLen;i++)view[i]=input.charCodeAt(i);return view.buffer}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 * @param {?=} rawParams
 * @return {?}
 */
function paramParser(rawParams){void 0===rawParams&&(rawParams="");var map=new Map;if(rawParams.length>0){var params=rawParams.split("&");params.forEach(function(param){var eqIdx=param.indexOf("="),_a=eqIdx==-1?[param,""]:[param.slice(0,eqIdx),param.slice(eqIdx+1)],key=_a[0],val=_a[1],list=map.get(key)||[];list.push(val),map.set(key,list)})}return map}function standardEncoding(v){return encodeURIComponent(v).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/gi,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%2B/gi,"+").replace(/%3D/gi,"=").replace(/%3F/gi,"?").replace(/%2F/gi,"/")}function _getJsonpConnections(){var w="object"==typeof window?window:{};return null===_jsonpConnections&&(_jsonpConnections=w[JSONP_HOME]={}),_jsonpConnections}function urlEncodeParams(params){var searchParams=new URLSearchParams;return Object.keys(params).forEach(function(key){var value=params[key];value&&Array.isArray(value)?value.forEach(function(element){return searchParams.append(key,element.toString())}):searchParams.append(key,value.toString())}),searchParams}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function httpRequest(backend,request){return backend.createConnection(request).response}function mergeOptions(defaultOpts,providedOpts,method,url){var newOptions=defaultOpts;return providedOpts?newOptions.merge(new RequestOptions({method:providedOpts.method||method,url:providedOpts.url||url,search:providedOpts.search,params:providedOpts.params,headers:providedOpts.headers,body:providedOpts.body,withCredentials:providedOpts.withCredentials,responseType:providedOpts.responseType})):newOptions.merge(new RequestOptions({method:method,url:url}))}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function _createDefaultCookieXSRFStrategy(){return new CookieXSRFStrategy}function httpFactory(xhrBackend,requestOptions){return new Http(xhrBackend,requestOptions)}function jsonpFactory(jsonpBackend,requestOptions){return new Jsonp(jsonpBackend,requestOptions)}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},BrowserXhr=function(){function BrowserXhr(){}return BrowserXhr.prototype.build=function(){return new XMLHttpRequest},BrowserXhr}();BrowserXhr.decorators=[{type:_angular_core.Injectable}],BrowserXhr.ctorParameters=function(){return[]};var RequestMethod={};RequestMethod.Get=0,RequestMethod.Post=1,RequestMethod.Put=2,RequestMethod.Delete=3,RequestMethod.Options=4,RequestMethod.Head=5,RequestMethod.Patch=6,RequestMethod[RequestMethod.Get]="Get",RequestMethod[RequestMethod.Post]="Post",RequestMethod[RequestMethod.Put]="Put",RequestMethod[RequestMethod.Delete]="Delete",RequestMethod[RequestMethod.Options]="Options",RequestMethod[RequestMethod.Head]="Head",RequestMethod[RequestMethod.Patch]="Patch";var ReadyState={};ReadyState.Unsent=0,ReadyState.Open=1,ReadyState.HeadersReceived=2,ReadyState.Loading=3,ReadyState.Done=4,ReadyState.Cancelled=5,ReadyState[ReadyState.Unsent]="Unsent",ReadyState[ReadyState.Open]="Open",ReadyState[ReadyState.HeadersReceived]="HeadersReceived",ReadyState[ReadyState.Loading]="Loading",ReadyState[ReadyState.Done]="Done",ReadyState[ReadyState.Cancelled]="Cancelled";var ResponseType={};ResponseType.Basic=0,ResponseType.Cors=1,ResponseType.Default=2,ResponseType.Error=3,ResponseType.Opaque=4,ResponseType[ResponseType.Basic]="Basic",ResponseType[ResponseType.Cors]="Cors",ResponseType[ResponseType.Default]="Default",ResponseType[ResponseType.Error]="Error",ResponseType[ResponseType.Opaque]="Opaque";var ContentType={};ContentType.NONE=0,ContentType.JSON=1,ContentType.FORM=2,ContentType.FORM_DATA=3,ContentType.TEXT=4,ContentType.BLOB=5,ContentType.ARRAY_BUFFER=6,ContentType[ContentType.NONE]="NONE",ContentType[ContentType.JSON]="JSON",ContentType[ContentType.FORM]="FORM",ContentType[ContentType.FORM_DATA]="FORM_DATA",ContentType[ContentType.TEXT]="TEXT",ContentType[ContentType.BLOB]="BLOB",ContentType[ContentType.ARRAY_BUFFER]="ARRAY_BUFFER";var ResponseContentType={};ResponseContentType.Text=0,ResponseContentType.Json=1,ResponseContentType.ArrayBuffer=2,ResponseContentType.Blob=3,ResponseContentType[ResponseContentType.Text]="Text",ResponseContentType[ResponseContentType.Json]="Json",ResponseContentType[ResponseContentType.ArrayBuffer]="ArrayBuffer",ResponseContentType[ResponseContentType.Blob]="Blob";var Headers=function(){function Headers(headers){var _this=this;if(this._headers=new Map,this._normalizedNames=new Map,headers)return headers instanceof Headers?void headers.forEach(function(values,name){values.forEach(function(value){return _this.append(name,value)})}):void Object.keys(headers).forEach(function(name){var values=Array.isArray(headers[name])?headers[name]:[headers[name]];_this.delete(name),values.forEach(function(value){return _this.append(name,value)})})}return Headers.fromResponseHeaderString=function(headersString){var headers=new Headers;return headersString.split("\n").forEach(function(line){var index=line.indexOf(":");if(index>0){var name=line.slice(0,index),value=line.slice(index+1).trim();headers.set(name,value)}}),headers},Headers.prototype.append=function(name,value){var values=this.getAll(name);null===values?this.set(name,value):values.push(value)},Headers.prototype.delete=function(name){var lcName=name.toLowerCase();this._normalizedNames.delete(lcName),this._headers.delete(lcName)},Headers.prototype.forEach=function(fn){var _this=this;this._headers.forEach(function(values,lcName){return fn(values,_this._normalizedNames.get(lcName),_this._headers)})},Headers.prototype.get=function(name){var values=this.getAll(name);return null===values?null:values.length>0?values[0]:null},Headers.prototype.has=function(name){return this._headers.has(name.toLowerCase())},Headers.prototype.keys=function(){return Array.from(this._normalizedNames.values())},Headers.prototype.set=function(name,value){Array.isArray(value)?value.length&&this._headers.set(name.toLowerCase(),[value.join(",")]):this._headers.set(name.toLowerCase(),[value]),this.mayBeSetNormalizedName(name)},Headers.prototype.values=function(){return Array.from(this._headers.values())},Headers.prototype.toJSON=function(){var _this=this,serialized={};return this._headers.forEach(function(values,name){var split=[];values.forEach(function(v){return split.push.apply(split,v.split(","))}),serialized[_this._normalizedNames.get(name)]=split}),serialized},Headers.prototype.getAll=function(name){return this.has(name)?this._headers.get(name.toLowerCase())||null:null},Headers.prototype.entries=function(){throw new Error('"entries" method is not implemented on Headers class')},Headers.prototype.mayBeSetNormalizedName=function(name){var lcName=name.toLowerCase();this._normalizedNames.has(lcName)||this._normalizedNames.set(lcName,name)},Headers}(),ResponseOptions=function(){function ResponseOptions(opts){void 0===opts&&(opts={});var body=opts.body,status=opts.status,headers=opts.headers,statusText=opts.statusText,type=opts.type,url=opts.url;this.body=null!=body?body:null,this.status=null!=status?status:null,this.headers=null!=headers?headers:null,this.statusText=null!=statusText?statusText:null,this.type=null!=type?type:null,this.url=null!=url?url:null}return ResponseOptions.prototype.merge=function(options){return new ResponseOptions({body:options&&null!=options.body?options.body:this.body,status:options&&null!=options.status?options.status:this.status,headers:options&&null!=options.headers?options.headers:this.headers,statusText:options&&null!=options.statusText?options.statusText:this.statusText,type:options&&null!=options.type?options.type:this.type,url:options&&null!=options.url?options.url:this.url})},ResponseOptions}(),BaseResponseOptions=function(_super){function BaseResponseOptions(){return _super.call(this,{status:200,statusText:"Ok",type:ResponseType.Default,headers:new Headers})||this}return __extends(BaseResponseOptions,_super),BaseResponseOptions}(ResponseOptions);BaseResponseOptions.decorators=[{type:_angular_core.Injectable}],BaseResponseOptions.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var ConnectionBackend=function(){function ConnectionBackend(){}return ConnectionBackend.prototype.createConnection=function(request){},ConnectionBackend}(),Connection=function(){function Connection(){}return Connection}(),XSRFStrategy=function(){function XSRFStrategy(){}return XSRFStrategy.prototype.configureRequest=function(req){},XSRFStrategy}(),isSuccess=function(status){return status>=200&&status<300},QueryEncoder=function(){function QueryEncoder(){}return QueryEncoder.prototype.encodeKey=function(k){return standardEncoding(k)},QueryEncoder.prototype.encodeValue=function(v){return standardEncoding(v)},QueryEncoder}(),URLSearchParams=function(){function URLSearchParams(rawParams,queryEncoder){void 0===rawParams&&(rawParams=""),void 0===queryEncoder&&(queryEncoder=new QueryEncoder),this.rawParams=rawParams,this.queryEncoder=queryEncoder,this.paramsMap=paramParser(rawParams)}return URLSearchParams.prototype.clone=function(){var clone=new URLSearchParams("",this.queryEncoder);return clone.appendAll(this),clone},URLSearchParams.prototype.has=function(param){return this.paramsMap.has(param)},URLSearchParams.prototype.get=function(param){var storedParam=this.paramsMap.get(param);return Array.isArray(storedParam)?storedParam[0]:null},URLSearchParams.prototype.getAll=function(param){return this.paramsMap.get(param)||[]},URLSearchParams.prototype.set=function(param,val){if(void 0===val||null===val)return void this.delete(param);var list=this.paramsMap.get(param)||[];list.length=0,list.push(val),this.paramsMap.set(param,list)},URLSearchParams.prototype.setAll=function(searchParams){var _this=this;searchParams.paramsMap.forEach(function(value,param){var list=_this.paramsMap.get(param)||[];list.length=0,list.push(value[0]),_this.paramsMap.set(param,list)})},URLSearchParams.prototype.append=function(param,val){if(void 0!==val&&null!==val){var list=this.paramsMap.get(param)||[];list.push(val),this.paramsMap.set(param,list)}},URLSearchParams.prototype.appendAll=function(searchParams){var _this=this;searchParams.paramsMap.forEach(function(value,param){for(var list=_this.paramsMap.get(param)||[],i=0;i<value.length;++i)list.push(value[i]);_this.paramsMap.set(param,list)})},URLSearchParams.prototype.replaceAll=function(searchParams){var _this=this;searchParams.paramsMap.forEach(function(value,param){var list=_this.paramsMap.get(param)||[];list.length=0;for(var i=0;i<value.length;++i)list.push(value[i]);_this.paramsMap.set(param,list)})},URLSearchParams.prototype.toString=function(){var _this=this,paramsList=[];return this.paramsMap.forEach(function(values,k){values.forEach(function(v){return paramsList.push(_this.queryEncoder.encodeKey(k)+"="+_this.queryEncoder.encodeValue(v))})}),paramsList.join("&")},URLSearchParams.prototype.delete=function(param){this.paramsMap.delete(param)},URLSearchParams}(),Body=function(){function Body(){}return Body.prototype.json=function(){return"string"==typeof this._body?JSON.parse(this._body):this._body instanceof ArrayBuffer?JSON.parse(this.text()):this._body},Body.prototype.text=function(encodingHint){if(void 0===encodingHint&&(encodingHint="legacy"),this._body instanceof URLSearchParams)return this._body.toString();if(this._body instanceof ArrayBuffer)switch(encodingHint){case"legacy":return String.fromCharCode.apply(null,new Uint16Array(this._body));case"iso-8859":return String.fromCharCode.apply(null,new Uint8Array(this._body));default:throw new Error("Invalid value for encodingHint: "+encodingHint)}return null==this._body?"":"object"==typeof this._body?JSON.stringify(this._body,null,2):this._body.toString()},Body.prototype.arrayBuffer=function(){return this._body instanceof ArrayBuffer?this._body:stringToArrayBuffer(this.text())},Body.prototype.blob=function(){if(this._body instanceof Blob)return this._body;if(this._body instanceof ArrayBuffer)return new Blob([this._body]);throw new Error("The request body isn't either a blob or an array buffer")},Body}(),Response=function(_super){function Response(responseOptions){var _this=_super.call(this)||this;return _this._body=responseOptions.body,_this.status=responseOptions.status,_this.ok=_this.status>=200&&_this.status<=299,_this.statusText=responseOptions.statusText,_this.headers=responseOptions.headers,_this.type=responseOptions.type,_this.url=responseOptions.url,_this}return __extends(Response,_super),Response.prototype.toString=function(){return"Response with status: "+this.status+" "+this.statusText+" for URL: "+this.url},Response}(Body),_nextRequestId=0,JSONP_HOME="__ng_jsonp__",_jsonpConnections=null,BrowserJsonp=function(){function BrowserJsonp(){}return BrowserJsonp.prototype.build=function(url){var node=document.createElement("script");return node.src=url,node},BrowserJsonp.prototype.nextRequestID=function(){return"__req"+_nextRequestId++},BrowserJsonp.prototype.requestCallback=function(id){return JSONP_HOME+"."+id+".finished"},BrowserJsonp.prototype.exposeConnection=function(id,connection){var connections=_getJsonpConnections();connections[id]=connection},BrowserJsonp.prototype.removeConnection=function(id){var connections=_getJsonpConnections();connections[id]=null},BrowserJsonp.prototype.send=function(node){document.body.appendChild(node)},BrowserJsonp.prototype.cleanup=function(node){node.parentNode&&node.parentNode.removeChild(node)},BrowserJsonp}();BrowserJsonp.decorators=[{type:_angular_core.Injectable}],BrowserJsonp.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var JSONP_ERR_NO_CALLBACK="JSONP injected script did not invoke callback.",JSONP_ERR_WRONG_METHOD="JSONP requests must use GET request method.",JSONPConnection=function(){function JSONPConnection(){}return JSONPConnection.prototype.finished=function(data){},JSONPConnection}(),JSONPConnection_=function(_super){function JSONPConnection_(req,_dom,baseResponseOptions){var _this=_super.call(this)||this;if(_this._dom=_dom,_this.baseResponseOptions=baseResponseOptions,_this._finished=!1,req.method!==RequestMethod.Get)throw new TypeError(JSONP_ERR_WRONG_METHOD);return _this.request=req,_this.response=new rxjs_Observable.Observable(function(responseObserver){_this.readyState=ReadyState.Loading;var id=_this._id=_dom.nextRequestID();_dom.exposeConnection(id,_this);var callback=_dom.requestCallback(_this._id),url=req.url;url.indexOf("=JSONP_CALLBACK&")>-1?url=url.replace("=JSONP_CALLBACK&","="+callback+"&"):url.lastIndexOf("=JSONP_CALLBACK")===url.length-"=JSONP_CALLBACK".length&&(url=url.substring(0,url.length-"=JSONP_CALLBACK".length)+("="+callback));var script=_this._script=_dom.build(url),onLoad=function(event){if(_this.readyState!==ReadyState.Cancelled){if(_this.readyState=ReadyState.Done,_dom.cleanup(script),!_this._finished){var responseOptions_1=new ResponseOptions({body:JSONP_ERR_NO_CALLBACK,type:ResponseType.Error,url:url});return baseResponseOptions&&(responseOptions_1=baseResponseOptions.merge(responseOptions_1)),void responseObserver.error(new Response(responseOptions_1))}var responseOptions=new ResponseOptions({body:_this._responseData,url:url});_this.baseResponseOptions&&(responseOptions=_this.baseResponseOptions.merge(responseOptions)),responseObserver.next(new Response(responseOptions)),responseObserver.complete()}},onError=function(error){if(_this.readyState!==ReadyState.Cancelled){_this.readyState=ReadyState.Done,_dom.cleanup(script);var responseOptions=new ResponseOptions({body:error.message,type:ResponseType.Error});baseResponseOptions&&(responseOptions=baseResponseOptions.merge(responseOptions)),responseObserver.error(new Response(responseOptions))}};return script.addEventListener("load",onLoad),script.addEventListener("error",onError),_dom.send(script),function(){_this.readyState=ReadyState.Cancelled,script.removeEventListener("load",onLoad),script.removeEventListener("error",onError),_this._dom.cleanup(script)}}),_this}return __extends(JSONPConnection_,_super),JSONPConnection_.prototype.finished=function(data){this._finished=!0,this._dom.removeConnection(this._id),this.readyState!==ReadyState.Cancelled&&(this._responseData=data)},JSONPConnection_}(JSONPConnection),JSONPBackend=function(_super){function JSONPBackend(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(JSONPBackend,_super),JSONPBackend}(ConnectionBackend),JSONPBackend_=function(_super){function JSONPBackend_(_browserJSONP,_baseResponseOptions){var _this=_super.call(this)||this;return _this._browserJSONP=_browserJSONP,_this._baseResponseOptions=_baseResponseOptions,_this}return __extends(JSONPBackend_,_super),JSONPBackend_.prototype.createConnection=function(request){return new JSONPConnection_(request,this._browserJSONP,this._baseResponseOptions)},JSONPBackend_}(JSONPBackend);JSONPBackend_.decorators=[{type:_angular_core.Injectable}],JSONPBackend_.ctorParameters=function(){return[{type:BrowserJsonp},{type:ResponseOptions}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var XSSI_PREFIX=/^\)\]\}',?\n/,XHRConnection=function(){function XHRConnection(req,browserXHR,baseResponseOptions){var _this=this;this.request=req,this.response=new rxjs_Observable.Observable(function(responseObserver){var _xhr=browserXHR.build();_xhr.open(RequestMethod[req.method].toUpperCase(),req.url),null!=req.withCredentials&&(_xhr.withCredentials=req.withCredentials);var onLoad=function(){var status=1223===_xhr.status?204:_xhr.status,body=null;204!==status&&(body="undefined"==typeof _xhr.response?_xhr.responseText:_xhr.response,"string"==typeof body&&(body=body.replace(XSSI_PREFIX,""))),0===status&&(status=body?200:0);var headers=Headers.fromResponseHeaderString(_xhr.getAllResponseHeaders()),url=getResponseURL(_xhr)||req.url,statusText=_xhr.statusText||"OK",responseOptions=new ResponseOptions({body:body,status:status,headers:headers,statusText:statusText,url:url});null!=baseResponseOptions&&(responseOptions=baseResponseOptions.merge(responseOptions));var response=new Response(responseOptions);return response.ok=isSuccess(status),response.ok?(responseObserver.next(response),void responseObserver.complete()):void responseObserver.error(response)},onError=function(err){var responseOptions=new ResponseOptions({body:err,type:ResponseType.Error,status:_xhr.status,statusText:_xhr.statusText});null!=baseResponseOptions&&(responseOptions=baseResponseOptions.merge(responseOptions)),responseObserver.error(new Response(responseOptions))};if(_this.setDetectedContentType(req,_xhr),null==req.headers&&(req.headers=new Headers),req.headers.has("Accept")||req.headers.append("Accept","application/json, text/plain, */*"),req.headers.forEach(function(values,name){return _xhr.setRequestHeader(name,values.join(","))}),null!=req.responseType&&null!=_xhr.responseType)switch(req.responseType){case ResponseContentType.ArrayBuffer:_xhr.responseType="arraybuffer";break;case ResponseContentType.Json:_xhr.responseType="json";break;case ResponseContentType.Text:_xhr.responseType="text";break;case ResponseContentType.Blob:_xhr.responseType="blob";break;default:throw new Error("The selected responseType is not supported")}return _xhr.addEventListener("load",onLoad),_xhr.addEventListener("error",onError),_xhr.send(_this.request.getBody()),function(){_xhr.removeEventListener("load",onLoad),_xhr.removeEventListener("error",onError),_xhr.abort()}})}return XHRConnection.prototype.setDetectedContentType=function(req,_xhr){if(null==req.headers||null==req.headers.get("Content-Type"))switch(req.contentType){case ContentType.NONE:break;case ContentType.JSON:_xhr.setRequestHeader("content-type","application/json");break;case ContentType.FORM:_xhr.setRequestHeader("content-type","application/x-www-form-urlencoded;charset=UTF-8");break;case ContentType.TEXT:_xhr.setRequestHeader("content-type","text/plain");break;case ContentType.BLOB:var blob=req.blob();blob.type&&_xhr.setRequestHeader("content-type",blob.type)}},XHRConnection}(),CookieXSRFStrategy=function(){function CookieXSRFStrategy(_cookieName,_headerName){void 0===_cookieName&&(_cookieName="XSRF-TOKEN"),void 0===_headerName&&(_headerName="X-XSRF-TOKEN"),this._cookieName=_cookieName,this._headerName=_headerName}return CookieXSRFStrategy.prototype.configureRequest=function(req){var xsrfToken=_angular_platformBrowser.ɵgetDOM().getCookie(this._cookieName);xsrfToken&&req.headers.set(this._headerName,xsrfToken)},CookieXSRFStrategy}(),XHRBackend=function(){function XHRBackend(_browserXHR,_baseResponseOptions,_xsrfStrategy){this._browserXHR=_browserXHR,this._baseResponseOptions=_baseResponseOptions,this._xsrfStrategy=_xsrfStrategy}return XHRBackend.prototype.createConnection=function(request){return this._xsrfStrategy.configureRequest(request),new XHRConnection(request,this._browserXHR,this._baseResponseOptions)},XHRBackend}();XHRBackend.decorators=[{type:_angular_core.Injectable}],XHRBackend.ctorParameters=function(){return[{type:BrowserXhr},{type:ResponseOptions},{type:XSRFStrategy}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var RequestOptions=function(){function RequestOptions(opts){void 0===opts&&(opts={});var method=opts.method,headers=opts.headers,body=opts.body,url=opts.url,search=opts.search,params=opts.params,withCredentials=opts.withCredentials,responseType=opts.responseType;this.method=null!=method?normalizeMethodName(method):null,this.headers=null!=headers?headers:null,this.body=null!=body?body:null,this.url=null!=url?url:null,this.params=this._mergeSearchParams(params||search),this.withCredentials=null!=withCredentials?withCredentials:null,this.responseType=null!=responseType?responseType:null}return Object.defineProperty(RequestOptions.prototype,"search",{get:function(){return this.params},set:function(params){this.params=params},enumerable:!0,configurable:!0}),RequestOptions.prototype.merge=function(options){return new RequestOptions({method:options&&null!=options.method?options.method:this.method,headers:options&&null!=options.headers?options.headers:new Headers(this.headers),body:options&&null!=options.body?options.body:this.body,url:options&&null!=options.url?options.url:this.url,params:options&&this._mergeSearchParams(options.params||options.search),withCredentials:options&&null!=options.withCredentials?options.withCredentials:this.withCredentials,responseType:options&&null!=options.responseType?options.responseType:this.responseType})},RequestOptions.prototype._mergeSearchParams=function(params){return params?params instanceof URLSearchParams?params.clone():"string"==typeof params?new URLSearchParams(params):this._parseParams(params):this.params},RequestOptions.prototype._parseParams=function(objParams){var _this=this;void 0===objParams&&(objParams={});var params=new URLSearchParams;return Object.keys(objParams).forEach(function(key){var value=objParams[key];Array.isArray(value)?value.forEach(function(item){return _this._appendParam(key,item,params)}):_this._appendParam(key,value,params)}),params},RequestOptions.prototype._appendParam=function(key,value,params){"string"!=typeof value&&(value=JSON.stringify(value)),params.append(key,value)},RequestOptions}(),BaseRequestOptions=function(_super){function BaseRequestOptions(){return _super.call(this,{method:RequestMethod.Get,headers:new Headers})||this}return __extends(BaseRequestOptions,_super),BaseRequestOptions}(RequestOptions);BaseRequestOptions.decorators=[{type:_angular_core.Injectable}],BaseRequestOptions.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var Request=function(_super){function Request(requestOptions){var _this=_super.call(this)||this,url=requestOptions.url;_this.url=requestOptions.url;var paramsArg=requestOptions.params||requestOptions.search;if(paramsArg){var params=void 0;if(params="object"!=typeof paramsArg||paramsArg instanceof URLSearchParams?paramsArg.toString():urlEncodeParams(paramsArg).toString(),params.length>0){var prefix="?";_this.url.indexOf("?")!=-1&&(prefix="&"==_this.url[_this.url.length-1]?"":"&"),_this.url=url+prefix+params}}return _this._body=requestOptions.body,_this.method=normalizeMethodName(requestOptions.method),_this.headers=new Headers(requestOptions.headers),_this.contentType=_this.detectContentType(),_this.withCredentials=requestOptions.withCredentials,_this.responseType=requestOptions.responseType,_this}return __extends(Request,_super),Request.prototype.detectContentType=function(){switch(this.headers.get("content-type")){case"application/json":return ContentType.JSON;case"application/x-www-form-urlencoded":return ContentType.FORM;case"multipart/form-data":return ContentType.FORM_DATA;case"text/plain":case"text/html":return ContentType.TEXT;case"application/octet-stream":return this._body instanceof ArrayBuffer$1?ContentType.ARRAY_BUFFER:ContentType.BLOB;default:return this.detectContentTypeFromBody()}},Request.prototype.detectContentTypeFromBody=function(){return null==this._body?ContentType.NONE:this._body instanceof URLSearchParams?ContentType.FORM:this._body instanceof FormData?ContentType.FORM_DATA:this._body instanceof Blob$1?ContentType.BLOB:this._body instanceof ArrayBuffer$1?ContentType.ARRAY_BUFFER:this._body&&"object"==typeof this._body?ContentType.JSON:ContentType.TEXT},Request.prototype.getBody=function(){switch(this.contentType){case ContentType.JSON:return this.text();case ContentType.FORM:return this.text();case ContentType.FORM_DATA:return this._body;case ContentType.TEXT:return this.text();case ContentType.BLOB:return this.blob();case ContentType.ARRAY_BUFFER:return this.arrayBuffer();default:return null}},Request}(Body),noop=function(){},w="object"==typeof window?window:noop,FormData=w.FormData||noop,Blob$1=w.Blob||noop,ArrayBuffer$1=w.ArrayBuffer||noop,Http=function(){function Http(_backend,_defaultOptions){this._backend=_backend,this._defaultOptions=_defaultOptions}return Http.prototype.request=function(url,options){var responseObservable;if("string"==typeof url)responseObservable=httpRequest(this._backend,new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Get,url)));else{if(!(url instanceof Request))throw new Error("First argument must be a url string or Request instance.");responseObservable=httpRequest(this._backend,url)}return responseObservable},Http.prototype.get=function(url,options){return this.request(new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Get,url)))},Http.prototype.post=function(url,body,options){return this.request(new Request(mergeOptions(this._defaultOptions.merge(new RequestOptions({body:body})),options,RequestMethod.Post,url)))},Http.prototype.put=function(url,body,options){return this.request(new Request(mergeOptions(this._defaultOptions.merge(new RequestOptions({body:body})),options,RequestMethod.Put,url)))},Http.prototype.delete=function(url,options){return this.request(new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Delete,url)))},Http.prototype.patch=function(url,body,options){return this.request(new Request(mergeOptions(this._defaultOptions.merge(new RequestOptions({body:body})),options,RequestMethod.Patch,url)))},Http.prototype.head=function(url,options){return this.request(new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Head,url)))},Http.prototype.options=function(url,options){return this.request(new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Options,url)))},Http}();Http.decorators=[{type:_angular_core.Injectable}],Http.ctorParameters=function(){return[{type:ConnectionBackend},{type:RequestOptions}]};var Jsonp=function(_super){function Jsonp(backend,defaultOptions){return _super.call(this,backend,defaultOptions)||this}return __extends(Jsonp,_super),Jsonp.prototype.request=function(url,options){var responseObservable;if("string"==typeof url&&(url=new Request(mergeOptions(this._defaultOptions,options,RequestMethod.Get,url))),!(url instanceof Request))throw new Error("First argument must be a url string or Request instance.");if(url.method!==RequestMethod.Get)throw new Error("JSONP requests must use GET request method.");return responseObservable=httpRequest(this._backend,url)},Jsonp}(Http);Jsonp.decorators=[{type:_angular_core.Injectable}],Jsonp.ctorParameters=function(){return[{type:ConnectionBackend},{type:RequestOptions}]};var HttpModule=function(){function HttpModule(){}return HttpModule}();HttpModule.decorators=[{type:_angular_core.NgModule,args:[{providers:[{provide:Http,useFactory:httpFactory,deps:[XHRBackend,RequestOptions]},BrowserXhr,{provide:RequestOptions,useClass:BaseRequestOptions},{provide:ResponseOptions,useClass:BaseResponseOptions},XHRBackend,{provide:XSRFStrategy,useFactory:_createDefaultCookieXSRFStrategy}]}]}],HttpModule.ctorParameters=function(){return[]};var JsonpModule=function(){function JsonpModule(){}return JsonpModule}();JsonpModule.decorators=[{type:_angular_core.NgModule,args:[{providers:[{provide:Jsonp,useFactory:jsonpFactory,deps:[JSONPBackend,RequestOptions]},BrowserJsonp,{provide:RequestOptions,useClass:BaseRequestOptions},{provide:ResponseOptions,useClass:BaseResponseOptions},{provide:JSONPBackend,useClass:JSONPBackend_}]}]}],JsonpModule.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var VERSION=new _angular_core.Version("4.2.5");exports.BrowserXhr=BrowserXhr,exports.JSONPBackend=JSONPBackend,exports.JSONPConnection=JSONPConnection,exports.CookieXSRFStrategy=CookieXSRFStrategy,exports.XHRBackend=XHRBackend,exports.XHRConnection=XHRConnection,exports.BaseRequestOptions=BaseRequestOptions,exports.RequestOptions=RequestOptions,exports.BaseResponseOptions=BaseResponseOptions,exports.ResponseOptions=ResponseOptions,exports.ReadyState=ReadyState,exports.RequestMethod=RequestMethod,exports.ResponseContentType=ResponseContentType,exports.ResponseType=ResponseType,exports.Headers=Headers,exports.Http=Http,exports.Jsonp=Jsonp,exports.HttpModule=HttpModule,exports.JsonpModule=JsonpModule,exports.Connection=Connection,exports.ConnectionBackend=ConnectionBackend,exports.XSRFStrategy=XSRFStrategy,exports.Request=Request,exports.Response=Response,exports.QueryEncoder=QueryEncoder,exports.URLSearchParams=URLSearchParams,exports.VERSION=VERSION,exports.ɵg=BrowserJsonp,exports.ɵa=JSONPBackend_,exports.ɵf=Body,exports.ɵb=_createDefaultCookieXSRFStrategy,exports.ɵc=httpFactory,exports.ɵd=jsonpFactory,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=http.umd.min.js.map
