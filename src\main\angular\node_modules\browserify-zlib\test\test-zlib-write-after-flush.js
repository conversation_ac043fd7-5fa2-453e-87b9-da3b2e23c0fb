// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

var tape = require('tape');
var zlib = require('../');
var fs = require('fs');

tape('write after flush', function(t) {
  t.plan(2);
  
  var gzip = zlib.createGzip();
  var gunz = zlib.createUnzip();

  gzip.pipe(gunz);

  var output = '';
  var input = 'A line of data\n';
  gunz.setEncoding('utf8');
  gunz.on('data', function(c) {
    output += c;
  });
  
  gunz.on('end', function() {
    t.equal(output, input);
    
    // Make sure that the flush flag was set back to normal
    t.equal(gzip._flushFlag, zlib.Z_NO_FLUSH);
  });
  
  // make sure that flush/write doesn't trigger an assert failure
  gzip.flush(); write();
  function write() {
    gzip.write(input);
    gzip.end();
    gunz.read(0);
  }
});
