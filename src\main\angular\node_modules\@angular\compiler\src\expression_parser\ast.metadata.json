[{"__symbolic": "module", "version": 3, "metadata": {"ParserError": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "ParseSpan": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}]}}, "AST": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "Quote": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "EmptyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"visit": [{"__symbolic": "method"}]}}, "ImplicitReceiver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"visit": [{"__symbolic": "method"}]}}, "Chain": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Conditional": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyWrite": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "SafePropertyRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "KeyedRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "KeyedWrite": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "BindingPipe": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralPrimitive": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "any"}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralArray": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralMap": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Interpolation": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Binary": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "PrefixNot": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "NonNullAssert": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "MethodCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "SafeMethodCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "FunctionCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "ASTWithSource": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "TemplateBinding": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "ASTWithSource"}]}]}}, "AstVisitor": {"__symbolic": "interface"}, "NullAstVisitor": {"__symbolic": "class", "members": {"visitBinary": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}]}}, "RecursiveAstVisitor": {"__symbolic": "class", "members": {"visitBinary": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitAll": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}]}}, "AstTransformer": {"__symbolic": "class", "members": {"visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitBinary": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitAll": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}]}}, "visitAstChildren": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"ParserError": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "ParseSpan": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}]}}, "AST": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "Quote": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "EmptyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"visit": [{"__symbolic": "method"}]}}, "ImplicitReceiver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"visit": [{"__symbolic": "method"}]}}, "Chain": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Conditional": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "PropertyWrite": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "SafePropertyRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}]}], "visit": [{"__symbolic": "method"}]}}, "KeyedRead": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "KeyedWrite": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "BindingPipe": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralPrimitive": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "any"}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralArray": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "LiteralMap": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Interpolation": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "Binary": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "PrefixNot": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "NonNullAssert": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}]}], "visit": [{"__symbolic": "method"}]}}, "MethodCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "SafeMethodCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "FunctionCall": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "visit": [{"__symbolic": "method"}]}}, "ASTWithSource": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AST"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "AST"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}], "visit": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "TemplateBinding": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "ASTWithSource"}]}]}}, "AstVisitor": {"__symbolic": "interface"}, "NullAstVisitor": {"__symbolic": "class", "members": {"visitBinary": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}]}}, "RecursiveAstVisitor": {"__symbolic": "class", "members": {"visitBinary": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitAll": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}]}}, "AstTransformer": {"__symbolic": "class", "members": {"visitImplicitReceiver": [{"__symbolic": "method"}], "visitInterpolation": [{"__symbolic": "method"}], "visitLiteralPrimitive": [{"__symbolic": "method"}], "visitPropertyRead": [{"__symbolic": "method"}], "visitPropertyWrite": [{"__symbolic": "method"}], "visitSafePropertyRead": [{"__symbolic": "method"}], "visitMethodCall": [{"__symbolic": "method"}], "visitSafeMethodCall": [{"__symbolic": "method"}], "visitFunctionCall": [{"__symbolic": "method"}], "visitLiteralArray": [{"__symbolic": "method"}], "visitLiteralMap": [{"__symbolic": "method"}], "visitBinary": [{"__symbolic": "method"}], "visitPrefixNot": [{"__symbolic": "method"}], "visitNonNullAssert": [{"__symbolic": "method"}], "visitConditional": [{"__symbolic": "method"}], "visitPipe": [{"__symbolic": "method"}], "visitKeyedRead": [{"__symbolic": "method"}], "visitKeyedWrite": [{"__symbolic": "method"}], "visitAll": [{"__symbolic": "method"}], "visitChain": [{"__symbolic": "method"}], "visitQuote": [{"__symbolic": "method"}]}}, "visitAstChildren": {"__symbolic": "function"}}}]