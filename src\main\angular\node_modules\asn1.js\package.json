{"_args": [["asn1.js@4.9.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "asn1.js@4.9.1", "_id": "asn1.js@4.9.1", "_inBundle": false, "_integrity": "sha1-SLokC0WpKA6UdImQull9IWYX/UA=", "_location": "/asn1.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "asn1.js@4.9.1", "name": "asn1.js", "escapedName": "asn1.js", "rawSpec": "4.9.1", "saveSpec": null, "fetchSpec": "4.9.1"}, "_requiredBy": ["/parse-asn1"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/asn1.js/-/asn1.js-4.9.1.tgz", "_spec": "4.9.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/indutny/asn1.js/issues"}, "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}, "description": "ASN.1 encoder and decoder", "devDependencies": {"mocha": "^2.3.4"}, "homepage": "https://github.com/indutny/asn1.js", "keywords": ["asn.1", "der"], "license": "MIT", "main": "lib/asn1.js", "name": "asn1.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/asn1.js.git"}, "scripts": {"test": "mocha --reporter spec test/*-test.js rfc/2560/test/*-test.js rfc/5280/test/*-test.js"}, "version": "4.9.1"}