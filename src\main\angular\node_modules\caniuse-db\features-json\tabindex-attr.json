{"title": "tabindex global attribute", "description": "Specifies the focusability of the element and in what order (if any) it should become focused (relative to other elements) when \"tabbing\" through the document.", "spec": "https://html.spec.whatwg.org/multipage/interaction.html#attr-tabindex", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex", "title": "Mozilla Developer Network (MDN) documentation - tabindex attribute"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "u", "6": "u", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y #2", "6": "y #2", "6.1": "y #2", "7": "y #2", "7.1": "y #2", "8": "y #2", "9": "y #2", "9.1": "y #2", "10": "y #2", "10.1": "y #2", "11": "y #2", "TP": "y #2"}, "opera": {"9": "u", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "y #2 #3", "4.0-4.1": "y #2 #3", "4.2-4.3": "y #2 #3", "5.0-5.1": "y #2 #3", "6.0-6.1": "y #2 #3", "7.0-7.1": "y #2 #3", "8": "y #2 #3", "8.1-8.4": "y #2 #3", "9.0-9.2": "y #2 #3", "9.3": "y #2 #3", "10.0-10.2": "y #2 #3", "10.3": "y #2 #3", "11": "y #2 #3"}, "op_mini": {"all": "u"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "u", "4.1": "u", "4.2-4.3": "u", "4.4": "u", "4.4.3-4.4.4": "u", "56": "u"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "u", "37": "u"}, "and_chr": {"59": "u"}, "and_ff": {"54": "u"}, "ie_mob": {"10": "u", "11": "u"}, "and_uc": {"11.4": "u"}, "samsung": {"4": "u", "5": "u"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "u"}}, "notes": "Mac OS X \"Full Keyboard Access\" refers to setting Keyboard→Shortcuts→Full Keyboard Access to \"All controls\" in the System Preferences.\r\n\r\n\"Unknown\" support for mobile browsers is due to lacking a method of tabbing through fields.", "notes_by_num": {"1": "On Mac OS X, unless Full Keyboard Access is enabled, `<a>` elements are not keyboard-focusable, even if they have `tabindex=\"0\"`.", "2": "`<a>` elements are never keyboard-focusable, even if they have `tabindex=\"0\"`. Unless Full Keyboard Access is enabled, then `<button>`s, radio buttons, and checkboxes are also not keyboard-focusable, even if they have `tabindex=\"0\"`.", "3": "Has \"previous\" and \"next\" virtual keyboard buttons that follow tabindex order."}, "usage_perc_y": 50.56, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "tabindex,tab,tabIndex", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}