{"version": 3, "file": "get-dependent-files.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["utilities/get-dependent-files.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,iCAAiC;AACjC,6BAA6B;AAC7B,6BAA6B;AAC7B,uCAAuC;AAGvC,MAAM,QAAQ,GAAQ,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC7C,MAAM,UAAU,GAAQ,SAAS,CAAC,IAAI,CAAC,CAAC;AAiBxC;;;;;GAKG;AACH,4BAAmC,QAAgB;IACjD,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;SAC9B,IAAI,CAAC,CAAC,QAAgB;QACrB,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;AACP,CAAC;AALD,gDAKC;AAED;;;;;;;;GAQG;AACH,0BAAiC,IAAmB;IAClD,MAAM,CAAC,IAAI,CAAC,UAAU;SACnB,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAE,gBAAgB;SAC/E,GAAG,CAAC,CAAC,IAA0B;QAC9B,IAAI,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3C,MAAM,CAAC;YACL,aAAa,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,GAAG,EAAE,eAAe,CAAC,GAAG;YACxB,GAAG,EAAE,eAAe,CAAC,GAAG;SACzB,CAAC;IACJ,CAAC,CAAC,CAAC;AACP,CAAC;AAXD,4CAWC;AAED;;;;;;;GAOG;AACH,sBAA6B,OAAe;IAC1C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAC/D,IAAI,CAAC,CAAC,SAAmB;QACxB,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACP,CAAC;AALD,oCAKC;AAED;;;;;;;;;;;;;;;GAeG;AACH,+BAAsC,QAAgB;IACpD,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAC7E,IAAI,CAAC,CAAC,KAAe;QACpB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI;YACvB,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AATD,sDASC;AAED;;;;;;;;;GASG;AACH,2BAAkC,QAAgB,EAAE,QAAgB;IAClE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SAC/D,IAAI,CAAC,CAAC,KAAe,KAAK,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;SAClF,IAAI,CAAC,CAAC,OAAwB,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/E,IAAI,CAAC,CAAC,gBAAkC;QACvC,IAAI,QAAQ,GAAc,EAAE,CAAC;QAC7B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK;YACxB,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtC,QAAQ,CAAC,UAAU,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,QAAQ,CAAC;IAClB,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,QAAmB;QACxB,IAAI,aAAa,GAAc,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ;YACpC,MAAM,oBAAoB,GAAmB,QAAQ,CAAC,QAAQ,CAAC;iBAC5D,MAAM,CAAC,YAAY;gBAClB,+BAA+B;gBAC/B,IAAI,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;gBAC/D,IAAI,gBAAgB,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;gBACvE,IAAI,eAAe,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC;gBACvE,MAAM,CAAC,WAAW,IAAI,gBAAgB,IAAI,eAAe,CAAC;YAC5D,CAAC,CAAC;iBACD,MAAM,CAAC,YAAY;gBAClB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;gBAClF,IAAI,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC9C,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBAC1D,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC,CAAC;gBAC7E,MAAM,CAAC,CAAC,cAAc,KAAK,UAAU,CAAC,IAAI,CAAC,gBAAgB,KAAK,UAAU,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;YACL,EAAE,CAAC,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpC,aAAa,CAAC,QAAQ,CAAC,GAAG,oBAAoB,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AApCD,8CAoCC"}