{"_args": [["@angular/cli@1.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@angular/cli@1.2.0", "_id": "@angular/cli@1.2.0", "_inBundle": false, "_integrity": "sha1-39i4mD7DfCttf5AurWA5bXtXFZc=", "_location": "/@angular/cli", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/cli@1.2.0", "name": "@angular/cli", "escapedName": "@angular%2fcli", "scope": "@angular", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/cli/-/cli-1.2.0.tgz", "_spec": "1.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "Angular Authors"}, "bin": {"ng": "bin/ng"}, "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "dependencies": {"@ngtools/json-schema": "1.1.0", "@ngtools/webpack": "1.5.0", "autoprefixer": "^6.5.3", "chalk": "^1.1.3", "common-tags": "^1.3.1", "core-object": "^3.1.0", "css-loader": "^0.28.1", "cssnano": "^3.10.0", "denodeify": "^1.2.1", "diff": "^3.1.0", "ember-cli-normalize-entity-name": "^1.0.0", "ember-cli-string-utils": "^1.0.0", "exports-loader": "^0.6.3", "extract-text-webpack-plugin": "^2.1.0", "file-loader": "^0.10.0", "fs-extra": "^3.0.1", "get-caller-file": "^1.0.0", "glob": "^7.0.3", "heimdalljs": "^0.2.4", "heimdalljs-logger": "^0.1.9", "html-webpack-plugin": "^2.19.0", "inflection": "^1.7.0", "inquirer": "^3.0.0", "isbinaryfile": "^3.0.0", "istanbul-instrumenter-loader": "^2.0.0", "json-loader": "^0.5.4", "less": "^2.7.2", "less-loader": "^4.0.2", "license-webpack-plugin": "^0.4.2", "lodash": "^4.11.1", "memory-fs": "^0.4.1", "minimatch": "^3.0.3", "node-modules-path": "^1.0.0", "node-sass": "^4.3.0", "nopt": "^4.0.1", "opn": "4.0.2", "portfinder": "~1.0.12", "postcss-loader": "^1.3.3", "postcss-url": "^5.1.2", "raw-loader": "^0.5.1", "resolve": "^1.1.7", "rsvp": "^3.0.17", "rxjs": "^5.0.1", "sass-loader": "^6.0.3", "script-loader": "^0.7.0", "semver": "^5.1.0", "silent-error": "^1.0.0", "source-map-loader": "^0.2.0", "style-loader": "^0.13.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.1", "temp": "0.8.3", "typescript": ">=2.0.0 <2.4.0", "url-loader": "^0.5.7", "walk-sync": "^0.3.1", "webpack": "~2.4.0", "webpack-dev-middleware": "^1.10.2", "webpack-dev-server": "~2.4.5", "webpack-merge": "^2.4.0", "zone.js": "^0.8.4"}, "description": "CLI tool for Angular", "engines": {"node": ">= 6.9.0", "npm": ">= 3.0.0"}, "homepage": "https://github.com/angular/angular-cli", "keywords": ["angular", "angular-cli", "Angular CLI"], "license": "MIT", "main": "lib/cli/index.js", "name": "@angular/cli", "optionalDependencies": {"node-sass": "^4.3.0"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular-cli.git"}, "trackingCode": "**********-19", "version": "1.2.0"}