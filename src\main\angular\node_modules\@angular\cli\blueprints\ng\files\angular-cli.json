{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "project": {
    "name": "<%= htmlComponentName %>"
  },
  "apps": [
    {
      "root": "<%= sourceDir %>",
      "outDir": "dist",
      "assets": [
        "assets",
        "favicon.ico"
      ],
      "index": "index.html",
      "main": "main.ts",
      "polyfills": "polyfills.ts",
      "test": "test.ts",
      "tsconfig": "tsconfig.app.json",
      "testTsconfig": "tsconfig.spec.json",
      "prefix": "<%= prefix %>",
      "styles": [
        "styles.<%= styleExt %>"
      ],
      "scripts": [],
      "environmentSource": "environments/environment.ts",
      "environments": {
        "dev": "environments/environment.ts",
        "prod": "environments/environment.prod.ts"
      }
    }
  ],
  "e2e": {
    "protractor": {
      "config": "./protractor.conf.js"
    }
  },
  "lint": [
    {
      "project": "<%= sourceDir %>/tsconfig.app.json"
    },
    {
      "project": "<%= sourceDir %>/tsconfig.spec.json"
    },
    {
      "project": "e2e/tsconfig.e2e.json"
    }
  ],
  "test": {
    "karma": {
      "config": "./karma.conf.js"
    }
  },
  "defaults": {
    "styleExt": "<%= styleExt %>",<% if (!minimal) { %>
    "component": {}<% } else { %>
    "component": {
      "spec": false,
      "inlineStyle": true,
      "inlineTemplate": true
    },
    "directive": {
      "spec": false
    },
    "class": {
      "spec": false
    },
    "guard": {
      "spec": false
    },
    "module": {
      "spec": false
    },
    "pipe": {
      "spec": false
    },
    "service": {
      "spec": false
    } <% } %>
  }
}
