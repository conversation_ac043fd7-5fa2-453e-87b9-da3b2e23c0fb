{"version": 3, "file": "evaluator.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/evaluator.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AAGjC,mCAAiR;AAGjR,yDAAyD;AACzD,IAAM,uBAAuB,GACxB,EAAE,CAAC,UAAkB,CAAC,aAAa,IAAK,EAAE,CAAC,UAAkB,CAAC,uBAAuB,CAAC;AAE3F,wBAAwB,cAAiC,EAAE,UAAkB;IAC3E,IAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;IAC7C,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAC/D,IAAM,wBAAwB,GAAgC,UAAU,CAAC;QACzE,IAAM,MAAI,GAAG,wBAAwB,CAAC,IAAI,CAAC;QAC3C,EAAE,CAAC,CAAC,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAI,CAAC,IAAI,KAAK,UAAU,CAAC;QAClC,CAAC;IACH,CAAC;IACD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAED,kBAAkB,cAAiC,EAAE,KAAa;IAChE,IAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;IAC7C,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QACjD,IAAM,UAAU,GAAkB,UAAU,CAAC;QAC7C,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK,KAAK,CAAC;IACnC,CAAC;IACD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,wBAAwB,IAAa,EAAE,EAA8B;IACnE,MAAM,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAA,IAAI,IAAI,OAAA,CAAC,EAAE,CAAC,IAAI,CAAC,EAAT,CAAS,CAAC,CAAC;AACnD,CAAC;AAED,qBAA4B,KAAU;IACpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AACjC,CAAC;AAFD,kCAEC;AAED,mBAAmB,GAAQ;IACzB,MAAM,CAAC,GAAG,KAAK,SAAS,CAAC;AAC3B,CAAC;AAgBD,6BAA6B,IAAa;IACxC,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QACrD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACD,MAAM,CAAgB,IAAI,CAAC;AAC7B,CAAC;AAED,eAAe;AACf,qBACI,OAAe,EAAE,IAAc,EAAE,OAAkC,EACnE,UAA0B;IAC5B,IAAI,MAAqB,CAAC;IAC1B,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACT,UAAU,GAAG,UAAU,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACrD,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACT,IAAA,4EACqE,EADpE,cAAI,EAAE,wBAAS,CACsD;YAC5E,MAAM,GAAG,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,SAAA,EAAE,IAAI,MAAA,EAAE,SAAS,WAAA,EAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACZ,MAAM,GAAG,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,SAAA,EAAC,CAAC;IAC1C,CAAC;IACD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IACD,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAnBD,kCAmBC;AAED;;;GAGG;AACH;IACE,mBACY,OAAgB,EAAU,OAAoC,EAC9D,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;QAD9B,YAAO,GAAP,OAAO,CAAS;QAAU,YAAO,GAAP,OAAO,CAA6B;QAC9D,YAAO,GAAP,OAAO,CAAuB;IAAG,CAAC;IAE9C,0BAAM,GAAN,UAAO,IAAa;QAClB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAiB,IAAK,CAAC,IAAI,CAAC;QACpC,CAAC;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACvC,EAAE,CAAC,CAAC,wBAAe,CAAC,MAAM,CAAC,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,EAAC,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,8BAAU,GAAjB,UAAkB,IAAa;QAC7B,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAoB,CAAC,CAAC;IAClE,CAAC;IAEO,oCAAgB,GAAxB,UAAyB,IAAa,EAAE,OAA8B;QAAtE,iBAmFC;QAlFC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;oBACxC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAA,KAAK;wBAC/B,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;4BACpD,IAAM,kBAAkB,GAA0B,KAAK,CAAC;4BACxD,MAAM,CAAC,KAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;wBACxE,CAAC;wBACD,MAAM,CAAC,KAAK,CAAC;oBACf,CAAC,CAAC,CAAC;gBACL,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB;oBACvC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,EAArC,CAAqC,CAAC,CAAC;gBAC9E,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;oBAC/B,IAAM,cAAc,GAAsB,IAAI,CAAC;oBAC/C,qCAAqC;oBACrC,EAAE,CAAC,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC;wBACxC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;wBACxD,IAAM,SAAS,GAAiC,cAAc,CAAC,UAAW,CAAC,UAAU,CAAC;wBACtF,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC;4BACzC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;4BAChE,2BAA2B;4BAC3B,IAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;4BAChD,EAAE,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gCAC5C,MAAM,CAAC,IAAI,CAAC;4BACd,CAAC;wBACH,CAAC;oBACH,CAAC;oBAED,mCAAmC;oBACnC,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC;wBACtC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;wBACtD,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;oBACrE,MAAM,CAAC,KAAK,CAAC;gBACf,KAAK,EAAE,CAAC,UAAU,CAAC,6BAA6B,CAAC;gBACjD,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;gBACjC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;gBAClC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;gBAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;gBAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;gBAChC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;gBAChC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;gBAClC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;oBAC7B,MAAM,CAAC,IAAI,CAAC;gBACd,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;oBACxC,IAAM,uBAAuB,GAA+B,IAAI,CAAC;oBACjE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC5E,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;oBACjC,IAAM,gBAAgB,GAAwB,IAAI,CAAC;oBACnD,MAAM,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5C,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;wBAC7B,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;wBACjC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;wBAChC,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC;wBAC3C,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;4BAC5B,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;gCACxD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;wBAC7D;4BACE,MAAM,CAAC,KAAK,CAAC;oBACjB,CAAC;gBACH,KAAK,EAAE,CAAC,UAAU,CAAC,wBAAwB;oBACzC,IAAM,wBAAwB,GAAgC,IAAI,CAAC;oBACnE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC7E,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;oBACxC,IAAM,uBAAuB,GAA+B,IAAI,CAAC;oBACjE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,UAAU,EAAE,OAAO,CAAC;wBACrE,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;gBACjF,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;oBAC3B,IAAI,UAAU,GAAkB,IAAI,CAAC;oBACrC,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACtD,EAAE,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACtD,MAAM,CAAC,IAAI,CAAC;oBACd,CAAC;oBACD,KAAK,CAAC;gBACR,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB;oBACnC,IAAM,kBAAkB,GAA0B,IAAI,CAAC;oBACvD,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CACzC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,EAA/C,CAA+C,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QACD,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,gCAAY,GAAnB,UAAoB,IAAa;QAAjC,iBAmaC;QAlaC,IAAM,CAAC,GAAG,IAAI,CAAC;QACf,IAAI,KAA8B,CAAC;QAEnC,qBAA8C,KAAQ,EAAE,IAAa;YACnE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,yBAAyB,KAAU;YACjC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,IAAI,wBAAe,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC;QAED,IAAM,WAAW,GAAG,UAAC,IAAY;YAC/B,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,EAAE,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC;gBAC5B,0EAA0E;gBAC1E,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,MAAA,EAAC,EAAE,IAAI,CAAC,CAAC;YAC5D,CAAC;YACD,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;gBACxC,IAAI,KAAG,GAA0B,EAAE,CAAC;gBACpC,IAAI,QAAM,GAAa,EAAE,CAAC;gBAC1B,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAA,KAAK;oBACzB,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;wBACnB,KAAK,EAAE,CAAC,UAAU,CAAC,2BAA2B,CAAC;wBAC/C,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB;4BACnC,IAAM,UAAU,GAAyD,KAAK,CAAC;4BAC/E,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;gCACxD,IAAM,MAAI,GAAI,UAAU,CAAC,IAAyB,CAAC,IAAI,CAAC;gCACxD,QAAM,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;4BACpB,CAAC;4BACD,IAAM,YAAY,GAAG,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;4BAClD,EAAE,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gCAClC,KAAK,GAAG,YAAY,CAAC;gCACrB,MAAM,CAAC,IAAI,CAAC;4BACd,CAAC;4BACD,IAAM,aAAa,GAAG,oBAAoB,CAAC,UAAU,CAAC;gCAClD,KAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC;gCACzC,WAAW,CAAC,YAAY,CAAC,CAAC;4BAC9B,EAAE,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gCACnC,KAAK,GAAG,aAAa,CAAC;gCACtB,MAAM,CAAC,IAAI,CAAC,CAAE,yBAAyB;4BACzC,CAAC;4BAAC,IAAI,CAAC,CAAC;gCACN,KAAG,CAAS,YAAY,CAAC,GAAG,aAAa,CAAC;4BAC5C,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,CAAC,KAAK,CAAC;oBAAC,MAAM,CAAC,KAAK,CAAC;gBACxB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,QAAM,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC9C,KAAG,CAAC,UAAU,CAAC,GAAG,QAAM,CAAC;gBAC3B,CAAC;gBACD,MAAM,CAAC,KAAG,CAAC;YACb,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB;gBACvC,IAAI,KAAG,GAAoB,EAAE,CAAC;gBAC9B,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAA,KAAK;oBACzB,IAAM,KAAK,GAAG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBAEvC,kBAAkB;oBAClB,EAAE,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC3B,KAAK,GAAG,KAAK,CAAC;wBACd,MAAM,CAAC,IAAI,CAAC,CAAE,yBAAyB;oBACzC,CAAC;oBAED,4BAA4B;oBAC5B,EAAE,CAAC,CAAC,2CAAkC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC9C,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;4BACpC,GAAG,CAAC,CAAsB,UAAgB,EAAhB,KAAA,KAAK,CAAC,UAAU,EAAhB,cAAgB,EAAhB,IAAgB;gCAArC,IAAM,WAAW,SAAA;gCACpB,KAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;6BACvB;4BACD,MAAM,CAAC;wBACT,CAAC;oBACH,CAAC;oBAED,KAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,CAAC,KAAK,CAAC;oBAAC,MAAM,CAAC,KAAK,CAAC;gBACxB,MAAM,CAAC,KAAG,CAAC;YACb,KAAK,uBAAuB;gBAC1B,IAAI,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAE,IAAY,CAAC,UAAU,CAAC,CAAC;gBACnE,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAC,EAAE,IAAI,CAAC,CAAC;YACjF,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,IAAM,cAAc,GAAsB,IAAI,CAAC;gBAC/C,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC;oBACtC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAM,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAClD,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;wBACtD,IAAM,aAAa,GAAqB,aAAa,CAAC;wBACtD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;oBAClE,CAAC;gBACH,CAAC;gBACD,IAAM,MAAI,GAAG,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAtB,CAAsB,CAAC,CAAC;gBACvF,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,MAAI,CAAC,IAAI,CAAC,wBAAe,CAAC,CAAC,CAAC,CAAC;oBACzE,MAAM,CAAC,MAAI,CAAC,IAAI,CAAC,wBAAe,CAAC,CAAC;gBACpC,CAAC;gBACD,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACpC,EAAE,CAAC,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAC7C,IAAM,UAAU,GAAoB,IAAI,CAAC,YAAY,CACnB,cAAc,CAAC,UAAW,CAAC,UAAU,CAAC,CAAC;wBACzE,EAAE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;4BAAC,MAAM,CAAC,UAAU,CAAC;wBACnD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC;gBACH,CAAC;gBACD,iEAAiE;gBACjE,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC;oBACtC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;oBACxD,MAAM,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACpC,CAAC;gBACD,IAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAChE,EAAE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,MAAM,GAAmC,EAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAC,CAAC;gBAC1F,EAAE,CAAC,CAAC,MAAI,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBACxB,MAAM,CAAC,SAAS,GAAG,MAAI,CAAC;gBAC1B,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACnC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,IAAM,aAAa,GAAqB,IAAI,CAAC;gBAC7C,IAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAtB,CAAsB,CAAC,CAAC;gBACzF,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC,wBAAe,CAAC,CAAC,CAAC,CAAC;oBAC5E,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAe,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC1D,CAAC;gBACD,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAC9D,EAAE,CAAC,CAAC,wBAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBACtC,CAAC;gBACD,IAAM,IAAI,GAAmC,EAAC,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAC,CAAC;gBACxF,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;gBAC3B,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC;gBAC5C,IAAM,wBAAwB,GAAgC,IAAI,CAAC;gBACnE,IAAM,YAAU,GAAG,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBAC1E,EAAE,CAAC,CAAC,eAAe,CAAC,YAAU,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM,CAAC,WAAW,CAAC,YAAU,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBAC1D,EAAE,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACnC,CAAC;gBACD,EAAE,CAAC,CAAC,YAAU,IAAI,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;oBACrE,MAAM,CAAO,YAAW,CAAS,MAAM,CAAC,CAAC;gBAC3C,EAAE,CAAC,CAAC,4CAAmC,CAAC,YAAU,CAAC,CAAC,CAAC,CAAC;oBACpD,kFAAkF;oBAClF,gBAAgB;oBAChB,MAAM,CAAC,WAAW,CACd,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAU,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAC,EAAE,IAAI,CAAC,CAAC;gBAChF,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,cAAA,EAAE,MAAM,QAAA,EAAC,EAAE,IAAI,CAAC,CAAC;YACvE,CAAC;YACD,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBAC3C,IAAM,uBAAuB,GAA+B,IAAI,CAAC;gBACjE,IAAM,YAAU,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;gBACzE,EAAE,CAAC,CAAC,eAAe,CAAC,YAAU,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM,CAAC,WAAW,CAAC,YAAU,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;gBAC5E,EAAE,CAAC,CAAC,eAAe,CAAC,YAAU,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM,CAAC,WAAW,CAAC,YAAU,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,UAAU,CAAC;oBACnD,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;oBAC9D,MAAM,CAAO,YAAW,CAAgB,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,OAAO,EAAE,UAAU,cAAA,EAAE,KAAK,OAAA,EAAC,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC;YACD,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,IAAM,UAAU,GAAkB,IAAI,CAAC;gBACvC,IAAM,MAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gBAC7B,MAAM,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC;YAC3B,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,IAAM,iBAAiB,GAAyB,IAAI,CAAC;gBACrD,IAAM,cAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC;gBAChD,IAAM,YAAY,GACd,UAAA,IAAI;oBACF,EAAE,CAAC,CAAC,cAAY,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;wBACtD,IAAM,aAAa,GAAqB,IAAI,CAAC;wBAC7C,IAAM,MAAI,GAAG,KAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;wBACnD,EAAE,CAAC,CAAC,4CAAmC,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;4BAC9C,MAAM,CAAC,WAAW,CAC6B;gCACzC,UAAU,EAAE,WAAW;gCACvB,MAAM,EAAE,MAAI,CAAC,MAAM;gCACnB,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;6BAC/B,EACD,IAAI,CAAC,CAAC;wBACZ,CAAC;wBACD,0DAA0D;wBAC1D,MAAM,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAI,EAAE,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,EAAC,CAAC;oBACpF,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,IAAM,YAAU,GAAkB,cAAY,CAAC;wBAC/C,IAAM,MAAM,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;wBACrD,EAAE,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,8CAAqC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BAC7E,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;wBACnC,CAAC;wBACD,MAAM,CAAC,WAAW,CACd,WAAW,CAAC,wBAAwB,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,YAAU,CAAC,IAAI,EAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACtF,CAAC;gBACH,CAAC,CAAC;gBACN,IAAM,aAAa,GAAG,YAAY,CAAC,cAAY,CAAC,CAAC;gBACjD,EAAE,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBACnC,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC;gBACD,EAAE,CAAC,CAAC,CAAC,4CAAmC,CAAC,aAAa,CAAC;oBACnD,iBAAiB,CAAC,aAAa,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC9E,IAAM,MAAI,GAAG,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBACxF,8EAA8E;oBAC9E,oDAAoD;oBACR,aAAc,CAAC,SAAS,GAAG,MAAI,CAAC;gBAC9E,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1C,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS;gBAC1B,IAAM,SAAS,GAAqB,IAAI,CAAC;gBAEzC,qDAAqD;gBACrD,IAAM,UAAU,GAAG,SAAS,CAAC,KAAK;qBACV,MAAM,CACH,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW;oBACpC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,gBAAgB,EADvC,CACuC,CAAC;qBAChD,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAApB,CAAoB,CAAC,CAAC;gBAEvD,sFAAsF;gBACtF,qDAAqD;gBACrD,IAAI,SAAS,GAAQ,IAAI,CAAC;gBAC1B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3C,IAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAChC,EAAE,CAAC,CAAC,8CAAqC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACrD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;4BACd,EAAE,CAAC,CAAE,SAAiB,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI;gCACxC,SAAiB,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,IAAI,CAAE,SAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;gCACnF,SAAS,GAAG,SAAS,CAAC;4BACxB,CAAC;wBACH,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,SAAS,GAAG,SAAS,CAAC;wBACxB,CAAC;oBACH,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,MAAM,CAAC,SAAS,CAAC;oBACnB,CAAC;gBACH,CAAC;gBACD,EAAE,CAAC,CAAC,SAAS,CAAC;oBAAC,MAAM,CAAC,SAAS,CAAC;gBAChC,KAAK,CAAC;YACR,KAAK,EAAE,CAAC,UAAU,CAAC,6BAA6B,CAAC;YACjD,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,MAAM,CAAsB,IAAK,CAAC,IAAI,CAAC;YACzC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,MAAM,CAAC,UAAU,CAAwB,IAAK,CAAC,IAAI,CAAC,CAAC;YACvD,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC,EAAE,IAAI,CAAC,CAAC;YACnE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAC,EAAE,IAAI,CAAC,CAAC;YACtE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAC,EAAE,IAAI,CAAC,CAAC;YACtE,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAC,EAAE,IAAI,CAAC,CAAC;YACvE,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS;gBAC1B,IAAM,aAAa,GAAqB,IAAI,CAAC;gBAC7C,MAAM,CAAC,WAAW,CACd;oBACE,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;iBAC1D,EACD,IAAI,CAAC,CAAC;YACZ,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gBAC5B,MAAM,CAAC,IAAI,CAAC;YACd,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gBAC5B,MAAM,CAAC,IAAI,CAAC;YACd,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;gBAC7B,MAAM,CAAC,KAAK,CAAC;YACf,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;gBACxC,IAAM,uBAAuB,GAA+B,IAAI,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAC/D,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;gBACxC,IAAM,aAAa,GAAqB,IAAI,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACrD,KAAK,EAAE,CAAC,UAAU,CAAC,qBAAqB;gBACtC,IAAM,qBAAqB,GAA6B,IAAI,CAAC;gBAC7D,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;gBACjE,EAAE,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACvC,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS;4BAC1B,MAAM,CAAC,CAAC,OAAO,CAAC;wBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;4BAC3B,MAAM,CAAC,CAAC,OAAO,CAAC;wBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;4BAC3B,MAAM,CAAC,CAAC,OAAO,CAAC;wBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;4BACjC,MAAM,CAAC,CAAC,OAAO,CAAC;oBACpB,CAAC;gBACH,CAAC;gBACD,IAAI,YAAY,SAAQ,CAAC;gBACzB,MAAM,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACvC,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS;wBAC1B,YAAY,GAAG,GAAG,CAAC;wBACnB,KAAK,CAAC;oBACR,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;wBAC3B,YAAY,GAAG,GAAG,CAAC;wBACnB,KAAK,CAAC;oBACR,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;wBAC3B,YAAY,GAAG,GAAG,CAAC;wBACnB,KAAK,CAAC;oBACR,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;wBACjC,YAAY,GAAG,GAAG,CAAC;wBACnB,KAAK,CAAC;oBACR;wBACE,MAAM,CAAC,SAAS,CAAC;gBACrB,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAC,EAAE,IAAI,CAAC,CAAC;YAC1F,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;gBACjC,IAAM,gBAAgB,GAAwB,IAAI,CAAC;gBACnD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACxD,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACxC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;wBAC1C,MAAM,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;4BAC5C,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gCAC5B,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;gCACxC,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gCAC/B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,QAAQ;gCACzB,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gCAC3B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;gCAClC,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB;gCACvC,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;gCACxC,MAAM,CAAM,IAAI,KAAU,KAAK,CAAC;4BAClC,KAAK,EAAE,CAAC,UAAU,CAAC,4BAA4B;gCAC7C,MAAM,CAAM,IAAI,KAAU,KAAK,CAAC;4BAClC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gCAC9B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;gCACjC,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;gCACpC,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB;gCACvC,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,qBAAqB;gCACtC,MAAM,CAAO,IAAK,IAAU,KAAM,CAAC;4BACrC,KAAK,EAAE,CAAC,UAAU,CAAC,2BAA2B;gCAC5C,MAAM,CAAM,IAAI,IAAS,KAAK,CAAC;4BACjC,KAAK,EAAE,CAAC,UAAU,CAAC,sCAAsC;gCACvD,MAAM,CAAM,IAAI,KAAU,KAAK,CAAC;4BAClC,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS;gCAC1B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gCAC3B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gCAC9B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gCAC3B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;4BAChC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;gCAC7B,MAAM,CAAM,IAAI,GAAQ,KAAK,CAAC;wBAClC,CAAC;oBACH,MAAM,CAAC,WAAW,CACd;wBACE,UAAU,EAAE,OAAO;wBACnB,QAAQ,EAAE,gBAAgB,CAAC,aAAa,CAAC,OAAO,EAAE;wBAClD,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,KAAK;qBACb,EACD,IAAI,CAAC,CAAC;gBACZ,CAAC;gBACD,KAAK,CAAC;YACR,KAAK,EAAE,CAAC,UAAU,CAAC,qBAAqB;gBACtC,IAAM,qBAAqB,GAA6B,IAAI,CAAC;gBAC7D,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBACrE,IAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBACzE,IAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAC1E,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC3B,MAAM,CAAC,SAAS,GAAG,cAAc,GAAG,cAAc,CAAC;gBACrD,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,EAAC,UAAU,EAAE,IAAI,EAAE,SAAS,WAAA,EAAE,cAAc,gBAAA,EAAE,cAAc,gBAAA,EAAC,EAAE,IAAI,CAAC,CAAC;YAC1F,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;YACtC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,6BAA6B,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAC7E,KAAK,EAAE,CAAC,UAAU,CAAC,wBAAwB;gBACzC,MAAM,CAAC,WAAW,CACd,WAAW,CAAC,2DAA2D,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAC5F,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB;gBACnC,IAAM,kBAAkB,GAA0B,IAAI,CAAC;gBACvD,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC1B,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,CAC1C,UAAC,QAAQ,EAAE,OAAO,IAAK,OAAA,QAAQ,GAAW,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC;wBACnE,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EADvB,CACuB,EAC9C,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClD,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,QAAQ,EAAE,OAAO;wBAC/D,IAAM,IAAI,GAAG,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBACnD,IAAM,OAAO,GAAG,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACnD,EAAE,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;4BAAC,MAAM,CAAC,IAAI,CAAC;wBACvC,EAAE,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;4BAAC,MAAM,CAAC,OAAO,CAAC;wBAC7C,EAAE,CAAC,CAAC,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ;4BACxD,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC;4BAChC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG,OAAO,CAAC;wBACnC,CAAC;wBACD,IAAI,MAAM,GAAG,IAAI,CAAC;wBAClB,EAAE,CAAC,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC;4BACpB,MAAM,GAAG,EAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;wBAC7E,CAAC;wBACD,EAAE,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;4BAClB,MAAM,GAAG,EAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAC,CAAC;wBAC9E,CAAC;wBACD,MAAM,CAAC,MAAM,CAAC;oBAChB,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBACjD,CAAC;QACL,CAAC;QACD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,+BAA+B,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC/E,CAAC;IACH,gBAAC;AAAD,CAAC,AAliBD,IAkiBC;AAliBY,8BAAS;AAoiBtB,8BAA8B,IAAa;IACzC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;AACvD,CAAC;AAED,IAAM,KAAK,GAAG,EAAuB,CAAC;AAEtC,sBAAyC,CAAkB;IACzD,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC;AACpB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\nimport {CollectorOptions} from './collector';\nimport {MetadataEntry, MetadataError, MetadataImportedSymbolReferenceExpression, MetadataSymbolicCallExpression, MetadataValue, isMetadataError, isMetadataModuleReferenceExpression, isMetadataSymbolicReferenceExpression, isMetadataSymbolicSpreadExpression} from './schema';\nimport {Symbols} from './symbols';\n\n// In TypeScript 2.1 the spread element kind was renamed.\nconst spreadElementSyntaxKind: ts.SyntaxKind =\n    (ts.SyntaxKind as any).SpreadElement || (ts.SyntaxKind as any).SpreadElementExpression;\n\nfunction isMethodCallOf(callExpression: ts.CallExpression, memberName: string): boolean {\n  const expression = callExpression.expression;\n  if (expression.kind === ts.SyntaxKind.PropertyAccessExpression) {\n    const propertyAccessExpression = <ts.PropertyAccessExpression>expression;\n    const name = propertyAccessExpression.name;\n    if (name.kind == ts.SyntaxKind.Identifier) {\n      return name.text === memberName;\n    }\n  }\n  return false;\n}\n\nfunction isCallOf(callExpression: ts.CallExpression, ident: string): boolean {\n  const expression = callExpression.expression;\n  if (expression.kind === ts.SyntaxKind.Identifier) {\n    const identifier = <ts.Identifier>expression;\n    return identifier.text === ident;\n  }\n  return false;\n}\n\n/**\n * ts.forEachChild stops iterating children when the callback return a truthy value.\n * This method inverts this to implement an `every` style iterator. It will return\n * true if every call to `cb` returns `true`.\n */\nfunction everyNodeChild(node: ts.Node, cb: (node: ts.Node) => boolean) {\n  return !ts.forEachChild(node, node => !cb(node));\n}\n\nexport function isPrimitive(value: any): boolean {\n  return Object(value) !== value;\n}\n\nfunction isDefined(obj: any): boolean {\n  return obj !== undefined;\n}\n\n// import {propertyName as name} from 'place'\n// import {name} from 'place'\nexport interface ImportSpecifierMetadata {\n  name: string;\n  propertyName?: string;\n}\nexport interface ImportMetadata {\n  defaultName?: string;                      // import d from 'place'\n  namespace?: string;                        // import * as d from 'place'\n  namedImports?: ImportSpecifierMetadata[];  // import {a} from 'place'\n  from: string;                              // from 'place'\n}\n\n\nfunction getSourceFileOfNode(node: ts.Node): ts.SourceFile {\n  while (node && node.kind != ts.SyntaxKind.SourceFile) {\n    node = node.parent;\n  }\n  return <ts.SourceFile>node;\n}\n\n/* @internal */\nexport function errorSymbol(\n    message: string, node?: ts.Node, context?: {[name: string]: string},\n    sourceFile?: ts.SourceFile): MetadataError {\n  let result: MetadataError;\n  if (node) {\n    sourceFile = sourceFile || getSourceFileOfNode(node);\n    if (sourceFile) {\n      const {line, character} =\n          ts.getLineAndCharacterOfPosition(sourceFile, node.getStart(sourceFile));\n      result = {__symbolic: 'error', message, line, character};\n    }\n  }\n  if (!result) {\n    result = {__symbolic: 'error', message};\n  }\n  if (context) {\n    result.context = context;\n  }\n  return result;\n}\n\n/**\n * Produce a symbolic representation of an expression folding values into their final value when\n * possible.\n */\nexport class Evaluator {\n  constructor(\n      private symbols: Symbols, private nodeMap: Map<MetadataEntry, ts.Node>,\n      private options: CollectorOptions = {}) {}\n\n  nameOf(node: ts.Node): string|MetadataError {\n    if (node.kind == ts.SyntaxKind.Identifier) {\n      return (<ts.Identifier>node).text;\n    }\n    const result = this.evaluateNode(node);\n    if (isMetadataError(result) || typeof result === 'string') {\n      return result;\n    } else {\n      return errorSymbol('Name expected', node, {received: node.getText()});\n    }\n  }\n\n  /**\n   * Returns true if the expression represented by `node` can be folded into a literal expression.\n   *\n   * For example, a literal is always foldable. This means that literal expressions such as `1.2`\n   * `\"Some value\"` `true` `false` are foldable.\n   *\n   * - An object literal is foldable if all the properties in the literal are foldable.\n   * - An array literal is foldable if all the elements are foldable.\n   * - A call is foldable if it is a call to a Array.prototype.concat or a call to CONST_EXPR.\n   * - A property access is foldable if the object is foldable.\n   * - A array index is foldable if index expression is foldable and the array is foldable.\n   * - Binary operator expressions are foldable if the left and right expressions are foldable and\n   *   it is one of '+', '-', '*', '/', '%', '||', and '&&'.\n   * - An identifier is foldable if a value can be found for its symbol in the evaluator symbol\n   *   table.\n   */\n  public isFoldable(node: ts.Node): boolean {\n    return this.isFoldableWorker(node, new Map<ts.Node, boolean>());\n  }\n\n  private isFoldableWorker(node: ts.Node, folding: Map<ts.Node, boolean>): boolean {\n    if (node) {\n      switch (node.kind) {\n        case ts.SyntaxKind.ObjectLiteralExpression:\n          return everyNodeChild(node, child => {\n            if (child.kind === ts.SyntaxKind.PropertyAssignment) {\n              const propertyAssignment = <ts.PropertyAssignment>child;\n              return this.isFoldableWorker(propertyAssignment.initializer, folding);\n            }\n            return false;\n          });\n        case ts.SyntaxKind.ArrayLiteralExpression:\n          return everyNodeChild(node, child => this.isFoldableWorker(child, folding));\n        case ts.SyntaxKind.CallExpression:\n          const callExpression = <ts.CallExpression>node;\n          // We can fold a <array>.concat(<v>).\n          if (isMethodCallOf(callExpression, 'concat') &&\n              arrayOrEmpty(callExpression.arguments).length === 1) {\n            const arrayNode = (<ts.PropertyAccessExpression>callExpression.expression).expression;\n            if (this.isFoldableWorker(arrayNode, folding) &&\n                this.isFoldableWorker(callExpression.arguments[0], folding)) {\n              // It needs to be an array.\n              const arrayValue = this.evaluateNode(arrayNode);\n              if (arrayValue && Array.isArray(arrayValue)) {\n                return true;\n              }\n            }\n          }\n\n          // We can fold a call to CONST_EXPR\n          if (isCallOf(callExpression, 'CONST_EXPR') &&\n              arrayOrEmpty(callExpression.arguments).length === 1)\n            return this.isFoldableWorker(callExpression.arguments[0], folding);\n          return false;\n        case ts.SyntaxKind.NoSubstitutionTemplateLiteral:\n        case ts.SyntaxKind.StringLiteral:\n        case ts.SyntaxKind.NumericLiteral:\n        case ts.SyntaxKind.NullKeyword:\n        case ts.SyntaxKind.TrueKeyword:\n        case ts.SyntaxKind.FalseKeyword:\n        case ts.SyntaxKind.TemplateHead:\n        case ts.SyntaxKind.TemplateMiddle:\n        case ts.SyntaxKind.TemplateTail:\n          return true;\n        case ts.SyntaxKind.ParenthesizedExpression:\n          const parenthesizedExpression = <ts.ParenthesizedExpression>node;\n          return this.isFoldableWorker(parenthesizedExpression.expression, folding);\n        case ts.SyntaxKind.BinaryExpression:\n          const binaryExpression = <ts.BinaryExpression>node;\n          switch (binaryExpression.operatorToken.kind) {\n            case ts.SyntaxKind.PlusToken:\n            case ts.SyntaxKind.MinusToken:\n            case ts.SyntaxKind.AsteriskToken:\n            case ts.SyntaxKind.SlashToken:\n            case ts.SyntaxKind.PercentToken:\n            case ts.SyntaxKind.AmpersandAmpersandToken:\n            case ts.SyntaxKind.BarBarToken:\n              return this.isFoldableWorker(binaryExpression.left, folding) &&\n                  this.isFoldableWorker(binaryExpression.right, folding);\n            default:\n              return false;\n          }\n        case ts.SyntaxKind.PropertyAccessExpression:\n          const propertyAccessExpression = <ts.PropertyAccessExpression>node;\n          return this.isFoldableWorker(propertyAccessExpression.expression, folding);\n        case ts.SyntaxKind.ElementAccessExpression:\n          const elementAccessExpression = <ts.ElementAccessExpression>node;\n          return this.isFoldableWorker(elementAccessExpression.expression, folding) &&\n              this.isFoldableWorker(elementAccessExpression.argumentExpression, folding);\n        case ts.SyntaxKind.Identifier:\n          let identifier = <ts.Identifier>node;\n          let reference = this.symbols.resolve(identifier.text);\n          if (reference !== undefined && isPrimitive(reference)) {\n            return true;\n          }\n          break;\n        case ts.SyntaxKind.TemplateExpression:\n          const templateExpression = <ts.TemplateExpression>node;\n          return templateExpression.templateSpans.every(\n              span => this.isFoldableWorker(span.expression, folding));\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Produce a JSON serialiable object representing `node`. The foldable values in the expression\n   * tree are folded. For example, a node representing `1 + 2` is folded into `3`.\n   */\n  public evaluateNode(node: ts.Node): MetadataValue {\n    const t = this;\n    let error: MetadataError|undefined;\n\n    function recordEntry<T extends MetadataEntry>(entry: T, node: ts.Node): T {\n      t.nodeMap.set(entry, node);\n      return entry;\n    }\n\n    function isFoldableError(value: any): value is MetadataError {\n      return !t.options.verboseInvalidExpression && isMetadataError(value);\n    }\n\n    const resolveName = (name: string): MetadataValue => {\n      const reference = this.symbols.resolve(name);\n      if (reference === undefined) {\n        // Encode as a global reference. StaticReflector will check the reference.\n        return recordEntry({__symbolic: 'reference', name}, node);\n      }\n      return reference;\n    };\n\n    switch (node.kind) {\n      case ts.SyntaxKind.ObjectLiteralExpression:\n        let obj: {[name: string]: any} = {};\n        let quoted: string[] = [];\n        ts.forEachChild(node, child => {\n          switch (child.kind) {\n            case ts.SyntaxKind.ShorthandPropertyAssignment:\n            case ts.SyntaxKind.PropertyAssignment:\n              const assignment = <ts.PropertyAssignment|ts.ShorthandPropertyAssignment>child;\n              if (assignment.name.kind == ts.SyntaxKind.StringLiteral) {\n                const name = (assignment.name as ts.StringLiteral).text;\n                quoted.push(name);\n              }\n              const propertyName = this.nameOf(assignment.name);\n              if (isFoldableError(propertyName)) {\n                error = propertyName;\n                return true;\n              }\n              const propertyValue = isPropertyAssignment(assignment) ?\n                  this.evaluateNode(assignment.initializer) :\n                  resolveName(propertyName);\n              if (isFoldableError(propertyValue)) {\n                error = propertyValue;\n                return true;  // Stop the forEachChild.\n              } else {\n                obj[<string>propertyName] = propertyValue;\n              }\n          }\n        });\n        if (error) return error;\n        if (this.options.quotedNames && quoted.length) {\n          obj['$quoted$'] = quoted;\n        }\n        return obj;\n      case ts.SyntaxKind.ArrayLiteralExpression:\n        let arr: MetadataValue[] = [];\n        ts.forEachChild(node, child => {\n          const value = this.evaluateNode(child);\n\n          // Check for error\n          if (isFoldableError(value)) {\n            error = value;\n            return true;  // Stop the forEachChild.\n          }\n\n          // Handle spread expressions\n          if (isMetadataSymbolicSpreadExpression(value)) {\n            if (Array.isArray(value.expression)) {\n              for (const spreadValue of value.expression) {\n                arr.push(spreadValue);\n              }\n              return;\n            }\n          }\n\n          arr.push(value);\n        });\n        if (error) return error;\n        return arr;\n      case spreadElementSyntaxKind:\n        let spreadExpression = this.evaluateNode((node as any).expression);\n        return recordEntry({__symbolic: 'spread', expression: spreadExpression}, node);\n      case ts.SyntaxKind.CallExpression:\n        const callExpression = <ts.CallExpression>node;\n        if (isCallOf(callExpression, 'forwardRef') &&\n            arrayOrEmpty(callExpression.arguments).length === 1) {\n          const firstArgument = callExpression.arguments[0];\n          if (firstArgument.kind == ts.SyntaxKind.ArrowFunction) {\n            const arrowFunction = <ts.ArrowFunction>firstArgument;\n            return recordEntry(this.evaluateNode(arrowFunction.body), node);\n          }\n        }\n        const args = arrayOrEmpty(callExpression.arguments).map(arg => this.evaluateNode(arg));\n        if (!this.options.verboseInvalidExpression && args.some(isMetadataError)) {\n          return args.find(isMetadataError);\n        }\n        if (this.isFoldable(callExpression)) {\n          if (isMethodCallOf(callExpression, 'concat')) {\n            const arrayValue = <MetadataValue[]>this.evaluateNode(\n                (<ts.PropertyAccessExpression>callExpression.expression).expression);\n            if (isFoldableError(arrayValue)) return arrayValue;\n            return arrayValue.concat(args[0]);\n          }\n        }\n        // Always fold a CONST_EXPR even if the argument is not foldable.\n        if (isCallOf(callExpression, 'CONST_EXPR') &&\n            arrayOrEmpty(callExpression.arguments).length === 1) {\n          return recordEntry(args[0], node);\n        }\n        const expression = this.evaluateNode(callExpression.expression);\n        if (isFoldableError(expression)) {\n          return recordEntry(expression, node);\n        }\n        let result: MetadataSymbolicCallExpression = {__symbolic: 'call', expression: expression};\n        if (args && args.length) {\n          result.arguments = args;\n        }\n        return recordEntry(result, node);\n      case ts.SyntaxKind.NewExpression:\n        const newExpression = <ts.NewExpression>node;\n        const newArgs = arrayOrEmpty(newExpression.arguments).map(arg => this.evaluateNode(arg));\n        if (!this.options.verboseInvalidExpression && newArgs.some(isMetadataError)) {\n          return recordEntry(newArgs.find(isMetadataError), node);\n        }\n        const newTarget = this.evaluateNode(newExpression.expression);\n        if (isMetadataError(newTarget)) {\n          return recordEntry(newTarget, node);\n        }\n        const call: MetadataSymbolicCallExpression = {__symbolic: 'new', expression: newTarget};\n        if (newArgs.length) {\n          call.arguments = newArgs;\n        }\n        return recordEntry(call, node);\n      case ts.SyntaxKind.PropertyAccessExpression: {\n        const propertyAccessExpression = <ts.PropertyAccessExpression>node;\n        const expression = this.evaluateNode(propertyAccessExpression.expression);\n        if (isFoldableError(expression)) {\n          return recordEntry(expression, node);\n        }\n        const member = this.nameOf(propertyAccessExpression.name);\n        if (isFoldableError(member)) {\n          return recordEntry(member, node);\n        }\n        if (expression && this.isFoldable(propertyAccessExpression.expression))\n          return (<any>expression)[<string>member];\n        if (isMetadataModuleReferenceExpression(expression)) {\n          // A select into a module refrence and be converted into a reference to the symbol\n          // in the module\n          return recordEntry(\n              {__symbolic: 'reference', module: expression.module, name: member}, node);\n        }\n        return recordEntry({__symbolic: 'select', expression, member}, node);\n      }\n      case ts.SyntaxKind.ElementAccessExpression: {\n        const elementAccessExpression = <ts.ElementAccessExpression>node;\n        const expression = this.evaluateNode(elementAccessExpression.expression);\n        if (isFoldableError(expression)) {\n          return recordEntry(expression, node);\n        }\n        const index = this.evaluateNode(elementAccessExpression.argumentExpression);\n        if (isFoldableError(expression)) {\n          return recordEntry(expression, node);\n        }\n        if (this.isFoldable(elementAccessExpression.expression) &&\n            this.isFoldable(elementAccessExpression.argumentExpression))\n          return (<any>expression)[<string|number>index];\n        return recordEntry({__symbolic: 'index', expression, index}, node);\n      }\n      case ts.SyntaxKind.Identifier:\n        const identifier = <ts.Identifier>node;\n        const name = identifier.text;\n        return resolveName(name);\n      case ts.SyntaxKind.TypeReference:\n        const typeReferenceNode = <ts.TypeReferenceNode>node;\n        const typeNameNode = typeReferenceNode.typeName;\n        const getReference: (typeNameNode: ts.Identifier | ts.QualifiedName) => MetadataValue =\n            node => {\n              if (typeNameNode.kind === ts.SyntaxKind.QualifiedName) {\n                const qualifiedName = <ts.QualifiedName>node;\n                const left = this.evaluateNode(qualifiedName.left);\n                if (isMetadataModuleReferenceExpression(left)) {\n                  return recordEntry(\n                      <MetadataImportedSymbolReferenceExpression>{\n                        __symbolic: 'reference',\n                        module: left.module,\n                        name: qualifiedName.right.text\n                      },\n                      node);\n                }\n                // Record a type reference to a declared type as a select.\n                return {__symbolic: 'select', expression: left, member: qualifiedName.right.text};\n              } else {\n                const identifier = <ts.Identifier>typeNameNode;\n                const symbol = this.symbols.resolve(identifier.text);\n                if (isFoldableError(symbol) || isMetadataSymbolicReferenceExpression(symbol)) {\n                  return recordEntry(symbol, node);\n                }\n                return recordEntry(\n                    errorSymbol('Could not resolve type', node, {typeName: identifier.text}), node);\n              }\n            };\n        const typeReference = getReference(typeNameNode);\n        if (isFoldableError(typeReference)) {\n          return recordEntry(typeReference, node);\n        }\n        if (!isMetadataModuleReferenceExpression(typeReference) &&\n            typeReferenceNode.typeArguments && typeReferenceNode.typeArguments.length) {\n          const args = typeReferenceNode.typeArguments.map(element => this.evaluateNode(element));\n          // TODO: Remove typecast when upgraded to 2.0 as it will be corretly inferred.\n          // Some versions of 1.9 do not infer this correctly.\n          (<MetadataImportedSymbolReferenceExpression>typeReference).arguments = args;\n        }\n        return recordEntry(typeReference, node);\n      case ts.SyntaxKind.UnionType:\n        const unionType = <ts.UnionTypeNode>node;\n\n        // Remove null and undefined from the list of unions.\n        const references = unionType.types\n                               .filter(\n                                   n => n.kind != ts.SyntaxKind.NullKeyword &&\n                                       n.kind != ts.SyntaxKind.UndefinedKeyword)\n                               .map(n => this.evaluateNode(n));\n\n        // The remmaining reference must be the same. If two have type arguments consider them\n        // different even if the type arguments are the same.\n        let candidate: any = null;\n        for (let i = 0; i < references.length; i++) {\n          const reference = references[i];\n          if (isMetadataSymbolicReferenceExpression(reference)) {\n            if (candidate) {\n              if ((reference as any).name == candidate.name &&\n                  (reference as any).module == candidate.module && !(reference as any).arguments) {\n                candidate = reference;\n              }\n            } else {\n              candidate = reference;\n            }\n          } else {\n            return reference;\n          }\n        }\n        if (candidate) return candidate;\n        break;\n      case ts.SyntaxKind.NoSubstitutionTemplateLiteral:\n      case ts.SyntaxKind.StringLiteral:\n      case ts.SyntaxKind.TemplateHead:\n      case ts.SyntaxKind.TemplateTail:\n      case ts.SyntaxKind.TemplateMiddle:\n        return (<ts.LiteralLikeNode>node).text;\n      case ts.SyntaxKind.NumericLiteral:\n        return parseFloat((<ts.LiteralExpression>node).text);\n      case ts.SyntaxKind.AnyKeyword:\n        return recordEntry({__symbolic: 'reference', name: 'any'}, node);\n      case ts.SyntaxKind.StringKeyword:\n        return recordEntry({__symbolic: 'reference', name: 'string'}, node);\n      case ts.SyntaxKind.NumberKeyword:\n        return recordEntry({__symbolic: 'reference', name: 'number'}, node);\n      case ts.SyntaxKind.BooleanKeyword:\n        return recordEntry({__symbolic: 'reference', name: 'boolean'}, node);\n      case ts.SyntaxKind.ArrayType:\n        const arrayTypeNode = <ts.ArrayTypeNode>node;\n        return recordEntry(\n            {\n              __symbolic: 'reference',\n              name: 'Array',\n              arguments: [this.evaluateNode(arrayTypeNode.elementType)]\n            },\n            node);\n      case ts.SyntaxKind.NullKeyword:\n        return null;\n      case ts.SyntaxKind.TrueKeyword:\n        return true;\n      case ts.SyntaxKind.FalseKeyword:\n        return false;\n      case ts.SyntaxKind.ParenthesizedExpression:\n        const parenthesizedExpression = <ts.ParenthesizedExpression>node;\n        return this.evaluateNode(parenthesizedExpression.expression);\n      case ts.SyntaxKind.TypeAssertionExpression:\n        const typeAssertion = <ts.TypeAssertion>node;\n        return this.evaluateNode(typeAssertion.expression);\n      case ts.SyntaxKind.PrefixUnaryExpression:\n        const prefixUnaryExpression = <ts.PrefixUnaryExpression>node;\n        const operand = this.evaluateNode(prefixUnaryExpression.operand);\n        if (isDefined(operand) && isPrimitive(operand)) {\n          switch (prefixUnaryExpression.operator) {\n            case ts.SyntaxKind.PlusToken:\n              return +operand;\n            case ts.SyntaxKind.MinusToken:\n              return -operand;\n            case ts.SyntaxKind.TildeToken:\n              return ~operand;\n            case ts.SyntaxKind.ExclamationToken:\n              return !operand;\n          }\n        }\n        let operatorText: string;\n        switch (prefixUnaryExpression.operator) {\n          case ts.SyntaxKind.PlusToken:\n            operatorText = '+';\n            break;\n          case ts.SyntaxKind.MinusToken:\n            operatorText = '-';\n            break;\n          case ts.SyntaxKind.TildeToken:\n            operatorText = '~';\n            break;\n          case ts.SyntaxKind.ExclamationToken:\n            operatorText = '!';\n            break;\n          default:\n            return undefined;\n        }\n        return recordEntry({__symbolic: 'pre', operator: operatorText, operand: operand}, node);\n      case ts.SyntaxKind.BinaryExpression:\n        const binaryExpression = <ts.BinaryExpression>node;\n        const left = this.evaluateNode(binaryExpression.left);\n        const right = this.evaluateNode(binaryExpression.right);\n        if (isDefined(left) && isDefined(right)) {\n          if (isPrimitive(left) && isPrimitive(right))\n            switch (binaryExpression.operatorToken.kind) {\n              case ts.SyntaxKind.BarBarToken:\n                return <any>left || <any>right;\n              case ts.SyntaxKind.AmpersandAmpersandToken:\n                return <any>left && <any>right;\n              case ts.SyntaxKind.AmpersandToken:\n                return <any>left & <any>right;\n              case ts.SyntaxKind.BarToken:\n                return <any>left | <any>right;\n              case ts.SyntaxKind.CaretToken:\n                return <any>left ^ <any>right;\n              case ts.SyntaxKind.EqualsEqualsToken:\n                return <any>left == <any>right;\n              case ts.SyntaxKind.ExclamationEqualsToken:\n                return <any>left != <any>right;\n              case ts.SyntaxKind.EqualsEqualsEqualsToken:\n                return <any>left === <any>right;\n              case ts.SyntaxKind.ExclamationEqualsEqualsToken:\n                return <any>left !== <any>right;\n              case ts.SyntaxKind.LessThanToken:\n                return <any>left < <any>right;\n              case ts.SyntaxKind.GreaterThanToken:\n                return <any>left > <any>right;\n              case ts.SyntaxKind.LessThanEqualsToken:\n                return <any>left <= <any>right;\n              case ts.SyntaxKind.GreaterThanEqualsToken:\n                return <any>left >= <any>right;\n              case ts.SyntaxKind.LessThanLessThanToken:\n                return (<any>left) << (<any>right);\n              case ts.SyntaxKind.GreaterThanGreaterThanToken:\n                return <any>left >> <any>right;\n              case ts.SyntaxKind.GreaterThanGreaterThanGreaterThanToken:\n                return <any>left >>> <any>right;\n              case ts.SyntaxKind.PlusToken:\n                return <any>left + <any>right;\n              case ts.SyntaxKind.MinusToken:\n                return <any>left - <any>right;\n              case ts.SyntaxKind.AsteriskToken:\n                return <any>left * <any>right;\n              case ts.SyntaxKind.SlashToken:\n                return <any>left / <any>right;\n              case ts.SyntaxKind.PercentToken:\n                return <any>left % <any>right;\n            }\n          return recordEntry(\n              {\n                __symbolic: 'binop',\n                operator: binaryExpression.operatorToken.getText(),\n                left: left,\n                right: right\n              },\n              node);\n        }\n        break;\n      case ts.SyntaxKind.ConditionalExpression:\n        const conditionalExpression = <ts.ConditionalExpression>node;\n        const condition = this.evaluateNode(conditionalExpression.condition);\n        const thenExpression = this.evaluateNode(conditionalExpression.whenTrue);\n        const elseExpression = this.evaluateNode(conditionalExpression.whenFalse);\n        if (isPrimitive(condition)) {\n          return condition ? thenExpression : elseExpression;\n        }\n        return recordEntry({__symbolic: 'if', condition, thenExpression, elseExpression}, node);\n      case ts.SyntaxKind.FunctionExpression:\n      case ts.SyntaxKind.ArrowFunction:\n        return recordEntry(errorSymbol('Function call not supported', node), node);\n      case ts.SyntaxKind.TaggedTemplateExpression:\n        return recordEntry(\n            errorSymbol('Tagged template expressions are not supported in metadata', node), node);\n      case ts.SyntaxKind.TemplateExpression:\n        const templateExpression = <ts.TemplateExpression>node;\n        if (this.isFoldable(node)) {\n          return templateExpression.templateSpans.reduce(\n              (previous, current) => previous + <string>this.evaluateNode(current.expression) +\n                  <string>this.evaluateNode(current.literal),\n              this.evaluateNode(templateExpression.head));\n        } else {\n          return templateExpression.templateSpans.reduce((previous, current) => {\n            const expr = this.evaluateNode(current.expression);\n            const literal = this.evaluateNode(current.literal);\n            if (isFoldableError(expr)) return expr;\n            if (isFoldableError(literal)) return literal;\n            if (typeof previous === 'string' && typeof expr === 'string' &&\n                typeof literal === 'string') {\n              return previous + expr + literal;\n            }\n            let result = expr;\n            if (previous !== '') {\n              result = {__symbolic: 'binop', operator: '+', left: previous, right: expr};\n            }\n            if (literal != '') {\n              result = {__symbolic: 'binop', operator: '+', left: result, right: literal};\n            }\n            return result;\n          }, this.evaluateNode(templateExpression.head));\n        }\n    }\n    return recordEntry(errorSymbol('Expression form not supported', node), node);\n  }\n}\n\nfunction isPropertyAssignment(node: ts.Node): node is ts.PropertyAssignment {\n  return node.kind == ts.SyntaxKind.PropertyAssignment;\n}\n\nconst empty = [] as ts.NodeArray<any>;\n\nfunction arrayOrEmpty<T extends ts.Node>(v: ts.NodeArray<T>): ts.NodeArray<T> {\n  return v || empty;\n}"]}