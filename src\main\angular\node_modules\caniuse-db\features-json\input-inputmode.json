{"title": "inputmode attribute", "description": "The `inputmode` attribute specifies what kind of input mechanism would be most helpful for users entering content into the form control.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#input-modalities:-the-inputmode-attribute", "status": "ls", "links": [{"url": "http://www.wufoo.com/html5/attributes/23-inputmode.html", "title": "Demo on Wufoo (old)"}], "bugs": [], "categories": ["DOM", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "a", "18": "a", "19": "a", "20": "a", "21": "n d #1", "22": "n d #1", "23": "n d #1", "24": "n d #1", "25": "n d #1", "26": "n d #1", "27": "n d #1", "28": "n d #1", "29": "n d #1", "30": "n d #1", "31": "n d #1", "32": "n d #1", "33": "n d #1", "34": "n d #1", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "n d #1", "45": "n d #1", "46": "n d #1", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "n d #1", "53": "n d #1", "54": "n d #1", "55": "n d #1", "56": "n d #1", "57": "n d #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "n d #1"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "Firefox OS is reported to have partial support for this property as `x-inputmode`.\r\n\r\nBlink-based browsers appear to recognize the `inputMode` property on `input` & `textarea` fields though it is unclear what effect it currently has as the feature is still in development.", "notes_by_num": {"1": "Support can be enabled via the `dom.forms.inputmode` flag"}, "usage_perc_y": 0, "usage_perc_a": 0.02, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "inputmode", "chrome_id": "6225984592281600", "firefox_id": "", "webkit_id": "", "shown": true}