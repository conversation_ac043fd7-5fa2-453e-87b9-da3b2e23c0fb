{"_args": [["cli-cursor@2.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cli-cursor@2.1.0", "_id": "cli-cursor@2.1.0", "_inBundle": false, "_integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "_location": "/cli-cursor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cli-cursor@2.1.0", "name": "cli-cursor", "escapedName": "cli-cursor", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/inquirer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cli-cursor/-/cli-cursor-2.1.0.tgz", "_spec": "2.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "dependencies": {"restore-cursor": "^2.0.0"}, "description": "Toggle the CLI cursor", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "cli-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/cli-cursor.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0", "xo": {"esnext": true}}