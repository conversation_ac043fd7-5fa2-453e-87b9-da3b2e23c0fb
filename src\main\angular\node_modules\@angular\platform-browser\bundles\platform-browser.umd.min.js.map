{"version": 3, "file": "platform-browser.umd.min.js", "sources": ["../../../../packages/platform-browser/src/browser/tools/common_tools.ts", "../../../../packages/platform-browser/src/security/url_sanitizer.ts", "../../../../packages/platform-browser/src/security/html_sanitizer.ts", "../../../../packages/platform-browser/src/security/dom_sanitization_service.ts", "../../../../packages/platform-browser/src/browser.ts", "../../../../packages/platform-browser/src/dom/events/key_events.ts", "../../../../packages/platform-browser/src/dom/events/hammer_gestures.ts", "../../../../packages/platform-browser/src/dom/dom_renderer.ts", "../../../../packages/platform-browser/src/dom/events/dom_events.ts", "../../../../packages/platform-browser/src/browser/testability.ts", "../../../../packages/platform-browser/src/browser/title.ts", "../../../../packages/platform-browser/src/dom/debug/ng_probe.ts", "../../../../packages/platform-browser/src/dom/events/event_manager.ts", "../../../../packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../packages/platform-browser/src/browser/meta.ts", "../../../../packages/platform-browser/src/security/style_sanitizer.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/platform-browser/src/dom/dom_adapter.ts", "../../../../packages/platform-browser/src/browser/generic_browser_adapter.ts", "../../../../packages/platform-browser/src/browser/browser_adapter.ts", "../../../../packages/platform-browser/src/browser/location/browser_platform_location.ts", "../../../../packages/platform-browser/src/browser/server-transition.ts", "../../../../packages/platform-browser/src/dom/dom_tokens.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ApplicationRef, ComponentRef} from '@angular/core';\nimport {getDOM} from '../../dom/dom_adapter';\nimport {window} from './browser';\nexport class ChangeDetectionPerfRecord {\n/**\n * @param {?} msPerTick\n * @param {?} numTicks\n */\nconstructor(public msPerTick: number,\npublic numTicks: number) {}\n}\n\nfunction ChangeDetectionPerfRecord_tsickle_Closure_declarations() {\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.msPerTick;\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.numTicks;\n}\n\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nexport class AngularProfiler {\n  appRef: ApplicationRef;\n/**\n * @param {?} ref\n */\nconstructor(ref: ComponentRef<any>) { this.appRef = ref.injector.get(ApplicationRef); }\n/**\n * Exercises change detection in a loop and then prints the average amount of\n * time in milliseconds how long a single round of change detection takes for\n * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n * of 500 milliseconds.\n * \n * Optionally, a user may pass a `config` parameter containing a map of\n * options. Supported options are:\n * \n * `record` (boolean) - causes the profiler to record a CPU profile while\n * it exercises the change detector. Example:\n * \n * ```\n * ng.profiler.timeChangeDetection({record: true})\n * ```\n * @param {?} config\n * @return {?}\n */\ntimeChangeDetection(config: any): ChangeDetectionPerfRecord {\n    const /** @type {?} */ record = config && config['record'];\n    const /** @type {?} */ profileName = 'Change Detection';\n    // Profiler is not available in Android browsers, nor in IE 9 without dev tools opened\n    const /** @type {?} */ isProfilerAvailable = window.console.profile != null;\n    if (record && isProfilerAvailable) {\n      window.console.profile(profileName);\n    }\n    const /** @type {?} */ start = getDOM().performanceNow();\n    let /** @type {?} */ numTicks = 0;\n    while (numTicks < 5 || (getDOM().performanceNow() - start) < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const /** @type {?} */ end = getDOM().performanceNow();\n    if (record && isProfilerAvailable) {\n      // need to cast to <any> because type checker thinks there's no argument\n      // while in fact there is:\n      //\n      // https://developer.mozilla.org/en-US/docs/Web/API/Console/profileEnd\n      ( /** @type {?} */((<any>window.console.profileEnd)))(profileName);\n    }\n    const /** @type {?} */ msPerTick = (end - start) / numTicks;\n    window.console.log(`ran ${numTicks} change detection cycles`);\n    window.console.log(`${msPerTick.toFixed(2)} ms per check`);\n\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\n\nfunction AngularProfiler_tsickle_Closure_declarations() {\n/** @type {?} */\nAngularProfiler.prototype.appRef;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n * \n * This regular expression matches a subset of URLs that will not cause script\n * execution if used in URL context within a HTML document. Specifically, this\n * regular expression matches if (comment from here on and regex copied from\n * Soy's EscapingConventions):\n * (1) Either a protocol in a whitelist (http, https, mailto or ftp).\n * (2) or no protocol.  A protocol must be followed by a colon. The below\n *     allows that by allowing colons only after one of the characters [/?#].\n *     A colon after a hash (#) must be in the fragment.\n *     Otherwise, a colon after a (?) must be in a query.\n *     Otherwise, a colon after a single solidus (/) must be in a path.\n *     Otherwise, a colon after a double solidus (//) must be in the authority\n *     (before port).\n * \n * The pattern disallows &, used in HTML entity declarations before\n * one of the characters in [/?#]. This disallows HTML entities used in the\n * protocol name, which should never happen, e.g. \"h&#116;tp\" for \"http\".\n * It also disallows HTML entities in the first path part of a relative path,\n * e.g. \"foo&lt;bar/baz\".  Our existing escaping functions should not produce\n * that. More importantly, it disallows masking of a colon,\n * e.g. \"javascript&#58;...\".\n * \n * This regular expression was taken from the Closure sanitization library.\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n\n/* A pattern that matches safe srcset values */\nconst /** @type {?} */ SAFE_SRCSET_PATTERN = /^(?:(?:https?|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n */\nconst DATA_URL_PATTERN =\n    /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\\/]+=*$/i;\n/**\n * @param {?} url\n * @return {?}\n */\nexport function sanitizeUrl(url: string): string {\n  url = String(url);\n  if (url.match(SAFE_URL_PATTERN) || url.match(DATA_URL_PATTERN)) return url;\n\n  if (isDevMode()) {\n    getDOM().log(`WARNING: sanitizing unsafe URL value ${url} (see http://g.co/ng/security#xss)`);\n  }\n\n  return 'unsafe:' + url;\n}\n/**\n * @param {?} srcset\n * @return {?}\n */\nexport function sanitizeSrcset(srcset: string): string {\n  srcset = String(srcset);\n  return srcset.split(',').map((srcset) => sanitizeUrl(srcset.trim())).join(', ');\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeSrcset, sanitizeUrl} from './url_sanitizer';\n/**\n * A <body> element that can be safely used to parse untrusted HTML. Lazily initialized below.\n */\nlet inertElement: HTMLElement|null = null;\n/**\n * Lazily initialized to make sure the DOM adapter gets set before use.\n */\nlet DOM: DomAdapter = null !;\n/**\n * Returns an HTML element that is guaranteed to not execute code when creating elements in it.\n * @return {?}\n */\nfunction getInertElement() {\n  if (inertElement) return inertElement;\n  DOM = getDOM();\n\n  // Prefer using <template> element if supported.\n  const /** @type {?} */ templateEl = DOM.createElement('template');\n  if ('content' in templateEl) return templateEl;\n\n  const /** @type {?} */ doc = DOM.createHtmlDocument();\n  inertElement = DOM.querySelector(doc, 'body');\n  if (inertElement == null) {\n    // usually there should be only one body element in the document, but IE doesn't have any, so we\n    // need to create one.\n    const /** @type {?} */ html = DOM.createElement('html', doc);\n    inertElement = DOM.createElement('body', doc);\n    DOM.appendChild(html, inertElement);\n    DOM.appendChild(doc, html);\n  }\n  return inertElement;\n}\n/**\n * @param {?} tags\n * @return {?}\n */\nfunction tagSet(tags: string): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ t of tags.split(',')) res[t] = true;\n  return res;\n}\n/**\n * @param {...?} sets\n * @return {?}\n */\nfunction merge(...sets: {[k: string]: boolean}[]): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ s of sets) {\n    for (const /** @type {?} */ v in s) {\n      if (s.hasOwnProperty(v)) res[v] = true;\n    }\n  }\n  return res;\n}\n\n// Good source of info about elements and attributes\n// http://dev.w3.org/html5/spec/Overview.html#semantics\n// http://simon.html5.org/html-elements\n\n// Safe Void Elements - HTML5\n// http://dev.w3.org/html5/spec/Overview.html#void-elements\nconst /** @type {?} */ VOID_ELEMENTS = tagSet('area,br,col,hr,img,wbr');\n\n// Elements that you can, intentionally, leave open (and which close themselves)\n// http://dev.w3.org/html5/spec/Overview.html#optional-tags\nconst /** @type {?} */ OPTIONAL_END_TAG_BLOCK_ELEMENTS = tagSet('colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr');\nconst /** @type {?} */ OPTIONAL_END_TAG_INLINE_ELEMENTS = tagSet('rp,rt');\nconst /** @type {?} */ OPTIONAL_END_TAG_ELEMENTS =\n    merge(OPTIONAL_END_TAG_INLINE_ELEMENTS, OPTIONAL_END_TAG_BLOCK_ELEMENTS);\n\n// Safe Block Elements - HTML5\nconst /** @type {?} */ BLOCK_ELEMENTS = merge(\n    OPTIONAL_END_TAG_BLOCK_ELEMENTS,\n    tagSet(\n        'address,article,' +\n        'aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,' +\n        'h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul'));\n\n// Inline Elements - HTML5\nconst /** @type {?} */ INLINE_ELEMENTS = merge(\n    OPTIONAL_END_TAG_INLINE_ELEMENTS,\n    tagSet(\n        'a,abbr,acronym,audio,b,' +\n        'bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,' +\n        'samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video'));\n\nconst /** @type {?} */ VALID_ELEMENTS =\n    merge(VOID_ELEMENTS, BLOCK_ELEMENTS, INLINE_ELEMENTS, OPTIONAL_END_TAG_ELEMENTS);\n\n// Attributes that have href and hence need to be sanitized\nconst /** @type {?} */ URI_ATTRS = tagSet('background,cite,href,itemtype,longdesc,poster,src,xlink:href');\n\n// Attributes that have special href set hence need to be sanitized\nconst /** @type {?} */ SRCSET_ATTRS = tagSet('srcset');\n\nconst /** @type {?} */ HTML_ATTRS = tagSet(\n    'abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,' +\n    'compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,' +\n    'ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,' +\n    'scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,' +\n    'valign,value,vspace,width');\n\n// NB: This currently conciously doesn't support SVG. SVG sanitization has had several security\n// issues in the past, so it seems safer to leave it out if possible. If support for binding SVG via\n// innerHTML is required, SVG attributes should be added here.\n\n// NB: Sanitization does not allow <form> elements or other active elements (<button> etc). Those\n// can be sanitized, but they increase security surface area without a legitimate use case, so they\n// are left out here.\n\nconst /** @type {?} */ VALID_ATTRS = merge(URI_ATTRS, SRCSET_ATTRS, HTML_ATTRS);\n/**\n * SanitizingHtmlSerializer serializes a DOM fragment, stripping out any unsafe elements and unsafe\n * attributes.\n */\nclass SanitizingHtmlSerializer {\npublic sanitizedSomething = false;\nprivate buf: string[] = [];\n/**\n * @param {?} el\n * @return {?}\n */\nsanitizeChildren(el: Element): string {\n    // This cannot use a TreeWalker, as it has to run on Angular's various DOM adapters.\n    // However this code never accesses properties off of `document` before deleting its contents\n    // again, so it shouldn't be vulnerable to DOM clobbering.\n    let /** @type {?} */ current: Node = /** @type {?} */(( el.firstChild));\n    while (current) {\n      if (DOM.isElementNode(current)) {\n        this.startElement( /** @type {?} */((current as Element)));\n      } else if (DOM.isTextNode(current)) {\n        this.chars( /** @type {?} */((DOM.nodeValue(current))));\n      } else {\n        // Strip non-element, non-text nodes.\n        this.sanitizedSomething = true;\n      }\n      if (DOM.firstChild(current)) {\n        current = /** @type {?} */(( DOM.firstChild(current)));\n        continue;\n      }\n      while (current) {\n        // Leaving the element. Walk up and to the right, closing tags as we go.\n        if (DOM.isElementNode(current)) {\n          this.endElement( /** @type {?} */((current as Element)));\n        }\n\n        let /** @type {?} */ next = checkClobberedElement(current, /** @type {?} */(( DOM.nextSibling(current))));\n\n        if (next) {\n          current = next;\n          break;\n        }\n\n        current = checkClobberedElement(current, /** @type {?} */(( DOM.parentElement(current))));\n      }\n    }\n    return this.buf.join('');\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprivate startElement(element: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(element).toLowerCase();\n    if (!VALID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.sanitizedSomething = true;\n      return;\n    }\n    this.buf.push('<');\n    this.buf.push(tagName);\n    DOM.attributeMap(element).forEach((value: string, attrName: string) => {\n      const /** @type {?} */ lower = attrName.toLowerCase();\n      if (!VALID_ATTRS.hasOwnProperty(lower)) {\n        this.sanitizedSomething = true;\n        return;\n      }\n      // TODO(martinprobst): Special case image URIs for data:image/...\n      if (URI_ATTRS[lower]) value = sanitizeUrl(value);\n      if (SRCSET_ATTRS[lower]) value = sanitizeSrcset(value);\n      this.buf.push(' ');\n      this.buf.push(attrName);\n      this.buf.push('=\"');\n      this.buf.push(encodeEntities(value));\n      this.buf.push('\"');\n    });\n    this.buf.push('>');\n  }\n/**\n * @param {?} current\n * @return {?}\n */\nprivate endElement(current: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(current).toLowerCase();\n    if (VALID_ELEMENTS.hasOwnProperty(tagName) && !VOID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.buf.push('</');\n      this.buf.push(tagName);\n      this.buf.push('>');\n    }\n  }\n/**\n * @param {?} chars\n * @return {?}\n */\nprivate chars(chars: string) { this.buf.push(encodeEntities(chars)); }\n}\n\nfunction SanitizingHtmlSerializer_tsickle_Closure_declarations() {\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.sanitizedSomething;\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.buf;\n}\n\n/**\n * @param {?} node\n * @param {?} nextNode\n * @return {?}\n */\nfunction checkClobberedElement(node: Node, nextNode: Node): Node {\n  if (nextNode && DOM.contains(node, nextNode)) {\n    throw new Error(\n        `Failed to sanitize html because the element is clobbered: ${DOM.getOuterHTML(node)}`);\n  }\n  return nextNode;\n}\n\n// Regular Expressions for parsing tags and attributes\nconst /** @type {?} */ SURROGATE_PAIR_REGEXP = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n// ! to ~ is the ASCII range.\nconst /** @type {?} */ NON_ALPHANUMERIC_REGEXP = /([^\\#-~ |!])/g;\n/**\n * Escapes all potentially dangerous characters, so that the\n * resulting string can be safely inserted into attribute or\n * element text.\n * @param {?} value\n * @return {?}\n */\nfunction encodeEntities(value: string) {\n  return value.replace(/&/g, '&amp;')\n      .replace(\n          SURROGATE_PAIR_REGEXP,\n          function(match: string) {\n            const /** @type {?} */ hi = match.charCodeAt(0);\n            const /** @type {?} */ low = match.charCodeAt(1);\n            return '&#' + (((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000) + ';';\n          })\n      .replace(\n          NON_ALPHANUMERIC_REGEXP,\n          function(match: string) { return '&#' + match.charCodeAt(0) + ';'; })\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n}\n/**\n * When IE9-11 comes across an unknown namespaced attribute e.g. 'xlink:foo' it adds 'xmlns:ns1'\n * attribute to declare ns1 namespace and prefixes the attribute with 'ns1' (e.g. 'ns1:xlink:foo').\n * \n * This is undesirable since we don't want to allow any of these custom attributes. This method\n * strips them all.\n * @param {?} el\n * @return {?}\n */\nfunction stripCustomNsAttrs(el: Element) {\n  DOM.attributeMap(el).forEach((_, attrName) => {\n    if (attrName === 'xmlns:ns1' || attrName.indexOf('ns1:') === 0) {\n      DOM.removeAttribute(el, attrName);\n    }\n  });\n  for (const /** @type {?} */ n of DOM.childNodesAsList(el)) {\n    if (DOM.isElementNode(n)) stripCustomNsAttrs( /** @type {?} */((n as Element)));\n  }\n}\n/**\n * Sanitizes the given unsafe, untrusted HTML fragment, and returns HTML text that is safe to add to\n * the DOM in a browser environment.\n * @param {?} defaultDoc\n * @param {?} unsafeHtmlInput\n * @return {?}\n */\nexport function sanitizeHtml(defaultDoc: any, unsafeHtmlInput: string): string {\n  try {\n    const /** @type {?} */ containerEl = getInertElement();\n    // Make sure unsafeHtml is actually a string (TypeScript types are not enforced at runtime).\n    let /** @type {?} */ unsafeHtml = unsafeHtmlInput ? String(unsafeHtmlInput) : '';\n\n    // mXSS protection. Repeatedly parse the document to make sure it stabilizes, so that a browser\n    // trying to auto-correct incorrect HTML cannot cause formerly inert HTML to become dangerous.\n    let /** @type {?} */ mXSSAttempts = 5;\n    let /** @type {?} */ parsedHtml = unsafeHtml;\n\n    do {\n      if (mXSSAttempts === 0) {\n        throw new Error('Failed to sanitize html because the input is unstable');\n      }\n      mXSSAttempts--;\n\n      unsafeHtml = parsedHtml;\n      DOM.setInnerHTML(containerEl, unsafeHtml);\n      if (defaultDoc.documentMode) {\n        // strip custom-namespaced attributes on IE<=11\n        stripCustomNsAttrs(containerEl);\n      }\n      parsedHtml = DOM.getInnerHTML(containerEl);\n    } while (unsafeHtml !== parsedHtml);\n\n    const /** @type {?} */ sanitizer = new SanitizingHtmlSerializer();\n    const /** @type {?} */ safeHtml = sanitizer.sanitizeChildren(DOM.getTemplateContent(containerEl) || containerEl);\n\n    // Clear out the body element.\n    const /** @type {?} */ parent = DOM.getTemplateContent(containerEl) || containerEl;\n    for (const /** @type {?} */ child of DOM.childNodesAsList(parent)) {\n      DOM.removeChild(parent, child);\n    }\n\n    if (isDevMode() && sanitizer.sanitizedSomething) {\n      DOM.log('WARNING: sanitizing HTML stripped some content (see http://g.co/ng/security#xss).');\n    }\n\n    return safeHtml;\n  } catch ( /** @type {?} */e) {\n    // In case anything goes wrong, clear out inertElement to reset the entire DOM structure.\n    inertElement = null;\n    throw e;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Sanitizer, SecurityContext} from '@angular/core';\n\nimport {DOCUMENT} from '../dom/dom_tokens';\n\nimport {sanitizeHtml} from './html_sanitizer';\nimport {sanitizeStyle} from './style_sanitizer';\nimport {sanitizeUrl} from './url_sanitizer';\n\nexport {SecurityContext};\n\n\n\n/**\n * Marker interface for a value that's safe to use in a particular context.\n *\n * @stable\n */\nexport interface SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as HTML.\n *\n * @stable\n */\nexport interface SafeHtml extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as style (CSS).\n *\n * @stable\n */\nexport interface SafeStyle extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as JavaScript.\n *\n * @stable\n */\nexport interface SafeScript extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL linking to a document.\n *\n * @stable\n */\nexport interface SafeUrl extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL to load executable code from.\n *\n * @stable\n */\nexport interface SafeResourceUrl extends SafeValue {}\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n * \n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n * \n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n * \n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n * \n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n * \n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n * \n * \\@security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](http://g.co/ng/security).\n * \n * \\@stable\n * @abstract\n */\nexport abstract class DomSanitizer implements Sanitizer {\n/**\n * Sanitizes a value for use in the given SecurityContext.\n * \n * If value is trusted for the context, this method will unwrap the contained safe value and use\n * it directly. Otherwise, value will be sanitized to be safe in the given context, for example\n * by replacing URLs that have an unsafe protocol part (such as `javascript:`). The implementation\n * is responsible to make sure that the value can definitely be safely used in the given context.\n * @abstract\n * @param {?} context\n * @param {?} value\n * @return {?}\n */\nsanitize(context: SecurityContext, value: SafeValue|string|null) {}\n/**\n * Bypass security and trust the given value to be safe HTML. Only use this when the bound HTML\n * is unsafe (e.g. contains `<script>` tags) and the code should be executed. The sanitizer will\n * leave safe HTML intact, so in most situations this method should not be used.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string) {}\n/**\n * Bypass security and trust the given value to be safe style value (CSS).\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string) {}\n/**\n * Bypass security and trust the given value to be safe JavaScript.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe style URL, i.e. a value that can be used\n * in hyperlinks or `<img src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe resource URL, i.e. a location that may\n * be used to load executable code from, like `<script src>`, or `<iframe src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string) {}\n}\nexport class DomSanitizerImpl extends DomSanitizer {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { super(); }\n/**\n * @param {?} ctx\n * @param {?} value\n * @return {?}\n */\nsanitize(ctx: SecurityContext, value: SafeValue|string|null): string|null {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return /** @type {?} */(( value as string));\n      case SecurityContext.HTML:\n        if (value instanceof SafeHtmlImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'HTML');\n        return sanitizeHtml(this._doc, String(value));\n      case SecurityContext.STYLE:\n        if (value instanceof SafeStyleImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Style');\n        return sanitizeStyle( /** @type {?} */((value as string)));\n      case SecurityContext.SCRIPT:\n        if (value instanceof SafeScriptImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Script');\n        throw new Error('unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (value instanceof SafeResourceUrlImpl || value instanceof SafeUrlImpl) {\n          // Allow resource URLs in URL contexts, they are strictly more trusted.\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'URL');\n        return sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (value instanceof SafeResourceUrlImpl) {\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'ResourceURL');\n        throw new Error(\n            'unsafe value used in a resource URL context (see http://g.co/ng/security#xss)');\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @param {?} expectedType\n * @return {?}\n */\nprivate checkNotSafeValue(value: any, expectedType: string) {\n    if (value instanceof SafeValueImpl) {\n      throw new Error(\n          `Required a safe ${expectedType}, got a ${value.getTypeName()} ` +\n          `(see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string): SafeHtml { return new SafeHtmlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string): SafeStyle { return new SafeStyleImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string): SafeScript { return new SafeScriptImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string): SafeUrl { return new SafeUrlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {\n    return new SafeResourceUrlImpl(value);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSanitizerImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSanitizerImpl.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSanitizerImpl.ctorParameters;\n/** @type {?} */\nDomSanitizerImpl.prototype._doc;\n}\n\n/**\n * @abstract\n */\nabstract class SafeValueImpl implements SafeValue {\n/**\n * @param {?} changingThisBreaksApplicationSecurity\n */\nconstructor(public changingThisBreaksApplicationSecurity: string) {\n    // empty\n  }\n/**\n * @abstract\n * @return {?}\n */\ngetTypeName() {}\n/**\n * @return {?}\n */\ntoString() {\n    return `SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity}` +\n        ` (see http://g.co/ng/security#xss)`;\n  }\n}\n\nfunction SafeValueImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nSafeValueImpl.prototype.changingThisBreaksApplicationSecurity;\n}\n\nclass SafeHtmlImpl extends SafeValueImpl implements SafeHtml {\n/**\n * @return {?}\n */\ngetTypeName() { return 'HTML'; }\n}\nclass SafeStyleImpl extends SafeValueImpl implements SafeStyle {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Style'; }\n}\nclass SafeScriptImpl extends SafeValueImpl implements SafeScript {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Script'; }\n}\nclass SafeUrlImpl extends SafeValueImpl implements SafeUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'URL'; }\n}\nclass SafeResourceUrlImpl extends SafeValueImpl implements SafeResourceUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'ResourceURL'; }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {CommonModule, PlatformLocation, ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {APP_ID, ApplicationModule, ErrorHandler, ModuleWithProviders, NgModule, Optional, PLATFORM_ID, PLATFORM_INITIALIZER, PlatformRef, Provider, RendererFactory2, RootRenderer, <PERSON>itizer, SkipSelf, Testability, createPlatformFactory, platformCore} from '@angular/core';\n\nimport {BrowserDomAdapter} from './browser/browser_adapter';\nimport {BrowserPlatformLocation} from './browser/location/browser_platform_location';\nimport {Meta} from './browser/meta';\nimport {SERVER_TRANSITION_PROVIDERS, TRANSITION_ID} from './browser/server-transition';\nimport {BrowserGetTestability} from './browser/testability';\nimport {Title} from './browser/title';\nimport {ELEMENT_PROBE_PROVIDERS} from './dom/debug/ng_probe';\nimport {getDOM} from './dom/dom_adapter';\nimport {DomRendererFactory2} from './dom/dom_renderer';\nimport {DOCUMENT} from './dom/dom_tokens';\nimport {DomEventsPlugin} from './dom/events/dom_events';\nimport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nimport {HAMMER_GESTURE_CONFIG, HammerGestureConfig, HammerGesturesPlugin} from './dom/events/hammer_gestures';\nimport {KeyEventsPlugin} from './dom/events/key_events';\nimport {DomSharedStylesHost, SharedStylesHost} from './dom/shared_styles_host';\nimport {DomSanitizer, DomSanitizerImpl} from './security/dom_sanitization_service';\n\nexport const /** @type {?} */ INTERNAL_BROWSER_PLATFORM_PROVIDERS: Provider[] = [\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n  {provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true},\n  {provide: PlatformLocation, useClass: BrowserPlatformLocation},\n  {provide: DOCUMENT, useFactory: _document, deps: []},\n];\n/**\n * \\@security Replacing built-in sanitization providers exposes the application to XSS risks.\n * Attacker-controlled data introduced by an unsanitized provider could expose your\n * application to XSS risks. For more detail, see the [Security Guide](http://g.co/ng/security).\n * \\@experimental\n */\nexport const BROWSER_SANITIZATION_PROVIDERS: Array<any> = [\n  {provide: Sanitizer, useExisting: DomSanitizer},\n  {provide: DomSanitizer, useClass: DomSanitizerImpl},\n];\n/**\n * \\@stable\n */\nexport const platformBrowser: (extraProviders?: Provider[]) => PlatformRef =\n    createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * @return {?}\n */\nexport function initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserGetTestability.init();\n}\n/**\n * @return {?}\n */\nexport function errorHandler(): ErrorHandler {\n  return new ErrorHandler();\n}\n/**\n * @return {?}\n */\nexport function _document(): any {\n  return document;\n}\n/**\n * The ng module for the browser.\n * \n * \\@stable\n */\nexport class BrowserModule {\n/**\n * @param {?} parentModule\n */\nconstructor(  parentModule: BrowserModule) {\n    if (parentModule) {\n      throw new Error(\n          `BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n    }\n  }\n/**\n * Configures a browser-based application to transition from a server-rendered app, if\n * one is present on the page. The specified parameters must include an application id,\n * which must match between the client and server applications.\n * \n * \\@experimental\n * @param {?} params\n * @return {?}\n */\nstatic withServerTransition(params: {appId: string}): ModuleWithProviders {\n    return {\n      ngModule: BrowserModule,\n      providers: [\n        {provide: APP_ID, useValue: params.appId},\n        {provide: TRANSITION_ID, useExisting: APP_ID},\n        SERVER_TRANSITION_PROVIDERS,\n      ],\n    };\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    BROWSER_SANITIZATION_PROVIDERS,\n    {provide: ErrorHandler, useFactory: errorHandler, deps: []},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: DomEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: HammerGesturesPlugin, multi: true},\n    {provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig},\n    DomRendererFactory2,\n    {provide: RendererFactory2, useExisting: DomRendererFactory2},\n    {provide: SharedStylesHost, useExisting: DomSharedStylesHost},\n    DomSharedStylesHost,\n    Testability,\n    EventManager,\n    ELEMENT_PROBE_PROVIDERS,\n    Meta,\n    Title,\n  ],\n  exports: [CommonModule, ApplicationModule]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserModule, decorators: [{ type: Optional }, { type: SkipSelf }, ]},\n];\n}\n\nfunction BrowserModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst /** @type {?} */ MODIFIER_KEY_GETTERS: {[key: string]: (event: KeyboardEvent) => boolean} = {\n  'alt': (event: KeyboardEvent) => event.altKey,\n  'control': (event: KeyboardEvent) => event.ctrlKey,\n  'meta': (event: KeyboardEvent) => event.metaKey,\n  'shift': (event: KeyboardEvent) => event.shiftKey\n};\n/**\n * \\@experimental\n */\nexport class KeyEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return KeyEventsPlugin.parseEventName(eventName) != null; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ parsedEvent = /** @type {?} */(( KeyEventsPlugin.parseEventName(eventName)));\n\n    const /** @type {?} */ outsideHandler =\n        KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n\n    return this.manager.getZone().runOutsideAngular(() => {\n      return getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nstatic parseEventName(eventName: string): {[key: string]: string}|null {\n    const /** @type {?} */ parts: string[] = eventName.toLowerCase().split('.');\n\n    const /** @type {?} */ domEventName = parts.shift();\n    if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const /** @type {?} */ key = KeyEventsPlugin._normalizeKey( /** @type {?} */((parts.pop())));\n\n    let /** @type {?} */ fullKey = '';\n    MODIFIER_KEYS.forEach(modifierName => {\n      const /** @type {?} */ index: number = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n\n    const /** @type {?} */ result: {[k: string]: string} = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n/**\n * @param {?} event\n * @return {?}\n */\nstatic getEventFullKey(event: KeyboardEvent): string {\n    let /** @type {?} */ fullKey = '';\n    let /** @type {?} */ key = getDOM().getEventKey(event);\n    key = key.toLowerCase();\n    if (key === ' ') {\n      key = 'space';  // for readability\n    } else if (key === '.') {\n      key = 'dot';  // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName != key) {\n        const /** @type {?} */ modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          fullKey += modifierName + '.';\n        }\n      }\n    });\n    fullKey += key;\n    return fullKey;\n  }\n/**\n * @param {?} fullKey\n * @param {?} handler\n * @param {?} zone\n * @return {?}\n */\nstatic eventCallback(fullKey: any, handler: Function, zone: NgZone): Function {\n    return (event: any /** TODO #9100 */) => {\n      if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n/**\n * \\@internal\n * @param {?} keyName\n * @return {?}\n */\nstatic _normalizeKey(keyName: string): string {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction KeyEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nKeyEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n};\n/**\n * A DI token that you can use to provide{\\@link HammerGestureConfig} to Angular. Use it to configure\n * Hammer gestures.\n * \n * \\@experimental\n */\nexport const HAMMER_GESTURE_CONFIG = new InjectionToken<HammerGestureConfig>('HammerGestureConfig');\n\nexport interface HammerInstance {\n  on(eventName: string, callback?: Function): void;\n  off(eventName: string, callback?: Function): void;\n}\n/**\n * \\@experimental\n */\nexport class HammerGestureConfig {\n  events: string[] = [];\n\n  overrides: {[key: string]: Object} = {};\n/**\n * @param {?} element\n * @return {?}\n */\nbuildHammer(element: HTMLElement): HammerInstance {\n    const /** @type {?} */ mc = new Hammer(element);\n\n    mc.get('pinch').set({enable: true});\n    mc.get('rotate').set({enable: true});\n\n    for (const /** @type {?} */ eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HammerGestureConfig_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGestureConfig.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGestureConfig.ctorParameters;\n/** @type {?} */\nHammerGestureConfig.prototype.events;\n/** @type {?} */\nHammerGestureConfig.prototype.overrides;\n}\n\nexport class HammerGesturesPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n * @param {?} _config\n */\nconstructor(\n       doc: any,\nprivate _config: HammerGestureConfig) {\n    super(doc);\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!( /** @type {?} */((window as any))).Hammer) {\n      throw new Error(`Hammer.js is not loaded, can not bind ${eventName} event`);\n    }\n\n    return true;\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const /** @type {?} */ mc = this._config.buildHammer(element);\n      const /** @type {?} */ callback = function(eventObj: HammerInput) {\n        zone.runGuarded(function() { handler(eventObj); });\n      };\n      mc.on(eventName, callback);\n      return () => mc.off(eventName, callback);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nisCustomEvent(eventName: string): boolean { return this._config.events.indexOf(eventName) > -1; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n{type: HammerGestureConfig, decorators: [{ type: Inject, args: [HAMMER_GESTURE_CONFIG, ] }, ]},\n];\n}\n\nfunction HammerGesturesPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGesturesPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGesturesPlugin.ctorParameters;\n/** @type {?} */\nHammerGesturesPlugin.prototype._config;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {EventManager} from './events/event_manager';\nimport {DomSharedStylesHost} from './shared_styles_host';\n\nexport const /** @type {?} */ NAMESPACE_URIS: {[ns: string]: string} = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n};\n\nconst /** @type {?} */ COMPONENT_REGEX = /%COMP%/g;\nexport const /** @type {?} */ COMPONENT_VARIABLE = '%COMP%';\nexport const /** @type {?} */ HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const /** @type {?} */ CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimContentAttribute(componentShortId: string): string {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimHostAttribute(componentShortId: string): string {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} compId\n * @param {?} styles\n * @param {?} target\n * @return {?}\n */\nexport function flattenStyles(\n    compId: string, styles: Array<any|any[]>, target: string[]): string[] {\n  for (let /** @type {?} */ i = 0; i < styles.length; i++) {\n    let /** @type {?} */ style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n  return target;\n}\n/**\n * @param {?} eventHandler\n * @return {?}\n */\nfunction decoratePreventDefault(eventHandler: Function): Function {\n  return (event: any) => {\n    const /** @type {?} */ allowDefaultBehavior = eventHandler(event);\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n  };\n}\nexport class DomRendererFactory2 implements RendererFactory2 {\nprivate rendererByCompId = new Map<string, Renderer2>();\nprivate defaultRenderer: Renderer2;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n */\nconstructor(private eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost) {\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  };\n/**\n * @param {?} element\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(element: any, type: RendererType2|null): Renderer2 {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated: {\n        let /** @type {?} */ renderer = this.rendererByCompId.get(type.id);\n        if (!renderer) {\n          renderer =\n              new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type);\n          this.rendererByCompId.set(type.id, renderer);\n        }\n        ( /** @type {?} */((<EmulatedEncapsulationDomRenderer2>renderer))).applyToHost(element);\n        return renderer;\n      }\n      case ViewEncapsulation.Native:\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n      default: {\n        if (!this.rendererByCompId.has(type.id)) {\n          const /** @type {?} */ styles = flattenStyles(type.id, type.styles, []);\n          this.sharedStylesHost.addStyles(styles);\n          this.rendererByCompId.set(type.id, this.defaultRenderer);\n        }\n        return this.defaultRenderer;\n      }\n    }\n  }\n/**\n * @return {?}\n */\nbegin() {}\n/**\n * @return {?}\n */\nend() {}\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: EventManager, },\n{type: DomSharedStylesHost, },\n];\n}\n\nfunction DomRendererFactory2_tsickle_Closure_declarations() {\n/** @type {?} */\nDomRendererFactory2.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomRendererFactory2.ctorParameters;\n/** @type {?} */\nDomRendererFactory2.prototype.rendererByCompId;\n/** @type {?} */\nDomRendererFactory2.prototype.defaultRenderer;\n/** @type {?} */\nDomRendererFactory2.prototype.eventManager;\n/** @type {?} */\nDomRendererFactory2.prototype.sharedStylesHost;\n}\n\nclass DefaultDomRenderer2 implements Renderer2 {\n  data: {[key: string]: any} = Object.create(null);\n/**\n * @param {?} eventManager\n */\nconstructor(private eventManager: EventManager) {}\n/**\n * @return {?}\n */\ndestroy(): void {}\n\n  destroyNode: null;\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string): any {\n    if (namespace) {\n      return document.createElementNS(NAMESPACE_URIS[namespace], name);\n    }\n\n    return document.createElement(name);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string): any { return document.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string): any { return document.createTextNode(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void { parent.appendChild(newChild); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    if (parent) {\n      parent.insertBefore(newChild, refChild);\n    }\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: string|any): any {\n    let /** @type {?} */ el: any = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n                                                       selectorOrNode;\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    el.textContent = '';\n    return el;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any { return node.parentNode; }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any): any { return node.nextSibling; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string): void {\n    if (namespace) {\n      name = `${namespace}:${name}`;\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string): void {\n    if (namespace) {\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { el.classList.add(name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { el.classList.remove(name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.setProperty(\n          style, value, !!(flags & RendererStyleFlags2.Important) ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { node.nodeValue = value; }\n/**\n * @param {?} target\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, event: string, callback: (event: any) => boolean):\n      () => void {\n    checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      return /** @type {?} */(( <() => void>this.eventManager.addGlobalEventListener(\n          target, event, decoratePreventDefault(callback))));\n    }\n    return /** @type {?} */(( /** @type {?} */(( <() => void>this.eventManager.addEventListener(\n               target, event, decoratePreventDefault(callback)))) as() => void));\n  }\n}\n\nfunction DefaultDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nDefaultDomRenderer2.prototype.data;\n/** @type {?} */\nDefaultDomRenderer2.prototype.destroyNode;\n/** @type {?} */\nDefaultDomRenderer2.prototype.eventManager;\n}\n\n\nconst /** @type {?} */ AT_CHARCODE = '@'.charCodeAt(0);\n/**\n * @param {?} name\n * @param {?} nameKind\n * @return {?}\n */\nfunction checkNoSyntheticProp(name: string, nameKind: string) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(\n        `Found the synthetic ${nameKind} ${name}. Please include either \"BrowserAnimationsModule\" or \"NoopAnimationsModule\" in your application.`);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\nprivate contentAttr: string;\nprivate hostAttr: string;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager, sharedStylesHost: DomSharedStylesHost,\nprivate component: RendererType2) {\n    super(eventManager);\n    const styles = flattenStyles(component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n\n    this.contentAttr = shimContentAttribute(component.id);\n    this.hostAttr = shimHostAttribute(component.id);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\napplyToHost(element: any) { super.setAttribute(element, this.hostAttr, ''); }\n/**\n * @param {?} parent\n * @param {?} name\n * @return {?}\n */\ncreateElement(parent: any, name: string): Element {\n    const /** @type {?} */ el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\n\nfunction EmulatedEncapsulationDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.contentAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.hostAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.component;\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\nprivate shadowRoot: any;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} hostEl\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost,\nprivate hostEl: any,\nprivate component: RendererType2) {\n    super(eventManager);\n    this.shadowRoot = (hostEl as any).createShadowRoot();\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nprivate nodeOrShadowRoot(node: any): any { return node === this.hostEl ? this.shadowRoot : node; }\n/**\n * @return {?}\n */\ndestroy() { this.sharedStylesHost.removeHost(this.shadowRoot); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n}\n\nfunction ShadowDomRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nShadowDomRenderer.prototype.shadowRoot;\n/** @type {?} */\nShadowDomRenderer.prototype.sharedStylesHost;\n/** @type {?} */\nShadowDomRenderer.prototype.hostEl;\n/** @type {?} */\nShadowDomRenderer.prototype.component;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\nexport class DomEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return true; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    element.addEventListener(eventName, /** @type {?} */(( handler as any)), false);\n    return () => element.removeEventListener(eventName, /** @type {?} */(( handler as any)), false);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nDomEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {GetTestability, Testability, TestabilityRegistry, setTestabilityGetter, ɵglobal as global} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nexport class BrowserGetTestability implements GetTestability {\n/**\n * @return {?}\n */\nstatic init() { setTestabilityGetter(new BrowserGetTestability()); }\n/**\n * @param {?} registry\n * @return {?}\n */\naddToWindow(registry: TestabilityRegistry): void {\n    global['getAngularTestability'] = (elem: any, findInAncestors: boolean = true) => {\n      const /** @type {?} */ testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n      return testability;\n    };\n\n    global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const /** @type {?} */ whenAllStable = (callback: any /** TODO #9100 */) => {\n      const /** @type {?} */ testabilities = global['getAllAngularTestabilities']();\n      let /** @type {?} */ count = testabilities.length;\n      let /** @type {?} */ didWork = false;\n      const /** @type {?} */ decrement = function(didWork_: any /** TODO #9100 */) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(function(testability: any /** TODO #9100 */) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!global['frameworkStabilizers']) {\n      global['frameworkStabilizers'] = [];\n    }\n    global['frameworkStabilizers'].push(whenAllStable);\n  }\n/**\n * @param {?} registry\n * @param {?} elem\n * @param {?} findInAncestors\n * @return {?}\n */\nfindTestabilityInTree(registry: TestabilityRegistry, elem: any, findInAncestors: boolean):\n      Testability|null {\n    if (elem == null) {\n      return null;\n    }\n    const /** @type {?} */ t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, getDOM().getHost(elem), true);\n    }\n    return this.findTestabilityInTree(registry, getDOM().parentElement(elem), true);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * A service that can be used to get and set the title of a current HTML document.\n * \n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n * \n * \\@experimental\n */\nexport class Title {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {}\n/**\n * Get the title of the current HTML document.\n * @return {?}\n */\ngetTitle(): string { return getDOM().getTitle(this._doc); }\n/**\n * Set the title of the current HTML document.\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(newTitle: string) { getDOM().setTitle(this._doc, newTitle); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Title_tsickle_Closure_declarations() {\n/** @type {?} */\nTitle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitle.ctorParameters;\n/** @type {?} */\nTitle.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport * as core from '@angular/core';\nimport {exportNgVar} from '../util';\n\nconst /** @type {?} */ CORE_TOKENS = {\n  'ApplicationRef': core.ApplicationRef,\n  'NgZone': core.NgZone,\n};\n\nconst /** @type {?} */ INSPECT_GLOBAL_NAME = 'probe';\nconst /** @type {?} */ CORE_TOKENS_GLOBAL_NAME = 'coreTokens';\n/**\n * Returns a {\\@link DebugElement} for the given native DOM element, or\n * null if the given native element does not have an Angular view associated\n * with it.\n * @param {?} element\n * @return {?}\n */\nexport function inspectNativeElement(element: any): core.DebugNode|null {\n  return core.getDebugNode(element);\n}\n/**\n * Deprecated. Use the one from '\\@angular/core'.\n * @deprecated\n */\nexport class NgProbeToken {\n/**\n * @param {?} name\n * @param {?} token\n */\nconstructor(public name: string,\npublic token: any) {}\n}\n\nfunction NgProbeToken_tsickle_Closure_declarations() {\n/** @type {?} */\nNgProbeToken.prototype.name;\n/** @type {?} */\nNgProbeToken.prototype.token;\n}\n\n/**\n * @param {?} extraTokens\n * @param {?} coreTokens\n * @return {?}\n */\nexport function _createNgProbe(extraTokens: NgProbeToken[], coreTokens: core.NgProbeToken[]): any {\n  const /** @type {?} */ tokens = (extraTokens || []).concat(coreTokens || []);\n  exportNgVar(INSPECT_GLOBAL_NAME, inspectNativeElement);\n  exportNgVar(CORE_TOKENS_GLOBAL_NAME, {...CORE_TOKENS, ..._ngProbeTokensToMap(tokens || [])});\n  return () => inspectNativeElement;\n}\n/**\n * @param {?} tokens\n * @return {?}\n */\nfunction _ngProbeTokensToMap(tokens: NgProbeToken[]): {[name: string]: any} {\n  return tokens.reduce((prev: any, t: any) => (prev[t.name] = t.token, prev), {});\n}\n/**\n * Providers which support debugging Angular applications (e.g. via `ng.probe`).\n */\nexport const ELEMENT_PROBE_PROVIDERS: core.Provider[] = [\n  {\n    provide: core.APP_INITIALIZER,\n    useFactory: _createNgProbe,\n    deps: [\n      [NgProbeToken, new core.Optional()],\n      [core.NgProbeToken, new core.Optional()],\n    ],\n    multi: true,\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\n/**\n * \\@stable\n */\nexport const EVENT_MANAGER_PLUGINS =\n    new InjectionToken<EventManagerPlugin[]>('EventManagerPlugins');\n/**\n * \\@stable\n */\nexport class EventManager {\nprivate _plugins: EventManagerPlugin[];\nprivate _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n/**\n * @param {?} plugins\n * @param {?} _zone\n */\nconstructor( plugins: EventManagerPlugin[],\nprivate _zone: NgZone) {\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(target: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n/**\n * @return {?}\n */\ngetZone(): NgZone { return this._zone; }\n/**\n * \\@internal\n * @param {?} eventName\n * @return {?}\n */\n_findPluginFor(eventName: string): EventManagerPlugin {\n    const /** @type {?} */ plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const /** @type {?} */ plugins = this._plugins;\n    for (let /** @type {?} */ i = 0; i < plugins.length; i++) {\n      const /** @type {?} */ plugin = plugins[i];\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n      }\n    }\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Inject, args: [EVENT_MANAGER_PLUGINS, ] }, ]},\n{type: NgZone, },\n];\n}\n\nfunction EventManager_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManager.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nEventManager.ctorParameters;\n/** @type {?} */\nEventManager.prototype._plugins;\n/** @type {?} */\nEventManager.prototype._eventNameToPlugin;\n/** @type {?} */\nEventManager.prototype._zone;\n}\n\n/**\n * @abstract\n */\nexport abstract class EventManagerPlugin {\n/**\n * @param {?} _doc\n */\nconstructor(private _doc: any) {}\n\n  manager: EventManager;\n/**\n * @abstract\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function) {}\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(element: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ target: HTMLElement = getDOM().getGlobalEventTarget(this._doc, element);\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n    return this.addEventListener(target, eventName, handler);\n  };\n}\n\nfunction EventManagerPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManagerPlugin.prototype.manager;\n/** @type {?} */\nEventManagerPlugin.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, OnDestroy} from '@angular/core';\nimport {getDOM} from './dom_adapter';\nimport {DOCUMENT} from './dom_tokens';\nexport class SharedStylesHost {\n/**\n * \\@internal\n */\nprotected _stylesSet = new Set<string>();\n/**\n * @param {?} styles\n * @return {?}\n */\naddStyles(styles: string[]): void {\n    const /** @type {?} */ additions = new Set<string>();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {}\n/**\n * @return {?}\n */\ngetAllStyles(): string[] { return Array.from(this._stylesSet); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSharedStylesHost.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nSharedStylesHost.prototype._stylesSet;\n}\n\nexport class DomSharedStylesHost extends SharedStylesHost implements OnDestroy {\nprivate _hostNodes = new Set<Node>();\nprivate _styleNodes = new Set<Node>();\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._hostNodes.add(_doc.head);\n  }\n/**\n * @param {?} styles\n * @param {?} host\n * @return {?}\n */\nprivate _addStylesToHost(styles: Set<string>, host: Node): void {\n    styles.forEach((style: string) => {\n      const /** @type {?} */ styleEl = this._doc.createElement('style');\n      styleEl.textContent = style;\n      this._styleNodes.add(host.appendChild(styleEl));\n    });\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\naddHost(hostNode: Node): void {\n    this._addStylesToHost(this._stylesSet, hostNode);\n    this._hostNodes.add(hostNode);\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\nremoveHost(hostNode: Node): void { this._hostNodes.delete(hostNode); }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {\n    this._hostNodes.forEach(hostNode => this._addStylesToHost(additions, hostNode));\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this._styleNodes.forEach(styleNode => getDOM().remove(styleNode)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSharedStylesHost.ctorParameters;\n/** @type {?} */\nDomSharedStylesHost.prototype._hostNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._styleNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n\n\n/**\n * Represents a meta element.\n *\n * @experimental\n */\nexport type MetaDefinition = {\n  charset?: string; content?: string; httpEquiv?: string; id?: string; itemprop?: string;\n  name?: string;\n  property?: string;\n  scheme?: string;\n  url?: string;\n} &\n{\n  // TODO(IgorMinar): this type looks wrong\n  [prop: string]: string;\n};\n/**\n * A service that can be used to get and add meta tags.\n * \n * \\@experimental\n */\nexport class Meta {\nprivate _dom: DomAdapter;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { this._dom = getDOM(); }\n/**\n * @param {?} tag\n * @param {?=} forceCreation\n * @return {?}\n */\naddTag(tag: MetaDefinition, forceCreation: boolean = false): HTMLMetaElement|null {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n/**\n * @param {?} tags\n * @param {?=} forceCreation\n * @return {?}\n */\naddTags(tags: MetaDefinition[], forceCreation: boolean = false): HTMLMetaElement[] {\n    if (!tags) return [];\n    return tags.reduce((result: HTMLMetaElement[], tag: MetaDefinition) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTag(attrSelector: string): HTMLMetaElement|null {\n    if (!attrSelector) return null;\n    return this._dom.querySelector(this._doc, `meta[${attrSelector}]`);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTags(attrSelector: string): HTMLMetaElement[] {\n    if (!attrSelector) return [];\n    const /** @type {?} */ list /*NodeList*/ = this._dom.querySelectorAll(this._doc, `meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n/**\n * @param {?} tag\n * @param {?=} selector\n * @return {?}\n */\nupdateTag(tag: MetaDefinition, selector?: string): HTMLMetaElement|null {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const /** @type {?} */ meta: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\nremoveTag(attrSelector: string): void { this.removeTagElement( /** @type {?} */((this.getTag(attrSelector)))); }\n/**\n * @param {?} meta\n * @return {?}\n */\nremoveTagElement(meta: HTMLMetaElement): void {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n/**\n * @param {?} meta\n * @param {?=} forceCreation\n * @return {?}\n */\nprivate _getOrCreateElement(meta: MetaDefinition, forceCreation: boolean = false):\n      HTMLMetaElement {\n    if (!forceCreation) {\n      const /** @type {?} */ selector: string = this._parseSelector(meta);\n      const /** @type {?} */ elem: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      if (elem && this._containsAttributes(meta, elem)) return elem;\n    }\n    const /** @type {?} */ element: HTMLMetaElement = /** @type {?} */(( this._dom.createElement('meta') as HTMLMetaElement));\n    this._setMetaElementAttributes(meta, element);\n    const /** @type {?} */ head = this._dom.getElementsByTagName(this._doc, 'head')[0];\n    this._dom.appendChild(head, element);\n    return element;\n  }\n/**\n * @param {?} tag\n * @param {?} el\n * @return {?}\n */\nprivate _setMetaElementAttributes(tag: MetaDefinition, el: HTMLMetaElement): HTMLMetaElement {\n    Object.keys(tag).forEach((prop: string) => this._dom.setAttribute(el, prop, tag[prop]));\n    return el;\n  }\n/**\n * @param {?} tag\n * @return {?}\n */\nprivate _parseSelector(tag: MetaDefinition): string {\n    const /** @type {?} */ attr: string = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n/**\n * @param {?} tag\n * @param {?} elem\n * @return {?}\n */\nprivate _containsAttributes(tag: MetaDefinition, elem: HTMLMetaElement): boolean {\n    return Object.keys(tag).every((key: string) => this._dom.getAttribute(elem, key) === tag[key]);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Meta_tsickle_Closure_declarations() {\n/** @type {?} */\nMeta.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMeta.ctorParameters;\n/** @type {?} */\nMeta.prototype._dom;\n/** @type {?} */\nMeta.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeUrl} from './url_sanitizer';\n/**\n * Regular expression for safe style values.\n * \n * Quotes (\" and ') are allowed, but a check must be done elsewhere to ensure they're balanced.\n * \n * ',' allows multiple values to be assigned to the same property (e.g. background-attachment or\n * font-family) and hence could allow multiple values to get injected, but that should pose no risk\n * of XSS.\n * \n * The function expression checks only for XSS safety, not for CSS validity.\n * \n * This regular expression was taken from the Closure sanitization library, and augmented for\n * transformation values.\n */\nconst VALUES = '[-,.\"\\'%_!# a-zA-Z0-9]+';\nconst /** @type {?} */ TRANSFORMATION_FNS = '(?:matrix|translate|scale|rotate|skew|perspective)(?:X|Y|3d)?';\nconst /** @type {?} */ COLOR_FNS = '(?:rgb|hsl)a?';\nconst /** @type {?} */ GRADIENTS = '(?:repeating-)?(?:linear|radial)-gradient';\nconst /** @type {?} */ CSS3_FNS = '(?:calc|attr)';\nconst /** @type {?} */ FN_ARGS = '\\\\([-0-9.%, #a-zA-Z]+\\\\)';\nconst /** @type {?} */ SAFE_STYLE_VALUE = new RegExp(\n    `^(${VALUES}|` +\n        `(?:${TRANSFORMATION_FNS}|${COLOR_FNS}|${GRADIENTS}|${CSS3_FNS})` +\n        `${FN_ARGS})$`,\n    'g');\n/**\n * Matches a `url(...)` value with an arbitrary argument as long as it does\n * not contain parentheses.\n * \n * The URL value still needs to be sanitized separately.\n * \n * `url(...)` values are a very common use case, e.g. for `background-image`. With carefully crafted\n * CSS style rules, it is possible to construct an information leak with `url` values in CSS, e.g.\n * by observing whether scroll bars are displayed, or character ranges used by a font face\n * definition.\n * \n * Angular only allows binding CSS values (as opposed to entire CSS rules), so it is unlikely that\n * binding a URL value without further cooperation from the page will cause an information leak, and\n * if so, it is just a leak, not a full blown XSS vulnerability.\n * \n * Given the common use case, low likelihood of attack vector, and low impact of an attack, this\n * code is permissive and allows URLs that sanitize otherwise.\n */\nconst URL_RE = /^url\\(([^)]+)\\)$/;\n/**\n * Checks that quotes (\" and ') are properly balanced inside a string. Assumes\n * that neither escape (\\) nor any other character that could result in\n * breaking out of a string parsing context are allowed;\n * see http://www.w3.org/TR/css3-syntax/#string-token-diagram.\n * \n * This code was taken from the Closure sanitization library.\n * @param {?} value\n * @return {?}\n */\nfunction hasBalancedQuotes(value: string) {\n  let /** @type {?} */ outsideSingle = true;\n  let /** @type {?} */ outsideDouble = true;\n  for (let /** @type {?} */ i = 0; i < value.length; i++) {\n    const /** @type {?} */ c = value.charAt(i);\n    if (c === '\\'' && outsideDouble) {\n      outsideSingle = !outsideSingle;\n    } else if (c === '\"' && outsideSingle) {\n      outsideDouble = !outsideDouble;\n    }\n  }\n  return outsideSingle && outsideDouble;\n}\n/**\n * Sanitizes the given untrusted CSS style property value (i.e. not an entire object, just a single\n * value) and returns a value that is safe to use in a browser environment.\n * @param {?} value\n * @return {?}\n */\nexport function sanitizeStyle(value: string): string {\n  value = String(value).trim();  // Make sure it's actually a string.\n  if (!value) return '';\n\n  // Single url(...) values are supported, but only for URLs that sanitize cleanly. See above for\n  // reasoning behind this.\n  const /** @type {?} */ urlMatch = value.match(URL_RE);\n  if ((urlMatch && sanitizeUrl(urlMatch[1]) === urlMatch[1]) ||\n      value.match(SAFE_STYLE_VALUE) && hasBalancedQuotes(value)) {\n    return value;  // Safe style values.\n  }\n\n  if (isDevMode()) {\n    getDOM().log(\n        `WARNING: sanitizing unsafe style value ${value} (see http://g.co/ng/security#xss).`);\n  }\n\n  return 'unsafe';\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\n\nlet /** @type {?} */ _DOM: DomAdapter = /** @type {?} */(( null));\n/**\n * @return {?}\n */\nexport function getDOM() {\n  return _DOM;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setDOM(adapter: DomAdapter) {\n  _DOM = adapter;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setRootDomAdapter(adapter: DomAdapter) {\n  if (!_DOM) {\n    _DOM = adapter;\n  }\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class DomAdapter {\npublic resourceLoaderType: Type<any> = /** @type {?} */(( null));\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Element, name: string, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Element, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Element, methodName: string, args: any[]) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogError(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlog(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: any) {}\n/**\n * @abstract\n * @return {?}\n */\nlogGroupEnd() {}\n/**\n * Maps attribute names to their corresponding property names for cases\n * where attribute name doesn't match property name.\n * @return {?}\n */\nget attrToPropMap(): {[key: string]: string} { return this._attrToPropMap; };\n/**\n * @param {?} value\n * @return {?}\n */\nset attrToPropMap(value: {[key: string]: string}) { this._attrToPropMap = value; };\n/**\n * \\@internal\n */\n_attrToPropMap: {[key: string]: string};\n/**\n * @abstract\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any) {}\n/**\n * @abstract\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: any, evt: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: string) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: any) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: any) {}\n/**\n * Returns content if el is a <template> element, null otherwise.\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeName(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ntype(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ncontent(node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nparentElement(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} newNode\n * @param {?} oldNode\n * @return {?}\n */\nreplaceChild(el: any, newNode: any, oldNode: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nremove(el: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: any, ref: any, node: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: any, ref: any, nodes: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: any, el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: any, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetText(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) {}\n/**\n * @abstract\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string) {}\n/**\n * @abstract\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any) {}\n/**\n * @abstract\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: any, doc?: any) {}\n/**\n * @abstract\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetHost(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nclone /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nclassList(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\nremoveStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\ngetStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ntagName(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\ngetAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: any, ns: string, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nremoveAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: any) {}\n/**\n * @abstract\n * @return {?}\n */\ncreateHtmlDocument() {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: any) {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document) {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) {}\n/**\n * @abstract\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nadoptNode /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetHref(element: any) {}\n/**\n * @abstract\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(element: any, baseUrl: string, href: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsDOMEvents() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsNativeShadowDOM() {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string) {}\n/**\n * @abstract\n * @return {?}\n */\ngetHistory() {}\n/**\n * @abstract\n * @return {?}\n */\ngetLocation() {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document) {}\n/**\n * @abstract\n * @return {?}\n */\nresetBaseElement() {}\n/**\n * @abstract\n * @return {?}\n */\ngetUserAgent() {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: any, name: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsWebAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nperformanceNow() {}\n/**\n * @abstract\n * @return {?}\n */\ngetAnimationPrefix() {}\n/**\n * @abstract\n * @return {?}\n */\ngetTransitionEnd() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsCookies() {}\n/**\n * @abstract\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string) {}\n/**\n * @abstract\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {}\n}\n\nfunction DomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nDomAdapter.prototype.resourceLoaderType;\n/**\n * \\@internal\n * @type {?}\n */\nDomAdapter.prototype._attrToPropMap;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {DomAdapter} from '../dom/dom_adapter';\n/**\n * Provides DOM operations in any browser environment.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class GenericBrowserDomAdapter extends DomAdapter {\nprivate _animationPrefix: string|null = null;\nprivate _transitionEnd: string|null = null;\nconstructor() {\n    super();\n    try {\n      const element = this.createElement('div', document);\n      if (this.getStyle(element, 'animationName') != null) {\n        this._animationPrefix = '';\n      } else {\n        const domPrefixes = ['Webkit', 'Moz', 'O', 'ms'];\n\n        for (let i = 0; i < domPrefixes.length; i++) {\n          if (this.getStyle(element, domPrefixes[i] + 'AnimationName') != null) {\n            this._animationPrefix = '-' + domPrefixes[i].toLowerCase() + '-';\n            break;\n          }\n        }\n      }\n\n      const transEndEventNames: {[key: string]: string} = {\n        WebkitTransition: 'webkitTransitionEnd',\n        MozTransition: 'transitionend',\n        OTransition: 'oTransitionEnd otransitionend',\n        transition: 'transitionend'\n      };\n\n      Object.keys(transEndEventNames).forEach((key: string) => {\n        if (this.getStyle(element, key) != null) {\n          this._transitionEnd = transEndEventNames[key];\n        }\n      });\n    } catch (e) {\n      this._animationPrefix = null;\n      this._transitionEnd = null;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: HTMLElement): Node[] { return ( /** @type {?} */((<any>el))).getDistributedNodes(); }\n/**\n * @param {?} el\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(el: HTMLAnchorElement, baseUrl: string, href: string) {\n    el.href = href == null ? baseUrl : baseUrl + '/../' + href;\n  }\n/**\n * @return {?}\n */\nsupportsDOMEvents(): boolean { return true; }\n/**\n * @return {?}\n */\nsupportsNativeShadowDOM(): boolean {\n    return typeof( /** @type {?} */((<any>document.body))).createShadowRoot === 'function';\n  }\n/**\n * @return {?}\n */\ngetAnimationPrefix(): string { return this._animationPrefix ? this._animationPrefix : ''; }\n/**\n * @return {?}\n */\ngetTransitionEnd(): string { return this._transitionEnd ? this._transitionEnd : ''; }\n/**\n * @return {?}\n */\nsupportsAnimation(): boolean {\n    return this._animationPrefix != null && this._transitionEnd != null;\n  }\n}\n\nfunction GenericBrowserDomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._animationPrefix;\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._transitionEnd;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵglobal as global} from '@angular/core';\nimport {setRootDomAdapter} from '../dom/dom_adapter';\n\nimport {GenericBrowserDomAdapter} from './generic_browser_adapter';\n\nconst /** @type {?} */ _attrToPropMap = {\n  'class': 'className',\n  'innerHtml': 'innerHTML',\n  'readonly': 'readOnly',\n  'tabindex': 'tabIndex',\n};\n\nconst /** @type {?} */ DOM_KEY_LOCATION_NUMPAD = 3;\n\n// Map to convert some key or keyIdentifier values to what will be returned by getEventKey\nconst /** @type {?} */ _keyMap: {[k: string]: string} = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf http://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n\n// There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\nconst /** @type {?} */ _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n\nlet /** @type {?} */ nodeContains: (a: any, b: any) => boolean;\n\nif (global['Node']) {\n  nodeContains = global['Node'].prototype.contains || function(node) {\n    return !!(this.compareDocumentPosition(node) & 16);\n  };\n}\nexport class BrowserDomAdapter extends GenericBrowserDomAdapter {\n/**\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) { throw new Error('parse not implemented'); }\n/**\n * @return {?}\n */\nstatic makeCurrent() { setRootDomAdapter(new BrowserDomAdapter()); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: Node, name: string): boolean { return name in element; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Node, name: string, value: any) { ( /** @type {?} */((<any>el)))[name] = value; }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Node, name: string): any { return ( /** @type {?} */((<any>el)))[name]; }\n/**\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Node, methodName: string, args: any[]): any { ( /** @type {?} */((<any>el)))[methodName](...args); }\n/**\n * @param {?} error\n * @return {?}\n */\nlogError(error: string): void {\n    if (window.console) {\n      if (console.error) {\n        console.error(error);\n      } else {\n        console.log(error);\n      }\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlog(error: string): void {\n    if (window.console) {\n      window.console.log && window.console.log(error);\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: string): void {\n    if (window.console) {\n      window.console.group && window.console.group(error);\n    }\n  }\n/**\n * @return {?}\n */\nlogGroupEnd(): void {\n    if (window.console) {\n      window.console.groupEnd && window.console.groupEnd();\n    }\n  }\n/**\n * @return {?}\n */\nget attrToPropMap(): any { return _attrToPropMap; }\n/**\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any): boolean { return nodeContains.call(nodeA, nodeB); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: Element, selector: string): any { return el.querySelector(selector); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string): any[] { return el.querySelectorAll(selector); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: Node, evt: any, listener: any) { el.addEventListener(evt, listener, false); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: Node, evt: any, listener: any): Function {\n    el.addEventListener(evt, listener, false);\n    // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n    return () => { el.removeEventListener(evt, listener, false); };\n  }\n/**\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: Node, evt: any) { el.dispatchEvent(evt); }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: string): MouseEvent {\n    const /** @type {?} */ evt: MouseEvent = document.createEvent('MouseEvent');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: any): Event {\n    const /** @type {?} */ evt: Event = document.createEvent('Event');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: Event) {\n    evt.preventDefault();\n    evt.returnValue = false;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: Event): boolean {\n    return evt.defaultPrevented || evt.returnValue != null && !evt.returnValue;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: HTMLElement): string { return el.innerHTML; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: Node): Node|null {\n    return 'content' in el && el instanceof HTMLTemplateElement ? el.content : null;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: HTMLElement): string { return el.outerHTML; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeName(node: Node): string { return node.nodeName; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: Node): string|null { return node.nodeValue; }\n/**\n * @param {?} node\n * @return {?}\n */\ntype(node: HTMLInputElement): string { return node.type; }\n/**\n * @param {?} node\n * @return {?}\n */\ncontent(node: Node): Node {\n    if (this.hasProperty(node, 'content')) {\n      return ( /** @type {?} */((<any>node))).content;\n    } else {\n      return node;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: Node): Node|null { return el.firstChild; }\n/**\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: Node): Node|null { return el.nextSibling; }\n/**\n * @param {?} el\n * @return {?}\n */\nparentElement(el: Node): Node|null { return el.parentNode; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any): Node[] { return el.childNodes; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: Node): any[] {\n    const /** @type {?} */ childNodes = el.childNodes;\n    const /** @type {?} */ res = new Array(childNodes.length);\n    for (let /** @type {?} */ i = 0; i < childNodes.length; i++) {\n      res[i] = childNodes[i];\n    }\n    return res;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: Node) {\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: Node, node: Node) { el.appendChild(node); }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: Node, node: Node) { el.removeChild(node); }\n/**\n * @param {?} el\n * @param {?} newChild\n * @param {?} oldChild\n * @return {?}\n */\nreplaceChild(el: Node, newChild: Node, oldChild: Node) { el.replaceChild(newChild, oldChild); }\n/**\n * @param {?} node\n * @return {?}\n */\nremove(node: Node): Node {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n    return node;\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: Node, ref: Node, node: Node) { parent.insertBefore(node, ref); }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: Node, ref: Node, nodes: Node[]) {\n    nodes.forEach((n: any) => parent.insertBefore(n, ref));\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: Node, ref: Node, node: any) { parent.insertBefore(node, ref.nextSibling); }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: Element, value: string) { el.innerHTML = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetText(el: Node): string|null { return el.textContent; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: Node, value: string) { el.textContent = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any): string { return el.value; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) { el.value = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any): boolean { return el.checked; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) { el.checked = value; }\n/**\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string): Comment { return document.createComment(text); }\n/**\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any): HTMLElement {\n    const /** @type {?} */ t = document.createElement('template');\n    t.innerHTML = html;\n    return t;\n  }\n/**\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: string, doc = document): HTMLElement { return doc.createElement(tagName); }\n/**\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc = document): Element {\n    return doc.createElementNS(ns, tagName);\n  }\n/**\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc = document): Text { return doc.createTextNode(text); }\n/**\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc = document): HTMLScriptElement {\n    const /** @type {?} */ el = /** @type {?} */(( <HTMLScriptElement>doc.createElement('SCRIPT')));\n    el.setAttribute(attrName, attrValue);\n    return el;\n  }\n/**\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc = document): HTMLStyleElement {\n    const /** @type {?} */ style = /** @type {?} */(( <HTMLStyleElement>doc.createElement('style')));\n    this.appendChild(style, this.createTextNode(css));\n    return style;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).createShadowRoot(); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).shadowRoot; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHost(el: HTMLElement): HTMLElement { return ( /** @type {?} */((<any>el))).host; }\n/**\n * @param {?} node\n * @return {?}\n */\nclone(node: Node): Node { return node.cloneNode(true); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByClassName(name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByTagName(name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclassList(element: any): any[] { return Array.prototype.slice.call(element.classList, 0); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) { element.classList.add(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) { element.classList.remove(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string): boolean {\n    return element.classList.contains(className);\n  }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {\n    element.style[styleName] = styleValue;\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\nremoveStyle(element: any, stylename: string) {\n    // IE requires '' instead of null\n    // see https://github.com/angular/angular/issues/7916\n    element.style[stylename] = '';\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\ngetStyle(element: any, stylename: string): string { return element.style[stylename]; }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string|null): boolean {\n    const /** @type {?} */ value = this.getStyle(element, styleName) || '';\n    return styleValue ? value == styleValue : value.length > 0;\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ntagName(element: any): string { return element.tagName; }\n/**\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any): Map<string, string> {\n    const /** @type {?} */ res = new Map<string, string>();\n    const /** @type {?} */ elAttrs = element.attributes;\n    for (let /** @type {?} */ i = 0; i < elAttrs.length; i++) {\n      const /** @type {?} */ attrib = elAttrs[i];\n      res.set(attrib.name, attrib.value);\n    }\n    return res;\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: Element, attribute: string): boolean {\n    return element.hasAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: Element, ns: string, attribute: string): boolean {\n    return element.hasAttributeNS(ns, attribute);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: Element, attribute: string): string|null {\n    return element.getAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\ngetAttributeNS(element: Element, ns: string, name: string): string {\n    return element.getAttributeNS(ns, name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: Element, name: string, value: string) { element.setAttribute(name, value); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: Element, ns: string, name: string, value: string) {\n    element.setAttributeNS(ns, name, value);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: Element, attribute: string) { element.removeAttribute(attribute); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\nremoveAttributeNS(element: Element, ns: string, name: string) {\n    element.removeAttributeNS(ns, name);\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: Node): any { return this.isTemplateElement(el) ? this.content(el) : el; }\n/**\n * @return {?}\n */\ncreateHtmlDocument(): HTMLDocument {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: Element): any {\n    try {\n      return el.getBoundingClientRect();\n    } catch ( /** @type {?} */e) {\n      return {top: 0, bottom: 0, left: 0, right: 0, width: 0, height: 0};\n    }\n  }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document): string { return document.title; }\n/**\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) { document.title = newTitle || ''; }\n/**\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string): boolean {\n    if (n instanceof HTMLElement) {\n      return n.matches && n.matches(selector) ||\n          n.msMatchesSelector && n.msMatchesSelector(selector) ||\n          n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n\n    return false;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: Node): boolean {\n    return el instanceof HTMLElement && el.nodeName == 'TEMPLATE';\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: Node): boolean { return node.nodeType === Node.TEXT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: Node): boolean { return node.nodeType === Node.COMMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: Node): boolean { return node.nodeType === Node.ELEMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any): boolean {\n    return node.shadowRoot != null && node instanceof HTMLElement;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any): boolean { return node instanceof DocumentFragment; }\n/**\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc(node: Node): any { return document.importNode(this.templateAwareRoot(node), true); }\n/**\n * @param {?} node\n * @return {?}\n */\nadoptNode(node: Node): any { return document.adoptNode(node); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHref(el: Element): string { return ( /** @type {?} */((<any>el))).href; }\n/**\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any): string {\n    let /** @type {?} */ key = event.key;\n    if (key == null) {\n      key = event.keyIdentifier;\n      // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n      // Safari cf\n      // http://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n      if (key == null) {\n        return 'Unidentified';\n      }\n      if (key.startsWith('U+')) {\n        key = String.fromCharCode(parseInt(key.substring(2), 16));\n        if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n          // There is a bug in Chrome for numeric keypad keys:\n          // https://code.google.com/p/chromium/issues/detail?id=155654\n          // 1, 2, 3 ... are reported as A, B, C ...\n          key = ( /** @type {?} */((_chromeNumKeyPadMap as any)))[key];\n        }\n      }\n    }\n\n    return _keyMap[key] || key;\n  }\n/**\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string): EventTarget|null {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return document;\n    }\n    if (target === 'body') {\n      return document.body;\n    }\n    return null;\n  }\n/**\n * @return {?}\n */\ngetHistory(): History { return window.history; }\n/**\n * @return {?}\n */\ngetLocation(): Location { return window.location; }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document): string|null {\n    const /** @type {?} */ href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n/**\n * @return {?}\n */\nresetBaseElement(): void { baseElement = null; }\n/**\n * @return {?}\n */\ngetUserAgent(): string { return window.navigator.userAgent; }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: Element, name: string, value: string) {\n    this.setAttribute(element, 'data-' + name, value);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: Element, name: string): string|null {\n    return this.getAttribute(element, 'data-' + name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any): any { return getComputedStyle(element); }\n/**\n * @return {?}\n */\nsupportsWebAnimation(): boolean {\n    return typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n  }\n/**\n * @return {?}\n */\nperformanceNow(): number {\n    // performance.now() is not available in all browsers, see\n    // http://caniuse.com/#search=performance.now\n    return window.performance && window.performance.now ? window.performance.now() :\n                                                          new Date().getTime();\n  }\n/**\n * @return {?}\n */\nsupportsCookies(): boolean { return true; }\n/**\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string): string|null { return parseCookieValue(document.cookie, name); }\n/**\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {\n    // document.cookie is magical, assigning into it assigns/overrides one cookie value, but does\n    // not clear other cookies.\n    document.cookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);\n  }\n}\n\nlet /** @type {?} */ baseElement: HTMLElement|null = null;\n/**\n * @return {?}\n */\nfunction getBaseElementHref(): string|null {\n  if (!baseElement) {\n    baseElement = /** @type {?} */(( document.querySelector('base')));\n    if (!baseElement) {\n      return null;\n    }\n  }\n  return baseElement.getAttribute('href');\n}\n\n// based on urlUtils.js in AngularJS 1\nlet /** @type {?} */ urlParsingNode: any;\n/**\n * @param {?} url\n * @return {?}\n */\nfunction relativePath(url: any): string {\n  if (!urlParsingNode) {\n    urlParsingNode = document.createElement('a');\n  }\n  urlParsingNode.setAttribute('href', url);\n  return (urlParsingNode.pathname.charAt(0) === '/') ? urlParsingNode.pathname :\n                                                       '/' + urlParsingNode.pathname;\n}\n/**\n * @param {?} cookieStr\n * @param {?} name\n * @return {?}\n */\nexport function parseCookieValue(cookieStr: string, name: string): string|null {\n  name = encodeURIComponent(name);\n  for (const /** @type {?} */ cookie of cookieStr.split(';')) {\n    const /** @type {?} */ eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n        eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {LocationChangeListener, PlatformLocation} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../../dom/dom_adapter';\nimport {DOCUMENT} from '../../dom/dom_tokens';\n\nimport {supportsState} from './history';\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n */\nexport class BrowserPlatformLocation extends PlatformLocation {\nprivate _location: Location;\nprivate _history: History;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._init();\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_init() {\n    this._location = getDOM().getLocation();\n    this._history = getDOM().getHistory();\n  }\n/**\n * @return {?}\n */\nget location(): Location { return this._location; }\n/**\n * @return {?}\n */\ngetBaseHrefFromDOM(): string { return /** @type {?} */(( getDOM().getBaseHref(this._doc))); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('popstate', fn, false);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('hashchange', fn, false);\n  }\n/**\n * @return {?}\n */\nget pathname(): string { return this._location.pathname; }\n/**\n * @return {?}\n */\nget search(): string { return this._location.search; }\n/**\n * @return {?}\n */\nget hash(): string { return this._location.hash; }\n/**\n * @param {?} newPath\n * @return {?}\n */\nset pathname(newPath: string) { this._location.pathname = newPath; }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.pushState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.replaceState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @return {?}\n */\nforward(): void { this._history.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._history.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction BrowserPlatformLocation_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserPlatformLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserPlatformLocation.ctorParameters;\n/** @type {?} */\nBrowserPlatformLocation.prototype._location;\n/** @type {?} */\nBrowserPlatformLocation.prototype._history;\n/** @type {?} */\nBrowserPlatformLocation.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {APP_INITIALIZER, ApplicationInitStatus, Inject, InjectionToken, Injector, Provider} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nexport const TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n/**\n * @param {?} transitionId\n * @param {?} document\n * @param {?} injector\n * @return {?}\n */\nexport function appInitializerFactory(transitionId: string, document: any, injector: Injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const /** @type {?} */ dom = getDOM();\n      const /** @type {?} */ styles: any[] =\n          Array.prototype.slice.apply(dom.querySelectorAll(document, `style[ng-transition]`));\n      styles.filter(el => dom.getAttribute(el, 'ng-transition') === transitionId)\n          .forEach(el => dom.remove(el));\n    });\n  };\n}\n\nexport const /** @type {?} */ SERVER_TRANSITION_PROVIDERS: Provider[] = [\n  {\n    provide: APP_INITIALIZER,\n    useFactory: appInitializerFactory,\n    deps: [TRANSITION_ID, DOCUMENT, Injector],\n    multi: true\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * A DI Token representing the main rendering context. In a browser this is the DOM Document.\n * \n * Note: Document might not be available in the Application Context when Application and Rendering\n * Contexts are not the same (e.g. when running the application into a Web Worker).\n * \n * \\@stable\n */\nexport const DOCUMENT = new InjectionToken<Document>('DocumentToken');\n"], "names": ["ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "prototype", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "win", "console", "profile", "getDOM", "performanceNow", "start", "DATA_URL_PATTERN", "DOM", "OPTIONAL_END_TAG_BLOCK_ELEMENTS", "tagSet", "OPTIONAL_END_TAG_INLINE_ELEMENTS", "INLINE_ELEMENTS", "merge", "SanitizingHtmlSerializer", "sanitizeChildren", "el", "current", "isElementNode", "this", "startElement", "isTextNode", "chars", "nodeValue", "sanitizedSomething", "<PERSON><PERSON><PERSON><PERSON>", "endElement", "element", "_this", "tagName", "nodeName", "toLowerCase", "VALID_ELEMENTS", "hasOwnProperty", "buf", "push", "attributeMap", "for<PERSON>ach", "value", "attrName", "VALID_ATTRS", "lower", "URI_ATTRS", "sanitizeUrl", "VOID_ELEMENTS", "DomSanitizerImpl", "sanitize", "ctx", "_angular_core", "SecurityContext", "NONE", "HTML", "SafeHtmlImpl", "changingThisBreaksApplicationSecurity", "checkNotSafeValue", "sanitizeHtml", "_doc", "String", "STYLE", "SafeStyleImpl", "sanitizeStyle", "SCRIPT", "SafeScriptImpl", "Error", "URL", "SafeResourceUrlImpl", "SafeUrlImpl", "expectedType", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "type", "undefined", "decorators", "Inject", "args", "DOCUMENT", "SafeValueImpl", "toString", "tslib_1.__extends", "getTypeName", "provide", "Dom<PERSON><PERSON><PERSON>zer", "useClass", "ngModule", "BrowserModule", "providers", "APP_ID", "useValue", "params", "appId", "TRANSITION_ID", "useExisting", "SERVER_TRANSITION_PROVIDERS", "NgModule", "BROWSER_SANITIZATION_PROVIDERS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useFactory", "<PERSON><PERSON><PERSON><PERSON>", "deps", "RendererFactory2", "DomEventsPlugin", "multi", "EVENT_MANAGER_PLUGINS", "KeyEventsPlugin", "HammerGesturesPlugin", "Testability", "DomRendererFactory2", "SharedStylesHost", "DomSharedStylesHost", "ELEMENT_PROBE_PROVIDERS", "Meta", "MODIFIER_KEYS", "_super", "__extends", "parsedEvent", "parseEventName", "eventName", "parts", "split", "domEventName", "shift", "length", "key", "_normalizeKey", "pop", "modifierName", "index", "indexOf", "splice", "getEventFullKey", "event", "<PERSON><PERSON><PERSON>", "getEventKey", "keyName", "EVENT_NAMES", "pan", "panstart", "panmove", "panend", "panleft", "panright", "pandown", "pinchstart", "pinchend", "pinchcancel", "press", "pressup", "rotatemove", "rotateend", "HammerGestureConfig", "buildHammer", "mc", "Hammer", "get", "set", "enable", "zone", "manager", "getZone", "runGuarded", "handler", "eventObj", "isCustomEvent", "_config", "events", "Injectable", "NAMESPACE_URIS", "xmlns", "<PERSON><PERSON><PERSON><PERSON>", "defaultRenderer", "encapsulation", "ViewEncapsulation", "Emulated", "renderer", "rendererByCompId", "id", "EmulatedEncapsulationDomRenderer2", "eventManager", "sharedStylesHost", "applyToHost", "Native", "ShadowDom<PERSON><PERSON><PERSON>", "addStyles", "styles", "end", "DefaultDomRenderer2", "createElement", "name", "namespace", "setAttribute", "namespaceUri", "removeAttribute", "addGlobalEventListener", "target", "decoratePreventDefault", "callback", "flattenStyles", "component", "hostEl", "call", "styleEl", "document", "removeEventListener", "BrowserGetTestability", "init", "setTestabilityGetter", "global", "addToWindow", "registry", "ɵglobal", "elem", "findInAncestors", "testability", "findTestabilityInTree", "getAllTestabilities", "getAllRootElements", "whenAllStable", "testabilities", "count", "didWork", "decrement", "didWork_", "t", "getTestability", "Title", "setTitle", "newTitle", "NgProbeToken$1", "token", "APP_INITIALIZER", "EventManager", "_findPluginFor", "plugin", "_eventNameToPlugin", "i", "plugins", "plugin_1", "supports", "NgZone", "EventManagerPlugin", "_stylesSet", "add", "style", "getAllStyles", "Array", "from", "ngOnDestroy", "_styleNodes", "styleNode", "remove", "addTags", "tags", "forceCreation", "tag", "meta", "getTag", "selector", "_parseSelector", "_containsAttributes", "Object", "keys", "every", "_dom", "getAttribute", "_createNgProbe", "extraTokens", "coreTokens", "tokens", "reduce", "prev", "compId", "replace", "COMPONENT_REGEX", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "url", "getInertElement", "templateEl", "doc", "createHtmlDocument", "inertElement", "querySelector", "_i", "_a", "sets", "arguments", "encodeEntities", "stripCustomNsAttrs", "containerEl", "unsafeHtml", "unsafeHtmlInput", "parsedHtml", "mXSSAttempts", "setInnerHTML", "defaultDoc", "documentMode", "getInnerHTML", "sanitizer", "safeHtml", "getTemplateContent", "parent", "child", "<PERSON><PERSON><PERSON><PERSON>", "isDevMode", "hasBalancedQuotes", "outsideSingle", "outsideDouble", "trim", "urlMatch", "extendStatics", "setPrototypeOf", "__proto__", "d", "b", "p", "DomAdapter", "resolveAndSetHref", "baseUrl", "href", "getGlobalEventTarget", "getBaseHref", "getData", "supportsWebAnimation", "getAnimationPrefix", "getTransitionEnd", "GenericBrowserDomAdapter", "_animationPrefix", "_transitionEnd", "element_1", "getStyle", "domPrefixes", "transEndEventNames_1", "WebkitTransition", "MozTransition", "OTransition", "transition", "e", "supportsDOMEvents", "class", "readonly", "DOM_KEY_LOCATION_NUMPAD", "_keyMap", "\b", "\t", "", "\u001b", "Right", "Up", "Win", "_chromeNumKeyPadMap", "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "M", "N", "compareDocumentPosition", "node", "urlParsingNode", "BrowserDomAdapter", "invoke", "methodName", "apply", "getOuterHTML", "outerHTML", "content", "nextS<PERSON>ling", "parentElement", "parentNode", "childNodes", "res", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setChecked", "checked", "createStyleElement", "css", "createShadowRoot", "getShadowRoot", "shadowRoot", "getBoundingClientRect", "n", "matches", "nodeType", "Node", "TEXT_NODE", "isCommentNode", "COMMENT_NODE", "isShadowRoot", "DocumentFragment", "importIntoDoc", "importNode", "templateAwareRoot", "adoptNode", "fromCharCode", "parseInt", "substring", "getComputedStyle", "Date", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "baseElement", "BrowserPlatformLocation", "configurable", "supportsState", "back", "_history", "appInitializerFactory", "transitionId", "injector", "ApplicationInitStatus", "donePromise", "then", "dom", "slice", "querySelectorAll", "filter", "exports", "module", "factory", "require", "getBaseElementHref", "parseCookieValue", "cookieStr", "encodeURIComponent", "cookie"], "mappings": ";;;;;0BgBAA,gBAAAmb,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,mBAAAA,QAAA,+hBG02BA,QAAAC,gWA4BA,QAAAC,kBAAAC,UAAAnP,MACAA,KAAAoP,mBAAApP,8DGv4BA,GAAAqP,QAAArJ,GAAAD;;;;;;;;0DDmCA,QAAAoI,uBAAAC,aAAAtN,SAAAuN,UACA,MAAA,YAIAA,SAAAvQ,IAAA3G,cAAAmX,uBAAAC,YAAAC,KAAA,WACA,GAAAC,KAAAla,SACAqL,OAAA6D,MAAA3P,UAAA4a,MAAArD,MAAAoD,IAAAE,iBAAA7N,SAAA,wBACAlB,QAAAgP,OAAA,SAAAzZ,IAAA,MAAAsZ,KAAA7J,aAAAzP,GAAA,mBAAAiZ,eACA5X,QAAA,SAAArB,IAAA,MAAAsZ,KAAA3K,OAAA3O;;;;;;;iMVoBA,QAAA0P,gBAAAC,YAAAC,6SAWA,MAAAC,QAAAC,OAAA,SAAAC,KAAA/C,GAAA,MAAA+C,MAAA/C,EAAAnC,MAAAmC,EAAAM,MAAAyC,gOJlBA,QAAAzE,eAAA0E,OAAAvF,OAAAU,QACA,IAAA,GAAAyC,GAAA,EAAAA,EAAAnD,OAAA9D,OAAAiH,IAAA,CACA,GAAAQ,OAAA3D,OAAAmD,4DAKAQ,MAAAA,MAAA6B,QAAAC,gBAAAF,0CAUA,QAAA5E,wBAAA+E,8GAOA/I,MAAAgJ,gYNhBAC,iSC7BA,QAAAC,sDAGA9Q,KAAAJ,QAEA,IAAAmR,YAAA/Q,IAAAoL,cAAA,uDAGA,IAAA4F,KAAAhR,IAAAiR,wBACAC,aAAAlR,IAAAmR,cAAAH,IAAA,kEAKAE,cAAAlR,IAAAoL,cAAA,OAAA4F,4GASA,IAAA,WAAAI,GAAA,EAAAC,GAAAhC,KAAArI,MAAA,KAAAoK,GAAAC,GAAAlK,OAAAiK,KAAA,mCAKA,QAAA/Q,SACA,IAAA,GAAAiR,SAAAF,GAAA,EAAAA,GAAAG,UAAApK,OAAAiK,KACAE,KAAAF,IAAAG,UAAAH,mVAuMA,QAAAI,gBAAA1P,+TAmBA,QAAA2P,oBAAAjR,2TAsBA,GAAAkR,aAAAZ,kBAGAa,WAAAC,gBAAA3O,OAAA2O,iBAAA,kBAIAC,WAAAF,UACA,GAAA,CACM,GAAN,IAAAG,aACA,KAAA,IAAAvO,OAAA,wDAGAuO,sCAGA9R,IAAA+R,aAAAL,YAAAC,YACAK,WAAAC,cAEAR,mBAAAC,aAGAG,WAAA7R,IAAAkS,aAAAR,mBACAC,aAAAE,mBAAAM,WAAA,GAAA7R,0BAEA8R,SAAAD,UAAA5R,iBAAAP,IAAAqS,mBAAAX,cAAAA,aAlCAY,OAAAtS,IAAAqS,mBAAAX,cAAAA,oEAqCA,GAAAa,OAAAlB,GAAAD,GACApR,KAAAwS,YAAAF,OAAAC,aAEA/P,eAAAiQ,aAAAN,UAAAnR,8JapQA,QAAA0R,mBAAA5Q,OAGA,IAAA,GAFA6Q,gBAAA,EACAC,eAAA,EACAxE,EAAA,EAAAA,EAAAtM,MAAAqF,OAAAiH,IAAA,oMAoBA,GADEtM,MAAFmB,OAAAnB,OAAA+Q,QACA/Q,MAEA,MAAA,mCZtGA,OAAAgR,WAAA3Q,YAAA2Q,SAAA,MAAAA,SAAA,+iBaqBA,iBAAIC,cAAJlD,OAAAmD,iBACAC,uBAAAnE,QAA2C,SAA3CoE,EAAAC,GAAAD,EAAAD,UAAAE,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6C1R,eAA7C2R,KAAAF,EAAkEE,GAAlED,EAAAC,80KCqqBAC,WAAAlU,UAAAmU,kBAAA,SAAAnS,QAAAoS,QAAAC,sHAiBAH,WAAAlU,UAAAsU,qBAAA,SAAAzC,IAAArF,qGAgBA0H,WAAAlU,UAAAuU,YAAA,SAAA1C,+NA+BAqC,WAAAlU,UAAAwU,QAAA,SAlmBYxS,QAkmBZkK,QAKAgI,WAAAlU,UAAAyU,qBAAA,aAKAP,WAAAlU,UAAAU,eAAA,aAKAwT,WAAAlU,UAAA0U,mBAAA,aAKAR,WAAAlU,UAAA2U,iBAAA,uOCtvBAC,yBAAA,SAAArN,QACA,QAAAqN,4BAEA,GAAA3S,OAAAsF,OAAAuF,KAAAtL,OAAAA,IACAS,OAAA4S,iBAAA,KACA5S,MAAA6S,eAAA,IACA,KACA,GAAAC,WAAA9S,MAAAgK,cAAA,MAAAe,SACA,IAAA,MAAA/K,MAAA+S,SAAAD,UAAA,iBACA9S,MAAA4S,iBAAA,OAKA,KAAA,GADAI,cAAA,SAAA,MAAA,IAAA,MACAhG,EAAA,EAAAA,EAAAgG,YAAAjN,OAAAiH,IACA,GAAA,MAAAhN,MAAA+S,SAAAD,UAAAE,YAAAhG,GAAA,iBAAA,CACAhN,MAAA4S,iBAAA,IAAAI,YAAAhG,GAAA7M,cAAA,GAEA,OAIA,GAAA8S,uBACAC,iBAAA,sBAAcC,cAAd,gBACAC,YAAA,gCACAC,WAAA,gBAEA5E,QAAAC,KAAAuE,sBAAAxS,QAAA,SAAAuF,6FAKA,MAAAsN,4EAhCA/N,WAAAoN,yBAAArN,4LAkDAlG,GAAAgT,KAAA,MAAAA,KAAAD,QAAAA,QAAA,OAAAC,MAKAO,yBAAA5U,UAAAwV,kBAAA,WAAA,OAAA,iiBCtDAC,MAAA,kCAEAC,SAAA,gCAGEC,wBAAF,EAEEC,SAGAC,KAAF,YACEC,KAAF,MACEC,IAAF,SACAC,IAAA,oDAKAC,MAAA,aACAC,GAAA,kEAIAC,IAAA,MAKEC,qBACAC,EADK,IAELC,EADK,IAELC,EADK,IAELC,EADK,IAELC,EAAF,IACEC,EAAF,IACAC,EAAA,IACIC,EACJ,IAEIvJ,EAAJ,IACEwJ,EAAF,IACAC,EAAA,IACAC,EAAA,IACAC,EAAA,IACAxJ,EAAA,0BAKAnK,eAAAmK,QAAA,OAGsCnI,aAAtCA,cAAAmI,QAAAnI,KAAAA,UAAAA,UAAAA,SAAAA,MAHA,SAAA,GAAA7D,KAAAyV,wBAAAC,YAwyBAC,4lBAzxBAC,kBAAApX,UAAAqX,OAAA,SAAAhW,GAAAiW,WAAArS,MAAA,GAAAqS,YAAAC,MAAA,GAAAtS,g8DAiKAmS,kBAAApX,UAAAwX,aAAA,SAAAnW,IAAA,MAAAA,IArG8CoW,WA0G9CL,kBAAApX,UAAAmC,SAAA,SAAA+U,MAAA,MAAAA,MAzGsD/U,UA8GtDiV,kBAAApX,UAAA4B,UAAA,SAAAsV,MAAA,MAAAA,MAAAtV,WAIAwV,kBAAApX,UAAA6E,KAAA,SAAAqS,MAAA,MAAAA,MAAArS,iGAOA,KAAA6S,cAUAN,kBAAApX,UAAA8B,WAAA,SAAAT,IAAA,MAAAA,IAzH8CS,YA8H9CsV,kBAAApX,UAAA2X,YAAA,SAAAtW,IAAA,MAAAA,IAAAsW,aAKAP,kBAAApX,UAAA4X,cAAA,SAAAvW,IAAA,MAAAA,IAAAwW,YAKAT,kBAAApX,UAAA8X,WAAA,SAAAzW,IAAA,MAAAA,IAAAyW,uGAOAC,IAAA,GAAApI,OAAAmI,WAAA9P,4UAiCAoP,kBAAApX,UAAAgY,aAAA,SAAA3W,GAAA4W,SAAAC,UAAA7W,GAAA2W,aAAAC,SAAAC,y4BAwEAd,kBAAApX,UAAAmY,WAAA,SAAA9W,GAAAsB,OAAAtB,GAAA+W,QAAAzV,qyBAmDAyU,kBAAApX,UAAAqY,mBAAA,SAAAC,IAAAzG,sIAUAuF,kBAAApX,UAAAuY,iBAAA,SAAAlX,IAAA,MAAA,IAAAkX,oBAKAnB,kBAAApX,UAAAwY,cAAA,SA9P4BnX,IA8P5B,MAAA,IAAAoX,ixCA2FArB,kBAAApX,UAAAkC,QAAA,SAAAF,SAAA,MAAAA,SAAAE,g3CAgGA,MAAAb,IAAAqX,+VAyBAC,EAAAC,SAAAD,EAAAC,QAAArI,kPAiBA6G,kBAAApX,UAAA0B,WAhZG,SAgZHwV,MAAA,MAAAA,MAAA2B,WAAAC,KAAAC,WAKA3B,kBAAApX,UAAAgZ,cAAA,SApZG9B,MAoZH,MAAAA,MAAA2B,WAAAC,KAAAG,qOAiBA7B,kBAAApX,UAAAkZ,aAAA,SAAAhC,MAAA,MAAAA,gBAAAiC,mBAKA/B,kBAAApX,UAAAoZ,cAAA,SApaiClC,MAoajC,MAAAlK,UAAAqM,WAAA7X,KAAA8X,kBAAApC,OAAA,IAKAE,kBAAApX,UAAAuZ,UAAA,SAAArC,MAAA,MAAAlK,UAAAuM,UAAArC,iJAYA,IAAA,MAAAjP,IAAA,CAKA,2BAAA,MAAAA,gDAKAA,IAAAnE,OAAA0V,aAAAC,SAAAxR,IAAAyR,UAAA,GAAA,kJAgBAtC,kBAAApX,UAAAsU,qBAAA,SAAAzC,IAAArF,QACA,MAAA,WAAAA,kCA3a0BQ,4rBAme1BoK,kBAAApX,UAAA2Z,iBAAA,SAAA3X,SAAA,MAAA2X,kBAAA3X,sEAKA,MAAA,kBAAA,SAAAhC,UAAA,2IASA,GAAA4Z,OAAAC,gLAiBAzC,kBAAApX,UAAA8Z,UAAA,SAAA5N,KAAAvJ,kGAKAiS,0BACAmF,YAAA,yGCj0BA,QAAAC,yBAAAnW,yFAAA2D,WAAAwS,wBAAAzS,8PAgBA0S,cAAA,qlCAwDAC,mKAYAF,wBAAAha,UAAAma,KAAA,WAAA3Y,KAAA4Y,SAAAD;;;;;;;2ONxDA9S,KAAArH,UAAAiQ,QAAA,SAAAC,KAAAC,eACA,GAAAlO,OAAAT,mGAKA4O,sfA+BA,IAAAC,MAAA7O,KAAA8O,OAAAC,8YA2BA,GAAAA,UAAA/O,KAAAgP,eAAAH,MACA5C,KAAAjM,KAAA8O,OAAAC,6iBA1FAlJ,KAAArH,UAAAyQ,oBAAA,SAAAL,IAAA3C,oBAgFA,OAAAiD,QAAAC,KAAAP,KAAAQ,MAAA,SAAA3I,KAAA,MAAAhG,OAAA4O,KAAAC,aAAArD,KAAAxF,OAAAmI,IAAAnI;;;;;;;0QLlGA,QAAAiF,gCAKAA,uBAAAC,KAAA,WAAA9J,cAAA+J,qBAAA,GAAAF,yBAAAA,sBAMAG,UANAC,YAAA,SAAAC,UAQAlK,cAAAmK,QAAA,sBAAA,SAAAC,KAAAC,iBACA,SAAAA,kBAAAA,iBAAA,EACA,IAAAC,aAAAJ,SAAAK,sBAAAH,KAAAC,gBACAF,IAAAA,MAAAA,YACAA,KAAAA,IAAAA,OAAAA,0CAEA,OAAAG,cAEAtK,cAAAmK,QAAA,2BAAA,WAAA,MAAAD,UAAAM,uBACAxK,cAAAmK,QAAA,0BAAA,WAAA,MAAAD,UAAAO,qBACA,IAAAC,eAAA,SAAArB,UACA,GAAAsB,eAAA3K,cAAAmK,QAAA,6BACAS,MAAAD,cAAAhG,OACAkG,SAAA,EACAC,UAAA,SAAAC,UAJAf,QAAAA,SAAAA,SAOAA,QACA,GAAAY,OACAZ,SAAAA,uPAkBAH,sBAAAlN,UAAA4N,sBAAA,SAAAL,SAAAE,KAAAC,iBACA,GAXe,MAWPD,KACR,MAAA,KAEA,IAAAY,GAAAd,SAAAe,eAAAb,KACA,OAAA,OAAAY,EACAA,EC7EAX,oVA+CAa,MAAAvO,UAAAwO,SAAA,SAAAC,UAAAhO,SAAA+N,SAAAhN,KAAAqC,KAAA4K,qVCLAC,eAAA,WAJA,QAAAA,gBAAAxC,KAAAyC,gEA0CAvH,0BChFA7B,QAAAlC,cAAAuL,8OA+BAC,aAAA,6nBAyCAA,aAAA7O,UAAA8O,eAAA,SAAAnH,WACA,GAAAoH,QAAAvN,KAAAwN,mBAAAhF,IAAArC,UACA,IAAAoH,OACA,MAAAA,OAfA,KAAA,0BAAAE,EAAA,EAAAA,EAAAC,QAAAlH,OAAAiH,IAAA,CAkBA,GAAAE,UAAAD,QAAAD,EACA,IAAAE,SAAAC,SAAAzH,2EAfA,KAAA,IAAAvD,OAAA,2CAAAuD,wNAOA9C,KAAAxB,cAAAgM,oPCvEAC,mBAAAtP,UAAAuM,uBAAA,SAAAvK,QAAA2F,UAAA4C,sQAqBA,QAAArD,kDASAA,kBAAAlH,UAAA6L,UAAA,SAAAC,sGAKA7J,MAAAsN,WAAAC,IAAAC,6HAeAvI,iBAAAlH,UAAA0P,aAAA,WAAA,MAAAC,OAAAC,KAAApO,KAAA+N,+DAqBA1K,KAAAxB,cAAAuH,iEAGA,IAAAzD,qBAAA,SAAAI,mMAAAC,WAAAL,oBAAAI,2FAkBAuE,QAAApJ,QAAA,SAAA+M,whBA2BAtI,oBAAAnH,UAAA6P,YAAA,WAAArO,KAAAsO,YAAApN,QAAA,SAAAqN,WAAA,MAAAtP,UAAAuP,OAAAD;;;;;;;AN9FA,GAAAlF,uKAKAC,MAAA,6KAsDA7D,oBAAA,4OAgBAA,qBAAAjH,UAAA+K,eAAA,SAAA/I,QAAA6C,MACA,IAAA7C,UAAA6C,KACA,MAAArD,MAAAwJ,eAEA,QAAAnG,KAAAoG,eACA,IAAA5H,eAAA6H,kBAAAC,SACA,GAAAC,UAAA5J,KAAA6J,iBAAArB,IAAAnF,KAAAyG,GAOQ,OANRF,YACAA,SACA,GAAAG,mCAAA/J,KAAAgK,aAAAhK,KAAAiK,iBAAA5G,MACArD,KAAA6J,iBAAApB,IAAApF,KAAAyG,GArBgBF,WAuBhB,SAAAM,YAAA1J,SACAoJ,QAEA,KAAA/H,eAAA6H,kBAAAS,OACA,MAAA,IAAAC,mBAAApK,KAAAgK,aAAAhK,KAAAiK,iBAAAzJ,QAAA6C,sGAnBArD,MAAAiK,iBAAAI,UAAAC,+IAmCA7E,oBAAAjH,UAAA+L,IAAA,gZAmDAC,oBAAAhM,UAAAiM,cAAA,SAAAC,KAAAC,oyBAkDA,KAAA9K,kRAsBA2K,oBAAAhM,UAAAoM,aAAA,SAAA/K,GAAA6K,KAAAvJ,MAAAwJ,WAxFA,GAAAA,UAAA,CAyFAD,KAAAC,UAAA,IAAAD,IACA,IAAAG,cAAAxB,eAAAsB,UACAE,uHA9EAL,oBAAAhM,UAAAsM,gBAAA,SAAAjL,GAAA6K,KAAAC,WA8FA,GAAAA,UAAA,CACA,GAAAE,cAAAxB,eAAAsB,UACAE,ihCApKA7K,KAAAgK,aAAAe,uBAAAC,OAAA/D,MAAAgE,uBAAAC,kWA6RA,IAAAZ,QAAAa,cAAAC,UAAAtB,GAAAsB,UAAAd,0pBAyCA,QAAMF,mBAANJ,aAAAC,iBAAAoB,OAAAD,WACA,GAAA3K,OAAAsF,OAAAuF,KAAAtL,KAAAgK,eAAAhK,IACAS,OAAAwJ,iBAAAA,iBACAxJ,MAAA4K,OAAAA,qNArKA,GAAAE,SAAAC,SAAAf,cAAA,k/BC7PAtF,gBAAA,SAAAY,+EAAAC,WAAAb,gBAAAY,wJAOA,sDAAA,WAAA,MAAAvF,SAAAiL,oBAAAtF,UAAA,SAAA;;;;;;;AFLA,GAAEkB,cAEAC,KAAF,EACEC,UAAY,EACZC,SAAF,EACEC,QAAF,eAEEC,SAAF,EACEC,UADY,WAGZC,SAAF,WAGEC,YAAF,eAEEC,UAAF,EACEC,aAAF,yBAIEC,OAAF,EACEC,SAAF,2BAIEC,YAAF,EACAC,WAAA,yQAmCAC,qBAAA5J,UAAA6J,YAAA,SAAA7H,SAEA,GAAA8H,IAAA,GAAAC,QAAA/H,QACA8H,IAAAE,IAAA,SAAAC,KAAAC,QAAA,0HACA,OAAAJ,2rBA+DA,GAAM7H,OAANT,KACA2I,KAAA3I,KAAA4I,QAAAC,gBACA1C,WAAAA,UAAAvF,sHAKA+H,KAAAG,WAAA,WAAAC,QAAAC,+FAQAzD,qBAAA/G,UAAAyK,cAAA,SAAA9C,WAAA,MAAAnG,MAAAkJ,QAAAC,OAAArC,QAAAX,YAAA,+EDxKA9C,KAAAxB,cAAAuH;;;;;;;AA0BA,GAAAtD,gBAAA,MAAA,UAAA,OAAA,+MASAR,gBAAA,SAAAS,+EAAAC,WAAAV,gBAAAS,sMAqBA,GAAAE,aAAAX,gBAAAY,eAAAC,uPAaAb,gBAAAY,eAAA,SAAAC,WACA,GAAAC,OAAAD,UAAAvF,cAAAyF,MAAA,KACAC,aAAAF,MAAAG,OACA,IAAA,IAAAH,MAAAI,QAAA,YAAAF,cAAA,UAAAA,aACA,MAAA,KAGA,IAbwBG,KAaxBnB,gBAAAoB,cAAAN,MAAAO,qBAEAb,cAAA5E,QAAA,SAAA0F,cACA,GAAAC,OAAAT,MAAAU,QAAAF,aAEAC,QAAA,IACAT,MAAAW,OAbYF,MAaZ,8KAiBAvB,gBAAA0B,gBAAA,SAAAC,OACA,GAAAC,SAAA,GACAT,IAAAxH,SAAAkI,YAAAF,aACAR,KAAAA,IAAA7F,cACA,MAAA6F,IACAA,IAAA,QAEA,MAAAA,MACAA,IAAA,OAEAX,cAAA5E,QAAA,SAAA0F,yVAIAtB,gBAAAoB,cAAA,SAAAU,SA0BA,OAAAA;;;;;;;qFJnFAhI,iBAAA,yJCzBAC,IAAA,oDAmEQC,gCAARC,OAAA,kDAEAC,iCAAAD,OAAA,2WAYAE,gBAAAC,MAAAF,iCAAAD,OAAA,whCAiCAI,0BAAAnB,UAAAoB,iBAAA,SAAAC,IAIA,IADA,GAAAC,SAAAD,GAAA,WACAC,YACAT,IAAAU,cAAAD,SACAE,KAAAC,aAAA,SAEAZ,IAAAa,WAAAJ,SACAE,KAAAG,MAAAd,IAAAe,UAAAN,UAIAE,KAAAK,oBAAA,0BAKAP,QAAAT,IAAAiB,WAAAR,aAIA,MAAAA,SAAA,CAEAT,IAAAU,cAAAD,UAXgBE,KAAhBO,WAAA,kMA2BAZ,yBAAAnB,UAAAyB,aAAA,SAAAO,SACA,GAAAC,OAAAT,KACAU,QAAArB,IAAAsB,SAAAH,SAAAI,aACA,OAAAC,gBAAAC,eAAAJ,UAZAV,KAAAe,IAAAC,KAA4B,KAgB5BhB,KAAAe,IAAAC,KAAAN,SAfArB,IAAA4B,aAAAT,SAAAU,QAAA,SAAAC,MAAAC,0CAiBM,OAANC,aAAAP,eAAAQ,QAKAC,UAfmBD,SAgBnBH,MAAAK,YAAAL,wGAbAV,MAAAM,IAAAC,KAAA,2EAQUP,MAAVJ,oBAAA,mCANAL,KAAAK,oBAAA,mHAOAQ,gBAAAC,eAAAJ,WAAAe,cAAAX,eAAAJ,m0CCdAgB,iBAAAlD,UAAAmD,SAAA,SAAAC,IAAAT,OACA,GAAA,MAAMA,MACN,MAAA,KApBA,QAAAS,KAqBA,IAAAC,eAAAC,gBAAAC,KACQ,MApBO,MAqBT,KApBKD,eAoBXA,gBApB2BE,KAqBnB,MAAIb,iBApBiBc,cAAuBd,MAAMe,uCAqBlDlC,KApBKmC,kBAAkBhB,MAAO,QAqBtCiB,aAAApC,KAAAqC,KAAAC,OAAAnB,QACM,KApBKW,eAoBXA,gBApB2BS,MAqBnB,MAAIpB,iBApBiBqB,4DAsB7BxC,KAAAmC,kBAAAhB,MAAA,SACAsB,cAAA,OACA,KAAAX,eAAAA,gBAAAY,OACQ,GAARvB,gBAAAwB,kEAGA,MADA3C,MAAAmC,kBAAAhB,MAAA,UACA,GAAAyB,OAAA,wCACA,KAAAf,eAAAC,gBAAAe,IACQ,MAAR1B,iBAAA2B,sBAAA3B,gBAAA4B,aAGA5B,MAAAe,uCAEAlC,KAAAmC,kBAAAhB,MAAA,OACAK,YAAAc,OAAAnB,mYAiBAO,iBAAAlD,UAAA2D,kBAAA,SAAAhB,MAAA6B,sKAUAtB,iBAAAlD,UAAAyE,wBAAA,SAAA9B,OAAA,MAAA,IAAAc,cAAAd,QAKAO,iBAAAlD,UAAA0E,yBAAA,SAAA/B,OAAA,MAAA,IAAAqB,eAAArB,QAKAO,iBAAAlD,UAAA2E,0BAAA,SAAAhC,OAAA,MAAA,IAAAwB,gBAAAxB,QAIAO,iBAAAlD,UAAA4E,uBAAA,SAAAjC,OAAA,MAAA,IAAA4B,aAAA5B,kFAxCA,MAAA,IAAA2B,qBAAA3B,yJAMAkC,KAAAC,OAAAC,aAAAF,KAAAxB,cAAA2B,OAAAC,MAAAC,mPAyFAC,cAAAnF,UAAAoF,SAAA,WACA,MAAA,0CAAA5D,KAAAkC,sCAAA2B,4MAMA5B,aAAAzD,UAAAsF,YAAA,WAAA,MAAA,sBAKAH,uKACAnB,cAAAhE,UAAAsF,YAAA,WAAA,MAAA,wBAKAH,0KACAhB,eAAAnE,UAAAsF,YAAA,WAAA,MAAA,0BAKAH,iKACAZ,YAAAvE,UAAAsF,YAAA,WAAA,MAAA,ytBCnRAC,QAAAC,aAAAC,SAAAvC,gZAyDA,2DAfA,OAQAwC,SAAAC,cACAC,YACAL,QAAAlC,cAAAwC,OAAAC,SAAAC,OAAAC,QACAT,QAAAU,cAAAC,YAAA7C,cAAAwC,QAAAM,+BAIAR,gBAEAA,eAAAZ,aACAF,KAAAxB,cAAA+C,SAAAnB,OACAW,WACIS,gCACJd,QAAAlC,cAAAiD,aAAAC,WAAAC,aAAAC,UACAlB,QAAAmB,sBAAAjB,SAAAkB,gBAAAC,OAAA,IACArB,QAAAsB,sBAAApB,SAAAqB,gBAAAF,OAAA,IACArB,QAAAsB,sBAAApB,SAAAsB,qBAAAH,OAAA,IACAI,QAAAA,sBAAAA,SAAAA,qBANAC,qBAQA1B,QAAAlC,cAAAqD,iBAAAR,YAAAe,sBACA1B,QAAA2B,iBAAAhB,YAAAiB,gFALAC,wBAUAC;;;;;;;+CJ5GAzH,0BAAA,WAWA,QAAAA,2BAAAC,UAAAC,wNAkCAC,iBAAAC,UAAAC,oBAAA,SAAAC,QACA,GAAAC,QAAAD,QAAAA,OAAA,OACAE,YAAA,mBAEAC,oBAAA,MAAAC,IAAAC,QAAAC,OACAL,SAAAE,qDAKA,oDAAAP,SAAA,GAAAW,SAAAC,iBAAAC,MAAA,oEAMAR,SAAAE"}