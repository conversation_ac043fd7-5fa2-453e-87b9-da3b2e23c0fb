{"_args": [["asap@2.0.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "asap@2.0.5", "_id": "asap@2.0.5", "_inBundle": false, "_integrity": "sha1-UidltQw1EEkOUtfc/ghe+bqWlY8=", "_location": "/asap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "asap@2.0.5", "name": "asap", "escapedName": "asap", "rawSpec": "2.0.5", "saveSpec": null, "fetchSpec": "2.0.5"}, "_requiredBy": ["/promise"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/asap/-/asap-2.0.5.tgz", "_spec": "2.0.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "browser": {"./asap": "./browser-asap.js", "./asap.js": "./browser-asap.js", "./raw": "./browser-raw.js", "./raw.js": "./browser-raw.js", "./test/domain.js": "./test/browser-domain.js"}, "bugs": {"url": "https://github.com/kriskowal/asap/issues"}, "description": "High-priority task queue for Node.js and browsers", "devDependencies": {"benchmark": "^1.0.0", "events": "^1.0.1", "jshint": "^2.5.1", "knox": "^0.8.10", "mr": "^2.0.5", "opener": "^1.3.0", "q": "^2.0.3", "q-io": "^2.0.3", "saucelabs": "^0.1.1", "wd": "^0.2.21", "weak-map": "^1.0.5"}, "files": ["raw.js", "asap.js", "browser-raw.js", "browser-asap.js"], "homepage": "https://github.com/kriskowal/asap#readme", "keywords": ["event", "task", "queue"], "license": "MIT", "main": "./asap.js", "name": "asap", "repository": {"type": "git", "url": "git+https://github.com/kriskowal/asap.git"}, "scripts": {"benchmarks": "node benchmarks", "lint": "jshint raw.js asap.js browser-raw.js browser-asap.js $(find scripts -name '*.js' | grep -v gauntlet)", "test": "npm run lint && npm run test-node", "test-browser": "node scripts/publish-bundle.js test/asap-test.js | xargs opener", "test-node": "node test/asap-test.js", "test-publish": "node scripts/publish-bundle.js test/asap-test.js | pbcopy", "test-saucelabs": "node scripts/saucelabs.js test/asap-test.js scripts/saucelabs-spot-configurations.json", "test-saucelabs-all": "node scripts/saucelabs.js test/asap-test.js scripts/saucelabs-all-configurations.json", "test-saucelabs-worker": "node scripts/saucelabs-worker-test.js scripts/saucelabs-spot-configurations.json", "test-saucelabs-worker-all": "node scripts/saucelabs-worker-test.js scripts/saucelabs-all-configurations.json", "test-travis": "npm run lint && npm run test-node && npm run test-saucelabs && npm run test-saucelabs-worker"}, "version": "2.0.5"}