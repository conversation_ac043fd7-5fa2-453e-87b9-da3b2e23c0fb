{"authors": "<PERSON> <https://github.com/b<PERSON><PERSON><PERSON>/>, <PERSON> <https://github.com/theodorejb>, <PERSON> <https://github.com/david<PERSON><PERSON>/>", "definitionFilename": "index.d.ts", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": 2, "libraryMinorVersion": 5, "libraryName": "Jasmine 2.5", "typingsPackageName": "jasmine", "projectName": "http://jasmine.github.io/", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "Global", "globals": ["afterAll", "after<PERSON>ach", "beforeAll", "beforeEach", "describe", "expect", "fail", "fdescribe", "fit", "it", "jasmine", "pending", "runs", "spyOn", "waits", "waitsFor", "xdescribe", "xit"], "declaredModules": [], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "deae5b77cefabcb3a692eff958b0cfe55516a87b535b4247349dbe4fc5f835f6"}