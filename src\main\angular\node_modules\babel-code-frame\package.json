{"_args": [["babel-code-frame@6.22.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-code-frame@6.22.0", "_id": "babel-code-frame@6.22.0", "_inBundle": false, "_integrity": "sha1-AnYgvuVnqIwyVhV05/0IAdMxGOQ=", "_location": "/babel-code-frame", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-code-frame@6.22.0", "name": "babel-code-frame", "escapedName": "babel-code-frame", "rawSpec": "6.22.0", "saveSpec": null, "fetchSpec": "6.22.0"}, "_requiredBy": ["/babel-traverse", "/css-loader", "/tslint"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-code-frame/-/babel-code-frame-6.22.0.tgz", "_spec": "6.22.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"chalk": "^1.1.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "description": "Generate errors that contain a code frame that point to source locations.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-code-frame", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame"}, "version": "6.22.0"}