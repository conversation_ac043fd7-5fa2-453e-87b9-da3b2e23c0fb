{"title": "CSS3 Border images", "description": "Method of using images for borders", "spec": "https://www.w3.org/TR/css3-background/#border-images", "status": "cr", "links": [{"url": "https://www.webplatform.org/docs/css/properties/border-image", "title": "WebPlatform Docs"}, {"url": "https://developer.mozilla.org//docs/Web/CSS/border-image", "title": "Mozilla Developer Network (MDN) documentation - Border image"}], "bugs": [{"description": "Firefox is not able to stretch svg images across an element - [bug report](https://bugzilla.mozilla.org/show_bug.cgi?id=619500)."}, {"description": "WebKit browsers have a different rendering with the `round` value from other browsers, stretching the border rather than repeating it in certain cases [see bug](https://bugs.webkit.org/show_bug.cgi?id=155955)."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "y"}, "edge": {"12": "y #1", "13": "y #1", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "a x #2 #3", "3.6": "a x #2 #3", "4": "a x #2 #3", "5": "a x #2 #3", "6": "a x #2 #3", "7": "a x #2 #3", "8": "a x #2 #3", "9": "a x #2 #3", "10": "a x #2 #3", "11": "a x #2 #3", "12": "a x #2 #3", "13": "a x #2 #3", "14": "a x #2 #3", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "a #2", "37": "a #2", "38": "a #2", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a x #1 #2 #3 #4", "5": "a x #1 #2 #3 #4", "6": "a x #1 #2 #3 #4", "7": "a x #1 #2 #3 #4", "8": "a x #1 #2 #3 #4", "9": "a x #1 #2 #3 #4", "10": "a x #1 #2 #3 #4", "11": "a x #1 #2 #3 #4", "12": "a x #1 #2 #3 #4", "13": "a x #1 #2 #3 #4", "14": "a x #1 #2 #3 #4", "15": "a #1 #2 #4", "16": "a #1 #2 #4", "17": "a #1 #2 #4", "18": "a #1 #2 #4", "19": "a #1 #2 #4", "20": "a #1 #2 #4", "21": "a #1 #2 #4", "22": "a #1 #2 #4", "23": "a #1 #2 #4", "24": "a #1 #2 #4", "25": "a #1 #2 #4", "26": "a #1 #2 #4", "27": "a #1 #2 #4", "28": "a #1 #2 #4", "29": "a #1 #2 #4", "30": "a #1 #2", "31": "a #1 #2", "32": "a #1 #2", "33": "a #1 #2", "34": "a #1 #2", "35": "a #1 #2", "36": "a #1 #2", "37": "a #1 #2", "38": "a #1 #2", "39": "a #1 #2", "40": "a #1 #2", "41": "a #1 #2", "42": "a #1 #2", "43": "a #1 #2", "44": "a #1 #2", "45": "a #1 #2", "46": "a #1 #2", "47": "a #1 #2", "48": "a #1 #2", "49": "a #1 #2", "50": "a #1 #2", "51": "a #2", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2", "62": "a #2"}, "safari": {"3.1": "a x #1 #2 #3 #4", "3.2": "a x #1 #2 #3 #4", "4": "a x #1 #2 #3 #4", "5": "a x #1 #2 #3 #4", "5.1": "a x #1 #2 #3 #4", "6": "a #1 #2 #4", "6.1": "a #1 #2 #4", "7": "a #1 #2 #4", "7.1": "a #1 #2 #4", "8": "a #1 #2 #4", "9": "a #1 #2 #4", "9.1": "y #1", "10": "y #1", "10.1": "y #1", "11": "y #1", "TP": "y #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "a #2 #3 #4", "10.6": "a #2 #3 #4", "11": "a x #2 #3 #4", "11.1": "a x #2 #3 #4", "11.5": "a x #2 #3 #4", "11.6": "a x #2 #3 #4", "12": "a x #2 #3 #4", "12.1": "a x #2 #3 #4", "15": "a #1 #2", "16": "a #1 #2", "17": "a #1 #2", "18": "a #1 #2", "19": "a #1 #2", "20": "a #1 #2", "21": "a #1 #2", "22": "a #1 #2", "23": "a #1 #2", "24": "a #1 #2", "25": "a #1 #2", "26": "a #1 #2", "27": "a #1 #2", "28": "a #1 #2", "29": "a #1 #2", "30": "a #1 #2", "31": "a #1 #2", "32": "a #1 #2", "33": "a #1 #2", "34": "a #1 #2", "35": "a #1 #2", "36": "a #1 #2", "37": "a #1 #2", "38": "a #2", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2"}, "ios_saf": {"3.2": "a x #1 #2 #3 #4", "4.0-4.1": "a x #1 #2 #3 #4", "4.2-4.3": "a x #1 #2 #3 #4", "5.0-5.1": "a x #1 #2 #3 #4", "6.0-6.1": "a #1 #2 #4", "7.0-7.1": "a #1 #2 #4", "8": "a #1 #2 #4", "8.1-8.4": "a #1 #2 #4", "9.0-9.2": "a #1 #2 #4", "9.3": "y #1", "10.0-10.2": "y #1", "10.3": "y #1", "11": "y #1"}, "op_mini": {"all": "a x #2 #3 #4"}, "android": {"2.1": "a x #1 #2 #3 #4", "2.2": "a x #1 #2 #3 #4", "2.3": "a x #1 #2 #3 #4", "3": "a x #1 #2 #3 #4", "4": "a x #1 #2 #3 #4", "4.1": "a x #1 #2 #3 #4", "4.2-4.3": "a x #1 #2 #3 #4", "4.4": "a #1 #2", "4.4.3-4.4.4": "a #1 #2", "56": "a #1 #2"}, "bb": {"7": "a #1 #2 #3 #4", "10": "a #1 #2 #4"}, "op_mob": {"10": "n", "11": "a x #2 #3 #4", "11.1": "a x #2 #3 #4", "11.5": "a x #2 #3 #4", "12": "a x #2 #3 #4", "12.1": "a x #2 #3 #4", "37": "a #1 #2"}, "and_chr": {"59": "a #2"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.4": "a #1 #2"}, "samsung": {"4": "a #1 #2", "5": "a #2"}, "and_qq": {"1.2": "a #2"}, "baidu": {"7.12": "a #2"}}, "notes": "Note that both the `border-style` and `border-width` must be specified (not set to `none` or 0) for border-images to work.", "notes_by_num": {"1": "Has a bug where `border-image` incorrectly overrides `border-style`. See [test case](http://codepen.io/Savago/pen/yYrgyK), [WebKit bug](https://bugs.webkit.org/show_bug.cgi?id=99922), [discussion](https://github.com/whatwg/compat/issues/17)", "2": "Partial support refers to not supporting `border-image-repeat: space`", "3": "Partial support refers to supporting the shorthand syntax, but not the individual properties (`border-image-source`, `border-image-slice`, etc). ", "4": "Partial support refers to not supporting `border-image-repeat: round`"}, "usage_perc_y": 21.78, "usage_perc_a": 75.49, "ucprefix": false, "parent": "", "keywords": "border-image-source,border-image-slice,border-image-repeat,border-image-width,,border-image-outset", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}