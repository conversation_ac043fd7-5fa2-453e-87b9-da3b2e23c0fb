[{"__symbolic": "module", "version": 3, "metadata": {"Node": {"__symbolic": "interface"}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Expansion": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ExpansionCase"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ExpansionCase": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Attribute": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Element": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Attribute"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Comment": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Visitor": {"__symbolic": "interface"}, "visitAll": {"__symbolic": "function"}, "RecursiveVisitor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitElement": [{"__symbolic": "method"}], "visitAttribute": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitComment": [{"__symbolic": "method"}], "visitExpansion": [{"__symbolic": "method"}], "visitExpansionCase": [{"__symbolic": "method"}], "visitChildren": [{"__symbolic": "method"}]}}, "findNode": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"Node": {"__symbolic": "interface"}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Expansion": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ExpansionCase"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "ExpansionCase": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Attribute": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Element": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Attribute"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Comment": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Visitor": {"__symbolic": "interface"}, "visitAll": {"__symbolic": "function"}, "RecursiveVisitor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitElement": [{"__symbolic": "method"}], "visitAttribute": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitComment": [{"__symbolic": "method"}], "visitExpansion": [{"__symbolic": "method"}], "visitExpansionCase": [{"__symbolic": "method"}], "visitChildren": [{"__symbolic": "method"}]}}, "findNode": {"__symbolic": "function"}}}]