{"__symbolic": "module", "version": 3, "metadata": {"ɵa": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "JSONPBackend"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵg"}, {"__symbolic": "reference", "name": "ResponseOptions"}]}], "createConnection": [{"__symbolic": "method"}]}}, "ɵb": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "CookieXSRFStrategy"}}}, "ɵc": {"__symbolic": "function", "parameters": ["xhrBackend", "requestOptions"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Http"}, "arguments": [{"__symbolic": "reference", "name": "xhrBackend"}, {"__symbolic": "reference", "name": "requestOptions"}]}}, "ɵd": {"__symbolic": "function", "parameters": ["jsonpBackend", "requestOptions"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Jsonp"}, "arguments": [{"__symbolic": "reference", "name": "jsonpBackend"}, {"__symbolic": "reference", "name": "requestOptions"}]}}, "ɵe": {"__symbolic": "interface"}, "BrowserXhr": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "build": [{"__symbolic": "method"}]}}, "JSONPBackend": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ConnectionBackend"}, "members": {}}, "JSONPConnection": {"__symbolic": "class", "members": {"finished": [{"__symbolic": "method"}]}}, "CookieXSRFStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "configureRequest": [{"__symbolic": "method"}]}}, "XHRBackend": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "BrowserXhr"}, {"__symbolic": "reference", "name": "ResponseOptions"}, {"__symbolic": "reference", "name": "XSRFStrategy"}]}], "createConnection": [{"__symbolic": "method"}]}}, "XHRConnection": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Request"}, {"__symbolic": "reference", "name": "BrowserXhr"}, {"__symbolic": "reference", "name": "ResponseOptions"}]}], "setDetectedContentType": [{"__symbolic": "method"}]}}, "BaseRequestOptions": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "RequestOptions"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}}, "RequestOptions": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "RequestOptionsArgs"}]}], "merge": [{"__symbolic": "method"}], "_mergeSearchParams": [{"__symbolic": "method"}], "_parseParams": [{"__symbolic": "method"}], "_appendParam": [{"__symbolic": "method"}]}}, "BaseResponseOptions": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ResponseOptions"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}]}}, "ResponseOptions": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ResponseOptionsArgs"}]}], "merge": [{"__symbolic": "method"}]}}, "ReadyState": {"Unsent": 0, "Open": 1, "HeadersReceived": 2, "Loading": 3, "Done": 4, "Cancelled": 5}, "RequestMethod": {"Get": 0, "Post": 1, "Put": 2, "Delete": 3, "Options": 4, "Head": 5, "Patch": 6}, "ResponseContentType": {"Text": 0, "Json": 1, "ArrayBuffer": 2, "Blob": 3}, "ResponseType": {"Basic": 0, "Cors": 1, "Default": 2, "Error": 3, "Opaque": 4}, "Headers": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 43, "character": 32, "module": "./src/headers"}]}], "append": [{"__symbolic": "method"}], "delete": [{"__symbolic": "method"}], "forEach": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "has": [{"__symbolic": "method"}], "keys": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "values": [{"__symbolic": "method"}], "toJSON": [{"__symbolic": "method"}], "getAll": [{"__symbolic": "method"}], "entries": [{"__symbolic": "method"}], "mayBeSetNormalizedName": [{"__symbolic": "method"}]}}, "Http": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ConnectionBackend"}, {"__symbolic": "reference", "name": "RequestOptions"}]}], "request": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "post": [{"__symbolic": "method"}], "put": [{"__symbolic": "method"}], "delete": [{"__symbolic": "method"}], "patch": [{"__symbolic": "method"}], "head": [{"__symbolic": "method"}], "options": [{"__symbolic": "method"}]}}, "Jsonp": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Http"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ConnectionBackend"}, {"__symbolic": "reference", "name": "RequestOptions"}]}], "request": [{"__symbolic": "method"}]}}, "HttpModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"providers": [{"provide": {"__symbolic": "reference", "name": "Http"}, "useFactory": {"__symbolic": "reference", "name": "ɵc"}, "deps": [{"__symbolic": "reference", "name": "XHRBackend"}, {"__symbolic": "reference", "name": "RequestOptions"}]}, {"__symbolic": "reference", "name": "BrowserXhr"}, {"provide": {"__symbolic": "reference", "name": "RequestOptions"}, "useClass": {"__symbolic": "reference", "name": "BaseRequestOptions"}}, {"provide": {"__symbolic": "reference", "name": "ResponseOptions"}, "useClass": {"__symbolic": "reference", "name": "BaseResponseOptions"}}, {"__symbolic": "reference", "name": "XHRBackend"}, {"provide": {"__symbolic": "reference", "name": "XSRFStrategy"}, "useFactory": {"__symbolic": "reference", "name": "ɵb"}}]}]}], "members": {}}, "JsonpModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"providers": [{"provide": {"__symbolic": "reference", "name": "Jsonp"}, "useFactory": {"__symbolic": "reference", "name": "ɵd"}, "deps": [{"__symbolic": "reference", "name": "JSONPBackend"}, {"__symbolic": "reference", "name": "RequestOptions"}]}, {"__symbolic": "reference", "name": "ɵg"}, {"provide": {"__symbolic": "reference", "name": "RequestOptions"}, "useClass": {"__symbolic": "reference", "name": "BaseRequestOptions"}}, {"provide": {"__symbolic": "reference", "name": "ResponseOptions"}, "useClass": {"__symbolic": "reference", "name": "BaseResponseOptions"}}, {"provide": {"__symbolic": "reference", "name": "JSONPBackend"}, "useClass": {"__symbolic": "reference", "name": "ɵa"}}]}]}], "members": {}}, "Connection": {"__symbolic": "class", "members": {}}, "ConnectionBackend": {"__symbolic": "class", "members": {"createConnection": [{"__symbolic": "method"}]}}, "RequestOptionsArgs": {"__symbolic": "interface"}, "ResponseOptionsArgs": {"__symbolic": "interface"}, "XSRFStrategy": {"__symbolic": "class", "members": {"configureRequest": [{"__symbolic": "method"}]}}, "Request": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵf"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵe"}]}], "detectContentType": [{"__symbolic": "method"}], "detectContentTypeFromBody": [{"__symbolic": "method"}], "getBody": [{"__symbolic": "method"}]}}, "Response": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵf"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ResponseOptions"}]}], "toString": [{"__symbolic": "method"}]}}, "QueryEncoder": {"__symbolic": "class", "members": {"encodeKey": [{"__symbolic": "method"}], "encodeValue": [{"__symbolic": "method"}]}}, "URLSearchParams": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}], "clone": [{"__symbolic": "method"}], "has": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "getAll": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "setAll": [{"__symbolic": "method"}], "append": [{"__symbolic": "method"}], "appendAll": [{"__symbolic": "method"}], "replaceAll": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}], "delete": [{"__symbolic": "method"}]}}, "VERSION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Version"}, "arguments": ["4.2.5"]}, "ɵf": {"__symbolic": "class", "members": {"json": [{"__symbolic": "method"}], "text": [{"__symbolic": "method"}], "arrayBuffer": [{"__symbolic": "method"}], "blob": [{"__symbolic": "method"}]}}, "ɵg": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"build": [{"__symbolic": "method"}], "nextRequestID": [{"__symbolic": "method"}], "requestCallback": [{"__symbolic": "method"}], "exposeConnection": [{"__symbolic": "method"}], "removeConnection": [{"__symbolic": "method"}], "send": [{"__symbolic": "method"}], "cleanup": [{"__symbolic": "method"}]}}}, "origins": {"ɵa": "./src/backends/jsonp_backend", "ɵb": "./src/http_module", "ɵc": "./src/http_module", "ɵd": "./src/http_module", "ɵe": "./src/interfaces", "BrowserXhr": "./src/backends/browser_xhr", "JSONPBackend": "./src/backends/jsonp_backend", "JSONPConnection": "./src/backends/jsonp_backend", "CookieXSRFStrategy": "./src/backends/xhr_backend", "XHRBackend": "./src/backends/xhr_backend", "XHRConnection": "./src/backends/xhr_backend", "BaseRequestOptions": "./src/base_request_options", "RequestOptions": "./src/base_request_options", "BaseResponseOptions": "./src/base_response_options", "ResponseOptions": "./src/base_response_options", "ReadyState": "./src/enums", "RequestMethod": "./src/enums", "ResponseContentType": "./src/enums", "ResponseType": "./src/enums", "Headers": "./src/headers", "Http": "./src/http", "Jsonp": "./src/http", "HttpModule": "./src/http_module", "JsonpModule": "./src/http_module", "Connection": "./src/interfaces", "ConnectionBackend": "./src/interfaces", "RequestOptionsArgs": "./src/interfaces", "ResponseOptionsArgs": "./src/interfaces", "XSRFStrategy": "./src/interfaces", "Request": "./src/static_request", "Response": "./src/static_response", "QueryEncoder": "./src/url_search_params", "URLSearchParams": "./src/url_search_params", "VERSION": "./src/version", "ɵf": "./src/body", "ɵg": "./src/backends/browser_jsonp"}, "importAs": "@angular/http"}