export declare class RoundProgressEase {
    linearEase(t: number, b: number, c: number, d: number): number;
    easeInQuad(t: number, b: number, c: number, d: number): number;
    easeOutQuad(t: number, b: number, c: number, d: number): number;
    easeInOutQuad(t: number, b: number, c: number, d: number): number;
    easeInCubic(t: number, b: number, c: number, d: number): number;
    easeOutCubic(t: number, b: number, c: number, d: number): number;
    easeInOutCubic(t: number, b: number, c: number, d: number): number;
    easeInQuart(t: number, b: number, c: number, d: number): number;
    easeOutQuart(t: number, b: number, c: number, d: number): number;
    easeInOutQuart(t: number, b: number, c: number, d: number): number;
    easeInQuint(t: number, b: number, c: number, d: number): number;
    easeOutQuint(t: number, b: number, c: number, d: number): number;
    easeInOutQuint(t: number, b: number, c: number, d: number): number;
    easeInSine(t: number, b: number, c: number, d: number): number;
    easeOutSine(t: number, b: number, c: number, d: number): number;
    easeInOutSine(t: number, b: number, c: number, d: number): number;
    easeInExpo(t: number, b: number, c: number, d: number): number;
    easeOutExpo(t: number, b: number, c: number, d: number): number;
    easeInOutExpo(t: number, b: number, c: number, d: number): number;
    easeInCirc(t: number, b: number, c: number, d: number): number;
    easeOutCirc(t: number, b: number, c: number, d: number): number;
    easeInOutCirc(t: number, b: number, c: number, d: number): number;
    easeInElastic(t: number, b: number, c: number, d: number): number;
    easeOutElastic(t: number, b: number, c: number, d: number): number;
    easeInOutElastic(t: number, b: number, c: number, d: number): number;
    easeInBack(t: number, b: number, c: number, d: number, s?: number): number;
    easeOutBack(t: number, b: number, c: number, d: number, s?: number): number;
    easeInOutBack(t: number, b: number, c: number, d: number, s?: number): number;
    easeInBounce(t: number, b: number, c: number, d: number): number;
    easeOutBounce(t: number, b: number, c: number, d: number): number;
    easeInOutBounce(t: number, b: number, c: number, d: number): number;
}
