/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/compiler"),require("@angular/core"),require("@angular/common"),require("@angular/platform-browser")):"function"==typeof define&&define.amd?define(["exports","@angular/compiler","@angular/core","@angular/common","@angular/platform-browser"],factory):factory((global.ng=global.ng||{},global.ng.platformBrowserDynamic=global.ng.platformBrowserDynamic||{}),global.ng.compiler,global.ng.core,global.ng.common,global.ng.platformBrowser)}(this,function(exports,_angular_compiler,_angular_core,_angular_common,_angular_platformBrowser){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},ResourceLoaderImpl=function(_super){function ResourceLoaderImpl(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(ResourceLoaderImpl,_super),ResourceLoaderImpl.prototype.get=function(url){var resolve,reject,promise=new Promise(function(res,rej){resolve=res,reject=rej}),xhr=new XMLHttpRequest;return xhr.open("GET",url,!0),xhr.responseType="text",xhr.onload=function(){var response=xhr.response||xhr.responseText,status=1223===xhr.status?204:xhr.status;0===status&&(status=response?200:0),200<=status&&status<=300?resolve(response):reject("Failed to load "+url)},xhr.onerror=function(){reject("Failed to load "+url)},xhr.send(),promise},ResourceLoaderImpl}(_angular_compiler.ResourceLoader);ResourceLoaderImpl.decorators=[{type:_angular_core.Injectable}],ResourceLoaderImpl.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS=[_angular_platformBrowser.ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,{provide:_angular_core.COMPILER_OPTIONS,useValue:{providers:[{provide:_angular_compiler.ResourceLoader,useClass:ResourceLoaderImpl}]},multi:!0},{provide:_angular_core.PLATFORM_ID,useValue:_angular_common.ɵPLATFORM_BROWSER_ID}],CachedResourceLoader=function(_super){function CachedResourceLoader(){var _this=_super.call(this)||this;if(_this._cache=_angular_core.ɵglobal.$templateCache,null==_this._cache)throw new Error("CachedResourceLoader: Template cache was not found in $templateCache.");return _this}return __extends(CachedResourceLoader,_super),CachedResourceLoader.prototype.get=function(url){return this._cache.hasOwnProperty(url)?Promise.resolve(this._cache[url]):Promise.reject("CachedResourceLoader: Did not find cached template for "+url)},CachedResourceLoader}(_angular_compiler.ResourceLoader),VERSION=new _angular_core.Version("4.2.5"),RESOURCE_CACHE_PROVIDER=[{provide:_angular_compiler.ResourceLoader,useClass:CachedResourceLoader}],platformBrowserDynamic=_angular_core.createPlatformFactory(_angular_compiler.platformCoreDynamic,"browserDynamic",INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);exports.RESOURCE_CACHE_PROVIDER=RESOURCE_CACHE_PROVIDER,exports.platformBrowserDynamic=platformBrowserDynamic,exports.VERSION=VERSION,exports.ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS=INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS,exports.ɵResourceLoaderImpl=ResourceLoaderImpl,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=platform-browser-dynamic.umd.min.js.map
