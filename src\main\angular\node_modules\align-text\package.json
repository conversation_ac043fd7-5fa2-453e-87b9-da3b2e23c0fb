{"_args": [["align-text@0.1.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "align-text@0.1.4", "_id": "align-text@0.1.4", "_inBundle": false, "_integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "_location": "/align-text", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "align-text@0.1.4", "name": "align-text", "escapedName": "align-text", "rawSpec": "0.1.4", "saveSpec": null, "fetchSpec": "0.1.4"}, "_requiredBy": ["/center-align", "/right-align"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/align-text/-/align-text-0.1.4.tgz", "_spec": "0.1.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/align-text/issues"}, "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "description": "Align the text in a string.", "devDependencies": {"mocha": "*", "should": "*", "word-wrap": "^1.0.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/align-text", "keywords": ["align", "align-center", "alignment", "center", "center-align", "indent", "pad", "padding", "right", "right-align", "text", "typography"], "license": "MIT", "main": "index.js", "name": "align-text", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/align-text.git"}, "scripts": {"test": "mocha"}, "version": "0.1.4"}