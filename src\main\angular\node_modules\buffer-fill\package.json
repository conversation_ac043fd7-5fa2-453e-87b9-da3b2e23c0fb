{"_args": [["buffer-fill@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-fill@1.0.0", "_id": "buffer-fill@1.0.0", "_inBundle": false, "_integrity": "sha1-+PeLdniYiO858gXNY39o5wISKyw=", "_location": "/buffer-fill", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-fill@1.0.0", "name": "buffer-fill", "escapedName": "buffer-fill", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/buffer-alloc"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-fill/-/buffer-fill-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/LinusU/buffer-fill/issues"}, "description": "A [ponyfill](https://ponyfill.com) for `Buffer.fill`.", "devDependencies": {"buffer-alloc-unsafe": "^1.1.0", "standard": "^7.1.2"}, "files": ["index.js"], "homepage": "https://github.com/LinusU/buffer-fill#readme", "license": "MIT", "name": "buffer-fill", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-fill.git"}, "scripts": {"test": "standard && node test"}, "version": "1.0.0"}