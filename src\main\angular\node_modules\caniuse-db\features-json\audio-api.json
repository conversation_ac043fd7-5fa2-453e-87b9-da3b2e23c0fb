{"title": "Web Audio API", "description": "High-level JavaScript API for processing and synthesizing audio", "spec": "http://www.w3.org/TR/webaudio/", "status": "wd", "links": [{"url": "https://github.com/corbanbrook/audionode.js", "title": "Polyfill to support Web Audio API in Firefox"}, {"url": "https://www.webplatform.org/docs/apis/webaudio", "title": "WebPlatform Docs"}, {"url": "http://www.doboism.com/projects/webaudio-compatibility/", "title": "Additional browser compatibility tests for specific features"}, {"url": "https://github.com/g200kg/WAAPISim", "title": "Polyfill to enable Web Audio API through Firefox Audio Data api or flash"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API", "title": "Mozilla Developer Network (MDN) documentation - Web Audio API"}], "bugs": [{"description": "Safari does not appear to support `createMediaElementSource`"}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y x", "11": "y x", "12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "y x", "7.0-7.1": "y x", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y x", "9.3": "y x", "10.0-10.2": "y x", "10.3": "y x", "11": "y x"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Not all browsers with support for the Audio API also support media streams (e.g. microphone input). See the [getUserMedia/Streams API](/#feat=stream) data for support for that feature.\r\n\r\nFirefox versions < 25 support an alternative, deprecated audio API.\r\n\r\nChrome support [went through some changes](http://updates.html5rocks.com/2014/07/Web-Audio-Changes-in-m36) as of version 36.", "notes_by_num": {}, "usage_perc_y": 78.97, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "web-audio,webaudio,AudioContext,AudioBuffer,AudioNode", "ie_id": "webaudioapi", "chrome_id": "6261718720184320", "firefox_id": "webaudio", "webkit_id": "feature-web-audio", "shown": true}