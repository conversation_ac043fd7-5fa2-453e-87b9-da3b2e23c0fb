{"title": "CSS font-feature-settings", "description": "Method of applying advanced typographic and language-specific font features to supported OpenType fonts.", "spec": "http://w3.org/TR/css3-fonts/#font-rend-props", "status": "cr", "links": [{"url": "http://ie.microsoft.com/testdrive/Graphics/opentype/", "title": "Demo pages (IE/Firefox only)"}, {"url": "http://hacks.mozilla.org/2010/11/firefox-4-font-feature-support/", "title": "Mozilla hacks article"}, {"url": "http://html5accessibility.com/", "title": "Detailed tables on accessability support"}, {"url": "https://www.webplatform.org/docs/css/properties/font-feature-settings", "title": "WebPlatform Docs"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/font-feature-settings", "title": "Mozilla Developer Network (MDN) documentation - font-feature-settings"}, {"url": "https://www.microsoft.com/typography/otspec/featuretags.htm", "title": "OpenType layout feature tag registry"}, {"url": "http://help.typekit.com/customer/portal/articles/1789736-syntax-for-opentype-features-in-css#salt", "title": "Syntax for OpenType features in CSS (Adobe Typekit Help)"}], "bugs": [{"description": "IE10 and 11 do not always appear to support the `ss01` value correctly."}, {"description": "IE10 and 11 on Windows 7 [can hide the text](http://stackoverflow.com/questions/22151835/msie-10-web-font-and-font-feature-settings-causes-invisible-text) under certain circumstances."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "a x #2", "17": "a x #2", "18": "a x #2", "19": "a x #2", "20": "a x #2", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y x", "4.4.3-4.4.4": "y x", "56": "y"}, "bb": {"7": "n", "10": "y x"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y x"}, "samsung": {"4": "y x", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Whenever possible, font-variant shorthand property or an associated longhand property, font-variant-ligatures, font-variant-caps, font-variant-east-asian, font-variant-alternates, font-variant-numeric or font-variant-position should be used. This property is a low-level feature designed to handle special cases where no other way to enable or access an OpenType font feature exists. In particular, this CSS property shouldn't be used to enable small caps.", "notes_by_num": {"1": "From Gecko 2.0 (Firefox 4.0) to Gecko 14.0 (Firefox 14.0) included, Gecko supported an older syntax, slightly different from the modern one: http://hacks.mozilla.org/2010/11/firefox-4-font-feature-support/", "2": "Partial support in older Chrome versions refers to lacking support in Mac OS X."}, "usage_perc_y": 92.81, "usage_perc_a": 0.19, "ucprefix": false, "parent": "", "keywords": "font-feature,font-feature-settings,kern,kerning,font-variant-alternates,ligatures,font-variant-ligatures", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}