{"version": 3, "file": "schema.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/schema.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,kBAAkB;AAElB,2FAA2F;AAE3F,kGAAkG;AAClG,+FAA+F;AAC/F,iGAAiG;AACjG,4FAA4F;AAE/E,QAAA,OAAO,GAAG,CAAC,CAAC;AAYzB,0BAAiC,KAAU;IACzC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;AAChD,CAAC;AAFD,4CAEC;AAeD,yBAAgC,KAAU;IACxC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC;AAC/C,CAAC;AAFD,0CAEC;AAGD,6BAAoC,KAAU;IAC5C,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,WAAW,CAAC;AACnD,CAAC;AAFD,kDAEC;AAQD,0BAAiC,KAAU;IACzC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACV,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;YACzB,KAAK,aAAa,CAAC;YACnB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,MAAM,CAAC,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IACD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAVD,4CAUC;AAMD,0BAAiC,KAAU;IACzC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,aAAa,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC;AACxF,CAAC;AAFD,4CAEC;AAMD,+BAAsC,KAAU;IAC9C,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,aAAa,CAAC;AACrD,CAAC;AAFD,sDAEC;AAQD,4BAAmC,KAAU;IAC3C,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,CAAC;AAClD,CAAC;AAFD,gDAEC;AAYD,sCAA6C,KAAU;IACrD,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACV,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;YACzB,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO,CAAC;YACb,KAAK,KAAK,CAAC;YACX,KAAK,KAAK,CAAC;YACX,KAAK,WAAW,CAAC;YACjB,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ,CAAC;YACd,KAAK,IAAI;gBACP,MAAM,CAAC,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IACD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAhBD,oEAgBC;AASD,4CAAmD,KAAU;IAE3D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;AAChD,CAAC;AAHD,gFAGC;AAOD,2CAAkD,KAAU;IAE1D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC;AAC/C,CAAC;AAHD,8EAGC;AAOD,0CAAiD,KAAU;IAEzD,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,CAAC;AAC9E,CAAC;AAHD,4EAGC;AAOD,4CAAmD,KAAU;IAE3D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC;AAC7C,CAAC;AAHD,gFAGC;AAQD,wCAA+C,KAAU;IACvD,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,CAAC;AAC5C,CAAC;AAFD,wEAEC;AAOD,6CAAoD,KAAU;IAE5D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,qCAAqC,CAAC,KAAK,CAAC,CAAC;AAC9F,CAAC;AAHD,kFAGC;AAMD,6CAAoD,KAAU;IAE5D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;QACzD,qCAAqC,CAAC,KAAK,CAAC,CAAC;AACnD,CAAC;AAJD,kFAIC;AAQD,qDAA4D,KAAU;IAEpE,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,qCAAqC,CAAC,KAAK,CAAC,CAAC;AAC/F,CAAC;AAHD,kGAGC;AASD,0CAAiD,KAAU;IAEzD,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC,KAAK,CAAC,CAAC;AACvF,CAAC;AAHD,4EAGC;AAKD,+CAAsD,KAAU;IAE9D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,WAAW,CAAC;AACnD,CAAC;AAHD,sFAGC;AAOD,4CAAmD,KAAU;IAE3D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;AAChD,CAAC;AAHD,gFAGC;AAMD,4CAAmD,KAAU;IAE3D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;AAChD,CAAC;AAHD,gFAGC;AAkCD,yBAAgC,KAAU;IACxC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC;AAC/C,CAAC;AAFD,0CAEC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Metadata Schema\n\n// If you make a backwards incompatible change to the schema, increment the VERSION number.\n\n// If you make a backwards compatible change to the metadata (such as adding an option field) then\n// leave VERSION the same. If possible, as many versions of the metadata that can represent the\n// semantics of the file in an array. For example, when generating a version 2 file, if version 1\n// can accurately represent the metadata, generate both version 1 and version 2 in an array.\n\nexport const VERSION = 3;\n\nexport type MetadataEntry = ClassMetadata | InterfaceMetadata | FunctionMetadata | MetadataValue;\n\nexport interface ModuleMetadata {\n  __symbolic: 'module';\n  version: number;\n  exports?: ModuleExportMetadata[];\n  importAs?: string;\n  metadata: {[name: string]: MetadataEntry};\n  origins?: {[name: string]: string};\n}\nexport function isModuleMetadata(value: any): value is ModuleMetadata {\n  return value && value.__symbolic === 'module';\n}\n\nexport interface ModuleExportMetadata {\n  export?: (string|{name: string, as: string})[];\n  from: string;\n}\n\nexport interface ClassMetadata {\n  __symbolic: 'class';\n  extends?: MetadataSymbolicExpression|MetadataError;\n  arity?: number;\n  decorators?: (MetadataSymbolicExpression|MetadataError)[];\n  members?: MetadataMap;\n  statics?: {[name: string]: MetadataValue | FunctionMetadata};\n}\nexport function isClassMetadata(value: any): value is ClassMetadata {\n  return value && value.__symbolic === 'class';\n}\n\nexport interface InterfaceMetadata { __symbolic: 'interface'; }\nexport function isInterfaceMetadata(value: any): value is InterfaceMetadata {\n  return value && value.__symbolic === 'interface';\n}\n\nexport interface MetadataMap { [name: string]: MemberMetadata[]; }\n\nexport interface MemberMetadata {\n  __symbolic: 'constructor'|'method'|'property';\n  decorators?: (MetadataSymbolicExpression|MetadataError)[];\n}\nexport function isMemberMetadata(value: any): value is MemberMetadata {\n  if (value) {\n    switch (value.__symbolic) {\n      case 'constructor':\n      case 'method':\n      case 'property':\n        return true;\n    }\n  }\n  return false;\n}\n\nexport interface MethodMetadata extends MemberMetadata {\n  __symbolic: 'constructor'|'method';\n  parameterDecorators?: (MetadataSymbolicExpression|MetadataError)[][];\n}\nexport function isMethodMetadata(value: any): value is MethodMetadata {\n  return value && (value.__symbolic === 'constructor' || value.__symbolic === 'method');\n}\n\nexport interface ConstructorMetadata extends MethodMetadata {\n  __symbolic: 'constructor';\n  parameters?: (MetadataSymbolicExpression|MetadataError|null)[];\n}\nexport function isConstructorMetadata(value: any): value is ConstructorMetadata {\n  return value && value.__symbolic === 'constructor';\n}\n\nexport interface FunctionMetadata {\n  __symbolic: 'function';\n  parameters: string[];\n  defaults?: MetadataValue[];\n  value: MetadataValue;\n}\nexport function isFunctionMetadata(value: any): value is FunctionMetadata {\n  return value && value.__symbolic === 'function';\n}\n\nexport type MetadataValue = string | number | boolean | MetadataObject | MetadataArray |\n    MetadataSymbolicExpression | MetadataError;\n\nexport interface MetadataObject { [name: string]: MetadataValue; }\n\nexport interface MetadataArray { [name: number]: MetadataValue; }\n\nexport interface MetadataSymbolicExpression {\n  __symbolic: 'binary'|'call'|'index'|'new'|'pre'|'reference'|'select'|'spread'|'if';\n}\nexport function isMetadataSymbolicExpression(value: any): value is MetadataSymbolicExpression {\n  if (value) {\n    switch (value.__symbolic) {\n      case 'binary':\n      case 'call':\n      case 'index':\n      case 'new':\n      case 'pre':\n      case 'reference':\n      case 'select':\n      case 'spread':\n      case 'if':\n        return true;\n    }\n  }\n  return false;\n}\n\nexport interface MetadataSymbolicBinaryExpression extends MetadataSymbolicExpression {\n  __symbolic: 'binary';\n  operator: '&&'|'||'|'|'|'^'|'&'|'=='|'!='|'==='|'!=='|'<'|'>'|'<='|'>='|'instanceof'|'in'|'as'|\n      '<<'|'>>'|'>>>'|'+'|'-'|'*'|'/'|'%'|'**';\n  left: MetadataValue;\n  right: MetadataValue;\n}\nexport function isMetadataSymbolicBinaryExpression(value: any):\n    value is MetadataSymbolicBinaryExpression {\n  return value && value.__symbolic === 'binary';\n}\n\nexport interface MetadataSymbolicIndexExpression extends MetadataSymbolicExpression {\n  __symbolic: 'index';\n  expression: MetadataValue;\n  index: MetadataValue;\n}\nexport function isMetadataSymbolicIndexExpression(value: any):\n    value is MetadataSymbolicIndexExpression {\n  return value && value.__symbolic === 'index';\n}\n\nexport interface MetadataSymbolicCallExpression extends MetadataSymbolicExpression {\n  __symbolic: 'call'|'new';\n  expression: MetadataValue;\n  arguments?: MetadataValue[];\n}\nexport function isMetadataSymbolicCallExpression(value: any):\n    value is MetadataSymbolicCallExpression {\n  return value && (value.__symbolic === 'call' || value.__symbolic === 'new');\n}\n\nexport interface MetadataSymbolicPrefixExpression extends MetadataSymbolicExpression {\n  __symbolic: 'pre';\n  operator: '+'|'-'|'~'|'!';\n  operand: MetadataValue;\n}\nexport function isMetadataSymbolicPrefixExpression(value: any):\n    value is MetadataSymbolicPrefixExpression {\n  return value && value.__symbolic === 'pre';\n}\n\nexport interface MetadataSymbolicIfExpression extends MetadataSymbolicExpression {\n  __symbolic: 'if';\n  condition: MetadataValue;\n  thenExpression: MetadataValue;\n  elseExpression: MetadataValue;\n}\nexport function isMetadataSymbolicIfExpression(value: any): value is MetadataSymbolicIfExpression {\n  return value && value.__symbolic === 'if';\n}\n\nexport interface MetadataGlobalReferenceExpression extends MetadataSymbolicExpression {\n  __symbolic: 'reference';\n  name: string;\n  arguments?: MetadataValue[];\n}\nexport function isMetadataGlobalReferenceExpression(value: any):\n    value is MetadataGlobalReferenceExpression {\n  return value && value.name && !value.module && isMetadataSymbolicReferenceExpression(value);\n}\n\nexport interface MetadataModuleReferenceExpression extends MetadataSymbolicExpression {\n  __symbolic: 'reference';\n  module: string;\n}\nexport function isMetadataModuleReferenceExpression(value: any):\n    value is MetadataModuleReferenceExpression {\n  return value && value.module && !value.name && !value.default &&\n      isMetadataSymbolicReferenceExpression(value);\n}\n\nexport interface MetadataImportedSymbolReferenceExpression extends MetadataSymbolicExpression {\n  __symbolic: 'reference';\n  module: string;\n  name: string;\n  arguments?: MetadataValue[];\n}\nexport function isMetadataImportedSymbolReferenceExpression(value: any):\n    value is MetadataImportedSymbolReferenceExpression {\n  return value && value.module && !!value.name && isMetadataSymbolicReferenceExpression(value);\n}\n\nexport interface MetadataImportedDefaultReferenceExpression extends MetadataSymbolicExpression {\n  __symbolic: 'reference';\n  module: string;\n  default:\n    boolean;\n    arguments?: MetadataValue[];\n}\nexport function isMetadataImportDefaultReference(value: any):\n    value is MetadataImportedDefaultReferenceExpression {\n  return value.module && value.default && isMetadataSymbolicReferenceExpression(value);\n}\n\nexport type MetadataSymbolicReferenceExpression = MetadataGlobalReferenceExpression |\n    MetadataModuleReferenceExpression | MetadataImportedSymbolReferenceExpression |\n    MetadataImportedDefaultReferenceExpression;\nexport function isMetadataSymbolicReferenceExpression(value: any):\n    value is MetadataSymbolicReferenceExpression {\n  return value && value.__symbolic === 'reference';\n}\n\nexport interface MetadataSymbolicSelectExpression extends MetadataSymbolicExpression {\n  __symbolic: 'select';\n  expression: MetadataValue;\n  name: string;\n}\nexport function isMetadataSymbolicSelectExpression(value: any):\n    value is MetadataSymbolicSelectExpression {\n  return value && value.__symbolic === 'select';\n}\n\nexport interface MetadataSymbolicSpreadExpression extends MetadataSymbolicExpression {\n  __symbolic: 'spread';\n  expression: MetadataValue;\n}\nexport function isMetadataSymbolicSpreadExpression(value: any):\n    value is MetadataSymbolicSpreadExpression {\n  return value && value.__symbolic === 'spread';\n}\n\nexport interface MetadataError {\n  __symbolic: 'error';\n\n  /**\n   * This message should be short and relatively discriptive and should be fixed once it is created.\n   * If the reader doesn't recognize the message, it will display the message unmodified. If the\n   * reader recognizes the error message is it free to use substitute message the is more\n   * descriptive and/or localized.\n   */\n  message: string;\n\n  /**\n   * The line number of the error in the .ts file the metadata was created for.\n   */\n  line?: number;\n\n  /**\n   * The number of utf8 code-units from the beginning of the file of the error.\n   */\n  character?: number;\n\n  /**\n   * The module of the error (only used in bundled metadata)\n   */\n  module?: string;\n\n  /**\n   * Context information that can be used to generate a more descriptive error message. The content\n   * of the context is dependent on the error message.\n   */\n  context?: {[name: string]: string};\n}\nexport function isMetadataError(value: any): value is MetadataError {\n  return value && value.__symbolic === 'error';\n}\n"]}