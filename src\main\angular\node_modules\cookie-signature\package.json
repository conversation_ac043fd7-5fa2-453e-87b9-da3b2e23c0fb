{"_args": [["cookie-signature@1.0.6", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cookie-signature@1.0.6", "_id": "cookie-signature@1.0.6", "_inBundle": false, "_integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "_location": "/cookie-signature", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie-signature@1.0.6", "name": "cookie-signature", "escapedName": "cookie-signature", "rawSpec": "1.0.6", "saveSpec": null, "fetchSpec": "1.0.6"}, "_requiredBy": ["/express"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cookie-signature/-/cookie-signature-1.0.6.tgz", "_spec": "1.0.6", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dependencies": {}, "description": "Sign and unsign cookies", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "keywords": ["cookie", "sign", "unsign"], "license": "MIT", "main": "index", "name": "cookie-signature", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "scripts": {"test": "mocha --require should --reporter spec"}, "version": "1.0.6"}