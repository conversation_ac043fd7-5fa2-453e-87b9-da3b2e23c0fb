{"_args": [["browserify-zlib@0.1.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-zlib@0.1.4", "_id": "browserify-zlib@0.1.4", "_inBundle": false, "_integrity": "sha1-uzX4pRn2AOD6a4SFJByXnQFB+y0=", "_location": "/browserify-zlib", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-zlib@0.1.4", "name": "browserify-zlib", "escapedName": "browserify-zlib", "rawSpec": "0.1.4", "saveSpec": null, "fetchSpec": "0.1.4"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-zlib/-/browserify-zlib-0.1.4.tgz", "_spec": "0.1.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "Devon Govett", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/devongovett/browserify-zlib/issues"}, "dependencies": {"pako": "~0.2.0"}, "description": "Full zlib module for browserify", "devDependencies": {"brfs": "^1.0.1", "tape": "^2.12.3"}, "directories": {"test": "test"}, "homepage": "https://github.com/devongovett/browserify-zlib#readme", "keywords": ["zlib", "browserify"], "license": "MIT", "main": "src/index.js", "name": "browserify-zlib", "repository": {"type": "git", "url": "git://github.com/devongovett/browserify-zlib.git"}, "scripts": {"test": "node_modules/tape/bin/tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "version": "0.1.4"}