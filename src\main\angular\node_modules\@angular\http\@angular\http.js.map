{"version": 3, "file": "http.js", "sources": ["../../../../packages/http/index.ts", "../../../../packages/http/public_api.ts", "../../../../packages/http/src/index.ts", "../../../../packages/http/src/version.ts", "../../../../packages/http/src/http_module.ts", "../../../../packages/http/src/http.ts", "../../../../packages/http/src/static_request.ts", "../../../../packages/http/src/base_request_options.ts", "../../../../packages/http/src/backends/xhr_backend.ts", "../../../../packages/http/src/backends/jsonp_backend.ts", "../../../../packages/http/src/backends/browser_jsonp.ts", "../../../../packages/http/src/static_response.ts", "../../../../packages/http/src/body.ts", "../../../../packages/http/src/url_search_params.ts", "../../../../packages/http/src/http_utils.ts", "../../../../packages/http/src/interfaces.ts", "../../../../packages/http/src/base_response_options.ts", "../../../../packages/http/src/headers.ts", "../../../../packages/http/src/enums.ts", "../../../../packages/http/src/backends/browser_xhr.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {<PERSON><PERSON>er<PERSON><PERSON>,JSONPBackend,JSONPConnection,CookieXSRFStrategy,XHRBackend,XHRConnection,BaseRequestOptions,RequestOptions,BaseResponseOptions,ResponseOptions,ReadyState,RequestMethod,ResponseContentType,ResponseType,Headers,Http,Jsonp,HttpModule,JsonpModule,Connection,ConnectionBackend,RequestOptionsArgs,ResponseOptionsArgs,XSRFStrategy,Request,Response,QueryEncoder,URLSearchParams,VERSION} from './public_api';\n\nexport {BrowserJsonp as ɵg} from './src/backends/browser_jsonp';\nexport {JSONPBackend_ as ɵa} from './src/backends/jsonp_backend';\nexport {Body as ɵf} from './src/body';\nexport {_createDefaultCookieXSRFStrategy as ɵb,httpFactory as ɵc,jsonpFactory as ɵd} from './src/http_module';\nexport {RequestArgs as ɵe} from './src/interfaces';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the http package.\n */\nexport {BrowserXhr,JSONPBackend,JSONPConnection,CookieXSRFStrategy,XHRBackend,XHRConnection,BaseRequestOptions,RequestOptions,BaseResponseOptions,ResponseOptions,ReadyState,RequestMethod,ResponseContentType,ResponseType,Headers,Http,Jsonp,HttpModule,JsonpModule,Connection,ConnectionBackend,RequestOptionsArgs,ResponseOptionsArgs,XSRFStrategy,Request,Response,QueryEncoder,URLSearchParams,VERSION} from './src/index';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BrowserXhr} from './backends/browser_xhr';\nexport {JSONPBackend, JSONPConnection} from './backends/jsonp_backend';\nexport {CookieXSRFStrategy, XHRBackend, XHRConnection} from './backends/xhr_backend';\nexport {BaseRequestOptions, RequestOptions} from './base_request_options';\nexport {BaseResponseOptions, ResponseOptions} from './base_response_options';\nexport {ReadyState, RequestMethod, ResponseContentType, ResponseType} from './enums';\nexport {Headers} from './headers';\nexport {Http, Jsonp} from './http';\nexport {HttpModule, JsonpModule} from './http_module';\nexport {Connection, ConnectionBackend, RequestOptionsArgs, ResponseOptionsArgs, XSRFStrategy} from './interfaces';\nexport {Request} from './static_request';\nexport {Response} from './static_response';\nexport {QueryEncoder, URLSearchParams} from './url_search_params';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * The http module provides services to perform http requests. To get started, see the {@link Http}\n * class.\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {BrowserJsonp} from './backends/browser_jsonp';\nimport {BrowserXhr} from './backends/browser_xhr';\nimport {JSONPBackend, JSONPBackend_} from './backends/jsonp_backend';\nimport {CookieXSRFStrategy, XHRBackend} from './backends/xhr_backend';\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {BaseResponseOptions, ResponseOptions} from './base_response_options';\nimport {Http, Jsonp} from './http';\nimport {XSRFStrategy} from './interfaces';\n/**\n * @return {?}\n */\nexport function _createDefaultCookieXSRFStrategy() {\n  return new CookieXSRFStrategy();\n}\n/**\n * @param {?} xhrBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function httpFactory(xhrBackend: XHRBackend, requestOptions: RequestOptions): Http {\n  return new Http(xhrBackend, requestOptions);\n}\n/**\n * @param {?} jsonpBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function jsonpFactory(jsonpBackend: JSONPBackend, requestOptions: RequestOptions): Jsonp {\n  return new Jsonp(jsonpBackend, requestOptions);\n}\n/**\n * The module that includes http's providers\n * \n * \\@experimental\n */\nexport class HttpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Http, useFactory: httpFactory, deps: [XHRBackend, RequestOptions]},\n    BrowserXhr,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    XHRBackend,\n    {provide: XSRFStrategy, useFactory: _createDefaultCookieXSRFStrategy},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HttpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nHttpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttpModule.ctorParameters;\n}\n\n/**\n * The module that includes jsonp's providers\n * \n * \\@experimental\n */\nexport class JsonpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Jsonp, useFactory: jsonpFactory, deps: [JSONPBackend, RequestOptions]},\n    BrowserJsonp,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    {provide: JSONPBackend, useClass: JSONPBackend_},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction JsonpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonpModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\n\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {RequestMethod} from './enums';\nimport {ConnectionBackend, RequestArgs, RequestOptionsArgs} from './interfaces';\nimport {Request} from './static_request';\nimport {Response} from './static_response';\n/**\n * @param {?} backend\n * @param {?} request\n * @return {?}\n */\nfunction httpRequest(backend: ConnectionBackend, request: Request): Observable<Response> {\n  return backend.createConnection(request).response;\n}\n/**\n * @param {?} defaultOpts\n * @param {?} providedOpts\n * @param {?} method\n * @param {?} url\n * @return {?}\n */\nfunction mergeOptions(\n    defaultOpts: BaseRequestOptions, providedOpts: RequestOptionsArgs | undefined,\n    method: RequestMethod, url: string): RequestArgs {\n  const /** @type {?} */ newOptions = defaultOpts;\n  if (providedOpts) {\n    // Hack so Dart can used named parameters\n    return /** @type {?} */(( newOptions.merge(new RequestOptions({\n      method: providedOpts.method || method,\n      url: providedOpts.url || url,\n      search: providedOpts.search,\n      params: providedOpts.params,\n      headers: providedOpts.headers,\n      body: providedOpts.body,\n      withCredentials: providedOpts.withCredentials,\n      responseType: providedOpts.responseType\n    })) as RequestArgs));\n  }\n\n  return /** @type {?} */(( newOptions.merge(new RequestOptions({method, url})) as RequestArgs));\n}\n/**\n * Performs http requests using `XMLHttpRequest` as the default backend.\n * \n * `Http` is available as an injectable class, with methods to perform http requests. Calling\n * `request` returns an `Observable` which will emit a single {\\@link Response} when a\n * response is received.\n * \n * ### Example\n * \n * ```typescript\n * import {Http, HTTP_PROVIDERS} from '\\@angular/http';\n * import 'rxjs/add/operator/map'\n * \\@Component({ \n *   selector: 'http-app',\n *   viewProviders: [HTTP_PROVIDERS],\n *   templateUrl: 'people.html'\n * })\n * class PeopleComponent {\n *   constructor(http: Http) {\n *     http.get('people.json')\n *       // Call map on the response observable to get the parsed people object\n *       .map(res => res.json())\n *       // Subscribe to the observable to get the parsed people object and attach it to the\n *       // component\n *       .subscribe(people => this.people = people);\n *   }\n * }\n * ```\n * \n * \n * ### Example\n * \n * ```\n * http.get('people.json').subscribe((res:Response) => this.people = res.json());\n * ```\n * \n * The default construct used to perform requests, `XMLHttpRequest`, is abstracted as a \"Backend\" (\n * {\\@link XHRBackend} in this case), which could be mocked with dependency injection by replacing\n * the {\\@link XHRBackend} provider, as in the following example:\n * \n * ### Example\n * \n * ```typescript\n * import {BaseRequestOptions, Http} from '\\@angular/http';\n * import {MockBackend} from '\\@angular/http/testing';\n * var injector = Injector.resolveAndCreate([\n *   BaseRequestOptions,\n *   MockBackend,\n *   {provide: Http, useFactory:\n *       function(backend, defaultOptions) {\n *         return new Http(backend, defaultOptions);\n *       },\n *       deps: [MockBackend, BaseRequestOptions]}\n * ]);\n * var http = injector.get(Http);\n * http.get('request-from-mock-backend.json').subscribe((res:Response) => doSomething(res));\n * ```\n * \n * \\@experimental\n */\nexport class Http {\n/**\n * @param {?} _backend\n * @param {?} _defaultOptions\n */\nconstructor(protected _backend: ConnectionBackend, protected _defaultOptions: RequestOptions) {}\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      responseObservable = httpRequest(\n          this._backend,\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url)))));\n    } else if (url instanceof Request) {\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\n/**\n * Performs a request with `get` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nget(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, url)));\n  }\n/**\n * Performs a request with `post` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npost(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Post,\n        url)));\n  }\n/**\n * Performs a request with `put` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\nput(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Put,\n        url)));\n  }\n/**\n * Performs a request with `delete` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\ndelete (url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Delete, url)));\n  }\n/**\n * Performs a request with `patch` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npatch(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Patch,\n        url)));\n  }\n/**\n * Performs a request with `head` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nhead(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Head, url)));\n  }\n/**\n * Performs a request with `options` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\noptions(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Options, url)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Http_tsickle_Closure_declarations() {\n/** @type {?} */\nHttp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttp.ctorParameters;\n/** @type {?} */\nHttp.prototype._backend;\n/** @type {?} */\nHttp.prototype._defaultOptions;\n}\n\n/**\n * \\@experimental\n */\nexport class Jsonp extends Http {\n/**\n * @param {?} backend\n * @param {?} defaultOptions\n */\nconstructor(backend: ConnectionBackend, defaultOptions: RequestOptions) {\n    super(backend, defaultOptions);\n  }\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * \n * \\@security Regular XHR is the safest alternative to JSONP for most applications, and is\n * supported by all current browsers. Because JSONP creates a `<script>` element with\n * contents retrieved from a remote source, attacker-controlled data introduced by an untrusted\n * source could expose your application to XSS risks. Data exposed by JSONP may also be\n * readable by malicious third-party websites. In addition, JSONP introduces potential risk for\n * future security issues (e.g. content sniffing).  For more detail, see the\n * [Security Guide](http://g.co/ng/security).\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      url =\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url))));\n    }\n    if (url instanceof Request) {\n      if (url.method !== RequestMethod.Get) {\n        throw new Error('JSONP requests must use GET request method.');\n      }\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Jsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Body} from './body';\nimport {ContentType, RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates `Request` instances from provided values.\n * \n * The Request's interface is inspired by the Request constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#request-class),\n * but is considered a static value whose body can be accessed many times. There are other\n * differences in the implementation, but this is the most significant.\n * \n * `Request` instances are typically created by higher-level classes, like {\\@link Http} and\n * {\\@link Jsonp}, but it may occasionally be useful to explicitly create `Request` instances.\n * One such example is when creating services that wrap higher-level services, like {\\@link Http},\n * where it may be useful to generate a `Request` with arbitrary headers and search params.\n * \n * ```typescript\n * import {Injectable, Injector} from '\\@angular/core';\n * import {HTTP_PROVIDERS, Http, Request, RequestMethod} from '\\@angular/http';\n * \n * \\@Injectable() \n * class AutoAuthenticator {\n *   constructor(public http:Http) {}\n *   request(url:string) {\n *     return this.http.request(new Request({\n *       method: RequestMethod.Get,\n *       url: url,\n *       search: 'password=123'\n *     }));\n *   }\n * }\n * \n * var injector = Injector.resolveAndCreate([HTTP_PROVIDERS, AutoAuthenticator]);\n * var authenticator = injector.get(AutoAuthenticator);\n * authenticator.request('people.json').subscribe(res => {\n *   //URL should have included '?password=123'\n *   console.log('people', res.json());\n * });\n * ```\n * \n * \\@experimental\n */\nexport class Request extends Body {\n/**\n * Http method with which to perform the request.\n */\nmethod: RequestMethod;\n/**\n * {\\@link Headers} instance\n */\nheaders: Headers;\n/**\n * Url of the remote resource\n */\nurl: string;\n/**\n * Type of the request body *\n */\nprivate contentType: ContentType;\n/**\n * Enable use credentials\n */\nwithCredentials: boolean;\n/**\n * Buffer to store the response\n */\nresponseType: ResponseContentType;\n/**\n * @param {?} requestOptions\n */\nconstructor(requestOptions: RequestArgs) {\n    super();\n    // TODO: assert that url is present\n    const url = requestOptions.url;\n    this.url = requestOptions.url !;\n    const paramsArg = requestOptions.params || requestOptions.search;\n    if (paramsArg) {\n      let params: string;\n      if (typeof paramsArg === 'object' && !(paramsArg instanceof URLSearchParams)) {\n        params = urlEncodeParams(paramsArg).toString();\n      } else {\n        params = paramsArg.toString();\n      }\n      if (params.length > 0) {\n        let prefix = '?';\n        if (this.url.indexOf('?') != -1) {\n          prefix = (this.url[this.url.length - 1] == '&') ? '' : '&';\n        }\n        // TODO: just delete search-query-looking string in url?\n        this.url = url + prefix + params;\n      }\n    }\n    this._body = requestOptions.body;\n    this.method = normalizeMethodName(requestOptions.method !);\n    // TODO(jeffbcross): implement behavior\n    // Defaults to 'omit', consistent with browser\n    this.headers = new Headers(requestOptions.headers);\n    this.contentType = this.detectContentType();\n    this.withCredentials = requestOptions.withCredentials !;\n    this.responseType = requestOptions.responseType !;\n  }\n/**\n * Returns the content type enum based on header options.\n * @return {?}\n */\ndetectContentType(): ContentType {\n    switch (this.headers.get('content-type')) {\n      case 'application/json':\n        return ContentType.JSON;\n      case 'application/x-www-form-urlencoded':\n        return ContentType.FORM;\n      case 'multipart/form-data':\n        return ContentType.FORM_DATA;\n      case 'text/plain':\n      case 'text/html':\n        return ContentType.TEXT;\n      case 'application/octet-stream':\n        return this._body instanceof ArrayBuffer ? ContentType.ARRAY_BUFFER : ContentType.BLOB;\n      default:\n        return this.detectContentTypeFromBody();\n    }\n  }\n/**\n * Returns the content type of request's body based on its type.\n * @return {?}\n */\ndetectContentTypeFromBody(): ContentType {\n    if (this._body == null) {\n      return ContentType.NONE;\n    } else if (this._body instanceof URLSearchParams) {\n      return ContentType.FORM;\n    } else if (this._body instanceof FormData) {\n      return ContentType.FORM_DATA;\n    } else if (this._body instanceof Blob) {\n      return ContentType.BLOB;\n    } else if (this._body instanceof ArrayBuffer) {\n      return ContentType.ARRAY_BUFFER;\n    } else if (this._body && typeof this._body === 'object') {\n      return ContentType.JSON;\n    } else {\n      return ContentType.TEXT;\n    }\n  }\n/**\n * Returns the request's body according to its type. If body is undefined, return\n * null.\n * @return {?}\n */\ngetBody(): any {\n    switch (this.contentType) {\n      case ContentType.JSON:\n        return this.text();\n      case ContentType.FORM:\n        return this.text();\n      case ContentType.FORM_DATA:\n        return this._body;\n      case ContentType.TEXT:\n        return this.text();\n      case ContentType.BLOB:\n        return this.blob();\n      case ContentType.ARRAY_BUFFER:\n        return this.arrayBuffer();\n      default:\n        return null;\n    }\n  }\n}\n\nfunction Request_tsickle_Closure_declarations() {\n/**\n * Http method with which to perform the request.\n * @type {?}\n */\nRequest.prototype.method;\n/**\n * {\\@link Headers} instance\n * @type {?}\n */\nRequest.prototype.headers;\n/**\n * Url of the remote resource\n * @type {?}\n */\nRequest.prototype.url;\n/**\n * Type of the request body *\n * @type {?}\n */\nRequest.prototype.contentType;\n/**\n * Enable use credentials\n * @type {?}\n */\nRequest.prototype.withCredentials;\n/**\n * Buffer to store the response\n * @type {?}\n */\nRequest.prototype.responseType;\n}\n\n/**\n * @param {?} params\n * @return {?}\n */\nfunction urlEncodeParams(params: {[key: string]: any}): URLSearchParams {\n  const /** @type {?} */ searchParams = new URLSearchParams();\n  Object.keys(params).forEach(key => {\n    const /** @type {?} */ value = params[key];\n    if (value && Array.isArray(value)) {\n      value.forEach(element => searchParams.append(key, element.toString()));\n    } else {\n      searchParams.append(key, value.toString());\n    }\n  });\n  return searchParams;\n}\n\nconst /** @type {?} */ noop = function() {};\nconst /** @type {?} */ w = typeof window == 'object' ? window : noop;\nconst /** @type {?} */ FormData = ( /** @type {?} */((w as any)) /** TODO #9100 */)['FormData'] || noop;\nconst /** @type {?} */ Blob = ( /** @type {?} */((w as any)) /** TODO #9100 */)['Blob'] || noop;\nexport const /** @type {?} */ ArrayBuffer: ArrayBufferConstructor =\n    ( /** @type {?} */((w as any)) /** TODO #9100 */)['ArrayBuffer'] || noop;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestOptionsArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates a request options object to be optionally provided when instantiating a\n * {\\@link Request}.\n * \n * This class is based on the `RequestInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#requestinit).\n * \n * All values are null by default. Typical defaults can be found in the {\\@link BaseRequestOptions}\n * class, which sub-classes `RequestOptions`.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * });\n * const req = new Request(options);\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class RequestOptions {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n */\nmethod: RequestMethod|string|null;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n */\nheaders: Headers|null;\n/**\n * Body to be used when creating a {\\@link Request}.\n */\nbody: any;\n/**\n * Url with which to perform a {\\@link Request}.\n */\nurl: string|null;\n/**\n * Search parameters to be included in a {\\@link Request}.\n */\nparams: URLSearchParams;\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @return {?}\n */\nget search(): URLSearchParams { return this.params; }\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @param {?} params\n * @return {?}\n */\nset search(params: URLSearchParams) { this.params = params; }\n/**\n * Enable use credentials for a {\\@link Request}.\n */\nwithCredentials: boolean|null;\n  /*\n   * Select a buffer to store the response, such as ArrayBuffer, Blob, Json (or Document)\n   */\n  responseType: ResponseContentType|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: RequestOptionsArgs = {}) {\n    const {method, headers, body, url, search, params, withCredentials, responseType} = opts;\n    this.method = method != null ? normalizeMethodName(method) : null;\n    this.headers = headers != null ? headers : null;\n    this.body = body != null ? body : null;\n    this.url = url != null ? url : null;\n    this.params = this._mergeSearchParams(params || search);\n    this.withCredentials = withCredentials != null ? withCredentials : null;\n    this.responseType = responseType != null ? responseType : null;\n  }\n/**\n * Creates a copy of the `RequestOptions` instance, using the optional input as values to override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * Note that `headers` and `search` will override existing values completely if present in\n * the `options` object. If these values should be merged, it should be done prior to calling\n * `merge` on the `RequestOptions` instance.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post\n * });\n * const req = new Request(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: RequestOptionsArgs): RequestOptions {\n    return new RequestOptions({\n      method: options && options.method != null ? options.method : this.method,\n      headers: options && options.headers != null ? options.headers : new Headers(this.headers),\n      body: options && options.body != null ? options.body : this.body,\n      url: options && options.url != null ? options.url : this.url,\n      params: options && this._mergeSearchParams(options.params || options.search),\n      withCredentials: options && options.withCredentials != null ? options.withCredentials :\n                                                                    this.withCredentials,\n      responseType: options && options.responseType != null ? options.responseType :\n                                                              this.responseType\n    });\n  }\n/**\n * @param {?=} params\n * @return {?}\n */\nprivate _mergeSearchParams(params?: string|URLSearchParams|{[key: string]: any | any[]}|\n                             null): URLSearchParams {\n    if (!params) return this.params;\n\n    if (params instanceof URLSearchParams) {\n      return params.clone();\n    }\n\n    if (typeof params === 'string') {\n      return new URLSearchParams(params);\n    }\n\n    return this._parseParams(params);\n  }\n/**\n * @param {?=} objParams\n * @return {?}\n */\nprivate _parseParams(objParams: {[key: string]: any | any[]} = {}): URLSearchParams {\n    const /** @type {?} */ params = new URLSearchParams();\n    Object.keys(objParams).forEach((key: string) => {\n      const /** @type {?} */ value: any|any[] = objParams[key];\n      if (Array.isArray(value)) {\n        value.forEach((item: any) => this._appendParam(key, item, params));\n      } else {\n        this._appendParam(key, value, params);\n      }\n    });\n    return params;\n  }\n/**\n * @param {?} key\n * @param {?} value\n * @param {?} params\n * @return {?}\n */\nprivate _appendParam(key: string, value: any, params: URLSearchParams): void {\n    if (typeof value !== 'string') {\n      value = JSON.stringify(value);\n    }\n    params.append(key, value);\n  }\n}\n\nfunction RequestOptions_tsickle_Closure_declarations() {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n * @type {?}\n */\nRequestOptions.prototype.method;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.headers;\n/**\n * Body to be used when creating a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.body;\n/**\n * Url with which to perform a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.url;\n/**\n * Search parameters to be included in a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.params;\n/**\n * Enable use credentials for a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.withCredentials;\n/** @type {?} */\nRequestOptions.prototype.responseType;\n}\n\n/**\n * Subclass of {\\@link RequestOptions}, with default values.\n * \n * Default values:\n *  * method: {\\@link RequestMethod RequestMethod.Get}\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link RequestOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create and send {\\@link Request Requests}.\n * \n * ```typescript\n * import {BaseRequestOptions, RequestOptions} from '\\@angular/http';\n * \n * class MyOptions extends BaseRequestOptions {\n *   search: string = 'coreTeam=true';\n * }\n * \n * {provide: RequestOptions, useClass: MyOptions};\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Request}\n * object.\n * \n * ```\n * import {BaseRequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new BaseRequestOptions();\n * const req = new Request(options.merge({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class BaseRequestOptions extends RequestOptions {\nconstructor() { super({method: RequestMethod.Get, headers: new Headers()}); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseRequestOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseRequestOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseRequestOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {ResponseOptions} from '../base_response_options';\nimport {ContentType, ReadyState, RequestMethod, ResponseContentType, ResponseType} from '../enums';\nimport {Headers} from '../headers';\nimport {getResponseURL, isSuccess} from '../http_utils';\nimport {Connection, ConnectionBackend, XSRFStrategy} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\nimport {BrowserXhr} from './browser_xhr';\n\nconst /** @type {?} */ XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Creates connections using `XMLHttpRequest`. Given a fully-qualified\n * request, an `XHRConnection` will immediately create an `XMLHttpRequest` object and send the\n * request.\n * \n * This class would typically not be created or interacted with directly inside applications, though\n * the {\\@link MockConnection} may be interacted with in tests.\n * \n * \\@experimental\n */\nexport class XHRConnection implements Connection {\n  request: Request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n */\nresponse: Observable<Response>;\n  readyState: ReadyState;\n/**\n * @param {?} req\n * @param {?} browserXHR\n * @param {?=} baseResponseOptions\n */\nconstructor(req: Request, browserXHR: BrowserXhr, baseResponseOptions?: ResponseOptions) {\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n      const _xhr: XMLHttpRequest = browserXHR.build();\n      _xhr.open(RequestMethod[req.method].toUpperCase(), req.url);\n      if (req.withCredentials != null) {\n        _xhr.withCredentials = req.withCredentials;\n      }\n      // load event handler\n      const onLoad = () => {\n        // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n        let status: number = _xhr.status === 1223 ? 204 : _xhr.status;\n\n        let body: any = null;\n\n        // HTTP 204 means no content\n        if (status !== 204) {\n          // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n          // response/responseType properties were introduced in ResourceLoader Level2 spec\n          // (supported by IE10)\n          body = (typeof _xhr.response === 'undefined') ? _xhr.responseText : _xhr.response;\n\n          // Implicitly strip a potential XSSI prefix.\n          if (typeof body === 'string') {\n            body = body.replace(XSSI_PREFIX, '');\n          }\n        }\n\n        // fix status code when it is 0 (0 status is undocumented).\n        // Occurs when accessing file resources or on Android 4.1 stock browser\n        // while retrieving files from application cache.\n        if (status === 0) {\n          status = body ? 200 : 0;\n        }\n\n        const headers: Headers = Headers.fromResponseHeaderString(_xhr.getAllResponseHeaders());\n        // IE 9 does not provide the way to get URL of response\n        const url = getResponseURL(_xhr) || req.url;\n        const statusText: string = _xhr.statusText || 'OK';\n\n        let responseOptions = new ResponseOptions({body, status, headers, statusText, url});\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        const response = new Response(responseOptions);\n        response.ok = isSuccess(status);\n        if (response.ok) {\n          responseObserver.next(response);\n          // TODO(gdi2290): defer complete if array buffer until done\n          responseObserver.complete();\n          return;\n        }\n        responseObserver.error(response);\n      };\n      // error event handler\n      const onError = (err: ErrorEvent) => {\n        let responseOptions = new ResponseOptions({\n          body: err,\n          type: ResponseType.Error,\n          status: _xhr.status,\n          statusText: _xhr.statusText,\n        });\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      this.setDetectedContentType(req, _xhr);\n\n      if (req.headers == null) {\n        req.headers = new Headers();\n      }\n      if (!req.headers.has('Accept')) {\n        req.headers.append('Accept', 'application/json, text/plain, */*');\n      }\n      req.headers.forEach((values, name) => _xhr.setRequestHeader(name !, values.join(',')));\n\n      // Select the correct buffer type to store the response\n      if (req.responseType != null && _xhr.responseType != null) {\n        switch (req.responseType) {\n          case ResponseContentType.ArrayBuffer:\n            _xhr.responseType = 'arraybuffer';\n            break;\n          case ResponseContentType.Json:\n            _xhr.responseType = 'json';\n            break;\n          case ResponseContentType.Text:\n            _xhr.responseType = 'text';\n            break;\n          case ResponseContentType.Blob:\n            _xhr.responseType = 'blob';\n            break;\n          default:\n            throw new Error('The selected responseType is not supported');\n        }\n      }\n\n      _xhr.addEventListener('load', onLoad);\n      _xhr.addEventListener('error', onError);\n\n      _xhr.send(this.request.getBody());\n\n      return () => {\n        _xhr.removeEventListener('load', onLoad);\n        _xhr.removeEventListener('error', onError);\n        _xhr.abort();\n      };\n    });\n  }\n/**\n * @param {?} req\n * @param {?} _xhr\n * @return {?}\n */\nsetDetectedContentType(req: any /** TODO Request */, _xhr: any /** XMLHttpRequest */) {\n    // Skip if a custom Content-Type header is provided\n    if (req.headers != null && req.headers.get('Content-Type') != null) {\n      return;\n    }\n\n    // Set the detected content type\n    switch (req.contentType) {\n      case ContentType.NONE:\n        break;\n      case ContentType.JSON:\n        _xhr.setRequestHeader('content-type', 'application/json');\n        break;\n      case ContentType.FORM:\n        _xhr.setRequestHeader('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        break;\n      case ContentType.TEXT:\n        _xhr.setRequestHeader('content-type', 'text/plain');\n        break;\n      case ContentType.BLOB:\n        const /** @type {?} */ blob = req.blob();\n        if (blob.type) {\n          _xhr.setRequestHeader('content-type', blob.type);\n        }\n        break;\n    }\n  }\n}\n\nfunction XHRConnection_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRConnection.prototype.request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n * @type {?}\n */\nXHRConnection.prototype.response;\n/** @type {?} */\nXHRConnection.prototype.readyState;\n}\n\n/**\n * `XSRFConfiguration` sets up Cross Site Request Forgery (XSRF) protection for the application\n * using a cookie. See https://www.owasp.org/index.php/Cross-Site_Request_Forgery_(CSRF)\n * for more information on XSRF.\n * \n * Applications can configure custom cookie and header names by binding an instance of this class\n * with different `cookieName` and `headerName` values. See the main HTTP documentation for more\n * details.\n * \n * \\@experimental\n */\nexport class CookieXSRFStrategy implements XSRFStrategy {\n/**\n * @param {?=} _cookieName\n * @param {?=} _headerName\n */\nconstructor(\nprivate _cookieName: string = 'XSRF-TOKEN',\nprivate _headerName: string = 'X-XSRF-TOKEN') {}\n/**\n * @param {?} req\n * @return {?}\n */\nconfigureRequest(req: Request): void {\n    const /** @type {?} */ xsrfToken = getDOM().getCookie(this._cookieName);\n    if (xsrfToken) {\n      req.headers.set(this._headerName, xsrfToken);\n    }\n  }\n}\n\nfunction CookieXSRFStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nCookieXSRFStrategy.prototype._cookieName;\n/** @type {?} */\nCookieXSRFStrategy.prototype._headerName;\n}\n\n/**\n * Creates {\\@link XHRConnection} instances.\n * \n * This class would typically not be used by end users, but could be\n * overridden if a different backend implementation should be used,\n * such as in a node backend.\n * \n * ### Example\n * \n * ```\n * import {Http, MyNodeBackend, HTTP_PROVIDERS, BaseRequestOptions} from '\\@angular/http';\n * \\@Component({ \n *   viewProviders: [\n *     HTTP_PROVIDERS,\n *     {provide: Http, useFactory: (backend, options) => {\n *       return new Http(backend, options);\n *     }, deps: [MyNodeBackend, BaseRequestOptions]}]\n * })\n * class MyComponent {\n *   constructor(http:Http) {\n *     http.request('people.json').subscribe(res => this.people = res.json());\n *   }\n * }\n * ```\n * \\@experimental\n */\nexport class XHRBackend implements ConnectionBackend {\n/**\n * @param {?} _browserXHR\n * @param {?} _baseResponseOptions\n * @param {?} _xsrfStrategy\n */\nconstructor(\nprivate _browserXHR: BrowserXhr,\nprivate _baseResponseOptions: ResponseOptions,\nprivate _xsrfStrategy: XSRFStrategy) {}\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): XHRConnection {\n    this._xsrfStrategy.configureRequest(request);\n    return new XHRConnection(request, this._browserXHR, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserXhr, },\n{type: ResponseOptions, },\n{type: XSRFStrategy, },\n];\n}\n\nfunction XHRBackend_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRBackend.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nXHRBackend.ctorParameters;\n/** @type {?} */\nXHRBackend.prototype._browserXHR;\n/** @type {?} */\nXHRBackend.prototype._baseResponseOptions;\n/** @type {?} */\nXHRBackend.prototype._xsrfStrategy;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\n\nimport {ResponseOptions} from '../base_response_options';\nimport {ReadyState, RequestMethod, ResponseType} from '../enums';\nimport {Connection, ConnectionBackend} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\n\nimport {BrowserJsonp} from './browser_jsonp';\n\nconst /** @type {?} */ JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\nconst /** @type {?} */ JSONP_ERR_WRONG_METHOD = 'JSONP requests must use GET request method.';\n/**\n * Abstract base class for an in-flight JSONP request.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPConnection implements Connection {\n/**\n * The {\\@link ReadyState} of this request.\n */\nreadyState: ReadyState;\n/**\n * The outgoing HTTP request.\n */\nrequest: Request;\n/**\n * An observable that completes with the response, when the request is finished.\n */\nresponse: Observable<Response>;\n/**\n * Callback called when the JSONP request completes, to notify the application\n * of the new data.\n * @abstract\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {}\n}\n\nfunction JSONPConnection_tsickle_Closure_declarations() {\n/**\n * The {\\@link ReadyState} of this request.\n * @type {?}\n */\nJSONPConnection.prototype.readyState;\n/**\n * The outgoing HTTP request.\n * @type {?}\n */\nJSONPConnection.prototype.request;\n/**\n * An observable that completes with the response, when the request is finished.\n * @type {?}\n */\nJSONPConnection.prototype.response;\n}\n\nexport class JSONPConnection_ extends JSONPConnection {\nprivate _id: string;\nprivate _script: Element;\nprivate _responseData: any;\nprivate _finished: boolean = false;\n/**\n * @param {?} req\n * @param {?} _dom\n * @param {?=} baseResponseOptions\n */\nconstructor(\n      req: Request,\nprivate _dom: BrowserJsonp,\nprivate baseResponseOptions?: ResponseOptions) {\n    super();\n    if (req.method !== RequestMethod.Get) {\n      throw new TypeError(JSONP_ERR_WRONG_METHOD);\n    }\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n\n      this.readyState = ReadyState.Loading;\n      const id = this._id = _dom.nextRequestID();\n\n      _dom.exposeConnection(id, this);\n\n      // Workaround Dart\n      // url = url.replace(/=JSONP_CALLBACK(&|$)/, `generated method`);\n      const callback = _dom.requestCallback(this._id);\n      let url: string = req.url;\n      if (url.indexOf('=JSONP_CALLBACK&') > -1) {\n        url = url.replace('=JSONP_CALLBACK&', `=${callback}&`);\n      } else if (url.lastIndexOf('=JSONP_CALLBACK') === url.length - '=JSONP_CALLBACK'.length) {\n        url = url.substring(0, url.length - '=JSONP_CALLBACK'.length) + `=${callback}`;\n      }\n\n      const script = this._script = _dom.build(url);\n\n      const onLoad = (event: Event) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        if (!this._finished) {\n          let responseOptions =\n              new ResponseOptions({body: JSONP_ERR_NO_CALLBACK, type: ResponseType.Error, url});\n          if (baseResponseOptions) {\n            responseOptions = baseResponseOptions.merge(responseOptions);\n          }\n          responseObserver.error(new Response(responseOptions));\n          return;\n        }\n\n        let responseOptions = new ResponseOptions({body: this._responseData, url});\n        if (this.baseResponseOptions) {\n          responseOptions = this.baseResponseOptions.merge(responseOptions);\n        }\n\n        responseObserver.next(new Response(responseOptions));\n        responseObserver.complete();\n      };\n\n      const onError = (error: Error) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        let responseOptions = new ResponseOptions({body: error.message, type: ResponseType.Error});\n        if (baseResponseOptions) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      script.addEventListener('load', onLoad);\n      script.addEventListener('error', onError);\n\n      _dom.send(script);\n\n      return () => {\n        this.readyState = ReadyState.Cancelled;\n        script.removeEventListener('load', onLoad);\n        script.removeEventListener('error', onError);\n        this._dom.cleanup(script);\n      };\n    });\n  }\n/**\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {\n    // Don't leak connections\n    this._finished = true;\n    this._dom.removeConnection(this._id);\n    if (this.readyState === ReadyState.Cancelled) return;\n    this._responseData = data;\n  }\n}\n\nfunction JSONPConnection__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPConnection_.prototype._id;\n/** @type {?} */\nJSONPConnection_.prototype._script;\n/** @type {?} */\nJSONPConnection_.prototype._responseData;\n/** @type {?} */\nJSONPConnection_.prototype._finished;\n/** @type {?} */\nJSONPConnection_.prototype._dom;\n/** @type {?} */\nJSONPConnection_.prototype.baseResponseOptions;\n}\n\n/**\n * A {\\@link ConnectionBackend} that uses the JSONP strategy of making requests.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPBackend extends ConnectionBackend {}\nexport class JSONPBackend_ extends JSONPBackend {\n/**\n * @param {?} _browserJSONP\n * @param {?} _baseResponseOptions\n */\nconstructor(private _browserJSONP: BrowserJsonp,\nprivate _baseResponseOptions: ResponseOptions) {\n    super();\n  }\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): JSONPConnection {\n    return new JSONPConnection_(request, this._browserJSONP, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserJsonp, },\n{type: ResponseOptions, },\n];\n}\n\nfunction JSONPBackend__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPBackend_.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJSONPBackend_.ctorParameters;\n/** @type {?} */\nJSONPBackend_.prototype._browserJSONP;\n/** @type {?} */\nJSONPBackend_.prototype._baseResponseOptions;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nlet /** @type {?} */ _nextRequestId = 0;\nexport const /** @type {?} */ JSONP_HOME = '__ng_jsonp__';\nlet /** @type {?} */ _jsonpConnections: {[key: string]: any}|null = null;\n/**\n * @return {?}\n */\nfunction _getJsonpConnections(): {[key: string]: any} {\n  const /** @type {?} */ w: {[key: string]: any} = typeof window == 'object' ? window : {};\n  if (_jsonpConnections === null) {\n    _jsonpConnections = w[JSONP_HOME] = {};\n  }\n  return _jsonpConnections;\n}\nexport class BrowserJsonp {\n/**\n * @param {?} url\n * @return {?}\n */\nbuild(url: string): any {\n    const /** @type {?} */ node = document.createElement('script');\n    node.src = url;\n    return node;\n  }\n/**\n * @return {?}\n */\nnextRequestID(): string { return `__req${_nextRequestId++}`; }\n/**\n * @param {?} id\n * @return {?}\n */\nrequestCallback(id: string): string { return `${JSONP_HOME}.${id}.finished`; }\n/**\n * @param {?} id\n * @param {?} connection\n * @return {?}\n */\nexposeConnection(id: string, connection: any) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = connection;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nremoveConnection(id: string) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = null;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nsend(node: any) { document.body.appendChild( /** @type {?} */((<Node>(node)))); }\n/**\n * @param {?} node\n * @return {?}\n */\ncleanup(node: any) {\n    if (node.parentNode) {\n      node.parentNode.removeChild( /** @type {?} */((<Node>(node))));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserJsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\n\nimport {ResponseOptions} from './base_response_options';\nimport {Body} from './body';\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\n/**\n * Creates `Response` instances from provided values.\n * \n * Though this object isn't\n * usually instantiated by end-users, it is the primary object interacted with when it comes time to\n * add data to a view.\n * \n * ### Example\n * \n * ```\n * http.request('my-friends.txt').subscribe(response => this.friends = response.text());\n * ```\n * \n * The Response's interface is inspired by the Response constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#response-class), but is considered a static value whose body\n * can be accessed many times. There are other differences in the implementation, but this is the\n * most significant.\n * \n * \\@experimental\n */\nexport class Response extends Body {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n */\ntype: ResponseType;\n/**\n * True if the response's status is within 200-299\n */\nok: boolean;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n */\nurl: string;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n */\nstatus: number;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n */\nstatusText: string|null;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n */\nbytesLoaded: number;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n */\ntotalBytes: number;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n */\nheaders: Headers|null;\n/**\n * @param {?} responseOptions\n */\nconstructor(responseOptions: ResponseOptions) {\n    super();\n    this._body = responseOptions.body;\n    this.status = responseOptions.status !;\n    this.ok = (this.status >= 200 && this.status <= 299);\n    this.statusText = responseOptions.statusText;\n    this.headers = responseOptions.headers;\n    this.type = responseOptions.type !;\n    this.url = responseOptions.url !;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    return `Response with status: ${this.status} ${this.statusText} for URL: ${this.url}`;\n  }\n}\n\nfunction Response_tsickle_Closure_declarations() {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n * @type {?}\n */\nResponse.prototype.type;\n/**\n * True if the response's status is within 200-299\n * @type {?}\n */\nResponse.prototype.ok;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n * @type {?}\n */\nResponse.prototype.url;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n * @type {?}\n */\nResponse.prototype.status;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n * @type {?}\n */\nResponse.prototype.statusText;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n * @type {?}\n */\nResponse.prototype.bytesLoaded;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n * @type {?}\n */\nResponse.prototype.totalBytes;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n * @type {?}\n */\nResponse.prototype.headers;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {stringToArrayBuffer} from './http_utils';\nimport {URLSearchParams} from './url_search_params';\n/**\n * HTTP request body used by both {\\@link Request} and {\\@link Response}\n * https://fetch.spec.whatwg.org/#body\n * @abstract\n */\nexport abstract class Body {\n/**\n * \\@internal\n */\nprotected _body: any;\n/**\n * Attempts to return body as parsed `JSON` object, or raises an exception.\n * @return {?}\n */\njson(): any {\n    if (typeof this._body === 'string') {\n      return JSON.parse( /** @type {?} */((<string>this._body)));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return JSON.parse(this.text());\n    }\n\n    return this._body;\n  }\n/**\n * Returns the body as a string, presuming `toString()` can be called on the response body.\n * \n * When decoding an `ArrayBuffer`, the optional `encodingHint` parameter determines how the\n * bytes in the buffer will be interpreted. Valid values are:\n * \n * - `legacy` - incorrectly interpret the bytes as UTF-16 (technically, UCS-2). Only characters\n *   in the Basic Multilingual Plane are supported, surrogate pairs are not handled correctly.\n *   In addition, the endianness of the 16-bit octet pairs in the `ArrayBuffer` is not taken\n *   into consideration. This is the default behavior to avoid breaking apps, but should be\n *   considered deprecated.\n * \n * - `iso-8859` - interpret the bytes as ISO-8859 (which can be used for ASCII encoded text).\n * @param {?=} encodingHint\n * @return {?}\n */\ntext(encodingHint: 'legacy'|'iso-8859' = 'legacy'): string {\n    if (this._body instanceof URLSearchParams) {\n      return this._body.toString();\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      switch (encodingHint) {\n        case 'legacy':\n          return String.fromCharCode.apply(null, new Uint16Array( /** @type {?} */((this._body as ArrayBuffer))));\n        case 'iso-8859':\n          return String.fromCharCode.apply(null, new Uint8Array( /** @type {?} */((this._body as ArrayBuffer))));\n        default:\n          throw new Error(`Invalid value for encodingHint: ${encodingHint}`);\n      }\n    }\n\n    if (this._body == null) {\n      return '';\n    }\n\n    if (typeof this._body === 'object') {\n      return JSON.stringify(this._body, null, 2);\n    }\n\n    return this._body.toString();\n  }\n/**\n * Return the body as an ArrayBuffer\n * @return {?}\n */\narrayBuffer(): ArrayBuffer {\n    if (this._body instanceof ArrayBuffer) {\n      return /** @type {?} */(( <ArrayBuffer>this._body));\n    }\n\n    return stringToArrayBuffer(this.text());\n  }\n/**\n * Returns the request's body as a Blob, assuming that body exists.\n * @return {?}\n */\nblob(): Blob {\n    if (this._body instanceof Blob) {\n      return /** @type {?} */(( <Blob>this._body));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return new Blob([this._body]);\n    }\n\n    throw new Error('The request body isn\\'t either a blob or an array buffer');\n  }\n}\n\nfunction Body_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nBody.prototype._body;\n}\n\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @param {?=} rawParams\n * @return {?}\n */\nfunction paramParser(rawParams: string = ''): Map<string, string[]> {\n  const /** @type {?} */ map = new Map<string, string[]>();\n  if (rawParams.length > 0) {\n    const /** @type {?} */ params: string[] = rawParams.split('&');\n    params.forEach((param: string) => {\n      const /** @type {?} */ eqIdx = param.indexOf('=');\n      const [key, val]: string[] =\n          eqIdx == -1 ? [param, ''] : [param.slice(0, eqIdx), param.slice(eqIdx + 1)];\n      const /** @type {?} */ list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * \\@experimental \n * \n */\nexport class QueryEncoder {\n/**\n * @param {?} k\n * @return {?}\n */\nencodeKey(k: string): string { return standardEncoding(k); }\n/**\n * @param {?} v\n * @return {?}\n */\nencodeValue(v: string): string { return standardEncoding(v); }\n}\n/**\n * @param {?} v\n * @return {?}\n */\nfunction standardEncoding(v: string): string {\n  return encodeURIComponent(v)\n      .replace(/%40/gi, '@')\n      .replace(/%3A/gi, ':')\n      .replace(/%24/gi, '$')\n      .replace(/%2C/gi, ',')\n      .replace(/%3B/gi, ';')\n      .replace(/%2B/gi, '+')\n      .replace(/%3D/gi, '=')\n      .replace(/%3F/gi, '?')\n      .replace(/%2F/gi, '/');\n}\n/**\n * Map-like representation of url search parameters, based on\n * [URLSearchParams](https://url.spec.whatwg.org/#urlsearchparams) in the url living standard,\n * with several extensions for merging URLSearchParams objects:\n *   - setAll()\n *   - appendAll()\n *   - replaceAll()\n * \n * This class accepts an optional second parameter of ${\\@link QueryEncoder},\n * which is used to serialize parameters before making a request. By default,\n * `QueryEncoder` encodes keys and values of parameters using `encodeURIComponent`,\n * and then un-encodes certain characters that are allowed to be part of the query\n * according to IETF RFC 3986: https://tools.ietf.org/html/rfc3986.\n * \n * These are the characters that are not encoded: `! $ \\' ( ) * + , ; A 9 - . _ ~ ? /`\n * \n * If the set of allowed query characters is not acceptable for a particular backend,\n * `QueryEncoder` can be subclassed and provided as the 2nd argument to URLSearchParams.\n * \n * ```\n * import {URLSearchParams, QueryEncoder} from '\\@angular/http';\n * class MyQueryEncoder extends QueryEncoder {\n *   encodeKey(k: string): string {\n *     return myEncodingFunction(k);\n *   }\n * \n *   encodeValue(v: string): string {\n *     return myEncodingFunction(v);\n *   }\n * }\n * \n * let params = new URLSearchParams('', new MyQueryEncoder());\n * ```\n * \\@experimental\n */\nexport class URLSearchParams {\n  paramsMap: Map<string, string[]>;\n/**\n * @param {?=} rawParams\n * @param {?=} queryEncoder\n */\nconstructor(\npublic rawParams: string = '',\nprivate queryEncoder: QueryEncoder = new QueryEncoder()) {\n    this.paramsMap = paramParser(rawParams);\n  }\n/**\n * @return {?}\n */\nclone(): URLSearchParams {\n    const /** @type {?} */ clone = new URLSearchParams('', this.queryEncoder);\n    clone.appendAll(this);\n    return clone;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\nhas(param: string): boolean { return this.paramsMap.has(param); }\n/**\n * @param {?} param\n * @return {?}\n */\nget(param: string): string|null {\n    const /** @type {?} */ storedParam = this.paramsMap.get(param);\n\n    return Array.isArray(storedParam) ? storedParam[0] : null;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ngetAll(param: string): string[] { return this.paramsMap.get(param) || []; }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nset(param: string, val: string) {\n    if (val === void 0 || val === null) {\n      this.delete(param);\n      return;\n    }\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.length = 0;\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nsetAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      list.push(value[0]);\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nappend(param: string, val: string): void {\n    if (val === void 0 || val === null) return;\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nappendAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nreplaceAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    const /** @type {?} */ paramsList: string[] = [];\n    this.paramsMap.forEach((values, k) => {\n      values.forEach(\n          v => paramsList.push(\n              this.queryEncoder.encodeKey(k) + '=' + this.queryEncoder.encodeValue(v)));\n    });\n    return paramsList.join('&');\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ndelete (param: string): void { this.paramsMap.delete(param); }\n}\n\nfunction URLSearchParams_tsickle_Closure_declarations() {\n/** @type {?} */\nURLSearchParams.prototype.paramsMap;\n/** @type {?} */\nURLSearchParams.prototype.rawParams;\n/** @type {?} */\nURLSearchParams.prototype.queryEncoder;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {RequestMethod} from './enums';\n/**\n * @param {?} method\n * @return {?}\n */\nexport function normalizeMethodName(method: string | RequestMethod): RequestMethod {\n  if (typeof method !== 'string') return method;\n\n  switch (method.toUpperCase()) {\n    case 'GET':\n      return RequestMethod.Get;\n    case 'POST':\n      return RequestMethod.Post;\n    case 'PUT':\n      return RequestMethod.Put;\n    case 'DELETE':\n      return RequestMethod.Delete;\n    case 'OPTIONS':\n      return RequestMethod.Options;\n    case 'HEAD':\n      return RequestMethod.Head;\n    case 'PATCH':\n      return RequestMethod.Patch;\n  }\n  throw new Error(`Invalid request method. The method \"${method}\" is not supported.`);\n}\n\nexport const /** @type {?} */ isSuccess = (status: number): boolean => (status >= 200 && status < 300);\n/**\n * @param {?} xhr\n * @return {?}\n */\nexport function getResponseURL(xhr: any): string|null {\n  if ('responseURL' in xhr) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer8(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint8Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint16Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ReadyState, RequestMethod, ResponseContentType, ResponseType} from './enums';\nimport {Headers} from './headers';\nimport {Request} from './static_request';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Abstract class from which real backends are derived.\n * \n * The primary purpose of a `ConnectionBackend` is to create new connections to fulfill a given\n * {\\@link Request}.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class ConnectionBackend {\n/**\n * @abstract\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: any) {} }\n/**\n * Abstract class from which real connections are derived.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class Connection {\n  readyState: ReadyState;\n  request: Request;\n  response: any;  // TODO: generic of <Response>;\n}\n\nfunction Connection_tsickle_Closure_declarations() {\n/** @type {?} */\nConnection.prototype.readyState;\n/** @type {?} */\nConnection.prototype.request;\n/** @type {?} */\nConnection.prototype.response;\n}\n\n/**\n * An XSRFStrategy configures XSRF protection (e.g. via headers) on an HTTP request.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class XSRFStrategy {\n/**\n * @abstract\n * @param {?} req\n * @return {?}\n */\nconfigureRequest(req: Request) {} }\n\n/**\n * Interface for options to construct a RequestOptions, based on\n * [RequestInit](https://fetch.spec.whatwg.org/#requestinit) from the Fetch spec.\n *\n * @experimental\n */\nexport interface RequestOptionsArgs {\n  url?: string|null;\n  method?: string|RequestMethod|null;\n  /** @deprecated from 4.0.0. Use params instead. */\n  search?: string|URLSearchParams|{[key: string]: any | any[]}|null;\n  params?: string|URLSearchParams|{[key: string]: any | any[]}|null;\n  headers?: Headers|null;\n  body?: any;\n  withCredentials?: boolean|null;\n  responseType?: ResponseContentType|null;\n}\n\n/**\n * Required structure when constructing new Request();\n */\nexport interface RequestArgs extends RequestOptionsArgs { url: string|null; }\n\n/**\n * Interface for options to construct a Response, based on\n * [ResponseInit](https://fetch.spec.whatwg.org/#responseinit) from the Fetch spec.\n *\n * @experimental\n */\nexport interface ResponseOptionsArgs {\n  body?: string|Object|FormData|ArrayBuffer|Blob|null;\n  status?: number|null;\n  statusText?: string|null;\n  headers?: Headers|null;\n  type?: ResponseType|null;\n  url?: string|null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\nimport {ResponseOptionsArgs} from './interfaces';\n/**\n * Creates a response options object to be optionally provided when instantiating a\n * {\\@link Response}.\n * \n * This class is based on the `ResponseInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#responseinit).\n * \n * All values are null by default. Typical defaults can be found in the\n * {\\@link BaseResponseOptions} class, which sub-classes `ResponseOptions`.\n * \n * This class may be used in tests to build {\\@link Response Responses} for\n * mock responses (see {\\@link MockBackend}).\n * \n * ### Example ([live demo](http://plnkr.co/edit/P9Jkk8e8cz6NVzbcxEsD?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: '{\"name\":\"<PERSON>\"}'\n * });\n * var res = new Response(options);\n * \n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * ```\n * \n * \\@experimental\n */\nexport class ResponseOptions {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n */\nbody: string|Object|ArrayBuffer|Blob|null;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n */\nstatus: number|null;\n/**\n * Response {\\@link Headers headers}\n */\nheaders: Headers|null;\n/**\n * \\@internal\n */\nstatusText: string|null;\n/**\n * \\@internal\n */\ntype: ResponseType|null;\n  url: string|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: ResponseOptionsArgs = {}) {\n    const {body, status, headers, statusText, type, url} = opts;\n    this.body = body != null ? body : null;\n    this.status = status != null ? status : null;\n    this.headers = headers != null ? headers : null;\n    this.statusText = statusText != null ? statusText : null;\n    this.type = type != null ? type : null;\n    this.url = url != null ? url : null;\n  }\n/**\n * Creates a copy of the `ResponseOptions` instance, using the optional input as values to\n * override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * This may be useful when sharing a base `ResponseOptions` object inside tests,\n * where certain properties may change from test to test.\n * \n * ### Example ([live demo](http://plnkr.co/edit/1lXquqFfgduTFBWjNoRE?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: {name: 'Jeff'}\n * });\n * var res = new Response(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('options.url:', options.url); // null\n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * console.log('res.url:', res.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: ResponseOptionsArgs): ResponseOptions {\n    return new ResponseOptions({\n      body: options && options.body != null ? options.body : this.body,\n      status: options && options.status != null ? options.status : this.status,\n      headers: options && options.headers != null ? options.headers : this.headers,\n      statusText: options && options.statusText != null ? options.statusText : this.statusText,\n      type: options && options.type != null ? options.type : this.type,\n      url: options && options.url != null ? options.url : this.url,\n    });\n  }\n}\n\nfunction ResponseOptions_tsickle_Closure_declarations() {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n * @type {?}\n */\nResponseOptions.prototype.body;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n * @type {?}\n */\nResponseOptions.prototype.status;\n/**\n * Response {\\@link Headers headers}\n * @type {?}\n */\nResponseOptions.prototype.headers;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.statusText;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.type;\n/** @type {?} */\nResponseOptions.prototype.url;\n}\n\n/**\n * Subclass of {\\@link ResponseOptions}, with default values.\n * \n * Default values:\n *  * status: 200\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link ResponseOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create {\\@link Response Responses}.\n * \n * ### Example ([live demo](http://plnkr.co/edit/qv8DLT?p=preview))\n * \n * ```typescript\n * import {provide} from '\\@angular/core';\n * import {bootstrap} from '\\@angular/platform-browser/browser';\n * import {HTTP_PROVIDERS, Headers, Http, BaseResponseOptions, ResponseOptions} from\n * '\\@angular/http';\n * import {App} from './myapp';\n * \n * class MyOptions extends BaseResponseOptions {\n *   headers:Headers = new Headers({network: 'github'});\n * }\n * \n * bootstrap(App, [HTTP_PROVIDERS, {provide: ResponseOptions, useClass: MyOptions}]);\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Response}\n * object.\n * \n * ### Example ([live demo](http://plnkr.co/edit/VngosOWiaExEtbstDoix?p=preview))\n * \n * ```\n * import {BaseResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new BaseResponseOptions();\n * var res = new Response(options.merge({\n *   body: 'Angular',\n *   headers: new Headers({framework: 'angular'})\n * }));\n * console.log('res.headers.get(\"framework\"):', res.headers.get('framework')); // angular\n * console.log('res.text():', res.text()); // Angular;\n * ```\n * \n * \\@experimental\n */\nexport class BaseResponseOptions extends ResponseOptions {\nconstructor() {\n    super({status: 200, statusText: 'Ok', type: ResponseType.Default, headers: new Headers()});\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseResponseOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseResponseOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseResponseOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\n/**\n * Polyfill for [Headers](https://developer.mozilla.org/en-US/docs/Web/API/Headers/Headers), as\n * specified in the [Fetch Spec](https://fetch.spec.whatwg.org/#headers-class).\n * \n * The only known difference between this `Headers` implementation and the spec is the\n * lack of an `entries` method.\n * \n * ### Example\n * \n * ```\n * import {Headers} from '\\@angular/http';\n * \n * var firstHeaders = new Headers();\n * firstHeaders.append('Content-Type', 'image/jpeg');\n * console.log(firstHeaders.get('Content-Type')) //'image/jpeg'\n * \n * // Create headers from Plain Old JavaScript Object\n * var secondHeaders = new Headers({\n *   'X-My-Custom-Header': 'Angular'\n * });\n * console.log(secondHeaders.get('X-My-Custom-Header')); //'Angular'\n * \n * var thirdHeaders = new Headers(secondHeaders);\n * console.log(thirdHeaders.get('X-My-Custom-Header')); //'Angular'\n * ```\n * \n * \\@experimental\n */\nexport class Headers {\n/**\n * \\@internal header names are lower case\n */\n_headers: Map<string, string[]> = new Map();\n/**\n * \\@internal map lower case names to actual names\n */\n_normalizedNames: Map<string, string> = new Map();\n/**\n * @param {?=} headers\n */\nconstructor(headers?: Headers|{[name: string]: any}|null) {\n    if (!headers) {\n      return;\n    }\n\n    if (headers instanceof Headers) {\n      headers.forEach((values: string[], name: string) => {\n        values.forEach(value => this.append(name, value));\n      });\n      return;\n    }\n\n    Object.keys(headers).forEach((name: string) => {\n      const values: string[] = Array.isArray(headers[name]) ? headers[name] : [headers[name]];\n      this.delete(name);\n      values.forEach(value => this.append(name, value));\n    });\n  }\n/**\n * Returns a new Headers instance from the given DOMString of Response Headers\n * @param {?} headersString\n * @return {?}\n */\nstatic fromResponseHeaderString(headersString: string): Headers {\n    const /** @type {?} */ headers = new Headers();\n\n    headersString.split('\\n').forEach(line => {\n      const /** @type {?} */ index = line.indexOf(':');\n      if (index > 0) {\n        const /** @type {?} */ name = line.slice(0, index);\n        const /** @type {?} */ value = line.slice(index + 1).trim();\n        headers.set(name, value);\n      }\n    });\n\n    return headers;\n  }\n/**\n * Appends a header to existing list of header values for a given header name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nappend(name: string, value: string): void {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      this.set(name, value);\n    } else {\n      values.push(value);\n    }\n  }\n/**\n * Deletes all header values for the given name.\n * @param {?} name\n * @return {?}\n */\ndelete (name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n    this._normalizedNames.delete(lcName);\n    this._headers.delete(lcName);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nforEach(fn: (values: string[], name: string|undefined, headers: Map<string, string[]>) => void):\n      void {\n    this._headers.forEach(\n        (values, lcName) => fn(values, this._normalizedNames.get(lcName), this._headers));\n  }\n/**\n * Returns first header that matches given name.\n * @param {?} name\n * @return {?}\n */\nget(name: string): string|null {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      return null;\n    }\n\n    return values.length > 0 ? values[0] : null;\n  }\n/**\n * Checks for existence of header by given name.\n * @param {?} name\n * @return {?}\n */\nhas(name: string): boolean { return this._headers.has(name.toLowerCase()); }\n/**\n * Returns the names of the headers\n * @return {?}\n */\nkeys(): string[] { return Array.from(this._normalizedNames.values()); }\n/**\n * Sets or overrides header value for given name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nset(name: string, value: string|string[]): void {\n    if (Array.isArray(value)) {\n      if (value.length) {\n        this._headers.set(name.toLowerCase(), [value.join(',')]);\n      }\n    } else {\n      this._headers.set(name.toLowerCase(), [value]);\n    }\n    this.mayBeSetNormalizedName(name);\n  }\n/**\n * Returns values of all headers.\n * @return {?}\n */\nvalues(): string[][] { return Array.from(this._headers.values()); }\n/**\n * @return {?}\n */\ntoJSON(): {[name: string]: any} {\n    const /** @type {?} */ serialized: {[name: string]: string[]} = {};\n\n    this._headers.forEach((values: string[], name: string) => {\n      const /** @type {?} */ split: string[] = [];\n      values.forEach(v => split.push(...v.split(',')));\n      serialized[ /** @type {?} */((this._normalizedNames.get(name)))] = split;\n    });\n\n    return serialized;\n  }\n/**\n * Returns list of header values for a given name.\n * @param {?} name\n * @return {?}\n */\ngetAll(name: string): string[]|null {\n    return this.has(name) ? this._headers.get(name.toLowerCase()) || null : null;\n  }\n/**\n * This method is not implemented.\n * @return {?}\n */\nentries() { throw new Error('\"entries\" method is not implemented on Headers class'); }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate mayBeSetNormalizedName(name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n\n    if (!this._normalizedNames.has(lcName)) {\n      this._normalizedNames.set(lcName, name);\n    }\n  }\n}\n\nfunction Headers_tsickle_Closure_declarations() {\n/**\n * \\@internal header names are lower case\n * @type {?}\n */\nHeaders.prototype._headers;\n/**\n * \\@internal map lower case names to actual names\n * @type {?}\n */\nHeaders.prototype._normalizedNames;\n}\n\n", "\nexport type RequestMethod = number;\nexport let RequestMethod: any = {};\nRequestMethod.Get = 0;\nRequestMethod.Post = 1;\nRequestMethod.Put = 2;\nRequestMethod.Delete = 3;\nRequestMethod.Options = 4;\nRequestMethod.Head = 5;\nRequestMethod.Patch = 6;\nRequestMethod[RequestMethod.Get] = \"Get\";\nRequestMethod[RequestMethod.Post] = \"Post\";\nRequestMethod[RequestMethod.Put] = \"Put\";\nRequestMethod[RequestMethod.Delete] = \"Delete\";\nRequestMethod[RequestMethod.Options] = \"Options\";\nRequestMethod[RequestMethod.Head] = \"Head\";\nRequestMethod[RequestMethod.Patch] = \"Patch\";\n\nexport type ReadyState = number;\nexport let ReadyState: any = {};\nReadyState.Unsent = 0;\nReadyState.Open = 1;\nReadyState.HeadersReceived = 2;\nReadyState.Loading = 3;\nReadyState.Done = 4;\nReadyState.Cancelled = 5;\nReadyState[ReadyState.Unsent] = \"Unsent\";\nReadyState[ReadyState.Open] = \"Open\";\nReadyState[ReadyState.HeadersReceived] = \"HeadersReceived\";\nReadyState[ReadyState.Loading] = \"Loading\";\nReadyState[ReadyState.Done] = \"Done\";\nReadyState[ReadyState.Cancelled] = \"Cancelled\";\n\nexport type ResponseType = number;\nexport let ResponseType: any = {};\nResponseType.Basic = 0;\nResponseType.Cors = 1;\nResponseType.Default = 2;\nResponseType.Error = 3;\nResponseType.Opaque = 4;\nResponseType[ResponseType.Basic] = \"Basic\";\nResponseType[ResponseType.Cors] = \"Cors\";\nResponseType[ResponseType.Default] = \"Default\";\nResponseType[ResponseType.Error] = \"Error\";\nResponseType[ResponseType.Opaque] = \"Opaque\";\n\nexport type ContentType = number;\nexport let ContentType: any = {};\nContentType.NONE = 0;\nContentType.JSON = 1;\nContentType.FORM = 2;\nContentType.FORM_DATA = 3;\nContentType.TEXT = 4;\nContentType.BLOB = 5;\nContentType.ARRAY_BUFFER = 6;\nContentType[ContentType.NONE] = \"NONE\";\nContentType[ContentType.JSON] = \"JSON\";\nContentType[ContentType.FORM] = \"FORM\";\nContentType[ContentType.FORM_DATA] = \"FORM_DATA\";\nContentType[ContentType.TEXT] = \"TEXT\";\nContentType[ContentType.BLOB] = \"BLOB\";\nContentType[ContentType.ARRAY_BUFFER] = \"ARRAY_BUFFER\";\n\nexport type ResponseContentType = number;\nexport let ResponseContentType: any = {};\nResponseContentType.Text = 0;\nResponseContentType.Json = 1;\nResponseContentType.ArrayBuffer = 2;\nResponseContentType.Blob = 3;\nResponseContentType[ResponseContentType.Text] = \"Text\";\nResponseContentType[ResponseContentType.Json] = \"Json\";\nResponseContentType[ResponseContentType.ArrayBuffer] = \"ArrayBuffer\";\nResponseContentType[ResponseContentType.Blob] = \"Blob\";\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n/**\n * A backend for http that uses the `XMLHttpRequest` browser API.\n * \n * Take care not to evaluate this in non-browser contexts.\n * \n * \\@experimental\n */\nexport class BrowserXhr {\nconstructor() {}\n/**\n * @return {?}\n */\nbuild(): any { return /** @type {?} */(( <any>(new XMLHttpRequest()))); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserXhr_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserXhr.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserXhr.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "getDOM"], "mappings": ";;;;AmBAA;;;;;;;AASA,AACA;;;;;;;AAOA,AAAA,MAAA,UAAA,CAAA;IACA,WAAA,GAAA,GACG;;;;IAGH,KAFG,GAEH,EAFiB,SAAa,IAAI,cAAA,EAAe,GAAG,EAAC;;AAC9C,UAAP,CAAA,UAAO,GAAoC;IAG3C,EAFE,IAAA,EAAM,UAAA,EAAW;CAGlB,CAFC;;;;AAED,UAAD,CAAA,cAAC,GAAA,MAAA,EAKA,CAAC,AAGF,AAQC;;ADvCM,IAAI,aAAa,GAAQ,EAAE,CAAC;AACnC,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;AACvB,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACzB,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1B,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;AACvB,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;AACxB,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACzC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3C,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACzC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAC/C,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACjD,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3C,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAG7C,AAAO,IAAI,UAAU,GAAQ,EAAE,CAAC;AAChC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACtB,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,UAAU,CAAC,eAAe,GAAG,CAAC,CAAC;AAC/B,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;AACvB,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;AACzB,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACzC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACrC,UAAU,CAAC,UAAU,CAAC,eAAe,CAAC,GAAG,iBAAiB,CAAC;AAC3D,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AAC3C,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACrC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;AAG/C,AAAO,IAAI,YAAY,GAAQ,EAAE,CAAC;AAClC,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;AACtB,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;AACzB,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAC3C,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACzC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AAC/C,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAC3C,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAG7C,AAAO,IAAI,WAAW,GAAQ,EAAE,CAAC;AACjC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;AAC1B,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,YAAY,GAAG,CAAC,CAAC;AAC7B,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;AACjD,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;AAGvD,AAAO,IAAI,mBAAmB,GAAQ,EAAE,CAAC;AACzC,mBAAmB,CAAC,IAAI,GAAG,CAAC,CAAC;AAC7B,mBAAmB,CAAC,IAAI,GAAG,CAAC,CAAC;AAC7B,mBAAmB,CAAC,WAAW,GAAG,CAAC,CAAC;AACpC,mBAAmB,CAAC,IAAI,GAAG,CAAC,CAAC;AAC7B,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvD,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvD,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;AACrE,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;;ADvEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,AAAA,MAAA,OAAA,CAAA;;;;IAYA,WAAA,CAEG,OAAoD,EAFvD;;;;QARA,IAAA,CAAA,QAKG,GAAA,IAAA,GAAA,EAAA,CAAA;;;;QADH,IAAA,CAAA,gBAGG,GAAA,IAAA,GAAA,EAAA,CAAA;QAEC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QAED,IAAI,OAAO,YAAY,OAAO,EAAE;YAC9B,OAAO,CAAC,OAAO,CAAC,CAAC,MAAgB,EAAE,IAAY,KAArD;gBACQ,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;aACnD,CAAC,CAAC;YACH,OAAO;SACR;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAY,KAA9C;YACM,MAAM,MAAM,GAAa,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACxF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAClB,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;SACnD,CAAC,CAAC;KACJ;;;;;;IAMH,OACG,wBAAA,CAAA,aAAA,EADH;QACI,uBACM,OAAA,GAAU,IAAI,OAAA,EAAQ,CAAE;QAC9B,aAAa,CACC,KAAC,CAAK,IAAC,CAAI,CAAC,OAAC,CAAO,IAAC,IADvC;YACM,uBACM,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,GAAC,CAAG,CAAC;YAAhC,IAAI,KACC,GAAO,CAAA,EAAG;gBAAb,uBACM,IAAA,GAAO,IAAA,CAAK,KAAC,CAAK,CAAC,EAAE,KAAA,CAAM,CAAC;gBAAlC,uBACM,KAAA,GAAQ,IAAA,CAAK,KAAC,CAAK,KAAC,GAAO,CAAA,CAAE,CAAC,IAAC,EAAI,CAAE;gBAA3C,OAAO,CACC,GAAC,CAAG,IAAC,EAAK,KAAA,CAAM,CAAC;aAA1B;SACF,CACC,CAAC;QACH,OACO,OAAA,CAAQ;KAAhB;;;;;;;IAOH,MADG,CAAA,IAAA,EAAA,KAAA,EACH;QACI,uBADM,MAAA,GAAS,IAAA,CAAK,MAAC,CAAM,IAAC,CAAI,CAAC;QAGjC,IAAI,MADC,KAAU,IAAA,EAAM;YAEnB,IAAI,CADC,GAAC,CAAG,IAAC,EAAK,KAAA,CAAM,CAAC;SAEvB;aADM;YAEL,MAAM,CADC,IAAC,CAAI,KAAC,CAAK,CAAC;SAEpB;KACF;;;;;;IAMH,MAFG,CAAA,IAAA,EAEH;QACI,uBAFM,MAAA,GAAS,IAAA,CAAK,WAAC,EAAW,CAAE;QAGlC,IAAI,CAFC,gBAAC,CAAgB,MAAC,CAAM,MAAC,CAAM,CAAC;QAGrC,IAAI,CAFC,QAAC,CAAQ,MAAC,CAAM,MAAC,CAAM,CAAC;KAG9B;;;;;IAKH,OALG,CAAA,EAAA,EAKH;QAEI,IAAI,CALC,QAAC,CAAQ,OAAC,CAMX,CAAC,MALC,EAAO,MAAA,KAAW,EAAA,CAAG,MAAC,EAAO,IAAA,CAAK,gBAAC,CAAgB,GAAC,CAAG,MAAC,CAAM,EAAE,IAAA,CAAK,QAAC,CAAQ,CAAC,CAAC;KAMvF;;;;;;IAMH,GANG,CAAA,IAAA,EAMH;QACI,uBANM,MAAA,GAAS,IAAA,CAAK,MAAC,CAAM,IAAC,CAAI,CAAC;QAQjC,IAAI,MANC,KAAU,IAAA,EAAM;YAOnB,OANO,IAAA,CAAK;SAOb;QAED,OANO,MAAA,CAAO,MAAC,GAAQ,CAAA,GAAI,MAAA,CAAO,CAAC,CAAC,GAAG,IAAA,CAAK;KAO7C;;;;;;IAMH,GAPG,CAAA,IAAA,EAOH,EAP+B,OAAO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,IAAC,CAAI,WAAC,EAAW,CAAE,CAAC,EAAC;;;;;IAY7E,IAPG,GAOH,EAPqB,OAAO,KAAA,CAAM,IAAC,CAAI,IAAC,CAAI,gBAAC,CAAgB,MAAC,EAAM,CAAE,CAAC,EAAC;;;;;;;IAcxE,GATG,CAAA,IAAA,EAAA,KAAA,EASH;QACI,IAAI,KATC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;YAUxB,IAAI,KATC,CAAK,MAAC,EAAO;gBAUhB,IAAI,CATC,QAAC,CAAQ,GAAC,CAAG,IAAC,CAAI,WAAC,EAAW,EAAG,CAAA,KAAE,CAAK,IAAC,CAAI,GAAC,CAAG,CAAC,CAAC,CAAC;aAU1D;SACF;aATM;YAUL,IAAI,CATC,QAAC,CAAQ,GAAC,CAAG,IAAC,CAAI,WAAC,EAAW,EAAG,CAAA,KAAE,CAAK,CAAC,CAAC;SAUhD;QACD,IAAI,CATC,sBAAC,CAAsB,IAAC,CAAI,CAAC;KAUnC;;;;;IAKH,MATG,GASH,EATyB,OAAO,KAAA,CAAM,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,MAAC,EAAM,CAAE,CAAC,EAAC;;;;IAapE,MAPG,GAOH;QACI,uBAPM,UAAA,GAAyC,EAAA,CAAG;QASlD,IAAI,CAPC,QAAC,CAAQ,OAAC,CAAO,CAAC,MAAe,EAAG,IAAM,KAOnD;YACM,uBAPM,KAAA,GAAkB,EAAA,CAAG;YAQ3B,MAAM,CAPC,OAAC,CAAO,CAAC,IAAI,KAAA,CAAM,IAAC,CAAI,GAAC,CAAG,CAAC,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC;YAQjD,UAAU,GAPC,IAAC,CAAI,gBAAC,CAAgB,GAAC,CAAG,IAAC,CAAI,GAAG,GAAG,KAAA,CAAM;SAQvD,CAPC,CAAC;QASH,OAPO,UAAA,CAAW;KAQnB;;;;;;IAMH,MARG,CAAA,IAAA,EAQH;QACI,OARO,IAAA,CAAK,GAAC,CAAG,IAAC,CAAI,GAAG,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,IAAC,CAAI,WAAC,EAAW,CAAE,IAAI,IAAA,GAAO,IAAA,CAAK;KAS9E;;;;;IAKH,OARG,GAQH,EARc,MAAM,IAAI,KAAA,CAAM,sDAAC,CAAsD,CAAC,EAAC;;;;;IAEpF,sBAAA,CAAA,IAAA,EAAH;QAYI,uBAXM,MAAA,GAAS,IAAA,CAAK,WAAC,EAAW,CAAE;QAalC,IAAI,CAXC,IAAC,CAAI,gBAAC,CAAgB,GAAC,CAAG,MAAC,CAAM,EAAE;YAYtC,IAAI,CAXC,gBAAC,CAAgB,GAAC,CAAG,MAAC,EAAO,IAAA,CAAK,CAAC;SAYzC;KACF;CACF,AAED,AAWC;;ADjND;;;;;;;AASA,AAEA,AACA,AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,AAAA,MAAA,eAAA,CAAA;;;;IA0BA,WAAA,CADG,IACH,GAD0C,EAAA,EAC1C;QACI,MAAM,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAC,GAAG,IAAI,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,IAAI,CAAC;QACzD,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;KACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BH,KAFG,CAAA,OAAA,EAEH;QACI,OAFO,IAAI,eAAA,CAAgB;YAGzB,IAAI,EAFE,OAAA,IAAW,OAAA,CAAQ,IAAC,IAAO,IAAA,GAAO,OAAA,CAAQ,IAAC,GAAM,IAAA,CAAK,IAAC;YAG7D,MAAM,EAFE,OAAA,IAAW,OAAA,CAAQ,MAAC,IAAS,IAAA,GAAO,OAAA,CAAQ,MAAC,GAAQ,IAAA,CAAK,MAAC;YAGnE,OAAO,EAFE,OAAA,IAAW,OAAA,CAAQ,OAAC,IAAU,IAAA,GAAO,OAAA,CAAQ,OAAC,GAAS,IAAA,CAAK,OAAC;YAGtE,UAAU,EAFE,OAAA,IAAW,OAAA,CAAQ,UAAC,IAAa,IAAA,GAAO,OAAA,CAAQ,UAAC,GAAY,IAAA,CAAK,UAAC;YAG/E,IAAI,EAFE,OAAA,IAAW,OAAA,CAAQ,IAAC,IAAO,IAAA,GAAO,OAAA,CAAQ,IAAC,GAAM,IAAA,CAAK,IAAC;YAG7D,GAAG,EAFE,OAAA,IAAW,OAAA,CAAQ,GAAC,IAAM,IAAA,GAAO,OAAA,CAAQ,GAAC,GAAK,IAAA,CAAK,GAAC;SAG3D,CAFC,CAAC;KAGJ;CACF;AAED,AA+BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,AAAA,MAAA,mBAhCC,SAAA,eAAA,CAgCD;IACA,WAAA,GAAA;QACI,KAAK,CAAC,EAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,OAAO,EAAE,EAAC,CAAC,CAAC;KAC5F;;AA/BI,mBAAP,CAAA,UAAO,GAAoC;IAiC3C,EAhCE,IAAA,EAAM,UAAA,EAAW;CAiClB,CAhCC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA,EAmCA,CAAC,AAGF,AAQC;;ADvND;;;;;;;;;;;;;;;;AAsBA,AAAA,MAAA,iBAAA,CAAA;;;;;;IAMA,gBAPoD,CAAA,OAAA,EAOpD,GAPoD;CAA2C;;;;;;;AAc/F,AAAA,MAAA,UAAA,CAAA;CAIC;AAED,AASA;;;;;;AAMA,AAAA,MAAA,YAAA,CAAA;;;;;;IAMA,gBAvB+C,CAAA,GAAA,EAuB/C,GAvB+C;CAAqC;;ADvCpF;;;;;;;AASA,AACA;;;;AAIA,AAAA,SAAA,mBAAA,CAJC,MAAA,EAID;IACE,IAAI,OAJO,MAAA,KAAW,QAAA;QAAU,OAAO,MAAA,CAAO;IAM9C,QAAQ,MAJC,CAAM,WAAC,EAAW;QAKzB,KAJK,KAAA;YAKH,OAJO,aAAA,CAAc,GAAC,CAAG;QAK3B,KAJK,MAAA;YAKH,OAJO,aAAA,CAAc,IAAC,CAAI;QAK5B,KAJK,KAAA;YAKH,OAJO,aAAA,CAAc,GAAC,CAAG;QAK3B,KAJK,QAAA;YAKH,OAJO,aAAA,CAAc,MAAC,CAAM;QAK9B,KAJK,SAAA;YAKH,OAJO,aAAA,CAAc,OAAC,CAAO;QAK/B,KAJK,MAAA;YAKH,OAJO,aAAA,CAAc,IAAC,CAAI;QAK5B,KAJK,OAAA;YAKH,OAJO,aAAA,CAAc,KAAC,CAAK;KAK9B;IACD,MAJM,IAAI,KAAA,CAAM,CAIlB,oCAAA,EAJmB,MAAuC,CAI1D,mBAAA,CAJgE,CAAqB,CAAC;CAKrF;AAED,AAJO,MAAM,SAAA,GAAY,CAAA,MAAS,MAAoB,MAAE,IAAS,GAAA,IAAO,MAAA,GAAS,GAAA,CAAI,CAAC;;;;;AAStF,AAAA,SAAA,cAAA,CAPC,GAAA,EAOD;IACE,IAAI,aAPC,IAAgB,GAAA,EAAK;QAQxB,OAPO,GAAA,CAAI,WAAC,CAAW;KAQxB;IACD,IAAI,kBAPC,CAAkB,IAAC,CAAI,GAAC,CAAG,qBAAC,EAAqB,CAAE,EAAE;QAQxD,OAPO,GAAA,CAAI,iBAAC,CAAiB,eAAC,CAAe,CAAC;KAQ/C;IACD,OAPO,IAAA,CAAK;CAQb;;;;;AAKD,AAAA,AAMC;;;;;AAKD,AAAA,SAAA,mBAAA,CAZC,KAAA,EAYD;IACE,uBAZM,IAAA,GAAO,IAAI,WAAA,CAAY,KAAC,CAAK,MAAC,CAAM,CAAC;IAa3C,KAAK,qBAZI,CAAA,GAAI,CAAA,mBAAG,MAAA,GAAS,KAAA,CAAM,MAAC,EAAO,CAAA,GAAI,MAAA,EAAQ,CAAA,EAAE,EAAG;QAatD,IAAI,CAZC,CAAC,CAAC,GAAG,KAAA,CAAM,UAAC,CAAU,CAAC,CAAC,CAAC;KAa/B;IACD,OAZO,IAAA,CAAK,MAAC,CAAM;CAapB;;ADtED;;;;;;;;;AASA,SAAA,WAAA,CAFC,SAED,GAFC,EAAA,EAED;IACE,uBAFM,GAAA,GAAM,IAAI,GAAA,EAAqB,CAAG;IAGxC,IAAI,SAFC,CAAS,MAAC,GAAQ,CAAA,EAAG;QAGxB,uBAFM,MAAA,GAAmB,SAAA,CAAU,KAAC,CAAK,GAAC,CAAG,CAAC;QAG9C,MAAM,CAFC,OAAC,CAAO,CAAC,KAAO,KAE3B;YACM,uBAFM,KAAA,GAAQ,KAAA,CAAM,OAAC,CAAO,GAAC,CAAG,CAAC;YAGjC,MAFM,CAAA,GAAE,EAAI,GAAA,CAAI,GAGZ,KAAK,IAFI,CAAA,CAAE,GAAG,CAAA,KAAE,EAAM,EAAA,CAAG,GAAG,CAAA,KAAE,CAAK,KAAC,CAAK,CAAC,EAAE,KAAA,CAAM,EAAE,KAAA,CAAM,KAAC,CAAK,KAAC,GAAO,CAAA,CAAE,CAAC,CAAC;YAGhF,uBAFM,IAAA,GAAO,GAAA,CAAI,GAAC,CAAG,GAAC,CAAG,IAAI,EAAA,CAAG;YAGhC,IAAI,CAFC,IAAC,CAAI,GAAC,CAAG,CAAC;YAGf,GAAG,CAFC,GAAC,CAAG,GAAC,EAAI,IAAA,CAAK,CAAC;SAGpB,CAFC,CAAC;KAGJ;IACD,OAFO,GAAA,CAAI;CAGZ;;;;;AAKD,AAAA,MAAA,YAAA,CAAA;;;;;IAKA,SAPG,CAAA,CAAA,EAOH,EAPiC,OAAO,gBAAA,CAAiB,CAAC,CAAC,CAAC,EAAC;;;;;IAY7D,WAVG,CAAA,CAAA,EAUH,EAVmC,OAAO,gBAAA,CAAiB,CAAC,CAAC,CAAC,EAAC;CAW9D;;;;;AAKD,SAAA,gBAAA,CAbC,CAAA,EAaD;IACE,OAbO,kBAAA,CAAmB,CAAC,CAAC;SAcvB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI,CAAC;CAc5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCD,AAAA,MAAA,eAAA,CAAA;;;;;IAMA,WAAA,CAfa,SAeb,GAfiC,EAAA,EAAY,YAe7C,GAf0E,IAAI,YAAA,EAAa,EAe3F;QAfa,IAAb,CAAA,SAAa,GAAA,SAAA,CAAoB;QAAY,IAA7C,CAAA,YAA6C,GAAA,YAAA,CAA8C;QAkBvF,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;KACzC;;;;IAIH,KAnBG,GAmBH;QACI,uBAnBM,KAAA,GAAQ,IAAI,eAAA,CAAgB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,CAAC;QAoBzD,KAAK,CAnBC,SAAC,CAAS,IAAC,CAAI,CAAC;QAoBtB,OAnBO,KAAA,CAAM;KAoBd;;;;;IAKH,GAtBG,CAAA,KAAA,EAsBH,EAtBgC,OAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,CAAC,EAAC;;;;;IA2BlE,GAzBG,CAAA,KAAA,EAyBH;QACI,uBAzBM,WAAA,GAAc,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,CAAC;QA2B9C,OAzBO,KAAA,CAAM,OAAC,CAAO,WAAC,CAAW,GAAG,WAAA,CAAY,CAAC,CAAC,GAAG,IAAA,CAAK;KA0B3D;;;;;IAKH,MA5BG,CAAA,KAAA,EA4BH,EA5BoC,OAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG,EAAC;;;;;;IAkC5E,GAhCG,CAAA,KAAA,EAAA,GAAA,EAgCH;QACI,IAAI,GAhCC,KAAO,KAAK,CAAA,IAAK,GAAA,KAAQ,IAAA,EAAM;YAiClC,IAAI,CAhCC,MAAC,CAAM,KAAC,CAAK,CAAC;YAiCnB,OAAO;SACR;QACD,uBAhCM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG;QAiC7C,IAAI,CAhCC,MAAC,GAAQ,CAAA,CAAE;QAiChB,IAAI,CAhCC,IAAC,CAAI,GAAC,CAAG,CAAC;QAiCf,IAAI,CAhCC,SAAC,CAAS,GAAC,CAAG,KAAC,EAAM,IAAA,CAAK,CAAC;KAiCjC;;;;;IAKH,MA7BG,CAAA,YAAA,EA6BH;QACI,YAAY,CA7BC,SAAC,CAAS,OAAC,CAAO,CAAC,KAAC,EAAM,KAAA,KA6B3C;YACM,uBA7BM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG;YA8B7C,IAAI,CA7BC,MAAC,GAAQ,CAAA,CAAE;YA8BhB,IAAI,CA7BC,IAAC,CAAI,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;YA8BpB,IAAI,CA7BC,SAAC,CAAS,GAAC,CAAG,KAAC,EAAM,IAAA,CAAK,CAAC;SA8BjC,CA7BC,CAAC;KA8BJ;;;;;;IAMH,MAjCG,CAAA,KAAA,EAAA,GAAA,EAiCH;QACI,IAAI,GAjCC,KAAO,KAAK,CAAA,IAAK,GAAA,KAAQ,IAAA;YAAM,OAAA;QAkCpC,uBAjCM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG;QAkC7C,IAAI,CAjCC,IAAC,CAAI,GAAC,CAAG,CAAC;QAkCf,IAAI,CAjCC,SAAC,CAAS,GAAC,CAAG,KAAC,EAAM,IAAA,CAAK,CAAC;KAkCjC;;;;;IAKH,SA7BG,CAAA,YAAA,EA6BH;QACI,YAAY,CA7BC,SAAC,CAAS,OAAC,CAAO,CAAC,KAAC,EAAM,KAAA,KA6B3C;YACM,uBA7BM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG;YA8B7C,KAAK,qBA7BI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,KAAA,CAAM,MAAC,EAAO,EAAA,CAAG,EAAE;gBA8BrC,IAAI,CA7BC,IAAC,CAAI,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;aA8BrB;YACD,IAAI,CA7BC,SAAC,CAAS,GAAC,CAAG,KAAC,EAAM,IAAA,CAAK,CAAC;SA8BjC,CA7BC,CAAC;KA8BJ;;;;;IAKH,UAxBG,CAAA,YAAA,EAwBH;QACI,YAAY,CAxBC,SAAC,CAAS,OAAC,CAAO,CAAC,KAAC,EAAM,KAAA,KAwB3C;YACM,uBAxBM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,KAAC,CAAK,IAAI,EAAA,CAAG;YAyB7C,IAAI,CAxBC,MAAC,GAAQ,CAAA,CAAE;YAyBhB,KAAK,qBAxBI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,KAAA,CAAM,MAAC,EAAO,EAAA,CAAG,EAAE;gBAyBrC,IAAI,CAxBC,IAAC,CAAI,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;aAyBrB;YACD,IAAI,CAxBC,SAAC,CAAS,GAAC,CAAG,KAAC,EAAM,IAAA,CAAK,CAAC;SAyBjC,CAxBC,CAAC;KAyBJ;;;;IAIH,QA1BG,GA0BH;QACI,uBA1BM,UAAA,GAAuB,EAAA,CAAG;QA2BhC,IAAI,CA1BC,SAAC,CAAS,OAAC,CAAO,CAAC,MAAC,EAAO,CAAA,KA0BpC;YACM,MAAM,CA1BC,OAAC,CA2BJ,CAAC,IA1BI,UAAA,CAAW,IAAC,CA2Bb,IAAI,CA1BC,YAAC,CAAY,SAAC,CAAS,CAAC,CAAC,GAAG,GAAA,GAAM,IAAA,CAAK,YAAC,CAAY,WAAC,CAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SA2BnF,CA1BC,CAAC;QA2BH,OA1BO,UAAA,CAAW,IAAC,CAAI,GAAC,CAAG,CAAC;KA2B7B;;;;;IAKH,MA7BG,CAAA,KAAA,EA6BH,EA7BiC,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,KAAC,CAAK,CAAC,EAAC;CA8B9D,AAED,AAOC;;AD7ND;;;;;;;AASA,AACA,AACA;;;;;AAKA,AAAA,MAAA,IAAA,CAAA;;;;;IASA,IAAG,GAAH;QACI,IAAI,OAAO,IAAA,CAAK,KAAC,KAAS,QAAA,EAAU;YAClC,OAAO,IAAA,CAAK,KAAC,mBAAa,IAAC,CAAI,KAAC,EAAK,CAAC;SACvC;QAED,IAAI,IAAC,CAAI,KAAC,YAAgB,WAAA,EAAa;YACrC,OAAO,IAAA,CAAK,KAAC,CAAK,IAAC,CAAI,IAAC,EAAI,CAAE,CAAC;SAChC;QAED,OAAO,IAAA,CAAK,KAAC,CAAK;KACnB;;;;;;;;;;;;;;;;;IAiBH,IADG,CAAA,YACH,GADG,QAAA,EACH;QACI,IAAI,IADC,CAAI,KAAC,YAAgB,eAAA,EAAiB;YAEzC,OADO,IAAA,CAAK,KAAC,CAAK,QAAC,EAAQ,CAAE;SAE9B;QAED,IAAI,IADC,CAAI,KAAC,YAAgB,WAAA,EAAa;YAErC,QAAQ,YADC;gBAEP,KADK,QAAA;oBAEH,OADO,MAAA,CAAO,YAAC,CAAY,KAAC,CAAK,IAAC,EAAK,IAAI,WAAA,mBAAY,IAAC,CAAI,KAAS,EAAY,CAAC,CAAC;gBAErF,KADK,UAAA;oBAEH,OADO,MAAA,CAAO,YAAC,CAAY,KAAC,CAAK,IAAC,EAAK,IAAI,UAAA,mBAAW,IAAC,CAAI,KAAS,EAAY,CAAC,CAAC;gBAEpF;oBACE,MADM,IAAI,KAAA,CAAM,CAC1B,gCAAA,EAD2B,YAAmC,CAC9D,CAD0E,CAAE,CAAC;aAEtE;SACF;QAED,IAAI,IADC,CAAI,KAAC,IAAQ,IAAA,EAAM;YAEtB,OADO,EAAA,CAAG;SAEX;QAED,IAAI,OADO,IAAA,CAAK,KAAC,KAAS,QAAA,EAAU;YAElC,OADO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,KAAC,EAAM,IAAA,EAAM,CAAA,CAAE,CAAC;SAE5C;QAED,OADO,IAAA,CAAK,KAAC,CAAK,QAAC,EAAQ,CAAE;KAE9B;;;;;IAKH,WADG,GACH;QACI,IAAI,IADC,CAAI,KAAC,YAAgB,WAAA,EAAa;YAErC,QADoB,IAAC,CAAI,KAAC,EAAK;SAEhC;QAED,OADO,mBAAA,CAAoB,IAAC,CAAI,IAAC,EAAI,CAAE,CAAC;KAEzC;;;;;IAKH,IADG,GACH;QACI,IAAI,IADC,CAAI,KAAC,YAAgB,IAAA,EAAM;YAE9B,QADa,IAAC,CAAI,KAAC,EAAK;SAEzB;QAED,IAAI,IADC,CAAI,KAAC,YAAgB,WAAA,EAAa;YAErC,OADO,IAAI,IAAA,CAAK,CAAC,IAAC,CAAI,KAAC,CAAK,CAAC,CAAC;SAE/B;QAED,MADM,IAAI,KAAA,CAAM,0DAAC,CAA0D,CAAC;KAE7E;CACF,AAED,AAMC;;ADhHD;;;;;;;AAYA,AAGA;;;;;;;;;;;;;;;;;;;;AAoBA,AAAA,MAAA,QACC,SAAA,IAAA,CADD;;;;IAmDA,WAAA,CADG,eAA4B,EAC/B;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,MAAQ,CAAC;QACvC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,IAAM,CAAC;QACnC,IAAI,CAAC,GAAG,GAAG,eAAe,CAAC,GAAK,CAAC;KAClC;;;;IAIH,QAHG,GAGH;QACI,OAHO,CAGX,sBAAA,EAHW,IAA0B,CAAI,MAAC,CAG1C,CAAA,EAHgD,IAAI,CAAI,UAAC,CAGzD,UAAA,EAHmE,IAAa,CAAI,GAAC,CAGrF,CAHwF,CAAE;KAIvF;CACF,AAED,AAwDC;;ADhKD;;;;;;;AASA,AAEA,IADI,cAAA,GAAiB,CAAA,CAAE;AAEvB,AADO,MAAM,UAAA,GAAa,cAAA,CAAe;AAEzC,IADI,iBAAA,GAA+C,IAAA,CAAK;;;;AAKxD,SAAA,oBAAA,GAAA;IACE,uBAHM,CAAA,GAA0B,OAAO,MAAA,IAAU,QAAA,GAAW,MAAA,GAAS,EAAA,CAAG;IAIxE,IAAI,iBAHC,KAAqB,IAAA,EAAM;QAI9B,iBAAiB,GAHG,CAAA,CAAE,UAAC,CAAU,GAAG,EAAA,CAAG;KAIxC;IACD,OAHO,iBAAA,CAAkB;CAI1B;AACD,AAAA,MAAA,YAAA,CAAA;;;;;IAKA,KAHG,CAAA,GAAA,EAGH;QACI,uBAHM,IAAA,GAAO,QAAA,CAAS,aAAC,CAAa,QAAC,CAAQ,CAAC;QAI9C,IAAI,CAHC,GAAC,GAAK,GAAA,CAAI;QAIf,OAHO,IAAA,CAAK;KAIb;;;;IAIH,aALG,GAKH,EAL4B,OAAO,CAKnC,KAAA,EALmC,cAAS,EAAc,CAK1D,CAL4D,CAAE,EAAC;;;;;IAU/D,eARG,CAAA,EAAA,EAQH,EARwC,OAAO,CAQ/C,EAR+C,UAAI,CAQnD,CAAA,EAR6D,EAAI,CAQjE,SAAA,CARmE,CAAW,EAAC;;;;;;IAc/E,gBAZG,CAAA,EAAA,EAAA,UAAA,EAYH;QACI,uBAZM,WAAA,GAAc,oBAAA,EAAqB,CAAE;QAa3C,WAAW,CAZC,EAAC,CAAE,GAAG,UAAA,CAAW;KAa9B;;;;;IAKH,gBAfG,CAAA,EAAA,EAeH;QACI,uBAfM,WAAA,GAAc,oBAAA,EAAqB,CAAE;QAgB3C,WAAW,CAfC,EAAC,CAAE,GAAG,IAAA,CAAK;KAgBxB;;;;;IAKH,IAjBG,CAAA,IAAA,EAiBH,EAjBoB,QAAA,CAAS,IAAC,CAAI,WAAC,oBAAkB,IAAC,GAAK,CAAC,EAAC;;;;;IAsB7D,OAnBG,CAAA,IAAA,EAmBH;QACI,IAAI,IAnBC,CAAI,UAAC,EAAW;YAoBnB,IAAI,CAnBC,UAAC,CAAU,WAAC,oBAAkB,IAAC,GAAK,CAAC;SAoB3C;KACF;;AAlBI,YAAP,CAAA,UAAO,GAAoC;IAoB3C,EAnBE,IAAA,EAAM,UAAA,EAAW;CAoBlB,CAnBC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA,EAsBA,CAAC,AAGF,AAQC;;AD5FD;;;;;;;AASA,AACA,AAGA,AACA,AACA,AAEA,AAEA,AAEA,MADM,qBAAA,GAAwB,gDAAA,CAAiD;AAE/E,MADM,sBAAA,GAAyB,6CAAA,CAA8C;;;;;;;AAQ7E,AAAA,MAAA,eAAA,CAAA;;;;;;;;IAoBA,QADY,CAAA,IAAA,EACZ,GADY;CAEX;AAED,AAkBA,AAAA,MAAA,gBAnBC,SAAA,eAAA,CAmBD;;;;;;IAUA,WAAA,CACM,GAvBK,EAAiB,IAAM,EAAsB,mBAAsB,EAsB9E;QAII,KAAK,EAAE,CAAC;QA1BgB,IAA5B,CAAA,IAA4B,GAAA,IAAA,CAAM;QAAsB,IAAxD,CAAA,mBAAwD,GAAA,mBAAA,CAAsB;QAHpE,IAAV,CAAA,SAAU,GAAqB,KAAA,CAAM;QA8BjC,IAAI,GAAG,CAAC,MAAM,KAAK,aAAa,CAAC,GAAG,EAAE;YACpC,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;SAC7C;QACD,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAW,CAAC,gBAAoC,KAAlF;YAEM,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;YACrC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3C,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;;;YAIhC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,GAAG,GAAW,GAAG,CAAC,GAAG,CAAC;YAC1B,IAAI,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;gBACxC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAA9C,CAAA,EAAkD,QAAQ,CAA1D,CAAA,CAA6D,CAAC,CAAC;aACxD;iBAAM,IAAI,GAAG,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE;gBACvF,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAxE,CAAA,EAA4E,QAAQ,CAApF,CAAsF,CAAC;aAChF;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE9C,MAAM,MAAM,GAAG,CAAC,KAAY,KAAlC;gBACQ,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS;oBAAE,OAAO;gBACrD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,IAAI,eAAe,GACf,IAAI,eAAe,CAAC,EAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,GAAG,EAAC,CAAC,CAAC;oBACtF,IAAI,mBAAmB,EAAE;wBACvB,eAAe,GAAG,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;qBAC9D;oBACD,gBAAgB,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;oBACtD,OAAO;iBACR;gBAED,IAAI,eAAe,GAAG,IAAI,eAAe,CAAC,EAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,GAAG,EAAC,CAAC,CAAC;gBAC3E,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBAC5B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBACnE;gBAED,gBAAgB,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;gBACrD,gBAAgB,CAAC,QAAQ,EAAE,CAAC;aAC7B,CAAC;YAEF,MAAM,OAAO,GAAG,CAAC,KAAY,KAAnC;gBACQ,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,SAAS;oBAAE,OAAO;gBACrD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrB,IAAI,eAAe,GAAG,IAAI,eAAe,CAAC,EAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,EAAC,CAAC,CAAC;gBAC3F,IAAI,mBAAmB,EAAE;oBACvB,eAAe,GAAG,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBAC9D;gBACD,gBAAgB,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;aACvD,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAElB,OAAO,MAAb;gBACQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;gBACvC,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC3C,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3B,CAAC;SACH,CAAC,CAAC;KACJ;;;;;IAKH,QA5BG,CAAA,IAAA,EA4BH;;QAEI,IAAI,CA5BC,SAAC,GAAW,IAAA,CAAK;QA6BtB,IAAI,CA5BC,IAAC,CAAI,gBAAC,CAAgB,IAAC,CAAI,GAAC,CAAG,CAAC;QA6BrC,IAAI,IA5BC,CAAI,UAAC,KAAc,UAAA,CAAW,SAAC;YAAU,OAAA;QAqBhD,IAAA,CApBO,aAAC,GAAe,IAAA,CAAK;KA6B3B;CACF;AAED,AAeA;;;;;;AAMA,AAAA,MAAA,YA5CC,SAAA,iBAAA,CA4CD;CA5C+D;AA6C/D,AAAA,MAAA,aA1CC,SAAA,YAAA,CA0CD;;;;;IAKA,WAAA,CA9CsB,aAAe,EAAsB,oBAAsB,EA8CjF;QAEI,KAAK,EAAE,CAAC;QAhDU,IAAtB,CAAA,aAAsB,GAAA,aAAA,CAAe;QAAsB,IAA3D,CAAA,oBAA2D,GAAA,oBAAA,CAAsB;KAiD9E;;;;;IAKH,gBAlDG,CAAA,OAAA,EAkDH;QACI,OAlDO,IAAI,gBAAA,CAAiB,OAAC,EAAQ,IAAA,CAAK,aAAC,EAAc,IAAA,CAAK,oBAAC,CAAoB,CAAC;KAmDrF;;AAjDI,aAAP,CAAA,UAAO,GAAoC;IAmD3C,EAlDE,IAAA,EAAM,UAAA,EAAW;CAmDlB,CAlDC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA;IAqDD,EAAC,IAAI,EAAE,YAAY,GAAG;IACtB,EAAC,IAAI,EAAE,eAAe,GAAG;CACxB,CAAC,AAGF,AAYC;;ADtOD;;;;;;;AASA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AAEA,AACA,AAEA,MADM,WAAA,GAAc,cAAA,CAAe;;;;;;;;;;;AAYnC,AAAA,MAAA,aAAA,CAAA;;;;;;IAaA,WAAA,CALG,GAAgB,EAAS,UAAY,EAAY,mBAAsB,EAK1E;QACI,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAW,CAAC,gBAAoC,KAAlF;YACM,MAAM,IAAI,GAAmB,UAAU,CAAC,KAAK,EAAE,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5D,IAAI,GAAG,CAAC,eAAe,IAAI,IAAI,EAAE;gBAC/B,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,CAAC;aAC5C;;YAED,MAAM,MAAM,GAAG,MAArB;;gBAEQ,IAAI,MAAM,GAAW,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;gBAE9D,IAAI,IAAI,GAAQ,IAAI,CAAC;;gBAGrB,IAAI,MAAM,KAAK,GAAG,EAAE;;;;oBAIlB,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,QAAQ,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;;oBAGlF,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;wBAC5B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;qBACtC;iBACF;;;;gBAKD,IAAI,MAAM,KAAK,CAAC,EAAE;oBAChB,MAAM,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;iBACzB;gBAED,MAAM,OAAO,GAAY,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;;gBAExF,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC;gBAC5C,MAAM,UAAU,GAAW,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;gBAEnD,IAAI,eAAe,GAAG,IAAI,eAAe,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC;gBACpF,IAAI,mBAAmB,IAAI,IAAI,EAAE;oBAC/B,eAAe,GAAG,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBAC9D;gBACD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC/C,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,QAAQ,CAAC,EAAE,EAAE;oBACf,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;oBAEhC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;oBAC5B,OAAO;iBACR;gBACD,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;aAClC,CAAC;;YAEF,MAAM,OAAO,GAAG,CAAC,GAAe,KAAtC;gBACQ,IAAI,eAAe,GAAG,IAAI,eAAe,CAAC;oBACxC,IAAI,EAAE,GAAG;oBACT,IAAI,EAAE,YAAY,CAAC,KAAK;oBACxB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B,CAAC,CAAC;gBACH,IAAI,mBAAmB,IAAI,IAAI,EAAE;oBAC/B,eAAe,GAAG,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBAC9D;gBACD,gBAAgB,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;aACvD,CAAC;YAEF,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAEvC,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE;gBACvB,GAAG,CAAC,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;aAC7B;YACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC9B,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;aACnE;YACD,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,gBAAgB,CAAC,IAAM,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;YAGvF,IAAI,GAAG,CAAC,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;gBACzD,QAAQ,GAAG,CAAC,YAAY;oBACtB,KAAK,mBAAmB,CAAC,WAAW;wBAClC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;wBAClC,MAAM;oBACR,KAAK,mBAAmB,CAAC,IAAI;wBAC3B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;wBAC3B,MAAM;oBACR,KAAK,mBAAmB,CAAC,IAAI;wBAC3B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;wBAC3B,MAAM;oBACR,KAAK,mBAAmB,CAAC,IAAI;wBAC3B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;wBAC3B,MAAM;oBACR;wBACE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;iBACjE;aACF;YAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YAElC,OAAO,MAAb;gBACQ,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACzC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;aACd,CAAC;SACH,CAAC,CAAC;KACJ;;;;;;IAMH,sBATG,CAAA,GAAA,sBAAA,IAAA,wBASH;;QAEI,IAAI,GATC,CAAG,OAAC,IAAU,IAAA,IAAQ,GAAA,CAAI,OAAC,CAAO,GAAC,CAAG,cAAC,CAAc,IAAI,IAAA,EAAM;YAUlE,OAAO;SACR;;QAGD,QAAQ,GATC,CAAG,WAAC;YAUX,KATK,WAAA,CAAY,IAAC;gBAUhB,MAAM;YACR,KATK,WAAA,CAAY,IAAC;gBAUhB,IAAI,CATC,gBAAC,CAAgB,cAAC,EAAe,kBAAA,CAAmB,CAAC;gBAU1D,MAAM;YACR,KATK,WAAA,CAAY,IAAC;gBAUhB,IAAI,CATC,gBAAC,CAAgB,cAAC,EAAe,iDAAA,CAAkD,CAAC;gBAUzF,MAAM;YACR,KATK,WAAA,CAAY,IAAC;gBAUhB,IAAI,CATC,gBAAC,CAAgB,cAAC,EAAe,YAAA,CAAa,CAAC;gBAUpD,MAAM;YACR,KATK,WAAA,CAAY,IAAC;gBAUhB,uBATM,IAAA,GAAO,GAAA,CAAI,IAAC,EAAI,CAAE;gBAUxB,IAAI,IATC,CAAI,IAAC,EAAK;oBAUb,IAAI,CATC,gBAAC,CAAgB,cAAC,EAAe,IAAA,CAAK,IAAC,CAAI,CAAC;iBAUlD;gBACD,MAAM;SACT;KACF;CACF;AAED,AAaA;;;;;;;;;;;AAWA,AAAA,MAAA,kBAAA,CAAA;;;;;IAKA,WAAA,CAzBc,WAyBd,GAzBoC,YAAA,EAAsB,WAyB1D,GAzBgF,cAAA,EAyBhF;QAzBc,IAAd,CAAA,WAAc,GAAA,WAAA,CAAsB;QAAsB,IAA1D,CAAA,WAA0D,GAAA,WAAA,CAAsB;KAAe;;;;;IAgC/F,gBA9BG,CAAA,GAAA,EA8BH;QACI,uBA9BM,SAAA,GAAYE,OAAA,EAAO,CAAE,SAAC,CAAS,IAAC,CAAI,WAAC,CAAW,CAAC;QA+BvD,IAAI,SA9BC,EAAU;YA+Bb,GAAG,CA9BC,OAAC,CAAO,GAAC,CAAG,IAAC,CAAI,WAAC,EAAY,SAAA,CAAU,CAAC;SA+B9C;KACF;CACF;AAED,AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,AAAA,MAAA,UAAA,CAAA;;;;;;IAMA,WAAA,CAxCc,WAAa,EAAoB,oBAAsB,EACvD,aAAe,EAuC7B;QAxCc,IAAd,CAAA,WAAc,GAAA,WAAA,CAAa;QAAoB,IAA/C,CAAA,oBAA+C,GAAA,oBAAA,CAAsB;QACvD,IAAd,CAAA,aAAc,GAAA,aAAA,CAAe;KAAa;;;;;IA+C1C,gBA7CG,CAAA,OAAA,EA6CH;QACI,IAAI,CA7CC,aAAC,CAAa,gBAAC,CAAgB,OAAC,CAAO,CAAC;QA8C7C,OA7CO,IAAI,aAAA,CAAc,OAAC,EAAQ,IAAA,CAAK,WAAC,EAAY,IAAA,CAAK,oBAAC,CAAoB,CAAC;KA8ChF;;AA5CI,UAAP,CAAA,UAAO,GAAoC;IA8C3C,EA7CE,IAAA,EAAM,UAAA,EAAW;CA8ClB,CA7CC;;;;AAED,UAAD,CAAA,cAAC,GAAA,MAAA;IAgDD,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,eAAe,GAAG;IACzB,EAAC,IAAI,EAAE,YAAY,GAAG;CACrB,CAAC,AAGF,AAcC;;ADxTD;;;;;;;AASA,AAEA,AACA,AACA,AAEA,AACA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAA,MAAA,cAAA,CAAA;;;;;IA0BA,IAAG,MAAA,GAAH,EAAkC,OAAO,IAAA,CAAK,MAAC,CAAM,EAAC;;;;;;IAMtD,IAFG,MAAA,CAAA,MAAA,EAEH,EAFwC,IAAA,CAAK,MAAC,GAAQ,MAAA,CAAO,EAAC;;;;IAc9D,WAAA,CAHG,IAGH,GAHyC,EAAA,EAGzC;QACI,MAAM,EAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,YAAY,EAAC,GAAG,IAAI,CAAC;QACzF,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,GAAG,mBAAmB,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAClE,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,GAAG,eAAe,GAAG,IAAI,CAAC;QACxE,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,GAAG,YAAY,GAAG,IAAI,CAAC;KAChE;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BH,KAJG,CAAA,OAAA,EAIH;QACI,OAJO,IAAI,cAAA,CAAe;YAKxB,MAAM,EAJE,OAAA,IAAW,OAAA,CAAQ,MAAC,IAAS,IAAA,GAAO,OAAA,CAAQ,MAAC,GAAQ,IAAA,CAAK,MAAC;YAKnE,OAAO,EAJE,OAAA,IAAW,OAAA,CAAQ,OAAC,IAAU,IAAA,GAAO,OAAA,CAAQ,OAAC,GAAS,IAAI,OAAA,CAAQ,IAAC,CAAI,OAAC,CAAO;YAKzF,IAAI,EAJE,OAAA,IAAW,OAAA,CAAQ,IAAC,IAAO,IAAA,GAAO,OAAA,CAAQ,IAAC,GAAM,IAAA,CAAK,IAAC;YAK7D,GAAG,EAJE,OAAA,IAAW,OAAA,CAAQ,GAAC,IAAM,IAAA,GAAO,OAAA,CAAQ,GAAC,GAAK,IAAA,CAAK,GAAC;YAK1D,MAAM,EAJE,OAAA,IAAW,IAAA,CAAK,kBAAC,CAAkB,OAAC,CAAO,MAAC,IAAS,OAAA,CAAQ,MAAC,CAAM;YAK5E,eAAe,EAJE,OAAA,IAAW,OAAA,CAAQ,eAAC,IAAkB,IAAA,GAAO,OAAA,CAAQ,eAAC;gBAKT,IAAI,CAJC,eAAC;YAKpE,YAAY,EAJE,OAAA,IAAW,OAAA,CAAQ,YAAC,IAAe,IAAA,GAAO,OAAA,CAAQ,YAAC;gBAKT,IAAI,CAJC,YAAC;SAK/D,CAJC,CAAC;KAKJ;;;;;IAFA,kBAAA,CAAA,MAQ8B,EARjC;QASI,IAAI,CAPC,MAAC;YAAO,OAAO,IAAA,CAAK,MAAC,CAAM;QAShC,IAAI,MAPC,YAAiB,eAAA,EAAiB;YAQrC,OAPO,MAAA,CAAO,KAAC,EAAK,CAAE;SAQvB;QAED,IAAI,OAPO,MAAA,KAAW,QAAA,EAAU;YAQ9B,OAPO,IAAI,eAAA,CAAgB,MAAC,CAAM,CAAC;SAQpC;QAED,OAPO,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,CAAC;KAQlC;;;;;IALA,YAAA,CAAA,SAAH,GAAG,EAAA,EAAH;QAWI,uBAVM,MAAA,GAAS,IAAI,eAAA,EAAgB,CAAE;QAWrC,MAAM,CAVC,IAAC,CAAI,SAAC,CAAS,CAAC,OAAC,CAAO,CAAC,GAAK,KAUzC;YACM,uBAVM,KAAA,GAAmB,SAAA,CAAU,GAAC,CAAG,CAAC;YAWxC,IAAI,KAVC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;gBAWxB,KAAK,CAVC,OAAC,CAAO,CAAC,IAAM,KAAQ,IAAA,CAAK,YAAC,CAAY,GAAC,EAAI,IAAA,EAAM,MAAA,CAAO,CAAC,CAAC;aAWpE;iBAVM;gBAWL,IAAI,CAVC,YAAC,CAAY,GAAC,EAAI,KAAA,EAAO,MAAA,CAAO,CAAC;aAWvC;SACF,CAVC,CAAC;QAWH,OAVO,MAAA,CAAO;KAWf;;;;;;;IARA,YAAA,CAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAH;QAgBI,IAAI,OAfO,KAAA,KAAU,QAAA,EAAU;YAgB7B,KAAK,GAfG,IAAA,CAAK,SAAC,CAAS,KAAC,CAAK,CAAC;SAgB/B;QACD,MAAM,CAfC,MAAC,CAAM,GAAC,EAAI,KAAA,CAAM,CAAC;KAgB3B;CACF;AAED,AAoCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,AAAA,MAAA,kBAlDC,SAAA,cAAA,CAkDD;IACA,WAAA,GAAA,EAlDG,KAAA,CAAA,EAAA,MAAA,EAAA,aAAA,CAAA,GAAA,EAAA,OAAA,EAAA,IAAA,OAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;AACI,kBAAP,CAAA,UAAO,GAAoC;IAmD3C,EAlDE,IAAA,EAAM,UAAA,EAAW;CAmDlB,CAlDC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA,EAqDA,CAAC,AAGF,AAQC;;ADnRD;;;;;;;AASA,AACA,AACA,AACA,AAEA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,AAAA,MAAA,OAEC,SAAA,IAAA,CAFD;;;;IA4BA,WAAA,CATG,cAA2B,EAS9B;QACI,KAAK,EAAE,CAAC;;QAER,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,GAAK,CAAC;QAChC,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC;QACjE,IAAI,SAAS,EAAE;YACb,IAAI,MAAc,CAAC;YACnB,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,EAAE,SAAS,YAAY,eAAe,CAAC,EAAE;gBAC5E,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;aAChD;iBAAM;gBACL,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;aAC/B;YACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,IAAI,MAAM,GAAG,GAAG,CAAC;gBACjB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;oBAC/B,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;iBAC5D;;gBAED,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;aAClC;SACF;QACD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC,cAAc,CAAC,MAAQ,CAAC,CAAC;;;QAG3D,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,eAAiB,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAc,CAAC;KACnD;;;;;IAKH,iBATG,GASH;QACI,QAAQ,IATC,CAAI,OAAC,CAAO,GAAC,CAAG,cAAC,CAAc;YAUtC,KATK,kBAAA;gBAUH,OATO,WAAA,CAAY,IAAC,CAAI;YAU1B,KATK,mCAAA;gBAUH,OATO,WAAA,CAAY,IAAC,CAAI;YAU1B,KATK,qBAAA;gBAUH,OATO,WAAA,CAAY,SAAC,CAAS;YAU/B,KATK,YAAA,CAAa;YAUlB,KATK,WAAA;gBAUH,OATO,WAAA,CAAY,IAAC,CAAI;YAU1B,KATK,0BAAA;gBAUH,OATO,IAAA,CAAK,KAAC,YAAgBF,aAAA,GAAc,WAAA,CAAY,YAAC,GAAc,WAAA,CAAY,IAAC,CAAI;YAUzF;gBACE,OATO,IAAA,CAAK,yBAAC,EAAyB,CAAE;SAU3C;KACF;;;;;IAKH,yBATG,GASH;QACI,IAAI,IATC,CAAI,KAAC,IAAQ,IAAA,EAAM;YAUtB,OATO,WAAA,CAAY,IAAC,CAAI;SAUzB;aATM,IAAA,IAAK,CAAI,KAAC,YAAgB,eAAA,EAAiB;YAUhD,OATO,WAAA,CAAY,IAAC,CAAI;SAUzB;aATM,IAAA,IAAK,CAAI,KAAC,YAAgB,QAAA,EAAU;YAUzC,OATO,WAAA,CAAY,SAAC,CAAS;SAU9B;aATM,IAAA,IAAK,CAAI,KAAC,YAAgBC,MAAA,EAAM;YAUrC,OATO,WAAA,CAAY,IAAC,CAAI;SAUzB;aATM,IAAA,IAAK,CAAI,KAAC,YAAgBD,aAAA,EAAa;YAU5C,OATO,WAAA,CAAY,YAAC,CAAY;SAUjC;aATM,IAAA,IAAK,CAAI,KAAC,IAAQ,OAAO,IAAA,CAAK,KAAC,KAAS,QAAA,EAAU;YAUvD,OATO,WAAA,CAAY,IAAC,CAAI;SAUzB;aATM;YAUL,OATO,WAAA,CAAY,IAAC,CAAI;SAUzB;KACF;;;;;;IAMH,OATG,GASH;QACI,QAAQ,IATC,CAAI,WAAC;YAUZ,KATK,WAAA,CAAY,IAAC;gBAUhB,OATO,IAAA,CAAK,IAAC,EAAI,CAAE;YAUrB,KATK,WAAA,CAAY,IAAC;gBAUhB,OATO,IAAA,CAAK,IAAC,EAAI,CAAE;YAUrB,KATK,WAAA,CAAY,SAAC;gBAUhB,OATO,IAAA,CAAK,KAAC,CAAK;YAUpB,KATK,WAAA,CAAY,IAAC;gBAUhB,OATO,IAAA,CAAK,IAAC,EAAI,CAAE;YAUrB,KATK,WAAA,CAAY,IAAC;gBAUhB,OATO,IAAA,CAAK,IAAC,EAAI,CAAE;YAUrB,KATK,WAAA,CAAY,YAAC;gBAUhB,OATO,IAAA,CAAK,WAAC,EAAW,CAAE;YAU5B;gBACE,OATO,IAAA,CAAK;SAUf;KACF;CACF;AAED,AAiCA;;;;AAIA,SAAA,eAAA,CA9CC,MAAA,EA8CD;IACE,uBA9CM,YAAA,GAAe,IAAI,eAAA,EAAgB,CAAE;IA+C3C,MAAM,CA9CC,IAAC,CAAI,MAAC,CAAM,CAAC,OAAC,CAAO,GAAC,IA8C/B;QACI,uBA9CM,KAAA,GAAQ,MAAA,CAAO,GAAC,CAAG,CAAC;QA+C1B,IAAI,KA9CC,IAAQ,KAAA,CAAM,OAAC,CAAO,KAAC,CAAK,EAAE;YA+CjC,KAAK,CA9CC,OAAC,CAAO,OAAC,IAAU,YAAA,CAAa,MAAC,CAAM,GAAC,EAAI,OAAA,CAAQ,QAAC,EAAQ,CAAE,CAAC,CAAC;SA+CxE;aA9CM;YA+CL,YAAY,CA9CC,MAAC,CAAM,GAAC,EAAI,KAAA,CAAM,QAAC,EAAQ,CAAE,CAAC;SA+C5C;KACF,CA9CC,CAAC;IA+CH,OA9CO,YAAA,CAAa;CA+CrB;AAED,MA9CM,IAAA,GAAO,YA8Cb,GA9CwB,CAAG;AA+C3B,MA9CM,CAAA,GAAI,OAAO,MAAA,IAAU,QAAA,GAAW,MAAA,GAAS,IAAA,CAAK;AA+CpD,MA9CM,QAAA,GAAW,EAAA,CAAM,qBAAuB,UAAC,CAAU,IAAI,IAAA,CAAK;AA+ClE,MA9CMC,MAAA,GAAO,EAAA,CAAM,qBAAuB,MAAC,CAAM,IAAI,IAAA,CAAK;AA+C1D,AA9CO,MAAMD,aAAA,GA+CT,EAAoB,CA9Cd,qBAAuB,aAAC,CAAa,IAAI,IAAA,CAAK;;AD7LxD;;;;;;;AASA,AAGA,AACA,AACA,AACA,AAEA;;;;;AAKA,SAAA,WAAA,CALC,OAAA,EAAA,OAAA,EAKD;IACE,OALO,OAAA,CAAQ,gBAAC,CAAgB,OAAC,CAAO,CAAC,QAAC,CAAQ;CAMnD;;;;;;;;AAQD,SAAA,YAAA,CACI,WAA2B,EAAA,YAAA,EAC3B,MAAmB,EAAA,GAAc,EAFrC;IAGE,uBAXM,UAAA,GAAa,WAAA,CAAY;IAY/B,IAAI,YAXC,EAAa;;QAahB,QAXO,UAAA,CAAW,KAAC,CAAK,IAAI,cAAA,CAAe;YAYzC,MAAG,EAXK,YAAA,CAAa,MAAC,IAAS,MAAA;YAY/B,GAAG,EAXE,YAAA,CAAa,GAAC,IAAM,GAAA;YAYzB,MAAM,EAXE,YAAA,CAAa,MAAC;YAYtB,MAAM,EAXE,YAAA,CAAa,MAAC;YAYtB,OAAO,EAXE,YAAA,CAAa,OAAC;YAYvB,IAAI,EAXE,YAAA,CAAa,IAAC;YAYpB,eAAe,EAXE,YAAA,CAAa,eAAC;YAY/B,YAAY,EAXE,YAAA,CAAa,YAAC;SAY7B,CAXC,CAAK,EAAY;KAYpB;IAED,QAXO,UAAA,CAAW,KAAC,CAAK,IAAI,cAAA,CAAe,EAAC,MAAC,EAAO,GAAA,EAAI,CAAC,CAAK,EAAY;CAY3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DD,AAAA,MAAA,IAAA,CAAA;;;;;IAKA,WAAA,CAbwB,QAAU,EAA6B,eAAiB,EAahF;QAbwB,IAAxB,CAAA,QAAwB,GAAA,QAAA,CAAU;QAA6B,IAA/D,CAAA,eAA+D,GAAA,eAAA,CAAiB;KAAe;;;;;;;;;;IAuB/F,OAfG,CAAA,GAAA,EAAA,OAAA,EAeH;QACI,qBAfI,kBAAoB,CAAI;QAgB5B,IAAI,OAfO,GAAA,KAAQ,QAAA,EAAU;YAgB3B,kBAAkB,GAfG,WAAA,CAgBjB,IAAI,CAfC,QAAC,EAgBN,IAfI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,GAAC,oBAAY,GAAC,EAAG,CAAC,CAAC,CAAC;SAgB/F;aAfM,IAAA,GAAK,YAAc,OAAA,EAAS;YAgBjC,kBAAkB,GAfG,WAAA,CAAY,IAAC,CAAI,QAAC,EAAS,GAAA,CAAI,CAAC;SAgBtD;aAfM;YAgBL,MAfM,IAAI,KAAA,CAAM,0DAAC,CAA0D,CAAC;SAgB7E;QACD,OAfO,kBAAA,CAAmB;KAgB3B;;;;;;;IAOH,GAjBG,CAAA,GAAA,EAAA,OAAA,EAiBH;QACI,OAjBO,IAAA,CAAK,OAAC,CAkBT,IAjBI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,GAAC,EAAI,GAAA,CAAI,CAAC,CAAC,CAAC;KAkBvF;;;;;;;;IAQH,IApBG,CAAA,GAAA,EAAA,IAAA,EAAA,OAAA,EAoBH;QACI,OApBO,IAAA,CAAK,OAAC,CAAO,IAAI,OAAA,CAAQ,YAAC,CAqB7B,IAAI,CApBC,eAAC,CAAe,KAAC,CAAK,IAAI,cAAA,CAAe,EAAC,IAAC,EAAK,IAAA,EAAK,CAAC,CAAC,EAAE,OAAA,EAAS,aAAA,CAAc,IAAC,EAqBtF,GAAG,CApBC,CAAC,CAAC,CAAC;KAqBZ;;;;;;;;IAQH,GAvBG,CAAA,GAAA,EAAA,IAAA,EAAA,OAAA,EAuBH;QACI,OAvBO,IAAA,CAAK,OAAC,CAAO,IAAI,OAAA,CAAQ,YAAC,CAwB7B,IAAI,CAvBC,eAAC,CAAe,KAAC,CAAK,IAAI,cAAA,CAAe,EAAC,IAAC,EAAK,IAAA,EAAK,CAAC,CAAC,EAAE,OAAA,EAAS,aAAA,CAAc,GAAC,EAwBtF,GAAG,CAvBC,CAAC,CAAC,CAAC;KAwBZ;;;;;;;IAOH,MAzBG,CAAA,GAAA,EAAA,OAAA,EAyBH;QACI,OAzBO,IAAA,CAAK,OAAC,CA0BT,IAzBI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,MAAC,EAAO,GAAA,CAAI,CAAC,CAAC,CAAC;KA0B1F;;;;;;;;IAQH,KA5BG,CAAA,GAAA,EAAA,IAAA,EAAA,OAAA,EA4BH;QACI,OA5BO,IAAA,CAAK,OAAC,CAAO,IAAI,OAAA,CAAQ,YAAC,CA6B7B,IAAI,CA5BC,eAAC,CAAe,KAAC,CAAK,IAAI,cAAA,CAAe,EAAC,IAAC,EAAK,IAAA,EAAK,CAAC,CAAC,EAAE,OAAA,EAAS,aAAA,CAAc,KAAC,EA6BtF,GAAG,CA5BC,CAAC,CAAC,CAAC;KA6BZ;;;;;;;IAOH,IA9BG,CAAA,GAAA,EAAA,OAAA,EA8BH;QACI,OA9BO,IAAA,CAAK,OAAC,CA+BT,IA9BI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,IAAC,EAAK,GAAA,CAAI,CAAC,CAAC,CAAC;KA+BxF;;;;;;;IAOH,OAhCG,CAAA,GAAA,EAAA,OAAA,EAgCH;QACI,OAhCO,IAAA,CAAK,OAAC,CAiCT,IAhCI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,OAAC,EAAQ,GAAA,CAAI,CAAC,CAAC,CAAC;KAiC3F;;AA/BI,IAAP,CAAA,UAAO,GAAoC;IAiC3C,EAhCE,IAAA,EAAM,UAAA,EAAW;CAiClB,CAhCC;;;;AAED,IAAD,CAAA,cAAC,GAAA,MAAA;IAmCD,EAAC,IAAI,EAAE,iBAAiB,GAAG;IAC3B,EAAC,IAAI,EAAE,cAAc,GAAG;CACvB,CAAC;AAGF,AAcA;;;AAGA,AAAA,MAAA,KA9CC,SAAA,IAAA,CA8CD;;;;;IAKA,WAAA,CAlDG,OAAoB,EAAmB,cAAgB,EAkD1D;QACI,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAChC;;;;;;;;;;;;;;;;;;IAkBH,OApDG,CAAA,GAAA,EAAA,OAAA,EAoDH;QACI,qBApDI,kBAAoB,CAAI;QAqD5B,IAAI,OApDO,GAAA,KAAQ,QAAA,EAAU;YAqD3B,GAAG;gBACC,IApDI,OAAA,CAAQ,YAAC,CAAY,IAAC,CAAI,eAAC,EAAgB,OAAA,EAAS,aAAA,CAAc,GAAC,oBAAY,GAAC,EAAG,CAAC,CAAC;SAqD9F;QACD,IAAI,GApDC,YAAc,OAAA,EAAS;YAqD1B,IAAI,GApDC,CAAG,MAAC,KAAU,aAAA,CAAc,GAAC,EAAI;gBAqDpC,MApDM,IAAI,KAAA,CAAM,6CAAC,CAA6C,CAAC;aAqDhE;YACD,kBAAkB,GApDG,WAAA,CAAY,IAAC,CAAI,QAAC,EAAS,GAAA,CAAI,CAAC;SAqDtD;aApDM;YAqDL,MApDM,IAAI,KAAA,CAAM,0DAAC,CAA0D,CAAC;SAqD7E;QACD,OApDO,kBAAA,CAAmB;KAqD3B;;AAnDI,KAAP,CAAA,UAAO,GAAoC;IAqD3C,EApDE,IAAA,EAAM,UAAA,EAAW;CAqDlB,CApDC;;;;AAED,KAAD,CAAA,cAAC,GAAA,MAAA;IAuDD,EAAC,IAAI,EAAE,iBAAiB,GAAG;IAC3B,EAAC,IAAI,EAAE,cAAc,GAAG;CACvB,CAAC,AAGF,AAQC;;ADlTD;;;;;;;;;;;;;AAeA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA;;;AAGA,AAAA,SAAA,gCAAA,GAAA;IACE,OAFO,IAAI,kBAAA,EAAmB,CAAE;CAGjC;;;;;;AAMD,AAAA,SAAA,WAAA,CANC,UAAA,EAAA,cAAA,EAMD;IACE,OANO,IAAI,IAAA,CAAK,UAAC,EAAW,cAAA,CAAe,CAAC;CAO7C;;;;;;AAMD,AAAA,SAAA,YAAA,CAVC,YAAA,EAAA,cAAA,EAUD;IACE,OAVO,IAAI,KAAA,CAAM,YAAC,EAAa,cAAA,CAAe,CAAC;CAWhD;;;;;;AAMD,AAAA,MAAA,UAAA,CAAA;;AANO,UAAP,CAAA,UAAO,GAAoC;IAQ3C,EAPE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAQtB,SAAS,EAPE;;;oBAUT,EAAC,OAPC,EAAQ,IAAA,EAAM,UAAA,EAAY,WAAA,EAAa,IAAA,EAAM,CAAA,UAAE,EAAW,cAAA,CAAe,EAAC;oBAQ5E,UAAU;oBACV,EAAC,OAPC,EAAQ,cAAA,EAAgB,QAAA,EAAU,kBAAA,EAAmB;oBAQvD,EAAC,OAPC,EAAQ,eAAA,EAAiB,QAAA,EAAU,mBAAA,EAAoB;oBAQzD,UAAU;oBACV,EAAC,OAPC,EAAQ,YAAA,EAAc,UAAA,EAAY,gCAAA,EAAiC;iBAQtE;aACF,EAPC,EAAG;CAQJ,CAPC;;;;AAED,UAAD,CAAA,cAAC,GAAA,MAAA,EAUA,CAAC;AAGF,AAUA;;;;;AAKA,AAAA,MAAA,WAAA,CAAA;;AAjBO,WAAP,CAAA,UAAO,GAAoC;IAmB3C,EAlBE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAmBtB,SAAS,EAlBE;;;oBAqBT,EAAC,OAlBC,EAAQ,KAAA,EAAO,UAAA,EAAY,YAAA,EAAc,IAAA,EAAM,CAAA,YAAE,EAAa,cAAA,CAAe,EAAC;oBAmBhF,YAAY;oBACZ,EAAC,OAlBC,EAAQ,cAAA,EAAgB,QAAA,EAAU,kBAAA,EAAmB;oBAmBvD,EAAC,OAlBC,EAAQ,eAAA,EAAiB,QAAA,EAAU,mBAAA,EAAoB;oBAmBzD,EAAC,OAlBC,EAAQ,YAAA,EAAc,QAAA,EAAU,aAAA,EAAc;iBAmBjD;aACF,EAlBC,EAAG;CAmBJ,CAlBC;;;;AAED,WAAD,CAAA,cAAC,GAAA,MAAA,EAqBA,CAAC,AAGF,AAQC;;ADtHD;;;;;;;;;;;;AAeA,AACA;;;AAGA,AADC,MAAA,OAAA,GAAA,IAAA,OAAA,CAAA,mBAAA,CAAA,CAAA;;ADlBD;;;;;;GAMG,AAEH,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAAgB;;ADrBhB;;;;;;;;;;;;AAaA,AAAia;0EAEvV;;ADf1E;;GAEG,AAEH,AAEA,AACA,AACA,AACA,AAAoF;;"}