{"version": 3, "file": "build.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/build.ts"], "names": [], "mappings": ";;AAAA,6CAA6C;AAE7C,gDAA6C;AAC7C,6CAAsC;AAEtC,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAE3D,MAAM,MAAM,GAAG,kBAAS,CAAC,WAAW,EAAE,IAAI,kBAAS,CAAC,UAAU,EAAE,CAAC;AACjE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AAE1E,4BAA4B;AACf,QAAA,uBAAuB,GAAQ;IAC1C;QACE,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;QAClE,WAAW,EAAE,2BAA2B;KACzC;IACD;QACE,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,WAAW,EAAE,gCAAgC;KAC9C;IACD;QACE,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,IAAI,CAAC;QACf,WAAW,EAAE,mCAAmC;KACjD;IACD;QACE,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,wCAAwC;KACtD;IACD;QACE,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;QAC5B,WAAW,EAAE,oBAAoB;KAClC;IACD;QACE,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,CAAC,IAAI,CAAC;QACf,WAAW,EAAE,yDAAyD;KACvE;IACD;QACE,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,IAAI,CAAC;QACf,WAAW,EAAE,2CAA2C;KACzD;IACD;QACE,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,WAAW,EAAE,mCAAmC;KACjD;IACD;QACE,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,WAAW,EAAE,sCAAsC;KACpD;IACD;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,CAAC,IAAI,CAAC;QACf,WAAW,EAAE,6CAA6C;KAC3D;IACD;QACE,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,oCAAoC;KAClD;IACD;QACE,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,6DAA6D;KAC3E;IACD;QACE,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,yBAAyB;KACvC;IACD;QACE,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,CAAC,IAAI,CAAC;QACf,WAAW,EAAE,mEAAmE;KACjF;IACD;QACE,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,WAAW,EAAE,8BAA8B;KAC5C;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;QAC3C,WAAW,EAAE,wDAAwD;QACrE,OAAO,EAAE,CAAC,IAAI,CAAC;KAChB;IACD;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,WAAW;QACpB,WAAW,EAAE,sEAAsE;KACpF;IACD;QACE,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,WAAW,EAAE,qCAAqC;KACnD;IACD;QACE,IAAI,EAAE,oBAAoB;QAC1B,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,CAAC,KAAK,CAAC;QAChB,WAAW,EAAE,kCAAkC;KAChD;IACD;QACE,IAAI,EAAE,mBAAmB;QACzB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,kDAAkD;KAChE;IACD;QACE,IAAI,EAAE,kBAAkB;QACxB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,iFAAiF;KAC/F;CACF,CAAC;AAMF,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IAClC,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,wEAAwE;IACrF,OAAO,EAAE,CAAC,GAAG,CAAC;IAEd,gBAAgB,EAAE,+BAAuB,CAAC,MAAM,CAAC;QAC/C;YACG,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,qBAAO,CAAA;kFACuD;SAC3E;KACJ,CAAC;IAEF,GAAG,EAAE,UAAU,cAAgC;QAC7C,yBAAyB;QACzB,iBAAO,CAAC,mCAAmC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE/D,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;QAEpD,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC;YAC9B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC,CAAC;QAEH,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACvC,CAAC;CACF,CAAC,CAAC;AAGH,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,kBAAe,YAAY,CAAC"}