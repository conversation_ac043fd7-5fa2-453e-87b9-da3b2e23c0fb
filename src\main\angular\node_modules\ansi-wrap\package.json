{"_args": [["ansi-wrap@0.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ansi-wrap@0.1.0", "_id": "ansi-wrap@0.1.0", "_inBundle": false, "_integrity": "sha1-qCJQ3bABXponyoLoLqYDu/pF768=", "_location": "/ansi-wrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ansi-wrap@0.1.0", "name": "ansi-wrap", "escapedName": "ansi-wrap", "rawSpec": "0.1.0", "saveSpec": null, "fetchSpec": "0.1.0"}, "_requiredBy": ["/ansi-gray"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ansi-wrap/-/ansi-wrap-0.1.0.tgz", "_spec": "0.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/ansi-wrap/issues"}, "dependencies": {}, "description": "Create ansi colors by passing the open and close codes.", "devDependencies": {}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/ansi-wrap", "keywords": [], "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/ansi-wrap/blob/master/LICENSE"}, "main": "index.js", "name": "ansi-wrap", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/ansi-wrap.git"}, "scripts": {"test": "mocha"}, "version": "0.1.0"}