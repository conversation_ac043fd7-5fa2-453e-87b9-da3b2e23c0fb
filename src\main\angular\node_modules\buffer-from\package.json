{"_args": [["buffer-from@1.1.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-from@1.1.2", "_id": "buffer-from@1.1.2", "_inBundle": false, "_integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "_location": "/buffer-from", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-from@1.1.2", "name": "buffer-from", "escapedName": "buffer-from", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/concat-stream"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-from/-/buffer-from-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "devDependencies": {"standard": "^12.0.1"}, "files": ["index.js"], "homepage": "https://github.com/LinusU/buffer-from#readme", "keywords": ["buffer", "buffer from"], "license": "MIT", "name": "buffer-from", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "version": "1.1.2"}