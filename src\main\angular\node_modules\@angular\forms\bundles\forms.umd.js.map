{"version": 3, "file": "forms.umd.js", "sources": ["../../../../packages/forms/src/form_providers.ts", "../../../../packages/forms/src/directives.ts", "../../../../packages/forms/src/directives/ng_no_validate_directive.ts", "../../../../packages/forms/src/form_builder.ts", "../../../../packages/forms/src/directives/validators.ts", "../../../../packages/forms/src/directives/reactive_directives/form_control_name.ts", "../../../../packages/forms/src/directives/reactive_directives/form_group_name.ts", "../../../../packages/forms/src/directives/reactive_directives/form_group_directive.ts", "../../../../packages/forms/src/directives/reactive_directives/form_control_directive.ts", "../../../../packages/forms/src/directives/reactive_errors.ts", "../../../../packages/forms/src/directives/ng_model.ts", "../../../../packages/forms/src/directives/ng_model_group.ts", "../../../../packages/forms/src/directives/template_driven_errors.ts", "../../../../packages/forms/src/directives/error_examples.ts", "../../../../packages/forms/src/directives/ng_form.ts", "../../../../packages/forms/src/model.ts", "../../../../packages/forms/src/directives/ng_control_status.ts", "../../../../packages/forms/src/directives/abstract_form_group_directive.ts", "../../../../packages/forms/src/directives/shared.ts", "../../../../packages/forms/src/directives/select_multiple_control_value_accessor.ts", "../../../../packages/forms/src/directives/select_control_value_accessor.ts", "../../../../packages/forms/src/directives/range_value_accessor.ts", "../../../../packages/forms/src/directives/radio_control_value_accessor.ts", "../../../../packages/forms/src/directives/ng_control.ts", "../../../../packages/forms/src/directives/number_value_accessor.ts", "../../../../packages/forms/src/directives/normalize_validator.ts", "../../../../packages/forms/src/directives/default_value_accessor.ts", "../../../../packages/forms/src/directives/checkbox_value_accessor.ts", "../../../../packages/forms/src/directives/control_value_accessor.ts", "../../../../packages/forms/src/validators.ts", "../../../../packages/forms/src/directives/control_container.ts", "../../../../packages/forms/src/directives/abstract_control_directive.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule} from '@angular/core';\n\nimport {InternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES, TEMPLATE_DRIVEN_DIRECTIVES} from './directives';\nimport {RadioControlRegistry} from './directives/radio_control_value_accessor';\nimport {FormBuilder} from './form_builder';\n/**\n * The ng module for forms.\n * \\@stable\n */\nexport class FormsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: TEMPLATE_DRIVEN_DIRECTIVES,\n  providers: [RadioControlRegistry],\n  exports: [InternalFormsSharedModule, TEMPLATE_DRIVEN_DIRECTIVES]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction FormsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nFormsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormsModule.ctorParameters;\n}\n\n/**\n * The ng module for reactive forms.\n * \\@stable\n */\nexport class ReactiveFormsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: [REACTIVE_DRIVEN_DIRECTIVES],\n  providers: [FormBuilder, RadioControlRegistry],\n  exports: [InternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction ReactiveFormsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nReactiveFormsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nReactiveFormsModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule, Type} from '@angular/core';\n\nimport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nimport {DefaultValueAccessor} from './directives/default_value_accessor';\nimport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nimport {NgForm} from './directives/ng_form';\nimport {NgModel} from './directives/ng_model';\nimport {NgModelGroup} from './directives/ng_model_group';\nimport {NgNoValidate} from './directives/ng_no_validate_directive';\nimport {NumberValueAccessor} from './directives/number_value_accessor';\nimport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nimport {RangeValueAccessor} from './directives/range_value_accessor';\nimport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nimport {FormControlName} from './directives/reactive_directives/form_control_name';\nimport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nimport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nimport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nimport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\nimport {CheckboxRequiredValidator, EmailValidator, MaxLengthValidator, MinLengthValidator, PatternValidator, RequiredValidator} from './directives/validators';\n\nexport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nexport {ControlValueAccessor} from './directives/control_value_accessor';\nexport {DefaultValueAccessor} from './directives/default_value_accessor';\nexport {NgControl} from './directives/ng_control';\nexport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nexport {NgForm} from './directives/ng_form';\nexport {NgModel} from './directives/ng_model';\nexport {NgModelGroup} from './directives/ng_model_group';\nexport {NumberValueAccessor} from './directives/number_value_accessor';\nexport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nexport {RangeValueAccessor} from './directives/range_value_accessor';\nexport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nexport {FormControlName} from './directives/reactive_directives/form_control_name';\nexport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nexport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nexport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nexport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\n\nexport const /** @type {?} */ SHARED_FORM_DIRECTIVES: Type<any>[] = [\n  NgNoValidate,\n  NgSelectOption,\n  NgSelectMultipleOption,\n  DefaultValueAccessor,\n  NumberValueAccessor,\n  RangeValueAccessor,\n  CheckboxControlValueAccessor,\n  SelectControlValueAccessor,\n  SelectMultipleControlValueAccessor,\n  RadioControlValueAccessor,\n  NgControlStatus,\n  NgControlStatusGroup,\n  RequiredValidator,\n  MinLengthValidator,\n  MaxLengthValidator,\n  PatternValidator,\n  CheckboxRequiredValidator,\n  EmailValidator,\n];\n\nexport const /** @type {?} */ TEMPLATE_DRIVEN_DIRECTIVES: Type<any>[] = [NgModel, NgModelGroup, NgForm];\n\nexport const /** @type {?} */ REACTIVE_DRIVEN_DIRECTIVES: Type<any>[] =\n    [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName];\n/**\n * Internal module used for sharing directives between FormsModule and ReactiveFormsModule\n */\nexport class InternalFormsSharedModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: SHARED_FORM_DIRECTIVES,\n  exports: SHARED_FORM_DIRECTIVES,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction InternalFormsSharedModule_tsickle_Closure_declarations() {\n/** @type {?} */\nInternalFormsSharedModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nInternalFormsSharedModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive} from '@angular/core';\n/**\n * \\@whatItDoes Adds `novalidate` attribute to all forms by default.\n * \n * `novalidate` is used to disable browser's native form validation.\n * \n * If you want to use native validation with Angular forms, just add `ngNativeValidate` attribute:\n * \n * ```\n * <form ngNativeValidate></form>\n * ```\n * \n * \\@experimental\n */\nexport class NgNoValidate {\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: 'form:not([ngNoForm]):not([ngNativeValidate])',\n  host: {'novalidate': ''},\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction NgNoValidate_tsickle_Closure_declarations() {\n/** @type {?} */\nNgNoValidate.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgNoValidate.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from './directives/validators';\nimport {AbstractControl, FormArray, FormControl, FormGroup} from './model';\n/**\n * \\@whatItDoes Creates an {\\@link AbstractControl} from a user-specified configuration.\n * \n * It is essentially syntactic sugar that shortens the `new FormGroup()`,\n * `new FormControl()`, and `new FormArray()` boilerplate that can build up in larger\n * forms.\n * \n * \\@howToUse \n * \n * To use, inject `FormBuilder` into your component class. You can then call its methods\n * directly.\n * \n * {\\@example forms/ts/formBuilder/form_builder_example.ts region='Component'}\n * \n *  * **npm package**: `\\@angular/forms`\n * \n *  * **NgModule**: {\\@link ReactiveFormsModule}\n * \n * \\@stable\n */\nexport class FormBuilder {\n/**\n * Construct a new {\\@link FormGroup} with the given map of configuration.\n * Valid keys for the `extra` parameter map are `validator` and `asyncValidator`.\n * \n * See the {\\@link FormGroup} constructor for more details.\n * @param {?} controlsConfig\n * @param {?=} extra\n * @return {?}\n */\ngroup(controlsConfig: {[key: string]: any}, extra: {[key: string]: any}|null = null): FormGroup {\n    const /** @type {?} */ controls = this._reduceControls(controlsConfig);\n    const /** @type {?} */ validator: ValidatorFn = extra != null ? extra['validator'] : null;\n    const /** @type {?} */ asyncValidator: AsyncValidatorFn = extra != null ? extra['asyncValidator'] : null;\n    return new FormGroup(controls, validator, asyncValidator);\n  }\n/**\n * Construct a new {\\@link FormControl} with the given `formState`,`validator`, and\n * `asyncValidator`.\n * \n * `formState` can either be a standalone value for the form control or an object\n * that contains both a value and a disabled status.\n * \n * @param {?} formState\n * @param {?=} validator\n * @param {?=} asyncValidator\n * @return {?}\n */\ncontrol(\n      formState: Object, validator?: ValidatorFn|ValidatorFn[]|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl {\n    return new FormControl(formState, validator, asyncValidator);\n  }\n/**\n * Construct a {\\@link FormArray} from the given `controlsConfig` array of\n * configuration, with the given optional `validator` and `asyncValidator`.\n * @param {?} controlsConfig\n * @param {?=} validator\n * @param {?=} asyncValidator\n * @return {?}\n */\narray(\n      controlsConfig: any[], validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null): FormArray {\n    const /** @type {?} */ controls = controlsConfig.map(c => this._createControl(c));\n    return new FormArray(controls, validator, asyncValidator);\n  }\n/**\n * \\@internal\n * @param {?} controlsConfig\n * @return {?}\n */\n_reduceControls(controlsConfig: {[k: string]: any}): {[key: string]: AbstractControl} {\n    const /** @type {?} */ controls: {[key: string]: AbstractControl} = {};\n    Object.keys(controlsConfig).forEach(controlName => {\n      controls[controlName] = this._createControl(controlsConfig[controlName]);\n    });\n    return controls;\n  }\n/**\n * \\@internal\n * @param {?} controlConfig\n * @return {?}\n */\n_createControl(controlConfig: any): AbstractControl {\n    if (controlConfig instanceof FormControl || controlConfig instanceof FormGroup ||\n        controlConfig instanceof FormArray) {\n      return controlConfig;\n\n    } else if (Array.isArray(controlConfig)) {\n      const /** @type {?} */ value = controlConfig[0];\n      const /** @type {?} */ validator: ValidatorFn = controlConfig.length > 1 ? controlConfig[1] : null;\n      const /** @type {?} */ asyncValidator: AsyncValidatorFn = controlConfig.length > 2 ? controlConfig[2] : null;\n      return this.control(value, validator, asyncValidator);\n\n    } else {\n      return this.control(controlConfig);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction FormBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nFormBuilder.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormBuilder.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Input, OnChanges, Provider, SimpleChanges, forwardRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {AbstractControl} from '../model';\nimport {NG_VALIDATORS, Validators} from '../validators';\n\n/** @experimental */\nexport type ValidationErrors = {\n  [key: string]: any\n};\n\n/**\n * An interface that can be implemented by classes that can act as validators.\n *\n * ## Usage\n *\n * ```typescript\n * @Directive({\n *   selector: '[custom-validator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: CustomValidatorDirective, multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(c: Control): {[key: string]: any} {\n *     return {\"custom\": true};\n *   }\n * }\n * ```\n *\n * @stable\n */\nexport interface Validator {\n  validate(c: AbstractControl): ValidationErrors|null;\n  registerOnValidatorChange?(fn: () => void): void;\n}\n\n/** @experimental */\nexport interface AsyncValidator extends Validator {\n  validate(c: AbstractControl): Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n\nexport const /** @type {?} */ REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => RequiredValidator),\n  multi: true\n};\n\nexport const /** @type {?} */ CHECKBOX_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => CheckboxRequiredValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `required` validator to any controls marked with the\n * `required` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input name=\"fullName\" ngModel required>\n * ```\n * \n * \\@stable\n */\nexport class RequiredValidator implements Validator {\nprivate _required: boolean;\nprivate _onChange: () => void;\n/**\n * @return {?}\n */\nget required(): boolean|string { return this._required; }\n/**\n * @param {?} value\n * @return {?}\n */\nset required(value: boolean|string) {\n    this._required = value != null && value !== false && `${value}` !== 'false';\n    if (this._onChange) this._onChange();\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.required ? Validators.required(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      ':not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]',\n  providers: [REQUIRED_VALIDATOR],\n  host: {'[attr.required]': 'required ? \"\" : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'required': [{ type: Input },],\n};\n}\n\nfunction RequiredValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nRequiredValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRequiredValidator.ctorParameters;\n/** @type {?} */\nRequiredValidator.propDecorators;\n/** @type {?} */\nRequiredValidator.prototype._required;\n/** @type {?} */\nRequiredValidator.prototype._onChange;\n}\n\n/**\n * A Directive that adds the `required` validator to checkbox controls marked with the\n * `required` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input type=\"checkbox\" name=\"active\" ngModel required>\n * ```\n * \n * \\@experimental\n */\nexport class CheckboxRequiredValidator extends RequiredValidator {\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.required ? Validators.requiredTrue(c) : null;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=checkbox][required][formControlName],input[type=checkbox][required][formControl],input[type=checkbox][required][ngModel]',\n  providers: [CHECKBOX_REQUIRED_VALIDATOR],\n  host: {'[attr.required]': 'required ? \"\" : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction CheckboxRequiredValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nCheckboxRequiredValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCheckboxRequiredValidator.ctorParameters;\n}\n\n/**\n * Provider which adds {\\@link EmailValidator} to {\\@link NG_VALIDATORS}.\n */\nexport const EMAIL_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => EmailValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `email` validator to controls marked with the\n * `email` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input type=\"email\" name=\"email\" ngModel email>\n * <input type=\"email\" name=\"email\" ngModel email=\"true\">\n * <input type=\"email\" name=\"email\" ngModel [email]=\"true\">\n * ```\n * \n * \\@experimental\n */\nexport class EmailValidator implements Validator {\nprivate _enabled: boolean;\nprivate _onChange: () => void;\n/**\n * @param {?} value\n * @return {?}\n */\nset email(value: boolean|string) {\n    this._enabled = value === '' || value === true || value === 'true';\n    if (this._onChange) this._onChange();\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this._enabled ? Validators.email(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[email][formControlName],[email][formControl],[email][ngModel]',\n  providers: [EMAIL_VALIDATOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'email': [{ type: Input },],\n};\n}\n\nfunction EmailValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nEmailValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nEmailValidator.ctorParameters;\n/** @type {?} */\nEmailValidator.propDecorators;\n/** @type {?} */\nEmailValidator.prototype._enabled;\n/** @type {?} */\nEmailValidator.prototype._onChange;\n}\n\n\n/**\n * @stable\n */\nexport interface ValidatorFn { (c: AbstractControl): ValidationErrors|null; }\n\n/**\n * @stable\n */\nexport interface AsyncValidatorFn {\n  (c: AbstractControl): Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n/**\n * Provider which adds {\\@link MinLengthValidator} to {\\@link NG_VALIDATORS}.\n * \n * ## Example:\n * \n * {\\@example common/forms/ts/validators/validators.ts region='min'}\n */\nexport const MIN_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinLengthValidator),\n  multi: true\n};\n/**\n * A directive which installs the {\\@link MinLengthValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `minlength` attribute.\n * \n * \\@stable\n */\nexport class MinLengthValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   minlength: string;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('minlength' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.minlength == null ? null : this._validator(c);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void {\n    this._validator = Validators.minLength(parseInt(this.minlength, 10));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[minlength][formControlName],[minlength][formControl],[minlength][ngModel]',\n  providers: [MIN_LENGTH_VALIDATOR],\n  host: {'[attr.minlength]': 'minlength ? minlength : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'minlength': [{ type: Input },],\n};\n}\n\nfunction MinLengthValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nMinLengthValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMinLengthValidator.ctorParameters;\n/** @type {?} */\nMinLengthValidator.propDecorators;\n/** @type {?} */\nMinLengthValidator.prototype._validator;\n/** @type {?} */\nMinLengthValidator.prototype._onChange;\n/** @type {?} */\nMinLengthValidator.prototype.minlength;\n}\n\n/**\n * Provider which adds {\\@link MaxLengthValidator} to {\\@link NG_VALIDATORS}.\n * \n * ## Example:\n * \n * {\\@example common/forms/ts/validators/validators.ts region='max'}\n */\nexport const MAX_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxLengthValidator),\n  multi: true\n};\n/**\n * A directive which installs the {\\@link MaxLengthValidator} for any `formControlName,\n * `formControl`,\n * or control with `ngModel` that also has a `maxlength` attribute.\n * \n * \\@stable\n */\nexport class MaxLengthValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   maxlength: string;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('maxlength' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.maxlength != null ? this._validator(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void {\n    this._validator = Validators.maxLength(parseInt(this.maxlength, 10));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]',\n  providers: [MAX_LENGTH_VALIDATOR],\n  host: {'[attr.maxlength]': 'maxlength ? maxlength : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'maxlength': [{ type: Input },],\n};\n}\n\nfunction MaxLengthValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nMaxLengthValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMaxLengthValidator.ctorParameters;\n/** @type {?} */\nMaxLengthValidator.propDecorators;\n/** @type {?} */\nMaxLengthValidator.prototype._validator;\n/** @type {?} */\nMaxLengthValidator.prototype._onChange;\n/** @type {?} */\nMaxLengthValidator.prototype.maxlength;\n}\n\n\n\nexport const /** @type {?} */ PATTERN_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => PatternValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `pattern` validator to any controls marked with the\n * `pattern` attribute, via the {\\@link NG_VALIDATORS} binding. Uses attribute value\n * as the regex to validate Control value against.  Follows pattern attribute\n * semantics; i.e. regex must match entire Control value.\n * \n * ### Example\n * \n * ```\n * <input [name]=\"fullName\" pattern=\"[a-zA-Z ]*\" ngModel>\n * ```\n * \\@stable\n */\nexport class PatternValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   pattern: string|RegExp;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('pattern' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null { return this._validator(c); }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void { this._validator = Validators.pattern(this.pattern); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[pattern][formControlName],[pattern][formControl],[pattern][ngModel]',\n  providers: [PATTERN_VALIDATOR],\n  host: {'[attr.pattern]': 'pattern ? pattern : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'pattern': [{ type: Input },],\n};\n}\n\nfunction PatternValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nPatternValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPatternValidator.ctorParameters;\n/** @type {?} */\nPatternValidator.propDecorators;\n/** @type {?} */\nPatternValidator.prototype._validator;\n/** @type {?} */\nPatternValidator.prototype._onChange;\n/** @type {?} */\nPatternValidator.prototype.pattern;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Self, SimpleChanges, SkipSelf, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, controlPath, isPropertyUpdated, selectValueAccessor} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nimport {FormGroupDirective} from './form_group_directive';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nexport const /** @type {?} */ controlNameBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlName)\n};\n/**\n * \\@whatItDoes Syncs a {\\@link FormControl} in an existing {\\@link FormGroup} to a form control\n * element by name.\n * \n * In other words, this directive ensures that any values written to the {\\@link FormControl}\n * instance programmatically will be written to the DOM element (model -> view). Conversely,\n * any values written to the DOM element through user input will be reflected in the\n * {\\@link FormControl} instance (view -> model).\n * \n * \\@howToUse \n * \n * This directive is designed to be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the {\\@link FormControl} instance you want to\n * link, and will look for a {\\@link FormControl} registered with that name in the\n * closest {\\@link FormGroup} or {\\@link FormArray} above it.\n * \n * **Access the control**: You can access the {\\@link FormControl} associated with\n * this directive by using the {\\@link AbstractControl#get} method.\n * Ex: `this.form.get('first');`\n * \n * **Get value**: the `value` property is always synced and available on the {\\@link FormControl}.\n * See a full list of available properties in {\\@link AbstractControl}.\n * \n *  **Set value**: You can set an initial value for the control when instantiating the\n *  {\\@link FormControl}, or you can set it programmatically later using\n *  {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the control, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * In this example, we create form controls for first name and last name.\n * \n * {\\@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n * \n * To see `formControlName` examples with different form control types, see:\n * \n * * Radio buttons: {\\@link RadioControlValueAccessor}\n * * Selects: {\\@link SelectControlValueAccessor}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: {\\@link ReactiveFormsModule}\n * \n *  \\@stable\n */\nexport class FormControlName extends NgControl implements OnChanges, OnDestroy {\nprivate _added = false;\n/**\n * \\@internal\n */\nviewModel: any;\n/**\n * \\@internal\n */\n_control: FormControl;\n\n   name: string;\n\n  // TODO(kara):  Replace ngModel with reactive API\n   model: any;\n   update = new EventEmitter();\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nset isDisabled(isDisabled: boolean) { ReactiveErrors.disabledAttrWarning(); }\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(\n         parent: ControlContainer,\n         validators: Array<Validator|ValidatorFn>,\n         asyncValidators:\n          Array<AsyncValidator|AsyncValidatorFn>,\n         valueAccessors: ControlValueAccessor[]) {\n    super();\n    this._parent = parent;\n    this._rawValidators = validators || [];\n    this._rawAsyncValidators = asyncValidators || [];\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    if (!this._added) this._setUpControl();\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      this.viewModel = this.model;\n      this.formDirective.updateModel(this, this.model);\n    }\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeControl(this);\n    }\n  }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n/**\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, /** @type {?} */(( this._parent))); }\n/**\n * @return {?}\n */\nget formDirective(): any { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn {\n    return /** @type {?} */(( composeAsyncValidators(this._rawAsyncValidators)));\n  }\n/**\n * @return {?}\n */\nget control(): FormControl { return this._control; }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n    if (!(this._parent instanceof FormGroupName) &&\n        this._parent instanceof AbstractFormGroupDirective) {\n      ReactiveErrors.ngModelGroupException();\n    } else if (\n        !(this._parent instanceof FormGroupName) && !(this._parent instanceof FormGroupDirective) &&\n        !(this._parent instanceof FormArrayName)) {\n      ReactiveErrors.controlParentException();\n    }\n  }\n/**\n * @return {?}\n */\nprivate _setUpControl() {\n    this._checkParentType();\n    this._control = this.formDirective.addControl(this);\n    if (this.control.disabled && /** @type {?} */(( this.valueAccessor)).setDisabledState) { /** @type {?} */(( /** @type {?} */((\n      this.valueAccessor)).setDisabledState))(true);\n    }\n    this._added = true;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControlName]', providers: [controlNameBinding]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formControlName', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n};\n}\n\nfunction FormControlName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormControlName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormControlName.ctorParameters;\n/** @type {?} */\nFormControlName.propDecorators;\n/** @type {?} */\nFormControlName.prototype._added;\n/**\n * \\@internal\n * @type {?}\n */\nFormControlName.prototype.viewModel;\n/**\n * \\@internal\n * @type {?}\n */\nFormControlName.prototype._control;\n/** @type {?} */\nFormControlName.prototype.name;\n/** @type {?} */\nFormControlName.prototype.model;\n/** @type {?} */\nFormControlName.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Host, Inject, Input, OnDestroy, OnInit, Optional, Self, SkipSelf, forwardRef} from '@angular/core';\n\nimport {FormArray} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, controlPath} from '../shared';\nimport {AsyncValidatorFn, ValidatorFn} from '../validators';\n\nimport {FormGroupDirective} from './form_group_directive';\n\nexport const /** @type {?} */ formGroupNameProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupName)\n};\n/**\n * \\@whatItDoes Syncs a nested {\\@link FormGroup} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive can only be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the nested {\\@link FormGroup} you want to link, and\n * will look for a {\\@link FormGroup} registered with that name in the parent\n * {\\@link FormGroup} instance you passed into {\\@link FormGroupDirective}.\n * \n * Nested form groups can come in handy when you want to validate a sub-group of a\n * form separately from the rest or when you'd like to group the values of certain\n * controls into their own nested object.\n * \n * **Access the group**: You can access the associated {\\@link FormGroup} using the\n * {\\@link AbstractControl#get} method. Ex: `this.form.get('name')`.\n * \n * You can also access individual controls within the group using dot syntax.\n * Ex: `this.form.get('name.first')`\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormGroup}. See a full list of available properties in {\\@link AbstractControl}.\n * \n * **Set the value**: You can set an initial value for each child control when instantiating\n * the {\\@link FormGroup}, or you can set it programmatically later using\n * {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the group, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * {\\@example forms/ts/nestedFormGroup/nested_form_group_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n * \\@stable\n */\nexport class FormGroupName extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {\n    if (_hasInvalidParent(this._parent)) {\n      ReactiveErrors.groupParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formGroupName]', providers: [formGroupNameProvider]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formGroupName', ] },],\n};\n}\n\nfunction FormGroupName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroupName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormGroupName.ctorParameters;\n/** @type {?} */\nFormGroupName.propDecorators;\n/** @type {?} */\nFormGroupName.prototype.name;\n}\n\n\nexport const /** @type {?} */ formArrayNameProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormArrayName)\n};\n/**\n * \\@whatItDoes Syncs a nested {\\@link FormArray} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive is designed to be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the nested {\\@link FormArray} you want to link, and\n * will look for a {\\@link FormArray} registered with that name in the parent\n * {\\@link FormGroup} instance you passed into {\\@link FormGroupDirective}.\n * \n * Nested form arrays can come in handy when you have a group of form controls but\n * you're not sure how many there will be. Form arrays allow you to create new\n * form controls dynamically.\n * \n * **Access the array**: You can access the associated {\\@link FormArray} using the\n * {\\@link AbstractControl#get} method on the parent {\\@link FormGroup}.\n * Ex: `this.form.get('cities')`.\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormArray}. See a full list of available properties in {\\@link AbstractControl}.\n * \n * **Set the value**: You can set an initial value for each child control when instantiating\n * the {\\@link FormArray}, or you can set the value programmatically later using the\n * {\\@link FormArray}'s {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}\n * methods.\n * \n * **Listen to value**: If you want to listen to changes in the value of the array, you can\n * subscribe to the {\\@link FormArray}'s {\\@link AbstractControl#valueChanges} event.  You can also\n * listen to its {\\@link AbstractControl#statusChanges} event to be notified when the validation\n * status is re-calculated.\n * \n * **Add new controls**: You can add new controls to the {\\@link FormArray} dynamically by\n * calling its {\\@link FormArray#push} method.\n *  Ex: `this.form.get('cities').push(new FormControl());`\n * \n * ### Example\n * \n * {\\@example forms/ts/nestedFormArray/nested_form_array_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n * \\@stable\n */\nexport class FormArrayName extends ControlContainer implements OnInit, OnDestroy {\n/**\n * \\@internal\n */\n_parent: ControlContainer;\n/**\n * \\@internal\n */\n_validators: any[];\n/**\n * \\@internal\n */\n_asyncValidators: any[];\n\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._checkParentType(); /** @type {?} */((\n    this.formDirective)).addFormArray(this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeFormArray(this);\n    }\n  }\n/**\n * @return {?}\n */\nget control(): FormArray { return /** @type {?} */(( this.formDirective)).getFormArray(this); }\n/**\n * @return {?}\n */\nget formDirective(): FormGroupDirective|null {\n    return this._parent ? /** @type {?} */(( <FormGroupDirective>this._parent.formDirective)) : null;\n  }\n/**\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, this._parent); }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._validators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n    return composeAsyncValidators(this._asyncValidators);\n  }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n    if (_hasInvalidParent(this._parent)) {\n      ReactiveErrors.arrayParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formArrayName]', providers: [formArrayNameProvider]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formArrayName', ] },],\n};\n}\n\nfunction FormArrayName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormArrayName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormArrayName.ctorParameters;\n/** @type {?} */\nFormArrayName.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._parent;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._validators;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._asyncValidators;\n/** @type {?} */\nFormArrayName.prototype.name;\n}\n\n/**\n * @param {?} parent\n * @return {?}\n */\nfunction _hasInvalidParent(parent: ControlContainer): boolean {\n  return !(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) &&\n      !(parent instanceof FormArrayName);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Input, OnChanges, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\nimport {FormArray, FormControl, FormGroup} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS, Validators} from '../../validators';\nimport {ControlContainer} from '../control_container';\nimport {Form} from '../form_interface';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {cleanUpControl, composeAsyncValidators, composeValidators, setUpControl, setUpFormContainer} from '../shared';\n\nimport {FormControlName} from './form_control_name';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nexport const /** @type {?} */ formDirectiveProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupDirective)\n};\n/**\n * \\@whatItDoes Binds an existing {\\@link FormGroup} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive accepts an existing {\\@link FormGroup} instance. It will then use this\n * {\\@link FormGroup} instance to match any child {\\@link FormControl}, {\\@link FormGroup},\n * and {\\@link FormArray} instances to child {\\@link FormControlName}, {\\@link FormGroupName},\n * and {\\@link FormArrayName} directives.\n * \n * **Set value**: You can set the form's initial value when instantiating the\n * {\\@link FormGroup}, or you can set it programmatically later using the {\\@link FormGroup}'s\n * {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue} methods.\n * \n * **Listen to value**: If you want to listen to changes in the value of the form, you can subscribe\n * to the {\\@link FormGroup}'s {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * its {\\@link AbstractControl#statusChanges} event to be notified when the validation status is\n * re-calculated.\n * \n * Furthermore, you can listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event will be emitted with the original form\n * submission event.\n * \n * ### Example\n * \n * In this example, we create form controls for first name and last name.\n * \n * {\\@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: {\\@link ReactiveFormsModule}\n * \n *  \\@stable\n */\nexport class FormGroupDirective extends ControlContainer implements Form,\n    OnChanges {\nprivate _submitted: boolean = false;\nprivate _oldForm: FormGroup;\n  directives: FormControlName[] = [];\n\n   form: FormGroup = /** @type {?} */(( null));\n   ngSubmit = new EventEmitter();\n/**\n * @param {?} _validators\n * @param {?} _asyncValidators\n */\nconstructor(\nprivate _validators: any[],\nprivate _asyncValidators: any[]) {\n    super();\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    this._checkFormPresent();\n    if (changes.hasOwnProperty('form')) {\n      this._updateValidators();\n      this._updateDomValue();\n      this._updateRegistrations();\n    }\n  }\n/**\n * @return {?}\n */\nget submitted(): boolean { return this._submitted; }\n/**\n * @return {?}\n */\nget formDirective(): Form { return this; }\n/**\n * @return {?}\n */\nget control(): FormGroup { return this.form; }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @param {?} dir\n * @return {?}\n */\naddControl(dir: FormControlName): FormControl {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpControl(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n    this.directives.push(dir);\n    return ctrl;\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetControl(dir: FormControlName): FormControl { return /** @type {?} */(( <FormControl>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveControl(dir: FormControlName): void { remove(this.directives, dir); }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormGroup(dir: FormGroupName): void {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormGroup(dir: FormGroupName): void {}\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormGroup(dir: FormGroupName): FormGroup { return /** @type {?} */(( <FormGroup>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormArray(dir: FormArrayName): void {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormArray(dir: FormArrayName): void {}\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormArray(dir: FormArrayName): FormArray { return /** @type {?} */(( <FormArray>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @param {?} value\n * @return {?}\n */\nupdateModel(dir: FormControlName, value: any): void {\n    const /** @type {?} */ ctrl  = /** @type {?} */(( <FormControl>this.form.get(dir.path)));\n    ctrl.setValue(value);\n  }\n/**\n * @param {?} $event\n * @return {?}\n */\nonSubmit($event: Event): boolean {\n    this._submitted = true;\n    this.ngSubmit.emit($event);\n    return false;\n  }\n/**\n * @return {?}\n */\nonReset(): void { this.resetForm(); }\n/**\n * @param {?=} value\n * @return {?}\n */\nresetForm(value: any = undefined): void {\n    this.form.reset(value);\n    this._submitted = false;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateDomValue() {\n    this.directives.forEach(dir => {\n      const /** @type {?} */ newCtrl: any = this.form.get(dir.path);\n      if (dir._control !== newCtrl) {\n        cleanUpControl(dir._control, dir);\n        if (newCtrl) setUpControl(newCtrl, dir);\n        dir._control = newCtrl;\n      }\n    });\n\n    this.form._updateTreeValidity({emitEvent: false});\n  }\n/**\n * @return {?}\n */\nprivate _updateRegistrations() {\n    this.form._registerOnCollectionChange(() => this._updateDomValue());\n    if (this._oldForm) this._oldForm._registerOnCollectionChange(() => {});\n    this._oldForm = this.form;\n  }\n/**\n * @return {?}\n */\nprivate _updateValidators() {\n    const /** @type {?} */ sync = composeValidators(this._validators);\n    this.form.validator = Validators.compose([ /** @type {?} */((this.form.validator)), /** @type {?} */(( sync))]);\n\n    const /** @type {?} */ async = composeAsyncValidators(this._asyncValidators);\n    this.form.asyncValidator = Validators.composeAsync([ /** @type {?} */((this.form.asyncValidator)), /** @type {?} */(( async))]);\n  }\n/**\n * @return {?}\n */\nprivate _checkFormPresent() {\n    if (!this.form) {\n      ReactiveErrors.missingFormException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[formGroup]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  exportAs: 'ngForm'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'form': [{ type: Input, args: ['formGroup', ] },],\n'ngSubmit': [{ type: Output },],\n};\n}\n\nfunction FormGroupDirective_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroupDirective.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormGroupDirective.ctorParameters;\n/** @type {?} */\nFormGroupDirective.propDecorators;\n/** @type {?} */\nFormGroupDirective.prototype._submitted;\n/** @type {?} */\nFormGroupDirective.prototype._oldForm;\n/** @type {?} */\nFormGroupDirective.prototype.directives;\n/** @type {?} */\nFormGroupDirective.prototype.form;\n/** @type {?} */\nFormGroupDirective.prototype.ngSubmit;\n/** @type {?} */\nFormGroupDirective.prototype._validators;\n/** @type {?} */\nFormGroupDirective.prototype._asyncValidators;\n}\n\n/**\n * @template T\n * @param {?} list\n * @param {?} el\n * @return {?}\n */\nfunction remove<T>(list: T[], el: T): void {\n  const /** @type {?} */ index = list.indexOf(el);\n  if (index > -1) {\n    list.splice(index, 1);\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Input, OnChanges, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, isPropertyUpdated, selectValueAccessor, setUpControl} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nexport const /** @type {?} */ formControlBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlDirective)\n};\n/**\n * \\@whatItDoes Syncs a standalone {\\@link FormControl} instance to a form control element.\n * \n * In other words, this directive ensures that any values written to the {\\@link FormControl}\n * instance programmatically will be written to the DOM element (model -> view). Conversely,\n * any values written to the DOM element through user input will be reflected in the\n * {\\@link FormControl} instance (view -> model).\n * \n * \\@howToUse \n * \n * Use this directive if you'd like to create and manage a {\\@link FormControl} instance directly.\n * Simply create a {\\@link FormControl}, save it to your component class, and pass it into the\n * {\\@link FormControlDirective}.\n * \n * This directive is designed to be used as a standalone control.  Unlike {\\@link FormControlName},\n * it does not require that your {\\@link FormControl} instance be part of any parent\n * {\\@link FormGroup}, and it won't be registered to any {\\@link FormGroupDirective} that\n * exists above it.\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormControl} instance. See a full list of available properties in\n * {\\@link AbstractControl}.\n * \n * **Set the value**: You can pass in an initial value when instantiating the {\\@link FormControl},\n * or you can set it programmatically later using {\\@link AbstractControl#setValue} or\n * {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the control, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * {\\@example forms/ts/simpleFormControl/simple_form_control_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n *  \\@stable\n */\nexport class FormControlDirective extends NgControl implements OnChanges {\n  viewModel: any;\n\n   form: FormControl;\n   model: any;\n   update = new EventEmitter();\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nset isDisabled(isDisabled: boolean) { ReactiveErrors.disabledAttrWarning(); }\n/**\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(   validators: Array<Validator|ValidatorFn>,\n                 asyncValidators: Array<AsyncValidator|AsyncValidatorFn>,\n                \n              valueAccessors: ControlValueAccessor[]) {\n                super();\n                this._rawValidators = validators || [];\n                this._rawAsyncValidators = asyncValidators || [];\n                this.valueAccessor = selectValueAccessor(this, valueAccessors);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n                if (this._isControlChanged(changes)) {\n                  setUpControl(this.form, this);\n                  if (this.control.disabled && /** @type {?} */(( this.valueAccessor)).setDisabledState) { /** @type {?} */(( /** @type {?} */((\n                    this.valueAccessor)).setDisabledState))(true);\n                  }\n                  this.form.updateValueAndValidity({emitEvent: false});\n                }\n                if (isPropertyUpdated(changes, this.viewModel)) {\n                  this.form.setValue(this.model);\n                  this.viewModel = this.model;\n                }\n              }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n                return composeAsyncValidators(this._rawAsyncValidators);\n              }\n/**\n * @return {?}\n */\nget control(): FormControl { return this.form; }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n                this.viewModel = newValue;\n                this.update.emit(newValue);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _isControlChanged(changes: {[key: string]: any}): boolean {\n                return changes.hasOwnProperty('form');\n              }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControl]', providers: [formControlBinding], exportAs: 'ngForm'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'form': [{ type: Input, args: ['formControl', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n};\n}\n\nfunction FormControlDirective_tsickle_Closure_declarations() {\n/** @type {?} */\nFormControlDirective.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormControlDirective.ctorParameters;\n/** @type {?} */\nFormControlDirective.propDecorators;\n/** @type {?} */\nFormControlDirective.prototype.viewModel;\n/** @type {?} */\nFormControlDirective.prototype.form;\n/** @type {?} */\nFormControlDirective.prototype.model;\n/** @type {?} */\nFormControlDirective.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {FormErrorExamples as Examples} from './error_examples';\nexport class ReactiveErrors {\n/**\n * @return {?}\n */\nstatic controlParentException(): void {\n    throw new Error(\n        `formControlName must be used with a parent formGroup directive.  You'll want to add a formGroup\n       directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${Examples.formControlName}`);\n  }\n/**\n * @return {?}\n */\nstatic ngModelGroupException(): void {\n    throw new Error(\n        `formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents\n       that also have a \"form\" prefix: formGroupName, formArrayName, or formGroup.\n\n       Option 1:  Update the parent to be formGroupName (reactive form strategy)\n\n        ${Examples.formGroupName}\n\n        Option 2: Use ngModel instead of formControlName (template-driven strategy)\n\n        ${Examples.ngModelGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic missingFormException(): void {\n    throw new Error(`formGroup expects a FormGroup instance. Please pass one in.\n\n       Example:\n\n       ${Examples.formControlName}`);\n  }\n/**\n * @return {?}\n */\nstatic groupParentException(): void {\n    throw new Error(\n        `formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${Examples.formGroupName}`);\n  }\n/**\n * @return {?}\n */\nstatic arrayParentException(): void {\n    throw new Error(\n        `formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup\n       directive and pass it an existing FormGroup instance (you can create one in your class).\n\n        Example:\n\n        ${Examples.formArrayName}`);\n  }\n/**\n * @return {?}\n */\nstatic disabledAttrWarning(): void {\n    console.warn(`\n      It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true\n      when you set up this control in your component class, the disabled attribute will actually be set in the DOM for\n      you. We recommend using this approach to avoid 'changed after checked' errors.\n       \n      Example: \n      form = new FormGroup({\n        first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),\n        last: new FormControl('Drew', Validators.required)\n      });\n    `);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\nimport {NgForm} from './ng_form';\nimport {NgModelGroup} from './ng_model_group';\nimport {composeAsyncValidators, composeValidators, controlPath, isPropertyUpdated, selectValueAccessor, setUpControl} from './shared';\nimport {TemplateDrivenErrors} from './template_driven_errors';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\nexport const /** @type {?} */ formControlBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => NgModel)\n};\n/**\n * `ngModel` forces an additional change detection run when its inputs change:\n * E.g.:\n * ```\n * <div>{{myModel.valid}}</div>\n * <input [(ngModel)]=\"myValue\" #myModel=\"ngModel\">\n * ```\n * I.e. `ngModel` can export itself on the element and then be used in the template.\n * Normally, this would result in expressions before the `input` that use the exported directive\n * to have and old value as they have been\n * dirty checked before. As this is a very common case for `ngModel`, we added this second change\n * detection run.\n * \n * Notes:\n * - this is just one extra run no matter how many `ngModel` have been changed.\n * - this is a general problem when using `exportAs` for directives!\n */\nconst resolvedPromise = Promise.resolve(null);\n/**\n * \\@whatItDoes Creates a {\\@link FormControl} instance from a domain model and binds it\n * to a form control element.\n * \n * The {\\@link FormControl} instance will track the value, user interaction, and\n * validation status of the control and keep the view synced with the model. If used\n * within a parent form, the directive will also register itself with the form as a child\n * control.\n * \n * \\@howToUse \n * \n * This directive can be used by itself or as part of a larger form. All you need is the\n * `ngModel` selector to activate it.\n * \n * It accepts a domain model as an optional {\\@link Input}. If you have a one-way binding\n * to `ngModel` with `[]` syntax, changing the value of the domain model in the component\n * class will set the value in the view. If you have a two-way binding with `[()]` syntax\n * (also known as 'banana-box syntax'), the value in the UI will always be synced back to\n * the domain model in your class as well.\n * \n * If you wish to inspect the properties of the associated {\\@link FormControl} (like\n * validity state), you can also export the directive into a local template variable using\n * `ngModel` as the key (ex: `#myVar=\"ngModel\"`). You can then access the control using the\n * directive's `control` property, but most properties you'll need (like `valid` and `dirty`)\n * will fall through to the control anyway, so you can access them directly. You can see a\n * full list of properties directly available in {\\@link AbstractControlDirective}.\n * \n * The following is an example of a simple standalone control using `ngModel`:\n * \n * {\\@example forms/ts/simpleNgModel/simple_ng_model_example.ts region='Component'}\n * \n * When using the `ngModel` within `<form>` tags, you'll also need to supply a `name` attribute\n * so that the control can be registered with the parent form under that name.\n * \n * It's worth noting that in the context of a parent form, you often can skip one-way or\n * two-way binding because the parent form will sync the value for you. You can access\n * its properties by exporting it into a local template variable using `ngForm` (ex:\n * `#f=\"ngForm\"`). Then you can pass it where it needs to go on submit.\n * \n * If you do need to populate initial values into your form, using a one-way binding for\n * `ngModel` tends to be sufficient as long as you use the exported form's value rather\n * than the domain model's value on submit.\n * \n * Take a look at an example of using `ngModel` within a form:\n * \n * {\\@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n * \n * To see `ngModel` examples with different form control types, see:\n * \n * * Radio buttons: {\\@link RadioControlValueAccessor}\n * * Selects: {\\@link SelectControlValueAccessor}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: `FormsModule`\n * \n *  \\@stable\n */\nexport class NgModel extends NgControl implements OnChanges,\n    OnDestroy {\n/**\n * \\@internal\n */\n_control = new FormControl();\n/**\n * \\@internal\n */\n_registered = false;\n  viewModel: any;\n\n   name: string;\n   isDisabled: boolean;\n   model: any;\n   options: {name?: string, standalone?: boolean};\n\n   update = new EventEmitter();\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(  parent: ControlContainer,\n                 validators: Array<Validator|ValidatorFn>,\n                 asyncValidators: Array<AsyncValidator|AsyncValidatorFn>,\n                \n              valueAccessors: ControlValueAccessor[]) {\n                super();\n                this._parent = parent;\n                this._rawValidators = validators || [];\n                this._rawAsyncValidators = asyncValidators || [];\n                this.valueAccessor = selectValueAccessor(this, valueAccessors);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n                this._checkForErrors();\n                if (!this._registered) this._setUpControl();\n                if ('isDisabled' in changes) {\n                  this._updateDisabled(changes);\n                }\n\n                if (isPropertyUpdated(changes, this.viewModel)) {\n                  this._updateValue(this.model);\n                  this.viewModel = this.model;\n                }\n              }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.formDirective && this.formDirective.removeControl(this); }\n/**\n * @return {?}\n */\nget control(): FormControl { return this._control; }\n/**\n * @return {?}\n */\nget path(): string[] {\n                return this._parent ? controlPath(this.name, this._parent) : [this.name];\n              }\n/**\n * @return {?}\n */\nget formDirective(): any { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n                return composeAsyncValidators(this._rawAsyncValidators);\n              }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n                this.viewModel = newValue;\n                this.update.emit(newValue);\n              }\n/**\n * @return {?}\n */\nprivate _setUpControl(): void {\n                this._isStandalone() ? this._setUpStandalone() :\n                                       this.formDirective.addControl(this);\n                this._registered = true;\n              }\n/**\n * @return {?}\n */\nprivate _isStandalone(): boolean {\n                return !this._parent || !!(this.options && this.options.standalone);\n              }\n/**\n * @return {?}\n */\nprivate _setUpStandalone(): void {\n                setUpControl(this._control, this);\n                this._control.updateValueAndValidity({emitEvent: false});\n              }\n/**\n * @return {?}\n */\nprivate _checkForErrors(): void {\n                if (!this._isStandalone()) {\n                  this._checkParentType();\n                }\n                this._checkName();\n              }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n                if (!(this._parent instanceof NgModelGroup) &&\n                    this._parent instanceof AbstractFormGroupDirective) {\n                  TemplateDrivenErrors.formGroupNameException();\n                } else if (\n                    !(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n                  TemplateDrivenErrors.modelParentException();\n                }\n              }\n/**\n * @return {?}\n */\nprivate _checkName(): void {\n                if (this.options && this.options.name) this.name = this.options.name;\n\n                if (!this._isStandalone() && !this.name) {\n                  TemplateDrivenErrors.missingNameException();\n                }\n              }\n/**\n * @param {?} value\n * @return {?}\n */\nprivate _updateValue(value: any): void {\n                resolvedPromise.then(\n                    () => { this.control.setValue(value, {emitViewToModelChange: false}); });\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _updateDisabled(changes: SimpleChanges) {\n                const /** @type {?} */ disabledValue = changes['isDisabled'].currentValue;\n\n                const /** @type {?} */ isDisabled =\n                    disabledValue === '' || (disabledValue && disabledValue !== 'false');\n\n                resolvedPromise.then(() => {\n                  if (isDisabled && !this.control.disabled) {\n                    this.control.disable();\n                  } else if (!isDisabled && this.control.disabled) {\n                    this.control.enable();\n                  }\n                });\n              }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[ngModel]:not([formControlName]):not([formControl])',\n  providers: [formControlBinding],\n  exportAs: 'ngModel'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'options': [{ type: Input, args: ['ngModelOptions', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n};\n}\n\nfunction NgModel_tsickle_Closure_declarations() {\n/** @type {?} */\nNgModel.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgModel.ctorParameters;\n/** @type {?} */\nNgModel.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nNgModel.prototype._control;\n/**\n * \\@internal\n * @type {?}\n */\nNgModel.prototype._registered;\n/** @type {?} */\nNgModel.prototype.viewModel;\n/** @type {?} */\nNgModel.prototype.name;\n/** @type {?} */\nNgModel.prototype.isDisabled;\n/** @type {?} */\nNgModel.prototype.model;\n/** @type {?} */\nNgModel.prototype.options;\n/** @type {?} */\nNgModel.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Host, Inject, Input, OnD<PERSON>roy, OnInit, Optional, Self, SkipSelf, forwardRef} from '@angular/core';\n\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {NgForm} from './ng_form';\nimport {TemplateDrivenErrors} from './template_driven_errors';\n\nexport const /** @type {?} */ modelGroupProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgModelGroup)\n};\n/**\n * \\@whatItDoes Creates and binds a {\\@link FormGroup} instance to a DOM element.\n * \n * \\@howToUse \n * \n * This directive can only be used as a child of {\\@link NgForm} (or in other words,\n * within `<form>` tags).\n * \n * Use this directive if you'd like to create a sub-group within a form. This can\n * come in handy if you want to validate a sub-group of your form separately from\n * the rest of your form, or if some values in your domain model make more sense to\n * consume together in a nested object.\n * \n * Pass in the name you'd like this sub-group to have and it will become the key\n * for the sub-group in the form's full value. You can also export the directive into\n * a local template variable using `ngModelGroup` (ex: `#myGroup=\"ngModelGroup\"`).\n * \n * {\\@example forms/ts/ngModelGroup/ng_model_group_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `FormsModule`\n * \n * \\@stable\n */\nexport class NgModelGroup extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n        parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {\n    if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n      TemplateDrivenErrors.modelGroupParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngModelGroup]', providers: [modelGroupProvider], exportAs: 'ngModelGroup'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['ngModelGroup', ] },],\n};\n}\n\nfunction NgModelGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nNgModelGroup.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgModelGroup.ctorParameters;\n/** @type {?} */\nNgModelGroup.propDecorators;\n/** @type {?} */\nNgModelGroup.prototype.name;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {FormErrorExamples as Examples} from './error_examples';\nexport class TemplateDrivenErrors {\n/**\n * @return {?}\n */\nstatic modelParentException(): void {\n    throw new Error(`\n      ngModel cannot be used to register form controls with a parent formGroup directive.  Try using\n      formGroup's partner directive \"formControlName\" instead.  Example:\n\n      ${Examples.formControlName}\n\n      Or, if you'd like to avoid registering this form control, indicate that it's standalone in ngModelOptions:\n\n      Example:\n\n      ${Examples.ngModelWithFormGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic formGroupNameException(): void {\n    throw new Error(`\n      ngModel cannot be used to register form controls with a parent formGroupName or formArrayName directive.\n\n      Option 1: Use formControlName instead of ngModel (reactive strategy):\n\n      ${Examples.formGroupName}\n\n      Option 2:  Update ngModel's parent be ngModelGroup (template-driven strategy):\n\n      ${Examples.ngModelGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic missingNameException() {\n    throw new Error(\n        `If ngModel is used within a form tag, either the name attribute must be set or the form\n      control must be defined as 'standalone' in ngModelOptions.\n\n      Example 1: <input [(ngModel)]=\"person.firstName\" name=\"first\">\n      Example 2: <input [(ngModel)]=\"person.firstName\" [ngModelOptions]=\"{standalone: true}\">`);\n  }\n/**\n * @return {?}\n */\nstatic modelGroupParentException() {\n    throw new Error(`\n      ngModelGroup cannot be used with a parent formGroup directive.\n\n      Option 1: Use formGroupName instead of ngModelGroup (reactive strategy):\n\n      ${Examples.formGroupName}\n\n      Option 2:  Use a regular form tag instead of the formGroup directive (template-driven strategy):\n\n      ${Examples.ngModelGroup}`);\n  }\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const FormErrorExamples = {\n  formControlName: `\n    <div [formGroup]=\"myGroup\">\n      <input formControlName=\"firstName\">\n    </div>\n\n    In your class:\n\n    this.myGroup = new FormGroup({\n       firstName: new FormControl()\n    });`,\n\n  formGroupName: `\n    <div [formGroup]=\"myGroup\">\n       <div formGroupName=\"person\">\n          <input formControlName=\"firstName\">\n       </div>\n    </div>\n\n    In your class:\n\n    this.myGroup = new FormGroup({\n       person: new FormGroup({ firstName: new FormControl() })\n    });`,\n\n  formArrayName: `\n    <div [formGroup]=\"myGroup\">\n      <div formArrayName=\"cities\">\n        <div *ngFor=\"let city of cityArray.controls; index as i\">\n          <input [formControlName]=\"i\">\n        </div>\n      </div>\n    </div>\n\n    In your class:\n\n    this.cityArray = new FormArray([new FormControl('SF')]);\n    this.myGroup = new FormGroup({\n      cities: this.cityArray\n    });`,\n\n  ngModelGroup: `\n    <form>\n       <div ngModelGroup=\"person\">\n          <input [(ngModel)]=\"person.name\" name=\"firstName\">\n       </div>\n    </form>`,\n\n  ngModelWithFormGroup: `\n    <div [formGroup]=\"myGroup\">\n       <input formControlName=\"firstName\">\n       <input [(ngModel)]=\"showMoreControls\" [ngModelOptions]=\"{standalone: true}\">\n    </div>\n  `\n};\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Optional, Self, forwardRef} from '@angular/core';\n\nimport {AbstractControl, FormControl, FormGroup} from '../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {NgControl} from './ng_control';\nimport {NgModel} from './ng_model';\nimport {NgModelGroup} from './ng_model_group';\nimport {composeAsyncValidators, composeValidators, setUpControl, setUpFormContainer} from './shared';\n\nexport const /** @type {?} */ formDirectiveProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgForm)\n};\n\nconst /** @type {?} */ resolvedPromise = Promise.resolve(null);\n/**\n * \\@whatItDoes Creates a top-level {\\@link FormGroup} instance and binds it to a form\n * to track aggregate form value and validation status.\n * \n * \\@howToUse \n * \n * As soon as you import the `FormsModule`, this directive becomes active by default on\n * all `<form>` tags.  You don't need to add a special selector.\n * \n * You can export the directive into a local template variable using `ngForm` as the key\n * (ex: `#myForm=\"ngForm\"`). This is optional, but useful.  Many properties from the underlying\n * {\\@link FormGroup} instance are duplicated on the directive itself, so a reference to it\n * will give you access to the aggregate value and validity status of the form, as well as\n * user interaction properties like `dirty` and `touched`.\n * \n * To register child controls with the form, you'll want to use {\\@link NgModel} with a\n * `name` attribute.  You can also use {\\@link NgModelGroup} if you'd like to create\n * sub-groups within the form.\n * \n * You can listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event will be emitted with the original form\n * submission event.\n * \n * {\\@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `FormsModule`\n * \n *  \\@stable\n */\nexport class NgForm extends ControlContainer implements Form {\nprivate _submitted: boolean = false;\n\n  form: FormGroup;\n  ngSubmit = new EventEmitter();\n/**\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this.form =\n        new FormGroup({}, composeValidators(validators), composeAsyncValidators(asyncValidators));\n  }\n/**\n * @return {?}\n */\nget submitted(): boolean { return this._submitted; }\n/**\n * @return {?}\n */\nget formDirective(): Form { return this; }\n/**\n * @return {?}\n */\nget control(): FormGroup { return this.form; }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @return {?}\n */\nget controls(): {[key: string]: AbstractControl} { return this.form.controls; }\n/**\n * @param {?} dir\n * @return {?}\n */\naddControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      dir._control = /** @type {?} */(( <FormControl>container.registerControl(dir.name, dir.control)));\n      setUpControl(dir.control, dir);\n      dir.control.updateValueAndValidity({emitEvent: false});\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetControl(dir: NgModel): FormControl { return /** @type {?} */(( <FormControl>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      const /** @type {?} */ group = new FormGroup({});\n      setUpFormContainer(group, dir);\n      container.registerControl(dir.name, group);\n      group.updateValueAndValidity({emitEvent: false});\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormGroup(dir: NgModelGroup): FormGroup { return /** @type {?} */(( <FormGroup>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @param {?} value\n * @return {?}\n */\nupdateModel(dir: NgControl, value: any): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ ctrl = /** @type {?} */(( <FormControl>this.form.get( /** @type {?} */((dir.path)))));\n      ctrl.setValue(value);\n    });\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nsetValue(value: {[key: string]: any}): void { this.control.setValue(value); }\n/**\n * @param {?} $event\n * @return {?}\n */\nonSubmit($event: Event): boolean {\n    this._submitted = true;\n    this.ngSubmit.emit($event);\n    return false;\n  }\n/**\n * @return {?}\n */\nonReset(): void { this.resetForm(); }\n/**\n * @param {?=} value\n * @return {?}\n */\nresetForm(value: any = undefined): void {\n    this.form.reset(value);\n    this._submitted = false;\n  }\n/**\n * \\@internal\n * @param {?} path\n * @return {?}\n */\n_findContainer(path: string[]): FormGroup {\n    path.pop();\n    return path.length ? /** @type {?} */(( <FormGroup>this.form.get(path))) : this.form;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: 'form:not([ngNoForm]):not([formGroup]),ngForm,[ngForm]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  outputs: ['ngSubmit'],\n  exportAs: 'ngForm'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\n}\n\nfunction NgForm_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForm.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgForm.ctorParameters;\n/** @type {?} */\nNgForm.prototype._submitted;\n/** @type {?} */\nNgForm.prototype.form;\n/** @type {?} */\nNgForm.prototype.ngSubmit;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {EventEmitter} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {composeAsyncValidators, composeValidators} from './directives/shared';\nimport {AsyncValidatorFn, ValidationErrors, ValidatorFn} from './directives/validators';\nimport {toObservable} from './validators';\n/**\n * Indicates that a FormControl is valid, i.e. that no errors exist in the input value.\n */\nexport const VALID = 'VALID';\n/**\n * Indicates that a FormControl is invalid, i.e. that an error exists in the input value.\n */\nexport const INVALID = 'INVALID';\n/**\n * Indicates that a FormControl is pending, i.e. that async validation is occurring and\n * errors are not yet available for the input value.\n */\nexport const PENDING = 'PENDING';\n/**\n * Indicates that a FormControl is disabled, i.e. that the control is exempt from ancestor\n * calculations of validity or value.\n */\nexport const DISABLED = 'DISABLED';\n/**\n * @param {?} control\n * @param {?} path\n * @param {?} delimiter\n * @return {?}\n */\nfunction _find(control: AbstractControl, path: Array<string|number>| string, delimiter: string) {\n  if (path == null) return null;\n\n  if (!(path instanceof Array)) {\n    path = ( /** @type {?} */((<string>path))).split(delimiter);\n  }\n  if (path instanceof Array && (path.length === 0)) return null;\n\n  return ( /** @type {?} */((<Array<string|number>>path))).reduce((v: AbstractControl, name) => {\n    if (v instanceof FormGroup) {\n      return v.controls[name] || null;\n    }\n\n    if (v instanceof FormArray) {\n      return v.at( /** @type {?} */((<number>name))) || null;\n    }\n\n    return null;\n  }, control);\n}\n/**\n * @param {?=} validator\n * @return {?}\n */\nfunction coerceToValidator(validator?: ValidatorFn | ValidatorFn[] | null): ValidatorFn|null {\n  return Array.isArray(validator) ? composeValidators(validator) : validator || null;\n}\n/**\n * @param {?=} asyncValidator\n * @return {?}\n */\nfunction coerceToAsyncValidator(asyncValidator?: AsyncValidatorFn | AsyncValidatorFn[] | null):\n    AsyncValidatorFn|null {\n  return Array.isArray(asyncValidator) ? composeAsyncValidators(asyncValidator) :\n                                         asyncValidator || null;\n}\n/**\n * \\@whatItDoes This is the base class for {\\@link FormControl}, {\\@link FormGroup}, and\n * {\\@link FormArray}.\n * \n * It provides some of the shared behavior that all controls and groups of controls have, like\n * running validators, calculating status, and resetting state. It also defines the properties\n * that are shared between all sub-classes, like `value`, `valid`, and `dirty`. It shouldn't be\n * instantiated directly.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class AbstractControl {\n/**\n * \\@internal\n */\n_value: any;\n/**\n * \\@internal\n */\n_onCollectionChange = () => {};\nprivate _valueChanges: EventEmitter<any>;\nprivate _statusChanges: EventEmitter<any>;\nprivate _status: string;\nprivate _errors: ValidationErrors|null;\nprivate _pristine: boolean = true;\nprivate _touched: boolean = false;\nprivate _parent: FormGroup|FormArray;\nprivate _asyncValidationSubscription: any;\n/**\n * @param {?} validator\n * @param {?} asyncValidator\n */\nconstructor(public validator: ValidatorFn|null,\npublic asyncValidator: AsyncValidatorFn|null) {}\n/**\n * The value of the control.\n * @return {?}\n */\nget value(): any { return this._value; }\n/**\n * The parent control.\n * @return {?}\n */\nget parent(): FormGroup|FormArray { return this._parent; }\n/**\n * The validation status of the control. There are four possible\n * validation statuses:\n * \n * * **VALID**:  control has passed all validation checks\n * * **INVALID**: control has failed at least one validation check\n * * **PENDING**: control is in the midst of conducting a validation check\n * * **DISABLED**: control is exempt from validation checks\n * \n * These statuses are mutually exclusive, so a control cannot be\n * both valid AND invalid or invalid AND disabled.\n * @return {?}\n */\nget status(): string { return this._status; }\n/**\n * A control is `valid` when its `status === VALID`.\n * \n * In order to have this status, the control must have passed all its\n * validation checks.\n * @return {?}\n */\nget valid(): boolean { return this._status === VALID; }\n/**\n * A control is `invalid` when its `status === INVALID`.\n * \n * In order to have this status, the control must have failed\n * at least one of its validation checks.\n * @return {?}\n */\nget invalid(): boolean { return this._status === INVALID; }\n/**\n * A control is `pending` when its `status === PENDING`.\n * \n * In order to have this status, the control must be in the\n * middle of conducting a validation check.\n * @return {?}\n */\nget pending(): boolean { return this._status == PENDING; }\n/**\n * A control is `disabled` when its `status === DISABLED`.\n * \n * Disabled controls are exempt from validation checks and\n * are not included in the aggregate value of their ancestor\n * controls.\n * @return {?}\n */\nget disabled(): boolean { return this._status === DISABLED; }\n/**\n * A control is `enabled` as long as its `status !== DISABLED`.\n * \n * In other words, it has a status of `VALID`, `INVALID`, or\n * `PENDING`.\n * @return {?}\n */\nget enabled(): boolean { return this._status !== DISABLED; }\n/**\n * Returns any errors generated by failing validation. If there\n * are no errors, it will return null.\n * @return {?}\n */\nget errors(): ValidationErrors|null { return this._errors; }\n/**\n * A control is `pristine` if the user has not yet changed\n * the value in the UI.\n * \n * Note that programmatic changes to a control's value will\n * *not* mark it dirty.\n * @return {?}\n */\nget pristine(): boolean { return this._pristine; }\n/**\n * A control is `dirty` if the user has changed the value\n * in the UI.\n * \n * Note that programmatic changes to a control's value will\n * *not* mark it dirty.\n * @return {?}\n */\nget dirty(): boolean { return !this.pristine; }\n/**\n * A control is marked `touched` once the user has triggered\n * a `blur` event on it.\n * @return {?}\n */\nget touched(): boolean { return this._touched; }\n/**\n * A control is `untouched` if the user has not yet triggered\n * a `blur` event on it.\n * @return {?}\n */\nget untouched(): boolean { return !this._touched; }\n/**\n * Emits an event every time the value of the control changes, in\n * the UI or programmatically.\n * @return {?}\n */\nget valueChanges(): Observable<any> { return this._valueChanges; }\n/**\n * Emits an event every time the validation status of the control\n * is re-calculated.\n * @return {?}\n */\nget statusChanges(): Observable<any> { return this._statusChanges; }\n/**\n * Sets the synchronous validators that are active on this control.  Calling\n * this will overwrite any existing sync validators.\n * @param {?} newValidator\n * @return {?}\n */\nsetValidators(newValidator: ValidatorFn|ValidatorFn[]|null): void {\n    this.validator = coerceToValidator(newValidator);\n  }\n/**\n * Sets the async validators that are active on this control. Calling this\n * will overwrite any existing async validators.\n * @param {?} newValidator\n * @return {?}\n */\nsetAsyncValidators(newValidator: AsyncValidatorFn|AsyncValidatorFn[]): void {\n    this.asyncValidator = coerceToAsyncValidator(newValidator);\n  }\n/**\n * Empties out the sync validator list.\n * @return {?}\n */\nclearValidators(): void { this.validator = null; }\n/**\n * Empties out the async validator list.\n * @return {?}\n */\nclearAsyncValidators(): void { this.asyncValidator = null; }\n/**\n * Marks the control as `touched`.\n * \n * This will also mark all direct ancestors as `touched` to maintain\n * the model.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsTouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = true;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsTouched(opts);\n    }\n  }\n/**\n * Marks the control as `untouched`.\n * \n * If the control has any children, it will also mark all children as `untouched`\n * to maintain the model, and re-calculate the `touched` status of all parent\n * controls.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsUntouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = false;\n\n    this._forEachChild(\n        (control: AbstractControl) => { control.markAsUntouched({onlySelf: true}); });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n/**\n * Marks the control as `dirty`.\n * \n * This will also mark all direct ancestors as `dirty` to maintain\n * the model.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsDirty(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = false;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsDirty(opts);\n    }\n  }\n/**\n * Marks the control as `pristine`.\n * \n * If the control has any children, it will also mark all children as `pristine`\n * to maintain the model, and re-calculate the `pristine` status of all parent\n * controls.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsPristine(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = true;\n\n    this._forEachChild((control: AbstractControl) => { control.markAsPristine({onlySelf: true}); });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n/**\n * Marks the control as `pending`.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsPending(opts: {onlySelf?: boolean} = {}): void {\n    this._status = PENDING;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsPending(opts);\n    }\n  }\n/**\n * Disables the control. This means the control will be exempt from validation checks and\n * excluded from the aggregate value of any parent. Its status is `DISABLED`.\n * \n * If the control has children, all children will be disabled to maintain the model.\n * @param {?=} opts\n * @return {?}\n */\ndisable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._status = DISABLED;\n    this._errors = null;\n    this._forEachChild((control: AbstractControl) => { control.disable({onlySelf: true}); });\n    this._updateValue();\n\n    if (opts.emitEvent !== false) {\n      this._valueChanges.emit(this._value);\n      this._statusChanges.emit(this._status);\n    }\n\n    this._updateAncestors(!!opts.onlySelf);\n    this._onDisabledChange.forEach((changeFn) => changeFn(true));\n  }\n/**\n * Enables the control. This means the control will be included in validation checks and\n * the aggregate value of its parent. Its status is re-calculated based on its value and\n * its validators.\n * \n * If the control has children, all children will be enabled.\n * @param {?=} opts\n * @return {?}\n */\nenable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._status = VALID;\n    this._forEachChild((control: AbstractControl) => { control.enable({onlySelf: true}); });\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n\n    this._updateAncestors(!!opts.onlySelf);\n    this._onDisabledChange.forEach((changeFn) => changeFn(false));\n  }\n/**\n * @param {?} onlySelf\n * @return {?}\n */\nprivate _updateAncestors(onlySelf: boolean) {\n    if (this._parent && !onlySelf) {\n      this._parent.updateValueAndValidity();\n      this._parent._updatePristine();\n      this._parent._updateTouched();\n    }\n  }\n/**\n * @param {?} parent\n * @return {?}\n */\nsetParent(parent: FormGroup|FormArray): void { this._parent = parent; }\n/**\n * Sets the value of the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any, options?: Object) {}\n/**\n * Patches the value of the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any, options?: Object) {}\n/**\n * Resets the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value?: any, options?: Object) {}\n/**\n * Re-calculates the value and validation status of the control.\n * \n * By default, it will also update the value and validity of its ancestors.\n * @param {?=} opts\n * @return {?}\n */\nupdateValueAndValidity(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._setInitialStatus();\n    this._updateValue();\n\n    if (this.enabled) {\n      this._cancelExistingSubscription();\n      this._errors = this._runValidator();\n      this._status = this._calculateStatus();\n\n      if (this._status === VALID || this._status === PENDING) {\n        this._runAsyncValidator(opts.emitEvent);\n      }\n    }\n\n    if (opts.emitEvent !== false) {\n      this._valueChanges.emit(this._value);\n      this._statusChanges.emit(this._status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n    }\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updateTreeValidity(opts: {emitEvent?: boolean} = {emitEvent: true}) {\n    this._forEachChild((ctrl: AbstractControl) => ctrl._updateTreeValidity(opts));\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n  }\n/**\n * @return {?}\n */\nprivate _setInitialStatus() { this._status = this._allControlsDisabled() ? DISABLED : VALID; }\n/**\n * @return {?}\n */\nprivate _runValidator(): ValidationErrors|null {\n    return this.validator ? this.validator(this) : null;\n  }\n/**\n * @param {?=} emitEvent\n * @return {?}\n */\nprivate _runAsyncValidator(emitEvent?: boolean): void {\n    if (this.asyncValidator) {\n      this._status = PENDING;\n      const /** @type {?} */ obs = toObservable(this.asyncValidator(this));\n      this._asyncValidationSubscription =\n          obs.subscribe((errors: ValidationErrors | null) => this.setErrors(errors, {emitEvent}));\n    }\n  }\n/**\n * @return {?}\n */\nprivate _cancelExistingSubscription(): void {\n    if (this._asyncValidationSubscription) {\n      this._asyncValidationSubscription.unsubscribe();\n    }\n  }\n/**\n * Sets errors on a form control.\n * \n * This is used when validations are run manually by the user, rather than automatically.\n * \n * Calling `setErrors` will also update the validity of the parent control.\n * \n * ### Example\n * \n * ```\n * const login = new FormControl(\"someLogin\");\n * login.setErrors({\n *   \"notUnique\": true\n * });\n * \n * expect(login.valid).toEqual(false);\n * expect(login.errors).toEqual({\"notUnique\": true});\n * \n * login.setValue(\"someOtherLogin\");\n * \n * expect(login.valid).toEqual(true);\n * ```\n * @param {?} errors\n * @param {?=} opts\n * @return {?}\n */\nsetErrors(errors: ValidationErrors|null, opts: {emitEvent?: boolean} = {}): void {\n    this._errors = errors;\n    this._updateControlsErrors(opts.emitEvent !== false);\n  }\n/**\n * Retrieves a child control given the control's name or path.\n * \n * Paths can be passed in as an array or a string delimited by a dot.\n * \n * To get a control nested within a `person` sub-group:\n * \n * * `this.form.get('person.name');`\n * \n * -OR-\n * \n * * `this.form.get(['person', 'name']);`\n * @param {?} path\n * @return {?}\n */\nget(path: Array<string|number>|string): AbstractControl|null { return _find(this, path, '.'); }\n/**\n * Returns true if the control with the given path has the error specified. Otherwise\n * returns null or undefined.\n * \n * If no path is given, it checks for the error on the present control.\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\ngetError(errorCode: string, path?: string[]): any {\n    const /** @type {?} */ control = path ? this.get(path) : this;\n    return control && control._errors ? control._errors[errorCode] : null;\n  }\n/**\n * Returns true if the control with the given path has the error specified. Otherwise\n * returns false.\n * \n * If no path is given, it checks for the error on the present control.\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\nhasError(errorCode: string, path?: string[]): boolean { return !!this.getError(errorCode, path); }\n/**\n * Retrieves the top-level ancestor of this control.\n * @return {?}\n */\nget root(): AbstractControl {\n    let /** @type {?} */ x: AbstractControl = this;\n\n    while (x._parent) {\n      x = x._parent;\n    }\n\n    return x;\n  }\n/**\n * \\@internal\n * @param {?} emitEvent\n * @return {?}\n */\n_updateControlsErrors(emitEvent: boolean): void {\n    this._status = this._calculateStatus();\n\n    if (emitEvent) {\n      this._statusChanges.emit(this._status);\n    }\n\n    if (this._parent) {\n      this._parent._updateControlsErrors(emitEvent);\n    }\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_initObservables() {\n    this._valueChanges = new EventEmitter();\n    this._statusChanges = new EventEmitter();\n  }\n/**\n * @return {?}\n */\nprivate _calculateStatus(): string {\n    if (this._allControlsDisabled()) return DISABLED;\n    if (this._errors) return INVALID;\n    if (this._anyControlsHaveStatus(PENDING)) return PENDING;\n    if (this._anyControlsHaveStatus(INVALID)) return INVALID;\n    return VALID;\n  }\n/**\n * \\@internal\n * @abstract\n * @return {?}\n */\n_updateValue() {}\n/**\n * \\@internal\n * @abstract\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function) {}\n/**\n * \\@internal\n * @abstract\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function) {}\n/**\n * \\@internal\n * @abstract\n * @return {?}\n */\n_allControlsDisabled() {}\n/**\n * \\@internal\n * @param {?} status\n * @return {?}\n */\n_anyControlsHaveStatus(status: string): boolean {\n    return this._anyControls((control: AbstractControl) => control.status === status);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_anyControlsDirty(): boolean {\n    return this._anyControls((control: AbstractControl) => control.dirty);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_anyControlsTouched(): boolean {\n    return this._anyControls((control: AbstractControl) => control.touched);\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updatePristine(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = !this._anyControlsDirty();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updateTouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = this._anyControlsTouched();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n/**\n * \\@internal\n */\n_onDisabledChange: Function[] = [];\n/**\n * \\@internal\n * @param {?} formState\n * @return {?}\n */\n_isBoxedValue(formState: any): boolean {\n    return typeof formState === 'object' && formState !== null &&\n        Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;\n  }\n/**\n * \\@internal\n * @param {?} fn\n * @return {?}\n */\n_registerOnCollectionChange(fn: () => void): void { this._onCollectionChange = fn; }\n}\n\nfunction AbstractControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._value;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._onCollectionChange;\n/** @type {?} */\nAbstractControl.prototype._valueChanges;\n/** @type {?} */\nAbstractControl.prototype._statusChanges;\n/** @type {?} */\nAbstractControl.prototype._status;\n/** @type {?} */\nAbstractControl.prototype._errors;\n/** @type {?} */\nAbstractControl.prototype._pristine;\n/** @type {?} */\nAbstractControl.prototype._touched;\n/** @type {?} */\nAbstractControl.prototype._parent;\n/** @type {?} */\nAbstractControl.prototype._asyncValidationSubscription;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._onDisabledChange;\n/** @type {?} */\nAbstractControl.prototype.validator;\n/** @type {?} */\nAbstractControl.prototype.asyncValidator;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validation status of an individual form control.\n * \n * It is one of the three fundamental building blocks of Angular forms, along with\n * {\\@link FormGroup} and {\\@link FormArray}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormControl}, you can pass in an initial value as the\n * first argument. Example:\n * \n * ```ts\n * const ctrl = new FormControl('some value');\n * console.log(ctrl.value);     // 'some value'\n * ```\n * \n * You can also initialize the control with a form state object on instantiation,\n * which includes both the value and whether or not the control is disabled.\n * You can't use the value key without the disabled key; both are required\n * to use this way of initialization.\n * \n * ```ts\n * const ctrl = new FormControl({value: 'n/a', disabled: true});\n * console.log(ctrl.value);     // 'n/a'\n * console.log(ctrl.status);   // 'DISABLED'\n * ```\n * \n * To include a sync validator (or an array of sync validators) with the control,\n * pass it in as the second argument. Async validators are also supported, but\n * have to be passed in separately as the third arg.\n * \n * ```ts\n * const ctrl = new FormControl('', Validators.required);\n * console.log(ctrl.value);     // ''\n * console.log(ctrl.status);   // 'INVALID'\n * ```\n * \n * See its superclass, {\\@link AbstractControl}, for more properties and methods.\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormControl extends AbstractControl {\n/**\n * \\@internal\n */\n_onChange: Function[] = [];\n/**\n * @param {?=} formState\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\n      formState: any = null, validator?: ValidatorFn|ValidatorFn[]|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n    super(coerceToValidator(validator), coerceToAsyncValidator(asyncValidator));\n    this._applyFormState(formState);\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n    this._initObservables();\n  }\n/**\n * Set the value of the form control to `value`.\n * \n * If `onlySelf` is `true`, this change will only affect the validation of this `FormControl`\n * and not its parent component. This defaults to false.\n * \n * If `emitEvent` is `true`, this\n * change will cause a `valueChanges` event on the `FormControl` to be emitted. This defaults\n * to true (as it falls through to `updateValueAndValidity`).\n * \n * If `emitModelToViewChange` is `true`, the view will be notified about the new value\n * via an `onChange` event. This is the default behavior if `emitModelToViewChange` is not\n * specified.\n * \n * If `emitViewToModelChange` is `true`, an ngModelChange event will be fired to update the\n * model.  This is the default behavior if `emitViewToModelChange` is not specified.\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  } = {}): void {\n    this._value = value;\n    if (this._onChange.length && options.emitModelToViewChange !== false) {\n      this._onChange.forEach(\n          (changeFn) => changeFn(this._value, options.emitViewToModelChange !== false));\n    }\n    this.updateValueAndValidity(options);\n  }\n/**\n * Patches the value of a control.\n * \n * This function is functionally the same as {\\@link FormControl#setValue} at this level.\n * It exists for symmetry with {\\@link FormGroup#patchValue} on `FormGroups` and `FormArrays`,\n * where it does behave differently.\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  } = {}): void {\n    this.setValue(value, options);\n  }\n/**\n * Resets the form control. This means by default:\n * \n * * it is marked as `pristine`\n * * it is marked as `untouched`\n * * value is set to null\n * \n * You can also reset to a specific form state by passing through a standalone\n * value or a form state object that contains both a value and a disabled state\n * (these are the only two properties that cannot be calculated).\n * \n * Ex:\n * \n * ```ts\n * this.control.reset('Nancy');\n * \n * console.log(this.control.value);  // 'Nancy'\n * ```\n * \n * OR\n * \n * ```\n * this.control.reset({value: 'Nancy', disabled: true});\n * \n * console.log(this.control.value);  // 'Nancy'\n * console.log(this.control.status);  // 'DISABLED'\n * ```\n * @param {?=} formState\n * @param {?=} options\n * @return {?}\n */\nreset(formState: any = null, options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._applyFormState(formState);\n    this.markAsPristine(options);\n    this.markAsUntouched(options);\n    this.setValue(this._value, options);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue() {}\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean { return false; }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean { return this.disabled; }\n/**\n * Register a listener for change events.\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: Function): void { this._onChange.push(fn); }\n/**\n * \\@internal\n * @return {?}\n */\n_clearChangeFns(): void {\n    this._onChange = [];\n    this._onDisabledChange = [];\n    this._onCollectionChange = () => {};\n  }\n/**\n * Register a listener for disabled events.\n * @param {?} fn\n * @return {?}\n */\nregisterOnDisabledChange(fn: (isDisabled: boolean) => void): void {\n    this._onDisabledChange.push(fn);\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function): void {}\n/**\n * @param {?} formState\n * @return {?}\n */\nprivate _applyFormState(formState: any) {\n    if (this._isBoxedValue(formState)) {\n      this._value = formState.value;\n      formState.disabled ? this.disable({onlySelf: true, emitEvent: false}) :\n                           this.enable({onlySelf: true, emitEvent: false});\n    } else {\n      this._value = formState;\n    }\n  }\n}\n\nfunction FormControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nFormControl.prototype._onChange;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validity state of a group of {\\@link FormControl}\n * instances.\n * \n * A `FormGroup` aggregates the values of each child {\\@link FormControl} into one object,\n * with each control name as the key.  It calculates its status by reducing the statuses\n * of its children. For example, if one of the controls in a group is invalid, the entire\n * group becomes invalid.\n * \n * `FormGroup` is one of the three fundamental building blocks used to define forms in Angular,\n * along with {\\@link FormControl} and {\\@link FormArray}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormGroup}, pass in a collection of child controls as the first\n * argument. The key for each child will be the name under which it is registered.\n * \n * ### Example\n * \n * ```\n * const form = new FormGroup({\n *   first: new FormControl('Nancy', Validators.minLength(2)),\n *   last: new FormControl('Drew'),\n * });\n * \n * console.log(form.value);   // {first: 'Nancy', last; 'Drew'}\n * console.log(form.status);  // 'VALID'\n * ```\n * \n * You can also include group-level validators as the second arg, or group-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n * \n * ### Example\n * \n * ```\n * const form = new FormGroup({\n *   password: new FormControl('', Validators.minLength(2)),\n *   passwordConfirm: new FormControl('', Validators.minLength(2)),\n * }, passwordMatchValidator);\n * \n * \n * function passwordMatchValidator(g: FormGroup) {\n *    return g.get('password').value === g.get('passwordConfirm').value\n *       ? null : {'mismatch': true};\n * }\n * ```\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormGroup extends AbstractControl {\n/**\n * @param {?} controls\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\npublic controls: {[key: string]: AbstractControl}, validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null) {\n    super(validator || null, asyncValidator || null);\n    this._initObservables();\n    this._setUpControls();\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n  }\n/**\n * Registers a control with the group's list of controls.\n * \n * This method does not update value or validity of the control, so for\n * most cases you'll want to use {\\@link FormGroup#addControl} instead.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\nregisterControl(name: string, control: AbstractControl): AbstractControl {\n    if (this.controls[name]) return this.controls[name];\n    this.controls[name] = control;\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n    return control;\n  }\n/**\n * Add a control to this group.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\naddControl(name: string, control: AbstractControl): void {\n    this.registerControl(name, control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Remove a control from this group.\n * @param {?} name\n * @return {?}\n */\nremoveControl(name: string): void {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete (this.controls[name]);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Replace an existing control.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\nsetControl(name: string, control: AbstractControl): void {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete (this.controls[name]);\n    if (control) this.registerControl(name, control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Check whether there is an enabled control with the given name in the group.\n * \n * It will return false for disabled controls. If you'd like to check for\n * existence in the group only, use {\\@link AbstractControl#get} instead.\n * @param {?} controlName\n * @return {?}\n */\ncontains(controlName: string): boolean {\n    return this.controls.hasOwnProperty(controlName) && this.controls[controlName].enabled;\n  }\n/**\n *  Sets the value of the {\\@link FormGroup}. It accepts an object that matches\n *  the structure of the group, with control names as keys.\n * \n * This method performs strict checks, so it will throw an error if you try\n * to set the value of a control that doesn't exist or if you exclude the\n * value of a control.\n * \n *  ### Example\n * \n *  ```\n *  const form = new FormGroup({\n *     first: new FormControl(),\n *     last: new FormControl()\n *  });\n *  console.log(form.value);   // {first: null, last: null}\n * \n *  form.setValue({first: 'Nancy', last: 'Drew'});\n *  console.log(form.value);   // {first: 'Nancy', last: 'Drew'}\n * \n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: {[key: string]: any}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}):\n      void {\n    this._checkAllValuesPresent(value);\n    Object.keys(value).forEach(name => {\n      this._throwIfControlMissing(name);\n      this.controls[name].setValue(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n *  Patches the value of the {\\@link FormGroup}. It accepts an object with control\n *  names as keys, and will do its best to match the values to the correct controls\n *  in the group.\n * \n *  It accepts both super-sets and sub-sets of the group without throwing an error.\n * \n *  ### Example\n * \n *  ```\n *  const form = new FormGroup({\n *     first: new FormControl(),\n *     last: new FormControl()\n *  });\n *  console.log(form.value);   // {first: null, last: null}\n * \n *  form.patchValue({first: 'Nancy'});\n *  console.log(form.value);   // {first: 'Nancy', last: null}\n * \n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: {[key: string]: any}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}):\n      void {\n    Object.keys(value).forEach(name => {\n      if (this.controls[name]) {\n        this.controls[name].patchValue(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n * Resets the {\\@link FormGroup}. This means by default:\n * \n * * The group and all descendants are marked `pristine`\n * * The group and all descendants are marked `untouched`\n * * The value of all descendants will be null or null maps\n * \n * You can also reset to a specific form state by passing in a map of states\n * that matches the structure of your form, with control names as keys. The state\n * can be a standalone value or a form state object with both a value and a disabled\n * status.\n * \n * ### Example\n * \n * ```ts\n * this.form.reset({first: 'name', last: 'last name'});\n * \n * console.log(this.form.value);  // {first: 'name', last: 'last name'}\n * ```\n * \n * - OR -\n * \n * ```\n * this.form.reset({\n *   first: {value: 'name', disabled: true},\n *   last: 'last'\n * });\n * \n * console.log(this.form.value);  // {first: 'name', last: 'last name'}\n * console.log(this.form.get('first').status);  // 'DISABLED'\n * ```\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value: any = {}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._forEachChild((control: AbstractControl, name: string) => {\n      control.reset(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n    this._updatePristine(options);\n    this._updateTouched(options);\n  }\n/**\n * The aggregate value of the {\\@link FormGroup}, including any disabled controls.\n * \n * If you'd like to include all values regardless of disabled status, use this method.\n * Otherwise, the `value` property is the best way to get the value of the group.\n * @return {?}\n */\ngetRawValue(): any {\n    return this._reduceChildren(\n        {}, (acc: {[k: string]: AbstractControl}, control: AbstractControl, name: string) => {\n          acc[name] = control instanceof FormControl ? control.value : ( /** @type {?} */((<any>control))).getRawValue();\n          return acc;\n        });\n  }\n/**\n * \\@internal\n * @param {?} name\n * @return {?}\n */\n_throwIfControlMissing(name: string): void {\n    if (!Object.keys(this.controls).length) {\n      throw new Error(`\n        There are no form controls registered with this group yet.  If you're using ngModel,\n        you may want to check next tick (e.g. use setTimeout).\n      `);\n    }\n    if (!this.controls[name]) {\n      throw new Error(`Cannot find form control with name: ${name}.`);\n    }\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: (v: any, k: string) => void): void {\n    Object.keys(this.controls).forEach(k => cb(this.controls[k], k));\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_setUpControls(): void {\n    this._forEachChild((control: AbstractControl) => {\n      control.setParent(this);\n      control._registerOnCollectionChange(this._onCollectionChange);\n    });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue(): void { this._value = this._reduceValue(); }\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean {\n    let /** @type {?} */ res = false;\n    this._forEachChild((control: AbstractControl, name: string) => {\n      res = res || (this.contains(name) && condition(control));\n    });\n    return res;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_reduceValue() {\n    return this._reduceChildren(\n        {}, (acc: {[k: string]: AbstractControl}, control: AbstractControl, name: string) => {\n          if (control.enabled || this.disabled) {\n            acc[name] = control.value;\n          }\n          return acc;\n        });\n  }\n/**\n * \\@internal\n * @param {?} initValue\n * @param {?} fn\n * @return {?}\n */\n_reduceChildren(initValue: any, fn: Function) {\n    let /** @type {?} */ res = initValue;\n    this._forEachChild(\n        (control: AbstractControl, name: string) => { res = fn(res, control, name); });\n    return res;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean {\n    for (const /** @type {?} */ controlName of Object.keys(this.controls)) {\n      if (this.controls[controlName].enabled) {\n        return false;\n      }\n    }\n    return Object.keys(this.controls).length > 0 || this.disabled;\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_checkAllValuesPresent(value: any): void {\n    this._forEachChild((control: AbstractControl, name: string) => {\n      if (value[name] === undefined) {\n        throw new Error(`Must supply a value for form control with name: '${name}'.`);\n      }\n    });\n  }\n}\n\nfunction FormGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroup.prototype.controls;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validity state of an array of {\\@link FormControl},\n * {\\@link FormGroup} or {\\@link FormArray} instances.\n * \n * A `FormArray` aggregates the values of each child {\\@link FormControl} into an array.\n * It calculates its status by reducing the statuses of its children. For example, if one of\n * the controls in a `FormArray` is invalid, the entire array becomes invalid.\n * \n * `FormArray` is one of the three fundamental building blocks used to define forms in Angular,\n * along with {\\@link FormControl} and {\\@link FormGroup}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormArray}, pass in an array of child controls as the first\n * argument.\n * \n * ### Example\n * \n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy', Validators.minLength(2)),\n *   new FormControl('Drew'),\n * ]);\n * \n * console.log(arr.value);   // ['Nancy', 'Drew']\n * console.log(arr.status);  // 'VALID'\n * ```\n * \n * You can also include array-level validators as the second arg, or array-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n * \n * ### Adding or removing controls\n * \n * To change the controls in the array, use the `push`, `insert`, or `removeAt` methods\n * in `FormArray` itself. These methods ensure the controls are properly tracked in the\n * form's hierarchy. Do not modify the array of `AbstractControl`s used to instantiate\n * the `FormArray` directly, as that will result in strange and unexpected behavior such\n * as broken change detection.\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormArray extends AbstractControl {\n/**\n * @param {?} controls\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\npublic controls: AbstractControl[], validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null) {\n    super(validator || null, asyncValidator || null);\n    this._initObservables();\n    this._setUpControls();\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n  }\n/**\n * Get the {\\@link AbstractControl} at the given `index` in the array.\n * @param {?} index\n * @return {?}\n */\nat(index: number): AbstractControl { return this.controls[index]; }\n/**\n * Insert a new {\\@link AbstractControl} at the end of the array.\n * @param {?} control\n * @return {?}\n */\npush(control: AbstractControl): void {\n    this.controls.push(control);\n    this._registerControl(control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Insert a new {\\@link AbstractControl} at the given `index` in the array.\n * @param {?} index\n * @param {?} control\n * @return {?}\n */\ninsert(index: number, control: AbstractControl): void {\n    this.controls.splice(index, 0, control);\n\n    this._registerControl(control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Remove the control at the given `index` in the array.\n * @param {?} index\n * @return {?}\n */\nremoveAt(index: number): void {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Replace an existing control.\n * @param {?} index\n * @param {?} control\n * @return {?}\n */\nsetControl(index: number, control: AbstractControl): void {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n\n    if (control) {\n      this.controls.splice(index, 0, control);\n      this._registerControl(control);\n    }\n\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Length of the control array.\n * @return {?}\n */\nget length(): number { return this.controls.length; }\n/**\n *  Sets the value of the {\\@link FormArray}. It accepts an array that matches\n *  the structure of the control.\n * \n * This method performs strict checks, so it will throw an error if you try\n * to set the value of a control that doesn't exist or if you exclude the\n * value of a control.\n * \n *  ### Example\n * \n *  ```\n *  const arr = new FormArray([\n *     new FormControl(),\n *     new FormControl()\n *  ]);\n *  console.log(arr.value);   // [null, null]\n * \n *  arr.setValue(['Nancy', 'Drew']);\n *  console.log(arr.value);   // ['Nancy', 'Drew']\n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any[], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._checkAllValuesPresent(value);\n    value.forEach((newValue: any, index: number) => {\n      this._throwIfControlMissing(index);\n      this.at(index).setValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n *  Patches the value of the {\\@link FormArray}. It accepts an array that matches the\n *  structure of the control, and will do its best to match the values to the correct\n *  controls in the group.\n * \n *  It accepts both super-sets and sub-sets of the array without throwing an error.\n * \n *  ### Example\n * \n *  ```\n *  const arr = new FormArray([\n *     new FormControl(),\n *     new FormControl()\n *  ]);\n *  console.log(arr.value);   // [null, null]\n * \n *  arr.patchValue(['Nancy']);\n *  console.log(arr.value);   // ['Nancy', null]\n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any[], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    value.forEach((newValue: any, index: number) => {\n      if (this.at(index)) {\n        this.at(index).patchValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n * Resets the {\\@link FormArray}. This means by default:\n * \n * * The array and all descendants are marked `pristine`\n * * The array and all descendants are marked `untouched`\n * * The value of all descendants will be null or null maps\n * \n * You can also reset to a specific form state by passing in an array of states\n * that matches the structure of the control. The state can be a standalone value\n * or a form state object with both a value and a disabled status.\n * \n * ### Example\n * \n * ```ts\n * this.arr.reset(['name', 'last name']);\n * \n * console.log(this.arr.value);  // ['name', 'last name']\n * ```\n * \n * - OR -\n * \n * ```\n * this.arr.reset([\n *   {value: 'name', disabled: true},\n *   'last'\n * ]);\n * \n * console.log(this.arr.value);  // ['name', 'last name']\n * console.log(this.arr.get(0).status);  // 'DISABLED'\n * ```\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value: any = [], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._forEachChild((control: AbstractControl, index: number) => {\n      control.reset(value[index], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n    this._updatePristine(options);\n    this._updateTouched(options);\n  }\n/**\n * The aggregate value of the array, including any disabled controls.\n * \n * If you'd like to include all values regardless of disabled status, use this method.\n * Otherwise, the `value` property is the best way to get the value of the array.\n * @return {?}\n */\ngetRawValue(): any[] {\n    return this.controls.map((control: AbstractControl) => {\n      return control instanceof FormControl ? control.value : ( /** @type {?} */((<any>control))).getRawValue();\n    });\n  }\n/**\n * \\@internal\n * @param {?} index\n * @return {?}\n */\n_throwIfControlMissing(index: number): void {\n    if (!this.controls.length) {\n      throw new Error(`\n        There are no form controls registered with this array yet.  If you're using ngModel,\n        you may want to check next tick (e.g. use setTimeout).\n      `);\n    }\n    if (!this.at(index)) {\n      throw new Error(`Cannot find form control at index ${index}`);\n    }\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function): void {\n    this.controls.forEach((control: AbstractControl, index: number) => { cb(control, index); });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue(): void {\n    this._value = this.controls.filter((control) => control.enabled || this.disabled)\n                      .map((control) => control.value);\n  }\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean {\n    return this.controls.some((control: AbstractControl) => control.enabled && condition(control));\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_setUpControls(): void {\n    this._forEachChild((control: AbstractControl) => this._registerControl(control));\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_checkAllValuesPresent(value: any): void {\n    this._forEachChild((control: AbstractControl, i: number) => {\n      if (value[i] === undefined) {\n        throw new Error(`Must supply a value for form control at index: ${i}.`);\n      }\n    });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean {\n    for (const /** @type {?} */ control of this.controls) {\n      if (control.enabled) return false;\n    }\n    return this.controls.length > 0 || this.disabled;\n  }\n/**\n * @param {?} control\n * @return {?}\n */\nprivate _registerControl(control: AbstractControl) {\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n  }\n}\n\nfunction FormArray_tsickle_Closure_declarations() {\n/** @type {?} */\nFormArray.prototype.controls;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Self} from '@angular/core';\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {NgControl} from './ng_control';\nexport class AbstractControlStatus {\nprivate _cd: AbstractControlDirective;\n/**\n * @param {?} cd\n */\nconstructor(cd: AbstractControlDirective) { this._cd = cd; }\n/**\n * @return {?}\n */\nget ngClassUntouched(): boolean { return this._cd.control ? this._cd.control.untouched : false; }\n/**\n * @return {?}\n */\nget ngClassTouched(): boolean { return this._cd.control ? this._cd.control.touched : false; }\n/**\n * @return {?}\n */\nget ngClassPristine(): boolean { return this._cd.control ? this._cd.control.pristine : false; }\n/**\n * @return {?}\n */\nget ngClassDirty(): boolean { return this._cd.control ? this._cd.control.dirty : false; }\n/**\n * @return {?}\n */\nget ngClassValid(): boolean { return this._cd.control ? this._cd.control.valid : false; }\n/**\n * @return {?}\n */\nget ngClassInvalid(): boolean { return this._cd.control ? this._cd.control.invalid : false; }\n/**\n * @return {?}\n */\nget ngClassPending(): boolean { return this._cd.control ? this._cd.control.pending : false; }\n}\n\nfunction AbstractControlStatus_tsickle_Closure_declarations() {\n/** @type {?} */\nAbstractControlStatus.prototype._cd;\n}\n\n\nexport const /** @type {?} */ ngControlStatusHost = {\n  '[class.ng-untouched]': 'ngClassUntouched',\n  '[class.ng-touched]': 'ngClassTouched',\n  '[class.ng-pristine]': 'ngClassPristine',\n  '[class.ng-dirty]': 'ngClassDirty',\n  '[class.ng-valid]': 'ngClassValid',\n  '[class.ng-invalid]': 'ngClassInvalid',\n  '[class.ng-pending]': 'ngClassPending',\n};\n/**\n * Directive automatically applied to Angular form controls that sets CSS classes\n * based on control status (valid/invalid/dirty/etc).\n * \n * \\@stable\n */\nexport class NgControlStatus extends AbstractControlStatus {\n/**\n * @param {?} cd\n */\nconstructor( cd: NgControl) { super(cd); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControlName],[ngModel],[formControl]', host: ngControlStatusHost}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgControl, decorators: [{ type: Self }, ]},\n];\n}\n\nfunction NgControlStatus_tsickle_Closure_declarations() {\n/** @type {?} */\nNgControlStatus.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgControlStatus.ctorParameters;\n}\n\n/**\n * Directive automatically applied to Angular form groups that sets CSS classes\n * based on control status (valid/invalid/dirty/etc).\n * \n * \\@stable\n */\nexport class NgControlStatusGroup extends AbstractControlStatus {\n/**\n * @param {?} cd\n */\nconstructor( cd: ControlContainer) { super(cd); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      '[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]',\n  host: ngControlStatusHost\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Self }, ]},\n];\n}\n\nfunction NgControlStatusGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nNgControlStatusGroup.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgControlStatusGroup.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {On<PERSON><PERSON>roy, OnInit} from '@angular/core';\n\nimport {FormGroup} from '../model';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {composeAsyncValidators, composeValidators, controlPath} from './shared';\nimport {AsyncValidatorFn, ValidatorFn} from './validators';\n/**\n * This is a base class for code shared between {\\@link NgModelGroup} and {\\@link FormGroupName}.\n * \n * \\@stable\n */\nexport class AbstractFormGroupDirective extends ControlContainer implements OnInit, OnDestroy {\n/**\n * \\@internal\n */\n_parent: ControlContainer;\n/**\n * \\@internal\n */\n_validators: any[];\n/**\n * \\@internal\n */\n_asyncValidators: any[];\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._checkParentType(); /** @type {?} */((\n    this.formDirective)).addFormGroup(this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeFormGroup(this);\n    }\n  }\n/**\n * Get the {\\@link FormGroup} backing this binding.\n * @return {?}\n */\nget control(): FormGroup { return /** @type {?} */(( this.formDirective)).getFormGroup(this); }\n/**\n * Get the path to this control group.\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, this._parent); }\n/**\n * Get the {\\@link Form} to which this group belongs.\n * @return {?}\n */\nget formDirective(): Form|null { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._validators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n    return composeAsyncValidators(this._asyncValidators);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {}\n}\n\nfunction AbstractFormGroupDirective_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._parent;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._validators;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._asyncValidators;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {FormArray, FormControl, FormGroup} from '../model';\nimport {Validators} from '../validators';\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {CheckboxControlValueAccessor} from './checkbox_value_accessor';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor} from './control_value_accessor';\nimport {DefaultValueAccessor} from './default_value_accessor';\nimport {NgControl} from './ng_control';\nimport {normalizeAsyncValidator, normalizeValidator} from './normalize_validator';\nimport {NumberValueAccessor} from './number_value_accessor';\nimport {RadioControlValueAccessor} from './radio_control_value_accessor';\nimport {RangeValueAccessor} from './range_value_accessor';\nimport {FormArrayName} from './reactive_directives/form_group_name';\nimport {SelectControlValueAccessor} from './select_control_value_accessor';\nimport {SelectMultipleControlValueAccessor} from './select_multiple_control_value_accessor';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @param {?} name\n * @param {?} parent\n * @return {?}\n */\nexport function controlPath(name: string, parent: ControlContainer): string[] {\n  return [... /** @type {?} */((parent.path)), name];\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function setUpControl(control: FormControl, dir: NgControl): void {\n  if (!control) _throwError(dir, 'Cannot find control with');\n  if (!dir.valueAccessor) _throwError(dir, 'No value accessor for form control with');\n\n  control.validator = Validators.compose([ /** @type {?} */((control.validator)), dir.validator]);\n  control.asyncValidator = Validators.composeAsync([ /** @type {?} */((control.asyncValidator)), dir.asyncValidator]); /** @type {?} */((\n  dir.valueAccessor)).writeValue(control.value); /** @type {?} */((\n\n  // view -> model\n  dir.valueAccessor)).registerOnChange((newValue: any) => {\n    dir.viewToModelUpdate(newValue);\n    control.markAsDirty();\n    control.setValue(newValue, {emitModelToViewChange: false});\n  }); /** @type {?} */((\n\n  // touched\n  dir.valueAccessor)).registerOnTouched(() => control.markAsTouched());\n\n  control.registerOnChange((newValue: any, emitModelEvent: boolean) => { /** @type {?} */((\n    // control -> view\n    dir.valueAccessor)).writeValue(newValue);\n\n    // control -> ngModel\n    if (emitModelEvent) dir.viewToModelUpdate(newValue);\n  });\n\n  if ( /** @type {?} */((dir.valueAccessor)).setDisabledState) {\n    control.registerOnDisabledChange(\n        (isDisabled: boolean) => { /** @type {?} */(( /** @type {?} */(( dir.valueAccessor)).setDisabledState))(isDisabled); });\n  }\n\n  // re-run validation when validator binding changes, e.g. minlength=3 -> minlength=4\n  dir._rawValidators.forEach((validator: Validator | ValidatorFn) => {\n    if (( /** @type {?} */((<Validator>validator))).registerOnValidatorChange) /** @type {?} */((\n      ( /** @type {?} */((<Validator>validator))).registerOnValidatorChange))(() => control.updateValueAndValidity());\n  });\n\n  dir._rawAsyncValidators.forEach((validator: AsyncValidator | AsyncValidatorFn) => {\n    if (( /** @type {?} */((<Validator>validator))).registerOnValidatorChange) /** @type {?} */((\n      ( /** @type {?} */((<Validator>validator))).registerOnValidatorChange))(() => control.updateValueAndValidity());\n  });\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function cleanUpControl(control: FormControl, dir: NgControl) { /** @type {?} */((\n  dir.valueAccessor)).registerOnChange(() => _noControlError(dir)); /** @type {?} */((\n  dir.valueAccessor)).registerOnTouched(() => _noControlError(dir));\n\n  dir._rawValidators.forEach((validator: any) => {\n    if (validator.registerOnValidatorChange) {\n      validator.registerOnValidatorChange(null);\n    }\n  });\n\n  dir._rawAsyncValidators.forEach((validator: any) => {\n    if (validator.registerOnValidatorChange) {\n      validator.registerOnValidatorChange(null);\n    }\n  });\n\n  if (control) control._clearChangeFns();\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function setUpFormContainer(\n    control: FormGroup | FormArray, dir: AbstractFormGroupDirective | FormArrayName) {\n  if (control == null) _throwError(dir, 'Cannot find control with');\n  control.validator = Validators.compose([control.validator, dir.validator]);\n  control.asyncValidator = Validators.composeAsync([control.asyncValidator, dir.asyncValidator]);\n}\n/**\n * @param {?} dir\n * @return {?}\n */\nfunction _noControlError(dir: NgControl) {\n  return _throwError(dir, 'There is no FormControl instance attached to form control element with');\n}\n/**\n * @param {?} dir\n * @param {?} message\n * @return {?}\n */\nfunction _throwError(dir: AbstractControlDirective, message: string): void {\n  let /** @type {?} */ messageEnd: string;\n  if ( /** @type {?} */((dir.path)).length > 1) {\n    messageEnd = `path: '${ /** @type {?} */((dir.path)).join(' -> ')}'`;\n  } else if ( /** @type {?} */((dir.path))[0]) {\n    messageEnd = `name: '${dir.path}'`;\n  } else {\n    messageEnd = 'unspecified name attribute';\n  }\n  throw new Error(`${message} ${messageEnd}`);\n}\n/**\n * @param {?} validators\n * @return {?}\n */\nexport function composeValidators(validators: Array<Validator|Function>): ValidatorFn|null {\n  return validators != null ? Validators.compose(validators.map(normalizeValidator)) : null;\n}\n/**\n * @param {?} validators\n * @return {?}\n */\nexport function composeAsyncValidators(validators: Array<Validator|Function>): AsyncValidatorFn|\n    null {\n  return validators != null ? Validators.composeAsync(validators.map(normalizeAsyncValidator)) :\n                              null;\n}\n/**\n * @param {?} changes\n * @param {?} viewModel\n * @return {?}\n */\nexport function isPropertyUpdated(changes: {[key: string]: any}, viewModel: any): boolean {\n  if (!changes.hasOwnProperty('model')) return false;\n  const /** @type {?} */ change = changes['model'];\n\n  if (change.isFirstChange()) return true;\n  return !looseIdentical(viewModel, change.currentValue);\n}\n\nconst /** @type {?} */ BUILTIN_ACCESSORS = [\n  CheckboxControlValueAccessor,\n  RangeValueAccessor,\n  NumberValueAccessor,\n  SelectControlValueAccessor,\n  SelectMultipleControlValueAccessor,\n  RadioControlValueAccessor,\n];\n/**\n * @param {?} valueAccessor\n * @return {?}\n */\nexport function isBuiltInAccessor(valueAccessor: ControlValueAccessor): boolean {\n  return BUILTIN_ACCESSORS.some(a => valueAccessor.constructor === a);\n}\n/**\n * @param {?} dir\n * @param {?} valueAccessors\n * @return {?}\n */\nexport function selectValueAccessor(\n    dir: NgControl, valueAccessors: ControlValueAccessor[]): ControlValueAccessor|null {\n  if (!valueAccessors) return null;\n\n  let /** @type {?} */ defaultAccessor: ControlValueAccessor|undefined = undefined;\n  let /** @type {?} */ builtinAccessor: ControlValueAccessor|undefined = undefined;\n  let /** @type {?} */ customAccessor: ControlValueAccessor|undefined = undefined;\n  valueAccessors.forEach((v: ControlValueAccessor) => {\n    if (v.constructor === DefaultValueAccessor) {\n      defaultAccessor = v;\n\n    } else if (isBuiltInAccessor(v)) {\n      if (builtinAccessor)\n        _throwError(dir, 'More than one built-in value accessor matches form control with');\n      builtinAccessor = v;\n\n    } else {\n      if (customAccessor)\n        _throwError(dir, 'More than one custom value accessor matches form control with');\n      customAccessor = v;\n    }\n  });\n\n  if (customAccessor) return customAccessor;\n  if (builtinAccessor) return builtinAccessor;\n  if (defaultAccessor) return defaultAccessor;\n\n  _throwError(dir, 'No valid value accessor for form control with');\n  return null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Host, Input, OnDestroy, Optional, Provider, Renderer, forwardRef, ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ SELECT_MULTIPLE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectMultipleControlValueAccessor),\n  multi: true\n};\n/**\n * @param {?} id\n * @param {?} value\n * @return {?}\n */\nfunction _buildValueString(id: string, value: any): string {\n  if (id == null) return `${value}`;\n  if (typeof value === 'string') value = `'${value}'`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n/**\n * @param {?} valueString\n * @return {?}\n */\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n\n/** Mock interface for HTML Options */\ninterface HTMLOption {\n  value: string;\n  selected: boolean;\n}\n/**\n * Mock interface for HTMLCollection\n * @abstract\n */\nabstract class HTMLCollection {\n  length: number;\n/**\n * @abstract\n * @param {?} _\n * @return {?}\n */\nitem(_: number) {}\n}\n\nfunction HTMLCollection_tsickle_Closure_declarations() {\n/** @type {?} */\nHTMLCollection.prototype.length;\n}\n\n/**\n * The accessor for writing a value and listening to changes on a select element.\n * \n *  ### Caveat: Options selection\n * \n * Angular uses object identity to select options. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n * \n * To customize the default option comparison algorithm, `<select multiple>` supports `compareWith`\n * input. `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects options by the return value of the function.\n * \n * #### Syntax\n * \n * ```\n * <select multiple [compareWith]=\"compareFn\"  [(ngModel)]=\"selectedCountries\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n * \n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n * \n * \\@stable\n */\nexport class SelectMultipleControlValueAccessor implements ControlValueAccessor {\n  value: any;\n/**\n * \\@internal\n */\n_optionMap: Map<string, NgSelectMultipleOption> = new Map<string, NgSelectMultipleOption>();\n/**\n * \\@internal\n */\n_idCounter: number = 0;\n\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} fn\n * @return {?}\n */\nset compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function') {\n      throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\nprivate _compareWith: (o1: any, o2: any) => boolean = looseIdentical;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this.value = value;\n    let /** @type {?} */ optionSelectedStateSetter: (opt: NgSelectMultipleOption, o: any) => void;\n    if (Array.isArray(value)) {\n      // convert values to ids\n      const /** @type {?} */ ids = value.map((v) => this._getOptionId(v));\n      optionSelectedStateSetter = (opt, o) => { opt._setSelected(ids.indexOf(o.toString()) > -1); };\n    } else {\n      optionSelectedStateSetter = (opt, o) => { opt._setSelected(false); };\n    }\n    this._optionMap.forEach(optionSelectedStateSetter);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (value: any) => any): void {\n    this.onChange = (_: any) => {\n      const /** @type {?} */ selected: Array<any> = [];\n      if (_.hasOwnProperty('selectedOptions')) {\n        const /** @type {?} */ options: HTMLCollection = _.selectedOptions;\n        for (let /** @type {?} */ i = 0; i < options.length; i++) {\n          const /** @type {?} */ opt: any = options.item(i);\n          const /** @type {?} */ val: any = this._getOptionValue(opt.value);\n          selected.push(val);\n        }\n      }\n      // Degrade on IE\n      else {\n        const /** @type {?} */ options: HTMLCollection = /** @type {?} */(( <HTMLCollection>_.options));\n        for (let /** @type {?} */ i = 0; i < options.length; i++) {\n          const /** @type {?} */ opt: HTMLOption = options.item(i);\n          if (opt.selected) {\n            const /** @type {?} */ val: any = this._getOptionValue(opt.value);\n            selected.push(val);\n          }\n        }\n      }\n      this.value = selected;\n      fn(selected);\n    };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => any): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_registerOption(value: NgSelectMultipleOption): string {\n    const /** @type {?} */ id: string = (this._idCounter++).toString();\n    this._optionMap.set(id, value);\n    return id;\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_getOptionId(value: any): string|null {\n    for (const /** @type {?} */ id of Array.from(this._optionMap.keys())) {\n      if (this._compareWith( /** @type {?} */((this._optionMap.get(id)))._value, value)) return id;\n    }\n    return null;\n  }\n/**\n * \\@internal\n * @param {?} valueString\n * @return {?}\n */\n_getOptionValue(valueString: string): any {\n    const /** @type {?} */ id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? /** @type {?} */(( this._optionMap.get(id)))._value : valueString;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'select[multiple][formControlName],select[multiple][formControl],select[multiple][ngModel]',\n  host: {'(change)': 'onChange($event.target)', '(blur)': 'onTouched()'},\n  providers: [SELECT_MULTIPLE_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'compareWith': [{ type: Input },],\n};\n}\n\nfunction SelectMultipleControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nSelectMultipleControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSelectMultipleControlValueAccessor.ctorParameters;\n/** @type {?} */\nSelectMultipleControlValueAccessor.propDecorators;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.value;\n/**\n * \\@internal\n * @type {?}\n */\nSelectMultipleControlValueAccessor.prototype._optionMap;\n/**\n * \\@internal\n * @type {?}\n */\nSelectMultipleControlValueAccessor.prototype._idCounter;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.onChange;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._compareWith;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._renderer;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._elementRef;\n}\n\n/**\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n * \n * ### Example\n * \n * ```\n * <select multiple name=\"city\" ngModel>\n *   <option *ngFor=\"let c of cities\" [value]=\"c\"></option>\n * </select>\n * ```\n */\nexport class NgSelectMultipleOption implements OnDestroy {\n  id: string;\n/**\n * \\@internal\n */\n_value: any;\n/**\n * @param {?} _element\n * @param {?} _renderer\n * @param {?} _select\n */\nconstructor(\nprivate _element: ElementRef,\nprivate _renderer: Renderer,\nprivate _select: SelectMultipleControlValueAccessor) {\n    if (this._select) {\n      this.id = this._select._registerOption(this);\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset ngValue(value: any) {\n    if (this._select == null) return;\n    this._value = value;\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset value(value: any) {\n    if (this._select) {\n      this._value = value;\n      this._setElementValue(_buildValueString(this.id, value));\n      this._select.writeValue(this._select.value);\n    } else {\n      this._setElementValue(value);\n    }\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_setElementValue(value: string): void {\n    this._renderer.setElementProperty(this._element.nativeElement, 'value', value);\n  }\n/**\n * \\@internal\n * @param {?} selected\n * @return {?}\n */\n_setSelected(selected: boolean) {\n    this._renderer.setElementProperty(this._element.nativeElement, 'selected', selected);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'option'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ElementRef, },\n{type: Renderer, },\n{type: SelectMultipleControlValueAccessor, decorators: [{ type: Optional }, { type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngValue': [{ type: Input, args: ['ngValue', ] },],\n'value': [{ type: Input, args: ['value', ] },],\n};\n}\n\nfunction NgSelectMultipleOption_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSelectMultipleOption.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSelectMultipleOption.ctorParameters;\n/** @type {?} */\nNgSelectMultipleOption.propDecorators;\n/** @type {?} */\nNgSelectMultipleOption.prototype.id;\n/**\n * \\@internal\n * @type {?}\n */\nNgSelectMultipleOption.prototype._value;\n/** @type {?} */\nNgSelectMultipleOption.prototype._element;\n/** @type {?} */\nNgSelectMultipleOption.prototype._renderer;\n/** @type {?} */\nNgSelectMultipleOption.prototype._select;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Host, Input, OnD<PERSON>roy, Optional, Provider, Renderer, forwardRef, ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ SELECT_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectControlValueAccessor),\n  multi: true\n};\n/**\n * @param {?} id\n * @param {?} value\n * @return {?}\n */\nfunction _buildValueString(id: string | null, value: any): string {\n  if (id == null) return `${value}`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n/**\n * @param {?} valueString\n * @return {?}\n */\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n/**\n * \\@whatItDoes Writes values and listens to changes on a select element.\n * \n * Used by {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName}\n * to keep the view synced with the {\\@link FormControl} model.\n * \n * \\@howToUse \n * \n * If you have imported the {\\@link FormsModule} or the {\\@link ReactiveFormsModule}, this\n * value accessor will be active on any select control that has a form directive. You do\n * **not** need to add a special selector to activate it.\n * \n * ### How to use select controls with form directives\n * \n * To use a select in a template-driven form, simply add an `ngModel` and a `name`\n * attribute to the main `<select>` tag.\n * \n * If your option values are simple strings, you can bind to the normal `value` property\n * on the option.  If your option values happen to be objects (and you'd like to save the\n * selection in your form as an object), use `ngValue` instead:\n * \n * {\\@example forms/ts/selectControl/select_control_example.ts region='Component'}\n * \n * In reactive forms, you'll also want to add your form directive (`formControlName` or\n * `formControl`) on the main `<select>` tag. Like in the former example, you have the\n * choice of binding to the  `value` or `ngValue` property on the select's options.\n * \n * {\\@example forms/ts/reactiveSelectControl/reactive_select_control_example.ts region='Component'}\n * \n * ### Caveat: Option selection\n * \n * Angular uses object identity to select option. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n * \n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects option by the return value of the function.\n * \n * #### Syntax\n * \n * ```\n * <select [compareWith]=\"compareFn\"  [(ngModel)]=\"selectedCountries\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n * \n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n * \n * Note: We listen to the 'change' event because 'input' events aren't fired\n * for selects in Firefox and IE:\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1024350\n * https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/4660045/\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class SelectControlValueAccessor implements ControlValueAccessor {\n  value: any;\n/**\n * \\@internal\n */\n_optionMap: Map<string, any> = new Map<string, any>();\n/**\n * \\@internal\n */\n_idCounter: number = 0;\n\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} fn\n * @return {?}\n */\nset compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function') {\n      throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\nprivate _compareWith: (o1: any, o2: any) => boolean = looseIdentical;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this.value = value;\n    const /** @type {?} */ id: string|null = this._getOptionId(value);\n    if (id == null) {\n      this._renderer.setElementProperty(this._elementRef.nativeElement, 'selectedIndex', -1);\n    }\n    const /** @type {?} */ valueString = _buildValueString(id, value);\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', valueString);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (value: any) => any): void {\n    this.onChange = (valueString: string) => {\n      this.value = valueString;\n      fn(this._getOptionValue(valueString));\n    };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => any): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_registerOption(): string { return (this._idCounter++).toString(); }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_getOptionId(value: any): string|null {\n    for (const /** @type {?} */ id of Array.from(this._optionMap.keys())) {\n      if (this._compareWith(this._optionMap.get(id), value)) return id;\n    }\n    return null;\n  }\n/**\n * \\@internal\n * @param {?} valueString\n * @return {?}\n */\n_getOptionValue(valueString: string): any {\n    const /** @type {?} */ id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id) : valueString;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]',\n  host: {'(change)': 'onChange($event.target.value)', '(blur)': 'onTouched()'},\n  providers: [SELECT_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'compareWith': [{ type: Input },],\n};\n}\n\nfunction SelectControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nSelectControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSelectControlValueAccessor.ctorParameters;\n/** @type {?} */\nSelectControlValueAccessor.propDecorators;\n/** @type {?} */\nSelectControlValueAccessor.prototype.value;\n/**\n * \\@internal\n * @type {?}\n */\nSelectControlValueAccessor.prototype._optionMap;\n/**\n * \\@internal\n * @type {?}\n */\nSelectControlValueAccessor.prototype._idCounter;\n/** @type {?} */\nSelectControlValueAccessor.prototype.onChange;\n/** @type {?} */\nSelectControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nSelectControlValueAccessor.prototype._compareWith;\n/** @type {?} */\nSelectControlValueAccessor.prototype._renderer;\n/** @type {?} */\nSelectControlValueAccessor.prototype._elementRef;\n}\n\n/**\n * \\@whatItDoes Marks `<option>` as dynamic, so Angular can be notified when options change.\n * \n * \\@howToUse \n * \n * See docs for {\\@link SelectControlValueAccessor} for usage examples.\n * \n * \\@stable\n */\nexport class NgSelectOption implements OnDestroy {\n  id: string;\n/**\n * @param {?} _element\n * @param {?} _renderer\n * @param {?} _select\n */\nconstructor(\nprivate _element: ElementRef,\nprivate _renderer: Renderer,\nprivate _select: SelectControlValueAccessor) {\n    if (this._select) this.id = this._select._registerOption();\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset ngValue(value: any) {\n    if (this._select == null) return;\n    this._select._optionMap.set(this.id, value);\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset value(value: any) {\n    this._setElementValue(value);\n    if (this._select) this._select.writeValue(this._select.value);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_setElementValue(value: string): void {\n    this._renderer.setElementProperty(this._element.nativeElement, 'value', value);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'option'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ElementRef, },\n{type: Renderer, },\n{type: SelectControlValueAccessor, decorators: [{ type: Optional }, { type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngValue': [{ type: Input, args: ['ngValue', ] },],\n'value': [{ type: Input, args: ['value', ] },],\n};\n}\n\nfunction NgSelectOption_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSelectOption.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSelectOption.ctorParameters;\n/** @type {?} */\nNgSelectOption.propDecorators;\n/** @type {?} */\nNgSelectOption.prototype.id;\n/** @type {?} */\nNgSelectOption.prototype._element;\n/** @type {?} */\nNgSelectOption.prototype._renderer;\n/** @type {?} */\nNgSelectOption.prototype._select;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Provider, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ RANGE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RangeValueAccessor),\n  multi: true\n};\n/**\n * The accessor for writing a range value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"range\" [(ngModel)]=\"age\" >\n *  ```\n */\nexport class RangeValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', parseFloat(value));\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => { fn(value == '' ? null : parseFloat(value)); };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]',\n  host: {\n    '(change)': 'onChange($event.target.value)',\n    '(input)': 'onChange($event.target.value)',\n    '(blur)': 'onTouched()'\n  },\n  providers: [RANGE_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction RangeValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nRangeValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRangeValueAccessor.ctorParameters;\n/** @type {?} */\nRangeValueAccessor.prototype.onChange;\n/** @type {?} */\nRangeValueAccessor.prototype.onTouched;\n/** @type {?} */\nRangeValueAccessor.prototype._renderer;\n/** @type {?} */\nRangeValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Injectable, Injector, Input, OnDestroy, OnInit, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\n\nexport const /** @type {?} */ RADIO_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RadioControlValueAccessor),\n  multi: true\n};\n/**\n * Internal class used by Angular to uncheck radio buttons with the matching name.\n */\nexport class RadioControlRegistry {\nprivate _accessors: any[] = [];\n/**\n * @param {?} control\n * @param {?} accessor\n * @return {?}\n */\nadd(control: NgControl, accessor: RadioControlValueAccessor) {\n    this._accessors.push([control, accessor]);\n  }\n/**\n * @param {?} accessor\n * @return {?}\n */\nremove(accessor: RadioControlValueAccessor) {\n    for (let /** @type {?} */ i = this._accessors.length - 1; i >= 0; --i) {\n      if (this._accessors[i][1] === accessor) {\n        this._accessors.splice(i, 1);\n        return;\n      }\n    }\n  }\n/**\n * @param {?} accessor\n * @return {?}\n */\nselect(accessor: RadioControlValueAccessor) {\n    this._accessors.forEach((c) => {\n      if (this._isSameGroup(c, accessor) && c[1] !== accessor) {\n        c[1].fireUncheck(accessor.value);\n      }\n    });\n  }\n/**\n * @param {?} controlPair\n * @param {?} accessor\n * @return {?}\n */\nprivate _isSameGroup(\n      controlPair: [NgControl, RadioControlValueAccessor],\n      accessor: RadioControlValueAccessor): boolean {\n    if (!controlPair[0].control) return false;\n    return controlPair[0]._parent === accessor._control._parent &&\n        controlPair[1].name === accessor.name;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction RadioControlRegistry_tsickle_Closure_declarations() {\n/** @type {?} */\nRadioControlRegistry.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRadioControlRegistry.ctorParameters;\n/** @type {?} */\nRadioControlRegistry.prototype._accessors;\n}\n\n/**\n * \\@whatItDoes Writes radio control values and listens to radio control changes.\n * \n * Used by {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName}\n * to keep the view synced with the {\\@link FormControl} model.\n * \n * \\@howToUse \n * \n * If you have imported the {\\@link FormsModule} or the {\\@link ReactiveFormsModule}, this\n * value accessor will be active on any radio control that has a form directive. You do\n * **not** need to add a special selector to activate it.\n * \n * ### How to use radio buttons with form directives\n * \n * To use radio buttons in a template-driven form, you'll want to ensure that radio buttons\n * in the same group have the same `name` attribute.  Radio buttons with different `name`\n * attributes do not affect each other.\n * \n * {\\@example forms/ts/radioButtons/radio_button_example.ts region='TemplateDriven'}\n * \n * When using radio buttons in a reactive form, radio buttons in the same group should have the\n * same `formControlName`. You can also add a `name` attribute, but it's optional.\n * \n * {\\@example forms/ts/reactiveRadioButtons/reactive_radio_button_example.ts region='Reactive'}\n * \n *  * **npm package**: `\\@angular/forms`\n * \n *  \\@stable\n */\nexport class RadioControlValueAccessor implements ControlValueAccessor,\n    OnDestroy, OnInit {\n/**\n * \\@internal\n */\n_state: boolean;\n/**\n * \\@internal\n */\n_control: NgControl;\n/**\n * \\@internal\n */\n_fn: Function;\n  onChange = () => {};\n  onTouched = () => {};\n\n   name: string;\n   formControlName: string;\n   value: any;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n * @param {?} _registry\n * @param {?} _injector\n */\nconstructor(\nprivate _renderer: Renderer,\nprivate _elementRef: ElementRef,\nprivate _registry: RadioControlRegistry,\nprivate _injector: Injector) {}\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._control = this._injector.get(NgControl);\n    this._checkName();\n    this._registry.add(this._control, this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this._registry.remove(this); }\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._state = value === this.value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'checked', this._state);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => {}): void {\n    this._fn = fn;\n    this.onChange = () => {\n      fn(this.value);\n      this._registry.select(this);\n    };\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nfireUncheck(value: any): void { this.writeValue(value); }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => {}): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * @return {?}\n */\nprivate _checkName(): void {\n    if (this.name && this.formControlName && this.name !== this.formControlName) {\n      this._throwNameError();\n    }\n    if (!this.name && this.formControlName) this.name = this.formControlName;\n  }\n/**\n * @return {?}\n */\nprivate _throwNameError(): void {\n    throw new Error(`\n      If you define both a name and a formControlName attribute on your radio button, their values\n      must match. Ex: <input type=\"radio\" formControlName=\"food\" name=\"food\">\n    `);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=radio][formControlName],input[type=radio][formControl],input[type=radio][ngModel]',\n  host: {'(change)': 'onChange()', '(blur)': 'onTouched()'},\n  providers: [RADIO_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n{type: RadioControlRegistry, },\n{type: Injector, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input },],\n'formControlName': [{ type: Input },],\n'value': [{ type: Input },],\n};\n}\n\nfunction RadioControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nRadioControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRadioControlValueAccessor.ctorParameters;\n/** @type {?} */\nRadioControlValueAccessor.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._state;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._control;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._fn;\n/** @type {?} */\nRadioControlValueAccessor.prototype.onChange;\n/** @type {?} */\nRadioControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nRadioControlValueAccessor.prototype.name;\n/** @type {?} */\nRadioControlValueAccessor.prototype.formControlName;\n/** @type {?} */\nRadioControlValueAccessor.prototype.value;\n/** @type {?} */\nRadioControlValueAccessor.prototype._renderer;\n/** @type {?} */\nRadioControlValueAccessor.prototype._elementRef;\n/** @type {?} */\nRadioControlValueAccessor.prototype._registry;\n/** @type {?} */\nRadioControlValueAccessor.prototype._injector;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor} from './control_value_accessor';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @return {?}\n */\nfunction unimplemented(): any {\n  throw new Error('unimplemented');\n}\n/**\n * A base class that all control directive extend.\n * It binds a {\\@link FormControl} object to a DOM element.\n * \n * Used internally by Angular forms.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class NgControl extends AbstractControlDirective {\n/**\n * \\@internal\n */\n_parent: ControlContainer|null = null;\n  name: string|null = null;\n  valueAccessor: ControlValueAccessor|null = null;\n/**\n * \\@internal\n */\n_rawValidators: Array<Validator|ValidatorFn> = [];\n/**\n * \\@internal\n */\n_rawAsyncValidators: Array<AsyncValidator|AsyncValidatorFn> = [];\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return /** @type {?} */(( <ValidatorFn>unimplemented())); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null { return /** @type {?} */(( <AsyncValidatorFn>unimplemented())); }\n/**\n * @abstract\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any) {}\n}\n\nfunction NgControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._parent;\n/** @type {?} */\nNgControl.prototype.name;\n/** @type {?} */\nNgControl.prototype.valueAccessor;\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._rawValidators;\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._rawAsyncValidators;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ NUMBER_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => NumberValueAccessor),\n  multi: true\n};\n/**\n * The accessor for writing a number value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"number\" [(ngModel)]=\"age\">\n *  ```\n */\nexport class NumberValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: number): void {\n    // The value needs to be normalized for IE9, otherwise it is set to 'null' when null\n    const /** @type {?} */ normalizedValue = value == null ? '' : value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', normalizedValue);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => { fn(value == '' ? null : parseFloat(value)); };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=number][formControlName],input[type=number][formControl],input[type=number][ngModel]',\n  host: {\n    '(change)': 'onChange($event.target.value)',\n    '(input)': 'onChange($event.target.value)',\n    '(blur)': 'onTouched()'\n  },\n  providers: [NUMBER_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction NumberValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nNumberValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNumberValueAccessor.ctorParameters;\n/** @type {?} */\nNumberValueAccessor.prototype.onChange;\n/** @type {?} */\nNumberValueAccessor.prototype.onTouched;\n/** @type {?} */\nNumberValueAccessor.prototype._renderer;\n/** @type {?} */\nNumberValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AbstractControl} from '../model';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @param {?} validator\n * @return {?}\n */\nexport function normalizeValidator(validator: ValidatorFn | Validator): ValidatorFn {\n  if (( /** @type {?} */((<Validator>validator))).validate) {\n    return (c: AbstractControl) => ( /** @type {?} */((<Validator>validator))).validate(c);\n  } else {\n    return /** @type {?} */(( <ValidatorFn>validator));\n  }\n}\n/**\n * @param {?} validator\n * @return {?}\n */\nexport function normalizeAsyncValidator(validator: AsyncValidatorFn | AsyncValidator):\n    AsyncValidatorFn {\n  if (( /** @type {?} */((<AsyncValidator>validator))).validate) {\n    return (c: AbstractControl) => ( /** @type {?} */((<AsyncValidator>validator))).validate(c);\n  } else {\n    return /** @type {?} */(( <AsyncValidatorFn>validator));\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Inject, InjectionToken, Optional, Renderer, forwardRef} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ DEFAULT_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => DefaultValueAccessor),\n  multi: true\n};\n/**\n * We must check whether the agent is Android because composition events\n * behave differently between iOS and Android.\n * @return {?}\n */\nfunction _isAndroid(): boolean {\n  const /** @type {?} */ userAgent = getDOM() ? getDOM().getUserAgent() : '';\n  return /android (\\d+)/.test(userAgent.toLowerCase());\n}\n/**\n * Turn this mode on if you want form directives to buffer IME input until compositionend\n * \\@experimental\n */\nexport const COMPOSITION_BUFFER_MODE = new InjectionToken<boolean>('CompositionEventMode');\n/**\n * The default accessor for writing a value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"text\" name=\"searchQuery\" ngModel>\n *  ```\n * \n *  \\@stable\n */\nexport class DefaultValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * Whether the user is creating a composition string (IME events).\n */\nprivate _composing = false;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n * @param {?} _compositionMode\n */\nconstructor(\nprivate _renderer: Renderer,\nprivate _elementRef: ElementRef,\nprivate _compositionMode: boolean) {\n    if (this._compositionMode == null) {\n      this._compositionMode = !_isAndroid();\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    const /** @type {?} */ normalizedValue = value == null ? '' : value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', normalizedValue);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => void): void { this.onChange = fn; }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\n_handleInput(value: any): void {\n    if (!this._compositionMode || (this._compositionMode && !this._composing)) {\n      this.onChange(value);\n    }\n  }\n/**\n * @return {?}\n */\n_compositionStart(): void { this._composing = true; }\n/**\n * @param {?} value\n * @return {?}\n */\n_compositionEnd(value: any): void {\n    this._composing = false;\n    this._compositionMode && this.onChange(value);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]',\n  // TODO: vsavkin replace the above selector with the one below it once\n  // https://github.com/angular/angular/issues/3011 is implemented\n  // selector: '[ngModel],[formControl],[formControlName]',\n  host: {\n    '(input)': '_handleInput($event.target.value)',\n    '(blur)': 'onTouched()',\n    '(compositionstart)': '_compositionStart()',\n    '(compositionend)': '_compositionEnd($event.target.value)'\n  },\n  providers: [DEFAULT_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [COMPOSITION_BUFFER_MODE, ] }, ]},\n];\n}\n\nfunction DefaultValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nDefaultValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDefaultValueAccessor.ctorParameters;\n/** @type {?} */\nDefaultValueAccessor.prototype.onChange;\n/** @type {?} */\nDefaultValueAccessor.prototype.onTouched;\n/**\n * Whether the user is creating a composition string (IME events).\n * @type {?}\n */\nDefaultValueAccessor.prototype._composing;\n/** @type {?} */\nDefaultValueAccessor.prototype._renderer;\n/** @type {?} */\nDefaultValueAccessor.prototype._elementRef;\n/** @type {?} */\nDefaultValueAccessor.prototype._compositionMode;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ CHECKBOX_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => CheckboxControlValueAccessor),\n  multi: true,\n};\n/**\n * The accessor for writing a value and listening to changes on a checkbox input element.\n * \n *  ### Example\n *  ```\n *  <input type=\"checkbox\" name=\"rememberLogin\" ngModel>\n *  ```\n * \n *  \\@stable\n */\nexport class CheckboxControlValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: <PERSON>derer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'checked', value);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => {}): void { this.onChange = fn; }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => {}): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]',\n  host: {'(change)': 'onChange($event.target.checked)', '(blur)': 'onTouched()'},\n  providers: [CHECKBOX_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction CheckboxControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nCheckboxControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCheckboxControlValueAccessor.ctorParameters;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype.onChange;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype._renderer;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * A bridge between a control and a native element.\n *\n * A `ControlValueAccessor` abstracts the operations of writing a new value to a\n * DOM element representing an input control.\n *\n * Please see {@link DefaultValueAccessor} for more information.\n *\n * @stable\n */\nexport interface ControlValueAccessor {\n  /**\n   * Write a new value to the element.\n   */\n  writeValue(obj: any): void;\n\n  /**\n   * Set the function to be called when the control receives a change event.\n   */\n  registerOnChange(fn: any): void;\n\n  /**\n   * Set the function to be called when the control receives a touch event.\n   */\n  registerOnTouched(fn: any): void;\n\n  /**\n   * This function is called when the control status changes to or from \"DISABLED\".\n   * Depending on the value, it will enable or disable the appropriate DOM element.\n   *\n   * @param isDisabled\n   */\n  setDisabledState?(isDisabled: boolean): void;\n}\n/**\n * Used to provide a {\\@link ControlValueAccessor} for form controls.\n * \n * See {\\@link DefaultValueAccessor} for how to implement one.\n * \\@stable\n */\nexport const NG_VALUE_ACCESSOR = new InjectionToken<ControlValueAccessor>('NgValueAccessor');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken, ɵisObservable as isObservable, ɵisPromise as isPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {forkJoin} from 'rxjs/observable/forkJoin';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {map} from 'rxjs/operator/map';\nimport {AsyncValidatorFn, ValidationErrors, Validator, ValidatorFn} from './directives/validators';\nimport {AbstractControl, FormControl} from './model';\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isEmptyInputValue(value: any): boolean {\n  // we don't check for string here so it also works with arrays\n  return value == null || value.length === 0;\n}\n/**\n * Providers for validators to be used for {\\@link FormControl}s in a form.\n * \n * Provide this using `multi: true` to add validators.\n * \n * \\@stable\n */\nexport const NG_VALIDATORS = new InjectionToken<Array<Validator|Function>>('NgValidators');\n/**\n * Providers for asynchronous validators to be used for {\\@link FormControl}s\n * in a form.\n * \n * Provide this using `multi: true` to add validators.\n * \n * See {\\@link NG_VALIDATORS} for more details.\n * \n * \\@stable\n */\nexport const NG_ASYNC_VALIDATORS =\n    new InjectionToken<Array<Validator|Function>>('NgAsyncValidators');\n\nconst /** @type {?} */ EMAIL_REGEXP =\n    /^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+(\\.[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/;\n/**\n * Provides a set of validators used by form controls.\n * \n * A validator is a function that processes a {\\@link FormControl} or collection of\n * controls and returns a map of errors. A null map means that validation has passed.\n * \n * ### Example\n * \n * ```typescript\n * var loginControl = new FormControl(\"\", Validators.required)\n * ```\n * \n * \\@stable\n */\nexport class Validators {\n/**\n * Validator that requires controls to have a value greater than a number.\n * @param {?} min\n * @return {?}\n */\nstatic min(min: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value) || isEmptyInputValue(min)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value = parseFloat(control.value);\n      // Controls with NaN values after parsing should be treated as not having a\n      // minimum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-min\n      return !isNaN(value) && value < min ? {'min': {'min': min, 'actual': control.value}} : null;\n    };\n  }\n/**\n * Validator that requires controls to have a value less than a number.\n * @param {?} max\n * @return {?}\n */\nstatic max(max: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value) || isEmptyInputValue(max)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value = parseFloat(control.value);\n      // Controls with NaN values after parsing should be treated as not having a\n      // maximum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-max\n      return !isNaN(value) && value > max ? {'max': {'max': max, 'actual': control.value}} : null;\n    };\n  }\n/**\n * Validator that requires controls to have a non-empty value.\n * @param {?} control\n * @return {?}\n */\nstatic required(control: AbstractControl): ValidationErrors|null {\n    return isEmptyInputValue(control.value) ? {'required': true} : null;\n  }\n/**\n * Validator that requires control value to be true.\n * @param {?} control\n * @return {?}\n */\nstatic requiredTrue(control: AbstractControl): ValidationErrors|null {\n    return control.value === true ? null : {'required': true};\n  }\n/**\n * Validator that performs email validation.\n * @param {?} control\n * @return {?}\n */\nstatic email(control: AbstractControl): ValidationErrors|null {\n    return EMAIL_REGEXP.test(control.value) ? null : {'email': true};\n  }\n/**\n * Validator that requires controls to have a value of a minimum length.\n * @param {?} minLength\n * @return {?}\n */\nstatic minLength(minLength: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ length: number = control.value ? control.value.length : 0;\n      return length < minLength ?\n          {'minlength': {'requiredLength': minLength, 'actualLength': length}} :\n          null;\n    };\n  }\n/**\n * Validator that requires controls to have a value of a maximum length.\n * @param {?} maxLength\n * @return {?}\n */\nstatic maxLength(maxLength: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      const /** @type {?} */ length: number = control.value ? control.value.length : 0;\n      return length > maxLength ?\n          {'maxlength': {'requiredLength': maxLength, 'actualLength': length}} :\n          null;\n    };\n  }\n/**\n * Validator that requires a control to match a regex to its value.\n * @param {?} pattern\n * @return {?}\n */\nstatic pattern(pattern: string|RegExp): ValidatorFn {\n    if (!pattern) return Validators.nullValidator;\n    let /** @type {?} */ regex: RegExp;\n    let /** @type {?} */ regexStr: string;\n    if (typeof pattern === 'string') {\n      regexStr = `^${pattern}$`;\n      regex = new RegExp(regexStr);\n    } else {\n      regexStr = pattern.toString();\n      regex = pattern;\n    }\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value: string = control.value;\n      return regex.test(value) ? null :\n                                 {'pattern': {'requiredPattern': regexStr, 'actualValue': value}};\n    };\n  }\n/**\n * No-op validator.\n * @param {?} c\n * @return {?}\n */\nstatic nullValidator(c: AbstractControl): ValidationErrors|null { return null; }\n\n  /**\n   * Compose multiple validators into a single function that returns the union\n   * of the individual error maps.\n   */\n  static compose(validators: null): null;\n  static compose(validators: (ValidatorFn|null|undefined)[]): ValidatorFn|null;\n/**\n * @param {?} validators\n * @return {?}\n */\nstatic compose(validators: (ValidatorFn|null|undefined)[]|null): ValidatorFn|null {\n    if (!validators) return null;\n    const /** @type {?} */ presentValidators: ValidatorFn[] = /** @type {?} */(( validators.filter(isPresent) as any));\n    if (presentValidators.length == 0) return null;\n\n    return function(control: AbstractControl) {\n      return _mergeErrors(_executeValidators(control, presentValidators));\n    };\n  }\n/**\n * @param {?} validators\n * @return {?}\n */\nstatic composeAsync(validators: (AsyncValidatorFn|null)[]): AsyncValidatorFn|null {\n    if (!validators) return null;\n    const /** @type {?} */ presentValidators: AsyncValidatorFn[] = /** @type {?} */(( validators.filter(isPresent) as any));\n    if (presentValidators.length == 0) return null;\n\n    return function(control: AbstractControl) {\n      const /** @type {?} */ observables = _executeAsyncValidators(control, presentValidators).map(toObservable);\n      return map.call(forkJoin(observables), _mergeErrors);\n    };\n  }\n}\n/**\n * @param {?} o\n * @return {?}\n */\nfunction isPresent(o: any): boolean {\n  return o != null;\n}\n/**\n * @param {?} r\n * @return {?}\n */\nexport function toObservable(r: any): Observable<any> {\n  const /** @type {?} */ obs = isPromise(r) ? fromPromise(r) : r;\n  if (!(isObservable(obs))) {\n    throw new Error(`Expected validator to return Promise or Observable.`);\n  }\n  return obs;\n}\n/**\n * @param {?} control\n * @param {?} validators\n * @return {?}\n */\nfunction _executeValidators(control: AbstractControl, validators: ValidatorFn[]): any[] {\n  return validators.map(v => v(control));\n}\n/**\n * @param {?} control\n * @param {?} validators\n * @return {?}\n */\nfunction _executeAsyncValidators(control: AbstractControl, validators: AsyncValidatorFn[]): any[] {\n  return validators.map(v => v(control));\n}\n/**\n * @param {?} arrayOfErrors\n * @return {?}\n */\nfunction _mergeErrors(arrayOfErrors: ValidationErrors[]): ValidationErrors|null {\n  const /** @type {?} */ res: {[key: string]: any} =\n      arrayOfErrors.reduce((res: ValidationErrors | null, errors: ValidationErrors | null) => {\n        return errors != null ? {... /** @type {?} */((res)), ...errors} : /** @type {?} */(( res));\n      }, {});\n  return Object.keys(res).length === 0 ? null : res;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {Form} from './form_interface';\n/**\n * A directive that contains multiple {\\@link NgControl}s.\n * \n * Only used by the forms module.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class ControlContainer extends AbstractControlDirective {\n  name: string;\n/**\n * Get the form to which this container belongs.\n * @return {?}\n */\nget formDirective(): Form|null { return null; }\n/**\n * Get the path to this container.\n * @return {?}\n */\nget path(): string[]|null { return null; }\n}\n\nfunction ControlContainer_tsickle_Closure_declarations() {\n/** @type {?} */\nControlContainer.prototype.name;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Observable} from 'rxjs/Observable';\nimport {AbstractControl} from '../model';\nimport {ValidationErrors} from './validators';\n/**\n * Base class for control directives.\n * \n * Only used internally in the forms module.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class AbstractControlDirective {\n/**\n * @abstract\n * @return {?}\n */\ncontrol() {}\n/**\n * @return {?}\n */\nget value(): any { return this.control ? this.control.value : null; }\n/**\n * @return {?}\n */\nget valid(): boolean|null { return this.control ? this.control.valid : null; }\n/**\n * @return {?}\n */\nget invalid(): boolean|null { return this.control ? this.control.invalid : null; }\n/**\n * @return {?}\n */\nget pending(): boolean|null { return this.control ? this.control.pending : null; }\n/**\n * @return {?}\n */\nget errors(): ValidationErrors|null { return this.control ? this.control.errors : null; }\n/**\n * @return {?}\n */\nget pristine(): boolean|null { return this.control ? this.control.pristine : null; }\n/**\n * @return {?}\n */\nget dirty(): boolean|null { return this.control ? this.control.dirty : null; }\n/**\n * @return {?}\n */\nget touched(): boolean|null { return this.control ? this.control.touched : null; }\n/**\n * @return {?}\n */\nget untouched(): boolean|null { return this.control ? this.control.untouched : null; }\n/**\n * @return {?}\n */\nget disabled(): boolean|null { return this.control ? this.control.disabled : null; }\n/**\n * @return {?}\n */\nget enabled(): boolean|null { return this.control ? this.control.enabled : null; }\n/**\n * @return {?}\n */\nget statusChanges(): Observable<any>|null {\n    return this.control ? this.control.statusChanges : null;\n  }\n/**\n * @return {?}\n */\nget valueChanges(): Observable<any>|null {\n    return this.control ? this.control.valueChanges : null;\n  }\n/**\n * @return {?}\n */\nget path(): string[]|null { return null; }\n/**\n * @param {?=} value\n * @return {?}\n */\nreset(value: any = undefined): void {\n    if (this.control) this.control.reset(value);\n  }\n/**\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\nhasError(errorCode: string, path?: string[]): boolean {\n    return this.control ? this.control.hasError(errorCode, path) : false;\n  }\n/**\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\ngetError(errorCode: string, path?: string[]): any {\n    return this.control ? this.control.getError(errorCode, path) : null;\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["NgModule", "Injectable", "Directive", "Input", "tslib_1.__extends", "forwardRef", "Optional", "Self", "Inject", "Host", "SkipSelf", "EventEmitter", "Examples", "ɵlooseIdentical", "<PERSON><PERSON><PERSON>", "ElementRef", "Injector", "ɵisPromise", "fromPromise"], "mappings": ";;;;;;AgCAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;AD5JP;;;;;;;;;;;;;;;AAyBA,IAAA,wBAAA,IAAA,YAAA;IAAA,SAAA,wBAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDA,MAAA,CAAA,cAAA,CAAI,wBAAJ,CAAA,SAAA,EAAA,SA7BW,EA6BX;;;;QAAA,GAAA,EAAA,YAAA,EA7BgB,OAAC,IA6BjB,CAAA,OAAA,GA7BgC,IA6BhC,CAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;YA1BG,OA+BH,IAAA,CA/BG,OA+BH,GAAA,IAAA,CAAA,OAAA,CAAA,aAAA,GAAA,IAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;;;YA5BG,OAiCH,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA,CAAA,YAAA,GAAA,IAAA,CAAA;;;;;IAKA,MAAA,CAAA,cAAA,CAAA,wBAAA,CAAA,SAAA,EAAA,MAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;ID1FA,wBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,SAAA,EAAA,IAAA,EAAA;;;;CCyBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;ADAA,IAAA,gBAAA,IAAA,UAAA,MAAA,EAAA;IAAAI,SAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAAA,SAAA,gBAAA,GAAA;;;IAKA,MAAA,CAAA,cAAA,CAAG,gBAAH,CAAA,SAAA,EAAA,eAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;;;;CALA,CAAA,wBAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KDgMA;;;;;;IAjJA,UAAA,CAAA,GAAA,GAAA,UAAA,GAAA,EAAA;;;gBAGA,OAAA,IAAA,CAAA;aACA;YACA,qBAAA,KAAA,GAAA,UAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;IAWA,UAAA,CAAA,GAAA,GAAA,UAAA,GAAA,EAAA;;;gBAGA,OAAA,IAAA,CAAA;aACA;YACA,qBAAA,KAAA,GAAA,UAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCA,UAAA,CAAA,SAAA,GAAA,UAAA,SAAA,EAAA;QACA,OAAA,UAAA,OAAA,EAAA;YACA,IAAU,iBARc,CAQxB,OAAA,CAAA,KAAA,CAAA,EAAA;gBACU,OAAV,IAAA,CAAA;aACA;YACA,qBAAA,MAAA,GAAA,OAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;;;;;;;;;;;IAWA,UAAA,CAAA,SAAc,GAAd,UATe,SASf,EAAA;QACA,OAAA,UAAA,OAAA,EAAA;YACA,qBAAA,MAAA,GAAA,OAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;;;;;;;;;;;IAUA,UAAA,CAAA,OAAA,GAAA,UAVe,OAAA,EAUf;QACA,IAAM,CAAN,OAAc;YACR,OAAN,UAAA,CAVkB,aAUlB,CAAA;QACA,qBAAA,KAAA,CAAA;QAVA,qBAAA,QAAA,CAAA;QAWA,IAAM,OAAN,OAAA,KAVyB,QAAC,EAAQ;YAW5B,QAVQ,GAUd,GAAA,GAVc,OAUd,GAAA,GAAA,CAAA;YACA,KAAA,GAAA,IAAA,MAAA,CAAA,QAAA,CAAA,CAAA;SACA;aACA;YACA,QAAA,GAVe,OAUf,CAAA,QAAA,EAAA,CAAA;YACA,KAAA,GAAA,OAAA,CAAA;SACA;QACA,OAAA,UAAA,OAAA,EAAA;YACA,IAAiC,iBAAjC,CAAA,OAAA,CAAA,KAAA,CAV+C,EAAkB;gBAWjE,OAAA,IAAA,CAAA;aACA;;;;;;;;;;;IAGG,UAAH,CAAA,aAeA,GAAA,UAAA,CAAA,EAfA,EAeA,OAAA,IAAA,CAAA,EAAA,CAfA;;;;;IAGA,UAAA,CAAA,OAAA,GAAA,UAA8C,UAA9C,EAAA;QAiBI,IAAJ,CAAA,UAAA;YACM,OAfO,IAeb,CAAA;QACA,qBAAA,iBAAA,IAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA;QACA,IAAA,iBAAA,CAAA,MAAA,IAAA,CAAA;;;;;KAKA,CAAA;;;;;IAfA,UAAA,CAAA,YAA8C,GAA9C,UAAmD,UAAnD,EAAA;QAoBI,IAAJ,CAAA,UAAA;YACM,OAAN,IAAA,CAAA;QACA,qBAAA,iBAlBgC,IAkBhC,UAAA,CAlB6C,MAkB7C,CAAA,SAAA,CAAA,CAAA,CAAA;QACA,IAAA,iBAAA,CAAA,MAAA,IAAA,CAAA;YACA,OAAA,IAAA,CAAA;QACA,OAAA,UAAA,OAAA,EAAA;;;;;IAKA,OAAA,UAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;CAOA;;;;;AAKA,SAAA,YAAA,CAAA,CAAA,EAAA;IACA,qBAAA,GAAA,GAAAa,wBAAA,CAAA,CAAA,CAAA,GAAAC,uCAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;CAqBA;;;;;AAMA,SAAA,YAAA,CAAA,aAAA,EAAA;;QDjQA,OAAA,MAAA,IAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,qBAAA,GAAA,IAAA,MAAA,CAAA,KAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADmCA,IAAA,4BAAA,IAAA,YAAA;;;;;IAFA,SAAA,4BAAA,CAAA,SAAA,EAAA,WAAA,EAAA;;;;;KAQA;;;;;;;KAOA,CAAA;;;;;IAKA,4BAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAdG,EAcH,EAAA,EAAA,IAAA,CAd0C,QAc1C,GAAA,EAd2D,CAc3D,EAd8D,CAc9D;;;;;IAKA,4BAAA,CAAA,SAAA,CAAA,iBAjBG,GAiBH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;IAIA,4BAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAjBQ,UAiBR,EAAA;QACA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA;KAEA,CAAA;IACA,OAAA,4BAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,4BAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAhB,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAfA,EAAA,EAAA;CAoBA,CAAA;;;;ADzEA,4BAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;CA+BA;;;;;;;;;;;;;;;;;;;;;;;IAcA,SAAA,oBAAA,CAAA,SAEc,EAFd,WAAA,EAAA,gBAAA,EAAA;;;;QAKG,IAAH,CAAA,QAAA,GAAA,UAAA,CAAG,EAAH,GAAA,CAAA;QASI,IAAI,CAAR,SAAA,GAAA,YAAA,GAAA,CAAA;;;;;;;;KAQA;;;;;;;;KAQA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAdG,EAcH,EAAA,EAAA,IAAA,CAd4C,QAc5C,GAAA,EAd6D,CAc7D,EAdgE,CAchE;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,iBAjBG,GAiBH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;KAOA,CAAA;;;;;;;;SAQA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA,EAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA,CAAA;;;;;IArBA,oBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,KAAA,EAAA;QA0BA,IAzBQ,CAyBR,UAzBmB,GAyBnB,KAAA,CAAA;QACA,IAAA,CAAA,gBAAA,IAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA;;;;;IAKA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAA,EAAA,CAAA;gBACA,QAAA,EAAA,8MAAA;;;;gBAIA,IAAA,EAAA;oBACA,SAAA,EAAA,mCAAA;oBACA,QAAA,EAAA,aAAA;oBACA,oBAAA,EAAA,qBAAA;;;;aAvBA,EAAA,EAAA;CA4BA,CAAA;;;;;IDjIA,EAAA,IAAA,EAAAY,sBAAA,GAAA;;;;;;;;;;;;;;;AAmBA,SAAA,kBAAA,CAAA,SAAA,EAAA;IACA,IAAA,EAAA,SAAA,GAAA,QAAA,EAAA;QACA,OAAA,UAAA,CAAA,EAAA,EAAA,OAAA,EAAA,SAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,uBAAA,CAAA,SAAA,EAAA;IACA,IAAA,EAAA,SAAA,GAAA,QAAA,EAAA;QACA,OAAA,UAAA,CAAA,EAAA,EAAA,OAAA,EAAA,SAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;SDjCA;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAA,mBAAA,IAAA,YAAA;;;;;IAFA,SAAA,mBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;;;;;KAQA;;;;;;;;;KASA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,iBAhBG,GAgBH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAhBQ,UAgBR,EAAA;QACA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA;KAEA,CAAA;IACA,OAAA,mBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,mBAAA,CAAI,UAAJ,GAAA;IACA,EAAA,IAAA,EAAAZ,uBAAA,EAAA,IAAA,EAAA,CAhBc;gBAiBd,QAAA,EAAA,iGAAA;gBACE,IAAF,EAAA;oBACA,UAAA,EAAA,+BAAA;oBACA,SAAA,EAAA,+BAAA;;;;aAdA,EAAA,EAAA;CAmBA,CAAA;;;;ADhFA,mBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;IAiCA,SAAA,SAAA,GAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EACM,SAAN,CADA,IAAA,IAAA,CAAA;;;;;QAMA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;;QAmBA,KAAA,CAAA,cAAA,GAAA,EAAA,CAAA;;;;QAtBG,KAWH,CAAA,mBAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDzBA,KAAA,EAAA,IAAA;CAAA,CAAA;;;;;;;;;;;;;;;KAcA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,QAAA,EAAA;QACA,KAAA,qBAAA,CAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA,EAAA;YACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,QAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,MATM,GASN,UATO,QASP,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;;;;;;;;;;;IAYA,oBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,WAAA,EAAA,QAAA,EAAA;;YAZA,OAAA,KAAA,CAAA;QAcA,OAAA,WAAA,CAAA,CAAA,CAAA,CAAA,OAAA,KAAA,QAAA,CAAA,QAAA,CAAA,OAAA;YACA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,KAAA,QAAA,CAAA,IAAA,CAAA;;;;;IAXA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CAmBA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuDA,SAAA,yBAAA,CAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,SAAA,EAAA;QACE,IAAF,CAAA,SAAW,GAhCG,SAAM,CAAG;QAQvB,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA;;;;QA2CA,IAzCG,CAyCH,SAAA,GAAA,YAAA,GAAA,CAAA;KACA;;;;;;;QAOA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,EA3C8C,IAAC,CAAI,CAAC;;;;;IAgDpD,yBAAA,CAAA,SAAA,CAAA,WA9CG,GA8CH,YAAA,EA9CG,IA8CH,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;KAQA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,EAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAMA;QALA,IAAA,CAAA,GAAA,GAAA,EAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,KAvDG,EAuDH,EAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAvD2D,CAAG,EAAC,CAuD/D;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,iBA1DG,GA0DH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;;QAtDA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA;KA6DA,CAAA;;;;IAzDA,yBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA;QA6DA,IAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,IAAA,KAAA,IAAA,CAAA,eAAA,EAAA;;;;YA1DA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,eAAA,CAAA;KA+DA,CAAA;;;;IAIA,yBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,YAAA;QAGA,MAAA,IAAA,KAAA,CAAA,iMAAA,CAAA,CAAA;KAEA,CAAA;IACA,OAAA,yBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,yBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAC,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aA5DA,EAAA,EAAA;CAiEA,CAAA;;;;AAIA,yBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IA/DA,EAAA,IAAA,EAAAY,sBAAA,GAAA;IAiEA,EAAA,IAAM,EAhEEC,wBAgER,GAAA;IACA,EAAA,IAAA,EAAA,oBAhE4B,GAgE5B;IACA,EAAA,IAAA,EAAAC,sBAhEY,GAgEZ;CACC,CAAD,EAAC,CAhEC;;ID5KF,MAAA,EAAA,CAAA,EAAA,IAAA,EAAAb,mBAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAA,kBAAA,IAAA,YAAA;;;;;IAFA,SAAA,kBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;;;;;KAQA;;;;;;;KAOA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,iBAfG,GAeH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;IAIA,kBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAfQ,UAeR,EAAA;QAPA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA;KAUA,CAAA;IACA,OAAA,kBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,kBAAA,CAAA,UAAa,GAAb;IACA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAA,EAAA,CAfc;gBAgBd,QAAA,EAAA,8FAAA;gBACE,IAAF,EAAA;oBACA,UAAA,EAAA,+BAAA;oBACA,SAAA,EAAA,+BAAA;;;;aAbA,EAAA,EAAA;CAkBA,CAAA;;;;AD9EA,kBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;AAyBA,SAAA,iBAAA,CAAA,EAL2B,EAK3B,KALkC,EAKlC;IACA,IAAA,EAAA,IAAA,IAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA,IAAA,0BAAA,IAAA,YAAA;;;;;;QAvBA,IAAA,CAAA,SAAA,GAAA,SAAA,CARG;;;;;QAcD,IAAF,CAAA,UAAA,GAViB,IAUjB,GAAA,EAV4B,CAU5B;;;;;;;;KAMA;IAKA,MAAA,CAAA,cAAA,CAAA,0BAAA,CAAA,SAAA,EAAA,aAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,EAAA,EAAA;;;;;SAYA;;;KAAA,CAAA,CAAA;;;;;IAKA,0BAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA;QACI,IAAJ,CAAA,KAAA,GAAA,KAAA,CAAA;QACI,qBAAJ,EAAA,GAAA,IAAA,CAlBoB,YAkBpB,CAAA,KAlBuD,CAkBvD,CAAA;QACA,IAAA,EAAA,IAAA,IAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,0BAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,EAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAKA;;;;;KAAA,CAAA;;;;;IAKA,0BAAA,CAAA,SAAA,CAAA,iBA5BG,GA4BH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;KAOA,CAAA;;;;;;;;;;;IAUA,0BAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,KAAA,EAAA;QACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;IA1BA,0BAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAAA,EAAA;QAqCA,qBAAA,EAAA,GAAA,UAAA,CAAA,WAAA,CAAA,CAAA;QACA,OAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,WAAA,CAAA;KAEA,CAAA;IACA,OAAA,0BAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,0BAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAlCA,EAAA,EAAA;CAuCA,CAAA;;;;AAIA,0BAtCkB,CAsClB,cAAA,GAAA,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAAY,sBAAA,GAAA;IAGA,EAAA,IAAA,EAAAC,wBAAA,GAAA;CAJA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;IA0DA,SAAA,cAAA,CAA0B,QAA1B,EAAoC,SAAS,EAA7C,OAAA,EAAA;QACA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;KAKA;IAIA,MAAA,CAAA,cAAA,CAAI,cAAJ,CAAA,SAAA,EAAA,SAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,KA9EiB,EA8EjB;YACA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;QAeA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,cAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;;YApFA,IAAA,CAAA,OAAA,CAAO,UAAP,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;YACQ,IAqFR,CAAA,OAAA,CAAA,UAAA,CAAA,IArF4B,CAqF5B,OAAA,CAAA,KArF+C,CAqF/C,CArFkD;SAsFlD;;;;;IAnFA,EAAA,IAAA,EAAAb,uBAAA,EAAA,IAAA,EAAC,CAAD,EAAC,QAAD,EAAA,QAAA,EAAA,EAAA,EAAA;CAwFA,CAAA;;;;AAnFO,cAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAwFA,EAAA,IAAA,EAAAa,wBAvFc,GAuFd;IACA,EAAA,IAAA,EAAAD,sBAvFY,GAuFZ;IACA,EAAA,IAAA,EAAA,0BAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAR,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA;CA1FA,CAAA,EAAA,CAAA;AD9NA,cAAA,CAAA,cAAA,GAAA;;;;;;;;;;;;;;;;;;;;;AAyBA,SAAA,mBAAA,CALsB,EAKtB,EAAA,KAAA,EAAA;IALA,IAA0C,EAA1C,IAAA,IAAA;QAMA,OAAA,EAAA,GAAA,KAAA,CALoB;IAMpB,IAAA,OAAA,KAAA,KAAA,QAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA,IAAA,kCAAA,IAAA,YAAA;;;;;;QAvBA,IAAA,CAAA,SAAA,GAAA,SAAA,CArBG;;;;;QA2BD,IAAF,CAAA,UAAA,GAvBiB,IAuBjB,GAAA,EAvB4B,CAuB5B;;;;;;;;KAMA;IAKA,MAAA,CAAA,cAAA,CAAA,kCAAA,CAAA,SAAA,EAAA,aAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,EAAA,EAAA;;;;;SAYA;;;KAAA,CAAA,CAAA;;;;;IAKA,kCAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAWA;QAVA,IAAM,CAAN,KAAA,GAAA,KAAA,CAAA;QACA,qBAAA,yBAAA,CAAA;QA/BA,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA;;YAiCA,qBAAA,KAAA,GAAA,KAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACQ,yBAAR,GAAA,UAAA,GAAA,EAAA,CAAA,EAAA,EAAA,GA/B6B,CAAyB,YA+BtD,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,QAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;SACA;;;;;KAKA,CAAA;;;;;IAKA,kCAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAa,EAAb,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAyBA;QAxBA,IAAA,CAAA,QAAA,GAAA,UAAA,CAAA,EAAA;YACA,qBAAA,QAlCgB,GAAA,EAkChB,CAlC2B;YAmC3B,IAAA,CAAA,CAAA,cAAA,CAlCoB,iBAkCpB,CAAA,EAAA;gBACA,qBAAA,OAAA,GAAA,CAAA,CAAA,eAAA,CAAA;gBACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;oBAhCA,qBAAA,GAAA,GAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBAmCA,qBAAA,GAlCc,GAkCd,KAAA,CAAA,eAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;oBACA,QAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;iBACA;aACA;iBACA;gBACA,qBAAA,OAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;gBACA,KAAW,qBAAX,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;oBACA,qBAAA,GAAA,GAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAAA,GAAA,CAAA,QAAA,EAAA;wBACA,qBAAA,GAAA,GAAA,KAAA,CAAA,eAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;wBAjCkB,QAkClB,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;qBACA;iBACA;;;;;KAKA,CAAA;;;;;IAKA,kCAAA,CAAA,SAAA,CAAA,iBAzCG,GAyCH,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;IAsBA,kCAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,KAAA,EAAA;QACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;IAxCA,kCAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,WAAA,EAAA;QAmDA,qBAAA,EAAA,GAAA,YAAA,CAAA,WAAA,CAAA,CAAA;QACA,OAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,GAAA,WAAA,CAAA;KAEA,CAAA;IACA,OAAA,kCAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,kCAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAP,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAhDA,EAAA,EAAA;CAqDA,CAAA;;;;AAIA,kCAAA,CApD8B,cAoD9B,GAAA,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAAY,sBAAA,GAAA;IAGA,EAAA,IAAA,EAAAC,wBAAA,GAAA;CAJA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;IAiEA,SAAA,sBAAA,CAAU,QAAV,EAAoB,SAAS,EAA7B,OAAA,EAAA;QACA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;QACA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;;KAKA;IAIA,MAAA,CAAA,cAAA,CAAI,sBAAJ,CAAA,SAAA,EAAA,SAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,KA9FiB,EA8FjB;YACA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,SAAA,EAAA,OAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,KAAA,EAAA;YAhGA,IAAA,IAAA,CAAA,OAAA,EAAA;gBAiGM,IAAI,CAhGC,MAgGX,GAAA,KAAA,CAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;QAoBA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,EAAA,UAAA,EAAA,QAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,sBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;;YAvGA,IAAA,CAAA,OAAA,CAAA,UAAA,CAA2C,MAA3C,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;YACQ,IAwGR,CAAA,OAAA,CAAA,UAAA,CAAA,IAxG4B,CAwG5B,OAAA,CAAA,KAxG+C,CAwG/C,CAxGkD;SAyGlD;;;;;IAtGA,EAAA,IAAA,EAAAb,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAC,EAAD,QAAA,EAAA,EAAA,EAAA;CA2GA,CAAA;;;;AAtGO,sBAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IA2GA,EAAA,IAAA,EAAAa,wBA1Gc,GA0Gd;IACA,EAAA,IAAA,EAAAD,sBA1GY,GA0GZ;IACA,EAAA,IAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAR,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA;CA7GA,CAAA,EAAA,CAAA;ADrPA,sBAAA,CAAA,cAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAA,YAAA,CAAA,OAAA,EAAA,GARsB,EAQtB;IACE,IAAF,CAAA,OAAA;QARA,WAAA,CAAA,GACO,EADP,0BAC+C,CAAC,CADhD;IACgD,IAAhD,CAAA,GAAA,CAAA,aAAA;;IAWE,OAAF,CAAA,SARO,GAAe,UAQtB,CAAA,OARwC,CAQxC,oBAAA,OAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,CAAA,CAAA,CAAA;IACA,OAAO,CARC,cAQR,GARS,UAA0B,CAAC,YAQpC,CAAA,oBAAA,OAAA,CAAA,cAAA,IAAA,GAAA,CAAA,cAAA,CAAA,CAAA,CAAA;IACA,EAAA,GAAA,CAAA,aAAA,GAAA,UAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;IACA;;IAPK,GAAL,CAAA,aAAA,GAAA,gBAAA,CAAA,UAAA,QAAA,EAAA;;QAGM,OAQN,CAAA,WAAA,EAAA,CAAA;QAEA,OAAA,CAAA,QAAA,CAAA,QAAA,EAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,CAAA,CAAA;KARA,CAAA,CAAA;;;;IAaA,OAAA,CAAQ,gBAAR,CAAA,UAAA,QAAA,EAAA,cAAA,EAAA;QARA;;QAWuB,GAAvB,CAAA,aAAA,GAAA,UAAA,CAAA,QAAA,CAR4C,CAQ5C;;QAGA,IAAA,cAAA;;KAGA,CAAA,CAAK;IACL,IAAI,EAAJ,GAAA,CARoB,aAQpB,GAAA,gBAAA,EAAA;QARA,OAAA,CACkB,wBADlB,CAAA,UAAA,UAAA,EAAA,mBAAA,EAAA,EAAA,GAAA,CAAA,aAAA,GAAA,gBAAA,GAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA;KAUG;;IAGH,GAAA,CAAI,cAAJ,CARqB,OAQrB,CAAA,UAAA,SAAA,EAAA;QARA,IAAyD,EASnD,SATN,GAAA,yBAAA;YAUA,EAAA,EAAA,SAAA,GAAA,yBAAA,GAAA,YAAA,EAAA,OAAA,OAAA,CAAA,sBAAA,EAAA,CAAA,EAAA,CAAA,CAAA;KACA,CAAA,CAAA;;;;;;;;;;;AAYA,SAAA,cAAA,CAAA,OAAA,EAAA,GAAA,EAAA;IACA,EAAA,GAAK,CAAL,aAAA,GAAA,gBAAA,CAAA,YAAA,EAAA,OAAA,eAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACA,EAZI,GAYJ,CAAA,aAAA,GAAA,iBAAA,CAAA,YAAA,EAAA,OAAA,eAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IAEE,GAAG,CAZC,cAYN,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;QACI,IAAI,SAZC,CAAS,yBAAC,EAA0B;YAavC,SAAS,CAZC,yBAAC,CAAyB,IAAC,CAAI,CAAC;SAa3C;KACF,CAZC,CAAC;IAcH,GAAF,CAAM,mBAAN,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;QAZe,IAAf,SAAA,CAAA,yBAAA,EAAA;YAaA,SAAA,CAAA,yBAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;AAWA,SAAA,kBAAA,CAAA,OAAA,EAAA,GAAA,EAAA;;;;;CAKA;;;;;;;;;;;;;AAXA,SAAA,WAAA,CAAA,GAAA,EAAwB,OAAxB,EAAA;IAwBA,qBAAA,UAvB+B,CAuB/B;IACA,IAAA,EAAA,GAAA,CAAA,IAAA,GAAA,MAAA,GAAA,CAAA,EAAA;QAvBA,UAAA,GAAA,SAAA,GAAA,EAAA,GAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,GAAA,GAAA,CAAA;KAwBA;SACA,IAAA,EAAA,GAAA,CAAA,IAAA,GAAA,CAAA,CAAA,EAAA;QACA,UAAA,GAAA,SAAA,GAAA,GAvB6B,CAuB7B,IAAA,GAAA,GAAA,CAAA;KACA;;;;;CAKA;;;;;;;CAOA;;;;;;;;;;;;;;AAnBA,SAAA,iBAAA,CAAA,OAAA,EAAA,SAAA,EAAA;IAkCE,IAAF,CAAA,OAAA,CAAA,cAAA,CAAA,OAjCoC,CAiCpC;QACA,OAAA,KAAA,CAAA;IAEA,qBAAA,MAAA,GAAA,OAAA,CAAA,OAAA,CAAA,CAAA;IACE,IAAF,MAAA,CAAA,aAAA,EAAA;QACA,OAAA,IAAA,CAAA;IACE,OAAF,CAAAI,6BAAA,CAAA,SAAA,EAAA,MAAA,CAAA,YAAA,CAAA,CAAA;CACA;AACA,IAAA,iBAAA,GAAA;IACE,4BAAF;IACA,kBAAA;;;;;CAKA,CAAA;;;;;;;;;;;;;AAcA,SAAA,mBAAA,CAAA,GAAA,EAAA,cAAA,EAAA;IACE,IAAF,CAAA,cAAA;QACI,OAAJ,IAAA,CAAA;IACA,qBAAA,eAAA,GAAA,SAAA,CAAA;IAEA,qBAAA,eAAA,GAAA,SAAA,CAAA;IAvCA,qBAAA,cAAqC,GAArC,SAAA,CAAA;IAwCA,cAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;QACA,IAAA,CAAA,CAAA,WAAA,KAAA,oBAAA,EAAA;YACM,eAAe,GAvCG,CAAA,CAAE;SAyCrB;aAvCM,IAAX,iBAAA,CAAA,CAAA,CAAA,EAAA;YAwCM,IAAI,eAAV;gBACQ,WAAW,CAvCC,GAAC,EAAI,iEAAiE,CAuC1F,CAAA;YACM,eAAN,GAvCuB,CAAE,CAuCzB;SACK;aACL;YAEA,IAAA,cAAA;gBAvCA,WAAA,CAAA,GAAA,EAAA,+DAAA,CAAA,CAAA;YAwCA,cAAA,GAAA,CAAA,CAAA;SAvCA;KAwCA,CAAA,CAAA;IAvCA,IAAuB,cAAvB;QAyCA,OAAa,cAAb,CAAA;IACE,IAAF,eAAA;QACA,OAAA,eAAA,CAAA;;QDzNA,OAAA,eAAA,CAAA;;;;;;;;;;;;;;;;AAuCA,IAAA,0BAAA,IAAA,UAAA,MAAA,EAAA;IAAAT,SAAA,CAAA,0BAAA,EAAA,MAAA,CAAA,CAAA;IAAA,SAAA,0BAAA,GAAA;;;;;;;;QAMA,EAAA,IAAA,CANG,aAMH,GAAA,YAAA,CAAA,IAAA,CAAA,CAAA;KACA,CAAA;;;;;;;;KAQA,CAAA;IAKA,MAAA,CAAA,cAAA,CANG,0BAMH,CAAA,SAAA,EAAA,SAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,EAAA,IAAA,CAAA,aANuD,GAMvD,YAAA,CANsE,IAMtE,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CANG,0BAMH,CAAA,SAAA,EAAA,MAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,CAAA,IANgD,EAMhD,IAAA,CANyD,OAMzD,CAN+D,CAAO,EAMtE;;;KAAA,CAAA,CAAA;;;;;;;;;;IASA,MAAA,CAAA,cAAA,CAAI,0BAAJ,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,iBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;SAMA;;;KAAA,CAAA,CAAA;;;;;;;CAxCA,CAAA,gBAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDSA,MAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,SAAA,EAAA,gBAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,GAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,OAAA,GAAA,KAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAWE,MAAF,CAAA,cAAA,CAAA,qBAAA,CAAA,SAAA,EAAA,gBAAA,EAAA;;;;QAAA,GAAA,EAAE,YAAF,EA5ByB,OA4BzB,IAAA,CAAA,GAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,OAAA,GAAA,KAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IACA,OAAA,qBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,IAAA,mBAAA,GAAA;IACE,sBA5BsB,EA4BxB,kBAAA;IACE,oBAAoB,EA5BE,gBAAA;IA6BxB,qBAAA,EAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;IAbA,EAAA,IAAA,EAAAF,uBAAA,EAAA,IAAA,EAAA,CAAC,EAAD,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,mBAAA,EAAA,EAAA,EAAA;CAgCA,CAAA;;;;;;;;;;;;;;;;;;IA6BA,SAAA,oBAAA,CAAE,EAAF,EAAA;QA3CA,OAAQ,MAAR,CAAA,IAAA,CAAA,IAAA,EA2CA,EAAA,CAAA,IA3CA,IAAA,CAAA;KA2CA;IACA,OAAA,oBAAA,CAAA;CAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,oBAAA,CAAA,UAAA,GAAA;;;;aAzCA,EAAA,EAAA;CA8CA,CAAA;;;;;;;;;;;;;;;;;;;;;AD7FA,IAEC,OAAA,GAAA,SAAA,CAAA;;;;;AAGD,IAGC,OAHD,GAAA,SAAA,CAAA;;;;;;;;;;;;AAYA,SAAA,KAAA,CAAA,OAAA,EAAA,IAAA,EAAA,SAAA,EAAA;IACE,IAAI,IAFC,IAEP,IAAA;QAFoD,OAAO,IAAA,CAAK;IAI9D,IAAF,EAAA,IAAA,YAFuC,KAA2B,CAElE,EAAA;QACI,IAAI,GAAR,EAAA,IAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;KACA;IACA,IAAA,IAAA,YAAA,KAAA,KAAA,IAAA,CAAA,MAAA,KAAA,CAAA,CAAA;QAEI,OAAJ,IAAA,CAAA;IACA,OAAA,EAAA,IAAA,GAFa,MAEb,CAAA,UAAA,CAAA,EAAA,IAAA,EAAA;QACA,IAAA,CAAA,YAAA,SAAA,EAAA;YAEA,OAFW,CAAK,CAEhB,QAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;SACA;QACA,IAAA,CAAA,YAAA,SAAA,EAAA;;;;;CAKA;;;;;;;CAOA;;;;;;;;;;;;;;;;;;;;;AAsCA,IAAA,eAAA,IAAA,YAAA;;;;;;QAbA,IAAA,CAAA,SAAA,GAAA,SAAA,CAZG;QAMO,IAAV,CAAA,cAAA,GAAoC,cAApC,CAAA;;;;;QAokBA,IAAA,CAAA,SAAA,GAAA,IAAA,CA7FG;QAleH,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA;;;;;KAoBA;IAKA,MAAA,CAAA,cAAA,CAfG,eAeH,CAAA,SAAA,EAAA,OAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAf6C,MAe7C,CAAA,EAf0D;;;KAA1D,CAAA,CAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6I1D,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,eAAA,GAAA,YAAA,EAjBG,IAiBH,CAjBiC,SAiBjC,GAAA,IAAA,CAAA,EAjBuC,CAiBvC;;;;;;;;;;;;;;IAeA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;;IAkBA,eAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;QACA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA;;;;;;;;;;;;;;IAeA,eAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;;IAiBA,eAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;QACA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;;;;;;;;;;;IAYA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;IAeA,eAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,IAAR,EAAA;QAAQ,IAAR,IAAA,KAAA,KAAA,CAAA,EAAQ,EAAA,IAAR,GAAA,EAAA,CAAA,EAAA;QACA,IAAM,CAAN,OAAA,GAAA,QAvByB,CAuBzB;QACA,IAAM,CAAN,OAAA,GAAA,IAAA,CAAA;QACA,IAAA,CAAA,aAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QAEI,IAAI,CAvBC,YAuBT,EAAA,CAAA;QACI,IAAI,IAAR,CAAA,SAAA,KAvB2B,KAuB3B,EAvB4B;YAwB5B,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;IAgBA,eAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;QACA,IAAA,CAAA,OAAA,GAAA,KAAA,CAAA;;;;;KAtBA,CAAA;;;;;IAgCA,eAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,QAAA,EAAA;QACA,IAAA,IAAA,CAAA,OAAA,IAAA,CAAA,QAAA,EAAA;;;;;KAKA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsCA,eAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAxCqB,IAwCrB,EAAA;QAxCqB,IAArB,IAAA,KAAA,KAAA,CAAA,EAAqB,EAAA,IAArB,GAAA,EAwCA,CAxCA,EAAA;QAyCA,IAAM,CAAN,iBAAA,EAxC0B,CAwC1B;QAEA,IAAM,CAAN,YAAA,EAAA,CAxCgB;QAyChB,IAAA,IAAQ,CAAR,OAAA,EAAA;YACA,IAAA,CAAA,2BAAA,EAAA,CAAA;YACA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,aAAA,EAAA,CAAA;YAEQ,IAxCC,CAAI,OAwCb,GAAA,IAxC2B,CAwC3B,gBAAA,EAAA,CAAA;YACM,IAAI,IAAV,CAAA,OAAA,KAAA,KAAA,IAAA,IAAA,CAxCoC,OAwCpC,KAAA,OAAA,EAAA;gBACU,IAAV,CAAA,kBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;aACA;SAEA;QACA,IAAM,IAAI,CAxCC,SAwCX,KAAA,KAAA,EAAA;YACA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;QAhCA,IAAA,CAAA,sBAAA,CAAA,EAAsC,QAAc,EAApD,IAAA,EAAA,SAAA,EAAA,IAAA,CAAA,SAA6E,EAA7E,CAAwF,CAAxF;;;;;IAkDA,eAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA,EAAA,IAAA,CAAA,OAAA,GAAA,IAAA,CA/CkC,oBA+ClC,EAAA,GAAA,QAAA,GAAA,KAAA,CAAA,EAAA,CAAA;;;;;;KA5CA,CAAA;;;;;IAuDA,eAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAOA;QANA,IAAA,IAAA,CAAA,cAAA,EAAA;YACA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;;;;SAhDA;KAqDA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6EA,eAAA,CAAA,SAAA,CAAA,QA3DG,GA2DH,UAAA,SAAA,EAAA,IAAA,EAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,SAAA,EAAA,IAAA,CAAA,CAAA,EAAA,CAAA;IAOA,MAAA,CAAA,cAAA,CAAI,eAAJ,CAAA,SAAA,EAAA,MAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA;YACA,qBAAA,CAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;;IAaA,eAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UA9DsB,SA8DtB,EAAA;QACA,IAAM,CAAN,OAAA,GAAA,IAAA,CAAA,gBAAA,EA9DoB,CAAqB;QA+DzC,IAAA,SAAA,EAAA;YACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;;;QA1DA,IAAA,CAAA,cAAA,GAAA,IAAAS,0BAAA,EAAA,CAAA;KAkEA,CAAA;;;;IAEA,eAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QAjEA,IAA8C,IAA9C,CAAA,oBAAA,EAAA;YAkEQ,OAAR,QAAA,CAAA;QAjEA,IAA8C,IAA9C,CAAA,OAAA;YAkEA,OAAA,OAAA,CAAA;QACA,IAAA,IAAA,CAAA,sBAAA,CAAA,OAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCA,CAAA;;;;;;;KAOA,CAAA;;;;;;;;;;;;;IAcA,eAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;;;;;;;;;;;IAYA,eAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;CA7iBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwpBA,SAAA,WAAA,CAAA,SAAA,EAAA,SAAA,EAAA,cAAA,EAAA;QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;QAAA,IAAA,KAAA,GAUI,MAVJ,CAAA,IAAA,CAAA,IAAA,EAUA,iBAAA,CAAA,SAAA,CAAA,EAAA,sBAAA,CAAA,cAAA,CAAA,CAAA,IAVA,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6CA,WAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAjJS,KAiJT,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACA,IAAA,CAAA,MAAA,GAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuDA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,SAAA,EAAA,OAAA,EAAA;QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;;;;;KAKA,CAAA;;;;;;;;;;;IAWA,WAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,SAAA,EAAA,EAAA,OAAA,KAAA,CAAA,EAAA,CAAA;;;;;;;;;;;IAWA,WAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;IArIG,WAAH,CAAA,SAAA,CAAA,aAAA,GAAG,UAAH,EAAG,EAAH,GAAA,CAAA;;;;;IAiKA,WAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,SAAA,EAAA;QA5JA,IAAA,IAAA,CAAA,aAAA,CAAA,SAAA,CAAA,EAAA;YA6JM,IAAI,CA5JC,MAAC,GAAQ,SAAA,CAAU,KA4J9B,CAAA;YACA,SAAA,CAAA,QAAA,GAAA,IAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA;gBACA,IAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,CAAA;SACA;aAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwEA,SAAA,SAAA,CAAA,QAAA,EAAA,SAA+B,EAA/B,cAAA,EAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,EACA,SAAA,IAAA,IAAA,EAAA,cAAA,IAAA,IAAA,CAAA,IADA,IAAA,CAAA;;;;;;;;;;;;;;;;IAeA,SAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,IAAA,EAAA,OAAA,EAAA;QACI,IAAJ,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;YACA,OAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;IAqBA,SAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;;;;;;;;;;;;IAtKA,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,IAAA,EAAA,OAAA,EAAA;QAiLI,IAAI,IAAR,CAAA,QAAA,CAAA,IAAA,CAAA;YACQ,IAAR,CAAA,QAAA,CAAA,IAAA,CAAA,CAhLU,2BAgLV,CAAA,YAAA,GAAA,CAAA,CAAA;QACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2CA,SAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,IAAI,CAnLC,sBAAC,CAAsB,KAmLhC,CAAA,CAnLiC;QAoLjC,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BA,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,MAAJ,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CArLU,UAAA,IAqLV,EAAA;YACA,IAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyCA,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAA,EAAA,CAAA,EAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,IAAI,CAvLC,aAuLT,CAvLU,UAAc,OAAC,EAAQ,IAuLjC,EAAA;YACA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAA,EAAA,OAAA,CAAA,SAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;IAcA,SAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;;;;;;;;;;;IAWA,SAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,MAAA,EAAA;YACQ,MA1LM,IA0Ld,KA1LuB,CA0LvB,wKAGA,CAAA,CAAA;;;;;;;;;;;;QAaA,IAAA,KAAA,GAAA,IAAA,CAAA;;KAAA,CAAA;;;;;IAKA,SAAA,CAAA,SAAA,CAAA,cAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAKA;;;;;KAAA,CAAA;;;;;;;;;;;IAWA,SAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAMA;QALA,qBAAA,GAAA,GAAA,KAAA,CAAA;;;;;KAKA,CAAA;;;;;IAMA,SAAA,CAAA,SAAA,CAAA,YAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,OAAA,IAAA,CAAA,eAAA,CAAA,EAAA,EAAA,UAAA,GAAA,EAAA,OAAA,EAAA,IAAA,EAAA;YACA,IAAA,OAAA,CAAA,OAAA,IAAA,KAAA,CAAA,QAAA,EAAA;;;;;;;;;;;;;;;;KAiBA,CAAA;;;;;IAKA,SAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,YAAA;QACI,KA5M2C,IA4M/C,EAAA,GAAA,CAAA,EA5M+C,EA4M/C,GA5M+C,MA4M/C,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EA5M+C,EA4M/C,GAAA,EAAA,CAAA,MAAA,EA5M+C,EA4M/C,EAAA,EAAA;YAAA,IAAA,WAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,IAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,OAAA,EAAA;;;;;;;;;;;IAWA,SAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,KAAA,EAAA;QACA,IAAA,CAAA,aAAA,CAAA,UAAA,OAAA,EAAA,IAAA,EAAA;YACA,IAAA,KAAA,CAAA,IAAA,CAAA,KAAA,SAAA,EAAA;gBAEA,MAAA,IAAA,KAAA,CAAA,mDAAA,GAAA,IAAA,GAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6DA,SAAA,SAAA,CAAA,QAAA,EAAA,SAA+B,EAA/B,cAAA,EAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,EACA,SAAA,IAAA,IAAA,EAAA,cAAA,IAAA,IAAA,CAAA,IADA,IAAA,CAAA;;;;;;;;;;;;;;;;;;IAkBA,SAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA,OAAA,EAAA;;;;;;;;;;;;IAaA,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;;;;;;;;;;;IAUA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UA9NS,KA8NT,EAAA;QACA,IAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA;;;;;;;;;;;;IAYA,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QACA,IAAM,IAAI,CAhOC,QAgOX,CAAA,KAAA,CAAA;YACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,2BAAA,CAAA,YAAA,GAAA,CAAA,CAAA;QAEI,IAAI,CAhOC,QAgOT,CAAA,MAAA,CAAA,KAAA,EAAA,CAhOgC,CAAE,CAgOlC;QACI,IAAI,OAAR,EAAA;YACA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,EAAA,OAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8BA,SAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,IAAI,CAlOC,sBAAC,CAAsB,KAkOhC,CAAA,CAlOiC;QAmOjC,KAAA,CAAA,OAAA,CAAA,UAAA,QAAA,EAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BA,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,KApOK,CAoOT,OAAA,CAAA,UAAA,QAAA,EAAA,KAAA,EAAA;YACA,IAAA,KAAA,CAAA,EAAA,CAAA,KAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwCA,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAA,EAAA,CAAA,EAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,EAAA,CAAA,EAAA;QACI,IAAI,CAtOC,aAsOT,CAtOU,UAAc,OAAC,EAAQ,KAsOjC,EAAA;YACA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAA,EAAA,OAAA,CAAA,SAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;IAuBA,SAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,KAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,EAAA;YACQ,MAzOM,IAyOd,KAzOuB,CAyOvB,wKAGA,CAAA,CAAA;;;;;;;;;;;;;KAaA,CAAA;;;;;;;;;;;;;;;;;KAgBA,CAAA;;;;;;;;;;;;;;IAaA,SAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,KAAA,EAAA;QACA,IAAA,CAAA,aAAA,CAAA,UAAA,OAAA,EAAA,CAAA,EAAA;;;;;KAKA,CAAA;;;;;IAIA,SAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,YAAA;QACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,IAAA,CAAA,QAAA,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,OAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;;KAtPA,CAAA;;;;;;QDj1CA,OAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,IAAA,MAAA,IAAA,UAAA,MAAA,EAAA;IAAAP,SAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;;;;;IAKA,SAAA,MAAA,CAAY,UAAU,EAAE,eAAxB,EAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAAA;;;;YAKA,IAAA,SAAA,CAAA,EAAA,EAAA,iBAJqD,CAIrD,UAAA,CAAA,EAAA,sBAAA,CAAA,eAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBA,MAAA,CAAA,cAAA,CAAA,MAAA,CAAA,SAAA,EAAA,UAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAS,GAAT,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAOA;QANA,eAAA,CAAA,IAAA,CAAA,YAAA;YACA,qBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,GArBG,EAqBH,EAAA,QAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,GAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAOA;QANA,eAAA,CAAA,IAAA,CAAA,YAAA;YACA,qBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,GAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAQA;QAPA,eAAA,CAAA,IAAA,CAAA,YAAA;YACA,qBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,qBAAA,KAAA,GAAA,IAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,GAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAOA;QANA,eAAA,CAAA,IAAA,CAAA,YAAA;YACA,qBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;;;;;;;IAWA,MAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAKA;;;;;KAAA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,QAxCG,GAwCH,UAxCG,KAwCH,EAAA,EAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;QAQA,OAAA,KA1CoB,CAAK;;;;;IA+CzB,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;IAnCA,MAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,IAAA,EAAA;QAiDA,IAhDQ,CAgDR,GAAA,EAAA,CAAA;QACA,OAAA,IAAA,CAAA,MAhDY,IAgDZ,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA;KACA,CAAA;IACA,OAAA,MAAA,CAAA;CAAA,CAxIA,gBAAA,CAwIA,CAAA,CAAA;AACA,MAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAF,uBAAA,EAAA,IAAA,EAAA,CAAA;gBACA,QAAA,EAAA,uDAAA;gBACA,SAAA,EAAA,CAAA,qBAAA,CAAA;;;;aA9CA,EAAA,EAAA;CAmDA,CAAA;;;;ADnNA,MAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;IAqDA,YAAA,EAAA,kJAAA;IAOA,oBAAA,EAAA,4LAAA;;;;;;;;;AD9CA,IAAA,oBAAA,IAAA,YAAA;IAAA,SAAA,oBAAA,GAAA;;;;;IAIA,oBAAA,CAAA,oBAAA,GAAA,YAAA;QAWA,MAAA,IAAA,KAAA,CAAA,8LAAA,GAAA,iBAAA,CAAA,eAAA,GAAA,kJAAA,GAAA,iBAAA,CAAA,oBAAA,CAAA,CAAA;KACA,CAAA;;;;;QAKQU,MAAR,IAAA,KAAA,CAAA,2MAAAA,GAKA,iBAAA,CAAA,aAAA,GALAA,oGAAAA,GASA,iBAAA,CAAA,YAAA,CAAA,CAAA;KACA,CAAA;;;;IAKA,oBAAA,CAAA,oBAAA,GAAA,YAAA;QACA,MAAA,IAAA,KAAA,CAAA,0UAIA,CAAA,CAAA;KACA,CAAA;;;;;QAKQA,MAAR,IAAA,KAAA,CAAA,oKAAAA,GAKA,iBAAA,CAAA,aAAA,GALAA,sHAAAA,GAAAA,iBAAAA,CAAAA,YAAAA,CAAAA,CAAAA;;;CA/CA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ID+CA,SAAA,YAAA,CAAA,MAAA,EAAA,UAAA,EAAA,eAAA,EAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAKA;;;;;KAAA;;;;;;QAAA,IAAA,EAAA,IAAA,CAAA,OAA2C,YAA3C,YAAA,CAAA,IAAA,EAAA,IAAA,CAAA,OAAA,YAAA,MAAA,CAAA,EAAA;YACQ,oBAAmB,CAK3B,yBALqC,EAAkB,CAKvD;SACA;;;;;IAHA,EAAA,IAAA,EAAAV,uBAAA,EAAA,IAAC,EAAD,CAAC,EAAD,QAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,EAAA,EAAA;CAQA,CAAA;;;;AAHO,YAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAQA,EAAA,IAAM,EAPE,gBAAgB,EAOxB,UAAA,EAAA,CAAA,EAAA,IAPgC,EAAcO,kBAAK,EAOnD,EAAA,EAAA,IAAA,EAAAC,sBAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAJ,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;;CDrFA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6GA,SAAA,OAAA,CAAA,MAAA,EAAG,UAAH,EAAA,eAAA,EAAA,cAAA,EAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAkCA;;;;QAtBG,KAAH,CAAA,QAAA,GAAA,IAAA,WAAA,EAF+B,CAE/B;;;;QAgBgB,KAAI,CAAC,WAArB,GAAA,KAAA,CAAA;QACA,KAAA,CAAA,MAAA,GAAA,IAAAG,0BAAA,EAAA,CAAA;;;;;;KAKA;;;;;IAIA,OAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAsB,OAAtB,EAAA;QACA,IAAA,CAAA,eAAA,EAAA,CAAA;QAEgB,IAAI,CAApB,IAAA,CAAA,WAVqB;YAWH,IAAI,CAVC,aAAa,EAUpC,CAAA;QACA,IAAkB,YAAlB,IAAA,OAAA,EAAA;YACA,IAAA,CAAA,eAAA,CAAA,OAAA,CAAA,CAAA;SACA;;;;SAIA;;;;;;IASA,MAAA,CAAA,cAAA,CAAgB,OAAhB,CAAA,SAAA,EAAA,SAhBuB,EAgBvB;;;;QAAA,GAAA,EAAA,YAAA,EAhB4B,OAAC,IAgB7B,CAAA,QAAA,CAhBsC,EAgBtC;;;KAAA,CAAA,CAAA;;;;;;YAbe,OAkBf,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,IAAA,CAAA,IAAA,EAlBoE,IAkBpE,CAAA,OAAA,CAAA,GAAA,CAAA,IAlB6E,CAkB7E,IAAA,CAAA,CAlB4F;;;;;;;;;;;;;IA2B5F,MAAA,CAAA,cAAA,CAAgB,OAAhB,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,iBAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;SAMA;;;KAAA,CAAA,CAAA;;;;;;;QApBA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;KA4BA,CAAA;;;;;;;QAtBA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA;KA8BA,CAAA;;;;;QA1BA,OAAA,CAAA,IAAe,CAAf,OAAA,IAAA,CAAA,EAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA;KAgCA,CAAA;;;;;;QA3BA,IAAA,CAAA,QAAA,CAAA,sBAAA,CAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,CAAA;KAkCA,CAAA;;;;IAIA,OAAA,CAAA,SAAA,CAAA,eAAA,GAAA,YAAA;;;;QA/BA,IAAA,CAAA,UAAA,EAAA,CAAA;KAoCA,CAAA;;;;IAhCA,OAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QAqCA,IAAkB,EAAlB,IAAA,CAAA,OAAA,YAAA,YAAA,CAAA;YACA,IAAA,CAAA,OAAA,YAAA,0BAAA,EAAA;YACA,oBAAA,CAAA,sBAAA,EAAA,CAAA;;;;SAjCA;KAsCA,CAAA;;;;IAIA,OAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA;QACA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA,IAAA;;;;;KAnCA,CAAA;;;;;;QAKA,IAAA,KAAA,GAAA,IAAA,CAAA;;KAAA,CAAA;;;;;IAmDA,OAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAwB,OAAxB,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CASA;QARA,qBAAA,aAAA,GAAA,OAAA,CAAA,YAAA,CAAA,CAAA,YAAA,CAAA;QA3CA,qBAAA,UAAiD,GAAjD,aAA0D,KAA1D,EAAA,KAAA,aAAA,IAAA,aAAA,KAAA,OAAA,CAAA,CAAA;QA4CA,iBAAA,CAAA,IAAA,CAAA,YAAA;YACA,IAAA,UAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,QAAA,EAAA;gBACA,KAAA,CAAA,OAAA,CAAA,OAAA,EAAA,CAAA;aACA;;gBA1CA,KAAA,CAA2C,OAA3C,CAAA,MAAA,EAAA,CAAA;aA4CA;SACA,CAAA,CAAA;KACA,CAAA;IACA,OAAA,OAAA,CAAA;CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAT,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAzCA,EAAA,EAAA;CA8CA,CAAA;;;;AAIA,OAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IA5CA,EAAA,IAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAI,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA;IA8CA,EAAA,IAAM,EA7CE,KA6CR,EA7CW,UA6CX,EAAA,CAAA,EAAA,IAAA,EAAAH,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EA7Cc,UA6Cd,EAAA,CA7CuB,EAAO,IAAA,EAAMF,sBA6CpC,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UA7CyB,EA6CzB,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,EAAA;CACA,CAAA,EAAA,CAAA;AACA,OAAA,CAAA,cAAA,GAAA;IACA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAAL,mBAAA,EAAA,EAAA;;IDnSA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,IAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA;;;;;;;;;;;AAgBA,IAAA,cAAA,IAAA,YAAA;IAAA,SAAA,cAAA,GAAA;;;;;;QAMQS,MAAR,IAAA,KAAA,CAAA,8NAAAA,GAKA,iBAAA,CAAA,eAAA,CAAA,CAAA;KACA,CAAA;;;;;QAMA,MAAA,IAAA,KAAA,CAAA,sRAAA,GAKA,iBAAA,CAAA,aAAA,GALA,qGAAA,GASA,iBAAA,CAAA,YAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,cAAA,CAAA,oBAAA,GAAA,YAAA;QACA,MAAA,IAAA,KAAA,CAAA,2FAAA,GAIA,iBAAA,CAAA,eAAA,CAAA,CAAA;KACA,CAAA;;;;;QAMQA,MAAR,IAAA,KAAA,CAAA,2NAAAA,GAKA,iBAAA,CAAA,aAAA,CAAA,CAAA;KACA,CAAA;;;;;QAMA,MAAA,IAAA,KAAA,CAAA,gOAAA,GAKA,iBAAA,CAAA,aAAA,CAAA,CAAA;KACA,CAAA;;;;;;;;CA9DA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDwEA,SAAA,oBAAA,CAAA,UAAkC,EAAlC,eAAA,EAAA,cAAA,EAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAbA;;;;;;KAAA;IAmBA,MAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,UAAA,EAAA,EAAA,cAAA,CAAA,mBAAA,EAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAkB,IAAI,CAPC,iBAOvB,CAAA,OAAA,CAAA,EAPmD;YAQnD,YAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;YACoB,IAApB,IAAA,CAAA,OAAA,CAPqB,QAAkB,IAOvC,EAP+C,IAO/C,CAAA,aAAA,GAAA,gBAAA,EAAA;gBACsB,EAAtB,EAAA,IAAA,CAAA,aAAA,GAP2C,gBAO3C,GAAA,IAAA,CAAA,CAAA;aACA;YACA,IAAA,CAAA,IAAA,CAAA,sBAAA,CAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,CAAA;SACA;;;;SAIA;;;;;;;;;;IASA,MAAA,CAAA,cAAA,CAAgB,oBAAhB,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,iBAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;YAVe,OAAA,sBAA6C,CAe5D,IAAA,CAAA,mBAAA,CAAA,CAAA;;;;;IAKA,MAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,SAAA,EAAA,SAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;KAbA,CAAA;;;;;IAyBA,oBAAA,CAAA,SAAA,CAAA,iBArBQ,GAqBR,UAAA,OArByB,EAqBzB;QACA,OAAA,OAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA;;;;;IAnBA,EAAA,IAAA,EAAAV,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA;CAwBA,CAAA;;;;AAnBO,oBAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAwBA,EAAA,IAAM,EAvBE,KAuBR,EAvBW,UAuBX,EAAA,CAvBwB,EAAM,IAuB9B,EAAAI,sBAvBgC,EAAa,EAAG,EAAE,IAuBlD,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAvByB,EAuBzB,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAQ,KAvBK,EAuBb,UAvBmB,EAAQ,CAuB3B,EAAA,IAvBiC,EAuBjCF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,EAAA;CACA,CAAA,EAAA,CAAA;AACA,oBAAA,CAAA,cAAA,GAAA;;ID5JA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAAL,mBAAA,EAAA,IAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,IAAA,kBAAA,IAAA,UAAA,MAAA,EAAA;IAAAC,SAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;;;;;IARA,SAAA,kBAAA,CAAA,WAAA,EACqC,gBADrC,EAAA;QAAA,IAAA,KAAA,GAEG,MAFH,CAAA,IAAA,CAAA,IAAA,CAEA,IAFA,IAAA,CAiBA;QAdG,KAAH,CAAA,WACc,GADd,WAAA,CAAA;QASA,KAAA,CAAA,gBAAA,GAAA,gBAAA,CAAA;;;;;;KAKA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAU,OAAV,EAAA;QACA,IAAA,CAAA,iBAAA,EAAA,CAAA;QACA,IAAA,OAAA,CAAA,cAAA,CAAA,MAAA,CAAA,EAAA;;;;SAIA;;;;;;;;;;;;;;;;;;;;;;;;;;IAiBA,MAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,SAAA,EAAA,MAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAhBW,GAgBX,EAAA;QACA,qBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,GAtBG,EAsBH,EAAA,QAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAtB2D,CAsB3D,IAtBsE,CAAI,EAsB1E,EAAA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,aAzBG,GAyBH,UAAA,GAAA,EAAA,EAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,GAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;;KASA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UA/BG,GA+BH,EAAA,GAAA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,YAlCG,GAkCH,UAlCG,GAAA,EAkCH,EAAA,QAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA;;;;;;;;;KASA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAxCG,GAwCH,EAAA,GAAA,CAAA;;;;;;;;;;;;;;KAcA,CAAA;;;;;;;;QAQA,OAAA,KAjDoB,CAAK;;;;;IAsDzB,kBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;KAQA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,eAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAWA;QAjEA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAqB,UAAA,GAArB,EAAA;YAuDA,qBAAA,OAAA,GAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,IAAA,GAAA,CAAA,QAAA,KAAA,OAAA,EAAA;gBACA,cAAA,CAAA,GAAA,CAAA,QAAA,EAAA,GAAA,CAAA,CAAA;gBAEA,IAAA,OAAA;oBACA,YAAA,CAAA,OAAA,EAAA,GAAA,CAAA,CAAA;;;;QApDA,IAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,CAAA;KAyDA,CAAA;;;;IAGA,kBAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAKA;;;;QA3DA,IAAA,CAAA,QAAG,GAAH,IAAA,CAAA,IAAA,CAAA;KA2DA,CAAA;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA;;;;QAxDA,IAAA,CAAA,IAAA,CAAA,cAAA,GAAA,UAAA,CAAA,YAAA,CAAA,oBAAA,IAAA,CAAA,IAAA,CAAA,cAAA,uBAAA,KAAA,GAAA,CAAA,CAAA;KA6DA,CAAA;;;;;QAxDA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;YACQ,cA4DR,CA5DmB,oBA4DnB,EAAA,CAAA;SACA;KACA,CAAA;IACA,OAAA,kBAAA,CAAA;CAAA,CAxKA,gBAAA,CAwKA,CAAA,CAAA;AACA,kBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAF,uBAAA,EAAA,IAAA,EAAA,CAAA;gBACA,QAAA,EAAA,aAAA;;;;aA1DA,EAAA,EAAA;CA+DA,CAAA;;;;AAIA,kBAAA,CA9DW,cA8DX,GAAA,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAA,KA9DY,EAAG,UA8Df,EA9DqB,CA8DrB,EAAA,IAAA,EAAAI,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;CAGA,CAAA,EAAA,CAAA;;;;;;;;;;;AAqCA,SAAA,MAAA,CAAA,IAAA,EAAA,EAAA,EAAA;;IDrSA,IAAA,KAAA,GAAA,CAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoFA,SAAA,aAAA,CAAA,MAAA,EAAA,UAAA,EAAA,eAAA,EAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAKA;;;;;KAAA;;;;;;QAAA,IAAA,iBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,EAAA;YACQ,cAKR,CALmB,oBAKnB,EAAA,CAAA;SACA;;;;;IAHA,EAAA,IAAA,EAAAN,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA;CAQA,CAAA;;;;AAHO,aAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAQA,EAAA,IAZE,EAKM,gBAAgB,EAOxB,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAI,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,sBAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAJ,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IAGA,EAnBa,IAmBb,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;CAgBA,CAAA,EAAA,CAAA;AACA,aAAA,CAAA,cAlC0B,GAkC1B;IACA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAAL,mBAAA,EAAA,IAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4EA,SAAA,aAAA,CAAA,MAAA,EAAA,UAAA,EAAA,eAAA,EAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAKA;;;QADA,KA9BG,CA8BH,gBAAA,GAAA,eAAA,CAAA;;KACA;;;;;;QAMA,EAAA,IAAA,CAhCG,aAgCH,GAAA,YAAA,CAAA,IAAA,CAAA,CAAA;KACA,CAAA;;;;;;;SAOA;;IAKA,MAAA,CAAA,cAAA,CAAI,aAAJ,CAAA,SAAA,EAAA,SApCW,EAoCX;;;;QAAA,GAAA,EAAA,YAAA,EApCgB,OAAC,EAoCjB,IAAA,CAAA,aAAA,GAAA,YAAA,CAAA,IAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;YAjCG,OAsCH,IAAA,CAAA,OAAA,IAAA,IAtCgC,CAAY,OAsC5C,CAAA,aAAA,IAtCqE,IAsCrE,CAAA;;;;;;;;;;;;;IASA,MAAA,CAAA,cAAA,CAAI,aAAJ,CAAA,SAAA,EAAA,WAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,iBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;YAvCA,OAAA,sBAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA;SA6CA;;;KAAA,CAAA,CAAA;;;;;QAxCA,IAAA,iBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,EAAA;YACQ,cA4CR,CA5CmB,oBA4CnB,EAAA,CAAA;SACA;;;;;IA1CA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA;CA+CA,CAAA;;;;AA1CO,aAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IA+CA,EAAA,IAAM,EA9CE,gBAAgB,EA8CxB,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAI,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,sBAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAJ,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IAGA,EAAA,IAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;CAnDA,CAAA,EAAA,CAAA;;;CAoFA,CAAA;;;;;AD7SA,SAAA,iBAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkHA,SAAA,eAAA,CAAA,MAAA,EAAA,UAAA,EAAA,eAAA,EAAA,cAAA,EAAA;QAAA,IAAA,KAAA,GACI,MADJ,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAfA;QAiBI,KAAI,CAAC,MAAT,GAAA,KAAA,CAAA;QACA,KAAA,CAAA,MAAA,GAAA,IAAAG,0BAAA,EAAA,CAAA;;;;;;KAlBA;IAuBA,MAAA,CAAA,cAAA,CAAA,eAAA,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,UAAA,EAAA,EAAA,cAAA,CAAA,mBAAA,EAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAIA,eAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAU,OAAV,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,MAAA;YACA,IAAA,CAAA,aAAA,EAAA,CAAA;;;;SAIA;KACA,CAAA;;;;;;;;KAQA,CAAA;;;;;;;QAdG,IAAA,CAqBH,MAAA,CAAA,IArBgC,CAqBhC,QAAA,CAAA,CArBgC;;;;;;;;;;;;;;;;;;IAkChC,MAAA,CAAA,cAAA,CAAI,eAAJ,CAAA,SAAA,EAAA,WA3BW,EA2BX;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,iBAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CA3B8D,EA2B9D;;;KAAA,CAAA,CAAA;;;;;;YAxBG,SA6BH,sBAAA,CA7B4C,IA6B5C,CAAA,mBAAA,CAAA,GAAA;;;;;IAKA,MAAA,CAAA,cAAA,CAAI,eAAJ,CAAA,SAAA,EAAA,SAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,QAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;IA5BA,eAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QAiCA,IAAQ,EA/BE,IAAC,CAAI,OAAC,YAAkB,aAAA,CAAc;YAgC1C,IAAN,CAAA,OAAA,YAAA,0BAAA,EAAA;YACA,cAAA,CAAA,qBAAA,EAAA,CAAA;SACA;;;;SA7BA;KAkCA,CAAA;;;;IAIA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,YAAA;QACI,IAAI,CAjCC,gBAiCT,EAAA,CAAA;QACA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,aAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;YAhCA,EAAA,EAAA,IAAA,CAAA,aAAA,GAAA,gBAAA,GAAA,IAAA,CAAA,CAAA;SAkCA;QACA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA;;;;;IA/BA,EAAA,IAAA,EAAAT,uBAAA,EAAA,IAAA,EAAA,CAAC,EAAD,QAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA;CAoCA,CAAA;;;;AAIA,eAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IAlCA,EAAA,IAAA,EAAA,gBAAA,EAAO,UAAP,EAAA,CAAA,EAAA,IAAA,EAAAI,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAG,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,sBAAA,EAAA,EAAA,EAAA;IAoCA,EAAA,IAAM,EAnCE,KAmCR,EAnCW,UAmCX,EAAA,CAnCwB,EAAM,IAmC9B,EAAAJ,sBAAA,EAAA,EAnCgC,EAAiB,IAAK,EAmCtDC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,KAAA,EAAA,UAnCyB,EAmCzB,CAAA,EAAA,IAAA,EAAAF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAQ,KAnCK,EAmCb,UAnCmB,EAAQ,CAmC3B,EAAA,IAnCiC,EAmCjCF,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,oBAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,EAAA;CACA,CAAA,EAAA,CAAA;AACA,eAAA,CAAA,cAAA,GAAA;;IDhNA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAAL,mBAAA,EAAA,IAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA;;;;;;;;;;;AAsDA,IADa,kBACb,GAAA;IACE,OAAO,EADE,aAAA;IAET,WAAO,EADME,wBAAA,CAAW,YAC1B,EADgC,OAAA,iBAChC,CAAA,EAAA,CAAA;IACE,KAAA,EADO,IAAA;CAER,CADC;;;;;;;;;;;;;;;;;;;;KA8CF;IArBA,MAAA,CAAA,cAAA,CAFG,iBAEH,CAAA,SAAA,EAAA,UAFG,EAEH;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,EAAA;;;;;;;;;SAQA;;;KARA,CAAA,CAAA;;;;;;;KAeA,CAAA;;;;;IAKA,iBAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;IACA,OAAA,iBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,iBARK,CAQL,UAAA,GAAA;IACA,EAAA,IAAA,EAAAH,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aANA,EAAA,EAAA;CAEA,CAAA;;;;;;;;;;;;;;;;;;;;AAgDA,IAAA,yBAAA,IAAA,UAAA,MAAA,EAAA;IAAAE,SAAA,CAAA,yBAAA,EAAA,MAAA,CAAA,CAAA;IAAA,SAAA,yBAAA,GAAA;;KAQA;;;;;IAJA,yBAAA,CAAA,SAAA,CAAA,QA5BQ,GA4BR,UAAA,CAAA,EAAA;QACA,OAAA,IAAA,CAAA,QAAA,GAAA,UAAA,CAAA,YAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;KAEA,CAAA;IACA,OAAA,yBAAA,CAAA;CAAA,CARA,iBAAA,CAQA,CAAA,CAAA;AACA,yBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAF,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aA1BA,EAAA,EAAA;CAkCA,CAAA;;;;AAcA,yBAAA,CAAwB,cAAxB,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAA,cAAA,IAAA,YAAA;IAAA,SAAA,cAAA,GAAA;KAoBA;;;;;;;;;;SAZA;;;KAAA,CAAA,CAAA;;;;;;;KAOA,CAAA;;;;;IAIA,cAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA,EAAA,CAAA;IACA,OAAA,cAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,cAAA,CAAA,UAAA,GAAA;;;;aA5CA,EAAA,EAAA;CAEA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA,IAAA,kBAAA,IAAA,YAAA;IAAA,SAAA,kBAAA,GAAA;KA4BA;;;;;IAxBA,kBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAA,WAAA,IAAA,OAAA,EAAA;;;;;KAKA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,UAAA,EAAA,EAAA,EAzEsB,IAyEtB,CAAA,SAAA,GAAA,EAzE4C,CAAQ,EAyEpD,CAAA;;;;IAGA,kBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QACA,IAAA,CAAA,UAAA,GAzEY,UAyEZ,CAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA,CAAA;KACA,CAAA;IACA,OAAA,kBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,kBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAvEA,EAAA,EAAA;CAEA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA,IAAA,kBAAA,IAAA,YAAA;IAAA,SAAA,kBAAA,GAAA;KA4BA;;;;;IAxBA,kBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAA,WAAA,IAAA,OAAA,EAAA;;;;;KAKA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,UAAA,EAAA,EAAA,EAtGsB,IAsGtB,CAAA,SAAA,GAAA,EAtG4C,CAAQ,EAsGpD,CAAA;;;;IAGA,kBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QACA,IAAA,CAAA,UAAA,GAtGY,UAsGZ,CAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA,CAAA;KACA,CAAA;IACA,OAAA,kBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,kBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aApGA,EAAA,EAAA;CAEA,CAAA;;;;AAkIA,kBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;AACA,kBAAA,CAAA,cAAA,GAAA;IACE,WArKO,EAqKT,CAAA,EAAA,IAAA,EAAAC,mBAAA,EAAA,EAAA;CACC,CArKC;;;;;;;;;;;;;;;;;;;AA6LF,IAAA,gBAAA,IAAA,YAAA;IAAA,SAAA,gBAAA,GAAA;KAwBA;;;;;IApBA,gBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAA,SAAA,IAAA,OAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,gBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,CAAA,EAAA,EAAA,OAAA,IAjIG,CAAA,UAAiD,CAAK,CAiIzD,CAAA,CAAA,EAAA,CAAA;;;;;;;;;IAQA,gBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA,EAAW,IAAX,CAAA,UAAA,GAAA,UAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA;IACA,OAAA,gBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,gBAAA,CAnIK,UAmIL,GAAA;IACA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAjIA,EAAA,EAAA;CAEA,CAAA;;;;ADlXA,gBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgDA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,cAAA,EAAA,KAAA,EAAA;QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAA,IAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0CA,WAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,cAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,QAAA,GAAA,EAAA,CAAA;;;;;;;;;;;IAJA,WAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,aAAA,EAAA;QAUA,IAAE,aAAF,YAAA,WAAA,IAAA,aAAA,YAAA,SAAA;YAOM,aAAN,YAAA,SAAA,EAAA;YACM,OAAN,aAAA,CAAA;SACA;aAEA,IAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,EAAA;YAfA,qBAAA,KAAA,GAAA,aAAA,CAAA,CAAA,CAAA,CAAA;YAgBM,qBAAN,SAAA,GAfyC,aAezC,CAAA,MAAA,GAAA,CAAA,GAAA,aAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;YACA,qBAAA,cAAA,GAAA,aAAA,CAAA,MAAA,GAAA,CAAA,GAAA,aAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;YACA,OAAA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,SAAA,EAAA,cAAA,CAAA,CAAA;;aAdA;YACQ,OAeR,IAAA,CAfmB,OAenB,CAAA,aAAA,CAAA,CAAA;SACA;;;;;IAbA,EAAA,IAAA,EAAAD,wBAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD1EA,IAAA,YAAA,IAAA,YAAA;IAAA,SAAA,YAAA,GAAA;KACA;IAAA,OAAA,YAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,YAAA,CAAA,UAAA,GAAA;;;;aAGA,EAAA,EAAA;;;;;;;;;;;;;ADoBA,IAAA,sBAAA,GAAA;IACE,YAAF;IACE,cAAF;IACE,sBAAF;IACE,oBAAF;IACE,mBAAF;IACE,kBAAF;IACE,4BAAF;IACE,0BAAF;IACE,kCAAF;IACE,yBAAF;IACE,eAAF;IACE,oBAAF;IACE,iBAAF;IACA,kBAAA;IAEA,kBAAA;IAEA,gBAAA;;;;AAKA,IAAA,0BAAA,GAAA,CAAA,OAAA,EAAA,YAAA,EAAA,MAAA,CAAA,CAAA;;;;;AAIA,IAAA,yBAAA,IAAA,YAAA;IAAA,SAAA,yBAAA,GAAA;KACA;IAAA,OAAA,yBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,yBAAA,CAAA,UAAA,GAAA;;;;aAGA,EAAA,EAAA;;;;;;;;;;;;;;;;;AD9DA,IAAA,WAAA,IAAA,YAAA;IAAA,SAAA,WAAA,GAAA;KACA;IAAA,OAAA,WAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,WAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAD,sBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAIA,EAAA,EAAA;CAIA,CAAA;;;;AAcA,WAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;;;;;AAIA,IAAA,mBAAA,IAAA,YAAA;IAAA,SAAA,mBAAA,GAAA;KACA;IAAA,OAAA,mBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,mBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAA,sBAAA,EAAA,IAAA,EAAA,CAAA;;;;aAPA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}