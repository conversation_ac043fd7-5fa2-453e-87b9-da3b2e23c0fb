# 1.1.2

* Improved performance of `isKeyword` (thanks to @maxnordlund & @ljharb).

# 1.1.1

* Bump css-color-names to 0.0.4.

# 1.1.0

* Add a legacy option for old Internet Explorer versions.

# 1.0.7

* Update color to 0.11.0.

# 1.0.6

* Now passes through invalid colour functions.

# 1.0.5

* Performance tweaks.
* Update css-color-names to 0.0.3.

# 1.0.4

* Fixes an issue with the last patch - module was not working correctly on
  Node 0.10.

# 1.0.3

* Updated to ES6.

# 1.0.2

* Improved regex for finding leading zeroes.

# 1.0.1

* Update color to 0.8.0.

# 1.0.0

* Initial release.
