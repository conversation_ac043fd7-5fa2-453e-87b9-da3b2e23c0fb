[{"__symbolic": "module", "version": 3, "metadata": {"QUOTED_KEYS": "$quoted$", "convertValueToOutputAst": {"__symbolic": "function", "parameters": ["ctx", "value", "type"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 20, "character": 0, "context": {"className": "_ValueOutputAstTransformer"}}, "defaults": [null, null, null]}}}, {"__symbolic": "module", "version": 1, "metadata": {"QUOTED_KEYS": "$quoted$", "convertValueToOutputAst": {"__symbolic": "function", "parameters": ["ctx", "value", "type"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 20, "character": 0, "context": {"className": "_ValueOutputAstTransformer"}}, "defaults": [null, null, null]}}}]