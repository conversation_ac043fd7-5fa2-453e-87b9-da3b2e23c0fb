[{"__symbolic": "module", "version": 3, "metadata": {"TranslateLoader": {"__symbolic": "class", "members": {"getTranslation": [{"__symbolic": "method"}]}}, "TranslateFakeLoader": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Translate<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"getTranslation": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslateLoader": {"__symbolic": "class", "members": {"getTranslation": [{"__symbolic": "method"}]}}, "TranslateFakeLoader": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Translate<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"getTranslation": [{"__symbolic": "method"}]}}}}]