import { LocationStrategy } from './location_strategy';
/** @experimental */
export interface PopStateEvent {
    pop?: boolean;
    type?: string;
    url?: string;
}
/**
 * @whatItDoes `Location` is a service that applications can use to interact with a browser's URL.
 * @description
 * Depending on which {@link LocationStrategy} is used, `Location` will either persist
 * to the URL's path or the URL's hash segment.
 *
 * Note: it's better to use {@link Router#navigate} service to trigger route changes. Use
 * `Location` only if you need to interact with or create normalized URLs outside of
 * routing.
 *
 * `Location` is responsible for normalizing the URL against the application's base href.
 * A normalized URL is absolute from the URL host, includes the application's base href, and has no
 * trailing slash:
 * - `/my/app/user/123` is normalized
 * - `my/app/user/123` **is not** normalized
 * - `/my/app/user/123/` **is not** normalized
 *
 * ### Example
 * {@example common/location/ts/path_location_component.ts region='LocationComponent'}
 * @stable
 */
export declare class Location {
    constructor(platformStrategy: LocationStrategy);
    /**
     * Returns the normalized URL path.
     */
    path(includeHash?: boolean): string;
    /**
     * Normalizes the given path and compares to the current normalized path.
     */
    isCurrentPathEqualTo(path: string, query?: string): boolean;
    /**
     * Given a string representing a URL, returns the normalized URL path without leading or
     * trailing slashes.
     */
    normalize(url: string): string;
    /**
     * Given a string representing a URL, returns the platform-specific external URL path.
     * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one
     * before normalizing. This method will also add a hash if `HashLocationStrategy` is
     * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.
     */
    prepareExternalUrl(url: string): string;
    /**
     * Changes the browsers URL to the normalized version of the given URL, and pushes a
     * new item onto the platform's history.
     */
    go(path: string, query?: string): void;
    /**
     * Changes the browsers URL to the normalized version of the given URL, and replaces
     * the top item on the platform's history stack.
     */
    replaceState(path: string, query?: string): void;
    /**
     * Navigates forward in the platform's history.
     */
    forward(): void;
    /**
     * Navigates back in the platform's history.
     */
    back(): void;
    /**
     * Subscribe to the platform's `popState` events.
     */
    subscribe(onNext: (value: PopStateEvent) => void, onThrow?: ((exception: any) => void) | null, onReturn?: (() => void) | null): Object;
    /**
     * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as
     * is.
     */
    static normalizeQueryParams(params: string): string;
    /**
     * Given 2 parts of a url, join them with a slash if needed.
     */
    static joinWithSlash(start: string, end: string): string;
    /**
     * If url has a trailing slash, remove it, otherwise return url as is. This
     * method looks for the first occurence of either #, ?, or the end of the
     * line as `/` characters after any of these should not be replaced.
     */
    static stripTrailingSlash(url: string): string;
}
