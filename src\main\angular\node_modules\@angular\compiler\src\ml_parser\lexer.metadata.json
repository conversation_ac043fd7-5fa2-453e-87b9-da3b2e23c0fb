[{"__symbolic": "module", "version": 3, "metadata": {"TokenType": {"TAG_OPEN_START": 0, "TAG_OPEN_END": 1, "TAG_OPEN_END_VOID": 2, "TAG_CLOSE": 3, "TEXT": 4, "ESCAPABLE_RAW_TEXT": 5, "RAW_TEXT": 6, "COMMENT_START": 7, "COMMENT_END": 8, "CDATA_START": 9, "CDATA_END": 10, "ATTR_NAME": 11, "ATTR_VALUE": 12, "DOC_TYPE": 13, "EXPANSION_FORM_START": 14, "EXPANSION_CASE_VALUE": 15, "EXPANSION_CASE_EXP_START": 16, "EXPANSION_CASE_EXP_END": 17, "EXPANSION_FORM_END": 18, "EOF": 19}, "Token": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 27, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "TokenError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 42, "character": 50, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "TokenizeResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Token"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "TokenError"}]}]}]}}, "tokenize": {"__symbolic": "function", "parameters": ["source", "url", "getTagDefinition", "tokenizeExpansionForms", "interpolationConfig"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 77, "character": 0, "context": {"className": "_Tokenizer"}}, "defaults": [null, null, null, false, {"__symbolic": "reference", "module": "./interpolation_config", "name": "DEFAULT_INTERPOLATION_CONFIG"}]}}}, {"__symbolic": "module", "version": 1, "metadata": {"TokenType": {"TAG_OPEN_START": 0, "TAG_OPEN_END": 1, "TAG_OPEN_END_VOID": 2, "TAG_CLOSE": 3, "TEXT": 4, "ESCAPABLE_RAW_TEXT": 5, "RAW_TEXT": 6, "COMMENT_START": 7, "COMMENT_END": 8, "CDATA_START": 9, "CDATA_END": 10, "ATTR_NAME": 11, "ATTR_VALUE": 12, "DOC_TYPE": 13, "EXPANSION_FORM_START": 14, "EXPANSION_CASE_VALUE": 15, "EXPANSION_CASE_EXP_START": 16, "EXPANSION_CASE_EXP_END": 17, "EXPANSION_FORM_END": 18, "EOF": 19}, "Token": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 27, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "TokenError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 42, "character": 50, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "TokenizeResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Token"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "TokenError"}]}]}]}}, "tokenize": {"__symbolic": "function", "parameters": ["source", "url", "getTagDefinition", "tokenizeExpansionForms", "interpolationConfig"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 77, "character": 0, "context": {"className": "_Tokenizer"}}, "defaults": [null, null, null, false, {"__symbolic": "reference", "module": "./interpolation_config", "name": "DEFAULT_INTERPOLATION_CONFIG"}]}}}]