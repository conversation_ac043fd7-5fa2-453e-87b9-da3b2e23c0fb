[{"__symbolic": "module", "version": 3, "metadata": {"TreeError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}, "statics": {"create": {"__symbolic": "function", "parameters": ["elementName", "span", "msg"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "TreeError"}, "arguments": [{"__symbolic": "reference", "name": "elementName"}, {"__symbolic": "reference", "name": "span"}, {"__symbolic": "reference", "name": "msg"}]}}}}, "ParseTreeResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "Node"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "Parser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 30, "character": 39}]}], "parse": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TreeError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}, "statics": {"create": {"__symbolic": "function", "parameters": ["elementName", "span", "msg"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "TreeError"}, "arguments": [{"__symbolic": "reference", "name": "elementName"}, {"__symbolic": "reference", "name": "span"}, {"__symbolic": "reference", "name": "msg"}]}}}}, "ParseTreeResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "Node"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "Parser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 30, "character": 39}]}], "parse": [{"__symbolic": "method"}]}}}}]