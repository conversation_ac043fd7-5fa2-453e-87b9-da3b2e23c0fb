{"version": 3, "file": "generate.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/generate.ts"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,yBAAyB;AACzB,yBAAyB;AACzB,6BAA6B;AAC7B,6CAAsC;AACtC,6CAA6C;AAE7C,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,MAAM,SAAS,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACzE,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE5C;IACE,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;IAC/E,MAAM,UAAU,GAAG,aAAa;SAC7B,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SACxC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;SACzB,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAE3E,MAAM,CAAC,UAAU,CAAC;AACpB,CAAC;AAED,kBAAe,OAAO,CAAC,MAAM,CAAC;IAC5B,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,uDAAuD;IACpE,OAAO,EAAE,CAAC,GAAG,CAAC;IAEd,gBAAgB,EAAE;QAChB;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,yCAAyC;SACvD;QACD;YACE,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,yCAAyC;SACvD;QACD;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,sCAAsC;SACpD;KACF;IAED,gBAAgB,EAAE;QAChB,aAAa;KACd;IAED,SAAS,EAAE,UAAU,OAAiB;QACpC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,MAAM,CAAC;QACT,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,cAAc,EAAE,CAAC;QAEnC,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAO,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI;eAC/D,CAAC,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhD,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACf,WAAW,CAAC,YAAY,CAAC,gCAAgC,EACvD,sBAAsB,IAAI,EAAE,CAAC,CAAC;QAClC,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,WAAW,CAAC,YAAY,CAAC,gCAAgC,EACvD,qBAAqB,IAAI,6CAA6C,CAAC,CAAC;QAC5E,CAAC;QAED,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,WAAW,CAAC,YAAY,CAAC,gCAAgC,EACrD,qBAAqB,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC;QACxF,CAAC;QAED,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,iBAAiB,EAAE;QACjB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,cAAc,EAAE,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAO,KAAK,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7F,CAAC;IAED,GAAG,EAAE,UAAU,cAAmB,EAAE,OAAiB;QACnD,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,qBAAO,CAAA;;;;OAI5C,CAAC,CAAC,CAAC;QACN,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAO,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI;eAC/D,CAAC,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhD,MAAM,WAAW,GAAG,kBAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACvD,MAAM,gBAAgB,mBACpB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EACzB,MAAM,EAAE;gBACN,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;gBAChB,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACxC,EACD,WAAW,EACX,EAAE,EAAE,IAAI,CAAC,EAAE,EACX,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,OAAO,IACV,cAAc,CAClB,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC;aACvC,IAAI,CAAC;YACJ,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,KAAK,SAAS;gBAClD,cAAc,CAAC,OAAO,GAAG,kBAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAElE,EAAE,CAAC,CAAC,OAAO,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC;gBAClD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC;oBAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAC;gBAEH,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAClB,GAAG,EAAE,IAAI;oBACT,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,CAAC;4BACR,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAY,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAC3E,CAAC;iBACH,CAAC,CAAC;YACP,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;CACF,CAAC,CAAC"}