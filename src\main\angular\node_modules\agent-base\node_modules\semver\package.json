{"_args": [["semver@5.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "semver@5.0.3", "_id": "semver@5.0.3", "_inBundle": false, "_integrity": "sha1-d0Zt5YnNXTyV8TiqeLxWmjy10no=", "_location": "/agent-base/semver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "semver@5.0.3", "name": "semver", "escapedName": "semver", "rawSpec": "5.0.3", "saveSpec": null, "fetchSpec": "5.0.3"}, "_requiredBy": ["/agent-base"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/semver/-/semver-5.0.3.tgz", "_spec": "5.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bin": {"semver": "bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^1.3.4"}, "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"test": "tap test/*.js"}, "version": "5.0.3"}