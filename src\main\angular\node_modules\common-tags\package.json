{"_args": [["common-tags@1.4.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "common-tags@1.4.0", "_id": "common-tags@1.4.0", "_inBundle": false, "_integrity": "sha1-EYe+Tz1M8MBCfUP3Tu8fc1AWFMA=", "_location": "/common-tags", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "common-tags@1.4.0", "name": "common-tags", "escapedName": "common-tags", "rawSpec": "1.4.0", "saveSpec": null, "fetchSpec": "1.4.0"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/common-tags/-/common-tags-1.4.0.tgz", "_spec": "1.4.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "ava": {"verbose": true, "babel": "inherit", "failFast": true, "require": ["babel-register"], "files": ["src/**/*.test.js"]}, "bugs": {"url": "http://github.com/declandewet/common-tags/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/declandewet"}, {"name": "<PERSON>", "url": "https://github.com/JKillian"}, {"name": "<PERSON>", "url": "https://github.com/laurentgoudet"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kamilogorek"}], "dependencies": {"babel-runtime": "^6.18.0"}, "description": "a few common utility template tags for ES2015", "devDependencies": {"ava": "^0.16.0", "babel-cli": "^6.18.0", "babel-eslint": "^7.1.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.8.0", "babel-preset-latest": "^6.16.0", "babel-preset-stage-0": "^6.16.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "cross-env": "3.1.3", "doctoc": "^1.0.0", "micromatch": "^2.3.8", "nyc": "^8.3.2", "regenerator-runtime": "^0.9.5", "rimraf": "^2.5.2", "snazzy": "^5.0.0", "when": "^3.7.7", "which": "^1.2.11"}, "directories": {"lib": "lib"}, "engines": {"node": ">=4.0.0"}, "homepage": "https://github.com/declandewet/common-tags", "jsnext:main": "es", "keywords": ["array", "babel", "es2015", "es2015-tag", "es6", "es6-tag", "heredoc", "html", "indent", "indents", "line", "literal", "multi", "multiline", "normalize", "one", "oneline", "single", "singleline", "string", "strings", "strip", "tag", "tagged", "template"], "license": "MIT", "main": "lib", "module": "es", "name": "common-tags", "repository": {"type": "git", "url": "git+https://github.com/declandewet/common-tags.git"}, "scripts": {"build": "npm run clear && npm run build:cjs && npm run build:es", "build:cjs": "cross-env BABEL_ENV=cjs babel src -d lib --ignore *.test.js", "build:es": "cross-env BABEL_ENV=es babel src -d es --ignore *.test.js", "clear": "rimraf lib && rimraf es", "codecov": "npm run coverage && codecov", "coverage": "nyc report --reporter=lcov", "lint": "snazzy", "precoveralls": "npm run coverage", "prerelease": "npm run build", "preversion": "doctoc readme.md --title '# :books: Table of Contents' && npm test", "release": "npm publish", "test": "npm run lint && cross-env BABEL_ENV=cjs nyc ava", "test-ci": "npm run lint && cross-env BABEL_ENV=cjs nyc ava --serial --fail-fast"}, "standard": {"parser": "babel-es<PERSON>", "ignore": ["readme.md", "es"]}, "version": "1.4.0"}