{"title": ":default CSS pseudo-class", "description": "The `:default` pseudo-class matches checkboxes and radio buttons which are checked by default, `<option>`s with the `selected` attribute, and the default submit button (if any) of a form.", "spec": "https://drafts.csswg.org/selectors-4/#the-default-pseudo", "status": "unoff", "links": [{"url": "https://html.spec.whatwg.org/multipage/scripting.html#selector-default", "title": "HTML specification for `:default`"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:default", "title": "Mozilla Developer Network (MDN) documentation - CSS :default"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/13311459--default-pseudo-class-from-selectors-level-4", "title": "MS Edge feature request on UserVoice"}, {"url": "http://jsbin.com/hiyada/edit?html,css,output", "title": "JS Bin testcase"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=156230", "title": "WebKit bug 156230 - `:default` CSS pseudo-class should match checkboxes+radios with a `checked` attribute"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "u", "10.5": "u", "10.6": "u", "11": "u", "11.1": "u", "11.5": "u", "11.6": "a #2", "12": "a #2", "12.1": "a #2", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #2"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "a #1", "4.4.3-4.4.4": "a #1", "56": "y"}, "bb": {"7": "u", "10": "a #1"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "a #2", "37": "a #1"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a #1"}, "samsung": {"4": "a #1", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "n"}}, "notes": "Whether `<option selected>` matches `:default` (per the spec) was not tested since `<select>`s and `<option>`s are generally not styleable, which makes it hard to formulate a test for this.", "notes_by_num": {"1": "Does not match `<input type=\"checkbox\" checked>` or `<input type=\"radio\" checked>`", "2": "Does not match the default submit button of a form"}, "usage_perc_y": 67.99, "usage_perc_a": 24.21, "ucprefix": false, "parent": "", "keywords": ":default,default", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}