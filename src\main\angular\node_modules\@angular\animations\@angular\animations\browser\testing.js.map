{"version": 3, "file": "testing.js", "sources": ["../../../../../../packages/animations/browser/testing/index.ts", "../../../../../../packages/animations/browser/testing/src/testing.ts", "../../../../../../packages/animations/browser/testing/src/mock_animation_driver.ts", "../../../../../../packages/animations/browser/src/render/shared.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/animations/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {MockAnimationDriver, MockAnimationPlayer} from './mock_animation_driver';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationPlayer, NoopAnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../../src/render/animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../../src/render/shared';\n\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): MockAnimationPlayer {\n    const player =\n        new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: {[key: string]: string | number} = {};\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ɵStyleData = {};\n\n  constructor(\n      public element: any, public keyframes: {[key: string]: string | number}[],\n      public duration: number, public delay: number, public easing: string,\n      public previousPlayers: any[]) {\n    super();\n    previousPlayers.forEach(player => {\n      if (player instanceof MockAnimationPlayer) {\n        const styles = player.currentSnapshot;\n        Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n      }\n    });\n\n    this.totalTime = delay + duration;\n  }\n\n  /* @internal */\n  onInit(fn: () => any) { this._onInitFns.push(fn); }\n\n  /* @internal */\n  init() {\n    super.init();\n    this._onInitFns.forEach(fn => fn());\n    this._onInitFns = [];\n  }\n\n  finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /* @internal */\n  triggerMicrotask() {}\n\n  play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  hasStarted() { return this.__started; }\n\n  beforeDestroy() {\n    const captures: ɵStyleData = {};\n\n    Object.keys(this.previousStyles).forEach(prop => {\n      captures[prop] = this.previousStyles[prop];\n    });\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this.keyframes.forEach(kf => {\n        Object.keys(kf).forEach(prop => {\n          if (prop != 'offset') {\n            captures[prop] = this.__finished ? kf[prop] : AUTO_STYLE;\n          }\n        });\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n"], "names": [], "mappings": ";;AGAA;;;;;;;AAOA,AAKA,AAAA,AASC;AAED,AAAA,AAsCC;AAED,AAAA,AAeC;AAED,AAAA,AAUC;AAED,AAAA,AAIC;AAED,AAAA,AAeC;AAED,AAAA,AAKC;AAED,IAAI,SAAS,GAAsC,CAAC,IAAS,EAAE,IAAS,KAAK,KAAK,CAAC;AACnF,IAAI,QAAQ,GAAgD,CAAC,OAAY,EAAE,QAAgB,KACvF,KAAK,CAAC;AACV,IAAI,MAAM,GACN,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,KADnD;IAEM,OAAO,EAAE,CAAC;CACX,CAAC;AAEN,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAEjC,SAAS,GAAG,CAAC,IAAS,EAAE,IAAS,KAAnC,EAA0C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAY,CAAC,EAAE,CAAC;IAEjF,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC7B,QAAQ,GAAG,CAAC,OAAY,EAAE,QAAgB,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1E;SAAM;QACL,MAAM,KAAK,GAAG,OAAO,CAAC,SAAgB,CAAC;QACvC,MAAM,EAAE,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACnF,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,EAAE,EAAE;YACN,QAAQ,GAAG,CAAC,OAAY,EAAE,QAAgB,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9E;KACF;IAED,MAAM,GAAG,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,KAA1D;QACI,IAAI,OAAO,GAAU,EAAE,CAAC;QACxB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACrD;aAAM;YACL,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,GAAG,EAAE;gBACP,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnB;SACF;QACD,OAAO,OAAO,CAAC;KAChB,CAAC;CACH;AAED,AAAO,MAAM,cAAc,GAAG,QAAQ,CAAC;AACvC,AAAO,MAAM,eAAe,GAAG,SAAS,CAAC;AACzC,AAAO,MAAM,WAAW,GAAG,MAAM,CAAC;;ADjKlC;;;;;;;AAOA,AAGA,AAGA;;;AAGA,AAAA,MAAA,mBAAA,CAAA;IAGE,cAAc,CAAC,OAAY,EAAE,QAAgB,EAA/C;QACI,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC1C;IAED,eAAe,CAAC,IAAS,EAAE,IAAS,EAAtC,EAAmD,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;IAEtF,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAtD;QACI,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;IAED,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAhE;QACI,OAAO,YAAY,IAAI,EAAE,CAAC;KAC3B;IAED,OAAO,CACH,OAAY,EAAE,SAA6C,EAAE,QAAgB,EAAE,KAAa,EAC5F,MAAc,EAAE,eAFtB,GAE+C,EAAE,EAFjD;QAGI,MAAM,MAAM,GACR,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QAC1F,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAkB,MAAM,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC;KACf;;AAvBM,mBAAT,CAAA,GAAY,GAAsB,EAAE,CAAC;;;;AA6BrC,AAAA,MAAA,mBAAiC,SAAQ,mBAAmB,CAA5D;IAOE,WAAF,CACa,OAAY,EAAS,SAA6C,EAClE,QAAgB,EAAS,KAAa,EAAS,MAAc,EAC7D,eAAsB,EAHnC;QAII,KAAK,EAAE,CAAC;QAHC,IAAb,CAAA,OAAoB,GAAP,OAAO,CAAK;QAAS,IAAlC,CAAA,SAA2C,GAAT,SAAS,CAAoC;QAClE,IAAb,CAAA,QAAqB,GAAR,QAAQ,CAAQ;QAAS,IAAtC,CAAA,KAA2C,GAAL,KAAK,CAAQ;QAAS,IAA5D,CAAA,MAAkE,GAAN,MAAM,CAAQ;QAC7D,IAAb,CAAA,eAA4B,GAAf,eAAe,CAAO;QATzB,IAAV,CAAA,UAAoB,GAAG,KAAK,CAAC;QACnB,IAAV,CAAA,SAAmB,GAAG,KAAK,CAAC;QACnB,IAAT,CAAA,cAAuB,GAAqC,EAAE,CAAC;QACrD,IAAV,CAAA,UAAoB,GAAkB,EAAE,CAAC;QAChC,IAAT,CAAA,eAAwB,GAAe,EAAE,CAAC;QAOtC,eAAe,CAAC,OAAO,CAAC,MAAM,IAAlC;YACM,IAAI,MAAM,YAAY,mBAAmB,EAAE;gBACzC,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;aAC/E;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAC;KACnC;;IAGD,MAAM,CAAC,EAAa,EAAtB,EAA0B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;IAGnD,IAAI,GAAN;QACI,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;IAED,MAAM,GAAR;QACI,KAAK,CAAC,MAAM,EAAE,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;IAED,OAAO,GAAT;QACI,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;;IAGD,gBAAgB,GAAlB,GAAuB;IAErB,IAAI,GAAN;QACI,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;IAED,UAAU,GAAZ,EAAiB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;IAEvC,aAAa,GAAf;QACI,MAAM,QAAQ,GAAe,EAAE,CAAC;QAEhC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,IAAjD;YACM,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC5C,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;YAIrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAA/B;gBACQ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAApC;oBACU,IAAI,IAAI,IAAI,QAAQ,EAAE;wBACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;qBAC1D;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;KACjC;CACF;;ADxHD;;;;;;GAMG,AACH,AAAiF;;ADPjF;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}