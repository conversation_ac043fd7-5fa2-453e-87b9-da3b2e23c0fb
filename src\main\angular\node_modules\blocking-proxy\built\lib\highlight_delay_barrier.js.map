{"version": 3, "file": "highlight_delay_barrier.js", "sourceRoot": "", "sources": ["../../lib/highlight_delay_barrier.ts"], "names": [], "mappings": ";;;;;;;;;AACA,qCAA4C,sBAAsB,CAAC,CAAA;AAGnE,MAAM,iBAAiB,GACnB,CAAC,gCAAW,CAAC,YAAY,EAAE,gCAAW,CAAC,eAAe,EAAE,gCAAW,CAAC,YAAY,CAAC,CAAC;AAEtF,IAAI,aAAa,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAG7D;;;;GAIG;AACH;IACE,YAAoB,MAA6B,EAAS,KAAa;QAAnD,WAAM,GAAN,MAAM,CAAuB;QAAS,UAAK,GAAL,KAAK,CAAQ;IAAG,CAAC;IAEnE,kBAAkB,CAAC,OAAyB;QAClD,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEO,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM;QAC5C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,UAAU,GAAG,aAAa,CAAC,YAAY,GAAG,2BAA2B;YAC7E,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;SACjC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB;QACzB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,UAAU,GAAG,aAAa,CAAC,mBAAmB,GAAG,2BAA2B;YACpF,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;IACL,CAAC;IAED,uDAAuD;IAC/C,KAAK,CAAC,KAAa;QACzB,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO;YACzB,UAAU,CAAC;gBACT,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;IAEK,SAAS,CAAC,OAAyB;;YACvC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACrD,MAAM,CAAC;YACT,CAAC;YACD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;YACjC,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAEzC,wFAAwF;YACxF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAEnD,oBAAoB;YACpB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CACrB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEnF,OAAO;YACP,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7B,sBAAsB;YACtB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAChE,CAAC;KAAA;AACH,CAAC;AAnDY,6BAAqB,wBAmDjC,CAAA"}