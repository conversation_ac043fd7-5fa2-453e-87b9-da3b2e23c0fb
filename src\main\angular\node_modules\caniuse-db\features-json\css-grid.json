{"title": "CSS Grid Layout", "description": "Method of using a grid concept to lay out content, providing a mechanism for authors to divide available space for layout into columns and rows using a set of predictable sizing behaviors", "spec": "http://www.w3.org/TR/css3-grid-layout/", "status": "cr", "links": [{"url": "http://blogs.msdn.com/b/ie/archive/2011/04/14/ie10-platform-preview-and-css-features-for-adaptive-layouts.aspx", "title": "IE Blog post"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=60731", "title": "Webkit (Chrome, Safari, etc.) feature request"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=616605", "title": "Mozilla (Firefox) feature request"}, {"url": "https://github.com/codler/Grid-Layout-Polyfill", "title": "Polyfill based on old spec"}, {"url": "https://github.com/FremyCompany/css-grid-polyfill/", "title": "Polyfill based on new spec"}, {"url": "https://webkit.org/blog/7434/css-grid-layout-a-new-layout-module-for-the-web/", "title": "WebKit Blog post"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "p", "10": "a x #2", "11": "a x #2"}, "edge": {"12": "a x #2", "13": "a x #2", "14": "a x #2", "15": "a x #2", "16": "y #5"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p d #3", "41": "p d #3", "42": "p d #3", "43": "p d #3", "44": "p d #3", "45": "p d #3", "46": "p d #3", "47": "p d #3", "48": "p d #3", "49": "p d #3", "50": "p d #3", "51": "p d #3", "52": "y #4", "53": "y #4", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p d #1", "30": "p d #1", "31": "p d #1", "32": "p d #1", "33": "p d #1", "34": "p d #1", "35": "p d #1", "36": "p d #1", "37": "p d #1", "38": "p d #1", "39": "p d #1", "40": "p d #1", "41": "p d #1", "42": "p d #1", "43": "p d #1", "44": "p d #1", "45": "p d #1", "46": "p d #1", "47": "p d #1", "48": "p d #1", "49": "p d #1", "50": "p d #1", "51": "p d #1", "52": "p d #1", "53": "p d #1", "54": "p d #1", "55": "p d #1", "56": "p d #1", "57": "y #4", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "p", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "p d #1", "29": "p d #1", "30": "p d #1", "31": "p d #1", "32": "p d #1", "33": "p d #1", "34": "p d #1", "35": "p d #1", "36": "p d #1", "37": "p d #1", "38": "p d #1", "39": "p d #1", "40": "p d #1", "41": "p d #1", "42": "p d #1", "43": "p d #1", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "p", "7.0-7.1": "p", "8": "p", "8.1-8.4": "p", "9.0-9.2": "p", "9.3": "p", "10.0-10.2": "p", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "p", "4.4": "p", "4.4.3-4.4.4": "p", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "p"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a x #2", "11": "a x #2"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "p", "5": "n"}, "and_qq": {"1.2": "p d #1"}, "baidu": {"7.12": "n"}}, "notes": "Supported in WebKit Nightly with `-webkit-` prefix.", "notes_by_num": {"1": "Enabled in Chrome through the \"experimental Web Platform features\" flag in chrome://flags", "2": "Partial support in IE refers to supporting an [older version](http://www.w3.org/TR/2011/WD-css3-grid-layout-20110407/) of the specification.", "3": "Enabled in Firefox through the `layout.css.grid.enabled ` flag", "4": "There are some bugs with overflow ([1356820](https://bugzilla.mozilla.org/show_bug.cgi?id=1356820), [1348857](https://bugzilla.mozilla.org/show_bug.cgi?id=1348857), [1350925](https://bugzilla.mozilla.org/show_bug.cgi?id=1350925))", "5": "Enabled in Edge through the \"Enable Unprefixed CSS Grid Layout\" flag in about:flags"}, "usage_perc_y": 65.64, "usage_perc_a": 5.11, "ucprefix": false, "parent": "", "keywords": "grids,grid-row,grid-column,grid-template,display:grid", "ie_id": "grid", "chrome_id": "4589636412243968", "firefox_id": "css-grid-layout", "webkit_id": "specification-css-grid-layout-level-1", "shown": true}