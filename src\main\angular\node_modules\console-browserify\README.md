# console-browserify

[![build status][1]][2]

[![browser support][3]][4]


Emulate console for all the browsers

## Example

```js
var console = require("console-browserify")

console.log("hello world!")
```

## Installation

`npm install console-browserify`

## Contributors

 - Raynos

## MIT Licenced



  [1]: https://secure.travis-ci.org/Raynos/console-browserify.png
  [2]: http://travis-ci.org/Raynos/console-browserify
  [3]: http://ci.testling.com/Raynos/console-browserify.png
  [4]: http://ci.testling.com/Raynos/console-browserify
