{"_args": [["babel-template@6.25.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-template@6.25.0", "_id": "babel-template@6.25.0", "_inBundle": false, "_integrity": "sha1-ZlJBFmt8KqTGGdceGSlpVSsQwHE=", "_location": "/babel-template", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-template@6.25.0", "name": "babel-template", "escapedName": "babel-template", "rawSpec": "6.25.0", "saveSpec": null, "fetchSpec": "6.25.0"}, "_requiredBy": ["/istanbul-lib-instrument"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-template/-/babel-template-6.25.0.tgz", "_spec": "6.25.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.22.0", "babel-traverse": "^6.25.0", "babel-types": "^6.25.0", "babylon": "^6.17.2", "lodash": "^4.2.0"}, "description": "Generate an AST from a string template.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-template", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-template"}, "version": "6.25.0"}