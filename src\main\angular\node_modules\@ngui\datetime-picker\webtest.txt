DateTime Picker Test On Browser
To Run this, run $ `npm start`first to start the web server with port 9002

START
  open browser
  go to http://localhost:9002

TEST2
  click "#test2 input"
  click "#test2 div.month b.prev_next.prev"
  verify element "#test2 div.day[title='2016-1-20']" without class selectable
  click "#test2 div.month b.prev_next.next"
  verify element "#test2 div.day[title='2017-1-10']" without class selectable
  verify element "#test2 div.day[title='2017-1-20']" without class selectable
  click "#test2 div.month b.prev_next.next"
  verify element "#test2 div.day[title='2018-1-20']" without class selectable
  click "#test2 div.month b.prev_next.prev"
  click "#test2 div.day.selectable[title='2017-1-25']"
  verify element "#test2 input" value is "2017-01-25"
  click "#test2 #set-date"
  verify element "#test2 input" value is "2017-12-31"


TEST3
  click "#test3 input"
  verify element "#test3 .month" is not present
  verify element "#test3 .days" is not present
  verify element "#test3 .time" is visible

TEST4
  verify element "#test4 input" value matches "15/01/2017 15:22 [+-]"
  click "#test4 input"
  click "#test4 div.day.selectable[title='2017-1-25']"
  verify element "#test4 input" value matches "25/01/2017"

TEST5
  verify element "#test5 input" value is "2016-02-15"
  see "2015-12-31"
  click
  verify element "#test5 input" value is "2015-12-31"
  click "#test5 input"
  click "#test5 div.day.selectable[title='2015-12-30']"
  set element "#test5 input.hourInput" value 11
  set element "#test5 input.minutesInput" value as 19
  click body
  verify element "#test5 input" value is "2015-12-30 11:19"

TEST6
  verify element "#test6 input" value is "2017-01-28"
  click "#test6 input"
  click "#test6 div.month b.prev_next.prev.month"
  click "#test6 div.month b.prev_next.next.month"
  click "#test6 div.close-button"
  verify element "#test6 input" value is "2017-01-28"

END
  close browser


