{"_args": [["assert@1.4.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "assert@1.4.1", "_id": "assert@1.4.1", "_inBundle": false, "_integrity": "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=", "_location": "/assert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "assert@1.4.1", "name": "assert", "escapedName": "assert", "rawSpec": "1.4.1", "saveSpec": null, "fetchSpec": "1.4.1"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/assert/-/assert-1.4.1.tgz", "_spec": "1.4.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/defunctzombie/commonjs-assert/issues"}, "dependencies": {"util": "0.10.3"}, "description": "commonjs assert - node.js api compatible", "devDependencies": {"mocha": "~1.21.4", "zuul": "~3.10.0", "zuul-ngrok": "^4.0.0"}, "homepage": "https://github.com/defunctzombie/commonjs-assert", "keywords": ["assert"], "license": "MIT", "main": "./assert.js", "name": "assert", "repository": {"type": "git", "url": "git://github.com/defunctzombie/commonjs-assert.git"}, "scripts": {"browser-local": "zuul --no-coverage --local 8000 -- test.js", "test": "npm run test-node && npm run test-browser", "test-browser": "zuul -- test.js", "test-native": "TEST_NATIVE=true mocha --ui qunit test.js", "test-node": "mocha --ui qunit test.js"}, "version": "1.4.1"}