{"title": "HTML Imports", "description": "Method of including and reusing HTML documents in other HTML documents.", "spec": "http://www.w3.org/TR/html-imports/", "status": "wd", "links": [{"url": "http://www.polymer-project.org/platform/html-imports.html", "title": "Polymer project (polyfill & web components framework)"}, {"url": "http://www.html5rocks.com/tutorials/webcomponents/imports/", "title": "HTML5Rocks - HTML Imports: #include for the web"}, {"url": "https://code.google.com/p/chromium/issues/detail?id=240592", "title": "Chromium tracking bug: Implement HTML Imports"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=877072", "title": "Firefox tracking bug: Implement HTML Imports"}, {"url": "http://status.modern.ie/htmlimports", "title": "IE Web Platform Status and Roadmap: HTML Imports"}], "bugs": [], "categories": ["DOM", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "p", "11": "p"}, "edge": {"12": "p", "13": "p", "14": "p", "15": "p", "16": "p"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "p", "31": "p", "32": "p d #1", "33": "p d #1", "34": "p d #1", "35": "p d #1", "36": "p d #1", "37": "p d #1", "38": "p d #1", "39": "p d #1", "40": "p d #1", "41": "p d #1", "42": "p d #1", "43": "p d #1", "44": "p d #1", "45": "p d #1", "46": "p d #1", "47": "p d #1", "48": "p d #1", "49": "p d #1", "50": "p d #1", "51": "p d #1", "52": "p d #1", "53": "p d #1", "54": "p d #1", "55": "p d #1", "56": "p d #1", "57": "p d #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "n d #2", "35": "p d #3", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "p", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "p", "11": "p", "TP": "p"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n d #4", "18": "n d #4", "19": "n d #4", "20": "n d #4", "21": "n d #4", "22": "p d #5", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "p", "8": "p", "8.1-8.4": "p", "9.0-9.2": "p", "9.3": "p", "10.0-10.2": "p", "10.3": "p", "11": "p"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "p"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Firefox [has no plans to support HTML imports](https://hacks.mozilla.org/2014/12/mozilla-and-web-components/) though for now it can be enabled through the \"dom.webcomponents.enabled\" preference in about:config", "2": "Enabled through the \"Enable HTML Imports\" flag in chrome://flags", "3": "Enabled through the \"Experimental Web Platform features\" flag in chrome://flags", "4": "Enabled through the \"Enable HTML Imports\" flag in opera://flags", "5": "Enabled through the \"Experimental Web Platform features\" flag in opera://flags"}, "usage_perc_y": 68.41, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "web components", "ie_id": "htmlimports", "chrome_id": "5144752345317376", "firefox_id": "html-imports", "webkit_id": "feature-html-imports", "shown": true}