{"_args": [["array-find-index@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "array-find-index@1.0.2", "_id": "array-find-index@1.0.2", "_inBundle": false, "_integrity": "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=", "_location": "/array-find-index", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-find-index@1.0.2", "name": "array-find-index", "escapedName": "array-find-index", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/currently-unhandled"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-find-index/-/array-find-index-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/array-find-index/issues"}, "description": "ES2015 `Array#findIndex()` ponyfill", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/array-find-index#readme", "keywords": ["es2015", "ponyfill", "polyfill", "shim", "find", "index", "findindex", "array"], "license": "MIT", "name": "array-find-index", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/array-find-index.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}