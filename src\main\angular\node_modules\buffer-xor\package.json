{"_args": [["buffer-xor@1.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-xor@1.0.3", "_id": "buffer-xor@1.0.3", "_inBundle": false, "_integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "_location": "/buffer-xor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-xor@1.0.3", "name": "buffer-xor", "escapedName": "buffer-xor", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/browserify-aes"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-xor/-/buffer-xor-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/crypto-browserify/buffer-xor/issues"}, "description": "A simple module for bitwise-xor on buffers", "devDependencies": {"mocha": "*", "standard": "*"}, "homepage": "https://github.com/crypto-browserify/buffer-xor", "keywords": ["bits", "bitwise", "buffer", "buffer-xor", "crypto", "inline", "math", "memory", "performance", "xor"], "license": "MIT", "main": "index.js", "name": "buffer-xor", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/buffer-xor.git"}, "scripts": {"standard": "standard", "test": "npm run-script unit", "unit": "mocha"}, "version": "1.0.3"}