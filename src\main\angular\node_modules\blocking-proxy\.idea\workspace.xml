<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="92b2698b-bb6b-446a-bd76-fd2d285e17d5" name="Default" comment="" />
    <ignored path="blockingproxy.iws" />
    <ignored path=".idea/workspace.xml" />
    <ignored path=".idea/dataSources.local.xml" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="default_target" />
  <component name="FavoritesManager">
    <favorites_list name="blockingproxy" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="angular_wait_barrier.ts" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/angular_wait_barrier.ts">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="360">
              <caret line="97" column="6" lean-forward="false" selection-start-line="97" selection-start-column="6" selection-end-line="97" selection-end-column="6" />
              <folding>
                <element signature="e#0#64#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="config.ts" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/lib/config.ts">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="105">
              <caret line="7" column="26" lean-forward="false" selection-start-line="7" selection-start-column="26" selection-end-line="7" selection-end-column="26" />
              <folding>
                <marker date="1485852025000" expanded="true" signature="731:1531" ph="..." />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GradleLocalSettings">
    <option name="externalProjectsViewState">
      <projects_view />
    </option>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/spec/e2e/hybrid_async_spec.ts" />
        <option value="$PROJECT_DIR$/tsconfig.json" />
        <option value="$PROJECT_DIR$/lib/index.ts" />
        <option value="$PROJECT_DIR$/spec/unit/fake_selenium.ts" />
        <option value="$PROJECT_DIR$/lib/webdriver_command.ts" />
        <option value="$PROJECT_DIR$/spec/e2e/ng1_timeout_spec.ts" />
        <option value="$PROJECT_DIR$/spec/unit/config_spec.ts" />
        <option value="$PROJECT_DIR$/lib/angular/http_helper.ts" />
        <option value="$PROJECT_DIR$/testapp/ng1/alt_root_index.html" />
        <option value="$PROJECT_DIR$/testapp/ng1/index.html" />
        <option value="$PROJECT_DIR$/testapp/ng1/interaction/interaction.js" />
        <option value="$PROJECT_DIR$/testapp/ng1/interaction/interaction.html" />
        <option value="$PROJECT_DIR$/webdriver_commands.ts" />
        <option value="$PROJECT_DIR$/spec/jasmine_e2e.json" />
        <option value="$PROJECT_DIR$/spec/unit/logging_spec.ts" />
        <option value="$PROJECT_DIR$/lib/webdriverCommands.ts" />
        <option value="$PROJECT_DIR$/lib/webdriverLogger.ts" />
        <option value="$PROJECT_DIR$/spec/unit/webdriverLogger_spec.ts" />
        <option value="$PROJECT_DIR$/lib/angular/wait.js" />
        <option value="$PROJECT_DIR$/testapp/ng1/polling/polling.html" />
        <option value="$PROJECT_DIR$/testapp/ng1/app.js" />
        <option value="$PROJECT_DIR$/spec/unit/webdriver_proxy_spec.ts" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/lib/webdriver_proxy.ts" />
        <option value="$PROJECT_DIR$/spec/unit/webdriver_commands_spec.ts" />
        <option value="$PROJECT_DIR$/spec/unit/util.ts" />
        <option value="$PROJECT_DIR$/spec/helpers/mock_selenium.ts" />
        <option value="$PROJECT_DIR$/lib/highlight_barrier.ts" />
        <option value="$PROJECT_DIR$/spec/unit/client_spec.ts" />
        <option value="$PROJECT_DIR$/spec/e2e/e2e_spec.ts" />
        <option value="$PROJECT_DIR$/spec/e2e/ng1_polling_spec.ts" />
        <option value="$PROJECT_DIR$/lib/config.ts" />
        <option value="$PROJECT_DIR$/lib/bin.ts" />
        <option value="$PROJECT_DIR$/spec/e2e/environment.ts" />
        <option value="$PROJECT_DIR$/lib/blockingproxy.ts" />
        <option value="$PROJECT_DIR$/spec/unit/proxy_spec.ts" />
        <option value="$PROJECT_DIR$/spec/unit/simple_webdriver_client_spec.ts" />
        <option value="$PROJECT_DIR$/spec/e2e/ng1_async_spec.ts" />
        <option value="$PROJECT_DIR$/lib/highlight_delay_spec.ts" />
        <option value="$PROJECT_DIR$/lib/highlight_delay_barrier.ts" />
        <option value="$PROJECT_DIR$/spec/unit/highlight_delay_barrier_spec.ts" />
        <option value="$PROJECT_DIR$/testapp/ng1/async/async.js" />
        <option value="$PROJECT_DIR$/spec/e2e/logging_spec.ts" />
        <option value="$PROJECT_DIR$/spec/unit/webdriver_logger_spec.ts" />
        <option value="$PROJECT_DIR$/lib/webdriver_commands.ts" />
        <option value="$PROJECT_DIR$/lib/webdriver_logger.ts" />
        <option value="$PROJECT_DIR$/lib/client.ts" />
        <option value="$PROJECT_DIR$/lib/simple_webdriver_client.ts" />
        <option value="$PROJECT_DIR$/lib/client_scripts/highlight.js" />
        <option value="$PROJECT_DIR$/spec/unit/angular_wait_barrier_spec.ts" />
        <option value="$PROJECT_DIR$/lib/angular_wait_barrier.ts" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <gulpfiles>
      <GulpfileState>
        <gulpfile-path>$PROJECT_DIR$/gulpfile.js</gulpfile-path>
        <tasks>
          <GulpTaskState>
            <dependencies>
              <dependency value="prepublish" />
            </dependencies>
            <task-name>build</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>built:copy</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>clang</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>clang-check</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <dependencies>
              <dependency value="prepublish" />
            </dependencies>
            <task-name>default</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>jslint</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>prepublish</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>pretest</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <dependencies>
              <dependency value="build" />
              <dependency value="test:copy" />
            </dependencies>
            <task-name>test</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>test:copy</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <dependencies>
              <dependency value="build" />
              <dependency value="test:copy" />
            </dependencies>
            <task-name>test:e2e</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>tsc</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>typings</task-name>
          </GulpTaskState>
          <GulpTaskState>
            <task-name>webdriver:update</task-name>
          </GulpTaskState>
        </tasks>
      </GulpfileState>
    </gulpfiles>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
    <handled-path value="$PROJECT_DIR$/testapp/node_modules" />
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="79" />
    <option name="y" value="74" />
    <option name="width" value="1294" />
    <option name="height" value="754" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>AngularJS</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="testapp" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="testapp" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="ng1" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="testapp" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="ng1" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="polling" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="testapp" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="ng1" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="interaction" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="testapp" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="ng1" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="async" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="spec" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="spec" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="unit" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="spec" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="helpers" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="spec" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="e2e" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="lib" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="blocking-proxy" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="lib" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="client_scripts" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
      <pane id="PackagesPane" />
      <pane id="Scratches" />
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="nodejs_interpreter_path" value="$USER_HOME$/.nvm/versions/node/v5.11.0/bin/node" />
    <property name="js.buildTools.gulp.gulp_package_dir" value="$PROJECT_DIR$/node_modules/gulp" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="js-jscs-nodeInterpreter" value="$USER_HOME$/.nvm/versions/node/v5.11.0/bin/node" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
    <property name="JavaScriptPreferStrict" value="false" />
    <property name="JavaScriptWeakerCompletionTypeGuess" value="true" />
    <property name="SearchEverywhereHistoryKey" value="&#9;FILE&#9;file:///Users/<USER>/src/blockingproxy/lib/blockingproxy.ts" />
    <property name="js.eslint.eslintPackage" value="" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/spec/e2e" />
      <recent name="$PROJECT_DIR$/testapp/ng1/interaction" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/lib" />
    </key>
  </component>
  <component name="RunManager" selected="Gulp.js.Unit tests">
    <configuration default="true" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="" />
      <option name="DEPLOY" value="true" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="TARGET_SELECTION_MODE" value="SHOW_DIALOG" />
      <option name="PREFERRED_AVD" value="" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="SKIP_NOOP_APK_INSTALLATIONS" value="true" />
      <option name="FORCE_STOP_RUNNING_APP" value="true" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <option name="USE_LAST_SELECTED_DEVICE" value="false" />
      <option name="PREFERRED_AVD" value="" />
      <Java />
      <Profilers>
        <option name="ENABLE_ADVANCED_PROFILING" value="true" />
        <option name="GAPID_ENABLED" value="false" />
        <option name="GAPID_DISABLE_PCS" value="false" />
        <option name="SUPPORT_LIB_ENABLED" value="true" />
        <option name="INSTRUMENTATION_ENABLED" value="true" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <method />
    </configuration>
    <configuration default="true" type="AndroidTestRunConfigurationType" factoryName="Android Tests">
      <module name="" />
      <option name="TESTING_TYPE" value="0" />
      <option name="INSTRUMENTATION_RUNNER_CLASS" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="PACKAGE_NAME" value="" />
      <option name="EXTRA_OPTIONS" value="" />
      <option name="TARGET_SELECTION_MODE" value="SHOW_DIALOG" />
      <option name="PREFERRED_AVD" value="" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="SKIP_NOOP_APK_INSTALLATIONS" value="true" />
      <option name="FORCE_STOP_RUNNING_APP" value="true" />
      <option name="DEBUGGER_TYPE" value="Java" />
      <option name="USE_LAST_SELECTED_DEVICE" value="false" />
      <option name="PREFERRED_AVD" value="" />
      <Java />
      <Profilers>
        <option name="ENABLE_ADVANCED_PROFILING" value="true" />
        <option name="GAPID_ENABLED" value="false" />
        <option name="GAPID_DISABLE_PCS" value="false" />
        <option name="SUPPORT_LIB_ENABLED" value="true" />
        <option name="INSTRUMENTATION_ENABLED" value="true" />
      </Profilers>
      <method />
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <option name="MAIN_CLASS_NAME" />
      <option name="VM_PARAMETERS" />
      <option name="PROGRAM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="ENABLE_SWING_INSPECTOR" value="false" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <module name="" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="ArquillianJUnit" factoryName="" nameIsGenerated="true">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <module name="" />
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="TEST_OBJECT" value="class" />
      <option name="VM_PARAMETERS" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <envs />
      <patterns />
      <method />
    </configuration>
    <configuration default="true" type="ArquillianTestNG" factoryName="">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <module name="" />
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="SUITE_NAME" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="GROUP_NAME" />
      <option name="TEST_OBJECT" value="CLASS" />
      <option name="VM_PARAMETERS" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="OUTPUT_DIRECTORY" />
      <option name="ANNOTATION_TYPE" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <option name="USE_DEFAULT_REPORTERS" value="false" />
      <option name="PROPERTIES_FILE" />
      <envs />
      <properties />
      <listeners />
      <method />
    </configuration>
    <configuration default="true" type="Cold Fusion runner description" factoryName="Cold Fusion" custom_browser="" web_path="">
      <method />
    </configuration>
    <configuration default="true" type="CucumberJavaRunConfigurationType" factoryName="Cucumber java">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <option name="myFilePath" />
      <option name="GLUE" />
      <option name="myNameFilter" />
      <option name="myGeneratedName" />
      <option name="MAIN_CLASS_NAME" />
      <option name="VM_PARAMETERS" />
      <option name="PROGRAM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="ENABLE_SWING_INSPECTOR" value="false" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <module name="" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="FlashRunConfigurationType" factoryName="Flash App">
      <option name="BCName" value="" />
      <option name="IOSSimulatorSdkPath" value="" />
      <option name="adlOptions" value="" />
      <option name="airProgramParameters" value="" />
      <option name="appDescriptorForEmulator" value="Android" />
      <option name="debugTransport" value="USB" />
      <option name="debuggerSdkRaw" value="BC SDK" />
      <option name="emulator" value="NexusOne" />
      <option name="emulatorAdlOptions" value="" />
      <option name="fastPackaging" value="true" />
      <option name="fullScreenHeight" value="0" />
      <option name="fullScreenWidth" value="0" />
      <option name="launchUrl" value="false" />
      <option name="launcherParameters">
        <LauncherParameters>
          <option name="browser" value="a7bb68e0-33c0-4d6f-a81a-aac1fdb870c8" />
          <option name="launcherType" value="OSDefault" />
          <option name="newPlayerInstance" value="false" />
          <option name="playerPath" value="/Applications/Flash Player Debugger.app" />
        </LauncherParameters>
      </option>
      <option name="mobileRunTarget" value="Emulator" />
      <option name="moduleName" value="" />
      <option name="overriddenMainClass" value="" />
      <option name="overriddenOutputFileName" value="" />
      <option name="overrideMainClass" value="false" />
      <option name="runTrusted" value="true" />
      <option name="screenDpi" value="0" />
      <option name="screenHeight" value="0" />
      <option name="screenWidth" value="0" />
      <option name="url" value="http://" />
      <option name="usbDebugPort" value="7936" />
      <method />
    </configuration>
    <configuration default="true" type="FlexUnitRunConfigurationType" factoryName="FlexUnit" appDescriptorForEmulator="Android" class_name="" emulatorAdlOptions="" method_name="" package_name="" scope="Class">
      <option name="BCName" value="" />
      <option name="launcherParameters">
        <LauncherParameters>
          <option name="browser" value="a7bb68e0-33c0-4d6f-a81a-aac1fdb870c8" />
          <option name="launcherType" value="OSDefault" />
          <option name="newPlayerInstance" value="false" />
          <option name="playerPath" value="/Applications/Flash Player Debugger.app" />
        </LauncherParameters>
      </option>
      <option name="moduleName" value="" />
      <option name="trusted" value="true" />
      <method />
    </configuration>
    <configuration default="true" type="GradleRunConfiguration" factoryName="Gradle">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list />
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <method />
    </configuration>
    <configuration default="true" type="GrailsRunConfigurationType" factoryName="Grails">
      <setting name="vmparams" value="" />
      <setting name="cmdLine" value="run-app" />
      <setting name="passParentEnv" value="true" />
      <setting name="launchBrowser" value="true" />
      <setting name="launchBrowserUrl" value="" />
      <setting name="depsClasspath" value="false" />
      <method />
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <module name="" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="TEST_OBJECT" value="class" />
      <option name="VM_PARAMETERS" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <envs />
      <patterns />
      <method />
    </configuration>
    <configuration default="true" type="JarApplication" factoryName="JAR Application">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="Java Scratch" factoryName="Java Scratch">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <option name="SCRATCH_FILE_ID" value="0" />
      <option name="MAIN_CLASS_NAME" />
      <option name="VM_PARAMETERS" />
      <option name="PROGRAM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="ENABLE_SWING_INSPECTOR" value="false" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <module name="" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="JavaScriptTestRunnerProtractor" factoryName="Protractor">
      <config-file value="" />
      <node-interpreter value="project" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="JavascriptDebugType" factoryName="JavaScript Debug">
      <method />
    </configuration>
    <configuration default="true" type="JetRunConfigurationType" factoryName="Kotlin">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <option name="MAIN_CLASS_NAME" />
      <option name="VM_PARAMETERS" />
      <option name="PROGRAM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <module name="blockingproxy" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType" factoryName="Kotlin script">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <option name="filePath" />
      <option name="vmParameters" />
      <option name="alternativeJrePath" />
      <option name="programParameters" />
      <option name="passParentEnvs" value="true" />
      <option name="workingDirectory" />
      <option name="isAlternativeJrePathEnabled" value="false" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <module name="" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="TestNG" factoryName="TestNG">
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <module name="" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="SUITE_NAME" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="GROUP_NAME" />
      <option name="TEST_OBJECT" value="CLASS" />
      <option name="VM_PARAMETERS" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <option name="OUTPUT_DIRECTORY" />
      <option name="ANNOTATION_TYPE" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <option name="USE_DEFAULT_REPORTERS" value="false" />
      <option name="PROPERTIES_FILE" />
      <envs />
      <properties />
      <listeners />
      <method />
    </configuration>
    <configuration default="true" type="js.build_tools.gulp" factoryName="Gulp.js">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="js.build_tools.npm" factoryName="npm">
      <command value="run" />
      <scripts />
      <node-interpreter value="project" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="osgi.bnd.run" factoryName="Run Launcher">
      <method />
    </configuration>
    <configuration default="true" type="osgi.bnd.run" factoryName="Test Launcher (JUnit)">
      <method />
    </configuration>
    <configuration default="false" name="Unit tests" type="js.build_tools.gulp" factoryName="Gulp.js">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile>$PROJECT_DIR$/gulpfile.js</gulpfile>
      <tasks>
        <task>test</task>
      </tasks>
      <arguments />
      <envs />
      <method />
    </configuration>
    <list size="1">
      <item index="0" class="java.lang.String" itemvalue="Gulp.js.Unit tests" />
    </list>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="92b2698b-bb6b-446a-bd76-fd2d285e17d5" name="Default" comment="" />
      <created>1462817577958</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1462817577958</updated>
      <workItem from="1462817579296" duration="4587000" />
      <workItem from="1462983628636" duration="473000" />
      <workItem from="1463426743459" duration="146000" />
      <workItem from="1465422466937" duration="12000" />
      <workItem from="1465422496030" duration="5000" />
      <workItem from="1465423025115" duration="1000" />
      <workItem from="1465423109007" duration="1000" />
      <workItem from="1470275302818" duration="1668000" />
      <workItem from="1470678785842" duration="10597000" />
      <workItem from="1471467483259" duration="621000" />
      <workItem from="1471991052064" duration="531000" />
      <workItem from="1472059031644" duration="2774000" />
      <workItem from="1472159398081" duration="24000" />
      <workItem from="1472680629409" duration="2043000" />
      <workItem from="1474335298214" duration="8000" />
      <workItem from="1475778406096" duration="1890000" />
      <workItem from="1475785324467" duration="2650000" />
      <workItem from="1475799794962" duration="15486000" />
      <workItem from="1476125747944" duration="532000" />
      <workItem from="1476136080521" duration="4183000" />
      <workItem from="1476145366937" duration="2125000" />
      <workItem from="1476208702973" duration="14980000" />
      <workItem from="1476420345961" duration="3160000" />
      <workItem from="1476486730323" duration="13973000" />
      <workItem from="1476993168124" duration="17746000" />
      <workItem from="1477429325463" duration="7481000" />
      <workItem from="1477443038487" duration="3658000" />
      <workItem from="1477613757165" duration="1211000" />
      <workItem from="1479338048590" duration="3268000" />
      <workItem from="1480362348089" duration="3089000" />
      <workItem from="1481062896780" duration="4994000" />
      <workItem from="1481223583342" duration="7237000" />
      <workItem from="1481322954064" duration="6802000" />
      <workItem from="1481669750717" duration="7890000" />
      <workItem from="1481847620043" duration="3612000" />
      <workItem from="1482366328962" duration="30126000" />
      <workItem from="1483483658434" duration="5672000" />
      <workItem from="1483557384733" duration="53892000" />
      <workItem from="1484974051092" duration="47176000" />
      <workItem from="1485200180023" duration="63000" />
      <workItem from="1485200289465" duration="20741000" />
      <workItem from="1485323626067" duration="60563000" />
      <workItem from="1485837427702" duration="2078000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="369769000" />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="79" y="74" width="1294" height="754" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info id="Palette" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Nl-Palette" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.24894515" sideWeight="0.50328946" order="7" side_tool="true" content_ui="tabs" />
      <window_info id="Maven Projects" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Properties" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Capture Tool" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="TypeScript" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.39104477" sideWeight="0.48519737" order="11" side_tool="false" content_ui="tabs" />
      <window_info id="Designer" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="UI Designer" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32863188" sideWeight="0.49671054" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Palette&#9;" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Image Layers" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Capture Analysis" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.3299363" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.31420764" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="true" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="10" side_tool="false" content_ui="tabs" />
      <window_info id="Gulp" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="true" content_ui="tabs" />
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.18929712" sideWeight="0.48378378" order="0" side_tool="false" content_ui="combo" />
      <window_info id="Theme Preview" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.18372093" sideWeight="0.5162162" order="8" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="TypeScript Compiler" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.32903227" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32880434" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
    </layout>
    <layout-to-restore>
      <window_info id="Palette" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Nl-Palette" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.24894515" sideWeight="0.50328946" order="7" side_tool="true" content_ui="tabs" />
      <window_info id="Maven Projects" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Properties" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Capture Tool" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="TypeScript" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.39104477" sideWeight="0.48519737" order="11" side_tool="false" content_ui="tabs" />
      <window_info id="Designer" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="UI Designer" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32863188" sideWeight="0.49671054" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="TypeScript Compiler" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.32903227" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Palette&#9;" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Image Layers" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Capture Analysis" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.3299363" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="true" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.31420764" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="10" side_tool="false" content_ui="tabs" />
      <window_info id="Gulp" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="true" content_ui="tabs" />
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.18929712" sideWeight="0.48378378" order="0" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32880434" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Theme Preview" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.18372093" sideWeight="0.5162162" order="8" side_tool="true" content_ui="tabs" />
    </layout-to-restore>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="processedProjectFiles" value="true" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.component.js" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.component.js.map" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.module.js" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.module.js.map" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/main.js" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/main.js.map" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.routes.js" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/app.routes.js.map" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/home/<USER>" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/home/<USER>" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/async/async.component.js" />
        <option value="$PROJECT_DIR$/testapp/ng2/app/async/async.component.js.map" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/ng1.js" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/ng1.js.map" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/myApp.js" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/myApp.js.map" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/ng2.js" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/ng2.js.map" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/upgrader.js" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/upgrader.js.map" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/main.js" />
        <option value="$PROJECT_DIR$/testapp/hybrid/app/main.js.map" />
        <option value="$PROJECT_DIR$/../protractor/spec/install/browserts_spec.js" />
        <option value="$PROJECT_DIR$/../protractor/spec/install/typescript_spec.js" />
        <option value="$PROJECT_DIR$/../protractor/spec/install/conf.js" />
        <option value="$PROJECT_DIR$/../protractor/spec/install/typescript_conf.js" />
        <option value="$PROJECT_DIR$/../protractor/testapp/ng2/app/main.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/ng2/app/app.module.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/ng2/app/app.routes.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/ng2/app/app.component.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/ng2.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/module.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/myApp.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/main.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/ng1.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/ng2/app/async/async.component.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/no_static/main.js.map" />
        <option value="$PROJECT_DIR$/../protractor/testapp/upgrade/app/no_static/upgrader.js.map" />
      </list>
    </option>
    <option name="configsExcludedFiles">
      <set>
        <option value="$PROJECT_DIR$/built" />
      </set>
    </option>
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/spec/unit/webdriver_proxy_spec.ts</url>
          <line>91</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/lib/angular/wait.js</url>
          <line>29</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
      </breakpoints>
      <option name="time" value="12" />
    </breakpoint-manager>
    <watches-manager />
  </component>
  <component name="antWorkspaceConfiguration">
    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
    <option name="FILTER_TARGETS" value="false" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/lib/webdriver_command.ts" />
    <entry file="file://$PROJECT_DIR$/spec/unit/fake_selenium.ts" />
    <entry file="file://$PROJECT_DIR$/spec/e2e/ng1_timeout_spec.ts" />
    <entry file="file://$PROJECT_DIR$/spec/environment.js" />
    <entry file="file://$PROJECT_DIR$/lib/angular/http_helper.ts" />
    <entry file="file://$PROJECT_DIR$/testapp/ng1/alt_root_index.html" />
    <entry file="file://$PROJECT_DIR$/testapp/ng1/interaction/interaction.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="17" column="0" lean-forward="false" selection-start-line="17" selection-start-column="0" selection-end-line="17" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/jasmine_e2e.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="90">
          <caret line="6" column="29" lean-forward="false" selection-start-line="6" selection-start-column="29" selection-end-line="6" selection-end-column="29" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/jasmine_unit.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/config_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="75">
          <caret line="5" column="56" lean-forward="false" selection-start-line="5" selection-start-column="56" selection-end-line="5" selection-end-column="56" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/index.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="15">
          <caret line="1" column="34" lean-forward="false" selection-start-line="1" selection-start-column="34" selection-end-line="1" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/app.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="75">
          <caret line="5" column="37" lean-forward="false" selection-start-line="5" selection-start-column="37" selection-end-line="5" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="165">
          <caret line="11" column="51" lean-forward="false" selection-start-line="11" selection-start-column="51" selection-end-line="11" selection-end-column="51" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="342">
          <caret line="41" column="0" lean-forward="false" selection-start-line="41" selection-start-column="0" selection-end-line="41" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/node_modules/@types/selenium-webdriver/index.d.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="226">
          <caret line="4121" column="4" lean-forward="false" selection-start-line="4121" selection-start-column="4" selection-end-line="4121" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/async/async.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/interaction/interaction.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="210">
          <caret line="14" column="13" lean-forward="false" selection-start-line="14" selection-start-column="13" selection-end-line="14" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/polling/polling.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="60">
          <caret line="4" column="0" lean-forward="false" selection-start-line="4" selection-start-column="0" selection-end-line="4" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/highlight_barrier.ts" />
    <entry file="file://$PROJECT_DIR$/spec/unit/webdriver_commands_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="86" column="16" lean-forward="true" selection-start-line="86" selection-start-column="16" selection-end-line="86" selection-end-column="16" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/helpers/jasmine-co.helper.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="45">
          <caret line="3" column="0" lean-forward="true" selection-start-line="3" selection-start-column="0" selection-end-line="3" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/helpers/mock_selenium.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="83" column="22" lean-forward="false" selection-start-line="83" selection-start-column="22" selection-end-line="83" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/node_modules/@types/node/index.d.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="446">
          <caret line="2148" column="12" lean-forward="true" selection-start-line="2148" selection-start-column="12" selection-end-line="2148" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/util.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="570">
          <caret line="41" column="13" lean-forward="true" selection-start-line="41" selection-start-column="13" selection-end-line="41" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/e2e/e2e_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="30">
          <caret line="3" column="0" lean-forward="false" selection-start-line="3" selection-start-column="0" selection-end-line="3" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/e2e/ng1_polling_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="135">
          <caret line="13" column="8" lean-forward="false" selection-start-line="13" selection-start-column="8" selection-end-line="13" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/angular/wait.js" />
    <entry file="file://$PROJECT_DIR$/lib/bin.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="240">
          <caret line="17" column="83" lean-forward="false" selection-start-line="17" selection-start-column="83" selection-end-line="17" selection-end-column="83" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/e2e/environment.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="17" column="35" lean-forward="false" selection-start-line="17" selection-start-column="35" selection-end-line="17" selection-end-column="35" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/client_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="345">
          <caret line="23" column="48" lean-forward="false" selection-start-line="23" selection-start-column="48" selection-end-line="23" selection-end-column="48" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/highlight_delay_spec.ts" />
    <entry file="file://$PROJECT_DIR$/spec/e2e/ng1_async_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="30">
          <caret line="6" column="0" lean-forward="false" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/simple_webdriver_client_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="480">
          <caret line="32" column="0" lean-forward="false" selection-start-line="32" selection-start-column="0" selection-end-line="32" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/proxy_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="60">
          <caret line="4" column="34" lean-forward="false" selection-start-line="4" selection-start-column="34" selection-end-line="4" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/client_scripts/wait.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1493">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/highlight_delay_barrier_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="240">
          <caret line="16" column="5" lean-forward="false" selection-start-line="16" selection-start-column="5" selection-end-line="16" selection-end-column="5" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/webdriver_logger_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="484">
          <caret line="234" column="37" lean-forward="false" selection-start-line="234" selection-start-column="37" selection-end-line="234" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/e2e/logging_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="345">
          <caret line="23" column="0" lean-forward="false" selection-start-line="23" selection-start-column="0" selection-end-line="23" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testapp/ng1/async/async.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="169">
          <caret line="45" column="7" lean-forward="false" selection-start-line="45" selection-start-column="7" selection-end-line="45" selection-end-column="7" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/webdriver_proxy_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="319">
          <caret line="180" column="20" lean-forward="false" selection-start-line="180" selection-start-column="20" selection-end-line="180" selection-end-column="20" />
          <folding>
            <element signature="e#0#29#0" expanded="false" />
            <element signature="e#3583#3623#0" expanded="false" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/webdriver_logger.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="394">
          <caret line="154" column="9" lean-forward="false" selection-start-line="154" selection-start-column="9" selection-end-line="154" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/client.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="345">
          <caret line="39" column="41" lean-forward="false" selection-start-line="39" selection-start-column="41" selection-end-line="39" selection-end-column="41" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/webdriver_commands.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-2951">
          <caret line="5" column="3" lean-forward="false" selection-start-line="5" selection-start-column="3" selection-end-line="5" selection-end-column="3" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/blockingproxy.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="304">
          <caret line="115" column="48" lean-forward="false" selection-start-line="115" selection-start-column="48" selection-end-line="115" selection-end-column="48" />
          <folding>
            <element signature="e#0#29#0" expanded="false" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/client_scripts/highlight.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="225">
          <caret line="15" column="4" lean-forward="false" selection-start-line="15" selection-start-column="4" selection-end-line="15" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/webdriver_proxy.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="465">
          <caret line="34" column="23" lean-forward="false" selection-start-line="34" selection-start-column="23" selection-end-line="34" selection-end-column="23" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/simple_webdriver_client.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="559">
          <caret line="95" column="0" lean-forward="false" selection-start-line="95" selection-start-column="0" selection-end-line="95" selection-end-column="0" />
          <folding>
            <element signature="e#0#29#0" expanded="false" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/highlight_delay_barrier.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="10" column="0" lean-forward="false" selection-start-line="10" selection-start-column="0" selection-end-line="10" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/spec/unit/angular_wait_barrier_spec.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="15">
          <caret line="1" column="11" lean-forward="false" selection-start-line="1" selection-start-column="11" selection-end-line="1" selection-end-column="11" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/angular_wait_barrier.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="360">
          <caret line="97" column="6" lean-forward="false" selection-start-line="97" selection-start-column="6" selection-end-line="97" selection-end-column="6" />
          <folding>
            <element signature="e#0#64#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/config.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="7" column="26" lean-forward="false" selection-start-line="7" selection-start-column="26" selection-end-line="7" selection-end-column="26" />
          <folding>
            <marker date="1485852025000" expanded="true" signature="731:1531" ph="..." />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>