[{"__symbolic": "module", "version": 3, "metadata": {"NgModuleCompileResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}]}}, "NgModuleCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "compile": [{"__symbolic": "method"}], "createStub": [{"__symbolic": "method"}], "_createNgModuleFactory": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"NgModuleCompileResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}]}}, "NgModuleCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "compile": [{"__symbolic": "method"}], "createStub": [{"__symbolic": "method"}], "_createNgModuleFactory": [{"__symbolic": "method"}]}}}}]