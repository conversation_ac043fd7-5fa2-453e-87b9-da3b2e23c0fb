{"_args": [["acorn@5.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "acorn@5.1.0", "_id": "acorn@5.1.0", "_inBundle": false, "_integrity": "sha512-WXZ0VTJT8EE25BmZjc+wr0qIwG7QaEna9csPKHS6WQp8gDo4V376wUWi222LXRiuAF6CAS4Ejv736DdRwuPK9g==", "_location": "/acorn", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "acorn@5.1.0", "name": "acorn", "escapedName": "acorn", "rawSpec": "5.1.0", "saveSpec": null, "fetchSpec": "5.1.0"}, "_requiredBy": ["/webpack"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/acorn/-/acorn-5.1.0.tgz", "_spec": "5.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bin": {"acorn": "bin/acorn"}, "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "description": "ECMAScript parser", "devDependencies": {"eslint": "^3.18.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^2.1.1", "rollup": "^0.43.0", "rollup-plugin-buble": "^0.15.0", "unicode-9.0.0": "^0.7.0"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/ternjs/acorn", "license": "MIT", "main": "dist/acorn.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}], "module": "dist/acorn.es.js", "name": "acorn", "repository": {"type": "git", "url": "git+https://github.com/ternjs/acorn.git"}, "scripts": {"build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "build:bin": "rollup -c rollup/config.bin.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "lint": "eslint src/", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "test": "node test/run.js && node test/lint.js"}, "version": "5.1.0"}