{"version": 3, "file": "schema-class-factory.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/schema-class-factory.ts"], "names": [], "mappings": ";;AAAA,6CAAwC;AACxC,+CAAiE;AACjE,mCAA4C;AAE5C,uBAAqB;AAErB,qBAA6B,SAAQ,2BAAmB;CAAG;AAA3D,0CAA2D;AAE3D,oDAAoD;AACpD,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC1C,yCAAyC;AACzC,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAG7C;;;;;;;GAOG;AACH,wBAAwB,IAAY;IAClC,MAAM,SAAS,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QAEnC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACX,MAAM,IAAI,eAAe,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAGD,sDAAsD;AACtD,+BAAkC,YAA+B,EAC/B,IAAY;IAC5C,IAAI,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IACrC,kDAAkD;IAClD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAuB,EAAE,OAAe;QAC/D,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,EAAE,YAAY,CAAC,CAAC;AACnB,CAAC;AAuBD;IACE,YAAY,MAAc,EAAE,KAAQ,EAAE,GAAG,SAAc;QACpD,IAAY,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;QACrC,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;cACpB,CAAC,IAAI,eAAe,CAAI,MAAM,EAAE,SAAS,CAAC,GAAG,EAAE,EAAE,GAAG,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;cAC1E,IAAI,CAAC;QACpB,IAAY,CAAC,WAAW,CAAC,GAAG,IAAI,gCAAkB,CAAC,IAAI,EAAE;YACxD,OAAO;YACP,KAAK;YACL,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED,MAAM,KAAQ,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC;IACnC,QAAQ,KAAyB,MAAM,CAAE,IAAY,CAAC,WAAW,CAAuB,CAAC,CAAC,CAAC;IAC3F,cAAc,KAAQ,MAAM,CAAE,IAAY,CAAC,aAAa,CAAM,CAAC,CAAC,CAAC;IAEjE,2EAA2E;IAC3E,OAAO,CAAC,MAAc,EAAE,WAAmB;QACzC,IAAI,oBAAoB,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;QAC1E,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9C,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAU,EAAE,OAAe;YAC9D,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAE1B,EAAE,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC;YAC7B,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QACD,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;IAED,yDAAyD;IACzD,SAAS;QACP,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IAED,oCAAoC;IACpC,KAAK,CAAC,IAAY;QAChB,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;IACvC,CAAC;IAED,oCAAoC;IACpC,KAAK,CAAC,IAAY,EAAE,KAAU;QAC5B,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QAE1D,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,wEAAwE;YACxE,8BAA8B;YAC9B,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,CAAC,SAAS,CAAC;YACnB,CAAC;YACD,MAAM,MAAM,GAAQ,SAAS;iBAC1B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACZ,MAAM,CAAC,CAAC,MAAW,EAAE,IAAY,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAEvE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YAClD,CAAC;QACH,CAAC;IACH,CAAC;IAED,6CAA6C;IAC7C,QAAQ,CAAC,IAAY;QACnB,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,SAAS,CAAC,IAAY;QACpB,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACrC,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED,+BAA+B;IAC/B,WAAW,CAAC,QAAQ,GAAG,kBAAkB,EAAE,GAAG,OAAc;QAC1D,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,MAAM,UAAU,GAAG,uBAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC;QAElF,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtC,UAAU,CAAC,GAAG,EAAE,CAAC;QAEjB,MAAM,CAAC,GAAG,CAAC;IACb,CAAC;CACF;AAKD;;;;;;;GAOG;AACH,4BAAsC,MAAc;IAClD,0BAA2B,SAAQ,eAAkB;QACnD,YAAY,KAAQ,EAAE,GAAG,SAAc;YACrC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,CAAC;QACrC,CAAC;KACF;IAED,MAAM,CAAC,oBAAoB,CAAC;AAC9B,CAAC;AARD,gDAQC"}