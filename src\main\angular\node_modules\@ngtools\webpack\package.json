{"_args": [["@ngtools/webpack@1.5.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@ngtools/webpack@1.5.0", "_id": "@ngtools/webpack@1.5.0", "_inBundle": false, "_integrity": "sha1-tr5Y2FfUH4mZdR1rvD0h6EvJd8o=", "_location": "/@ngtools/webpack", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@ngtools/webpack@1.5.0", "name": "@ngtools/webpack", "escapedName": "@ngtools%2fwebpack", "scope": "@ngtools", "rawSpec": "1.5.0", "saveSpec": null, "fetchSpec": "1.5.0"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@ngtools/webpack/-/webpack-1.5.0.tgz", "_spec": "1.5.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "dependencies": {"enhanced-resolve": "^3.1.0", "loader-utils": "^1.0.2", "magic-string": "^0.19.0", "source-map": "^0.5.6"}, "description": "Webpack plugin that AoT compiles your Angular components and modules.", "engines": {"node": ">= 4.1.0", "npm": ">= 3.0.0"}, "homepage": "https://github.com/angular/angular-cli/tree/master/packages/@ngtools/webpack", "keywords": ["angular", "webpack", "plugin", "aot"], "license": "MIT", "main": "./src/index.js", "name": "@ngtools/webpack", "peerDependencies": {"typescript": "^2.0.2", "webpack": "^2.2.0 || ^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular-cli.git"}, "typings": "src/index.d.ts", "version": "1.5.0"}