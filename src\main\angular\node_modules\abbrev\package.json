{"_args": [["abbrev@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "abbrev@1.1.0", "_id": "abbrev@1.1.0", "_inBundle": false, "_integrity": "sha1-0FVMIlZjbi9W58LlrRg/hZQo2B8=", "_location": "/abbrev", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "abbrev@1.1.0", "name": "abbrev", "escapedName": "abbrev", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/node-gyp/nopt", "/nopt"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/abbrev/-/abbrev-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "description": "Like ruby's abbrev module, but in js", "devDependencies": {"tap": "^10.1"}, "files": ["abbrev.js"], "homepage": "https://github.com/isaacs/abbrev-js#readme", "license": "ISC", "main": "abbrev.js", "name": "abbrev", "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test.js --100"}, "version": "1.1.0"}