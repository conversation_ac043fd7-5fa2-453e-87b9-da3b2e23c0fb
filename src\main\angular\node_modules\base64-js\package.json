{"_args": [["base64-js@1.2.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "base64-js@1.2.1", "_id": "base64-js@1.2.1", "_inBundle": false, "_integrity": "sha512-dwVUVIXsBZXwTuwnXI9RK8sBmgq09NDHzyR9SAph9eqk76gKK2JSQmZARC2zRC81JC2QTtxD0ARU5qTS25gIGw==", "_location": "/base64-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64-js@1.2.1", "name": "base64-js", "escapedName": "base64-js", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/buffer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/base64-js/-/base64-js-1.2.1.tgz", "_spec": "1.2.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "description": "Base64 encoding/decoding in pure JS", "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}, "files": ["test", "index.js", "base64js.min.js"], "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "name": "base64-js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "version": "1.2.1"}