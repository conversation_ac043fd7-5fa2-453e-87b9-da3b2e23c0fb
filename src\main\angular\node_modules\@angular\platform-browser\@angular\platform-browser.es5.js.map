{"version": 3, "file": "platform-browser.es5.js", "sources": ["../../../../packages/platform-browser/public_api.ts", "../../../../packages/platform-browser/src/platform-browser.ts", "../../../../packages/platform-browser/src/version.ts", "../../../../packages/platform-browser/src/private_export.ts", "../../../../packages/platform-browser/src/dom/debug/by.ts", "../../../../packages/platform-browser/src/browser/tools/tools.ts", "../../../../packages/platform-browser/src/browser/tools/common_tools.ts", "../../../../packages/platform-browser/src/browser/tools/browser.ts", "../../../../packages/platform-browser/src/browser.ts", "../../../../packages/platform-browser/src/security/dom_sanitization_service.ts", "../../../../packages/platform-browser/src/security/style_sanitizer.ts", "../../../../packages/platform-browser/src/security/html_sanitizer.ts", "../../../../packages/platform-browser/src/security/url_sanitizer.ts", "../../../../packages/platform-browser/src/dom/events/key_events.ts", "../../../../packages/platform-browser/src/dom/events/hammer_gestures.ts", "../../../../packages/platform-browser/src/dom/events/dom_events.ts", "../../../../packages/platform-browser/src/dom/dom_renderer.ts", "../../../../packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../packages/platform-browser/src/dom/events/event_manager.ts", "../../../../packages/platform-browser/src/dom/debug/ng_probe.ts", "../../../../packages/platform-browser/src/dom/util.ts", "../../../../packages/platform-browser/src/browser/title.ts", "../../../../packages/platform-browser/src/browser/testability.ts", "../../../../packages/platform-browser/src/browser/server-transition.ts", "../../../../packages/platform-browser/src/browser/meta.ts", "../../../../packages/platform-browser/src/browser/location/browser_platform_location.ts", "../../../../packages/platform-browser/src/dom/dom_tokens.ts", "../../../../packages/platform-browser/src/browser/browser_adapter.ts", "../../../../packages/platform-browser/src/browser/generic_browser_adapter.ts", "../../../../packages/platform-browser/src/dom/dom_adapter.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\nexport {BrowserModule,platformBrowser,Meta,MetaDefinition,Title,disableDebugTools,enableDebugTools,By,Ng<PERSON>robeToken,DOCUMENT,EVENT_MANAGER_PLUGINS,EventManager,HAMMER_GESTURE_CONFIG,HammerGestureConfig,DomSanitizer,SafeHtml,SafeResourceUrl,SafeScript,SafeStyle,SafeUrl,SafeValue,VERSION,ɵBROWSER_SANITIZATION_PROVIDERS,ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,ɵinitDomAdapter,ɵBrowserDomAdapter,ɵBrowserPlatformLocation,ɵTRANSITION_ID,ɵBrowserGetTestability,ɵELEMENT_PROBE_PROVIDERS,ɵDomAdapter,ɵgetDOM,ɵsetRootDomAdapter,ɵDomRendererFactory2,ɵNAMESPACE_URIS,ɵflattenStyles,ɵshimContentAttribute,ɵshimHostAttribute,ɵDomEventsPlugin,ɵHammerGesturesPlugin,ɵKeyEventsPlugin,ɵDomSharedStylesHost,ɵSharedStylesHost} from './src/platform-browser';\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BrowserModule, platformBrowser} from './browser';\nexport {Meta, MetaDefinition} from './browser/meta';\nexport {Title} from './browser/title';\nexport {disableDebugTools, enableDebugTools} from './browser/tools/tools';\nexport {By} from './dom/debug/by';\nexport {NgProbeToken} from './dom/debug/ng_probe';\nexport {DOCUMENT} from './dom/dom_tokens';\nexport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nexport {HAMMER_GESTURE_CONFIG, HammerGestureConfig} from './dom/events/hammer_gestures';\nexport {DomSanitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl, SafeValue} from './security/dom_sanitization_service';\nexport {ɵBROWSER_SANITIZATION_PROVIDERS,ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,ɵinitDomAdapter,ɵBrowserDomAdapter,ɵBrowserPlatformLocation,ɵTRANSITION_ID,ɵBrowserGetTestability,ɵELEMENT_PROBE_PROVIDERS,ɵDomAdapter,ɵgetDOM,ɵsetRootDomAdapter,ɵDomRendererFactory2,ɵNAMESPACE_URIS,ɵflattenStyles,ɵshimContentAttribute,ɵshimHostAttribute,ɵDomEventsPlugin,ɵHammerGesturesPlugin,ɵKeyEventsPlugin,ɵDomSharedStylesHost,ɵSharedStylesHost} from './private_export';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BROWSER_SANITIZATION_PROVIDERS as ɵBROWSER_SANITIZATION_PROVIDERS, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, initDomAdapter as ɵinitDomAdapter} from './browser';\nexport {BrowserDomAdapter as ɵBrowserDomAdapter} from './browser/browser_adapter';\nexport {BrowserPlatformLocation as ɵBrowserPlatformLocation} from './browser/location/browser_platform_location';\nexport {TRANSITION_ID as ɵTRANSITION_ID} from './browser/server-transition';\nexport {BrowserGetTestability as ɵBrowserGetTestability} from './browser/testability';\nexport {ELEMENT_PROBE_PROVIDERS as ɵELEMENT_PROBE_PROVIDERS} from './dom/debug/ng_probe';\nexport {DomAdapter as ɵDomAdapter, getDOM as ɵgetDOM, setRootDomAdapter as ɵsetRootDomAdapter} from './dom/dom_adapter';\nexport {DomRendererFactory2 as ɵDomRendererFactory2, NAMESPACE_URIS as ɵNAMESPACE_URIS, flattenStyles as ɵflattenStyles, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute} from './dom/dom_renderer';\nexport {DomEventsPlugin as ɵDomEventsPlugin} from './dom/events/dom_events';\nexport {HammerGesturesPlugin as ɵHammerGesturesPlugin} from './dom/events/hammer_gestures';\nexport {KeyEventsPlugin as ɵKeyEventsPlugin} from './dom/events/key_events';\nexport {DomSharedStylesHost as ɵDomSharedStylesHost, SharedStylesHost as ɵSharedStylesHost} from './dom/shared_styles_host';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {DebugElement, Predicate, Type} from '@angular/core';\nimport {getDOM} from '../../dom/dom_adapter';\n/**\n * Predicates for use with {\\@link DebugElement}'s query functions.\n * \n * \\@experimental All debugging apis are currently experimental.\n */\nexport class By {\n/**\n * Match all elements.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n * @return {?}\n */\nstatic all(): Predicate<DebugElement> { return (debugElement) => true; }\n/**\n * Match elements by the given CSS selector.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n * @param {?} selector\n * @return {?}\n */\nstatic css(selector: string): Predicate<DebugElement> {\n    return (debugElement) => {\n      return debugElement.nativeElement != null ?\n          getDOM().elementMatches(debugElement.nativeElement, selector) :\n          false;\n    };\n  }\n/**\n * Match elements that have the given directive present.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n * @param {?} type\n * @return {?}\n */\nstatic directive(type: Type<any>): Predicate<DebugElement> {\n    return (debugElement) => /** @type {?} */(( debugElement.providerTokens)).indexOf(type) !== -1;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentRef} from '@angular/core';\nimport {exportNgVar} from '../../dom/util';\nimport {AngularProfiler} from './common_tools';\n\nconst /** @type {?} */ PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n * \n * Usage:\n * \n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n * \n * \\@experimental All debugging apis are currently experimental.\n * @template T\n * @param {?} ref\n * @return {?}\n */\nexport function enableDebugTools<T>(ref: ComponentRef<T>): ComponentRef<T> {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n * \n * \\@experimental All debugging apis are currently experimental.\n * @return {?}\n */\nexport function disableDebugTools(): void {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ApplicationRef, ComponentRef} from '@angular/core';\nimport {getDOM} from '../../dom/dom_adapter';\nimport {window} from './browser';\nexport class ChangeDetectionPerfRecord {\n/**\n * @param {?} msPerTick\n * @param {?} numTicks\n */\nconstructor(public msPerTick: number,\npublic numTicks: number) {}\n}\n\nfunction ChangeDetectionPerfRecord_tsickle_Closure_declarations() {\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.msPerTick;\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.numTicks;\n}\n\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nexport class AngularProfiler {\n  appRef: ApplicationRef;\n/**\n * @param {?} ref\n */\nconstructor(ref: ComponentRef<any>) { this.appRef = ref.injector.get(ApplicationRef); }\n/**\n * Exercises change detection in a loop and then prints the average amount of\n * time in milliseconds how long a single round of change detection takes for\n * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n * of 500 milliseconds.\n * \n * Optionally, a user may pass a `config` parameter containing a map of\n * options. Supported options are:\n * \n * `record` (boolean) - causes the profiler to record a CPU profile while\n * it exercises the change detector. Example:\n * \n * ```\n * ng.profiler.timeChangeDetection({record: true})\n * ```\n * @param {?} config\n * @return {?}\n */\ntimeChangeDetection(config: any): ChangeDetectionPerfRecord {\n    const /** @type {?} */ record = config && config['record'];\n    const /** @type {?} */ profileName = 'Change Detection';\n    // Profiler is not available in Android browsers, nor in IE 9 without dev tools opened\n    const /** @type {?} */ isProfilerAvailable = window.console.profile != null;\n    if (record && isProfilerAvailable) {\n      window.console.profile(profileName);\n    }\n    const /** @type {?} */ start = getDOM().performanceNow();\n    let /** @type {?} */ numTicks = 0;\n    while (numTicks < 5 || (getDOM().performanceNow() - start) < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const /** @type {?} */ end = getDOM().performanceNow();\n    if (record && isProfilerAvailable) {\n      // need to cast to <any> because type checker thinks there's no argument\n      // while in fact there is:\n      //\n      // https://developer.mozilla.org/en-US/docs/Web/API/Console/profileEnd\n      ( /** @type {?} */((<any>window.console.profileEnd)))(profileName);\n    }\n    const /** @type {?} */ msPerTick = (end - start) / numTicks;\n    window.console.log(`ran ${numTicks} change detection cycles`);\n    window.console.log(`${msPerTick.toFixed(2)} ms per check`);\n\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\n\nfunction AngularProfiler_tsickle_Closure_declarations() {\n/** @type {?} */\nAngularProfiler.prototype.appRef;\n}\n\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst win = typeof window !== 'undefined' && window || <any>{};\nexport {win as window};\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {CommonModule, PlatformLocation, ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {APP_ID, ApplicationModule, ErrorHandler, ModuleWithProviders, NgModule, Optional, PLATFORM_ID, PLATFORM_INITIALIZER, PlatformRef, Provider, RendererFactory2, RootRenderer, <PERSON>itizer, SkipSelf, Testability, createPlatformFactory, platformCore} from '@angular/core';\n\nimport {BrowserDomAdapter} from './browser/browser_adapter';\nimport {BrowserPlatformLocation} from './browser/location/browser_platform_location';\nimport {Meta} from './browser/meta';\nimport {SERVER_TRANSITION_PROVIDERS, TRANSITION_ID} from './browser/server-transition';\nimport {BrowserGetTestability} from './browser/testability';\nimport {Title} from './browser/title';\nimport {ELEMENT_PROBE_PROVIDERS} from './dom/debug/ng_probe';\nimport {getDOM} from './dom/dom_adapter';\nimport {DomRendererFactory2} from './dom/dom_renderer';\nimport {DOCUMENT} from './dom/dom_tokens';\nimport {DomEventsPlugin} from './dom/events/dom_events';\nimport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nimport {HAMMER_GESTURE_CONFIG, HammerGestureConfig, HammerGesturesPlugin} from './dom/events/hammer_gestures';\nimport {KeyEventsPlugin} from './dom/events/key_events';\nimport {DomSharedStylesHost, SharedStylesHost} from './dom/shared_styles_host';\nimport {DomSanitizer, DomSanitizerImpl} from './security/dom_sanitization_service';\n\nexport const /** @type {?} */ INTERNAL_BROWSER_PLATFORM_PROVIDERS: Provider[] = [\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n  {provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true},\n  {provide: PlatformLocation, useClass: BrowserPlatformLocation},\n  {provide: DOCUMENT, useFactory: _document, deps: []},\n];\n/**\n * \\@security Replacing built-in sanitization providers exposes the application to XSS risks.\n * Attacker-controlled data introduced by an unsanitized provider could expose your\n * application to XSS risks. For more detail, see the [Security Guide](http://g.co/ng/security).\n * \\@experimental\n */\nexport const BROWSER_SANITIZATION_PROVIDERS: Array<any> = [\n  {provide: Sanitizer, useExisting: DomSanitizer},\n  {provide: DomSanitizer, useClass: DomSanitizerImpl},\n];\n/**\n * \\@stable\n */\nexport const platformBrowser: (extraProviders?: Provider[]) => PlatformRef =\n    createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * @return {?}\n */\nexport function initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserGetTestability.init();\n}\n/**\n * @return {?}\n */\nexport function errorHandler(): ErrorHandler {\n  return new ErrorHandler();\n}\n/**\n * @return {?}\n */\nexport function _document(): any {\n  return document;\n}\n/**\n * The ng module for the browser.\n * \n * \\@stable\n */\nexport class BrowserModule {\n/**\n * @param {?} parentModule\n */\nconstructor(  parentModule: BrowserModule) {\n    if (parentModule) {\n      throw new Error(\n          `BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n    }\n  }\n/**\n * Configures a browser-based application to transition from a server-rendered app, if\n * one is present on the page. The specified parameters must include an application id,\n * which must match between the client and server applications.\n * \n * \\@experimental\n * @param {?} params\n * @return {?}\n */\nstatic withServerTransition(params: {appId: string}): ModuleWithProviders {\n    return {\n      ngModule: BrowserModule,\n      providers: [\n        {provide: APP_ID, useValue: params.appId},\n        {provide: TRANSITION_ID, useExisting: APP_ID},\n        SERVER_TRANSITION_PROVIDERS,\n      ],\n    };\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    BROWSER_SANITIZATION_PROVIDERS,\n    {provide: ErrorHandler, useFactory: errorHandler, deps: []},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: DomEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: HammerGesturesPlugin, multi: true},\n    {provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig},\n    DomRendererFactory2,\n    {provide: RendererFactory2, useExisting: DomRendererFactory2},\n    {provide: SharedStylesHost, useExisting: DomSharedStylesHost},\n    DomSharedStylesHost,\n    Testability,\n    EventManager,\n    ELEMENT_PROBE_PROVIDERS,\n    Meta,\n    Title,\n  ],\n  exports: [CommonModule, ApplicationModule]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserModule, decorators: [{ type: Optional }, { type: SkipSelf }, ]},\n];\n}\n\nfunction BrowserModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Sanitizer, SecurityContext} from '@angular/core';\n\nimport {DOCUMENT} from '../dom/dom_tokens';\n\nimport {sanitizeHtml} from './html_sanitizer';\nimport {sanitizeStyle} from './style_sanitizer';\nimport {sanitizeUrl} from './url_sanitizer';\n\nexport {SecurityContext};\n\n\n\n/**\n * Marker interface for a value that's safe to use in a particular context.\n *\n * @stable\n */\nexport interface SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as HTML.\n *\n * @stable\n */\nexport interface SafeHtml extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as style (CSS).\n *\n * @stable\n */\nexport interface SafeStyle extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as JavaScript.\n *\n * @stable\n */\nexport interface SafeScript extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL linking to a document.\n *\n * @stable\n */\nexport interface SafeUrl extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL to load executable code from.\n *\n * @stable\n */\nexport interface SafeResourceUrl extends SafeValue {}\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n * \n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n * \n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n * \n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n * \n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n * \n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n * \n * \\@security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](http://g.co/ng/security).\n * \n * \\@stable\n * @abstract\n */\nexport abstract class DomSanitizer implements Sanitizer {\n/**\n * Sanitizes a value for use in the given SecurityContext.\n * \n * If value is trusted for the context, this method will unwrap the contained safe value and use\n * it directly. Otherwise, value will be sanitized to be safe in the given context, for example\n * by replacing URLs that have an unsafe protocol part (such as `javascript:`). The implementation\n * is responsible to make sure that the value can definitely be safely used in the given context.\n * @abstract\n * @param {?} context\n * @param {?} value\n * @return {?}\n */\nsanitize(context: SecurityContext, value: SafeValue|string|null) {}\n/**\n * Bypass security and trust the given value to be safe HTML. Only use this when the bound HTML\n * is unsafe (e.g. contains `<script>` tags) and the code should be executed. The sanitizer will\n * leave safe HTML intact, so in most situations this method should not be used.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string) {}\n/**\n * Bypass security and trust the given value to be safe style value (CSS).\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string) {}\n/**\n * Bypass security and trust the given value to be safe JavaScript.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe style URL, i.e. a value that can be used\n * in hyperlinks or `<img src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe resource URL, i.e. a location that may\n * be used to load executable code from, like `<script src>`, or `<iframe src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string) {}\n}\nexport class DomSanitizerImpl extends DomSanitizer {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { super(); }\n/**\n * @param {?} ctx\n * @param {?} value\n * @return {?}\n */\nsanitize(ctx: SecurityContext, value: SafeValue|string|null): string|null {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return /** @type {?} */(( value as string));\n      case SecurityContext.HTML:\n        if (value instanceof SafeHtmlImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'HTML');\n        return sanitizeHtml(this._doc, String(value));\n      case SecurityContext.STYLE:\n        if (value instanceof SafeStyleImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Style');\n        return sanitizeStyle( /** @type {?} */((value as string)));\n      case SecurityContext.SCRIPT:\n        if (value instanceof SafeScriptImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Script');\n        throw new Error('unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (value instanceof SafeResourceUrlImpl || value instanceof SafeUrlImpl) {\n          // Allow resource URLs in URL contexts, they are strictly more trusted.\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'URL');\n        return sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (value instanceof SafeResourceUrlImpl) {\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'ResourceURL');\n        throw new Error(\n            'unsafe value used in a resource URL context (see http://g.co/ng/security#xss)');\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @param {?} expectedType\n * @return {?}\n */\nprivate checkNotSafeValue(value: any, expectedType: string) {\n    if (value instanceof SafeValueImpl) {\n      throw new Error(\n          `Required a safe ${expectedType}, got a ${value.getTypeName()} ` +\n          `(see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string): SafeHtml { return new SafeHtmlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string): SafeStyle { return new SafeStyleImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string): SafeScript { return new SafeScriptImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string): SafeUrl { return new SafeUrlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {\n    return new SafeResourceUrlImpl(value);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSanitizerImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSanitizerImpl.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSanitizerImpl.ctorParameters;\n/** @type {?} */\nDomSanitizerImpl.prototype._doc;\n}\n\n/**\n * @abstract\n */\nabstract class SafeValueImpl implements SafeValue {\n/**\n * @param {?} changingThisBreaksApplicationSecurity\n */\nconstructor(public changingThisBreaksApplicationSecurity: string) {\n    // empty\n  }\n/**\n * @abstract\n * @return {?}\n */\ngetTypeName() {}\n/**\n * @return {?}\n */\ntoString() {\n    return `SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity}` +\n        ` (see http://g.co/ng/security#xss)`;\n  }\n}\n\nfunction SafeValueImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nSafeValueImpl.prototype.changingThisBreaksApplicationSecurity;\n}\n\nclass SafeHtmlImpl extends SafeValueImpl implements SafeHtml {\n/**\n * @return {?}\n */\ngetTypeName() { return 'HTML'; }\n}\nclass SafeStyleImpl extends SafeValueImpl implements SafeStyle {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Style'; }\n}\nclass SafeScriptImpl extends SafeValueImpl implements SafeScript {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Script'; }\n}\nclass SafeUrlImpl extends SafeValueImpl implements SafeUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'URL'; }\n}\nclass SafeResourceUrlImpl extends SafeValueImpl implements SafeResourceUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'ResourceURL'; }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeUrl} from './url_sanitizer';\n/**\n * Regular expression for safe style values.\n * \n * Quotes (\" and ') are allowed, but a check must be done elsewhere to ensure they're balanced.\n * \n * ',' allows multiple values to be assigned to the same property (e.g. background-attachment or\n * font-family) and hence could allow multiple values to get injected, but that should pose no risk\n * of XSS.\n * \n * The function expression checks only for XSS safety, not for CSS validity.\n * \n * This regular expression was taken from the Closure sanitization library, and augmented for\n * transformation values.\n */\nconst VALUES = '[-,.\"\\'%_!# a-zA-Z0-9]+';\nconst /** @type {?} */ TRANSFORMATION_FNS = '(?:matrix|translate|scale|rotate|skew|perspective)(?:X|Y|3d)?';\nconst /** @type {?} */ COLOR_FNS = '(?:rgb|hsl)a?';\nconst /** @type {?} */ GRADIENTS = '(?:repeating-)?(?:linear|radial)-gradient';\nconst /** @type {?} */ CSS3_FNS = '(?:calc|attr)';\nconst /** @type {?} */ FN_ARGS = '\\\\([-0-9.%, #a-zA-Z]+\\\\)';\nconst /** @type {?} */ SAFE_STYLE_VALUE = new RegExp(\n    `^(${VALUES}|` +\n        `(?:${TRANSFORMATION_FNS}|${COLOR_FNS}|${GRADIENTS}|${CSS3_FNS})` +\n        `${FN_ARGS})$`,\n    'g');\n/**\n * Matches a `url(...)` value with an arbitrary argument as long as it does\n * not contain parentheses.\n * \n * The URL value still needs to be sanitized separately.\n * \n * `url(...)` values are a very common use case, e.g. for `background-image`. With carefully crafted\n * CSS style rules, it is possible to construct an information leak with `url` values in CSS, e.g.\n * by observing whether scroll bars are displayed, or character ranges used by a font face\n * definition.\n * \n * Angular only allows binding CSS values (as opposed to entire CSS rules), so it is unlikely that\n * binding a URL value without further cooperation from the page will cause an information leak, and\n * if so, it is just a leak, not a full blown XSS vulnerability.\n * \n * Given the common use case, low likelihood of attack vector, and low impact of an attack, this\n * code is permissive and allows URLs that sanitize otherwise.\n */\nconst URL_RE = /^url\\(([^)]+)\\)$/;\n/**\n * Checks that quotes (\" and ') are properly balanced inside a string. Assumes\n * that neither escape (\\) nor any other character that could result in\n * breaking out of a string parsing context are allowed;\n * see http://www.w3.org/TR/css3-syntax/#string-token-diagram.\n * \n * This code was taken from the Closure sanitization library.\n * @param {?} value\n * @return {?}\n */\nfunction hasBalancedQuotes(value: string) {\n  let /** @type {?} */ outsideSingle = true;\n  let /** @type {?} */ outsideDouble = true;\n  for (let /** @type {?} */ i = 0; i < value.length; i++) {\n    const /** @type {?} */ c = value.charAt(i);\n    if (c === '\\'' && outsideDouble) {\n      outsideSingle = !outsideSingle;\n    } else if (c === '\"' && outsideSingle) {\n      outsideDouble = !outsideDouble;\n    }\n  }\n  return outsideSingle && outsideDouble;\n}\n/**\n * Sanitizes the given untrusted CSS style property value (i.e. not an entire object, just a single\n * value) and returns a value that is safe to use in a browser environment.\n * @param {?} value\n * @return {?}\n */\nexport function sanitizeStyle(value: string): string {\n  value = String(value).trim();  // Make sure it's actually a string.\n  if (!value) return '';\n\n  // Single url(...) values are supported, but only for URLs that sanitize cleanly. See above for\n  // reasoning behind this.\n  const /** @type {?} */ urlMatch = value.match(URL_RE);\n  if ((urlMatch && sanitizeUrl(urlMatch[1]) === urlMatch[1]) ||\n      value.match(SAFE_STYLE_VALUE) && hasBalancedQuotes(value)) {\n    return value;  // Safe style values.\n  }\n\n  if (isDevMode()) {\n    getDOM().log(\n        `WARNING: sanitizing unsafe style value ${value} (see http://g.co/ng/security#xss).`);\n  }\n\n  return 'unsafe';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeSrcset, sanitizeUrl} from './url_sanitizer';\n/**\n * A <body> element that can be safely used to parse untrusted HTML. Lazily initialized below.\n */\nlet inertElement: HTMLElement|null = null;\n/**\n * Lazily initialized to make sure the DOM adapter gets set before use.\n */\nlet DOM: DomAdapter = null !;\n/**\n * Returns an HTML element that is guaranteed to not execute code when creating elements in it.\n * @return {?}\n */\nfunction getInertElement() {\n  if (inertElement) return inertElement;\n  DOM = getDOM();\n\n  // Prefer using <template> element if supported.\n  const /** @type {?} */ templateEl = DOM.createElement('template');\n  if ('content' in templateEl) return templateEl;\n\n  const /** @type {?} */ doc = DOM.createHtmlDocument();\n  inertElement = DOM.querySelector(doc, 'body');\n  if (inertElement == null) {\n    // usually there should be only one body element in the document, but IE doesn't have any, so we\n    // need to create one.\n    const /** @type {?} */ html = DOM.createElement('html', doc);\n    inertElement = DOM.createElement('body', doc);\n    DOM.appendChild(html, inertElement);\n    DOM.appendChild(doc, html);\n  }\n  return inertElement;\n}\n/**\n * @param {?} tags\n * @return {?}\n */\nfunction tagSet(tags: string): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ t of tags.split(',')) res[t] = true;\n  return res;\n}\n/**\n * @param {...?} sets\n * @return {?}\n */\nfunction merge(...sets: {[k: string]: boolean}[]): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ s of sets) {\n    for (const /** @type {?} */ v in s) {\n      if (s.hasOwnProperty(v)) res[v] = true;\n    }\n  }\n  return res;\n}\n\n// Good source of info about elements and attributes\n// http://dev.w3.org/html5/spec/Overview.html#semantics\n// http://simon.html5.org/html-elements\n\n// Safe Void Elements - HTML5\n// http://dev.w3.org/html5/spec/Overview.html#void-elements\nconst /** @type {?} */ VOID_ELEMENTS = tagSet('area,br,col,hr,img,wbr');\n\n// Elements that you can, intentionally, leave open (and which close themselves)\n// http://dev.w3.org/html5/spec/Overview.html#optional-tags\nconst /** @type {?} */ OPTIONAL_END_TAG_BLOCK_ELEMENTS = tagSet('colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr');\nconst /** @type {?} */ OPTIONAL_END_TAG_INLINE_ELEMENTS = tagSet('rp,rt');\nconst /** @type {?} */ OPTIONAL_END_TAG_ELEMENTS =\n    merge(OPTIONAL_END_TAG_INLINE_ELEMENTS, OPTIONAL_END_TAG_BLOCK_ELEMENTS);\n\n// Safe Block Elements - HTML5\nconst /** @type {?} */ BLOCK_ELEMENTS = merge(\n    OPTIONAL_END_TAG_BLOCK_ELEMENTS,\n    tagSet(\n        'address,article,' +\n        'aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,' +\n        'h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul'));\n\n// Inline Elements - HTML5\nconst /** @type {?} */ INLINE_ELEMENTS = merge(\n    OPTIONAL_END_TAG_INLINE_ELEMENTS,\n    tagSet(\n        'a,abbr,acronym,audio,b,' +\n        'bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,' +\n        'samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video'));\n\nconst /** @type {?} */ VALID_ELEMENTS =\n    merge(VOID_ELEMENTS, BLOCK_ELEMENTS, INLINE_ELEMENTS, OPTIONAL_END_TAG_ELEMENTS);\n\n// Attributes that have href and hence need to be sanitized\nconst /** @type {?} */ URI_ATTRS = tagSet('background,cite,href,itemtype,longdesc,poster,src,xlink:href');\n\n// Attributes that have special href set hence need to be sanitized\nconst /** @type {?} */ SRCSET_ATTRS = tagSet('srcset');\n\nconst /** @type {?} */ HTML_ATTRS = tagSet(\n    'abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,' +\n    'compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,' +\n    'ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,' +\n    'scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,' +\n    'valign,value,vspace,width');\n\n// NB: This currently conciously doesn't support SVG. SVG sanitization has had several security\n// issues in the past, so it seems safer to leave it out if possible. If support for binding SVG via\n// innerHTML is required, SVG attributes should be added here.\n\n// NB: Sanitization does not allow <form> elements or other active elements (<button> etc). Those\n// can be sanitized, but they increase security surface area without a legitimate use case, so they\n// are left out here.\n\nconst /** @type {?} */ VALID_ATTRS = merge(URI_ATTRS, SRCSET_ATTRS, HTML_ATTRS);\n/**\n * SanitizingHtmlSerializer serializes a DOM fragment, stripping out any unsafe elements and unsafe\n * attributes.\n */\nclass SanitizingHtmlSerializer {\npublic sanitizedSomething = false;\nprivate buf: string[] = [];\n/**\n * @param {?} el\n * @return {?}\n */\nsanitizeChildren(el: Element): string {\n    // This cannot use a TreeWalker, as it has to run on Angular's various DOM adapters.\n    // However this code never accesses properties off of `document` before deleting its contents\n    // again, so it shouldn't be vulnerable to DOM clobbering.\n    let /** @type {?} */ current: Node = /** @type {?} */(( el.firstChild));\n    while (current) {\n      if (DOM.isElementNode(current)) {\n        this.startElement( /** @type {?} */((current as Element)));\n      } else if (DOM.isTextNode(current)) {\n        this.chars( /** @type {?} */((DOM.nodeValue(current))));\n      } else {\n        // Strip non-element, non-text nodes.\n        this.sanitizedSomething = true;\n      }\n      if (DOM.firstChild(current)) {\n        current = /** @type {?} */(( DOM.firstChild(current)));\n        continue;\n      }\n      while (current) {\n        // Leaving the element. Walk up and to the right, closing tags as we go.\n        if (DOM.isElementNode(current)) {\n          this.endElement( /** @type {?} */((current as Element)));\n        }\n\n        let /** @type {?} */ next = checkClobberedElement(current, /** @type {?} */(( DOM.nextSibling(current))));\n\n        if (next) {\n          current = next;\n          break;\n        }\n\n        current = checkClobberedElement(current, /** @type {?} */(( DOM.parentElement(current))));\n      }\n    }\n    return this.buf.join('');\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprivate startElement(element: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(element).toLowerCase();\n    if (!VALID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.sanitizedSomething = true;\n      return;\n    }\n    this.buf.push('<');\n    this.buf.push(tagName);\n    DOM.attributeMap(element).forEach((value: string, attrName: string) => {\n      const /** @type {?} */ lower = attrName.toLowerCase();\n      if (!VALID_ATTRS.hasOwnProperty(lower)) {\n        this.sanitizedSomething = true;\n        return;\n      }\n      // TODO(martinprobst): Special case image URIs for data:image/...\n      if (URI_ATTRS[lower]) value = sanitizeUrl(value);\n      if (SRCSET_ATTRS[lower]) value = sanitizeSrcset(value);\n      this.buf.push(' ');\n      this.buf.push(attrName);\n      this.buf.push('=\"');\n      this.buf.push(encodeEntities(value));\n      this.buf.push('\"');\n    });\n    this.buf.push('>');\n  }\n/**\n * @param {?} current\n * @return {?}\n */\nprivate endElement(current: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(current).toLowerCase();\n    if (VALID_ELEMENTS.hasOwnProperty(tagName) && !VOID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.buf.push('</');\n      this.buf.push(tagName);\n      this.buf.push('>');\n    }\n  }\n/**\n * @param {?} chars\n * @return {?}\n */\nprivate chars(chars: string) { this.buf.push(encodeEntities(chars)); }\n}\n\nfunction SanitizingHtmlSerializer_tsickle_Closure_declarations() {\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.sanitizedSomething;\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.buf;\n}\n\n/**\n * @param {?} node\n * @param {?} nextNode\n * @return {?}\n */\nfunction checkClobberedElement(node: Node, nextNode: Node): Node {\n  if (nextNode && DOM.contains(node, nextNode)) {\n    throw new Error(\n        `Failed to sanitize html because the element is clobbered: ${DOM.getOuterHTML(node)}`);\n  }\n  return nextNode;\n}\n\n// Regular Expressions for parsing tags and attributes\nconst /** @type {?} */ SURROGATE_PAIR_REGEXP = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n// ! to ~ is the ASCII range.\nconst /** @type {?} */ NON_ALPHANUMERIC_REGEXP = /([^\\#-~ |!])/g;\n/**\n * Escapes all potentially dangerous characters, so that the\n * resulting string can be safely inserted into attribute or\n * element text.\n * @param {?} value\n * @return {?}\n */\nfunction encodeEntities(value: string) {\n  return value.replace(/&/g, '&amp;')\n      .replace(\n          SURROGATE_PAIR_REGEXP,\n          function(match: string) {\n            const /** @type {?} */ hi = match.charCodeAt(0);\n            const /** @type {?} */ low = match.charCodeAt(1);\n            return '&#' + (((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000) + ';';\n          })\n      .replace(\n          NON_ALPHANUMERIC_REGEXP,\n          function(match: string) { return '&#' + match.charCodeAt(0) + ';'; })\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n}\n/**\n * When IE9-11 comes across an unknown namespaced attribute e.g. 'xlink:foo' it adds 'xmlns:ns1'\n * attribute to declare ns1 namespace and prefixes the attribute with 'ns1' (e.g. 'ns1:xlink:foo').\n * \n * This is undesirable since we don't want to allow any of these custom attributes. This method\n * strips them all.\n * @param {?} el\n * @return {?}\n */\nfunction stripCustomNsAttrs(el: Element) {\n  DOM.attributeMap(el).forEach((_, attrName) => {\n    if (attrName === 'xmlns:ns1' || attrName.indexOf('ns1:') === 0) {\n      DOM.removeAttribute(el, attrName);\n    }\n  });\n  for (const /** @type {?} */ n of DOM.childNodesAsList(el)) {\n    if (DOM.isElementNode(n)) stripCustomNsAttrs( /** @type {?} */((n as Element)));\n  }\n}\n/**\n * Sanitizes the given unsafe, untrusted HTML fragment, and returns HTML text that is safe to add to\n * the DOM in a browser environment.\n * @param {?} defaultDoc\n * @param {?} unsafeHtmlInput\n * @return {?}\n */\nexport function sanitizeHtml(defaultDoc: any, unsafeHtmlInput: string): string {\n  try {\n    const /** @type {?} */ containerEl = getInertElement();\n    // Make sure unsafeHtml is actually a string (TypeScript types are not enforced at runtime).\n    let /** @type {?} */ unsafeHtml = unsafeHtmlInput ? String(unsafeHtmlInput) : '';\n\n    // mXSS protection. Repeatedly parse the document to make sure it stabilizes, so that a browser\n    // trying to auto-correct incorrect HTML cannot cause formerly inert HTML to become dangerous.\n    let /** @type {?} */ mXSSAttempts = 5;\n    let /** @type {?} */ parsedHtml = unsafeHtml;\n\n    do {\n      if (mXSSAttempts === 0) {\n        throw new Error('Failed to sanitize html because the input is unstable');\n      }\n      mXSSAttempts--;\n\n      unsafeHtml = parsedHtml;\n      DOM.setInnerHTML(containerEl, unsafeHtml);\n      if (defaultDoc.documentMode) {\n        // strip custom-namespaced attributes on IE<=11\n        stripCustomNsAttrs(containerEl);\n      }\n      parsedHtml = DOM.getInnerHTML(containerEl);\n    } while (unsafeHtml !== parsedHtml);\n\n    const /** @type {?} */ sanitizer = new SanitizingHtmlSerializer();\n    const /** @type {?} */ safeHtml = sanitizer.sanitizeChildren(DOM.getTemplateContent(containerEl) || containerEl);\n\n    // Clear out the body element.\n    const /** @type {?} */ parent = DOM.getTemplateContent(containerEl) || containerEl;\n    for (const /** @type {?} */ child of DOM.childNodesAsList(parent)) {\n      DOM.removeChild(parent, child);\n    }\n\n    if (isDevMode() && sanitizer.sanitizedSomething) {\n      DOM.log('WARNING: sanitizing HTML stripped some content (see http://g.co/ng/security#xss).');\n    }\n\n    return safeHtml;\n  } catch ( /** @type {?} */e) {\n    // In case anything goes wrong, clear out inertElement to reset the entire DOM structure.\n    inertElement = null;\n    throw e;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n * \n * This regular expression matches a subset of URLs that will not cause script\n * execution if used in URL context within a HTML document. Specifically, this\n * regular expression matches if (comment from here on and regex copied from\n * Soy's EscapingConventions):\n * (1) Either a protocol in a whitelist (http, https, mailto or ftp).\n * (2) or no protocol.  A protocol must be followed by a colon. The below\n *     allows that by allowing colons only after one of the characters [/?#].\n *     A colon after a hash (#) must be in the fragment.\n *     Otherwise, a colon after a (?) must be in a query.\n *     Otherwise, a colon after a single solidus (/) must be in a path.\n *     Otherwise, a colon after a double solidus (//) must be in the authority\n *     (before port).\n * \n * The pattern disallows &, used in HTML entity declarations before\n * one of the characters in [/?#]. This disallows HTML entities used in the\n * protocol name, which should never happen, e.g. \"h&#116;tp\" for \"http\".\n * It also disallows HTML entities in the first path part of a relative path,\n * e.g. \"foo&lt;bar/baz\".  Our existing escaping functions should not produce\n * that. More importantly, it disallows masking of a colon,\n * e.g. \"javascript&#58;...\".\n * \n * This regular expression was taken from the Closure sanitization library.\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n\n/* A pattern that matches safe srcset values */\nconst /** @type {?} */ SAFE_SRCSET_PATTERN = /^(?:(?:https?|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n */\nconst DATA_URL_PATTERN =\n    /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\\/]+=*$/i;\n/**\n * @param {?} url\n * @return {?}\n */\nexport function sanitizeUrl(url: string): string {\n  url = String(url);\n  if (url.match(SAFE_URL_PATTERN) || url.match(DATA_URL_PATTERN)) return url;\n\n  if (isDevMode()) {\n    getDOM().log(`WARNING: sanitizing unsafe URL value ${url} (see http://g.co/ng/security#xss)`);\n  }\n\n  return 'unsafe:' + url;\n}\n/**\n * @param {?} srcset\n * @return {?}\n */\nexport function sanitizeSrcset(srcset: string): string {\n  srcset = String(srcset);\n  return srcset.split(',').map((srcset) => sanitizeUrl(srcset.trim())).join(', ');\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst /** @type {?} */ MODIFIER_KEY_GETTERS: {[key: string]: (event: KeyboardEvent) => boolean} = {\n  'alt': (event: KeyboardEvent) => event.altKey,\n  'control': (event: KeyboardEvent) => event.ctrlKey,\n  'meta': (event: KeyboardEvent) => event.metaKey,\n  'shift': (event: KeyboardEvent) => event.shiftKey\n};\n/**\n * \\@experimental\n */\nexport class KeyEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return KeyEventsPlugin.parseEventName(eventName) != null; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ parsedEvent = /** @type {?} */(( KeyEventsPlugin.parseEventName(eventName)));\n\n    const /** @type {?} */ outsideHandler =\n        KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n\n    return this.manager.getZone().runOutsideAngular(() => {\n      return getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nstatic parseEventName(eventName: string): {[key: string]: string}|null {\n    const /** @type {?} */ parts: string[] = eventName.toLowerCase().split('.');\n\n    const /** @type {?} */ domEventName = parts.shift();\n    if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const /** @type {?} */ key = KeyEventsPlugin._normalizeKey( /** @type {?} */((parts.pop())));\n\n    let /** @type {?} */ fullKey = '';\n    MODIFIER_KEYS.forEach(modifierName => {\n      const /** @type {?} */ index: number = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n\n    const /** @type {?} */ result: {[k: string]: string} = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n/**\n * @param {?} event\n * @return {?}\n */\nstatic getEventFullKey(event: KeyboardEvent): string {\n    let /** @type {?} */ fullKey = '';\n    let /** @type {?} */ key = getDOM().getEventKey(event);\n    key = key.toLowerCase();\n    if (key === ' ') {\n      key = 'space';  // for readability\n    } else if (key === '.') {\n      key = 'dot';  // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName != key) {\n        const /** @type {?} */ modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          fullKey += modifierName + '.';\n        }\n      }\n    });\n    fullKey += key;\n    return fullKey;\n  }\n/**\n * @param {?} fullKey\n * @param {?} handler\n * @param {?} zone\n * @return {?}\n */\nstatic eventCallback(fullKey: any, handler: Function, zone: NgZone): Function {\n    return (event: any /** TODO #9100 */) => {\n      if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n/**\n * \\@internal\n * @param {?} keyName\n * @return {?}\n */\nstatic _normalizeKey(keyName: string): string {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction KeyEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nKeyEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n};\n/**\n * A DI token that you can use to provide{\\@link HammerGestureConfig} to Angular. Use it to configure\n * Hammer gestures.\n * \n * \\@experimental\n */\nexport const HAMMER_GESTURE_CONFIG = new InjectionToken<HammerGestureConfig>('HammerGestureConfig');\n\nexport interface HammerInstance {\n  on(eventName: string, callback?: Function): void;\n  off(eventName: string, callback?: Function): void;\n}\n/**\n * \\@experimental\n */\nexport class HammerGestureConfig {\n  events: string[] = [];\n\n  overrides: {[key: string]: Object} = {};\n/**\n * @param {?} element\n * @return {?}\n */\nbuildHammer(element: HTMLElement): HammerInstance {\n    const /** @type {?} */ mc = new Hammer(element);\n\n    mc.get('pinch').set({enable: true});\n    mc.get('rotate').set({enable: true});\n\n    for (const /** @type {?} */ eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HammerGestureConfig_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGestureConfig.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGestureConfig.ctorParameters;\n/** @type {?} */\nHammerGestureConfig.prototype.events;\n/** @type {?} */\nHammerGestureConfig.prototype.overrides;\n}\n\nexport class HammerGesturesPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n * @param {?} _config\n */\nconstructor(\n       doc: any,\nprivate _config: HammerGestureConfig) {\n    super(doc);\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!( /** @type {?} */((window as any))).Hammer) {\n      throw new Error(`Hammer.js is not loaded, can not bind ${eventName} event`);\n    }\n\n    return true;\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const /** @type {?} */ mc = this._config.buildHammer(element);\n      const /** @type {?} */ callback = function(eventObj: HammerInput) {\n        zone.runGuarded(function() { handler(eventObj); });\n      };\n      mc.on(eventName, callback);\n      return () => mc.off(eventName, callback);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nisCustomEvent(eventName: string): boolean { return this._config.events.indexOf(eventName) > -1; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n{type: HammerGestureConfig, decorators: [{ type: Inject, args: [HAMMER_GESTURE_CONFIG, ] }, ]},\n];\n}\n\nfunction HammerGesturesPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGesturesPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGesturesPlugin.ctorParameters;\n/** @type {?} */\nHammerGesturesPlugin.prototype._config;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\nexport class DomEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return true; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    element.addEventListener(eventName, /** @type {?} */(( handler as any)), false);\n    return () => element.removeEventListener(eventName, /** @type {?} */(( handler as any)), false);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nDomEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {EventManager} from './events/event_manager';\nimport {DomSharedStylesHost} from './shared_styles_host';\n\nexport const /** @type {?} */ NAMESPACE_URIS: {[ns: string]: string} = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n};\n\nconst /** @type {?} */ COMPONENT_REGEX = /%COMP%/g;\nexport const /** @type {?} */ COMPONENT_VARIABLE = '%COMP%';\nexport const /** @type {?} */ HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const /** @type {?} */ CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimContentAttribute(componentShortId: string): string {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimHostAttribute(componentShortId: string): string {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} compId\n * @param {?} styles\n * @param {?} target\n * @return {?}\n */\nexport function flattenStyles(\n    compId: string, styles: Array<any|any[]>, target: string[]): string[] {\n  for (let /** @type {?} */ i = 0; i < styles.length; i++) {\n    let /** @type {?} */ style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n  return target;\n}\n/**\n * @param {?} eventHandler\n * @return {?}\n */\nfunction decoratePreventDefault(eventHandler: Function): Function {\n  return (event: any) => {\n    const /** @type {?} */ allowDefaultBehavior = eventHandler(event);\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n  };\n}\nexport class DomRendererFactory2 implements RendererFactory2 {\nprivate rendererByCompId = new Map<string, Renderer2>();\nprivate defaultRenderer: Renderer2;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n */\nconstructor(private eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost) {\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  };\n/**\n * @param {?} element\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(element: any, type: RendererType2|null): Renderer2 {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated: {\n        let /** @type {?} */ renderer = this.rendererByCompId.get(type.id);\n        if (!renderer) {\n          renderer =\n              new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type);\n          this.rendererByCompId.set(type.id, renderer);\n        }\n        ( /** @type {?} */((<EmulatedEncapsulationDomRenderer2>renderer))).applyToHost(element);\n        return renderer;\n      }\n      case ViewEncapsulation.Native:\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n      default: {\n        if (!this.rendererByCompId.has(type.id)) {\n          const /** @type {?} */ styles = flattenStyles(type.id, type.styles, []);\n          this.sharedStylesHost.addStyles(styles);\n          this.rendererByCompId.set(type.id, this.defaultRenderer);\n        }\n        return this.defaultRenderer;\n      }\n    }\n  }\n/**\n * @return {?}\n */\nbegin() {}\n/**\n * @return {?}\n */\nend() {}\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: EventManager, },\n{type: DomSharedStylesHost, },\n];\n}\n\nfunction DomRendererFactory2_tsickle_Closure_declarations() {\n/** @type {?} */\nDomRendererFactory2.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomRendererFactory2.ctorParameters;\n/** @type {?} */\nDomRendererFactory2.prototype.rendererByCompId;\n/** @type {?} */\nDomRendererFactory2.prototype.defaultRenderer;\n/** @type {?} */\nDomRendererFactory2.prototype.eventManager;\n/** @type {?} */\nDomRendererFactory2.prototype.sharedStylesHost;\n}\n\nclass DefaultDomRenderer2 implements Renderer2 {\n  data: {[key: string]: any} = Object.create(null);\n/**\n * @param {?} eventManager\n */\nconstructor(private eventManager: EventManager) {}\n/**\n * @return {?}\n */\ndestroy(): void {}\n\n  destroyNode: null;\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string): any {\n    if (namespace) {\n      return document.createElementNS(NAMESPACE_URIS[namespace], name);\n    }\n\n    return document.createElement(name);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string): any { return document.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string): any { return document.createTextNode(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void { parent.appendChild(newChild); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    if (parent) {\n      parent.insertBefore(newChild, refChild);\n    }\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: string|any): any {\n    let /** @type {?} */ el: any = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n                                                       selectorOrNode;\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    el.textContent = '';\n    return el;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any { return node.parentNode; }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any): any { return node.nextSibling; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string): void {\n    if (namespace) {\n      name = `${namespace}:${name}`;\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string): void {\n    if (namespace) {\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { el.classList.add(name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { el.classList.remove(name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.setProperty(\n          style, value, !!(flags & RendererStyleFlags2.Important) ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { node.nodeValue = value; }\n/**\n * @param {?} target\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, event: string, callback: (event: any) => boolean):\n      () => void {\n    checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      return /** @type {?} */(( <() => void>this.eventManager.addGlobalEventListener(\n          target, event, decoratePreventDefault(callback))));\n    }\n    return /** @type {?} */(( /** @type {?} */(( <() => void>this.eventManager.addEventListener(\n               target, event, decoratePreventDefault(callback)))) as() => void));\n  }\n}\n\nfunction DefaultDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nDefaultDomRenderer2.prototype.data;\n/** @type {?} */\nDefaultDomRenderer2.prototype.destroyNode;\n/** @type {?} */\nDefaultDomRenderer2.prototype.eventManager;\n}\n\n\nconst /** @type {?} */ AT_CHARCODE = '@'.charCodeAt(0);\n/**\n * @param {?} name\n * @param {?} nameKind\n * @return {?}\n */\nfunction checkNoSyntheticProp(name: string, nameKind: string) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(\n        `Found the synthetic ${nameKind} ${name}. Please include either \"BrowserAnimationsModule\" or \"NoopAnimationsModule\" in your application.`);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\nprivate contentAttr: string;\nprivate hostAttr: string;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager, sharedStylesHost: DomSharedStylesHost,\nprivate component: RendererType2) {\n    super(eventManager);\n    const styles = flattenStyles(component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n\n    this.contentAttr = shimContentAttribute(component.id);\n    this.hostAttr = shimHostAttribute(component.id);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\napplyToHost(element: any) { super.setAttribute(element, this.hostAttr, ''); }\n/**\n * @param {?} parent\n * @param {?} name\n * @return {?}\n */\ncreateElement(parent: any, name: string): Element {\n    const /** @type {?} */ el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\n\nfunction EmulatedEncapsulationDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.contentAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.hostAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.component;\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\nprivate shadowRoot: any;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} hostEl\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost,\nprivate hostEl: any,\nprivate component: RendererType2) {\n    super(eventManager);\n    this.shadowRoot = (hostEl as any).createShadowRoot();\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nprivate nodeOrShadowRoot(node: any): any { return node === this.hostEl ? this.shadowRoot : node; }\n/**\n * @return {?}\n */\ndestroy() { this.sharedStylesHost.removeHost(this.shadowRoot); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n}\n\nfunction ShadowDomRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nShadowDomRenderer.prototype.shadowRoot;\n/** @type {?} */\nShadowDomRenderer.prototype.sharedStylesHost;\n/** @type {?} */\nShadowDomRenderer.prototype.hostEl;\n/** @type {?} */\nShadowDomRenderer.prototype.component;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, OnDestroy} from '@angular/core';\nimport {getDOM} from './dom_adapter';\nimport {DOCUMENT} from './dom_tokens';\nexport class SharedStylesHost {\n/**\n * \\@internal\n */\nprotected _stylesSet = new Set<string>();\n/**\n * @param {?} styles\n * @return {?}\n */\naddStyles(styles: string[]): void {\n    const /** @type {?} */ additions = new Set<string>();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {}\n/**\n * @return {?}\n */\ngetAllStyles(): string[] { return Array.from(this._stylesSet); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSharedStylesHost.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nSharedStylesHost.prototype._stylesSet;\n}\n\nexport class DomSharedStylesHost extends SharedStylesHost implements OnDestroy {\nprivate _hostNodes = new Set<Node>();\nprivate _styleNodes = new Set<Node>();\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._hostNodes.add(_doc.head);\n  }\n/**\n * @param {?} styles\n * @param {?} host\n * @return {?}\n */\nprivate _addStylesToHost(styles: Set<string>, host: Node): void {\n    styles.forEach((style: string) => {\n      const /** @type {?} */ styleEl = this._doc.createElement('style');\n      styleEl.textContent = style;\n      this._styleNodes.add(host.appendChild(styleEl));\n    });\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\naddHost(hostNode: Node): void {\n    this._addStylesToHost(this._stylesSet, hostNode);\n    this._hostNodes.add(hostNode);\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\nremoveHost(hostNode: Node): void { this._hostNodes.delete(hostNode); }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {\n    this._hostNodes.forEach(hostNode => this._addStylesToHost(additions, hostNode));\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this._styleNodes.forEach(styleNode => getDOM().remove(styleNode)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSharedStylesHost.ctorParameters;\n/** @type {?} */\nDomSharedStylesHost.prototype._hostNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._styleNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\n/**\n * \\@stable\n */\nexport const EVENT_MANAGER_PLUGINS =\n    new InjectionToken<EventManagerPlugin[]>('EventManagerPlugins');\n/**\n * \\@stable\n */\nexport class EventManager {\nprivate _plugins: EventManagerPlugin[];\nprivate _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n/**\n * @param {?} plugins\n * @param {?} _zone\n */\nconstructor( plugins: EventManagerPlugin[],\nprivate _zone: NgZone) {\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(target: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n/**\n * @return {?}\n */\ngetZone(): NgZone { return this._zone; }\n/**\n * \\@internal\n * @param {?} eventName\n * @return {?}\n */\n_findPluginFor(eventName: string): EventManagerPlugin {\n    const /** @type {?} */ plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const /** @type {?} */ plugins = this._plugins;\n    for (let /** @type {?} */ i = 0; i < plugins.length; i++) {\n      const /** @type {?} */ plugin = plugins[i];\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n      }\n    }\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Inject, args: [EVENT_MANAGER_PLUGINS, ] }, ]},\n{type: NgZone, },\n];\n}\n\nfunction EventManager_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManager.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nEventManager.ctorParameters;\n/** @type {?} */\nEventManager.prototype._plugins;\n/** @type {?} */\nEventManager.prototype._eventNameToPlugin;\n/** @type {?} */\nEventManager.prototype._zone;\n}\n\n/**\n * @abstract\n */\nexport abstract class EventManagerPlugin {\n/**\n * @param {?} _doc\n */\nconstructor(private _doc: any) {}\n\n  manager: EventManager;\n/**\n * @abstract\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function) {}\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(element: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ target: HTMLElement = getDOM().getGlobalEventTarget(this._doc, element);\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n    return this.addEventListener(target, eventName, handler);\n  };\n}\n\nfunction EventManagerPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManagerPlugin.prototype.manager;\n/** @type {?} */\nEventManagerPlugin.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport * as core from '@angular/core';\nimport {exportNgVar} from '../util';\n\nconst /** @type {?} */ CORE_TOKENS = {\n  'ApplicationRef': core.ApplicationRef,\n  'NgZone': core.NgZone,\n};\n\nconst /** @type {?} */ INSPECT_GLOBAL_NAME = 'probe';\nconst /** @type {?} */ CORE_TOKENS_GLOBAL_NAME = 'coreTokens';\n/**\n * Returns a {\\@link DebugElement} for the given native DOM element, or\n * null if the given native element does not have an Angular view associated\n * with it.\n * @param {?} element\n * @return {?}\n */\nexport function inspectNativeElement(element: any): core.DebugNode|null {\n  return core.getDebugNode(element);\n}\n/**\n * Deprecated. Use the one from '\\@angular/core'.\n * @deprecated\n */\nexport class NgProbeToken {\n/**\n * @param {?} name\n * @param {?} token\n */\nconstructor(public name: string,\npublic token: any) {}\n}\n\nfunction NgProbeToken_tsickle_Closure_declarations() {\n/** @type {?} */\nNgProbeToken.prototype.name;\n/** @type {?} */\nNgProbeToken.prototype.token;\n}\n\n/**\n * @param {?} extraTokens\n * @param {?} coreTokens\n * @return {?}\n */\nexport function _createNgProbe(extraTokens: NgProbeToken[], coreTokens: core.NgProbeToken[]): any {\n  const /** @type {?} */ tokens = (extraTokens || []).concat(coreTokens || []);\n  exportNgVar(INSPECT_GLOBAL_NAME, inspectNativeElement);\n  exportNgVar(CORE_TOKENS_GLOBAL_NAME, {...CORE_TOKENS, ..._ngProbeTokensToMap(tokens || [])});\n  return () => inspectNativeElement;\n}\n/**\n * @param {?} tokens\n * @return {?}\n */\nfunction _ngProbeTokensToMap(tokens: NgProbeToken[]): {[name: string]: any} {\n  return tokens.reduce((prev: any, t: any) => (prev[t.name] = t.token, prev), {});\n}\n/**\n * Providers which support debugging Angular applications (e.g. via `ng.probe`).\n */\nexport const ELEMENT_PROBE_PROVIDERS: core.Provider[] = [\n  {\n    provide: core.APP_INITIALIZER,\n    useFactory: _createNgProbe,\n    deps: [\n      [NgProbeToken, new core.Optional()],\n      [core.NgProbeToken, new core.Optional()],\n    ],\n    multi: true,\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵglobal as global} from '@angular/core';\n\nconst /** @type {?} */ CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst /** @type {?} */ DASH_CASE_REGEXP = /-([a-z])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function camelCaseToDashCase(input: string): string {\n  return input.replace(CAMEL_CASE_REGEXP, (...m: string[]) => '-' + m[1].toLowerCase());\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: string[]) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param {?} name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param {?} value The value to export.\n * @return {?}\n */\nexport function exportNgVar(name: string, value: any): void {\n  if (!ng) {\n    global['ng'] = ng = ( /** @type {?} */((global['ng'] as{[key: string]: any} | undefined))) || {};\n  }\n  ng[name] = value;\n}\n\nlet /** @type {?} */ ng: {[key: string]: any}|undefined;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * A service that can be used to get and set the title of a current HTML document.\n * \n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n * \n * \\@experimental\n */\nexport class Title {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {}\n/**\n * Get the title of the current HTML document.\n * @return {?}\n */\ngetTitle(): string { return getDOM().getTitle(this._doc); }\n/**\n * Set the title of the current HTML document.\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(newTitle: string) { getDOM().setTitle(this._doc, newTitle); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Title_tsickle_Closure_declarations() {\n/** @type {?} */\nTitle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitle.ctorParameters;\n/** @type {?} */\nTitle.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {GetTestability, Testability, TestabilityRegistry, setTestabilityGetter, ɵglobal as global} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nexport class BrowserGetTestability implements GetTestability {\n/**\n * @return {?}\n */\nstatic init() { setTestabilityGetter(new BrowserGetTestability()); }\n/**\n * @param {?} registry\n * @return {?}\n */\naddToWindow(registry: TestabilityRegistry): void {\n    global['getAngularTestability'] = (elem: any, findInAncestors: boolean = true) => {\n      const /** @type {?} */ testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n      return testability;\n    };\n\n    global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const /** @type {?} */ whenAllStable = (callback: any /** TODO #9100 */) => {\n      const /** @type {?} */ testabilities = global['getAllAngularTestabilities']();\n      let /** @type {?} */ count = testabilities.length;\n      let /** @type {?} */ didWork = false;\n      const /** @type {?} */ decrement = function(didWork_: any /** TODO #9100 */) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(function(testability: any /** TODO #9100 */) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!global['frameworkStabilizers']) {\n      global['frameworkStabilizers'] = [];\n    }\n    global['frameworkStabilizers'].push(whenAllStable);\n  }\n/**\n * @param {?} registry\n * @param {?} elem\n * @param {?} findInAncestors\n * @return {?}\n */\nfindTestabilityInTree(registry: TestabilityRegistry, elem: any, findInAncestors: boolean):\n      Testability|null {\n    if (elem == null) {\n      return null;\n    }\n    const /** @type {?} */ t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, getDOM().getHost(elem), true);\n    }\n    return this.findTestabilityInTree(registry, getDOM().parentElement(elem), true);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {APP_INITIALIZER, ApplicationInitStatus, Inject, InjectionToken, Injector, Provider} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nexport const TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n/**\n * @param {?} transitionId\n * @param {?} document\n * @param {?} injector\n * @return {?}\n */\nexport function appInitializerFactory(transitionId: string, document: any, injector: Injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const /** @type {?} */ dom = getDOM();\n      const /** @type {?} */ styles: any[] =\n          Array.prototype.slice.apply(dom.querySelectorAll(document, `style[ng-transition]`));\n      styles.filter(el => dom.getAttribute(el, 'ng-transition') === transitionId)\n          .forEach(el => dom.remove(el));\n    });\n  };\n}\n\nexport const /** @type {?} */ SERVER_TRANSITION_PROVIDERS: Provider[] = [\n  {\n    provide: APP_INITIALIZER,\n    useFactory: appInitializerFactory,\n    deps: [TRANSITION_ID, DOCUMENT, Injector],\n    multi: true\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n\n\n/**\n * Represents a meta element.\n *\n * @experimental\n */\nexport type MetaDefinition = {\n  charset?: string; content?: string; httpEquiv?: string; id?: string; itemprop?: string;\n  name?: string;\n  property?: string;\n  scheme?: string;\n  url?: string;\n} &\n{\n  // TODO(IgorMinar): this type looks wrong\n  [prop: string]: string;\n};\n/**\n * A service that can be used to get and add meta tags.\n * \n * \\@experimental\n */\nexport class Meta {\nprivate _dom: DomAdapter;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { this._dom = getDOM(); }\n/**\n * @param {?} tag\n * @param {?=} forceCreation\n * @return {?}\n */\naddTag(tag: MetaDefinition, forceCreation: boolean = false): HTMLMetaElement|null {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n/**\n * @param {?} tags\n * @param {?=} forceCreation\n * @return {?}\n */\naddTags(tags: MetaDefinition[], forceCreation: boolean = false): HTMLMetaElement[] {\n    if (!tags) return [];\n    return tags.reduce((result: HTMLMetaElement[], tag: MetaDefinition) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTag(attrSelector: string): HTMLMetaElement|null {\n    if (!attrSelector) return null;\n    return this._dom.querySelector(this._doc, `meta[${attrSelector}]`);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTags(attrSelector: string): HTMLMetaElement[] {\n    if (!attrSelector) return [];\n    const /** @type {?} */ list /*NodeList*/ = this._dom.querySelectorAll(this._doc, `meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n/**\n * @param {?} tag\n * @param {?=} selector\n * @return {?}\n */\nupdateTag(tag: MetaDefinition, selector?: string): HTMLMetaElement|null {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const /** @type {?} */ meta: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\nremoveTag(attrSelector: string): void { this.removeTagElement( /** @type {?} */((this.getTag(attrSelector)))); }\n/**\n * @param {?} meta\n * @return {?}\n */\nremoveTagElement(meta: HTMLMetaElement): void {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n/**\n * @param {?} meta\n * @param {?=} forceCreation\n * @return {?}\n */\nprivate _getOrCreateElement(meta: MetaDefinition, forceCreation: boolean = false):\n      HTMLMetaElement {\n    if (!forceCreation) {\n      const /** @type {?} */ selector: string = this._parseSelector(meta);\n      const /** @type {?} */ elem: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      if (elem && this._containsAttributes(meta, elem)) return elem;\n    }\n    const /** @type {?} */ element: HTMLMetaElement = /** @type {?} */(( this._dom.createElement('meta') as HTMLMetaElement));\n    this._setMetaElementAttributes(meta, element);\n    const /** @type {?} */ head = this._dom.getElementsByTagName(this._doc, 'head')[0];\n    this._dom.appendChild(head, element);\n    return element;\n  }\n/**\n * @param {?} tag\n * @param {?} el\n * @return {?}\n */\nprivate _setMetaElementAttributes(tag: MetaDefinition, el: HTMLMetaElement): HTMLMetaElement {\n    Object.keys(tag).forEach((prop: string) => this._dom.setAttribute(el, prop, tag[prop]));\n    return el;\n  }\n/**\n * @param {?} tag\n * @return {?}\n */\nprivate _parseSelector(tag: MetaDefinition): string {\n    const /** @type {?} */ attr: string = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n/**\n * @param {?} tag\n * @param {?} elem\n * @return {?}\n */\nprivate _containsAttributes(tag: MetaDefinition, elem: HTMLMetaElement): boolean {\n    return Object.keys(tag).every((key: string) => this._dom.getAttribute(elem, key) === tag[key]);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Meta_tsickle_Closure_declarations() {\n/** @type {?} */\nMeta.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMeta.ctorParameters;\n/** @type {?} */\nMeta.prototype._dom;\n/** @type {?} */\nMeta.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {LocationChangeListener, PlatformLocation} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../../dom/dom_adapter';\nimport {DOCUMENT} from '../../dom/dom_tokens';\n\nimport {supportsState} from './history';\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n */\nexport class BrowserPlatformLocation extends PlatformLocation {\nprivate _location: Location;\nprivate _history: History;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._init();\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_init() {\n    this._location = getDOM().getLocation();\n    this._history = getDOM().getHistory();\n  }\n/**\n * @return {?}\n */\nget location(): Location { return this._location; }\n/**\n * @return {?}\n */\ngetBaseHrefFromDOM(): string { return /** @type {?} */(( getDOM().getBaseHref(this._doc))); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('popstate', fn, false);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('hashchange', fn, false);\n  }\n/**\n * @return {?}\n */\nget pathname(): string { return this._location.pathname; }\n/**\n * @return {?}\n */\nget search(): string { return this._location.search; }\n/**\n * @return {?}\n */\nget hash(): string { return this._location.hash; }\n/**\n * @param {?} newPath\n * @return {?}\n */\nset pathname(newPath: string) { this._location.pathname = newPath; }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.pushState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.replaceState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @return {?}\n */\nforward(): void { this._history.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._history.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction BrowserPlatformLocation_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserPlatformLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserPlatformLocation.ctorParameters;\n/** @type {?} */\nBrowserPlatformLocation.prototype._location;\n/** @type {?} */\nBrowserPlatformLocation.prototype._history;\n/** @type {?} */\nBrowserPlatformLocation.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * A DI Token representing the main rendering context. In a browser this is the DOM Document.\n * \n * Note: Document might not be available in the Application Context when Application and Rendering\n * Contexts are not the same (e.g. when running the application into a Web Worker).\n * \n * \\@stable\n */\nexport const DOCUMENT = new InjectionToken<Document>('DocumentToken');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵglobal as global} from '@angular/core';\nimport {setRootDomAdapter} from '../dom/dom_adapter';\n\nimport {GenericBrowserDomAdapter} from './generic_browser_adapter';\n\nconst /** @type {?} */ _attrToPropMap = {\n  'class': 'className',\n  'innerHtml': 'innerHTML',\n  'readonly': 'readOnly',\n  'tabindex': 'tabIndex',\n};\n\nconst /** @type {?} */ DOM_KEY_LOCATION_NUMPAD = 3;\n\n// Map to convert some key or keyIdentifier values to what will be returned by getEventKey\nconst /** @type {?} */ _keyMap: {[k: string]: string} = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf http://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n\n// There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\nconst /** @type {?} */ _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n\nlet /** @type {?} */ nodeContains: (a: any, b: any) => boolean;\n\nif (global['Node']) {\n  nodeContains = global['Node'].prototype.contains || function(node) {\n    return !!(this.compareDocumentPosition(node) & 16);\n  };\n}\nexport class BrowserDomAdapter extends GenericBrowserDomAdapter {\n/**\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) { throw new Error('parse not implemented'); }\n/**\n * @return {?}\n */\nstatic makeCurrent() { setRootDomAdapter(new BrowserDomAdapter()); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: Node, name: string): boolean { return name in element; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Node, name: string, value: any) { ( /** @type {?} */((<any>el)))[name] = value; }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Node, name: string): any { return ( /** @type {?} */((<any>el)))[name]; }\n/**\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Node, methodName: string, args: any[]): any { ( /** @type {?} */((<any>el)))[methodName](...args); }\n/**\n * @param {?} error\n * @return {?}\n */\nlogError(error: string): void {\n    if (window.console) {\n      if (console.error) {\n        console.error(error);\n      } else {\n        console.log(error);\n      }\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlog(error: string): void {\n    if (window.console) {\n      window.console.log && window.console.log(error);\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: string): void {\n    if (window.console) {\n      window.console.group && window.console.group(error);\n    }\n  }\n/**\n * @return {?}\n */\nlogGroupEnd(): void {\n    if (window.console) {\n      window.console.groupEnd && window.console.groupEnd();\n    }\n  }\n/**\n * @return {?}\n */\nget attrToPropMap(): any { return _attrToPropMap; }\n/**\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any): boolean { return nodeContains.call(nodeA, nodeB); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: Element, selector: string): any { return el.querySelector(selector); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string): any[] { return el.querySelectorAll(selector); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: Node, evt: any, listener: any) { el.addEventListener(evt, listener, false); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: Node, evt: any, listener: any): Function {\n    el.addEventListener(evt, listener, false);\n    // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n    return () => { el.removeEventListener(evt, listener, false); };\n  }\n/**\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: Node, evt: any) { el.dispatchEvent(evt); }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: string): MouseEvent {\n    const /** @type {?} */ evt: MouseEvent = document.createEvent('MouseEvent');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: any): Event {\n    const /** @type {?} */ evt: Event = document.createEvent('Event');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: Event) {\n    evt.preventDefault();\n    evt.returnValue = false;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: Event): boolean {\n    return evt.defaultPrevented || evt.returnValue != null && !evt.returnValue;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: HTMLElement): string { return el.innerHTML; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: Node): Node|null {\n    return 'content' in el && el instanceof HTMLTemplateElement ? el.content : null;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: HTMLElement): string { return el.outerHTML; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeName(node: Node): string { return node.nodeName; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: Node): string|null { return node.nodeValue; }\n/**\n * @param {?} node\n * @return {?}\n */\ntype(node: HTMLInputElement): string { return node.type; }\n/**\n * @param {?} node\n * @return {?}\n */\ncontent(node: Node): Node {\n    if (this.hasProperty(node, 'content')) {\n      return ( /** @type {?} */((<any>node))).content;\n    } else {\n      return node;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: Node): Node|null { return el.firstChild; }\n/**\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: Node): Node|null { return el.nextSibling; }\n/**\n * @param {?} el\n * @return {?}\n */\nparentElement(el: Node): Node|null { return el.parentNode; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any): Node[] { return el.childNodes; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: Node): any[] {\n    const /** @type {?} */ childNodes = el.childNodes;\n    const /** @type {?} */ res = new Array(childNodes.length);\n    for (let /** @type {?} */ i = 0; i < childNodes.length; i++) {\n      res[i] = childNodes[i];\n    }\n    return res;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: Node) {\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: Node, node: Node) { el.appendChild(node); }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: Node, node: Node) { el.removeChild(node); }\n/**\n * @param {?} el\n * @param {?} newChild\n * @param {?} oldChild\n * @return {?}\n */\nreplaceChild(el: Node, newChild: Node, oldChild: Node) { el.replaceChild(newChild, oldChild); }\n/**\n * @param {?} node\n * @return {?}\n */\nremove(node: Node): Node {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n    return node;\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: Node, ref: Node, node: Node) { parent.insertBefore(node, ref); }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: Node, ref: Node, nodes: Node[]) {\n    nodes.forEach((n: any) => parent.insertBefore(n, ref));\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: Node, ref: Node, node: any) { parent.insertBefore(node, ref.nextSibling); }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: Element, value: string) { el.innerHTML = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetText(el: Node): string|null { return el.textContent; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: Node, value: string) { el.textContent = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any): string { return el.value; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) { el.value = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any): boolean { return el.checked; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) { el.checked = value; }\n/**\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string): Comment { return document.createComment(text); }\n/**\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any): HTMLElement {\n    const /** @type {?} */ t = document.createElement('template');\n    t.innerHTML = html;\n    return t;\n  }\n/**\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: string, doc = document): HTMLElement { return doc.createElement(tagName); }\n/**\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc = document): Element {\n    return doc.createElementNS(ns, tagName);\n  }\n/**\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc = document): Text { return doc.createTextNode(text); }\n/**\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc = document): HTMLScriptElement {\n    const /** @type {?} */ el = /** @type {?} */(( <HTMLScriptElement>doc.createElement('SCRIPT')));\n    el.setAttribute(attrName, attrValue);\n    return el;\n  }\n/**\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc = document): HTMLStyleElement {\n    const /** @type {?} */ style = /** @type {?} */(( <HTMLStyleElement>doc.createElement('style')));\n    this.appendChild(style, this.createTextNode(css));\n    return style;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).createShadowRoot(); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).shadowRoot; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHost(el: HTMLElement): HTMLElement { return ( /** @type {?} */((<any>el))).host; }\n/**\n * @param {?} node\n * @return {?}\n */\nclone(node: Node): Node { return node.cloneNode(true); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByClassName(name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByTagName(name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclassList(element: any): any[] { return Array.prototype.slice.call(element.classList, 0); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) { element.classList.add(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) { element.classList.remove(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string): boolean {\n    return element.classList.contains(className);\n  }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {\n    element.style[styleName] = styleValue;\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\nremoveStyle(element: any, stylename: string) {\n    // IE requires '' instead of null\n    // see https://github.com/angular/angular/issues/7916\n    element.style[stylename] = '';\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\ngetStyle(element: any, stylename: string): string { return element.style[stylename]; }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string|null): boolean {\n    const /** @type {?} */ value = this.getStyle(element, styleName) || '';\n    return styleValue ? value == styleValue : value.length > 0;\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ntagName(element: any): string { return element.tagName; }\n/**\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any): Map<string, string> {\n    const /** @type {?} */ res = new Map<string, string>();\n    const /** @type {?} */ elAttrs = element.attributes;\n    for (let /** @type {?} */ i = 0; i < elAttrs.length; i++) {\n      const /** @type {?} */ attrib = elAttrs[i];\n      res.set(attrib.name, attrib.value);\n    }\n    return res;\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: Element, attribute: string): boolean {\n    return element.hasAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: Element, ns: string, attribute: string): boolean {\n    return element.hasAttributeNS(ns, attribute);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: Element, attribute: string): string|null {\n    return element.getAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\ngetAttributeNS(element: Element, ns: string, name: string): string {\n    return element.getAttributeNS(ns, name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: Element, name: string, value: string) { element.setAttribute(name, value); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: Element, ns: string, name: string, value: string) {\n    element.setAttributeNS(ns, name, value);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: Element, attribute: string) { element.removeAttribute(attribute); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\nremoveAttributeNS(element: Element, ns: string, name: string) {\n    element.removeAttributeNS(ns, name);\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: Node): any { return this.isTemplateElement(el) ? this.content(el) : el; }\n/**\n * @return {?}\n */\ncreateHtmlDocument(): HTMLDocument {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: Element): any {\n    try {\n      return el.getBoundingClientRect();\n    } catch ( /** @type {?} */e) {\n      return {top: 0, bottom: 0, left: 0, right: 0, width: 0, height: 0};\n    }\n  }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document): string { return document.title; }\n/**\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) { document.title = newTitle || ''; }\n/**\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string): boolean {\n    if (n instanceof HTMLElement) {\n      return n.matches && n.matches(selector) ||\n          n.msMatchesSelector && n.msMatchesSelector(selector) ||\n          n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n\n    return false;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: Node): boolean {\n    return el instanceof HTMLElement && el.nodeName == 'TEMPLATE';\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: Node): boolean { return node.nodeType === Node.TEXT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: Node): boolean { return node.nodeType === Node.COMMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: Node): boolean { return node.nodeType === Node.ELEMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any): boolean {\n    return node.shadowRoot != null && node instanceof HTMLElement;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any): boolean { return node instanceof DocumentFragment; }\n/**\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc(node: Node): any { return document.importNode(this.templateAwareRoot(node), true); }\n/**\n * @param {?} node\n * @return {?}\n */\nadoptNode(node: Node): any { return document.adoptNode(node); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHref(el: Element): string { return ( /** @type {?} */((<any>el))).href; }\n/**\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any): string {\n    let /** @type {?} */ key = event.key;\n    if (key == null) {\n      key = event.keyIdentifier;\n      // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n      // Safari cf\n      // http://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n      if (key == null) {\n        return 'Unidentified';\n      }\n      if (key.startsWith('U+')) {\n        key = String.fromCharCode(parseInt(key.substring(2), 16));\n        if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n          // There is a bug in Chrome for numeric keypad keys:\n          // https://code.google.com/p/chromium/issues/detail?id=155654\n          // 1, 2, 3 ... are reported as A, B, C ...\n          key = ( /** @type {?} */((_chromeNumKeyPadMap as any)))[key];\n        }\n      }\n    }\n\n    return _keyMap[key] || key;\n  }\n/**\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string): EventTarget|null {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return document;\n    }\n    if (target === 'body') {\n      return document.body;\n    }\n    return null;\n  }\n/**\n * @return {?}\n */\ngetHistory(): History { return window.history; }\n/**\n * @return {?}\n */\ngetLocation(): Location { return window.location; }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document): string|null {\n    const /** @type {?} */ href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n/**\n * @return {?}\n */\nresetBaseElement(): void { baseElement = null; }\n/**\n * @return {?}\n */\ngetUserAgent(): string { return window.navigator.userAgent; }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: Element, name: string, value: string) {\n    this.setAttribute(element, 'data-' + name, value);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: Element, name: string): string|null {\n    return this.getAttribute(element, 'data-' + name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any): any { return getComputedStyle(element); }\n/**\n * @return {?}\n */\nsupportsWebAnimation(): boolean {\n    return typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n  }\n/**\n * @return {?}\n */\nperformanceNow(): number {\n    // performance.now() is not available in all browsers, see\n    // http://caniuse.com/#search=performance.now\n    return window.performance && window.performance.now ? window.performance.now() :\n                                                          new Date().getTime();\n  }\n/**\n * @return {?}\n */\nsupportsCookies(): boolean { return true; }\n/**\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string): string|null { return parseCookieValue(document.cookie, name); }\n/**\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {\n    // document.cookie is magical, assigning into it assigns/overrides one cookie value, but does\n    // not clear other cookies.\n    document.cookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);\n  }\n}\n\nlet /** @type {?} */ baseElement: HTMLElement|null = null;\n/**\n * @return {?}\n */\nfunction getBaseElementHref(): string|null {\n  if (!baseElement) {\n    baseElement = /** @type {?} */(( document.querySelector('base')));\n    if (!baseElement) {\n      return null;\n    }\n  }\n  return baseElement.getAttribute('href');\n}\n\n// based on urlUtils.js in AngularJS 1\nlet /** @type {?} */ urlParsingNode: any;\n/**\n * @param {?} url\n * @return {?}\n */\nfunction relativePath(url: any): string {\n  if (!urlParsingNode) {\n    urlParsingNode = document.createElement('a');\n  }\n  urlParsingNode.setAttribute('href', url);\n  return (urlParsingNode.pathname.charAt(0) === '/') ? urlParsingNode.pathname :\n                                                       '/' + urlParsingNode.pathname;\n}\n/**\n * @param {?} cookieStr\n * @param {?} name\n * @return {?}\n */\nexport function parseCookieValue(cookieStr: string, name: string): string|null {\n  name = encodeURIComponent(name);\n  for (const /** @type {?} */ cookie of cookieStr.split(';')) {\n    const /** @type {?} */ eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n        eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {DomAdapter} from '../dom/dom_adapter';\n/**\n * Provides DOM operations in any browser environment.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class GenericBrowserDomAdapter extends DomAdapter {\nprivate _animationPrefix: string|null = null;\nprivate _transitionEnd: string|null = null;\nconstructor() {\n    super();\n    try {\n      const element = this.createElement('div', document);\n      if (this.getStyle(element, 'animationName') != null) {\n        this._animationPrefix = '';\n      } else {\n        const domPrefixes = ['Webkit', 'Moz', 'O', 'ms'];\n\n        for (let i = 0; i < domPrefixes.length; i++) {\n          if (this.getStyle(element, domPrefixes[i] + 'AnimationName') != null) {\n            this._animationPrefix = '-' + domPrefixes[i].toLowerCase() + '-';\n            break;\n          }\n        }\n      }\n\n      const transEndEventNames: {[key: string]: string} = {\n        WebkitTransition: 'webkitTransitionEnd',\n        MozTransition: 'transitionend',\n        OTransition: 'oTransitionEnd otransitionend',\n        transition: 'transitionend'\n      };\n\n      Object.keys(transEndEventNames).forEach((key: string) => {\n        if (this.getStyle(element, key) != null) {\n          this._transitionEnd = transEndEventNames[key];\n        }\n      });\n    } catch (e) {\n      this._animationPrefix = null;\n      this._transitionEnd = null;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: HTMLElement): Node[] { return ( /** @type {?} */((<any>el))).getDistributedNodes(); }\n/**\n * @param {?} el\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(el: HTMLAnchorElement, baseUrl: string, href: string) {\n    el.href = href == null ? baseUrl : baseUrl + '/../' + href;\n  }\n/**\n * @return {?}\n */\nsupportsDOMEvents(): boolean { return true; }\n/**\n * @return {?}\n */\nsupportsNativeShadowDOM(): boolean {\n    return typeof( /** @type {?} */((<any>document.body))).createShadowRoot === 'function';\n  }\n/**\n * @return {?}\n */\ngetAnimationPrefix(): string { return this._animationPrefix ? this._animationPrefix : ''; }\n/**\n * @return {?}\n */\ngetTransitionEnd(): string { return this._transitionEnd ? this._transitionEnd : ''; }\n/**\n * @return {?}\n */\nsupportsAnimation(): boolean {\n    return this._animationPrefix != null && this._transitionEnd != null;\n  }\n}\n\nfunction GenericBrowserDomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._animationPrefix;\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._transitionEnd;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\n\nlet /** @type {?} */ _DOM: DomAdapter = /** @type {?} */(( null));\n/**\n * @return {?}\n */\nexport function getDOM() {\n  return _DOM;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setDOM(adapter: DomAdapter) {\n  _DOM = adapter;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setRootDomAdapter(adapter: DomAdapter) {\n  if (!_DOM) {\n    _DOM = adapter;\n  }\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class DomAdapter {\npublic resourceLoaderType: Type<any> = /** @type {?} */(( null));\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Element, name: string, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Element, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Element, methodName: string, args: any[]) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogError(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlog(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: any) {}\n/**\n * @abstract\n * @return {?}\n */\nlogGroupEnd() {}\n/**\n * Maps attribute names to their corresponding property names for cases\n * where attribute name doesn't match property name.\n * @return {?}\n */\nget attrToPropMap(): {[key: string]: string} { return this._attrToPropMap; };\n/**\n * @param {?} value\n * @return {?}\n */\nset attrToPropMap(value: {[key: string]: string}) { this._attrToPropMap = value; };\n/**\n * \\@internal\n */\n_attrToPropMap: {[key: string]: string};\n/**\n * @abstract\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any) {}\n/**\n * @abstract\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: any, evt: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: string) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: any) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: any) {}\n/**\n * Returns content if el is a <template> element, null otherwise.\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeName(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ntype(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ncontent(node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nparentElement(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} newNode\n * @param {?} oldNode\n * @return {?}\n */\nreplaceChild(el: any, newNode: any, oldNode: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nremove(el: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: any, ref: any, node: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: any, ref: any, nodes: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: any, el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: any, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetText(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) {}\n/**\n * @abstract\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string) {}\n/**\n * @abstract\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any) {}\n/**\n * @abstract\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: any, doc?: any) {}\n/**\n * @abstract\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetHost(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nclone /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nclassList(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\nremoveStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\ngetStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ntagName(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\ngetAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: any, ns: string, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nremoveAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: any) {}\n/**\n * @abstract\n * @return {?}\n */\ncreateHtmlDocument() {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: any) {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document) {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) {}\n/**\n * @abstract\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nadoptNode /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetHref(element: any) {}\n/**\n * @abstract\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(element: any, baseUrl: string, href: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsDOMEvents() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsNativeShadowDOM() {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string) {}\n/**\n * @abstract\n * @return {?}\n */\ngetHistory() {}\n/**\n * @abstract\n * @return {?}\n */\ngetLocation() {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document) {}\n/**\n * @abstract\n * @return {?}\n */\nresetBaseElement() {}\n/**\n * @abstract\n * @return {?}\n */\ngetUserAgent() {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: any, name: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsWebAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nperformanceNow() {}\n/**\n * @abstract\n * @return {?}\n */\ngetAnimationPrefix() {}\n/**\n * @abstract\n * @return {?}\n */\ngetTransitionEnd() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsCookies() {}\n/**\n * @abstract\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string) {}\n/**\n * @abstract\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {}\n}\n\nfunction DomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nDomAdapter.prototype.resourceLoaderType;\n/**\n * \\@internal\n * @type {?}\n */\nDomAdapter.prototype._attrToPropMap;\n}\n\n"], "names": ["window", "global"], "mappings": ";;;;;G6BAA;;;;;;;;;GAeA;AACA,IAAE,IAAF,GAHS,CAGT,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;;;;;AAKA,CAAA;;;;;AAQA;;;GAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6CA;;;;;;IAMA;;;;;;IAMA;;;;;IAKA,6BAAA,GAAA,UAAA,KAAA,IAAA,CAAA;;;;;;;QAMA;;;;;;;;;;;;;;IAKA,CAAA;;;;;;;;;IAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2CA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;IAaA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqCA;;;;;;;;;;;;;IAaA;;;;;;;;;;;;;IAaA;;;;;;;;;;;;;IAaA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2CA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;IAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyDA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoEA;;;;;IAKA,sCAAA,GAAA,UAtdY,EAsdZ,IAtdY,CAsdZ;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;IAoBA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;IAaA,sCAziBY,GAyiBZ,UAAA,OAAA,EAAA,OAAA,EAAA,IAAA,IAAA,CAAA;;;;;IAKA,sCAAA,GAAA,cAAA,CA7iBY;;;;;;;;;;;;IAyjBZ,yCAAA,GAAA,UAAA,GAAA,EAAA,MAAA,IAAA,CAAA;;;;;IAKA,+BAAA,GAAA,cAAA,CAAA;;;;;;IAMA;;;;;IAKA,gCAAA,GAAA,UAAA,GAAA,IAAA,CAAA;;;;;IAKA,qCAAA,GAAA,cAAA,CAAA;;;;;;;;;;;;;;IAcA;;;;;;;;;;;;IAYA,4BAAA,GAAA,UAAA,OAAA,EAAA,IAAA,IAAA,CA9lBY;;;;;IAmmBZ,yCAlmBY,GAkmBZ,cAAA,CAAA;;;;;IAKA,mCAAA,GAAA,cAAA,CAAA;;;;;IAKA,uCAAA,GAAA,cAAA,CAAA;;;;;IAKA,qCAAA,GAAA,cAAA,CAAA;;;;;IAKA,sCAAA,GAAA,cAAA,CAAA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GDlwBA;AAEA;IAAA,oDAAA;IACA;QAAA,YACA,iBAAA,SAkCA;QAjCA,KAAA,CAAA,gBAAA,GAAA,IAAA,CAA6B;QAC7B,KAAA,CAAO,cAAP,GAAA,IAAA,CAAA;QAAA,IAAA,CAAA;YACA,IAAA,SAAA,GAAA,KAAA,CAAyB,aAAzB,CAAuC,KAAK,EAAE,QAA9C,CAAuD,CAAC;YAExD,EAAA,CAAA,CAAQ,KAAR,CAAa,QAAQ,CAAC,SAAtB,EAAA,eAA8C,CAA9C,IAAmD,IAAnD,CAAA,CAAA,CAAA;gBACA,KAAU,CAAV,gBAA2B,GAA3B,EAAA,CAAA;YACA,CAAA;YACA,IAAA,CAAA,CAAA;gBACA,IAAA,WAAA,GAAA,CAAA,QAAA,EAAA,KAAA,EAAA,GAAA,EAAA,IAAA,CAAA,CAAA;gBACA,GAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,WAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;oBACA,EAAA,CAAA,CAAA,KAAA,CAAA,QAAA,CAAA,SAAA,EAAA,WAAA,CAAA,CAAA,CAAA,GAAA,eAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;wBAEA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,GAAA,GAAA,CAAA;wBACA,KAAA,CAAA;oBACA,CAAA;gBACQ,CAAR;YACA,CAAA;YACA,IAAA,oBAAA,GAAA;gBAEA,gBAAA,EAAA,qBAAA;gBACQ,aAAR,EAAA,eAAsC;gBACtC,WAAA,EAAA,+BAAkD;gBAClD,UAAA,EAAA,eAAA;aACO,CAAC;YACR,MAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA;gBAAc,EAAE,CAAhB,CAAA,KAAA,CAAA,QAAA,CAAA,SAAA,EAAA,GAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;oBACA,KAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,GAAA,CAAA,CAAA;gBACU,CAAC;YACX,CAAA,CAAA,CAAA;QACA,CAAA;;;;;;IAKA,CAAA;;;;;;;;;;;;;QAaA,EAAA,CAAA,IAAA,GAAA,IAAA,IAXiC,IAWjC,GAXwC,OAAM,GAW9C,OAAA,GAAA,MAAA,GAAA,IAAA,CAAA;;;;OAIA;IACA,oDAAA,GAAA,cAAA,MAdwB,CAAQ,IAAC,CAcjC,CAAA,CAdsC;IAetC;;;;QAIA,MAAA,CAAA,OAjBG,CAiBH,CAAA,QAAA,CAAA,IAAA,CAjBwC,CAAK,CAiB7C,gBAAA,KAAA,UAAA,CAAA;;;;OAIA;;;;OAIA;IACA,mDAvBgB,GAuBhB,cAAA,MAAA,CAAA,IAvBiB,CAuBjB,cAAA,GAAA,IAAA,CAAA,cAAA,GAvBwE,EAuBxE,CAAA,CAAA,CAAA;IACA;;;ID3FA,oDAAA,GAAA;;;;CCsBA,CAAA,UAAA;;;;;;;GDJA;AACA,IAAA,cAAA,GAAA;IAEA,OAAA,EAAA,WAAA;;IAGA,UAAA,EADuC,UACvC;;;AAGA,IAAA,uBAAA,GAAA,CAAA,CAAA;AACA,0FAAA;AACA,IAAA,OAAA,GAAA;IACE,8FAAF;IACE,kDAAF;IACE,IAAF,EAAA,WAAA;IACE,IAAF,EAAQ,KAAR;IACE,MAAF,EAAA,QAAA;IACE,MADM,EACR,QAAA;IACE,KAAF,EAAA,QAAA;IACE,KAAF,EAAA,QAAA;IACE,MAAF,EAAU,WAAV;IACE,OADO,EACT,YAAA;IACA,IAAA,EAAA,SAAA;;;;IAKA,KAAA,EAAA,IAAA;CACA,CAAA;AACA,oDAAA;AACA,6DAAA;AACA,0CAAA;AACA,IAAA,mBAAA,GAAA;IACE,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAF,EAAA,GADU;IAER,GAAF,EAAA,GADU;IAEV,GAAA,EAAA,GAAA;IACI,GACJ,EAAA,GAAA;IAEIC,GAAJ,EAAA,GADW;IAET,MAAF,EAAA,GAAA;IACA,MAAA,EAAA,SAAA;CACA,CAAA;AACA,IAAA,YAAA,CAAA;AACA,EAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;;;;;AAKA;IAGsC,6CAHtC;IAAA;;IAmxBA,CAAA;;;;OA/wBA;;;;;;IAMA;;;;;;;;;;;;;IAaA;;;;;;;;;;;;IAYA,kCAAA,GAAA,UAAA,EAxBG,EAwBH,UAAA,EAAA,IAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,IAAA,EAAA,CAAA,CAAA;IACA;;;OAGA;IAxBA,oCAAA,GAAA,UAAA,KAAA;QAyBA,EAAA,CAAA,CAAA,MAAA,CAAA,OAAA,CAxBiB,CAAG,CAwBpB;YACA,EAAA,CAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;gBACA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;YACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;;;;;IAKA,CAAA;IACA;;;OAGA;;;;QAIA,CAAA;IACA,CAAA;IACA;;OAEA;;;;QA9BG,CAkCH;;;;;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;;;;;;OAkBA;IACA,uCAAA,GAAA,UAAA,EAAA,EAAA,GAAA,EAAA,QAAA;;;;;;IAMA;;;;;IAKA,yCAAA,GAAA,UAAA,EArEG,EAqEH,GAAA,IAAA,EAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;;;;;IAKA,CAAA;IACA;;;OAGA;;;;;IAKA,CAAA;IACA;;;;;;;IAOA,CAAA;IACA;;;;;;IAMA,CAAA;;;;;IAKA,wCAAA,GAAA,UAAA,EAAA,IAzFG,MAyFH,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;IACA;;;;;;IAMA,CAAA;;;;;IAKA,wCAAA,GAAA,UAjGG,EAiGH,IAAA,MAAA,CAAA,EAjGwC,CAAK,SAAS,CAiGtD,CAjGuD,CAiGvD;;;;;IAKA,oCAAA,GAAA,UArGG,IAqGH,IAAA,MAAA,CAAA,IAAA,CArG8C,QAqG9C,CAAA,CArGoD,CAAS;;;;;IA0G7D,qCAzGG,GAyGH,UAAA,IAAA,IAAA,MAzGgD,CAAK,IAAC,CAAI,SAyG1D,CAAA,CAAA,CAAA;;;;;IAKA,gCAAA,GAAA,UAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;IACA;;;OA3GA;IA8GA,mCAAA,GAAA,UAAM,IAAN;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,EAAA,SAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,OAAA,CAAA;;;;;IAKA,CAAA;;;;;IAKA,sCAAA,GAAA,UArHG,EAqHH,IAAA,MAAA,CAAA,EAAA,CArH4C,UAqH5C,CAAA,CArHgD,CAAW;;;;;IA0H3D,uCAAA,GAAA,UAAA,EAzHG,IAyHH,MAAA,CAAA,EAzH8C,CAyH9C,WAAA,CAzHkD,CAAU,CAyH5D;;;;;IAKA,yCA7HG,GA6HH,UAAA,EAAA,IAAA,MA7HuC,CAAG,EA6H1C,CAAA,UA7HsD,CA6HtD,CAAA,CAAA;;;;;IAKA,sCAAA,GAAA,UAAA,EAAA,IAjIG,MAiIH,CAAA,EAAA,CAAA,UAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;IACA,4CAAA,GAAA,UAAA,EAAA;QACI,IAAJ,gBAAA,CAAA,UAAA,GAAA,EAAA,CAAA,UAAA,CAAA;QACA,IAAA,gBAAA,CAAA,GAAA,GAAA,IAAA,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;IAYA,wCAAA,GAAA,UAAA,EAAA,EAAA,QAAA,EAAA,QAAA,IAAA,EAAA,CAAA,YAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;IACA,kCAAA,GAAA,UAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BA;;;;;IAKA,wCAAA,GAAA,UAAA,EAAA,EAAA,KAAA,IAAA,EAAA,CAAA,SApL0D,GAoL1D,KAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;;IAKA,mCAAA,GAAA,UA7LG,EA6LH,EAAA,KAAA,IAAA,EAAA,CA7LqC,WA6LrC,GAAA,KAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;;IAKA,oCAAA,GAAA,UAAA,EAtMG,EAAA,KAsMH,IAAA,EAtMwC,CAsMxC,KAAA,GAAA,KAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;;IAKA,sCAAA,GAAA,UAAA,EA/MG,EA+MH,KAAA,IAAA,EAAA,CAAA,OAAA,GA/MgD,KA+MhD,CAAA,CAAA,CAAA;;;;;IAKA,yCAAA,GAAA,UAnNG,IAmNH,IAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;;;;;;IAMA;;;;;;;;;;;;;;;;;;;IAeA;;;;;;;;;;;;;;OAWA;;;;;;;IAMA;;;;OAIA;;;;;;IAKA,CAAA;;;;;IAKA,4CAtPG,GAsPH,UAAA,EAAA,IAAA,MAtPkE,CAsPlE,CAtPmE,CAsPnE,EAtPsE,CAsPtE,CAAA,CAAA,gBAAA,EAAA,CAAA,CAAA,CAAA;;;;;IAKA,yCAAA,GAAA,UA1P0C,EA0P1C,IAAA,MAAA,CAAA,CA1P2D,CA0P3D,EAAA,CA1P4D,CAAI,CA0PhE,UAAA,CAAA,CAAA,CAAA;;;;;IAKA,mCAAA,GAAA,UAAA,EA9PG,IAAyB,MA8P5B,CA9PmC,CA8PnC,CAAA,EA9PmC,CAAK,CA8PxC,CAAA,IAAA,CAAA,CAAA,CA9PyC;;;;;;IAoQzC;;;;;;;;IAQA;;;;;;;IAOA,CAAA;;;;;;IAMA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;;;;;;IAiBA;;;;OAIA;;;;;;IAMA;;;;;;;;;;;;;;;IAeA,CAAA;;;;;IAKA,mCAAA,GAAA,UAAA,OAAA,IAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;IACA,wCAzTU,GAyTV,UAAA,OAAA;QACA,IAAA,gBAAA,CAAA,GAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,UAAA,CAAA;QACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;;;;;;IAMA;;;;;;;;;;;;;;;;;IAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCA;;;;;;;;;;;;;;IAcA,CAAA;;;;OAIA;IACA,6CAAA,GAAA,UAAA,EA9WoB,IA8WpB,MAAA,CAAA,IA9WmC,CA8WnC,iBA9WoC,CAAkB,EA8WtD,CAAA,GAAA,IAAA,CA9WuD,OA8WvD,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IACA;;;;;IAKA,CAAA;IACA;;;OAhXA;IAmXA,iDAAA,GAAA,UAlXmB,EAkXnB;QACA,IAAA,CAAA;YACA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA,CAAA;;;;;IAKA,CAAA;;;;;;IAMA;;;;;;IAMA;;;;OAIA;IACA,0CAAA,GAAA,UAAA,CAAA,EAAA,QAAA;QAEI,EAAJ,CAAA,CAAA,CAAA,YAAA,WAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,CAAA,OAAA,CAAA,QAAA,CAAA;;;;;IAKA,CAAA;IACA;;;;;;IAMA,CAAA;;;;;IAKA,sCAAA,GAAA,UAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;IAKA,yCAhZG,GAgZH,UAhZG,IAAA,IAAoC,MAgZvC,CAhZ8C,IAAA,CAAK,QAAC,KAAY,IAAA,CAAK,YAAC,CAAY,CAgZlF,CAhZmF;;;;;IAqZnF,yCApZG,GAoZH,UApZG,IAAA,IAoZH,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACA;;;;;;IAMA,CAAA;;;;;IAKA,wCAAA,GAAA,UA5ZG,IA4ZH,IAAA,MAAA,CAAA,IAAA,YAAA,gBAAA,CAAA,CAAA,CAAA;;;;;IAKA,yCAAA,GAAA,UAhaG,IAA4B,IAga/B,MAAA,CAAA,QAAA,CAAA,UAha0D,CAAI,IAga9D,CAAA,iBAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,qCAAA,GAAA,UApaG,IAA8B,IAoajC,MAAA,CApa+C,QAAQ,CAoavD,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,mCAAA,GAAA,UAAA,EAAA,IAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;IACA;;;;;;QAMA,EAAA,CAAA,CAAM,GAAN,IAvaW,IAAM,CAuajB,CAAA,CAAA;YACA,GAAA,GAAA,KAvae,CAuaf,aAvae,CAAe;YAwa9B,4FAAA;YACM,YAAN;YACA,wGAAA;YACA,EAAA,CAAA,CAAQ,GAAR,IAAA,IAAA,CAAA,CAAA,CAAA;;;;gBAIA,GAAA,GAAA,MAvagB,CAuahB,YAAA,CAAA,QAva6C,CAua7C,GAvaiD,CAAC,SAualD,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,KAAA,CAAA,QAAA,KAAA,uBAAA,IAAA,mBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;oBACA,oDAAA;oBACA,6DAAA;oBAEA,0CAAA;oBACA,GAAA,GAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,gDAAA,GAAA,UAAA,GA5asB,EA4atB,MAAA;QACA,EAAA,CAAA,CAAA,MAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YACQ,MA5aC,CA4aT,MAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,KAAA,UAAA,CAAA,CAAA,CAAA;YACA,MAAA,CA5aW,QA4aX,CAAA;QACA,CAAA;;;;QAIA,MA/aG,CA+aH,IA/a0B,CA+a1B;;;;OAIA;;;;;IAKA,uCAtbG,GAsbH,cAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,CAAA;IACA;;;;;;QAMA,MAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,YAAA,CAAA,IAAA,CAAA,CAAA;;;;OAIA;;;;;;;;;;;;;;;IAeA;;;;;;;IAOA,CAAA;;;;OAIA;IACA,4CAAA,GAAA,UAAA,OAAA,IAAA,MAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;;QAIA,MAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA,SAAA,CAAA,KAAA,UAAA,CAAA;;;;OAIA;IACA,0CAAA,GAAA;;;;YAIA,IAAA,IAAA,EAAA,CAAA,OAAA,EAAA,CAAA;;;;;IAKA,2CAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,qCAAA,GAAA,UAAA,IAAA,EAAA,KAAA;QAEA,6FAAA;;;;IAIA,wBAAA;AAAA,CAAA,AAnxBA,CAGsC,wBAHtC,GAmxBA;AACA,IAAE,WAAF,GAAA,IA5doB,CA4dpB;AACA;;GAEA;AACA;IACA,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA;QACA,WAAA,GA5dS,CAAY,CA4drB,QAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA;;QAGA,CAAA;;;;;AAKA,IAAA,cAAA,CAAA;AACA;;;GAGA;AACA,sBAAA,GAAA;IACA,EAAA,CAAA,CAAuD,CAAvD,cAAA,CAAA,CAAA,CAAA;QACA,cAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA;;;;;;AAMA;;;;GAIA;AAEA,0BAAA,SAAA,EAAA,IAAA;IACA,IAAA,GAAA,kBAAA,CAAA,IAAA,CAAA,CAAA;IACA,GAAA,CAAA,CAAA,UAAA,EAAA,KAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,cAAA,EAAA,IAAA;QAAK,IAAL,MAAA,SAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;QACA,IAAA,yFAAA,EApeS,kBAoeT,EAAA,mBAAA,CAAA;QACA,EAAA,CAAA,CAAA,UAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,CAAA,CAAA;;QDv4BA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GDAA;;;;;;;;;;;AAqBA;;;;GAMA;AAEA;IAAA,mDAAA;IADA;;OAGA;;8CAKA;;;;IAAA,CAAA;IACA;;;;;;QAGG,IAGH,CAAA,QAH6B,GAG7B,MAAA,EAHoC,CAAK,UAAU,EAAC,CAGpD;;;;;WAIA;;;;;;;;IAKA,oDAAA,GAAA,cAAA,MAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;;;;IAMA,CAAA;IACA;;;;;QARG,MAaH,EAbG,CAaH,oBAAA,CAAA,IAbwC,CAAS,IAajD,EAAA,QAAA,CAAA,CAAA,gBAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,CAAA,CAAA;;;;;WAIA;;;;;;;;;;;;;WAIA;;;;;IAKA,sBAvBG,yCAuBH;;;;aAAA,cAvBG,MAuBH,CAvBG,IAA+B,CAuBlC,SAAA,CAAA,IAvBiD,CAuBjD,CAAA,CAAA;;;OAAA;;;;;;OAlBA;IA6BA,2CAAA,GAAA,UAAA,KAAA,EAAA,KA5BY,EA4BZ,GA5BsB;QA6BtB,EAAA,CAAA,CAAA,aAAA,EAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,CAAA,CAAA;;;;;;;;;;;OAvBA;IAkCA,8CAAU,GAAV,UAjCW,KAiCX,EAAA,KAAA,EAjCsB,GAAM;QAkC5B,EAAA,CAAA,CAAA,aAAA,EAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,QAAA,CAAA,YAAA,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,CAAA,CAAA;;;;QAIA,CAAA;;;;OAIA;;IApCA;;OAuCA;;;CAtFA,CAAA,gBAAA;;IAmDA,EAAA,IAAA,EAAA,UAAA,EAAA;CAwCA,CAAA;AACA;;GDzHA;;;;;;;;;;;AAoCA;;;;GAKA;AAFA;IAAA;;;;;;;IASA;;;;OAGA;;;;;;;IAMA;;;;OAGA;IACA,sBAAA,GAAA,UAAA,IAAQ,EAAR,aAAA;QAAA,iBASA;QATA,8BAAA,EAAA,qBAAA;QACA,EAAA,CAAA,CAAA,CAAO,IAAP,CAAA;YACM,MAAN,CAXa,EAWb,CAAA;QACA,MAXU,CAAC,IAWX,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA,GAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAEA;;;;;IAKA,CAAA;IACA;;;OAEA;IACA,sBAAA,GAAA,UAAA,YAAA;;;;;;IAMA;;;;OAGA;IACA,wBAAA,GAAA,UAAA,GAAA,EAAA,QAAA;QACA,EAAA,CAAA,CAAM,CAAN,GAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QACI,QAAJ,GArBW,QAqBX,IAAA,IAAA,CAAA,cArB8C,CAAC,GAqB/C,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,IAAA,GAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;;IAKA,wBAAA,GAAA,UAAA,YAAA,IAAA,IAAA,CAAA,gBAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;;;;;;IAzBG;;;;;;;;YAuCG,IAAN,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;YA/BA,IAAA,gBAAA,CAAA,IAAA,GAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;YAgCA,kFAAA;YACA,0FAAA;YACQ,sCAAR;YACA,EAAA,CAAA,CAAA,IAAA,IAAA,IAAA,CAAA,mBA/B2B,CA+B3B,IAAA,EAAA,IAAA,CAAA,CAAA;gBACA,MAAA,CAAA,IAAA,CAAA;QACI,CAAJ;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;;IA7BG;;;;;;yBAKH;;;IAAG,CAAH;IAuCA;;;;;;;;IAlCG;;;;OAGH;IA2CA,kCAAA,GAAA,UA1CmB,GA0CnB,EAAA,IAAA;QAAA;QACA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,KAAA,CAAA,UAAA,GAAA,IAAA,OAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,EAAA,GAAA,CAAA,KAAA,GAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA;;;CAxHA;;IAgFA,EAAA,IAAA,EAAA,UAAC,EAAD;CA6CA,CAAA;AACA;;GDrKA;;;;;;;;;;GAiBA;;;;;;;;;;;GAWA;AACA,+BAAA,YAAA,EAAA,QAAA,EAAA,QAAA;IACA,MAAA,CAAA;QAEA,0FAAA;QACA,cAAA;QACA,QAAA,CAAA,GAAA,CAAA,qBAAA,CAAA,CAAA,WAAA,CAAA,IAAA,CAAA;YACA,IAAA,gBAAA,CAAA,GAAA,GAAA,MAAA,EAAA,CAAA;YACA,IAAA,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,sBAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAAA,MAAA,CAAA,UAAA,EAAA,IAAA,OAAA,GAAA,CALuD,YAKvD,CAAA,EAAA,EAAA,eAAA,CAAA,KAAA,YAAA,EAAA,CAAA,CAAA;iBACA,OAAA,CAAA,UAAA,EAAA,IAAA,OAAA,GAAA,CAAA,MAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA;IACA,CAAA,CAAA;AACA,CAAA;AACA,IAAA,2BAAA,GAAA;IACA;QACA,OAAA,EAAA,eAAA;;QD7CA,IAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,QAAA,CAAA;;;;;;;;;;GAgBA;;;;;;;IAKA,0BANG,GAMH,cAAA,oBAAA,CAAA,IAAA,qBAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACAA;;;OAGA;IACA,2CAAA,GAAA,UAAA,QAAA;QACA,OAAA,CAAA,uBAAA,CAAA,GAAA,UAAA,IAAA,EAAA,eAAA;YAAA,gCAAA,EAAA,sBAAA;YACA,IAAA,gBAAA,CAAA,WAAA,GAAA,QAAA,CAAA,qBAAA,CAAA,IAAA,EAAA,eAAA,CAAA,CAAA;YAEAA,EAAAA,CAAU,CANC,WAMXA,IAAAA,IAAAA,CAAAA,CAAAA,CAAAA;gBAJW,MAMXA,IAAAA,KAAAA,CAAAA,yCAAAA,CAAAA,CAAAA;YAEA,CAAA;YACM,MAAN,CAAA,WAAA,CAAA;QACA,CAAA,CAAA;QACA,OAAA,CAAA,4BAAA,CAAA,GANoB,cAMpB,OAAA,QAAA,CAAA,mBAAA,EAAA,EAAA,CAAA,CAAA;QACA,OAAA,CAAA,2BAAA,CANY,GAAY,cAMxB,OAAA,QAAA,CAAA,kBAAA,EAAA,EAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,aAAA,GAAA,UAAA,QAAA,CAAA,iBAAA;YACA,IAAA,gBAAA,CAAA,aAAA,GAAA,OAAA,CAAA,4BAAA,CAAA,EAAA,CAAA;YACA,IAAQ,gBANgB,CAMxB,KAAA,GAAA,aAAA,CAAA,MAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA;YACA,IAAA,gBAAA,CAAA,SAAA,GAAA,UAAA,QAAA,CAAA,iBAAA;gBACA,OAAA,GAAA,OAAA,IAAA,QAAA,CAAA;gBACA,KAAA,EAAA,CAAA;gBACQ,EAAR,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBACA,QAAA,CAAA,OAAA,CAAA,CAAA;gBACA,CAAA;YAEQ,CANCA,CAMT;YACMA,aAANA,CAAAA,OAAAA,CAAAA,UAAAA,WAAAA,CAAAA,iBAAAA;gBACA,WAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA;YACAA,CAAAA,CAAAA,CAAU;QACV,CAAA,CAAA;;;;;;;;;;;OAYA;IACA,qDAAA,GAAA,UAAA,QAAA,EAAA,IAAA,EAAA,eAAA;QACA,EAAA,CAAA,CAAM,IAAN,IAXa,IAWb,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QAXA,CAAA;QAYA,IAAA,gBAAA,CAAA,CAAA,GAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YACQ,MAXC,CAWT,CAXe,CAAE;QAYjB,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAXW,IAWX,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,EAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;QD7EA,CAAA;;;;;;;;;;;;;;;;;;;;;GA2BA;AADA;IAAA;;;;;IAOA,CAAA;;;;;;IAMA;;;;OAGA;;;CAhBA;;IAgBA,EAAA,IAAA,EAAA,UAAA,EAAA;CAKA,CAAA;AACA;;GDhDA;;;;;;;;;;GAiBA;;;;;;;;;;;;;;;;GAsBA;AACA,qBAAA,IAAA,EAAA,KAAA;IAJI,EAA0B,CAAS,CAMvC,CAAA,EAAA,CAAA,CAAA,CAAA;;ID1CA,CAAA;;;;;;;;;;GAiBA;AACA,IADM,WACN,GAAA;;;;;;;;;;;;;;;AAeA,CAAA;;;;;AAKA;IALA;;;OAOA;IAEA,wBAAA,IAAA,EAAA,KAAA;;;;;CAJA;AAgBA;;;;GAIA;AACA,wBAAA,WAAA,EAAA,UAAA;;;;;AAKA,CAAA;AACA;;;;;IAKA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EApBC,CAoBD,IAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,KAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;AACA,CAAA;AACA;;GAEA;AACA,IAAA,uBAAqB,GAArB;IACA;QACA,OAAA,EAAA,eAAA;QACI,UAAJ,EAAA,cAAA;QACA,IAAA,EAAA;YACA,CAAA,cAAA,EAAA,IAAA,QAAA,EAAA,CAAA;;SDhFA;;;;;;;;;;;;;GAoBA;;;;;AAOA;IADA;;;OAIA;IACA,sBAAA,OAAA,EAAA,KAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;QAwBA,MAAA,CAAA,MAAA,CAAA,sBAAA,CAAA,MAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IAEA,qCAAA,GAAA,UAAA,SAAA;QACI,IAAJ,gBAAA,CAAA,MAAA,GAAA,IAAA,CAjBwB,kBAiBxB,CAjB0C,GAiB1C,CAAA,SAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,MAAN,CAAA,CAAA,CAAA;YACM,MAAN,CAAA,MAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAjBsB,CAiBtB,OAAA,GAAA,IAAA,CAAA,QAAA,CAAA;QACA,GAAA,CAAA,CAAO,IAAP,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YACA,EAjBU,CAiBV,CAAA,QAAA,CAjBc,QAiBd,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;;YAhBA,CAAA;QAkBA,CAAA;QACA,MAAA,IAAA,KAAA,CAAA,6CAAA,SAAA,CAAA,CAAA;;;CApDA;;IAqCA,EAAA,IAAA,EAAA,UAAA,EAAA;CAoBA,CAAA;AACA;;GAIA;;;IAmBA,EAAA,IAAA,EAAA,MAAA,GAAA;;;;GAIA;AAzCA;IAAA;;;;;;IAiDA;;;;;;;;;;;;;;;;;;;OAmBA;IACA,mDAAA,GAAA,UAAA,OAAA,EAxDiB,SAAwB,EAwDzC,OAxDyC;QAyDzC,IAAA,gBAAA,CAAA,MAAA,GAAA,MAAA,EAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;YACA,MAAA,IAAA,KAAA,CAAA,8BAAA,MAAA,mBAAA,SAAA,CAAA,CAAA;;QD9IA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA;;;;CCuEA;;;;;;;;ADxDA;IAiCA;;;;;IA3BA,CAAA;IACA;;;OAGA;IACA,oCAAA,GAAA,UAAA,MAAA;QAAA,iBASA;QARA,IAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YACQ,EAAR,CAAA,CAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAJwB,CAAS,KAIjC,CAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;OAIA;;IARA;;OAWA;;;CA3BA;;IAoBA,EAAA,IAAA,EAAA,UAAA,EAAA;CAeA,CAAA;;;;AAqBA,gBA5BuB,CA4BvB,cAAA,GAAA,cAAA,OAAA,EAAA,EAAA,CAAA,CAAA;AAEA;IAAA,+CAAA;IA9BA;;OADA;IAgCA,6BAAA,IAAA;QAAA,YAZA,iBAAA;;;;;;;IAdG;;;;OAqCH;IACA,8CAAA,GAAA,UAAA,MAAA,EAAA,IAAA;QAAA,iBAMA;QALA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA;;;;;IAKA,CAAA;IACA;;;;;;;IAOA,CAAA;;;;;IAKA,wCAAA,GAAA,UAAA,QAAA,IA1CG,IA0CH,CAAA,UAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;;;;QAKA,IAAA,CAAA,UAAA,CA5CwB,OA4CxB,CAAA,UAAA,QAAA,IA5C0C,OAAA,KA4C1C,CAAA,gBAAA,CA5CqE,SA4CrE,EAAA,QA5CyF,CAAC,EAAhD,CAAgD,CA4C1F,CA5C2F;;IAC3F;;OA8CA;;;CAzCA,CAAA,gBAAA;;IADA,EAAA,IAAA,EAAA,UAAA,EAAA;CA+CA,CAAA;AACA;;GDxHA;;;;;;;;;;GAkBA;AACA,IAAA,cAAA,GAAA;IACA,KAAA,EAAA,4BAAA;IAEA,OAAA,EAAA,8BAAA;IACA,OAAA,EAAA,8BAAA;IACA,KAAA,EAAA,sCADuD;IAEvD,OAAA,EAAA,+BAAA;;;;;AAKA,IAAA,YAAA,GAAA,gBAAA,kBAAA,CAAA;AACA;;;;;;AAMA,CAAA;AACA;;;;;;;;;;;;GAcA;AACA,uBAAA,MAAA,EAAA,MAAA,EAAA,MAAA;IAZA,GAAA,CAAA,CAAA,IAAW,gBAAX,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;QAaA,IAAM,gBAAN,CAAA,KAZ4B,GAY5B,MAAA,CAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,KAAN,CAAY,OAAZ,CAAA,KAZyB,CAYzB,CAAA,CAAA,CAAA;YACA,aAAA,CAAA,MAAA,EAAA,KAAA,EAAA,MAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YACA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;;AAIA,gCAfa,YAeb;IACA,MAAA,CAAA,UAAM,KAAK;QACX,IAAA,gBAAA,CAAA,oBAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,oBAAA,KAAA,KAAA,CAAA,CAAA,CAAA;YACA,0DAAA;YACA,KAAA,CAAA,cAAA,EAAA,CAAA;;;;;AAOA;IAhBA;;;OAkBA;IACA,6BAAA,YAAA,EAAA,gBAAA;;;;;;;IAMA;;;;OAIA;IACA,4CAAA,GAAA,UAAA,OAAA,EAAA,IAAA;QACA,EAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,eAAA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA;YACA,KAAA,iBAAA,CAAA,QAAA,EArBiC,CAAG;gBAsBpC,IAAA,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;gBACQ,EArBoC,CAqB5C,CAAA,CAAA,QArBsD,CAqBtD,CAAA,CAAA;oBACA,QAAA;wBACA,IAAA,iCAAA,CAAA,IAAA,CAAA,YAAA,EAAA,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;oBACA,IAAA,CAAA,gBArB8B,CAqB9B,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,QAAA,CAAA,CAAA;gBACQ,CAAR;gBACA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA;gBACQ,MAAR,CAAA,QAAA,CAAA;YACA,CAAA;YACA,KAAA,iBAAA,CAAA,MArBgB;gBAsBhB,MAAA,CAAA,IAAA,iBAAA,CArBiC,IAqBjC,CArBqC,YAqBrC,EAAA,IAAA,CAAA,gBAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;YACA,SAAA,CAAA;gBACQ,EAAR,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CArBqB,GAqBrB,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,aAAA,CAAA,IAAA,CAAA,EAAA,EAAA,IAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA;oBACA,IAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;oBACA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,IAAA,CAAA,eAAA,CAAA,CAAA;;;;QAIA,CAvBG;;;;OACA;;IACH;;OA4BA;;;CA9CA;;IAsBA,EAAA,IAAA,EAAA,UAAA,EAAA;CA6BA,CAAA;AACA;;GAIA;;;;CAuBA,GAAA,CAAA;AAhDA;IA4CA;;;;;QAQA,IAAA,CAAA,IAlDkB,GAkDlB,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;;;;;;IAQA;;;;OAKA;IACA,2CAAA,GAAA,UAAA,IAAA,EAAA,SAAA;;;;;IAKA,CAAA;;;;;IAKA,2CAAA,GAAA,UAAA,KAAA,IAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KA5DyE,CA4DzE,CA5D0E,CA4D1E,CAAA;;;;;;IAMA;;;;;;;;;;;OAWA;;;;;;IAMA;;;;OAIA;;;;;IAKA,CAAA;IACA;;;OAGA;IAPA,+CAAA,GAAA,UAAA,cAAA;QASI,IAAJ,gBA5EwB,CA4ExB,EAAA,GAAA,OAAA,cAAA,KAAA,QAAA,GAAA,QAAA,CAAA,aAAA,CAAA,cAAA,CAAA;YACA,cAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;;IAKA,wCAAA,GAAA,UAlFG,IAkFH,IAAA,MAAA,CAAA,IAAA,CAlFuC,UAkFvC,CAAA,CAlF6C,CAAW;;;;;;;;;;;;OA8FxD;IACA,0CAAQ,GAAR,UAAA,EAxFW,EAwFX,IAAA,EAAA,KAAA,EAxF0B,SAwF1B;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YAxFA,IAAA,GAAA,SAAA,SAAA,IAAA,CAAA;YAyFA,IAAU,gBAAV,CAAA,YAAA,GAAA,cAAA,CAAA,SAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,cAAA,CAAA,YAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA;YAxFA,CAAW;YAyFL,IAAN,CAAA,CAAA;gBACA,EAAA,CAAA,YAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;YACA,CAAA;;;;;;;;;;;OAWA;IACA,6CAAA,GAAA,UAAA,EAAA,EAAA,IAAA,EAAA,SAAA;QA7FA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YA8FA,IAAU,gBA7FE,CAAe,YAAI,GA6F/B,cAAA,CAAA,SAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,iBAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;YA7FA,CAAW;YA8FL,IAAN,CAAA,CAAA;gBACA,EAAA,CAAA,eAAA,CAAA,SAAA,SAAA,IAAA,CAAA,CAAA;YACA,CAAA;;;;;;IAMA;;;;;;IAMA;;;;;;;;;;;;OA/FA;IA4GA,sCAAM,GAAN,UAAA,EA3GS,EA2GT,KAAA,EAAA,KAAA,EAAA,KA3G8B;QA4G9B,EAAA,CAAA,CAAA,KAAA,GAAA,mBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,KAAA,EAAA,KAAA,EAAA,CAAA,CAAA,CAAA,KAAA,GAAA,mBAAA,CAAA,SAAA,CAAA,GAAA,WAAA,GAAA,EAAA,CAAA,CAAA;;;;;;;;;;;OAtGA;;;YAmHM,EAAE,CAhHC,KAAC,CAAK,cAgHf,CAAA,KAAA,CAAA,CAAA;QACA,CAAK;QACL,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;IAgBA;;;;;;;;;;;OAaA;IACA,oCAAA,GAAA,UAAA,MA9HuB,EA8HvB,KAAA,EAAA,QA9H6B;QAgI7B,oBAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,OAAA,MAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAAA,CAtIoB,IAAI,CAsIxB,YAtIqC,CAAC,sBAsItC,CAAA,MAAA,EAAA,KAAA,EAAA,sBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;;;;CAjPA;;AAiQA;;;;GAKA;AACA,8BAAA,IAAA,EAAA,QAAA;;;;;;;IAQA;;;;OAKA;IAEA,2CAAA,YAAA,EAAA,gBAAA,EAA4C,SAAS;QAArD,YACI,kBAAJ,YAAA,CAAA,SAMA;QALA,KAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;;;IAKA,CAAA;;;;;;IAMA;;;;OAIA;IACA,yDAAA,GAAA,UAAA,MAAA,EAAA,IAAA;QAEA,IAAA,gBAAA,CAAA,EAAA,GAAA,iBAAA,aAAA,YAAA,MAAA,EAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;OAlJA;IAyKA,2BAAA,YAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,SAAA;QAAA,YACI,kBAAJ,YAAA,CAAA,SA9JA;QA+JI,KAAJ,CAAA,gBAAA,GAAA,gBAAA,CAA6C;QACzC,KAAJ,CAAS,MAAT,GAAkB,MAAM,CAAxB;QACA,KAAM,CAAN,SAAA,GAAmB,SAAnB,CAAA;QACA,KAAM,CAAN,UAAA,GAAA,MAAA,CAAA,gBAAA,EAAA,CAAA;QACA,KAAM,CAAN,gBAAA,CAAA,OAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;QACA,IAAA,MAAA,GAAA,aAAA,CAAA,SAAA,CAAA,EAAA,EAAA,SAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA;QACA,GAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;;;;;;IArKG,CAAH;;;;OA8KA;;;;;;IAMA;;;;;;;;;;;;;;;;;IAiBA;;;;;;;IAOA,CAAA;IACA;;;;IDjeA,sCAAA,GAAA,UAAA,IAAA;;;;;;;;;;;GAkBA;;;;;;IAKA,yBAAA,GAAA;eAAA,kBAAA,GAAA,CAHgD;IAGhD,CAAA;;;;;;;;;;;;IAGA,0CAAA,GAAA,UAAA,OAAA,EAAA,SAAA,EAAA,OAAA;QASA,OAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,EAAA,KAAA,CAAA,CAAA;QACA,MAAA,CAAA,cAAA,OAAA,OAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,EAAA,KAAA,CAAA,EAAA,CAAA,CAAA;;;;;IANA,EAAA,IAAA,EAAA,UAAA,EAAA;CAWA,CAAA;AACA;;GD1CA;;;;;;;;;;GAmBA;AACA,IAAA,WAAA,GAAA;IACE,MAAF;IACE,KAAF,EAAA,IADa;IAEX,UAAS,EADG,IAAA;IAEZ,SADS,EACX,IAAA;IACE,QAAF,EAAA,IAAA;;IAEE,SADS,EACX,IAAA;IACE,UAAF,EAAc,IAAd;IACE,OAAF,EAAA,IADe;IAEb,SAAF,EAAA,IAAA;IACE,QAAF;IACE,OAAF,EAAW,IAAX;IACE,YADY,EACd,IAAA;;IAEE,UAAF,EAAA,IAAA;IACE,aAAF,EADa,IACb;;IAEE,UADU,EACZ,IAAA;IACE,QAAF;IACE,OAAF,EAAA,IAAA;IACE,SAAF,EAAa,IAAb;IACE,SAAF;;IAEE,aADS,EACX,IAAA;IACE,YAAF,EAAA,IAAA;IACE,WAAF,EAAA,IAAA;IACE,cAAF,EAAA,IAAA;IACE,QAAF;;IAEE,WADO,EACT,IAAA;IACA,YAAA,EAAA,IAAA;;;;;;;;;;;GAgBA;AAAA,IAAA,qBAAA,GAAA,IAAA,cAAA,CAAA,qBAAA,CAAA,CAAA;AACA;;GA2BA;;;;;IApBA,CAAA;IACA;;;OAKA;IACA,yCADS,GACT,UAAA,OAAA;QACA,IAAA,gBAAA,CAAA,EAAA,GAAA,IAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QAEI,EAAJ,CAAA,GAAA,CADW,OACX,CAAA,CAAA,GAAA,CAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,CAAA;QACA,EAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;YAAA,EAAA,CAAA,GAAA,CAAA,SAAA,CAAA,CAAO,GAAoC,CAA3C,IAAA,CAAA,SAAA,CAAA,SAAA,CAAA,CAAA,CAAA;QAEA,CAAA;QACA,MAAA,CAAA,EAAA,CAAA;;;;;IACA,EAAA,IAAA,EAAA,UAAA,EAAA;CAOA,CAAA;;;;;AAmBA;IAAA,gDAAA;IAGA;;;;;mDAMA;;;IAAA,CAAA;IACA;;;OAIA;IACA,uCAAM,GAAN,UAAA,SAvBgB;QAwBhB,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA,cAAA,CAAA,SAAA,CAAA,WAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAvBW,KAuBX,CAAA;QACA,CAAA;;;;;;;;;;;;IAaA,+CAAA,GAAA,UAAA,OAAA,EAAA,SAAA,EAAA,OAAA;QAAA,iBAYA;QAXA,IAAA,gBAAA,CAAA,IA5BY,GA4BZ,IAAA,CA5BY,OA4BZ,CAAA,OAAA,EAAA,CAAA;QACA,SAAA,GAAY,SAAZ,CAAA,WAAA,EAAA,CAAA;QACA,MA5BQ,CA4BR,IAAA,CAAA,iBAAA,CAAA;YACM,oEAAN;YACM,IAAN,gBAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OA5B8C,CAAC,WA4B/C,CAAA,OAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,UAAA,QAAA;gBACA,IAAA,CAAA,UAAA,CAAA,cAAA,OAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;OAGA;;;CA/CA,CAAA,kBAAA;;IAkBA,EAAA,IAAA,EAAA,UAAA,EAAA;CAkCA,CAAA;AACA;;;ADzKA,oBAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;GAoBA;AACA,IAAA,aADmB,GACnB,CAAA,KAAA,EAD2C,SAC3C,EAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA,IAAA,oBAAA,GAAA;;;;IAIA,OAAA,EAAA,UAAA,KAAA,IAAA,OAAA,KAAA,CACC,QADD,EAAA,CAAA;;;;GAIA;;;;;;IAKA,yBAAA,GAAA;eAAA,kBAAA,GAAA,CALgD;IAKhD,CAAA;;;;;;;;;;;OAcA;IACA,0CAAA,GAAA,UAAA,OAAA,EAAA,SAAA,EAAA,OAAA;QACA,IAAA,gBAAA,CAAA,WAAA,GAAA,CAAA,CAAA,eAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAIA;IACA,8BAAA,GAAA,UAAA,SAAA;QAEI,IAAJ,gBAAA,CAbU,KAaV,GAAA,SAAA,CAAA,WAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,YAAA,GAAA,KAAA,CAAA,KAAA,EAAA,CAAA;QACI,EAAJ,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,KAb0B,CAa1B,CAAA,IAAA,CAAA,CAAA,YAAA,KAAA,SAAA,IAAA,YAAA,KAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YACM,MAAN,CAAA,IAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,GAbsB,GAAM,eAa5B,CAAA,aAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAbmB,EAanB,CAbkC;QAclC,aAAA,CAAA,OAAA,CAAA,UAAA,YAAA;YACA,IAAA,gBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA;YACA,EAAA,CAAW,CAAX,KAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,KAAA,CAAA,MAbwB,CAAA,KAaxB,EAb6B,CAAI,CAajC,CAAA;;YAEM,CAAN;QACA,CAAK,CAAL,CAAA;QAEI,OAAJ,IAAA,GAAA,CAAA;QACI,EAAJ,CAAA,CAAA,KAAA,CAAA,MAAA,IAAA,CAbY,IAAiB,GAa7B,CAAA,MAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YACA,6EAAA;YACA,MAAA,CAAA,IAAA,CAAA;QACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,+BAhBoB,GAgBpB,UAAA,KAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,EAAA,CAAA;QAhBA,IAAA,gBAAuB,CAAvB,GAAA,GAAA,MAAA,EAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;QAiBA,GAAA,GAAA,GAAA,CAhBY,WAgBZ,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;YACA,GAAA,GAAA,OAAA,CAAA,CAAA,kBAAA;QACA,CAAA;QACA,IAAA,CAAA,EAAA,CAAQ,CAAR,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;YACA,GAAA,GAAA,KAAA,CAAA,CAAA,oDAAA;QACA,CAAA;QACA,aAAA,CAAA,OAAA,CAAA,UAAA,YAAA;YACA,EAAA,CAAA,CAAA,YAAA,IAAA,GAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,YAAA,CAAA,CAAA;gBACA,EAAA,CAhBe,CAgBf,cAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;oBACA,OAAA,IAAA,YAAA,GAAA,GAAA,CAAA;gBACA,CAAA;;;;;;;;;;;OAIA;IAQA,6BAAA,GAAA,UAAA,OAAA,EAAA,OAAA,EAAA,IAAA;QACA,MAAA,CAAA,UAAA,KAAA,CAAA,iBAAA;;;;;;IAMA;;;;OAIA;IACA,6BAAA,GAAA,UAAA,OAAA;QACA,sDAAA;QACA,MAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;YACA,KAAA,KAAA;;YAvBA;gBAyBA,MAxBQ,CAwBR,OAAA,CAAA;QACA,CAAA;;;;;IAtBA,EAAA,IAAA,EAAA,UAAA,EAAA;CA2BA,CAAA;AACA;;GDjJA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CA;;;;;AAMA,IAAA,gBAAA,GAAA,sIAAA,CAAA;AACA;;;GAGA;AACA,qBAHiB,GAGjB;IACA,GAAA,GAAA,MAAA,CAAA,GAAA,CAAA,CAAA;IAEE,EAAF,CAAA,CAAA,GAHS,CAGT,KAAA,CAAA,gBAAA,CAAA,IAAA,GAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA;QACA,MAAA,CAAA,GAAA,CAAA;;;;;AAKA,CAAA;AACA;;;;ADlEA,wBAAA,MAAA;;;;;;;;;;;;;GAqBA;;;;;AAKA,IAAA,GAAA,GAAA,IAAA,CAAA;AACA;;;;AAIA;IACE,EAAF,CAAA,CAAM,YAAN,CANmB;QAAY,MAA/B,CAAsC,YAAtC,CAAA;IAQE,GAAF,GAAA,MAAA,EAAA,CAAA;IACE,gDAAF;IACE,IAAF,gBAAA,CAAA,UAAA,GAAA,GAAA,CAAA,aAAA,CAAA,UAAA,CAAA,CAAA;;;IAGA,IAAA,gBAAA,CAAA,GAAA,GAAA,GAAA,CAAA,kBANsB,EAMtB,CAAA;IACA,YAAA,GAAA,GAAA,CANmB,aAMnB,CAAA,GANwB,EAMxB,MAAA,CAN6C,CAM7C;IACA,EAAA,CAAA,CAAI,YAAJ,IANoB,IAAC,CAMrB,CAN0B,CAM1B;QACI,gGAAJ;QACA,sBAAA;QACA,IAAA,gBAAA,CAAA,IAAA,GAAA,GAAA,CAAA,aAAA,CAAA,MAAA,EAAA,GAAA,CAAA,CAAA;QACA,YAAA,GAAA,GAAA,CAAA,aAAA,CAAA,MAAA,EAAA,GAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;GAEA;AACA,gBAAA,IAAA;;;;;;;AAKA,CAAA;AACA;;;GAGA;AAZA;IAAA,cAAqC;SAArC,UAAqC,EAArC,qBAAqC,EAArC,IAAqC;QAArC,yBAAqC;;IAarC,IAAA,gBAAA,CAAA,GAAA,GAAA,EAAA,CAAA;IACA,GAAA,CAAA,CAAA,UAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,IAAA;QAAA,IAAA,CAAA,aAAA;QACA,GAZS,CAYT,CAAA,IAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;;;;;;AAQA,oDAAA;;;AAIA,6BAAA;AACA,2DAAA;AACA,IAZM,aAYN,GAAA,MAAA,CAAA,wBAAA,CAAA,CAAA;;AAIA,2DAAA;AAIA,IAAA,+BAAA,GAAA,MAAA,CAAA,gDAAA,CAAA,CAAA;AACA,IAAA,gCAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA;;AAGA,8BACI;AAGJ,IAAA,cAAA,GAAA,KAAA,CAAA,+BAAA,EAAA,MAAA,CAAA,kBAAA;IACQ,wGAAR;IAEA,2EAAA,CAAA,CAAA,CAAA;;AAIA,IAZM,eAYN,GAZkB,KAYlB,CAAA,gCAAA,EAAA,MAAA,CAAA,yBAAA;;IAGA,wEAAA,CAAA,CAAA,CAAA;AAEA,IAZM,cAYN,GAAA,KAAA,CAAA,aAAA,EAAA,cAAA,EAAA,eAAA,EAAA,yBAAA,CAAA,CAAA;AAEA,2DAAA;AACA,IAAA,SAAA,GAAA,MAAA,CAAA,8DAAA,CAAA,CAAA;AACA,mEAAA;AACA,IAAA,YAAA,GAAA,MAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;AAUA,oGAAA;;;;;AAKA,IAAA,WAAA,GAAA,KAAA,CAAA,SAAA,EAAA,YAAA,EAAA,UAAA,CAAA,CAAA;AAAA;;;GAyFA;;;;;IAlFA,CAAA;;;;OAIA;IACA,mDAAA,GAAA,UAAA,EAAA;QACA,oFAAA;QACA,6FAAA;QACA,0DAAA;QAZA,IAAA,gBAAA,CAAA,OAAgC,GAAhC,CAAA,CAAA,EAAiC,CAAO,UAAxC,CAAA,CAAA,CAAA;QAaA,OAAA,OAAA,EAAA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,aAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBAZA,IAAA,CAAA,YAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;YAcA,IAAQ,CAAR,EAAA,CAAY,CAZC,GAYb,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,CAZkC,CAAK;gBAavC,IAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,GAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN;YACA,IAAQ,CAAR,CAAA;gBACQ,qCAAR;gBACA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA;YACM,CAAN;;gBAEQ,OAZK,GAYb,CAAA,CAAA,GAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBACA,QAAc,CAZC;YAaf,CAAA;YAEA,OAAA,OAAA,EAAA,CAAA;gBAEQ,wEAAR;gBACA,EAAA,CAAA,CAAU,GAAV,CAAA,aAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;oBACU,IAAV,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;gBACA,CAAS;gBAED,IAAR,gBAAA,CAAA,IAAA,GAAA,qBAAA,CAAA,OAAA,EAAA,gBAAA,CAAA,CAZsD,CAAa,GAYnE,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;oBACA,OAAA,GAAA,IAAA,CAAA;oBAXgB,KAYhB,CAAA;gBACA,CAAA;;;;;IAVG,CAAH;IAgBA;;;OAGA;IACA,+CAAA,GAAA,UAAA,OAAA;QAAA,iBAMA;QALI,IAAJ,gBAAA,CAAA,OAAA,GAAA,GAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA,WAAA,EAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,CAfC,cAeT,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YAdQ,IAeR,CAAA,kBAAA,GAAA,IAfsC,CAAC;YAgBjC,MAAN,CAAA;QACA,CAAA;QACA,IAAA,CAAA,GAAQ,CAAR,IAfa,CAeb,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,GAAQ,CAAR,IAAA,CAAA,OAAA,CAAA,CAAA;QACA,GAAA,CAAA,YAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA,QAAA;;YAEM,EAAN,CAAA,CAAU,CAAV,WAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBAf4B,KAA5B,CAA4B,kBAA5B,GAAsD,IAAtD,CAAA;gBAgBU,MAAV,CAAA;YAfA,CAAA;YAgBM,iEAAN;YACM,EAAN,CAAA,CAAU,SAfM,CAAI,KAepB,CAAA,CAAA;gBACU,KAfK,GAef,WAAA,CAAA,KAAA,CAAA,CAAA;YACM,EAAN,CAAA,CAAU,YAAV,CAAA,KAAA,CAAA,CAAA;gBACU,KAfK,GAef,cAAA,CAAA,KAAA,CAAA,CAAA;YACA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;YACQ,KAfE,CAAG,GAeb,CAfc,IAAK,CAAG,QAetB,CAAA,CAAA;YACA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;;;;IAbG,CAAH;IAmBA;;;OAGA;IACA,6CAAA,GAAA,UAAA,OAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,GAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA,WAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,cAAA,CAAA,cAAA,CAAA,OAAA,CAAA,IAAA,CAAA,aAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAhBG,CAAH;IAsBA;;;;;;;AAcA;;;;GAKA;AACA,+BAAA,IAAA,EAAA,QAAA;;QAGA,MAAA,IAAA,KAAA,CAAA,+DAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;IAEA,MAAA,CAAA,QAAA,CAAA;;;;;;;;;;;;GAcA;AACA,wBAhC4B,KAgC5B;IACA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,EAAA,OAAA,CAAA;SACO,OAhCC,CAiCE,qBADV,EACiC,UADjC,KAAA;QAGA,IAAA,gBAhC4B,CAgC5B,EAAA,GAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAhC4B,CAAC,GAgC7B,GAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,GAAA,CAAA,CAAA,CAAA,EAAA,GAAA,MAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,GAAA,GAAA,MAAA,CAAA,GAAA,OAAA,CAAA,GAAA,GAAA,CAAA;;;;;;;;;;;;;;GAcA;AACA,4BAAA,EAAA;IACE,GAAF,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA,OAjCa,CAAA,UAiCb,CAAA,EAjCkB,QAiClB;QACI,EAAJ,CAAA,CAAQ,QAAR,KAAA,WAAA,IAAA,QAAA,CAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YAjC8B,GAA9B,CAAA,eAAA,CAAA,EAAA,EAAA,QAAA,CAAA,CAAA;QAkCA,CAAA;IACA,CAAA,CAAA,CAAA;;;;;;;;;;;;;GAYA;;;QAII,IAAJ,gBAAA,CAAA,WAAA,GAnCuB,eAmCvB,EAAA,CAAA;QACI,4FAAJ;QAEI,IAAJ,gBAAA,CAAA,UAAA,GAAA,eAAA,GAAA,MAAA,CAAA,eAAA,CAAA,GAAA,EAAA,CAAA;QACA,+FAAA;QACA,8FAAA;QACA,IAAA,gBAAA,CAAA,YAAA,GAAA,CAAA,CAAA;QACA,IAAM,gBAAN,CAAA,UAAA,GAAA,UAAA,CAAA;QAEA,GAAA,CAAM;YACA,EAAN,CAAS,CAnCC,YAAC,KAmCX,CAAA,CAAA,CAAA,CAAA;gBACU,MAAV,IAnCW,KAmCX,CAAA,uDAAA,CAAA,CAAA;;YAEA,YAAA,EAAA,CAAA;YACA,UAAA,GAAA,UAAA,CAAA;YACM,GAAN,CAAA,YAnCmB,CAAI,WAmCvB,EAnCoC,UAmCpC,CAnCqC,CAAW;YAoChD,EAAA,CAAA,CAAA,UAAA,CAnCc,YAmCd,CAAA,CAAA,CAnC4B;gBAqC5B,+CAAA;gBACA,kBAAA,CAAA,WAAA,CAAA,CAAA;;YAGA,UAAA,GAAA,GAAA,CAAA,YAAA,CAAA,WAAA,CAAA,CAAA;QACI,CAAJ,QAAA,UAAA,KAAA,UAAA,EAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAnCqC,IAmCrC,wBAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,IAAA,WAAA,CAAA,CAAA;QAEI,8BAAJ;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,IAAA,WAAA,CAAA;QACA,GAAA,CAAA,CAAA,UAAA,EAAA,KAAA,GAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,EAAA,cAAA,EAAA,IAAA;YAAA,IAAA,KAAA,SAAA;YAEA,GAnCW,CAmCX,WAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;SACA;QAnCA,EAAA,CAAI,CAAQ,SAAZ,EAAA,IAAA,SAAA,CAAA,kBAAA,CAAA,CAAA,CAAA;;QAqCI,CAAJ;QACI,MAnCM,CAAA,QAmCV,CAAA;IACA,CAAG;IACH,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;QDjVA,YAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;GAgCA;AACA,IACM,MADN,GAAA,yBAAA,CAAA;AACA,IACM,kBADN,GAAA,+DAAA,CAAA;AAEA,IAAA,SAAA,GAAA,eAAA,CAAA;AACA,IAAA,SAAA,GACmB,2CADnB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCA;AACA,2BAAA,KAAA;IACA,IAAA,gBAAA,CAAmB,aAAnB,GAAA,IAAA,CAAA;IACA,IAAA,gBAAA,CAAA,aAAA,GAAA,IAAA,CAAA;IACA,GAAA,CAAA,CAAA,IAAW,gBAAX,CAA4B,CAA5B,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;QAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,IAAA,aAAA,CAAA,CAAA,CAAA;YACA,aAAA,GAAA,CAAA,aAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,KAAA,GAAA,IAAA,aAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;IAaE,KAAF,GAAA,MAAA,CAAA,KAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,oCAAA;IACE,EAAF,CAAA,CAAM,CAAC,KAAP,CAAA;QACM,MAAM,CAAZ,EAAA,CAAA;IACA,+FAAA;IACA,yBAAA;IAEE,IAAF,gBAAA,CAAA,QAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAI,CAAJ,QAAa,IACL,WADR,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,QAAA,CAAA,CAAA,CAAA,CAAA;QAEA,KAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,IAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;QAEA,MAAA,CAAA,KAAkB,CAAlB,CAAA,qBAAA;IACA,CAAA;;QDzGA,MAAA,EAAA,CAAA,GAAA,CAAA,4CAAA,KAAA,wCAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuKA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,mBAAA;AAAA,CAAA,IAAA;AAfA;IAAA,4CAAA;IAAA;;;;;;;;IAsBA;;;;OAGA;IACA,mCAAA,GAAA,UAAA,GAAQ,EAAR,KAAA;QACA,EAAA,CAAA,CAAM,KApBK,IAoBX,IAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QApBA,MAAA,CAAA,CAAA,GAAA,CAA2C,CAA3C,CAAA;YAqBA,KAAA,eAAA,CAAA,IAAA;gBACQ,MAAR,CApBe,CAoBf,KAAA,CAAA,CAAA;YACM,KApBK,eAAA,CAAgB,IAoB3B;gBACQ,EAAR,CAAA,CAAY,KApBC,YAAgB,YAoB7B,CApB6B;oBAAe,MAA5C,CAAmD,KAAA,CAAM,qCAAC,CAAqC;gBAqBvF,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,MAoBtC,CApBsC,CAAQ;gBAqBtC,MAAR,CApBe,YAoBf,CApBe,IAoBf,CAAA,IAAA,EAAA,MAAA,CAAA,KAAA,CApBsC,CAoBtC,CApB6C;YAqBvC,KApBK,eAAA,CAAgB,KAoB3B;gBACQ,EAAR,CAAA,CAAY,KApBC,YAAgB,aAoB7B,CApB6B;oBAAgB,MAA7C,CAAoD,KAAA,CAAM,qCAAC,CAAqC;gBAqBxF,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,OAoBtC,CApBsC,CAAS;gBAqBvC,MApBM,CAoBd,aAAA,CAAA,gBAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YACM,KApBK,eAAA,CAAgB,MAoB3B;gBACQ,EAAR,CAAA,CAAY,KApBC,YAAgB,cAoB7B,CAAA;;gBAEA,IAAU,CAAV,iBAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;gBACA,MAAA,IAAA,KAAA,CAAA,uCAAA,CAAA,CAAA;YACA,KAAA,eAAA,CAAA,GAAA;gBACQ,EAAR,CAAA,CAAA,KAAA,YAAA,mBAAA,IAAA,KAAA,YAAA,WAAA,CAAA,CAAA,CAAA;oBACA,uEAAA;oBACY,MAAZ,CAAA,KAAA,CAAA,qCAAA,CAAA;gBACA,CAAA;gBACA,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;gBACQ,MAAR,CAAA,WAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YACA,KAAA,eACY,CADZ,YAAA;gBAEA,EAAA,CAAA,CAAA,KAAA,YAAA,mBAAA,CAAA,CAAA,CAAA;oBACA,MApBkB,CAoBlB,KApBwB,CAoBxB,qCAAA,CAAA;gBACA,CAAA;gBACA,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,aAAA,CAAA,CAAA;;;;;;IAlBG;;;;OA6BH;IACA,4CAAA,GAAA,UAAA,KAAA,EAAA,YAAA;;;;;IAKA,CAAA;;;;;IAKA,kDAAA,GAAA,UA/BG,KA+BH,IAAA,MAAA,CAAA,IAAA,YAAA,CAAA,KAAA,CAAA,CA/BiF,CAAK,CAAC;;;;;IAoCvF,mDAAA,GAAA,UAnCG,KAmCH,IAAA,MAAA,CAAA,IAAA,aAAA,CAAA,KAAA,CAAA,CAnCoF,CAAK,CAAC;;;;;IAwC1F,oDAAA,GAAA,UAAA,KAAA,IAAA,MAAA,CAAA,IAAA,cAvC2E,CAAK,KAuChF,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,iDAAA,GAAA,UAAA,KAAA,IAAA,MAAA,CAAA,IAAA,WAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAzCA;IA4CA,yDAAA,GAAA,UAAA,KAAA;QACA,MAAA,CAAA,IAAA,mBAAA,CAAA,KAAA,CAAA,CAAA;;;CAlGA,CAAA,YAAA;;IAyDA,EAAA,IAAA,EAAA,UAAA,EAAA;CA8CA,CAAA;AACA;;;;IAkBA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,EAAA;;;;GAIA;AA/DA;;;;;;;IAsEA,CAAA;;;;OAIA;IACA,mCApEW,GAoEX,cAAA,CAAA;IACA;;OAEA;IAEA,gCAAA,GAAA;;;;IASA,oBAAA;AAAA,CAAA,AAzFA,IAyFA;AACA;IAAA,wCAAA;IAAA;;IAKA,CAAA;IAJA;;;;IAIA,mBAAA;AAAA,CAAA,AALA,CAAA,aAAA,GAKA;AACA;IAAA,yCAAA;IAAA;;IAKA,CAAA;IAJA;;;;IAIA,oBAAA;AAAA,CAAA,AALA,CAAA,aAAA,GAKA;AACA;IAAA,0CAAA;IAAA;;IAKA,CAAA;IAJA;;;;IAIA,qBAAA;AAAA,CAAA,AALA,CAAA,aAAA,GAKA;AACA;IAAA,uCAAA;IAAA;;IAKA,CAAA;IAJA;;;;IAIA,kBAAA;AAAA,CAAA,AALA,CAAA,aAAA,GAKA;AACA;IAAA,+CAAA;IAAA;;;;;;;;CAAA,CAAA,aAAA;;;;;;;GDxSA;AACA,IAAA,mCAAA,GAAA;;;;;;;;;;;;;;IAcA,EACC,OADD,EAAA,YAAA,EAAA,QAAA,EAAA,gBAAA,EAAA;;;;GAKA;AACA,IAAA,eAAmB,GAAnB,qBAAA,CAAA,YAAA,EAAA,SAAA,EAAA,mCAAA,CAAA,CAAA;AACA;;;;;IAKA,qBAAA,CAAA,IAAA,EAAA,CAAA;AACA,CAAA;AACA;;;;IAIA,MAAA,CAAA,IAAA,YAAA,EAAA,CAAA;AACA,CAAA;AACA;;;;;;AAMA;;;;GAIA;AACA;IACA;;OAGA;;;;;;;;;;;;;;OAcA;IACA,kCAAA,GAAA,UAAA,MAAA;QACA,MAAA,CAAA;YACA,QAAA,EAAA,aAAA;YACA,SAAA,EAAA;gBACA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,CAAA,KAAA,EAAA;;gBANA,2BAAA;aAQA;SACA,CAAA;IACA,CAAA;IACA,oBAAA;AAAA,CAAA,AA5BA,IA4BA;AACA,aAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAA,QAAI,EAAC,IAAL,EAAA,CAPM;gBAQN,SAAA,EAAA;oBACI,8BAAJ;oBACI,EAAJ,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,EAAA,EAAA;oBACI,EAAC,OAPC,EAAQ,qBAOd,EAAA,QAP6C,EAO7C,eAAA,EAP6C,KAO7C,EAAA,IAAA,EAAA;oBACI,EAAC,OAPC,EAAQ,qBAOd,EAAA,QAP6C,EAO7C,eAAA,EAP6C,KAO7C,EAAA,IAAA,EAAA;oBACI,EAAJ,OAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA;oBACI,EAAJ,OAAA,EAAe,qBAAf,EAAA,QAAA,EAAA,mBAAA,EAAA;oBACI,mBAAJ;oBACI,EAAJ,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,mBAAA,EAAA;oBACI,EAAJ,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,mBAAA,EAAA;oBACI,mBAAJ;oBACA,WAAA;oBANA,YAAA;oBAQA,uBAAA;oBACA,IAAA;;;;aALA,EAAA,EAAA;CAUA,CAAA;AACA;;GDjIA;;;;;;;;;;;;;;;;;;;ADgBA;IAJA;;;OAMA;IAEA,mCAAA,SAAA,EAAA,QAAA;;;;IAWA,gCAAA;AAAA,CAAA,AAfA,IAeA;;;;GAKA;;;;;;;;;;;;;;;;;;;;;;;;;OAuBA;IACA,6CAAA,GAAA,UAAA,MAAA;QACAD,IAAAA,gBAAAA,CAdsB,MActBA,GAAAA,MAAAA,IAAAA,MAAAA,CAAAA,QAAAA,CAAAA,CAAAA;QACA,IAAA,gBAAA,CAAA,WAAA,GAAA,kBAAA,CAAA;QACI,sFAAJ;QACI,IAAJ,gBAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,OAAA,CAAA,OAAA,IAAA,IAAA,CAAA;QACI,EAAJ,CAAA,CAAA,MAAA,IAAA,mBAdqC,CAcrC,CAAA,CAAA;YACM,GAAN,CAAU,OAdE,CAAM,OAAO,CAczB,WAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,MAAA,EAAA,CAAA,cAAA,EAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,QAdgB,GAchB,CAAA,CAAA;QACI,OAAJ,QAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAdkB,cAclB,EAAA,GAAA,KAAA,CAAA,GAAA,GAAA,EAAA,CAAA;;;;;QAKA,EAAA,CAAA,CAAM,MAda,IAcnB,mBAAA,CAAA,CAAA,CAAA;YACA,wEAAA;YACA,0BAAA;YAbW,EAcXA;YAbW,sEAcXA;YAEA,CAAA,CAAA,GAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA;;QDpFA,GAAA,CAAA,OAAA,CAAA,GAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GDAA;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;OAuBA;IACA,MAAA,GAAA,UAAA,QAAA;QACA,MAAA,CAAA,UAAA,YAAA;;;;;;;;;;;;;;;IDzCA,YAAA,GAAA,UAAA,IAAA;;;;;;;;;;;;;;;;;;;ADeA;;;;;ADfA;;;;;;;;;;;;;;;;;;ADaA;;;;;;;;;;"}