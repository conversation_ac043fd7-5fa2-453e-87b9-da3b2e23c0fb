{"_args": [["compressible@2.0.10", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "compressible@2.0.10", "_id": "compressible@2.0.10", "_inBundle": false, "_integrity": "sha1-/tocf3YXkScyspv4zyYlKiC57s0=", "_location": "/compressible", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "compressible@2.0.10", "name": "compressible", "escapedName": "compressible", "rawSpec": "2.0.10", "saveSpec": null, "fetchSpec": "2.0.10"}, "_requiredBy": ["/compression"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/compressible/-/compressible-2.0.10.tgz", "_spec": "2.0.10", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/jshttp/compressible/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}], "dependencies": {"mime-db": ">= 1.27.0 < 2"}, "description": "Compressible Content-Type / mime checking", "devDependencies": {"eslint": "3.18.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "2.1.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/compressible#readme", "keywords": ["compress", "gzip", "mime", "content-type"], "license": "MIT", "name": "compressible", "repository": {"type": "git", "url": "git+https://github.com/jshttp/compressible.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --check-leaks"}, "version": "2.0.10"}