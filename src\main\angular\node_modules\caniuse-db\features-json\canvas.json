{"title": "Canvas (basic support)", "description": "Method of generating fast, dynamic graphics using JavaScript.", "spec": "https://html.spec.whatwg.org/multipage/scripting.html#the-canvas-element", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en/Canvas_tutorial", "title": "Tutorial by Mo<PERSON>"}, {"url": "http://glimr.rubyforge.org/cake/canvas.html", "title": "Animation kit"}, {"url": "http://diveintohtml5.info/canvas.html", "title": "Another tutorial"}, {"url": "http://explorercanvas.googlecode.com/", "title": "Implementation for Internet Explorer"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/graphics.js#canvas", "title": "has.js test"}, {"url": "https://skilled.co/html-canvas/", "title": "Canvas Tutorial & Cheat Sheet"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API", "title": "Mozilla Developer Network (MDN) documentation - Canvas API"}], "bugs": [{"description": "The Android browser does not support clipping on HTML5 canvas. See the bug filed here: http://code.google.com/p/android/issues/detail?id=21099"}, {"description": "Older versions of iOS did not support video as a source for the canvas `drawImage()`, though it does appear to work as of iOS 8 [test case](http://jsfiddle.net/zL8KC/)"}, {"description": "Limits of `toDataURL()` for iOS:\r\n- The maximum size for decoded GIF, PNG, and TIFF images is 3 megapixels for devices with less than 256 MB RAM and 5 megapixels for devices with greater or equal than 256 MB RAM.\r\n- The maximum size for a canvas element is 3 megapixels for devices with less than 256 MB RAM and 5 megapixels for devices with greater or equal than 256 MB RAM.\r\n- JavaScript execution time is limited to 10 seconds for each top-level entry point."}, {"description": "In IE 11 `canvas.toDataURL()` does not work if the canvas has images with data URI sources [see bug](https://connect.microsoft.com/IE/Feedback/Details/828416)"}, {"description": "IE 10 canvas doesn't support `setLineDash` or `lineDashOffset` [see bug](https://social.msdn.microsoft.com/Forums/en-US/85007e72-90ad-4bd9-affd-9a24702219e6/canvasrenderingcontext2dsetlinedash-and-linedashoffset-missing?forum=winappswithhtml5) "}, {"description": "IE and Edge does not support globalAlpha for drawImage of SVG graphics. [see bug](https://connect.microsoft.com/IE/feedback/details/1847897/globalalpha-ignored-when-drawing-svg-to-canvas) [see testcase](http://jsfiddle.net/p7b0wmcu/)"}], "categories": ["<PERSON><PERSON>", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #2"}, "android": {"2.1": "a #1", "2.2": "a #1", "2.3": "a #1", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "For screen readers, IE, Chrome & Firefox support the [accessible canvas element sub-DOM](http://www.paciellogroup.com/blog/2012/06/html5-canvas-accessibility-in-firefox-13/).\r\nFirefox & Chrome also support the drawfocus ring.", "notes_by_num": {"1": "Does not support `toDataURL()`", "2": "Opera Mini supports the canvas element, but is unable to play animations or run other more complex applications."}, "usage_perc_y": 94.61, "usage_perc_a": 3.12, "ucprefix": false, "parent": "", "keywords": "toDataURL()", "ie_id": "canvas", "chrome_id": "5100084685438976", "firefox_id": "", "webkit_id": "", "shown": true}