{"_args": [["babel-runtime@6.23.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-runtime@6.23.0", "_id": "babel-runtime@6.23.0", "_inBundle": false, "_integrity": "sha1-CpSJ8UTecO+zzkMArM2zKeL8VDs=", "_location": "/babel-runtime", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-runtime@6.23.0", "name": "babel-runtime", "escapedName": "babel-runtime", "rawSpec": "6.23.0", "saveSpec": null, "fetchSpec": "6.23.0"}, "_requiredBy": ["/babel-generator", "/babel-messages", "/babel-template", "/babel-traverse", "/babel-types", "/common-tags"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-runtime/-/babel-runtime-6.23.0.tgz", "_spec": "6.23.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.10.0"}, "description": "babel selfContained runtime", "devDependencies": {"babel-helpers": "^6.22.0", "babel-plugin-transform-runtime": "^6.23.0"}, "license": "MIT", "name": "babel-runtime", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-runtime"}, "version": "6.23.0"}