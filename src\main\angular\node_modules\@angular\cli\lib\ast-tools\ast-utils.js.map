{"version": 3, "file": "ast-utils.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["lib/ast-tools/ast-utils.ts"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,qCAAyE;AACzE,iCAAmC;AACnC,+CAA6C;AAE7C,sDAAmD;AACnD,qCAAmC;AACnC,kCAAgC;AAChC,gCAA8B;AAC9B,oCAAkC;AAClC,kCAAgC;AAChC,iCAA+B;AAC/B,sCAAoC;AACpC,qCAAmC;AACnC,uCAAqC;AAGrC;;;;EAIE;AACF,mBAA0B,QAAgB;IACxC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,EACvE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClC,CAAC;AAHD,8BAGC;AAGD;;;;GAIG;AACH,wBAA+B,UAAyB;IACtD,MAAM,OAAO,GAAG,IAAI,6BAAa,EAAW,CAAC;IAC7C,IAAI,KAAK,GAAc,CAAC,UAAU,CAAC,CAAC;IAEpC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAE3B,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxC,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,CAAC,QAAQ,EAAE,CAAC;IACnB,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AAChC,CAAC;AAjBD,wCAiBC;AAGD;;;GAGG;AACH,yBAAyB,KAAc,EAAE,MAAe;IACtD,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;AAChC,CAAC;AAGD;;;;;;;;;;;;GAYG;AACH,mCAA0C,KAAgB,EAAE,QAAgB,EACxE,IAAY,EAAE,WAAoB,EAAE,UAA0B;IAChE,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACf,QAAQ,GAAG,gBAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC;IACzE,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,WAAW,IAAI,SAAS,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,+CAA+C,CAAC,CAAC;IAC9F,CAAC;IACD,IAAI,gBAAgB,GAAW,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,WAAW,CAAC;IACrE,MAAM,CAAC,IAAI,qBAAY,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;AAC5D,CAAC;AAXD,8DAWC;AAGD,gCAAuC,OAAsB,EAAE,IAAa;IAC1E,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAE,IAAsB,CAAC,IAAI,CAAC;IACtC,CAAC;IAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;QACpD,MAAM,CAAE,IAAyB,CAAC,IAAI,CAAC;IACzC,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AARD,wDAQC;AAID,iCAAiC,IAA0B,EAC1B,WAA0B;IACzD,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;IAChC,IAAI,UAAU,GAAkB,IAAI,CAAC;IACrC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAChB,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;YAC9B,UAAU,GAAI,EAAuB,CAAC,IAAI,CAAC;YAC3C,KAAK,CAAC;QACR;YACE,MAAM,CAAC,EAAE,CAAC;IACd,CAAC;IAED,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC;IAED,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3B,yDAAyD;YACzD,MAAM,CAAC,EAAE,CAAC;QACZ,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YAC3C,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;gBAC7C,sEAAsE;gBACtE,MAAM,CAAC;oBACL,CAAE,EAAyB,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,UAAU;iBACzD,CAAC;YACJ,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,mDAAmD;gBACnD,MAAM,YAAY,GAAG,EAAqB,CAAC;gBAE3C,MAAM,CAAC,YAAY,CAAC,QAAQ;qBACzB,GAAG,CAAC,CAAC,EAAsB,KAAK,EAAE,CAAC,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;qBACtF,MAAM,CAAC,CAAC,GAA6B,EAAE,IAAY;oBAClD,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;oBACvB,MAAM,CAAC,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;YACX,CAAC;QACH,CAAC;IACH,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,uDAAuD;QACvD,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAGD,8BAAqC,MAAqB,EAAE,UAAkB,EACzC,MAAc;IACjD,MAAM,cAAc,GAChB,gBAAS,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;SACjD,GAAG,CAAC,CAAC,IAA0B,KAAK,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC1E,MAAM,CAAC,CAAC,GAA6B,EAAE,OAAiC;QACvE,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QACD,MAAM,CAAC,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEX,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;SAC1B,MAAM,CAAC,IAAI;QACV,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS;eACnC,IAAqB,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;IAChF,CAAC,CAAC;SACD,GAAG,CAAC,IAAI,IAAK,IAAqB,CAAC,UAA+B,CAAC;SACnE,MAAM,CAAC,IAAI;QACV,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YACrD,MAAM,EAAE,GAAG,IAAI,CAAC,UAA2B,CAAC;YAC5C,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,UAAU;mBACpC,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;QAC3D,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC,CAAC;YAC1E,oDAAoD;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAyC,CAAC;YAC9D,2EAA2E;YAC3E,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;gBACxD,MAAM,CAAC,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,MAAM,QAAQ,GAAmB,MAAM,CAAC,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACpE,MAAM,CAAC,EAAE,KAAK,UAAU,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC;QAC1E,CAAC;QACD,MAAM,CAAC,KAAK,CAAC;IACf,CAAC,CAAC;SACD,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;WACjB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC;SAC/E,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAA+B,CAAC,CAAC;AAClE,CAAC;AAxCD,oDAwCC;AAGD,sCAAsC,YAAoB,EAAE,aAAqB,EAC3C,UAAkB,EAAE,UAAkB;IAC1E,MAAM,MAAM,GAAkB,SAAS,CAAC,YAAY,CAAC,CAAC;IACtD,IAAI,QAAQ,GAAG,oBAAoB,CAAC,MAAM,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;IAEzE,kCAAkC;IAClC,MAAM,CAAC,QAAQ;SACZ,SAAS,EAAE;SACX,IAAI,CAAC,CAAC,IAAgC;QACrC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACV,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAED,+DAA+D;QAC/D,MAAM,CAAC,IAAI,CAAC,UAAU;aACnB,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;aAG7D,MAAM,CAAC,CAAC,IAA2B;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;oBAC3B,MAAM,CAAE,IAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC;gBAClE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;oBAC9B,MAAM,CAAE,IAAyB,CAAC,IAAI,IAAI,aAAa,CAAC;YAC5D,CAAC;YAED,MAAM,CAAC,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;SAED,IAAI,CAAC,CAAC,kBAA6C;QAClD,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QACD,EAAE,CAAC,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAA0B,CAAC;QAElE,kDAAkD;QAClD,EAAE,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACzE,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,WAAwC,CAAC;QACvE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,wBAAwB;YACxB,MAAM,CAAC,UAAU,CAAC;QACpB,CAAC;QACD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;IAC7B,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,IAAa;QAClB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACV,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;YACjF,MAAM,CAAC,IAAI,mBAAU,EAAE,CAAC;QAC1B,CAAC;QAED,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,SAAS,GAAG,IAA6B,CAAC;YAChD,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3D,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,mBAAU,EAAE,CAAC;YAC1B,CAAC;YAED,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,QAAgB,CAAC;QACrB,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvD,uEAAuE;YACvE,SAAS;YACT,IAAI,IAAI,GAAG,IAAkC,CAAC;YAC9C,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC7B,QAAQ,GAAG,KAAK,aAAa,MAAM,UAAU,KAAK,CAAC;YACrD,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnD,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,mDAAmD;gBACnD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACtC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC5B,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,MAAM,UAAU,GAAG,CAAC;gBAC/E,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,QAAQ,GAAG,KAAK,aAAa,MAAM,UAAU,GAAG,CAAC;gBACnD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAC7D,oEAAoE;YACpE,QAAQ,EAAE,CAAC;YACX,QAAQ,GAAG,GAAG,UAAU,EAAE,CAAC;QAC7B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,mDAAmD;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACtC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACzB,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;YAChE,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,QAAQ,GAAG,KAAK,UAAU,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,qBAAY,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClE,MAAM,YAAY,GAAW,0BAAY,CACvC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,oBAAW,CAAC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;EAGE;AACF,gCAAuC,UAAkB,EAAE,cAAsB,EAC1C,UAAkB;IAEvD,MAAM,CAAC,4BAA4B,CAAC,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AAC9F,CAAC;AAJD,wDAIC;AAED;;;GAGG;AACH,2BAAkC,UAAkB,EAAE,cAAsB,EAC1C,UAAkB;IAElD,MAAM,CAAC,4BAA4B,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AACzF,CAAC;AAJD,8CAIC;AAED;;GAEG;AACH,6BAAoC,UAAkB,EAAE,cAAsB,EAC1C,UAAkB;IACpD,MAAM,CAAC,4BAA4B,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AAC3F,CAAC;AAHD,kDAGC;AAED;;GAEG;AACH,2BAAkC,UAAkB,EAAE,cAAsB,EAC1C,UAAkB;IAClD,MAAM,CAAC,4BAA4B,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AACzF,CAAC;AAHD,8CAGC"}