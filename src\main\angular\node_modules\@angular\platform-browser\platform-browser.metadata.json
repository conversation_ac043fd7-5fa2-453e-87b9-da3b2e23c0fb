{"__symbolic": "module", "version": 3, "metadata": {"ɵa": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "ɵb": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "reference", "name": "document"}}, "ɵc": {"__symbolic": "function"}, "ɵd": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}]}], "supports": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}], "addGlobalEventListener": [{"__symbolic": "method"}]}}, "ɵe": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Dom<PERSON><PERSON><PERSON>zer"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "sanitize": [{"__symbolic": "method"}], "checkNotSafeValue": [{"__symbolic": "method"}], "bypassSecurityTrustHtml": [{"__symbolic": "method"}], "bypassSecurityTrustStyle": [{"__symbolic": "method"}], "bypassSecurityTrustScript": [{"__symbolic": "method"}], "bypassSecurityTrustUrl": [{"__symbolic": "method"}], "bypassSecurityTrustResourceUrl": [{"__symbolic": "method"}]}}, "ɵf": {"__symbolic": "function", "parameters": ["transitionId", "document", "injector"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 20, "character": 9, "module": "./src/browser/server-transition"}}, "ɵg": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_INITIALIZER"}, "useFactory": {"__symbolic": "reference", "name": "ɵf"}, "deps": [{"__symbolic": "reference", "name": "ɵTRANSITION_ID"}, {"__symbolic": "reference", "name": "DOCUMENT"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}], "multi": true}], "BrowserModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "ɵBROWSER_SANITIZATION_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "useFactory": {"__symbolic": "reference", "name": "ɵa"}, "deps": []}, {"provide": {"__symbolic": "reference", "name": "EVENT_MANAGER_PLUGINS"}, "useClass": {"__symbolic": "reference", "name": "ɵDomEventsPlugin"}, "multi": true}, {"provide": {"__symbolic": "reference", "name": "EVENT_MANAGER_PLUGINS"}, "useClass": {"__symbolic": "reference", "name": "ɵKeyEventsPlugin"}, "multi": true}, {"provide": {"__symbolic": "reference", "name": "EVENT_MANAGER_PLUGINS"}, "useClass": {"__symbolic": "reference", "name": "ɵHammerGesturesPlugin"}, "multi": true}, {"provide": {"__symbolic": "reference", "name": "HAMMER_GESTURE_CONFIG"}, "useClass": {"__symbolic": "reference", "name": "HammerGestureConfig"}}, {"__symbolic": "reference", "name": "ɵDomRendererFactory2"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "RendererFactory2"}, "useExisting": {"__symbolic": "reference", "name": "ɵDomRendererFactory2"}}, {"provide": {"__symbolic": "reference", "name": "ɵSharedStylesHost"}, "useExisting": {"__symbolic": "reference", "name": "ɵDomSharedStylesHost"}}, {"__symbolic": "reference", "name": "ɵDomSharedStylesHost"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Testability"}, {"__symbolic": "reference", "name": "EventManager"}, {"__symbolic": "reference", "name": "ɵELEMENT_PROBE_PROVIDERS"}, {"__symbolic": "reference", "name": "Meta"}, {"__symbolic": "reference", "name": "Title"}], "exports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationModule"}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf"}}]], "parameters": [{"__symbolic": "reference", "name": "BrowserModule"}]}]}, "statics": {"withServerTransition": {"__symbolic": "function", "parameters": ["params"], "value": {"ngModule": {"__symbolic": "reference", "name": "BrowserModule"}, "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_ID"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "params"}, "member": "appId"}}, {"provide": {"__symbolic": "reference", "name": "ɵTRANSITION_ID"}, "useExisting": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_ID"}}, {"__symbolic": "reference", "name": "ɵg"}]}}}}, "platformBrowser": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "platformCore"}, "browser", {"__symbolic": "reference", "name": "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS"}]}, "Meta": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "addTag": [{"__symbolic": "method"}], "addTags": [{"__symbolic": "method"}], "getTag": [{"__symbolic": "method"}], "getTags": [{"__symbolic": "method"}], "updateTag": [{"__symbolic": "method"}], "removeTag": [{"__symbolic": "method"}], "removeTagElement": [{"__symbolic": "method"}], "_getOrCreateElement": [{"__symbolic": "method"}], "_setMetaElementAttributes": [{"__symbolic": "method"}], "_parseSelector": [{"__symbolic": "method"}], "_containsAttributes": [{"__symbolic": "method"}]}}, "Title": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "getTitle": [{"__symbolic": "method"}], "setTitle": [{"__symbolic": "method"}]}}, "disableDebugTools": {"__symbolic": "function"}, "enableDebugTools": {"__symbolic": "function"}, "By": {"__symbolic": "class", "members": {}, "statics": {"all": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 26, "character": 49, "module": "./src/dom/debug/by"}}, "css": {"__symbolic": "function", "parameters": ["selector"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 36, "character": 11, "module": "./src/dom/debug/by"}}, "directive": {"__symbolic": "function", "parameters": ["type"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 51, "character": 11, "module": "./src/dom/debug/by"}}}}, "NgProbeToken": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "DOCUMENT": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["DocumentToken"]}, "EVENT_MANAGER_PLUGINS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["EventManagerPlugins"]}, "EventManager": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "EVENT_MANAGER_PLUGINS"}]}], null], "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵd"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}]}], "addEventListener": [{"__symbolic": "method"}], "addGlobalEventListener": [{"__symbolic": "method"}], "getZone": [{"__symbolic": "method"}], "_findPluginFor": [{"__symbolic": "method"}]}}, "HAMMER_GESTURE_CONFIG": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["HammerGestureConfig"]}, "HammerGestureConfig": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"buildHammer": [{"__symbolic": "method"}]}}, "DomSanitizer": {"__symbolic": "class", "members": {"sanitize": [{"__symbolic": "method"}], "bypassSecurityTrustHtml": [{"__symbolic": "method"}], "bypassSecurityTrustStyle": [{"__symbolic": "method"}], "bypassSecurityTrustScript": [{"__symbolic": "method"}], "bypassSecurityTrustUrl": [{"__symbolic": "method"}], "bypassSecurityTrustResourceUrl": [{"__symbolic": "method"}]}}, "SafeHtml": {"__symbolic": "interface"}, "SafeResourceUrl": {"__symbolic": "interface"}, "SafeScript": {"__symbolic": "interface"}, "SafeStyle": {"__symbolic": "interface"}, "SafeUrl": {"__symbolic": "interface"}, "SafeValue": {"__symbolic": "interface"}, "ɵBROWSER_SANITIZATION_PROVIDERS": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "Sanitizer"}, "useExisting": {"__symbolic": "reference", "name": "Dom<PERSON><PERSON><PERSON>zer"}}, {"provide": {"__symbolic": "reference", "name": "Dom<PERSON><PERSON><PERSON>zer"}, "useClass": {"__symbolic": "reference", "name": "ɵe"}}], "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PLATFORM_ID"}, "useValue": {"__symbolic": "reference", "module": "@angular/common", "name": "ɵPLATFORM_BROWSER_ID"}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PLATFORM_INITIALIZER"}, "useValue": {"__symbolic": "reference", "name": "ɵinitDomAdapter"}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "PlatformLocation"}, "useClass": {"__symbolic": "reference", "name": "ɵBrowserPlatformLocation"}}, {"provide": {"__symbolic": "reference", "name": "DOCUMENT"}, "useFactory": {"__symbolic": "reference", "name": "ɵb"}, "deps": []}], "ɵinitDomAdapter": {"__symbolic": "function"}, "ɵBrowserDomAdapter": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵh"}, "members": {"parse": [{"__symbolic": "method"}], "hasProperty": [{"__symbolic": "method"}], "setProperty": [{"__symbolic": "method"}], "getProperty": [{"__symbolic": "method"}], "invoke": [{"__symbolic": "method"}], "logError": [{"__symbolic": "method"}], "log": [{"__symbolic": "method"}], "logGroup": [{"__symbolic": "method"}], "logGroupEnd": [{"__symbolic": "method"}], "contains": [{"__symbolic": "method"}], "querySelector": [{"__symbolic": "method"}], "querySelectorAll": [{"__symbolic": "method"}], "on": [{"__symbolic": "method"}], "onAndCancel": [{"__symbolic": "method"}], "dispatchEvent": [{"__symbolic": "method"}], "createMouseEvent": [{"__symbolic": "method"}], "createEvent": [{"__symbolic": "method"}], "preventDefault": [{"__symbolic": "method"}], "isPrevented": [{"__symbolic": "method"}], "getInnerHTML": [{"__symbolic": "method"}], "getTemplateContent": [{"__symbolic": "method"}], "getOuterHTML": [{"__symbolic": "method"}], "nodeName": [{"__symbolic": "method"}], "nodeValue": [{"__symbolic": "method"}], "type": [{"__symbolic": "method"}], "content": [{"__symbolic": "method"}], "firstChild": [{"__symbolic": "method"}], "nextSibling": [{"__symbolic": "method"}], "parentElement": [{"__symbolic": "method"}], "childNodes": [{"__symbolic": "method"}], "childNodesAsList": [{"__symbolic": "method"}], "clearNodes": [{"__symbolic": "method"}], "appendChild": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "replaceChild": [{"__symbolic": "method"}], "remove": [{"__symbolic": "method"}], "insertBefore": [{"__symbolic": "method"}], "insertAllBefore": [{"__symbolic": "method"}], "insertAfter": [{"__symbolic": "method"}], "setInnerHTML": [{"__symbolic": "method"}], "getText": [{"__symbolic": "method"}], "setText": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}], "setValue": [{"__symbolic": "method"}], "getChecked": [{"__symbolic": "method"}], "setChecked": [{"__symbolic": "method"}], "createComment": [{"__symbolic": "method"}], "createTemplate": [{"__symbolic": "method"}], "createElement": [{"__symbolic": "method"}], "createElementNS": [{"__symbolic": "method"}], "createTextNode": [{"__symbolic": "method"}], "createScriptTag": [{"__symbolic": "method"}], "createStyleElement": [{"__symbolic": "method"}], "createShadowRoot": [{"__symbolic": "method"}], "getShadowRoot": [{"__symbolic": "method"}], "getHost": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "getElementsByClassName": [{"__symbolic": "method"}], "getElementsByTagName": [{"__symbolic": "method"}], "classList": [{"__symbolic": "method"}], "addClass": [{"__symbolic": "method"}], "removeClass": [{"__symbolic": "method"}], "hasClass": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "removeStyle": [{"__symbolic": "method"}], "getStyle": [{"__symbolic": "method"}], "hasStyle": [{"__symbolic": "method"}], "tagName": [{"__symbolic": "method"}], "attributeMap": [{"__symbolic": "method"}], "hasAttribute": [{"__symbolic": "method"}], "hasAttributeNS": [{"__symbolic": "method"}], "getAttribute": [{"__symbolic": "method"}], "getAttributeNS": [{"__symbolic": "method"}], "setAttribute": [{"__symbolic": "method"}], "setAttributeNS": [{"__symbolic": "method"}], "removeAttribute": [{"__symbolic": "method"}], "removeAttributeNS": [{"__symbolic": "method"}], "templateAwareRoot": [{"__symbolic": "method"}], "createHtmlDocument": [{"__symbolic": "method"}], "getBoundingClientRect": [{"__symbolic": "method"}], "getTitle": [{"__symbolic": "method"}], "setTitle": [{"__symbolic": "method"}], "elementMatches": [{"__symbolic": "method"}], "isTemplateElement": [{"__symbolic": "method"}], "isTextNode": [{"__symbolic": "method"}], "isCommentNode": [{"__symbolic": "method"}], "isElementNode": [{"__symbolic": "method"}], "hasShadowRoot": [{"__symbolic": "method"}], "isShadowRoot": [{"__symbolic": "method"}], "importIntoDoc": [{"__symbolic": "method"}], "adoptNode": [{"__symbolic": "method"}], "getHref": [{"__symbolic": "method"}], "getEventKey": [{"__symbolic": "method"}], "getGlobalEventTarget": [{"__symbolic": "method"}], "getHistory": [{"__symbolic": "method"}], "getLocation": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "resetBaseElement": [{"__symbolic": "method"}], "getUserAgent": [{"__symbolic": "method"}], "setData": [{"__symbolic": "method"}], "getData": [{"__symbolic": "method"}], "getComputedStyle": [{"__symbolic": "method"}], "supportsWebAnimation": [{"__symbolic": "method"}], "performanceNow": [{"__symbolic": "method"}], "supportsCookies": [{"__symbolic": "method"}], "getCookie": [{"__symbolic": "method"}], "setCookie": [{"__symbolic": "method"}]}}, "ɵBrowserPlatformLocation": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/common", "name": "PlatformLocation"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "_init": [{"__symbolic": "method"}], "getBaseHrefFromDOM": [{"__symbolic": "method"}], "onPopState": [{"__symbolic": "method"}], "onHashChange": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}]}}, "ɵTRANSITION_ID": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["TRANSITION_ID"]}, "ɵBrowserGetTestability": {"__symbolic": "class", "members": {"addToWindow": [{"__symbolic": "method"}], "findTestabilityInTree": [{"__symbolic": "method"}]}}, "ɵELEMENT_PROBE_PROVIDERS": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_INITIALIZER"}, "useFactory": {"__symbolic": "reference", "name": "ɵc"}, "deps": [[{"__symbolic": "reference", "name": "NgProbeToken"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}], [{"__symbolic": "reference", "module": "@angular/core", "name": "NgProbeToken"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}]], "multi": true}], "ɵDomAdapter": {"__symbolic": "class", "members": {"hasProperty": [{"__symbolic": "method"}], "setProperty": [{"__symbolic": "method"}], "getProperty": [{"__symbolic": "method"}], "invoke": [{"__symbolic": "method"}], "logError": [{"__symbolic": "method"}], "log": [{"__symbolic": "method"}], "logGroup": [{"__symbolic": "method"}], "logGroupEnd": [{"__symbolic": "method"}], "contains": [{"__symbolic": "method"}], "parse": [{"__symbolic": "method"}], "querySelector": [{"__symbolic": "method"}], "querySelectorAll": [{"__symbolic": "method"}], "on": [{"__symbolic": "method"}], "onAndCancel": [{"__symbolic": "method"}], "dispatchEvent": [{"__symbolic": "method"}], "createMouseEvent": [{"__symbolic": "method"}], "createEvent": [{"__symbolic": "method"}], "preventDefault": [{"__symbolic": "method"}], "isPrevented": [{"__symbolic": "method"}], "getInnerHTML": [{"__symbolic": "method"}], "getTemplateContent": [{"__symbolic": "method"}], "getOuterHTML": [{"__symbolic": "method"}], "nodeName": [{"__symbolic": "method"}], "nodeValue": [{"__symbolic": "method"}], "type": [{"__symbolic": "method"}], "content": [{"__symbolic": "method"}], "firstChild": [{"__symbolic": "method"}], "nextSibling": [{"__symbolic": "method"}], "parentElement": [{"__symbolic": "method"}], "childNodes": [{"__symbolic": "method"}], "childNodesAsList": [{"__symbolic": "method"}], "clearNodes": [{"__symbolic": "method"}], "appendChild": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "replaceChild": [{"__symbolic": "method"}], "remove": [{"__symbolic": "method"}], "insertBefore": [{"__symbolic": "method"}], "insertAllBefore": [{"__symbolic": "method"}], "insertAfter": [{"__symbolic": "method"}], "setInnerHTML": [{"__symbolic": "method"}], "getText": [{"__symbolic": "method"}], "setText": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}], "setValue": [{"__symbolic": "method"}], "getChecked": [{"__symbolic": "method"}], "setChecked": [{"__symbolic": "method"}], "createComment": [{"__symbolic": "method"}], "createTemplate": [{"__symbolic": "method"}], "createElement": [{"__symbolic": "method"}], "createElementNS": [{"__symbolic": "method"}], "createTextNode": [{"__symbolic": "method"}], "createScriptTag": [{"__symbolic": "method"}], "createStyleElement": [{"__symbolic": "method"}], "createShadowRoot": [{"__symbolic": "method"}], "getShadowRoot": [{"__symbolic": "method"}], "getHost": [{"__symbolic": "method"}], "getDistributedNodes": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "getElementsByClassName": [{"__symbolic": "method"}], "getElementsByTagName": [{"__symbolic": "method"}], "classList": [{"__symbolic": "method"}], "addClass": [{"__symbolic": "method"}], "removeClass": [{"__symbolic": "method"}], "hasClass": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "removeStyle": [{"__symbolic": "method"}], "getStyle": [{"__symbolic": "method"}], "hasStyle": [{"__symbolic": "method"}], "tagName": [{"__symbolic": "method"}], "attributeMap": [{"__symbolic": "method"}], "hasAttribute": [{"__symbolic": "method"}], "hasAttributeNS": [{"__symbolic": "method"}], "getAttribute": [{"__symbolic": "method"}], "getAttributeNS": [{"__symbolic": "method"}], "setAttribute": [{"__symbolic": "method"}], "setAttributeNS": [{"__symbolic": "method"}], "removeAttribute": [{"__symbolic": "method"}], "removeAttributeNS": [{"__symbolic": "method"}], "templateAwareRoot": [{"__symbolic": "method"}], "createHtmlDocument": [{"__symbolic": "method"}], "getBoundingClientRect": [{"__symbolic": "method"}], "getTitle": [{"__symbolic": "method"}], "setTitle": [{"__symbolic": "method"}], "elementMatches": [{"__symbolic": "method"}], "isTemplateElement": [{"__symbolic": "method"}], "isTextNode": [{"__symbolic": "method"}], "isCommentNode": [{"__symbolic": "method"}], "isElementNode": [{"__symbolic": "method"}], "hasShadowRoot": [{"__symbolic": "method"}], "isShadowRoot": [{"__symbolic": "method"}], "importIntoDoc": [{"__symbolic": "method"}], "adoptNode": [{"__symbolic": "method"}], "getHref": [{"__symbolic": "method"}], "getEventKey": [{"__symbolic": "method"}], "resolveAndSetHref": [{"__symbolic": "method"}], "supportsDOMEvents": [{"__symbolic": "method"}], "supportsNativeShadowDOM": [{"__symbolic": "method"}], "getGlobalEventTarget": [{"__symbolic": "method"}], "getHistory": [{"__symbolic": "method"}], "getLocation": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "resetBaseElement": [{"__symbolic": "method"}], "getUserAgent": [{"__symbolic": "method"}], "setData": [{"__symbolic": "method"}], "getComputedStyle": [{"__symbolic": "method"}], "getData": [{"__symbolic": "method"}], "supportsWebAnimation": [{"__symbolic": "method"}], "performanceNow": [{"__symbolic": "method"}], "getAnimationPrefix": [{"__symbolic": "method"}], "getTransitionEnd": [{"__symbolic": "method"}], "supportsAnimation": [{"__symbolic": "method"}], "supportsCookies": [{"__symbolic": "method"}], "getCookie": [{"__symbolic": "method"}], "setCookie": [{"__symbolic": "method"}]}}, "ɵgetDOM": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 10, "character": 4, "context": {"name": "_DOM"}, "module": "./src/dom/dom_adapter"}}, "ɵsetRootDomAdapter": {"__symbolic": "function"}, "ɵDomRendererFactory2": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "EventManager"}, {"__symbolic": "reference", "name": "ɵDomSharedStylesHost"}]}], "createRenderer": [{"__symbolic": "method"}], "begin": [{"__symbolic": "method"}], "end": [{"__symbolic": "method"}]}}, "ɵNAMESPACE_URIS": {"svg": "http://www.w3.org/2000/svg", "xhtml": "http://www.w3.org/1999/xhtml", "xlink": "http://www.w3.org/1999/xlink", "xml": "http://www.w3.org/XML/1998/namespace", "xmlns": "http://www.w3.org/2000/xmlns/"}, "ɵflattenStyles": {"__symbolic": "function"}, "ɵshimContentAttribute": {"__symbolic": "function", "parameters": ["componentShortId"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 21, "character": 6, "context": {"name": "COMPONENT_REGEX"}, "module": "./src/dom/dom_renderer"}}, "ɵshimHostAttribute": {"__symbolic": "function", "parameters": ["componentShortId"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 21, "character": 6, "context": {"name": "COMPONENT_REGEX"}, "module": "./src/dom/dom_renderer"}}, "ɵDomEventsPlugin": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵd"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "supports": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}]}}, "ɵHammerGesturesPlugin": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵd"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "HAMMER_GESTURE_CONFIG"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "HammerGestureConfig"}]}], "supports": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}], "isCustomEvent": [{"__symbolic": "method"}]}}, "ɵKeyEventsPlugin": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵd"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "supports": [{"__symbolic": "method"}], "addEventListener": [{"__symbolic": "method"}]}, "statics": {"eventCallback": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON>", "handler", "zone"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 96, "character": 11, "module": "./src/dom/events/key_events"}}}}, "ɵDomSharedStylesHost": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵSharedStylesHost"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "_addStylesToHost": [{"__symbolic": "method"}], "addHost": [{"__symbolic": "method"}], "removeHost": [{"__symbolic": "method"}], "onStylesAdded": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "ɵSharedStylesHost": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"addStyles": [{"__symbolic": "method"}], "onStylesAdded": [{"__symbolic": "method"}], "getAllStyles": [{"__symbolic": "method"}]}}, "VERSION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Version"}, "arguments": ["4.2.5"]}, "ɵh": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵDomAdapter"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "getDistributedNodes": [{"__symbolic": "method"}], "resolveAndSetHref": [{"__symbolic": "method"}], "supportsDOMEvents": [{"__symbolic": "method"}], "supportsNativeShadowDOM": [{"__symbolic": "method"}], "getAnimationPrefix": [{"__symbolic": "method"}], "getTransitionEnd": [{"__symbolic": "method"}], "supportsAnimation": [{"__symbolic": "method"}]}}}, "origins": {"ɵa": "./src/browser", "ɵb": "./src/browser", "ɵc": "./src/dom/debug/ng_probe", "ɵd": "./src/dom/events/event_manager", "ɵe": "./src/security/dom_sanitization_service", "ɵf": "./src/browser/server-transition", "ɵg": "./src/browser/server-transition", "BrowserModule": "./src/browser", "platformBrowser": "./src/browser", "Meta": "./src/browser/meta", "MetaDefinition": "./src/browser/meta", "Title": "./src/browser/title", "disableDebugTools": "./src/browser/tools/tools", "enableDebugTools": "./src/browser/tools/tools", "By": "./src/dom/debug/by", "NgProbeToken": "./src/dom/debug/ng_probe", "DOCUMENT": "./src/dom/dom_tokens", "EVENT_MANAGER_PLUGINS": "./src/dom/events/event_manager", "EventManager": "./src/dom/events/event_manager", "HAMMER_GESTURE_CONFIG": "./src/dom/events/hammer_gestures", "HammerGestureConfig": "./src/dom/events/hammer_gestures", "DomSanitizer": "./src/security/dom_sanitization_service", "SafeHtml": "./src/security/dom_sanitization_service", "SafeResourceUrl": "./src/security/dom_sanitization_service", "SafeScript": "./src/security/dom_sanitization_service", "SafeStyle": "./src/security/dom_sanitization_service", "SafeUrl": "./src/security/dom_sanitization_service", "SafeValue": "./src/security/dom_sanitization_service", "ɵBROWSER_SANITIZATION_PROVIDERS": "./src/browser", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS": "./src/browser", "ɵinitDomAdapter": "./src/browser", "ɵBrowserDomAdapter": "./src/browser/browser_adapter", "ɵBrowserPlatformLocation": "./src/browser/location/browser_platform_location", "ɵTRANSITION_ID": "./src/browser/server-transition", "ɵBrowserGetTestability": "./src/browser/testability", "ɵELEMENT_PROBE_PROVIDERS": "./src/dom/debug/ng_probe", "ɵDomAdapter": "./src/dom/dom_adapter", "ɵgetDOM": "./src/dom/dom_adapter", "ɵsetRootDomAdapter": "./src/dom/dom_adapter", "ɵDomRendererFactory2": "./src/dom/dom_renderer", "ɵNAMESPACE_URIS": "./src/dom/dom_renderer", "ɵflattenStyles": "./src/dom/dom_renderer", "ɵshimContentAttribute": "./src/dom/dom_renderer", "ɵshimHostAttribute": "./src/dom/dom_renderer", "ɵDomEventsPlugin": "./src/dom/events/dom_events", "ɵHammerGesturesPlugin": "./src/dom/events/hammer_gestures", "ɵKeyEventsPlugin": "./src/dom/events/key_events", "ɵDomSharedStylesHost": "./src/dom/shared_styles_host", "ɵSharedStylesHost": "./src/dom/shared_styles_host", "VERSION": "./src/version", "ɵh": "./src/browser/generic_browser_adapter"}, "importAs": "@angular/platform-browser"}