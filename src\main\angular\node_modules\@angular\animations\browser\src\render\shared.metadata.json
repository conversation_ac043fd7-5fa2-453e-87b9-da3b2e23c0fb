[{"__symbolic": "module", "version": 3, "metadata": {"optimizeGroupPlayer": {"__symbolic": "function"}, "normalizeKeyframes": {"__symbolic": "function"}, "listenOnPlayer": {"__symbolic": "function"}, "copyAnimationEvent": {"__symbolic": "function"}, "makeAnimationEvent": {"__symbolic": "function", "parameters": ["element", "triggerName", "fromState", "toState", "phaseName", "totalTime"], "value": {"element": {"__symbolic": "reference", "name": "element"}, "triggerName": {"__symbolic": "reference", "name": "triggerName"}, "fromState": {"__symbolic": "reference", "name": "fromState"}, "toState": {"__symbolic": "reference", "name": "toState"}, "phaseName": {"__symbolic": "reference", "name": "phaseName"}, "totalTime": {"__symbolic": "reference", "name": "totalTime"}}, "defaults": [null, null, null, null, "", 0]}, "getOrSetAsInMap": {"__symbolic": "function"}, "parseTimelineCommand": {"__symbolic": "function"}, "matchesElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 123, "character": 4, "context": {"name": "_matches"}}, "containsElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 122, "character": 4, "context": {"name": "_contains"}}, "invokeQuery": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 125, "character": 4, "context": {"name": "_query"}}}}, {"__symbolic": "module", "version": 1, "metadata": {"optimizeGroupPlayer": {"__symbolic": "function"}, "normalizeKeyframes": {"__symbolic": "function"}, "listenOnPlayer": {"__symbolic": "function"}, "copyAnimationEvent": {"__symbolic": "function"}, "makeAnimationEvent": {"__symbolic": "function", "parameters": ["element", "triggerName", "fromState", "toState", "phaseName", "totalTime"], "value": {"element": {"__symbolic": "reference", "name": "element"}, "triggerName": {"__symbolic": "reference", "name": "triggerName"}, "fromState": {"__symbolic": "reference", "name": "fromState"}, "toState": {"__symbolic": "reference", "name": "toState"}, "phaseName": {"__symbolic": "reference", "name": "phaseName"}, "totalTime": {"__symbolic": "reference", "name": "totalTime"}}, "defaults": [null, null, null, null, "", 0]}, "getOrSetAsInMap": {"__symbolic": "function"}, "parseTimelineCommand": {"__symbolic": "function"}, "matchesElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 123, "character": 4, "context": {"name": "_matches"}}, "containsElement": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 122, "character": 4, "context": {"name": "_contains"}}, "invokeQuery": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 125, "character": 4, "context": {"name": "_query"}}}}]