{"version": 3, "file": "base-href-webpack-plugin.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["lib/base-href-webpack/base-href-webpack-plugin.ts"], "names": [], "mappings": ";;AAIA;IACE,YAA4B,OAAqC;QAArC,YAAO,GAAP,OAAO,CAA8B;IAAI,CAAC;IAEtE,KAAK,CAAC,QAAa;QACjB,mCAAmC;QACnC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC;QACT,CAAC;QAED,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,WAAgB;YAC9C,WAAW,CAAC,MAAM,CAChB,4CAA4C,EAC5C,CAAC,cAAmB,EAAE,QAAkB;gBACtC,mCAAmC;gBACnC,MAAM,YAAY,GAAG,YAAY,CAAC;gBAClC,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC/D,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;oBACpB,4CAA4C;oBAC5C,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAC/C,SAAS,EAAE,IAAI,GAAG,eAAe,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAC3D,CAAC;gBACJ,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,wCAAwC;oBACxC,MAAM,eAAe,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAC/C,aAAa,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CACjD,CAAC;oBACF,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBACnF,CAAC;gBAED,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACjC,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAlCD,sDAkCC"}