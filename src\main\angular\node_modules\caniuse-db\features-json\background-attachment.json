{"title": "CSS background-attachment", "description": "Method of defining how a background image is attached to a scrollable element. Values include `scroll` (default), `fixed` and `local`.", "spec": "http://www.w3.org/TR/css3-background/#the-background-attachment", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/background-attachment", "title": "Mozilla Developer Network (MDN) documentation - background-attachment"}], "bugs": [{"description": "iOS has an issue preventing `background-position: fixed` from being used with `background-size: cover` - [see details](http://stackoverflow.com/questions/21476380/background-size-on-ios)"}, {"description": "Chrome has an issue that occurs when using the will-change property on a selector which also has `background-attachment: fixed` defined. It causes the image to get cut off and gain whitespace around it. "}], "categories": ["CSS"], "stats": {"ie": {"5.5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "a #1", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #2 #3", "6.0-6.1": "a #2 #3", "7.0-7.1": "a #2 #3", "8": "a #2 #3", "8.1-8.4": "a #2 #3", "9.0-9.2": "a #2 #3", "9.3": "a #2 #3", "10.0-10.2": "a #2 #3", "10.3": "a #2 #3", "11": "a #2 #3"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "a #2", "10": "a #2"}, "op_mob": {"10": "a #1", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "a #4"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "a #1"}, "samsung": {"4": "n", "5": "a #4"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "a #4"}}, "notes": "Most mobile devices have a delay in updating the background position after scrolling a page with `fixed` backgrounds.", "notes_by_num": {"1": "Partial support refers to supporting `fixed` but not `local`", "2": "Partial support refers to supporting `local` but not `fixed`", "3": "Only supports `local` when `-webkit-overflow-scrolling: touch` is _not_ used", "4": "Does not support `fixed`, and due [to a bug](https://crbug.com/627037) only supports `local` if a `border-radius` is set on the element."}, "usage_perc_y": 40.32, "usage_perc_a": 49.4, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}