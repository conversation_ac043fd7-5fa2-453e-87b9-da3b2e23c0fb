{"title": "HTML5 form features", "description": "Expanded form options, including things like date pickers, sliders, validation, placeholders and multiple file uploads. Previously known as \"Web forms 2.0\".", "spec": "https://html.spec.whatwg.org/multipage/forms.html#forms", "status": "ls", "links": [{"url": "https://miketaylr.com/code/input-type-attr.html", "title": "HTML5 inputs and attribute support page"}, {"url": "https://github.com/westonruter/webforms2", "title": "Cross-browser JS implementation (based on original spec)"}], "bugs": [{"description": "Safari (>= 5.1) on Windows has a bug in multiple file uploads when used together with FormData to do binary uploads; each file will be reported as 0 bytes. Single file selection, however, works fine."}, {"description": "File uploading is not possible on iOS Safari before iOS 6."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "p", "10": "a", "11": "a"}, "edge": {"12": "a", "13": "a", "14": "a", "15": "a", "16": "a"}, "firefox": {"2": "p", "3": "p", "3.5": "p", "3.6": "p", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a"}, "safari": {"3.1": "p", "3.2": "p", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "a", "10.1": "a", "11": "a", "TP": "a"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a"}, "ios_saf": {"3.2": "n", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "n", "10": "a"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "a"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "", "notes_by_num": {}, "usage_perc_y": 9.27, "usage_perc_a": 84.62, "ucprefix": false, "parent": "", "keywords": "input,datepicker", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}