{"version": 3, "file": "platform-browser-dynamic.es5.js", "sources": ["../../../../packages/platform-browser-dynamic/index.ts", "../../../../packages/platform-browser-dynamic/src/platform-browser-dynamic.ts", "../../../../packages/platform-browser-dynamic/src/version.ts", "../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_cache.ts", "../../../../packages/platform-browser-dynamic/src/private_export.ts", "../../../../packages/platform-browser-dynamic/src/platform_providers.ts", "../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\nexport * from './src/platform-browser-dynamic';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader, platformCoreDynamic} from '@angular/compiler';\nimport {PlatformRef, Provider, createPlatformFactory} from '@angular/core';\n\nimport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nimport {CachedResourceLoader} from './resource_loader/resource_loader_cache';\n\nexport * from './private_export';\nexport {VERSION} from './version';\n/**\n * @experimental\n */\nexport const RESOURCE_CACHE_PROVIDER: Provider[] =\n    [{provide: ResourceLoader, useClass: CachedResourceLoader}];\n\n/**\n * @stable\n */\nexport const platformBrowserDynamic = createPlatformFactory(\n    platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\nimport {Version} from '@angular/core';\n/**\n * @stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n */\nexport class CachedResourceLoader extends ResourceLoader {\n  private _cache: {[url: string]: string};\n\n  constructor() {\n    super();\n    this._cache = (<any>global).$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n\n  get(url: string): Promise<string> {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return <Promise<any>>Promise.reject(\n          'CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nexport {ResourceLoaderImpl as ɵResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {ResourceLoader} from '@angular/compiler';\nimport {COMPILER_OPTIONS, PLATFORM_ID, Provider} from '@angular/core';\n\nimport {ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS as INTERNAL_BROWSER_PLATFORM_PROVIDERS} from '@angular/platform-browser';\n\nimport {ResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n\nexport const INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS: Provider[] = [\n  INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n  {\n    provide: COMPILER_OPTIONS,\n    useValue: {providers: [{provide: ResourceLoader, useClass: ResourceLoaderImpl}]},\n    multi: true\n  },\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ResourceLoader} from '@angular/compiler';\nimport {Injectable} from '@angular/core';\n\n\n\nexport class ResourceLoaderImpl extends ResourceLoader {\n  get(url: string): Promise<string> {\n    let resolve: (result: any) => void;\n    let reject: (error: any) => void;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function() {\n      // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n      // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n      // by IE10)\n      const response = xhr.response || xhr.responseText;\n\n      // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n      let status = xhr.status === 1223 ? 204 : xhr.status;\n\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function() { reject(`Failed to load ${url}`); };\n\n    xhr.send();\n    return promise;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": [], "mappings": ";;;;;;AMAA,OAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,MAAA,mBAAA,CAAA;;;;;;;;;;GAgBA;AACA;IAAA,8CAAA;IAAA;;IAuCO,CAAP;IAtCA,gCAAA,GAAA,UAAA,GAAA;QACA,IAAA,OAAA,CAAA;QACI,IAAJ,MAAA,CAAA;QACI,IAAJ,OAAA,GAAoB,IAApB,OAA+B,CAA/B,UAAA,GAAA,EAAA,GAAA;YACQ,OAAR,GAAA,GAAA,CAAA;YAEQ,MAAM,GAAG,GAAjB,CAAA;;;;QAIA,GAAA,CAAM,YAAN,GAAA,MAA2B,CAA3B;;YAGM,mFAAN;;;;YAKM,yDAAN;YACA,IAAQ,MAAM,GAAG,GAAjB,CAAA,MAAA,KAAA,IAAoC,GAApC,GAAA,GAAA,GAAA,CAAA,MAAA,CAAA;YACA,2DAAA;YAEM,uEAAN;YACA,iDAAA;YACA,EAAA,CAAA,CAAA,MAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBAAA,MAAA,GAAA,QAAA,GAAA,GAAA,GAAA,CAAA,CAAA;YACA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,IAAA,MAAA,IAAA,MAAA,IAAA,GAAA,CAAA,CAAA,CAAA;gBACA,OAAA,CAAA,QAAA,CAAA,CAAA;YAEQ,CAAR;YAEQ,IAAI,CAAZ,CAAc;gBACd,MAAkB,CAAC,oBAAnB,GAAA,CAAA,CAAA;YACA,CAAA;;QACA,GAAA,CAAA,OAAA,GAAA,cAAA,MAAA,CAAA,oBAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,GAAA,CAAQ,IAAR,EAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;;IAEO,yBAAP;AAAO,CAAP,AAvCA,CAAA,cAAA,GAuCA;;IDxDA,EAAA,IAAA,EAAA,UAAA,EAAA;;;;;;;;;;GAoBA;AACA,IAAA,2CAAA,GAAA;IACA,oCAAA;IACE;QACF,OAAA,EAAA,gBAAA;;QFxBA,KAAA,EAAA,IAAA;;;;;;;;;;;;;;;;;GAwBA;AACA;IAAA,gDAAA;IACA;QAAA,YACA,iBAAA,SAKA;QAHM,KAAN,CAAA,MAAA,GAAA,OAAA,CAAA,cAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,KAAI,CAAC,MAAM,IAAnB,IAAA,CAAA,CAAA,CAAA;YACM,MAAN,IAAA,KAAqB,CAArB,uEAAA,CAAA,CAAA;QACA,CAAK;;IAAL,CAAA;IACA,kCAAA,GAAA,UAAA,GAAA;QAEA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;QACA,CAAA;;YCrCA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,yDAAA,GAAA,GAAA,CAAA,CAAA;;;;CDyBA,CAAA,cAAA;;;;;;;;;;;;;;;ADXA;;;;;ADdA;;;;;;;;;;;;;GAyBA;;ADzBA;;;;;;;;;;;AAaA;;;;;;;"}