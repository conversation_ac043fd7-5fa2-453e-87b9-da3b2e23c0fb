/* ajv 5.2.0: Another JSON Schema Validator */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Ajv=e()}}(function(){return function e(r,t,a){function s(i,n){if(!t[i]){if(!r[i]){var l="function"==typeof require&&require;if(!n&&l)return l(i,!0);if(o)return o(i,!0);var h=new Error("Cannot find module '"+i+"'");throw h.code="MODULE_NOT_FOUND",h}var u=t[i]={exports:{}};r[i][0].call(u.exports,function(e){var t=r[i][1][e];return s(t||e)},u,u.exports,e,r,t,a)}return t[i].exports}for(var o="function"==typeof require&&require,i=0;i<a.length;i++)s(a[i]);return s}({1:[function(e,r,t){"use strict";var a=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"];r.exports=function(e,r){for(var t=0;t<r.length;t++){e=JSON.parse(JSON.stringify(e));var s,o=r[t].split("/"),i=e;for(s=1;s<o.length;s++)i=i[o[s]];for(s=0;s<a.length;s++){var n=a[s],l=i[n];l&&(i[n]={anyOf:[l,{$ref:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#"}]})}}return e}},{}],2:[function(e,r,t){"use strict";var a=r.exports=function(){this._cache={}};a.prototype.put=function(e,r){this._cache[e]=r},a.prototype.get=function(e){return this._cache[e]},a.prototype.del=function(e){delete this._cache[e]},a.prototype.clear=function(){this._cache={}}},{}],3:[function(e,r,t){"use strict";r.exports={$ref:e("../dotjs/ref"),allOf:e("../dotjs/allOf"),anyOf:e("../dotjs/anyOf"),const:e("../dotjs/const"),contains:e("../dotjs/contains"),dependencies:e("../dotjs/dependencies"),enum:e("../dotjs/enum"),format:e("../dotjs/format"),items:e("../dotjs/items"),maximum:e("../dotjs/_limit"),minimum:e("../dotjs/_limit"),maxItems:e("../dotjs/_limitItems"),minItems:e("../dotjs/_limitItems"),maxLength:e("../dotjs/_limitLength"),minLength:e("../dotjs/_limitLength"),maxProperties:e("../dotjs/_limitProperties"),minProperties:e("../dotjs/_limitProperties"),multipleOf:e("../dotjs/multipleOf"),not:e("../dotjs/not"),oneOf:e("../dotjs/oneOf"),pattern:e("../dotjs/pattern"),properties:e("../dotjs/properties"),propertyNames:e("../dotjs/propertyNames"),required:e("../dotjs/required"),uniqueItems:e("../dotjs/uniqueItems"),validate:e("../dotjs/validate")}},{"../dotjs/_limit":13,"../dotjs/_limitItems":14,"../dotjs/_limitLength":15,"../dotjs/_limitProperties":16,"../dotjs/allOf":17,"../dotjs/anyOf":18,"../dotjs/const":19,"../dotjs/contains":20,"../dotjs/dependencies":22,"../dotjs/enum":23,"../dotjs/format":24,"../dotjs/items":25,"../dotjs/multipleOf":26,"../dotjs/not":27,"../dotjs/oneOf":28,"../dotjs/pattern":29,"../dotjs/properties":30,"../dotjs/propertyNames":31,"../dotjs/ref":32,"../dotjs/required":33,"../dotjs/uniqueItems":34,"../dotjs/validate":35}],4:[function(e,r,t){"use strict";function a(e,r,t){function o(e){var r=e.$schema;return r&&!n.getSchema(r)?a.call(n,{$ref:r},!0):Promise.resolve()}function i(e){try{return n._compile(e)}catch(t){if(t instanceof s)return function(t){function a(){delete n._loadingSchemas[l]}function s(e){return n._refs[e]||n._schemas[e]}var l=t.missingSchema;if(s(l))throw new Error("Schema "+l+" is loaded but "+t.missingRef+" cannot be resolved");var h=n._loadingSchemas[l];return h||(h=n._loadingSchemas[l]=n._opts.loadSchema(l)).then(a,a),h.then(function(e){if(!s(l))return o(e).then(function(){s(l)||n.addSchema(e,l,void 0,r)})}).then(function(){return i(e)})}(t);throw t}}var n=this;if("function"!=typeof this._opts.loadSchema)throw new Error("options.loadSchema should be a function");"function"==typeof r&&(t=r,r=void 0);var l=o(e).then(function(){var t=n._addSchema(e,void 0,r);return t.validate||i(t)});return t&&l.then(function(e){t(null,e)},t),l}var s=e("./error_classes").MissingRef;r.exports=a},{"./error_classes":5}],5:[function(e,r,t){"use strict";function a(e){this.message="validation failed",this.errors=e,this.ajv=this.validation=!0}function s(e,r,t){this.message=t||s.message(e,r),this.missingRef=i.url(e,r),this.missingSchema=i.normalizeId(i.fullPath(this.missingRef))}function o(e){return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}var i=e("./resolve");r.exports={Validation:o(a),MissingRef:o(s)},s.message=function(e,r){return"can't resolve reference "+r+" from id "+e}},{"./resolve":8}],6:[function(e,r,t){"use strict";function a(e){return e="full"==e?"full":"fast",u.copy(a[e])}function s(e){var r=e.match(c);if(!r)return!1;var t=+r[1],a=+r[2];return t>=1&&t<=12&&a>=1&&a<=f[t]}function o(e,r){var t=e.match(d);if(!t)return!1;var a=t[1],s=t[2],o=t[3],i=t[5];return a<=23&&s<=59&&o<=59&&(!r||i)}function i(e){var r=e.split(w);return 2==r.length&&s(r[0])&&o(r[1],!0)}function n(e){return e.length<=255&&p.test(e)}function l(e){return j.test(e)&&m.test(e)}function h(e){if(S.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}}var u=e("./util"),c=/^\d\d\d\d-(\d\d)-(\d\d)$/,f=[0,31,29,31,30,31,30,31,31,30,31,30,31],d=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d:\d\d)?$/i,p=/^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*$/i,m=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@\/?]|%[0-9a-f]{2})*)?(?:\#(?:[a-z0-9\-._~!$&'()*+,;=:@\/?]|%[0-9a-f]{2})*)?$/i,v=/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@\/?]|%[0-9a-f]{2})*)?(?:\#(?:[a-z0-9\-._~!$&'"()*+,;=:@\/?]|%[0-9a-f]{2})*)?$/i,y=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#.\/;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?:\:[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?:\:[1-9][0-9]{0,3}|\*)?)*\})*$/i,g=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+\-?)*(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+\-?)*(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i,P=/^(?:urn\:uuid\:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,E=/^(?:\/(?:[^~\/]|~0|~1)*)*$|^\#(?:\/(?:[a-z0-9_\-\.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,b=/^(?:0|[1-9][0-9]*)(?:\#|(?:\/(?:[^~\/]|~0|~1)*)*)$/;r.exports=a,a.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^[0-2]\d:[0-5]\d:[0-5]\d(?:\.\d+)?(?:z|[+-]\d\d:\d\d)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s][0-2]\d:[0-5]\d:[0-5]\d(?:\.\d+)?(?:z|[+-]\d\d:\d\d)$/i,uri:/^(?:[a-z][a-z0-9+-.]*)(?:\:|\/)\/?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+-.]*:)?\/\/)?[^\s]*$/i,"uri-template":y,url:g,email:/^[a-z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:p,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:h,uuid:P,"json-pointer":E,"relative-json-pointer":b},a.full={date:s,time:o,"date-time":i,uri:l,"uri-reference":v,"uri-template":y,url:g,email:/^[a-z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&''*+\/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:n,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:h,uuid:P,"json-pointer":E,"relative-json-pointer":b};var w=/t|\s/i,j=/\/|\:/,S=/[^\\]\\Z/},{"./util":12}],7:[function(e,r,t){"use strict";function a(e,r,t,i){function b(){var e=U.validate,r=e.apply(null,arguments);return b.errors=e.errors,r}function w(e,t,s,o){var i=!t||t&&t.schema==e;if(t.schema!=r.schema)return a.call(O,e,t,s,o);var m=!0===e.$async,b=v({isTop:!0,schema:e,isRoot:i,baseId:o,root:t,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:p.MissingRef,RULES:N,validate:v,util:d,resolve:f,resolveRef:j,usePattern:F,useDefault:$,useCustomRule:R,opts:D,formats:V,self:O});b=c(I,h)+c(k,n)+c(C,l)+c(z,u)+b,D.processCode&&(b=D.processCode(b));var w;try{w=new Function("self","RULES","formats","root","refVal","defaults","customRules","co","equal","ucs2length","ValidationError",b)(O,N,V,r,I,C,z,y,P,g,E),I[0]=w}catch(e){throw console.error("Error compiling schema, function code:",b),e}return w.schema=e,w.errors=null,w.refs=A,w.refVal=I,w.root=i?w:t,m&&(w.$async=!0),!0===D.sourceCode&&(w.source={code:b,patterns:k,defaults:C}),w}function j(e,s,o){s=f.url(e,s);var i,n,l=A[s];if(void 0!==l)return i=I[l],n="refVal["+l+"]",x(i,n);if(!o&&r.refs){var h=r.refs[s];if(void 0!==h)return i=r.refVal[h],n=S(s,i),x(i,n)}n=S(s);var u=f.call(O,w,r,s);if(void 0===u){var c=t&&t[s];c&&(u=f.inlineRef(c,D.inlineRefs)?c:a.call(O,c,r,t,e))}return void 0!==u?(_(s,u),x(u,n)):void 0}function S(e,r){var t=I.length;return I[t]=r,A[e]=t,"refVal"+t}function _(e,r){I[A[e]]=r}function x(e,r){return"object"==typeof e||"boolean"==typeof e?{code:r,schema:e,inline:!0}:{code:r,$async:e&&e.$async}}function F(e){var r=q[e];return void 0===r&&(r=q[e]=k.length,k[r]=e),"pattern"+r}function $(e){switch(typeof e){case"boolean":case"number":return""+e;case"string":return d.toQuotedString(e);case"object":if(null===e)return"null";var r=m(e),t=L[r];return void 0===t&&(t=L[r]=C.length,C[t]=e),"default"+t}}function R(e,r,t,a){var s=e.definition.validateSchema;if(s&&!1!==O._opts.validateSchema&&!s(r)){var o="keyword schema is invalid: "+O.errorsText(s.errors);if("log"!=O._opts.validateSchema)throw new Error(o);console.error(o)}var i,n=e.definition.compile,l=e.definition.inline,h=e.definition.macro;if(n)i=n.call(O,r,t,a);else if(h)i=h.call(O,r,t,a),!1!==D.validateSchema&&O.validateSchema(i,!0);else if(l)i=l.call(O,a,e.keyword,r,t);else if(!(i=e.definition.validate))return;if(void 0===i)throw new Error('custom keyword "'+e.keyword+'"failed to compile');var u=z.length;return z[u]=i,{code:"customRule"+u,validate:i}}var O=this,D=this._opts,I=[void 0],A={},k=[],q={},C=[],L={},z=[];r=r||{schema:e,refVal:I,refs:A};var Q=s.call(this,e,r,i),U=this._compilations[Q.index];if(Q.compiling)return U.callValidate=b;var V=this._formats,N=this.RULES;try{var T=w(e,r,t,i);U.validate=T;var M=U.callValidate;return M&&(M.schema=T.schema,M.errors=null,M.refs=T.refs,M.refVal=T.refVal,M.root=T.root,M.$async=T.$async,D.sourceCode&&(M.source=T.source)),T}finally{o.call(this,e,r,i)}}function s(e,r,t){var a=i.call(this,e,r,t);return a>=0?{index:a,compiling:!0}:(a=this._compilations.length,this._compilations[a]={schema:e,root:r,baseId:t},{index:a,compiling:!1})}function o(e,r,t){var a=i.call(this,e,r,t);a>=0&&this._compilations.splice(a,1)}function i(e,r,t){for(var a=0;a<this._compilations.length;a++){var s=this._compilations[a];if(s.schema==e&&s.root==r&&s.baseId==t)return a}return-1}function n(e,r){return"var pattern"+e+" = new RegExp("+d.toQuotedString(r[e])+");"}function l(e){return"var default"+e+" = defaults["+e+"];"}function h(e,r){return void 0===r[e]?"":"var refVal"+e+" = refVal["+e+"];"}function u(e){return"var customRule"+e+" = customRules["+e+"];"}function c(e,r){if(!e.length)return"";for(var t="",a=0;a<e.length;a++)t+=r(a,e);return t}var f=e("./resolve"),d=e("./util"),p=e("./error_classes"),m=e("json-stable-stringify"),v=e("../dotjs/validate"),y=e("co"),g=d.ucs2length,P=e("fast-deep-equal"),E=p.Validation;r.exports=a},{"../dotjs/validate":35,"./error_classes":5,"./resolve":8,"./util":12,co:40,"fast-deep-equal":41,"json-stable-stringify":43}],8:[function(e,r,t){"use strict";function a(e,r,t){var o=this._refs[t];if("string"==typeof o){if(!this._refs[o])return a.call(this,e,r,o);o=this._refs[o]}if((o=o||this._schemas[t])instanceof g)return n(o.schema,this._opts.inlineRefs)?o.schema:o.validate||this._compile(o);var i,l,h,u=s.call(this,r,t);return u&&(i=u.schema,r=u.root,h=u.baseId),i instanceof g?l=i.validate||e.call(this,i.schema,r,void 0,h):void 0!==i&&(l=n(i,this._opts.inlineRefs)?i:e.call(this,i,r,void 0,h)),l}function s(e,r){var t=m.parse(r,!1,!0),a=c(t),s=u(this._getId(e.schema));if(a!==s){var n=f(a),l=this._refs[n];if("string"==typeof l)return o.call(this,e,l,t);if(l instanceof g)l.validate||this._compile(l),e=l;else{if(!((l=this._schemas[n])instanceof g))return;if(l.validate||this._compile(l),n==f(r))return{schema:l,root:e,baseId:s};e=l}if(!e.schema)return;s=u(this._getId(e.schema))}return i.call(this,t,s,e.schema,e)}function o(e,r,t){var a=s.call(this,e,r);if(a){var o=a.schema,n=a.baseId;e=a.root;var l=this._getId(o);return l&&(n=d(n,l)),i.call(this,t,n,o,e)}}function i(e,r,t,a){if("#/"==(e.hash=e.hash||"").slice(0,2)){for(var o=e.hash.split("/"),i=1;i<o.length;i++){var n=o[i];if(n){if(n=y.unescapeFragment(n),void 0===(t=t[n]))break;var l;if(!E[n]&&((l=this._getId(t))&&(r=d(r,l)),t.$ref)){var h=d(r,t.$ref),u=s.call(this,a,h);u&&(t=u.schema,a=u.root,r=u.baseId)}}}return void 0!==t&&t!==a.schema?{schema:t,root:a,baseId:r}:void 0}}function n(e,r){return!1!==r&&(void 0===r||!0===r?l(e):r?h(e)<=r:void 0)}function l(e){var r;if(Array.isArray(e)){for(var t=0;t<e.length;t++)if("object"==typeof(r=e[t])&&!l(r))return!1}else for(var a in e){if("$ref"==a)return!1;if("object"==typeof(r=e[a])&&!l(r))return!1}return!0}function h(e){var r,t=0;if(Array.isArray(e)){for(var a=0;a<e.length;a++)if("object"==typeof(r=e[a])&&(t+=h(r)),t==1/0)return 1/0}else for(var s in e){if("$ref"==s)return 1/0;if(b[s])t++;else if("object"==typeof(r=e[s])&&(t+=h(r)+1),t==1/0)return 1/0}return t}function u(e,r){return!1!==r&&(e=f(e)),c(m.parse(e,!1,!0))}function c(e){var r=e.protocol||"//"==e.href.slice(0,2)?"//":"";return(e.protocol||"")+r+(e.host||"")+(e.path||"")+"#"}function f(e){return e?e.replace(w,""):""}function d(e,r){return r=f(r),m.resolve(e,r)}function p(e){var r=f(this._getId(e)),t={"":r},a={"":u(r,!1)},s={},o=this;return P(e,{allKeys:!0},function(e,r,i,n,l,h,u){if(""!==r){var c=o._getId(e),d=t[n],p=a[n]+"/"+l;if(void 0!==u&&(p+="/"+("number"==typeof u?u:y.escapeFragment(u))),"string"==typeof c){c=d=f(d?m.resolve(d,c):c);var g=o._refs[c];if("string"==typeof g&&(g=o._refs[g]),g&&g.schema){if(!v(e,g.schema))throw new Error('id "'+c+'" resolves to more than one schema')}else if(c!=f(p))if("#"==c[0]){if(s[c]&&!v(e,s[c]))throw new Error('id "'+c+'" resolves to more than one schema');s[c]=e}else o._refs[c]=p}t[r]=d,a[r]=p}}),s}var m=e("url"),v=e("fast-deep-equal"),y=e("./util"),g=e("./schema_obj"),P=e("json-schema-traverse");r.exports=a,a.normalizeId=f,a.fullPath=u,a.url=d,a.ids=p,a.inlineRef=n,a.schema=s;var E=y.toHash(["properties","patternProperties","enum","dependencies","definitions"]),b=y.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]),w=/#\/?$/},{"./schema_obj":10,"./util":12,"fast-deep-equal":41,"json-schema-traverse":42,url:51}],9:[function(e,r,t){"use strict";var a=e("./_rules"),s=e("./util").toHash;r.exports=function(){var e=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","uniqueItems","contains","items"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf"]}],r=["type"],t=["additionalItems","$schema","id","title","description","default","definitions"],o=["number","integer","string","array","object","boolean","null"];return e.all=s(r),e.types=s(o),e.forEach(function(t){t.rules=t.rules.map(function(t){var s;if("object"==typeof t){var o=Object.keys(t)[0];s=t[o],t=o,s.forEach(function(t){r.push(t),e.all[t]=!0})}return r.push(t),e.all[t]={keyword:t,code:a[t],implements:s}}),t.type&&(e.types[t.type]=t)}),e.keywords=s(r.concat(t)),e.custom={},e}},{"./_rules":3,"./util":12}],10:[function(e,r,t){"use strict";function a(e){s.copy(e,this)}var s=e("./util");r.exports=a},{"./util":12}],11:[function(e,r,t){"use strict";r.exports=function(e){for(var r,t=0,a=e.length,s=0;s<a;)t++,(r=e.charCodeAt(s++))>=55296&&r<=56319&&s<a&&56320==(64512&(r=e.charCodeAt(s)))&&s++;return t}},{}],12:[function(e,r,t){"use strict";function a(e,r){r=r||{};for(var t in e)r[t]=e[t];return r}function s(e,r,t){var a=t?" !== ":" === ",s=t?" || ":" && ",o=t?"!":"",i=t?"":"!";switch(e){case"null":return r+a+"null";case"array":return o+"Array.isArray("+r+")";case"object":return"("+o+r+s+"typeof "+r+a+'"object"'+s+i+"Array.isArray("+r+"))";case"integer":return"(typeof "+r+a+'"number"'+s+i+"("+r+" % 1)"+s+r+a+r+")";default:return"typeof "+r+a+'"'+e+'"'}}function o(e,r){switch(e.length){case 1:return s(e[0],r,!0);default:var t="",a=n(e);a.array&&a.object&&(t=a.null?"(":"(!"+r+" || ",t+="typeof "+r+' !== "object")',delete a.null,delete a.array,delete a.object),a.number&&delete a.integer;for(var o in a)t+=(t?" && ":"")+s(o,r,!0);return t}}function i(e,r){if(Array.isArray(r)){for(var t=[],a=0;a<r.length;a++){var s=r[a];_[s]?t[t.length]=s:"array"===e&&"array"===s&&(t[t.length]=s)}if(t.length)return t}else{if(_[r])return[r];if("array"===e&&"array"===r)return["array"]}}function n(e){for(var r={},t=0;t<e.length;t++)r[e[t]]=!0;return r}function l(e){return"number"==typeof e?"["+e+"]":x.test(e)?"."+e:"['"+h(e)+"']"}function h(e){return e.replace(F,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function u(e,r){r+="[^0-9]";var t=e.match(new RegExp(r,"g"));return t?t.length:0}function c(e,r,t){return r+="([^0-9])",t=t.replace(/\$/g,"$$$$"),e.replace(new RegExp(r,"g"),t+"$1")}function f(e){return e.replace($,"").replace(R,"").replace(O,"if (!($1))")}function d(e,r){var t=e.match(D);return t&&2==t.length&&(e=r?e.replace(A,"").replace(C,L):e.replace(I,"").replace(k,q)),t=e.match(z),t&&3===t.length?e.replace(Q,""):e}function p(e,r){if("boolean"==typeof e)return!e;for(var t in e)if(r[t])return!0}function m(e,r,t){if("boolean"==typeof e)return!e&&"not"!=t;for(var a in e)if(a!=t&&r[a])return!0}function v(e){return"'"+h(e)+"'"}function y(e,r,t,a){return E(e,t?"'/' + "+r+(a?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):a?"'[' + "+r+" + ']'":"'[\\'' + "+r+" + '\\']'")}function g(e,r,t){return E(e,v(t?"/"+j(r):l(r)))}function P(e,r,t){var a,s,o,i;if(""===e)return"rootData";if("/"==e[0]){if(!U.test(e))throw new Error("Invalid JSON-pointer: "+e);s=e,o="rootData"}else{if(!(i=e.match(V)))throw new Error("Invalid JSON-pointer: "+e);if(a=+i[1],"#"==(s=i[2])){if(a>=r)throw new Error("Cannot access property/index "+a+" levels up, current level is "+r);return t[r-a]}if(a>r)throw new Error("Cannot access data "+a+" levels up, current level is "+r);if(o="data"+(r-a||""),!s)return o}for(var n=o,h=s.split("/"),u=0;u<h.length;u++){var c=h[u];c&&(n+=" && "+(o+=l(S(c))))}return n}function E(e,r){return'""'==e?r:(e+" + "+r).replace(/' \+ '/g,"")}function b(e){return S(decodeURIComponent(e))}function w(e){return encodeURIComponent(j(e))}function j(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function S(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}r.exports={copy:a,checkDataType:s,checkDataTypes:o,coerceToTypes:i,toHash:n,getProperty:l,escapeQuotes:h,equal:e("fast-deep-equal"),ucs2length:e("./ucs2length"),varOccurences:u,varReplace:c,cleanUpCode:f,finalCleanUpCode:d,schemaHasRules:p,schemaHasRulesExcept:m,toQuotedString:v,getPathExpr:y,getPath:g,getData:P,unescapeFragment:b,escapeFragment:w,escapeJsonPointer:j};var _=n(["string","number","integer","boolean","null"]),x=/^[a-z$_][a-z$_0-9]*$/i,F=/'|\\/g,$=/else\s*{\s*}/g,R=/if\s*\([^)]+\)\s*\{\s*\}(?!\s*else)/g,O=/if\s*\(([^)]+)\)\s*\{\s*\}\s*else(?!\s*if)/g,D=/[^v\.]errors/g,I=/var errors = 0;|var vErrors = null;|validate.errors = vErrors;/g,A=/var errors = 0;|var vErrors = null;/g,k="return errors === 0;",q="validate.errors = null; return true;",C=/if \(errors === 0\) return data;\s*else throw new ValidationError\(vErrors\);/,L="return data;",z=/[^A-Za-z_$]rootData[^A-Za-z0-9_$]/g,Q=/if \(rootData === undefined\) rootData = data;/,U=/^\/(?:[^~]|~0|~1)*$/,V=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/},{"./ucs2length":11,"fast-deep-equal":41}],13:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var d="maximum"==r,p=d?"exclusiveMaximum":"exclusiveMinimum",m=e.schema[p],v=e.opts.$data&&m&&m.$data,y=d?"<":">",g=d?">":"<",P=void 0;if(v){var E=e.util.getData(m.$data,i,e.dataPathArr),b="exclusive"+o,w="exclType"+o,j="exclIsNumber"+o,S="' + "+(_="op"+o)+" + '";s+=" var schemaExcl"+o+" = "+E+"; ",s+=" var "+b+"; var "+w+" = typeof "+(E="schemaExcl"+o)+"; if ("+w+" != 'boolean' && "+w+" != 'undefined' && "+w+" != 'number') { ";P=p;(x=x||[]).push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(P||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: {} ",!1!==e.opts.messages&&(s+=" , message: '"+p+" should be boolean' "),e.opts.verbose&&(s+=" , schema: validate.schema"+l+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";F=s;s=x.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+F+"]); ":" validate.errors = ["+F+"]; return false; ":" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } else if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+w+" == 'number' ? ( ("+b+" = "+a+" === undefined || "+E+" "+y+"= "+a+") ? "+c+" "+g+"= "+E+" : "+c+" "+g+" "+a+" ) : ( ("+b+" = "+E+" === true) ? "+c+" "+g+"= "+a+" : "+c+" "+g+" "+a+" ) || "+c+" !== "+c+") { var op"+o+" = "+b+" ? '"+y+"' : '"+y+"=';"}else{S=y;if((j="number"==typeof m)&&f){_="'"+S+"'";s+=" if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" ( "+a+" === undefined || "+m+" "+y+"= "+a+" ? "+c+" "+g+"= "+m+" : "+c+" "+g+" "+a+" ) || "+c+" !== "+c+") { "}else{j&&void 0===n?(b=!0,P=p,h=e.errSchemaPath+"/"+p,a=m,g+="="):(j&&(a=Math[d?"min":"max"](m,n)),m===(!j||a)?(b=!0,P=p,h=e.errSchemaPath+"/"+p,g+="="):(b=!1,S+="="));var _="'"+S+"'";s+=" if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+c+" "+g+" "+a+" || "+c+" !== "+c+") { "}}P=P||r;var x=x||[];x.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(P||"_limit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { comparison: "+_+", limit: "+a+", exclusive: "+b+" } ",!1!==e.opts.messages&&(s+=" , message: 'should be "+S+" ",s+=f?"' + "+a:a+"'"),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var F=s;return s=x.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+F+"]); ":" validate.errors = ["+F+"]; return false; ":" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } ",u&&(s+=" else { "),s}},{}],14:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var d="maxItems"==r?">":"<";s+="if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+c+".length "+d+" "+a+") { ";var p=r,m=m||[];m.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(p||"_limitItems")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have ",s+="maxItems"==r?"more":"less",s+=" than ",s+=f?"' + "+a+" + '":""+n,s+=" items' "),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var v=s;return s=m.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+v+"]); ":" validate.errors = ["+v+"]; return false; ":" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],15:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var d="maxLength"==r?">":"<";s+="if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=!1===e.opts.unicode?" "+c+".length ":" ucs2length("+c+") ",s+=" "+d+" "+a+") { ";var p=r,m=m||[];m.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(p||"_limitLength")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT be ",s+="maxLength"==r?"longer":"shorter",s+=" than ",s+=f?"' + "+a+" + '":""+n,s+=" characters' "),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var v=s;return s=m.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+v+"]); ":" validate.errors = ["+v+"]; return false; ":" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],16:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var d="maxProperties"==r?">":"<";s+="if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" Object.keys("+c+").length "+d+" "+a+") { ";var p=r,m=m||[];m.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(p||"_limitProperties")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have ",s+="maxProperties"==r?"more":"less",s+=" than ",s+=f?"' + "+a+" + '":""+n,s+=" properties' "),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var v=s;return s=m.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+v+"]); ":" validate.errors = ["+v+"]; return false; ":" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],17:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.schema[r],o=e.schemaPath+e.util.getProperty(r),i=e.errSchemaPath+"/"+r,n=!e.opts.allErrors,l=e.util.copy(e),h="",u="valid"+ ++l.level,c=l.baseId,f=!0,d=s;if(d)for(var p,m=-1,v=d.length-1;m<v;)p=d[m+=1],e.util.schemaHasRules(p,e.RULES.all)&&(f=!1,l.schema=p,l.schemaPath=o+"["+m+"]",l.errSchemaPath=i+"/"+m,a+="  "+e.validate(l)+" ",l.baseId=c,n&&(a+=" if ("+u+") { ",h+="}"));return n&&(a+=f?" if (true) { ":" "+h.slice(0,-1)+" "),a=e.util.cleanUpCode(a)}},{}],18:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f="errs__"+s,d=e.util.copy(e),p="",m="valid"+ ++d.level;if(i.every(function(r){return e.util.schemaHasRules(r,e.RULES.all)})){var v=d.baseId;a+=" var "+f+" = errors; var "+c+" = false;  ";var y=e.compositeRule;e.compositeRule=d.compositeRule=!0;var g=i;if(g)for(var P,E=-1,b=g.length-1;E<b;)P=g[E+=1],d.schema=P,d.schemaPath=n+"["+E+"]",d.errSchemaPath=l+"/"+E,a+="  "+e.validate(d)+" ",d.baseId=v,a+=" "+c+" = "+c+" || "+m+"; if (!"+c+") { ",p+="}";e.compositeRule=d.compositeRule=y,a+=" "+p+" if (!"+c+") {   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'anyOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should match some schema in anyOf' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),a+=" } else {  errors = "+f+"; if (vErrors !== null) { if ("+f+") vErrors.length = "+f+"; else vErrors = null; } ",e.opts.allErrors&&(a+=" } "),a=e.util.cleanUpCode(a)}else h&&(a+=" if (true) { ");return a}},{}],19:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f=e.opts.$data&&i&&i.$data;f&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; "),f||(a+=" var schema"+s+" = validate.schema"+n+";"),a+="var "+c+" = equal("+u+", schema"+s+"); if (!"+c+") {   ";var d=d||[];d.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'const' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should be equal to constant' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var p=a;return a=d.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+p+"]); ":" validate.errors = ["+p+"]; return false; ":" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" }",h&&(a+=" else { "),a}},{}],20:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f="errs__"+s,d=e.util.copy(e),p="valid"+ ++d.level,m="i"+s,v=d.dataLevel=e.dataLevel+1,y="data"+v,g=e.baseId,P=e.util.schemaHasRules(i,e.RULES.all);if(a+="var "+f+" = errors;var "+c+";",P){var E=e.compositeRule;e.compositeRule=d.compositeRule=!0,d.schema=i,d.schemaPath=n,d.errSchemaPath=l,a+=" var "+p+" = false; for (var "+m+" = 0; "+m+" < "+u+".length; "+m+"++) { ",d.errorPath=e.util.getPathExpr(e.errorPath,m,e.opts.jsonPointers,!0);var b=u+"["+m+"]";d.dataPathArr[v]=m;var w=e.validate(d);d.baseId=g,e.util.varOccurences(w,y)<2?a+=" "+e.util.varReplace(w,y,b)+" ":a+=" var "+y+" = "+b+"; "+w+" ",a+=" if ("+p+") break; }  ",e.compositeRule=d.compositeRule=E,a+="  if (!"+p+") {"}else a+=" if ("+u+".length == 0) {";var j=j||[];j.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'contains' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should contain a valid item' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var S=a;return a=j.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+S+"]); ":" validate.errors = ["+S+"]; return false; ":" var err = "+S+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { ",P&&(a+="  errors = "+f+"; if (vErrors !== null) { if ("+f+") vErrors.length = "+f+"; else vErrors = null; } "),e.opts.allErrors&&(a+=" } "),a=e.util.cleanUpCode(a)}},{}],21:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s,o=" ",i=e.level,n=e.dataLevel,l=e.schema[r],h=e.schemaPath+e.util.getProperty(r),u=e.errSchemaPath+"/"+r,c=!e.opts.allErrors,f="data"+(n||""),d="valid"+i,p="errs__"+i,m=e.opts.$data&&l&&l.$data;m?(o+=" var schema"+i+" = "+e.util.getData(l.$data,n,e.dataPathArr)+"; ",s="schema"+i):s=l;var v,y,g,P,E,b=this,w="definition"+i,j=b.definition,S="";if(m&&j.$data){E="keywordValidate"+i;var _=j.validateSchema;o+=" var "+w+" = RULES.custom['"+r+"'].definition; var "+E+" = "+w+".validate;"}else{if(!(P=e.useCustomRule(b,l,e.schema,e)))return;s="validate.schema"+h,E=P.code,v=j.compile,y=j.inline,g=j.macro}var x=E+".errors",F="i"+i,$="ruleErr"+i,R=j.async;if(R&&!e.async)throw new Error("async keyword in sync schema");if(y||g||(o+=x+" = null;"),o+="var "+p+" = errors;var "+d+";",m&&j.$data&&(S+="}",o+=" if ("+s+" === undefined) { "+d+" = true; } else { ",_&&(S+="}",o+=" "+d+" = "+w+".validateSchema("+s+"); if ("+d+") { ")),y)o+=j.statements?" "+P.validate+" ":" "+d+" = "+P.validate+"; ";else if(g){var O=e.util.copy(e),S="",D="valid"+ ++O.level;O.schema=P.validate,O.schemaPath="";var I=e.compositeRule;e.compositeRule=O.compositeRule=!0;var A=e.validate(O).replace(/validate\.schema/g,E);e.compositeRule=O.compositeRule=I,o+=" "+A}else{(L=L||[]).push(o),o="",o+="  "+E+".call( ",o+=e.opts.passContext?"this":"self",o+=v||!1===j.schema?" , "+f+" ":" , "+s+" , "+f+" , validate.schema"+e.schemaPath+" ",o+=" , (dataPath || '')",'""'!=e.errorPath&&(o+=" + "+e.errorPath);var k=n?"data"+(n-1||""):"parentData",q=n?e.dataPathArr[n]:"parentDataProperty",C=o+=" , "+k+" , "+q+" , rootData )  ";o=L.pop(),!1===j.errors?(o+=" "+d+" = ",R&&(o+=""+e.yieldAwait),o+=C+"; "):o+=R?" var "+(x="customErrors"+i)+" = null; try { "+d+" = "+e.yieldAwait+C+"; } catch (e) { "+d+" = false; if (e instanceof ValidationError) "+x+" = e.errors; else throw e; } ":" "+x+" = null; "+d+" = "+C+"; "}if(j.modifying&&(o+=" if ("+k+") "+f+" = "+k+"["+q+"];"),o+=""+S,j.valid)c&&(o+=" if (true) { ");else{o+=" if ( ",void 0===j.valid?(o+=" !",o+=g?""+D:""+d):o+=" "+!j.valid+" ",o+=") { ",a=b.keyword;var L=L||[];L.push(o),o="",(L=L||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(a||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(u)+" , params: { keyword: '"+b.keyword+"' } ",!1!==e.opts.messages&&(o+=" , message: 'should pass \""+b.keyword+"\" keyword validation' "),e.opts.verbose&&(o+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),o+=" } "):o+=" {} ";var z=o;o=L.pop();var Q=o+=!e.compositeRule&&c?e.async?" throw new ValidationError(["+z+"]); ":" validate.errors = ["+z+"]; return false; ":" var err = "+z+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";o=L.pop(),y?j.errors?"full"!=j.errors&&(o+="  for (var "+F+"="+p+"; "+F+"<errors; "+F+"++) { var "+$+" = vErrors["+F+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+$+".schemaPath === undefined) { "+$+'.schemaPath = "'+u+'"; } ',e.opts.verbose&&(o+=" "+$+".schema = "+s+"; "+$+".data = "+f+"; "),o+=" } "):!1===j.errors?o+=" "+Q+" ":(o+=" if ("+p+" == errors) { "+Q+" } else {  for (var "+F+"="+p+"; "+F+"<errors; "+F+"++) { var "+$+" = vErrors["+F+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+$+".schemaPath === undefined) { "+$+'.schemaPath = "'+u+'"; } ',e.opts.verbose&&(o+=" "+$+".schema = "+s+"; "+$+".data = "+f+"; "),o+=" } } "):g?(o+="   var err =   ",!1!==e.createErrors?(o+=" { keyword: '"+(a||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(u)+" , params: { keyword: '"+b.keyword+"' } ",!1!==e.opts.messages&&(o+=" , message: 'should pass \""+b.keyword+"\" keyword validation' "),e.opts.verbose&&(o+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "),o+=" } "):o+=" {} ",o+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&c&&(o+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; ")):!1===j.errors?o+=" "+Q+" ":(o+=" if (Array.isArray("+x+")) { if (vErrors === null) vErrors = "+x+"; else vErrors = vErrors.concat("+x+"); errors = vErrors.length;  for (var "+F+"="+p+"; "+F+"<errors; "+F+"++) { var "+$+" = vErrors["+F+"]; if ("+$+".dataPath === undefined) "+$+".dataPath = (dataPath || '') + "+e.errorPath+";  "+$+'.schemaPath = "'+u+'";  ',e.opts.verbose&&(o+=" "+$+".schema = "+s+"; "+$+".data = "+f+"; "),o+=" } } else { "+Q+" } "),o+=" } ",c&&(o+=" else { ")}return o}},{}],22:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,f=e.util.copy(e),d="",p="valid"+ ++f.level,m={},v={},y=e.opts.ownProperties;for(b in i){var g=i[b],P=Array.isArray(g)?v:m;P[b]=g}a+="var "+c+" = errors;";var E=e.errorPath;a+="var missing"+s+";";for(var b in v)if((P=v[b]).length){if(a+=" if ( "+u+e.util.getProperty(b)+" !== undefined ",y&&(a+=" && Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(b)+"') "),h){a+=" && ( ";var w=P;if(w)for(var j=-1,S=w.length-1;j<S;)O=w[j+=1],j&&(a+=" || "),a+=" ( ( "+(k=u+(A=e.util.getProperty(O)))+" === undefined ",y&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(O)+"') "),a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?O:A)+") ) ";a+=")) {  ";var _="missing"+s,x="' + "+_+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(E,_,!0):E+" + "+_);var F=F||[];F.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(b)+"', missingProperty: '"+x+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(1==P.length?P[0]:P.join(", "))+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should have ",a+=1==P.length?"property "+e.util.escapeQuotes(P[0]):"properties "+e.util.escapeQuotes(P.join(", ")),a+=" when property "+e.util.escapeQuotes(b)+" is present' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var $=a;a=F.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+$+"]); ":" validate.errors = ["+$+"]; return false; ":" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else{a+=" ) { ";var R=P;if(R)for(var O,D=-1,I=R.length-1;D<I;){O=R[D+=1];var A=e.util.getProperty(O),x=e.util.escapeQuotes(O),k=u+A;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(E,O,e.opts.jsonPointers)),a+=" if ( "+k+" === undefined ",y&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(O)+"') "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(b)+"', missingProperty: '"+x+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(1==P.length?P[0]:P.join(", "))+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should have ",a+=1==P.length?"property "+e.util.escapeQuotes(P[0]):"properties "+e.util.escapeQuotes(P.join(", ")),a+=" when property "+e.util.escapeQuotes(b)+" is present' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}a+=" }   ",h&&(d+="}",a+=" else { ")}e.errorPath=E;var q=f.baseId;for(var b in m){g=m[b];e.util.schemaHasRules(g,e.RULES.all)&&(a+=" "+p+" = true; if ( "+u+e.util.getProperty(b)+" !== undefined ",y&&(a+=" && Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(b)+"') "),a+=") { ",f.schema=g,f.schemaPath=n+e.util.getProperty(b),f.errSchemaPath=l+"/"+e.util.escapeFragment(b),a+="  "+e.validate(f)+" ",f.baseId=q,a+=" }  ",h&&(a+=" if ("+p+") { ",d+="}"))}return h&&(a+="   "+d+" if ("+c+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],23:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f=e.opts.$data&&i&&i.$data;f&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ");var d="i"+s,p="schema"+s;f||(a+=" var "+p+" = validate.schema"+n+";"),a+="var "+c+";",f&&(a+=" if (schema"+s+" === undefined) "+c+" = true; else if (!Array.isArray(schema"+s+")) "+c+" = false; else {"),a+=c+" = false;for (var "+d+"=0; "+d+"<"+p+".length; "+d+"++) if (equal("+u+", "+p+"["+d+"])) { "+c+" = true; break; }",f&&(a+="  }  "),a+=" if (!"+c+") {   ";var m=m||[];m.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'enum' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { allowedValues: schema"+s+" } ",!1!==e.opts.messages&&(a+=" , message: 'should be equal to one of the allowed values' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var v=a;return a=m.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+v+"]); ":" validate.errors = ["+v+"]; return false; ":" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" }",h&&(a+=" else { "),a}},{}],24:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||"");if(!1===e.opts.format)return h&&(a+=" if (true) { "),a;var c,f=e.opts.$data&&i&&i.$data;f?(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ",c="schema"+s):c=i;var d=e.opts.unknownFormats,p=Array.isArray(d);if(f)a+=" var "+(m="format"+s)+" = formats["+c+"]; var "+(v="isObject"+s)+" = typeof "+m+" == 'object' && !("+m+" instanceof RegExp) && "+m+".validate; var "+(y="formatType"+s)+" = "+v+" && "+m+".type || 'string'; if ("+v+") { ",e.async&&(a+=" var async"+s+" = "+m+".async; "),a+=" "+m+" = "+m+".validate; } if (  ",f&&(a+=" ("+c+" !== undefined && typeof "+c+" != 'string') || "),a+=" (","ignore"!=d&&(a+=" ("+c+" && !"+m+" ",p&&(a+=" && self._opts.unknownFormats.indexOf("+c+") == -1 "),a+=") || "),a+=" ("+m+" && "+y+" == '"+t+"' && !(typeof "+m+" == 'function' ? ",a+=e.async?" (async"+s+" ? "+e.yieldAwait+" "+m+"("+u+") : "+m+"("+u+")) ":" "+m+"("+u+") ",a+=" : "+m+".test("+u+"))))) {";else{var m=e.formats[i];if(!m){if("ignore"==d)return console.warn('unknown format "'+i+'" ignored in schema at path "'+e.errSchemaPath+'"'),h&&(a+=" if (true) { "),a;if(p&&d.indexOf(i)>=0)return h&&(a+=" if (true) { "),a;throw new Error('unknown format "'+i+'" is used in schema at path "'+e.errSchemaPath+'"')}var v="object"==typeof m&&!(m instanceof RegExp)&&m.validate,y=v&&m.type||"string";if(v){var g=!0===m.async;m=m.validate}if(y!=t)return h&&(a+=" if (true) { "),a;if(g){if(!e.async)throw new Error("async format in sync schema");P="formats"+e.util.getProperty(i)+".validate";a+=" if (!("+e.yieldAwait+" "+P+"("+u+"))) { "}else{a+=" if (! ";var P="formats"+e.util.getProperty(i);v&&(P+=".validate"),a+="function"==typeof m?" "+P+"("+u+") ":" "+P+".test("+u+") ",a+=") { "}}var E=E||[];E.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'format' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { format:  ",a+=f?""+c:""+e.util.toQuotedString(i),a+="  } ",!1!==e.opts.messages&&(a+=" , message: 'should match format \"",a+=f?"' + "+c+" + '":""+e.util.escapeQuotes(i),a+="\"' "),e.opts.verbose&&(a+=" , schema:  ",a+=f?"validate.schema"+n:""+e.util.toQuotedString(i),a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var b=a;return a=E.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+b+"]); ":" validate.errors = ["+b+"]; return false; ":" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",h&&(a+=" else { "),a}},{}],25:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f="errs__"+s,d=e.util.copy(e),p="",m="valid"+ ++d.level,v="i"+s,y=d.dataLevel=e.dataLevel+1,g="data"+y,P=e.baseId;if(a+="var "+f+" = errors;var "+c+";",Array.isArray(i)){var E=e.schema.additionalItems;if(!1===E){a+=" "+c+" = "+u+".length <= "+i.length+"; ";var b=l;l=e.errSchemaPath+"/additionalItems",a+="  if (!"+c+") {   ";var w=w||[];w.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'additionalItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+i.length+" } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have more than "+i.length+" items' "),e.opts.verbose&&(a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var j=a;a=w.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",l=b,h&&(p+="}",a+=" else { ")}var S=i;if(S)for(var _,x=-1,F=S.length-1;x<F;)if(_=S[x+=1],e.util.schemaHasRules(_,e.RULES.all)){a+=" "+m+" = true; if ("+u+".length > "+x+") { ";$=u+"["+x+"]";d.schema=_,d.schemaPath=n+"["+x+"]",d.errSchemaPath=l+"/"+x,d.errorPath=e.util.getPathExpr(e.errorPath,x,e.opts.jsonPointers,!0),d.dataPathArr[y]=x;R=e.validate(d);d.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}if("object"==typeof E&&e.util.schemaHasRules(E,e.RULES.all)){d.schema=E,d.schemaPath=e.schemaPath+".additionalItems",d.errSchemaPath=e.errSchemaPath+"/additionalItems",a+=" "+m+" = true; if ("+u+".length > "+i.length+") {  for (var "+v+" = "+i.length+"; "+v+" < "+u+".length; "+v+"++) { ",d.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers,!0);$=u+"["+v+"]";d.dataPathArr[y]=v;R=e.validate(d);d.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",h&&(a+=" if (!"+m+") break; "),a+=" } }  ",h&&(a+=" if ("+m+") { ",p+="}")}}else if(e.util.schemaHasRules(i,e.RULES.all)){d.schema=i,d.schemaPath=n,d.errSchemaPath=l,a+="  for (var "+v+" = 0; "+v+" < "+u+".length; "+v+"++) { ",d.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers,!0);var $=u+"["+v+"]";d.dataPathArr[y]=v;var R=e.validate(d);d.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",h&&(a+=" if (!"+m+") break; "),a+=" }"}return h&&(a+=" "+p+" if ("+f+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],26:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n,s+="var division"+o+";if (",f&&(s+=" "+a+" !== undefined && ( typeof "+a+" != 'number' || "),s+=" (division"+o+" = "+c+" / "+a+", ",s+=e.opts.multipleOfPrecision?" Math.abs(Math.round(division"+o+") - division"+o+") > 1e-"+e.opts.multipleOfPrecision+" ":" division"+o+" !== parseInt(division"+o+") ",s+=" ) ",f&&(s+="  )  "),s+=" ) {   ";var d=d||[];d.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'multipleOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { multipleOf: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should be multiple of ",s+=f?"' + "+a:a+"'"),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var p=s;return s=d.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+p+"]); ":" validate.errors = ["+p+"]; return false; ":" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],27:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,f=e.util.copy(e),d="valid"+ ++f.level;if(e.util.schemaHasRules(i,e.RULES.all)){f.schema=i,f.schemaPath=n,f.errSchemaPath=l,a+=" var "+c+" = errors;  ";var p=e.compositeRule;e.compositeRule=f.compositeRule=!0,f.createErrors=!1;var m;f.opts.allErrors&&(m=f.opts.allErrors,f.opts.allErrors=!1),a+=" "+e.validate(f)+" ",f.createErrors=!0,m&&(f.opts.allErrors=m),e.compositeRule=f.compositeRule=p,a+=" if ("+d+") {   ";var v=v||[];v.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should NOT be valid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var y=a;a=v.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+y+"]); ":" validate.errors = ["+y+"]; return false; ":" var err = "+y+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else {  errors = "+c+"; if (vErrors !== null) { if ("+c+") vErrors.length = "+c+"; else vErrors = null; } ",e.opts.allErrors&&(a+=" } ")}else a+="  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should NOT be valid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",h&&(a+=" if (false) { ");return a}},{}],28:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f="errs__"+s,d=e.util.copy(e),p="",m="valid"+ ++d.level;a+="var "+f+" = errors;var prevValid"+s+" = false;var "+c+" = false;";var v=d.baseId,y=e.compositeRule;e.compositeRule=d.compositeRule=!0;var g=i;if(g)for(var P,E=-1,b=g.length-1;E<b;)P=g[E+=1],e.util.schemaHasRules(P,e.RULES.all)?(d.schema=P,d.schemaPath=n+"["+E+"]",d.errSchemaPath=l+"/"+E,a+="  "+e.validate(d)+" ",d.baseId=v):a+=" var "+m+" = true; ",E&&(a+=" if ("+m+" && prevValid"+s+") "+c+" = false; else { ",p+="}"),a+=" if ("+m+") "+c+" = prevValid"+s+" = true;";return e.compositeRule=d.compositeRule=y,a+=p+"if (!"+c+") {   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'oneOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should match exactly one schema in oneOf' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),a+="} else {  errors = "+f+"; if (vErrors !== null) { if ("+f+") vErrors.length = "+f+"; else vErrors = null; }",e.opts.allErrors&&(a+=" } "),a}},{}],29:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f=e.opts.$data&&n&&n.$data;f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var d=f?"(new RegExp("+a+"))":e.usePattern(n);s+="if ( ",f&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'string') || "),s+=" !"+d+".test("+c+") ) {   ";var p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'pattern' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { pattern:  ",s+=f?""+a:""+e.util.toQuotedString(n),s+="  } ",!1!==e.opts.messages&&(s+=" , message: 'should match pattern \"",s+=f?"' + "+a+" + '":""+e.util.escapeQuotes(n),s+="\"' "),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+e.util.toQuotedString(n),s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;return s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],30:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f="errs__"+s,d=e.util.copy(e),p="",m="valid"+ ++d.level,v="key"+s,y="idx"+s,g=d.dataLevel=e.dataLevel+1,P="data"+g,E="dataProperties"+s,b=Object.keys(i||{}),w=e.schema.patternProperties||{},j=Object.keys(w),S=e.schema.additionalProperties,_=b.length||j.length,x=!1===S,F="object"==typeof S&&Object.keys(S).length,$=e.opts.removeAdditional,R=x||F||$,O=e.opts.ownProperties,D=e.baseId,I=e.schema.required;if(I&&(!e.opts.v5||!I.$data)&&I.length<e.opts.loopRequired)var A=e.util.toHash(I);if(e.opts.patternGroups)var k=e.schema.patternGroups||{},q=Object.keys(k);if(a+="var "+f+" = errors;var "+m+" = true;",O&&(a+=" var "+E+" = undefined;"),R){if(a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",_){if(a+=" var isAdditional"+s+" = !(false ",b.length)if(b.length>5)a+=" || validate.schema"+n+"["+v+"] ";else{var C=b;if(C)for(var L=-1,z=C.length-1;L<z;)J=C[L+=1],a+=" || "+v+" == "+e.util.toQuotedString(J)+" "}if(j.length){var Q=j;if(Q)for(var U=-1,V=Q.length-1;U<V;)oe=Q[U+=1],a+=" || "+e.usePattern(oe)+".test("+v+") "}if(e.opts.patternGroups&&q.length){var N=q;if(N)for(var U=-1,T=N.length-1;U<T;)he=N[U+=1],a+=" || "+e.usePattern(he)+".test("+v+") "}a+=" ); if (isAdditional"+s+") { "}if("all"==$)a+=" delete "+u+"["+v+"]; ";else{var M=e.errorPath,B="' + "+v+" + '";if(e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers)),x)if($)a+=" delete "+u+"["+v+"]; ";else{a+=" "+m+" = false; ";te=l;l=e.errSchemaPath+"/additionalProperties",(Ee=Ee||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'additionalProperties' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { additionalProperty: '"+B+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have additional properties' "),e.opts.verbose&&(a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";be=a;a=Ee.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+be+"]); ":" validate.errors = ["+be+"]; return false; ":" var err = "+be+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l=te,h&&(a+=" break; ")}else if(F)if("failing"==$){a+=" var "+f+" = errors;  ";var H=e.compositeRule;e.compositeRule=d.compositeRule=!0,d.schema=S,d.schemaPath=e.schemaPath+".additionalProperties",d.errSchemaPath=e.errSchemaPath+"/additionalProperties",d.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);X=u+"["+v+"]";d.dataPathArr[g]=v;pe=e.validate(d);d.baseId=D,e.util.varOccurences(pe,P)<2?a+=" "+e.util.varReplace(pe,P,X)+" ":a+=" var "+P+" = "+X+"; "+pe+" ",a+=" if (!"+m+") { errors = "+f+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+u+"["+v+"]; }  ",e.compositeRule=d.compositeRule=H}else{d.schema=S,d.schemaPath=e.schemaPath+".additionalProperties",d.errSchemaPath=e.errSchemaPath+"/additionalProperties",d.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);X=u+"["+v+"]";d.dataPathArr[g]=v;pe=e.validate(d);d.baseId=D,e.util.varOccurences(pe,P)<2?a+=" "+e.util.varReplace(pe,P,X)+" ":a+=" var "+P+" = "+X+"; "+pe+" ",h&&(a+=" if (!"+m+") break; ")}e.errorPath=M}_&&(a+=" } "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}var K=e.opts.useDefaults&&!e.compositeRule;if(b.length){var G=b;if(G)for(var J,Z=-1,Y=G.length-1;Z<Y;){de=i[J=G[Z+=1]];if(e.util.schemaHasRules(de,e.RULES.all)){var W=e.util.getProperty(J),X=u+W,ee=K&&void 0!==de.default;d.schema=de,d.schemaPath=n+W,d.errSchemaPath=l+"/"+e.util.escapeFragment(J),d.errorPath=e.util.getPath(e.errorPath,J,e.opts.jsonPointers),d.dataPathArr[g]=e.util.toQuotedString(J);pe=e.validate(d);if(d.baseId=D,e.util.varOccurences(pe,P)<2){pe=e.util.varReplace(pe,P,X);re=X}else{var re=P;a+=" var "+P+" = "+X+"; "}if(ee)a+=" "+pe+" ";else{if(A&&A[J]){a+=" if ( "+re+" === undefined ",O&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(J)+"') "),a+=") { "+m+" = false; ";var M=e.errorPath,te=l,ae=e.util.escapeQuotes(J);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(M,J,e.opts.jsonPointers)),l=e.errSchemaPath+"/required",(Ee=Ee||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+ae+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+ae+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";be=a;a=Ee.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+be+"]); ":" validate.errors = ["+be+"]; return false; ":" var err = "+be+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l=te,e.errorPath=M,a+=" } else { "}else h?(a+=" if ( "+re+" === undefined ",O&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(J)+"') "),a+=") { "+m+" = true; } else { "):(a+=" if ("+re+" !== undefined ",O&&(a+=" &&   Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(J)+"') "),a+=" ) { ");a+=" "+pe+" } "}}h&&(a+=" if ("+m+") { ",p+="}")}}if(j.length){var se=j;if(se)for(var oe,ie=-1,ne=se.length-1;ie<ne;){de=w[oe=se[ie+=1]];if(e.util.schemaHasRules(de,e.RULES.all)){d.schema=de,d.schemaPath=e.schemaPath+".patternProperties"+e.util.getProperty(oe),d.errSchemaPath=e.errSchemaPath+"/patternProperties/"+e.util.escapeFragment(oe),a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",a+=" if ("+e.usePattern(oe)+".test("+v+")) { ",d.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);X=u+"["+v+"]";d.dataPathArr[g]=v;pe=e.validate(d);d.baseId=D,e.util.varOccurences(pe,P)<2?a+=" "+e.util.varReplace(pe,P,X)+" ":a+=" var "+P+" = "+X+"; "+pe+" ",h&&(a+=" if (!"+m+") break; "),a+=" } ",h&&(a+=" else "+m+" = true; "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}}}if(e.opts.patternGroups&&q.length){var le=q;if(le)for(var he,ue=-1,ce=le.length-1;ue<ce;){var fe=k[he=le[ue+=1]],de=fe.schema;if(e.util.schemaHasRules(de,e.RULES.all)){d.schema=de,d.schemaPath=e.schemaPath+".patternGroups"+e.util.getProperty(he)+".schema",d.errSchemaPath=e.errSchemaPath+"/patternGroups/"+e.util.escapeFragment(he)+"/schema",a+=" var pgPropCount"+s+" = 0;  ",a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",a+=" if ("+e.usePattern(he)+".test("+v+")) { pgPropCount"+s+"++; ",d.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);X=u+"["+v+"]";d.dataPathArr[g]=v;var pe=e.validate(d);d.baseId=D,e.util.varOccurences(pe,P)<2?a+=" "+e.util.varReplace(pe,P,X)+" ":a+=" var "+P+" = "+X+"; "+pe+" ",h&&(a+=" if (!"+m+") break; "),a+=" } ",h&&(a+=" else "+m+" = true; "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}");var me=fe.minimum,ve=fe.maximum;if(void 0!==me||void 0!==ve){a+=" var "+c+" = true; ";te=l;if(void 0!==me){var ye=me,ge="minimum",Pe="less";a+=" "+c+" = pgPropCount"+s+" >= "+me+"; ",l=e.errSchemaPath+"/patternGroups/minimum",a+="  if (!"+c+") {   ",(Ee=Ee||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'patternGroups' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { reason: '"+ge+"', limit: "+ye+", pattern: '"+e.util.escapeQuotes(he)+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have "+Pe+" than "+ye+' properties matching pattern "'+e.util.escapeQuotes(he)+"\"' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";be=a;a=Ee.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+be+"]); ":" validate.errors = ["+be+"]; return false; ":" var err = "+be+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",void 0!==ve&&(a+=" else ")}if(void 0!==ve){var ye=ve,ge="maximum",Pe="more";a+=" "+c+" = pgPropCount"+s+" <= "+ve+"; ",l=e.errSchemaPath+"/patternGroups/maximum",a+="  if (!"+c+") {   ";var Ee=Ee||[];Ee.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'patternGroups' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { reason: '"+ge+"', limit: "+ye+", pattern: '"+e.util.escapeQuotes(he)+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have "+Pe+" than "+ye+' properties matching pattern "'+e.util.escapeQuotes(he)+"\"' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var be=a;a=Ee.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+be+"]); ":" validate.errors = ["+be+"]; return false; ":" var err = "+be+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } "}l=te,h&&(a+=" if ("+c+") { ",p+="}")}}}}return h&&(a+=" "+p+" if ("+f+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],31:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,f=e.util.copy(e),d="valid"+ ++f.level;if(e.util.schemaHasRules(i,e.RULES.all)){f.schema=i,f.schemaPath=n,f.errSchemaPath=l;var p="key"+s,m="idx"+s,v="i"+s,y="' + "+p+" + '",g="data"+(f.dataLevel=e.dataLevel+1),P="dataProperties"+s,E=e.opts.ownProperties,b=e.baseId;a+=" var "+c+" = errors; ",E&&(a+=" var "+P+" = undefined; "),a+=E?" "+P+" = "+P+" || Object.keys("+u+"); for (var "+m+"=0; "+m+"<"+P+".length; "+m+"++) { var "+p+" = "+P+"["+m+"]; ":" for (var "+p+" in "+u+") { ",a+=" var startErrs"+s+" = errors; ";var w=p,j=e.compositeRule;e.compositeRule=f.compositeRule=!0;var S=e.validate(f);f.baseId=b,e.util.varOccurences(S,g)<2?a+=" "+e.util.varReplace(S,g,w)+" ":a+=" var "+g+" = "+w+"; "+S+" ",e.compositeRule=f.compositeRule=j,a+=" if (!"+d+") { for (var "+v+"=startErrs"+s+"; "+v+"<errors; "+v+"++) { vErrors["+v+"].propertyName = "+p+"; }   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'propertyNames' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { propertyName: '"+y+"' } ",!1!==e.opts.messages&&(a+=" , message: 'property name \\'"+y+"\\' is invalid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),h&&(a+=" break; "),a+=" } }"}return h&&(a+="  if ("+c+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],32:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s,o=" ",i=e.level,n=e.dataLevel,l=e.schema[r],h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(n||""),f="valid"+i;if("#"==l||"#/"==l)e.isRoot?(a=e.async,s="validate"):(a=!0===e.root.schema.$async,s="root.refVal[0]");else{var d=e.resolveRef(e.baseId,l,e.isRoot);if(void 0===d){var p=e.MissingRefError.message(e.baseId,l);if("fail"==e.opts.missingRefs){console.error(p),(g=g||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '$ref' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { ref: '"+e.util.escapeQuotes(l)+"' } ",!1!==e.opts.messages&&(o+=" , message: 'can\\'t resolve reference "+e.util.escapeQuotes(l)+"' "),e.opts.verbose&&(o+=" , schema: "+e.util.toQuotedString(l)+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),o+=" } "):o+=" {} ";var m=o;o=g.pop(),o+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",u&&(o+=" if (false) { ")}else{if("ignore"!=e.opts.missingRefs)throw new e.MissingRefError(e.baseId,l,p);console.warn(p),u&&(o+=" if (true) { ")}}else if(d.inline){var v=e.util.copy(e),y="valid"+ ++v.level;v.schema=d.schema,v.schemaPath="",v.errSchemaPath=l,o+=" "+e.validate(v).replace(/validate\.schema/g,d.code)+" ",u&&(o+=" if ("+y+") { ")}else a=!0===d.$async,s=d.code}if(s){var g=g||[];g.push(o),o="",o+=e.opts.passContext?" "+s+".call(this, ":" "+s+"( ",o+=" "+c+", (dataPath || '')",'""'!=e.errorPath&&(o+=" + "+e.errorPath);var P=o+=" , "+(n?"data"+(n-1||""):"parentData")+" , "+(n?e.dataPathArr[n]:"parentDataProperty")+", rootData)  ";if(o=g.pop(),a){if(!e.async)throw new Error("async schema referenced by sync schema");u&&(o+=" var "+f+"; "),o+=" try { "+e.yieldAwait+" "+P+"; ",u&&(o+=" "+f+" = true; "),o+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ",u&&(o+=" "+f+" = false; "),o+=" } ",u&&(o+=" if ("+f+") { ")}else o+=" if (!"+P+") { if (vErrors === null) vErrors = "+s+".errors; else vErrors = vErrors.concat("+s+".errors); errors = vErrors.length; } ",u&&(o+=" else { ")}return o}},{}],33:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,f=e.opts.$data&&i&&i.$data;f&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ");var d="schema"+s;if(!f)if(i.length<e.opts.loopRequired&&e.schema.properties&&Object.keys(e.schema.properties).length){var p=[],m=i;if(m)for(var v,y=-1,g=m.length-1;y<g;){v=m[y+=1];var P=e.schema.properties[v];P&&e.util.schemaHasRules(P,e.RULES.all)||(p[p.length]=v)}}else p=i;if(f||p.length){var E=e.errorPath,b=f||p.length>=e.opts.loopRequired,w=e.opts.ownProperties;if(h)if(a+=" var missing"+s+"; ",b){f||(a+=" var "+d+" = validate.schema"+n+"; ");R="' + "+($="schema"+s+"["+(S="i"+s)+"]")+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(E,$,e.opts.jsonPointers)),a+=" var "+c+" = true; ",f&&(a+=" if (schema"+s+" === undefined) "+c+" = true; else if (!Array.isArray(schema"+s+")) "+c+" = false; else {"),a+=" for (var "+S+" = 0; "+S+" < "+d+".length; "+S+"++) { "+c+" = "+u+"["+d+"["+S+"]] !== undefined ",w&&(a+=" &&   Object.prototype.hasOwnProperty.call("+u+", "+d+"["+S+"]) "),a+="; if (!"+c+") break; } ",f&&(a+="  }  "),a+="  if (!"+c+") {   ",(x=x||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+R+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+R+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";F=a;a=x.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+F+"]); ":" validate.errors = ["+F+"]; return false; ":" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { "}else{a+=" if ( ";var j=p;if(j)for(var S=-1,_=j.length-1;S<_;)D=j[S+=1],S&&(a+=" || "),a+=" ( ( "+(q=u+(k=e.util.getProperty(D)))+" === undefined ",w&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(D)+"') "),a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?D:k)+") ) ";a+=") {  ";R="' + "+($="missing"+s)+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(E,$,!0):E+" + "+$);var x=x||[];x.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+R+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+R+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var F=a;a=x.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+F+"]); ":" validate.errors = ["+F+"]; return false; ":" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { "}else if(b){f||(a+=" var "+d+" = validate.schema"+n+"; ");var $="schema"+s+"["+(S="i"+s)+"]",R="' + "+$+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(E,$,e.opts.jsonPointers)),f&&(a+=" if ("+d+" && !Array.isArray("+d+")) {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+R+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+R+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+d+" !== undefined) { "),a+=" for (var "+S+" = 0; "+S+" < "+d+".length; "+S+"++) { if ("+u+"["+d+"["+S+"]] === undefined ",w&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", "+d+"["+S+"]) "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+R+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+R+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ",f&&(a+="  }  ")}else{var O=p;if(O)for(var D,I=-1,A=O.length-1;I<A;){D=O[I+=1];var k=e.util.getProperty(D),R=e.util.escapeQuotes(D),q=u+k;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(E,D,e.opts.jsonPointers)),a+=" if ( "+q+" === undefined ",w&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(D)+"') "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+R+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+R+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}e.errorPath=E}else h&&(a+=" if (true) {");return a}},{}],34:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),f="valid"+o,d=e.opts.$data&&n&&n.$data;if(d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n,(n||d)&&!1!==e.opts.uniqueItems){d&&(s+=" var "+f+"; if ("+a+" === false || "+a+" === undefined) "+f+" = true; else if (typeof "+a+" != 'boolean') "+f+" = false; else { "),s+=" var "+f+" = true; if ("+c+".length > 1) { var i = "+c+".length, j; outer: for (;i--;) { for (j = i; j--;) { if (equal("+c+"[i], "+c+"[j])) { "+f+" = false; break outer; } } } } ",d&&(s+="  }  "),s+=" if (!"+f+") {   ";var p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'uniqueItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { i: i, j: j } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' "),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } ",u&&(s+=" else { ")}else u&&(s+=" if (true) { ");return s}},{}],35:[function(e,r,t){"use strict";r.exports=function(e,r,t){function a(e){for(var r=e.rules,t=0;t<r.length;t++)if(s(r[t]))return!0}function s(r){return void 0!==e.schema[r.keyword]||r.implements&&o(r)}function o(r){for(var t=r.implements,a=0;a<t.length;a++)if(void 0!==e.schema[t[a]])return!0}var i="",n=!0===e.schema.$async,l=e.util.schemaHasRulesExcept(e.schema,e.RULES.all,"$ref"),h=e.self._getId(e.schema);if(e.isTop){if(n){e.async=!0;var u="es7"==e.opts.async;e.yieldAwait=u?"await":"yield"}i+=" var validate = ",n?u?i+=" (async function ":("*"!=e.opts.async&&(i+="co.wrap"),i+="(function* "):i+=" (function ",i+=" (data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ",h&&(e.opts.sourceCode||e.opts.processCode)&&(i+=" /*# sourceURL="+h+" */ ")}if("boolean"==typeof e.schema||!l&&!e.schema.$ref){var c=e.level,f=e.dataLevel,d=e.schema["false schema"],p=e.schemaPath+e.util.getProperty("false schema"),m=e.errSchemaPath+"/false schema",v=!e.opts.allErrors,y="data"+(f||""),g="valid"+c;if(!1===e.schema){e.isTop?v=!0:i+=" var "+g+" = false; ",(X=X||[]).push(i),i="",!1!==e.createErrors?(i+=" { keyword: '"+(E||"false schema")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(m)+" , params: {} ",!1!==e.opts.messages&&(i+=" , message: 'boolean schema is false' "),e.opts.verbose&&(i+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+y+" "),i+=" } "):i+=" {} ";ee=i;i=X.pop(),i+=!e.compositeRule&&v?e.async?" throw new ValidationError(["+ee+"]); ":" validate.errors = ["+ee+"]; return false; ":" var err = "+ee+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else i+=e.isTop?n?" return data; ":" validate.errors = null; return true; ":" var "+g+" = true; ";return e.isTop&&(i+=" }); return validate; "),i}if(e.isTop){var P=e.isTop,c=e.level=0,f=e.dataLevel=0,y="data";e.rootId=e.resolve.fullPath(e.self._getId(e.root.schema)),e.baseId=e.baseId||e.rootId,delete e.isTop,e.dataPathArr=[void 0],i+=" var vErrors = null; ",i+=" var errors = 0;     ",i+=" if (rootData === undefined) rootData = data; "}else{var c=e.level,y="data"+((f=e.dataLevel)||"");if(h&&(e.baseId=e.resolve.url(e.baseId,h)),n&&!e.async)throw new Error("async schema in sync schema");i+=" var errs_"+c+" = errors;"}var E,g="valid"+c,v=!e.opts.allErrors,b="",w="",j=e.schema.type,S=Array.isArray(j);if(S&&1==j.length&&(j=j[0],S=!1),e.schema.$ref&&l){if("fail"==e.opts.extendRefs)throw new Error('$ref: validation keywords used in schema at path "'+e.errSchemaPath+'" (see option extendRefs)');!0!==e.opts.extendRefs&&(l=!1,console.warn('$ref: keywords ignored in schema at path "'+e.errSchemaPath+'"'))}if(j){if(e.opts.coerceTypes)var _=e.util.coerceToTypes(e.opts.coerceTypes,j);L=e.RULES.types[j];if(_||S||!0===L||L&&!a(L)){var p=e.schemaPath+".type",m=e.errSchemaPath+"/type",p=e.schemaPath+".type",m=e.errSchemaPath+"/type",x=S?"checkDataTypes":"checkDataType";if(i+=" if ("+e.util[x](j,y,!0)+") { ",_){var F="dataType"+c,$="coerced"+c;i+=" var "+F+" = typeof "+y+"; ","array"==e.opts.coerceTypes&&(i+=" if ("+F+" == 'object' && Array.isArray("+y+")) "+F+" = 'array'; "),i+=" var "+$+" = undefined; ";var R="",O=_;if(O)for(var D,I=-1,A=O.length-1;I<A;)D=O[I+=1],I&&(i+=" if ("+$+" === undefined) { ",R+="}"),"array"==e.opts.coerceTypes&&"array"!=D&&(i+=" if ("+F+" == 'array' && "+y+".length == 1) { "+$+" = "+y+" = "+y+"[0]; "+F+" = typeof "+y+";  } "),"string"==D?i+=" if ("+F+" == 'number' || "+F+" == 'boolean') "+$+" = '' + "+y+"; else if ("+y+" === null) "+$+" = ''; ":"number"==D||"integer"==D?(i+=" if ("+F+" == 'boolean' || "+y+" === null || ("+F+" == 'string' && "+y+" && "+y+" == +"+y+" ","integer"==D&&(i+=" && !("+y+" % 1)"),i+=")) "+$+" = +"+y+"; "):"boolean"==D?i+=" if ("+y+" === 'false' || "+y+" === 0 || "+y+" === null) "+$+" = false; else if ("+y+" === 'true' || "+y+" === 1) "+$+" = true; ":"null"==D?i+=" if ("+y+" === '' || "+y+" === 0 || "+y+" === false) "+$+" = null; ":"array"==e.opts.coerceTypes&&"array"==D&&(i+=" if ("+F+" == 'string' || "+F+" == 'number' || "+F+" == 'boolean' || "+y+" == null) "+$+" = ["+y+"]; ");i+=" "+R+" if ("+$+" === undefined) {   ",(X=X||[]).push(i),i="",!1!==e.createErrors?(i+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(m)+" , params: { type: '",i+=S?""+j.join(","):""+j,i+="' } ",!1!==e.opts.messages&&(i+=" , message: 'should be ",i+=S?""+j.join(","):""+j,i+="' "),e.opts.verbose&&(i+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+y+" "),i+=" } "):i+=" {} ";ee=i;i=X.pop(),i+=!e.compositeRule&&v?e.async?" throw new ValidationError(["+ee+"]); ":" validate.errors = ["+ee+"]; return false; ":" var err = "+ee+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",i+=" } else {  ";var k=f?"data"+(f-1||""):"parentData",q=f?e.dataPathArr[f]:"parentDataProperty";i+=" "+y+" = "+$+"; ",f||(i+="if ("+k+" !== undefined)"),i+=" "+k+"["+q+"] = "+$+"; } "}else{(X=X||[]).push(i),i="",!1!==e.createErrors?(i+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(m)+" , params: { type: '",i+=S?""+j.join(","):""+j,i+="' } ",!1!==e.opts.messages&&(i+=" , message: 'should be ",i+=S?""+j.join(","):""+j,i+="' "),e.opts.verbose&&(i+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+y+" "),i+=" } "):i+=" {} ";ee=i;i=X.pop(),i+=!e.compositeRule&&v?e.async?" throw new ValidationError(["+ee+"]); ":" validate.errors = ["+ee+"]; return false; ":" var err = "+ee+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}i+=" } "}}if(e.schema.$ref&&!l)i+=" "+e.RULES.all.$ref.code(e,"$ref")+" ",v&&(i+=" } if (errors === ",i+=P?"0":"errs_"+c,i+=") { ",w+="}");else{e.opts.v5&&e.schema.patternGroups&&console.warn('keyword "patternGroups" is deprecated and disabled. Use option patternGroups: true to enable.');var C=e.RULES;if(C)for(var L,z=-1,Q=C.length-1;z<Q;)if(L=C[z+=1],a(L)){if(L.type&&(i+=" if ("+e.util.checkDataType(L.type,y)+") { "),e.opts.useDefaults&&!e.compositeRule)if("object"==L.type&&e.schema.properties){var d=e.schema.properties,U=Object.keys(d);if(U)for(var V,N=-1,T=U.length-1;N<T;)void 0!==(B=d[V=U[N+=1]]).default&&(i+="  if ("+(K=y+e.util.getProperty(V))+" === undefined) "+K+" = ",i+="shared"==e.opts.useDefaults?" "+e.useDefault(B.default)+" ":" "+JSON.stringify(B.default)+" ",i+="; ")}else if("array"==L.type&&Array.isArray(e.schema.items)){var M=e.schema.items;if(M)for(var B,I=-1,H=M.length-1;I<H;)if(void 0!==(B=M[I+=1]).default){var K=y+"["+I+"]";i+="  if ("+K+" === undefined) "+K+" = ",i+="shared"==e.opts.useDefaults?" "+e.useDefault(B.default)+" ":" "+JSON.stringify(B.default)+" ",i+="; "}}var G=L.rules;if(G)for(var J,Z=-1,Y=G.length-1;Z<Y;)if(J=G[Z+=1],s(J)){var W=J.code(e,J.keyword,L.type);W&&(i+=" "+W+" ",v&&(b+="}"))}if(v&&(i+=" "+b+" ",b=""),L.type&&(i+=" } ",j&&j===L.type&&!_)){i+=" else { ";var p=e.schemaPath+".type",m=e.errSchemaPath+"/type",X=X||[];X.push(i),i="",!1!==e.createErrors?(i+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(m)+" , params: { type: '",i+=S?""+j.join(","):""+j,i+="' } ",!1!==e.opts.messages&&(i+=" , message: 'should be ",i+=S?""+j.join(","):""+j,i+="' "),e.opts.verbose&&(i+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+y+" "),i+=" } "):i+=" {} ";var ee=i;i=X.pop(),i+=!e.compositeRule&&v?e.async?" throw new ValidationError(["+ee+"]); ":" validate.errors = ["+ee+"]; return false; ":" var err = "+ee+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",i+=" } "}v&&(i+=" if (errors === ",i+=P?"0":"errs_"+c,i+=") { ",w+="}")}}return v&&(i+=" "+w+" "),P?(n?(i+=" if (errors === 0) return data;           ",i+=" else throw new ValidationError(vErrors); "):(i+=" validate.errors = vErrors; ",i+=" return errors === 0;       "),i+=" }); return validate;"):i+=" var "+g+" = errors === errs_"+c+";",i=e.util.cleanUpCode(i),P&&(i=e.util.finalCleanUpCode(i,n)),i}},{}],36:[function(e,r,t){"use strict";function a(e,r){function t(e,r,t){for(var a,o=0;o<s.length;o++){var i=s[o];if(i.type==r){a=i;break}}a||(a={type:r,rules:[]},s.push(a));var l={keyword:e,definition:t,custom:!0,code:n,implements:t.implements};a.rules.push(l),s.custom[e]=l}function a(e){if(!s.types[e])throw new Error("Unknown type "+e)}var s=this.RULES;if(s.keywords[e])throw new Error("Keyword "+e+" is already defined");if(!i.test(e))throw new Error("Keyword "+e+" is not a valid identifier");if(r){if(r.macro&&void 0!==r.valid)throw new Error('"valid" option cannot be used with macro keywords');var o=r.type;if(Array.isArray(o)){var l,h=o.length;for(l=0;l<h;l++)a(o[l]);for(l=0;l<h;l++)t(e,o[l],r)}else o&&a(o),t(e,o,r);var u=!0===r.$data&&this._opts.$data;if(u&&!r.validate)throw new Error('$data support: "validate" function is not defined');var c=r.metaSchema;c&&(u&&(c={anyOf:[c,{$ref:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#"}]}),r.validateSchema=this.compile(c,!0))}s.keywords[e]=s.all[e]=!0}function s(e){var r=this.RULES.custom[e];return r?r.definition:this.RULES.keywords[e]||!1}function o(e){var r=this.RULES;delete r.keywords[e],delete r.all[e],delete r.custom[e];for(var t=0;t<r.length;t++)for(var a=r[t].rules,s=0;s<a.length;s++)if(a[s].keyword==e){a.splice(s,1);break}}var i=/^[a-z_$][a-z0-9_$\-]*$/i,n=e("./dotjs/custom");r.exports={add:a,get:s,remove:o}},{"./dotjs/custom":21}],37:[function(e,r,t){"use strict";r.exports=function(e){var r=e._opts.defaultMeta,t="string"==typeof r?{$ref:r}:e.getSchema("http://json-schema.org/draft-06/schema")?{$ref:"http://json-schema.org/draft-06/schema"}:{};e.addKeyword("patternGroups",{metaSchema:{type:"object",additionalProperties:{type:"object",required:["schema"],properties:{maximum:{type:"integer",minimum:0},minimum:{type:"integer",minimum:0},schema:t},additionalProperties:!1}}}),e.RULES.all.properties.implements.push("patternGroups")}},{}],38:[function(e,r,t){r.exports={$schema:"http://json-schema.org/draft-06/schema#",$id:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#",description:"Meta-schema for $data reference (JSON-schema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}},{}],39:[function(e,r,t){r.exports={$schema:"http://json-schema.org/draft-06/schema#",$id:"http://json-schema.org/draft-06/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:{},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:{}}},{}],40:[function(e,r,t){function a(e){var r=this,t=f.call(arguments,1);return new Promise(function(a,o){function i(r){var t;try{t=e.next(r)}catch(e){return o(e)}h(t)}function n(r){var t;try{t=e.throw(r)}catch(e){return o(e)}h(t)}function h(e){if(e.done)return a(e.value);var t=s.call(r,e.value);return t&&l(t)?t.then(i,n):n(new TypeError('You may only yield a function, promise, generator, array, or object, but the following object was passed: "'+String(e.value)+'"'))}if("function"==typeof e&&(e=e.apply(r,t)),!e||"function"!=typeof e.next)return a(e);i()})}function s(e){return e?l(e)?e:u(e)||h(e)?a.call(this,e):"function"==typeof e?o.call(this,e):Array.isArray(e)?i.call(this,e):c(e)?n.call(this,e):e:e}function o(e){var r=this;return new Promise(function(t,a){e.call(r,function(e,r){if(e)return a(e);arguments.length>2&&(r=f.call(arguments,1)),t(r)})})}function i(e){return Promise.all(e.map(s,this))}function n(e){for(var r=new e.constructor,t=Object.keys(e),a=[],o=0;o<t.length;o++){var i=t[o],n=s.call(this,e[i]);n&&l(n)?function(e,t){r[t]=void 0,a.push(e.then(function(e){r[t]=e}))}(n,i):r[i]=e[i]}return Promise.all(a).then(function(){return r})}function l(e){return"function"==typeof e.then}function h(e){return"function"==typeof e.next&&"function"==typeof e.throw}function u(e){var r=e.constructor;return!!r&&("GeneratorFunction"===r.name||"GeneratorFunction"===r.displayName||h(r.prototype))}function c(e){return Object==e.constructor}var f=Array.prototype.slice;r.exports=a.default=a.co=a,a.wrap=function(e){function r(){return a.call(this,e.apply(this,arguments))}return r.__generatorFunction__=e,r}},{}],41:[function(e,r,t){"use strict";r.exports=function e(r,t){if(r===t)return!0;var a,s=Array.isArray(r),o=Array.isArray(t);if(s&&o){if(r.length!=t.length)return!1;for(a=0;a<r.length;a++)if(!e(r[a],t[a]))return!1;return!0}if(s!=o)return!1;if(r&&t&&"object"==typeof r&&"object"==typeof t){var i=Object.keys(r);if(i.length!==Object.keys(t).length)return!1;var n=r instanceof Date,l=t instanceof Date;if(n&&l)return r.getTime()==t.getTime();if(n!=l)return!1;var h=r instanceof RegExp,u=t instanceof RegExp;if(h&&u)return r.toString()==t.toString();if(h!=u)return!1;for(a=0;a<i.length;a++)if(!Object.prototype.hasOwnProperty.call(t,i[a]))return!1;for(a=0;a<i.length;a++)if(!e(r[i[a]],t[i[a]]))return!1;return!0}return!1}},{}],42:[function(e,r,t){"use strict";function a(e,r,t,i,n,l,h,u,c){if(t&&"object"==typeof t&&!Array.isArray(t)){r(t,i,n,l,h,u,c);for(var f in t){var d=t[f];if(Array.isArray(d)){if(f in o.arrayKeywords)for(var p=0;p<d.length;p++)a(e,r,d[p],i+"/"+f+"/"+p,n,i,f,t,p)}else if(f in o.propsKeywords){if(d&&"object"==typeof d)for(var m in d)a(e,r,d[m],i+"/"+f+"/"+s(m),n,i,f,t,m)}else(f in o.keywords||e.allKeys&&!(f in o.skipKeywords))&&a(e,r,d,i+"/"+f,n,i,f,t)}}}function s(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}var o=r.exports=function(e,r,t){"function"==typeof r&&(t=r,r={}),a(r,t,e,"",e)};o.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0},o.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},o.propsKeywords={definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},o.skipKeywords={enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},{}],43:[function(e,r,t){var a="undefined"!=typeof JSON?JSON:e("jsonify");r.exports=function(e,r){r||(r={}),"function"==typeof r&&(r={cmp:r});var t=r.space||"";"number"==typeof t&&(t=Array(t+1).join(" "));var i="boolean"==typeof r.cycles&&r.cycles,n=r.replacer||function(e,r){return r},l=r.cmp&&function(e){return function(r){return function(t,a){return e({key:t,value:r[t]},{key:a,value:r[a]})}}}(r.cmp),h=[];return function e(r,u,c,f){var d=t?"\n"+new Array(f+1).join(t):"",p=t?": ":":";if(c&&c.toJSON&&"function"==typeof c.toJSON&&(c=c.toJSON()),void 0!==(c=n.call(r,u,c))){if("object"!=typeof c||null===c)return a.stringify(c);if(s(c)){for(var m=[],v=0;v<c.length;v++){var y=e(c,v,c[v],f+1)||a.stringify(null);m.push(d+t+y)}return"["+m.join(",")+d+"]"}if(-1!==h.indexOf(c)){if(i)return a.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}h.push(c);for(var g=o(c).sort(l&&l(c)),m=[],v=0;v<g.length;v++){var P=e(c,u=g[v],c[u],f+1);if(P){var E=a.stringify(u)+p+P;m.push(d+t+E)}}return h.splice(h.indexOf(c),1),"{"+m.join(",")+d+"}"}}({"":e},"",e,0)};var s=Array.isArray||function(e){return"[object Array]"==={}.toString.call(e)},o=Object.keys||function(e){var r=Object.prototype.hasOwnProperty||function(){return!0},t=[];for(var a in e)r.call(e,a)&&t.push(a);return t}},{jsonify:44}],44:[function(e,r,t){t.parse=e("./lib/parse"),t.stringify=e("./lib/stringify")},{"./lib/parse":45,"./lib/stringify":46}],45:[function(e,r,t){var a,s,o,i,n={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"},l=function(e){throw{name:"SyntaxError",message:e,at:a,text:o}},h=function(e){return e&&e!==s&&l("Expected '"+e+"' instead of '"+s+"'"),s=o.charAt(a),a+=1,s},u=function(){var e,r="";for("-"===s&&(r="-",h("-"));s>="0"&&s<="9";)r+=s,h();if("."===s)for(r+=".";h()&&s>="0"&&s<="9";)r+=s;if("e"===s||"E"===s)for(r+=s,h(),"-"!==s&&"+"!==s||(r+=s,h());s>="0"&&s<="9";)r+=s,h();if(e=+r,isFinite(e))return e;l("Bad number")},c=function(){var e,r,t,a="";if('"'===s)for(;h();){if('"'===s)return h(),a;if("\\"===s)if(h(),"u"===s){for(t=0,r=0;r<4&&(e=parseInt(h(),16),isFinite(e));r+=1)t=16*t+e;a+=String.fromCharCode(t)}else{if("string"!=typeof n[s])break;a+=n[s]}else a+=s}l("Bad string")},f=function(){for(;s&&s<=" ";)h()},d=function(){switch(s){case"t":return h("t"),h("r"),h("u"),h("e"),!0;case"f":return h("f"),h("a"),h("l"),h("s"),h("e"),!1;case"n":return h("n"),h("u"),h("l"),h("l"),null}l("Unexpected '"+s+"'")},p=function(){var e=[];if("["===s){if(h("["),f(),"]"===s)return h("]"),e;for(;s;){if(e.push(i()),f(),"]"===s)return h("]"),e;h(","),f()}}l("Bad array")},m=function(){var e,r={};if("{"===s){if(h("{"),f(),"}"===s)return h("}"),r;for(;s;){if(e=c(),f(),h(":"),Object.hasOwnProperty.call(r,e)&&l('Duplicate key "'+e+'"'),r[e]=i(),f(),"}"===s)return h("}"),r;h(","),f()}}l("Bad object")};i=function(){switch(f(),s){case"{":return m();case"[":return p();case'"':return c();case"-":return u();default:return s>="0"&&s<="9"?u():d()}},r.exports=function(e,r){var t;return o=e,a=0,s=" ",t=i(),f(),s&&l("Syntax error"),"function"==typeof r?function e(t,a){var s,o,i=t[a];if(i&&"object"==typeof i)for(s in i)Object.prototype.hasOwnProperty.call(i,s)&&(void 0!==(o=e(i,s))?i[s]=o:delete i[s]);return r.call(t,a,i)}({"":t},""):t}},{}],46:[function(e,r,t){function a(e){return l.lastIndex=0,l.test(e)?'"'+e.replace(l,function(e){var r=h[e];return"string"==typeof r?r:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function s(e,r){var t,l,h,u,c,f=o,d=r[e];switch(d&&"object"==typeof d&&"function"==typeof d.toJSON&&(d=d.toJSON(e)),"function"==typeof n&&(d=n.call(r,e,d)),typeof d){case"string":return a(d);case"number":return isFinite(d)?String(d):"null";case"boolean":case"null":return String(d);case"object":if(!d)return"null";if(o+=i,c=[],"[object Array]"===Object.prototype.toString.apply(d)){for(u=d.length,t=0;t<u;t+=1)c[t]=s(t,d)||"null";return h=0===c.length?"[]":o?"[\n"+o+c.join(",\n"+o)+"\n"+f+"]":"["+c.join(",")+"]",o=f,h}if(n&&"object"==typeof n)for(u=n.length,t=0;t<u;t+=1)"string"==typeof(l=n[t])&&(h=s(l,d))&&c.push(a(l)+(o?": ":":")+h);else for(l in d)Object.prototype.hasOwnProperty.call(d,l)&&(h=s(l,d))&&c.push(a(l)+(o?": ":":")+h);return h=0===c.length?"{}":o?"{\n"+o+c.join(",\n"+o)+"\n"+f+"}":"{"+c.join(",")+"}",o=f,h}}var o,i,n,l=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,h={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};r.exports=function(e,r,t){var a;if(o="",i="","number"==typeof t)for(a=0;a<t;a+=1)i+=" ";else"string"==typeof t&&(i=t);if(n=r,r&&"function"!=typeof r&&("object"!=typeof r||"number"!=typeof r.length))throw new Error("JSON.stringify");return s("",{"":e})}},{}],47:[function(e,r,t){(function(e){!function(a){function s(e){throw new RangeError(A[e])}function o(e,r){for(var t=e.length,a=[];t--;)a[t]=r(e[t]);return a}function i(e,r){var t=e.split("@"),a="";return t.length>1&&(a=t[0]+"@",e=t[1]),a+o((e=e.replace(I,".")).split("."),r).join(".")}function n(e){for(var r,t,a=[],s=0,o=e.length;s<o;)(r=e.charCodeAt(s++))>=55296&&r<=56319&&s<o?56320==(64512&(t=e.charCodeAt(s++)))?a.push(((1023&r)<<10)+(1023&t)+65536):(a.push(r),s--):a.push(r);return a}function l(e){return o(e,function(e){var r="";return e>65535&&(r+=C((e-=65536)>>>10&1023|55296),e=56320|1023&e),r+=C(e)}).join("")}function h(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:w}function u(e,r){return e+22+75*(e<26)-((0!=r)<<5)}function c(e,r,t){var a=0;for(e=t?q(e/x):e>>1,e+=q(e/r);e>k*S>>1;a+=w)e=q(e/k);return q(a+(k+1)*e/(e+_))}function f(e){var r,t,a,o,i,n,u,f,d,p,m=[],v=e.length,y=0,g=$,P=F;for((t=e.lastIndexOf(R))<0&&(t=0),a=0;a<t;++a)e.charCodeAt(a)>=128&&s("not-basic"),m.push(e.charCodeAt(a));for(o=t>0?t+1:0;o<v;){for(i=y,n=1,u=w;o>=v&&s("invalid-input"),((f=h(e.charCodeAt(o++)))>=w||f>q((b-y)/n))&&s("overflow"),y+=f*n,d=u<=P?j:u>=P+S?S:u-P,!(f<d);u+=w)n>q(b/(p=w-d))&&s("overflow"),n*=p;P=c(y-i,r=m.length+1,0==i),q(y/r)>b-g&&s("overflow"),g+=q(y/r),y%=r,m.splice(y++,0,g)}return l(m)}function d(e){var r,t,a,o,i,l,h,f,d,p,m,v,y,g,P,E=[];for(v=(e=n(e)).length,r=$,t=0,i=F,l=0;l<v;++l)(m=e[l])<128&&E.push(C(m));for(a=o=E.length,o&&E.push(R);a<v;){for(h=b,l=0;l<v;++l)(m=e[l])>=r&&m<h&&(h=m);for(h-r>q((b-t)/(y=a+1))&&s("overflow"),t+=(h-r)*y,r=h,l=0;l<v;++l)if((m=e[l])<r&&++t>b&&s("overflow"),m==r){for(f=t,d=w;p=d<=i?j:d>=i+S?S:d-i,!(f<p);d+=w)P=f-p,g=w-p,E.push(C(u(p+P%g,0))),f=q(P/g);E.push(C(u(f,0))),i=c(t,y,a==o),t=0,++a}++t,++r}return E.join("")}function p(e){return i(e,function(e){return O.test(e)?f(e.slice(4).toLowerCase()):e})}function m(e){return i(e,function(e){return D.test(e)?"xn--"+d(e):e})}var v="object"==typeof t&&t&&!t.nodeType&&t,y="object"==typeof r&&r&&!r.nodeType&&r,g="object"==typeof e&&e;g.global!==g&&g.window!==g&&g.self!==g||(a=g);var P,E,b=2147483647,w=36,j=1,S=26,_=38,x=700,F=72,$=128,R="-",O=/^xn--/,D=/[^\x20-\x7E]/,I=/[\x2E\u3002\uFF0E\uFF61]/g,A={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},k=w-j,q=Math.floor,C=String.fromCharCode;if(P={version:"1.4.1",ucs2:{decode:n,encode:l},decode:f,encode:d,toASCII:m,toUnicode:p},v&&y)if(r.exports==v)y.exports=P;else for(E in P)P.hasOwnProperty(E)&&(v[E]=P[E]);else a.punycode=P}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],48:[function(e,r,t){"use strict";function a(e,r){return Object.prototype.hasOwnProperty.call(e,r)}r.exports=function(e,r,t,o){r=r||"&",t=t||"=";var i={};if("string"!=typeof e||0===e.length)return i;var n=/\+/g;e=e.split(r);var l=1e3;o&&"number"==typeof o.maxKeys&&(l=o.maxKeys);var h=e.length;l>0&&h>l&&(h=l);for(var u=0;u<h;++u){var c,f,d,p,m=e[u].replace(n,"%20"),v=m.indexOf(t);v>=0?(c=m.substr(0,v),f=m.substr(v+1)):(c=m,f=""),d=decodeURIComponent(c),p=decodeURIComponent(f),a(i,d)?s(i[d])?i[d].push(p):i[d]=[i[d],p]:i[d]=p}return i};var s=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{}],49:[function(e,r,t){"use strict";function a(e,r){if(e.map)return e.map(r);for(var t=[],a=0;a<e.length;a++)t.push(r(e[a],a));return t}var s=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};r.exports=function(e,r,t,n){return r=r||"&",t=t||"=",null===e&&(e=void 0),"object"==typeof e?a(i(e),function(i){var n=encodeURIComponent(s(i))+t;return o(e[i])?a(e[i],function(e){return n+encodeURIComponent(s(e))}).join(r):n+encodeURIComponent(s(e[i]))}).join(r):n?encodeURIComponent(s(n))+t+encodeURIComponent(s(e)):""};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=Object.keys||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.push(t);return r}},{}],50:[function(e,r,t){"use strict";t.decode=t.parse=e("./decode"),t.encode=t.stringify=e("./encode")},{"./decode":48,"./encode":49}],51:[function(e,r,t){"use strict";function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function s(e,r,t){if(e&&h.isObject(e)&&e instanceof a)return e;var s=new a;return s.parse(e,r,t),s}function o(e){return h.isString(e)&&(e=s(e)),e instanceof a?e.format():a.prototype.format.call(e)}function i(e,r){return s(e,!1,!0).resolve(r)}function n(e,r){return e?s(e,!1,!0).resolveObject(r):r}var l=e("punycode"),h=e("./util");t.parse=s,t.resolve=i,t.resolveObject=n,t.format=o,t.Url=a;var u=/^([a-z0-9.+-]+:)/i,c=/:[0-9]*$/,f=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,d=["<",">",'"',"`"," ","\r","\n","\t"],p=["{","}","|","\\","^","`"].concat(d),m=["'"].concat(p),v=["%","/","?",";","#"].concat(m),y=["/","?","#"],g=/^[+a-z0-9A-Z_-]{0,63}$/,P=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,E={javascript:!0,"javascript:":!0},b={javascript:!0,"javascript:":!0},w={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},j=e("querystring");a.prototype.parse=function(e,r,t){if(!h.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),s=-1!==a&&a<e.indexOf("#")?"?":"#",o=e.split(s),i=/\\/g;o[0]=o[0].replace(i,"/");var n=e=o.join(s);if(n=n.trim(),!t&&1===e.split("#").length){var c=f.exec(n);if(c)return this.path=n,this.href=n,this.pathname=c[1],c[2]?(this.search=c[2],this.query=r?j.parse(this.search.substr(1)):this.search.substr(1)):r&&(this.search="",this.query={}),this}var d=u.exec(n);if(d){var p=(d=d[0]).toLowerCase();this.protocol=p,n=n.substr(d.length)}if(t||d||n.match(/^\/\/[^@\/]+@[^@\/]+/)){var S="//"===n.substr(0,2);!S||d&&b[d]||(n=n.substr(2),this.slashes=!0)}if(!b[d]&&(S||d&&!w[d])){for(var _=-1,x=0;x<y.length;x++)-1!==(R=n.indexOf(y[x]))&&(-1===_||R<_)&&(_=R);var F,$;-1!==($=-1===_?n.lastIndexOf("@"):n.lastIndexOf("@",_))&&(F=n.slice(0,$),n=n.slice($+1),this.auth=decodeURIComponent(F)),_=-1;for(x=0;x<v.length;x++){var R=n.indexOf(v[x]);-1!==R&&(-1===_||R<_)&&(_=R)}-1===_&&(_=n.length),this.host=n.slice(0,_),n=n.slice(_),this.parseHost();var O="["===(this.hostname=this.hostname||"")[0]&&"]"===this.hostname[this.hostname.length-1];if(!O)for(var D=this.hostname.split(/\./),x=0,I=D.length;x<I;x++){var A=D[x];if(A&&!A.match(g)){for(var k="",q=0,C=A.length;q<C;q++)A.charCodeAt(q)>127?k+="x":k+=A[q];if(!k.match(g)){var L=D.slice(0,x),z=D.slice(x+1),Q=A.match(P);Q&&(L.push(Q[1]),z.unshift(Q[2])),z.length&&(n="/"+z.join(".")+n),this.hostname=L.join(".");break}}}this.hostname=this.hostname.length>255?"":this.hostname.toLowerCase(),O||(this.hostname=l.toASCII(this.hostname));var U=this.port?":"+this.port:"",V=this.hostname||"";this.host=V+U,this.href+=this.host,O&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==n[0]&&(n="/"+n))}if(!E[p])for(var x=0,I=m.length;x<I;x++){var N=m[x];if(-1!==n.indexOf(N)){var T=encodeURIComponent(N);T===N&&(T=escape(N)),n=n.split(N).join(T)}}var M=n.indexOf("#");-1!==M&&(this.hash=n.substr(M),n=n.slice(0,M));var B=n.indexOf("?");if(-1!==B?(this.search=n.substr(B),this.query=n.substr(B+1),r&&(this.query=j.parse(this.query)),n=n.slice(0,B)):r&&(this.search="",this.query={}),n&&(this.pathname=n),w[p]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var U=this.pathname||"",H=this.search||"";this.path=U+H}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var r=this.protocol||"",t=this.pathname||"",a=this.hash||"",s=!1,o="";this.host?s=e+this.host:this.hostname&&(s=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(s+=":"+this.port)),this.query&&h.isObject(this.query)&&Object.keys(this.query).length&&(o=j.stringify(this.query));var i=this.search||o&&"?"+o||"";return r&&":"!==r.substr(-1)&&(r+=":"),this.slashes||(!r||w[r])&&!1!==s?(s="//"+(s||""),t&&"/"!==t.charAt(0)&&(t="/"+t)):s||(s=""),a&&"#"!==a.charAt(0)&&(a="#"+a),i&&"?"!==i.charAt(0)&&(i="?"+i),t=t.replace(/[?#]/g,function(e){return encodeURIComponent(e)}),i=i.replace("#","%23"),r+s+t+i+a},a.prototype.resolve=function(e){return this.resolveObject(s(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(h.isString(e)){var r=new a;r.parse(e,!1,!0),e=r}for(var t=new a,s=Object.keys(this),o=0;o<s.length;o++){var i=s[o];t[i]=this[i]}if(t.hash=e.hash,""===e.href)return t.href=t.format(),t;if(e.slashes&&!e.protocol){for(var n=Object.keys(e),l=0;l<n.length;l++){var u=n[l];"protocol"!==u&&(t[u]=e[u])}return w[t.protocol]&&t.hostname&&!t.pathname&&(t.path=t.pathname="/"),t.href=t.format(),t}if(e.protocol&&e.protocol!==t.protocol){if(!w[e.protocol]){for(var c=Object.keys(e),f=0;f<c.length;f++){var d=c[f];t[d]=e[d]}return t.href=t.format(),t}if(t.protocol=e.protocol,e.host||b[e.protocol])t.pathname=e.pathname;else{for(j=(e.pathname||"").split("/");j.length&&!(e.host=j.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==j[0]&&j.unshift(""),j.length<2&&j.unshift(""),t.pathname=j.join("/")}if(t.search=e.search,t.query=e.query,t.host=e.host||"",t.auth=e.auth,t.hostname=e.hostname||e.host,t.port=e.port,t.pathname||t.search){var p=t.pathname||"",m=t.search||"";t.path=p+m}return t.slashes=t.slashes||e.slashes,t.href=t.format(),t}var v=t.pathname&&"/"===t.pathname.charAt(0),y=e.host||e.pathname&&"/"===e.pathname.charAt(0),g=y||v||t.host&&e.pathname,P=g,E=t.pathname&&t.pathname.split("/")||[],j=e.pathname&&e.pathname.split("/")||[],S=t.protocol&&!w[t.protocol];if(S&&(t.hostname="",t.port=null,t.host&&(""===E[0]?E[0]=t.host:E.unshift(t.host)),t.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===j[0]?j[0]=e.host:j.unshift(e.host)),e.host=null),g=g&&(""===j[0]||""===E[0])),y)t.host=e.host||""===e.host?e.host:t.host,t.hostname=e.hostname||""===e.hostname?e.hostname:t.hostname,t.search=e.search,t.query=e.query,E=j;else if(j.length)E||(E=[]),E.pop(),E=E.concat(j),t.search=e.search,t.query=e.query;else if(!h.isNullOrUndefined(e.search))return S&&(t.hostname=t.host=E.shift(),(O=!!(t.host&&t.host.indexOf("@")>0)&&t.host.split("@"))&&(t.auth=O.shift(),t.host=t.hostname=O.shift())),t.search=e.search,t.query=e.query,h.isNull(t.pathname)&&h.isNull(t.search)||(t.path=(t.pathname?t.pathname:"")+(t.search?t.search:"")),t.href=t.format(),t;if(!E.length)return t.pathname=null,t.path=t.search?"/"+t.search:null,t.href=t.format(),t;for(var _=E.slice(-1)[0],x=(t.host||e.host||E.length>1)&&("."===_||".."===_)||""===_,F=0,$=E.length;$>=0;$--)"."===(_=E[$])?E.splice($,1):".."===_?(E.splice($,1),F++):F&&(E.splice($,1),F--);if(!g&&!P)for(;F--;F)E.unshift("..");!g||""===E[0]||E[0]&&"/"===E[0].charAt(0)||E.unshift(""),x&&"/"!==E.join("/").substr(-1)&&E.push("");var R=""===E[0]||E[0]&&"/"===E[0].charAt(0);if(S){t.hostname=t.host=R?"":E.length?E.shift():"";var O=!!(t.host&&t.host.indexOf("@")>0)&&t.host.split("@");O&&(t.auth=O.shift(),t.host=t.hostname=O.shift())}return(g=g||t.host&&E.length)&&!R&&E.unshift(""),E.length?t.pathname=E.join("/"):(t.pathname=null,t.path=null),h.isNull(t.pathname)&&h.isNull(t.search)||(t.path=(t.pathname?t.pathname:"")+(t.search?t.search:"")),t.auth=e.auth||t.auth,t.slashes=t.slashes||e.slashes,t.href=t.format(),t},a.prototype.parseHost=function(){var e=this.host,r=c.exec(e);r&&(":"!==(r=r[0])&&(this.port=r.substr(1)),e=e.substr(0,e.length-r.length)),e&&(this.hostname=e)}},{"./util":52,punycode:47,querystring:50}],52:[function(e,r,t){"use strict";r.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},{}],ajv:[function(e,r,t){"use strict";function a(e){if(!(this instanceof a))return new a(e);e=this._opts=L.copy(e)||{},this._schemas={},this._refs={},this._fragments={},this._formats=A(e.format);var r=this._schemaUriFormat=this._formats["uri-reference"];this._schemaUriFormatFunc=function(e){return r.test(e)},this._cache=e.cache||new O,this._loadingSchemas={},this._compilations=[],this.RULES=k(),this._getId=y(e),e.loopRequired=e.loopRequired||1/0,"property"==e.errorDataPath&&(e._errorDataPathProperty=!0),void 0===e.serialize&&(e.serialize=I),this._metaOpts=F(this),e.formats&&_(this),j(this),"object"==typeof e.meta&&this.addMetaSchema(e.meta),S(this),e.patternGroups&&C(this)}function s(e,r){var t;if("string"==typeof e){if(!(t=this.getSchema(e)))throw new Error('no schema with key or ref "'+e+'"')}else{var a=this._addSchema(e);t=a.validate||this._compile(a)}var s=t(r);return!0===t.$async?"*"==this._opts.async?z(s):s:(this.errors=t.errors,s)}function o(e,r){var t=this._addSchema(e,void 0,r);return t.validate||this._compile(t)}function i(e,r,t,a){if(Array.isArray(e))for(var s=0;s<e.length;s++)this.addSchema(e[s],void 0,t,a);else{var o=this._getId(e);if(void 0!==o&&"string"!=typeof o)throw new Error("schema id must be string");x(this,r=R.normalizeId(r||o)),this._schemas[r]=this._addSchema(e,t,a,!0)}}function n(e,r,t){this.addSchema(e,r,t,!0)}function l(e,r){var t=e.$schema;if(void 0!==t&&"string"!=typeof t)throw new Error("$schema must be a string");if(!(t=t||this._opts.defaultMeta||h(this)))return console.warn("meta-schema not available"),this.errors=null,!0;var a=this._formats.uri;this._formats.uri="function"==typeof a?this._schemaUriFormatFunc:this._schemaUriFormat;var s;try{s=this.validate(t,e)}finally{this._formats.uri=a}if(!s&&r){var o="schema is invalid: "+this.errorsText();if("log"!=this._opts.validateSchema)throw new Error(o);console.error(o)}return s}function h(e){var r=e._opts.meta;return e._opts.defaultMeta="object"==typeof r?e._getId(r)||r:e.getSchema(V)?V:void 0}function u(e){var r=f(this,e);switch(typeof r){case"object":return r.validate||this._compile(r);case"string":return this.getSchema(r);case"undefined":return c(this,e)}}function c(e,r){var t=R.schema.call(e,{schema:{}},r);if(t){var a=t.schema,s=t.root,o=t.baseId,i=$.call(e,a,s,void 0,o);return e._fragments[r]=new D({ref:r,fragment:!0,schema:a,root:s,baseId:o,validate:i}),i}}function f(e,r){return r=R.normalizeId(r),e._schemas[r]||e._refs[r]||e._fragments[r]}function d(e){if(e instanceof RegExp)return p(this,this._schemas,e),void p(this,this._refs,e);switch(typeof e){case"undefined":return p(this,this._schemas),p(this,this._refs),void this._cache.clear();case"string":var r=f(this,e);return r&&this._cache.del(r.cacheKey),delete this._schemas[e],void delete this._refs[e];case"object":var t=this._opts.serialize,a=t?t(e):e;this._cache.del(a);var s=this._getId(e);s&&(s=R.normalizeId(s),delete this._schemas[s],delete this._refs[s])}}function p(e,r,t){for(var a in r){var s=r[a];s.meta||t&&!t.test(a)||(e._cache.del(s.cacheKey),delete r[a])}}function m(e,r,t,a){if("object"!=typeof e&&"boolean"!=typeof e)throw new Error("schema should be object or boolean");var s=this._opts.serialize,o=s?s(e):e,i=this._cache.get(o);if(i)return i;a=a||!1!==this._opts.addUsedSchema;var n=R.normalizeId(this._getId(e));n&&a&&x(this,n);var l,h=!1!==this._opts.validateSchema&&!r;h&&!(l=n&&n==R.normalizeId(e.$schema))&&this.validateSchema(e,!0);var u=R.ids.call(this,e),c=new D({id:n,schema:e,localRefs:u,cacheKey:o,meta:t});return"#"!=n[0]&&a&&(this._refs[n]=c),this._cache.put(o,c),h&&l&&this.validateSchema(e,!0),c}function v(e,r){function t(){var r=e.validate,a=r.apply(null,arguments);return t.errors=r.errors,a}if(e.compiling)return e.validate=t,t.schema=e.schema,t.errors=null,t.root=r||t,!0===e.schema.$async&&(t.$async=!0),t;e.compiling=!0;var a;e.meta&&(a=this._opts,this._opts=this._metaOpts);var s;try{s=$.call(this,e.schema,r,e.localRefs)}finally{e.compiling=!1,e.meta&&(this._opts=a)}return e.validate=s,e.refs=s.refs,e.refVal=s.refVal,e.root=s.root,s}function y(e){switch(e.schemaId){case"$id":return P;case"id":return g;default:return E}}function g(e){return e.$id&&console.warn("schema $id ignored",e.$id),e.id}function P(e){return e.id&&console.warn("schema id ignored",e.id),e.$id}function E(e){if(e.$id&&e.id&&e.$id!=e.id)throw new Error("schema $id is different from id");return e.$id||e.id}function b(e,r){if(!(e=e||this.errors))return"No errors";for(var t=void 0===(r=r||{}).separator?", ":r.separator,a=void 0===r.dataVar?"data":r.dataVar,s="",o=0;o<e.length;o++){var i=e[o];i&&(s+=a+i.dataPath+" "+i.message+t)}return s.slice(0,-t.length)}function w(e,r){"string"==typeof r&&(r=new RegExp(r)),this._formats[e]=r}function j(r){var t;if(r._opts.$data&&(t=e("./refs/$data.json"),r.addMetaSchema(t,t.$id,!0)),!1!==r._opts.meta){var a=e("./refs/json-schema-draft-06.json");r._opts.$data&&(a=q(a,T)),r.addMetaSchema(a,V,!0),r._refs["http://json-schema.org/schema"]=V}}function S(e){var r=e._opts.schemas;if(r)if(Array.isArray(r))e.addSchema(r);else for(var t in r)e.addSchema(r[t],t)}function _(e){for(var r in e._opts.formats){var t=e._opts.formats[r];e.addFormat(r,t)}}function x(e,r){if(e._schemas[r]||e._refs[r])throw new Error('schema with key or id "'+r+'" already exists')}function F(e){for(var r=L.copy(e._opts),t=0;t<N.length;t++)delete r[N[t]];return r}var $=e("./compile"),R=e("./compile/resolve"),O=e("./cache"),D=e("./compile/schema_obj"),I=e("json-stable-stringify"),A=e("./compile/formats"),k=e("./compile/rules"),q=e("./$data"),C=e("./patternGroups"),L=e("./compile/util"),z=e("co");r.exports=a,a.prototype.validate=s,a.prototype.compile=o,a.prototype.addSchema=i,a.prototype.addMetaSchema=n,a.prototype.validateSchema=l,a.prototype.getSchema=u,a.prototype.removeSchema=d,a.prototype.addFormat=w,a.prototype.errorsText=b,a.prototype._addSchema=m,a.prototype._compile=v,a.prototype.compileAsync=e("./compile/async");var Q=e("./keyword");a.prototype.addKeyword=Q.add,a.prototype.getKeyword=Q.get,a.prototype.removeKeyword=Q.remove;var U=e("./compile/error_classes");a.ValidationError=U.Validation,a.MissingRefError=U.MissingRef,a.$dataMetaSchema=q;var V="http://json-schema.org/draft-06/schema",N=["removeAdditional","useDefaults","coerceTypes"],T=["/properties"]},{"./$data":1,"./cache":2,"./compile":7,"./compile/async":4,"./compile/error_classes":5,"./compile/formats":6,"./compile/resolve":8,"./compile/rules":9,"./compile/schema_obj":10,"./compile/util":12,"./keyword":36,"./patternGroups":37,"./refs/$data.json":38,"./refs/json-schema-draft-06.json":39,co:40,"json-stable-stringify":43}]},{},[])("ajv")});
//# sourceMappingURL=ajv.min.js.map