{"version": 3, "file": "dynamic-path-parser.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["utilities/dynamic-path-parser.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,mCAAmC;AACnC,+BAA+B;AAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAStD,2BAAkC,OAA2B;IAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;IACzC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IACzC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IAE5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IAEzD,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/C,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IAED,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,kCAAkC;QAClC,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAChD,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,QAAgB,EAAE,IAAY;YAC1D,yBAAyB;YACzB,iBAAiB;YACjB,IAAI;YAEJ,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;YACjD,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC;YACrB,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnC,MAAM,CAAC,QAAQ,CAAC;YAClB,CAAC;YAED,6CAA6C;YAC7C,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC9D,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YACnC,CAAC;YACD,MAAM,CAAC,iBAAiB,CAAC;QAE3B,CAAC,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC1B,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,kBAAkB,OAAO,CAAC,UAAU,cAAc;YACpD,cAAc,OAAO,aAAa,CAAC;IACzC,CAAC;IAED,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAEzD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAE5C,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3C,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC;IAEnE,MAAM,mBAAM,UAAU,IAAE,OAAO,EAAE,SAAS,IAAG;AAC/C,CAAC;AA5DD,8CA4DC"}