{"version": 3, "file": "round-progress.component.js", "sourceRoot": "", "sources": ["../src/round-progress.component.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,sCASuB;AAEvB,mEAA8D;AAC9D,iEAA4D;AAC5D,6DAAwD;AAmDxD,IAAa,sBAAsB;IAGjC,gCACU,QAA8B,EAC9B,OAA0B,EAC1B,SAA8B,EAC9B,OAAe,EACf,SAAmB;QAJnB,aAAQ,GAAR,QAAQ,CAAsB;QAC9B,YAAO,GAAP,OAAO,CAAmB;QAC1B,cAAS,GAAT,SAAS,CAAqB;QAC9B,YAAO,GAAP,OAAO,CAAQ;QACf,cAAS,GAAT,SAAS,CAAU;QAPrB,qBAAgB,GAAW,CAAC,CAAC;QAsH5B,WAAM,GAAqB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,cAAS,GAAkB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC3D,mBAAc,GAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAChE,aAAQ,GAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC1D,WAAM,GAAqB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,UAAK,GAAsB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,eAAU,GAAiB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC5D,eAAU,GAAkB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC7D,cAAS,GAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC5D,eAAU,GAAkB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC7D,YAAO,GAAqB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzD,aAAQ,GAAgC,IAAI,mBAAY,EAAE,CAAC;IAzHlE,CAAC;IAEJ,8CAA8C;IACtC,+CAAc,GAAtB,UAAuB,IAAY,EAAE,EAAU;QAA/C,iBAqCC;QApCC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,CAAC;QACX,CAAC;QAED,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC;QAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,kEAAkE;QAClE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC7B,IAAI,KAAK,GAAG;gBACV,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC/C,IAAM,EAAE,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC;gBAEnC,qBAAqB,CAAC;oBACpB,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;oBAC/E,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;oBAErF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAE1B,EAAE,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,IAAI,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;wBAC3D,qBAAqB,CAAC,SAAS,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,EAAE,CAAC,CAAC,KAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5B,UAAU,CAAC,KAAK,EAAE,KAAI,CAAC,cAAc,CAAC,CAAC;YACzC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gCAAgC;IACxB,yCAAQ,GAAhB,UAAiB,KAAa;QAC5B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EACxF,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED,gDAAgD;IACxC,uCAAM,GAAd,UAAe,KAAa;QAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,yDAAyD;IACzD,iDAAgB,GAAhB;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAE9B,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,SAAS;gBACnB,kBAAgB,QAAQ,kBAAe;gBACvC,gBAAa,QAAQ,GAAG,GAAG,GAAG,QAAQ,+BAA2B,CAAC;QACtE,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,6BAA2B,QAAQ,QAAK,CAAC;QAClD,CAAC;IACH,CAAC;IAED,4CAA4C;IAC5C,6CAAY,GAAZ,UAAa,KAAa;QACxB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,iCAAiC;IACjC,4CAAW,GAAX,UAAY,OAAO;QACjB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACnF,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAGD,sBAAI,6CAAS;QADb,8BAA8B;aAC9B;YACE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,CAAC;;;OAAA;IAGD,sBAAI,kDAAc;QADlB,6CAA6C;aAC7C;YACE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBACrB,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;YACjE,CAAC;QACH,CAAC;;;OAAA;IAGD,sBAAI,4CAAQ;QADZ,mCAAmC;aACnC;YACE,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9B,MAAM,CAAC,SAAO,QAAQ,UAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAE,CAAC;QACvE,CAAC;;;OAAA;IAGD,sBAAI,kDAAc;QADlB,8CAA8C;aAC9C;YACE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBACpB,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC;YAC1C,CAAC;QACH,CAAC;;;OAAA;IAiBH,6BAAC;AAAD,CAAC,AAnID,IAmIC;AAf4B;IAA1B,gBAAS,CAAC,MAAM,CAAC;;qDAAe;AACxB;IAAR,YAAK,EAAE;;uDAA0B;AACzB;IAAR,YAAK,EAAE;;mDAA0B;AACzB;IAAR,YAAK,EAAE;;sDAAyD;AACxD;IAAR,YAAK,EAAE;;yDAA4D;AAC3D;IAAR,YAAK,EAAE;;8DAAiE;AAChE;IAAR,YAAK,EAAE;;wDAA2D;AAC1D;IAAR,YAAK,EAAE;;sDAAyD;AACxD;IAAR,YAAK,EAAE;;qDAAwD;AACvD;IAAR,YAAK,EAAE;;0DAA6D;AAC5D;IAAR,YAAK,EAAE;;0DAA8D;AAC7D;IAAR,YAAK,EAAE;;yDAA6D;AAC5D;IAAR,YAAK,EAAE;;0DAA8D;AAC7D;IAAR,YAAK,EAAE;;uDAA2D;AACzD;IAAT,aAAM,EAAE;8BAAkB,mBAAY;wDAA8B;AAlI1D,sBAAsB;IAjDlC,gBAAS,CAAC;QACT,QAAQ,EAAE,gBAAgB;QAC1B,QAAQ,EAAE,skBAkBT;QACD,IAAI,EAAE;YACJ,MAAM,EAAE,aAAa;YACrB,sBAAsB,EAAE,SAAS;YACjC,sBAAsB,EAAE,KAAK;YAC7B,eAAe,EAAE,oCAAoC;YACrD,gBAAgB,EAAE,gBAAgB;YAClC,wBAAwB,EAAE,gBAAgB;YAC1C,oBAAoB,EAAE,YAAY;SACnC;QACD,MAAM,EAAE;YACN,2FAIE;YACF,4EAGE;YACF,oIAME;SACH;KACF,CAAC;qCAKoB,6CAAoB;QACrB,uCAAiB;QACf,2CAAmB;QACrB,aAAM;QACJ,eAAQ;GARlB,sBAAsB,CAmIlC;AAnIY,wDAAsB"}