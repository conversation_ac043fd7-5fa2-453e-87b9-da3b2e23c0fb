{"_args": [["bcrypt-pbkdf@1.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "bcrypt-pbkdf@1.0.1", "_id": "bcrypt-pbkdf@1.0.1", "_inBundle": false, "_integrity": "sha1-Y7xdy2EzG5K8Bf1SiVPDNGKgb40=", "_location": "/bcrypt-pbkdf", "_optional": true, "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bcrypt-pbkdf@1.0.1", "name": "bcrypt-pbkdf", "escapedName": "bcrypt-pbkdf", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/sshpk"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "dependencies": {"tweetnacl": "^0.14.3"}, "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "devDependencies": {}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "name": "bcrypt-pbkdf", "version": "1.0.1"}