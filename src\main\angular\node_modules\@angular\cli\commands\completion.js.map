{"version": 3, "file": "completion.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/completion.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,6BAA6B;AAE7B,6CAA0C;AAE1C,MAAM,WAAW,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACtD,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,MAAM,aAAa,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAErE,wBAAwB,IAAS;IAC/B,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,2BAA2B,IAAS;IAClC,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAQD,MAAM,MAAM,GAAa,EAAE,CAAC;AAE5B,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,mEAAmE;IAChF,KAAK,EAAE,YAAY;IACnB,gBAAgB,EAAE;QAChB;YACE,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,iEAAiE;SAC/E;QACD;YACE,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,wCAAwC;SACtD;QACD;YACE,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,uCAAuC;SACrD;KACF;IAED,GAAG,EAAE,UAAU,cAAwC;QACrD,cAAc,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAEjE,MAAM,YAAY,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC;aAC3C,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;aACjE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;aAClC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEnC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,IAAY;YAC5D,IAAI,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,aAAa,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC;YAEjD,GAAG,CAAC,cAAc,CAAC,GAAG,aAAa,CAAC;YAEpC,MAAM,CAAC,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,YAAY,CAAC,OAAO,CAAC,GAAG;YACtB,MAAM,OAAO,GAAG,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC/C,MAAM,GAAG,GAAa,EAAE,CAAC;YAEzB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC;gBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;YAEH,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC;YACT,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAe;oBACtC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACrB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,IAAI,GAAa,EAAE,CAAC;YACxB,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5D,CAAC;YAED,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC7D,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtC,SAAS,GAAG,GAAG,SAAS;eACjB,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,OAAO,MAAM,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,SAAS,GAAG,kBAAkB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,SAAS;2BAC9C,CAAC;QAExB,OAAO,CAAC,GAAG,CAAC,yBAAW,CAAA;;;;;;;;;;;;;QAanB,CAAC,CAAC;QAEN,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAC;QACvF,CAAC;QAED,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,yBAAW,CAAA;;;;;;;;;eASd,SAAS;;;;;;;;;UASd,CAAC,CAAC;QACR,CAAC;QAED,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,yBAAW,CAAA;;SAEpB,CAAC,CAAC;QACP,CAAC;QAED,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,yBAAW,CAAA;;;;;;;;gBAQb,SAAS;;;;;;;;;WASd,CAAC,CAAC;QACT,CAAC;QAED,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,yBAAW,CAAA;;;;WAIlB,CAAC,CAAC;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAE1C,CAAC;CACF,CAAC,CAAC;AAEH,kBAAe,iBAAiB,CAAC"}