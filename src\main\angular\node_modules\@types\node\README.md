# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for Node.js (http://nodejs.org/).

# Details
Files were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v6

Additional Details
 * Last updated: Thu, 29 Jun 2017 13:32:10 GMT
 * Dependencies: events, net, stream, child_process, tls, http, readline, crypto
 * Global values: Buffer, NodeJS, SlowBuffer, ___dirname, ___filename, clearImmediate, clearInterval, clearTimeout, console, exports, global, module, process, require, setImmediate, setInterval, setTimeout

# Credits
These definitions were written by Microsoft TypeScript <http://typescriptlang.org>, DefinitelyTyped <https://github.com/DefinitelyTyped/DefinitelyTyped>, <PERSON><PERSON><PERSON> <https://github.com/WilcoBakker>.
