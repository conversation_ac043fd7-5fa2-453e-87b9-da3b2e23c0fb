[{"__symbolic": "module", "version": 3, "metadata": {"TEMPLATE_TRANSFORMS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["TemplateTransforms"]}, "TemplateParseError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseErrorLevel"}]}]}}, "TemplateParseResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_ast", "name": "TemplateAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompilePipeSummary"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "TemplateParser": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "TEMPLATE_TRANSFORMS"}]}]], "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "../compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "../expression_parser/parser", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "module": "../i18n/i18n_html_parser", "name": "I18NHtmlParser"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_ast", "name": "TemplateAstVisitor"}]}]}], "parse": [{"__symbolic": "method"}], "tryParse": [{"__symbolic": "method"}], "tryParseHtml": [{"__symbolic": "method"}], "expandHtml": [{"__symbolic": "method"}], "getInterpolationConfig": [{"__symbolic": "method"}], "_assertNoReferenceDuplicationOnTemplate": [{"__symbolic": "method"}]}}, "splitClasses": {"__symbolic": "function", "parameters": ["classAttrValue"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 805, "character": 37}}, "createElementCssSelector": {"__symbolic": "function"}, "removeSummaryDuplicates": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"TEMPLATE_TRANSFORMS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["TemplateTransforms"]}, "TemplateParseError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseErrorLevel"}]}]}}, "TemplateParseResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_ast", "name": "TemplateAst"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompilePipeSummary"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "TemplateParser": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "TEMPLATE_TRANSFORMS"}]}]], "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "../compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "../expression_parser/parser", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "module": "../i18n/i18n_html_parser", "name": "I18NHtmlParser"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./template_ast", "name": "TemplateAstVisitor"}]}]}], "parse": [{"__symbolic": "method"}], "tryParse": [{"__symbolic": "method"}], "tryParseHtml": [{"__symbolic": "method"}], "expandHtml": [{"__symbolic": "method"}], "getInterpolationConfig": [{"__symbolic": "method"}], "_assertNoReferenceDuplicationOnTemplate": [{"__symbolic": "method"}]}}, "splitClasses": {"__symbolic": "function", "parameters": ["classAttrValue"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 805, "character": 37}}, "createElementCssSelector": {"__symbolic": "function"}, "removeSummaryDuplicates": {"__symbolic": "function"}}}]