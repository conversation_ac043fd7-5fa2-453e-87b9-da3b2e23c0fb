[{"__symbolic": "module", "version": 3, "metadata": {"MissingTranslationHandlerParams": {"__symbolic": "interface"}, "MissingTranslationHandler": {"__symbolic": "class", "members": {"handle": [{"__symbolic": "method"}]}}, "FakeMissingTranslationHandler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"handle": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MissingTranslationHandlerParams": {"__symbolic": "interface"}, "MissingTranslationHandler": {"__symbolic": "class", "members": {"handle": [{"__symbolic": "method"}]}}, "FakeMissingTranslationHandler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"handle": [{"__symbolic": "method"}]}}}}]