{"version": 3, "file": "platform-browser-dynamic-testing.umd.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../@angular/platform-browser-dynamic/testing.es5.js"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "import * as tslib_1 from \"tslib\";\n/**\n * @license Angular v4.2.5\n * (c) 2010-2017 Google, Inc. https://angular.io/\n * License: MIT\n */\nimport { platformCoreDynamicTesting } from '@angular/compiler/testing';\nimport { Inject, Injectable, NgModule, createPlatformFactory } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS } from '@angular/platform-browser-dynamic';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport { DOCUMENT, ɵgetDOM } from '@angular/platform-browser';\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nvar DOMTestComponentRenderer = (function (_super) {\n    tslib_1.__extends(DOMTestComponentRenderer, _super);\n    function DOMTestComponentRenderer(_doc /** TODO #9100 */) {\n        var _this = _super.call(this) || this;\n        _this._doc = _doc; /** TODO #9100 */\n        return _this;\n    }\n    DOMTestComponentRenderer.prototype.insertRootElement = function (rootElId) {\n        var rootEl = ɵgetDOM().firstChild(ɵgetDOM().content(ɵgetDOM().createTemplate(\"<div id=\\\"\" + rootElId + \"\\\"></div>\")));\n        // TODO(juliemr): can/should this be optional?\n        var oldRoots = ɵgetDOM().querySelectorAll(this._doc, '[id^=root]');\n        for (var i = 0; i < oldRoots.length; i++) {\n            ɵgetDOM().remove(oldRoots[i]);\n        }\n        ɵgetDOM().appendChild(this._doc.body, rootEl);\n    };\n    return DOMTestComponentRenderer;\n}(TestComponentRenderer));\nDOMTestComponentRenderer.decorators = [\n    { type: Injectable },\n];\n/** @nocollapse */\nDOMTestComponentRenderer.ctorParameters = function () { return [\n    { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] },] },\n]; };\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @stable\n */\nvar platformBrowserDynamicTesting = createPlatformFactory(platformCoreDynamicTesting, 'browserDynamicTesting', ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @stable\n */\nvar BrowserDynamicTestingModule = (function () {\n    function BrowserDynamicTestingModule() {\n    }\n    return BrowserDynamicTestingModule;\n}());\nBrowserDynamicTestingModule.decorators = [\n    { type: NgModule, args: [{\n                exports: [BrowserTestingModule],\n                providers: [\n                    { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n                ]\n            },] },\n];\n/** @nocollapse */\nBrowserDynamicTestingModule.ctorParameters = function () { return []; };\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic/testing package.\n */\nexport { platformBrowserDynamicTesting, BrowserDynamicTestingModule, DOMTestComponentRenderer as ɵDOMTestComponentRenderer };\n//# sourceMappingURL=testing.es5.js.map\n"], "names": ["tslib_1.__extends", "ɵgetDOM", "TestComponent<PERSON><PERSON><PERSON>", "Injectable", "Inject", "DOCUMENT", "createPlatformFactory", "platformCoreDynamicTesting", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "NgModule", "BrowserTestingModule"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;AC3JP;;;;;AAKA,AACA,AACA,AACA,AACA,AACA,AACA;;;;;;;;;;AAUA,IAAI,wBAAwB,IAAI,UAAU,MAAM,EAAE;IAC9CA,SAAiB,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;IACpD,SAAS,wBAAwB,CAAC,IAAI,oBAAoB;QACtD,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,KAAK,CAAC;KAChB;IACD,wBAAwB,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,QAAQ,EAAE;QACvE,IAAI,MAAM,GAAGC,gCAAO,EAAE,CAAC,UAAU,CAACA,gCAAO,EAAE,CAAC,OAAO,CAACA,gCAAO,EAAE,CAAC,cAAc,CAAC,YAAY,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;;QAEtH,IAAI,QAAQ,GAAGA,gCAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtCA,gCAAO,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACjC;QACDA,gCAAO,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACjD,CAAC;IACF,OAAO,wBAAwB,CAAC;CACnC,CAACC,2CAAqB,CAAC,CAAC,CAAC;AAC1B,wBAAwB,CAAC,UAAU,GAAG;IAClC,EAAE,IAAI,EAAEC,wBAAU,EAAE;CACvB,CAAC;;AAEF,wBAAwB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;IAC3D,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAEC,oBAAM,EAAE,IAAI,EAAE,CAACC,iCAAQ,EAAE,EAAE,EAAE,EAAE;CAC1E,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;;;;AAkBL,IAAI,6BAA6B,GAAGC,mCAAqB,CAACC,oDAA0B,EAAE,uBAAuB,EAAEC,4EAA4C,CAAC,CAAC;;;;;;AAM7J,IAAI,2BAA2B,IAAI,YAAY;IAC3C,SAAS,2BAA2B,GAAG;KACtC;IACD,OAAO,2BAA2B,CAAC;CACtC,EAAE,CAAC,CAAC;AACL,2BAA2B,CAAC,UAAU,GAAG;IACrC,EAAE,IAAI,EAAEC,sBAAQ,EAAE,IAAI,EAAE,CAAC;gBACb,OAAO,EAAE,CAACC,qDAAoB,CAAC;gBAC/B,SAAS,EAAE;oBACP,EAAE,OAAO,EAAER,2CAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAE;iBACzE;aACJ,EAAE,EAAE;CAChB,CAAC;;AAEF,2BAA2B,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,AACxE,AAY6H,AAC7H,AAAuC,;;;;,;;,;;"}