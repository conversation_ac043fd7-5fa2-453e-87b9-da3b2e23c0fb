{"_args": [["cli-width@2.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cli-width@2.1.0", "_id": "cli-width@2.1.0", "_inBundle": false, "_integrity": "sha1-sjTKIJsp72b8UY2bmNWEewDt8Ao=", "_location": "/cli-width", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cli-width@2.1.0", "name": "cli-width", "escapedName": "cli-width", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/inquirer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cli-width/-/cli-width-2.1.0.tgz", "_spec": "2.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/knownasilya/cli-width/issues"}, "description": "Get stdout window width, with two fallbacks, tty and then a default.", "devDependencies": {"coveralls": "^2.11.4", "isparta": "^3.0.4", "rimraf": "^2.4.3", "tap-spec": "^4.1.0", "tape": "^3.4.0"}, "homepage": "https://github.com/knownasilya/cli-width", "license": "ISC", "main": "index.js", "name": "cli-width", "repository": {"type": "git", "url": "git+ssh://**************/knownasilya/cli-width.git"}, "scripts": {"coverage": "isparta cover test/*.js | tspec", "coveralls": "npm run coverage -s && coveralls < coverage/lcov.info", "postcoveralls": "rimraf ./coverage", "test": "node test | tspec"}, "version": "2.1.0"}