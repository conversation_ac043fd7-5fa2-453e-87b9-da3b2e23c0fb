{"version": 3, "file": "common.js", "sources": ["../../../../packages/common/index.ts", "../../../../packages/common/public_api.ts", "../../../../packages/common/src/common.ts", "../../../../packages/common/src/version.ts", "../../../../packages/common/src/platform_id.ts", "../../../../packages/common/src/common_module.ts", "../../../../packages/common/src/pipes/index.ts", "../../../../packages/common/src/pipes/slice_pipe.ts", "../../../../packages/common/src/pipes/json_pipe.ts", "../../../../packages/common/src/pipes/i18n_select_pipe.ts", "../../../../packages/common/src/pipes/i18n_plural_pipe.ts", "../../../../packages/common/src/pipes/date_pipe.ts", "../../../../packages/common/src/pipes/number_pipe.ts", "../../../../packages/common/src/pipes/intl.ts", "../../../../packages/common/src/pipes/case_conversion_pipes.ts", "../../../../packages/common/src/pipes/async_pipe.ts", "../../../../packages/common/src/pipes/invalid_pipe_argument_error.ts", "../../../../packages/common/src/directives/index.ts", "../../../../packages/common/src/directives/ng_template_outlet.ts", "../../../../packages/common/src/directives/ng_style.ts", "../../../../packages/common/src/directives/ng_plural.ts", "../../../../packages/common/src/directives/ng_switch.ts", "../../../../packages/common/src/directives/ng_if.ts", "../../../../packages/common/src/directives/ng_for_of.ts", "../../../../packages/common/src/directives/ng_component_outlet.ts", "../../../../packages/common/src/directives/ng_class.ts", "../../../../packages/common/src/localization.ts", "../../../../packages/common/src/location/index.ts", "../../../../packages/common/src/location/path_location_strategy.ts", "../../../../packages/common/src/location/hash_location_strategy.ts", "../../../../packages/common/src/location/location.ts", "../../../../packages/common/src/location/location_strategy.ts", "../../../../packages/common/src/location/platform_location.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {NgLocaleLocalization,NgLocalization,CommonModule,NgClass,NgFor,NgForOf,NgForOfContext,NgIf,NgIfContext,NgPlural,NgPluralCase,NgStyle,NgSwitch,NgSwitchCase,NgSwitchDefault,NgTemplateOutlet,NgComponentOutlet,AsyncPipe,DatePipe,I18nPluralPipe,I18nSelectPipe,JsonPipe,LowerCasePipe,CurrencyPipe,DecimalPipe,PercentPipe,SlicePipe,UpperCasePipe,TitleCasePipe,ɵPLATFORM_BROWSER_ID,ɵPLATFORM_SERVER_ID,ɵPLATFORM_WORKER_APP_ID,ɵPLATFORM_WORKER_UI_ID,isPlatformBrowser,isPlatformServer,isPlatformWorkerApp,isPlatformWorkerUi,VERSION,PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener,LocationStrategy,APP_BASE_HREF,HashLocationStrategy,PathLocationStrategy,PopStateEvent,Location} from './public_api';\n\nexport {COMMON_DIRECTIVES as ɵa} from './src/directives/index';\nexport {COMMON_PIPES as ɵb} from './src/pipes/index';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\nexport {NgLocaleLocalization,NgLocalization,CommonModule,NgClass,NgFor,NgForOf,NgForOfContext,NgIf,NgIfContext,NgPlural,NgPluralCase,NgStyle,NgSwitch,NgSwitchCase,NgSwitchDefault,NgTemplateOutlet,NgComponentOutlet,AsyncPipe,DatePipe,I18nPluralPipe,I18nSelectPipe,JsonPipe,LowerCasePipe,CurrencyPipe,DecimalPipe,PercentPipe,SlicePipe,UpperCasePipe,TitleCasePipe,ɵPLATFORM_BROWSER_ID,ɵPLATFORM_SERVER_ID,ɵPLATFORM_WORKER_APP_ID,ɵPLATFORM_WORKER_UI_ID,is<PERSON>lat<PERSON><PERSON><PERSON>er,isPlatformServer,isPlatformWorkerApp,isPlatformWorkerUi,VERSION,PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener,LocationStrategy,APP_BASE_HREF,HashLocationStrategy,PathLocationStrategy,PopStateEvent,Location} from './src/common';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\nexport {PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener,LocationStrategy,APP_BASE_HREF,HashLocationStrategy,PathLocationStrategy,PopStateEvent,Location} from './location/index';\nexport {NgLocaleLocalization, NgLocalization} from './localization';\nexport {CommonModule} from './common_module';\nexport {NgClass, NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NgComponentOutlet} from './directives/index';\nexport {AsyncPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, CurrencyPipe, DecimalPipe, PercentPipe, SlicePipe, UpperCasePipe, TitleCasePipe} from './pipes/index';\nexport {PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID, PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi} from './platform_id';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const PLATFORM_BROWSER_ID = 'browser';\nexport const /** @type {?} */ PLATFORM_SERVER_ID = 'server';\nexport const /** @type {?} */ PLATFORM_WORKER_APP_ID = 'browserWorkerApp';\nexport const /** @type {?} */ PLATFORM_WORKER_UI_ID = 'browserWorkerUi';\n/**\n * Returns whether a platform id represents a browser platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformBrowser(platformId: Object): boolean {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformServer(platformId: Object): boolean {\n  return platformId === PLATFORM_SERVER_ID;\n}\n/**\n * Returns whether a platform id represents a web worker app platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformWorkerApp(platformId: Object): boolean {\n  return platformId === PLATFORM_WORKER_APP_ID;\n}\n/**\n * Returns whether a platform id represents a web worker UI platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformWorkerUi(platformId: Object): boolean {\n  return platformId === PLATFORM_WORKER_UI_ID;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule} from '@angular/core';\n\nimport {COMMON_DEPRECATED_DIRECTIVES, COMMON_DIRECTIVES} from './directives/index';\nimport {NgLocaleLocalization, NgLocalization} from './localization';\nimport {COMMON_PIPES} from './pipes/index';\n/**\n * The module that includes all the basic Angular directives like {\\@link NgIf}, {\\@link NgForOf}, ...\n * \n * \\@stable\n */\nexport class CommonModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: [COMMON_DIRECTIVES, COMMON_PIPES],\n  exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n  providers: [\n    {provide: NgLocalization, useClass: NgLocaleLocalization},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction CommonModule_tsickle_Closure_declarations() {\n/** @type {?} */\nCommonModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCommonModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\n\nimport {AsyncPipe} from './async_pipe';\nimport {LowerCasePipe, TitleCasePipe, UpperCasePipe} from './case_conversion_pipes';\nimport {DatePipe} from './date_pipe';\nimport {I18nPluralPipe} from './i18n_plural_pipe';\nimport {I18nSelectPipe} from './i18n_select_pipe';\nimport {JsonPipe} from './json_pipe';\nimport {CurrencyPipe, DecimalPipe, PercentPipe} from './number_pipe';\nimport {SlicePipe} from './slice_pipe';\n\nexport {\n  AsyncPipe,\n  CurrencyPipe,\n  DatePipe,\n  DecimalPipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  LowerCasePipe,\n  PercentPipe,\n  SlicePipe,\n  TitleCasePipe,\n  UpperCasePipe\n};\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport const COMMON_PIPES = [\n  AsyncPipe,\n  UpperCasePipe,\n  LowerCasePipe,\n  JsonPipe,\n  SlicePipe,\n  DecimalPipe,\n  PercentPipe,\n  TitleCasePipe,\n  CurrencyPipe,\n  DatePipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a new List or String containing a subset (slice) of the elements.\n * \\@howToUse `array_or_string_expression | slice:start[:end]`\n * \\@description \n * \n * Where the input expression is a `List` or `String`, and:\n * - `start`: The starting index of the subset to return.\n *   - **a positive integer**: return the item at `start` index and all items after\n *     in the list or string expression.\n *   - **a negative integer**: return the item at `start` index from the end and all items after\n *     in the list or string expression.\n *   - **if positive and greater than the size of the expression**: return an empty list or string.\n *   - **if negative and greater than the size of the expression**: return entire list or string.\n * - `end`: The ending index of the subset to return.\n *   - **omitted**: return all items until the end.\n *   - **if positive**: return all items before `end` index of the list or string.\n *   - **if negative**: return all items before `end` index from the end of the list or string.\n * \n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n * \n * When operating on a [List], the returned list is always a copy even when all\n * the elements are being returned.\n * \n * When operating on a blank value, the pipe returns the blank value.\n * \n * ## List Example\n * \n * This `ngFor` example:\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n * \n * produces the following:\n * \n *     <li>b</li>\n *     <li>c</li>\n * \n * ## String Examples\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n * \n * \\@stable\n */\nexport class SlicePipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} start\n * @param {?=} end\n * @return {?}\n */\ntransform(value: any, start: number, end?: number): any {\n    if (value == null) return value;\n\n    if (!this.supports(value)) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n\n    return value.slice(start, end);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate supports(obj: any): boolean { return typeof obj === 'string' || Array.isArray(obj); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'slice', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SlicePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nSlicePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSlicePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Converts value into JSON string.\n * \\@howToUse `expression | json`\n * \\@description \n * \n * Converts value into string using `JSON.stringify`. Useful for debugging.\n * \n * ### Example\n * {\\@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n * \n * \\@stable\n */\nexport class JsonPipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: any): string { return JSON.stringify(value, null, 2); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'json', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction JsonPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonPipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Generic selector that displays the string that matches the current value.\n * \\@howToUse `expression | i18nSelect:mapping`\n * \\@description \n * \n *  Where `mapping` is an object that indicates the text that should be displayed\n *  for different values of the provided `expression`.\n *  If none of the keys of the mapping match the value of the `expression`, then the content\n *  of the `other` key is returned when present, otherwise an empty string is returned.\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n * \n *  \\@experimental\n */\nexport class I18nSelectPipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} mapping\n * @return {?}\n */\ntransform(value: string|null|undefined, mapping: {[key: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n\n    return '';\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nSelect', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction I18nSelectPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nSelectPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nSelectPipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {NgLocalization, getPluralCategory} from '../localization';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _INTERPOLATION_REGEXP: RegExp = /#/g;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Maps a value to a string that pluralizes the value according to locale rules.\n * \\@howToUse `expression | i18nPlural:mapping`\n * \\@description \n * \n *  Where:\n *  - `expression` is a number.\n *  - `mapping` is an object that mimics the ICU format, see\n *    http://userguide.icu-project.org/formatparse/messages\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n * \n * \\@experimental\n */\nexport class I18nPluralPipe implements PipeTransform {\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @param {?} pluralMap\n * @return {?}\n */\ntransform(value: number, pluralMap: {[count: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n\n    const /** @type {?} */ key = getPluralCategory(value, Object.keys(pluralMap), this._localization);\n\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nPlural', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\n}\n\nfunction I18nPluralPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nPluralPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nPluralPipe.ctorParameters;\n/** @type {?} */\nI18nPluralPipe.prototype._localization;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform} from '@angular/core';\nimport {DateFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\nimport {isNumeric} from './number_pipe';\n\nconst /** @type {?} */ ISO8601_DATE_REGEX =\n    /^(\\d{4})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a date according to locale rules.\n * \\@howToUse `date_expression | date[:format]`\n * \\@description \n * \n * Where:\n * - `expression` is a date object or a number (milliseconds since UTC epoch) or an ISO string\n * (https://www.w3.org/TR/NOTE-datetime).\n * - `format` indicates which date/time components to include. The format can be predefined as\n *   shown below or custom as shown in the table.\n *   - `'medium'`: equivalent to `'yMMMdjms'` (e.g. `Sep 3, 2010, 12:05:08 PM` for `en-US`)\n *   - `'short'`: equivalent to `'yMdjm'` (e.g. `9/3/2010, 12:05 PM` for `en-US`)\n *   - `'fullDate'`: equivalent to `'yMMMMEEEEd'` (e.g. `Friday, September 3, 2010` for `en-US`)\n *   - `'longDate'`: equivalent to `'yMMMMd'` (e.g. `September 3, 2010` for `en-US`)\n *   - `'mediumDate'`: equivalent to `'yMMMd'` (e.g. `Sep 3, 2010` for `en-US`)\n *   - `'shortDate'`: equivalent to `'yMd'` (e.g. `9/3/2010` for `en-US`)\n *   - `'mediumTime'`: equivalent to `'jms'` (e.g. `12:05:08 PM` for `en-US`)\n *   - `'shortTime'`: equivalent to `'jm'` (e.g. `12:05 PM` for `en-US`)\n * \n * \n *  | Component | Symbol | Narrow | Short Form   | Long Form         | Numeric   | 2-digit   |\n *  |-----------|:------:|--------|--------------|-------------------|-----------|-----------|\n *  | era       |   G    | G (A)  | GGG (AD)     | GGGG (Anno Domini)| -         | -         |\n *  | year      |   y    | -      | -            | -                 | y (2015)  | yy (15)   |\n *  | month     |   M    | L (S)  | MMM (Sep)    | MMMM (September)  | M (9)     | MM (09)   |\n *  | day       |   d    | -      | -            | -                 | d (3)     | dd (03)   |\n *  | weekday   |   E    | E (S)  | EEE (Sun)    | EEEE (Sunday)     | -         | -         |\n *  | hour      |   j    | -      | -            | -                 | j (13)    | jj (13)   |\n *  | hour12    |   h    | -      | -            | -                 | h (1 PM)  | hh (01 PM)|\n *  | hour24    |   H    | -      | -            | -                 | H (13)    | HH (13)   |\n *  | minute    |   m    | -      | -            | -                 | m (5)     | mm (05)   |\n *  | second    |   s    | -      | -            | -                 | s (9)     | ss (09)   |\n *  | timezone  |   z    | -      | -            | z (Pacific Standard Time)| -  | -         |\n *  | timezone  |   Z    | -      | Z (GMT-8:00) | -                 | -         | -         |\n *  | timezone  |   a    | -      | a (PM)       | -                 | -         | -         |\n * \n * In javascript, only the components specified will be respected (not the ordering,\n * punctuations, ...) and details of the formatting will be dependent on the locale.\n * \n * Timezone of the formatted text will be the local system timezone of the end-user's machine.\n * \n * When the expression is a ISO string without time (e.g. 2016-09-19) the time zone offset is not\n * applied and the formatted text will have the same day, month and year of the expression.\n * \n * WARNINGS:\n * - this pipe is marked as pure hence it will not be re-evaluated when the input is mutated.\n *   Instead users should treat the date as an immutable object and change the reference when the\n *   pipe needs to re-run (this is to avoid reformatting the date on every change detection run\n *   which would be an expensive operation).\n * - this pipe uses the Internationalization API. Therefore it is only reliable in Chrome and Opera\n *   browsers.\n * \n * ### Examples\n * \n * Assuming `dateObj` is (year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11)\n * in the _local_ time and locale is 'en-US':\n * \n * ```\n *     {{ dateObj | date }}               // output is 'Jun 15, 2015'\n *     {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n *     {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n *     {{ dateObj | date:'mmss' }}        // output is '43:11'\n * ```\n * \n * {\\@example common/pipes/ts/date_pipe.ts region='DatePipe'}\n * \n * \\@stable\n */\nexport class DatePipe implements PipeTransform {\n/**\n * \\@internal\n */\nstatic _ALIASES: {[key: string]: string} = {\n    'medium': 'yMMMdjms',\n    'short': 'yMdjm',\n    'fullDate': 'yMMMMEEEEd',\n    'longDate': 'yMMMMd',\n    'mediumDate': 'yMMMd',\n    'shortDate': 'yMd',\n    'mediumTime': 'jms',\n    'shortTime': 'jm'\n  };\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} pattern\n * @return {?}\n */\ntransform(value: any, pattern: string = 'mediumDate'): string|null {\n    let /** @type {?} */ date: Date;\n\n    if (isBlank(value) || value !== value) return null;\n\n    if (typeof value === 'string') {\n      value = value.trim();\n    }\n\n    if (isDate(value)) {\n      date = value;\n    } else if (isNumeric(value)) {\n      date = new Date(parseFloat(value));\n    } else if (typeof value === 'string' && /^(\\d{4}-\\d{1,2}-\\d{1,2})$/.test(value)) {\n/**\n * For ISO Strings without time the day, month and year must be extracted from the ISO String\n * before Date creation to avoid time offset and errors in the new Date.\n * If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n * date, some browsers (e.g. IE 9) will throw an invalid Date error\n * If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n * is applied\n * Note: ISO months are 0 for January, 1 for February, ...\n */\nconst [y, m, d] = value.split('-').map((val: string) => parseInt(val, 10));\n      date = new Date(y, m - 1, d);\n    } else {\n      date = new Date(value);\n    }\n\n    if (!isDate(date)) {\n      let /** @type {?} */ match: RegExpMatchArray|null;\n      if ((typeof value === 'string') && (match = value.match(ISO8601_DATE_REGEX))) {\n        date = isoStringToDate(match);\n      } else {\n        throw invalidPipeArgumentError(DatePipe, value);\n      }\n    }\n\n    return DateFormatter.format(date, this._locale, DatePipe._ALIASES[pattern] || pattern);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'date', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DatePipe_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nDatePipe._ALIASES;\n/** @type {?} */\nDatePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDatePipe.ctorParameters;\n/** @type {?} */\nDatePipe.prototype._locale;\n}\n\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isBlank(obj: any): boolean {\n  return obj == null || obj === '';\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isDate(obj: any): obj is Date {\n  return obj instanceof Date && !isNaN(obj.valueOf());\n}\n/**\n * @param {?} match\n * @return {?}\n */\nfunction isoStringToDate(match: RegExpMatchArray): Date {\n  const /** @type {?} */ date = new Date(0);\n  let /** @type {?} */ tzHour = 0;\n  let /** @type {?} */ tzMin = 0;\n  const /** @type {?} */ dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const /** @type {?} */ timeSetter = match[8] ? date.setUTCHours : date.setHours;\n\n  if (match[9]) {\n    tzHour = toInt(match[9] + match[10]);\n    tzMin = toInt(match[9] + match[11]);\n  }\n  dateSetter.call(date, toInt(match[1]), toInt(match[2]) - 1, toInt(match[3]));\n  const /** @type {?} */ h = toInt(match[4] || '0') - tzHour;\n  const /** @type {?} */ m = toInt(match[5] || '0') - tzMin;\n  const /** @type {?} */ s = toInt(match[6] || '0');\n  const /** @type {?} */ ms = Math.round(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n/**\n * @param {?} str\n * @return {?}\n */\nfunction toInt(str: string): number {\n  return parseInt(str, 10);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform, Type} from '@angular/core';\nimport {NumberFormatStyle, NumberFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\n/**\n * @param {?} pipe\n * @param {?} locale\n * @param {?} value\n * @param {?} style\n * @param {?=} digits\n * @param {?=} currency\n * @param {?=} currencyAsSymbol\n * @return {?}\n */\nfunction formatNumber(\n    pipe: Type<any>, locale: string, value: number | string, style: NumberFormatStyle,\n    digits?: string | null, currency: string | null = null,\n    currencyAsSymbol: boolean = false): string|null {\n  if (value == null) return null;\n\n  // Convert strings to numbers\n  value = typeof value === 'string' && isNumeric(value) ? +value : value;\n  if (typeof value !== 'number') {\n    throw invalidPipeArgumentError(pipe, value);\n  }\n\n  let /** @type {?} */ minInt: number|undefined = undefined;\n  let /** @type {?} */ minFraction: number|undefined = undefined;\n  let /** @type {?} */ maxFraction: number|undefined = undefined;\n  if (style !== NumberFormatStyle.Currency) {\n    // rely on Intl default for currency\n    minInt = 1;\n    minFraction = 0;\n    maxFraction = 3;\n  }\n\n  if (digits) {\n    const /** @type {?} */ parts = digits.match(_NUMBER_FORMAT_REGEXP);\n    if (parts === null) {\n      throw new Error(`${digits} is not a valid digit info for number pipes`);\n    }\n    if (parts[1] != null) {  // min integer digits\n      minInt = parseIntAutoRadix(parts[1]);\n    }\n    if (parts[3] != null) {  // min fraction digits\n      minFraction = parseIntAutoRadix(parts[3]);\n    }\n    if (parts[5] != null) {  // max fraction digits\n      maxFraction = parseIntAutoRadix(parts[5]);\n    }\n  }\n\n  return NumberFormatter.format( /** @type {?} */((value as number)), locale, style, {\n    minimumIntegerDigits: minInt,\n    minimumFractionDigits: minFraction,\n    maximumFractionDigits: maxFraction,\n    currency: currency,\n    currencyAsSymbol: currencyAsSymbol,\n  });\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number according to locale rules.\n * \\@howToUse `number_expression | number[:digitInfo]`\n * \n * Formats a number as text. Group sizing and separator and other locale-specific\n * configurations are based on the active locale.\n * \n * where `expression` is a number:\n *  - `digitInfo` is a `string` which has a following format: <br>\n *     <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>\n *   - `minIntegerDigits` is the minimum number of integer digits to use. Defaults to `1`.\n *   - `minFractionDigits` is the minimum number of digits after fraction. Defaults to `0`.\n *   - `maxFractionDigits` is the maximum number of digits after fraction. Defaults to `3`.\n * \n * For more information on the acceptable range for each of these numbers and other\n * details see your native internationalization library.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='NumberPipe'}\n * \n * \\@stable\n */\nexport class DecimalPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(DecimalPipe, this._locale, value, NumberFormatStyle.Decimal, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'number'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DecimalPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nDecimalPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDecimalPipe.ctorParameters;\n/** @type {?} */\nDecimalPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as a percentage according to locale rules.\n * \\@howToUse `number_expression | percent[:digitInfo]`\n * \n * \\@description \n * \n * Formats a number as percentage.\n * \n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='PercentPipe'}\n * \n * \\@stable\n */\nexport class PercentPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(PercentPipe, this._locale, value, NumberFormatStyle.Percent, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'percent'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction PercentPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nPercentPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPercentPipe.ctorParameters;\n/** @type {?} */\nPercentPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as currency using locale rules.\n * \\@howToUse `number_expression | currency[:currencyCode[:symbolDisplay[:digitInfo]]]`\n * \\@description \n * \n * Use `currency` to format a number as currency.\n * \n * - `currencyCode` is the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code, such\n *    as `USD` for the US dollar and `EUR` for the euro.\n * - `symbolDisplay` is a boolean indicating whether to use the currency symbol or code.\n *   - `true`: use symbol (e.g. `$`).\n *   - `false`(default): use code (e.g. `USD`).\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='CurrencyPipe'}\n * \n * \\@stable\n */\nexport class CurrencyPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} currencyCode\n * @param {?=} symbolDisplay\n * @param {?=} digits\n * @return {?}\n */\ntransform(\n      value: any, currencyCode: string = 'USD', symbolDisplay: boolean = false,\n      digits?: string): string|null {\n    return formatNumber(\n        CurrencyPipe, this._locale, value, NumberFormatStyle.Currency, digits, currencyCode,\n        symbolDisplay);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'currency'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction CurrencyPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nCurrencyPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCurrencyPipe.ctorParameters;\n/** @type {?} */\nCurrencyPipe.prototype._locale;\n}\n\n/**\n * @param {?} text\n * @return {?}\n */\nfunction parseIntAutoRadix(text: string): number {\n  const /** @type {?} */ result: number = parseInt(text);\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n  return result;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nexport function isNumeric(value: any): boolean {\n  return !isNaN(value - parseFloat(value));\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\nexport type NumberFormatStyle = number;\nexport let NumberFormatStyle: any = {};\nNumberFormatStyle.Decimal = 0;\nNumberFormatStyle.Percent = 1;\nNumberFormatStyle.Currency = 2;\nNumberFormatStyle[NumberFormatStyle.Decimal] = \"Decimal\";\nNumberFormatStyle[NumberFormatStyle.Percent] = \"Percent\";\nNumberFormatStyle[NumberFormatStyle.Currency] = \"Currency\";\n\nexport class NumberFormatter {\n/**\n * @param {?} num\n * @param {?} locale\n * @param {?} style\n * @param {?=} opts\n * @return {?}\n */\nstatic format(num: number, locale: string, style: NumberFormatStyle, opts: {\n    minimumIntegerDigits?: number,\n    minimumFractionDigits?: number,\n    maximumFractionDigits?: number,\n    currency?: string|null,\n    currencyAsSymbol?: boolean\n  } = {}): string {\n    const {minimumIntegerDigits, minimumFractionDigits, maximumFractionDigits, currency,\n           currencyAsSymbol = false} = opts;\n    const /** @type {?} */ options: Intl.NumberFormatOptions = {\n      minimumIntegerDigits,\n      minimumFractionDigits,\n      maximumFractionDigits,\n      style: NumberFormatStyle[style].toLowerCase()\n    };\n\n    if (style == NumberFormatStyle.Currency) {\n      options.currency = typeof currency == 'string' ? currency : undefined;\n      options.currencyDisplay = currencyAsSymbol ? 'symbol' : 'code';\n    }\n    return new Intl.NumberFormat(locale, options).format(num);\n  }\n}\n\ntype DateFormatterFn = (date: Date, locale: string) => string;\n\nconst /** @type {?} */ DATE_FORMATS_SPLIT =\n    /((?:[^yMLdHhmsazZEwGjJ']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|J+|j+|m+|s+|a|z|Z|G+|w+))(.*)/;\n\nconst /** @type {?} */ PATTERN_ALIASES: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed during closure compilation.\n  'yMMMdjms': datePartGetterFactory(combine([\n    digitCondition('year', 1),\n    nameCondition('month', 3),\n    digitCondition('day', 1),\n    digitCondition('hour', 1),\n    digitCondition('minute', 1),\n    digitCondition('second', 1),\n  ])),\n  'yMdjm': datePartGetterFactory(combine([\n    digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1),\n    digitCondition('hour', 1), digitCondition('minute', 1)\n  ])),\n  'yMMMMEEEEd': datePartGetterFactory(combine([\n    digitCondition('year', 1), nameCondition('month', 4), nameCondition('weekday', 4),\n    digitCondition('day', 1)\n  ])),\n  'yMMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 4), digitCondition('day', 1)])),\n  'yMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 3), digitCondition('day', 1)])),\n  'yMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1)])),\n  'jms': datePartGetterFactory(combine(\n      [digitCondition('hour', 1), digitCondition('second', 1), digitCondition('minute', 1)])),\n  'jm': datePartGetterFactory(combine([digitCondition('hour', 1), digitCondition('minute', 1)]))\n};\n\nconst /** @type {?} */ DATE_FORMATS: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed.\n  'yyyy': datePartGetterFactory(digitCondition('year', 4)),\n  'yy': datePartGetterFactory(digitCondition('year', 2)),\n  'y': datePartGetterFactory(digitCondition('year', 1)),\n  'MMMM': datePartGetterFactory(nameCondition('month', 4)),\n  'MMM': datePartGetterFactory(nameCondition('month', 3)),\n  'MM': datePartGetterFactory(digitCondition('month', 2)),\n  'M': datePartGetterFactory(digitCondition('month', 1)),\n  'LLLL': datePartGetterFactory(nameCondition('month', 4)),\n  'L': datePartGetterFactory(nameCondition('month', 1)),\n  'dd': datePartGetterFactory(digitCondition('day', 2)),\n  'd': datePartGetterFactory(digitCondition('day', 1)),\n  'HH': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), false)))),\n  'H': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), false))),\n  'hh': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), true)))),\n  'h': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'jj': datePartGetterFactory(digitCondition('hour', 2)),\n  'j': datePartGetterFactory(digitCondition('hour', 1)),\n  'mm': digitModifier(datePartGetterFactory(digitCondition('minute', 2))),\n  'm': datePartGetterFactory(digitCondition('minute', 1)),\n  'ss': digitModifier(datePartGetterFactory(digitCondition('second', 2))),\n  's': datePartGetterFactory(digitCondition('second', 1)),\n  // while ISO 8601 requires fractions to be prefixed with `.` or `,`\n  // we can be just safely rely on using `sss` since we currently don't support single or two digit\n  // fractions\n  'sss': datePartGetterFactory(digitCondition('second', 3)),\n  'EEEE': datePartGetterFactory(nameCondition('weekday', 4)),\n  'EEE': datePartGetterFactory(nameCondition('weekday', 3)),\n  'EE': datePartGetterFactory(nameCondition('weekday', 2)),\n  'E': datePartGetterFactory(nameCondition('weekday', 1)),\n  'a': hourClockExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'Z': timeZoneGetter('short'),\n  'z': timeZoneGetter('long'),\n  'ww': datePartGetterFactory({}),  // Week of year, padded (00-53). Week 01 is the week with the\n                                    // first Thursday of the year. not support ?\n  'w':\n      datePartGetterFactory({}),  // Week of year (0-53). Week 1 is the week with the first Thursday\n                                  // of the year not support ?\n  'G': datePartGetterFactory(nameCondition('era', 1)),\n  'GG': datePartGetterFactory(nameCondition('era', 2)),\n  'GGG': datePartGetterFactory(nameCondition('era', 3)),\n  'GGGG': datePartGetterFactory(nameCondition('era', 4))\n};\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction digitModifier(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = inner(date, locale);\n    return result.length == 1 ? '0' + result : result;\n  };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourClockExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[1]; };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[0]; };\n}\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} options\n * @return {?}\n */\nfunction intlDateFormat(date: Date, locale: string, options: Intl.DateTimeFormatOptions): string {\n  return new Intl.DateTimeFormat(locale, options).format(date).replace(/[\\u200e\\u200f]/g, '');\n}\n/**\n * @param {?} timezone\n * @return {?}\n */\nfunction timeZoneGetter(timezone: string): DateFormatterFn {\n  // To workaround `Intl` API restriction for single timezone let format with 24 hours\n  const /** @type {?} */ options = {hour: '2-digit', hour12: false, timeZoneName: timezone};\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = intlDateFormat(date, locale, options);\n    // Then extract first 3 letters that related to hours\n    return result ? result.substring(3) : '';\n  };\n}\n/**\n * @param {?} options\n * @param {?} value\n * @return {?}\n */\nfunction hour12Modify(\n    options: Intl.DateTimeFormatOptions, value: boolean): Intl.DateTimeFormatOptions {\n  options.hour12 = value;\n  return options;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction digitCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  result[prop] = len === 2 ? '2-digit' : 'numeric';\n  return result;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction nameCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  if (len < 4) {\n    result[prop] = len > 1 ? 'short' : 'narrow';\n  } else {\n    result[prop] = 'long';\n  }\n\n  return result;\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction combine(options: Intl.DateTimeFormatOptions[]): Intl.DateTimeFormatOptions {\n  return ( /** @type {?} */((<any>Object))).assign({}, ...options);\n}\n/**\n * @param {?} ret\n * @return {?}\n */\nfunction datePartGetterFactory(ret: Intl.DateTimeFormatOptions): DateFormatterFn {\n  return (date: Date, locale: string): string => intlDateFormat(date, locale, ret);\n}\n\nconst /** @type {?} */ DATE_FORMATTER_CACHE = new Map<string, string[]>();\n/**\n * @param {?} format\n * @param {?} date\n * @param {?} locale\n * @return {?}\n */\nfunction dateFormatter(format: string, date: Date, locale: string): string {\n  const /** @type {?} */ fn = PATTERN_ALIASES[format];\n\n  if (fn) return fn(date, locale);\n\n  const /** @type {?} */ cacheKey = format;\n  let /** @type {?} */ parts = DATE_FORMATTER_CACHE.get(cacheKey);\n\n  if (!parts) {\n    parts = [];\n    let /** @type {?} */ match: RegExpExecArray|null;\n    DATE_FORMATS_SPLIT.exec(format);\n\n    let /** @type {?} */ _format: string|null = format;\n    while (_format) {\n      match = DATE_FORMATS_SPLIT.exec(_format);\n      if (match) {\n        parts = parts.concat(match.slice(1));\n        _format = /** @type {?} */(( parts.pop()));\n      } else {\n        parts.push(_format);\n        _format = null;\n      }\n    }\n\n    DATE_FORMATTER_CACHE.set(cacheKey, parts);\n  }\n\n  return parts.reduce((text, part) => {\n    const /** @type {?} */ fn = DATE_FORMATS[part];\n    return text + (fn ? fn(date, locale) : partToTime(part));\n  }, '');\n}\n/**\n * @param {?} part\n * @return {?}\n */\nfunction partToTime(part: string): string {\n  return part === '\\'\\'' ? '\\'' : part.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n}\nexport class DateFormatter {\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} pattern\n * @return {?}\n */\nstatic format(date: Date, locale: string, pattern: string): string {\n    return dateFormatter(pattern, date, locale);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * Transforms text to lowercase.\n * \n * {\\@example  common/pipes/ts/lowerupper_pipe.ts region='LowerUpperPipe' }\n * \n * \\@stable\n */\nexport class LowerCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'lowercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction LowerCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nLowerCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLowerCasePipe.ctorParameters;\n}\n\n/**\n * Helper method to transform a single word to titlecase.\n * \n * \\@stable\n * @param {?} word\n * @return {?}\n */\nfunction titleCaseWord(word: string) {\n  if (!word) return word;\n  return word[0].toUpperCase() + word.substr(1).toLowerCase();\n}\n/**\n * Transforms text to titlecase.\n * \n * \\@stable\n */\nexport class TitleCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n\n    return value.split(/\\b/g).map(word => titleCaseWord(word)).join('');\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'titlecase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction TitleCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nTitleCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitleCasePipe.ctorParameters;\n}\n\n/**\n * Transforms text to uppercase.\n * \n * \\@stable\n */\nexport class UpperCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'uppercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction UpperCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nUpperCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nUpperCasePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, EventEmitter, OnDestroy, Pipe, PipeTransform, WrappedValue, ɵisObservable, ɵisPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {ISubscription} from 'rxjs/Subscription';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\ninterface SubscriptionStrategy {\n  createSubscription(async: Observable<any>|Promise<any>, updateLatestValue: any): ISubscription\n      |Promise<any>;\n  dispose(subscription: ISubscription|Promise<any>): void;\n  onDestroy(subscription: ISubscription|Promise<any>): void;\n}\nclass ObservableStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Observable<any>, updateLatestValue: any): ISubscription {\n    return async.subscribe({next: updateLatestValue, error: (e: any) => { throw e; }});\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: ISubscription): void { subscription.unsubscribe(); }\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: ISubscription): void { subscription.unsubscribe(); }\n}\nclass PromiseStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Promise<any>, updateLatestValue: (v: any) => any): Promise<any> {\n    return async.then(updateLatestValue, e => { throw e; });\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: Promise<any>): void {}\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: Promise<any>): void {}\n}\n\nconst /** @type {?} */ _promiseStrategy = new PromiseStrategy();\nconst /** @type {?} */ _observableStrategy = new ObservableStrategy();\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Unwraps a value from an asynchronous primitive.\n * \\@howToUse `observable_or_promise_expression | async`\n * \\@description \n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks.\n * \n * \n * ## Examples\n * \n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n * \n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n * \n * \\@stable\n */\nexport class AsyncPipe implements OnDestroy, PipeTransform {\nprivate _latestValue: any = null;\nprivate _latestReturnedValue: any = null;\nprivate _subscription: ISubscription|Promise<any>|null = null;\nprivate _obj: Observable<any>|Promise<any>|EventEmitter<any>|null = null;\nprivate _strategy: SubscriptionStrategy = /** @type {?} */(( null));\n/**\n * @param {?} _ref\n */\nconstructor(private _ref: ChangeDetectorRef) {}\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._subscription) {\n      this._dispose();\n    }\n  }\n\n  transform<T>(obj: null): null;\n  transform<T>(obj: undefined): undefined;\n  transform<T>(obj: Observable<T>): T|null;\n  transform<T>(obj: Promise<T>): T|null;\n/**\n * @param {?} obj\n * @return {?}\n */\ntransform(obj: Observable<any>|Promise<any>|null|undefined): any {\n    if (!this._obj) {\n      if (obj) {\n        this._subscribe(obj);\n      }\n      this._latestReturnedValue = this._latestValue;\n      return this._latestValue;\n    }\n\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform( /** @type {?} */((obj as any)));\n    }\n\n    if (this._latestValue === this._latestReturnedValue) {\n      return this._latestReturnedValue;\n    }\n\n    this._latestReturnedValue = this._latestValue;\n    return WrappedValue.wrap(this._latestValue);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _subscribe(obj: Observable<any>|Promise<any>|EventEmitter<any>): void {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(\n        obj, (value: Object) => this._updateLatestValue(obj, value));\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _selectStrategy(obj: Observable<any>|Promise<any>|EventEmitter<any>): any {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n\n    if (ɵisObservable(obj)) {\n      return _observableStrategy;\n    }\n\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n/**\n * @return {?}\n */\nprivate _dispose(): void {\n    this._strategy.dispose( /** @type {?} */((this._subscription)));\n    this._latestValue = null;\n    this._latestReturnedValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n/**\n * @param {?} async\n * @param {?} value\n * @return {?}\n */\nprivate _updateLatestValue(async: any, value: Object): void {\n    if (async === this._obj) {\n      this._latestValue = value;\n      this._ref.markForCheck();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'async', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ChangeDetectorRef, },\n];\n}\n\nfunction AsyncPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nAsyncPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAsyncPipe.ctorParameters;\n/** @type {?} */\nAsyncPipe.prototype._latestValue;\n/** @type {?} */\nAsyncPipe.prototype._latestReturnedValue;\n/** @type {?} */\nAsyncPipe.prototype._subscription;\n/** @type {?} */\nAsyncPipe.prototype._obj;\n/** @type {?} */\nAsyncPipe.prototype._strategy;\n/** @type {?} */\nAsyncPipe.prototype._ref;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type, ɵstringify as stringify} from '@angular/core';\n/**\n * @param {?} type\n * @param {?} value\n * @return {?}\n */\nexport function invalidPipeArgumentError(type: Type<any>, value: Object) {\n  return Error(`InvalidPipeArgument: '${value}' for pipe '${stringify(type)}'`);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Provider} from '@angular/core';\n\nimport {NgClass} from './ng_class';\nimport {NgComponentOutlet} from './ng_component_outlet';\nimport {Ng<PERSON>or, NgForOf, NgForOfContext} from './ng_for_of';\nimport {NgIf, NgIfContext} from './ng_if';\nimport {NgPlural, NgPluralCase} from './ng_plural';\nimport {NgStyle} from './ng_style';\nimport {Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault} from './ng_switch';\nimport {NgTemplateOutlet} from './ng_template_outlet';\n\nexport {\n  NgClass,\n  NgComponentOutlet,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  Ng<PERSON><PERSON><PERSON>,\n  Ng<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>Case,\n  <PERSON><PERSON><PERSON>Default,\n  NgTemplateOutlet\n};\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport const COMMON_DIRECTIVES: Provider[] = [\n  NgClass,\n  NgComponentOutlet,\n  NgForOf,\n  NgIf,\n  NgTemplateOutlet,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgPlural,\n  NgPluralCase,\n];\n/**\n * A collection of deprecated directives that are no longer part of the core module.\n */\nexport const COMMON_DEPRECATED_DIRECTIVES: Provider[] = [NgFor];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, OnChanges, SimpleChanges, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Inserts an embedded view from a prepared `TemplateRef`\n * \n * \\@howToUse \n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n * \n * \\@description \n * \n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n * \n * Note: using the key `$implicit` in the context object will set it's value as default.\n * \n * ## Example\n * \n * {\\@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n * \n * \\@experimental\n */\nexport class NgTemplateOutlet implements OnChanges {\nprivate _viewRef: EmbeddedViewRef<any>;\npublic ngTemplateOutletContext: Object;\npublic ngTemplateOutlet: TemplateRef<any>;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @deprecated v4.0.0 - Renamed to ngTemplateOutletContext.\n * @param {?} context\n * @return {?}\n */\nset ngOutletContext(context: Object) { this.ngTemplateOutletContext = context; }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    if (this._viewRef) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._viewRef));\n    }\n\n    if (this.ngTemplateOutlet) {\n      this._viewRef = this._viewContainerRef.createEmbeddedView(\n          this.ngTemplateOutlet, this.ngTemplateOutletContext);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngTemplateOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngTemplateOutletContext': [{ type: Input },],\n'ngTemplateOutlet': [{ type: Input },],\n'ngOutletContext': [{ type: Input },],\n};\n}\n\nfunction NgTemplateOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgTemplateOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgTemplateOutlet.ctorParameters;\n/** @type {?} */\nNgTemplateOutlet.propDecorators;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewRef;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutletContext;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutlet;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, KeyValueChanges, KeyV<PERSON>ueDiffer, Key<PERSON><PERSON>ue<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Update an HTML element styles.\n * \n * \\@howToUse \n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The styles are updated according to the value of the expression evaluation:\n * - keys are style names with an optional `.<unit>` suffix (ie 'top.px', 'font-style.em'),\n * - values are the values assigned to those properties (expressed in the given unit).\n * \n * \\@stable\n */\nexport class NgStyle implements DoCheck {\nprivate _ngStyle: {[key: string]: string};\nprivate _differ: KeyValueDiffer<string, string|number>;\n/**\n * @param {?} _differs\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _differs: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset ngStyle(v: {[key: string]: string}) {\n    this._ngStyle = v;\n    if (!this._differ && v) {\n      this._differ = this._differs.find(v).create();\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck() {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this._ngStyle);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: KeyValueChanges<string, string|number>): void {\n    changes.forEachRemovedItem((record) => this._setStyle(record.key, null));\n    changes.forEachAddedItem((record) => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._setStyle(record.key, record.currentValue));\n  }\n/**\n * @param {?} nameAndUnit\n * @param {?} value\n * @return {?}\n */\nprivate _setStyle(nameAndUnit: string, value: string|number|null|undefined): void {\n    const [name, unit] = nameAndUnit.split('.');\n    value = value != null && unit ? `${value}${unit}` : value;\n\n    this._renderer.setElementStyle(this._ngEl.nativeElement, name, /** @type {?} */(( value as string)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngStyle]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngStyle': [{ type: Input },],\n};\n}\n\nfunction NgStyle_tsickle_Closure_declarations() {\n/** @type {?} */\nNgStyle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgStyle.ctorParameters;\n/** @type {?} */\nNgStyle.propDecorators;\n/** @type {?} */\nNgStyle.prototype._ngStyle;\n/** @type {?} */\nNgStyle.prototype._differ;\n/** @type {?} */\nNgStyle.prototype._differs;\n/** @type {?} */\nNgStyle.prototype._ngEl;\n/** @type {?} */\nNgStyle.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n\nimport {NgLocalization, getPluralCategory} from '../localization';\n\nimport {SwitchView} from './ng_switch';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n * \n * \\@description \n * \n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n * \n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n * \n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n * \n * \\@experimental\n */\nexport class NgPlural {\nprivate _switchValue: number;\nprivate _activeView: SwitchView;\nprivate _caseViews: {[k: string]: SwitchView} = {};\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngPlural(value: number) {\n    this._switchValue = value;\n    this._updateView();\n  }\n/**\n * @param {?} value\n * @param {?} switchView\n * @return {?}\n */\naddCase(value: string, switchView: SwitchView): void { this._caseViews[value] = switchView; }\n/**\n * @return {?}\n */\nprivate _updateView(): void {\n    this._clearViews();\n\n    const /** @type {?} */ cases = Object.keys(this._caseViews);\n    const /** @type {?} */ key = getPluralCategory(this._switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n/**\n * @return {?}\n */\nprivate _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n/**\n * @param {?} view\n * @return {?}\n */\nprivate _activateView(view: SwitchView) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPlural]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngPlural': [{ type: Input },],\n};\n}\n\nfunction NgPlural_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPlural.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPlural.ctorParameters;\n/** @type {?} */\nNgPlural.propDecorators;\n/** @type {?} */\nNgPlural.prototype._switchValue;\n/** @type {?} */\nNgPlural.prototype._activeView;\n/** @type {?} */\nNgPlural.prototype._caseViews;\n/** @type {?} */\nNgPlural.prototype._localization;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgPlural} when the\n *             given expression matches the plural expression according to CLDR rules.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n * ```\n * \n * See {\\@link NgPlural} for more details and example.\n * \n * \\@experimental\n */\nexport class NgPluralCase {\n/**\n * @param {?} value\n * @param {?} template\n * @param {?} viewContainer\n * @param {?} ngPlural\n */\nconstructor(\npublic value: string, template: TemplateRef<Object>,\n      viewContainer: ViewContainerRef,  ngPlural: NgPlural) {\n    const isANumber: boolean = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPluralCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Attribute, args: ['ngPluralCase', ] }, ]},\n{type: TemplateRef, },\n{type: ViewContainerRef, },\n{type: NgPlural, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgPluralCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPluralCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPluralCase.ctorParameters;\n/** @type {?} */\nNgPluralCase.prototype.value;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\nexport class SwitchView {\nprivate _created = false;\n/**\n * @param {?} _viewContainerRef\n * @param {?} _templateRef\n */\nconstructor(\nprivate _viewContainerRef: ViewContainerRef,\nprivate _templateRef: TemplateRef<Object>) {}\n/**\n * @return {?}\n */\ncreate(): void {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n/**\n * @param {?} created\n * @return {?}\n */\nenforceState(created: boolean) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n\nfunction SwitchView_tsickle_Closure_declarations() {\n/** @type {?} */\nSwitchView.prototype._created;\n/** @type {?} */\nSwitchView.prototype._viewContainerRef;\n/** @type {?} */\nSwitchView.prototype._templateRef;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees when the nest match expressions matches the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n *     <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n * \\@description \n * \n * `NgSwitch` stamps out nested views when their match expression value matches the value of the\n * switch expression.\n * \n * In other words:\n * - you define a container element (where you place the directive with a switch expression on the\n * `[ngSwitch]=\"...\"` attribute)\n * - you define inner views inside the `NgSwitch` and place a `*ngSwitchCase` attribute on the view\n * root elements.\n * \n * Elements within `NgSwitch` but outside of a `NgSwitchCase` or `NgSwitchDefault` directives will\n * be preserved at the location.\n * \n * The `ngSwitchCase` directive informs the parent `NgSwitch` of which view to display when the\n * expression is evaluated.\n * When no matching expression is found on a `ngSwitchCase` view, the `ngSwitchDefault` view is\n * stamped out.\n * \n * \\@stable\n */\nexport class NgSwitch {\nprivate _defaultViews: SwitchView[];\nprivate _defaultUsed = false;\nprivate _caseCount = 0;\nprivate _lastCaseCheckIndex = 0;\nprivate _lastCasesMatched = false;\nprivate _ngSwitch: any;\n/**\n * @param {?} newValue\n * @return {?}\n */\nset ngSwitch(newValue: any) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_addCase(): number { return this._caseCount++; }\n/**\n * \\@internal\n * @param {?} view\n * @return {?}\n */\n_addDefault(view: SwitchView) {\n    if (!this._defaultViews) {\n      this._defaultViews = [];\n    }\n    this._defaultViews.push(view);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_matchCase(value: any): boolean {\n    const /** @type {?} */ matched = value == this._ngSwitch;\n    this._lastCasesMatched = this._lastCasesMatched || matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n/**\n * @param {?} useDefault\n * @return {?}\n */\nprivate _updateDefaultCases(useDefault: boolean) {\n    if (this._defaultViews && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (let /** @type {?} */ i = 0; i < this._defaultViews.length; i++) {\n        const /** @type {?} */ defaultView = this._defaultViews[i];\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitch]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitch': [{ type: Input },],\n};\n}\n\nfunction NgSwitch_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitch.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitch.ctorParameters;\n/** @type {?} */\nNgSwitch.propDecorators;\n/** @type {?} */\nNgSwitch.prototype._defaultViews;\n/** @type {?} */\nNgSwitch.prototype._defaultUsed;\n/** @type {?} */\nNgSwitch.prototype._caseCount;\n/** @type {?} */\nNgSwitch.prototype._lastCaseCheckIndex;\n/** @type {?} */\nNgSwitch.prototype._lastCasesMatched;\n/** @type {?} */\nNgSwitch.prototype._ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgSwitch} when the\n *             given expression evaluate to respectively the same/different value as the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * </container-element>\n * ```\n * \\@description \n * \n * Insert the sub-tree when the expression evaluates to the same value as the enclosing switch\n * expression.\n * \n * If multiple match expressions match the switch expression value, all of them are displayed.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchCase implements DoCheck {\nprivate _view: SwitchView;\n\n  \n  ngSwitchCase: any;\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\nprivate ngSwitch: NgSwitch) {\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n/**\n * @return {?}\n */\nngDoCheck() { this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitchCase': [{ type: Input },],\n};\n}\n\nfunction NgSwitchCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchCase.ctorParameters;\n/** @type {?} */\nNgSwitchCase.propDecorators;\n/** @type {?} */\nNgSwitchCase.prototype._view;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitchCase;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a view that is added to the parent {\\@link NgSwitch} when no case expressions\n * match the\n *             switch expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-other-element *ngSwitchDefault>...</some-other-element>\n * </container-element>\n * ```\n * \n * \\@description \n * \n * Insert the sub-tree when no case expressions evaluate to the same value as the enclosing switch\n * expression.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchDefault {\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\n       ngSwitch: NgSwitch) {\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchDefault]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgSwitchDefault_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchDefault.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchDefault.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * Conditionally includes a template based on the value of an `expression`.\n * \n * `ngIf` evaluates the `expression` and then renders the `then` or `else` template in its place\n * when expression is truthy or falsy respectively. Typically the:\n *  - `then` template is the inline template of `ngIf` unless bound to a different value.\n *  - `else` template is blank unless it is bound.\n * \n * ## Most common usage\n * \n * The most common usage of the `ngIf` directive is to conditionally show the inline template as\n * seen in this example:\n * {\\@example common/ngIf/ts/module.ts region='NgIfSimple'}\n * \n * ## Showing an alternative template using `else`\n * \n * If it is necessary to display a template when the `expression` is falsy use the `else` template\n * binding as shown. Note that the `else` binding points to a `<ng-template>` labeled `#elseBlock`.\n * The template can be defined anywhere in the component view but is typically placed right after\n * `ngIf` for readability.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfElse'}\n * \n * ## Using non-inlined `then` template\n * \n * Usually the `then` template is the inlined template of the `ngIf`, but it can be changed using\n * a binding (just like `else`). Because `then` and `else` are bindings, the template references can\n * change at runtime as shown in this example.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n * \n * ## Storing conditional result in a variable\n * \n * A common pattern is that we need to show a set of properties from the same object. If the\n * object is undefined, then we have to use the safe-traversal-operator `?.` to guard against\n * dereferencing a `null` value. This is especially the case when waiting on async data such as\n * when using the `async` pipe as shown in folowing example:\n * \n * ```\n * Hello {{ (userStream|async)?.last }}, {{ (userStream|async)?.first }}!\n * ```\n * \n * There are several inefficiencies in the above example:\n *  - We create multiple subscriptions on `userStream`. One for each `async` pipe, or two in the\n *    example above.\n *  - We cannot display an alternative screen while waiting for the data to arrive asynchronously.\n *  - We have to use the safe-traversal-operator `?.` to access properties, which is cumbersome.\n *  - We have to place the `async` pipe in parenthesis.\n * \n * A better way to do this is to use `ngIf` and store the result of the condition in a local\n * variable as shown in the the example below:\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfAs'}\n * \n * Notice that:\n *  - We use only one `async` pipe and hence only one subscription gets created.\n *  - `ngIf` stores the result of the `userStream|async` in the local variable `user`.\n *  - The local `user` can then be bound repeatedly in a more efficient way.\n *  - No need to use the safe-traversal-operator `?.` to access properties as `ngIf` will only\n *    display the data if `userStream` returns a value.\n *  - We can display an alternative template while waiting for the data.\n * \n * ### Syntax\n * \n * Simple form:\n * - `<div *ngIf=\"condition\">...</div>`\n * - `<div template=\"ngIf condition\">...</div>`\n * - `<ng-template [ngIf]=\"condition\"><div>...</div></ng-template>`\n * \n * Form with an else block:\n * ```\n * <div *ngIf=\"condition; else elseBlock\">...</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with a `then` and `else` block:\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>...</ng-template>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with storing the value locally:\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * \\@stable\n */\nexport class NgIf {\nprivate _context: NgIfContext = new NgIfContext();\nprivate _thenTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _elseTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _thenViewRef: EmbeddedViewRef<NgIfContext>|null = null;\nprivate _elseViewRef: EmbeddedViewRef<NgIfContext>|null = null;\n/**\n * @param {?} _viewContainer\n * @param {?} templateRef\n */\nconstructor(private _viewContainer: ViewContainerRef, templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n  }\n/**\n * @param {?} condition\n * @return {?}\n */\nset ngIf(condition: any) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfThen(templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfElse(templateRef: TemplateRef<NgIfContext>) {\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @return {?}\n */\nprivate _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef =\n              this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef =\n              this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n        }\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngIf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngIf': [{ type: Input },],\n'ngIfThen': [{ type: Input },],\n'ngIfElse': [{ type: Input },],\n};\n}\n\nfunction NgIf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgIf.ctorParameters;\n/** @type {?} */\nNgIf.propDecorators;\n/** @type {?} */\nNgIf.prototype._context;\n/** @type {?} */\nNgIf.prototype._thenTemplateRef;\n/** @type {?} */\nNgIf.prototype._elseTemplateRef;\n/** @type {?} */\nNgIf.prototype._thenViewRef;\n/** @type {?} */\nNgIf.prototype._elseViewRef;\n/** @type {?} */\nNgIf.prototype._viewContainer;\n}\n\n/**\n * \\@stable\n */\nexport class NgIfContext {\npublic $implicit: any = null;\npublic ngIf: any = null;\n}\n\nfunction NgIfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIfContext.prototype.$implicit;\n/** @type {?} */\nNgIfContext.prototype.ngIf;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, Directive, DoCheck, EmbeddedViewRef, Input, IterableChangeRecord, IterableChanges, IterableDiffer, IterableDiffers, NgIterable, OnChanges, SimpleChanges, TemplateRef, TrackByFunction, ViewContainerRef, forwardRef, isDevMode} from '@angular/core';\n/**\n * \\@stable\n */\nexport class NgForOfContext<T> {\n/**\n * @param {?} $implicit\n * @param {?} ngForOf\n * @param {?} index\n * @param {?} count\n */\nconstructor(\npublic $implicit: T,\npublic ngForOf: NgIterable<T>,\npublic index: number,\npublic count: number) {}\n/**\n * @return {?}\n */\nget first(): boolean { return this.index === 0; }\n/**\n * @return {?}\n */\nget last(): boolean { return this.index === this.count - 1; }\n/**\n * @return {?}\n */\nget even(): boolean { return this.index % 2 === 0; }\n/**\n * @return {?}\n */\nget odd(): boolean { return !this.even; }\n}\n\nfunction NgForOfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOfContext.prototype.$implicit;\n/** @type {?} */\nNgForOfContext.prototype.ngForOf;\n/** @type {?} */\nNgForOfContext.prototype.index;\n/** @type {?} */\nNgForOfContext.prototype.count;\n}\n\n/**\n * The `NgForOf` directive instantiates a template once per item from an iterable. The context\n * for each instantiated template inherits from the outer context with the given loop variable\n * set to the current item from the iterable.\n * \n * ### Local Variables\n * \n * `NgForOf` provides several exported values that can be aliased to local variables:\n * \n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n * \n * ```\n * <li *ngFor=\"let user of userObservable | async as users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n * \n * ### Change Propagation\n * \n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n * \n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n * * Otherwise, the DOM element for that item will remain the same.\n * \n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls (such as `<input>` elements which accept user input) that are present. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * \n * It is possible for the identities of elements in the iterator to change while the data does not.\n * This can happen, for example, if the iterator produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response will produce objects with\n * different identities, and Angular will tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted). This is an expensive operation and should\n * be avoided if possible.\n * \n * To customize the default tracking algorithm, `NgForOf` supports `trackBy` option.\n * `trackBy` takes a function which has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n * \n * ### Syntax\n * \n * - `<li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>`\n * - `<li template=\"ngFor let item of items; index as i; trackBy: trackByFn\">...</li>`\n * \n * With `<ng-template>` element:\n * \n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n * \n * ### Example\n * \n * See a [live demo](http://plnkr.co/edit/KVuXxDp0qinGDyo307QW?p=preview) for a more detailed\n * example.\n * \n * \\@stable\n */\nexport class NgForOf<T> implements DoCheck, OnChanges {\n   ngForOf: NgIterable<T>;\n/**\n * @param {?} fn\n * @return {?}\n */\nset ngForTrackBy(fn: TrackByFunction<T>) {\n    if (isDevMode() && fn != null && typeof fn !== 'function') {\n      // TODO(vicb): use a log service once there is a public one available\n      if ( /** @type {?} */((<any>console)) && /** @type {?} */(( <any>console.warn))) {\n        console.warn(\n            `trackBy must be a function, but received ${JSON.stringify(fn)}. ` +\n            `See https://angular.io/docs/ts/latest/api/common/index/NgFor-directive.html#!#change-propagation for more information.`);\n      }\n    }\n    this._trackByFn = fn;\n  }\n/**\n * @return {?}\n */\nget ngForTrackBy(): TrackByFunction<T> { return this._trackByFn; }\nprivate _differ: IterableDiffer<T>|null = null;\nprivate _trackByFn: TrackByFunction<T>;\n/**\n * @param {?} _viewContainer\n * @param {?} _template\n * @param {?} _differs\n */\nconstructor(\nprivate _viewContainer: ViewContainerRef,\nprivate _template: TemplateRef<NgForOfContext<T>>,\nprivate _differs: IterableDiffers) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngForTemplate(value: TemplateRef<NgForOfContext<T>>) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('ngForOf' in changes) {\n      // React on ngForOf changes only once all inputs have been initialized\n      const /** @type {?} */ value = changes['ngForOf'].currentValue;\n      if (!this._differ && value) {\n        try {\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        } catch ( /** @type {?} */e) {\n          throw new Error(\n              `Cannot find a differ supporting object '${value}' of type '${getTypeNameForDebugging(value)}'. NgFor only supports binding to Iterables such as Arrays.`);\n        }\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this.ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: IterableChanges<T>) {\n    const /** @type {?} */ insertTuples: RecordViewTuple<T>[] = [];\n    changes.forEachOperation(\n        (item: IterableChangeRecord<any>, adjustedPreviousIndex: number, currentIndex: number) => {\n          if (item.previousIndex == null) {\n            const /** @type {?} */ view = this._viewContainer.createEmbeddedView(\n                this._template, new NgForOfContext<T>( /** @type {?} */((null)), this.ngForOf, -1, -1), currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple<T>(item, view);\n            insertTuples.push(tuple);\n          } else if (currentIndex == null) {\n            this._viewContainer.remove(adjustedPreviousIndex);\n          } else {\n            const /** @type {?} */ view = /** @type {?} */(( this._viewContainer.get(adjustedPreviousIndex)));\n            this._viewContainer.move(view, currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple(item, /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>view)));\n            insertTuples.push(tuple);\n          }\n        });\n\n    for (let /** @type {?} */ i = 0; i < insertTuples.length; i++) {\n      this._perViewChange(insertTuples[i].view, insertTuples[i].record);\n    }\n\n    for (let /** @type {?} */ i = 0, /** @type {?} */ ilen = this._viewContainer.length; i < ilen; i++) {\n      const /** @type {?} */ viewRef = /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(i)));\n      viewRef.context.index = i;\n      viewRef.context.count = ilen;\n    }\n\n    changes.forEachIdentityChange((record: any) => {\n      const /** @type {?} */ viewRef = /** @type {?} */((\n          <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(record.currentIndex)));\n      viewRef.context.$implicit = record.item;\n    });\n  }\n/**\n * @param {?} view\n * @param {?} record\n * @return {?}\n */\nprivate _perViewChange(\n      view: EmbeddedViewRef<NgForOfContext<T>>, record: IterableChangeRecord<any>) {\n    view.context.$implicit = record.item;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngFor][ngForOf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: IterableDiffers, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngForOf': [{ type: Input },],\n'ngForTrackBy': [{ type: Input },],\n'ngForTemplate': [{ type: Input },],\n};\n}\n\nfunction NgForOf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgForOf.ctorParameters;\n/** @type {?} */\nNgForOf.propDecorators;\n/** @type {?} */\nNgForOf.prototype.ngForOf;\n/** @type {?} */\nNgForOf.prototype._differ;\n/** @type {?} */\nNgForOf.prototype._trackByFn;\n/** @type {?} */\nNgForOf.prototype._viewContainer;\n/** @type {?} */\nNgForOf.prototype._template;\n/** @type {?} */\nNgForOf.prototype._differs;\n}\n\nclass RecordViewTuple<T> {\n/**\n * @param {?} record\n * @param {?} view\n */\nconstructor(public record: any,\npublic view: EmbeddedViewRef<NgForOfContext<T>>) {}\n}\n\nfunction RecordViewTuple_tsickle_Closure_declarations() {\n/** @type {?} */\nRecordViewTuple.prototype.record;\n/** @type {?} */\nRecordViewTuple.prototype.view;\n}\n\n\n/**\n * @deprecated from v4.0.0 - Use NgForOf<any> instead.\n */\nexport type NgFor = NgForOf<any>;\n/**\n * @deprecated from v4.0.0 - Use NgForOf instead.\n */\nexport const NgFor = NgForOf;\n/**\n * @param {?} type\n * @return {?}\n */\nexport function getTypeNameForDebugging(type: any): string {\n  return type['name'] || typeof type;\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentFactoryResolver, ComponentRef, Directive, Injector, Input, NgModuleFactory, NgModuleRef, OnChanges, OnDestroy, Provider, SimpleChanges, Type, ViewContainerRef} from '@angular/core';\n/**\n * Instantiates a single {\\@link Component} type and inserts its Host View into current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n * \n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will get destroyed.\n * \n * ### Fine tune control\n * \n * You can control the component creation process by using the following optional attributes:\n * \n * * `ngComponentOutletInjector`: Optional custom {\\@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n * \n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if exists.\n * \n * * `ngComponentOutletNgModuleFactory`: Optional module factory to allow dynamically loading other\n * module, then load a component from that module.\n * \n * ### Syntax\n * \n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n * \n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n * \n * Customized ngModuleFactory\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModuleFactory: moduleFactory;\">\n * </ng-container>\n * ```\n * ## Example\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n * \n * A more complete example with additional options:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n * A more complete example with ngModuleFactory:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='NgModuleFactoryExample'}\n * \n * \\@experimental\n */\nexport class NgComponentOutlet implements OnChanges, OnDestroy {\n   ngComponentOutlet: Type<any>;\n   ngComponentOutletInjector: Injector;\n   ngComponentOutletContent: any[][];\n   ngComponentOutletNgModuleFactory: NgModuleFactory<any>;\nprivate _componentRef: ComponentRef<any>|null = null;\nprivate _moduleRef: NgModuleRef<any>|null = null;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    this._viewContainerRef.clear();\n    this._componentRef = null;\n\n    if (this.ngComponentOutlet) {\n      const /** @type {?} */ elInjector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n      if (changes['ngComponentOutletNgModuleFactory']) {\n        if (this._moduleRef) this._moduleRef.destroy();\n\n        if (this.ngComponentOutletNgModuleFactory) {\n          const /** @type {?} */ parentModule = elInjector.get(NgModuleRef);\n          this._moduleRef = this.ngComponentOutletNgModuleFactory.create(parentModule.injector);\n        } else {\n          this._moduleRef = null;\n        }\n      }\n\n      const /** @type {?} */ componentFactoryResolver = this._moduleRef ? this._moduleRef.componentFactoryResolver :\n                                                         elInjector.get(ComponentFactoryResolver);\n\n      const /** @type {?} */ componentFactory =\n          componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);\n\n      this._componentRef = this._viewContainerRef.createComponent(\n          componentFactory, this._viewContainerRef.length, elInjector,\n          this.ngComponentOutletContent);\n    }\n  }\n/**\n * @return {?}\n */\nngOnDestroy() {\n    if (this._moduleRef) this._moduleRef.destroy();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngComponentOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngComponentOutlet': [{ type: Input },],\n'ngComponentOutletInjector': [{ type: Input },],\n'ngComponentOutletContent': [{ type: Input },],\n'ngComponentOutletNgModuleFactory': [{ type: Input },],\n};\n}\n\nfunction NgComponentOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgComponentOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgComponentOutlet.ctorParameters;\n/** @type {?} */\nNgComponentOutlet.propDecorators;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutlet;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletInjector;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletContent;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletNgModuleFactory;\n/** @type {?} */\nNgComponentOutlet.prototype._componentRef;\n/** @type {?} */\nNgComponentOutlet.prototype._moduleRef;\n/** @type {?} */\nNgComponentOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, IterableChanges, IterableDiffer, IterableDiffers, KeyValueChanges, KeyValueDiffer, KeyValueDiffers, <PERSON><PERSON><PERSON>, ɵisListLikeIterable as isListLikeIterable, ɵstringify as stringify} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds and removes CSS classes on an HTML element.\n * \n * \\@howToUse \n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n * \n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n * \n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n * \n * \\@stable\n */\nexport class NgClass implements DoCheck {\nprivate _iterableDiffer: IterableDiffer<string>|null;\nprivate _keyValueDiffer: KeyValueDiffer<string, any>|null;\nprivate _initialClasses: string[] = [];\nprivate _rawClass: string[]|Set<string>|{[klass: string]: any};\n/**\n * @param {?} _iterableDiffers\n * @param {?} _keyValueDiffers\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _iterableDiffers: IterableDiffers,\nprivate _keyValueDiffers: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset klass(v: string) {\n    this._applyInitialClasses(true);\n    this._initialClasses = typeof v === 'string' ? v.split(/\\s+/) : [];\n    this._applyInitialClasses(false);\n    this._applyClasses(this._rawClass, false);\n  }\n/**\n * @param {?} v\n * @return {?}\n */\nset ngClass(v: string|string[]|Set<string>|{[klass: string]: any}) {\n    this._cleanupClasses(this._rawClass);\n\n    this._iterableDiffer = null;\n    this._keyValueDiffer = null;\n\n    this._rawClass = typeof v === 'string' ? v.split(/\\s+/) : v;\n\n    if (this._rawClass) {\n      if (isListLikeIterable(this._rawClass)) {\n        this._iterableDiffer = this._iterableDiffers.find(this._rawClass).create();\n      } else {\n        this._keyValueDiffer = this._keyValueDiffers.find(this._rawClass).create();\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._iterableDiffer) {\n      const /** @type {?} */ iterableChanges = this._iterableDiffer.diff( /** @type {?} */((this._rawClass as string[])));\n      if (iterableChanges) {\n        this._applyIterableChanges(iterableChanges);\n      }\n    } else if (this._keyValueDiffer) {\n      const /** @type {?} */ keyValueChanges = this._keyValueDiffer.diff( /** @type {?} */((this._rawClass as{[k: string]: any})));\n      if (keyValueChanges) {\n        this._applyKeyValueChanges(keyValueChanges);\n      }\n    }\n  }\n/**\n * @param {?} rawClassVal\n * @return {?}\n */\nprivate _cleanupClasses(rawClassVal: string[]|{[klass: string]: any}): void {\n    this._applyClasses(rawClassVal, true);\n    this._applyInitialClasses(false);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyKeyValueChanges(changes: KeyValueChanges<string, any>): void {\n    changes.forEachAddedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachRemovedItem((record) => {\n      if (record.previousValue) {\n        this._toggleClass(record.key, false);\n      }\n    });\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyIterableChanges(changes: IterableChanges<string>): void {\n    changes.forEachAddedItem((record) => {\n      if (typeof record.item === 'string') {\n        this._toggleClass(record.item, true);\n      } else {\n        throw new Error(\n            `NgClass can only toggle CSS classes expressed as strings, got ${stringify(record.item)}`);\n      }\n    });\n\n    changes.forEachRemovedItem((record) => this._toggleClass(record.item, false));\n  }\n/**\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyInitialClasses(isCleanup: boolean) {\n    this._initialClasses.forEach(klass => this._toggleClass(klass, !isCleanup));\n  }\n/**\n * @param {?} rawClassVal\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyClasses(\n      rawClassVal: string[]|Set<string>|{[klass: string]: any}, isCleanup: boolean) {\n    if (rawClassVal) {\n      if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n        ( /** @type {?} */((<any>rawClassVal))).forEach((klass: string) => this._toggleClass(klass, !isCleanup));\n      } else {\n        Object.keys(rawClassVal).forEach(klass => {\n          if (rawClassVal[klass] != null) this._toggleClass(klass, !isCleanup);\n        });\n      }\n    }\n  }\n/**\n * @param {?} klass\n * @param {?} enabled\n * @return {?}\n */\nprivate _toggleClass(klass: string, enabled: any): void {\n    klass = klass.trim();\n    if (klass) {\n      klass.split(/\\s+/g).forEach(\n          klass => { this._renderer.setElementClass(this._ngEl.nativeElement, klass, !!enabled); });\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngClass]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: IterableDiffers, },\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'klass': [{ type: Input, args: ['class', ] },],\n'ngClass': [{ type: Input },],\n};\n}\n\nfunction NgClass_tsickle_Closure_declarations() {\n/** @type {?} */\nNgClass.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgClass.ctorParameters;\n/** @type {?} */\nNgClass.propDecorators;\n/** @type {?} */\nNgClass.prototype._iterableDiffer;\n/** @type {?} */\nNgClass.prototype._keyValueDiffer;\n/** @type {?} */\nNgClass.prototype._initialClasses;\n/** @type {?} */\nNgClass.prototype._rawClass;\n/** @type {?} */\nNgClass.prototype._iterableDiffers;\n/** @type {?} */\nNgClass.prototype._keyValueDiffers;\n/** @type {?} */\nNgClass.prototype._ngEl;\n/** @type {?} */\nNgClass.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, LOCALE_ID} from '@angular/core';\n/**\n * \\@experimental\n * @abstract\n */\nexport abstract class NgLocalization {\n/**\n * @abstract\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any) {} }\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n * \n * \\@internal\n * @param {?} value\n * @param {?} cases\n * @param {?} ngLocalization\n * @return {?}\n */\nexport function getPluralCategory(\n    value: number, cases: string[], ngLocalization: NgLocalization): string {\n  let /** @type {?} */ key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n */\nexport class NgLocaleLocalization extends NgLocalization {\n/**\n * @param {?} locale\n */\nconstructor( protected locale: string) { super(); }\n/**\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any): string {\n    const /** @type {?} */ plural = getPluralCase(this.locale, value);\n\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction NgLocaleLocalization_tsickle_Closure_declarations() {\n/** @type {?} */\nNgLocaleLocalization.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgLocaleLocalization.ctorParameters;\n/** @type {?} */\nNgLocaleLocalization.prototype.locale;\n}\n\nexport type Plural = number;\nexport let Plural: any = {};\nPlural.Zero = 0;\nPlural.One = 1;\nPlural.Two = 2;\nPlural.Few = 3;\nPlural.Many = 4;\nPlural.Other = 5;\nPlural[Plural.Zero] = \"Zero\";\nPlural[Plural.One] = \"One\";\nPlural[Plural.Two] = \"Two\";\nPlural[Plural.Few] = \"Few\";\nPlural[Plural.Many] = \"Many\";\nPlural[Plural.Other] = \"Other\";\n\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n * @param {?} locale\n * @param {?} nLike\n * @return {?}\n */\nexport function getPluralCase(locale: string, nLike: number | string): Plural {\n  // TODO(vicb): lazy compute\n  if (typeof nLike === 'string') {\n    nLike = parseInt( /** @type {?} */((<string>nLike)), 10);\n  }\n  const /** @type {?} */ n: number = /** @type {?} */(( nLike as number));\n  const /** @type {?} */ nDecimal = n.toString().replace(/^[^.]*\\.?/, '');\n  const /** @type {?} */ i = Math.floor(Math.abs(n));\n  const /** @type {?} */ v = nDecimal.length;\n  const /** @type {?} */ f = parseInt(nDecimal, 10);\n  const /** @type {?} */ t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n\n  const /** @type {?} */ lang = locale.split('-')[0].toLowerCase();\n\n  switch (lang) {\n    case 'af':\n    case 'asa':\n    case 'az':\n    case 'bem':\n    case 'bez':\n    case 'bg':\n    case 'brx':\n    case 'ce':\n    case 'cgg':\n    case 'chr':\n    case 'ckb':\n    case 'ee':\n    case 'el':\n    case 'eo':\n    case 'es':\n    case 'eu':\n    case 'fo':\n    case 'fur':\n    case 'gsw':\n    case 'ha':\n    case 'haw':\n    case 'hu':\n    case 'jgo':\n    case 'jmc':\n    case 'ka':\n    case 'kk':\n    case 'kkj':\n    case 'kl':\n    case 'ks':\n    case 'ksb':\n    case 'ky':\n    case 'lb':\n    case 'lg':\n    case 'mas':\n    case 'mgo':\n    case 'ml':\n    case 'mn':\n    case 'nb':\n    case 'nd':\n    case 'ne':\n    case 'nn':\n    case 'nnh':\n    case 'nyn':\n    case 'om':\n    case 'or':\n    case 'os':\n    case 'ps':\n    case 'rm':\n    case 'rof':\n    case 'rwk':\n    case 'saq':\n    case 'seh':\n    case 'sn':\n    case 'so':\n    case 'sq':\n    case 'ta':\n    case 'te':\n    case 'teo':\n    case 'tk':\n    case 'tr':\n    case 'ug':\n    case 'uz':\n    case 'vo':\n    case 'vun':\n    case 'wae':\n    case 'xog':\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ak':\n    case 'ln':\n    case 'mg':\n    case 'pa':\n    case 'ti':\n      if (n === Math.floor(n) && n >= 0 && n <= 1) return Plural.One;\n      return Plural.Other;\n    case 'am':\n    case 'as':\n    case 'bn':\n    case 'fa':\n    case 'gu':\n    case 'hi':\n    case 'kn':\n    case 'mr':\n    case 'zu':\n      if (i === 0 || n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ar':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return Plural.Many;\n      return Plural.Other;\n    case 'ast':\n    case 'ca':\n    case 'de':\n    case 'en':\n    case 'et':\n    case 'fi':\n    case 'fy':\n    case 'gl':\n    case 'it':\n    case 'nl':\n    case 'sv':\n    case 'sw':\n    case 'ur':\n    case 'yi':\n      if (i === 1 && v === 0) return Plural.One;\n      return Plural.Other;\n    case 'be':\n      if (n % 10 === 1 && !(n % 100 === 11)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 4 &&\n          !(n % 100 >= 12 && n % 100 <= 14))\n        return Plural.Few;\n      if (n % 10 === 0 || n % 10 === Math.floor(n % 10) && n % 10 >= 5 && n % 10 <= 9 ||\n          n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'br':\n      if (n % 10 === 1 && !(n % 100 === 11 || n % 100 === 71 || n % 100 === 91)) return Plural.One;\n      if (n % 10 === 2 && !(n % 100 === 12 || n % 100 === 72 || n % 100 === 92)) return Plural.Two;\n      if (n % 10 === Math.floor(n % 10) && (n % 10 >= 3 && n % 10 <= 4 || n % 10 === 9) &&\n          !(n % 100 >= 10 && n % 100 <= 19 || n % 100 >= 70 && n % 100 <= 79 ||\n            n % 100 >= 90 && n % 100 <= 99))\n        return Plural.Few;\n      if (!(n === 0) && n % 1e6 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'bs':\n    case 'hr':\n    case 'sr':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11))\n        return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n              !(i % 100 >= 12 && i % 100 <= 14) ||\n          f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 &&\n              !(f % 100 >= 12 && f % 100 <= 14))\n        return Plural.Few;\n      return Plural.Other;\n    case 'cs':\n    case 'sk':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0) return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'cy':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === 3) return Plural.Few;\n      if (n === 6) return Plural.Many;\n      return Plural.Other;\n    case 'da':\n      if (n === 1 || !(t === 0) && (i === 0 || i === 1)) return Plural.One;\n      return Plural.Other;\n    case 'dsb':\n    case 'hsb':\n      if (v === 0 && i % 100 === 1 || f % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2 || f % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 ||\n          f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ff':\n    case 'fr':\n    case 'hy':\n    case 'kab':\n      if (i === 0 || i === 1) return Plural.One;\n      return Plural.Other;\n    case 'fil':\n      if (v === 0 && (i === 1 || i === 2 || i === 3) ||\n          v === 0 && !(i % 10 === 4 || i % 10 === 6 || i % 10 === 9) ||\n          !(v === 0) && !(f % 10 === 4 || f % 10 === 6 || f % 10 === 9))\n        return Plural.One;\n      return Plural.Other;\n    case 'ga':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === Math.floor(n) && n >= 3 && n <= 6) return Plural.Few;\n      if (n === Math.floor(n) && n >= 7 && n <= 10) return Plural.Many;\n      return Plural.Other;\n    case 'gd':\n      if (n === 1 || n === 11) return Plural.One;\n      if (n === 2 || n === 12) return Plural.Two;\n      if (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19)) return Plural.Few;\n      return Plural.Other;\n    case 'gv':\n      if (v === 0 && i % 10 === 1) return Plural.One;\n      if (v === 0 && i % 10 === 2) return Plural.Two;\n      if (v === 0 &&\n          (i % 100 === 0 || i % 100 === 20 || i % 100 === 40 || i % 100 === 60 || i % 100 === 80))\n        return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'he':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === 2 && v === 0) return Plural.Two;\n      if (v === 0 && !(n >= 0 && n <= 10) && n % 10 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'is':\n      if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return Plural.One;\n      return Plural.Other;\n    case 'ksh':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'kw':\n    case 'naq':\n    case 'se':\n    case 'smn':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      return Plural.Other;\n    case 'lag':\n      if (n === 0) return Plural.Zero;\n      if ((i === 0 || i === 1) && !(n === 0)) return Plural.One;\n      return Plural.Other;\n    case 'lt':\n      if (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 9 &&\n          !(n % 100 >= 11 && n % 100 <= 19))\n        return Plural.Few;\n      if (!(f === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'lv':\n    case 'prg':\n      if (n % 10 === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19 ||\n          v === 2 && f % 100 === Math.floor(f % 100) && f % 100 >= 11 && f % 100 <= 19)\n        return Plural.Zero;\n      if (n % 10 === 1 && !(n % 100 === 11) || v === 2 && f % 10 === 1 && !(f % 100 === 11) ||\n          !(v === 2) && f % 10 === 1)\n        return Plural.One;\n      return Plural.Other;\n    case 'mk':\n      if (v === 0 && i % 10 === 1 || f % 10 === 1) return Plural.One;\n      return Plural.Other;\n    case 'mt':\n      if (n === 1) return Plural.One;\n      if (n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 10)\n        return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19) return Plural.Many;\n      return Plural.Other;\n    case 'pl':\n      if (i === 1 && v === 0) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && !(i === 1) && i % 10 === Math.floor(i % 10) && i % 10 >= 0 && i % 10 <= 1 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 12 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'pt':\n      if (n === Math.floor(n) && n >= 0 && n <= 2 && !(n === 2)) return Plural.One;\n      return Plural.Other;\n    case 'ro':\n      if (i === 1 && v === 0) return Plural.One;\n      if (!(v === 0) || n === 0 ||\n          !(n === 1) && n % 100 === Math.floor(n % 100) && n % 100 >= 1 && n % 100 <= 19)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ru':\n    case 'uk':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11)) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && i % 10 === 0 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 11 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'shi':\n      if (i === 0 || n === 1) return Plural.One;\n      if (n === Math.floor(n) && n >= 2 && n <= 10) return Plural.Few;\n      return Plural.Other;\n    case 'si':\n      if (n === 0 || n === 1 || i === 0 && f === 1) return Plural.One;\n      return Plural.Other;\n    case 'sl':\n      if (v === 0 && i % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || !(v === 0))\n        return Plural.Few;\n      return Plural.Other;\n    case 'tzm':\n      if (n === Math.floor(n) && n >= 0 && n <= 1 || n === Math.floor(n) && n >= 11 && n <= 99)\n        return Plural.One;\n      return Plural.Other;\n    // When there is no specification, the default is always \"other\"\n    // Spec: http://cldr.unicode.org/index/cldr-spec/plural-rules\n    // > other (required—general plural form — also used if the language only has a single form)\n    default:\n      return Plural.Other;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener} from './platform_location';\nexport {LocationStrategy,APP_BASE_HREF} from './location_strategy';\nexport {HashLocationStrategy} from './hash_location_strategy';\nexport {PathLocationStrategy} from './path_location_strategy';\nexport {PopStateEvent,Location} from './location';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL for storing application location data.\n * \\@description \n * `PathLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n * \n * If you're using `PathLocationStrategy`, you must provide a {\\@link APP_BASE_HREF}\n * or add a base element to the document. This URL prefix that will be preserved\n * when generating and recognizing URLs.\n * \n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * Similarly, if you add `<base href='/my/app'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class PathLocationStrategy extends LocationStrategy {\nprivate _baseHref: string;\n/**\n * @param {?} _platformLocation\n * @param {?=} href\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        href?: string) {\n    super();\n\n    if (href == null) {\n      href = this._platformLocation.getBaseHrefFromDOM();\n    }\n\n    if (href == null) {\n      throw new Error(\n          `No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.`);\n    }\n\n    this._baseHref = href;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    return Location.joinWithSlash(this._baseHref, internal);\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    const /** @type {?} */ pathname = this._platformLocation.pathname +\n        Location.normalizeQueryParams(this._platformLocation.search);\n    const /** @type {?} */ hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction PathLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nPathLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPathLocationStrategy.ctorParameters;\n/** @type {?} */\nPathLocationStrategy.prototype._baseHref;\n/** @type {?} */\nPathLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL hash for storing application location data.\n * \\@description \n * `HashLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n * \n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class HashLocationStrategy extends LocationStrategy {\nprivate _baseHref: string = '';\n/**\n * @param {?} _platformLocation\n * @param {?=} _baseHref\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        _baseHref?: string) {\n    super();\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    let /** @type {?} */ path = this._platformLocation.hash;\n    if (path == null) path = '#';\n\n    return path.length > 0 ? path.substring(1) : path;\n  }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    const /** @type {?} */ url = Location.joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? ('#' + url) : url;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url: string|null =\n        this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.pushState(state, title, url);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url = this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.replaceState(state, title, url);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction HashLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nHashLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHashLocationStrategy.ctorParameters;\n/** @type {?} */\nHashLocationStrategy.prototype._baseHref;\n/** @type {?} */\nHashLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {EventEmitter, Injectable} from '@angular/core';\n\nimport {LocationStrategy} from './location_strategy';\n\n/** @experimental */\nexport interface PopStateEvent {\n  pop?: boolean;\n  type?: string;\n  url?: string;\n}\n/**\n * \\@whatItDoes `Location` is a service that applications can use to interact with a browser's URL.\n * \\@description \n * Depending on which {\\@link LocationStrategy} is used, `Location` will either persist\n * to the URL's path or the URL's hash segment.\n * \n * Note: it's better to use {\\@link Router#navigate} service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n * \n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n * \n * ### Example\n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \\@stable\n */\nexport class Location {\n/**\n * \\@internal\n */\n_subject: EventEmitter<any> = new EventEmitter();\n/**\n * \\@internal\n */\n_baseHref: string;\n/**\n * \\@internal\n */\n_platformStrategy: LocationStrategy;\n/**\n * @param {?} platformStrategy\n */\nconstructor(platformStrategy: LocationStrategy) {\n    this._platformStrategy = platformStrategy;\n    const browserBaseHref = this._platformStrategy.getBaseHref();\n    this._baseHref = Location.stripTrailingSlash(_stripIndexHtml(browserBaseHref));\n    this._platformStrategy.onPopState((ev) => {\n      this._subject.emit({\n        'url': this.path(true),\n        'pop': true,\n        'type': ev.type,\n      });\n    });\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    return this.normalize(this._platformStrategy.path(includeHash));\n  }\n/**\n * Normalizes the given path and compares to the current normalized path.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nisCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    return this.path() == this.normalize(path + Location.normalizeQueryParams(query));\n  }\n/**\n * Given a string representing a URL, returns the normalized URL path without leading or\n * trailing slashes.\n * @param {?} url\n * @return {?}\n */\nnormalize(url: string): string {\n    return Location.stripTrailingSlash(_stripBaseHref(this._baseHref, _stripIndexHtml(url)));\n  }\n/**\n * Given a string representing a URL, returns the platform-specific external URL path.\n * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n * @param {?} url\n * @return {?}\n */\nprepareExternalUrl(url: string): string {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._platformStrategy.prepareExternalUrl(url);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and pushes a\n * new item onto the platform's history.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\ngo(path: string, query: string = ''): void {\n    this._platformStrategy.pushState(null, '', path, query);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and replaces\n * the top item on the platform's history stack.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nreplaceState(path: string, query: string = ''): void {\n    this._platformStrategy.replaceState(null, '', path, query);\n  }\n/**\n * Navigates forward in the platform's history.\n * @return {?}\n */\nforward(): void { this._platformStrategy.forward(); }\n/**\n * Navigates back in the platform's history.\n * @return {?}\n */\nback(): void { this._platformStrategy.back(); }\n/**\n * Subscribe to the platform's `popState` events.\n * @param {?} onNext\n * @param {?=} onThrow\n * @param {?=} onReturn\n * @return {?}\n */\nsubscribe(\n      onNext: (value: PopStateEvent) => void, onThrow?: ((exception: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n/**\n * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as\n * is.\n * @param {?} params\n * @return {?}\n */\npublic static normalizeQueryParams(params: string): string {\n    return params && params[0] !== '?' ? '?' + params : params;\n  }\n/**\n * Given 2 parts of a url, join them with a slash if needed.\n * @param {?} start\n * @param {?} end\n * @return {?}\n */\npublic static joinWithSlash(start: string, end: string): string {\n    if (start.length == 0) {\n      return end;\n    }\n    if (end.length == 0) {\n      return start;\n    }\n    let /** @type {?} */ slashes = 0;\n    if (start.endsWith('/')) {\n      slashes++;\n    }\n    if (end.startsWith('/')) {\n      slashes++;\n    }\n    if (slashes == 2) {\n      return start + end.substring(1);\n    }\n    if (slashes == 1) {\n      return start + end;\n    }\n    return start + '/' + end;\n  }\n/**\n * If url has a trailing slash, remove it, otherwise return url as is. This\n * method looks for the first occurence of either #, ?, or the end of the\n * line as `/` characters after any of these should not be replaced.\n * @param {?} url\n * @return {?}\n */\npublic static stripTrailingSlash(url: string): string {\n    const /** @type {?} */ match = url.match(/#|\\?|$/);\n    const /** @type {?} */ pathEndIdx = match && match.index || url.length;\n    const /** @type {?} */ droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n    return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: LocationStrategy, },\n];\n}\n\nfunction Location_tsickle_Closure_declarations() {\n/** @type {?} */\nLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLocation.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._subject;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._baseHref;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._platformStrategy;\n}\n\n/**\n * @param {?} baseHref\n * @param {?} url\n * @return {?}\n */\nfunction _stripBaseHref(baseHref: string, url: string): string {\n  return baseHref && url.startsWith(baseHref) ? url.substring(baseHref.length) : url;\n}\n/**\n * @param {?} url\n * @return {?}\n */\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\nimport {LocationChangeListener} from './platform_location';\n/**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. Angular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n * \n * This is used under the hood of the {\\@link Location} service.\n * \n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n * \n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n * \n * See these two classes for more.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class LocationStrategy {\n/**\n * @abstract\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash?: boolean) {}\n/**\n * @abstract\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHref() {}\n}\n/**\n * The `APP_BASE_HREF` token represents the base href to be used with the\n * {\\@link PathLocationStrategy}.\n * \n * If you're using {\\@link PathLocationStrategy}, you must provide a provider to a string\n * representing the URL prefix that should be preserved when generating and recognizing\n * URLs.\n * \n * ### Example\n * \n * ```typescript\n * import {Component, NgModule} from '\\@angular/core';\n * import {APP_BASE_HREF} from '\\@angular/common';\n * \n * \\@NgModule({ \n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n * \n * \\@stable\n */\nexport const APP_BASE_HREF = new InjectionToken<string>('appBaseHref');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n * \n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n * \n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n * \n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class PlatformLocation {\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHrefFromDOM() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\npathname() {}\n/**\n * @abstract\n * @return {?}\n */\nsearch() {}\n/**\n * @abstract\n * @return {?}\n */\nhash() {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n}\n/**\n * \\@whatItDoes indicates when a location is initialized\n * \\@experimental\n */\nexport const LOCATION_INITIALIZED = new InjectionToken<Promise<any>>('Location Initialized');\n\n/**\n * A serializable version of the event from onPopState or onHashChange\n *\n * @experimental\n */\nexport interface LocationChangeEvent { type: string; }\n\n/**\n * @experimental\n */\nexport interface LocationChangeListener { (e: LocationChangeEvent): any; }\n"], "names": ["stringify", "isListLikeIterable"], "mappings": ";;AgCAA;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,AAAA,MAAA,gBAAA,CAAA;;;;;IAKA,kBANY,GAMZ,GANY;;;;;;IAYZ,UAXY,CAAA,EAAA,EAWZ,GAXY;;;;;;IAiBZ,YAhBY,CAAA,EAAA,EAgBZ,GAhBY;;;;;IAqBZ,QAnBgB,GAmBhB,GAnBgB;;;;;IAwBhB,MAvBgB,GAuBhB,GAvBgB;;;;;IA4BhB,IA3BgB,GA2BhB,GA3BgB;;;;;;;;IAmChB,YAjCY,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAiCZ,GAjCY;;;;;;;;IAyCZ,SAvCY,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAuCZ,GAvCY;;;;;IA4CZ,OA1CY,GA0CZ,GA1CY;;;;;IA+CZ,IA7CY,GA6CZ,GA7CY;CA8CX;;;;;AAKD,AA5CC,MAAA,oBAAA,GAAA,IAAA,cAAA,CAAA,sBAAA,CAAA,CAAA;;ADrDD;;;;;;;AASA,AAEA;;;;;;;;;;;;;;;;;;;AAmBA,AAAA,MAAA,gBAAA,CAAA;;;;;;IAMA,IANY,CAAA,WAAA,EAMZ,GANY;;;;;;IAYZ,kBAXY,CAAA,QAAA,EAWZ,GAXY;;;;;;;;;IAoBZ,SAnBY,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA,WAAA,EAmBZ,GAnBY;;;;;;;;;IA4BZ,YA3BY,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA,WAAA,EA2BZ,GA3BY;;;;;IAgCZ,OA/BY,GA+BZ,GA/BY;;;;;IAoCZ,IAnCY,GAmCZ,GAnCY;;;;;;IAyCZ,UAxCY,CAAA,EAAA,EAwCZ,GAxCY;;;;;IA6CZ,WA5CY,GA4CZ,GA5CY;CA6CX;;;;;;;;;;;;;;;;;;;;;;;AAuBD,AA1CC,MAAA,aAAA,GAAA,IAAA,cAAA,CAAA,aAAA,CAAA,CAAA;;AD/DD;;;;;;;AASA,AAEA,AAQA;;;;;;;;;;;;;;;;;;;;;AAqBA,AAAA,MAAA,QAAA,CAAA;;;;IAgBA,WAAA,CAPG,gBAA6B,EAOhC;;;;QAZA,IAAA,CAAA,QADG,GAAA,IAAA,YAAA,EAAA,CAAA;QAcC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAC7D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,kBAAkB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,KAAzC;YACM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACtB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,EAAE,CAAC,IAAI;aAChB,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ;;;;;IAKH,IALG,CAAA,WAKH,GALG,KAAA,EAKH;QACI,OALO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,iBAAC,CAAiB,IAAC,CAAI,WAAC,CAAW,CAAC,CAAC;KAMjE;;;;;;;IAOH,oBAPG,CAAA,IAAA,EAAA,KAOH,GAPG,EAAA,EAOH;QACI,OAPO,IAAA,CAAK,IAAC,EAAI,IAAK,IAAA,CAAK,SAAC,CAAS,IAAC,GAAM,QAAA,CAAS,oBAAC,CAAoB,KAAC,CAAK,CAAC,CAAC;KAQnF;;;;;;;IAOH,SARG,CAAA,GAAA,EAQH;QACI,OARO,QAAA,CAAS,kBAAC,CAAkB,cAAC,CAAc,IAAC,CAAI,SAAC,EAAU,eAAA,CAAgB,GAAC,CAAG,CAAC,CAAC,CAAC;KAS1F;;;;;;;;;IASH,kBATG,CAAA,GAAA,EASH;QACI,IAAI,GATC,IAAM,GAAA,CAAI,CAAC,CAAC,KAAK,GAAA,EAAK;YAUzB,GAAG,GATG,GAAA,GAAM,GAAA,CAAI;SAUjB;QACD,OATO,IAAA,CAAK,iBAAC,CAAiB,kBAAC,CAAkB,GAAC,CAAG,CAAC;KAUvD;;;;;;;;IAQH,EAVG,CAAA,IAAA,EAAA,KAUH,GAVG,EAAA,EAUH;QACI,IAAI,CAVC,iBAAC,CAAiB,SAAC,CAAS,IAAC,EAAK,EAAA,EAAI,IAAA,EAAM,KAAA,CAAM,CAAC;KAWzD;;;;;;;;IAQH,YAZG,CAAA,IAAA,EAAA,KAYH,GAZG,EAAA,EAYH;QACI,IAAI,CAZC,iBAAC,CAAiB,YAAC,CAAY,IAAC,EAAK,EAAA,EAAI,IAAA,EAAM,KAAA,CAAM,CAAC;KAa5D;;;;;IAKH,OAZG,GAYH,EAZoB,IAAA,CAAK,iBAAC,CAAiB,OAAC,EAAO,CAAE,EAAC;;;;;IAiBtD,IAZG,GAYH,EAZiB,IAAA,CAAK,iBAAC,CAAiB,IAAC,EAAI,CAAE,EAAC;;;;;;;;IAoBhD,SAfG,CAgBG,MAAsC,EAAE,OAAyC,EACjF,QAA4B,EAFlC;QAGI,OAfO,IAAA,CAAK,QAAC,CAAQ,SAAC,CAAS,EAAC,IAAC,EAAK,MAAA,EAAQ,KAAA,EAAO,OAAA,EAAS,QAAA,EAAU,QAAA,EAAS,CAAC,CAAC;KAgBpF;;;;;;;IATA,OAAA,oBAAA,CAAA,MAAA,EAAH;QAiBI,OAhBO,MAAA,IAAU,MAAA,CAAO,CAAC,CAAC,KAAK,GAAA,GAAM,GAAA,GAAM,MAAA,GAAS,MAAA,CAAO;KAiB5D;;;;;;;IAXA,OAAA,aAAA,CAAA,KAAA,EAAA,GAAA,EAAH;QAmBI,IAAI,KAlBC,CAAK,MAAC,IAAS,CAAA,EAAG;YAmBrB,OAlBO,GAAA,CAAI;SAmBZ;QACD,IAAI,GAlBC,CAAG,MAAC,IAAS,CAAA,EAAG;YAmBnB,OAlBO,KAAA,CAAM;SAmBd;QACD,qBAlBI,OAAA,GAAU,CAAA,CAAE;QAmBhB,IAAI,KAlBC,CAAK,QAAC,CAAQ,GAAC,CAAG,EAAE;YAmBvB,OAAO,EAlBC,CAAE;SAmBX;QACD,IAAI,GAlBC,CAAG,UAAC,CAAU,GAAC,CAAG,EAAE;YAmBvB,OAAO,EAlBC,CAAE;SAmBX;QACD,IAAI,OAlBC,IAAU,CAAA,EAAG;YAmBhB,OAlBO,KAAA,GAAQ,GAAA,CAAI,SAAC,CAAS,CAAC,CAAC,CAAC;SAmBjC;QACD,IAAI,OAlBC,IAAU,CAAA,EAAG;YAmBhB,OAlBO,KAAA,GAAQ,GAAA,CAAI;SAmBpB;QACD,OAlBO,KAAA,GAAQ,GAAA,GAAM,GAAA,CAAI;KAmB1B;;;;;;;;IAXA,OAAA,kBAAA,CAAA,GAAA,EAAH;QAoBI,uBAnBM,KAAA,GAAQ,GAAA,CAAI,KAAC,CAAK,QAAC,CAAQ,CAAC;QAapC,uBAZQ,UAAA,GAAa,KAAA,IAAS,KAAA,CAAM,KAAC,IAAQ,GAAA,CAAI,MAAC,CAAM;QAoBtD,uBAnBM,eAAA,GAAkB,UAAA,IAAa,GAAE,CAAG,UAAC,GAAY,CAAA,CAAE,KAAK,GAAA,GAAM,CAAA,GAAI,CAAA,CAAE,CAAC;QAoB3E,OAnBO,GAAA,CAAI,KAAC,CAAK,CAAC,EAAE,eAAA,CAAgB,GAAG,GAAA,CAAI,KAAC,CAAK,UAAC,CAAU,CAAC;KAoB9D;;AAlBI,QAAP,CAAA,UAAO,GAAoC;IAoB3C,EAnBE,IAAA,EAAM,UAAA,EAAW;CAoBlB,CAnBC;;;;AAED,QAAD,CAAA,cAAC,GAAA,MAAA;IAsBD,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;AAGF,AAyBA;;;;;AAKA,SAAA,cAAA,CAnDC,QAAA,EAAA,GAAA,EAmDD;IACE,OA1DO,QAAA,IAAY,GAAA,CAAI,UAAC,CAAU,QAAC,CAAQ,GAAG,GAAA,CAAI,SAAC,CAAS,QAAC,CAAQ,MAAC,CAAM,GAAG,GAAA,CAAI;CA2DpF;;;;;AAKD,SAAA,eAAA,CAtDC,GAAA,EAsDD;IACE,OA7DO,GAAA,CAAI,OAAC,CAAO,eAAC,EAAgB,EAAA,CAAG,CAAC;CA8DzC;;ADzPD;;;;;;;AASA,AAGA,AACA,AACA,AACA;;;;;;;;;;;;;;;;;AAiBA,AAAA,MAAA,oBAGC,SAAA,gBAAA,CAHD;;;;;IAMA,WAAA,CAAc,iBAAmB,EAEzB,SADY,EADpB;QAGI,KAAK,EAAE,CAAC;QAHE,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAAmB;QAFvB,IAAV,CAAA,SAAU,GAAoB,EAAA,CAAG;QAM7B,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC5B;KACF;;;;;IAKH,UAJG,CAAA,EAAA,EAIH;QACI,IAAI,CAJC,iBAAC,CAAiB,UAAC,CAAU,EAAC,CAAE,CAAC;QAKtC,IAAI,CAJC,iBAAC,CAAiB,YAAC,CAAY,EAAC,CAAE,CAAC;KAKzC;;;;IAIH,WANG,GAMH,EAN0B,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;IAWjD,IATG,CAAA,WASH,GATG,KAAA,EASH;;;QAGI,qBATI,IAAA,GAAO,IAAA,CAAK,iBAAC,CAAiB,IAAC,CAAI;QAUvC,IAAI,IATC,IAAO,IAAA;YAAM,IAAA,GAAO,GAAA,CAAI;QAW7B,OATO,IAAA,CAAK,MAAC,GAAQ,CAAA,GAAI,IAAA,CAAK,SAAC,CAAS,CAAC,CAAC,GAAG,IAAA,CAAK;KAUnD;;;;;IAKH,kBAZG,CAAA,QAAA,EAYH;QACI,uBAZM,GAAA,GAAM,QAAA,CAAS,aAAC,CAAa,IAAC,CAAI,SAAC,EAAU,QAAA,CAAS,CAAC;QAa7D,OAZO,GAAA,CAAI,MAAC,GAAQ,CAAA,IAAI,GAAE,GAAK,GAAA,IAAO,GAAA,CAAI;KAa3C;;;;;;;;IAQH,SAlBG,CAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,WAAA,EAkBH;QACI,qBAlBI,GAAA,GAmBA,IAAI,CAlBC,kBAAC,CAAkB,IAAC,GAAM,QAAA,CAAS,oBAAC,CAAoB,WAAC,CAAW,CAAC,CAAC;QAmB/E,IAAI,GAlBC,CAAG,MAAC,IAAS,CAAA,EAAG;YAmBnB,GAAG,GAlBG,IAAA,CAAK,iBAAC,CAAiB,QAAC,CAAQ;SAmBvC;QACD,IAAI,CAlBC,iBAAC,CAAiB,SAAC,CAAS,KAAC,EAAM,KAAA,EAAO,GAAA,CAAI,CAAC;KAmBrD;;;;;;;;IAQH,YAxBG,CAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,WAAA,EAwBH;QACI,qBAxBI,GAAA,GAAM,IAAA,CAAK,kBAAC,CAAkB,IAAC,GAAM,QAAA,CAAS,oBAAC,CAAoB,WAAC,CAAW,CAAC,CAAC;QAyBrF,IAAI,GAxBC,CAAG,MAAC,IAAS,CAAA,EAAG;YAyBnB,GAAG,GAxBG,IAAA,CAAK,iBAAC,CAAiB,QAAC,CAAQ;SAyBvC;QACD,IAAI,CAxBC,iBAAC,CAAiB,YAAC,CAAY,KAAC,EAAM,KAAA,EAAO,GAAA,CAAI,CAAC;KAyBxD;;;;IAIH,OA1BG,GA0BH,EA1BoB,IAAA,CAAK,iBAAC,CAAiB,OAAC,EAAO,CAAE,EAAC;;;;IA8BtD,IA5BG,GA4BH,EA5BiB,IAAA,CAAK,iBAAC,CAAiB,IAAC,EAAI,CAAE,EAAC;;AACzC,oBAAP,CAAA,UAAO,GAAoC;IA6B3C,EA5BE,IAAA,EAAM,UAAA,EAAW;CA6BlB,CA5BC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IA+BD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;CAC/F,CAAC,AAGF,AAYC;;AD3ID;;;;;;;AASA,AAGA,AACA,AACA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,AAAA,MAAA,oBAGC,SAAA,gBAAA,CAHD;;;;;IAMA,WAAA,CACc,iBAAmB,EACzB,IAAO,EAFf;QAGI,KAAK,EAAE,CAAC;QAFE,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAAmB;QAI7B,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;SACpD;QAED,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,MAAM,IAAI,KAAK,CACX,CADV,2GAAA,CACuH,CAAC,CAAC;SACpH;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;;;;;IAKH,UAHG,CAAA,EAAA,EAGH;QACI,IAAI,CAHC,iBAAC,CAAiB,UAAC,CAAU,EAAC,CAAE,CAAC;QAItC,IAAI,CAHC,iBAAC,CAAiB,YAAC,CAAY,EAAC,CAAE,CAAC;KAIzC;;;;IAIH,WALG,GAKH,EAL0B,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;IAUjD,kBARG,CAAA,QAAA,EAQH;QACI,OARO,QAAA,CAAS,aAAC,CAAa,IAAC,CAAI,SAAC,EAAU,QAAA,CAAS,CAAC;KASzD;;;;;IAKH,IAXG,CAAA,WAWH,GAXG,KAAA,EAWH;QACI,uBAXM,QAAA,GAAW,IAAA,CAAK,iBAAC,CAAiB,QAAC;YAYrC,QAAQ,CAXC,oBAAC,CAAoB,IAAC,CAAI,iBAAC,CAAiB,MAAC,CAAM,CAAC;QAYjE,uBAXM,IAAA,GAAO,IAAA,CAAK,iBAAC,CAAiB,IAAC,CAAI;QAYzC,OAXO,IAAA,IAAQ,WAAA,GAAc,CAWjC,EAXiC,QAAI,CAWrC,EAX6C,IAAG,CAWhD,CAXoD,GAAI,QAAA,CAAS;KAY9D;;;;;;;;IAQH,SAjBG,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA,WAAA,EAiBH;QACI,uBAjBM,WAAA,GAAc,IAAA,CAAK,kBAAC,CAAkB,GAAC,GAAK,QAAA,CAAS,oBAAC,CAAoB,WAAC,CAAW,CAAC,CAAC;QAkB9F,IAAI,CAjBC,iBAAC,CAAiB,SAAC,CAAS,KAAC,EAAM,KAAA,EAAO,WAAA,CAAY,CAAC;KAkB7D;;;;;;;;IAQH,YAvBG,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA,WAAA,EAuBH;QACI,uBAvBM,WAAA,GAAc,IAAA,CAAK,kBAAC,CAAkB,GAAC,GAAK,QAAA,CAAS,oBAAC,CAAoB,WAAC,CAAW,CAAC,CAAC;QAwB9F,IAAI,CAvBC,iBAAC,CAAiB,YAAC,CAAY,KAAC,EAAM,KAAA,EAAO,WAAA,CAAY,CAAC;KAwBhE;;;;IAIH,OAzBG,GAyBH,EAzBoB,IAAA,CAAK,iBAAC,CAAiB,OAAC,EAAO,CAAE,EAAC;;;;IA6BtD,IA3BG,GA2BH,EA3BiB,IAAA,CAAK,iBAAC,CAAiB,IAAC,EAAI,CAAE,EAAC;;AACzC,oBAAP,CAAA,UAAO,GAAoC;IA4B3C,EA3BE,IAAA,EAAM,UAAA,EAAW;CA4BlB,CA3BC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IA8BD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;CAC/F,CAAC,AAGF,AAYC;;ADlJD;;;;;;GAMG,AAEH,AACA,AACA,AACA,AACA,AAAkD;;ADZlD;;;;;;;AASA,AACA;;;;AAIA,AAAA,MAAA,cAAA,CAAA;;;;;;IAMA,iBAPiD,CAAA,KAAA,EAOjD,GAPiD;CAAsC;;;;;;;;;;;;AAmBvF,AAAA,SAAA,iBAAA,CACI,KAAS,EAAA,KAAoB,EAAA,cAAA,EADjC;IAEE,qBATI,GAAA,GAAM,CASZ,CAAA,EATY,KAAK,CASjB,CATsB,CAAE;IAWtB,IAAI,KATC,CAAK,OAAC,CAAO,GAAC,CAAG,GAAG,CAAA,CAAE,EAAE;QAU3B,OATO,GAAA,CAAI;KAUZ;IAED,GAAG,GATG,cAAA,CAAe,iBAAC,CAAiB,KAAC,CAAK,CAAC;IAW9C,IAAI,KATC,CAAK,OAAC,CAAO,GAAC,CAAG,GAAG,CAAA,CAAE,EAAE;QAU3B,OATO,GAAA,CAAI;KAUZ;IAED,IAAI,KATC,CAAK,OAAC,CAAO,OAAC,CAAO,GAAG,CAAA,CAAE,EAAE;QAU/B,OATO,OAAA,CAAQ;KAUhB;IAED,MATM,IAAI,KAAA,CAAM,CASlB,mCAAA,EATmB,KAAsC,CASzD,CAAA,CAT8D,CAAG,CAAC;CAUjE;;;;;;AAMD,AAAA,MAAA,oBAPC,SAAA,cAAA,CAOD;;;;IAIA,WAAA,CAVyB,MAAQ,EAUjC;QAVwC,KAAA,EAAA,CAAA;QAAf,IAAzB,CAAA,MAAyB,GAAA,MAAA,CAAQ;KAAO;;;;;IAexC,iBAbG,CAAA,KAAA,EAaH;QACI,uBAbM,MAAA,GAAS,aAAA,CAAc,IAAC,CAAI,MAAC,EAAO,KAAA,CAAM,CAAC;QAejD,QAAQ,MAbC;YAcP,KAbK,MAAA,CAAO,IAAC;gBAcX,OAbO,MAAA,CAAO;YAchB,KAbK,MAAA,CAAO,GAAC;gBAcX,OAbO,KAAA,CAAM;YAcf,KAbK,MAAA,CAAO,GAAC;gBAcX,OAbO,KAAA,CAAM;YAcf,KAbK,MAAA,CAAO,GAAC;gBAcX,OAbO,KAAA,CAAM;YAOnB,KANS,MAAA,CAAO,IAAC;gBAcX,OAbO,MAAA,CAAO;YAchB;gBACE,OAbO,OAAA,CAAQ;SAclB;KACF;;AAZI,oBAAP,CAAA,UAAO,GAAoC;IAc3C,EAbE,IAAA,EAAM,UAAA,EAAW;CAclB,CAbC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IAgBD,EAAC,IAPC,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,EAAA;CAQD,CAAC;AAGF,AAaA,AAAO,IAPG,MAAE,GAAY,EAAA,CAAA;AAQxB,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AAChB,MAAM,CAAC,GAPC,GAAK,CAAA,CAAE;AAQf,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AACf,MAAM,CAAC,GAAG,GAPC,CAAA,CAAI;AAQf,MAAM,CAAC,IAPE,GAAK,CAAC,CAAA;AAQf,MAAM,CAAC,KAPE,GAAM,CAAC,CAAA;AAQhB,MAAM,CAAC,MAPE,CAAK,IAAC,CAAA,GAAA,MAAA,CAAA;AAQf,MAAM,CAAC,MAPE,CAAA,GAAO,CAAA,GAAA,KAAA,CAAA;AAQhB,MAAM,CAAC,MAPE,CAAA,GAAO,CAAA,GAAA,KAAA,CAAA;AAQhB,MAAM,CAAC,MAPE,CAAK,GAAC,CAAA,GAAA,KAAA,CAAA;AAQf,MAAM,CAAC,MAPE,CAAA,IAAO,CAAA,GAAA,MAAA,CAAA;AAQhB,MAAM,CAAC,MAPE,CAAK,KAAC,CAAA,GAAA,OAAA,CAAA;;;;;;;;;AAiBf,AAAA,SAAA,aAAA,CAhCC,MAAA,EAAA,KAAA,EAgCD;;IAEE,IAAI,OAvCO,KAAA,KAAU,QAAA,EAAU;QAwC7B,KAPK,GAhCG,QAAA,mBAAiB,KAAC,GAAM,EAAA,CAAG,CAAC;KAwCrC;IACD,uBAvCM,CAAA,IAAY,KAAS,CAAA,CAAO;IAwClC,uBAvCM,QAAA,GAAW,CAAA,CAAE,QAAC,EAAQ,CAAE,OAAC,CAAO,WAAC,EAAY,EAAA,CAAG,CAAC;IAwCvD,uBAvCM,CAAA,GAAI,IAAA,CAAK,KAAC,CAAK,IAAC,CAAI,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC;IAwClC,uBAvCM,CAAA,GAAI,QAAA,CAAS,MAAC,CAAM;IAwC1B,uBAvCM,CAAA,GAAI,QAAA,CAAS,QAAC,EAAS,EAAA,CAAG,CAAC;IAwCjC,uBAvCM,CAAA,GAAI,QAAA,CAAS,CAAC,CAAC,QAAC,EAAQ,CAAE,OAAC,CAAO,gBAAC,EAAiB,EAAA,CAAG,EAAE,EAAA,CAAG,IAAI,CAAA,CAAE;IAyCxE,uBAvCM,IAAA,GAAO,MAAA,CAAO,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC,WAAC,EAAW,CAAE;IAyChD,QAPO,IAhCE;QAwCP,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCH,IAPG,CAhCE,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/D,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwChC,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,GAAG,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,EAAA;gBAAI,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCxF,IAAI,CAvCC,GAAG,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAAI,OAAO,MAAA,CAAO,IAAC,CAAI;YAwC1F,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCzD,IAAI,CAvCC,GAAG,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAiC3D,EAhCG,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCnC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwC1E,CAPC,GAhCG,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwCjE,OAvCO,MAAA,CAAO,IAAC,CAAI;YAwCrB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAPG,CAhCE,GAAG,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC7F,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC7F,IAAI,CAvCC,GAAG,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,KAAI,CAAE,GAAG,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,CAAE;gBAiC9E,EAhCG,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;oBAiCjE,CAAA,GAhCO,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCnC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAI,EAvCE,CAAC,KAAK,CAAA,CAAE,IAAI,CAAA,GAAI,GAAA,KAAQ,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCpD,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG;gBAwCnF,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAPG,CAhCE,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAiCtE,EAhCO,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCrC,CAPC,GAhCG,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;oBAiCvD,EAhCG,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCvC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1E,IAPG,EAhCG,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCnC,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwChC,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwChC,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,EAAE,CAAC,KAAK,CAAA,CAAE,KAAI,CAAE,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCrE,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCjE,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCjE,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,CAAA;gBAwCzE,CAPC,GAhCG,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,CAAA;gBAwChE,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA,KAAK,CAAE,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA,IAAK,CAAA,KAAM,CAAA,CAAE;gBAwC1C,CAPC,KAhCK,CAAA,IAAK,EAAE,CAAC,GAAG,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,CAAE;gBAwC1D,EAvCE,CAAC,KAAK,CAAA,CAAE,IAAI,EAAE,CAAC,GAAG,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,CAAE;gBAwC/D,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/D,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,EAAA;gBAAI,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCjE,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,EAAA;gBAAI,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC3C,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,EAAA;gBAAI,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC3C,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,KAAI,CAAE,IAAI,CAAA,IAAK,CAAA,IAAK,EAAA,IAAM,CAAA,IAAK,EAAA,IAAM,CAAA,IAAK,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCxF,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/C,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/C,IAAI,CAvCC,KAAK,CAAA;iBAiCL,CAhCC,GAAG,GAAA,KAAQ,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,IAAM,CAAA,GAAI,GAAA,KAAQ,EAAA,CAAG;gBAwCzF,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAPG,EAhCG,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCnC,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAAI,CAvCC,KAAK,CAAA,IAAK,EAAE,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,KAAO,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCxE,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG,IAAI,EAAE,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwClF,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwChC,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA;YAwCH,IAPG,CAhCE,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,IAAC,CAAI;YAwChC,IAAI,CAvCC,CAAC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA,KAAM,EAAE,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1D,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCzE,IAAI,CAvCC,GAAG,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAiC3D,EAhCG,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCnC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAI,EAvCE,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,IAAC,CAAI;YAwCnC,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA;YAwCH,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC/E,CAAC,KAvCK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC5E,OAvCO,MAAA,CAAO,IAAC,CAAI;YAwCrB,IAPG,CAhCE,GAAG,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG,IAAI,CAAA,KAAM,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG;gBAiClF,EAhCG,CAAC,KAAK,CAAA,CAAE,IAAI,CAAA,GAAI,EAAA,KAAO,CAAA;gBAwC3B,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/D,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC/B,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC3E,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAPG,CAhCE,GAAG,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAAI,OAAO,MAAA,CAAO,IAAC,CAAI;YAwC1F,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAPG,CAhCE,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwCrE,EAvCE,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCnC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAPG,CAhCE,KAAK,CAAA,IAAK,EAAE,CAAC,KAAK,CAAA,CAAE,IAAI,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwCnF,CAPC,KAhCK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwCrE,CAPC,KAhCK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC5E,OAvCO,MAAA,CAAO,IAAC,CAAI;YAwCrB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAPG,CAhCE,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,EAAE,CAAC,KAAK,CAAA,CAAE;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC7E,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAAI,EAvCE,CAAC,KAAK,CAAA,CAAE,IAAI,CAAA,KAAM,CAAA;gBAwCpB,EAvCE,CAAC,KAAK,CAAA,CAAE,IAAI,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC9E,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA,CAAK;QAiCV,KAhCK,IAAA;YAiCP,IAAA,CAhCS,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAAK,EAAE,CAAC,GAAG,GAAA,KAAQ,EAAA,CAAG;gBAAE,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCpE,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwCrE,EAvCE,CAAC,GAAG,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA,CAAG;gBAwCnC,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA;gBAwCtB,CAAC,KAvCK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,EAAA,CAAG,IAAI,CAAA,GAAI,EAAA,IAAM,CAAA,IAAK,CAAA,GAAI,EAAA,IAAM,CAAA;gBAwCrE,CAAC,KAvCK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,EAAA,IAAM,CAAA,GAAI,GAAA,IAAO,EAAA;gBAwC5E,OAvCO,MAAA,CAAO,IAAC,CAAI;YAwCrB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,EAAA;gBAAI,OAAO,MAAA,CAAO,GAAC,CAAG;YAwChE,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,KAAM,CAAA,IAAK,CAAA,KAAM,CAAA,IAAK,CAAA,KAAM,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwChE,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,IAAA;YAwCH,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwChD,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,CAAA;gBAAG,OAAO,MAAA,CAAO,GAAC,CAAG;YAwChD,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,GAAA,KAAQ,IAAA,CAAK,KAAC,CAAK,CAAC,GAAG,GAAA,CAAI,IAAI,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,CAAA,GAAI,GAAA,IAAO,CAAA,IAAK,EAAE,CAAC,KAAK,CAAA,CAAE;gBAwC1F,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;QAwCtB,KAvCK,KAAA;YAwCH,IAAI,CAvCC,KAAK,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA,IAAK,CAAA,KAAM,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,IAAI,CAAA,IAAK,EAAA,IAAM,CAAA,IAAK,EAAA;gBAwCpF,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,OAvCO,MAAA,CAAO,KAAC,CAAK;;;;QA2CtB;YACE,OAvCO,MAAA,CAAO,KAAC,CAAK;KAwCvB;CACF;;ADvbD;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,AAAA,MAAA,OAAA,CAAA;;;;;;;IAWA,WAAA,CAHc,gBAAkB,EAAyB,gBAAkB,EAC7D,KAAO,EAAoB,SAAW,EAEpD;QAHc,IAAd,CAAA,gBAAc,GAAA,gBAAA,CAAkB;QAAyB,IAAzD,CAAA,gBAAyD,GAAA,gBAAA,CAAkB;QAC7D,IAAd,CAAA,KAAc,GAAA,KAAA,CAAO;QAAoB,IAAzC,CAAA,SAAyC,GAAA,SAAA,CAAW;QAL1C,IAAV,CAAA,eAAU,GAA4B,EAAA,CAAG;KAKoB;;;;;IAW7D,IARG,KAAA,CAAA,CAAA,EAQH;QACI,IAAI,CARC,oBAAC,CAAoB,IAAC,CAAI,CAAC;QAShC,IAAI,CARC,eAAC,GAAiB,OAAO,CAAA,KAAM,QAAA,GAAW,CAAA,CAAE,KAAC,CAAK,KAAC,CAAK,GAAG,EAAA,CAAG;QASnE,IAAI,CARC,oBAAC,CAAoB,KAAC,CAAK,CAAC;QASjC,IAAI,CARC,aAAC,CAAa,IAAC,CAAI,SAAC,EAAU,KAAA,CAAM,CAAC;KAS3C;;;;;IAKH,IAVG,OAAA,CAAA,CAAA,EAUH;QACI,IAAI,CAVC,eAAC,CAAe,IAAC,CAAI,SAAC,CAAS,CAAC;QAYrC,IAAI,CAVC,eAAC,GAAiB,IAAA,CAAK;QAW5B,IAAI,CAVC,eAAC,GAAiB,IAAA,CAAK;QAY5B,IAAI,CAVC,SAAC,GAAW,OAAO,CAAA,KAAM,QAAA,GAAW,CAAA,CAAE,KAAC,CAAK,KAAC,CAAK,GAAG,CAAA,CAAE;QAY5D,IAAI,IAVC,CAAI,SAAC,EAAU;YAWlB,IAAIC,mBAVC,CAAkB,IAAC,CAAI,SAAC,CAAS,EAAE;gBAWtC,IAAI,CAVC,eAAC,GAAiB,IAAA,CAAK,gBAAC,CAAgB,IAAC,CAAI,IAAC,CAAI,SAAC,CAAS,CAAC,MAAC,EAAM,CAAE;aAW5E;iBAVM;gBAWL,IAAI,CAVC,eAAC,GAAiB,IAAA,CAAK,gBAAC,CAAgB,IAAC,CAAI,IAAC,CAAI,SAAC,CAAS,CAAC,MAAC,EAAM,CAAE;aAW5E;SACF;KACF;;;;IAIH,SAZG,GAYH;QACI,IAAI,IAZC,CAAI,eAAC,EAAgB;YAaxB,uBAZM,eAAA,GAAkB,IAAA,CAAK,eAAC,CAAe,IAAC,mBAAI,IAAC,CAAI,SAAoB,EAAE,CAAC;YAa9E,IAAI,eAZC,EAAgB;gBAanB,IAAI,CAZC,qBAAC,CAAqB,eAAC,CAAe,CAAC;aAa7C;SACF;aAZM,IAAA,IAAK,CAAI,eAAC,EAAgB;YAa/B,uBAZM,eAAA,GAAkB,IAAA,CAAK,eAAC,CAAe,IAAC,mBAAI,IAAC,CAAI,SAA8B,EAAC,CAAC;YAavF,IAAI,eAZC,EAAgB;gBAanB,IAAI,CAZC,qBAAC,CAAqB,eAAC,CAAe,CAAC;aAa7C;SACF;KACF;;;;;IAVA,eAAA,CAAA,WAAA,EAAH;QAgBI,IAAI,CAfC,aAAC,CAAa,WAAC,EAAY,IAAA,CAAK,CAAC;QAgBtC,IAAI,CAfC,oBAAC,CAAoB,KAAC,CAAK,CAAC;KAgBlC;;;;;IAbA,qBAAA,CAAA,OAAA,EAAH;QAmBI,OAAO,CAlBC,gBAAC,CAAgB,CAAC,MAAC,KAAU,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,GAAC,EAAI,MAAA,CAAO,YAAC,CAAY,CAAC,CAAC;QAmBzF,OAAO,CAlBC,kBAAC,CAAkB,CAAC,MAAC,KAAU,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,GAAC,EAAI,MAAA,CAAO,YAAC,CAAY,CAAC,CAAC;QAmB3F,OAAO,CAlBC,kBAAC,CAAkB,CAAC,MAAC,KAkBjC;YACM,IAAI,MAlBC,CAAM,aAAC,EAAc;gBAmBxB,IAAI,CAlBC,YAAC,CAAY,MAAC,CAAM,GAAC,EAAI,KAAA,CAAM,CAAC;aAmBtC;SACF,CAlBC,CAAC;KAmBJ;;;;;IAhBA,qBAAA,CAAA,OAAA,EAAH;QAsBI,OAAO,CArBC,gBAAC,CAAgB,CAAC,MAAC,KAqB/B;YACM,IAAI,OArBO,MAAA,CAAO,IAAC,KAAQ,QAAA,EAAU;gBAsBnC,IAAI,CArBC,YAAC,CAAY,MAAC,CAAM,IAAC,EAAK,IAAA,CAAK,CAAC;aAsBtC;iBArBM;gBAsBL,MArBM,IAAI,KAAA,CAsBN,CADZ,8DAAA,EAC6ED,UArBC,CAAS,MAAC,CAAM,IAAC,CAAI,CAoBnG,CApBoG,CAAE,CAAC;aAsBhG;SACF,CArBC,CAAC;QAuBH,OAAO,CArBC,kBAAC,CAAkB,CAAC,MAAC,KAAU,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,IAAC,EAAK,KAAA,CAAM,CAAC,CAAC;KAsB/E;;;;;IAnBA,oBAAA,CAAA,SAAA,EAAH;QAyBI,IAAI,CAxBC,eAAC,CAAe,OAAC,CAAO,KAAC,IAAQ,IAAA,CAAK,YAAC,CAAY,KAAC,EAAM,CAAA,SAAE,CAAS,CAAC,CAAC;KAyB7E;;;;;;IAtBA,aAAA,CA6BG,WAAwD,EAAE,SAAkB,EA7BlF;QA8BI,IAAI,WA5BC,EAAY;YA6Bf,IAAI,KA5BC,CAAK,OAAC,CAAO,WAAC,CAAW,IAAI,WAAA,YAAuB,GAAA,EAAK;gBA6B5D,EA5BM,WAAC,GAAY,OAAC,CAAO,CAAC,KAAO,KAAW,IAAA,CAAK,YAAC,CAAY,KAAC,EAAM,CAAA,SAAE,CAAS,CAAC,CAAC;aA6BrF;iBA5BM;gBAeX,MAAA,CAda,IAAC,CAAI,WAAC,CAAW,CAAC,OAAC,CAAO,KAAC,IAc1C;oBAeU,IAAI,WA5BC,CAAW,KAAC,CAAK,IAAI,IAAA;wBAAM,IAAA,CAAK,YAAC,CAAY,KAAC,EAAM,CAAA,SAAE,CAAS,CAAC;iBA6BtE,CA5BC,CAAC;aA6BJ;SACF;KACF;;;;;;IA1BA,YAAA,CAAA,KAAA,EAAA,OAAA,EAAH;QAiCI,KAAK,GAhCG,KAAA,CAAM,IAAC,EAAI,CAAE;QAiCrB,IAAI,KAhCC,EAAM;YAiCT,KAAK,CAhCC,KAAC,CAAK,MAAC,CAAM,CAAC,OAAC,CAiCjB,KAAK,IADf,EA/BqB,IAAA,CAAK,SAAC,CAAS,eAAC,CAAe,IAAC,CAAI,KAAC,CAAK,aAAC,EAAc,KAAA,EAAO,CAAA,CAAE,OAAC,CAAO,CAAC,EAAC,CAAE,CAAC;SAiC/F;KACF;;AA/BI,OAAP,CAAA,UAAO,GAAoC;IAiC3C,EAhCE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,WAAA,EAAY,EAAC,EAAG;CAiCpD,CAhCC;;;;AAED,OAAD,CAAA,cAAC,GAAA,MAAA;IAmCD,EAAC,IAAI,EAAE,eAAe,GAAG;IACzB,EAAC,IAAI,EAAE,eAAe,GAAG;IACzB,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,QAAQ,GAAG;CACjB,CAAC;AAjCK,OAAP,CAAA,cAAO,GAAyD;IAmChE,OAAO,EAlCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,OAAE,EAAO,EAAG,EAAE;IAmC7C,SAAS,EAlCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAmC3B,CAlCC,AAqCF,AA0BC;;ADzND;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,AAAA,MAAA,iBAAA,CAAA;;;;IAUA,WAAA,CACsB,iBAAmB,EADzC;QACsB,IAAtB,CAAA,iBAAsB,GAAA,iBAAA,CAAmB;QAH/B,IAAV,CAAA,aAAU,GAAwC,IAAA,CAAK;QAC7C,IAAV,CAAA,UAAU,GAAoC,IAAA,CAAK;KAEO;;;;;IAI1D,WAFG,CAAA,OAAA,EAEH;QACI,IAAI,CAFC,iBAAC,CAAiB,KAAC,EAAK,CAAE;QAG/B,IAAI,CAFC,aAAC,GAAe,IAAA,CAAK;QAI1B,IAAI,IAFC,CAAI,iBAAC,EAAkB;YAG1B,uBAFM,UAAA,GAAa,IAAA,CAAK,yBAAC,IAA4B,IAAA,CAAK,iBAAC,CAAiB,cAAC,CAAc;YAI3F,IAAI,OAFC,CAAO,kCAAC,CAAkC,EAAE;gBAG/C,IAAI,IAFC,CAAI,UAAC;oBAAW,IAAA,CAAK,UAAC,CAAU,OAAC,EAAO,CAAE;gBAI/C,IAAI,IAFC,CAAI,gCAAC,EAAiC;oBAGzC,uBAFM,YAAA,GAAe,UAAA,CAAW,GAAC,CAAG,WAAC,CAAW,CAAC;oBAGjD,IAAI,CAFC,UAAC,GAAY,IAAA,CAAK,gCAAC,CAAgC,MAAC,CAAM,YAAC,CAAY,QAAC,CAAQ,CAAC;iBAGvF;qBAFM;oBAGL,IAAI,CAFC,UAAC,GAAY,IAAA,CAAK;iBAGxB;aACF;YAED,uBAFM,wBAAA,GAA2B,IAAA,CAAK,UAAC,GAAY,IAAA,CAAK,UAAC,CAAU,wBAAC;gBAGjB,UAAU,CAFC,GAAC,CAAG,wBAAC,CAAwB,CAAC;YAI5F,uBAFM,gBAAA,GAGF,wBAAwB,CAFC,uBAAC,CAAuB,IAAC,CAAI,iBAAC,CAAiB,CAAC;YAI7E,IAAI,CAFC,aAAC,GAAe,IAAA,CAAK,iBAAC,CAAiB,eAAC,CAGzC,gBAAgB,EAFE,IAAA,CAAK,iBAAC,CAAiB,MAAC,EAAO,UAAA,EAGjD,IAAI,CAFC,wBAAC,CAAwB,CAAC;SAGpC;KACF;;;;IAIH,WAJG,GAIH;QACI,IAAI,IAJC,CAAI,UAAC;YAAW,IAAA,CAAK,UAAC,CAAU,OAAC,EAAO,CAAE;KAKhD;;AAHI,iBAAP,CAAA,UAAO,GAAoC;IAK3C,EAJE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,qBAAA,EAAsB,EAAC,EAAG;CAK9D,CAJC;;;;AAED,iBAAD,CAAA,cAAC,GAAA,MAAA;IAOD,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;AALK,iBAAP,CAAA,cAAO,GAAyD;IAOhE,mBAAmB,EANE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAOtC,2BAA2B,EANE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAO9C,0BAA0B,EANE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAO7C,kCAAkC,EANE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAOpD,CANC,AASF,AAwBC;;AD3JD;;;;;;;AASA,AACA;;;AAGA,AAAA,MAAA,cAAA,CAAA;;;;;;;IAOA,WAAA,CALa,SAAW,EAAU,OAAsB,EAAS,KAAO,EAC3D,KAAO,EAIpB;QALa,IAAb,CAAA,SAAa,GAAA,SAAA,CAAW;QAAU,IAAlC,CAAA,OAAkC,GAAA,OAAA,CAAsB;QAAS,IAAjE,CAAA,KAAiE,GAAA,KAAA,CAAO;QAC3D,IAAb,CAAA,KAAa,GAAA,KAAA,CAAO;KAAO;;;;IAY3B,IAVG,KAAA,GAUH,EAVyB,OAAO,IAAA,CAAK,KAAC,KAAS,CAAA,CAAE,EAAC;;;;IAclD,IAZG,IAAA,GAYH,EAZwB,OAAO,IAAA,CAAK,KAAC,KAAS,IAAA,CAAK,KAAC,GAAO,CAAA,CAAE,EAAC;;;;IAgB9D,IAdG,IAAA,GAcH,EAdwB,OAAO,IAAA,CAAK,KAAC,GAAO,CAAA,KAAM,CAAA,CAAE,EAAC;;;;IAkBrD,IAhBG,GAAA,GAgBH,EAhBuB,OAAO,CAAA,IAAE,CAAI,IAAC,CAAI,EAAC;CAiBzC;AAED,AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,AAAA,MAAA,OAAA,CAAA;;;;;;IA4BA,WAAA,CAjCc,cAAgB,EAA0B,SAAyC,EACnF,QAAU,EAgCxB;QAjCc,IAAd,CAAA,cAAc,GAAA,cAAA,CAAgB;QAA0B,IAAxD,CAAA,SAAwD,GAAA,SAAA,CAAyC;QACnF,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QALd,IAAV,CAAA,OAAU,GAAkC,IAAA,CAAK;KAKT;;;;;IAUxC,IA7BG,YAAA,CAAA,EAAA,EA6BH;QACI,IAAI,SA7BC,EAAS,IAAK,EAAA,IAAM,IAAA,IAAQ,OAAO,EAAA,KAAO,UAAA,EAAY;;YA+BzD,IAAqB,CA7BZ,OAAC,MAAe,OAAC,CAAO,IAAC,CAAA,EAAK;gBA8BrC,OAAO,CA7BC,IAAC,CA8BL,CADZ,yCAAA,EACwD,IA7BC,CAAI,SAAC,CAAS,EAAC,CAAE,CA4B1E,EAAA,CA5B2E;oBA8B/D,CAAZ,sHAAA,CAAoI,CA7BC,CAAC;aA8B/H;SACF;QACD,IAAI,CA7BC,UAAC,GAAY,EAAA,CAAG;KA8BtB;;;;IAIH,IA/BG,YAAA,GA+BH,EA/B2C,OAAO,IAAA,CAAK,UAAC,CAAU,EAAC;;;;;IA+CnE,IArCG,aAAA,CAAA,KAAA,EAqCH;;;;QAII,IAAI,KArCC,EAAM;YAsCT,IAAI,CArCC,SAAC,GAAW,KAAA,CAAM;SAsCxB;KACF;;;;;IAKH,WAxCG,CAAA,OAAA,EAwCH;QACI,IAAI,SAxCC,IAAY,OAAA,EAAS;;YA0CxB,uBAxCM,KAAA,GAAQ,OAAA,CAAQ,SAAC,CAAS,CAAC,YAAC,CAAY;YAyC9C,IAAI,CAxCC,IAAC,CAAI,OAAC,IAAU,KAAA,EAAO;gBAyC1B,IAxCI;oBAyCF,IAAI,CAxCC,OAAC,GAAS,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,KAAC,CAAK,CAAC,MAAC,CAAM,IAAC,CAAI,YAAC,CAAY,CAAC;iBAyCpE;gBAxCC,OAAA,CAAQ,EAAE;oBAyCV,MAxCM,IAAI,KAAA,CAyCN,CADd,wCAAA,EACyD,KAxCC,CAuC1D,WAAA,EAvC+D,uBAAc,CAAuB,KAAC,CAAK,CAuC1G,2DAAA,CAvC2G,CAA6D,CAAC;iBAyChK;aACF;SACF;KACF;;;;IAIH,SA1CG,GA0CH;QACI,IAAI,IA1CC,CAAI,OAAC,EAAQ;YA2ChB,uBA1CM,OAAA,GAAU,IAAA,CAAK,OAAC,CAAO,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;YA2ChD,IAAI,OA1CC;gBAAQ,IAAA,CAAK,aAAC,CAAa,OAAC,CAAO,CAAC;SA2C1C;KACF;;;;;IAxCA,aAAA,CAAA,OAAA,EAAH;QA8CI,uBA7CM,YAAA,GAAqC,EAAA,CAAG;QA8C9C,OAAO,CA7CC,gBAAC,CA8CL,CAAC,IA7C+B,EAAE,qBAAuB,EAAQ,YAAc,KA4CvF;YAEU,IAAI,IA7CC,CAAI,aAAC,IAAgB,IAAA,EAAM;gBA8C9B,uBA7CM,IAAA,GAAO,IAAA,CAAK,cAAC,CAAc,kBAAC,CA8C9B,IAAI,CA7CC,SAAC,EAAU,IAAI,cAAA,oBAAiB,IAAE,IAAO,IAAA,CAAK,OAAC,EAAQ,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,EAAE,YAAA,CAAa,CAAC;gBA8CvF,uBA7CM,KAAA,GAAQ,IAAI,eAAA,CAAkB,IAAE,EAAK,IAAA,CAAK,CAAC;gBA8CjD,YAAY,CA7CC,IAAC,CAAI,KAAC,CAAK,CAAC;aA8C1B;iBA7CM,IAAA,YAAK,IAAe,IAAA,EAAM;gBA8C/B,IAAI,CA7CC,cAAC,CAAc,MAAC,CAAM,qBAAC,CAAqB,CAAC;aA8CnD;iBA7CM;gBA8CL,uBA7CM,IAAA,KAAO,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,qBAAC,CAAqB,EAAA,CAAG;gBAgCxE,IAAA,CA/Be,cAAC,CAAc,IAAC,CAAI,IAAC,EAAK,YAAA,CAAa,CAAC;gBA8C7C,uBA7CM,KAAA,GAAQ,IAAI,eAAA,CAAgB,IAAC,oBAAyC,IAAC,EAAI,CAAC;gBA8ClF,YAAY,CA7CC,IAAC,CAAI,KAAC,CAAK,CAAC;aA8C1B;SACF,CA7CC,CAAC;QA+CP,KAAK,qBA7CI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,YAAA,CAAa,MAAC,EAAO,CAAA,EAAE,EAAG;YA8C5C,IAAI,CA7CC,cAAC,CAAc,YAAC,CAAY,CAAC,CAAC,CAAC,IAAC,EAAK,YAAA,CAAa,CAAC,CAAC,CAAC,MAAC,CAAM,CAAC;SA8CnE;QAED,KAdG,qBA/BM,CAAA,GAAI,CAAA,mBAAG,IAAA,GAAO,IAAA,CAAK,cAAC,CAAc,MAAC,EAAO,CAAA,GAAI,IAAA,EAAM,CAAA,EAAE,EAAG;YA8ChE,uBA7CM,OAAA,IAA8C,IAAC,CAAI,cAAC,CAAc,GAAC,CAAG,CAAC,CAAC,CAAA,CAAC;YA8C/E,OAdC,CA/BO,OAAC,CAAO,KAAC,GAAO,CAAA,CAAE;YA8C1B,OAdG,CA/BK,OAAC,CAAO,KAAC,GAAO,IAAA,CAAK;SAgCjC;QAgBE,OAAO,CA7CC,qBAAC,CAAqB,CAAC,MAAQ,KA6C3C;YACM,uBA7CM,OAAA,IACkC,IAAC,CAAI,cAAC,CAAc,GAAC,CAAG,MAAC,CAAM,YAAC,CAAY,CAAA,CAAC;YA8CrF,OAAO,CA7CC,OAAC,CAAO,SAAC,GAAW,MAAA,CAAO,IAAC,CAAI;SA8CzC,CA7CC,CAAC;KA8CJ;;;;;;IA3CA,cAAA,CAkDG,IAAwC,EAAE,MAAiC,EAlDjF;QAmDI,IAAI,CAjDC,OAAC,CAAO,SAAC,GAAW,MAAA,CAAO,IAAC,CAAI;KAkDtC;;AAhDI,OAAP,CAAA,UAAO,GAAoC;IAkD3C,EAjDE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,kBAAA,EAAmB,EAAC,EAAG;CAkD3D,CAjDC;;;;AAED,OAAD,CAAA,cAAC,GAAA,MAAA;IAoDD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,WAAW,GAAG;IACrB,EAAC,IAAI,EAAE,eAAe,GAAG;CACxB,CAAC;AAlDK,OAAP,CAAA,cAAO,GAAyD;IAoDhE,SAAS,EAnDE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAoD5B,cAAc,EAnDE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAoDjC,eAAe,EAnDE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAoDjC,CAnDC;AAsDF,AAwBA,MAAA,eAAA,CAAA;;;;;IAKA,WAAA,CA/EqB,MAAQ,EAAY,IAAwC,EA+EjF;QA/EqB,IAArB,CAAA,MAAqB,GAAA,MAAA,CAAQ;QAAY,IAAzC,CAAA,IAAyC,GAAA,IAAA,CAAwC;KAAC;CAiFjF;AAED,AAYA;;;AAGA,AAvFC,MAAA,KAAA,GAAA,OAAA,CAAA;;;;;AA4FD,AAAA,SAAA,uBAAA,CA1FC,IAAA,EA0FD;IACE,OAxGO,IAAA,CAAK,MAAC,CAAM,IAAI,OAAO,IAAA,CAAK;CAyGpC;;AD7TD;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA,AAAA,MAAA,IAAA,CAAA;;;;;IAUA,WAAA,CADsB,cAAgB,EAAkB,WAAqC,EAC7F;QADsB,IAAtB,CAAA,cAAsB,GAAA,cAAA,CAAgB;QAN5B,IAAV,CAAA,QAAU,GAAwB,IAAI,WAAA,EAAY,CAAE;QAC1C,IAAV,CAAA,gBAAU,GAAkD,IAAA,CAAK;QACvD,IAAV,CAAA,gBAAU,GAAkD,IAAA,CAAK;QACvD,IAAV,CAAA,YAAU,GAAkD,IAAA,CAAK;QACvD,IAAV,CAAA,YAAU,GAAkD,IAAA,CAAK;QAI7D,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;KACrC;;;;;IAKH,IAHG,IAAA,CAAA,SAAA,EAGH;QACI,IAAI,CAHC,QAAC,CAAQ,SAAC,GAAW,IAAA,CAAK,QAAC,CAAQ,IAAC,GAAM,SAAA,CAAU;QAIzD,IAAI,CAHC,WAAC,EAAW,CAAE;KAIpB;;;;;IAKH,IALG,QAAA,CAAA,WAAA,EAKH;QACI,IAAI,CALC,gBAAC,GAAkB,WAAA,CAAY;QAMpC,IAAI,CALC,YAAC,GAAc,IAAA,CAAK;QAMzB,IAAI,CALC,WAAC,EAAW,CAAE;KAMpB;;;;;IAKH,IAPG,QAAA,CAAA,WAAA,EAOH;QACI,IAAI,CAPC,gBAAC,GAAkB,WAAA,CAAY;QAQpC,IAAI,CAPC,YAAC,GAAc,IAAA,CAAK;QAQzB,IAAI,CAPC,WAAC,EAAW,CAAE;KAQpB;;;;IALA,WAAA,GAAH;QAUI,IAAI,IATC,CAAI,QAAC,CAAQ,SAAC,EAAU;YAU3B,IAAI,CATC,IAAC,CAAI,YAAC,EAAa;gBAUtB,IAAI,CATC,cAAC,CAAc,KAAC,EAAK,CAAE;gBAU5B,IAAI,CATC,YAAC,GAAc,IAAA,CAAK;gBAUzB,IAAI,IATC,CAAI,gBAAC,EAAiB;oBAUzB,IAAI,CATC,YAAC;wBAUF,IAAI,CATC,cAAC,CAAc,kBAAC,CAAkB,IAAC,CAAI,gBAAC,EAAiB,IAAA,CAAK,QAAC,CAAQ,CAAC;iBAUlF;aACF;SACF;aATM;YAUL,IAAI,CATC,IAAC,CAAI,YAAC,EAAa;gBAUtB,IAAI,CATC,cAAC,CAAc,KAAC,EAAK,CAAE;gBAU5B,IAAI,CATC,YAAC,GAAc,IAAA,CAAK;gBAUzB,IAAI,IATC,CAAI,gBAAC,EAAiB;oBAUzB,IAAI,CATC,YAAC;wBAUF,IAAI,CATC,cAAC,CAAc,kBAAC,CAAkB,IAAC,CAAI,gBAAC,EAAiB,IAAA,CAAK,QAAC,CAAQ,CAAC;iBAUlF;aACF;SACF;KACF;;AARI,IAAP,CAAA,UAAO,GAAoC;IAU3C,EATE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,QAAA,EAAS,EAAC,EAAG;CAUjD,CATC;;;;AAED,IAAD,CAAA,cAAC,GAAA,MAAA;IAYD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,WAAW,GAAG;CACpB,CAAC;AAVK,IAAP,CAAA,cAAO,GAAyD;IAYhE,MAAM,EAXE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAYzB,UAbE,EAEU,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAY7B,UAAU,EAXE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAY5B,CAXC;AAcF,AAwBA;;;AAGA,AAAA,MAAA,WAAA,CAAA;IAAA,WAAA,GAAA;QAlCS,IAAT,CAAA,SAAS,GAAiB,IAAA,CAAK;QACtB,IAAT,CAAA,IAAS,GAAY,IAAA,CAAK;KAoCzB;CAAA,AAED,AAKC;;ADzND;;;;;;;AASA,AACA,AAAA,MAAA,UAAA,CAAA;;;;;IAMA,WAAA,CAFc,iBAAmB,EAA0B,YAAiC,EAE5F;QAFc,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAAmB;QAA0B,IAA3D,CAAA,YAA2D,GAAA,YAAA,CAAiC;QAHlF,IAAV,CAAA,QAAU,GAAW,KAAA,CAAM;KAGkE;;;;IAQ7F,MANG,GAMH;QACI,IAAI,CANC,QAAC,GAAU,IAAA,CAAK;QAOrB,IAAI,CANC,iBAAC,CAAiB,kBAAC,CAAkB,IAAC,CAAI,YAAC,CAAY,CAAC;KAO9D;;;;IAIH,OARG,GAQH;QACI,IAAI,CARC,QAAC,GAAU,KAAA,CAAM;QAStB,IAAI,CARC,iBAAC,CAAiB,KAAC,EAAK,CAAE;KAShC;;;;;IAKH,YAXG,CAAA,OAAA,EAWH;QACI,IAAI,OAXC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAY7B,IAAI,CAXC,MAAC,EAAM,CAAE;SAYf;aAXM,IAAA,CAAK,OAAC,IAAU,IAAA,CAAK,QAAC,EAAS;YAYpC,IAAI,CAXC,OAAC,EAAO,CAAE;SAYhB;KACF;CACF;AAED,AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,AAAA,MAAA,QAAA,CAAA;IAAA,WAAA,GAAA;QAjBU,IAAV,CAAA,YAAU,GAAe,KAAA,CAAM;QACrB,IAAV,CAAA,UAAU,GAAa,CAAA,CAAE;QACf,IAAV,CAAA,mBAAU,GAAsB,CAAA,CAAE;QACxB,IAAV,CAAA,iBAAU,GAAoB,KAAA,CAAM;KAuFnC;;;;;IA9DD,IArBG,QAAA,CAAA,QAAA,EAqBH;QACI,IAAI,CArBC,SAAC,GAAW,QAAA,CAAS;QAsB1B,IAAI,IArBC,CAAI,UAAC,KAAc,CAAA,EAAG;YAsBzB,IAAI,CArBC,mBAAC,CAAmB,IAAC,CAAI,CAAC;SAsBhC;KACF;;;;;IAKH,QAvBG,GAuBH,EAvBuB,OAAO,IAAA,CAAK,UAAC,EAAU,CAAE,EAAC;;;;;;IA6BjD,WA1BG,CAAA,IAAA,EA0BH;QACI,IAAI,CA1BC,IAAC,CAAI,aAAC,EAAc;YA2BvB,IAAI,CA1BC,aAAC,GAAe,EAAA,CAAG;SA2BzB;QACD,IAAI,CA1BC,aAAC,CAAa,IAAC,CAAI,IAAC,CAAI,CAAC;KA2B/B;;;;;;IAMH,UA7BG,CAAA,KAAA,EA6BH;QACI,uBA7BM,OAAA,GAAU,KAAA,IAAS,IAAA,CAAK,SAAC,CAAS;QA8BxC,IAAI,CA7BC,iBAAC,GAAmB,IAAA,CAAK,iBAAC,IAAoB,OAAA,CAAQ;QA8B3D,IAAI,CA7BC,mBAAC,EAAmB,CAAE;QA8B3B,IAAI,IA7BC,CAAI,mBAAC,KAAuB,IAAA,CAAK,UAAC,EAAW;YA8BhD,IAAI,CA7BC,mBAAC,CAAmB,CAAC,IAAC,CAAI,iBAAC,CAAiB,CAAC;YA8BlD,IAAI,CA7BC,mBAAC,GAAqB,CAAA,CAAE;YA8B7B,IAAI,CA7BC,iBAAC,GAAmB,KAAA,CAAM;SA8BhC;QACD,OA7BO,OAAA,CAAQ;KA8BhB;;;;;IA3BA,mBAAA,CAAA,UAAA,EAAH;QAiCI,IAAI,IAhCC,CAAI,aAAC,IAAgB,UAAA,KAAe,IAAA,CAAK,YAAC,EAAa;YAiC1D,IAAI,CAhCC,YAAC,GAAc,UAAA,CAAW;YAiC/B,KAAK,qBAhCI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,IAAA,CAAK,aAAC,CAAa,MAAC,EAAO,CAAA,EAAE,EAAG;gBAiClD,uBAhCM,WAAA,GAAc,IAAA,CAAK,aAAC,CAAa,CAAC,CAAC,CAAC;gBAiC1C,WAAW,CAhCC,YAAC,CAAY,UAAC,CAAU,CAAC;aAiCtC;SACF;KACF;;AA/BI,QAAP,CAAA,UAAO,GAAoC;IAiC3C,EAhCE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,YAAA,EAAa,EAAC,EAAG;CAiCrD,CAhCC;;;;AAED,QAAD,CAAA,cAAC,GAAA,MAAA,EAmCA,CAAC;AAjCK,QAAP,CAAA,cAAO,GAAyD;IAmChE,UAAU,EAlCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAmC5B,CAlCC;AAqCF,AAwBA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAA,MAAA,YAAA,CAAA;;;;;;IAUA,WAAA,CACM,aA7De,EAAkB,WAAgC,EACxD,QAAU,EA2DzB;QA3De,IAAf,CAAA,QAAe,GAAA,QAAA,CAAU;QA8DrB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;KACzD;;;;IAIH,SA/DG,GA+DH,EA/DgB,IAAA,CAAK,KAAC,CAAK,YAAC,CAAY,IAAC,CAAI,QAAC,CAAQ,UAAC,CAAU,IAAC,CAAI,YAAC,CAAY,CAAC,CAAC,EAAC;;AAC/E,YAAP,CAAA,UAAO,GAAoC;IAgE3C,EA/DE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,gBAAA,EAAiB,EAAC,EAAG;CAgEzD,CA/DC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IAkED,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,WAAW,GAAG;IACrB,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CAC/C,CAAC;AAhEK,YAAP,CAAA,cAAO,GAAyD;IAkEhE,cAAc,EAjEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAkEhC,CAjEC;AAoEF,AAkBA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,AAAA,MAAA,eAAA,CAAA;;;;;;IAMA,WAAA,CACM,aAvFe,EAAkB,WAAgC,EAwFhE,QAvFU,EAqFjB;QAGI,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KAClE;;AAtFI,eAAP,CAAA,UAAO,GAAoC;IAwF3C,EAvFE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,mBAAA,EAAoB,EAAC,EAAG;CAwF5D,CAvFC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IA0FD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,WAAW,GAAG;IACrB,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CAC/C,CAAC,AAGF,AAQC;;ADxUD;;;;;;;AASA,AAEA,AAEA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,AAAA,MAAA,QAAA,CAAA;;;;IAOA,WAAA,CAAsB,aAAe,EAArC;QAAsB,IAAtB,CAAA,aAAsB,GAAA,aAAA,CAAe;QAF3B,IAAV,CAAA,UAAU,GAAwC,EAAA,CAAG;KAED;;;;;IAKpD,IAFG,QAAA,CAAA,KAAA,EAEH;QACI,IAAI,CAFC,YAAC,GAAc,KAAA,CAAM;QAG1B,IAAI,CAFC,WAAC,EAAW,CAAE;KAGpB;;;;;;IAMH,OANG,CAAA,KAAA,EAAA,UAAA,EAMH,EANyD,IAAA,CAAK,UAAC,CAAU,KAAC,CAAK,GAAG,UAAA,CAAW,EAAC;;;;IAE3F,WAAA,GAAH;QASI,IAAI,CARC,WAAC,EAAW,CAAE;QAUnB,uBARM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,UAAC,CAAU,CAAC;QAS3C,uBARM,GAAA,GAAM,iBAAA,CAAkB,IAAC,CAAI,YAAC,EAAa,KAAA,EAAO,IAAA,CAAK,aAAC,CAAa,CAAC;QAS5E,IAAI,CARC,aAAC,CAAa,IAAC,CAAI,UAAC,CAAU,GAAC,CAAG,CAAC,CAAC;KAS1C;;;;IANA,WAAA,GAAH;QAWI,IAAI,IAVC,CAAI,WAAC;YAAY,IAAA,CAAK,WAAC,CAAW,OAAC,EAAO,CAAE;KAWlD;;;;;IARA,aAAA,CAAA,IAAA,EAAH;QAcI,IAAI,IAbC,EAAK;YAcR,IAAI,CAbC,WAAC,GAAa,IAAA,CAAK;YAcxB,IAAI,CAbC,WAAC,CAAW,MAAC,EAAM,CAAE;SAc3B;KACF;;AAZI,QAAP,CAAA,UAAO,GAAoC;IAc3C,EAbE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,YAAA,EAAa,EAAC,EAAG;CAcrD,CAbC;;;;AAED,QAAD,CAAA,cAAC,GAAA,MAAA;IAgBD,EAAC,IAAI,EAAE,cAAc,GAAG;CACvB,CAAC;AAdK,QAAP,CAAA,cAAO,GAAyD;IAgBhE,UAAU,EAfE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAgB5B,CAfC;AAkBF,AAoBA;;;;;;;;;;;;;;;;;;AAkBA,AAAA,MAAA,YAAA,CAAA;;;;;;;IAOA,WAAA,CAvCc,KAAO,EAAQ,QAA6B,EAyCpD,aAxCe,EAAkB,QAAW,EAsClD;QAvCc,IAAd,CAAA,KAAc,GAAA,KAAA,CAAO;QA0CjB,MAAM,SAAS,GAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,CAAjC,CAAA,EAAqC,KAAK,CAA1C,CAA4C,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5F;;AAvCI,YAAP,CAAA,UAAO,GAAoC;IAyC3C,EAxCE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,gBAAA,EAAiB,EAAC,EAAG;CAyCzD,CAxCC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IA2CD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,cAAc,EAAG,EAAE,EAAG,EAAC;IAChF,EAAC,IAAI,EAAE,WAAW,GAAG;IACrB,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CAC/C,CAAC,AAGF,AAUC;;ADtLD;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;AAsBA,AAAA,MAAA,OAAA,CAAA;;;;;;IAQA,WAAA,CAFc,QAAU,EAAyB,KAAO,EAAoB,SAAW,EAEvF;QAFc,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QAAyB,IAAjD,CAAA,KAAiD,GAAA,KAAA,CAAO;QAAoB,IAA5E,CAAA,SAA4E,GAAA,SAAA,CAAW;KAAS;;;;;IAUhG,IAPG,OAAA,CAAA,CAAA,EAOH;QACI,IAAI,CAPC,QAAC,GAAU,CAAA,CAAE;QAQlB,IAAI,CAPC,IAAC,CAAI,OAAC,IAAU,CAAA,EAAG;YAQtB,IAAI,CAPC,OAAC,GAAS,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,CAAC,CAAC,CAAC,MAAC,EAAM,CAAE;SAQ/C;KACF;;;;IAIH,SATG,GASH;QACI,IAAI,IATC,CAAI,OAAC,EAAQ;YAUhB,uBATM,OAAA,GAAU,IAAA,CAAK,OAAC,CAAO,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,CAAC;YAUjD,IAAI,OATC,EAAQ;gBAUX,IAAI,CATC,aAAC,CAAa,OAAC,CAAO,CAAC;aAU7B;SACF;KACF;;;;;IAPA,aAAA,CAAA,OAAA,EAAH;QAaI,OAAO,CAZC,kBAAC,CAAkB,CAAC,MAAC,KAAU,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,GAAC,EAAI,IAAA,CAAK,CAAC,CAAC;QAazE,OAAO,CAZC,gBAAC,CAAgB,CAAC,MAAC,KAAU,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,GAAC,EAAI,MAAA,CAAO,YAAC,CAAY,CAAC,CAAC;QAatF,OAAO,CAZC,kBAAC,CAAkB,CAAC,MAAC,KAAU,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,GAAC,EAAI,MAAA,CAAO,YAAC,CAAY,CAAC,CAAC;KAazF;;;;;;IAVA,SAAA,CAAA,WAAA,EAAA,KAAA,EAAH;QAiBI,MAhBM,CAAA,IAAE,EAAK,IAAA,CAAK,GAAG,WAAA,CAAY,KAAC,CAAK,GAAC,CAAG,CAAC;QAK9C,KAAA,GAJU,KAAA,IAAS,IAAA,IAAQ,IAAA,GAAO,CAIpC,EAJoC,KAAI,CAIxC,EAJ6C,IAAG,CAIhD,CAJoD,GAAI,KAAA,CAAM;QAkB1D,IAAI,CAhBC,SAAC,CAAS,eAAC,CAAe,IAAC,CAAI,KAAC,CAAK,aAAC,EAAc,IAAA,oBAAM,KAAS,EAAO,CAAC;KAiBjF;;AAfI,OAAP,CAAA,UAAO,GAAoC;IAiB3C,EAhBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,WAAA,EAAY,EAAC,EAAG;CAiBpD,CAhBC;;;;AAED,OAAD,CAAA,cAAC,GAAA,MAAA;IAmBD,EAAC,IAAI,EAAE,eAAe,GAAG;IACzB,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,QAAQ,GAAG;CACjB,CAAC;AAjBK,OAAP,CAAA,cAAO,GAAyD;IAmBhE,SAAS,EAlBE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAmB3B,CAlBC,AAqBF,AAoBC;;ADzHD;;;;;;;AASA,AACA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAA,MAAA,gBAAA,CAAA;;;;IAOA,WAAA,CACsB,iBAAmB,EADzC;QACsB,IAAtB,CAAA,iBAAsB,GAAA,iBAAA,CAAmB;KAAiB;;;;;;IAK1D,IACG,eAAA,CAAA,OAAA,EADH,EACyC,IAAA,CAAK,uBAAC,GAAyB,OAAA,CAAQ,EAAC;;;;;IAIjF,WAFG,CAAA,OAAA,EAEH;QACI,IAAI,IAFC,CAAI,QAAC,EAAS;YAGjB,IAAI,CAFC,iBAAC,CAAiB,MAAC,CAAM,IAAC,CAAI,iBAAC,CAAiB,OAAC,CAAO,IAAC,CAAI,QAAC,CAAQ,CAAC,CAAC;SAG9E;QAED,IAAI,IAFC,CAAI,gBAAC,EAAiB;YAGzB,IAAI,CAFC,QAAC,GAAU,IAAA,CAAK,iBAAC,CAAiB,kBAAC,CAGpC,IAAI,CAFC,gBAAC,EAAiB,IAAA,CAAK,uBAAC,CAAuB,CAAC;SAG1D;KACF;;AADI,gBAAP,CAAA,UAAO,GAAoC;IAG3C,EAFE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,oBAAA,EAAqB,EAAC,EAAG;CAG7D,CAFC;;;;AAED,gBAAD,CAAA,cAAC,GAAA,MAAA;IAKD,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;AAHK,gBAAP,CAAA,cAAO,GAAyD;IAKhE,yBAZE,EAQyB,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAK5C,kBAAkB,EAJE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAKrC,iBAAiB,EAJE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAKnC,CAJC,AAOF,AAkBC;;ADhGD;;;;;;;AAWA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAgBA;;;;AAIA,AAEC,MAAA,iBAAA,GAAA;IADC,OAAO;IACP,iBAAiB;IACjB,OAAO;IACP,IAAI;IACJ,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,QAAQ;IACR,YAAY;CACb,CAAC;;;GAGC,AACH,AAGC,AAAA;;AD3DD;;;;;;;AASA,AACA;;;;;AAKA,AAAA,SAAA,wBAAA,CALC,IAAA,EAAA,KAAA,EAKD;IACE,OALO,KAAA,CAAM,CAKf,sBAAA,EALgB,KAAyB,CAKzC,YAAA,EAL8CA,UAAe,CAAS,IAAC,CAAI,CAK3E,CAAA,CAL4E,CAAG,CAAC;CAM/E;;ADjBD;;;;;;;AASA,AAGA,AAQA,MAAA,kBAAA,CAAA;;;;;;IAMA,kBALG,CAAA,KAAA,EAAA,iBAAA,EAKH;QACI,OALO,KAAA,CAAM,SAAC,CAAS,EAAC,IAAC,EAAK,iBAAA,EAAmB,KAAA,EAAO,CAAA,CAAI,KAKhE,EAL0E,MAAM,CAAA,CAAE,EAAC,EAAE,CAAC,CAAC;KAMpF;;;;;IAKH,OARG,CAAA,YAAA,EAQH,EAR+C,YAAA,CAAa,WAAC,EAAW,CAAE,EAAC;;;;;IAa3E,SAXG,CAAA,YAAA,EAWH,EAXiD,YAAA,CAAa,WAAC,EAAW,CAAE,EAAC;CAY5E;AACD,MAAA,eAAA,CAAA;;;;;;IAMA,kBAfG,CAAA,KAAA,EAAA,iBAAA,EAeH;QACI,OAfO,KAAA,CAAM,IAAC,CAAI,iBAAC,EAAkB,CAAA,IAezC,EAfgD,MAAM,CAAA,CAAE,EAAC,CAAE,CAAC;KAgBzD;;;;;IAKH,OAlBG,CAAA,YAAA,EAkBH,GAlB4C;;;;;IAuB5C,SArBG,CAAA,YAAA,EAqBH,GArB8C;CAsB7C;AAED,MArBM,gBAAA,GAAmB,IAAI,eAAA,EAAgB,CAAE;AAsB/C,MArBM,mBAAA,GAAsB,IAAI,kBAAA,EAAmB,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CrD,AAAA,MAAA,SAAA,CAAA;;;;IASA,WAAA,CApBsB,IAAM,EAoB5B;QApBsB,IAAtB,CAAA,IAAsB,GAAA,IAAA,CAAM;QAPlB,IAAV,CAAA,YAAU,GAAoB,IAAA,CAAK;QACzB,IAAV,CAAA,oBAAU,GAA4B,IAAA,CAAK;QAEjC,IAAV,CAAA,aAAU,GAAiD,IAAA,CAAK;QACtD,IAAV,CAAA,IAAU,GAA4D,IAAA,CAAK;QACjE,IAAV,CAAA,SAAU,KAAkC,IAAA,EAAA,CAAO;KAEL;;;;IAwB9C,WAtBG,GAsBH;QACI,IAAI,IAtBC,CAAI,aAAC,EAAc;YAuBtB,IAAI,CAtBC,QAAC,EAAQ,CAAE;SAuBjB;KACF;;;;;IAUH,SA1BG,CAAA,GAAA,EA0BH;QACI,IAAI,CA1BC,IAAC,CAAI,IAAC,EAAK;YA2Bd,IAAI,GA1BC,EAAI;gBA2BP,IAAI,CA1BC,UAAC,CAAU,GAAC,CAAG,CAAC;aA2BtB;YACD,IAAI,CA1BC,oBAAC,GAAsB,IAAA,CAAK,YAAC,CAAY;YA2B9C,OA1BO,IAAA,CAAK,YAAC,CAAY;SA2B1B;QAED,IAAI,GA1BC,KAAO,IAAA,CAAK,IAAC,EAAK;YA2BrB,IAAI,CA1BC,QAAC,EAAQ,CAAE;YA2BhB,OA1BO,IAAA,CAAK,SAAC,mBAAS,GAAO,EAAI,CAAC;SA2BnC;QAED,IAAI,IA1BC,CAAI,YAAC,KAAgB,IAAA,CAAK,oBAAC,EAAqB;YA2BnD,OA1BO,IAAA,CAAK,oBAAC,CAAoB;SA2BlC;QAED,IAAI,CA1BC,oBAAC,GAAsB,IAAA,CAAK,YAAC,CAAY;QA2B9C,OA1BO,YAAA,CAAa,IAAC,CAAI,IAAC,CAAI,YAAC,CAAY,CAAC;KA2B7C;;;;;IAxBA,UAAA,CAAA,GAAA,EAAH;QA8BI,IAAI,CA7BC,IAAC,GAAM,GAAA,CAAI;QA8BhB,IAAI,CA7BC,SAAC,GAAW,IAAA,CAAK,eAAC,CAAe,GAAC,CAAG,CAAC;QA8B3C,IAAI,CA7BC,aAAC,GAAe,IAAA,CAAK,SAAC,CAAS,kBAAC,CA8BjC,GAAG,EA7BE,CAAA,KAAQ,KAAW,IAAA,CAAK,kBAAC,CAAkB,GAAC,EAAI,KAAA,CAAM,CAAC,CAAC;KA8BlE;;;;;IA3BA,eAAA,CAAA,GAAA,EAAH;QA0BE,IAAA,UAzBO,CAAU,GAAC,CAAG,EAAE;YAiCnB,OAhCO,gBAAA,CAAiB;SAiCzB;QAED,IAAI,aAhCC,CAAa,GAAC,CAAG,EAAE;YAiCtB,OAhCO,mBAAA,CAAoB;SAiC5B;QAED,MAhCM,wBAAA,CAAyB,SAAC,EAAU,GAAA,CAAI,CAAC;KAiChD;;;;IA9BA,QAAA,GAAH;QAmCI,IAAI,CAlCC,SAAC,CAAS,OAAC,oBAAO,IAAC,CAAI,aAAC,GAAe,CAAC;QAmC7C,IAAI,CAlCC,YAAC,GAAc,IAAA,CAAK;QAmCzB,IAAI,CAlCC,oBAAC,GAAsB,IAAA,CAAK;QAmCjC,IAAI,CAlCC,aAAC,GAAe,IAAA,CAAK;QAmC1B,IAAI,CAlCC,IAAC,GAAM,IAAA,CAAK;KAmClB;;;;;;IAhCA,kBAAA,CAAA,KAAA,EAAA,KAAA,EAAH;QAuCI,IAAI,KAtCC,KAAS,IAAA,CAAK,IAAC,EAAK;YAuCvB,IAAI,CAtCC,YAAC,GAAc,KAAA,CAAM;YAuC1B,IAAI,CAtCC,IAAC,CAAI,YAAC,EAAY,CAAE;SAuC1B;KACF;;AArCI,SAAP,CAAA,UAAO,GAAoC;IAuC3C,EAtCE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,OAAA,EAAS,IAAA,EAAM,KAAA,EAAM,EAAC,EAAG;CAuCpD,CAtCC;;;;AAED,SAAD,CAAA,cAAC,GAAA,MAAA;IAyCD,EAAC,IAAI,EAAE,iBAAiB,GAAG;CAC1B,CAAC,AAGF,AAoBC;;ADrND;;;;;;;AASA,AACA,AACA;;;;;;;AAOA,AAAA,MAAA,aAAA,CAAA;;;;;IAKA,SAHG,CAAA,KAAA,EAGH;QACI,IAAI,CAHC,KAAC;YAAM,OAAO,KAAA,CAAM;QAIzB,IAAI,OAHO,KAAA,KAAU,QAAA,EAAU;YAI7B,MAHM,wBAAA,CAAyB,aAAC,EAAc,KAAA,CAAM,CAAC;SAItD;QACD,OAHO,KAAA,CAAM,WAAC,EAAW,CAAE;KAI5B;;AAFI,aAAP,CAAA,UAAO,GAAoC;IAI3C,EAHE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,WAAA,EAAY,EAAC,EAAG;CAI3C,CAHC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA,EAMA,CAAC;AAGF,AAUA;;;;;;;AAOA,SAAA,aAAA,CAhBC,IAAA,EAgBD;IACE,IAAI,CAtBC,IAAC;QAAK,OAAO,IAAA,CAAK;IAuBvB,OAtBO,IAAA,CAAK,CAAC,CAAC,CAAC,WAAC,EAAW,GAAI,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC,CAAC,WAAC,EAAW,CAAE;CAuB7D;;;;;;AAMD,AAAA,MAAA,aAAA,CAAA;;;;;IAKA,SAlBG,CAAA,KAAA,EAkBH;QACI,IAAI,CAlBC,KAAC;YAAM,OAAO,KAAA,CAAM;QAmBzB,IAAI,OAlBO,KAAA,KAAU,QAAA,EAAU;YAmB7B,MAlBM,wBAAA,CAAyB,aAAC,EAAc,KAAA,CAAM,CAAC;SAmBtD;QAED,OAlBO,KAAA,CAAM,KAAC,CAAK,KAAC,CAAK,CAAC,GAAC,CAAG,IAAC,IAAO,aAAA,CAAc,IAAC,CAAI,CAAC,CAAC,IAAC,CAAI,EAAC,CAAE,CAAC;KAmBrE;;AAjBI,aAAP,CAAA,UAAO,GAAoC;IAmB3C,EAlBE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,WAAA,EAAY,EAAC,EAAG;CAmB3C,CAlBC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA,EAqBA,CAAC;AAGF,AAUA;;;;;AAKA,AAAA,MAAA,aAAA,CAAA;;;;;IAKA,SAjCG,CAAA,KAAA,EAiCH;QACI,IAAI,CAjCC,KAAC;YAAM,OAAO,KAAA,CAAM;QAkCzB,IAAI,OAjCO,KAAA,KAAU,QAAA,EAAU;YAkC7B,MAjCM,wBAAA,CAAyB,aAAC,EAAc,KAAA,CAAM,CAAC;SAkCtD;QACD,OAjCO,KAAA,CAAM,WAAC,EAAW,CAAE;KAkC5B;;AAhCI,aAAP,CAAA,UAAO,GAAoC;IAkC3C,EAjCE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,WAAA,EAAY,EAAC,EAAG;CAkC3C,CAjCC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA,EAoCA,CAAC,AAGF,AAQC;;ADpIM,IAAI,iBAAiB,GAAQ,EAAE,CAAC;AACvC,iBAAiB,CAAC,OAAO,GAAG,CAAC,CAAC;AAC9B,iBAAiB,CAAC,OAAO,GAAG,CAAC,CAAC;AAC9B,iBAAiB,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/B,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACzD,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACzD,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AAE3D,AAAA,MAAA,eAAA,CAAA;;;;;;;;IAQA,OAHG,MAAA,CAAA,GAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAGH,GAMM,EAAE,EANR;QAOI,MAHM,EAAA,oBAAE,EAAqB,qBAAA,EAAuB,qBAAA,EAAuB,QAAA,EAIpE,gBAAgB,GAHG,KAAA,EAAM,GAAG,IAAA,CAAK;QAIxC,uBAHM,OAAA,GAAoC;YAIxC,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,KAAK,EAHE,iBAAA,CAAkB,KAAC,CAAK,CAAC,WAAC,EAAW;SAI7C,CAHC;QAKF,IAAI,KAHC,IAAQ,iBAAA,CAAkB,QAAC,EAAS;YAIvC,OAAO,CAHC,QAAC,GAAU,OAAO,QAAA,IAAY,QAAA,GAAW,QAAA,GAAW,SAAA,CAAU;YAItE,OAAO,CAHC,eAAC,GAAiB,gBAAA,GAAmB,QAAA,GAAW,MAAA,CAAO;SAIhE;QACD,OAHO,IAAI,IAAA,CAAK,YAAC,CAAY,MAAC,EAAO,OAAA,CAAQ,CAAC,MAAC,CAAM,GAAC,CAAG,CAAC;KAI3D;CACF;AAID,MAHM,kBAAA,GAIF,qGAAqG,CAHC;AAK1G,MAHM,eAAA,GAAuD;;IAK3D,UAAU,EAHE,qBAAA,CAAsB,OAAC,CAAO;QAIxC,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE;QAIzB,aAAa,CAHC,OAAC,EAAQ,CAAA,CAAE;QAIzB,cAAc,CAHC,KAAC,EAAM,CAAA,CAAE;QAIxB,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE;QAIzB,cAAc,CAHC,QAAC,EAAS,CAAA,CAAE;QAI3B,cAAc,CAHC,QAAC,EAAS,CAAA,CAAE;KAI5B,CAHC,CAAC;IAIH,OAAO,EAHE,qBAAA,CAAsB,OAAC,CAAO;QAIrC,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE,EAAE,cAAA,CAAe,OAAC,EAAQ,CAAA,CAAE,EAAE,cAAA,CAAe,KAAC,EAAM,CAAA,CAAE;QAI/E,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE,EAAE,cAAA,CAAe,QAAC,EAAS,CAAA,CAAE;KAIvD,CAHC,CAAC;IAIH,YAAY,EAHE,qBAAA,CAAsB,OAAC,CAAO;QAI1C,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE,EAAE,aAAA,CAAc,OAAC,EAAQ,CAAA,CAAE,EAAE,aAAA,CAAc,SAAC,EAAU,CAAA,CAAE;QAIjF,cAAc,CAHC,KAAC,EAAM,CAAA,CAAE;KAIzB,CAHC,CAAC;IAIH,QAAQ,EAHE,qBAAA,CAIN,OAAO,CAHC,CAAC,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,aAAA,CAAc,OAAC,EAAQ,CAAA,CAAE,EAAE,cAAA,CAAe,KAAC,EAAM,CAAA,CAAE,CAAC,CAAC,CAAC;IAI9F,OAAO,EAHE,qBAAA,CAIL,OAAO,CAHC,CAAC,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,aAAA,CAAc,OAAC,EAAQ,CAAA,CAAE,EAAE,cAAA,CAAe,KAAC,EAAM,CAAA,CAAE,CAAC,CAAC,CAAC;IAI9F,KAAK,EAHE,qBAAA,CAIH,OAAO,CAHC,CAAC,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,cAAA,CAAe,OAAC,EAAQ,CAAA,CAAE,EAAE,cAAA,CAAe,KAAC,EAAM,CAAA,CAAE,CAAC,CAAC,CAAC;IAI/F,KAAK,EAHE,qBAAA,CAAsB,OAAC,CAI1B,CAAC,cAHC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,cAAA,CAAe,QAAC,EAAS,CAAA,CAAE,EAAE,cAAA,CAAe,QAAC,EAAS,CAAA,CAAE,CAAC,CAAC,CAAC;IAI3F,IAAI,EAHE,qBAAA,CAAsB,OAAC,CAAO,CAAC,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,cAAA,CAAe,QAAC,EAAS,CAAA,CAAE,CAAC,CAAC,CAAC;CAI/F,CAHC;AAKF,MAHM,YAAA,GAAoD;;IAKxD,MAAM,EAHE,qBAAA,CAAsB,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,CAAC;IAIxD,IAAI,EAHE,qBAAA,CAAsB,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,CAAC;IAItD,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,CAAC;IAIrD,MAAM,EAHE,qBAAA,CAAsB,aAAC,CAAa,OAAC,EAAQ,CAAA,CAAE,CAAC;IAIxD,KAAK,EAHE,qBAAA,CAAsB,aAAC,CAAa,OAAC,EAAQ,CAAA,CAAE,CAAC;IAIvD,IAAI,EAHE,qBAAA,CAAsB,cAAC,CAAc,OAAC,EAAQ,CAAA,CAAE,CAAC;IAIvD,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,OAAC,EAAQ,CAAA,CAAE,CAAC;IAItD,MAAM,EAHE,qBAAA,CAAsB,aAAC,CAAa,OAAC,EAAQ,CAAA,CAAE,CAAC;IAIxD,GAAG,EAHE,qBAAA,CAAsB,aAAC,CAAa,OAAC,EAAQ,CAAA,CAAE,CAAC;IAIrD,IAAI,EAHE,qBAAA,CAAsB,cAAC,CAAc,KAAC,EAAM,CAAA,CAAE,CAAC;IAIrD,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,KAAC,EAAM,CAAA,CAAE,CAAC;IAIpD,IAAI,EAHE,aAAA,CAIF,aAAa,CAHC,qBAAC,CAAqB,YAAC,CAAY,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,KAAA,CAAM,CAAC,CAAC,CAAC;IAIzF,GAAG,EAHE,aAAA,CAAc,qBAAC,CAAqB,YAAC,CAAY,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,KAAA,CAAM,CAAC,CAAC;IAIzF,IAAI,EAHE,aAAA,CAIF,aAAa,CAHC,qBAAC,CAAqB,YAAC,CAAY,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,IAAA,CAAK,CAAC,CAAC,CAAC;IAIxF,GAAG,EAHE,aAAA,CAAc,qBAAC,CAAqB,YAAC,CAAY,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,IAAA,CAAK,CAAC,CAAC;IAIxF,IAAI,EAHE,qBAAA,CAAsB,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,CAAC;IAItD,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,CAAC;IAIrD,IAAI,EAHE,aAAA,CAAc,qBAAC,CAAqB,cAAC,CAAc,QAAC,EAAS,CAAA,CAAE,CAAC,CAAC;IAIvE,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,QAAC,EAAS,CAAA,CAAE,CAAC;IAIvD,IAAI,EAHE,aAAA,CAAc,qBAAC,CAAqB,cAAC,CAAc,QAAC,EAAS,CAAA,CAAE,CAAC,CAAC;IAIvE,GAAG,EAHE,qBAAA,CAAsB,cAAC,CAAc,QAAC,EAAS,CAAA,CAAE,CAAC;;;;IAOvD,KAAK,EAHE,qBAAA,CAAsB,cAAC,CAAc,QAAC,EAAS,CAAA,CAAE,CAAC;IAIzD,MAAM,EAHE,qBAAA,CAAsB,aAAC,CAAa,SAAC,EAAU,CAAA,CAAE,CAAC;IAI1D,KAAK,EAHE,qBAAA,CAAsB,aAAC,CAAa,SAAC,EAAU,CAAA,CAAE,CAAC;IAIzD,IAAI,EAHE,qBAAA,CAAsB,aAAC,CAAa,SAAC,EAAU,CAAA,CAAE,CAAC;IAIxD,GAAG,EAHE,qBAAA,CAAsB,aAAC,CAAa,SAAC,EAAU,CAAA,CAAE,CAAC;IAIvD,GAAG,EAHE,kBAAA,CAAmB,qBAAC,CAAqB,YAAC,CAAY,cAAC,CAAc,MAAC,EAAO,CAAA,CAAE,EAAE,IAAA,CAAK,CAAC,CAAC;IAI7F,GAAG,EAHE,cAAA,CAAe,OAAC,CAAO;IAI5B,GAAG,EAHE,cAAA,CAAe,MAAC,CAAM;IAI3B,IAAI,EAHE,qBAAA,CAAsB,EAAC,CAAE;;IAK/B,GAAG,EACC,qBAAqB,CAHC,EAAC,CAAE;;IAK7B,GAAG,EAHE,qBAAA,CAAsB,aAAC,CAAa,KAAC,EAAM,CAAA,CAAE,CAAC;IAInD,IAAI,EAHE,qBAAA,CAAsB,aAAC,CAAa,KAAC,EAAM,CAAA,CAAE,CAAC;IAIpD,KAAK,EAHE,qBAAA,CAAsB,aAAC,CAAa,KAAC,EAAM,CAAA,CAAE,CAAC;IAIrD,MAAM,EAHE,qBAAA,CAAsB,aAAC,CAAa,KAAC,EAAM,CAAA,CAAE,CAAC;CAIvD,CAHC;;;;;AAQF,SAAA,aAAA,CALC,KAAA,EAKD;IACE,OALO,UAAA,IAAe,EAAM,MAAQ,EAKtC;QACI,uBALM,MAAA,GAAS,KAAA,CAAM,IAAC,EAAK,MAAA,CAAO,CAAC;QAMnC,OALO,MAAA,CAAO,MAAC,IAAS,CAAA,GAAI,GAAA,GAAM,MAAA,GAAS,MAAA,CAAO;KAMnD,CALC;CAMH;;;;;AAKD,SAAA,kBAAA,CARC,KAAA,EAQD;IACE,OARO,UAAA,IAAe,EAAM,MAAQ,EAQtC,EARwD,OAAO,KAAA,CAAM,IAAC,EAAK,MAAA,CAAO,CAAC,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAE;CASnG;;;;;AAKD,SAAA,aAAA,CAXC,KAAA,EAWD;IACE,OAXO,UAAA,IAAe,EAAM,MAAQ,EAWtC,EAXwD,OAAO,KAAA,CAAM,IAAC,EAAK,MAAA,CAAO,CAAC,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAE;CAYnG;;;;;;;AAOD,SAAA,cAAA,CAhBC,IAAA,EAAA,MAAA,EAAA,OAAA,EAgBD;IACE,OAhBO,IAAI,IAAA,CAAK,cAAC,CAAc,MAAC,EAAO,OAAA,CAAQ,CAAC,MAAC,CAAM,IAAC,CAAI,CAAC,OAAC,CAAO,iBAAC,EAAkB,EAAA,CAAG,CAAC;CAiB7F;;;;;AAKD,SAAA,cAAA,CAnBC,QAAA,EAmBD;;IAEE,uBAnBM,OAAA,GAAU,EAAA,IAAE,EAAK,SAAA,EAAW,MAAA,EAAQ,KAAA,EAAO,YAAA,EAAc,QAAA,EAAS,CAAC;IAoBzE,OAnBO,UAAA,IAAe,EAAM,MAAQ,EAmBtC;QACI,uBAnBM,MAAA,GAAS,cAAA,CAAe,IAAC,EAAK,MAAA,EAAQ,OAAA,CAAQ,CAAC;;QAqBrD,OAnBO,MAAA,GAAS,MAAA,CAAO,SAAC,CAAS,CAAC,CAAC,GAAG,EAAA,CAAG;KAoB1C,CAnBC;CAoBH;;;;;;AAMD,SAAA,YAAA,CACI,OAAmC,EAAE,KAAc,EADvD;IAEE,OAAO,CAvBC,MAAC,GAAQ,KAAA,CAAM;IAwBvB,OAvBO,OAAA,CAAQ;CAwBhB;;;;;;AAMD,SAAA,cAAA,CA3BC,IAAA,EAAA,GAAA,EA2BD;IACE,uBA3BM,MAAA,GAAgC,EAAA,CAAG;IA4BzC,MAAM,CA3BC,IAAC,CAAI,GAAG,GAAA,KAAQ,CAAA,GAAI,SAAA,GAAY,SAAA,CAAU;IA4BjD,OA3BO,MAAA,CAAO;CA4Bf;;;;;;AAMD,SAAA,aAAA,CA/BC,IAAA,EAAA,GAAA,EA+BD;IACE,uBA/BM,MAAA,GAAgC,EAAA,CAAG;IAgCzC,IAAI,GA/BC,GAAK,CAAA,EAAG;QAgCX,MAAM,CA/BC,IAAC,CAAI,GAAG,GAAA,GAAM,CAAA,GAAI,OAAA,GAAU,QAAA,CAAS;KAgC7C;SA/BM;QAgCL,MAAM,CA/BC,IAAC,CAAI,GAAG,MAAA,CAAO;KAgCvB;IAED,OA/BO,MAAA,CAAO;CAgCf;;;;;AAKD,SAAA,OAAA,CAlCC,OAAA,EAkCD;IACE,OAlCO,EAAM,MAAC,GAAO,MAAC,CAAM,EAAC,EAAG,GAAA,OAAI,CAAO,CAAC;CAmC7C;;;;;AAKD,SAAA,qBAAA,CArCC,GAAA,EAqCD;IACE,OArCO,CAAA,IAAO,EAAM,MAAQ,KAAmB,cAAA,CAAe,IAAC,EAAK,MAAA,EAAQ,GAAA,CAAI,CAAC;CAsClF;AAED,MArCM,oBAAA,GAAuB,IAAI,GAAA,EAAqB,CAAG;;;;;;;AA4CzD,SAAA,aAAA,CA1CC,MAAA,EAAA,IAAA,EAAA,MAAA,EA0CD;IACE,uBA1CM,EAAA,GAAK,eAAA,CAAgB,MAAC,CAAM,CAAC;IA4CnC,IAAI,EA1CC;QAAG,OAAO,EAAA,CAAG,IAAC,EAAK,MAAA,CAAO,CAAC;IA4ChC,uBA1CM,QAAA,GAAW,MAAA,CAAO;IA2CxB,qBA1CI,KAAA,GAAQ,oBAAA,CAAqB,GAAC,CAAG,QAAC,CAAQ,CAAC;IA4C/C,IAAI,CA1CC,KAAC,EAAM;QA2CV,KAAK,GA1CG,EAAA,CAAG;QA2CX,qBA1CI,KAAwB,CAAI;QA2ChC,kBAAkB,CA1CC,IAAC,CAAI,MAAC,CAAM,CAAC;QA4ChC,qBA1CI,OAAA,GAAuB,MAAA,CAAO;QA2ClC,OAAO,OA1CC,EAAQ;YA2Cd,KAAK,GA1CG,kBAAA,CAAmB,IAAC,CAAI,OAAC,CAAO,CAAC;YA2CzC,IAAI,KA1CC,EAAM;gBA2CT,KAAK,GA1CG,KAAA,CAAM,MAAC,CAAM,KAAC,CAAK,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;gBA2CrC,OAAO,KA1CG,KAAA,CAAM,GAAC,EAAG,EAAA,CAAI;aA2CzB;iBA1CM;gBA2CL,KAAK,CA1CC,IAAC,CAAI,OAAC,CAAO,CAAC;gBA2CpB,OAAO,GA1CG,IAAA,CAAK;aA2ChB;SACF;QAED,oBAAoB,CA1CC,GAAC,CAAG,QAAC,EAAS,KAAA,CAAM,CAAC;KA2C3C;IAED,OA1CO,KAAA,CAAM,MAAC,CAAM,CAAC,IAAC,EAAK,IAAA,KA0C7B;QACI,uBA1CM,EAAA,GAAK,YAAA,CAAa,IAAC,CAAI,CAAC;QA2C9B,OA1CO,IAAA,IAAO,EAAE,GAAI,EAAA,CAAG,IAAC,EAAK,MAAA,CAAO,GAAG,UAAA,CAAW,IAAC,CAAI,CAAC,CAAC;KA2C1D,EA1CE,EAAA,CAAG,CAAC;CA2CR;;;;;AAKD,SAAA,UAAA,CA7CC,IAAA,EA6CD;IACE,OA7CO,IAAA,KAAS,MAAA,GAAS,IAAA,GAAO,IAAA,CAAK,OAAC,CAAO,UAAC,EAAW,EAAA,CAAG,CAAC,OAAC,CAAO,KAAC,EAAM,IAAA,CAAK,CAAC;CA8CnF;AACD,AAAA,MAAA,aAAA,CAAA;;;;;;;IAOA,OAlDG,MAAA,CAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAkDH;QACI,OAlDO,aAAA,CAAc,OAAC,EAAQ,IAAA,EAAM,MAAA,CAAO,CAAC;KAmD7C;CACF;;ADnRD;;;;;;;AASA,AACA,AACA,AAEA,MADM,qBAAA,GAAwB,6BAAA,CAA8B;;;;;;;;;;;AAY5D,SAAA,YAAA,CACI,IAAe,EAAE,MAAc,EAAE,KAAsB,EAAE,KAAwB,EACjF,MAAkB,EAAA,QAFtB,GAE4C,IAAA,EACxC,gBAHJ,GAG2B,KAAS,EAHpC;IAIE,IAAI,KAVC,IAAQ,IAAA;QAAM,OAAO,IAAA,CAAK;;IAa/B,KAAK,GAVG,OAAO,KAAA,KAAU,QAAA,IAAY,SAAA,CAAU,KAAC,CAAK,GAAG,CAAA,KAAE,GAAO,KAAA,CAAM;IAWvE,IAAI,OAVO,KAAA,KAAU,QAAA,EAAU;QAW7B,MAVM,wBAAA,CAAyB,IAAC,EAAK,KAAA,CAAM,CAAC;KAW7C;IAED,qBAVI,MAAA,GAA2B,SAAA,CAAU;IAWzC,qBAVI,WAAA,GAAgC,SAAA,CAAU;IAW9C,qBAVI,WAAA,GAAgC,SAAA,CAAU;IAW9C,IAAI,KAVC,KAAS,iBAAA,CAAkB,QAAC,EAAS;;QAYxC,MAAK,GAVI,CAAA,CAAE;QAWX,WAAW,GAVG,CAAA,CAAE;QAWhB,WAAE,GAVY,CAAA,CAAE;KAWjB;IAED,IAAI,MAVC,EAAO;QAWV,uBAVM,KAAA,GAAQ,MAAA,CAAO,KAAC,CAAK,qBAAC,CAAqB,CAAC;QAWlD,IAAI,KAVC,KAAS,IAAA,EAAM;YAWlB,MAVM,IAAI,KAAA,CAAM,CAUtB,EAVuB,MAAG,CAU1B,2CAAA,CAVgC,CAA6C,CAAC;SAW1E;QACA,IAAI,KAVC,CAAK,CAAC,CAAC,IAAI,IAAA,EAAM;YAWpB,MAAG,GAVM,iBAAA,CAAkB,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;SAWtC;QACD,IAAI,KAVC,CAAK,CAAC,CAAC,IAAI,IAAA,EAAM;YAWpB,WAAW,GAVG,iBAAA,CAAkB,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;SAW3C;QACD,IAAI,KAVC,CAAK,CAAC,CAAC,IAAI,IAAA,EAAM;YAWpB,WAAA,GAVc,iBAAA,CAAkB,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;SAW3C;KACF;IAED,OAVO,eAAA,CAAgB,MAAC,mBAAM,KAAS,GAAQ,MAAA,EAAQ,KAAA,EAAO;QAW5D,oBAAoB,EAVE,MAAA;QAWtB,qBAAqB,EAVE,WAAA;QAWvB,qBAAqB,EAVE,WAAA;QAWvB,QAAQ,EAVE,QAAA;QAWV,gBAAgB,EAVE,gBAAA;KAWnB,CAVC,CAAC;CAWJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BD,AAAA,MAAA,WAAA,CAAA;;;;IAIA,WAAA,CAXuB,OAAS,EAWhC;QAXuB,IAAvB,CAAA,OAAuB,GAAA,OAAA,CAAS;KAAO;;;;;;IAkBvC,SAhBG,CAAA,KAAA,EAAA,MAAA,EAgBH;QACI,OAhBO,YAAA,CAAa,WAAC,EAAY,IAAA,CAAK,OAAC,EAAQ,KAAA,EAAO,iBAAA,CAAkB,OAAC,EAAQ,MAAA,CAAO,CAAC;KAiB1F;;AAfI,WAAP,CAAA,UAAO,GAAoC;IAiB3C,EAhBE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,QAAA,EAAS,EAAC,EAAG;CAiBxC,CAhBC;;;;AAED,WAAD,CAAA,cAAC,GAAA,MAAA;IAmBD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAG,EAAE,EAAG,EAAC;CACvE,CAAC;AAGF,AAYA;;;;;;;;;;;;;;;;;;;;AAoBA,AAAA,MAAA,WAAA,CAAA;;;;IAIA,WAAA,CAhCuB,OAAS,EAgChC;QAhCuB,IAAvB,CAAA,OAAuB,GAAA,OAAA,CAAS;KAAO;;;;;;IAuCvC,SArCG,CAAA,KAAA,EAAA,MAAA,EAqCH;QACI,OArCO,YAAA,CAAa,WAAC,EAAY,IAAA,CAAK,OAAC,EAAQ,KAAA,EAAO,iBAAA,CAAkB,OAAC,EAAQ,MAAA,CAAO,CAAC;KAsC1F;;AApCI,WAAP,CAAA,UAAO,GAAoC;IAsC3C,EArCE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,SAAA,EAAU,EAAC,EAAG;CAsCzC,CArCC;;;;AAED,WAAD,CAAA,cAAC,GAAA,MAAA;IAwCD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAG,EAAE,EAAG,EAAC;CACvE,CAAC;AAGF,AAYA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAA,MAAA,YAAA,CAAA;;;;IAIA,WAAA,CArDuB,OAAS,EAqDhC;QArDuB,IAAvB,CAAA,OAAuB,GAAA,OAAA,CAAS;KAAO;;;;;;;;IA8DvC,SA5DG,CA6DG,KAAU,EAAE,YADlB,GACyC,KAAK,EAAE,aADhD,GACyE,KAAK,EACxE,MAAe,EAFrB;QAGI,OA5DO,YAAA,CA6DH,YAAY,EA5DE,IAAA,CAAK,OAAC,EAAQ,KAAA,EAAO,iBAAA,CAAkB,QAAC,EAAS,MAAA,EAAQ,YAAA,EA6DvE,aAAa,CA5DC,CAAC;KA6DpB;;AA3DI,YAAP,CAAA,UAAO,GAAoC;IA6D3C,EA5DE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,UAAA,EAAW,EAAC,EAAG;CA6D1C,CA5DC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IA+DD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAG,EAAE,EAAG,EAAC;CACvE,CAAC;AAGF,AAYA;;;;AAIA,SAAA,iBAAA,CA9EC,IAAA,EA8ED;IACE,uBAnGM,MAAA,GAAiB,QAAA,CAAS,IAAC,CAAI,CAAC;IAoGtC,IAAI,KAnGC,CAAK,MAAC,CAAM,EAAE;QAoGjB,MAnGM,IAAI,KAAA,CAAM,uCAAC,GAAyC,IAAA,CAAK,CAAC;KAoGjE;IACD,OAnGO,MAAA,CAAO;CAoGf;;;;;AAKD,AAAA,SAAA,SAAA,CAjFC,KAAA,EAiFD;IACE,OAtGO,CAAA,KAAE,CAAK,KAAC,GAAO,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC;CAuG1C;;ADnRD;;;;;;;AASA,AACA,AACA,AACA,AAEA,MADM,kBAAA,GAEF,sGAAsG,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuE3G,AAAA,MAAA,QAAA,CAAA;;;;IAiBA,WAAA,CAFuB,OAAS,EAEhC;QAFuB,IAAvB,CAAA,OAAuB,GAAA,OAAA,CAAS;KAAO;;;;;;IASvC,SAPG,CAAA,KAAA,EAAA,OAOH,GAPG,YAAA,EAOH;QACI,qBAPI,IAAM,CAAK;QASf,IAAI,OAPC,CAAO,KAAC,CAAK,IAAI,KAAA,KAAU,KAAA;YAAO,OAAO,IAAA,CAAK;QASnD,IAAI,OAPO,KAAA,KAAU,QAAA,EAAU;YAQ7B,KAAK,GAPG,KAAA,CAAM,IAAC,EAAI,CAAE;SAQtB;QAED,IAAI,MAPC,CAAM,KAAC,CAAK,EAAE;YAQjB,IAAI,GAPG,KAAA,CAAM;SAQd;aAPM,IAAA,SAAK,CAAS,KAAC,CAAK,EAAE;YAQ3B,IAAI,GAPG,IAAI,IAAA,CAAK,UAAC,CAAU,KAAC,CAAK,CAAC,CAAC;SAQpC;aAPM,IAAA,OAAW,KAAA,KAAU,QAAA,IAAY,2BAAA,CAA4B,IAAC,CAAI,KAAC,CAAK,EAAE;;;;;;;;;;YAiBrF,MAPO,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,QAAA,CAAA,GAAA,EAAA,EAAA,CAAA,CAAA,CAAA;YAQD,IAAI,GAPG,IAAI,IAAA,CAAK,CAAC,EAAE,CAAA,GAAI,CAAA,EAAG,CAAA,CAAE,CAAC;SAQ9B;aAPM;YAQL,IAAI,GAPG,IAAI,IAAA,CAAK,KAAC,CAAK,CAAC;SAQxB;QAED,IAAI,CAPC,MAAC,CAAM,IAAC,CAAI,EAAE;YAQjB,qBAPI,KAAyB,CAAI;YAQjC,IAAI,CAPC,OAAO,KAAA,KAAU,QAAA,MAAa,KAAE,GAAO,KAAA,CAAM,KAAC,CAAK,kBAAC,CAAkB,CAAC,EAAE;gBAQ5E,IAAI,GAPG,eAAA,CAAgB,KAAC,CAAK,CAAC;aAQ/B;iBAPM;gBAQL,MAPM,wBAAA,CAAyB,QAAC,EAAS,KAAA,CAAM,CAAC;aAQjD;SACF;QAED,OAPO,aAAA,CAAc,MAAC,CAAM,IAAC,EAAK,IAAA,CAAK,OAAC,EAAQ,QAAA,CAAS,QAAC,CAAQ,OAAC,CAAO,IAAI,OAAA,CAAQ,CAAC;KAQxF;;;;;AA3DA,QAAH,CAAA,QAAG,GAAA;IACC,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,YAAY;IACxB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,OAAO;IACrB,WAAW,EAAE,KAAK;IAClB,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,IAAI;CAClB,CAAC;AA4CG,QAAP,CAAA,UAAO,GAAoC;IAQ3C,EAPE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,MAAA,EAAQ,IAAA,EAAM,IAAA,EAAK,EAAC,EAAG;CAQlD,CAPC;;;;AAED,QAAD,CAAA,cAAC,GAAA,MAAA;IAUD,EAAC,IAAI,EAAE,SAPM,EAAA,UAAW,EAAK,CAAA,EAAG,IAAE,EAAK,MAAK,EAAA,IAAO,EAAE,CAAC,SAAC,EAAA,EAAA,EAAA,EAAA;CAQtD,CAPC;AAUF,AAiBA;;;;AAIA,SAAA,OAAA,CA9BC,GAAA,EA8BD;IACE,OArCO,GAAA,IAAO,IAAA,IAAQ,GAAA,KAAQ,EAAA,CAAG;CAsClC;;;;;AAKD,SAAA,MAAA,CAjCC,GAAA,EAiCD;IACE,OAxCO,GAAA,YAAe,IAAA,IAAQ,CAAA,KAAE,CAAK,GAAC,CAAG,OAAC,EAAO,CAAE,CAAC;CAyCrD;;;;;AAKD,SAAA,eAAA,CApCC,KAAA,EAoCD;IACE,uBA3CM,IAAA,GAAO,IAAI,IAAA,CAAK,CAAC,CAAC,CAAC;IA4CzB,qBA3CI,MAAA,GAAS,CAAA,CAAE;IA4Cf,qBA3CI,KAAA,GAAQ,CAAA,CAAE;IA4Cd,uBA3CM,UAAA,GAAa,KAAA,CAAM,CAAC,CAAC,GAAG,IAAA,CAAK,cAAC,GAAgB,IAAA,CAAK,WAAC,CAAW;IA4CrE,uBA3CM,UAAA,GAAa,KAAA,CAAM,CAAC,CAAC,GAAG,IAAA,CAAK,WAAC,GAAa,IAAA,CAAK,QAAC,CAAQ;IA6C/D,IAAI,KA3CC,CAAK,CAAC,CAAC,EAAE;QA4CZ,MAAM,GA3CG,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,GAAG,KAAA,CAAM,EAAC,CAAE,CAAC,CAAC;QA4CrC,KAAK,GA3CG,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,GAAG,KAAA,CAAM,EAAC,CAAE,CAAC,CAAC;KA4CrC;IACD,UAAU,CA3CC,IAAC,CAAI,IAAC,EAAK,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,CAAC,EAAE,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,CAAC,GAAG,CAAA,EAAG,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IA4C7E,uBA3CM,CAAA,GAAI,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,IAAI,GAAA,CAAI,GAAG,MAAA,CAAO;IA4C1C,uBA3CM,CAAA,GAAI,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,IAAI,GAAA,CAAI,GAAG,KAAA,CAAM;IA4CzC,uBA3CM,CAAA,GAAI,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC,IAAI,GAAA,CAAI,CAAC;IA4CjC,uBA3CM,EAAA,GAAK,IAAA,CAAK,KAAC,CAAK,UAAC,CAAU,IAAC,IAAM,KAAE,CAAK,CAAC,CAAC,IAAI,CAAA,CAAE,CAAC,GAAG,IAAA,CAAK,CAAC;IA4CjE,UAAU,CA3CC,IAAC,CAAI,IAAC,EAAK,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,EAAA,CAAG,CAAC;IA4CnC,OA3CO,IAAA,CAAK;CA4Cb;;;;;AAKD,SAAA,KAAA,CAvCC,GAAA,EAuCD;IACE,OA9CO,QAAA,CAAS,GAAC,EAAI,EAAA,CAAG,CAAC;CA+C1B;;AD5ND;;;;;;;AASA,AACA,AACA,AAEA,MADM,qBAAA,GAAgC,IAAA,CAAK;;;;;;;;;;;;;;;;;;AAmB3C,AAAA,MAAA,cAAA,CAAA;;;;IAIA,WAAA,CAFsB,aAAe,EAErC;QAFsB,IAAtB,CAAA,aAAsB,GAAA,aAAA,CAAe;KAAe;;;;;;IAQpD,SANG,CAAA,KAAA,EAAA,SAAA,EAMH;QACI,IAAI,KANC,IAAQ,IAAA;YAAM,OAAO,EAAA,CAAG;QAQ7B,IAAI,OANO,SAAA,KAAc,QAAA,IAAY,SAAA,KAAc,IAAA,EAAM;YAOvD,MANM,wBAAA,CAAyB,cAAC,EAAe,SAAA,CAAU,CAAC;SAO3D;QAED,uBANM,GAAA,GAAM,iBAAA,CAAkB,KAAC,EAAM,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,EAAE,IAAA,CAAK,aAAC,CAAa,CAAC;QAQjF,OANO,SAAA,CAAU,GAAC,CAAG,CAAC,OAAC,CAAO,qBAAC,EAAsB,KAAA,CAAM,QAAC,EAAQ,CAAE,CAAC;KAOxE;;AALI,cAAP,CAAA,UAAO,GAAoC;IAO3C,EANE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,YAAA,EAAc,IAAA,EAAM,IAAA,EAAK,EAAC,EAAG;CAOxD,CANC;;;;AAED,cAAD,CAAA,cAAC,GAAA,MAAA;IASD,EAAC,IAAI,EAAE,cAAc,GAAG;CACvB,CAAC,AAGF,AAUC;;ADzED;;;;;;;AASA,AACA,AACA;;;;;;;;;;;;;;;;;AAiBA,AAAA,MAAA,cAAA,CAAA;;;;;;IAMA,SAJG,CAAA,KAAA,EAAA,OAAA,EAIH;QACI,IAAI,KAJC,IAAQ,IAAA;YAAM,OAAO,EAAA,CAAG;QAM7B,IAAI,OAJO,OAAA,KAAY,QAAA,IAAY,OAAO,KAAA,KAAU,QAAA,EAAU;YAK5D,MAJM,wBAAA,CAAyB,cAAC,EAAe,OAAA,CAAQ,CAAC;SAKzD;QAED,IAAI,OAJC,CAAO,cAAC,CAAc,KAAC,CAAK,EAAE;YAKjC,OAJO,OAAA,CAAQ,KAAC,CAAK,CAAC;SAKvB;QAED,IAAI,OAJC,CAAO,cAAC,CAAc,OAAC,CAAO,EAAE;YAKnC,OAJO,OAAA,CAAQ,OAAC,CAAO,CAAC;SAKzB;QAED,OAJO,EAAA,CAAG;KAKX;;AAHI,cAAP,CAAA,UAAO,GAAoC;IAK3C,EAJE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,YAAA,EAAc,IAAA,EAAM,IAAA,EAAK,EAAC,EAAG;CAKxD,CAJC;;;;AAED,cAAD,CAAA,cAAC,GAAA,MAAA,EAOA,CAAC,AAGF,AAQC;;ADrED;;;;;;;AASA,AACA;;;;;;;;;;;;;AAaA,AAAA,MAAA,QAAA,CAAA;;;;;IAKA,SAHG,CAAA,KAAA,EAGH,EAHkC,OAAO,IAAA,CAAK,SAAC,CAAS,KAAC,EAAM,IAAA,EAAM,CAAA,CAAE,CAAC,EAAC;;AAClE,QAAP,CAAA,UAAO,GAAoC;IAI3C,EAHE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,MAAA,EAAQ,IAAA,EAAM,KAAA,EAAM,EAAC,EAAG;CAInD,CAHC;;;;AAED,QAAD,CAAA,cAAC,GAAA,MAAA,EAMA,CAAC,AAGF,AAQC;;AD/CD;;;;;;;AASA,AACA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,AAAA,MAAA,SAAA,CAAA;;;;;;;IAOA,SAJG,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAIH;QACI,IAAI,KAJC,IAAQ,IAAA;YAAM,OAAO,KAAA,CAAM;QAMhC,IAAI,CAJC,IAAC,CAAI,QAAC,CAAQ,KAAC,CAAK,EAAE;YAKzB,MAJM,wBAAA,CAAyB,SAAC,EAAU,KAAA,CAAM,CAAC;SAKlD;QAED,OAJO,KAAA,CAAM,KAAC,CAAK,KAAC,EAAM,GAAA,CAAI,CAAC;KAKhC;;;;;IAFA,QAAA,CAAA,GAAA,EAAH,EAAwC,OAAO,OAAO,GAAA,KAAQ,QAAA,IAAY,KAAA,CAAM,OAAC,CAAO,GAAC,CAAG,CAAC,EAAC;;AACvF,SAAP,CAAA,UAAO,GAAoC;IAQ3C,EAPE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,CAAA,EAAE,IAAC,EAAK,OAAA,EAAS,IAAA,EAAM,KAAA,EAAM,EAAC,EAAG;CAQpD,CAPC;;;;AAED,SAAD,CAAA,cAAC,GAAA,MAAA,EAUA,CAAC,AAGF,AAQC;;AD9FD;;;;;;;;;;;;AAcA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAcA;;;AAGA,AACC,MAAA,YAAA,GAAA;IAAC,SAAS;IACT,aAAa;IACb,aAAa;IACb,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,aAAa;IACb,YAAY;IACZ,QAAQ;IACR,cAAc;IACd,cAAc;CACf,CAAC;;ADrDF;;;;;;;AASA,AAEA,AACA,AACA,AACA;;;;;AAKA,AAAA,MAAA,YAAA,CAAA;;AAKO,YAAP,CAAA,UAAO,GAAoC;IAH3C,EAIE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAHtB,YAAY,EAIE,CAAA,iBAAE,EAAkB,YAAA,CAAa;gBAH/C,OAAO,EAIE,CAAA,iBAAE,EAAkB,YAAA,CAAa;gBAH1C,SAAS,EAIE;oBAHT,EAAC,OAIC,EAAQ,cAAA,EAAgB,QAAA,EAAU,oBAAA,EAAqB;iBAH1D;aACF,EAIC,EAAG;CAHJ,CAIC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA,EADA,CAAC,AAGF,AAQC;;AD3CD;;;;;;;AAOA,AAAC,MAAA,mBAAA,GAAA,SAAA,CAAA;AACD,AAAO,MAAM,kBAAA,GAAqB,QAAA,CAAS;AAC3C,AAAO,MAAM,sBAAA,GAAyB,kBAAA,CAAmB;AACzD,AAAO,MAAM,qBAAA,GAAwB,iBAAA,CAAkB;;;;;;;AAOvD,AAAA,SAAA,iBAAA,CADC,UAAA,EACD;IACE,OADO,UAAA,KAAe,mBAAA,CAAoB;CAE3C;;;;;;;AAOD,AAAA,SAAA,gBAAA,CAFC,UAAA,EAED;IACE,OAFO,UAAA,KAAe,kBAAA,CAAmB;CAG1C;;;;;;;AAOD,AAAA,SAAA,mBAAA,CAHC,UAAA,EAGD;IACE,OAHO,UAAA,KAAe,sBAAA,CAAuB;CAI9C;;;;;;;AAOD,AAAA,SAAA,kBAAA,CAJC,UAAA,EAID;IACE,OAJO,UAAA,KAAe,qBAAA,CAAsB;CAK7C;;AD/CD;;;;;;;;;;;;AAeA,AACA;;;AAGA,AADC,MAAA,OAAA,GAAA,IAAA,OAAA,CAAA,mBAAA,CAAA,CAAA;;ADlBD;;;;;;;;;;;GAYG,AACH,AACA,AACA,AACA,AACA,AACA,AACA,AAAgB;;ADnBhB;;;;;;;;;;;;AAaA,AAAytB;0EAE/oB;;ADf1E;;GAEG,AAEH,AAEA,AACA,AAA2B;;"}