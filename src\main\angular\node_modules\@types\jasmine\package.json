{"_args": [["@types/jasmine@2.5.38", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@types/jasmine@2.5.38", "_id": "@types/jasmine@2.5.38", "_inBundle": false, "_integrity": "sha1-pDeRJMSSHU4h3lTsdGacnps1Zxc=", "_location": "/@types/jasmine", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/jasmine@2.5.38", "name": "@types/jasmine", "escapedName": "@types%2fjasmine", "scope": "@types", "rawSpec": "2.5.38", "saveSpec": null, "fetchSpec": "2.5.38"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/jasmine/-/jasmine-2.5.38.tgz", "_spec": "2.5.38", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "dependencies": {}, "description": "TypeScript definitions for Jasmine 2.5", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/jasmine", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typesPublisherContentHash": "deae5b77cefabcb3a692eff958b0cfe55516a87b535b4247349dbe4fc5f835f6", "typings": "index.d.ts", "version": "2.5.38"}