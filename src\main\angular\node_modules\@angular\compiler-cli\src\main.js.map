{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/main.ts"], "names": [], "mappings": ";;;AAWA,4BAA0B;AAG1B,0CAA4C;AAC5C,8CAAgD;AAEhD,qCAAwC;AAExC,iBACI,SAAqC,EAAE,UAA6B,EAAE,OAAmB,EACzF,IAAqB;IACvB,EAAE,CAAC,CAAC,SAAS,CAAC,qBAAqB,KAAK,SAAS,CAAC,CAAC,CAAC;QAClD,mBAAmB;QACnB,SAAS,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAC1C,CAAC;IACD,MAAM,CAAC,uBAAa,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;AAC9E,CAAC;AAED,cACI,IAAS,EAAE,YAAiD;IAAjD,6BAAA,EAAA,eAAoC,OAAO,CAAC,KAAK;IAC9D,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC;IAC9C,IAAM,UAAU,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAE/C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC;QACjE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,SAAS,IAAI,wBAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACtB,YAAY,CAAC,oBAAoB,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAfD,oBAeC;AAED,kBAAkB;AAClB,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,QAAgB,IAAK,OAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAtB,CAAsB,CAAC,CAAC;AAChE,CAAC", "sourcesContent": ["#!/usr/bin/env node\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n// Must be imported first, because Angular decorators throw on load.\nimport 'reflect-metadata';\n\nimport * as ts from 'typescript';\nimport * as tsc from '@angular/tsc-wrapped';\nimport {isSyntaxError} from '@angular/compiler';\n\nimport {CodeGenerator} from './codegen';\n\nfunction codegen(\n    ngOptions: tsc.AngularCompilerOptions, cliOptions: tsc.NgcCliOptions, program: ts.Program,\n    host: ts.CompilerHost) {\n  if (ngOptions.enableSummariesForJit === undefined) {\n    // default to false\n    ngOptions.enableSummariesForJit = false;\n  }\n  return CodeGenerator.create(ngOptions, cliOptions, program, host).codegen();\n}\n\nexport function main(\n    args: any, consoleError: (s: string) => void = console.error): Promise<number> {\n  const project = args.p || args.project || '.';\n  const cliOptions = new tsc.NgcCliOptions(args);\n\n  return tsc.main(project, cliOptions, codegen).then(() => 0).catch(e => {\n    if (e instanceof tsc.UserError || isSyntaxError(e)) {\n      consoleError(e.message);\n      return Promise.resolve(1);\n    } else {\n      consoleError(e.stack);\n      consoleError('Compilation failed');\n      return Promise.resolve(1);\n    }\n  });\n}\n\n// CLI entry point\nif (require.main === module) {\n  const args = require('minimist')(process.argv.slice(2));\n  main(args).then((exitCode: number) => process.exit(exitCode));\n}\n"]}