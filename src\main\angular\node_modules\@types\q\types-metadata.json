{"authors": "<PERSON><PERSON> <https://github.com/bnemetchek>, <PERSON> <https://github.com/Andrew<PERSON>/>, <PERSON> <https://github.com/johnnyreilly>", "definitionFilename": "index.d.ts", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": "0", "libraryMinorVersion": "0", "libraryName": "Q", "typingsPackageName": "q", "projectName": "https://github.com/kriskowal/q", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "UMD", "globals": ["Q"], "declaredModules": [], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "6597b89d57d9ceec9ef1e6af32e2fd02f4582abaf309f8a76f6746ee1af830d5"}