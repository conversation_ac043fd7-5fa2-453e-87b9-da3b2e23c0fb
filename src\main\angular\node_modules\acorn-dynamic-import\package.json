{"_args": [["acorn-dynamic-import@2.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "acorn-dynamic-import@2.0.2", "_id": "acorn-dynamic-import@2.0.2", "_inBundle": false, "_integrity": "sha1-x1K9IQvvZ5UBtsbLf8hPj0cVjMQ=", "_location": "/acorn-dynamic-import", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "acorn-dynamic-import@2.0.2", "name": "acorn-dynamic-import", "escapedName": "acorn-dynamic-import", "rawSpec": "2.0.2", "saveSpec": null, "fetchSpec": "2.0.2"}, "_requiredBy": ["/webpack"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/acorn-dynamic-import/-/acorn-dynamic-import-2.0.2.tgz", "_spec": "2.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/kesne/acorn-dynamic-import/issues"}, "dependencies": {"acorn": "^4.0.3"}, "description": "Support dynamic imports in acorn", "devDependencies": {"babel-cli": "^6.18.0", "babel-eslint": "^7.1.1", "babel-preset-airbnb": "^2.1.1", "babel-register": "^6.18.0", "chai": "^3.0.0", "eslint": "^3.10.2", "eslint-config-airbnb-base": "^10.0.1", "eslint-plugin-import": "^2.2.0", "in-publish": "^2.0.0", "mocha": "^2.2.5", "rimraf": "^2.5.4", "safe-publish-latest": "^1.1.1"}, "homepage": "https://github.com/kesne/acorn-dynamic-import", "license": "MIT", "main": "lib/index.js", "name": "acorn-dynamic-import", "repository": {"type": "git", "url": "git+https://github.com/kesne/acorn-dynamic-import.git"}, "scripts": {"build": "babel src --out-dir lib", "check-changelog": "expr $(git status --porcelain 2>/dev/null| grep \"^\\s*M.*CHANGELOG.md\" | wc -l) >/dev/null || (echo 'Please edit CHANGELOG.md' && exit 1)", "check-only-changelog-changed": "(expr $(git status --porcelain 2>/dev/null| grep -v \"CHANGELOG.md\" | wc -l) >/dev/null && echo 'Only CHANGELOG.md may have uncommitted changes' && exit 1) || exit 0", "lint": "eslint .", "postversion": "git commit package.json CHANGELOG.md -m \"v$npm_package_version\" && npm run tag && git push && git push --tags", "prepublish": "in-publish && safe-publish-latest && npm run build || not-in-publish", "preversion": "npm run test && npm run check-changelog && npm run check-only-changelog-changed", "tag": "git tag v$npm_package_version", "test": "npm run lint && npm run tests-only", "tests-only": "mocha", "version:major": "npm --no-git-tag-version version major", "version:minor": "npm --no-git-tag-version version minor", "version:patch": "npm --no-git-tag-version version patch"}, "version": "2.0.2"}