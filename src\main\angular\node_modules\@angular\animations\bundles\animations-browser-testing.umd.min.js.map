{"version": 3, "file": "animations-browser-testing.umd.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/animations/browser/src/render/shared.ts", "../../../../packages/animations/browser/testing/src/mock_animation_driver.ts", "../../../../packages/animations/browser/testing/src/testing.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationPlayer, NoopAnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../../src/render/animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../../src/render/shared';\n\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): MockAnimationPlayer {\n    const player =\n        new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: {[key: string]: string | number} = {};\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ɵStyleData = {};\n\n  constructor(\n      public element: any, public keyframes: {[key: string]: string | number}[],\n      public duration: number, public delay: number, public easing: string,\n      public previousPlayers: any[]) {\n    super();\n    previousPlayers.forEach(player => {\n      if (player instanceof MockAnimationPlayer) {\n        const styles = player.currentSnapshot;\n        Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n      }\n    });\n\n    this.totalTime = delay + duration;\n  }\n\n  /* @internal */\n  onInit(fn: () => any) { this._onInitFns.push(fn); }\n\n  /* @internal */\n  init() {\n    super.init();\n    this._onInitFns.forEach(fn => fn());\n    this._onInitFns = [];\n  }\n\n  finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /* @internal */\n  triggerMicrotask() {}\n\n  play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  hasStarted() { return this.__started; }\n\n  beforeDestroy() {\n    const captures: ɵStyleData = {};\n\n    Object.keys(this.previousStyles).forEach(prop => {\n      captures[prop] = this.previousStyles[prop];\n    });\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this.keyframes.forEach(kf => {\n        Object.keys(kf).forEach(prop => {\n          if (prop != 'offset') {\n            captures[prop] = this.__finished ? kf[prop] : AUTO_STYLE;\n          }\n        });\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {MockAnimationDriver, MockAnimationPlayer} from './mock_animation_driver';\n"], "names": ["exports", "module", "factory", "require", "extendStatics", "Object", "setPrototypeOf", "__proto__", "Array", "d", "b", "p", "hasOwnProperty", "_contains", "elm1", "elm2", "_query", "element", "selector", "multi", "Element", "contains", "prototype", "matches", "_matches", "proto", "fn_1", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "apply", "results", "push", "querySelectorAll", "elm", "querySelector", "MockAnimationDriver", "matchesElement", "containsElement", "query", "computeStyle", "prop", "defaultValue", "previousPlayers", "player", "MockAnimationPlayer", "keyframes", "duration", "delay", "easing", "log", "tslib_1.__extends", "_super", "_this", "call", "this", "__finished", "__started", "previousStyles", "currentSnapshot", "keys", "styles_1", "for<PERSON>ach", "__extends", "onInit", "fn", "_onInitFns", "init", "finish", "destroy", "triggerMicrotask", "play", "<PERSON><PERSON><PERSON><PERSON>", "hasStarted", "kf"], "mappings": ";;;;;0BAAA,gBAAAA,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,qjBAqBA,GAAIC,eAAJC,OAAAC,iBACAC,uBAAAC,QAA2C,SAA3CC,EAAAC,GAAAD,EAAAF,UAAAG,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,KCiHAE,UAAA,SAAAC,KAAAC,MAAA,OAAA,iDACAC,OAAA,SAAAC,QAAAC,SAAAC,OACA,SAEA,IAAA,mBAAAC,SAAA,CAGA,GADAP,UAAA,SAAAC,KAAAC,MAAA,MAAAD,MAAAO,SAAAN,OACAK,QAAAE,UAAAC,QAEAC,SAAA,SAAAP,QAA0DC,UAA1D,MAAAD,SAAAM,QAAAL,eAEA,CACA,GAAAO,OAAAL,QAAAE,UACAI,KAAAD,MAAAE,iBAAAF,MAAAG,oBAAAH,MAAAI,mBAAAJ,MAAAK,kBAAAL,MAAAM,qBACML,QACAF,SAAN,SAAAP,QAAAC,UAAA,MAAAQ,MAAAM,MAAAf,SAAAC,aAGAF,OAAA,SAAAC,QAAAC,SAAAC,OACA,GAAAc,WACA,IAAAd,MACAc,QAAAC,KAAAF,MAAAC,QAAAhB,QAAAkB,iBAAAjB,eAGA,CACA,GAAAkB,KAAAnB,QAAAoB,cAAAnB,eCjKAe,QAAAC,KAAAE,8FA6BAE,oBAAA,WACA,QAAAA,8BAGAA,qBAAAhB,UAAAiB,eAAA,SAAAtB,QAAAC,UAGA,MAAAqB,gBAAAtB,QAAAC,WAGAoB,oBAAAhB,UAAAkB,gBAAA,SAAA1B,KAAAC,MAAA,MAAAyB,iBAAA1B,KAAAC,OACAuB,oBAAAhB,UAAAmB,MAAA,SAAAxB,QAAAC,SAAAC,mDAvBAmB,oBAAAhB,UAAAoB,aAAA,SAAAzB,QAAA0B,KAAAC,+IA6BA,SAAAC,kBAAAA,mBAAA,IAAAC,QAAA,GAAAC,qBAAA9B,QAAA+B,UAAAC,SAAAC,MAAAC,OAAAN,uBAOAP,qBAAAc,IAAAlB,KAAAY,sCAEAO,qBAAAA,MARA,IAAAN,qBAAA,SAAAO,QAEA,QAAAP,qBAAA9B,QAAA+B,UAAAC,SAAAC,MAAAC,OAAAN,iBACU,GAAVU,OAAAD,OAAAE,KAAAC,OAAAA,IA4BA,OA3BSF,OAATtC,QAAAA,QAOIsC,MAAJP,UAAAA,UACAO,MAAAN,SAAAA,SACAM,MAAAL,MAAAA,MACAK,MAAAJ,OAAAA,OACAI,MAAOV,gBAAPA,gBACAU,MAAAG,YAAA,EAEAH,MAAAI,WAAA,EACAJ,MAAAK,sCAGAL,MAAAM,4DAGA,GAAAf,iBAAAC,qBAAA,oCACA1C,QAAAyD,KAAAC,UAAAC,QAAA,SAAArB,MAAA,MAAAY,OAAAK,eAAAjB,MAAAoB,SAAApB,0CAKAY,YA9BAU,WAAAlB,oBAAAO,QAiCAP,oBAAAzB,UAAA4C,OAAA,SAAAC,IAAAV,KAAAW,WAAAlC,KAAAiC,KAGApB,oBAAAzB,UAAA+C,KAAA,WACIf,OAAJhC,UAAA+C,KAAAb,KAAAC,MACAA,KAAAW,WAAAJ,QAAA,SAAAG,IAAA,MAAAA,4BAKApB,oBAAAzB,UAAAgD,OAAA,WACIhB,OAAJhC,UAAAgD,OAAAd,KAAAC,MACAA,KAAAC,YAAA,GAGEX,oBAAFzB,UAAAiD,QAAA,WAEAjB,OAAAhC,UAAAiD,QAAAf,KAAAC,MACIA,KAAJC,YAAA,GAIAX,oBAAAzB,UAAAkD,iBAAA,aAEAzB,oBAAAzB,UAAAmD,KAAA,WAAAnB,OAAAhC,UAAAmD,KAAAjB,KAAAC,oGAIAV,oBAAAzB,UAAAoD,cAAA,WACA,GAAAnB,OAAAE,qHAKAA,KAAAkB,cAKAlB,KAAAT,UAAAgB,QAAA,SAAAY,2CCxHA,UAAAjC"}