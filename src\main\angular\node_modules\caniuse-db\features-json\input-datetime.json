{"title": "Date and time input types", "description": "Form field widget to easily allow users to enter a date, time or both, generally by using a calendar/time input widget.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#date-state-(type=date)", "status": "ls", "links": [{"url": "http://net.tutsplus.com/tutorials/javascript-ajax/quick-tip-cross-browser-datepickers-within-minutes/", "title": "Datepicker tutorial w/polyfill"}, {"url": "https://github.com/zoltan-dulac/html5Forms.js", "title": "Polyfill for HTML5 forms"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/form.js#input-type-datetime-local", "title": "has.js test"}, {"url": "https://www.webplatform.org/docs/html/elements/input/type/date", "title": "WebPlatform Docs"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=888320", "title": "Bug on Firefox support"}], "bugs": [{"description": "Chrome for Android grays out dates that are out of range (using `min` and `max` attributes), but does not prevent the user from selecting these dates."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "a #1", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n d #4", "54": "n d #4", "55": "n d #4", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #2", "6.0-6.1": "a #2", "7.0-7.1": "a #2", "8": "a #2", "8.1-8.4": "a #2", "9.0-9.2": "a #2", "9.3": "a #2", "10.0-10.2": "a #2", "10.3": "a #2", "11": "a #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n #3", "4.1": "n #3", "4.2-4.3": "n #3", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "n", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Older versions of Safari provide date-formatted text fields, but no real calendar widget.\r\n\r\nChrome, Opera, and Microsoft Edge also support the `datetime-local` type for both date and time.\r\n\r\nThere used to also be a `datetime` type, but it was [dropped from the HTML spec](https://github.com/whatwg/html/issues/336) since only Presto ever fully supported it.", "notes_by_num": {"1": "Partial support in Microsoft Edge refers to supporting `date`, `week`, and `month` input types, and not `time` and `datetime-local`.", "2": "Partial support in iOS Safari refers to not supporting `min`, `max` or `step` attributes", "3": "Some modified versions of the Android 4.x browser do have support for date/time fields.", "4": "Can be enabled in Firefox using the `dom.forms.datetime` flag."}, "usage_perc_y": 71.63, "usage_perc_a": 10.29, "ucprefix": false, "parent": "forms", "keywords": "datepicker,timepicker,input type=\"date\",input type=\"time\",input date,datetime-local", "ie_id": "daterelatedinputtypes,timerelatedinputtypes", "chrome_id": "6640933999214592", "firefox_id": "", "webkit_id": "", "shown": true}