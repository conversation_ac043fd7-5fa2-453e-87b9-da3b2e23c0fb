[{"__symbolic": "module", "version": 3, "metadata": {"XmlParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parser", "name": "<PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "parse": [{"__symbolic": "method"}]}}}, "exports": [{"from": "./parser", "export": ["ParseTreeResult", "TreeError"]}]}, {"__symbolic": "module", "version": 1, "metadata": {"XmlParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parser", "name": "<PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "parse": [{"__symbolic": "method"}]}}}, "exports": [{"from": "./parser", "export": ["ParseTreeResult", "TreeError"]}]}]