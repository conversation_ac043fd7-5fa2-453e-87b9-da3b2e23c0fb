{"_args": [["browserify-rsa@4.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-rsa@4.0.1", "_id": "browserify-rsa@4.0.1", "_inBundle": false, "_integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "_location": "/browserify-rsa", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-rsa@4.0.1", "name": "browserify-rsa", "escapedName": "browserify-rsa", "rawSpec": "4.0.1", "saveSpec": null, "fetchSpec": "4.0.1"}, "_requiredBy": ["/browserify-sign", "/public-encrypt"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "_spec": "4.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": "", "bugs": {"url": "https://github.com/crypto-browserify/browserify-rsa/issues"}, "dependencies": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}, "description": "RSA for browserify", "devDependencies": {"parse-asn1": "^5.0.0", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "homepage": "https://github.com/crypto-browserify/browserify-rsa#readme", "license": "MIT", "main": "index.js", "name": "browserify-rsa", "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/browserify-rsa.git"}, "scripts": {"test": "node test.js | tspec"}, "version": "4.0.1"}