{"_args": [["base64id@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "base64id@1.0.0", "_id": "base64id@1.0.0", "_inBundle": false, "_integrity": "sha1-R2iMuZu2gE8OBtPnY7HDLlfY5rY=", "_location": "/base64id", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64id@1.0.0", "name": "base64id", "escapedName": "base64id", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/engine.io"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/base64id/-/base64id-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "fael<PERSON><EMAIL>"}, "bugs": {"url": "https://github.com/faeldt/base64id/issues"}, "description": "Generates a base64 id", "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/faeldt/base64id#readme", "license": "MIT", "main": "./lib/base64id.js", "name": "base64id", "repository": {"type": "git", "url": "git+https://github.com/faeldt/base64id.git"}, "version": "1.0.0"}