[{"__symbolic": "module", "version": 3, "metadata": {"browserDetection": {"__symbolic": "error", "message": "Variable not initialized", "line": 11, "character": 11}, "BrowserDetection": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}]}}, "dispatchEvent": {"__symbolic": "function"}, "el": {"__symbolic": "function", "parameters": ["html"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "<PERSON><PERSON><PERSON><PERSON>"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "content"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "createTemplate"}, "arguments": [{"__symbolic": "reference", "name": "html"}]}]}]}}, "normalizeCSS": {"__symbolic": "function", "parameters": ["css"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 88, "character": 15}}, "stringifyElement": {"__symbolic": "function"}, "createNgZone": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, "arguments": [{"enableLongStackTrace": true}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"browserDetection": {"__symbolic": "error", "message": "Variable not initialized", "line": 11, "character": 11}, "BrowserDetection": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}]}}, "dispatchEvent": {"__symbolic": "function"}, "el": {"__symbolic": "function", "parameters": ["html"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "<PERSON><PERSON><PERSON><PERSON>"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "content"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵgetDOM"}}, "member": "createTemplate"}, "arguments": [{"__symbolic": "reference", "name": "html"}]}]}]}}, "normalizeCSS": {"__symbolic": "function", "parameters": ["css"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 88, "character": 15}}, "stringifyElement": {"__symbolic": "function"}, "createNgZone": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, "arguments": [{"enableLongStackTrace": true}]}}}}]