{"version": 3, "file": "router-testing.umd.min.js", "sources": ["../../../../packages/router/testing/src/router_testing_module.ts", "../../../../packages/router/testing/src/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {MockLocationStrategy, SpyLocation} from '@angular/common/testing';\nimport {Compiler, Injectable, Injector, ModuleWithProviders, NgModule, NgModuleFactory, NgModuleFactoryLoader, Optional} from '@angular/core';\nimport {ChildrenOutletContexts, NoPreloading, PreloadingStrategy, ROUTES, Route, Router, RouterModule, Routes, UrlHandlingStrategy, UrlSerializer, provideRoutes, ɵROUTER_PROVIDERS as ROUTER_PROVIDERS, ɵflatten as flatten} from '@angular/router';\n\n\n\n/**\n * @whatItDoes Allows to simulate the loading of ng modules in tests.\n *\n * @howToUse\n *\n * ```\n * const loader = TestBed.get(NgModuleFactoryLoader);\n *\n * @Component({template: 'lazy-loaded'})\n * class LazyLoadedComponent {}\n * @NgModule({\n *   declarations: [LazyLoadedComponent],\n *   imports: [RouterModule.forChild([{path: 'loaded', component: LazyLoadedComponent}])]\n * })\n *\n * class LoadedModule {}\n *\n * // sets up stubbedModules\n * loader.stubbedModules = {lazyModule: LoadedModule};\n *\n * router.resetConfig([\n *   {path: 'lazy', loadChildren: 'lazyModule'},\n * ]);\n *\n * router.navigateByUrl('/lazy/loaded');\n * ```\n *\n * @stable\n */\n\nexport class SpyNgModuleFactoryLoader implements NgModuleFactoryLoader {\n  /**\n   * @docsNotRequired\n   */\n  private _stubbedModules: {[path: string]: Promise<NgModuleFactory<any>>} = {};\n\n  /**\n   * @docsNotRequired\n   */\n  set stubbedModules(modules: {[path: string]: any}) {\n    const res: {[path: string]: any} = {};\n    for (const t of Object.keys(modules)) {\n      res[t] = this.compiler.compileModuleAsync(modules[t]);\n    }\n    this._stubbedModules = res;\n  }\n\n  /**\n   * @docsNotRequired\n   */\n  get stubbedModules(): {[path: string]: any} { return this._stubbedModules; }\n\n  constructor(private compiler: Compiler) {}\n\n  load(path: string): Promise<NgModuleFactory<any>> {\n    if (this._stubbedModules[path]) {\n      return this._stubbedModules[path];\n    } else {\n      return <any>Promise.reject(new Error(`Cannot find module ${path}`));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Compiler, },\n];\n}\n\n/**\n * Router setup factory function used for testing.\n *\n * @stable\n */\nexport function setupTestingRouter(\n    urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts, location: Location,\n    loader: NgModuleFactoryLoader, compiler: Compiler, injector: Injector, routes: Route[][],\n    urlHandlingStrategy?: UrlHandlingStrategy) {\n  const router = new Router(\n      null !, urlSerializer, contexts, location, injector, loader, compiler, flatten(routes));\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n  return router;\n}\n\n/**\n * @whatItDoes Sets up the router to be used for testing.\n *\n * @howToUse\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}])]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @description\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of {@link Location}, {@link LocationStrategy}, and {@link\n * NgModuleFactoryLoader}.\n *\n * @stable\n */\n\nexport class RouterTestingModule {\n  static withRoutes(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterTestingModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [RouterModule],\n  providers: [\n    ROUTER_PROVIDERS, {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n    {provide: NgModuleFactoryLoader, useClass: SpyNgModuleFactoryLoader}, {\n      provide: Router,\n      useFactory: setupTestingRouter,\n      deps: [\n        UrlSerializer, ChildrenOutletContexts, Location, NgModuleFactoryLoader, Compiler, Injector,\n        ROUTES, [UrlHandlingStrategy, new Optional()]\n      ]\n    },\n    {provide: PreloadingStrategy, useExisting: NoPreloading}, provideRoutes([])\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\nexport * from './router_testing_module';\n"], "names": ["SpyNgModuleFactoryLoader", "compiler", "this", "get", "_stubbedModules", "res", "_i", "_a", "Object", "keys", "modules", "length", "enumerable", "prototype", "load", "path", "Promise", "reject", "Error", "RouterTestingModule", "ROUTER_PROVIDERS", "with<PERSON>out<PERSON>", "routes", "ngModule", "providers", "_angular_router", "provideRoutes", "decorators", "type", "_angular_core", "NgModule", "args", "exports", "RouterModule", "ɵROUTER_PROVIDERS", "provide", "_angular_common", "Location", "useClass", "_angular_common_testing", "SpyLocation", "UrlSerializer", "ROUTES", "Router", "deps", "UrlHandlingStrategy", "Optional"], "mappings": ";;;;;;;;;;;;;;;;;wCAsDA,QAAAA,0BAAAC,UACAC,KAAAD,SAAAA,mHAKAE,IAAA,WAAA,MAAAD,MAAAE,uCASA,IAAA,GAJAC,QAIAC,GAAA,EAAAC,GAAAC,OAAAC,KAAAC,SAAAJ,GAAAC,GAAAI,OAAAL,KAAA,kEAIAJ,KAAAE,gBAAAC,KAEAO,YAAA,oBAEAZ,yBAAAa,UAAAC,KAAA,SAAAC,MACA,MAAAb,MAAAE,gBAAAW,iCAGAC,QAAAC,OAAA,GAAAC,OAAA,sBAAAH,mMAsDA,IAAAI,qBAAA,WACAC,QAAAA,wBAIA,MAHAD,qBAAAE,WAAA,SAAAC,QACA,OAAAC,SAAAJ,oBAAAK,WAAAC,gBAAAC,cAAAJ,WAEAH,sBAEAA,qBAAAQ,aACAC,KAAAC,cAAAC,SAAAC,OACAC,SAAAP,gBAAAQ,cACAT,WACAC,gBAAAS,mBAAAC,QAAAC,gBAAAC,SAAAC,SAAAC,wBAAAC,cACAC,QAAAA,gBAAAA,iBAAAA,SAAAA,wBAAAJ,uBACAK,QAAAA,cAAAA,sBAAAA,SAAAA,2BACAP,QAAAV,gBAAAkB,qCAEAC,sLCvJAnB,gBAAAiB,QAAAjB,gBAAAoB,oBAAA,GAAAhB,eAAAiB"}