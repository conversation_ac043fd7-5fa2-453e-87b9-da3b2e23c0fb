[{"__symbolic": "module", "version": 3, "metadata": {"MODULE_SUFFIX": "", "camelCaseToDashCase": {"__symbolic": "function", "parameters": ["input"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 14, "character": 6, "context": {"name": "CAMEL_CASE_REGEXP"}}}, "dashCaseToCamelCase": {"__symbolic": "function", "parameters": ["input"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 15, "character": 6, "context": {"name": "DASH_CASE_REGEXP"}}}, "splitAtColon": {"__symbolic": "function", "parameters": ["input", "defaultValues"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 33, "character": 9, "context": {"name": "_splitAt"}}}, "splitAtPeriod": {"__symbolic": "function", "parameters": ["input", "defaultValues"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 33, "character": 9, "context": {"name": "_splitAt"}}}, "visitValue": {"__symbolic": "function"}, "isDefined": {"__symbolic": "function", "parameters": ["val"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "val"}, "right": null}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "val"}, "right": {"__symbolic": "reference", "name": "undefined"}}}}, "noUndefined": {"__symbolic": "function", "parameters": ["val"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "val"}, "right": {"__symbolic": "reference", "name": "undefined"}}, "thenExpression": {"__symbolic": "error", "message": "Expression form not supported", "line": 61, "character": 29}, "elseExpression": {"__symbolic": "reference", "name": "val"}}}, "ValueVisitor": {"__symbolic": "interface"}, "ValueTransformer": {"__symbolic": "class", "members": {"visitArray": [{"__symbolic": "method"}], "visitStringMap": [{"__symbolic": "method"}], "visitPrimitive": [{"__symbolic": "method"}], "visitOther": [{"__symbolic": "method"}]}}, "SyncAsync": {"__symbolic": "error", "message": "Function call not supported", "line": 87, "character": 14}, "syntaxError": {"__symbolic": "function"}, "isSyntaxError": {"__symbolic": "function", "parameters": ["error"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 110, "character": 10}}, "escapeRegExp": {"__symbolic": "function", "parameters": ["s"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 114, "character": 19}}, "utf8Encode": {"__symbolic": "function"}, "OutputContext": {"__symbolic": "interface"}}}, {"__symbolic": "module", "version": 1, "metadata": {"MODULE_SUFFIX": "", "camelCaseToDashCase": {"__symbolic": "function", "parameters": ["input"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 14, "character": 6, "context": {"name": "CAMEL_CASE_REGEXP"}}}, "dashCaseToCamelCase": {"__symbolic": "function", "parameters": ["input"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 15, "character": 6, "context": {"name": "DASH_CASE_REGEXP"}}}, "splitAtColon": {"__symbolic": "function", "parameters": ["input", "defaultValues"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 33, "character": 9, "context": {"name": "_splitAt"}}}, "splitAtPeriod": {"__symbolic": "function", "parameters": ["input", "defaultValues"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 33, "character": 9, "context": {"name": "_splitAt"}}}, "visitValue": {"__symbolic": "function"}, "isDefined": {"__symbolic": "function", "parameters": ["val"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "val"}, "right": null}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "val"}, "right": {"__symbolic": "reference", "name": "undefined"}}}}, "noUndefined": {"__symbolic": "function", "parameters": ["val"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "val"}, "right": {"__symbolic": "reference", "name": "undefined"}}, "thenExpression": {"__symbolic": "error", "message": "Expression form not supported", "line": 61, "character": 29}, "elseExpression": {"__symbolic": "reference", "name": "val"}}}, "ValueVisitor": {"__symbolic": "interface"}, "ValueTransformer": {"__symbolic": "class", "members": {"visitArray": [{"__symbolic": "method"}], "visitStringMap": [{"__symbolic": "method"}], "visitPrimitive": [{"__symbolic": "method"}], "visitOther": [{"__symbolic": "method"}]}}, "SyncAsync": {"__symbolic": "error", "message": "Function call not supported", "line": 87, "character": 14}, "syntaxError": {"__symbolic": "function"}, "isSyntaxError": {"__symbolic": "function", "parameters": ["error"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 110, "character": 10}}, "escapeRegExp": {"__symbolic": "function", "parameters": ["s"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 114, "character": 19}}, "utf8Encode": {"__symbolic": "function"}, "OutputContext": {"__symbolic": "interface"}}}]