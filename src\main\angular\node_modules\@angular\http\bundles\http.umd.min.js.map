{"version": 3, "file": "http.umd.min.js", "sources": ["../../../../packages/http/src/static_request.ts", "../../../../packages/http/src/http.ts", "../../../../packages/http/src/http_module.ts", "../../../../packages/http/src/base_request_options.ts", "../../../../packages/http/src/backends/xhr_backend.ts", "../../../../packages/http/src/backends/jsonp_backend.ts", "../../../../packages/http/src/http_utils.ts", "../../../../packages/http/src/url_search_params.ts", "../../../../packages/http/src/body.ts", "../../../../packages/http/src/static_response.ts", "../../../../packages/http/src/backends/browser_jsonp.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/http/src/enums.ts", "../../../../packages/http/src/headers.ts", "../../../../packages/http/src/base_response_options.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Body} from './body';\nimport {ContentType, RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates `Request` instances from provided values.\n * \n * The Request's interface is inspired by the Request constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#request-class),\n * but is considered a static value whose body can be accessed many times. There are other\n * differences in the implementation, but this is the most significant.\n * \n * `Request` instances are typically created by higher-level classes, like {\\@link Http} and\n * {\\@link Jsonp}, but it may occasionally be useful to explicitly create `Request` instances.\n * One such example is when creating services that wrap higher-level services, like {\\@link Http},\n * where it may be useful to generate a `Request` with arbitrary headers and search params.\n * \n * ```typescript\n * import {Injectable, Injector} from '\\@angular/core';\n * import {HTTP_PROVIDERS, Http, Request, RequestMethod} from '\\@angular/http';\n * \n * \\@Injectable() \n * class AutoAuthenticator {\n *   constructor(public http:Http) {}\n *   request(url:string) {\n *     return this.http.request(new Request({\n *       method: RequestMethod.Get,\n *       url: url,\n *       search: 'password=123'\n *     }));\n *   }\n * }\n * \n * var injector = Injector.resolveAndCreate([HTTP_PROVIDERS, AutoAuthenticator]);\n * var authenticator = injector.get(AutoAuthenticator);\n * authenticator.request('people.json').subscribe(res => {\n *   //URL should have included '?password=123'\n *   console.log('people', res.json());\n * });\n * ```\n * \n * \\@experimental\n */\nexport class Request extends Body {\n/**\n * Http method with which to perform the request.\n */\nmethod: RequestMethod;\n/**\n * {\\@link Headers} instance\n */\nheaders: Headers;\n/**\n * Url of the remote resource\n */\nurl: string;\n/**\n * Type of the request body *\n */\nprivate contentType: ContentType;\n/**\n * Enable use credentials\n */\nwithCredentials: boolean;\n/**\n * Buffer to store the response\n */\nresponseType: ResponseContentType;\n/**\n * @param {?} requestOptions\n */\nconstructor(requestOptions: RequestArgs) {\n    super();\n    // TODO: assert that url is present\n    const url = requestOptions.url;\n    this.url = requestOptions.url !;\n    const paramsArg = requestOptions.params || requestOptions.search;\n    if (paramsArg) {\n      let params: string;\n      if (typeof paramsArg === 'object' && !(paramsArg instanceof URLSearchParams)) {\n        params = urlEncodeParams(paramsArg).toString();\n      } else {\n        params = paramsArg.toString();\n      }\n      if (params.length > 0) {\n        let prefix = '?';\n        if (this.url.indexOf('?') != -1) {\n          prefix = (this.url[this.url.length - 1] == '&') ? '' : '&';\n        }\n        // TODO: just delete search-query-looking string in url?\n        this.url = url + prefix + params;\n      }\n    }\n    this._body = requestOptions.body;\n    this.method = normalizeMethodName(requestOptions.method !);\n    // TODO(jeffbcross): implement behavior\n    // Defaults to 'omit', consistent with browser\n    this.headers = new Headers(requestOptions.headers);\n    this.contentType = this.detectContentType();\n    this.withCredentials = requestOptions.withCredentials !;\n    this.responseType = requestOptions.responseType !;\n  }\n/**\n * Returns the content type enum based on header options.\n * @return {?}\n */\ndetectContentType(): ContentType {\n    switch (this.headers.get('content-type')) {\n      case 'application/json':\n        return ContentType.JSON;\n      case 'application/x-www-form-urlencoded':\n        return ContentType.FORM;\n      case 'multipart/form-data':\n        return ContentType.FORM_DATA;\n      case 'text/plain':\n      case 'text/html':\n        return ContentType.TEXT;\n      case 'application/octet-stream':\n        return this._body instanceof ArrayBuffer ? ContentType.ARRAY_BUFFER : ContentType.BLOB;\n      default:\n        return this.detectContentTypeFromBody();\n    }\n  }\n/**\n * Returns the content type of request's body based on its type.\n * @return {?}\n */\ndetectContentTypeFromBody(): ContentType {\n    if (this._body == null) {\n      return ContentType.NONE;\n    } else if (this._body instanceof URLSearchParams) {\n      return ContentType.FORM;\n    } else if (this._body instanceof FormData) {\n      return ContentType.FORM_DATA;\n    } else if (this._body instanceof Blob) {\n      return ContentType.BLOB;\n    } else if (this._body instanceof ArrayBuffer) {\n      return ContentType.ARRAY_BUFFER;\n    } else if (this._body && typeof this._body === 'object') {\n      return ContentType.JSON;\n    } else {\n      return ContentType.TEXT;\n    }\n  }\n/**\n * Returns the request's body according to its type. If body is undefined, return\n * null.\n * @return {?}\n */\ngetBody(): any {\n    switch (this.contentType) {\n      case ContentType.JSON:\n        return this.text();\n      case ContentType.FORM:\n        return this.text();\n      case ContentType.FORM_DATA:\n        return this._body;\n      case ContentType.TEXT:\n        return this.text();\n      case ContentType.BLOB:\n        return this.blob();\n      case ContentType.ARRAY_BUFFER:\n        return this.arrayBuffer();\n      default:\n        return null;\n    }\n  }\n}\n\nfunction Request_tsickle_Closure_declarations() {\n/**\n * Http method with which to perform the request.\n * @type {?}\n */\nRequest.prototype.method;\n/**\n * {\\@link Headers} instance\n * @type {?}\n */\nRequest.prototype.headers;\n/**\n * Url of the remote resource\n * @type {?}\n */\nRequest.prototype.url;\n/**\n * Type of the request body *\n * @type {?}\n */\nRequest.prototype.contentType;\n/**\n * Enable use credentials\n * @type {?}\n */\nRequest.prototype.withCredentials;\n/**\n * Buffer to store the response\n * @type {?}\n */\nRequest.prototype.responseType;\n}\n\n/**\n * @param {?} params\n * @return {?}\n */\nfunction urlEncodeParams(params: {[key: string]: any}): URLSearchParams {\n  const /** @type {?} */ searchParams = new URLSearchParams();\n  Object.keys(params).forEach(key => {\n    const /** @type {?} */ value = params[key];\n    if (value && Array.isArray(value)) {\n      value.forEach(element => searchParams.append(key, element.toString()));\n    } else {\n      searchParams.append(key, value.toString());\n    }\n  });\n  return searchParams;\n}\n\nconst /** @type {?} */ noop = function() {};\nconst /** @type {?} */ w = typeof window == 'object' ? window : noop;\nconst /** @type {?} */ FormData = ( /** @type {?} */((w as any)) /** TODO #9100 */)['FormData'] || noop;\nconst /** @type {?} */ Blob = ( /** @type {?} */((w as any)) /** TODO #9100 */)['Blob'] || noop;\nexport const /** @type {?} */ ArrayBuffer: ArrayBufferConstructor =\n    ( /** @type {?} */((w as any)) /** TODO #9100 */)['ArrayBuffer'] || noop;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\n\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {RequestMethod} from './enums';\nimport {ConnectionBackend, RequestArgs, RequestOptionsArgs} from './interfaces';\nimport {Request} from './static_request';\nimport {Response} from './static_response';\n/**\n * @param {?} backend\n * @param {?} request\n * @return {?}\n */\nfunction httpRequest(backend: ConnectionBackend, request: Request): Observable<Response> {\n  return backend.createConnection(request).response;\n}\n/**\n * @param {?} defaultOpts\n * @param {?} providedOpts\n * @param {?} method\n * @param {?} url\n * @return {?}\n */\nfunction mergeOptions(\n    defaultOpts: BaseRequestOptions, providedOpts: RequestOptionsArgs | undefined,\n    method: RequestMethod, url: string): RequestArgs {\n  const /** @type {?} */ newOptions = defaultOpts;\n  if (providedOpts) {\n    // Hack so Dart can used named parameters\n    return /** @type {?} */(( newOptions.merge(new RequestOptions({\n      method: providedOpts.method || method,\n      url: providedOpts.url || url,\n      search: providedOpts.search,\n      params: providedOpts.params,\n      headers: providedOpts.headers,\n      body: providedOpts.body,\n      withCredentials: providedOpts.withCredentials,\n      responseType: providedOpts.responseType\n    })) as RequestArgs));\n  }\n\n  return /** @type {?} */(( newOptions.merge(new RequestOptions({method, url})) as RequestArgs));\n}\n/**\n * Performs http requests using `XMLHttpRequest` as the default backend.\n * \n * `Http` is available as an injectable class, with methods to perform http requests. Calling\n * `request` returns an `Observable` which will emit a single {\\@link Response} when a\n * response is received.\n * \n * ### Example\n * \n * ```typescript\n * import {Http, HTTP_PROVIDERS} from '\\@angular/http';\n * import 'rxjs/add/operator/map'\n * \\@Component({ \n *   selector: 'http-app',\n *   viewProviders: [HTTP_PROVIDERS],\n *   templateUrl: 'people.html'\n * })\n * class PeopleComponent {\n *   constructor(http: Http) {\n *     http.get('people.json')\n *       // Call map on the response observable to get the parsed people object\n *       .map(res => res.json())\n *       // Subscribe to the observable to get the parsed people object and attach it to the\n *       // component\n *       .subscribe(people => this.people = people);\n *   }\n * }\n * ```\n * \n * \n * ### Example\n * \n * ```\n * http.get('people.json').subscribe((res:Response) => this.people = res.json());\n * ```\n * \n * The default construct used to perform requests, `XMLHttpRequest`, is abstracted as a \"Backend\" (\n * {\\@link XHRBackend} in this case), which could be mocked with dependency injection by replacing\n * the {\\@link XHRBackend} provider, as in the following example:\n * \n * ### Example\n * \n * ```typescript\n * import {BaseRequestOptions, Http} from '\\@angular/http';\n * import {MockBackend} from '\\@angular/http/testing';\n * var injector = Injector.resolveAndCreate([\n *   BaseRequestOptions,\n *   MockBackend,\n *   {provide: Http, useFactory:\n *       function(backend, defaultOptions) {\n *         return new Http(backend, defaultOptions);\n *       },\n *       deps: [MockBackend, BaseRequestOptions]}\n * ]);\n * var http = injector.get(Http);\n * http.get('request-from-mock-backend.json').subscribe((res:Response) => doSomething(res));\n * ```\n * \n * \\@experimental\n */\nexport class Http {\n/**\n * @param {?} _backend\n * @param {?} _defaultOptions\n */\nconstructor(protected _backend: ConnectionBackend, protected _defaultOptions: RequestOptions) {}\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      responseObservable = httpRequest(\n          this._backend,\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url)))));\n    } else if (url instanceof Request) {\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\n/**\n * Performs a request with `get` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nget(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, url)));\n  }\n/**\n * Performs a request with `post` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npost(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Post,\n        url)));\n  }\n/**\n * Performs a request with `put` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\nput(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Put,\n        url)));\n  }\n/**\n * Performs a request with `delete` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\ndelete (url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Delete, url)));\n  }\n/**\n * Performs a request with `patch` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npatch(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Patch,\n        url)));\n  }\n/**\n * Performs a request with `head` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nhead(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Head, url)));\n  }\n/**\n * Performs a request with `options` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\noptions(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Options, url)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Http_tsickle_Closure_declarations() {\n/** @type {?} */\nHttp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttp.ctorParameters;\n/** @type {?} */\nHttp.prototype._backend;\n/** @type {?} */\nHttp.prototype._defaultOptions;\n}\n\n/**\n * \\@experimental\n */\nexport class Jsonp extends Http {\n/**\n * @param {?} backend\n * @param {?} defaultOptions\n */\nconstructor(backend: ConnectionBackend, defaultOptions: RequestOptions) {\n    super(backend, defaultOptions);\n  }\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * \n * \\@security Regular XHR is the safest alternative to JSONP for most applications, and is\n * supported by all current browsers. Because JSONP creates a `<script>` element with\n * contents retrieved from a remote source, attacker-controlled data introduced by an untrusted\n * source could expose your application to XSS risks. Data exposed by JSONP may also be\n * readable by malicious third-party websites. In addition, JSONP introduces potential risk for\n * future security issues (e.g. content sniffing).  For more detail, see the\n * [Security Guide](http://g.co/ng/security).\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      url =\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url))));\n    }\n    if (url instanceof Request) {\n      if (url.method !== RequestMethod.Get) {\n        throw new Error('JSONP requests must use GET request method.');\n      }\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Jsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * The http module provides services to perform http requests. To get started, see the {@link Http}\n * class.\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {BrowserJsonp} from './backends/browser_jsonp';\nimport {BrowserXhr} from './backends/browser_xhr';\nimport {JSONPBackend, JSONPBackend_} from './backends/jsonp_backend';\nimport {CookieXSRFStrategy, XHRBackend} from './backends/xhr_backend';\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {BaseResponseOptions, ResponseOptions} from './base_response_options';\nimport {Http, Jsonp} from './http';\nimport {XSRFStrategy} from './interfaces';\n/**\n * @return {?}\n */\nexport function _createDefaultCookieXSRFStrategy() {\n  return new CookieXSRFStrategy();\n}\n/**\n * @param {?} xhrBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function httpFactory(xhrBackend: XHRBackend, requestOptions: RequestOptions): Http {\n  return new Http(xhrBackend, requestOptions);\n}\n/**\n * @param {?} jsonpBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function jsonpFactory(jsonpBackend: JSONPBackend, requestOptions: RequestOptions): Jsonp {\n  return new Jsonp(jsonpBackend, requestOptions);\n}\n/**\n * The module that includes http's providers\n * \n * \\@experimental\n */\nexport class HttpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Http, useFactory: httpFactory, deps: [XHRBackend, RequestOptions]},\n    BrowserXhr,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    XHRBackend,\n    {provide: XSRFStrategy, useFactory: _createDefaultCookieXSRFStrategy},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HttpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nHttpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttpModule.ctorParameters;\n}\n\n/**\n * The module that includes jsonp's providers\n * \n * \\@experimental\n */\nexport class JsonpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Jsonp, useFactory: jsonpFactory, deps: [JSONPBackend, RequestOptions]},\n    BrowserJsonp,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    {provide: JSONPBackend, useClass: JSONPBackend_},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction JsonpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonpModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestOptionsArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates a request options object to be optionally provided when instantiating a\n * {\\@link Request}.\n * \n * This class is based on the `RequestInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#requestinit).\n * \n * All values are null by default. Typical defaults can be found in the {\\@link BaseRequestOptions}\n * class, which sub-classes `RequestOptions`.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * });\n * const req = new Request(options);\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class RequestOptions {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n */\nmethod: RequestMethod|string|null;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n */\nheaders: Headers|null;\n/**\n * Body to be used when creating a {\\@link Request}.\n */\nbody: any;\n/**\n * Url with which to perform a {\\@link Request}.\n */\nurl: string|null;\n/**\n * Search parameters to be included in a {\\@link Request}.\n */\nparams: URLSearchParams;\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @return {?}\n */\nget search(): URLSearchParams { return this.params; }\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @param {?} params\n * @return {?}\n */\nset search(params: URLSearchParams) { this.params = params; }\n/**\n * Enable use credentials for a {\\@link Request}.\n */\nwithCredentials: boolean|null;\n  /*\n   * Select a buffer to store the response, such as ArrayBuffer, Blob, Json (or Document)\n   */\n  responseType: ResponseContentType|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: RequestOptionsArgs = {}) {\n    const {method, headers, body, url, search, params, withCredentials, responseType} = opts;\n    this.method = method != null ? normalizeMethodName(method) : null;\n    this.headers = headers != null ? headers : null;\n    this.body = body != null ? body : null;\n    this.url = url != null ? url : null;\n    this.params = this._mergeSearchParams(params || search);\n    this.withCredentials = withCredentials != null ? withCredentials : null;\n    this.responseType = responseType != null ? responseType : null;\n  }\n/**\n * Creates a copy of the `RequestOptions` instance, using the optional input as values to override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * Note that `headers` and `search` will override existing values completely if present in\n * the `options` object. If these values should be merged, it should be done prior to calling\n * `merge` on the `RequestOptions` instance.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post\n * });\n * const req = new Request(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: RequestOptionsArgs): RequestOptions {\n    return new RequestOptions({\n      method: options && options.method != null ? options.method : this.method,\n      headers: options && options.headers != null ? options.headers : new Headers(this.headers),\n      body: options && options.body != null ? options.body : this.body,\n      url: options && options.url != null ? options.url : this.url,\n      params: options && this._mergeSearchParams(options.params || options.search),\n      withCredentials: options && options.withCredentials != null ? options.withCredentials :\n                                                                    this.withCredentials,\n      responseType: options && options.responseType != null ? options.responseType :\n                                                              this.responseType\n    });\n  }\n/**\n * @param {?=} params\n * @return {?}\n */\nprivate _mergeSearchParams(params?: string|URLSearchParams|{[key: string]: any | any[]}|\n                             null): URLSearchParams {\n    if (!params) return this.params;\n\n    if (params instanceof URLSearchParams) {\n      return params.clone();\n    }\n\n    if (typeof params === 'string') {\n      return new URLSearchParams(params);\n    }\n\n    return this._parseParams(params);\n  }\n/**\n * @param {?=} objParams\n * @return {?}\n */\nprivate _parseParams(objParams: {[key: string]: any | any[]} = {}): URLSearchParams {\n    const /** @type {?} */ params = new URLSearchParams();\n    Object.keys(objParams).forEach((key: string) => {\n      const /** @type {?} */ value: any|any[] = objParams[key];\n      if (Array.isArray(value)) {\n        value.forEach((item: any) => this._appendParam(key, item, params));\n      } else {\n        this._appendParam(key, value, params);\n      }\n    });\n    return params;\n  }\n/**\n * @param {?} key\n * @param {?} value\n * @param {?} params\n * @return {?}\n */\nprivate _appendParam(key: string, value: any, params: URLSearchParams): void {\n    if (typeof value !== 'string') {\n      value = JSON.stringify(value);\n    }\n    params.append(key, value);\n  }\n}\n\nfunction RequestOptions_tsickle_Closure_declarations() {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n * @type {?}\n */\nRequestOptions.prototype.method;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.headers;\n/**\n * Body to be used when creating a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.body;\n/**\n * Url with which to perform a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.url;\n/**\n * Search parameters to be included in a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.params;\n/**\n * Enable use credentials for a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.withCredentials;\n/** @type {?} */\nRequestOptions.prototype.responseType;\n}\n\n/**\n * Subclass of {\\@link RequestOptions}, with default values.\n * \n * Default values:\n *  * method: {\\@link RequestMethod RequestMethod.Get}\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link RequestOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create and send {\\@link Request Requests}.\n * \n * ```typescript\n * import {BaseRequestOptions, RequestOptions} from '\\@angular/http';\n * \n * class MyOptions extends BaseRequestOptions {\n *   search: string = 'coreTeam=true';\n * }\n * \n * {provide: RequestOptions, useClass: MyOptions};\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Request}\n * object.\n * \n * ```\n * import {BaseRequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new BaseRequestOptions();\n * const req = new Request(options.merge({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class BaseRequestOptions extends RequestOptions {\nconstructor() { super({method: RequestMethod.Get, headers: new Headers()}); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseRequestOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseRequestOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseRequestOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {ResponseOptions} from '../base_response_options';\nimport {ContentType, ReadyState, RequestMethod, ResponseContentType, ResponseType} from '../enums';\nimport {Headers} from '../headers';\nimport {getResponseURL, isSuccess} from '../http_utils';\nimport {Connection, ConnectionBackend, XSRFStrategy} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\nimport {BrowserXhr} from './browser_xhr';\n\nconst /** @type {?} */ XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Creates connections using `XMLHttpRequest`. Given a fully-qualified\n * request, an `XHRConnection` will immediately create an `XMLHttpRequest` object and send the\n * request.\n * \n * This class would typically not be created or interacted with directly inside applications, though\n * the {\\@link MockConnection} may be interacted with in tests.\n * \n * \\@experimental\n */\nexport class XHRConnection implements Connection {\n  request: Request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n */\nresponse: Observable<Response>;\n  readyState: ReadyState;\n/**\n * @param {?} req\n * @param {?} browserXHR\n * @param {?=} baseResponseOptions\n */\nconstructor(req: Request, browserXHR: BrowserXhr, baseResponseOptions?: ResponseOptions) {\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n      const _xhr: XMLHttpRequest = browserXHR.build();\n      _xhr.open(RequestMethod[req.method].toUpperCase(), req.url);\n      if (req.withCredentials != null) {\n        _xhr.withCredentials = req.withCredentials;\n      }\n      // load event handler\n      const onLoad = () => {\n        // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n        let status: number = _xhr.status === 1223 ? 204 : _xhr.status;\n\n        let body: any = null;\n\n        // HTTP 204 means no content\n        if (status !== 204) {\n          // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n          // response/responseType properties were introduced in ResourceLoader Level2 spec\n          // (supported by IE10)\n          body = (typeof _xhr.response === 'undefined') ? _xhr.responseText : _xhr.response;\n\n          // Implicitly strip a potential XSSI prefix.\n          if (typeof body === 'string') {\n            body = body.replace(XSSI_PREFIX, '');\n          }\n        }\n\n        // fix status code when it is 0 (0 status is undocumented).\n        // Occurs when accessing file resources or on Android 4.1 stock browser\n        // while retrieving files from application cache.\n        if (status === 0) {\n          status = body ? 200 : 0;\n        }\n\n        const headers: Headers = Headers.fromResponseHeaderString(_xhr.getAllResponseHeaders());\n        // IE 9 does not provide the way to get URL of response\n        const url = getResponseURL(_xhr) || req.url;\n        const statusText: string = _xhr.statusText || 'OK';\n\n        let responseOptions = new ResponseOptions({body, status, headers, statusText, url});\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        const response = new Response(responseOptions);\n        response.ok = isSuccess(status);\n        if (response.ok) {\n          responseObserver.next(response);\n          // TODO(gdi2290): defer complete if array buffer until done\n          responseObserver.complete();\n          return;\n        }\n        responseObserver.error(response);\n      };\n      // error event handler\n      const onError = (err: ErrorEvent) => {\n        let responseOptions = new ResponseOptions({\n          body: err,\n          type: ResponseType.Error,\n          status: _xhr.status,\n          statusText: _xhr.statusText,\n        });\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      this.setDetectedContentType(req, _xhr);\n\n      if (req.headers == null) {\n        req.headers = new Headers();\n      }\n      if (!req.headers.has('Accept')) {\n        req.headers.append('Accept', 'application/json, text/plain, */*');\n      }\n      req.headers.forEach((values, name) => _xhr.setRequestHeader(name !, values.join(',')));\n\n      // Select the correct buffer type to store the response\n      if (req.responseType != null && _xhr.responseType != null) {\n        switch (req.responseType) {\n          case ResponseContentType.ArrayBuffer:\n            _xhr.responseType = 'arraybuffer';\n            break;\n          case ResponseContentType.Json:\n            _xhr.responseType = 'json';\n            break;\n          case ResponseContentType.Text:\n            _xhr.responseType = 'text';\n            break;\n          case ResponseContentType.Blob:\n            _xhr.responseType = 'blob';\n            break;\n          default:\n            throw new Error('The selected responseType is not supported');\n        }\n      }\n\n      _xhr.addEventListener('load', onLoad);\n      _xhr.addEventListener('error', onError);\n\n      _xhr.send(this.request.getBody());\n\n      return () => {\n        _xhr.removeEventListener('load', onLoad);\n        _xhr.removeEventListener('error', onError);\n        _xhr.abort();\n      };\n    });\n  }\n/**\n * @param {?} req\n * @param {?} _xhr\n * @return {?}\n */\nsetDetectedContentType(req: any /** TODO Request */, _xhr: any /** XMLHttpRequest */) {\n    // Skip if a custom Content-Type header is provided\n    if (req.headers != null && req.headers.get('Content-Type') != null) {\n      return;\n    }\n\n    // Set the detected content type\n    switch (req.contentType) {\n      case ContentType.NONE:\n        break;\n      case ContentType.JSON:\n        _xhr.setRequestHeader('content-type', 'application/json');\n        break;\n      case ContentType.FORM:\n        _xhr.setRequestHeader('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        break;\n      case ContentType.TEXT:\n        _xhr.setRequestHeader('content-type', 'text/plain');\n        break;\n      case ContentType.BLOB:\n        const /** @type {?} */ blob = req.blob();\n        if (blob.type) {\n          _xhr.setRequestHeader('content-type', blob.type);\n        }\n        break;\n    }\n  }\n}\n\nfunction XHRConnection_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRConnection.prototype.request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n * @type {?}\n */\nXHRConnection.prototype.response;\n/** @type {?} */\nXHRConnection.prototype.readyState;\n}\n\n/**\n * `XSRFConfiguration` sets up Cross Site Request Forgery (XSRF) protection for the application\n * using a cookie. See https://www.owasp.org/index.php/Cross-Site_Request_Forgery_(CSRF)\n * for more information on XSRF.\n * \n * Applications can configure custom cookie and header names by binding an instance of this class\n * with different `cookieName` and `headerName` values. See the main HTTP documentation for more\n * details.\n * \n * \\@experimental\n */\nexport class CookieXSRFStrategy implements XSRFStrategy {\n/**\n * @param {?=} _cookieName\n * @param {?=} _headerName\n */\nconstructor(\nprivate _cookieName: string = 'XSRF-TOKEN',\nprivate _headerName: string = 'X-XSRF-TOKEN') {}\n/**\n * @param {?} req\n * @return {?}\n */\nconfigureRequest(req: Request): void {\n    const /** @type {?} */ xsrfToken = getDOM().getCookie(this._cookieName);\n    if (xsrfToken) {\n      req.headers.set(this._headerName, xsrfToken);\n    }\n  }\n}\n\nfunction CookieXSRFStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nCookieXSRFStrategy.prototype._cookieName;\n/** @type {?} */\nCookieXSRFStrategy.prototype._headerName;\n}\n\n/**\n * Creates {\\@link XHRConnection} instances.\n * \n * This class would typically not be used by end users, but could be\n * overridden if a different backend implementation should be used,\n * such as in a node backend.\n * \n * ### Example\n * \n * ```\n * import {Http, MyNodeBackend, HTTP_PROVIDERS, BaseRequestOptions} from '\\@angular/http';\n * \\@Component({ \n *   viewProviders: [\n *     HTTP_PROVIDERS,\n *     {provide: Http, useFactory: (backend, options) => {\n *       return new Http(backend, options);\n *     }, deps: [MyNodeBackend, BaseRequestOptions]}]\n * })\n * class MyComponent {\n *   constructor(http:Http) {\n *     http.request('people.json').subscribe(res => this.people = res.json());\n *   }\n * }\n * ```\n * \\@experimental\n */\nexport class XHRBackend implements ConnectionBackend {\n/**\n * @param {?} _browserXHR\n * @param {?} _baseResponseOptions\n * @param {?} _xsrfStrategy\n */\nconstructor(\nprivate _browserXHR: BrowserXhr,\nprivate _baseResponseOptions: ResponseOptions,\nprivate _xsrfStrategy: XSRFStrategy) {}\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): XHRConnection {\n    this._xsrfStrategy.configureRequest(request);\n    return new XHRConnection(request, this._browserXHR, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserXhr, },\n{type: ResponseOptions, },\n{type: XSRFStrategy, },\n];\n}\n\nfunction XHRBackend_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRBackend.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nXHRBackend.ctorParameters;\n/** @type {?} */\nXHRBackend.prototype._browserXHR;\n/** @type {?} */\nXHRBackend.prototype._baseResponseOptions;\n/** @type {?} */\nXHRBackend.prototype._xsrfStrategy;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\n\nimport {ResponseOptions} from '../base_response_options';\nimport {ReadyState, RequestMethod, ResponseType} from '../enums';\nimport {Connection, ConnectionBackend} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\n\nimport {BrowserJsonp} from './browser_jsonp';\n\nconst /** @type {?} */ JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\nconst /** @type {?} */ JSONP_ERR_WRONG_METHOD = 'JSONP requests must use GET request method.';\n/**\n * Abstract base class for an in-flight JSONP request.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPConnection implements Connection {\n/**\n * The {\\@link ReadyState} of this request.\n */\nreadyState: ReadyState;\n/**\n * The outgoing HTTP request.\n */\nrequest: Request;\n/**\n * An observable that completes with the response, when the request is finished.\n */\nresponse: Observable<Response>;\n/**\n * Callback called when the JSONP request completes, to notify the application\n * of the new data.\n * @abstract\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {}\n}\n\nfunction JSONPConnection_tsickle_Closure_declarations() {\n/**\n * The {\\@link ReadyState} of this request.\n * @type {?}\n */\nJSONPConnection.prototype.readyState;\n/**\n * The outgoing HTTP request.\n * @type {?}\n */\nJSONPConnection.prototype.request;\n/**\n * An observable that completes with the response, when the request is finished.\n * @type {?}\n */\nJSONPConnection.prototype.response;\n}\n\nexport class JSONPConnection_ extends JSONPConnection {\nprivate _id: string;\nprivate _script: Element;\nprivate _responseData: any;\nprivate _finished: boolean = false;\n/**\n * @param {?} req\n * @param {?} _dom\n * @param {?=} baseResponseOptions\n */\nconstructor(\n      req: Request,\nprivate _dom: BrowserJsonp,\nprivate baseResponseOptions?: ResponseOptions) {\n    super();\n    if (req.method !== RequestMethod.Get) {\n      throw new TypeError(JSONP_ERR_WRONG_METHOD);\n    }\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n\n      this.readyState = ReadyState.Loading;\n      const id = this._id = _dom.nextRequestID();\n\n      _dom.exposeConnection(id, this);\n\n      // Workaround Dart\n      // url = url.replace(/=JSONP_CALLBACK(&|$)/, `generated method`);\n      const callback = _dom.requestCallback(this._id);\n      let url: string = req.url;\n      if (url.indexOf('=JSONP_CALLBACK&') > -1) {\n        url = url.replace('=JSONP_CALLBACK&', `=${callback}&`);\n      } else if (url.lastIndexOf('=JSONP_CALLBACK') === url.length - '=JSONP_CALLBACK'.length) {\n        url = url.substring(0, url.length - '=JSONP_CALLBACK'.length) + `=${callback}`;\n      }\n\n      const script = this._script = _dom.build(url);\n\n      const onLoad = (event: Event) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        if (!this._finished) {\n          let responseOptions =\n              new ResponseOptions({body: JSONP_ERR_NO_CALLBACK, type: ResponseType.Error, url});\n          if (baseResponseOptions) {\n            responseOptions = baseResponseOptions.merge(responseOptions);\n          }\n          responseObserver.error(new Response(responseOptions));\n          return;\n        }\n\n        let responseOptions = new ResponseOptions({body: this._responseData, url});\n        if (this.baseResponseOptions) {\n          responseOptions = this.baseResponseOptions.merge(responseOptions);\n        }\n\n        responseObserver.next(new Response(responseOptions));\n        responseObserver.complete();\n      };\n\n      const onError = (error: Error) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        let responseOptions = new ResponseOptions({body: error.message, type: ResponseType.Error});\n        if (baseResponseOptions) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      script.addEventListener('load', onLoad);\n      script.addEventListener('error', onError);\n\n      _dom.send(script);\n\n      return () => {\n        this.readyState = ReadyState.Cancelled;\n        script.removeEventListener('load', onLoad);\n        script.removeEventListener('error', onError);\n        this._dom.cleanup(script);\n      };\n    });\n  }\n/**\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {\n    // Don't leak connections\n    this._finished = true;\n    this._dom.removeConnection(this._id);\n    if (this.readyState === ReadyState.Cancelled) return;\n    this._responseData = data;\n  }\n}\n\nfunction JSONPConnection__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPConnection_.prototype._id;\n/** @type {?} */\nJSONPConnection_.prototype._script;\n/** @type {?} */\nJSONPConnection_.prototype._responseData;\n/** @type {?} */\nJSONPConnection_.prototype._finished;\n/** @type {?} */\nJSONPConnection_.prototype._dom;\n/** @type {?} */\nJSONPConnection_.prototype.baseResponseOptions;\n}\n\n/**\n * A {\\@link ConnectionBackend} that uses the JSONP strategy of making requests.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPBackend extends ConnectionBackend {}\nexport class JSONPBackend_ extends JSONPBackend {\n/**\n * @param {?} _browserJSONP\n * @param {?} _baseResponseOptions\n */\nconstructor(private _browserJSONP: BrowserJsonp,\nprivate _baseResponseOptions: ResponseOptions) {\n    super();\n  }\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): JSONPConnection {\n    return new JSONPConnection_(request, this._browserJSONP, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserJsonp, },\n{type: ResponseOptions, },\n];\n}\n\nfunction JSONPBackend__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPBackend_.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJSONPBackend_.ctorParameters;\n/** @type {?} */\nJSONPBackend_.prototype._browserJSONP;\n/** @type {?} */\nJSONPBackend_.prototype._baseResponseOptions;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {RequestMethod} from './enums';\n/**\n * @param {?} method\n * @return {?}\n */\nexport function normalizeMethodName(method: string | RequestMethod): RequestMethod {\n  if (typeof method !== 'string') return method;\n\n  switch (method.toUpperCase()) {\n    case 'GET':\n      return RequestMethod.Get;\n    case 'POST':\n      return RequestMethod.Post;\n    case 'PUT':\n      return RequestMethod.Put;\n    case 'DELETE':\n      return RequestMethod.Delete;\n    case 'OPTIONS':\n      return RequestMethod.Options;\n    case 'HEAD':\n      return RequestMethod.Head;\n    case 'PATCH':\n      return RequestMethod.Patch;\n  }\n  throw new Error(`Invalid request method. The method \"${method}\" is not supported.`);\n}\n\nexport const /** @type {?} */ isSuccess = (status: number): boolean => (status >= 200 && status < 300);\n/**\n * @param {?} xhr\n * @return {?}\n */\nexport function getResponseURL(xhr: any): string|null {\n  if ('responseURL' in xhr) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer8(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint8Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint16Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @param {?=} rawParams\n * @return {?}\n */\nfunction paramParser(rawParams: string = ''): Map<string, string[]> {\n  const /** @type {?} */ map = new Map<string, string[]>();\n  if (rawParams.length > 0) {\n    const /** @type {?} */ params: string[] = rawParams.split('&');\n    params.forEach((param: string) => {\n      const /** @type {?} */ eqIdx = param.indexOf('=');\n      const [key, val]: string[] =\n          eqIdx == -1 ? [param, ''] : [param.slice(0, eqIdx), param.slice(eqIdx + 1)];\n      const /** @type {?} */ list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * \\@experimental \n * \n */\nexport class QueryEncoder {\n/**\n * @param {?} k\n * @return {?}\n */\nencodeKey(k: string): string { return standardEncoding(k); }\n/**\n * @param {?} v\n * @return {?}\n */\nencodeValue(v: string): string { return standardEncoding(v); }\n}\n/**\n * @param {?} v\n * @return {?}\n */\nfunction standardEncoding(v: string): string {\n  return encodeURIComponent(v)\n      .replace(/%40/gi, '@')\n      .replace(/%3A/gi, ':')\n      .replace(/%24/gi, '$')\n      .replace(/%2C/gi, ',')\n      .replace(/%3B/gi, ';')\n      .replace(/%2B/gi, '+')\n      .replace(/%3D/gi, '=')\n      .replace(/%3F/gi, '?')\n      .replace(/%2F/gi, '/');\n}\n/**\n * Map-like representation of url search parameters, based on\n * [URLSearchParams](https://url.spec.whatwg.org/#urlsearchparams) in the url living standard,\n * with several extensions for merging URLSearchParams objects:\n *   - setAll()\n *   - appendAll()\n *   - replaceAll()\n * \n * This class accepts an optional second parameter of ${\\@link QueryEncoder},\n * which is used to serialize parameters before making a request. By default,\n * `QueryEncoder` encodes keys and values of parameters using `encodeURIComponent`,\n * and then un-encodes certain characters that are allowed to be part of the query\n * according to IETF RFC 3986: https://tools.ietf.org/html/rfc3986.\n * \n * These are the characters that are not encoded: `! $ \\' ( ) * + , ; A 9 - . _ ~ ? /`\n * \n * If the set of allowed query characters is not acceptable for a particular backend,\n * `QueryEncoder` can be subclassed and provided as the 2nd argument to URLSearchParams.\n * \n * ```\n * import {URLSearchParams, QueryEncoder} from '\\@angular/http';\n * class MyQueryEncoder extends QueryEncoder {\n *   encodeKey(k: string): string {\n *     return myEncodingFunction(k);\n *   }\n * \n *   encodeValue(v: string): string {\n *     return myEncodingFunction(v);\n *   }\n * }\n * \n * let params = new URLSearchParams('', new MyQueryEncoder());\n * ```\n * \\@experimental\n */\nexport class URLSearchParams {\n  paramsMap: Map<string, string[]>;\n/**\n * @param {?=} rawParams\n * @param {?=} queryEncoder\n */\nconstructor(\npublic rawParams: string = '',\nprivate queryEncoder: QueryEncoder = new QueryEncoder()) {\n    this.paramsMap = paramParser(rawParams);\n  }\n/**\n * @return {?}\n */\nclone(): URLSearchParams {\n    const /** @type {?} */ clone = new URLSearchParams('', this.queryEncoder);\n    clone.appendAll(this);\n    return clone;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\nhas(param: string): boolean { return this.paramsMap.has(param); }\n/**\n * @param {?} param\n * @return {?}\n */\nget(param: string): string|null {\n    const /** @type {?} */ storedParam = this.paramsMap.get(param);\n\n    return Array.isArray(storedParam) ? storedParam[0] : null;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ngetAll(param: string): string[] { return this.paramsMap.get(param) || []; }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nset(param: string, val: string) {\n    if (val === void 0 || val === null) {\n      this.delete(param);\n      return;\n    }\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.length = 0;\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nsetAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      list.push(value[0]);\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nappend(param: string, val: string): void {\n    if (val === void 0 || val === null) return;\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nappendAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nreplaceAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    const /** @type {?} */ paramsList: string[] = [];\n    this.paramsMap.forEach((values, k) => {\n      values.forEach(\n          v => paramsList.push(\n              this.queryEncoder.encodeKey(k) + '=' + this.queryEncoder.encodeValue(v)));\n    });\n    return paramsList.join('&');\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ndelete (param: string): void { this.paramsMap.delete(param); }\n}\n\nfunction URLSearchParams_tsickle_Closure_declarations() {\n/** @type {?} */\nURLSearchParams.prototype.paramsMap;\n/** @type {?} */\nURLSearchParams.prototype.rawParams;\n/** @type {?} */\nURLSearchParams.prototype.queryEncoder;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {stringToArrayBuffer} from './http_utils';\nimport {URLSearchParams} from './url_search_params';\n/**\n * HTTP request body used by both {\\@link Request} and {\\@link Response}\n * https://fetch.spec.whatwg.org/#body\n * @abstract\n */\nexport abstract class Body {\n/**\n * \\@internal\n */\nprotected _body: any;\n/**\n * Attempts to return body as parsed `JSON` object, or raises an exception.\n * @return {?}\n */\njson(): any {\n    if (typeof this._body === 'string') {\n      return JSON.parse( /** @type {?} */((<string>this._body)));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return JSON.parse(this.text());\n    }\n\n    return this._body;\n  }\n/**\n * Returns the body as a string, presuming `toString()` can be called on the response body.\n * \n * When decoding an `ArrayBuffer`, the optional `encodingHint` parameter determines how the\n * bytes in the buffer will be interpreted. Valid values are:\n * \n * - `legacy` - incorrectly interpret the bytes as UTF-16 (technically, UCS-2). Only characters\n *   in the Basic Multilingual Plane are supported, surrogate pairs are not handled correctly.\n *   In addition, the endianness of the 16-bit octet pairs in the `ArrayBuffer` is not taken\n *   into consideration. This is the default behavior to avoid breaking apps, but should be\n *   considered deprecated.\n * \n * - `iso-8859` - interpret the bytes as ISO-8859 (which can be used for ASCII encoded text).\n * @param {?=} encodingHint\n * @return {?}\n */\ntext(encodingHint: 'legacy'|'iso-8859' = 'legacy'): string {\n    if (this._body instanceof URLSearchParams) {\n      return this._body.toString();\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      switch (encodingHint) {\n        case 'legacy':\n          return String.fromCharCode.apply(null, new Uint16Array( /** @type {?} */((this._body as ArrayBuffer))));\n        case 'iso-8859':\n          return String.fromCharCode.apply(null, new Uint8Array( /** @type {?} */((this._body as ArrayBuffer))));\n        default:\n          throw new Error(`Invalid value for encodingHint: ${encodingHint}`);\n      }\n    }\n\n    if (this._body == null) {\n      return '';\n    }\n\n    if (typeof this._body === 'object') {\n      return JSON.stringify(this._body, null, 2);\n    }\n\n    return this._body.toString();\n  }\n/**\n * Return the body as an ArrayBuffer\n * @return {?}\n */\narrayBuffer(): ArrayBuffer {\n    if (this._body instanceof ArrayBuffer) {\n      return /** @type {?} */(( <ArrayBuffer>this._body));\n    }\n\n    return stringToArrayBuffer(this.text());\n  }\n/**\n * Returns the request's body as a Blob, assuming that body exists.\n * @return {?}\n */\nblob(): Blob {\n    if (this._body instanceof Blob) {\n      return /** @type {?} */(( <Blob>this._body));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return new Blob([this._body]);\n    }\n\n    throw new Error('The request body isn\\'t either a blob or an array buffer');\n  }\n}\n\nfunction Body_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nBody.prototype._body;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\n\nimport {ResponseOptions} from './base_response_options';\nimport {Body} from './body';\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\n/**\n * Creates `Response` instances from provided values.\n * \n * Though this object isn't\n * usually instantiated by end-users, it is the primary object interacted with when it comes time to\n * add data to a view.\n * \n * ### Example\n * \n * ```\n * http.request('my-friends.txt').subscribe(response => this.friends = response.text());\n * ```\n * \n * The Response's interface is inspired by the Response constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#response-class), but is considered a static value whose body\n * can be accessed many times. There are other differences in the implementation, but this is the\n * most significant.\n * \n * \\@experimental\n */\nexport class Response extends Body {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n */\ntype: ResponseType;\n/**\n * True if the response's status is within 200-299\n */\nok: boolean;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n */\nurl: string;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n */\nstatus: number;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n */\nstatusText: string|null;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n */\nbytesLoaded: number;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n */\ntotalBytes: number;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n */\nheaders: Headers|null;\n/**\n * @param {?} responseOptions\n */\nconstructor(responseOptions: ResponseOptions) {\n    super();\n    this._body = responseOptions.body;\n    this.status = responseOptions.status !;\n    this.ok = (this.status >= 200 && this.status <= 299);\n    this.statusText = responseOptions.statusText;\n    this.headers = responseOptions.headers;\n    this.type = responseOptions.type !;\n    this.url = responseOptions.url !;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    return `Response with status: ${this.status} ${this.statusText} for URL: ${this.url}`;\n  }\n}\n\nfunction Response_tsickle_Closure_declarations() {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n * @type {?}\n */\nResponse.prototype.type;\n/**\n * True if the response's status is within 200-299\n * @type {?}\n */\nResponse.prototype.ok;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n * @type {?}\n */\nResponse.prototype.url;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n * @type {?}\n */\nResponse.prototype.status;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n * @type {?}\n */\nResponse.prototype.statusText;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n * @type {?}\n */\nResponse.prototype.bytesLoaded;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n * @type {?}\n */\nResponse.prototype.totalBytes;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n * @type {?}\n */\nResponse.prototype.headers;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nlet /** @type {?} */ _nextRequestId = 0;\nexport const /** @type {?} */ JSONP_HOME = '__ng_jsonp__';\nlet /** @type {?} */ _jsonpConnections: {[key: string]: any}|null = null;\n/**\n * @return {?}\n */\nfunction _getJsonpConnections(): {[key: string]: any} {\n  const /** @type {?} */ w: {[key: string]: any} = typeof window == 'object' ? window : {};\n  if (_jsonpConnections === null) {\n    _jsonpConnections = w[JSONP_HOME] = {};\n  }\n  return _jsonpConnections;\n}\nexport class BrowserJsonp {\n/**\n * @param {?} url\n * @return {?}\n */\nbuild(url: string): any {\n    const /** @type {?} */ node = document.createElement('script');\n    node.src = url;\n    return node;\n  }\n/**\n * @return {?}\n */\nnextRequestID(): string { return `__req${_nextRequestId++}`; }\n/**\n * @param {?} id\n * @return {?}\n */\nrequestCallback(id: string): string { return `${JSONP_HOME}.${id}.finished`; }\n/**\n * @param {?} id\n * @param {?} connection\n * @return {?}\n */\nexposeConnection(id: string, connection: any) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = connection;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nremoveConnection(id: string) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = null;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nsend(node: any) { document.body.appendChild( /** @type {?} */((<Node>(node)))); }\n/**\n * @param {?} node\n * @return {?}\n */\ncleanup(node: any) {\n    if (node.parentNode) {\n      node.parentNode.removeChild( /** @type {?} */((<Node>(node))));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserJsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "\nexport type RequestMethod = number;\nexport let RequestMethod: any = {};\nRequestMethod.Get = 0;\nRequestMethod.Post = 1;\nRequestMethod.Put = 2;\nRequestMethod.Delete = 3;\nRequestMethod.Options = 4;\nRequestMethod.Head = 5;\nRequestMethod.Patch = 6;\nRequestMethod[RequestMethod.Get] = \"Get\";\nRequestMethod[RequestMethod.Post] = \"Post\";\nRequestMethod[RequestMethod.Put] = \"Put\";\nRequestMethod[RequestMethod.Delete] = \"Delete\";\nRequestMethod[RequestMethod.Options] = \"Options\";\nRequestMethod[RequestMethod.Head] = \"Head\";\nRequestMethod[RequestMethod.Patch] = \"Patch\";\n\nexport type ReadyState = number;\nexport let ReadyState: any = {};\nReadyState.Unsent = 0;\nReadyState.Open = 1;\nReadyState.HeadersReceived = 2;\nReadyState.Loading = 3;\nReadyState.Done = 4;\nReadyState.Cancelled = 5;\nReadyState[ReadyState.Unsent] = \"Unsent\";\nReadyState[ReadyState.Open] = \"Open\";\nReadyState[ReadyState.HeadersReceived] = \"HeadersReceived\";\nReadyState[ReadyState.Loading] = \"Loading\";\nReadyState[ReadyState.Done] = \"Done\";\nReadyState[ReadyState.Cancelled] = \"Cancelled\";\n\nexport type ResponseType = number;\nexport let ResponseType: any = {};\nResponseType.Basic = 0;\nResponseType.Cors = 1;\nResponseType.Default = 2;\nResponseType.Error = 3;\nResponseType.Opaque = 4;\nResponseType[ResponseType.Basic] = \"Basic\";\nResponseType[ResponseType.Cors] = \"Cors\";\nResponseType[ResponseType.Default] = \"Default\";\nResponseType[ResponseType.Error] = \"Error\";\nResponseType[ResponseType.Opaque] = \"Opaque\";\n\nexport type ContentType = number;\nexport let ContentType: any = {};\nContentType.NONE = 0;\nContentType.JSON = 1;\nContentType.FORM = 2;\nContentType.FORM_DATA = 3;\nContentType.TEXT = 4;\nContentType.BLOB = 5;\nContentType.ARRAY_BUFFER = 6;\nContentType[ContentType.NONE] = \"NONE\";\nContentType[ContentType.JSON] = \"JSON\";\nContentType[ContentType.FORM] = \"FORM\";\nContentType[ContentType.FORM_DATA] = \"FORM_DATA\";\nContentType[ContentType.TEXT] = \"TEXT\";\nContentType[ContentType.BLOB] = \"BLOB\";\nContentType[ContentType.ARRAY_BUFFER] = \"ARRAY_BUFFER\";\n\nexport type ResponseContentType = number;\nexport let ResponseContentType: any = {};\nResponseContentType.Text = 0;\nResponseContentType.Json = 1;\nResponseContentType.ArrayBuffer = 2;\nResponseContentType.Blob = 3;\nResponseContentType[ResponseContentType.Text] = \"Text\";\nResponseContentType[ResponseContentType.Json] = \"Json\";\nResponseContentType[ResponseContentType.ArrayBuffer] = \"ArrayBuffer\";\nResponseContentType[ResponseContentType.Blob] = \"Blob\";\n\n", "\n/**\n * Polyfill for [Headers](https://developer.mozilla.org/en-US/docs/Web/API/Headers/Headers), as\n * specified in the [Fetch Spec](https://fetch.spec.whatwg.org/#headers-class).\n * \n * The only known difference between this `Headers` implementation and the spec is the\n * lack of an `entries` method.\n * \n * ### Example\n * \n * ```\n * import {Headers} from '\\@angular/http';\n * \n * var firstHeaders = new Headers();\n * firstHeaders.append('Content-Type', 'image/jpeg');\n * console.log(firstHeaders.get('Content-Type')) //'image/jpeg'\n * \n * // Create headers from Plain Old JavaScript Object\n * var secondHeaders = new Headers({\n *   'X-My-Custom-Header': 'Angular'\n * });\n * console.log(secondHeaders.get('X-My-Custom-Header')); //'Angular'\n * \n * var thirdHeaders = new Headers(secondHeaders);\n * console.log(thirdHeaders.get('X-My-Custom-Header')); //'Angular'\n * ```\n * \n * \\@experimental\n */\nexport class Headers {\n/**\n * \\@internal header names are lower case\n */\n_headers: Map<string, string[]> = new Map();\n/**\n * \\@internal map lower case names to actual names\n */\n_normalizedNames: Map<string, string> = new Map();\n/**\n * @param {?=} headers\n */\nconstructor(headers?: Headers|{[name: string]: any}|null) {\n    if (!headers) {\n      return;\n    }\n\n    if (headers instanceof Headers) {\n      headers.forEach((values: string[], name: string) => {\n        values.forEach(value => this.append(name, value));\n      });\n      return;\n    }\n\n    Object.keys(headers).forEach((name: string) => {\n      const values: string[] = Array.isArray(headers[name]) ? headers[name] : [headers[name]];\n      this.delete(name);\n      values.forEach(value => this.append(name, value));\n    });\n  }\n/**\n * Returns a new Headers instance from the given DOMString of Response Headers\n * @param {?} headersString\n * @return {?}\n */\nstatic fromResponseHeaderString(headersString: string): Headers {\n    const /** @type {?} */ headers = new Headers();\n\n    headersString.split('\\n').forEach(line => {\n      const /** @type {?} */ index = line.indexOf(':');\n      if (index > 0) {\n        const /** @type {?} */ name = line.slice(0, index);\n        const /** @type {?} */ value = line.slice(index + 1).trim();\n        headers.set(name, value);\n      }\n    });\n\n    return headers;\n  }\n/**\n * Appends a header to existing list of header values for a given header name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nappend(name: string, value: string): void {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      this.set(name, value);\n    } else {\n      values.push(value);\n    }\n  }\n/**\n * Deletes all header values for the given name.\n * @param {?} name\n * @return {?}\n */\ndelete (name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n    this._normalizedNames.delete(lcName);\n    this._headers.delete(lcName);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nforEach(fn: (values: string[], name: string|undefined, headers: Map<string, string[]>) => void):\n      void {\n    this._headers.forEach(\n        (values, lcName) => fn(values, this._normalizedNames.get(lcName), this._headers));\n  }\n/**\n * Returns first header that matches given name.\n * @param {?} name\n * @return {?}\n */\nget(name: string): string|null {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      return null;\n    }\n\n    return values.length > 0 ? values[0] : null;\n  }\n/**\n * Checks for existence of header by given name.\n * @param {?} name\n * @return {?}\n */\nhas(name: string): boolean { return this._headers.has(name.toLowerCase()); }\n/**\n * Returns the names of the headers\n * @return {?}\n */\nkeys(): string[] { return Array.from(this._normalizedNames.values()); }\n/**\n * Sets or overrides header value for given name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nset(name: string, value: string|string[]): void {\n    if (Array.isArray(value)) {\n      if (value.length) {\n        this._headers.set(name.toLowerCase(), [value.join(',')]);\n      }\n    } else {\n      this._headers.set(name.toLowerCase(), [value]);\n    }\n    this.mayBeSetNormalizedName(name);\n  }\n/**\n * Returns values of all headers.\n * @return {?}\n */\nvalues(): string[][] { return Array.from(this._headers.values()); }\n/**\n * @return {?}\n */\ntoJSON(): {[name: string]: any} {\n    const /** @type {?} */ serialized: {[name: string]: string[]} = {};\n\n    this._headers.forEach((values: string[], name: string) => {\n      const /** @type {?} */ split: string[] = [];\n      values.forEach(v => split.push(...v.split(',')));\n      serialized[ /** @type {?} */((this._normalizedNames.get(name)))] = split;\n    });\n\n    return serialized;\n  }\n/**\n * Returns list of header values for a given name.\n * @param {?} name\n * @return {?}\n */\ngetAll(name: string): string[]|null {\n    return this.has(name) ? this._headers.get(name.toLowerCase()) || null : null;\n  }\n/**\n * This method is not implemented.\n * @return {?}\n */\nentries() { throw new Error('\"entries\" method is not implemented on Headers class'); }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate mayBeSetNormalizedName(name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n\n    if (!this._normalizedNames.has(lcName)) {\n      this._normalizedNames.set(lcName, name);\n    }\n  }\n}\n\nfunction Headers_tsickle_Closure_declarations() {\n/**\n * \\@internal header names are lower case\n * @type {?}\n */\nHeaders.prototype._headers;\n/**\n * \\@internal map lower case names to actual names\n * @type {?}\n */\nHeaders.prototype._normalizedNames;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\nimport {ResponseOptionsArgs} from './interfaces';\n/**\n * Creates a response options object to be optionally provided when instantiating a\n * {\\@link Response}.\n * \n * This class is based on the `ResponseInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#responseinit).\n * \n * All values are null by default. Typical defaults can be found in the\n * {\\@link BaseResponseOptions} class, which sub-classes `ResponseOptions`.\n * \n * This class may be used in tests to build {\\@link Response Responses} for\n * mock responses (see {\\@link MockBackend}).\n * \n * ### Example ([live demo](http://plnkr.co/edit/P9Jkk8e8cz6NVzbcxEsD?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: '{\"name\":\"<PERSON>\"}'\n * });\n * var res = new Response(options);\n * \n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * ```\n * \n * \\@experimental\n */\nexport class ResponseOptions {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n */\nbody: string|Object|ArrayBuffer|Blob|null;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n */\nstatus: number|null;\n/**\n * Response {\\@link Headers headers}\n */\nheaders: Headers|null;\n/**\n * \\@internal\n */\nstatusText: string|null;\n/**\n * \\@internal\n */\ntype: ResponseType|null;\n  url: string|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: ResponseOptionsArgs = {}) {\n    const {body, status, headers, statusText, type, url} = opts;\n    this.body = body != null ? body : null;\n    this.status = status != null ? status : null;\n    this.headers = headers != null ? headers : null;\n    this.statusText = statusText != null ? statusText : null;\n    this.type = type != null ? type : null;\n    this.url = url != null ? url : null;\n  }\n/**\n * Creates a copy of the `ResponseOptions` instance, using the optional input as values to\n * override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * This may be useful when sharing a base `ResponseOptions` object inside tests,\n * where certain properties may change from test to test.\n * \n * ### Example ([live demo](http://plnkr.co/edit/1lXquqFfgduTFBWjNoRE?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: {name: 'Jeff'}\n * });\n * var res = new Response(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('options.url:', options.url); // null\n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * console.log('res.url:', res.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: ResponseOptionsArgs): ResponseOptions {\n    return new ResponseOptions({\n      body: options && options.body != null ? options.body : this.body,\n      status: options && options.status != null ? options.status : this.status,\n      headers: options && options.headers != null ? options.headers : this.headers,\n      statusText: options && options.statusText != null ? options.statusText : this.statusText,\n      type: options && options.type != null ? options.type : this.type,\n      url: options && options.url != null ? options.url : this.url,\n    });\n  }\n}\n\nfunction ResponseOptions_tsickle_Closure_declarations() {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n * @type {?}\n */\nResponseOptions.prototype.body;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n * @type {?}\n */\nResponseOptions.prototype.status;\n/**\n * Response {\\@link Headers headers}\n * @type {?}\n */\nResponseOptions.prototype.headers;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.statusText;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.type;\n/** @type {?} */\nResponseOptions.prototype.url;\n}\n\n/**\n * Subclass of {\\@link ResponseOptions}, with default values.\n * \n * Default values:\n *  * status: 200\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link ResponseOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create {\\@link Response Responses}.\n * \n * ### Example ([live demo](http://plnkr.co/edit/qv8DLT?p=preview))\n * \n * ```typescript\n * import {provide} from '\\@angular/core';\n * import {bootstrap} from '\\@angular/platform-browser/browser';\n * import {HTTP_PROVIDERS, Headers, Http, BaseResponseOptions, ResponseOptions} from\n * '\\@angular/http';\n * import {App} from './myapp';\n * \n * class MyOptions extends BaseResponseOptions {\n *   headers:Headers = new Headers({network: 'github'});\n * }\n * \n * bootstrap(App, [HTTP_PROVIDERS, {provide: ResponseOptions, useClass: MyOptions}]);\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Response}\n * object.\n * \n * ### Example ([live demo](http://plnkr.co/edit/VngosOWiaExEtbstDoix?p=preview))\n * \n * ```\n * import {BaseResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new BaseResponseOptions();\n * var res = new Response(options.merge({\n *   body: 'Angular',\n *   headers: new Headers({framework: 'angular'})\n * }));\n * console.log('res.headers.get(\"framework\"):', res.headers.get('framework')); // angular\n * console.log('res.text():', res.text()); // Angular;\n * ```\n * \n * \\@experimental\n */\nexport class BaseResponseOptions extends ResponseOptions {\nconstructor() {\n    super({status: 200, statusText: 'Ok', type: ResponseType.Default, headers: new Headers()});\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseResponseOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseResponseOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseResponseOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["Request", "_super", "requestOptions", "_this", "call", "this", "url", "paramsArg", "params", "search", "URLSearchParams", "toString", "prefix", "length", "headers", "Headers", "__extends", "prototype", "detectContentType", "get", "ContentType", "JSON", "FORM", "detectContentTypeFromBody", "_body", "NONE", "FormData", "FORM_DATA", "Blob$1", "BLOB", "getBody", "contentType", "text", "TEXT", "arrayBuffer", "Body", "Http", "request", "options", "responseObservable", "mergeOptions", "_defaultOptions", "RequestMethod", "Options", "type", "RequestOptions", "Jsonp", "Get", "Error", "httpRequest", "_backend", "_angular_core", "Injectable", "HttpModule", "decorators", "NgModule", "args", "providers", "provide", "useClass", "BaseRequestOptions", "ResponseOptions", "BaseResponseOptions", "JsonpModule", "BrowserJsonp", "opts", "merge", "method", "withCredentials", "_mergeSearchParams", "_parseParams", "objParams", "Object", "keys", "for<PERSON>ach", "key", "value", "stringify", "XHRConnection", "req", "browserXHR", "baseResponseOptions", "response", "rxjs_Observable", "Observable", "responseObserver", "_xhr", "open", "toUpperCase", "body", "responseText", "status", "fromResponseHeaderString", "getAllResponseHeaders", "statusText", "responseOptions", "ok", "isSuccess", "next", "error", "onError", "err", "ResponseType", "setDetectedContentType", "has", "append", "values", "name", "setRequestHeader", "join", "responseType", "ResponseContentType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Json", "Text", "Blob", "CookieXSRFStrategy", "_cookieName", "_headerName", "xsrfToken", "_browserXHR", "_baseResponseOptions", "JSONPConnection_", "_dom", "TypeError", "JSONP_ERR_WRONG_METHOD", "readyState", "ReadyState", "Loading", "id", "_id", "nextRequestID", "exposeConnection", "callback", "requestCallback", "indexOf", "replace", "lastIndexOf", "substring", "script", "_script", "build", "onLoad", "event", "Cancelled", "Done", "cleanup", "_finished", "responseOptions_1", "JSONP_ERR_NO_CALLBACK", "Response", "_responseData", "complete", "message", "addEventListener", "removeEventListener", "JSONPBackend", "tslib_1.__extends", "JSONPBackend_", "_browserJSONP", "rawParams", "query<PERSON><PERSON><PERSON>", "val", "i", "replaceAll", "searchParams", "list", "paramsList", "encodingHint", "String", "fromCharCode", "apply", "Uint16Array", "Uint8Array", "JSONP_HOME", "_jsonpConnections", "node", "parentNode", "extendStatics", "setPrototypeOf", "__proto__", "Array", "d", "b", "p", "hasOwnProperty", "BrowserXhr", "ctorParameters", "Post", "Put", "Delete", "Head", "Patch", "Unsent", "Open", "HeadersReceived", "Basic", "Cors", "<PERSON><PERSON><PERSON>", "Opaque", "ARRAY_BUFFER", "_headers", "Map", "_normalizedNames", "headersString", "set", "from", "entries", "defaultOpts", "providedOpts", "newOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "split", "push", "standardEncoding", "v", "encodeURIComponent", "w", "urlEncodeParams", "isArray", "element", "normalizeMethodName", "exports", "module", "factory", "require"], "mappings": ";;;;;0BWAA,gBAAA+N,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,iBAAAA,QAAA,mBAAAA,QAAA;;;;;;;ALwBA,QAAAJ,qBAAA3J,QACA,GAAA,gBAAAA,QACA,MAAAA,OACA,QAAAA,OAAAqB,eACA,IAAA,MACA,MAAA9C,eAAAK,GACA,KAAA,OACA,MAAAL,eAAAkJ,IACA,KAAA,MACA,MAJYlJ,eAIZmJ,GACA,KAAA,SAEA,MAAAnJ,eAAAoJ,sFAKA,KAAA;;;;;;;;;ACrBA,QAAAqB,aAAAnD,WACA,SAAAA,YAAAA,UAAA,GACA,IAAAoD,KAAA,GAAAV,IACA,IAAA1C,UAAAnJ,OAAA,EAAA,CACA,GAAAL,QAAAwJ,UAAAqD,MAAA,8KAKA/C,MAAAgD,KAAApD,oCA0BA,QAAOqD,kBAbiBC,GAcxB,MAAAC,oBAAAD,8QG3BA,QAAAzC,oBAAAA,kBAAA2C,EAAA5C,kCVqMA,QAAA6C,iBAAAnN,QACA,GAAA6J,cAAA,GAAA3J,uBACA8D,QAAAC,KAAAjE,QAAAkE,QAAA,SAAAC,KAEA,GAAAC,OAAApE,OAAAmE,IACAC,QAAAyG,MAAAuC,QAAAhJ,OACAA,MAAAF,QAAA,SAAAmJ,SAAA,MAAAxD,cAAA5D,OAAA9B,IAAAkJ,QAAAlN;;;;;;;wFC5LA,QAAA6B,cAAAwK,YAAAC,aAAA9I,OAAA7D,KACA,GAAA4M,YAAAF,WACA,OAAAC,cAEAC,WAAAhJ,MAAA,GAAArB,iBATSsB,OAAT8I,aAAA9I,QAAAA,OAYA7D,IAAA2M,aAAA3M,KAAAA;;;;;;;sQU9BA,GAAI4K,eAAJ1G,OAAA2G,iBACAC,uBAAAC,QAA2C,SAA3CC,EAAAC,GAAAD,EAAAF,UAAAG,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,gKClBA5I,KAAAO,cAAAC,aAMAsI,WAAAC,eAAA,WAAA,SACA,IAAAjJ,iBACAA,eAAcK,IAAd,EACAL,cAAckJ,KAAd,EACAlJ,cAAcmJ,IAAd,EACAnJ,cAAcoJ,OAAd,EAGApJ,cAAqBC,QAArB,EACAD,cAAAqJ,KAAA,EACArJ,cAAAsJ,MAAA,EACAtJ,cAAAA,cAAAK,KAAA,MACAL,cAAAA,cAAAkJ,MAAA,OACAlJ,cAAAA,cAAAmJ,KAAA,MACAnJ,cAAAA,cAAAoJ,QAAA,SACApJ,cAAAA,cAAAC,SAAA,UACAD,cAAAA,cAAAqJ,MAAA,OACArJ,cAAAA,cAAAsJ,OAAA,OACA,IAAAjE,cACAA,YAAWkE,OAAX,EACAlE,WAAWmE,KAAX,EAGAnE,WAAAoE,gBAAA,EACApE,WAAAC,QAAqB,EACrBD,WAAAkB,KAAA,EACAlB,WAAAiB,UAAA,EACAjB,WAAAA,WAAsBkE,QAAtB,SACAlE,WAAAA,WAAsBmE,MAAtB,OACAnE,WAAAA,WAAAoE,iBAAA,kBACApE,WAAAA,WAAAC,SAAA,UACAD,WAAAA,WAAAkB,MAAA,OACAlB,WAAAA,WAAAiB,WAAA,WACA,IAAA1C,gBAGAA,cAAA8F,MAAA,EACA9F,aAAA+F,KAAA,EACA/F,aAAAgG,QAAA,EACAhG,aAAAtD,MAAA,EACAsD,aAAAiG,OAAA,EACAjG,aAAAA,aAAA8F,OAAA,QACA9F,aAAAA,aAAA+F,MAAA,OACA/F,aAAAA,aAAAgG,SAAA,UACAhG,aAAAA,aAAAtD,OAAA,QACAsD,aAAAA,aAAAiG,QAAA,QACA,IAAAnL,eACAA,aAAYK,KAAZ,EACAL,YAAYC,KAAZ,EACAD,YAAYE,KAAZ,EACAF,YAAYO,UAAY,EAGxBP,YAAAa,KAAA,EACAb,YAAAS,KAAA,EACAT,YAAAoL,aAAA,EACApL,YAAAA,YAAAK,MAAA,OACAL,YAAAA,YAAwBC,MAAxB,OACAD,YAAAA,YAAAE,MAAA,OACAF,YAAAA,YAAAO,WAAA,YACAP,YAAAA,YAAAa,MAAA,OACAb,YAAAA,YAAAS,MAAA,2DCvEA,IAAAkF,oYAyCA,QAAShG,SAATD,uBAYA,IANAT,KAAAoM,SAAA,GAAAC,KAKArM,KAAAsM,iBAAA,GAAAD,KACA5L,QAGA,MAAAA,mBAAAC,aACAD,SAAA4D,QAAA,SAAAgC,OAAAC,4RAkBA5F,SAAA6E,yBAAA,SAAAgH,eACA,GAAA9L,SAAA,GAAAC,q6BA2EAA,QAAAE,UAAA4L,IAAA,SAAAlG,KAAA/B,mLAeA7D,QAAAE,UAAAyF,OAAA,WAAA,MAAA2E,OAAAyB,KAAAzM,KAAAoM,SAAA/F,sYA4BA3F,QAAAE,UAAA8L,QAAA,WAAA,KAAA,IAAA/J,OAAA,qOC1HAa,gBAAA,mZAyCAA,iBAAA5C,UAAAiD,MAAA,SAAA5B,SAEA,MAAA,IAAAuB,8RA/CAjB,KAAAN,SAAA,MAAAA,QAAAM,KAAAN,QAAAM,KAAAvC,KAAAuC,mJAoGA,MAAA3C,QAAAG,KAAAC,MAAAsF,OAAA,IAAAG,WAAA,KAAAlD,KAAA0D,aAAAgG,QAAAxL,QAAA,GAAAC,YAAAV,WAJAW,WAAA8C,oBAAA7D;;;;;;;8VRvHAgG,UAAA,SAAAN,QAAA,MAAAA,SAAA,KAAAA,OAAA,4PC4DA,QAAAjF,iBAAAsJ,UAAAC,cACA,SAAAD,YAAAA,UAAA,wkCAiEA,GAAA,SAAAE,KAAA,OAAAA,IAAA,uNAaA,IAAA,wCAAAC,EAAA,EAAAA,EAAAvF,MAAA/D,SAAAsJ,yDASAzJ,gBAAAO,UAAAmJ,WAAA,SAAAC,yHAIAC,MAAAzJ,OAAA,CACA,KAAA,GAAAsJ,GAAA,EAAAA,EAAAvF,MAAA/D,SAAAsJ,sHAYAI,0fCrJApI,KAAAlB,UAAAe,KAAA,SAAAwI,cAEA,GADA,SAAAA,eAAAA,aAAA,UACAnK,KAAAmB,gBAAAd,iBACA,MAAAL,MAAAmB,MAAAb,UAGA,IAAAN,KAAAmB,gBAAAwF,aACA,OAAAwD,cACA,IAAA,SAEA,MAAAC,QAAAC,aAAAC,MAAA,KAAA,GAAAC,aAAAvK,KAAA,OACA,KAAA,WACA,MAAAoK,QAAAC,aAAAC,MAAA,KAAA,GAAAE,YAAAxK,KAAA,OAEA,SACA,KAAA,IAAA2C,OAAA,mCAAAwH,8HAgBArI,KAAAlB,UAAAiB,YAAA,gIC7FA,GAAA7B,KAAAmB,gBAAA2F,6DDyBA,MAAA,IAAAA,OAAA9G,KAAAmB,4FCkEA8H,SAAA,SAAArJ,4EC3FA,yCDmGAE,MAAAwF,OAAAI,gBAAAJ,qDACAxF,MAAA2F,WAAAC,gBAAAD,+GCpGA3F,YD2FAa,WAAAsI,SAAArJ,sKCrEA6K,WAAA,eACAC,kBAAA,iJAmBA,qBAAAC,+hBAbAA,KAAAC;;;;;;;uTL8DA,QAAAvD,kBAAA3C,IAAA4C,KAAmC1C,qBACnC,GAAM9E,OAANF,OAAAG,KAAAC,OAAAA,IAMA,IAJAF,MAAAwH,KAAAA,sEAIM5C,IAANZ,SAAAzB,cAAAK,IACM,KAAN,IAAA6E,WAAAC,0HAGA1H,MAAA2H,WAAAC,WAAAC,OACA,IAAQC,IAAR9H,MAAA+H,IAAAP,KAAAQ,eACAR,MAAAS,iBAAAH,GAAA9H,MAKA,IAAAkI,UAAAV,KAAAW,gBAAAnI,MAAA+H,KAAA5H,IAAAyE,IAAAzE,GACAA,KAAAiI,QAAA,qBAAA,EACAjI,IAAAA,IAAAkI,QAAA,mBAAA,IAAAH,SAAA,KAEA/H,IAAcmI,YAAd,qBAAAnI,IAAAO,OAAA,kBAAAA,SAEAP,IAAAA,IAAAoI,UAAA,EAAApI,IAAAO,OAAA,kBAAAA,SAAA,IAAAwH,UAEA,IAAAM,QAAAxI,MAAAyI,QAAAjB,KAAAkB,MAAAvI,KACAwI,OAAA,SAAAC,OACA,GAAU5I,MAAV2H,aAAAC,WAAAiB,UAAA,CAKA,GAFA7I,MAAA2H,WAAAC,WAAAkB,KACAtB,KAAAuB,QAAAP,SACAxI,MAAAgJ,UAAA,CACA,GAAAC,mBAAA,GAAAvF,kBAAA4B,KAAA4D,sBAAAzG,KAAA0D,aAAAtD,MAAA1C,IAAAA,KAOA,OALA2E,uBACAmE,kBAAAnE,oBAAAf,MAAAkF,wBAGA/D,kBAAAc,MAAA,GAAAmD,UAAAF,oBAEQ,GAARrD,iBAAA,GAAAlC,kBAAA4B,KAAAtF,MAAAoJ,cAAAjJ,IAAAA,KACAH,OAAA8E,sBACAc,gBAAA5F,MAAA8E,oBAAAf,MAAA6B,kBAEAV,iBAAAa,KAAA,GAAAoD,UAAAvD,kBACAV,iBAAAmE,aAEApD,QAAA,SAAAD,OAEA,GAAAhG,MAAA2H,aAAAC,WAAAiB,UAAA,CAGA7I,MAAA2H,WAAAC,WAAAkB,KAEAtB,KAAAuB,QAAAP,OACA,IAAA5C,iBAAA,GAAAlC,kBAAA4B,KAAAU,MAAAsD,QAAA7G,KAAA0D,aAAAtD,OACAiC,uBACAc,gBAAAd,oBAAAf,MAAA6B,kBAEAV,iBAAAc,MAAA,GAAAmD,UAAAvD,0BAEA4C,QAAAe,iBAAA,OAAAZ,8JAKAH,OAAAgB,oBAAA,QAAAvD,qUAoCA,QAAAwD,gBAAAC,MAAAA,QAAAA,QAAAA,OAAAA,MAAAA,KAAAA,YAAAA,2GAQA,QAAAC,eAAAC,cAAAtC,yPA3CA,MAAA,IAAAC,kBAAArF,QAAAhC,KAAA0J,cAAA1J,KAAAoH,+EDhKA7E,KAAAO,cAAAC;;;;;;;wDAuDA,QAAA0B,eAAAC,IAAAC,WAAAC,mCAEA5E,MAAAgC,QAAA0C,IAEA1E,KAAA6E,SAAA,GAAAC,iBAAAC,WAAA,SAAAC,6CAGAC,MAAAC,KAAA7C,cAAAqC,IAAAZ,QAAAqB,cAAAT,IAAAzE,wKAgBAmF,KAAA,mBAAAH,MAAAJ,SAAAI,KAAAI,aAAAJ,KAAAJ,qEAWA,IAAAS,SACcA,OAAdF,KAAA,IAAA,EAEQ,IAAI3E,SAAZC,QAAA6E,yBAAAN,KAAAO,2DAGAC,WAAAR,KAAAQ,YAAA,KACAC,gBAAA,GAAAlC,kBAAA4B,KAAAA,KAAAE,OAAAA,OAAA7E,QAAAA,QAAAgF,WAAAA,WAAAxF,IAAAA,KACA,OAAA2E,sBACAc,gBAAAd,oBAAAf,MAAA6B,4DAIA,OADAb,UAAAc,GAAAC,UAAAN,QACAT,SAAAc,IACAX,iBAAAa,KAAAhB,gDAKAG,kBAAAc,MAAAjB,WAGAkB,QAAA,SAAAC,KACA,GAAAN,iBAAA,GAAAlC,kBAEA4B,KAAAY,IAEczD,KAAd0D,aAAAtD,MACA2C,OAAAL,KAAAK,OACAG,WAAAR,KAAAQ,YAEA,OAAAb,sBACAc,gBAAAd,oBAAAf,MAAA6B,wEAcA,IATA5F,MAAAoG,uBAAAxB,IAAAO,MACA,MAAAP,IAAAjE,UACAiE,IAAAjE,QAAA,GAAAC,UAEAgE,IAAAjE,QAAA0F,IAAA,WACAzB,IAAAjE,QAAgB2F,OAAhB,SAAA,qCAEA1B,IAAAjE,QAAA4D,QAAA,SAAAgC,OAAAC,MAAA,MAAArB,MAAAsB,iBAAAD,KAAAD,OAAAG,KAAA,QAEA,MAAA9B,IAAA+B,cAAA,MAAAxB,KAAAwB,aACA,OAAA/B,IAAA+B,cACA,IAAAC,qBAAAC,YACA1B,KAAAwB,aAAA,aACA,MACA,KAAAC,qBAAAE,KACA3B,KAAAwB,aAAA,MACA,MAEA,KAAAC,qBAAAG,KACA5B,KAAAwB,aAAA,MAEA,MAEA,KAAAC,qBAAAI,KACA7B,KAAAwB,aAAA,MACA,MACA,SACA,KAAA,IAAA9D,OAAA,0VAqBA,GAAA,MAAM+B,IAANjE,SAAA,MAAAiE,IAAAjE,QAAAK,IAAA,gBAIA,OAAQ4D,IAARhD,aACA,IAAAX,aAAAK,KACA,KACA,KAAAL,aAAAC,KACAiE,KAAAsB,iBAAA,eAAA,mBACA,MACA,KAAAxF,aAAAE,KACAgE,KAAAsB,iBAAA,eAAA,kDACA,MACA,KAAAxF,aAAAa,KACAqD,KAAAsB,iBAAA,eAAA,aAEA,6JAoCA,QAAAQ,oBAAAC,YAAAC,0TAPAC,qVAyBA,oDAAA,GAAAzC,eAAAzC,QAAAhC,KAAAmH,YAAAnH,KAAAoH;;;;;;;AD3JA,GAAA5E,gBAAA,WAIA,QAAAA,gBAAAoB,qsBAoCApB,eAAA5B,UAAAiD,MAAA,SAAA5B,SACA,MAAA,IAAAO,iBACAsB,OAAA7B,SAAA,MAAAA,QAAA6B,OAAA7B,QAAA6B,OAAA9D,KAAA8D,4QAFAC,gBAAA9B,SAAA,MAAAA,QAAA8B,gBAAA9B,QAAA8B,gIAoBAvB,eAAA5B,UAAAoD,mBAAA,SAAA7D,kKAYAqC,eAAA5B,UAAAqD,aAAA,SAAAC,WACA,GAAApE,OAAAE,IACA,UAAAkE,YAAAA,aACA,IAAA/D,QAAA,GAAAE,uBACA8D,QAAAC,KAAAF,WAAAG,QAAA,SAAAC,sQAnGAC,MAAAvD,KAAAwD,UAAAD,sHAiJA,MAAA3E,QAAAG,KAAAC,MAAA8D,OAAAzB,cAAAK,IAAAjC,QAAA,GAAAC,YAAAV,WAgDAW,WAAA4C,mBAAA3D;;;;;;;AH5KA,GAAAD,SAAA,SAAAC,QAKA,QAAAD,SAAAE,gBAAA,GAAAC,OAAAF,OAAAG,KAAAC,OAAAA,KAEAC,IAAAJ,eAAAI,GACAH,OAAAG,IAAAJ,eAAAI,GACA,IAAAC,WAAAL,eAAAM,QAAAN,eAAAO,MACA,IAAAF,UAAA,CACA,GAAAC,QAAA,UAKAA,OAJA,gBAAAD,YAAAA,oBAAAG,iBAIAH,UAAAI,8GAKAC,OAAA,KAAAT,MAAAG,IAAAH,MAAAG,IAAAO,OAAA,GAAA,GAAA,iIASAV,MAAAW,QAAA,GAAAC,SAAAb,eAAAY,qKA9BAE,WAAAhB,QAAAC,QAwCAD,QAAAiB,UAAAC,kBAAA,WACA,OAAAb,KAAAS,QAAAK,IAAA,iBACA,IAAA,mBACA,MAAAC,aAAAC,IACA,KAAA,oCACA,MAAAD,aAAAE,IACA,KAAA,2GAKA,KAAA,kKAFAtB,QAAAiB,UAAAM,0BAAA,WAUA,MAAA,OAAAlB,KAAAmB,MACAJ,YAAAK,KACApB,KAAAmB,gBAAAd,iBACAU,YAAAE,KACAjB,KAAAmB,gBAAAE,UACAN,YAAAO,UACAtB,KAAAmB,gBAAAI,QACAR,YAAAS,6IAiBA7B,QAAAiB,UAAAa,QAAA,WACA,OAAAzB,KAAA0B,aACA,IAAAX,aAAAC,KACA,MAAAhB,MAToB2B,MAUpB,KAAAZ,aAAAE,KACA,MAAAjB,MAToB2B,MAUpB,KAAAZ,aAAAO,UACA,MAAAtB,MAAAmB,KACA,KAAAJ,aAAAa,KAEA,MAAA5B,MAAA2B,8EAqCA,MAAA3B,MAAA6B,aAAA,gCAKAC,wQCrFAC,MAAAnB,UAAAoB,QAAA,SAAA/B,IAAAgC,SACA,GAAAC,mBACA,IAAA,gBAAAjC,y0CAiDA,MAAAD,MAAAgC,QAAA,GAAArC,SAAAwC,aAAAnC,KAAAoC,gBAAAH,QAAAI,cAAAC,QAAArC,kIA8DAsC,KAAAC,yKA8BAC,MAAA7B,UAAAoB,QAAA,SAAA/B,IAAAgC,SACA,GAAAC,mBAIA,IAxDA,gBAAAjC,OAqDAA,IACA,GAAAN,SAAAwC,aAAAnC,KAAAoC,gBAAAH,QAAAI,cAAAK,IAAA,SAEAzC,cAAAN,UAnCA,KAAA,IAAAgD,OAAA,8FAhBA,KAAA,IAAAA,OAAA,8CAIA,OAkDAT,oBAAAU,YAAA5C,KAAA6C,SAAA5C,sCChSAsC,KAAAO,cAAAC,oGA0DA,IAAAC,YAAA,WACA,QAAAA,mCAIAA,YAAAC,aACAV,KAAAO,cAAAI,SAAAC,OACAC,8FAJAC,QAAAb,eAAAc,SAAAC,qBAaAF,QAAAG,gBAAAF,SAAAG,sJAqBA,IAAAC,aAAA,WACA,QAAAA,qCAIAA,aAAAT,aACAV,KAAAO,cAAAI,SAAAC,OACAC,sFAhBAO;;;;;;;"}