{"version": 3, "file": "collector.spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/collector.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AAEjC,8CAAmD;AACnD,wCAAiH;AAEjH,uDAAuE;AAEvE,QAAQ,CAAC,WAAW,EAAE;IACpB,IAAM,gBAAgB,GAAG,EAAE,CAAC,sBAAsB,EAAE,CAAC;IACrD,IAAI,IAAU,CAAC;IACf,IAAI,OAA2B,CAAC;IAChC,IAAI,OAAmB,CAAC;IACxB,IAAI,SAA4B,CAAC;IAEjC,UAAU,CAAC;QACT,IAAI,GAAG,IAAI,uBAAI,CAAC,KAAK,EAAE;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,aAAa;YACb,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,gBAAgB;YAChB,gBAAgB;YAChB,qBAAqB;YACrB,uBAAuB;YACvB,kBAAkB;YAClB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,0BAA0B;YAC1B,kCAAkC;YAClC,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YACjB,gBAAgB;YAChB,2BAA2B;YAC3B,kBAAkB;YAClB,uBAAuB;YACvB,0BAA0B;YAC1B,+BAA+B;YAC/B,sBAAsB;YACtB,6BAA6B;YAC7B,qCAAqC;YACrC,wBAAwB;SACzB,CAAC,CAAC;QACH,OAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAC3D,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAC/B,SAAS,GAAG,IAAI,6BAAiB,CAAC,EAAC,WAAW,EAAE,IAAI,EAAC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,cAAQ,qCAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3F,EAAE,CAAC,2DAA2D,EAAE;QAC9D,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACzD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE;QACxD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACxD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CACpB,EAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAC,IAAI,EAAE,EAAC,UAAU,EAAE,WAAW,EAAC,EAAC,EAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0DAA0D,EAAE;QAC7D,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;QACzE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,mBAAmB,EAAE;oBACnB,UAAU,EAAE,OAAO;oBACnB,UAAU,EAAE,CAAC;4BACX,UAAU,EAAE,MAAM;4BAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;4BACjF,SAAS,EAAE,CAAC;oCACV,QAAQ,EAAE,gBAAgB;oCAC1B,QAAQ,EAAE,mSASjB;iCACM,CAAC;yBACH,CAAC;oBACF,OAAO,EAAE;wBACP,IAAI,EAAE,CAAC;gCACL,UAAU,EAAE,UAAU;gCACtB,UAAU,EAAE,CAAC;wCACX,UAAU,EAAE,MAAM;wCAClB,UAAU,EACN,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAC;qCACtE,CAAC;6BACH,CAAC;qBACH;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE;QACnE,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QAClE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,YAAY,EAAE;oBACZ,UAAU,EAAE,OAAO;oBACnB,UAAU,EAAE,CAAC;4BACX,UAAU,EAAE,MAAM;4BAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;4BACjF,SAAS,EAAE,CAAC;oCACV,QAAQ,EAAE,QAAQ;oCAClB,QAAQ,EAAE,iZAUf;oCACK,UAAU,EAAE;wCACV;4CACE,UAAU,EAAE,WAAW;4CACvB,MAAM,EAAE,yBAAyB;4CACjC,IAAI,EAAE,qBAAqB;yCAC5B;wCACD,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAC;qCACpE;oCACD,SAAS,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;oCAC/E,KAAK,EAAE;wCACL,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,eAAe,EAAC;wCAC3E,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,eAAe,EAAC;qCAC5E;iCACF,CAAC;yBACH,CAAC;oBACF,OAAO,EAAE;wBACP,QAAQ,EAAE,CAAC;gCACT,UAAU,EAAE,aAAa;gCACzB,UAAU,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;6BACjF,CAAC;wBACF,QAAQ,EAAE,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAC,CAAC;wBAClC,QAAQ,EAAE,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAC,CAAC;wBAClC,SAAS,EAAE,CAAC,EAAC,UAAU,EAAE,QAAQ,EAAC,CAAC;qBACpC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,MAAM,EAAE;oBACN,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBAC1D,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBACvD,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBAC1D,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBAC3D,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBACzD,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBAC3D,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBACxD,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBACvD,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;oBACvD,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAC;iBAC1D;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,SAAwB,CAAC;IAC7B,IAAI,aAA6B,CAAC;IAElC,UAAU,CAAC;QACT,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QACxD,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE;QAChE,IAAM,QAAQ,GAAkB,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAClE,MAAM,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9B,IAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CACpB,CAAC,EAAC,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,YAAY,GAAG;YACnB,IAAI,EAAE,CAAC;oBACL,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,CAAC;4BACX,UAAU,EAAE,MAAM;4BAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAC;4BAC7E,SAAS,EAAE,CAAC,WAAW,CAAC;yBACzB,CAAC;iBACH,CAAC;SACH,CAAC;QACF,IAAM,WAAW,GAAkB,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACrE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAClD,IAAM,WAAW,GAAkB,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACrE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAClD,IAAM,YAAY,GAAkB,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACvE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE;QACpD,IAAM,UAAU,GAAkB,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;YACzB,UAAU,EAAE,OAAO;YACnB,UAAU,EAAE,CAAC;oBACX,UAAU,EAAE,MAAM;oBAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY,EAAC;iBACnF,CAAC;YACF,OAAO,EAAE;gBACP,QAAQ,EAAE,CAAC;wBACT,UAAU,EAAE,aAAa;wBACzB,UAAU,EAAE,CAAC;gCACX,UAAU,EAAE,WAAW;gCACvB,IAAI,EAAE,gBAAgB;gCACtB,SAAS,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAC,CAAC;6BACzD,CAAC;qBACH,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE;QAClD,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,CAAC,EAAE,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC;gBACxF,CAAC,EAAE,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC;gBACxF,CAAC,EAAE,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC;gBACxF,CAAC,EAAE,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC;gBACxF,CAAC,EAAE,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,0BAA0B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC;aACtF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2DAA2D,EAAE;QAC9D,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,IAAM,QAAQ,GAAkB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzD,IAAM,IAAI,GAAwB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;YACxB,UAAU,EAAE,OAAO;YACnB,OAAO,EAAE,iCAAiC;YAC1C,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,EAAC,SAAS,EAAE,KAAK,EAAC;SAC5B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChE,IAAM,IAAI,GAAwB,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;YACzB,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAC;SACpE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE;QACvC,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACjE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,WAAW,EAAE,EAAC,UAAU,EAAE,OAAO,EAAC;gBAClC,aAAa,EAAE,EAAC,UAAU,EAAE,OAAO,EAAC;gBACpC,aAAa,EAAE,EAAC,UAAU,EAAE,OAAO,EAAC;aACrC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE;QACvC,IAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QAC1E,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAC1D,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YACvB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE;gBACR,GAAG,EAAE;oBACH,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;oBAC3B,KAAK,EAAE;wBACL,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;wBACvC,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;wBACvC,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;qBACxC;iBACF;gBACD,GAAG,EAAE;oBACH,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;oBAC3B,KAAK,EAAE;wBACL,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;wBACvC,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;wBACvC,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;qBACxC;iBACF;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;oBAC3B,KAAK,EAAE;wBACL,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;wBAC1E,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC;qBACrC;iBACF;gBACD,aAAa,EAAE;oBACb,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,EAAE;oBACd,KAAK,EAAE;wBACL,UAAU,EAAE,KAAK;wBACjB,QAAQ,EAAE,GAAG;wBACb,OAAO,EAAE;4BACP,UAAU,EAAE,KAAK;4BACjB,QAAQ,EAAE,GAAG;4BACb,OAAO,EAAE;gCACP,UAAU,EAAE,QAAQ;gCACpB,UAAU,EAAE;oCACV,UAAU,EAAE,QAAQ;oCACpB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAC;oCACrD,MAAM,EAAE,SAAS;iCAClB;gCACD,MAAM,EAAE,WAAW;6BACpB;yBACF;qBACF;iBACF;gBACD,SAAS,EAAE,EAAC,UAAU,EAAE,UAAU,EAAC;gBACnC,UAAU,EAAE,EAAC,UAAU,EAAE,UAAU,EAAC;aACrC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChE,IAAM,IAAI,GAAwB,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;YACzB,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAC;SACpE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC9D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,IAAM,QAAQ,GAAQ,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE;QACpC,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC7D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,IAAM,UAAU,GAAQ,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACxD,IAAM,WAAW,GAAQ,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1D,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;QAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC9D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,IAAM,WAAW,GAAQ,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1D,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;YAC1B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,YAAY,EAAC;SAC9E,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kDAAkD,EAAE;QACrD,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAChC,IAAI,EAAE;gBACJ,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE;oBACL,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAC;oBAC3C,EAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC,EAAC;iBACnE;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE;QACxD,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QACrE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;gBACpC,UAAU,EAAE,MAAM;gBAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;gBACjF,SAAS,EAAE,CAAC;wBACV,SAAS,EAAE;4BACT,UAAU,EAAE,MAAM;4BAClB,UAAU,EAAE;gCACV,UAAU,EAAE,QAAQ;gCACpB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,UAAU,EAAC;gCAClF,MAAM,EAAE,MAAM;6BACf;4BACD,SAAS,EAAE,CAAC,GAAG,CAAC;yBACjB;qBACF,CAAC;aACH,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;QAC7C,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC/D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,aAAa,EAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;QACzE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC;gBACpC,UAAU,EAAE,MAAM;gBAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;gBACjF,SAAS,EAAE,CAAC;wBACV,SAAS,EAAE,CAAC;gCACV,OAAO,EAAE,GAAG;gCACZ,QAAQ,EAAE;oCACR,UAAU,EAAE,QAAQ;oCACpB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAC;oCACjF,MAAM,EAAE,OAAO;iCAChB;6BACF,CAAC;qBACH,CAAC;aACH,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kEAAkE,EAAE;QACrE,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QAClE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAChC,IAAI,EAAE;gBACJ,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE;oBACL,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAC,EAAE;wBAC3C,QAAQ,EAAE,GAAG;wBACb,QAAQ,EAAE;4BACR,UAAU,EAAE,IAAI;4BAChB,SAAS,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC;4BAClD,cAAc,EAAE,GAAG;4BACnB,cAAc,EAAE,GAAG;yBACpB;qBACF;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE;QAChE,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;QACvE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAM,SAAS,GAAkB,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAChC,IAAI,EAAE;gBACJ,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;gBAClC,QAAQ,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC;gBAClC,KAAK,EAAE;oBACL,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAC,EAAE;wBAC3C,UAAU,EAAE,IAAI;wBAChB,SAAS,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC;wBACjD,cAAc,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC,EAAC;wBAClF,cAAc,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC,EAAC;qBACnF;oBACD;wBACE,UAAU,EAAE,IAAI;wBAChB,SAAS,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC;wBACjD,cAAc,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC,EAAC;wBAClF,cAAc,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAC,EAAC;qBACnF;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE;QAClD,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACvD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAC/B,EAAC,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAC;YAC9C,EAAC,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,aAAa,EAAC,CAAC,EAAC;YAC9E,EAAC,IAAI,EAAE,eAAe,EAAC;SACxB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8CAA8C,EAAE;QACjD,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACvD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAC,YAAY,EAAE,EAAC,UAAU,EAAE,UAAU,EAAC,EAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE;QAC/D,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QACzD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAChC,OAAO,EAAE,MAAM,CAAC,EAAC,UAAU,EAAE,OAAO,EAAC,CAAC;YACtC,WAAW,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,KAAK,EAAC;YACvF,aAAa,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAC;SACrF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mFAAmF,EAAE;QACtF,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QAC7D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAChC,kBAAkB,EAAE;gBAClB,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE,6BAA6B;gBACtC,IAAI,EAAE,CAAC;gBACP,SAAS,EAAE,CAAC;gBACZ,OAAO,EAAE,EAAC,IAAI,EAAE,UAAU,EAAC;aAC5B;YACD,aAAa,EAAE;gBACb,UAAU,EAAE,OAAO;gBACnB,UAAU,EAAE,CAAC;wBACX,UAAU,EAAE,MAAM;wBAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;wBACjF,SAAS,EAAE,CAAC,EAAC,SAAS,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,oBAAoB,EAAC,CAAC,EAAC,CAAC;qBAClF,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qFAAqF,EAAE;QACxF,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QAC/D,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAChC,kBAAkB,EAAE;gBAClB,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE,sCAAsC;gBAC/C,IAAI,EAAE,CAAC;gBACP,SAAS,EAAE,EAAE;gBACb,OAAO,EAAE,EAAC,IAAI,EAAE,UAAU,EAAC;aAC5B;YACD,aAAa,EAAE;gBACb,UAAU,EAAE,OAAO;gBACnB,UAAU,EAAE,CAAC;wBACX,UAAU,EAAE,MAAM;wBAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAC;wBACjF,SAAS,EAAE,CAAC,EAAC,SAAS,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,oBAAoB,EAAC,CAAC,EAAC,CAAC;qBAClF,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gFAAgF,EAAE;QACnF,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QAClE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAChC,GAAG,EAAE;gBACH,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE;oBACL,UAAU,EAAE,OAAO;oBACnB,OAAO,EAAE,6BAA6B;oBACtC,IAAI,EAAE,CAAC;oBACP,SAAS,EAAE,CAAC;oBACZ,OAAO,EAAE,EAAC,IAAI,EAAE,aAAa,EAAC;iBAC/B;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACxE,QAAQ,EAAE,CAAC;oBACT,UAAU,EAAE,aAAa;oBACzB,mBAAmB,EAAE,CAAC,CAAC;gCACrB,UAAU,EAAE,MAAM;gCAClB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAC;gCAC9E,SAAS,EAAE,CAAC,GAAG,CAAC;6BACjB,CAAC,CAAC;oBACH,UAAU,EAAE,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;iBACrD,CAAC;SACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,WAAW,IAAY,EAAE,MAAe;YACtC,IAAM,MAAM,GAAG,YAAY,CAAC,CAAG,MAAM,IAAI,EAAE,6BAAuB,IAAI,MAAG,CAAC,CAAC;YAC3E,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,EAAE,CAAC,qDAAqD,EACrD,cAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,EAAE,CAAC,8CAA8C,EAC9C,cAAQ,CAAC,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1E,EAAE,CAAC,+CAA+C,EAAE;YAClD,CAAC,CAAC,+BAA+B,EAAE,uCAAuC,CAAC;iBACtE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE;YAC7D,CAAC,CAAC,wBAAwB,EAAE,sCAAsC,CAAC,CAAC,OAAO,CAAC;gBAC1E,UAAU,EAAE,OAAO;gBACnB,QAAQ,EAAE,GAAG;gBACb,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE;oBACL,UAAU,EAAE,WAAW;oBACvB,MAAM,EAAE,YAAY;oBACpB,IAAI,EAAE,UAAU;iBACjB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,CAAC,CAAC,eAAe,EAAE,sCAAsC,CAAC;iBACrD,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE;YACxE,CAAC,CAAC,+BAA+B,EAAE,sCAAsC,CAAC,CAAC,OAAO,CAAC;gBACjF,UAAU,EAAE,OAAO;gBACnB,QAAQ,EAAE,GAAG;gBACb,IAAI,EAAE;oBACJ,UAAU,EAAE,OAAO;oBACnB,QAAQ,EAAE,GAAG;oBACb,IAAI,EAAE;wBACJ,UAAU,EAAE,OAAO;wBACnB,QAAQ,EAAE,GAAG;wBACb,IAAI,EAAE;4BACJ,UAAU,EAAE,OAAO;4BACnB,QAAQ,EAAE,GAAG;4BACb,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAC;yBACpE;wBACD,KAAK,EAAE,QAAQ;qBAChB;oBACD,KAAK,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAC;iBACpE;gBACD,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE;YACnC,CAAC,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;gBAC3B,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE,2DAA2D;gBACpE,IAAI,EAAE,CAAC;gBACP,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAC9B,aAAa,EAAE,mNAKlB,EACG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAClC,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACxE,QAAQ,EAAE,CAAC;oBACT,UAAU,EAAE,aAAa;oBACzB,UAAU,EAAE;wBACV,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAC;wBACvD,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAC;wBACvD,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAC;wBACvD,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAC;wBACvD,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAC;qBACxD;iBACF,CAAC;SACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,oFAAoF,EAAE;YACvF,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;YAC7D,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+EAA+E,EAAE;YAClF,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;YAC/D,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC;iBAC5C,YAAY,CAAC,sCAAsC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAChE,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,EAAzC,CAAyC,CAAC;iBAClD,YAAY,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAChE,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,EAAzC,CAAyC,CAAC;iBAClD,YAAY,CAAC,mCAAmC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAM,QAAQ,GAAG,mBAAmB,CAAC;YACrC,QAAQ,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YACnC,IAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,EAAnC,CAAmC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE;YAClD,IAAM,QAAQ,GAAG,sBAAsB,CAAC;YACxC,QAAQ,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;YACtC,IAAM,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,cAAM,OAAA,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,EAAtC,CAAsC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE;QACtB,EAAE,CAAC,sDAAsD,EAAE;YACzD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvF,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;iBAC1C,OAAO,CAAC,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAC,EAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE;YACjE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvF,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;iBAC1C,OAAO,CAAC,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAC,EAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE;YACpE,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvF,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC;gBACvD,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE;oBACP,UAAU,EAAE,WAAW;oBACvB,MAAM,EAAE,4BAA4B;oBACpC,IAAI,EAAE,0BAA0B;iBACjC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qBAAqB,KAAoB;YACvC,IAAM,MAAM,GAAG,wBAAe,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;QAED,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAEjF,IAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1D,IAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,IAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,IAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzC,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,IAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE;QACrB,EAAE,CAAC,uDAAuD,EAAE;YAC1D,IAAM,MAAM,GAAG,YAAY,CAAC,8HAK3B,CAAC,CAAC;YACH,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAC,EAAE,EAAE,CAAC,EAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,EAAE,CAAC,gCAAgC,EAAE;YACnC,IAAM,MAAM,GAAG,YAAY,CAAC,4QAWxB,CAAC,CAAC;YACN,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;YACvC,IAAM,MAAM,GAAG,YAAY,CAAC,2SAczB,CAAC,CAAC;YACL,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,kBAAkB,QAAgB,EAAE,OAAe;QACjD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvB,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;IACjC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,wEAAwE;AACxE,IAAM,KAAK,GAAc;IACvB,KAAK,EAAE;QACL,kBAAkB,EAAE,iZAWN;YACV,GAAG;YACH,iZAUC;YACD,GAAG;YACH,uoBAqBA;QACJ,SAAS,EAAE,sFAIP;QACJ,UAAU,EAAE,EAAE;QACd,0BAA0B,EAAE,4KAMb;YACX,GAAG;YACH,mSASD;YACC,GAAG;YACH,qGAIA;QACJ,gBAAgB,EAAE,+jBAcb;QACL,qBAAqB,EAAE,uDAGtB;QACD,iBAAiB,EAAE,+dAgBW;QAC9B,eAAe,EAAE,m/BA4Cf;QACF,gBAAgB,EAAE,+JAMjB;KACF;IACD,YAAY,EAAE,6vCAuBb;IACD,gBAAgB,EAAE,gMAMjB;IACD,kBAAkB,EAAE,iGAInB;IACD,kBAAkB,EAAE,mKASnB;IACD,kBAAkB,EAAE,0HAQnB;IACD,wBAAwB,EAAE,sMAQzB;IACD,gBAAgB,EAAE,8MAQjB;IACD,qBAAqB,EAAE,8HAItB;IACD,6BAA6B,EAAE,oDAE9B;IACD,sBAAsB,EAAE,mUAUvB;IACD,uBAAuB,EAAE,4kBAqBxB;IACD,kBAAkB,EAAE,uOAMnB;IACD,oBAAoB,EAAE,0CAErB;IACD,kBAAkB,EAAE,0LASnB;IACD,+BAA+B,EAAE,mWAUhC;IACD,uBAAuB,EAAE,8LAQxB;IACD,iBAAiB,EAAE,+EAIlB;IACD,2BAA2B,EAAE,yNAQ5B;IACD,0BAA0B,EAAE,0MAS3B;IACD,eAAe,EAAE,0JAIhB;IACD,iBAAiB,EAAE,2MAKlB;IACD,gBAAgB,EAAE,iGAGlB;IACA,qBAAqB,EAAE,8TAetB;IACD,iBAAiB,EAAE,gFAGlB;IACD,uBAAuB,EAAE,uUAexB;IACD,0BAA0B,EAAE,gIAM3B;IACD,cAAc,EAAE;QACd,UAAU,EAAE;YACV,WAAW,EAAE,imEAwDZ;YACD,aAAa,EAAE,kYAYd;SACF;KACF;CACF,CAAC;AAEF,sBAAsB,IAAY;IAChC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACrE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\nimport {MetadataCollector} from '../src/collector';\nimport {ClassMetadata, ConstructorMetadata, MetadataEntry, ModuleMetadata, isClassMetadata} from '../src/schema';\n\nimport {Directory, Host, expectValidSources} from './typescript.mocks';\n\ndescribe('Collector', () => {\n  const documentRegistry = ts.createDocumentRegistry();\n  let host: Host;\n  let service: ts.LanguageService;\n  let program: ts.Program;\n  let collector: MetadataCollector;\n\n  beforeEach(() => {\n    host = new Host(FILES, [\n      '/app/app.component.ts',\n      '/app/cases-data.ts',\n      '/app/error-cases.ts',\n      '/promise.ts',\n      '/unsupported-1.ts',\n      '/unsupported-2.ts',\n      '/unsupported-3.ts',\n      'class-arity.ts',\n      'import-star.ts',\n      'exported-classes.ts',\n      'exported-functions.ts',\n      'exported-enum.ts',\n      'exported-consts.ts',\n      'local-symbol-ref.ts',\n      'local-function-ref.ts',\n      'local-symbol-ref-func.ts',\n      'local-symbol-ref-func-dynamic.ts',\n      'private-enum.ts',\n      're-exports.ts',\n      're-exports-2.ts',\n      'export-as.d.ts',\n      'static-field-reference.ts',\n      'static-method.ts',\n      'static-method-call.ts',\n      'static-method-with-if.ts',\n      'static-method-with-default.ts',\n      'class-inheritance.ts',\n      'class-inheritance-parent.ts',\n      'class-inheritance-declarations.d.ts',\n      'interface-reference.ts'\n    ]);\n    service = ts.createLanguageService(host, documentRegistry);\n    program = service.getProgram();\n    collector = new MetadataCollector({quotedNames: true});\n  });\n\n  it('should not have errors in test data', () => { expectValidSources(service, program); });\n\n  it('should return undefined for modules that have no metadata', () => {\n    const sourceFile = program.getSourceFile('app/empty.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toBeUndefined();\n  });\n\n  it('should return an interface reference for interfaces', () => {\n    const sourceFile = program.getSourceFile('app/hero.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toEqual(\n        {__symbolic: 'module', version: 3, metadata: {Hero: {__symbolic: 'interface'}}});\n  });\n\n  it('should be able to collect a simple component\\'s metadata', () => {\n    const sourceFile = program.getSourceFile('app/hero-detail.component.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        HeroDetailComponent: {\n          __symbolic: 'class',\n          decorators: [{\n            __symbolic: 'call',\n            expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n            arguments: [{\n              selector: 'my-hero-detail',\n              template: `\n        <div *ngIf=\"hero\">\n          <h2>{{hero.name}} details!</h2>\n          <div><label>id: </label>{{hero.id}}</div>\n          <div>\n            <label>name: </label>\n            <input [(ngModel)]=\"hero.name\" placeholder=\"name\"/>\n          </div>\n        </div>\n      `\n            }]\n          }],\n          members: {\n            hero: [{\n              __symbolic: 'property',\n              decorators: [{\n                __symbolic: 'call',\n                expression:\n                    {__symbolic: 'reference', module: 'angular2/core', name: 'Input'}\n              }]\n            }]\n          }\n        }\n      }\n    });\n  });\n\n  it('should be able to get a more complicated component\\'s metadata', () => {\n    const sourceFile = program.getSourceFile('/app/app.component.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        AppComponent: {\n          __symbolic: 'class',\n          decorators: [{\n            __symbolic: 'call',\n            expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n            arguments: [{\n              selector: 'my-app',\n              template: `\n        <h2>My Heroes</h2>\n        <ul class=\"heroes\">\n          <li *ngFor=\"#hero of heroes\"\n            (click)=\"onSelect(hero)\"\n            [class.selected]=\"hero === selectedHero\">\n            <span class=\"badge\">{{hero.id | lowercase}}</span> {{hero.name | uppercase}}\n          </li>\n        </ul>\n        <my-hero-detail [hero]=\"selectedHero\"></my-hero-detail>\n        `,\n              directives: [\n                {\n                  __symbolic: 'reference',\n                  module: './hero-detail.component',\n                  name: 'HeroDetailComponent',\n                },\n                {__symbolic: 'reference', module: 'angular2/common', name: 'NgFor'}\n              ],\n              providers: [{__symbolic: 'reference', module: './hero.service', default: true}],\n              pipes: [\n                {__symbolic: 'reference', module: 'angular2/common', name: 'LowerCasePipe'},\n                {__symbolic: 'reference', module: 'angular2/common', name: 'UpperCasePipe'}\n              ]\n            }]\n          }],\n          members: {\n            __ctor__: [{\n              __symbolic: 'constructor',\n              parameters: [{__symbolic: 'reference', module: './hero.service', default: true}]\n            }],\n            onSelect: [{__symbolic: 'method'}],\n            ngOnInit: [{__symbolic: 'method'}],\n            getHeroes: [{__symbolic: 'method'}]\n          }\n        }\n      }\n    });\n  });\n\n  it('should return the values of exported variables', () => {\n    const sourceFile = program.getSourceFile('/app/mock-heroes.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        HEROES: [\n          {'id': 11, 'name': 'Mr. Nice', '$quoted$': ['id', 'name']},\n          {'id': 12, 'name': 'Narco', '$quoted$': ['id', 'name']},\n          {'id': 13, 'name': 'Bombasto', '$quoted$': ['id', 'name']},\n          {'id': 14, 'name': 'Celeritas', '$quoted$': ['id', 'name']},\n          {'id': 15, 'name': 'Magneta', '$quoted$': ['id', 'name']},\n          {'id': 16, 'name': 'RubberMan', '$quoted$': ['id', 'name']},\n          {'id': 17, 'name': 'Dynama', '$quoted$': ['id', 'name']},\n          {'id': 18, 'name': 'Dr IQ', '$quoted$': ['id', 'name']},\n          {'id': 19, 'name': 'Magma', '$quoted$': ['id', 'name']},\n          {'id': 20, 'name': 'Tornado', '$quoted$': ['id', 'name']}\n        ]\n      }\n    });\n  });\n\n  let casesFile: ts.SourceFile;\n  let casesMetadata: ModuleMetadata;\n\n  beforeEach(() => {\n    casesFile = program.getSourceFile('/app/cases-data.ts');\n    casesMetadata = collector.getMetadata(casesFile);\n  });\n\n  it('should provide any reference for an any ctor parameter type', () => {\n    const casesAny = <ClassMetadata>casesMetadata.metadata['CaseAny'];\n    expect(casesAny).toBeTruthy();\n    const ctorData = casesAny.members['__ctor__'];\n    expect(ctorData).toEqual(\n        [{__symbolic: 'constructor', parameters: [{__symbolic: 'reference', name: 'any'}]}]);\n  });\n\n  it('should record annotations on set and get declarations', () => {\n    const propertyData = {\n      name: [{\n        __symbolic: 'property',\n        decorators: [{\n          __symbolic: 'call',\n          expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Input'},\n          arguments: ['firstName']\n        }]\n      }]\n    };\n    const caseGetProp = <ClassMetadata>casesMetadata.metadata['GetProp'];\n    expect(caseGetProp.members).toEqual(propertyData);\n    const caseSetProp = <ClassMetadata>casesMetadata.metadata['SetProp'];\n    expect(caseSetProp.members).toEqual(propertyData);\n    const caseFullProp = <ClassMetadata>casesMetadata.metadata['FullProp'];\n    expect(caseFullProp.members).toEqual(propertyData);\n  });\n\n  it('should record references to parameterized types', () => {\n    const casesForIn = <ClassMetadata>casesMetadata.metadata['NgFor'];\n    expect(casesForIn).toEqual({\n      __symbolic: 'class',\n      decorators: [{\n        __symbolic: 'call',\n        expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Injectable'}\n      }],\n      members: {\n        __ctor__: [{\n          __symbolic: 'constructor',\n          parameters: [{\n            __symbolic: 'reference',\n            name: 'ClassReference',\n            arguments: [{__symbolic: 'reference', name: 'NgForRow'}]\n          }]\n        }]\n      }\n    });\n  });\n\n  it('should report errors for destructured imports', () => {\n    const unsupported1 = program.getSourceFile('/unsupported-1.ts');\n    const metadata = collector.getMetadata(unsupported1);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        a: {__symbolic: 'error', message: 'Destructuring not supported', line: 1, character: 16},\n        b: {__symbolic: 'error', message: 'Destructuring not supported', line: 1, character: 19},\n        c: {__symbolic: 'error', message: 'Destructuring not supported', line: 2, character: 16},\n        d: {__symbolic: 'error', message: 'Destructuring not supported', line: 2, character: 19},\n        e: {__symbolic: 'error', message: 'Variable not initialized', line: 3, character: 15}\n      }\n    });\n  });\n\n  it('should report an error for references to unexpected types', () => {\n    const unsupported1 = program.getSourceFile('/unsupported-2.ts');\n    const metadata = collector.getMetadata(unsupported1);\n    const barClass = <ClassMetadata>metadata.metadata['Bar'];\n    const ctor = <ConstructorMetadata>barClass.members['__ctor__'][0];\n    const parameter = ctor.parameters[0];\n    expect(parameter).toEqual({\n      __symbolic: 'error',\n      message: 'Reference to non-exported class',\n      line: 3,\n      character: 4,\n      context: {className: 'Foo'}\n    });\n  });\n\n  it('should be able to handle import star type references', () => {\n    const importStar = program.getSourceFile('/import-star.ts');\n    const metadata = collector.getMetadata(importStar);\n    const someClass = <ClassMetadata>metadata.metadata['SomeClass'];\n    const ctor = <ConstructorMetadata>someClass.members['__ctor__'][0];\n    const parameters = ctor.parameters;\n    expect(parameters).toEqual([\n      {__symbolic: 'reference', module: 'angular2/common', name: 'NgFor'}\n    ]);\n  });\n\n  it('should record all exported classes', () => {\n    const sourceFile = program.getSourceFile('/exported-classes.ts');\n    const metadata = collector.getMetadata(sourceFile);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        SimpleClass: {__symbolic: 'class'},\n        AbstractClass: {__symbolic: 'class'},\n        DeclaredClass: {__symbolic: 'class'}\n      }\n    });\n  });\n\n  it('should be able to record functions', () => {\n    const exportedFunctions = program.getSourceFile('/exported-functions.ts');\n    const metadata = collector.getMetadata(exportedFunctions);\n    expect(metadata).toEqual({\n      __symbolic: 'module',\n      version: 3,\n      metadata: {\n        one: {\n          __symbolic: 'function',\n          parameters: ['a', 'b', 'c'],\n          value: {\n            a: {__symbolic: 'reference', name: 'a'},\n            b: {__symbolic: 'reference', name: 'b'},\n            c: {__symbolic: 'reference', name: 'c'}\n          }\n        },\n        two: {\n          __symbolic: 'function',\n          parameters: ['a', 'b', 'c'],\n          value: {\n            a: {__symbolic: 'reference', name: 'a'},\n            b: {__symbolic: 'reference', name: 'b'},\n            c: {__symbolic: 'reference', name: 'c'}\n          }\n        },\n        three: {\n          __symbolic: 'function',\n          parameters: ['a', 'b', 'c'],\n          value: [\n            {__symbolic: 'reference', name: 'a'}, {__symbolic: 'reference', name: 'b'},\n            {__symbolic: 'reference', name: 'c'}\n          ]\n        },\n        supportsState: {\n          __symbolic: 'function',\n          parameters: [],\n          value: {\n            __symbolic: 'pre',\n            operator: '!',\n            operand: {\n              __symbolic: 'pre',\n              operator: '!',\n              operand: {\n                __symbolic: 'select',\n                expression: {\n                  __symbolic: 'select',\n                  expression: {__symbolic: 'reference', name: 'window'},\n                  member: 'history'\n                },\n                member: 'pushState'\n              }\n            }\n          }\n        },\n        complexFn: {__symbolic: 'function'},\n        declaredFn: {__symbolic: 'function'}\n      }\n    });\n  });\n\n  it('should be able to handle import star type references', () => {\n    const importStar = program.getSourceFile('/import-star.ts');\n    const metadata = collector.getMetadata(importStar);\n    const someClass = <ClassMetadata>metadata.metadata['SomeClass'];\n    const ctor = <ConstructorMetadata>someClass.members['__ctor__'][0];\n    const parameters = ctor.parameters;\n    expect(parameters).toEqual([\n      {__symbolic: 'reference', module: 'angular2/common', name: 'NgFor'}\n    ]);\n  });\n\n  it('should be able to collect the value of an enum', () => {\n    const enumSource = program.getSourceFile('/exported-enum.ts');\n    const metadata = collector.getMetadata(enumSource);\n    const someEnum: any = metadata.metadata['SomeEnum'];\n    expect(someEnum).toEqual({A: 0, B: 1, C: 100, D: 101});\n  });\n\n  it('should ignore a non-export enum', () => {\n    const enumSource = program.getSourceFile('/private-enum.ts');\n    const metadata = collector.getMetadata(enumSource);\n    const publicEnum: any = metadata.metadata['PublicEnum'];\n    const privateEnum: any = metadata.metadata['PrivateEnum'];\n    expect(publicEnum).toEqual({a: 0, b: 1, c: 2});\n    expect(privateEnum).toBeUndefined();\n  });\n\n  it('should be able to collect enums initialized from consts', () => {\n    const enumSource = program.getSourceFile('/exported-enum.ts');\n    const metadata = collector.getMetadata(enumSource);\n    const complexEnum: any = metadata.metadata['ComplexEnum'];\n    expect(complexEnum).toEqual({\n      A: 0,\n      B: 1,\n      C: 30,\n      D: 40,\n      E: {__symbolic: 'reference', module: './exported-consts', name: 'constValue'}\n    });\n  });\n\n  it('should be able to collect a simple static method', () => {\n    const staticSource = program.getSourceFile('/static-method.ts');\n    const metadata = collector.getMetadata(staticSource);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['MyModule'];\n    expect(classData).toBeDefined();\n    expect(classData.statics).toEqual({\n      with: {\n        __symbolic: 'function',\n        parameters: ['comp'],\n        value: [\n          {__symbolic: 'reference', name: 'MyModule'},\n          {provider: 'a', useValue: {__symbolic: 'reference', name: 'comp'}}\n        ]\n      }\n    });\n  });\n\n  it('should be able to collect a call to a static method', () => {\n    const staticSource = program.getSourceFile('/static-method-call.ts');\n    const metadata = collector.getMetadata(staticSource);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['Foo'];\n    expect(classData).toBeDefined();\n    expect(classData.decorators).toEqual([{\n      __symbolic: 'call',\n      expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n      arguments: [{\n        providers: {\n          __symbolic: 'call',\n          expression: {\n            __symbolic: 'select',\n            expression: {__symbolic: 'reference', module: './static-method', name: 'MyModule'},\n            member: 'with'\n          },\n          arguments: ['a']\n        }\n      }]\n    }]);\n  });\n\n  it('should be able to collect a static field', () => {\n    const staticSource = program.getSourceFile('/static-field.ts');\n    const metadata = collector.getMetadata(staticSource);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['MyModule'];\n    expect(classData).toBeDefined();\n    expect(classData.statics).toEqual({VALUE: 'Some string'});\n  });\n\n  it('should be able to collect a reference to a static field', () => {\n    const staticSource = program.getSourceFile('/static-field-reference.ts');\n    const metadata = collector.getMetadata(staticSource);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['Foo'];\n    expect(classData).toBeDefined();\n    expect(classData.decorators).toEqual([{\n      __symbolic: 'call',\n      expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n      arguments: [{\n        providers: [{\n          provide: 'a',\n          useValue: {\n            __symbolic: 'select',\n            expression: {__symbolic: 'reference', module: './static-field', name: 'MyModule'},\n            member: 'VALUE'\n          }\n        }]\n      }]\n    }]);\n  });\n\n  it('should be able to collect a method with a conditional expression', () => {\n    const source = program.getSourceFile('/static-method-with-if.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['MyModule'];\n    expect(classData).toBeDefined();\n    expect(classData.statics).toEqual({\n      with: {\n        __symbolic: 'function',\n        parameters: ['cond'],\n        value: [\n          {__symbolic: 'reference', name: 'MyModule'}, {\n            provider: 'a',\n            useValue: {\n              __symbolic: 'if',\n              condition: {__symbolic: 'reference', name: 'cond'},\n              thenExpression: '1',\n              elseExpression: '2'\n            }\n          }\n        ]\n      }\n    });\n  });\n\n  it('should be able to collect a method with a default parameter', () => {\n    const source = program.getSourceFile('/static-method-with-default.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata).toBeDefined();\n    const classData = <ClassMetadata>metadata.metadata['MyModule'];\n    expect(classData).toBeDefined();\n    expect(classData.statics).toEqual({\n      with: {\n        __symbolic: 'function',\n        parameters: ['comp', 'foo', 'bar'],\n        defaults: [undefined, true, false],\n        value: [\n          {__symbolic: 'reference', name: 'MyModule'}, {\n            __symbolic: 'if',\n            condition: {__symbolic: 'reference', name: 'foo'},\n            thenExpression: {provider: 'a', useValue: {__symbolic: 'reference', name: 'comp'}},\n            elseExpression: {provider: 'b', useValue: {__symbolic: 'reference', name: 'comp'}}\n          },\n          {\n            __symbolic: 'if',\n            condition: {__symbolic: 'reference', name: 'bar'},\n            thenExpression: {provider: 'c', useValue: {__symbolic: 'reference', name: 'comp'}},\n            elseExpression: {provider: 'd', useValue: {__symbolic: 'reference', name: 'comp'}}\n          }\n        ]\n      }\n    });\n  });\n\n  it('should be able to collect re-exported symbols', () => {\n    const source = program.getSourceFile('/re-exports.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.exports).toEqual([\n      {from: './static-field', export: ['MyModule']},\n      {from: './static-field-reference', export: [{name: 'Foo', as: 'OtherModule'}]},\n      {from: 'angular2/core'}\n    ]);\n  });\n\n  it('should be able to collect a export as symbol', () => {\n    const source = program.getSourceFile('export-as.d.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.metadata).toEqual({SomeFunction: {__symbolic: 'function'}});\n  });\n\n  it('should be able to collect exports with no module specifier', () => {\n    const source = program.getSourceFile('/re-exports-2.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.metadata).toEqual({\n      MyClass: Object({__symbolic: 'class'}),\n      OtherModule: {__symbolic: 'reference', module: './static-field-reference', name: 'Foo'},\n      MyOtherModule: {__symbolic: 'reference', module: './static-field', name: 'MyModule'}\n    });\n  });\n\n  it('should collect an error symbol if collecting a reference to a non-exported symbol', () => {\n    const source = program.getSourceFile('/local-symbol-ref.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.metadata).toEqual({\n      REQUIRED_VALIDATOR: {\n        __symbolic: 'error',\n        message: 'Reference to a local symbol',\n        line: 3,\n        character: 8,\n        context: {name: 'REQUIRED'}\n      },\n      SomeComponent: {\n        __symbolic: 'class',\n        decorators: [{\n          __symbolic: 'call',\n          expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n          arguments: [{providers: [{__symbolic: 'reference', name: 'REQUIRED_VALIDATOR'}]}]\n        }]\n      }\n    });\n  });\n\n  it('should collect an error symbol if collecting a reference to a non-exported function', () => {\n    const source = program.getSourceFile('/local-function-ref.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.metadata).toEqual({\n      REQUIRED_VALIDATOR: {\n        __symbolic: 'error',\n        message: 'Reference to a non-exported function',\n        line: 3,\n        character: 13,\n        context: {name: 'required'}\n      },\n      SomeComponent: {\n        __symbolic: 'class',\n        decorators: [{\n          __symbolic: 'call',\n          expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Component'},\n          arguments: [{providers: [{__symbolic: 'reference', name: 'REQUIRED_VALIDATOR'}]}]\n        }]\n      }\n    });\n  });\n\n  it('should collect an error for a simple function that references a local variable', () => {\n    const source = program.getSourceFile('/local-symbol-ref-func.ts');\n    const metadata = collector.getMetadata(source);\n    expect(metadata.metadata).toEqual({\n      foo: {\n        __symbolic: 'function',\n        parameters: ['index'],\n        value: {\n          __symbolic: 'error',\n          message: 'Reference to a local symbol',\n          line: 1,\n          character: 8,\n          context: {name: 'localSymbol'}\n        }\n      }\n    });\n  });\n\n  it('should collect any for interface parameter reference', () => {\n    const source = program.getSourceFile('/interface-reference.ts');\n    const metadata = collector.getMetadata(source);\n    expect((metadata.metadata['SomeClass'] as ClassMetadata).members).toEqual({\n      __ctor__: [{\n        __symbolic: 'constructor',\n        parameterDecorators: [[{\n          __symbolic: 'call',\n          expression: {__symbolic: 'reference', module: 'angular2/core', name: 'Inject'},\n          arguments: ['a']\n        }]],\n        parameters: [{__symbolic: 'reference', name: 'any'}]\n      }]\n    });\n  });\n\n  describe('with interpolations', () => {\n    function e(expr: string, prefix?: string) {\n      const source = createSource(`${prefix || ''} export let value = ${expr};`);\n      const metadata = collector.getMetadata(source);\n      return expect(metadata.metadata['value']);\n    }\n\n    it('should be able to collect a raw interpolated string',\n       () => { e('`simple value`').toBe('simple value'); });\n\n    it('should be able to interpolate a single value',\n       () => { e('`${foo}`', 'const foo = \"foo value\"').toBe('foo value'); });\n\n    it('should be able to interpolate multiple values', () => {\n      e('`foo:${foo}, bar:${bar}, end`', 'const foo = \"foo\"; const bar = \"bar\";')\n          .toBe('foo:foo, bar:bar, end');\n    });\n\n    it('should be able to interpolate with an imported reference', () => {\n      e('`external:${external}`', 'import {external} from \"./external\";').toEqual({\n        __symbolic: 'binop',\n        operator: '+',\n        left: 'external:',\n        right: {\n          __symbolic: 'reference',\n          module: './external',\n          name: 'external',\n        }\n      });\n    });\n\n    it('should simplify a redundant template', () => {\n      e('`${external}`', 'import {external} from \"./external\";')\n          .toEqual({__symbolic: 'reference', module: './external', name: 'external'});\n    });\n\n    it('should be able to collect complex template with imported references', () => {\n      e('`foo:${foo}, bar:${bar}, end`', 'import {foo, bar} from \"./external\";').toEqual({\n        __symbolic: 'binop',\n        operator: '+',\n        left: {\n          __symbolic: 'binop',\n          operator: '+',\n          left: {\n            __symbolic: 'binop',\n            operator: '+',\n            left: {\n              __symbolic: 'binop',\n              operator: '+',\n              left: 'foo:',\n              right: {__symbolic: 'reference', module: './external', name: 'foo'}\n            },\n            right: ', bar:'\n          },\n          right: {__symbolic: 'reference', module: './external', name: 'bar'}\n        },\n        right: ', end'\n      });\n    });\n\n    it('should reject a tagged literal', () => {\n      e('tag`some value`').toEqual({\n        __symbolic: 'error',\n        message: 'Tagged template expressions are not supported in metadata',\n        line: 0,\n        character: 20\n      });\n    });\n  });\n\n  it('should ignore |null or |undefined in type expressions', () => {\n    const source = ts.createSourceFile(\n        'somefile.ts', `\n      import {Foo} from './foo';\n      export class SomeClass {\n        constructor (a: Foo, b: Foo | null, c: Foo | undefined, d: Foo | undefined | null, e: Foo | undefined | null | Foo) {}\n      }\n    `,\n        ts.ScriptTarget.Latest, true);\n    const metadata = collector.getMetadata(source);\n    expect((metadata.metadata['SomeClass'] as ClassMetadata).members).toEqual({\n      __ctor__: [{\n        __symbolic: 'constructor',\n        parameters: [\n          {__symbolic: 'reference', module: './foo', name: 'Foo'},\n          {__symbolic: 'reference', module: './foo', name: 'Foo'},\n          {__symbolic: 'reference', module: './foo', name: 'Foo'},\n          {__symbolic: 'reference', module: './foo', name: 'Foo'},\n          {__symbolic: 'reference', module: './foo', name: 'Foo'}\n        ]\n      }]\n    });\n  });\n\n  describe('in strict mode', () => {\n    it('should throw if an error symbol is collecting a reference to a non-exported symbol', () => {\n      const source = program.getSourceFile('/local-symbol-ref.ts');\n      expect(() => collector.getMetadata(source, true)).toThrowError(/Reference to a local symbol/);\n    });\n\n    it('should throw if an error if collecting a reference to a non-exported function', () => {\n      const source = program.getSourceFile('/local-function-ref.ts');\n      expect(() => collector.getMetadata(source, true))\n          .toThrowError(/Reference to a non-exported function/);\n    });\n\n    it('should throw for references to unexpected types', () => {\n      const unsupported2 = program.getSourceFile('/unsupported-2.ts');\n      expect(() => collector.getMetadata(unsupported2, true))\n          .toThrowError(/Reference to non-exported class/);\n    });\n\n    it('should throw for errors in a static method', () => {\n      const unsupported3 = program.getSourceFile('/unsupported-3.ts');\n      expect(() => collector.getMetadata(unsupported3, true))\n          .toThrowError(/Reference to a non-exported class/);\n    });\n  });\n\n  describe('with invalid input', () => {\n    it('should not throw with a class with no name', () => {\n      const fileName = '/invalid-class.ts';\n      override(fileName, 'export class');\n      const invalidClass = program.getSourceFile(fileName);\n      expect(() => collector.getMetadata(invalidClass)).not.toThrow();\n    });\n\n    it('should not throw with a function with no name', () => {\n      const fileName = '/invalid-function.ts';\n      override(fileName, 'export function');\n      const invalidFunction = program.getSourceFile(fileName);\n      expect(() => collector.getMetadata(invalidFunction)).not.toThrow();\n    });\n  });\n\n  describe('inheritance', () => {\n    it('should record `extends` clauses for declared classes', () => {\n      const metadata = collector.getMetadata(program.getSourceFile('/class-inheritance.ts'));\n      expect(metadata.metadata['DeclaredChildClass'])\n          .toEqual({__symbolic: 'class', extends: {__symbolic: 'reference', name: 'ParentClass'}});\n    });\n\n    it('should record `extends` clauses for classes in the same file', () => {\n      const metadata = collector.getMetadata(program.getSourceFile('/class-inheritance.ts'));\n      expect(metadata.metadata['ChildClassSameFile'])\n          .toEqual({__symbolic: 'class', extends: {__symbolic: 'reference', name: 'ParentClass'}});\n    });\n\n    it('should record `extends` clauses for classes in a different file', () => {\n      const metadata = collector.getMetadata(program.getSourceFile('/class-inheritance.ts'));\n      expect(metadata.metadata['ChildClassOtherFile']).toEqual({\n        __symbolic: 'class',\n        extends: {\n          __symbolic: 'reference',\n          module: './class-inheritance-parent',\n          name: 'ParentClassFromOtherFile'\n        }\n      });\n    });\n\n    function expectClass(entry: MetadataEntry): entry is ClassMetadata {\n      const result = isClassMetadata(entry);\n      expect(result).toBeTruthy();\n      return result;\n    }\n\n    it('should collect the correct arity for a class', () => {\n      const metadata = collector.getMetadata(program.getSourceFile('/class-arity.ts'));\n\n      const zero = metadata.metadata['Zero'];\n      if (expectClass(zero)) expect(zero.arity).toBeUndefined();\n      const one = metadata.metadata['One'];\n      if (expectClass(one)) expect(one.arity).toBe(1);\n      const two = metadata.metadata['Two'];\n      if (expectClass(two)) expect(two.arity).toBe(2);\n      const three = metadata.metadata['Three'];\n      if (expectClass(three)) expect(three.arity).toBe(3);\n      const nine = metadata.metadata['Nine'];\n      if (expectClass(nine)) expect(nine.arity).toBe(9);\n    });\n  });\n\n  describe('regerssion', () => {\n    it('should be able to collect a short-hand property value', () => {\n      const source = createSource(`\n        const children = { f1: 1 };\n        export const r = [\n          {path: ':locale', children}\n        ];\n      `);\n      const metadata = collector.getMetadata(source);\n      expect(metadata.metadata).toEqual({r: [{path: ':locale', children: {f1: 1}}]});\n    });\n\n    // #17518\n    it('should skip a default function', () => {\n      const source = createSource(`\n        export default function () {\n\n          const mainRoutes = [\n            {name: 'a', abstract: true, component: 'main'},\n\n            {name: 'a.welcome', url: '/welcome', component: 'welcome'}\n          ];\n\n          return mainRoutes;\n\n        }`);\n      const metadata = collector.getMetadata(source);\n      expect(metadata).toBeUndefined();\n    });\n\n    it('should skip a named default export', () => {\n      const source = createSource(`\n        function mainRoutes() {\n\n          const mainRoutes = [\n            {name: 'a', abstract: true, component: 'main'},\n\n            {name: 'a.welcome', url: '/welcome', component: 'welcome'}\n          ];\n\n          return mainRoutes;\n\n        }\n\n        exports = foo;\n        `);\n      const metadata = collector.getMetadata(source);\n      expect(metadata).toBeUndefined();\n    });\n  });\n\n  function override(fileName: string, content: string) {\n    host.overrideFile(fileName, content);\n    host.addFile(fileName);\n    program = service.getProgram();\n  }\n});\n\n// TODO: Do not use \\` in a template literal as it confuses clang-format\nconst FILES: Directory = {\n  'app': {\n    'app.component.ts': `\n      import {Component as MyComponent, OnInit} from 'angular2/core';\n      import * as common from 'angular2/common';\n      import {Hero} from './hero';\n      import {HeroDetailComponent} from './hero-detail.component';\n      import HeroService from './hero.service';\n      // thrown away\n      import 'angular2/core';\n\n      @MyComponent({\n        selector: 'my-app',\n        template:` +\n        '`' +\n        `\n        <h2>My Heroes</h2>\n        <ul class=\"heroes\">\n          <li *ngFor=\"#hero of heroes\"\n            (click)=\"onSelect(hero)\"\n            [class.selected]=\"hero === selectedHero\">\n            <span class=\"badge\">{{hero.id | lowercase}}</span> {{hero.name | uppercase}}\n          </li>\n        </ul>\n        <my-hero-detail [hero]=\"selectedHero\"></my-hero-detail>\n        ` +\n        '`' +\n        `,\n        directives: [HeroDetailComponent, common.NgFor],\n        providers: [HeroService],\n        pipes: [common.LowerCasePipe, common.UpperCasePipe]\n      })\n      export class AppComponent implements OnInit {\n        public title = 'Tour of Heroes';\n        public heroes: Hero[];\n        public selectedHero: Hero;\n\n        constructor(private _heroService: HeroService) { }\n\n        onSelect(hero: Hero) { this.selectedHero = hero; }\n\n        ngOnInit() {\n            this.getHeroes()\n        }\n\n        getHeroes() {\n          this._heroService.getHeroesSlowly().then(heros => this.heroes = heros);\n        }\n      }`,\n    'hero.ts': `\n      export interface Hero {\n        id: number;\n        name: string;\n      }`,\n    'empty.ts': ``,\n    'hero-detail.component.ts': `\n      import {Component, Input} from 'angular2/core';\n      import {Hero} from './hero';\n\n      @Component({\n        selector: 'my-hero-detail',\n        template: ` +\n        '`' +\n        `\n        <div *ngIf=\"hero\">\n          <h2>{{hero.name}} details!</h2>\n          <div><label>id: </label>{{hero.id}}</div>\n          <div>\n            <label>name: </label>\n            <input [(ngModel)]=\"hero.name\" placeholder=\"name\"/>\n          </div>\n        </div>\n      ` +\n        '`' +\n        `,\n      })\n      export class HeroDetailComponent {\n        @Input() public hero: Hero;\n      }`,\n    'mock-heroes.ts': `\n      import {Hero as Hero} from './hero';\n\n      export const HEROES: Hero[] = [\n          {\"id\": 11, \"name\": \"Mr. Nice\"},\n          {\"id\": 12, \"name\": \"Narco\"},\n          {\"id\": 13, \"name\": \"Bombasto\"},\n          {\"id\": 14, \"name\": \"Celeritas\"},\n          {\"id\": 15, \"name\": \"Magneta\"},\n          {\"id\": 16, \"name\": \"RubberMan\"},\n          {\"id\": 17, \"name\": \"Dynama\"},\n          {\"id\": 18, \"name\": \"Dr IQ\"},\n          {\"id\": 19, \"name\": \"Magma\"},\n          {\"id\": 20, \"name\": \"Tornado\"}\n      ];`,\n    'default-exporter.ts': `\n      let a: string;\n      export default a;\n    `,\n    'hero.service.ts': `\n      import {Injectable} from 'angular2/core';\n      import {HEROES} from './mock-heroes';\n      import {Hero} from './hero';\n\n      @Injectable()\n      class HeroService {\n          getHeros() {\n              return Promise.resolve(HEROES);\n          }\n\n          getHeroesSlowly() {\n              return new Promise<Hero[]>(resolve =>\n                setTimeout(()=>resolve(HEROES), 2000)); // 2 seconds\n          }\n      }\n      export default HeroService;`,\n    'cases-data.ts': `\n      import {Injectable, Input} from 'angular2/core';\n\n      @Injectable()\n      export class CaseAny {\n        constructor(param: any) {}\n      }\n\n      @Injectable()\n      export class GetProp {\n        private _name: string;\n        @Input('firstName') get name(): string {\n          return this._name;\n        }\n      }\n\n      @Injectable()\n      export class SetProp {\n        private _name: string;\n        @Input('firstName') set name(value: string) {\n          this._name = value;\n        }\n      }\n\n      @Injectable()\n      export class FullProp {\n        private _name: string;\n        @Input('firstName') get name(): string {\n          return this._name;\n        }\n        set name(value: string) {\n          this._name = value;\n        }\n      }\n\n      export class ClassReference<T> { }\n      export class NgForRow {\n\n      }\n\n      @Injectable()\n      export class NgFor {\n        constructor (public ref: ClassReference<NgForRow>) {}\n      }\n     `,\n    'error-cases.ts': `\n      import HeroService from './hero.service';\n\n      export class CaseCtor {\n        constructor(private _heroService: HeroService) { }\n      }\n    `\n  },\n  'promise.ts': `\n    interface PromiseLike<T> {\n        then<TResult>(onfulfilled?: (value: T) => TResult | PromiseLike<TResult>, onrejected?: (reason: any) => TResult | PromiseLike<TResult>): PromiseLike<TResult>;\n        then<TResult>(onfulfilled?: (value: T) => TResult | PromiseLike<TResult>, onrejected?: (reason: any) => void): PromiseLike<TResult>;\n    }\n\n    interface Promise<T> {\n        then<TResult>(onfulfilled?: (value: T) => TResult | PromiseLike<TResult>, onrejected?: (reason: any) => TResult | PromiseLike<TResult>): Promise<TResult>;\n        then<TResult>(onfulfilled?: (value: T) => TResult | PromiseLike<TResult>, onrejected?: (reason: any) => void): Promise<TResult>;\n        catch(onrejected?: (reason: any) => T | PromiseLike<T>): Promise<T>;\n        catch(onrejected?: (reason: any) => void): Promise<T>;\n    }\n\n    interface PromiseConstructor {\n        prototype: Promise<any>;\n        new <T>(executor: (resolve: (value?: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void): Promise<T>;\n        reject(reason: any): Promise<void>;\n        reject<T>(reason: any): Promise<T>;\n        resolve<T>(value: T | PromiseLike<T>): Promise<T>;\n        resolve(): Promise<void>;\n    }\n\n    declare var Promise: PromiseConstructor;\n  `,\n  'class-arity.ts': `\n    export class Zero {}\n    export class One<T> {}\n    export class Two<T, V> {}\n    export class Three<T1, T2, T3> {}\n    export class Nine<T1, T2, T3, T4, T5, T6, T7, T8, T9> {}\n  `,\n  'unsupported-1.ts': `\n    export let {a, b} = {a: 1, b: 2};\n    export let [c, d] = [1, 2];\n    export let e;\n  `,\n  'unsupported-2.ts': `\n    import {Injectable} from 'angular2/core';\n\n    class Foo {}\n\n    @Injectable()\n    export class Bar {\n      constructor(private f: Foo) {}\n    }\n  `,\n  'unsupported-3.ts': `\n    class Foo {}\n\n    export class SomeClass {\n      static someStatic() {\n        return Foo;\n      }\n    }\n  `,\n  'interface-reference.ts': `\n    import {Injectable, Inject} from 'angular2/core';\n    export interface Test {}\n\n    @Injectable()\n    export class SomeClass {\n      constructor(@Inject(\"a\") test: Test) {}\n    }\n  `,\n  'import-star.ts': `\n    import {Injectable} from 'angular2/core';\n    import * as common from 'angular2/common';\n\n    @Injectable()\n    export class SomeClass {\n      constructor(private f: common.NgFor) {}\n    }\n  `,\n  'exported-classes.ts': `\n    export class SimpleClass {}\n    export abstract class AbstractClass {}\n    export declare class DeclaredClass {}\n  `,\n  'class-inheritance-parent.ts': `\n    export class ParentClassFromOtherFile {}\n  `,\n  'class-inheritance.ts': `\n    import {ParentClassFromOtherFile} from './class-inheritance-parent';\n\n    export class ParentClass {}\n\n    export declare class DeclaredChildClass extends ParentClass {}\n\n    export class ChildClassSameFile extends ParentClass {}\n\n    export class ChildClassOtherFile extends ParentClassFromOtherFile {}\n  `,\n  'exported-functions.ts': `\n    export function one(a: string, b: string, c: string) {\n      return {a: a, b: b, c: c};\n    }\n    export function two(a: string, b: string, c: string) {\n      return {a, b, c};\n    }\n    export function three({a, b, c}: {a: string, b: string, c: string}) {\n      return [a, b, c];\n    }\n    export function supportsState(): boolean {\n     return !!window.history.pushState;\n    }\n    export function complexFn(x: any): boolean {\n      if (x) {\n        return true;\n      } else {\n        return false;\n      }\n    }\n    export declare function declaredFn();\n  `,\n  'exported-enum.ts': `\n    import {constValue} from './exported-consts';\n\n    export const someValue = 30;\n    export enum SomeEnum { A, B, C = 100, D };\n    export enum ComplexEnum { A, B, C = someValue, D = someValue + 10, E = constValue };\n  `,\n  'exported-consts.ts': `\n    export const constValue = 100;\n  `,\n  'static-method.ts': `\n    export class MyModule {\n      static with(comp: any): any[] {\n        return [\n          MyModule,\n          { provider: 'a', useValue: comp }\n        ];\n      }\n    }\n  `,\n  'static-method-with-default.ts': `\n    export class MyModule {\n      static with(comp: any, foo: boolean = true, bar: boolean = false): any[] {\n        return [\n          MyModule,\n          foo ? { provider: 'a', useValue: comp } : {provider: 'b', useValue: comp},\n          bar ? { provider: 'c', useValue: comp } : {provider: 'd', useValue: comp}\n        ];\n      }\n    }\n  `,\n  'static-method-call.ts': `\n    import {Component} from 'angular2/core';\n    import {MyModule} from './static-method';\n\n    @Component({\n      providers: MyModule.with('a')\n    })\n    export class Foo { }\n  `,\n  'static-field.ts': `\n    export class MyModule {\n      static VALUE = 'Some string';\n    }\n  `,\n  'static-field-reference.ts': `\n    import {Component} from 'angular2/core';\n    import {MyModule} from './static-field';\n\n    @Component({\n      providers: [ { provide: 'a', useValue: MyModule.VALUE } ]\n    })\n    export class Foo { }\n  `,\n  'static-method-with-if.ts': `\n    export class MyModule {\n      static with(cond: boolean): any[] {\n        return [\n          MyModule,\n          { provider: 'a', useValue: cond ? '1' : '2' }\n        ];\n      }\n    }\n  `,\n  're-exports.ts': `\n    export {MyModule} from './static-field';\n    export {Foo as OtherModule} from './static-field-reference';\n    export * from 'angular2/core';\n  `,\n  're-exports-2.ts': `\n    import {MyModule} from './static-field';\n    import {Foo as OtherModule} from './static-field-reference';\n    class MyClass {}\n    export {OtherModule, MyModule as MyOtherModule, MyClass};\n  `,\n  'export-as.d.ts': `\n     declare function someFunction(): void;\n     export { someFunction as SomeFunction };\n `,\n  'local-symbol-ref.ts': `\n    import {Component, Validators} from 'angular2/core';\n\n    var REQUIRED;\n\n    export const REQUIRED_VALIDATOR: any = {\n      provide: 'SomeToken',\n      useValue: REQUIRED,\n      multi: true\n    };\n\n    @Component({\n      providers: [REQUIRED_VALIDATOR]\n    })\n    export class SomeComponent {}\n  `,\n  'private-enum.ts': `\n    export enum PublicEnum { a, b, c }\n    enum PrivateEnum { e, f, g }\n  `,\n  'local-function-ref.ts': `\n    import {Component, Validators} from 'angular2/core';\n\n    function required() {}\n\n    export const REQUIRED_VALIDATOR: any = {\n      provide: 'SomeToken',\n      useValue: required,\n      multi: true\n    };\n\n    @Component({\n      providers: [REQUIRED_VALIDATOR]\n    })\n    export class SomeComponent {}\n  `,\n  'local-symbol-ref-func.ts': `\n    var localSymbol: any[];\n\n    export function foo(index: number): string {\n      return localSymbol[index];\n    }\n  `,\n  'node_modules': {\n    'angular2': {\n      'core.d.ts': `\n          export interface Type extends Function { }\n          export interface TypeDecorator {\n              <T extends Type>(type: T): T;\n              (target: Object, propertyKey?: string | symbol, parameterIndex?: number): void;\n              annotations: any[];\n          }\n          export interface ComponentDecorator extends TypeDecorator { }\n          export interface ComponentFactory {\n              (obj: {\n                  selector?: string;\n                  inputs?: string[];\n                  outputs?: string[];\n                  properties?: string[];\n                  events?: string[];\n                  host?: {\n                      [key: string]: string;\n                  };\n                  bindings?: any[];\n                  providers?: any[];\n                  exportAs?: string;\n                  moduleId?: string;\n                  queries?: {\n                      [key: string]: any;\n                  };\n                  viewBindings?: any[];\n                  viewProviders?: any[];\n                  templateUrl?: string;\n                  template?: string;\n                  styleUrls?: string[];\n                  styles?: string[];\n                  directives?: Array<Type | any[]>;\n                  pipes?: Array<Type | any[]>;\n              }): ComponentDecorator;\n          }\n          export declare var Component: ComponentFactory;\n          export interface InputFactory {\n              (bindingPropertyName?: string): any;\n              new (bindingPropertyName?: string): any;\n          }\n          export declare var Input: InputFactory;\n          export interface InjectableFactory {\n              (): any;\n          }\n          export declare var Injectable: InjectableFactory;\n          export interface InjectFactory {\n            (binding?: any): any;\n            new (binding?: any): any;\n          }\n          export declare var Inject: InjectFactory;\n          export interface OnInit {\n              ngOnInit(): any;\n          }\n          export class Validators {\n            static required(): void;\n          }\n      `,\n      'common.d.ts': `\n        export declare class NgFor {\n            ngForOf: any;\n            ngForTemplate: any;\n            ngDoCheck(): void;\n        }\n        export declare class LowerCasePipe  {\n          transform(value: string, args?: any[]): string;\n        }\n        export declare class UpperCasePipe {\n            transform(value: string, args?: any[]): string;\n        }\n      `\n    }\n  }\n};\n\nfunction createSource(text: string): ts.SourceFile {\n  return ts.createSourceFile('', text, ts.ScriptTarget.Latest, true);\n}\n"]}