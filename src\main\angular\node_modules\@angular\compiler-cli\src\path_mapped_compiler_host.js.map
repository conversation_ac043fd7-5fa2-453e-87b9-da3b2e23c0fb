{"version": 3, "file": "path_mapped_compiler_host.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/path_mapped_compiler_host.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAKH,2BAA6B;AAC7B,+BAAiC;AAEjC,iDAAkE;AAElE,IAAM,GAAG,GAAG,kCAAkC,CAAC;AAC/C,IAAM,GAAG,GAAG,UAAU,CAAC;AAEvB;;;;;;GAMG;AACH;IAA4C,0CAAY;IACtD,gCAAY,OAAmB,EAAE,OAA+B,EAAE,OAA4B;eAC5F,kBAAM,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;IAClC,CAAC;IAED,qDAAoB,GAApB,UAAqB,QAAgB;QACnC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;YAAC,MAAM,CAAC,QAAQ,CAAC;QAC/B,yDAAyD;QACzD,GAAG,CAAC,CAAc,UAA2B,EAA3B,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAA3B,cAA2B,EAA3B,IAA2B;YAAxC,IAAM,GAAG,SAAA;YACZ,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC5C,CAAC;SACF;QACD,MAAM,CAAC,QAAQ,CAAC;IAClB,CAAC;IAED,qDAAoB,GAApB,UAAqB,CAAS,EAAE,cAAsB;QACpD,EAAE,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;YACD,iEAAiE;YACjE,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;QACnF,CAAC;QACD,GAAG,CAAC,CAAe,UAA6B,EAA7B,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC,EAA7B,cAA6B,EAA7B,IAA6B;YAA3C,IAAM,IAAI,SAAA;YACb,IAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC7D,IAAM,QAAQ,GACV,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,oBAAoB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;YAC7F,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACb,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;oBACjC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAC/E,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC9D,CAAC;SACF;QACD,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,qDAAoB,GAApB,UAAqB,YAAoB,EAAE,cAAsB;QAAjE,iBA6CC;QA5CC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;YACjC,OAAO,CAAC,KAAK,CACT,mCAAmC,EAAE,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAC5F,CAAC;QAED,+EAA+E;QAC/E,uDAAuD;QACvD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;YACnF,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,IAAM,UAAU,GAAG,UAAC,SAAiB;YACnC,IAAM,QAAQ,GAAG,KAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACpE,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC;QAEF,IAAM,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACvD,IAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,CAAC;QAChE,IAAI,mBAAmB,GAAW,SAAW,CAAC;QAC9C,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YACvD,IAAI,WAAS,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChE,EAAE,CAAC,CAAC,UAAU,CAAC,WAAS,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,CAAC,WAAS,CAAC;YACnB,CAAC;YACD,WAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,WAAS,CAAC;YACvC,EAAE,CAAC,CAAC,UAAU,CAAC,WAAS,CAAC,CAAC,CAAC,CAAC;gBAC1B,mBAAmB,GAAG,WAAS,CAAC;YAClC,CAAC;QACH,CAAC;QAED,EAAE,CAAC,CAAC,mBAAmB,CAAC;YAAC,MAAM,CAAC,mBAAmB,CAAC;QAEpD,wBAAwB;QACxB,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAChF,EAAE,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,IAAI,KAAK,CACX,8CAA4C,YAAY,qBAAgB,cAAgB,CAAC,CAAC;IAChG,CAAC;IAED,+CAAc,GAAd,UAAe,QAAgB;QAC7B,GAAG,CAAC,CAAe,UAA2B,EAA3B,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAA3B,cAA2B,EAA3B,IAA2B;YAAzC,IAAM,IAAI,SAAA;YACb,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC7C,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACzC,0EAA0E;gBAC1E,4EAA4E;gBAC5E,oDAAoD;gBACpD,QAAQ,CAAC;YACX,CAAC;YACD,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;gBAC/D,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAC1C,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBACxD,MAAM,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YACpC,CAAC;SACF;QACD,MAAM,CAAC,IAAM,CAAC;IAChB,CAAC;IACH,6BAAC;AAAD,CAAC,AAlHD,CAA4C,4BAAY,GAkHvD;AAlHY,wDAAsB", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {StaticSymbol} from '@angular/compiler';\nimport {AngularCompilerOptions, ModuleMetadata} from '@angular/tsc-wrapped';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport {CompilerHost, CompilerHostContext} from './compiler_host';\n\nconst EXT = /(\\.ts|\\.d\\.ts|\\.js|\\.jsx|\\.tsx)$/;\nconst DTS = /\\.d\\.ts$/;\n\n/**\n * This version of the AotCompilerHost expects that the program will be compiled\n * and executed with a \"path mapped\" directory structure, where generated files\n * are in a parallel tree with the sources, and imported using a `./` relative\n * import. This requires using TS `rootDirs` option and also teaching the module\n * loader what to do.\n */\nexport class PathMappedCompilerHost extends CompilerHost {\n  constructor(program: ts.Program, options: AngularCompilerOptions, context: CompilerHostContext) {\n    super(program, options, context);\n  }\n\n  getCanonicalFileName(fileName: string): string {\n    if (!fileName) return fileName;\n    // NB: the rootDirs should have been sorted longest-first\n    for (const dir of this.options.rootDirs || []) {\n      if (fileName.indexOf(dir) === 0) {\n        fileName = fileName.substring(dir.length);\n      }\n    }\n    return fileName;\n  }\n\n  moduleNameToFileName(m: string, containingFile: string): string|null {\n    if (!containingFile || !containingFile.length) {\n      if (m.indexOf('.') === 0) {\n        throw new Error('Resolution of relative paths requires a containing file.');\n      }\n      // Any containing file gives the same result for absolute imports\n      containingFile = this.getCanonicalFileName(path.join(this.basePath, 'index.ts'));\n    }\n    for (const root of this.options.rootDirs || ['']) {\n      const rootedContainingFile = path.join(root, containingFile);\n      const resolved =\n          ts.resolveModuleName(m, rootedContainingFile, this.options, this.context).resolvedModule;\n      if (resolved) {\n        if (this.options.traceResolution) {\n          console.error('resolve', m, containingFile, '=>', resolved.resolvedFileName);\n        }\n        return this.getCanonicalFileName(resolved.resolvedFileName);\n      }\n    }\n    return null;\n  }\n\n  /**\n   * We want a moduleId that will appear in import statements in the generated code.\n   * These need to be in a form that system.js can load, so absolute file paths don't work.\n   * Relativize the paths by checking candidate prefixes of the absolute path, to see if\n   * they are resolvable by the moduleResolution strategy from the CompilerHost.\n   */\n  fileNameToModuleName(importedFile: string, containingFile: string): string {\n    if (this.options.traceResolution) {\n      console.error(\n          'getImportPath from containingFile', containingFile, 'to importedFile', importedFile);\n    }\n\n    // If a file does not yet exist (because we compile it later), we still need to\n    // assume it exists so that the `resolve` method works!\n    if (!this.context.fileExists(importedFile)) {\n      if (this.options.rootDirs && this.options.rootDirs.length > 0) {\n        this.context.assumeFileExists(path.join(this.options.rootDirs[0], importedFile));\n      } else {\n        this.context.assumeFileExists(importedFile);\n      }\n    }\n\n    const resolvable = (candidate: string) => {\n      const resolved = this.moduleNameToFileName(candidate, importedFile);\n      return resolved && resolved.replace(EXT, '') === importedFile.replace(EXT, '');\n    };\n\n    const importModuleName = importedFile.replace(EXT, '');\n    const parts = importModuleName.split(path.sep).filter(p => !!p);\n    let foundRelativeImport: string = undefined !;\n    for (let index = parts.length - 1; index >= 0; index--) {\n      let candidate = parts.slice(index, parts.length).join(path.sep);\n      if (resolvable(candidate)) {\n        return candidate;\n      }\n      candidate = '.' + path.sep + candidate;\n      if (resolvable(candidate)) {\n        foundRelativeImport = candidate;\n      }\n    }\n\n    if (foundRelativeImport) return foundRelativeImport;\n\n    // Try a relative import\n    const candidate = path.relative(path.dirname(containingFile), importModuleName);\n    if (resolvable(candidate)) {\n      return candidate;\n    }\n\n    throw new Error(\n        `Unable to find any resolvable import for ${importedFile} relative to ${containingFile}`);\n  }\n\n  getMetadataFor(filePath: string): ModuleMetadata[] {\n    for (const root of this.options.rootDirs || []) {\n      const rootedPath = path.join(root, filePath);\n      if (!this.context.fileExists(rootedPath)) {\n        // If the file doesn't exists then we cannot return metadata for the file.\n        // This will occur if the user refernced a declared module for which no file\n        // exists for the module (i.e. jQuery or angularjs).\n        continue;\n      }\n      if (DTS.test(rootedPath)) {\n        const metadataPath = rootedPath.replace(DTS, '.metadata.json');\n        if (this.context.fileExists(metadataPath)) {\n          return this.readMetadata(metadataPath, rootedPath);\n        }\n      } else {\n        const sf = this.getSourceFile(rootedPath);\n        sf.fileName = sf.fileName;\n        const metadata = this.metadataCollector.getMetadata(sf);\n        return metadata ? [metadata] : [];\n      }\n    }\n    return null !;\n  }\n}\n"]}