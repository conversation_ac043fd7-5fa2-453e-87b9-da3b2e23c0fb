[{"__symbolic": "module", "version": 3, "metadata": {"TranslateHttpLoader": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/http", "name": "Http"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "getTranslation": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslateHttpLoader": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/http", "name": "Http"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "getTranslation": [{"__symbolic": "method"}]}}}}]