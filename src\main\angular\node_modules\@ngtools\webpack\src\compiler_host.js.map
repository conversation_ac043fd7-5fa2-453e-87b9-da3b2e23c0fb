{"version": 3, "file": "compiler_host.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/compiler_host.ts"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC,+BAA6C;AAS7C,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;AAG9C;IAWE,YAAsB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAVzB,WAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACpB,WAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACpB,WAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACpB,WAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACpB,SAAI,GAAG,GAAG,CAAC;QACX,SAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;QAC1C,UAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAE,oBAAoB;QACjD,SAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,SAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAEH,CAAC;IAEvC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1B,WAAW,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B,aAAa,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACjC,iBAAiB,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACrC,cAAc,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1B,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAE5B,IAAI,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACjC,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,0BAA0B;IACrD,IAAI,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,OAAO,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;CACxC;AAnCD,oCAmCC;AAED,qBAA6B,SAAQ,YAAY;IAC/C,YAAY,SAAiB;QAC3B,KAAK,CAAC,SAAS,CAAC,CAAC;IACnB,CAAC;IAED,WAAW,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAE9B,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CAC5B;AARD,0CAQC;AAED,sBAA8B,SAAQ,YAAY;IAEhD,YAAY,SAAiB,EAAU,QAAgB;QACrD,KAAK,CAAC,SAAS,CAAC,CAAC;QADoB,aAAQ,GAAR,QAAQ,CAAQ;IAEvD,CAAC;IAED,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,CAAS;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IACD,aAAa,CAAC,UAAyB;QACrC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IACD,aAAa,CAAC,eAAgC,EAAE,cAAuB;QACrE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,gBAAgB,CACpC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,eAAe,EACf,cAAc,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzB,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;CAC5C;AA9BD,4CA8BC;AAGD;IAaE,YAAoB,QAA4B,EAAE,QAAgB;QAA9C,aAAQ,GAAR,QAAQ,CAAoB;QAXxC,WAAM,GAAuC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjE,iBAAY,GAAsC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEtE,kBAAa,GAA8B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/D,iBAAY,GAA8B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAK9D,WAAM,GAAG,KAAK,CAAC;QAGrB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAEO,QAAQ,CAAC,IAAY;QAC3B,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACjC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,OAAe;QACvD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,CAAC,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAC5B,CAAC,GAAG,cAAO,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,IAAI,KAAK;QACP,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,aAAa;QACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAED,uBAAuB,CAAC,QAAa;QACnC,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC;QAC/B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAChB,MAAM,CAAC;QACT,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrD,GAAG,CAAC,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACV,2EAA2E;gBAC3E,MAAM,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;gBAClE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3C,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,kCAAkC;gBAClC,MAAM,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;gBAClE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;gBACjD,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QACD,GAAG,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrC,2EAA2E;YAC3E,MAAM,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC;YAChE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3C,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,uBAAuB;QACrB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IACD,mBAAmB;QACjB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzC,CAAC;IAED,UAAU,CAAC,QAAgB;QACzB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QACtC,CAAC;IACH,CAAC;IAED,UAAU,CAAC,QAAgB;QACzB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED,QAAQ,CAAC,QAAgB;QACvB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjD,EAAE,CAAC,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC;YAChB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;IACvC,CAAC;IAED,eAAe,CAAC,aAAqB;QACnC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC7C,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;eAC1C,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;aAC5B,MAAM,CAAC,QAAQ,IAAI,cAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;aAC7C,GAAG,CAAC,IAAI,IAAI,eAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;aAC3C,MAAM,CAAC,QAAQ,IAAI,cAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;aAC7C,GAAG,CAAC,IAAI,IAAI,eAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/B,IAAI,SAAmB,CAAC;QACxB,IAAI,CAAC;YACH,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,SAAS,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,aAAa,CAAC,QAAgB,EAAE,eAAgC,EAAE,QAAoB;QACpF,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEnC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjB,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACpF,CAAC;IAED,oBAAoB;QAClB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;IAC/C,CAAC;IAED,qBAAqB,CAAC,OAA2B;QAC/C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,6FAA6F;IAC7F,uBAAuB;IACvB,IAAI,SAAS;QACX,MAAM,CAAC,CAAC,QAAgB,EAAE,IAAY,EAAE,mBAA4B,EAC5D,QAAoC,EAAE,YAA8B;YAC1E,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IAED,mBAAmB;QACjB,MAAM,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;IACzF,CAAC;IAED,oBAAoB,CAAC,QAAgB;QACnC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED,yBAAyB;QACvB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;IACpD,CAAC;IAED,UAAU;QACR,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;IACrC,CAAC;CACF;AArMD,kDAqMC"}