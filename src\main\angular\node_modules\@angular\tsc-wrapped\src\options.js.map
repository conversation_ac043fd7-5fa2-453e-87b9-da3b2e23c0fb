{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/options.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\ninterface Options extends ts.CompilerOptions {\n  // Absolute path to a directory where generated file structure is written.\n  // If unspecified, generated files will be written alongside sources.\n  genDir?: string;\n\n  // Path to the directory containing the tsconfig.json file.\n  basePath?: string;\n\n  // Don't produce .metadata.json files (they don't work for bundled emit with --out)\n  skipMetadataEmit?: boolean;\n\n  // Produce an error if the metadata written for a class would produce an error if used.\n  strictMetadataEmit?: boolean;\n\n  // Don't produce .ngfactory.ts or .ngstyle.ts files\n  skipTemplateCodegen?: boolean;\n\n  // Whether to generate a flat module index of the given name and the corresponding\n  // flat module metadata. This option is intended to be used when creating flat\n  // modules similar to how `@angular/core` and `@angular/common` are packaged.\n  // When this option is used the `package.json` for the library should refered to the\n  // generated flat module index instead of the library index file. When using this\n  // option only one .metadata.json file is produced that contains all the metadata\n  // necessary for symbols exported from the library index.\n  // In the generated .ngfactory.ts files flat module index is used to import symbols\n  // includes both the public API from the library index as well as shrowded internal\n  // symbols.\n  // By default the .ts file supplied in the `files` files field is assumed to be\n  // library index. If more than one is specified, uses `libraryIndex` to select the\n  // file to use. If more than on .ts file is supplied and no `libraryIndex` is supplied\n  // an error is produced.\n  // A flat module index .d.ts and .js will be created with the given `flatModuleOutFile`\n  // name in the same location as the library index .d.ts file is emitted.\n  // For example, if a library uses `public_api.ts` file as the library index of the\n  // module the `tsconfig.json` `files` field would be `[\"public_api.ts\"]`. The\n  // `flatModuleOutFile` options could then be set to, for example `\"index.js\"`, which\n  // produces `index.d.ts` and  `index.metadata.json` files. The library's\n  // `package.json`'s `module` field would be `\"index.js\"` and the `typings` field would\n  // be `\"index.d.ts\"`.\n  flatModuleOutFile?: string;\n\n  // Preferred module id to use for importing flat module. References generated by `ngc`\n  // will use this module name when importing symbols from the flat module. This is only\n  // meaningful when `flatModuleOutFile` is also supplied. It is otherwise ignored.\n  flatModuleId?: string;\n\n  // Whether to generate code for library code.\n  // If true, produce .ngfactory.ts and .ngstyle.ts files for .d.ts inputs.\n  // Default is true.\n  generateCodeForLibraries?: boolean;\n\n  // Insert JSDoc type annotations needed by Closure Compiler\n  annotateForClosureCompiler?: boolean;\n\n  // Modify how angular annotations are emitted to improve tree-shaking.\n  // Default is static fields.\n  // decorators: Leave the Decorators in-place. This makes compilation faster.\n  //             TypeScript will emit calls to the __decorate helper.\n  //             `--emitDecoratorMetadata` can be used for runtime reflection.\n  //             However, the resulting code will not properly tree-shake.\n  // static fields: Replace decorators with a static field in the class.\n  //                Allows advanced tree-shakers like Closure Compiler to remove\n  //                unused classes.\n  annotationsAs?: 'decorators'|'static fields';\n\n  // Print extra information while running the compiler\n  trace?: boolean;\n\n  /** @deprecated since v4 this option has no effect anymore. */\n  debug?: boolean;\n\n  // Whether to enable support for <template> and the template attribute (true by default)\n  enableLegacyTemplate?: boolean;\n\n  // Whether to generate .ngsummary.ts files that allow to use AOTed artifacts\n  // in JIT mode. This is off by default.\n  enableSummariesForJit?: boolean;\n\n  // Whether to compile generated .ngfacgtory.ts files, even when they are no\n  // matched by the `files` / `includes` in the `tsconfig.json`.\n  // This is off by default.\n  alwaysCompileGeneratedCode?: boolean;\n}\n\nexport default Options;\n"]}