{"version": 3, "file": "browser.js", "sources": ["../../../../../packages/animations/browser/index.ts", "../../../../../packages/animations/browser/public_api.ts", "../../../../../packages/animations/browser/src/browser.ts", "../../../../../packages/animations/browser/src/private_export.ts", "../../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../../packages/animations/browser/src/dsl/animation_dsl_visitor.ts", "../../../../../packages/animations/browser/src/dsl/animation_ast.ts", "../../../../../packages/animations/browser/src/util.ts", "../../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../../packages/animations/browser/src/render/shared.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {AnimationDriver,ɵAnimation,ɵAnimationStyleNormalizer,ɵNoopAnimationStyleNormalizer,ɵWebAnimationsStyleNormalizer,ɵNoopAnimationDriver,ɵAnimationEngine,ɵWebAnimationsDriver,ɵsupportsWebAnimations,ɵWebAnimationsPlayer} from './public_api';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the animation package.\n */\nexport {AnimationDriver,ɵAnimation,ɵAnimationStyleNormalizer,ɵNoopAnimationStyleNormalizer,ɵWebAnimationsStyleNormalizer,ɵNoopAnimationDriver,ɵAnimationEngine,ɵWebAnimationsDriver,ɵsupportsWebAnimations,ɵWebAnimationsPlayer} from './src/browser';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {AnimationDriver} from './render/animation_driver';\nexport {ɵAnimation,ɵAnimationStyleNormalizer,ɵNoopAnimationStyleNormalizer,ɵWebAnimationsStyleNormalizer,ɵNoopAnimationDriver,ɵAnimationEngine,ɵWebAnimationsDriver,ɵsupportsWebAnimations,ɵWebAnimationsPlayer} from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {Animation as ɵAnimation} from './dsl/animation';\nexport {AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer} from './dsl/style_normalization/animation_style_normalizer';\nexport {WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer} from './dsl/style_normalization/web_animations_style_normalizer';\nexport {NoopAnimationDriver as ɵNoopAnimationDriver} from './render/animation_driver';\nexport {AnimationEngine as ɵAnimationEngine} from './render/animation_engine_next';\nexport {WebAnimationsDriver as ɵWebAnimationsDriver, supportsWebAnimations as ɵsupportsWebAnimations} from './render/web_animations/web_animations_driver';\nexport {WebAnimationsPlayer as ɵWebAnimationsPlayer} from './render/web_animations/web_animations_player';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../shared';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\nexport class WebAnimationsDriver implements AnimationDriver {\n/**\n * @param {?} element\n * @param {?} selector\n * @return {?}\n */\nmatchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n/**\n * @param {?} elm1\n * @param {?} elm2\n * @return {?}\n */\ncontainsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n/**\n * @param {?} element\n * @param {?} selector\n * @param {?} multi\n * @return {?}\n */\nquery(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n/**\n * @param {?} element\n * @param {?} prop\n * @param {?=} defaultValue\n * @return {?}\n */\ncomputeStyle(element: any, prop: string, defaultValue?: string): string {\n    return /** @type {?} */(( ( /** @type {?} */((window.getComputedStyle(element) as any)))[prop] as string));\n  }\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @param {?=} previousPlayers\n * @return {?}\n */\nanimate(\n      element: any, keyframes: ɵStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = []): WebAnimationsPlayer {\n    const /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n    const /** @type {?} */ playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */(( <WebAnimationsPlayer[]>previousPlayers.filter(\n        player => { return player instanceof WebAnimationsPlayer; })));\n    return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n  }\n}\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n  return typeof Element !== 'undefined' && typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {copyStyles, eraseStyles, setStyles} from '../../util';\n\nimport {DOMAnimation} from './dom_animation';\nexport class WebAnimationsPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _player: DOMAnimation;\nprivate _duration: number;\nprivate _delay: number;\nprivate _initialized = false;\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finalKeyframe: {[key: string]: string | number};\npublic time = 0;\npublic parentPlayer: AnimationPlayer|null = null;\npublic previousStyles: {[styleName: string]: string | number};\npublic currentSnapshot: {[styleName: string]: string | number} = {};\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @param {?=} previousPlayers\n */\nconstructor(\npublic element: any,\npublic keyframes: {[key: string]: string | number}[],\npublic options: {[key: string]: string | number},\nprivate previousPlayers: WebAnimationsPlayer[] = []) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n\n    this.previousStyles = {};\n    previousPlayers.forEach(player => {\n      let styles = player.currentSnapshot;\n      Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n    });\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n/**\n * @return {?}\n */\nprivate _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const /** @type {?} */ keyframes = this.keyframes.map(styles => copyStyles(styles, false));\n    const /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n    if (previousStyleProps.length) {\n      let /** @type {?} */ startingKeyframe = keyframes[0];\n      let /** @type {?} */ missingStyleProps: string[] = [];\n      previousStyleProps.forEach(prop => {\n        if (!startingKeyframe.hasOwnProperty(prop)) {\n          missingStyleProps.push(prop);\n        }\n        startingKeyframe[prop] = this.previousStyles[prop];\n      });\n\n      if (missingStyleProps.length) {\n        const /** @type {?} */ self = this;\n        // tslint:disable-next-line\n        for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n          let /** @type {?} */ kf = keyframes[i];\n          missingStyleProps.forEach(function(prop) {\n            kf[prop] = _computeStyle(self.element, prop);\n          });\n        }\n      }\n    }\n\n    this._player = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n    this._player.addEventListener('finish', () => this._onFinish());\n  }\n/**\n * @return {?}\n */\nprivate _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this._player.pause();\n    }\n  }\n/**\n * \\@internal\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @return {?}\n */\n_triggerWebAnimation(element: any, keyframes: any[], options: any): DOMAnimation {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return /** @type {?} */(( element['animate'](keyframes, options) as DOMAnimation));\n  }\n/**\n * @return {?}\n */\nget domPlayer() { return this._player; }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nplay(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n    this._player.play();\n  }\n/**\n * @return {?}\n */\npause(): void {\n    this.init();\n    this._player.pause();\n  }\n/**\n * @return {?}\n */\nfinish(): void {\n    this.init();\n    this._onFinish();\n    this._player.finish();\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @return {?}\n */\nprivate _resetDomPlayerState() {\n    if (this._player) {\n      this._player.cancel();\n    }\n  }\n/**\n * @return {?}\n */\nrestart(): void {\n    this.reset();\n    this.play();\n  }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._resetDomPlayerState();\n      this._onFinish();\n      this._destroyed = true;\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._player.currentTime = p * this.time; }\n/**\n * @return {?}\n */\ngetPosition(): number { return this._player.currentTime / this.time; }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._delay + this._duration; }\n/**\n * @return {?}\n */\nbeforeDestroy() {\n    const /** @type {?} */ styles: {[key: string]: string | number} = {};\n    if (this.hasStarted()) {\n      Object.keys(this._finalKeyframe).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] =\n              this._finished ? this._finalKeyframe[prop] : _computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n}\n\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDoneFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onStartFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDestroyFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._player;\n/** @type {?} */\nWebAnimationsPlayer.prototype._duration;\n/** @type {?} */\nWebAnimationsPlayer.prototype._delay;\n/** @type {?} */\nWebAnimationsPlayer.prototype._initialized;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finished;\n/** @type {?} */\nWebAnimationsPlayer.prototype._started;\n/** @type {?} */\nWebAnimationsPlayer.prototype._destroyed;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finalKeyframe;\n/** @type {?} */\nWebAnimationsPlayer.prototype.time;\n/** @type {?} */\nWebAnimationsPlayer.prototype.parentPlayer;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousStyles;\n/** @type {?} */\nWebAnimationsPlayer.prototype.currentSnapshot;\n/** @type {?} */\nWebAnimationsPlayer.prototype.element;\n/** @type {?} */\nWebAnimationsPlayer.prototype.keyframes;\n/** @type {?} */\nWebAnimationsPlayer.prototype.options;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousPlayers;\n}\n\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element: any, prop: string): string {\n  return ( /** @type {?} */((<any>window.getComputedStyle(element))))[prop];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\nexport class AnimationEngine {\nprivate _transitionEngine: TransitionAnimationEngine;\nprivate _timelineEngine: TimelineAnimationEngine;\nprivate _triggerCache: {[key: string]: AnimationTrigger} = {};\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    this._transitionEngine = new TransitionAnimationEngine(driver, normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(driver, normalizer);\n\n    this._transitionEngine.onRemovalComplete =\n        (element: any, context: any) => { this.onRemovalComplete(element, context); }\n  }\n/**\n * @param {?} componentId\n * @param {?} namespaceId\n * @param {?} hostElement\n * @param {?} name\n * @param {?} metadata\n * @return {?}\n */\nregisterTrigger(\n      componentId: string, namespaceId: string, hostElement: any, name: string,\n      metadata: AnimationTriggerMetadata): void {\n    const /** @type {?} */ cacheKey = componentId + '-' + name;\n    let /** @type {?} */ trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const /** @type {?} */ errors: any[] = [];\n      const /** @type {?} */ ast = /** @type {?} */(( buildAnimationAst( /** @type {?} */((metadata as AnimationMetadata)), errors) as TriggerAst));\n      if (errors.length) {\n        throw new Error(\n            `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n      }\n      trigger = buildTrigger(name, ast);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\nonInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\nonRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} property\n * @param {?} value\n * @return {?}\n */\nsetProperty(namespaceId: string, element: any, property: string, value: any): boolean {\n    // @@property\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const /** @type {?} */ args = /** @type {?} */(( value as any[]));\n      this._timelineEngine.command(id, element, action, args);\n      return false;\n    }\n    return this._transitionEngine.trigger(namespaceId, element, property, value);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} eventName\n * @param {?} eventPhase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, eventName: string, eventPhase: string,\n      callback: (event: any) => any): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1): void { this._transitionEngine.flush(microtaskId); }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] {\n    return ( /** @type {?} */((this._transitionEngine.players as AnimationPlayer[])))\n        .concat( /** @type {?} */((this._timelineEngine.players as AnimationPlayer[])));\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this._transitionEngine.whenRenderingDone(); }\n}\n\nfunction AnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationEngine.prototype._transitionEngine;\n/** @type {?} */\nAnimationEngine.prototype._timelineEngine;\n/** @type {?} */\nAnimationEngine.prototype._triggerCache;\n/** @type {?} */\nAnimationEngine.prototype.onRemovalComplete;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_PLAYER_ARRAY: AnimationPlayer[] = [];\nconst /** @type {?} */ NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst /** @type {?} */ NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\nexport interface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nexport const /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n\nexport interface ElementAnimationState {\n  setForRemoval: any;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n}\nexport class StateValue {\npublic value: string;\npublic options: AnimationOptions;\n/**\n * @param {?} input\n */\nconstructor(input: any) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      const options = copyObj(input as any);\n      delete options['value'];\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n/**\n * @param {?} options\n * @return {?}\n */\nabsorbOptions(options: AnimationOptions) {\n    const /** @type {?} */ newParams = options.params;\n    if (newParams) {\n      const /** @type {?} */ oldParams = /** @type {?} */(( this.options.params));\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nfunction StateValue_tsickle_Closure_declarations() {\n/** @type {?} */\nStateValue.prototype.value;\n/** @type {?} */\nStateValue.prototype.options;\n}\n\n\nexport const /** @type {?} */ VOID_VALUE = 'void';\nexport const /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport const /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nexport class AnimationTransitionNamespace {\npublic players: TransitionAnimationPlayer[] = [];\nprivate _triggers: {[triggerName: string]: AnimationTrigger} = {};\nprivate _queue: QueueInstruction[] = [];\nprivate _elementListeners = new Map<any, TriggerListener[]>();\nprivate _hostClassName: string;\n/**\n * @param {?} id\n * @param {?} hostElement\n * @param {?} _engine\n */\nconstructor(\npublic id: string,\npublic hostElement: any,\nprivate _engine: TransitionAnimationEngine) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.hasOwnProperty(name)) {\n      throw new Error(\n          `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw new Error(\n          `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw new Error(\n          `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n    }\n\n    const /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n    const /** @type {?} */ data = {name, phase, callback};\n    listeners.push(data);\n\n    const /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n    if (!triggersWithStates.hasOwnProperty(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates[name] = null;\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const /** @type {?} */ index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers[name]) {\n          delete triggersWithStates[name];\n        }\n      });\n    };\n  }\n/**\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nregister(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers[name]) {\n      // throw\n      return false;\n    } else {\n      this._triggers[name] = ast;\n      return true;\n    }\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate _getTrigger(name: string) {\n    const /** @type {?} */ trigger = this._triggers[name];\n    if (!trigger) {\n      throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n    }\n    return trigger;\n  }\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} value\n * @param {?=} defaultToFallback\n * @return {?}\n */\ntrigger(element: any, triggerName: string, value: any, defaultToFallback: boolean = true):\n      TransitionAnimationPlayer|undefined {\n    const /** @type {?} */ trigger = this._getTrigger(triggerName);\n    const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = {});\n    }\n\n    let /** @type {?} */ fromState = triggersWithStates[triggerName];\n    const /** @type {?} */ toState = new StateValue(value);\n\n    const /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates[triggerName] = toState;\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    } else if (fromState === DELETED_STATE_VALUE) {\n      return player;\n    }\n\n    const /** @type {?} */ playersOnElement: TransitionAnimationPlayer[] =\n        getOrSetAsInMap(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new palyer)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n    let /** @type {?} */ isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push(\n        {element, triggerName, transition, fromState, toState, player, isFallbackTransition});\n\n    if (!isFallbackTransition) {\n      addClass(element, NG_ANIMATING_CLASSNAME);\n    }\n\n    player.onDone(() => {\n      removeClass(element, NG_ANIMATING_CLASSNAME);\n\n      let /** @type {?} */ index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const /** @type {?} */ players = this._engine.playersByElement.get(element);\n      if (players) {\n        let /** @type {?} */ index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nderegister(name: string) {\n    delete this._triggers[name];\n\n    this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n          element, listeners.filter(entry => { return entry.name != name; }));\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n/**\n * @param {?} rootElement\n * @param {?} context\n * @param {?=} animate\n * @return {?}\n */\nprivate _destroyInnerNodes(rootElement: any, context: any, animate: boolean = false) {\n    this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n      if (animate && containsClass(elm, this._hostClassName)) {\n        const /** @type {?} */ innerNs = this._engine.namespacesByHostElement.get(elm);\n\n        // special case for a host element with animations on the same element\n        if (innerNs) {\n          innerNs.removeNode(elm, context, true);\n        }\n\n        this.removeNode(elm, context, true);\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n  }\n/**\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(element: any, context: any, doNotRecurse?: boolean): void {\n    const /** @type {?} */ engine = this._engine;\n\n    if (!doNotRecurse && element.childElementCount) {\n      this._destroyInnerNodes(element, context, true);\n    }\n\n    const /** @type {?} */ triggerStates = engine.statesByElement.get(element);\n    if (triggerStates) {\n      const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n      Object.keys(triggerStates).forEach(triggerName => {\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers[triggerName]) {\n          const /** @type {?} */ player = this.trigger(element, triggerName, VOID_VALUE, false);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        engine.markElementAsRemoved(this.id, element, true, context);\n        optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n        return;\n      }\n    }\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let /** @type {?} */ containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const /** @type {?} */ currentPlayers =\n          engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue fowards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let /** @type {?} */ parent = element;\n        while (parent = parent.parentNode) {\n          const /** @type {?} */ triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    const /** @type {?} */ listeners = this._elementListeners.get(element);\n    if (listeners) {\n      const /** @type {?} */ visitedTriggers = new Set<string>();\n      listeners.forEach(listener => {\n        const /** @type {?} */ triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const /** @type {?} */ trigger = this._triggers[triggerName];\n        const /** @type {?} */ transition = trigger.fallbackTransition;\n        const /** @type {?} */ elementStates = /** @type {?} */(( engine.statesByElement.get(element)));\n        const /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n        const /** @type {?} */ toState = new StateValue(VOID_VALUE);\n        const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      // we do this after the flush has occurred such\n      // that the callbacks can be fired\n      engine.afterFlush(() => this.clearElementCache(element));\n      engine.destroyInnerAnimations(element);\n      engine._onRemovalComplete(element, context);\n    }\n  }\n/**\n * @param {?} element\n * @param {?} parent\n * @return {?}\n */\ninsertNode(element: any, parent: any): void { addClass(element, this._hostClassName); }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\ndrainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const /** @type {?} */ instructions: QueueInstruction[] = [];\n    this._queue.forEach(entry => {\n      const /** @type {?} */ player = entry.player;\n      if (player.destroyed) return;\n\n      const /** @type {?} */ element = entry.element;\n      const /** @type {?} */ listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const /** @type {?} */ baseEvent = makeAnimationEvent(\n                element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            ( /** @type {?} */((baseEvent as any)))['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const /** @type {?} */ d0 = a.transition.ast.depCount;\n      const /** @type {?} */ d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n/**\n * @param {?} context\n * @return {?}\n */\ndestroy(context: any) {\n    this.players.forEach(p => p.destroy());\n    this._destroyInnerNodes(this.hostElement, context);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nelementContainsData(element: any): boolean {\n    let /** @type {?} */ containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData =\n        (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n}\n\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionNamespace.prototype.players;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._triggers;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._queue;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._elementListeners;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._hostClassName;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.id;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.hostElement;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._engine;\n}\n\n\nexport interface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\nexport class TransitionAnimationEngine {\npublic players: TransitionAnimationPlayer[] = [];\npublic newHostElements = new Map<any, AnimationTransitionNamespace>();\npublic playersByElement = new Map<any, TransitionAnimationPlayer[]>();\npublic playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\npublic statesByElement = new Map<any, {[triggerName: string]: StateValue}>();\npublic totalAnimations = 0;\npublic totalQueuedPlayers = 0;\nprivate _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\nprivate _namespaceList: AnimationTransitionNamespace[] = [];\nprivate _flushFns: (() => any)[] = [];\nprivate _whenQuietFns: (() => any)[] = [];\npublic namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\npublic collectedEnterElements: any[] = [];\npublic collectedLeaveElements: any[] = [];\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\n_onRemovalComplete(element: any, context: any) { this.onRemovalComplete(element, context); }\n/**\n * @param {?} driver\n * @param {?} _normalizer\n */\nconstructor(public driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @return {?}\n */\nget queuedPlayers(): TransitionAnimationPlayer[] {\n    const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\ncreateNamespace(namespaceId: string, hostElement: any) {\n    const /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (hostElement.parentNode) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is apart of the animation code, it\n      // may or may not be inserted by a parent node that is an of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n/**\n * @param {?} ns\n * @param {?} hostElement\n * @return {?}\n */\nprivate _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const /** @type {?} */ limit = this._namespaceList.length - 1;\n    if (limit >= 0) {\n      let /** @type {?} */ found = false;\n      for (let /** @type {?} */ i = limit; i >= 0; i--) {\n        const /** @type {?} */ nextNamespace = this._namespaceList[i];\n        if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n          this._namespaceList.splice(i + 1, 0, ns);\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        this._namespaceList.splice(0, 0, ns);\n      }\n    } else {\n      this._namespaceList.push(ns);\n    }\n\n    this.namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} name\n * @param {?} trigger\n * @return {?}\n */\nregisterTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n\n    const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n      const /** @type {?} */ index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _fetchNamespace(id: string) { return this._namespaceLookup[id]; }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\ntrigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      this._fetchNamespace(namespaceId).trigger(element, name, value);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\ninsertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      this._fetchNamespace(namespaceId).insertNode(element, parent);\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ncollectEnterElement(element: any) { this.collectedEnterElements.push(element); }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(namespaceId: string, element: any, context: any, doNotRecurse?: boolean): void {\n    if (!isElementNode(element)) {\n      this._onRemovalComplete(element, context);\n      return;\n    }\n\n    const /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n    if (ns) {\n      ns.removeNode(element, context, doNotRecurse);\n    } else {\n      this.markElementAsRemoved(namespaceId, element, false, context);\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?=} hasAnimation\n * @param {?=} context\n * @return {?}\n */\nmarkElementAsRemoved(namespaceId: string, element: any, hasAnimation?: boolean, context?: any) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context, hasAnimation,\n      removedBeforeQueried: false\n    };\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, name: string, phase: string,\n      callback: (event: any) => boolean): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n/**\n * @param {?} entry\n * @param {?} subTimelines\n * @return {?}\n */\nprivate _buildInstruction(entry: QueueInstruction, subTimelines: ElementInstructionMap) {\n    return entry.transition.build(\n        this.driver, entry.element, entry.fromState.value, entry.toState.value,\n        entry.toState.options, subTimelines);\n  }\n/**\n * @param {?} containerElement\n * @return {?}\n */\ndestroyInnerAnimations(containerElement: any) {\n    let /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => {\n      const /** @type {?} */ players = this.playersByElement.get(element);\n      if (players) {\n        players.forEach(player => {\n          // special case for when an element is set for destruction, but hasn't started.\n          // in this situation we want to delay the destruction until the flush occurs\n          // so that any event listeners attached to the player are triggered.\n          if (player.queued) {\n            player.markedForDestroy = true;\n          } else {\n            player.destroy();\n          }\n        });\n      }\n      const /** @type {?} */ stateMap = this.statesByElement.get(element);\n      if (stateMap) {\n        Object.keys(stateMap).forEach(triggerName => stateMap[triggerName] = DELETED_STATE_VALUE);\n      }\n    });\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    if (elements.length) {\n      elements.forEach(element => {\n        const /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n          players.forEach(player => player.finish());\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprocessLeaveNode(element: any) {\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1) {\n    let /** @type {?} */ players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this._namespaceList.length &&\n        (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      players = this._flushAnimations(microtaskId);\n    } else {\n      for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n        const /** @type {?} */ element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const /** @type {?} */ quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\nprivate _flushAnimations(microtaskId: number): TransitionAnimationPlayer[] {\n    const /** @type {?} */ subTimelines = new ElementInstructionMap();\n    const /** @type {?} */ skippedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const /** @type {?} */ queuedInstructions: QueuedTransition[] = [];\n    const /** @type {?} */ queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const /** @type {?} */ allPreStyleElements = new Map<any, Set<string>>();\n    const /** @type {?} */ allPostStyleElements = new Map<any, Set<string>>();\n\n    const /** @type {?} */ bodyNode = getBodyNode();\n    const /** @type {?} */ allEnterNodes: any[] = this.collectedEnterElements.length ?\n        this.collectedEnterElements.filter(createIsRootFilterFn(this.collectedEnterElements)) :\n        [];\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      addClass(allEnterNodes[i], ENTER_CLASSNAME);\n    }\n\n    const /** @type {?} */ allLeaveNodes: any[] = [];\n    const /** @type {?} */ leaveNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n      const /** @type {?} */ element = this.collectedLeaveElements[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n      if (details && details.setForRemoval) {\n        addClass(element, LEAVE_CLASSNAME);\n        allLeaveNodes.push(element);\n        if (!details.hasAnimation) {\n          leaveNodesWithoutAnimations.push(element);\n        }\n      }\n    }\n\n    for (let /** @type {?} */ i = this._namespaceList.length - 1; i >= 0; i--) {\n      const /** @type {?} */ ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const /** @type {?} */ player = entry.player;\n\n        const /** @type {?} */ element = entry.element;\n        if (!bodyNode || !this.driver.containsElement(bodyNode, element)) {\n          player.destroy();\n          return;\n        }\n\n        const /** @type {?} */ instruction = this._buildInstruction(entry, subTimelines);\n        if (!instruction) return;\n\n        // if a unmatched transition is queued to go then it SHOULD NOT render\n        // an animation and cancel the previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub trigger\n        // then it will instruct the timeline builder to not add a player delay, but\n        // instead stretch the first keyframe gap up until the animation starts. The\n        // reason this is important is to prevent extra initialization styles from being\n        // required by the user in the animation.\n        instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n\n        subTimelines.append(element, instruction.timelines);\n\n        const /** @type {?} */ tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach(\n            element => getOrSetAsInMap(queriedElements, element, []).push(player));\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          if (props.length) {\n            let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPreStyleElements.get(element)));\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set<string>());\n            }\n            props.forEach(prop => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPostStyleElements.get(element)));\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set<string>());\n          }\n          props.forEach(prop => setVal.add(prop));\n        });\n      });\n    }\n\n    // these can only be detected here since we have a map of all the elements\n    // that have animations attached to them...\n    const /** @type {?} */ enterNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      const /** @type {?} */ element = allEnterNodes[i];\n      if (!subTimelines.has(element)) {\n        enterNodesWithoutAnimations.push(element);\n      }\n    }\n\n    const /** @type {?} */ allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    let /** @type {?} */ sortedParentElements: any[] = [];\n    queuedInstructions.forEach(entry => {\n      const /** @type {?} */ element = entry.element;\n      if (subTimelines.has(element)) {\n        sortedParentElements.unshift(element);\n        this._beforeAnimationBuild(\n            entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n\n    skippedPlayers.forEach(player => {\n      const /** @type {?} */ element = player.element;\n      const /** @type {?} */ previousPlayers =\n          this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(\n          prevPlayer => { getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer); });\n    });\n\n    allPreviousPlayersMap.forEach(players => players.forEach(player => player.destroy()));\n\n    // PRE STAGE: fill the ! styles\n    const /** @type {?} */ preStylesMap = allPreStyleElements.size ?\n        cloakAndComputeStyles(\n            this.driver, enterNodesWithoutAnimations, allPreStyleElements, PRE_STYLE) :\n        new Map<any, ɵStyleData>();\n\n    // POST STAGE: fill the * styles\n    const /** @type {?} */ postStylesMap = cloakAndComputeStyles(\n        this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n\n    const /** @type {?} */ rootPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ subPlayers: TransitionAnimationPlayer[] = [];\n    queuedInstructions.forEach(entry => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        const /** @type {?} */ innerPlayer = this._buildAnimation(\n            player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap,\n            postStylesMap);\n        player.setRealPlayer(innerPlayer);\n\n        let /** @type {?} */ parentHasPriority: any = null;\n        for (let /** @type {?} */ i = 0; i < sortedParentElements.length; i++) {\n          const /** @type {?} */ parent = sortedParentElements[i];\n          if (parent === element) break;\n          if (this.driver.containsElement(parent, element)) {\n            parentHasPriority = parent;\n            break;\n          }\n        }\n\n        if (parentHasPriority) {\n          const /** @type {?} */ parentPlayers = this.playersByElement.get(parentHasPriority);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        } else {\n          rootPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        subPlayers.push(player);\n      }\n    });\n\n    subPlayers.forEach(player => {\n      const /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.parentPlayer.onDestroy(() => player.destroy());\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let /** @type {?} */ i = 0; i < allLeaveNodes.length; i++) {\n      const /** @type {?} */ element = allLeaveNodes[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let /** @type {?} */ players: AnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n          let /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      if (players.length) {\n        removeNodesAfterAnimationDone(this, element, players);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const /** @type {?} */ index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    allEnterNodes.forEach(element => removeClass(element, ENTER_CLASSNAME));\n\n    return rootPlayers;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @return {?}\n */\nelementContainsData(namespaceId: string, element: any) {\n    let /** @type {?} */ containsData = false;\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlush(callback: () => any) { this._flushFns.push(callback); }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlushAnimationsDone(callback: () => any) { this._whenQuietFns.push(callback); }\n/**\n * @param {?} element\n * @param {?} isQueriedElement\n * @param {?=} namespaceId\n * @param {?=} triggerName\n * @param {?=} toStateValue\n * @return {?}\n */\nprivate _getPreviousPlayers(\n      element: string, isQueriedElement: boolean, namespaceId?: string, triggerName?: string,\n      toStateValue?: any): TransitionAnimationPlayer[] {\n    let /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const /** @type {?} */ isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @return {?}\n */\nprivate _beforeAnimationBuild(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>) {\n    // it's important to do this step before destroying the players\n    // so that the onDone callback below won't fire before this\n    eraseStyles(instruction.element, instruction.fromStyles);\n\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const /** @type {?} */ targetNameSpaceId: string|undefined =\n        instruction.isRemovalTransition ? undefined : namespaceId;\n    const /** @type {?} */ targetTriggerName: string|undefined =\n        instruction.isRemovalTransition ? undefined : triggerName;\n\n    instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      const /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n      const /** @type {?} */ previousPlayers = this._getPreviousPlayers(\n          element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const /** @type {?} */ realPlayer = /** @type {?} */(( player.getRealPlayer() as any));\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        players.push(player);\n      });\n    });\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @param {?} skippedPlayersMap\n * @param {?} preStylesMap\n * @param {?} postStylesMap\n * @return {?}\n */\nprivate _buildAnimation(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n      skippedPlayersMap: Map<any, AnimationPlayer[]>, preStylesMap: Map<any, ɵStyleData>,\n      postStylesMap: Map<any, ɵStyleData>): AnimationPlayer {\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const /** @type {?} */ allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ allConsumedElements = new Set<any>();\n    const /** @type {?} */ allSubElements = new Set<any>();\n    const /** @type {?} */ allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const /** @type {?} */ details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer();\n\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      let /** @type {?} */ previousPlayers: AnimationPlayer[] = EMPTY_PLAYER_ARRAY;\n      if (!allConsumedElements.has(element)) {\n        allConsumedElements.add(element);\n        const /** @type {?} */ _previousPlayers = allPreviousPlayersMap.get(element);\n        if (_previousPlayers) {\n          previousPlayers = _previousPlayers.map(p => p.getRealPlayer());\n        }\n      }\n      const /** @type {?} */ preStyles = preStylesMap.get(element);\n      const /** @type {?} */ postStyles = postStylesMap.get(element);\n      const /** @type {?} */ keyframes = normalizeKeyframes(\n          this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles,\n          postStyles);\n      const /** @type {?} */ player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach(player => {\n      getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(\n        element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n\n    return player;\n  }\n/**\n * @param {?} instruction\n * @param {?} keyframes\n * @param {?} previousPlayers\n * @return {?}\n */\nprivate _buildPlayer(\n      instruction: AnimationTimelineInstruction, keyframes: ɵStyleData[],\n      previousPlayers: AnimationPlayer[]): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n          instruction.element, keyframes, instruction.duration, instruction.delay,\n          instruction.easing, previousPlayers);\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer();\n  }\n}\n\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationEngine.prototype.players;\n/** @type {?} */\nTransitionAnimationEngine.prototype.newHostElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByQueriedElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.statesByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalAnimations;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalQueuedPlayers;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceLookup;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceList;\n/** @type {?} */\nTransitionAnimationEngine.prototype._flushFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype._whenQuietFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype.namespacesByHostElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedEnterElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedLeaveElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.onRemovalComplete;\n/** @type {?} */\nTransitionAnimationEngine.prototype.driver;\n/** @type {?} */\nTransitionAnimationEngine.prototype._normalizer;\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\nprivate _player: AnimationPlayer = new NoopAnimationPlayer();\nprivate _containsRealPlayer = false;\nprivate _queuedCallbacks: {[name: string]: (() => any)[]} = {};\nprivate _destroyed = false;\npublic parentPlayer: AnimationPlayer;\npublic markedForDestroy: boolean = false;\n/**\n * @param {?} namespaceId\n * @param {?} triggerName\n * @param {?} element\n */\nconstructor(public namespaceId: string,\npublic triggerName: string,\npublic element: any) {}\n/**\n * @return {?}\n */\nget queued() { return this._containsRealPlayer == false; }\n/**\n * @return {?}\n */\nget destroyed() { return this._destroyed; }\n/**\n * @param {?} player\n * @return {?}\n */\nsetRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    Object.keys(this._queuedCallbacks).forEach(phase => {\n      this._queuedCallbacks[phase].forEach(\n          callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks = {};\n    this._containsRealPlayer = true;\n  }\n/**\n * @return {?}\n */\ngetRealPlayer() { return this._player; }\n/**\n * @param {?} name\n * @param {?} callback\n * @return {?}\n */\nprivate _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n/**\n * @return {?}\n */\ninit(): void { this._player.init(); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this.queued ? false : this._player.hasStarted(); }\n/**\n * @return {?}\n */\nplay(): void { !this.queued && this._player.play(); }\n/**\n * @return {?}\n */\npause(): void { !this.queued && this._player.pause(); }\n/**\n * @return {?}\n */\nrestart(): void { !this.queued && this._player.restart(); }\n/**\n * @return {?}\n */\nfinish(): void { this._player.finish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._destroyed = true;\n    this._player.destroy();\n  }\n/**\n * @return {?}\n */\nreset(): void { !this.queued && this._player.reset(); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: any): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n/**\n * @return {?}\n */\ngetPosition(): number { return this.queued ? 0 : this._player.getPosition(); }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._player.totalTime; }\n}\n\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationPlayer.prototype._player;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._containsRealPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._queuedCallbacks;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.markedForDestroy;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.namespaceId;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.triggerName;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.element;\n}\n\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map: Map<any, any[]>| {[key: string]: any}, key: any, value: any) {\n  let /** @type {?} */ currentValues: any[]|null|undefined;\n  if (map instanceof Map) {\n    currentValues = map.get(key);\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        map.delete(key);\n      }\n    }\n  } else {\n    currentValues = map[key];\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        delete map[key];\n      }\n    }\n  }\n  return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value: any): string {\n  switch (typeof value) {\n    case 'boolean':\n      return value ? '1' : '0';\n    default:\n      return value != null ? value.toString() : null;\n  }\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element: any, value?: string) {\n  const /** @type {?} */ oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n/**\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(\n    driver: AnimationDriver, elements: any[], elementPropsMap: Map<any, Set<string>>,\n    defaultStyle: string): Map<any, ɵStyleData> {\n  const /** @type {?} */ cloakVals = elements.map(element => cloakElement(element));\n  const /** @type {?} */ valuesMap = new Map<any, ɵStyleData>();\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const /** @type {?} */ styles: ɵStyleData = {};\n    props.forEach(prop => {\n      const /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  elements.forEach((element, i) => cloakElement(element, cloakVals[i]));\n  return valuesMap;\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction createIsRootFilterFn(nodes: any): (node: any) => boolean {\n  const /** @type {?} */ nodeSet = new Set(nodes);\n  const /** @type {?} */ knownRootContainer = new Set();\n  let /** @type {?} */ isRoot: (node: any) => boolean;\n  isRoot = node => {\n    if (!node) return true;\n    if (nodeSet.has(node.parentNode)) return false;\n    if (knownRootContainer.has(node.parentNode)) return true;\n    if (isRoot(node.parentNode)) {\n      knownRootContainer.add(node);\n      return true;\n    }\n    return false;\n  };\n  return isRoot;\n}\n\nconst /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element: any, className: string): boolean {\n  if (element.classList) {\n    return element.classList.contains(className);\n  } else {\n    const /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n    return classes && classes[className];\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (!classes) {\n      classes = element[CLASSES_CACHE_KEY] = {};\n    }\n    classes[className] = true;\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (classes) {\n      delete classes[className];\n    }\n  }\n}\n/**\n * @return {?}\n */\nfunction getBodyNode(): any|null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(\n    engine: TransitionAnimationEngine, element: any, players: AnimationPlayer[]) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationMetadata, AnimationOptions, AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nexport class TimelineAnimationEngine {\nprivate _animations: {[id: string]: Ast} = {};\nprivate _playersById: {[id: string]: AnimationPlayer} = {};\npublic players: AnimationPlayer[] = [];\n/**\n * @param {?} _driver\n * @param {?} _normalizer\n */\nconstructor(private _driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @param {?} id\n * @param {?} metadata\n * @return {?}\n */\nregister(id: string, metadata: AnimationMetadata|AnimationMetadata[]) {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = buildAnimationAst(metadata, errors);\n    if (errors.length) {\n      throw new Error(\n          `Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    } else {\n      this._animations[id] = ast;\n    }\n  }\n/**\n * @param {?} i\n * @param {?} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nprivate _buildPlayer(\n      i: AnimationTimelineInstruction, preStyles: ɵStyleData,\n      postStyles?: ɵStyleData): AnimationPlayer {\n    const /** @type {?} */ element = i.element;\n    const /** @type {?} */ keyframes = normalizeKeyframes(\n        this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = this._animations[id];\n    let /** @type {?} */ instructions: AnimationTimelineInstruction[];\n\n    const /** @type {?} */ autoStylesMap = new Map<any, ɵStyleData>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n          this._driver, element, ast, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n        inst.postStyleProps.forEach(prop => styles[prop] = null);\n      });\n    } else {\n      errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw new Error(\n          `Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      Object.keys(styles).forEach(\n          prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n    });\n\n    const /** @type {?} */ players = instructions.map(i => {\n      const /** @type {?} */ styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, {}, styles);\n    });\n    const /** @type {?} */ player = optimizeGroupPlayer(players);\n    this._playersById[id] = player;\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\ndestroy(id: string) {\n    const /** @type {?} */ player = this._getPlayer(id);\n    player.destroy();\n    delete this._playersById[id];\n    const /** @type {?} */ index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _getPlayer(id: string): AnimationPlayer {\n    const /** @type {?} */ player = this._playersById[id];\n    if (!player) {\n      throw new Error(`Unable to find the timeline player referenced by ${id}`);\n    }\n    return player;\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(id: string, element: string, eventName: string, callback: (event: any) => any):\n      () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\ncommand(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, /** @type {?} */(( args[0] as AnimationMetadata | AnimationMetadata[])));\n      return;\n    }\n\n    if (command == 'create') {\n      const /** @type {?} */ options = /** @type {?} */(( (args[0] || {}) as AnimationOptions));\n      this.create(id, element, options);\n      return;\n    }\n\n    const /** @type {?} */ player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat( /** @type {?} */((args[0] as string))));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineAnimationEngine.prototype._animations;\n/** @type {?} */\nTimelineAnimationEngine.prototype._playersById;\n/** @type {?} */\nTimelineAnimationEngine.prototype.players;\n/** @type {?} */\nTimelineAnimationEngine.prototype._driver;\n/** @type {?} */\nTimelineAnimationEngine.prototype._normalizer;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\n\nimport {copyStyles} from '../util';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTransitionFactory} from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name: string, ast: TriggerAst): AnimationTrigger {\n  return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class AnimationTrigger {\npublic transitionFactories: AnimationTransitionFactory[] = [];\npublic fallbackTransition: AnimationTransitionFactory;\npublic states: {[stateName: string]: ɵStyleData} = {};\n/**\n * @param {?} name\n * @param {?} ast\n */\nconstructor(public name: string,\npublic ast: TriggerAst) {\n    ast.states.forEach(ast => {\n      const obj = this.states[ast.name] = {};\n      ast.style.styles.forEach(styleTuple => {\n        if (typeof styleTuple == 'object') {\n          copyStyles(styleTuple as ɵStyleData, false, obj);\n        }\n      });\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n/**\n * @return {?}\n */\nget containsQueries() { return this.ast.queryCount > 0; }\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatchTransition(currentState: any, nextState: any): AnimationTransitionFactory|null {\n    const /** @type {?} */ entry = this.transitionFactories.find(f => f.match(currentState, nextState));\n    return entry || null;\n  }\n}\n\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTrigger.prototype.transitionFactories;\n/** @type {?} */\nAnimationTrigger.prototype.fallbackTransition;\n/** @type {?} */\nAnimationTrigger.prototype.states;\n/** @type {?} */\nAnimationTrigger.prototype.name;\n/** @type {?} */\nAnimationTrigger.prototype.ast;\n}\n\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(\n    triggerName: string, states: {[stateName: string]: ɵStyleData}): AnimationTransitionFactory {\n  const /** @type {?} */ matchers = [(fromState: any, toState: any) => true];\n  const /** @type {?} */ animation = new SequenceAst([]);\n  const /** @type {?} */ transition = new TransitionAst(matchers, animation);\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj: {[key: string]: any}, key1: string, key2: string) {\n  if (obj.hasOwnProperty(key1)) {\n    if (!obj.hasOwnProperty(key2)) {\n      obj[key2] = obj[key1];\n    }\n  } else if (obj.hasOwnProperty(key2)) {\n    obj[key1] = obj[key2];\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {iteratorToArray, mergeAnimationOptions} from '../util';\n\nimport {TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {AnimationTransitionInstruction, createTransitionInstruction} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class AnimationTransitionFactory {\n/**\n * @param {?} _triggerName\n * @param {?} ast\n * @param {?} _stateStyles\n */\nconstructor(\nprivate _triggerName: string,\npublic ast: TransitionAst,\nprivate _stateStyles: {[stateName: string]: ɵStyleData}) {}\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatch(currentState: any, nextState: any): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n  }\n/**\n * @param {?} driver\n * @param {?} element\n * @param {?} currentState\n * @param {?} nextState\n * @param {?=} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuild(\n      driver: AnimationDriver, element: any, currentState: any, nextState: any,\n      options?: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTransitionInstruction|undefined {\n    const /** @type {?} */ animationOptions = mergeAnimationOptions(this.ast.options || {}, options || {});\n\n    const /** @type {?} */ backupStateStyles = this._stateStyles['*'] || {};\n    const /** @type {?} */ currentStateStyles = this._stateStyles[currentState] || backupStateStyles;\n    const /** @type {?} */ nextStateStyles = this._stateStyles[nextState] || backupStateStyles;\n\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ timelines = buildAnimationTimelines(\n        driver, element, this.ast.animation, currentStateStyles, nextStateStyles, animationOptions,\n        subInstructions, errors);\n\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n\n    const /** @type {?} */ preStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ postStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ queriedElements = new Set<any>();\n    timelines.forEach(tl => {\n      const /** @type {?} */ elm = tl.element;\n      const /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n      tl.preStyleProps.forEach(prop => preProps[prop] = true);\n\n      const /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n      tl.postStyleProps.forEach(prop => postProps[prop] = true);\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    const /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(\n        element, this._triggerName, currentState, nextState, nextState === 'void',\n        currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap,\n        postStyleMap);\n  }\n}\n\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionFactory.prototype._triggerName;\n/** @type {?} */\nAnimationTransitionFactory.prototype.ast;\n/** @type {?} */\nAnimationTransitionFactory.prototype._stateStyles;\n}\n\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(\n    matchFns: TransitionMatcherFn[], currentState: any, nextState: any): boolean {\n  return matchFns.some(fn => fn(currentState, nextState));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleData;\n  toState: string;\n  toStyles: ɵStyleData;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, {[prop: string]: boolean}>;\n  postStyleProps: Map<any, {[prop: string]: boolean}>;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @return {?}\n */\nexport function createTransitionInstruction(\n    element: any, triggerName: string, fromState: string, toState: string,\n    isRemovalTransition: boolean, fromStyles: ɵStyleData, toStyles: ɵStyleData,\n    timelines: AnimationTimelineInstruction[], queriedElements: any[],\n    preStyleProps: Map<any, {[prop: string]: boolean}>,\n    postStyleProps: Map<any, {[prop: string]: boolean}>): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n/**\n * @param {?} propertyName\n * @param {?} errors\n * @return {?}\n */\nnormalizePropertyName(propertyName: string, errors: string[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n/**\n * @param {?} userProvidedProperty\n * @param {?} normalizedProperty\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nnormalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    let /** @type {?} */ unit: string = '';\n    const /** @type {?} */ strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n\nconst /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap(\n    'width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys: string[]): {[key: string]: boolean} {\n  const /** @type {?} */ map: {[key: string]: boolean} = {};\n  keys.forEach(key => map[key] = true);\n  return map;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @experimental Animation support is experimental.\n */\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: string[]): string;\n  abstract normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string { return propertyName; }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {normalizeStyles} from '../util';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class Animation {\nprivate _animationAst: Ast;\n/**\n * @param {?} _driver\n * @param {?} input\n */\nconstructor(private _driver: AnimationDriver, input: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(input, errors);\n    if (errors.length) {\n      const errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    this._animationAst = ast;\n  }\n/**\n * @param {?} element\n * @param {?} startingStyles\n * @param {?} destinationStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuildTimelines(\n      element: any, startingStyles: ɵStyleData|ɵStyleData[],\n      destinationStyles: ɵStyleData|ɵStyleData[], options: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[] {\n    const /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */((\n                                                  <ɵStyleData>startingStyles));\n    const /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */((\n                                                    <ɵStyleData>destinationStyles));\n    const /** @type {?} */ errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ result = buildAnimationTimelines(\n        this._driver, element, this._animationAst, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    return result;\n  }\n}\n\nfunction Animation_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimation.prototype._animationAst;\n/** @type {?} */\nAnimation.prototype._driver;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationOptions, AnimationQueryOptions, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, AstVisitor, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTimelineInstruction, createTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(\n    driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData = {},\n    finalStyles: ɵStyleData = {}, options: AnimationOptions,\n    subInstructions?: ElementInstructionMap, errors: any[] = []): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n      driver, rootElement, ast, startingStyles, finalStyles, options, subInstructions, errors);\n}\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} startingStyles\n * @param {?} finalStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nbuildKeyframes(\n      driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData,\n      finalStyles: ɵStyleData, options: AnimationOptions, subInstructions?: ElementInstructionMap,\n      errors: any[] = []): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, errors, []);\n    context.options = options;\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    ast.visit(this, context);\n\n    // this checks to see if an actual animation happened\n    const /** @type {?} */ timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    if (timelines.length && Object.keys(finalStyles).length) {\n      const /** @type {?} */ tl = timelines[timelines.length - 1];\n      if (!tl.allowOnlyTimelineStyles()) {\n        tl.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n                              [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n    if (elementInstructions) {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n      const /** @type {?} */ endTime = this._visitSubInstructions(\n          elementInstructions, innerContext, /** @type {?} */(( innerContext.options as AnimateChildOptions)));\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} instructions\n * @param {?} context\n * @param {?} options\n * @return {?}\n */\nprivate _visitSubInstructions(\n      instructions: AnimationTimelineInstruction[], context: AnimationTimelineContext,\n      options: AnimateChildOptions): number {\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    let /** @type {?} */ furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const /** @type {?} */ instructionTimings =\n            context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime =\n            Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ subContextCount = context.subContextCount;\n    let /** @type {?} */ ctx = context;\n    const /** @type {?} */ options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode instanceof StyleAst) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const /** @type {?} */ delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => s.visit(this, ctx));\n\n      // this is here just incase the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ innerTimelines: TimelineBuilder[] = [];\n    let /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach(s => {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      s.visit(this, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(\n        timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if (ast instanceof DynamicTimingAst) {\n      const /** @type {?} */ strValue = context.params ?\n          interpolateParams(ast.value, context.params, context.errors) :\n          ast.value.toString();\n      return resolveTiming(strValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timings = context.currentAnimateTimings = this.visitTiming(ast.timings, context);\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const /** @type {?} */ style = ast.style;\n    if (style instanceof KeyframesAst) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle( /** @type {?} */((style as StyleAst)), context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timeline = context.currentTimeline;\n    const /** @type {?} */ timings = /** @type {?} */(( context.currentAnimateTimings));\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.getCurrentStyleProperties().length) {\n      timeline.forwardFrame();\n    }\n\n    const /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ startTime = ( /** @type {?} */((context.currentTimeline))).duration;\n    const /** @type {?} */ duration = currentAnimateTimings.duration;\n    const /** @type {?} */ innerContext = context.createSubContext();\n    const /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach(step => {\n      const /** @type {?} */ offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ options = /** @type {?} */(( (ast.options || {}) as AnimationQueryOptions));\n    const /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode instanceof StyleAst ||\n                  (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let /** @type {?} */ furthestTime = startTime;\n    const /** @type {?} */ elms = context.invokeQuery(\n        ast.selector, ast.originalSelector, ast.limit, ast.includeSelf,\n        options.optional ? true : false, context.errors);\n\n    context.currentQueryTotal = elms.length;\n    let /** @type {?} */ sameElementTimeline: TimelineBuilder|null = null;\n    elms.forEach((element, i) => {\n\n      context.currentQueryIndex = i;\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      ast.animation.visit(this, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ parentContext = /** @type {?} */(( context.parentContext));\n    const /** @type {?} */ tl = context.currentTimeline;\n    const /** @type {?} */ timings = ast.timings;\n    const /** @type {?} */ duration = Math.abs(timings.duration);\n    const /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n    let /** @type {?} */ delay = duration * context.currentQueryIndex;\n\n    let /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const /** @type {?} */ startingTime = timeline.currentTime;\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n        (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number; value: string | number;\n};\n\nconst /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */(( <Ast>{}));\nexport class AnimationTimelineContext {\npublic parentContext: AnimationTimelineContext|null = null;\npublic currentTimeline: TimelineBuilder;\npublic currentAnimateTimings: AnimateTimings|null = null;\npublic previousNode: Ast = DEFAULT_NOOP_PREVIOUS_NODE;\npublic subContextCount = 0;\npublic options: AnimationOptions = {};\npublic currentQueryIndex: number = 0;\npublic currentQueryTotal: number = 0;\npublic currentStaggerTime: number = 0;\n/**\n * @param {?} _driver\n * @param {?} element\n * @param {?} subInstructions\n * @param {?} errors\n * @param {?} timelines\n * @param {?=} initialTimeline\n */\nconstructor(\nprivate _driver: AnimationDriver,\npublic element: any,\npublic subInstructions: ElementInstructionMap,\npublic errors: any[],\npublic timelines: TimelineBuilder[], initialTimeline?: TimelineBuilder) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(element, 0);\n    timelines.push(this.currentTimeline);\n  }\n/**\n * @return {?}\n */\nget params() { return this.options.params; }\n/**\n * @param {?} options\n * @param {?=} skipIfExists\n * @return {?}\n */\nupdateOptions(options: AnimationOptions|null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const /** @type {?} */ newOptions = /** @type {?} */(( options as any));\n    let /** @type {?} */ optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      ( /** @type {?} */((optionsToUpdate as any))).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const /** @type {?} */ newParams = newOptions.params;\n    if (newParams) {\n      let /** @type {?} */ paramsToUpdate: {[name: string]: any} = /** @type {?} */(( optionsToUpdate.params));\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nprivate _copyOptions() {\n    const /** @type {?} */ options: AnimationOptions = {};\n    if (this.options) {\n      const /** @type {?} */ oldParams = this.options.params;\n      if (oldParams) {\n        const /** @type {?} */ params: {[name: string]: any} = options['params'] = {};\n        Object.keys(this.options.params).forEach(name => { params[name] = oldParams[name]; });\n      }\n    }\n    return options;\n  }\n/**\n * @param {?=} options\n * @param {?=} element\n * @param {?=} newTime\n * @return {?}\n */\ncreateSubContext(options: AnimationOptions|null = null, element?: any, newTime?: number):\n      AnimationTimelineContext {\n    const /** @type {?} */ target = element || this.element;\n    const /** @type {?} */ context = new AnimationTimelineContext(\n        this._driver, target, this.subInstructions, this.errors, this.timelines,\n        this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n/**\n * @param {?=} newTime\n * @return {?}\n */\ntransformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n/**\n * @param {?} instruction\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nappendInstructionToTimeline(\n      instruction: AnimationTimelineInstruction, duration: number|null,\n      delay: number|null): AnimateTimings {\n    const /** @type {?} */ updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const /** @type {?} */ builder = new SubTimelineBuilder(\n        instruction.element, instruction.keyframes, instruction.preStyleProps,\n        instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nincrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n/**\n * @param {?} selector\n * @param {?} originalSelector\n * @param {?} limit\n * @param {?} includeSelf\n * @param {?} optional\n * @param {?} errors\n * @return {?}\n */\ninvokeQuery(\n      selector: string, originalSelector: string, limit: number, includeSelf: boolean,\n      optional: boolean, errors: any[]): any[] {\n    let /** @type {?} */ results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {  // if :self is only used then the selector is empty\n      const /** @type {?} */ multi = limit != 1;\n      results.push(...this._driver.query(this.element, selector, multi));\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(\n          `\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n    }\n    return results;\n  }\n}\n\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTimelineContext.prototype.parentContext;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentTimeline;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationTimelineContext.prototype.previousNode;\n/** @type {?} */\nAnimationTimelineContext.prototype.subContextCount;\n/** @type {?} */\nAnimationTimelineContext.prototype.options;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryIndex;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryTotal;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentStaggerTime;\n/** @type {?} */\nAnimationTimelineContext.prototype._driver;\n/** @type {?} */\nAnimationTimelineContext.prototype.element;\n/** @type {?} */\nAnimationTimelineContext.prototype.subInstructions;\n/** @type {?} */\nAnimationTimelineContext.prototype.errors;\n/** @type {?} */\nAnimationTimelineContext.prototype.timelines;\n}\n\nexport class TimelineBuilder {\npublic duration: number = 0;\npublic easing: string|null;\nprivate _previousKeyframe: ɵStyleData = {};\nprivate _currentKeyframe: ɵStyleData = {};\nprivate _keyframes = new Map<number, ɵStyleData>();\nprivate _styleSummary: {[prop: string]: StyleAtTime} = {};\nprivate _localTimelineStyles: ɵStyleData;\nprivate _globalTimelineStyles: ɵStyleData;\nprivate _pendingStyles: ɵStyleData = {};\nprivate _backFill: ɵStyleData = {};\nprivate _currentEmptyStepKeyframe: ɵStyleData|null = null;\n/**\n * @param {?} element\n * @param {?} startTime\n * @param {?=} _elementTimelineStylesLookup\n */\nconstructor(\npublic element: any,\npublic startTime: number,\nprivate _elementTimelineStylesLookup?: Map<any, ɵStyleData>) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleData>();\n    }\n\n    this._localTimelineStyles = Object.create(this._backFill, {});\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element) !;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.getCurrentStyleProperties().length > 0;\n      default:\n        return true;\n    }\n  }\n/**\n * @return {?}\n */\ngetCurrentStyleProperties(): string[] { return Object.keys(this._currentKeyframe); }\n/**\n * @return {?}\n */\nget currentTime() { return this.startTime + this.duration; }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n/**\n * @param {?} element\n * @param {?=} currentTime\n * @return {?}\n */\nfork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n        element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n/**\n * @return {?}\n */\nprivate _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = /** @type {?} */(( this._keyframes.get(this.duration)));\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = Object.create(this._backFill, {});\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n/**\n * @return {?}\n */\nforwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nforwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} prop\n * @param {?} value\n * @return {?}\n */\nprivate _updateStyle(prop: string, value: string|number) {\n    this._localTimelineStyles[prop] = value;\n    this._globalTimelineStyles[prop] = value;\n    this._styleSummary[prop] = {time: this.currentTime, value};\n  }\n/**\n * @return {?}\n */\nallowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n/**\n * @param {?} easing\n * @return {?}\n */\napplyEmptyStep(easing: string|null) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    Object.keys(this._globalTimelineStyles).forEach(prop => {\n      this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n      this._currentKeyframe[prop] = AUTO_STYLE;\n    });\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n/**\n * @param {?} input\n * @param {?} easing\n * @param {?} errors\n * @param {?=} options\n * @return {?}\n */\nsetStyles(\n      input: (ɵStyleData|string)[], easing: string|null, errors: any[],\n      options?: AnimationOptions) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    const /** @type {?} */ params = (options && options.params) || {};\n    const /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n      this._pendingStyles[prop] = val;\n      if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n        this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n            this._globalTimelineStyles[prop] :\n            AUTO_STYLE;\n      }\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\napplyStylesToKeyframe() {\n    const /** @type {?} */ styles = this._pendingStyles;\n    const /** @type {?} */ props = Object.keys(styles);\n    if (props.length == 0) return;\n\n    this._pendingStyles = {};\n\n    props.forEach(prop => {\n      const /** @type {?} */ val = styles[prop];\n      this._currentKeyframe[prop] = val;\n    });\n\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      if (!this._currentKeyframe.hasOwnProperty(prop)) {\n        this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n      }\n    });\n  }\n/**\n * @return {?}\n */\nsnapshotCurrentStyles() {\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      const /** @type {?} */ val = this._localTimelineStyles[prop];\n      this._pendingStyles[prop] = val;\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\ngetFinalKeyframe() { return this._keyframes.get(this.duration); }\n/**\n * @return {?}\n */\nget properties() {\n    const /** @type {?} */ properties: string[] = [];\n    for (let /** @type {?} */ prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n/**\n * @param {?} timeline\n * @return {?}\n */\nmergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    Object.keys(timeline._styleSummary).forEach(prop => {\n      const /** @type {?} */ details0 = this._styleSummary[prop];\n      const /** @type {?} */ details1 = timeline._styleSummary[prop];\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const /** @type {?} */ preStyleProps = new Set<string>();\n    const /** @type {?} */ postStyleProps = new Set<string>();\n    const /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let /** @type {?} */ finalKeyframes: ɵStyleData[] = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n      Object.keys(finalKeyframe).forEach(prop => {\n        const /** @type {?} */ value = finalKeyframe[prop];\n        if (value == PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value == AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe['offset'] = time / this.duration;\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const /** @type {?} */ preProps: string[] = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const /** @type {?} */ postProps: string[] = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const /** @type {?} */ kf0 = finalKeyframes[0];\n      const /** @type {?} */ kf1 = copyObj(kf0);\n      kf0['offset'] = 0;\n      kf1['offset'] = 1;\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n        this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime,\n        this.easing, false);\n  }\n}\n\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineBuilder.prototype.duration;\n/** @type {?} */\nTimelineBuilder.prototype.easing;\n/** @type {?} */\nTimelineBuilder.prototype._previousKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._currentKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._keyframes;\n/** @type {?} */\nTimelineBuilder.prototype._styleSummary;\n/** @type {?} */\nTimelineBuilder.prototype._localTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._globalTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._pendingStyles;\n/** @type {?} */\nTimelineBuilder.prototype._backFill;\n/** @type {?} */\nTimelineBuilder.prototype._currentEmptyStepKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype.element;\n/** @type {?} */\nTimelineBuilder.prototype.startTime;\n/** @type {?} */\nTimelineBuilder.prototype._elementTimelineStylesLookup;\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\npublic timings: AnimateTimings;\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} timings\n * @param {?=} _stretchStartingKeyframe\n */\nconstructor(\npublic element: any,\npublic keyframes: ɵStyleData[],\npublic preStyleProps: string[],\npublic postStyleProps: string[], timings: AnimateTimings,\nprivate _stretchStartingKeyframe: boolean = false) {\n    super(element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean { return this.keyframes.length > 1; }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    let /** @type {?} */ keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const /** @type {?} */ newKeyframes: ɵStyleData[] = [];\n      const /** @type {?} */ totalTime = duration + delay;\n      const /** @type {?} */ startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n      newFirstKeyframe['offset'] = 0;\n      newKeyframes.push(newFirstKeyframe);\n\n      const /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n      oldFirstKeyframe['offset'] = roundOffset(startingGap);\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still renderered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const /** @type {?} */ limit = keyframes.length - 1;\n      for (let /** @type {?} */ i = 1; i <= limit; i++) {\n        let /** @type {?} */ kf = copyStyles(keyframes[i], false);\n        const /** @type {?} */ oldOffset = /** @type {?} */(( kf['offset'] as number));\n        const /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n        kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n        this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing,\n        true);\n  }\n}\n\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nSubTimelineBuilder.prototype.timings;\n/** @type {?} */\nSubTimelineBuilder.prototype.element;\n/** @type {?} */\nSubTimelineBuilder.prototype.keyframes;\n/** @type {?} */\nSubTimelineBuilder.prototype.preStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype.postStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input: (ɵStyleData | string)[], allStyles: ɵStyleData) {\n  const /** @type {?} */ styles: ɵStyleData = {};\n  let /** @type {?} */ allProperties: string[];\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || Object.keys(allStyles);\n      allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n    } else {\n      copyStyles( /** @type {?} */((token as ɵStyleData)), false, styles);\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nexport class ElementInstructionMap {\nprivate _map = new Map<any, AnimationTimelineInstruction[]>();\n/**\n * @param {?} element\n * @return {?}\n */\nconsume(element: any): AnimationTimelineInstruction[] {\n    let /** @type {?} */ instructions = this._map.get(element);\n    if (instructions) {\n      this._map.delete(element);\n    } else {\n      instructions = [];\n    }\n    return instructions;\n  }\n/**\n * @param {?} element\n * @param {?} instructions\n * @return {?}\n */\nappend(element: any, instructions: AnimationTimelineInstruction[]) {\n    let /** @type {?} */ existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nhas(element: any): boolean { return this._map.has(element); }\n/**\n * @return {?}\n */\nclear() { this._map.clear(); }\n}\n\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n/** @type {?} */\nElementInstructionMap.prototype._map;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: ɵStyleData[];\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string|null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(\n    element: any, keyframes: ɵStyleData[], preStyleProps: string[], postStyleProps: string[],\n    duration: number, delay: number, easing: string | null = null,\n    subTimeline: boolean = false): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay, easing, subTimeline\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, style, ɵStyleData} from '@angular/animations';\n\nimport {getOrSetAsInMap} from '../render/shared';\nimport {ENTER_SELECTOR, LEAVE_SELECTOR, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, copyObj, normalizeAnimationEntry, resolveTiming, validateStyleParams} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationDslVisitor, visitAnimationNode} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst /** @type {?} */ SELF_TOKEN = ':self';\nconst /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(\n    metadata: AnimationMetadata | AnimationMetadata[], errors: any[]): Ast {\n  return new AnimationAstBuilderVisitor().build(metadata, errors);\n}\n\nconst /** @type {?} */ LEAVE_TOKEN = ':leave';\nconst /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\nconst /** @type {?} */ ENTER_TOKEN = ':enter';\nconst /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst /** @type {?} */ ROOT_SELECTOR = '';\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nbuild(metadata: AnimationMetadata|AnimationMetadata[], errors: any[]): Ast {\n    const /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    return /** @type {?} */(( visitAnimationNode(this, normalizeAnimationEntry(metadata), context) as Ast));\n  }\n/**\n * @param {?} context\n * @return {?}\n */\nprivate _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = {};\n    context.collectedStyles[ROOT_SELECTOR] = {};\n    context.currentTime = 0;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(metadata: AnimationTriggerMetadata, context: AnimationAstBuilderContext):\n      TriggerAst {\n    let /** @type {?} */ queryCount = context.queryCount = 0;\n    let /** @type {?} */ depCount = context.depCount = 0;\n    const /** @type {?} */ states: StateAst[] = [];\n    const /** @type {?} */ transitions: TransitionAst[] = [];\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const /** @type {?} */ stateDef = /** @type {?} */(( def as AnimationStateMetadata));\n        const /** @type {?} */ name = stateDef.name;\n        name.split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const /** @type {?} */ transition = this.visitTransition( /** @type {?} */((def as AnimationTransitionMetadata)), context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(\n            'only state() and transition() definitions can sit inside of a trigger()');\n      }\n    });\n    const /** @type {?} */ ast = new TriggerAst(metadata.name, states, transitions);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = queryCount;\n    ast.depCount = depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    return new StateAst(metadata.name, this.visitStyle(metadata.styles, context));\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTransition(metadata: AnimationTransitionMetadata, context: AnimationAstBuilderContext):\n      TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n    const /** @type {?} */ ast = new TransitionAst(matchers, entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = context.queryCount;\n    ast.depCount = context.depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitSequence(metadata: AnimationSequenceMetadata, context: AnimationAstBuilderContext):\n      SequenceAst {\n    const /** @type {?} */ ast = new SequenceAst(metadata.steps.map(s => visitAnimationNode(this, s, context)));\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const /** @type {?} */ currentTime = context.currentTime;\n    let /** @type {?} */ furthestTime = 0;\n    const /** @type {?} */ steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const /** @type {?} */ innerAst = visitAnimationNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    const /** @type {?} */ ast = new GroupAst(steps);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(metadata: AnimationAnimateMetadata, context: AnimationAstBuilderContext):\n      AnimateAst {\n    const /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n\n    let /** @type {?} */ styles: StyleAst|KeyframesAst;\n    let /** @type {?} */ styleMetadata: AnimationMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styles = this.visitKeyframes( /** @type {?} */((styleMetadata as AnimationKeyframesSequenceMetadata)), context);\n    } else {\n      let /** @type {?} */ styleMetadata = /** @type {?} */(( metadata.styles as AnimationStyleMetadata));\n      let /** @type {?} */ isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const /** @type {?} */ newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const /** @type {?} */ styleAst = this.visitStyle(styleMetadata, context);\n      styleAst.isEmptyStep = isEmpty;\n      styles = styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return new AnimateAst(timingAst, styles);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nprivate _makeStyleAst(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext):\n      StyleAst {\n    const /** @type {?} */ styles: (ɵStyleData | string)[] = [];\n    if (Array.isArray(metadata.styles)) {\n      ( /** @type {?} */((metadata.styles as(ɵStyleData | string)[]))).forEach(styleTuple => {\n        if (typeof styleTuple == 'string') {\n          if (styleTuple == AUTO_STYLE) {\n            styles.push( /** @type {?} */((styleTuple as string)));\n          } else {\n            context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n          }\n        } else {\n          styles.push( /** @type {?} */((styleTuple as ɵStyleData)));\n        }\n      })\n    } else {\n      styles.push(metadata.styles);\n    }\n\n    let /** @type {?} */ collectedEasing: string|null = null;\n    styles.forEach(styleData => {\n      if (isObject(styleData)) {\n        const /** @type {?} */ styleMap = /** @type {?} */(( styleData as ɵStyleData));\n        const /** @type {?} */ easing = styleMap['easing'];\n        if (easing) {\n          collectedEasing = /** @type {?} */(( easing as string));\n          delete styleMap['easing'];\n        }\n      }\n    });\n    return new StyleAst(styles, collectedEasing, metadata.offset);\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nprivate _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const /** @type {?} */ timings = context.currentAnimateTimings;\n    let /** @type {?} */ endTime = context.currentTime;\n    let /** @type {?} */ startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple == 'string') return;\n\n      Object.keys(tuple).forEach(prop => {\n        const /** @type {?} */ collectedStyles = context.collectedStyles[ /** @type {?} */((context.currentQuerySelector))];\n        const /** @type {?} */ collectedEntry = collectedStyles[prop];\n        let /** @type {?} */ updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime &&\n              endTime <= collectedEntry.endTime) {\n            context.errors.push(\n                `The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles[prop] = {startTime, endTime};\n        }\n\n        if (context.options) {\n          validateStyleParams(tuple[prop], context.options, context.errors);\n        }\n      });\n    });\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(metadata: AnimationKeyframesSequenceMetadata, context: AnimationAstBuilderContext):\n      KeyframesAst {\n    if (!context.currentAnimateTimings) {\n      context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n      return new KeyframesAst([]);\n    }\n\n    const /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n\n    let /** @type {?} */ totalKeyframesWithOffsets = 0;\n    const /** @type {?} */ offsets: number[] = [];\n    let /** @type {?} */ offsetsOutOfOrder = false;\n    let /** @type {?} */ keyframesOutOfRange = false;\n    let /** @type {?} */ previousOffset: number = 0;\n\n    const /** @type {?} */ keyframes: StyleAst[] = metadata.steps.map(styles => {\n      const /** @type {?} */ style = this._makeStyleAst(styles, context);\n      let /** @type {?} */ offsetVal: number|null =\n          style.offset != null ? style.offset : consumeOffset(style.styles);\n      let /** @type {?} */ offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(`Please ensure that all keyframe offsets are in order`);\n    }\n\n    const /** @type {?} */ length = metadata.steps.length;\n    let /** @type {?} */ generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const /** @type {?} */ limit = length - 1;\n    const /** @type {?} */ currentTime = context.currentTime;\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n      const /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n    });\n\n    return new KeyframesAst(keyframes);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitReference(metadata: AnimationReferenceMetadata, context: AnimationAstBuilderContext):\n      ReferenceAst {\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ ast = new ReferenceAst(entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(metadata: AnimationAnimateChildMetadata, context: AnimationAstBuilderContext):\n      AnimateChildAst {\n    context.depCount++;\n    const /** @type {?} */ ast = new AnimateChildAst();\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(metadata: AnimationAnimateRefMetadata, context: AnimationAstBuilderContext):\n      AnimateRefAst {\n    const /** @type {?} */ animation = this.visitReference(metadata.animation, context);\n    const /** @type {?} */ ast = new AnimateRefAst(animation);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const /** @type {?} */ parentSelector = /** @type {?} */(( context.currentQuerySelector));\n    const /** @type {?} */ options = /** @type {?} */(( (metadata.options || {}) as AnimationQueryOptions));\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector =\n        parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n    getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    const /** @type {?} */ ast = new QueryAst(selector, options.limit || 0, !!options.optional, includeSelf, entry);\n    ast.originalSelector = metadata.selector;\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStagger(metadata: AnimationStaggerMetadata, context: AnimationAstBuilderContext):\n      StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(`stagger() can only be used inside of query()`);\n    }\n    const /** @type {?} */ timings = metadata.timings === 'full' ?\n        {duration: 0, delay: 0, easing: 'full'} :\n        resolveTiming(metadata.timings, context.errors, true);\n    const /** @type {?} */ animation =\n        visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    return new StaggerAst(timings, animation);\n  }\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  selector = selector.replace(ENTER_TOKEN_REGEX, ENTER_SELECTOR)\n                 .replace(LEAVE_TOKEN_REGEX, LEAVE_SELECTOR)\n                 .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n                 .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n                 .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any}|null {\n  return obj ? copyObj(obj) : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number; endTime: number;\n};\nexport class AnimationAstBuilderContext {\npublic queryCount: number = 0;\npublic depCount: number = 0;\npublic currentTransition: AnimationTransitionMetadata|null = null;\npublic currentQuery: AnimationQueryMetadata|null = null;\npublic currentQuerySelector: string|null = null;\npublic currentAnimateTimings: TimingAst|null = null;\npublic currentTime: number = 0;\npublic collectedStyles: {[selectorName: string]: {[propName: string]: StyleTimeTuple}} = {};\npublic options: AnimationOptions|null = null;\n/**\n * @param {?} errors\n */\nconstructor(public errors: any[]) {}\n}\n\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationAstBuilderContext.prototype.queryCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.depCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTransition;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuery;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuerySelector;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTime;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.collectedStyles;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.options;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.errors;\n}\n\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles: ɵStyleData | string | (ɵStyleData | string)[]): number|null {\n  if (typeof styles == 'string') return null;\n\n  let /** @type {?} */ offset: number|null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n        const /** @type {?} */ obj = /** @type {?} */(( styleTuple as ɵStyleData));\n        offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n        delete obj['offset'];\n      }\n    });\n  } else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n    const /** @type {?} */ obj = /** @type {?} */(( styles as ɵStyleData));\n    offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n    delete obj['offset'];\n  }\n  return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value: any): boolean {\n  return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: any[]) {\n  let /** @type {?} */ timings: AnimateTimings|null = null;\n  if (value.hasOwnProperty('duration')) {\n    timings = /** @type {?} */(( value as AnimateTimings));\n  } else if (typeof value == 'number') {\n    const /** @type {?} */ duration = resolveTiming( /** @type {?} */((value as number)), errors).duration;\n    return new TimingAst( /** @type {?} */((value as number)), 0, '');\n  }\n\n  const /** @type {?} */ strValue = /** @type {?} */(( value as string));\n  const /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    return new DynamicTimingAst(strValue);\n  }\n\n  timings = timings || resolveTiming(strValue, errors);\n  return new TimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = copyObj(options);\n    if (options['params']) {\n      options['params'] = /** @type {?} */(( normalizeParams(options['params'])));\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (fromState: any, toState: any) => boolean;\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(\n    transitionValue: string | TransitionMatcherFn, errors: string[]): TransitionMatcherFn[] {\n  const /** @type {?} */ expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    ( /** @type {?} */((<string>transitionValue)))\n        .split(/\\s*,\\s*/)\n        .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push( /** @type {?} */((<TransitionMatcherFn>transitionValue)));\n  }\n  return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(\n    eventStr: string, expressions: TransitionMatcherFn[], errors: string[]) {\n  if (eventStr[0] == ':') {\n    eventStr = parseAnimationAlias(eventStr, errors);\n  }\n  const /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n    return expressions;\n  }\n\n  const /** @type {?} */ fromState = match[1];\n  const /** @type {?} */ separator = match[2];\n  const /** @type {?} */ toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias: string, errors: string[]): string {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    default:\n      errors.push(`The transition alias value \"${alias}\" is not supported`);\n      return '* => *';\n  }\n}\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  return (fromState: any, toState: any): boolean => {\n    let /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? lhs === 'true' : lhs === 'false';\n    }\n    if (!rhsMatch && typeof toState === 'boolean') {\n      rhsMatch = toState ? rhs === 'true' : rhs === 'false';\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationQueryMetadata, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata} from '@angular/animations';\n\nexport interface AnimationDslVisitor {\n  visitTrigger(ast: AnimationTriggerMetadata, context: any): any;\n  visitState(ast: AnimationStateMetadata, context: any): any;\n  visitTransition(ast: AnimationTransitionMetadata, context: any): any;\n  visitSequence(ast: AnimationSequenceMetadata, context: any): any;\n  visitGroup(ast: AnimationGroupMetadata, context: any): any;\n  visitAnimate(ast: AnimationAnimateMetadata, context: any): any;\n  visitStyle(ast: AnimationStyleMetadata, context: any): any;\n  visitKeyframes(ast: AnimationKeyframesSequenceMetadata, context: any): any;\n  visitReference(ast: AnimationReferenceMetadata, context: any): any;\n  visitAnimateChild(ast: AnimationAnimateChildMetadata, context: any): any;\n  visitAnimateRef(ast: AnimationAnimateRefMetadata, context: any): any;\n  visitQuery(ast: AnimationQueryMetadata, context: any): any;\n  visitStagger(ast: AnimationStaggerMetadata, context: any): any;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitAnimationNode(\n    visitor: AnimationDslVisitor, node: AnimationMetadata, context: any) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger( /** @type {?} */((node as AnimationTriggerMetadata)), context);\n    case AnimationMetadataType.State:\n      return visitor.visitState( /** @type {?} */((node as AnimationStateMetadata)), context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition( /** @type {?} */((node as AnimationTransitionMetadata)), context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence( /** @type {?} */((node as AnimationSequenceMetadata)), context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup( /** @type {?} */((node as AnimationGroupMetadata)), context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate( /** @type {?} */((node as AnimationAnimateMetadata)), context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes( /** @type {?} */((node as AnimationKeyframesSequenceMetadata)), context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle( /** @type {?} */((node as AnimationStyleMetadata)), context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference( /** @type {?} */((node as AnimationReferenceMetadata)), context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild( /** @type {?} */((node as AnimationAnimateChildMetadata)), context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef( /** @type {?} */((node as AnimationAnimateRefMetadata)), context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery( /** @type {?} */((node as AnimationQueryMetadata)), context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger( /** @type {?} */((node as AnimationStaggerMetadata)), context);\n    default:\n      throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nconst /** @type {?} */ EMPTY_ANIMATION_OPTIONS: AnimationOptions = {};\n\nexport interface AstVisitor {\n  visitTrigger(ast: TriggerAst, context: any): any;\n  visitState(ast: StateAst, context: any): any;\n  visitTransition(ast: TransitionAst, context: any): any;\n  visitSequence(ast: SequenceAst, context: any): any;\n  visitGroup(ast: GroupAst, context: any): any;\n  visitAnimate(ast: AnimateAst, context: any): any;\n  visitStyle(ast: StyleAst, context: any): any;\n  visitKeyframes(ast: KeyframesAst, context: any): any;\n  visitReference(ast: ReferenceAst, context: any): any;\n  visitAnimateChild(ast: AnimateChildAst, context: any): any;\n  visitAnimateRef(ast: AnimateRefAst, context: any): any;\n  visitQuery(ast: QueryAst, context: any): any;\n  visitStagger(ast: StaggerAst, context: any): any;\n  visitTiming(ast: TimingAst, context: any): any;\n}\n/**\n * @abstract\n */\nexport abstract class Ast {\n/**\n * @abstract\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisit(ast: AstVisitor, context: any) {}\npublic options: AnimationOptions = EMPTY_ANIMATION_OPTIONS;\n/**\n * @return {?}\n */\nget params(): {[name: string]: any}|null { return this.options['params'] || null; }\n}\n\nfunction Ast_tsickle_Closure_declarations() {\n/** @type {?} */\nAst.prototype.options;\n}\n\nexport class TriggerAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} name\n * @param {?} states\n * @param {?} transitions\n */\nconstructor(public name: string,\npublic states: StateAst[],\npublic transitions: TransitionAst[]) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTrigger(this, context); }\n}\n\nfunction TriggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTriggerAst.prototype.queryCount;\n/** @type {?} */\nTriggerAst.prototype.depCount;\n/** @type {?} */\nTriggerAst.prototype.name;\n/** @type {?} */\nTriggerAst.prototype.states;\n/** @type {?} */\nTriggerAst.prototype.transitions;\n}\n\nexport class StateAst extends Ast {\n/**\n * @param {?} name\n * @param {?} style\n */\nconstructor(public name: string,\npublic style: StyleAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitState(this, context); }\n}\n\nfunction StateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStateAst.prototype.name;\n/** @type {?} */\nStateAst.prototype.style;\n}\n\nexport class TransitionAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} matchers\n * @param {?} animation\n */\nconstructor(\npublic matchers: ((fromState: string, toState: string) => boolean)[],\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTransition(this, context); }\n}\n\nfunction TransitionAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAst.prototype.queryCount;\n/** @type {?} */\nTransitionAst.prototype.depCount;\n/** @type {?} */\nTransitionAst.prototype.matchers;\n/** @type {?} */\nTransitionAst.prototype.animation;\n}\n\nexport class SequenceAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitSequence(this, context); }\n}\n\nfunction SequenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nSequenceAst.prototype.steps;\n}\n\nexport class GroupAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitGroup(this, context); }\n}\n\nfunction GroupAst_tsickle_Closure_declarations() {\n/** @type {?} */\nGroupAst.prototype.steps;\n}\n\nexport class AnimateAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} style\n */\nconstructor(public timings: TimingAst,\npublic style: StyleAst|KeyframesAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimate(this, context); }\n}\n\nfunction AnimateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateAst.prototype.timings;\n/** @type {?} */\nAnimateAst.prototype.style;\n}\n\nexport class StyleAst extends Ast {\npublic isEmptyStep = false;\n/**\n * @param {?} styles\n * @param {?} easing\n * @param {?} offset\n */\nconstructor(\npublic styles: (ɵStyleData|string)[],\npublic easing: string|null,\npublic offset: number|null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStyle(this, context); }\n}\n\nfunction StyleAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStyleAst.prototype.isEmptyStep;\n/** @type {?} */\nStyleAst.prototype.styles;\n/** @type {?} */\nStyleAst.prototype.easing;\n/** @type {?} */\nStyleAst.prototype.offset;\n}\n\nexport class KeyframesAst extends Ast {\n/**\n * @param {?} styles\n */\nconstructor(public styles: StyleAst[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitKeyframes(this, context); }\n}\n\nfunction KeyframesAst_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyframesAst.prototype.styles;\n}\n\nexport class ReferenceAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitReference(this, context); }\n}\n\nfunction ReferenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nReferenceAst.prototype.animation;\n}\n\nexport class AnimateChildAst extends Ast {\nconstructor() { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateChild(this, context); }\n}\nexport class AnimateRefAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: ReferenceAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateRef(this, context); }\n}\n\nfunction AnimateRefAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateRefAst.prototype.animation;\n}\n\nexport class QueryAst extends Ast {\npublic originalSelector: string;\n/**\n * @param {?} selector\n * @param {?} limit\n * @param {?} optional\n * @param {?} includeSelf\n * @param {?} animation\n */\nconstructor(\npublic selector: string,\npublic limit: number,\npublic optional: boolean,\npublic includeSelf: boolean,\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitQuery(this, context); }\n}\n\nfunction QueryAst_tsickle_Closure_declarations() {\n/** @type {?} */\nQueryAst.prototype.originalSelector;\n/** @type {?} */\nQueryAst.prototype.selector;\n/** @type {?} */\nQueryAst.prototype.limit;\n/** @type {?} */\nQueryAst.prototype.optional;\n/** @type {?} */\nQueryAst.prototype.includeSelf;\n/** @type {?} */\nQueryAst.prototype.animation;\n}\n\nexport class StaggerAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} animation\n */\nconstructor(public timings: AnimateTimings,\npublic animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStagger(this, context); }\n}\n\nfunction StaggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStaggerAst.prototype.timings;\n/** @type {?} */\nStaggerAst.prototype.animation;\n}\n\nexport class TimingAst extends Ast {\n/**\n * @param {?} duration\n * @param {?=} delay\n * @param {?=} easing\n */\nconstructor(\npublic duration: number,\npublic delay: number = 0,\npublic easing: string|null = null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction TimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTimingAst.prototype.duration;\n/** @type {?} */\nTimingAst.prototype.delay;\n/** @type {?} */\nTimingAst.prototype.easing;\n}\n\nexport class DynamicTimingAst extends TimingAst {\n/**\n * @param {?} value\n */\nconstructor(public value: string) { super(0, 0, ''); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction DynamicTimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nDynamicTimingAst.prototype.value;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationMetadata, AnimationOptions, sequence, ɵStyleData} from '@angular/animations';\n\nexport const /** @type {?} */ ONE_SECOND = 1000;\n\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const /** @type {?} */ matches = ( /** @type {?} */((value as string))).match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:  // ms or something else\n      return value;\n  }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(\n    timings: string | number | AnimateTimings, errors: any[], allowNegativeValues?: boolean) {\n  return timings.hasOwnProperty('duration') ? /** @type {?} */((\n      <AnimateTimings>timings)) :\n      parseTimeExpression( /** @type {?} */((<string|number>timings)), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(\n    exp: string | number, errors: string[], allowNegativeValues?: boolean): AnimateTimings {\n  const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let /** @type {?} */ duration: number;\n  let /** @type {?} */ delay: number = 0;\n  let /** @type {?} */ easing: string = '';\n  if (typeof exp === 'string') {\n    const /** @type {?} */ matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(`The provided timing value \"${exp}\" is invalid.`);\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const /** @type {?} */ delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n    }\n\n    const /** @type {?} */ easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = /** @type {?} */(( <number>exp));\n  }\n\n  if (!allowNegativeValues) {\n    let /** @type {?} */ containsErrors = false;\n    let /** @type {?} */ startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(`Duration values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(`Delay values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n    }\n  }\n\n  return {duration, delay, easing};\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(\n    obj: {[key: string]: any}, destination: {[key: string]: any} = {}): {[key: string]: any} {\n  Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n  return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles: ɵStyleData | ɵStyleData[]): ɵStyleData {\n  const /** @type {?} */ normalizedStyles: ɵStyleData = {};\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, false, normalizedStyles));\n  } else {\n    copyStyles(styles, false, normalizedStyles);\n  }\n  return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(\n    styles: ɵStyleData, readPrototype: boolean, destination: ɵStyleData = {}): ɵStyleData {\n  if (readPrototype) {\n    // we make use of a for-in loop so that the\n    // prototypically inherited properties are\n    // revealed from the backFill map\n    for (let /** @type {?} */ prop in styles) {\n      destination[prop] = styles[prop];\n    }\n  } else {\n    copyObj(styles, destination);\n  }\n  return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = styles[prop];\n    });\n  }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    });\n  }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps: AnimationMetadata | AnimationMetadata[]):\n    AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return /** @type {?} */(( steps as AnimationMetadata));\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(\n    value: string | number, options: AnimationOptions, errors: any[]) {\n  const /** @type {?} */ params = options.params || {};\n  if (typeof value !== 'string') return;\n\n  const /** @type {?} */ matches = value.toString().match(PARAM_REGEX);\n  if (matches) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(\n            `Unable to resolve the local animation param ${varName} in the given list of values`);\n      }\n    });\n  }\n}\n\nconst /** @type {?} */ PARAM_REGEX = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(\n    value: string | number, params: {[name: string]: any}, errors: any[]): string|number {\n  const /** @type {?} */ original = value.toString();\n  const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n    let /** @type {?} */ localVal = params[varName];\n    // this means that the value was never overidden by the data passed in by the user\n    if (!params.hasOwnProperty(varName)) {\n      errors.push(`Please provide a value for the animation param ${varName}`);\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator: any): any[] {\n  const /** @type {?} */ arr: any[] = [];\n  let /** @type {?} */ item = iterator.next();\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n  return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(\n    source: AnimationOptions, destination: AnimationOptions): AnimationOptions {\n  if (source.params) {\n    const /** @type {?} */ p0 = source.params;\n    if (!destination.params) {\n      destination.params = {};\n    }\n    const /** @type {?} */ p1 = destination.params;\n    Object.keys(p0).forEach(param => {\n      if (!p1.hasOwnProperty(param)) {\n        p1[param] = p0[param];\n      }\n    });\n  }\n  return destination;\n}\n\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\n\nimport {containsElement, invokeQuery, matchesElement} from './shared';\n\n\n/**\n * @experimental\n */\nexport class NoopAnimationDriver implements AnimationDriver {\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): AnimationPlayer {\n    return new NoopAnimationPlayer();\n  }\n}\n\n/**\n * @experimental\n */\nexport abstract class AnimationDriver {\n  static NOOP: AnimationDriver = new NoopAnimationDriver();\n\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing?: string|null, previousPlayers?: any[]): any;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n"], "names": ["PRE_STYLE", "style"], "mappings": ";;AwBAA;;;;;;;AAOA,AAKA,AAAA,SAAA,mBAAA,CAAoC,OAA0B,EAA9D;IACE,QAAQ,OAAO,CAAC,MAAM;QACpB,KAAK,CAAC;YACJ,OAAO,IAAI,mBAAmB,EAAE,CAAC;QACnC,KAAK,CAAC;YACJ,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB;YACE,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;KAC7C;CACF;AAED,AAAA,SAAA,kBAAA,CACI,MAAuB,EAAE,UAAoC,EAAE,OAAY,EAC3E,SAAuB,EAAE,SAF7B,GAEqD,EAAE,EACnD,UAHJ,GAG6B,EAAE,EAH/B;IAIE,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,mBAAmB,GAAiB,EAAE,CAAC;IAC7C,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,gBAAgB,GAAoB,IAAI,CAAC;IAC7C,SAAS,CAAC,OAAO,CAAC,EAAE,IAAtB;QACI,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAW,CAAC;QACtC,MAAM,YAAY,GAAG,MAAM,IAAI,cAAc,CAAC;QAC9C,MAAM,kBAAkB,GAAe,CAAC,YAAY,IAAI,gBAAgB,KAAK,EAAE,CAAC;QAChF,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAAhC;YACM,IAAI,cAAc,GAAG,IAAI,CAAC;YAC1B,IAAI,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,eAAe,IAAIA,UAAS,EAAE;gBAChC,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;aACnC;iBAAM,IAAI,eAAe,IAAI,UAAU,EAAE;gBACxC,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;aACpC;iBAAM,IAAI,IAAI,IAAI,QAAQ,EAAE;gBAC3B,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAChE,eAAe,GAAG,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;aAC1F;YACD,kBAAkB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;SACtD,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACjB,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC9C;QACD,gBAAgB,GAAG,kBAAkB,CAAC;QACtC,cAAc,GAAG,MAAM,CAAC;KACzB,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,MAAM,UAAU,GAAG,OAAO,CAAC;QAC3B,MAAM,IAAI,KAAK,CACX,CADR,8CAAA,EACyD,UAAU,CADnE,EACsE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAD7F,CAC+F,CAAC,CAAC;KAC9F;IAED,OAAO,mBAAmB,CAAC;CAC5B;AAED,AAAA,SAAA,cAAA,CACI,MAAuB,EAAE,SAAiB,EAAE,KAAiC,EAC7E,QAA6B,EAFjC;IAGE,QAAQ,SAAS;QACf,KAAK,OAAO;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM;QACR,KAAK,MAAM;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5F,MAAM;QACR,KAAK,SAAS;YACZ,MAAM,CAAC,SAAS,CACZ,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACrF,MAAM;KACT;CACF;AAED,AAAA,SAAA,kBAAA,CACI,CAAiB,EAAE,SAAkB,EAAE,SAAkB,EAD7D;IAEE,MAAM,KAAK,GAAG,kBAAkB,CAC5B,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,SAAS,EAC1E,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;IACtD,MAAM,IAAI,GAAI,CAAS,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,IAAI,IAAI,IAAI,EAAE;QACf,KAAa,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;KAChC;IACD,OAAO,KAAK,CAAC;CACd;AAED,AAAA,SAAA,kBAAA,CACI,OAAY,EAAE,WAAmB,EAAE,SAAiB,EAAE,OAAe,EAAE,SAD3E,GAC+F,EAAE,EAC7F,SAFJ,GAEwB,CAAC,EAFzB;IAGE,OAAO,EAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC;CACzE;AAED,AAAA,SAAA,eAAA,CACI,GAAwC,EAAE,GAAQ,EAAE,YAAiB,EADzE;IAEE,IAAI,KAAU,CAAC;IACf,IAAI,GAAG,YAAY,GAAG,EAAE;QACtB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE;YACV,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;SACpC;KACF;SAAM;QACL,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;SACjC;KACF;IACD,OAAO,KAAK,CAAC;CACd;AAED,AAAA,SAAA,oBAAA,CAAqC,OAAe,EAApD;IACE,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAChD,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CACrB;AAED,IAAI,SAAS,GAAsC,CAAC,IAAS,EAAE,IAAS,KAAK,KAAK,CAAC;AACnF,IAAI,QAAQ,GAAgD,CAAC,OAAY,EAAE,QAAgB,KACvF,KAAK,CAAC;AACV,IAAI,MAAM,GACN,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,KADnD;IAEM,OAAO,EAAE,CAAC;CACX,CAAC;AAEN,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAEjC,SAAS,GAAG,CAAC,IAAS,EAAE,IAAS,KAAnC,EAA0C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAY,CAAC,EAAE,CAAC;IAEjF,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC7B,QAAQ,GAAG,CAAC,OAAY,EAAE,QAAgB,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1E;SAAM;QACL,MAAM,KAAK,GAAG,OAAO,CAAC,SAAgB,CAAC;QACvC,MAAM,EAAE,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACnF,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,EAAE,EAAE;YACN,QAAQ,GAAG,CAAC,OAAY,EAAE,QAAgB,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9E;KACF;IAED,MAAM,GAAG,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,KAA1D;QACI,IAAI,OAAO,GAAU,EAAE,CAAC;QACxB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACrD;aAAM;YACL,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,GAAG,EAAE;gBACP,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnB;SACF;QACD,OAAO,OAAO,CAAC;KAChB,CAAC;CACH;AAED,AAAO,MAAM,cAAc,GAAG,QAAQ,CAAC;AACvC,AAAO,MAAM,eAAe,GAAG,SAAS,CAAC;AACzC,AAAO,MAAM,WAAW,GAAG,MAAM,CAAC;;ADjKlC;;;;;;;AAOA,AAEA,AAGA;;;AAGA,AAAA,MAAA,mBAAA,CAAA;IACE,cAAc,CAAC,OAAY,EAAE,QAAgB,EAA/C;QACI,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC1C;IAED,eAAe,CAAC,IAAS,EAAE,IAAS,EAAtC,EAAmD,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;IAEtF,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAtD;QACI,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;IAED,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAhE;QACI,OAAO,YAAY,IAAI,EAAE,CAAC;KAC3B;IAED,OAAO,CACH,OAAY,EAAE,SAA6C,EAAE,QAAgB,EAAE,KAAa,EAC5F,MAAc,EAAE,eAFtB,GAE+C,EAAE,EAFjD;QAGI,OAAO,IAAI,mBAAmB,EAAE,CAAC;KAClC;CACF;;;;AAKD,AAAA,MAAA,eAAA,CAAA;;AACS,eAAT,CAAA,IAAa,GAAoB,IAAI,mBAAmB,EAAE,CAAC;;ADzC3D;;;;;;;AAQA,AAEA,AADO,MAAM,UAAA,GAAa,IAAA,CAAK;AAG/B,AADO,MAAM,eAAA,GAAkB,UAAA,CAAW;AAE1C,AADO,MAAM,eAAA,GAAkB,UAAA,CAAW;AAE1C,AADO,MAAM,cAAA,GAAiB,WAAA,CAAY;AAE1C,AADO,MAAM,cAAA,GAAiB,WAAA,CAAY;AAE1C,AADO,MAAM,oBAAA,GAAuB,YAAA,CAAa;AAEjD,AADO,MAAM,mBAAA,GAAsB,aAAA,CAAc;AAEjD,AADO,MAAM,sBAAA,GAAyB,cAAA,CAAe;AAErD,AADO,MAAM,qBAAA,GAAwB,eAAA,CAAgB;;;;;AAMrD,AAAA,SAAA,kBAAA,CAJC,KAAA,EAID;IACE,IAAI,OAJO,KAAA,IAAS,QAAA;QAAU,OAAO,KAAA,CAAM;IAM3C,uBAJM,OAAA,GAAU,EAAA,KAAU,GAAQ,KAAC,CAAK,mBAAC,CAAmB,CAAC;IAK7D,IAAI,CAJC,OAAC,IAAU,OAAA,CAAQ,MAAC,GAAQ,CAAA;QAAG,OAAO,CAAA,CAAE;IAM7C,OAJO,qBAAA,CAAsB,UAAC,CAAU,OAAC,CAAO,CAAC,CAAC,CAAC,EAAE,OAAA,CAAQ,CAAC,CAAC,CAAC,CAAC;CAKlE;;;;;;AAMD,SAAA,qBAAA,CARC,KAAA,EAAA,IAAA,EAQD;IACE,QAAQ,IARC;QASP,KARK,GAAA;YASH,OARO,KAAA,GAAQ,UAAA,CAAW;QAS5B;YACE,OARO,KAAA,CAAM;KAShB;CACF;;;;;;;AAOD,AAAA,SAAA,aAAA,CACI,OAAyC,EAAE,MAAa,EAAE,mBAA6B,EAD3F;IAEE,OAbO,OAAA,CAAQ,cAAC,CAAc,UAAC,CAAU,IACrB,OAAC;QAcjB,mBAAmB,mBAbgB,OAAC,GAAQ,MAAA,EAAQ,mBAAA,CAAoB,CAAC;CAc9E;;;;;;;AAOD,SAAA,mBAAA,CACI,GAAoB,EAAE,MAAgB,EAAE,mBAA6B,EADzE;IAEE,uBAlBM,KAAA,GAAQ,0EAAA,CAA2E;IAmBzF,qBAlBI,QAAU,CAAO;IAmBrB,qBAlBI,KAAA,GAAgB,CAAA,CAAE;IAmBtB,qBAlBI,MAAA,GAAiB,EAAA,CAAG;IAmBxB,IAAI,OAlBO,GAAA,KAAQ,QAAA,EAAU;QAmB3B,uBAlBM,OAAA,GAAU,GAAA,CAAI,KAAC,CAAK,KAAC,CAAK,CAAC;QAmBjC,IAAI,OAlBC,KAAW,IAAA,EAAM;YAmBpB,MAAM,CAlBC,IAAC,CAAI,CAkBlB,2BAAA,EAlBmB,GAA8B,CAkBjD,aAAA,CAlBoD,CAAe,CAAC;YAmB9D,OAlBO,EAAA,QAAE,EAAS,CAAA,EAAG,KAAA,EAAO,CAAA,EAAG,MAAA,EAAQ,EAAA,EAAG,CAAC;SAmB5C;QAED,QAAQ,GAlBG,qBAAA,CAAsB,UAAC,CAAU,OAAC,CAAO,CAAC,CAAC,CAAC,EAAE,OAAA,CAAQ,CAAC,CAAC,CAAC,CAAC;QAoBrE,uBAlBM,UAAA,GAAa,OAAA,CAAQ,CAAC,CAAC,CAAC;QAmB9B,IAAI,UAlBC,IAAa,IAAA,EAAM;YAmBtB,KAAK,GAlBG,qBAAA,CAAsB,IAAC,CAAI,KAAC,CAAK,UAAC,CAAU,UAAC,CAAU,CAAC,EAAE,OAAA,CAAQ,CAAC,CAAC,CAAC,CAAC;SAmB/E;QAED,uBAlBM,SAAA,GAAY,OAAA,CAAQ,CAAC,CAAC,CAAC;QAmB7B,IAAI,SAlBC,EAAU;YAmBb,MAAM,GAlBG,SAAA,CAAU;SAmBpB;KACF;SAlBM;QAmBL,QAAQ,IAlBW,GAAC,CAAA,CAAG;KAmBxB;IAED,IAAI,CAlBC,mBAAC,EAAoB;QAmBxB,qBAlBI,cAAA,GAAiB,KAAA,CAAM;QAmB3B,qBAlBI,UAAA,GAAa,MAAA,CAAO,MAAC,CAAM;QAmB/B,IAAI,QAlBC,GAAU,CAAA,EAAG;YAmBhB,MAAM,CAlBC,IAAC,CAAI,CAkBlB,gEAAA,CAlBmB,CAAkE,CAAC;YAmBhF,cAAc,GAlBG,IAAA,CAAK;SAmBvB;QACD,IAAI,KAlBC,GAAO,CAAA,EAAG;YAmBb,MAAM,CAlBC,IAAC,CAAI,CAkBlB,6DAAA,CAlBmB,CAA+D,CAAC;YAmB7E,cAAc,GAlBG,IAAA,CAAK;SAmBvB;QACD,IAAI,cAlBC,EAAe;YAmBlB,MAAM,CAlBC,MAAC,CAAM,UAAC,EAAW,CAAA,EAAG,CAkBnC,2BAAA,EAlBmC,GAA+B,CAkBlE,aAAA,CAlBqE,CAAe,CAAC;SAmBhF;KACF;IAED,OAlBO,EAAA,QAAE,EAAS,KAAA,EAAO,MAAA,EAAO,CAAC;CAmBlC;;;;;;AAMD,AAAA,SAAA,OAAA,CACI,GAAyB,EAAE,WAD/B,GACmE,EAAE,EADrE;IAEE,MAAM,CAtBC,IAAC,CAAI,GAAC,CAAG,CAAC,OAAC,CAAO,IAAC,IAsB5B,EAtBqC,WAAA,CAAY,IAAC,CAAI,GAAG,GAAA,CAAI,IAAC,CAAI,CAAC,EAAC,CAAE,CAAC;IAuBrE,OAtBO,WAAA,CAAY;CAuBpB;;;;;AAKD,AAAA,SAAA,eAAA,CAzBC,MAAA,EAyBD;IACE,uBAzBM,gBAAA,GAA+B,EAAA,CAAG;IA0BxC,IAAI,KAzBC,CAAK,OAAC,CAAO,MAAC,CAAM,EAAE;QA0BzB,MAAM,CAzBC,OAAC,CAAO,IAAC,IAAO,UAAA,CAAW,IAAC,EAAK,KAAA,EAAO,gBAAA,CAAiB,CAAC,CAAC;KA0BnE;SAzBM;QA0BL,UAAU,CAzBC,MAAC,EAAO,KAAA,EAAO,gBAAA,CAAiB,CAAC;KA0B7C;IACD,OAzBO,gBAAA,CAAiB;CA0BzB;;;;;;;AAOD,AAAA,SAAA,UAAA,CACI,MAAkB,EAAE,aAAsB,EAAE,WADhD,GAC0E,EAAE,EAD5E;IAEE,IAAI,aA9BC,EAAc;;;;QAkCjB,KAAK,qBA9BI,IAAA,IAAQ,MAAA,EAAQ;YA+BvB,WAAW,CA9BC,IAAC,CAAI,GAAG,MAAA,CAAO,IAAC,CAAI,CAAC;SA+BlC;KACF;SA9BM;QA+BL,OAAO,CA9BC,MAAC,EAAO,WAAA,CAAY,CAAC;KA+B9B;IACD,OA9BO,WAAA,CAAY;CA+BpB;;;;;;AAMD,AAAA,SAAA,SAAA,CAlCC,OAAA,EAAA,MAAA,EAkCD;IACE,IAAI,OAlCC,CAAO,OAAC,CAAO,EAAE;QAmCpB,MAAM,CAlCC,IAAC,CAAI,MAAC,CAAM,CAAC,OAAC,CAAO,IAAC,IAkCjC;YACM,uBAlCM,SAAA,GAAY,mBAAA,CAAoB,IAAC,CAAI,CAAC;YAmC5C,OAAO,CAlCC,KAAC,CAAK,SAAC,CAAS,GAAG,MAAA,CAAO,IAAC,CAAI,CAAC;SAmCzC,CAlCC,CAAC;KAmCJ;CACF;;;;;;AAMD,AAAA,SAAA,WAAA,CAtCC,OAAA,EAAA,MAAA,EAsCD;IACE,IAAI,OAtCC,CAAO,OAAC,CAAO,EAAE;QAuCpB,MAAM,CAtCC,IAAC,CAAI,MAAC,CAAM,CAAC,OAAC,CAAO,IAAC,IAsCjC;YACM,uBAtCM,SAAA,GAAY,mBAAA,CAAoB,IAAC,CAAI,CAAC;YAuC5C,OAAO,CAtCC,KAAC,CAAK,SAAC,CAAS,GAAG,EAAA,CAAG;SAuC/B,CAtCC,CAAC;KAuCJ;CACF;;;;;AAKD,AAAA,SAAA,uBAAA,CAzCC,KAAA,EAyCD;IAEE,IAAI,KAzCC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;QA0CxB,IAAI,KAzCC,CAAK,MAAC,IAAS,CAAA;YAAG,OAAO,KAAA,CAAM,CAAC,CAAC,CAAC;QA0CvC,OAzCO,QAAA,CAAS,KAAC,CAAK,CAAC;KA0CxB;IACD,QAzCO,KAAS,EAAkB;CA0CnC;;;;;;;AAOD,AAAA,SAAA,mBAAA,CACI,KAAsB,EAAE,OAAyB,EAAE,MAAa,EADpE;IAEE,uBA9CM,MAAA,GAAS,OAAA,CAAQ,MAAC,IAAS,EAAA,CAAG;IA+CpC,IAAI,OA9CO,KAAA,KAAU,QAAA;QAAU,OAAA;IAgD/B,uBA9CM,OAAA,GAAU,KAAA,CAAM,QAAC,EAAQ,CAAE,KAAC,CAAK,WAAC,CAAW,CAAC;IA+CpD,IAAI,OA9CC,EAAQ;QA+CX,OAAO,CA9CC,OAAC,CAAO,OAAC,IA8CrB;YACM,IAAI,CA9CC,MAAC,CAAM,cAAC,CAAc,OAAC,CAAO,EAAE;gBA+CnC,MAAM,CA9CC,IAAC,CA+CJ,CADZ,4CAAA,EAC2D,OA9CC,CA6C5D,4BAAA,CA7CmE,CAA8B,CAAC;aA+C3F;SACF,CA9CC,CAAC;KA+CJ;CACF;AAED,MA9CM,WAAA,GAAc,sBAAA,CAAuB;;;;;;;AAqD3C,AAAA,SAAA,iBAAA,CACI,KAAsB,EAAE,MAA6B,EAAE,MAAa,EADxE;IAEE,uBApDM,QAAA,GAAW,KAAA,CAAM,QAAC,EAAQ,CAAE;IAqDlC,uBApDM,GAAA,GAAM,QAAA,CAAS,OAAC,CAAO,WAAC,EAAY,CAAA,CAAE,EAAE,OAAA,KAoDhD;QACI,qBApDI,QAAA,GAAW,MAAA,CAAO,OAAC,CAAO,CAAC;;QAsD/B,IAAI,CApDC,MAAC,CAAM,cAAC,CAAc,OAAC,CAAO,EAAE;YAqDnC,MAAM,CApDC,IAAC,CAAI,CAoDlB,+CAAA,EApDmB,OAAkD,CAoDrE,CApD4E,CAAE,CAAC;YAqDzE,QAAQ,GApDG,EAAA,CAAG;SAqDf;QACD,OApDO,QAAA,CAAS,QAAC,EAAQ,CAAE;KAqD5B,CApDC,CAAC;;IAuDH,OApDO,GAAA,IAAO,QAAA,GAAW,KAAA,GAAQ,GAAA,CAAI;CAqDtC;;;;;AAKD,AAAA,SAAA,eAAA,CAvDC,QAAA,EAuDD;IACE,uBAvDM,GAAA,GAAa,EAAA,CAAG;IAwDtB,qBAvDI,IAAA,GAAO,QAAA,CAAS,IAAC,EAAI,CAAE;IAwD3B,OAAO,CAvDC,IAAC,CAAI,IAAC,EAAK;QAwDjB,GAAG,CAvDC,IAAC,CAAI,IAAC,CAAI,KAAC,CAAK,CAAC;QAwDrB,IAAI,GAvDG,QAAA,CAAS,IAAC,EAAI,CAAE;KAwDxB;IACD,OAvDO,GAAA,CAAI;CAwDZ;;;;;;AAMD,AAAA,SAAA,qBAAA,CACI,MAAwB,EAAE,WAA6B,EAD3D;IAEE,IAAI,MA3DC,CAAM,MAAC,EAAO;QA4DjB,uBA3DM,EAAA,GAAK,MAAA,CAAO,MAAC,CAAM;QA4DzB,IAAI,CA3DC,WAAC,CAAW,MAAC,EAAO;YA4DvB,WAAW,CA3DC,MAAC,GAAQ,EAAA,CAAG;SA4DzB;QACD,uBA3DM,EAAA,GAAK,WAAA,CAAY,MAAC,CAAM;QA4D9B,MAAM,CA3DC,IAAC,CAAI,EAAC,CAAE,CAAC,OAAC,CAAO,KAAC,IA2D7B;YACM,IAAI,CA3DC,EAAC,CAAE,cAAC,CAAc,KAAC,CAAK,EAAE;gBA4D7B,EAAE,CA3DC,KAAC,CAAK,GAAG,EAAA,CAAG,KAAC,CAAK,CAAC;aA4DvB;SACF,CA3DC,CAAC;KA4DJ;IACD,OA3DO,WAAA,CAAY;CA4DpB;AAED,MA3DM,gBAAA,GAAmB,eAAA,CAAgB;;;;;AAgEzC,AAAA,SAAA,mBAAA,CA/DC,KAAA,EA+DD;IACE,OA/DO,KAAA,CAAM,OAAC,CAAO,gBAAC,EAAiB,CAAA,GAAE,CAAS,KAAM,CAAA,CAAE,CAAC,CAAC,CAAC,WAAC,EAAW,CAAE,CAAC;CAgE7E;;ADrRD;;;;;;;AAUA,MADM,uBAAA,GAA4C,EAAA,CAAG;;;;AAsBrD,AAAA,MAAA,GAAA,CAAA;IAAA,WAAA,GAAA;QADS,IAAT,CAAA,OAAS,GAA4B,uBAAA,CAAwB;KAc5D;;;;;;;IAND,KATY,CAAA,GAAA,EAAA,OAAA,EASZ,GATY;;;;IAcZ,IAXG,MAAA,GAWH,EAX6C,OAAO,IAAA,CAAK,OAAC,CAAO,QAAC,CAAQ,IAAI,IAAA,CAAK,EAAC;CAYnF;AAED,AAKA,AAAA,MAAA,UAhBC,SAAA,GAAA,CAgBD;;;;;;IAQA,WAAA,CApBqB,IAAM,EAAe,MAAiB,EAAU,WAA2B,EAoBhG;QAGI,KAAK,EAAE,CAAC;QAvBS,IAArB,CAAA,IAAqB,GAAA,IAAA,CAAM;QAAe,IAA1C,CAAA,MAA0C,GAAA,MAAA,CAAiB;QAAU,IAArE,CAAA,WAAqE,GAAA,WAAA,CAA2B;QAHvF,IAAT,CAAA,UAAS,GAAqB,CAAA,CAAE;QACvB,IAAT,CAAA,QAAS,GAAmB,CAAA,CAAE;KA0B3B;;;;;;IAMH,KA1BG,CAAA,OAAA,EAAA,OAAA,EA0BH,EA1BkD,OAAO,OAAA,CAAQ,YAAC,CAAY,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA2B7F;AAED,AAaA,AAAA,MAAA,QAvCC,SAAA,GAAA,CAuCD;;;;;IAKA,WAAA,CA3CqB,IAAM,EAAeC,QAAO,EA2CjD;QA3C0D,KAAA,EAAA,CAAA;QAArC,IAArB,CAAA,IAAqB,GAAA,IAAA,CAAM;QAAe,IAA1C,CAAA,KAA0C,GAAAA,QAAA,CAAO;KAAS;;;;;;IAkD1D,KAhDG,CAAA,OAAA,EAAA,OAAA,EAgDH,EAhDkD,OAAO,OAAA,CAAQ,UAAC,CAAU,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAiD3F;AAED,AAOA,AAAA,MAAA,aAvDC,SAAA,GAAA,CAuDD;;;;;IAOA,WAAA,CAzDa,QAA4D,EAAU,SAAW,EAyD9F;QAGI,KAAK,EAAE,CAAC;QA5DC,IAAb,CAAA,QAAa,GAAA,QAAA,CAA4D;QAAU,IAAnF,CAAA,SAAmF,GAAA,SAAA,CAAW;QAJrF,IAAT,CAAA,UAAS,GAAqB,CAAA,CAAE;QACvB,IAAT,CAAA,QAAS,GAAmB,CAAA,CAAE;KAgE3B;;;;;;IAMH,KA/DG,CAAA,OAAA,EAAA,OAAA,EA+DH,EA/DkD,OAAO,OAAA,CAAQ,eAAC,CAAe,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAgEhG;AAED,AAWA,AAAA,MAAA,WA1EC,SAAA,GAAA,CA0ED;;;;IAIA,WAAA,CA7EqB,KAAW,EA6EhC;QA7EkC,KAAA,EAAA,CAAA;QAAb,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAW;KAAE;;;;;;IAmFlC,KAjFG,CAAA,OAAA,EAAA,OAAA,EAiFH,EAjFkD,OAAO,OAAA,CAAQ,aAAC,CAAa,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAkF9F;AAED,AAKA,AAAA,MAAA,QAtFC,SAAA,GAAA,CAsFD;;;;IAIA,WAAA,CAzFqB,KAAW,EAyFhC;QAzFkC,KAAA,EAAA,CAAA;QAAb,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAW;KAAE;;;;;;IA+FlC,KA7FG,CAAA,OAAA,EAAA,OAAA,EA6FH,EA7FkD,OAAO,OAAA,CAAQ,UAAC,CAAU,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA8F3F;AAED,AAKA,AAAA,MAAA,UAlGC,SAAA,GAAA,CAkGD;;;;;IAKA,WAAA,CAtGqB,OAAS,EAAkBA,QAAiB,EAsGjE;QAtG6E,KAAA,EAAA,CAAA;QAAxD,IAArB,CAAA,OAAqB,GAAA,OAAA,CAAS;QAAkB,IAAhD,CAAA,KAAgD,GAAAA,QAAA,CAAiB;KAAY;;;;;;IA6G7E,KA3GG,CAAA,OAAA,EAAA,OAAA,EA2GH,EA3GkD,OAAO,OAAA,CAAQ,YAAC,CAAY,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA4G7F;AAED,AAOA,AAAA,MAAA,QAlHC,SAAA,GAAA,CAkHD;;;;;;IAOA,WAAA,CArHa,MAA4B,EAAU,MAAgB,EACtD,MAAgB,EAoH7B;QAII,KAAK,EAAE,CAAC;QAzHC,IAAb,CAAA,MAAa,GAAA,MAAA,CAA4B;QAAU,IAAnD,CAAA,MAAmD,GAAA,MAAA,CAAgB;QACtD,IAAb,CAAA,MAAa,GAAA,MAAA,CAAgB;QAJpB,IAAT,CAAA,WAAS,GAAc,KAAA,CAAM;KA6H1B;;;;;;IAMH,KA3HG,CAAA,OAAA,EAAA,OAAA,EA2HH,EA3HkD,OAAO,OAAA,CAAQ,UAAC,CAAU,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA4H3F;AAED,AAWA,AAAA,MAAA,YAtIC,SAAA,GAAA,CAsID;;;;IAIA,WAAA,CAzIqB,MAAiB,EAyItC;QAzIwC,KAAA,EAAA,CAAA;QAAnB,IAArB,CAAA,MAAqB,GAAA,MAAA,CAAiB;KAAE;;;;;;IA+IxC,KA7IG,CAAA,OAAA,EAAA,OAAA,EA6IH,EA7IkD,OAAO,OAAA,CAAQ,cAAC,CAAc,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA8I/F;AAED,AAKA,AAAA,MAAA,YAlJC,SAAA,GAAA,CAkJD;;;;IAIA,WAAA,CArJqB,SAAW,EAqJhC;QArJoC,KAAA,EAAA,CAAA;QAAf,IAArB,CAAA,SAAqB,GAAA,SAAA,CAAW;KAAI;;;;;;IA2JpC,KAzJG,CAAA,OAAA,EAAA,OAAA,EAyJH,EAzJkD,OAAO,OAAA,CAAQ,cAAC,CAAc,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA0J/F;AAED,AAKA,AAAA,MAAA,eA9JC,SAAA,GAAA,CA8JD;IACA,WAAA,GAAA,EA9JG,KAAA,EAAA,CAAA,EAAA;;;;;;IAoKH,KAlKG,CAAA,OAAA,EAAA,OAAA,EAkKH,EAlKkD,OAAO,OAAA,CAAQ,iBAAC,CAAiB,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAmKlG;AACD,AAAA,MAAA,aAjKC,SAAA,GAAA,CAiKD;;;;IAIA,WAAA,CApKqB,SAAW,EAoKhC;QApK6C,KAAA,EAAA,CAAA;QAAxB,IAArB,CAAA,SAAqB,GAAA,SAAA,CAAW;KAAa;;;;;;IA0K7C,KAxKG,CAAA,OAAA,EAAA,OAAA,EAwKH,EAxKkD,OAAO,OAAA,CAAQ,eAAC,CAAe,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAyKhG;AAED,AAKA,AAAA,MAAA,QA7KC,SAAA,GAAA,CA6KD;;;;;;;;IASA,WAAA,CAlLa,QAAU,EAAe,KAAO,EAAe,QAAU,EACzD,WAAa,EAAgB,SAAW,EAiLrD;QAMI,KAAK,EAAE,CAAC;QAxLC,IAAb,CAAA,QAAa,GAAA,QAAA,CAAU;QAAe,IAAtC,CAAA,KAAsC,GAAA,KAAA,CAAO;QAAe,IAA5D,CAAA,QAA4D,GAAA,QAAA,CAAU;QACzD,IAAb,CAAA,WAAa,GAAA,WAAA,CAAa;QAAgB,IAA1C,CAAA,SAA0C,GAAA,SAAA,CAAW;KAwLlD;;;;;;IAMH,KA1LG,CAAA,OAAA,EAAA,OAAA,EA0LH,EA1LkD,OAAO,OAAA,CAAQ,UAAC,CAAU,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CA2L3F;AAED,AAeA,AAAA,MAAA,UAzMC,SAAA,GAAA,CAyMD;;;;;IAKA,WAAA,CA7MqB,OAAS,EAAuB,SAAW,EA6MhE;QA7MoE,KAAA,EAAA,CAAA;QAA/C,IAArB,CAAA,OAAqB,GAAA,OAAA,CAAS;QAAuB,IAArD,CAAA,SAAqD,GAAA,SAAA,CAAW;KAAI;;;;;;IAoNpE,KAlNG,CAAA,OAAA,EAAA,OAAA,EAkNH,EAlNkD,OAAO,OAAA,CAAQ,YAAC,CAAY,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAmN7F;AAED,AAOA,AAAA,MAAA,SAzNC,SAAA,GAAA,CAyND;;;;;;IAMA,WAAA,CA7Na,QAAU,EAAe,KA6NtC,GA7NsD,CAAA,EAAU,MA6NhE,GA7NsF,IAAA,EA6NtF;QAII,KAAK,EAAE,CAAC;QAjOC,IAAb,CAAA,QAAa,GAAA,QAAA,CAAU;QAAe,IAAtC,CAAA,KAAsC,GAAA,KAAA,CAAgB;QAAU,IAAhE,CAAA,MAAgE,GAAA,MAAA,CAAsB;KAkOnF;;;;;;IAMH,KApOG,CAAA,OAAA,EAAA,OAAA,EAoOH,EApOkD,OAAO,OAAA,CAAQ,WAAC,CAAW,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAqO5F;AAED,AASA,AAAA,MAAA,gBA7OC,SAAA,SAAA,CA6OD;;;;IAIA,WAAA,CAhPqB,KAAO,EAgP5B;QAhPmC,KAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;QAAd,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAO;KAAO;;;;;;IAsPnC,KApPG,CAAA,OAAA,EAAA,OAAA,EAoPH,EApPkD,OAAO,OAAA,CAAQ,WAAC,CAAW,IAAC,EAAK,OAAA,CAAQ,CAAC,EAAC;CAqP5F,AAED,AAGC;;AD9YD;;;;;;;;;;;;;AA+BA,AAAA,SAAA,kBAAA,CACI,OAA4B,EAAE,IAAuB,EAAE,OAAY,EADvE;IAEE,QAAQ,IANC,CAAI,IAAC;QAOZ,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,YAAC,mBAAY,IAAQ,GAA0B,OAAA,CAAQ,CAAC;QAOzE,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,UAAC,mBAAU,IAAQ,GAAwB,OAAA,CAAQ,CAAC;QAOrE,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,eAAC,mBAAe,IAAQ,GAA6B,OAAA,CAAQ,CAAC;QAO/E,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,aAAC,mBAAa,IAAQ,GAA2B,OAAA,CAAQ,CAAC;QAO3E,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,UAAC,mBAAU,IAAQ,GAAwB,OAAA,CAAQ,CAAC;QAOrE,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,YAAC,mBAAY,IAAQ,GAA0B,OAAA,CAAQ,CAAC;QAOzE,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,cAAC,mBAAc,IAAQ,GAAoC,OAAA,CAAQ,CAAC;QAOrF,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,UAAC,mBAAU,IAAQ,GAAwB,OAAA,CAAQ,CAAC;QAOrE,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,cAAC,mBAAc,IAAQ,GAA4B,OAAA,CAAQ,CAAC;QAO7E,KAAJ,CAAA;YACM,OANO,OAAA,CAAQ,iBAAC,mBAAiB,IAAQ,GAA+B,OAAA,CAAQ,CAAC;QAOnF,KAAJ,EAAA;YACM,OANO,OAAA,CAAQ,eAAC,mBAAe,IAAQ,GAA6B,OAAA,CAAQ,CAAC;QAO/E,KAAJ,EAAA;YACM,OANO,OAAA,CAAQ,UAAC,mBAAU,IAAQ,GAAwB,OAAA,CAAQ,CAAC;QAOrE,KAAJ,EAAA;YACM,OANO,OAAA,CAAQ,YAAC,mBAAY,IAAQ,GAA0B,OAAA,CAAQ,CAAC;QAOzE;YACE,MANM,IAAI,KAAA,CAAM,CAMtB,2CAAA,EANuB,IAA8C,CAAI,IAAC,CAM1E,CAN8E,CAAE,CAAC;KAO9E;CACF;;AD9DD;;;;;;;AAOA,AADC,MAAA,SAAA,GAAA,GAAA,CAAA;;;;;;AAQD,AAAA,SAAA,mBAAA,CACI,eAA6C,EAAE,MAAgB,EADnE;IAEE,uBALM,WAAA,GAAqC,EAAA,CAAG;IAM9C,IAAI,OALO,eAAA,IAAmB,QAAA,EAAU;QAMtC,EALS,eAAC;aAML,KALC,CAAK,SAAC,CAAS;aAMhB,OALC,CAAO,GAAC,IAAM,uBAAA,CAAwB,GAAC,EAAI,WAAA,EAAa,MAAA,CAAO,CAAC,CAAC;KAMxE;SALM;QAML,WAAW,CALC,IAAC,mBAAyB,eAAC,EAAe,CAAC;KAMxD;IACD,OALO,WAAA,CAAY;CAMpB;;;;;;;AAOD,SAAA,uBAAA,CACI,QAAgB,EAAE,WAAkC,EAAE,MAAgB,EAD1E;IAEE,IAAI,QAVC,CAAQ,CAAC,CAAC,IAAI,GAAA,EAAK;QAWtB,QAAQ,GAVG,mBAAA,CAAoB,QAAC,EAAS,MAAA,CAAO,CAAC;KAWlD;IACD,uBAVM,KAAA,GAAQ,QAAA,CAAS,KAAC,CAAK,yCAAC,CAAyC,CAAC;IAWxE,IAAI,KAVC,IAAQ,IAAA,IAAQ,KAAA,CAAM,MAAC,GAAQ,CAAA,EAAG;QAWrC,MAAM,CAVC,IAAC,CAAI,CAUhB,oCAAA,EAViB,QAAuC,CAUxD,kBAAA,CAVgE,CAAoB,CAAC;QAWjF,OAVO,WAAA,CAAY;KAWpB;IAED,uBAVM,SAAA,GAAY,KAAA,CAAM,CAAC,CAAC,CAAC;IAW3B,uBAVM,SAAA,GAAY,KAAA,CAAM,CAAC,CAAC,CAAC;IAW3B,uBAVM,OAAA,GAAU,KAAA,CAAM,CAAC,CAAC,CAAC;IAWzB,WAAW,CAVC,IAAC,CAAI,oBAAC,CAAoB,SAAC,EAAU,OAAA,CAAQ,CAAC,CAAC;IAY3D,uBAVM,kBAAA,GAAqB,SAAA,IAAa,SAAA,IAAa,OAAA,IAAW,SAAA,CAAU;IAW1E,IAAI,SAVC,CAAS,CAAC,CAAC,IAAI,GAAA,IAAO,CAAA,kBAAE,EAAmB;QAW9C,WAAW,CAVC,IAAC,CAAI,oBAAC,CAAoB,OAAC,EAAQ,SAAA,CAAU,CAAC,CAAC;KAW5D;CACF;;;;;;AAMD,SAAA,mBAAA,CAdC,KAAA,EAAA,MAAA,EAcD;IACE,QAAQ,KAdC;QAeP,KAdK,QAAA;YAeH,OAdO,WAAA,CAAY;QAerB,KAdK,QAAA;YAeH,OAdO,WAAA,CAAY;QAerB;YACE,MAAM,CAdC,IAAC,CAAI,CAclB,4BAAA,EAdmB,KAA+B,CAclD,kBAAA,CAduD,CAAoB,CAAC;YAetE,OAdO,QAAA,CAAS;KAenB;CACF;;;;;;AAMD,SAAA,oBAAA,CAlBC,GAAA,EAAA,GAAA,EAkBD;IACE,OAlBO,CAAA,SAAY,EAAK,OAAS,KAkBnC;QACI,qBAlBI,QAAA,GAAW,GAAA,IAAO,SAAA,IAAa,GAAA,IAAO,SAAA,CAAU;QAmBpD,qBAlBI,QAAA,GAAW,GAAA,IAAO,SAAA,IAAa,GAAA,IAAO,OAAA,CAAQ;QAoBlD,IAAI,CAlBC,QAAC,IAAW,OAAO,SAAA,KAAc,SAAA,EAAW;YAmB/C,QAAQ,GAlBG,SAAA,GAAY,GAAA,KAAQ,MAAA,GAAS,GAAA,KAAQ,OAAA,CAAQ;SAmBzD;QACD,IAAI,CAlBC,QAAC,IAAW,OAAO,OAAA,KAAY,SAAA,EAAW;YAmB7C,QAAQ,GAlBG,OAAA,GAAU,GAAA,KAAQ,MAAA,GAAS,GAAA,KAAQ,OAAA,CAAQ;SAmBvD;QAED,OAlBO,QAAA,IAAY,QAAA,CAAS;KAmB7B,CAlBC;CAmBH;;ADzFD;;;;;;;AAQA,AAEA,AACA,AAEA,AACA,AACA,AAEA,MADM,UAAA,GAAa,OAAA,CAAQ;AAE3B,MADM,gBAAA,GAAmB,IAAI,MAAA,CAAO,CACpC,GAAA,EADqC,UAAM,CAC3C,KAAA,CADqD,EAAQ,GAAA,CAAI,CAAC;;;;;;AAOlE,AAAA,SAAA,iBAAA,CACI,QAAiD,EAAE,MAAa,EADpE;IAEE,OA+BO,IAAI,0BAAA,EAA2B,CAAE,KAAC,CAAK,QAAC,EAAS,MAAA,CAAO,CAAC;CA9BjE;AAED,MA+BM,WAAA,GAAc,QAAA,CAAS;AA9B7B,MA+BM,iBAAA,GAAoB,IAAI,MAAA,CAAO,WAAC,EAAY,GAAA,CAAI,CAAC;AA9BvD,MA+BM,WAAA,GAAc,QAAA,CAAS;AA9B7B,MA+BM,iBAAA,GAAoB,IAAI,MAAA,CAAO,WAAC,EAAY,GAAA,CAAI,CAAC;AA9BvD,MA+BM,aAAA,GAAgB,EAAA,CAAG;AA9BzB,AAAA,MAAA,0BAAA,CAAA;;;;;;IAMA,KA2BG,CAAA,QAAA,EAAA,MAAA,EA3BH;QACI,uBA2BM,OAAA,GAAU,IAAI,0BAAA,CAA2B,MAAC,CAAM,CAAC;QA1BvD,IAAI,CA2BC,6BAAC,CAA6B,OAAC,CAAO,CAAC;QA1B5C,QA2BO,kBAAA,CAAmB,IAAC,EAAK,uBAAA,CAAwB,QAAC,CAAQ,EAAE,OAAA,CAAY,EAAI;KA1BpF;;;;;IA6BA,6BAAA,CAAA,OAAA,EAAH;QAvBI,OAAO,CAwBC,oBAAC,GAAsB,aAAA,CAAc;QAvB7C,OAAO,CAwBC,eAAC,GAAiB,EAAA,CAAG;QAvB7B,OAAO,CAwBC,eAAC,CAAe,aAAC,CAAa,GAAG,EAAA,CAAG;QAvB5C,OAAO,CAwBC,WAAC,GAAa,CAAA,CAAE;KAvBzB;;;;;;IAMH,YAoBG,CAAA,QAAA,EAAA,OAAA,EApBH;QAEI,qBAoBI,UAAA,GAAa,OAAA,CAAQ,UAAC,GAAY,CAAA,CAAE;QAnBxC,qBAoBI,QAAA,GAAW,OAAA,CAAQ,QAAC,GAAU,CAAA,CAAE;QAnBpC,uBAoBM,MAAA,GAAqB,EAAA,CAAG;QAnB9B,uBAoBM,WAAA,GAA+B,EAAA,CAAG;QAnBxC,QAAQ,CAoBC,WAAC,CAAW,OAAC,CAAO,GAAC,IApBlC;YACM,IAAI,CAoBC,6BAAC,CAA6B,OAAC,CAAO,CAAC;YAnB5C,IAAI,GAoBC,CAAG,IAAC,IApBf,CAAA,cAoBmD;gBAnB3C,uBAoBM,QAAA,IAAW,GAAO,CAAA,CAAuB;gBAnB/C,uBAoBM,IAAA,GAAO,QAAA,CAAS,IAAC,CAAI;gBAnB3B,IAAI,CAoBC,KAAC,CAAK,SAAC,CAAS,CAAC,OAAC,CAAO,CAAC,IApBvC;oBACU,QAAQ,CAoBC,IAAC,GAAM,CAAA,CAAE;oBAnBlB,MAAM,CAoBC,IAAC,CAAI,IAAC,CAAI,UAAC,CAAU,QAAC,EAAS,OAAA,CAAQ,CAAC,CAAC;iBAnBjD,CAoBC,CAAC;gBAnBH,QAAQ,CAoBC,IAAC,GAAM,IAAA,CAAK;aAnBtB;iBAoBM,IAAA,GAAK,CAAG,IAAC,IAAtB,CAAA,mBAA+D;gBAnBvD,uBAoBM,UAAA,GAAa,IAAA,CAAK,eAAC,mBAAe,GAAO,GAA6B,OAAA,CAAQ,CAAC;gBAnBrF,UAAU,IAoBI,UAAA,CAAW,UAAC,CAAU;gBAnBpC,QAAQ,IAoBI,UAAA,CAAW,QAAC,CAAQ;gBAnBhC,WAAW,CAoBC,IAAC,CAAI,UAAC,CAAU,CAAC;aAnB9B;iBAoBM;gBAnBL,OAAO,CAoBC,MAAC,CAAM,IAAC,CAnBZ,yEAAyE,CAoBC,CAAC;aAnBhF;SACF,CAoBC,CAAC;QAnBH,uBAoBM,GAAA,GAAM,IAAI,UAAA,CAAW,QAAC,CAAQ,IAAC,EAAK,MAAA,EAAQ,WAAA,CAAY,CAAC;QAnB/D,GAAG,CAoBC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAnB1D,GAAG,CAoBC,UAAC,GAAY,UAAA,CAAW;QAnB5B,GAAG,CAoBC,QAAC,GAAU,QAAA,CAAS;QAnBxB,OAoBO,GAAA,CAAI;KAnBZ;;;;;;IAMH,UAgBG,CAAA,QAAA,EAAA,OAAA,EAhBH;QACI,OAgBO,IAAI,QAAA,CAAS,QAAC,CAAQ,IAAC,EAAK,IAAA,CAAK,UAAC,CAAU,QAAC,CAAQ,MAAC,EAAO,OAAA,CAAQ,CAAC,CAAC;KAf/E;;;;;;IAMH,eAYG,CAAA,QAAA,EAAA,OAAA,EAZH;QAEI,OAAO,CAYC,UAAC,GAAY,CAAA,CAAE;QAXvB,OAAO,CAYC,QAAC,GAAU,CAAA,CAAE;QAXrB,uBAYM,KAAA,GAAQ,kBAAA,CAAmB,IAAC,EAAK,uBAAA,CAAwB,QAAC,CAAQ,SAAC,CAAS,EAAE,OAAA,CAAQ,CAAC;QAX7F,uBAYM,QAAA,GAAW,mBAAA,CAAoB,QAAC,CAAQ,IAAC,EAAK,OAAA,CAAQ,MAAC,CAAM,CAAC;QAXpE,uBAYM,GAAA,GAAM,IAAI,aAAA,CAAc,QAAC,EAAS,KAAA,CAAM,CAAC;QAX/C,GAAG,CAYC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAX1D,GAAG,CAYC,UAAC,GAAY,OAAA,CAAQ,UAAC,CAAU;QAXpC,GAAG,CAYC,QAAC,GAAU,OAAA,CAAQ,QAAC,CAAQ;QAXhC,OAYO,GAAA,CAAI;KAXZ;;;;;;IAMH,aAQG,CAAA,QAAA,EAAA,OAAA,EARH;QAEI,uBAQM,GAAA,GAAM,IAAI,WAAA,CAAY,QAAC,CAAQ,KAAC,CAAK,GAAC,CAAG,CAAC,IAAI,kBAAA,CAAmB,IAAC,EAAK,CAAA,EAAG,OAAA,CAAQ,CAAC,CAAC,CAAC;QAP3F,GAAG,CAQC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAP1D,OAQO,GAAA,CAAI;KAPZ;;;;;;IAMH,UAIG,CAAA,QAAA,EAAA,OAAA,EAJH;QACI,uBAIM,WAAA,GAAc,OAAA,CAAQ,WAAC,CAAW;QAHxC,qBAII,YAAA,GAAe,CAAA,CAAE;QAHrB,uBAIM,KAAA,GAAQ,QAAA,CAAS,KAAC,CAAK,GAAC,CAAG,IAAC,IAJtC;YACM,OAAO,CAIC,WAAC,GAAa,WAAA,CAAY;YAHlC,uBAIM,QAAA,GAAW,kBAAA,CAAmB,IAAC,EAAK,IAAA,EAAM,OAAA,CAAQ,CAAC;YAHzD,YAAY,GAIG,IAAA,CAAK,GAAC,CAAG,YAAC,EAAa,OAAA,CAAQ,WAAC,CAAW,CAAC;YAH3D,OAIO,QAAA,CAAS;SAHjB,CAIC,CAAC;QAFH,OAAO,CAIC,WAAC,GAAa,YAAA,CAAa;QAHnC,uBAIM,GAAA,GAAM,IAAI,QAAA,CAAS,KAAC,CAAK,CAAC;QAHhC,GAAG,CAIC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAH1D,OAIO,GAAA,CAAI;KAHZ;;;;;;IAMH,YAAG,CAAA,QAAA,EAAA,OAAA,EAAH;QAEI,uBAAM,SAAA,GAAY,kBAAA,CAAmB,QAAC,CAAQ,OAAC,EAAQ,OAAA,CAAQ,MAAC,CAAM,CAAC;QACvE,OAAO,CAAC,qBAAC,GAAuB,SAAA,CAAU;QAE1C,qBAAI,MAAkB,CAAY;QAClC,qBAAI,aAAA,GAAmC,QAAA,CAAS,MAAC,GAAQ,QAAA,CAAS,MAAC,GAAQ,KAAA,CAAM,EAAC,CAAE,CAAC;QACrF,IAAI,aAAC,CAAa,IAAC,IAAvB,CAAA,kBAA+D;YACzD,MAAM,GAAG,IAAA,CAAK,cAAC,mBAAc,aAAiB,GAAoC,OAAA,CAAQ,CAAC;SAC5F;aAAM;YACL,qBAAI,aAAA,IAAgB,QAAA,CAAS,MAAU,CAAA,CAAuB;YAC9D,qBAAI,OAAA,GAAU,KAAA,CAAM;YACpB,IAAI,CAAC,aAAC,EAAc;gBAClB,OAAO,GAAG,IAAA,CAAK;gBACf,uBAAM,YAAA,GAAkD,EAAA,CAAG;gBAC3D,IAAI,SAAC,CAAS,MAAC,EAAO;oBACpB,YAAY,CAAC,QAAC,CAAQ,GAAG,SAAA,CAAU,MAAC,CAAM;iBAC3C;gBACD,aAAa,GAAG,KAAA,CAAM,YAAC,CAAY,CAAC;aACrC;YACD,OAAO,CAAC,WAAC,IAAc,SAAA,CAAU,QAAC,GAAU,SAAA,CAAU,KAAC,CAAK;YAC5D,uBAAM,QAAA,GAAW,IAAA,CAAK,UAAC,CAAU,aAAC,EAAc,OAAA,CAAQ,CAAC;YACzD,QAAQ,CAAC,WAAC,GAAa,OAAA,CAAQ;YAC/B,MAAM,GAAG,QAAA,CAAS;SACnB;QAED,OAAO,CAAC,qBAAC,GAAuB,IAAA,CAAK;QACrC,OAAO,IAAI,UAAA,CAAW,SAAC,EAAU,MAAA,CAAO,CAAC;KAC1C;;;;;;IAMH,UAJG,CAAA,QAAA,EAAA,OAAA,EAIH;QACI,uBAJM,GAAA,GAAM,IAAA,CAAK,aAAC,CAAa,QAAC,EAAS,OAAA,CAAQ,CAAC;QAKlD,IAAI,CAJC,iBAAC,CAAiB,GAAC,EAAI,OAAA,CAAQ,CAAC;QAKrC,OAJO,GAAA,CAAI;KAKZ;;;;;;IAFA,aAAA,CAAA,QAAA,EAAA,OAAA,EAAH;QAUI,uBARM,MAAA,GAAkC,EAAA,CAAG;QAS3C,IAAI,KARC,CAAK,OAAC,CAAO,QAAC,CAAQ,MAAC,CAAM,EAAE;YASlC,EAAoB,QARlB,CAAQ,MAA+B,GAAG,OAAC,CAAO,UAAC,IAQ3D;gBACQ,IAAI,OARO,UAAA,IAAc,QAAA,EAAU;oBASjC,IAAI,UARC,IAAa,UAAA,EAAY;wBAS5B,MAAM,CARC,IAAC,mBAAI,UAAc,EAAO,CAAC;qBASnC;yBARM;wBASL,OAAO,CARC,MAAC,CAAM,IAAC,CAAI,CAQhC,gCAAA,EARiC,UAAmC,CAQpE,gBAAA,CAR8E,CAAkB,CAAC;qBAStF;iBACF;qBARM;oBASL,MAAM,CARC,IAAC,mBAAI,UAAc,EAAW,CAAC;iBASvC;aACF,CARC,CAAA;SASH;aARM;YASL,MAAM,CARC,IAAC,CAAI,QAAC,CAAQ,MAAC,CAAM,CAAC;SAS9B;QAED,qBARI,eAAA,GAA+B,IAAA,CAAK;QASxC,MAAM,CARC,OAAC,CAAO,SAAC,IAQpB;YACM,IAAI,QARC,CAAQ,SAAC,CAAS,EAAE;gBASvB,uBARM,QAAA,IAAW,SAAa,CAAA,CAAW;gBASzC,uBARM,MAAA,GAAS,QAAA,CAAS,QAAC,CAAQ,CAAC;gBASlC,IAAI,MARC,EAAO;oBASV,eAAe,IARG,MAAU,CAAA,CAAO;oBASnC,OARO,QAAA,CAAS,QAAC,CAAQ,CAAC;iBAS3B;aACF;SACF,CARC,CAAC;QASH,OARO,IAAI,QAAA,CAAS,MAAC,EAAO,eAAA,EAAiB,QAAA,CAAS,MAAC,CAAM,CAAC;KAS/D;;;;;;IANA,iBAAA,CAAA,GAAA,EAAA,OAAA,EAAH;QAaI,uBAZM,OAAA,GAAU,OAAA,CAAQ,qBAAC,CAAqB;QAa9C,qBAZI,OAAA,GAAU,OAAA,CAAQ,WAAC,CAAW;QAalC,qBAZI,SAAA,GAAY,OAAA,CAAQ,WAAC,CAAW;QAapC,IAAI,OAZC,IAAU,SAAA,GAAY,CAAA,EAAG;YAa5B,SAAS,IAZI,OAAA,CAAQ,QAAC,GAAU,OAAA,CAAQ,KAAC,CAAK;SAa/C;QAED,GAAG,CAZC,MAAC,CAAM,OAAC,CAAO,KAAC,IAYxB;YACM,IAAI,OAZO,KAAA,IAAS,QAAA;gBAAU,OAAA;YAc9B,MAAM,CAZC,IAAC,CAAI,KAAC,CAAK,CAAC,OAAC,CAAO,IAAC,IAYlC;gBACQ,uBAZM,eAAA,GAAkB,OAAA,CAAQ,eAAC,GAAe,OAAC,CAAO,oBAAC,GAAsB,CAAC;gBAahF,uBAZM,cAAA,GAAiB,eAAA,CAAgB,IAAC,CAAI,CAAC;gBAa7C,qBAZI,oBAAA,GAAuB,IAAA,CAAK;gBAahC,IAAI,cAZC,EAAe;oBAalB,IAAI,SAZC,IAAY,OAAA,IAAW,SAAA,IAAa,cAAA,CAAe,SAAC;wBAarD,OAAO,IAZI,cAAA,CAAe,OAAC,EAAQ;wBAarC,OAAO,CAZC,MAAC,CAAM,IAAC,CAaZ,CADhB,kBAAA,EACqC,IAZC,CAWtC,oCAAA,EAX0C,cAAuC,CAAc,SAAC,CAWhG,SAAA,EAXyG,cAAY,CAAc,OAAC,CAWpI,yEAAA,EAX2I,SAA4E,CAWvN,SAAA,EAXgO,OAAY,CAW5O,GAAA,CAXmP,CAAK,CAAC;wBAa7O,oBAAoB,GAZG,KAAA,CAAM;qBAa9B;;;;oBAKD,SAAS,GAZG,cAAA,CAAe,SAAC,CAAS;iBAatC;gBAED,IAAI,oBAZC,EAAqB;oBAaxB,eAAe,CAZC,IAAC,CAAI,GAAG,EAAA,SAAE,EAAU,OAAA,EAAQ,CAAC;iBAa9C;gBAED,IAAI,OAZC,CAAO,OAAC,EAAQ;oBAanB,mBAAmB,CAZC,KAAC,CAAK,IAAC,CAAI,EAAE,OAAA,CAAQ,OAAC,EAAQ,OAAA,CAAQ,MAAC,CAAM,CAAC;iBAanE;aACF,CAZC,CAAC;SAaJ,CAZC,CAAC;KAaJ;;;;;;IAMH,cAhBG,CAAA,QAAA,EAAA,OAAA,EAgBH;QAEI,IAAI,CAhBC,OAAC,CAAO,qBAAC,EAAsB;YAiBlC,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,CAgB1B,wDAAA,CAhB2B,CAA0D,CAAC;YAiBhF,OAhBO,IAAI,YAAA,CAAa,EAAC,CAAE,CAAC;SAiB7B;QAED,uBAhBM,mBAAA,GAAsB,CAAA,CAAE;QAkB9B,qBAhBI,yBAAA,GAA4B,CAAA,CAAE;QAiBlC,uBAhBM,OAAA,GAAoB,EAAA,CAAG;QAiB7B,qBAhBI,iBAAA,GAAoB,KAAA,CAAM;QAiB9B,qBAhBI,mBAAA,GAAsB,KAAA,CAAM;QAiBhC,qBAhBI,cAAA,GAAyB,CAAA,CAAE;QAkB/B,uBAhBM,SAAA,GAAwB,QAAA,CAAS,KAAC,CAAK,GAAC,CAAG,MAAC,IAgBtD;YACM,uBAhBMA,QAAA,GAAQ,IAAA,CAAK,aAAC,CAAa,MAAC,EAAO,OAAA,CAAQ,CAAC;YAiBlD,qBAhBI,SAAA,GAiBAA,QAAK,CAhBC,MAAC,IAAS,IAAA,GAAOA,QAAA,CAAM,MAAC,GAAQ,aAAA,CAAcA,QAAC,CAAK,MAAC,CAAM,CAAC;YAiBtE,qBAhBI,MAAA,GAAiB,CAAA,CAAE;YAiBvB,IAAI,SAhBC,IAAY,IAAA,EAAM;gBAiBrB,yBAAyB,EAhBC,CAAE;gBAiB5B,MAAM,GAhBGA,QAAA,CAAM,MAAC,GAAQ,SAAA,CAAU;aAiBnC;YACD,mBAAmB,GAhBG,mBAAA,IAAuB,MAAA,GAAS,CAAA,IAAK,MAAA,GAAS,CAAA,CAAE;YAiBtE,iBAAiB,GAhBG,iBAAA,IAAqB,MAAA,GAAS,cAAA,CAAe;YAiBjE,cAAc,GAhBG,MAAA,CAAO;YAiBxB,OAAO,CAhBC,IAAC,CAAI,MAAC,CAAM,CAAC;YAiBrB,OAhBOA,QAAA,CAAM;SAiBd,CAhBC,CAAC;QAkBH,IAAI,mBAhBC,EAAoB;YAiBvB,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,CAgB1B,2DAAA,CAhB2B,CAA6D,CAAC;SAiBpF;QAED,IAAI,iBAhBC,EAAkB;YAiBrB,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,CAgB1B,oDAAA,CAhB2B,CAAsD,CAAC;SAiB7E;QAED,uBAhBM,MAAA,GAAS,QAAA,CAAS,KAAC,CAAK,MAAC,CAAM;QAiBrC,qBAhBI,eAAA,GAAkB,CAAA,CAAE;QAiBxB,IAAI,yBAhBC,GAA2B,CAAA,IAAK,yBAAA,GAA4B,MAAA,EAAQ;YAiBvE,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,CAgB1B,qEAAA,CAhB2B,CAAuE,CAAC;SAiB9F;aAhBM,IAAA,yBAAK,IAA4B,CAAA,EAAG;YAiBzC,eAAe,GAhBG,mBAAA,IAAsB,MAAE,GAAQ,CAAA,CAAE,CAAC;SAiBtD;QAED,uBAhBM,KAAA,GAAQ,MAAA,GAAS,CAAA,CAAE;QAiBzB,uBAhBM,WAAA,GAAc,OAAA,CAAQ,WAAC,CAAW;QAiBxC,uBAhBM,qBAAA,KAAwB,OAAA,CAAQ,qBAAC,EAAA,CAAuB;QAiB9D,uBAhBM,eAAA,GAAkB,qBAAA,CAAsB,QAAC,CAAQ;QAiBvD,SAAS,CAhBC,OAAC,CAAO,CAAC,EAAC,EAAG,CAAA,KAgB3B;YACM,uBAhBM,MAAA,GAAS,eAAA,GAAkB,CAAA,IAAI,CAAE,IAAI,KAAA,GAAQ,CAAA,IAAI,eAAE,GAAiB,CAAA,CAAE,IAAI,OAAA,CAAQ,CAAC,CAAC,CAAC;YAiB3F,uBAhBM,qBAAA,GAAwB,MAAA,GAAS,eAAA,CAAgB;YAiBvD,OAAO,CAhBC,WAAC,GAAa,WAAA,GAAc,qBAAA,CAAsB,KAAC,GAAO,qBAAA,CAAsB;YAiBxF,qBAAqB,CAhBC,QAAC,GAAU,qBAAA,CAAsB;YAiBvD,IAAI,CAhBC,iBAAC,CAAiB,EAAC,EAAG,OAAA,CAAQ,CAAC;YAiBpC,EAAE,CAhBC,MAAC,GAAQ,MAAA,CAAO;SAiBpB,CAhBC,CAAC;QAkBH,OAhBO,IAAI,YAAA,CAAa,SAAC,CAAS,CAAC;KAiBpC;;;;;;IAMH,cApBG,CAAA,QAAA,EAAA,OAAA,EAoBH;QAEI,uBApBM,KAAA,GAAQ,kBAAA,CAAmB,IAAC,EAAK,uBAAA,CAAwB,QAAC,CAAQ,SAAC,CAAS,EAAE,OAAA,CAAQ,CAAC;QAqB7F,uBApBM,GAAA,GAAM,IAAI,YAAA,CAAa,KAAC,CAAK,CAAC;QAqBpC,GAAG,CApBC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAqB1D,OApBO,GAAA,CAAI;KAqBZ;;;;;;IAMH,iBAxBG,CAAA,QAAA,EAAA,OAAA,EAwBH;QAEI,OAAO,CAxBC,QAAC,EAAQ,CAAE;QAyBnB,uBAxBM,GAAA,GAAM,IAAI,eAAA,EAAgB,CAAE;QAyBlC,GAAG,CAxBC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAyB1D,OAxBO,GAAA,CAAI;KAyBZ;;;;;;IAMH,eA5BG,CAAA,QAAA,EAAA,OAAA,EA4BH;QAEI,uBA5BM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,QAAC,CAAQ,SAAC,EAAU,OAAA,CAAQ,CAAC;QA6BnE,uBA5BM,GAAA,GAAM,IAAI,aAAA,CAAc,SAAC,CAAS,CAAC;QA6BzC,GAAG,CA5BC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QA6B1D,OA5BO,GAAA,CAAI;KA6BZ;;;;;;IAMH,UAhCG,CAAA,QAAA,EAAA,OAAA,EAgCH;QACI,uBAhCM,cAAA,KAAiB,OAAA,CAAQ,oBAAC,EAAA,CAAsB;QAiCtD,uBAhCM,OAAA,KAAU,QAAE,CAAQ,OAAC,IAAU,EAAA,EAAO,CAAsB;QAkClE,OAAO,CAhCC,UAAC,EAAU,CAAE;QAiCrB,OAAO,CAhCC,YAAC,GAAc,QAAA,CAAS;QAiChC,MAhCM,CAAA,QAAE,EAAS,WAAA,CAAY,GAAG,iBAAA,CAAkB,QAAC,CAAQ,QAAC,CAAQ,CAAC;QAiCrE,OAAO,CAhCC,oBAAC;YAiCL,cAAc,CAhCC,MAAC,IAAQ,cAAE,GAAgB,GAAA,GAAM,QAAA,IAAY,QAAA,CAAS;QAiCzE,eAAe,CAhCC,OAAC,CAAO,eAAC,EAAgB,OAAA,CAAQ,oBAAC,EAAqB,EAAA,CAAG,CAAC;QAkC3E,uBAhCM,KAAA,GAAQ,kBAAA,CAAmB,IAAC,EAAK,uBAAA,CAAwB,QAAC,CAAQ,SAAC,CAAS,EAAE,OAAA,CAAQ,CAAC;QAiC7F,OAAO,CAhCC,YAAC,GAAc,IAAA,CAAK;QAiC5B,OAAO,CAhCC,oBAAC,GAAsB,cAAA,CAAe;QAkC9C,uBAhCM,GAAA,GAAM,IAAI,QAAA,CAAS,QAAC,EAAS,OAAA,CAAQ,KAAC,IAAQ,CAAA,EAAG,CAAA,CAAE,OAAC,CAAO,QAAC,EAAS,WAAA,EAAa,KAAA,CAAM,CAAC;QAiC/F,GAAG,CAhCC,gBAAC,GAAkB,QAAA,CAAS,QAAC,CAAQ;QAiCzC,GAAG,CAhCC,OAAC,GAAS,yBAAA,CAA0B,QAAC,CAAQ,OAAC,CAAO,CAAC;QAiC1D,OAhCO,GAAA,CAAI;KAiCZ;;;;;;IAMH,YApCG,CAAA,QAAA,EAAA,OAAA,EAoCH;QAEI,IAAI,CApCC,OAAC,CAAO,YAAC,EAAa;YAqCzB,OAAO,CApCC,MAAC,CAAM,IAAC,CAAI,CAoC1B,4CAAA,CApC2B,CAA8C,CAAC;SAqCrE;QACD,uBApCM,OAAA,GAAU,QAAA,CAAS,OAAC,KAAW,MAAA;YAqCjC,EAAC,QApCC,EAAS,CAAA,EAAG,KAAA,EAAO,CAAA,EAAG,MAAA,EAAQ,MAAA,EAAO;YAqCvC,aAAa,CApCC,QAAC,CAAQ,OAAC,EAAQ,OAAA,CAAQ,MAAC,EAAO,IAAA,CAAK,CAAC;QAqC1D,uBApCM,SAAA,GAqCF,kBAAkB,CApCC,IAAC,EAAK,uBAAA,CAAwB,QAAC,CAAQ,SAAC,CAAS,EAAE,OAAA,CAAQ,CAAC;QAqCnF,OApCO,IAAI,UAAA,CAAW,OAAC,EAAQ,SAAA,CAAU,CAAC;KAqC3C;CACF;;;;;AAKD,SAAA,iBAAA,CAvCC,QAAA,EAuCD;IACE,uBAvCM,YAAA,GAAe,QAAA,CAAS,KAAC,CAAK,SAAC,CAAS,CAAC,IAAC,CAAI,KAAC,IAAQ,KAAA,IAAS,UAAA,CAAW,GAAG,IAAA,GAAO,KAAA,CAAM;IAwCjG,IAAI,YAvCC,EAAa;QAwChB,QAAQ,GAvCG,QAAA,CAAS,OAAC,CAAO,gBAAC,EAAiB,EAAA,CAAG,CAAC;KAwCnD;IAED,QAAQ,GAvCG,QAAA,CAAS,OAAC,CAAO,iBAAC,EAAkB,cAAA,CAAe;SAwC9C,OAvCC,CAAO,iBAAC,EAAkB,cAAA,CAAe;SAwC1C,OAvCC,CAAO,MAAC,EAAO,mBAAA,CAAoB;SAwCpC,OAvCC,CAAO,OAAC,EAAQ,KAAA,IAAS,mBAAA,GAAsB,GAAA,GAAM,KAAA,CAAM,MAAC,CAAM,CAAC,CAAC,CAAC;SAwCtE,OAvCC,CAAO,aAAC,EAAc,qBAAA,CAAsB,CAAC;IAyC9D,OAvCO,CAAA,QAAE,EAAS,YAAA,CAAa,CAAC;CAwCjC;;;;;AAKD,SAAA,eAAA,CAzCC,GAAA,EAyCD;IACE,OAzCO,GAAA,GAAM,OAAA,CAAQ,GAAC,CAAG,GAAG,IAAA,CAAK;CA0ClC;AAKD,AAAA,MAAA,0BAAA,CAAA;;;;IAaA,WAAA,CA3CqB,MAAY,EA2CjC;QA3CqB,IAArB,CAAA,MAAqB,GAAA,MAAA,CAAY;QATxB,IAAT,CAAA,UAAS,GAAqB,CAAA,CAAE;QACvB,IAAT,CAAA,QAAS,GAAmB,CAAA,CAAE;QACrB,IAAT,CAAA,iBAAS,GAAsD,IAAA,CAAK;QAC3D,IAAT,CAAA,YAAS,GAA4C,IAAA,CAAK;QACjD,IAAT,CAAA,oBAAS,GAAoC,IAAA,CAAK;QACzC,IAAT,CAAA,qBAAS,GAAwC,IAAA,CAAK;QAC7C,IAAT,CAAA,WAAS,GAAsB,CAAA,CAAE;QACxB,IAAT,CAAA,eAAS,GAAkF,EAAA,CAAG;QACrF,IAAT,CAAA,OAAS,GAAiC,IAAA,CAAK;KACZ;CA4ClC;AAED,AAuBA;;;;AAIA,SAAA,aAAA,CAtEC,MAAA,EAsED;IACE,IAAI,OAtEO,MAAA,IAAU,QAAA;QAAU,OAAO,IAAA,CAAK;IAwE3C,qBAtEI,MAAA,GAAsB,IAAA,CAAK;IAwE/B,IAAI,KAtEC,CAAK,OAAC,CAAO,MAAC,CAAM,EAAE;QAuEzB,MAAM,CAtEC,OAAC,CAAO,UAAC,IAsEpB;YACM,IAAI,QAtEC,CAAQ,UAAC,CAAU,IAAI,UAAA,CAAW,cAAC,CAAc,QAAC,CAAQ,EAAE;gBAuE/D,uBAtEM,GAAA,IAAM,UAAc,CAAA,CAAW;gBAuErC,MAAM,GAtEG,UAAA,mBAAW,GAAC,CAAG,QAAC,CAAY,EAAO,CAAC;gBAuE7C,OAtEO,GAAA,CAAI,QAAC,CAAQ,CAAC;aAuEtB;SACF,CAtEC,CAAC;KAuEJ;SAtEM,IAAA,QAAK,CAAQ,MAAC,CAAM,IAAI,MAAA,CAAO,cAAC,CAAc,QAAC,CAAQ,EAAE;QAuE9D,uBAtEM,GAAA,IAAM,MAAU,CAAA,CAAW;QAuEjC,MAAM,GAtEG,UAAA,mBAAW,GAAC,CAAG,QAAC,CAAY,EAAO,CAAC;QAuE7C,OAtEO,GAAA,CAAI,QAAC,CAAQ,CAAC;KAuEtB;IACD,OAtEO,MAAA,CAAO;CAuEf;;;;;AAKD,SAAA,QAAA,CAzEC,KAAA,EAyED;IACE,OAzEO,CAAA,KAAE,CAAK,OAAC,CAAO,KAAC,CAAK,IAAI,OAAO,KAAA,IAAS,QAAA,CAAS;CA0E1D;;;;;;AAMD,SAAA,kBAAA,CA7EC,KAAA,EAAA,MAAA,EA6ED;IACE,qBA7EI,OAAA,GAA+B,IAAA,CAAK;IA8ExC,IAAI,KA7EC,CAAK,cAAC,CAAc,UAAC,CAAU,EAAE;QA8EpC,OAAO,IA7EG,KAAS,CAAA,CAAe;KA8EnC;SA7EM,IAAA,OAAW,KAAA,IAAS,QAAA,EAAU;QA8EnC,uBA7EM,QAAA,GAAW,aAAA,mBAAc,KAAS,GAAQ,MAAA,CAAO,CAAC,QAAC,CAAQ;QA8EjE,OA7EO,IAAI,SAAA,mBAAU,KAAS,GAAQ,CAAA,EAAG,EAAA,CAAG,CAAC;KA8E9C;IAED,uBA7EM,QAAA,IAAW,KAAS,CAAA,CAAO;IA8EjC,uBA7EM,SAAA,GAAY,QAAA,CAAS,KAAC,CAAK,KAAC,CAAK,CAAC,IAAC,CAAI,CAAC,IAAI,CAAA,CAAE,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,IAAO,CAAA,CAAE,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,CAAI,CAAC;IA8E5F,IAAI,SA7EC,EAAU;QA8Eb,OA7EO,IAAI,gBAAA,CAAiB,QAAC,CAAQ,CAAC;KA8EvC;IAED,OAAO,GA7EG,OAAA,IAAW,aAAA,CAAc,QAAC,EAAS,MAAA,CAAO,CAAC;IA8ErD,OA7EO,IAAI,SAAA,CAAU,OAAC,CAAO,QAAC,EAAS,OAAA,CAAQ,KAAC,EAAM,OAAA,CAAQ,MAAC,CAAM,CAAC;CA8EvE;;;;;AAKD,SAAA,yBAAA,CAhFC,OAAA,EAgFD;IACE,IAAI,OAhFC,EAAQ;QAiFX,OAAO,GAhFG,OAAA,CAAQ,OAAC,CAAO,CAAC;QAiF3B,IAAI,OAhFC,CAAO,QAAC,CAAQ,EAAE;YAiFrB,OAAO,CAhFC,QAAC,CAAQ,KAAG,eAAA,CAAgB,OAAC,CAAO,QAAC,CAAQ,CAAC,EAAA,CAAG;SAiF1D;KACF;SAhFM;QAiFL,OAAO,GAhFG,EAAA,CAAG;KAiFd;IACD,OAhFO,OAAA,CAAQ;CAiFhB;;AD3iBD;;;;;;;;;;;;;;;;;;AAkCA,AAAA,SAAA,yBAAA,CACI,OAAY,EAAE,SAAuB,EAAE,aAAuB,EAAE,cAAwB,EACxF,QAAgB,EAAE,KAAa,EAAE,MAFrC,GAE6D,IAAI,EAC7D,WAHJ,GAG2B,KAAK,EAHhC;IAIE,OAXO;QAYL,IAAI,EAAR,CAAA;QACI,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;QACd,QAAQ;QACR,KAAK;QACL,SAAS,EAXE,QAAA,GAAW,KAAA,EAAO,MAAA,EAAQ,WAAA;KAYtC,CAXC;CAYH;;ADhDD;;;;;;;AASA,AAAA,MAAA,qBAAA,CAAA;IAAA,WAAA,GAAA;QACU,IAAV,CAAA,IAAU,GAAO,IAAI,GAAA,EAAwC,CAAG;KAmC/D;;;;;IA9BD,OAHG,CAAA,OAAA,EAGH;QACI,qBAHI,YAAA,GAAe,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,OAAC,CAAO,CAAC;QAI1C,IAAI,YAHC,EAAa;YAIhB,IAAI,CAHC,IAAC,CAAI,MAAC,CAAM,OAAC,CAAO,CAAC;SAI3B;aAHM;YAIL,YAAY,GAHG,EAAA,CAAG;SAInB;QACD,OAHO,YAAA,CAAa;KAIrB;;;;;;IAMH,MAPG,CAAA,OAAA,EAAA,YAAA,EAOH;QACI,qBAPI,oBAAA,GAAuB,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,OAAC,CAAO,CAAC;QAQlD,IAAI,CAPC,oBAAC,EAAqB;YAQzB,IAAI,CAPC,IAAC,CAAI,GAAC,CAAG,OAAC,EAAQ,oBAAA,GAAuB,EAAA,CAAG,CAAC;SAQnD;QACD,oBAAoB,CAPC,IAAC,CAAI,GAAC,YAAG,CAAY,CAAC;KAQ5C;;;;;IAKH,GAVG,CAAA,OAAA,EAUH,EAV+B,OAAO,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,OAAC,CAAO,CAAC,EAAC;;;;IAc9D,KAZG,GAYH,EAZY,IAAA,CAAK,IAAC,CAAI,KAAC,EAAK,CAAE,EAAC;CAa9B,AAED,AAGC;;ADlDD;;;;;;;AAQA,AAGA,AAEA,AACA,AACA,AAEA,MADM,yBAAA,GAA4B,CAAA,CAAE;;;;;;;;;;;;AAapC,AAAA,SAAA,uBAAA,CACI,MAAuB,EAAE,WAAgB,EAAE,GAAQ,EAAE,cADzD,GACsF,EAAE,EACpF,WAFJ,GAE8B,EAAE,EAAE,OAAyB,EACvD,eAAuC,EAAE,MAH7C,GAG6D,EAAE,EAH/D;IAIE,OAyEO,IAAI,+BAAA,EAAgC,CAAE,cAAC,CAxE1C,MAAM,EAyEE,WAAA,EAAa,GAAA,EAAK,cAAA,EAAgB,WAAA,EAAa,OAAA,EAAS,eAAA,EAAiB,MAAA,CAAO,CAAC;CAxE9F;AACD,AAAA,MAAA,+BAAA,CAAA;;;;;;;;;;;;IAYA,cA+DG,CA9DG,MAAuB,EAAE,WAAgB,EAAE,GAAQ,EAAE,cAA0B,EAC/E,WAAuB,EAAE,OAAyB,EAAE,eAAuC,EAC3F,MAHN,GAGsB,EAAE,EAHxB;QAII,eAAe,GA+DG,eAAA,IAAmB,IAAI,qBAAA,EAAsB,CAAE;QA9DjE,uBA+DM,OAAA,GAAU,IAAI,wBAAA,CAAyB,MAAC,EAAO,WAAA,EAAa,eAAA,EAAiB,MAAA,EAAQ,EAAA,CAAG,CAAC;QA9D/F,OAAO,CA+DC,OAAC,GAAS,OAAA,CAAQ;QA9D1B,OAAO,CA+DC,eAAC,CAAe,SAAC,CAAS,CAAC,cAAC,CAAc,EAAE,IAAA,EAAM,OAAA,CAAQ,MAAC,EAAO,OAAA,CAAQ,CAAC;QA7DnF,GAAG,CA+DC,KAAC,CAAK,IAAC,EAAK,OAAA,CAAQ,CAAC;;QA5DzB,uBA+DM,SAAA,GAAY,OAAA,CAAQ,SAAC,CAAS,MAAC,CAAM,QAAC,IAAW,QAAA,CAAS,iBAAC,EAAiB,CAAE,CAAC;QA9DrF,IAAI,SA+DC,CAAS,MAAC,IAAS,MAAA,CAAO,IAAC,CAAI,WAAC,CAAW,CAAC,MAAC,EAAO;YA9DvD,uBA+DM,EAAA,GAAK,SAAA,CAAU,SAAC,CAAS,MAAC,GAAQ,CAAA,CAAE,CAAC;YA9D3C,IAAI,CA+DC,EAAC,CAAE,uBAAC,EAAuB,EAAG;gBA9DjC,EAAE,CA+DC,SAAC,CAAS,CAAC,WAAC,CAAW,EAAE,IAAA,EAAM,OAAA,CAAQ,MAAC,EAAO,OAAA,CAAQ,CAAC;aA9D5D;SACF;QAED,OA+DO,SAAA,CAAU,MAAC,GAAQ,SAAA,CAAU,GAAC,CAAG,QAAC,IAAW,QAAA,CAAS,cAAC,EAAc,CAAE;YA9DpD,CAAC,yBA+DC,CAAyB,WAAC,EAAY,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,CAAA,EAAG,EAAA,EAAI,KAAA,CAAM,CAAC,CAAC;KA9DjG;;;;;;IAMH,YA2DG,CAAA,GAAA,EAAA,OAAA,EA3DH;;KAEG;;;;;;IAMH,UAuDG,CAAA,GAAA,EAAA,OAAA,EAvDH;;KAEG;;;;;;IAMH,eAmDG,CAAA,GAAA,EAAA,OAAA,EAnDH;;KAEG;;;;;;IAMH,iBA+CG,CAAA,GAAA,EAAA,OAAA,EA/CH;QACI,uBA+CM,mBAAA,GAAsB,OAAA,CAAQ,eAAC,CAAe,OAAC,CAAO,OAAC,CAAO,OAAC,CAAO,CAAC;QA9C7E,IAAI,mBA+CC,EAAoB;YA9CvB,uBA+CM,YAAA,GAAe,OAAA,CAAQ,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;YA9C3D,uBA+CM,SAAA,GAAY,OAAA,CAAQ,eAAC,CAAe,WAAC,CAAW;YA9CtD,uBA+CM,OAAA,GAAU,IAAA,CAAK,qBAAC,CA9ClB,mBAAmB,EA+CE,YAAA,oBAAc,YAAA,CAAa,OAAW,EAAoB,CAAC;YA9CpF,IAAI,SA+CC,IAAY,OAAA,EAAS;;;gBA5CxB,OAAO,CA+CC,wBAAC,CAAwB,OAAC,CAAO,CAAC;aA9C3C;SACF;QACD,OAAO,CA+CC,YAAC,GAAc,GAAA,CAAI;KA9C5B;;;;;;IAMH,eA2CG,CAAA,GAAA,EAAA,OAAA,EA3CH;QACI,uBA2CM,YAAA,GAAe,OAAA,CAAQ,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;QA1C3D,YAAY,CA2CC,wBAAC,EAAwB,CAAE;QA1CxC,IAAI,CA2CC,cAAC,CAAc,GAAC,CAAG,SAAC,EAAU,YAAA,CAAa,CAAC;QA1CjD,OAAO,CA2CC,wBAAC,CAAwB,YAAC,CAAY,eAAC,CAAe,WAAC,CAAW,CAAC;QA1C3E,OAAO,CA2CC,YAAC,GAAc,GAAA,CAAI;KA1C5B;;;;;;;IA6CA,qBAAA,CArCG,YAA4C,EAAE,OAAiC,EAC/E,OAA4B,EAoClC;QAnCI,uBAsCM,SAAA,GAAY,OAAA,CAAQ,eAAC,CAAe,WAAC,CAAW;QArCtD,qBAsCI,YAAA,GAAe,SAAA,CAAU;;;QAlC7B,uBAsCM,QAAA,GAAW,OAAA,CAAQ,QAAC,IAAW,IAAA,GAAO,kBAAA,CAAmB,OAAC,CAAO,QAAC,CAAQ,GAAG,IAAA,CAAK;QArCxF,uBAsCM,KAAA,GAAQ,OAAA,CAAQ,KAAC,IAAQ,IAAA,GAAO,kBAAA,CAAmB,OAAC,CAAO,KAAC,CAAK,GAAG,IAAA,CAAK;QArC/E,IAAI,QAsCC,KAAY,CAAA,EAAG;YArClB,YAAY,CAsCC,OAAC,CAAO,WAAC,IAtC5B;gBACQ,uBAsCM,kBAAA,GArCF,OAAO,CAsCC,2BAAC,CAA2B,WAAC,EAAY,QAAA,EAAU,KAAA,CAAM,CAAC;gBArCtE,YAAY;oBACR,IAAI,CAsCC,GAAC,CAAG,YAAC,EAAa,kBAAA,CAAmB,QAAC,GAAU,kBAAA,CAAmB,KAAC,CAAK,CAAC;aArCpF,CAsCC,CAAC;SArCJ;QAED,OAsCO,YAAA,CAAa;KArCrB;;;;;;IAMH,cAkCG,CAAA,GAAA,EAAA,OAAA,EAlCH;QACI,OAAO,CAkCC,aAAC,CAAa,GAAC,CAAG,OAAC,EAAQ,IAAA,CAAK,CAAC;QAjCzC,GAAG,CAkCC,SAAC,CAAS,KAAC,CAAK,IAAC,EAAK,OAAA,CAAQ,CAAC;QAjCnC,OAAO,CAkCC,YAAC,GAAc,GAAA,CAAI;KAjC5B;;;;;;IAMH,aA8BG,CAAA,GAAA,EAAA,OAAA,EA9BH;QACI,uBA8BM,eAAA,GAAkB,OAAA,CAAQ,eAAC,CAAe;QA7BhD,qBA8BI,GAAA,GAAM,OAAA,CAAQ;QA7BlB,uBA8BM,OAAA,GAAU,GAAA,CAAI,OAAC,CAAO;QA5B5B,IAAI,OA8BC,KAAU,OAAE,CAAO,MAAC,IAAS,OAAA,CAAQ,KAAC,CAAK,EAAE;YA7BhD,GAAG,GA8BG,OAAA,CAAQ,gBAAC,CAAgB,OAAC,CAAO,CAAC;YA7BxC,GAAG,CA8BC,wBAAC,EAAwB,CAAE;YA5B/B,IAAI,OA8BC,CAAO,KAAC,IAAQ,IAAA,EAAM;gBA7BzB,IAAI,GA8BC,CAAG,YAAC,YAAuB,QAAA,EAAU;oBA7BxC,GAAG,CA8BC,eAAC,CAAe,qBAAC,EAAqB,CAAE;oBA7B5C,GAAG,CA8BC,YAAC,GAAc,0BAAA,CAA2B;iBA7B/C;gBAED,uBA8BM,KAAA,GAAQ,kBAAA,CAAmB,OAAC,CAAO,KAAC,CAAK,CAAC;gBA7BhD,GAAG,CA8BC,aAAC,CAAa,KAAC,CAAK,CAAC;aA7B1B;SACF;QAED,IAAI,GA8BC,CAAG,KAAC,CAAK,MAAC,EAAO;YA7BpB,GAAG,CA8BC,KAAC,CAAK,OAAC,CAAO,CAAC,IAAI,CAAA,CAAE,KAAC,CAAK,IAAC,EAAK,GAAA,CAAI,CAAC,CAAC;;YA3B3C,GAAG,CA8BC,eAAC,CAAe,qBAAC,EAAqB,CAAE;;;;YAzB5C,IAAI,GA8BC,CAAG,eAAC,GAAiB,eAAA,EAAiB;gBA7BzC,GAAG,CA8BC,wBAAC,EAAwB,CAAE;aA7BhC;SACF;QAED,OAAO,CA8BC,YAAC,GAAc,GAAA,CAAI;KA7B5B;;;;;;IAMH,UA0BG,CAAA,GAAA,EAAA,OAAA,EA1BH;QACI,uBA0BM,cAAA,GAAoC,EAAA,CAAG;QAzB7C,qBA0BI,YAAA,GAAe,OAAA,CAAQ,eAAC,CAAe,WAAC,CAAW;QAzBvD,uBA0BM,KAAA,GAAQ,GAAA,CAAI,OAAC,IAAU,GAAA,CAAI,OAAC,CAAO,KAAC,GAAO,kBAAA,CAAmB,GAAC,CAAG,OAAC,CAAO,KAAC,CAAK,GAAG,CAAA,CAAE;QAxB3F,GAAG,CA0BC,KAAC,CAAK,OAAC,CAAO,CAAC,IA1BvB;YACM,uBA0BM,YAAA,GAAe,OAAA,CAAQ,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;YAzB3D,IAAI,KA0BC,EAAM;gBAzBT,YAAY,CA0BC,aAAC,CAAa,KAAC,CAAK,CAAC;aAzBnC;YAED,CAAC,CA0BC,KAAC,CAAK,IAAC,EAAK,YAAA,CAAa,CAAC;YAzB5B,YAAY,GA0BG,IAAA,CAAK,GAAC,CAAG,YAAC,EAAa,YAAA,CAAa,eAAC,CAAe,WAAC,CAAW,CAAC;YAzBhF,cAAc,CA0BC,IAAC,CAAI,YAAC,CAAY,eAAC,CAAe,CAAC;SAzBnD,CA0BC,CAAC;;;;QArBH,cAAc,CA0BC,OAAC,CAzBZ,QAAQ,IA0BI,OAAA,CAAQ,eAAC,CAAe,4BAAC,CAA4B,QAAC,CAAQ,CAAC,CAAC;QAzBhF,OAAO,CA0BC,wBAAC,CAAwB,YAAC,CAAY,CAAC;QAzB/C,OAAO,CA0BC,YAAC,GAAc,GAAA,CAAI;KAzB5B;;;;;;IAMH,WAsBG,CAAA,GAAA,EAAA,OAAA,EAtBH;QACI,IAAI,GAsBC,YAAc,gBAAA,EAAkB;YArBnC,uBAsBM,QAAA,GAAW,OAAA,CAAQ,MAAC;gBArBtB,iBAAiB,CAsBC,GAAC,CAAG,KAAC,EAAM,OAAA,CAAQ,MAAC,EAAO,OAAA,CAAQ,MAAC,CAAM;gBArB5D,GAAG,CAsBC,KAAC,CAAK,QAAC,EAAQ,CAAE;YArBzB,OAsBO,aAAA,CAAc,QAAC,EAAS,OAAA,CAAQ,MAAC,CAAM,CAAC;SArBhD;aAsBM;YArBL,OAsBO,EAAA,QAAE,EAAS,GAAA,CAAI,QAAC,EAAS,KAAA,EAAO,GAAA,CAAI,KAAC,EAAM,MAAA,EAAQ,GAAA,CAAI,MAAC,EAAM,CAAC;SArBvE;KACF;;;;;;IAMH,YAkBG,CAAA,GAAA,EAAA,OAAA,EAlBH;QACI,uBAkBM,OAAA,GAAU,OAAA,CAAQ,qBAAC,GAAuB,IAAA,CAAK,WAAC,CAAW,GAAC,CAAG,OAAC,EAAQ,OAAA,CAAQ,CAAC;QAjBvF,uBAkBM,QAAA,GAAW,OAAA,CAAQ,eAAC,CAAe;QAjBzC,IAAI,OAkBC,CAAO,KAAC,EAAM;YAjBjB,OAAO,CAkBC,aAAC,CAAa,OAAC,CAAO,KAAC,CAAK,CAAC;YAjBrC,QAAQ,CAkBC,qBAAC,EAAqB,CAAE;SAjBlC;QAED,uBAkBMA,QAAA,GAAQ,GAAA,CAAI,KAAC,CAAK;QAjBxB,IAAIA,QAkBC,YAAgB,YAAA,EAAc;YAjBjC,IAAI,CAkBC,cAAC,CAAcA,QAAC,EAAM,OAAA,CAAQ,CAAC;SAjBrC;aAkBM;YAjBL,OAAO,CAkBC,aAAC,CAAa,OAAC,CAAO,QAAC,CAAQ,CAAC;YAjBxC,IAAI,CAkBC,UAAC,mBAAUA,QAAS,GAAU,OAAA,CAAQ,CAAC;YAjB5C,QAAQ,CAkBC,qBAAC,EAAqB,CAAE;SAjBlC;QAED,OAAO,CAkBC,qBAAC,GAAuB,IAAA,CAAK;QAjBrC,OAAO,CAkBC,YAAC,GAAc,GAAA,CAAI;KAjB5B;;;;;;IAMH,UAcG,CAAA,GAAA,EAAA,OAAA,EAdH;QACI,uBAcM,QAAA,GAAW,OAAA,CAAQ,eAAC,CAAe;QAbzC,uBAcM,OAAA,KAAU,OAAA,CAAQ,qBAAC,EAAA,CAAuB;;;QAVhD,IAAI,CAcC,OAAC,IAAU,QAAA,CAAS,yBAAC,EAAyB,CAAE,MAAC,EAAO;YAb3D,QAAQ,CAcC,YAAC,EAAY,CAAE;SAbzB;QAED,uBAcM,MAAA,GAAS,CAAA,OAAE,IAAU,OAAA,CAAQ,MAAC,KAAU,GAAA,CAAI,MAAC,CAAM;QAbzD,IAAI,GAcC,CAAG,WAAC,EAAY;YAbnB,QAAQ,CAcC,cAAC,CAAc,MAAC,CAAM,CAAC;SAbjC;aAcM;YAbL,QAAQ,CAcC,SAAC,CAAS,GAAC,CAAG,MAAC,EAAO,MAAA,EAAQ,OAAA,CAAQ,MAAC,EAAO,OAAA,CAAQ,OAAC,CAAO,CAAC;SAbzE;QAED,OAAO,CAcC,YAAC,GAAc,GAAA,CAAI;KAb5B;;;;;;IAMH,cAUG,CAAA,GAAA,EAAA,OAAA,EAVH;QACI,uBAUM,qBAAA,KAAwB,OAAA,CAAQ,qBAAC,EAAA,CAAuB;QAT9D,uBAUM,SAAA,GAAY,GAAA,OAAE,CAAO,eAAC,IAAkB,QAAC,CAAQ;QATvD,uBAUM,QAAA,GAAW,qBAAA,CAAsB,QAAC,CAAQ;QAThD,uBAUM,YAAA,GAAe,OAAA,CAAQ,gBAAC,EAAgB,CAAE;QAThD,uBAUM,aAAA,GAAgB,YAAA,CAAa,eAAC,CAAe;QATnD,aAAa,CAUC,MAAC,GAAQ,qBAAA,CAAsB,MAAC,CAAM;QARpD,GAAG,CAUC,MAAC,CAAM,OAAC,CAAO,IAAC,IAVxB;YACM,uBAUM,MAAA,GAAiB,IAAA,CAAK,MAAC,IAAS,CAAA,CAAE;YATxC,aAAa,CAUC,WAAC,CAAW,MAAC,GAAQ,QAAA,CAAS,CAAC;YAT7C,aAAa,CAUC,SAAC,CAAS,IAAC,CAAI,MAAC,EAAO,IAAA,CAAK,MAAC,EAAO,OAAA,CAAQ,MAAC,EAAO,OAAA,CAAQ,OAAC,CAAO,CAAC;YATnF,aAAa,CAUC,qBAAC,EAAqB,CAAE;SATvC,CAUC,CAAC;;;QANH,OAAO,CAUC,eAAC,CAAe,4BAAC,CAA4B,aAAC,CAAa,CAAC;;;QANpE,OAAO,CAUC,wBAAC,CAAwB,SAAC,GAAW,QAAA,CAAS,CAAC;QATvD,OAAO,CAUC,YAAC,GAAc,GAAA,CAAI;KAT5B;;;;;;IAMH,UAMG,CAAA,GAAA,EAAA,OAAA,EANH;;;QAGI,uBAMM,SAAA,GAAY,OAAA,CAAQ,eAAC,CAAe,WAAC,CAAW;QALtD,uBAMM,OAAA,KAAU,GAAE,CAAG,OAAC,IAAU,EAAA,EAAO,CAAsB;QAL7D,uBAMM,KAAA,GAAQ,OAAA,CAAQ,KAAC,GAAO,kBAAA,CAAmB,OAAC,CAAO,KAAC,CAAK,GAAG,CAAA,CAAE;QAJpE,IAAI,KAMC,KAAQ,OAAE,CAAO,YAAC,YAAuB,QAAA;aAL/B,SAMC,IAAY,CAAA,IAAK,OAAA,CAAQ,eAAC,CAAe,yBAAC,EAAyB,CAAE,MAAC,CAAM,CAAC,EAAE;YAL7F,OAAO,CAMC,eAAC,CAAe,qBAAC,EAAqB,CAAE;YALhD,OAAO,CAMC,YAAC,GAAc,0BAAA,CAA2B;SALnD;QAED,qBAMI,YAAA,GAAe,SAAA,CAAU;QAL7B,uBAMM,IAAA,GAAO,OAAA,CAAQ,WAAC,CALlB,GAAG,CAMC,QAAC,EAAS,GAAA,CAAI,gBAAC,EAAiB,GAAA,CAAI,KAAC,EAAM,GAAA,CAAI,WAAC,EALpD,OAAO,CAMC,QAAC,GAAU,IAAA,GAAO,KAAA,EAAO,OAAA,CAAQ,MAAC,CAAM,CAAC;QAJrD,OAAO,CAMC,iBAAC,GAAmB,IAAA,CAAK,MAAC,CAAM;QALxC,qBAMI,mBAAA,GAA4C,IAAA,CAAK;QALrD,IAAI,CAMC,OAAC,CAAO,CAAC,OAAC,EAAQ,CAAA,KAN3B;YAEM,OAAO,CAMC,iBAAC,GAAmB,CAAA,CAAE;YAL9B,uBAMM,YAAA,GAAe,OAAA,CAAQ,gBAAC,CAAgB,GAAC,CAAG,OAAC,EAAQ,OAAA,CAAQ,CAAC;YALpE,IAAI,KAMC,EAAM;gBALT,YAAY,CAMC,aAAC,CAAa,KAAC,CAAK,CAAC;aALnC;YAED,IAAI,OAMC,KAAW,OAAA,CAAQ,OAAC,EAAQ;gBAL/B,mBAAmB,GAMG,YAAA,CAAa,eAAC,CAAe;aALpD;YAED,GAAG,CAMC,SAAC,CAAS,KAAC,CAAK,IAAC,EAAK,YAAA,CAAa,CAAC;;;;YADxC,YAAY,CAMC,eAAC,CAAe,qBAAC,EAAqB,CAAE;YAJrD,uBAMM,OAAA,GAAU,YAAA,CAAa,eAAC,CAAe,WAAC,CAAW;YALzD,YAAY,GAMG,IAAA,CAAK,GAAC,CAAG,YAAC,EAAa,OAAA,CAAQ,CAAC;SALhD,CAMC,CAAC;QAJH,OAAO,CAMC,iBAAC,GAAmB,CAAA,CAAE;QAL9B,OAAO,CAMC,iBAAC,GAAmB,CAAA,CAAE;QAL9B,OAAO,CAMC,wBAAC,CAAwB,YAAC,CAAY,CAAC;QAJ/C,IAAI,mBAMC,EAAoB;YALvB,OAAO,CAMC,eAAC,CAAe,4BAAC,CAA4B,mBAAC,CAAmB,CAAC;YAL1E,OAAO,CAMC,eAAC,CAAe,qBAAC,EAAqB,CAAE;SALjD;QAED,OAAO,CAMC,YAAC,GAAc,GAAA,CAAI;KAL5B;;;;;;IAMH,YAEG,CAAA,GAAA,EAAA,OAAA,EAFH;QACI,uBAEM,aAAA,KAAgB,OAAA,CAAQ,aAAC,EAAA,CAAe;QAD9C,uBAEM,EAAA,GAAK,OAAA,CAAQ,eAAC,CAAe;QADnC,uBAEM,OAAA,GAAU,GAAA,CAAI,OAAC,CAAO;QAD5B,uBAEM,QAAA,GAAW,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,QAAC,CAAQ,CAAC;QAD5C,uBAEM,OAAA,GAAU,QAAA,IAAW,OAAE,CAAO,iBAAC,GAAmB,CAAA,CAAE,CAAC;QAD3D,qBAEI,KAAA,GAAQ,QAAA,GAAW,OAAA,CAAQ,iBAAC,CAAiB;QAAjD,qBAEI,kBAAA,GAAqB,OAAA,CAAQ,QAAC,GAAU,CAAA,GAAI,SAAA,GAAY,OAAA,CAAQ,MAAC,CAAM;QAD3E,QAAQ,kBAEC;YADP,KAEK,SAAA;gBADH,KAAK,GAEG,OAAA,GAAU,KAAA,CAAM;gBADxB,MAAM;YACR,KAEK,MAAA;gBADH,KAAK,GAEG,aAAA,CAAc,kBAAC,CAAkB;gBADzC,MAAM;SACT;QAED,uBAEM,QAAA,GAAW,OAAA,CAAQ,eAAC,CAAe;QADzC,IAAI,KAEC,EAAM;YADT,QAAQ,CAEC,aAAC,CAAa,KAAC,CAAK,CAAC;SAD/B;QAED,uBAEM,YAAA,GAAe,QAAA,CAAS,WAAC,CAAW;QAD1C,GAAG,CAEC,SAAC,CAAS,KAAC,CAAK,IAAC,EAAK,OAAA,CAAQ,CAAC;QADnC,OAAO,CAEC,YAAC,GAAc,GAAA,CAAI;;;;;QAI3B,aAAa,CAEC,kBAAC;YADX,CAAC,EAEC,CAAE,WAAC,GAAa,YAAA,KAAgB,EAAE,CAAE,SAAC,GAAW,aAAA,CAAc,eAAC,CAAe,SAAC,CAAS,CAAC;KADhG;CACF;AAMD,MAEM,0BAAA,IAAkC,EAAC,CAAA,CAAE;AAD3C,AAAA,MAAA,wBAAA,CAAA;;;;;;;;;IAkBA,WAAA,CAJc,OAAS,EAAwB,OAAS,EAC3C,eAAiB,EAA8B,MAAY,EAC3D,SAA2B,EAAG,eAAkB,EAE7D;QAJc,IAAd,CAAA,OAAc,GAAA,OAAA,CAAS;QAAwB,IAA/C,CAAA,OAA+C,GAAA,OAAA,CAAS;QAC3C,IAAb,CAAA,eAAa,GAAA,eAAA,CAAiB;QAA8B,IAA5D,CAAA,MAA4D,GAAA,MAAA,CAAY;QAC3D,IAAb,CAAA,SAAa,GAAA,SAAA,CAA2B;QAb/B,IAAT,CAAA,aAAS,GAA+C,IAAA,CAAK;QAEpD,IAAT,CAAA,qBAAS,GAA6C,IAAA,CAAK;QAClD,IAAT,CAAA,YAAS,GAAoB,0BAAA,CAA2B;QAC/C,IAAT,CAAA,eAAS,GAAkB,CAAA,CAAE;QACpB,IAAT,CAAA,OAAS,GAA4B,EAAA,CAAG;QAC/B,IAAT,CAAA,iBAAS,GAA4B,CAAA,CAAE;QAC9B,IAAT,CAAA,iBAAS,GAA4B,CAAA,CAAE;QAC9B,IAAT,CAAA,kBAAS,GAA6B,CAAA,CAAE;QAapC,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC1E,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACtC;;;;IAIH,IATG,MAAA,GASH,EATiB,OAAO,IAAA,CAAK,OAAC,CAAO,MAAC,CAAM,EAAC;;;;;;IAe7C,aAbG,CAAA,OAAA,EAAA,YAAA,EAaH;QACI,IAAI,CAbC,OAAC;YAAQ,OAAA;QAed,uBAbM,UAAA,IAAa,OAAW,CAAA,CAAI;QAclC,qBAbI,eAAA,GAAkB,IAAA,CAAK,OAAC,CAAO;;QAgBnC,IAAI,UAbC,CAAU,QAAC,IAAW,IAAA,EAAM;YAc/B,EAAoB,eAbA,GAAK,QAAC,GAAU,kBAAA,CAAmB,UAAC,CAAU,QAAC,CAAQ,CAAC;SAc7E;QAED,IAAI,UAbC,CAAU,KAAC,IAAQ,IAAA,EAAM;YAc5B,eAAe,CAbC,KAAC,GAAO,kBAAA,CAAmB,UAAC,CAAU,KAAC,CAAK,CAAC;SAc9D;QAED,uBAbM,SAAA,GAAY,UAAA,CAAW,MAAC,CAAM;QAcpC,IAAI,SAbC,EAAU;YAcb,qBAbI,cAAA,KAAwC,eAAA,CAAgB,MAAC,EAAA,CAAQ;YAcrE,IAAI,CAbC,cAAC,EAAe;gBAcnB,cAAc,GAbG,IAAA,CAAK,OAAC,CAAO,MAAC,GAAQ,EAAA,CAAG;aAc3C;YAED,MAAM,CAbC,IAAC,CAAI,SAAC,CAAS,CAAC,OAAC,CAAO,IAAC,IAatC;gBACQ,IAAI,CAbC,YAAC,IAAe,CAAA,cAAE,CAAc,cAAC,CAAc,IAAC,CAAI,EAAE;oBAczD,cAAc,CAbC,IAAC,CAAI,GAAG,iBAAA,CAAkB,SAAC,CAAS,IAAC,CAAI,EAAE,cAAA,EAAgB,IAAA,CAAK,MAAC,CAAM,CAAC;iBAcxF;aACF,CAbC,CAAC;SAcJ;KACF;;;;IAXA,YAAA,GAAH;QAgBI,uBAfM,OAAA,GAA4B,EAAA,CAAG;QAgBrC,IAAI,IAfC,CAAI,OAAC,EAAQ;YAgBhB,uBAfM,SAAA,GAAY,IAAA,CAAK,OAAC,CAAO,MAAC,CAAM;YAgBtC,IAAI,SAfC,EAAU;gBAgBb,uBAfM,MAAA,GAAgC,OAAA,CAAQ,QAAC,CAAQ,GAAG,EAAA,CAAG;gBAgB7D,MAAM,CAfC,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,MAAC,CAAM,CAAC,OAAC,CAAO,IAAC,IAelD,EAf2D,MAAA,CAAO,IAAC,CAAI,GAAG,SAAA,CAAU,IAAC,CAAI,CAAC,EAAC,CAAE,CAAC;aAgBvF;SACF;QACD,OAfO,OAAA,CAAQ;KAgBhB;;;;;;;IAOH,gBApBG,CAAA,OAoBH,GApBG,IAAA,EAAA,OAAA,EAAA,OAAA,EAoBH;QAEI,uBApBM,MAAA,GAAS,OAAA,IAAW,IAAA,CAAK,OAAC,CAAO;QAqBvC,uBApBM,OAAA,GAAU,IAAI,wBAAA,CAqBhB,IAAI,CApBC,OAAC,EAAQ,MAAA,EAAQ,IAAA,CAAK,eAAC,EAAgB,IAAA,CAAK,MAAC,EAAO,IAAA,CAAK,SAAC,EAqB/D,IAAI,CApBC,eAAC,CAAe,IAAC,CAAI,MAAC,EAAO,OAAA,IAAW,CAAA,CAAE,CAAC,CAAC;QAqBrD,OAAO,CApBC,YAAC,GAAc,IAAA,CAAK,YAAC,CAAY;QAqBzC,OAAO,CApBC,qBAAC,GAAuB,IAAA,CAAK,qBAAC,CAAqB;QAsB3D,OAAO,CApBC,OAAC,GAAS,IAAA,CAAK,YAAC,EAAY,CAAE;QAqBtC,OAAO,CApBC,aAAC,CAAa,OAAC,CAAO,CAAC;QAsB/B,OAAO,CApBC,iBAAC,GAAmB,IAAA,CAAK,iBAAC,CAAiB;QAqBnD,OAAO,CApBC,iBAAC,GAAmB,IAAA,CAAK,iBAAC,CAAiB;QAqBnD,OAAO,CApBC,aAAC,GAAe,IAAA,CAAK;QAqB7B,IAAI,CApBC,eAAC,EAAe,CAAE;QAqBvB,OApBO,OAAA,CAAQ;KAqBhB;;;;;IAKH,wBAvBG,CAAA,OAAA,EAuBH;QACI,IAAI,CAvBC,YAAC,GAAc,0BAAA,CAA2B;QAwB/C,IAAI,CAvBC,eAAC,GAAiB,IAAA,CAAK,eAAC,CAAe,IAAC,CAAI,IAAC,CAAI,OAAC,EAAQ,OAAA,CAAQ,CAAC;QAwBxE,IAAI,CAvBC,SAAC,CAAS,IAAC,CAAI,IAAC,CAAI,eAAC,CAAe,CAAC;QAwB1C,OAvBO,IAAA,CAAK,eAAC,CAAe;KAwB7B;;;;;;;IAOH,2BA5BG,CA6BG,WAAyC,EAAE,QAAqB,EAChE,KAAkB,EAFxB;QAGI,uBA5BM,cAAA,GAAiC;YA6BrC,QAAQ,EA5BE,QAAA,IAAY,IAAA,GAAO,QAAA,GAAW,WAAA,CAAY,QAAC;YA6BrD,KAAK,EA5BE,IAAA,CAAK,eAAC,CAAe,WAAC,IAAa,KAAE,IAAQ,IAAA,GAAO,KAAA,GAAQ,CAAA,CAAE,GAAG,WAAA,CAAY,KAAC;YA6BrF,MAAM,EA5BE,EAAA;SA6BT,CA5BC;QA6BF,uBA5BM,OAAA,GAAU,IAAI,kBAAA,CA6BhB,WAAW,CA5BC,OAAC,EAAQ,WAAA,CAAY,SAAC,EAAU,WAAA,CAAY,aAAC,EA6BzD,WAAW,CA5BC,cAAC,EAAe,cAAA,EAAgB,WAAA,CAAY,uBAAC,CAAuB,CAAC;QA6BrF,IAAI,CA5BC,SAAC,CAAS,IAAC,CAAI,OAAC,CAAO,CAAC;QA6B7B,OA5BO,cAAA,CAAe;KA6BvB;;;;;IAKH,aA/BG,CAAA,IAAA,EA+BH;QACI,IAAI,CA/BC,eAAC,CAAe,WAAC,CAAW,IAAC,CAAI,eAAC,CAAe,QAAC,GAAU,IAAA,CAAK,CAAC;KAgCxE;;;;;IAKH,aAlCG,CAAA,KAAA,EAkCH;;QAEI,IAAI,KAlCC,GAAO,CAAA,EAAG;YAmCb,IAAI,CAlCC,eAAC,CAAe,aAAC,CAAa,KAAC,CAAK,CAAC;SAmC3C;KACF;;;;;;;;;;IAUH,WA1CG,CA2CG,QAAgB,EAAE,gBAAwB,EAAE,KAAa,EAAE,WAAoB,EAC/E,QAAiB,EAAE,MAAa,EAFtC;QAGI,qBA1CI,OAAA,GAAiB,EAAA,CAAG;QA2CxB,IAAI,WA1CC,EAAY;YA2Cf,OAAO,CA1CC,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;SA2C5B;QACD,IAAI,QA1CC,CAAQ,MAAC,GAAQ,CAAA,EAAG;YA2CvB,uBA1CM,KAAA,GAAQ,KAAA,IAAS,CAAA,CAAE;YA2CzB,OAAO,CA1CC,IAAC,CAAI,GAAC,IAAG,CAAI,OAAC,CAAO,KAAC,CAAK,IAAC,CAAI,OAAC,EAAQ,QAAA,EAAU,KAAA,CAAM,CAAC,CAAC;SA2CpE;QAED,IAAI,CA1CC,QAAC,IAAW,OAAA,CAAQ,MAAC,IAAS,CAAA,EAAG;YA2CpC,MAAM,CA1CC,IAAC,CA2CJ,CADV,SAAA,EACsB,gBA1CC,CAyCvB,2CAAA,EAzCuC,gBAA8C,CAyCrF,oDAAA,CAzCqG,CAAsD,CAAC;SA2CvJ;QACD,OA1CO,OAAA,CAAQ;KA2ChB;CACF;AAED,AA+BA,AAAA,MAAA,eAAA,CAAA;;;;;;IAiBA,WAAA,CA3Ea,OAAS,EAAY,SAAW,EAC/B,4BAAmD,EA0EjE;QA3Ea,IAAb,CAAA,OAAa,GAAA,OAAA,CAAS;QAAY,IAAlC,CAAA,SAAkC,GAAA,SAAA,CAAW;QAC/B,IAAd,CAAA,4BAAc,GAAA,4BAAA,CAAmD;QAdxD,IAAT,CAAA,QAAS,GAAmB,CAAA,CAAE;QAEpB,IAAV,CAAA,iBAAU,GAAgC,EAAA,CAAG;QACnC,IAAV,CAAA,gBAAU,GAA+B,EAAA,CAAG;QAClC,IAAV,CAAA,UAAU,GAAa,IAAI,GAAA,EAAuB,CAAG;QAC3C,IAAV,CAAA,aAAU,GAA+C,EAAA,CAAG;QAGlD,IAAV,CAAA,cAAU,GAA6B,EAAA,CAAG;QAChC,IAAV,CAAA,SAAU,GAAwB,EAAA,CAAG;QAC3B,IAAV,CAAA,yBAAU,GAA6C,IAAA,CAAK;QAkFxD,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACtC,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAmB,CAAC;SAChE;QAED,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC;QAC9E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;;;IAIH,iBA/EG,GA+EH;QACI,QAAQ,IA/EC,CAAI,UAAC,CAAU,IAAC;YAgFvB,KA/EK,CAAA;gBAgFH,OA/EO,KAAA,CAAM;YAgFf,KA/EK,CAAA;gBAgFH,OA/EO,IAAA,CAAK,yBAAC,EAAyB,CAAE,MAAC,GAAQ,CAAA,CAAE;YAgFrD;gBACE,OA/EO,IAAA,CAAK;SAgFf;KACF;;;;IAIH,yBAjFG,GAiFH,EAjF0C,OAAO,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,gBAAC,CAAgB,CAAC,EAAC;;;;IAqFrF,IAnFG,WAAA,GAmFH,EAnFsB,OAAO,IAAA,CAAK,SAAC,GAAW,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;;IAwF7D,aAtFG,CAAA,KAAA,EAsFH;;;;;QAKI,uBAtFM,eAAA,GAAkB,IAAA,CAAK,UAAC,CAAU,IAAC,IAAO,CAAA,IAAK,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,cAAC,CAAc,CAAC,MAAC,CAAM;QAwF7F,IAAI,IAtFC,CAAI,QAAC,IAAW,eAAA,EAAiB;YAuFpC,IAAI,CAtFC,WAAC,CAAW,IAAC,CAAI,WAAC,GAAa,KAAA,CAAM,CAAC;YAuF3C,IAAI,eAtFC,EAAgB;gBAuFnB,IAAI,CAtFC,qBAAC,EAAqB,CAAE;aAuF9B;SACF;aAtFM;YAuFL,IAAI,CAtFC,SAAC,IAAY,KAAA,CAAM;SAuFzB;KACF;;;;;;IAMH,IA1FG,CAAA,OAAA,EAAA,WAAA,EA0FH;QACI,IAAI,CA1FC,qBAAC,EAAqB,CAAE;QA2F7B,OA1FO,IAAI,eAAA,CA2FP,OAAO,EA1FE,WAAA,IAAe,IAAA,CAAK,WAAC,EAAY,IAAA,CAAK,4BAAC,CAA4B,CAAC;KA2FlF;;;;IAxFA,aAAA,GAAH;QA6FI,IAAI,IA5FC,CAAI,gBAAC,EAAiB;YA6FzB,IAAI,CA5FC,iBAAC,GAAmB,IAAA,CAAK,gBAAC,CAAgB;SA6FhD;QACD,IAAI,CA5FC,gBAAC,KAAkB,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,IAAC,CAAI,QAAC,CAAQ,EAAA,CAAG;QA6F7D,IAAI,CA5FC,IAAC,CAAI,gBAAC,EAAiB;YA6F1B,IAAI,CA5FC,gBAAC,GAAkB,MAAA,CAAO,MAAC,CAAM,IAAC,CAAI,SAAC,EAAU,EAAA,CAAG,CAAC;YA6F1D,IAAI,CA5FC,UAAC,CAAU,GAAC,CAAG,IAAC,CAAI,QAAC,EAAS,IAAA,CAAK,gBAAC,CAAgB,CAAC;SA6F3D;KACF;;;;IAIH,YA9FG,GA8FH;QACI,IAAI,CA9FC,QAAC,IAAW,yBAAA,CAA0B;QA+F3C,IAAI,CA9FC,aAAC,EAAa,CAAE;KA+FtB;;;;;IAKH,WAjGG,CAAA,IAAA,EAiGH;QACI,IAAI,CAjGC,qBAAC,EAAqB,CAAE;QAkG7B,IAAI,CAjGC,QAAC,GAAU,IAAA,CAAK;QAkGrB,IAAI,CAjGC,aAAC,EAAa,CAAE;KAkGtB;;;;;;IA/FA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAH;QAsGI,IAAI,CArGC,oBAAC,CAAoB,IAAC,CAAI,GAAG,KAAA,CAAM;QAsGxC,IAAI,CArGC,qBAAC,CAAqB,IAAC,CAAI,GAAG,KAAA,CAAM;QAsGzC,IAAI,CArGC,aAAC,CAAa,IAAC,CAAI,GAAG,EAAA,IAAE,EAAK,IAAA,CAAK,WAAC,EAAY,KAAA,EAAM,CAAC;KAsG5D;;;;IAIH,uBAvGG,GAuGH,EAvG8B,OAAO,IAAA,CAAK,yBAAC,KAA6B,IAAA,CAAK,gBAAC,CAAgB,EAAC;;;;;IA4G/F,cA1GG,CAAA,MAAA,EA0GH;QACI,IAAI,MA1GC,EAAO;YA2GV,IAAI,CA1GC,iBAAC,CAAiB,QAAC,CAAQ,GAAG,MAAA,CAAO;SA2G3C;;;;;;;QAQD,MAAM,CA1GC,IAAC,CAAI,IAAC,CAAI,qBAAC,CAAqB,CAAC,OAAC,CAAO,IAAC,IA0GrD;YACM,IAAI,CA1GC,SAAC,CAAS,IAAC,CAAI,GAAG,IAAA,CAAK,qBAAC,CAAqB,IAAC,CAAI,IAAI,UAAA,CAAW;YA2GtE,IAAI,CA1GC,gBAAC,CAAgB,IAAC,CAAI,GAAG,UAAA,CAAW;SA2G1C,CA1GC,CAAC;QA2GH,IAAI,CA1GC,yBAAC,GAA2B,IAAA,CAAK,gBAAC,CAAgB;KA2GxD;;;;;;;;IAQH,SAhHG,CAiHG,KAA4B,EAAE,MAAmB,EAAE,MAAa,EAChE,OAA0B,EAFhC;QAGI,IAAI,MAhHC,EAAO;YAiHV,IAAI,CAhHC,iBAAC,CAAiB,QAAC,CAAQ,GAAG,MAAA,CAAO;SAiH3C;QAED,uBAhHM,MAAA,GAAS,CAAA,OAAE,IAAU,OAAA,CAAQ,MAAC,KAAU,EAAA,CAAG;QAiHjD,uBAhHM,MAAA,GAAS,aAAA,CAAc,KAAC,EAAM,IAAA,CAAK,qBAAC,CAAqB,CAAC;QAiHhE,MAAM,CAhHC,IAAC,CAAI,MAAC,CAAM,CAAC,OAAC,CAAO,IAAC,IAgHjC;YACM,uBAhHM,GAAA,GAAM,iBAAA,CAAkB,MAAC,CAAM,IAAC,CAAI,EAAE,MAAA,EAAQ,MAAA,CAAO,CAAC;YAiH5D,IAAI,CAhHC,cAAC,CAAc,IAAC,CAAI,GAAG,GAAA,CAAI;YAiHhC,IAAI,CAhHC,IAAC,CAAI,oBAAC,CAAoB,cAAC,CAAc,IAAC,CAAI,EAAE;gBAiHnD,IAAI,CAhHC,SAAC,CAAS,IAAC,CAAI,GAAG,IAAA,CAAK,qBAAC,CAAqB,cAAC,CAAc,IAAC,CAAI;oBAiHlE,IAAI,CAhHC,qBAAC,CAAqB,IAAC,CAAI;oBAiHhC,UAAU,CAhHC;aAiHhB;YACD,IAAI,CAhHC,YAAC,CAAY,IAAC,EAAK,GAAA,CAAI,CAAC;SAiH9B,CAhHC,CAAC;KAiHJ;;;;IAIH,qBAlHG,GAkHH;QACI,uBAlHM,MAAA,GAAS,IAAA,CAAK,cAAC,CAAc;QAmHnC,uBAlHM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC;QAmHlC,IAAI,KAlHC,CAAK,MAAC,IAAS,CAAA;YAAG,OAAA;QAoHvB,IAAI,CAlHC,cAAC,GAAgB,EAAA,CAAG;QAoHzB,KAAK,CAlHC,OAAC,CAAO,IAAC,IAkHnB;YACM,uBAlHM,GAAA,GAAM,MAAA,CAAO,IAAC,CAAI,CAAC;YAmHzB,IAAI,CAlHC,gBAAC,CAAgB,IAAC,CAAI,GAAG,GAAA,CAAI;SAmHnC,CAlHC,CAAC;QAoHH,MAAM,CAlHC,IAAC,CAAI,IAAC,CAAI,oBAAC,CAAoB,CAAC,OAAC,CAAO,IAAC,IAkHpD;YACM,IAAI,CAlHC,IAAC,CAAI,gBAAC,CAAgB,cAAC,CAAc,IAAC,CAAI,EAAE;gBAmH/C,IAAI,CAlHC,gBAAC,CAAgB,IAAC,CAAI,GAAG,IAAA,CAAK,oBAAC,CAAoB,IAAC,CAAI,CAAC;aAmH/D;SACF,CAlHC,CAAC;KAmHJ;;;;IAIH,qBApHG,GAoHH;QACI,MAAM,CApHC,IAAC,CAAI,IAAC,CAAI,oBAAC,CAAoB,CAAC,OAAC,CAAO,IAAC,IAoHpD;YACM,uBApHM,GAAA,GAAM,IAAA,CAAK,oBAAC,CAAoB,IAAC,CAAI,CAAC;YAqH5C,IAAI,CApHC,cAAC,CAAc,IAAC,CAAI,GAAG,GAAA,CAAI;YAqHhC,IAAI,CApHC,YAAC,CAAY,IAAC,EAAK,GAAA,CAAI,CAAC;SAqH9B,CApHC,CAAC;KAqHJ;;;;IAIH,gBAtHG,GAsHH,EAtHuB,OAAO,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,IAAC,CAAI,QAAC,CAAQ,CAAC,EAAC;;;;IA0HlE,IAxHG,UAAA,GAwHH;QACI,uBAxHM,UAAA,GAAuB,EAAA,CAAG;QAyHhC,KAAK,qBAxHI,IAAA,IAAQ,IAAA,CAAK,gBAAC,EAAiB;YAyHtC,UAAU,CAxHC,IAAC,CAAI,IAAC,CAAI,CAAC;SAyHvB;QACD,OAxHO,UAAA,CAAW;KAyHnB;;;;;IAKH,4BA3HG,CAAA,QAAA,EA2HH;QACI,MAAM,CA3HC,IAAC,CAAI,QAAC,CAAQ,aAAC,CAAa,CAAC,OAAC,CAAO,IAAC,IA2HjD;YACM,uBA3HM,QAAA,GAAW,IAAA,CAAK,aAAC,CAAa,IAAC,CAAI,CAAC;YA4H1C,uBA3HM,QAAA,GAAW,QAAA,CAAS,aAAC,CAAa,IAAC,CAAI,CAAC;YA4H9C,IAAI,CA3HC,QAAC,IAAW,QAAA,CAAS,IAAC,GAAM,QAAA,CAAS,IAAC,EAAK;gBA4H9C,IAAI,CA3HC,YAAC,CAAY,IAAC,EAAK,QAAA,CAAS,KAAC,CAAK,CAAC;aA4HzC;SACF,CA3HC,CAAC;KA4HJ;;;;IAIH,cA7HG,GA6HH;QACI,IAAI,CA7HC,qBAAC,EAAqB,CAAE;QA8H7B,uBA7HM,aAAA,GAAgB,IAAI,GAAA,EAAW,CAAG;QA8HxC,uBA7HM,cAAA,GAAiB,IAAI,GAAA,EAAW,CAAG;QA8HzC,uBA7HM,OAAA,GAAU,IAAA,CAAK,UAAC,CAAU,IAAC,KAAQ,CAAA,IAAK,IAAA,CAAK,QAAC,KAAY,CAAA,CAAE;QA+HlE,qBA7HI,cAAA,GAA+B,EAAA,CAAG;QA8HtC,IAAI,CA7HC,UAAC,CAAU,OAAC,CAAO,CAAC,QAAC,EAAS,IAAA,KA6HvC;YACM,uBA7HM,aAAA,GAAgB,UAAA,CAAW,QAAC,EAAS,IAAA,CAAK,CAAC;YA8HjD,MAAM,CA7HC,IAAC,CAAI,aAAC,CAAa,CAAC,OAAC,CAAO,IAAC,IA6H1C;gBACQ,uBA7HM,KAAA,GAAQ,aAAA,CAAc,IAAC,CAAI,CAAC;gBA8HlC,IAAI,KA7HC,IAAQD,UAAA,EAAW;oBA8HtB,aAAa,CA7HC,GAAC,CAAG,IAAC,CAAI,CAAC;iBA8HzB;qBA7HM,IAAA,KAAK,IAAQ,UAAA,EAAY;oBA8H9B,cAAc,CA7HC,GAAC,CAAG,IAAC,CAAI,CAAC;iBA8H1B;aACF,CA7HC,CAAC;YA8HH,IAAI,CA7HC,OAAC,EAAQ;gBA8HZ,aAAa,CA7HC,QAAC,CAAQ,GAAG,IAAA,GAAO,IAAA,CAAK,QAAC,CAAQ;aA8HhD;YACD,cAAc,CA7HC,IAAC,CAAI,aAAC,CAAa,CAAC;SA8HpC,CA7HC,CAAC;QA+HH,uBA7HM,QAAA,GAAqB,aAAA,CAAc,IAAC,GAAM,eAAA,CAAgB,aAAC,CAAa,MAAC,EAAM,CAAE,GAAG,EAAA,CAAG;QA8H7F,uBA7HM,SAAA,GAAsB,cAAA,CAAe,IAAC,GAAM,eAAA,CAAgB,cAAC,CAAc,MAAC,EAAM,CAAE,GAAG,EAAA,CAAG;;QAgIhG,IAAI,OA7HC,EAAQ;YA8HX,uBA7HM,GAAA,GAAM,cAAA,CAAe,CAAC,CAAC,CAAC;YA8H9B,uBA7HM,GAAA,GAAM,OAAA,CAAQ,GAAC,CAAG,CAAC;YA8HzB,GAAG,CA7HC,QAAC,CAAQ,GAAG,CAAA,CAAE;YA8HlB,GAAG,CA7HC,QAAC,CAAQ,GAAG,CAAA,CAAE;YA8HlB,cAAc,GA7HG,CAAA,GAAE,EAAI,GAAA,CAAI,CAAC;SA8H7B;QAED,OA7HO,yBAAA,CA8HH,IAAI,CA7HC,OAAC,EAAQ,cAAA,EAAgB,QAAA,EAAU,SAAA,EAAW,IAAA,CAAK,QAAC,EAAS,IAAA,CAAK,SAAC,EA8HxE,IAAI,CA7HC,MAAC,EAAO,KAAA,CAAM,CAAC;KA8HzB;CACF;AAED,AA+BA,MAAA,kBA5JC,SAAA,eAAA,CA4JD;;;;;;;;;IAUA,WAAA,CAlKa,OAAS,EAAY,SAAsB,EAAU,aAAsB,EAC3E,cAAuB,EAAG,OAAS,EAClC,wBAgKd,GAhKkD,KAAA,EAgKlD;QAMI,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAxKrB,IAAb,CAAA,OAAa,GAAA,OAAA,CAAS;QAAY,IAAlC,CAAA,SAAkC,GAAA,SAAA,CAAsB;QAAU,IAAlE,CAAA,aAAkE,GAAA,aAAA,CAAsB;QAC3E,IAAb,CAAA,cAAa,GAAA,cAAA,CAAuB;QACtB,IAAd,CAAA,wBAAc,GAAA,wBAAA,CAAoC;QAuK9C,IAAI,CAAC,OAAO,GAAG,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC,CAAC;KAC3F;;;;IAIH,iBAvKG,GAuKH,EAvKiC,OAAO,IAAA,CAAK,SAAC,CAAS,MAAC,GAAQ,CAAA,CAAE,EAAC;;;;IA2KnE,cAzKG,GAyKH;QACI,qBAzKI,SAAA,GAAY,IAAA,CAAK,SAAC,CAAS;QA0K/B,IAzKI,EAAA,KAAE,EAAM,QAAA,EAAU,MAAA,EAAO,GAAG,IAAA,CAAK,OAAC,CAAO;QA0K7C,IAAI,IAzKC,CAAI,wBAAC,IAA2B,KAAA,EAAO;YA0K1C,uBAzKM,YAAA,GAA6B,EAAA,CAAG;YA0KtC,uBAzKM,SAAA,GAAY,QAAA,GAAW,KAAA,CAAM;YA0KnC,uBAzKM,WAAA,GAAc,KAAA,GAAQ,SAAA,CAAU;;YA4KtC,uBAzKM,gBAAA,GAAmB,UAAA,CAAW,SAAC,CAAS,CAAC,CAAC,EAAE,KAAA,CAAM,CAAC;YA0KzD,gBAAgB,CAzKC,QAAC,CAAQ,GAAG,CAAA,CAAE;YA0K/B,YAAY,CAzKC,IAAC,CAAI,gBAAC,CAAgB,CAAC;YA2KpC,uBAzKM,gBAAA,GAAmB,UAAA,CAAW,SAAC,CAAS,CAAC,CAAC,EAAE,KAAA,CAAM,CAAC;YA0KzD,gBAAgB,CAzKC,QAAC,CAAQ,GAAG,WAAA,CAAY,WAAC,CAAW,CAAC;YA0KtD,YAAY,CAzKC,IAAC,CAAI,gBAAC,CAAgB,CAAC;;;;;;;;;;;;;;;;YA2LpC,uBAzKM,KAAA,GAAQ,SAAA,CAAU,MAAC,GAAQ,CAAA,CAAE;YA0KnC,KAAK,qBAzKI,CAAA,GAAI,CAAA,EAAG,CAAA,IAAK,KAAA,EAAO,CAAA,EAAE,EAAG;gBA0K/B,qBAzKI,EAAA,GAAK,UAAA,CAAW,SAAC,CAAS,CAAC,CAAC,EAAE,KAAA,CAAM,CAAC;gBA0KzC,uBAzKM,SAAA,IAAY,EAAA,CAAG,QAAC,CAAY,CAAA,CAAO;gBA0KzC,uBAzKM,cAAA,GAAiB,KAAA,GAAQ,SAAA,GAAY,QAAA,CAAS;gBA0KpD,EAAE,CAzKC,QAAC,CAAQ,GAAG,WAAA,CAAY,cAAC,GAAgB,SAAA,CAAU,CAAC;gBA0KvD,YAAY,CAzKC,IAAC,CAAI,EAAC,CAAE,CAAC;aA0KvB;;YAGD,QAAQ,GAzKG,SAAA,CAAU;YA0KrB,KAAK,GAzKG,CAAA,CAAE;YA0KV,MAAM,GAzKG,EAAA,CAAG;YA2KZ,SAAS,GAzKG,YAAA,CAAa;SA0K1B;QAED,OAzKO,yBAAA,CA0KH,IAAI,CAzKC,OAAC,EAAQ,SAAA,EAAW,IAAA,CAAK,aAAC,EAAc,IAAA,CAAK,cAAC,EAAe,QAAA,EAAU,KAAA,EAAO,MAAA,EA0KnF,IAAI,CAzKC,CAAC;KA0KX;CACF;AAED,AAeA;;;;;AAKA,SAAA,WAAA,CA7LC,MAAA,EAAA,aAAA,GAAA,CAAA,EA6LD;IACE,uBA7LM,IAAA,GAAO,IAAA,CAAK,GAAC,CAAG,EAAC,EAAG,aAAA,GAAgB,CAAA,CAAE,CAAC;IA8L7C,OA7LO,IAAA,CAAK,KAAC,CAAK,MAAC,GAAQ,IAAA,CAAK,GAAG,IAAA,CAAK;CA8LzC;;;;;;AAMD,SAAA,aAAA,CAjMC,KAAA,EAAA,SAAA,EAiMD;IACE,uBAjMM,MAAA,GAAqB,EAAA,CAAG;IAkM9B,qBAjMI,aAAsB,CAAE;IAkM5B,KAAK,CAjMC,OAAC,CAAO,KAAC,IAiMjB;QACI,IAAI,KAjMC,KAAS,GAAA,EAAK;YAkMjB,aAAa,GAjMG,aAAA,IAAiB,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,CAAC;YAkMxD,aAAa,CAjMC,OAAC,CAAO,IAAC,IAiM7B,EAjMsC,MAAA,CAAO,IAAC,CAAI,GAAG,UAAA,CAAW,EAAC,CAAE,CAAC;SAkM/D;aAjMM;YAkML,UAAU,mBAjMC,KAAS,GAAY,KAAA,EAAO,MAAA,CAAO,CAAC;SAkMhD;KACF,CAjMC,CAAC;IAkMH,OAjMO,MAAA,CAAO;CAkMf;;ADjjCD;;;;;;;AAWA,AAGA,AACA,AAEA,AACA,AAAA,MAAA,SAAA,CAAA;;;;;IAMA,WAAA,CAJsB,OAAS,EAAiB,KAA2C,EAI3F;QAJsB,IAAtB,CAAA,OAAsB,GAAA,OAAA,CAAS;QAK3B,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7C,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,MAAM,YAAY,GAAG,CAA3B,8BAAA,EAA4D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAA7E,CAA+E,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;KAC1B;;;;;;;;;IASH,cAXG,CAYG,OAAY,EAAE,cAAuC,EACrD,iBAA0C,EAAE,OAAyB,EACrE,eAAuC,EAH7C;QAII,uBAXM,KAAA,GAAQ,KAAA,CAAM,OAAC,CAAO,cAAC,CAAc,GAAG,eAAA,CAAgB,cAAC,CAAc,IACnB,cAAC,CAAA,CAAc;QAYzE,uBAXM,IAAA,GAAO,KAAA,CAAM,OAAC,CAAO,iBAAC,CAAiB,GAAG,eAAA,CAAgB,iBAAC,CAAiB,IACtB,iBAAC,CAAA,CAAiB;QAY9E,uBAXM,MAAA,GAAc,EAAA,CAAG;QAYvB,eAAe,GAXG,eAAA,IAAmB,IAAI,qBAAA,EAAsB,CAAE;QAYjE,uBAXM,MAAA,GAAS,uBAAA,CAYX,IAAI,CAXC,OAAC,EAAQ,OAAA,EAAS,IAAA,CAAK,aAAC,EAAc,KAAA,EAAO,IAAA,EAAM,OAAA,EAAS,eAAA,EAAiB,MAAA,CAAO,CAAC;QAY9F,IAAI,MAXC,CAAM,MAAC,EAAO;YAYjB,uBAXM,YAAA,GAAe,CAW3B,4BAAA,EAX2B,MAAgC,CAAM,IAAC,CAAI,IAAC,CAAI,CAW3E,CAX4E,CAAE;YAYxE,MAXM,IAAI,KAAA,CAAM,YAAC,CAAY,CAAC;SAY/B;QACD,OAXO,MAAA,CAAO;KAYf;CACF,AAED,AAKC;;ADlED;;;;;;;;;;;;;;;;IAWA,AALA,MAAA,wBAAA,CAAA;CAUC;;;;AAKD,AAAA,MAAA,4BAAA,CAAA;IACE,qBAAqB,CAAC,YAAoB,EAAE,MAAgB,EAA9D,EAA0E,OAAO,YAAY,CAAC,EAAE;IAE9F,mBAAmB,CACf,oBAA4B,EAAE,kBAA0B,EAAE,KAAoB,EAC9E,MAAgB,EAFtB;QAGI,OAAY,KAAK,CAAC;KACnB;CACF;;AD7BD;;;;;;;AAQA,AAEA,AACA,AAAA,MAAA,4BAAC,SAAA,wBAAA,CAAD;;;;;;IAMA,qBALG,CAAA,YAAA,EAAA,MAAA,EAKH;QACI,OALO,mBAAA,CAAoB,YAAC,CAAY,CAAC;KAM1C;;;;;;;;IAQH,mBAXG,CAYG,oBAA4B,EAAE,kBAA0B,EAAE,KAAoB,EAC9E,MAAgB,EAFtB;QAGI,qBAXI,IAAA,GAAe,EAAA,CAAG;QAYtB,uBAXM,MAAA,GAAS,KAAA,CAAM,QAAC,EAAQ,CAAE,IAAC,EAAI,CAAE;QAavC,IAAI,oBAXC,CAAoB,kBAAC,CAAkB,IAAI,KAAA,KAAU,CAAA,IAAK,KAAA,KAAU,GAAA,EAAK;YAY5E,IAAI,OAXO,KAAA,KAAU,QAAA,EAAU;gBAY7B,IAAI,GAXG,IAAA,CAAK;aAYb;iBAXM;gBAYL,uBAXM,iBAAA,GAAoB,KAAA,CAAM,KAAC,CAAK,wBAAC,CAAwB,CAAC;gBAYhE,IAAI,iBAXC,IAAoB,iBAAA,CAAkB,CAAC,CAAC,CAAC,MAAC,IAAS,CAAA,EAAG;oBAYzD,MAAM,CAXC,IAAC,CAAI,CAWtB,oCAAA,EAXuB,oBAAuC,CAW9D,CAAA,EAXkF,KAAI,CAWtF,CAX2F,CAAE,CAAC;iBAYrF;aACF;SACF;QACD,OAXO,MAAA,GAAS,IAAA,CAAK;KAYtB;CACF;AAED,MAXM,oBAAA,GAAuB,cAAA,CAYzB,gUAAgU;KAC3T,KAXC,CAAK,GAAC,CAAG,CAAC,CAAC;;;;;AAgBrB,SAAA,cAAA,CAdC,IAAA,EAcD;IACE,uBAdM,GAAA,GAAgC,EAAA,CAAG;IAezC,IAAI,CAdC,OAAC,CAAO,GAAC,IAAM,GAAA,CAAI,GAAC,CAAG,GAAG,IAAA,CAAK,CAAC;IAerC,OAdO,GAAA,CAAI;CAeZ;;AD1DD;;;;;;;;;;;;;;;;;;;;;AAuCA,AAAA,SAAA,2BAAA,CACI,OAAY,EAAE,WAAmB,EAAE,SAAiB,EAAE,OAAe,EACrE,mBAA4B,EAAE,UAAsB,EAAE,QAAoB,EAC1E,SAAyC,EAAE,eAAsB,EACjE,aAAkD,EAClD,cAAmD,EALvD;IAME,OAdO;QAeL,IAAI,EAAR,CAAA;QACI,OAAO;QACP,WAAW;QACX,mBAAmB;QACnB,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,eAAe;QACf,aAAa;QACb,cAAc;KACf,CAdC;CAeH;;AD3DD;;;;;;;AAWA,AACA,AAGA,AAEA,AAEA,AAAA,MAAA,0BAAA,CAAA;;;;;;IAMA,WAAA,CAJc,YAAc,EAAe,GAAK,EAClC,YAA+C,EAG7D;QAJc,IAAd,CAAA,YAAc,GAAA,YAAA,CAAc;QAAe,IAA3C,CAAA,GAA2C,GAAA,GAAA,CAAK;QAClC,IAAd,CAAA,YAAc,GAAA,YAAA,CAA+C;KAAC;;;;;;IAY9D,KAVG,CAAA,YAAA,EAAA,SAAA,EAUH;QACI,OAVO,yBAAA,CAA0B,IAAC,CAAI,GAAC,CAAG,QAAC,EAAS,YAAA,EAAc,SAAA,CAAU,CAAC;KAW9E;;;;;;;;;;IAUH,KAlBG,CAmBG,MAAuB,EAAE,OAAY,EAAE,YAAiB,EAAE,SAAc,EACxE,OAA0B,EAC1B,eAAuC,EAH7C;QAII,uBAlBM,gBAAA,GAAmB,qBAAA,CAAsB,IAAC,CAAI,GAAC,CAAG,OAAC,IAAU,EAAA,EAAI,OAAA,IAAW,EAAA,CAAG,CAAC;QAoBtF,uBAlBM,iBAAA,GAAoB,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,IAAI,EAAA,CAAG;QAmBvD,uBAlBM,kBAAA,GAAqB,IAAA,CAAK,YAAC,CAAY,YAAC,CAAY,IAAI,iBAAA,CAAkB;QAmBhF,uBAlBM,eAAA,GAAkB,IAAA,CAAK,YAAC,CAAY,SAAC,CAAS,IAAI,iBAAA,CAAkB;QAoB1E,uBAlBM,MAAA,GAAgB,EAAA,CAAG;QAmBzB,uBAlBM,SAAA,GAAY,uBAAA,CAmBd,MAAM,EAlBE,OAAA,EAAS,IAAA,CAAK,GAAC,CAAG,SAAC,EAAU,kBAAA,EAAoB,eAAA,EAAiB,gBAAA,EAmB1E,eAAe,EAlBE,MAAA,CAAO,CAAC;QAoB7B,IAAI,MAlBC,CAAM,MAAC,EAAO;YAmBjB,uBAlBM,YAAA,GAAe,CAkB3B,4BAAA,EAlB2B,MAAgC,CAAM,IAAC,CAAI,IAAC,CAAI,CAkB3E,CAlB4E,CAAE;YAmBxE,MAlBM,IAAI,KAAA,CAAM,YAAC,CAAY,CAAC;SAmB/B;QAED,uBAlBM,WAAA,GAAc,IAAI,GAAA,EAAmC,CAAG;QAmB9D,uBAlBM,YAAA,GAAe,IAAI,GAAA,EAAmC,CAAG;QAmB/D,uBAlBM,eAAA,GAAkB,IAAI,GAAA,EAAQ,CAAG;QAmBvC,SAAS,CAlBC,OAAC,CAAO,EAAC,IAkBvB;YACM,uBAlBM,GAAA,GAAM,EAAA,CAAG,OAAC,CAAO;YAmBvB,uBAlBM,QAAA,GAAW,eAAA,CAAgB,WAAC,EAAY,GAAA,EAAK,EAAA,CAAG,CAAC;YAmBvD,EAAE,CAlBC,aAAC,CAAa,OAAC,CAAO,IAAC,IAAO,QAAA,CAAS,IAAC,CAAI,GAAG,IAAA,CAAK,CAAC;YAoBxD,uBAlBM,SAAA,GAAY,eAAA,CAAgB,YAAC,EAAa,GAAA,EAAK,EAAA,CAAG,CAAC;YAmBzD,EAAE,CAlBC,cAAC,CAAc,OAAC,CAAO,IAAC,IAAO,SAAA,CAAU,IAAC,CAAI,GAAG,IAAA,CAAK,CAAC;YAoB1D,IAAI,GAlBC,KAAO,OAAA,EAAS;gBAmBnB,eAAe,CAlBC,GAAC,CAAG,GAAC,CAAG,CAAC;aAmB1B;SACF,CAlBC,CAAC;QAoBH,uBAlBM,mBAAA,GAAsB,eAAA,CAAgB,eAAC,CAAe,MAAC,EAAM,CAAE,CAAC;QAmBtE,OAlBO,2BAAA,CAmBH,OAAO,EAlBE,IAAA,CAAK,YAAC,EAAa,YAAA,EAAc,SAAA,EAAW,SAAA,KAAc,MAAA,EAmBnE,kBAAkB,EAlBE,eAAA,EAAiB,SAAA,EAAW,mBAAA,EAAqB,WAAA,EAmBrE,YAAY,CAlBC,CAAC;KAmBnB;CACF;AAED,AASA;;;;;;AAMA,SAAA,yBAAA,CACI,QAA+B,EAAE,YAAiB,EAAE,SAAc,EADtE;IAEE,OAjCO,QAAA,CAAS,IAAC,CAAI,EAAC,IAAK,EAAA,CAAG,YAAC,EAAa,SAAA,CAAU,CAAC,CAAC;CAkCzD;;AD5GD;;;;;;;AAUA,AAEA,AACA,AACA;;;;;;AAMA,AAAA,SAAA,YAAA,CAHC,IAAA,EAAA,GAAA,EAGD;IACE,OAHO,IAAI,gBAAA,CAAiB,IAAC,EAAK,GAAA,CAAI,CAAC;CAIxC;;;;AAID,AAAA,MAAA,gBAAA,CAAA;;;;;IAQA,WAAA,CALqB,IAAM,EAAe,GAAK,EAK/C;QALqB,IAArB,CAAA,IAAqB,GAAA,IAAA,CAAM;QAAe,IAA1C,CAAA,GAA0C,GAAA,GAAA,CAAK;QAJtC,IAAT,CAAA,mBAAS,GAAoD,EAAA,CAAG;QAEvD,IAAT,CAAA,MAAS,GAA4C,EAAA,CAAG;QASpD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAA1B;YACM,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACvC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAzC;gBACQ,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;oBACjC,UAAU,CAAC,UAAwB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;iBAClD;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;QAEH,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE7C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAA/B;YACM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SACvF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACvE;;;;IAIH,IARG,eAAA,GAQH,EAR0B,OAAO,IAAA,CAAK,GAAC,CAAG,UAAC,GAAY,CAAA,CAAE,EAAC;;;;;;IAc1D,eAZG,CAAA,YAAA,EAAA,SAAA,EAYH;QACI,uBAZM,KAAA,GAAQ,IAAA,CAAK,mBAAC,CAAmB,IAAC,CAAI,CAAC,IAAI,CAAA,CAAE,KAAC,CAAK,YAAC,EAAa,SAAA,CAAU,CAAC,CAAC;QAanF,OAZO,KAAA,IAAS,IAAA,CAAK;KAatB;CACF;AAED,AAaA;;;;;AAKA,SAAA,wBAAA,CACI,WAAmB,EAAE,MAAyC,EADlE;IAEE,uBA9BM,QAAA,GAAW,CAAA,CAAE,SAAW,EAAK,OAAS,KAAQ,IAAA,CAAK,CAAC;IA+B1D,uBA9BM,SAAA,GAAY,IAAI,WAAA,CAAY,EAAC,CAAE,CAAC;IA+BtC,uBA9BM,UAAA,GAAa,IAAI,aAAA,CAAc,QAAC,EAAS,SAAA,CAAU,CAAC;IA+B1D,OA9BO,IAAI,0BAAA,CAA2B,WAAC,EAAY,UAAA,EAAY,MAAA,CAAO,CAAC;CA+BxE;;;;;;;AAOD,SAAA,iBAAA,CAnCC,GAAA,EAAA,IAAA,EAAA,IAAA,EAmCD;IACE,IAAI,GAnCC,CAAG,cAAC,CAAc,IAAC,CAAI,EAAE;QAoC5B,IAAI,CAnCC,GAAC,CAAG,cAAC,CAAc,IAAC,CAAI,EAAE;YAoC7B,GAAG,CAnCC,IAAC,CAAI,GAAG,GAAA,CAAI,IAAC,CAAI,CAAC;SAoCvB;KACF;SAnCM,IAAA,GAAK,CAAG,cAAC,CAAc,IAAC,CAAI,EAAE;QAoCnC,GAAG,CAnCC,IAAC,CAAI,GAAG,GAAA,CAAI,IAAC,CAAI,CAAC;KAoCvB;CACF;;AD5GD;;;;;;;AAQA,AAGA,AACA,AAEA,AAIA,AAEA,MADM,qBAAA,GAAwB,IAAI,qBAAA,EAAsB,CAAE;AAE1D,AAAA,MAAA,uBAAA,CAAA;;;;;IAQA,WAAA,CAHsB,OAAS,EAAyB,WAAa,EAGrE;QAHsB,IAAtB,CAAA,OAAsB,GAAA,OAAA,CAAS;QAAyB,IAAxD,CAAA,WAAwD,GAAA,WAAA,CAAa;QAJ3D,IAAV,CAAA,WAAU,GAAmC,EAAA,CAAG;QACtC,IAAV,CAAA,YAAU,GAAgD,EAAA,CAAG;QACpD,IAAT,CAAA,OAAS,GAA6B,EAAA,CAAG;KAEqD;;;;;;IAU9F,QARG,CAAA,EAAA,EAAA,QAAA,EAQH;QACI,uBARM,MAAA,GAAgB,EAAA,CAAG;QASzB,uBARM,GAAA,GAAM,iBAAA,CAAkB,QAAC,EAAS,MAAA,CAAO,CAAC;QAShD,IAAI,MARC,CAAM,MAAC,EAAO;YASjB,MARM,IAAI,KAAA,CASN,CADV,2DAAA,EACwE,MARC,CAAM,IAAC,CAAI,IAAC,CAAI,CAOzF,CAP0F,CAAE,CAAC;SASxF;aARM;YASL,IAAI,CARC,WAAC,CAAW,EAAC,CAAE,GAAG,GAAA,CAAI;SAS5B;KACF;;;;;;;IANA,YAAA,CAcG,CAA+B,EAAE,SAAqB,EACtD,UAAuB,EAf7B;QAgBI,uBAbM,OAAA,GAAU,CAAA,CAAE,OAAC,CAAO;QAc1B,uBAbM,SAAA,GAAY,kBAAA,CAcd,IAAI,CAbC,OAAC,EAAQ,IAAA,CAAK,WAAC,EAAY,OAAA,EAAS,CAAA,CAAE,SAAC,EAAU,SAAA,EAAW,UAAA,CAAW,CAAC;QAcjF,OAbO,IAAA,CAAK,OAAC,CAAO,OAAC,CAAO,OAAC,EAAQ,SAAA,EAAW,CAAA,CAAE,QAAC,EAAS,CAAA,CAAE,KAAC,EAAM,CAAA,CAAE,MAAC,EAAO,EAAA,CAAG,CAAC;KAcpF;;;;;;;IAOH,MAlBG,CAAA,EAAA,EAAA,OAAA,EAAA,OAkBH,GAlBG,EAAA,EAkBH;QACI,uBAlBM,MAAA,GAAgB,EAAA,CAAG;QAmBzB,uBAlBM,GAAA,GAAM,IAAA,CAAK,WAAC,CAAW,EAAC,CAAE,CAAC;QAmBjC,qBAlBI,YAA2C,CAAE;QAoBjD,uBAlBM,aAAA,GAAgB,IAAI,GAAA,EAAoB,CAAG;QAoBjD,IAAI,GAlBC,EAAI;YAmBP,YAAY,GAlBG,uBAAA,CAmBX,IAAI,CAlBC,OAAC,EAAQ,OAAA,EAAS,GAAA,EAAK,EAAA,EAAI,EAAA,EAAI,OAAA,EAAS,qBAAA,EAAuB,MAAA,CAAO,CAAC;YAmBhF,YAAY,CAlBC,OAAC,CAAO,IAAC,IAkB5B;gBACQ,uBAlBM,MAAA,GAAS,eAAA,CAAgB,aAAC,EAAc,IAAA,CAAK,OAAC,EAAQ,EAAA,CAAG,CAAC;gBAmBhE,IAAI,CAlBC,cAAC,CAAc,OAAC,CAAO,IAAC,IAAO,MAAA,CAAO,IAAC,CAAI,GAAG,IAAA,CAAK,CAAC;aAmB1D,CAlBC,CAAC;SAmBJ;aAlBM;YAmBL,MAAM,CAlBC,IAAC,CAAI,sEAAC,CAAsE,CAAC;YAmBpF,YAAY,GAlBG,EAAA,CAAG;SAmBnB;QAED,IAAI,MAlBC,CAAM,MAAC,EAAO;YAmBjB,MAlBM,IAAI,KAAA,CAmBN,CADV,4DAAA,EACyE,MAlBC,CAAM,IAAC,CAAI,IAAC,CAAI,CAiB1F,CAjB2F,CAAE,CAAC;SAmBzF;QAED,aAAa,CAlBC,OAAC,CAAO,CAAC,MAAC,EAAO,OAAA,KAkBnC;YACM,MAAM,CAlBC,IAAC,CAAI,MAAC,CAAM,CAAC,OAAC,CAmBjB,IAAI,IADd,EAjBoB,MAAA,CAAO,IAAC,CAAI,GAAG,IAAA,CAAK,OAAC,CAAO,YAAC,CAAY,OAAC,EAAQ,IAAA,EAAM,UAAA,CAAW,CAAC,EAAC,CAAE,CAAC;SAmBvF,CAlBC,CAAC;QAoBH,uBAlBM,OAAA,GAAU,YAAA,CAAa,GAAC,CAAG,CAAC,IAkBtC;YACM,uBAlBM,MAAA,GAAS,aAAA,CAAc,GAAC,CAAG,CAAC,CAAC,OAAC,CAAO,CAAC;YAmB5C,OAlBO,IAAA,CAAK,YAAC,CAAY,CAAC,EAAE,EAAA,EAAI,MAAA,CAAO,CAAC;SAmBzC,CAlBC,CAAC;QAmBH,uBAlBM,MAAA,GAAS,mBAAA,CAAoB,OAAC,CAAO,CAAC;QAmB5C,IAAI,CAlBC,YAAC,CAAY,EAAC,CAAE,GAAG,MAAA,CAAO;QAmB/B,MAAM,CAlBC,SAAC,CAAS,MAAM,IAAA,CAAK,OAAC,CAAO,EAAC,CAAE,CAAC,CAAC;QAoBzC,IAAI,CAlBC,OAAC,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC;QAmB1B,OAlBO,MAAA,CAAO;KAmBf;;;;;IAKH,OArBG,CAAA,EAAA,EAqBH;QACI,uBArBM,MAAA,GAAS,IAAA,CAAK,UAAC,CAAU,EAAC,CAAE,CAAC;QAsBnC,MAAM,CArBC,OAAC,EAAO,CAAE;QAsBjB,OArBO,IAAA,CAAK,YAAC,CAAY,EAAC,CAAE,CAAC;QAsB7B,uBArBM,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,OAAC,CAAO,MAAC,CAAM,CAAC;QAsB3C,IAAI,KArBC,IAAQ,CAAA,EAAG;YAsBd,IAAI,CArBC,OAAC,CAAO,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;SAsB/B;KACF;;;;;IAnBA,UAAA,CAAA,EAAA,EAAH;QAyBI,uBAxBM,MAAA,GAAS,IAAA,CAAK,YAAC,CAAY,EAAC,CAAE,CAAC;QAyBrC,IAAI,CAxBC,MAAC,EAAO;YAyBX,MAxBM,IAAI,KAAA,CAAM,CAwBtB,iDAAA,EAxBuB,EAAoD,CAwB3E,CAxB6E,CAAE,CAAC;SAyB3E;QACD,OAxBO,MAAA,CAAO;KAyBf;;;;;;;;IAQH,MA9BG,CAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EA8BH;;QAGI,uBA9BM,SAAA,GAAY,kBAAA,CAAmB,OAAC,EAAQ,EAAA,EAAI,EAAA,EAAI,EAAA,CAAG,CAAC;QA+B1D,cAAc,CA9BC,IAAC,CAAI,UAAC,CAAU,EAAC,CAAE,EAAE,SAAA,EAAW,SAAA,EAAW,QAAA,CAAS,CAAC;QA+BpE,OA9BO,MA8BX,GA9BiB,CAAG;KA+BjB;;;;;;;;IAQH,OApCG,CAAA,EAAA,EAAA,OAAA,EAAA,OAAA,EAAA,IAAA,EAoCH;QACI,IAAI,OApCC,IAAU,UAAA,EAAY;YAqCzB,IAAI,CApCC,QAAC,CAAQ,EAAC,oBAAG,IAAA,CAAK,CAAC,CAA2C,EAAE,CAAC;YAqCtE,OAAO;SACR;QAED,IAAI,OApCC,IAAU,QAAA,EAAU;YAqCvB,uBApCM,OAAA,KAAU,IAAE,CAAI,CAAC,CAAC,IAAI,EAAA,EAAO,CAAiB;YAqCpD,IAAI,CApCC,MAAC,CAAM,EAAC,EAAG,OAAA,EAAS,OAAA,CAAQ,CAAC;YAqClC,OAAO;SACR;QAED,uBApCM,MAAA,GAAS,IAAA,CAAK,UAAC,CAAU,EAAC,CAAE,CAAC;QAqCnC,QAAQ,OApCC;YAqCP,KApCK,MAAA;gBAqCH,MAAM,CApCC,IAAC,EAAI,CAAE;gBAqCd,MAAM;YACR,KApCK,OAAA;gBAqCH,MAAM,CApCC,KAAC,EAAK,CAAE;gBAqCf,MAAM;YACR,KApCK,OAAA;gBAqCH,MAAM,CApCC,KAAC,EAAK,CAAE;gBAqCf,MAAM;YACR,KApCK,SAAA;gBAqCH,MAAM,CApCC,OAAC,EAAO,CAAE;gBAqCjB,MAAM;YACR,KApCK,QAAA;gBAqCH,MAAM,CApCC,MAAC,EAAM,CAAE;gBAqChB,MAAM;YACR,KApCK,MAAA;gBAqCH,MAAM,CApCC,IAAC,EAAI,CAAE;gBAqCd,MAAM;YACR,KApCK,aAAA;gBAqCH,MAAM,CApCC,WAAC,CAAW,UAAC,mBAAU,IAAC,CAAI,CAAC,CAAK,EAAO,CAAC,CAAC;gBAqClD,MAAM;YACR,KApCK,SAAA;gBAqCH,IAAI,CApCC,OAAC,CAAO,EAAC,CAAE,CAAC;gBAqCjB,MAAM;SACT;KACF;CACF,AAED,AAWC;;AD5MD;;;;;;;AAQA,AAMA,AAEA,AAGA,AAEA,MADM,kBAAA,GAAwC,EAAA,CAAG;AAEjD,MADM,kBAAA,GAA4C;IAEhD,WAAW,EADE,EAAA;IAEb,aAAa,EADE,IAAA;IAEf,YAAY,EADE,KAAA;IAEd,oBAAoB,EADE,KAAA;CAEvB,CADC;AAEF,MADM,0BAAA,GAAoD;IAExD,WAAW,EADE,EAAA;IAEb,aAAa,EADE,IAAA;IAEf,YAAY,EADE,KAAA;IAEd,oBAAoB,EADE,IAAA;CAEvB,CADC;AAmBF,AADO,MAAM,YAAA,GAAe,cAAA,CAAe;AAS3C,AAAA,MAAA,UAAA,CAAA;;;;IAMA,WAAA,CAFG,KAAkB,EAErB;QACI,MAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,MAAM,OAAO,GAAG,OAAO,CAAC,KAAY,CAAC,CAAC;YACtC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,OAA2B,CAAC;SAC5C;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;SAC1B;KACF;;;;;IAKH,aALG,CAAA,OAAA,EAKH;QACI,uBALM,SAAA,GAAY,OAAA,CAAQ,MAAC,CAAM;QAMjC,IAAI,SALC,EAAU;YAMb,uBALM,SAAA,KAAY,IAAA,CAAK,OAAC,CAAO,MAAC,EAAA,CAAQ;YAMxC,MAAM,CALC,IAAC,CAAI,SAAC,CAAS,CAAC,OAAC,CAAO,IAAC,IAKtC;gBACQ,IAAI,SALC,CAAS,IAAC,CAAI,IAAI,IAAA,EAAM;oBAM3B,SAAS,CALC,IAAC,CAAI,GAAG,SAAA,CAAU,IAAC,CAAI,CAAC;iBAMnC;aACF,CALC,CAAC;SAMJ;KACF;CACF;AAED,AAQA,AAbO,MAAM,UAAA,GAAa,MAAA,CAAO;AAcjC,AAbO,MAAM,mBAAA,GAAsB,IAAI,UAAA,CAAW,UAAC,CAAU,CAAC;AAc9D,AAbO,MAAM,mBAAA,GAAsB,IAAI,UAAA,CAAW,SAAC,CAAS,CAAC;AAc7D,AAAA,MAAA,4BAAA,CAAA;;;;;;IAWA,WAAA,CAZa,EAAI,EAAe,WAAa,EAAa,OAAS,EAYnE;QAZa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAAe,IAAhC,CAAA,WAAgC,GAAA,WAAA,CAAa;QAAa,IAA1D,CAAA,OAA0D,GAAA,OAAA,CAAS;QAV1D,IAAT,CAAA,OAAS,GAAuC,EAAA,CAAG;QAEzC,IAAV,CAAA,SAAU,GAAuD,EAAA,CAAG;QAC1D,IAAV,CAAA,MAAU,GAA6B,EAAA,CAAG;QAEhC,IAAV,CAAA,iBAAU,GAAoB,IAAI,GAAA,EAA2B,CAAG;QAqB5D,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE,CAAC;QACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC5C;;;;;;;;IAQH,MArBG,CAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAqBH;QACI,IAAI,CArBC,IAAC,CAAI,SAAC,CAAS,cAAC,CAAc,IAAC,CAAI,EAAE;YAsBxC,MArBM,IAAI,KAAA,CAsBN,CADV,iDAAA,EAC8D,KArBC,CAoB/D,iCAAA,EApBoE,IAAoC,CAoBxG,iBAAA,CApB4G,CAAmB,CAAC;SAsB3H;QAED,IAAI,KArBC,IAAQ,IAAA,IAAQ,KAAA,CAAM,MAAC,IAAS,CAAA,EAAG;YAsBtC,MArBM,IAAI,KAAA,CAsBN,CADV,2CAAA,EACwD,IArBC,CAoBzD,0CAAA,CApB6D,CAA4C,CAAC;SAsBrG;QAED,IAAI,CArBC,mBAAC,CAAmB,KAAC,CAAK,EAAE;YAsB/B,MArBM,IAAI,KAAA,CAsBN,CADV,sCAAA,EACmD,KArBC,CAoBpD,6BAAA,EApByD,IAAgC,CAoBzF,mBAAA,CApB6F,CAAqB,CAAC;SAsB9G;QAED,uBArBM,SAAA,GAAY,eAAA,CAAgB,IAAC,CAAI,iBAAC,EAAkB,OAAA,EAAS,EAAA,CAAG,CAAC;QAsBvE,uBArBM,IAAA,GAAO,EAAA,IAAE,EAAK,KAAA,EAAO,QAAA,EAAS,CAAC;QAsBrC,SAAS,CArBC,IAAC,CAAI,IAAC,CAAI,CAAC;QAuBrB,uBArBM,kBAAA,GAAqB,eAAA,CAAgB,IAAC,CAAI,OAAC,CAAO,eAAC,EAAgB,OAAA,EAAS,EAAA,CAAG,CAAC;QAsBtF,IAAI,CArBC,kBAAC,CAAkB,cAAC,CAAc,IAAC,CAAI,EAAE;YAsB5C,QAAQ,CArBC,OAAC,EAAQ,oBAAA,CAAqB,CAAC;YAsBxC,QAAQ,CArBC,OAAC,EAAQ,oBAAA,GAAuB,GAAA,GAAM,IAAA,CAAK,CAAC;YAsBrD,kBAAkB,CArBC,IAAC,CAAI,GAAG,IAAA,CAAK;SAsBjC;QAED,OArBO,MAqBX;;;;YAIM,IAAI,CArBC,OAAC,CAAO,UAAC,CAAU,MAqB9B;gBACQ,uBArBM,KAAA,GAAQ,SAAA,CAAU,OAAC,CAAO,IAAC,CAAI,CAAC;gBAsBtC,IAAI,KArBC,IAAQ,CAAA,EAAG;oBAsBd,SAAS,CArBC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;iBAsB5B;gBAED,IAAI,CArBC,IAAC,CAAI,SAAC,CAAS,IAAC,CAAI,EAAE;oBAsBzB,OArBO,kBAAA,CAAmB,IAAC,CAAI,CAAC;iBAsBjC;aACF,CArBC,CAAC;SAsBJ,CArBC;KAsBH;;;;;;IAMH,QAzBG,CAAA,IAAA,EAAA,GAAA,EAyBH;QACI,IAAI,IAzBC,CAAI,SAAC,CAAS,IAAC,CAAI,EAAE;;YA2BxB,OAzBO,KAAA,CAAM;SA0Bd;aAzBM;YA0BL,IAAI,CAzBC,SAAC,CAAS,IAAC,CAAI,GAAG,GAAA,CAAI;YA0B3B,OAzBO,IAAA,CAAK;SA0Bb;KACF;;;;;IAvBA,WAAA,CAAA,IAAA,EAAH;QA6BI,uBA5BM,OAAA,GAAU,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,CAAC;QA6BrC,IAAI,CA5BC,OAAC,EAAQ;YA6BZ,MA5BM,IAAI,KAAA,CAAM,CA4BtB,gCAAA,EA5BuB,IAAmC,CA4B1D,0BAAA,CA5B8D,CAA4B,CAAC;SA6BtF;QACD,OA5BO,OAAA,CAAQ;KA6BhB;;;;;;;;IAQH,OAlCG,CAAA,OAAA,EAAA,WAAA,EAAA,KAAA,EAAA,iBAkCH,GAlCG,IAAA,EAkCH;QAEI,uBAlCM,OAAA,GAAU,IAAA,CAAK,WAAC,CAAW,WAAC,CAAW,CAAC;QAmC9C,uBAlCM,MAAA,GAAS,IAAI,yBAAA,CAA0B,IAAC,CAAI,EAAC,EAAG,WAAA,EAAa,OAAA,CAAQ,CAAC;QAoC5E,qBAlCI,kBAAA,GAAqB,IAAA,CAAK,OAAC,CAAO,eAAC,CAAe,GAAC,CAAG,OAAC,CAAO,CAAC;QAmCnE,IAAI,CAlCC,kBAAC,EAAmB;YAmCvB,QAAQ,CAlCC,OAAC,EAAQ,oBAAA,CAAqB,CAAC;YAmCxC,QAAQ,CAlCC,OAAC,EAAQ,oBAAA,GAAuB,GAAA,GAAM,WAAA,CAAY,CAAC;YAmC5D,IAAI,CAlCC,OAAC,CAAO,eAAC,CAAe,GAAC,CAAG,OAAC,EAAQ,kBAAA,GAAqB,EAAA,CAAG,CAAC;SAmCpE;QAED,qBAlCI,SAAA,GAAY,kBAAA,CAAmB,WAAC,CAAW,CAAC;QAmChD,uBAlCM,OAAA,GAAU,IAAI,UAAA,CAAW,KAAC,CAAK,CAAC;QAoCtC,uBAlCM,KAAA,GAAQ,KAAA,IAAS,KAAA,CAAM,cAAC,CAAc,OAAC,CAAO,CAAC;QAmCrD,IAAI,CAlCC,KAAC,IAAQ,SAAA,EAAW;YAmCvB,OAAO,CAlCC,aAAC,CAAa,SAAC,CAAS,OAAC,CAAO,CAAC;SAmC1C;QAED,kBAAkB,CAlCC,WAAC,CAAW,GAAG,OAAA,CAAQ;QAoC1C,IAAI,CAlCC,SAAC,EAAU;YAmCd,SAAS,GAlCG,mBAAA,CAAoB;SAmCjC;aAlCM,IAAA,SAAK,KAAa,mBAAA,EAAqB;YAmC5C,OAlCO,MAAA,CAAO;SAmCf;QAED,uBAlCM,gBAAA,GAmCF,eAAe,CAlCC,IAAC,CAAI,OAAC,CAAO,gBAAC,EAAiB,OAAA,EAAS,EAAA,CAAG,CAAC;QAmChE,gBAAgB,CAlCC,OAAC,CAAO,MAAC,IAkC9B;;;;;YAKM,IAAI,MAlCC,CAAM,WAAC,IAAc,IAAA,CAAK,EAAC,IAAK,MAAA,CAAO,WAAC,IAAc,WAAA,IAAe,MAAA,CAAO,MAAC,EAAO;gBAmCvF,MAAM,CAlCC,OAAC,EAAO,CAAE;aAmClB;SACF,CAlCC,CAAC;QAoCH,qBAlCI,UAAA,GAAa,OAAA,CAAQ,eAAC,CAAe,SAAC,CAAS,KAAC,EAAM,OAAA,CAAQ,KAAC,CAAK,CAAC;QAmCzE,qBAlCI,oBAAA,GAAuB,KAAA,CAAM;QAmCjC,IAAI,CAlCC,UAAC,EAAW;YAmCf,IAAI,CAlCC,iBAAC;gBAAkB,OAAA;YAmCxB,UAAU,GAlCG,OAAA,CAAQ,kBAAC,CAAkB;YAmCxC,oBAAoB,GAlCG,IAAA,CAAK;SAmC7B;QAED,IAAI,CAlCC,OAAC,CAAO,kBAAC,EAAkB,CAAE;QAmClC,IAAI,CAlCC,MAAC,CAAM,IAAC,CAmCT,EAAC,OAlCC,EAAQ,WAAA,EAAa,UAAA,EAAY,SAAA,EAAW,OAAA,EAAS,MAAA,EAAQ,oBAAA,EAAqB,CAAC,CAAC;QAoC1F,IAAI,CAlCC,oBAAC,EAAqB;YAmCzB,QAAQ,CAlCC,OAAC,EAAQ,sBAAA,CAAuB,CAAC;SAmC3C;QAED,MAAM,CAlCC,MAAC,CAAM,MAkClB;YACM,WAAW,CAlCC,OAAC,EAAQ,sBAAA,CAAuB,CAAC;YAoC7C,qBAlCI,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,OAAC,CAAO,MAAC,CAAM,CAAC;YAmCzC,IAAI,KAlCC,IAAQ,CAAA,EAAG;gBAmCd,IAAI,CAlCC,OAAC,CAAO,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;aAmC/B;YAED,uBAlCM,OAAA,GAAU,IAAA,CAAK,OAAC,CAAO,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;YAmC3D,IAAI,OAlCC,EAAQ;gBAmCX,qBAlCI,KAAA,GAAQ,OAAA,CAAQ,OAAC,CAAO,MAAC,CAAM,CAAC;gBAmCpC,IAAI,KAlCC,IAAQ,CAAA,EAAG;oBAmCd,OAAO,CAlCC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;iBAmC1B;aACF;SACF,CAlCC,CAAC;QAoCH,IAAI,CAlCC,OAAC,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC;QAmC1B,gBAAgB,CAlCC,IAAC,CAAI,MAAC,CAAM,CAAC;QAoC9B,OAlCO,MAAA,CAAO;KAmCf;;;;;IAKH,UArCG,CAAA,IAAA,EAqCH;QACI,OArCO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,CAAC;QAuC5B,IAAI,CArCC,OAAC,CAAO,eAAC,CAAe,OAAC,CAAO,CAAC,QAAC,EAAS,OAAA,KAqCpD,EArCkE,OAAO,QAAA,CAAS,IAAC,CAAI,CAAC,EAAC,CAAE,CAAC;QAuCxF,IAAI,CArCC,iBAAC,CAAiB,OAAC,CAAO,CAAC,SAAC,EAAU,OAAA,KAqC/C;YACM,IAAI,CArCC,iBAAC,CAAiB,GAAC,CAsCpB,OAAO,EArCE,SAAA,CAAU,MAAC,CAAM,KAAC,IAoCrC,EApC+C,OAAO,KAAA,CAAM,IAAC,IAAO,IAAA,CAAK,EAAC,CAAE,CAAC,CAAC;SAsCzE,CArCC,CAAC;KAsCJ;;;;;IAKH,iBAxCG,CAAA,OAAA,EAwCH;QACI,IAAI,CAxCC,OAAC,CAAO,eAAC,CAAe,MAAC,CAAM,OAAC,CAAO,CAAC;QAyC7C,IAAI,CAxCC,iBAAC,CAAiB,MAAC,CAAM,OAAC,CAAO,CAAC;QAyCvC,uBAxCM,cAAA,GAAiB,IAAA,CAAK,OAAC,CAAO,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;QAyClE,IAAI,cAxCC,EAAe;YAyClB,cAAc,CAxCC,OAAC,CAAO,MAAC,IAAS,MAAA,CAAO,OAAC,EAAO,CAAE,CAAC;YAyCnD,IAAI,CAxCC,OAAC,CAAO,gBAAC,CAAgB,MAAC,CAAM,OAAC,CAAO,CAAC;SAyC/C;KACF;;;;;;;IAtCA,kBAAA,CAAA,WAAA,EAAA,OAAA,EAAA,OAAH,GAAG,KAAA,EAAH;QA8CI,IAAI,CA7CC,OAAC,CAAO,MAAC,CAAM,KAAC,CAAK,WAAC,EAAY,mBAAA,EAAqB,IAAA,CAAK,CAAC,OAAC,CAAO,GAAC,IA6C/E;YACM,IAAI,OA7CC,IAAU,aAAA,CAAc,GAAC,EAAI,IAAA,CAAK,cAAC,CAAc,EAAE;gBA8CtD,uBA7CM,OAAA,GAAU,IAAA,CAAK,OAAC,CAAO,uBAAC,CAAuB,GAAC,CAAG,GAAC,CAAG,CAAC;;gBAgD9D,IAAI,OA7CC,EAAQ;oBA8CX,OAAO,CA7CC,UAAC,CAAU,GAAC,EAAI,OAAA,EAAS,IAAA,CAAK,CAAC;iBA8CxC;gBAED,IAAI,CA7CC,UAAC,CAAU,GAAC,EAAI,OAAA,EAAS,IAAA,CAAK,CAAC;aA8CrC;iBA7CM;gBA8CL,IAAI,CA7CC,iBAAC,CAAiB,GAAC,CAAG,CAAC;aA8C7B;SACF,CA7CC,CAAC;KA8CJ;;;;;;;IAOH,UAlDG,CAAA,OAAA,EAAA,OAAA,EAAA,YAAA,EAkDH;QACI,uBAlDM,MAAA,GAAS,IAAA,CAAK,OAAC,CAAO;QAoD5B,IAAI,CAlDC,YAAC,IAAe,OAAA,CAAQ,iBAAC,EAAkB;YAmD9C,IAAI,CAlDC,kBAAC,CAAkB,OAAC,EAAQ,OAAA,EAAS,IAAA,CAAK,CAAC;SAmDjD;QAED,uBAlDM,aAAA,GAAgB,MAAA,CAAO,eAAC,CAAe,GAAC,CAAG,OAAC,CAAO,CAAC;QAmD1D,IAAI,aAlDC,EAAc;YAmDjB,uBAlDM,OAAA,GAAuC,EAAA,CAAG;YAmDhD,MAAM,CAlDC,IAAC,CAAI,aAAC,CAAa,CAAC,OAAC,CAAO,WAAC,IAkD1C;;;gBAGQ,IAAI,IAlDC,CAAI,SAAC,CAAS,WAAC,CAAW,EAAE;oBAmD/B,uBAlDM,MAAA,GAAS,IAAA,CAAK,OAAC,CAAO,OAAC,EAAQ,WAAA,EAAa,UAAA,EAAY,KAAA,CAAM,CAAC;oBAmDrE,IAAI,MAlDC,EAAO;wBAmDV,OAAO,CAlDC,IAAC,CAAI,MAAC,CAAM,CAAC;qBAmDtB;iBACF;aACF,CAlDC,CAAC;YAoDH,IAAI,OAlDC,CAAO,MAAC,EAAO;gBAmDlB,MAAM,CAlDC,oBAAC,CAAoB,IAAC,CAAI,EAAC,EAAG,OAAA,EAAS,IAAA,EAAM,OAAA,CAAQ,CAAC;gBAmD7D,mBAAmB,CAlDC,OAAC,CAAO,CAAC,MAAC,CAAM,MAAM,MAAA,CAAO,gBAAC,CAAgB,OAAC,CAAO,CAAC,CAAC;gBAmD5E,OAAO;aACR;SACF;;;QAID,qBAlDI,iCAAA,GAAoC,KAAA,CAAM;QAmD9C,IAAI,MAlDC,CAAM,eAAC,EAAgB;YAmD1B,uBAlDM,cAAA,GAmDF,MAAM,CAlDC,OAAC,CAAO,MAAC,GAAQ,MAAA,CAAO,uBAAC,CAAuB,GAAC,CAAG,OAAC,CAAO,GAAG,EAAA,CAAG;;;;;YAwD7E,IAAI,cAlDC,IAAiB,cAAA,CAAe,MAAC,EAAO;gBAmD3C,iCAAiC,GAlDG,IAAA,CAAK;aAmD1C;iBAlDM;gBAmDL,qBAlDI,MAAA,GAAS,OAAA,CAAQ;gBAmDrB,OAAO,MAlDC,GAAQ,MAAA,CAAO,UAAC,EAAW;oBAmDjC,uBAlDM,QAAA,GAAW,MAAA,CAAO,eAAC,CAAe,GAAC,CAAG,MAAC,CAAM,CAAC;oBAmDpD,IAAI,QAlDC,EAAS;wBAmDZ,iCAAiC,GAlDG,IAAA,CAAK;wBAmDzC,MAAM;qBACP;iBACF;aACF;SACF;;;;;QAMD,uBAlDM,SAAA,GAAY,IAAA,CAAK,iBAAC,CAAiB,GAAC,CAAG,OAAC,CAAO,CAAC;QAmDtD,IAAI,SAlDC,EAAU;YAmDb,uBAlDM,eAAA,GAAkB,IAAI,GAAA,EAAW,CAAG;YAmD1C,SAAS,CAlDC,OAAC,CAAO,QAAC,IAkDzB;gBACQ,uBAlDM,WAAA,GAAc,QAAA,CAAS,IAAC,CAAI;gBAmDlC,IAAI,eAlDC,CAAe,GAAC,CAAG,WAAC,CAAW;oBAAE,OAAA;gBAmDtC,eAAe,CAlDC,GAAC,CAAG,WAAC,CAAW,CAAC;gBAoDjC,uBAlDM,OAAA,GAAU,IAAA,CAAK,SAAC,CAAS,WAAC,CAAW,CAAC;gBAmD5C,uBAlDM,UAAA,GAAa,OAAA,CAAQ,kBAAC,CAAkB;gBAmD9C,uBAlDM,aAAA,KAAgB,MAAA,CAAO,eAAC,CAAe,GAAC,CAAG,OAAC,CAAO,EAAA,CAAG;gBAmD5D,uBAlDM,SAAA,GAAY,aAAA,CAAc,WAAC,CAAW,IAAI,mBAAA,CAAoB;gBAmDpE,uBAlDM,OAAA,GAAU,IAAI,UAAA,CAAW,UAAC,CAAU,CAAC;gBAmD3C,uBAlDM,MAAA,GAAS,IAAI,yBAAA,CAA0B,IAAC,CAAI,EAAC,EAAG,WAAA,EAAa,OAAA,CAAQ,CAAC;gBAoD5E,IAAI,CAlDC,OAAC,CAAO,kBAAC,EAAkB,CAAE;gBAmDlC,IAAI,CAlDC,MAAC,CAAM,IAAC,CAAI;oBAmDf,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,MAAM;oBACN,oBAAoB,EAlDE,IAAA;iBAmDvB,CAlDC,CAAC;aAmDJ,CAlDC,CAAC;SAmDJ;;;QAID,IAAI,iCAlDC,EAAkC;YAmDrC,MAAM,CAlDC,oBAAC,CAAoB,IAAC,CAAI,EAAC,EAAG,OAAA,EAAS,KAAA,EAAO,OAAA,CAAQ,CAAC;SAmD/D;aAlDM;;;YAqDL,MAAM,CAlDC,UAAC,CAAU,MAAM,IAAA,CAAK,iBAAC,CAAiB,OAAC,CAAO,CAAC,CAAC;YAmDzD,MAAM,CAlDC,sBAAC,CAAsB,OAAC,CAAO,CAAC;YAmDvC,MAAM,CAlDC,kBAAC,CAAkB,OAAC,EAAQ,OAAA,CAAQ,CAAC;SAmD7C;KACF;;;;;;IAMH,UAtDG,CAAA,OAAA,EAAA,MAAA,EAsDH,EAtDgD,QAAA,CAAS,OAAC,EAAQ,IAAA,CAAK,cAAC,CAAc,CAAC,EAAC;;;;;IA2DxF,sBAzDG,CAAA,WAAA,EAyDH;QACI,uBAzDM,YAAA,GAAmC,EAAA,CAAG;QA0D5C,IAAI,CAzDC,MAAC,CAAM,OAAC,CAAO,KAAC,IAyDzB;YACM,uBAzDM,MAAA,GAAS,KAAA,CAAM,MAAC,CAAM;YA0D5B,IAAI,MAzDC,CAAM,SAAC;gBAAU,OAAA;YA2DtB,uBAzDM,OAAA,GAAU,KAAA,CAAM,OAAC,CAAO;YA0D9B,uBAzDM,SAAA,GAAY,IAAA,CAAK,iBAAC,CAAiB,GAAC,CAAG,OAAC,CAAO,CAAC;YA0DtD,IAAI,SAzDC,EAAU;gBA0Db,SAAS,CAzDC,OAAC,CAAO,CAAC,QAAU,KAyDrC;oBACU,IAAI,QAzDC,CAAQ,IAAC,IAAO,KAAA,CAAM,WAAC,EAAY;wBA0DtC,uBAzDM,SAAA,GAAY,kBAAA,CA0Dd,OAAO,EAzDE,KAAA,CAAM,WAAC,EAAY,KAAA,CAAM,SAAC,CAAS,KAAC,EAAM,KAAA,CAAM,OAAC,CAAO,KAAC,CAAK,CAAC;wBA0D5E,EAAoB,SAzDN,GAAK,OAAC,CAAO,GAAG,WAAA,CAAY;wBA0D1C,cAAc,CAzDC,KAAC,CAAK,MAAC,EAAO,QAAA,CAAS,KAAC,EAAM,SAAA,EAAW,QAAA,CAAS,QAAC,CAAQ,CAAC;qBA0D5E;iBACF,CAzDC,CAAC;aA0DJ;YAED,IAAI,MAzDC,CAAM,gBAAC,EAAiB;gBA0D3B,IAAI,CAzDC,OAAC,CAAO,UAAC,CAAU,MAyDhC;;;oBAGU,MAAM,CAzDC,OAAC,EAAO,CAAE;iBA0DlB,CAzDC,CAAC;aA0DJ;iBAzDM;gBA0DL,YAAY,CAzDC,IAAC,CAAI,KAAC,CAAK,CAAC;aA0D1B;SACF,CAzDC,CAAC;QA2DH,IAAI,CAzDC,MAAC,GAAQ,EAAA,CAAG;QA2DjB,OAzDO,YAAA,CAAa,IAAC,CAAI,CAAC,CAAC,EAAE,CAAA,KAyDjC;;;YAGM,uBAzDM,EAAA,GAAK,CAAA,CAAE,UAAC,CAAU,GAAC,CAAG,QAAC,CAAQ;YA0DrC,uBAzDM,EAAA,GAAK,CAAA,CAAE,UAAC,CAAU,GAAC,CAAG,QAAC,CAAQ;YA0DrC,IAAI,EAzDC,IAAK,CAAA,IAAK,EAAA,IAAM,CAAA,EAAG;gBA0DtB,OAzDO,EAAA,GAAK,EAAA,CAAG;aA0DhB;YACD,OAzDO,IAAA,CAAK,OAAC,CAAO,MAAC,CAAM,eAAC,CAAe,CAAC,CAAC,OAAC,EAAQ,CAAA,CAAE,OAAC,CAAO,GAAG,CAAA,GAAI,CAAA,CAAE,CAAC;SA0D3E,CAzDC,CAAC;KA0DJ;;;;;IAKH,OA5DG,CAAA,OAAA,EA4DH;QACI,IAAI,CA5DC,OAAC,CAAO,OAAC,CAAO,CAAC,IAAI,CAAA,CAAE,OAAC,EAAO,CAAE,CAAC;QA6DvC,IAAI,CA5DC,kBAAC,CAAkB,IAAC,CAAI,WAAC,EAAY,OAAA,CAAQ,CAAC;KA6DpD;;;;;IAKH,mBA/DG,CAAA,OAAA,EA+DH;QACI,qBA/DI,YAAA,GAAe,KAAA,CAAM;QAgEzB,IAAI,IA/DC,CAAI,iBAAC,CAAiB,GAAC,CAAG,OAAC,CAAO;YAAE,YAAA,GAAe,IAAA,CAAK;QAgE7D,YAAY;YACR,CAAC,IA/DC,CAAI,MAAC,CAAM,IAAC,CAAI,KAAC,IAAQ,KAAA,CAAM,OAAC,KAAW,OAAA,CAAQ,GAAG,IAAA,GAAO,KAAA,KAAU,YAAA,CAAa;QAgE1F,OA/DO,YAAA,CAAa;KAgErB;CACF;AAED,AAyBA,AAAA,MAAA,yBAAA,CAAA;;;;;IA0BA,WAAA,CArFqB,MAAQ,EAAyB,WAAa,EAqFnE;QArFqB,IAArB,CAAA,MAAqB,GAAA,MAAA,CAAQ;QAAyB,IAAtD,CAAA,WAAsD,GAAA,WAAA,CAAa;QAtB1D,IAAT,CAAA,OAAS,GAAuC,EAAA,CAAG;QAC1C,IAAT,CAAA,eAAS,GAAkB,IAAI,GAAA,EAAsC,CAAG;QAC/D,IAAT,CAAA,gBAAS,GAAmB,IAAI,GAAA,EAAqC,CAAG;QAC/D,IAAT,CAAA,uBAAS,GAA0B,IAAI,GAAA,EAAqC,CAAG;QACtE,IAAT,CAAA,eAAS,GAAkB,IAAI,GAAA,EAA6C,CAAG;QACtE,IAAT,CAAA,eAAS,GAAkB,CAAA,CAAE;QACpB,IAAT,CAAA,kBAAS,GAAqB,CAAA,CAAE;QAEtB,IAAV,CAAA,gBAAU,GAAiE,EAAA,CAAG;QACpE,IAAV,CAAA,cAAU,GAAiD,EAAA,CAAG;QACpD,IAAV,CAAA,SAAU,GAA2B,EAAA,CAAG;QAC9B,IAAV,CAAA,aAAU,GAA+B,EAAA,CAAG;QAEnC,IAAT,CAAA,uBAAS,GAA0B,IAAI,GAAA,EAAsC,CAAG;QACvE,IAAT,CAAA,sBAAS,GAAgC,EAAA,CAAG;QACnC,IAAT,CAAA,sBAAS,GAAgC,EAAA,CAAG;QAGnC,IAAT,CAAA,iBAAS,GAAoB,CAAA,OAAU,EAAK,OAAS,KAArD,GAA6D,CAAG;KAI4B;;;;;;IAgF5F,kBAlFG,CAAA,OAAA,EAAA,OAAA,EAkFH,EAlFmD,IAAA,CAAK,iBAAC,CAAiB,OAAC,EAAQ,OAAA,CAAQ,CAAC,EAAC;;;;IA4F7F,IAxFG,aAAA,GAwFH;QACI,uBAxFM,OAAA,GAAuC,EAAA,CAAG;QAyFhD,IAAI,CAxFC,cAAC,CAAc,OAAC,CAAO,EAAC,IAwFjC;YACM,EAAE,CAxFC,OAAC,CAAO,OAAC,CAAO,MAAC,IAwF1B;gBACQ,IAAI,MAxFC,CAAM,MAAC,EAAO;oBAyFjB,OAAO,CAxFC,IAAC,CAAI,MAAC,CAAM,CAAC;iBAyFtB;aACF,CAxFC,CAAC;SAyFJ,CAxFC,CAAC;QAyFH,OAxFO,OAAA,CAAQ;KAyFhB;;;;;;IAMH,eA5FG,CAAA,WAAA,EAAA,WAAA,EA4FH;QACI,uBA5FM,EAAA,GAAK,IAAI,4BAAA,CAA6B,WAAC,EAAY,WAAA,EAAa,IAAA,CAAK,CAAC;QA6F5E,IAAI,WA5FC,CAAW,UAAC,EAAW;YA6F1B,IAAI,CA5FC,qBAAC,CAAqB,EAAC,EAAG,WAAA,CAAY,CAAC;SA6F7C;aA5FM;;;;YAgGL,IAAI,CA5FC,eAAC,CAAe,GAAC,CAAG,WAAC,EAAY,EAAA,CAAG,CAAC;;;;;;YAmG1C,IAAI,CA5FC,mBAAC,CAAmB,WAAC,CAAW,CAAC;SA6FvC;QACD,OA5FO,IAAA,CAAK,gBAAC,CAAgB,WAAC,CAAW,GAAG,EAAA,CAAG;KA6FhD;;;;;;IA1FA,qBAAA,CAAA,EAAA,EAAA,WAAA,EAAH;QAiGI,uBAhGM,KAAA,GAAQ,IAAA,CAAK,cAAC,CAAc,MAAC,GAAQ,CAAA,CAAE;QAiG7C,IAAI,KAhGC,IAAQ,CAAA,EAAG;YAiGd,qBAhGI,KAAA,GAAQ,KAAA,CAAM;YAiGlB,KAAK,qBAhGI,CAAA,GAAI,KAAA,EAAO,CAAA,IAAK,CAAA,EAAG,CAAA,EAAE,EAAG;gBAiG/B,uBAhGM,aAAA,GAAgB,IAAA,CAAK,cAAC,CAAc,CAAC,CAAC,CAAC;gBAiG7C,IAAI,IAhGC,CAAI,MAAC,CAAM,eAAC,CAAe,aAAC,CAAa,WAAC,EAAY,WAAA,CAAY,EAAE;oBAiGvE,IAAI,CAhGC,cAAC,CAAc,MAAC,CAAM,CAAC,GAAG,CAAA,EAAG,CAAA,EAAG,EAAA,CAAG,CAAC;oBAiGzC,KAAK,GAhGG,IAAA,CAAK;oBAiGb,MAAM;iBACP;aACF;YACD,IAAI,CAhGC,KAAC,EAAM;gBAiGV,IAAI,CAhGC,cAAC,CAAc,MAAC,CAAM,CAAC,EAAE,CAAA,EAAG,EAAA,CAAG,CAAC;aAiGtC;SACF;aAhGM;YAiGL,IAAI,CAhGC,cAAC,CAAc,IAAC,CAAI,EAAC,CAAE,CAAC;SAiG9B;QAED,IAAI,CAhGC,uBAAC,CAAuB,GAAC,CAAG,WAAC,EAAY,EAAA,CAAG,CAAC;QAiGlD,OAhGO,EAAA,CAAG;KAiGX;;;;;;IAMH,QApGG,CAAA,WAAA,EAAA,WAAA,EAoGH;QACI,qBApGI,EAAA,GAAK,IAAA,CAAK,gBAAC,CAAgB,WAAC,CAAW,CAAC;QAqG5C,IAAI,CApGC,EAAC,EAAG;YAqGP,EAAE,GApGG,IAAA,CAAK,eAAC,CAAe,WAAC,EAAY,WAAA,CAAY,CAAC;SAqGrD;QACD,OApGO,EAAA,CAAG;KAqGX;;;;;;;IAOH,eAzGG,CAAA,WAAA,EAAA,IAAA,EAAA,OAAA,EAyGH;QACI,qBAzGI,EAAA,GAAK,IAAA,CAAK,gBAAC,CAAgB,WAAC,CAAW,CAAC;QA0G5C,IAAI,EAzGC,IAAK,EAAA,CAAG,QAAC,CAAQ,IAAC,EAAK,OAAA,CAAQ,EAAE;YA0GpC,IAAI,CAzGC,eAAC,EAAe,CAAE;SA0GxB;KACF;;;;;;IAMH,OA7GG,CAAA,WAAA,EAAA,OAAA,EA6GH;QACI,IAAI,CA7GC,WAAC;YAAY,OAAA;QA+GlB,uBA7GM,EAAA,GAAK,IAAA,CAAK,eAAC,CAAe,WAAC,CAAW,CAAC;QA+G7C,IAAI,CA7GC,UAAC,CAAU,MA6GpB;YACM,IAAI,CA7GC,uBAAC,CAAuB,MAAC,CAAM,EAAC,CAAE,WAAC,CAAW,CAAC;YA8GpD,OA7GO,IAAA,CAAK,gBAAC,CAAgB,WAAC,CAAW,CAAC;YA8G1C,uBA7GM,KAAA,GAAQ,IAAA,CAAK,cAAC,CAAc,OAAC,CAAO,EAAC,CAAE,CAAC;YA8G9C,IAAI,KA7GC,IAAQ,CAAA,EAAG;gBA8Gd,IAAI,CA7GC,cAAC,CAAc,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;aA8GtC;SACF,CA7GC,CAAC;QA+GH,IAAI,CA7GC,wBAAC,CAAwB,MAAM,EAAA,CAAG,OAAC,CAAO,OAAC,CAAO,CAAC,CAAC;KA8G1D;;;;;IA3GA,eAAA,CAAA,EAAA,EAAH,EAAwC,OAAO,IAAA,CAAK,gBAAC,CAAgB,EAAC,CAAE,CAAC,EAAC;;;;;;;;IAwH1E,OAtHG,CAAA,WAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAsHH;QACI,IAAI,aAtHC,CAAa,OAAC,CAAO,EAAE;YAuH1B,IAAI,CAtHC,eAAC,CAAe,WAAC,CAAW,CAAC,OAAC,CAAO,OAAC,EAAQ,IAAA,EAAM,KAAA,CAAM,CAAC;YAuHhE,OAtHO,IAAA,CAAK;SAuHb;QACD,OAtHO,KAAA,CAAM;KAuHd;;;;;;;;IAQH,UA5HG,CAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EA4HH;QACI,IAAI,CA5HC,aAAC,CAAa,OAAC,CAAO;YAAE,OAAA;;;QAgI7B,uBA5HM,OAAA,IAAU,OAAA,CAAQ,YAAC,CAAgB,CAAA,CAAsB;QA6H/D,IAAI,OA5HC,IAAU,OAAA,CAAQ,aAAC,EAAc;YA6HpC,OAAO,CA5HC,aAAC,GAAe,KAAA,CAAM;SA6H/B;;;;QAKD,IAAI,WA5HC,EAAY;YA6Hf,IAAI,CA5HC,eAAC,CAAe,WAAC,CAAW,CAAC,UAAC,CAAU,OAAC,EAAQ,MAAA,CAAO,CAAC;SA6H/D;;QAGD,IAAI,YA5HC,EAAa;YA6HhB,IAAI,CA5HC,mBAAC,CAAmB,OAAC,CAAO,CAAC;SA6HnC;KACF;;;;;IAKH,mBA/HG,CAAA,OAAA,EA+HH,EA/HsC,IAAA,CAAK,sBAAC,CAAsB,IAAC,CAAI,OAAC,CAAO,CAAC,EAAC;;;;;;;;IAuIjF,UArIG,CAAA,WAAA,EAAA,OAAA,EAAA,OAAA,EAAA,YAAA,EAqIH;QACI,IAAI,CArIC,aAAC,CAAa,OAAC,CAAO,EAAE;YAsI3B,IAAI,CArIC,kBAAC,CAAkB,OAAC,EAAQ,OAAA,CAAQ,CAAC;YAsI1C,OAAO;SACR;QAED,uBArIM,EAAA,GAAK,WAAA,GAAc,IAAA,CAAK,eAAC,CAAe,WAAC,CAAW,GAAG,IAAA,CAAK;QAsIlE,IAAI,EArIC,EAAG;YAsIN,EAAE,CArIC,UAAC,CAAU,OAAC,EAAQ,OAAA,EAAS,YAAA,CAAa,CAAC;SAsI/C;aArIM;YAsIL,IAAI,CArIC,oBAAC,CAAoB,WAAC,EAAY,OAAA,EAAS,KAAA,EAAO,OAAA,CAAQ,CAAC;SAsIjE;KACF;;;;;;;;IAQH,oBA3IG,CAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,OAAA,EA2IH;QACI,IAAI,CA3IC,sBAAC,CAAsB,IAAC,CAAI,OAAC,CAAO,CAAC;QA4I1C,OAAO,CA3IC,YAAC,CAAY,GAAG;YA4ItB,WAAW;YACX,aAAa,EA3IE,OAAA,EAAS,YAAA;YA4IxB,oBAAoB,EA3IE,KAAA;SA4IvB,CA3IC;KA4IH;;;;;;;;;IASH,MAlJG,CAmJG,WAAmB,EAAE,OAAY,EAAE,IAAY,EAAE,KAAa,EAC9D,QAAiC,EAFvC;QAGI,IAAI,aAlJC,CAAa,OAAC,CAAO,EAAE;YAmJ1B,OAlJO,IAAA,CAAK,eAAC,CAAe,WAAC,CAAW,CAAC,MAAC,CAAM,OAAC,EAAQ,IAAA,EAAM,KAAA,EAAO,QAAA,CAAS,CAAC;SAmJjF;QACD,OAlJO,MAkJX,GAlJiB,CAAG;KAmJjB;;;;;;IAhJA,iBAAA,CAAA,KAAA,EAAA,YAAA,EAAH;QAuJI,OAtJO,KAAA,CAAM,UAAC,CAAU,KAAC,CAuJrB,IAAI,CAtJC,MAAC,EAAO,KAAA,CAAM,OAAC,EAAQ,KAAA,CAAM,SAAC,CAAS,KAAC,EAAM,KAAA,CAAM,OAAC,CAAO,KAAC,EAuJlE,KAAK,CAtJC,OAAC,CAAO,OAAC,EAAQ,YAAA,CAAa,CAAC;KAuJ1C;;;;;IAKH,sBAzJG,CAAA,gBAAA,EAyJH;QACI,qBAzJI,QAAA,GAAW,IAAA,CAAK,MAAC,CAAM,KAAC,CAAK,gBAAC,EAAiB,mBAAA,EAAqB,IAAA,CAAK,CAAC;QA0J9E,QAAQ,CAzJC,OAAC,CAAO,OAAC,IAyJtB;YACM,uBAzJM,OAAA,GAAU,IAAA,CAAK,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;YA0JnD,IAAI,OAzJC,EAAQ;gBA0JX,OAAO,CAzJC,OAAC,CAAO,MAAC,IAyJzB;;;;oBAIU,IAAI,MAzJC,CAAM,MAAC,EAAO;wBA0JjB,MAAM,CAzJC,gBAAC,GAAkB,IAAA,CAAK;qBA0JhC;yBAzJM;wBA0JL,MAAM,CAzJC,OAAC,EAAO,CAAE;qBA0JlB;iBACF,CAzJC,CAAC;aA0JJ;YACD,uBAzJM,QAAA,GAAW,IAAA,CAAK,eAAC,CAAe,GAAC,CAAG,OAAC,CAAO,CAAC;YA0JnD,IAAI,QAzJC,EAAS;gBA0JZ,MAAM,CAzJC,IAAC,CAAI,QAAC,CAAQ,CAAC,OAAC,CAAO,WAAC,IAAc,QAAA,CAAS,WAAC,CAAW,GAAG,mBAAA,CAAoB,CAAC;aA0J3F;SACF,CAzJC,CAAC;QA2JH,IAAI,IAzJC,CAAI,uBAAC,CAAuB,IAAC,IAAO,CAAA;YAAG,OAAA;QA2J5C,QAAQ,GAzJG,IAAA,CAAK,MAAC,CAAM,KAAC,CAAK,gBAAC,EAAiB,qBAAA,EAAuB,IAAA,CAAK,CAAC;QA0J5E,IAAI,QAzJC,CAAQ,MAAC,EAAO;YA0JnB,QAAQ,CAzJC,OAAC,CAAO,OAAC,IAyJxB;gBACQ,uBAzJM,OAAA,GAAU,IAAA,CAAK,uBAAC,CAAuB,GAAC,CAAG,OAAC,CAAO,CAAC;gBA0J1D,IAAI,OAzJC,EAAQ;oBA0JX,OAAO,CAzJC,OAAC,CAAO,MAAC,IAAS,MAAA,CAAO,MAAC,EAAM,CAAE,CAAC;iBA0J5C;aACF,CAzJC,CAAC;SA0JJ;KACF;;;;IAIH,iBA3JG,GA2JH;QACI,OA3JO,IAAI,OAAA,CAAQ,OAAC,IA2JxB;YACM,IAAI,IA3JC,CAAI,OAAC,CAAO,MAAC,EAAO;gBA4JvB,OA3JO,mBAAA,CAAoB,IAAC,CAAI,OAAC,CAAO,CAAC,MAAC,CAAM,MAAM,OAAA,EAAQ,CAAE,CAAC;aA4JlE;iBA3JM;gBA4JL,OAAO,EA3JC,CAAE;aA4JX;SACF,CA3JC,CAAC;KA4JJ;;;;;IAKH,gBA9JG,CAAA,OAAA,EA8JH;QACI,uBA9JM,OAAA,IAAU,OAAA,CAAQ,YAAC,CAAgB,CAAA,CAAsB;QA+J/D,IAAI,OA9JC,IAAU,OAAA,CAAQ,aAAC,EAAc;;YAgKpC,OAAO,CA9JC,YAAC,CAAY,GAAG,kBAAA,CAAmB;YA+J3C,IAAI,OA9JC,CAAO,WAAC,EAAY;gBA+JvB,IAAI,CA9JC,sBAAC,CAAsB,OAAC,CAAO,CAAC;gBA+JrC,uBA9JM,EAAA,GAAK,IAAA,CAAK,eAAC,CAAe,OAAC,CAAO,WAAC,CAAW,CAAC;gBA+JrD,IAAI,EA9JC,EAAG;oBA+JN,EAAE,CA9JC,iBAAC,CAAiB,OAAC,CAAO,CAAC;iBA+J/B;aACF;YACD,IAAI,CA9JC,kBAAC,CAAkB,OAAC,EAAQ,OAAA,CAAQ,aAAC,CAAa,CAAC;SA+JzD;KACF;;;;;IAKH,KAjKG,CAAA,WAiKH,GAjKG,CAAA,CAAA,EAiKH;QACI,qBAjKI,OAAA,GAA6B,EAAA,CAAG;QAkKpC,IAAI,IAjKC,CAAI,eAAC,CAAe,IAAC,EAAK;YAkK7B,IAAI,CAjKC,eAAC,CAAe,OAAC,CAAO,CAAC,EAAC,EAAG,OAAA,KAAY,IAAA,CAAK,qBAAC,CAAqB,EAAC,EAAG,OAAA,CAAQ,CAAC,CAAC;YAkKvF,IAAI,CAjKC,eAAC,CAAe,KAAC,EAAK,CAAE;SAkK9B;QAED,IAAI,IAjKC,CAAI,cAAC,CAAc,MAAC;aAkKpB,IAjKC,CAAI,kBAAC,IAAqB,IAAA,CAAK,sBAAC,CAAsB,MAAC,CAAM,EAAE;YAkKnE,OAAO,GAjKG,IAAA,CAAK,gBAAC,CAAgB,WAAC,CAAW,CAAC;SAkK9C;aAjKM;YAkKL,KAAK,qBAjKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,IAAA,CAAK,sBAAC,CAAsB,MAAC,EAAO,CAAA,EAAE,EAAG;gBAkK3D,uBAjKM,OAAA,GAAU,IAAA,CAAK,sBAAC,CAAsB,CAAC,CAAC,CAAC;gBAkK/C,IAAI,CAjKC,gBAAC,CAAgB,OAAC,CAAO,CAAC;aAkKhC;SACF;QAED,IAAI,CAjKC,kBAAC,GAAoB,CAAA,CAAE;QAkK5B,IAAI,CAjKC,sBAAC,CAAsB,MAAC,GAAQ,CAAA,CAAE;QAkKvC,IAAI,CAjKC,sBAAC,CAAsB,MAAC,GAAQ,CAAA,CAAE;QAkKvC,IAAI,CAjKC,SAAC,CAAS,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC;QAkKnC,IAAI,CAjKC,SAAC,GAAW,EAAA,CAAG;QAmKpB,IAAI,IAjKC,CAAI,aAAC,CAAa,MAAC,EAAO;;;;YAqK7B,uBAjKM,QAAA,GAAW,IAAA,CAAK,aAAC,CAAa;YAkKpC,IAAI,CAjKC,aAAC,GAAe,EAAA,CAAG;YAmKxB,IAAI,OAjKC,CAAO,MAAC,EAAO;gBAkKlB,mBAAmB,CAjKC,OAAC,CAAO,CAAC,MAAC,CAAM,MAiK5C,EAjKoD,QAAA,CAAS,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC,EAAC,CAAE,CAAC;aAkK9E;iBAjKM;gBAkKL,QAAQ,CAjKC,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC;aAkK9B;SACF;KACF;;;;;IA/JA,gBAAA,CAAA,WAAA,EAAH;QAqKI,uBApKM,YAAA,GAAe,IAAI,qBAAA,EAAsB,CAAE;QAqKjD,uBApKM,cAAA,GAA8C,EAAA,CAAG;QAqKvD,uBApKM,iBAAA,GAAoB,IAAI,GAAA,EAA2B,CAAG;QAqK5D,uBApKM,kBAAA,GAAyC,EAAA,CAAG;QAqKlD,uBApKM,eAAA,GAAkB,IAAI,GAAA,EAAqC,CAAG;QAqKpE,uBApKM,mBAAA,GAAsB,IAAI,GAAA,EAAqB,CAAG;QAqKxD,uBApKM,oBAAA,GAAuB,IAAI,GAAA,EAAqB,CAAG;QAsKzD,uBApKM,QAAA,GAAW,WAAA,EAAY,CAAE;QAqK/B,uBApKM,aAAA,GAAuB,IAAA,CAAK,sBAAC,CAAsB,MAAC;YAqKtD,IAAI,CApKC,sBAAC,CAAsB,MAAC,CAAM,oBAAC,CAAoB,IAAC,CAAI,sBAAC,CAAsB,CAAC;YAqKrF,EAAE,CApKC;;;;QAyKP,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,aAAA,CAAc,MAAC,EAAO,CAAA,EAAE,EAAG;YAqK7C,QAAQ,CApKC,aAAC,CAAa,CAAC,CAAC,EAAE,eAAA,CAAgB,CAAC;SAqK7C;QAED,uBApKM,aAAA,GAAuB,EAAA,CAAG;QAqKhC,uBApKM,2BAAA,GAAqC,EAAA,CAAG;QAqK9C,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,IAAA,CAAK,sBAAC,CAAsB,MAAC,EAAO,CAAA,EAAE,EAAG;YAqK3D,uBApKM,OAAA,GAAU,IAAA,CAAK,sBAAC,CAAsB,CAAC,CAAC,CAAC;YAqK/C,uBApKM,OAAA,IAAU,OAAA,CAAQ,YAAC,CAAgB,CAAA,CAAsB;YAqK/D,IAAI,OApKC,IAAU,OAAA,CAAQ,aAAC,EAAc;gBAqKpC,QAAQ,CApKC,OAAC,EAAQ,eAAA,CAAgB,CAAC;gBAqKnC,aAAa,CApKC,IAAC,CAAI,OAAC,CAAO,CAAC;gBAqK5B,IAAI,CApKC,OAAC,CAAO,YAAC,EAAa;oBAqKzB,2BAA2B,CApKC,IAAC,CAAI,OAAC,CAAO,CAAC;iBAqK3C;aACF;SACF;QAED,KAAK,qBApKI,CAAA,GAAI,IAAA,CAAK,cAAC,CAAc,MAAC,GAAQ,CAAA,EAAG,CAAA,IAAK,CAAA,EAAG,CAAA,EAAE,EAAG;YAqKxD,uBApKM,EAAA,GAAK,IAAA,CAAK,cAAC,CAAc,CAAC,CAAC,CAAC;YAqKlC,EAAE,CApKC,sBAAC,CAAsB,WAAC,CAAW,CAAC,OAAC,CAAO,KAAC,IAoKtD;gBACQ,uBApKM,MAAA,GAAS,KAAA,CAAM,MAAC,CAAM;gBAsK5B,uBApKM,OAAA,GAAU,KAAA,CAAM,OAAC,CAAO;gBAqK9B,IAAI,CApKC,QAAC,IAAW,CAAA,IAAE,CAAI,MAAC,CAAM,eAAC,CAAe,QAAC,EAAS,OAAA,CAAQ,EAAE;oBAqKhE,MAAM,CApKC,OAAC,EAAO,CAAE;oBAqKjB,OAAO;iBACR;gBAED,uBApKM,WAAA,GAAc,IAAA,CAAK,iBAAC,CAAiB,KAAC,EAAM,YAAA,CAAa,CAAC;gBAqKhE,IAAI,CApKC,WAAC;oBAAY,OAAA;;;gBAwKlB,IAAI,KApKC,CAAK,oBAAC,EAAqB;oBAqK9B,MAAM,CApKC,OAAC,CAAO,MAAM,WAAA,CAAY,OAAC,EAAQ,WAAA,CAAY,UAAC,CAAU,CAAC,CAAC;oBAqKnE,MAAM,CApKC,SAAC,CAAS,MAAM,SAAA,CAAU,OAAC,EAAQ,WAAA,CAAY,QAAC,CAAQ,CAAC,CAAC;oBAqKjE,cAAc,CApKC,IAAC,CAAI,MAAC,CAAM,CAAC;oBAqK5B,OAAO;iBACR;;;;;;gBAOD,WAAW,CApKC,SAAC,CAAS,OAAC,CAAO,EAAC,IAAK,EAAA,CAAG,uBAAC,GAAyB,IAAA,CAAK,CAAC;gBAsKvE,YAAY,CApKC,MAAC,CAAM,OAAC,EAAQ,WAAA,CAAY,SAAC,CAAS,CAAC;gBAsKpD,uBApKM,KAAA,GAAQ,EAAA,WAAE,EAAY,MAAA,EAAQ,OAAA,EAAQ,CAAC;gBAsK7C,kBAAkB,CApKC,IAAC,CAAI,KAAC,CAAK,CAAC;gBAsK/B,WAAW,CApKC,eAAC,CAAe,OAAC,CAqKzB,OAAO,IApKI,eAAA,CAAgB,eAAC,EAAgB,OAAA,EAAS,EAAA,CAAG,CAAC,IAAC,CAAI,MAAC,CAAM,CAAC,CAAC;gBAsK3E,WAAW,CApKC,aAAC,CAAa,OAAC,CAAO,CAAC,SAAC,EAAU,OAAA,KAoKtD;oBACU,uBApKM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,CAAC;oBAqKrC,IAAI,KApKC,CAAK,MAAC,EAAO;wBAqKhB,qBApKI,MAAA,KAAsB,mBAAA,CAAoB,GAAC,CAAG,OAAC,CAAO,EAAA,CAAG;wBAqK7D,IAAI,CApKC,MAAC,EAAO;4BAqKX,mBAAmB,CApKC,GAAC,CAAG,OAAC,EAAQ,MAAA,GAAS,IAAI,GAAA,EAAW,CAAG,CAAC;yBAqK9D;wBACD,KAAK,CApKC,OAAC,CAAO,IAAC,IAAO,MAAA,CAAO,GAAC,CAAG,IAAC,CAAI,CAAC,CAAC;qBAqKzC;iBACF,CApKC,CAAC;gBAsKH,WAAW,CApKC,cAAC,CAAc,OAAC,CAAO,CAAC,SAAC,EAAU,OAAA,KAoKvD;oBACU,uBApKM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,CAAC;oBAqKrC,qBApKI,MAAA,KAAsB,oBAAA,CAAqB,GAAC,CAAG,OAAC,CAAO,EAAA,CAAG;oBAqK9D,IAAI,CApKC,MAAC,EAAO;wBAqKX,oBAAoB,CApKC,GAAC,CAAG,OAAC,EAAQ,MAAA,GAAS,IAAI,GAAA,EAAW,CAAG,CAAC;qBAqK/D;oBACD,KAAK,CApKC,OAAC,CAAO,IAAC,IAAO,MAAA,CAAO,GAAC,CAAG,IAAC,CAAI,CAAC,CAAC;iBAqKzC,CApKC,CAAC;aAqKJ,CApKC,CAAC;SAqKJ;;;QAID,uBApKM,2BAAA,GAAqC,EAAA,CAAG;QAqK9C,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,aAAA,CAAc,MAAC,EAAO,CAAA,EAAE,EAAG;YAqK7C,uBApKM,OAAA,GAAU,aAAA,CAAc,CAAC,CAAC,CAAC;YAqKjC,IAAI,CApKC,YAAC,CAAY,GAAC,CAAG,OAAC,CAAO,EAAE;gBAqK9B,2BAA2B,CApKC,IAAC,CAAI,OAAC,CAAO,CAAC;aAqK3C;SACF;QAED,uBApKM,qBAAA,GAAwB,IAAI,GAAA,EAAqC,CAAG;QAqK1E,qBApKI,oBAAA,GAA8B,EAAA,CAAG;QAqKrC,kBAAkB,CApKC,OAAC,CAAO,KAAC,IAoKhC;YACM,uBApKM,OAAA,GAAU,KAAA,CAAM,OAAC,CAAO;YAqK9B,IAAI,YApKC,CAAY,GAAC,CAAG,OAAC,CAAO,EAAE;gBAqK7B,oBAAoB,CApKC,OAAC,CAAO,OAAC,CAAO,CAAC;gBAqKtC,IAAI,CApKC,qBAAC,CAqKF,KAAK,CApKC,MAAC,CAAM,WAAC,EAAY,KAAA,CAAM,WAAC,EAAY,qBAAA,CAAsB,CAAC;aAqKzE;SACF,CApKC,CAAC;QAsKH,cAAc,CApKC,OAAC,CAAO,MAAC,IAoK5B;YACM,uBApKM,OAAA,GAAU,MAAA,CAAO,OAAC,CAAO;YAqK/B,uBApKM,eAAA,GAqKF,IAAI,CApKC,mBAAC,CAAmB,OAAC,EAAQ,KAAA,EAAO,MAAA,CAAO,WAAC,EAAY,MAAA,CAAO,WAAC,EAAY,IAAA,CAAK,CAAC;YAqK3F,eAAe,CApKC,OAAC,CAqKb,UAAU,IADpB,EAnK0B,eAAA,CAAgB,qBAAC,EAAsB,OAAA,EAAS,EAAA,CAAG,CAAC,IAAC,CAAI,UAAC,CAAU,CAAC,EAAC,CAAE,CAAC;SAqK9F,CApKC,CAAC;QAsKH,qBAAqB,CApKC,OAAC,CAAO,OAAC,IAAU,OAAA,CAAQ,OAAC,CAAO,MAAC,IAAS,MAAA,CAAO,OAAC,EAAO,CAAE,CAAC,CAAC;;QAuKtF,uBApKM,YAAA,GAAe,mBAAA,CAAoB,IAAC;YAqKtC,qBAAqB,CACjB,IAAI,CApKC,MAAC,EAAO,2BAAA,EAA6B,mBAAA,EAAqBA,UAAA,CAAU;YAqK7E,IApKI,GAAA,EAAoB,CAAG;;QAuK/B,uBApKM,aAAA,GAAgB,qBAAA,CAqKlB,IAAI,CApKC,MAAC,EAAO,2BAAA,EAA6B,oBAAA,EAAsB,UAAA,CAAW,CAAC;QAsKhF,uBApKM,WAAA,GAA2C,EAAA,CAAG;QAqKpD,uBApKM,UAAA,GAA0C,EAAA,CAAG;QAqKnD,kBAAkB,CApKC,OAAC,CAAO,KAAC,IAoKhC;YACM,MApKM,EAAA,OAAE,EAAQ,MAAA,EAAQ,WAAA,EAAY,GAAG,KAAA,CAAM;;;YAuK7C,IAAI,YApKC,CAAY,GAAC,CAAG,OAAC,CAAO,EAAE;gBAqK7B,uBApKM,WAAA,GAAc,IAAA,CAAK,eAAC,CAqKtB,MAAM,CApKC,WAAC,EAAY,WAAA,EAAa,qBAAA,EAAuB,iBAAA,EAAmB,YAAA,EAqK3E,aAAa,CApKC,CAAC;gBAqKnB,MAAM,CApKC,aAAC,CAAa,WAAC,CAAW,CAAC;gBAsKlC,qBApKI,iBAAA,GAAyB,IAAA,CAAK;gBAqKlC,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,oBAAA,CAAqB,MAAC,EAAO,CAAA,EAAE,EAAG;oBAqKpD,uBApKM,MAAA,GAAS,oBAAA,CAAqB,CAAC,CAAC,CAAC;oBAqKvC,IAAI,MApKC,KAAU,OAAA;wBAAS,MAAA;oBAqKxB,IAAI,IApKC,CAAI,MAAC,CAAM,eAAC,CAAe,MAAC,EAAO,OAAA,CAAQ,EAAE;wBAqKhD,iBAAiB,GApKG,MAAA,CAAO;wBAqK3B,MAAM;qBACP;iBACF;gBAED,IAAI,iBApKC,EAAkB;oBAqKrB,uBApKM,aAAA,GAAgB,IAAA,CAAK,gBAAC,CAAgB,GAAC,CAAG,iBAAC,CAAiB,CAAC;oBAqKnE,IAAI,aApKC,IAAgB,aAAA,CAAc,MAAC,EAAO;wBAqKzC,MAAM,CApKC,YAAC,GAAc,mBAAA,CAAoB,aAAC,CAAa,CAAC;qBAqK1D;oBACD,cAAc,CApKC,IAAC,CAAI,MAAC,CAAM,CAAC;iBAqK7B;qBApKM;oBAqKL,WAAW,CApKC,IAAC,CAAI,MAAC,CAAM,CAAC;iBAqK1B;aACF;iBApKM;gBAqKL,WAAW,CApKC,OAAC,EAAQ,WAAA,CAAY,UAAC,CAAU,CAAC;gBAqK7C,MAAM,CApKC,SAAC,CAAS,MAAM,SAAA,CAAU,OAAC,EAAQ,WAAA,CAAY,QAAC,CAAQ,CAAC,CAAC;gBAqKjE,UAAU,CApKC,IAAC,CAAI,MAAC,CAAM,CAAC;aAqKzB;SACF,CApKC,CAAC;QAsKH,UAAU,CApKC,OAAC,CAAO,MAAC,IAoKxB;YACM,uBApKM,iBAAA,GAAoB,iBAAA,CAAkB,GAAC,CAAG,MAAC,CAAM,OAAC,CAAO,CAAC;YAqKhE,IAAI,iBApKC,IAAoB,iBAAA,CAAkB,MAAC,EAAO;gBAqKjD,uBApKM,WAAA,GAAc,mBAAA,CAAoB,iBAAC,CAAiB,CAAC;gBAqK3D,MAAM,CApKC,aAAC,CAAa,WAAC,CAAW,CAAC;aAqKnC;SACF,CApKC,CAAC;;;;QAyKH,cAAc,CApKC,OAAC,CAAO,MAAC,IAoK5B;YACM,IAAI,MApKC,CAAM,YAAC,EAAa;gBAqKvB,MAAM,CApKC,YAAC,CAAY,SAAC,CAAS,MAAM,MAAA,CAAO,OAAC,EAAO,CAAE,CAAC;aAqKvD;iBApKM;gBAqKL,MAAM,CApKC,OAAC,EAAO,CAAE;aAqKlB;SACF,CApKC,CAAC;;;;QAyKH,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,aAAA,CAAc,MAAC,EAAO,CAAA,EAAE,EAAG;YAqK7C,uBApKM,OAAA,GAAU,aAAA,CAAc,CAAC,CAAC,CAAC;YAqKjC,uBApKM,OAAA,IAAU,OAAA,CAAQ,YAAC,CAAgB,CAAA,CAAsB;;;;YAyK/D,IAAI,OApKC,IAAU,OAAA,CAAQ,YAAC;gBAAa,SAAA;YAsKrC,qBApKI,OAAA,GAA6B,EAAA,CAAG;;;;YAyKpC,IAAI,eApKC,CAAe,IAAC,EAAK;gBAqKxB,qBApKI,oBAAA,GAAuB,eAAA,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;gBAqKxD,IAAI,oBApKC,IAAuB,oBAAA,CAAqB,MAAC,EAAO;oBAqKvD,OAAO,CApKC,IAAC,CAAI,GAAC,oBAAG,CAAoB,CAAC;iBAqKvC;gBAED,qBApKI,oBAAA,GAAuB,IAAA,CAAK,MAAC,CAAM,KAAC,CAAK,OAAC,EAAQ,qBAAA,EAAuB,IAAA,CAAK,CAAC;gBAqKnF,KAAK,qBApKI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,oBAAA,CAAqB,MAAC,EAAO,CAAA,EAAE,EAAG;oBAqKpD,qBApKI,cAAA,GAAiB,eAAA,CAAgB,GAAC,CAAG,oBAAC,CAAoB,CAAC,CAAC,CAAC,CAAC;oBAqKlE,IAAI,cApKC,IAAiB,cAAA,CAAe,MAAC,EAAO;wBAqK3C,OAAO,CApKC,IAAC,CAAI,GAAC,cAAG,CAAc,CAAC;qBAqKjC;iBACF;aACF;YACD,IAAI,OApKC,CAAO,MAAC,EAAO;gBAqKlB,6BAA6B,CApKC,IAAC,EAAK,OAAA,EAAS,OAAA,CAAQ,CAAC;aAqKvD;iBApKM;gBAqKL,IAAI,CApKC,gBAAC,CAAgB,OAAC,CAAO,CAAC;aAqKhC;SACF;QAED,WAAW,CApKC,OAAC,CAAO,MAAC,IAoKzB;YACM,IAAI,CApKC,OAAC,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC;YAqK1B,MAAM,CApKC,MAAC,CAAM,MAoKpB;gBACQ,MAAM,CApKC,OAAC,EAAO,CAAE;gBAsKjB,uBApKM,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,OAAC,CAAO,MAAC,CAAM,CAAC;gBAqK3C,IAAI,CApKC,OAAC,CAAO,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;aAqK/B,CApKC,CAAC;YAqKH,MAAM,CApKC,IAAC,EAAI,CAAE;SAqKf,CApKC,CAAC;QAsKH,aAAa,CApKC,OAAC,CAAO,OAAC,IAAU,WAAA,CAAY,OAAC,EAAQ,eAAA,CAAgB,CAAC,CAAC;QAsKxE,OApKO,WAAA,CAAY;KAqKpB;;;;;;IAMH,mBAxKG,CAAA,WAAA,EAAA,OAAA,EAwKH;QACI,qBAxKI,YAAA,GAAe,KAAA,CAAM;QAyKzB,uBAxKM,OAAA,IAAU,OAAA,CAAQ,YAAC,CAAgB,CAAA,CAAsB;QAyK/D,IAAI,OAxKC,IAAU,OAAA,CAAQ,aAAC;YAAc,YAAA,GAAe,IAAA,CAAK;QAyK1D,IAAI,IAxKC,CAAI,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO;YAAE,YAAA,GAAe,IAAA,CAAK;QAyK5D,IAAI,IAxKC,CAAI,uBAAC,CAAuB,GAAC,CAAG,OAAC,CAAO;YAAE,YAAA,GAAe,IAAA,CAAK;QAyKnE,IAAI,IAxKC,CAAI,eAAC,CAAe,GAAC,CAAG,OAAC,CAAO;YAAE,YAAA,GAAe,IAAA,CAAK;QAyK3D,OAxKO,IAAA,CAAK,eAAC,CAAe,WAAC,CAAW,CAAC,mBAAC,CAAmB,OAAC,CAAO,IAAI,YAAA,CAAa;KAyKvF;;;;;IAKH,UA3KG,CAAA,QAAA,EA2KH,EA3KoC,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,QAAC,CAAQ,CAAC,EAAC;;;;;IAgLnE,wBA9KG,CAAA,QAAA,EA8KH,EA9KkD,IAAA,CAAK,aAAC,CAAa,IAAC,CAAI,QAAC,CAAQ,CAAC,EAAC;;;;;;;;;IAElF,mBAAA,CAsLG,OAAe,EAAE,gBAAyB,EAAE,WAAoB,EAAE,WAAoB,EACtF,YAAkB,EAvLxB;QAwLI,qBArLI,OAAA,GAAuC,EAAA,CAAG;QAsL9C,IAAI,gBArLC,EAAiB;YAsLpB,uBArLM,qBAAA,GAAwB,IAAA,CAAK,uBAAC,CAAuB,GAAC,CAAG,OAAC,CAAO,CAAC;YAsLxE,IAAI,qBArLC,EAAsB;gBAsLzB,OAAO,GArLG,qBAAA,CAAsB;aAsLjC;SACF;aArLM;YAsLL,uBArLM,cAAA,GAAiB,IAAA,CAAK,gBAAC,CAAgB,GAAC,CAAG,OAAC,CAAO,CAAC;YAsL1D,IAAI,cArLC,EAAe;gBAsLlB,uBArLM,kBAAA,GAAqB,CAAA,YAAE,IAAe,YAAA,IAAgB,UAAA,CAAW;gBAsLvE,cAAc,CArLC,OAAC,CAAO,MAAC,IAqLhC;oBACU,IAAI,MArLC,CAAM,MAAC;wBAAO,OAAA;oBAsLnB,IAAI,CArLC,kBAAC,IAAqB,MAAA,CAAO,WAAC,IAAc,WAAA;wBAAa,OAAA;oBAsL9D,OAAO,CArLC,IAAC,CAAI,MAAC,CAAM,CAAC;iBAsLtB,CArLC,CAAC;aAsLJ;SACF;QACD,IAAI,WArLC,IAAc,WAAA,EAAa;YAsL9B,OAAO,GArLG,OAAA,CAAQ,MAAC,CAAM,MAAC,IAqLhC;gBACQ,IAAI,WArLC,IAAc,WAAA,IAAe,MAAA,CAAO,WAAC;oBAAY,OAAO,KAAA,CAAM;gBAsLnE,IAAI,WArLC,IAAc,WAAA,IAAe,MAAA,CAAO,WAAC;oBAAY,OAAO,KAAA,CAAM;gBAsLnE,OArLO,IAAA,CAAK;aAsLb,CArLC,CAAC;SAsLJ;QACD,OArLO,OAAA,CAAQ;KAsLhB;;;;;;;IAnLA,qBAAA,CA2LG,WAAmB,EAAE,WAA2C,EAChE,qBAA4D,EA5LlE;;;QA+LI,WAAW,CA1LC,WAAC,CAAW,OAAC,EAAQ,WAAA,CAAY,UAAC,CAAU,CAAC;QA4LzD,uBA1LM,WAAA,GAAc,WAAA,CAAY,WAAC,CAAW;QA2L5C,uBA1LM,WAAA,GAAc,WAAA,CAAY,OAAC,CAAO;;;QA8LxC,uBA1LM,iBAAA,GA2LF,WAAW,CA1LC,mBAAC,GAAqB,SAAA,GAAY,WAAA,CAAY;QA2L9D,uBA1LM,iBAAA,GA2LF,WAAW,CA1LC,mBAAC,GAAqB,SAAA,GAAY,WAAA,CAAY;QA4L9D,WAAW,CA1LC,SAAC,CAAS,GAAC,CAAG,mBAAC,IA0L/B;YACM,uBA1LM,OAAA,GAAU,mBAAA,CAAoB,OAAC,CAAO;YA2L5C,uBA1LM,gBAAA,GAAmB,OAAA,KAAY,WAAA,CAAY;YA2LjD,uBA1LM,OAAA,GAAU,eAAA,CAAgB,qBAAC,EAAsB,OAAA,EAAS,EAAA,CAAG,CAAC;YA2LpE,uBA1LM,eAAA,GAAkB,IAAA,CAAK,mBAAC,CA2L1B,OAAO,EA1LE,gBAAA,EAAkB,iBAAA,EAAmB,iBAAA,EAAmB,WAAA,CAAY,OAAC,CAAO,CAAC;YA2L1F,eAAe,CA1LC,OAAC,CAAO,MAAC,IA0L/B;gBACQ,uBA1LM,UAAA,IAAa,MAAA,CAAO,aAAC,EAAkB,CAAA,CAAI;gBA2LjD,IAAI,UA1LC,CAAU,aAAC,EAAc;oBA2L5B,UAAU,CA1LC,aAAC,EAAa,CAAE;iBA2L5B;gBACD,OAAO,CA1LC,IAAC,CAAI,MAAC,CAAM,CAAC;aA2LtB,CA1LC,CAAC;SA2LJ,CA1LC,CAAC;KA2LJ;;;;;;;;;;IAxLA,eAAA,CAmMG,WAAmB,EAAE,WAA2C,EAChE,qBAA4D,EAC5D,iBAA8C,EAAE,YAAkC,EAClF,aAAmC,EAtMzC;QAuMI,uBAlMM,WAAA,GAAc,WAAA,CAAY,WAAC,CAAW;QAmM5C,uBAlMM,WAAA,GAAc,WAAA,CAAY,OAAC,CAAO;;;QAsMxC,uBAlMM,iBAAA,GAAiD,EAAA,CAAG;QAmM1D,uBAlMM,mBAAA,GAAsB,IAAI,GAAA,EAAQ,CAAG;QAmM3C,uBAlMM,cAAA,GAAiB,IAAI,GAAA,EAAQ,CAAG;QAmMtC,uBAlMM,aAAA,GAAgB,WAAA,CAAY,SAAC,CAAS,GAAC,CAAG,mBAAC,IAkMrD;YACM,uBAlMM,OAAA,GAAU,mBAAA,CAAoB,OAAC,CAAO;;YAqM5C,uBAlMM,OAAA,GAAU,OAAA,CAAQ,YAAC,CAAY,CAAC;YAmMtC,IAAI,OAlMC,IAAU,OAAA,CAAQ,oBAAC;gBAAqB,OAAO,IAAI,mBAAA,EAAoB,CAAE;YAoM9E,uBAlMM,gBAAA,GAAmB,OAAA,KAAY,WAAA,CAAY;YAmMjD,qBAlMI,eAAA,GAAqC,kBAAA,CAAmB;YAmM5D,IAAI,CAlMC,mBAAC,CAAmB,GAAC,CAAG,OAAC,CAAO,EAAE;gBAmMrC,mBAAmB,CAlMC,GAAC,CAAG,OAAC,CAAO,CAAC;gBAmMjC,uBAlMM,gBAAA,GAAmB,qBAAA,CAAsB,GAAC,CAAG,OAAC,CAAO,CAAC;gBAmM5D,IAAI,gBAlMC,EAAiB;oBAmMpB,eAAe,GAlMG,gBAAA,CAAiB,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,aAAC,EAAa,CAAE,CAAC;iBAmMhE;aACF;YACD,uBAlMM,SAAA,GAAY,YAAA,CAAa,GAAC,CAAG,OAAC,CAAO,CAAC;YAmM5C,uBAlMM,UAAA,GAAa,aAAA,CAAc,GAAC,CAAG,OAAC,CAAO,CAAC;YAmM9C,uBAlMM,SAAA,GAAY,kBAAA,CAmMd,IAAI,CAlMC,MAAC,EAAO,IAAA,CAAK,WAAC,EAAY,OAAA,EAAS,mBAAA,CAAoB,SAAC,EAAU,SAAA,EAmMvE,UAAU,CAlMC,CAAC;YAmMhB,uBAlMM,MAAA,GAAS,IAAA,CAAK,YAAC,CAAY,mBAAC,EAAoB,SAAA,EAAW,eAAA,CAAgB,CAAC;;;YAsMlF,IAAI,mBAlMC,CAAmB,WAAC,IAAc,iBAAA,EAAmB;gBAmMxD,cAAc,CAlMC,GAAC,CAAG,OAAC,CAAO,CAAC;aAmM7B;YAED,IAAI,gBAlMC,EAAiB;gBAmMpB,uBAlMM,aAAA,GAAgB,IAAI,yBAAA,CAA0B,WAAC,EAAY,WAAA,EAAa,OAAA,CAAQ,CAAC;gBAmMvF,aAAa,CAlMC,aAAC,CAAa,MAAC,CAAM,CAAC;gBAmMpC,iBAAiB,CAlMC,IAAC,CAAI,aAAC,CAAa,CAAC;aAmMvC;YAED,OAlMO,MAAA,CAAO;SAmMf,CAlMC,CAAC;QAoMH,iBAAiB,CAlMC,OAAC,CAAO,MAAC,IAkM/B;YACM,eAAe,CAlMC,IAAC,CAAI,uBAAC,EAAwB,MAAA,CAAO,OAAC,EAAQ,EAAA,CAAG,CAAC,IAAC,CAAI,MAAC,CAAM,CAAC;YAmM/E,MAAM,CAlMC,MAAC,CAAM,MAAM,kBAAA,CAAmB,IAAC,CAAI,uBAAC,EAAwB,MAAA,CAAO,OAAC,EAAQ,MAAA,CAAO,CAAC,CAAC;SAmM/F,CAlMC,CAAC;QAoMH,mBAAmB,CAlMC,OAAC,CAAO,OAAC,IAAU,QAAA,CAAS,OAAC,EAAQ,sBAAA,CAAuB,CAAC,CAAC;QAmMlF,uBAlMM,MAAA,GAAS,mBAAA,CAAoB,aAAC,CAAa,CAAC;QAmMlD,MAAM,CAlMC,SAAC,CAAS,MAkMrB;YACM,mBAAmB,CAlMC,OAAC,CAAO,OAAC,IAAU,WAAA,CAAY,OAAC,EAAQ,sBAAA,CAAuB,CAAC,CAAC;YAmMrF,SAAS,CAlMC,WAAC,EAAY,WAAA,CAAY,QAAC,CAAQ,CAAC;SAmM9C,CAlMC,CAAC;;;QAsMH,cAAc,CAlMC,OAAC,CAmMZ,OAAO,IADf,EAjMqB,eAAA,CAAgB,iBAAC,EAAkB,OAAA,EAAS,EAAA,CAAG,CAAC,IAAC,CAAI,MAAC,CAAM,CAAC,EAAC,CAAE,CAAC;QAoMlF,OAlMO,MAAA,CAAO;KAmMf;;;;;;;IAhMA,YAAA,CAwMG,WAAyC,EAAE,SAAuB,EAClE,eAAkC,EAzMxC;QA0MI,IAAI,SAvMC,CAAS,MAAC,GAAQ,CAAA,EAAG;YAwMxB,OAvMO,IAAA,CAAK,MAAC,CAAM,OAAC,CAwMhB,WAAW,CAvMC,OAAC,EAAQ,SAAA,EAAW,WAAA,CAAY,QAAC,EAAS,WAAA,CAAY,KAAC,EAwMnE,WAAW,CAvMC,MAAC,EAAO,eAAA,CAAgB,CAAC;SAwM1C;;;QAID,OAvMO,IAAI,mBAAA,EAAoB,CAAE;KAwMlC;CACF;AAED,AAqCA,AAAA,MAAA,yBAAA,CAAA;;;;;;IAYA,WAAA,CA9OqB,WAAa,EAAe,WAAa,EAAe,OAAS,EA8OtF;QA9OqB,IAArB,CAAA,WAAqB,GAAA,WAAA,CAAa;QAAe,IAAjD,CAAA,WAAiD,GAAA,WAAA,CAAa;QAAe,IAA7E,CAAA,OAA6E,GAAA,OAAA,CAAS;QAT5E,IAAV,CAAA,OAAU,GAA2B,IAAI,mBAAA,EAAoB,CAAE;QACrD,IAAV,CAAA,mBAAU,GAAsB,KAAA,CAAM;QAE5B,IAAV,CAAA,gBAAU,GAAoD,EAAA,CAAG;QACvD,IAAV,CAAA,UAAU,GAAa,KAAA,CAAM;QAGpB,IAAT,CAAA,gBAAS,GAA4B,KAAA,CAAM;KAE+C;;;;IAoP1F,IAlPG,MAAA,GAkPH,EAlPiB,OAAO,IAAA,CAAK,mBAAC,IAAsB,KAAA,CAAM,EAAC;;;;IAsP3D,IApPG,SAAA,GAoPH,EApPoB,OAAO,IAAA,CAAK,UAAC,CAAU,EAAC;;;;;IAyP5C,aAvPG,CAAA,MAAA,EAuPH;QACI,IAAI,IAvPC,CAAI,mBAAC;YAAoB,OAAA;QAyP9B,IAAI,CAvPC,OAAC,GAAS,MAAA,CAAO;QAwPtB,MAAM,CAvPC,IAAC,CAAI,IAAC,CAAI,gBAAC,CAAgB,CAAC,OAAC,CAAO,KAAC,IAuPhD;YACM,IAAI,CAvPC,gBAAC,CAAgB,KAAC,CAAK,CAAC,OAAC,CAwP1B,QAAQ,IAvPI,cAAA,CAAe,MAAC,EAAO,KAAA,EAAO,SAAA,EAAW,QAAA,CAAS,CAAC,CAAC;SAwPrE,CAvPC,CAAC;QAwPH,IAAI,CAvPC,gBAAC,GAAkB,EAAA,CAAG;QAwP3B,IAAI,CAvPC,mBAAC,GAAqB,IAAA,CAAK;KAwPjC;;;;IAIH,aAzPG,GAyPH,EAzPoB,OAAO,IAAA,CAAK,OAAC,CAAO,EAAC;;;;;;IAEtC,WAAA,CAAA,IAAA,EAAA,QAAA,EAAH;QA8PI,eAAe,CA7PC,IAAC,CAAI,gBAAC,EAAiB,IAAA,EAAM,EAAA,CAAG,CAAC,IAAC,CAAI,QAAC,CAAQ,CAAC;KA8PjE;;;;;IAKH,MAhQG,CAAA,EAAA,EAgQH;QACI,IAAI,IAhQC,CAAI,MAAC,EAAO;YAiQf,IAAI,CAhQC,WAAC,CAAW,MAAC,EAAO,EAAA,CAAG,CAAC;SAiQ9B;QACD,IAAI,CAhQC,OAAC,CAAO,MAAC,CAAM,EAAC,CAAE,CAAC;KAiQzB;;;;;IAKH,OAnQG,CAAA,EAAA,EAmQH;QACI,IAAI,IAnQC,CAAI,MAAC,EAAO;YAoQf,IAAI,CAnQC,WAAC,CAAW,OAAC,EAAQ,EAAA,CAAG,CAAC;SAoQ/B;QACD,IAAI,CAnQC,OAAC,CAAO,OAAC,CAAO,EAAC,CAAE,CAAC;KAoQ1B;;;;;IAKH,SAtQG,CAAA,EAAA,EAsQH;QACI,IAAI,IAtQC,CAAI,MAAC,EAAO;YAuQf,IAAI,CAtQC,WAAC,CAAW,SAAC,EAAU,EAAA,CAAG,CAAC;SAuQjC;QACD,IAAI,CAtQC,OAAC,CAAO,SAAC,CAAS,EAAC,CAAE,CAAC;KAuQ5B;;;;IAIH,IAxQG,GAwQH,EAxQiB,IAAA,CAAK,OAAC,CAAO,IAAC,EAAI,CAAE,EAAC;;;;IA4QtC,UA1QG,GA0QH,EA1Q0B,OAAO,IAAA,CAAK,MAAC,GAAQ,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,UAAC,EAAU,CAAE,EAAC;;;;IA8QlF,IA5QG,GA4QH,EA5QiB,CAAA,IAAE,CAAI,MAAC,IAAS,IAAA,CAAK,OAAC,CAAO,IAAC,EAAI,CAAE,EAAC;;;;IAgRtD,KA9QG,GA8QH,EA9QkB,CAAA,IAAE,CAAI,MAAC,IAAS,IAAA,CAAK,OAAC,CAAO,KAAC,EAAK,CAAE,EAAC;;;;IAkRxD,OAhRG,GAgRH,EAhRoB,CAAA,IAAE,CAAI,MAAC,IAAS,IAAA,CAAK,OAAC,CAAO,OAAC,EAAO,CAAE,EAAC;;;;IAoR5D,MAlRG,GAkRH,EAlRmB,IAAA,CAAK,OAAC,CAAO,MAAC,EAAM,CAAE,EAAC;;;;IAsR1C,OApRG,GAoRH;QACI,IAAI,CApRC,UAAC,GAAY,IAAA,CAAK;QAqRvB,IAAI,CApRC,OAAC,CAAO,OAAC,EAAO,CAAE;KAqRxB;;;;IAIH,KAtRG,GAsRH,EAtRkB,CAAA,IAAE,CAAI,MAAC,IAAS,IAAA,CAAK,OAAC,CAAO,KAAC,EAAK,CAAE,EAAC;;;;;IA2RxD,WAzRG,CAAA,CAAA,EAyRH;QACI,IAAI,CAzRC,IAAC,CAAI,MAAC,EAAO;YA0RhB,IAAI,CAzRC,OAAC,CAAO,WAAC,CAAW,CAAC,CAAC,CAAC;SA0R7B;KACF;;;;IAIH,WA3RG,GA2RH,EA3R0B,OAAO,IAAA,CAAK,MAAC,GAAQ,CAAA,GAAI,IAAA,CAAK,OAAC,CAAO,WAAC,EAAW,CAAE,EAAC;;;;IA+R/E,IA7RG,SAAA,GA6RH,EA7R4B,OAAO,IAAA,CAAK,OAAC,CAAO,SAAC,CAAS,EAAC;CA8R1D;AAED,AAqBA;;;;;;AAMA,SAAA,kBAAA,CAxTC,GAAA,EAAA,GAAA,EAAA,KAAA,EAwTD;IACE,qBAxTI,aAA2B,CAAS;IAyTxC,IAAI,GAxTC,YAAc,GAAA,EAAK;QAyTtB,aAAa,GAxTG,GAAA,CAAI,GAAC,CAAG,GAAC,CAAG,CAAC;QAyT7B,IAAI,aAxTC,EAAc;YAyTjB,IAAI,aAxTC,CAAa,MAAC,EAAO;gBAyTxB,uBAxTM,KAAA,GAAQ,aAAA,CAAc,OAAC,CAAO,KAAC,CAAK,CAAC;gBAyT3C,aAAa,CAxTC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;aAyThC;YACD,IAAI,aAxTC,CAAa,MAAC,IAAS,CAAA,EAAG;gBAyT7B,GAAG,CAxTC,MAAC,CAAM,GAAC,CAAG,CAAC;aAyTjB;SACF;KACF;SAxTM;QAyTL,aAAa,GAxTG,GAAA,CAAI,GAAC,CAAG,CAAC;QAyTzB,IAAI,aAxTC,EAAc;YAyTjB,IAAI,aAxTC,CAAa,MAAC,EAAO;gBAyTxB,uBAxTM,KAAA,GAAQ,aAAA,CAAc,OAAC,CAAO,KAAC,CAAK,CAAC;gBAyT3C,aAAa,CAxTC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;aAyThC;YACD,IAAI,aAxTC,CAAa,MAAC,IAAS,CAAA,EAAG;gBAyT7B,OAxTO,GAAA,CAAI,GAAC,CAAG,CAAC;aAyTjB;SACF;KACF;IACD,OAxTO,aAAA,CAAc;CAyTtB;;;;;AAKD,SAAA,qBAAA,CA3TC,KAAA,EA2TD;IACE,QAAQ,OA3TO,KAAA;QA4Tb,KA3TK,SAAA;YA4TH,OA3TO,KAAA,GAAQ,GAAA,GAAM,GAAA,CAAI;QA4T3B;YACE,OA3TO,KAAA,IAAS,IAAA,GAAO,KAAA,CAAM,QAAC,EAAQ,GAAI,IAAA,CAAK;KA4TlD;CACF;;;;;AAKD,SAAA,aAAA,CA9TC,IAAA,EA8TD;IACE,OA9TO,IAAA,IAAQ,IAAA,CAAK,UAAC,CAAU,KAAK,CAAA,CAAE;CA+TvC;;;;;AAKD,SAAA,mBAAA,CAjUC,SAAA,EAiUD;IACE,OAjUO,SAAA,IAAa,OAAA,IAAW,SAAA,IAAa,MAAA,CAAO;CAkUpD;;;;;;AAMD,SAAA,YAAA,CArUC,OAAA,EAAA,KAAA,EAqUD;IACE,uBArUM,QAAA,GAAW,OAAA,CAAQ,KAAC,CAAK,OAAC,CAAO;IAsUvC,OAAO,CArUC,KAAC,CAAK,OAAC,GAAS,KAAA,IAAS,IAAA,GAAO,KAAA,GAAQ,MAAA,CAAO;IAsUvD,OArUO,QAAA,CAAS;CAsUjB;;;;;;;;AAQD,SAAA,qBAAA,CACI,MAAuB,EAAE,QAAe,EAAE,eAAsC,EAChF,YAAoB,EAFxB;IAGE,uBA3UM,SAAA,GAAY,QAAA,CAAS,GAAC,CAAG,OAAC,IAAU,YAAA,CAAa,OAAC,CAAO,CAAC,CAAC;IA4UjE,uBA3UM,SAAA,GAAY,IAAI,GAAA,EAAoB,CAAG;IA6U7C,eAAe,CA3UC,OAAC,CAAO,CAAC,KAAkB,EAAE,OAAS,KA2UxD;QACI,uBA3UM,MAAA,GAAqB,EAAA,CAAG;QA4U9B,KAAK,CA3UC,OAAC,CAAO,IAAC,IA2UnB;YACM,uBA3UM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,GAAG,MAAA,CAAO,YAAC,CAAY,OAAC,EAAQ,IAAA,EAAM,YAAA,CAAa,CAAC;;;YA+U9E,IAAI,CA3UC,KAAC,IAAQ,KAAA,CAAM,MAAC,IAAS,CAAA,EAAG;gBA4U/B,OAAO,CA3UC,YAAC,CAAY,GAAG,0BAAA,CAA2B;aA4UpD;SACF,CA3UC,CAAC;QA4UH,SAAS,CA3UC,GAAC,CAAG,OAAC,EAAQ,MAAA,CAAO,CAAC;KA4UhC,CA3UC,CAAC;IA6UH,QAAQ,CA3UC,OAAC,CAAO,CAAC,OAAC,EAAQ,CAAA,KAAM,YAAA,CAAa,OAAC,EAAQ,SAAA,CAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IA4UtE,OA3UO,SAAA,CAAU;CA4UlB;;;;;AAKD,SAAA,oBAAA,CA/TC,KAAA,EA+TD;IACE,uBA/TM,OAAA,GAAU,IAAI,GAAA,CAAI,KAAC,CAAK,CAAC;IAgU/B,uBA/TM,kBAAA,GAAqB,IAAI,GAAA,EAAI,CAAE;IAgUrC,qBA/TI,MAAuB,CAAQ;IAgUnC,MAAM,GA/TG,IAAA,IA+TX;QACI,IAAI,CA/TC,IAAC;YAAK,OAAO,IAAA,CAAK;QAgUvB,IAAI,OA/TC,CAAO,GAAC,CAAG,IAAC,CAAI,UAAC,CAAU;YAAE,OAAO,KAAA,CAAM;QAgU/C,IAAI,kBA/TC,CAAkB,GAAC,CAAG,IAAC,CAAI,UAAC,CAAU;YAAE,OAAO,IAAA,CAAK;QAgUzD,IAAI,MA/TC,CAAM,IAAC,CAAI,UAAC,CAAU,EAAE;YAgU3B,kBAAkB,CA/TC,GAAC,CAAG,IAAC,CAAI,CAAC;YAgU7B,OA/TO,IAAA,CAAK;SAgUb;QACD,OA/TO,KAAA,CAAM;KAgUd,CA/TC;IAgUF,OA/TO,MAAA,CAAO;CAgUf;AAED,MA/TM,iBAAA,GAAoB,WAAA,CAAY;;;;;;AAqUtC,SAAA,aAAA,CApUC,OAAA,EAAA,SAAA,EAoUD;IACE,IAAI,OApUC,CAAO,SAAC,EAAU;QAqUrB,OApUO,OAAA,CAAQ,SAAC,CAAS,QAAC,CAAQ,SAAC,CAAS,CAAC;KAqU9C;SApUM;QAqUL,uBApUM,OAAA,GAAU,OAAA,CAAQ,iBAAC,CAAiB,CAAC;QAqU3C,OApUO,OAAA,IAAW,OAAA,CAAQ,SAAC,CAAS,CAAC;KAqUtC;CACF;;;;;;AAMD,SAAA,QAAA,CAxUC,OAAA,EAAA,SAAA,EAwUD;IACE,IAAI,OAxUC,CAAO,SAAC,EAAU;QAyUrB,OAAO,CAxUC,SAAC,CAAS,GAAC,CAAG,SAAC,CAAS,CAAC;KAyUlC;SAxUM;QAyUL,qBAxUI,OAAA,GAA0C,OAAA,CAAQ,iBAAC,CAAiB,CAAC;QAyUzE,IAAI,CAxUC,OAAC,EAAQ;YAyUZ,OAAO,GAxUG,OAAA,CAAQ,iBAAC,CAAiB,GAAG,EAAA,CAAG;SAyU3C;QACD,OAAO,CAxUC,SAAC,CAAS,GAAG,IAAA,CAAK;KAyU3B;CACF;;;;;;AAMD,SAAA,WAAA,CA5UC,OAAA,EAAA,SAAA,EA4UD;IACE,IAAI,OA5UC,CAAO,SAAC,EAAU;QA6UrB,OAAO,CA5UC,SAAC,CAAS,MAAC,CAAM,SAAC,CAAS,CAAC;KA6UrC;SA5UM;QA6UL,qBA5UI,OAAA,GAA0C,OAAA,CAAQ,iBAAC,CAAiB,CAAC;QA6UzE,IAAI,OA5UC,EAAQ;YA6UX,OA5UO,OAAA,CAAQ,SAAC,CAAS,CAAC;SA6U3B;KACF;CACF;;;;AAID,SAAA,WAAA,GAAA;IACE,IAAI,OA9UO,QAAA,IAAY,WAAA,EAAa;QA+UlC,OA9UO,QAAA,CAAS,IAAC,CAAI;KA+UtB;IACD,OA9UO,IAAA,CAAK;CA+Ub;;;;;;;AAOD,SAAA,6BAAA,CACI,MAAiC,EAAE,OAAY,EAAE,OAA0B,EAD/E;IAEE,mBAAmB,CAnVC,OAAC,CAAO,CAAC,MAAC,CAAM,MAAM,MAAA,CAAO,gBAAC,CAAgB,OAAC,CAAO,CAAC,CAAC;CAoV7E;;AD5rDD;;;;;;;AAUA,AACA,AAIA,AACA,AACA,AACA,AAAA,MAAA,eAAA,CAAA;;;;;IASA,WAAA,CAAG,MAAmB,EAAiB,UAAY,EAAnD;QALU,IAAV,CAAA,aAAU,GAAmD,EAAA,CAAG;QAGvD,IAAT,CAAA,iBAAS,GAAoB,CAAA,OAAU,EAAK,OAAS,KAArD,GAA6D,CAAG;QAG5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC3E,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAEvE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;YACpC,CAAC,OAAY,EAAE,OAAY,KAAnC,EAA0C,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAA;KAClF;;;;;;;;;IASH,eAPG,CAQG,WAAmB,EAAE,WAAmB,EAAE,WAAgB,EAAE,IAAY,EACxE,QAAkC,EAFxC;QAGI,uBAPM,QAAA,GAAW,WAAA,GAAc,GAAA,GAAM,IAAA,CAAK;QAQ1C,qBAPI,OAAA,GAAU,IAAA,CAAK,aAAC,CAAa,QAAC,CAAQ,CAAC;QAQ3C,IAAI,CAPC,OAAC,EAAQ;YAQZ,uBAPM,MAAA,GAAgB,EAAA,CAAG;YAQzB,uBAPM,GAAA,IAAM,iBAAA,mBAAkB,QAAY,GAAmB,MAAA,CAAW,CAAA,CAAW;YAQnF,IAAI,MAPC,CAAM,MAAC,EAAO;gBAQjB,MAPM,IAAI,KAAA,CAQN,CADZ,uBAAA,EACsC,IAPC,CAMvC,uDAAA,EAN2C,MAA0D,CAAM,IAAC,CAAI,OAAC,CAAO,CAMxH,CANyH,CAAE,CAAC;aAQrH;YACD,OAAO,GAPG,YAAA,CAAa,IAAC,EAAK,GAAA,CAAI,CAAC;YAQlC,IAAI,CAPC,aAAC,CAAa,QAAC,CAAQ,GAAG,OAAA,CAAQ;SAQxC;QACD,IAAI,CAPC,iBAAC,CAAiB,eAAC,CAAe,WAAC,EAAY,IAAA,EAAM,OAAA,CAAQ,CAAC;KAQpE;;;;;;IAMH,QAXG,CAAA,WAAA,EAAA,WAAA,EAWH;QACI,IAAI,CAXC,iBAAC,CAAiB,QAAC,CAAQ,WAAC,EAAY,WAAA,CAAY,CAAC;KAY3D;;;;;;IAMH,OAfG,CAAA,WAAA,EAAA,OAAA,EAeH;QACI,IAAI,CAfC,iBAAC,CAAiB,OAAC,CAAO,WAAC,EAAY,OAAA,CAAQ,CAAC;KAgBtD;;;;;;;;IAQH,QArBG,CAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAqBH;QACI,IAAI,CArBC,iBAAC,CAAiB,UAAC,CAAU,WAAC,EAAY,OAAA,EAAS,MAAA,EAAQ,YAAA,CAAa,CAAC;KAsB/E;;;;;;;IAOH,QA1BG,CAAA,WAAA,EAAA,OAAA,EAAA,OAAA,EA0BH;QACI,IAAI,CA1BC,iBAAC,CAAiB,UAAC,CAAU,WAAC,EAAY,OAAA,EAAS,OAAA,CAAQ,CAAC;KA2BlE;;;;;;;;IAQH,WAhCG,CAAA,WAAA,EAAA,OAAA,EAAA,QAAA,EAAA,KAAA,EAgCH;;QAEI,IAAI,QAhCC,CAAQ,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,EAAK;YAiC7B,MAhCM,CAAA,EAAE,EAAG,MAAA,CAAO,GAAG,oBAAA,CAAqB,QAAC,CAAQ,CAAC;YAiCpD,uBAhCM,IAAA,IAAO,KAAa,CAAA,CAAE;YAiC5B,IAAI,CAhCC,eAAC,CAAe,OAAC,CAAO,EAAC,EAAG,OAAA,EAAS,MAAA,EAAQ,IAAA,CAAK,CAAC;YAiCxD,OAhCO,KAAA,CAAM;SAiCd;QACD,OAhCO,IAAA,CAAK,iBAAC,CAAiB,OAAC,CAAO,WAAC,EAAY,OAAA,EAAS,QAAA,EAAU,KAAA,CAAM,CAAC;KAiC9E;;;;;;;;;IASH,MAvCG,CAwCG,WAAmB,EAAE,OAAY,EAAE,SAAiB,EAAE,UAAkB,EACxE,QAA6B,EAFnC;;QAII,IAAI,SAvCC,CAAS,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,EAAK;YAwC9B,MAvCM,CAAA,EAAE,EAAG,MAAA,CAAO,GAAG,oBAAA,CAAqB,SAAC,CAAS,CAAC;YAwCrD,OAvCO,IAAA,CAAK,eAAC,CAAe,MAAC,CAAM,EAAC,EAAG,OAAA,EAAS,MAAA,EAAQ,QAAA,CAAS,CAAC;SAwCnE;QACD,OAvCO,IAAA,CAAK,iBAAC,CAAiB,MAAC,CAAM,WAAC,EAAY,OAAA,EAAS,SAAA,EAAW,UAAA,EAAY,QAAA,CAAS,CAAC;KAwC7F;;;;;IAKH,KA1CG,CAAA,WA0CH,GA1CG,CAAA,CAAA,EA0CH,EA1C0C,IAAA,CAAK,iBAAC,CAAiB,KAAC,CAAK,WAAC,CAAW,CAAC,EAAC;;;;IA8CrF,IA5CG,OAAA,GA4CH;QACI,OA5CO,EAAA,IAAE,CAAI,iBAAC,CAAiB,OAA2B;aA6CrD,MA5CC,mBAAM,IAAC,CAAI,eAAC,CAAe,OAA2B,EAAE,CAAC;KA6ChE;;;;IAIH,iBA9CG,GA8CH,EA9CsC,OAAO,IAAA,CAAK,iBAAC,CAAiB,iBAAC,EAAiB,CAAE,EAAC;CA+CxF,AAED,AASC;;AD5JD;;;;;;;AAUA,AAGA,AAAA,MAAA,mBAAA,CAAA;;;;;;;IAsBA,WAAA,CAHa,OAAS,EAAY,SAA4C,EACjE,OAAyC,EACxC,eACd,GADuD,EAAA,EACvD;QAHa,IAAb,CAAA,OAAa,GAAA,OAAA,CAAS;QAAY,IAAlC,CAAA,SAAkC,GAAA,SAAA,CAA4C;QACjE,IAAb,CAAA,OAAa,GAAA,OAAA,CAAyC;QACxC,IAAd,CAAA,eAAc,GAAA,eAAA,CAAyC;QApB7C,IAAV,CAAA,UAAU,GAAyB,EAAA,CAAG;QAC5B,IAAV,CAAA,WAAU,GAA0B,EAAA,CAAG;QAC7B,IAAV,CAAA,aAAU,GAA4B,EAAA,CAAG;QAI/B,IAAV,CAAA,YAAU,GAAe,KAAA,CAAM;QACrB,IAAV,CAAA,SAAU,GAAY,KAAA,CAAM;QAClB,IAAV,CAAA,QAAU,GAAW,KAAA,CAAM;QACjB,IAAV,CAAA,UAAU,GAAa,KAAA,CAAM;QAEpB,IAAT,CAAA,IAAS,GAAO,CAAA,CAAE;QAET,IAAT,CAAA,YAAS,GAAqC,IAAA,CAAK;QAE1C,IAAT,CAAA,eAAS,GAA0D,EAAA,CAAG;QAWlE,IAAI,CAAC,SAAS,GAAW,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,GAAW,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,eAAe,CAAC,OAAO,CAAC,MAAM,IAAlC;YACM,IAAI,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/E,CAAC,CAAC;KACJ;;;;IAHA,SAAA,GAAH;QAQI,IAAI,CAPC,IAAC,CAAI,SAAC,EAAU;YAQnB,IAAI,CAPC,SAAC,GAAW,IAAA,CAAK;YAQtB,IAAI,CAPC,UAAC,CAAU,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC;YAQpC,IAAI,CAPC,UAAC,GAAY,EAAA,CAAG;SAQtB;KACF;;;;IAIH,IATG,GASH;QACI,IAAI,CATC,YAAC,EAAY,CAAE;QAUpB,IAAI,CATC,yBAAC,EAAyB,CAAE;KAUlC;;;;IAPA,YAAA,GAAH;QAYI,IAAI,IAXC,CAAI,YAAC;YAAa,OAAA;QAYvB,IAAI,CAXC,YAAC,GAAc,IAAA,CAAK;QAazB,uBAXM,SAAA,GAAY,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,MAAC,IAAS,UAAA,CAAW,MAAC,EAAO,KAAA,CAAM,CAAC,CAAC;QAY1E,uBAXM,kBAAA,GAAqB,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,cAAC,CAAc,CAAC;QAY5D,IAAI,kBAXC,CAAkB,MAAC,EAAO;YAY7B,qBAXI,gBAAA,GAAmB,SAAA,CAAU,CAAC,CAAC,CAAC;YAYpC,qBAXI,iBAAA,GAA8B,EAAA,CAAG;YAYrC,kBAAkB,CAXC,OAAC,CAAO,IAAC,IAWlC;gBACQ,IAAI,CAXC,gBAAC,CAAgB,cAAC,CAAc,IAAC,CAAI,EAAE;oBAY1C,iBAAiB,CAXC,IAAC,CAAI,IAAC,CAAI,CAAC;iBAY9B;gBACD,gBAAgB,CAXC,IAAC,CAAI,GAAG,IAAA,CAAK,cAAC,CAAc,IAAC,CAAI,CAAC;aAYpD,CAXC,CAAC;YAaH,IAAI,iBAXC,CAAiB,MAAC,EAAO;gBAY5B,uBAXM,IAAA,GAAO,IAAA,CAAK;;gBAalB,KAAK,qBAXI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,SAAA,CAAU,MAAC,EAAO,CAAA,EAAE,EAAG;oBAYzC,qBAXI,EAAA,GAAK,SAAA,CAAU,CAAC,CAAC,CAAC;oBAYtB,iBAAiB,CAXC,OAAC,CAAO,UAAC,IAAS,EAW9C;wBACY,EAAE,CAXC,IAAC,CAAI,GAAG,aAAA,CAAc,IAAC,CAAI,OAAC,EAAQ,IAAA,CAAK,CAAC;qBAY9C,CAXC,CAAC;iBAYJ;aACF;SACF;QAED,IAAI,CAXC,OAAC,GAAS,IAAA,CAAK,oBAAC,CAAoB,IAAC,CAAI,OAAC,EAAQ,SAAA,EAAW,IAAA,CAAK,OAAC,CAAO,CAAC;QAYhF,IAAI,CAXC,cAAC,GAAgB,SAAA,CAAU,MAAC,GAAQ,SAAA,CAAU,SAAC,CAAS,MAAC,GAAQ,CAAA,CAAE,GAAG,EAAA,CAAG;QAY9E,IAAI,CAXC,OAAC,CAAO,gBAAC,CAAgB,QAAC,EAAS,MAAM,IAAA,CAAK,SAAC,EAAS,CAAE,CAAC;KAYjE;;;;IATA,yBAAA,GAAH;;QAeI,IAAI,IAbC,CAAI,MAAC,EAAO;YAcf,IAAI,CAbC,oBAAC,EAAoB,CAAE;SAc7B;aAbM;YAcL,IAAI,CAbC,OAAC,CAAO,KAAC,EAAK,CAAE;SActB;KACF;;;;;;;;IAQH,oBAlBG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAkBH;;;QAGI,QAlBO,OAAA,CAAQ,SAAC,CAAS,CAAC,SAAC,EAAU,OAAA,CAAY,EAAa;KAmB/D;;;;IAIH,IApBG,SAAA,GAoBH,EApBoB,OAAO,IAAA,CAAK,OAAC,CAAO,EAAC;;;;;IAyBzC,OAvBG,CAAA,EAAA,EAuBH,EAvBkC,IAAA,CAAK,WAAC,CAAW,IAAC,CAAI,EAAC,CAAE,CAAC,EAAC;;;;;IA4B7D,MA1BG,CAAA,EAAA,EA0BH,EA1BiC,IAAA,CAAK,UAAC,CAAU,IAAC,CAAI,EAAC,CAAE,CAAC,EAAC;;;;;IA+B3D,SA7BG,CAAA,EAAA,EA6BH,EA7BoC,IAAA,CAAK,aAAC,CAAa,IAAC,CAAI,EAAC,CAAE,CAAC,EAAC;;;;IAiCjE,IA/BG,GA+BH;QACI,IAAI,CA/BC,YAAC,EAAY,CAAE;QAgCpB,IAAI,CA/BC,IAAC,CAAI,UAAC,EAAU,EAAG;YAgCtB,IAAI,CA/BC,WAAC,CAAW,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC;YAgCrC,IAAI,CA/BC,WAAC,GAAa,EAAA,CAAG;YAgCtB,IAAI,CA/BC,QAAC,GAAU,IAAA,CAAK;SAgCtB;QACD,IAAI,CA/BC,OAAC,CAAO,IAAC,EAAI,CAAE;KAgCrB;;;;IAIH,KAjCG,GAiCH;QACI,IAAI,CAjCC,IAAC,EAAI,CAAE;QAkCZ,IAAI,CAjCC,OAAC,CAAO,KAAC,EAAK,CAAE;KAkCtB;;;;IAIH,MAnCG,GAmCH;QACI,IAAI,CAnCC,IAAC,EAAI,CAAE;QAoCZ,IAAI,CAnCC,SAAC,EAAS,CAAE;QAoCjB,IAAI,CAnCC,OAAC,CAAO,MAAC,EAAM,CAAE;KAoCvB;;;;IAIH,KArCG,GAqCH;QACI,IAAI,CArCC,oBAAC,EAAoB,CAAE;QAsC5B,IAAI,CArCC,UAAC,GAAY,KAAA,CAAM;QAsCxB,IAAI,CArCC,SAAC,GAAW,KAAA,CAAM;QAsCvB,IAAI,CArCC,QAAC,GAAU,KAAA,CAAM;KAsCvB;;;;IAnCA,oBAAA,GAAH;QAwCI,IAAI,IAvCC,CAAI,OAAC,EAAQ;YAwChB,IAAI,CAvCC,OAAC,CAAO,MAAC,EAAM,CAAE;SAwCvB;KACF;;;;IAIH,OAzCG,GAyCH;QACI,IAAI,CAzCC,KAAC,EAAK,CAAE;QA0Cb,IAAI,CAzCC,IAAC,EAAI,CAAE;KA0Cb;;;;IAIH,UA3CG,GA2CH,EA3C0B,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;IA+ChD,OA7CG,GA6CH;QACI,IAAI,CA7CC,IAAC,CAAI,UAAC,EAAW;YA8CpB,IAAI,CA7CC,oBAAC,EAAoB,CAAE;YA8C5B,IAAI,CA7CC,SAAC,EAAS,CAAE;YA8CjB,IAAI,CA7CC,UAAC,GAAY,IAAA,CAAK;YA8CvB,IAAI,CA7CC,aAAC,CAAa,OAAC,CAAO,EAAC,IAAK,EAAA,EAAG,CAAE,CAAC;YA8CvC,IAAI,CA7CC,aAAC,GAAe,EAAA,CAAG;SA8CzB;KACF;;;;;IAKH,WAhDG,CAAA,CAAA,EAgDH,EAhDiC,IAAA,CAAK,OAAC,CAAO,WAAC,GAAa,CAAA,GAAI,IAAA,CAAK,IAAC,CAAI,EAAC;;;;IAoD3E,WAlDG,GAkDH,EAlD0B,OAAO,IAAA,CAAK,OAAC,CAAO,WAAC,GAAa,IAAA,CAAK,IAAC,CAAI,EAAC;;;;IAsDvE,IApDG,SAAA,GAoDH,EApD4B,OAAO,IAAA,CAAK,MAAC,GAAQ,IAAA,CAAK,SAAC,CAAS,EAAC;;;;IAwDjE,aAtDG,GAsDH;QACI,uBAtDM,MAAA,GAA2C,EAAA,CAAG;QAuDpD,IAAI,IAtDC,CAAI,UAAC,EAAU,EAAG;YAuDrB,MAAM,CAtDC,IAAC,CAAI,IAAC,CAAI,cAAC,CAAc,CAAC,OAAC,CAAO,IAAC,IAsDhD;gBACQ,IAAI,IAtDC,IAAO,QAAA,EAAU;oBAuDpB,MAAM,CAtDC,IAAC,CAAI;wBAuDR,IAAI,CAtDC,SAAC,GAAW,IAAA,CAAK,cAAC,CAAc,IAAC,CAAI,GAAG,aAAA,CAAc,IAAC,CAAI,OAAC,EAAQ,IAAA,CAAK,CAAC;iBAuDpF;aACF,CAtDC,CAAC;SAuDJ;QACD,IAAI,CAtDC,eAAC,GAAiB,MAAA,CAAO;KAuD/B;CACF;AAED,AAyCA;;;;;AAKA,SAAA,aAAA,CApGC,OAAA,EAAA,IAAA,EAoGD;IACE,OApGO,EAAM,MAAC,CAAM,gBAAC,CAAgB,OAAC,CAAO,GAAE,IAAC,CAAI,CAAC;CAqGtD;;ADjSD;;;;;;;AAWA,AAEA,AACA,AAAA,MAAA,mBAAA,CAAA;;;;;;IAMA,cALG,CAAA,OAAA,EAAA,QAAA,EAKH;QACI,OALO,cAAA,CAAe,OAAC,EAAQ,QAAA,CAAS,CAAC;KAM1C;;;;;;IAMH,eATG,CAAA,IAAA,EAAA,IAAA,EASH,EATmD,OAAO,eAAA,CAAgB,IAAC,EAAK,IAAA,CAAK,CAAC,EAAC;;;;;;;IAgBvF,KAdG,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,EAcH;QACI,OAdO,WAAA,CAAY,OAAC,EAAQ,QAAA,EAAU,KAAA,CAAM,CAAC;KAe9C;;;;;;;IAOH,YAnBG,CAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAmBH;QACI,QAnBO,EAAA,MAAE,CAAM,gBAAC,CAAgB,OAAC,CAAW,GAAK,IAAC,CAAQ,EAAO;KAoBlE;;;;;;;;;;IAUH,OA3BG,CA4BG,OAAY,EAAE,SAAuB,EAAE,QAAgB,EAAE,KAAa,EAAE,MAAc,EACtF,eAFN,GAE2C,EAAE,EAF7C;QAGI,uBA3BM,IAAA,GAAO,KAAA,IAAS,CAAA,GAAI,MAAA,GAAS,UAAA,CAAW;QA4B9C,uBA3BM,aAAA,GAAkD,EAAA,QAAE,EAAS,KAAA,EAAO,IAAA,EAAK,CAAC;;;QA+BhF,IAAI,MA3BC,EAAO;YA4BV,aAAa,CA3BC,QAAC,CAAQ,GAAG,MAAA,CAAO;SA4BlC;QAED,uBA3BM,2BAAA,IAAqD,eAAC,CAAe,MAAC,CA4BxE,MAAM,IADd,EA1BoB,OAAO,MAAA,YAAkB,mBAAA,CAAoB,EAAC,CAAE,CAAA,CAAC;QA4BjE,OA3BO,IAAI,mBAAA,CAAoB,OAAC,EAAQ,SAAA,EAAW,aAAA,EAAe,2BAAA,CAA4B,CAAC;KA4BhG;CACF;;;;AAID,AAAA,SAAA,qBAAA,GAAA;IACE,OA7BO,OAAO,OAAA,KAAY,WAAA,IAAe,OAAA,EAAY,OAAC,GAAQ,SAAC,CAAS,SAAC,CAAS,KAAK,UAAA,CAAW;CA8BnG;;AD9ED;;;;;;GAMG,AACH,AACA,AACA,AACA,AACA,AACA,AACA,AAAoD;;ADbpD;;;;;;;;;;;GAYG,AACH,AACA,AAAyO;;ADdzO;;;;;;;;;;;GAYG,AACH,AAAsP;;ADbtP;;GAEG,AAEH,AAAqP;;"}