{"version": 3, "file": "tsc.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/tsc.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAEH,yBAA8B;AAC9B,2BAA6B;AAC7B,+BAAiC;AAGjC,2CAAoD;AAepD;IAA+B,6BAAK;IAGlC,mBAAY,OAAe;QAA3B,YACE,kBAAM,OAAO,CAAC,SAOf;QANC,2BAA2B;QAC3B,gIAAgI;QAChI,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAM,WAAW,GAAG,IAAI,KAAK,CAAC,OAAO,CAAiB,CAAC;QACvD,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;;IAClC,CAAC;IAED,sBAAI,8BAAO;aAAX,cAAgB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;aACnD,UAAY,OAAO;YACjB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;gBAAC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7D,CAAC;;;OAHkD;IAInD,sBAAI,2BAAI;aAAR,cAAa,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;aAC7C,UAAS,IAAI;YACX,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;gBAAC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;QACvD,CAAC;;;OAH4C;IAI7C,sBAAI,4BAAK;aAAT,cAAc,MAAM,CAAE,IAAI,CAAC,YAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;aACxD,UAAU,KAAK;YACb,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;gBAAE,IAAI,CAAC,YAAoB,CAAC,KAAK,GAAG,KAAK,CAAC;QAClE,CAAC;;;OAHuD;IAIxD,4BAAQ,GAAR,cAAa,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACrD,gBAAC;AAAD,CAAC,AA1BD,CAA+B,KAAK,GA0BnC;AA1BY,8BAAS;AA4BtB,IAAM,KAAK,GAAG,KAAK,CAAC;AAEpB,eAAe,GAAW;IAAE,WAAW;SAAX,UAAW,EAAX,qBAAW,EAAX,IAAW;QAAX,0BAAW;;IACrC,sCAAsC;IACtC,EAAE,CAAC,CAAC,KAAK,CAAC;QAAC,OAAO,CAAC,GAAG,OAAX,OAAO,GAAK,GAAG,SAAK,CAAC,GAAE;AACpC,CAAC;AAED,2BAAkC,KAAsB;IACtD,MAAM,CAAC,KAAK;SACP,GAAG,CAAC,UAAC,CAAC;QACL,IAAI,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC5C,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACX,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;YAChC,IAAA,kDAAiE,EAAhE,cAAI,EAAE,wBAAS,CAAkD;YACxE,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QAClD,CAAC;QACD,GAAG,IAAI,GAAG,GAAG,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAClE,MAAM,CAAC,GAAG,CAAC;IACb,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,CAAC;AAbD,8CAaC;AAED,eAAsB,KAAsB;IAC1C,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,CAAC;AACH,CAAC;AAJD,sBAIC;AAED,wCAA+C,OAA+B;IAC5E,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;YAC9B,KAAK,YAAY,CAAC;YAClB,KAAK,eAAe;gBAClB,KAAK,CAAC;YACR;gBACE,MAAM,CAAC,CAAC;wBACN,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,MAAM,EAAE,IAAI;wBACZ,WAAW,EACP,yFAAyF;wBAC7F,QAAQ,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK;wBACrC,IAAI,EAAE,CAAC;qBACR,CAAC,CAAC;QACP,CAAC;IACH,CAAC;AACH,CAAC;AAlBD,wEAkBC;AAED;IAGE,aAAoB,QAA0B,EAAU,aAAoC;QAAxE,yBAAA,EAAA,WAAW,EAAE,CAAC,GAAG,CAAC,QAAQ;QAAU,8BAAA,EAAA,gBAAgB,EAAE,CAAC,GAAG,CAAC,aAAa;QAAxE,aAAQ,GAAR,QAAQ,CAAkB;QAAU,kBAAa,GAAb,aAAa,CAAuB;QAC1F,IAAI,CAAC,eAAe,GAAG;YACrB,yBAAyB,EAAE,IAAI;YAC/B,UAAU,EAAE,eAAU;YACtB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAED,+BAAiB,GAAjB,UACI,OAAyB,EAAE,QAAgB,EAAE,eAAoC;QADrF,iBAuCC;QArCC,kEAAkE;QAClE,qEAAqE;QACrE,IAAI,CAAC;YACH,EAAE,CAAC,CAAC,CAAC,wBAAW,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,wDAAwD;QAC1D,CAAC;QAEG,IAAA;;;;;;;;YASA,EATC,kBAAM,EAAE,gBAAK,CASb;QACL,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAEf,IAAM,MAAM,GACR,EAAE,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAE3F,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAErB,gDAAgD;QAChD,yDAAyD;QACzD,IAAM,SAAS,GAAG,MAAM,CAAC,sBAAsB,IAAI,EAAE,CAAC;QACtD,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;QAChE,GAAG,CAAC,CAAc,UAA2B,EAA3B,KAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAA3B,cAA2B,EAA3B,IAA2B;YAAxC,IAAM,GAAG,SAAA;YACZ,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACtC;QACD,KAAK,CAAC,8BAA8B,CAAC,SAAS,CAAC,CAAC,CAAC;QAEjD,MAAM,CAAC,EAAC,MAAM,QAAA,EAAE,SAAS,WAAA,EAAC,CAAC;IAC7B,CAAC;IAED,uBAAS,GAAT,UAAU,YAA6B,EAAE,OAAmB;QAC1D,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACxC,KAAK,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAEtC,IAAM,WAAW,GAAoB,EAAE,CAAC;QACxC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAE1B,GAAG,CAAC,CAAa,UAAwB,EAAxB,KAAA,OAAO,CAAC,cAAc,EAAE,EAAxB,cAAwB,EAAxB,IAAwB;YAApC,IAAM,EAAE,SAAA;YACX,WAAW,CAAC,IAAI,OAAhB,WAAW,EAAS,EAAE,CAAC,qBAAqB,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;SAC5D;QACD,KAAK,CAAC,WAAW,CAAC,CAAC;IACrB,CAAC;IAED,kBAAI,GAAJ,UAAK,OAAmB;QACtB,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC7B,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,IAAM,WAAW,GAAoB,EAAE,CAAC;QACxC,WAAW,CAAC,IAAI,OAAhB,WAAW,EAAS,UAAU,CAAC,WAAW,EAAE;QAC5C,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IACH,UAAC;AAAD,CAAC,AAzED,IAyEC;AAzEY,kBAAG;AA0EH,QAAA,GAAG,GAAsB,IAAI,GAAG,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {existsSync} from 'fs';\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport AngularCompilerOptions from './options';\nimport {VinylFile, isVinylFile} from './vinyl_file';\n\n/**\n * Our interface to the TypeScript standard compiler.\n * If you write an Angular compiler plugin for another build tool,\n * you should implement a similar interface.\n */\nexport interface CompilerInterface {\n  readConfiguration(\n      project: string|VinylFile, basePath: string, existingOptions?: ts.CompilerOptions):\n      {parsed: ts.ParsedCommandLine, ngOptions: AngularCompilerOptions};\n  typeCheck(compilerHost: ts.CompilerHost, program: ts.Program): void;\n  emit(program: ts.Program): number;\n}\n\nexport class UserError extends Error {\n  private _nativeError: Error;\n\n  constructor(message: string) {\n    super(message);\n    // Required for TS 2.1, see\n    // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, UserError.prototype);\n\n    const nativeError = new Error(message) as any as Error;\n    this._nativeError = nativeError;\n  }\n\n  get message() { return this._nativeError.message; }\n  set message(message) {\n    if (this._nativeError) this._nativeError.message = message;\n  }\n  get name() { return this._nativeError.name; }\n  set name(name) {\n    if (this._nativeError) this._nativeError.name = name;\n  }\n  get stack() { return (this._nativeError as any).stack; }\n  set stack(value) {\n    if (this._nativeError) (this._nativeError as any).stack = value;\n  }\n  toString() { return this._nativeError.toString(); }\n}\n\nconst DEBUG = false;\n\nfunction debug(msg: string, ...o: any[]) {\n  // tslint:disable-next-line:no-console\n  if (DEBUG) console.log(msg, ...o);\n}\n\nexport function formatDiagnostics(diags: ts.Diagnostic[]): string {\n  return diags\n      .map((d) => {\n        let res = ts.DiagnosticCategory[d.category];\n        if (d.file) {\n          res += ' at ' + d.file.fileName + ':';\n          const {line, character} = d.file.getLineAndCharacterOfPosition(d.start);\n          res += (line + 1) + ':' + (character + 1) + ':';\n        }\n        res += ' ' + ts.flattenDiagnosticMessageText(d.messageText, '\\n');\n        return res;\n      })\n      .join('\\n');\n}\n\nexport function check(diags: ts.Diagnostic[]) {\n  if (diags && diags.length && diags[0]) {\n    throw new UserError(formatDiagnostics(diags));\n  }\n}\n\nexport function validateAngularCompilerOptions(options: AngularCompilerOptions): ts.Diagnostic[] {\n  if (options.annotationsAs) {\n    switch (options.annotationsAs) {\n      case 'decorators':\n      case 'static fields':\n        break;\n      default:\n        return [{\n          file: null,\n          start: null,\n          length: null,\n          messageText:\n              'Angular compiler options \"annotationsAs\" only supports \"static fields\" and \"decorators\"',\n          category: ts.DiagnosticCategory.Error,\n          code: 0\n        }];\n    }\n  }\n}\n\nexport class Tsc implements CompilerInterface {\n  private parseConfigHost: ts.ParseConfigHost;\n\n  constructor(private readFile = ts.sys.readFile, private readDirectory = ts.sys.readDirectory) {\n    this.parseConfigHost = {\n      useCaseSensitiveFileNames: true,\n      fileExists: existsSync,\n      readDirectory: this.readDirectory,\n      readFile: ts.sys.readFile\n    };\n  }\n\n  readConfiguration(\n      project: string|VinylFile, basePath: string, existingOptions?: ts.CompilerOptions) {\n    // Allow a directory containing tsconfig.json as the project value\n    // Note, TS@next returns an empty array, while earlier versions throw\n    try {\n      if (!isVinylFile(project) && this.readDirectory(project).length > 0) {\n        project = path.join(project, 'tsconfig.json');\n      }\n    } catch (e) {\n      // Was not a directory, continue on assuming it's a file\n    }\n\n    let {config, error} = (() => {\n      // project is vinyl like file object\n      if (isVinylFile(project)) {\n        return {config: JSON.parse(project.contents.toString()), error: null};\n      }\n      // project is path to project file\n      else {\n        return ts.readConfigFile(project, this.readFile);\n      }\n    })();\n    check([error]);\n\n    const parsed =\n        ts.parseJsonConfigFileContent(config, this.parseConfigHost, basePath, existingOptions);\n\n    check(parsed.errors);\n\n    // Default codegen goes to the current directory\n    // Parsed options are already converted to absolute paths\n    const ngOptions = config.angularCompilerOptions || {};\n    ngOptions.genDir = path.join(basePath, ngOptions.genDir || '.');\n    for (const key of Object.keys(parsed.options)) {\n      ngOptions[key] = parsed.options[key];\n    }\n    check(validateAngularCompilerOptions(ngOptions));\n\n    return {parsed, ngOptions};\n  }\n\n  typeCheck(compilerHost: ts.CompilerHost, program: ts.Program): void {\n    debug('Checking global diagnostics...');\n    check(program.getGlobalDiagnostics());\n\n    const diagnostics: ts.Diagnostic[] = [];\n    debug('Type checking...');\n\n    for (const sf of program.getSourceFiles()) {\n      diagnostics.push(...ts.getPreEmitDiagnostics(program, sf));\n    }\n    check(diagnostics);\n  }\n\n  emit(program: ts.Program): number {\n    debug('Emitting outputs...');\n    const emitResult = program.emit();\n    const diagnostics: ts.Diagnostic[] = [];\n    diagnostics.push(...emitResult.diagnostics);\n    return emitResult.emitSkipped ? 1 : 0;\n  }\n}\nexport const tsc: CompilerInterface = new Tsc();\n"]}