{"title": "DOM Parsing and Serialization", "description": "Various DOM parsing and serializing functions, specifically `DOMParser`, `XMLSerializer`, `innerHTML`, `outerHTML` and `insertAdjacentHTML`.", "spec": "http://www.w3.org/TR/DOM-Parsing/", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/XMLSerializer", "title": "Mozilla Developer Network (MDN) documentation - XMLSerializer"}, {"url": "http://ejohn.org/blog/dom-insertadjacenthtml/", "title": "Comparing Document Position by <PERSON>"}], "bugs": [{"description": "In IE10 and IE11, when using `innerText`, `innerHTML` or `outerHTML` on a `textarea` which has a `placeholder` attribute, the returned HTML/text also uses the `placeholder`'s value as the actual `textarea`'s value. [See bug](https://connect.microsoft.com/IE/feedback/details/811408)."}, {"description": "innerHTML, insertAdjacentHTML, etc aren't supported or are read-only on the following elements in IE9 and below: col, colgroup, frameset, html, head, style, table, tbody, tfoot, thead, title, and tr."}], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #1", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a #2", "3": "a #2", "3.5": "a #2", "3.6": "a #2", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #3", "9": "a #3", "10": "a #3", "11": "a #1", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "a #1", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "a #1", "16": "a #1", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #1", "4.2-4.3": "a #1", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "a #1", "2.2": "a #1", "2.3": "a #1", "3": "a #1", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "a #1", "10": "a #1"}, "op_mob": {"10": "u", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "a #1", "12.1": "a #1", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to lacking support for `parseFromString` on the DOMParser.", "2": "Partial support refers to supporting only `innerHTML`.", "3": "Partial support refers to supporting only `innerHTML` and `insertAdjacentHTML`."}, "usage_perc_y": 93.27, "usage_perc_a": 4.81, "ucprefix": false, "parent": "", "keywords": "parseFromString", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}