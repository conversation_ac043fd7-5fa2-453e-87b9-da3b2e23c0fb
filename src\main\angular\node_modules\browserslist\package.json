{"_args": [["browserslist@1.7.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserslist@1.7.7", "_id": "browserslist@1.7.7", "_inBundle": false, "_integrity": "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk=", "_location": "/browserslist", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserslist@1.7.7", "name": "browserslist", "escapedName": "browserslist", "rawSpec": "1.7.7", "saveSpec": null, "fetchSpec": "1.7.7"}, "_requiredBy": ["/autoprefixer", "/caniuse-api", "/postcss-merge-rules"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserslist/-/browserslist-1.7.7.tgz", "_spec": "1.7.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"browserslist": "cli.js"}, "bugs": {"url": "https://github.com/ai/browserslist/issues"}, "dependencies": {"caniuse-db": "^1.0.30000639", "electron-to-chromium": "^1.2.7"}, "description": "Share browsers list between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "devDependencies": {"eslint": "^3.18.0", "eslint-config-postcss": "^2.0.2", "jest": "^19.0.2", "lint-staged": "^3.4.0", "pre-commit": "^1.1.3", "yaspeller-ci": "^0.3.0"}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "env": {"jest": true}, "rules": {"consistent-return": "off", "no-process-exit": "off", "valid-jsdoc": "error"}}, "homepage": "https://github.com/ai/browserslist#readme", "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "keywords": ["caniuse", "browsers"], "license": "MIT", "lint-staged": {"*.md": "yaspeller-ci", "*.js": "eslint"}, "name": "browserslist", "pre-commit": ["lint-staged"], "repository": {"type": "git", "url": "git+https://github.com/ai/browserslist.git"}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js && yaspeller-ci *.md"}, "version": "1.7.7"}