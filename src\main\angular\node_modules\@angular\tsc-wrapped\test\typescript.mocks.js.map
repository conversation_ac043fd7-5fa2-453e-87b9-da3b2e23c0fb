{"version": 3, "file": "typescript.mocks.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/typescript.mocks.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,uBAAyB;AACzB,+BAAiC;AAIjC;IAIE,cAAoB,SAAoB,EAAU,OAAiB;QAA/C,cAAS,GAAT,SAAS,CAAW;QAAU,YAAO,GAAP,OAAO,CAAU;QAH3D,cAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;QACtC,YAAO,GAAG,CAAC,CAAC;IAEkD,CAAC;IAEvE,qCAAsB,GAAtB;QACE,MAAM,CAAC;YACL,sBAAsB,EAAE,IAAI;YAC5B,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;YAC9B,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG;SAC5B,CAAC;IACJ,CAAC;IAED,iCAAkB,GAAlB,cAAiC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAEvD,+BAAgB,GAAhB,UAAiB,QAAgB,IAAY,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAE9E,gCAAiB,GAAjB,UAAkB,QAAgB;QAChC,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9C,EAAE,CAAC,CAAC,OAAO,CAAC;YAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,kCAAmB,GAAnB,cAAgC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,oCAAqB,GAArB,UAAsB,OAA2B,IAAY,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjF,2BAAY,GAAZ,UAAa,QAAgB,EAAE,OAAe;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED,sBAAO,GAAP,UAAQ,QAAgB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEO,6BAAc,GAAtB,UAAuB,QAAgB;QACrC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;QACD,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAC1F,CAAC;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC/C,EAAE,CAAC,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC;YAAC,MAAM,CAAC,OAAO,CAAC;IAClD,CAAC;IACH,WAAC;AAAD,CAAC,AA/CD,IA+CC;AA/CY,oBAAI;AAiDjB,cAAqB,SAAoB,EAAE,QAAgB;IACzD,+FAA+F;IAC/F,kGAAkG;IAClG,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,IAAI,OAAO,GAAqB,SAAS,CAAC;IAC1C,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACnD,GAAG,CAAC,CAAe,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK;QAAnB,IAAM,MAAI,cAAA;QACb,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAC;YAAC,MAAM,CAAC,SAAS,CAAC;QAC9D,OAAO,GAAG,OAAO,CAAC,MAAI,CAAC,CAAC;KACzB;IACD,MAAM,CAAC,OAAO,CAAC;AACjB,CAAC;AAXD,oBAWC;AAED;IACE,kBACW,IAA8C,EAAS,KAAuB,EAC9E,GAAe,EAAS,GAAe;QADvC,qBAAA,EAAA,OAAsB,EAAE,CAAC,UAAU,CAAC,UAAU;QAAS,sBAAA,EAAA,SAAuB;QAC9E,oBAAA,EAAA,OAAe;QAAS,oBAAA,EAAA,OAAe;QADvC,SAAI,GAAJ,IAAI,CAA0C;QAAS,UAAK,GAAL,KAAK,CAAkB;QAC9E,QAAG,GAAH,GAAG,CAAY;QAAS,QAAG,GAAH,GAAG,CAAY;IAAG,CAAC;IACtD,gCAAa,GAAb,cAAiC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,gCAAa,GAAb,UAAc,UAA0B,IAAY,MAAM,CAAC,CAAC,CAAA,CAAC,CAAC;IAC9D,6BAAU,GAAV,UAAW,KAAa,EAAE,UAA0B,IAAa,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E,8BAAW,GAAX,UAAY,UAA0B,IAAe,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,2BAAQ,GAAR,UAAS,UAA0B,IAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,+BAAY,GAAZ,cAAyB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,yBAAM,GAAN,cAAmB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,2BAAQ,GAAR,UAAS,UAA0B,IAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,+BAAY,GAAZ,cAAyB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,wCAAqB,GAArB,UAAsB,UAA0B,IAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,8BAAW,GAAX,UAAY,UAA0B,IAAY,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9D,0BAAO,GAAP,UAAQ,UAA0B,IAAY,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,gCAAa,GAAb,UAAc,UAA0B,IAAa,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACnE,+BAAY,GAAZ,UAAa,UAA0B,IAAa,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,+BAAY,GAAZ,UAAgB,MAA4B,EAAE,WAAqC;QACjF,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IACH,eAAC;AAAD,CAAC,AArBD,IAqBC;AArBY,4BAAQ;AAuBrB;IAAoC,kCAAQ;IAS1C,wBACW,IAAY,EAAS,IAAyD,EACrF,KAAuB,EAAE,GAAe,EAAE,GAAe;QAD7B,qBAAA,EAAA,OAAiC,EAAE,CAAC,UAAU,CAAC,UAAU;QACrF,sBAAA,EAAA,SAAuB;QAAE,oBAAA,EAAA,OAAe;QAAE,oBAAA,EAAA,OAAe;QAF7D,YAGE,kBAAM,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,SAE7B;QAJU,UAAI,GAAJ,IAAI,CAAQ;QAAS,UAAI,GAAJ,IAAI,CAAqD;QAGvF,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;;IACnB,CAAC;IACH,qBAAC;AAAD,CAAC,AAfD,CAAoC,QAAQ,GAe3C;AAfY,wCAAc;AAiB3B;IAA6C,2CAAQ;IAGnD,iCACW,IAAmB,EACnB,IAA2E,EAClF,KAAuB,EAAE,GAAe,EAAE,GAAe;QADlD,qBAAA,EAAA,OAA0C,EAAE,CAAC,UAAU,CAAC,mBAAmB;QAClF,sBAAA,EAAA,SAAuB;QAAE,oBAAA,EAAA,OAAe;QAAE,oBAAA,EAAA,OAAe;QAH7D,YAIE,kBAAM,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,SAC7B;QAJU,UAAI,GAAJ,IAAI,CAAe;QACnB,UAAI,GAAJ,IAAI,CAAuE;;IAGtF,CAAC;IAEM,0BAAE,GAAT,UAAW,IAAY;QACrB,MAAM,CAAC,IAAI,uBAAuB,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IACH,8BAAC;AAAD,CAAC,AAbD,CAA6C,QAAQ,GAapD;AAbY,0DAAuB;AAepC;IACE,oBACW,IAAY,EAAU,IAAuD,EAC7E,KAAyB;QADH,qBAAA,EAAA,OAAuB,uBAAuB,CAAC,EAAE,CAAC,IAAI,CAAC;QAC7E,sBAAA,EAAA,SAAyB;QADzB,SAAI,GAAJ,IAAI,CAAQ;QAAU,SAAI,GAAJ,IAAI,CAAmD;QAC7E,UAAK,GAAL,KAAK,CAAoB;IAAG,CAAC;IAExC,6BAAQ,GAAR,cAA6B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,4BAAO,GAAP,cAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACvC,oCAAe,GAAf,cAAsC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3D,4CAAuB,GAAvB,cAAoD,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAChE,gCAAgC;IAChC,iCAAY,GAAZ,cAAsB,MAAM,CAAC,EAAE,CAAA,CAAA,CAAC;IAAA,CAAC;IAE1B,aAAE,GAAT,UAAW,IAAY,IAAgB,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvE,iBAAC;AAAD,CAAC,AAbD,IAaC;AAbY,gCAAU;AAevB,6BAAoC,WAA4B;IAC9D,GAAG,CAAC,CAAqB,UAAW,EAAX,2BAAW,EAAX,yBAAW,EAAX,IAAW;QAA/B,IAAM,UAAU,oBAAA;QACnB,IAAM,OAAO,GAAG,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACxE,IAAA,oEAAmF,EAAlF,cAAI,EAAE,wBAAS,CAAoE;QAC1F,OAAO,CAAC,GAAG,CAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,WAAK,IAAI,GAAG,CAAC,WAAI,SAAS,GAAG,CAAC,YAAM,OAAS,CAAC,CAAC;KACvF;IACD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC;AAPD,kDAOC;AAED,4BAAmC,OAA2B,EAAE,OAAmB;IACjF,mBAAmB,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,CAAC;IAC7D,GAAG,CAAC,CAAqB,UAAwB,EAAxB,KAAA,OAAO,CAAC,cAAc,EAAE,EAAxB,cAAwB,EAAxB,IAAwB;QAA5C,IAAM,UAAU,SAAA;QACnB,mBAAmB,CAAC,OAAO,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1E,mBAAmB,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC1E;AACH,CAAC;AAND,gDAMC;AAED,qBAA+B,IAAa,EAAE,EAAwB;IACpE,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAA,KAAK;QAChC,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QACxB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChC,CAAC,CAAC,CAAA;AACJ,CAAC;AARD,kCAQC;AAED,mBAA0B,UAAyB,EAAE,IAAY;IAC/D,MAAM,CAAC,EAAE,CAAC,YAAY,CAClB,UAAU,EAAE,UAAA,IAAI,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,SAAS,EAA5D,CAA4D,CAAC,CAAC;AACxF,CAAC;AAHD,8BAGC;AAED,iBAAwB,UAAyB,EAAE,IAAY;IAC7D,MAAM,CAAC,WAAW,CACd,UAAU,EAAE,UAAA,IAAI,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,SAAS,EAA1D,CAA0D,CAAC,CAAC;AACtF,CAAC;AAHD,0BAGC;AAED,iBAAwB,IAAa;IACnC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;AACtD,CAAC;AAFD,0BAEC;AAED,iBAAwB,IAAa,EAAE,IAAY;IACjD,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,IAAoB,IAAK,CAAC,IAAI,KAAK,IAAI,CAAC;AACvF,CAAC;AAFD,0BAEC;AAED,eAAsB,IAAa;IACjC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC;AACzD,CAAC;AAFD,sBAEC", "sourcesContent": ["import * as fs from 'fs';\nimport * as ts from 'typescript';\n\nexport interface Directory { [name: string]: (Directory|string); }\n\nexport class Host implements ts.LanguageServiceHost {\n  private overrides = new Map<string, string>();\n  private version = 1;\n\n  constructor(private directory: Directory, private scripts: string[]) {}\n\n  getCompilationSettings(): ts.CompilerOptions {\n    return {\n      experimentalDecorators: true,\n      module: ts.ModuleKind.CommonJS,\n      target: ts.ScriptTarget.ES5\n    };\n  }\n\n  getScriptFileNames(): string[] { return this.scripts; }\n\n  getScriptVersion(fileName: string): string { return this.version.toString(); }\n\n  getScriptSnapshot(fileName: string): ts.IScriptSnapshot {\n    const content = this.getFileContent(fileName);\n    if (content) return ts.ScriptSnapshot.fromString(content);\n  }\n\n  getCurrentDirectory(): string { return '/'; }\n\n  getDefaultLibFileName(options: ts.CompilerOptions): string { return 'lib.d.ts'; }\n\n  overrideFile(fileName: string, content: string) {\n    this.overrides.set(fileName, content);\n    this.version++;\n  }\n\n  addFile(fileName: string) {\n    this.scripts.push(fileName);\n    this.version++;\n  }\n\n  private getFileContent(fileName: string): string {\n    if (this.overrides.has(fileName)) {\n      return this.overrides.get(fileName);\n    }\n    if (fileName.endsWith('lib.d.ts')) {\n      return fs.readFileSync(ts.getDefaultLibFilePath(this.getCompilationSettings()), 'utf8');\n    }\n    const current = open(this.directory, fileName);\n    if (typeof current === 'string') return current;\n  }\n}\n\nexport function open(directory: Directory, fileName: string): Directory|string|undefined {\n  // Path might be normalized by the current node environment. But it could also happen that this\n  // path directly comes from the compiler in POSIX format. Support both separators for development.\n  const names = fileName.split(/[\\\\/]/);\n  let current: Directory|string = directory;\n  if (names.length && names[0] === '') names.shift();\n  for (const name of names) {\n    if (!current || typeof current === 'string') return undefined;\n    current = current[name];\n  }\n  return current;\n}\n\nexport class MockNode implements ts.Node {\n  constructor(\n      public kind: ts.SyntaxKind = ts.SyntaxKind.Identifier, public flags: ts.NodeFlags = 0,\n      public pos: number = 0, public end: number = 0) {}\n  getSourceFile(): ts.SourceFile { return null; }\n  getChildCount(sourceFile?: ts.SourceFile): number { return 0 }\n  getChildAt(index: number, sourceFile?: ts.SourceFile): ts.Node { return null; }\n  getChildren(sourceFile?: ts.SourceFile): ts.Node[] { return []; }\n  getStart(sourceFile?: ts.SourceFile): number { return 0; }\n  getFullStart(): number { return 0; }\n  getEnd(): number { return 0; }\n  getWidth(sourceFile?: ts.SourceFile): number { return 0; }\n  getFullWidth(): number { return 0; }\n  getLeadingTriviaWidth(sourceFile?: ts.SourceFile): number { return 0; }\n  getFullText(sourceFile?: ts.SourceFile): string { return ''; }\n  getText(sourceFile?: ts.SourceFile): string { return ''; }\n  getFirstToken(sourceFile?: ts.SourceFile): ts.Node { return null; }\n  getLastToken(sourceFile?: ts.SourceFile): ts.Node { return null; }\n  forEachChild<T>(cbNode: (node: ts.Node) => T, cbNodeArray?: (nodes: ts.Node[]) => T): T {\n    return null;\n  }\n}\n\nexport class MockIdentifier extends MockNode implements ts.Identifier {\n  public text: string;\n  public _primaryExpressionBrand: any;\n  public _memberExpressionBrand: any;\n  public _leftHandSideExpressionBrand: any;\n  public _incrementExpressionBrand: any;\n  public _unaryExpressionBrand: any;\n  public _expressionBrand: any;\n\n  constructor(\n      public name: string, public kind: ts.SyntaxKind.Identifier = ts.SyntaxKind.Identifier,\n      flags: ts.NodeFlags = 0, pos: number = 0, end: number = 0) {\n    super(kind, flags, pos, end);\n    this.text = name;\n  }\n}\n\nexport class MockVariableDeclaration extends MockNode implements ts.VariableDeclaration {\n  public _declarationBrand: any;\n\n  constructor(\n      public name: ts.Identifier,\n      public kind: ts.SyntaxKind.VariableDeclaration = ts.SyntaxKind.VariableDeclaration,\n      flags: ts.NodeFlags = 0, pos: number = 0, end: number = 0) {\n    super(kind, flags, pos, end);\n  }\n\n  static of (name: string): MockVariableDeclaration {\n    return new MockVariableDeclaration(new MockIdentifier(name));\n  }\n}\n\nexport class MockSymbol implements ts.Symbol {\n  constructor(\n      public name: string, private node: ts.Declaration = MockVariableDeclaration.of(name),\n      public flags: ts.SymbolFlags = 0) {}\n\n  getFlags(): ts.SymbolFlags { return this.flags; }\n  getName(): string { return this.name; }\n  getDeclarations(): ts.Declaration[] { return [this.node]; }\n  getDocumentationComment(): ts.SymbolDisplayPart[] { return []; }\n  // TODO(vicb): removed in TS 2.2\n  getJsDocTags(): any[]{return []};\n\n  static of (name: string): MockSymbol { return new MockSymbol(name); }\n}\n\nexport function expectNoDiagnostics(diagnostics: ts.Diagnostic[]) {\n  for (const diagnostic of diagnostics) {\n    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\\n');\n    const {line, character} = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);\n    console.log(`${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`);\n  }\n  expect(diagnostics.length).toBe(0);\n}\n\nexport function expectValidSources(service: ts.LanguageService, program: ts.Program) {\n  expectNoDiagnostics(service.getCompilerOptionsDiagnostics());\n  for (const sourceFile of program.getSourceFiles()) {\n    expectNoDiagnostics(service.getSyntacticDiagnostics(sourceFile.fileName));\n    expectNoDiagnostics(service.getSemanticDiagnostics(sourceFile.fileName));\n  }\n}\n\nexport function allChildren<T>(node: ts.Node, cb: (node: ts.Node) => T): T {\n  return ts.forEachChild(node, child => {\n    const result = cb(node);\n    if (result) {\n      return result;\n    }\n    return allChildren(child, cb);\n  })\n}\n\nexport function findClass(sourceFile: ts.SourceFile, name: string): ts.ClassDeclaration {\n  return ts.forEachChild(\n      sourceFile, node => isClass(node) && isNamed(node.name, name) ? node : undefined);\n}\n\nexport function findVar(sourceFile: ts.SourceFile, name: string): ts.VariableDeclaration {\n  return allChildren(\n      sourceFile, node => isVar(node) && isNamed(node.name, name) ? node : undefined);\n}\n\nexport function isClass(node: ts.Node): node is ts.ClassDeclaration {\n  return node.kind === ts.SyntaxKind.ClassDeclaration;\n}\n\nexport function isNamed(node: ts.Node, name: string): node is ts.Identifier {\n  return node.kind === ts.SyntaxKind.Identifier && (<ts.Identifier>node).text === name;\n}\n\nexport function isVar(node: ts.Node): node is ts.VariableDeclaration {\n  return node.kind === ts.SyntaxKind.VariableDeclaration;\n}\n"]}