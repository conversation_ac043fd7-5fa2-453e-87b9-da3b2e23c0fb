{"_args": [["component-inherit@0.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "component-inherit@0.0.3", "_id": "component-inherit@0.0.3", "_inBundle": false, "_integrity": "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM=", "_location": "/component-inherit", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "component-inherit@0.0.3", "name": "component-inherit", "escapedName": "component-inherit", "rawSpec": "0.0.3", "saveSpec": null, "fetchSpec": "0.0.3"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/component-inherit/-/component-inherit-0.0.3.tgz", "_spec": "0.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/component/inherit/issues"}, "component": {"scripts": {"inherit/index.js": "index.js"}}, "dependencies": {}, "description": "Prototype inheritance utility", "homepage": "https://github.com/component/inherit#readme", "keywords": ["inherit", "utility"], "name": "component-inherit", "repository": {"type": "git", "url": "git+https://github.com/component/inherit.git"}, "version": "0.0.3"}