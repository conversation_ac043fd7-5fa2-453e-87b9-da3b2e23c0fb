{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/platform-browser-dynamic/testing/index.ts", "../../../../../packages/platform-browser-dynamic/testing/src/testing.ts", "../../../../../packages/platform-browser-dynamic/testing/src/private_export_testing.ts", "../../../../../packages/platform-browser-dynamic/testing/src/dom_test_component_renderer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {platformCoreDynamicTesting} from '@angular/compiler/testing';\nimport {NgModule, PlatformRef, Provider, createPlatformFactory} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from '@angular/platform-browser-dynamic';\nimport {BrowserTestingModule} from '@angular/platform-browser/testing';\n\nimport {DOMTestComponentRenderer} from './dom_test_component_renderer';\n\nexport * from './private_export_testing'\n\n/**\n * @stable\n */\nexport const platformBrowserDynamicTesting = createPlatformFactory(\n    platformCoreDynamicTesting, 'browserDynamicTesting',\n    INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @stable\n */\n\nexport class BrowserDynamicTestingModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [BrowserTestingModule],\n  providers: [\n    {provide: TestComponentRenderer, useClass: DOMTestComponentRenderer},\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {DOMTestComponentRenderer as ɵDOMTestComponentRenderer} from './dom_test_component_renderer';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/platform-browser';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\n\nexport class DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor( private _doc: any /** TODO #9100 */) { super(); }\n\n  insertRootElement(rootElId: string) {\n    const rootEl = <HTMLElement>getDOM().firstChild(\n        getDOM().content(getDOM().createTemplate(`<div id=\"${rootElId}\"></div>`)));\n\n    // TODO(juliemr): can/should this be optional?\n    const oldRoots = getDOM().querySelectorAll(this._doc, '[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      getDOM().remove(oldRoots[i]);\n    }\n    getDOM().appendChild(this._doc.body, rootEl);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "getDOM"], "mappings": ";;;;;;;AGAA;;;;;;;AAQA,AACA,AACA,AAEA;;;AAIA,AAAA,MAAA,wBAAsC,SAAQ,qBAAqB,CAAnE;IACE,WAAF,CAAuB,IAAS,oBAAhC;QAAsD,KAAK,EAAE,CAAC;QAAvC,IAAvB,CAAA,IAA2B,GAAJ,IAAI,CAAK;KAAgC;IAE9D,iBAAiB,CAAC,QAAgB,EAApC;QACI,MAAM,MAAM,GAAgBC,OAAM,EAAE,CAAC,UAAU,CAC3CA,OAAM,EAAE,CAAC,OAAO,CAACA,OAAM,EAAE,CAAC,cAAc,CAAC,CADjD,SAAA,EAC6D,QAAQ,CADrE,QAAA,CAC+E,CAAC,CAAC,CAAC,CAAC;;QAG/E,MAAM,QAAQ,GAAGA,OAAM,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxCA,OAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;QACDA,OAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC9C;;AACI,wBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,wBAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC;;ADpCF;;;;;;GAMG,AAEH,AAAoG;;ADRpG;;;;;;;AAQA,AACA,AACA,AACA,AACA,AAEA,AAEA,AAEA;;;AAGA,AAAO,MAAM,6BAAA,GAAgC,qBAAA,CACzC,0BAA0B,EAAE,uBAAA,EAC5BD,4CAA2C,CAAC,CAAC;;;;;;AAQjD,AAAA,MAAA,2BAAA,CAAA;;AACO,2BAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;gBACvB,OAAO,EAAE,CAAC,oBAAoB,CAAC;gBAC/B,SAAS,EAAE;oBACT,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC;iBACrE;aACF,EAAG,EAAE;CACL,CAAC;;AAEK,2BAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;;AD1CF;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}