{"_args": [["bn.js@4.11.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "bn.js@4.11.7", "_id": "bn.js@4.11.7", "_inBundle": false, "_integrity": "sha512-LxFiV5mefv0ley0SzqkOPR1bC4EbpPx8LkOz5vMe/Yi15t5hzwgO/G+tc7wOtL4PZTYjwHu8JnEiSLumuSjSfA==", "_location": "/bn.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bn.js@4.11.7", "name": "bn.js", "escapedName": "bn.js", "rawSpec": "4.11.7", "saveSpec": null, "fetchSpec": "4.11.7"}, "_requiredBy": ["/asn1.js", "/browserify-rsa", "/browserify-sign", "/create-ecdh", "/diffie-hellman", "/elliptic", "/miller-rabin", "/public-encrypt"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/bn.js/-/bn.js-4.11.7.tgz", "_spec": "4.11.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "description": "Big number implementation in pure javascript", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "semistandard": "^7.0.4"}, "homepage": "https://github.com/indutny/bn.js", "keywords": ["BN", "BigNum", "Big number", "<PERSON><PERSON><PERSON>", "<PERSON>"], "license": "MIT", "main": "lib/bn.js", "name": "bn.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/bn.js.git"}, "scripts": {"lint": "semistandard", "test": "npm run lint && npm run unit", "unit": "mocha --reporter=spec test/*-test.js"}, "version": "4.11.7"}