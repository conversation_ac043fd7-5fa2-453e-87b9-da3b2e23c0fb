{"version": 3, "file": "compiler-testing.umd.min.js", "sources": ["../../../../packages/compiler/testing/src/metadata_overrider.ts", "../../../../packages/compiler/testing/src/testing.ts", "../../../../packages/compiler/testing/index.ts", "../../../../packages/compiler/testing/src/pipe_resolver_mock.ts", "../../../../packages/compiler/testing/src/ng_module_resolver_mock.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/compiler/testing/src/schema_registry_mock.ts", "../../../../packages/compiler/testing/src/directive_resolver_mock.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵstringify as stringify} from '@angular/core';\nimport {MetadataOverride} from '@angular/core/testing';\n\ntype StringMap = {\n  [key: string]: any\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n      metadataClass: {new (options: T): C;}, oldMetadata: C, override: MetadataOverride<T>): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => props[prop] = (<any>oldMetadata)[prop]);\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (removeValue instanceof Array) {\n      removeValue.forEach(\n          (value: any) => { removeObjects.add(_propHashKey(prop, value, references)); });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (propValue instanceof Array) {\n      metadata[prop] = propValue.filter(\n          (value: any) => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && propValue instanceof Array) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  const replacer = (key: any, value: any) => {\n    if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all APIs of the compiler package.\n *\n * <div class=\"callout is-critical\">\n *   <header>Unstable APIs</header>\n *   <p>\n *     All compiler apis are currently considered experimental and private!\n *   </p>\n *   <p>\n *     We expect the APIs in this package to keep on changing. Do not rely on them.\n *   </p>\n * </div>\n */\nexport * from './schema_registry_mock';\nexport * from './directive_resolver_mock';\nexport * from './ng_module_resolver_mock';\nexport * from './pipe_resolver_mock';\n\nimport {createPlatformFactory, ModuleWithComponentFactories, Injectable, CompilerOptions, COMPILER_OPTIONS, CompilerFactory, ComponentFactory, NgModuleFactory, Injector, NgModule, Component, Directive, Pipe, Type, PlatformRef, ɵstringify} from '@angular/core';\nimport {MetadataOverride, ɵTestingCompilerFactory as TestingCompilerFactory, ɵTestingCompiler as TestingCompiler} from '@angular/core/testing';\nimport {platformCoreDynamic, JitCompiler, DirectiveResolver, NgModuleResolver, PipeResolver, CompileMetadataResolver} from '@angular/compiler';\nimport {MockDirectiveResolver} from './directive_resolver_mock';\nimport {MockNgModuleResolver} from './ng_module_resolver_mock';\nimport {MockPipeResolver} from './pipe_resolver_mock';\nimport {MetadataOverrider} from './metadata_overrider';\n\n\nexport class TestingCompilerFactoryImpl implements TestingCompilerFactory {\n  constructor(private _compilerFactory: CompilerFactory) {}\n\n  createTestingCompiler(options: CompilerOptions[]): TestingCompiler {\n    const compiler = <JitCompiler>this._compilerFactory.createCompiler(options);\n    return new TestingCompilerImpl(\n        compiler, compiler.injector.get(MockDirectiveResolver),\n        compiler.injector.get(MockPipeResolver), compiler.injector.get(MockNgModuleResolver),\n        compiler.injector.get(CompileMetadataResolver));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: CompilerFactory, },\n];\n}\n\nexport class TestingCompilerImpl implements TestingCompiler {\n  private _overrider = new MetadataOverrider();\n  constructor(\n      private _compiler: JitCompiler, private _directiveResolver: MockDirectiveResolver,\n      private _pipeResolver: MockPipeResolver, private _moduleResolver: MockNgModuleResolver,\n      private _metadataResolver: CompileMetadataResolver) {}\n  get injector(): Injector { return this._compiler.injector; }\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    return this._compiler.compileModuleSync(moduleType);\n  }\n\n  compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    return this._compiler.compileModuleAsync(moduleType);\n  }\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    return this._compiler.compileModuleAndAllComponentsSync(moduleType);\n  }\n\n  compileModuleAndAllComponentsAsync<T>(moduleType: Type<T>):\n      Promise<ModuleWithComponentFactories<T>> {\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType);\n  }\n\n  getNgContentSelectors(component: Type<any>): string[] {\n    return this._compiler.getNgContentSelectors(component);\n  }\n\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> {\n    return this._compiler.getComponentFactory(component);\n  }\n\n  checkOverrideAllowed(type: Type<any>) {\n    if (this._compiler.hasAotSummary(type)) {\n      throw new Error(`${ɵstringify(type)} was AOT compiled, so its metadata cannot be changed.`);\n    }\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this.checkOverrideAllowed(ngModule);\n    const oldMetadata = this._moduleResolver.resolve(ngModule, false);\n    this._moduleResolver.setNgModule(\n        ngModule, this._overrider.overrideMetadata(NgModule, oldMetadata, override));\n  }\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.checkOverrideAllowed(directive);\n    const oldMetadata = this._directiveResolver.resolve(directive, false);\n    this._directiveResolver.setDirective(\n        directive, this._overrider.overrideMetadata(Directive, oldMetadata !, override));\n  }\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.checkOverrideAllowed(component);\n    const oldMetadata = this._directiveResolver.resolve(component, false);\n    this._directiveResolver.setDirective(\n        component, this._overrider.overrideMetadata(Component, oldMetadata !, override));\n  }\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.checkOverrideAllowed(pipe);\n    const oldMetadata = this._pipeResolver.resolve(pipe, false);\n    this._pipeResolver.setPipe(pipe, this._overrider.overrideMetadata(Pipe, oldMetadata, override));\n  }\n  loadAotSummaries(summaries: () => any[]) { this._compiler.loadAotSummaries(summaries); }\n  clearCache(): void { this._compiler.clearCache(); }\n  clearCacheFor(type: Type<any>) { this._compiler.clearCacheFor(type); }\n}\n\n/**\n * Platform for dynamic tests\n *\n * @experimental\n */\nexport const platformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef =\n    createPlatformFactory(platformCoreDynamic, 'coreDynamicTesting', [\n      {\n        provide: COMPILER_OPTIONS,\n        useValue: {\n          providers: [\n            MockPipeResolver,\n            {provide: PipeResolver, useExisting: MockPipeResolver},\n            MockDirectiveResolver,\n            {provide: DirectiveResolver, useExisting: MockDirectiveResolver},\n            MockNgModuleResolver,\n            {provide: NgModuleResolver, useExisting: MockNgModuleResolver},\n          ]\n        },\n        multi: true\n      },\n      {provide: TestingCompilerFactory, useClass: TestingCompilerFactoryImpl}\n    ]);\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the compiler/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, PipeResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, Pipe, Type} from '@angular/core';\n\n\nexport class MockPipeResolver extends PipeResolver {\n  private _pipes = new Map<Type<any>, Pipe>();\n\n  constructor(private _injector: Injector, refector: CompileReflector) { super(refector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(pipe: Type<any>) { this._compiler.clearCacheFor(pipe); }\n\n  /**\n   * Overrides the {@link Pipe} for a pipe.\n   */\n  setPipe(type: Type<any>, metadata: Pipe): void {\n    this._pipes.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link Pipe} for a pipe:\n   * - Set the {@link Pipe} to the overridden view when it exists or fallback to the\n   * default\n   * `PipeResolver`, see `setPipe`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): Pipe {\n    let metadata = this._pipes.get(type);\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound) !;\n    }\n    return metadata;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, NgModuleResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, NgModule, Type} from '@angular/core';\n\n\nexport class MockNgModuleResolver extends NgModuleResolver {\n  private _ngModules = new Map<Type<any>, NgModule>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  /**\n   * Overrides the {@link NgModule} for a module.\n   */\n  setNgModule(type: Type<any>, metadata: NgModule): void {\n    this._ngModules.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link NgModule} for a module:\n   * - Set the {@link NgModule} to the overridden view when it exists or fallback to the\n   * default\n   * `NgModuleResolver`, see `setNgModule`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): NgModule {\n    return this._ngModules.get(type) || super.resolve(type, throwIfNotFound) !;\n  }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementSchemaRegistry} from '@angular/compiler';\nimport {SchemaMetadata, SecurityContext} from '@angular/core';\n\nexport class MockSchemaRegistry implements ElementSchemaRegistry {\n  constructor(\n      public existingProperties: {[key: string]: boolean},\n      public attrPropMapping: {[key: string]: string},\n      public existingElements: {[key: string]: boolean}, public invalidProperties: Array<string>,\n      public invalidAttributes: Array<string>) {}\n\n  hasProperty(tagName: string, property: string, schemas: SchemaMetadata[]): boolean {\n    const value = this.existingProperties[property];\n    return value === void 0 ? true : value;\n  }\n\n  hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean {\n    const value = this.existingElements[tagName.toLowerCase()];\n    return value === void 0 ? true : value;\n  }\n\n  allKnownElementNames(): string[] { return Object.keys(this.existingElements); }\n\n  securityContext(selector: string, property: string, isAttribute: boolean): SecurityContext {\n    return SecurityContext.NONE;\n  }\n\n  getMappedPropName(attrName: string): string { return this.attrPropMapping[attrName] || attrName; }\n\n  getDefaultComponentElementName(): string { return 'ng-component'; }\n\n  validateProperty(name: string): {error: boolean, msg?: string} {\n    if (this.invalidProperties.indexOf(name) > -1) {\n      return {error: true, msg: `Binding to property '${name}' is disallowed for security reasons`};\n    } else {\n      return {error: false};\n    }\n  }\n\n  validateAttribute(name: string): {error: boolean, msg?: string} {\n    if (this.invalidAttributes.indexOf(name) > -1) {\n      return {\n        error: true,\n        msg: `Binding to attribute '${name}' is disallowed for security reasons`\n      };\n    } else {\n      return {error: false};\n    }\n  }\n\n  normalizeAnimationStyleProperty(propName: string): string { return propName; }\n  normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string|number):\n      {error: string, value: string} {\n    return {error: null !, value: val.toString()};\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CompileReflector, DirectiveResolver} from '@angular/compiler';\nimport {Compiler, Component, Directive, Injectable, Injector, Provider, Type, resolveForwardRef, ɵViewMetadata as ViewMetadata} from '@angular/core';\n\n\n\n/**\n * An implementation of {@link DirectiveResolver} that allows overriding\n * various properties of directives.\n */\n\nexport class MockDirectiveResolver extends DirectiveResolver {\n  private _directives = new Map<Type<any>, Directive>();\n  private _providerOverrides = new Map<Type<any>, any[]>();\n  private _viewProviderOverrides = new Map<Type<any>, any[]>();\n  private _views = new Map<Type<any>, ViewMetadata>();\n  private _inlineTemplates = new Map<Type<any>, string>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\n\n  resolve(type: Type<any>): Directive;\n  resolve(type: Type<any>, throwIfNotFound: true): Directive;\n  resolve(type: Type<any>, throwIfNotFound: boolean): Directive|null;\n  resolve(type: Type<any>, throwIfNotFound = true): Directive|null {\n    let metadata = this._directives.get(type) || null;\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound);\n    }\n    if (!metadata) {\n      return null;\n    }\n\n    const providerOverrides = this._providerOverrides.get(type);\n    const viewProviderOverrides = this._viewProviderOverrides.get(type);\n\n    let providers = metadata.providers;\n    if (providerOverrides != null) {\n      const originalViewProviders: Provider[] = metadata.providers || [];\n      providers = originalViewProviders.concat(providerOverrides);\n    }\n\n    if (metadata instanceof Component) {\n      let viewProviders = metadata.viewProviders;\n      if (viewProviderOverrides != null) {\n        const originalViewProviders: Provider[] = metadata.viewProviders || [];\n        viewProviders = originalViewProviders.concat(viewProviderOverrides);\n      }\n\n      let view = this._views.get(type) || metadata;\n      let animations = view.animations;\n      let templateUrl: string|undefined = view.templateUrl;\n\n      let inlineTemplate = this._inlineTemplates.get(type);\n      if (inlineTemplate) {\n        templateUrl = undefined;\n      } else {\n        inlineTemplate = view.template;\n      }\n\n      return new Component({\n        selector: metadata.selector,\n        inputs: metadata.inputs,\n        outputs: metadata.outputs,\n        host: metadata.host,\n        exportAs: metadata.exportAs,\n        moduleId: metadata.moduleId,\n        queries: metadata.queries,\n        changeDetection: metadata.changeDetection,\n        providers: providers,\n        viewProviders: viewProviders,\n        entryComponents: metadata.entryComponents,\n        template: inlineTemplate,\n        templateUrl: templateUrl,\n        animations: animations,\n        styles: view.styles,\n        styleUrls: view.styleUrls,\n        encapsulation: view.encapsulation,\n        interpolation: view.interpolation\n      });\n    }\n\n    return new Directive({\n      selector: metadata.selector,\n      inputs: metadata.inputs,\n      outputs: metadata.outputs,\n      host: metadata.host,\n      providers: providers,\n      exportAs: metadata.exportAs,\n      queries: metadata.queries\n    });\n  }\n\n  /**\n   * Overrides the {@link Directive} for a directive.\n   */\n  setDirective(type: Type<any>, metadata: Directive): void {\n    this._directives.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  setProvidersOverride(type: Type<any>, providers: Provider[]): void {\n    this._providerOverrides.set(type, providers);\n    this._clearCacheFor(type);\n  }\n\n  setViewProvidersOverride(type: Type<any>, viewProviders: Provider[]): void {\n    this._viewProviderOverrides.set(type, viewProviders);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Overrides the {@link ViewMetadata} for a component.\n   */\n  setView(component: Type<any>, view: ViewMetadata): void {\n    this._views.set(component, view);\n    this._clearCacheFor(component);\n  }\n  /**\n   * Overrides the inline template for a component - other configuration remains unchanged.\n   */\n  setInlineTemplate(component: Type<any>, template: string): void {\n    this._inlineTemplates.set(component, template);\n    this._clearCacheFor(component);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\nfunction flattenArray(tree: any[], out: Array<Type<any>|any[]>): void {\n  if (tree == null) return;\n  for (let i = 0; i < tree.length; i++) {\n    const item = resolveForwardRef(tree[i]);\n    if (Array.isArray(item)) {\n      flattenArray(item, out);\n    } else {\n      out.push(item);\n    }\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["MetadataOverrider", "prototype", "overrideMetadata", "metadataClass", "oldMetadata", "override", "props", "_valueProps", "for<PERSON>ach", "prop", "set", "remove", "add", "Error", "_angular_core", "ɵstringify", "setMetadata", "removeMetadata", "this", "_references", "addMetadata", "TestingCompilerFactoryImpl", "_compilerFactory", "createTestingCompiler", "options", "compiler", "createCompiler", "TestingCompilerImpl", "injector", "get", "MockDirectiveResolver", "MockPipeResolver", "MockNgModuleResolver", "_angular_compiler", "CompileMetadataResolver", "decorators", "type", "Injectable", "ctorParameters", "CompilerFactory", "_compiler", "_directiveResolver", "_pipeResolver", "_moduleResolver", "_metadataResolver", "Object", "defineProperty", "enumerable", "configurable", "compileModuleSync", "moduleType", "compileModuleAsync", "compileModuleAndAllComponentsSync", "compileModuleAndAllComponentsAsync", "getNgContentSelectors", "component", "getComponentFactory", "checkOverrideAllowed", "hasAotSummary", "overrideModule", "ngModule", "resolve", "setNgModule", "_overrider", "NgModule", "overrideDirective", "directive", "setDirective", "Component", "Directive", "overrideComponent", "platformCoreDynamicTesting", "createPlatformFactory", "platformCoreDynamic", "provide", "COMPILER_OPTIONS", "useValue", "providers", "PipeResolver", "useExisting", "DirectiveResolver", "_this", "_super", "call", "refector", "_injector", "throwIfNotFound", "metadata", "reflector", "Compiler", "_clearCacheFor", "clearCacheFor", "NgModuleResolver", "exports", "module", "factory", "require", "references", "removeObjects", "Set", "_loop_1", "removeValue", "Array", "value", "_propH<PERSON><PERSON><PERSON>", "_loop_2", "propValue", "filter", "has", "undefined", "addValue", "concat", "propName", "replacer", "key", "_serializeReference", "JSON", "stringify", "id", "ref", "_nextReferenceId", "obj", "startsWith", "push", "proto", "keys", "protoProp", "extendStatics", "setPrototypeOf", "__proto__", "d", "b", "p", "hasOwnProperty", "MockSchemaRegistry", "existingProperties", "attrPropMapping", "existingElements", "invalidProperties", "invalidAttributes", "hasProperty", "tagName", "property", "schemas", "hasElement", "schemaMetas", "toLowerCase", "allKnownElementNames", "securityContext", "selector", "isAttribute", "SecurityContext", "NONE", "getMappedPropName", "attrName", "getDefaultComponentElementName", "validateProperty", "name", "indexOf", "error", "msg", "validateAttribute", "normalizeAnimationStyleProperty", "_providerOverrides", "Map", "_viewProviderOverrides", "_views", "__extends", "_directives", "providerOverrides", "viewProviderOverrides", "originalViewProviders", "viewProviders", "view", "animations", "templateUrl", "inlineTemplate", "_inlineTemplates", "template", "inputs", "outputs", "host", "exportAs", "moduleId", "queries", "changeDetection", "entryComponents", "styles", "styleUrls", "encapsulation", "interpolation", "setInlineTemplate"], "mappings": ";;;;;0BKAA,gBAAA6F,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,iBAAAA,QAAA,qBAAAA,QAAA,ukBLwDA,QAAA/E,gBAAAsE,SAAA5E,OAAAsF,YAEA,GAAAC,eAAA,GAAAC,KACAC,QAAA,SAAA3F,MACA,GAAQ4F,aAAR1F,OAAAF,KACA4F,uBAAAC,OAEAD,YAAA7F,QAAA,SAAA+F,OAAAL,cAAAtF,IAAA4F,aAAA/F,KAAA8F,MAAAN,eAAAC,cAAAtF,IAAA4F,aAAA/F,KAAA4F,YAAAJ,aAEA,KAAA,GAAAxF,QAAAE,QACAyF,QAAA3F,KAEA,IAAAgG,SAAA,SAAAhG,MACA,GAAAiG,WAAAnB,SAAA9E,KAEAiG,qBAAAJ,OACAf,SAAA9E,MAAAiG,UAAAC,OAAA,SAAAJ,OAAA,OAAAL,cAAAU,IAAAJ,aAAA/F,KAAA8F,MAAAN,eATAC,cAAAU,IAAAJ,aAAA/F,KAAAiG,UAAAT,eAAAV,SAAA9E,MAAAoG,QAcA,KAAK,GAALpG,QAAA8E,UAAAkB,QAAAhG,MAGA,QAAAW,aAAAmE,SAAA3E,KACA,IAAA,GAAAH,QAAAG,KAAA,CAEA,GAAAkG,UAAAlG,IAAAH,MACAiG,UAAAnB,SAAA9E,KACA,OAAAiG,WAAAA,oBAAAJ,OACAf,SAAA9E,MAAAiG,UAAAK,OAAAD,UAIAvB,SAAA9E,MAAAqG,UAIA,QAAA9F,aAAAuE,SAAA7E,KACA,IAAA,GAAAD,QAAAC,KAEA6E,SAAA9E,MAAAC,IAAAD,MAIA,QAAA+F,cAAAQ,SAAAN,UAAAT,YACA,GAAAgB,UAAA,SAAAC,IAAAX,OAIA,MAHA,kBAAAA,SACAA,MAAAY,oBAAAZ,MAAAN,aAEAM,MAIA,OAAAS,UAAA,IAAAI,KAAAC,UAAAX,UAAAO,uDAGE,GAAFK,IAAArB,WAAApE,IAAA0F,WACAD,MACAA,GAAA,GAAAxG,cAAAC,WAAAwG,KAAAC,mBACAvB,WAAAvF,IAAA6G,IAAAD,QAKA,QAAA/G,aAAAkH,KACA,GAAAnH,kDAGAG,KAAAiH,WAAA,MACApH,MAAAqH,KAAAlH,YAIA,GAAAmH,OAAAH,wCClIA5E,OAAAgF,KAAAD,OAAApH,QAAA,SAAAsH,0JIqBA,GAAIC,eAAJlF,OAAAmF,iBACAC,uBAAA3B,QAA2C,SAA3C4B,EAAAC,GAAAD,EAAAD,UAAAE,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,KCHAE,mBAAA,WACA,QAAAA,oBAAAC,mBAAAC,gBAAAC,iBAAAC,kBAAAC,mBAEAzH,KAAAqH,mBAAAA,mBACArH,KAAAsH,gBAAAA,gBACItH,KAAJuH,iBAAAA,iBACAvH,KAAAwH,kBAAAA,kBAEAxH,KAAAyH,kBAAAA,wBAGAL,oBAAArI,UAAA2I,YAAA,SAAAC,QAAAC,SAAAC,SACA,GAAAxC,OAAArF,KAAAqH,mBAAAO,SAEA,OAAA,UAAAvC,OAAAA,OAIE+B,mBAAFrI,UAAA+I,WAAA,SAAAH,QAAAI,aACI,GAAJ1C,OAAArF,KAAAuH,iBAAAI,QAAAK,cACA,OAAA,UAAA3C,OAAAA,OACA+B,mBAAArI,UAAAkJ,qBAAA,WAAA,MAAAtG,QAAAgF,KAAA3G,KAAAuH,mBACAH,mBAAArI,UAAAmJ,gBAAA,SAAAC,SAAAP,SAAAQ,aACA,MAAAxI,eAAAyI,gBAAAC,MAGAlB,mBAAArI,UAAAwJ,kBAAA,SAAAC,UAAA,MAAAxI,MAAAsH,gBAAAkB,WAAAA,UACApB,mBAAArI,UAAA0J,+BAAA,WAAA,MAAA,gBACArB,mBAAArI,UAAA2J,iBAAA,SAAAC,MACA,MAAA3I,MAAAwH,kBAAAoB,QAAAD,OAAA,GACAE,OAAA,EAAAC,IAAA,wBAAAH,KAAA,yCAEAE,OAAA,IAGAzB,mBAAArI,UAAAgK,kBAAA,SAAAJ,MAEA,MAAA3I,MAAAyH,kBAAAmB,QAAAD,OAAA,GAGAE,OAAA,EACAC,IAAA,yBAAAH,KAAA,oDA9CAvB,mBAAArI,UAAAiK,gCAAA,SAAAlD,UAAA,MAAAA,iLCSAlF,sBAAA,SAAAoD,QAIA,QAAApD,uBAAAuD,UAAAG,WAKA,GAAAP,OAAAC,OAAAC,KAAAjE,KAAAsE,YAAAtE,WACI+D,OAAJI,UAAAA,oCACAJ,MAAAkF,mBAAA,GAAAC,KACAnF,MAAAoF,uBAAA,GAAAD,KAAAnF,MAAAqF,OAAA,GAAAF,gDAVAG,WAAAzI,sBAAAoD,QAWArC,OAAAC,eAAAhB,sBAAA7B,UAAA,aACA4B,IAAA,WAAA,MAAAX,MAAAmE,UAAAxD,IAAAf,cAAA2E,WAAA1C,YAAA,EACAC,cAAA,IAGAlB,sBAAA7B,UAAAyF,eAAA,SAAAnC,WAAArC,KAAAsB,UAAAmD,cAAApC,YACAzB,sBAAA7B,UAAA4D,QAAA,SAAAzB,KAAAkD,iBAEA,SAAQA,kBAARA,iBAAA,EACA,IAAQC,UAARrE,KAAAsJ,YAAA3I,IAAAO,OAAA,IAKI,IAJJmD,WACAA,SAAAL,OAAAjF,UAAA4D,QAAAsB,KAAAjE,KAAAkB,KAAAkD,mBAGAC,SACM,MAAN,KAEA,IAAAkF,mBAAAvJ,KAAAiJ,mBAAAtI,IAAAO,MACAsI,sBAAAxJ,KAAAmJ,uBAAAxI,IAAAO,MACAyC,UAAAU,SAAAV,SAEA,IAAA,MAAM4F,kBAAN,CACM,GAAIE,uBAAVpF,SAAAV,aACAA,WAAA8F,sBAAA5D,OAAA0D,mBAGA,GAAAlF,mBAAAzE,eAAAsD,UAAA,CACA,GAAQwG,eAARrF,SAAAqF,aACA,IAAA,MAAAF,sBAAA,CAAA,GAAAC,uBAAApF,SAAAqF,iBACQA,eAARD,sBAAA5D,OAAA2D,uBAGA,GAAAG,MAAA3J,KAAAoJ,OAAAzI,IAAAO,OAAAmD,SACAuF,WAAAD,KAAAC,WACAC,YAAAF,KAAAE,YACQC,eAAiB9J,KAAzB+J,iBAAApJ,IAAAO,KAOA,OANA4I,gBACAD,YAAAlE,OAGQmE,eAAiBH,KAAzBK,SAEA,GAAApK,eAAAsD,WACQiF,SAAR9D,SAAA8D,SACQ8B,OAAR5F,SAAA4F,OACQC,QAAR7F,SAAA6F,QACQC,KAAR9F,SAAA8F,KACQC,SAAR/F,SAAA+F,SACQC,SAARhG,SAAAgG,SACQC,QAARjG,SAAAiG,QACQC,gBAARlG,SAAAkG,gBACA5G,UAAAA,UACA+F,cAAAA,cAEAc,gBAAAnG,SAAAmG,gBACAR,SAAAF,eACAD,YAAuBA,YACvBD,WAAAA,WACAa,OAAAd,KAAAc,OACAC,UAAAf,KAAAe,UACAC,cAAyBhB,KAAzBgB,cACAC,cAAAjB,KAAAiB,sGAOAV,QAAA7F,SAAA6F,QACAC,KAAA9F,SAAA8F,gFAWAvJ,sBAAA7B,UAAAkE,aAAA,SAAA/B,KAAAmD,UACArE,KAAAsJ,YAAA9J,IAAA0B,KAAAmD,mHAKArE,KAAAiJ,mBAAAzJ,IAAA0B,KAAAyC,WACA3D,KAAAwE,eAAAtD,6QAUAlB,KAAAwE,eAAAnC,YAIAzB,sBAAA7B,UAAA8L,kBAAA,SAAAxI,UAAA2H,UACAhK,KAAA+J,iBAAAvK,IAAA6C,UAAA2H,UACAhK,KAAAwE,eAAAnC,mCH5IAtB,kBAAA+C;;;;;;;AAoBA,GAAAhD,sBAAA,SAAAkD,QACA,QAAAlD,sBAAAqD,UAAAG,sSAmBAxD,qBAAA/B,UAAA4D,QAAA,SAAAzB,KAAAkD,8NAEAzD,IAAA,WAAA,MAAAX,MAAAmE,UAAAxD,IAAAf,cAAA2E,WAAA1C,YAAA,EACAC,cAAA,IAEAhB,qBAAA/B,UAAAyF,eAAA,SAAAnC,WAAArC,KAAAsB,UAAAmD,cAAApC,kCD7CAtB,kBAAA2D;;;;;;;oFAwBA,GAAAX,OAAAC,OAAAC,KAAAjE,KAAAkE,WAAAlE,WAAA+D,OAAAI,UAAAA,+EACAxC,OAAAC,eAAAf,iBAAA9B,UAAA,iUAkBA8B,iBAAA9B,UAAA4D,QAAA,SAAAzB,KAAAkD,iBACA,SAAAA,kBAAAA,iBAAA,qCAIA,OAFAC,YAAAA,SAAAL,OAAAjF,UAAA4D,QAAAsB,KAAAjE,KAAAkB,KAAAkD,kBAEAC,4BHhDAtD,kBAAA6C;;;;;;;0GAsDA,MAnBA9E,mBAAAC,UAAAC,iBAAA,SAAAC,cAAAC,YAAAC,UACA,GAAQC,SAIR,IAHAF,aACAG,YAAAH,aAAAI,QAAA,SAAAC,MAAA,MAAAH,OAAAG,MAAAL,YAAAK,QAEAJ,SAAAK,IAAA,CACA,GAAAL,SAAAM,QAAAN,SAAAO,IACA,KAAA,IAAAC,OAA4B,6BAA5BC,cAAAC,WAAAZ,eAAA,qBAEAa,aAAAV,MAAAD,SAAAK,KASA,MANAL,UAAAM,QACAM,eAAAX,MAAAD,SAAAM,OAAAO,KAAAC,aAEAd,SAAAO,KAAAQ,YAAAd,MAAAD,SAAAO,KAGA,GAAAT,eAAAG,QACAN,qBCNAqB,2BAAA,WACA,QAAAA,4BAAAC,+DAEAD,4BAAApB,UAAAsB,sBAAA,SAAAC,SACA,GAAAC,UAAAP,KAAAI,iBAAAI,eAAAF,QACA,OAAA,IAAAG,qBAAAF,SAAAA,SAAAG,SAAAC,IAAAC,uBAAAL,SAAAG,SAAAC,IAAAE,kBAAAN,SAAAG,SAAAC,IAAAG,sBAAAP,SAAAG,SAAAC,IAAAI,kBAAAC,wDAMAb,4BAAAc,aACAC,KAAAtB,cAAAuB,aAHAhB,2BAAAiB,eAAA,WAAA,QAIAF,KAAAtB,cAAAyB,kBAGA,IAAAZ,qBAAA,WACA,QAAAA,qBAAAa,UAAAC,mBAAAC,cAAAC,gBAAAC,mBACA1B,KAAAsB,UAAAA,UAEAtB,KAAAuB,mBAAAA,mBACAvB,KAAAwB,cAAAA,cAAAxB,KAAAyB,gBAAAA,qGACAE,QAAAC,eAAAnB,oBAAA1B,UAAA,YACA4B,IAAA,WAAA,MAAAX,MAAAsB,UAAAZ,UACAmB,YAAA,EACAC,cAAA,IAIArB,oBAAA1B,UAAAgD,kBAAA,SAAAC,YACA,MAAAhC,MAAAsB,UAAAS,kBAAAC,aAGAvB,oBAAA1B,UAAAkD,mBAAA,SAAAD,YACA,MAAAhC,MAAAsB,UAAAW,mBAAAD,aAGAvB,oBAAA1B,UAAAmD,kCAAA,SAAAF,YACA,MAAAhC,MAAAsB,UAAAY,kCAAAF,aAGAvB,oBAAA1B,UAAAoD,mCAAA,SAAAH,YACA,MAAAhC,MAAAsB,UAAAa,mCAAAH,aAEAvB,oBAAA1B,UAAAqD,sBAAA,SAAAC,WAEA,MAAArC,MAAAsB,UAAAc,sBAAAC,YAEA5B,oBAAA1B,UAAAuD,oBAAA,SAAAD,WACA,MAAArC,MAAAsB,UAAAgB,oBAAAD,YAGE5B,oBAAF1B,UAAAwD,qBAAA,SAAArB,MACI,GAAIlB,KAARsB,UAAAkB,cAAAtB,MACU,KAAV,IAAAvB,OAAAC,cAAAC,WAAAqB,MAAA,0DAIET,oBAAF1B,UAAA0D,eAAmB,SAAnBC,SAAAvD,UACIa,KAAKuC,qBAAqBG,SAC1B,IAAMxD,aAAcc,KAAKyB,gBAA7BkB,QAAAD,UAAA,EACI1C,MAAKyB,gBAATmB,YAAAF,SAAA1C,KACmB6C,WADnB7D,iBAAAY,cAAAkD,SAAA5D,YAAAC,YAGEsB,oBAAF1B,UAAAgE,kBAAE,SAAFC,UAAA7D,UACIa,KAAKuC,qBAAqBS,UAC1B,IAAM9D,aAAcc,KAAKuB,mBAA7BoB,QAAAK,WAAA,EACIhD,MAAKuB,mBAAT0B,aAAyCD,UAAWhD,KAApD6C,WAAA7D,iBAAAkE,cAAAC,UAAAjE,YAAAC,YAEEsB,oBAAF1B,UAAAqE,kBAAA,SAAAf,UAAAlD,UACAa,KAAAuC,qBAAsCF,UACtC,IAAAnD,aAAAc,KAAAuB,mBAAAoB,QAAAN,WAAA,EACArC,MAAAuB,mBAAA0B,aAAAZ,UAAArC,KAAA6C,WAAA7D,iBAAAY,cAAAsD,UAAAhE,YAAAC,qkBAkBAkE,2BAAAzD,cAAA0D,sBAAAvC,kBAAAwC,oBAAA,uBAEAC,QAAA5D,cAAA6D,iBACAC,UACAC,WACA9C,kBACA2C,QAAAzC,kBAAA6C,aAAAC,YAAAhD,yCChJA2C,QAAAzC,kBAAA+C,kBAAAD,YAAAjD"}