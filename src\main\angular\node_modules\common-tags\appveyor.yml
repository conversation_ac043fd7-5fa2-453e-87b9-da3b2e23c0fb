version: "{build}"
environment:
  matrix:
    - nodejs_version: "6"
    - nodejs_version: "5"
platform:
  - x86
  - x64
install:
  - ps: Install-Product node $env:nodejs_version $env:platform
  - set CI=true
  - set AVA_APPVEYOR=true
  - npm install -g npm@latest || (timeout 30 && npm install -g npm@latest)
  - set PATH=%APPDATA%\npm;%PATH%
  - npm install || (timeout 30 && npm install)
test_script:
  # Output useful info for debugging.
  - node --version && npm --version
  - cmd: npm run test-ci
build: off
shallow_clone: true
clone_depth: 1
matrix:
  fast_finish: true
cache:
  - node_modules -> package.json # local npm modules
