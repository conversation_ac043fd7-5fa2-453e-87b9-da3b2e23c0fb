{"_args": [["cipher-base@1.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cipher-base@1.0.3", "_id": "cipher-base@1.0.3", "_inBundle": false, "_integrity": "sha1-7qvxlEGc6QDaMBjCB9IS8qbfCgc=", "_location": "/cipher-base", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cipher-base@1.0.3", "name": "cipher-base", "escapedName": "cipher-base", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/browserify-aes", "/browserify-des", "/create-hash", "/create-hmac"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cipher-base/-/cipher-base-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/crypto-browserify/cipher-base/issues"}, "dependencies": {"inherits": "^2.0.1"}, "description": "abstract base class for crypto-streams", "devDependencies": {"tap-spec": "^4.1.0", "tape": "^4.2.0"}, "homepage": "https://github.com/crypto-browserify/cipher-base#readme", "keywords": ["cipher", "stream"], "license": "MIT", "main": "index.js", "name": "cipher-base", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/cipher-base.git"}, "scripts": {"test": "node test.js | tspec"}, "version": "1.0.3"}