[{"__symbolic": "module", "version": 3, "metadata": {"MockAnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"log": []}}, "MockAnimationPlayer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations", "name": "NoopAnimationPlayer"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 45}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "onInit": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "triggerMicrotask": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MockAnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"log": []}}, "MockAnimationPlayer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations", "name": "NoopAnimationPlayer"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 45}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "onInit": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "triggerMicrotask": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}]}}}}]