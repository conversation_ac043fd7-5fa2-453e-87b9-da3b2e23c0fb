# ansi-gray [![NPM version](https://badge.fury.io/js/ansi-gray.svg)](http://badge.fury.io/js/ansi-gray)

> The color gray, in ansi.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i ansi-gray --save
```

## Usage

```js
var gray = require('ansi-gray');
```

## Related projects

* [ansi-reset](https://github.com/jonschlinkert/ansi-reset)
* [ansi-bold](https://github.com/jonschlinkert/ansi-bold)
* [ansi-dim](https://github.com/jonschlinkert/ansi-dim)
* [ansi-italic](https://github.com/jonschlinkert/ansi-italic)
* [ansi-underline](https://github.com/jonschlinkert/ansi-underline)
* [ansi-inverse](https://github.com/jonschlinkert/ansi-inverse)
* [ansi-hidden](https://github.com/jonschlinkert/ansi-hidden)
* [ansi-strikethrough](https://github.com/jonschlinkert/ansi-strikethrough)
* [ansi-black](https://github.com/jonschlinkert/ansi-black)
* [ansi-red](https://github.com/jonschlinkert/ansi-red)
* [ansi-green](https://github.com/jonschlinkert/ansi-green)
* [ansi-yellow](https://github.com/jonschlinkert/ansi-yellow)
* [ansi-blue](https://github.com/jonschlinkert/ansi-blue)
* [ansi-magenta](https://github.com/jonschlinkert/ansi-magenta)
* [ansi-cyan](https://github.com/jonschlinkert/ansi-cyan)
* [ansi-white](https://github.com/jonschlinkert/ansi-white)
* [ansi-gray](https://github.com/jonschlinkert/ansi-gray)
* [ansi-grey](https://github.com/jonschlinkert/ansi-grey)
* [ansi-bgblack](https://github.com/jonschlinkert/ansi-bgblack)
* [ansi-bgred](https://github.com/jonschlinkert/ansi-bgred)
* [ansi-bggreen](https://github.com/jonschlinkert/ansi-bggreen)
* [ansi-bgyellow](https://github.com/jonschlinkert/ansi-bgyellow)
* [ansi-bgblue](https://github.com/jonschlinkert/ansi-bgblue)
* [ansi-bgmagenta](https://github.com/jonschlinkert/ansi-bgmagenta)
* [ansi-bgcyan](https://github.com/jonschlinkert/ansi-bgcyan)
* [ansi-bgwhite](https://github.com/jonschlinkert/ansi-bgwhite)

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/ansi-gray/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on May 21, 2015._