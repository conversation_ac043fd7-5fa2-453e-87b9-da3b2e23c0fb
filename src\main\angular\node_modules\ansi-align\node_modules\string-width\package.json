{"_args": [["string-width@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "string-width@1.0.2", "_id": "string-width@1.0.2", "_inBundle": false, "_integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "_location": "/ansi-align/string-width", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "string-width@1.0.2", "name": "string-width", "escapedName": "string-width", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/ansi-align"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/string-width/-/string-width-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "description": "Get the visual width of a string - the number of columns required to display it", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/string-width#readme", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "license": "MIT", "name": "string-width", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}