# Bytes utility

[![NPM Version][npm-image]][npm-url]
[![NPM Downloads][downloads-image]][downloads-url]
[![Build Status][travis-image]][travis-url]

Utility to parse a string bytes (ex: `1TB`) to bytes (`1099511627776`) and vice-versa.

## Usage

```js
var bytes = require('bytes');
```

#### bytes.format(number value, [options]): string|null

Format the given value in bytes into a string. If the value is negative, it is kept as such. If it is a float, it is
 rounded.

**Arguments**

| Name    | Type   | Description        |
|---------|--------|--------------------|
| value   | `number` | Value in bytes     |
| options | `Object` | Conversion options |

**Options**

| Property          | Type   | Description                                                                             |
|-------------------|--------|-----------------------------------------------------------------------------------------|
| decimalPlaces | `number`&#124;`null` | Maximum number of decimal places to include in output. Default value to `2`. |
| fixedDecimals | `boolean`&#124;`null` | Whether to always display the maximum number of decimal places. Default value to `false` |
| thousandsSeparator | `string`&#124;`null` | Example of values: `' '`, `','` and `.`... Default value to `' '`. |
| unitSeparator | `string`&#124;`null` | Separator to use between number and unit. Default value to `''`. |

**Returns**

| Name    | Type        | Description             |
|---------|-------------|-------------------------|
| results | `string`&#124;`null` | Return null upon error. String value otherwise. |

**Example**

```js
bytes(1024);
// output: '1kB'

bytes(1000);
// output: '1000B'

bytes(1000, {thousandsSeparator: ' '});
// output: '1 000B'

bytes(1024 * 1.7, {decimalPlaces: 0});
// output: '2kB'

bytes(1024, {unitSeparator: ' '});
// output: '1 kB'

```

#### bytes.parse(string value): number|null

Parse the string value into an integer in bytes. If no unit is given, it is assumed the value is in bytes.

Supported units and abbreviations are as follows and are case-insensitive:

  * "b" for bytes
  * "kb" for kilobytes
  * "mb" for megabytes
  * "gb" for gigabytes
  * "tb" for terabytes

The units are in powers of two, not ten. This means 1kb = 1024b according to this parser.

**Arguments**

| Name          | Type   | Description        |
|---------------|--------|--------------------|
| value   | `string` | String to parse.   |

**Returns**

| Name    | Type        | Description             |
|---------|-------------|-------------------------|
| results | `number`&#124;`null` | Return null upon error. Value in bytes otherwise. |

**Example**

```js
bytes('1kB');
// output: 1024

bytes('1024');
// output: 1024
```

## Installation

```bash
npm install bytes --save
component install visionmedia/bytes.js
```

## License 

[![npm](https://img.shields.io/npm/l/express.svg)](https://github.com/visionmedia/bytes.js/blob/master/LICENSE)

[downloads-image]: https://img.shields.io/npm/dm/bytes.svg
[downloads-url]: https://npmjs.org/package/bytes
[npm-image]: https://img.shields.io/npm/v/bytes.svg
[npm-url]: https://npmjs.org/package/bytes
[travis-image]: https://img.shields.io/travis/visionmedia/bytes.js/master.svg
[travis-url]: https://travis-ci.org/visionmedia/bytes.js
