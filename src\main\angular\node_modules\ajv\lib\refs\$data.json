{"$schema": "http://json-schema.org/draft-06/schema#", "$id": "https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#", "description": "Meta-schema for $data reference (JSON-schema extension proposal)", "type": "object", "required": ["$data"], "properties": {"$data": {"type": "string", "anyOf": [{"format": "relative-json-pointer"}, {"format": "json-pointer"}]}}, "additionalProperties": false}