{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/platform-browser/testing/index.ts", "../../../../../packages/platform-browser/testing/src/testing.ts", "../../../../../packages/platform-browser/testing/src/browser.ts", "../../../../../packages/platform-browser/testing/src/browser_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\nexport * from './browser';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {APP_ID, NgModule, NgZone, PLATFORM_INITIALIZER, PlatformRef, Provider, createPlatformFactory, platformCore} from '@angular/core';\nimport {BrowserModule, ɵBrowserDomAdapter as BrowserDomAdapter, ɵELEMENT_PROBE_PROVIDERS as ELEMENT_PROBE_PROVIDERS} from '@angular/platform-browser';\nimport {BrowserDetection, createNgZone} from './browser_util';\n\nfunction initBrowserTests() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserDetection.setup();\n}\n\nconst _TEST_BROWSER_PLATFORM_PROVIDERS: Provider[] =\n    [{provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true}];\n\n/**\n * Platform for testing\n *\n * @stable\n */\nexport const platformBrowserTesting =\n    createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @stable\n */\n\nexport class BrowserTestingModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    ELEMENT_PROBE_PROVIDERS,\n    {provide: NgZone, useFactory: createNgZone},\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone, ɵglobal as global} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\n\nexport let browserDetection: BrowserDetection;\n\nexport class BrowserDetection {\n  private _overrideUa: string|null;\n  private get _ua(): string {\n    if (typeof this._overrideUa === 'string') {\n      return this._overrideUa;\n    }\n\n    return getDOM() ? getDOM().getUserAgent() : '';\n  }\n\n  static setup() { browserDetection = new BrowserDetection(null); }\n\n  constructor(ua: string|null) { this._overrideUa = ua; }\n\n  get isFirefox(): boolean { return this._ua.indexOf('Firefox') > -1; }\n\n  get isAndroid(): boolean {\n    return this._ua.indexOf('Mozilla/5.0') > -1 && this._ua.indexOf('Android') > -1 &&\n        this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Chrome') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isEdge(): boolean { return this._ua.indexOf('Edge') > -1; }\n\n  get isIE(): boolean { return this._ua.indexOf('Trident') > -1; }\n\n  get isWebkit(): boolean {\n    return this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Edge') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isIOS7(): boolean {\n    return (this._ua.indexOf('iPhone OS 7') > -1 || this._ua.indexOf('iPad OS 7') > -1) &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isSlow(): boolean { return this.isAndroid || this.isIE || this.isIOS7; }\n\n  // The Intl API is only natively supported in Chrome, Firefox, IE11 and Edge.\n  // This detector is needed in tests to make the difference between:\n  // 1) IE11/Edge: they have a native Intl API, but with some discrepancies\n  // 2) IE9/IE10: they use the polyfill, and so no discrepancies\n  get supportsNativeIntlApi(): boolean {\n    return !!(<any>global).Intl && (<any>global).Intl !== (<any>global).IntlPolyfill;\n  }\n\n  get isChromeDesktop(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Mobile Safari') == -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n\n  // \"Old Chrome\" means Chrome 3X, where there are some discrepancies in the Intl API.\n  // Android 4.4 and 5.X have such browsers by default (respectively 30 and 39).\n  get isOldChrome(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Chrome/3') > -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n}\n\nBrowserDetection.setup();\n\nexport function dispatchEvent(element: any, eventType: any): void {\n  getDOM().dispatchEvent(element, getDOM().createEvent(eventType));\n}\n\nexport function el(html: string): HTMLElement {\n  return <HTMLElement>getDOM().firstChild(getDOM().content(getDOM().createTemplate(html)));\n}\n\nexport function normalizeCSS(css: string): string {\n  return css.replace(/\\s+/g, ' ')\n      .replace(/:\\s/g, ':')\n      .replace(/'/g, '\"')\n      .replace(/ }/g, '}')\n      .replace(/url\\((\\\"|\\s)(.+)(\\\"|\\s)\\)(\\s*)/g, (...match: string[]) => `url(\"${match[2]}\")`)\n      .replace(/\\[(.+)=([^\"\\]]+)\\]/g, (...match: string[]) => `[${match[1]}=\"${match[2]}\"]`);\n}\n\nconst _singleTagWhitelist = ['br', 'hr', 'input'];\nexport function stringifyElement(el: any /** TODO #9100 */): string {\n  let result = '';\n  if (getDOM().isElementNode(el)) {\n    const tagName = getDOM().tagName(el).toLowerCase();\n\n    // Opening tag\n    result += `<${tagName}`;\n\n    // Attributes in an ordered way\n    const attributeMap = getDOM().attributeMap(el);\n    const keys: string[] = Array.from(attributeMap.keys()).sort();\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const attValue = attributeMap.get(key);\n      if (typeof attValue !== 'string') {\n        result += ` ${key}`;\n      } else {\n        result += ` ${key}=\"${attValue}\"`;\n      }\n    }\n    result += '>';\n\n    // Children\n    const childrenRoot = getDOM().templateAwareRoot(el);\n    const children = childrenRoot ? getDOM().childNodes(childrenRoot) : [];\n    for (let j = 0; j < children.length; j++) {\n      result += stringifyElement(children[j]);\n    }\n\n    // Closing tag\n    if (_singleTagWhitelist.indexOf(tagName) == -1) {\n      result += `</${tagName}>`;\n    }\n  } else if (getDOM().isCommentNode(el)) {\n    result += `<!--${getDOM().nodeValue(el)}-->`;\n  } else {\n    result += getDOM().getText(el);\n  }\n\n  return result;\n}\n\nexport function createNgZone(): NgZone {\n  return new NgZone({enableLongStackTrace: true});\n}\n"], "names": ["ELEMENT_PROBE_PROVIDERS", "BrowserDomAdapter", "global", "getDOM"], "mappings": ";;;AGAA;;;;;;;AAQA,AACA,AAEA,AAAO,IAAI,gBAAkC,CAAC;AAE9C,AAAA,MAAA,gBAAA,CAAA;IAEE,IAAY,GAAG,GAAjB;QACI,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;YACxC,OAAO,IAAI,CAAC,WAAW,CAAC;SACzB;QAED,OAAOG,OAAM,EAAE,GAAGA,OAAM,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC;KAChD;IAED,OAAO,KAAK,GAAd,EAAmB,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE;IAEjE,WAAF,CAAc,EAAe,EAA7B,EAAiC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE;IAEvD,IAAI,SAAS,GAAf,EAA6B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAErE,IAAI,SAAS,GAAf;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC3E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KACxC;IAED,IAAI,MAAM,GAAZ,EAA0B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAE/D,IAAI,IAAI,GAAV,EAAwB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAEhE,IAAI,QAAQ,GAAd;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KACxC;IAED,IAAI,MAAM,GAAZ;QACI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC9E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KACxC;IAED,IAAI,MAAM,GAAZ,EAA0B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;;;;;IAM5E,IAAI,qBAAqB,GAA3B;QACI,OAAO,CAAC,CAAOD,OAAO,CAAC,IAAI,IAAUA,OAAO,CAAC,IAAI,KAAWA,OAAO,CAAC,YAAY,CAAC;KAClF;IAED,IAAI,eAAe,GAArB;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KACpC;;;IAID,IAAI,WAAW,GAAjB;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACvE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KACpC;CACF;AAED,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAEzB,AAAA,AAEC;AAED,AAAA,AAEC;AAED,AAAA,AAOC;AAED,AACA,AAAA,AAwCC;AAED,AAAA,SAAA,YAAA,GAAA;IACE,OAAO,IAAI,MAAM,CAAC,EAAC,oBAAoB,EAAE,IAAI,EAAC,CAAC,CAAC;CACjD;;ADxID;;;;;;;AAOA,AACA,AACA,AAEA,SAAA,gBAAA,GAAA;IACED,kBAAiB,CAAC,WAAC,EAAW,CAAE;IAChC,gBAAgB,CAAC,KAAC,EAAK,CAAE;CAC1B;AAED,MAAM,gCAAA,GACF,CAAC,EAAC,OAAC,EAAQ,oBAAA,EAAsB,QAAA,EAAU,gBAAA,EAAkB,KAAA,EAAO,IAAA,EAAK,CAAC,CAAC;;;;;;AAO/E,AAAO,MAAM,sBAAA,GACT,qBAAqB,CAAC,YAAC,EAAa,gBAAA,EAAkB,gCAAA,CAAiC,CAAC;;;;;;AAQ5F,AAAA,MAAA,oBAAA,CAAA;;AACO,oBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;gBACvB,OAAO,EAAE,CAAC,aAAa,CAAC;gBACxB,SAAS,EAAE;oBACT,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;oBAChCD,wBAAuB;oBACvB,EAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAC;iBAC5C;aACF,EAAG,EAAE;CACL,CAAC;;AAEK,oBAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;;AD9CF;;;;;;;;;;;GAYG,AACH,AAA0B;;ADb1B;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}