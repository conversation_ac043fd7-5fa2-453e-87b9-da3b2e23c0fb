[{"__symbolic": "module", "version": 3, "metadata": {"ParseLocation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSourceFile"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}], "toString": [{"__symbolic": "method"}], "moveBy": [{"__symbolic": "method"}], "getContext": [{"__symbolic": "method"}]}}, "ParseSourceFile": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "ParseSourceSpan": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseLocation"}, {"__symbolic": "reference", "name": "ParseLocation"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "ParseErrorLevel": {"WARNING": 0, "ERROR": 1}, "ParseError": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 118, "character": 20, "context": {"typeName": "ParseErrorLevel"}}]}], "toString": [{"__symbolic": "method"}]}}, "typeSourceSpan": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"ParseLocation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSourceFile"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}], "toString": [{"__symbolic": "method"}], "moveBy": [{"__symbolic": "method"}], "getContext": [{"__symbolic": "method"}]}}, "ParseSourceFile": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "ParseSourceSpan": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseLocation"}, {"__symbolic": "reference", "name": "ParseLocation"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "ParseErrorLevel": {"WARNING": 0, "ERROR": 1}, "ParseError": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 118, "character": 20, "context": {"typeName": "ParseErrorLevel"}}]}], "toString": [{"__symbolic": "method"}]}}, "typeSourceSpan": {"__symbolic": "function"}}}]