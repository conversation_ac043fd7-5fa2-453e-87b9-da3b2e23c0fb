{"_args": [["commander@2.9.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "commander@2.9.0", "_id": "commander@2.9.0", "_inBundle": false, "_integrity": "sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q=", "_location": "/commander", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "commander@2.9.0", "name": "commander", "escapedName": "commander", "rawSpec": "2.9.0", "saveSpec": null, "fetchSpec": "2.9.0"}, "_requiredBy": ["/html-minifier", "/seek-bzip", "/uglify-js"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/commander/-/commander-2.9.0.tgz", "_spec": "2.9.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dependencies": {"graceful-readlink": ">= 1.0.0"}, "description": "the complete solution for node.js command-line programs", "devDependencies": {"should": ">= 0.0.1", "sinon": ">=1.17.1"}, "engines": {"node": ">= 0.6.x"}, "files": ["index.js"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["command", "option", "parser"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"test": "make test"}, "version": "2.9.0"}