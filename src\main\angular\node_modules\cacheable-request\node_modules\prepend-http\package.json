{"_args": [["prepend-http@2.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "prepend-http@2.0.0", "_id": "prepend-http@2.0.0", "_inBundle": false, "_integrity": "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=", "_location": "/cacheable-request/prepend-http", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "prepend-http@2.0.0", "name": "prepend-http", "escapedName": "prepend-http", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/cacheable-request/normalize-url"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/prepend-http/-/prepend-http-2.0.0.tgz", "_spec": "2.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/prepend-http/issues"}, "description": "Prepend `http://` to humanized URLs like todomvc.com and localhost", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/prepend-http#readme", "keywords": ["prepend", "protocol", "scheme", "url", "uri", "http", "https", "humanized"], "license": "MIT", "name": "prepend-http", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/prepend-http.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}