{"_args": [["@angular/core@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/core@4.2.5", "_id": "@angular/core@4.2.5", "_inBundle": false, "_integrity": "sha1-YcG1iFwmIzLXN/vg9dcRUXWahGQ=", "_location": "/@angular/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/core@4.2.5", "name": "@angular/core", "escapedName": "@angular%2fcore", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/", "/angular-svg-round-progressbar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/core/-/core-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - the core framework", "es2015": "./@angular/core.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/core.umd.js", "module": "./@angular/core.es5.js", "name": "@angular/core", "peerDependencies": {"rxjs": "^5.0.1", "zone.js": "^0.8.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./core.d.ts", "version": "4.2.5"}