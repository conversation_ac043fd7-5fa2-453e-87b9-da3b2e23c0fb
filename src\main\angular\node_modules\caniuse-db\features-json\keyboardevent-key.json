{"title": "KeyboardEvent.key", "description": "A `KeyboardEvent` property whose value is a string identifying the key that was pressed. Covers character keys, non-character keys (e.g. arrow keys), and dead keys.", "spec": "http://www.w3.org/TR/uievents/#widl-KeyboardEvent-key", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key", "title": "Mozilla Developer Network (MDN) documentation - key"}, {"url": "https://code.google.com/p/chromium/issues/detail?id=227231", "title": "Chrome tracking bug"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=69029", "title": "WebKit feature request bug"}, {"url": "http://www.w3.org/TR/DOM-Level-3-Events-key/", "title": "Spec listing all key string values"}], "bugs": [], "categories": ["DOM"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #2", "10": "a #2", "11": "a #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "u", "12.1": "y", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "u", "12.1": "y", "37": "n"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a #2", "11": "a #2"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "y"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to these versions of Firefox returning `\"MozPrintableKey\"` for all character keys.", "2": "Has non-standard key identifiers and incorrect behaviour with AltGraph."}, "usage_perc_y": 67.89, "usage_perc_a": 5.34, "ucprefix": false, "parent": "", "keywords": "keyboard,event,key", "ie_id": "", "chrome_id": "4748790720364544", "firefox_id": "", "webkit_id": "", "shown": true}