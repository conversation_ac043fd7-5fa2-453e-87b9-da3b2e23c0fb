{"version": 3, "file": "common.umd.min.js", "sources": ["../../../../packages/common/src/pipes/index.ts", "../../../../packages/common/src/common_module.ts", "../../../../packages/common/src/pipes/slice_pipe.ts", "../../../../packages/common/src/pipes/i18n_select_pipe.ts", "../../../../packages/common/src/pipes/i18n_plural_pipe.ts", "../../../../packages/common/src/pipes/date_pipe.ts", "../../../../packages/common/src/pipes/case_conversion_pipes.ts", "../../../../packages/common/src/pipes/intl.ts", "../../../../packages/common/src/pipes/number_pipe.ts", "../../../../packages/common/src/directives/index.ts", "../../../../packages/common/src/pipes/invalid_pipe_argument_error.ts", "../../../../packages/common/src/pipes/async_pipe.ts", "../../../../packages/common/src/directives/ng_template_outlet.ts", "../../../../packages/common/src/directives/ng_style.ts", "../../../../packages/common/src/directives/ng_plural.ts", "../../../../packages/common/src/directives/ng_for_of.ts", "../../../../packages/common/src/directives/ng_if.ts", "../../../../packages/common/src/directives/ng_switch.ts", "../../../../packages/common/src/directives/ng_component_outlet.ts", "../../../../packages/common/src/directives/ng_class.ts", "../../../../packages/common/src/localization.ts", "../../../../packages/common/src/location/path_location_strategy.ts", "../../../../packages/common/src/location/index.ts", "../../../../packages/common/src/location/hash_location_strategy.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/common/src/location/platform_location.ts", "../../../../packages/common/src/location/location_strategy.ts", "../../../../packages/common/src/location/location.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\n\nimport {AsyncPipe} from './async_pipe';\nimport {LowerCasePipe, TitleCasePipe, UpperCasePipe} from './case_conversion_pipes';\nimport {DatePipe} from './date_pipe';\nimport {I18nPluralPipe} from './i18n_plural_pipe';\nimport {I18nSelectPipe} from './i18n_select_pipe';\nimport {JsonPipe} from './json_pipe';\nimport {CurrencyPipe, DecimalPipe, PercentPipe} from './number_pipe';\nimport {SlicePipe} from './slice_pipe';\n\nexport {\n  AsyncPipe,\n  CurrencyPipe,\n  DatePipe,\n  DecimalPipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  LowerCasePipe,\n  PercentPipe,\n  SlicePipe,\n  TitleCasePipe,\n  UpperCasePipe\n};\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport const COMMON_PIPES = [\n  AsyncPipe,\n  UpperCasePipe,\n  LowerCasePipe,\n  JsonPipe,\n  SlicePipe,\n  DecimalPipe,\n  PercentPipe,\n  TitleCasePipe,\n  CurrencyPipe,\n  DatePipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule} from '@angular/core';\n\nimport {COMMON_DEPRECATED_DIRECTIVES, COMMON_DIRECTIVES} from './directives/index';\nimport {NgLocaleLocalization, NgLocalization} from './localization';\nimport {COMMON_PIPES} from './pipes/index';\n/**\n * The module that includes all the basic Angular directives like {\\@link NgIf}, {\\@link NgForOf}, ...\n * \n * \\@stable\n */\nexport class CommonModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: [COMMON_DIRECTIVES, COMMON_PIPES],\n  exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n  providers: [\n    {provide: NgLocalization, useClass: NgLocaleLocalization},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction CommonModule_tsickle_Closure_declarations() {\n/** @type {?} */\nCommonModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCommonModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a new List or String containing a subset (slice) of the elements.\n * \\@howToUse `array_or_string_expression | slice:start[:end]`\n * \\@description \n * \n * Where the input expression is a `List` or `String`, and:\n * - `start`: The starting index of the subset to return.\n *   - **a positive integer**: return the item at `start` index and all items after\n *     in the list or string expression.\n *   - **a negative integer**: return the item at `start` index from the end and all items after\n *     in the list or string expression.\n *   - **if positive and greater than the size of the expression**: return an empty list or string.\n *   - **if negative and greater than the size of the expression**: return entire list or string.\n * - `end`: The ending index of the subset to return.\n *   - **omitted**: return all items until the end.\n *   - **if positive**: return all items before `end` index of the list or string.\n *   - **if negative**: return all items before `end` index from the end of the list or string.\n * \n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n * \n * When operating on a [List], the returned list is always a copy even when all\n * the elements are being returned.\n * \n * When operating on a blank value, the pipe returns the blank value.\n * \n * ## List Example\n * \n * This `ngFor` example:\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n * \n * produces the following:\n * \n *     <li>b</li>\n *     <li>c</li>\n * \n * ## String Examples\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n * \n * \\@stable\n */\nexport class SlicePipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} start\n * @param {?=} end\n * @return {?}\n */\ntransform(value: any, start: number, end?: number): any {\n    if (value == null) return value;\n\n    if (!this.supports(value)) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n\n    return value.slice(start, end);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate supports(obj: any): boolean { return typeof obj === 'string' || Array.isArray(obj); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'slice', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SlicePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nSlicePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSlicePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Generic selector that displays the string that matches the current value.\n * \\@howToUse `expression | i18nSelect:mapping`\n * \\@description \n * \n *  Where `mapping` is an object that indicates the text that should be displayed\n *  for different values of the provided `expression`.\n *  If none of the keys of the mapping match the value of the `expression`, then the content\n *  of the `other` key is returned when present, otherwise an empty string is returned.\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n * \n *  \\@experimental\n */\nexport class I18nSelectPipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} mapping\n * @return {?}\n */\ntransform(value: string|null|undefined, mapping: {[key: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n\n    return '';\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nSelect', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction I18nSelectPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nSelectPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nSelectPipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {NgLocalization, getPluralCategory} from '../localization';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _INTERPOLATION_REGEXP: RegExp = /#/g;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Maps a value to a string that pluralizes the value according to locale rules.\n * \\@howToUse `expression | i18nPlural:mapping`\n * \\@description \n * \n *  Where:\n *  - `expression` is a number.\n *  - `mapping` is an object that mimics the ICU format, see\n *    http://userguide.icu-project.org/formatparse/messages\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n * \n * \\@experimental\n */\nexport class I18nPluralPipe implements PipeTransform {\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @param {?} pluralMap\n * @return {?}\n */\ntransform(value: number, pluralMap: {[count: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n\n    const /** @type {?} */ key = getPluralCategory(value, Object.keys(pluralMap), this._localization);\n\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nPlural', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\n}\n\nfunction I18nPluralPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nPluralPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nPluralPipe.ctorParameters;\n/** @type {?} */\nI18nPluralPipe.prototype._localization;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform} from '@angular/core';\nimport {DateFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\nimport {isNumeric} from './number_pipe';\n\nconst /** @type {?} */ ISO8601_DATE_REGEX =\n    /^(\\d{4})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a date according to locale rules.\n * \\@howToUse `date_expression | date[:format]`\n * \\@description \n * \n * Where:\n * - `expression` is a date object or a number (milliseconds since UTC epoch) or an ISO string\n * (https://www.w3.org/TR/NOTE-datetime).\n * - `format` indicates which date/time components to include. The format can be predefined as\n *   shown below or custom as shown in the table.\n *   - `'medium'`: equivalent to `'yMMMdjms'` (e.g. `Sep 3, 2010, 12:05:08 PM` for `en-US`)\n *   - `'short'`: equivalent to `'yMdjm'` (e.g. `9/3/2010, 12:05 PM` for `en-US`)\n *   - `'fullDate'`: equivalent to `'yMMMMEEEEd'` (e.g. `Friday, September 3, 2010` for `en-US`)\n *   - `'longDate'`: equivalent to `'yMMMMd'` (e.g. `September 3, 2010` for `en-US`)\n *   - `'mediumDate'`: equivalent to `'yMMMd'` (e.g. `Sep 3, 2010` for `en-US`)\n *   - `'shortDate'`: equivalent to `'yMd'` (e.g. `9/3/2010` for `en-US`)\n *   - `'mediumTime'`: equivalent to `'jms'` (e.g. `12:05:08 PM` for `en-US`)\n *   - `'shortTime'`: equivalent to `'jm'` (e.g. `12:05 PM` for `en-US`)\n * \n * \n *  | Component | Symbol | Narrow | Short Form   | Long Form         | Numeric   | 2-digit   |\n *  |-----------|:------:|--------|--------------|-------------------|-----------|-----------|\n *  | era       |   G    | G (A)  | GGG (AD)     | GGGG (Anno Domini)| -         | -         |\n *  | year      |   y    | -      | -            | -                 | y (2015)  | yy (15)   |\n *  | month     |   M    | L (S)  | MMM (Sep)    | MMMM (September)  | M (9)     | MM (09)   |\n *  | day       |   d    | -      | -            | -                 | d (3)     | dd (03)   |\n *  | weekday   |   E    | E (S)  | EEE (Sun)    | EEEE (Sunday)     | -         | -         |\n *  | hour      |   j    | -      | -            | -                 | j (13)    | jj (13)   |\n *  | hour12    |   h    | -      | -            | -                 | h (1 PM)  | hh (01 PM)|\n *  | hour24    |   H    | -      | -            | -                 | H (13)    | HH (13)   |\n *  | minute    |   m    | -      | -            | -                 | m (5)     | mm (05)   |\n *  | second    |   s    | -      | -            | -                 | s (9)     | ss (09)   |\n *  | timezone  |   z    | -      | -            | z (Pacific Standard Time)| -  | -         |\n *  | timezone  |   Z    | -      | Z (GMT-8:00) | -                 | -         | -         |\n *  | timezone  |   a    | -      | a (PM)       | -                 | -         | -         |\n * \n * In javascript, only the components specified will be respected (not the ordering,\n * punctuations, ...) and details of the formatting will be dependent on the locale.\n * \n * Timezone of the formatted text will be the local system timezone of the end-user's machine.\n * \n * When the expression is a ISO string without time (e.g. 2016-09-19) the time zone offset is not\n * applied and the formatted text will have the same day, month and year of the expression.\n * \n * WARNINGS:\n * - this pipe is marked as pure hence it will not be re-evaluated when the input is mutated.\n *   Instead users should treat the date as an immutable object and change the reference when the\n *   pipe needs to re-run (this is to avoid reformatting the date on every change detection run\n *   which would be an expensive operation).\n * - this pipe uses the Internationalization API. Therefore it is only reliable in Chrome and Opera\n *   browsers.\n * \n * ### Examples\n * \n * Assuming `dateObj` is (year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11)\n * in the _local_ time and locale is 'en-US':\n * \n * ```\n *     {{ dateObj | date }}               // output is 'Jun 15, 2015'\n *     {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n *     {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n *     {{ dateObj | date:'mmss' }}        // output is '43:11'\n * ```\n * \n * {\\@example common/pipes/ts/date_pipe.ts region='DatePipe'}\n * \n * \\@stable\n */\nexport class DatePipe implements PipeTransform {\n/**\n * \\@internal\n */\nstatic _ALIASES: {[key: string]: string} = {\n    'medium': 'yMMMdjms',\n    'short': 'yMdjm',\n    'fullDate': 'yMMMMEEEEd',\n    'longDate': 'yMMMMd',\n    'mediumDate': 'yMMMd',\n    'shortDate': 'yMd',\n    'mediumTime': 'jms',\n    'shortTime': 'jm'\n  };\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} pattern\n * @return {?}\n */\ntransform(value: any, pattern: string = 'mediumDate'): string|null {\n    let /** @type {?} */ date: Date;\n\n    if (isBlank(value) || value !== value) return null;\n\n    if (typeof value === 'string') {\n      value = value.trim();\n    }\n\n    if (isDate(value)) {\n      date = value;\n    } else if (isNumeric(value)) {\n      date = new Date(parseFloat(value));\n    } else if (typeof value === 'string' && /^(\\d{4}-\\d{1,2}-\\d{1,2})$/.test(value)) {\n/**\n * For ISO Strings without time the day, month and year must be extracted from the ISO String\n * before Date creation to avoid time offset and errors in the new Date.\n * If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n * date, some browsers (e.g. IE 9) will throw an invalid Date error\n * If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n * is applied\n * Note: ISO months are 0 for January, 1 for February, ...\n */\nconst [y, m, d] = value.split('-').map((val: string) => parseInt(val, 10));\n      date = new Date(y, m - 1, d);\n    } else {\n      date = new Date(value);\n    }\n\n    if (!isDate(date)) {\n      let /** @type {?} */ match: RegExpMatchArray|null;\n      if ((typeof value === 'string') && (match = value.match(ISO8601_DATE_REGEX))) {\n        date = isoStringToDate(match);\n      } else {\n        throw invalidPipeArgumentError(DatePipe, value);\n      }\n    }\n\n    return DateFormatter.format(date, this._locale, DatePipe._ALIASES[pattern] || pattern);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'date', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DatePipe_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nDatePipe._ALIASES;\n/** @type {?} */\nDatePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDatePipe.ctorParameters;\n/** @type {?} */\nDatePipe.prototype._locale;\n}\n\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isBlank(obj: any): boolean {\n  return obj == null || obj === '';\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isDate(obj: any): obj is Date {\n  return obj instanceof Date && !isNaN(obj.valueOf());\n}\n/**\n * @param {?} match\n * @return {?}\n */\nfunction isoStringToDate(match: RegExpMatchArray): Date {\n  const /** @type {?} */ date = new Date(0);\n  let /** @type {?} */ tzHour = 0;\n  let /** @type {?} */ tzMin = 0;\n  const /** @type {?} */ dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const /** @type {?} */ timeSetter = match[8] ? date.setUTCHours : date.setHours;\n\n  if (match[9]) {\n    tzHour = toInt(match[9] + match[10]);\n    tzMin = toInt(match[9] + match[11]);\n  }\n  dateSetter.call(date, toInt(match[1]), toInt(match[2]) - 1, toInt(match[3]));\n  const /** @type {?} */ h = toInt(match[4] || '0') - tzHour;\n  const /** @type {?} */ m = toInt(match[5] || '0') - tzMin;\n  const /** @type {?} */ s = toInt(match[6] || '0');\n  const /** @type {?} */ ms = Math.round(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n/**\n * @param {?} str\n * @return {?}\n */\nfunction toInt(str: string): number {\n  return parseInt(str, 10);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * Transforms text to lowercase.\n * \n * {\\@example  common/pipes/ts/lowerupper_pipe.ts region='LowerUpperPipe' }\n * \n * \\@stable\n */\nexport class LowerCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'lowercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction LowerCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nLowerCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLowerCasePipe.ctorParameters;\n}\n\n/**\n * Helper method to transform a single word to titlecase.\n * \n * \\@stable\n * @param {?} word\n * @return {?}\n */\nfunction titleCaseWord(word: string) {\n  if (!word) return word;\n  return word[0].toUpperCase() + word.substr(1).toLowerCase();\n}\n/**\n * Transforms text to titlecase.\n * \n * \\@stable\n */\nexport class TitleCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n\n    return value.split(/\\b/g).map(word => titleCaseWord(word)).join('');\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'titlecase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction TitleCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nTitleCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitleCasePipe.ctorParameters;\n}\n\n/**\n * Transforms text to uppercase.\n * \n * \\@stable\n */\nexport class UpperCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'uppercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction UpperCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nUpperCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nUpperCasePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\nexport type NumberFormatStyle = number;\nexport let NumberFormatStyle: any = {};\nNumberFormatStyle.Decimal = 0;\nNumberFormatStyle.Percent = 1;\nNumberFormatStyle.Currency = 2;\nNumberFormatStyle[NumberFormatStyle.Decimal] = \"Decimal\";\nNumberFormatStyle[NumberFormatStyle.Percent] = \"Percent\";\nNumberFormatStyle[NumberFormatStyle.Currency] = \"Currency\";\n\nexport class NumberFormatter {\n/**\n * @param {?} num\n * @param {?} locale\n * @param {?} style\n * @param {?=} opts\n * @return {?}\n */\nstatic format(num: number, locale: string, style: NumberFormatStyle, opts: {\n    minimumIntegerDigits?: number,\n    minimumFractionDigits?: number,\n    maximumFractionDigits?: number,\n    currency?: string|null,\n    currencyAsSymbol?: boolean\n  } = {}): string {\n    const {minimumIntegerDigits, minimumFractionDigits, maximumFractionDigits, currency,\n           currencyAsSymbol = false} = opts;\n    const /** @type {?} */ options: Intl.NumberFormatOptions = {\n      minimumIntegerDigits,\n      minimumFractionDigits,\n      maximumFractionDigits,\n      style: NumberFormatStyle[style].toLowerCase()\n    };\n\n    if (style == NumberFormatStyle.Currency) {\n      options.currency = typeof currency == 'string' ? currency : undefined;\n      options.currencyDisplay = currencyAsSymbol ? 'symbol' : 'code';\n    }\n    return new Intl.NumberFormat(locale, options).format(num);\n  }\n}\n\ntype DateFormatterFn = (date: Date, locale: string) => string;\n\nconst /** @type {?} */ DATE_FORMATS_SPLIT =\n    /((?:[^yMLdHhmsazZEwGjJ']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|J+|j+|m+|s+|a|z|Z|G+|w+))(.*)/;\n\nconst /** @type {?} */ PATTERN_ALIASES: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed during closure compilation.\n  'yMMMdjms': datePartGetterFactory(combine([\n    digitCondition('year', 1),\n    nameCondition('month', 3),\n    digitCondition('day', 1),\n    digitCondition('hour', 1),\n    digitCondition('minute', 1),\n    digitCondition('second', 1),\n  ])),\n  'yMdjm': datePartGetterFactory(combine([\n    digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1),\n    digitCondition('hour', 1), digitCondition('minute', 1)\n  ])),\n  'yMMMMEEEEd': datePartGetterFactory(combine([\n    digitCondition('year', 1), nameCondition('month', 4), nameCondition('weekday', 4),\n    digitCondition('day', 1)\n  ])),\n  'yMMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 4), digitCondition('day', 1)])),\n  'yMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 3), digitCondition('day', 1)])),\n  'yMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1)])),\n  'jms': datePartGetterFactory(combine(\n      [digitCondition('hour', 1), digitCondition('second', 1), digitCondition('minute', 1)])),\n  'jm': datePartGetterFactory(combine([digitCondition('hour', 1), digitCondition('minute', 1)]))\n};\n\nconst /** @type {?} */ DATE_FORMATS: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed.\n  'yyyy': datePartGetterFactory(digitCondition('year', 4)),\n  'yy': datePartGetterFactory(digitCondition('year', 2)),\n  'y': datePartGetterFactory(digitCondition('year', 1)),\n  'MMMM': datePartGetterFactory(nameCondition('month', 4)),\n  'MMM': datePartGetterFactory(nameCondition('month', 3)),\n  'MM': datePartGetterFactory(digitCondition('month', 2)),\n  'M': datePartGetterFactory(digitCondition('month', 1)),\n  'LLLL': datePartGetterFactory(nameCondition('month', 4)),\n  'L': datePartGetterFactory(nameCondition('month', 1)),\n  'dd': datePartGetterFactory(digitCondition('day', 2)),\n  'd': datePartGetterFactory(digitCondition('day', 1)),\n  'HH': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), false)))),\n  'H': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), false))),\n  'hh': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), true)))),\n  'h': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'jj': datePartGetterFactory(digitCondition('hour', 2)),\n  'j': datePartGetterFactory(digitCondition('hour', 1)),\n  'mm': digitModifier(datePartGetterFactory(digitCondition('minute', 2))),\n  'm': datePartGetterFactory(digitCondition('minute', 1)),\n  'ss': digitModifier(datePartGetterFactory(digitCondition('second', 2))),\n  's': datePartGetterFactory(digitCondition('second', 1)),\n  // while ISO 8601 requires fractions to be prefixed with `.` or `,`\n  // we can be just safely rely on using `sss` since we currently don't support single or two digit\n  // fractions\n  'sss': datePartGetterFactory(digitCondition('second', 3)),\n  'EEEE': datePartGetterFactory(nameCondition('weekday', 4)),\n  'EEE': datePartGetterFactory(nameCondition('weekday', 3)),\n  'EE': datePartGetterFactory(nameCondition('weekday', 2)),\n  'E': datePartGetterFactory(nameCondition('weekday', 1)),\n  'a': hourClockExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'Z': timeZoneGetter('short'),\n  'z': timeZoneGetter('long'),\n  'ww': datePartGetterFactory({}),  // Week of year, padded (00-53). Week 01 is the week with the\n                                    // first Thursday of the year. not support ?\n  'w':\n      datePartGetterFactory({}),  // Week of year (0-53). Week 1 is the week with the first Thursday\n                                  // of the year not support ?\n  'G': datePartGetterFactory(nameCondition('era', 1)),\n  'GG': datePartGetterFactory(nameCondition('era', 2)),\n  'GGG': datePartGetterFactory(nameCondition('era', 3)),\n  'GGGG': datePartGetterFactory(nameCondition('era', 4))\n};\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction digitModifier(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = inner(date, locale);\n    return result.length == 1 ? '0' + result : result;\n  };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourClockExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[1]; };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[0]; };\n}\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} options\n * @return {?}\n */\nfunction intlDateFormat(date: Date, locale: string, options: Intl.DateTimeFormatOptions): string {\n  return new Intl.DateTimeFormat(locale, options).format(date).replace(/[\\u200e\\u200f]/g, '');\n}\n/**\n * @param {?} timezone\n * @return {?}\n */\nfunction timeZoneGetter(timezone: string): DateFormatterFn {\n  // To workaround `Intl` API restriction for single timezone let format with 24 hours\n  const /** @type {?} */ options = {hour: '2-digit', hour12: false, timeZoneName: timezone};\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = intlDateFormat(date, locale, options);\n    // Then extract first 3 letters that related to hours\n    return result ? result.substring(3) : '';\n  };\n}\n/**\n * @param {?} options\n * @param {?} value\n * @return {?}\n */\nfunction hour12Modify(\n    options: Intl.DateTimeFormatOptions, value: boolean): Intl.DateTimeFormatOptions {\n  options.hour12 = value;\n  return options;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction digitCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  result[prop] = len === 2 ? '2-digit' : 'numeric';\n  return result;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction nameCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  if (len < 4) {\n    result[prop] = len > 1 ? 'short' : 'narrow';\n  } else {\n    result[prop] = 'long';\n  }\n\n  return result;\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction combine(options: Intl.DateTimeFormatOptions[]): Intl.DateTimeFormatOptions {\n  return ( /** @type {?} */((<any>Object))).assign({}, ...options);\n}\n/**\n * @param {?} ret\n * @return {?}\n */\nfunction datePartGetterFactory(ret: Intl.DateTimeFormatOptions): DateFormatterFn {\n  return (date: Date, locale: string): string => intlDateFormat(date, locale, ret);\n}\n\nconst /** @type {?} */ DATE_FORMATTER_CACHE = new Map<string, string[]>();\n/**\n * @param {?} format\n * @param {?} date\n * @param {?} locale\n * @return {?}\n */\nfunction dateFormatter(format: string, date: Date, locale: string): string {\n  const /** @type {?} */ fn = PATTERN_ALIASES[format];\n\n  if (fn) return fn(date, locale);\n\n  const /** @type {?} */ cacheKey = format;\n  let /** @type {?} */ parts = DATE_FORMATTER_CACHE.get(cacheKey);\n\n  if (!parts) {\n    parts = [];\n    let /** @type {?} */ match: RegExpExecArray|null;\n    DATE_FORMATS_SPLIT.exec(format);\n\n    let /** @type {?} */ _format: string|null = format;\n    while (_format) {\n      match = DATE_FORMATS_SPLIT.exec(_format);\n      if (match) {\n        parts = parts.concat(match.slice(1));\n        _format = /** @type {?} */(( parts.pop()));\n      } else {\n        parts.push(_format);\n        _format = null;\n      }\n    }\n\n    DATE_FORMATTER_CACHE.set(cacheKey, parts);\n  }\n\n  return parts.reduce((text, part) => {\n    const /** @type {?} */ fn = DATE_FORMATS[part];\n    return text + (fn ? fn(date, locale) : partToTime(part));\n  }, '');\n}\n/**\n * @param {?} part\n * @return {?}\n */\nfunction partToTime(part: string): string {\n  return part === '\\'\\'' ? '\\'' : part.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n}\nexport class DateFormatter {\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} pattern\n * @return {?}\n */\nstatic format(date: Date, locale: string, pattern: string): string {\n    return dateFormatter(pattern, date, locale);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform, Type} from '@angular/core';\nimport {NumberFormatStyle, NumberFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\n/**\n * @param {?} pipe\n * @param {?} locale\n * @param {?} value\n * @param {?} style\n * @param {?=} digits\n * @param {?=} currency\n * @param {?=} currencyAsSymbol\n * @return {?}\n */\nfunction formatNumber(\n    pipe: Type<any>, locale: string, value: number | string, style: NumberFormatStyle,\n    digits?: string | null, currency: string | null = null,\n    currencyAsSymbol: boolean = false): string|null {\n  if (value == null) return null;\n\n  // Convert strings to numbers\n  value = typeof value === 'string' && isNumeric(value) ? +value : value;\n  if (typeof value !== 'number') {\n    throw invalidPipeArgumentError(pipe, value);\n  }\n\n  let /** @type {?} */ minInt: number|undefined = undefined;\n  let /** @type {?} */ minFraction: number|undefined = undefined;\n  let /** @type {?} */ maxFraction: number|undefined = undefined;\n  if (style !== NumberFormatStyle.Currency) {\n    // rely on Intl default for currency\n    minInt = 1;\n    minFraction = 0;\n    maxFraction = 3;\n  }\n\n  if (digits) {\n    const /** @type {?} */ parts = digits.match(_NUMBER_FORMAT_REGEXP);\n    if (parts === null) {\n      throw new Error(`${digits} is not a valid digit info for number pipes`);\n    }\n    if (parts[1] != null) {  // min integer digits\n      minInt = parseIntAutoRadix(parts[1]);\n    }\n    if (parts[3] != null) {  // min fraction digits\n      minFraction = parseIntAutoRadix(parts[3]);\n    }\n    if (parts[5] != null) {  // max fraction digits\n      maxFraction = parseIntAutoRadix(parts[5]);\n    }\n  }\n\n  return NumberFormatter.format( /** @type {?} */((value as number)), locale, style, {\n    minimumIntegerDigits: minInt,\n    minimumFractionDigits: minFraction,\n    maximumFractionDigits: maxFraction,\n    currency: currency,\n    currencyAsSymbol: currencyAsSymbol,\n  });\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number according to locale rules.\n * \\@howToUse `number_expression | number[:digitInfo]`\n * \n * Formats a number as text. Group sizing and separator and other locale-specific\n * configurations are based on the active locale.\n * \n * where `expression` is a number:\n *  - `digitInfo` is a `string` which has a following format: <br>\n *     <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>\n *   - `minIntegerDigits` is the minimum number of integer digits to use. Defaults to `1`.\n *   - `minFractionDigits` is the minimum number of digits after fraction. Defaults to `0`.\n *   - `maxFractionDigits` is the maximum number of digits after fraction. Defaults to `3`.\n * \n * For more information on the acceptable range for each of these numbers and other\n * details see your native internationalization library.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='NumberPipe'}\n * \n * \\@stable\n */\nexport class DecimalPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(DecimalPipe, this._locale, value, NumberFormatStyle.Decimal, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'number'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DecimalPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nDecimalPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDecimalPipe.ctorParameters;\n/** @type {?} */\nDecimalPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as a percentage according to locale rules.\n * \\@howToUse `number_expression | percent[:digitInfo]`\n * \n * \\@description \n * \n * Formats a number as percentage.\n * \n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='PercentPipe'}\n * \n * \\@stable\n */\nexport class PercentPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(PercentPipe, this._locale, value, NumberFormatStyle.Percent, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'percent'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction PercentPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nPercentPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPercentPipe.ctorParameters;\n/** @type {?} */\nPercentPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as currency using locale rules.\n * \\@howToUse `number_expression | currency[:currencyCode[:symbolDisplay[:digitInfo]]]`\n * \\@description \n * \n * Use `currency` to format a number as currency.\n * \n * - `currencyCode` is the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code, such\n *    as `USD` for the US dollar and `EUR` for the euro.\n * - `symbolDisplay` is a boolean indicating whether to use the currency symbol or code.\n *   - `true`: use symbol (e.g. `$`).\n *   - `false`(default): use code (e.g. `USD`).\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='CurrencyPipe'}\n * \n * \\@stable\n */\nexport class CurrencyPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} currencyCode\n * @param {?=} symbolDisplay\n * @param {?=} digits\n * @return {?}\n */\ntransform(\n      value: any, currencyCode: string = 'USD', symbolDisplay: boolean = false,\n      digits?: string): string|null {\n    return formatNumber(\n        CurrencyPipe, this._locale, value, NumberFormatStyle.Currency, digits, currencyCode,\n        symbolDisplay);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'currency'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction CurrencyPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nCurrencyPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCurrencyPipe.ctorParameters;\n/** @type {?} */\nCurrencyPipe.prototype._locale;\n}\n\n/**\n * @param {?} text\n * @return {?}\n */\nfunction parseIntAutoRadix(text: string): number {\n  const /** @type {?} */ result: number = parseInt(text);\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n  return result;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nexport function isNumeric(value: any): boolean {\n  return !isNaN(value - parseFloat(value));\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Provider} from '@angular/core';\n\nimport {NgClass} from './ng_class';\nimport {NgComponentOutlet} from './ng_component_outlet';\nimport {Ng<PERSON>or, NgForOf, NgForOfContext} from './ng_for_of';\nimport {NgIf, NgIfContext} from './ng_if';\nimport {NgPlural, NgPluralCase} from './ng_plural';\nimport {NgStyle} from './ng_style';\nimport {Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault} from './ng_switch';\nimport {NgTemplateOutlet} from './ng_template_outlet';\n\nexport {\n  NgClass,\n  NgComponentOutlet,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  Ng<PERSON><PERSON><PERSON>,\n  Ng<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>Case,\n  <PERSON><PERSON><PERSON>Default,\n  NgTemplateOutlet\n};\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport const COMMON_DIRECTIVES: Provider[] = [\n  NgClass,\n  NgComponentOutlet,\n  NgForOf,\n  NgIf,\n  NgTemplateOutlet,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgPlural,\n  NgPluralCase,\n];\n/**\n * A collection of deprecated directives that are no longer part of the core module.\n */\nexport const COMMON_DEPRECATED_DIRECTIVES: Provider[] = [NgFor];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type, ɵstringify as stringify} from '@angular/core';\n/**\n * @param {?} type\n * @param {?} value\n * @return {?}\n */\nexport function invalidPipeArgumentError(type: Type<any>, value: Object) {\n  return Error(`InvalidPipeArgument: '${value}' for pipe '${stringify(type)}'`);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, EventEmitter, OnDestroy, Pipe, PipeTransform, WrappedValue, ɵisObservable, ɵisPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {ISubscription} from 'rxjs/Subscription';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\ninterface SubscriptionStrategy {\n  createSubscription(async: Observable<any>|Promise<any>, updateLatestValue: any): ISubscription\n      |Promise<any>;\n  dispose(subscription: ISubscription|Promise<any>): void;\n  onDestroy(subscription: ISubscription|Promise<any>): void;\n}\nclass ObservableStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Observable<any>, updateLatestValue: any): ISubscription {\n    return async.subscribe({next: updateLatestValue, error: (e: any) => { throw e; }});\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: ISubscription): void { subscription.unsubscribe(); }\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: ISubscription): void { subscription.unsubscribe(); }\n}\nclass PromiseStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Promise<any>, updateLatestValue: (v: any) => any): Promise<any> {\n    return async.then(updateLatestValue, e => { throw e; });\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: Promise<any>): void {}\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: Promise<any>): void {}\n}\n\nconst /** @type {?} */ _promiseStrategy = new PromiseStrategy();\nconst /** @type {?} */ _observableStrategy = new ObservableStrategy();\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Unwraps a value from an asynchronous primitive.\n * \\@howToUse `observable_or_promise_expression | async`\n * \\@description \n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks.\n * \n * \n * ## Examples\n * \n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n * \n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n * \n * \\@stable\n */\nexport class AsyncPipe implements OnDestroy, PipeTransform {\nprivate _latestValue: any = null;\nprivate _latestReturnedValue: any = null;\nprivate _subscription: ISubscription|Promise<any>|null = null;\nprivate _obj: Observable<any>|Promise<any>|EventEmitter<any>|null = null;\nprivate _strategy: SubscriptionStrategy = /** @type {?} */(( null));\n/**\n * @param {?} _ref\n */\nconstructor(private _ref: ChangeDetectorRef) {}\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._subscription) {\n      this._dispose();\n    }\n  }\n\n  transform<T>(obj: null): null;\n  transform<T>(obj: undefined): undefined;\n  transform<T>(obj: Observable<T>): T|null;\n  transform<T>(obj: Promise<T>): T|null;\n/**\n * @param {?} obj\n * @return {?}\n */\ntransform(obj: Observable<any>|Promise<any>|null|undefined): any {\n    if (!this._obj) {\n      if (obj) {\n        this._subscribe(obj);\n      }\n      this._latestReturnedValue = this._latestValue;\n      return this._latestValue;\n    }\n\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform( /** @type {?} */((obj as any)));\n    }\n\n    if (this._latestValue === this._latestReturnedValue) {\n      return this._latestReturnedValue;\n    }\n\n    this._latestReturnedValue = this._latestValue;\n    return WrappedValue.wrap(this._latestValue);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _subscribe(obj: Observable<any>|Promise<any>|EventEmitter<any>): void {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(\n        obj, (value: Object) => this._updateLatestValue(obj, value));\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _selectStrategy(obj: Observable<any>|Promise<any>|EventEmitter<any>): any {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n\n    if (ɵisObservable(obj)) {\n      return _observableStrategy;\n    }\n\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n/**\n * @return {?}\n */\nprivate _dispose(): void {\n    this._strategy.dispose( /** @type {?} */((this._subscription)));\n    this._latestValue = null;\n    this._latestReturnedValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n/**\n * @param {?} async\n * @param {?} value\n * @return {?}\n */\nprivate _updateLatestValue(async: any, value: Object): void {\n    if (async === this._obj) {\n      this._latestValue = value;\n      this._ref.markForCheck();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'async', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ChangeDetectorRef, },\n];\n}\n\nfunction AsyncPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nAsyncPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAsyncPipe.ctorParameters;\n/** @type {?} */\nAsyncPipe.prototype._latestValue;\n/** @type {?} */\nAsyncPipe.prototype._latestReturnedValue;\n/** @type {?} */\nAsyncPipe.prototype._subscription;\n/** @type {?} */\nAsyncPipe.prototype._obj;\n/** @type {?} */\nAsyncPipe.prototype._strategy;\n/** @type {?} */\nAsyncPipe.prototype._ref;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, OnChanges, SimpleChanges, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Inserts an embedded view from a prepared `TemplateRef`\n * \n * \\@howToUse \n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n * \n * \\@description \n * \n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n * \n * Note: using the key `$implicit` in the context object will set it's value as default.\n * \n * ## Example\n * \n * {\\@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n * \n * \\@experimental\n */\nexport class NgTemplateOutlet implements OnChanges {\nprivate _viewRef: EmbeddedViewRef<any>;\npublic ngTemplateOutletContext: Object;\npublic ngTemplateOutlet: TemplateRef<any>;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @deprecated v4.0.0 - Renamed to ngTemplateOutletContext.\n * @param {?} context\n * @return {?}\n */\nset ngOutletContext(context: Object) { this.ngTemplateOutletContext = context; }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    if (this._viewRef) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._viewRef));\n    }\n\n    if (this.ngTemplateOutlet) {\n      this._viewRef = this._viewContainerRef.createEmbeddedView(\n          this.ngTemplateOutlet, this.ngTemplateOutletContext);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngTemplateOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngTemplateOutletContext': [{ type: Input },],\n'ngTemplateOutlet': [{ type: Input },],\n'ngOutletContext': [{ type: Input },],\n};\n}\n\nfunction NgTemplateOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgTemplateOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgTemplateOutlet.ctorParameters;\n/** @type {?} */\nNgTemplateOutlet.propDecorators;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewRef;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutletContext;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutlet;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, KeyValueChanges, KeyV<PERSON>ueDiffer, Key<PERSON><PERSON>ue<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Update an HTML element styles.\n * \n * \\@howToUse \n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The styles are updated according to the value of the expression evaluation:\n * - keys are style names with an optional `.<unit>` suffix (ie 'top.px', 'font-style.em'),\n * - values are the values assigned to those properties (expressed in the given unit).\n * \n * \\@stable\n */\nexport class NgStyle implements DoCheck {\nprivate _ngStyle: {[key: string]: string};\nprivate _differ: KeyValueDiffer<string, string|number>;\n/**\n * @param {?} _differs\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _differs: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset ngStyle(v: {[key: string]: string}) {\n    this._ngStyle = v;\n    if (!this._differ && v) {\n      this._differ = this._differs.find(v).create();\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck() {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this._ngStyle);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: KeyValueChanges<string, string|number>): void {\n    changes.forEachRemovedItem((record) => this._setStyle(record.key, null));\n    changes.forEachAddedItem((record) => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._setStyle(record.key, record.currentValue));\n  }\n/**\n * @param {?} nameAndUnit\n * @param {?} value\n * @return {?}\n */\nprivate _setStyle(nameAndUnit: string, value: string|number|null|undefined): void {\n    const [name, unit] = nameAndUnit.split('.');\n    value = value != null && unit ? `${value}${unit}` : value;\n\n    this._renderer.setElementStyle(this._ngEl.nativeElement, name, /** @type {?} */(( value as string)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngStyle]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngStyle': [{ type: Input },],\n};\n}\n\nfunction NgStyle_tsickle_Closure_declarations() {\n/** @type {?} */\nNgStyle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgStyle.ctorParameters;\n/** @type {?} */\nNgStyle.propDecorators;\n/** @type {?} */\nNgStyle.prototype._ngStyle;\n/** @type {?} */\nNgStyle.prototype._differ;\n/** @type {?} */\nNgStyle.prototype._differs;\n/** @type {?} */\nNgStyle.prototype._ngEl;\n/** @type {?} */\nNgStyle.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n\nimport {NgLocalization, getPluralCategory} from '../localization';\n\nimport {SwitchView} from './ng_switch';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n * \n * \\@description \n * \n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n * \n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n * \n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n * \n * \\@experimental\n */\nexport class NgPlural {\nprivate _switchValue: number;\nprivate _activeView: SwitchView;\nprivate _caseViews: {[k: string]: SwitchView} = {};\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngPlural(value: number) {\n    this._switchValue = value;\n    this._updateView();\n  }\n/**\n * @param {?} value\n * @param {?} switchView\n * @return {?}\n */\naddCase(value: string, switchView: SwitchView): void { this._caseViews[value] = switchView; }\n/**\n * @return {?}\n */\nprivate _updateView(): void {\n    this._clearViews();\n\n    const /** @type {?} */ cases = Object.keys(this._caseViews);\n    const /** @type {?} */ key = getPluralCategory(this._switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n/**\n * @return {?}\n */\nprivate _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n/**\n * @param {?} view\n * @return {?}\n */\nprivate _activateView(view: SwitchView) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPlural]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngPlural': [{ type: Input },],\n};\n}\n\nfunction NgPlural_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPlural.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPlural.ctorParameters;\n/** @type {?} */\nNgPlural.propDecorators;\n/** @type {?} */\nNgPlural.prototype._switchValue;\n/** @type {?} */\nNgPlural.prototype._activeView;\n/** @type {?} */\nNgPlural.prototype._caseViews;\n/** @type {?} */\nNgPlural.prototype._localization;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgPlural} when the\n *             given expression matches the plural expression according to CLDR rules.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n * ```\n * \n * See {\\@link NgPlural} for more details and example.\n * \n * \\@experimental\n */\nexport class NgPluralCase {\n/**\n * @param {?} value\n * @param {?} template\n * @param {?} viewContainer\n * @param {?} ngPlural\n */\nconstructor(\npublic value: string, template: TemplateRef<Object>,\n      viewContainer: ViewContainerRef,  ngPlural: NgPlural) {\n    const isANumber: boolean = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPluralCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Attribute, args: ['ngPluralCase', ] }, ]},\n{type: TemplateRef, },\n{type: ViewContainerRef, },\n{type: NgPlural, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgPluralCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPluralCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPluralCase.ctorParameters;\n/** @type {?} */\nNgPluralCase.prototype.value;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, Directive, DoCheck, EmbeddedViewRef, Input, IterableChangeRecord, IterableChanges, IterableDiffer, IterableDiffers, NgIterable, OnChanges, SimpleChanges, TemplateRef, TrackByFunction, ViewContainerRef, forwardRef, isDevMode} from '@angular/core';\n/**\n * \\@stable\n */\nexport class NgForOfContext<T> {\n/**\n * @param {?} $implicit\n * @param {?} ngForOf\n * @param {?} index\n * @param {?} count\n */\nconstructor(\npublic $implicit: T,\npublic ngForOf: NgIterable<T>,\npublic index: number,\npublic count: number) {}\n/**\n * @return {?}\n */\nget first(): boolean { return this.index === 0; }\n/**\n * @return {?}\n */\nget last(): boolean { return this.index === this.count - 1; }\n/**\n * @return {?}\n */\nget even(): boolean { return this.index % 2 === 0; }\n/**\n * @return {?}\n */\nget odd(): boolean { return !this.even; }\n}\n\nfunction NgForOfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOfContext.prototype.$implicit;\n/** @type {?} */\nNgForOfContext.prototype.ngForOf;\n/** @type {?} */\nNgForOfContext.prototype.index;\n/** @type {?} */\nNgForOfContext.prototype.count;\n}\n\n/**\n * The `NgForOf` directive instantiates a template once per item from an iterable. The context\n * for each instantiated template inherits from the outer context with the given loop variable\n * set to the current item from the iterable.\n * \n * ### Local Variables\n * \n * `NgForOf` provides several exported values that can be aliased to local variables:\n * \n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n * \n * ```\n * <li *ngFor=\"let user of userObservable | async as users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n * \n * ### Change Propagation\n * \n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n * \n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n * * Otherwise, the DOM element for that item will remain the same.\n * \n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls (such as `<input>` elements which accept user input) that are present. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * \n * It is possible for the identities of elements in the iterator to change while the data does not.\n * This can happen, for example, if the iterator produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response will produce objects with\n * different identities, and Angular will tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted). This is an expensive operation and should\n * be avoided if possible.\n * \n * To customize the default tracking algorithm, `NgForOf` supports `trackBy` option.\n * `trackBy` takes a function which has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n * \n * ### Syntax\n * \n * - `<li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>`\n * - `<li template=\"ngFor let item of items; index as i; trackBy: trackByFn\">...</li>`\n * \n * With `<ng-template>` element:\n * \n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n * \n * ### Example\n * \n * See a [live demo](http://plnkr.co/edit/KVuXxDp0qinGDyo307QW?p=preview) for a more detailed\n * example.\n * \n * \\@stable\n */\nexport class NgForOf<T> implements DoCheck, OnChanges {\n   ngForOf: NgIterable<T>;\n/**\n * @param {?} fn\n * @return {?}\n */\nset ngForTrackBy(fn: TrackByFunction<T>) {\n    if (isDevMode() && fn != null && typeof fn !== 'function') {\n      // TODO(vicb): use a log service once there is a public one available\n      if ( /** @type {?} */((<any>console)) && /** @type {?} */(( <any>console.warn))) {\n        console.warn(\n            `trackBy must be a function, but received ${JSON.stringify(fn)}. ` +\n            `See https://angular.io/docs/ts/latest/api/common/index/NgFor-directive.html#!#change-propagation for more information.`);\n      }\n    }\n    this._trackByFn = fn;\n  }\n/**\n * @return {?}\n */\nget ngForTrackBy(): TrackByFunction<T> { return this._trackByFn; }\nprivate _differ: IterableDiffer<T>|null = null;\nprivate _trackByFn: TrackByFunction<T>;\n/**\n * @param {?} _viewContainer\n * @param {?} _template\n * @param {?} _differs\n */\nconstructor(\nprivate _viewContainer: ViewContainerRef,\nprivate _template: TemplateRef<NgForOfContext<T>>,\nprivate _differs: IterableDiffers) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngForTemplate(value: TemplateRef<NgForOfContext<T>>) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('ngForOf' in changes) {\n      // React on ngForOf changes only once all inputs have been initialized\n      const /** @type {?} */ value = changes['ngForOf'].currentValue;\n      if (!this._differ && value) {\n        try {\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        } catch ( /** @type {?} */e) {\n          throw new Error(\n              `Cannot find a differ supporting object '${value}' of type '${getTypeNameForDebugging(value)}'. NgFor only supports binding to Iterables such as Arrays.`);\n        }\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this.ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: IterableChanges<T>) {\n    const /** @type {?} */ insertTuples: RecordViewTuple<T>[] = [];\n    changes.forEachOperation(\n        (item: IterableChangeRecord<any>, adjustedPreviousIndex: number, currentIndex: number) => {\n          if (item.previousIndex == null) {\n            const /** @type {?} */ view = this._viewContainer.createEmbeddedView(\n                this._template, new NgForOfContext<T>( /** @type {?} */((null)), this.ngForOf, -1, -1), currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple<T>(item, view);\n            insertTuples.push(tuple);\n          } else if (currentIndex == null) {\n            this._viewContainer.remove(adjustedPreviousIndex);\n          } else {\n            const /** @type {?} */ view = /** @type {?} */(( this._viewContainer.get(adjustedPreviousIndex)));\n            this._viewContainer.move(view, currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple(item, /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>view)));\n            insertTuples.push(tuple);\n          }\n        });\n\n    for (let /** @type {?} */ i = 0; i < insertTuples.length; i++) {\n      this._perViewChange(insertTuples[i].view, insertTuples[i].record);\n    }\n\n    for (let /** @type {?} */ i = 0, /** @type {?} */ ilen = this._viewContainer.length; i < ilen; i++) {\n      const /** @type {?} */ viewRef = /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(i)));\n      viewRef.context.index = i;\n      viewRef.context.count = ilen;\n    }\n\n    changes.forEachIdentityChange((record: any) => {\n      const /** @type {?} */ viewRef = /** @type {?} */((\n          <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(record.currentIndex)));\n      viewRef.context.$implicit = record.item;\n    });\n  }\n/**\n * @param {?} view\n * @param {?} record\n * @return {?}\n */\nprivate _perViewChange(\n      view: EmbeddedViewRef<NgForOfContext<T>>, record: IterableChangeRecord<any>) {\n    view.context.$implicit = record.item;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngFor][ngForOf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: IterableDiffers, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngForOf': [{ type: Input },],\n'ngForTrackBy': [{ type: Input },],\n'ngForTemplate': [{ type: Input },],\n};\n}\n\nfunction NgForOf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgForOf.ctorParameters;\n/** @type {?} */\nNgForOf.propDecorators;\n/** @type {?} */\nNgForOf.prototype.ngForOf;\n/** @type {?} */\nNgForOf.prototype._differ;\n/** @type {?} */\nNgForOf.prototype._trackByFn;\n/** @type {?} */\nNgForOf.prototype._viewContainer;\n/** @type {?} */\nNgForOf.prototype._template;\n/** @type {?} */\nNgForOf.prototype._differs;\n}\n\nclass RecordViewTuple<T> {\n/**\n * @param {?} record\n * @param {?} view\n */\nconstructor(public record: any,\npublic view: EmbeddedViewRef<NgForOfContext<T>>) {}\n}\n\nfunction RecordViewTuple_tsickle_Closure_declarations() {\n/** @type {?} */\nRecordViewTuple.prototype.record;\n/** @type {?} */\nRecordViewTuple.prototype.view;\n}\n\n\n/**\n * @deprecated from v4.0.0 - Use NgForOf<any> instead.\n */\nexport type NgFor = NgForOf<any>;\n/**\n * @deprecated from v4.0.0 - Use NgForOf instead.\n */\nexport const NgFor = NgForOf;\n/**\n * @param {?} type\n * @return {?}\n */\nexport function getTypeNameForDebugging(type: any): string {\n  return type['name'] || typeof type;\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * Conditionally includes a template based on the value of an `expression`.\n * \n * `ngIf` evaluates the `expression` and then renders the `then` or `else` template in its place\n * when expression is truthy or falsy respectively. Typically the:\n *  - `then` template is the inline template of `ngIf` unless bound to a different value.\n *  - `else` template is blank unless it is bound.\n * \n * ## Most common usage\n * \n * The most common usage of the `ngIf` directive is to conditionally show the inline template as\n * seen in this example:\n * {\\@example common/ngIf/ts/module.ts region='NgIfSimple'}\n * \n * ## Showing an alternative template using `else`\n * \n * If it is necessary to display a template when the `expression` is falsy use the `else` template\n * binding as shown. Note that the `else` binding points to a `<ng-template>` labeled `#elseBlock`.\n * The template can be defined anywhere in the component view but is typically placed right after\n * `ngIf` for readability.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfElse'}\n * \n * ## Using non-inlined `then` template\n * \n * Usually the `then` template is the inlined template of the `ngIf`, but it can be changed using\n * a binding (just like `else`). Because `then` and `else` are bindings, the template references can\n * change at runtime as shown in this example.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n * \n * ## Storing conditional result in a variable\n * \n * A common pattern is that we need to show a set of properties from the same object. If the\n * object is undefined, then we have to use the safe-traversal-operator `?.` to guard against\n * dereferencing a `null` value. This is especially the case when waiting on async data such as\n * when using the `async` pipe as shown in folowing example:\n * \n * ```\n * Hello {{ (userStream|async)?.last }}, {{ (userStream|async)?.first }}!\n * ```\n * \n * There are several inefficiencies in the above example:\n *  - We create multiple subscriptions on `userStream`. One for each `async` pipe, or two in the\n *    example above.\n *  - We cannot display an alternative screen while waiting for the data to arrive asynchronously.\n *  - We have to use the safe-traversal-operator `?.` to access properties, which is cumbersome.\n *  - We have to place the `async` pipe in parenthesis.\n * \n * A better way to do this is to use `ngIf` and store the result of the condition in a local\n * variable as shown in the the example below:\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfAs'}\n * \n * Notice that:\n *  - We use only one `async` pipe and hence only one subscription gets created.\n *  - `ngIf` stores the result of the `userStream|async` in the local variable `user`.\n *  - The local `user` can then be bound repeatedly in a more efficient way.\n *  - No need to use the safe-traversal-operator `?.` to access properties as `ngIf` will only\n *    display the data if `userStream` returns a value.\n *  - We can display an alternative template while waiting for the data.\n * \n * ### Syntax\n * \n * Simple form:\n * - `<div *ngIf=\"condition\">...</div>`\n * - `<div template=\"ngIf condition\">...</div>`\n * - `<ng-template [ngIf]=\"condition\"><div>...</div></ng-template>`\n * \n * Form with an else block:\n * ```\n * <div *ngIf=\"condition; else elseBlock\">...</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with a `then` and `else` block:\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>...</ng-template>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with storing the value locally:\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * \\@stable\n */\nexport class NgIf {\nprivate _context: NgIfContext = new NgIfContext();\nprivate _thenTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _elseTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _thenViewRef: EmbeddedViewRef<NgIfContext>|null = null;\nprivate _elseViewRef: EmbeddedViewRef<NgIfContext>|null = null;\n/**\n * @param {?} _viewContainer\n * @param {?} templateRef\n */\nconstructor(private _viewContainer: ViewContainerRef, templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n  }\n/**\n * @param {?} condition\n * @return {?}\n */\nset ngIf(condition: any) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfThen(templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfElse(templateRef: TemplateRef<NgIfContext>) {\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @return {?}\n */\nprivate _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef =\n              this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef =\n              this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n        }\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngIf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngIf': [{ type: Input },],\n'ngIfThen': [{ type: Input },],\n'ngIfElse': [{ type: Input },],\n};\n}\n\nfunction NgIf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgIf.ctorParameters;\n/** @type {?} */\nNgIf.propDecorators;\n/** @type {?} */\nNgIf.prototype._context;\n/** @type {?} */\nNgIf.prototype._thenTemplateRef;\n/** @type {?} */\nNgIf.prototype._elseTemplateRef;\n/** @type {?} */\nNgIf.prototype._thenViewRef;\n/** @type {?} */\nNgIf.prototype._elseViewRef;\n/** @type {?} */\nNgIf.prototype._viewContainer;\n}\n\n/**\n * \\@stable\n */\nexport class NgIfContext {\npublic $implicit: any = null;\npublic ngIf: any = null;\n}\n\nfunction NgIfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIfContext.prototype.$implicit;\n/** @type {?} */\nNgIfContext.prototype.ngIf;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\nexport class SwitchView {\nprivate _created = false;\n/**\n * @param {?} _viewContainerRef\n * @param {?} _templateRef\n */\nconstructor(\nprivate _viewContainerRef: ViewContainerRef,\nprivate _templateRef: TemplateRef<Object>) {}\n/**\n * @return {?}\n */\ncreate(): void {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n/**\n * @param {?} created\n * @return {?}\n */\nenforceState(created: boolean) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n\nfunction SwitchView_tsickle_Closure_declarations() {\n/** @type {?} */\nSwitchView.prototype._created;\n/** @type {?} */\nSwitchView.prototype._viewContainerRef;\n/** @type {?} */\nSwitchView.prototype._templateRef;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees when the nest match expressions matches the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n *     <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n * \\@description \n * \n * `NgSwitch` stamps out nested views when their match expression value matches the value of the\n * switch expression.\n * \n * In other words:\n * - you define a container element (where you place the directive with a switch expression on the\n * `[ngSwitch]=\"...\"` attribute)\n * - you define inner views inside the `NgSwitch` and place a `*ngSwitchCase` attribute on the view\n * root elements.\n * \n * Elements within `NgSwitch` but outside of a `NgSwitchCase` or `NgSwitchDefault` directives will\n * be preserved at the location.\n * \n * The `ngSwitchCase` directive informs the parent `NgSwitch` of which view to display when the\n * expression is evaluated.\n * When no matching expression is found on a `ngSwitchCase` view, the `ngSwitchDefault` view is\n * stamped out.\n * \n * \\@stable\n */\nexport class NgSwitch {\nprivate _defaultViews: SwitchView[];\nprivate _defaultUsed = false;\nprivate _caseCount = 0;\nprivate _lastCaseCheckIndex = 0;\nprivate _lastCasesMatched = false;\nprivate _ngSwitch: any;\n/**\n * @param {?} newValue\n * @return {?}\n */\nset ngSwitch(newValue: any) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_addCase(): number { return this._caseCount++; }\n/**\n * \\@internal\n * @param {?} view\n * @return {?}\n */\n_addDefault(view: SwitchView) {\n    if (!this._defaultViews) {\n      this._defaultViews = [];\n    }\n    this._defaultViews.push(view);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_matchCase(value: any): boolean {\n    const /** @type {?} */ matched = value == this._ngSwitch;\n    this._lastCasesMatched = this._lastCasesMatched || matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n/**\n * @param {?} useDefault\n * @return {?}\n */\nprivate _updateDefaultCases(useDefault: boolean) {\n    if (this._defaultViews && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (let /** @type {?} */ i = 0; i < this._defaultViews.length; i++) {\n        const /** @type {?} */ defaultView = this._defaultViews[i];\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitch]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitch': [{ type: Input },],\n};\n}\n\nfunction NgSwitch_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitch.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitch.ctorParameters;\n/** @type {?} */\nNgSwitch.propDecorators;\n/** @type {?} */\nNgSwitch.prototype._defaultViews;\n/** @type {?} */\nNgSwitch.prototype._defaultUsed;\n/** @type {?} */\nNgSwitch.prototype._caseCount;\n/** @type {?} */\nNgSwitch.prototype._lastCaseCheckIndex;\n/** @type {?} */\nNgSwitch.prototype._lastCasesMatched;\n/** @type {?} */\nNgSwitch.prototype._ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgSwitch} when the\n *             given expression evaluate to respectively the same/different value as the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * </container-element>\n * ```\n * \\@description \n * \n * Insert the sub-tree when the expression evaluates to the same value as the enclosing switch\n * expression.\n * \n * If multiple match expressions match the switch expression value, all of them are displayed.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchCase implements DoCheck {\nprivate _view: SwitchView;\n\n  \n  ngSwitchCase: any;\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\nprivate ngSwitch: NgSwitch) {\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n/**\n * @return {?}\n */\nngDoCheck() { this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitchCase': [{ type: Input },],\n};\n}\n\nfunction NgSwitchCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchCase.ctorParameters;\n/** @type {?} */\nNgSwitchCase.propDecorators;\n/** @type {?} */\nNgSwitchCase.prototype._view;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitchCase;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a view that is added to the parent {\\@link NgSwitch} when no case expressions\n * match the\n *             switch expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-other-element *ngSwitchDefault>...</some-other-element>\n * </container-element>\n * ```\n * \n * \\@description \n * \n * Insert the sub-tree when no case expressions evaluate to the same value as the enclosing switch\n * expression.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchDefault {\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\n       ngSwitch: NgSwitch) {\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchDefault]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgSwitchDefault_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchDefault.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchDefault.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentFactoryResolver, ComponentRef, Directive, Injector, Input, NgModuleFactory, NgModuleRef, OnChanges, OnDestroy, Provider, SimpleChanges, Type, ViewContainerRef} from '@angular/core';\n/**\n * Instantiates a single {\\@link Component} type and inserts its Host View into current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n * \n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will get destroyed.\n * \n * ### Fine tune control\n * \n * You can control the component creation process by using the following optional attributes:\n * \n * * `ngComponentOutletInjector`: Optional custom {\\@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n * \n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if exists.\n * \n * * `ngComponentOutletNgModuleFactory`: Optional module factory to allow dynamically loading other\n * module, then load a component from that module.\n * \n * ### Syntax\n * \n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n * \n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n * \n * Customized ngModuleFactory\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModuleFactory: moduleFactory;\">\n * </ng-container>\n * ```\n * ## Example\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n * \n * A more complete example with additional options:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n * A more complete example with ngModuleFactory:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='NgModuleFactoryExample'}\n * \n * \\@experimental\n */\nexport class NgComponentOutlet implements OnChanges, OnDestroy {\n   ngComponentOutlet: Type<any>;\n   ngComponentOutletInjector: Injector;\n   ngComponentOutletContent: any[][];\n   ngComponentOutletNgModuleFactory: NgModuleFactory<any>;\nprivate _componentRef: ComponentRef<any>|null = null;\nprivate _moduleRef: NgModuleRef<any>|null = null;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    this._viewContainerRef.clear();\n    this._componentRef = null;\n\n    if (this.ngComponentOutlet) {\n      const /** @type {?} */ elInjector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n      if (changes['ngComponentOutletNgModuleFactory']) {\n        if (this._moduleRef) this._moduleRef.destroy();\n\n        if (this.ngComponentOutletNgModuleFactory) {\n          const /** @type {?} */ parentModule = elInjector.get(NgModuleRef);\n          this._moduleRef = this.ngComponentOutletNgModuleFactory.create(parentModule.injector);\n        } else {\n          this._moduleRef = null;\n        }\n      }\n\n      const /** @type {?} */ componentFactoryResolver = this._moduleRef ? this._moduleRef.componentFactoryResolver :\n                                                         elInjector.get(ComponentFactoryResolver);\n\n      const /** @type {?} */ componentFactory =\n          componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);\n\n      this._componentRef = this._viewContainerRef.createComponent(\n          componentFactory, this._viewContainerRef.length, elInjector,\n          this.ngComponentOutletContent);\n    }\n  }\n/**\n * @return {?}\n */\nngOnDestroy() {\n    if (this._moduleRef) this._moduleRef.destroy();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngComponentOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngComponentOutlet': [{ type: Input },],\n'ngComponentOutletInjector': [{ type: Input },],\n'ngComponentOutletContent': [{ type: Input },],\n'ngComponentOutletNgModuleFactory': [{ type: Input },],\n};\n}\n\nfunction NgComponentOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgComponentOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgComponentOutlet.ctorParameters;\n/** @type {?} */\nNgComponentOutlet.propDecorators;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutlet;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletInjector;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletContent;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletNgModuleFactory;\n/** @type {?} */\nNgComponentOutlet.prototype._componentRef;\n/** @type {?} */\nNgComponentOutlet.prototype._moduleRef;\n/** @type {?} */\nNgComponentOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, IterableChanges, IterableDiffer, IterableDiffers, KeyValueChanges, KeyValueDiffer, KeyValueDiffers, <PERSON><PERSON><PERSON>, ɵisListLikeIterable as isListLikeIterable, ɵstringify as stringify} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds and removes CSS classes on an HTML element.\n * \n * \\@howToUse \n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n * \n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n * \n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n * \n * \\@stable\n */\nexport class NgClass implements DoCheck {\nprivate _iterableDiffer: IterableDiffer<string>|null;\nprivate _keyValueDiffer: KeyValueDiffer<string, any>|null;\nprivate _initialClasses: string[] = [];\nprivate _rawClass: string[]|Set<string>|{[klass: string]: any};\n/**\n * @param {?} _iterableDiffers\n * @param {?} _keyValueDiffers\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _iterableDiffers: IterableDiffers,\nprivate _keyValueDiffers: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset klass(v: string) {\n    this._applyInitialClasses(true);\n    this._initialClasses = typeof v === 'string' ? v.split(/\\s+/) : [];\n    this._applyInitialClasses(false);\n    this._applyClasses(this._rawClass, false);\n  }\n/**\n * @param {?} v\n * @return {?}\n */\nset ngClass(v: string|string[]|Set<string>|{[klass: string]: any}) {\n    this._cleanupClasses(this._rawClass);\n\n    this._iterableDiffer = null;\n    this._keyValueDiffer = null;\n\n    this._rawClass = typeof v === 'string' ? v.split(/\\s+/) : v;\n\n    if (this._rawClass) {\n      if (isListLikeIterable(this._rawClass)) {\n        this._iterableDiffer = this._iterableDiffers.find(this._rawClass).create();\n      } else {\n        this._keyValueDiffer = this._keyValueDiffers.find(this._rawClass).create();\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._iterableDiffer) {\n      const /** @type {?} */ iterableChanges = this._iterableDiffer.diff( /** @type {?} */((this._rawClass as string[])));\n      if (iterableChanges) {\n        this._applyIterableChanges(iterableChanges);\n      }\n    } else if (this._keyValueDiffer) {\n      const /** @type {?} */ keyValueChanges = this._keyValueDiffer.diff( /** @type {?} */((this._rawClass as{[k: string]: any})));\n      if (keyValueChanges) {\n        this._applyKeyValueChanges(keyValueChanges);\n      }\n    }\n  }\n/**\n * @param {?} rawClassVal\n * @return {?}\n */\nprivate _cleanupClasses(rawClassVal: string[]|{[klass: string]: any}): void {\n    this._applyClasses(rawClassVal, true);\n    this._applyInitialClasses(false);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyKeyValueChanges(changes: KeyValueChanges<string, any>): void {\n    changes.forEachAddedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachRemovedItem((record) => {\n      if (record.previousValue) {\n        this._toggleClass(record.key, false);\n      }\n    });\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyIterableChanges(changes: IterableChanges<string>): void {\n    changes.forEachAddedItem((record) => {\n      if (typeof record.item === 'string') {\n        this._toggleClass(record.item, true);\n      } else {\n        throw new Error(\n            `NgClass can only toggle CSS classes expressed as strings, got ${stringify(record.item)}`);\n      }\n    });\n\n    changes.forEachRemovedItem((record) => this._toggleClass(record.item, false));\n  }\n/**\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyInitialClasses(isCleanup: boolean) {\n    this._initialClasses.forEach(klass => this._toggleClass(klass, !isCleanup));\n  }\n/**\n * @param {?} rawClassVal\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyClasses(\n      rawClassVal: string[]|Set<string>|{[klass: string]: any}, isCleanup: boolean) {\n    if (rawClassVal) {\n      if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n        ( /** @type {?} */((<any>rawClassVal))).forEach((klass: string) => this._toggleClass(klass, !isCleanup));\n      } else {\n        Object.keys(rawClassVal).forEach(klass => {\n          if (rawClassVal[klass] != null) this._toggleClass(klass, !isCleanup);\n        });\n      }\n    }\n  }\n/**\n * @param {?} klass\n * @param {?} enabled\n * @return {?}\n */\nprivate _toggleClass(klass: string, enabled: any): void {\n    klass = klass.trim();\n    if (klass) {\n      klass.split(/\\s+/g).forEach(\n          klass => { this._renderer.setElementClass(this._ngEl.nativeElement, klass, !!enabled); });\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngClass]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: IterableDiffers, },\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'klass': [{ type: Input, args: ['class', ] },],\n'ngClass': [{ type: Input },],\n};\n}\n\nfunction NgClass_tsickle_Closure_declarations() {\n/** @type {?} */\nNgClass.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgClass.ctorParameters;\n/** @type {?} */\nNgClass.propDecorators;\n/** @type {?} */\nNgClass.prototype._iterableDiffer;\n/** @type {?} */\nNgClass.prototype._keyValueDiffer;\n/** @type {?} */\nNgClass.prototype._initialClasses;\n/** @type {?} */\nNgClass.prototype._rawClass;\n/** @type {?} */\nNgClass.prototype._iterableDiffers;\n/** @type {?} */\nNgClass.prototype._keyValueDiffers;\n/** @type {?} */\nNgClass.prototype._ngEl;\n/** @type {?} */\nNgClass.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, LOCALE_ID} from '@angular/core';\n/**\n * \\@experimental\n * @abstract\n */\nexport abstract class NgLocalization {\n/**\n * @abstract\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any) {} }\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n * \n * \\@internal\n * @param {?} value\n * @param {?} cases\n * @param {?} ngLocalization\n * @return {?}\n */\nexport function getPluralCategory(\n    value: number, cases: string[], ngLocalization: NgLocalization): string {\n  let /** @type {?} */ key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n */\nexport class NgLocaleLocalization extends NgLocalization {\n/**\n * @param {?} locale\n */\nconstructor( protected locale: string) { super(); }\n/**\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any): string {\n    const /** @type {?} */ plural = getPluralCase(this.locale, value);\n\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction NgLocaleLocalization_tsickle_Closure_declarations() {\n/** @type {?} */\nNgLocaleLocalization.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgLocaleLocalization.ctorParameters;\n/** @type {?} */\nNgLocaleLocalization.prototype.locale;\n}\n\nexport type Plural = number;\nexport let Plural: any = {};\nPlural.Zero = 0;\nPlural.One = 1;\nPlural.Two = 2;\nPlural.Few = 3;\nPlural.Many = 4;\nPlural.Other = 5;\nPlural[Plural.Zero] = \"Zero\";\nPlural[Plural.One] = \"One\";\nPlural[Plural.Two] = \"Two\";\nPlural[Plural.Few] = \"Few\";\nPlural[Plural.Many] = \"Many\";\nPlural[Plural.Other] = \"Other\";\n\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n * @param {?} locale\n * @param {?} nLike\n * @return {?}\n */\nexport function getPluralCase(locale: string, nLike: number | string): Plural {\n  // TODO(vicb): lazy compute\n  if (typeof nLike === 'string') {\n    nLike = parseInt( /** @type {?} */((<string>nLike)), 10);\n  }\n  const /** @type {?} */ n: number = /** @type {?} */(( nLike as number));\n  const /** @type {?} */ nDecimal = n.toString().replace(/^[^.]*\\.?/, '');\n  const /** @type {?} */ i = Math.floor(Math.abs(n));\n  const /** @type {?} */ v = nDecimal.length;\n  const /** @type {?} */ f = parseInt(nDecimal, 10);\n  const /** @type {?} */ t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n\n  const /** @type {?} */ lang = locale.split('-')[0].toLowerCase();\n\n  switch (lang) {\n    case 'af':\n    case 'asa':\n    case 'az':\n    case 'bem':\n    case 'bez':\n    case 'bg':\n    case 'brx':\n    case 'ce':\n    case 'cgg':\n    case 'chr':\n    case 'ckb':\n    case 'ee':\n    case 'el':\n    case 'eo':\n    case 'es':\n    case 'eu':\n    case 'fo':\n    case 'fur':\n    case 'gsw':\n    case 'ha':\n    case 'haw':\n    case 'hu':\n    case 'jgo':\n    case 'jmc':\n    case 'ka':\n    case 'kk':\n    case 'kkj':\n    case 'kl':\n    case 'ks':\n    case 'ksb':\n    case 'ky':\n    case 'lb':\n    case 'lg':\n    case 'mas':\n    case 'mgo':\n    case 'ml':\n    case 'mn':\n    case 'nb':\n    case 'nd':\n    case 'ne':\n    case 'nn':\n    case 'nnh':\n    case 'nyn':\n    case 'om':\n    case 'or':\n    case 'os':\n    case 'ps':\n    case 'rm':\n    case 'rof':\n    case 'rwk':\n    case 'saq':\n    case 'seh':\n    case 'sn':\n    case 'so':\n    case 'sq':\n    case 'ta':\n    case 'te':\n    case 'teo':\n    case 'tk':\n    case 'tr':\n    case 'ug':\n    case 'uz':\n    case 'vo':\n    case 'vun':\n    case 'wae':\n    case 'xog':\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ak':\n    case 'ln':\n    case 'mg':\n    case 'pa':\n    case 'ti':\n      if (n === Math.floor(n) && n >= 0 && n <= 1) return Plural.One;\n      return Plural.Other;\n    case 'am':\n    case 'as':\n    case 'bn':\n    case 'fa':\n    case 'gu':\n    case 'hi':\n    case 'kn':\n    case 'mr':\n    case 'zu':\n      if (i === 0 || n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ar':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return Plural.Many;\n      return Plural.Other;\n    case 'ast':\n    case 'ca':\n    case 'de':\n    case 'en':\n    case 'et':\n    case 'fi':\n    case 'fy':\n    case 'gl':\n    case 'it':\n    case 'nl':\n    case 'sv':\n    case 'sw':\n    case 'ur':\n    case 'yi':\n      if (i === 1 && v === 0) return Plural.One;\n      return Plural.Other;\n    case 'be':\n      if (n % 10 === 1 && !(n % 100 === 11)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 4 &&\n          !(n % 100 >= 12 && n % 100 <= 14))\n        return Plural.Few;\n      if (n % 10 === 0 || n % 10 === Math.floor(n % 10) && n % 10 >= 5 && n % 10 <= 9 ||\n          n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'br':\n      if (n % 10 === 1 && !(n % 100 === 11 || n % 100 === 71 || n % 100 === 91)) return Plural.One;\n      if (n % 10 === 2 && !(n % 100 === 12 || n % 100 === 72 || n % 100 === 92)) return Plural.Two;\n      if (n % 10 === Math.floor(n % 10) && (n % 10 >= 3 && n % 10 <= 4 || n % 10 === 9) &&\n          !(n % 100 >= 10 && n % 100 <= 19 || n % 100 >= 70 && n % 100 <= 79 ||\n            n % 100 >= 90 && n % 100 <= 99))\n        return Plural.Few;\n      if (!(n === 0) && n % 1e6 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'bs':\n    case 'hr':\n    case 'sr':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11))\n        return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n              !(i % 100 >= 12 && i % 100 <= 14) ||\n          f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 &&\n              !(f % 100 >= 12 && f % 100 <= 14))\n        return Plural.Few;\n      return Plural.Other;\n    case 'cs':\n    case 'sk':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0) return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'cy':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === 3) return Plural.Few;\n      if (n === 6) return Plural.Many;\n      return Plural.Other;\n    case 'da':\n      if (n === 1 || !(t === 0) && (i === 0 || i === 1)) return Plural.One;\n      return Plural.Other;\n    case 'dsb':\n    case 'hsb':\n      if (v === 0 && i % 100 === 1 || f % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2 || f % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 ||\n          f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ff':\n    case 'fr':\n    case 'hy':\n    case 'kab':\n      if (i === 0 || i === 1) return Plural.One;\n      return Plural.Other;\n    case 'fil':\n      if (v === 0 && (i === 1 || i === 2 || i === 3) ||\n          v === 0 && !(i % 10 === 4 || i % 10 === 6 || i % 10 === 9) ||\n          !(v === 0) && !(f % 10 === 4 || f % 10 === 6 || f % 10 === 9))\n        return Plural.One;\n      return Plural.Other;\n    case 'ga':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === Math.floor(n) && n >= 3 && n <= 6) return Plural.Few;\n      if (n === Math.floor(n) && n >= 7 && n <= 10) return Plural.Many;\n      return Plural.Other;\n    case 'gd':\n      if (n === 1 || n === 11) return Plural.One;\n      if (n === 2 || n === 12) return Plural.Two;\n      if (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19)) return Plural.Few;\n      return Plural.Other;\n    case 'gv':\n      if (v === 0 && i % 10 === 1) return Plural.One;\n      if (v === 0 && i % 10 === 2) return Plural.Two;\n      if (v === 0 &&\n          (i % 100 === 0 || i % 100 === 20 || i % 100 === 40 || i % 100 === 60 || i % 100 === 80))\n        return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'he':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === 2 && v === 0) return Plural.Two;\n      if (v === 0 && !(n >= 0 && n <= 10) && n % 10 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'is':\n      if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return Plural.One;\n      return Plural.Other;\n    case 'ksh':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'kw':\n    case 'naq':\n    case 'se':\n    case 'smn':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      return Plural.Other;\n    case 'lag':\n      if (n === 0) return Plural.Zero;\n      if ((i === 0 || i === 1) && !(n === 0)) return Plural.One;\n      return Plural.Other;\n    case 'lt':\n      if (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 9 &&\n          !(n % 100 >= 11 && n % 100 <= 19))\n        return Plural.Few;\n      if (!(f === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'lv':\n    case 'prg':\n      if (n % 10 === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19 ||\n          v === 2 && f % 100 === Math.floor(f % 100) && f % 100 >= 11 && f % 100 <= 19)\n        return Plural.Zero;\n      if (n % 10 === 1 && !(n % 100 === 11) || v === 2 && f % 10 === 1 && !(f % 100 === 11) ||\n          !(v === 2) && f % 10 === 1)\n        return Plural.One;\n      return Plural.Other;\n    case 'mk':\n      if (v === 0 && i % 10 === 1 || f % 10 === 1) return Plural.One;\n      return Plural.Other;\n    case 'mt':\n      if (n === 1) return Plural.One;\n      if (n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 10)\n        return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19) return Plural.Many;\n      return Plural.Other;\n    case 'pl':\n      if (i === 1 && v === 0) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && !(i === 1) && i % 10 === Math.floor(i % 10) && i % 10 >= 0 && i % 10 <= 1 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 12 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'pt':\n      if (n === Math.floor(n) && n >= 0 && n <= 2 && !(n === 2)) return Plural.One;\n      return Plural.Other;\n    case 'ro':\n      if (i === 1 && v === 0) return Plural.One;\n      if (!(v === 0) || n === 0 ||\n          !(n === 1) && n % 100 === Math.floor(n % 100) && n % 100 >= 1 && n % 100 <= 19)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ru':\n    case 'uk':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11)) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && i % 10 === 0 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 11 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'shi':\n      if (i === 0 || n === 1) return Plural.One;\n      if (n === Math.floor(n) && n >= 2 && n <= 10) return Plural.Few;\n      return Plural.Other;\n    case 'si':\n      if (n === 0 || n === 1 || i === 0 && f === 1) return Plural.One;\n      return Plural.Other;\n    case 'sl':\n      if (v === 0 && i % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || !(v === 0))\n        return Plural.Few;\n      return Plural.Other;\n    case 'tzm':\n      if (n === Math.floor(n) && n >= 0 && n <= 1 || n === Math.floor(n) && n >= 11 && n <= 99)\n        return Plural.One;\n      return Plural.Other;\n    // When there is no specification, the default is always \"other\"\n    // Spec: http://cldr.unicode.org/index/cldr-spec/plural-rules\n    // > other (required—general plural form — also used if the language only has a single form)\n    default:\n      return Plural.Other;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL for storing application location data.\n * \\@description \n * `PathLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n * \n * If you're using `PathLocationStrategy`, you must provide a {\\@link APP_BASE_HREF}\n * or add a base element to the document. This URL prefix that will be preserved\n * when generating and recognizing URLs.\n * \n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * Similarly, if you add `<base href='/my/app'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class PathLocationStrategy extends LocationStrategy {\nprivate _baseHref: string;\n/**\n * @param {?} _platformLocation\n * @param {?=} href\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        href?: string) {\n    super();\n\n    if (href == null) {\n      href = this._platformLocation.getBaseHrefFromDOM();\n    }\n\n    if (href == null) {\n      throw new Error(\n          `No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.`);\n    }\n\n    this._baseHref = href;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    return Location.joinWithSlash(this._baseHref, internal);\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    const /** @type {?} */ pathname = this._platformLocation.pathname +\n        Location.normalizeQueryParams(this._platformLocation.search);\n    const /** @type {?} */ hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction PathLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nPathLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPathLocationStrategy.ctorParameters;\n/** @type {?} */\nPathLocationStrategy.prototype._baseHref;\n/** @type {?} */\nPathLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener} from './platform_location';\nexport {LocationStrategy,APP_BASE_HREF} from './location_strategy';\nexport {HashLocationStrategy} from './hash_location_strategy';\nexport {PathLocationStrategy} from './path_location_strategy';\nexport {PopStateEvent,Location} from './location';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL hash for storing application location data.\n * \\@description \n * `HashLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n * \n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class HashLocationStrategy extends LocationStrategy {\nprivate _baseHref: string = '';\n/**\n * @param {?} _platformLocation\n * @param {?=} _baseHref\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        _baseHref?: string) {\n    super();\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    let /** @type {?} */ path = this._platformLocation.hash;\n    if (path == null) path = '#';\n\n    return path.length > 0 ? path.substring(1) : path;\n  }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    const /** @type {?} */ url = Location.joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? ('#' + url) : url;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url: string|null =\n        this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.pushState(state, title, url);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url = this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.replaceState(state, title, url);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction HashLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nHashLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHashLocationStrategy.ctorParameters;\n/** @type {?} */\nHashLocationStrategy.prototype._baseHref;\n/** @type {?} */\nHashLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n * \n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n * \n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n * \n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class PlatformLocation {\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHrefFromDOM() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\npathname() {}\n/**\n * @abstract\n * @return {?}\n */\nsearch() {}\n/**\n * @abstract\n * @return {?}\n */\nhash() {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n}\n/**\n * \\@whatItDoes indicates when a location is initialized\n * \\@experimental\n */\nexport const LOCATION_INITIALIZED = new InjectionToken<Promise<any>>('Location Initialized');\n\n/**\n * A serializable version of the event from onPopState or onHashChange\n *\n * @experimental\n */\nexport interface LocationChangeEvent { type: string; }\n\n/**\n * @experimental\n */\nexport interface LocationChangeListener { (e: LocationChangeEvent): any; }\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\nimport {LocationChangeListener} from './platform_location';\n/**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. Angular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n * \n * This is used under the hood of the {\\@link Location} service.\n * \n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n * \n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n * \n * See these two classes for more.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class LocationStrategy {\n/**\n * @abstract\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash?: boolean) {}\n/**\n * @abstract\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHref() {}\n}\n/**\n * The `APP_BASE_HREF` token represents the base href to be used with the\n * {\\@link PathLocationStrategy}.\n * \n * If you're using {\\@link PathLocationStrategy}, you must provide a provider to a string\n * representing the URL prefix that should be preserved when generating and recognizing\n * URLs.\n * \n * ### Example\n * \n * ```typescript\n * import {Component, NgModule} from '\\@angular/core';\n * import {APP_BASE_HREF} from '\\@angular/common';\n * \n * \\@NgModule({ \n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n * \n * \\@stable\n */\nexport const APP_BASE_HREF = new InjectionToken<string>('appBaseHref');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {EventEmitter, Injectable} from '@angular/core';\n\nimport {LocationStrategy} from './location_strategy';\n\n/** @experimental */\nexport interface PopStateEvent {\n  pop?: boolean;\n  type?: string;\n  url?: string;\n}\n/**\n * \\@whatItDoes `Location` is a service that applications can use to interact with a browser's URL.\n * \\@description \n * Depending on which {\\@link LocationStrategy} is used, `Location` will either persist\n * to the URL's path or the URL's hash segment.\n * \n * Note: it's better to use {\\@link Router#navigate} service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n * \n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n * \n * ### Example\n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \\@stable\n */\nexport class Location {\n/**\n * \\@internal\n */\n_subject: EventEmitter<any> = new EventEmitter();\n/**\n * \\@internal\n */\n_baseHref: string;\n/**\n * \\@internal\n */\n_platformStrategy: LocationStrategy;\n/**\n * @param {?} platformStrategy\n */\nconstructor(platformStrategy: LocationStrategy) {\n    this._platformStrategy = platformStrategy;\n    const browserBaseHref = this._platformStrategy.getBaseHref();\n    this._baseHref = Location.stripTrailingSlash(_stripIndexHtml(browserBaseHref));\n    this._platformStrategy.onPopState((ev) => {\n      this._subject.emit({\n        'url': this.path(true),\n        'pop': true,\n        'type': ev.type,\n      });\n    });\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    return this.normalize(this._platformStrategy.path(includeHash));\n  }\n/**\n * Normalizes the given path and compares to the current normalized path.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nisCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    return this.path() == this.normalize(path + Location.normalizeQueryParams(query));\n  }\n/**\n * Given a string representing a URL, returns the normalized URL path without leading or\n * trailing slashes.\n * @param {?} url\n * @return {?}\n */\nnormalize(url: string): string {\n    return Location.stripTrailingSlash(_stripBaseHref(this._baseHref, _stripIndexHtml(url)));\n  }\n/**\n * Given a string representing a URL, returns the platform-specific external URL path.\n * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n * @param {?} url\n * @return {?}\n */\nprepareExternalUrl(url: string): string {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._platformStrategy.prepareExternalUrl(url);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and pushes a\n * new item onto the platform's history.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\ngo(path: string, query: string = ''): void {\n    this._platformStrategy.pushState(null, '', path, query);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and replaces\n * the top item on the platform's history stack.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nreplaceState(path: string, query: string = ''): void {\n    this._platformStrategy.replaceState(null, '', path, query);\n  }\n/**\n * Navigates forward in the platform's history.\n * @return {?}\n */\nforward(): void { this._platformStrategy.forward(); }\n/**\n * Navigates back in the platform's history.\n * @return {?}\n */\nback(): void { this._platformStrategy.back(); }\n/**\n * Subscribe to the platform's `popState` events.\n * @param {?} onNext\n * @param {?=} onThrow\n * @param {?=} onReturn\n * @return {?}\n */\nsubscribe(\n      onNext: (value: PopStateEvent) => void, onThrow?: ((exception: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n/**\n * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as\n * is.\n * @param {?} params\n * @return {?}\n */\npublic static normalizeQueryParams(params: string): string {\n    return params && params[0] !== '?' ? '?' + params : params;\n  }\n/**\n * Given 2 parts of a url, join them with a slash if needed.\n * @param {?} start\n * @param {?} end\n * @return {?}\n */\npublic static joinWithSlash(start: string, end: string): string {\n    if (start.length == 0) {\n      return end;\n    }\n    if (end.length == 0) {\n      return start;\n    }\n    let /** @type {?} */ slashes = 0;\n    if (start.endsWith('/')) {\n      slashes++;\n    }\n    if (end.startsWith('/')) {\n      slashes++;\n    }\n    if (slashes == 2) {\n      return start + end.substring(1);\n    }\n    if (slashes == 1) {\n      return start + end;\n    }\n    return start + '/' + end;\n  }\n/**\n * If url has a trailing slash, remove it, otherwise return url as is. This\n * method looks for the first occurence of either #, ?, or the end of the\n * line as `/` characters after any of these should not be replaced.\n * @param {?} url\n * @return {?}\n */\npublic static stripTrailingSlash(url: string): string {\n    const /** @type {?} */ match = url.match(/#|\\?|$/);\n    const /** @type {?} */ pathEndIdx = match && match.index || url.length;\n    const /** @type {?} */ droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n    return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: LocationStrategy, },\n];\n}\n\nfunction Location_tsickle_Closure_declarations() {\n/** @type {?} */\nLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLocation.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._subject;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._baseHref;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._platformStrategy;\n}\n\n/**\n * @param {?} baseHref\n * @param {?} url\n * @return {?}\n */\nfunction _stripBaseHref(baseHref: string, url: string): string {\n  return baseHref && url.startsWith(baseHref) ? url.substring(baseHref.length) : url;\n}\n/**\n * @param {?} url\n * @return {?}\n */\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["COMMON_PIPES", "AsyncPipe", "UpperCasePipe", "LowerCasePipe", "SlicePipe", "CommonModule", "declarations", "COMMON_DIRECTIVES", "value", "I18nSelectPipe", "prototype", "transform", "mapping", "hasOwnProperty", "I18nPluralPipe", "pluralMap", "key", "replace", "_INTERPOLATION_REGEXP", "toString", "DatePipe", "pattern", "date", "isBlank", "_a", "split", "map", "val", "parseInt", "y", "m", "d", "Date", "isDate", "invalidPipeArgumentError", "_ALIASES", "medium", "short", "shortDate", "mediumTime", "toLowerCase", "TitleCasePipe", "word", "titleCaseWord", "join", "toUpperCase", "type", "_angular_core", "<PERSON><PERSON>", "args", "name", "NumberFormatStyle", "Decimal", "NumberFormatter", "format", "num", "locale", "style", "opts", "minimumIntegerDigits", "minimumFractionDigits", "maximumFractionDigits", "currency", "currencyAsSymbol", "options", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "currencyDisplay", "Intl", "NumberFormat", "DATE_FORMATS_SPLIT", "PATTERN_ALIASES", "yMMMdjms", "datePartGetterFactory", "combine", "digitCondition", "nameCondition", "yMdjm", "yMMMMEEEEd", "yMMMMd", "yMMMd", "jms", "jm", "DATE_FORMATS", "yyyy", "yy", "MMMM", "MMM", "MM", "M", "LLLL", "L", "dd", "h", "hourExtractor", "hour12Modify", "jj", "ss", "digitModifier", "s", "sss", "EEE", "EE", "a", "hourClockExtractor", "formatNumber", "DecimalPipe", "this", "_locale", "digits", "Percent<PERSON><PERSON>e", "Percent", "currencyCode", "C<PERSON><PERSON>cyPipe", "symbolDisplay", "Ng<PERSON><PERSON>", "NgComponentOutlet", "NgTemplateOutlet", "NgSwitch", "_ref", "_latestValue", "obj", "_obj", "_latestReturnedValue", "_subscribe", "_promiseStrategy", "ngOnChanges", "changes", "_viewRef", "ngTemplateOutlet", "Directive", "selector", "NgStyle", "_differs", "_ngEl", "_renderer", "Object", "defineProperty", "set", "v", "_differ", "find", "create", "configurable", "diff", "_ngStyle", "setElementStyle", "nativeElement", "Ng<PERSON><PERSON><PERSON>", "_updateView", "_active<PERSON>iew", "view", "ngPlural", "addCase", "isANumber", "SwitchView", "viewContainer", "template", "NgPluralCase", "ctorParameters", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_viewContainer", "_template", "get", "_trackByFn", "console", "fn", "currentValue", "e", "_applyChanges", "_this", "insertTuples", "forEachOperation", "item", "adjustedPreviousIndex", "currentIndex", "previousIndex", "createEmbeddedView", "NgForOfContext", "ngForOf", "tuple", "RecordViewTuple", "push", "remove", "move", "i", "length", "_perViewChange", "record", "context", "$implicit", "propDecorators", "NgIf", "_thenTemplateRef", "_context", "ngIf", "condition", "_thenViewRef", "_elseViewRef", "clear", "_elseTemplateRef", "TemplateRef", "ngIfElse", "Input", "NgIfContext", "created", "_created", "_defaultUsed", "newValue", "_updateDefaultCases", "_matchCase", "_lastCasesMatched", "useDefault", "_defaultViews", "defaultView", "enforceState", "NgSwitchCase", "ngDoCheck", "_view", "ngSwitch", "ngSwitchCase", "_addDefault", "templateRef", "_viewContainerRef", "_componentRef", "ngComponentOutlet", "elInjector", "ngComponentOutletInjector", "parentInjector", "_moduleRef", "destroy", "ngComponentOutletNgModuleFactory", "parentModule", "NgModuleRef", "injector", "componentFactoryResolver", "ngOnDestroy", "_iterable<PERSON><PERSON><PERSON>", "_key<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_applyInitialClasses", "_applyClasses", "_rawClass", "_cleanupClasses", "_itera<PERSON><PERSON><PERSON><PERSON>", "_key<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iterableChanges", "key<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forEachRemovedItem", "_applyIterableChanges", "rawClassVal", "isCleanup", "Array", "isArray", "Set", "_toggleClass", "klass", "enabled", "Iterable<PERSON><PERSON><PERSON>", "__extends", "NgLocaleLocalization", "_super", "getPluralCategory", "plural", "getPluralCase", "Plural", "Zero", "One", "Few", "Injectable", "decorators", "Inject", "LOCALE_ID", "PathLocationStrategy", "_platformLocation", "href", "onHashChange", "back", "_baseHref", "HashLocationStrategy", "getBaseHref", "replaceState", "state", "title", "path", "queryParams", "inner", "dateF<PERSON><PERSON><PERSON>", "cache<PERSON>ey", "parts", "DATE_FORMATTER_CACHE", "match", "exec", "_format", "concat", "slice", "pop", "isNumeric", "pipe", "minInt", "minFraction", "maxFraction", "_NUMBER_FORMAT_REGEXP", "Error", "parseIntAutoRadix", "result", "text", "isoStringToDate", "tzHour", "tzMin", "dateSetter", "setUTCFullYear", "setFullYear", "timeSetter", "setUTCHours", "setHours", "toInt", "extendStatics", "setPrototypeOf", "__proto__", "b", "p", "PlatformLocation", "pathname", "pushState", "url", "LocationStrategy", "Location", "platformStrategy", "_subject", "EventEmitter", "_platformStrategy", "joinWithSlash", "start", "end", "slashes", "endsWith", "startsWith", "droppedSlashIdx", "pathEndIdx", "exports", "module", "factory", "require", "cases", "ngLocalization", "indexOf", "nLike", "n", "nDecimal", "Math", "floor", "abs", "f", "t", "lang", "Other", "Two", "Many"], "mappings": ";;;;;0BwBAA,gBAAAmV,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,2jBJ+CA,QAAAjF,mBAAA7P,MAAA+U,MAAAC,gBACA,GAAAxU,KAAA,IAAAR,KAEA,IAAA+U,MAAAE,QAAAzU,MAAA,EACA,MAAAA,iMAyFA,QAAAuP,eAAA/M,OAAAkS,OAIA,gBAAAA,SACAA,MAAA9T,SAAA,MAAA,IAEA,IAAA+T,GAAA,MACAC,SAAAD,EAAAxU,WAAAF,QAAA,YAAA,IACAwK,EAAAoK,KAAAC,MAAAD,KAAAE,IAAAJ,IACAhN,EAAAiN,SAAAlK,OACAsK,EAAApU,SAAAgU,SAAA,IACAK,EAAArU,SAAA+T,EAAAxU,WAAAF,QAAA,iBAAA,IAAA,KAAA,EACAiV,KAAA1S,OAAA/B,MAAA,KAAA,GAAAe,aACA,QAAA0T,MACI,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCT,IAAA,KAvCA,IAAA,MAwCA,IAAA,KACI,IAvCK,KAwCL,IAvCK,KAwCT,IAAA,KACA,IAAA,KACA,IAAA,MACA,IAAA,MAvCA,IAAA,MAwCA,MAAA,KAAAP,EACAnF,OAAAE,IACAF,OAAA2F,KACA,KAAA,KACA,IAAA,KACA,IAAA,KACI,IAvCK,KAwCL,IAvCK,KAwCT,MAAAR,KAAAE,KAAAC,MAAAH,IAAAA,GAAA,GAAAA,GAAA,EACAnF,OAAAE,IACAF,OAAA2F,KAvCA,KAAA,KAwCA,IAAA,KACI,IAvCK,KAwCT,IAAA,KAvCA,IAAA,KAwCA,IAAA,KAvCA,IAAA,KAwCA,IAAA,KAvCA,IAAA,KAwCA,MAAA,KAAU1K,GAAV,IAAAkK,EAvCmFnF,OAAnFE,IAwCAF,OAAA2F,KAvCA,KAAA,KAwCA,MAAA,KAAAR,EACAnF,OAAAC,KACA,IAAAkF,EACAnF,OAAAE,IACA,IAAAiF,EACAnF,OAAA4F,IACAT,EAAA,MAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GACAnF,OAAAG,IACAgF,EAAA,MAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GACAnF,OAAA6F,KACA7F,OAAA2F,KACI,KAvCK,MAwCL,IAvCK,KAwCL,IAvCK,KAwCL,IAvCK,KAwCT,IAAA,KAvCA,IAAA,KAwCA,IAAA,KACI,IAvCK,KAwCT,IAAA,KAvCA,IAAA,KAwCA,IAAA,KANA,IAAA,KAQA,IAAA,KACA,IAAA,KACA,MAAA,KAAU1K,GAvCiB,IAuC3B9C,EACA6H,OAAAE,IAtCaF,OAuCb2F,KACA,KAAA,KACM,MAPGR,GAhCK,KAAO,GAuCrBA,EAvCgC,MAuChC,GAvCwFnF,OAAxFE,IAwCUiF,EAAV,KAAAE,KAAAC,MAAAH,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KAvCAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IAwCAnF,OAAAG,IANSgF,EAAT,KAAA,GAAAA,EAAA,KAAAE,KAhCwCC,MAgCxCH,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,GACAA,EAAA,MAAAE,KAhCuBC,MAgCvBH,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GAQAnF,OAAA6F,KACA7F,OAAA2F,KAvCA,KAAA,KAwCA,MAAAR,GAAA,KAAA,GAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,GACAnF,OAAAE,IACAiF,EAAA,KAAA,GAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,GACAnF,OAAA4F,IANST,EAAT,KAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,GAAAA,EAAA,KAAA,MAQAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IACAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IANAnF,OAAAG,IAQA,IAAAgF,GAAAA,EAAA,MAAA,EAtCiBnF,OAgCjB6F,KAQA7F,OAAA2F,KACA,KAAA,KACA,IAAA,KACA,IAAA,KACA,MAvCgB,KAuChBxN,GAAA8C,EAAA,KAAA,GAAAA,EAAA,MAAA,IAAAuK,EAAA,KAAA,GAAAA,EAAA,MAAA,GAvCqCxF,OAAOE,IAwC5C,IAAA/H,GAAA8C,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KAvCAA,EAAA,KAAA,IAAAA,EAAA,KAAA,KAwCAuK,EAAA,KAAAH,KAAAC,MAAAE,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KAvCAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IAwCAxF,OAAAG,IACAH,OAAA2F,KACA,KAAA,KAvCA,IAAA,KAwCA,MAAA,KAAU1K,GAAV,IAAA9C,EAvCA6H,OAAAE,IAwCAjF,IAAAoK,KAAAC,MAAArK,IAAAA,GAAA,GAAAA,GAAA,GAAA,IAAA9C,EAvC0B6H,OAA1BG,IAiCA,IAAAhI,EAhC0B6H,OAA1B6F,KAwCA7F,OAAA2F,KAvCA,KAAA,KAwCA,MAAA,KAAAR,EACAnF,OAAAC,KACA,IAAUkF,EAvCsDnF,OAAhEE,IAwCA,IAAAiF,EACAnF,OAAA4F,IACA,IAAAT,EAtCgBnF,OAuChBG,IAvCA,IAAqDgF,EAwCrDnF,OAAA6F,KAvCA7F,OAAA2F,KAwCA,KAAA,KACA,MAAA,KAAUR,GAAV,IAAAM,IAAA,IAAAxK,GAAA,IAAAA,GAtCe+E,OAAOE,IAwCtBF,OAAA2F,KACA,KAAA,MACA,IAAA,MACA,MAAA,KAAAxN,GAAA8C,EAAA,MAAA,GAAAuK,EAAA,MAAA,EACAxF,OAAAE,IACA,IAAA/H,GAAA8C,EAAA,MAAA,GAAAuK,EAAA,MAAA,EAvCAxF,OAAA4F,IAwCA,IAAAzN,GAAA8C,EAAA,MAAAoK,KAAAC,MAAArK,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GACAuK,EAAA,MAAAH,KAAAC,MAAAE,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,EACAxF,OAAAG,IACAH,OAAA2F,KACA,KAAA,KACA,IAAA,KACA,IAAA,KACA,IAAA,MANA,MAhCgB,KAgCP1K,GAOT,IAAAA,EAvCA+E,OAAiCE,IAiCjCF,OAAA2F,KAhCA,KAAA,MAwCA,MAAA,KAAAxN,IAAA,IAAA8C,GAAA,IAAAA,GAAA,IAAAA,IAvCA,IAAmD9C,GAAnD8C,EAAA,KAAA,GAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GAwCA,IAAA9C,GAAAqN,EAAA,KAAA,GAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,EAvC2DxF,OAA3DE,IAwCAF,OAAA2F,KACA,KAAA,KACA,MAAA,KAAUR,EAvCVnF,OAAAE,IAwCA,IAAUiF,EAvCVnF,OAAA4F,IAwCAT,IAAAE,KAAAC,MAAAH,IAAAA,GAAA,GAAAA,GAAA,EAvCmFnF,OAAnFG,IAwCAgF,IAAAE,KAAAC,MAAAH,IAAAA,GAAA,GAAAA,GAAA,GACAnF,OAAA6F,KACA7F,OAAA2F,KAvCA,KAAA,KAwCA,MAAA,KAAUR,GAvCe,KAuCzBA,EAvCAnF,OAAAE,IAwCA,IAAAiF,GAAA,KAAAA,EANAnF,OAAA4F,IAQQT,IAARE,KAAAC,MAvCuBH,KAuCvBA,GAAA,GAAAA,GAAA,IAAAA,GAAA,IAAAA,GAAA,IACAnF,OAAAG,IAvCAH,OAAA2F,KAwCA,KAAA,KAtCA,MAAA,KAAAxN,GAAA8C,EAAA,KAuCA,EAtCgB+E,OAuChBE,IAvCA,IAA8B/H,GAA9B8C,EAAA,KAAA,EACgB+E,OAuChB4F,IAvCA,IAAAzN,GACW8C,EAuCX,MAAA,GAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,IAAAA,EAAA,MAAA,GACA,IAAA9C,EACA6H,OAAA6F,KACA7F,OAAA2F,MA1CkE3F,OAAlEG,GAGA,KAAA,KAwCA,MAAA,KAAAlF,GAAA,IAAA9C,EACA6H,OAAAE,IACA,IAAAjF,GAAA,IAAA9C,EAvC0B6H,OAA1B4F,IAiCA,IAASzN,GAOTgN,GAAA,GAPAA,GAAA,IAAAA,EAAA,KAAA,EAQAnF,OAAA2F,MAxCA3F,OAAA6F,IAyCA,KAAA,KACA,MAAA,KAAAJ,GAAAxK,EAAA,KAAA,GAAAA,EAAA,MAAA,IAAA,IAAAwK,EACAzF,OAAAE,IACAF,OAAA2F,KACA,KAAA,MAvCA,MAAA,KAAAR,EAwCAnF,OAAAC,KAvCA,IAAAkF,EAwCAnF,OAAAE,IACAF,OAAA2F,KACA,KAAA,KAvCA,IAAA,MAwCA,IAAA,KAvCA,IAAA,MAwCA,MAAA,KAAAR,EACAnF,OAAAE,IACA,IAAUiF,EAvC0DnF,OAApE4F,IAwCA5F,OAAA2F,KANA,KAAA,MAQA,MAAA,KAAAR,EACAnF,OAAAC,KAvC6B,IAA7BhF,GAAqC,IAArCA,GAAA,IAAAkK,EAyCAnF,OAAA2F,MADA3F,OAAAE,GAEA,KAAA,KACM,MAAIiF,GAAV,KAAA,GAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GAEAA,EAAA,KAAAE,KAAAC,MAAAH,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KACAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IANAnF,OAAAG,IAQA,IAAAqF,EACAxF,OAAA6F,KACA7F,OAAA2F,MA7CgB3F,OAuChBE,GAOA,KAAA,KAvCA,IAAA,MAwCA,MAAAiF,GAAA,KAAA,GAAAA,EAAA,MAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IACA,IAAAhN,GAAAqN,EAAA,MAAAH,KAAAC,MAAAE,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GACAxF,OAAAC,KAvCmBkF,EAAnB,KAAA,GAAAA,EAAA,MAAA,IAAA,IAAAhN,GAAAqN,EAAA,KAAA,GAAAA,EAAA,MAAA,IAwCA,IAAArN,GAAAqN,EAvCyB,KAuCzB,EACAxF,OAAAE,IACAF,OAAA2F,KAvCA,KAAA,KAwCA,MAAA,KAAAxN,GAAA8C,EAAA,KAAA,GAAAuK,EAAA,KAAA,EACAxF,OAAAE,IACAF,OAAA2F,KAvCA,KAAA,KAiCA,MAAA,KAASR,EAQTnF,OAAAE,IACA,IAAAiF,GAAAA,EAAA,MAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GAtCgBnF,OAuChBG,IACUgF,EAAV,MAAAE,KAvCyBC,MAuCzBH,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GAtCgBnF,OAuChB6F,KACA7F,OAAA2F,KACA,KAAA,KACA,MAAA,KAAA1K,GAAA,IAAA9C,EACA6H,OAPAE,IAhCA,IAAiE/H,GAAjE8C,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KAwCAA,EAAA,KAAA,IAAAA,EAAA,KAAA,IACA+E,OAAAG,IACA,IAAAhI,GAAA,IAAA8C,GAAAA,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,GAvCA,IAA8B9C,GAA9B8C,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,GAwCA,IAAU9C,GAAV8C,EAAA,MAAAoK,KAAAC,MAAArK,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GACA+E,OAAA6F,KACA7F,OAAA2F,KACA,KAAA,KACA,MAAAR,KAAAE,KAAAC,MAAAH,IAAAA,GAAA,GAAAA,GAAA,GAAA,IAAAA,EANAnF,OAAAE,IACAF,OAAA2F,KAhCA,KAAA,KAwCM,MAAN,KAAA1K,GAAA,IAAA9C,EACA6H,OAAAE,IACA,IAAA/H,GAAA,IAAAgN,GACA,IAAAA,GAAAA,EAAA,MAAAE,KAAAC,MAAAH,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GAtCgBnF,OAuChBG,IACAH,OAAA2F,KACA,KAAA,KACA,IAAA,KACA,MAAA,KAAAxN,GAAA8C,EAAA,KAAA,GAAAA,EAAA,MAAA,GAtCgB+E,OAuChBE,IAvCA,IAA8B/H,GAA9B8C,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,KAwCAA,EAAA,KAAA,IAvC6BA,EAuC7B,KAAA,IAvCA+E,OAAAG,IAwCA,IAAAhI,GAAA8C,EAAA,KAAA,GACA,IAAA9C,GAAA8C,EAAA,KAAAoK,KAAAC,MAAArK,EAAA,KAAAA,EAAA,IAAA,GAAAA,EAAA,IAAA,GACA,IAAU9C,GAAV8C,EAAA,MAAAoK,KAAAC,MAAArK,EAAA,MAAAA,EAAA,KAAA,IAAAA,EAAA,KAAA,GAvCA+E,OAAA6F,KAwCA7F,OAAA2F,KACA,KAAA,MACA,MAAA,KAAA1K,GAAA,IAAAkK,EAvC2CnF,OAA3CE,IAwCUiF,IAAVE,KAAAC,MAAAH,IAAAA,GAAA,GAAAA,GAAA,GAvCAnF,OAAAG,IAwCAH,OAAA2F,KACA,KAAA,KACA,MAAA,KAAAR,GAAA,IAAAA,GAAA,IAAAlK,GAAA,IAAAuK,EACAxF,OAAAE,IACAF,OAAA2F,KACA,KAAA,KACA,MAAA,KAAAxN,GAAA8C,EAAA,MAAA,yCAIA,IAAA9C,GAAA8C,EAAA,MAAAoK,KAAAC,MAAArK,EAAA,MAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,GAAA,IAAA9C,EACA6H,OAAAG,iGDrbAH,OAAAE;;;;;;;8OZwIA,QAAAxK,eAAA2L,wnCAsGA,QAAAC,eAAAxO,OAAAhC,KAAAkC,QACA,GAAA8G,IAAA/F,gBAAAjB,OACA,IAAAgH,GACA,MAAAA,IAAAhJ,KAAAkC,OACA,IAAAuO,UAAAzO,OACA0O,MAAAC,qBAAA9H,IAAA4H,SACA,KAAAC,MAAA,CA1CAA,QA2CA,IAAAE,OAAA,MACA5N,oBAAA6N,KAAA7O,OAEA,KADA,GAAA8O,SAAA9O,OACA8O,SAEAF,MAAA5N,mBAAA6N,KAAAC,SACAF,OAxCSF,MAATA,MA0CAK,OA1CAH,MAAAI,MAAA,IA2CAF,QAAAJ,MAAAO,QAGAP,MAAA1G,KAAA8G,wWC1NA,GAFA,SAAAtO,WAAAA,SAAA,MACA,SAAAC,mBAAAA,kBAAA,GACA,MAAMvD,iBAIN,IADAA,MAAA,gBAAAA,QAAAgS,UAAAhS,QAAAA,MAAAA,MACA,gBAAAA,OACA,KAAA0B,0BAAAuQ,KAAAjS,MAGA,IAAAkS,QAAAxO,OACAyO,YAAAzO,OACA0O,YAAA1O,MAOA,IANAT,QAAAN,kBAAAc,WAEAyO,OAAA,EACAC,YAAA,EACAC,YAAA,GAEA/L,OAAA,CACA,GAAAmL,OAAAnL,OAAAqL,MAAAW,sBACA,IAAA,OAAAb,MACA,KAAA,IAAAc,OAAAjM,OAAA,8CAGA,OAAAmL,MAAA,KACAU,OAAAK,kBAAAf,MAAA,KAEA,MAAAA,MAAA,KACAW,YAAAI,kBAAAf,MAAA,KAEA,MAAAA,MAAA,KACAY,YAAAG,kBAAAf,MAAA,8OA4MA,GAAAgB,QAAApR,SAAAqR,2RHnEA,QAAAC,iBAAAhB,OACE,GAAF5Q,MAAA,GAAAU,MAAA,GACAmR,OAAA,EACAC,MAAA,EACAC,WAAAnB,MAAA,GAAA5Q,KAAAgS,eAAAhS,KAAAiS,YACAC,WA3CoCtB,MA2CpC,GAAA5Q,KAAAmS,YAAAnS,KAAAoS,QACAxB,OAAA,KACAiB,OAAAQ,MAAAzB,MAAA,GAAAA,MAAA,gJAKApQ,EAAA6R,MAAAzB,MAAA,IAAA,KAAAkB,wemBrMA,GAAIQ,eAAJpL,OAAAqL,iBACAC,uBAAAnE,QAA2C,SAA3C5N,EAAAgS,GAAAhS,EAAA+R,UAAAC,IACI,SAAJhS,EAAAgS,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6ClT,eAA7CmT,KAAAjS,EAAkEiS,GAAlED,EAAAC,qLCqCAC,iBAAAvT,UAAAyQ,aAAA,SAAA7G,MAKA2J,iBAAAvT,UAAAwT,SAAA,6KA0BAD,iBAAAvT,UAAAyT,UAAA,SAAA1C,MAAAC,MAAA0C,8cCrBAC,iBAAA3T,UAAA8Q,aAAA,SAAAC,MAAAC,MAAA0C,IAAAxC,uSCbA0C,SAAA,WAIA,QAAAA,UAAAC,kBACA,GAAA7J,OAAA/D,IAIAA,MAAA6N,SAAA,GAAAzR,eAAA0R,aACA9N,KAAA+N,kBAAAH,2NAKAH,IAAA1J,MAAAiH,MAAA,6uCAsGA2C,SAAAK,cAAA,SAAAC,MAAAC,KACA,GAAA,GAAAD,MAAAlJ,OACA,MAAAmJ,IAEA,IAAA,GAAQA,IAARnJ,OACA,MAAAkJ,MAEA,IAAAE,SAAA,QACAF,OAAAG,SAAA,MACAD,UAEAD,IAAAG,WAAA,gQADA,OAAAZ,KAAA9B,MAAA,EAAA2C,iBAAAb,IAAA9B,MAAA4C;;;;;;;wLJtIAxK,MAAA2G,UAAA,4DAPAnB,WAAAoB,qBAAAlB,qGAmBAzJ,KAAAsK,kBAAAE,aAAA7G,KALAgH,qBAAA5Q,UAAA6Q,YAAA,WAAA,MAAA5K,MAAA0K,4nBAqDAC,qBAAA5Q,UAAA8Q,aAAA,SAAAC,MAAAC,MAAAC,KAAAC,6RAYAN,qBAAA5Q,UAAA0Q,KAAA,WAAAzK,KAAAsK,kBAAAG,kFF1HAtO,KAAAC,cAAA6N;;;;;;;0CA8DA,QAAAI,sBAAAC,kBAAAC,uCAKA,8GAAA,MAAAA,4KAbAhB,WAAAc,qBAAAZ,qGAyBAzJ,KAAAsK,kBAAAE,aAAA7G,6gCAkDA0G,qBAAAtQ,UAAA0Q,KAAA,WAAAzK,KAAAsK,kBAAAG,kFCjIAtO,KAAAC,cAAA6N;;;;;;;;;;;;;;oSFkEAV,WAAAC,qBAAAC,QAWAD,qBAAAzP,UAAA2P,kBAAA,SAAA7P,OANA,GAAA8P,QAAAC,cAAA5J,KAAAnD,OAAAhD,MAQA,QAAA8P,QACA,IAAAE,QAAAC,KACA,MAbe,MAcf,KAAAD,QAAAE,IACA,MAAA,sBAZA,MAAA,KAcA,KAAAF,QAAAG,IACA,MAAA,mCAnCA,iGA2DA7N,KAAAC,cAAA6N,aAKAT,qBAAApG,eAAA,WAAA,QACMjH,KAANoB,OAAA2M,aAAA/N,KAAAC,cAAA+N,OAAA7N,MAAAF,cAAAgO,eAEA,IAAAP,UACAA,QAAOC,KAAP,EACAD,OAAOE,IAPE;;;;;;;2FDvDT/J,KAAAkI,iBAAAA,iBAKAlI,KAAAmI,iBAAAA,yFAAAtG,QAAAC,eAAAtB,QAAAzG,UAAA,SAKAgI,IAAA,SAAAC,2FAAAhC,KAAAoI,sBAAA,GAQApI,KAAAqI,cAAArI,KAAAsI,WAAA,oCAAAzG,OAAAC,eAAAtB,QAAAzG,UAAA,WAIAgI,IAAA,SAAAC,GACAhC,KAAAuI,gBAAAvI,KAAAsI,WACAtI,KAAAwI,gBAAA,KACAxI,KAAAyI,gBAAA,2HAIAzI,KAAAwI,gBAAAxI,KAAAkI,iBAAAhG,KAAAlC,KAAAsI,WAAAnG,kGAKAC,cAAA,IAIA5B,QAAAzG,UAAA+M,UAAA,WACA,GAAA9G,KAAAwI,gBAAA,CACA,GAAAE,iBAAA1I,KAAAwI,gBAAAnG,KAAArC,KAAA,UACA0I,4FAVA,GAAAC,iBAAA3I,KAAAyI,gBAAApG,KAAArC,KAAA,2dAeAoB,QAAAwH,mBAAA,SAAA3D,mEA+BAzE,QAAAzG,UAAA8O,sBAAA,SAAAzH,SACA,GAAA2C,OAAA/D,2eAqBAQ,QAAAzG,UAAAsO,cAAA,SAAAS,YAAAC,WACA,GAAAhF,OAAA/D,IACA8I,eACAE,MAAAC,QAAAH,cAAAA,sBAAAI,2MAeA1I,QAAAzG,UAAAoP,aAAA,SAAAC,MAAAC,4MASAlN,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,gBAAAhB,QAAA4C,eAAA,WAAA,QDxLAjH,KAAAC,cAAAkN;;;;;;;2KA2FA7I,mBAAA1G,UAAAoH,YAAA,SAAAC,SAEA,GADApB,KAAAoH,kBAAAxB,QAFA5F,KAAAqH,cAAA,KAGArH,KAAAsH,kBAAA,CACA,GAAAC,YAAAvH,KAAAwH,2BAAAxH,KAAAoH,kBAAAK,cACA,IAAArG,QAAA,iCAKA,GAHApB,KAAA0H,YACA1H,KAAA0H,WAAAC,UAEA3H,KAAA4H,iCAAA,CACA,GAAAC,cAAAN,WAAA/D,IAAApH,cAAA0L,YAKA9H,MAAA0H,WAAA1H,KAAA4H,iCAAAzF,OAAA0F,aAAAE,mCAMA,IAAAC,0BAAAhI,KAAA0H,WAAA1H,KAAA0H,WAAAM,+TArCAvH,kBAAA1G,UAAAkO,YAAA,4BAuCA1G,KAAAmG,WAAAC,+DAWAxL,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,0BH7HAf,kBAAA2C,eAAA,WAAA;;;;;;;6sBAmIA,QAAAC,SAAAC,eAAAC,UAAA7B,UAMA1B,KAAAsD,eAAAA,gJAAAE,IAAA,WAAA,MAAAxD,MAAAyD,yFAvBA,SAAAC,QAAA,8MAoDA1D,KAAAyD,WAAAE,oCAAA9B,OAAAC,eAAAuB,QAAAtJ,UAAA,6CAOAiG,KAAAuD,UAAA1J,wCAUAwJ,QAAAtJ,UAAAoH,YAAA,SAAAC,SACA,GAAA,WAAAA,SAAA,CAEA,GAAAvH,OAAAuH,QAAA,QAAAwC,0GAKA,MAAAC,0NApCA,GAAA7D,KAAAiC,QAAA,oFAuDAoB,QAAAtJ,UAAA+J,cAAA,SAAA1C,SACA,GAAA2C,OAAA/D,KA7CAgE,eA8CA5C,SAAA6C,iBAAA,SAAAC,KAAAC,sBAAAC,cAbA,GAAA,MAAAF,KA/BiBG,cA+BjB,CAeY,GAAZzB,MAAAmB,MAAAT,eAAAgB,mBAAAP,MAAAR,UAAA,GAAAgB,gBAAA,KAAAR,MAAAS,UAAA,KAAA,IAAAJ,cACAK,MAAA,GAAAC,iBAAAR,KAAAtB,KACAoB,cAAAW,KAAAF,WAGA,IAAA,MAAAL,aACUL,MAAVT,eAAAsB,OAAAT,2BAXA,CAeA,GAAAvB,MAAAmB,MAAAT,eAAAE,IAAAW,sBACAJ,OAAAT,eAAAuB,KAAAjC,KAAAwB,aACA,IAAAK,OAAA,GAAAC,iBAAAR,KAAA,KAbAF,cAAAW,KAAAF,SAmBA,KAAA,GAAAK,GAAA,EAAAA,EAAAd,aAAAe,OAAAD,IACA9E,KAAAgF,eAAAhB,aAAAc,GAAAlC,KAAAoB,aAAAc,GAAAG,mWAlCArC,KAAAsC,QAAAC,UAAAF,OAAAf,sCAKA/H,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,uBA0DA6B,QAAAD,eAAA,WAAA,qHA6BAC,QAAA+B,0IAIA,IAAAV,iBAAA,WAJA,QAAAA,iBAAAO,OAAArC,wCAwBA,MAAA8B,kCCjNAW,KAAA,sHAWArF,KAAAsF,iBAAA,4LAQAtF,KAAAuF,SAAAJ,UAAAnF,KAAAuF,SAAAC,KAAAC,2KASAzF,KAAA0F,aAAA,sKASA1F,KAAA2F,aAAA,uCAIAvD,cAAA,IAKAiD,KAAAtL,UAAA2I,YAAA,WATA1C,KAAAuF,SAAAJ,UAUAnF,KAAA0F,eACA1F,KAAAsD,eAAAsC,QACA5F,KAAA2F,aAAA,KACA3F,KAAAsF,mBACAtF,KAAA0F,aACA1F,KAAAsD,eAAAgB,mBAAAtE,KAAAsF,iBAAAtF,KAAAuF,gCAJAvF,KAAAsD,eAAAsC,QAUA5F,KAAA0F,aAAA,KACA1F,KAAA6F,8IASA1J,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,0FAiCArF,KAAAC,cAAA0J,4GAGAC,WAAA5J,KAAAC,cAAA4J,oCAAA,QAAAC,4hBClMAC,SAAAlG,KAAAmG,4DA2FA,QAAAxF,YAKAX,KAAAoG,cAAA,+EAAAvE,QAAAC,eAAAnB,SAAA5G,UAAA,YAKAgI,IAAA,SAAAsE,uDAAArG,KAAAsG,qBAAA,8NA2BA3F,SAAA5G,UAAAwM,WAAA,SAAA1M,+KA3BAmG,KAAAsG,qBAAAtG,KAAAwG,kFASA7F,SAAA5G,UAAAuM,oBAAA,SAAAG,YAiCA,GAAAzG,KAAA0G,eAAAD,aAAAzG,KAAAoG,aAAA,CACApG,KAAAoG,aAAAK,6FA9EAE,aAAAC,aAAAH,wZAoKAI,cAAA9M,UAAA+M,UAAA,WAAA9G,KAAA+G,MAAAH,aAAA5G,KAAAgH,SAAAT,WAAAvG,KAAAiH,0DA7DA9K,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,uWAuCAwF,SAAAE,YAAA,GAAAlE,YAACC,cAADkE;;;;;;;kWH/IA1E,SAAA1I,UAAA2I,YAAA,gUA7BA1C,KAAA2C,YAAAC,mEAoDAzG,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,gSAkBAqB,UAAAC,QAAAC,UAAA,IAAAlJ,MAAAA,MAAA,GAAAmJ,YAAAC,cAAAC,4DA+CA/G,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA,qBAAA2B,aAAAC,eAAA,WAAA;;;;;;;uBDpHA,QAAA3B,SAAAC,SAAAC,MAAAC,kFAAAC,QAAAC,eAAAL,QAAA1H,UAAA,WAKAgI,IAAA,SAAAC,sCAAAhC,KAAAiC,QAAAjC,KAAA0B,SAAAQ,KAAAF,GAAAG,yBAIAC,cAAA,4DALA,GAAAhB,SAAApB,KAAAiC,QAAAI,KAAArC,KAAAsC,yiBAgBAtC,KAAA4B,UAAAW,gBAAAvC,KAAA2B,MAAAa,cAAAjG,KAAA,wCAKAJ,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA;;;;;;;6RDjBAd,iBAAA3G,UAAAoH,YAAA,SAAAC,SAGApB,KAAAqB,uFAtBArB,KAAAsB,mLA+BAnF,KAAAC,cAAAmF,UAAAjF,OAAAkF,SAAA;;;;;;;AHvBA,GAAE5H,oBACA4G,QACFC,+BAGAC,yBCvDAC,24BC2EArH,UAAA,oCA0BA0G,KAAAY,KAAAA,KACAZ,KAAAa,aAAA,sLAwBAvH,UAAAS,UAAAC,UAAA,SAAA8G,KACA,MAAAd,MAAAe,2DAhBAf,KAAAa,eAAAb,KAAAgB,iJAkBAF,KACAd,KAAAiB,WAAAH,KAGQd,KAARgB,qBAAAhB,KAAAa,aACAb,KAAAa,eAhBAvH,UAAAS,UAAAkH,WAAA,SAAAH,qRAYA,MAAAI,gFAuCA,MAAA3F,0BAAAjC,UAAAwH,wQA5FAd,KAAAa,aAAAhH;;;;;;;6DL9CAL,eAAAO,UAAAC,UAAA,SAAAH,OACA,IAAAA,6CATA,KAAA0B,0BAAA/B,cAAAK,MAQA,OAAAA,OAAAgC,8NAiDAC,eAAA/B,UAAAC,UAAA,SAAAH,OACA,IAAAA,6CAVA,KAAA0B,0BAAAO,cAAAjC,MANA,OAAAA,OAAAiB,MAAA,OAAAC,IAAA,SAAAgB,MAAA,MAAAC,eAAAD,QAAAE,KAAA,oNAoDA1C,eAAAQ,UAAAC,UAAA,SAAAH,OACA,IAAAA,6CATA,KAAA0B,0BAAAhC,cAAAM,MAtBA,OAAAA,OAAAqC,2DClFAC,KAAAC,cAAAC,KAAAC,OAAAC,KAAA,gLA6CAC,kBAAAA,kBAAAC,SAAA,qLAIA,MAnBAC,iBAAAC,OAAA,SAAAC,IAAAC,OAAAC,MAAAC,MACA,SAAMA,OAANA,QACA,IAAAC,sBAAAD,KAAAC,qBAAAC,sBAAAF,KAAAE,sBAAAC,sBAAAH,KAAAG,sBAAAC,SAAAJ,KAAAI,SAAAtC,GAAAkC,KAAAK,iBAAAA,iBAAA,SAAAvC,IAAAA,GACAwC,SACAL,qBAAAA,qBACAC,sBAAAA,sBAIAC,sBAAAA,sBAGAJ,MAAAN,kBAAAM,OAAAjB,cAKA,OAHAiB,QAAAN,kBAAAc,WACAD,QAAAF,SAAA,gBAAAA,UAAAA,SAAAI,OAAAF,QAAAG,gBAAAJ,iBAAA,SAAA,QAEA,GAAAK,MAAAC,aAAAb,OAAAQ,SAAAV,OAAAC,MAEAF,mBAEAiB,mBAAA,sGACAC,iBAEAC,SAAAC,sBAAAC,SACAC,eAAA,OAAA,GACAC,cAAA,QAAA,GACAD,eAAA,MAAA,GACIA,eAHe,OAAO,GAI1BA,eAAA,SAAA,GACAA,eAAA,SAAA,MAIAE,MAAAJ,sBAAAC,SAEAC,eAAA,OAAA,GAAAA,eAAA,QAAA,GAAAA,eAAA,MAAA,GAEAA,eAAA,OAAA,GAAAA,eAAA,SAAA,MAGAG,WAAAL,sBAAAC,sFAEAC,eAAA,MAAA,MAEAI,OAAAN,sBAAAC,SAAAC,eAAA,OAAA,GAAAC,cAAA,QAAA,GAAAD,eAAA,MAAA,MACAK,MAAAP,sBAAAC,SAAAC,eAAA,OAAA,GAAAC,cAAA,QAAA,GAAAD,eAAA,MAAA,uHAEEM,IAAFR,sBAAAC,SAAAC,eAHsD,OAGtD,GAAAA,eAAA,SAAA,GAAAA,eAAA,SAAA,MACEO,GAAFT,sBAAAC,SAAAC,eAHqD,OAGrD,GAAAA,eAAA,SAAA,OAEEQ,cAEAC,KAAFX,sBAAAE,eAAA,OAAA,IACEU,GAAFZ,sBAAAE,eAAA,OAAA,IAEE9C,EAAF4C,sBAAAE,eAAA,OAAA,IACEW,KAAFb,sBAAAG,cAAA,QAAA,IAEEW,IAAFd,sBAAAG,cAAA,QAAA,IACEY,GAAFf,sBAAAE,eAAA,QAHqD,IAInDc,EAAFhB,sBAAAE,eAAA,QAAA,IACEe,KAAFjB,sBAAAG,cAAA,QAAA,IACEe,EAAFlB,sBAAAG,cAAA,QAAA,IACEgB,GAAFnB,sBAAAE,eAAA,MAAA,IACE5C,EAAF0C,sBAH6BE,eAAe,MAG5C,0RAIEkB,EAAFC,cAAArB,sBAAAsB,aAAApB,eAAA,OAAA,IAAA,KACEqB,GAAFvB,sBAAAE,eAAA,OAAA,8KAIEsB,GAAFC,cAAAzB,sBAAAE,eAAA,SAAA,KACEwB,EAAF1B,sBAAAE,eAAA,SAAA,IAIEyB,IAAF3B,sBAAAE,eAAA,SAAA,2DAGE0B,IAAF5B,sBAAAG,cAAA,UAAA,IACE0B,GAHM7B,sBAAsBG,cAG9B,UAAA,wDAEE2B,EAAFC,mBAAA/B,sBAAAsB,aAAApB,eAAA,OAAA,IAAA,0rBCrBA,MAAA8B,cAAAC,YAAAC,KAAAC,QAAApG,MAAA2C,kBAAAC,QAAAyD,mXAoCA,MAAAJ,cAAAK,YAAAH,KAAAC,QAAApG,MAAA2C,kBAAA4D,QAAAF,kZA4CA,MAbA,UAAAG,eAAAA,aAAA,kDAaAP,aAAAQ,aAAAN,KAAAC,QAAApG,MAAA2C,kBAAAc,SAAA4C,OAAAG,aAAAE;;;;;;;kMHrFA,MA0BA9F,UAAAV,UAAAC,UAAA,SAAAH,MAAAa,SAPA,SAAAA,UAAAA,QAAA,aAQA,IAAAC,KACA,IAAAC,QAAAf,QAAAA,QAAAA,MAPA,MAAA,kNA2BA,GAAAgB,IAAAhB,MAAAiB,MAAA,KAAAC,IAAA,SAAAC,KAAA,MAAAC,UAAAD,IAAA,MAAAE,EAAAL,GAAA,GAAAM,EAAAN,GAAA,GAAAO,EAAAP,GAAA,EAPAF,MAAA,GAAAU,MAAAH,EAAAC,EAAA,EAAAC,OAUAT,MAAA,GAAAU,MAAAxB,MAGA,KAAAyB,OAAAX,MAAA,sFA1DA,KAAAY,0BAAAd,SAAAZ,uHAIAY,WAgDAA,UAAAe,UAQAC,OAAA,WACAC,MAAA,mEALAC,UAAA,MAUAC,WAAA;;;;;;;uID9GAzB,gBAAAJ,UAAAC,UAAA,SAAAH,MAAAO,WAOA,GAAA,MAAAP,MACA,MAAA,sLAJA,OAAAO,WAAAC,KAAAC,QAAAC,sBAAAV,MAAAW;;;;;;;+DDJAV,gBAAAC,UAAAC,UAAA,SAAAH,MAAAI,SACA,GAAA,MAAAJ,MAEA,MAAA,EACA,IAAA,gBAAAI,UAAA,gBAAAJ,6DAEA,OAAAI,SAAAC,eAAAL,OACAI,QAAAJ,qDAFA;;;;;;;;;;;;;;6HDiBA,MAAAA;;;;;;;AFlBA,GAAER,eACAC,UACAC,cACFC,uBCrDAC,qGAyBAC,aAAA,WACA,QAAAA,iBAEA,MAAAA,8EAMAC,cAAAC,kBAAAP;;;;;;;"}