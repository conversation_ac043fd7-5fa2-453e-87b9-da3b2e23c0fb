{"_args": [["babel-messages@6.23.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-messages@6.23.0", "_id": "babel-messages@6.23.0", "_inBundle": false, "_integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "_location": "/babel-messages", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-messages@6.23.0", "name": "babel-messages", "escapedName": "babel-messages", "rawSpec": "6.23.0", "saveSpec": null, "fetchSpec": "6.23.0"}, "_requiredBy": ["/babel-generator", "/babel-traverse"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-messages/-/babel-messages-6.23.0.tgz", "_spec": "6.23.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.22.0"}, "description": "Collection of debug messages used by Babel.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-messages", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-messages"}, "version": "6.23.0"}