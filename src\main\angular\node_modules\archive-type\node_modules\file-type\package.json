{"_args": [["file-type@4.4.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "file-type@4.4.0", "_id": "file-type@4.4.0", "_inBundle": false, "_integrity": "sha1-G2AOX8ofvcboDApwxxyNul95BsU=", "_location": "/archive-type/file-type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "file-type@4.4.0", "name": "file-type", "escapedName": "file-type", "rawSpec": "4.4.0", "saveSpec": null, "fetchSpec": "4.4.0"}, "_requiredBy": ["/archive-type"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/file-type/-/file-type-4.4.0.tgz", "_spec": "4.4.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/file-type/issues"}, "description": "Detect the file type of a Buffer/Uint8Array", "devDependencies": {"ava": "*", "read-chunk": "^2.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/file-type#readme", "keywords": ["mime", "file", "type", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "type", "detect", "check", "is", "exif", "exe", "binary", "buffer", "uint8array", "jpg", "png", "gif", "webp", "flif", "cr2", "tif", "bmp", "jxr", "psd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "m4v", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "exe", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "msi", "mxf", "wasm", "webassembly", "blend"], "license": "MIT", "name": "file-type", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/file-type.git"}, "scripts": {"test": "xo && ava"}, "version": "4.4.0"}