{"title": "srcdoc attribute for iframes", "description": "Override the content specified in the `src` attribute (if present) with HTML content within the attribute.", "spec": "https://html.spec.whatwg.org/multipage/embedded-content.html#attr-iframe-srcdoc", "status": "ls", "links": [{"url": "https://docs.webplatform.org/wiki/html/elements/iframe", "title": "WebPlatform Docs"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe", "title": "Mozilla Developer Network (MDN) documentation - iframe"}, {"url": "https://github.com/jugglinmike/srcdoc-polyfill", "title": "Srcdoc Polyfill"}, {"url": "https://bocoup.com/weblog/third-party-javascript-development-future/", "title": "Article"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p"}, "edge": {"12": "p", "13": "p", "14": "p", "15": "p", "16": "p"}, "firefox": {"2": "n", "3": "p", "3.5": "p", "3.6": "p", "4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "p", "5": "p", "5.1": "p", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "p", "11.5": "p", "11.6": "p", "12": "p", "12.1": "p", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "p", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "p", "4": "p", "4.1": "p", "4.2-4.3": "p", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "p", "10": "y"}, "op_mob": {"10": "n", "11": "n", "11.1": "p", "11.5": "p", "12": "p", "12.1": "p", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "p", "11": "p"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {}, "usage_perc_y": 88.31, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "iframesrcdocattribute", "chrome_id": "5222955109842944", "firefox_id": "", "webkit_id": "", "shown": true}