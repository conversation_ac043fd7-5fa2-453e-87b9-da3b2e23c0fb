{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../packages/compiler-cli/index.ts"], "names": [], "mappings": ";;;;;AAAA;;;;;;GAMG;AACH,8CAAyH;AAAxD,qCAAA,eAAe,CAAA;AAAE,kCAAA,YAAY,CAAA;AAC9F,yCAA4C;AAApC,kCAAA,aAAa,CAAA;AACrB,qDAA4H;AAApH,uCAAA,YAAY,CAAA;AAAuB,sDAAA,2BAA2B,CAAA;AAAE,kDAAA,uBAAuB,CAAA;AAC/F,6CAA0C;AAAlC,gCAAA,SAAS,CAAA;AACjB,0CAAqC;AACrC,yCAAsC;AAA9B,4BAAA,OAAO,CAAA;AAEf,mFAAsI;AAAtG,oEAAA,gCAAgC,CAAA;AAAE,sDAAA,kBAAkB,CAAA;AACpF,qEAAwF;AAAhF,oCAAA,OAAO,CAAA;AACf,2EAAmH;AAA3G,8DAAA,8BAA8B,CAAA;AAAE,6CAAA,aAAa,CAAA;AAAE,8CAAA,cAAc,CAAA;AACrE,qDAA0K;AAAlK,gCAAA,WAAW,CAAA;AAEnB,4DAA4D;AAC5D,iDAAsF;AAA9E,gDAAA,wBAAwB,CAA2B", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {AotCompilerHost, AotCompilerHost as StaticReflectorHost, StaticReflector, StaticSymbol} from '@angular/compiler';\nexport {CodeGenerator} from './src/codegen';\nexport {CompilerHost, CompilerHostContext, ModuleResolutionHostAdapter, NodeCompilerHostContext} from './src/compiler_host';\nexport {Extractor} from './src/extractor';\nexport * from '@angular/tsc-wrapped';\nexport {VERSION} from './src/version';\n\nexport {DiagnosticTemplateInfo, getTemplateExpressionDiagnostics, getExpressionScope} from './src/diagnostics/expression_diagnostics';\nexport {AstType, ExpressionDiagnosticsContext} from './src/diagnostics/expression_type';\nexport {getClassMembersFromDeclaration, getPipesTable, getSymbolQuery} from './src/diagnostics/typescript_symbols';\nexport {BuiltinType, DeclarationKind, Definition, PipeInfo, Pipes, Signature, Span, Symbol, SymbolDeclaration, SymbolQuery, SymbolTable} from './src/diagnostics/symbols';\n\n// TODO(hansl): moving to Angular 4 need to update this API.\nexport {NgTools_InternalApi_NG_2 as __NGTOOLS_PRIVATE_API_2} from './src/ngtools_api';\n"]}