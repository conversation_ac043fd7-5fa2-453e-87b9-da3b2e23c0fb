{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/core/testing/index.ts", "../../../../../packages/core/testing/src/testing.ts", "../../../../../packages/core/testing/src/private_export_testing.ts", "../../../../../packages/core/testing/src/before_each.ts", "../../../../../packages/core/testing/src/test_bed.ts", "../../../../../packages/core/testing/src/test_compiler.ts", "../../../../../packages/core/testing/src/async_test_completer.ts", "../../../../../packages/core/testing/src/fake_async.ts", "../../../../../packages/core/testing/src/component_fixture.ts", "../../../../../packages/core/testing/src/async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './async';\nexport * from './component_fixture';\nexport * from './fake_async';\nexport * from './test_bed';\nexport * from './before_each';\nexport * from './metadata_override';\nexport * from './private_export_testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {TestingCompiler as ɵTestingCompiler, TestingCompilerFactory as ɵTestingCompilerFactory} from './test_compiler';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n\nimport {resetFakeAsyncZone} from './fake_async';\nimport {TestBed} from './test_bed';\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n// Reset the test providers and the fake async zone before each test.\nif (_global.beforeEach) {\n  _global.beforeEach(() => {\n    TestBed.resetTestingModule();\n    resetFakeAsyncZone();\n  });\n}\n\n// TODO(juliemr): remove this, only used because we need to export something to have compilation\n// work.\nexport const __core_private_testing_placeholder__ = '';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ApplicationInitStatus, CompilerOptions, Component, Directive, InjectionToken, Injector, ModuleWithComponentFactories, NgModule, NgModuleFactory, NgModuleRef, NgZone, Optional, Pipe, PlatformRef, Provider, ReflectiveInjector, SchemaMetadata, SkipSelf, Type, ɵDepFlags as DepFlags, ɵERROR_COMPONENT_TYPE, ɵNodeFlags as NodeFlags, ɵclearProviderOverrides as clearProviderOverrides, ɵoverrideProvider as overrideProvider, ɵstringify as stringify} from '@angular/core';\n\nimport {AsyncTestCompleter} from './async_test_completer';\nimport {ComponentFixture} from './component_fixture';\nimport {MetadataOverride} from './metadata_override';\nimport {TestingCompiler, TestingCompilerFactory} from './test_compiler';\n\nconst UNDEFINED = new Object();\n\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @experimental\n */\nexport class TestComponentRenderer {\n  insertRootElement(rootElementId: string) {}\n}\n\nlet _nextRootElementId = 0;\n\n/**\n * @experimental\n */\nexport const ComponentFixtureAutoDetect =\n    new InjectionToken<boolean[]>('ComponentFixtureAutoDetect');\n\n/**\n * @experimental\n */\nexport const ComponentFixtureNoNgZone = new InjectionToken<boolean[]>('ComponentFixtureNoNgZone');\n\n/**\n * @experimental\n */\nexport type TestModuleMetadata = {\n  providers?: any[],\n  declarations?: any[],\n  imports?: any[],\n  schemas?: Array<SchemaMetadata|any[]>,\n};\n\n/**\n * @whatItDoes Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n * @description\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @stable\n */\nexport class TestBed implements Injector {\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  static initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]): TestBed {\n    const testBed = getTestBed();\n    testBed.initTestEnvironment(ngModule, platform, aotSummaries);\n    return testBed;\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  static resetTestEnvironment() { getTestBed().resetTestEnvironment(); }\n\n  static resetTestingModule(): typeof TestBed {\n    getTestBed().resetTestingModule();\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default compiler providers and settings\n   * which are defined in test_injector.js\n   */\n  static configureCompiler(config: {providers?: any[]; useJit?: boolean;}): typeof TestBed {\n    getTestBed().configureCompiler(config);\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef: TestModuleMetadata): typeof TestBed {\n    getTestBed().configureTestingModule(moduleDef);\n    return TestBed;\n  }\n\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents(): Promise<any> { return getTestBed().compileComponents(); }\n\n  static overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): typeof TestBed {\n    getTestBed().overrideModule(ngModule, override);\n    return TestBed;\n  }\n\n  static overrideComponent(component: Type<any>, override: MetadataOverride<Component>):\n      typeof TestBed {\n    getTestBed().overrideComponent(component, override);\n    return TestBed;\n  }\n\n  static overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>):\n      typeof TestBed {\n    getTestBed().overrideDirective(directive, override);\n    return TestBed;\n  }\n\n  static overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): typeof TestBed {\n    getTestBed().overridePipe(pipe, override);\n    return TestBed;\n  }\n\n  static overrideTemplate(component: Type<any>, template: string): typeof TestBed {\n    getTestBed().overrideComponent(component, {set: {template, templateUrl: null !}});\n    return TestBed;\n  }\n\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  static overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  static overrideProvider(token: any, provider: {useValue: any;}): void;\n  static overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): typeof TestBed {\n    getTestBed().overrideProvider(token, provider as any);\n    return TestBed;\n  }\n\n  static get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    return getTestBed().get(token, notFoundValue);\n  }\n\n  static createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    return getTestBed().createComponent(component);\n  }\n\n  private _instantiated: boolean = false;\n\n  private _compiler: TestingCompiler = null !;\n  private _moduleRef: NgModuleRef<any> = null !;\n  private _moduleFactory: NgModuleFactory<any> = null !;\n\n  private _compilerOptions: CompilerOptions[] = [];\n\n  private _moduleOverrides: [Type<any>, MetadataOverride<NgModule>][] = [];\n  private _componentOverrides: [Type<any>, MetadataOverride<Component>][] = [];\n  private _directiveOverrides: [Type<any>, MetadataOverride<Directive>][] = [];\n  private _pipeOverrides: [Type<any>, MetadataOverride<Pipe>][] = [];\n\n  private _providers: Provider[] = [];\n  private _declarations: Array<Type<any>|any[]|any> = [];\n  private _imports: Array<Type<any>|any[]|any> = [];\n  private _schemas: Array<SchemaMetadata|any[]> = [];\n  private _activeFixtures: ComponentFixture<any>[] = [];\n\n  private _aotSummaries: () => any[] = () => [];\n\n  platform: PlatformRef = null !;\n\n  ngModule: Type<any>|Type<any>[] = null !;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    this.platform = platform;\n    this.ngModule = ngModule;\n    if (aotSummaries) {\n      this._aotSummaries = aotSummaries;\n    }\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this.platform = null !;\n    this.ngModule = null !;\n    this._aotSummaries = () => [];\n  }\n\n  resetTestingModule() {\n    clearProviderOverrides();\n    this._compiler = null !;\n    this._moduleOverrides = [];\n    this._componentOverrides = [];\n    this._directiveOverrides = [];\n    this._pipeOverrides = [];\n\n    this._moduleRef = null !;\n    this._moduleFactory = null !;\n    this._compilerOptions = [];\n    this._providers = [];\n    this._declarations = [];\n    this._imports = [];\n    this._schemas = [];\n    this._instantiated = false;\n    this._activeFixtures.forEach((fixture) => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        console.error('Error during cleanup of component', fixture.componentInstance);\n      }\n    });\n    this._activeFixtures = [];\n  }\n\n  configureCompiler(config: {providers?: any[], useJit?: boolean}) {\n    this._assertNotInstantiated('TestBed.configureCompiler', 'configure the compiler');\n    this._compilerOptions.push(config);\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata) {\n    this._assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    if (moduleDef.providers) {\n      this._providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.declarations) {\n      this._declarations.push(...moduleDef.declarations);\n    }\n    if (moduleDef.imports) {\n      this._imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.schemas) {\n      this._schemas.push(...moduleDef.schemas);\n    }\n  }\n\n  compileComponents(): Promise<any> {\n    if (this._moduleFactory || this._instantiated) {\n      return Promise.resolve(null);\n    }\n\n    const moduleType = this._createCompilerAndModule();\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType)\n        .then((moduleAndComponentFactories) => {\n          this._moduleFactory = moduleAndComponentFactories.ngModuleFactory;\n        });\n  }\n\n  private _initIfNeeded() {\n    if (this._instantiated) {\n      return;\n    }\n    if (!this._moduleFactory) {\n      try {\n        const moduleType = this._createCompilerAndModule();\n        this._moduleFactory =\n            this._compiler.compileModuleAndAllComponentsSync(moduleType).ngModuleFactory;\n      } catch (e) {\n        if (getComponentType(e)) {\n          throw new Error(\n              `This test module uses the component ${stringify(getComponentType(e))} which is using a \"templateUrl\" or \"styleUrls\", but they were never compiled. ` +\n              `Please call \"TestBed.compileComponents\" before your test.`);\n        } else {\n          throw e;\n        }\n      }\n    }\n    const ngZone = new NgZone({enableLongStackTrace: true});\n    const ngZoneInjector = ReflectiveInjector.resolveAndCreate(\n        [{provide: NgZone, useValue: ngZone}], this.platform.injector);\n    this._moduleRef = this._moduleFactory.create(ngZoneInjector);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core. So casting to any\n    // before accessing it.\n    (this._moduleRef.injector.get(ApplicationInitStatus) as any).runInitializers();\n    this._instantiated = true;\n  }\n\n  private _createCompilerAndModule(): Type<any> {\n    const providers = this._providers.concat([{provide: TestBed, useValue: this}]);\n    const declarations = this._declarations;\n    const imports = [this.ngModule, this._imports];\n    const schemas = this._schemas;\n\n    \n    class DynamicTestModule {\n    static decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{providers, declarations, imports, schemas}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\n    const compilerFactory: TestingCompilerFactory =\n        this.platform.injector.get(TestingCompilerFactory);\n    this._compiler =\n        compilerFactory.createTestingCompiler(this._compilerOptions.concat([{useDebug: true}]));\n    this._compiler.loadAotSummaries(this._aotSummaries);\n    this._moduleOverrides.forEach((entry) => this._compiler.overrideModule(entry[0], entry[1]));\n    this._componentOverrides.forEach(\n        (entry) => this._compiler.overrideComponent(entry[0], entry[1]));\n    this._directiveOverrides.forEach(\n        (entry) => this._compiler.overrideDirective(entry[0], entry[1]));\n    this._pipeOverrides.forEach((entry) => this._compiler.overridePipe(entry[0], entry[1]));\n    return DynamicTestModule;\n  }\n\n  private _assertNotInstantiated(methodName: string, methodDescription: string) {\n    if (this._instantiated) {\n      throw new Error(\n          `Cannot ${methodDescription} when the test module has already been instantiated. ` +\n          `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n\n  get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    this._initIfNeeded();\n    if (token === TestBed) {\n      return this;\n    }\n    // Tests can inject things from the ng module and from the compiler,\n    // but the ng module can't inject things from the compiler and vice versa.\n    const result = this._moduleRef.injector.get(token, UNDEFINED);\n    return result === UNDEFINED ? this._compiler.injector.get(token, notFoundValue) : result;\n  }\n\n  execute(tokens: any[], fn: Function, context?: any): any {\n    this._initIfNeeded();\n    const params = tokens.map(t => this.get(t));\n    return fn.apply(context, params);\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this._assertNotInstantiated('overrideModule', 'override module metadata');\n    this._moduleOverrides.push([ngModule, override]);\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this._assertNotInstantiated('overrideComponent', 'override component metadata');\n    this._componentOverrides.push([component, override]);\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this._assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this._directiveOverrides.push([directive, override]);\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this._assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this._pipeOverrides.push([pipe, override]);\n  }\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  overrideProvider(token: any, provider: {useValue: any;}): void;\n  overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): void {\n    let flags: NodeFlags = 0;\n    let value: any;\n    if (provider.useFactory) {\n      flags |= NodeFlags.TypeFactoryProvider;\n      value = provider.useFactory;\n    } else {\n      flags |= NodeFlags.TypeValueProvider;\n      value = provider.useValue;\n    }\n    const deps = (provider.deps || []).map((dep) => {\n      let depFlags: DepFlags = DepFlags.None;\n      let depToken: any;\n      if (Array.isArray(dep)) {\n        dep.forEach((entry: any) => {\n          if (entry instanceof Optional) {\n            depFlags |= DepFlags.Optional;\n          } else if (entry instanceof SkipSelf) {\n            depFlags |= DepFlags.SkipSelf;\n          } else {\n            depToken = entry;\n          }\n        });\n      } else {\n        depToken = dep;\n      }\n      return [depFlags, depToken];\n    });\n    overrideProvider({token, flags, deps, value});\n  }\n\n  createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    this._initIfNeeded();\n    const componentFactory = this._compiler.getComponentFactory(component);\n\n    if (!componentFactory) {\n      throw new Error(\n          `Cannot create the component ${stringify(component)} as it was not imported into the testing module!`);\n    }\n\n    const noNgZone = this.get(ComponentFixtureNoNgZone, false);\n    const autoDetect: boolean = this.get(ComponentFixtureAutoDetect, false);\n    const ngZone: NgZone = noNgZone ? null : this.get(NgZone, null);\n    const testComponentRenderer: TestComponentRenderer = this.get(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n\n    const initComponent = () => {\n      const componentRef =\n          componentFactory.create(Injector.NULL, [], `#${rootElId}`, this._moduleRef);\n      return new ComponentFixture<T>(componentRef, ngZone, autoDetect);\n    };\n\n    const fixture = !ngZone ? initComponent() : ngZone.run(initComponent);\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n}\n\nlet _testBed: TestBed = null !;\n\n/**\n * @experimental\n */\nexport function getTestBed() {\n  return _testBed = _testBed || new TestBed();\n}\n\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * Notes:\n * - inject is currently a function because of some Traceur limitation the syntax should\n * eventually\n *   becomes `it('...', @Inject (object: AClass, async: AsyncTestCompleter) => { ... });`\n *\n * @stable\n */\nexport function inject(tokens: any[], fn: Function): () => any {\n  const testBed = getTestBed();\n  if (tokens.indexOf(AsyncTestCompleter) >= 0) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      // Return an async test method that returns a Promise if AsyncTestCompleter is one of\n      // the injected tokens.\n      return testBed.compileComponents().then(() => {\n        const completer: AsyncTestCompleter = testBed.get(AsyncTestCompleter);\n        testBed.execute(tokens, fn, this);\n        return completer.promise;\n      });\n    };\n  } else {\n    // Not using an arrow function to preserve context passed from call site\n    return function() { return testBed.execute(tokens, fn, this); };\n  }\n}\n\n/**\n * @experimental\n */\nexport class InjectSetupWrapper {\n  constructor(private _moduleDef: () => TestModuleMetadata) {}\n\n  private _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      getTestBed().configureTestingModule(moduleDef);\n    }\n  }\n\n  inject(tokens: any[], fn: Function): () => any {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\n\n/**\n * @experimental\n */\nexport function withModule(moduleDef: TestModuleMetadata): InjectSetupWrapper;\nexport function withModule(moduleDef: TestModuleMetadata, fn: Function): () => any;\nexport function withModule(moduleDef: TestModuleMetadata, fn?: Function | null): (() => any)|\n    InjectSetupWrapper {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      const testBed = getTestBed();\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\nfunction getComponentType(error: Error): Function {\n  return (error as any)[ɵERROR_COMPONENT_TYPE];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Compiler, CompilerOptions, Component, ComponentFactory, Directive, Injector, NgModule, Pipe, Type} from '@angular/core';\n\nimport {MetadataOverride} from './metadata_override';\n\nfunction unimplemented(): any {\n  throw Error('unimplemented');\n}\n\n/**\n * Special interface to the compiler only used by testing\n *\n * @experimental\n */\nexport class TestingCompiler extends Compiler {\n  get injector(): Injector { throw unimplemented(); }\n  overrideModule(module: Type<any>, overrides: MetadataOverride<NgModule>): void {\n    throw unimplemented();\n  }\n  overrideDirective(directive: Type<any>, overrides: MetadataOverride<Directive>): void {\n    throw unimplemented();\n  }\n  overrideComponent(component: Type<any>, overrides: MetadataOverride<Component>): void {\n    throw unimplemented();\n  }\n  overridePipe(directive: Type<any>, overrides: MetadataOverride<Pipe>): void {\n    throw unimplemented();\n  }\n  /**\n   * Allows to pass the compile summary from AOT compilation to the JIT compiler,\n   * so that it can use the code generated by AOT.\n   */\n  loadAotSummaries(summaries: () => any[]) { throw unimplemented(); };\n\n  /**\n   * Gets the component factory for the given component.\n   * This assumes that the component has been compiled before calling this call using\n   * `compileModuleAndAllComponents*`.\n   */\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> { throw unimplemented(); }\n}\n\n/**\n * A factory for creating a Compiler\n *\n * @experimental\n */\nexport abstract class TestingCompilerFactory {\n  abstract createTestingCompiler(options?: CompilerOptions[]): TestingCompiler;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injectable completer that allows signaling completion of an asynchronous test. Used internally.\n */\nexport class AsyncTestCompleter {\n  private _resolve: (result: any) => void;\n  private _reject: (err: any) => void;\n  private _promise: Promise<any> = new Promise((res, rej) => {\n    this._resolve = res;\n    this._reject = rej;\n  });\n  done(value?: any) { this._resolve(value); }\n\n  fail(error?: any, stackTrace?: string) { this._reject(error); }\n\n  get promise(): Promise<any> { return this._promise; }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst FakeAsyncTestZoneSpec = (Zone as any)['FakeAsyncTestZoneSpec'];\ntype ProxyZoneSpec = {\n  setDelegate(delegateSpec: ZoneSpec): void; getDelegate(): ZoneSpec; resetDelegate(): void;\n};\nconst ProxyZoneSpec: {get(): ProxyZoneSpec; assertPresent: () => ProxyZoneSpec} =\n    (Zone as any)['ProxyZoneSpec'];\n\nlet _fakeAsyncTestZoneSpec: any = null;\n\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nexport function resetFakeAsyncZone() {\n  _fakeAsyncTestZoneSpec = null;\n  ProxyZoneSpec.assertPresent().resetDelegate();\n}\n\nlet _inFakeAsyncCall = false;\n\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @returns {Function} The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nexport function fakeAsync(fn: Function): (...args: any[]) => any {\n  // Not using an arrow function to preserve context passed from call site\n  return function(...args: any[]) {\n    const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n    if (_inFakeAsyncCall) {\n      throw new Error('fakeAsync() calls can not be nested');\n    }\n    _inFakeAsyncCall = true;\n    try {\n      if (!_fakeAsyncTestZoneSpec) {\n        if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n          throw new Error('fakeAsync() calls can not be nested');\n        }\n\n        _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n      }\n\n      let res: any;\n      const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n      proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n      try {\n        res = fn.apply(this, args);\n        flushMicrotasks();\n      } finally {\n        proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n            `periodic timer(s) still in the queue.`);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n      }\n      return res;\n    } finally {\n      _inFakeAsyncCall = false;\n      resetFakeAsyncZone();\n    }\n  };\n}\n\nfunction _getFakeAsyncZoneSpec(): any {\n  if (_fakeAsyncTestZoneSpec == null) {\n    throw new Error('The code should be running in the fakeAsync zone to call this function');\n  }\n  return _fakeAsyncTestZoneSpec;\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nexport function tick(millis: number = 0): void {\n  _getFakeAsyncZoneSpec().tick(millis);\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns {number} The simulated time elapsed, in millis.\n *\n * @experimental\n */\nexport function flush(maxTurns?: number): number {\n  return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nexport function discardPeriodicTasks(): void {\n  const zoneSpec = _getFakeAsyncZoneSpec();\n  const pendingTimers = zoneSpec.pendingPeriodicTimers;\n  zoneSpec.pendingPeriodicTimers.length = 0;\n}\n\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nexport function flushMicrotasks(): void {\n  _getFakeAsyncZoneSpec().flushMicrotasks();\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectorRef, ComponentRef, DebugElement, ElementRef, NgZone, RendererFactory2, getDebugNode} from '@angular/core';\n\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @stable\n */\nexport class ComponentFixture<T> {\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement: DebugElement;\n\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance: T;\n\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement: any;\n\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef: ElementRef;\n\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef: ChangeDetectorRef;\n\n  private _renderer: RendererFactory2|null|undefined;\n  private _isStable: boolean = true;\n  private _isDestroyed: boolean = false;\n  private _resolve: ((result: any) => void)|null = null;\n  private _promise: Promise<any>|null = null;\n  private _onUnstableSubscription: any /** TODO #9100 */ = null;\n  private _onStableSubscription: any /** TODO #9100 */ = null;\n  private _onMicrotaskEmptySubscription: any /** TODO #9100 */ = null;\n  private _onErrorSubscription: any /** TODO #9100 */ = null;\n\n  constructor(\n      public componentRef: ComponentRef<T>, public ngZone: NgZone|null,\n      private _autoDetect: boolean) {\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = <DebugElement>getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n    this.ngZone = ngZone;\n\n    if (ngZone) {\n      this._onUnstableSubscription =\n          ngZone.onUnstable.subscribe({next: () => { this._isStable = false; }});\n      this._onMicrotaskEmptySubscription = ngZone.onMicrotaskEmpty.subscribe({\n        next: () => {\n          if (this._autoDetect) {\n            // Do a change detection run with checkNoChanges set to true to check\n            // there are no changes on the second run.\n            this.detectChanges(true);\n          }\n        }\n      });\n      this._onStableSubscription = ngZone.onStable.subscribe({\n        next: () => {\n          this._isStable = true;\n          // Check whether there is a pending whenStable() completer to resolve.\n          if (this._promise !== null) {\n            // If so check whether there are no pending macrotasks before resolving.\n            // Do this check in the next tick so that ngZone gets a chance to update the state of\n            // pending macrotasks.\n            scheduleMicroTask(() => {\n              if (!ngZone.hasPendingMacrotasks) {\n                if (this._promise !== null) {\n                  this._resolve !(true);\n                  this._resolve = null;\n                  this._promise = null;\n                }\n              }\n            });\n          }\n        }\n      });\n\n      this._onErrorSubscription =\n          ngZone.onError.subscribe({next: (error: any) => { throw error; }});\n    }\n  }\n\n  private _tick(checkNoChanges: boolean) {\n    this.changeDetectorRef.detectChanges();\n    if (checkNoChanges) {\n      this.checkNoChanges();\n    }\n  }\n\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges: boolean = true): void {\n    if (this.ngZone != null) {\n      // Run the change detection inside the NgZone so that any async tasks as part of the change\n      // detection are captured by the zone and can be waited for in isStable.\n      this.ngZone.run(() => { this._tick(checkNoChanges); });\n    } else {\n      // Running without zone. Just do the change detection.\n      this._tick(checkNoChanges);\n    }\n  }\n\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges(): void { this.changeDetectorRef.checkNoChanges(); }\n\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   */\n  autoDetectChanges(autoDetect: boolean = true) {\n    if (this.ngZone == null) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set');\n    }\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable(): boolean { return this._isStable && !this.ngZone !.hasPendingMacrotasks; }\n\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable(): Promise<any> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    } else if (this._promise !== null) {\n      return this._promise;\n    } else {\n      this._promise = new Promise(res => { this._resolve = res; });\n      return this._promise;\n    }\n  }\n\n\n  private _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer as RendererFactory2 | null;\n  }\n\n  /**\n    * Get a promise that resolves when the ui state is stable following animations.\n    */\n  whenRenderingDone(): Promise<any> {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n\n  /**\n   * Trigger component destruction.\n   */\n  destroy(): void {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      if (this._onUnstableSubscription != null) {\n        this._onUnstableSubscription.unsubscribe();\n        this._onUnstableSubscription = null;\n      }\n      if (this._onStableSubscription != null) {\n        this._onStableSubscription.unsubscribe();\n        this._onStableSubscription = null;\n      }\n      if (this._onMicrotaskEmptySubscription != null) {\n        this._onMicrotaskEmptySubscription.unsubscribe();\n        this._onMicrotaskEmptySubscription = null;\n      }\n      if (this._onErrorSubscription != null) {\n        this._onErrorSubscription.unsubscribe();\n        this._onErrorSubscription = null;\n      }\n      this._isDestroyed = true;\n    }\n  }\n}\n\nfunction scheduleMicroTask(fn: Function) {\n  Zone.current.scheduleMicroTask('scheduleMicrotask', fn);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', async(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * });\n * ```\n *\n * @stable\n */\nexport function async(fn: Function): (done: any) => any {\n  // If we're running using the Jasmine test framework, adapt to call the 'done'\n  // function when asynchronous activity is finished.\n  if (_global.jasmine) {\n    // Not using an arrow function to preserve context passed from call site\n    return function(done: any) {\n      if (!done) {\n        // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n        // fake it here and assume sync.\n        done = function() {};\n        done.fail = function(e: any) { throw e; };\n      }\n      runInTestZone(fn, this, done, (err: any) => {\n        if (typeof err === 'string') {\n          return done.fail(new Error(<string>err));\n        } else {\n          done.fail(err);\n        }\n      });\n    };\n  }\n  // Otherwise, return a promise which will resolve when asynchronous activity\n  // is finished. This will be correctly consumed by the Mocha framework with\n  // it('...', async(myFn)); or can be used in a custom framework.\n  // Not using an arrow function to preserve context passed from call site\n  return function() {\n    return new Promise<void>((finishCallback, failCallback) => {\n      runInTestZone(fn, this, finishCallback, failCallback);\n    });\n  };\n}\n\nfunction runInTestZone(\n    fn: Function, context: any, finishCallback: Function, failCallback: Function) {\n  const currentZone = Zone.current;\n  const AsyncTestZoneSpec = (Zone as any)['AsyncTestZoneSpec'];\n  if (AsyncTestZoneSpec === undefined) {\n    throw new Error(\n        'AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/async-test.js');\n  }\n  const ProxyZoneSpec = (Zone as any)['ProxyZoneSpec'] as {\n    get(): {setDelegate(spec: ZoneSpec): void; getDelegate(): ZoneSpec;};\n    assertPresent: () => void;\n  };\n  if (ProxyZoneSpec === undefined) {\n    throw new Error(\n        'ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/proxy.js');\n  }\n  const proxyZoneSpec = ProxyZoneSpec.get();\n  ProxyZoneSpec.assertPresent();\n  // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n  // If we do it in ProxyZone then we will get to infinite recursion.\n  const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n  const previousDelegate = proxyZoneSpec.getDelegate();\n  proxyZone.parent.run(() => {\n    const testZoneSpec: ZoneSpec = new AsyncTestZoneSpec(\n        () => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            finishCallback();\n          });\n        },\n        (error: any) => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            failCallback(error);\n          });\n        },\n        'test');\n    proxyZoneSpec.setDelegate(testZoneSpec);\n  });\n  return Zone.current.runGuarded(fn, context);\n}\n"], "names": ["override<PERSON><PERSON><PERSON>", "clearProviderOverrides"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ASkCA,eAAW,EAAX;IACA,8EAAA;;;QAGA,wEAAA;QACA,MAAA,CAAA,UAAiB,IAAjB;YACA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;gBACA,qFAAA;gBACQ,gCAAR;gBACA,IAAU,GAAV,cAA2B,CAA3B,CAAA;gBACA,IAAA,CAAA,IAAA,GAAA,UAAA,CAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA;YACA,aAAe,CAAf,EAAA,EAAoB,IAAI,EAAxB,IAAA,EAAA,UAAA,GAAA;gBACA,EAAA,CAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,CAAA,CAAA;oBACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBACA,CAAA;gBACA,IAAA,CAAA,CAAA;;;;;IAKE,CAAF;IACA,4EAAA;IACA,2EAAA;IACA,gEAAA;IACA,wEAAA;IACA,MAAA,CAAA;QAAA,iBAMA;QAJA,MAAA,CAAA,IAAA,OAAA,CACkB,UADlB,cAAA,EAAA,YAAA;YAEA,aAAA,CAAA,EAA0B,EAA1B,KAAA,EAAkC,cAAlC,EAAA,YAAA,CAAA,CAAA;QACA,CAAA,CAAQ,CAAR;IACE,CAAF,CAAA;AACA,CAAA;AAEA,uBAAA,EAAA,EAAA,OAAA,EAAA,cAAA,EAAA,YAAA;IACA,IAAA,WAAA,GAAA,IAAA,CAAA,OAAA,CAAA;IACE,IAAM,iBAAR,GAAqC,IAArC,CAAA,mBAAA,CAAA,CAAA;IAIE,EAAF,CAAA,CAAM,iBAAN,KAAA,SAAA,CAAA,CAAA,CAAA;QACI,MAAM,IAAI,KAAK,CACX,kFADR;YAEQ,4EAAR,CAAA,CAAA;IACA,CAAG;IACD,IAAM,aAAa,GAAG,IAAxB,CAAA,eAA4C,CAA5C,CAAA;IACE,EAAF,CAAA,CAAA,aAAA,KAAA,SAAA,CAAA,CAAA,CAAA;;;IAGE,CAAF;IACE,IAAM,aAAR,GAAwB,aAAxB,CAAA,GAAyC,EAAzC,CAAA;IACE,aAAF,CAAA,aAAA,EAAA,CAAA;IACA,iEAAA;;IAGA,IAAA,SAAA,GAAA,IAAA,CAAyB,OAAzB,CAAA,WAAA,CAAA,eAAA,CAAA,CAAA;IACA,IAAA,gBAAA,GAAA,aAAA,CAAA,WAAA,EAAA,CAAA;;QAEA,IAAA,YAAA,GAAA,IAA2B,iBAA3B,CAAA;YACA,qCAAA;YACA,WAAA,CAAA,GAAA,CAAA;gBACA,EAAA,CAAA,CAAA,aAAA,CAAA,WAAA,EAAA,IAAA,YAAA,CAAA,CAAA,CAAA;oBACA,6EAAA;;gBAGA,CAAA;gBACY,cAAZ,EAAA,CAA6B;;QAE7B,CAAA,EAAA,UAAA,KAAA;YACA,qCAAA;YACA,WAAA,CAAA,GAAA,CAAwB;gBACxB,EAAA,CAAA,CAAA,aAAA,CAAA,WAAA,EAAA,IAAA,YAAA,CAAA,CAAA,CAAA;oBACA,6EAAA;oBAEA,aAA6B,CAAC,WAA9B,CAA0C,gBAA1C,CAAA,CAAA;gBACA,CAAA;gBACc,YAAd,CAAA,KAAgC,CAAC,CAAjC;YACA,CAAA,CAAA,CAAA;;QD/GA,aAAA,CAAA,WAAA,CAAA,YAAA,CAAA,CAAA;;;;;;;;;;;AAgBA;;;;GAsCA;AAXA;IACA,0BAAA,YAAkC,EAAlC,MAAA,EAAA,WAAA;QAAA,iBA6DA;QA5DU,IAAV,CAAA,YAAA,GAAuD,YAAvD,CAAA;QACU,IAAV,CAAA,MAAA,GAAA,MAA4C,CAAC;QACnC,IAAV,CAAA,WAAA,GAAA,WAAA,CAA2D;QACjD,IAAV,CAAA,SAAA,GAAA,IAAA,CAAA;QACU,IAAV,CAAA,YAAA,GAAA,KAAA,CAAA;QACU,IAAV,CAAA,QAAA,GAAA,IAAA,CAAA;QAKI,IAAI,CAAC,QAAT,GAAA,IAAA,CAAA;QACI,IAAI,CAAC,uBAAT,GAAmC,IAAnC,CAAA;QACI,IAAI,CAAC,qBAAT,GAAA,IAAmD,CAAnD;QACI,IAAI,CAAC,6BAAT,GAAyC,IAAzC,CAAA;QACI,IAAI,CAAC,oBAAoB,GAA7B,IAAA,CAAA;QACI,IAAI,CAAC,iBAAT,GAAA,YAAA,CAAA,iBAAA,CAAA;QACI,IAAI,CAAC,UAAT,GAAA,YAAA,CAAA,QAAA,CAAA;QAEI,IAAI,CAAR,YAAA,GAAA,YAAA,CAAA,IAAA,CAAA,UAAA,CAAA,aAAA,CAAA,CAAA;QACA,IAAM,CAAN,iBAAA,GAAA,YAAA,CAAA,QAAA,CAAA;QACA,IAAA,CAAA,aAAA,GAAA,IAA2B,CAAC,UAAU,CAAtC,aAAA,CAAA;QACA,IAAM,CAAN,YAAA,GAAA,YAAA,CAAA;QACA,IAAA,CAAA,MAAA,GAAc,MAAd,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAU,CAAV;;;YAGA,IAAA,CAAA,6BAAA,GAAqC,MAArC,CAAA,gBAAA,CAAA,SAAA,CAAA;gBACA,IAAA,EAAA;oBACA,EAAA,CAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;wBACA,qEAAA;wBACA,0CAA4D;wBAC5D,KAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;oBACU,CAAV;;aAEA,CAAA,CAAA;;;;oBAIA,sEAAA;oBACA,EAAA,CAAA,CAAA,KAAc,CAAd,QAAA,KAAA,IAAA,CAAA,CAAA,CAAA;wBACA,wEAAA;wBACA,qFAAA;wBACA,sBAAA;wBACA,iBAAuB,CAAvB;4BACA,EAAA,CAAA,CAAA,CAAiB,MAAjB,CAAA,oBAAA,CAAA,CAAA,CAAA;gCACA,EAAA,CAAA,CAAA,KAAA,CAAA,QAAA,KAAA,IAAA,CAAA,CAAA,CAAA;oCACA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;oCACA,KAAA,CAAA,QAAA,GAAA,IAAA,CAAA;oCACA,KAAA,CAAA,QAAA,GAAA,IAAA,CAAA;gCACA,CAAA;4BAEA,CAAA;wBACA,CAAA,CAAA,CAAA;oBACA,CAAA;gBACA,CAAA;aAEA,CAAA,CAAA;YACQ,IAAR,CAAA,oBAAA;gBACA,MAAA,CAAA,OAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,UAAA,KAAA,IAAA,MAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;IACA,gCAAA,GAAA,UAAA,cAAA;;;;QAKA,CAAA;IACA,CAAA;;;OAGA;IACA,wCAAA,GAAA,UAAA,cAAA;QAAA;QAAA,+BAAA,EAAA,qBAAA;QAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,CAAA,CAAA;;YAEM,wEAAN;YACA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,cAAA,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;;;;QAKA,CAAA;;;;;;IAOE;;;;OAIF;IACA,4CAAA,GAAA,UAAA,UAAA;QAAA,2BAAA,EAAA,iBAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,CAAA,CAAA;;;;;IAME,CAAF;;;;;;;;;;;OAWA;IACA,qCAAA,GAAA;QAAA,iBAYA;QAXA,EAAA,CAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA;YAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAM,CAAN,EAAA,CAAA,CAAA,IAAA,CAAA,QAAA,KAAA,IAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA;QACA,CAAA;QAGA,IAAA,CAAA,CAAA;YACQ,IAAI,CAAC,QAAb,GAAA,IAAA,OAAA,CAAoC,UAAA,GAApC,IAAA,KAAA,CAAA,QAAA,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,MAAN,CAAA,IAAA,CAAA,QAAA,CAA2B;QAC3B,CAAK;IACL,CAAA;IACA,uCAAA,GAAA;;;;QAKA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA;IACA,CAAA;IACA;;QAEA;IACA,4CAAA,GAAA;QACA,IAAA,QAAA,GAAA,IAAA,CAAA,YAAA,EAAA,CAAA;;;;QAKA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA;IACA,CAAA;IACA;;OAEA;IACA,kCAAA,GAAA;QACA,EAAA,CAAA,CAAA,CAAO,IAAP,CAAA,YAAA,CAAA,CAAA,CAAA;YACM,IAAI,CAAV,YAAA,CAAA,OAAA,EAAA,CAAA;YACA,EAAA,CAAA,CAAQ,IAAI,CAAC,uBAAb,IAAA,IAAA,CAAA,CAA8C,CAA9C;gBACQ,IAAI,CAAC,uBAAb,CAAqC,WAArC,EAAA,CAAA;gBACA,IAAA,CAAA,uBAAA,GAAA,IAAA,CAAA;YACM,CAAN;YACA,EAAA,CAAA,CAAQ,IAAI,CAAC,qBAAb,IAAA,IAA0C,CAAC,CAA3C,CAAA;gBACQ,IAAI,CAAC,qBAAb,CAAA,WAAA,EAAA,CAAiD;gBACjD,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA;YACM,CAAN;YACA,EAAA,CAAA,CAAQ,IAAI,CAAC,6BAAb,IAAA,IAAA,CAAA,CAAA,CAAA;gBACQ,IAAI,CAAC,6BAAb,CAAA,WAAA,EAAA,CAAA;gBACA,IAAA,CAAA,6BAAA,GAAA,IAAA,CAAA;YACM,CAAN;YACA,EAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,IAAA,IAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,oBAAA,CAAA,WAAA,EAAA,CAAA;gBACA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA;YAEA,CAAA;YACA,IAAc,CAAC,YAAf,GAAA,IAAA,CAAA;QACA,CAAA;;IDnNA,uBAAA;AAAA,CAAA,AC2CA,ID3CA;;;;;;;;;;;;;;;;;;;GA6BA;;;;;;;;;;;;;;;;;;;;;;;GAwBA;AACA,mBAAA,EAAA;IACA,wEAAA;IACA,MAAA,CAAA;QAAA,cAAA;aAAA,UAAA,EAAA,qBAAA,EAAA,IAAA;YAAA,yBAAA;;QACI,IAAJ,aAAA,GAAA,aAAA,CAAA,aAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAM,gBAAN,CAAA,CAAA,CAAA;YACA,MAAA,IAAA,KAAA,CAAA,qCAAA,CAAA,CAAA;QACA,CAAA;QACA,gBAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA;YACA,EAAA,CAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;gBAEU,EAAV,CAAkB,CAAC,aAAnB,CAAA,WAAA,EAAA,YAAA,qBAAA,CAAA,CAAA,CAAA;oBACA,MAAA,IAAA,KAA6B,CAA7B,qCAAA,CAAA,CAAA;gBACA,CAAA;gBACU,sBAAV,GAAA,IAAA,qBAAA,EAAA,CAAA;YACA,CAAA;YACA,IAAQ,GAAR,SAAA,CAAA;YACA,IAAA,iBAAA,GAAA,aAAA,CAAA,WAAA,EAAA,CAAA;YAAA,aAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,CAAA;YACA,IAAQ,CAAR;gBACA,GAAA,GAAA,EAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;gBAEU,eAAV,EAAA,CAAA;YACA,CAAA;oBAEY,CAAZ;gBACA,aAAA,CAAA,WAAA,CAAA,iBAAA,CAAA,CAAA;YAEM,CAAN;YACA,EAAA,CAAA,CAAQ,sBAAR,CAAA,qBAAA,CAAA,MAAA,GAAA,CACmD,CAAC,CADpD,CAAA;gBAEA,MAAA,IAAA,KAAA,CAAA,sBAAA,CAAA,qBAAA,CAAA,MAAA,MAAA;oBACA,uCAAA,CAAA,CAAA;YACA,CAAA;YAAA,EAAA,CAAA,CAAc,sBAAd,CAAA,aAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;gBACA,MAAA,IAAA,KAAyB,CAAzB,sBAAA,CAAA,aAAA,CAAA,MAAA,kCAAA,CAAA,CAAA;YACM,CAAN;YACA,MAAA,CAAA,GAAA,CAAA;QACA,CAAA;gBACA,CAAA;YAEA,gBAAA,GAAA,KAAA,CAAA;YACA,kBAA4B,EAA5B,CAAA;QACI,CAAJ;IACA,CAAG,CAAH;AACA,CAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;GAIA;;;;;;AAOA;;;;GDnJA;;;;;;;;;;;;;;;;;;;;GAiBA,CAAA;IAMA;QAAA;QALM,IAAN,CAAA,QAAA,GAAA,IAAA,OAAA,CAAA,UAAyC,GAAzC,EAAA,GAAA;YAEA,KAAoB,CAApB,QAAA,GAAA,GAAA,CAAA;YAEA,KAAA,CAAA,OAAA,GAAuC,GAAvC,CAA2C;QAC3C,CAAA,CAAA,CAAA;;IDvBA,iCAAA,GAAA,UAAA,KAAA,IAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;CCiBA;;;;;;;;;;;ADIA;;;;GAIA;AACA;IAAA,2CAAA;IAAA;;;IACA,sBAAI,qCAAJ;aAAA,cAAA,MAAuB,aAAvB,EAAA,CAAA,CAAA,CAAA;;;OAAA;IACA,wCAAA,GAAA,UAAA,MAAA,EAAA,SAAA;QACA,MAAA,aAAA,EAAA,CAAA;IACA,CAAA;IACA,2CAAA,GAAA,UAAA,SAAA,EAAA,SAAA;QACA,MAAA,aAAA,EAAA,CAAA;IACA,CAAA;IACA,2CAAA,GAAA,UAAA,SAAA,EAAA,SAAA;;;;;IAKE,CAAF;;;;;;;IAOE;;;;;;;CApBF,CAAA,QAAA;AA4BA;;;;;;;;;;;;;;;;;;ADhCA;;;;;;;IASA,CAAA;;IAAA,4BAAA;AAAA,CAAA,IAAA;;;;GAMA;;;;;;;;;;;;;;GAuIA;AACA;IAEA;QAEU,IAAV,CAAA,aAAA,GAA0B,KAAgD,CAAC;QACjE,IAAV,CAAA,SAAA,GAAA,IAAA,CAAA;QACU,IAAV,CAAA,UAAA,GAAA,IAAA,CAAA;QACU,IAAV,CAAA,cAAwB,GAA0C,IAAlE,CAAA;QAEU,IAAV,CAAA,gBAAsC,GAAtC,EAAA,CAAA;QACU,IAAV,CAAA,gBAAsD,GAAG,EAAzD,CAAA;QACU,IAAV,CAAA,mBAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,mBAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,cAAA,GAAA,EAAA,CAAuD;QAE7C,IAAV,CAAA,UAAA,GAAuB,EAAvB,CAAuC;QAErC,IAAF,CAAA,aAAA,GAAiC,EAAjC,CAAA;QAEE,IAAF,CAAA,QAAU,GAA0B,EAApC,CAAA;QA+QA,IAAA,CAAA,QAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;OAlYA;;;;;;IAOE;;;;OAKF;;;;;IAME,CAAF;IACA;;;;;;;IAQE,CAAF;IACA;;;;;;;;IASE;;;;OAKF;IAES,yBAAiB,GAAxB,cAAF,MAAA,CAAiD,UAAjD,EAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA;IAEA,sBAAA,GAAA,UAAA,QAAA,EAAA,QAAA;QACI,UAAJ,EAAA,CAAA,cAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;IAEE,CAAF;IAEA,yBAAA,GAAA,UAAA,SAAkC,EAAlC,QAA4C;QACxC,UAAJ,EAAA,CAAA,iBAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;IAEE,CAAF;IACA,yBAAA,GAAA,UAAA,SAAkC,EAAE,QAAQ;QACxC,UAAJ,EAAA,CAAA,iBAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;IAEE,CAAF;IACA,oBAAA,GAAA,UAAA,IAAA,EAAA,QAAkC;QAC9B,UAAJ,EAAA,CAAA,YAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;IAWE,CAAF;IAKA,wBAAA,GAAA,UAAA,SAAiC,EAAjC,QAAA;QACI,UAAJ,EAAA,CAAA,iBAAA,CAAA,SAAA,EAAA,EAAA,GAAA,EAAA,EAAA,QAAA,UAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;IAEE,CAAF;IACA,wBAAuB,GAAvB,UAAwB,KAAxB,EAAA,QAAA;QACA,UAAA,EAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;QAEA,MAAA,CAAA,OAAA,CAAA;IACA,CAAA;IACA,WAAA,GAAA,UAAA,KAAA,EAAA,aAAA;QAAA,8BAAA,EAAA,gBAAA,QAAA,CAAA,kBAAA;;;;;;;;;;;;;;;;;;OA6CA;IACA,qCAAA,GAAA,UAAoB,QAAQ,EAA5B,QAAA,EAAA,YAAA;QACI,EAAJ,CAAA,CAAQ,IAAR,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YACM,MAAN,IAAA,KAAA,CAAA,8DAAA,CAAA,CAAA;QACA,CAAK;QACL,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;;IAOE;;;;OAIF;IACA,sCAAA,GAAA;QAEA,IAAA,CAAA,kBAAA,EAAA,CAAA;QACIC,IAAJA,CAAAA,QAAAA,GAAAA,IAAAA,CAAAA;QACI,IAAI,CAAC,QAAT,GAAA,IAAA,CAA2B;QACvB,IAAI,CAAC,aAAT,GAAyB,cAAM,OAAA,EAA/B,EAA+B,CAA/B,CAAA;IACA,CAAA;IACA,oCAAA,GAAA;QACI,uBAAJ,EAA6B,CAA7B;QAEI,IAAI,CAAC,SAAT,GAAA,IAAA,CAA4B;QACxB,IAAI,CAAC,gBAAT,GAAA,EAAgC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,GAAG,EAA/B,CAAA;QACI,IAAI,CAAC,mBAAT,GAAA,EAAA,CAAA;QACI,IAAI,CAAC,cAAT,GAAA,EAA4B,CAA5B;QACI,IAAI,CAAC,UAAT,GAAsB,IAAtB,CAAA;QACI,IAAI,CAAC,cAAc,GAAvB,IAAA,CAAA;QACI,IAAI,CAAC,gBAAgB,GAAzB,EAA8B,CAAC;QAC3B,IAAI,CAAC,UAAT,GAAA,EAAwB,CAAC;QACzB,IAAM,CAAN,aAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,QAAA,GAAgB,EAAhB,CAAA;QACA,IAAA,CAAO,QAAP,GAAA,EAAA,CAAA;QAAA,IAAQ,CAAR,aAAA,GAAA,KAAA,CAAA;QACA,IAAA,CAAA,eAAA,CAAqB,OAArB,CAAA,UAAA,OAAA;YACA,IAAA,CAAA;gBACA,OAAA,CAAA,OAAA,EAAA,CAAA;YACQ,CAAC;YACT,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,OAAA,CAAA,KAAA,CAAA,mCAAA,EAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;YACQ,CAAC;QACL,CAAJ,CAAA,CAAA;QACA,IAAA,CAAA,eAAA,GAAA,EAAA,CAAA;IAEE,CAAF;IACA,mCAAA,GAAA,UAAA,MAAA;QACI,IAAI,CAAR,sBAAA,CAAA,2BAAA,EAAA,wBAAA,CAAA,CAAA;QACA,IAAM,CAAN,gBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;IACA,CAAA;IACA,wCAAA,GAAA,UAAA,SAAgC;QAChC,IAAM,CAAN,sBAA6B,CAAC,gCAA9B,EAAA,2BAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YACQ,CAAA,KAAA,IAAR,CAAA,UAAA,CAAA,CAAA,IAAA,WAAA,SAAA,CAAA,SAAA,EAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,YAAA,CAAA,CAAA,CAAA;YACQ,CAAA,KAAA,IAAR,CAAA,aAAA,CAAA,CAA2B,IAA3B,WAAA,SAAA,CAAA,YAAA,EAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;YACA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,CAAA,IAAA,WAAA,SAAA,CAAA,OAAA,EAAA;QAEA,CAAA;QACI,EAAJ,CAAA,CAAQ,SAAR,CAAA,OAAA,CAAA,CAA2B,CAA3B;YACM,CAAA,KAAA,IAAN,CAAA,QAAA,CAAA,CAAoB,IAApB,WAA4B,SAA5B,CAAA,OAAA,EAAA;QACA,CAAK;;IAEL,CAAA;IACA,mCAAA,GAAA;QAAA,iBAUA;QATA,EAAA,CAAA,CAAA,IAAA,CAAa,cAAb,IAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA;YACU,MAAV,CAAA,OAAA,CAAA,OAAgC,CAAhC,IAAA,CAAA,CAAA;QACA,CAAS;QACT,IAAA,UAAA,GAAA,IAAA,CAAA,wBAAA,EAAA,CAAA;QAEA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,kCAAA,CAAA,UAAA,CAAA;aACA,IAAa,CAAb,UAAA,2BAAA;YACM,KAAN,CAAA,cAAA,GAAA,2BAAA,CAAA,eAAA,CAAA;QACA,CAAK,CAAL,CAAA;IACA,CAAA;IACA,+BAAA,GAAA;QACA,EAAA,CAAA,CAAA,IAAQ,CAAR,aAAA,CAAA,CAAwB,CAAxB;YACA,MAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,CAAO,IAAP,CAAA,cAAA,CAAA,CAAA,CAAA;YAAQ,IAAR,CAAA;gBACQ,IAAR,UAAA,GAAA,IAA+B,CAA/B,wBAAA,EAAA,CAAA;gBACA,IAAU,CAAV,cAAyB;oBAEzB,IAAc,CAAd,SAAA,CAAA,iCAAA,CAAA,UAAA,CAAA,CAAA,eAAA,CAAA;YACA,CAAA;YAAA,KAAA,CAAA,CAAA,CAAA,CAAe,CAAf,CAAA;gBACA,EAAA,CAAA,CAAU,gBAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACA,MAAA,IAAA,KAAA,CAAA,yCAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,uFAAA;wBACA,6DAAA,CAAA,CAAA;gBACA,CAAA;gBACA,IAAgB,CAAhB,CAAA;oBACA,MAAA,CAAA,CAAwB;gBAExB,CAAA;;;QAGK,IAAL,MAAA,GAAoB,IAApB,MAA8B,CAA9B,EAAiC,oBAAjC,EAAuD,IAAvD,EAAA,CAAA,CAAA;QACI,IAAJ,cAAA,GAAA,kBAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,cAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA;QAEA,yFAAA;QACI,uBAAuB;QACvB,IAAJ,CAAA,UAAA,CAAA,QAAA,CAA6B,GAA7B,CAAA,qBAAA,CAAA,CAAA,eAAA,EAAA,CAAA;QACI,IAAJ,CAAA,aAAA,GAAyB,IAAzB,CAAA;IACA,CAAA;IAGA,0CAAA,GAAA;QAAA,iBA4BA;;QA3BW,IAAX,YAAA,GAAA,IAAA,CAAA,aAAA,CAAA;QACA,IAAE,OAAF,GAAA,CAAA,IAAA,CAAkB,QAAlB,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAA,OAAA,GAAA,IAAA,CAAA,QAAA,CAAA;;;YAEO,CAAP;YAAO,wBAAP;QAAO,CAAP,IAAA;QAII,iBAAJ,CAAA,UACY,GADZ;YAEQ,EAAR,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,SAAA,WAAA,EAAA,YAAA,cAAA,EAAA,OAAA,SAAA,EAAA,OAAA,SAAA,EAAA,EAAA,EAAA;SACA,CAAA;QACI,kBAAJ;QACI,iBAAJ,CAAA,cAAA,GAAA,cAA6C,OAAA,EAA7C,EAA6C,CAA7C,CAAA;QACI,IAAJ,eAAA,GAA4B,IAA5B,CAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,sBAAA,CAAA,CAAA;QAEI,IAAI,CAAC,SAAT;YAEQ,eAAe,CAAC,qBAAxB,CAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,CAA4E,EAAE,QAAQ,EAAE,IAAI,EAA5F,CAAA,CAAA,CAAA,CAAA;QACI,IAAJ,CAAA,SAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;QACA,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,UAAA,KAAA,IAAA,OAAA,KAAA,CAAA,SAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QAEA,IAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,UAAA,KAAA,IAAA,OAAA,KAA8E,CAA9E,SAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACI,IAAI,CAAR,mBAA4B,CAA5B,OAAA,CAAA,UAAA,KAAA,IAAA,OAAA,KAAA,CAAA,SAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,IAAM,CAAN,cAAqB,CACX,OADV,CAAA,UAAA,KAAA,IAAA,OAAA,KAAA,CAAA,SAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QAEA,MAAA,CAAA,iBAAA,CAAA;IACA,CAAA;IACA,wCAAA,GAAA,UAAA,UAAA,EAAA,iBAAA;QAEM,EAAN,CAAA,CAAA,IAAA,CAAA,aAAA,CAAuC,CAAvC,CAAA;YACQ,MAAR,IAAA,KAAA,CAAwB,YAAxB,iBAAA,0DAAA;iBACA,kDAAA,UAAA,OAAA,CAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;;;;QAGI,EAAJ,CAAA,CAAA,KAAA,KAAA,OAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QACA,CAAA;QAEA,oEAAA;QACI,0EAAJ;QACI,IAAM,MAAM,GAAG,IAAnB,CAAA,UAAA,CAAmC,QAAQ,CAAC,GAAG,CAAC,KAAhD,EAAA,SAAA,CAAA,CAAA;QACI,MAAJ,CAAW,MAAX,KAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,KAAA,EAAA,aAAA,CAAA,GAAA,MAAA,CAAA;IACA,CAAG;IAED,yBAAF,GAAE,UAAF,MAAgB,EAAhB,EAAA,EAAA,OAAA;QAAE,iBAKF;QAJI,IAAI,CAAC,aAAT,EAAA,CAAA;QACI,IAAJ,MAAA,GAAA,MAAyB,CAAC,GAA1B,CAA8B,UAAA,CAAC,IAA/B,OAAA,KAAA,CAAwC,GAAxC,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,EAAA,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;IAEE,CAAF;IACA,gCAAA,GAAA,UAAA,QAAA,EAAA,QAAA;QACI,IAAI,CAAC,sBAAT,CAAA,gBAAA,EAAA,0BAAA,CAAA,CAAA;QACA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IAEE,CAAF;IACA,mCAAA,GAAA,UAAA,SAAA,EAAA,QAAA;QACI,IAAI,CAAC,sBAAT,CAAA,mBAAA,EAAA,6BAAA,CAAA,CAAA;QACA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IAEE,CAAF;IACA,mCAAA,GAAA,UAAA,SAAA,EAAA,QAAA;QACI,IAAI,CAAC,sBAAT,CAAA,mBAAA,EAAA,6BAAA,CAAA,CAAA;QACA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IAUE,CAAF;IAKA,8BAAA,GAAA,UAAa,IAAe,EAA5B,QAAA;QACI,IAAI,CAAR,sBAAA,CAAA,cAAA,EAAA,wBAAA,CAAA,CAAA;QACI,IAAI,CAAR,cAAA,CAAA,IAAA,CAA6B,CAA7B,IAAA,EAAA,QAAA,CAAA,CAAA,CAAA;IACA,CAAA;IACA,kCAAc,GAAd,UAAA,KAAA,EAAsB,QAAtB;QACA,IAAA,KAAA,GAAA,CAAA,CAAA;QAAA,IAAA,KAAA,CAAA;QACA,EAAA,CAAA,CAAM,QAAN,CAAA,UAAA,CAAA,CAAA,CAAA;YACM,KAAK,IAAX,IAAA,CAAA,yBAAA,CAAA;YACA,KAAA,GAAA,QAAA,CAAA,UAAA,CAAA;QACI,CAAJ;QACA,IAAM,CAAN,CAAA;YACM,KAAN,IAAA,GAAuB,CAAC,uBAAxB,CAAA;YACM,KAAN,GAAA,QAAA,CAAuB,QAAvB,CAAA;QACA,CAAA;QACA,IAAA,IAAA,GAAA,CAAA,QAAA,CAAA,IAAA,IAAA,EAA+B,CAA/B,CAAA,GAAA,CAAA,UAAA,GAAyC;YACzC,IAAA,QAAY,GAAZ,CAAA,CAAA;YACA,IAAA,QAAA,CAAA;YAAA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAqB,CAArB,GAAA,CAA0B,CAA1B,CAAA,CAAA;gBACA,GAAA,CAAA,OAAA,CAAA,UAAA,KAAA;oBACA,EAAA,CAAA,CAAA,KAAA,YAAA,QAAA,CAAA,CAAA,CAAA;wBAAA,QAAA,IAAA,CAAA,CAAA,cAAA,CAAA;oBACA,CAAA;oBACA,IAAA,CAAA,EAAA,CAAA,CAAA,KAAA,YAAA,QAAA,CAAA,CAAA,CAAA;wBACA,QAAA,IAAA,CAAA,CAAA,cAAA,CAAA;oBACA,CAAA;oBAAA,IAAA,CAAA,CAAA;wBACgB,QAAhB,GAAA,KAAA,CAAA;oBACA,CAAA;gBACA,CAAA,CAAA,CAAa;YACb,CAAA;YACAD,IAAAA,CAAAA,CAAAA;gBACA,QAAA,GAAA,GAAA,CAAA;YAEA,CAAA;YACQ,MAAR,CAAA,CAAA,QAAwB,EAAxB,QAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA;QAEI,iBAAJ,CAAA,EAAA,KAAA,OAAA,EAAA,KAAA,OAAA,EAAA,IAAA,MAAA,EAAA,KAAA,OAAA,EAAA,CAAA,CAAA;IACA,CAAA;IAEA,iCAAA,GAAA,UAAA,SAAA;QAAA;QAEI,IAAJ,CAAA,aAAA,EAAA,CAAyB;QACrB,IAAM,gBAAV,GAAA,IAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,CAAA;QACI,EAAJ,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAmC,CAAnC;YACA,MAAA,IAAA,KAAA,CAAA,iCAAA,UAAwF,CAAC,SAAzF,CAAA,qDAAA,CAAA,CAAA;QACI,CAAJ;QACI,IAAJ,QAAA,GAAA,IAAyB,CAAC,GAA1B,CAAA,wBAAsD,EAAtD,KAAA,CAAA,CAAA;QAEI,IAAM,UAAV,GAAuB,IAAvB,CAAA,GAAA,CAAA,0BAAA,EAAA,KAAA,CAAA,CAAA;QACA,IAAA,MAAA,GAAA,QAAA,GAAA,IAAA,GAAA,IAAA,CAAA,GAC0B,CAAC,MAAM,EADjC,IAAA,CAAA,CAAA;QAEA,IAAA,qBAAA,GAAA,IAAA,CAAA,GAAA,CAAA,qBAAA,CAAA,CAAA;QACA,IAAA,QAAA,GAAA,SAAA,kBAAA,EAAA,CAAA;QAEI,qBAAJ,CAAA,iBAA2C,CAA3C,QAAA,CAAA,CAAsD;QAClD,IAAJ,aAAA,GAAA;YACA,IAAA,YAAA,GAAA,gBAAA,CAAA,MAAA,CAAA,QAAA,CAAA,IAAA,EAAA,EAAA,EAAA,MAAA,QAAA,EAAA,KAAA,CAAA,UAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,gBAAA,CAAA,YAAA,EAAA,MAAA,EAAA,UAAA,CAAA,CAAA;QACA,CAAA,CAAA;QAEA,IAAA,OAAA,GAAA,CAAA,MAAA,GAAA,aAAA,EAAA,GAAA,MAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA;;;;IAKA,cAAA;AAAA,CAAA,AAzSA,IAySA;AACA,IAAE,QAAF,GAAA,IANS,CAMT;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BA;;;IAGA,EAAA,CAAA,CAAA,MAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IANuC,CAAE,CAMzC,CAAA,CAAA;QACA,wEAAA;QACA,MAAA,CAAA;YAAA,iBAOA;YANA,qFAAA;YACA,uBAAA;YACA,MAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,CAAA,IAAA,CAAA;gBACA,IAAA,SAAA,GAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,CAAA,CAAA;gBANA,OAAA,CAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,CAAA,CAAA;;YAQA,CAAA,CAAA,CANW;QAOX,CAAA,CAAA;IACA,CAAA;;;;IAKA,CAAA;AACA,CAAA;AAAA;;GAEA;AACA;IACA,4BAAA,UAAA;QACA,IAAM,CAAN,UAAA,GAAA,UAAA,CAAA;IACA,CAAA;IACA,uCAAA,GAAA;QAEA,IAAA,SAAA,GAAA,IAAA,CAAA,UAAA,EAAA,CAAA;QACI,EAAJ,CAAA,CAAA,SAAiB,CAAjB,CAAA,CAAA;;QAEI,CAAJ;IACA,CAAA;IACA,mCAAA,GAAA,UAAA,MAAA,EAAa,EAAb;QACA,IAAA,IAAA,GAAA,IAAA,CAAA;QACA,wEAAA;QACA,MAAA,CAAA;YAOA,IAAA,CAAA,UAAA,EANsC,CAMtC;YAJU,MAMV,CAAA,MAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;IAEA,CAAA;IACA,yBAAA;AAAA,CAAA,AA1BA,IA0BA;AACA,oBAAA,SAAA,EAAA,EAAA;IACA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,wEAAA;QACA,MAAA,CAAA;YACA,IAAA,OAAA,GAAA,UAAA,EAAA,CAAA;YACA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;gBACA,OAAA,CAAA,sBAAA,CAAA,SAAA,CAAA,CAAA;YACA,CAAA;YAEA,MAAA,CAAA,EAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;QACA,CAAA,CAAA;IACA,CAAA;;ADljBA,CAAA;;;;;;;;;;;AAcA;;;;GAUA;AACA,IAAA,SAAA,GAAA,CAAA,OAAsB,MAAtB,KAAA,WAAA,GAAA,MAAA,GAAA,MAAA,CAAA,CAAA;AACA,qEAAA;AACA,EAAA,CAAA,CAAA,SAAA,CAAA,UAAA,CAAA,CAAA,CAAA;;;QAIA,kBAAA,EAAA,CAAA;;AD/BA,CAAA;;;;;;;;;;;;;;;;;GDYG;;;;;;;;;;;;GDAA;;;;;;;"}