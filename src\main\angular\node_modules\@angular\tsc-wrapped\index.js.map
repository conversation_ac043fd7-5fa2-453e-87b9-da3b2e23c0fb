{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../tools/@angular/tsc-wrapped/index.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;AAEH,qDAAuD;AAA/C,6CAAA,kBAAkB,CAAA;AAC1B,mCAA6D;AAAnC,2BAAA,SAAS,CAAA;AAAE,sBAAA,IAAI,CAAA;AAGzC,mCAA8B;AAC9B,uCAAkC;AAClC,qCAAgC;AAChC,wCAAmC;AACnC,kCAA6B", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {MetadataWriterHost} from './src/compiler_host';\nexport {CodegenExtension, UserError, main} from './src/main';\n\nexport {default as AngularCompilerOptions} from './src/options';\nexport * from './src/bundler';\nexport * from './src/cli_options';\nexport * from './src/collector';\nexport * from './src/index_writer';\nexport * from './src/schema';\n"]}