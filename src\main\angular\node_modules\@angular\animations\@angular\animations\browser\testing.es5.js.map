{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../../packages/animations/browser/testing/index.ts", "../../../../../../packages/animations/browser/testing/src/mock_animation_driver.ts", "../../../../../../packages/animations/browser/testing/src/testing.ts", "../../../../../../packages/animations/browser/src/render/shared.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/animations/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationPlayer, NoopAnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../../src/render/animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../../src/render/shared';\n\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): MockAnimationPlayer {\n    const player =\n        new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: {[key: string]: string | number} = {};\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ɵStyleData = {};\n\n  constructor(\n      public element: any, public keyframes: {[key: string]: string | number}[],\n      public duration: number, public delay: number, public easing: string,\n      public previousPlayers: any[]) {\n    super();\n    previousPlayers.forEach(player => {\n      if (player instanceof MockAnimationPlayer) {\n        const styles = player.currentSnapshot;\n        Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n      }\n    });\n\n    this.totalTime = delay + duration;\n  }\n\n  /* @internal */\n  onInit(fn: () => any) { this._onInitFns.push(fn); }\n\n  /* @internal */\n  init() {\n    super.init();\n    this._onInitFns.forEach(fn => fn());\n    this._onInitFns = [];\n  }\n\n  finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /* @internal */\n  triggerMicrotask() {}\n\n  play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  hasStarted() { return this.__started; }\n\n  beforeDestroy() {\n    const captures: ɵStyleData = {};\n\n    Object.keys(this.previousStyles).forEach(prop => {\n      captures[prop] = this.previousStyles[prop];\n    });\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this.keyframes.forEach(kf => {\n        Object.keys(kf).forEach(prop => {\n          if (prop != 'offset') {\n            captures[prop] = this.__finished ? kf[prop] : AUTO_STYLE;\n          }\n        });\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {MockAnimationDriver, MockAnimationPlayer} from './mock_animation_driver';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n"], "names": [], "mappings": ";;;;;;;;;;;;;GG4FA;AAsCA,IAAI,SAAJ,GAAA,UAAA,IAAA,EAAA,IAAA,IAAA,OAAA,KAAA,EAAA,CAAA,CAAA;;AAEA,IAAE,MAAF,GAAW,UAAX,OAAwB,EAAE,QAA1B,EAAA,KAAA;IAEE,MAAF,CAAA,EAAA,CAAA;AACA,CAAA,CAAA;AACA,EAAA,CAAA,CAAA,OAAA,OAAA,IAAA,WAAA,CAAA,CAAA,CAAA;IAAA,yCAAA;IACA,SAAA,GAAA,UAAA,IAAA,EAAA,IAAA,IAAA,MAA2C,CAA3C,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAI,OAAJ,CAAY,SAAS,CAArB,OAAA,CAAA,CAAA,CAAA;QACA,QAAA,GAAA,UAAA,OAAA,EAAA,QAAA,IAAA,OAAA,OAAA,CAAA,OAAA,CAAA,QAA8D,CAA9D,EAAA,CAAA,CAAA;IACA,CAAA;IACA,IAAA,CAAA,CAAA;QACA,IAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA;QACA,IAAA,IAAA,GAAA,KAAA,CAAA,eAAA,IAAA,KAAA,CAAA,kBAAA,IAAA,KAAA,CAAA,iBAAA;YAEA,KAAA,CAAA,gBAAA,IAA0D,KAA1D,CAAA,qBAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,IAAR,CAAA,CAAA,CAAA;YACQ,QAAR,GAAA,UAAA,OAAA,EAAA,QAAA,IAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;IAAA,MAAA,GAAW,UAAX,OAAA,EAAA,QAAA,EAAA,KAAA;QACA,IAAM,OAAN,GAAA,EAAkB,CAAlB;QACA,EAAA,CAAA,CAAM,KAAN,CAAA,CAAa,CAAb;YACA,OAAA,CAAA,IAAgB,OAAhB,OAAA,EAAoB,OAApB,CAAA,gBAAA,CAAA,QAAA,CAAA,EAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YACA,IAAA,GAAA,GAAA,OAAA,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBACA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;YAEA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;;AFjKA,CAAA;;;;;;;;;;GAmBA;AACA;;GAGA;AAEA;IAAA;IAqBA,CAAA;IApBA,4CAAA,GAAA,UAAA,OAAsB,EAAtB,QAAgC;QAChC,MAAA,CAAA,cAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;IAEE,CAAF;IACA,6CAAA,GAAA,UAAA,IAAA,EAAA,IAAA,IAA8B,MAA9B,CAAA,eAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA,mCAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,KAAA;QAEA,MAAA,CAAA,WAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;IAGA,CAAA;IAEA,0CAAA,GAAA,UAAA,OAAA,EAAA,IAAA,EAA4B,YAA6B;QACrD,MAAJ,CAAW,YAAX,IAAA,EAAA,CAAA;IACA,CAAG;;;QAvBH,IAAA,MAAA,GAAY,IAAZ,mBAAA,CAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,eAAA,CAAA,CAAA;;;;IA6BA,0BAAA;AAAA,CAAA,AArBA,IAqBA;AAOA,mBAAA,CAAA,GACyB,GADzB,EAAA,CAAA;AAIA;;GAHA;AACA;IAAA,+CAAA;IAAA,6BAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,eAAA;QAAA,YAA4D,iBAA5D,SAkBA;QAjBa,KAAb,CAAA,OAAA,GAAA,OAAA,CAAa;QATH,KAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QACU,KAAV,CAAA,QAAA,GAAA,QAAA,CAAA;QACS,KAAT,CAAA,KAAA,GAAA,KAAA,CAAuB;QACb,KAAV,CAAA,MAAA,GAAA,MAAwC,CAAC;QAChC,KAAT,CAAA,eAAwB,GAAe,eAAvC,CAAA;QAOI,KAAJ,CAAA,UAAmB,GAAnB,KAA2B,CAAC;QAC5B,KAAM,CAAN,SAAgB,GAAhB,KAAA,CAAA;QACA,KAAA,CAAA,cAAA,GAAA,EAAA,CAAA;QACA,KAAA,CAAA,UAAe,GAAf,EAAoB,CAApB;QACA,KAAA,CAAO,eAAP,GAAA,EAAA,CAAA;QACA,eAAA,CAAA,OAAA,CAAA,UAAA,MAAA;YAEQ,EAAR,CAAA,CAAA,MAAkB,YAAlB,mBAAA,CAAA,CAAA,CAAA;gBACA,IAAA,QAAA,GAAA,MAAA,CAAA,eAAA,CAAA;;YAGA,CAAsB;;QAGhB,KAAN,CAAA,SAAA,GAAA,KAAA,GAAA,QAAA,CAAA;;IACA,CAAA;IACA,eAAA;IACA,oCAAA,GAAA,UAAA,EAAS,IAAT,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACA,eAAA;IAEE,kCAAF,GAAE;QACE,iBAAM,IAAV,WAAgB,CAAhB;QACI,IAAI,CAAC,UAAU,CAAnB,OAA2B,CAA3B,UAAA,EAAA,IAAA,OAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;QACA,IAAA,CAAA,UAAA,GAAA,EAAA,CAAA;IAEE,CAAF;IACA,oCAAA,GAAA;QACI,iBAAJ,MAAA,WAAA,CAAmB;QACnB,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA;;IAGE,qCAAF,GAAE;QAEI,iBAAN,OAAA,WAAA,CAAA;QACI,IAAJ,CAAS,UAAT,GAAA,IAAA,CAAA;IACA,CAAA;IACA,eAAA;IAEE,8CAAF,GAAE,cAAF,CAAwB;IAEtB,kCAAF,GAAE;QACE,iBAAM,IAAV,WAAA,CAAA;QAEI,IAAJ,CAAA,SAAA,GAAqB,IAArB,CAAA;IACA,CAAA;IACA,wCAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA;IAEA,2CAAa,GAAb;QAAA;;;;QAIA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAQ,CAAR,UAAmB,EAAnB,CAAsB,CAAC,CAAC;YACxB,2DAAA;YACA,uDAAuD;YACvD,uDAAA;YACA,IAAA,CAAS,SAAT,CAAA,OAAA,CAAA,UAAA,EAAA;gBACA,MAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;oBACA,EAAA,CAAA,CAAA,IAAA,IAAA,QAAA,CAAA,CAAA,CAAA;wBAEA,QAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,UAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,UAAA,CAAA;oBACA,CAAA;gBACA,CAAA,CAAA,CAAA;;QCxHA,CAAA;;;;CDuDA,CAAA,mBAAA;;;;;;;;;;;;;;GD3CG;;;;;;;"}