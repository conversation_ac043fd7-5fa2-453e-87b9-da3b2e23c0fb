/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core"),require("@angular/router"),require("@angular/upgrade/static")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/router","@angular/upgrade/static"],factory):factory((global.ng=global.ng||{},global.ng.router=global.ng.router||{},global.ng.router.upgrade=global.ng.router.upgrade||{}),global.ng.core,global.ng.router,global.ng.upgrade.static)}(this,function(exports,_angular_core,_angular_router,_angular_upgrade_static){"use strict";function locationSyncBootstrapListener(ngUpgrade){return function(){setUpLocationSync(ngUpgrade)}}function setUpLocationSync(ngUpgrade){if(!ngUpgrade.$injector)throw new Error("\n        RouterUpgradeInitializer can be used only after UpgradeModule.bootstrap has been called.\n        Remove RouterUpgradeInitializer and call setUpLocationSync after UpgradeModule.bootstrap.\n      ");var router=ngUpgrade.injector.get(_angular_router.Router),url=document.createElement("a");ngUpgrade.$injector.get("$rootScope").$on("$locationChangeStart",function(_,next,__){url.href=next,router.navigateByUrl(url.pathname+url.search)})}/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var RouterUpgradeInitializer={provide:_angular_core.APP_BOOTSTRAP_LISTENER,multi:!0,useFactory:locationSyncBootstrapListener,deps:[_angular_upgrade_static.UpgradeModule]};exports.RouterUpgradeInitializer=RouterUpgradeInitializer,exports.locationSyncBootstrapListener=locationSyncBootstrapListener,exports.setUpLocationSync=setUpLocationSync,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=router-upgrade.umd.min.js.map
