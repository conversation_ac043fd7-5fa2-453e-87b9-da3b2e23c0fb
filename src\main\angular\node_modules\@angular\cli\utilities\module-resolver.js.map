{"version": 3, "file": "module-resolver.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["utilities/module-resolver.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;AAEb,6BAA6B;AAE7B,6DAA6D;AAG7D,qCAA+C;AAC/C,gDAAgD;AAEhD;;;GAGG;AACH;IAEE,YAAmB,WAAmB,EAAS,WAAmB,EAAS,QAAgB;QAAxE,gBAAW,GAAX,WAAW,CAAQ;QAAS,gBAAW,GAAX,WAAW,CAAQ;QAAS,aAAQ,GAAR,QAAQ,CAAQ;IAAG,CAAC;IAE/F;;;;;;;;;OASG;IACH,wBAAwB,CAAC,OAAiB,EAAE,OAAa,oBAAQ;QAC/D,MAAM,CAAC,OAAO;aACX,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU,KAAK,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;aAC3E,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IAChG,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,YAA8C;QAC7D,IAAI,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QAC/D,IAAI,gBAAgB,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;QACvE,IAAI,eAAe,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC;QACvE,MAAM,CAAC,WAAW,IAAI,gBAAgB,IAAI,eAAe,CAAC;IAC5D,CAAC;IAED;;;;;;;OAOG;IACH,qBAAqB;QACnB,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC;aAC1E,IAAI,CAAC,CAAC,KAAqC;YAC1C,IAAI,OAAO,GAAa,EAAE,CAAC;YAC3B,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC1D,0EAA0E;YAC1E,IAAI,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI;gBACjD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;oBACzD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,YAAY,CAAC;gBAC7E,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,OAAO,CAAC,IAAI;gBACxB,IAAI,WAAW,GAAoB,KAAK,CAAC,IAAI,CAAC;qBAC3C,GAAG,CAAC,SAAS;oBACZ,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;oBAC3D,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACjC,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;oBACpF,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;wBAC1D,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,UAAU,EAAE,CAAC;oBAC3C,CAAC;oBACD,IAAI,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;oBAC9D,MAAM,CAAC,IAAI,sBAAa,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,SAAS,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;gBACpF,CAAC,CAAC,CAAC;gBACL,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;IACR,CAAC;IAEA;;;;OAIG;IACH,iBAAiB;QACf,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;aAC5D,IAAI,CAAC,CAAC,MAAqB,KAAK,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;aAC7E,IAAI,CAAC,gBAAgB;YACpB,IAAI,OAAO,GAAa,gBAAgB;iBACvC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;iBAC3D,GAAG,CAAC,SAAS;gBACZ,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;gBAC5C,IAAI,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,aAAa,CAAC,CAAC;gBACrF,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;gBACrE,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;oBAC1D,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,UAAU,EAAE,CAAC;gBAC3C,CAAC;gBACD,IAAI,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC9D,MAAM,CAAC,IAAI,sBAAa,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,GAAG,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AA/FD,wCA+FC"}