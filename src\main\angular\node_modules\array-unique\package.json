{"_args": [["array-unique@0.2.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "array-unique@0.2.1", "_id": "array-unique@0.2.1", "_inBundle": false, "_integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=", "_location": "/array-unique", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-unique@0.2.1", "name": "array-unique", "escapedName": "array-unique", "rawSpec": "0.2.1", "saveSpec": null, "fetchSpec": "0.2.1"}, "_requiredBy": ["/expand-braces", "/micromatch"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-unique/-/array-unique-0.2.1.tgz", "_spec": "0.2.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/array-unique/issues"}, "description": "Return an array free of duplicate values. Fastest ES5 implementation.", "devDependencies": {"array-uniq": "^1.0.2", "benchmarked": "^0.1.3", "mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/array-unique", "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/array-unique/blob/master/LICENSE"}, "main": "index.js", "name": "array-unique", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/array-unique.git"}, "scripts": {"test": "mocha"}, "version": "0.2.1"}