{"_args": [["clone-deep@0.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "clone-deep@0.3.0", "_id": "clone-deep@0.3.0", "_inBundle": false, "_integrity": "sha1-NIxhrpzb4O3+BT2R/0zFIdeQ7eg=", "_location": "/clone-deep", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clone-deep@0.3.0", "name": "clone-deep", "escapedName": "clone-deep", "rawSpec": "0.3.0", "saveSpec": null, "fetchSpec": "0.3.0"}, "_requiredBy": ["/sass-loader"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/clone-deep/-/clone-deep-0.3.0.tgz", "_spec": "0.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.1", "kind-of": "^3.2.2", "shallow-clone": "^0.1.2"}, "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/clone-deep", "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "license": "MIT", "main": "index.js", "name": "clone-deep", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "0.3.0"}