{"title": "Intrinsic & Extrinsic Sizing", "description": "Allows for the heights and widths to be specified in intrinsic values using the `max-content`, `min-content`, `fit-content` and `stretch` (formerly `fill`) properties.", "spec": "http://www.w3.org/TR/css3-sizing/", "status": "wd", "links": [{"url": "http://demosthenes.info/blog/662/Design-From-the-Inside-Out-With-CSS-<PERSON><PERSON>ontent", "title": "Min-Content tutorial"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "a x #1 #2 #3", "3.5": "a x #1 #2 #3", "3.6": "a x #1 #2 #3", "4": "a x #1 #2 #3", "5": "a x #1 #2 #3", "6": "a x #1 #2 #3", "7": "a x #1 #2 #3", "8": "a x #1 #2 #3", "9": "a x #1 #2 #3", "10": "a x #1 #2 #3", "11": "a x #1 #2 #3", "12": "a x #1 #2 #3", "13": "a x #1 #2 #3", "14": "a x #1 #2 #3", "15": "a x #1 #2 #3", "16": "a x #1 #2 #3", "17": "a x #1 #2 #3", "18": "a x #1 #2 #3", "19": "a x #1 #2 #3", "20": "a x #1 #2 #3", "21": "a x #1 #2 #3", "22": "a x #1 #2 #3", "23": "a x #1 #2 #3", "24": "a x #1 #2 #3", "25": "a x #1 #2 #3", "26": "a x #1 #2 #3", "27": "a x #1 #2 #3", "28": "a x #1 #2 #3", "29": "a x #1 #2 #3", "30": "a x #1 #2 #3", "31": "a x #1 #2 #3", "32": "a x #1 #2 #3", "33": "a x #1 #2 #3", "34": "a x #1 #2 #3", "35": "a x #1 #2 #3", "36": "a x #1 #2 #3", "37": "a x #1 #2 #3", "38": "a x #1 #2 #3", "39": "a x #1 #2 #3", "40": "a x #1 #2 #3", "41": "a x #1 #2 #3", "42": "a x #1 #2 #3", "43": "a x #1 #2 #3", "44": "a x #1 #2 #3", "45": "a x #1 #2 #3", "46": "a x #1 #2 #3", "47": "a x #1 #2 #3", "48": "a x #1 #2 #3", "49": "a x #1 #2 #3", "50": "a x #1 #2 #3", "51": "a x #1 #2 #3", "52": "a x #1 #2 #3", "53": "a x #1 #2 #3", "54": "a x #1 #2 #3", "55": "a x #1 #2 #3", "56": "a x #1 #2 #3", "57": "a x #1 #2 #3"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "y x #3", "23": "y x #3", "24": "y x #3", "25": "y x #3", "26": "y x #3", "27": "y x #3", "28": "y x #3", "29": "y x #3", "30": "y x #3", "31": "y x #3", "32": "y x #3", "33": "y x #3", "34": "y x #3", "35": "y x #3", "36": "y x #3", "37": "y x #3", "38": "y x #3", "39": "y x #3", "40": "y x #3", "41": "y x #3", "42": "y x #3", "43": "y x #3", "44": "y x #3", "45": "y x #3", "46": "y #3 #4", "47": "y #3 #4", "48": "y #3 #4", "49": "y #3 #4", "50": "y #3 #4", "51": "y #3 #4", "52": "y #3 #4", "53": "y #3 #4", "54": "y #3 #4", "55": "y #3 #4", "56": "y #3 #4", "57": "y #3 #4", "58": "y #3 #4", "59": "y #3 #4", "60": "y #3 #4", "61": "y #3 #4", "62": "y #3 #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "a x #1 #3", "7": "a x #1 #3", "7.1": "a x #1 #3", "8": "a x #1 #3", "9": "a x #3", "9.1": "a x #3", "10": "a x #3", "10.1": "a x #3", "11": "a #3", "TP": "a #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x #3", "16": "y x #3", "17": "y x #3", "18": "y x #3", "19": "y x #3", "20": "y x #3", "21": "y x #3", "22": "y x #3", "23": "y x #3", "24": "y x #3", "25": "y x #3", "26": "y x #3", "27": "y x #3", "28": "y x #3", "29": "y x #3", "30": "y x #3", "31": "y x #3", "32": "y x #3", "33": "y #3 #4", "34": "y #3", "35": "y #3 #4", "36": "y #3 #4", "37": "y #3 #4", "38": "y #3 #4", "39": "y #3 #4", "40": "y #3 #4", "41": "y #3 #4", "42": "y #3 #4", "43": "y #3 #4", "44": "y #3 #4", "45": "y #3 #4", "46": "y #3 #4", "47": "y #3 #4", "48": "y #3 #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "a x #1 #3", "8": "a x #1 #3", "8.1-8.4": "a x #1 #3", "9.0-9.2": "a x #3", "9.3": "a x #3", "10.0-10.2": "a x #3", "10.3": "a x #3", "11": "a x #3"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y x #3", "4.4.3-4.4.4": "y x #3", "56": "y #3 #4"}, "bb": {"7": "n", "10": "y x #3"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y #3 #4"}, "and_chr": {"59": "y #3 #4"}, "and_ff": {"54": "a x #1 #2 #3"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y x #3"}, "samsung": {"4": "y x #3", "5": "y #3 #4"}, "and_qq": {"1.2": "y x #3"}, "baidu": {"7.12": "y #3 #4"}}, "notes": "Prefixes are on the values, not the property names (e.g. -webkit-min-content)\r\n\r\nOlder webkit browsers also support the unofficial `intrinsic` value which acts the same as `max-content`.", "notes_by_num": {"1": "Does not support for height/min-height/max-height property, only width. [see test case](http://codepen.io/shshaw/pen/Kiwaz) [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=567039)", "2": "Firefox currently supports the \"-moz-available\" property rather than \"-moz-stretch\".", "3": "Does not support for \"flex-basis\" property. [see specs](http://www.w3.org/TR/2015/WD-css-flexbox-1-20150514/#flex-basis-property).\r\n[Blink bug](https://codereview.chromium.org/1304853002/),[Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1055887)", "4": "This does not yet unprefix `stretch` (aka fill/fill-available)[See bug](https://chromium.googlesource.com/chromium/blink.git/+/bf119cdfece210e69c9a99af06f1b9981e2a1bc2), because the [CSSWG](https://lists.w3.org/Archives/Public/www-style/2015Aug/0127.html) is not ready for that yet."}, "usage_perc_y": 70.26, "usage_perc_a": 18.22, "ucprefix": false, "parent": "", "keywords": "fill,fill-available,max-content,min-content,fit-content,contain-floats,intrinsic,extrinsic,sizing", "ie_id": "cssintrinsicsizing", "chrome_id": "5901353784180736", "firefox_id": "", "webkit_id": "", "shown": true}