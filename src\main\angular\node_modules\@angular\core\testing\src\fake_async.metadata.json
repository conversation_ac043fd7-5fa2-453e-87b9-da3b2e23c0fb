[{"__symbolic": "module", "version": 3, "metadata": {"resetFakeAsyncZone": {"__symbolic": "function"}, "fakeAsync": {"__symbolic": "function", "parameters": ["fn"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 51, "character": 9}}, "tick": {"__symbolic": "function"}, "flush": {"__symbolic": "function", "parameters": ["maxTurns"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 94, "character": 9, "context": {"name": "_getFakeAsyncZoneSpec"}}}, "discardPeriodicTasks": {"__symbolic": "function"}, "flushMicrotasks": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"resetFakeAsyncZone": {"__symbolic": "function"}, "fakeAsync": {"__symbolic": "function", "parameters": ["fn"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 51, "character": 9}}, "tick": {"__symbolic": "function"}, "flush": {"__symbolic": "function", "parameters": ["maxTurns"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 94, "character": 9, "context": {"name": "_getFakeAsyncZoneSpec"}}}, "discardPeriodicTasks": {"__symbolic": "function"}, "flushMicrotasks": {"__symbolic": "function"}}}]