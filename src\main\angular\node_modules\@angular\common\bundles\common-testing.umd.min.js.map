{"version": 3, "file": "common-testing.umd.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/common/testing/src/location_mock.ts", "../../../../packages/common/testing/src/mock_location_strategy.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @experimental\n */\n\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '')];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _baseHref: string = '';\n  /** @internal */\n  _platformStrategy: LocationStrategy = null !;\n\n  setInitialPath(url: string) { this._history[this._historyIndex].path = url; }\n\n  setBaseHref(url: string) { this._baseHref = url; }\n\n  path(): string { return this._history[this._historyIndex].path; }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath =\n        this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n  }\n\n  simulateUrlPop(pathname: string) { this._subject.emit({'url': pathname, 'pop': true}); }\n\n  simulateHashChange(pathname: string) {\n    // Because we don't prevent the native event, the browser will independently update the path\n    this.setInitialPath(pathname);\n    this.urlChanges.push('hash: ' + pathname);\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._baseHref + url;\n  }\n\n  go(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query));\n    this._historyIndex = this._history.length - 1;\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push(url);\n    this._subject.emit({'url': url, 'pop': false});\n  }\n\n  replaceState(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push('replace: ' + url);\n  }\n\n  forward() {\n    if (this._historyIndex < (this._history.length - 1)) {\n      this._historyIndex++;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  subscribe(\n      onNext: (value: any) => void, onThrow?: ((error: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  normalize(url: string): string { return null !; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass LocationState {\n  path: string;\n  query: string;\n  constructor(path: string, query: string) {\n    this.path = path;\n    this.query = query;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @stable\n */\n\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  constructor() { super(); }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n\n  path(includeHash: boolean = false): string { return this.internalPath; }\n\n  prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  pushState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  replaceState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  onPopState(fn: (value: any) => void): void { this._subject.subscribe({next: fn}); }\n\n  getBaseHref(): string { return this.internalBaseHref; }\n\n  back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  forward(): void { throw 'not implemented'; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["exports", "module", "factory", "require", "extendStatics", "Object", "setPrototypeOf", "__proto__", "Array", "d", "b", "p", "hasOwnProperty", "SpyLocation", "this", "_history", "LocationState", "_subject", "_angular_core", "EventEmitter", "_baseHref", "_platformStrategy", "prototype", "setInitialPath", "url", "_historyIndex", "path", "isCurrentPathEqualTo", "query", "<PERSON><PERSON><PERSON>", "endsWith", "substring", "length", "currPath", "simulateUrlPop", "pathname", "emit", "pop", "simulateHashChange", "url<PERSON><PERSON><PERSON>", "push", "type", "prepareExternalUrl", "startsWith", "go", "splice", "locationState", "replaceState", "history", "forward", "subscribe", "onNext", "onThrow", "onReturn", "next", "error", "complete", "normalize", "decorators", "MockLocationStrategy", "_super", "_this", "internalBaseHref", "internalTitle", "__extends", "simulatePopState", "internalPath", "_MockPopStateEvent", "includeHash", "internal", "pushState", "ctx", "title", "externalUrl", "getBaseHref", "back", "nextUrl", "_angular_common", "LocationStrategy", "Injectable", "newUrl"], "mappings": ";;;;;0BAAA,gBAAAA,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,iBAAAA,QAAA,8eAqBA,GAAIC,eAAJC,OAAAC,iBACAC,uBAAAC,QAA2C,SAA3CC,EAAAC,GAAAD,EAAAF,UAAAG,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,4BCiGA,QAAAE,kCAzFAC,KAAAC,UAAA,GAAAC,eAAA,GAAA,0BAKAF,KAAAG,SAAA,GAAAC,eAAAC,aAIAL,KAAAM,UAAA,GAGAN,KAAAO,kBAAA,WAEAR,aAAAS,UAAAC,eAAA,SAAAC,KAAAV,KAAAC,SAAAD,KAAAW,eAAAC,KAAAF,yEAEAX,YAAAS,UAAAI,KAAA,WAAA,MAAAZ,MAAAC,SAAAD,KAAAW,eAAAC,MACAb,YAAAS,UAAAK,qBAAA,SAAAD,KAAAE,OACA,SAAAA,QAAAA,MAAA,GACA,IAAAC,WAAAH,KAAAI,SAAA,KAAAJ,KAAAK,UAAA,EAAAL,KAAAM,OAAA,GAAAN,iGAGI,OAAJO,WAAAJ,WAAAD,MAAAI,OAAA,EAAA,IAAAJ,MAAA,KAEAf,YAAAS,UAAAY,eAAA,SAAAC,UAAArB,KAAAG,SAAAmB,MAAAZ,IAAAW,SAAAE,KAAA,KACAxB,YAAAS,UAAAgB,mBAAA,SAAAH,UAGArB,KAAAS,eAAAY,UACArB,KAAAyB,WAAAC,KAAA,SAAAL,UAEArB,KAAAG,SAAAmB,MAAAZ,IAAAW,SAAAE,KAAA,EAAAI,KAAA,gBAEA5B,YAAAS,UAAAoB,mBAAA,SAAAlB,KAII,MAHJA,KAAAQ,OAAA,IAAAR,IAAAmB,WAAA,OAAAnB,IAAA,IAAAA,KAGAV,KAAAM,UAAAI,KAEAX,YAAAS,UAAAsB,GAAA,SAAAlB,KAAAE,OACA,SAAAA,QAAAA,MAAA,IAEIF,KAAJZ,KAAA4B,mBAAAhB,MACAZ,KAAAW,cAAA,GACQX,KAARC,SAAsB8B,OAAtB/B,KAAAW,cAAA,GAGAX,KAAAC,SAAAyB,KAAA,GAAAxB,eAAAU,KAAAE,QACId,KAAJW,cAAAX,KAAAC,SAAAiB,OAAA,CAEI,IAAJc,eAAwBhC,KAAxBC,SAAAD,KAAAW,cAAA,EACI,IAAIqB,cAARpB,MAAAA,MAAAoB,cAAAlB,OAAAA,MAAI,CAEJ,GAAAJ,KAAAE,MAAAE,MAAAI,OAAA,EAAA,IAAAJ,MAAA,GAEId,MAAJyB,WAAAC,KAAAhB,KACIV,KAAJG,SAAoBmB,MAApBZ,IAAAA,IAAAa,KAAA,MAGAxB,YAAAS,UAAAyB,aAAA,SAAArB,KAAAE,OACA,SAAAA,QAAAA,MAAA,IAEAF,KAAAZ,KAAA4B,mBAAAhB,KACI,IAAJsB,SAAAlC,KAAAC,SAAAD,KAAAW,cACA,IAAAuB,QAAAtB,MAAAA,MAAAsB,QAAApB,OAAAA,MAAA,CAGAoB,QAAAtB,KAAAA,KAEMsB,QAANpB,MAAAA,KACA,IAAQJ,KAARE,MAAAE,MAAAI,OAAA,EAAA,IAAAJ,MAAA,GACAd,MAAAyB,WAAAC,KAAA,YAAAhB,OAEAX,YAAAS,UAAA2B,QAAA,WACAnC,KAAAW,cAAAX,KAAAC,SAAAiB,OAAA,IAEAlB,KAAAW,gBAGAX,KAAAG,SAAAmB,MAAAZ,IAAAV,KAAAY,OAAAW,KAAA,4CAIAvB,KAAAW,cAAA,IACAX,KAAAW,gBACAX,KAAAG,SAAAmB,MAAAZ,IAAAV,KAAAY,OAAAW,KAAA,MAEAxB,YAAAS,UAAA4B,UAAA,SAAAC,OAAAC,QAAAC,UAIA,MAAAvC,MAAAG,SAAAiC,WAAAI,KAAAH,OAAAI,MAAAH,QAAAI,SAAAH,YAIAxC,YAAAS,UAAAmC,UAAA,SAAAjC,KAAA,MAAA,qBAGAX,aAAA6C,0HAAA,QAAA1C,eAAAU,KAAAE,+DCtGA+B,qBAAA,SAAAC,QAGA,QAAQD,yDASR,OAPAE,OAAAC,iBAAA,2BAEAD,MAAAE,cAAA,GAEAF,MAAAtB,cAEAsB,MAAA5C,SAAA,GAAAC,eAAAC,aACA0C,YAVAG,WAAAL,qBAAAC,QAWAD,qBAAArC,UAAA2C,iBAAA,SAAAzC,KAAAV,KAAAoD,aAAA1C,IAAAV,KAAAG,SAAAmB,KAAA,GAAA+B,oBAAArD,KAAAY,UAGAiC,qBAAArC,UAAAI,KAAA,SAAA0C,aAGA,MAFA,UAAQA,cAARA,aAAA,GAEAtD,KAAAoD,cAGAP,qBAAArC,UAAAoB,mBAAA,SAAA2B,UACA,MAAAA,UAAA1B,WAAA,MAAA7B,KAAAgD,iBAAAhC,SAAA,KACAhB,KAAAgD,iBAAAO,SAAAtC,UAAA,GAGAjB,KAAAgD,iBAAAO,UAGAV,qBAAArC,UAAAgD,UAAA,SAAAC,IAAAC,MAAA9C,KAAAE,OAEAd,KAAAiD,cAAAS,KACA,IAAAhD,KAAAE,MAAAE,MAAAI,OAAA,EAAA,IAAAJ,MAAA,GACAd,MAAAoD,aAAA1C,GAEA,IAAYiD,aAAZ3D,KAAA4B,mBAAAlB,IAEAV,MAAAyB,WAAAC,KAAAiC,cAGAd,qBAAArC,UAAAyB,aAAA,SAAAwB,IAAAC,MAAA9C,KAAAE,OACAd,KAAAiD,cAAAS,KACA,IAAAhD,KAAAE,MAAAE,MAAAI,OAAA,EAAA,IAAAJ,MAAA,GACAd,MAAAoD,aAAA1C,GACA,IAAAiD,aAAA3D,KAAA4B,mBAAAlB,IACAV,MAAAyB,WAAAC,KAAA,YAAAiC,yGAGAd,qBAAArC,UAAAoD,YAAA,WAAA,MAAA5D,MAAAgD,kBACAH,qBAAArC,UAAAqD,KAAA,WACA,GAAA7D,KAAAyB,WAAAP,OAAA,EAAA,sBAEA,IAAA4C,SAAA9D,KAAAyB,WAAAP,OAAA,EAAAlB,KAAAyB,WAAAzB,KAAAyB,WAAAP,OAAA,GAAA,EAAAlB,MAAAmD,iBAzDAW,WAgEAjB,qBAAArC,UAAA2B,QAAA,WAAA,KAAA,yCADA4B,gBAAAC,iBACAnB,sBAAAD,aACAjB,KAAAvB,cAAA6D,4IADAjE,KAAAkE,OAAAA"}