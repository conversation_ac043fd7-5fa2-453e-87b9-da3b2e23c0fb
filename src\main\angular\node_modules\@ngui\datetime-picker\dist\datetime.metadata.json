{"__symbolic": "module", "version": 1, "metadata": {"NguiDatetime": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"getMonthData": [{"__symbolic": "method"}]}, "statics": {"locale": {"date": "date", "time": "time", "year": "year", "month": "month", "day": "day", "hour": "hour", "minute": "minute", "currentTime": "current time"}, "days": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], "weekends": [0, 6], "daysOfWeek": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 33, "character": 4}, "right": "undefined"}, "thenExpression": [{"fullName": "Sunday", "shortName": "Su"}, {"fullName": "Monday", "shortName": "Mo"}, {"fullName": "Tuesday", "shortName": "Tu"}, {"fullName": "Wednesday", "shortName": "We"}, {"fullName": "Thursday", "shortName": "Th"}, {"fullName": "Friday", "shortName": "Fr"}, {"fullName": "Saturday", "shortName": "Sa"}], "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 41, "character": 30}}, "firstDayOfWeek": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 49, "character": 4}, "right": "undefined"}, "thenExpression": 0, "elseExpression": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 0, "character": 12, "context": {"name": "moment"}}}, "months": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 51, "character": 25}, "right": "undefined"}, "thenExpression": [{"fullName": "January", "shortName": "Jan"}, {"fullName": "February", "shortName": "Feb"}, {"fullName": "March", "shortName": "Mar"}, {"fullName": "April", "shortName": "Apr"}, {"fullName": "May", "shortName": "May"}, {"fullName": "June", "shortName": "Jun"}, {"fullName": "July", "shortName": "Jul"}, {"fullName": "August", "shortName": "Aug"}, {"fullName": "September", "shortName": "Sep"}, {"fullName": "October", "shortName": "Oct"}, {"fullName": "November", "shortName": "Nov"}, {"fullName": "December", "shortName": "Dec"}], "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 64, "character": 26}}}}}}