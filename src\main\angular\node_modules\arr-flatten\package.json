{"_args": [["arr-flatten@1.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "arr-flatten@1.0.3", "_id": "arr-flatten@1.0.3", "_inBundle": false, "_integrity": "sha1-onTthawIhJtr14R8RYB0XcUa37E=", "_location": "/arr-flatten", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "arr-flatten@1.0.3", "name": "arr-flatten", "escapedName": "arr-flatten", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/arr-diff"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/arr-flatten/-/arr-flatten-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-flatten/issues"}, "description": "Recursively flatten an array or arrays.", "devDependencies": {"ansi-bold": "^0.1.1", "array-flatten": "^2.1.1", "array-slice": "^1.0.0", "benchmarked": "^1.0.0", "compute-flatten": "^1.0.0", "flatit": "^1.1.1", "flatten": "^1.0.2", "flatten-array": "^1.0.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "just-flatten-it": "^1.1.23", "lodash.flattendeep": "^4.4.0", "m_flattened": "^1.0.1", "mocha": "^3.2.0", "utils-flatten": "^1.0.0", "write": "^0.3.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/arr-flatten", "keywords": ["arr", "array", "elements", "flat", "flatten", "nested", "recurse", "recursive", "recursively"], "license": "MIT", "main": "index.js", "name": "arr-flatten", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/arr-flatten.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-union", "array-unique", "array-each", "arr-filter"]}, "lint": {"reflinks": true}}, "version": "1.0.3"}