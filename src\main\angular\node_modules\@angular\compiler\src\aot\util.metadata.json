[{"__symbolic": "module", "version": 3, "metadata": {"ngfactoryFilePath": {"__symbolic": "function"}, "stripGeneratedFileSuffix": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 9, "character": 6, "context": {"name": "GENERATED_FILE"}}}, "isGeneratedFile": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 9, "character": 6, "context": {"name": "GENERATED_FILE"}}}, "splitTypescriptSuffix": {"__symbolic": "function"}, "summaryFileName": {"__symbolic": "function"}, "summaryForJitFileName": {"__symbolic": "function"}, "stripSummaryForJitFileSuffix": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 11, "character": 6, "context": {"name": "JIT_SUMMARY_FILE"}}}, "summaryForJitName": {"__symbolic": "function", "parameters": ["symbolName"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "reference", "name": "symbolName"}, "right": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "stripSummaryForJitNameSuffix": {"__symbolic": "function", "parameters": ["symbolName"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 12, "character": 6, "context": {"name": "JIT_SUMMARY_NAME"}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ngfactoryFilePath": {"__symbolic": "function"}, "stripGeneratedFileSuffix": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 9, "character": 6, "context": {"name": "GENERATED_FILE"}}}, "isGeneratedFile": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 9, "character": 6, "context": {"name": "GENERATED_FILE"}}}, "splitTypescriptSuffix": {"__symbolic": "function"}, "summaryFileName": {"__symbolic": "function"}, "summaryForJitFileName": {"__symbolic": "function"}, "stripSummaryForJitFileSuffix": {"__symbolic": "function", "parameters": ["filePath"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 11, "character": 6, "context": {"name": "JIT_SUMMARY_FILE"}}}, "summaryForJitName": {"__symbolic": "function", "parameters": ["symbolName"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "reference", "name": "symbolName"}, "right": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "stripSummaryForJitNameSuffix": {"__symbolic": "function", "parameters": ["symbolName"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 12, "character": 6, "context": {"name": "JIT_SUMMARY_NAME"}}}}}]