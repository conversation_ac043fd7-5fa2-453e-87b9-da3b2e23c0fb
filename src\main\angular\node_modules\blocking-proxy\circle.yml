machine:
  node:
    version: 6.9.1
  environment:
    # Fix issue with selenium-server in containers.
    # See http://github.com/SeleniumHQ/docker-selenium/issues/87
    DBUS_SESSION_BUS_ADDRESS: /dev/null

dependencies:
  override:
    - npm update
  cache_directories:
    - testapp/node_modules
  post:
    - npm run webdriver:
          background: true
    - cd testapp && npm update
    - npm run testapp:
          background: true
test:
  override:
    - ./node_modules/.bin/gulp lint
    - npm test
    - npm run test:e2e
