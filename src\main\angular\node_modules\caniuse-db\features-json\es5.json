{"title": "ECMAScript 5", "description": "Full support for the ECMAScript 5 specification. Features include `Function.prototype.bind`, Array methods like `indexOf`, `forEach`, `map` & `filter`, Object methods like `defineProperty`, `create` & `keys`, the `trim` method on Strings and many more.", "spec": "http://www.ecma-international.org/ecma-262/5.1/", "status": "other", "links": [{"url": "http://kangax.github.io/compat-table/es5/", "title": "Detailed compatibility tables & tests"}, {"url": "http://ejohn.org/blog/ecmascript-5-objects-and-properties/", "title": "Overview of objects & properties"}, {"url": "https://github.com/es-shims/es5-shim", "title": "ES5 polyfill"}], "bugs": [], "categories": ["JS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n #4", "9": "a #2", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a", "3": "a", "3.5": "a", "3.6": "a", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a", "3.2": "a", "4": "a", "5": "a", "5.1": "a", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "a", "9.5-9.6": "a", "10.0-10.1": "a", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "a #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "a", "2.2": "a", "2.3": "a", "3": "a", "4": "a #1 #2 #3", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "a", "10": "y"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "a #1", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "As the specification includes many JavaScript features, un-numbered partial support varies widely and is shown in detail on the [ECMAScript 5 compatibilty tables](http://kangax.github.io/compat-table/es5/) by Kangax.", "notes_by_num": {"1": "Does not support `parseInt()` ignoring leading zeros. ", "2": "Does not support Strict mode", "3": "Does not support zero-width chars in identifiers & Immutable `undefined`", "4": "IE8 has virtually no ES5 support, but does support `Object.defineProperty`, `Object.getOwnPropertyDescriptor`, JSON parsing & Property access on strings"}, "usage_perc_y": 93.41, "usage_perc_a": 4.32, "ucprefix": false, "parent": "", "keywords": "es5,function.bind,array.foreach,array.indexof,array.map,date.now,defineproperties,getprototypeof,keys,seal,freeze,preventextensions,issealed,isfrozen,isextensible,getownpropertydescriptorgetownpropertynames,toisostringc,isarray,lastindexof,every,some,reduce,reduceright,getter,setter", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}