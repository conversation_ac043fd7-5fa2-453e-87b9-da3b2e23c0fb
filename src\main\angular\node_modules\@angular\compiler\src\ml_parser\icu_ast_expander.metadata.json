[{"__symbolic": "module", "version": 3, "metadata": {"expandNodes": {"__symbolic": "function"}, "ExpansionResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "Node"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "ExpansionError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"expandNodes": {"__symbolic": "function"}, "ExpansionResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./ast", "name": "Node"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}]}}, "ExpansionError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}}}}]