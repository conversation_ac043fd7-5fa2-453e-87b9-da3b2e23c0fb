{"_args": [["color-name@1.1.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "color-name@1.1.2", "_id": "color-name@1.1.2", "_inBundle": false, "_integrity": "sha1-XIq3K2S9IhXWF66VWeuxSEdc+Y0=", "_location": "/color-name", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color-name@1.1.2", "name": "color-name", "escapedName": "color-name", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/color-convert", "/color-string"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/color-name/-/color-name-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "DY", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "description": "A list of color names and its values", "files": ["index.js"], "homepage": "https://github.com/dfcreative/color-name", "keywords": ["color-name", "color", "color-keyword", "keyword"], "license": "MIT", "main": "index.js", "name": "color-name", "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "scripts": {"test": "node test.js"}, "version": "1.1.2"}