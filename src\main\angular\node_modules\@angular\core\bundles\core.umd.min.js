/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("rxjs/Observable"),require("rxjs/observable/merge"),require("rxjs/operator/share"),require("rxjs/Subject")):"function"==typeof define&&define.amd?define(["exports","rxjs/Observable","rxjs/observable/merge","rxjs/operator/share","rxjs/Subject"],factory):factory((global.ng=global.ng||{},global.ng.core=global.ng.core||{}),global.Rx,global.Rx.Observable,global.Rx.Observable.prototype,global.Rx)}(this,function(exports,rxjs_Observable,rxjs_observable_merge,rxjs_operator_share,rxjs_Subject){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}function getSymbolIterator(){if(!_symbolIterator){var Symbol=_global.Symbol;if(Symbol&&Symbol.iterator)_symbolIterator=Symbol.iterator;else for(var keys=Object.getOwnPropertyNames(Map.prototype),i=0;i<keys.length;++i){var key=keys[i];"entries"!==key&&"size"!==key&&Map.prototype[key]===Map.prototype.entries&&(_symbolIterator=key)}}return _symbolIterator}function scheduleMicroTask(fn){Zone.current.scheduleMicroTask("scheduleMicrotask",fn)}function looseIdentical(a,b){return a===b||"number"==typeof a&&"number"==typeof b&&isNaN(a)&&isNaN(b)}function stringify(token){if("string"==typeof token)return token;if(null==token)return""+token;if(token.overriddenName)return""+token.overriddenName;if(token.name)return""+token.name;var res=token.toString();if(null==res)return""+res;var newLineIndex=res.indexOf("\n");return newLineIndex===-1?res:res.substring(0,newLineIndex)}function extractAnnotation(annotation){return"function"==typeof annotation&&annotation.hasOwnProperty("annotation")&&(annotation=annotation.annotation),annotation}function applyParams(fnOrArray,key){if(fnOrArray===Object||fnOrArray===String||fnOrArray===Function||fnOrArray===Number||fnOrArray===Array)throw new Error("Can not use native "+stringify(fnOrArray)+" as constructor");if("function"==typeof fnOrArray)return fnOrArray;if(Array.isArray(fnOrArray)){var annotations=fnOrArray,annoLength=annotations.length-1,fn=fnOrArray[annoLength];if("function"!=typeof fn)throw new Error("Last position of Class method array must be Function in key "+key+" was '"+stringify(fn)+"'");if(annoLength!=fn.length)throw new Error("Number of annotations ("+annoLength+") does not match number of arguments ("+fn.length+") in the function: "+stringify(fn));for(var paramsAnnotations=[],i=0,ii=annotations.length-1;i<ii;i++){var paramAnnotations=[];paramsAnnotations.push(paramAnnotations);var annotation=annotations[i];if(Array.isArray(annotation))for(var j=0;j<annotation.length;j++)paramAnnotations.push(extractAnnotation(annotation[j]));else"function"==typeof annotation?paramAnnotations.push(extractAnnotation(annotation)):paramAnnotations.push(annotation)}return Reflect$1.defineMetadata("parameters",paramsAnnotations,fn),fn}throw new Error("Only Function or Array is supported in Class definition for key '"+key+"' is '"+stringify(fnOrArray)+"'")}function Class(clsDef){var constructor=applyParams(clsDef.hasOwnProperty("constructor")?clsDef.constructor:void 0,"constructor"),proto=constructor.prototype;if(clsDef.hasOwnProperty("extends")){if("function"!=typeof clsDef.extends)throw new Error("Class definition 'extends' property must be a constructor function was: "+stringify(clsDef.extends));constructor.prototype=proto=Object.create(clsDef.extends.prototype)}for(var key in clsDef)"extends"!==key&&"prototype"!==key&&clsDef.hasOwnProperty(key)&&(proto[key]=applyParams(clsDef[key],key));this&&this.annotations instanceof Array&&Reflect$1.defineMetadata("annotations",this.annotations,constructor);var constructorName=constructor.name;return constructorName&&"constructor"!==constructorName||(constructor.overriddenName="class"+_nextClassId++),constructor}function makeDecorator(name,props,parentClass,chainFn){function DecoratorFactory(objOrType){if(!Reflect$1||!Reflect$1.getOwnMetadata)throw"reflect-metadata shim is required when using class decorators";if(this instanceof DecoratorFactory)return metaCtor.call(this,objOrType),this;var annotationInstance=new DecoratorFactory(objOrType),chainAnnotation="function"==typeof this&&Array.isArray(this.annotations)?this.annotations:[];chainAnnotation.push(annotationInstance);var TypeDecorator=function(cls){var annotations=Reflect$1.getOwnMetadata("annotations",cls)||[];return annotations.push(annotationInstance),Reflect$1.defineMetadata("annotations",annotations,cls),cls};return TypeDecorator.annotations=chainAnnotation,TypeDecorator.Class=Class,chainFn&&chainFn(TypeDecorator),TypeDecorator}var metaCtor=makeMetadataCtor(props);return parentClass&&(DecoratorFactory.prototype=Object.create(parentClass.prototype)),DecoratorFactory.prototype.toString=function(){return"@"+name},DecoratorFactory.annotationCls=DecoratorFactory,DecoratorFactory}function makeMetadataCtor(props){return function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i]=arguments[_i];if(props){var values=props.apply(void 0,args);for(var propName in values)this[propName]=values[propName]}}}function makeParamDecorator(name,props,parentClass){function ParamDecoratorFactory(){function ParamDecorator(cls,unusedKey,index){for(var parameters=Reflect$1.getOwnMetadata("parameters",cls)||[];parameters.length<=index;)parameters.push(null);return parameters[index]=parameters[index]||[],parameters[index].push(annotationInstance),Reflect$1.defineMetadata("parameters",parameters,cls),cls}for(var args=[],_i=0;_i<arguments.length;_i++)args[_i]=arguments[_i];if(this instanceof ParamDecoratorFactory)return metaCtor.apply(this,args),this;var annotationInstance=new(ParamDecoratorFactory.bind.apply(ParamDecoratorFactory,[void 0].concat(args)));return ParamDecorator.annotation=annotationInstance,ParamDecorator}var metaCtor=makeMetadataCtor(props);return parentClass&&(ParamDecoratorFactory.prototype=Object.create(parentClass.prototype)),ParamDecoratorFactory.prototype.toString=function(){return"@"+name},ParamDecoratorFactory.annotationCls=ParamDecoratorFactory,ParamDecoratorFactory}function makePropDecorator(name,props,parentClass){function PropDecoratorFactory(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i]=arguments[_i];if(this instanceof PropDecoratorFactory)return metaCtor.apply(this,args),this;var decoratorInstance=new(PropDecoratorFactory.bind.apply(PropDecoratorFactory,[void 0].concat(args)));return function(target,name){var meta=Reflect$1.getOwnMetadata("propMetadata",target.constructor)||{};meta[name]=meta.hasOwnProperty(name)&&meta[name]||[],meta[name].unshift(decoratorInstance),Reflect$1.defineMetadata("propMetadata",meta,target.constructor)}}var metaCtor=makeMetadataCtor(props);return parentClass&&(PropDecoratorFactory.prototype=Object.create(parentClass.prototype)),PropDecoratorFactory.prototype.toString=function(){return"@"+name},PropDecoratorFactory.annotationCls=PropDecoratorFactory,PropDecoratorFactory}function isDefaultChangeDetectionStrategy(changeDetectionStrategy){return null==changeDetectionStrategy||changeDetectionStrategy===ChangeDetectionStrategy.Default}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function forwardRef(forwardRefFn){return forwardRefFn.__forward_ref__=forwardRef,forwardRefFn.toString=function(){return stringify(this())},forwardRefFn}function resolveForwardRef(type){return"function"==typeof type&&type.hasOwnProperty("__forward_ref__")&&type.__forward_ref__===forwardRef?type():type}function getDebugContext(error){return error[ERROR_DEBUG_CONTEXT]}function getOriginalError(error){return error[ERROR_ORIGINAL_ERROR]}function getErrorLogger(error){return error[ERROR_LOGGER]||defaultErrorLogger}function defaultErrorLogger(console){for(var values=[],_i=1;_i<arguments.length;_i++)values[_i-1]=arguments[_i];console.error.apply(console,values)}function wrappedError(message,originalError){var msg=message+" caused by: "+(originalError instanceof Error?originalError.message:originalError),error=Error(msg);return error[ERROR_ORIGINAL_ERROR]=originalError,error}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function findFirstClosedCycle(keys){for(var res=[],i=0;i<keys.length;++i){if(res.indexOf(keys[i])>-1)return res.push(keys[i]),res;res.push(keys[i])}return res}function constructResolvingPath(keys){if(keys.length>1){var reversed=findFirstClosedCycle(keys.slice().reverse()),tokenStrs=reversed.map(function(k){return stringify(k.token)});return" ("+tokenStrs.join(" -> ")+")"}return""}function injectionError(injector,key,constructResolvingMessage,originalError){var keys=[key],errMsg=constructResolvingMessage(keys),error=originalError?wrappedError(errMsg,originalError):Error(errMsg);return error.addKey=addKey,error.keys=keys,error.injectors=[injector],error.constructResolvingMessage=constructResolvingMessage,error[ERROR_ORIGINAL_ERROR]=originalError,error}function addKey(injector,key){this.injectors.push(injector),this.keys.push(key),this.message=this.constructResolvingMessage(this.keys)}function noProviderError(injector,key){return injectionError(injector,key,function(keys){var first=stringify(keys[0].token);return"No provider for "+first+"!"+constructResolvingPath(keys)})}function cyclicDependencyError(injector,key){return injectionError(injector,key,function(keys){return"Cannot instantiate cyclic dependency!"+constructResolvingPath(keys)})}function instantiationError(injector,originalException,originalStack,key){return injectionError(injector,key,function(keys){var first=stringify(keys[0].token);return originalException.message+": Error during instantiation of "+first+"!"+constructResolvingPath(keys)+"."},originalException)}function invalidProviderError(provider){return Error("Invalid provider - only instances of Provider and Type are allowed, got: "+provider)}function noAnnotationError(typeOrFunc,params){for(var signature=[],i=0,ii=params.length;i<ii;i++){var parameter=params[i];parameter&&0!=parameter.length?signature.push(parameter.map(stringify).join(" ")):signature.push("?")}return Error("Cannot resolve all parameters for '"+stringify(typeOrFunc)+"'("+signature.join(", ")+"). Make sure that all the parameters are decorated with Inject or have valid type annotations and that '"+stringify(typeOrFunc)+"' is decorated with Injectable.")}function outOfBoundsError(index){return Error("Index "+index+" is out-of-bounds.")}function mixingMultiProvidersWithRegularProvidersError(provider1,provider2){return Error("Cannot mix multi providers and regular providers, got: "+provider1+" "+provider2)}function isType(v){return"function"==typeof v}function convertTsickleDecoratorIntoMetadata(decoratorInvocations){return decoratorInvocations?decoratorInvocations.map(function(decoratorInvocation){var decoratorType=decoratorInvocation.type,annotationCls=decoratorType.annotationCls,annotationArgs=decoratorInvocation.args?decoratorInvocation.args:[];return new(annotationCls.bind.apply(annotationCls,[void 0].concat(annotationArgs)))}):[]}function getParentCtor(ctor){var parentProto=Object.getPrototypeOf(ctor.prototype),parentCtor=parentProto?parentProto.constructor:null;return parentCtor||Object}function resolveReflectiveFactory(provider){var factoryFn,resolvedDeps;if(provider.useClass){var useClass=resolveForwardRef(provider.useClass);factoryFn=reflector.factory(useClass),resolvedDeps=_dependenciesFor(useClass)}else provider.useExisting?(factoryFn=function(aliasInstance){return aliasInstance},resolvedDeps=[ReflectiveDependency.fromKey(ReflectiveKey.get(provider.useExisting))]):provider.useFactory?(factoryFn=provider.useFactory,resolvedDeps=constructDependencies(provider.useFactory,provider.deps)):(factoryFn=function(){return provider.useValue},resolvedDeps=_EMPTY_LIST);return new ResolvedReflectiveFactory(factoryFn,resolvedDeps)}function resolveReflectiveProvider(provider){return new ResolvedReflectiveProvider_(ReflectiveKey.get(provider.provide),[resolveReflectiveFactory(provider)],provider.multi||!1)}function resolveReflectiveProviders(providers){var normalized=_normalizeProviders(providers,[]),resolved=normalized.map(resolveReflectiveProvider),resolvedProviderMap=mergeResolvedReflectiveProviders(resolved,new Map);return Array.from(resolvedProviderMap.values())}function mergeResolvedReflectiveProviders(providers,normalizedProvidersMap){for(var i=0;i<providers.length;i++){var provider=providers[i],existing=normalizedProvidersMap.get(provider.key.id);if(existing){if(provider.multiProvider!==existing.multiProvider)throw mixingMultiProvidersWithRegularProvidersError(existing,provider);if(provider.multiProvider)for(var j=0;j<provider.resolvedFactories.length;j++)existing.resolvedFactories.push(provider.resolvedFactories[j]);else normalizedProvidersMap.set(provider.key.id,provider)}else{var resolvedProvider=void 0;resolvedProvider=provider.multiProvider?new ResolvedReflectiveProvider_(provider.key,provider.resolvedFactories.slice(),provider.multiProvider):provider,normalizedProvidersMap.set(provider.key.id,resolvedProvider)}}return normalizedProvidersMap}function _normalizeProviders(providers,res){return providers.forEach(function(b){if(b instanceof Type)res.push({provide:b,useClass:b});else if(b&&"object"==typeof b&&void 0!==b.provide)res.push(b);else{if(!(b instanceof Array))throw invalidProviderError(b);_normalizeProviders(b,res)}}),res}function constructDependencies(typeOrFunc,dependencies){if(dependencies){var params_1=dependencies.map(function(t){return[t]});return dependencies.map(function(t){return _extractToken(typeOrFunc,t,params_1)})}return _dependenciesFor(typeOrFunc)}function _dependenciesFor(typeOrFunc){var params=reflector.parameters(typeOrFunc);if(!params)return[];if(params.some(function(p){return null==p}))throw noAnnotationError(typeOrFunc,params);return params.map(function(p){return _extractToken(typeOrFunc,p,params)})}function _extractToken(typeOrFunc,metadata,params){var token=null,optional=!1;if(!Array.isArray(metadata))return metadata instanceof Inject?_createDependency(metadata.token,optional,null):_createDependency(metadata,optional,null);for(var visibility=null,i=0;i<metadata.length;++i){var paramMetadata=metadata[i];paramMetadata instanceof Type?token=paramMetadata:paramMetadata instanceof Inject?token=paramMetadata.token:paramMetadata instanceof Optional?optional=!0:paramMetadata instanceof Self||paramMetadata instanceof SkipSelf?visibility=paramMetadata:paramMetadata instanceof InjectionToken&&(token=paramMetadata)}if(token=resolveForwardRef(token),null!=token)return _createDependency(token,optional,visibility);throw noAnnotationError(typeOrFunc,params)}function _createDependency(token,optional,visibility){return new ReflectiveDependency(ReflectiveKey.get(token),optional,visibility)}function _mapProviders(injector,fn){for(var res=new Array(injector._providers.length),i=0;i<injector._providers.length;++i)res[i]=fn(injector.getProviderAtIndex(i));return res}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function isPromise(obj){return!!obj&&"function"==typeof obj.then}function isObservable(obj){return!!obj&&"function"==typeof obj.subscribe}function _appIdRandomProviderFactory(){return""+_randomChar()+_randomChar()+_randomChar()}function _randomChar(){return String.fromCharCode(97+Math.floor(25*Math.random()))}function _throwError(){throw new Error("Runtime compiler is not loaded")}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function noComponentFactoryError(component){var error=Error("No component factory found for "+stringify(component)+". Did you add it to @NgModule.entryComponents?");return error[ERROR_COMPONENT]=component,error}function detectWTF(){var wtf=_global.wtf;return!(!wtf||!(trace=wtf.trace))&&(events=trace.events,!0)}function createScope$1(signature,flags){return void 0===flags&&(flags=null),events.createScope(signature,flags)}function leave(scope,returnValue){return trace.leaveScope(scope,returnValue),returnValue}function startTimeRange(rangeType,action){return trace.beginTimeRange(rangeType,action)}function endTimeRange(range){trace.endTimeRange(range)}function noopScope(arg0,arg1){return null}function setTestabilityGetter(getter){_testabilityGetter=getter}function enableProdMode(){if(_runModeLocked)throw new Error("Cannot enable prod mode after platform setup.");_devMode=!1}function isDevMode(){return _runModeLocked=!0,_devMode}function createPlatform(injector){if(_platform&&!_platform.destroyed&&!_platform.injector.get(ALLOW_MULTIPLE_PLATFORMS,!1))throw new Error("There can be only one platform. Destroy the previous one to create a new one.");_platform=injector.get(PlatformRef);var inits=injector.get(PLATFORM_INITIALIZER,null);return inits&&inits.forEach(function(init){return init()}),_platform}function createPlatformFactory(parentPlatformFactory,name,providers){void 0===providers&&(providers=[]);var marker=new InjectionToken("Platform: "+name);return function(extraProviders){void 0===extraProviders&&(extraProviders=[]);var platform=getPlatform();return platform&&!platform.injector.get(ALLOW_MULTIPLE_PLATFORMS,!1)||(parentPlatformFactory?parentPlatformFactory(providers.concat(extraProviders).concat({provide:marker,useValue:!0})):createPlatform(ReflectiveInjector.resolveAndCreate(providers.concat(extraProviders).concat({provide:marker,useValue:!0})))),assertPlatform(marker)}}function assertPlatform(requiredToken){var platform=getPlatform();if(!platform)throw new Error("No platform exists!");if(!platform.injector.get(requiredToken,null))throw new Error("A platform with a different configuration has been created. Please destroy it first.");return platform}function destroyPlatform(){_platform&&!_platform.destroyed&&_platform.destroy()}function getPlatform(){return _platform&&!_platform.destroyed?_platform:null}function _callAndReportToErrorHandler(errorHandler,callback){try{var result=callback();return isPromise(result)?result.catch(function(e){throw errorHandler.handleError(e),e}):result}catch(e){throw errorHandler.handleError(e),e}}function remove(list,el){var index=list.indexOf(el);index>-1&&list.splice(index,1)}function registerModuleFactory(id,factory){var existing=moduleFactories.get(id);if(existing)throw new Error("Duplicate module registered for "+id+" - "+existing.moduleType.name+" vs "+factory.moduleType.name);moduleFactories.set(id,factory)}function getModuleFactory(id){var factory=moduleFactories.get(id);if(!factory)throw new Error("No module with ID "+id+" loaded");return factory}function flatten(list){return list.reduce(function(flat,item){var flatItem=Array.isArray(item)?flatten(item):item;return flat.concat(flatItem)},[])}function checkNotEmpty(value,modulePath,exportName){if(!value)throw new Error("Cannot find '"+exportName+"' in '"+modulePath+"'");return value}function asNativeElements(debugEls){return debugEls.map(function(el){return el.nativeElement})}function _queryElementChildren(element,predicate,matches){element.childNodes.forEach(function(node){node instanceof DebugElement&&(predicate(node)&&matches.push(node),_queryElementChildren(node,predicate,matches))})}function _queryNodeChildren(parentNode,predicate,matches){parentNode instanceof DebugElement&&parentNode.childNodes.forEach(function(node){predicate(node)&&matches.push(node),node instanceof DebugElement&&_queryNodeChildren(node,predicate,matches)})}function getDebugNode(nativeNode){return _nativeNodeToDebugNode.get(nativeNode)||null}function indexDebugNode(node){_nativeNodeToDebugNode.set(node.nativeNode,node)}function removeDebugNodeFromIndex(node){_nativeNodeToDebugNode.delete(node.nativeNode)}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function devModeEqual(a,b){var isListLikeIterableA=isListLikeIterable(a),isListLikeIterableB=isListLikeIterable(b);if(isListLikeIterableA&&isListLikeIterableB)return areIterablesEqual(a,b,devModeEqual);var isAObject=a&&("object"==typeof a||"function"==typeof a),isBObject=b&&("object"==typeof b||"function"==typeof b);return!(isListLikeIterableA||!isAObject||isListLikeIterableB||!isBObject)||looseIdentical(a,b)}function isListLikeIterable(obj){return!!isJsObject(obj)&&(Array.isArray(obj)||!(obj instanceof Map)&&getSymbolIterator()in obj)}function areIterablesEqual(a,b,comparator){for(var iterator1=a[getSymbolIterator()](),iterator2=b[getSymbolIterator()]();;){var item1=iterator1.next(),item2=iterator2.next();if(item1.done&&item2.done)return!0;if(item1.done||item2.done)return!1;if(!comparator(item1.value,item2.value))return!1}}function iterateListLike(obj,fn){if(Array.isArray(obj))for(var i=0;i<obj.length;i++)fn(obj[i]);else for(var iterator=obj[getSymbolIterator()](),item=void 0;!(item=iterator.next()).done;)fn(item.value)}function isJsObject(o){return null!==o&&("function"==typeof o||"object"==typeof o)}function getPreviousIndex(item,addRemoveOffset,moveOffsets){var previousIndex=item.previousIndex;if(null===previousIndex)return previousIndex;var moveOffset=0;return moveOffsets&&previousIndex<moveOffsets.length&&(moveOffset=moveOffsets[previousIndex]),previousIndex+addRemoveOffset+moveOffset}function getTypeNameForDebugging(type){return type.name||typeof type}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function _reflector(){return reflector}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function _iterableDiffersFactory(){return defaultIterableDiffers}function _keyValueDiffersFactory(){return defaultKeyValueDiffers}function _localeFactory(locale){return locale||"en-US"}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function asTextData(view,index){return view.nodes[index]}function asElementData(view,index){return view.nodes[index]}function asProviderData(view,index){return view.nodes[index]}function asPureExpressionData(view,index){return view.nodes[index]}function asQueryList(view,index){return view.nodes[index]}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function expressionChangedAfterItHasBeenCheckedError(context,oldValue,currValue,isFirstCheck){var msg="ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked. Previous value: '"+oldValue+"'. Current value: '"+currValue+"'.";return isFirstCheck&&(msg+=" It seems like the view has been created after its parent and its children have been dirty checked. Has it been created in a change detection hook ?"),viewDebugError(msg,context)}function viewWrappedDebugError(err,context){return err instanceof Error||(err=new Error(err.toString())),_addDebugContext(err,context),err}function viewDebugError(msg,context){var err=new Error(msg);return _addDebugContext(err,context),err}function _addDebugContext(err,context){err[ERROR_DEBUG_CONTEXT]=context,err[ERROR_LOGGER]=context.logError.bind(context)}function isViewDebugError(err){return!!getDebugContext(err)}function viewDestroyedError(action){return new Error("ViewDestroyedError: Attempt to use a destroyed view: "+action)}function tokenKey(token){var key=_tokenKeyCache.get(token);return key||(key=stringify(token)+"_"+_tokenKeyCache.size,_tokenKeyCache.set(token,key)),key}function unwrapValue(view,nodeIdx,bindingIdx,value){if(value instanceof WrappedValue){value=value.wrapped;var globalBindingIdx=view.def.nodes[nodeIdx].bindingIndex+bindingIdx,oldValue=view.oldValues[globalBindingIdx];oldValue instanceof WrappedValue&&(oldValue=oldValue.wrapped),view.oldValues[globalBindingIdx]=new WrappedValue(oldValue)}return value}function createRendererType2(values){return{id:UNDEFINED_RENDERER_TYPE_ID,styles:values.styles,encapsulation:values.encapsulation,data:values.data}}function resolveRendererType2(type){if(type&&type.id===UNDEFINED_RENDERER_TYPE_ID){var isFilled=null!=type.encapsulation&&type.encapsulation!==ViewEncapsulation.None||type.styles.length||Object.keys(type.data).length;isFilled?type.id="c"+_renderCompCount++:type.id=EMPTY_RENDERER_TYPE_ID}return type&&type.id===EMPTY_RENDERER_TYPE_ID&&(type=null),type||null}function checkBinding(view,def,bindingIdx,value){var oldValues=view.oldValues;return!(!(2&view.state)&&looseIdentical(oldValues[def.bindingIndex+bindingIdx],value))}function checkAndUpdateBinding(view,def,bindingIdx,value){return!!checkBinding(view,def,bindingIdx,value)&&(view.oldValues[def.bindingIndex+bindingIdx]=value,!0)}function checkBindingNoChanges(view,def,bindingIdx,value){var oldValue=view.oldValues[def.bindingIndex+bindingIdx];if(1&view.state||!devModeEqual(oldValue,value))throw expressionChangedAfterItHasBeenCheckedError(Services.createDebugContext(view,def.index),oldValue,value,0!==(1&view.state))}function markParentViewsForCheck(view){for(var currView=view;currView;)2&currView.def.flags&&(currView.state|=8),currView=currView.viewContainerParent||currView.parent}function markParentViewsForCheckProjectedViews(view,endView){for(var currView=view;currView&&currView!==endView;)currView.state|=64,currView=currView.viewContainerParent||currView.parent}function dispatchEvent(view,nodeIndex,eventName,event){var nodeDef=view.def.nodes[nodeIndex],startView=33554432&nodeDef.flags?asElementData(view,nodeIndex).componentView:view;return markParentViewsForCheck(startView),Services.handleEvent(view,nodeIndex,eventName,event)}function declaredViewContainer(view){if(view.parent){var parentView=view.parent;return asElementData(parentView,view.parentNodeDef.index)}return null}function viewParentEl(view){var parentView=view.parent;return parentView?view.parentNodeDef.parent:null}function renderNode(view,def){switch(201347067&def.flags){case 1:return asElementData(view,def.index).renderElement;case 2:return asTextData(view,def.index).renderText}}function elementEventFullName(target,name){return target?target+":"+name:name}function isComponentView(view){return!!view.parent&&!!(32768&view.parentNodeDef.flags)}function isEmbeddedView(view){return!(!view.parent||32768&view.parentNodeDef.flags)}function filterQueryId(queryId){return 1<<queryId%32}function splitMatchedQueriesDsl(matchedQueriesDsl){var matchedQueries={},matchedQueryIds=0,references={};return matchedQueriesDsl&&matchedQueriesDsl.forEach(function(_a){var queryId=_a[0],valueType=_a[1];"number"==typeof queryId?(matchedQueries[queryId]=valueType,matchedQueryIds|=filterQueryId(queryId)):references[queryId]=valueType}),{matchedQueries:matchedQueries,references:references,matchedQueryIds:matchedQueryIds}}function splitDepsDsl(deps){return deps.map(function(value){var token,flags;return Array.isArray(value)?(flags=value[0],token=value[1]):(flags=0,token=value),{flags:flags,token:token,tokenKey:tokenKey(token)}})}function getParentRenderElement(view,renderHost,def){var renderParent=def.renderParent;return renderParent?0===(1&renderParent.flags)||0===(33554432&renderParent.flags)||renderParent.element.componentRendererType&&renderParent.element.componentRendererType.encapsulation===ViewEncapsulation.Native?asElementData(view,def.renderParent.index).renderElement:void 0:renderHost}function resolveDefinition(factory){var value=DEFINITION_CACHE.get(factory);return value||(value=factory(function(){return NOOP}),value.factory=factory,DEFINITION_CACHE.set(factory,value)),value}function rootRenderNodes(view){var renderNodes=[];return visitRootRenderNodes(view,0,void 0,void 0,renderNodes),renderNodes}function visitRootRenderNodes(view,action,parentNode,nextSibling,target){3===action&&(parentNode=view.renderer.parentNode(renderNode(view,view.def.lastRenderRootNode))),visitSiblingRenderNodes(view,action,0,view.def.nodes.length-1,parentNode,nextSibling,target)}function visitSiblingRenderNodes(view,action,startIndex,endIndex,parentNode,nextSibling,target){for(var i=startIndex;i<=endIndex;i++){var nodeDef=view.def.nodes[i];11&nodeDef.flags&&visitRenderNode(view,nodeDef,action,parentNode,nextSibling,target),i+=nodeDef.childCount}}function visitProjectedRenderNodes(view,ngContentIndex,action,parentNode,nextSibling,target){for(var compView=view;compView&&!isComponentView(compView);)compView=compView.parent;for(var hostView=compView.parent,hostElDef=viewParentEl(compView),startIndex=hostElDef.index+1,endIndex=hostElDef.index+hostElDef.childCount,i=startIndex;i<=endIndex;i++){var nodeDef=hostView.def.nodes[i];nodeDef.ngContentIndex===ngContentIndex&&visitRenderNode(hostView,nodeDef,action,parentNode,nextSibling,target),i+=nodeDef.childCount}if(!hostView.parent){var projectedNodes=view.root.projectableNodes[ngContentIndex];if(projectedNodes)for(var i=0;i<projectedNodes.length;i++)execRenderNodeAction(view,projectedNodes[i],action,parentNode,nextSibling,target)}}function visitRenderNode(view,nodeDef,action,parentNode,nextSibling,target){if(8&nodeDef.flags)visitProjectedRenderNodes(view,nodeDef.ngContent.index,action,parentNode,nextSibling,target);else{var rn=renderNode(view,nodeDef);if(3===action&&33554432&nodeDef.flags&&48&nodeDef.bindingFlags){if(16&nodeDef.bindingFlags&&execRenderNodeAction(view,rn,action,parentNode,nextSibling,target),32&nodeDef.bindingFlags){var compView=asElementData(view,nodeDef.index).componentView;execRenderNodeAction(compView,rn,action,parentNode,nextSibling,target)}}else execRenderNodeAction(view,rn,action,parentNode,nextSibling,target);if(16777216&nodeDef.flags)for(var embeddedViews=asElementData(view,nodeDef.index).viewContainer._embeddedViews,k=0;k<embeddedViews.length;k++)visitRootRenderNodes(embeddedViews[k],action,parentNode,nextSibling,target);1&nodeDef.flags&&!nodeDef.element.name&&visitSiblingRenderNodes(view,action,nodeDef.index+1,nodeDef.index+nodeDef.childCount,parentNode,nextSibling,target)}}function execRenderNodeAction(view,renderNode,action,parentNode,nextSibling,target){var renderer=view.renderer;switch(action){case 1:renderer.appendChild(parentNode,renderNode);break;case 2:renderer.insertBefore(parentNode,renderNode,nextSibling);break;case 3:renderer.removeChild(parentNode,renderNode);break;case 0:target.push(renderNode)}}function splitNamespace(name){if(":"===name[0]){var match=name.match(NS_PREFIX_RE);return[match[1],match[2]]}return["",name]}function calcBindingFlags(bindings){for(var flags=0,i=0;i<bindings.length;i++)flags|=bindings[i].flags;return flags}function interpolate(valueCount,constAndInterp){for(var result="",i=0;i<2*valueCount;i+=2)result=result+constAndInterp[i]+_toStringWithNull(constAndInterp[i+1]);return result+constAndInterp[2*valueCount]}function inlineInterpolate(valueCount,c0,a1,c1,a2,c2,a3,c3,a4,c4,a5,c5,a6,c6,a7,c7,a8,c8,a9,c9){switch(valueCount){case 1:return c0+_toStringWithNull(a1)+c1;case 2:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2;case 3:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3;case 4:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4;case 5:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4+_toStringWithNull(a5)+c5;case 6:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4+_toStringWithNull(a5)+c5+_toStringWithNull(a6)+c6;case 7:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4+_toStringWithNull(a5)+c5+_toStringWithNull(a6)+c6+_toStringWithNull(a7)+c7;case 8:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4+_toStringWithNull(a5)+c5+_toStringWithNull(a6)+c6+_toStringWithNull(a7)+c7+_toStringWithNull(a8)+c8;case 9:return c0+_toStringWithNull(a1)+c1+_toStringWithNull(a2)+c2+_toStringWithNull(a3)+c3+_toStringWithNull(a4)+c4+_toStringWithNull(a5)+c5+_toStringWithNull(a6)+c6+_toStringWithNull(a7)+c7+_toStringWithNull(a8)+c8+_toStringWithNull(a9)+c9;default:throw new Error("Does not support more than 9 expressions")}}function _toStringWithNull(v){return null!=v?v.toString():""}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function anchorDef(flags,matchedQueriesDsl,ngContentIndex,childCount,handleEvent,templateFactory){flags|=1;var _a=splitMatchedQueriesDsl(matchedQueriesDsl),matchedQueries=_a.matchedQueries,references=_a.references,matchedQueryIds=_a.matchedQueryIds,template=templateFactory?resolveDefinition(templateFactory):null;return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:matchedQueries,matchedQueryIds:matchedQueryIds,references:references,ngContentIndex:ngContentIndex,childCount:childCount,bindings:[],bindingFlags:0,outputs:[],element:{ns:null,name:null,attrs:null,template:template,componentProvider:null,componentView:null,componentRendererType:null,publicProviders:null,allProviders:null,handleEvent:handleEvent||NOOP},provider:null,text:null,query:null,ngContent:null}}function elementDef(flags,matchedQueriesDsl,ngContentIndex,childCount,namespaceAndName,fixedAttrs,bindings,outputs,handleEvent,componentView,componentRendererType){void 0===fixedAttrs&&(fixedAttrs=[]),handleEvent||(handleEvent=NOOP);var _a=splitMatchedQueriesDsl(matchedQueriesDsl),matchedQueries=_a.matchedQueries,references=_a.references,matchedQueryIds=_a.matchedQueryIds,ns=null,name=null;namespaceAndName&&(_b=splitNamespace(namespaceAndName),ns=_b[0],name=_b[1]),bindings=bindings||[];for(var bindingDefs=new Array(bindings.length),i=0;i<bindings.length;i++){var _c=bindings[i],bindingFlags=_c[0],namespaceAndName_1=_c[1],suffixOrSecurityContext=_c[2],_d=splitNamespace(namespaceAndName_1),ns_1=_d[0],name_1=_d[1],securityContext=void 0,suffix=void 0;switch(15&bindingFlags){case 4:suffix=suffixOrSecurityContext;break;case 1:case 8:securityContext=suffixOrSecurityContext}bindingDefs[i]={flags:bindingFlags,ns:ns_1,name:name_1,nonMinifiedName:name_1,securityContext:securityContext,suffix:suffix}}outputs=outputs||[];for(var outputDefs=new Array(outputs.length),i=0;i<outputs.length;i++){var _e=outputs[i],target=_e[0],eventName=_e[1];outputDefs[i]={type:0,target:target,eventName:eventName,propName:null}}fixedAttrs=fixedAttrs||[];var attrs=fixedAttrs.map(function(_a){var namespaceAndName=_a[0],value=_a[1],_b=splitNamespace(namespaceAndName),ns=_b[0],name=_b[1];return[ns,name,value]});return componentRendererType=resolveRendererType2(componentRendererType),componentView&&(flags|=33554432),flags|=1,{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:matchedQueries,matchedQueryIds:matchedQueryIds,references:references,ngContentIndex:ngContentIndex,childCount:childCount,bindings:bindingDefs,bindingFlags:calcBindingFlags(bindingDefs),outputs:outputDefs,element:{ns:ns,name:name,attrs:attrs,template:null,componentProvider:null,componentView:componentView||null,componentRendererType:componentRendererType,publicProviders:null,allProviders:null,handleEvent:handleEvent||NOOP},provider:null,text:null,query:null,ngContent:null};var _b}function createElement(view,renderHost,def){var el,elDef=def.element,rootSelectorOrNode=view.root.selectorOrNode,renderer=view.renderer;if(view.parent||!rootSelectorOrNode){el=elDef.name?renderer.createElement(elDef.name,elDef.ns):renderer.createComment("");var parentEl=getParentRenderElement(view,renderHost,def);parentEl&&renderer.appendChild(parentEl,el)}else el=renderer.selectRootElement(rootSelectorOrNode);if(elDef.attrs)for(var i=0;i<elDef.attrs.length;i++){var _a=elDef.attrs[i],ns=_a[0],name=_a[1],value=_a[2];renderer.setAttribute(el,name,value,ns)}return el}function listenToElementOutputs(view,compView,def,el){for(var i=0;i<def.outputs.length;i++){var output=def.outputs[i],handleEventClosure=renderEventHandlerClosure(view,def.index,elementEventFullName(output.target,output.eventName)),listenTarget=output.target,listenerView=view;"component"===output.target&&(listenTarget=null,listenerView=compView);var disposable=listenerView.renderer.listen(listenTarget||el,output.eventName,handleEventClosure);view.disposables[def.outputIndex+i]=disposable}}function renderEventHandlerClosure(view,index,eventName){return function(event){try{return dispatchEvent(view,index,eventName,event)}catch(e){view.root.errorHandler.handleError(e)}}}function checkAndUpdateElementInline(view,def,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var bindLen=def.bindings.length,changed=!1;return bindLen>0&&checkAndUpdateElementValue(view,def,0,v0)&&(changed=!0),bindLen>1&&checkAndUpdateElementValue(view,def,1,v1)&&(changed=!0),bindLen>2&&checkAndUpdateElementValue(view,def,2,v2)&&(changed=!0),bindLen>3&&checkAndUpdateElementValue(view,def,3,v3)&&(changed=!0),bindLen>4&&checkAndUpdateElementValue(view,def,4,v4)&&(changed=!0),bindLen>5&&checkAndUpdateElementValue(view,def,5,v5)&&(changed=!0),bindLen>6&&checkAndUpdateElementValue(view,def,6,v6)&&(changed=!0),bindLen>7&&checkAndUpdateElementValue(view,def,7,v7)&&(changed=!0),bindLen>8&&checkAndUpdateElementValue(view,def,8,v8)&&(changed=!0),bindLen>9&&checkAndUpdateElementValue(view,def,9,v9)&&(changed=!0),changed}function checkAndUpdateElementDynamic(view,def,values){for(var changed=!1,i=0;i<values.length;i++)checkAndUpdateElementValue(view,def,i,values[i])&&(changed=!0);return changed}function checkAndUpdateElementValue(view,def,bindingIdx,value){if(!checkAndUpdateBinding(view,def,bindingIdx,value))return!1;var binding=def.bindings[bindingIdx],elData=asElementData(view,def.index),renderNode$$1=elData.renderElement,name=binding.name;switch(15&binding.flags){case 1:setElementAttribute(view,binding,renderNode$$1,binding.ns,name,value);break;case 2:setElementClass(view,renderNode$$1,name,value);break;case 4:setElementStyle(view,binding,renderNode$$1,name,value);break;case 8:var bindView=33554432&def.flags&&32&binding.flags?elData.componentView:view;setElementProperty(bindView,binding,renderNode$$1,name,value)}return!0}function setElementAttribute(view,binding,renderNode$$1,ns,name,value){var securityContext=binding.securityContext,renderValue=securityContext?view.root.sanitizer.sanitize(securityContext,value):value;renderValue=null!=renderValue?renderValue.toString():null;var renderer=view.renderer;null!=value?renderer.setAttribute(renderNode$$1,name,renderValue,ns):renderer.removeAttribute(renderNode$$1,name,ns)}function setElementClass(view,renderNode$$1,name,value){var renderer=view.renderer;value?renderer.addClass(renderNode$$1,name):renderer.removeClass(renderNode$$1,name)}function setElementStyle(view,binding,renderNode$$1,name,value){var renderValue=view.root.sanitizer.sanitize(SecurityContext.STYLE,value);if(null!=renderValue){renderValue=renderValue.toString();var unit=binding.suffix;null!=unit&&(renderValue+=unit)}else renderValue=null;var renderer=view.renderer;null!=renderValue?renderer.setStyle(renderNode$$1,name,renderValue):renderer.removeStyle(renderNode$$1,name)}function setElementProperty(view,binding,renderNode$$1,name,value){var securityContext=binding.securityContext,renderValue=securityContext?view.root.sanitizer.sanitize(securityContext,value):value;view.renderer.setProperty(renderNode$$1,name,renderValue)}function moduleProvideDef(flags,token,value,deps){var depDefs=splitDepsDsl(deps);return{index:-1,deps:depDefs,flags:flags,token:token,value:value}}function moduleDef(providers){for(var providersByKey={},i=0;i<providers.length;i++){var provider=providers[i];provider.index=i,providersByKey[tokenKey(provider.token)]=provider}return{factory:null,providersByKey:providersByKey,providers:providers}}function initNgModule(data){for(var def=data._def,providers=data._providers=new Array(def.providers.length),i=0;i<def.providers.length;i++){var provDef=def.providers[i];providers[i]=4096&provDef.flags?NOT_CREATED$1:_createProviderInstance$1(data,provDef)}}function resolveNgModuleDep(data,depDef,notFoundValue){if(void 0===notFoundValue&&(notFoundValue=Injector.THROW_IF_NOT_FOUND),8&depDef.flags)return depDef.token;if(2&depDef.flags&&(notFoundValue=null),1&depDef.flags)return data._parent.get(depDef.token,notFoundValue);var tokenKey$$1=depDef.tokenKey;switch(tokenKey$$1){case InjectorRefTokenKey$1:case NgModuleRefTokenKey:return data}var providerDef=data._def.providersByKey[tokenKey$$1];if(providerDef){var providerInstance=data._providers[providerDef.index];return providerInstance===NOT_CREATED$1&&(providerInstance=data._providers[providerDef.index]=_createProviderInstance$1(data,providerDef)),providerInstance}return data._parent.get(depDef.token,notFoundValue)}function _createProviderInstance$1(ngModule,providerDef){var injectable;switch(201347067&providerDef.flags){case 512:injectable=_createClass(ngModule,providerDef.value,providerDef.deps);break;case 1024:injectable=_callFactory(ngModule,providerDef.value,providerDef.deps);break;case 2048:injectable=resolveNgModuleDep(ngModule,providerDef.deps[0]);break;case 256:injectable=providerDef.value}return injectable}function _createClass(ngModule,ctor,deps){var injectable,len=deps.length;switch(len){case 0:injectable=new ctor;break;case 1:injectable=new ctor(resolveNgModuleDep(ngModule,deps[0]));break;case 2:injectable=new ctor(resolveNgModuleDep(ngModule,deps[0]),resolveNgModuleDep(ngModule,deps[1]));break;case 3:injectable=new ctor(resolveNgModuleDep(ngModule,deps[0]),resolveNgModuleDep(ngModule,deps[1]),resolveNgModuleDep(ngModule,deps[2]));break;default:for(var depValues=new Array(len),i=0;i<len;i++)depValues[i]=resolveNgModuleDep(ngModule,deps[i]);injectable=new(ctor.bind.apply(ctor,[void 0].concat(depValues)))}return injectable}function _callFactory(ngModule,factory,deps){var injectable,len=deps.length;switch(len){case 0:injectable=factory();break;case 1:injectable=factory(resolveNgModuleDep(ngModule,deps[0]));break;case 2:injectable=factory(resolveNgModuleDep(ngModule,deps[0]),resolveNgModuleDep(ngModule,deps[1]));break;case 3:injectable=factory(resolveNgModuleDep(ngModule,deps[0]),resolveNgModuleDep(ngModule,deps[1]),resolveNgModuleDep(ngModule,deps[2]));break;default:for(var depValues=Array(len),i=0;i<len;i++)depValues[i]=resolveNgModuleDep(ngModule,deps[i]);injectable=factory.apply(void 0,depValues)}return injectable}function callNgModuleLifecycle(ngModule,lifecycles){for(var def=ngModule._def,i=0;i<def.providers.length;i++){var provDef=def.providers[i];if(131072&provDef.flags){var instance=ngModule._providers[i];instance&&instance!==NOT_CREATED$1&&instance.ngOnDestroy()}}}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function attachEmbeddedView(parentView,elementData,viewIndex,view){var embeddedViews=elementData.viewContainer._embeddedViews;null!==viewIndex&&void 0!==viewIndex||(viewIndex=embeddedViews.length),view.viewContainerParent=parentView,addToArray(embeddedViews,viewIndex,view),attachProjectedView(elementData,view),Services.dirtyParentQueries(view);var prevView=viewIndex>0?embeddedViews[viewIndex-1]:null;renderAttachEmbeddedView(elementData,prevView,view)}function attachProjectedView(vcElementData,view){var dvcElementData=declaredViewContainer(view);if(dvcElementData&&dvcElementData!==vcElementData&&!(16&view.state)){view.state|=16;var projectedViews=dvcElementData.template._projectedViews;projectedViews||(projectedViews=dvcElementData.template._projectedViews=[]),projectedViews.push(view),markNodeAsProjectedTemplate(view.parent.def,view.parentNodeDef)}}function markNodeAsProjectedTemplate(viewDef,nodeDef){if(!(4&nodeDef.flags)){viewDef.nodeFlags|=4,nodeDef.flags|=4;for(var parentNodeDef=nodeDef.parent;parentNodeDef;)parentNodeDef.childFlags|=4,parentNodeDef=parentNodeDef.parent}}function detachEmbeddedView(elementData,viewIndex){var embeddedViews=elementData.viewContainer._embeddedViews;if((null==viewIndex||viewIndex>=embeddedViews.length)&&(viewIndex=embeddedViews.length-1),viewIndex<0)return null;var view=embeddedViews[viewIndex];return view.viewContainerParent=null,removeFromArray(embeddedViews,viewIndex),Services.dirtyParentQueries(view),renderDetachView(view),view}function detachProjectedView(view){if(16&view.state){var dvcElementData=declaredViewContainer(view);if(dvcElementData){var projectedViews=dvcElementData.template._projectedViews;projectedViews&&(removeFromArray(projectedViews,projectedViews.indexOf(view)),Services.dirtyParentQueries(view))}}}function moveEmbeddedView(elementData,oldViewIndex,newViewIndex){var embeddedViews=elementData.viewContainer._embeddedViews,view=embeddedViews[oldViewIndex];removeFromArray(embeddedViews,oldViewIndex),null==newViewIndex&&(newViewIndex=embeddedViews.length),addToArray(embeddedViews,newViewIndex,view),Services.dirtyParentQueries(view),renderDetachView(view);var prevView=newViewIndex>0?embeddedViews[newViewIndex-1]:null;return renderAttachEmbeddedView(elementData,prevView,view),view}function renderAttachEmbeddedView(elementData,prevView,view){var prevRenderNode=prevView?renderNode(prevView,prevView.def.lastRenderRootNode):elementData.renderElement,parentNode=view.renderer.parentNode(prevRenderNode),nextSibling=view.renderer.nextSibling(prevRenderNode);visitRootRenderNodes(view,2,parentNode,nextSibling,void 0)}function renderDetachView(view){visitRootRenderNodes(view,3,null,null,void 0)}function addToArray(arr,index,value){index>=arr.length?arr.push(value):arr.splice(index,0,value)}function removeFromArray(arr,index){index>=arr.length-1?arr.pop():arr.splice(index,1)}function createComponentFactory(selector,componentType,viewDefFactory,inputs,outputs,ngContentSelectors){return new ComponentFactory_(selector,componentType,viewDefFactory,inputs,outputs,ngContentSelectors)}function getComponentViewDefinitionFactory(componentFactory){return componentFactory.viewDefFactory}function createViewContainerData(view,elDef,elData){return new ViewContainerRef_(view,elDef,elData)}function createChangeDetectorRef(view){return new ViewRef_(view)}function createTemplateData(view,def){return new TemplateRef_(view,def)}function createInjector(view,elDef){return new Injector_(view,elDef)}function nodeValue(view,index){var def=view.def.nodes[index];if(1&def.flags){var elData=asElementData(view,def.index);return def.element.template?elData.template:elData.renderElement}if(2&def.flags)return asTextData(view,def.index).renderText;if(20240&def.flags)return asProviderData(view,def.index).instance;throw new Error("Illegal state: read nodeValue for node index "+index)}function createRendererV1(view){return new RendererAdapter(view.renderer)}function createNgModuleRef(moduleType,parent,bootstrapComponents,def){return new NgModuleRef_(moduleType,parent,bootstrapComponents,def)}function directiveDef(flags,matchedQueries,childCount,ctor,deps,props,outputs){var bindings=[];if(props)for(var prop in props){var _a=props[prop],bindingIndex=_a[0],nonMinifiedName=_a[1];bindings[bindingIndex]={flags:8,name:prop,nonMinifiedName:nonMinifiedName,ns:null,securityContext:null,suffix:null}}var outputDefs=[];if(outputs)for(var propName in outputs)outputDefs.push({type:1,propName:propName,target:null,eventName:outputs[propName]});return flags|=16384,_def(flags,matchedQueries,childCount,ctor,ctor,deps,bindings,outputDefs)}function pipeDef(flags,ctor,deps){return flags|=16,_def(flags,null,0,ctor,ctor,deps)}function providerDef(flags,matchedQueries,token,value,deps){return _def(flags,matchedQueries,0,token,value,deps)}function _def(flags,matchedQueriesDsl,childCount,token,value,deps,bindings,outputs){var _a=splitMatchedQueriesDsl(matchedQueriesDsl),matchedQueries=_a.matchedQueries,references=_a.references,matchedQueryIds=_a.matchedQueryIds;outputs||(outputs=[]),bindings||(bindings=[]);var depDefs=splitDepsDsl(deps);return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:matchedQueries,matchedQueryIds:matchedQueryIds,references:references,ngContentIndex:-1,childCount:childCount,bindings:bindings,bindingFlags:calcBindingFlags(bindings),outputs:outputs,element:null,provider:{token:token,value:value,deps:depDefs},text:null,query:null,ngContent:null}}function createProviderInstance(view,def){return 4096&def.flags?NOT_CREATED:_createProviderInstance(view,def)}function createPipeInstance(view,def){for(var compView=view;compView.parent&&!isComponentView(compView);)compView=compView.parent;var allowPrivateServices=!0;return createClass(compView.parent,viewParentEl(compView),allowPrivateServices,def.provider.value,def.provider.deps)}function createDirectiveInstance(view,def){var allowPrivateServices=(32768&def.flags)>0,instance=createClass(view,def.parent,allowPrivateServices,def.provider.value,def.provider.deps);if(def.outputs.length)for(var i=0;i<def.outputs.length;i++){var output=def.outputs[i],subscription=instance[output.propName].subscribe(eventHandlerClosure(view,def.parent.index,output.eventName));view.disposables[def.outputIndex+i]=subscription.unsubscribe.bind(subscription)}return instance}function eventHandlerClosure(view,index,eventName){return function(event){try{return dispatchEvent(view,index,eventName,event)}catch(e){view.root.errorHandler.handleError(e)}}}function checkAndUpdateDirectiveInline(view,def,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var providerData=asProviderData(view,def.index),directive=providerData.instance,changed=!1,changes=void 0,bindLen=def.bindings.length;return bindLen>0&&checkBinding(view,def,0,v0)&&(changed=!0,changes=updateProp(view,providerData,def,0,v0,changes)),bindLen>1&&checkBinding(view,def,1,v1)&&(changed=!0,changes=updateProp(view,providerData,def,1,v1,changes)),bindLen>2&&checkBinding(view,def,2,v2)&&(changed=!0,changes=updateProp(view,providerData,def,2,v2,changes)),bindLen>3&&checkBinding(view,def,3,v3)&&(changed=!0,changes=updateProp(view,providerData,def,3,v3,changes)),bindLen>4&&checkBinding(view,def,4,v4)&&(changed=!0,changes=updateProp(view,providerData,def,4,v4,changes)),bindLen>5&&checkBinding(view,def,5,v5)&&(changed=!0,changes=updateProp(view,providerData,def,5,v5,changes)),bindLen>6&&checkBinding(view,def,6,v6)&&(changed=!0,changes=updateProp(view,providerData,def,6,v6,changes)),bindLen>7&&checkBinding(view,def,7,v7)&&(changed=!0,changes=updateProp(view,providerData,def,7,v7,changes)),bindLen>8&&checkBinding(view,def,8,v8)&&(changed=!0,changes=updateProp(view,providerData,def,8,v8,changes)),bindLen>9&&checkBinding(view,def,9,v9)&&(changed=!0,changes=updateProp(view,providerData,def,9,v9,changes)),changes&&directive.ngOnChanges(changes),2&view.state&&65536&def.flags&&directive.ngOnInit(),262144&def.flags&&directive.ngDoCheck(),changed}function checkAndUpdateDirectiveDynamic(view,def,values){for(var providerData=asProviderData(view,def.index),directive=providerData.instance,changed=!1,changes=void 0,i=0;i<values.length;i++)checkBinding(view,def,i,values[i])&&(changed=!0,changes=updateProp(view,providerData,def,i,values[i],changes));return changes&&directive.ngOnChanges(changes),2&view.state&&65536&def.flags&&directive.ngOnInit(),262144&def.flags&&directive.ngDoCheck(),changed}function _createProviderInstance(view,def){var injectable,allowPrivateServices=(8192&def.flags)>0,providerDef=def.provider;switch(201347067&def.flags){case 512:injectable=createClass(view,def.parent,allowPrivateServices,providerDef.value,providerDef.deps);break;case 1024:injectable=callFactory(view,def.parent,allowPrivateServices,providerDef.value,providerDef.deps);break;case 2048:injectable=resolveDep(view,def.parent,allowPrivateServices,providerDef.deps[0]);break;case 256:injectable=providerDef.value}return injectable}function createClass(view,elDef,allowPrivateServices,ctor,deps){var injectable,len=deps.length;switch(len){case 0:injectable=new ctor;break;case 1:injectable=new ctor(resolveDep(view,elDef,allowPrivateServices,deps[0]));break;case 2:injectable=new ctor(resolveDep(view,elDef,allowPrivateServices,deps[0]),resolveDep(view,elDef,allowPrivateServices,deps[1]));break;case 3:injectable=new ctor(resolveDep(view,elDef,allowPrivateServices,deps[0]),resolveDep(view,elDef,allowPrivateServices,deps[1]),resolveDep(view,elDef,allowPrivateServices,deps[2]));break;default:for(var depValues=new Array(len),i=0;i<len;i++)depValues[i]=resolveDep(view,elDef,allowPrivateServices,deps[i]);injectable=new(ctor.bind.apply(ctor,[void 0].concat(depValues)))}return injectable}function callFactory(view,elDef,allowPrivateServices,factory,deps){var injectable,len=deps.length;switch(len){case 0:injectable=factory();break;case 1:injectable=factory(resolveDep(view,elDef,allowPrivateServices,deps[0]));break;case 2:injectable=factory(resolveDep(view,elDef,allowPrivateServices,deps[0]),resolveDep(view,elDef,allowPrivateServices,deps[1]));break;case 3:injectable=factory(resolveDep(view,elDef,allowPrivateServices,deps[0]),resolveDep(view,elDef,allowPrivateServices,deps[1]),resolveDep(view,elDef,allowPrivateServices,deps[2]));break;default:for(var depValues=Array(len),i=0;i<len;i++)depValues[i]=resolveDep(view,elDef,allowPrivateServices,deps[i]);injectable=factory.apply(void 0,depValues)}return injectable}function resolveDep(view,elDef,allowPrivateServices,depDef,notFoundValue){if(void 0===notFoundValue&&(notFoundValue=Injector.THROW_IF_NOT_FOUND),8&depDef.flags)return depDef.token;var startView=view;2&depDef.flags&&(notFoundValue=null);var tokenKey$$1=depDef.tokenKey;for(tokenKey$$1===ChangeDetectorRefTokenKey&&(allowPrivateServices=!(!elDef||!elDef.element.componentView)),elDef&&1&depDef.flags&&(allowPrivateServices=!1,elDef=elDef.parent);view;){if(elDef)switch(tokenKey$$1){case RendererV1TokenKey:var compView=findCompView(view,elDef,allowPrivateServices);return createRendererV1(compView);case Renderer2TokenKey:var compView=findCompView(view,elDef,allowPrivateServices);return compView.renderer;case ElementRefTokenKey:return new ElementRef(asElementData(view,elDef.index).renderElement);case ViewContainerRefTokenKey:return asElementData(view,elDef.index).viewContainer;case TemplateRefTokenKey:if(elDef.element.template)return asElementData(view,elDef.index).template;break;case ChangeDetectorRefTokenKey:var cdView=findCompView(view,elDef,allowPrivateServices);return createChangeDetectorRef(cdView);case InjectorRefTokenKey:return createInjector(view,elDef);default:var providerDef_1=(allowPrivateServices?elDef.element.allProviders:elDef.element.publicProviders)[tokenKey$$1];if(providerDef_1){var providerData=asProviderData(view,providerDef_1.index);return providerData.instance===NOT_CREATED&&(providerData.instance=_createProviderInstance(view,providerDef_1)),providerData.instance}}allowPrivateServices=isComponentView(view),elDef=viewParentEl(view),view=view.parent}var value=startView.root.injector.get(depDef.token,NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR);return value!==NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR||notFoundValue===NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR?value:startView.root.ngModule.injector.get(depDef.token,notFoundValue)}function findCompView(view,elDef,allowPrivateServices){var compView;if(allowPrivateServices)compView=asElementData(view,elDef.index).componentView;else for(compView=view;compView.parent&&!isComponentView(compView);)compView=compView.parent;return compView}function updateProp(view,providerData,def,bindingIdx,value,changes){if(32768&def.flags){var compView=asElementData(view,def.parent.index).componentView;2&compView.def.flags&&(compView.state|=8)}var binding=def.bindings[bindingIdx],propName=binding.name;if(providerData.instance[propName]=value,524288&def.flags){changes=changes||{};var oldValue=view.oldValues[def.bindingIndex+bindingIdx];oldValue instanceof WrappedValue&&(oldValue=oldValue.wrapped);var binding_1=def.bindings[bindingIdx];changes[binding_1.nonMinifiedName]=new SimpleChange(oldValue,value,0!==(2&view.state))}return view.oldValues[def.bindingIndex+bindingIdx]=value,changes}function callLifecycleHooksChildrenFirst(view,lifecycles){if(view.def.nodeFlags&lifecycles)for(var nodes=view.def.nodes,i=0;i<nodes.length;i++){var nodeDef=nodes[i],parent=nodeDef.parent;for(!parent&&nodeDef.flags&lifecycles&&callProviderLifecycles(view,i,nodeDef.flags&lifecycles),0===(nodeDef.childFlags&lifecycles)&&(i+=nodeDef.childCount);parent&&1&parent.flags&&i===parent.index+parent.childCount;)parent.directChildFlags&lifecycles&&callElementProvidersLifecycles(view,parent,lifecycles),parent=parent.parent}}function callElementProvidersLifecycles(view,elDef,lifecycles){for(var i=elDef.index+1;i<=elDef.index+elDef.childCount;i++){var nodeDef=view.def.nodes[i];nodeDef.flags&lifecycles&&callProviderLifecycles(view,i,nodeDef.flags&lifecycles),i+=nodeDef.childCount}}function callProviderLifecycles(view,index,lifecycles){var provider=asProviderData(view,index).instance;provider!==NOT_CREATED&&(Services.setCurrentNode(view,index),1048576&lifecycles&&provider.ngAfterContentInit(),2097152&lifecycles&&provider.ngAfterContentChecked(),4194304&lifecycles&&provider.ngAfterViewInit(),8388608&lifecycles&&provider.ngAfterViewChecked(),131072&lifecycles&&provider.ngOnDestroy())}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function queryDef(flags,id,bindings){var bindingDefs=[];for(var propName in bindings){var bindingType=bindings[propName];bindingDefs.push({propName:propName,bindingType:bindingType})}return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,ngContentIndex:-1,matchedQueries:{},matchedQueryIds:0,references:{},childCount:0,bindings:[],bindingFlags:0,outputs:[],element:null,provider:null,text:null,query:{id:id,filterId:filterQueryId(id),bindings:bindingDefs},ngContent:null}}function createQuery(){return new QueryList}function dirtyParentQueries(view){for(var queryIds=view.def.nodeMatchedQueries;view.parent&&isEmbeddedView(view);){var tplDef=view.parentNodeDef;view=view.parent;for(var end=tplDef.index+tplDef.childCount,i=0;i<=end;i++){var nodeDef=view.def.nodes[i];67108864&nodeDef.flags&&536870912&nodeDef.flags&&(nodeDef.query.filterId&queryIds)===nodeDef.query.filterId&&asQueryList(view,i).setDirty(),!(1&nodeDef.flags&&i+nodeDef.childCount<tplDef.index)&&67108864&nodeDef.childFlags&&536870912&nodeDef.childFlags||(i+=nodeDef.childCount)}}if(134217728&view.def.nodeFlags)for(var i=0;i<view.def.nodes.length;i++){var nodeDef=view.def.nodes[i];134217728&nodeDef.flags&&536870912&nodeDef.flags&&asQueryList(view,i).setDirty(),i+=nodeDef.childCount}}function checkAndUpdateQuery(view,nodeDef){var queryList=asQueryList(view,nodeDef.index);if(queryList.dirty){var directiveInstance,newValues=void 0;if(67108864&nodeDef.flags){var elementDef_1=nodeDef.parent.parent;newValues=calcQueryValues(view,elementDef_1.index,elementDef_1.index+elementDef_1.childCount,nodeDef.query,[]),directiveInstance=asProviderData(view,nodeDef.parent.index).instance}else 134217728&nodeDef.flags&&(newValues=calcQueryValues(view,0,view.def.nodes.length-1,nodeDef.query,[]),directiveInstance=view.component);queryList.reset(newValues);for(var bindings=nodeDef.query.bindings,notify=!1,i=0;i<bindings.length;i++){var binding=bindings[i],boundValue=void 0;switch(binding.bindingType){case 0:boundValue=queryList.first;break;case 1:boundValue=queryList,notify=!0}directiveInstance[binding.propName]=boundValue}notify&&queryList.notifyOnChanges()}}function calcQueryValues(view,startIndex,endIndex,queryDef,values){for(var i=startIndex;i<=endIndex;i++){var nodeDef=view.def.nodes[i],valueType=nodeDef.matchedQueries[queryDef.id];if(null!=valueType&&values.push(getQueryValue(view,nodeDef,valueType)),1&nodeDef.flags&&nodeDef.element.template&&(nodeDef.element.template.nodeMatchedQueries&queryDef.filterId)===queryDef.filterId){var elementData=asElementData(view,i);if(16777216&nodeDef.flags)for(var embeddedViews=elementData.viewContainer._embeddedViews,k=0;k<embeddedViews.length;k++){var embeddedView=embeddedViews[k],dvc=declaredViewContainer(embeddedView);dvc&&dvc===elementData&&calcQueryValues(embeddedView,0,embeddedView.def.nodes.length-1,queryDef,values)}var projectedViews=elementData.template._projectedViews;if(projectedViews)for(var k=0;k<projectedViews.length;k++){var projectedView=projectedViews[k];calcQueryValues(projectedView,0,projectedView.def.nodes.length-1,queryDef,values)}}(nodeDef.childMatchedQueries&queryDef.filterId)!==queryDef.filterId&&(i+=nodeDef.childCount)}return values}function getQueryValue(view,nodeDef,queryValueType){if(null!=queryValueType){var value=void 0;switch(queryValueType){case 1:value=asElementData(view,nodeDef.index).renderElement;break;case 0:value=new ElementRef(asElementData(view,nodeDef.index).renderElement);break;case 2:value=asElementData(view,nodeDef.index).template;break;case 3:value=asElementData(view,nodeDef.index).viewContainer;break;case 4:value=asProviderData(view,nodeDef.index).instance}return value}}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function ngContentDef(ngContentIndex,index){return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:8,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:ngContentIndex,childCount:0,bindings:[],bindingFlags:0,outputs:[],element:null,provider:null,text:null,query:null,ngContent:{index:index}}}function appendNgContent(view,renderHost,def){var parentEl=getParentRenderElement(view,renderHost,def);if(parentEl){var ngContentIndex=def.ngContent.index;visitProjectedRenderNodes(view,ngContentIndex,1,parentEl,null,void 0)}}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function purePipeDef(argCount){return _pureExpressionDef(128,new Array(argCount+1))}function pureArrayDef(argCount){return _pureExpressionDef(32,new Array(argCount))}function pureObjectDef(propertyNames){return _pureExpressionDef(64,propertyNames)}function _pureExpressionDef(flags,propertyNames){for(var bindings=new Array(propertyNames.length),i=0;i<propertyNames.length;i++){var prop=propertyNames[i];bindings[i]={flags:8,name:prop,ns:null,nonMinifiedName:prop,securityContext:null,suffix:null}}return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:-1,childCount:0,bindings:bindings,bindingFlags:calcBindingFlags(bindings),outputs:[],element:null,provider:null,text:null,query:null,ngContent:null}}function createPureExpression(view,def){return{value:void 0}}function checkAndUpdatePureExpressionInline(view,def,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var bindings=def.bindings,changed=!1,bindLen=bindings.length;if(bindLen>0&&checkAndUpdateBinding(view,def,0,v0)&&(changed=!0),bindLen>1&&checkAndUpdateBinding(view,def,1,v1)&&(changed=!0),bindLen>2&&checkAndUpdateBinding(view,def,2,v2)&&(changed=!0),bindLen>3&&checkAndUpdateBinding(view,def,3,v3)&&(changed=!0),bindLen>4&&checkAndUpdateBinding(view,def,4,v4)&&(changed=!0),bindLen>5&&checkAndUpdateBinding(view,def,5,v5)&&(changed=!0),bindLen>6&&checkAndUpdateBinding(view,def,6,v6)&&(changed=!0),bindLen>7&&checkAndUpdateBinding(view,def,7,v7)&&(changed=!0),bindLen>8&&checkAndUpdateBinding(view,def,8,v8)&&(changed=!0),bindLen>9&&checkAndUpdateBinding(view,def,9,v9)&&(changed=!0),changed){var data=asPureExpressionData(view,def.index),value=void 0;switch(201347067&def.flags){case 32:value=new Array(bindings.length),bindLen>0&&(value[0]=v0),bindLen>1&&(value[1]=v1),bindLen>2&&(value[2]=v2),bindLen>3&&(value[3]=v3),bindLen>4&&(value[4]=v4),bindLen>5&&(value[5]=v5),bindLen>6&&(value[6]=v6),bindLen>7&&(value[7]=v7),bindLen>8&&(value[8]=v8),bindLen>9&&(value[9]=v9);break;case 64:value={},bindLen>0&&(value[bindings[0].name]=v0),bindLen>1&&(value[bindings[1].name]=v1),bindLen>2&&(value[bindings[2].name]=v2),bindLen>3&&(value[bindings[3].name]=v3),bindLen>4&&(value[bindings[4].name]=v4),bindLen>5&&(value[bindings[5].name]=v5),bindLen>6&&(value[bindings[6].name]=v6),bindLen>7&&(value[bindings[7].name]=v7),bindLen>8&&(value[bindings[8].name]=v8),bindLen>9&&(value[bindings[9].name]=v9);break;case 128:var pipe=v0;switch(bindLen){case 1:value=pipe.transform(v0);break;case 2:value=pipe.transform(v1);break;case 3:value=pipe.transform(v1,v2);break;case 4:value=pipe.transform(v1,v2,v3);break;case 5:value=pipe.transform(v1,v2,v3,v4);break;case 6:value=pipe.transform(v1,v2,v3,v4,v5);break;case 7:value=pipe.transform(v1,v2,v3,v4,v5,v6);break;case 8:value=pipe.transform(v1,v2,v3,v4,v5,v6,v7);break;case 9:value=pipe.transform(v1,v2,v3,v4,v5,v6,v7,v8);break;case 10:value=pipe.transform(v1,v2,v3,v4,v5,v6,v7,v8,v9)}}data.value=value}return changed}function checkAndUpdatePureExpressionDynamic(view,def,values){for(var bindings=def.bindings,changed=!1,i=0;i<values.length;i++)checkAndUpdateBinding(view,def,i,values[i])&&(changed=!0);if(changed){var data=asPureExpressionData(view,def.index),value=void 0;switch(201347067&def.flags){case 32:value=values;break;case 64:value={};for(var i=0;i<values.length;i++)value[bindings[i].name]=values[i];break;case 128:var pipe=values[0],params=values.slice(1);value=pipe.transform.apply(pipe,params)}data.value=value}return changed}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function textDef(ngContentIndex,constants){for(var bindings=new Array(constants.length-1),i=1;i<constants.length;i++)bindings[i-1]={flags:8,name:null,ns:null,nonMinifiedName:null,securityContext:null,suffix:constants[i]};var flags=2;return{index:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:flags,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:ngContentIndex,childCount:0,bindings:bindings,bindingFlags:calcBindingFlags(bindings),outputs:[],element:null,provider:null,text:{prefix:constants[0]},query:null,ngContent:null}}function createText(view,renderHost,def){var renderNode$$1,renderer=view.renderer;renderNode$$1=renderer.createText(def.text.prefix);var parentEl=getParentRenderElement(view,renderHost,def);return parentEl&&renderer.appendChild(parentEl,renderNode$$1),{renderText:renderNode$$1}}function checkAndUpdateTextInline(view,def,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var changed=!1,bindings=def.bindings,bindLen=bindings.length;if(bindLen>0&&checkAndUpdateBinding(view,def,0,v0)&&(changed=!0),bindLen>1&&checkAndUpdateBinding(view,def,1,v1)&&(changed=!0),bindLen>2&&checkAndUpdateBinding(view,def,2,v2)&&(changed=!0),bindLen>3&&checkAndUpdateBinding(view,def,3,v3)&&(changed=!0),bindLen>4&&checkAndUpdateBinding(view,def,4,v4)&&(changed=!0),bindLen>5&&checkAndUpdateBinding(view,def,5,v5)&&(changed=!0),bindLen>6&&checkAndUpdateBinding(view,def,6,v6)&&(changed=!0),bindLen>7&&checkAndUpdateBinding(view,def,7,v7)&&(changed=!0),bindLen>8&&checkAndUpdateBinding(view,def,8,v8)&&(changed=!0),bindLen>9&&checkAndUpdateBinding(view,def,9,v9)&&(changed=!0),changed){var value=def.text.prefix;bindLen>0&&(value+=_addInterpolationPart(v0,bindings[0])),bindLen>1&&(value+=_addInterpolationPart(v1,bindings[1])),bindLen>2&&(value+=_addInterpolationPart(v2,bindings[2])),bindLen>3&&(value+=_addInterpolationPart(v3,bindings[3])),bindLen>4&&(value+=_addInterpolationPart(v4,bindings[4])),bindLen>5&&(value+=_addInterpolationPart(v5,bindings[5])),bindLen>6&&(value+=_addInterpolationPart(v6,bindings[6])),bindLen>7&&(value+=_addInterpolationPart(v7,bindings[7])),bindLen>8&&(value+=_addInterpolationPart(v8,bindings[8])),bindLen>9&&(value+=_addInterpolationPart(v9,bindings[9]));var renderNode$$1=asTextData(view,def.index).renderText;view.renderer.setValue(renderNode$$1,value)}return changed}function checkAndUpdateTextDynamic(view,def,values){for(var bindings=def.bindings,changed=!1,i=0;i<values.length;i++)checkAndUpdateBinding(view,def,i,values[i])&&(changed=!0);if(changed){for(var value="",i=0;i<values.length;i++)value+=_addInterpolationPart(values[i],bindings[i]);value=def.text.prefix+value;var renderNode$$1=asTextData(view,def.index).renderText;view.renderer.setValue(renderNode$$1,value)}return changed}function _addInterpolationPart(value,binding){var valueStr=null!=value?value.toString():"";return valueStr+binding.suffix}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function viewDef(flags,nodes,updateDirectives,updateRenderer){for(var viewBindingCount=0,viewDisposableCount=0,viewNodeFlags=0,viewRootNodeFlags=0,viewMatchedQueries=0,currentParent=null,currentElementHasPublicProviders=!1,currentElementHasPrivateProviders=!1,lastRenderRootNode=null,i=0;i<nodes.length;i++){for(;currentParent&&i>currentParent.index+currentParent.childCount;){var newParent=currentParent.parent;newParent&&(newParent.childFlags|=currentParent.childFlags,newParent.childMatchedQueries|=currentParent.childMatchedQueries),currentParent=newParent}var node=nodes[i];node.index=i,node.parent=currentParent,node.bindingIndex=viewBindingCount,node.outputIndex=viewDisposableCount;var currentRenderParent=void 0;if(currentRenderParent=currentParent&&1&currentParent.flags&&!currentParent.element.name?currentParent.renderParent:currentParent,node.renderParent=currentRenderParent,node.element){var elDef=node.element;elDef.publicProviders=currentParent?currentParent.element.publicProviders:Object.create(null),elDef.allProviders=elDef.publicProviders,currentElementHasPublicProviders=!1,currentElementHasPrivateProviders=!1}if(validateNode(currentParent,node,nodes.length),viewNodeFlags|=node.flags,viewMatchedQueries|=node.matchedQueryIds,node.element&&node.element.template&&(viewMatchedQueries|=node.element.template.nodeMatchedQueries),currentParent?(currentParent.childFlags|=node.flags,currentParent.directChildFlags|=node.flags,currentParent.childMatchedQueries|=node.matchedQueryIds,node.element&&node.element.template&&(currentParent.childMatchedQueries|=node.element.template.nodeMatchedQueries)):viewRootNodeFlags|=node.flags,viewBindingCount+=node.bindings.length,viewDisposableCount+=node.outputs.length,!currentRenderParent&&3&node.flags&&(lastRenderRootNode=node),20224&node.flags){currentElementHasPublicProviders||(currentElementHasPublicProviders=!0,currentParent.element.publicProviders=Object.create(currentParent.element.publicProviders),currentParent.element.allProviders=currentParent.element.publicProviders);var isPrivateService=0!==(8192&node.flags),isComponent=0!==(32768&node.flags);!isPrivateService||isComponent?currentParent.element.publicProviders[tokenKey(node.provider.token)]=node:(currentElementHasPrivateProviders||(currentElementHasPrivateProviders=!0,currentParent.element.allProviders=Object.create(currentParent.element.publicProviders)),currentParent.element.allProviders[tokenKey(node.provider.token)]=node),isComponent&&(currentParent.element.componentProvider=node)}node.childCount&&(currentParent=node)}for(;currentParent;){var newParent=currentParent.parent;newParent&&(newParent.childFlags|=currentParent.childFlags,newParent.childMatchedQueries|=currentParent.childMatchedQueries),currentParent=newParent}var handleEvent=function(view,nodeIndex,eventName,event){return nodes[nodeIndex].element.handleEvent(view,eventName,event)};return{factory:null,nodeFlags:viewNodeFlags,rootNodeFlags:viewRootNodeFlags,nodeMatchedQueries:viewMatchedQueries,flags:flags,nodes:nodes,updateDirectives:updateDirectives||NOOP,updateRenderer:updateRenderer||NOOP,handleEvent:handleEvent||NOOP,bindingCount:viewBindingCount,outputCount:viewDisposableCount,lastRenderRootNode:lastRenderRootNode}}function validateNode(parent,node,nodeCount){var template=node.element&&node.element.template;if(template){if(!template.lastRenderRootNode)throw new Error("Illegal State: Embedded templates without nodes are not allowed!");if(template.lastRenderRootNode&&16777216&template.lastRenderRootNode.flags)throw new Error("Illegal State: Last root node of a template can't have embedded views, at index "+node.index+"!")}if(20224&node.flags){var parentFlags=parent?parent.flags:0;if(0===(1&parentFlags))throw new Error("Illegal State: Provider/Directive nodes need to be children of elements or anchors, at index "+node.index+"!")}if(node.query){if(67108864&node.flags&&(!parent||0===(16384&parent.flags)))throw new Error("Illegal State: Content Query nodes need to be children of directives, at index "+node.index+"!");if(134217728&node.flags&&parent)throw new Error("Illegal State: View Query nodes have to be top level nodes, at index "+node.index+"!")}if(node.childCount){var parentEnd=parent?parent.index+parent.childCount:nodeCount-1;if(node.index<=parentEnd&&node.index+node.childCount>parentEnd)throw new Error("Illegal State: childCount of node leads outside of parent, at index "+node.index+"!")}}function createEmbeddedView(parent,anchorDef$$1,viewDef,context){var view=createView(parent.root,parent.renderer,parent,anchorDef$$1,viewDef);return initView(view,parent.component,context),createViewNodes(view),view}function createRootView(root,def,context){var view=createView(root,root.renderer,null,null,def);return initView(view,context,context),createViewNodes(view),view}function createComponentView(parentView,nodeDef,viewDef,hostElement){var compRenderer,rendererType=nodeDef.element.componentRendererType;return compRenderer=rendererType?parentView.root.rendererFactory.createRenderer(hostElement,rendererType):parentView.root.renderer,createView(parentView.root,compRenderer,parentView,nodeDef.element.componentProvider,viewDef)}function createView(root,renderer,parent,parentNodeDef,def){var nodes=new Array(def.nodes.length),disposables=def.outputCount?new Array(def.outputCount):null,view={def:def,parent:parent,viewContainerParent:null,parentNodeDef:parentNodeDef,context:null,component:null,nodes:nodes,state:13,root:root,renderer:renderer,oldValues:new Array(def.bindingCount),disposables:disposables};return view}function initView(view,component,context){view.component=component,view.context=context}function createViewNodes(view){var renderHost;if(isComponentView(view)){var hostDef=view.parentNodeDef;renderHost=asElementData(view.parent,hostDef.parent.index).renderElement}for(var def=view.def,nodes=view.nodes,i=0;i<def.nodes.length;i++){var nodeDef=def.nodes[i];Services.setCurrentNode(view,i);var nodeData=void 0;switch(201347067&nodeDef.flags){case 1:var el=createElement(view,renderHost,nodeDef),componentView=void 0;if(33554432&nodeDef.flags){var compViewDef=resolveDefinition(nodeDef.element.componentView);componentView=Services.createComponentView(view,nodeDef,compViewDef,el)}listenToElementOutputs(view,componentView,nodeDef,el),nodeData={renderElement:el,componentView:componentView,viewContainer:null,template:nodeDef.element.template?createTemplateData(view,nodeDef):void 0},16777216&nodeDef.flags&&(nodeData.viewContainer=createViewContainerData(view,nodeDef,nodeData));break;case 2:nodeData=createText(view,renderHost,nodeDef);break;case 512:case 1024:case 2048:case 256:var instance=createProviderInstance(view,nodeDef);nodeData={instance:instance};break;case 16:var instance=createPipeInstance(view,nodeDef);nodeData={instance:instance};break;case 16384:var instance=createDirectiveInstance(view,nodeDef);if(nodeData={instance:instance},32768&nodeDef.flags){var compView=asElementData(view,nodeDef.parent.index).componentView;initView(compView,instance,instance)}break;case 32:case 64:case 128:nodeData=createPureExpression(view,nodeDef);break;case 67108864:case 134217728:nodeData=createQuery();break;case 8:appendNgContent(view,renderHost,nodeDef),nodeData=void 0}nodes[i]=nodeData}execComponentViewsAction(view,ViewAction.CreateViewNodes),execQueriesAction(view,201326592,268435456,0)}function checkNoChangesView(view){markProjectedViewsForCheck(view),Services.updateDirectives(view,1),execEmbeddedViewsAction(view,ViewAction.CheckNoChanges),Services.updateRenderer(view,1),execComponentViewsAction(view,ViewAction.CheckNoChanges),view.state&=-97}function checkAndUpdateView(view){1&view.state?(view.state&=-2,view.state|=2):view.state&=-3,markProjectedViewsForCheck(view),Services.updateDirectives(view,0),execEmbeddedViewsAction(view,ViewAction.CheckAndUpdate),execQueriesAction(view,67108864,536870912,0),callLifecycleHooksChildrenFirst(view,2097152|(2&view.state?1048576:0)),Services.updateRenderer(view,0),execComponentViewsAction(view,ViewAction.CheckAndUpdate),execQueriesAction(view,134217728,536870912,0),callLifecycleHooksChildrenFirst(view,8388608|(2&view.state?4194304:0)),2&view.def.flags&&(view.state&=-9),view.state&=-97}function checkAndUpdateNode(view,nodeDef,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){return 0===argStyle?checkAndUpdateNodeInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9):checkAndUpdateNodeDynamic(view,nodeDef,v0)}function markProjectedViewsForCheck(view){var def=view.def;if(4&def.nodeFlags)for(var i=0;i<def.nodes.length;i++){var nodeDef=def.nodes[i];if(4&nodeDef.flags){var projectedViews=asElementData(view,i).template._projectedViews;if(projectedViews)for(var i_1=0;i_1<projectedViews.length;i_1++){var projectedView=projectedViews[i_1];projectedView.state|=32,markParentViewsForCheckProjectedViews(projectedView,view)}}else 0===(4&nodeDef.childFlags)&&(i+=nodeDef.childCount)}}function checkAndUpdateNodeInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var changed=!1;switch(201347067&nodeDef.flags){case 1:changed=checkAndUpdateElementInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9);break;case 2:changed=checkAndUpdateTextInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9);break;case 16384:changed=checkAndUpdateDirectiveInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9);break;case 32:case 64:case 128:changed=checkAndUpdatePureExpressionInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9)}return changed}function checkAndUpdateNodeDynamic(view,nodeDef,values){var changed=!1;switch(201347067&nodeDef.flags){case 1:changed=checkAndUpdateElementDynamic(view,nodeDef,values);break;case 2:changed=checkAndUpdateTextDynamic(view,nodeDef,values);break;case 16384:changed=checkAndUpdateDirectiveDynamic(view,nodeDef,values);break;case 32:case 64:case 128:changed=checkAndUpdatePureExpressionDynamic(view,nodeDef,values)}if(changed)for(var bindLen=nodeDef.bindings.length,bindingStart=nodeDef.bindingIndex,oldValues=view.oldValues,i=0;i<bindLen;i++)oldValues[bindingStart+i]=values[i];return changed}function checkNoChangesNode(view,nodeDef,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){return 0===argStyle?checkNoChangesNodeInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9):checkNoChangesNodeDynamic(view,nodeDef,v0),!1}function checkNoChangesNodeInline(view,nodeDef,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var bindLen=nodeDef.bindings.length;bindLen>0&&checkBindingNoChanges(view,nodeDef,0,v0),bindLen>1&&checkBindingNoChanges(view,nodeDef,1,v1),bindLen>2&&checkBindingNoChanges(view,nodeDef,2,v2),bindLen>3&&checkBindingNoChanges(view,nodeDef,3,v3),bindLen>4&&checkBindingNoChanges(view,nodeDef,4,v4),bindLen>5&&checkBindingNoChanges(view,nodeDef,5,v5),bindLen>6&&checkBindingNoChanges(view,nodeDef,6,v6),bindLen>7&&checkBindingNoChanges(view,nodeDef,7,v7),bindLen>8&&checkBindingNoChanges(view,nodeDef,8,v8),bindLen>9&&checkBindingNoChanges(view,nodeDef,9,v9)}function checkNoChangesNodeDynamic(view,nodeDef,values){for(var i=0;i<values.length;i++)checkBindingNoChanges(view,nodeDef,i,values[i])}function checkNoChangesQuery(view,nodeDef){var queryList=asQueryList(view,nodeDef.index);if(queryList.dirty)throw expressionChangedAfterItHasBeenCheckedError(Services.createDebugContext(view,nodeDef.index),"Query "+nodeDef.query.id+" not dirty","Query "+nodeDef.query.id+" dirty",0!==(1&view.state))}function destroyView(view){if(!(128&view.state)){if(execEmbeddedViewsAction(view,ViewAction.Destroy),execComponentViewsAction(view,ViewAction.Destroy),callLifecycleHooksChildrenFirst(view,131072),view.disposables)for(var i=0;i<view.disposables.length;i++)view.disposables[i]();detachProjectedView(view),view.renderer.destroyNode&&destroyViewNodes(view),isComponentView(view)&&view.renderer.destroy(),view.state|=128}}function destroyViewNodes(view){for(var len=view.def.nodes.length,i=0;i<len;i++){var def=view.def.nodes[i];1&def.flags?view.renderer.destroyNode(asElementData(view,i).renderElement):2&def.flags&&view.renderer.destroyNode(asTextData(view,i).renderText)}}function execComponentViewsAction(view,action){var def=view.def;if(33554432&def.nodeFlags)for(var i=0;i<def.nodes.length;i++){var nodeDef=def.nodes[i];33554432&nodeDef.flags?callViewAction(asElementData(view,i).componentView,action):0===(33554432&nodeDef.childFlags)&&(i+=nodeDef.childCount)}}function execEmbeddedViewsAction(view,action){var def=view.def;if(16777216&def.nodeFlags)for(var i=0;i<def.nodes.length;i++){var nodeDef=def.nodes[i];if(16777216&nodeDef.flags)for(var embeddedViews=asElementData(view,i).viewContainer._embeddedViews,k=0;k<embeddedViews.length;k++)callViewAction(embeddedViews[k],action);else 0===(16777216&nodeDef.childFlags)&&(i+=nodeDef.childCount)}}function callViewAction(view,action){var viewState=view.state;switch(action){case ViewAction.CheckNoChanges:0===(128&viewState)&&(12===(12&viewState)?checkNoChangesView(view):64&viewState&&execProjectedViewsAction(view,ViewAction.CheckNoChangesProjectedViews));break;case ViewAction.CheckNoChangesProjectedViews:0===(128&viewState)&&(32&viewState?checkNoChangesView(view):64&viewState&&execProjectedViewsAction(view,action));break;case ViewAction.CheckAndUpdate:0===(128&viewState)&&(12===(12&viewState)?checkAndUpdateView(view):64&viewState&&execProjectedViewsAction(view,ViewAction.CheckAndUpdateProjectedViews));break;case ViewAction.CheckAndUpdateProjectedViews:0===(128&viewState)&&(32&viewState?checkAndUpdateView(view):64&viewState&&execProjectedViewsAction(view,action));break;case ViewAction.Destroy:destroyView(view);break;case ViewAction.CreateViewNodes:createViewNodes(view)}}function execProjectedViewsAction(view,action){execEmbeddedViewsAction(view,action),execComponentViewsAction(view,action)}function execQueriesAction(view,queryFlags,staticDynamicQueryFlag,checkType){if(view.def.nodeFlags&queryFlags&&view.def.nodeFlags&staticDynamicQueryFlag)for(var nodeCount=view.def.nodes.length,i=0;i<nodeCount;i++){var nodeDef=view.def.nodes[i];if(nodeDef.flags&queryFlags&&nodeDef.flags&staticDynamicQueryFlag)switch(Services.setCurrentNode(view,nodeDef.index),checkType){case 0:checkAndUpdateQuery(view,nodeDef);break;case 1:checkNoChangesQuery(view,nodeDef)}nodeDef.childFlags&queryFlags&&nodeDef.childFlags&staticDynamicQueryFlag||(i+=nodeDef.childCount)}}function initServicesIfNeeded(){if(!initialized){initialized=!0;var services=isDevMode()?createDebugServices():createProdServices();Services.setCurrentNode=services.setCurrentNode,Services.createRootView=services.createRootView,Services.createEmbeddedView=services.createEmbeddedView,Services.createComponentView=services.createComponentView,Services.createNgModuleRef=services.createNgModuleRef,Services.overrideProvider=services.overrideProvider,Services.clearProviderOverrides=services.clearProviderOverrides,Services.checkAndUpdateView=services.checkAndUpdateView,Services.checkNoChangesView=services.checkNoChangesView,Services.destroyView=services.destroyView,Services.resolveDep=resolveDep,Services.createDebugContext=services.createDebugContext,Services.handleEvent=services.handleEvent,Services.updateDirectives=services.updateDirectives,Services.updateRenderer=services.updateRenderer,Services.dirtyParentQueries=dirtyParentQueries}}function createProdServices(){return{setCurrentNode:function(){},createRootView:createProdRootView,createEmbeddedView:createEmbeddedView,createComponentView:createComponentView,createNgModuleRef:createNgModuleRef,overrideProvider:NOOP,clearProviderOverrides:NOOP,checkAndUpdateView:checkAndUpdateView,checkNoChangesView:checkNoChangesView,destroyView:destroyView,createDebugContext:function(view,nodeIndex){return new DebugContext_(view,nodeIndex)},handleEvent:function(view,nodeIndex,eventName,event){return view.def.handleEvent(view,nodeIndex,eventName,event)},updateDirectives:function(view,checkType){return view.def.updateDirectives(0===checkType?prodCheckAndUpdateNode:prodCheckNoChangesNode,view)},updateRenderer:function(view,checkType){return view.def.updateRenderer(0===checkType?prodCheckAndUpdateNode:prodCheckNoChangesNode,view)}}}function createDebugServices(){return{setCurrentNode:debugSetCurrentNode,createRootView:debugCreateRootView,createEmbeddedView:debugCreateEmbeddedView,createComponentView:debugCreateComponentView,createNgModuleRef:debugCreateNgModuleRef,overrideProvider:debugOverrideProvider,clearProviderOverrides:debugClearProviderOverrides,checkAndUpdateView:debugCheckAndUpdateView,checkNoChangesView:debugCheckNoChangesView,destroyView:debugDestroyView,createDebugContext:function(view,nodeIndex){return new DebugContext_(view,nodeIndex)},handleEvent:debugHandleEvent,updateDirectives:debugUpdateDirectives,updateRenderer:debugUpdateRenderer}}function createProdRootView(elInjector,projectableNodes,rootSelectorOrNode,def,ngModule,context){var rendererFactory=ngModule.injector.get(RendererFactory2);return createRootView(createRootData(elInjector,ngModule,rendererFactory,projectableNodes,rootSelectorOrNode),def,context)}function debugCreateRootView(elInjector,projectableNodes,rootSelectorOrNode,def,ngModule,context){var rendererFactory=ngModule.injector.get(RendererFactory2),root=createRootData(elInjector,ngModule,new DebugRendererFactory2(rendererFactory),projectableNodes,rootSelectorOrNode),defWithOverride=applyProviderOverridesToView(def);return callWithDebugContext(DebugAction.create,createRootView,null,[root,defWithOverride,context])}function createRootData(elInjector,ngModule,rendererFactory,projectableNodes,rootSelectorOrNode){var sanitizer=ngModule.injector.get(Sanitizer),errorHandler=ngModule.injector.get(ErrorHandler),renderer=rendererFactory.createRenderer(null,null);return{ngModule:ngModule,injector:elInjector,projectableNodes:projectableNodes,selectorOrNode:rootSelectorOrNode,sanitizer:sanitizer,rendererFactory:rendererFactory,renderer:renderer,errorHandler:errorHandler}}function debugCreateEmbeddedView(parentView,anchorDef,viewDef$$1,context){var defWithOverride=applyProviderOverridesToView(viewDef$$1);return callWithDebugContext(DebugAction.create,createEmbeddedView,null,[parentView,anchorDef,defWithOverride,context])}function debugCreateComponentView(parentView,nodeDef,viewDef$$1,hostElement){var defWithOverride=applyProviderOverridesToView(viewDef$$1);return callWithDebugContext(DebugAction.create,createComponentView,null,[parentView,nodeDef,defWithOverride,hostElement])}function debugCreateNgModuleRef(moduleType,parentInjector,bootstrapComponents,def){var defWithOverride=applyProviderOverridesToNgModule(def);return createNgModuleRef(moduleType,parentInjector,bootstrapComponents,defWithOverride)}function debugOverrideProvider(override){providerOverrides.set(override.token,override)}function debugClearProviderOverrides(){providerOverrides.clear()}function applyProviderOverridesToView(def){function findElementIndicesWithOverwrittenProviders(def){for(var elIndicesWithOverwrittenProviders=[],lastElementDef=null,i=0;i<def.nodes.length;i++){var nodeDef=def.nodes[i];1&nodeDef.flags&&(lastElementDef=nodeDef),lastElementDef&&3840&nodeDef.flags&&providerOverrides.has(nodeDef.provider.token)&&(elIndicesWithOverwrittenProviders.push(lastElementDef.index),lastElementDef=null)}return elIndicesWithOverwrittenProviders}function applyProviderOverridesToElement(viewDef$$1,elIndex){for(var i=elIndex+1;i<viewDef$$1.nodes.length;i++){var nodeDef=viewDef$$1.nodes[i];if(1&nodeDef.flags)return;if(3840&nodeDef.flags){nodeDef.flags|=4096;var provider=nodeDef.provider,override=providerOverrides.get(provider.token);override&&(nodeDef.flags=nodeDef.flags&-3841|override.flags,provider.deps=splitDepsDsl(override.deps),provider.value=override.value)}}}if(0===providerOverrides.size)return def;var elementIndicesWithOverwrittenProviders=findElementIndicesWithOverwrittenProviders(def);if(0===elementIndicesWithOverwrittenProviders.length)return def;def=def.factory(function(){return NOOP});for(var i=0;i<elementIndicesWithOverwrittenProviders.length;i++)applyProviderOverridesToElement(def,elementIndicesWithOverwrittenProviders[i]);return def}function applyProviderOverridesToNgModule(def){function hasOverrrides(def){return def.providers.some(function(node){return!!(3840&node.flags)&&providerOverrides.has(node.token)})}function applyProviderOverrides(def){for(var i=0;i<def.providers.length;i++){var provider=def.providers[i];provider.flags|=4096;var override=providerOverrides.get(provider.token);override&&(provider.flags=provider.flags&-3841|override.flags,provider.deps=splitDepsDsl(override.deps),provider.value=override.value)}}return 0!==providerOverrides.size&&hasOverrrides(def)?(def=def.factory(function(){return NOOP}),applyProviderOverrides(def),def):def}function prodCheckAndUpdateNode(view,nodeIndex,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var nodeDef=view.def.nodes[nodeIndex];return checkAndUpdateNode(view,nodeDef,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9),224&nodeDef.flags?asPureExpressionData(view,nodeIndex).value:void 0}function prodCheckNoChangesNode(view,nodeIndex,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9){var nodeDef=view.def.nodes[nodeIndex];return checkNoChangesNode(view,nodeDef,argStyle,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9),224&nodeDef.flags?asPureExpressionData(view,nodeIndex).value:void 0}function debugCheckAndUpdateView(view){return callWithDebugContext(DebugAction.detectChanges,checkAndUpdateView,null,[view])}function debugCheckNoChangesView(view){return callWithDebugContext(DebugAction.checkNoChanges,checkNoChangesView,null,[view])}function debugDestroyView(view){return callWithDebugContext(DebugAction.destroy,destroyView,null,[view])}function debugSetCurrentNode(view,nodeIndex){_currentView=view,_currentNodeIndex=nodeIndex}function debugHandleEvent(view,nodeIndex,eventName,event){return debugSetCurrentNode(view,nodeIndex),callWithDebugContext(DebugAction.handleEvent,view.def.handleEvent,null,[view,nodeIndex,eventName,event])}function debugUpdateDirectives(view,checkType){function debugCheckDirectivesFn(view,nodeIndex,argStyle){for(var values=[],_i=3;_i<arguments.length;_i++)values[_i-3]=arguments[_i];var nodeDef=view.def.nodes[nodeIndex];return 0===checkType?debugCheckAndUpdateNode(view,nodeDef,argStyle,values):debugCheckNoChangesNode(view,nodeDef,argStyle,values),16384&nodeDef.flags&&debugSetCurrentNode(view,nextDirectiveWithBinding(view,nodeIndex)),224&nodeDef.flags?asPureExpressionData(view,nodeDef.index).value:void 0}if(128&view.state)throw viewDestroyedError(DebugAction[_currentAction]);return debugSetCurrentNode(view,nextDirectiveWithBinding(view,0)),view.def.updateDirectives(debugCheckDirectivesFn,view)}function debugUpdateRenderer(view,checkType){function debugCheckRenderNodeFn(view,nodeIndex,argStyle){for(var values=[],_i=3;_i<arguments.length;_i++)values[_i-3]=arguments[_i];var nodeDef=view.def.nodes[nodeIndex];return 0===checkType?debugCheckAndUpdateNode(view,nodeDef,argStyle,values):debugCheckNoChangesNode(view,nodeDef,argStyle,values),3&nodeDef.flags&&debugSetCurrentNode(view,nextRenderNodeWithBinding(view,nodeIndex)),224&nodeDef.flags?asPureExpressionData(view,nodeDef.index).value:void 0}if(128&view.state)throw viewDestroyedError(DebugAction[_currentAction]);return debugSetCurrentNode(view,nextRenderNodeWithBinding(view,0)),view.def.updateRenderer(debugCheckRenderNodeFn,view)}function debugCheckAndUpdateNode(view,nodeDef,argStyle,givenValues){var changed=checkAndUpdateNode.apply(void 0,[view,nodeDef,argStyle].concat(givenValues));if(changed){var values=1===argStyle?givenValues[0]:givenValues;if(16384&nodeDef.flags){for(var bindingValues={},i=0;i<nodeDef.bindings.length;i++){var binding=nodeDef.bindings[i],value=values[i];8&binding.flags&&(bindingValues[normalizeDebugBindingName(binding.nonMinifiedName)]=normalizeDebugBindingValue(value))}var elDef=nodeDef.parent,el=asElementData(view,elDef.index).renderElement;if(elDef.element.name)for(var attr in bindingValues){var value=bindingValues[attr];null!=value?view.renderer.setAttribute(el,attr,value):view.renderer.removeAttribute(el,attr)}else view.renderer.setValue(el,"bindings="+JSON.stringify(bindingValues,null,2))}}}function debugCheckNoChangesNode(view,nodeDef,argStyle,values){checkNoChangesNode.apply(void 0,[view,nodeDef,argStyle].concat(values))}function normalizeDebugBindingName(name){return name=camelCaseToDashCase(name.replace(/[$@]/g,"_")),"ng-reflect-"+name}function camelCaseToDashCase(input){return input.replace(CAMEL_CASE_REGEXP,function(){for(var m=[],_i=0;_i<arguments.length;_i++)m[_i]=arguments[_i];return"-"+m[1].toLowerCase()})}function normalizeDebugBindingValue(value){try{return null!=value?value.toString().slice(0,30):value}catch(e){return"[ERROR] Exception while trying to serialize the value"}}function nextDirectiveWithBinding(view,nodeIndex){for(var i=nodeIndex;i<view.def.nodes.length;i++){var nodeDef=view.def.nodes[i];if(16384&nodeDef.flags&&nodeDef.bindings&&nodeDef.bindings.length)return i}return null}function nextRenderNodeWithBinding(view,nodeIndex){for(var i=nodeIndex;i<view.def.nodes.length;i++){var nodeDef=view.def.nodes[i];if(3&nodeDef.flags&&nodeDef.bindings&&nodeDef.bindings.length)return i}return null}function getRenderNodeIndex(viewDef$$1,nodeIndex){for(var renderNodeIndex=-1,i=0;i<=nodeIndex;i++){var nodeDef=viewDef$$1.nodes[i];3&nodeDef.flags&&renderNodeIndex++}return renderNodeIndex}function findHostElement(view){for(;view&&!isComponentView(view);)view=view.parent;return view.parent?asElementData(view.parent,viewParentEl(view).index):null}function collectReferences(view,nodeDef,references){for(var refName in nodeDef.references)references[refName]=getQueryValue(view,nodeDef,nodeDef.references[refName])}function callWithDebugContext(action,fn,self,args){var oldAction=_currentAction,oldView=_currentView,oldNodeIndex=_currentNodeIndex;try{_currentAction=action;var result=fn.apply(self,args);return _currentView=oldView,_currentNodeIndex=oldNodeIndex,_currentAction=oldAction,result}catch(e){if(isViewDebugError(e)||!_currentView)throw e;throw viewWrappedDebugError(e,getCurrentDebugContext())}}function getCurrentDebugContext(){return _currentView?new DebugContext_(_currentView,_currentNodeIndex):null}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function overrideProvider(override){return initServicesIfNeeded(),Services.overrideProvider(override)}function clearProviderOverrides(){return initServicesIfNeeded(),Services.clearProviderOverrides()}function createNgModuleFactory(ngModuleType,bootstrapComponents,defFactory){return new NgModuleFactory_(ngModuleType,bootstrapComponents,defFactory)}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function trigger$1(name,definitions){return{type:7,name:name,definitions:definitions,options:{}}}function animate$1(timings,styles){return void 0===styles&&(styles=null),{type:4,styles:styles,timings:timings}}function group$1(steps,options){return void 0===options&&(options=null),{type:3,steps:steps,options:options}}function sequence$1(steps,options){return void 0===options&&(options=null),{type:2,steps:steps,options:options}}function style$1(tokens){return{type:6,styles:tokens,offset:null}}function state$1(name,styles){return{type:0,name:name,styles:styles}}function keyframes$1(steps){return{type:5,steps:steps}}function transition$1(stateChangeExpr,steps,options){return void 0===options&&(options=null),{type:1,expr:stateChangeExpr,animation:steps,options:options}}function trigger$$1(name,definitions){return trigger$1(name,definitions)}function animate$$1(timings,styles){return animate$1(timings,styles)}function group$$1(steps){return group$1(steps)}function sequence$$1(steps){return sequence$1(steps)}function style$$1(tokens){return style$1(tokens)}function state$$1(name,styles){return state$1(name,styles)}function keyframes$$1(steps){return keyframes$1(steps)}function transition$$1(stateChangeExpr,steps){return transition$1(stateChangeExpr,steps)}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},OpaqueToken=function(){function OpaqueToken(_desc){this._desc=_desc}return OpaqueToken.prototype.toString=function(){return"Token "+this._desc},OpaqueToken}(),InjectionToken=function(_super){function InjectionToken(desc){return _super.call(this,desc)||this}return __extends(InjectionToken,_super),InjectionToken.prototype.toString=function(){return"InjectionToken "+this._desc},InjectionToken}(OpaqueToken),__window="undefined"!=typeof window&&window,__self="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,__global="undefined"!=typeof global&&global,_global=__window||__global||__self,_symbolIterator=null,_nextClassId=0,Reflect$1=_global.Reflect,ANALYZE_FOR_ENTRY_COMPONENTS=new InjectionToken("AnalyzeForEntryComponents"),Attribute=makeParamDecorator("Attribute",function(attributeName){return{attributeName:attributeName}}),Query=function(){function Query(){}return Query}(),ContentChildren=makePropDecorator("ContentChildren",function(selector,data){return void 0===data&&(data={}),Object.assign({selector:selector,first:!1,isViewQuery:!1,descendants:!1},data)},Query),ContentChild=makePropDecorator("ContentChild",function(selector,data){return void 0===data&&(data={}),Object.assign({selector:selector,first:!0,isViewQuery:!1,descendants:!0},data)},Query),ViewChildren=makePropDecorator("ViewChildren",function(selector,data){return void 0===data&&(data={}),Object.assign({selector:selector,first:!1,isViewQuery:!0,descendants:!0},data)},Query),ViewChild=makePropDecorator("ViewChild",function(selector,data){return Object.assign({selector:selector,first:!0,isViewQuery:!0,descendants:!0},data)},Query),ChangeDetectionStrategy={};ChangeDetectionStrategy.OnPush=0,ChangeDetectionStrategy.Default=1,ChangeDetectionStrategy[ChangeDetectionStrategy.OnPush]="OnPush",ChangeDetectionStrategy[ChangeDetectionStrategy.Default]="Default";var ChangeDetectorStatus={};ChangeDetectorStatus.CheckOnce=0,ChangeDetectorStatus.Checked=1,ChangeDetectorStatus.CheckAlways=2,ChangeDetectorStatus.Detached=3,ChangeDetectorStatus.Errored=4,ChangeDetectorStatus.Destroyed=5,ChangeDetectorStatus[ChangeDetectorStatus.CheckOnce]="CheckOnce",ChangeDetectorStatus[ChangeDetectorStatus.Checked]="Checked",ChangeDetectorStatus[ChangeDetectorStatus.CheckAlways]="CheckAlways",ChangeDetectorStatus[ChangeDetectorStatus.Detached]="Detached",ChangeDetectorStatus[ChangeDetectorStatus.Errored]="Errored",ChangeDetectorStatus[ChangeDetectorStatus.Destroyed]="Destroyed";/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var Directive=makeDecorator("Directive",function(dir){return void 0===dir&&(dir={}),dir}),Component=makeDecorator("Component",function(c){return void 0===c&&(c={}),Object.assign({changeDetection:ChangeDetectionStrategy.Default},c)},Directive),Pipe=makeDecorator("Pipe",function(p){return Object.assign({pure:!0},p)}),Input=makePropDecorator("Input",function(bindingPropertyName){return{bindingPropertyName:bindingPropertyName}}),Output=makePropDecorator("Output",function(bindingPropertyName){return{bindingPropertyName:bindingPropertyName}}),HostBinding=makePropDecorator("HostBinding",function(hostPropertyName){return{hostPropertyName:hostPropertyName}}),HostListener=makePropDecorator("HostListener",function(eventName,args){return{eventName:eventName,args:args}}),CUSTOM_ELEMENTS_SCHEMA={name:"custom-elements"},NO_ERRORS_SCHEMA={name:"no-errors-schema"},NgModule=makeDecorator("NgModule",function(ngModule){return ngModule}),ViewEncapsulation={};ViewEncapsulation.Emulated=0,ViewEncapsulation.Native=1,ViewEncapsulation.None=2,ViewEncapsulation[ViewEncapsulation.Emulated]="Emulated",ViewEncapsulation[ViewEncapsulation.Native]="Native",ViewEncapsulation[ViewEncapsulation.None]="None";var ViewMetadata=function(){function ViewMetadata(opts){void 0===opts&&(opts={}),this.templateUrl=opts.templateUrl,this.template=opts.template,this.styleUrls=opts.styleUrls,this.styles=opts.styles,this.encapsulation=opts.encapsulation,this.animations=opts.animations,this.interpolation=opts.interpolation}return ViewMetadata}(),Version=function(){function Version(full){this.full=full}return Object.defineProperty(Version.prototype,"major",{get:function(){return this.full.split(".")[0]},enumerable:!0,configurable:!0}),Object.defineProperty(Version.prototype,"minor",{get:function(){return this.full.split(".")[1]},enumerable:!0,configurable:!0}),Object.defineProperty(Version.prototype,"patch",{get:function(){return this.full.split(".").slice(2).join(".")},enumerable:!0,configurable:!0}),Version}(),VERSION=new Version("4.2.5"),Inject=makeParamDecorator("Inject",function(token){return{token:token}}),Optional=makeParamDecorator("Optional"),Injectable=makeDecorator("Injectable"),Self=makeParamDecorator("Self"),SkipSelf=makeParamDecorator("SkipSelf"),Host=makeParamDecorator("Host"),_THROW_IF_NOT_FOUND=new Object,THROW_IF_NOT_FOUND=_THROW_IF_NOT_FOUND,_NullInjector=function(){function _NullInjector(){}return _NullInjector.prototype.get=function(token,notFoundValue){if(void 0===notFoundValue&&(notFoundValue=_THROW_IF_NOT_FOUND),notFoundValue===_THROW_IF_NOT_FOUND)throw new Error("No provider for "+stringify(token)+"!");return notFoundValue},_NullInjector}(),Injector=function(){function Injector(){}return Injector.prototype.get=function(token,notFoundValue){},Injector.prototype.get=function(token,notFoundValue){},Injector}();Injector.THROW_IF_NOT_FOUND=_THROW_IF_NOT_FOUND,Injector.NULL=new _NullInjector;/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var ERROR_COMPONENT_TYPE="ngComponentType",ERROR_DEBUG_CONTEXT="ngDebugContext",ERROR_ORIGINAL_ERROR="ngOriginalError",ERROR_LOGGER="ngErrorLogger",ErrorHandler=function(){function ErrorHandler(deprecatedParameter){this._console=console}return ErrorHandler.prototype.handleError=function(error){var originalError=this._findOriginalError(error),context=this._findContext(error),errorLogger=getErrorLogger(error);errorLogger(this._console,"ERROR",error),originalError&&errorLogger(this._console,"ORIGINAL ERROR",originalError),context&&errorLogger(this._console,"ERROR CONTEXT",context)},ErrorHandler.prototype._findContext=function(error){return error?getDebugContext(error)?getDebugContext(error):this._findContext(getOriginalError(error)):null},ErrorHandler.prototype._findOriginalError=function(error){for(var e=getOriginalError(error);e&&getOriginalError(e);)e=getOriginalError(e);return e},ErrorHandler}(),ReflectiveKey=function(){function ReflectiveKey(token,id){if(this.token=token,this.id=id,!token)throw new Error("Token must be defined!")}return Object.defineProperty(ReflectiveKey.prototype,"displayName",{get:function(){return stringify(this.token)},enumerable:!0,configurable:!0}),ReflectiveKey.get=function(token){return _globalKeyRegistry.get(resolveForwardRef(token))},Object.defineProperty(ReflectiveKey,"numberOfKeys",{get:function(){return _globalKeyRegistry.numberOfKeys},enumerable:!0,configurable:!0}),ReflectiveKey}(),KeyRegistry=function(){function KeyRegistry(){this._allKeys=new Map}return KeyRegistry.prototype.get=function(token){if(token instanceof ReflectiveKey)return token;if(this._allKeys.has(token))return this._allKeys.get(token);var newKey=new ReflectiveKey(token,ReflectiveKey.numberOfKeys);return this._allKeys.set(token,newKey),newKey},Object.defineProperty(KeyRegistry.prototype,"numberOfKeys",{get:function(){return this._allKeys.size},enumerable:!0,configurable:!0}),KeyRegistry}(),_globalKeyRegistry=new KeyRegistry,Type=Function,DELEGATE_CTOR=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*arguments\)/,ReflectionCapabilities=function(){function ReflectionCapabilities(reflect){this._reflect=reflect||_global.Reflect}return ReflectionCapabilities.prototype.isReflectionEnabled=function(){return!0},ReflectionCapabilities.prototype.factory=function(t){return function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i]=arguments[_i];return new(t.bind.apply(t,[void 0].concat(args)))}},ReflectionCapabilities.prototype._zipTypesAndAnnotations=function(paramTypes,paramAnnotations){var result;result="undefined"==typeof paramTypes?new Array(paramAnnotations.length):new Array(paramTypes.length);for(var i=0;i<result.length;i++)"undefined"==typeof paramTypes?result[i]=[]:paramTypes[i]!=Object?result[i]=[paramTypes[i]]:result[i]=[],paramAnnotations&&null!=paramAnnotations[i]&&(result[i]=result[i].concat(paramAnnotations[i]));return result},ReflectionCapabilities.prototype._ownParameters=function(type,parentCtor){if(DELEGATE_CTOR.exec(type.toString()))return null;if(type.parameters&&type.parameters!==parentCtor.parameters)return type.parameters;var tsickleCtorParams=type.ctorParameters;if(tsickleCtorParams&&tsickleCtorParams!==parentCtor.ctorParameters){var ctorParameters="function"==typeof tsickleCtorParams?tsickleCtorParams():tsickleCtorParams,paramTypes=ctorParameters.map(function(ctorParam){return ctorParam&&ctorParam.type}),paramAnnotations=ctorParameters.map(function(ctorParam){return ctorParam&&convertTsickleDecoratorIntoMetadata(ctorParam.decorators)});return this._zipTypesAndAnnotations(paramTypes,paramAnnotations)}if(null!=this._reflect&&null!=this._reflect.getOwnMetadata){var paramAnnotations=this._reflect.getOwnMetadata("parameters",type),paramTypes=this._reflect.getOwnMetadata("design:paramtypes",type);if(paramTypes||paramAnnotations)return this._zipTypesAndAnnotations(paramTypes,paramAnnotations)}return new Array(type.length).fill(void 0)},ReflectionCapabilities.prototype.parameters=function(type){if(!isType(type))return[];var parentCtor=getParentCtor(type),parameters=this._ownParameters(type,parentCtor);return parameters||parentCtor===Object||(parameters=this.parameters(parentCtor)),parameters||[]},ReflectionCapabilities.prototype._ownAnnotations=function(typeOrFunc,parentCtor){if(typeOrFunc.annotations&&typeOrFunc.annotations!==parentCtor.annotations){var annotations=typeOrFunc.annotations;return"function"==typeof annotations&&annotations.annotations&&(annotations=annotations.annotations),annotations}return typeOrFunc.decorators&&typeOrFunc.decorators!==parentCtor.decorators?convertTsickleDecoratorIntoMetadata(typeOrFunc.decorators):this._reflect&&this._reflect.getOwnMetadata?this._reflect.getOwnMetadata("annotations",typeOrFunc):null},ReflectionCapabilities.prototype.annotations=function(typeOrFunc){if(!isType(typeOrFunc))return[];var parentCtor=getParentCtor(typeOrFunc),ownAnnotations=this._ownAnnotations(typeOrFunc,parentCtor)||[],parentAnnotations=parentCtor!==Object?this.annotations(parentCtor):[];return parentAnnotations.concat(ownAnnotations)},ReflectionCapabilities.prototype._ownPropMetadata=function(typeOrFunc,parentCtor){if(typeOrFunc.propMetadata&&typeOrFunc.propMetadata!==parentCtor.propMetadata){var propMetadata=typeOrFunc.propMetadata;return"function"==typeof propMetadata&&propMetadata.propMetadata&&(propMetadata=propMetadata.propMetadata),propMetadata}if(typeOrFunc.propDecorators&&typeOrFunc.propDecorators!==parentCtor.propDecorators){var propDecorators_1=typeOrFunc.propDecorators,propMetadata_1={};return Object.keys(propDecorators_1).forEach(function(prop){propMetadata_1[prop]=convertTsickleDecoratorIntoMetadata(propDecorators_1[prop])}),propMetadata_1}return this._reflect&&this._reflect.getOwnMetadata?this._reflect.getOwnMetadata("propMetadata",typeOrFunc):null},ReflectionCapabilities.prototype.propMetadata=function(typeOrFunc){if(!isType(typeOrFunc))return{};var parentCtor=getParentCtor(typeOrFunc),propMetadata={};if(parentCtor!==Object){var parentPropMetadata_1=this.propMetadata(parentCtor);Object.keys(parentPropMetadata_1).forEach(function(propName){propMetadata[propName]=parentPropMetadata_1[propName]})}var ownPropMetadata=this._ownPropMetadata(typeOrFunc,parentCtor);return ownPropMetadata&&Object.keys(ownPropMetadata).forEach(function(propName){var decorators=[];propMetadata.hasOwnProperty(propName)&&decorators.push.apply(decorators,propMetadata[propName]),decorators.push.apply(decorators,ownPropMetadata[propName]),propMetadata[propName]=decorators}),propMetadata},ReflectionCapabilities.prototype.hasLifecycleHook=function(type,lcProperty){return type instanceof Type&&lcProperty in type.prototype},ReflectionCapabilities.prototype.getter=function(name){return new Function("o","return o."+name+";")},ReflectionCapabilities.prototype.setter=function(name){return new Function("o","v","return o."+name+" = v;")},ReflectionCapabilities.prototype.method=function(name){var functionBody="if (!o."+name+") throw new Error('\""+name+"\" is undefined');\n        return o."+name+".apply(o, args);";return new Function("o","args",functionBody)},ReflectionCapabilities.prototype.importUri=function(type){return"object"==typeof type&&type.filePath?type.filePath:"./"+stringify(type)},ReflectionCapabilities.prototype.resourceUri=function(type){return"./"+stringify(type)},ReflectionCapabilities.prototype.resolveIdentifier=function(name,moduleUrl,members,runtime){return runtime},ReflectionCapabilities.prototype.resolveEnum=function(enumIdentifier,name){return enumIdentifier[name]},ReflectionCapabilities}(),Reflector=function(){function Reflector(reflectionCapabilities){this.reflectionCapabilities=reflectionCapabilities}return Reflector.prototype.updateCapabilities=function(caps){this.reflectionCapabilities=caps},Reflector.prototype.factory=function(type){return this.reflectionCapabilities.factory(type)},Reflector.prototype.parameters=function(typeOrFunc){return this.reflectionCapabilities.parameters(typeOrFunc)},Reflector.prototype.annotations=function(typeOrFunc){return this.reflectionCapabilities.annotations(typeOrFunc)},Reflector.prototype.propMetadata=function(typeOrFunc){return this.reflectionCapabilities.propMetadata(typeOrFunc)},Reflector.prototype.hasLifecycleHook=function(type,lcProperty){return this.reflectionCapabilities.hasLifecycleHook(type,lcProperty)},Reflector.prototype.getter=function(name){return this.reflectionCapabilities.getter(name)},Reflector.prototype.setter=function(name){return this.reflectionCapabilities.setter(name)},Reflector.prototype.method=function(name){return this.reflectionCapabilities.method(name)},Reflector.prototype.importUri=function(type){return this.reflectionCapabilities.importUri(type)},Reflector.prototype.resourceUri=function(type){return this.reflectionCapabilities.resourceUri(type)},Reflector.prototype.resolveIdentifier=function(name,moduleUrl,members,runtime){return this.reflectionCapabilities.resolveIdentifier(name,moduleUrl,members,runtime)},Reflector.prototype.resolveEnum=function(identifier,name){return this.reflectionCapabilities.resolveEnum(identifier,name)},Reflector}(),reflector=new Reflector(new ReflectionCapabilities),ReflectiveDependency=function(){function ReflectiveDependency(key,optional,visibility){this.key=key,this.optional=optional,this.visibility=visibility}return ReflectiveDependency.fromKey=function(key){return new ReflectiveDependency(key,(!1),null)},ReflectiveDependency}(),_EMPTY_LIST=[],ResolvedReflectiveProvider_=function(){function ResolvedReflectiveProvider_(key,resolvedFactories,multiProvider){this.key=key,this.resolvedFactories=resolvedFactories,this.multiProvider=multiProvider}return Object.defineProperty(ResolvedReflectiveProvider_.prototype,"resolvedFactory",{get:function(){return this.resolvedFactories[0]},enumerable:!0,configurable:!0}),ResolvedReflectiveProvider_}(),ResolvedReflectiveFactory=function(){function ResolvedReflectiveFactory(factory,dependencies){this.factory=factory,this.dependencies=dependencies}return ResolvedReflectiveFactory}(),UNDEFINED=new Object,ReflectiveInjector=function(){function ReflectiveInjector(){}return ReflectiveInjector.resolve=function(providers){return resolveReflectiveProviders(providers)},ReflectiveInjector.resolveAndCreate=function(providers,parent){var ResolvedReflectiveProviders=ReflectiveInjector.resolve(providers);return ReflectiveInjector.fromResolvedProviders(ResolvedReflectiveProviders,parent)},ReflectiveInjector.fromResolvedProviders=function(providers,parent){return new ReflectiveInjector_(providers,parent)},ReflectiveInjector.prototype.parent=function(){},ReflectiveInjector.prototype.resolveAndCreateChild=function(providers){},ReflectiveInjector.prototype.createChildFromResolved=function(providers){},ReflectiveInjector.prototype.resolveAndInstantiate=function(provider){},ReflectiveInjector.prototype.instantiateResolved=function(provider){},ReflectiveInjector.prototype.get=function(token,notFoundValue){},ReflectiveInjector}(),ReflectiveInjector_=function(){function ReflectiveInjector_(_providers,_parent){this._constructionCounter=0,this._providers=_providers,this._parent=_parent||null;var len=_providers.length;this.keyIds=new Array(len),this.objs=new Array(len);for(var i=0;i<len;i++)this.keyIds[i]=_providers[i].key.id,this.objs[i]=UNDEFINED}return ReflectiveInjector_.prototype.get=function(token,notFoundValue){return void 0===notFoundValue&&(notFoundValue=THROW_IF_NOT_FOUND),this._getByKey(ReflectiveKey.get(token),null,notFoundValue)},Object.defineProperty(ReflectiveInjector_.prototype,"parent",{get:function(){return this._parent},enumerable:!0,configurable:!0}),ReflectiveInjector_.prototype.resolveAndCreateChild=function(providers){var ResolvedReflectiveProviders=ReflectiveInjector.resolve(providers);return this.createChildFromResolved(ResolvedReflectiveProviders)},ReflectiveInjector_.prototype.createChildFromResolved=function(providers){var inj=new ReflectiveInjector_(providers);return inj._parent=this,inj},ReflectiveInjector_.prototype.resolveAndInstantiate=function(provider){return this.instantiateResolved(ReflectiveInjector.resolve([provider])[0])},ReflectiveInjector_.prototype.instantiateResolved=function(provider){return this._instantiateProvider(provider)},ReflectiveInjector_.prototype.getProviderAtIndex=function(index){if(index<0||index>=this._providers.length)throw outOfBoundsError(index);return this._providers[index]},ReflectiveInjector_.prototype._new=function(provider){if(this._constructionCounter++>this._getMaxNumberOfObjects())throw cyclicDependencyError(this,provider.key);return this._instantiateProvider(provider)},ReflectiveInjector_.prototype._getMaxNumberOfObjects=function(){return this.objs.length},ReflectiveInjector_.prototype._instantiateProvider=function(provider){if(provider.multiProvider){for(var res=new Array(provider.resolvedFactories.length),i=0;i<provider.resolvedFactories.length;++i)res[i]=this._instantiate(provider,provider.resolvedFactories[i]);return res}return this._instantiate(provider,provider.resolvedFactories[0])},ReflectiveInjector_.prototype._instantiate=function(provider,ResolvedReflectiveFactory$$1){var deps,_this=this,factory=ResolvedReflectiveFactory$$1.factory;try{deps=ResolvedReflectiveFactory$$1.dependencies.map(function(dep){return _this._getByReflectiveDependency(dep)})}catch(e){throw e.addKey&&e.addKey(this,provider.key),e}var obj;try{obj=factory.apply(void 0,deps)}catch(e){throw instantiationError(this,e,e.stack,provider.key)}return obj},ReflectiveInjector_.prototype._getByReflectiveDependency=function(dep){return this._getByKey(dep.key,dep.visibility,dep.optional?null:THROW_IF_NOT_FOUND)},ReflectiveInjector_.prototype._getByKey=function(key,visibility,notFoundValue){return key===INJECTOR_KEY?this:visibility instanceof Self?this._getByKeySelf(key,notFoundValue):this._getByKeyDefault(key,notFoundValue,visibility)},ReflectiveInjector_.prototype._getObjByKeyId=function(keyId){for(var i=0;i<this.keyIds.length;i++)if(this.keyIds[i]===keyId)return this.objs[i]===UNDEFINED&&(this.objs[i]=this._new(this._providers[i])),this.objs[i];return UNDEFINED},ReflectiveInjector_.prototype._throwOrNull=function(key,notFoundValue){if(notFoundValue!==THROW_IF_NOT_FOUND)return notFoundValue;throw noProviderError(this,key)},ReflectiveInjector_.prototype._getByKeySelf=function(key,notFoundValue){var obj=this._getObjByKeyId(key.id);return obj!==UNDEFINED?obj:this._throwOrNull(key,notFoundValue)},ReflectiveInjector_.prototype._getByKeyDefault=function(key,notFoundValue,visibility){var inj;for(inj=visibility instanceof SkipSelf?this._parent:this;inj instanceof ReflectiveInjector_;){var inj_=inj,obj=inj_._getObjByKeyId(key.id);if(obj!==UNDEFINED)return obj;inj=inj_._parent}return null!==inj?inj.get(key.token,notFoundValue):this._throwOrNull(key,notFoundValue)},Object.defineProperty(ReflectiveInjector_.prototype,"displayName",{get:function(){var providers=_mapProviders(this,function(b){return' "'+b.key.displayName+'" '}).join(", ");return"ReflectiveInjector(providers: ["+providers+"])"},enumerable:!0,configurable:!0}),ReflectiveInjector_.prototype.toString=function(){return this.displayName},ReflectiveInjector_}(),INJECTOR_KEY=ReflectiveKey.get(Injector),APP_INITIALIZER=new InjectionToken("Application Initializer"),ApplicationInitStatus=function(){function ApplicationInitStatus(appInits){var _this=this;this.appInits=appInits,this.initialized=!1,this._done=!1,this._donePromise=new Promise(function(res,rej){_this.resolve=res,_this.reject=rej})}return ApplicationInitStatus.prototype.runInitializers=function(){var _this=this;if(!this.initialized){var asyncInitPromises=[],complete=function(){_this._done=!0,_this.resolve()};if(this.appInits)for(var i=0;i<this.appInits.length;i++){var initResult=this.appInits[i]();isPromise(initResult)&&asyncInitPromises.push(initResult)}Promise.all(asyncInitPromises).then(function(){complete()}).catch(function(e){_this.reject(e)}),0===asyncInitPromises.length&&complete(),this.initialized=!0}},Object.defineProperty(ApplicationInitStatus.prototype,"done",{get:function(){return this._done},enumerable:!0,configurable:!0}),Object.defineProperty(ApplicationInitStatus.prototype,"donePromise",{get:function(){return this._donePromise},enumerable:!0,configurable:!0}),ApplicationInitStatus}();ApplicationInitStatus.decorators=[{type:Injectable}],ApplicationInitStatus.ctorParameters=function(){return[{type:Array,decorators:[{type:Inject,args:[APP_INITIALIZER]},{type:Optional}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var APP_ID=new InjectionToken("AppId"),APP_ID_RANDOM_PROVIDER={provide:APP_ID,useFactory:_appIdRandomProviderFactory,deps:[]},PLATFORM_INITIALIZER=new InjectionToken("Platform Initializer"),PLATFORM_ID=new InjectionToken("Platform ID"),APP_BOOTSTRAP_LISTENER=new InjectionToken("appBootstrapListener"),PACKAGE_ROOT_URL=new InjectionToken("Application Packages Root URL"),Console=function(){function Console(){}return Console.prototype.log=function(message){console.log(message)},Console.prototype.warn=function(message){console.warn(message)},Console}();Console.decorators=[{type:Injectable}],Console.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var ModuleWithComponentFactories=function(){function ModuleWithComponentFactories(ngModuleFactory,componentFactories){this.ngModuleFactory=ngModuleFactory,this.componentFactories=componentFactories}return ModuleWithComponentFactories}(),Compiler=function(){function Compiler(){}return Compiler.prototype.compileModuleSync=function(moduleType){throw _throwError()},Compiler.prototype.compileModuleAsync=function(moduleType){throw _throwError()},Compiler.prototype.compileModuleAndAllComponentsSync=function(moduleType){throw _throwError()},Compiler.prototype.compileModuleAndAllComponentsAsync=function(moduleType){throw _throwError()},Compiler.prototype.getNgContentSelectors=function(component){throw _throwError()},Compiler.prototype.clearCache=function(){},Compiler.prototype.clearCacheFor=function(type){},Compiler}();Compiler.decorators=[{type:Injectable}],Compiler.ctorParameters=function(){return[]};var COMPILER_OPTIONS=new InjectionToken("compilerOptions"),CompilerFactory=function(){function CompilerFactory(){}return CompilerFactory.prototype.createCompiler=function(options){},CompilerFactory}(),ComponentRef=function(){function ComponentRef(){}return ComponentRef.prototype.location=function(){},ComponentRef.prototype.injector=function(){},ComponentRef.prototype.instance=function(){},ComponentRef.prototype.hostView=function(){},ComponentRef.prototype.changeDetectorRef=function(){},ComponentRef.prototype.componentType=function(){},ComponentRef.prototype.destroy=function(){},ComponentRef.prototype.onDestroy=function(callback){},ComponentRef}(),ComponentFactory=function(){function ComponentFactory(){}return ComponentFactory.prototype.selector=function(){},ComponentFactory.prototype.componentType=function(){},ComponentFactory.prototype.ngContentSelectors=function(){},ComponentFactory.prototype.inputs=function(){},ComponentFactory.prototype.outputs=function(){},ComponentFactory.prototype.create=function(injector,projectableNodes,rootSelectorOrNode,ngModule){},ComponentFactory}(),ERROR_COMPONENT="ngComponent",_NullComponentFactoryResolver=function(){function _NullComponentFactoryResolver(){}return _NullComponentFactoryResolver.prototype.resolveComponentFactory=function(component){throw noComponentFactoryError(component)},_NullComponentFactoryResolver}(),ComponentFactoryResolver=function(){function ComponentFactoryResolver(){}return ComponentFactoryResolver.prototype.resolveComponentFactory=function(component){},ComponentFactoryResolver}();ComponentFactoryResolver.NULL=new _NullComponentFactoryResolver;var trace,events,CodegenComponentFactoryResolver=function(){function CodegenComponentFactoryResolver(factories,_parent,_ngModule){this._parent=_parent,this._ngModule=_ngModule,this._factories=new Map;for(var i=0;i<factories.length;i++){var factory=factories[i];this._factories.set(factory.componentType,factory)}}return CodegenComponentFactoryResolver.prototype.resolveComponentFactory=function(component){var factory=this._factories.get(component);if(!factory&&this._parent&&(factory=this._parent.resolveComponentFactory(component)),!factory)throw noComponentFactoryError(component);return new ComponentFactoryBoundToModule(factory,this._ngModule)},CodegenComponentFactoryResolver}(),ComponentFactoryBoundToModule=function(_super){function ComponentFactoryBoundToModule(factory,ngModule){var _this=_super.call(this)||this;return _this.factory=factory,_this.ngModule=ngModule,_this}return __extends(ComponentFactoryBoundToModule,_super),Object.defineProperty(ComponentFactoryBoundToModule.prototype,"selector",{get:function(){return this.factory.selector},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentFactoryBoundToModule.prototype,"componentType",{get:function(){return this.factory.componentType},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentFactoryBoundToModule.prototype,"ngContentSelectors",{get:function(){return this.factory.ngContentSelectors},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentFactoryBoundToModule.prototype,"inputs",{get:function(){return this.factory.inputs},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentFactoryBoundToModule.prototype,"outputs",{get:function(){return this.factory.outputs},enumerable:!0,configurable:!0}),ComponentFactoryBoundToModule.prototype.create=function(injector,projectableNodes,rootSelectorOrNode,ngModule){return this.factory.create(injector,projectableNodes,rootSelectorOrNode,ngModule||this.ngModule)},ComponentFactoryBoundToModule}(ComponentFactory),NgModuleRef=function(){function NgModuleRef(){}return NgModuleRef.prototype.injector=function(){},NgModuleRef.prototype.componentFactoryResolver=function(){},NgModuleRef.prototype.instance=function(){},NgModuleRef.prototype.destroy=function(){},NgModuleRef.prototype.onDestroy=function(callback){},NgModuleRef}(),NgModuleFactory=function(){function NgModuleFactory(){}return NgModuleFactory.prototype.moduleType=function(){},NgModuleFactory.prototype.create=function(parentInjector){},NgModuleFactory}(),wtfEnabled=detectWTF(),wtfCreateScope=wtfEnabled?createScope$1:function(signature,flags){return noopScope},wtfLeave=wtfEnabled?leave:function(s,r){return r},wtfStartTimeRange=wtfEnabled?startTimeRange:function(rangeType,action){return null},wtfEndTimeRange=wtfEnabled?endTimeRange:function(r){return null},EventEmitter=function(_super){function EventEmitter(isAsync){void 0===isAsync&&(isAsync=!1);var _this=_super.call(this)||this;return _this.__isAsync=isAsync,_this}return __extends(EventEmitter,_super),EventEmitter.prototype.emit=function(value){_super.prototype.next.call(this,value)},EventEmitter.prototype.subscribe=function(generatorOrNext,error,complete){var schedulerFn,errorFn=function(err){return null},completeFn=function(){return null};return generatorOrNext&&"object"==typeof generatorOrNext?(schedulerFn=this.__isAsync?function(value){setTimeout(function(){return generatorOrNext.next(value)})}:function(value){generatorOrNext.next(value)},generatorOrNext.error&&(errorFn=this.__isAsync?function(err){setTimeout(function(){return generatorOrNext.error(err)})}:function(err){generatorOrNext.error(err)}),generatorOrNext.complete&&(completeFn=this.__isAsync?function(){setTimeout(function(){return generatorOrNext.complete()})}:function(){generatorOrNext.complete()})):(schedulerFn=this.__isAsync?function(value){setTimeout(function(){return generatorOrNext(value)})}:function(value){generatorOrNext(value)},error&&(errorFn=this.__isAsync?function(err){setTimeout(function(){return error(err)})}:function(err){error(err)}),complete&&(completeFn=this.__isAsync?function(){setTimeout(function(){return complete()})}:function(){complete()})),_super.prototype.subscribe.call(this,schedulerFn,errorFn,completeFn)},EventEmitter}(rxjs_Subject.Subject),NgZone=function(){function NgZone(_a){var _b=_a.enableLongStackTrace,enableLongStackTrace=void 0!==_b&&_b;if(this._hasPendingMicrotasks=!1,this._hasPendingMacrotasks=!1,this._isStable=!0,this._nesting=0,this._onUnstable=new EventEmitter((!1)),this._onMicrotaskEmpty=new EventEmitter((!1)),this._onStable=new EventEmitter((!1)),this._onErrorEvents=new EventEmitter((!1)),"undefined"==typeof Zone)throw new Error("Angular requires Zone.js prolyfill.");Zone.assertZonePatched(),this.outer=this.inner=Zone.current,Zone.wtfZoneSpec&&(this.inner=this.inner.fork(Zone.wtfZoneSpec)),enableLongStackTrace&&Zone.longStackTraceZoneSpec&&(this.inner=this.inner.fork(Zone.longStackTraceZoneSpec)),this.forkInnerZoneWithAngularBehavior()}return NgZone.isInAngularZone=function(){return Zone.current.get("isAngularZone")===!0},NgZone.assertInAngularZone=function(){if(!NgZone.isInAngularZone())throw new Error("Expected to be in Angular Zone, but it is not!")},NgZone.assertNotInAngularZone=function(){if(NgZone.isInAngularZone())throw new Error("Expected to not be in Angular Zone, but it is!")},NgZone.prototype.run=function(fn){return this.inner.run(fn)},NgZone.prototype.runGuarded=function(fn){return this.inner.runGuarded(fn)},NgZone.prototype.runOutsideAngular=function(fn){return this.outer.run(fn)},Object.defineProperty(NgZone.prototype,"onUnstable",{get:function(){return this._onUnstable},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"onMicrotaskEmpty",{get:function(){return this._onMicrotaskEmpty},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"onStable",{get:function(){return this._onStable},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"onError",{get:function(){return this._onErrorEvents},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"isStable",{get:function(){return this._isStable},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"hasPendingMicrotasks",{get:function(){return this._hasPendingMicrotasks},enumerable:!0,configurable:!0}),Object.defineProperty(NgZone.prototype,"hasPendingMacrotasks",{get:function(){return this._hasPendingMacrotasks},enumerable:!0,configurable:!0}),NgZone.prototype.checkStable=function(){var _this=this;if(0==this._nesting&&!this._hasPendingMicrotasks&&!this._isStable)try{this._nesting++,this._onMicrotaskEmpty.emit(null)}finally{if(this._nesting--,!this._hasPendingMicrotasks)try{this.runOutsideAngular(function(){return _this._onStable.emit(null)})}finally{this._isStable=!0}}},NgZone.prototype.forkInnerZoneWithAngularBehavior=function(){var _this=this;this.inner=this.inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:function(delegate,current,target,task,applyThis,applyArgs){try{return _this.onEnter(),delegate.invokeTask(target,task,applyThis,applyArgs)}finally{_this.onLeave()}},onInvoke:function(delegate,current,target,callback,applyThis,applyArgs,source){try{return _this.onEnter(),delegate.invoke(target,callback,applyThis,applyArgs,source)}finally{_this.onLeave()}},onHasTask:function(delegate,current,target,hasTaskState){delegate.hasTask(target,hasTaskState),current===target&&("microTask"==hasTaskState.change?_this.setHasMicrotask(hasTaskState.microTask):"macroTask"==hasTaskState.change&&_this.setHasMacrotask(hasTaskState.macroTask))},onHandleError:function(delegate,current,target,error){return delegate.handleError(target,error),_this.triggerError(error),!1}})},NgZone.prototype.onEnter=function(){this._nesting++,this._isStable&&(this._isStable=!1,this._onUnstable.emit(null))},NgZone.prototype.onLeave=function(){this._nesting--,this.checkStable()},NgZone.prototype.setHasMicrotask=function(hasMicrotasks){this._hasPendingMicrotasks=hasMicrotasks,this.checkStable()},NgZone.prototype.setHasMacrotask=function(hasMacrotasks){this._hasPendingMacrotasks=hasMacrotasks},NgZone.prototype.triggerError=function(error){this._onErrorEvents.emit(error)},NgZone}(),Testability=function(){function Testability(_ngZone){this._ngZone=_ngZone,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this._watchAngularEvents()}return Testability.prototype._watchAngularEvents=function(){var _this=this;this._ngZone.onUnstable.subscribe({next:function(){_this._didWork=!0,_this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(function(){_this._ngZone.onStable.subscribe({next:function(){NgZone.assertNotInAngularZone(),scheduleMicroTask(function(){_this._isZoneStable=!0,_this._runCallbacksIfReady()})}})})},Testability.prototype.increasePendingRequestCount=function(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount},Testability.prototype.decreasePendingRequestCount=function(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount},Testability.prototype.isStable=function(){return this._isZoneStable&&0==this._pendingCount&&!this._ngZone.hasPendingMacrotasks},Testability.prototype._runCallbacksIfReady=function(){var _this=this;this.isStable()?scheduleMicroTask(function(){for(;0!==_this._callbacks.length;)_this._callbacks.pop()(_this._didWork);_this._didWork=!1}):this._didWork=!0},Testability.prototype.whenStable=function(callback){this._callbacks.push(callback),this._runCallbacksIfReady()},Testability.prototype.getPendingRequestCount=function(){return this._pendingCount},Testability.prototype.findBindings=function(using,provider,exactMatch){return[]},Testability.prototype.findProviders=function(using,provider,exactMatch){return[]},Testability}();Testability.decorators=[{type:Injectable}],Testability.ctorParameters=function(){return[{type:NgZone}]};var TestabilityRegistry=function(){function TestabilityRegistry(){this._applications=new Map,_testabilityGetter.addToWindow(this)}return TestabilityRegistry.prototype.registerApplication=function(token,testability){this._applications.set(token,testability)},TestabilityRegistry.prototype.getTestability=function(elem){return this._applications.get(elem)||null},TestabilityRegistry.prototype.getAllTestabilities=function(){return Array.from(this._applications.values())},TestabilityRegistry.prototype.getAllRootElements=function(){return Array.from(this._applications.keys())},TestabilityRegistry.prototype.findTestabilityInTree=function(elem,findInAncestors){return void 0===findInAncestors&&(findInAncestors=!0),_testabilityGetter.findTestabilityInTree(this,elem,findInAncestors)},TestabilityRegistry}();TestabilityRegistry.decorators=[{type:Injectable}],TestabilityRegistry.ctorParameters=function(){return[]};var _platform,_NoopGetTestability=function(){function _NoopGetTestability(){}return _NoopGetTestability.prototype.addToWindow=function(registry){},_NoopGetTestability.prototype.findTestabilityInTree=function(registry,elem,findInAncestors){return null},_NoopGetTestability}(),_testabilityGetter=new _NoopGetTestability,_devMode=!0,_runModeLocked=!1,ALLOW_MULTIPLE_PLATFORMS=new InjectionToken("AllowMultipleToken"),NgProbeToken=function(){function NgProbeToken(name,token){this.name=name,this.token=token}return NgProbeToken}(),PlatformRef=function(){function PlatformRef(){}return PlatformRef.prototype.bootstrapModuleFactory=function(moduleFactory){},PlatformRef.prototype.bootstrapModule=function(moduleType,compilerOptions){},PlatformRef.prototype.onDestroy=function(callback){},PlatformRef.prototype.injector=function(){},PlatformRef.prototype.destroy=function(){},PlatformRef.prototype.destroyed=function(){},PlatformRef}(),PlatformRef_=function(_super){function PlatformRef_(_injector){var _this=_super.call(this)||this;return _this._injector=_injector,_this._modules=[],_this._destroyListeners=[],_this._destroyed=!1,_this}return __extends(PlatformRef_,_super),PlatformRef_.prototype.onDestroy=function(callback){this._destroyListeners.push(callback)},Object.defineProperty(PlatformRef_.prototype,"injector",{get:function(){return this._injector},enumerable:!0,configurable:!0}),Object.defineProperty(PlatformRef_.prototype,"destroyed",{get:function(){return this._destroyed},enumerable:!0,configurable:!0}),PlatformRef_.prototype.destroy=function(){if(this._destroyed)throw new Error("The platform has already been destroyed!");this._modules.slice().forEach(function(module){return module.destroy()}),this._destroyListeners.forEach(function(listener){return listener()}),this._destroyed=!0},PlatformRef_.prototype.bootstrapModuleFactory=function(moduleFactory){return this._bootstrapModuleFactoryWithZone(moduleFactory)},PlatformRef_.prototype._bootstrapModuleFactoryWithZone=function(moduleFactory,ngZone){var _this=this;return ngZone||(ngZone=new NgZone({enableLongStackTrace:isDevMode()})),ngZone.run(function(){var ngZoneInjector=ReflectiveInjector.resolveAndCreate([{provide:NgZone,useValue:ngZone}],_this.injector),moduleRef=moduleFactory.create(ngZoneInjector),exceptionHandler=moduleRef.injector.get(ErrorHandler,null);if(!exceptionHandler)throw new Error("No ErrorHandler. Is platform module (BrowserModule) included?");return moduleRef.onDestroy(function(){return remove(_this._modules,moduleRef)}),ngZone.onError.subscribe({next:function(error){exceptionHandler.handleError(error)}}),_callAndReportToErrorHandler(exceptionHandler,function(){var initStatus=moduleRef.injector.get(ApplicationInitStatus);return initStatus.runInitializers(),initStatus.donePromise.then(function(){return _this._moduleDoBootstrap(moduleRef),moduleRef})})})},PlatformRef_.prototype.bootstrapModule=function(moduleType,compilerOptions){return void 0===compilerOptions&&(compilerOptions=[]),this._bootstrapModuleWithZone(moduleType,compilerOptions)},PlatformRef_.prototype._bootstrapModuleWithZone=function(moduleType,compilerOptions,ngZone){var _this=this;void 0===compilerOptions&&(compilerOptions=[]);var compilerFactory=this.injector.get(CompilerFactory),compiler=compilerFactory.createCompiler(Array.isArray(compilerOptions)?compilerOptions:[compilerOptions]);return compiler.compileModuleAsync(moduleType).then(function(moduleFactory){return _this._bootstrapModuleFactoryWithZone(moduleFactory,ngZone)})},PlatformRef_.prototype._moduleDoBootstrap=function(moduleRef){var appRef=moduleRef.injector.get(ApplicationRef);if(moduleRef._bootstrapComponents.length>0)moduleRef._bootstrapComponents.forEach(function(f){return appRef.bootstrap(f)});else{if(!moduleRef.instance.ngDoBootstrap)throw new Error("The module "+stringify(moduleRef.instance.constructor)+' was bootstrapped, but it does not declare "@NgModule.bootstrap" components nor a "ngDoBootstrap" method. Please define one of these.');moduleRef.instance.ngDoBootstrap(appRef)}this._modules.push(moduleRef)},PlatformRef_}(PlatformRef);PlatformRef_.decorators=[{type:Injectable}],PlatformRef_.ctorParameters=function(){return[{type:Injector}]};var ApplicationRef=function(){function ApplicationRef(){}return ApplicationRef.prototype.bootstrap=function(componentFactory,rootSelectorOrNode){},ApplicationRef.prototype.tick=function(){},ApplicationRef.prototype.componentTypes=function(){},ApplicationRef.prototype.components=function(){},ApplicationRef.prototype.attachView=function(view){},ApplicationRef.prototype.detachView=function(view){},ApplicationRef.prototype.viewCount=function(){},ApplicationRef.prototype.isStable=function(){},ApplicationRef}(),ApplicationRef_=function(_super){function ApplicationRef_(_zone,_console,_injector,_exceptionHandler,_componentFactoryResolver,_initStatus){var _this=_super.call(this)||this;_this._zone=_zone,_this._console=_console,_this._injector=_injector,_this._exceptionHandler=_exceptionHandler,_this._componentFactoryResolver=_componentFactoryResolver,_this._initStatus=_initStatus,_this._bootstrapListeners=[],_this._rootComponents=[],_this._rootComponentTypes=[],_this._views=[],_this._runningTick=!1,_this._enforceNoNewChanges=!1,_this._stable=!0,_this._enforceNoNewChanges=isDevMode(),_this._zone.onMicrotaskEmpty.subscribe({next:function(){_this._zone.run(function(){_this.tick()})}});var isCurrentlyStable=new rxjs_Observable.Observable(function(observer){_this._stable=_this._zone.isStable&&!_this._zone.hasPendingMacrotasks&&!_this._zone.hasPendingMicrotasks,_this._zone.runOutsideAngular(function(){observer.next(_this._stable),observer.complete()})}),isStable=new rxjs_Observable.Observable(function(observer){var stableSub=_this._zone.onStable.subscribe(function(){NgZone.assertNotInAngularZone(),scheduleMicroTask(function(){_this._stable||_this._zone.hasPendingMacrotasks||_this._zone.hasPendingMicrotasks||(_this._stable=!0,observer.next(!0))})}),unstableSub=_this._zone.onUnstable.subscribe(function(){NgZone.assertInAngularZone(),_this._stable&&(_this._stable=!1,_this._zone.runOutsideAngular(function(){observer.next(!1)}))});return function(){stableSub.unsubscribe(),unstableSub.unsubscribe()}});return _this._isStable=rxjs_observable_merge.merge(isCurrentlyStable,rxjs_operator_share.share.call(isStable)),_this}return __extends(ApplicationRef_,_super),ApplicationRef_.prototype.attachView=function(viewRef){var view=viewRef;this._views.push(view),view.attachToAppRef(this)},ApplicationRef_.prototype.detachView=function(viewRef){var view=viewRef;remove(this._views,view),view.detachFromAppRef()},ApplicationRef_.prototype.bootstrap=function(componentOrFactory,rootSelectorOrNode){var _this=this;if(!this._initStatus.done)throw new Error("Cannot bootstrap as there are still asynchronous initializers running. Bootstrap components in the `ngDoBootstrap` method of the root module.");var componentFactory;componentFactory=componentOrFactory instanceof ComponentFactory?componentOrFactory:this._componentFactoryResolver.resolveComponentFactory(componentOrFactory),this._rootComponentTypes.push(componentFactory.componentType);var ngModule=componentFactory instanceof ComponentFactoryBoundToModule?null:this._injector.get(NgModuleRef),selectorOrNode=rootSelectorOrNode||componentFactory.selector,compRef=componentFactory.create(Injector.NULL,[],selectorOrNode,ngModule);compRef.onDestroy(function(){_this._unloadComponent(compRef)});var testability=compRef.injector.get(Testability,null);return testability&&compRef.injector.get(TestabilityRegistry).registerApplication(compRef.location.nativeElement,testability),this._loadComponent(compRef),isDevMode()&&this._console.log("Angular is running in the development mode. Call enableProdMode() to enable the production mode."),compRef},ApplicationRef_.prototype._loadComponent=function(componentRef){this.attachView(componentRef.hostView),this.tick(),this._rootComponents.push(componentRef);var listeners=this._injector.get(APP_BOOTSTRAP_LISTENER,[]).concat(this._bootstrapListeners);listeners.forEach(function(listener){return listener(componentRef)})},ApplicationRef_.prototype._unloadComponent=function(componentRef){this.detachView(componentRef.hostView),remove(this._rootComponents,componentRef)},ApplicationRef_.prototype.tick=function(){if(this._runningTick)throw new Error("ApplicationRef.tick is called recursively");var scope=ApplicationRef_._tickScope();try{this._runningTick=!0,this._views.forEach(function(view){return view.detectChanges()}),this._enforceNoNewChanges&&this._views.forEach(function(view){return view.checkNoChanges()})}catch(e){this._exceptionHandler.handleError(e)}finally{this._runningTick=!1,wtfLeave(scope)}},ApplicationRef_.prototype.ngOnDestroy=function(){this._views.slice().forEach(function(view){return view.destroy()})},Object.defineProperty(ApplicationRef_.prototype,"viewCount",{get:function(){return this._views.length},enumerable:!0,configurable:!0}),Object.defineProperty(ApplicationRef_.prototype,"componentTypes",{get:function(){return this._rootComponentTypes},enumerable:!0,configurable:!0}),Object.defineProperty(ApplicationRef_.prototype,"components",{get:function(){return this._rootComponents},enumerable:!0,configurable:!0}),Object.defineProperty(ApplicationRef_.prototype,"isStable",{get:function(){return this._isStable},enumerable:!0,configurable:!0}),ApplicationRef_}(ApplicationRef);ApplicationRef_._tickScope=wtfCreateScope("ApplicationRef#tick()"),ApplicationRef_.decorators=[{type:Injectable}],ApplicationRef_.ctorParameters=function(){return[{type:NgZone},{type:Console},{type:Injector},{type:ErrorHandler},{type:ComponentFactoryResolver},{type:ApplicationInitStatus}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var RenderComponentType=function(){function RenderComponentType(id,templateUrl,slotCount,encapsulation,styles,animations){this.id=id,this.templateUrl=templateUrl,this.slotCount=slotCount,this.encapsulation=encapsulation,this.styles=styles,this.animations=animations}return RenderComponentType}(),RenderDebugInfo=function(){function RenderDebugInfo(){}return RenderDebugInfo.prototype.injector=function(){},RenderDebugInfo.prototype.component=function(){},RenderDebugInfo.prototype.providerTokens=function(){},RenderDebugInfo.prototype.references=function(){},RenderDebugInfo.prototype.context=function(){},RenderDebugInfo.prototype.source=function(){},RenderDebugInfo}(),Renderer=function(){function Renderer(){}return Renderer.prototype.selectRootElement=function(selectorOrNode,debugInfo){},Renderer.prototype.createElement=function(parentElement,name,debugInfo){},Renderer.prototype.createViewRoot=function(hostElement){},Renderer.prototype.createTemplateAnchor=function(parentElement,debugInfo){},Renderer.prototype.createText=function(parentElement,value,debugInfo){},Renderer.prototype.projectNodes=function(parentElement,nodes){},Renderer.prototype.attachViewAfter=function(node,viewRootNodes){},Renderer.prototype.detachView=function(viewRootNodes){},Renderer.prototype.destroyView=function(hostElement,viewAllNodes){},Renderer.prototype.listen=function(renderElement,name,callback){},Renderer.prototype.listenGlobal=function(target,name,callback){},Renderer.prototype.setElementProperty=function(renderElement,propertyName,propertyValue){},Renderer.prototype.setElementAttribute=function(renderElement,attributeName,attributeValue){},Renderer.prototype.setBindingDebugInfo=function(renderElement,propertyName,propertyValue){},Renderer.prototype.setElementClass=function(renderElement,className,isAdd){},Renderer.prototype.setElementStyle=function(renderElement,styleName,styleValue){},Renderer.prototype.invokeElementMethod=function(renderElement,methodName,args){},Renderer.prototype.setText=function(renderNode,text){},Renderer.prototype.animate=function(element,startingStyles,keyframes,duration,delay,easing,previousPlayers){},Renderer}(),RootRenderer=(new InjectionToken("Renderer2Interceptor"),function(){function RootRenderer(){}return RootRenderer.prototype.renderComponent=function(componentType){},RootRenderer}()),RendererFactory2=function(){function RendererFactory2(){}return RendererFactory2.prototype.createRenderer=function(hostElement,type){},RendererFactory2.prototype.begin=function(){},RendererFactory2.prototype.end=function(){},RendererFactory2.prototype.whenRenderingDone=function(){},RendererFactory2}(),RendererStyleFlags2={};RendererStyleFlags2.Important=1,RendererStyleFlags2.DashCase=2,RendererStyleFlags2[RendererStyleFlags2.Important]="Important",RendererStyleFlags2[RendererStyleFlags2.DashCase]="DashCase";var Renderer2=function(){function Renderer2(){}return Renderer2.prototype.data=function(){},Renderer2.prototype.destroy=function(){},Renderer2.prototype.createElement=function(name,namespace){},Renderer2.prototype.createComment=function(value){},Renderer2.prototype.createText=function(value){},Renderer2.prototype.appendChild=function(parent,newChild){},Renderer2.prototype.insertBefore=function(parent,newChild,refChild){},Renderer2.prototype.removeChild=function(parent,oldChild){},Renderer2.prototype.selectRootElement=function(selectorOrNode){},Renderer2.prototype.parentNode=function(node){},Renderer2.prototype.nextSibling=function(node){},Renderer2.prototype.setAttribute=function(el,name,value,namespace){},Renderer2.prototype.removeAttribute=function(el,name,namespace){},Renderer2.prototype.addClass=function(el,name){},Renderer2.prototype.removeClass=function(el,name){},Renderer2.prototype.setStyle=function(el,style,value,flags){},Renderer2.prototype.removeStyle=function(el,style,flags){},Renderer2.prototype.setProperty=function(el,name,value){},Renderer2.prototype.setValue=function(node,value){},Renderer2.prototype.listen=function(target,eventName,callback){},Renderer2}(),ElementRef=function(){function ElementRef(nativeElement){this.nativeElement=nativeElement}return ElementRef}(),NgModuleFactoryLoader=function(){function NgModuleFactoryLoader(){}return NgModuleFactoryLoader.prototype.load=function(path){},NgModuleFactoryLoader}(),moduleFactories=new Map,QueryList=function(){function QueryList(){this._dirty=!0,this._results=[],this._emitter=new EventEmitter}return Object.defineProperty(QueryList.prototype,"changes",{get:function(){return this._emitter},enumerable:!0,configurable:!0}),Object.defineProperty(QueryList.prototype,"length",{get:function(){return this._results.length},enumerable:!0,configurable:!0}),Object.defineProperty(QueryList.prototype,"first",{get:function(){return this._results[0]},enumerable:!0,configurable:!0}),Object.defineProperty(QueryList.prototype,"last",{get:function(){return this._results[this.length-1]},enumerable:!0,configurable:!0}),QueryList.prototype.map=function(fn){return this._results.map(fn)},QueryList.prototype.filter=function(fn){return this._results.filter(fn)},QueryList.prototype.find=function(fn){return this._results.find(fn)},QueryList.prototype.reduce=function(fn,init){return this._results.reduce(fn,init)},QueryList.prototype.forEach=function(fn){this._results.forEach(fn)},QueryList.prototype.some=function(fn){return this._results.some(fn)},QueryList.prototype.toArray=function(){return this._results.slice()},QueryList.prototype[getSymbolIterator()]=function(){return this._results[getSymbolIterator()]()},QueryList.prototype.toString=function(){return this._results.toString()},QueryList.prototype.reset=function(res){this._results=flatten(res),this._dirty=!1},QueryList.prototype.notifyOnChanges=function(){this._emitter.emit(this)},QueryList.prototype.setDirty=function(){this._dirty=!0},Object.defineProperty(QueryList.prototype,"dirty",{get:function(){return this._dirty},enumerable:!0,configurable:!0}),QueryList}(),_SEPARATOR="#",FACTORY_CLASS_SUFFIX="NgFactory",SystemJsNgModuleLoaderConfig=function(){function SystemJsNgModuleLoaderConfig(){}return SystemJsNgModuleLoaderConfig}(),DEFAULT_CONFIG={factoryPathPrefix:"",factoryPathSuffix:".ngfactory"},SystemJsNgModuleLoader=function(){function SystemJsNgModuleLoader(_compiler,config){this._compiler=_compiler,this._config=config||DEFAULT_CONFIG}return SystemJsNgModuleLoader.prototype.load=function(path){var offlineMode=this._compiler instanceof Compiler;return offlineMode?this.loadFactory(path):this.loadAndCompile(path)},SystemJsNgModuleLoader.prototype.loadAndCompile=function(path){var _this=this,_a=path.split(_SEPARATOR),module=_a[0],exportName=_a[1];return void 0===exportName&&(exportName="default"),System.import(module).then(function(module){return module[exportName]}).then(function(type){return checkNotEmpty(type,module,exportName)}).then(function(type){return _this._compiler.compileModuleAsync(type)})},SystemJsNgModuleLoader.prototype.loadFactory=function(path){var _a=path.split(_SEPARATOR),module=_a[0],exportName=_a[1],factoryClassSuffix=FACTORY_CLASS_SUFFIX;return void 0===exportName&&(exportName="default",factoryClassSuffix=""),System.import(this._config.factoryPathPrefix+module+this._config.factoryPathSuffix).then(function(module){return module[exportName+factoryClassSuffix]}).then(function(factory){return checkNotEmpty(factory,module,exportName)})},SystemJsNgModuleLoader}();SystemJsNgModuleLoader.decorators=[{type:Injectable}],SystemJsNgModuleLoader.ctorParameters=function(){return[{type:Compiler},{type:SystemJsNgModuleLoaderConfig,decorators:[{type:Optional}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var TemplateRef=function(){function TemplateRef(){}return TemplateRef.prototype.elementRef=function(){},TemplateRef.prototype.createEmbeddedView=function(context){},TemplateRef}(),ViewContainerRef=function(){function ViewContainerRef(){}return ViewContainerRef.prototype.element=function(){},ViewContainerRef.prototype.injector=function(){},ViewContainerRef.prototype.parentInjector=function(){},ViewContainerRef.prototype.clear=function(){},ViewContainerRef.prototype.get=function(index){},ViewContainerRef.prototype.length=function(){},ViewContainerRef.prototype.createEmbeddedView=function(templateRef,context,index){},ViewContainerRef.prototype.createComponent=function(componentFactory,index,injector,projectableNodes,ngModule){},ViewContainerRef.prototype.insert=function(viewRef,index){},ViewContainerRef.prototype.move=function(viewRef,currentIndex){},ViewContainerRef.prototype.indexOf=function(viewRef){},ViewContainerRef.prototype.remove=function(index){},ViewContainerRef.prototype.detach=function(index){},ViewContainerRef}(),ChangeDetectorRef=function(){function ChangeDetectorRef(){}return ChangeDetectorRef.prototype.markForCheck=function(){},ChangeDetectorRef.prototype.detach=function(){},ChangeDetectorRef.prototype.detectChanges=function(){},ChangeDetectorRef.prototype.checkNoChanges=function(){},ChangeDetectorRef.prototype.reattach=function(){},ChangeDetectorRef}(),ViewRef=function(_super){function ViewRef(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(ViewRef,_super),ViewRef.prototype.destroy=function(){},ViewRef.prototype.destroyed=function(){},ViewRef.prototype.onDestroy=function(callback){},ViewRef}(ChangeDetectorRef),EmbeddedViewRef=function(_super){function EmbeddedViewRef(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(EmbeddedViewRef,_super),EmbeddedViewRef.prototype.context=function(){},EmbeddedViewRef.prototype.rootNodes=function(){},EmbeddedViewRef}(ViewRef),EventListener=function(){function EventListener(name,callback){this.name=name,this.callback=callback}return EventListener}(),DebugNode=function(){function DebugNode(nativeNode,parent,_debugContext){this._debugContext=_debugContext,this.nativeNode=nativeNode,parent&&parent instanceof DebugElement?parent.addChild(this):this.parent=null,this.listeners=[]}return Object.defineProperty(DebugNode.prototype,"injector",{get:function(){return this._debugContext.injector},enumerable:!0,configurable:!0}),Object.defineProperty(DebugNode.prototype,"componentInstance",{get:function(){return this._debugContext.component},enumerable:!0,configurable:!0}),Object.defineProperty(DebugNode.prototype,"context",{get:function(){return this._debugContext.context},enumerable:!0,configurable:!0}),Object.defineProperty(DebugNode.prototype,"references",{get:function(){return this._debugContext.references},enumerable:!0,configurable:!0}),Object.defineProperty(DebugNode.prototype,"providerTokens",{get:function(){return this._debugContext.providerTokens},enumerable:!0,configurable:!0}),Object.defineProperty(DebugNode.prototype,"source",{get:function(){return"Deprecated since v4"},enumerable:!0,configurable:!0}),DebugNode}(),DebugElement=function(_super){function DebugElement(nativeNode,parent,_debugContext){var _this=_super.call(this,nativeNode,parent,_debugContext)||this;return _this.properties={},_this.attributes={},_this.classes={},_this.styles={},_this.childNodes=[],_this.nativeElement=nativeNode,_this}return __extends(DebugElement,_super),DebugElement.prototype.addChild=function(child){child&&(this.childNodes.push(child),child.parent=this)},DebugElement.prototype.removeChild=function(child){var childIndex=this.childNodes.indexOf(child);childIndex!==-1&&(child.parent=null,this.childNodes.splice(childIndex,1))},DebugElement.prototype.insertChildrenAfter=function(child,newChildren){var _this=this,siblingIndex=this.childNodes.indexOf(child);siblingIndex!==-1&&((_a=this.childNodes).splice.apply(_a,[siblingIndex+1,0].concat(newChildren)),newChildren.forEach(function(c){c.parent&&c.parent.removeChild(c),c.parent=_this}));var _a},DebugElement.prototype.insertBefore=function(refChild,newChild){var refIndex=this.childNodes.indexOf(refChild);refIndex===-1?this.addChild(newChild):(newChild.parent&&newChild.parent.removeChild(newChild),newChild.parent=this,this.childNodes.splice(refIndex,0,newChild))},DebugElement.prototype.query=function(predicate){var results=this.queryAll(predicate);return results[0]||null},DebugElement.prototype.queryAll=function(predicate){var matches=[];return _queryElementChildren(this,predicate,matches),matches},DebugElement.prototype.queryAllNodes=function(predicate){var matches=[];return _queryNodeChildren(this,predicate,matches),matches},Object.defineProperty(DebugElement.prototype,"children",{get:function(){return this.childNodes.filter(function(node){return node instanceof DebugElement})},enumerable:!0,configurable:!0}),DebugElement.prototype.triggerEventHandler=function(eventName,eventObj){this.listeners.forEach(function(listener){listener.name==eventName&&listener.callback(eventObj)})},DebugElement}(DebugNode),_nativeNodeToDebugNode=new Map,WrappedValue=function(){function WrappedValue(wrapped){this.wrapped=wrapped}return WrappedValue.wrap=function(value){return new WrappedValue(value)},WrappedValue}(),ValueUnwrapper=function(){function ValueUnwrapper(){this.hasWrappedValue=!1}return ValueUnwrapper.prototype.unwrap=function(value){return value instanceof WrappedValue?(this.hasWrappedValue=!0,value.wrapped):value},ValueUnwrapper.prototype.reset=function(){this.hasWrappedValue=!1},ValueUnwrapper}(),SimpleChange=function(){function SimpleChange(previousValue,currentValue,firstChange){this.previousValue=previousValue,this.currentValue=currentValue,this.firstChange=firstChange}return SimpleChange.prototype.isFirstChange=function(){return this.firstChange},SimpleChange}(),DefaultIterableDifferFactory=function(){function DefaultIterableDifferFactory(){}return DefaultIterableDifferFactory.prototype.supports=function(obj){return isListLikeIterable(obj)},DefaultIterableDifferFactory.prototype.create=function(cdRefOrTrackBy,trackByFn){return new DefaultIterableDiffer(trackByFn||cdRefOrTrackBy)},DefaultIterableDifferFactory}(),trackByIdentity=function(index,item){return item},DefaultIterableDiffer=function(){function DefaultIterableDiffer(trackByFn){this._length=0,this._collection=null,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=trackByFn||trackByIdentity}return Object.defineProperty(DefaultIterableDiffer.prototype,"collection",{get:function(){return this._collection},enumerable:!0,configurable:!0}),Object.defineProperty(DefaultIterableDiffer.prototype,"length",{get:function(){return this._length},enumerable:!0,configurable:!0}),DefaultIterableDiffer.prototype.forEachItem=function(fn){var record;for(record=this._itHead;null!==record;record=record._next)fn(record)},DefaultIterableDiffer.prototype.forEachOperation=function(fn){for(var nextIt=this._itHead,nextRemove=this._removalsHead,addRemoveOffset=0,moveOffsets=null;nextIt||nextRemove;){var record=!nextRemove||nextIt&&nextIt.currentIndex<getPreviousIndex(nextRemove,addRemoveOffset,moveOffsets)?nextIt:nextRemove,adjPreviousIndex=getPreviousIndex(record,addRemoveOffset,moveOffsets),currentIndex=record.currentIndex;if(record===nextRemove)addRemoveOffset--,nextRemove=nextRemove._nextRemoved;else if(nextIt=nextIt._next,null==record.previousIndex)addRemoveOffset++;else{moveOffsets||(moveOffsets=[]);var localMovePreviousIndex=adjPreviousIndex-addRemoveOffset,localCurrentIndex=currentIndex-addRemoveOffset;if(localMovePreviousIndex!=localCurrentIndex){for(var i=0;i<localMovePreviousIndex;i++){var offset=i<moveOffsets.length?moveOffsets[i]:moveOffsets[i]=0,index=offset+i;localCurrentIndex<=index&&index<localMovePreviousIndex&&(moveOffsets[i]=offset+1)}var previousIndex=record.previousIndex;moveOffsets[previousIndex]=localCurrentIndex-localMovePreviousIndex}}adjPreviousIndex!==currentIndex&&fn(record,adjPreviousIndex,currentIndex)}},DefaultIterableDiffer.prototype.forEachPreviousItem=function(fn){var record;for(record=this._previousItHead;null!==record;record=record._nextPrevious)fn(record)},DefaultIterableDiffer.prototype.forEachAddedItem=function(fn){var record;for(record=this._additionsHead;null!==record;record=record._nextAdded)fn(record)},DefaultIterableDiffer.prototype.forEachMovedItem=function(fn){var record;for(record=this._movesHead;null!==record;record=record._nextMoved)fn(record)},DefaultIterableDiffer.prototype.forEachRemovedItem=function(fn){var record;for(record=this._removalsHead;null!==record;record=record._nextRemoved)fn(record)},DefaultIterableDiffer.prototype.forEachIdentityChange=function(fn){var record;for(record=this._identityChangesHead;null!==record;record=record._nextIdentityChange)fn(record)},DefaultIterableDiffer.prototype.diff=function(collection){if(null==collection&&(collection=[]),!isListLikeIterable(collection))throw new Error("Error trying to diff '"+stringify(collection)+"'. Only arrays and iterables are allowed");return this.check(collection)?this:null},DefaultIterableDiffer.prototype.onDestroy=function(){},DefaultIterableDiffer.prototype.check=function(collection){var _this=this;this._reset();var index,item,itemTrackBy,record=this._itHead,mayBeDirty=!1;if(Array.isArray(collection)){this._length=collection.length;for(var index_1=0;index_1<this._length;index_1++)item=collection[index_1],itemTrackBy=this._trackByFn(index_1,item),null!==record&&looseIdentical(record.trackById,itemTrackBy)?(mayBeDirty&&(record=this._verifyReinsertion(record,item,itemTrackBy,index_1)),looseIdentical(record.item,item)||this._addIdentityChange(record,item)):(record=this._mismatch(record,item,itemTrackBy,index_1),mayBeDirty=!0),record=record._next}else index=0,iterateListLike(collection,function(item){itemTrackBy=_this._trackByFn(index,item),null!==record&&looseIdentical(record.trackById,itemTrackBy)?(mayBeDirty&&(record=_this._verifyReinsertion(record,item,itemTrackBy,index)),looseIdentical(record.item,item)||_this._addIdentityChange(record,item)):(record=_this._mismatch(record,item,itemTrackBy,index),mayBeDirty=!0),record=record._next,index++}),this._length=index;return this._truncate(record),this._collection=collection,this.isDirty},Object.defineProperty(DefaultIterableDiffer.prototype,"isDirty",{get:function(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead},enumerable:!0,configurable:!0}),DefaultIterableDiffer.prototype._reset=function(){if(this.isDirty){var record=void 0,nextRecord=void 0;for(record=this._previousItHead=this._itHead;null!==record;record=record._next)record._nextPrevious=record._next;for(record=this._additionsHead;null!==record;record=record._nextAdded)record.previousIndex=record.currentIndex;for(this._additionsHead=this._additionsTail=null,record=this._movesHead;null!==record;record=nextRecord)record.previousIndex=record.currentIndex,nextRecord=record._nextMoved;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}},DefaultIterableDiffer.prototype._mismatch=function(record,item,itemTrackBy,index){var previousRecord;return null===record?previousRecord=this._itTail:(previousRecord=record._prev,this._remove(record)),record=null===this._linkedRecords?null:this._linkedRecords.get(itemTrackBy,index),null!==record?(looseIdentical(record.item,item)||this._addIdentityChange(record,item),this._moveAfter(record,previousRecord,index)):(record=null===this._unlinkedRecords?null:this._unlinkedRecords.get(itemTrackBy,null),null!==record?(looseIdentical(record.item,item)||this._addIdentityChange(record,item),this._reinsertAfter(record,previousRecord,index)):record=this._addAfter(new IterableChangeRecord_(item,itemTrackBy),previousRecord,index)),record},DefaultIterableDiffer.prototype._verifyReinsertion=function(record,item,itemTrackBy,index){var reinsertRecord=null===this._unlinkedRecords?null:this._unlinkedRecords.get(itemTrackBy,null);return null!==reinsertRecord?record=this._reinsertAfter(reinsertRecord,record._prev,index):record.currentIndex!=index&&(record.currentIndex=index,this._addToMoves(record,index)),record},DefaultIterableDiffer.prototype._truncate=function(record){for(;null!==record;){var nextRecord=record._next;this._addToRemovals(this._unlink(record)),record=nextRecord}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)},DefaultIterableDiffer.prototype._reinsertAfter=function(record,prevRecord,index){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(record);var prev=record._prevRemoved,next=record._nextRemoved;return null===prev?this._removalsHead=next:prev._nextRemoved=next,null===next?this._removalsTail=prev:next._prevRemoved=prev,this._insertAfter(record,prevRecord,index),this._addToMoves(record,index),record},DefaultIterableDiffer.prototype._moveAfter=function(record,prevRecord,index){return this._unlink(record),this._insertAfter(record,prevRecord,index),this._addToMoves(record,index),record},DefaultIterableDiffer.prototype._addAfter=function(record,prevRecord,index){return this._insertAfter(record,prevRecord,index),null===this._additionsTail?this._additionsTail=this._additionsHead=record:this._additionsTail=this._additionsTail._nextAdded=record,record},DefaultIterableDiffer.prototype._insertAfter=function(record,prevRecord,index){var next=null===prevRecord?this._itHead:prevRecord._next;return record._next=next,record._prev=prevRecord,null===next?this._itTail=record:next._prev=record,null===prevRecord?this._itHead=record:prevRecord._next=record,null===this._linkedRecords&&(this._linkedRecords=new _DuplicateMap),this._linkedRecords.put(record),record.currentIndex=index,record},DefaultIterableDiffer.prototype._remove=function(record){return this._addToRemovals(this._unlink(record))},DefaultIterableDiffer.prototype._unlink=function(record){null!==this._linkedRecords&&this._linkedRecords.remove(record);var prev=record._prev,next=record._next;return null===prev?this._itHead=next:prev._next=next,null===next?this._itTail=prev:next._prev=prev,record},DefaultIterableDiffer.prototype._addToMoves=function(record,toIndex){return record.previousIndex===toIndex?record:(null===this._movesTail?this._movesTail=this._movesHead=record:this._movesTail=this._movesTail._nextMoved=record,record)},DefaultIterableDiffer.prototype._addToRemovals=function(record){return null===this._unlinkedRecords&&(this._unlinkedRecords=new _DuplicateMap),this._unlinkedRecords.put(record),record.currentIndex=null,record._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=record,record._prevRemoved=null):(record._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=record),record},DefaultIterableDiffer.prototype._addIdentityChange=function(record,item){return record.item=item,null===this._identityChangesTail?this._identityChangesTail=this._identityChangesHead=record:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=record,record},DefaultIterableDiffer.prototype.toString=function(){var list=[];this.forEachItem(function(record){return list.push(record)});var previous=[];this.forEachPreviousItem(function(record){return previous.push(record)});var additions=[];this.forEachAddedItem(function(record){return additions.push(record)});var moves=[];this.forEachMovedItem(function(record){return moves.push(record)});var removals=[];this.forEachRemovedItem(function(record){return removals.push(record)});var identityChanges=[];return this.forEachIdentityChange(function(record){return identityChanges.push(record)}),"collection: "+list.join(", ")+"\nprevious: "+previous.join(", ")+"\nadditions: "+additions.join(", ")+"\nmoves: "+moves.join(", ")+"\nremovals: "+removals.join(", ")+"\nidentityChanges: "+identityChanges.join(", ")+"\n"},DefaultIterableDiffer}(),IterableChangeRecord_=function(){function IterableChangeRecord_(item,trackById){this.item=item,this.trackById=trackById,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}return IterableChangeRecord_.prototype.toString=function(){return this.previousIndex===this.currentIndex?stringify(this.item):stringify(this.item)+"["+stringify(this.previousIndex)+"->"+stringify(this.currentIndex)+"]"},IterableChangeRecord_}(),_DuplicateItemRecordList=function(){function _DuplicateItemRecordList(){this._head=null,this._tail=null}return _DuplicateItemRecordList.prototype.add=function(record){null===this._head?(this._head=this._tail=record,record._nextDup=null,record._prevDup=null):(this._tail._nextDup=record,record._prevDup=this._tail,record._nextDup=null,this._tail=record)},_DuplicateItemRecordList.prototype.get=function(trackById,afterIndex){var record;for(record=this._head;null!==record;record=record._nextDup)if((null===afterIndex||afterIndex<record.currentIndex)&&looseIdentical(record.trackById,trackById))return record;return null},_DuplicateItemRecordList.prototype.remove=function(record){var prev=record._prevDup,next=record._nextDup;return null===prev?this._head=next:prev._nextDup=next,null===next?this._tail=prev:next._prevDup=prev,null===this._head},_DuplicateItemRecordList}(),_DuplicateMap=function(){function _DuplicateMap(){this.map=new Map}return _DuplicateMap.prototype.put=function(record){var key=record.trackById,duplicates=this.map.get(key);duplicates||(duplicates=new _DuplicateItemRecordList,this.map.set(key,duplicates)),duplicates.add(record)},_DuplicateMap.prototype.get=function(trackById,afterIndex){var key=trackById,recordList=this.map.get(key);return recordList?recordList.get(trackById,afterIndex):null},_DuplicateMap.prototype.remove=function(record){var key=record.trackById,recordList=this.map.get(key);return recordList.remove(record)&&this.map.delete(key),record},Object.defineProperty(_DuplicateMap.prototype,"isEmpty",{get:function(){return 0===this.map.size},enumerable:!0,configurable:!0}),_DuplicateMap.prototype.clear=function(){this.map.clear()},_DuplicateMap.prototype.toString=function(){return"_DuplicateMap("+stringify(this.map)+")"},_DuplicateMap}(),DefaultKeyValueDifferFactory=function(){function DefaultKeyValueDifferFactory(){}return DefaultKeyValueDifferFactory.prototype.supports=function(obj){return obj instanceof Map||isJsObject(obj)},DefaultKeyValueDifferFactory.prototype.create=function(cd){return new DefaultKeyValueDiffer},DefaultKeyValueDifferFactory}(),DefaultKeyValueDiffer=function(){function DefaultKeyValueDiffer(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}return Object.defineProperty(DefaultKeyValueDiffer.prototype,"isDirty",{get:function(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead},enumerable:!0,configurable:!0}),DefaultKeyValueDiffer.prototype.forEachItem=function(fn){var record;for(record=this._mapHead;null!==record;record=record._next)fn(record)},DefaultKeyValueDiffer.prototype.forEachPreviousItem=function(fn){var record;for(record=this._previousMapHead;null!==record;record=record._nextPrevious)fn(record)},DefaultKeyValueDiffer.prototype.forEachChangedItem=function(fn){var record;for(record=this._changesHead;null!==record;record=record._nextChanged)fn(record)},DefaultKeyValueDiffer.prototype.forEachAddedItem=function(fn){var record;for(record=this._additionsHead;null!==record;record=record._nextAdded)fn(record)},DefaultKeyValueDiffer.prototype.forEachRemovedItem=function(fn){var record;for(record=this._removalsHead;null!==record;record=record._nextRemoved)fn(record)},DefaultKeyValueDiffer.prototype.diff=function(map){if(map){if(!(map instanceof Map||isJsObject(map)))throw new Error("Error trying to diff '"+stringify(map)+"'. Only maps and objects are allowed")}else map=new Map;return this.check(map)?this:null},DefaultKeyValueDiffer.prototype.onDestroy=function(){},DefaultKeyValueDiffer.prototype.check=function(map){var _this=this;this._reset();var insertBefore=this._mapHead;if(this._appendAfter=null,this._forEach(map,function(value,key){if(insertBefore&&insertBefore.key===key)_this._maybeAddToChanges(insertBefore,value),_this._appendAfter=insertBefore,insertBefore=insertBefore._next;else{var record=_this._getOrCreateRecordForKey(key,value);insertBefore=_this._insertBeforeOrAppend(insertBefore,record)}}),insertBefore){insertBefore._prev&&(insertBefore._prev._next=null),this._removalsHead=insertBefore;for(var record=insertBefore;null!==record;record=record._nextRemoved)record===this._mapHead&&(this._mapHead=null),this._records.delete(record.key),record._nextRemoved=record._next,record.previousValue=record.currentValue,record.currentValue=null,record._prev=null,record._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty},DefaultKeyValueDiffer.prototype._insertBeforeOrAppend=function(before,record){if(before){var prev=before._prev;return record._next=before,record._prev=prev,before._prev=record,prev&&(prev._next=record),before===this._mapHead&&(this._mapHead=record),this._appendAfter=before,before}return this._appendAfter?(this._appendAfter._next=record,record._prev=this._appendAfter):this._mapHead=record,this._appendAfter=record,null},DefaultKeyValueDiffer.prototype._getOrCreateRecordForKey=function(key,value){if(this._records.has(key)){var record_1=this._records.get(key);this._maybeAddToChanges(record_1,value);var prev=record_1._prev,next=record_1._next;return prev&&(prev._next=next),next&&(next._prev=prev),record_1._next=null,record_1._prev=null,record_1}var record=new KeyValueChangeRecord_(key);return this._records.set(key,record),record.currentValue=value,this._addToAdditions(record),record},DefaultKeyValueDiffer.prototype._reset=function(){if(this.isDirty){var record=void 0;for(this._previousMapHead=this._mapHead,record=this._previousMapHead;null!==record;record=record._next)record._nextPrevious=record._next;for(record=this._changesHead;null!==record;record=record._nextChanged)record.previousValue=record.currentValue;for(record=this._additionsHead;null!=record;record=record._nextAdded)record.previousValue=record.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}},DefaultKeyValueDiffer.prototype._maybeAddToChanges=function(record,newValue){looseIdentical(newValue,record.currentValue)||(record.previousValue=record.currentValue,record.currentValue=newValue,this._addToChanges(record))},DefaultKeyValueDiffer.prototype._addToAdditions=function(record){null===this._additionsHead?this._additionsHead=this._additionsTail=record:(this._additionsTail._nextAdded=record,this._additionsTail=record)},DefaultKeyValueDiffer.prototype._addToChanges=function(record){null===this._changesHead?this._changesHead=this._changesTail=record:(this._changesTail._nextChanged=record,this._changesTail=record)},DefaultKeyValueDiffer.prototype._forEach=function(obj,fn){obj instanceof Map?obj.forEach(fn):Object.keys(obj).forEach(function(k){return fn(obj[k],k)})},DefaultKeyValueDiffer}(),KeyValueChangeRecord_=function(){function KeyValueChangeRecord_(key){this.key=key,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}return KeyValueChangeRecord_}(),IterableDiffers=function(){function IterableDiffers(factories){this.factories=factories}return IterableDiffers.create=function(factories,parent){if(null!=parent){var copied=parent.factories.slice();return factories=factories.concat(copied),new IterableDiffers(factories)}return new IterableDiffers(factories)},IterableDiffers.extend=function(factories){return{provide:IterableDiffers,useFactory:function(parent){if(!parent)throw new Error("Cannot extend IterableDiffers without a parent injector");return IterableDiffers.create(factories,parent)},deps:[[IterableDiffers,new SkipSelf,new Optional]]}},IterableDiffers.prototype.find=function(iterable){var factory=this.factories.find(function(f){return f.supports(iterable)});if(null!=factory)return factory;throw new Error("Cannot find a differ supporting object '"+iterable+"' of type '"+getTypeNameForDebugging(iterable)+"'")},IterableDiffers}(),KeyValueDiffers=function(){function KeyValueDiffers(factories){this.factories=factories}return KeyValueDiffers.create=function(factories,parent){if(parent){var copied=parent.factories.slice();factories=factories.concat(copied)}return new KeyValueDiffers(factories)},KeyValueDiffers.extend=function(factories){return{provide:KeyValueDiffers,useFactory:function(parent){if(!parent)throw new Error("Cannot extend KeyValueDiffers without a parent injector");return KeyValueDiffers.create(factories,parent)},deps:[[KeyValueDiffers,new SkipSelf,new Optional]]}},KeyValueDiffers.prototype.find=function(kv){var factory=this.factories.find(function(f){return f.supports(kv)});if(factory)return factory;throw new Error("Cannot find a differ supporting object '"+kv+"'")},KeyValueDiffers}(),keyValDiff=[new DefaultKeyValueDifferFactory],iterableDiff=[new DefaultIterableDifferFactory],defaultIterableDiffers=new IterableDiffers(iterableDiff),defaultKeyValueDiffers=new KeyValueDiffers(keyValDiff),_CORE_PLATFORM_PROVIDERS=[{provide:PLATFORM_ID,useValue:"unknown"},PlatformRef_,{provide:PlatformRef,useExisting:PlatformRef_},{provide:Reflector,useFactory:_reflector,deps:[]},TestabilityRegistry,Console],platformCore=createPlatformFactory(null,"core",_CORE_PLATFORM_PROVIDERS),LOCALE_ID=new InjectionToken("LocaleId"),TRANSLATIONS=new InjectionToken("Translations"),TRANSLATIONS_FORMAT=new InjectionToken("TranslationsFormat"),MissingTranslationStrategy={};MissingTranslationStrategy.Error=0,MissingTranslationStrategy.Warning=1,MissingTranslationStrategy.Ignore=2,MissingTranslationStrategy[MissingTranslationStrategy.Error]="Error",MissingTranslationStrategy[MissingTranslationStrategy.Warning]="Warning",MissingTranslationStrategy[MissingTranslationStrategy.Ignore]="Ignore";var ApplicationModule=function(){function ApplicationModule(appRef){}return ApplicationModule}();ApplicationModule.decorators=[{type:NgModule,args:[{providers:[ApplicationRef_,{provide:ApplicationRef,useExisting:ApplicationRef_},ApplicationInitStatus,Compiler,APP_ID_RANDOM_PROVIDER,{provide:IterableDiffers,useFactory:_iterableDiffersFactory},{provide:KeyValueDiffers,useFactory:_keyValueDiffersFactory},{provide:LOCALE_ID,useFactory:_localeFactory,deps:[[new Inject(LOCALE_ID),new Optional,new SkipSelf]]}]}]}],ApplicationModule.ctorParameters=function(){return[{type:ApplicationRef}]};var SecurityContext={};SecurityContext.NONE=0,SecurityContext.HTML=1,SecurityContext.STYLE=2,SecurityContext.SCRIPT=3,SecurityContext.URL=4,SecurityContext.RESOURCE_URL=5,SecurityContext[SecurityContext.NONE]="NONE",SecurityContext[SecurityContext.HTML]="HTML",SecurityContext[SecurityContext.STYLE]="STYLE",SecurityContext[SecurityContext.SCRIPT]="SCRIPT",SecurityContext[SecurityContext.URL]="URL",SecurityContext[SecurityContext.RESOURCE_URL]="RESOURCE_URL";var Sanitizer=function(){function Sanitizer(){}return Sanitizer.prototype.sanitize=function(context,value){},Sanitizer}(),DebugContext=function(){function DebugContext(){}return DebugContext.prototype.view=function(){},DebugContext.prototype.nodeIndex=function(){},DebugContext.prototype.injector=function(){},DebugContext.prototype.component=function(){},DebugContext.prototype.providerTokens=function(){},DebugContext.prototype.references=function(){},DebugContext.prototype.context=function(){},DebugContext.prototype.componentRenderElement=function(){},DebugContext.prototype.renderNode=function(){},DebugContext.prototype.logError=function(console){for(var values=[],_i=1;_i<arguments.length;_i++)values[_i-1]=arguments[_i]},DebugContext}(),Services={setCurrentNode:void 0,createRootView:void 0,createEmbeddedView:void 0,createComponentView:void 0,createNgModuleRef:void 0,overrideProvider:void 0,clearProviderOverrides:void 0,checkAndUpdateView:void 0,checkNoChangesView:void 0,destroyView:void 0,resolveDep:void 0,createDebugContext:void 0,handleEvent:void 0,updateDirectives:void 0,updateRenderer:void 0,dirtyParentQueries:void 0},NOOP=function(){},_tokenKeyCache=new Map,UNDEFINED_RENDERER_TYPE_ID="$$undefined",EMPTY_RENDERER_TYPE_ID="$$empty",_renderCompCount=0,DEFINITION_CACHE=new WeakMap,NS_PREFIX_RE=/^:([^:]+):(.+)$/,EMPTY_ARRAY=[],EMPTY_MAP={},NOT_CREATED$1=new Object,InjectorRefTokenKey$1=tokenKey(Injector),NgModuleRefTokenKey=tokenKey(NgModuleRef),EMPTY_CONTEXT=new Object,ComponentFactory_=function(_super){function ComponentFactory_(selector,componentType,viewDefFactory,_inputs,_outputs,ngContentSelectors){var _this=_super.call(this)||this;return _this.selector=selector,_this.componentType=componentType,_this._inputs=_inputs,_this._outputs=_outputs,_this.ngContentSelectors=ngContentSelectors,_this.viewDefFactory=viewDefFactory,_this}return __extends(ComponentFactory_,_super),Object.defineProperty(ComponentFactory_.prototype,"inputs",{get:function(){var inputsArr=[],inputs=this._inputs;for(var propName in inputs){var templateName=inputs[propName];inputsArr.push({propName:propName,templateName:templateName})}return inputsArr},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentFactory_.prototype,"outputs",{get:function(){var outputsArr=[];for(var propName in this._outputs){var templateName=this._outputs[propName];outputsArr.push({propName:propName,templateName:templateName})}return outputsArr},enumerable:!0,configurable:!0}),ComponentFactory_.prototype.create=function(injector,projectableNodes,rootSelectorOrNode,ngModule){if(!ngModule)throw new Error("ngModule should be provided");var viewDef=resolveDefinition(this.viewDefFactory),componentNodeIndex=viewDef.nodes[0].element.componentProvider.index,view=Services.createRootView(injector,projectableNodes||[],rootSelectorOrNode,viewDef,ngModule,EMPTY_CONTEXT),component=asProviderData(view,componentNodeIndex).instance;return rootSelectorOrNode&&view.renderer.setAttribute(asElementData(view,0).renderElement,"ng-version",VERSION.full),new ComponentRef_(view,new ViewRef_(view),component)},ComponentFactory_}(ComponentFactory),ComponentRef_=function(_super){function ComponentRef_(_view,_viewRef,_component){var _this=_super.call(this)||this;return _this._view=_view,_this._viewRef=_viewRef,_this._component=_component,_this._elDef=_this._view.def.nodes[0],_this}return __extends(ComponentRef_,_super),Object.defineProperty(ComponentRef_.prototype,"location",{get:function(){return new ElementRef(asElementData(this._view,this._elDef.index).renderElement)},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentRef_.prototype,"injector",{get:function(){return new Injector_(this._view,this._elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentRef_.prototype,"instance",{get:function(){return this._component},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentRef_.prototype,"hostView",{get:function(){
return this._viewRef},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentRef_.prototype,"changeDetectorRef",{get:function(){return this._viewRef},enumerable:!0,configurable:!0}),Object.defineProperty(ComponentRef_.prototype,"componentType",{get:function(){return this._component.constructor},enumerable:!0,configurable:!0}),ComponentRef_.prototype.destroy=function(){this._viewRef.destroy()},ComponentRef_.prototype.onDestroy=function(callback){this._viewRef.onDestroy(callback)},ComponentRef_}(ComponentRef),ViewContainerRef_=function(){function ViewContainerRef_(_view,_elDef,_data){this._view=_view,this._elDef=_elDef,this._data=_data,this._embeddedViews=[]}return Object.defineProperty(ViewContainerRef_.prototype,"element",{get:function(){return new ElementRef(this._data.renderElement)},enumerable:!0,configurable:!0}),Object.defineProperty(ViewContainerRef_.prototype,"injector",{get:function(){return new Injector_(this._view,this._elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(ViewContainerRef_.prototype,"parentInjector",{get:function(){for(var view=this._view,elDef=this._elDef.parent;!elDef&&view;)elDef=viewParentEl(view),view=view.parent;return view?new Injector_(view,elDef):new Injector_(this._view,null)},enumerable:!0,configurable:!0}),ViewContainerRef_.prototype.clear=function(){for(var len=this._embeddedViews.length,i=len-1;i>=0;i--){var view=detachEmbeddedView(this._data,i);Services.destroyView(view)}},ViewContainerRef_.prototype.get=function(index){var view=this._embeddedViews[index];if(view){var ref=new ViewRef_(view);return ref.attachToViewContainerRef(this),ref}return null},Object.defineProperty(ViewContainerRef_.prototype,"length",{get:function(){return this._embeddedViews.length},enumerable:!0,configurable:!0}),ViewContainerRef_.prototype.createEmbeddedView=function(templateRef,context,index){var viewRef=templateRef.createEmbeddedView(context||{});return this.insert(viewRef,index),viewRef},ViewContainerRef_.prototype.createComponent=function(componentFactory,index,injector,projectableNodes,ngModuleRef){var contextInjector=injector||this.parentInjector;ngModuleRef||componentFactory instanceof ComponentFactoryBoundToModule||(ngModuleRef=contextInjector.get(NgModuleRef));var componentRef=componentFactory.create(contextInjector,projectableNodes,void 0,ngModuleRef);return this.insert(componentRef.hostView,index),componentRef},ViewContainerRef_.prototype.insert=function(viewRef,index){var viewRef_=viewRef,viewData=viewRef_._view;return attachEmbeddedView(this._view,this._data,index,viewData),viewRef_.attachToViewContainerRef(this),viewRef},ViewContainerRef_.prototype.move=function(viewRef,currentIndex){var previousIndex=this._embeddedViews.indexOf(viewRef._view);return moveEmbeddedView(this._data,previousIndex,currentIndex),viewRef},ViewContainerRef_.prototype.indexOf=function(viewRef){return this._embeddedViews.indexOf(viewRef._view)},ViewContainerRef_.prototype.remove=function(index){var viewData=detachEmbeddedView(this._data,index);viewData&&Services.destroyView(viewData)},ViewContainerRef_.prototype.detach=function(index){var view=detachEmbeddedView(this._data,index);return view?new ViewRef_(view):null},ViewContainerRef_}(),ViewRef_=function(){function ViewRef_(_view){this._view=_view,this._viewContainerRef=null,this._appRef=null}return Object.defineProperty(ViewRef_.prototype,"rootNodes",{get:function(){return rootRenderNodes(this._view)},enumerable:!0,configurable:!0}),Object.defineProperty(ViewRef_.prototype,"context",{get:function(){return this._view.context},enumerable:!0,configurable:!0}),Object.defineProperty(ViewRef_.prototype,"destroyed",{get:function(){return 0!==(128&this._view.state)},enumerable:!0,configurable:!0}),ViewRef_.prototype.markForCheck=function(){markParentViewsForCheck(this._view)},ViewRef_.prototype.detach=function(){this._view.state&=-5},ViewRef_.prototype.detectChanges=function(){var fs=this._view.root.rendererFactory;fs.begin&&fs.begin(),Services.checkAndUpdateView(this._view),fs.end&&fs.end()},ViewRef_.prototype.checkNoChanges=function(){Services.checkNoChangesView(this._view)},ViewRef_.prototype.reattach=function(){this._view.state|=4},ViewRef_.prototype.onDestroy=function(callback){this._view.disposables||(this._view.disposables=[]),this._view.disposables.push(callback)},ViewRef_.prototype.destroy=function(){this._appRef?this._appRef.detachView(this):this._viewContainerRef&&this._viewContainerRef.detach(this._viewContainerRef.indexOf(this)),Services.destroyView(this._view)},ViewRef_.prototype.detachFromAppRef=function(){this._appRef=null,renderDetachView(this._view),Services.dirtyParentQueries(this._view)},ViewRef_.prototype.attachToAppRef=function(appRef){if(this._viewContainerRef)throw new Error("This view is already attached to a ViewContainer!");this._appRef=appRef},ViewRef_.prototype.attachToViewContainerRef=function(vcRef){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._viewContainerRef=vcRef},ViewRef_}(),TemplateRef_=function(_super){function TemplateRef_(_parentView,_def){var _this=_super.call(this)||this;return _this._parentView=_parentView,_this._def=_def,_this}return __extends(TemplateRef_,_super),TemplateRef_.prototype.createEmbeddedView=function(context){return new ViewRef_(Services.createEmbeddedView(this._parentView,this._def,this._def.element.template,context))},Object.defineProperty(TemplateRef_.prototype,"elementRef",{get:function(){return new ElementRef(asElementData(this._parentView,this._def.index).renderElement)},enumerable:!0,configurable:!0}),TemplateRef_}(TemplateRef),Injector_=function(){function Injector_(view,elDef){this.view=view,this.elDef=elDef}return Injector_.prototype.get=function(token,notFoundValue){void 0===notFoundValue&&(notFoundValue=Injector.THROW_IF_NOT_FOUND);var allowPrivateServices=!!this.elDef&&0!==(33554432&this.elDef.flags);return Services.resolveDep(this.view,this.elDef,allowPrivateServices,{flags:0,token:token,tokenKey:tokenKey(token)},notFoundValue)},Injector_}(),RendererAdapter=function(){function RendererAdapter(delegate){this.delegate=delegate}return RendererAdapter.prototype.selectRootElement=function(selectorOrNode){return this.delegate.selectRootElement(selectorOrNode)},RendererAdapter.prototype.createElement=function(parent,namespaceAndName){var _a=splitNamespace(namespaceAndName),ns=_a[0],name=_a[1],el=this.delegate.createElement(name,ns);return parent&&this.delegate.appendChild(parent,el),el},RendererAdapter.prototype.createViewRoot=function(hostElement){return hostElement},RendererAdapter.prototype.createTemplateAnchor=function(parentElement){var comment=this.delegate.createComment("");return parentElement&&this.delegate.appendChild(parentElement,comment),comment},RendererAdapter.prototype.createText=function(parentElement,value){var node=this.delegate.createText(value);return parentElement&&this.delegate.appendChild(parentElement,node),node},RendererAdapter.prototype.projectNodes=function(parentElement,nodes){for(var i=0;i<nodes.length;i++)this.delegate.appendChild(parentElement,nodes[i])},RendererAdapter.prototype.attachViewAfter=function(node,viewRootNodes){for(var parentElement=this.delegate.parentNode(node),nextSibling=this.delegate.nextSibling(node),i=0;i<viewRootNodes.length;i++)this.delegate.insertBefore(parentElement,viewRootNodes[i],nextSibling)},RendererAdapter.prototype.detachView=function(viewRootNodes){for(var i=0;i<viewRootNodes.length;i++){var node=viewRootNodes[i],parentElement=this.delegate.parentNode(node);this.delegate.removeChild(parentElement,node)}},RendererAdapter.prototype.destroyView=function(hostElement,viewAllNodes){for(var i=0;i<viewAllNodes.length;i++)this.delegate.destroyNode(viewAllNodes[i])},RendererAdapter.prototype.listen=function(renderElement,name,callback){return this.delegate.listen(renderElement,name,callback)},RendererAdapter.prototype.listenGlobal=function(target,name,callback){return this.delegate.listen(target,name,callback)},RendererAdapter.prototype.setElementProperty=function(renderElement,propertyName,propertyValue){this.delegate.setProperty(renderElement,propertyName,propertyValue)},RendererAdapter.prototype.setElementAttribute=function(renderElement,namespaceAndName,attributeValue){var _a=splitNamespace(namespaceAndName),ns=_a[0],name=_a[1];null!=attributeValue?this.delegate.setAttribute(renderElement,name,attributeValue,ns):this.delegate.removeAttribute(renderElement,name,ns)},RendererAdapter.prototype.setBindingDebugInfo=function(renderElement,propertyName,propertyValue){},RendererAdapter.prototype.setElementClass=function(renderElement,className,isAdd){isAdd?this.delegate.addClass(renderElement,className):this.delegate.removeClass(renderElement,className)},RendererAdapter.prototype.setElementStyle=function(renderElement,styleName,styleValue){null!=styleValue?this.delegate.setStyle(renderElement,styleName,styleValue):this.delegate.removeStyle(renderElement,styleName)},RendererAdapter.prototype.invokeElementMethod=function(renderElement,methodName,args){renderElement[methodName].apply(renderElement,args)},RendererAdapter.prototype.setText=function(renderNode$$1,text){this.delegate.setValue(renderNode$$1,text)},RendererAdapter.prototype.animate=function(){throw new Error("Renderer.animate is no longer supported!")},RendererAdapter}(),NgModuleRef_=function(){function NgModuleRef_(_moduleType,_parent,_bootstrapComponents,_def){this._moduleType=_moduleType,this._parent=_parent,this._bootstrapComponents=_bootstrapComponents,this._def=_def,this._destroyListeners=[],this._destroyed=!1,initNgModule(this)}return NgModuleRef_.prototype.get=function(token,notFoundValue){return void 0===notFoundValue&&(notFoundValue=Injector.THROW_IF_NOT_FOUND),resolveNgModuleDep(this,{token:token,tokenKey:tokenKey(token),flags:0},notFoundValue)},Object.defineProperty(NgModuleRef_.prototype,"instance",{get:function(){return this.get(this._moduleType)},enumerable:!0,configurable:!0}),Object.defineProperty(NgModuleRef_.prototype,"componentFactoryResolver",{get:function(){return this.get(ComponentFactoryResolver)},enumerable:!0,configurable:!0}),Object.defineProperty(NgModuleRef_.prototype,"injector",{get:function(){return this},enumerable:!0,configurable:!0}),NgModuleRef_.prototype.destroy=function(){if(this._destroyed)throw new Error("The ng module "+stringify(this.instance.constructor)+" has already been destroyed.");this._destroyed=!0,callNgModuleLifecycle(this,131072),this._destroyListeners.forEach(function(listener){return listener()})},NgModuleRef_.prototype.onDestroy=function(callback){this._destroyListeners.push(callback)},NgModuleRef_}(),RendererV1TokenKey=tokenKey(Renderer),Renderer2TokenKey=tokenKey(Renderer2),ElementRefTokenKey=tokenKey(ElementRef),ViewContainerRefTokenKey=tokenKey(ViewContainerRef),TemplateRefTokenKey=tokenKey(TemplateRef),ChangeDetectorRefTokenKey=tokenKey(ChangeDetectorRef),InjectorRefTokenKey=tokenKey(Injector),NOT_CREATED=new Object,NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR={},ViewAction={};ViewAction.CreateViewNodes=0,ViewAction.CheckNoChanges=1,ViewAction.CheckNoChangesProjectedViews=2,ViewAction.CheckAndUpdate=3,ViewAction.CheckAndUpdateProjectedViews=4,ViewAction.Destroy=5,ViewAction[ViewAction.CreateViewNodes]="CreateViewNodes",ViewAction[ViewAction.CheckNoChanges]="CheckNoChanges",ViewAction[ViewAction.CheckNoChangesProjectedViews]="CheckNoChangesProjectedViews",ViewAction[ViewAction.CheckAndUpdate]="CheckAndUpdate",ViewAction[ViewAction.CheckAndUpdateProjectedViews]="CheckAndUpdateProjectedViews",ViewAction[ViewAction.Destroy]="Destroy";/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var initialized=!1,providerOverrides=new Map,DebugAction={};DebugAction.create=0,DebugAction.detectChanges=1,DebugAction.checkNoChanges=2,DebugAction.destroy=3,DebugAction.handleEvent=4,DebugAction[DebugAction.create]="create",DebugAction[DebugAction.detectChanges]="detectChanges",DebugAction[DebugAction.checkNoChanges]="checkNoChanges",DebugAction[DebugAction.destroy]="destroy",DebugAction[DebugAction.handleEvent]="handleEvent";var _currentAction,_currentView,_currentNodeIndex,CAMEL_CASE_REGEXP=/([A-Z])/g,DebugContext_=function(){function DebugContext_(view,nodeIndex){this.view=view,this.nodeIndex=nodeIndex,null==nodeIndex&&(this.nodeIndex=nodeIndex=0),this.nodeDef=view.def.nodes[nodeIndex];for(var elDef=this.nodeDef,elView=view;elDef&&0===(1&elDef.flags);)elDef=elDef.parent;if(!elDef)for(;!elDef&&elView;)elDef=viewParentEl(elView),elView=elView.parent;this.elDef=elDef,this.elView=elView}return Object.defineProperty(DebugContext_.prototype,"elOrCompView",{get:function(){return asElementData(this.elView,this.elDef.index).componentView||this.view},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"injector",{get:function(){return createInjector(this.elView,this.elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"component",{get:function(){return this.elOrCompView.component},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"context",{get:function(){return this.elOrCompView.context},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"providerTokens",{get:function(){var tokens=[];if(this.elDef)for(var i=this.elDef.index+1;i<=this.elDef.index+this.elDef.childCount;i++){var childDef=this.elView.def.nodes[i];20224&childDef.flags&&tokens.push(childDef.provider.token),i+=childDef.childCount}return tokens},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"references",{get:function(){var references={};if(this.elDef){collectReferences(this.elView,this.elDef,references);for(var i=this.elDef.index+1;i<=this.elDef.index+this.elDef.childCount;i++){var childDef=this.elView.def.nodes[i];20224&childDef.flags&&collectReferences(this.elView,childDef,references),i+=childDef.childCount}}return references},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"componentRenderElement",{get:function(){var elData=findHostElement(this.elOrCompView);return elData?elData.renderElement:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(DebugContext_.prototype,"renderNode",{get:function(){return 2&this.nodeDef.flags?renderNode(this.view,this.nodeDef):renderNode(this.elView,this.elDef)},enumerable:!0,configurable:!0}),DebugContext_.prototype.logError=function(console){for(var values=[],_i=1;_i<arguments.length;_i++)values[_i-1]=arguments[_i];var logViewDef,logNodeIndex;2&this.nodeDef.flags?(logViewDef=this.view.def,logNodeIndex=this.nodeDef.index):(logViewDef=this.elView.def,logNodeIndex=this.elDef.index);var renderNodeIndex=getRenderNodeIndex(logViewDef,logNodeIndex),currRenderNodeIndex=-1,nodeLogger=function(){return currRenderNodeIndex++,currRenderNodeIndex===renderNodeIndex?(_a=console.error).bind.apply(_a,[console].concat(values)):NOOP;var _a};logViewDef.factory(nodeLogger),currRenderNodeIndex<renderNodeIndex&&(console.error("Illegal state: the ViewDefinitionFactory did not call the logger!"),console.error.apply(console,values))},DebugContext_}(),DebugRendererFactory2=function(){function DebugRendererFactory2(delegate){this.delegate=delegate}return DebugRendererFactory2.prototype.createRenderer=function(element,renderData){return new DebugRenderer2(this.delegate.createRenderer(element,renderData))},DebugRendererFactory2.prototype.begin=function(){this.delegate.begin&&this.delegate.begin()},DebugRendererFactory2.prototype.end=function(){this.delegate.end&&this.delegate.end()},DebugRendererFactory2.prototype.whenRenderingDone=function(){return this.delegate.whenRenderingDone?this.delegate.whenRenderingDone():Promise.resolve(null)},DebugRendererFactory2}(),DebugRenderer2=function(){function DebugRenderer2(delegate){this.delegate=delegate}return Object.defineProperty(DebugRenderer2.prototype,"data",{get:function(){return this.delegate.data},enumerable:!0,configurable:!0}),DebugRenderer2.prototype.destroyNode=function(node){removeDebugNodeFromIndex(getDebugNode(node)),this.delegate.destroyNode&&this.delegate.destroyNode(node)},DebugRenderer2.prototype.destroy=function(){this.delegate.destroy()},DebugRenderer2.prototype.createElement=function(name,namespace){var el=this.delegate.createElement(name,namespace),debugCtx=getCurrentDebugContext();if(debugCtx){var debugEl=new DebugElement(el,null,debugCtx);debugEl.name=name,indexDebugNode(debugEl)}return el},DebugRenderer2.prototype.createComment=function(value){var comment=this.delegate.createComment(value),debugCtx=getCurrentDebugContext();return debugCtx&&indexDebugNode(new DebugNode(comment,null,debugCtx)),comment},DebugRenderer2.prototype.createText=function(value){var text=this.delegate.createText(value),debugCtx=getCurrentDebugContext();return debugCtx&&indexDebugNode(new DebugNode(text,null,debugCtx)),text},DebugRenderer2.prototype.appendChild=function(parent,newChild){var debugEl=getDebugNode(parent),debugChildEl=getDebugNode(newChild);debugEl&&debugChildEl&&debugEl instanceof DebugElement&&debugEl.addChild(debugChildEl),this.delegate.appendChild(parent,newChild)},DebugRenderer2.prototype.insertBefore=function(parent,newChild,refChild){var debugEl=getDebugNode(parent),debugChildEl=getDebugNode(newChild),debugRefEl=getDebugNode(refChild);debugEl&&debugChildEl&&debugEl instanceof DebugElement&&debugEl.insertBefore(debugRefEl,debugChildEl),this.delegate.insertBefore(parent,newChild,refChild)},DebugRenderer2.prototype.removeChild=function(parent,oldChild){var debugEl=getDebugNode(parent),debugChildEl=getDebugNode(oldChild);debugEl&&debugChildEl&&debugEl instanceof DebugElement&&debugEl.removeChild(debugChildEl),this.delegate.removeChild(parent,oldChild)},DebugRenderer2.prototype.selectRootElement=function(selectorOrNode){var el=this.delegate.selectRootElement(selectorOrNode),debugCtx=getCurrentDebugContext();return debugCtx&&indexDebugNode(new DebugElement(el,null,debugCtx)),el},DebugRenderer2.prototype.setAttribute=function(el,name,value,namespace){var debugEl=getDebugNode(el);if(debugEl&&debugEl instanceof DebugElement){var fullName=namespace?namespace+":"+name:name;debugEl.attributes[fullName]=value}this.delegate.setAttribute(el,name,value,namespace)},DebugRenderer2.prototype.removeAttribute=function(el,name,namespace){var debugEl=getDebugNode(el);if(debugEl&&debugEl instanceof DebugElement){var fullName=namespace?namespace+":"+name:name;debugEl.attributes[fullName]=null}this.delegate.removeAttribute(el,name,namespace)},DebugRenderer2.prototype.addClass=function(el,name){var debugEl=getDebugNode(el);debugEl&&debugEl instanceof DebugElement&&(debugEl.classes[name]=!0),this.delegate.addClass(el,name)},DebugRenderer2.prototype.removeClass=function(el,name){var debugEl=getDebugNode(el);debugEl&&debugEl instanceof DebugElement&&(debugEl.classes[name]=!1),this.delegate.removeClass(el,name)},DebugRenderer2.prototype.setStyle=function(el,style,value,flags){var debugEl=getDebugNode(el);debugEl&&debugEl instanceof DebugElement&&(debugEl.styles[style]=value),this.delegate.setStyle(el,style,value,flags)},DebugRenderer2.prototype.removeStyle=function(el,style,flags){var debugEl=getDebugNode(el);debugEl&&debugEl instanceof DebugElement&&(debugEl.styles[style]=null),this.delegate.removeStyle(el,style,flags)},DebugRenderer2.prototype.setProperty=function(el,name,value){var debugEl=getDebugNode(el);debugEl&&debugEl instanceof DebugElement&&(debugEl.properties[name]=value),this.delegate.setProperty(el,name,value)},DebugRenderer2.prototype.listen=function(target,eventName,callback){if("string"!=typeof target){var debugEl=getDebugNode(target);debugEl&&debugEl.listeners.push(new EventListener(eventName,callback))}return this.delegate.listen(target,eventName,callback)},DebugRenderer2.prototype.parentNode=function(node){return this.delegate.parentNode(node)},DebugRenderer2.prototype.nextSibling=function(node){return this.delegate.nextSibling(node)},DebugRenderer2.prototype.setValue=function(node,value){return this.delegate.setValue(node,value)},DebugRenderer2}(),NgModuleFactory_=function(_super){function NgModuleFactory_(moduleType,_bootstrapComponents,_ngModuleDefFactory){var _this=_super.call(this)||this;return _this.moduleType=moduleType,_this._bootstrapComponents=_bootstrapComponents,_this._ngModuleDefFactory=_ngModuleDefFactory,_this}return __extends(NgModuleFactory_,_super),NgModuleFactory_.prototype.create=function(parentInjector){initServicesIfNeeded();var def=resolveDefinition(this._ngModuleDefFactory);return Services.createNgModuleRef(this.moduleType,parentInjector||Injector.NULL,this._bootstrapComponents,def)},NgModuleFactory_}(NgModuleFactory),AUTO_STYLE$$1="*";exports.Class=Class,exports.createPlatform=createPlatform,exports.assertPlatform=assertPlatform,exports.destroyPlatform=destroyPlatform,exports.getPlatform=getPlatform,exports.PlatformRef=PlatformRef,exports.ApplicationRef=ApplicationRef,exports.enableProdMode=enableProdMode,exports.isDevMode=isDevMode,exports.createPlatformFactory=createPlatformFactory,exports.NgProbeToken=NgProbeToken,exports.APP_ID=APP_ID,exports.PACKAGE_ROOT_URL=PACKAGE_ROOT_URL,exports.PLATFORM_INITIALIZER=PLATFORM_INITIALIZER,exports.PLATFORM_ID=PLATFORM_ID,exports.APP_BOOTSTRAP_LISTENER=APP_BOOTSTRAP_LISTENER,exports.APP_INITIALIZER=APP_INITIALIZER,exports.ApplicationInitStatus=ApplicationInitStatus,exports.DebugElement=DebugElement,exports.DebugNode=DebugNode,exports.asNativeElements=asNativeElements,exports.getDebugNode=getDebugNode,exports.Testability=Testability,exports.TestabilityRegistry=TestabilityRegistry,exports.setTestabilityGetter=setTestabilityGetter,exports.TRANSLATIONS=TRANSLATIONS,exports.TRANSLATIONS_FORMAT=TRANSLATIONS_FORMAT,exports.LOCALE_ID=LOCALE_ID,exports.MissingTranslationStrategy=MissingTranslationStrategy,exports.ApplicationModule=ApplicationModule,exports.wtfCreateScope=wtfCreateScope,exports.wtfLeave=wtfLeave,exports.wtfStartTimeRange=wtfStartTimeRange,exports.wtfEndTimeRange=wtfEndTimeRange,exports.Type=Type,exports.EventEmitter=EventEmitter,exports.ErrorHandler=ErrorHandler,exports.Sanitizer=Sanitizer,exports.SecurityContext=SecurityContext,exports.ANALYZE_FOR_ENTRY_COMPONENTS=ANALYZE_FOR_ENTRY_COMPONENTS,exports.Attribute=Attribute,exports.ContentChild=ContentChild,exports.ContentChildren=ContentChildren,exports.Query=Query,exports.ViewChild=ViewChild,exports.ViewChildren=ViewChildren,exports.Component=Component,exports.Directive=Directive,exports.HostBinding=HostBinding,exports.HostListener=HostListener,exports.Input=Input,exports.Output=Output,exports.Pipe=Pipe,exports.CUSTOM_ELEMENTS_SCHEMA=CUSTOM_ELEMENTS_SCHEMA,exports.NO_ERRORS_SCHEMA=NO_ERRORS_SCHEMA,exports.NgModule=NgModule,exports.ViewEncapsulation=ViewEncapsulation,exports.Version=Version,exports.VERSION=VERSION,exports.forwardRef=forwardRef,exports.resolveForwardRef=resolveForwardRef,exports.Injector=Injector,exports.ReflectiveInjector=ReflectiveInjector,exports.ResolvedReflectiveFactory=ResolvedReflectiveFactory,exports.ReflectiveKey=ReflectiveKey,exports.InjectionToken=InjectionToken,exports.OpaqueToken=OpaqueToken,exports.Inject=Inject,exports.Optional=Optional,exports.Injectable=Injectable,exports.Self=Self,exports.SkipSelf=SkipSelf,exports.Host=Host,exports.NgZone=NgZone,exports.RenderComponentType=RenderComponentType,exports.Renderer=Renderer,exports.Renderer2=Renderer2,exports.RendererFactory2=RendererFactory2,exports.RendererStyleFlags2=RendererStyleFlags2,exports.RootRenderer=RootRenderer,exports.COMPILER_OPTIONS=COMPILER_OPTIONS,exports.Compiler=Compiler,exports.CompilerFactory=CompilerFactory,exports.ModuleWithComponentFactories=ModuleWithComponentFactories,exports.ComponentFactory=ComponentFactory,exports.ComponentRef=ComponentRef,exports.ComponentFactoryResolver=ComponentFactoryResolver,exports.ElementRef=ElementRef,exports.NgModuleFactory=NgModuleFactory,exports.NgModuleRef=NgModuleRef,exports.NgModuleFactoryLoader=NgModuleFactoryLoader,exports.getModuleFactory=getModuleFactory,exports.QueryList=QueryList,exports.SystemJsNgModuleLoader=SystemJsNgModuleLoader,exports.SystemJsNgModuleLoaderConfig=SystemJsNgModuleLoaderConfig,exports.TemplateRef=TemplateRef,exports.ViewContainerRef=ViewContainerRef,exports.EmbeddedViewRef=EmbeddedViewRef,exports.ViewRef=ViewRef,exports.ChangeDetectionStrategy=ChangeDetectionStrategy,exports.ChangeDetectorRef=ChangeDetectorRef,exports.DefaultIterableDiffer=DefaultIterableDiffer,exports.IterableDiffers=IterableDiffers,exports.KeyValueDiffers=KeyValueDiffers,exports.SimpleChange=SimpleChange,exports.WrappedValue=WrappedValue,exports.platformCore=platformCore,exports.ɵALLOW_MULTIPLE_PLATFORMS=ALLOW_MULTIPLE_PLATFORMS,exports.ɵAPP_ID_RANDOM_PROVIDER=APP_ID_RANDOM_PROVIDER,exports.ɵValueUnwrapper=ValueUnwrapper,exports.ɵdevModeEqual=devModeEqual,exports.ɵisListLikeIterable=isListLikeIterable,exports.ɵChangeDetectorStatus=ChangeDetectorStatus,exports.ɵisDefaultChangeDetectionStrategy=isDefaultChangeDetectionStrategy,exports.ɵConsole=Console,exports.ɵERROR_COMPONENT_TYPE=ERROR_COMPONENT_TYPE,exports.ɵComponentFactory=ComponentFactory,exports.ɵCodegenComponentFactoryResolver=CodegenComponentFactoryResolver,exports.ɵViewMetadata=ViewMetadata,exports.ɵReflectionCapabilities=ReflectionCapabilities,exports.ɵRenderDebugInfo=RenderDebugInfo,exports.ɵglobal=_global,exports.ɵlooseIdentical=looseIdentical,exports.ɵstringify=stringify,exports.ɵmakeDecorator=makeDecorator,exports.ɵisObservable=isObservable,exports.ɵisPromise=isPromise,exports.ɵclearProviderOverrides=clearProviderOverrides,exports.ɵoverrideProvider=overrideProvider,exports.ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR=NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR,exports.ɵregisterModuleFactory=registerModuleFactory,exports.ɵEMPTY_ARRAY=EMPTY_ARRAY,exports.ɵEMPTY_MAP=EMPTY_MAP,exports.ɵand=anchorDef,exports.ɵccf=createComponentFactory,exports.ɵcmf=createNgModuleFactory,exports.ɵcrt=createRendererType2,exports.ɵdid=directiveDef,exports.ɵeld=elementDef,exports.ɵelementEventFullName=elementEventFullName,exports.ɵgetComponentViewDefinitionFactory=getComponentViewDefinitionFactory,exports.ɵinlineInterpolate=inlineInterpolate,exports.ɵinterpolate=interpolate,exports.ɵmod=moduleDef,exports.ɵmpd=moduleProvideDef,exports.ɵncd=ngContentDef,exports.ɵnov=nodeValue,exports.ɵpid=pipeDef,exports.ɵprd=providerDef,exports.ɵpad=pureArrayDef,exports.ɵpod=pureObjectDef,exports.ɵppd=purePipeDef,exports.ɵqud=queryDef,exports.ɵted=textDef,exports.ɵunv=unwrapValue,exports.ɵvid=viewDef,exports.AUTO_STYLE=AUTO_STYLE$$1,exports.trigger=trigger$$1,exports.animate=animate$$1,exports.group=group$$1,exports.sequence=sequence$$1,exports.style=style$$1,exports.state=state$$1,exports.keyframes=keyframes$$1,exports.transition=transition$$1,exports.ɵx=animate$1,exports.ɵy=group$1,exports.ɵbc=keyframes$1,exports.ɵz=sequence$1,exports.ɵbb=state$1,exports.ɵba=style$1,exports.ɵbd=transition$1,exports.ɵw=trigger$1,exports.ɵk=_iterableDiffersFactory,exports.ɵl=_keyValueDiffersFactory,exports.ɵm=_localeFactory,exports.ɵe=ApplicationRef_,exports.ɵf=_appIdRandomProviderFactory,exports.ɵg=defaultIterableDiffers,exports.ɵh=defaultKeyValueDiffers,exports.ɵi=DefaultIterableDifferFactory,exports.ɵj=DefaultKeyValueDifferFactory,exports.ɵb=ReflectiveInjector_,exports.ɵc=ReflectiveDependency,exports.ɵd=resolveReflectiveProviders,exports.ɵn=wtfEnabled,exports.ɵp=createScope$1,exports.ɵo=detectWTF,exports.ɵs=endTimeRange,exports.ɵq=leave,exports.ɵr=startTimeRange,exports.ɵa=makeParamDecorator,exports.ɵt=_def,exports.ɵu=DebugContext,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=core.umd.min.js.map
