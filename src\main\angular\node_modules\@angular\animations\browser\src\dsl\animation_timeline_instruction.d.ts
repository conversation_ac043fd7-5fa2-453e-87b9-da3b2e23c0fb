/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵStyleData } from '@angular/animations';
import { AnimationEngineInstruction } from '../render/animation_engine_instruction';
export interface AnimationTimelineInstruction extends AnimationEngineInstruction {
    element: any;
    keyframes: ɵStyleData[];
    preStyleProps: string[];
    postStyleProps: string[];
    duration: number;
    delay: number;
    totalTime: number;
    easing: string | null;
    stretchStartingKeyframe?: boolean;
    subTimeline: boolean;
}
export declare function createTimelineInstruction(element: any, keyframes: ɵStyleData[], preStyleProps: string[], postStyleProps: string[], duration: number, delay: number, easing?: string | null, subTimeline?: boolean): AnimationTimelineInstruction;
