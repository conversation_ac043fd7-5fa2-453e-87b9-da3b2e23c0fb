{"_args": [["caseless@0.12.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "caseless@0.12.0", "_id": "caseless@0.12.0", "_inBundle": false, "_integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "_location": "/caseless", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "caseless@0.12.0", "name": "caseless", "escapedName": "caseless", "rawSpec": "0.12.0", "saveSpec": null, "fetchSpec": "0.12.0"}, "_requiredBy": ["/node-gyp/request", "/node-sass/request", "/request"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/caseless/-/caseless-0.12.0.tgz", "_spec": "0.12.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless#readme", "keywords": ["headers", "http", "caseless"], "license": "Apache-2.0", "main": "index.js", "name": "caseless", "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "scripts": {"test": "node test.js"}, "test": "node test.js", "version": "0.12.0"}