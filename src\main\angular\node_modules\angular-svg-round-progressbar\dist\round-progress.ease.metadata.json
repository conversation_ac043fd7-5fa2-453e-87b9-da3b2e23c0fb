[{"__symbolic": "module", "version": 3, "metadata": {"RoundProgressEase": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"linearEase": [{"__symbolic": "method"}], "easeInQuad": [{"__symbolic": "method"}], "easeOutQuad": [{"__symbolic": "method"}], "easeInOutQuad": [{"__symbolic": "method"}], "easeInCubic": [{"__symbolic": "method"}], "easeOutCubic": [{"__symbolic": "method"}], "easeInOutCubic": [{"__symbolic": "method"}], "easeInQuart": [{"__symbolic": "method"}], "easeOutQuart": [{"__symbolic": "method"}], "easeInOutQuart": [{"__symbolic": "method"}], "easeInQuint": [{"__symbolic": "method"}], "easeOutQuint": [{"__symbolic": "method"}], "easeInOutQuint": [{"__symbolic": "method"}], "easeInSine": [{"__symbolic": "method"}], "easeOutSine": [{"__symbolic": "method"}], "easeInOutSine": [{"__symbolic": "method"}], "easeInExpo": [{"__symbolic": "method"}], "easeOutExpo": [{"__symbolic": "method"}], "easeInOutExpo": [{"__symbolic": "method"}], "easeInCirc": [{"__symbolic": "method"}], "easeOutCirc": [{"__symbolic": "method"}], "easeInOutCirc": [{"__symbolic": "method"}], "easeInElastic": [{"__symbolic": "method"}], "easeOutElastic": [{"__symbolic": "method"}], "easeInOutElastic": [{"__symbolic": "method"}], "easeInBack": [{"__symbolic": "method"}], "easeOutBack": [{"__symbolic": "method"}], "easeInOutBack": [{"__symbolic": "method"}], "easeInBounce": [{"__symbolic": "method"}], "easeOutBounce": [{"__symbolic": "method"}], "easeInOutBounce": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"RoundProgressEase": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"linearEase": [{"__symbolic": "method"}], "easeInQuad": [{"__symbolic": "method"}], "easeOutQuad": [{"__symbolic": "method"}], "easeInOutQuad": [{"__symbolic": "method"}], "easeInCubic": [{"__symbolic": "method"}], "easeOutCubic": [{"__symbolic": "method"}], "easeInOutCubic": [{"__symbolic": "method"}], "easeInQuart": [{"__symbolic": "method"}], "easeOutQuart": [{"__symbolic": "method"}], "easeInOutQuart": [{"__symbolic": "method"}], "easeInQuint": [{"__symbolic": "method"}], "easeOutQuint": [{"__symbolic": "method"}], "easeInOutQuint": [{"__symbolic": "method"}], "easeInSine": [{"__symbolic": "method"}], "easeOutSine": [{"__symbolic": "method"}], "easeInOutSine": [{"__symbolic": "method"}], "easeInExpo": [{"__symbolic": "method"}], "easeOutExpo": [{"__symbolic": "method"}], "easeInOutExpo": [{"__symbolic": "method"}], "easeInCirc": [{"__symbolic": "method"}], "easeOutCirc": [{"__symbolic": "method"}], "easeInOutCirc": [{"__symbolic": "method"}], "easeInElastic": [{"__symbolic": "method"}], "easeOutElastic": [{"__symbolic": "method"}], "easeInOutElastic": [{"__symbolic": "method"}], "easeInBack": [{"__symbolic": "method"}], "easeOutBack": [{"__symbolic": "method"}], "easeInOutBack": [{"__symbolic": "method"}], "easeInBounce": [{"__symbolic": "method"}], "easeOutBounce": [{"__symbolic": "method"}], "easeInOutBounce": [{"__symbolic": "method"}]}}}}]