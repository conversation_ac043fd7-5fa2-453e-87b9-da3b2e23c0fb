{"_args": [["body-parser@1.17.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "body-parser@1.17.2", "_id": "body-parser@1.17.2", "_inBundle": false, "_integrity": "sha1-+IkqvI+eYn1Crtr7yma/WrmRBO4=", "_location": "/body-parser", "_phantomChildren": {"ms": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "body-parser@1.17.2", "name": "body-parser", "escapedName": "body-parser", "rawSpec": "1.17.2", "saveSpec": null, "fetchSpec": "1.17.2"}, "_requiredBy": ["/karma"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/body-parser/-/body-parser-1.17.2.tgz", "_spec": "1.17.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "2.4.0", "content-type": "~1.0.2", "debug": "2.6.7", "depd": "~1.1.0", "http-errors": "~1.6.1", "iconv-lite": "0.4.15", "on-finished": "~2.3.0", "qs": "6.4.0", "raw-body": "~2.2.0", "type-is": "~1.6.15"}, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.2.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "4.2.2", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "methods": "1.1.2", "mocha": "2.5.3", "safe-buffer": "5.0.1", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "version": "1.17.2"}