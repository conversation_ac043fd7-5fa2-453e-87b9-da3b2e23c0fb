{"_args": [["append-transform@0.4.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "append-transform@0.4.0", "_id": "append-transform@0.4.0", "_inBundle": false, "_integrity": "sha1-126/jKlNJ24keja61EpLdKthGZE=", "_location": "/append-transform", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "append-transform@0.4.0", "name": "append-transform", "escapedName": "append-transform", "rawSpec": "0.4.0", "saveSpec": null, "fetchSpec": "0.4.0"}, "_requiredBy": ["/istanbul-lib-hook"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/append-transform/-/append-transform-0.4.0.tgz", "_spec": "0.4.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "bugs": {"url": "https://github.com/jamestalmage/append-transform/issues"}, "dependencies": {"default-require-extensions": "^1.0.0"}, "description": "Install a transform to `require.extensions` that always runs last, even if additional extensions are added later.", "devDependencies": {"ava": "^0.7.0", "coveralls": "^2.11.6", "fake-module-system": "^0.3.0", "nyc": "^4.0.1", "xo": "^0.11.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jamestalmage/append-transform#readme", "keywords": ["transform", "require", "append", "last", "coverage", "source-map", "extension", "module"], "license": "MIT", "name": "append-transform", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/append-transform.git"}, "scripts": {"test": "xo && nyc --reporter=lcov --reporter=text ava"}, "version": "0.4.0", "xo": {"ignores": ["test.js"]}}