/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { Provider } from '@angular/core';
import { NgClass } from './ng_class';
import { NgComponentOutlet } from './ng_component_outlet';
import { <PERSON><PERSON><PERSON>, NgForOf, NgForOfContext } from './ng_for_of';
import { NgIf, NgIfContext } from './ng_if';
import { NgPlural, NgPluralCase } from './ng_plural';
import { NgStyle } from './ng_style';
import { Ng<PERSON><PERSON>, Ng<PERSON>witchCase, NgSwitchDefault } from './ng_switch';
import { NgTemplateOutlet } from './ng_template_outlet';
export { NgClass, NgComponentOutlet, NgFor, NgForOf, NgForOfContext, NgI<PERSON>, NgIfContext, Ng<PERSON>lural, Ng<PERSON><PERSON>ralCase, Ng<PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Default, NgTemplateOutlet };
/**
 * A collection of Angular directives that are likely to be used in each and every Angular
 * application.
 */
export declare const COMMON_DIRECTIVES: Provider[];
/**
 * A collection of deprecated directives that are no longer part of the core module.
 */
export declare const COMMON_DEPRECATED_DIRECTIVES: Provider[];
