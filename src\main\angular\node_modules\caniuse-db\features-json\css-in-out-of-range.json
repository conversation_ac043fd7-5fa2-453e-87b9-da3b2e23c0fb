{"title": ":in-range and :out-of-range CSS pseudo-classes", "description": "If a temporal or number `<input>` has `max` and/or `min` attributes, then `:in-range` matches when the value is within the specified range and `:out-of-range` matches when the value is outside the specified range. If there are no range constraints, then neither pseudo-class matches.", "spec": "http://www.w3.org/TR/selectors4/#range-pseudos", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:out-of-range", "title": "Mozilla Developer Network (MDN) documentation - CSS :out-of-range"}, {"url": "https://html.spec.whatwg.org/multipage/scripting.html#selector-in-range", "title": "WHATWG HTML specification for `:in-range` and `:out-of-range`"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "a #3", "30": "a #3", "31": "a #3", "32": "a #3", "33": "a #3", "34": "a #3", "35": "a #3", "36": "a #3", "37": "a #3", "38": "a #3", "39": "a #3", "40": "a #3", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #3", "46": "a #3", "47": "a #3", "48": "a #3", "49": "a #3", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a #2 #3", "16": "a #2 #3", "17": "a #2 #3", "18": "a #2 #3", "19": "a #2 #3", "20": "a #2 #3", "21": "a #2 #3", "22": "a #2 #3", "23": "a #2 #3", "24": "a #2 #3", "25": "a #2 #3", "26": "a #2 #3", "27": "a #2 #3", "28": "a #2 #3", "29": "a #2 #3", "30": "a #2 #3", "31": "a #2 #3", "32": "a #2 #3", "33": "a #2 #3", "34": "a #2 #3", "35": "a #2 #3", "36": "a #2 #3", "37": "a #2 #3", "38": "a #2 #3", "39": "a #2 #3", "40": "a #2 #3", "41": "a #2 #3", "42": "a #2 #3", "43": "a #2 #3", "44": "a #2 #3", "45": "a #2 #3", "46": "a #2 #3", "47": "a #2 #3", "48": "a #2 #3", "49": "a #2 #3", "50": "a #2 #3", "51": "a #2 #3", "52": "a #2", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "u", "5.1": "a #2 #3", "6": "a #2 #3", "6.1": "a #2 #3", "7": "a #2 #3", "7.1": "a #2 #3", "8": "a #2 #3", "9": "a #2 #3", "9.1": "a #2 #3", "10": "a #2 #3", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "a #2", "10.5": "a #2", "10.6": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "11.6": "a #2", "12": "a #2", "12.1": "a #2", "15": "a #2 #3", "16": "a #2 #3", "17": "a #2 #3", "18": "a #2 #3", "19": "a #2 #3", "20": "a #2 #3", "21": "a #2 #3", "22": "a #2 #3", "23": "a #2 #3", "24": "a #2 #3", "25": "a #2 #3", "26": "a #2 #3", "27": "a #2 #3", "28": "a #2 #3", "29": "a #2 #3", "30": "a #2 #3", "31": "a #2 #3", "32": "a #2 #3", "33": "a #2 #3", "34": "a #2 #3", "35": "a #2 #3", "36": "a #2 #3", "37": "a #2 #3", "38": "a #2 #3", "39": "a #2", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #2 #3", "6.0-6.1": "a #2 #3", "7.0-7.1": "a #2 #3", "8": "a #2 #3", "8.1-8.4": "a #2 #3", "9.0-9.2": "a #2 #3", "9.3": "a #2 #3", "10.0-10.2": "a #2 #3", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "a #2", "4.1": "a #2", "4.2-4.3": "a #2", "4.4": "a #2", "4.4.3-4.4.4": "a #2", "56": "y"}, "bb": {"7": "n", "10": "a #2"}, "op_mob": {"10": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "12": "a #2", "12.1": "a #2", "37": "a #2"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a #2"}, "samsung": {"4": "a #2", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Note that `<input type=\"range\">` can never match `:out-of-range` because the user cannot input such a value, and if the initial value is outside the range, the browser immediately clamps it to the minimum or maximum (as appropriate) bound of the range.", "notes_by_num": {"1": "Opera Mini correctly applies style on initial load, but does not correctly update when value is changed.", "2": "`:in-range` also incorrectly matches temporal and `number` inputs which don't have `min` or `max` attributes. See [Edge bug](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/7200501/), [Chrome bug](https://bugs.chromium.org/p/chromium/issues/detail?id=603268), [WebKit bug](https://bugs.webkit.org/show_bug.cgi?id=156558).", "3": "`:in-range` and `:out-of-range` incorrectly match inputs which are disabled or readonly. See [Edge bug](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/7190958/), [Mozilla bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1264157), [WebKit bug](https://bugs.webkit.org/show_bug.cgi?id=156530), [Chrome bug](https://bugs.chromium.org/p/chromium/issues/detail?id=602568)."}, "usage_perc_y": 66.79, "usage_perc_a": 26.46, "ucprefix": false, "parent": "", "keywords": "in,out,of,range,:in-range,:out-of-range", "ie_id": "cssrangepseudoclasses", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}