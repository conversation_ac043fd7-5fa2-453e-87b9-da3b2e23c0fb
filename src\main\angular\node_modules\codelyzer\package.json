{"_args": [["codelyzer@2.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "codelyzer@2.0.1", "_id": "codelyzer@2.0.1", "_inBundle": false, "_integrity": "sha1-0PcSH2eoQkyS0h07MfNkC4Pe+e0=", "_location": "/codelyzer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "codelyzer@2.0.1", "name": "codelyzer", "escapedName": "codelyzer", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/codelyzer/-/codelyzer-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mgechev/codelyzer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"app-root-path": "^2.0.1", "css-selector-tokenizer": "^0.7.0", "cssauron": "^1.4.0", "semver-dsl": "^1.0.1", "source-map": "^0.5.6", "sprintf-js": "^1.0.3"}, "description": "Linting for Angular applications, following angular.io/styleguide.", "homepage": "https://github.com/mgechev/codelyzer#readme", "keywords": ["Angular", "style guide", "styleguide", "nglint", "codelyzer", "lint", "tslint"], "license": "MIT", "name": "codelyzer", "peerDependencies": {"tslint": "^4.0.0", "@angular/compiler": "^2.3.1 || >=4.0.0-beta <5.0.0", "@angular/core": "^2.3.1 || >=4.0.0-beta <5.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mgechev/codelyzer.git"}, "version": "2.0.1"}