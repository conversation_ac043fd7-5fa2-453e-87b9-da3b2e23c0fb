{"title": "Selection API", "description": "API for accessing selected content of a document, including the `window.getSelection()` method, as well as the `selectstart` & `selectionchange` events on `document`.", "spec": "https://www.w3.org/TR/selection-api/", "status": "wd", "links": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1231923", "title": "Firefox support bug"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "u", "6": "a #2", "7": "a #2", "8": "a #2", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1 #5", "44": "a #1 #5", "45": "a #1 #5", "46": "a #1 #5", "47": "a #1 #5", "48": "a #1 #5", "49": "a #1 #5", "50": "a #1 #5", "51": "a #1 #5", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #1", "4.2-4.3": "u", "5.0-5.1": "a #3", "6.0-6.1": "a #3", "7.0-7.1": "a #3", "8": "a #3", "8.1-8.4": "a #3", "9.0-9.2": "a #3", "9.3": "a #3", "10.0-10.2": "a #3", "10.3": "a #3", "11": "a #3"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "u", "4.1": "u", "4.2-4.3": "y #4", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "u", "10": "y"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "a #1", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "a #1"}, "ie_mob": {"10": "u", "11": "y"}, "and_uc": {"11.4": "y #4"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "See also support for the related [DOM range](http://caniuse.com/#feat=dom-range) ", "notes_by_num": {"1": "Supports `window.getSelection()` but no selection events", "2": "Supports selection events, but not `window.getSelection()`", "3": "Does not support the `selectstart` event", "4": "`window.getSelection()` may fail on tapping buttons, as the selection is lost right before the listener code runs.", "5": "Selection events are supported behind the `dom.select_events.enabled` flag"}, "usage_perc_y": 82.63, "usage_perc_a": 12.03, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}