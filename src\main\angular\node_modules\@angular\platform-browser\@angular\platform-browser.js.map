{"version": 3, "file": "platform-browser.js", "sources": ["../../../../packages/platform-browser/index.ts", "../../../../packages/platform-browser/public_api.ts", "../../../../packages/platform-browser/src/platform-browser.ts", "../../../../packages/platform-browser/src/version.ts", "../../../../packages/platform-browser/src/private_export.ts", "../../../../packages/platform-browser/src/dom/debug/by.ts", "../../../../packages/platform-browser/src/browser/tools/tools.ts", "../../../../packages/platform-browser/src/browser/tools/common_tools.ts", "../../../../packages/platform-browser/src/browser/tools/browser.ts", "../../../../packages/platform-browser/src/browser.ts", "../../../../packages/platform-browser/src/security/dom_sanitization_service.ts", "../../../../packages/platform-browser/src/security/style_sanitizer.ts", "../../../../packages/platform-browser/src/security/html_sanitizer.ts", "../../../../packages/platform-browser/src/security/url_sanitizer.ts", "../../../../packages/platform-browser/src/dom/events/key_events.ts", "../../../../packages/platform-browser/src/dom/events/hammer_gestures.ts", "../../../../packages/platform-browser/src/dom/events/dom_events.ts", "../../../../packages/platform-browser/src/dom/dom_renderer.ts", "../../../../packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../packages/platform-browser/src/dom/events/event_manager.ts", "../../../../packages/platform-browser/src/dom/debug/ng_probe.ts", "../../../../packages/platform-browser/src/dom/util.ts", "../../../../packages/platform-browser/src/browser/title.ts", "../../../../packages/platform-browser/src/browser/testability.ts", "../../../../packages/platform-browser/src/browser/server-transition.ts", "../../../../packages/platform-browser/src/browser/meta.ts", "../../../../packages/platform-browser/src/browser/location/browser_platform_location.ts", "../../../../packages/platform-browser/src/browser/location/history.ts", "../../../../packages/platform-browser/src/dom/dom_tokens.ts", "../../../../packages/platform-browser/src/browser/browser_adapter.ts", "../../../../packages/platform-browser/src/browser/generic_browser_adapter.ts", "../../../../packages/platform-browser/src/dom/dom_adapter.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {BrowserModule,platformBrowser,Meta,MetaDefinition,Title,disableDebugTools,enableDebugTools,By,NgProbeToken,DOCUMENT,EVENT_MANAGER_PLUGINS,EventManager,HAMMER_GESTURE_CONFIG,HammerGestureConfig,DomSanitizer,SafeHtml,SafeResourceUrl,SafeScript,SafeStyle,SafeUrl,SafeValue,VERSION,ɵBROWSER_SANITIZATION_PROVIDERS,ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,ɵinitDomAdapter,ɵBrowserDomAdapter,ɵBrowserPlatformLocation,ɵTRANSITION_ID,ɵBrowserGetTestability,ɵELEMENT_PROBE_PROVIDERS,ɵDomAdapter,ɵgetDOM,ɵsetRootDomAdapter,ɵDomRendererFactory2,ɵNAMESPACE_URIS,ɵflattenStyles,ɵshimContentAttribute,ɵshimHostAttribute,ɵDomEventsPlugin,ɵHammerGesturesPlugin,ɵKeyEventsPlugin,ɵDomSharedStylesHost,ɵSharedStylesHost} from './public_api';\n\nexport {_document as ɵb,errorHandler as ɵa} from './src/browser';\nexport {GenericBrowserDomAdapter as ɵh} from './src/browser/generic_browser_adapter';\nexport {SERVER_TRANSITION_PROVIDERS as ɵg,appInitializerFactory as ɵf} from './src/browser/server-transition';\nexport {_createNgProbe as ɵc} from './src/dom/debug/ng_probe';\nexport {EventManagerPlugin as ɵd} from './src/dom/events/event_manager';\nexport {DomSanitizerImpl as ɵe} from './src/security/dom_sanitization_service';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\nexport {BrowserModule,platformBrowser,Meta,MetaDefinition,Title,disableDebugTools,enableDebugTools,By,Ng<PERSON>robeToken,DOCUMENT,EVENT_MANAGER_PLUGINS,EventManager,HAMMER_GESTURE_CONFIG,HammerGestureConfig,DomSanitizer,SafeHtml,SafeResourceUrl,SafeScript,SafeStyle,SafeUrl,SafeValue,VERSION,ɵBROWSER_SANITIZATION_PROVIDERS,ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,ɵinitDomAdapter,ɵBrowserDomAdapter,ɵBrowserPlatformLocation,ɵTRANSITION_ID,ɵBrowserGetTestability,ɵELEMENT_PROBE_PROVIDERS,ɵDomAdapter,ɵgetDOM,ɵsetRootDomAdapter,ɵDomRendererFactory2,ɵNAMESPACE_URIS,ɵflattenStyles,ɵshimContentAttribute,ɵshimHostAttribute,ɵDomEventsPlugin,ɵHammerGesturesPlugin,ɵKeyEventsPlugin,ɵDomSharedStylesHost,ɵSharedStylesHost} from './src/platform-browser';\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BrowserModule, platformBrowser} from './browser';\nexport {Meta, MetaDefinition} from './browser/meta';\nexport {Title} from './browser/title';\nexport {disableDebugTools, enableDebugTools} from './browser/tools/tools';\nexport {By} from './dom/debug/by';\nexport {NgProbeToken} from './dom/debug/ng_probe';\nexport {DOCUMENT} from './dom/dom_tokens';\nexport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nexport {HAMMER_GESTURE_CONFIG, HammerGestureConfig} from './dom/events/hammer_gestures';\nexport {DomSanitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl, SafeValue} from './security/dom_sanitization_service';\nexport {ɵBROWSER_SANITIZATION_PROVIDERS,ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,ɵinitDomAdapter,ɵBrowserDomAdapter,ɵBrowserPlatformLocation,ɵTRANSITION_ID,ɵBrowserGetTestability,ɵELEMENT_PROBE_PROVIDERS,ɵDomAdapter,ɵgetDOM,ɵsetRootDomAdapter,ɵDomRendererFactory2,ɵNAMESPACE_URIS,ɵflattenStyles,ɵshimContentAttribute,ɵshimHostAttribute,ɵDomEventsPlugin,ɵHammerGesturesPlugin,ɵKeyEventsPlugin,ɵDomSharedStylesHost,ɵSharedStylesHost} from './private_export';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BROWSER_SANITIZATION_PROVIDERS as ɵBROWSER_SANITIZATION_PROVIDERS, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, initDomAdapter as ɵinitDomAdapter} from './browser';\nexport {BrowserDomAdapter as ɵBrowserDomAdapter} from './browser/browser_adapter';\nexport {BrowserPlatformLocation as ɵBrowserPlatformLocation} from './browser/location/browser_platform_location';\nexport {TRANSITION_ID as ɵTRANSITION_ID} from './browser/server-transition';\nexport {BrowserGetTestability as ɵBrowserGetTestability} from './browser/testability';\nexport {ELEMENT_PROBE_PROVIDERS as ɵELEMENT_PROBE_PROVIDERS} from './dom/debug/ng_probe';\nexport {DomAdapter as ɵDomAdapter, getDOM as ɵgetDOM, setRootDomAdapter as ɵsetRootDomAdapter} from './dom/dom_adapter';\nexport {DomRendererFactory2 as ɵDomRendererFactory2, NAMESPACE_URIS as ɵNAMESPACE_URIS, flattenStyles as ɵflattenStyles, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute} from './dom/dom_renderer';\nexport {DomEventsPlugin as ɵDomEventsPlugin} from './dom/events/dom_events';\nexport {HammerGesturesPlugin as ɵHammerGesturesPlugin} from './dom/events/hammer_gestures';\nexport {KeyEventsPlugin as ɵKeyEventsPlugin} from './dom/events/key_events';\nexport {DomSharedStylesHost as ɵDomSharedStylesHost, SharedStylesHost as ɵSharedStylesHost} from './dom/shared_styles_host';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {DebugElement, Predicate, Type} from '@angular/core';\nimport {getDOM} from '../../dom/dom_adapter';\n/**\n * Predicates for use with {\\@link DebugElement}'s query functions.\n * \n * \\@experimental All debugging apis are currently experimental.\n */\nexport class By {\n/**\n * Match all elements.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n * @return {?}\n */\nstatic all(): Predicate<DebugElement> { return (debugElement) => true; }\n/**\n * Match elements by the given CSS selector.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n * @param {?} selector\n * @return {?}\n */\nstatic css(selector: string): Predicate<DebugElement> {\n    return (debugElement) => {\n      return debugElement.nativeElement != null ?\n          getDOM().elementMatches(debugElement.nativeElement, selector) :\n          false;\n    };\n  }\n/**\n * Match elements that have the given directive present.\n * \n * ## Example\n * \n * {\\@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n * @param {?} type\n * @return {?}\n */\nstatic directive(type: Type<any>): Predicate<DebugElement> {\n    return (debugElement) => /** @type {?} */(( debugElement.providerTokens)).indexOf(type) !== -1;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentRef} from '@angular/core';\nimport {exportNgVar} from '../../dom/util';\nimport {AngularProfiler} from './common_tools';\n\nconst /** @type {?} */ PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n * \n * Usage:\n * \n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n * \n * \\@experimental All debugging apis are currently experimental.\n * @template T\n * @param {?} ref\n * @return {?}\n */\nexport function enableDebugTools<T>(ref: ComponentRef<T>): ComponentRef<T> {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n * \n * \\@experimental All debugging apis are currently experimental.\n * @return {?}\n */\nexport function disableDebugTools(): void {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ApplicationRef, ComponentRef} from '@angular/core';\nimport {getDOM} from '../../dom/dom_adapter';\nimport {window} from './browser';\nexport class ChangeDetectionPerfRecord {\n/**\n * @param {?} msPerTick\n * @param {?} numTicks\n */\nconstructor(public msPerTick: number,\npublic numTicks: number) {}\n}\n\nfunction ChangeDetectionPerfRecord_tsickle_Closure_declarations() {\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.msPerTick;\n/** @type {?} */\nChangeDetectionPerfRecord.prototype.numTicks;\n}\n\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nexport class AngularProfiler {\n  appRef: ApplicationRef;\n/**\n * @param {?} ref\n */\nconstructor(ref: ComponentRef<any>) { this.appRef = ref.injector.get(ApplicationRef); }\n/**\n * Exercises change detection in a loop and then prints the average amount of\n * time in milliseconds how long a single round of change detection takes for\n * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n * of 500 milliseconds.\n * \n * Optionally, a user may pass a `config` parameter containing a map of\n * options. Supported options are:\n * \n * `record` (boolean) - causes the profiler to record a CPU profile while\n * it exercises the change detector. Example:\n * \n * ```\n * ng.profiler.timeChangeDetection({record: true})\n * ```\n * @param {?} config\n * @return {?}\n */\ntimeChangeDetection(config: any): ChangeDetectionPerfRecord {\n    const /** @type {?} */ record = config && config['record'];\n    const /** @type {?} */ profileName = 'Change Detection';\n    // Profiler is not available in Android browsers, nor in IE 9 without dev tools opened\n    const /** @type {?} */ isProfilerAvailable = window.console.profile != null;\n    if (record && isProfilerAvailable) {\n      window.console.profile(profileName);\n    }\n    const /** @type {?} */ start = getDOM().performanceNow();\n    let /** @type {?} */ numTicks = 0;\n    while (numTicks < 5 || (getDOM().performanceNow() - start) < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const /** @type {?} */ end = getDOM().performanceNow();\n    if (record && isProfilerAvailable) {\n      // need to cast to <any> because type checker thinks there's no argument\n      // while in fact there is:\n      //\n      // https://developer.mozilla.org/en-US/docs/Web/API/Console/profileEnd\n      ( /** @type {?} */((<any>window.console.profileEnd)))(profileName);\n    }\n    const /** @type {?} */ msPerTick = (end - start) / numTicks;\n    window.console.log(`ran ${numTicks} change detection cycles`);\n    window.console.log(`${msPerTick.toFixed(2)} ms per check`);\n\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\n\nfunction AngularProfiler_tsickle_Closure_declarations() {\n/** @type {?} */\nAngularProfiler.prototype.appRef;\n}\n\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst win = typeof window !== 'undefined' && window || <any>{};\nexport {win as window};\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {CommonModule, PlatformLocation, ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {APP_ID, ApplicationModule, ErrorHandler, ModuleWithProviders, NgModule, Optional, PLATFORM_ID, PLATFORM_INITIALIZER, PlatformRef, Provider, RendererFactory2, RootRenderer, <PERSON>itizer, SkipSelf, Testability, createPlatformFactory, platformCore} from '@angular/core';\n\nimport {BrowserDomAdapter} from './browser/browser_adapter';\nimport {BrowserPlatformLocation} from './browser/location/browser_platform_location';\nimport {Meta} from './browser/meta';\nimport {SERVER_TRANSITION_PROVIDERS, TRANSITION_ID} from './browser/server-transition';\nimport {BrowserGetTestability} from './browser/testability';\nimport {Title} from './browser/title';\nimport {ELEMENT_PROBE_PROVIDERS} from './dom/debug/ng_probe';\nimport {getDOM} from './dom/dom_adapter';\nimport {DomRendererFactory2} from './dom/dom_renderer';\nimport {DOCUMENT} from './dom/dom_tokens';\nimport {DomEventsPlugin} from './dom/events/dom_events';\nimport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nimport {HAMMER_GESTURE_CONFIG, HammerGestureConfig, HammerGesturesPlugin} from './dom/events/hammer_gestures';\nimport {KeyEventsPlugin} from './dom/events/key_events';\nimport {DomSharedStylesHost, SharedStylesHost} from './dom/shared_styles_host';\nimport {DomSanitizer, DomSanitizerImpl} from './security/dom_sanitization_service';\n\nexport const /** @type {?} */ INTERNAL_BROWSER_PLATFORM_PROVIDERS: Provider[] = [\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n  {provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true},\n  {provide: PlatformLocation, useClass: BrowserPlatformLocation},\n  {provide: DOCUMENT, useFactory: _document, deps: []},\n];\n/**\n * \\@security Replacing built-in sanitization providers exposes the application to XSS risks.\n * Attacker-controlled data introduced by an unsanitized provider could expose your\n * application to XSS risks. For more detail, see the [Security Guide](http://g.co/ng/security).\n * \\@experimental\n */\nexport const BROWSER_SANITIZATION_PROVIDERS: Array<any> = [\n  {provide: Sanitizer, useExisting: DomSanitizer},\n  {provide: DomSanitizer, useClass: DomSanitizerImpl},\n];\n/**\n * \\@stable\n */\nexport const platformBrowser: (extraProviders?: Provider[]) => PlatformRef =\n    createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * @return {?}\n */\nexport function initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserGetTestability.init();\n}\n/**\n * @return {?}\n */\nexport function errorHandler(): ErrorHandler {\n  return new ErrorHandler();\n}\n/**\n * @return {?}\n */\nexport function _document(): any {\n  return document;\n}\n/**\n * The ng module for the browser.\n * \n * \\@stable\n */\nexport class BrowserModule {\n/**\n * @param {?} parentModule\n */\nconstructor(  parentModule: BrowserModule) {\n    if (parentModule) {\n      throw new Error(\n          `BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n    }\n  }\n/**\n * Configures a browser-based application to transition from a server-rendered app, if\n * one is present on the page. The specified parameters must include an application id,\n * which must match between the client and server applications.\n * \n * \\@experimental\n * @param {?} params\n * @return {?}\n */\nstatic withServerTransition(params: {appId: string}): ModuleWithProviders {\n    return {\n      ngModule: BrowserModule,\n      providers: [\n        {provide: APP_ID, useValue: params.appId},\n        {provide: TRANSITION_ID, useExisting: APP_ID},\n        SERVER_TRANSITION_PROVIDERS,\n      ],\n    };\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    BROWSER_SANITIZATION_PROVIDERS,\n    {provide: ErrorHandler, useFactory: errorHandler, deps: []},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: DomEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true},\n    {provide: EVENT_MANAGER_PLUGINS, useClass: HammerGesturesPlugin, multi: true},\n    {provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig},\n    DomRendererFactory2,\n    {provide: RendererFactory2, useExisting: DomRendererFactory2},\n    {provide: SharedStylesHost, useExisting: DomSharedStylesHost},\n    DomSharedStylesHost,\n    Testability,\n    EventManager,\n    ELEMENT_PROBE_PROVIDERS,\n    Meta,\n    Title,\n  ],\n  exports: [CommonModule, ApplicationModule]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserModule, decorators: [{ type: Optional }, { type: SkipSelf }, ]},\n];\n}\n\nfunction BrowserModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Sanitizer, SecurityContext} from '@angular/core';\n\nimport {DOCUMENT} from '../dom/dom_tokens';\n\nimport {sanitizeHtml} from './html_sanitizer';\nimport {sanitizeStyle} from './style_sanitizer';\nimport {sanitizeUrl} from './url_sanitizer';\n\nexport {SecurityContext};\n\n\n\n/**\n * Marker interface for a value that's safe to use in a particular context.\n *\n * @stable\n */\nexport interface SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as HTML.\n *\n * @stable\n */\nexport interface SafeHtml extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as style (CSS).\n *\n * @stable\n */\nexport interface SafeStyle extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as JavaScript.\n *\n * @stable\n */\nexport interface SafeScript extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL linking to a document.\n *\n * @stable\n */\nexport interface SafeUrl extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL to load executable code from.\n *\n * @stable\n */\nexport interface SafeResourceUrl extends SafeValue {}\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n * \n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n * \n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n * \n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n * \n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n * \n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n * \n * \\@security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](http://g.co/ng/security).\n * \n * \\@stable\n * @abstract\n */\nexport abstract class DomSanitizer implements Sanitizer {\n/**\n * Sanitizes a value for use in the given SecurityContext.\n * \n * If value is trusted for the context, this method will unwrap the contained safe value and use\n * it directly. Otherwise, value will be sanitized to be safe in the given context, for example\n * by replacing URLs that have an unsafe protocol part (such as `javascript:`). The implementation\n * is responsible to make sure that the value can definitely be safely used in the given context.\n * @abstract\n * @param {?} context\n * @param {?} value\n * @return {?}\n */\nsanitize(context: SecurityContext, value: SafeValue|string|null) {}\n/**\n * Bypass security and trust the given value to be safe HTML. Only use this when the bound HTML\n * is unsafe (e.g. contains `<script>` tags) and the code should be executed. The sanitizer will\n * leave safe HTML intact, so in most situations this method should not be used.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string) {}\n/**\n * Bypass security and trust the given value to be safe style value (CSS).\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string) {}\n/**\n * Bypass security and trust the given value to be safe JavaScript.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe style URL, i.e. a value that can be used\n * in hyperlinks or `<img src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string) {}\n/**\n * Bypass security and trust the given value to be a safe resource URL, i.e. a location that may\n * be used to load executable code from, like `<script src>`, or `<iframe src>`.\n * \n * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n * security risks!\n * @abstract\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string) {}\n}\nexport class DomSanitizerImpl extends DomSanitizer {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { super(); }\n/**\n * @param {?} ctx\n * @param {?} value\n * @return {?}\n */\nsanitize(ctx: SecurityContext, value: SafeValue|string|null): string|null {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return /** @type {?} */(( value as string));\n      case SecurityContext.HTML:\n        if (value instanceof SafeHtmlImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'HTML');\n        return sanitizeHtml(this._doc, String(value));\n      case SecurityContext.STYLE:\n        if (value instanceof SafeStyleImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Style');\n        return sanitizeStyle( /** @type {?} */((value as string)));\n      case SecurityContext.SCRIPT:\n        if (value instanceof SafeScriptImpl) return value.changingThisBreaksApplicationSecurity;\n        this.checkNotSafeValue(value, 'Script');\n        throw new Error('unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (value instanceof SafeResourceUrlImpl || value instanceof SafeUrlImpl) {\n          // Allow resource URLs in URL contexts, they are strictly more trusted.\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'URL');\n        return sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (value instanceof SafeResourceUrlImpl) {\n          return value.changingThisBreaksApplicationSecurity;\n        }\n        this.checkNotSafeValue(value, 'ResourceURL');\n        throw new Error(\n            'unsafe value used in a resource URL context (see http://g.co/ng/security#xss)');\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @param {?} expectedType\n * @return {?}\n */\nprivate checkNotSafeValue(value: any, expectedType: string) {\n    if (value instanceof SafeValueImpl) {\n      throw new Error(\n          `Required a safe ${expectedType}, got a ${value.getTypeName()} ` +\n          `(see http://g.co/ng/security#xss)`);\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustHtml(value: string): SafeHtml { return new SafeHtmlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustStyle(value: string): SafeStyle { return new SafeStyleImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustScript(value: string): SafeScript { return new SafeScriptImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustUrl(value: string): SafeUrl { return new SafeUrlImpl(value); }\n/**\n * @param {?} value\n * @return {?}\n */\nbypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {\n    return new SafeResourceUrlImpl(value);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSanitizerImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSanitizerImpl.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSanitizerImpl.ctorParameters;\n/** @type {?} */\nDomSanitizerImpl.prototype._doc;\n}\n\n/**\n * @abstract\n */\nabstract class SafeValueImpl implements SafeValue {\n/**\n * @param {?} changingThisBreaksApplicationSecurity\n */\nconstructor(public changingThisBreaksApplicationSecurity: string) {\n    // empty\n  }\n/**\n * @abstract\n * @return {?}\n */\ngetTypeName() {}\n/**\n * @return {?}\n */\ntoString() {\n    return `SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity}` +\n        ` (see http://g.co/ng/security#xss)`;\n  }\n}\n\nfunction SafeValueImpl_tsickle_Closure_declarations() {\n/** @type {?} */\nSafeValueImpl.prototype.changingThisBreaksApplicationSecurity;\n}\n\nclass SafeHtmlImpl extends SafeValueImpl implements SafeHtml {\n/**\n * @return {?}\n */\ngetTypeName() { return 'HTML'; }\n}\nclass SafeStyleImpl extends SafeValueImpl implements SafeStyle {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Style'; }\n}\nclass SafeScriptImpl extends SafeValueImpl implements SafeScript {\n/**\n * @return {?}\n */\ngetTypeName() { return 'Script'; }\n}\nclass SafeUrlImpl extends SafeValueImpl implements SafeUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'URL'; }\n}\nclass SafeResourceUrlImpl extends SafeValueImpl implements SafeResourceUrl {\n/**\n * @return {?}\n */\ngetTypeName() { return 'ResourceURL'; }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeUrl} from './url_sanitizer';\n/**\n * Regular expression for safe style values.\n * \n * Quotes (\" and ') are allowed, but a check must be done elsewhere to ensure they're balanced.\n * \n * ',' allows multiple values to be assigned to the same property (e.g. background-attachment or\n * font-family) and hence could allow multiple values to get injected, but that should pose no risk\n * of XSS.\n * \n * The function expression checks only for XSS safety, not for CSS validity.\n * \n * This regular expression was taken from the Closure sanitization library, and augmented for\n * transformation values.\n */\nconst VALUES = '[-,.\"\\'%_!# a-zA-Z0-9]+';\nconst /** @type {?} */ TRANSFORMATION_FNS = '(?:matrix|translate|scale|rotate|skew|perspective)(?:X|Y|3d)?';\nconst /** @type {?} */ COLOR_FNS = '(?:rgb|hsl)a?';\nconst /** @type {?} */ GRADIENTS = '(?:repeating-)?(?:linear|radial)-gradient';\nconst /** @type {?} */ CSS3_FNS = '(?:calc|attr)';\nconst /** @type {?} */ FN_ARGS = '\\\\([-0-9.%, #a-zA-Z]+\\\\)';\nconst /** @type {?} */ SAFE_STYLE_VALUE = new RegExp(\n    `^(${VALUES}|` +\n        `(?:${TRANSFORMATION_FNS}|${COLOR_FNS}|${GRADIENTS}|${CSS3_FNS})` +\n        `${FN_ARGS})$`,\n    'g');\n/**\n * Matches a `url(...)` value with an arbitrary argument as long as it does\n * not contain parentheses.\n * \n * The URL value still needs to be sanitized separately.\n * \n * `url(...)` values are a very common use case, e.g. for `background-image`. With carefully crafted\n * CSS style rules, it is possible to construct an information leak with `url` values in CSS, e.g.\n * by observing whether scroll bars are displayed, or character ranges used by a font face\n * definition.\n * \n * Angular only allows binding CSS values (as opposed to entire CSS rules), so it is unlikely that\n * binding a URL value without further cooperation from the page will cause an information leak, and\n * if so, it is just a leak, not a full blown XSS vulnerability.\n * \n * Given the common use case, low likelihood of attack vector, and low impact of an attack, this\n * code is permissive and allows URLs that sanitize otherwise.\n */\nconst URL_RE = /^url\\(([^)]+)\\)$/;\n/**\n * Checks that quotes (\" and ') are properly balanced inside a string. Assumes\n * that neither escape (\\) nor any other character that could result in\n * breaking out of a string parsing context are allowed;\n * see http://www.w3.org/TR/css3-syntax/#string-token-diagram.\n * \n * This code was taken from the Closure sanitization library.\n * @param {?} value\n * @return {?}\n */\nfunction hasBalancedQuotes(value: string) {\n  let /** @type {?} */ outsideSingle = true;\n  let /** @type {?} */ outsideDouble = true;\n  for (let /** @type {?} */ i = 0; i < value.length; i++) {\n    const /** @type {?} */ c = value.charAt(i);\n    if (c === '\\'' && outsideDouble) {\n      outsideSingle = !outsideSingle;\n    } else if (c === '\"' && outsideSingle) {\n      outsideDouble = !outsideDouble;\n    }\n  }\n  return outsideSingle && outsideDouble;\n}\n/**\n * Sanitizes the given untrusted CSS style property value (i.e. not an entire object, just a single\n * value) and returns a value that is safe to use in a browser environment.\n * @param {?} value\n * @return {?}\n */\nexport function sanitizeStyle(value: string): string {\n  value = String(value).trim();  // Make sure it's actually a string.\n  if (!value) return '';\n\n  // Single url(...) values are supported, but only for URLs that sanitize cleanly. See above for\n  // reasoning behind this.\n  const /** @type {?} */ urlMatch = value.match(URL_RE);\n  if ((urlMatch && sanitizeUrl(urlMatch[1]) === urlMatch[1]) ||\n      value.match(SAFE_STYLE_VALUE) && hasBalancedQuotes(value)) {\n    return value;  // Safe style values.\n  }\n\n  if (isDevMode()) {\n    getDOM().log(\n        `WARNING: sanitizing unsafe style value ${value} (see http://g.co/ng/security#xss).`);\n  }\n\n  return 'unsafe';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\n\nimport {sanitizeSrcset, sanitizeUrl} from './url_sanitizer';\n/**\n * A <body> element that can be safely used to parse untrusted HTML. Lazily initialized below.\n */\nlet inertElement: HTMLElement|null = null;\n/**\n * Lazily initialized to make sure the DOM adapter gets set before use.\n */\nlet DOM: DomAdapter = null !;\n/**\n * Returns an HTML element that is guaranteed to not execute code when creating elements in it.\n * @return {?}\n */\nfunction getInertElement() {\n  if (inertElement) return inertElement;\n  DOM = getDOM();\n\n  // Prefer using <template> element if supported.\n  const /** @type {?} */ templateEl = DOM.createElement('template');\n  if ('content' in templateEl) return templateEl;\n\n  const /** @type {?} */ doc = DOM.createHtmlDocument();\n  inertElement = DOM.querySelector(doc, 'body');\n  if (inertElement == null) {\n    // usually there should be only one body element in the document, but IE doesn't have any, so we\n    // need to create one.\n    const /** @type {?} */ html = DOM.createElement('html', doc);\n    inertElement = DOM.createElement('body', doc);\n    DOM.appendChild(html, inertElement);\n    DOM.appendChild(doc, html);\n  }\n  return inertElement;\n}\n/**\n * @param {?} tags\n * @return {?}\n */\nfunction tagSet(tags: string): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ t of tags.split(',')) res[t] = true;\n  return res;\n}\n/**\n * @param {...?} sets\n * @return {?}\n */\nfunction merge(...sets: {[k: string]: boolean}[]): {[k: string]: boolean} {\n  const /** @type {?} */ res: {[k: string]: boolean} = {};\n  for (const /** @type {?} */ s of sets) {\n    for (const /** @type {?} */ v in s) {\n      if (s.hasOwnProperty(v)) res[v] = true;\n    }\n  }\n  return res;\n}\n\n// Good source of info about elements and attributes\n// http://dev.w3.org/html5/spec/Overview.html#semantics\n// http://simon.html5.org/html-elements\n\n// Safe Void Elements - HTML5\n// http://dev.w3.org/html5/spec/Overview.html#void-elements\nconst /** @type {?} */ VOID_ELEMENTS = tagSet('area,br,col,hr,img,wbr');\n\n// Elements that you can, intentionally, leave open (and which close themselves)\n// http://dev.w3.org/html5/spec/Overview.html#optional-tags\nconst /** @type {?} */ OPTIONAL_END_TAG_BLOCK_ELEMENTS = tagSet('colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr');\nconst /** @type {?} */ OPTIONAL_END_TAG_INLINE_ELEMENTS = tagSet('rp,rt');\nconst /** @type {?} */ OPTIONAL_END_TAG_ELEMENTS =\n    merge(OPTIONAL_END_TAG_INLINE_ELEMENTS, OPTIONAL_END_TAG_BLOCK_ELEMENTS);\n\n// Safe Block Elements - HTML5\nconst /** @type {?} */ BLOCK_ELEMENTS = merge(\n    OPTIONAL_END_TAG_BLOCK_ELEMENTS,\n    tagSet(\n        'address,article,' +\n        'aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,' +\n        'h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul'));\n\n// Inline Elements - HTML5\nconst /** @type {?} */ INLINE_ELEMENTS = merge(\n    OPTIONAL_END_TAG_INLINE_ELEMENTS,\n    tagSet(\n        'a,abbr,acronym,audio,b,' +\n        'bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,' +\n        'samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video'));\n\nconst /** @type {?} */ VALID_ELEMENTS =\n    merge(VOID_ELEMENTS, BLOCK_ELEMENTS, INLINE_ELEMENTS, OPTIONAL_END_TAG_ELEMENTS);\n\n// Attributes that have href and hence need to be sanitized\nconst /** @type {?} */ URI_ATTRS = tagSet('background,cite,href,itemtype,longdesc,poster,src,xlink:href');\n\n// Attributes that have special href set hence need to be sanitized\nconst /** @type {?} */ SRCSET_ATTRS = tagSet('srcset');\n\nconst /** @type {?} */ HTML_ATTRS = tagSet(\n    'abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,' +\n    'compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,' +\n    'ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,' +\n    'scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,' +\n    'valign,value,vspace,width');\n\n// NB: This currently conciously doesn't support SVG. SVG sanitization has had several security\n// issues in the past, so it seems safer to leave it out if possible. If support for binding SVG via\n// innerHTML is required, SVG attributes should be added here.\n\n// NB: Sanitization does not allow <form> elements or other active elements (<button> etc). Those\n// can be sanitized, but they increase security surface area without a legitimate use case, so they\n// are left out here.\n\nconst /** @type {?} */ VALID_ATTRS = merge(URI_ATTRS, SRCSET_ATTRS, HTML_ATTRS);\n/**\n * SanitizingHtmlSerializer serializes a DOM fragment, stripping out any unsafe elements and unsafe\n * attributes.\n */\nclass SanitizingHtmlSerializer {\npublic sanitizedSomething = false;\nprivate buf: string[] = [];\n/**\n * @param {?} el\n * @return {?}\n */\nsanitizeChildren(el: Element): string {\n    // This cannot use a TreeWalker, as it has to run on Angular's various DOM adapters.\n    // However this code never accesses properties off of `document` before deleting its contents\n    // again, so it shouldn't be vulnerable to DOM clobbering.\n    let /** @type {?} */ current: Node = /** @type {?} */(( el.firstChild));\n    while (current) {\n      if (DOM.isElementNode(current)) {\n        this.startElement( /** @type {?} */((current as Element)));\n      } else if (DOM.isTextNode(current)) {\n        this.chars( /** @type {?} */((DOM.nodeValue(current))));\n      } else {\n        // Strip non-element, non-text nodes.\n        this.sanitizedSomething = true;\n      }\n      if (DOM.firstChild(current)) {\n        current = /** @type {?} */(( DOM.firstChild(current)));\n        continue;\n      }\n      while (current) {\n        // Leaving the element. Walk up and to the right, closing tags as we go.\n        if (DOM.isElementNode(current)) {\n          this.endElement( /** @type {?} */((current as Element)));\n        }\n\n        let /** @type {?} */ next = checkClobberedElement(current, /** @type {?} */(( DOM.nextSibling(current))));\n\n        if (next) {\n          current = next;\n          break;\n        }\n\n        current = checkClobberedElement(current, /** @type {?} */(( DOM.parentElement(current))));\n      }\n    }\n    return this.buf.join('');\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprivate startElement(element: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(element).toLowerCase();\n    if (!VALID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.sanitizedSomething = true;\n      return;\n    }\n    this.buf.push('<');\n    this.buf.push(tagName);\n    DOM.attributeMap(element).forEach((value: string, attrName: string) => {\n      const /** @type {?} */ lower = attrName.toLowerCase();\n      if (!VALID_ATTRS.hasOwnProperty(lower)) {\n        this.sanitizedSomething = true;\n        return;\n      }\n      // TODO(martinprobst): Special case image URIs for data:image/...\n      if (URI_ATTRS[lower]) value = sanitizeUrl(value);\n      if (SRCSET_ATTRS[lower]) value = sanitizeSrcset(value);\n      this.buf.push(' ');\n      this.buf.push(attrName);\n      this.buf.push('=\"');\n      this.buf.push(encodeEntities(value));\n      this.buf.push('\"');\n    });\n    this.buf.push('>');\n  }\n/**\n * @param {?} current\n * @return {?}\n */\nprivate endElement(current: Element) {\n    const /** @type {?} */ tagName = DOM.nodeName(current).toLowerCase();\n    if (VALID_ELEMENTS.hasOwnProperty(tagName) && !VOID_ELEMENTS.hasOwnProperty(tagName)) {\n      this.buf.push('</');\n      this.buf.push(tagName);\n      this.buf.push('>');\n    }\n  }\n/**\n * @param {?} chars\n * @return {?}\n */\nprivate chars(chars: string) { this.buf.push(encodeEntities(chars)); }\n}\n\nfunction SanitizingHtmlSerializer_tsickle_Closure_declarations() {\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.sanitizedSomething;\n/** @type {?} */\nSanitizingHtmlSerializer.prototype.buf;\n}\n\n/**\n * @param {?} node\n * @param {?} nextNode\n * @return {?}\n */\nfunction checkClobberedElement(node: Node, nextNode: Node): Node {\n  if (nextNode && DOM.contains(node, nextNode)) {\n    throw new Error(\n        `Failed to sanitize html because the element is clobbered: ${DOM.getOuterHTML(node)}`);\n  }\n  return nextNode;\n}\n\n// Regular Expressions for parsing tags and attributes\nconst /** @type {?} */ SURROGATE_PAIR_REGEXP = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n// ! to ~ is the ASCII range.\nconst /** @type {?} */ NON_ALPHANUMERIC_REGEXP = /([^\\#-~ |!])/g;\n/**\n * Escapes all potentially dangerous characters, so that the\n * resulting string can be safely inserted into attribute or\n * element text.\n * @param {?} value\n * @return {?}\n */\nfunction encodeEntities(value: string) {\n  return value.replace(/&/g, '&amp;')\n      .replace(\n          SURROGATE_PAIR_REGEXP,\n          function(match: string) {\n            const /** @type {?} */ hi = match.charCodeAt(0);\n            const /** @type {?} */ low = match.charCodeAt(1);\n            return '&#' + (((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000) + ';';\n          })\n      .replace(\n          NON_ALPHANUMERIC_REGEXP,\n          function(match: string) { return '&#' + match.charCodeAt(0) + ';'; })\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n}\n/**\n * When IE9-11 comes across an unknown namespaced attribute e.g. 'xlink:foo' it adds 'xmlns:ns1'\n * attribute to declare ns1 namespace and prefixes the attribute with 'ns1' (e.g. 'ns1:xlink:foo').\n * \n * This is undesirable since we don't want to allow any of these custom attributes. This method\n * strips them all.\n * @param {?} el\n * @return {?}\n */\nfunction stripCustomNsAttrs(el: Element) {\n  DOM.attributeMap(el).forEach((_, attrName) => {\n    if (attrName === 'xmlns:ns1' || attrName.indexOf('ns1:') === 0) {\n      DOM.removeAttribute(el, attrName);\n    }\n  });\n  for (const /** @type {?} */ n of DOM.childNodesAsList(el)) {\n    if (DOM.isElementNode(n)) stripCustomNsAttrs( /** @type {?} */((n as Element)));\n  }\n}\n/**\n * Sanitizes the given unsafe, untrusted HTML fragment, and returns HTML text that is safe to add to\n * the DOM in a browser environment.\n * @param {?} defaultDoc\n * @param {?} unsafeHtmlInput\n * @return {?}\n */\nexport function sanitizeHtml(defaultDoc: any, unsafeHtmlInput: string): string {\n  try {\n    const /** @type {?} */ containerEl = getInertElement();\n    // Make sure unsafeHtml is actually a string (TypeScript types are not enforced at runtime).\n    let /** @type {?} */ unsafeHtml = unsafeHtmlInput ? String(unsafeHtmlInput) : '';\n\n    // mXSS protection. Repeatedly parse the document to make sure it stabilizes, so that a browser\n    // trying to auto-correct incorrect HTML cannot cause formerly inert HTML to become dangerous.\n    let /** @type {?} */ mXSSAttempts = 5;\n    let /** @type {?} */ parsedHtml = unsafeHtml;\n\n    do {\n      if (mXSSAttempts === 0) {\n        throw new Error('Failed to sanitize html because the input is unstable');\n      }\n      mXSSAttempts--;\n\n      unsafeHtml = parsedHtml;\n      DOM.setInnerHTML(containerEl, unsafeHtml);\n      if (defaultDoc.documentMode) {\n        // strip custom-namespaced attributes on IE<=11\n        stripCustomNsAttrs(containerEl);\n      }\n      parsedHtml = DOM.getInnerHTML(containerEl);\n    } while (unsafeHtml !== parsedHtml);\n\n    const /** @type {?} */ sanitizer = new SanitizingHtmlSerializer();\n    const /** @type {?} */ safeHtml = sanitizer.sanitizeChildren(DOM.getTemplateContent(containerEl) || containerEl);\n\n    // Clear out the body element.\n    const /** @type {?} */ parent = DOM.getTemplateContent(containerEl) || containerEl;\n    for (const /** @type {?} */ child of DOM.childNodesAsList(parent)) {\n      DOM.removeChild(parent, child);\n    }\n\n    if (isDevMode() && sanitizer.sanitizedSomething) {\n      DOM.log('WARNING: sanitizing HTML stripped some content (see http://g.co/ng/security#xss).');\n    }\n\n    return safeHtml;\n  } catch ( /** @type {?} */e) {\n    // In case anything goes wrong, clear out inertElement to reset the entire DOM structure.\n    inertElement = null;\n    throw e;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {isDevMode} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n * \n * This regular expression matches a subset of URLs that will not cause script\n * execution if used in URL context within a HTML document. Specifically, this\n * regular expression matches if (comment from here on and regex copied from\n * Soy's EscapingConventions):\n * (1) Either a protocol in a whitelist (http, https, mailto or ftp).\n * (2) or no protocol.  A protocol must be followed by a colon. The below\n *     allows that by allowing colons only after one of the characters [/?#].\n *     A colon after a hash (#) must be in the fragment.\n *     Otherwise, a colon after a (?) must be in a query.\n *     Otherwise, a colon after a single solidus (/) must be in a path.\n *     Otherwise, a colon after a double solidus (//) must be in the authority\n *     (before port).\n * \n * The pattern disallows &, used in HTML entity declarations before\n * one of the characters in [/?#]. This disallows HTML entities used in the\n * protocol name, which should never happen, e.g. \"h&#116;tp\" for \"http\".\n * It also disallows HTML entities in the first path part of a relative path,\n * e.g. \"foo&lt;bar/baz\".  Our existing escaping functions should not produce\n * that. More importantly, it disallows masking of a colon,\n * e.g. \"javascript&#58;...\".\n * \n * This regular expression was taken from the Closure sanitization library.\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n\n/* A pattern that matches safe srcset values */\nconst /** @type {?} */ SAFE_SRCSET_PATTERN = /^(?:(?:https?|file):|[^&:/?#]*(?:[/?#]|$))/gi;\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n */\nconst DATA_URL_PATTERN =\n    /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\\/]+=*$/i;\n/**\n * @param {?} url\n * @return {?}\n */\nexport function sanitizeUrl(url: string): string {\n  url = String(url);\n  if (url.match(SAFE_URL_PATTERN) || url.match(DATA_URL_PATTERN)) return url;\n\n  if (isDevMode()) {\n    getDOM().log(`WARNING: sanitizing unsafe URL value ${url} (see http://g.co/ng/security#xss)`);\n  }\n\n  return 'unsafe:' + url;\n}\n/**\n * @param {?} srcset\n * @return {?}\n */\nexport function sanitizeSrcset(srcset: string): string {\n  srcset = String(srcset);\n  return srcset.split(',').map((srcset) => sanitizeUrl(srcset.trim())).join(', ');\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst /** @type {?} */ MODIFIER_KEY_GETTERS: {[key: string]: (event: KeyboardEvent) => boolean} = {\n  'alt': (event: KeyboardEvent) => event.altKey,\n  'control': (event: KeyboardEvent) => event.ctrlKey,\n  'meta': (event: KeyboardEvent) => event.metaKey,\n  'shift': (event: KeyboardEvent) => event.shiftKey\n};\n/**\n * \\@experimental\n */\nexport class KeyEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return KeyEventsPlugin.parseEventName(eventName) != null; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ parsedEvent = /** @type {?} */(( KeyEventsPlugin.parseEventName(eventName)));\n\n    const /** @type {?} */ outsideHandler =\n        KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n\n    return this.manager.getZone().runOutsideAngular(() => {\n      return getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nstatic parseEventName(eventName: string): {[key: string]: string}|null {\n    const /** @type {?} */ parts: string[] = eventName.toLowerCase().split('.');\n\n    const /** @type {?} */ domEventName = parts.shift();\n    if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const /** @type {?} */ key = KeyEventsPlugin._normalizeKey( /** @type {?} */((parts.pop())));\n\n    let /** @type {?} */ fullKey = '';\n    MODIFIER_KEYS.forEach(modifierName => {\n      const /** @type {?} */ index: number = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n\n    const /** @type {?} */ result: {[k: string]: string} = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n/**\n * @param {?} event\n * @return {?}\n */\nstatic getEventFullKey(event: KeyboardEvent): string {\n    let /** @type {?} */ fullKey = '';\n    let /** @type {?} */ key = getDOM().getEventKey(event);\n    key = key.toLowerCase();\n    if (key === ' ') {\n      key = 'space';  // for readability\n    } else if (key === '.') {\n      key = 'dot';  // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName != key) {\n        const /** @type {?} */ modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          fullKey += modifierName + '.';\n        }\n      }\n    });\n    fullKey += key;\n    return fullKey;\n  }\n/**\n * @param {?} fullKey\n * @param {?} handler\n * @param {?} zone\n * @return {?}\n */\nstatic eventCallback(fullKey: any, handler: Function, zone: NgZone): Function {\n    return (event: any /** TODO #9100 */) => {\n      if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n/**\n * \\@internal\n * @param {?} keyName\n * @return {?}\n */\nstatic _normalizeKey(keyName: string): string {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction KeyEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nKeyEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\n\nconst /** @type {?} */ EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n};\n/**\n * A DI token that you can use to provide{\\@link HammerGestureConfig} to Angular. Use it to configure\n * Hammer gestures.\n * \n * \\@experimental\n */\nexport const HAMMER_GESTURE_CONFIG = new InjectionToken<HammerGestureConfig>('HammerGestureConfig');\n\nexport interface HammerInstance {\n  on(eventName: string, callback?: Function): void;\n  off(eventName: string, callback?: Function): void;\n}\n/**\n * \\@experimental\n */\nexport class HammerGestureConfig {\n  events: string[] = [];\n\n  overrides: {[key: string]: Object} = {};\n/**\n * @param {?} element\n * @return {?}\n */\nbuildHammer(element: HTMLElement): HammerInstance {\n    const /** @type {?} */ mc = new Hammer(element);\n\n    mc.get('pinch').set({enable: true});\n    mc.get('rotate').set({enable: true});\n\n    for (const /** @type {?} */ eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HammerGestureConfig_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGestureConfig.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGestureConfig.ctorParameters;\n/** @type {?} */\nHammerGestureConfig.prototype.events;\n/** @type {?} */\nHammerGestureConfig.prototype.overrides;\n}\n\nexport class HammerGesturesPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n * @param {?} _config\n */\nconstructor(\n       doc: any,\nprivate _config: HammerGestureConfig) {\n    super(doc);\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!( /** @type {?} */((window as any))).Hammer) {\n      throw new Error(`Hammer.js is not loaded, can not bind ${eventName} event`);\n    }\n\n    return true;\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const /** @type {?} */ mc = this._config.buildHammer(element);\n      const /** @type {?} */ callback = function(eventObj: HammerInput) {\n        zone.runGuarded(function() { handler(eventObj); });\n      };\n      mc.on(eventName, callback);\n      return () => mc.off(eventName, callback);\n    });\n  }\n/**\n * @param {?} eventName\n * @return {?}\n */\nisCustomEvent(eventName: string): boolean { return this._config.events.indexOf(eventName) > -1; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n{type: HammerGestureConfig, decorators: [{ type: Inject, args: [HAMMER_GESTURE_CONFIG, ] }, ]},\n];\n}\n\nfunction HammerGesturesPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nHammerGesturesPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHammerGesturesPlugin.ctorParameters;\n/** @type {?} */\nHammerGesturesPlugin.prototype._config;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DOCUMENT} from '../dom_tokens';\n\nimport {EventManagerPlugin} from './event_manager';\nexport class DomEventsPlugin extends EventManagerPlugin {\n/**\n * @param {?} doc\n */\nconstructor( doc: any) { super(doc); }\n/**\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string): boolean { return true; }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    element.addEventListener(eventName, /** @type {?} */(( handler as any)), false);\n    return () => element.removeEventListener(eventName, /** @type {?} */(( handler as any)), false);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomEventsPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nDomEventsPlugin.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomEventsPlugin.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {EventManager} from './events/event_manager';\nimport {DomSharedStylesHost} from './shared_styles_host';\n\nexport const /** @type {?} */ NAMESPACE_URIS: {[ns: string]: string} = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n};\n\nconst /** @type {?} */ COMPONENT_REGEX = /%COMP%/g;\nexport const /** @type {?} */ COMPONENT_VARIABLE = '%COMP%';\nexport const /** @type {?} */ HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const /** @type {?} */ CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimContentAttribute(componentShortId: string): string {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} componentShortId\n * @return {?}\n */\nexport function shimHostAttribute(componentShortId: string): string {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n/**\n * @param {?} compId\n * @param {?} styles\n * @param {?} target\n * @return {?}\n */\nexport function flattenStyles(\n    compId: string, styles: Array<any|any[]>, target: string[]): string[] {\n  for (let /** @type {?} */ i = 0; i < styles.length; i++) {\n    let /** @type {?} */ style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n  return target;\n}\n/**\n * @param {?} eventHandler\n * @return {?}\n */\nfunction decoratePreventDefault(eventHandler: Function): Function {\n  return (event: any) => {\n    const /** @type {?} */ allowDefaultBehavior = eventHandler(event);\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n  };\n}\nexport class DomRendererFactory2 implements RendererFactory2 {\nprivate rendererByCompId = new Map<string, Renderer2>();\nprivate defaultRenderer: Renderer2;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n */\nconstructor(private eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost) {\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  };\n/**\n * @param {?} element\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(element: any, type: RendererType2|null): Renderer2 {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated: {\n        let /** @type {?} */ renderer = this.rendererByCompId.get(type.id);\n        if (!renderer) {\n          renderer =\n              new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type);\n          this.rendererByCompId.set(type.id, renderer);\n        }\n        ( /** @type {?} */((<EmulatedEncapsulationDomRenderer2>renderer))).applyToHost(element);\n        return renderer;\n      }\n      case ViewEncapsulation.Native:\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n      default: {\n        if (!this.rendererByCompId.has(type.id)) {\n          const /** @type {?} */ styles = flattenStyles(type.id, type.styles, []);\n          this.sharedStylesHost.addStyles(styles);\n          this.rendererByCompId.set(type.id, this.defaultRenderer);\n        }\n        return this.defaultRenderer;\n      }\n    }\n  }\n/**\n * @return {?}\n */\nbegin() {}\n/**\n * @return {?}\n */\nend() {}\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: EventManager, },\n{type: DomSharedStylesHost, },\n];\n}\n\nfunction DomRendererFactory2_tsickle_Closure_declarations() {\n/** @type {?} */\nDomRendererFactory2.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomRendererFactory2.ctorParameters;\n/** @type {?} */\nDomRendererFactory2.prototype.rendererByCompId;\n/** @type {?} */\nDomRendererFactory2.prototype.defaultRenderer;\n/** @type {?} */\nDomRendererFactory2.prototype.eventManager;\n/** @type {?} */\nDomRendererFactory2.prototype.sharedStylesHost;\n}\n\nclass DefaultDomRenderer2 implements Renderer2 {\n  data: {[key: string]: any} = Object.create(null);\n/**\n * @param {?} eventManager\n */\nconstructor(private eventManager: EventManager) {}\n/**\n * @return {?}\n */\ndestroy(): void {}\n\n  destroyNode: null;\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string): any {\n    if (namespace) {\n      return document.createElementNS(NAMESPACE_URIS[namespace], name);\n    }\n\n    return document.createElement(name);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string): any { return document.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string): any { return document.createTextNode(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void { parent.appendChild(newChild); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    if (parent) {\n      parent.insertBefore(newChild, refChild);\n    }\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: string|any): any {\n    let /** @type {?} */ el: any = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n                                                       selectorOrNode;\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    el.textContent = '';\n    return el;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any { return node.parentNode; }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any): any { return node.nextSibling; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string): void {\n    if (namespace) {\n      name = `${namespace}:${name}`;\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string): void {\n    if (namespace) {\n      const /** @type {?} */ namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { el.classList.add(name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { el.classList.remove(name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.setProperty(\n          style, value, !!(flags & RendererStyleFlags2.Important) ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { node.nodeValue = value; }\n/**\n * @param {?} target\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, event: string, callback: (event: any) => boolean):\n      () => void {\n    checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      return /** @type {?} */(( <() => void>this.eventManager.addGlobalEventListener(\n          target, event, decoratePreventDefault(callback))));\n    }\n    return /** @type {?} */(( /** @type {?} */(( <() => void>this.eventManager.addEventListener(\n               target, event, decoratePreventDefault(callback)))) as() => void));\n  }\n}\n\nfunction DefaultDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nDefaultDomRenderer2.prototype.data;\n/** @type {?} */\nDefaultDomRenderer2.prototype.destroyNode;\n/** @type {?} */\nDefaultDomRenderer2.prototype.eventManager;\n}\n\n\nconst /** @type {?} */ AT_CHARCODE = '@'.charCodeAt(0);\n/**\n * @param {?} name\n * @param {?} nameKind\n * @return {?}\n */\nfunction checkNoSyntheticProp(name: string, nameKind: string) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(\n        `Found the synthetic ${nameKind} ${name}. Please include either \"BrowserAnimationsModule\" or \"NoopAnimationsModule\" in your application.`);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\nprivate contentAttr: string;\nprivate hostAttr: string;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager, sharedStylesHost: DomSharedStylesHost,\nprivate component: RendererType2) {\n    super(eventManager);\n    const styles = flattenStyles(component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n\n    this.contentAttr = shimContentAttribute(component.id);\n    this.hostAttr = shimHostAttribute(component.id);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\napplyToHost(element: any) { super.setAttribute(element, this.hostAttr, ''); }\n/**\n * @param {?} parent\n * @param {?} name\n * @return {?}\n */\ncreateElement(parent: any, name: string): Element {\n    const /** @type {?} */ el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\n\nfunction EmulatedEncapsulationDomRenderer2_tsickle_Closure_declarations() {\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.contentAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.hostAttr;\n/** @type {?} */\nEmulatedEncapsulationDomRenderer2.prototype.component;\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\nprivate shadowRoot: any;\n/**\n * @param {?} eventManager\n * @param {?} sharedStylesHost\n * @param {?} hostEl\n * @param {?} component\n */\nconstructor(\n      eventManager: EventManager,\nprivate sharedStylesHost: DomSharedStylesHost,\nprivate hostEl: any,\nprivate component: RendererType2) {\n    super(eventManager);\n    this.shadowRoot = (hostEl as any).createShadowRoot();\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nprivate nodeOrShadowRoot(node: any): any { return node === this.hostEl ? this.shadowRoot : node; }\n/**\n * @return {?}\n */\ndestroy() { this.sharedStylesHost.removeHost(this.shadowRoot); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any): any {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n}\n\nfunction ShadowDomRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nShadowDomRenderer.prototype.shadowRoot;\n/** @type {?} */\nShadowDomRenderer.prototype.sharedStylesHost;\n/** @type {?} */\nShadowDomRenderer.prototype.hostEl;\n/** @type {?} */\nShadowDomRenderer.prototype.component;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, OnDestroy} from '@angular/core';\nimport {getDOM} from './dom_adapter';\nimport {DOCUMENT} from './dom_tokens';\nexport class SharedStylesHost {\n/**\n * \\@internal\n */\nprotected _stylesSet = new Set<string>();\n/**\n * @param {?} styles\n * @return {?}\n */\naddStyles(styles: string[]): void {\n    const /** @type {?} */ additions = new Set<string>();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {}\n/**\n * @return {?}\n */\ngetAllStyles(): string[] { return Array.from(this._stylesSet); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSharedStylesHost.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nSharedStylesHost.prototype._stylesSet;\n}\n\nexport class DomSharedStylesHost extends SharedStylesHost implements OnDestroy {\nprivate _hostNodes = new Set<Node>();\nprivate _styleNodes = new Set<Node>();\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._hostNodes.add(_doc.head);\n  }\n/**\n * @param {?} styles\n * @param {?} host\n * @return {?}\n */\nprivate _addStylesToHost(styles: Set<string>, host: Node): void {\n    styles.forEach((style: string) => {\n      const /** @type {?} */ styleEl = this._doc.createElement('style');\n      styleEl.textContent = style;\n      this._styleNodes.add(host.appendChild(styleEl));\n    });\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\naddHost(hostNode: Node): void {\n    this._addStylesToHost(this._stylesSet, hostNode);\n    this._hostNodes.add(hostNode);\n  }\n/**\n * @param {?} hostNode\n * @return {?}\n */\nremoveHost(hostNode: Node): void { this._hostNodes.delete(hostNode); }\n/**\n * @param {?} additions\n * @return {?}\n */\nonStylesAdded(additions: Set<string>): void {\n    this._hostNodes.forEach(hostNode => this._addStylesToHost(additions, hostNode));\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this._styleNodes.forEach(styleNode => getDOM().remove(styleNode)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction DomSharedStylesHost_tsickle_Closure_declarations() {\n/** @type {?} */\nDomSharedStylesHost.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDomSharedStylesHost.ctorParameters;\n/** @type {?} */\nDomSharedStylesHost.prototype._hostNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._styleNodes;\n/** @type {?} */\nDomSharedStylesHost.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken, NgZone} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\n/**\n * \\@stable\n */\nexport const EVENT_MANAGER_PLUGINS =\n    new InjectionToken<EventManagerPlugin[]>('EventManagerPlugins');\n/**\n * \\@stable\n */\nexport class EventManager {\nprivate _plugins: EventManagerPlugin[];\nprivate _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n/**\n * @param {?} plugins\n * @param {?} _zone\n */\nconstructor( plugins: EventManagerPlugin[],\nprivate _zone: NgZone) {\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(target: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ plugin = this._findPluginFor(eventName);\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n/**\n * @return {?}\n */\ngetZone(): NgZone { return this._zone; }\n/**\n * \\@internal\n * @param {?} eventName\n * @return {?}\n */\n_findPluginFor(eventName: string): EventManagerPlugin {\n    const /** @type {?} */ plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const /** @type {?} */ plugins = this._plugins;\n    for (let /** @type {?} */ i = 0; i < plugins.length; i++) {\n      const /** @type {?} */ plugin = plugins[i];\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n      }\n    }\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Inject, args: [EVENT_MANAGER_PLUGINS, ] }, ]},\n{type: NgZone, },\n];\n}\n\nfunction EventManager_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManager.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nEventManager.ctorParameters;\n/** @type {?} */\nEventManager.prototype._plugins;\n/** @type {?} */\nEventManager.prototype._eventNameToPlugin;\n/** @type {?} */\nEventManager.prototype._zone;\n}\n\n/**\n * @abstract\n */\nexport abstract class EventManagerPlugin {\n/**\n * @param {?} _doc\n */\nconstructor(private _doc: any) {}\n\n  manager: EventManager;\n/**\n * @abstract\n * @param {?} eventName\n * @return {?}\n */\nsupports(eventName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddEventListener(element: HTMLElement, eventName: string, handler: Function) {}\n/**\n * @param {?} element\n * @param {?} eventName\n * @param {?} handler\n * @return {?}\n */\naddGlobalEventListener(element: string, eventName: string, handler: Function): Function {\n    const /** @type {?} */ target: HTMLElement = getDOM().getGlobalEventTarget(this._doc, element);\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n    return this.addEventListener(target, eventName, handler);\n  };\n}\n\nfunction EventManagerPlugin_tsickle_Closure_declarations() {\n/** @type {?} */\nEventManagerPlugin.prototype.manager;\n/** @type {?} */\nEventManagerPlugin.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport * as core from '@angular/core';\nimport {exportNgVar} from '../util';\n\nconst /** @type {?} */ CORE_TOKENS = {\n  'ApplicationRef': core.ApplicationRef,\n  'NgZone': core.NgZone,\n};\n\nconst /** @type {?} */ INSPECT_GLOBAL_NAME = 'probe';\nconst /** @type {?} */ CORE_TOKENS_GLOBAL_NAME = 'coreTokens';\n/**\n * Returns a {\\@link DebugElement} for the given native DOM element, or\n * null if the given native element does not have an Angular view associated\n * with it.\n * @param {?} element\n * @return {?}\n */\nexport function inspectNativeElement(element: any): core.DebugNode|null {\n  return core.getDebugNode(element);\n}\n/**\n * Deprecated. Use the one from '\\@angular/core'.\n * @deprecated\n */\nexport class NgProbeToken {\n/**\n * @param {?} name\n * @param {?} token\n */\nconstructor(public name: string,\npublic token: any) {}\n}\n\nfunction NgProbeToken_tsickle_Closure_declarations() {\n/** @type {?} */\nNgProbeToken.prototype.name;\n/** @type {?} */\nNgProbeToken.prototype.token;\n}\n\n/**\n * @param {?} extraTokens\n * @param {?} coreTokens\n * @return {?}\n */\nexport function _createNgProbe(extraTokens: NgProbeToken[], coreTokens: core.NgProbeToken[]): any {\n  const /** @type {?} */ tokens = (extraTokens || []).concat(coreTokens || []);\n  exportNgVar(INSPECT_GLOBAL_NAME, inspectNativeElement);\n  exportNgVar(CORE_TOKENS_GLOBAL_NAME, {...CORE_TOKENS, ..._ngProbeTokensToMap(tokens || [])});\n  return () => inspectNativeElement;\n}\n/**\n * @param {?} tokens\n * @return {?}\n */\nfunction _ngProbeTokensToMap(tokens: NgProbeToken[]): {[name: string]: any} {\n  return tokens.reduce((prev: any, t: any) => (prev[t.name] = t.token, prev), {});\n}\n/**\n * Providers which support debugging Angular applications (e.g. via `ng.probe`).\n */\nexport const ELEMENT_PROBE_PROVIDERS: core.Provider[] = [\n  {\n    provide: core.APP_INITIALIZER,\n    useFactory: _createNgProbe,\n    deps: [\n      [NgProbeToken, new core.Optional()],\n      [core.NgProbeToken, new core.Optional()],\n    ],\n    multi: true,\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵglobal as global} from '@angular/core';\n\nconst /** @type {?} */ CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst /** @type {?} */ DASH_CASE_REGEXP = /-([a-z])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function camelCaseToDashCase(input: string): string {\n  return input.replace(CAMEL_CASE_REGEXP, (...m: string[]) => '-' + m[1].toLowerCase());\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: string[]) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param {?} name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param {?} value The value to export.\n * @return {?}\n */\nexport function exportNgVar(name: string, value: any): void {\n  if (!ng) {\n    global['ng'] = ng = ( /** @type {?} */((global['ng'] as{[key: string]: any} | undefined))) || {};\n  }\n  ng[name] = value;\n}\n\nlet /** @type {?} */ ng: {[key: string]: any}|undefined;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * A service that can be used to get and set the title of a current HTML document.\n * \n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n * \n * \\@experimental\n */\nexport class Title {\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {}\n/**\n * Get the title of the current HTML document.\n * @return {?}\n */\ngetTitle(): string { return getDOM().getTitle(this._doc); }\n/**\n * Set the title of the current HTML document.\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(newTitle: string) { getDOM().setTitle(this._doc, newTitle); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Title_tsickle_Closure_declarations() {\n/** @type {?} */\nTitle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitle.ctorParameters;\n/** @type {?} */\nTitle.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {GetTestability, Testability, TestabilityRegistry, setTestabilityGetter, ɵglobal as global} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nexport class BrowserGetTestability implements GetTestability {\n/**\n * @return {?}\n */\nstatic init() { setTestabilityGetter(new BrowserGetTestability()); }\n/**\n * @param {?} registry\n * @return {?}\n */\naddToWindow(registry: TestabilityRegistry): void {\n    global['getAngularTestability'] = (elem: any, findInAncestors: boolean = true) => {\n      const /** @type {?} */ testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n      return testability;\n    };\n\n    global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const /** @type {?} */ whenAllStable = (callback: any /** TODO #9100 */) => {\n      const /** @type {?} */ testabilities = global['getAllAngularTestabilities']();\n      let /** @type {?} */ count = testabilities.length;\n      let /** @type {?} */ didWork = false;\n      const /** @type {?} */ decrement = function(didWork_: any /** TODO #9100 */) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(function(testability: any /** TODO #9100 */) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!global['frameworkStabilizers']) {\n      global['frameworkStabilizers'] = [];\n    }\n    global['frameworkStabilizers'].push(whenAllStable);\n  }\n/**\n * @param {?} registry\n * @param {?} elem\n * @param {?} findInAncestors\n * @return {?}\n */\nfindTestabilityInTree(registry: TestabilityRegistry, elem: any, findInAncestors: boolean):\n      Testability|null {\n    if (elem == null) {\n      return null;\n    }\n    const /** @type {?} */ t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, getDOM().getHost(elem), true);\n    }\n    return this.findTestabilityInTree(registry, getDOM().parentElement(elem), true);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {APP_INITIALIZER, ApplicationInitStatus, Inject, InjectionToken, Injector, Provider} from '@angular/core';\n\nimport {getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nexport const TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n/**\n * @param {?} transitionId\n * @param {?} document\n * @param {?} injector\n * @return {?}\n */\nexport function appInitializerFactory(transitionId: string, document: any, injector: Injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const /** @type {?} */ dom = getDOM();\n      const /** @type {?} */ styles: any[] =\n          Array.prototype.slice.apply(dom.querySelectorAll(document, `style[ng-transition]`));\n      styles.filter(el => dom.getAttribute(el, 'ng-transition') === transitionId)\n          .forEach(el => dom.remove(el));\n    });\n  };\n}\n\nexport const /** @type {?} */ SERVER_TRANSITION_PROVIDERS: Provider[] = [\n  {\n    provide: APP_INITIALIZER,\n    useFactory: appInitializerFactory,\n    deps: [TRANSITION_ID, DOCUMENT, Injector],\n    multi: true\n  },\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable} from '@angular/core';\n\nimport {DomAdapter, getDOM} from '../dom/dom_adapter';\nimport {DOCUMENT} from '../dom/dom_tokens';\n\n\n/**\n * Represents a meta element.\n *\n * @experimental\n */\nexport type MetaDefinition = {\n  charset?: string; content?: string; httpEquiv?: string; id?: string; itemprop?: string;\n  name?: string;\n  property?: string;\n  scheme?: string;\n  url?: string;\n} &\n{\n  // TODO(IgorMinar): this type looks wrong\n  [prop: string]: string;\n};\n/**\n * A service that can be used to get and add meta tags.\n * \n * \\@experimental\n */\nexport class Meta {\nprivate _dom: DomAdapter;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) { this._dom = getDOM(); }\n/**\n * @param {?} tag\n * @param {?=} forceCreation\n * @return {?}\n */\naddTag(tag: MetaDefinition, forceCreation: boolean = false): HTMLMetaElement|null {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n/**\n * @param {?} tags\n * @param {?=} forceCreation\n * @return {?}\n */\naddTags(tags: MetaDefinition[], forceCreation: boolean = false): HTMLMetaElement[] {\n    if (!tags) return [];\n    return tags.reduce((result: HTMLMetaElement[], tag: MetaDefinition) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTag(attrSelector: string): HTMLMetaElement|null {\n    if (!attrSelector) return null;\n    return this._dom.querySelector(this._doc, `meta[${attrSelector}]`);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\ngetTags(attrSelector: string): HTMLMetaElement[] {\n    if (!attrSelector) return [];\n    const /** @type {?} */ list /*NodeList*/ = this._dom.querySelectorAll(this._doc, `meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n/**\n * @param {?} tag\n * @param {?=} selector\n * @return {?}\n */\nupdateTag(tag: MetaDefinition, selector?: string): HTMLMetaElement|null {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const /** @type {?} */ meta: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n/**\n * @param {?} attrSelector\n * @return {?}\n */\nremoveTag(attrSelector: string): void { this.removeTagElement( /** @type {?} */((this.getTag(attrSelector)))); }\n/**\n * @param {?} meta\n * @return {?}\n */\nremoveTagElement(meta: HTMLMetaElement): void {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n/**\n * @param {?} meta\n * @param {?=} forceCreation\n * @return {?}\n */\nprivate _getOrCreateElement(meta: MetaDefinition, forceCreation: boolean = false):\n      HTMLMetaElement {\n    if (!forceCreation) {\n      const /** @type {?} */ selector: string = this._parseSelector(meta);\n      const /** @type {?} */ elem: HTMLMetaElement = /** @type {?} */(( this.getTag(selector)));\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      if (elem && this._containsAttributes(meta, elem)) return elem;\n    }\n    const /** @type {?} */ element: HTMLMetaElement = /** @type {?} */(( this._dom.createElement('meta') as HTMLMetaElement));\n    this._setMetaElementAttributes(meta, element);\n    const /** @type {?} */ head = this._dom.getElementsByTagName(this._doc, 'head')[0];\n    this._dom.appendChild(head, element);\n    return element;\n  }\n/**\n * @param {?} tag\n * @param {?} el\n * @return {?}\n */\nprivate _setMetaElementAttributes(tag: MetaDefinition, el: HTMLMetaElement): HTMLMetaElement {\n    Object.keys(tag).forEach((prop: string) => this._dom.setAttribute(el, prop, tag[prop]));\n    return el;\n  }\n/**\n * @param {?} tag\n * @return {?}\n */\nprivate _parseSelector(tag: MetaDefinition): string {\n    const /** @type {?} */ attr: string = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n/**\n * @param {?} tag\n * @param {?} elem\n * @return {?}\n */\nprivate _containsAttributes(tag: MetaDefinition, elem: HTMLMetaElement): boolean {\n    return Object.keys(tag).every((key: string) => this._dom.getAttribute(elem, key) === tag[key]);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction Meta_tsickle_Closure_declarations() {\n/** @type {?} */\nMeta.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMeta.ctorParameters;\n/** @type {?} */\nMeta.prototype._dom;\n/** @type {?} */\nMeta.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {LocationChangeListener, PlatformLocation} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\nimport {getDOM} from '../../dom/dom_adapter';\nimport {DOCUMENT} from '../../dom/dom_tokens';\n\nimport {supportsState} from './history';\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n */\nexport class BrowserPlatformLocation extends PlatformLocation {\nprivate _location: Location;\nprivate _history: History;\n/**\n * @param {?} _doc\n */\nconstructor(\nprivate _doc: any) {\n    super();\n    this._init();\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_init() {\n    this._location = getDOM().getLocation();\n    this._history = getDOM().getHistory();\n  }\n/**\n * @return {?}\n */\nget location(): Location { return this._location; }\n/**\n * @return {?}\n */\ngetBaseHrefFromDOM(): string { return /** @type {?} */(( getDOM().getBaseHref(this._doc))); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('popstate', fn, false);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener): void {\n    getDOM().getGlobalEventTarget(this._doc, 'window').addEventListener('hashchange', fn, false);\n  }\n/**\n * @return {?}\n */\nget pathname(): string { return this._location.pathname; }\n/**\n * @return {?}\n */\nget search(): string { return this._location.search; }\n/**\n * @return {?}\n */\nget hash(): string { return this._location.hash; }\n/**\n * @param {?} newPath\n * @return {?}\n */\nset pathname(newPath: string) { this._location.pathname = newPath; }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.pushState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string): void {\n    if (supportsState()) {\n      this._history.replaceState(state, title, url);\n    } else {\n      this._location.hash = url;\n    }\n  }\n/**\n * @return {?}\n */\nforward(): void { this._history.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._history.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\nfunction BrowserPlatformLocation_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserPlatformLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserPlatformLocation.ctorParameters;\n/** @type {?} */\nBrowserPlatformLocation.prototype._location;\n/** @type {?} */\nBrowserPlatformLocation.prototype._history;\n/** @type {?} */\nBrowserPlatformLocation.prototype._doc;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @return {?}\n */\nexport function supportsState(): boolean {\n  return !!window.history.pushState;\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * A DI Token representing the main rendering context. In a browser this is the DOM Document.\n * \n * Note: Document might not be available in the Application Context when Application and Rendering\n * Contexts are not the same (e.g. when running the application into a Web Worker).\n * \n * \\@stable\n */\nexport const DOCUMENT = new InjectionToken<Document>('DocumentToken');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵglobal as global} from '@angular/core';\nimport {setRootDomAdapter} from '../dom/dom_adapter';\n\nimport {GenericBrowserDomAdapter} from './generic_browser_adapter';\n\nconst /** @type {?} */ _attrToPropMap = {\n  'class': 'className',\n  'innerHtml': 'innerHTML',\n  'readonly': 'readOnly',\n  'tabindex': 'tabIndex',\n};\n\nconst /** @type {?} */ DOM_KEY_LOCATION_NUMPAD = 3;\n\n// Map to convert some key or keyIdentifier values to what will be returned by getEventKey\nconst /** @type {?} */ _keyMap: {[k: string]: string} = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf http://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n\n// There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\nconst /** @type {?} */ _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n\nlet /** @type {?} */ nodeContains: (a: any, b: any) => boolean;\n\nif (global['Node']) {\n  nodeContains = global['Node'].prototype.contains || function(node) {\n    return !!(this.compareDocumentPosition(node) & 16);\n  };\n}\nexport class BrowserDomAdapter extends GenericBrowserDomAdapter {\n/**\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) { throw new Error('parse not implemented'); }\n/**\n * @return {?}\n */\nstatic makeCurrent() { setRootDomAdapter(new BrowserDomAdapter()); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: Node, name: string): boolean { return name in element; }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Node, name: string, value: any) { ( /** @type {?} */((<any>el)))[name] = value; }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Node, name: string): any { return ( /** @type {?} */((<any>el)))[name]; }\n/**\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Node, methodName: string, args: any[]): any { ( /** @type {?} */((<any>el)))[methodName](...args); }\n/**\n * @param {?} error\n * @return {?}\n */\nlogError(error: string): void {\n    if (window.console) {\n      if (console.error) {\n        console.error(error);\n      } else {\n        console.log(error);\n      }\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlog(error: string): void {\n    if (window.console) {\n      window.console.log && window.console.log(error);\n    }\n  }\n/**\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: string): void {\n    if (window.console) {\n      window.console.group && window.console.group(error);\n    }\n  }\n/**\n * @return {?}\n */\nlogGroupEnd(): void {\n    if (window.console) {\n      window.console.groupEnd && window.console.groupEnd();\n    }\n  }\n/**\n * @return {?}\n */\nget attrToPropMap(): any { return _attrToPropMap; }\n/**\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any): boolean { return nodeContains.call(nodeA, nodeB); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: Element, selector: string): any { return el.querySelector(selector); }\n/**\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string): any[] { return el.querySelectorAll(selector); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: Node, evt: any, listener: any) { el.addEventListener(evt, listener, false); }\n/**\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: Node, evt: any, listener: any): Function {\n    el.addEventListener(evt, listener, false);\n    // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n    return () => { el.removeEventListener(evt, listener, false); };\n  }\n/**\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: Node, evt: any) { el.dispatchEvent(evt); }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: string): MouseEvent {\n    const /** @type {?} */ evt: MouseEvent = document.createEvent('MouseEvent');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: any): Event {\n    const /** @type {?} */ evt: Event = document.createEvent('Event');\n    evt.initEvent(eventType, true, true);\n    return evt;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: Event) {\n    evt.preventDefault();\n    evt.returnValue = false;\n  }\n/**\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: Event): boolean {\n    return evt.defaultPrevented || evt.returnValue != null && !evt.returnValue;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: HTMLElement): string { return el.innerHTML; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: Node): Node|null {\n    return 'content' in el && el instanceof HTMLTemplateElement ? el.content : null;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: HTMLElement): string { return el.outerHTML; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeName(node: Node): string { return node.nodeName; }\n/**\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: Node): string|null { return node.nodeValue; }\n/**\n * @param {?} node\n * @return {?}\n */\ntype(node: HTMLInputElement): string { return node.type; }\n/**\n * @param {?} node\n * @return {?}\n */\ncontent(node: Node): Node {\n    if (this.hasProperty(node, 'content')) {\n      return ( /** @type {?} */((<any>node))).content;\n    } else {\n      return node;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: Node): Node|null { return el.firstChild; }\n/**\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: Node): Node|null { return el.nextSibling; }\n/**\n * @param {?} el\n * @return {?}\n */\nparentElement(el: Node): Node|null { return el.parentNode; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any): Node[] { return el.childNodes; }\n/**\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: Node): any[] {\n    const /** @type {?} */ childNodes = el.childNodes;\n    const /** @type {?} */ res = new Array(childNodes.length);\n    for (let /** @type {?} */ i = 0; i < childNodes.length; i++) {\n      res[i] = childNodes[i];\n    }\n    return res;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: Node) {\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n  }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: Node, node: Node) { el.appendChild(node); }\n/**\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: Node, node: Node) { el.removeChild(node); }\n/**\n * @param {?} el\n * @param {?} newChild\n * @param {?} oldChild\n * @return {?}\n */\nreplaceChild(el: Node, newChild: Node, oldChild: Node) { el.replaceChild(newChild, oldChild); }\n/**\n * @param {?} node\n * @return {?}\n */\nremove(node: Node): Node {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n    return node;\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: Node, ref: Node, node: Node) { parent.insertBefore(node, ref); }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: Node, ref: Node, nodes: Node[]) {\n    nodes.forEach((n: any) => parent.insertBefore(n, ref));\n  }\n/**\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: Node, ref: Node, node: any) { parent.insertBefore(node, ref.nextSibling); }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: Element, value: string) { el.innerHTML = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetText(el: Node): string|null { return el.textContent; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: Node, value: string) { el.textContent = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any): string { return el.value; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) { el.value = value; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any): boolean { return el.checked; }\n/**\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) { el.checked = value; }\n/**\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string): Comment { return document.createComment(text); }\n/**\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any): HTMLElement {\n    const /** @type {?} */ t = document.createElement('template');\n    t.innerHTML = html;\n    return t;\n  }\n/**\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: string, doc = document): HTMLElement { return doc.createElement(tagName); }\n/**\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc = document): Element {\n    return doc.createElementNS(ns, tagName);\n  }\n/**\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc = document): Text { return doc.createTextNode(text); }\n/**\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc = document): HTMLScriptElement {\n    const /** @type {?} */ el = /** @type {?} */(( <HTMLScriptElement>doc.createElement('SCRIPT')));\n    el.setAttribute(attrName, attrValue);\n    return el;\n  }\n/**\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc = document): HTMLStyleElement {\n    const /** @type {?} */ style = /** @type {?} */(( <HTMLStyleElement>doc.createElement('style')));\n    this.appendChild(style, this.createTextNode(css));\n    return style;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).createShadowRoot(); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: HTMLElement): DocumentFragment { return ( /** @type {?} */((<any>el))).shadowRoot; }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHost(el: HTMLElement): HTMLElement { return ( /** @type {?} */((<any>el))).host; }\n/**\n * @param {?} node\n * @return {?}\n */\nclone(node: Node): Node { return node.cloneNode(true); }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByClassName(name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string): HTMLElement[] {\n    return element.getElementsByTagName(name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclassList(element: any): any[] { return Array.prototype.slice.call(element.classList, 0); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) { element.classList.add(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) { element.classList.remove(className); }\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string): boolean {\n    return element.classList.contains(className);\n  }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {\n    element.style[styleName] = styleValue;\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\nremoveStyle(element: any, stylename: string) {\n    // IE requires '' instead of null\n    // see https://github.com/angular/angular/issues/7916\n    element.style[stylename] = '';\n  }\n/**\n * @param {?} element\n * @param {?} stylename\n * @return {?}\n */\ngetStyle(element: any, stylename: string): string { return element.style[stylename]; }\n/**\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string|null): boolean {\n    const /** @type {?} */ value = this.getStyle(element, styleName) || '';\n    return styleValue ? value == styleValue : value.length > 0;\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ntagName(element: any): string { return element.tagName; }\n/**\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any): Map<string, string> {\n    const /** @type {?} */ res = new Map<string, string>();\n    const /** @type {?} */ elAttrs = element.attributes;\n    for (let /** @type {?} */ i = 0; i < elAttrs.length; i++) {\n      const /** @type {?} */ attrib = elAttrs[i];\n      res.set(attrib.name, attrib.value);\n    }\n    return res;\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: Element, attribute: string): boolean {\n    return element.hasAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: Element, ns: string, attribute: string): boolean {\n    return element.hasAttributeNS(ns, attribute);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: Element, attribute: string): string|null {\n    return element.getAttribute(attribute);\n  }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\ngetAttributeNS(element: Element, ns: string, name: string): string {\n    return element.getAttributeNS(ns, name);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: Element, name: string, value: string) { element.setAttribute(name, value); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: Element, ns: string, name: string, value: string) {\n    element.setAttributeNS(ns, name, value);\n  }\n/**\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: Element, attribute: string) { element.removeAttribute(attribute); }\n/**\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @return {?}\n */\nremoveAttributeNS(element: Element, ns: string, name: string) {\n    element.removeAttributeNS(ns, name);\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: Node): any { return this.isTemplateElement(el) ? this.content(el) : el; }\n/**\n * @return {?}\n */\ncreateHtmlDocument(): HTMLDocument {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: Element): any {\n    try {\n      return el.getBoundingClientRect();\n    } catch ( /** @type {?} */e) {\n      return {top: 0, bottom: 0, left: 0, right: 0, width: 0, height: 0};\n    }\n  }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document): string { return document.title; }\n/**\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) { document.title = newTitle || ''; }\n/**\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string): boolean {\n    if (n instanceof HTMLElement) {\n      return n.matches && n.matches(selector) ||\n          n.msMatchesSelector && n.msMatchesSelector(selector) ||\n          n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n\n    return false;\n  }\n/**\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: Node): boolean {\n    return el instanceof HTMLElement && el.nodeName == 'TEMPLATE';\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: Node): boolean { return node.nodeType === Node.TEXT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: Node): boolean { return node.nodeType === Node.COMMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: Node): boolean { return node.nodeType === Node.ELEMENT_NODE; }\n/**\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any): boolean {\n    return node.shadowRoot != null && node instanceof HTMLElement;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any): boolean { return node instanceof DocumentFragment; }\n/**\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc(node: Node): any { return document.importNode(this.templateAwareRoot(node), true); }\n/**\n * @param {?} node\n * @return {?}\n */\nadoptNode(node: Node): any { return document.adoptNode(node); }\n/**\n * @param {?} el\n * @return {?}\n */\ngetHref(el: Element): string { return ( /** @type {?} */((<any>el))).href; }\n/**\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any): string {\n    let /** @type {?} */ key = event.key;\n    if (key == null) {\n      key = event.keyIdentifier;\n      // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n      // Safari cf\n      // http://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n      if (key == null) {\n        return 'Unidentified';\n      }\n      if (key.startsWith('U+')) {\n        key = String.fromCharCode(parseInt(key.substring(2), 16));\n        if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n          // There is a bug in Chrome for numeric keypad keys:\n          // https://code.google.com/p/chromium/issues/detail?id=155654\n          // 1, 2, 3 ... are reported as A, B, C ...\n          key = ( /** @type {?} */((_chromeNumKeyPadMap as any)))[key];\n        }\n      }\n    }\n\n    return _keyMap[key] || key;\n  }\n/**\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string): EventTarget|null {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return document;\n    }\n    if (target === 'body') {\n      return document.body;\n    }\n    return null;\n  }\n/**\n * @return {?}\n */\ngetHistory(): History { return window.history; }\n/**\n * @return {?}\n */\ngetLocation(): Location { return window.location; }\n/**\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document): string|null {\n    const /** @type {?} */ href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n/**\n * @return {?}\n */\nresetBaseElement(): void { baseElement = null; }\n/**\n * @return {?}\n */\ngetUserAgent(): string { return window.navigator.userAgent; }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: Element, name: string, value: string) {\n    this.setAttribute(element, 'data-' + name, value);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: Element, name: string): string|null {\n    return this.getAttribute(element, 'data-' + name);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any): any { return getComputedStyle(element); }\n/**\n * @return {?}\n */\nsupportsWebAnimation(): boolean {\n    return typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n  }\n/**\n * @return {?}\n */\nperformanceNow(): number {\n    // performance.now() is not available in all browsers, see\n    // http://caniuse.com/#search=performance.now\n    return window.performance && window.performance.now ? window.performance.now() :\n                                                          new Date().getTime();\n  }\n/**\n * @return {?}\n */\nsupportsCookies(): boolean { return true; }\n/**\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string): string|null { return parseCookieValue(document.cookie, name); }\n/**\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {\n    // document.cookie is magical, assigning into it assigns/overrides one cookie value, but does\n    // not clear other cookies.\n    document.cookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);\n  }\n}\n\nlet /** @type {?} */ baseElement: HTMLElement|null = null;\n/**\n * @return {?}\n */\nfunction getBaseElementHref(): string|null {\n  if (!baseElement) {\n    baseElement = /** @type {?} */(( document.querySelector('base')));\n    if (!baseElement) {\n      return null;\n    }\n  }\n  return baseElement.getAttribute('href');\n}\n\n// based on urlUtils.js in AngularJS 1\nlet /** @type {?} */ urlParsingNode: any;\n/**\n * @param {?} url\n * @return {?}\n */\nfunction relativePath(url: any): string {\n  if (!urlParsingNode) {\n    urlParsingNode = document.createElement('a');\n  }\n  urlParsingNode.setAttribute('href', url);\n  return (urlParsingNode.pathname.charAt(0) === '/') ? urlParsingNode.pathname :\n                                                       '/' + urlParsingNode.pathname;\n}\n/**\n * @param {?} cookieStr\n * @param {?} name\n * @return {?}\n */\nexport function parseCookieValue(cookieStr: string, name: string): string|null {\n  name = encodeURIComponent(name);\n  for (const /** @type {?} */ cookie of cookieStr.split(';')) {\n    const /** @type {?} */ eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n        eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {DomAdapter} from '../dom/dom_adapter';\n/**\n * Provides DOM operations in any browser environment.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class GenericBrowserDomAdapter extends DomAdapter {\nprivate _animationPrefix: string|null = null;\nprivate _transitionEnd: string|null = null;\nconstructor() {\n    super();\n    try {\n      const element = this.createElement('div', document);\n      if (this.getStyle(element, 'animationName') != null) {\n        this._animationPrefix = '';\n      } else {\n        const domPrefixes = ['Webkit', 'Moz', 'O', 'ms'];\n\n        for (let i = 0; i < domPrefixes.length; i++) {\n          if (this.getStyle(element, domPrefixes[i] + 'AnimationName') != null) {\n            this._animationPrefix = '-' + domPrefixes[i].toLowerCase() + '-';\n            break;\n          }\n        }\n      }\n\n      const transEndEventNames: {[key: string]: string} = {\n        WebkitTransition: 'webkitTransitionEnd',\n        MozTransition: 'transitionend',\n        OTransition: 'oTransitionEnd otransitionend',\n        transition: 'transitionend'\n      };\n\n      Object.keys(transEndEventNames).forEach((key: string) => {\n        if (this.getStyle(element, key) != null) {\n          this._transitionEnd = transEndEventNames[key];\n        }\n      });\n    } catch (e) {\n      this._animationPrefix = null;\n      this._transitionEnd = null;\n    }\n  }\n/**\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: HTMLElement): Node[] { return ( /** @type {?} */((<any>el))).getDistributedNodes(); }\n/**\n * @param {?} el\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(el: HTMLAnchorElement, baseUrl: string, href: string) {\n    el.href = href == null ? baseUrl : baseUrl + '/../' + href;\n  }\n/**\n * @return {?}\n */\nsupportsDOMEvents(): boolean { return true; }\n/**\n * @return {?}\n */\nsupportsNativeShadowDOM(): boolean {\n    return typeof( /** @type {?} */((<any>document.body))).createShadowRoot === 'function';\n  }\n/**\n * @return {?}\n */\ngetAnimationPrefix(): string { return this._animationPrefix ? this._animationPrefix : ''; }\n/**\n * @return {?}\n */\ngetTransitionEnd(): string { return this._transitionEnd ? this._transitionEnd : ''; }\n/**\n * @return {?}\n */\nsupportsAnimation(): boolean {\n    return this._animationPrefix != null && this._transitionEnd != null;\n  }\n}\n\nfunction GenericBrowserDomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._animationPrefix;\n/** @type {?} */\nGenericBrowserDomAdapter.prototype._transitionEnd;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\n\nlet /** @type {?} */ _DOM: DomAdapter = /** @type {?} */(( null));\n/**\n * @return {?}\n */\nexport function getDOM() {\n  return _DOM;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setDOM(adapter: DomAdapter) {\n  _DOM = adapter;\n}\n/**\n * @param {?} adapter\n * @return {?}\n */\nexport function setRootDomAdapter(adapter: DomAdapter) {\n  if (!_DOM) {\n    _DOM = adapter;\n  }\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n * \n * \\@security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n * @abstract\n */\nexport abstract class DomAdapter {\npublic resourceLoaderType: Type<any> = /** @type {?} */(( null));\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\nhasProperty(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: Element, name: string, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\ngetProperty(el: Element, name: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} methodName\n * @param {?} args\n * @return {?}\n */\ninvoke(el: Element, methodName: string, args: any[]) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogError(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlog(error: any) {}\n/**\n * @abstract\n * @param {?} error\n * @return {?}\n */\nlogGroup(error: any) {}\n/**\n * @abstract\n * @return {?}\n */\nlogGroupEnd() {}\n/**\n * Maps attribute names to their corresponding property names for cases\n * where attribute name doesn't match property name.\n * @return {?}\n */\nget attrToPropMap(): {[key: string]: string} { return this._attrToPropMap; };\n/**\n * @param {?} value\n * @return {?}\n */\nset attrToPropMap(value: {[key: string]: string}) { this._attrToPropMap = value; };\n/**\n * \\@internal\n */\n_attrToPropMap: {[key: string]: string};\n/**\n * @abstract\n * @param {?} nodeA\n * @param {?} nodeB\n * @return {?}\n */\ncontains(nodeA: any, nodeB: any) {}\n/**\n * @abstract\n * @param {?} templateHtml\n * @return {?}\n */\nparse(templateHtml: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelector(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} selector\n * @return {?}\n */\nquerySelectorAll(el: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\non(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @param {?} listener\n * @return {?}\n */\nonAndCancel(el: any, evt: any, listener: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} evt\n * @return {?}\n */\ndispatchEvent(el: any, evt: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateMouseEvent(eventType: any) {}\n/**\n * @abstract\n * @param {?} eventType\n * @return {?}\n */\ncreateEvent(eventType: string) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\npreventDefault(evt: any) {}\n/**\n * @abstract\n * @param {?} evt\n * @return {?}\n */\nisPrevented(evt: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetInnerHTML(el: any) {}\n/**\n * Returns content if el is a <template> element, null otherwise.\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetTemplateContent(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetOuterHTML(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeName(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nnodeValue(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ntype(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\ncontent(node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nfirstChild(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nnextSibling(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nparentElement(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nchildNodesAsList(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nclearNodes(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nappendChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\nremoveChild(el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} newNode\n * @param {?} oldNode\n * @return {?}\n */\nreplaceChild(el: any, newNode: any, oldNode: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nremove(el: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} node\n * @return {?}\n */\ninsertBefore(parent: any, ref: any, node: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} ref\n * @param {?} nodes\n * @return {?}\n */\ninsertAllBefore(parent: any, ref: any, nodes: any) {}\n/**\n * @abstract\n * @param {?} parent\n * @param {?} el\n * @param {?} node\n * @return {?}\n */\ninsertAfter(parent: any, el: any, node: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetInnerHTML(el: any, value: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetText(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetText(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetValue(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetValue(el: any, value: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetChecked(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @param {?} value\n * @return {?}\n */\nsetChecked(el: any, value: boolean) {}\n/**\n * @abstract\n * @param {?} text\n * @return {?}\n */\ncreateComment(text: string) {}\n/**\n * @abstract\n * @param {?} html\n * @return {?}\n */\ncreateTemplate(html: any) {}\n/**\n * @abstract\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElement(tagName: any, doc?: any) {}\n/**\n * @abstract\n * @param {?} ns\n * @param {?} tagName\n * @param {?=} doc\n * @return {?}\n */\ncreateElementNS(ns: string, tagName: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} text\n * @param {?=} doc\n * @return {?}\n */\ncreateTextNode(text: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} attrName\n * @param {?} attrValue\n * @param {?=} doc\n * @return {?}\n */\ncreateScriptTag(attrName: string, attrValue: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} css\n * @param {?=} doc\n * @return {?}\n */\ncreateStyleElement(css: string, doc?: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ncreateShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetShadowRoot(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetHost(el: any) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetDistributedNodes(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nclone /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByClassName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetElementsByTagName(element: any, name: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nclassList(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\naddClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nremoveClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nhasClass(element: any, className: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?} styleValue\n * @return {?}\n */\nsetStyle(element: any, styleName: string, styleValue: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\nremoveStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @return {?}\n */\ngetStyle(element: any, styleName: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} styleName\n * @param {?=} styleValue\n * @return {?}\n */\nhasStyle(element: any, styleName: string, styleValue?: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ntagName(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\nattributeMap(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nhasAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nhasAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\ngetAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\ngetAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttribute(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetAttributeNS(element: any, ns: string, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} attribute\n * @return {?}\n */\nremoveAttribute(element: any, attribute: string) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} ns\n * @param {?} attribute\n * @return {?}\n */\nremoveAttributeNS(element: any, ns: string, attribute: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ntemplateAwareRoot(el: any) {}\n/**\n * @abstract\n * @return {?}\n */\ncreateHtmlDocument() {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\ngetBoundingClientRect(el: any) {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetTitle(doc: Document) {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} newTitle\n * @return {?}\n */\nsetTitle(doc: Document, newTitle: string) {}\n/**\n * @abstract\n * @param {?} n\n * @param {?} selector\n * @return {?}\n */\nelementMatches(n: any, selector: string) {}\n/**\n * @abstract\n * @param {?} el\n * @return {?}\n */\nisTemplateElement(el: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisTextNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisCommentNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisElementNode(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nhasShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nisShadowRoot(node: any) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nimportIntoDoc /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} node\n * @return {?}\n */\nadoptNode /*<T extends Node>*/ (node: Node) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetHref(element: any) {}\n/**\n * @abstract\n * @param {?} event\n * @return {?}\n */\ngetEventKey(event: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} baseUrl\n * @param {?} href\n * @return {?}\n */\nresolveAndSetHref(element: any, baseUrl: string, href: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsDOMEvents() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsNativeShadowDOM() {}\n/**\n * @abstract\n * @param {?} doc\n * @param {?} target\n * @return {?}\n */\ngetGlobalEventTarget(doc: Document, target: string) {}\n/**\n * @abstract\n * @return {?}\n */\ngetHistory() {}\n/**\n * @abstract\n * @return {?}\n */\ngetLocation() {}\n/**\n * @abstract\n * @param {?} doc\n * @return {?}\n */\ngetBaseHref(doc: Document) {}\n/**\n * @abstract\n * @return {?}\n */\nresetBaseElement() {}\n/**\n * @abstract\n * @return {?}\n */\ngetUserAgent() {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetData(element: any, name: string, value: string) {}\n/**\n * @abstract\n * @param {?} element\n * @return {?}\n */\ngetComputedStyle(element: any) {}\n/**\n * @abstract\n * @param {?} element\n * @param {?} name\n * @return {?}\n */\ngetData(element: any, name: string) {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsWebAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nperformanceNow() {}\n/**\n * @abstract\n * @return {?}\n */\ngetAnimationPrefix() {}\n/**\n * @abstract\n * @return {?}\n */\ngetTransitionEnd() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsAnimation() {}\n/**\n * @abstract\n * @return {?}\n */\nsupportsCookies() {}\n/**\n * @abstract\n * @param {?} name\n * @return {?}\n */\ngetCookie(name: string) {}\n/**\n * @abstract\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetCookie(name: string, value: string) {}\n}\n\nfunction DomAdapter_tsickle_Closure_declarations() {\n/** @type {?} */\nDomAdapter.prototype.resourceLoaderType;\n/**\n * \\@internal\n * @type {?}\n */\nDomAdapter.prototype._attrToPropMap;\n}\n\n"], "names": ["window", "PLATFORM_BROWSER_ID", "<PERSON>.<PERSON><PERSON><PERSON>", "core.Optional", "NgProbeToken", "core.APP_INITIALIZER", "core.getDebugNode", "core.Ng<PERSON>one", "core.ApplicationRef", "global"], "mappings": ";;;;A+BAA;;;;;;;AAWA,IADI,IAAA,KAAmB,IAAA,EAAA,CAAO;;;;AAK9B,AAAA,SAAA,MAAA,GAAA;IACE,OAHO,IAAA,CAAK;CAIb;;;;;AAKD,AAAA,AAEC;;;;;AAKD,AAAA,SAAA,iBAAA,CATC,OAAA,EASD;IACE,IAAI,CATC,IAAC,EAAK;QAUT,IAAI,GATG,OAAA,CAAQ;KAUhB;CACF;;;;;;;;AAQD,AAAA,MAAA,UAAA,CAAA;IAAA,WAAA,GAAA;QAPS,IAAT,CAAA,kBAAS,KAAgC,IAAA,EAAA,CAAO;KA4vB/C;;;;;;;IA7uBD,WAdY,CAAA,OAAA,EAAA,IAAA,EAcZ,GAdY;;;;;;;;IAsBZ,WArBY,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAqBZ,GArBY;;;;;;;IA4BZ,WA3BY,CAAA,EAAA,EAAA,IAAA,EA2BZ,GA3BY;;;;;;;;IAmCZ,MAlCY,CAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAkCZ,GAlCY;;;;;;IAwCZ,QAtCY,CAAA,KAAA,EAsCZ,GAtCY;;;;;;IA4CZ,GA3CY,CAAA,KAAA,EA2CZ,GA3CY;;;;;;IAiDZ,QAhDY,CAAA,KAAA,EAgDZ,GAhDY;;;;;IAqDZ,WApDY,GAoDZ,GApDY;;;;;;IA0DZ,IApDG,aAAA,GAoDH,EApDiD,OAAO,IAAA,CAAK,cAAC,CAAc,EAAC;;;;;;IAyD7E,IAxDG,aAAA,CAAA,KAAA,EAwDH,EAxDsD,IAAA,CAAK,cAAC,GAAgB,KAAA,CAAM,EAAC;;;;;;;;IAmEnF,QA/DY,CAAA,KAAA,EAAA,KAAA,EA+DZ,GA/DY;;;;;;IAqEZ,KApEY,CAAA,YAAA,EAoEZ,GApEY;;;;;;;IA2EZ,aA1EY,CAAA,EAAA,EAAA,QAAA,EA0EZ,GA1EY;;;;;;;IAiFZ,gBAhFY,CAAA,EAAA,EAAA,QAAA,EAgFZ,GAhFY;;;;;;;;IAwFZ,EAvFY,CAAA,EAAA,EAAA,GAAA,EAAA,QAAA,EAuFZ,GAvFY;;;;;;;;IA+FZ,WA9FY,CAAA,EAAA,EAAA,GAAA,EAAA,QAAA,EA8FZ,GA9FY;;;;;;;IAqGZ,aApGY,CAAA,EAAA,EAAA,GAAA,EAoGZ,GApGY;;;;;;IA0GZ,gBAzGY,CAAA,SAAA,EAyGZ,GAzGY;;;;;;IA+GZ,WA9GY,CAAA,SAAA,EA8GZ,GA9GY;;;;;;IAoHZ,cAnHY,CAAA,GAAA,EAmHZ,GAnHY;;;;;;IAyHZ,WAxHY,CAAA,GAAA,EAwHZ,GAxHY;;;;;;IA8HZ,YA7HY,CAAA,EAAA,EA6HZ,GA7HY;;;;;;;IAoIZ,kBAlIY,CAAA,EAAA,EAkIZ,GAlIY;;;;;;IAwIZ,YAvIY,CAAA,EAAA,EAuIZ,GAvIY;;;;;;IA6IZ,QA5IY,CAAA,IAAA,EA4IZ,GA5IY;;;;;;IAkJZ,SAjJY,CAAA,IAAA,EAiJZ,GAjJY;;;;;;IAuJZ,IAtJY,CAAA,IAAA,EAsJZ,GAtJY;;;;;;IA4JZ,OA3JY,CAAA,IAAA,EA2JZ,GA3JY;;;;;;IAiKZ,UAhKY,CAAA,EAAA,EAgKZ,GAhKY;;;;;;IAsKZ,WArKY,CAAA,EAAA,EAqKZ,GArKY;;;;;;IA2KZ,aA1KY,CAAA,EAAA,EA0KZ,GA1KY;;;;;;IAgLZ,UA/KY,CAAA,EAAA,EA+KZ,GA/KY;;;;;;IAqLZ,gBApLY,CAAA,EAAA,EAoLZ,GApLY;;;;;;IA0LZ,UAzLY,CAAA,EAAA,EAyLZ,GAzLY;;;;;;;IAgMZ,WA/LY,CAAA,EAAA,EAAA,IAAA,EA+LZ,GA/LY;;;;;;;IAsMZ,WArMY,CAAA,EAAA,EAAA,IAAA,EAqMZ,GArMY;;;;;;;;IA6MZ,YA5MY,CAAA,EAAA,EAAA,OAAA,EAAA,OAAA,EA4MZ,GA5MY;;;;;;IAkNZ,MAjNY,CAAA,EAAA,EAiNZ,GAjNY;;;;;;;;IAyNZ,YAxNY,CAAA,MAAA,EAAA,GAAA,EAAA,IAAA,EAwNZ,GAxNY;;;;;;;;IAgOZ,eA/NY,CAAA,MAAA,EAAA,GAAA,EAAA,KAAA,EA+NZ,GA/NY;;;;;;;;IAuOZ,WAtOY,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,EAsOZ,GAtOY;;;;;;;IA6OZ,YA5OY,CAAA,EAAA,EAAA,KAAA,EA4OZ,GA5OY;;;;;;IAkPZ,OAjPY,CAAA,EAAA,EAiPZ,GAjPY;;;;;;;IAwPZ,OAvPY,CAAA,EAAA,EAAA,KAAA,EAuPZ,GAvPY;;;;;;IA6PZ,QA5PY,CAAA,EAAA,EA4PZ,GA5PY;;;;;;;IAmQZ,QAlQY,CAAA,EAAA,EAAA,KAAA,EAkQZ,GAlQY;;;;;;IAwQZ,UAvQY,CAAA,EAAA,EAuQZ,GAvQY;;;;;;;IA8QZ,UA7QY,CAAA,EAAA,EAAA,KAAA,EA6QZ,GA7QY;;;;;;IAmRZ,aAlRY,CAAA,IAAA,EAkRZ,GAlRY;;;;;;IAwRZ,cAvRY,CAAA,IAAA,EAuRZ,GAvRY;;;;;;;IA8RZ,aA7RY,CAAA,OAAA,EAAA,GAAA,EA6RZ,GA7RY;;;;;;;;IAqSZ,eApSY,CAAA,EAAA,EAAA,OAAA,EAAA,GAAA,EAoSZ,GApSY;;;;;;;IA2SZ,cA1SY,CAAA,IAAA,EAAA,GAAA,EA0SZ,GA1SY;;;;;;;;IAkTZ,eAjTY,CAAA,QAAA,EAAA,SAAA,EAAA,GAAA,EAiTZ,GAjTY;;;;;;;IAwTZ,kBAvTY,CAAA,GAAA,EAAA,GAAA,EAuTZ,GAvTY;;;;;;IA6TZ,gBA5TY,CAAA,EAAA,EA4TZ,GA5TY;;;;;;IAkUZ,aAjUY,CAAA,EAAA,EAiUZ,GAjUY;;;;;;IAuUZ,OAtUY,CAAA,EAAA,EAsUZ,GAtUY;;;;;;IA4UZ,mBA3UY,CAAA,EAAA,EA2UZ,GA3UY;;;;;;IAiVZ,KAhVY,qBAAA,CAAA,IAAA,EAgVZ,GAhVY;;;;;;;IAuVZ,sBAtVY,CAAA,OAAA,EAAA,IAAA,EAsVZ,GAtVY;;;;;;;IA6VZ,oBA5VY,CAAA,OAAA,EAAA,IAAA,EA4VZ,GA5VY;;;;;;IAkWZ,SAjWY,CAAA,OAAA,EAiWZ,GAjWY;;;;;;;IAwWZ,QAvWY,CAAA,OAAA,EAAA,SAAA,EAuWZ,GAvWY;;;;;;;IA8WZ,WA7WY,CAAA,OAAA,EAAA,SAAA,EA6WZ,GA7WY;;;;;;;IAoXZ,QAnXY,CAAA,OAAA,EAAA,SAAA,EAmXZ,GAnXY;;;;;;;;IA2XZ,QA1XY,CAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EA0XZ,GA1XY;;;;;;;IAiYZ,WAhYY,CAAA,OAAA,EAAA,SAAA,EAgYZ,GAhYY;;;;;;;IAuYZ,QAtYY,CAAA,OAAA,EAAA,SAAA,EAsYZ,GAtYY;;;;;;;;IA8YZ,QA7YY,CAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EA6YZ,GA7YY;;;;;;IAmZZ,OAlZY,CAAA,OAAA,EAkZZ,GAlZY;;;;;;IAwZZ,YAvZY,CAAA,OAAA,EAuZZ,GAvZY;;;;;;;IA8ZZ,YA7ZY,CAAA,OAAA,EAAA,SAAA,EA6ZZ,GA7ZY;;;;;;;;IAqaZ,cApaY,CAAA,OAAA,EAAA,EAAA,EAAA,SAAA,EAoaZ,GApaY;;;;;;;IA2aZ,YA1aY,CAAA,OAAA,EAAA,SAAA,EA0aZ,GA1aY;;;;;;;;IAkbZ,cAjbY,CAAA,OAAA,EAAA,EAAA,EAAA,SAAA,EAibZ,GAjbY;;;;;;;;IAybZ,YAxbY,CAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAwbZ,GAxbY;;;;;;;;;IAicZ,cAhcY,CAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAgcZ,GAhcY;;;;;;;IAucZ,eAtcY,CAAA,OAAA,EAAA,SAAA,EAscZ,GAtcY;;;;;;;;IA8cZ,iBA7cY,CAAA,OAAA,EAAA,EAAA,EAAA,SAAA,EA6cZ,GA7cY;;;;;;IAmdZ,iBAldY,CAAA,EAAA,EAkdZ,GAldY;;;;;IAudZ,kBAtdY,GAsdZ,GAtdY;;;;;;IA4dZ,qBA3dY,CAAA,EAAA,EA2dZ,GA3dY;;;;;;IAieZ,QAheY,CAAA,GAAA,EAgeZ,GAheY;;;;;;;IAueZ,QAteY,CAAA,GAAA,EAAA,QAAA,EAseZ,GAteY;;;;;;;IA6eZ,cA5eY,CAAA,CAAA,EAAA,QAAA,EA4eZ,GA5eY;;;;;;IAkfZ,iBAjfY,CAAA,EAAA,EAifZ,GAjfY;;;;;;IAufZ,UAtfY,CAAA,IAAA,EAsfZ,GAtfY;;;;;;IA4fZ,aA3fY,CAAA,IAAA,EA2fZ,GA3fY;;;;;;IAigBZ,aAhgBY,CAAA,IAAA,EAggBZ,GAhgBY;;;;;;IAsgBZ,aArgBY,CAAA,IAAA,EAqgBZ,GArgBY;;;;;;IA2gBZ,YA1gBY,CAAA,IAAA,EA0gBZ,GA1gBY;;;;;;IAghBZ,aA/gBY,qBAAA,CAAA,IAAA,EA+gBZ,GA/gBY;;;;;;IAqhBZ,SAphBY,qBAAA,CAAA,IAAA,EAohBZ,GAphBY;;;;;;IA0hBZ,OAzhBY,CAAA,OAAA,EAyhBZ,GAzhBY;;;;;;IA+hBZ,WA9hBY,CAAA,KAAA,EA8hBZ,GA9hBY;;;;;;;;IAsiBZ,iBAriBY,CAAA,OAAA,EAAA,OAAA,EAAA,IAAA,EAqiBZ,GAriBY;;;;;IA0iBZ,iBAziBY,GAyiBZ,GAziBY;;;;;IA8iBZ,uBA7iBY,GA6iBZ,GA7iBY;;;;;;;IAojBZ,oBAnjBY,CAAA,GAAA,EAAA,MAAA,EAmjBZ,GAnjBY;;;;;IAwjBZ,UAvjBY,GAujBZ,GAvjBY;;;;;IA4jBZ,WA3jBY,GA2jBZ,GA3jBY;;;;;;IAikBZ,WAhkBY,CAAA,GAAA,EAgkBZ,GAhkBY;;;;;IAqkBZ,gBApkBY,GAokBZ,GApkBY;;;;;IAykBZ,YAxkBY,GAwkBZ,GAxkBY;;;;;;;;IAglBZ,OA/kBY,CAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EA+kBZ,GA/kBY;;;;;;IAqlBZ,gBAplBY,CAAA,OAAA,EAolBZ,GAplBY;;;;;;;IA2lBZ,OA1lBY,CAAA,OAAA,EAAA,IAAA,EA0lBZ,GA1lBY;;;;;IA+lBZ,oBA9lBY,GA8lBZ,GA9lBY;;;;;IAmmBZ,cAlmBY,GAkmBZ,GAlmBY;;;;;IAumBZ,kBAtmBY,GAsmBZ,GAtmBY;;;;;IA2mBZ,gBA1mBY,GA0mBZ,GA1mBY;;;;;IA+mBZ,iBA9mBY,GA8mBZ,GA9mBY;;;;;IAmnBZ,eAjnBY,GAinBZ,GAjnBY;;;;;;IAunBZ,SAtnBY,CAAA,IAAA,EAsnBZ,GAtnBY;;;;;;;IA6nBZ,SA5nBY,CAAA,IAAA,EAAA,KAAA,EA4nBZ,GA5nBY;CA6nBX,AAED,AAQC;;ADxyBD;;;;;;;AASA,AACA;;;;;;;AAOA,AAAA,MAAA,wBACC,SAAA,UAAA,CADD;IAGA,WAAA,GAAA;QACI,KAAK,EAAE,CAAC;QAFF,IAAV,CAAA,gBAAU,GAAgC,IAAA,CAAK;QACrC,IAAV,CAAA,cAAU,GAA8B,IAAA,CAAK;QAEzC,IAAI;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,IAAI,EAAE;gBACnD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC5B;iBAAM;gBACL,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;gBAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,IAAI,IAAI,EAAE;wBACpE,IAAI,CAAC,gBAAgB,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC;wBACjE,MAAM;qBACP;iBACF;aACF;YAED,MAAM,kBAAkB,GAA4B;gBAClD,gBAAgB,EAAE,qBAAqB;gBACvC,aAAa,EAAE,eAAe;gBAC9B,WAAW,EAAE,+BAA+B;gBAC5C,UAAU,EAAE,eAAe;aAC5B,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,GAAW,KAA1D;gBACQ,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE;oBACvC,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;iBAC/C;aACF,CAAC,CAAC;SACJ;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;KACF;;;;;IAKH,mBAFG,CAAA,EAAA,EAEH,EAFiD,OAAO,EAAM,EAAC,GAAG,mBAAC,EAAmB,CAAE,EAAC;;;;;;;IASzF,iBARG,CAAA,EAAA,EAAA,OAAA,EAAA,IAAA,EAQH;QACI,EAAE,CARC,IAAC,GAAM,IAAA,IAAQ,IAAA,GAAO,OAAA,GAAU,OAAA,GAAU,MAAA,GAAS,IAAA,CAAK;KAS5D;;;;IAIH,iBAXG,GAWH,EAXiC,OAAO,IAAA,CAAK,EAAC;;;;IAe9C,uBAdG,GAcH;QACI,OAdO,OAAA,EAAY,QAAC,CAAQ,IAAC,GAAK,gBAAC,KAAoB,UAAA,CAAW;KAenE;;;;IAIH,kBAjBG,GAiBH,EAjBiC,OAAO,IAAA,CAAK,gBAAC,GAAkB,IAAA,CAAK,gBAAC,GAAkB,EAAA,CAAG,EAAC;;;;IAqB5F,gBApBG,GAoBH,EApB+B,OAAO,IAAA,CAAK,cAAC,GAAgB,IAAA,CAAK,cAAC,GAAgB,EAAA,CAAG,EAAC;;;;IAwBtF,iBAvBG,GAuBH;QACI,OAvBO,IAAA,CAAK,gBAAC,IAAmB,IAAA,IAAQ,IAAA,CAAK,cAAC,IAAiB,IAAA,CAAK;KAwBrE;CACF,AAED,AAKC;;ADnGD;;;;;;;AASA,AACA,AAEA,AAEA,MADM,cAAA,GAAiB;IAErB,OAAO,EADE,WAAA;IAET,WAAW,EADE,WAAA;IAEb,UAAU,EADE,UAAA;IAEZ,UAAU,EADE,UAAA;CAEb,CADC;AAGF,MADM,uBAAA,GAA0B,CAAA,CAAE;;AAIlC,MADM,OAAA,GAAiC;;;IAIrC,IAAI,EADE,WAAA;IAEN,IAAI,EADE,KAAA;IAEN,MAAM,EADE,QAAA;IAER,MAAM,EADE,QAAA;IAER,KAAK,EADE,QAAA;IAEP,KAAK,EADE,QAAA;IAEP,MAAM,EADE,WAAA;IAER,OAAO,EADE,YAAA;IAET,IAAI,EADE,SAAA;IAEN,MAAM,EADE,WAAA;IAER,MAAM,EADE,aAAA;IAER,QAAQ,EADE,YAAA;IAEV,KAAK,EADE,IAAA;CAER,CADC;;;;AAMF,MADM,mBAAA,GAAsB;IAE1B,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,GAAG,EADE,GAAA;IAEL,MAAM,EADE,GAAA;IAER,MAAM,EADE,SAAA;CAET,CADC;AAGF,IADI,YAAkC,CAAQ;AAG9C,IAAIS,OADC,CAAM,MAAC,CAAM,EAAE;IAElB,YAAY,GADGA,OAAA,CAAO,MAAC,CAAM,CAAC,SAAC,CAAS,QAAC,IAAW,UAAA,IAAU,EAChE;QACI,OADO,CAAA,EAAG,IAAC,CAAI,uBAAC,CAAuB,IAAC,CAAI,GAAG,EAAA,CAAG,CAAC;KAEpD,CADC;CAEH;AACD,AAAA,MAAA,iBAOC,SAAA,wBAAA,CAPD;;;;;IAKA,KAGG,CAAA,YAAA,EAHH,EAGgC,MAAM,IAAI,KAAA,CAAM,uBAAC,CAAuB,CAAC,EAAC;;;;IAC1E,OAAG,WAAA,GAAH,EAAyB,iBAAA,CAAkB,IAAI,iBAAA,EAAkB,CAAE,CAAC,EAAC;;;;;;IAMrE,WALG,CAAA,OAAA,EAAA,IAAA,EAKH,EALsD,OAAO,IAAA,IAAQ,OAAA,CAAQ,EAAC;;;;;;;IAY9E,WAXG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAWH,EAXoD,EAAM,EAAC,GAAG,IAAC,CAAI,GAAG,KAAA,CAAM,EAAC;;;;;;IAiB7E,WAhBG,CAAA,EAAA,EAAA,IAAA,EAgBH,EAhB6C,OAAO,EAAM,EAAC,GAAG,IAAC,CAAI,CAAC,EAAC;;;;;;;IAuBrE,MAtBG,CAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAsBH,EAtB2D,EAAM,EAAC,GAAG,UAAC,CAAU,CAAC,GAAC,IAAG,CAAI,CAAC,EAAC;;;;;IA2B3F,QAxBG,CAAA,KAAA,EAwBH;QACI,IAAI,MAxBC,CAAM,OAAC,EAAQ;YAyBlB,IAAI,OAxBC,CAAO,KAAC,EAAM;gBAyBjB,OAAO,CAxBC,KAAC,CAAK,KAAC,CAAK,CAAC;aAyBtB;iBAxBM;gBAyBL,OAAO,CAxBC,GAAC,CAAG,KAAC,CAAK,CAAC;aAyBpB;SACF;KACF;;;;;IAKH,GA3BG,CAAA,KAAA,EA2BH;QACI,IAAI,MA3BC,CAAM,OAAC,EAAQ;YA4BlB,MAAM,CA3BC,OAAC,CAAO,GAAC,IAAM,MAAA,CAAO,OAAC,CAAO,GAAC,CAAG,KAAC,CAAK,CAAC;SA4BjD;KACF;;;;;IAKH,QA9BG,CAAA,KAAA,EA8BH;QACI,IAAI,MA9BC,CAAM,OAAC,EAAQ;YA+BlB,MAAM,CA9BC,OAAC,CAAO,KAAC,IAAQ,MAAA,CAAO,OAAC,CAAO,KAAC,CAAK,KAAC,CAAK,CAAC;SA+BrD;KACF;;;;IAIH,WAhCG,GAgCH;QACI,IAAI,MAhCC,CAAM,OAAC,EAAQ;YAiClB,MAAM,CAhCC,OAAC,CAAO,QAAC,IAAW,MAAA,CAAO,OAAC,CAAO,QAAC,EAAQ,CAAE;SAiCtD;KACF;;;;IAIH,IAlCG,aAAA,GAkCH,EAlC6B,OAAO,cAAA,CAAe,EAAC;;;;;;IAwCpD,QAtCG,CAAA,KAAA,EAAA,KAAA,EAsCH,EAtC8C,OAAO,YAAA,CAAa,IAAC,CAAI,KAAC,EAAM,KAAA,CAAM,CAAC,EAAC;;;;;;IA4CtF,aA3CG,CAAA,EAAA,EAAA,QAAA,EA2CH,EA3CsD,OAAO,EAAA,CAAG,aAAC,CAAa,QAAC,CAAQ,CAAC,EAAC;;;;;;IAiDzF,gBAhDG,CAAA,EAAA,EAAA,QAAA,EAgDH,EAhDuD,OAAO,EAAA,CAAG,gBAAC,CAAgB,QAAC,CAAQ,CAAC,EAAC;;;;;;;IAuD7F,EAtDG,CAAA,EAAA,EAAA,GAAA,EAAA,QAAA,EAsDH,EAtD0C,EAAA,CAAG,gBAAC,CAAgB,GAAC,EAAI,QAAA,EAAU,KAAA,CAAM,CAAC,EAAC;;;;;;;IA6DrF,WA5DG,CAAA,EAAA,EAAA,GAAA,EAAA,QAAA,EA4DH;QACI,EAAE,CA5DC,gBAAC,CAAgB,GAAC,EAAI,QAAA,EAAU,KAAA,CAAM,CAAC;;;QA+D1C,OA5DO,MA4DX,EA5DmB,EAAA,CAAG,mBAAC,CAAmB,GAAC,EAAI,QAAA,EAAU,KAAA,CAAM,CAAC,EAAC,CAAE;KA6DhE;;;;;;IAMH,aAjEG,CAAA,EAAA,EAAA,GAAA,EAiEH,EAjEsC,EAAA,CAAG,aAAC,CAAa,GAAC,CAAG,CAAC,EAAC;;;;;IAsE7D,gBArEG,CAAA,SAAA,EAqEH;QACI,uBArEM,GAAA,GAAkB,QAAA,CAAS,WAAC,CAAW,YAAC,CAAY,CAAC;QAsE3D,GAAG,CArEC,SAAC,CAAS,SAAC,EAAU,IAAA,EAAM,IAAA,CAAK,CAAC;QAsErC,OArEO,GAAA,CAAI;KAsEZ;;;;;IAKH,WAzEG,CAAA,SAAA,EAyEH;QACI,uBAzEM,GAAA,GAAa,QAAA,CAAS,WAAC,CAAW,OAAC,CAAO,CAAC;QA0EjD,GAAG,CAzEC,SAAC,CAAS,SAAC,EAAU,IAAA,EAAM,IAAA,CAAK,CAAC;QA0ErC,OAzEO,GAAA,CAAI;KA0EZ;;;;;IAKH,cA7EG,CAAA,GAAA,EA6EH;QACI,GAAG,CA7EC,cAAC,EAAc,CAAE;QA8ErB,GAAG,CA7EC,WAAC,GAAa,KAAA,CAAM;KA8EzB;;;;;IAKH,WAjFG,CAAA,GAAA,EAiFH;QACI,OAjFO,GAAA,CAAI,gBAAC,IAAmB,GAAA,CAAI,WAAC,IAAc,IAAA,IAAQ,CAAA,GAAE,CAAG,WAAC,CAAW;KAkF5E;;;;;IAKH,YArFG,CAAA,EAAA,EAqFH,EArF0C,OAAO,EAAA,CAAG,SAAC,CAAS,EAAC;;;;;IA0F/D,kBAzFG,CAAA,EAAA,EAyFH;QACI,OAzFO,SAAA,IAAa,EAAA,IAAM,EAAA,YAAc,mBAAA,GAAsB,EAAA,CAAG,OAAC,GAAS,IAAA,CAAK;KA0FjF;;;;;IAKH,YA7FG,CAAA,EAAA,EA6FH,EA7F0C,OAAO,EAAA,CAAG,SAAC,CAAS,EAAC;;;;;IAkG/D,QAjGG,CAAA,IAAA,EAiGH,EAjGiC,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;;IAsGvD,SArGG,CAAA,IAAA,EAqGH,EArGuC,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;IA0G9D,IAzGG,CAAA,IAAA,EAyGH,EAzGyC,OAAO,IAAA,CAAK,IAAC,CAAI,EAAC;;;;;IA8G3D,OA7GG,CAAA,IAAA,EA6GH;QACI,IAAI,IA7GC,CAAI,WAAC,CAAW,IAAC,EAAK,SAAA,CAAU,EAAE;YA8GrC,OA7GO,EAAM,IAAC,GAAK,OAAC,CAAO;SA8G5B;aA7GM;YA8GL,OA7GO,IAAA,CAAK;SA8Gb;KACF;;;;;IAKH,UAjHG,CAAA,EAAA,EAiHH,EAjHoC,OAAO,EAAA,CAAG,UAAC,CAAU,EAAC;;;;;IAsH1D,WArHG,CAAA,EAAA,EAqHH,EArHqC,OAAO,EAAA,CAAG,WAAC,CAAW,EAAC;;;;;IA0H5D,aAzHG,CAAA,EAAA,EAyHH,EAzHuC,OAAO,EAAA,CAAG,UAAC,CAAU,EAAC;;;;;IA8H7D,UA7HG,CAAA,EAAA,EA6HH,EA7HgC,OAAO,EAAA,CAAG,UAAC,CAAU,EAAC;;;;;IAkItD,gBAjIG,CAAA,EAAA,EAiIH;QACI,uBAjIM,UAAA,GAAa,EAAA,CAAG,UAAC,CAAU;QAkIjC,uBAjIM,GAAA,GAAM,IAAI,KAAA,CAAM,UAAC,CAAU,MAAC,CAAM,CAAC;QAkIzC,KAAK,qBAjII,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,UAAA,CAAW,MAAC,EAAO,CAAA,EAAE,EAAG;YAkI1C,GAAG,CAjIC,CAAC,CAAC,GAAG,UAAA,CAAW,CAAC,CAAC,CAAC;SAkIxB;QACD,OAjIO,GAAA,CAAI;KAkIZ;;;;;IAKH,UArIG,CAAA,EAAA,EAqIH;QACI,OAAO,EArIC,CAAE,UAAC,EAAW;YAsIpB,EAAE,CArIC,WAAC,CAAW,EAAC,CAAE,UAAC,CAAU,CAAC;SAsI/B;KACF;;;;;;IAMH,WA1IG,CAAA,EAAA,EAAA,IAAA,EA0IH,EA1IsC,EAAA,CAAG,WAAC,CAAW,IAAC,CAAI,CAAC,EAAC;;;;;;IAgJ5D,WA/IG,CAAA,EAAA,EAAA,IAAA,EA+IH,EA/IsC,EAAA,CAAG,WAAC,CAAW,IAAC,CAAI,CAAC,EAAC;;;;;;;IAsJ5D,YArJG,CAAA,EAAA,EAAA,QAAA,EAAA,QAAA,EAqJH,EArJ2D,EAAA,CAAG,YAAC,CAAY,QAAC,EAAS,QAAA,CAAS,CAAC,EAAC;;;;;IA0JhG,MAzJG,CAAA,IAAA,EAyJH;QACI,IAAI,IAzJC,CAAI,UAAC,EAAW;YA0JnB,IAAI,CAzJC,UAAC,CAAU,WAAC,CAAW,IAAC,CAAI,CAAC;SA0JnC;QACD,OAzJO,IAAA,CAAK;KA0Jb;;;;;;;IAOH,YA/JG,CAAA,MAAA,EAAA,GAAA,EAAA,IAAA,EA+JH,EA/JsD,MAAA,CAAO,YAAC,CAAY,IAAC,EAAK,GAAA,CAAI,CAAC,EAAC;;;;;;;IAsKtF,eArKG,CAAA,MAAA,EAAA,GAAA,EAAA,KAAA,EAqKH;QACI,KAAK,CArKC,OAAC,CAAO,CAAC,CAAG,KAAQ,MAAA,CAAO,YAAC,CAAY,CAAC,EAAE,GAAA,CAAI,CAAC,CAAC;KAsKxD;;;;;;;IAOH,WA3KG,CAAA,MAAA,EAAA,GAAA,EAAA,IAAA,EA2KH,EA3KoD,MAAA,CAAO,YAAC,CAAY,IAAC,EAAK,GAAA,CAAI,WAAC,CAAW,CAAC,EAAC;;;;;;IAiLhG,YAhLG,CAAA,EAAA,EAAA,KAAA,EAgLH,EAhL6C,EAAA,CAAG,SAAC,GAAW,KAAA,CAAM,EAAC;;;;;IAqLnE,OApLG,CAAA,EAAA,EAoLH,EApLmC,OAAO,EAAA,CAAG,WAAC,CAAW,EAAC;;;;;;IA0L1D,OAzLG,CAAA,EAAA,EAAA,KAAA,EAyLH,EAzLqC,EAAA,CAAG,WAAC,GAAa,KAAA,CAAM,EAAC;;;;;IA8L7D,QA7LG,CAAA,EAAA,EA6LH,EA7L8B,OAAO,EAAA,CAAG,KAAC,CAAK,EAAC;;;;;;IAmM/C,QAlMG,CAAA,EAAA,EAAA,KAAA,EAkMH,EAlMqC,EAAA,CAAG,KAAC,GAAO,KAAA,CAAM,EAAC;;;;;IAuMvD,UAtMG,CAAA,EAAA,EAsMH,EAtMiC,OAAO,EAAA,CAAG,OAAC,CAAO,EAAC;;;;;;IA4MpD,UA3MG,CAAA,EAAA,EAAA,KAAA,EA2MH,EA3MwC,EAAA,CAAG,OAAC,GAAS,KAAA,CAAM,EAAC;;;;;IAgN5D,aA/MG,CAAA,IAAA,EA+MH,EA/MyC,OAAO,QAAA,CAAS,aAAC,CAAa,IAAC,CAAI,CAAC,EAAC;;;;;IAoN9E,cAnNG,CAAA,IAAA,EAmNH;QACI,uBAnNM,CAAA,GAAI,QAAA,CAAS,aAAC,CAAa,UAAC,CAAU,CAAC;QAoN7C,CAAC,CAnNC,SAAC,GAAW,IAAA,CAAK;QAoNnB,OAnNO,CAAA,CAAE;KAoNV;;;;;;IAMH,aAxNG,CAAA,OAAA,EAAA,GAAA,GAAA,QAAA,EAwNH,EAxNgE,OAAO,GAAA,CAAI,aAAC,CAAa,OAAC,CAAO,CAAC,EAAC;;;;;;;IA+NnG,eA9NG,CAAA,EAAA,EAAA,OAAA,EAAA,GAAA,GAAA,QAAA,EA8NH;QACI,OA9NO,GAAA,CAAI,eAAC,CAAe,EAAC,EAAG,OAAA,CAAQ,CAAC;KA+NzC;;;;;;IAMH,cAnOG,CAAA,IAAA,EAAA,GAAA,GAAA,QAAA,EAmOH,EAnOuD,OAAO,GAAA,CAAI,cAAC,CAAc,IAAC,CAAI,CAAC,EAAC;;;;;;;IA0OxF,eAzOG,CAAA,QAAA,EAAA,SAAA,EAAA,GAAA,GAAA,QAAA,EAyOH;QACI,uBAzOM,EAAA,IAAwB,GAAC,CAAG,aAAC,CAAa,QAAC,CAAQ,CAAA,CAAC;QA0O1D,EAAE,CAzOC,YAAC,CAAY,QAAC,EAAS,SAAA,CAAU,CAAC;QA0OrC,OAzOO,EAAA,CAAG;KA0OX;;;;;;IAMH,kBA9OG,CAAA,GAAA,EAAA,GAAA,GAAA,QAAA,EA8OH;QACI,uBA9OM,KAAA,IAA0B,GAAC,CAAG,aAAC,CAAa,OAAC,CAAO,CAAA,CAAC;QA+O3D,IAAI,CA9OC,WAAC,CAAW,KAAC,EAAM,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,CAAC,CAAC;QA+OlD,OA9OO,KAAA,CAAM;KA+Od;;;;;IAKH,gBAlPG,CAAA,EAAA,EAkPH,EAlPwD,OAAO,EAAM,EAAC,GAAG,gBAAC,EAAgB,CAAE,EAAC;;;;;IAuP7F,aAtPG,CAAA,EAAA,EAsPH,EAtPqD,OAAO,EAAM,EAAC,GAAG,UAAC,CAAU,EAAC;;;;;IA2PlF,OA1PG,CAAA,EAAA,EA0PH,EA1P0C,OAAO,EAAM,EAAC,GAAG,IAAC,CAAI,EAAC;;;;;IA+PjE,KA9PG,CAAA,IAAA,EA8PH,EA9P4B,OAAO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,CAAC,EAAC;;;;;;IAoQzD,sBAnQG,CAAA,OAAA,EAAA,IAAA,EAmQH;QACI,OAnQO,OAAA,CAAQ,sBAAC,CAAsB,IAAC,CAAI,CAAC;KAoQ7C;;;;;;IAMH,oBAxQG,CAAA,OAAA,EAAA,IAAA,EAwQH;QACI,OAxQO,OAAA,CAAQ,oBAAC,CAAoB,IAAC,CAAI,CAAC;KAyQ3C;;;;;IAKH,SA5QG,CAAA,OAAA,EA4QH,EA5QmC,OAAO,KAAA,CAAM,SAAC,CAAS,KAAC,CAAK,IAAC,CAAI,OAAC,CAAO,SAAC,EAAU,CAAA,CAAE,CAAC,EAAC;;;;;;IAkR5F,QAjRG,CAAA,OAAA,EAAA,SAAA,EAiRH,EAjR8C,OAAA,CAAQ,SAAC,CAAS,GAAC,CAAG,SAAC,CAAS,CAAC,EAAC;;;;;;IAuRhF,WAtRG,CAAA,OAAA,EAAA,SAAA,EAsRH,EAtRiD,OAAA,CAAQ,SAAC,CAAS,MAAC,CAAM,SAAC,CAAS,CAAC,EAAC;;;;;;IA4RtF,QA3RG,CAAA,OAAA,EAAA,SAAA,EA2RH;QACI,OA3RO,OAAA,CAAQ,SAAC,CAAS,QAAC,CAAQ,SAAC,CAAS,CAAC;KA4R9C;;;;;;;IAOH,QAjSG,CAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAiSH;QACI,OAAO,CAjSC,KAAC,CAAK,SAAC,CAAS,GAAG,UAAA,CAAW;KAkSvC;;;;;;IAMH,WAtSG,CAAA,OAAA,EAAA,SAAA,EAsSH;;;QAGI,OAAO,CAtSC,KAAC,CAAK,SAAC,CAAS,GAAG,EAAA,CAAG;KAuS/B;;;;;;IAMH,QA3SG,CAAA,OAAA,EAAA,SAAA,EA2SH,EA3SsD,OAAO,OAAA,CAAQ,KAAC,CAAK,SAAC,CAAS,CAAC,EAAC;;;;;;;IAkTvF,QAjTG,CAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAiTH;QACI,uBAjTM,KAAA,GAAQ,IAAA,CAAK,QAAC,CAAQ,OAAC,EAAQ,SAAA,CAAU,IAAI,EAAA,CAAG;QAkTtD,OAjTO,UAAA,GAAa,KAAA,IAAS,UAAA,GAAa,KAAA,CAAM,MAAC,GAAQ,CAAA,CAAE;KAkT5D;;;;;IAKH,OArTG,CAAA,OAAA,EAqTH,EArTkC,OAAO,OAAA,CAAQ,OAAC,CAAO,EAAC;;;;;IA0T1D,YAzTG,CAAA,OAAA,EAyTH;QACI,uBAzTM,GAAA,GAAM,IAAI,GAAA,EAAmB,CAAG;QA0TtC,uBAzTM,OAAA,GAAU,OAAA,CAAQ,UAAC,CAAU;QA0TnC,KAAK,qBAzTI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,OAAA,CAAQ,MAAC,EAAO,CAAA,EAAE,EAAG;YA0TvC,uBAzTM,MAAA,GAAS,OAAA,CAAQ,CAAC,CAAC,CAAC;YA0T1B,GAAG,CAzTC,GAAC,CAAG,MAAC,CAAM,IAAC,EAAK,MAAA,CAAO,KAAC,CAAK,CAAC;SA0TpC;QACD,OAzTO,GAAA,CAAI;KA0TZ;;;;;;IAMH,YA9TG,CAAA,OAAA,EAAA,SAAA,EA8TH;QACI,OA9TO,OAAA,CAAQ,YAAC,CAAY,SAAC,CAAS,CAAC;KA+TxC;;;;;;;IAOH,cApUG,CAAA,OAAA,EAAA,EAAA,EAAA,SAAA,EAoUH;QACI,OApUO,OAAA,CAAQ,cAAC,CAAc,EAAC,EAAG,SAAA,CAAU,CAAC;KAqU9C;;;;;;IAMH,YAzUG,CAAA,OAAA,EAAA,SAAA,EAyUH;QACI,OAzUO,OAAA,CAAQ,YAAC,CAAY,SAAC,CAAS,CAAC;KA0UxC;;;;;;;IAOH,cA/UG,CAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EA+UH;QACI,OA/UO,OAAA,CAAQ,cAAC,CAAc,EAAC,EAAG,IAAA,CAAK,CAAC;KAgVzC;;;;;;;IAOH,YArVG,CAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAqVH,EArVgE,OAAA,CAAQ,YAAC,CAAY,IAAC,EAAK,KAAA,CAAM,CAAC,EAAC;;;;;;;;IA6VnG,cA5VG,CAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EA4VH;QACI,OAAO,CA5VC,cAAC,CAAc,EAAC,EAAG,IAAA,EAAM,KAAA,CAAM,CAAC;KA6VzC;;;;;;IAMH,eAjWG,CAAA,OAAA,EAAA,SAAA,EAiWH,EAjWyD,OAAA,CAAQ,eAAC,CAAe,SAAC,CAAS,CAAC,EAAC;;;;;;;IAwW7F,iBAvWG,CAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAuWH;QACI,OAAO,CAvWC,iBAAC,CAAiB,EAAC,EAAG,IAAA,CAAK,CAAC;KAwWrC;;;;;IAKH,iBA3WG,CAAA,EAAA,EA2WH,EA3WqC,OAAO,IAAA,CAAK,iBAAC,CAAiB,EAAC,CAAE,GAAG,IAAA,CAAK,OAAC,CAAO,EAAC,CAAE,GAAG,EAAA,CAAG,EAAC;;;;IA+WhG,kBA9WG,GA8WH;QACI,OA9WO,QAAA,CAAS,cAAC,CAAc,kBAAC,CAAkB,WAAC,CAAW,CAAC;KA+WhE;;;;;IAKH,qBAlXG,CAAA,EAAA,EAkXH;QACI,IAlXI;YAmXF,OAlXO,EAAA,CAAG,qBAAC,EAAqB,CAAE;SAmXnC;QAlXC,OAAA,CAAQ,EAAE;YAmXV,OAlXO,EAAA,GAAE,EAAI,CAAA,EAAG,MAAA,EAAQ,CAAA,EAAG,IAAA,EAAM,CAAA,EAAG,KAAA,EAAO,CAAA,EAAG,KAAA,EAAO,CAAA,EAAG,MAAA,EAAQ,CAAA,EAAE,CAAC;SAmXpE;KACF;;;;;IAKH,QAtXG,CAAA,GAAA,EAsXH,EAtXoC,OAAO,QAAA,CAAS,KAAC,CAAK,EAAC;;;;;;IA4X3D,QA3XG,CAAA,GAAA,EAAA,QAAA,EA2XH,EA3X8C,QAAA,CAAS,KAAC,GAAO,QAAA,IAAY,EAAA,CAAG,EAAC;;;;;;IAiY/E,cAhYG,CAAA,CAAA,EAAA,QAAA,EAgYH;QACI,IAAI,CAhYC,YAAY,WAAA,EAAa;YAiY5B,OAhYO,CAAA,CAAE,OAAC,IAAU,CAAA,CAAE,OAAC,CAAO,QAAC,CAAQ;gBAiYnC,CAAC,CAhYC,iBAAC,IAAoB,CAAA,CAAE,iBAAC,CAAiB,QAAC,CAAQ;gBAiYpD,CAAC,CAhYC,qBAAC,IAAwB,CAAA,CAAE,qBAAC,CAAqB,QAAC,CAAQ,CAAC;SAiYlE;QAED,OAhYO,KAAA,CAAM;KAiYd;;;;;IAKH,iBApYG,CAAA,EAAA,EAoYH;QACI,OApYO,EAAA,YAAc,WAAA,IAAe,EAAA,CAAG,QAAC,IAAW,UAAA,CAAW;KAqY/D;;;;;IAKH,UAxYG,CAAA,IAAA,EAwYH,EAxYoC,OAAO,IAAA,CAAK,QAAC,KAAY,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;IA6Y7E,aA5YG,CAAA,IAAA,EA4YH,EA5YuC,OAAO,IAAA,CAAK,QAAC,KAAY,IAAA,CAAK,YAAC,CAAY,EAAC;;;;;IAiZnF,aAhZG,CAAA,IAAA,EAgZH,EAhZuC,OAAO,IAAA,CAAK,QAAC,KAAY,IAAA,CAAK,YAAC,CAAY,EAAC;;;;;IAqZnF,aApZG,CAAA,IAAA,EAoZH;QACI,OApZO,IAAA,CAAK,UAAC,IAAa,IAAA,IAAQ,IAAA,YAAgB,WAAA,CAAY;KAqZ/D;;;;;IAKH,YAxZG,CAAA,IAAA,EAwZH,EAxZqC,OAAO,IAAA,YAAgB,gBAAA,CAAiB,EAAC;;;;;IA6Z9E,aA5ZG,CAAA,IAAA,EA4ZH,EA5ZmC,OAAO,QAAA,CAAS,UAAC,CAAU,IAAC,CAAI,iBAAC,CAAiB,IAAC,CAAI,EAAE,IAAA,CAAK,CAAC,EAAC;;;;;IAianG,SAhaG,CAAA,IAAA,EAgaH,EAha+B,OAAO,QAAA,CAAS,SAAC,CAAS,IAAC,CAAI,CAAC,EAAC;;;;;IAqahE,OApaG,CAAA,EAAA,EAoaH,EApaiC,OAAO,EAAM,EAAC,GAAG,IAAC,CAAI,EAAC;;;;;IAyaxD,WAvaG,CAAA,KAAA,EAuaH;QACI,qBAvaI,GAAA,GAAM,KAAA,CAAM,GAAC,CAAG;QAwapB,IAAI,GAvaC,IAAM,IAAA,EAAM;YAwaf,GAAG,GAvaG,KAAA,CAAM,aAAC,CAAa;;;;YA2a1B,IAAI,GAvaC,IAAM,IAAA,EAAM;gBAwaf,OAvaO,cAAA,CAAe;aAwavB;YACD,IAAI,GAvaC,CAAG,UAAC,CAAU,IAAC,CAAI,EAAE;gBAwaxB,GAAG,GAvaG,MAAA,CAAO,YAAC,CAAY,QAAC,CAAQ,GAAC,CAAG,SAAC,CAAS,CAAC,CAAC,EAAE,EAAA,CAAG,CAAC,CAAC;gBAwa1D,IAAI,KAvaC,CAAK,QAAC,KAAY,uBAAA,IAA2B,mBAAA,CAAoB,cAAC,CAAc,GAAC,CAAG,EAAE;;;;oBA2azF,GAAG,GAvaG,EAAA,mBAAwB,GAAK,GAAC,CAAG,CAAC;iBAwazC;aACF;SACF;QAED,OAvaO,OAAA,CAAQ,GAAC,CAAG,IAAI,GAAA,CAAI;KAwa5B;;;;;;IAMH,oBA5aG,CAAA,GAAA,EAAA,MAAA,EA4aH;QACI,IAAI,MA5aC,KAAU,QAAA,EAAU;YA6avB,OA5aO,MAAA,CAAO;SA6af;QACD,IAAI,MA5aC,KAAU,UAAA,EAAY;YA6azB,OA5aO,QAAA,CAAS;SA6ajB;QACD,IAAI,MA5aC,KAAU,MAAA,EAAQ;YA6arB,OA5aO,QAAA,CAAS,IAAC,CAAI;SA6atB;QACD,OA5aO,IAAA,CAAK;KA6ab;;;;IAIH,UA/aG,GA+aH,EA/a0B,OAAO,MAAA,CAAO,OAAC,CAAO,EAAC;;;;IAmbjD,WAlbG,GAkbH,EAlb4B,OAAO,MAAA,CAAO,QAAC,CAAQ,EAAC;;;;;IAubpD,WAtbG,CAAA,GAAA,EAsbH;QACI,uBAtbM,IAAA,GAAO,kBAAA,EAAmB,CAAE;QAublC,OAtbO,IAAA,IAAQ,IAAA,GAAO,IAAA,GAAO,YAAA,CAAa,IAAC,CAAI,CAAC;KAubjD;;;;IAIH,gBAzbG,GAybH,EAzb6B,WAAA,GAAc,IAAA,CAAK,EAAC;;;;IA6bjD,YA5bG,GA4bH,EA5b2B,OAAO,MAAA,CAAO,SAAC,CAAS,SAAC,CAAS,EAAC;;;;;;;IAmc9D,OAlcG,CAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAkcH;QACI,IAAI,CAlcC,YAAC,CAAY,OAAC,EAAQ,OAAA,GAAU,IAAA,EAAM,KAAA,CAAM,CAAC;KAmcnD;;;;;;IAMH,OAvcG,CAAA,OAAA,EAAA,IAAA,EAucH;QACI,OAvcO,IAAA,CAAK,YAAC,CAAY,OAAC,EAAQ,OAAA,GAAU,IAAA,CAAK,CAAC;KAwcnD;;;;;IAKH,gBA3cG,CAAA,OAAA,EA2cH,EA3cwC,OAAO,gBAAA,CAAiB,OAAC,CAAO,CAAC,EAAC;;;;IA+c1E,oBA7cG,GA6cH;QACI,OA7cO,OAAA,EAAY,OAAC,GAAQ,SAAC,CAAS,SAAC,CAAS,KAAK,UAAA,CAAW;KA8cjE;;;;IAIH,cAhdG,GAgdH;;;QAGI,OAhdO,MAAA,CAAO,WAAC,IAAc,MAAA,CAAO,WAAC,CAAW,GAAC,GAAK,MAAA,CAAO,WAAC,CAAW,GAAC,EAAG;YAidvB,IAhdI,IAAA,EAAK,CAAE,OAAC,EAAO,CAAE;KAid5E;;;;IAIH,eAldG,GAkdH,EAld+B,OAAO,IAAA,CAAK,EAAC;;;;;IAud5C,SArdG,CAAA,IAAA,EAqdH,EArdyC,OAAO,gBAAA,CAAiB,QAAC,CAAQ,MAAC,EAAO,IAAA,CAAK,CAAC,EAAC;;;;;;IA2dzF,SAzdG,CAAA,IAAA,EAAA,KAAA,EAydH;;;QAGI,QAAQ,CAzdC,MAAC,GAAQ,kBAAA,CAAmB,IAAC,CAAI,GAAG,GAAA,GAAM,kBAAA,CAAmB,KAAC,CAAK,CAAC;KA0d9E;CACF;AAED,IAzdI,WAAA,GAAgC,IAAA,CAAK;;;;AA6dzC,SAAA,kBAAA,GAAA;IACE,IAAI,CA5dC,WAAC,EAAY;QA6dhB,WAAW,KA5dG,QAAA,CAAS,aAAC,CAAa,MAAC,CAAM,EAAA,CAAG;QA6d/C,IAAI,CA5dC,WAAC,EAAY;YA6dhB,OA5dO,IAAA,CAAK;SA6db;KACF;IACD,OA5dO,WAAA,CAAY,YAAC,CAAY,MAAC,CAAM,CAAC;CA6dzC;;AAGD,IA5dI,cAAgB,CAAI;;;;;AAiexB,SAAA,YAAA,CAheC,GAAA,EAgeD;IACE,IAAI,CAheC,cAAC,EAAe;QAienB,cAAc,GAheG,QAAA,CAAS,aAAC,CAAa,GAAC,CAAG,CAAC;KAie9C;IACD,cAAc,CAheC,YAAC,CAAY,MAAC,EAAO,GAAA,CAAI,CAAC;IAiezC,OAheO,CAAA,cAAE,CAAc,QAAC,CAAQ,MAAC,CAAM,CAAC,CAAC,KAAK,GAAA,IAAO,cAAA,CAAe,QAAC;QAiehB,GAAG,GAheG,cAAA,CAAe,QAAC,CAAQ;CAiepF;;;;;;AAMD,AAAA,SAAA,gBAAA,CApeC,SAAA,EAAA,IAAA,EAoeD;IACE,IAAI,GApeG,kBAAA,CAAmB,IAAC,CAAI,CAAC;IAqehC,KAAK,uBApeM,MAAA,IAAU,SAAA,CAAU,KAAC,CAAK,GAAC,CAAG,EAAE;QAqezC,uBApeM,OAAA,GAAU,MAAA,CAAO,OAAC,CAAO,GAAC,CAAG,CAAC;QAqepC,MApeM,CAAA,UAAE,EAAW,WAAA,CAAY,GAqe3B,OAAO,IApeI,CAAA,CAAE,GAAG,CAAA,MAAE,EAAO,EAAA,CAAG,GAAG,CAAA,MAAE,CAAM,KAAC,CAAK,CAAC,EAAE,OAAA,CAAQ,EAAE,MAAA,CAAO,KAAC,CAAK,OAAC,GAAS,CAAA,CAAE,CAAC,CAAC;QAqezF,IAAI,UApeC,CAAU,IAAC,EAAI,KAAM,IAAA,EAAM;YAqe9B,OApeO,kBAAA,CAAmB,WAAC,CAAW,CAAC;SAqexC;KACF;IACD,OApeO,IAAA,CAAK;CAqeb;;ADv4BD;;;;;;;AASA,AACA;;;;;;;;AAQA,AAAC,MAAA,QAAA,GAAA,IAAA,cAAA,CAAA,eAAA,CAAA,CAAA;;ADjBD;;;;;;;;AAQA,AAAA,SAAA,aAAA,GAAA;IACE,OADO,CAAA,CAAE,MAAC,CAAM,OAAC,CAAO,SAAC,CAAS;CAEnC;;ADXD;;;;;;;AASA,AACA,AAEA,AACA,AAEA,AACA;;;;;AAKA,AAAA,MAAA,uBAGC,SAAA,gBAAA,CAHD;;;;IAMA,WAAA,CACuB,IAAM,EAD7B;QAEI,KAAK,EAAE,CAAC;QADW,IAAvB,CAAA,IAAuB,GAAA,IAAA,CAAM;QAEzB,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;;;;;IAKH,KADG,GACH;QACI,IAAI,CADC,SAAC,GAAW,MAAA,EAAO,CAAE,WAAC,EAAW,CAAE;QAExC,IAAI,CADC,QAAC,GAAU,MAAA,EAAO,CAAE,UAAC,EAAU,CAAE;KAEvC;;;;IAIH,IAHG,QAAA,GAGH,EAH6B,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;IAOpD,kBALG,GAKH,EALiC,SAAO,MAAA,EAAO,CAAE,WAAC,CAAW,IAAC,CAAI,IAAC,CAAI,GAAG,EAAC;;;;;IAU3E,UARG,CAAA,EAAA,EAQH;QACI,MAAM,EARC,CAAE,oBAAC,CAAoB,IAAC,CAAI,IAAC,EAAK,QAAA,CAAS,CAAC,gBAAC,CAAgB,UAAC,EAAW,EAAA,EAAI,KAAA,CAAM,CAAC;KAS5F;;;;;IAKH,YAXG,CAAA,EAAA,EAWH;QACI,MAAM,EAXC,CAAE,oBAAC,CAAoB,IAAC,CAAI,IAAC,EAAK,QAAA,CAAS,CAAC,gBAAC,CAAgB,YAAC,EAAa,EAAA,EAAI,KAAA,CAAM,CAAC;KAY9F;;;;IAIH,IAbG,QAAA,GAaH,EAb2B,OAAO,IAAA,CAAK,SAAC,CAAS,QAAC,CAAQ,EAAC;;;;IAiB3D,IAhBG,MAAA,GAgBH,EAhByB,OAAO,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,EAAC;;;;IAoBvD,IAnBG,IAAA,GAmBH,EAnBuB,OAAO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,EAAC;;;;;IAwBnD,IAvBG,QAAA,CAAA,OAAA,EAuBH,EAvBkC,IAAA,CAAK,SAAC,CAAS,QAAC,GAAU,OAAA,CAAQ,EAAC;;;;;;;IA8BrE,SA5BG,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EA4BH;QACI,IAAI,aA5BC,EAAa,EAAG;YA6BnB,IAAI,CA5BC,QAAC,CAAQ,SAAC,CAAS,KAAC,EAAM,KAAA,EAAO,GAAA,CAAI,CAAC;SA6B5C;aA5BM;YA6BL,IAAI,CA5BC,SAAC,CAAS,IAAC,GAAM,GAAA,CAAI;SA6B3B;KACF;;;;;;;IAOH,YAjCG,CAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAiCH;QACI,IAAI,aAjCC,EAAa,EAAG;YAkCnB,IAAI,CAjCC,QAAC,CAAQ,YAAC,CAAY,KAAC,EAAM,KAAA,EAAO,GAAA,CAAI,CAAC;SAkC/C;aAjCM;YAkCL,IAAI,CAjCC,SAAC,CAAS,IAAC,GAAM,GAAA,CAAI;SAkC3B;KACF;;;;IAIH,OAnCG,GAmCH,EAnCoB,IAAA,CAAK,QAAC,CAAQ,OAAC,EAAO,CAAE,EAAC;;;;IAuC7C,IArCG,GAqCH,EArCiB,IAAA,CAAK,QAAC,CAAQ,IAAC,EAAI,CAAE,EAAC;;AAChC,uBAAP,CAAA,UAAO,GAAoC;IAsC3C,EArCE,IAAA,EAAM,UAAA,EAAW;CAsClB,CArCC;;;;AAED,uBAAD,CAAA,cAAC,GAAA,MAAA;IAwCD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAcC;;AD1ID;;;;;;;AASA,AAEA,AACA,AAmBA;;;;;AAKA,AAAA,MAAA,IAAA,CAAA;;;;IAKA,WAAA,CAFuB,IAAM,EAE7B;QAFuB,IAAvB,CAAA,IAAuB,GAAA,IAAA,CAAM;QAAI,IAAA,CAAA,IAAA,GAAA,MAAA,EAAA,CAAA;KAAA;;;;;;IASjC,MAPG,CAAA,GAAA,EAAA,aAOH,GAPG,KAAA,EAOH;QACI,IAAI,CAPC,GAAC;YAAI,OAAO,IAAA,CAAK;QAQtB,OAPO,IAAA,CAAK,mBAAC,CAAmB,GAAC,EAAI,aAAA,CAAc,CAAC;KAQrD;;;;;;IAMH,OAXG,CAAA,IAAA,EAAA,aAWH,GAXG,KAAA,EAWH;QACI,IAAI,CAXC,IAAC;YAAK,OAAO,EAAA,CAAG;QAYrB,OAXO,IAAA,CAAK,MAAC,CAAM,CAAC,MAAwB,EAAG,GAAK,KAWxD;YACM,IAAI,GAXC,EAAI;gBAYP,MAAM,CAXC,IAAC,CAAI,IAAC,CAAI,mBAAC,CAAmB,GAAC,EAAI,aAAA,CAAc,CAAC,CAAC;aAY3D;YACD,OAXO,MAAA,CAAO;SAYf,EAXE,EAAA,CAAG,CAAC;KAYR;;;;;IAKH,MAdG,CAAA,YAAA,EAcH;QACI,IAAI,CAdC,YAAC;YAAa,OAAO,IAAA,CAAK;QAe/B,OAdO,IAAA,CAAK,IAAC,CAAI,aAAC,CAAa,IAAC,CAAI,IAAC,EAAK,CAc9C,KAAA,EAd8C,YAAS,CAcvD,CAAA,CAdmE,CAAG,CAAC;KAepE;;;;;IAKH,OAjBG,CAAA,YAAA,EAiBH;QACI,IAAI,CAjBC,YAAC;YAAa,OAAO,EAAA,CAAG;QAkB7B,uBAjBM,IAAA,gBAAoB,IAAA,CAAK,IAAC,CAAI,gBAAC,CAAgB,IAAC,CAAI,IAAC,EAAK,CAiBpE,KAAA,EAjBoE,YAAS,CAiB7E,CAAA,CAjByF,CAAG,CAAC;QAkBzF,OAjBO,IAAA,GAAO,EAAA,CAAG,KAAC,CAAK,IAAC,CAAI,IAAC,CAAI,GAAG,EAAA,CAAG;KAkBxC;;;;;;IAMH,SArBG,CAAA,GAAA,EAAA,QAAA,EAqBH;QACI,IAAI,CArBC,GAAC;YAAI,OAAO,IAAA,CAAK;QAsBtB,QAAQ,GArBG,QAAA,IAAY,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,CAAC;QAsBhD,uBArBM,IAAA,KAAwB,IAAA,CAAK,MAAC,CAAM,QAAC,CAAQ,EAAA,CAAG;QAsBtD,IAAI,IArBC,EAAK;YAsBR,OArBO,IAAA,CAAK,yBAAC,CAAyB,GAAC,EAAI,IAAA,CAAK,CAAC;SAsBlD;QACD,OArBO,IAAA,CAAK,mBAAC,CAAmB,GAAC,EAAI,IAAA,CAAK,CAAC;KAsB5C;;;;;IAKH,SAxBG,CAAA,YAAA,EAwBH,EAxB0C,IAAA,CAAK,gBAAC,oBAAgB,IAAC,CAAI,MAAC,CAAM,YAAC,CAAY,GAAG,CAAC,EAAC;;;;;IA6B9F,gBA3BG,CAAA,IAAA,EA2BH;QACI,IAAI,IA3BC,EAAK;YA4BR,IAAI,CA3BC,IAAC,CAAI,MAAC,CAAM,IAAC,CAAI,CAAC;SA4BxB;KACF;;;;;;IAzBA,mBAAA,CAAA,IAAA,EAAA,aAAH,GAAG,KAAA,EAAH;QAiCI,IAAI,CA/BC,aAAC,EAAc;YAgClB,uBA/BM,QAAA,GAAmB,IAAA,CAAK,cAAC,CAAc,IAAC,CAAI,CAAC;YAgCnD,uBA/BM,IAAA,KAAwB,IAAA,CAAK,MAAC,CAAM,QAAC,CAAQ,EAAA,CAAG;;;;YAmCtD,IAAI,IA/BC,IAAO,IAAA,CAAK,mBAAC,CAAmB,IAAC,EAAK,IAAA,CAAK;gBAAE,OAAO,IAAA,CAAK;SAgC/D;QACD,uBA/BM,OAAA,IAA2B,IAAA,CAAK,IAAC,CAAI,aAAC,CAAa,MAAC,CAAU,CAAA,CAAgB;QAgCpF,IAAI,CA/BC,yBAAC,CAAyB,IAAC,EAAK,OAAA,CAAQ,CAAC;QAgC9C,uBA/BM,IAAA,GAAO,IAAA,CAAK,IAAC,CAAI,oBAAC,CAAoB,IAAC,CAAI,IAAC,EAAK,MAAA,CAAO,CAAC,CAAC,CAAC,CAAC;QAgClE,IAAI,CA/BC,IAAC,CAAI,WAAC,CAAW,IAAC,EAAK,OAAA,CAAQ,CAAC;QAgCrC,OA/BO,OAAA,CAAQ;KAgChB;;;;;;IA7BA,yBAAA,CAAA,GAAA,EAAA,EAAA,EAAH;QAoCI,MAAM,CAnCC,IAAC,CAAI,GAAC,CAAG,CAAC,OAAC,CAAO,CAAC,IAAM,KAAW,IAAA,CAAK,IAAC,CAAI,YAAC,CAAY,EAAC,EAAG,IAAA,EAAM,GAAA,CAAI,IAAC,CAAI,CAAC,CAAC,CAAC;QAoCxF,OAnCO,EAAA,CAAG;KAoCX;;;;;IAjCA,cAAA,CAAA,GAAA,EAAH;QAuCI,uBAtCM,IAAA,GAAe,GAAA,CAAI,IAAC,GAAM,MAAA,GAAS,UAAA,CAAW;QAuCpD,OAtCO,CAsCX,EAtCW,IAAI,CAsCf,EAAA,EAtCmB,GAAK,CAAG,IAAC,CAAI,CAsChC,CAAA,CAtCiC,CAAG;KAuCjC;;;;;;IApCA,mBAAA,CAAA,GAAA,EAAA,IAAA,EAAH;QA2CI,OA1CO,MAAA,CAAO,IAAC,CAAI,GAAC,CAAG,CAAC,KAAC,CAAK,CAAC,GAAK,KAAW,IAAA,CAAK,IAAC,CAAI,YAAC,CAAY,IAAC,EAAK,GAAA,CAAI,KAAK,GAAA,CAAI,GAAC,CAAG,CAAC,CAAC;KA2ChG;;AAzCI,IAAP,CAAA,UAAO,GAAoC;IA2C3C,EA1CE,IAAA,EAAM,UAAA,EAAW;CA2ClB,CA1CC;;;;AAED,IAAD,CAAA,cAAC,GAAA,MAAA;IA6CD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAYC;;ADpLD;;;;;;;AASA,AAEA,AACA,AACA;;;;AAIA,AAAC,MAAA,aAAA,GAAA,IAAA,cAAA,CAAA,eAAA,CAAA,CAAA;;;;;;;AAOD,AAAA,SAAA,qBAAA,CALC,YAAA,EAAA,QAAA,EAAA,QAAA,EAKD;IACE,OALO,MAKT;;;QAGI,QAAQ,CALC,GAAC,CAAG,qBAAC,CAAqB,CAAC,WAAC,CAAW,IAAC,CAAI,MAKzD;YACM,uBALM,GAAA,GAAM,MAAA,EAAO,CAAE;YAMrB,uBALM,MAAA,GAMF,KAAK,CALC,SAAC,CAAS,KAAC,CAAK,KAAC,CAAK,GAAC,CAAG,gBAAC,CAAgB,QAAC,EAAS,CAIrE,oBAAA,CAJqE,CAAuB,CAAC,CAAC;YAMxF,MAAM,CALC,MAAC,CAAM,EAAC,IAAK,GAAA,CAAI,YAAC,CAAY,EAAC,EAAG,eAAA,CAAgB,KAAK,YAAA,CAAa;iBAMtE,OALC,CAAO,EAAC,IAAK,GAAA,CAAI,MAAC,CAAM,EAAC,CAAE,CAAC,CAAC;SAMpC,CALC,CAAC;KAMJ,CALC;CAMH;AAED,AALO,MAAM,2BAAA,GAA0C;IAMrD;QACE,OAAO,EALE,eAAA;QAMT,UAAU,EALE,qBAAA;QAMZ,IAAI,EALE,CAAA,aAAE,EAAc,QAAA,EAAU,QAAA,CAAS;QAMzC,KAAK,EALE,IAAA;KAMR;CACF,CALC;;ADxCF;;;;;;;AASA,AAEA,AACA,AAAA,MAAA,qBAAA,CAAA;;;;IAIA,OAHG,IAAA,GAGH,EAHkB,oBAAA,CAAqB,IAAI,qBAAA,EAAsB,CAAE,CAAC,EAAC;;;;;IAQrE,WANG,CAAA,QAAA,EAMH;QACIA,OAAM,CANC,uBAAC,CAAuB,GAAG,CAAA,IAAO,EAAK,eAMlD,GAN6E,IAAA,KAM7E;YACM,uBANM,WAAA,GAAc,QAAA,CAAS,qBAAC,CAAqB,IAAC,EAAK,eAAA,CAAgB,CAAC;YAO1E,IAAI,WANC,IAAc,IAAA,EAAM;gBAOvB,MANM,IAAI,KAAA,CAAM,yCAAC,CAAyC,CAAC;aAO5D;YACD,OANO,WAAA,CAAY;SAOpB,CANC;QAQFA,OAAM,CANC,4BAAC,CAA4B,GAAG,MAAM,QAAA,CAAS,mBAAC,EAAmB,CAAE;QAQ5EA,OAAM,CANC,2BAAC,CAA2B,GAAG,MAAM,QAAA,CAAS,kBAAC,EAAkB,CAAE;QAQ1E,uBANM,aAAA,GAAgB,CAAA,QAAW,uBAMrC;YACM,uBANM,aAAA,GAAgBA,OAAA,CAAO,4BAAC,CAA4B,EAAC,CAAE;YAO7D,qBANI,KAAA,GAAQ,aAAA,CAAc,MAAC,CAAM;YAOjC,qBANI,OAAA,GAAU,KAAA,CAAM;YAOpB,uBANM,SAAA,GAAY,UAAA,QAAmB,oBAM3C;gBACQ,OAAO,GANG,OAAA,IAAW,QAAA,CAAS;gBAO9B,KAAK,EANC,CAAE;gBAOR,IAAI,KANC,IAAQ,CAAA,EAAG;oBAOd,QAAQ,CANC,OAAC,CAAO,CAAC;iBAOnB;aACF,CANC;YAOF,aAAa,CANC,OAAC,CAAO,UAAC,WAAqB,oBAMlD;gBACQ,WAAW,CANC,UAAC,CAAU,SAAC,CAAS,CAAC;aAOnC,CANC,CAAC;SAOJ,CANC;QAQF,IAAI,CANCA,OAAC,CAAM,sBAAC,CAAsB,EAAE;YAOnCA,OAAM,CANC,sBAAC,CAAsB,GAAG,EAAA,CAAG;SAOrC;QACDA,OAAM,CANC,sBAAC,CAAsB,CAAC,IAAC,CAAI,aAAC,CAAa,CAAC;KAOpD;;;;;;;IAOH,qBAXG,CAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAWH;QAEI,IAAI,IAXC,IAAO,IAAA,EAAM;YAYhB,OAXO,IAAA,CAAK;SAYb;QACD,uBAXM,CAAA,GAAI,QAAA,CAAS,cAAC,CAAc,IAAC,CAAI,CAAC;QAYxC,IAAI,CAXC,IAAI,IAAA,EAAM;YAYb,OAXO,CAAA,CAAE;SAYV;aAXM,IAAA,CAAK,eAAC,EAAgB;YAY3B,OAXO,IAAA,CAAK;SAYb;QACD,IAAI,MAXC,EAAM,CAAE,YAAC,CAAY,IAAC,CAAI,EAAE;YAY/B,OAXO,IAAA,CAAK,qBAAC,CAAqB,QAAC,EAAS,MAAA,EAAO,CAAE,OAAC,CAAO,IAAC,CAAI,EAAE,IAAA,CAAK,CAAC;SAY3E;QACD,OAXO,IAAA,CAAK,qBAAC,CAAqB,QAAC,EAAS,MAAA,EAAO,CAAE,aAAC,CAAa,IAAC,CAAI,EAAE,IAAA,CAAK,CAAC;KAYjF;CACF;;AD7ED;;;;;;;AASA,AAEA,AACA,AACA;;;;;;;;;;AAUA,AAAA,MAAA,KAAA,CAAA;;;;IAIA,WAAA,CADuB,IAAM,EAC7B;QADuB,IAAvB,CAAA,IAAuB,GAAA,IAAA,CAAM;KAAI;;;;;IAOjC,QAFG,GAEH,EAFuB,OAAO,MAAA,EAAO,CAAE,QAAC,CAAQ,IAAC,CAAI,IAAC,CAAI,CAAC,EAAC;;;;;;IAQ5D,QAFG,CAAA,QAAA,EAEH,EAF+B,MAAA,EAAO,CAAE,QAAC,CAAQ,IAAC,CAAI,IAAC,EAAK,QAAA,CAAS,CAAC,EAAC;;AAChE,KAAP,CAAA,UAAO,GAAoC;IAG3C,EAFE,IAAA,EAAM,UAAA,EAAW;CAGlB,CAFC;;;;AAED,KAAD,CAAA,cAAC,GAAA,MAAA;IAKD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAUC;;AD7DD;;;;;;;AASA,AAEA,AACA,AACA;;;;AAIA,AAAA,AAEC;;;;;AAKD,AAAA,AAEC;;;;;;;;;AASD,AAAA,SAAA,WAAA,CANC,IAAA,EAAA,KAAA,EAMD;IACE,IAAI,CANC,EAAC,EAAG;QAOPA,OAAM,CANC,IAAC,CAAI,GAAG,EAAA,GAAK,EAAAA,OAAE,CAAM,IAAC,CAA8B,MAAc,EAAA,CAAG;KAO7E;IACD,EAAE,CANC,IAAC,CAAI,GAAG,KAAA,CAAM;CAOlB;AAED,IANI,EAA0B,CAAS;;ADpCvC;;;;;;;AASA,AACA,AAEA,MADM,WAAA,GAAc;IAElB,gBAAgB,EADED,cAAM;IAExB,QAAQ,EADED,MAAM;CAEjB,CADC;AAGF,MADM,mBAAA,GAAsB,OAAA,CAAQ;AAEpC,MADM,uBAAA,GAA0B,YAAA,CAAa;;;;;;;;AAS7C,AAAA,SAAA,oBAAA,CAFC,OAAA,EAED;IACE,OAFOD,YAAM,CAAY,OAAC,CAAO,CAAC;CAGnC;;;;;AAKD,AAAA,MAAAF,cAAA,CAAA;;;;;IAKA,WAAA,CALqB,IAAM,EAAe,KAAO,EAKjD;QALqB,IAArB,CAAA,IAAqB,GAAA,IAAA,CAAM;QAAe,IAA1C,CAAA,KAA0C,GAAA,KAAA,CAAO;KAAI;CAOpD;AAED,AAOA;;;;;AAKA,AAAA,SAAA,cAAA,CAlBC,WAAA,EAAA,UAAA,EAkBD;IACE,uBAlBM,MAAA,GAAS,CAAA,WAAE,IAAc,EAAA,EAAI,MAAC,CAAM,UAAC,IAAa,EAAA,CAAG,CAAC;IAmB5D,WAAW,CAlBC,mBAAC,EAAoB,oBAAA,CAAqB,CAAC;IAmBvD,WAAW,CAlBC,uBAAC,EAkBf,MAAA,CAAA,MAAA,CAAA,EAAA,EAlByC,WAAG,EAAY,mBAAI,CAAmB,MAAC,IAAS,EAAA,CAAG,CAkB5F,CAlB8F,CAAC;IAmB7F,OAlBO,MAAM,oBAAA,CAAqB;CAmBnC;;;;;AAKD,SAAA,mBAAA,CArBC,MAAA,EAqBD;IACE,OArBO,MAAA,CAAO,MAAC,CAAM,CAAC,IAAM,EAAK,CAAG,MAAQ,IAAE,CAAI,CAAC,CAAC,IAAC,CAAI,GAAG,CAAA,CAAE,KAAC,EAAM,IAAA,CAAK,EAAE,EAAA,CAAG,CAAC;CAsBjF;;;;AAID,AApBC,MAAA,uBAAA,GAAA;IAqBC;QACE,OAAO,EAAEC,eAAoB;QAC7B,UAAU,EAAE,cAAc;QAC1B,IAAI,EAAE;YACJ,CAACD,cAAY,EAAE,IAAID,QAAa,EAAE,CAAC;YACnC,CAACD,YAAiB,EAAE,IAAIC,QAAa,EAAE,CAAC;SACzC;QACD,KAAK,EAAE,IAAI;KACZ;CACF,CAAC;;ADhFF;;;;;;;AASA,AAEA,AACA;;;AAGA,AAAC,MAAA,qBAAA,GACG,IAAI,cAAA,CAAoC,qBAAE,CAAqB,CAAC;;;;AAIpE,AAAA,MAAA,YAAA,CAAA;;;;;IAOA,WAAA,CADe,OAA4B,EAAW,KAAO,EAC7D;QADsD,IAAtD,CAAA,KAAsD,GAAA,KAAA,CAAO;QAFnD,IAAV,CAAA,kBAAU,GAAqB,IAAI,GAAA,EAA+B,CAAG;QAKjE,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;KAC3C;;;;;;;IAOH,gBAPG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAOH;QACI,uBAPM,MAAA,GAAS,IAAA,CAAK,cAAC,CAAc,SAAC,CAAS,CAAC;QAQ9C,OAPO,MAAA,CAAO,gBAAC,CAAgB,OAAC,EAAQ,SAAA,EAAW,OAAA,CAAQ,CAAC;KAQ7D;;;;;;;IAOH,sBAZG,CAAA,MAAA,EAAA,SAAA,EAAA,OAAA,EAYH;QACI,uBAZM,MAAA,GAAS,IAAA,CAAK,cAAC,CAAc,SAAC,CAAS,CAAC;QAa9C,OAZO,MAAA,CAAO,sBAAC,CAAsB,MAAC,EAAO,SAAA,EAAW,OAAA,CAAQ,CAAC;KAalE;;;;IAIH,OAdG,GAcH,EAdsB,OAAO,IAAA,CAAK,KAAC,CAAK,EAAC;;;;;;IAoBzC,cAjBG,CAAA,SAAA,EAiBH;QACI,uBAjBM,MAAA,GAAS,IAAA,CAAK,kBAAC,CAAkB,GAAC,CAAG,SAAC,CAAS,CAAC;QAkBtD,IAAI,MAjBC,EAAO;YAkBV,OAjBO,MAAA,CAAO;SAkBf;QAED,uBAjBM,OAAA,GAAU,IAAA,CAAK,QAAC,CAAQ;QAkB9B,KAAK,qBAjBI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,OAAA,CAAQ,MAAC,EAAO,CAAA,EAAE,EAAG;YAkBvC,uBAjBM,MAAA,GAAS,OAAA,CAAQ,CAAC,CAAC,CAAC;YAkB1B,IAAI,MAjBC,CAAM,QAAC,CAAQ,SAAC,CAAS,EAAE;gBAkB9B,IAAI,CAjBC,kBAAC,CAAkB,GAAC,CAAG,SAAC,EAAU,MAAA,CAAO,CAAC;gBAkB/C,OAjBO,MAAA,CAAO;aAkBf;SACF;QACD,MAjBM,IAAI,KAAA,CAAM,CAiBpB,wCAAA,EAjBqB,SAA2C,CAiBhE,CAjByE,CAAE,CAAC;KAkBzE;;AAhBI,YAAP,CAAA,UAAO,GAAoC;IAkB3C,EAjBE,IAAA,EAAM,UAAA,EAAW;CAkBlB,CAjBC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IAoBD,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,qBAAqB,EAAG,EAAE,EAAG,EAAC;IAChF,EAAC,IAAI,EAAE,MAAM,GAAG;CACf,CARC;AAWF,AAgBA;;;AAGA,AAAA,MAAA,kBAAA,CAAA;;;;IAIA,WAAA,CAzCsB,IAAM,EAyC5B;QAzCsB,IAAtB,CAAA,IAAsB,GAAA,IAAA,CAAM;KAAI;;;;;;IAiDhC,QA7CY,CAAA,SAAA,EA6CZ,GA7CY;;;;;;;;IAqDZ,gBAnDY,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAmDZ,GAnDY;;;;;;;IA0DZ,sBAxDG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAwDH;QACI,uBAxDM,MAAA,GAAsB,MAAA,EAAO,CAAE,oBAAC,CAAoB,IAAC,CAAI,IAAC,EAAK,OAAA,CAAQ,CAAC;QAyD9E,IAAI,CAxDC,MAAC,EAAO;YAyDX,MAxDM,IAAI,KAAA,CAAM,CAwDtB,yBAAA,EAxDuB,MAA4B,CAwDnD,WAAA,EAxDyD,SAAc,CAwDvE,CAxDgF,CAAE,CAAC;SAyD9E;QACD,OAxDO,IAAA,CAAK,gBAAC,CAAgB,MAAC,EAAO,SAAA,EAAW,OAAA,CAAQ,CAAC;KAyD1D;;CACF,AAED,AAKC;;ADrJD;;;;;;;AASA,AACA,AACA,AACA,AAAA,MAAA,gBAAA,CAAA;IAAA,WAAA,GAAA;;;;QAGG,IAAH,CAAA,UAAG,GAAA,IAAA,GAAA,EAAA,CAAA;KAiCF;;;;;IA3BD,SAJG,CAAA,MAAA,EAIH;QACI,uBAJM,SAAA,GAAY,IAAI,GAAA,EAAW,CAAG;QAKpC,MAAM,CAJC,OAAC,CAAO,KAAC,IAIpB;YACM,IAAI,CAJC,IAAC,CAAI,UAAC,CAAU,GAAC,CAAG,KAAC,CAAK,EAAE;gBAK/B,IAAI,CAJC,UAAC,CAAU,GAAC,CAAG,KAAC,CAAK,CAAC;gBAK3B,SAAS,CAJC,GAAC,CAAG,KAAC,CAAK,CAAC;aAKtB;SACF,CAJC,CAAC;QAKH,IAAI,CAJC,aAAC,CAAa,SAAC,CAAS,CAAC;KAK/B;;;;;IAKH,aAPG,CAAA,SAAA,EAOH,GAP8C;;;;IAW9C,YATG,GASH,EAT6B,OAAO,KAAA,CAAM,IAAC,CAAI,IAAC,CAAI,UAAC,CAAU,CAAC,EAAC;;AAC1D,gBAAP,CAAA,UAAO,GAAoC;IAU3C,EATE,IAAA,EAAM,UAAA,EAAW;CAUlB,CATC;;;;AAED,gBAAD,CAAA,cAAC,GAAA,MAAA,EAYA,CAAC;AAGF,AAeA,AAAA,MAAA,mBAzBC,SAAA,gBAAA,CAyBD;;;;IAMA,WAAA,CA5BuB,IAAM,EA4B7B;QAEI,KAAK,EAAE,CAAC;QA9BW,IAAvB,CAAA,IAAuB,GAAA,IAAA,CAAM;QAFnB,IAAV,CAAA,UAAU,GAAa,IAAI,GAAA,EAAS,CAAG;QAC7B,IAAV,CAAA,WAAU,GAAc,IAAI,GAAA,EAAS,CAAG;QAgCpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAZjC;;;;;;IAdC,gBAAA,CAAA,MAAA,EAAA,IAAA,EAAH;QAkCI,MAAM,CAjCC,OAAC,CAAO,CAAC,KAAO,KAiC3B;YACM,uBAjCM,OAAA,GAAU,IAAA,CAAK,IAAC,CAAI,aAAC,CAAa,OAAC,CAAO,CAAC;YAkCjD,OAAO,CAjCC,WAAC,GAAa,KAAA,CAAM;YAkC5B,IAAI,CAjCC,WAAC,CAAW,GAAC,CAAG,IAAC,CAAI,WAAC,CAAW,OAAC,CAAO,CAAC,CAAC;SAkCjD,CAjCC,CAAC;KAkCJ;;;;;IAKH,OApCG,CAAA,QAAA,EAoCH;QACI,IAAI,CApCC,gBAAC,CAAgB,IAAC,CAAI,UAAC,EAAW,QAAA,CAAS,CAAC;QAqCjD,IAAI,CApCC,UAAC,CAAU,GAAC,CAAG,QAAC,CAAQ,CAAC;KAqC/B;;;;;IAKH,UAvCG,CAAA,QAAA,EAuCH,EAvCqC,IAAA,CAAK,UAAC,CAAU,MAAC,CAAM,QAAC,CAAQ,CAAC,EAAC;;;;;IA4CvE,aA1CG,CAAA,SAAA,EA0CH;QACI,IAAI,CA1CC,UAAC,CAAU,OAAC,CAAO,QAAC,IAAW,IAAA,CAAK,gBAAC,CAAgB,SAAC,EAAU,QAAA,CAAS,CAAC,CAAC;KA2CjF;;;;IAIH,WA5CG,GA4CH,EA5CwB,IAAA,CAAK,WAAC,CAAW,OAAC,CAAO,SAAC,IAAY,MAAA,EAAO,CAAE,MAAC,CAAM,SAAC,CAAS,CAAC,CAAC,EAAC;;AACpF,mBAAP,CAAA,UAAO,GAAoC;IA6C3C,EA5CE,IAAA,EAAM,UAAA,EAAW;CA6ClB,CA5CC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA;IA+CD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAcC;;ADzID;;;;;;;AASA,AAEA,AACA,AAEA,AADO,MAAM,cAAA,GAAyC;IAEpD,KAAK,EADE,4BAAA;IAEP,OAAO,EADE,8BAAA;IAET,OAAO,EADE,8BAAA;IAET,KAAK,EADE,sCAAA;IAEP,OAAC,EADQ,+BAAA;CAEV,CADC;AAGF,MADM,eAAA,GAAkB,SAAA,CAAU;AAElC,AADO,MAAM,kBAAA,GAAqB,QAAA,CAAS;AAE3C,AADO,MAAM,SAAA,GAAY,CACzB,QAAA,EADyB,kBAAY,CACrC,CADuD,CAAE;AAEzD,AADO,MAAM,YAAA,GAAe,CAC5B,WAAA,EAD4B,kBAAe,CAC3C,CAD6D,CAAE;;;;;AAM/D,AAAA,SAAA,oBAAA,CAJC,gBAAA,EAID;IACE,OAJO,YAAA,CAAa,OAAC,CAAO,eAAC,EAAgB,gBAAA,CAAiB,CAAC;CAKhE;;;;;AAKD,AAAA,SAAA,iBAAA,CAPC,gBAAA,EAOD;IACE,OAPO,SAAA,CAAU,OAAC,CAAO,eAAC,EAAgB,gBAAA,CAAiB,CAAC;CAQ7D;;;;;;;AAOD,AAAA,SAAA,aAAA,CACE,MAAA,EAAA,MAAA,EAAA,MAAA,EADF;IAEE,KAAK,qBAZI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,MAAA,CAAO,MAAC,EAAO,CAAA,EAAE,EAAG;QAatC,qBAZI,KAAA,GAAQ,MAAA,CAAO,CAAC,CAAC,CAAC;QActB,IAAI,KAZC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;YAaxB,aAAG,CAZW,MAAC,EAAO,KAAA,EAAO,MAAA,CAAO,CAAC;SAatC;aAZM;YAaL,KAAK,GAZG,KAAA,CAAM,OAAC,CAAO,eAAC,EAAgB,MAAA,CAAO,CAAC;YAa/C,MAAM,CAZC,IAAC,CAAI,KAAC,CAAK,CAAC;SAapB;KACF;IACD,OAZO,MAAA,CAAO;CAaf;;;;;AAKD,SAAA,sBAAA,CAfC,YAAA,EAeD;IACE,OAfO,CAAA,KAAQ,KAejB;QACI,uBAfM,oBAAA,GAAuB,YAAA,CAAa,KAAC,CAAK,CAAC;QAgBjD,IAAI,oBAfC,KAAwB,KAAA,EAAO;;YAiBlC,KAAK,CAfC,cAAC,EAAc,CAAE;YAgBvB,KAAK,CAfC,WAAC,GAAa,KAAA,CAAM;SAgB3B;KACF,CAfC;CAgBH;AACD,AAAA,MAAA,mBAAA,CAAA;;;;;IAOA,WAAA,CAhBsB,YAAc,EAAsB,gBAAkB,EAgB5E;QAhBsB,IAAtB,CAAA,YAAsB,GAAA,YAAA,CAAc;QAAsB,IAA1D,CAAA,gBAA0D,GAAA,gBAAA,CAAkB;QAHlE,IAAV,CAAA,gBAAU,GAAmB,IAAI,GAAA,EAAsB,CAAG;QAqBtD,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,YAAY,CAAC,CAAC;KAC9D;;;;;;;IAMH,cArBG,CAAA,OAAA,EAAA,IAAA,EAqBH;QACI,IAAI,CArBC,OAAC,IAAU,CAAA,IAAE,EAAK;YAsBrB,OArBO,IAAA,CAAK,eAAC,CAAe;SAsB7B;QACD,QAAQ,IArBC,CAAI,aAAC;YAsBZ,KArBK,iBAAA,CAAkB,QAAC,EAAS;gBAsB/B,qBArBI,QAAA,GAAW,IAAA,CAAK,gBAAC,CAAgB,GAAC,CAAG,IAAC,CAAI,EAAC,CAAE,CAAC;gBAsBlD,IAAI,CArBC,QAAC,EAAS;oBAsBb,QAAQ;wBACJ,IArBI,iCAAA,CAAkC,IAAC,CAAI,YAAC,EAAa,IAAA,CAAK,gBAAC,EAAiB,IAAA,CAAK,CAAC;oBAsB1F,IAAI,CArBC,gBAAC,CAAgB,GAAC,CAAG,IAAC,CAAI,EAAC,EAAG,QAAA,CAAS,CAAC;iBAsB9C;gBACD,EArBoC,QAAC,GAAS,WAAC,CAAW,OAAC,CAAO,CAAC;gBAsBnE,OArBO,QAAA,CAAS;aAsBjB;YACD,KArBK,iBAAA,CAAkB,MAAC;gBAsBtB,OArBO,IAAI,iBAAA,CAAkB,IAAC,CAAI,YAAC,EAAa,IAAA,CAAK,gBAAC,EAAiB,OAAA,EAAS,IAAA,CAAK,CAAC;YAsBxF,SArBS;gBAsBP,IAAI,CArBC,IAAC,CAAI,gBAAC,CAAgB,GAAC,CAAG,IAAC,CAAI,EAAC,CAAE,EAAE;oBAsBvC,uBArBM,MAAA,GAAS,aAAA,CAAc,IAAC,CAAI,EAAC,EAAG,IAAA,CAAK,MAAC,EAAO,EAAA,CAAG,CAAC;oBAsBvD,IAAI,CArBC,gBAAC,CAAgB,SAAC,CAAS,MAAC,CAAM,CAAC;oBAsBxC,IAAI,CArBC,gBAAC,CAAgB,GAAC,CAAG,IAAC,CAAI,EAAC,EAAG,IAAA,CAAK,eAAC,CAAe,CAAC;iBAsB1D;gBACD,OArBO,IAAA,CAAK,eAAC,CAAe;aAsB7B;SACF;KACF;;;;IAIH,KAvBG,GAuBH,GAvBU;;;;IA2BV,GA1BG,GA0BH,GA1BQ;;AACD,mBAAP,CAAA,UAAO,GAAoC;IA2B3C,EA1BE,IAAA,EAAM,UAAA,EAAW;CA2BlB,CA1BC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA;IA6BD,EAAC,IAAI,EAAE,YAAY,GAAG;IACtB,EAAC,IAAI,EAAE,mBAAmB,GAAG;CAC5B,CAAC;AAGF,AAkBA,MAAA,mBAAA,CAAA;;;;IAKA,WAAA,CAhDsB,YAAc,EAgDpC;QAhDsB,IAAtB,CAAA,YAAsB,GAAA,YAAA,CAAc;QA4ClC,IAAF,CAAA,IAAM,GA9CyB,MAAA,CAAO,MAAC,CAAM,IAAC,CAAI,CAAC;KAEF;;;;IAoDjD,OAlDG,GAkDH,GAlDkB;;;;;;IA0DlB,aAtDG,CAAA,IAAA,EAAA,SAAA,EAsDH;QACI,IAAI,SAtDC,EAAU;YAuDb,OAtDO,QAAA,CAAS,eAAC,CAAe,cAAC,CAAc,SAAC,CAAS,EAAE,IAAA,CAAK,CAAC;SAuDlE;QAED,OAtDO,QAAA,CAAS,aAAC,CAAa,IAAC,CAAI,CAAC;KAuDrC;;;;;IAKH,aAzDG,CAAA,KAAA,EAyDH,EAzDsC,OAAO,QAAA,CAAS,aAAC,CAAa,KAAC,CAAK,CAAC,EAAC;;;;;IA8D5E,UA5DG,CAAA,KAAA,EA4DH,EA5DmC,OAAO,QAAA,CAAS,cAAC,CAAc,KAAC,CAAK,CAAC,EAAC;;;;;;IAkE1E,WAhEG,CAAA,MAAA,EAAA,QAAA,EAgEH,EAhEkD,MAAA,CAAO,WAAC,CAAW,QAAC,CAAQ,CAAC,EAAC;;;;;;;IAuEhF,YArEG,CAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAqEH;QACI,IAAI,MArEC,EAAO;YAsEV,MAAM,CArEC,YAAC,CAAY,QAAC,EAAS,QAAA,CAAS,CAAC;SAsEzC;KACF;;;;;;IAMH,WAzEG,CAAA,MAAA,EAAA,QAAA,EAyEH;QACI,IAAI,MAzEC,EAAO;YA0EV,MAAM,CAzEC,WAAC,CAAW,QAAC,CAAQ,CAAC;SA0E9B;KACF;;;;;IAKH,iBA5EG,CAAA,cAAA,EA4EH;QACI,qBA5EI,EAAA,GAAU,OAAO,cAAA,KAAmB,QAAA,GAAW,QAAA,CAAS,aAAC,CAAa,cAAC,CAAc;YA6EtC,cAAc,CA5EC;QA6ElE,IAAI,CA5EC,EAAC,EAAG;YA6EP,MA5EM,IAAI,KAAA,CAAM,CA4EtB,cAAA,EA5EuB,cAAiB,CA4ExC,4BAAA,CA5EsD,CAA8B,CAAC;SAqEnF;QASE,EAAE,CA5EC,WAAC,GAAa,EAAA,CAAG;QA6EpB,OA5EO,EAAA,CAAG;KA6EX;;;;;IAKH,UA/EG,CAAA,IAAA,EA+EH,EA/E+B,OAAO,IAAA,CAAK,UAAC,CAAU,EAAC;;;;;IAoFvD,WAlFG,CAAA,IAAA,EAkFH,EAlFgC,OAAO,IAAA,CAAK,WAAC,CAAW,EAAC;;;;;;;;IA0FzD,YAxFG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,SAAA,EAwFH;QACI,IAAI,SAxFC,EAAU;YAyFb,IAAI,GAxFG,CAwFb,EAxFa,SAAI,CAwFjB,CAAA,EAxF0B,IAAI,CAwF9B,CAxFkC,CAAE;YAyF9B,uBAxFM,YAAA,GAAe,cAAA,CAAe,SAAC,CAAS,CAAC;YAyF/C,IAAI,YAxFC,EAAa;gBAyFhB,EAAE,CAxFC,cAAC,CAAc,YAAC,EAAa,IAAA,EAAM,KAAA,CAAM,CAAC;aAyF9C;iBAxFM;gBAyFL,EAAE,CAxFC,YAAC,CAAY,IAAC,EAAK,KAAA,CAAM,CAAC;aAyF9B;SACF;aAxFM;YAyFL,EAAE,CAxFC,YAAC,CAAY,IAAC,EAAK,KAAA,CAAM,CAAC;SAyF9B;KACF;;;;;;;IAOH,eA7FG,CAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EA6FH;QACI,IAAI,SA7FC,EAAU;YA8Fb,uBA7FM,YAAA,GAAe,cAAA,CAAe,SAAC,CAAS,CAAC;YA8F/C,IAAI,YA7FC,EAAa;gBA8FhB,EAAE,CA7FC,iBAAC,CAAiB,YAAC,EAAa,IAAA,CAAK,CAAC;aA8F1C;iBA7FM;gBA8FL,EAAE,CA7FC,eAAC,CAAe,CA6F3B,EA7F4B,SAAG,CA6F/B,CAAA,EA7FwC,IAAI,CA6F5C,CA7FgD,CAAE,CAAC;aA8F5C;SACF;aA7FM;YA8FL,EAAE,CA7FC,eAAC,CAAe,IAAC,CAAI,CAAC;SA8F1B;KACF;;;;;;IAMH,QAjGG,CAAA,EAAA,EAAA,IAAA,EAiGH,EAjG0C,EAAA,CAAG,SAAC,CAAS,GAAC,CAAG,IAAC,CAAI,CAAC,EAAC;;;;;;IAuGlE,WArGG,CAAA,EAAA,EAAA,IAAA,EAqGH,EArG6C,EAAA,CAAG,SAAC,CAAS,MAAC,CAAM,IAAC,CAAI,CAAC,EAAC;;;;;;;;IA6GxE,QA3GG,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,EA2GH;QACI,IAAI,KA3GC,GAAO,mBAAA,CAAoB,QAAC,EAAS;YA4GxC,EAAE,CA3GC,KAAC,CAAK,WAAC,CA4GN,KAAK,EA3GE,KAAA,EAAO,CAAA,EAAG,KAAC,GAAO,mBAAA,CAAoB,SAAC,CAAS,GAAG,WAAA,GAAc,EAAA,CAAG,CAAC;SA4GjF;aA3GM;YA4GL,EAAE,CA3GC,KAAC,CAAK,KAAC,CAAK,GAAG,KAAA,CAAM;SA4GzB;KACF;;;;;;;IAOH,WAhHG,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA,EAgHH;QACI,IAAI,KAhHC,GAAO,mBAAA,CAAoB,QAAC,EAAS;YAiHxC,EAAE,CAhHC,KAAC,CAAK,cAAC,CAAc,KAAC,CAAK,CAAC;SAiHhC;aAhHM;;;YAmHL,EAAE,CAhHC,KAAC,CAAK,KAAC,CAAK,GAAG,EAAA,CAAG;SAiHtB;KACF;;;;;;;IAOH,WArHG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAqHH;QACI,oBAAoB,CArHC,IAAC,EAAK,UAAA,CAAW,CAAC;QAsHvC,EAAE,CArHC,IAAC,CAAI,GAAG,KAAA,CAAM;KAsHlB;;;;;;IAMH,QAzHG,CAAA,IAAA,EAAA,KAAA,EAyHH,EAzH6C,IAAA,CAAK,SAAC,GAAW,KAAA,CAAM,EAAC;;;;;;;IAgIrE,MA9HG,CAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EA8HH;QAEI,oBAAoB,CA9HC,KAAC,EAAM,UAAA,CAAW,CAAC;QA+HxC,IAAI,OA9HO,MAAA,KAAW,QAAA,EAAU;YA+H9B,QA9HmB,IAAC,CAAI,YAAC,CAAY,sBAAC,CA+HlC,MAAM,EA9HE,KAAA,EAAO,sBAAA,CAAuB,QAAC,CAAQ,CAAC,EAAC;SA+HtD;QACD,SA9HmB,IAAC,CAAI,YAAC,CAAY,gBAAC,CA+H3B,MAAM,EA9HE,KAAA,EAAO,sBAAA,CAAuB,QAAC,CAAQ,CAAC,GAAc;KA+H1E;CACF;AAED,AAUA,MAhJM,WAAA,GAAc,GAAA,CAAI,UAAC,CAAU,CAAC,CAAC,CAAC;;;;;;AAsJtC,SAAA,oBAAA,CA7IC,IAAA,EAAA,QAAA,EA6ID;IACE,IAAI,IArJC,CAAI,UAAC,CAAU,CAAC,CAAC,KAAK,WAAA,EAAa;QAsJtC,MArJM,IAAI,KAAA,CAsJN,CADR,oBAAA,EAC+B,QArJC,CAoJhC,CAAA,EApJwC,IAAI,CAoJ5C,gGAAA,CApJgD,CAAkG,CAAC;KAsJhJ;CACF;AACD,MAAA,iCA5IC,SAAA,mBAAA,CA4ID;;;;;;IAQA,WAAA,CACM,YAhJc,EAAc,gBAAkB,EACtC,SAAW,EA8IzB;QAGI,KAAK,CAAC,YAAY,CAAC,CAAC;QAjJV,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;QAkJrB,MAAM,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACjE,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KACjD;;;;;IAKH,WAnJG,CAAA,OAAA,EAmJH,EAnJ8B,KAAA,CAAM,YAAC,CAAY,OAAC,EAAQ,IAAA,CAAK,QAAC,EAAS,EAAA,CAAG,CAAC,EAAC;;;;;;IAyJ9E,aAvJG,CAAA,MAAA,EAAA,IAAA,EAuJH;QACI,uBAvJM,EAAA,GAAK,KAAA,CAAM,aAAC,CAAa,MAAC,EAAO,IAAA,CAAK,CAAC;QAwJ7C,KAAK,CAvJC,YAAC,CAAY,EAAC,EAAG,IAAA,CAAK,WAAC,EAAY,EAAA,CAAG,CAAC;QAwJ7C,OAvJO,EAAA,CAAG;KAwJX;CACF;AAED,AASA,MAAA,iBAhKC,SAAA,mBAAA,CAgKD;;;;;;;IAQA,WAAA,CACM,YArKc,EAAsB,gBAAkB,EAC9C,MAAQ,EAAa,SAAW,EAmK9C;QAKI,KAAK,CAAC,YAAY,CAAC,CAAC;QAzKkB,IAA1C,CAAA,gBAA0C,GAAA,gBAAA,CAAkB;QAC9C,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAAa,IAAnC,CAAA,SAAmC,GAAA,SAAA,CAAW;QAyK1C,IAAI,CAAC,UAAU,GAAI,MAAc,CAAC,gBAAgB,EAAE,CAAC;QACrD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAChD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACtC;KACF;;;;;IArKA,gBAAA,CAAA,IAAA,EAAH,EAA6C,OAAO,IAAA,KAAS,IAAA,CAAK,MAAC,GAAQ,IAAA,CAAK,UAAC,GAAY,IAAA,CAAK,EAAC;;;;IA8KnG,OA5KG,GA4KH,EA5Kc,IAAA,CAAK,gBAAC,CAAgB,UAAC,CAAU,IAAC,CAAI,UAAC,CAAU,CAAC,EAAC;;;;;;IAkLjE,WAhLG,CAAA,MAAA,EAAA,QAAA,EAgLH;QACI,OAhLO,KAAA,CAAM,WAAC,CAAW,IAAC,CAAI,gBAAC,CAAgB,MAAC,CAAM,EAAE,QAAA,CAAS,CAAC;KAiLnE;;;;;;;IAOH,YAtLG,CAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAsLH;QACI,OAtLO,KAAA,CAAM,YAAC,CAAY,IAAC,CAAI,gBAAC,CAAgB,MAAC,CAAM,EAAE,QAAA,EAAU,QAAA,CAAS,CAAC;KAuL9E;;;;;;IAMH,WA3LG,CAAA,MAAA,EAAA,QAAA,EA2LH;QACI,OA3LO,KAAA,CAAM,WAAC,CAAW,IAAC,CAAI,gBAAC,CAAgB,MAAC,CAAM,EAAE,QAAA,CAAS,CAAC;KA4LnE;;;;;IAKH,UA/LG,CAAA,IAAA,EA+LH;QACI,OA/LO,IAAA,CAAK,gBAAC,CAAgB,KAAC,CAAK,UAAC,CAAU,IAAC,CAAI,gBAAC,CAAgB,IAAC,CAAI,CAAC,CAAC,CAAC;KAgM7E;CACF,AAED,AASC;;AD9eD;;;;;;;AASA,AAEA,AAEA,AACA,AAAA,MAAA,eACC,SAAA,kBAAA,CADD;;;;IAIA,WAAA,CAFe,GAAK,EAEpB,EAFwB,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA;;;;;IAOxB,QAHG,CAAA,SAAA,EAGH,EAHyC,OAAO,IAAA,CAAK,EAAC;;;;;;;IAUtD,gBARG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAQH;QACI,OAAO,CARC,gBAAC,CAAgB,SAAC,oBAAU,OAAW,GAAK,KAAA,CAAM,CAAC;QAS3D,OARO,MAAM,OAAA,CAAQ,mBAAC,CAAmB,SAAC,oBAAU,OAAW,GAAK,KAAA,CAAM,CAAC;KAE7E;;AAAK,eAAP,CAAA,UAAO,GAAoC;IAS3C,EARE,IAAA,EAAM,UAAA,EAAW;CASlB,CARC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IAWD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAQC;;ADrDD;;;;;;;AASA,AAEA,AAEA,AAEA,MADM,WAAA,GAAc;;IAGlB,KAAK,EADE,IAAA;IAEP,UAAU,EADE,IAAA;IAEZ,SAAS,EADE,IAAA;IAEX,QAAQ,EADE,IAAA;IAEV,WAAW,EADE,IAAA;IAEb,SAAS,EADE,IAAA;IAEX,UAAS,EADG,IAAA;IAEZ,OAAO,EADE,IAAA;IAET,SAAS,EADE,IAAA;;IAGX,OAAO,EADE,IAAA;IAET,YAAY,EADE,IAAA;IAEd,WAAW,EADE,IAAA;IAEb,UAAU,EADE,IAAA;IAEZ,aAAW,EADI,IAAA;IAEf,SAAS,EADE,IAAA;IAEX,UAAU,EADE,IAAA;;IAGZ,OAAO,EADE,IAAA;IAET,SAAS,EADE,IAAA;;IAGX,QAAQ,EADE,IAAA;IAEV,aAAa,EADE,IAAA;IAEf,YAAY,EADE,IAAA;IAEd,WAAW,EADE,IAAA;IAEb,cAAc,EADE,IAAA;;IAGhB,OAAO,EADE,IAAA;IAET,WAAW,EADE,IAAA;IAEb,YAAW,EADG,IAAA;IAEd,SAAS,EADE,IAAA;IAEX,WAAW,EADE,IAAA;;IAGb,KAAC,EADM,IAAA;CAER,CADC;;;;;;;AAQF,AAAC,MAAA,qBAAA,GAAA,IAAA,cAAA,CAAA,qBAAA,CAAA,CAAA;;;;AASD,AAAA,MAAA,mBAAA,CAAA;IAAA,WAAA,GAAA;QACE,IAAF,CAAA,MAAQ,GAEa,EAAA,CAAG;QAAtB,IAAF,CAAA,SAAW,GAE4B,EAAA,CAAG;KAuBzC;;;;;IApBD,WADG,CAAA,OAAA,EACH;QACI,uBADM,EAAA,GAAK,IAAI,MAAA,CAAO,OAAC,CAAO,CAAC;QAG/B,EAAE,CADC,GAAC,CAAG,OAAC,CAAO,CAAC,GAAC,CAAG,EAAC,MAAC,EAAO,IAAA,EAAK,CAAC,CAAC;QAEpC,EAAE,CADC,GAAC,CAAG,QAAC,CAAQ,CAAC,GAAC,CAAG,EAAC,MAAC,EAAO,IAAA,EAAK,CAAC,CAAC;QAGrC,KAAK,uBADM,SAAA,IAAa,IAAA,CAAK,SAAC,EAAU;YAEtC,EAAE,CADC,GAAC,CAAG,SAAC,CAAS,CAAC,GAAC,CAAG,IAAC,CAAI,SAAC,CAAS,SAAC,CAAS,CAAC,CAAC;SAElD;QAED,OADO,EAAA,CAAG;KAEX;;AAAI,mBAAP,CAAA,UAAO,GAAoC;IAE3C,EADE,IAAA,EAAM,UAAA,EAAW;CAElB,CADC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA,EAIA,CAAC;AAGF,AAcA,AAAA,MAAA,oBAhBC,SAAA,kBAAA,CAgBD;;;;;IAKA,WAAA,CACO,GApBK,EACG,OAAS,EAkBxB;QAGI,KAAK,CAAC,GAAG,CAAC,CAAC;QArBA,IAAf,CAAA,OAAe,GAAA,OAAA,CAAS;KAsBrB;;;;;IAKH,QAvBG,CAAA,SAAA,EAuBH;QACI,IAAI,CAvBC,WAAC,CAAW,cAAC,CAAc,SAAC,CAAS,WAAC,EAAW,CAAE,IAAI,CAAA,IAAE,CAAI,aAAC,CAAa,SAAC,CAAS,EAAE;YAwB1F,OAvBO,KAAA,CAAM;SAwBd;QAED,IAAI,CAvBC,EAAC,MAAU,GAAK,MAAC,EAAO;YAwB3B,MAvBM,IAAI,KAAA,CAAM,CAuBtB,sCAAA,EAvBuB,SAAyC,CAuBhE,MAAA,CAvByE,CAAQ,CAAC;SAwB7E;QAED,OAvBO,IAAA,CAAK;KAwBb;;;;;;;IAOH,gBA5BG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EA4BH;QACI,uBA5BM,IAAA,GAAO,IAAA,CAAK,OAAC,CAAO,OAAC,EAAO,CAAE;QA6BpC,SAAS,GA5BG,SAAA,CAAU,WAAC,EAAW,CAAE;QA8BpC,OA5BO,IAAA,CAAK,iBAAC,CAAiB,MA4BlC;;YAEM,uBA5BM,EAAA,GAAK,IAAA,CAAK,OAAC,CAAO,WAAC,CAAW,OAAC,CAAO,CAAC;YA6B7C,uBA5BM,QAAA,GAAW,UAAA,QAAmB,EA4B1C;gBACQ,IAAI,CA5BC,UAAC,CAAU,YA4BxB,EA5BqC,OAAA,CAAQ,QAAC,CAAQ,CAAC,EAAC,CAAE,CAAC;aA6BpD,CA5BC;YA6BF,EAAE,CA5BC,EAAC,CAAE,SAAC,EAAU,QAAA,CAAS,CAAC;YA6B3B,OA5BO,MAAM,EAAA,CAAG,GAAC,CAAG,SAAC,EAAU,QAAA,CAAS,CAAC;SA6B1C,CA5BC,CAAC;KA6BJ;;;;;IAKH,aA/BG,CAAA,SAAA,EA+BH,EA/B8C,OAAO,IAAA,CAAK,OAAC,CAAO,MAAC,CAAM,OAAC,CAAO,SAAC,CAAS,GAAG,CAAA,CAAE,CAAC,EAAC;;AAC3F,oBAAP,CAAA,UAAO,GAAoC;IAgC3C,EA/BE,IAAA,EAAM,UAAA,EAAW;CAgClB,CA/BC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IAkCD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;IACvE,EAAC,IAAI,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,qBAAqB,EAAG,EAAE,EAAG,EAAC;CAC7F,CAAC,AAGF,AAUC;;ADvLD;;;;;;;AASA,AAEA,AACA,AAEA,AAEA,MADM,aAAA,GAAgB,CAAA,KAAE,EAAM,SAAA,EAAW,MAAA,EAAQ,OAAA,CAAQ,CAAC;AAE1D,MADM,oBAAA,GAA2E;IAE/E,KAAK,EADE,CAAA,KAAQ,KAAkB,KAAA,CAAM,MAAC;IAExC,SAAQ,EADG,CAAA,KAAQ,KAAkB,KAAA,CAAM,OAAC;IAE5C,MAAM,EADE,CAAA,KAAQ,KAAkB,KAAA,CAAM,OAAC;IAEzC,OAAA,EADS,CAAA,KAAQ,KAAkB,KAAA,CAAM,QAAC;CAE3C,CADC;;;;AAKF,AAAA,MAAA,eACC,SAAA,kBAAA,CADD;;;;IAIA,WAAA,CAFe,GAAK,EAEpB,EAFwB,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA;;;;;IAOxB,QALG,CAAA,SAAA,EAKH,EALyC,OAAO,eAAA,CAAgB,cAAC,CAAc,SAAC,CAAS,IAAI,IAAA,CAAK,EAAC;;;;;;;IAYnG,gBAVG,CAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAUH;QACI,uBAVM,WAAA,KAAc,eAAA,CAAgB,cAAC,CAAc,SAAC,CAAS,EAAA,CAAG;QAYhE,uBAVM,cAAA,GAWF,eAAe,CAVC,aAAC,CAAa,WAAC,CAAW,SAAC,CAAS,EAAE,OAAA,EAAS,IAAA,CAAK,OAAC,CAAO,OAAC,EAAO,CAAE,CAAC;QAY3F,OAVO,IAAA,CAAK,OAAC,CAAO,OAAC,EAAO,CAAE,iBAAC,CAAiB,MAUpD;YACM,OAVO,MAAA,EAAO,CAAE,WAAC,CAAW,OAAC,EAAQ,WAAA,CAAY,cAAC,CAAc,EAAE,cAAA,CAAe,CAAC;SAWnF,CAVC,CAAC;KAWJ;;;;;IAKH,OAbG,cAAA,CAAA,SAAA,EAaH;QACI,uBAbM,KAAA,GAAkB,SAAA,CAAU,WAAC,EAAW,CAAE,KAAC,CAAK,GAAC,CAAG,CAAC;QAe3D,uBAbM,YAAA,GAAe,KAAA,CAAM,KAAC,EAAK,CAAE;QAcnC,IAAI,CAbC,KAAC,CAAK,MAAC,KAAU,CAAA,KAAM,EAAE,YAAC,KAAgB,SAAA,IAAa,YAAA,KAAiB,OAAA,CAAQ,EAAE;YAcrF,OAbO,IAAA,CAAK;SAcb;QAED,uBAbM,GAAA,GAAM,eAAA,CAAgB,aAAC,oBAAa,KAAC,CAAK,GAAC,EAAG,GAAI,CAAC;QAezD,qBAbI,OAAA,GAAU,EAAA,CAAG;QAcjB,aAAa,CAbC,OAAC,CAAO,YAAC,IAa3B;YACM,uBAbM,KAAA,GAAgB,KAAA,CAAM,OAAC,CAAO,YAAC,CAAY,CAAC;YAclD,IAAI,KAbC,GAAO,CAAA,CAAE,EAAE;gBAcd,KAAK,CAbC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;gBAcvB,OAAO,IAbI,YAAA,GAAe,GAAA,CAAI;aAc/B;SACF,CAbC,CAAC;QAcH,OAAO,IAbI,GAAA,CAAI;QAef,IAAI,KAbC,CAAK,MAAC,IAAS,CAAA,IAAK,GAAA,CAAI,MAAC,KAAU,CAAA,EAAG;;YAezC,OAbO,IAAA,CAAK;SAcb;QAED,uBAbM,MAAA,GAAgC,EAAA,CAAG;QAczC,MAAM,CAbC,cAAC,CAAc,GAAG,YAAA,CAAa;QActC,MAAM,CAbC,SAAC,CAAS,GAAG,OAAA,CAAQ;QAc5B,OAbO,MAAA,CAAO;KAcf;;;;;IAKH,OAhBG,eAAA,CAAA,KAAA,EAgBH;QACI,qBAhBI,OAAA,GAAU,EAAA,CAAG;QAiBjB,qBAhBI,GAAA,GAAM,MAAA,EAAO,CAAE,WAAC,CAAW,KAAC,CAAK,CAAC;QAiBtC,GAAG,GAhBG,GAAA,CAAI,WAAC,EAAW,CAAE;QAiBxB,IAAI,GAhBC,KAAO,GAAA,EAAK;YAiBf,GAAG,GAhBG,OAAA,CAAQ;SAiBf;aAhBM,IAAA,GAAK,KAAO,GAAA,EAAK;YAiBtB,GAAG,GAhBG,KAAA,CAAM;SAiBb;QACD,aAAa,CAhBC,OAAC,CAAO,YAAC,IAgB3B;YACM,IAAI,YAhBC,IAAe,GAAA,EAAK;gBAiBvB,uBAhBM,cAAA,GAAiB,oBAAA,CAAqB,YAAC,CAAY,CAAC;gBAiB1D,IAAI,cAhBC,CAAc,KAAC,CAAK,EAAE;oBAiBzB,OAAO,IAhBI,YAAA,GAAe,GAAA,CAAI;iBAiB/B;aACF;SACF,CAhBC,CAAC;QAiBH,OAAO,IAhBI,GAAA,CAAI;QAiBf,OAhBO,OAAA,CAAQ;KAiBhB;;;;;;;IAOH,OArBG,aAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAAA,EAqBH;QACI,OArBO,CAAA,KAAQ,uBAqBnB;YACM,IAAI,eArBC,CAAe,eAAC,CAAe,KAAC,CAAK,KAAK,OAAA,EAAS;gBAsBtD,IAAI,CArBC,UAAC,CAAU,MAAM,OAAA,CAAQ,KAAC,CAAK,CAAC,CAAC;aAe5C;SAQG,CArBC;KAsBH;;;;;;IAMH,OAxBG,aAAA,CAAA,OAAA,EAwBH;;QAEI,QAAQ,OAxBC;YAyBP,KAxBK,KAAA;gBAyBH,OAxBO,QAAA,CAAS;YAyBlB;gBACE,OAxBO,OAAA,CAAQ;SAyBlB;KACF;;AAvBI,eAAP,CAAA,UAAO,GAAoC;IAyB3C,EAxBE,IAAA,EAAM,UAAA,EAAW;CAyBlB,CAxBC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IA2BD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC,AAGF,AAQC;;AD5JD;;;;;;;AASA,AAEA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,MACC,gBAAA,GAAA,6DAAA,CAAA;AACD,AAEA;;;AAGA,MAAC,gBAAA,GACG,sIAAsI,CAAC;;;;;AAK3I,AAAA,SAAA,WAAA,CAHC,GAAA,EAGD;IACE,GAAG,GAHG,MAAA,CAAO,GAAC,CAAG,CAAC;IAIlB,IAAI,GAHC,CAAG,KAAC,CAAK,gBAAC,CAAgB,IAAI,GAAA,CAAI,KAAC,CAAK,gBAAC,CAAgB;QAAE,OAAO,GAAA,CAAI;IAK3E,IAAI,SAHC,EAAS,EAAG;QAIf,MAAM,EAHC,CAAE,GAAC,CAAG,CAGjB,qCAAA,EAHkB,GAAwC,CAG1D,kCAAA,CAH6D,CAAoC,CAAC;KAI/F;IAED,OAHO,SAAA,GAAY,GAAA,CAAI;CAIxB;;;;;AAKD,AAAA,SAAA,cAAA,CANC,MAAA,EAMD;IACE,MAAM,GANG,MAAA,CAAO,MAAC,CAAM,CAAC;IAOxB,OANO,MAAA,CAAO,KAAC,CAAK,GAAC,CAAG,CAAC,GAAC,CAAG,CAAC,MAAC,KAAU,WAAA,CAAY,MAAC,CAAM,IAAC,EAAI,CAAE,CAAC,CAAC,IAAC,CAAI,IAAC,CAAI,CAAC;CAOjF;;ADpED;;;;;;;AASA,AAEA,AAEA,AACA;;;AAGA,IAFC,YAAA,GAAA,IAAA,CAAA;;;;AAMD,IAJC,GAAA,GAAA,IAAA,CAAA;;;;;AASD,SAAA,eAAA,GAAA;IACE,IAAI,YANC;QAAa,OAAO,YAAA,CAAa;IAOtC,GAAG,GANG,MAAA,EAAO,CAAE;;IASf,uBANM,UAAA,GAAa,GAAA,CAAI,aAAC,CAAa,UAAC,CAAU,CAAC;IAOjD,IAAI,SANC,IAAY,UAAA;QAAY,OAAO,UAAA,CAAW;IAQ/C,uBANM,GAAA,GAAM,GAAA,CAAI,kBAAC,EAAkB,CAAE;IAOrC,YAAY,GANG,GAAA,CAAI,aAAC,CAAa,GAAC,EAAI,MAAA,CAAO,CAAC;IAO9C,IAAI,YANC,IAAe,IAAA,EAAM;;;QASxB,uBANM,IAAA,GAAO,GAAA,CAAI,aAAC,CAAa,MAAC,EAAO,GAAA,CAAI,CAAC;QAO5C,YAAY,GANG,GAAA,CAAI,aAAC,CAAa,MAAC,EAAO,GAAA,CAAI,CAAC;QAO9C,GAAG,CANC,WAAC,CAAW,IAAC,EAAK,YAAA,CAAa,CAAC;QAOpC,GAAG,CANC,WAAC,CAAW,GAAC,EAAI,IAAA,CAAK,CAAC;KAO5B;IACD,OANO,YAAA,CAAa;CAOrB;;;;;AAKD,SAAA,MAAA,CATC,IAAA,EASD;IACE,uBATM,GAAA,GAA8B,EAAA,CAAG;IAUvC,KAAK,uBATM,CAAA,IAAK,IAAA,CAAK,KAAC,CAAK,GAAC,CAAG;QAAE,GAAA,CAAI,CAAC,CAAC,GAAG,IAAA,CAAK;IAU/C,OATO,GAAA,CAAI;CAUZ;;;;;AAKD,SAAA,KAAA,CAZC,GAAA,IAAA,EAYD;IACE,uBAZM,GAAA,GAA8B,EAAA,CAAG;IAavC,KAAK,uBAZM,CAAA,IAAK,IAAA,EAAM;QAapB,KAAK,uBAZM,CAAA,IAAK,CAAA,EAAG;YAajB,IAAI,CAZC,CAAC,cAAC,CAAc,CAAC,CAAC;gBAAE,GAAA,CAAI,CAAC,CAAC,GAAG,IAAA,CAAK;SAaxC;KACF;IACD,OAZO,GAAA,CAAI;CAaZ;;;;;;AAQD,MAZM,aAAA,GAAgB,MAAA,CAAO,wBAAC,CAAwB,CAAC;;;AAgBvD,MAZM,+BAAA,GAAkC,MAAA,CAAO,gDAAC,CAAgD,CAAC;AAajG,MAZM,gCAAA,GAAmC,MAAA,CAAO,OAAC,CAAO,CAAC;AAazD,MAZM,yBAAA,GAaF,KAAK,CAZC,gCAAC,EAAiC,+BAAA,CAAgC,CAAC;;AAe7E,MAZM,cAAA,GAAiB,KAAA,CAanB,+BAA+B,EAC/B,MAAM,CACF,kBAAkB;IAClB,wGAAwG;IACxG,2EAA2E,CAZC,CAAC,CAAC;;AAetF,MAZM,eAAA,GAAkB,KAAA,CAapB,gCAAgC,EAChC,MAAM,CACF,yBAAyB;IACzB,+FAA+F;IAC/F,wEAAwE,CAZC,CAAC,CAAC;AAcnF,MAZM,cAAA,GAaF,KAAK,CAZC,aAAC,EAAc,cAAA,EAAgB,eAAA,EAAiB,yBAAA,CAA0B,CAAC;;AAerF,MAZM,SAAA,GAAY,MAAA,CAAO,8DAAC,CAA8D,CAAC;;AAezF,MAZM,YAAA,GAAe,MAAA,CAAO,QAAC,CAAQ,CAAC;AActC,MAZM,UAAA,GAAa,MAAA,CAaf,+GAA+G;IAC/G,mGAAmG;IACnG,gIAAgI;IAChI,0GAA0G;IAC1G,2BAA2B,CAZC,CAAC;;;;;;;AAsBjC,MAZM,WAAA,GAAc,KAAA,CAAM,SAAC,EAAU,YAAA,EAAc,UAAA,CAAW,CAAC;;;;;AAiB/D,MAAA,wBAAA,CAAA;IAAA,WAAA,GAAA;QARS,IAAT,CAAA,kBAAS,GAAqB,KAAA,CAAM;QAC1B,IAAV,CAAA,GAAU,GAAgB,EAAA,CAAG;KAgG5B;;;;;IAlFD,gBAZG,CAAA,EAAA,EAYH;;;;QAII,qBAZI,OAAA,KAAgB,EAAA,CAAG,UAAC,EAAA,CAAY;QAapC,OAAO,OAZC,EAAQ;YAad,IAAI,GAZC,CAAG,aAAC,CAAa,OAAC,CAAO,EAAE;gBAa9B,IAAI,CAZC,YAAC,mBAAY,OAAW,EAAQ,CAAC;aAavC;iBAZM,IAAA,GAAK,CAAG,UAAC,CAAU,OAAC,CAAO,EAAE;gBAalC,IAAI,CAZC,KAAC,oBAAK,GAAC,CAAG,SAAC,CAAS,OAAC,CAAO,GAAG,CAAC;aAatC;iBAZM;;gBAcL,IAAI,CAZC,kBAAC,GAAoB,IAAA,CAAK;aAahC;YACD,IAAI,GAZC,CAAG,UAAC,CAAU,OAAC,CAAO,EAAE;gBAa3B,OAAO,KAZG,GAAA,CAAI,UAAC,CAAU,OAAC,CAAO,EAAA,CAAG;gBAapC,SAAS;aACV;YACD,OAAO,OAZC,EAAQ;;gBAcd,IAAI,GAZC,CAAG,aAAC,CAAa,OAAC,CAAO,EAAE;oBAa9B,IAAI,CAZC,UAAC,mBAAU,OAAW,EAAQ,CAAC;iBAarC;gBAED,qBAZI,IAAA,GAAO,qBAAA,CAAsB,OAAC,qBAAQ,GAAA,CAAI,WAAC,CAAW,OAAC,CAAO,GAAG,CAAC;gBActE,IAAI,IAZC,EAAK;oBAaR,OAAO,GAZG,IAAA,CAAK;oBAaf,MAAM;iBACP;gBAED,OAAO,GAZG,qBAAA,CAAsB,OAAC,qBAAQ,GAAA,CAAI,aAAC,CAAa,OAAC,CAAO,GAAG,CAAC;aAaxE;SACF;QACD,OAZO,IAAA,CAAK,GAAC,CAAG,IAAC,CAAI,EAAC,CAAE,CAAC;KAa1B;;;;;IAVA,YAAA,CAAA,OAAA,EAAH;QAgBI,uBAfM,OAAA,GAAU,GAAA,CAAI,QAAC,CAAQ,OAAC,CAAO,CAAC,WAAC,EAAW,CAAE;QAgBpD,IAAI,CAfC,cAAC,CAAc,cAAC,CAAc,OAAC,CAAO,EAAE;YAgB3C,IAAI,CAfC,kBAAC,GAAoB,IAAA,CAAK;YAgB/B,OAAO;SACR;QACD,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,GAAC,CAAG,CAAC;QAgBnB,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,OAAC,CAAO,CAAC;QAgBvB,GAAG,CAfC,YAAC,CAAY,OAAC,CAAO,CAAC,OAAC,CAAO,CAAC,KAAO,EAAQ,QAAU,KAehE;YACM,uBAfM,KAAA,GAAQ,QAAA,CAAS,WAAC,EAAW,CAAE;YAgBrC,IAAI,CAfC,WAAC,CAAW,cAAC,CAAc,KAAC,CAAK,EAAE;gBAgBtC,IAAI,CAfC,kBAAC,GAAoB,IAAA,CAAK;gBAgB/B,OAAO;aACR;;YAED,IAAI,SAfC,CAAS,KAAC,CAAK;gBAAE,KAAA,GAAQ,WAAA,CAAY,KAAC,CAAK,CAAC;YAgBjD,IAAI,YAfC,CAAY,KAAC,CAAK;gBAAE,KAAA,GAAQ,cAAA,CAAe,KAAC,CAAK,CAAC;YAgBvD,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,GAAC,CAAG,CAAC;YAgBnB,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,QAAC,CAAQ,CAAC;YAgBxB,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,IAAC,CAAI,CAAC;YAgBpB,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,cAAC,CAAc,KAAC,CAAK,CAAC,CAAC;YAgBrC,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,GAAC,CAAG,CAAC;SAgBpB,CAfC,CAAC;QAgBH,IAAI,CAfC,GAAC,CAAG,IAAC,CAAI,GAAC,CAAG,CAAC;KAgBpB;;;;;IAbA,UAAA,CAAA,OAAA,EAAH;QAmBI,uBAlBM,OAAA,GAAU,GAAA,CAAI,QAAC,CAAQ,OAAC,CAAO,CAAC,WAAC,EAAW,CAAE;QAmBpD,IAAI,cAlBC,CAAc,cAAC,CAAc,OAAC,CAAO,IAAI,CAAA,aAAE,CAAa,cAAC,CAAc,OAAC,CAAO,EAAE;YAmBpF,IAAI,CAlBC,GAAC,CAAG,IAAC,CAAI,IAAC,CAAI,CAAC;YAmBpB,IAAI,CAlBC,GAAC,CAAG,IAAC,CAAI,OAAC,CAAO,CAAC;YAmBvB,IAAI,CAlBC,GAAC,CAAG,IAAC,CAAI,GAAC,CAAG,CAAC;SAmBpB;KACF;;;;;IAhBA,KAAA,CAAA,KAAA,EAAH,EAAiC,IAAA,CAAK,GAAC,CAAG,IAAC,CAAI,cAAC,CAAc,KAAC,CAAK,CAAC,CAAC,EAAC;CAsBtE;AAED,AAOA;;;;;AAKA,SAAA,qBAAA,CAjCC,IAAA,EAAA,QAAA,EAiCD;IACE,IAAI,QAjCC,IAAW,GAAA,CAAI,QAAC,CAAQ,IAAC,EAAK,QAAA,CAAS,EAAE;QAkC5C,MAjCM,IAAI,KAAA,CAkCN,CADR,0DAAA,EACqE,GAjCC,CAAG,YAAC,CAAY,IAAC,CAAI,CAgC3F,CAhC4F,CAAE,CAAC;KAkC5F;IACD,OAjCO,QAAA,CAAS;CAkCjB;;AAGD,MAjCM,qBAAA,GAAwB,iCAAA,CAAkC;;AAmChE,MAjCM,uBAAA,GAA0B,eAAA,CAAgB;;;;;;;;AAyChD,SAAA,cAAA,CAhCC,KAAA,EAgCD;IACE,OAhCO,KAAA,CAAM,OAAC,CAAO,IAAC,EAAK,OAAA,CAAQ;SAiC9B,OAhCC,CAiCE,qBAAqB,EACrB,UAAS,KAhCO,EA8B1B;QAGY,uBAhCM,EAAA,GAAK,KAAA,CAAM,UAAC,CAAU,CAAC,CAAC,CAAC;QAiC/B,uBAhCM,GAAA,GAAM,KAAA,CAAM,UAAC,CAAU,CAAC,CAAC,CAAC;QAiChC,OAhCO,IAAA,IAAO,CAAE,CAAC,EAAC,GAAI,MAAA,IAAU,KAAA,KAAS,GAAE,GAAK,MAAA,CAAO,GAAG,OAAA,CAAQ,GAAG,GAAA,CAAI;KAiC1E,CAhCC;SAiCL,OAhCC,CAiCE,uBAAuB,EACvB,UAAS,KAhCO,EA8B1B,EA9BoC,OAAO,IAAA,GAAO,KAAA,CAAM,UAAC,CAAU,CAAC,CAAC,GAAG,GAAA,CAAI,EAAC,CAAE;SAiCxE,OAhCC,CAAO,IAAC,EAAK,MAAA,CAAO;SAiCrB,OAhCC,CAAO,IAAC,EAAK,MAAA,CAAO,CAAC;CAiC5B;;;;;;;;;;AAUD,SAAA,kBAAA,CAjCC,EAAA,EAiCD;IACE,GAAG,CAjCC,YAAC,CAAY,EAAC,CAAE,CAAC,OAAC,CAAO,CAAC,CAAC,EAAE,QAAA,KAiCnC;QACI,IAAI,QAjCC,KAAY,WAAA,IAAe,QAAA,CAAS,OAAC,CAAO,MAAC,CAAM,KAAK,CAAA,EAAG;YAkC9D,GAAG,CAjCC,eAAC,CAAe,EAAC,EAAG,QAAA,CAAS,CAAC;SAkCnC;KACF,CAjCC,CAAC;IAkCH,KAAK,uBAjCM,CAAA,IAAK,GAAA,CAAI,gBAAC,CAAgB,EAAC,CAAE,EAAE;QAkCxC,IAAI,GAjCC,CAAG,aAAC,CAAa,CAAC,CAAC;YAAE,kBAAA,mBAAmB,CAAK,EAAQ,CAAC;KAkC5D;CACF;;;;;;;;AAQD,AAAA,SAAA,YAAA,CAnCC,UAAA,EAAA,eAAA,EAmCD;IACE,IAnCI;QAoCF,uBAnCM,WAAA,GAAc,eAAA,EAAgB,CAAE;;QAqCtC,qBAnCI,UAAA,GAAa,eAAA,GAAkB,MAAA,CAAO,eAAC,CAAe,GAAG,EAAA,CAAG;;;QAuChE,qBAnCI,YAAA,GAAe,CAAA,CAAE;QAoCrB,qBAnCI,UAAA,GAAa,UAAA,CAAW;QAqC5B,GAnCG;YAoCD,IAAI,YAnCC,KAAgB,CAAA,EAAG;gBAoCtB,MAnCM,IAAI,KAAA,CAAM,uDAAC,CAAuD,CAAC;aAoC1E;YACD,YAAY,EAnCC,CAAE;YAqCf,UAAU,GAnCG,UAAA,CAAW;YAoCxB,GAAG,CAnCC,YAAC,CAAY,WAAC,EAAY,UAAA,CAAW,CAAC;YAoC1C,IAAI,UAnCC,CAAU,YAAC,EAAa;;gBAqC3B,kBAAkB,CAnCC,WAAC,CAAW,CAAC;aAoCjC;YACD,UAAU,GAnCG,GAAA,CAAI,YAAC,CAAY,WAAC,CAAW,CAAC;SAoC5C,QAnCC,UAAQ,KAAc,UAAA,EAAW;QAqCnC,uBAnCM,SAAA,GAAY,IAAI,wBAAA,EAAyB,CAAE;QAoCjD,uBAnCM,QAAA,GAAW,SAAA,CAAU,gBAAC,CAAgB,GAAC,CAAG,kBAAC,CAAkB,WAAC,CAAW,IAAI,WAAA,CAAY,CAAC;;QAsChG,uBAnCM,MAAA,GAAS,GAAA,CAAI,kBAAC,CAAkB,WAAC,CAAW,IAAI,WAAA,CAAY;QAoClE,KAAK,uBAnCM,KAAA,IAAS,GAAA,CAAI,gBAAC,CAAgB,MAAC,CAAM,EAAE;YAoChD,GAAG,CAnCC,WAAC,CAAW,MAAC,EAAO,KAAA,CAAM,CAAC;SAoChC;QAED,IAAI,SAnCC,EAAS,IAAK,SAAA,CAAU,kBAAC,EAAmB;YAoC/C,GAAG,CAnCC,GAAC,CAAG,mFAAC,CAAmF,CAAC;SAoC9F;QAED,OAnCO,QAAA,CAAS;KAoCjB;IAnCC,OAAA,CAAQ,EAAE;;QAqCV,YAAY,GAnCG,IAAA,CAAK;QAoCpB,MAnCM,CAAA,CAAE;KAoCT;CACF;;ADjVD;;;;;;;AASA,AAEA,AAEA,AACA;;;;;;;;;;;;;;AAcA,MACC,MAAA,GAAA,yBAAA,CAAA;AAAD,MACM,kBAAA,GAAqB,+DAAA,CAAgE;AAA3F,MACM,SAAA,GAAY,eAAA,CAAgB;AAAlC,MACM,SAAA,GAAY,2CAAA,CAA4C;AAA9D,MACM,QAAA,GAAW,eAAA,CAAgB;AAAjC,MACM,OAAA,GAAU,0BAAA,CAA2B;AAA3C,MACM,gBAAA,GAAmB,IAAI,MAAA,CAAzB,CADJ,EAAA,EACS,MACC,CAFV,CAAA,CAEgB;IAAR,CAAR,GAAA,EAAc,kBACC,CADf,CAAA,EACiC,SAAI,CADrC,CAAA,EAC8C,SAAI,CADlD,CAAA,EAC2D,QAAI,CAD/D,CAAA,CACuE;IAA/D,CAAR,EAAW,OACC,CADZ,EAAA,CACmB,EAAf,GAAG,CACC,CAAC;;;;;;;;;;;;;;;;;;;AAkBT,MAEC,MAAA,GAAA,kBAAA,CAAA;;;;;;;;;;;AASD,SAAA,iBAAA,CACC,KAAA,EADD;IACE,qBACI,aAAA,GAAgB,IAAA,CAAK;IAAzB,qBACI,aAAA,GAAgB,IAAA,CAAK;IAAzB,KAAK,qBACI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,KAAA,CAAM,MAAC,EAAO,CAAA,EAAE,EAAG;QAArC,uBACM,CAAA,GAAI,KAAA,CAAM,MAAC,CAAM,CAAC,CAAC,CAAC;QAA1B,IAAI,CACC,KAAK,IAAA,IAAQ,aAAA,EAAe;YAA/B,aAAa,GACG,CAAA,aAAE,CAAa;SAAhC;aACM,IAAA,CAAK,KAAK,GAAA,IAAO,aAAA,EAAe;YAArC,aAAa,GACG,CAAA,aAAE,CAAa;SAAhC;KACF;IACD,OACO,aAAA,IAAiB,aAAA,CAAc;CAAvC;;;;;;;AAOD,AAAA,SAAA,aAAA,CAAC,KAAA,EAAD;IACE,KAAK,GAAG,MAAA,CAAO,KAAC,CAAK,CAAC,IAAC,EAAI,CAAE;IAC7B,IAAI,CAAC,KAAC;QAAM,OAAO,EAAA,CAAG;;;IAItB,uBAAM,QAAA,GAAW,KAAA,CAAM,KAAC,CAAK,MAAC,CAAM,CAAC;IACrC,IAAI,CAAC,QAAC,IAAW,WAAA,CAAY,QAAC,CAAQ,CAAC,CAAC,CAAC,KAAK,QAAA,CAAS,CAAC,CAAC;QACrD,KAAK,CAAC,KAAC,CAAK,gBAAC,CAAgB,IAAI,iBAAA,CAAkB,KAAC,CAAK,EAAE;QAC7D,OAAO,KAAA,CAAM;KACd;IAED,IAAI,SAAC,EAAS,EAAG;QACf,MAAM,EAAC,CAAE,GAAC,CACN,CADR,uCAAA,EACkD,KAAC,CADnD,mCAAA,CACwD,CAAqC,CAAC;KAC3F;IAED,OAAO,QAAA,CAAS;CACjB;;ADzGD;;;;;;;AASA,AAEA,AAEA,AACA,AACA,AAEA,AA6CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,AAAA,MAAA,YAAA,CAAA;;;;;;;;;;;;;IAaA,QALY,CAAA,OAAA,EAAA,KAAA,EAKZ,GALY;;;;;;;;;;;;IAiBZ,uBAPY,CAAA,KAAA,EAOZ,GAPY;;;;;;;;;;IAiBZ,wBATY,CAAA,KAAA,EASZ,GATY;;;;;;;;;;IAmBZ,yBAXY,CAAA,KAAA,EAWZ,GAXY;;;;;;;;;;;IAsBZ,sBAbY,CAAA,KAAA,EAaZ,GAbY;;;;;;;;;;;IAwBZ,8BAfY,CAAA,KAAA,EAeZ,GAfY;CAgBX;AACD,AAAA,MAAA,gBAZC,SAAA,YAAA,CAYD;;;;IAIA,WAAA,CAfuB,IAAM,EAe7B;QAfiC,KAAA,EAAA,CAAA;QAAV,IAAvB,CAAA,IAAuB,GAAA,IAAA,CAAM;KAAI;;;;;;IAsBjC,QApBG,CAAA,GAAA,EAAA,KAAA,EAoBH;QACI,IAAI,KApBC,IAAQ,IAAA;YAAM,OAAO,IAAA,CAAK;QAqB/B,QAAQ,GApBC;YAqBP,KApBK,eAAA,CAAgB,IAAC;gBAqBpB,QApBO,KAAS,EAAO;YAqBzB,KApBK,eAAA,CAAgB,IAAC;gBAqBpB,IAAI,KApBC,YAAgB,YAAA;oBAAc,OAAO,KAAA,CAAM,qCAAC,CAAqC;gBAqBtF,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,MAAA,CAAO,CAAC;gBAqBtC,OApBO,YAAA,CAAa,IAAC,CAAI,IAAC,EAAK,MAAA,CAAO,KAAC,CAAK,CAAC,CAAC;YAqBhD,KApBK,eAAA,CAAgB,KAAC;gBAqBpB,IAAI,KApBC,YAAgB,aAAA;oBAAe,OAAO,KAAA,CAAM,qCAAC,CAAqC;gBAqBvF,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,OAAA,CAAQ,CAAC;gBAqBvC,OApBO,aAAA,mBAAc,KAAS,EAAO,CAAC;YAqBxC,KApBK,eAAA,CAAgB,MAAC;gBAqBpB,IAAI,KApBC,YAAgB,cAAA;oBAAgB,OAAO,KAAA,CAAM,qCAAC,CAAqC;gBAqBxF,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,QAAA,CAAS,CAAC;gBAqBxC,MApBM,IAAI,KAAA,CAAM,uCAAC,CAAuC,CAAC;YAqB3D,KApBK,eAAA,CAAgB,GAAC;gBAqBpB,IAAI,KApBC,YAAgB,mBAAA,IAAuB,KAAA,YAAiB,WAAA,EAAa;;oBAsBxE,OApBO,KAAA,CAAM,qCAAC,CAAqC;iBAqBpD;gBACD,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,KAAA,CAAM,CAAC;gBAqBrC,OApBO,WAAA,CAAY,MAAC,CAAM,KAAC,CAAK,CAAC,CAAC;YAqBpC,KApBK,eAAA,CAAgB,YAAC;gBAqBpB,IAAI,KApBC,YAAgB,mBAAA,EAAqB;oBAqBxC,OApBO,KAAA,CAAM,qCAAC,CAAqC;iBAqBpD;gBACD,IAAI,CApBC,iBAAC,CAAiB,KAAC,EAAM,aAAA,CAAc,CAAC;gBAqB7C,MApBM,IAAI,KAAA,CAqBN,+EAA+E,CApBC,CAAC;YAqBvF;gBACE,MApBM,IAAI,KAAA,CAAM,CAoBxB,2BAAA,EApByB,GAA8B,CAoBvD,kCAAA,CApB0D,CAAoC,CAAC;SAqB1F;KACF;;;;;;IAlBA,iBAAA,CAAA,KAAA,EAAA,YAAA,EAAH;QAyBI,IAAI,KAxBC,YAAgB,aAAA,EAAe;YAyBlC,MAxBM,IAAI,KAAA,CAyBN,CADV,gBAAA,EAC6B,YAxBC,CAuB9B,QAAA,EAvB0C,KAAW,CAAK,WAAC,EAAW,CAuBtE,CAAA,CAvBwE;gBAyB9D,CAAV,iCAAA,CAA6C,CAxBC,CAAC;SAyB1C;KACF;;;;;IAKH,uBA3BG,CAAA,KAAA,EA2BH,EA3BqD,OAAO,IAAI,YAAA,CAAa,KAAC,CAAK,CAAC,EAAC;;;;;IAgCrF,wBA/BG,CAAA,KAAA,EA+BH,EA/BuD,OAAO,IAAI,aAAA,CAAc,KAAC,CAAK,CAAC,EAAC;;;;;IAoCxF,yBAnCG,CAAA,KAAA,EAmCH,EAnCyD,OAAO,IAAI,cAAA,CAAe,KAAC,CAAK,CAAC,EAAC;;;;;IAwC3F,sBAvCG,CAAA,KAAA,EAuCH,EAvCmD,OAAO,IAAI,WAAA,CAAY,KAAC,CAAK,CAAC,EAAC;;;;;IA4ClF,8BA3CG,CAAA,KAAA,EA2CH;QACI,OA3CO,IAAI,mBAAA,CAAoB,KAAC,CAAK,CAAC;KA4CvC;;AA1CI,gBAAP,CAAA,UAAO,GAAoC;IA4C3C,EA3CE,IAAA,EAAM,UAAA,EAAW;CA4ClB,CA3CC;;;;AAED,gBAAD,CAAA,cAAC,GAAA,MAAA;IA8CD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAG,EAAE,EAAG,EAAC;CACtE,CAAC;AAGF,AAYA;;;AAGA,MAAA,aAAA,CAAA;;;;IAIA,WAAA,CA/DqB,qCAAuC,EA+D5D;QA/DqB,IAArB,CAAA,qCAAqB,GAAA,qCAAA,CAAuC;;KAiEzD;;;;;IAKH,WAlEY,GAkEZ,GAlEY;;;;IAsEZ,QApEG,GAoEH;QACI,OApEO,CAoEX,uCAAA,EApEW,IAA2C,CAAI,qCAAC,CAoE3D,CApEgG;YAqExF,CAAR,kCAAA,CAA4C,CApEC;KAqE1C;CACF;AAED,AAKA,MAAA,YAzEC,SAAA,aAAA,CAyED;;;;IAIA,WA5EG,GA4EH,EA5EkB,OAAO,MAAA,CAAO,EAAC;CA6EhC;AACD,MAAA,aA5EC,SAAA,aAAA,CA4ED;;;;IAIA,WA/EG,GA+EH,EA/EkB,OAAO,OAAA,CAAQ,EAAC;CAgFjC;AACD,MAAA,cA/EC,SAAA,aAAA,CA+ED;;;;IAIA,WAlFG,GAkFH,EAlFkB,OAAO,QAAA,CAAS,EAAC;CAmFlC;AACD,MAAA,WAlFC,SAAA,aAAA,CAkFD;;;;IAIA,WArFG,GAqFH,EArFkB,OAAO,KAAA,CAAM,EAAC;CAsF/B;AACD,MAAA,mBArFC,SAAA,aAAA,CAqFD;;;;IAIA,WAxFG,GAwFH,EAxFkB,OAAO,aAAA,CAAc,EAAC;CAyFvC;;ADzUD;;;;;;;AASA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AADO,MAAM,mCAAA,GAAkD;IAE7D,EAAC,OADC,EAAQ,WAAA,EAAa,QAAA,EAAUF,oBAAA,EAAoB;IAErD,EAAC,OADC,EAAQ,oBAAA,EAAsB,QAAA,EAAU,cAAA,EAAgB,KAAA,EAAO,IAAA,EAAK;IAEtE,EAAC,OADC,EAAQ,gBAAA,EAAkB,QAAA,EAAU,uBAAA,EAAwB;IAE9D,EAAC,OADC,EAAQ,QAAA,EAAU,UAAA,EAAY,SAAA,EAAW,IAAA,EAAM,EAAA,EAAG;CAErD,CADC;;;;;;;AAQF,AAAC,MAAA,8BAAA,GAAA;IACC,EAAC,OAAC,EAAQ,SAAA,EAAW,WAAA,EAAa,YAAA,EAAa;IAC/C,EAAC,OAAC,EAAQ,YAAA,EAAc,QAAA,EAAU,gBAAA,EAAiB;CACpD,CAAC;;;;AAIF,AACC,MAAA,eAAA,GAAG,qBAAS,CAAA,YAAmB,EAAA,SAAgB,EAAA,mCAA6B,CAAA,CAAA;;;;AAI7E,AAAA,SAAA,cAAA,GAAA;IACE,iBAAiB,CADC,WAAC,EAAW,CAAE;IAEhC,qBAAA,CADsB,IAAC,EAAI,CAAE;CAE9B;;;;AAID,AAAA,SAAA,YAAA,GAAA;IACE,OAHO,IAAI,YAAA,EAAa,CAAE;CAI3B;;;;AAID,AAAA,SAAA,SAAA,GAAA;IACE,OALO,QAAA,CAAS;CAMjB;;;;;;AAMD,AAAA,MAAA,aAAA,CAAA;;;;IAIA,WAAA,CANe,YAAe,EAM9B;QACI,IAAI,YAAY,EAAE;YAChB,MAAM,IAAI,KAAK,CACX,CADV,6JAAA,CACyK,CAAC,CAAC;SACtK;KACF;;;;;;;;;;IAUH,OAPG,oBAAA,CAAA,MAAA,EAOH;QACI,OAPO;YAQL,QAAQ,EAPE,aAAA;YAQV,SAAS,EAPE;gBAQT,EAAC,OAPC,EAAQ,MAAA,EAAQ,QAAA,EAAU,MAAA,CAAO,KAAC,EAAK;gBAQzC,EAAC,OAPC,EAAQ,aAAA,EAAe,WAAA,EAAa,MAAA,EAAO;gBAQ7C,2BAA2B;aAC5B;SACF,CAPC;KAQH;;AANI,aAAP,CAAA,UAAO,GAAoC;IAQ3C,EAPE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAQtB,SAAS,EAPE;oBAQT,8BAA8B;oBAC9B,EAAC,OAPC,EAAQ,YAAA,EAAc,UAAA,EAAY,YAAA,EAAc,IAAA,EAAM,EAAA,EAAG;oBAQ3D,EAAC,OAPC,EAAQ,qBAAA,EAAuB,QAAA,EAAU,eAAA,EAAiB,KAAA,EAAO,IAAA,EAAK;oBAQxE,EAAC,OAPC,EAAQ,qBAAA,EAAuB,QAAA,EAAU,eAAA,EAAiB,KAAA,EAAO,IAAA,EAAK;oBAQxE,EAAC,OAPC,EAAQ,qBAAA,EAAuB,QAAA,EAAU,oBAAA,EAAsB,KAAA,EAAO,IAAA,EAAK;oBAQ7E,EAAC,OAPC,EAAQ,qBAAA,EAAuB,QAAA,EAAU,mBAAA,EAAoB;oBAQ/D,mBAAmB;oBACnB,EAAC,OAPC,EAAQ,gBAAA,EAAkB,WAAA,EAAa,mBAAA,EAAoB;oBAQ7D,EAAC,OAPC,EAAQ,gBAAA,EAAkB,WAAA,EAAa,mBAAA,EAAoB;oBAQ7D,mBAAmB;oBACnB,WAAW;oBACX,YAAY;oBACZ,uBAAuB;oBACvB,IAAI;oBACJ,KAAK;iBACN;gBAND,OAAA,EAAS,CAAA,YAAE,EAAa,iBAAA,CAAkB;aAQ3C,EAPC,EAAG;CAQJ,CAPC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA;IAUD,EAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAG,EAAC;CAC5E,CAAC,AAGF,AAQC;;AD5ID;;;;;;;AAOA,MAAC,GAAA,GAAA,OAAA,MAAA,KAAA,WAAA,IAAA,MAAA,IAAA,EAAA,CAAA,AACD,AAAsB;;ADTtB;;;;;;;AASA,AACA,AACA,AACA,AAAA,MAAA,yBAAA,CAAA;;;;;IAKA,WAAA,CAJqB,SAAW,EAAe,QAAU,EAIzD;QAJqB,IAArB,CAAA,SAAqB,GAAA,SAAA,CAAW;QAAe,IAA/C,CAAA,QAA+C,GAAA,QAAA,CAAU;KAAO;CAM/D;AAED,AAOA;;;;AAIA,AAAA,MAAA,eAAA,CAAA;;;;IAKA,WAAA,CAdG,GAAiC,EAcpC,EAdqC,IAAA,CAAA,MAAA,GAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;IAiCrC,mBAdG,CAAA,MAAA,EAcH;QACI,uBAdM,MAAA,GAAS,MAAA,IAAU,MAAA,CAAO,QAAC,CAAQ,CAAC;QAe1C,uBAdM,WAAA,GAAc,kBAAA,CAAmB;;QAgBvC,uBAdM,mBAAA,GAAsBD,GAAA,CAAO,OAAC,CAAO,OAAC,IAAU,IAAA,CAAK;QAe3D,IAAI,MAdC,IAAS,mBAAA,EAAqB;YAejCA,GAAM,CAdC,OAAC,CAAO,OAAC,CAAO,WAAC,CAAW,CAAC;SAerC;QACD,uBAdM,KAAA,GAAQ,MAAA,EAAO,CAAE,cAAC,EAAc,CAAE;QAexC,qBAdI,QAAA,GAAW,CAAA,CAAE;QAejB,OAAO,QAdC,GAAU,CAAA,IAAK,CAAA,MAAE,EAAM,CAAE,cAAC,EAAc,GAAI,KAAA,IAAS,GAAA,EAAK;YAehE,IAAI,CAdC,MAAC,CAAM,IAAC,EAAI,CAAE;YAenB,QAAQ,EAdC,CAAE;SAeZ;QACD,uBAdM,GAAA,GAAM,MAAA,EAAO,CAAE,cAAC,EAAc,CAAE;QAetC,IAAI,MAdC,IAAS,mBAAA,EAAqB;;;;;YAmBjC,EAdMA,GAAC,CAAM,OAAC,CAAO,UAAC,GAAW,WAAC,CAAW,CAAC;SAe/C;QACD,uBAdM,SAAA,GAAY,CAAA,GAAE,GAAK,KAAA,IAAS,QAAA,CAAS;QAe3CA,GAAM,CAdC,OAAC,CAAO,GAAC,CAAG,CAcvB,IAAA,EAdwB,QAAO,CAc/B,wBAAA,CAduC,CAA0B,CAAC;QAe9DA,GAAM,CAdC,OAAC,CAAO,GAAC,CAAG,CAcvB,EAdwB,SAAG,CAAS,OAAC,CAAO,CAAC,CAAC,CAc9C,aAAA,CAd+C,CAAe,CAAC;QAgB3D,OAdO,IAAI,yBAAA,CAA0B,SAAC,EAAU,QAAA,CAAS,CAAC;KAe3D;CACF,AAED,AAGC;;ADzFD;;;;;;;AAUA,AACA,AAEA,MADM,oBAAA,GAAuB,UAAA,CAAW;;;;;;;;;;;;;;;;;AAkBxC,AAAA,SAAA,gBAAA,CAHC,GAAA,EAGD;IACE,WAAW,CAHC,oBAAC,EAAqB,IAAI,eAAA,CAAgB,GAAC,CAAG,CAAC,CAAC;IAI5D,OAHO,GAAA,CAAI;CAIZ;;;;;;;AAOD,AAAA,SAAA,iBAAA,GAAA;IACE,WAAW,CAHC,oBAAC,EAAqB,IAAA,CAAK,CAAC;CAIzC;;AD1CD;;;;;;;AAUA,AACA;;;;;AAKA,AAAA,MAAA,EAAA,CAAA;;;;;;;;;IASA,OACG,GAAA,GADH,EAC0C,OAAO,CAAA,YAAE,KAAgB,IAAA,CAAK,EAAC;;;;;;;;;;IASzE,OAAG,GAAA,CAAA,QAAA,EAAH;QACI,OAAO,CAAA,YAAE,KAAb;YACM,OAAO,YAAA,CAAa,aAAC,IAAgB,IAAA;gBACjC,MAAM,EAAC,CAAE,cAAC,CAAc,YAAC,CAAY,aAAC,EAAc,QAAA,CAAS;gBAC7D,KAAK,CAAC;SACX,CAAC;KACH;;;;;;;;;;IAUH,OADG,SAAA,CAAA,IAAA,EACH;QACI,OADO,CAAA,YAAE,KAAa,EAAG,YAAA,CAAa,cAAC,GAAgB,OAAC,CAAO,IAAC,CAAI,KAAK,CAAA,CAAE,CAAC;KAE7E;CACF;;ADtDD;;;;;;GAMG,AAEH,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAA2F;;ADnB3F;;;;;;;;;;;;AAeA,AACA;;;AAGA,AADC,MAAA,OAAA,GAAA,IAAA,OAAA,CAAA,mBAAA,CAAA,CAAA;;ADlBD;;;;;;GAMG,AAEH,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAAgB;;ADnBhB;;;;;;;;;;;;AAaA,AAAguB;0EACtpB;;ADd1E;;GAEG,AAEH,AAEA,AACA,AACA,AACA,AACA,AACA,AAA+B;;"}