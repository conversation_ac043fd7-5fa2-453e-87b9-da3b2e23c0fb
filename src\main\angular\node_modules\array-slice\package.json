{"_args": [["array-slice@0.2.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "array-slice@0.2.3", "_id": "array-slice@0.2.3", "_inBundle": false, "_integrity": "sha1-3Tz7gO15c6dRF82sabC5nshhhvU=", "_location": "/array-slice", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-slice@0.2.3", "name": "array-slice", "escapedName": "array-slice", "rawSpec": "0.2.3", "saveSpec": null, "fetchSpec": "0.2.3"}, "_requiredBy": ["/expand-braces"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-slice/-/array-slice-0.2.3.tgz", "_spec": "0.2.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/array-slice/issues"}, "description": "Array-slice method. Slices `array` from the `start` index up to, but not including, the `end` index.", "devDependencies": {"mocha": "*", "should": "^5.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/array-slice", "keywords": ["array", "javascript", "js", "slice", "util", "utils"], "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/array-slice/blob/master/LICENSE"}, "main": "index.js", "name": "array-slice", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/array-slice.git"}, "scripts": {"test": "mocha"}, "version": "0.2.3"}