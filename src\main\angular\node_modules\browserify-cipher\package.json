{"_args": [["browserify-cipher@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-cipher@1.0.0", "_id": "browserify-cipher@1.0.0", "_inBundle": false, "_integrity": "sha1-mYgkSHS/XtTijalWZtzWasj8Njo=", "_location": "/browserify-cipher", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-cipher@1.0.0", "name": "browserify-cipher", "escapedName": "browserify-cipher", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-cipher/-/browserify-cipher-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/browserify-cipher/issues"}, "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}, "description": "ciphers for the browser", "devDependencies": {"standard": "^5.3.1", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "homepage": "https://github.com/crypto-browserify/browserify-cipher#readme", "license": "MIT", "main": "index.js", "name": "browserify-cipher", "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/browserify-cipher.git"}, "scripts": {"test": "standard && node test.js | tspec"}, "version": "1.0.0"}