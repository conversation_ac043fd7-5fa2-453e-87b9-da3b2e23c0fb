{"_args": [["angular-svg-round-progressbar@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "angular-svg-round-progressbar@1.1.0", "_id": "angular-svg-round-progressbar@1.1.0", "_inBundle": false, "_integrity": "sha1-WlaDU8dLk6UOgYbZjQ/vhkuj2d0=", "_location": "/angular-svg-round-progressbar", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "angular-svg-round-progressbar@1.1.0", "name": "angular-svg-round-progressbar", "escapedName": "angular-svg-round-progressbar", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/angular-svg-round-progressbar/-/angular-svg-round-progressbar-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "cris<PERSON>o"}, "bugs": {"url": "https://github.com/crisbeto/angular-svg-round-progressbar/issues"}, "dependencies": {"@angular/common": "^4.0.0", "@angular/compiler": "^4.0.0", "@angular/core": "^4.0.0", "core-js": "^2.4.1", "rxjs": "^5.0.0", "zone.js": "^0.8.4"}, "description": "Angular module that uses SVG to create a circular progressbar", "devDependencies": {"@angular/compiler-cli": "^4.0.0", "@angular/forms": "^4.0.0", "@angular/platform-browser": "^4.0.0", "@angular/platform-browser-dynamic": "^4.0.0", "gulp": "^3.9.1", "gulp-clean": "^0.3.2", "gulp-gh-pages": "^0.5.4", "gulp-server-livereload": "^1.8.2", "merge2": "^1.0.2", "resolve-bin": "^0.4.0", "run-sequence": "^1.2.2", "systemjs": "0.19.38", "tslint": "^3.13.0", "typescript": "~2.2.0"}, "files": ["dist/**/*"], "homepage": "https://github.com/crisbeto/angular-svg-round-progressbar", "keywords": ["angular", "progress", "circle", "round", "svg"], "license": "MIT", "main": "dist/index.js", "name": "angular-svg-round-progressbar", "repository": {"type": "git", "url": "git://github.com/crisbeto/angular-svg-round-progressbar.git"}, "scripts": {}, "types": "dist/index.d.ts", "version": "1.1.0"}