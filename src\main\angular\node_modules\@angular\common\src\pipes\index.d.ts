/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @module
 * @description
 * This module provides a set of common Pipes.
 */
import { AsyncPipe } from './async_pipe';
import { LowerCasePipe, TitleCasePipe, UpperCasePipe } from './case_conversion_pipes';
import { DatePipe } from './date_pipe';
import { I18nPluralPipe } from './i18n_plural_pipe';
import { I18nSelectPipe } from './i18n_select_pipe';
import { JsonPipe } from './json_pipe';
import { CurrencyPipe, DecimalPipe, PercentPipe } from './number_pipe';
import { SlicePipe } from './slice_pipe';
export { AsyncPipe, CurrencyPipe, DatePipe, DecimalPipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, PercentPipe, SlicePipe, TitleCasePipe, UpperCasePipe };
/**
 * A collection of Angular pipes that are likely to be used in each and every application.
 */
export declare const COMMON_PIPES: (typeof AsyncPipe | typeof DecimalPipe | typeof PercentPipe | typeof CurrencyPipe | typeof DatePipe | typeof I18nPluralPipe | typeof I18nSelectPipe | typeof SlicePipe)[];
