{"_args": [["async@2.5.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "async@2.5.0", "_id": "async@2.5.0", "_inBundle": false, "_integrity": "sha512-e+lJAJeNWuPCNyxZKOBdaJGyLGHugXVQtrAwtuAe2vhxTYxFTKE73p8JuTmdH0qdQZtDvI4dhJwjZc5zsfIsYw==", "_location": "/async", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "async@2.5.0", "name": "async", "escapedName": "async", "rawSpec": "2.5.0", "saveSpec": null, "fetchSpec": "2.5.0"}, "_requiredBy": ["/extract-text-webpack-plugin", "/fs-walk", "/istanbul-api", "/sass-loader", "/watchpack", "/webpack"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/async/-/async-2.5.0.tgz", "_spec": "2.5.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "dependencies": {"lodash": "^4.14.0"}, "description": "Higher-order functions and common patterns for asynchronous code", "devDependencies": {"babel-cli": "^6.24.0", "babel-core": "^6.24.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-istanbul": "^2.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.16", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "babelify": "^7.2.0", "benchmark": "^2.1.1", "bluebird": "^3.4.6", "chai": "^3.1.0", "cheerio": "^0.22.0", "coveralls": "^2.11.2", "es6-promise": "^2.3.0", "eslint": "^2.13.1", "fs-extra": "^0.26.7", "gh-pages-deploy": "^0.4.2", "jsdoc": "^3.4.0", "karma": "^1.3.0", "karma-browserify": "^5.1.0", "karma-firefox-launcher": "^1.0.0", "karma-mocha": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "mocha": "^3.1.2", "native-promise-only": "^0.8.0-a", "nyc": "^7.0.0", "recursive-readdir": "^1.3.0", "rimraf": "^2.5.0", "rollup": "^0.36.3", "rollup-plugin-node-resolve": "^2.0.0", "rollup-plugin-npm": "^2.0.0", "rsvp": "^3.0.18", "semver": "^4.3.6", "uglify-js": "~2.7.3", "vinyl-buffer": "^1.0.0", "vinyl-source-stream": "^1.1.0", "watchify": "^3.7.0", "yargs": "~3.9.1"}, "gh-pages-deploy": {"staticpath": "docs"}, "homepage": "https://github.com/caolan/async#readme", "keywords": ["async", "callback", "module", "utility"], "license": "MIT", "main": "dist/async.js", "name": "async", "nyc": {"exclude": ["mocha_test"]}, "repository": {"type": "git", "url": "git+https://github.com/caolan/async.git"}, "scripts": {"coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "lint": "eslint lib/ mocha_test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "mocha-browser-test": "karma start", "mocha-node-test": "mocha mocha_test/ --compilers js:babel-core/register", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "test": "npm run lint && npm run mocha-node-test"}, "version": "2.5.0"}