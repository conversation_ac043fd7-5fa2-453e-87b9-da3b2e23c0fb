{"name": "contra", "description": "Asynchronous flow control with a functional taste to it", "version": "1.9.4", "homepage": "https://github.com/bevacqua/contra", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bevacqua.io"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/bevacqua/contra.git"}, "keywords": ["async", "contra", "asynchronous", "flow", "control", "promises", "q", "generator"], "main": "dist/contra.js", "ignore": [".*", "package.json", "node_modules", "test", "resources", "*.js"], "dependencies": {}, "devDependencies": {"assert": "~0.0.2", "mocha": "~1.17.1"}}