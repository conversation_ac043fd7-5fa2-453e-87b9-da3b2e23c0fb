{"_args": [["backo2@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "backo2@1.0.2", "_id": "backo2@1.0.2", "_inBundle": false, "_integrity": "sha1-MasayLEpNjRj41s+u2n038+6eUc=", "_location": "/backo2", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "backo2@1.0.2", "name": "backo2", "escapedName": "backo2", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/backo2/-/backo2-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/mokesmokes/backo/issues"}, "dependencies": {}, "description": "simple backoff based on segmentio/backo", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/mokesmokes/backo#readme", "keywords": ["backoff"], "license": "MIT", "name": "backo2", "repository": {"type": "git", "url": "git+https://github.com/mokesmokes/backo.git"}, "version": "1.0.2"}