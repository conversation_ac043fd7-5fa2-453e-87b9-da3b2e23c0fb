{"_args": [["config-chain@1.1.12", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "config-chain@1.1.12", "_id": "config-chain@1.1.12", "_inBundle": false, "_integrity": "sha512-a1eOIcu8+7lUInge4Rpf/n4Krkf3Dd9lqhljRzII1/Zno/kRtUWnznPO3jOKBmTEktkt3fkxisUcivoj0ebzoA==", "_location": "/config-chain", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "config-chain@1.1.12", "name": "config-chain", "escapedName": "config-chain", "rawSpec": "1.1.12", "saveSpec": null, "fetchSpec": "1.1.12"}, "_requiredBy": ["/npm-conf"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/config-chain/-/config-chain-1.1.12.tgz", "_spec": "1.1.12", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/config-chain/issues"}, "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}, "description": "HANDLE CONFIGURATION ONCE AND FOR ALL", "devDependencies": {"tap": "0.3.0"}, "files": ["index.js"], "homepage": "http://github.com/dominictarr/config-chain", "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/dominictarr/config-chain/master/LICENCE"}], "name": "config-chain", "repository": {"type": "git", "url": "git+https://github.com/dominictarr/config-chain.git"}, "scripts": {"test": "tap test/*"}, "version": "1.1.12"}