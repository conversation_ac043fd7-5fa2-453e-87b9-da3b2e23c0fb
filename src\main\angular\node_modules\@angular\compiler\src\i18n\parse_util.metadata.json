[{"__symbolic": "module", "version": 3, "metadata": {"I18nError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"I18nError": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}, {"__symbolic": "reference", "name": "string"}]}]}}}}]