{"_args": [["combine-lists@1.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "combine-lists@1.0.1", "_id": "combine-lists@1.0.1", "_inBundle": false, "_integrity": "sha1-RYwH4J4NkA/Ci3Cj/sLazR0st/Y=", "_location": "/combine-lists", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "combine-lists@1.0.1", "name": "combine-lists", "escapedName": "combine-lists", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/karma"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/combine-lists/-/combine-lists-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/sjelin/combine-lists/issues"}, "dependencies": {"lodash": "^4.5.0"}, "description": "Merge two lists, trying to preserve the order of both ", "devDependencies": {"jasmine": "^2.4.1", "seedrandom": "^2.4.2"}, "homepage": "https://github.com/sjelin/combine-lists#readme", "keywords": ["utility", "array", "list", "merge", "combine", "topological", "sort"], "license": "MIT", "main": "index.js", "name": "combine-lists", "repository": {"type": "git", "url": "git+https://github.com/sjelin/combine-lists.git"}, "scripts": {"test": "node node_modules/.bin/jasmine JASMINE_CONFIG_PATH=test/config.json"}, "version": "1.0.1"}