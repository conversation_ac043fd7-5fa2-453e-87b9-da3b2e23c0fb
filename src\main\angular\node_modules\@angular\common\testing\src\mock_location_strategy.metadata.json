[{"__symbolic": "module", "version": 3, "metadata": {"MockLocationStrategy": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "simulatePopState": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "onPopState": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MockLocationStrategy": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "simulatePopState": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "onPopState": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}]}}}}]