[{"__symbolic": "module", "version": 3, "metadata": {"platformBrowserTesting": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 16, "character": 6, "context": {"name": "_TEST_BROWSER_PLATFORM_PROVIDERS"}}, "BrowserTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "BrowserModule"}], "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_ID"}, "useValue": "a"}, {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵELEMENT_PROBE_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, "useFactory": {"__symbolic": "reference", "module": "./browser_util", "name": "createNgZone"}}]}]}]}}}, {"__symbolic": "module", "version": 1, "metadata": {"platformBrowserTesting": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 16, "character": 6, "context": {"name": "_TEST_BROWSER_PLATFORM_PROVIDERS"}}, "BrowserTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "BrowserModule"}], "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_ID"}, "useValue": "a"}, {"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵELEMENT_PROBE_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, "useFactory": {"__symbolic": "reference", "module": "./browser_util", "name": "createNgZone"}}]}]}]}}}]