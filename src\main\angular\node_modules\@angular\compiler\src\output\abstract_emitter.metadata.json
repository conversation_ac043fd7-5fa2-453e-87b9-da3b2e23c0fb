[{"__symbolic": "module", "version": 3, "metadata": {"CATCH_ERROR_VAR": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./output_ast", "name": "variable"}, "arguments": ["error", null, null]}, "CATCH_STACK_VAR": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./output_ast", "name": "variable"}, "arguments": ["stack", null, null]}, "OutputEmitter": {"__symbolic": "class", "members": {"emitStatements": [{"__symbolic": "method"}]}}, "EmitterVisitorContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}]}], "println": [{"__symbolic": "method"}], "lineIsEmpty": [{"__symbolic": "method"}], "lineLength": [{"__symbolic": "method"}], "print": [{"__symbolic": "method"}], "removeEmptyLastLine": [{"__symbolic": "method"}], "incIndent": [{"__symbolic": "method"}], "decIndent": [{"__symbolic": "method"}], "pushClass": [{"__symbolic": "method"}], "popClass": [{"__symbolic": "method"}], "toSource": [{"__symbolic": "method"}], "toSourceMapGenerator": [{"__symbolic": "method"}], "setPreambleLineCount": [{"__symbolic": "method"}], "spanOf": [{"__symbolic": "method"}]}, "statics": {"createRoot": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "EmitterVisitorContext"}, "arguments": [0]}}}}, "AbstractEmitterVisitor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "boolean"}]}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "getBuiltinMethodName": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitAllObjects": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "escapeIdentifier": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"CATCH_ERROR_VAR": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./output_ast", "name": "variable"}, "arguments": ["error", null, null]}, "CATCH_STACK_VAR": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./output_ast", "name": "variable"}, "arguments": ["stack", null, null]}, "OutputEmitter": {"__symbolic": "class", "members": {"emitStatements": [{"__symbolic": "method"}]}}, "EmitterVisitorContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}]}], "println": [{"__symbolic": "method"}], "lineIsEmpty": [{"__symbolic": "method"}], "lineLength": [{"__symbolic": "method"}], "print": [{"__symbolic": "method"}], "removeEmptyLastLine": [{"__symbolic": "method"}], "incIndent": [{"__symbolic": "method"}], "decIndent": [{"__symbolic": "method"}], "pushClass": [{"__symbolic": "method"}], "popClass": [{"__symbolic": "method"}], "toSource": [{"__symbolic": "method"}], "toSourceMapGenerator": [{"__symbolic": "method"}], "setPreambleLineCount": [{"__symbolic": "method"}], "spanOf": [{"__symbolic": "method"}]}, "statics": {"createRoot": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "EmitterVisitorContext"}, "arguments": [0]}}}}, "AbstractEmitterVisitor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "boolean"}]}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "getBuiltinMethodName": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitAllObjects": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "escapeIdentifier": {"__symbolic": "function"}}}]