{"_args": [["bootstrap@3.3.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "bootstrap@3.3.7", "_id": "bootstrap@3.3.7", "_inBundle": false, "_integrity": "sha1-WjiTlFSfIzMIdaOxUGVldPip63E=", "_location": "/bootstrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bootstrap@3.3.7", "name": "bootstrap", "escapedName": "bootstrap", "rawSpec": "3.3.7", "saveSpec": null, "fetchSpec": "3.3.7"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/bootstrap/-/bootstrap-3.3.7.tgz", "_spec": "3.3.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "Twitter, Inc."}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "devDependencies": {"btoa": "~1.1.2", "glob": "~7.0.3", "grunt": "~1.0.1", "grunt-autoprefixer": "~3.0.4", "grunt-contrib-clean": "~1.0.0", "grunt-contrib-compress": "~1.3.0", "grunt-contrib-concat": "~1.0.0", "grunt-contrib-connect": "~1.0.0", "grunt-contrib-copy": "~1.0.0", "grunt-contrib-csslint": "~1.0.0", "grunt-contrib-cssmin": "~1.0.0", "grunt-contrib-htmlmin": "~1.5.0", "grunt-contrib-jshint": "~1.0.0", "grunt-contrib-less": "~1.3.0", "grunt-contrib-pug": "~1.0.0", "grunt-contrib-qunit": "~0.7.0", "grunt-contrib-uglify": "~1.0.0", "grunt-contrib-watch": "~1.0.0", "grunt-csscomb": "~3.1.0", "grunt-exec": "~1.0.0", "grunt-html": "~8.0.1", "grunt-jekyll": "~0.4.4", "grunt-jscs": "~3.0.1", "grunt-saucelabs": "~9.0.0", "load-grunt-tasks": "~3.5.0", "markdown-it": "^7.0.0", "shelljs": "^0.7.0", "shx": "^0.1.2", "time-grunt": "^1.3.0"}, "engines": {"node": ">=0.10.1"}, "files": ["dist", "fonts", "grunt", "js/*.js", "less/**/*.less", "Gruntfile.js", "LICENSE"], "homepage": "http://getbootstrap.com", "jspm": {"main": "js/bootstrap", "shim": {"js/bootstrap": {"deps": "j<PERSON>y", "exports": "$"}}, "files": ["css", "fonts", "js"]}, "keywords": ["css", "less", "mobile-first", "responsive", "front-end", "framework", "web"], "less": "less/bootstrap.less", "license": "MIT", "main": "./dist/js/npm", "name": "bootstrap", "repository": {"type": "git", "url": "git+https://github.com/twbs/bootstrap.git"}, "scripts": {"change-version": "node grunt/change-version.js", "test": "grunt test", "update-shrinkwrap": "npm shrinkwrap --dev && shx mv ./npm-shrinkwrap.json ./grunt/npm-shrinkwrap.json"}, "style": "dist/css/bootstrap.css", "version": "3.3.7"}