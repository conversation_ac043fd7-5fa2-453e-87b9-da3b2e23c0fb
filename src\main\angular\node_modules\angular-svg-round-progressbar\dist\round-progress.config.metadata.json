[{"__symbolic": "module", "version": 3, "metadata": {"RoundProgressDefaults": {"__symbolic": "interface"}, "RoundProgressConfig": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"setDefaults": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"RoundProgressDefaults": {"__symbolic": "interface"}, "RoundProgressConfig": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"setDefaults": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}]}}}}]