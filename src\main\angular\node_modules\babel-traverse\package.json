{"_args": [["babel-traverse@6.25.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-traverse@6.25.0", "_id": "babel-traverse@6.25.0", "_inBundle": false, "_integrity": "sha1-IldJfi/NGbie3BPEyROB+VEklvE=", "_location": "/babel-traverse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-traverse@6.25.0", "name": "babel-traverse", "escapedName": "babel-traverse", "rawSpec": "6.25.0", "saveSpec": null, "fetchSpec": "6.25.0"}, "_requiredBy": ["/babel-template", "/istanbul-lib-instrument"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-traverse/-/babel-traverse-6.25.0.tgz", "_spec": "6.25.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-code-frame": "^6.22.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.25.0", "babylon": "^6.17.2", "debug": "^2.2.0", "globals": "^9.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "devDependencies": {"babel-generator": "^6.25.0"}, "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-traverse", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-traverse"}, "version": "6.25.0"}