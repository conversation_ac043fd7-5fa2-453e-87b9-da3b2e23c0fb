[{"__symbolic": "module", "version": 3, "metadata": {"extractMessages": {"__symbolic": "function"}, "mergeTranslations": {"__symbolic": "function"}, "ExtractionResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./i18n_ast", "name": "Message"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./parse_util", "name": "I18nError"}]}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"extractMessages": {"__symbolic": "function"}, "mergeTranslations": {"__symbolic": "function"}, "ExtractionResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./i18n_ast", "name": "Message"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "./parse_util", "name": "I18nError"}]}]}]}}}}]