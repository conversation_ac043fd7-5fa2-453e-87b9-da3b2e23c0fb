{"_args": [["co@4.6.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "co@4.6.0", "_id": "co@4.6.0", "_inBundle": false, "_integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "_location": "/co", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "co@4.6.0", "name": "co", "escapedName": "co", "rawSpec": "4.6.0", "saveSpec": null, "fetchSpec": "4.6.0"}, "_requiredBy": ["/ajv", "/har-validator/ajv", "/webpack/ajv"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/co/-/co-4.6.0.tgz", "_spec": "4.6.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/tj/co/issues"}, "description": "generator async control flow goodness", "devDependencies": {"browserify": "^10.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "files": ["index.js"], "homepage": "https://github.com/tj/co#readme", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "license": "MIT", "name": "co", "repository": {"type": "git", "url": "git+https://github.com/tj/co.git"}, "scripts": {"browserify": "browserify index.js -o ./co-browser.js -s co", "prepublish": "npm run browserify", "test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "version": "4.6.0"}