{"_args": [["big.js@3.1.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "big.js@3.1.3", "_id": "big.js@3.1.3", "_inBundle": false, "_integrity": "sha1-TK2iGTZS6zyp7I5VyQFWacmAaXg=", "_location": "/big.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "big.js@3.1.3", "name": "big.js", "escapedName": "big.js", "rawSpec": "3.1.3", "saveSpec": null, "fetchSpec": "3.1.3"}, "_requiredBy": ["/html-webpack-plugin/loader-utils", "/istanbul-instrumenter-loader/loader-utils", "/loader-utils", "/source-map-loader/loader-utils", "/webpack/loader-utils"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/big.js/-/big.js-3.1.3.tgz", "_spec": "3.1.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "engines": {"node": "*"}, "files": ["big.js", "big.min.js"], "homepage": "https://github.com/MikeMcl/big.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "license": "MIT", "main": "./big", "name": "big.js", "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/big.js.git"}, "scripts": {"build": "uglifyjs -o ./big.min.js ./big.js", "test": "node ./test/every-test.js"}, "version": "3.1.3"}