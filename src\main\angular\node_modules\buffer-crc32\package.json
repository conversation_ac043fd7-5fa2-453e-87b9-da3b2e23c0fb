{"_args": [["buffer-crc32@0.2.13", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-crc32@0.2.13", "_id": "buffer-crc32@0.2.13", "_inBundle": false, "_integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "_location": "/buffer-crc32", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-crc32@0.2.13", "name": "buffer-crc32", "escapedName": "buffer-crc32", "rawSpec": "0.2.13", "saveSpec": null, "fetchSpec": "0.2.13"}, "_requiredBy": ["/yauzl"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "_spec": "0.2.13", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "contributors": [{"name": "<PERSON>"}], "dependencies": {}, "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "devDependencies": {"tap": "~0.2.5"}, "engines": {"node": "*"}, "files": ["index.js"], "homepage": "https://github.com/brianloveswords/buffer-crc32", "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "main": "index.js", "name": "buffer-crc32", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js"}, "version": "0.2.13"}