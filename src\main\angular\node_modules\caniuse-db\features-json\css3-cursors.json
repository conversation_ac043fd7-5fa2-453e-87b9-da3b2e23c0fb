{"title": "CSS3 Cursors (original values)", "description": "CSS3 cursor values added in the 2004 spec, including none, context-menu, cell, vertical-text, alias, copy, no-drop, not-allowed, nesw-resize, nwse-resize, col-resize, row-resize and all-scroll. ", "spec": "http://www.w3.org/TR/css3-ui/#cursor", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/cursor", "title": "Mozilla Developer Network (MDN) documentation - CSS cursor"}], "bugs": [{"description": "Firefox/Mac, Safari/Mac, Chrome/Mac don't support PNG and JPG cursors (tested with 48px cursors).\r\nIE and Edge only supports cursors in the CUR format."}, {"description": "IE does not support Data URIs as cursor values"}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "a #2", "13": "a #2", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a", "3": "a", "3.5": "a", "3.6": "a", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a", "3.2": "a", "4": "a", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "a #2", "9.5-9.6": "a #2", "10.0-10.1": "a #2", "10.5": "a #2", "10.6": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "11.6": "a #2", "12": "a #2", "12.1": "a #2", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "u"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to no support for the alias, cell, copy, ew-resize, ns-resize, nesw-resize, nwse-resize or context-menu cursors.", "2": "Partial support refers to not supporting 'none'."}, "usage_perc_y": 39.78, "usage_perc_a": 0.71, "ucprefix": false, "parent": "", "keywords": "cursors, pointers", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}