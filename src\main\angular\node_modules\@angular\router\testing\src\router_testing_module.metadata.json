[{"__symbolic": "module", "version": 3, "metadata": {"SpyNgModuleFactoryLoader": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}]}], "load": [{"__symbolic": "method"}]}}, "setupTestingRouter": {"__symbolic": "function"}, "RouterTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/router", "name": "RouterModule"}], "providers": [{"__symbolic": "reference", "module": "@angular/router", "name": "ɵROUTER_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, "useClass": {"__symbolic": "reference", "module": "@angular/common/testing", "name": "SpyLocation"}}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}, "useClass": {"__symbolic": "reference", "module": "@angular/common/testing", "name": "MockLocationStrategy"}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, "useClass": {"__symbolic": "reference", "name": "SpyNgModuleFactoryLoader"}}, {"provide": {"__symbolic": "reference", "module": "@angular/router", "name": "Router"}, "useFactory": {"__symbolic": "reference", "name": "setupTestingRouter"}, "deps": [{"__symbolic": "reference", "module": "@angular/router", "name": "UrlSerializer"}, {"__symbolic": "reference", "module": "@angular/router", "name": "ChildrenOutletContexts"}, {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/router", "name": "ROUTES"}, [{"__symbolic": "reference", "module": "@angular/router", "name": "UrlHandlingStrategy"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}]]}, {"provide": {"__symbolic": "reference", "module": "@angular/router", "name": "PreloadingStrategy"}, "useExisting": {"__symbolic": "reference", "module": "@angular/router", "name": "NoPreloading"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/router", "name": "provideRoutes"}, "arguments": [[]]}]}]}], "statics": {"withRoutes": {"__symbolic": "function", "parameters": ["routes"], "value": {"ngModule": {"__symbolic": "reference", "name": "RouterTestingModule"}, "providers": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/router", "name": "provideRoutes"}, "arguments": [{"__symbolic": "reference", "name": "routes"}]}]}}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"SpyNgModuleFactoryLoader": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}]}], "load": [{"__symbolic": "method"}]}}, "setupTestingRouter": {"__symbolic": "function"}, "RouterTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/router", "name": "RouterModule"}], "providers": [{"__symbolic": "reference", "module": "@angular/router", "name": "ɵROUTER_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, "useClass": {"__symbolic": "reference", "module": "@angular/common/testing", "name": "SpyLocation"}}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}, "useClass": {"__symbolic": "reference", "module": "@angular/common/testing", "name": "MockLocationStrategy"}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, "useClass": {"__symbolic": "reference", "name": "SpyNgModuleFactoryLoader"}}, {"provide": {"__symbolic": "reference", "module": "@angular/router", "name": "Router"}, "useFactory": {"__symbolic": "reference", "name": "setupTestingRouter"}, "deps": [{"__symbolic": "reference", "module": "@angular/router", "name": "UrlSerializer"}, {"__symbolic": "reference", "module": "@angular/router", "name": "ChildrenOutletContexts"}, {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/router", "name": "ROUTES"}, [{"__symbolic": "reference", "module": "@angular/router", "name": "UrlHandlingStrategy"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}]]}, {"provide": {"__symbolic": "reference", "module": "@angular/router", "name": "PreloadingStrategy"}, "useExisting": {"__symbolic": "reference", "module": "@angular/router", "name": "NoPreloading"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/router", "name": "provideRoutes"}, "arguments": [[]]}]}]}], "statics": {"withRoutes": {"__symbolic": "function", "parameters": ["routes"], "value": {"ngModule": {"__symbolic": "reference", "name": "RouterTestingModule"}, "providers": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/router", "name": "provideRoutes"}, "arguments": [{"__symbolic": "reference", "name": "routes"}]}]}}}}}}]