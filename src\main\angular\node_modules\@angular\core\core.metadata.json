{"__symbolic": "module", "version": 3, "metadata": {"ɵa": {"__symbolic": "function"}, "ɵb": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ResolvedReflectiveProvider"}]}, {"__symbolic": "reference", "name": "Injector"}]}], "get": [{"__symbolic": "method"}], "resolveAndCreateChild": [{"__symbolic": "method"}], "createChildFromResolved": [{"__symbolic": "method"}], "resolveAndInstantiate": [{"__symbolic": "method"}], "instantiateResolved": [{"__symbolic": "method"}], "getProviderAtIndex": [{"__symbolic": "method"}], "_new": [{"__symbolic": "method"}], "_getMaxNumberOfObjects": [{"__symbolic": "method"}], "_instantiateProvider": [{"__symbolic": "method"}], "_instantiate": [{"__symbolic": "method"}], "_getByReflectiveDependency": [{"__symbolic": "method"}], "_getByKey": [{"__symbolic": "method"}], "_getObjByKeyId": [{"__symbolic": "method"}], "_throwOrNull": [{"__symbolic": "method"}], "_getByKeySelf": [{"__symbolic": "method"}], "_getByKeyDefault": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "ɵc": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ReflectiveKey"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "name": "Self"}]}]}, "statics": {"fromKey": {"__symbolic": "function", "parameters": ["key"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵc"}, "arguments": [{"__symbolic": "reference", "name": "key"}, false, null]}}}}, "ɵd": {"__symbolic": "function"}, "ɵe": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ApplicationRef"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "NgZone"}, {"__symbolic": "reference", "name": "ɵConsole"}, {"__symbolic": "reference", "name": "Injector"}, {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "name": "ComponentFactoryResolver"}, {"__symbolic": "reference", "name": "ApplicationInitStatus"}]}], "attachView": [{"__symbolic": "method"}], "detachView": [{"__symbolic": "method"}], "bootstrap": [{"__symbolic": "method"}], "_loadComponent": [{"__symbolic": "method"}], "_unloadComponent": [{"__symbolic": "method"}], "tick": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}, "statics": {"_tickScope": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./profile/profile", "name": "wtfCreateScope"}, "arguments": ["ApplicationRef#tick()"]}}}, "ɵf": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 38, "character": 9, "context": {"name": "_randomChar"}, "module": "./src/application_tokens"}}, "ɵg": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Iterable<PERSON><PERSON><PERSON>"}, "arguments": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵi"}}]]}, "ɵh": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "KeyValueDiffers"}, "arguments": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵj"}}]]}, "ɵi": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "supports": [{"__symbolic": "method"}], "create": [{"__symbolic": "method"}, {"__symbolic": "method"}]}}, "ɵj": {"__symbolic": "class", "arity": 2, "members": {"__ctor__": [{"__symbolic": "constructor"}], "supports": [{"__symbolic": "method"}], "create": [{"__symbolic": "method"}, {"__symbolic": "method"}]}}, "ɵk": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "reference", "name": "ɵg"}}, "ɵl": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "reference", "name": "ɵh"}}, "ɵm": {"__symbolic": "function", "parameters": ["locale"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "locale"}, "right": "en-US"}}, "ɵn": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵo"}}, "ɵo": {"__symbolic": "function"}, "ɵp": {"__symbolic": "function", "parameters": ["signature", "flags"], "defaults": [null, null], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 37, "character": 4, "context": {"name": "events"}, "module": "./src/profile/wtf_impl"}}, "ɵq": {"__symbolic": "function"}, "ɵr": {"__symbolic": "function", "parameters": ["rangeType", "action"], "value": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 36, "character": 4, "context": {"name": "trace"}, "module": "./src/profile/wtf_impl"}}, "ɵs": {"__symbolic": "function"}, "ɵt": {"__symbolic": "function"}, "ɵu": {"__symbolic": "class", "members": {"logError": [{"__symbolic": "method"}]}}, "ANALYZE_FOR_ENTRY_COMPONENTS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["AnalyzeForEntryComponents"]}, "Attribute": {"__symbolic": "error", "message": "Function call not supported", "line": 121, "character": 36, "module": "./src/metadata/di"}, "ContentChild": {"__symbolic": "error", "message": "Function call not supported", "line": 266, "character": 20, "module": "./src/metadata/di"}, "ContentChildDecorator": {"__symbolic": "interface"}, "ContentChildren": {"__symbolic": "error", "message": "Function call not supported", "line": 206, "character": 8, "module": "./src/metadata/di"}, "ContentChildrenDecorator": {"__symbolic": "interface"}, "Query": {"__symbolic": "class", "members": {}}, "ViewChild": {"__symbolic": "error", "message": "Function call not supported", "line": 382, "character": 17, "module": "./src/metadata/di"}, "ViewChildDecorator": {"__symbolic": "interface"}, "ViewChildren": {"__symbolic": "error", "message": "Function call not supported", "line": 325, "character": 20, "module": "./src/metadata/di"}, "ViewChildrenDecorator": {"__symbolic": "interface"}, "Component": {"__symbolic": "error", "message": "Function call not supported", "line": 686, "character": 17, "module": "./src/metadata/directives"}, "ComponentDecorator": {"__symbolic": "interface"}, "Directive": {"__symbolic": "error", "message": "Function call not supported", "line": 402, "character": 51, "module": "./src/metadata/directives"}, "DirectiveDecorator": {"__symbolic": "interface"}, "HostBinding": {"__symbolic": "error", "message": "Function call not supported", "line": 925, "character": 37, "module": "./src/metadata/directives"}, "HostListener": {"__symbolic": "error", "message": "Function call not supported", "line": 987, "character": 38, "module": "./src/metadata/directives"}, "Input": {"__symbolic": "error", "message": "Function call not supported", "line": 799, "character": 31, "module": "./src/metadata/directives"}, "Output": {"__symbolic": "error", "message": "Function call not supported", "line": 865, "character": 32, "module": "./src/metadata/directives"}, "Pipe": {"__symbolic": "error", "message": "Function call not supported", "line": 727, "character": 41, "module": "./src/metadata/directives"}, "AfterContentChecked": {"__symbolic": "interface"}, "AfterContentInit": {"__symbolic": "interface"}, "AfterViewChecked": {"__symbolic": "interface"}, "AfterViewInit": {"__symbolic": "interface"}, "DoCheck": {"__symbolic": "interface"}, "OnChanges": {"__symbolic": "interface"}, "OnDestroy": {"__symbolic": "interface"}, "OnInit": {"__symbolic": "interface"}, "CUSTOM_ELEMENTS_SCHEMA": {"name": "custom-elements"}, "ModuleWithProviders": {"__symbolic": "interface"}, "NO_ERRORS_SCHEMA": {"name": "no-errors-schema"}, "NgModule": {"__symbolic": "error", "message": "Function call not supported", "line": 193, "character": 49, "module": "./src/metadata/ng_module"}, "SchemaMetadata": {"__symbolic": "interface"}, "ViewEncapsulation": {"Emulated": 0, "Native": 1, "None": 2}, "Version": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}]}}, "VERSION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Version"}, "arguments": ["4.2.5"]}, "Class": {"__symbolic": "function"}, "TypeDecorator": {"__symbolic": "interface"}, "InjectDecorator": {"__symbolic": "interface"}, "Inject": {"__symbolic": "error", "message": "Function call not supported", "line": 60, "character": 68, "module": "./src/di/metadata"}, "OptionalDecorator": {"__symbolic": "interface"}, "Optional": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵa"}, "arguments": ["Optional"]}, "InjectableDecorator": {"__symbolic": "interface"}, "Injectable": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵmakeDecorator"}, "arguments": ["Injectable"]}, "SelfDecorator": {"__symbolic": "interface"}, "Self": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵa"}, "arguments": ["Self"]}, "SkipSelfDecorator": {"__symbolic": "interface"}, "SkipSelf": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵa"}, "arguments": ["SkipSelf"]}, "HostDecorator": {"__symbolic": "interface"}, "Host": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵa"}, "arguments": ["Host"]}, "forwardRef": {"__symbolic": "function"}, "resolveForwardRef": {"__symbolic": "function"}, "ForwardRefFn": {"__symbolic": "interface"}, "Injector": {"__symbolic": "class", "members": {"get": [{"__symbolic": "method"}, {"__symbolic": "method"}]}, "statics": {"THROW_IF_NOT_FOUND": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Object"}}, "NULL": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 16, "character": 0, "context": {"className": "_NullInjector"}}}}, "ReflectiveInjector": {"__symbolic": "class", "members": {"resolveAndCreateChild": [{"__symbolic": "method"}], "createChildFromResolved": [{"__symbolic": "method"}], "resolveAndInstantiate": [{"__symbolic": "method"}], "instantiateResolved": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}]}, "statics": {"resolve": {"__symbolic": "function", "parameters": ["providers"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵd"}, "arguments": [{"__symbolic": "reference", "name": "providers"}]}}, "fromResolvedProviders": {"__symbolic": "function", "parameters": ["providers", "parent"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵb"}, "arguments": [{"__symbolic": "reference", "name": "providers"}, {"__symbolic": "reference", "name": "parent"}]}}}}, "TypeProvider": {"__symbolic": "interface"}, "ValueProvider": {"__symbolic": "interface"}, "ClassProvider": {"__symbolic": "interface"}, "ExistingProvider": {"__symbolic": "interface"}, "FactoryProvider": {"__symbolic": "interface"}, "ResolvedReflectiveFactory": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Function"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵc"}]}]}]}}, "ResolvedReflectiveProvider": {"__symbolic": "interface"}, "ReflectiveKey": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Object"}, {"__symbolic": "reference", "name": "number"}]}]}, "statics": {"get": {"__symbolic": "function", "parameters": ["token"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "_globalKeyRegistry"}, "member": "get"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "resolveForwardRef"}, "arguments": [{"__symbolic": "reference", "name": "token"}]}]}}, "numberOfKeys": {"__symbolic": "error", "message": "Variable not initialized", "line": 53, "character": 13}}}, "InjectionToken": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "OpaqueToken"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "OpaqueToken": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "createPlatform": {"__symbolic": "function"}, "assertPlatform": {"__symbolic": "function"}, "destroyPlatform": {"__symbolic": "function"}, "getPlatform": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 34, "character": 4, "context": {"name": "_platform"}, "module": "./src/application_ref"}, "right": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 34, "character": 4, "context": {"name": "_platform"}, "module": "./src/application_ref"}}}, "thenExpression": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 34, "character": 4, "context": {"name": "_platform"}, "module": "./src/application_ref"}, "elseExpression": null}}, "PlatformRef": {"__symbolic": "class", "members": {"bootstrapModuleFactory": [{"__symbolic": "method"}], "bootstrapModule": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}]}}, "ApplicationRef": {"__symbolic": "class", "members": {"bootstrap": [{"__symbolic": "method"}], "tick": [{"__symbolic": "method"}], "attachView": [{"__symbolic": "method"}], "detachView": [{"__symbolic": "method"}]}}, "enableProdMode": {"__symbolic": "function"}, "isDevMode": {"__symbolic": "function"}, "createPlatformFactory": {"__symbolic": "function"}, "NgProbeToken": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "APP_ID": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["AppId"]}, "PACKAGE_ROOT_URL": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["Application Packages Root URL"]}, "PLATFORM_INITIALIZER": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["Platform Initializer"]}, "PLATFORM_ID": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["Platform ID"]}, "APP_BOOTSTRAP_LISTENER": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["appBootstrapListener"]}, "APP_INITIALIZER": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["Application Initializer"]}, "ApplicationInitStatus": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "APP_INITIALIZER"}]}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Optional"}}]], "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 32, "character": 69, "module": "./src/application_init"}]}]}], "runInitializers": [{"__symbolic": "method"}]}}, "NgZone": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [null]}], "run": [{"__symbolic": "method"}], "runGuarded": [{"__symbolic": "method"}], "runOutsideAngular": [{"__symbolic": "method"}], "checkStable": [{"__symbolic": "method"}], "forkInnerZoneWithAngularBehavior": [{"__symbolic": "method"}], "onEnter": [{"__symbolic": "method"}], "onLeave": [{"__symbolic": "method"}], "setHasMicrotask": [{"__symbolic": "method"}], "setHasMacrotask": [{"__symbolic": "method"}], "triggerError": [{"__symbolic": "method"}]}, "statics": {"isInAngularZone": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "Zone"}, "member": "current"}, "member": "get"}, "arguments": ["isAngularZone"]}, "right": true}}}}, "RenderComponentType": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "ViewEncapsulation"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "any"}]}]}}, "Renderer": {"__symbolic": "class", "members": {"selectRootElement": [{"__symbolic": "method"}], "createElement": [{"__symbolic": "method"}], "createViewRoot": [{"__symbolic": "method"}], "createTemplateAnchor": [{"__symbolic": "method"}], "createText": [{"__symbolic": "method"}], "projectNodes": [{"__symbolic": "method"}], "attachViewAfter": [{"__symbolic": "method"}], "detachView": [{"__symbolic": "method"}], "destroyView": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}], "listenGlobal": [{"__symbolic": "method"}], "setElementProperty": [{"__symbolic": "method"}], "setElementAttribute": [{"__symbolic": "method"}], "setBindingDebugInfo": [{"__symbolic": "method"}], "setElementClass": [{"__symbolic": "method"}], "setElementStyle": [{"__symbolic": "method"}], "invokeElementMethod": [{"__symbolic": "method"}], "setText": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "Renderer2": {"__symbolic": "class", "members": {"destroy": [{"__symbolic": "method"}], "createElement": [{"__symbolic": "method"}], "createComment": [{"__symbolic": "method"}], "createText": [{"__symbolic": "method"}], "appendChild": [{"__symbolic": "method"}], "insertBefore": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "selectRootElement": [{"__symbolic": "method"}], "parentNode": [{"__symbolic": "method"}], "nextSibling": [{"__symbolic": "method"}], "setAttribute": [{"__symbolic": "method"}], "removeAttribute": [{"__symbolic": "method"}], "addClass": [{"__symbolic": "method"}], "removeClass": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "removeStyle": [{"__symbolic": "method"}], "setProperty": [{"__symbolic": "method"}], "setValue": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}]}}, "RendererFactory2": {"__symbolic": "class", "members": {"createRenderer": [{"__symbolic": "method"}], "begin": [{"__symbolic": "method"}], "end": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}]}}, "RendererStyleFlags2": {"Important": 1, "DashCase": 2}, "RendererType2": {"__symbolic": "interface"}, "RootRenderer": {"__symbolic": "class", "members": {"renderComponent": [{"__symbolic": "method"}]}}, "COMPILER_OPTIONS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["compilerOptions"]}, "Compiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"compileModuleSync": [{"__symbolic": "method"}], "compileModuleAsync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsSync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsAsync": [{"__symbolic": "method"}], "getNgContentSelectors": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}]}}, "CompilerFactory": {"__symbolic": "class", "members": {"createCompiler": [{"__symbolic": "method"}]}}, "ModuleWithComponentFactories": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "NgModuleFactory"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵComponentFactory"}]}]}]}}, "ComponentFactory": {"__symbolic": "class", "arity": 1, "members": {"create": [{"__symbolic": "method"}]}}, "ComponentRef": {"__symbolic": "class", "arity": 1, "members": {"destroy": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}]}}, "ComponentFactoryResolver": {"__symbolic": "class", "members": {"resolveComponentFactory": [{"__symbolic": "method"}]}, "statics": {"NULL": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 29, "character": 0, "context": {"className": "_NullComponentFactoryResolver"}}}}, "ElementRef": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}]}]}}, "NgModuleFactory": {"__symbolic": "class", "arity": 1, "members": {"create": [{"__symbolic": "method"}]}}, "NgModuleRef": {"__symbolic": "class", "arity": 1, "members": {"destroy": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}]}}, "NgModuleFactoryLoader": {"__symbolic": "class", "members": {"load": [{"__symbolic": "method"}]}}, "getModuleFactory": {"__symbolic": "function"}, "QueryList": {"__symbolic": "class", "arity": 1, "members": {"map": [{"__symbolic": "method"}], "filter": [{"__symbolic": "method"}], "find": [{"__symbolic": "method"}], "reduce": [{"__symbolic": "method"}], "forEach": [{"__symbolic": "method"}], "some": [{"__symbolic": "method"}], "toArray": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "notifyOnChanges": [{"__symbolic": "method"}], "setDirty": [{"__symbolic": "method"}]}}, "SystemJsNgModuleLoader": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Optional"}}]], "parameters": [{"__symbolic": "reference", "name": "Compiler"}, {"__symbolic": "reference", "name": "SystemJsNgModuleLoaderConfig"}]}], "load": [{"__symbolic": "method"}], "loadAndCompile": [{"__symbolic": "method"}], "loadFactory": [{"__symbolic": "method"}]}}, "SystemJsNgModuleLoaderConfig": {"__symbolic": "class", "members": {}}, "TemplateRef": {"__symbolic": "class", "arity": 1, "members": {"createEmbeddedView": [{"__symbolic": "method"}]}}, "ViewContainerRef": {"__symbolic": "class", "members": {"clear": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "createEmbeddedView": [{"__symbolic": "method"}], "createComponent": [{"__symbolic": "method"}], "insert": [{"__symbolic": "method"}], "move": [{"__symbolic": "method"}], "indexOf": [{"__symbolic": "method"}], "remove": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}]}}, "EmbeddedViewRef": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "ViewRef"}, "members": {}}, "ViewRef": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ChangeDetectorRef"}, "members": {"destroy": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}]}}, "DebugElement": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "DebugNode"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "ɵu"}]}], "addChild": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "insertChildrenAfter": [{"__symbolic": "method"}], "insertBefore": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "queryAll": [{"__symbolic": "method"}], "queryAllNodes": [{"__symbolic": "method"}], "triggerEventHandler": [{"__symbolic": "method"}]}}, "DebugNode": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "DebugNode"}, {"__symbolic": "reference", "name": "ɵu"}]}]}}, "asNativeElements": {"__symbolic": "function", "parameters": ["debugEls"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 144, "character": 22, "module": "./src/debug/debug_node"}}, "getDebugNode": {"__symbolic": "function", "parameters": ["nativeNode"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Map"}}, "member": "get"}, "arguments": [{"__symbolic": "reference", "name": "nativeNode"}]}, "right": null}}, "Predicate": {"__symbolic": "interface"}, "GetTestability": {"__symbolic": "interface"}, "Testability": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "NgZone"}]}], "_watchAngularEvents": [{"__symbolic": "method"}], "increasePendingRequestCount": [{"__symbolic": "method"}], "decreasePendingRequestCount": [{"__symbolic": "method"}], "isStable": [{"__symbolic": "method"}], "_runCallbacksIfReady": [{"__symbolic": "method"}], "whenStable": [{"__symbolic": "method"}], "getPendingRequestCount": [{"__symbolic": "method"}], "findBindings": [{"__symbolic": "method"}], "findProviders": [{"__symbolic": "method"}]}}, "TestabilityRegistry": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "registerApplication": [{"__symbolic": "method"}], "getTestability": [{"__symbolic": "method"}], "getAllTestabilities": [{"__symbolic": "method"}], "getAllRootElements": [{"__symbolic": "method"}], "findTestabilityInTree": [{"__symbolic": "method"}]}}, "setTestabilityGetter": {"__symbolic": "function"}, "ChangeDetectionStrategy": {"OnPush": 0, "Default": 1}, "ChangeDetectorRef": {"__symbolic": "class", "members": {"markForCheck": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "detectChanges": [{"__symbolic": "method"}], "checkNoChanges": [{"__symbolic": "method"}], "reattach": [{"__symbolic": "method"}]}}, "CollectionChangeRecord": {"__symbolic": "interface"}, "DefaultIterableDiffer": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TrackByFunction"}]}], "forEachItem": [{"__symbolic": "method"}], "forEachOperation": [{"__symbolic": "method"}], "forEachPreviousItem": [{"__symbolic": "method"}], "forEachAddedItem": [{"__symbolic": "method"}], "forEachMovedItem": [{"__symbolic": "method"}], "forEachRemovedItem": [{"__symbolic": "method"}], "forEachIdentityChange": [{"__symbolic": "method"}], "diff": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "check": [{"__symbolic": "method"}], "_reset": [{"__symbolic": "method"}], "_mismatch": [{"__symbolic": "method"}], "_verifyReinsertion": [{"__symbolic": "method"}], "_truncate": [{"__symbolic": "method"}], "_reinsertAfter": [{"__symbolic": "method"}], "_moveAfter": [{"__symbolic": "method"}], "_addAfter": [{"__symbolic": "method"}], "_insertAfter": [{"__symbolic": "method"}], "_remove": [{"__symbolic": "method"}], "_unlink": [{"__symbolic": "method"}], "_addToMoves": [{"__symbolic": "method"}], "_addToRemovals": [{"__symbolic": "method"}], "_addIdentityChange": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "IterableChangeRecord": {"__symbolic": "interface"}, "IterableChanges": {"__symbolic": "interface"}, "IterableDiffer": {"__symbolic": "interface"}, "IterableDifferFactory": {"__symbolic": "interface"}, "IterableDiffers": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "find": [{"__symbolic": "method"}]}, "statics": {"extend": {"__symbolic": "function", "parameters": ["factories"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 186, "character": 18, "module": "./src/change_detection/differs/iterable_differs"}}}}, "KeyValueChangeRecord": {"__symbolic": "interface"}, "KeyValueChanges": {"__symbolic": "interface"}, "KeyValueDiffer": {"__symbolic": "interface"}, "KeyValueDifferFactory": {"__symbolic": "interface"}, "KeyValueDiffers": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}], "find": [{"__symbolic": "method"}]}, "statics": {"extend": {"__symbolic": "function", "parameters": ["factories"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 161, "character": 18, "module": "./src/change_detection/differs/keyvalue_differs"}}}}, "PipeTransform": {"__symbolic": "interface"}, "SimpleChange": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "boolean"}]}], "isFirstChange": [{"__symbolic": "method"}]}}, "SimpleChanges": {"__symbolic": "interface"}, "TrackByFn": {"__symbolic": "interface"}, "TrackByFunction": {"__symbolic": "interface"}, "WrappedValue": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}]}]}, "statics": {"wrap": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "WrappedValue"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}}}}, "platformCore": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 19, "character": 6, "context": {"name": "_CORE_PLATFORM_PROVIDERS"}, "module": "./src/platform_core_providers"}, "TRANSLATIONS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["Translations"]}, "TRANSLATIONS_FORMAT": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["TranslationsFormat"]}, "LOCALE_ID": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["LocaleId"]}, "MissingTranslationStrategy": {"Error": 0, "Warning": 1, "Ignore": 2}, "ApplicationModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "NgModule"}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "ɵe"}, {"provide": {"__symbolic": "reference", "name": "ApplicationRef"}, "useExisting": {"__symbolic": "reference", "name": "ɵe"}}, {"__symbolic": "reference", "name": "ApplicationInitStatus"}, {"__symbolic": "reference", "name": "Compiler"}, {"__symbolic": "reference", "name": "ɵAPP_ID_RANDOM_PROVIDER"}, {"provide": {"__symbolic": "reference", "name": "Iterable<PERSON><PERSON><PERSON>"}, "useFactory": {"__symbolic": "reference", "name": "ɵk"}}, {"provide": {"__symbolic": "reference", "name": "KeyValueDiffers"}, "useFactory": {"__symbolic": "reference", "name": "ɵl"}}, {"provide": {"__symbolic": "reference", "name": "LOCALE_ID"}, "useFactory": {"__symbolic": "reference", "name": "ɵm"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "LOCALE_ID"}]}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Optional"}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "SkipSelf"}}]]}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ApplicationRef"}]}]}}, "wtfCreateScope": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "ɵn"}, "thenExpression": {"__symbolic": "reference", "name": "ɵp"}, "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 53, "character": 31, "module": "./src/profile/profile"}}, "wtfLeave": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "ɵn"}, "thenExpression": {"__symbolic": "reference", "name": "ɵq"}, "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 65, "character": 25, "module": "./src/profile/profile"}}, "wtfStartTimeRange": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "ɵn"}, "thenExpression": {"__symbolic": "reference", "name": "ɵr"}, "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 81, "character": 34, "module": "./src/profile/profile"}}, "wtfEndTimeRange": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "ɵn"}, "thenExpression": {"__symbolic": "reference", "name": "ɵs"}, "elseExpression": {"__symbolic": "error", "message": "Function call not supported", "line": 89, "character": 81, "module": "./src/profile/profile"}}, "WtfScopeFn": {"__symbolic": "interface"}, "Type": {"__symbolic": "interface"}, "EventEmitter": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "module": "rxjs/Subject", "name": "Subject"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "boolean"}]}], "emit": [{"__symbolic": "method"}], "subscribe": [{"__symbolic": "method"}]}}, "ErrorHandler": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "boolean"}]}], "handleError": [{"__symbolic": "method"}], "_findContext": [{"__symbolic": "method"}], "_findOriginalError": [{"__symbolic": "method"}]}}, "ɵALLOW_MULTIPLE_PLATFORMS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "InjectionToken"}, "arguments": ["AllowMultipleToken"]}, "ɵAPP_ID_RANDOM_PROVIDER": {"provide": {"__symbolic": "reference", "name": "APP_ID"}, "useFactory": {"__symbolic": "reference", "name": "ɵf"}, "deps": []}, "ɵValueUnwrapper": {"__symbolic": "class", "members": {"unwrap": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}]}}, "ɵdevModeEqual": {"__symbolic": "function"}, "ɵisListLikeIterable": {"__symbolic": "function"}, "ɵChangeDetectorStatus": {"CheckOnce": 0, "Checked": 1, "CheckAlways": 2, "Detached": 3, "Errored": 4, "Destroyed": 5}, "ɵisDefaultChangeDetectionStrategy": {"__symbolic": "function", "parameters": ["changeDetectionStrategy"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "==", "left": {"__symbolic": "reference", "name": "changeDetectionStrategy"}, "right": null}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "changeDetectionStrategy"}, "right": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ChangeDetectionStrategy"}, "member": "<PERSON><PERSON><PERSON>"}}}}, "ɵConsole": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Injectable"}}], "members": {"log": [{"__symbolic": "method"}], "warn": [{"__symbolic": "method"}]}}, "ɵERROR_COMPONENT_TYPE": "ngComponentType", "ɵComponentFactory": {"__symbolic": "class", "arity": 1, "members": {"create": [{"__symbolic": "method"}]}}, "ɵCodegenComponentFactoryResolver": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵComponentFactory"}]}, {"__symbolic": "reference", "name": "ComponentFactoryResolver"}, {"__symbolic": "reference", "name": "NgModuleRef"}]}], "resolveComponentFactory": [{"__symbolic": "method"}]}}, "ɵViewMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 78, "character": 20, "module": "./src/metadata/view"}]}]}}, "ɵReflectionCapabilities": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}]}], "isReflectionEnabled": [{"__symbolic": "method"}], "factory": [{"__symbolic": "method"}], "_zipTypesAndAnnotations": [{"__symbolic": "method"}], "_ownParameters": [{"__symbolic": "method"}], "parameters": [{"__symbolic": "method"}], "_ownAnnotations": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "_ownPropMetadata": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "getter": [{"__symbolic": "method"}], "setter": [{"__symbolic": "method"}], "method": [{"__symbolic": "method"}], "importUri": [{"__symbolic": "method"}], "resourceUri": [{"__symbolic": "method"}], "resolveIdentifier": [{"__symbolic": "method"}], "resolveEnum": [{"__symbolic": "method"}]}}, "ɵDirectRenderer": {"__symbolic": "interface"}, "ɵRenderDebugInfo": {"__symbolic": "class", "members": {}}, "ɵglobal": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 18, "character": 17, "module": "./src/util"}, "right": "undefined"}, "right": {"__symbolic": "reference", "name": "window"}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 21, "character": 17, "module": "./src/util"}, "right": "undefined"}, "right": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 17, "character": 12, "context": {"name": "global"}, "module": "./src/util"}}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 19, "character": 15, "module": "./src/util"}, "right": "undefined"}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 19, "character": 46, "module": "./src/util"}, "right": "undefined"}}, "right": {"__symbolic": "binop", "operator": "instanceof", "left": {"__symbolic": "reference", "name": "self"}, "right": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 13, "character": 12, "context": {"name": "WorkerGlobalScope"}, "module": "./src/util"}}}, "right": {"__symbolic": "reference", "name": "self"}}}, "ɵlooseIdentical": {"__symbolic": "function", "parameters": ["a", "b"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "a"}, "right": {"__symbolic": "reference", "name": "b"}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 20, "module": "./src/util"}, "right": "number"}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 45, "module": "./src/util"}, "right": "number"}}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isNaN"}, "arguments": [{"__symbolic": "reference", "name": "a"}]}}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isNaN"}, "arguments": [{"__symbolic": "reference", "name": "b"}]}}}}, "ɵstringify": {"__symbolic": "function"}, "ɵmakeDecorator": {"__symbolic": "function"}, "ɵisObservable": {"__symbolic": "function", "parameters": ["obj"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "reference", "name": "obj"}}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 24, "character": 18, "module": "./src/util/lang"}, "right": "function"}}}, "ɵisPromise": {"__symbolic": "function", "parameters": ["obj"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "reference", "name": "obj"}}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 16, "character": 18, "module": "./src/util/lang"}, "right": "function"}}}, "ɵclearProviderOverrides": {"__symbolic": "function"}, "ɵoverrideProvider": {"__symbolic": "function"}, "ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR": {}, "Sanitizer": {"__symbolic": "class", "members": {"sanitize": [{"__symbolic": "method"}]}}, "SecurityContext": {"NONE": 0, "HTML": 1, "STYLE": 2, "SCRIPT": 3, "URL": 4, "RESOURCE_URL": 5}, "ɵregisterModuleFactory": {"__symbolic": "function"}, "ɵArgumentType": {"Inline": 0, "Dynamic": 1}, "ɵBindingFlags": {"TypeElementAttribute": 1, "TypeElementClass": 2, "TypeElementStyle": 4, "TypeProperty": 8, "SyntheticProperty": 16, "SyntheticHostProperty": 32, "CatSyntheticProperty": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "SyntheticProperty"}, "right": {"__symbolic": "reference", "name": "SyntheticHostProperty"}}, "Types": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "TypeElementAttribute"}, "right": {"__symbolic": "reference", "name": "TypeElementClass"}}, "right": {"__symbolic": "reference", "name": "TypeElementStyle"}}, "right": {"__symbolic": "reference", "name": "TypeProperty"}}}, "ɵDepFlags": {"None": 0, "SkipSelf": 1, "Optional": 2, "Value": 8}, "ɵEMPTY_ARRAY": [], "ɵEMPTY_MAP": {}, "ɵNodeFlags": {"None": 0, "TypeElement": 1, "TypeText": 2, "ProjectedTemplate": 4, "CatRenderNode": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "TypeElement"}, "right": {"__symbolic": "reference", "name": "TypeText"}}, "TypeNgContent": 8, "TypePipe": 16, "TypePureArray": 32, "TypePureObject": 64, "TypePurePipe": 128, "CatPureExpression": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "TypePureArray"}, "right": {"__symbolic": "reference", "name": "TypePureObject"}}, "right": {"__symbolic": "reference", "name": "TypePurePipe"}}, "TypeValueProvider": 256, "TypeClassProvider": 512, "TypeFactoryProvider": 1024, "TypeUseExistingProvider": 2048, "LazyProvider": 4096, "PrivateProvider": 8192, "TypeDirective": 16384, "Component": 32768, "CatProviderNoDirective": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "TypeValueProvider"}, "right": {"__symbolic": "reference", "name": "TypeClassProvider"}}, "right": {"__symbolic": "reference", "name": "TypeFactoryProvider"}}, "right": {"__symbolic": "reference", "name": "TypeUseExistingProvider"}}, "CatProvider": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "CatProviderNoDirective"}, "right": {"__symbolic": "reference", "name": "TypeDirective"}}, "OnInit": 65536, "OnDestroy": 131072, "DoCheck": 262144, "OnChanges": 524288, "AfterContentInit": 1048576, "AfterContentChecked": 2097152, "AfterViewInit": 4194304, "AfterViewChecked": 8388608, "EmbeddedViews": 16777216, "ComponentView": 33554432, "TypeContentQuery": 67108864, "TypeViewQuery": 134217728, "StaticQuery": 268435456, "DynamicQuery": 536870912, "CatQuery": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "TypeContentQuery"}, "right": {"__symbolic": "reference", "name": "TypeViewQuery"}}, "Types": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "binop", "operator": "|", "left": {"__symbolic": "reference", "name": "CatRenderNode"}, "right": {"__symbolic": "reference", "name": "TypeNgContent"}}, "right": {"__symbolic": "reference", "name": "TypePipe"}}, "right": {"__symbolic": "reference", "name": "CatPureExpression"}}, "right": {"__symbolic": "reference", "name": "CatProvider"}}, "right": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON>"}}}, "ɵQueryBindingType": {"First": 0, "All": 1}, "ɵQueryValueType": {"ElementRef": 0, "RenderElement": 1, "TemplateRef": 2, "ViewContainerRef": 3, "Provider": 4}, "ɵViewDefinition": {"__symbolic": "interface"}, "ɵViewFlags": {"None": 0, "OnPush": 2}, "ɵand": {"__symbolic": "function"}, "ɵccf": {"__symbolic": "function", "parameters": ["selector", "componentType", "viewDefFactory", "inputs", "outputs", "ngContentSelectors"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 45, "character": 0, "context": {"className": "ComponentFactory_"}, "module": "./src/view/refs"}}, "ɵcmf": {"__symbolic": "function", "parameters": ["ngModuleType", "bootstrapComponents", "defFactory"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 34, "character": 0, "context": {"className": "NgModuleFactory_"}, "module": "./src/view/entrypoint"}}, "ɵcrt": {"__symbolic": "function", "parameters": ["values"], "value": {"id": "$$undefined", "styles": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "values"}, "member": "styles"}, "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "values"}, "member": "encapsulation"}, "data": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "values"}, "member": "data"}}}, "ɵdid": {"__symbolic": "function"}, "ɵeld": {"__symbolic": "function"}, "ɵelementEventFullName": {"__symbolic": "function", "parameters": ["target", "name"], "value": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "target"}, "thenExpression": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "reference", "name": "target"}, "right": ":"}, "right": {"__symbolic": "reference", "name": "name"}}, "elseExpression": {"__symbolic": "reference", "name": "name"}}}, "ɵgetComponentViewDefinitionFactory": {"__symbolic": "function", "parameters": ["componentFactory"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 42, "character": 10, "module": "./src/view/refs"}}, "ɵinlineInterpolate": {"__symbolic": "function"}, "ɵinterpolate": {"__symbolic": "function"}, "ɵmod": {"__symbolic": "function"}, "ɵmpd": {"__symbolic": "function"}, "ɵncd": {"__symbolic": "function", "parameters": ["ngContentIndex", "index"], "value": {"index": -1, "parent": null, "renderParent": null, "bindingIndex": -1, "outputIndex": -1, "flags": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵNodeFlags"}, "member": "TypeNgContent"}, "childFlags": 0, "directChildFlags": 0, "childMatchedQueries": 0, "matchedQueries": {}, "matchedQueryIds": 0, "references": {}, "ngContentIndex": {"__symbolic": "reference", "name": "ngContentIndex"}, "childCount": 0, "bindings": [], "bindingFlags": 0, "outputs": [], "element": null, "provider": null, "text": null, "query": null, "ngContent": {"index": {"__symbolic": "reference", "name": "index"}}}}, "ɵnov": {"__symbolic": "function"}, "ɵpid": {"__symbolic": "function"}, "ɵprd": {"__symbolic": "function", "parameters": ["flags", "matchedQueries", "token", "value", "deps"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵt"}, "arguments": [{"__symbolic": "reference", "name": "flags"}, {"__symbolic": "reference", "name": "matchedQueries"}, 0, {"__symbolic": "reference", "name": "token"}, {"__symbolic": "reference", "name": "value"}, {"__symbolic": "reference", "name": "deps"}]}}, "ɵpad": {"__symbolic": "function", "parameters": ["argCount"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 24, "character": 9, "context": {"name": "_pureExpressionDef"}, "module": "./src/view/pure_expression"}}, "ɵpod": {"__symbolic": "function", "parameters": ["propertyNames"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 24, "character": 9, "context": {"name": "_pureExpressionDef"}, "module": "./src/view/pure_expression"}}, "ɵppd": {"__symbolic": "function", "parameters": ["argCount"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 24, "character": 9, "context": {"name": "_pureExpressionDef"}, "module": "./src/view/pure_expression"}}, "ɵqud": {"__symbolic": "function"}, "ɵted": {"__symbolic": "function"}, "ɵunv": {"__symbolic": "function"}, "ɵvid": {"__symbolic": "function"}, "AUTO_STYLE": "*", "AnimationMetadata": {"__symbolic": "interface"}, "AnimationTriggerMetadata": {"__symbolic": "interface"}, "AnimationStateMetadata": {"__symbolic": "interface"}, "AnimationTransitionMetadata": {"__symbolic": "interface"}, "AnimationKeyframesSequenceMetadata": {"__symbolic": "interface"}, "AnimationStyleMetadata": {"__symbolic": "interface"}, "AnimationAnimateMetadata": {"__symbolic": "interface"}, "AnimationSequenceMetadata": {"__symbolic": "interface"}, "AnimationGroupMetadata": {"__symbolic": "interface"}, "trigger": {"__symbolic": "function", "parameters": ["name", "definitions"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵw"}, "arguments": [{"__symbolic": "reference", "name": "name"}, {"__symbolic": "reference", "name": "definitions"}]}}, "animate": {"__symbolic": "function", "parameters": ["timings", "styles"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵx"}, "arguments": [{"__symbolic": "reference", "name": "timings"}, {"__symbolic": "reference", "name": "styles"}]}}, "group": {"__symbolic": "function", "parameters": ["steps"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵy"}, "arguments": [{"__symbolic": "reference", "name": "steps"}]}}, "sequence": {"__symbolic": "function", "parameters": ["steps"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵz"}, "arguments": [{"__symbolic": "reference", "name": "steps"}]}}, "style": {"__symbolic": "function", "parameters": ["tokens"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵba"}, "arguments": [{"__symbolic": "reference", "name": "tokens"}]}}, "state": {"__symbolic": "function", "parameters": ["name", "styles"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵbb"}, "arguments": [{"__symbolic": "reference", "name": "name"}, {"__symbolic": "reference", "name": "styles"}]}}, "keyframes": {"__symbolic": "function", "parameters": ["steps"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵbc"}, "arguments": [{"__symbolic": "reference", "name": "steps"}]}}, "transition": {"__symbolic": "function", "parameters": ["stateChangeExpr", "steps"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵbd"}, "arguments": [{"__symbolic": "reference", "name": "stateChangeExpr"}, {"__symbolic": "reference", "name": "steps"}]}}, "AnimationTransitionEvent": {"__symbolic": "interface"}, "ɵv": {"State": 0, "Transition": 1, "Sequence": 2, "Group": 3, "Animate": 4, "Keyframes": 5, "Style": 6, "Trigger": 7, "Reference": 8, "AnimateChild": 9, "AnimateRef": 10, "Query": 11, "Stagger": 12}, "ɵw": {"__symbolic": "function", "parameters": ["name", "definitions"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "<PERSON><PERSON>"}, "name": {"__symbolic": "reference", "name": "name"}, "definitions": {"__symbolic": "reference", "name": "definitions"}, "options": {}}}, "ɵx": {"__symbolic": "function", "parameters": ["timings", "styles"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Animate"}, "styles": {"__symbolic": "reference", "name": "styles"}, "timings": {"__symbolic": "reference", "name": "timings"}}}, "ɵy": {"__symbolic": "function", "parameters": ["steps", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Group"}, "steps": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "ɵz": {"__symbolic": "function", "parameters": ["steps", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Sequence"}, "steps": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "ɵba": {"__symbolic": "function", "parameters": ["tokens"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Style"}, "styles": {"__symbolic": "reference", "name": "tokens"}, "offset": null}}, "ɵbb": {"__symbolic": "function", "parameters": ["name", "styles"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "State"}, "name": {"__symbolic": "reference", "name": "name"}, "styles": {"__symbolic": "reference", "name": "styles"}}}, "ɵbc": {"__symbolic": "function", "parameters": ["steps"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Keyframes"}, "steps": {"__symbolic": "reference", "name": "steps"}}}, "ɵbd": {"__symbolic": "function", "parameters": ["stateChangeExpr", "steps", "options"], "defaults": [null, null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "ɵv"}, "member": "Transition"}, "expr": {"__symbolic": "reference", "name": "stateChangeExpr"}, "animation": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}}, "origins": {"ɵa": "./src/util/decorators", "ɵb": "./src/di/reflective_injector", "ɵc": "./src/di/reflective_provider", "ɵd": "./src/di/reflective_provider", "ɵe": "./src/application_ref", "ɵf": "./src/application_tokens", "ɵg": "./src/change_detection/change_detection", "ɵh": "./src/change_detection/change_detection", "ɵi": "./src/change_detection/differs/default_iterable_differ", "ɵj": "./src/change_detection/differs/default_keyvalue_differ", "ɵk": "./src/application_module", "ɵl": "./src/application_module", "ɵm": "./src/application_module", "ɵn": "./src/profile/profile", "ɵo": "./src/profile/wtf_impl", "ɵp": "./src/profile/wtf_impl", "ɵq": "./src/profile/wtf_impl", "ɵr": "./src/profile/wtf_impl", "ɵs": "./src/profile/wtf_impl", "ɵt": "./src/view/provider", "ɵu": "./src/view/types", "ANALYZE_FOR_ENTRY_COMPONENTS": "./src/metadata/di", "Attribute": "./src/metadata/di", "ContentChild": "./src/metadata/di", "ContentChildDecorator": "./src/metadata/di", "ContentChildren": "./src/metadata/di", "ContentChildrenDecorator": "./src/metadata/di", "Query": "./src/metadata/di", "ViewChild": "./src/metadata/di", "ViewChildDecorator": "./src/metadata/di", "ViewChildren": "./src/metadata/di", "ViewChildrenDecorator": "./src/metadata/di", "Component": "./src/metadata/directives", "ComponentDecorator": "./src/metadata/directives", "Directive": "./src/metadata/directives", "DirectiveDecorator": "./src/metadata/directives", "HostBinding": "./src/metadata/directives", "HostListener": "./src/metadata/directives", "Input": "./src/metadata/directives", "Output": "./src/metadata/directives", "Pipe": "./src/metadata/directives", "AfterContentChecked": "./src/metadata/lifecycle_hooks", "AfterContentInit": "./src/metadata/lifecycle_hooks", "AfterViewChecked": "./src/metadata/lifecycle_hooks", "AfterViewInit": "./src/metadata/lifecycle_hooks", "DoCheck": "./src/metadata/lifecycle_hooks", "OnChanges": "./src/metadata/lifecycle_hooks", "OnDestroy": "./src/metadata/lifecycle_hooks", "OnInit": "./src/metadata/lifecycle_hooks", "CUSTOM_ELEMENTS_SCHEMA": "./src/metadata/ng_module", "ModuleWithProviders": "./src/metadata/ng_module", "NO_ERRORS_SCHEMA": "./src/metadata/ng_module", "NgModule": "./src/metadata/ng_module", "SchemaMetadata": "./src/metadata/ng_module", "ViewEncapsulation": "./src/metadata/view", "Version": "./src/version", "VERSION": "./src/version", "Class": "./src/util/decorators", "ClassDefinition": "./src/util/decorators", "TypeDecorator": "./src/util/decorators", "InjectDecorator": "./src/di/metadata", "Inject": "./src/di/metadata", "OptionalDecorator": "./src/di/metadata", "Optional": "./src/di/metadata", "InjectableDecorator": "./src/di/metadata", "Injectable": "./src/di/metadata", "SelfDecorator": "./src/di/metadata", "Self": "./src/di/metadata", "SkipSelfDecorator": "./src/di/metadata", "SkipSelf": "./src/di/metadata", "HostDecorator": "./src/di/metadata", "Host": "./src/di/metadata", "forwardRef": "./src/di/forward_ref", "resolveForwardRef": "./src/di/forward_ref", "ForwardRefFn": "./src/di/forward_ref", "Injector": "./src/di/injector", "ReflectiveInjector": "./src/di/reflective_injector", "Provider": "./src/di/provider", "TypeProvider": "./src/di/provider", "ValueProvider": "./src/di/provider", "ClassProvider": "./src/di/provider", "ExistingProvider": "./src/di/provider", "FactoryProvider": "./src/di/provider", "ResolvedReflectiveFactory": "./src/di/reflective_provider", "ResolvedReflectiveProvider": "./src/di/reflective_provider", "ReflectiveKey": "./src/di/reflective_key", "InjectionToken": "./src/di/injection_token", "OpaqueToken": "./src/di/injection_token", "createPlatform": "./src/application_ref", "assertPlatform": "./src/application_ref", "destroyPlatform": "./src/application_ref", "getPlatform": "./src/application_ref", "PlatformRef": "./src/application_ref", "ApplicationRef": "./src/application_ref", "enableProdMode": "./src/application_ref", "isDevMode": "./src/application_ref", "createPlatformFactory": "./src/application_ref", "NgProbeToken": "./src/application_ref", "APP_ID": "./src/application_tokens", "PACKAGE_ROOT_URL": "./src/application_tokens", "PLATFORM_INITIALIZER": "./src/application_tokens", "PLATFORM_ID": "./src/application_tokens", "APP_BOOTSTRAP_LISTENER": "./src/application_tokens", "APP_INITIALIZER": "./src/application_init", "ApplicationInitStatus": "./src/application_init", "NgZone": "./src/zone/ng_zone", "RenderComponentType": "./src/render/api", "Renderer": "./src/render/api", "Renderer2": "./src/render/api", "RendererFactory2": "./src/render/api", "RendererStyleFlags2": "./src/render/api", "RendererType2": "./src/render/api", "RootRenderer": "./src/render/api", "COMPILER_OPTIONS": "./src/linker/compiler", "Compiler": "./src/linker/compiler", "CompilerFactory": "./src/linker/compiler", "CompilerOptions": "./src/linker/compiler", "ModuleWithComponentFactories": "./src/linker/compiler", "ComponentFactory": "./src/linker/component_factory", "ComponentRef": "./src/linker/component_factory", "ComponentFactoryResolver": "./src/linker/component_factory_resolver", "ElementRef": "./src/linker/element_ref", "NgModuleFactory": "./src/linker/ng_module_factory", "NgModuleRef": "./src/linker/ng_module_factory", "NgModuleFactoryLoader": "./src/linker/ng_module_factory_loader", "getModuleFactory": "./src/linker/ng_module_factory_loader", "QueryList": "./src/linker/query_list", "SystemJsNgModuleLoader": "./src/linker/system_js_ng_module_factory_loader", "SystemJsNgModuleLoaderConfig": "./src/linker/system_js_ng_module_factory_loader", "TemplateRef": "./src/linker/template_ref", "ViewContainerRef": "./src/linker/view_container_ref", "EmbeddedViewRef": "./src/linker/view_ref", "ViewRef": "./src/linker/view_ref", "DebugElement": "./src/debug/debug_node", "DebugNode": "./src/debug/debug_node", "asNativeElements": "./src/debug/debug_node", "getDebugNode": "./src/debug/debug_node", "Predicate": "./src/debug/debug_node", "GetTestability": "./src/testability/testability", "Testability": "./src/testability/testability", "TestabilityRegistry": "./src/testability/testability", "setTestabilityGetter": "./src/testability/testability", "ChangeDetectionStrategy": "./src/change_detection/constants", "ChangeDetectorRef": "./src/change_detection/change_detector_ref", "CollectionChangeRecord": "./src/change_detection/differs/iterable_differs", "DefaultIterableDiffer": "./src/change_detection/differs/default_iterable_differ", "IterableChangeRecord": "./src/change_detection/differs/iterable_differs", "IterableChanges": "./src/change_detection/differs/iterable_differs", "IterableDiffer": "./src/change_detection/differs/iterable_differs", "IterableDifferFactory": "./src/change_detection/differs/iterable_differs", "IterableDiffers": "./src/change_detection/differs/iterable_differs", "KeyValueChangeRecord": "./src/change_detection/differs/keyvalue_differs", "KeyValueChanges": "./src/change_detection/differs/keyvalue_differs", "KeyValueDiffer": "./src/change_detection/differs/keyvalue_differs", "KeyValueDifferFactory": "./src/change_detection/differs/keyvalue_differs", "KeyValueDiffers": "./src/change_detection/differs/keyvalue_differs", "NgIterable": "./src/change_detection/differs/iterable_differs", "PipeTransform": "./src/change_detection/pipe_transform", "SimpleChange": "./src/change_detection/change_detection_util", "SimpleChanges": "./src/metadata/lifecycle_hooks", "TrackByFn": "./src/change_detection/differs/iterable_differs", "TrackByFunction": "./src/change_detection/differs/iterable_differs", "WrappedValue": "./src/change_detection/change_detection_util", "platformCore": "./src/platform_core_providers", "TRANSLATIONS": "./src/i18n/tokens", "TRANSLATIONS_FORMAT": "./src/i18n/tokens", "LOCALE_ID": "./src/i18n/tokens", "MissingTranslationStrategy": "./src/i18n/tokens", "ApplicationModule": "./src/application_module", "wtfCreateScope": "./src/profile/profile", "wtfLeave": "./src/profile/profile", "wtfStartTimeRange": "./src/profile/profile", "wtfEndTimeRange": "./src/profile/profile", "WtfScopeFn": "./src/profile/wtf_impl", "Type": "./src/type", "EventEmitter": "./src/event_emitter", "ErrorHandler": "./src/error_handler", "ɵALLOW_MULTIPLE_PLATFORMS": "./src/application_ref", "ɵAPP_ID_RANDOM_PROVIDER": "./src/application_tokens", "ɵValueUnwrapper": "./src/change_detection/change_detection_util", "ɵdevModeEqual": "./src/change_detection/change_detection_util", "ɵisListLikeIterable": "./src/change_detection/change_detection_util", "ɵChangeDetectorStatus": "./src/change_detection/constants", "ɵisDefaultChangeDetectionStrategy": "./src/change_detection/constants", "ɵConsole": "./src/console", "ɵERROR_COMPONENT_TYPE": "./src/errors", "ɵComponentFactory": "./src/linker/component_factory", "ɵCodegenComponentFactoryResolver": "./src/linker/component_factory_resolver", "ɵViewMetadata": "./src/metadata/view", "ɵReflectionCapabilities": "./src/reflection/reflection_capabilities", "ɵGetterFn": "./src/reflection/types", "ɵMethodFn": "./src/reflection/types", "ɵSetterFn": "./src/reflection/types", "ɵDirectRenderer": "./src/render/api", "ɵRenderDebugInfo": "./src/render/api", "ɵglobal": "./src/util", "ɵlooseIdentical": "./src/util", "ɵstringify": "./src/util", "ɵmakeDecorator": "./src/util/decorators", "ɵisObservable": "./src/util/lang", "ɵisPromise": "./src/util/lang", "ɵclearProviderOverrides": "./src/view/entrypoint", "ɵoverrideProvider": "./src/view/entrypoint", "ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR": "./src/view/provider", "Sanitizer": "./src/security", "SecurityContext": "./src/security", "ɵregisterModuleFactory": "./src/linker/ng_module_factory_loader", "ɵArgumentType": "./src/view/types", "ɵBindingFlags": "./src/view/types", "ɵDepFlags": "./src/view/types", "ɵEMPTY_ARRAY": "./src/view/util", "ɵEMPTY_MAP": "./src/view/util", "ɵNodeFlags": "./src/view/types", "ɵQueryBindingType": "./src/view/types", "ɵQueryValueType": "./src/view/types", "ɵViewDefinition": "./src/view/types", "ɵViewFlags": "./src/view/types", "ɵand": "./src/view/element", "ɵccf": "./src/view/refs", "ɵcmf": "./src/view/entrypoint", "ɵcrt": "./src/view/util", "ɵdid": "./src/view/provider", "ɵeld": "./src/view/element", "ɵelementEventFullName": "./src/view/util", "ɵgetComponentViewDefinitionFactory": "./src/view/refs", "ɵinlineInterpolate": "./src/view/util", "ɵinterpolate": "./src/view/util", "ɵmod": "./src/view/ng_module", "ɵmpd": "./src/view/ng_module", "ɵncd": "./src/view/ng_content", "ɵnov": "./src/view/refs", "ɵpid": "./src/view/provider", "ɵprd": "./src/view/provider", "ɵpad": "./src/view/pure_expression", "ɵpod": "./src/view/pure_expression", "ɵppd": "./src/view/pure_expression", "ɵqud": "./src/view/query", "ɵted": "./src/view/text", "ɵunv": "./src/view/util", "ɵvid": "./src/view/view", "AUTO_STYLE": "./src/animation/animation_metadata_wrapped", "AnimationMetadata": "./src/animation/animation_metadata_wrapped", "AnimationTriggerMetadata": "./src/animation/animation_metadata_wrapped", "AnimationStateMetadata": "./src/animation/animation_metadata_wrapped", "AnimationTransitionMetadata": "./src/animation/animation_metadata_wrapped", "AnimationKeyframesSequenceMetadata": "./src/animation/animation_metadata_wrapped", "AnimationStyleMetadata": "./src/animation/animation_metadata_wrapped", "AnimationAnimateMetadata": "./src/animation/animation_metadata_wrapped", "AnimationSequenceMetadata": "./src/animation/animation_metadata_wrapped", "AnimationGroupMetadata": "./src/animation/animation_metadata_wrapped", "trigger": "./src/animation/animation_metadata_wrapped", "animate": "./src/animation/animation_metadata_wrapped", "group": "./src/animation/animation_metadata_wrapped", "sequence": "./src/animation/animation_metadata_wrapped", "style": "./src/animation/animation_metadata_wrapped", "state": "./src/animation/animation_metadata_wrapped", "keyframes": "./src/animation/animation_metadata_wrapped", "transition": "./src/animation/animation_metadata_wrapped", "AnimationTransitionEvent": "./src/animation/animation_metadata_wrapped", "ɵv": "./src/animation/dsl", "ɵw": "./src/animation/dsl", "ɵx": "./src/animation/dsl", "ɵy": "./src/animation/dsl", "ɵz": "./src/animation/dsl", "ɵba": "./src/animation/dsl", "ɵbb": "./src/animation/dsl", "ɵbc": "./src/animation/dsl", "ɵbd": "./src/animation/dsl"}, "importAs": "@angular/core"}