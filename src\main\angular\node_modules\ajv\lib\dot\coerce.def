{{## def.coerceType:
  {{
    var $dataType = 'dataType' + $lvl
      , $coerced = 'coerced' + $lvl;
  }}
  var {{=$dataType}} = typeof {{=$data}};
  {{? it.opts.coerceTypes == 'array'}}
    if ({{=$dataType}} == 'object' && Array.isArray({{=$data}})) {{=$dataType}} = 'array';
  {{?}}

  var {{=$coerced}} = undefined;

  {{ var $bracesCoercion = ''; }}
  {{~ $coerceToTypes:$type:$i }}
    {{? $i }}
      if ({{=$coerced}} === undefined) {
      {{ $bracesCoercion += '}'; }}
    {{?}}

    {{? it.opts.coerceTypes == 'array' && $type != 'array' }}
      if ({{=$dataType}} == 'array' && {{=$data}}.length == 1) {
        {{=$coerced}} = {{=$data}} = {{=$data}}[0];
        {{=$dataType}} = typeof {{=$data}};
        /*if ({{=$dataType}} == 'object' && Array.isArray({{=$data}})) {{=$dataType}} = 'array';*/
      }
    {{?}}

    {{? $type == 'string' }}
      if ({{=$dataType}} == 'number' || {{=$dataType}} == 'boolean')
        {{=$coerced}} = '' + {{=$data}};
      else if ({{=$data}} === null) {{=$coerced}} = '';
    {{?? $type == 'number' || $type == 'integer' }}
      if ({{=$dataType}} == 'boolean' || {{=$data}} === null
          || ({{=$dataType}} == 'string' && {{=$data}} && {{=$data}} == +{{=$data}}
          {{? $type == 'integer' }} && !({{=$data}} % 1){{?}}))
        {{=$coerced}} = +{{=$data}};
    {{?? $type == 'boolean' }}
      if ({{=$data}} === 'false' || {{=$data}} === 0 || {{=$data}} === null)
        {{=$coerced}} = false;
      else if ({{=$data}} === 'true' || {{=$data}} === 1)
        {{=$coerced}} = true;
    {{?? $type == 'null' }}
      if ({{=$data}} === '' || {{=$data}} === 0 || {{=$data}} === false)
        {{=$coerced}} = null;
    {{?? it.opts.coerceTypes == 'array' && $type == 'array' }}
      if ({{=$dataType}} == 'string' || {{=$dataType}} == 'number' || {{=$dataType}} == 'boolean' || {{=$data}} == null)
        {{=$coerced}} = [{{=$data}}];
    {{?}}
  {{~}}

  {{= $bracesCoercion }}

  if ({{=$coerced}} === undefined) {
    {{# def.error:'type' }}
  } else {
    {{# def.setParentData }}
    {{=$data}} = {{=$coerced}};
    {{? !$dataLvl }}if ({{=$parentData}} !== undefined){{?}}
      {{=$parentData}}[{{=$parentDataProperty}}] = {{=$coerced}};
  }
#}}
