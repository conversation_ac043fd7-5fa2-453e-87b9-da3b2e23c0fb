# Installation
> `npm install --save @types/jasmine`

# Summary
This package contains type definitions for Jasmine 2.5 (http://jasmine.github.io/).

# Details
Files were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/types-2.0/jasmine

Additional Details
 * Last updated: Mon, 14 Nov 2016 19:34:55 GMT
 * File structure: Global
 * Library Dependencies: none
 * Module Dependencies: none
 * Global values: afterAll, afterEach, beforeAll, beforeEach, describe, expect, fail, fdescribe, fit, it, jasmine, pending, runs, spyOn, waits, waitsFor, xdescribe, xit

# Credits
These definitions were written by <PERSON> <https://github.com/b<PERSON><PERSON><PERSON>/>, <PERSON> <https://github.com/theodorejb>, <PERSON> <https://github.com/davidparsson/>.
