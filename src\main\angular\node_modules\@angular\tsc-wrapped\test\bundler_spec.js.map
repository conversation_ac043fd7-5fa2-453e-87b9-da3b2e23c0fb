{"version": 3, "file": "bundler_spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/bundler_spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,2BAA6B;AAC7B,+BAAiC;AAEjC,0CAAoE;AACpE,8CAAmD;AAGnD,uDAAmD;AAEnD,QAAQ,CAAC,kBAAkB,EAAE;IAE3B,EAAE,CAAC,2CAA2C,EAAE;QAC9C,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE,sBAAc,CAAC,CAAC;QAC5D,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACnE,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC;YAC3D,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI;SAC7E,CAAC,CAAC;QAEH,IAAM,WAAW,GAAG,WAAW,CAAC;QAChC,IAAM,WAAW,GAAG,iBAAiB,CAAC;QACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;aAC/B,IAAI,EAAE;aACN,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC,EAAC,IAAI,MAAA,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,CAAC,EAA9C,CAA8C,CAAC,CAAC;aACnE,OAAO,CAAC;YACP,EAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAC,EAAE,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAC;YAC5E,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAC,EAAE,EAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAC;YAChF,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAC,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAC;YACxE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAC,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAC;SACnE,CAAC,CAAC;QACP,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAC9B,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAC;YAC5D,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAC;SAC7D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE;YAC1C,KAAK,EAAE;gBACL,UAAU,EAAE,oDAEX;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,gJAGX;oBACD,QAAQ,EAAE,6OAMT;oBACD,KAAK,EAAE;wBACL,UAAU,EAAE,yPAMX;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACnE,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC;YAC3D,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI;SAC7E,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;YAC9B,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAC;YAC5D,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAC;SACnE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE;QAChD,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE;YAC1C,UAAU,EAAE,mDAEX;YACD,SAAS,EAAE,EAAC,SAAS,EAAE,4BAA4B,EAAC;SACrD,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAE3C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAC,YAAY,EAAE,gBAAgB,EAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE;QAC7C,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE;YAC1C,UAAU,EAAE,0EAGX;YACD,QAAQ,EAAE,mGAKT;YACD,QAAQ,EAAE,4CAET;SACF,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,+DAA+D;QAC/D,MAAM,CAAE,MAAM,CAAC,QAAQ,CAAC,QAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE;QACvD,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE;YAC1C,UAAU,EAAE,sEAGX;YACD,MAAM,EAAE,mEAGP;YACD,MAAM,EAAE,sCAEP;YACD,MAAM,EAAE,kFAGP;SACF,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACzE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8DAA8D,EAAE;QACjE,IAAM,IAAI,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE;YAC1C,UAAU,EAAE,0CAEX;YACD,QAAQ,EAAE,mGAKT;YACD,QAAQ,EAAE,uCAET;SACF,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAC5E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAC,CAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;IAGE,+BAAoB,OAAe,EAAU,SAAoB;QAA7C,YAAO,GAAP,OAAO,CAAQ;QAAU,cAAS,GAAT,SAAS,CAAW;QAFjE,cAAS,GAAG,IAAI,6BAAiB,EAAE,CAAC;IAEgC,CAAC;IAErE,8CAAc,GAAd,UAAe,UAAkB;QAC/B,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC;QAC7D,IAAM,IAAI,GAAG,uBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5C,EAAE,CAAC,CAAC,OAAO,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC;YAC5B,IAAM,UAAU,GAAG,EAAE,CAAC,gBAAgB,CAClC,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACpF,IAAM,WAAW,GAAqB,UAAkB,CAAC,gBAAgB,CAAC;YAC1E,EAAE,CAAC,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtC,MAAM,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACjD,CAAC;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACH,4BAAC;AAAD,CAAC,AAnBD,IAmBC;AAnBY,sDAAqB;AAsBrB,QAAA,cAAc,GAAG;IAC5B,KAAK,EAAE;QACL,UAAU,EAAE,4CAEX;QACD,KAAK,EAAE;YACL,UAAU,EAAE,oIAGX;YACD,QAAQ,EAAE,wLAKT;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,kMAKX;aACF;SACF;KACF;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport {MetadataBundler, MetadataBundlerHost} from '../src/bundler';\nimport {MetadataCollector} from '../src/collector';\nimport {ModuleMetadata} from '../src/schema';\n\nimport {Directory, open} from './typescript.mocks';\n\ndescribe('metadata bundler', () => {\n\n  it('should be able to bundle a simple library', () => {\n    const host = new MockStringBundlerHost('/', SIMPLE_LIBRARY);\n    const bundler = new MetadataBundler('/lib/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n    expect(Object.keys(result.metadata.metadata).sort()).toEqual([\n      'ONE_CLASSES', 'One', 'OneMore', 'TWO_CLASSES', 'Two', 'TwoMore', 'ɵa', 'ɵb'\n    ]);\n\n    const originalOne = './src/one';\n    const originalTwo = './src/two/index';\n    expect(Object.keys(result.metadata.origins)\n               .sort()\n               .map(name => ({name, value: result.metadata.origins[name]})))\n        .toEqual([\n          {name: 'ONE_CLASSES', value: originalOne}, {name: 'One', value: originalOne},\n          {name: 'OneMore', value: originalOne}, {name: 'TWO_CLASSES', value: originalTwo},\n          {name: 'Two', value: originalTwo}, {name: 'TwoMore', value: originalTwo},\n          {name: 'ɵa', value: originalOne}, {name: 'ɵb', value: originalTwo}\n        ]);\n    expect(result.privates).toEqual([\n      {privateName: 'ɵa', name: 'PrivateOne', module: originalOne},\n      {privateName: 'ɵb', name: 'PrivateTwo', module: originalTwo}\n    ]);\n  });\n\n  it('should be able to bundle an oddly constructed library', () => {\n    const host = new MockStringBundlerHost('/', {\n      'lib': {\n        'index.ts': `\n          export * from './src/index';\n        `,\n        'src': {\n          'index.ts': `\n            export {One, OneMore, ONE_CLASSES} from './one';\n            export {Two, TwoMore, TWO_CLASSES} from './two/index';\n          `,\n          'one.ts': `\n            class One {}\n            class OneMore extends One {}\n            class PrivateOne {}\n            const ONE_CLASSES = [One, OneMore, PrivateOne];\n            export {One, OneMore, PrivateOne, ONE_CLASSES};\n          `,\n          'two': {\n            'index.ts': `\n              class Two {}\n              class TwoMore extends Two {}\n              class PrivateTwo {}\n              const TWO_CLASSES = [Two, TwoMore, PrivateTwo];\n              export {Two, TwoMore, PrivateTwo, TWO_CLASSES};\n            `\n          }\n        }\n      }\n    });\n    const bundler = new MetadataBundler('/lib/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n    expect(Object.keys(result.metadata.metadata).sort()).toEqual([\n      'ONE_CLASSES', 'One', 'OneMore', 'TWO_CLASSES', 'Two', 'TwoMore', 'ɵa', 'ɵb'\n    ]);\n    expect(result.privates).toEqual([\n      {privateName: 'ɵa', name: 'PrivateOne', module: './src/one'},\n      {privateName: 'ɵb', name: 'PrivateTwo', module: './src/two/index'}\n    ]);\n  });\n\n  it('should not output windows paths in metadata', () => {\n    const host = new MockStringBundlerHost('/', {\n      'index.ts': `\n        export * from './exports/test';\n      `,\n      'exports': {'test.ts': `export class TestExport {}`}\n    });\n    const bundler = new MetadataBundler('/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n\n    expect(result.metadata.origins).toEqual({'TestExport': './exports/test'});\n  });\n\n  it('should convert re-exported to the export', () => {\n    const host = new MockStringBundlerHost('/', {\n      'index.ts': `\n        export * from './bar';\n        export * from './foo';\n      `,\n      'bar.ts': `\n        import {Foo} from './foo';\n        export class Bar extends Foo {\n\n        }\n      `,\n      'foo.ts': `\n        export {Foo} from 'foo';\n      `\n    });\n    const bundler = new MetadataBundler('/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n    // Expect the extends reference to refer to the imported module\n    expect((result.metadata.metadata as any).Bar.extends.module).toEqual('foo');\n    expect(result.privates).toEqual([]);\n  });\n\n  it('should treat import then export as a simple export', () => {\n    const host = new MockStringBundlerHost('/', {\n      'index.ts': `\n        export * from './a';\n        export * from './c';\n      `,\n      'a.ts': `\n        import { B } from './b';\n        export { B };\n      `,\n      'b.ts': `\n        export class B { }\n      `,\n      'c.ts': `\n        import { B } from './b';\n        export class C extends B { }\n      `\n    });\n    const bundler = new MetadataBundler('/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n    expect(Object.keys(result.metadata.metadata).sort()).toEqual(['B', 'C']);\n    expect(result.privates).toEqual([]);\n  });\n\n  it('should be able to bundle a private from a un-exported module', () => {\n    const host = new MockStringBundlerHost('/', {\n      'index.ts': `\n        export * from './foo';\n      `,\n      'foo.ts': `\n        import {Bar} from './bar';\n        export class Foo extends Bar {\n\n        }\n      `,\n      'bar.ts': `\n        export class Bar {}\n      `\n    });\n    const bundler = new MetadataBundler('/index', undefined, host);\n    const result = bundler.getMetadataBundle();\n    expect(Object.keys(result.metadata.metadata).sort()).toEqual(['Foo', 'ɵa']);\n    expect(result.privates).toEqual([{privateName: 'ɵa', name: 'Bar', module: './bar'}]);\n  });\n});\n\nexport class MockStringBundlerHost implements MetadataBundlerHost {\n  collector = new MetadataCollector();\n\n  constructor(private dirName: string, private directory: Directory) {}\n\n  getMetadataFor(moduleName: string): ModuleMetadata {\n    const fileName = path.join(this.dirName, moduleName) + '.ts';\n    const text = open(this.directory, fileName);\n    if (typeof text == 'string') {\n      const sourceFile = ts.createSourceFile(\n          fileName, text, ts.ScriptTarget.Latest, /* setParent */ true, ts.ScriptKind.TS);\n      const diagnostics: ts.Diagnostic[] = (sourceFile as any).parseDiagnostics;\n      if (diagnostics && diagnostics.length) {\n        throw Error('Unexpected syntax error in test');\n      }\n      const result = this.collector.getMetadata(sourceFile);\n      return result;\n    }\n  }\n}\n\n\nexport const SIMPLE_LIBRARY = {\n  'lib': {\n    'index.ts': `\n      export * from './src/index';\n    `,\n    'src': {\n      'index.ts': `\n        export {One, OneMore, ONE_CLASSES} from './one';\n        export {Two, TwoMore, TWO_CLASSES} from './two/index';\n      `,\n      'one.ts': `\n        export class One {}\n        export class OneMore extends One {}\n        export class PrivateOne {}\n        export const ONE_CLASSES = [One, OneMore, PrivateOne];\n      `,\n      'two': {\n        'index.ts': `\n          export class Two {}\n          export class TwoMore extends Two {}\n          export class PrivateTwo {}\n          export const TWO_CLASSES = [Two, TwoMore, PrivateTwo];\n        `\n      }\n    }\n  }\n};"]}