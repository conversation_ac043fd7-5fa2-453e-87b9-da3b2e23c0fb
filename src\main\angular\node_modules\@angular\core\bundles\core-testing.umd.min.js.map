{"version": 3, "file": "core-testing.umd.min.js", "sources": ["../../../../packages/core/testing/src/test_bed.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/core/testing/src/component_fixture.ts", "../../../../packages/core/testing/src/fake_async.ts", "../../../../packages/core/testing/src/async_test_completer.ts", "../../../../packages/core/testing/src/test_compiler.ts", "../../../../packages/core/testing/src/before_each.ts", "../../../../packages/core/testing/src/private_export_testing.ts", "../../../../packages/core/testing/src/async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ApplicationInitStatus, CompilerOptions, Component, Directive, InjectionToken, Injector, ModuleWithComponentFactories, NgModule, NgModuleFactory, NgModuleRef, NgZone, Optional, Pipe, PlatformRef, Provider, ReflectiveInjector, SchemaMetadata, SkipSelf, Type, ɵDepFlags as DepFlags, ɵERROR_COMPONENT_TYPE, ɵNodeFlags as NodeFlags, ɵclearProviderOverrides as clearProviderOverrides, ɵoverrideProvider as overrideProvider, ɵstringify as stringify} from '@angular/core';\n\nimport {AsyncTestCompleter} from './async_test_completer';\nimport {ComponentFixture} from './component_fixture';\nimport {MetadataOverride} from './metadata_override';\nimport {TestingCompiler, TestingCompilerFactory} from './test_compiler';\n\nconst UNDEFINED = new Object();\n\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @experimental\n */\nexport class TestComponentRenderer {\n  insertRootElement(rootElementId: string) {}\n}\n\nlet _nextRootElementId = 0;\n\n/**\n * @experimental\n */\nexport const ComponentFixtureAutoDetect =\n    new InjectionToken<boolean[]>('ComponentFixtureAutoDetect');\n\n/**\n * @experimental\n */\nexport const ComponentFixtureNoNgZone = new InjectionToken<boolean[]>('ComponentFixtureNoNgZone');\n\n/**\n * @experimental\n */\nexport type TestModuleMetadata = {\n  providers?: any[],\n  declarations?: any[],\n  imports?: any[],\n  schemas?: Array<SchemaMetadata|any[]>,\n};\n\n/**\n * @whatItDoes Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n * @description\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @stable\n */\nexport class TestBed implements Injector {\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  static initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]): TestBed {\n    const testBed = getTestBed();\n    testBed.initTestEnvironment(ngModule, platform, aotSummaries);\n    return testBed;\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  static resetTestEnvironment() { getTestBed().resetTestEnvironment(); }\n\n  static resetTestingModule(): typeof TestBed {\n    getTestBed().resetTestingModule();\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default compiler providers and settings\n   * which are defined in test_injector.js\n   */\n  static configureCompiler(config: {providers?: any[]; useJit?: boolean;}): typeof TestBed {\n    getTestBed().configureCompiler(config);\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef: TestModuleMetadata): typeof TestBed {\n    getTestBed().configureTestingModule(moduleDef);\n    return TestBed;\n  }\n\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents(): Promise<any> { return getTestBed().compileComponents(); }\n\n  static overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): typeof TestBed {\n    getTestBed().overrideModule(ngModule, override);\n    return TestBed;\n  }\n\n  static overrideComponent(component: Type<any>, override: MetadataOverride<Component>):\n      typeof TestBed {\n    getTestBed().overrideComponent(component, override);\n    return TestBed;\n  }\n\n  static overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>):\n      typeof TestBed {\n    getTestBed().overrideDirective(directive, override);\n    return TestBed;\n  }\n\n  static overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): typeof TestBed {\n    getTestBed().overridePipe(pipe, override);\n    return TestBed;\n  }\n\n  static overrideTemplate(component: Type<any>, template: string): typeof TestBed {\n    getTestBed().overrideComponent(component, {set: {template, templateUrl: null !}});\n    return TestBed;\n  }\n\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  static overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  static overrideProvider(token: any, provider: {useValue: any;}): void;\n  static overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): typeof TestBed {\n    getTestBed().overrideProvider(token, provider as any);\n    return TestBed;\n  }\n\n  static get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    return getTestBed().get(token, notFoundValue);\n  }\n\n  static createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    return getTestBed().createComponent(component);\n  }\n\n  private _instantiated: boolean = false;\n\n  private _compiler: TestingCompiler = null !;\n  private _moduleRef: NgModuleRef<any> = null !;\n  private _moduleFactory: NgModuleFactory<any> = null !;\n\n  private _compilerOptions: CompilerOptions[] = [];\n\n  private _moduleOverrides: [Type<any>, MetadataOverride<NgModule>][] = [];\n  private _componentOverrides: [Type<any>, MetadataOverride<Component>][] = [];\n  private _directiveOverrides: [Type<any>, MetadataOverride<Directive>][] = [];\n  private _pipeOverrides: [Type<any>, MetadataOverride<Pipe>][] = [];\n\n  private _providers: Provider[] = [];\n  private _declarations: Array<Type<any>|any[]|any> = [];\n  private _imports: Array<Type<any>|any[]|any> = [];\n  private _schemas: Array<SchemaMetadata|any[]> = [];\n  private _activeFixtures: ComponentFixture<any>[] = [];\n\n  private _aotSummaries: () => any[] = () => [];\n\n  platform: PlatformRef = null !;\n\n  ngModule: Type<any>|Type<any>[] = null !;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    this.platform = platform;\n    this.ngModule = ngModule;\n    if (aotSummaries) {\n      this._aotSummaries = aotSummaries;\n    }\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this.platform = null !;\n    this.ngModule = null !;\n    this._aotSummaries = () => [];\n  }\n\n  resetTestingModule() {\n    clearProviderOverrides();\n    this._compiler = null !;\n    this._moduleOverrides = [];\n    this._componentOverrides = [];\n    this._directiveOverrides = [];\n    this._pipeOverrides = [];\n\n    this._moduleRef = null !;\n    this._moduleFactory = null !;\n    this._compilerOptions = [];\n    this._providers = [];\n    this._declarations = [];\n    this._imports = [];\n    this._schemas = [];\n    this._instantiated = false;\n    this._activeFixtures.forEach((fixture) => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        console.error('Error during cleanup of component', fixture.componentInstance);\n      }\n    });\n    this._activeFixtures = [];\n  }\n\n  configureCompiler(config: {providers?: any[], useJit?: boolean}) {\n    this._assertNotInstantiated('TestBed.configureCompiler', 'configure the compiler');\n    this._compilerOptions.push(config);\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata) {\n    this._assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    if (moduleDef.providers) {\n      this._providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.declarations) {\n      this._declarations.push(...moduleDef.declarations);\n    }\n    if (moduleDef.imports) {\n      this._imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.schemas) {\n      this._schemas.push(...moduleDef.schemas);\n    }\n  }\n\n  compileComponents(): Promise<any> {\n    if (this._moduleFactory || this._instantiated) {\n      return Promise.resolve(null);\n    }\n\n    const moduleType = this._createCompilerAndModule();\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType)\n        .then((moduleAndComponentFactories) => {\n          this._moduleFactory = moduleAndComponentFactories.ngModuleFactory;\n        });\n  }\n\n  private _initIfNeeded() {\n    if (this._instantiated) {\n      return;\n    }\n    if (!this._moduleFactory) {\n      try {\n        const moduleType = this._createCompilerAndModule();\n        this._moduleFactory =\n            this._compiler.compileModuleAndAllComponentsSync(moduleType).ngModuleFactory;\n      } catch (e) {\n        if (getComponentType(e)) {\n          throw new Error(\n              `This test module uses the component ${stringify(getComponentType(e))} which is using a \"templateUrl\" or \"styleUrls\", but they were never compiled. ` +\n              `Please call \"TestBed.compileComponents\" before your test.`);\n        } else {\n          throw e;\n        }\n      }\n    }\n    const ngZone = new NgZone({enableLongStackTrace: true});\n    const ngZoneInjector = ReflectiveInjector.resolveAndCreate(\n        [{provide: NgZone, useValue: ngZone}], this.platform.injector);\n    this._moduleRef = this._moduleFactory.create(ngZoneInjector);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core. So casting to any\n    // before accessing it.\n    (this._moduleRef.injector.get(ApplicationInitStatus) as any).runInitializers();\n    this._instantiated = true;\n  }\n\n  private _createCompilerAndModule(): Type<any> {\n    const providers = this._providers.concat([{provide: TestBed, useValue: this}]);\n    const declarations = this._declarations;\n    const imports = [this.ngModule, this._imports];\n    const schemas = this._schemas;\n\n    \n    class DynamicTestModule {\n    static decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{providers, declarations, imports, schemas}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\n    const compilerFactory: TestingCompilerFactory =\n        this.platform.injector.get(TestingCompilerFactory);\n    this._compiler =\n        compilerFactory.createTestingCompiler(this._compilerOptions.concat([{useDebug: true}]));\n    this._compiler.loadAotSummaries(this._aotSummaries);\n    this._moduleOverrides.forEach((entry) => this._compiler.overrideModule(entry[0], entry[1]));\n    this._componentOverrides.forEach(\n        (entry) => this._compiler.overrideComponent(entry[0], entry[1]));\n    this._directiveOverrides.forEach(\n        (entry) => this._compiler.overrideDirective(entry[0], entry[1]));\n    this._pipeOverrides.forEach((entry) => this._compiler.overridePipe(entry[0], entry[1]));\n    return DynamicTestModule;\n  }\n\n  private _assertNotInstantiated(methodName: string, methodDescription: string) {\n    if (this._instantiated) {\n      throw new Error(\n          `Cannot ${methodDescription} when the test module has already been instantiated. ` +\n          `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n\n  get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    this._initIfNeeded();\n    if (token === TestBed) {\n      return this;\n    }\n    // Tests can inject things from the ng module and from the compiler,\n    // but the ng module can't inject things from the compiler and vice versa.\n    const result = this._moduleRef.injector.get(token, UNDEFINED);\n    return result === UNDEFINED ? this._compiler.injector.get(token, notFoundValue) : result;\n  }\n\n  execute(tokens: any[], fn: Function, context?: any): any {\n    this._initIfNeeded();\n    const params = tokens.map(t => this.get(t));\n    return fn.apply(context, params);\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this._assertNotInstantiated('overrideModule', 'override module metadata');\n    this._moduleOverrides.push([ngModule, override]);\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this._assertNotInstantiated('overrideComponent', 'override component metadata');\n    this._componentOverrides.push([component, override]);\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this._assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this._directiveOverrides.push([directive, override]);\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this._assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this._pipeOverrides.push([pipe, override]);\n  }\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  overrideProvider(token: any, provider: {useValue: any;}): void;\n  overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): void {\n    let flags: NodeFlags = 0;\n    let value: any;\n    if (provider.useFactory) {\n      flags |= NodeFlags.TypeFactoryProvider;\n      value = provider.useFactory;\n    } else {\n      flags |= NodeFlags.TypeValueProvider;\n      value = provider.useValue;\n    }\n    const deps = (provider.deps || []).map((dep) => {\n      let depFlags: DepFlags = DepFlags.None;\n      let depToken: any;\n      if (Array.isArray(dep)) {\n        dep.forEach((entry: any) => {\n          if (entry instanceof Optional) {\n            depFlags |= DepFlags.Optional;\n          } else if (entry instanceof SkipSelf) {\n            depFlags |= DepFlags.SkipSelf;\n          } else {\n            depToken = entry;\n          }\n        });\n      } else {\n        depToken = dep;\n      }\n      return [depFlags, depToken];\n    });\n    overrideProvider({token, flags, deps, value});\n  }\n\n  createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    this._initIfNeeded();\n    const componentFactory = this._compiler.getComponentFactory(component);\n\n    if (!componentFactory) {\n      throw new Error(\n          `Cannot create the component ${stringify(component)} as it was not imported into the testing module!`);\n    }\n\n    const noNgZone = this.get(ComponentFixtureNoNgZone, false);\n    const autoDetect: boolean = this.get(ComponentFixtureAutoDetect, false);\n    const ngZone: NgZone = noNgZone ? null : this.get(NgZone, null);\n    const testComponentRenderer: TestComponentRenderer = this.get(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n\n    const initComponent = () => {\n      const componentRef =\n          componentFactory.create(Injector.NULL, [], `#${rootElId}`, this._moduleRef);\n      return new ComponentFixture<T>(componentRef, ngZone, autoDetect);\n    };\n\n    const fixture = !ngZone ? initComponent() : ngZone.run(initComponent);\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n}\n\nlet _testBed: TestBed = null !;\n\n/**\n * @experimental\n */\nexport function getTestBed() {\n  return _testBed = _testBed || new TestBed();\n}\n\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * Notes:\n * - inject is currently a function because of some Traceur limitation the syntax should\n * eventually\n *   becomes `it('...', @Inject (object: AClass, async: AsyncTestCompleter) => { ... });`\n *\n * @stable\n */\nexport function inject(tokens: any[], fn: Function): () => any {\n  const testBed = getTestBed();\n  if (tokens.indexOf(AsyncTestCompleter) >= 0) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      // Return an async test method that returns a Promise if AsyncTestCompleter is one of\n      // the injected tokens.\n      return testBed.compileComponents().then(() => {\n        const completer: AsyncTestCompleter = testBed.get(AsyncTestCompleter);\n        testBed.execute(tokens, fn, this);\n        return completer.promise;\n      });\n    };\n  } else {\n    // Not using an arrow function to preserve context passed from call site\n    return function() { return testBed.execute(tokens, fn, this); };\n  }\n}\n\n/**\n * @experimental\n */\nexport class InjectSetupWrapper {\n  constructor(private _moduleDef: () => TestModuleMetadata) {}\n\n  private _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      getTestBed().configureTestingModule(moduleDef);\n    }\n  }\n\n  inject(tokens: any[], fn: Function): () => any {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\n\n/**\n * @experimental\n */\nexport function withModule(moduleDef: TestModuleMetadata): InjectSetupWrapper;\nexport function withModule(moduleDef: TestModuleMetadata, fn: Function): () => any;\nexport function withModule(moduleDef: TestModuleMetadata, fn?: Function | null): (() => any)|\n    InjectSetupWrapper {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      const testBed = getTestBed();\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\nfunction getComponentType(error: Error): Function {\n  return (error as any)[ɵERROR_COMPONENT_TYPE];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectorRef, ComponentRef, DebugElement, ElementRef, NgZone, RendererFactory2, getDebugNode} from '@angular/core';\n\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @stable\n */\nexport class ComponentFixture<T> {\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement: DebugElement;\n\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance: T;\n\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement: any;\n\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef: ElementRef;\n\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef: ChangeDetectorRef;\n\n  private _renderer: RendererFactory2|null|undefined;\n  private _isStable: boolean = true;\n  private _isDestroyed: boolean = false;\n  private _resolve: ((result: any) => void)|null = null;\n  private _promise: Promise<any>|null = null;\n  private _onUnstableSubscription: any /** TODO #9100 */ = null;\n  private _onStableSubscription: any /** TODO #9100 */ = null;\n  private _onMicrotaskEmptySubscription: any /** TODO #9100 */ = null;\n  private _onErrorSubscription: any /** TODO #9100 */ = null;\n\n  constructor(\n      public componentRef: ComponentRef<T>, public ngZone: NgZone|null,\n      private _autoDetect: boolean) {\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = <DebugElement>getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n    this.ngZone = ngZone;\n\n    if (ngZone) {\n      this._onUnstableSubscription =\n          ngZone.onUnstable.subscribe({next: () => { this._isStable = false; }});\n      this._onMicrotaskEmptySubscription = ngZone.onMicrotaskEmpty.subscribe({\n        next: () => {\n          if (this._autoDetect) {\n            // Do a change detection run with checkNoChanges set to true to check\n            // there are no changes on the second run.\n            this.detectChanges(true);\n          }\n        }\n      });\n      this._onStableSubscription = ngZone.onStable.subscribe({\n        next: () => {\n          this._isStable = true;\n          // Check whether there is a pending whenStable() completer to resolve.\n          if (this._promise !== null) {\n            // If so check whether there are no pending macrotasks before resolving.\n            // Do this check in the next tick so that ngZone gets a chance to update the state of\n            // pending macrotasks.\n            scheduleMicroTask(() => {\n              if (!ngZone.hasPendingMacrotasks) {\n                if (this._promise !== null) {\n                  this._resolve !(true);\n                  this._resolve = null;\n                  this._promise = null;\n                }\n              }\n            });\n          }\n        }\n      });\n\n      this._onErrorSubscription =\n          ngZone.onError.subscribe({next: (error: any) => { throw error; }});\n    }\n  }\n\n  private _tick(checkNoChanges: boolean) {\n    this.changeDetectorRef.detectChanges();\n    if (checkNoChanges) {\n      this.checkNoChanges();\n    }\n  }\n\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges: boolean = true): void {\n    if (this.ngZone != null) {\n      // Run the change detection inside the NgZone so that any async tasks as part of the change\n      // detection are captured by the zone and can be waited for in isStable.\n      this.ngZone.run(() => { this._tick(checkNoChanges); });\n    } else {\n      // Running without zone. Just do the change detection.\n      this._tick(checkNoChanges);\n    }\n  }\n\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges(): void { this.changeDetectorRef.checkNoChanges(); }\n\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   */\n  autoDetectChanges(autoDetect: boolean = true) {\n    if (this.ngZone == null) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set');\n    }\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable(): boolean { return this._isStable && !this.ngZone !.hasPendingMacrotasks; }\n\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable(): Promise<any> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    } else if (this._promise !== null) {\n      return this._promise;\n    } else {\n      this._promise = new Promise(res => { this._resolve = res; });\n      return this._promise;\n    }\n  }\n\n\n  private _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer as RendererFactory2 | null;\n  }\n\n  /**\n    * Get a promise that resolves when the ui state is stable following animations.\n    */\n  whenRenderingDone(): Promise<any> {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n\n  /**\n   * Trigger component destruction.\n   */\n  destroy(): void {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      if (this._onUnstableSubscription != null) {\n        this._onUnstableSubscription.unsubscribe();\n        this._onUnstableSubscription = null;\n      }\n      if (this._onStableSubscription != null) {\n        this._onStableSubscription.unsubscribe();\n        this._onStableSubscription = null;\n      }\n      if (this._onMicrotaskEmptySubscription != null) {\n        this._onMicrotaskEmptySubscription.unsubscribe();\n        this._onMicrotaskEmptySubscription = null;\n      }\n      if (this._onErrorSubscription != null) {\n        this._onErrorSubscription.unsubscribe();\n        this._onErrorSubscription = null;\n      }\n      this._isDestroyed = true;\n    }\n  }\n}\n\nfunction scheduleMicroTask(fn: Function) {\n  Zone.current.scheduleMicroTask('scheduleMicrotask', fn);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst FakeAsyncTestZoneSpec = (Zone as any)['FakeAsyncTestZoneSpec'];\ntype ProxyZoneSpec = {\n  setDelegate(delegateSpec: ZoneSpec): void; getDelegate(): ZoneSpec; resetDelegate(): void;\n};\nconst ProxyZoneSpec: {get(): ProxyZoneSpec; assertPresent: () => ProxyZoneSpec} =\n    (Zone as any)['ProxyZoneSpec'];\n\nlet _fakeAsyncTestZoneSpec: any = null;\n\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nexport function resetFakeAsyncZone() {\n  _fakeAsyncTestZoneSpec = null;\n  ProxyZoneSpec.assertPresent().resetDelegate();\n}\n\nlet _inFakeAsyncCall = false;\n\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @returns {Function} The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nexport function fakeAsync(fn: Function): (...args: any[]) => any {\n  // Not using an arrow function to preserve context passed from call site\n  return function(...args: any[]) {\n    const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n    if (_inFakeAsyncCall) {\n      throw new Error('fakeAsync() calls can not be nested');\n    }\n    _inFakeAsyncCall = true;\n    try {\n      if (!_fakeAsyncTestZoneSpec) {\n        if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n          throw new Error('fakeAsync() calls can not be nested');\n        }\n\n        _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n      }\n\n      let res: any;\n      const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n      proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n      try {\n        res = fn.apply(this, args);\n        flushMicrotasks();\n      } finally {\n        proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n            `periodic timer(s) still in the queue.`);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n      }\n      return res;\n    } finally {\n      _inFakeAsyncCall = false;\n      resetFakeAsyncZone();\n    }\n  };\n}\n\nfunction _getFakeAsyncZoneSpec(): any {\n  if (_fakeAsyncTestZoneSpec == null) {\n    throw new Error('The code should be running in the fakeAsync zone to call this function');\n  }\n  return _fakeAsyncTestZoneSpec;\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nexport function tick(millis: number = 0): void {\n  _getFakeAsyncZoneSpec().tick(millis);\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns {number} The simulated time elapsed, in millis.\n *\n * @experimental\n */\nexport function flush(maxTurns?: number): number {\n  return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nexport function discardPeriodicTasks(): void {\n  const zoneSpec = _getFakeAsyncZoneSpec();\n  const pendingTimers = zoneSpec.pendingPeriodicTimers;\n  zoneSpec.pendingPeriodicTimers.length = 0;\n}\n\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nexport function flushMicrotasks(): void {\n  _getFakeAsyncZoneSpec().flushMicrotasks();\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injectable completer that allows signaling completion of an asynchronous test. Used internally.\n */\nexport class AsyncTestCompleter {\n  private _resolve: (result: any) => void;\n  private _reject: (err: any) => void;\n  private _promise: Promise<any> = new Promise((res, rej) => {\n    this._resolve = res;\n    this._reject = rej;\n  });\n  done(value?: any) { this._resolve(value); }\n\n  fail(error?: any, stackTrace?: string) { this._reject(error); }\n\n  get promise(): Promise<any> { return this._promise; }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Compiler, CompilerOptions, Component, ComponentFactory, Directive, Injector, NgModule, Pipe, Type} from '@angular/core';\n\nimport {MetadataOverride} from './metadata_override';\n\nfunction unimplemented(): any {\n  throw Error('unimplemented');\n}\n\n/**\n * Special interface to the compiler only used by testing\n *\n * @experimental\n */\nexport class TestingCompiler extends Compiler {\n  get injector(): Injector { throw unimplemented(); }\n  overrideModule(module: Type<any>, overrides: MetadataOverride<NgModule>): void {\n    throw unimplemented();\n  }\n  overrideDirective(directive: Type<any>, overrides: MetadataOverride<Directive>): void {\n    throw unimplemented();\n  }\n  overrideComponent(component: Type<any>, overrides: MetadataOverride<Component>): void {\n    throw unimplemented();\n  }\n  overridePipe(directive: Type<any>, overrides: MetadataOverride<Pipe>): void {\n    throw unimplemented();\n  }\n  /**\n   * Allows to pass the compile summary from AOT compilation to the JIT compiler,\n   * so that it can use the code generated by AOT.\n   */\n  loadAotSummaries(summaries: () => any[]) { throw unimplemented(); };\n\n  /**\n   * Gets the component factory for the given component.\n   * This assumes that the component has been compiled before calling this call using\n   * `compileModuleAndAllComponents*`.\n   */\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> { throw unimplemented(); }\n}\n\n/**\n * A factory for creating a Compiler\n *\n * @experimental\n */\nexport abstract class TestingCompilerFactory {\n  abstract createTestingCompiler(options?: CompilerOptions[]): TestingCompiler;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n\nimport {resetFakeAsyncZone} from './fake_async';\nimport {TestBed} from './test_bed';\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n// Reset the test providers and the fake async zone before each test.\nif (_global.beforeEach) {\n  _global.beforeEach(() => {\n    TestBed.resetTestingModule();\n    resetFakeAsyncZone();\n  });\n}\n\n// TODO(juliemr): remove this, only used because we need to export something to have compilation\n// work.\nexport const __core_private_testing_placeholder__ = '';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {TestingCompiler as ɵTestingCompiler, TestingCompilerFactory as ɵTestingCompilerFactory} from './test_compiler';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', async(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * });\n * ```\n *\n * @stable\n */\nexport function async(fn: Function): (done: any) => any {\n  // If we're running using the Jasmine test framework, adapt to call the 'done'\n  // function when asynchronous activity is finished.\n  if (_global.jasmine) {\n    // Not using an arrow function to preserve context passed from call site\n    return function(done: any) {\n      if (!done) {\n        // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n        // fake it here and assume sync.\n        done = function() {};\n        done.fail = function(e: any) { throw e; };\n      }\n      runInTestZone(fn, this, done, (err: any) => {\n        if (typeof err === 'string') {\n          return done.fail(new Error(<string>err));\n        } else {\n          done.fail(err);\n        }\n      });\n    };\n  }\n  // Otherwise, return a promise which will resolve when asynchronous activity\n  // is finished. This will be correctly consumed by the Mocha framework with\n  // it('...', async(myFn)); or can be used in a custom framework.\n  // Not using an arrow function to preserve context passed from call site\n  return function() {\n    return new Promise<void>((finishCallback, failCallback) => {\n      runInTestZone(fn, this, finishCallback, failCallback);\n    });\n  };\n}\n\nfunction runInTestZone(\n    fn: Function, context: any, finishCallback: Function, failCallback: Function) {\n  const currentZone = Zone.current;\n  const AsyncTestZoneSpec = (Zone as any)['AsyncTestZoneSpec'];\n  if (AsyncTestZoneSpec === undefined) {\n    throw new Error(\n        'AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/async-test.js');\n  }\n  const ProxyZoneSpec = (Zone as any)['ProxyZoneSpec'] as {\n    get(): {setDelegate(spec: ZoneSpec): void; getDelegate(): ZoneSpec;};\n    assertPresent: () => void;\n  };\n  if (ProxyZoneSpec === undefined) {\n    throw new Error(\n        'ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/proxy.js');\n  }\n  const proxyZoneSpec = ProxyZoneSpec.get();\n  ProxyZoneSpec.assertPresent();\n  // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n  // If we do it in ProxyZone then we will get to infinite recursion.\n  const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n  const previousDelegate = proxyZoneSpec.getDelegate();\n  proxyZone.parent.run(() => {\n    const testZoneSpec: ZoneSpec = new AsyncTestZoneSpec(\n        () => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            finishCallback();\n          });\n        },\n        (error: any) => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            failCallback(error);\n          });\n        },\n        'test');\n    proxyZoneSpec.setDelegate(testZoneSpec);\n  });\n  return Zone.current.runGuarded(fn, context);\n}\n"], "names": ["inject", "tokens", "fn", "_this", "this", "testBed", "compileComponents", "then", "completer", "get", "AsyncTestCompleter", "withModule", "moduleDef", "getTestBed", "extendStatics", "Object", "setPrototypeOf", "__proto__", "Array", "d", "b", "p", "hasOwnProperty", "ComponentFixture", "componentRef", "ngZone", "_autoDetect", "_isStable", "_isDestroyed", "_resolve", "_promise", "_onUnstableSubscription", "_onStableSubscription", "_onMicrotaskEmptySubscription", "_onErrorSubscription", "changeDetectorRef", "elementRef", "location", "debugElement", "_angular_core", "getDebugNode", "nativeElement", "onMicrotaskEmpty", "subscribe", "next", "scheduleMicroTask", "hasPendingMacrotasks", "prototype", "_tick", "checkNoChanges", "run", "whenStable", "isStable", "Promise", "resolve", "_get<PERSON><PERSON><PERSON>", "_renderer", "whenRenderingDone", "renderer", "destroy", "unsubscribe", "res", "rej", "TestingCompiler", "_super", "__extends", "defineProperty", "unimplemented", "enumerable", "configurable", "overrideModule", "module", "overrides", "overrideComponent", "component", "TestComponent<PERSON><PERSON><PERSON>", "TestBed", "_instantiated", "_compiler", "_moduleRef", "_moduleFactory", "_compilerOptions", "_moduleOverrides", "_componentOverrides", "_directiveOverrides", "ngModule", "override", "overrideDirective", "directive", "overridePipe", "pipe", "overrideTemplate", "template", "set", "templateUrl", "override<PERSON><PERSON><PERSON>", "token", "provider", "resetTestEnvironment", "resetTestingModule", "ɵclearProviderOverrides", "_aotSummaries", "_pipeOverrides", "_providers", "_declarations", "_imports", "_schemas", "_activeFixtures", "for<PERSON>ach", "fixture", "e", "console", "error", "componentInstance", "configureCompiler", "config", "_assertNotInstantiated", "push", "configureTestingModule", "providers", "_a", "apply", "declarations", "_b", "imports", "schemas", "_d", "_c", "moduleType", "_createCompilerAndModule", "compileModuleAndAllComponentsAsync", "moduleAndComponentFactories", "ngModuleFactory", "_initIfNeeded", "compileModuleAndAllComponentsSync", "getComponentType", "Error", "ɵstringify", "NgZone", "enableLongStackTrace", "ngZoneInjector", "ReflectiveInjector", "resolveAndCreate", "provide", "useValue", "platform", "injector", "create", "DynamicTestModule", "decorators", "type", "NgModule", "args", "ctorParameters", "compilerFactory", "TestingCompilerFactory", "createTestingCompiler", "concat", "useDebug", "loadAotSummaries", "entry", "methodName", "methodDescription", "notFoundValue", "result", "UNDEFINED", "execute", "context", "params", "map", "t", "value", "flags", "useFactory", "deps", "Optional", "dep", "depToken", "dep<PERSON>lags", "isArray", "SkipSelf", "ɵoverrideProvider", "createComponent", "componentFactory", "getComponentFactory", "noNgZone", "ComponentFixtureNoNgZone", "autoDetect", "ComponentFixtureAutoDetect", "testComponent<PERSON><PERSON><PERSON>", "rootElId", "_nextRootElementId", "insertRootElement", "initComponent", "InjectSetupWrapper", "_moduleDef", "_addModule", "self", "call", "_global$1", "window", "global", "beforeEach", "exports", "factory", "require", "async", "done", "fail", "finishCallback", "fail<PERSON><PERSON>back", "runInTestZone", "currentZone", "Zone", "current", "AsyncTestZoneSpec", "undefined", "ProxyZoneSpec", "proxyZoneSpec", "proxyZone", "getZoneWith", "previousDelegate", "getDelegate", "testZoneSpec", "setDelegate", "fakeAsync", "_i", "arguments", "length", "assertPresent", "_inFakeAsyncCall", "_fakeAsyncTestZoneSpec", "FakeAsyncTestZoneSpec", "lastProxyZoneSpec", "flushMicrotasks", "pendingPeriodicTimers", "pendingTimers"], "mappings": ";;;;;0BCAA,gBAAA2M,UAAA,mBAAApI,QAAAqI,QAAAD,QAAAE,QAAA,ibOuCA,QAAAC,OAAA5M,2BAKA,SAAA6M,MAAAA,OAGAA,KAAA,aACAA,KAAAC,KAAA,SAAAlG,GAAA,KAAAA,0HAiBA,WACA,GAAA3G,OAAAC,IAEA,OAAA,IAAAiD,SAAA,SAAA4J,eAAAC,cACAC,cAAAjN,GAAAC,MAAA8M,eAAAC,iBAQA,QAAAC,eAAAjN,GAAAqK,QAAA0C,eAAAC,cACA,GAAAE,aAAAC,KAAAC,QACQC,kBAARF,KAAA,iBACE,IAAFG,SAAAD,+LAIE,IAAME,eAAgBJ,KAAxB,aACE,IAAFG,SAAAC,kLAKA,IAAAC,eAAAD,cAAAhN,mCAIA,IAAAkN,WAAAN,KAAAC,QAAAM,YAAA,iBACAC,iBAAAH,cAAAI,uGAKAV,YAAAlK,IAAA,kGAKA+J,oBAGA,SAAAjG,OAEAoG,YAAAlK,IAAA,WACAwK,cAAAI,eAAAC,cN/GAL,cAAAM,YAAAH,6TCwDA,QAAAI,WAAA/N,IACA,MAAA,YAEA,IAAA,GADMsJ,SACN0E,GAAA,EAAAA,GAAAC,UAAAC,OAAAF,KACA1E,KAAA0E,IAAAC,UAAAD,GAGA,IAAAR,eAAAD,cAAAY,eACA,IAAAC,iBAEA,KAAA,IAAA9F,OAAA,sCAEA8F,mBAAA,CACA,KACA,IAAAC,uBAAA,CACQ,GAARb,cAAAI,uBAAAU,uBACA,KAAA,IAAAhG,OAAA,sCACQ+F,wBAAR,GAAAC,uBAGA,GAAU3K,KAAV,OACA4K,kBAAAf,cAAAI,aAEAJ,eAAAM,YAAAO,uBACA,KAEA1K,IAAA3D,GAAAuH,MAAArH,KAAAoJ,MACQkF,kBAGR,QACAhB,cAAAM,YAAAS,mBACA,GAAAF,uBAAAI,sBAAAP,OAAA,EACA,KAAA,IAAA5F,OAAA+F,uBAAAI,sBAAAP,OAAA,yCAGA,IAAAG,uBAAAK,cAAAR,OAAA,EAEA,KAAA,IAAA5F,OAAA+F,uBAAAK,cAAAR,OAAA,gCAEA,OAAAvK,KAEA,QACAyK,kBAAA;;;;;;;kHHwZA,QAAAtO,QAAAC,OAAAC,0EAIA,WANA,GAAAC,OAAAC,IASA,OAAAC,SAAAC,oBAAAC,KAAA,WACA,GAAAC,WAAAH,QAAAI,IAAAC,oIAyCA,QAAAC,YAAAC,UAAAV,IACA,MAAAA,IAGA,WACA,GAAAG,SAAAQ,mBACAD,8MC7hBA,GAAIE,eAAJC,OAAAC,iBACAC,uBAAAC,QAA2C,SAA3CC,EAAAC,GAAAD,EAAAF,UAAAG,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,sDCwBAE,iBAAA,WACA,QAAAA,kBAAAC,aAAAC,OAAAC,aACU,GAAVvB,OAAAC,IACUA,MAAVoB,aAAAA,aAKIpB,KAAKqB,OAATA,OACIrB,KAAKsB,YAATA,YACItB,KAAKuB,WAAT,EACIvB,KAAKwB,cAAT,EACIxB,KAAKyB,SAAT,KACIzB,KAAK0B,SAAT,KACI1B,KAAK2B,wBAAT,KAEI3B,KAAJ4B,sBAAA,KACA5B,KAAA6B,8BAAA,KACA7B,KAAA8B,qBAAA,KACA9B,KAAA+B,kBAAAX,aAAAW,kBACA/B,KAAAgC,WAAAZ,aAAAa,SACAjC,KAAAkC,aAAAC,cAAAC,aAAApC,KAAAgC,WAAAK,6GAGArC,KAAAoB,aAAAA,aACApB,KAAAqB,OAAAA,OACAA,yGAGArB,KAAA6B,8BAAAR,OAAAiB,iBAAAC,WACAC,KAAA,kJAYA,OAAAzC,MAAA2B,UAIAe,kBAAA,WACApB,OAAAqB,sBAEA,OAAA3C,MAAA2B,WACA3B,MAAA0B,UAAA,GACA1B,MAAA0B,SAAA,KACA1B,MAAA2B,SAAA,0GAaAP,kBAAAwB,UAAAC,MAAA,SAAAC,gKAOA,GAAA9C,OAAAC,IACA,UAAA6C,iBAAAA,gBAAA,qBAKA7C,KAAAqB,OAAAyB,IAAA,WAAA/C,MAAA6C,MAAAC,8MAmBA,wCAAA,MAAA7C,KAAAqB,wPAeAF,iBAAAwB,UAAAI,WAAA,WACA,GAAAhD,OAAAC,IACA,OAAAA,MAAAgD,WAGAC,QAAAC,SAAA,GAEA,OAAAlD,KAAA0B,SACA1B,KAAA0B,uFAQAP,iBAAAwB,UAAAQ,aAAA,WAIA,qHAAAnD,KAAAoD,WAMAjC,iBAAAwB,UAAAU,kBAAA,WACA,GAAAC,UAAAtD,KAAAmD,wFAIAnD,KAAA+C,cAKA5B,iBAAAwB,UAAAY,QAAA,WACAvD,KAAAwB,eACAxB,KAAAoB,aAAAmC,UACA,MAAQvD,KAAK2B,0BACb3B,KAAA2B,wBAAA6B,cACAxD,KAAA2B,wBAAA,MAEA,MAAQ3B,KAAK4B,wBACb5B,KAAA4B,sBAAA4B,cACAxD,KAAA4B,sBAAA,MAEA,MAAA5B,KAAA6B,gCACA7B,KAAA6B,8BAAA2B,cAEAxD,KAAA6B,8BAAA,MAEA,MAAA7B,KAAA8B,+DCnNA9B,KAAA8B,qBAAA,mLCsBAxB,mBAAA,WACA,QAAAA,oCCvBAN,MAAA0B,SAAA,GAAAuB,SAAA,SAAAQ,IAAAC,2QDiBArD,IAAA,WAAA,MAAAL,MAAA0B,gECUAiC,gBAAA,SAAAC,kGAAAC,WAAAF,gBAAAC,QACAjD,OAAAmD,eAAAH,gBAAAhB,UAAA,YACAtC,IAAA,WAAA,KAAA0D,kBACAC,YAAA,EACAC,cAAA,IAEAN,gBAAAhB,UAAAuB,eAAA,SAAAC,OAAAC,WACA,KAAAL,mHAKAJ,gBAAAhB,UAAA0B,kBAAA,SAAAC,UAAAF,idLRAG,sBAAA,8VAqJAC,QAAA,WAEA,QAAAA,WACUxE,KAAVyE,eAAsD,EAC5CzE,KAAV0E,UAAA,KACU1E,KAAV2E,WAAA,KACU3E,KAAV4E,eAAA,KAEU5E,KAAV6E,oBAEE7E,KAAF8E,oBAEE9E,KAAF+E,uBA+QA/E,KAAAgF,ywBA9UAR,QAAAtE,kBAAA,WAAA,MAAAO,cAAAP,qBACAsE,QAAAN,eAAA,SAAAe,SAAAC,UAGA,MAFAzE,cAAAyD,eAAAe,SAAAC,UAEAV,SAEAA,QAAAH,kBAAA,SAAAC,UAAAY,UAGA,MAFAzE,cAAA4D,kBAAAC,UAAAY,UAEAV,SAEAA,QAAAW,kBAAA,SAAAC,UAAAF,UAYA,MAXAzE,cAAA0E,kBAAAC,UAAAF,UAWAV,SAMAA,QAAAa,aAAA,SAAAC,KAAAJ,UAGA,MAFAzE,cAAA4E,aAAAC,KAAAJ,UAEAV,SAEAA,QAAAe,iBAAA,SAAAjB,UAAAkB,UAGA,MADA/E,cAAA4D,kBAAAC,WAAAmB,KAAAD,SAAAA,SAAAE,YAAA,QACAlB,SACAA,QAAAmB,iBAAA,SAAAC,MAAAC,4mBAoEArB,QAAA7B,UAAAmD,qBAAA,WACA9F,KAAA+F,qBACIC,KAAJA,SAAAA,KAEIhG,KAAKiF,SAAT,KACIjF,KAAKiG,cAAT,WAAA,WAEAzB,QAAQ7B,UAARoD,mBAAA,WACI5D,cAAJ6D,0BACIhG,KAAK0E,UAAT,KACI1E,KAAK8E,oBACL9E,KAAK+E,uBACL/E,KAAKgF,uBACThF,KAAAkG,kBACAlG,KAAA2E,WAAA,KACA3E,KAAO4E,eAAP,KAAA5E,KAAA6E,oBACA7E,KAAAmG,cACAnG,KAAAoG,iBACApG,KAAAqG,YACArG,KAASsG,YACTtG,KAAAyE,eAAA,EAEAzE,KAAAuG,gBAAAC,QAAA,SAAAC,SACA,IACAA,QAAAlD,UAGA,MAAAmD,GACAC,QAAAC,MAAA,oCAAAH,QAAAI,sBAGA7G,KAAAuG,oBAEA/B,QAAM7B,UAANmE,kBAAA,SAAAC,QACA/G,KAAAgH,uBAAA,4BAAA,0BACAhH,KAAQ6E,iBAARoC,KAAAF,SAEAvC,QAAA7B,UAAAuE,uBAAA,SAAA1G,WACAR,KAAQgH,uBAAR,iCAAA,6BACAxG,UAAA2G,YACAC,GAAApH,KAAAmG,YAAAc,KAAAI,MAAAD,GAAA5G,UAAA2G,WAGA3G,UAAA8G,eACAC,GAAAvH,KAAAoG,eAAAa,KAAAI,MAAAE,GAAA/G,UAAA8G,cAEA9G,UAAAgH,6DAGAhH,UAAAiH,UAAAC,GAAA1H,KAAAsG,UAAAW,KAAAI,MAAAK,GAAAlH,UAAAiH,QAEA,IAAUL,IAAVG,GAAAI,GAAAD,IAEAlD,QAAA7B,UAAAzC,kBAAA,WAEA,GAAAH,OAAAC,IACA,IAAAA,KAAa4E,gBAAb5E,KAAAyE,cACM,MAANxB,SAAAC,QAAA,KAEA,IAAA0E,YAAA5H,KAAA6H,0BACA,OAAA7H,MAAA0E,UAAAoD,mCAAAF,YACAzH,KAAA,SAAA4H,6BACAhI,MAAA6E,eAAAmD,4BAAAC,mBAEAxD,QAAQ7B,UAARsF,cAAA,WACA,IAAAjI,KAAAyE,cAAA,CAIA,IAAAzE,KAAA4E,eAAA,IACA,GAAUgD,YAAV5H,KAAA6H,0BACA7H,MAAA4E,eACA5E,KAAA0E,UAAAwD,kCAAAN,YAAAI,gBAEA,MAAAtB,GACA,KAAAyB,kBAAAzB,GAEA,GAAA0B,OAAA,uCAAAjG,cAAAkG,WAAAF,iBAAAzB,IAAA,2IAIAA,EAKI,GAAJrF,QAAA,GAAAc,eAAAmG,QAAAC,sBAAA,IACAC,eAAyBrG,cAAzBsG,mBAAAC,mBAAAC,QAAAxG,cAAAmG,OAAAM,SAAAvH,SAAArB,KAAA6I,SAAAC,SACA9I,MAAA2E,WAAA3E,KAAA4E,eAAAmE,OAAAP,oGAIWxI,KAAXyE,eAAA,IAEAD,QAAA7B,UAAAkF,yBAAA,8FAEAP,aAAAtH,KAAAoG,cAAOoB,SAAPxH,KAAAiF,SAAAjF,KAAAqG,UAAAoB,QAAAzH,KAAAsG,SAIA0C,kBAAA,WAEQ,QAARA,iDAIIA,mBAAJC,aAEAC,KAAA/G,cAAAgH,SAAAC,OAAAjC,UAAAA,UAAAG,aAAAA,aAAAE,QAAAA,QAAAC,QAAAA,YAIAuB,kBAAAK,eAAA,WAAA,SAEA,IAAAC,iBAAAtJ,KAAA6I,SAAAC,SAAAzI,IAAAkJ,uBAUA,OATIvJ,MAAJ0E,UACM4E,gBACIE,sBADVxJ,KAAA6E,iBAAA4E,SAAAC,UAAA,MAEA1J,KAAA0E,UAAAiF,iBAAA3J,KAAAiG,eACAjG,KAAA8E,iBAAA0B,QAAA,SAAAoD,OAAA,MAAA7J,OAAA2E,UAAAR,eAAA0F,MAAA,GAAAA,MAAA,MACA5J,KAAA+E,oBAAAyB,QAAA,SAAAoD,OAAA,MAAA7J,OAAA2E,UAAAL,kBAAAuF,MAAA,GAAAA,MAAA,MAEM5J,KAANgF,oBAAAwB,QAAA,SAAAoD,OAAA,MAAA7J,OAAA2E,UAAAS,kBAAAyE,MAAA,GAAAA,MAAA,MACA5J,KAAAkG,eAAwBM,QAAxB,SAAAoD,OAAA,MAAA7J,OAAA2E,UAAAW,aAAAuE,MAAA,GAAAA,MAAA,MACAZ,mBAEAxE,QAAA7B,UAAAqE,uBAAA,SAAA6C,WAAAC,iNAKAtF,QAAA7B,UAAAtC,IAAA,SAAAuF,MAAAmE,eAII,0GAAMnE,QAAVpB,QACA,MAAAxE,KAII,IAAIgK,QAARhK,KAAA2E,WAAAmE,SAAAzI,IAAAuF,MAAAqE,UACI,OAAJD,UAAAC,UAAAjK,KAA+B0E,UAA/BoE,SAAAzI,IAAAuF,MAAAmE,eAAAC,QAGAxF,QAAA7B,UAAAuH,QAAA,SAAArK,OAAAC,GAAAqK,SACA,GAAApK,OAAAC,IACIA,MAAKiI,eACT,IAAAmC,QAAAvK,OAAAwK,IAAA,SAAAC,GAAA,MAAAvK,OAAAM,IAAAiK,IAEA,OAAAxK,IAAAuH,MAAA8C,QAAAC,SAEA5F,QAAQ7B,UAARuB,eAAA,SAAAe,SAAAC,UACAlF,KAAAgH,uBAAA,iBAAA,4BAEAhH,KAAA8E,iBAAAmC,MAAAhC,SAAAC,YAEAV,QAAQ7B,UAAR0B,kBAAA,SAAAC,UAAAY,UACAlF,KAAAgH,uBAAA,oBAAA,+BAUAhH,KAAA+E,oBAAAkC,MAAA3C,UAAAY,YAMAV,QAAQ7B,UAARwC,kBAAA,SAAAC,UAAAF,UACIlF,KAAJgH,uBAAA,oBAAA,+BACAhH,KAAAgF,oBAAAiC,MAAA7B,UAAAF,YAEAV,QAAA7B,UAAA0C,aAAA,SAAAC,KAAAJ,UAAAlF,KAAAgH,uBAAA,eAAA,0BACAhH,KAAAkG,eAAAe,MAAA3B,KAAAJ,YAEAV,QAAA7B,UAAAgD,iBAAA,SAAAC,MAAAC,UACA,GACA0E,OADAC,MAAA,CAEM3E,UAAN4E,YACMD,OAAN,KACAD,MAAA1E,SAAA4E,aAGAD,OAAA,IAAAD,MAAA1E,SAAqB+C,SAErB,IAAA8B,OAAA7E,SAAA6E,UAAAC,IAAA,SAAAC,KAAA,GACAC,UADAC,SAAA,CAmBA,OAjBAhK,OAAAiK,QAAAH,KACAA,IAAApE,QAAA,SAAAoD,OACAA,gBAAAzH,eAAAwI,SAAAG,UAAA,EAEAlB,gBAAAzH,eAAA6I,SACAF,UAAA,EAGAD,SAAAjB,QAOAiB,SAAAD,KAEAE,SAAAD,WAGI1I,eAAJ8I,mBAAArF,MAAAA,MAAA4E,MAAAA,MAAAE,KAAAA,KAAAH,MAAAA,SAEA/F,QAAA7B,UAAAuI,gBAAA,SAAA5G,WACA,GAAAvE,OAAAC,IACIA,MAAJiI,eAEI,IAAMkD,kBAAVnL,KAAA0E,UAAA0G,oBAAA9G,UACA,KAAA6G,iBAEA,KAAA,IAAA/C,OAAA,+BAAAjG,cAAAkG,WAAA/D,WAAA,mDAGI,IAAJ+G,UAAArL,KAAAK,IAAAiL,0BAAsD,GACtDC,WAAAvL,KAAAK,IAAAmL,4BAAA,GACAnK,OAAAgK,SAAA,KAAArL,KAAAK,IAAA8B,cAAAmG,OAAA,MACAmD,sBAAAzL,KAAAK,IAAAkE,uBACAmH,SAAA,OAAAC,oBAEAF,uBAAAG,kBAAAF,2MAKAjF,QAAApF,OAAAA,OAAAyB,IAAA+I,eAAAA,sBACA7L,MAAAuG,gBAAAU,KAAAR,2CA2DAqF,mBAAA,WACA,QAAAA,oBAAAC,uCAuBA,MApBAD,oBAAAnJ,UAAAqJ,WAAA,WACA,GAAAxL,WAAAR,KAAA+L,YACAvL,4DAKAsL,mBAAAnJ,UAAA/C,OAMA,SANAC,OAAAC,iBASA,OAAA,YACA,MADAmM,MAAAD,aACApM,OAAAC,OAAAC,IAAAoM,KAAAlM,QAGA8L,sBM3gBAK,UAAA,mBAAAC,QAAAC,OAAAD,MC/BAD,WAAAG"}