{"version": 3, "file": "evaluator.spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/evaluator.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAGH,+BAAiC;AAEjC,8CAA2C;AAC3C,0CAAuC;AAEvC,uDAAiF;AAEjF,QAAQ,CAAC,WAAW,EAAE;IACpB,IAAM,gBAAgB,GAAG,EAAE,CAAC,sBAAsB,EAAE,CAAC;IACrD,IAAI,IAA4B,CAAC;IACjC,IAAI,OAA2B,CAAC;IAChC,IAAI,OAAmB,CAAC;IACxB,IAAI,WAA2B,CAAC;IAChC,IAAI,OAAgB,CAAC;IACrB,IAAI,SAAoB,CAAC;IAEzB,UAAU,CAAC;QACT,IAAI,GAAG,IAAI,uBAAI,CAAC,KAAK,EAAE;YACrB,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY;YAC7E,kBAAkB,EAAE,WAAW,EAAE,aAAa;SAC/C,CAAC,CAAC;QACH,OAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAC3D,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAC/B,WAAW,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACvC,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;QAC5B,SAAS,GAAG,IAAI,qBAAS,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,sCAAmB,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,CAAC;QAC7D,GAAG,CAAC,CAAqB,UAAwB,EAAxB,KAAA,OAAO,CAAC,cAAc,EAAE,EAAxB,cAAwB,EAAxB,IAAwB;YAA5C,IAAM,UAAU,SAAA;YACnB,sCAAmB,CAAC,OAAO,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1E,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,IAAI,WAAW,CAAC,CAAC,CAAC;gBACvC,wFAAwF;gBACxF,sCAAmB,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3E,CAAC;SACF;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACnF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACnF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9E,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE;QAChE,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACjC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACrF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACpF,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACnF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,0BAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3E,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE;QAC3C,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACjC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClF,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC;aAClE,OAAO,CAAC,EAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC,CAAC,CAAC;QAClD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACxF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAGpF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;QAChG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;QAChG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC9F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC/F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC9F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;aACrE,OAAO,CAAC,CAAC,IAAS,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC;aACxE,OAAO,CAAC,CAAC,IAAS,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;aACzE,OAAO,CAAC,CAAC,KAAU,GAAG,CAAC,CAAC;QAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC;aAC5E,OAAO,CAAC,CAAC,KAAU,GAAG,CAAC,CAAC;QAC7B,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAChG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,WAAW,CAAC,CAAC;aAC7E,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,WAAW,CAAC,CAAC;aAChF,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;aAC1E,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;aAC3E,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE;QACnD,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;aACzE,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAC,CAAC,CAAC;QAC5D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;aACzE,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+BAA+B,EAAE;QAClC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC,CAAC;QACvF,SAAS,GAAG,IAAI,qBAAS,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC9C,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAChE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;YACtF,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAC;YACzE,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC;SACxB,CAAC,CAAC;QACH,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,0BAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;YACpF,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAC;YACzE,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC;SACxB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE;QACtD,IAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACtD,IAAM,KAAK,GAAG,0BAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;YACjD,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC;YAClD,MAAM,EAAE,GAAG;SACZ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kDAAkD,EAAE;QACrD,IAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAClD,IAAM,KAAK,GAAG,0BAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;aAC5C,OAAO,CACJ,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAC,CAAC,CAAC;QAC/F,IAAM,KAAK,GAAG,0BAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;YACjD,UAAU,EAAE,OAAO;YACnB,OAAO,EAAE,wBAAwB;YACjC,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAC,QAAQ,EAAE,UAAU,EAAC;SAChC,CAAC,CAAC;QACH,IAAM,KAAK,GAAG,0BAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;YACxD,UAAU,EAAE,OAAO;YACnB,OAAO,EAAE,eAAe;YACxB,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAC;SACzB,CAAC,CAAC;QACH,IAAM,KAAK,GAAG,0BAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;YACxD,UAAU,EAAE,OAAO;YACnB,OAAO,EAAE,+BAA+B;YACxC,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE;QAC3C,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,IAAM,SAAS,GAAG,0BAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE;QAClD,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,IAAM,YAAY,GAAG,0BAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;YAC/D,CAAC,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAC,EAAC,EAAE,CAAC;SACvF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE;QAChE,IAAM,MAAM,GAAG,YAAY,CAAC,qCAE3B,CAAC,CAAC;QACH,IAAM,IAAI,GAAG,0BAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC3C,OAAO,CAAC,EAAC,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAC,EAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,sBAAsB,IAAY;IAChC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC5E,CAAC;AAED,IAAM,KAAK,GAAc;IACvB,eAAe,EAAE,+HAId;IACH,YAAY,EAAE,qGAIb;IACD,WAAW,EAAE,2KAMZ;IACD,gBAAgB,EAAE,ksDA0CjB;IACD,MAAM,EAAE,wGAIY;IACpB,MAAM,EAAE,kKAKY;IACpB,eAAe,EAAE,mJAIhB;IACD,eAAe,EAAE,+JAIhB;IACD,kBAAkB,EAAE,oSAMnB;IACD,WAAW,EAAE,uGAKZ;IACD,aAAa,EAAE,sGAMd;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as fs from 'fs';\nimport * as ts from 'typescript';\n\nimport {Evaluator} from '../src/evaluator';\nimport {Symbols} from '../src/symbols';\n\nimport {Directory, Host, expectNoDiagnostics, findVar} from './typescript.mocks';\n\ndescribe('Evaluator', () => {\n  const documentRegistry = ts.createDocumentRegistry();\n  let host: ts.LanguageServiceHost;\n  let service: ts.LanguageService;\n  let program: ts.Program;\n  let typeChecker: ts.TypeChecker;\n  let symbols: Symbols;\n  let evaluator: Evaluator;\n\n  beforeEach(() => {\n    host = new Host(FILES, [\n      'expressions.ts', 'consts.ts', 'const_expr.ts', 'forwardRef.ts', 'classes.ts',\n      'newExpression.ts', 'errors.ts', 'declared.ts'\n    ]);\n    service = ts.createLanguageService(host, documentRegistry);\n    program = service.getProgram();\n    typeChecker = program.getTypeChecker();\n    symbols = new Symbols(null);\n    evaluator = new Evaluator(symbols, new Map());\n  });\n\n  it('should not have typescript errors in test data', () => {\n    expectNoDiagnostics(service.getCompilerOptionsDiagnostics());\n    for (const sourceFile of program.getSourceFiles()) {\n      expectNoDiagnostics(service.getSyntacticDiagnostics(sourceFile.fileName));\n      if (sourceFile.fileName != 'errors.ts') {\n        // Skip errors.ts because we it has intentional semantic errors that we are testing for.\n        expectNoDiagnostics(service.getSemanticDiagnostics(sourceFile.fileName));\n      }\n    }\n  });\n\n  it('should be able to fold literal expressions', () => {\n    const consts = program.getSourceFile('consts.ts');\n    expect(evaluator.isFoldable(findVar(consts, 'someName').initializer)).toBeTruthy();\n    expect(evaluator.isFoldable(findVar(consts, 'someBool').initializer)).toBeTruthy();\n    expect(evaluator.isFoldable(findVar(consts, 'one').initializer)).toBeTruthy();\n    expect(evaluator.isFoldable(findVar(consts, 'two').initializer)).toBeTruthy();\n  });\n\n  it('should be able to fold expressions with foldable references', () => {\n    const expressions = program.getSourceFile('expressions.ts');\n    symbols.define('someName', 'some-name');\n    symbols.define('someBool', true);\n    symbols.define('one', 1);\n    symbols.define('two', 2);\n    expect(evaluator.isFoldable(findVar(expressions, 'three').initializer)).toBeTruthy();\n    expect(evaluator.isFoldable(findVar(expressions, 'four').initializer)).toBeTruthy();\n    symbols.define('three', 3);\n    symbols.define('four', 4);\n    expect(evaluator.isFoldable(findVar(expressions, 'obj').initializer)).toBeTruthy();\n    expect(evaluator.isFoldable(findVar(expressions, 'arr').initializer)).toBeTruthy();\n  });\n\n  it('should be able to evaluate literal expressions', () => {\n    const consts = program.getSourceFile('consts.ts');\n    expect(evaluator.evaluateNode(findVar(consts, 'someName').initializer)).toBe('some-name');\n    expect(evaluator.evaluateNode(findVar(consts, 'someBool').initializer)).toBe(true);\n    expect(evaluator.evaluateNode(findVar(consts, 'one').initializer)).toBe(1);\n    expect(evaluator.evaluateNode(findVar(consts, 'two').initializer)).toBe(2);\n  });\n\n  it('should be able to evaluate expressions', () => {\n    const expressions = program.getSourceFile('expressions.ts');\n    symbols.define('someName', 'some-name');\n    symbols.define('someBool', true);\n    symbols.define('one', 1);\n    symbols.define('two', 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'three').initializer)).toBe(3);\n    symbols.define('three', 3);\n    expect(evaluator.evaluateNode(findVar(expressions, 'four').initializer)).toBe(4);\n    symbols.define('four', 4);\n    expect(evaluator.evaluateNode(findVar(expressions, 'obj').initializer))\n        .toEqual({one: 1, two: 2, three: 3, four: 4});\n    expect(evaluator.evaluateNode(findVar(expressions, 'arr').initializer)).toEqual([1, 2, 3, 4]);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bTrue').initializer)).toEqual(true);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bFalse').initializer)).toEqual(false);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bAnd').initializer)).toEqual(true);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bOr').initializer)).toEqual(true);\n    expect(evaluator.evaluateNode(findVar(expressions, 'nDiv').initializer)).toEqual(2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'nMod').initializer)).toEqual(1);\n\n\n    expect(evaluator.evaluateNode(findVar(expressions, 'bLOr').initializer)).toEqual(false || true);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bLAnd').initializer)).toEqual(true && true);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bBOr').initializer)).toEqual(0x11 | 0x22);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bBAnd').initializer)).toEqual(0x11 & 0x03);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bXor').initializer)).toEqual(0x11 ^ 0x21);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bEqual').initializer))\n        .toEqual(1 == <any>'1');\n    expect(evaluator.evaluateNode(findVar(expressions, 'bNotEqual').initializer))\n        .toEqual(1 != <any>'1');\n    expect(evaluator.evaluateNode(findVar(expressions, 'bIdentical').initializer))\n        .toEqual(1 === <any>'1');\n    expect(evaluator.evaluateNode(findVar(expressions, 'bNotIdentical').initializer))\n        .toEqual(1 !== <any>'1');\n    expect(evaluator.evaluateNode(findVar(expressions, 'bLessThan').initializer)).toEqual(1 < 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bGreaterThan').initializer)).toEqual(1 > 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bLessThanEqual').initializer))\n        .toEqual(1 <= 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bGreaterThanEqual').initializer))\n        .toEqual(1 >= 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bShiftLeft').initializer)).toEqual(1 << 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bShiftRight').initializer))\n        .toEqual(-1 >> 2);\n    expect(evaluator.evaluateNode(findVar(expressions, 'bShiftRightU').initializer))\n        .toEqual(-1 >>> 2);\n\n  });\n\n  it('should report recursive references as symbolic', () => {\n    const expressions = program.getSourceFile('expressions.ts');\n    expect(evaluator.evaluateNode(findVar(expressions, 'recursiveA').initializer))\n        .toEqual({__symbolic: 'reference', name: 'recursiveB'});\n    expect(evaluator.evaluateNode(findVar(expressions, 'recursiveB').initializer))\n        .toEqual({__symbolic: 'reference', name: 'recursiveA'});\n  });\n\n  it('should correctly handle special cases for CONST_EXPR', () => {\n    const const_expr = program.getSourceFile('const_expr.ts');\n    expect(evaluator.evaluateNode(findVar(const_expr, 'bTrue').initializer)).toEqual(true);\n    expect(evaluator.evaluateNode(findVar(const_expr, 'bFalse').initializer)).toEqual(false);\n  });\n\n  it('should resolve a forwardRef', () => {\n    const forwardRef = program.getSourceFile('forwardRef.ts');\n    expect(evaluator.evaluateNode(findVar(forwardRef, 'bTrue').initializer)).toEqual(true);\n    expect(evaluator.evaluateNode(findVar(forwardRef, 'bFalse').initializer)).toEqual(false);\n  });\n\n  it('should return new expressions', () => {\n    symbols.define('Value', {__symbolic: 'reference', module: './classes', name: 'Value'});\n    evaluator = new Evaluator(symbols, new Map());\n    const newExpression = program.getSourceFile('newExpression.ts');\n    expect(evaluator.evaluateNode(findVar(newExpression, 'someValue').initializer)).toEqual({\n      __symbolic: 'new',\n      expression: {__symbolic: 'reference', name: 'Value', module: './classes'},\n      arguments: ['name', 12]\n    });\n    expect(evaluator.evaluateNode(findVar(newExpression, 'complex').initializer)).toEqual({\n      __symbolic: 'new',\n      expression: {__symbolic: 'reference', name: 'Value', module: './classes'},\n      arguments: ['name', 12]\n    });\n  });\n\n  it('should support referene to a declared module type', () => {\n    const declared = program.getSourceFile('declared.ts');\n    const aDecl = findVar(declared, 'a');\n    expect(evaluator.evaluateNode(aDecl.type)).toEqual({\n      __symbolic: 'select',\n      expression: {__symbolic: 'reference', name: 'Foo'},\n      member: 'A'\n    });\n  });\n\n  it('should return errors for unsupported expressions', () => {\n    const errors = program.getSourceFile('errors.ts');\n    const fDecl = findVar(errors, 'f');\n    expect(evaluator.evaluateNode(fDecl.initializer))\n        .toEqual(\n            {__symbolic: 'error', message: 'Function call not supported', line: 1, character: 12});\n    const eDecl = findVar(errors, 'e');\n    expect(evaluator.evaluateNode(eDecl.type)).toEqual({\n      __symbolic: 'error',\n      message: 'Could not resolve type',\n      line: 2,\n      character: 11,\n      context: {typeName: 'NotFound'}\n    });\n    const sDecl = findVar(errors, 's');\n    expect(evaluator.evaluateNode(sDecl.initializer)).toEqual({\n      __symbolic: 'error',\n      message: 'Name expected',\n      line: 3,\n      character: 14,\n      context: {received: '1'}\n    });\n    const tDecl = findVar(errors, 't');\n    expect(evaluator.evaluateNode(tDecl.initializer)).toEqual({\n      __symbolic: 'error',\n      message: 'Expression form not supported',\n      line: 4,\n      character: 12\n    });\n  });\n\n  it('should be able to fold an array spread', () => {\n    const expressions = program.getSourceFile('expressions.ts');\n    symbols.define('arr', [1, 2, 3, 4]);\n    const arrSpread = findVar(expressions, 'arrSpread');\n    expect(evaluator.evaluateNode(arrSpread.initializer)).toEqual([0, 1, 2, 3, 4, 5]);\n  });\n\n  it('should be able to produce a spread expression', () => {\n    const expressions = program.getSourceFile('expressions.ts');\n    const arrSpreadRef = findVar(expressions, 'arrSpreadRef');\n    expect(evaluator.evaluateNode(arrSpreadRef.initializer)).toEqual([\n      0, {__symbolic: 'spread', expression: {__symbolic: 'reference', name: 'arrImport'}}, 5\n    ]);\n  });\n\n  it('should be able to handle a new expression with no arguments', () => {\n    const source = sourceFileOf(`\n      export var a = new f;\n    `);\n    const expr = findVar(source, 'a');\n    expect(evaluator.evaluateNode(expr.initializer))\n        .toEqual({__symbolic: 'new', expression: {__symbolic: 'reference', name: 'f'}});\n  });\n});\n\nfunction sourceFileOf(text: string): ts.SourceFile {\n  return ts.createSourceFile('test.ts', text, ts.ScriptTarget.Latest, true);\n}\n\nconst FILES: Directory = {\n  'directives.ts': `\n    export function Pipe(options: { name?: string, pure?: boolean}) {\n      return function(fn: Function) { }\n    }\n    `,\n  'classes.ts': `\n    export class Value {\n      constructor(public name: string, public value: any) {}\n    }\n  `,\n  'consts.ts': `\n    export var someName = 'some-name';\n    export var someBool = true;\n    export var one = 1;\n    export var two = 2;\n    export var arrImport = [1, 2, 3, 4];\n  `,\n  'expressions.ts': `\n    import {arrImport} from './consts';\n\n    export var someName = 'some-name';\n    export var someBool = true;\n    export var one = 1;\n    export var two = 2;\n\n    export var three = one + two;\n    export var four = two * two;\n    export var obj = { one: one, two: two, three: three, four: four };\n    export var arr = [one, two, three, four];\n    export var bTrue = someBool;\n    export var bFalse = !someBool;\n    export var bAnd = someBool && someBool;\n    export var bOr = someBool || someBool;\n    export var nDiv = four / two;\n    export var nMod = (four + one) % two;\n\n    export var bLOr = false || true;             // true\n    export var bLAnd = true && true;             // true\n    export var bBOr = 0x11 | 0x22;               // 0x33\n    export var bBAnd = 0x11 & 0x03;              // 0x01\n    export var bXor = 0x11 ^ 0x21;               // 0x20\n    export var bEqual = 1 == <any>\"1\";           // true\n    export var bNotEqual = 1 != <any>\"1\";        // false\n    export var bIdentical = 1 === <any>\"1\";      // false\n    export var bNotIdentical = 1 !== <any>\"1\";   // true\n    export var bLessThan = 1 < 2;                // true\n    export var bGreaterThan = 1 > 2;             // false\n    export var bLessThanEqual = 1 <= 2;          // true\n    export var bGreaterThanEqual = 1 >= 2;       // false\n    export var bShiftLeft = 1 << 2;              // 0x04\n    export var bShiftRight = -1 >> 2;            // -1\n    export var bShiftRightU = -1 >>> 2;          // 0x3fffffff\n\n    export var arrSpread = [0, ...arr, 5];\n\n    export var arrSpreadRef = [0, ...arrImport, 5];\n\n    export var recursiveA = recursiveB;\n    export var recursiveB = recursiveA;\n  `,\n  'A.ts': `\n    import {Pipe} from './directives';\n\n    @Pipe({name: 'A', pure: false})\n    export class A {}`,\n  'B.ts': `\n    import {Pipe} from './directives';\n    import {someName, someBool} from './consts';\n\n    @Pipe({name: someName, pure: someBool})\n    export class B {}`,\n  'const_expr.ts': `\n    function CONST_EXPR(value: any) { return value; }\n    export var bTrue = CONST_EXPR(true);\n    export var bFalse = CONST_EXPR(false);\n  `,\n  'forwardRef.ts': `\n    function forwardRef(value: any) { return value; }\n    export var bTrue = forwardRef(() => true);\n    export var bFalse = forwardRef(() => false);\n  `,\n  'newExpression.ts': `\n    import {Value} from './classes';\n    function CONST_EXPR(value: any) { return value; }\n    function forwardRef(value: any) { return value; }\n    export const someValue = new Value(\"name\", 12);\n    export const complex = CONST_EXPR(new Value(\"name\", forwardRef(() => 12)));\n  `,\n  'errors.ts': `\n    let f = () => 1;\n    let e: NotFound;\n    let s = { 1: 1, 2: 2 };\n    let t = typeof 12;\n  `,\n  'declared.ts': `\n    declare namespace Foo {\n      type A = string;\n    }\n\n    let a: Foo.A = 'some value';\n  `\n};\n"]}