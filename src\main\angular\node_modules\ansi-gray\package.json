{"_args": [["ansi-gray@0.1.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ansi-gray@0.1.1", "_id": "ansi-gray@0.1.1", "_inBundle": false, "_integrity": "sha1-KWLPVOyXksSFEKPetSRDaGHvclE=", "_location": "/ansi-gray", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ansi-gray@0.1.1", "name": "ansi-gray", "escapedName": "ansi-gray", "rawSpec": "0.1.1", "saveSpec": null, "fetchSpec": "0.1.1"}, "_requiredBy": ["/fancy-log"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ansi-gray/-/ansi-gray-0.1.1.tgz", "_spec": "0.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/ansi-gray/issues"}, "dependencies": {"ansi-wrap": "0.1.0"}, "description": "The color gray, in ansi.", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/ansi-gray", "keywords": ["gray", "256", "ansi", "cli", "color", "colors", "colour", "command", "command-line", "console", "format", "formatting", "iterm", "log", "logging", "rgb", "shell", "string", "style", "styles", "styling", "terminal", "text", "tty", "xterm"], "license": "MIT", "main": "index.js", "name": "ansi-gray", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/ansi-gray.git"}, "scripts": {"test": "mocha"}, "version": "0.1.1"}