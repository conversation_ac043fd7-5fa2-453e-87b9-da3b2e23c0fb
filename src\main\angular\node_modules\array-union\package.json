{"_args": [["array-union@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "array-union@1.0.2", "_id": "array-union@1.0.2", "_inBundle": false, "_integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "_location": "/array-union", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-union@1.0.2", "name": "array-union", "escapedName": "array-union", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/globby", "/maximatch"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-union/-/array-union-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/array-union/issues"}, "dependencies": {"array-uniq": "^1.0.1"}, "description": "Create an array of unique values, in order, from the input arrays", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/array-union#readme", "keywords": ["array", "arr", "set", "uniq", "unique", "duplicate", "remove", "union", "combine", "merge"], "license": "MIT", "name": "array-union", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/array-union.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}