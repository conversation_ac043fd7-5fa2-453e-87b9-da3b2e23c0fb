{"_args": [["connect-history-api-fallback@1.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "connect-history-api-fallback@1.3.0", "_id": "connect-history-api-fallback@1.3.0", "_inBundle": false, "_integrity": "sha1-5R0X+PDvDbkKZP20feMFFVbp8Wk=", "_location": "/connect-history-api-fallback", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "connect-history-api-fallback@1.3.0", "name": "connect-history-api-fallback", "escapedName": "connect-history-api-fallback", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/connect-history-api-fallback/-/connect-history-api-fallback-1.3.0.tgz", "_spec": "1.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bripkens.de"}, "bugs": {"url": "https://github.com/bripkens/connect-history-api-fallback/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.craigmyles.com"}], "description": "Provides a fallback for non-existing directories so that the HTML 5 history API can be used.", "devDependencies": {"eslint": "^0.18.0", "nodeunit": "^0.9.1", "sinon": "^1.14.1"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/bripkens/connect-history-api-fallback#readme", "keyswords": ["connect", "html5", "history api", "fallback", "spa"], "license": "MIT", "main": "lib/index.js", "name": "connect-history-api-fallback", "repository": {"type": "git", "url": "git+ssh://**************/bripkens/connect-history-api-fallback.git"}, "scripts": {"test": "eslint lib/index.js test/index_test.js && nodeunit test/index_test.js"}, "version": "1.3.0"}