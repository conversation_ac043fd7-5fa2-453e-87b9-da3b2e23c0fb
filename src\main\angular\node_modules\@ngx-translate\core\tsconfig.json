{"compilerOptions": {"noImplicitAny": true, "module": "es2015", "target": "es5", "emitDecoratorMetadata": true, "experimentalDecorators": true, "declaration": true, "moduleResolution": "node", "noUnusedLocals": true, "types": ["<PERSON><PERSON><PERSON>", "jasmine", "node"], "lib": ["es2015", "dom"]}, "files": ["index.ts", "./src/translate.pipe.ts", "./src/translate.service.ts", "./src/translate.parser.ts", "tests/translate.parser.spec.ts", "tests/translate.service.spec.ts", "tests/translate.pipe.spec.ts"], "exclude": ["node_modules", "bundles"], "angularCompilerOptions": {"strictMetadataEmit": true, "skipTemplateCodegen": true}}