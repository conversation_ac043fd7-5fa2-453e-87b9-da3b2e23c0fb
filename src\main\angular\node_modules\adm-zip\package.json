{"_args": [["adm-zip@0.4.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "adm-zip@0.4.7", "_id": "adm-zip@0.4.7", "_inBundle": false, "_integrity": "sha1-hgbCy/HEJs6MjsABdER/1Jtur8E=", "_location": "/adm-zip", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "adm-zip@0.4.7", "name": "adm-zip", "escapedName": "adm-zip", "rawSpec": "0.4.7", "saveSpec": null, "fetchSpec": "0.4.7"}, "_requiredBy": ["/protractor/webdriver-manager", "/selenium-webdriver"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/adm-zip/-/adm-zip-0.4.7.tgz", "_spec": "0.4.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/cthackers"}, "bugs": {"url": "https://github.com/cthackers/adm-zip/issues", "email": "<EMAIL>"}, "description": "A Javascript implementation of zip for nodejs. Allows user to create or extract zip files both in memory or to/from disk", "engines": {"node": ">=0.3.0"}, "files": ["adm-zip.js", "headers", "methods", "util", "zipEntry.js", "zipFile.js"], "homepage": "http://github.com/cthackers/adm-zip", "keywords": ["zip", "methods", "archive", "unzip"], "licenses": [{"type": "MIT", "url": "https://raw.github.com/cthackers/adm-zip/master/MIT-LICENSE.txt"}], "main": "adm-zip.js", "name": "adm-zip", "repository": {"type": "git", "url": "git+https://github.com/cthackers/adm-zip.git"}, "version": "0.4.7"}