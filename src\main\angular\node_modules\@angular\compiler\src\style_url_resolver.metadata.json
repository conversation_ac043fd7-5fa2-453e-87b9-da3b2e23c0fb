[{"__symbolic": "module", "version": 3, "metadata": {"StyleWithImports": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}]}}, "isStyleUrlResolvable": {"__symbolic": "function"}, "extractStyleUrls": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"StyleWithImports": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}]}}, "isStyleUrlResolvable": {"__symbolic": "function"}, "extractStyleUrls": {"__symbolic": "function"}}}]