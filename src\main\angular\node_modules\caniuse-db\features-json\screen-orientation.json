{"title": "Screen Orientation", "description": "Provides the ability to read the screen orientation state, to be informed when this state changes, and to be able to lock the screen orientation to a specific state.", "spec": "http://www.w3.org/TR/screen-orientation/", "status": "wd", "links": [{"url": "http://aurelio.audero.it/demo/screen-orientation-api-demo.html", "title": "Demo"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/Screen.orientation", "title": "Mozilla Developer Network (MDN) documentation - Screen Orientation"}, {"url": "http://www.sitepoint.com/introducing-screen-orientation-api/", "title": "SitePoint article"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "a x"}, "edge": {"12": "a x", "13": "a x", "14": "a x", "15": "a x", "16": "a x"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "a x", "19": "a x", "20": "a x", "21": "a x", "22": "a x", "23": "a x", "24": "a x", "25": "a x", "26": "a x", "27": "a x", "28": "a x", "29": "a x", "30": "a x", "31": "a x", "32": "a x", "33": "a x", "34": "a x", "35": "a x", "36": "a x", "37": "a x", "38": "a x", "39": "a x", "40": "a x", "41": "a x", "42": "a x", "43": "a x", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "a x"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "u", "5": "y"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to an [older version](http://www.w3.org/TR/2014/WD-screen-orientation-20140220/) of the draft specification, and the spec has undergone significant changes since, for example renaming the `screen.lockOrientation` method to `screen.orientation.lock`.", "notes_by_num": {}, "usage_perc_y": 69.94, "usage_perc_a": 5.24, "ucprefix": false, "parent": "", "keywords": "lockorientation,unlockorientation", "ie_id": "screenorientationapi", "chrome_id": "6191285283061760", "firefox_id": "screen-orientation", "webkit_id": "", "shown": true}