{"_args": [["archive-type@4.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "archive-type@4.0.0", "_id": "archive-type@4.0.0", "_inBundle": false, "_integrity": "sha1-+S5yIzBW38aWlHJ0nCZ72wRrHXA=", "_location": "/archive-type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "archive-type@4.0.0", "name": "archive-type", "escapedName": "archive-type", "rawSpec": "4.0.0", "saveSpec": null, "fetchSpec": "4.0.0"}, "_requiredBy": ["/download"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/archive-type/-/archive-type-4.0.0.tgz", "_spec": "4.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/archive-type/issues"}, "dependencies": {"file-type": "^4.2.0"}, "description": "Detect the archive type of a Buffer/Uint8Array", "devDependencies": {"ava": "*", "pify": "^2.3.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/kevva/archive-type#readme", "keywords": ["7zip", "archive", "buffer", "bz2", "bzip2", "check", "detect", "gz", "gzip", "mime", "rar", "zip", "file", "type"], "license": "MIT", "name": "archive-type", "repository": {"type": "git", "url": "git+https://github.com/kevva/archive-type.git"}, "scripts": {"test": "xo && ava"}, "version": "4.0.0"}