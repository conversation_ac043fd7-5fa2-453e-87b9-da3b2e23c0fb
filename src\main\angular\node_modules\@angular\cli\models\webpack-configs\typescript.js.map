{"version": 3, "file": "typescript.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["models/webpack-configs/typescript.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,6CAA0C;AAC1C,8CAA2C;AAG3C,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAG5C,MAAM,CAAC,GAAQ,MAAM,CAAC;AACtB,MAAM,aAAa,GAAW,CAAC,CAAC,mBAAmB,CAAC;MAChD,CAAC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC,IAAI;MAC7C,kBAAkB,CAAC;AAGvB,0BAA0B,GAAyB,EAAE,OAAY;IAC/D,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,GAAG,CAAC;IAErD,yDAAyD;IACzD,IAAI,oBAAoB,GAAQ,EAAE,CAAC;IACnC,uCAAuC;IACvC,EAAE,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACjC,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,EAAE,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;gBACvC,gBAAgB,GAAG,MAAM,GAAG,yBAAW,CAAA;;;;;;;;;;;;;;;;;;;;;SAqBtC,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,WAAW,CACnB,wEAAwE,gBAAgB,EAAE,CAC3F,CAAC;QAEJ,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,IAAI,WAAW,CAAC,gBAAgB,YAAY,CAAC,WAAW,mBAAmB,CAAC,CAAC;QACrF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QAC1D,MAAM,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC;QAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEjE,oBAAoB,GAAG;YACrB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;SACpE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,IAAI,mBAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;QACnC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC;QAChE,QAAQ,EAAE,YAAY,CAAC,QAAQ;QAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;QACnC,MAAM,EAAE,YAAY,CAAC,MAAM;QAC3B,oBAAoB;QACpB,gFAAgF;QAChF,OAAO,EAAE,EAAE;KACZ,EAAE,OAAO,CAAC,CAAC,CAAC;AACjB,CAAC;AAGY,QAAA,eAAe,GAAG,UAAS,GAAyB;IAC/D,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC;IACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEnF,MAAM,CAAC;QACL,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,EAAE;QAC7D,OAAO,EAAE,CAAE,gBAAgB,CAAC,GAAG,EAAE,EAAE,YAAY,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAE;KAC/E,CAAC;AACJ,CAAC,CAAC;AAEW,QAAA,YAAY,GAAG,UAAS,GAAyB;IAC5D,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;IACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IACnF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IAE3F,IAAI,aAAa,GAAQ,EAAE,YAAY,EAAE,CAAC;IAE1C,2FAA2F;IAC3F,EAAE,CAAC,CAAC,gBAAgB,KAAK,YAAY,CAAC,CAAC,CAAC;QACtC,IAAI,OAAO,GAAG,CAAE,cAAc,CAAE,CAAC;QACjC,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAAC,CAAC;QAC7F,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAED,MAAM,CAAC;QACL,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,EAAE;QAC7D,OAAO,EAAE,CAAE,gBAAgB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAE;KAClD,CAAC;AACJ,CAAC,CAAC;AAEW,QAAA,mBAAmB,GAAG,UAAS,GAAyB;IACnE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;IACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IACvF,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEtF,IAAI,aAAa,GAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;IAEpE,yEAAyE;IACzE,EAAE,CAAC,CAAC,YAAY,KAAK,eAAe,CAAC,CAAC,CAAC;QACrC,aAAa,CAAC,eAAe,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;IACzD,CAAC;IAED,MAAM,CAAC;QACL,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,EAAE;QAC7D,OAAO,EAAE,CAAE,gBAAgB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAE;KAClD,CAAC;AACJ,CAAC,CAAC"}