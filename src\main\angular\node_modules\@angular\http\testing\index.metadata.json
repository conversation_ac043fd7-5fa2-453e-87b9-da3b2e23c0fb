{"__symbolic": "module", "version": 3, "metadata": {"MockConnection": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/http", "name": "Request"}]}], "mockRespond": [{"__symbolic": "method"}], "mockDownload": [{"__symbolic": "method"}], "mockError": [{"__symbolic": "method"}]}}, "MockBackend": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "verifyNoPendingRequests": [{"__symbolic": "method"}], "resolveAllConnections": [{"__symbolic": "method"}], "createConnection": [{"__symbolic": "method"}]}}}, "origins": {"MockConnection": "./src/mock_backend", "MockBackend": "./src/mock_backend"}, "importAs": "@angular/http/testing"}