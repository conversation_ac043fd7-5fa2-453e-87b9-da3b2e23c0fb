{"version": 3, "file": "platform-browser-dynamic.js", "sources": ["../../../../packages/platform-browser-dynamic/index.ts", "../../../../packages/platform-browser-dynamic/src/platform-browser-dynamic.ts", "../../../../packages/platform-browser-dynamic/src/version.ts", "../../../../packages/platform-browser-dynamic/src/private_export.ts", "../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_cache.ts", "../../../../packages/platform-browser-dynamic/src/platform_providers.ts", "../../../../packages/platform-browser-dynamic/src/resource_loader/resource_loader_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\nexport * from './src/platform-browser-dynamic';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader, platformCoreDynamic} from '@angular/compiler';\nimport {PlatformRef, Provider, createPlatformFactory} from '@angular/core';\n\nimport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nimport {CachedResourceLoader} from './resource_loader/resource_loader_cache';\n\nexport * from './private_export';\nexport {VERSION} from './version';\n/**\n * @experimental\n */\nexport const RESOURCE_CACHE_PROVIDER: Provider[] =\n    [{provide: ResourceLoader, useClass: CachedResourceLoader}];\n\n/**\n * @stable\n */\nexport const platformBrowserDynamic = createPlatformFactory(\n    platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\nimport {Version} from '@angular/core';\n/**\n * @stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from './platform_providers';\nexport {ResourceLoaderImpl as ɵResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n */\nexport class CachedResourceLoader extends ResourceLoader {\n  private _cache: {[url: string]: string};\n\n  constructor() {\n    super();\n    this._cache = (<any>global).$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n\n  get(url: string): Promise<string> {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return <Promise<any>>Promise.reject(\n          'CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {ResourceLoader} from '@angular/compiler';\nimport {COMPILER_OPTIONS, PLATFORM_ID, Provider} from '@angular/core';\n\nimport {ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS as INTERNAL_BROWSER_PLATFORM_PROVIDERS} from '@angular/platform-browser';\n\nimport {ResourceLoaderImpl} from './resource_loader/resource_loader_impl';\n\nexport const INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS: Provider[] = [\n  INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n  {\n    provide: COMPILER_OPTIONS,\n    useValue: {providers: [{provide: ResourceLoader, useClass: ResourceLoaderImpl}]},\n    multi: true\n  },\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ResourceLoader} from '@angular/compiler';\nimport {Injectable} from '@angular/core';\n\n\n\nexport class ResourceLoaderImpl extends ResourceLoader {\n  get(url: string): Promise<string> {\n    let resolve: (result: any) => void;\n    let reject: (error: any) => void;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function() {\n      // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n      // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n      // by IE10)\n      const response = xhr.response || xhr.responseText;\n\n      // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n      let status = xhr.status === 1223 ? 204 : xhr.status;\n\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function() { reject(`Failed to load ${url}`); };\n\n    xhr.send();\n    return promise;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["global", "PLATFORM_BROWSER_ID", "INTERNAL_BROWSER_PLATFORM_PROVIDERS"], "mappings": ";;;;;AMAA;;;;;;;AAOA,AACA,AAIA,AAAA,MAAA,kBAAgC,SAAQ,cAAc,CAAtD;IACE,GAAG,CAAC,GAAW,EAAjB;QACI,IAAI,OAA8B,CAAC;QACnC,IAAI,MAA4B,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,KAAzC;YACM,OAAO,GAAG,GAAG,CAAC;YACd,MAAM,GAAG,GAAG,CAAC;SACd,CAAC,CAAC;QACH,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QAC3B,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC;QAE1B,GAAG,CAAC,MAAM,GAAG,YAAjB;;;;YAIM,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,CAAC;;YAGlD,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;;;;YAKpD,IAAI,MAAM,KAAK,CAAC,EAAE;gBAChB,MAAM,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;aAC7B;YAED,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,EAAE;gBAClC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACnB;iBAAM;gBACL,MAAM,CAAC,CAAf,eAAA,EAAiC,GAAG,CAApC,CAAsC,CAAC,CAAC;aACjC;SACF,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG,YAAlB,EAA+B,MAAM,CAAC,CAAtC,eAAA,EAAwD,GAAG,CAA3D,CAA6D,CAAC,CAAC,EAAE,CAAC;QAE9D,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,OAAO,CAAC;KAChB;;AACI,kBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,kBAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;;ADzDF;;;;;;;AAQA,AACA,AACA,AAEA,AAEA,AAEA,AAAO,MAAM,2CAA2C,GAAe;IACrEE,oCAAmC;IACnC;QACE,OAAO,EAAE,gBAAgB;QACzB,QAAQ,EAAE,EAAC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,kBAAkB,EAAC,CAAC,EAAC;QAChF,KAAK,EAAE,IAAI;KACZ;IACD,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAED,oBAAmB,EAAC;CACtD,CAAC;;ADxBF;;;;;;;AAQA,AACA,AAEA;;;;;;;AAOA,AAAA,MAAA,oBAAkC,SAAQ,cAAc,CAAxD;IAGE,WAAF,GAAA;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAASD,OAAO,CAAC,cAAc,CAAC;QAC3C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;SAC1F;KACF;IAED,GAAG,CAAC,GAAW,EAAjB;QACI,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;aAAM;YACL,OAAqB,OAAO,CAAC,MAAM,CAC/B,yDAAyD,GAAG,GAAG,CAAC,CAAC;SACtE;KACF;CACF;;ADrCD;;;;;;GAMG,AAEH,AACA,AAAiG;;ADTjG;;;;;;;;;;;;AAcA,AACA;;;AAGA,AAAO,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;;ADlBxD;;;;;;;AAQA,AACA,AAEA,AACA,AAEA,AACA,AACA;;;AAGA,AAAO,MAAM,uBAAuB,GAChC,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAC,CAAC,CAAC;;;;AAKhE,AAAO,MAAM,sBAAsB,GAAG,qBAAqB,CACvD,mBAAmB,EAAE,gBAAgB,EAAE,2CAA2C,CAAC,CAAC;;AD1BxF;;;;;;;;;;;;AAaA,AAA+C;0EAE2B;;"}