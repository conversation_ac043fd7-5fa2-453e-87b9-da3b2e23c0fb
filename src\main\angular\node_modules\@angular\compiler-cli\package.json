{"_args": [["@angular/compiler-cli@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@angular/compiler-cli@4.2.5", "_id": "@angular/compiler-cli@4.2.5", "_inBundle": false, "_integrity": "sha1-OzltZa3oOA83EgHUNh/JYqSax2o=", "_location": "/@angular/compiler-cli", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/compiler-cli@4.2.5", "name": "@angular/compiler-cli", "escapedName": "@angular%2fcompiler-cli", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/compiler-cli/-/compiler-cli-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bin": {"ngc": "src/main.js", "ng-xi18n": "src/extract_i18n.js"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}], "dependencies": {"@angular/tsc-wrapped": "4.2.5", "minimist": "^1.2.0", "reflect-metadata": "^0.1.2"}, "description": "Angular - the compiler CLI for Node.js", "homepage": "https://github.com/angular/angular/tree/master/packages/compiler-cli", "keywords": ["angular", "compiler"], "license": "MIT", "main": "index.js", "name": "@angular/compiler-cli", "peerDependencies": {"typescript": "^2.0.2", "@angular/compiler": "4.2.5", "@angular/core": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "index.d.ts", "version": "4.2.5"}