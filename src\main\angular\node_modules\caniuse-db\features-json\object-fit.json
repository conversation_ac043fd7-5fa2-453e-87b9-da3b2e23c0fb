{"title": "CSS3 object-fit/object-position", "description": "Method of specifying how an object (image or video) should fit inside its box. object-fit options include \"contain\" (fit according to aspect ratio), \"fill\" (stretches object to fill) and \"cover\" (overflows box but maintains ratio), where object-position allows the object to be repositioned like background-image does.", "spec": "http://www.w3.org/TR/css3-images/", "status": "cr", "links": [{"url": "https://dev.opera.com/articles/view/css3-object-fit-object-position/", "title": "Dev.Opera article"}, {"url": "https://www.webplatform.org/docs/css/properties/object-fit", "title": "WebPlatform Docs"}, {"url": "https://github.com/bfred-it/object-fit-images/", "title": "object-fit-images Polyfill for IE & Edge"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "y x", "11": "y x", "11.1": "y x", "11.5": "y x", "11.6": "y x", "12": "y x", "12.1": "y x", "15": "n", "16": "n", "17": "n", "18": "n", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "y x"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "y x", "11.1": "y x", "11.5": "y x", "12": "y x", "12.1": "y x", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support in Safari refers to support for `object-fit` but not `object-position`."}, "usage_perc_y": 89.14, "usage_perc_a": 1.05, "ucprefix": false, "parent": "", "keywords": "objectfit,objectposition", "ie_id": "objectfitandobjectposition", "chrome_id": "5302669702856704", "firefox_id": "", "webkit_id": "", "shown": true}