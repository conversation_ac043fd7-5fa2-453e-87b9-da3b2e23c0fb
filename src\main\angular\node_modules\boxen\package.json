{"_args": [["boxen@0.6.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "boxen@0.6.0", "_id": "boxen@0.6.0", "_inBundle": false, "_integrity": "sha1-g2TUJIrDT/DvGy8r9JpsYM4NgbY=", "_location": "/boxen", "_phantomChildren": {"code-point-at": "1.1.0", "number-is-nan": "1.0.1", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "boxen@0.6.0", "name": "boxen", "escapedName": "boxen", "rawSpec": "0.6.0", "saveSpec": null, "fetchSpec": "0.6.0"}, "_requiredBy": ["/update-notifier"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/boxen/-/boxen-0.6.0.tgz", "_spec": "0.6.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/boxen/issues"}, "dependencies": {"ansi-align": "^1.1.0", "camelcase": "^2.1.0", "chalk": "^1.1.1", "cli-boxes": "^1.0.0", "filled-array": "^1.0.0", "object-assign": "^4.0.1", "repeating": "^2.0.0", "string-width": "^1.0.1", "widest-line": "^1.0.0"}, "description": "Create boxes in the terminal", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/boxen#readme", "keywords": ["cli", "box", "boxes", "terminal", "term", "console", "ascii", "unicode", "border", "text"], "license": "MIT", "name": "boxen", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/boxen.git"}, "scripts": {"test": "xo && ava"}, "version": "0.6.0"}