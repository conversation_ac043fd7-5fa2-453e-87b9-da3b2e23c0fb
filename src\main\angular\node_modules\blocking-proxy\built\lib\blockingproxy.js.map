{"version": 3, "file": "blockingproxy.js", "sourceRoot": "", "sources": ["../../lib/blockingproxy.ts"], "names": [], "mappings": ";AAAA,MAAY,IAAI,WAAM,MAAM,CAAC,CAAA;AAE7B,uCAAiC,wBAAwB,CAAC,CAAA;AAC1D,0CAAoC,2BAA2B,CAAC,CAAA;AAChE,0CAAoC,2BAA2B,CAAC,CAAA;AAEhE,kCAA6B,mBAAmB,CAAC,CAAA;AAEpC,iBAAS,GAAG,SAAS,CAAC;AAEnC;;;;GAIG;AACH;IAOE,YAAY,eAAuB,EAAE,cAAc,GAAW,IAAI;QAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,KAAK,GAAG,IAAI,gCAAc,CAAC,eAAe,CAAC,CAAC;QAEjD,IAAI,MAAM,GAAG,IAAI,+CAAqB,CAAC,eAAe,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,IAAI,yCAAkB,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,+CAAqB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,OAAO,cAAc,CAAC,WAAmB;QACvC,MAAM,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,iBAAS,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,MAAc;QAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAuB;QAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ;QACxC,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YAChB,KAAK,aAAa;gBAChB,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC;oBAC7B,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAC,CAAC,CAAC,CAAC;oBAClE,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;oBACrC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;oBAClD,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBACjC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBACD,KAAK,CAAC;YACR,KAAK,YAAY;gBACf,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC;oBAC7B,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAC,CAAC,CAAC,CAAC;oBAC9E,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;oBACrC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC;oBAC9D,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACxB,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBACjC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBACD,KAAK,CAAC;YACR;gBACE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxB,QAAQ,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACnD,QAAQ,CAAC,GAAG,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,eAAe,CAAC,eAAqC,EAAE,QAA6B;QAClF,EAAE,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC3B,WAAW,IAAI,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE;gBACxB,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YACH,MAAM,CAAC;QACT,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,IAAY;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QAC5C,MAAM,CAAC,UAAU,CAAC;IACpB,CAAC;IAED,IAAI;QACF,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAnHY,qBAAa,gBAmHzB,CAAA"}