[{"__symbolic": "module", "version": 3, "metadata": {"TestingCompiler": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, "members": {"overrideModule": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}]}}, "TestingCompilerFactory": {"__symbolic": "class", "members": {"createTestingCompiler": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TestingCompiler": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, "members": {"overrideModule": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}]}}, "TestingCompilerFactory": {"__symbolic": "class", "members": {"createTestingCompiler": [{"__symbolic": "method"}]}}}}]