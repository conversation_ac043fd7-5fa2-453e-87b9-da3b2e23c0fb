{"_args": [["console-browserify@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "console-browserify@1.1.0", "_id": "console-browserify@1.1.0", "_inBundle": false, "_integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "_location": "/console-browserify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "console-browserify@1.1.0", "name": "console-browserify", "escapedName": "console-browserify", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/console-browserify/-/console-browserify-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/console-browserify/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>"}], "dependencies": {"date-now": "^0.1.4"}, "description": "Emulate console for all the browsers", "devDependencies": {"jsonify": "0.0.0", "run-browser": "^1.3.0", "tap-dot": "^0.2.1", "tap-spec": "^0.1.8", "tape": "^2.12.3"}, "homepage": "https://github.com/Raynos/console-browserify", "keywords": [], "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/console-browserify/raw/master/LICENSE"}], "main": "index", "name": "console-browserify", "repository": {"type": "git", "url": "git://github.com/Raynos/console-browserify.git"}, "scripts": {"browser": "run-browser test/index.js", "build": "browserify test/index.js -o test/static/bundle.js", "cover": "istanbul cover --report none --print detail ./test/index.js", "dot": "node ./test/index.js | tap-dot", "phantom": "run-browser test/index.js -b | tap-spec", "start": "node ./index.js", "test": "node ./test/index.js | tap-spec", "testem": "testem", "view-cover": "istanbul report html && google-chrome ./coverage/index.html"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.1.0"}