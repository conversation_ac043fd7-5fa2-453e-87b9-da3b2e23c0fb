{"version": 3, "file": "browser.es5.js", "sources": ["../../../../../packages/animations/browser/public_api.ts", "../../../../../packages/animations/browser/src/browser.ts", "../../../../../packages/animations/browser/src/private_export.ts", "../../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../../packages/animations/browser/src/dsl/animation_dsl_visitor.ts", "../../../../../packages/animations/browser/src/dsl/animation_ast.ts", "../../../../../packages/animations/browser/src/util.ts", "../../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../../packages/animations/browser/src/render/shared.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the animation package.\n */\nexport {AnimationDriver,ɵAnimation,ɵAnimationStyleNormalizer,ɵNoopAnimationStyleNormalizer,ɵWebAnimationsStyleNormalizer,ɵNoopAnimationDriver,ɵAnimationEngine,ɵWebAnimationsDriver,ɵsupportsWebAnimations,ɵWebAnimationsPlayer} from './src/browser';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {AnimationDriver} from './render/animation_driver';\nexport {ɵAnimation,ɵAnimationStyleNormalizer,ɵNoopAnimationStyleNormalizer,ɵWebAnimationsStyleNormalizer,ɵNoopAnimationDriver,ɵAnimationEngine,ɵWebAnimationsDriver,ɵsupportsWebAnimations,ɵWebAnimationsPlayer} from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {Animation as ɵAnimation} from './dsl/animation';\nexport {AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer} from './dsl/style_normalization/animation_style_normalizer';\nexport {WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer} from './dsl/style_normalization/web_animations_style_normalizer';\nexport {NoopAnimationDriver as ɵNoopAnimationDriver} from './render/animation_driver';\nexport {AnimationEngine as ɵAnimationEngine} from './render/animation_engine_next';\nexport {WebAnimationsDriver as ɵWebAnimationsDriver, supportsWebAnimations as ɵsupportsWebAnimations} from './render/web_animations/web_animations_driver';\nexport {WebAnimationsPlayer as ɵWebAnimationsPlayer} from './render/web_animations/web_animations_player';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../shared';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\nexport class WebAnimationsDriver implements AnimationDriver {\n/**\n * @param {?} element\n * @param {?} selector\n * @return {?}\n */\nmatchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n/**\n * @param {?} elm1\n * @param {?} elm2\n * @return {?}\n */\ncontainsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n/**\n * @param {?} element\n * @param {?} selector\n * @param {?} multi\n * @return {?}\n */\nquery(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n/**\n * @param {?} element\n * @param {?} prop\n * @param {?=} defaultValue\n * @return {?}\n */\ncomputeStyle(element: any, prop: string, defaultValue?: string): string {\n    return /** @type {?} */(( ( /** @type {?} */((window.getComputedStyle(element) as any)))[prop] as string));\n  }\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @param {?=} previousPlayers\n * @return {?}\n */\nanimate(\n      element: any, keyframes: ɵStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = []): WebAnimationsPlayer {\n    const /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n    const /** @type {?} */ playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */(( <WebAnimationsPlayer[]>previousPlayers.filter(\n        player => { return player instanceof WebAnimationsPlayer; })));\n    return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n  }\n}\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n  return typeof Element !== 'undefined' && typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {copyStyles, eraseStyles, setStyles} from '../../util';\n\nimport {DOMAnimation} from './dom_animation';\nexport class WebAnimationsPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _player: DOMAnimation;\nprivate _duration: number;\nprivate _delay: number;\nprivate _initialized = false;\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finalKeyframe: {[key: string]: string | number};\npublic time = 0;\npublic parentPlayer: AnimationPlayer|null = null;\npublic previousStyles: {[styleName: string]: string | number};\npublic currentSnapshot: {[styleName: string]: string | number} = {};\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @param {?=} previousPlayers\n */\nconstructor(\npublic element: any,\npublic keyframes: {[key: string]: string | number}[],\npublic options: {[key: string]: string | number},\nprivate previousPlayers: WebAnimationsPlayer[] = []) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n\n    this.previousStyles = {};\n    previousPlayers.forEach(player => {\n      let styles = player.currentSnapshot;\n      Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n    });\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n/**\n * @return {?}\n */\nprivate _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const /** @type {?} */ keyframes = this.keyframes.map(styles => copyStyles(styles, false));\n    const /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n    if (previousStyleProps.length) {\n      let /** @type {?} */ startingKeyframe = keyframes[0];\n      let /** @type {?} */ missingStyleProps: string[] = [];\n      previousStyleProps.forEach(prop => {\n        if (!startingKeyframe.hasOwnProperty(prop)) {\n          missingStyleProps.push(prop);\n        }\n        startingKeyframe[prop] = this.previousStyles[prop];\n      });\n\n      if (missingStyleProps.length) {\n        const /** @type {?} */ self = this;\n        // tslint:disable-next-line\n        for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n          let /** @type {?} */ kf = keyframes[i];\n          missingStyleProps.forEach(function(prop) {\n            kf[prop] = _computeStyle(self.element, prop);\n          });\n        }\n      }\n    }\n\n    this._player = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n    this._player.addEventListener('finish', () => this._onFinish());\n  }\n/**\n * @return {?}\n */\nprivate _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this._player.pause();\n    }\n  }\n/**\n * \\@internal\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @return {?}\n */\n_triggerWebAnimation(element: any, keyframes: any[], options: any): DOMAnimation {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return /** @type {?} */(( element['animate'](keyframes, options) as DOMAnimation));\n  }\n/**\n * @return {?}\n */\nget domPlayer() { return this._player; }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nplay(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n    this._player.play();\n  }\n/**\n * @return {?}\n */\npause(): void {\n    this.init();\n    this._player.pause();\n  }\n/**\n * @return {?}\n */\nfinish(): void {\n    this.init();\n    this._onFinish();\n    this._player.finish();\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @return {?}\n */\nprivate _resetDomPlayerState() {\n    if (this._player) {\n      this._player.cancel();\n    }\n  }\n/**\n * @return {?}\n */\nrestart(): void {\n    this.reset();\n    this.play();\n  }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._resetDomPlayerState();\n      this._onFinish();\n      this._destroyed = true;\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._player.currentTime = p * this.time; }\n/**\n * @return {?}\n */\ngetPosition(): number { return this._player.currentTime / this.time; }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._delay + this._duration; }\n/**\n * @return {?}\n */\nbeforeDestroy() {\n    const /** @type {?} */ styles: {[key: string]: string | number} = {};\n    if (this.hasStarted()) {\n      Object.keys(this._finalKeyframe).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] =\n              this._finished ? this._finalKeyframe[prop] : _computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n}\n\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDoneFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onStartFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDestroyFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._player;\n/** @type {?} */\nWebAnimationsPlayer.prototype._duration;\n/** @type {?} */\nWebAnimationsPlayer.prototype._delay;\n/** @type {?} */\nWebAnimationsPlayer.prototype._initialized;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finished;\n/** @type {?} */\nWebAnimationsPlayer.prototype._started;\n/** @type {?} */\nWebAnimationsPlayer.prototype._destroyed;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finalKeyframe;\n/** @type {?} */\nWebAnimationsPlayer.prototype.time;\n/** @type {?} */\nWebAnimationsPlayer.prototype.parentPlayer;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousStyles;\n/** @type {?} */\nWebAnimationsPlayer.prototype.currentSnapshot;\n/** @type {?} */\nWebAnimationsPlayer.prototype.element;\n/** @type {?} */\nWebAnimationsPlayer.prototype.keyframes;\n/** @type {?} */\nWebAnimationsPlayer.prototype.options;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousPlayers;\n}\n\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element: any, prop: string): string {\n  return ( /** @type {?} */((<any>window.getComputedStyle(element))))[prop];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\nexport class AnimationEngine {\nprivate _transitionEngine: TransitionAnimationEngine;\nprivate _timelineEngine: TimelineAnimationEngine;\nprivate _triggerCache: {[key: string]: AnimationTrigger} = {};\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    this._transitionEngine = new TransitionAnimationEngine(driver, normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(driver, normalizer);\n\n    this._transitionEngine.onRemovalComplete =\n        (element: any, context: any) => { this.onRemovalComplete(element, context); }\n  }\n/**\n * @param {?} componentId\n * @param {?} namespaceId\n * @param {?} hostElement\n * @param {?} name\n * @param {?} metadata\n * @return {?}\n */\nregisterTrigger(\n      componentId: string, namespaceId: string, hostElement: any, name: string,\n      metadata: AnimationTriggerMetadata): void {\n    const /** @type {?} */ cacheKey = componentId + '-' + name;\n    let /** @type {?} */ trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const /** @type {?} */ errors: any[] = [];\n      const /** @type {?} */ ast = /** @type {?} */(( buildAnimationAst( /** @type {?} */((metadata as AnimationMetadata)), errors) as TriggerAst));\n      if (errors.length) {\n        throw new Error(\n            `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n      }\n      trigger = buildTrigger(name, ast);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\nonInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\nonRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} property\n * @param {?} value\n * @return {?}\n */\nsetProperty(namespaceId: string, element: any, property: string, value: any): boolean {\n    // @@property\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const /** @type {?} */ args = /** @type {?} */(( value as any[]));\n      this._timelineEngine.command(id, element, action, args);\n      return false;\n    }\n    return this._transitionEngine.trigger(namespaceId, element, property, value);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} eventName\n * @param {?} eventPhase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, eventName: string, eventPhase: string,\n      callback: (event: any) => any): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1): void { this._transitionEngine.flush(microtaskId); }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] {\n    return ( /** @type {?} */((this._transitionEngine.players as AnimationPlayer[])))\n        .concat( /** @type {?} */((this._timelineEngine.players as AnimationPlayer[])));\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this._transitionEngine.whenRenderingDone(); }\n}\n\nfunction AnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationEngine.prototype._transitionEngine;\n/** @type {?} */\nAnimationEngine.prototype._timelineEngine;\n/** @type {?} */\nAnimationEngine.prototype._triggerCache;\n/** @type {?} */\nAnimationEngine.prototype.onRemovalComplete;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_PLAYER_ARRAY: AnimationPlayer[] = [];\nconst /** @type {?} */ NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst /** @type {?} */ NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\nexport interface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nexport const /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n\nexport interface ElementAnimationState {\n  setForRemoval: any;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n}\nexport class StateValue {\npublic value: string;\npublic options: AnimationOptions;\n/**\n * @param {?} input\n */\nconstructor(input: any) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      const options = copyObj(input as any);\n      delete options['value'];\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n/**\n * @param {?} options\n * @return {?}\n */\nabsorbOptions(options: AnimationOptions) {\n    const /** @type {?} */ newParams = options.params;\n    if (newParams) {\n      const /** @type {?} */ oldParams = /** @type {?} */(( this.options.params));\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nfunction StateValue_tsickle_Closure_declarations() {\n/** @type {?} */\nStateValue.prototype.value;\n/** @type {?} */\nStateValue.prototype.options;\n}\n\n\nexport const /** @type {?} */ VOID_VALUE = 'void';\nexport const /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport const /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nexport class AnimationTransitionNamespace {\npublic players: TransitionAnimationPlayer[] = [];\nprivate _triggers: {[triggerName: string]: AnimationTrigger} = {};\nprivate _queue: QueueInstruction[] = [];\nprivate _elementListeners = new Map<any, TriggerListener[]>();\nprivate _hostClassName: string;\n/**\n * @param {?} id\n * @param {?} hostElement\n * @param {?} _engine\n */\nconstructor(\npublic id: string,\npublic hostElement: any,\nprivate _engine: TransitionAnimationEngine) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.hasOwnProperty(name)) {\n      throw new Error(\n          `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw new Error(\n          `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw new Error(\n          `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n    }\n\n    const /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n    const /** @type {?} */ data = {name, phase, callback};\n    listeners.push(data);\n\n    const /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n    if (!triggersWithStates.hasOwnProperty(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates[name] = null;\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const /** @type {?} */ index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers[name]) {\n          delete triggersWithStates[name];\n        }\n      });\n    };\n  }\n/**\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nregister(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers[name]) {\n      // throw\n      return false;\n    } else {\n      this._triggers[name] = ast;\n      return true;\n    }\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate _getTrigger(name: string) {\n    const /** @type {?} */ trigger = this._triggers[name];\n    if (!trigger) {\n      throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n    }\n    return trigger;\n  }\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} value\n * @param {?=} defaultToFallback\n * @return {?}\n */\ntrigger(element: any, triggerName: string, value: any, defaultToFallback: boolean = true):\n      TransitionAnimationPlayer|undefined {\n    const /** @type {?} */ trigger = this._getTrigger(triggerName);\n    const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = {});\n    }\n\n    let /** @type {?} */ fromState = triggersWithStates[triggerName];\n    const /** @type {?} */ toState = new StateValue(value);\n\n    const /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates[triggerName] = toState;\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    } else if (fromState === DELETED_STATE_VALUE) {\n      return player;\n    }\n\n    const /** @type {?} */ playersOnElement: TransitionAnimationPlayer[] =\n        getOrSetAsInMap(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new palyer)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n    let /** @type {?} */ isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push(\n        {element, triggerName, transition, fromState, toState, player, isFallbackTransition});\n\n    if (!isFallbackTransition) {\n      addClass(element, NG_ANIMATING_CLASSNAME);\n    }\n\n    player.onDone(() => {\n      removeClass(element, NG_ANIMATING_CLASSNAME);\n\n      let /** @type {?} */ index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const /** @type {?} */ players = this._engine.playersByElement.get(element);\n      if (players) {\n        let /** @type {?} */ index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nderegister(name: string) {\n    delete this._triggers[name];\n\n    this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n          element, listeners.filter(entry => { return entry.name != name; }));\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n/**\n * @param {?} rootElement\n * @param {?} context\n * @param {?=} animate\n * @return {?}\n */\nprivate _destroyInnerNodes(rootElement: any, context: any, animate: boolean = false) {\n    this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n      if (animate && containsClass(elm, this._hostClassName)) {\n        const /** @type {?} */ innerNs = this._engine.namespacesByHostElement.get(elm);\n\n        // special case for a host element with animations on the same element\n        if (innerNs) {\n          innerNs.removeNode(elm, context, true);\n        }\n\n        this.removeNode(elm, context, true);\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n  }\n/**\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(element: any, context: any, doNotRecurse?: boolean): void {\n    const /** @type {?} */ engine = this._engine;\n\n    if (!doNotRecurse && element.childElementCount) {\n      this._destroyInnerNodes(element, context, true);\n    }\n\n    const /** @type {?} */ triggerStates = engine.statesByElement.get(element);\n    if (triggerStates) {\n      const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n      Object.keys(triggerStates).forEach(triggerName => {\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers[triggerName]) {\n          const /** @type {?} */ player = this.trigger(element, triggerName, VOID_VALUE, false);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        engine.markElementAsRemoved(this.id, element, true, context);\n        optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n        return;\n      }\n    }\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let /** @type {?} */ containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const /** @type {?} */ currentPlayers =\n          engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue fowards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let /** @type {?} */ parent = element;\n        while (parent = parent.parentNode) {\n          const /** @type {?} */ triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    const /** @type {?} */ listeners = this._elementListeners.get(element);\n    if (listeners) {\n      const /** @type {?} */ visitedTriggers = new Set<string>();\n      listeners.forEach(listener => {\n        const /** @type {?} */ triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const /** @type {?} */ trigger = this._triggers[triggerName];\n        const /** @type {?} */ transition = trigger.fallbackTransition;\n        const /** @type {?} */ elementStates = /** @type {?} */(( engine.statesByElement.get(element)));\n        const /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n        const /** @type {?} */ toState = new StateValue(VOID_VALUE);\n        const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      // we do this after the flush has occurred such\n      // that the callbacks can be fired\n      engine.afterFlush(() => this.clearElementCache(element));\n      engine.destroyInnerAnimations(element);\n      engine._onRemovalComplete(element, context);\n    }\n  }\n/**\n * @param {?} element\n * @param {?} parent\n * @return {?}\n */\ninsertNode(element: any, parent: any): void { addClass(element, this._hostClassName); }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\ndrainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const /** @type {?} */ instructions: QueueInstruction[] = [];\n    this._queue.forEach(entry => {\n      const /** @type {?} */ player = entry.player;\n      if (player.destroyed) return;\n\n      const /** @type {?} */ element = entry.element;\n      const /** @type {?} */ listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const /** @type {?} */ baseEvent = makeAnimationEvent(\n                element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            ( /** @type {?} */((baseEvent as any)))['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const /** @type {?} */ d0 = a.transition.ast.depCount;\n      const /** @type {?} */ d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n/**\n * @param {?} context\n * @return {?}\n */\ndestroy(context: any) {\n    this.players.forEach(p => p.destroy());\n    this._destroyInnerNodes(this.hostElement, context);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nelementContainsData(element: any): boolean {\n    let /** @type {?} */ containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData =\n        (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n}\n\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionNamespace.prototype.players;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._triggers;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._queue;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._elementListeners;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._hostClassName;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.id;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.hostElement;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._engine;\n}\n\n\nexport interface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\nexport class TransitionAnimationEngine {\npublic players: TransitionAnimationPlayer[] = [];\npublic newHostElements = new Map<any, AnimationTransitionNamespace>();\npublic playersByElement = new Map<any, TransitionAnimationPlayer[]>();\npublic playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\npublic statesByElement = new Map<any, {[triggerName: string]: StateValue}>();\npublic totalAnimations = 0;\npublic totalQueuedPlayers = 0;\nprivate _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\nprivate _namespaceList: AnimationTransitionNamespace[] = [];\nprivate _flushFns: (() => any)[] = [];\nprivate _whenQuietFns: (() => any)[] = [];\npublic namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\npublic collectedEnterElements: any[] = [];\npublic collectedLeaveElements: any[] = [];\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\n_onRemovalComplete(element: any, context: any) { this.onRemovalComplete(element, context); }\n/**\n * @param {?} driver\n * @param {?} _normalizer\n */\nconstructor(public driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @return {?}\n */\nget queuedPlayers(): TransitionAnimationPlayer[] {\n    const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\ncreateNamespace(namespaceId: string, hostElement: any) {\n    const /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (hostElement.parentNode) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is apart of the animation code, it\n      // may or may not be inserted by a parent node that is an of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n/**\n * @param {?} ns\n * @param {?} hostElement\n * @return {?}\n */\nprivate _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const /** @type {?} */ limit = this._namespaceList.length - 1;\n    if (limit >= 0) {\n      let /** @type {?} */ found = false;\n      for (let /** @type {?} */ i = limit; i >= 0; i--) {\n        const /** @type {?} */ nextNamespace = this._namespaceList[i];\n        if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n          this._namespaceList.splice(i + 1, 0, ns);\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        this._namespaceList.splice(0, 0, ns);\n      }\n    } else {\n      this._namespaceList.push(ns);\n    }\n\n    this.namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} name\n * @param {?} trigger\n * @return {?}\n */\nregisterTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n\n    const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n      const /** @type {?} */ index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _fetchNamespace(id: string) { return this._namespaceLookup[id]; }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\ntrigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      this._fetchNamespace(namespaceId).trigger(element, name, value);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\ninsertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      this._fetchNamespace(namespaceId).insertNode(element, parent);\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ncollectEnterElement(element: any) { this.collectedEnterElements.push(element); }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(namespaceId: string, element: any, context: any, doNotRecurse?: boolean): void {\n    if (!isElementNode(element)) {\n      this._onRemovalComplete(element, context);\n      return;\n    }\n\n    const /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n    if (ns) {\n      ns.removeNode(element, context, doNotRecurse);\n    } else {\n      this.markElementAsRemoved(namespaceId, element, false, context);\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?=} hasAnimation\n * @param {?=} context\n * @return {?}\n */\nmarkElementAsRemoved(namespaceId: string, element: any, hasAnimation?: boolean, context?: any) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context, hasAnimation,\n      removedBeforeQueried: false\n    };\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, name: string, phase: string,\n      callback: (event: any) => boolean): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n/**\n * @param {?} entry\n * @param {?} subTimelines\n * @return {?}\n */\nprivate _buildInstruction(entry: QueueInstruction, subTimelines: ElementInstructionMap) {\n    return entry.transition.build(\n        this.driver, entry.element, entry.fromState.value, entry.toState.value,\n        entry.toState.options, subTimelines);\n  }\n/**\n * @param {?} containerElement\n * @return {?}\n */\ndestroyInnerAnimations(containerElement: any) {\n    let /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => {\n      const /** @type {?} */ players = this.playersByElement.get(element);\n      if (players) {\n        players.forEach(player => {\n          // special case for when an element is set for destruction, but hasn't started.\n          // in this situation we want to delay the destruction until the flush occurs\n          // so that any event listeners attached to the player are triggered.\n          if (player.queued) {\n            player.markedForDestroy = true;\n          } else {\n            player.destroy();\n          }\n        });\n      }\n      const /** @type {?} */ stateMap = this.statesByElement.get(element);\n      if (stateMap) {\n        Object.keys(stateMap).forEach(triggerName => stateMap[triggerName] = DELETED_STATE_VALUE);\n      }\n    });\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    if (elements.length) {\n      elements.forEach(element => {\n        const /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n          players.forEach(player => player.finish());\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprocessLeaveNode(element: any) {\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1) {\n    let /** @type {?} */ players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this._namespaceList.length &&\n        (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      players = this._flushAnimations(microtaskId);\n    } else {\n      for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n        const /** @type {?} */ element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const /** @type {?} */ quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\nprivate _flushAnimations(microtaskId: number): TransitionAnimationPlayer[] {\n    const /** @type {?} */ subTimelines = new ElementInstructionMap();\n    const /** @type {?} */ skippedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const /** @type {?} */ queuedInstructions: QueuedTransition[] = [];\n    const /** @type {?} */ queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const /** @type {?} */ allPreStyleElements = new Map<any, Set<string>>();\n    const /** @type {?} */ allPostStyleElements = new Map<any, Set<string>>();\n\n    const /** @type {?} */ bodyNode = getBodyNode();\n    const /** @type {?} */ allEnterNodes: any[] = this.collectedEnterElements.length ?\n        this.collectedEnterElements.filter(createIsRootFilterFn(this.collectedEnterElements)) :\n        [];\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      addClass(allEnterNodes[i], ENTER_CLASSNAME);\n    }\n\n    const /** @type {?} */ allLeaveNodes: any[] = [];\n    const /** @type {?} */ leaveNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n      const /** @type {?} */ element = this.collectedLeaveElements[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n      if (details && details.setForRemoval) {\n        addClass(element, LEAVE_CLASSNAME);\n        allLeaveNodes.push(element);\n        if (!details.hasAnimation) {\n          leaveNodesWithoutAnimations.push(element);\n        }\n      }\n    }\n\n    for (let /** @type {?} */ i = this._namespaceList.length - 1; i >= 0; i--) {\n      const /** @type {?} */ ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const /** @type {?} */ player = entry.player;\n\n        const /** @type {?} */ element = entry.element;\n        if (!bodyNode || !this.driver.containsElement(bodyNode, element)) {\n          player.destroy();\n          return;\n        }\n\n        const /** @type {?} */ instruction = this._buildInstruction(entry, subTimelines);\n        if (!instruction) return;\n\n        // if a unmatched transition is queued to go then it SHOULD NOT render\n        // an animation and cancel the previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub trigger\n        // then it will instruct the timeline builder to not add a player delay, but\n        // instead stretch the first keyframe gap up until the animation starts. The\n        // reason this is important is to prevent extra initialization styles from being\n        // required by the user in the animation.\n        instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n\n        subTimelines.append(element, instruction.timelines);\n\n        const /** @type {?} */ tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach(\n            element => getOrSetAsInMap(queriedElements, element, []).push(player));\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          if (props.length) {\n            let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPreStyleElements.get(element)));\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set<string>());\n            }\n            props.forEach(prop => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPostStyleElements.get(element)));\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set<string>());\n          }\n          props.forEach(prop => setVal.add(prop));\n        });\n      });\n    }\n\n    // these can only be detected here since we have a map of all the elements\n    // that have animations attached to them...\n    const /** @type {?} */ enterNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      const /** @type {?} */ element = allEnterNodes[i];\n      if (!subTimelines.has(element)) {\n        enterNodesWithoutAnimations.push(element);\n      }\n    }\n\n    const /** @type {?} */ allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    let /** @type {?} */ sortedParentElements: any[] = [];\n    queuedInstructions.forEach(entry => {\n      const /** @type {?} */ element = entry.element;\n      if (subTimelines.has(element)) {\n        sortedParentElements.unshift(element);\n        this._beforeAnimationBuild(\n            entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n\n    skippedPlayers.forEach(player => {\n      const /** @type {?} */ element = player.element;\n      const /** @type {?} */ previousPlayers =\n          this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(\n          prevPlayer => { getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer); });\n    });\n\n    allPreviousPlayersMap.forEach(players => players.forEach(player => player.destroy()));\n\n    // PRE STAGE: fill the ! styles\n    const /** @type {?} */ preStylesMap = allPreStyleElements.size ?\n        cloakAndComputeStyles(\n            this.driver, enterNodesWithoutAnimations, allPreStyleElements, PRE_STYLE) :\n        new Map<any, ɵStyleData>();\n\n    // POST STAGE: fill the * styles\n    const /** @type {?} */ postStylesMap = cloakAndComputeStyles(\n        this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n\n    const /** @type {?} */ rootPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ subPlayers: TransitionAnimationPlayer[] = [];\n    queuedInstructions.forEach(entry => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        const /** @type {?} */ innerPlayer = this._buildAnimation(\n            player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap,\n            postStylesMap);\n        player.setRealPlayer(innerPlayer);\n\n        let /** @type {?} */ parentHasPriority: any = null;\n        for (let /** @type {?} */ i = 0; i < sortedParentElements.length; i++) {\n          const /** @type {?} */ parent = sortedParentElements[i];\n          if (parent === element) break;\n          if (this.driver.containsElement(parent, element)) {\n            parentHasPriority = parent;\n            break;\n          }\n        }\n\n        if (parentHasPriority) {\n          const /** @type {?} */ parentPlayers = this.playersByElement.get(parentHasPriority);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        } else {\n          rootPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        subPlayers.push(player);\n      }\n    });\n\n    subPlayers.forEach(player => {\n      const /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.parentPlayer.onDestroy(() => player.destroy());\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let /** @type {?} */ i = 0; i < allLeaveNodes.length; i++) {\n      const /** @type {?} */ element = allLeaveNodes[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let /** @type {?} */ players: AnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n          let /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      if (players.length) {\n        removeNodesAfterAnimationDone(this, element, players);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const /** @type {?} */ index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    allEnterNodes.forEach(element => removeClass(element, ENTER_CLASSNAME));\n\n    return rootPlayers;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @return {?}\n */\nelementContainsData(namespaceId: string, element: any) {\n    let /** @type {?} */ containsData = false;\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlush(callback: () => any) { this._flushFns.push(callback); }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlushAnimationsDone(callback: () => any) { this._whenQuietFns.push(callback); }\n/**\n * @param {?} element\n * @param {?} isQueriedElement\n * @param {?=} namespaceId\n * @param {?=} triggerName\n * @param {?=} toStateValue\n * @return {?}\n */\nprivate _getPreviousPlayers(\n      element: string, isQueriedElement: boolean, namespaceId?: string, triggerName?: string,\n      toStateValue?: any): TransitionAnimationPlayer[] {\n    let /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const /** @type {?} */ isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @return {?}\n */\nprivate _beforeAnimationBuild(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>) {\n    // it's important to do this step before destroying the players\n    // so that the onDone callback below won't fire before this\n    eraseStyles(instruction.element, instruction.fromStyles);\n\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const /** @type {?} */ targetNameSpaceId: string|undefined =\n        instruction.isRemovalTransition ? undefined : namespaceId;\n    const /** @type {?} */ targetTriggerName: string|undefined =\n        instruction.isRemovalTransition ? undefined : triggerName;\n\n    instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      const /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n      const /** @type {?} */ previousPlayers = this._getPreviousPlayers(\n          element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const /** @type {?} */ realPlayer = /** @type {?} */(( player.getRealPlayer() as any));\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        players.push(player);\n      });\n    });\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @param {?} skippedPlayersMap\n * @param {?} preStylesMap\n * @param {?} postStylesMap\n * @return {?}\n */\nprivate _buildAnimation(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n      skippedPlayersMap: Map<any, AnimationPlayer[]>, preStylesMap: Map<any, ɵStyleData>,\n      postStylesMap: Map<any, ɵStyleData>): AnimationPlayer {\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const /** @type {?} */ allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ allConsumedElements = new Set<any>();\n    const /** @type {?} */ allSubElements = new Set<any>();\n    const /** @type {?} */ allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const /** @type {?} */ details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer();\n\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      let /** @type {?} */ previousPlayers: AnimationPlayer[] = EMPTY_PLAYER_ARRAY;\n      if (!allConsumedElements.has(element)) {\n        allConsumedElements.add(element);\n        const /** @type {?} */ _previousPlayers = allPreviousPlayersMap.get(element);\n        if (_previousPlayers) {\n          previousPlayers = _previousPlayers.map(p => p.getRealPlayer());\n        }\n      }\n      const /** @type {?} */ preStyles = preStylesMap.get(element);\n      const /** @type {?} */ postStyles = postStylesMap.get(element);\n      const /** @type {?} */ keyframes = normalizeKeyframes(\n          this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles,\n          postStyles);\n      const /** @type {?} */ player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach(player => {\n      getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(\n        element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n\n    return player;\n  }\n/**\n * @param {?} instruction\n * @param {?} keyframes\n * @param {?} previousPlayers\n * @return {?}\n */\nprivate _buildPlayer(\n      instruction: AnimationTimelineInstruction, keyframes: ɵStyleData[],\n      previousPlayers: AnimationPlayer[]): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n          instruction.element, keyframes, instruction.duration, instruction.delay,\n          instruction.easing, previousPlayers);\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer();\n  }\n}\n\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationEngine.prototype.players;\n/** @type {?} */\nTransitionAnimationEngine.prototype.newHostElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByQueriedElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.statesByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalAnimations;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalQueuedPlayers;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceLookup;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceList;\n/** @type {?} */\nTransitionAnimationEngine.prototype._flushFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype._whenQuietFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype.namespacesByHostElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedEnterElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedLeaveElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.onRemovalComplete;\n/** @type {?} */\nTransitionAnimationEngine.prototype.driver;\n/** @type {?} */\nTransitionAnimationEngine.prototype._normalizer;\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\nprivate _player: AnimationPlayer = new NoopAnimationPlayer();\nprivate _containsRealPlayer = false;\nprivate _queuedCallbacks: {[name: string]: (() => any)[]} = {};\nprivate _destroyed = false;\npublic parentPlayer: AnimationPlayer;\npublic markedForDestroy: boolean = false;\n/**\n * @param {?} namespaceId\n * @param {?} triggerName\n * @param {?} element\n */\nconstructor(public namespaceId: string,\npublic triggerName: string,\npublic element: any) {}\n/**\n * @return {?}\n */\nget queued() { return this._containsRealPlayer == false; }\n/**\n * @return {?}\n */\nget destroyed() { return this._destroyed; }\n/**\n * @param {?} player\n * @return {?}\n */\nsetRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    Object.keys(this._queuedCallbacks).forEach(phase => {\n      this._queuedCallbacks[phase].forEach(\n          callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks = {};\n    this._containsRealPlayer = true;\n  }\n/**\n * @return {?}\n */\ngetRealPlayer() { return this._player; }\n/**\n * @param {?} name\n * @param {?} callback\n * @return {?}\n */\nprivate _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n/**\n * @return {?}\n */\ninit(): void { this._player.init(); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this.queued ? false : this._player.hasStarted(); }\n/**\n * @return {?}\n */\nplay(): void { !this.queued && this._player.play(); }\n/**\n * @return {?}\n */\npause(): void { !this.queued && this._player.pause(); }\n/**\n * @return {?}\n */\nrestart(): void { !this.queued && this._player.restart(); }\n/**\n * @return {?}\n */\nfinish(): void { this._player.finish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._destroyed = true;\n    this._player.destroy();\n  }\n/**\n * @return {?}\n */\nreset(): void { !this.queued && this._player.reset(); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: any): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n/**\n * @return {?}\n */\ngetPosition(): number { return this.queued ? 0 : this._player.getPosition(); }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._player.totalTime; }\n}\n\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationPlayer.prototype._player;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._containsRealPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._queuedCallbacks;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.markedForDestroy;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.namespaceId;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.triggerName;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.element;\n}\n\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map: Map<any, any[]>| {[key: string]: any}, key: any, value: any) {\n  let /** @type {?} */ currentValues: any[]|null|undefined;\n  if (map instanceof Map) {\n    currentValues = map.get(key);\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        map.delete(key);\n      }\n    }\n  } else {\n    currentValues = map[key];\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        delete map[key];\n      }\n    }\n  }\n  return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value: any): string {\n  switch (typeof value) {\n    case 'boolean':\n      return value ? '1' : '0';\n    default:\n      return value != null ? value.toString() : null;\n  }\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element: any, value?: string) {\n  const /** @type {?} */ oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n/**\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(\n    driver: AnimationDriver, elements: any[], elementPropsMap: Map<any, Set<string>>,\n    defaultStyle: string): Map<any, ɵStyleData> {\n  const /** @type {?} */ cloakVals = elements.map(element => cloakElement(element));\n  const /** @type {?} */ valuesMap = new Map<any, ɵStyleData>();\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const /** @type {?} */ styles: ɵStyleData = {};\n    props.forEach(prop => {\n      const /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  elements.forEach((element, i) => cloakElement(element, cloakVals[i]));\n  return valuesMap;\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction createIsRootFilterFn(nodes: any): (node: any) => boolean {\n  const /** @type {?} */ nodeSet = new Set(nodes);\n  const /** @type {?} */ knownRootContainer = new Set();\n  let /** @type {?} */ isRoot: (node: any) => boolean;\n  isRoot = node => {\n    if (!node) return true;\n    if (nodeSet.has(node.parentNode)) return false;\n    if (knownRootContainer.has(node.parentNode)) return true;\n    if (isRoot(node.parentNode)) {\n      knownRootContainer.add(node);\n      return true;\n    }\n    return false;\n  };\n  return isRoot;\n}\n\nconst /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element: any, className: string): boolean {\n  if (element.classList) {\n    return element.classList.contains(className);\n  } else {\n    const /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n    return classes && classes[className];\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (!classes) {\n      classes = element[CLASSES_CACHE_KEY] = {};\n    }\n    classes[className] = true;\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (classes) {\n      delete classes[className];\n    }\n  }\n}\n/**\n * @return {?}\n */\nfunction getBodyNode(): any|null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(\n    engine: TransitionAnimationEngine, element: any, players: AnimationPlayer[]) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationMetadata, AnimationOptions, AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nexport class TimelineAnimationEngine {\nprivate _animations: {[id: string]: Ast} = {};\nprivate _playersById: {[id: string]: AnimationPlayer} = {};\npublic players: AnimationPlayer[] = [];\n/**\n * @param {?} _driver\n * @param {?} _normalizer\n */\nconstructor(private _driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @param {?} id\n * @param {?} metadata\n * @return {?}\n */\nregister(id: string, metadata: AnimationMetadata|AnimationMetadata[]) {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = buildAnimationAst(metadata, errors);\n    if (errors.length) {\n      throw new Error(\n          `Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    } else {\n      this._animations[id] = ast;\n    }\n  }\n/**\n * @param {?} i\n * @param {?} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nprivate _buildPlayer(\n      i: AnimationTimelineInstruction, preStyles: ɵStyleData,\n      postStyles?: ɵStyleData): AnimationPlayer {\n    const /** @type {?} */ element = i.element;\n    const /** @type {?} */ keyframes = normalizeKeyframes(\n        this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = this._animations[id];\n    let /** @type {?} */ instructions: AnimationTimelineInstruction[];\n\n    const /** @type {?} */ autoStylesMap = new Map<any, ɵStyleData>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n          this._driver, element, ast, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n        inst.postStyleProps.forEach(prop => styles[prop] = null);\n      });\n    } else {\n      errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw new Error(\n          `Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      Object.keys(styles).forEach(\n          prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n    });\n\n    const /** @type {?} */ players = instructions.map(i => {\n      const /** @type {?} */ styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, {}, styles);\n    });\n    const /** @type {?} */ player = optimizeGroupPlayer(players);\n    this._playersById[id] = player;\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\ndestroy(id: string) {\n    const /** @type {?} */ player = this._getPlayer(id);\n    player.destroy();\n    delete this._playersById[id];\n    const /** @type {?} */ index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _getPlayer(id: string): AnimationPlayer {\n    const /** @type {?} */ player = this._playersById[id];\n    if (!player) {\n      throw new Error(`Unable to find the timeline player referenced by ${id}`);\n    }\n    return player;\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(id: string, element: string, eventName: string, callback: (event: any) => any):\n      () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\ncommand(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, /** @type {?} */(( args[0] as AnimationMetadata | AnimationMetadata[])));\n      return;\n    }\n\n    if (command == 'create') {\n      const /** @type {?} */ options = /** @type {?} */(( (args[0] || {}) as AnimationOptions));\n      this.create(id, element, options);\n      return;\n    }\n\n    const /** @type {?} */ player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat( /** @type {?} */((args[0] as string))));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineAnimationEngine.prototype._animations;\n/** @type {?} */\nTimelineAnimationEngine.prototype._playersById;\n/** @type {?} */\nTimelineAnimationEngine.prototype.players;\n/** @type {?} */\nTimelineAnimationEngine.prototype._driver;\n/** @type {?} */\nTimelineAnimationEngine.prototype._normalizer;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\n\nimport {copyStyles} from '../util';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTransitionFactory} from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name: string, ast: TriggerAst): AnimationTrigger {\n  return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class AnimationTrigger {\npublic transitionFactories: AnimationTransitionFactory[] = [];\npublic fallbackTransition: AnimationTransitionFactory;\npublic states: {[stateName: string]: ɵStyleData} = {};\n/**\n * @param {?} name\n * @param {?} ast\n */\nconstructor(public name: string,\npublic ast: TriggerAst) {\n    ast.states.forEach(ast => {\n      const obj = this.states[ast.name] = {};\n      ast.style.styles.forEach(styleTuple => {\n        if (typeof styleTuple == 'object') {\n          copyStyles(styleTuple as ɵStyleData, false, obj);\n        }\n      });\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n/**\n * @return {?}\n */\nget containsQueries() { return this.ast.queryCount > 0; }\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatchTransition(currentState: any, nextState: any): AnimationTransitionFactory|null {\n    const /** @type {?} */ entry = this.transitionFactories.find(f => f.match(currentState, nextState));\n    return entry || null;\n  }\n}\n\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTrigger.prototype.transitionFactories;\n/** @type {?} */\nAnimationTrigger.prototype.fallbackTransition;\n/** @type {?} */\nAnimationTrigger.prototype.states;\n/** @type {?} */\nAnimationTrigger.prototype.name;\n/** @type {?} */\nAnimationTrigger.prototype.ast;\n}\n\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(\n    triggerName: string, states: {[stateName: string]: ɵStyleData}): AnimationTransitionFactory {\n  const /** @type {?} */ matchers = [(fromState: any, toState: any) => true];\n  const /** @type {?} */ animation = new SequenceAst([]);\n  const /** @type {?} */ transition = new TransitionAst(matchers, animation);\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj: {[key: string]: any}, key1: string, key2: string) {\n  if (obj.hasOwnProperty(key1)) {\n    if (!obj.hasOwnProperty(key2)) {\n      obj[key2] = obj[key1];\n    }\n  } else if (obj.hasOwnProperty(key2)) {\n    obj[key1] = obj[key2];\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {iteratorToArray, mergeAnimationOptions} from '../util';\n\nimport {TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {AnimationTransitionInstruction, createTransitionInstruction} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class AnimationTransitionFactory {\n/**\n * @param {?} _triggerName\n * @param {?} ast\n * @param {?} _stateStyles\n */\nconstructor(\nprivate _triggerName: string,\npublic ast: TransitionAst,\nprivate _stateStyles: {[stateName: string]: ɵStyleData}) {}\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatch(currentState: any, nextState: any): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n  }\n/**\n * @param {?} driver\n * @param {?} element\n * @param {?} currentState\n * @param {?} nextState\n * @param {?=} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuild(\n      driver: AnimationDriver, element: any, currentState: any, nextState: any,\n      options?: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTransitionInstruction|undefined {\n    const /** @type {?} */ animationOptions = mergeAnimationOptions(this.ast.options || {}, options || {});\n\n    const /** @type {?} */ backupStateStyles = this._stateStyles['*'] || {};\n    const /** @type {?} */ currentStateStyles = this._stateStyles[currentState] || backupStateStyles;\n    const /** @type {?} */ nextStateStyles = this._stateStyles[nextState] || backupStateStyles;\n\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ timelines = buildAnimationTimelines(\n        driver, element, this.ast.animation, currentStateStyles, nextStateStyles, animationOptions,\n        subInstructions, errors);\n\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n\n    const /** @type {?} */ preStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ postStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ queriedElements = new Set<any>();\n    timelines.forEach(tl => {\n      const /** @type {?} */ elm = tl.element;\n      const /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n      tl.preStyleProps.forEach(prop => preProps[prop] = true);\n\n      const /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n      tl.postStyleProps.forEach(prop => postProps[prop] = true);\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    const /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(\n        element, this._triggerName, currentState, nextState, nextState === 'void',\n        currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap,\n        postStyleMap);\n  }\n}\n\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionFactory.prototype._triggerName;\n/** @type {?} */\nAnimationTransitionFactory.prototype.ast;\n/** @type {?} */\nAnimationTransitionFactory.prototype._stateStyles;\n}\n\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(\n    matchFns: TransitionMatcherFn[], currentState: any, nextState: any): boolean {\n  return matchFns.some(fn => fn(currentState, nextState));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleData;\n  toState: string;\n  toStyles: ɵStyleData;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, {[prop: string]: boolean}>;\n  postStyleProps: Map<any, {[prop: string]: boolean}>;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @return {?}\n */\nexport function createTransitionInstruction(\n    element: any, triggerName: string, fromState: string, toState: string,\n    isRemovalTransition: boolean, fromStyles: ɵStyleData, toStyles: ɵStyleData,\n    timelines: AnimationTimelineInstruction[], queriedElements: any[],\n    preStyleProps: Map<any, {[prop: string]: boolean}>,\n    postStyleProps: Map<any, {[prop: string]: boolean}>): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n/**\n * @param {?} propertyName\n * @param {?} errors\n * @return {?}\n */\nnormalizePropertyName(propertyName: string, errors: string[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n/**\n * @param {?} userProvidedProperty\n * @param {?} normalizedProperty\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nnormalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    let /** @type {?} */ unit: string = '';\n    const /** @type {?} */ strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n\nconst /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap(\n    'width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys: string[]): {[key: string]: boolean} {\n  const /** @type {?} */ map: {[key: string]: boolean} = {};\n  keys.forEach(key => map[key] = true);\n  return map;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @experimental Animation support is experimental.\n */\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: string[]): string;\n  abstract normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string { return propertyName; }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {normalizeStyles} from '../util';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class Animation {\nprivate _animationAst: Ast;\n/**\n * @param {?} _driver\n * @param {?} input\n */\nconstructor(private _driver: AnimationDriver, input: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(input, errors);\n    if (errors.length) {\n      const errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    this._animationAst = ast;\n  }\n/**\n * @param {?} element\n * @param {?} startingStyles\n * @param {?} destinationStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuildTimelines(\n      element: any, startingStyles: ɵStyleData|ɵStyleData[],\n      destinationStyles: ɵStyleData|ɵStyleData[], options: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[] {\n    const /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */((\n                                                  <ɵStyleData>startingStyles));\n    const /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */((\n                                                    <ɵStyleData>destinationStyles));\n    const /** @type {?} */ errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ result = buildAnimationTimelines(\n        this._driver, element, this._animationAst, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    return result;\n  }\n}\n\nfunction Animation_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimation.prototype._animationAst;\n/** @type {?} */\nAnimation.prototype._driver;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationOptions, AnimationQueryOptions, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, AstVisitor, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTimelineInstruction, createTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(\n    driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData = {},\n    finalStyles: ɵStyleData = {}, options: AnimationOptions,\n    subInstructions?: ElementInstructionMap, errors: any[] = []): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n      driver, rootElement, ast, startingStyles, finalStyles, options, subInstructions, errors);\n}\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} startingStyles\n * @param {?} finalStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nbuildKeyframes(\n      driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData,\n      finalStyles: ɵStyleData, options: AnimationOptions, subInstructions?: ElementInstructionMap,\n      errors: any[] = []): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, errors, []);\n    context.options = options;\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    ast.visit(this, context);\n\n    // this checks to see if an actual animation happened\n    const /** @type {?} */ timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    if (timelines.length && Object.keys(finalStyles).length) {\n      const /** @type {?} */ tl = timelines[timelines.length - 1];\n      if (!tl.allowOnlyTimelineStyles()) {\n        tl.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n                              [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n    if (elementInstructions) {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n      const /** @type {?} */ endTime = this._visitSubInstructions(\n          elementInstructions, innerContext, /** @type {?} */(( innerContext.options as AnimateChildOptions)));\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} instructions\n * @param {?} context\n * @param {?} options\n * @return {?}\n */\nprivate _visitSubInstructions(\n      instructions: AnimationTimelineInstruction[], context: AnimationTimelineContext,\n      options: AnimateChildOptions): number {\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    let /** @type {?} */ furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const /** @type {?} */ instructionTimings =\n            context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime =\n            Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ subContextCount = context.subContextCount;\n    let /** @type {?} */ ctx = context;\n    const /** @type {?} */ options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode instanceof StyleAst) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const /** @type {?} */ delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => s.visit(this, ctx));\n\n      // this is here just incase the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ innerTimelines: TimelineBuilder[] = [];\n    let /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach(s => {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      s.visit(this, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(\n        timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if (ast instanceof DynamicTimingAst) {\n      const /** @type {?} */ strValue = context.params ?\n          interpolateParams(ast.value, context.params, context.errors) :\n          ast.value.toString();\n      return resolveTiming(strValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timings = context.currentAnimateTimings = this.visitTiming(ast.timings, context);\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const /** @type {?} */ style = ast.style;\n    if (style instanceof KeyframesAst) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle( /** @type {?} */((style as StyleAst)), context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timeline = context.currentTimeline;\n    const /** @type {?} */ timings = /** @type {?} */(( context.currentAnimateTimings));\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.getCurrentStyleProperties().length) {\n      timeline.forwardFrame();\n    }\n\n    const /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ startTime = ( /** @type {?} */((context.currentTimeline))).duration;\n    const /** @type {?} */ duration = currentAnimateTimings.duration;\n    const /** @type {?} */ innerContext = context.createSubContext();\n    const /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach(step => {\n      const /** @type {?} */ offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ options = /** @type {?} */(( (ast.options || {}) as AnimationQueryOptions));\n    const /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode instanceof StyleAst ||\n                  (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let /** @type {?} */ furthestTime = startTime;\n    const /** @type {?} */ elms = context.invokeQuery(\n        ast.selector, ast.originalSelector, ast.limit, ast.includeSelf,\n        options.optional ? true : false, context.errors);\n\n    context.currentQueryTotal = elms.length;\n    let /** @type {?} */ sameElementTimeline: TimelineBuilder|null = null;\n    elms.forEach((element, i) => {\n\n      context.currentQueryIndex = i;\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      ast.animation.visit(this, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ parentContext = /** @type {?} */(( context.parentContext));\n    const /** @type {?} */ tl = context.currentTimeline;\n    const /** @type {?} */ timings = ast.timings;\n    const /** @type {?} */ duration = Math.abs(timings.duration);\n    const /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n    let /** @type {?} */ delay = duration * context.currentQueryIndex;\n\n    let /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const /** @type {?} */ startingTime = timeline.currentTime;\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n        (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number; value: string | number;\n};\n\nconst /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */(( <Ast>{}));\nexport class AnimationTimelineContext {\npublic parentContext: AnimationTimelineContext|null = null;\npublic currentTimeline: TimelineBuilder;\npublic currentAnimateTimings: AnimateTimings|null = null;\npublic previousNode: Ast = DEFAULT_NOOP_PREVIOUS_NODE;\npublic subContextCount = 0;\npublic options: AnimationOptions = {};\npublic currentQueryIndex: number = 0;\npublic currentQueryTotal: number = 0;\npublic currentStaggerTime: number = 0;\n/**\n * @param {?} _driver\n * @param {?} element\n * @param {?} subInstructions\n * @param {?} errors\n * @param {?} timelines\n * @param {?=} initialTimeline\n */\nconstructor(\nprivate _driver: AnimationDriver,\npublic element: any,\npublic subInstructions: ElementInstructionMap,\npublic errors: any[],\npublic timelines: TimelineBuilder[], initialTimeline?: TimelineBuilder) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(element, 0);\n    timelines.push(this.currentTimeline);\n  }\n/**\n * @return {?}\n */\nget params() { return this.options.params; }\n/**\n * @param {?} options\n * @param {?=} skipIfExists\n * @return {?}\n */\nupdateOptions(options: AnimationOptions|null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const /** @type {?} */ newOptions = /** @type {?} */(( options as any));\n    let /** @type {?} */ optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      ( /** @type {?} */((optionsToUpdate as any))).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const /** @type {?} */ newParams = newOptions.params;\n    if (newParams) {\n      let /** @type {?} */ paramsToUpdate: {[name: string]: any} = /** @type {?} */(( optionsToUpdate.params));\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nprivate _copyOptions() {\n    const /** @type {?} */ options: AnimationOptions = {};\n    if (this.options) {\n      const /** @type {?} */ oldParams = this.options.params;\n      if (oldParams) {\n        const /** @type {?} */ params: {[name: string]: any} = options['params'] = {};\n        Object.keys(this.options.params).forEach(name => { params[name] = oldParams[name]; });\n      }\n    }\n    return options;\n  }\n/**\n * @param {?=} options\n * @param {?=} element\n * @param {?=} newTime\n * @return {?}\n */\ncreateSubContext(options: AnimationOptions|null = null, element?: any, newTime?: number):\n      AnimationTimelineContext {\n    const /** @type {?} */ target = element || this.element;\n    const /** @type {?} */ context = new AnimationTimelineContext(\n        this._driver, target, this.subInstructions, this.errors, this.timelines,\n        this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n/**\n * @param {?=} newTime\n * @return {?}\n */\ntransformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n/**\n * @param {?} instruction\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nappendInstructionToTimeline(\n      instruction: AnimationTimelineInstruction, duration: number|null,\n      delay: number|null): AnimateTimings {\n    const /** @type {?} */ updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const /** @type {?} */ builder = new SubTimelineBuilder(\n        instruction.element, instruction.keyframes, instruction.preStyleProps,\n        instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nincrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n/**\n * @param {?} selector\n * @param {?} originalSelector\n * @param {?} limit\n * @param {?} includeSelf\n * @param {?} optional\n * @param {?} errors\n * @return {?}\n */\ninvokeQuery(\n      selector: string, originalSelector: string, limit: number, includeSelf: boolean,\n      optional: boolean, errors: any[]): any[] {\n    let /** @type {?} */ results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {  // if :self is only used then the selector is empty\n      const /** @type {?} */ multi = limit != 1;\n      results.push(...this._driver.query(this.element, selector, multi));\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(\n          `\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n    }\n    return results;\n  }\n}\n\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTimelineContext.prototype.parentContext;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentTimeline;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationTimelineContext.prototype.previousNode;\n/** @type {?} */\nAnimationTimelineContext.prototype.subContextCount;\n/** @type {?} */\nAnimationTimelineContext.prototype.options;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryIndex;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryTotal;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentStaggerTime;\n/** @type {?} */\nAnimationTimelineContext.prototype._driver;\n/** @type {?} */\nAnimationTimelineContext.prototype.element;\n/** @type {?} */\nAnimationTimelineContext.prototype.subInstructions;\n/** @type {?} */\nAnimationTimelineContext.prototype.errors;\n/** @type {?} */\nAnimationTimelineContext.prototype.timelines;\n}\n\nexport class TimelineBuilder {\npublic duration: number = 0;\npublic easing: string|null;\nprivate _previousKeyframe: ɵStyleData = {};\nprivate _currentKeyframe: ɵStyleData = {};\nprivate _keyframes = new Map<number, ɵStyleData>();\nprivate _styleSummary: {[prop: string]: StyleAtTime} = {};\nprivate _localTimelineStyles: ɵStyleData;\nprivate _globalTimelineStyles: ɵStyleData;\nprivate _pendingStyles: ɵStyleData = {};\nprivate _backFill: ɵStyleData = {};\nprivate _currentEmptyStepKeyframe: ɵStyleData|null = null;\n/**\n * @param {?} element\n * @param {?} startTime\n * @param {?=} _elementTimelineStylesLookup\n */\nconstructor(\npublic element: any,\npublic startTime: number,\nprivate _elementTimelineStylesLookup?: Map<any, ɵStyleData>) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleData>();\n    }\n\n    this._localTimelineStyles = Object.create(this._backFill, {});\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element) !;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.getCurrentStyleProperties().length > 0;\n      default:\n        return true;\n    }\n  }\n/**\n * @return {?}\n */\ngetCurrentStyleProperties(): string[] { return Object.keys(this._currentKeyframe); }\n/**\n * @return {?}\n */\nget currentTime() { return this.startTime + this.duration; }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n/**\n * @param {?} element\n * @param {?=} currentTime\n * @return {?}\n */\nfork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n        element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n/**\n * @return {?}\n */\nprivate _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = /** @type {?} */(( this._keyframes.get(this.duration)));\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = Object.create(this._backFill, {});\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n/**\n * @return {?}\n */\nforwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nforwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} prop\n * @param {?} value\n * @return {?}\n */\nprivate _updateStyle(prop: string, value: string|number) {\n    this._localTimelineStyles[prop] = value;\n    this._globalTimelineStyles[prop] = value;\n    this._styleSummary[prop] = {time: this.currentTime, value};\n  }\n/**\n * @return {?}\n */\nallowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n/**\n * @param {?} easing\n * @return {?}\n */\napplyEmptyStep(easing: string|null) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    Object.keys(this._globalTimelineStyles).forEach(prop => {\n      this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n      this._currentKeyframe[prop] = AUTO_STYLE;\n    });\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n/**\n * @param {?} input\n * @param {?} easing\n * @param {?} errors\n * @param {?=} options\n * @return {?}\n */\nsetStyles(\n      input: (ɵStyleData|string)[], easing: string|null, errors: any[],\n      options?: AnimationOptions) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    const /** @type {?} */ params = (options && options.params) || {};\n    const /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n      this._pendingStyles[prop] = val;\n      if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n        this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n            this._globalTimelineStyles[prop] :\n            AUTO_STYLE;\n      }\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\napplyStylesToKeyframe() {\n    const /** @type {?} */ styles = this._pendingStyles;\n    const /** @type {?} */ props = Object.keys(styles);\n    if (props.length == 0) return;\n\n    this._pendingStyles = {};\n\n    props.forEach(prop => {\n      const /** @type {?} */ val = styles[prop];\n      this._currentKeyframe[prop] = val;\n    });\n\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      if (!this._currentKeyframe.hasOwnProperty(prop)) {\n        this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n      }\n    });\n  }\n/**\n * @return {?}\n */\nsnapshotCurrentStyles() {\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      const /** @type {?} */ val = this._localTimelineStyles[prop];\n      this._pendingStyles[prop] = val;\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\ngetFinalKeyframe() { return this._keyframes.get(this.duration); }\n/**\n * @return {?}\n */\nget properties() {\n    const /** @type {?} */ properties: string[] = [];\n    for (let /** @type {?} */ prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n/**\n * @param {?} timeline\n * @return {?}\n */\nmergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    Object.keys(timeline._styleSummary).forEach(prop => {\n      const /** @type {?} */ details0 = this._styleSummary[prop];\n      const /** @type {?} */ details1 = timeline._styleSummary[prop];\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const /** @type {?} */ preStyleProps = new Set<string>();\n    const /** @type {?} */ postStyleProps = new Set<string>();\n    const /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let /** @type {?} */ finalKeyframes: ɵStyleData[] = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n      Object.keys(finalKeyframe).forEach(prop => {\n        const /** @type {?} */ value = finalKeyframe[prop];\n        if (value == PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value == AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe['offset'] = time / this.duration;\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const /** @type {?} */ preProps: string[] = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const /** @type {?} */ postProps: string[] = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const /** @type {?} */ kf0 = finalKeyframes[0];\n      const /** @type {?} */ kf1 = copyObj(kf0);\n      kf0['offset'] = 0;\n      kf1['offset'] = 1;\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n        this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime,\n        this.easing, false);\n  }\n}\n\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineBuilder.prototype.duration;\n/** @type {?} */\nTimelineBuilder.prototype.easing;\n/** @type {?} */\nTimelineBuilder.prototype._previousKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._currentKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._keyframes;\n/** @type {?} */\nTimelineBuilder.prototype._styleSummary;\n/** @type {?} */\nTimelineBuilder.prototype._localTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._globalTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._pendingStyles;\n/** @type {?} */\nTimelineBuilder.prototype._backFill;\n/** @type {?} */\nTimelineBuilder.prototype._currentEmptyStepKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype.element;\n/** @type {?} */\nTimelineBuilder.prototype.startTime;\n/** @type {?} */\nTimelineBuilder.prototype._elementTimelineStylesLookup;\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\npublic timings: AnimateTimings;\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} timings\n * @param {?=} _stretchStartingKeyframe\n */\nconstructor(\npublic element: any,\npublic keyframes: ɵStyleData[],\npublic preStyleProps: string[],\npublic postStyleProps: string[], timings: AnimateTimings,\nprivate _stretchStartingKeyframe: boolean = false) {\n    super(element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean { return this.keyframes.length > 1; }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    let /** @type {?} */ keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const /** @type {?} */ newKeyframes: ɵStyleData[] = [];\n      const /** @type {?} */ totalTime = duration + delay;\n      const /** @type {?} */ startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n      newFirstKeyframe['offset'] = 0;\n      newKeyframes.push(newFirstKeyframe);\n\n      const /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n      oldFirstKeyframe['offset'] = roundOffset(startingGap);\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still renderered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const /** @type {?} */ limit = keyframes.length - 1;\n      for (let /** @type {?} */ i = 1; i <= limit; i++) {\n        let /** @type {?} */ kf = copyStyles(keyframes[i], false);\n        const /** @type {?} */ oldOffset = /** @type {?} */(( kf['offset'] as number));\n        const /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n        kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n        this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing,\n        true);\n  }\n}\n\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nSubTimelineBuilder.prototype.timings;\n/** @type {?} */\nSubTimelineBuilder.prototype.element;\n/** @type {?} */\nSubTimelineBuilder.prototype.keyframes;\n/** @type {?} */\nSubTimelineBuilder.prototype.preStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype.postStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input: (ɵStyleData | string)[], allStyles: ɵStyleData) {\n  const /** @type {?} */ styles: ɵStyleData = {};\n  let /** @type {?} */ allProperties: string[];\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || Object.keys(allStyles);\n      allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n    } else {\n      copyStyles( /** @type {?} */((token as ɵStyleData)), false, styles);\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nexport class ElementInstructionMap {\nprivate _map = new Map<any, AnimationTimelineInstruction[]>();\n/**\n * @param {?} element\n * @return {?}\n */\nconsume(element: any): AnimationTimelineInstruction[] {\n    let /** @type {?} */ instructions = this._map.get(element);\n    if (instructions) {\n      this._map.delete(element);\n    } else {\n      instructions = [];\n    }\n    return instructions;\n  }\n/**\n * @param {?} element\n * @param {?} instructions\n * @return {?}\n */\nappend(element: any, instructions: AnimationTimelineInstruction[]) {\n    let /** @type {?} */ existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nhas(element: any): boolean { return this._map.has(element); }\n/**\n * @return {?}\n */\nclear() { this._map.clear(); }\n}\n\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n/** @type {?} */\nElementInstructionMap.prototype._map;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: ɵStyleData[];\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string|null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(\n    element: any, keyframes: ɵStyleData[], preStyleProps: string[], postStyleProps: string[],\n    duration: number, delay: number, easing: string | null = null,\n    subTimeline: boolean = false): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay, easing, subTimeline\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, style, ɵStyleData} from '@angular/animations';\n\nimport {getOrSetAsInMap} from '../render/shared';\nimport {ENTER_SELECTOR, LEAVE_SELECTOR, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, copyObj, normalizeAnimationEntry, resolveTiming, validateStyleParams} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationDslVisitor, visitAnimationNode} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst /** @type {?} */ SELF_TOKEN = ':self';\nconst /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(\n    metadata: AnimationMetadata | AnimationMetadata[], errors: any[]): Ast {\n  return new AnimationAstBuilderVisitor().build(metadata, errors);\n}\n\nconst /** @type {?} */ LEAVE_TOKEN = ':leave';\nconst /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\nconst /** @type {?} */ ENTER_TOKEN = ':enter';\nconst /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst /** @type {?} */ ROOT_SELECTOR = '';\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nbuild(metadata: AnimationMetadata|AnimationMetadata[], errors: any[]): Ast {\n    const /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    return /** @type {?} */(( visitAnimationNode(this, normalizeAnimationEntry(metadata), context) as Ast));\n  }\n/**\n * @param {?} context\n * @return {?}\n */\nprivate _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = {};\n    context.collectedStyles[ROOT_SELECTOR] = {};\n    context.currentTime = 0;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(metadata: AnimationTriggerMetadata, context: AnimationAstBuilderContext):\n      TriggerAst {\n    let /** @type {?} */ queryCount = context.queryCount = 0;\n    let /** @type {?} */ depCount = context.depCount = 0;\n    const /** @type {?} */ states: StateAst[] = [];\n    const /** @type {?} */ transitions: TransitionAst[] = [];\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const /** @type {?} */ stateDef = /** @type {?} */(( def as AnimationStateMetadata));\n        const /** @type {?} */ name = stateDef.name;\n        name.split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const /** @type {?} */ transition = this.visitTransition( /** @type {?} */((def as AnimationTransitionMetadata)), context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(\n            'only state() and transition() definitions can sit inside of a trigger()');\n      }\n    });\n    const /** @type {?} */ ast = new TriggerAst(metadata.name, states, transitions);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = queryCount;\n    ast.depCount = depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    return new StateAst(metadata.name, this.visitStyle(metadata.styles, context));\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTransition(metadata: AnimationTransitionMetadata, context: AnimationAstBuilderContext):\n      TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n    const /** @type {?} */ ast = new TransitionAst(matchers, entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = context.queryCount;\n    ast.depCount = context.depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitSequence(metadata: AnimationSequenceMetadata, context: AnimationAstBuilderContext):\n      SequenceAst {\n    const /** @type {?} */ ast = new SequenceAst(metadata.steps.map(s => visitAnimationNode(this, s, context)));\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const /** @type {?} */ currentTime = context.currentTime;\n    let /** @type {?} */ furthestTime = 0;\n    const /** @type {?} */ steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const /** @type {?} */ innerAst = visitAnimationNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    const /** @type {?} */ ast = new GroupAst(steps);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(metadata: AnimationAnimateMetadata, context: AnimationAstBuilderContext):\n      AnimateAst {\n    const /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n\n    let /** @type {?} */ styles: StyleAst|KeyframesAst;\n    let /** @type {?} */ styleMetadata: AnimationMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styles = this.visitKeyframes( /** @type {?} */((styleMetadata as AnimationKeyframesSequenceMetadata)), context);\n    } else {\n      let /** @type {?} */ styleMetadata = /** @type {?} */(( metadata.styles as AnimationStyleMetadata));\n      let /** @type {?} */ isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const /** @type {?} */ newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const /** @type {?} */ styleAst = this.visitStyle(styleMetadata, context);\n      styleAst.isEmptyStep = isEmpty;\n      styles = styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return new AnimateAst(timingAst, styles);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nprivate _makeStyleAst(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext):\n      StyleAst {\n    const /** @type {?} */ styles: (ɵStyleData | string)[] = [];\n    if (Array.isArray(metadata.styles)) {\n      ( /** @type {?} */((metadata.styles as(ɵStyleData | string)[]))).forEach(styleTuple => {\n        if (typeof styleTuple == 'string') {\n          if (styleTuple == AUTO_STYLE) {\n            styles.push( /** @type {?} */((styleTuple as string)));\n          } else {\n            context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n          }\n        } else {\n          styles.push( /** @type {?} */((styleTuple as ɵStyleData)));\n        }\n      })\n    } else {\n      styles.push(metadata.styles);\n    }\n\n    let /** @type {?} */ collectedEasing: string|null = null;\n    styles.forEach(styleData => {\n      if (isObject(styleData)) {\n        const /** @type {?} */ styleMap = /** @type {?} */(( styleData as ɵStyleData));\n        const /** @type {?} */ easing = styleMap['easing'];\n        if (easing) {\n          collectedEasing = /** @type {?} */(( easing as string));\n          delete styleMap['easing'];\n        }\n      }\n    });\n    return new StyleAst(styles, collectedEasing, metadata.offset);\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nprivate _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const /** @type {?} */ timings = context.currentAnimateTimings;\n    let /** @type {?} */ endTime = context.currentTime;\n    let /** @type {?} */ startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple == 'string') return;\n\n      Object.keys(tuple).forEach(prop => {\n        const /** @type {?} */ collectedStyles = context.collectedStyles[ /** @type {?} */((context.currentQuerySelector))];\n        const /** @type {?} */ collectedEntry = collectedStyles[prop];\n        let /** @type {?} */ updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime &&\n              endTime <= collectedEntry.endTime) {\n            context.errors.push(\n                `The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles[prop] = {startTime, endTime};\n        }\n\n        if (context.options) {\n          validateStyleParams(tuple[prop], context.options, context.errors);\n        }\n      });\n    });\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(metadata: AnimationKeyframesSequenceMetadata, context: AnimationAstBuilderContext):\n      KeyframesAst {\n    if (!context.currentAnimateTimings) {\n      context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n      return new KeyframesAst([]);\n    }\n\n    const /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n\n    let /** @type {?} */ totalKeyframesWithOffsets = 0;\n    const /** @type {?} */ offsets: number[] = [];\n    let /** @type {?} */ offsetsOutOfOrder = false;\n    let /** @type {?} */ keyframesOutOfRange = false;\n    let /** @type {?} */ previousOffset: number = 0;\n\n    const /** @type {?} */ keyframes: StyleAst[] = metadata.steps.map(styles => {\n      const /** @type {?} */ style = this._makeStyleAst(styles, context);\n      let /** @type {?} */ offsetVal: number|null =\n          style.offset != null ? style.offset : consumeOffset(style.styles);\n      let /** @type {?} */ offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(`Please ensure that all keyframe offsets are in order`);\n    }\n\n    const /** @type {?} */ length = metadata.steps.length;\n    let /** @type {?} */ generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const /** @type {?} */ limit = length - 1;\n    const /** @type {?} */ currentTime = context.currentTime;\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n      const /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n    });\n\n    return new KeyframesAst(keyframes);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitReference(metadata: AnimationReferenceMetadata, context: AnimationAstBuilderContext):\n      ReferenceAst {\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ ast = new ReferenceAst(entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(metadata: AnimationAnimateChildMetadata, context: AnimationAstBuilderContext):\n      AnimateChildAst {\n    context.depCount++;\n    const /** @type {?} */ ast = new AnimateChildAst();\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(metadata: AnimationAnimateRefMetadata, context: AnimationAstBuilderContext):\n      AnimateRefAst {\n    const /** @type {?} */ animation = this.visitReference(metadata.animation, context);\n    const /** @type {?} */ ast = new AnimateRefAst(animation);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const /** @type {?} */ parentSelector = /** @type {?} */(( context.currentQuerySelector));\n    const /** @type {?} */ options = /** @type {?} */(( (metadata.options || {}) as AnimationQueryOptions));\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector =\n        parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n    getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    const /** @type {?} */ ast = new QueryAst(selector, options.limit || 0, !!options.optional, includeSelf, entry);\n    ast.originalSelector = metadata.selector;\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStagger(metadata: AnimationStaggerMetadata, context: AnimationAstBuilderContext):\n      StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(`stagger() can only be used inside of query()`);\n    }\n    const /** @type {?} */ timings = metadata.timings === 'full' ?\n        {duration: 0, delay: 0, easing: 'full'} :\n        resolveTiming(metadata.timings, context.errors, true);\n    const /** @type {?} */ animation =\n        visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    return new StaggerAst(timings, animation);\n  }\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  selector = selector.replace(ENTER_TOKEN_REGEX, ENTER_SELECTOR)\n                 .replace(LEAVE_TOKEN_REGEX, LEAVE_SELECTOR)\n                 .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n                 .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n                 .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any}|null {\n  return obj ? copyObj(obj) : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number; endTime: number;\n};\nexport class AnimationAstBuilderContext {\npublic queryCount: number = 0;\npublic depCount: number = 0;\npublic currentTransition: AnimationTransitionMetadata|null = null;\npublic currentQuery: AnimationQueryMetadata|null = null;\npublic currentQuerySelector: string|null = null;\npublic currentAnimateTimings: TimingAst|null = null;\npublic currentTime: number = 0;\npublic collectedStyles: {[selectorName: string]: {[propName: string]: StyleTimeTuple}} = {};\npublic options: AnimationOptions|null = null;\n/**\n * @param {?} errors\n */\nconstructor(public errors: any[]) {}\n}\n\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationAstBuilderContext.prototype.queryCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.depCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTransition;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuery;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuerySelector;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTime;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.collectedStyles;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.options;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.errors;\n}\n\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles: ɵStyleData | string | (ɵStyleData | string)[]): number|null {\n  if (typeof styles == 'string') return null;\n\n  let /** @type {?} */ offset: number|null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n        const /** @type {?} */ obj = /** @type {?} */(( styleTuple as ɵStyleData));\n        offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n        delete obj['offset'];\n      }\n    });\n  } else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n    const /** @type {?} */ obj = /** @type {?} */(( styles as ɵStyleData));\n    offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n    delete obj['offset'];\n  }\n  return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value: any): boolean {\n  return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: any[]) {\n  let /** @type {?} */ timings: AnimateTimings|null = null;\n  if (value.hasOwnProperty('duration')) {\n    timings = /** @type {?} */(( value as AnimateTimings));\n  } else if (typeof value == 'number') {\n    const /** @type {?} */ duration = resolveTiming( /** @type {?} */((value as number)), errors).duration;\n    return new TimingAst( /** @type {?} */((value as number)), 0, '');\n  }\n\n  const /** @type {?} */ strValue = /** @type {?} */(( value as string));\n  const /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    return new DynamicTimingAst(strValue);\n  }\n\n  timings = timings || resolveTiming(strValue, errors);\n  return new TimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = copyObj(options);\n    if (options['params']) {\n      options['params'] = /** @type {?} */(( normalizeParams(options['params'])));\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (fromState: any, toState: any) => boolean;\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(\n    transitionValue: string | TransitionMatcherFn, errors: string[]): TransitionMatcherFn[] {\n  const /** @type {?} */ expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    ( /** @type {?} */((<string>transitionValue)))\n        .split(/\\s*,\\s*/)\n        .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push( /** @type {?} */((<TransitionMatcherFn>transitionValue)));\n  }\n  return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(\n    eventStr: string, expressions: TransitionMatcherFn[], errors: string[]) {\n  if (eventStr[0] == ':') {\n    eventStr = parseAnimationAlias(eventStr, errors);\n  }\n  const /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n    return expressions;\n  }\n\n  const /** @type {?} */ fromState = match[1];\n  const /** @type {?} */ separator = match[2];\n  const /** @type {?} */ toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias: string, errors: string[]): string {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    default:\n      errors.push(`The transition alias value \"${alias}\" is not supported`);\n      return '* => *';\n  }\n}\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  return (fromState: any, toState: any): boolean => {\n    let /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? lhs === 'true' : lhs === 'false';\n    }\n    if (!rhsMatch && typeof toState === 'boolean') {\n      rhsMatch = toState ? rhs === 'true' : rhs === 'false';\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationQueryMetadata, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata} from '@angular/animations';\n\nexport interface AnimationDslVisitor {\n  visitTrigger(ast: AnimationTriggerMetadata, context: any): any;\n  visitState(ast: AnimationStateMetadata, context: any): any;\n  visitTransition(ast: AnimationTransitionMetadata, context: any): any;\n  visitSequence(ast: AnimationSequenceMetadata, context: any): any;\n  visitGroup(ast: AnimationGroupMetadata, context: any): any;\n  visitAnimate(ast: AnimationAnimateMetadata, context: any): any;\n  visitStyle(ast: AnimationStyleMetadata, context: any): any;\n  visitKeyframes(ast: AnimationKeyframesSequenceMetadata, context: any): any;\n  visitReference(ast: AnimationReferenceMetadata, context: any): any;\n  visitAnimateChild(ast: AnimationAnimateChildMetadata, context: any): any;\n  visitAnimateRef(ast: AnimationAnimateRefMetadata, context: any): any;\n  visitQuery(ast: AnimationQueryMetadata, context: any): any;\n  visitStagger(ast: AnimationStaggerMetadata, context: any): any;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitAnimationNode(\n    visitor: AnimationDslVisitor, node: AnimationMetadata, context: any) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger( /** @type {?} */((node as AnimationTriggerMetadata)), context);\n    case AnimationMetadataType.State:\n      return visitor.visitState( /** @type {?} */((node as AnimationStateMetadata)), context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition( /** @type {?} */((node as AnimationTransitionMetadata)), context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence( /** @type {?} */((node as AnimationSequenceMetadata)), context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup( /** @type {?} */((node as AnimationGroupMetadata)), context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate( /** @type {?} */((node as AnimationAnimateMetadata)), context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes( /** @type {?} */((node as AnimationKeyframesSequenceMetadata)), context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle( /** @type {?} */((node as AnimationStyleMetadata)), context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference( /** @type {?} */((node as AnimationReferenceMetadata)), context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild( /** @type {?} */((node as AnimationAnimateChildMetadata)), context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef( /** @type {?} */((node as AnimationAnimateRefMetadata)), context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery( /** @type {?} */((node as AnimationQueryMetadata)), context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger( /** @type {?} */((node as AnimationStaggerMetadata)), context);\n    default:\n      throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nconst /** @type {?} */ EMPTY_ANIMATION_OPTIONS: AnimationOptions = {};\n\nexport interface AstVisitor {\n  visitTrigger(ast: TriggerAst, context: any): any;\n  visitState(ast: StateAst, context: any): any;\n  visitTransition(ast: TransitionAst, context: any): any;\n  visitSequence(ast: SequenceAst, context: any): any;\n  visitGroup(ast: GroupAst, context: any): any;\n  visitAnimate(ast: AnimateAst, context: any): any;\n  visitStyle(ast: StyleAst, context: any): any;\n  visitKeyframes(ast: KeyframesAst, context: any): any;\n  visitReference(ast: ReferenceAst, context: any): any;\n  visitAnimateChild(ast: AnimateChildAst, context: any): any;\n  visitAnimateRef(ast: AnimateRefAst, context: any): any;\n  visitQuery(ast: QueryAst, context: any): any;\n  visitStagger(ast: StaggerAst, context: any): any;\n  visitTiming(ast: TimingAst, context: any): any;\n}\n/**\n * @abstract\n */\nexport abstract class Ast {\n/**\n * @abstract\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisit(ast: AstVisitor, context: any) {}\npublic options: AnimationOptions = EMPTY_ANIMATION_OPTIONS;\n/**\n * @return {?}\n */\nget params(): {[name: string]: any}|null { return this.options['params'] || null; }\n}\n\nfunction Ast_tsickle_Closure_declarations() {\n/** @type {?} */\nAst.prototype.options;\n}\n\nexport class TriggerAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} name\n * @param {?} states\n * @param {?} transitions\n */\nconstructor(public name: string,\npublic states: StateAst[],\npublic transitions: TransitionAst[]) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTrigger(this, context); }\n}\n\nfunction TriggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTriggerAst.prototype.queryCount;\n/** @type {?} */\nTriggerAst.prototype.depCount;\n/** @type {?} */\nTriggerAst.prototype.name;\n/** @type {?} */\nTriggerAst.prototype.states;\n/** @type {?} */\nTriggerAst.prototype.transitions;\n}\n\nexport class StateAst extends Ast {\n/**\n * @param {?} name\n * @param {?} style\n */\nconstructor(public name: string,\npublic style: StyleAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitState(this, context); }\n}\n\nfunction StateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStateAst.prototype.name;\n/** @type {?} */\nStateAst.prototype.style;\n}\n\nexport class TransitionAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} matchers\n * @param {?} animation\n */\nconstructor(\npublic matchers: ((fromState: string, toState: string) => boolean)[],\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTransition(this, context); }\n}\n\nfunction TransitionAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAst.prototype.queryCount;\n/** @type {?} */\nTransitionAst.prototype.depCount;\n/** @type {?} */\nTransitionAst.prototype.matchers;\n/** @type {?} */\nTransitionAst.prototype.animation;\n}\n\nexport class SequenceAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitSequence(this, context); }\n}\n\nfunction SequenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nSequenceAst.prototype.steps;\n}\n\nexport class GroupAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitGroup(this, context); }\n}\n\nfunction GroupAst_tsickle_Closure_declarations() {\n/** @type {?} */\nGroupAst.prototype.steps;\n}\n\nexport class AnimateAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} style\n */\nconstructor(public timings: TimingAst,\npublic style: StyleAst|KeyframesAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimate(this, context); }\n}\n\nfunction AnimateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateAst.prototype.timings;\n/** @type {?} */\nAnimateAst.prototype.style;\n}\n\nexport class StyleAst extends Ast {\npublic isEmptyStep = false;\n/**\n * @param {?} styles\n * @param {?} easing\n * @param {?} offset\n */\nconstructor(\npublic styles: (ɵStyleData|string)[],\npublic easing: string|null,\npublic offset: number|null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStyle(this, context); }\n}\n\nfunction StyleAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStyleAst.prototype.isEmptyStep;\n/** @type {?} */\nStyleAst.prototype.styles;\n/** @type {?} */\nStyleAst.prototype.easing;\n/** @type {?} */\nStyleAst.prototype.offset;\n}\n\nexport class KeyframesAst extends Ast {\n/**\n * @param {?} styles\n */\nconstructor(public styles: StyleAst[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitKeyframes(this, context); }\n}\n\nfunction KeyframesAst_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyframesAst.prototype.styles;\n}\n\nexport class ReferenceAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitReference(this, context); }\n}\n\nfunction ReferenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nReferenceAst.prototype.animation;\n}\n\nexport class AnimateChildAst extends Ast {\nconstructor() { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateChild(this, context); }\n}\nexport class AnimateRefAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: ReferenceAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateRef(this, context); }\n}\n\nfunction AnimateRefAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateRefAst.prototype.animation;\n}\n\nexport class QueryAst extends Ast {\npublic originalSelector: string;\n/**\n * @param {?} selector\n * @param {?} limit\n * @param {?} optional\n * @param {?} includeSelf\n * @param {?} animation\n */\nconstructor(\npublic selector: string,\npublic limit: number,\npublic optional: boolean,\npublic includeSelf: boolean,\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitQuery(this, context); }\n}\n\nfunction QueryAst_tsickle_Closure_declarations() {\n/** @type {?} */\nQueryAst.prototype.originalSelector;\n/** @type {?} */\nQueryAst.prototype.selector;\n/** @type {?} */\nQueryAst.prototype.limit;\n/** @type {?} */\nQueryAst.prototype.optional;\n/** @type {?} */\nQueryAst.prototype.includeSelf;\n/** @type {?} */\nQueryAst.prototype.animation;\n}\n\nexport class StaggerAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} animation\n */\nconstructor(public timings: AnimateTimings,\npublic animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStagger(this, context); }\n}\n\nfunction StaggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStaggerAst.prototype.timings;\n/** @type {?} */\nStaggerAst.prototype.animation;\n}\n\nexport class TimingAst extends Ast {\n/**\n * @param {?} duration\n * @param {?=} delay\n * @param {?=} easing\n */\nconstructor(\npublic duration: number,\npublic delay: number = 0,\npublic easing: string|null = null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction TimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTimingAst.prototype.duration;\n/** @type {?} */\nTimingAst.prototype.delay;\n/** @type {?} */\nTimingAst.prototype.easing;\n}\n\nexport class DynamicTimingAst extends TimingAst {\n/**\n * @param {?} value\n */\nconstructor(public value: string) { super(0, 0, ''); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction DynamicTimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nDynamicTimingAst.prototype.value;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationMetadata, AnimationOptions, sequence, ɵStyleData} from '@angular/animations';\n\nexport const /** @type {?} */ ONE_SECOND = 1000;\n\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const /** @type {?} */ matches = ( /** @type {?} */((value as string))).match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:  // ms or something else\n      return value;\n  }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(\n    timings: string | number | AnimateTimings, errors: any[], allowNegativeValues?: boolean) {\n  return timings.hasOwnProperty('duration') ? /** @type {?} */((\n      <AnimateTimings>timings)) :\n      parseTimeExpression( /** @type {?} */((<string|number>timings)), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(\n    exp: string | number, errors: string[], allowNegativeValues?: boolean): AnimateTimings {\n  const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let /** @type {?} */ duration: number;\n  let /** @type {?} */ delay: number = 0;\n  let /** @type {?} */ easing: string = '';\n  if (typeof exp === 'string') {\n    const /** @type {?} */ matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(`The provided timing value \"${exp}\" is invalid.`);\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const /** @type {?} */ delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n    }\n\n    const /** @type {?} */ easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = /** @type {?} */(( <number>exp));\n  }\n\n  if (!allowNegativeValues) {\n    let /** @type {?} */ containsErrors = false;\n    let /** @type {?} */ startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(`Duration values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(`Delay values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n    }\n  }\n\n  return {duration, delay, easing};\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(\n    obj: {[key: string]: any}, destination: {[key: string]: any} = {}): {[key: string]: any} {\n  Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n  return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles: ɵStyleData | ɵStyleData[]): ɵStyleData {\n  const /** @type {?} */ normalizedStyles: ɵStyleData = {};\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, false, normalizedStyles));\n  } else {\n    copyStyles(styles, false, normalizedStyles);\n  }\n  return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(\n    styles: ɵStyleData, readPrototype: boolean, destination: ɵStyleData = {}): ɵStyleData {\n  if (readPrototype) {\n    // we make use of a for-in loop so that the\n    // prototypically inherited properties are\n    // revealed from the backFill map\n    for (let /** @type {?} */ prop in styles) {\n      destination[prop] = styles[prop];\n    }\n  } else {\n    copyObj(styles, destination);\n  }\n  return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = styles[prop];\n    });\n  }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    });\n  }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps: AnimationMetadata | AnimationMetadata[]):\n    AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return /** @type {?} */(( steps as AnimationMetadata));\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(\n    value: string | number, options: AnimationOptions, errors: any[]) {\n  const /** @type {?} */ params = options.params || {};\n  if (typeof value !== 'string') return;\n\n  const /** @type {?} */ matches = value.toString().match(PARAM_REGEX);\n  if (matches) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(\n            `Unable to resolve the local animation param ${varName} in the given list of values`);\n      }\n    });\n  }\n}\n\nconst /** @type {?} */ PARAM_REGEX = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(\n    value: string | number, params: {[name: string]: any}, errors: any[]): string|number {\n  const /** @type {?} */ original = value.toString();\n  const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n    let /** @type {?} */ localVal = params[varName];\n    // this means that the value was never overidden by the data passed in by the user\n    if (!params.hasOwnProperty(varName)) {\n      errors.push(`Please provide a value for the animation param ${varName}`);\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator: any): any[] {\n  const /** @type {?} */ arr: any[] = [];\n  let /** @type {?} */ item = iterator.next();\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n  return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(\n    source: AnimationOptions, destination: AnimationOptions): AnimationOptions {\n  if (source.params) {\n    const /** @type {?} */ p0 = source.params;\n    if (!destination.params) {\n      destination.params = {};\n    }\n    const /** @type {?} */ p1 = destination.params;\n    Object.keys(p0).forEach(param => {\n      if (!p1.hasOwnProperty(param)) {\n        p1[param] = p0[param];\n      }\n    });\n  }\n  return destination;\n}\n\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\n\nimport {containsElement, invokeQuery, matchesElement} from './shared';\n\n\n/**\n * @experimental\n */\nexport class NoopAnimationDriver implements AnimationDriver {\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): AnimationPlayer {\n    return new NoopAnimationPlayer();\n  }\n}\n\n/**\n * @experimental\n */\nexport abstract class AnimationDriver {\n  static NOOP: AnimationDriver = new NoopAnimationDriver();\n\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing?: string|null, previousPlayers?: any[]): any;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n"], "names": ["PRE_STYLE", "style"], "mappings": ";;;;;;;;;;;;;GuBgBA;AACA,6BAAuB,OAAvB;IACA,MAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;QACA,KAAA,CAAA;YACA,MAAA,CAAA,IAAA,mBAAA,EAAA,CAAA;QACA,KAAA,CAAA;YAEA,MAAA,CAAA,OAAA,CAAA,CACI,CADJ,CAAA;QAIA;YACA,MAAA,CAAA,IAAA,qBAAA,CAAA,OAAA,CAAA,CAAA;IACE,CAAF;AACA,CAAA;AACA,4BAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA;IAAA,0BAAA,EAAA,cAAA;IAAA,2BAAA,EAAA,eAAA;IACA,IAAA,MAAA,GAAA,EAAA,CAAA;IACA,IAAA,mBAAyB,GAAzB,EAAA,CAA+B;IAC/B,IAAI,cAAJ,GAAA,CAAA,CAAA,CAAA;IACA,IAAI,gBAAgB,GAApB,IAA2B,CAAC;IAC5B,SAAA,CAAA,OAAA,CAAA,UAAA,EAAA;QACA,IAAA,MAAA,GAAA,EAAA,CAAA,QAA4B,CAA5B,CAA8B;QAC9B,IAAA,YAAA,GAAA,MAA6BA,IAA7B,cAAA,CAAA;QACA,IAAA,kBAAA,GAAA,CAAA,YAAwC,IAAxC,gBAAA,CAAA,IAAA,EAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;YAAA,IAAA,cAAA,GAAA,IAAA,CAAA;YACA,IAAQ,eAAe,GAAG,EAA1B,CAAA,IAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,eAAA,IAAA,UAAA,CAAA,CAAA,CAAA;gBAAA,eAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;YACA,CAAA;YACA,IAAQ,CAAR,EAAA,CAAA,CAAA,eAAA,IAAA,UAAA,CAAA,CAAA,CAAA;gBACA,eAAA,GAAA,UAAA,CAAA,IAAA,CAAA,CAAA;YACM,CAAN;YACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,IAAA,QAAA,CAAA,CAAA,CAAA;gBACA,cAAA,GAAA,UAAA,CAAA,qBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;gBACA,eAAyB,GAAzB,UAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,cAAA,EAAA,EAAA,CAAA,IAAA,CAAA,EAAA,MAAA,CAAA,CAAA;YACA,CAAA;YACA,kBAAA,CAAA,cAAyC,CAAC,GAA1C,eAAA,CAAA;QACI,CAAJ,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;YACA,mBAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,CAAA;QACI,CAAJ;QACI,gBACI,GADR,kBAAA,CAAA;QAEA,cAAA,GAAA,MAAA,CAAA;IAEE,CAAF,CAAA,CAAA;IACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;QAEA,IAAA,UACI,GADJ,OAAA,CAAA;QAGA,MAAA,IAAA,KAAA,CAAA,mDAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;IACA,CAAA;IACA,MAAA,CAAA,mBAAA,CAAA;AACA,CAAA;AACA,wBAAA,MAAA,EAAA,SAAA,EAAA,KAAA,EAAA,QAAA;IACA,MAAA,CAAA,CAAM,SAAN,CAAA,CAAA,CAAA;QACA,KAAA,OAAA;YACA,MAAA,CAAA,OAAA,CAAA,cAAA,OAAA,QAAA,CAAA,KAAA,IAAA,kBAAA,CAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACM,KAAN,CAAY;QAEZ,KAAA,MAAA;YACA,MAAA,CAAA,MAAA,CAAA,cAAA,OAAA,QAAA,CAAA,KAAA,IAAA,kBAAA,CAAA,KAAA,EAAA,MAAA,EAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,KAAA,CAAA;QAEA,KAAA,SAAA;YAEA,MAAgB,CAAhB,SAAA,CAAA,cAAA,OAAA,QAAA,CAAA,KACkB,IADlB,kBAAA,CAAA,KAC8C,EAD9C,SAAA,EAAA,MACiE,CADjE,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YAGA,KAAgB,CAAS;IACvB,CAAF;AACA,CAAA;AACA,4BAAA,CAAA,EAAA,SAAA,EAAA,SAAA;IACE,IAAF,KAAA,GAAA,kBAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,OAAA,EAAA,SAAA,IAAA,CAAA,CAAA,SAAA,EAAA,SAAA,IAAA,SAAA,GAAA,CAAA,CAAA,SAAA,GAAA,SAAA,CAAA,CAAA;IACA,IAAA,IAAA,GAAA,CAAA,CAAA,OAAA,CAAA,CAAA;IAEA,EAAA,CAAA,CAAA,IAAA,IAAA,IAAA,CAAA,CAAA,CAAA;QAGA,KAAU,CAAV,OAAA,CAAmB,GAAnB,IAAA,CAAA;IACA,CAAA;IAEA,MAAA,CAAA,KAAA,CAAA;AAEA,CAAA;AACA,4BAA0B,OAA1B,EAAA,WAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA;IAAA,0BAAA,EAAA,cAAA;IAAA,0BAAA,EAAA,aAAA;IACA,MAAA,CAAA,EAAS,OAAO,SAAA,EAAhB,WAAA,aAAA,EAAA,SAAA,WAAA,EAAA,OAAA,SAAA,EAAA,SAAA,WAAA,EAAA,SAAA,WAAA,EAAA,CAAA;AACA,CAAA;AACA,yBAAmB,GAAnB,EAAwB,GAAG,EAA3B,YAAyC;IACzC,IAAA,KAAA,CAAA;IACA,EAAA,CAAA,CAAA,GAAA,YAAA,GAAA,CAAA,CAAA,CAAA;QAAA,KAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACI,EAAJ,CAAA,CAAA,CAAS,KAAT,CAAe,CAAC,CAAhB;YACQ,GAAR,CAAA,GAAA,CAAgB,GAAhB,EAAA,KAAA,GAAA,YAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;IACA,IAAA,CAAA,CAAA;QACA,KAAA,GAAc,GAAd,CAAA,GAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YAEA,KAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,YAAA,CAAA;QACA,CAAA;IACE,CAAF;IACE,MAAM,CAAR,KAAc,CAAd;AACA,CAAA;AACA,8BAAA,OAAA;IAEI,IAAJ,YAAA,GAAA,OAA6E,CAA7E,OAAA,CAAA,GAAA,CAAA,CAAA;IACI,IAAJ,EAAY,GAAgD,OAA5D,CAAyE,SAAzE,CAA2F,CAA3F,EAAA,YAAA,CAAA,CAAA;IAEI,IAAM,MAAV,GAAA,OAAA,CAAA,MACqC,CADrC,YAAA,GAAA,CAAA,CAAA,CAAA;IAEM,MAAN,CAAa,CAAb,EAAgB,EAAhB,MAAA,CAAA,CAAA;AACA,CAAK;AAEL,IAAI,SAAJ,GAAA,UAAA,IAAA,EAAA,IAAA,IAAA,OAAA,KAAA,EAAA,CAAA,CAAA;;AAEA,IAAE,MAAF,GAAW,UAAX,OAAwB,EAAE,QAA1B,EAAA,KAAA;IAEE,MAAF,CAAA,EAAA,CAAA;AACA,CAAA,CAAA;AACA,EAAA,CAAA,CAAA,OAAA,OAAA,IAAA,WAAA,CAAA,CAAA,CAAA;IAAA,yCAAA;IACA,SAAA,GAAA,UAAA,IAAA,EAAA,IAAA,IAAA,MAA2C,CAA3C,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAI,OAAJ,CAAY,SAAS,CAArB,OAAA,CAAA,CAAA,CAAA;QACA,QAAA,GAAA,UAAA,OAAA,EAAA,QAAA,IAAA,OAAA,OAAA,CAAA,OAAA,CAAA,QAA8D,CAA9D,EAAA,CAAA,CAAA;IACA,CAAA;IACA,IAAA,CAAA,CAAA;QACA,IAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA;QACA,IAAA,IAAA,GAAA,KAAA,CAAA,eAAA,IAAA,KAAA,CAAA,kBAAA,IAAA,KAAA,CAAA,iBAAA;YAEA,KAAA,CAAA,gBAAA,IAA0D,KAA1D,CAAA,qBAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,IAAR,CAAA,CAAA,CAAA;YACQ,QAAR,GAAA,UAAA,OAAA,EAAA,QAAA,IAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;IAAA,MAAA,GAAW,UAAX,OAAA,EAAA,QAAA,EAAA,KAAA;QACA,IAAM,OAAN,GAAA,EAAkB,CAAlB;QACA,EAAA,CAAA,CAAM,KAAN,CAAA,CAAa,CAAb;YACA,OAAA,CAAA,IAAgB,OAAhB,OAAA,EAAoB,OAApB,CAAA,gBAAA,CAAA,QAAA,CAAA,EAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YACA,IAAA,GAAA,GAAA,OAAA,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBACA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;YAEA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA;;ADjKA,CAAA;;;;;;;;;;GAgBA;AACA;;GAGA;AAEA;IAAA;;IACA,4CAAA,GAAA,UAAA,OAAsB,EAAtB,QAAgC;QAChC,MAAA,CAAA,cAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;IAEE,CAAF;IACA,6CAAA,GAAA,UAAA,IAAA,EAAA,IAAA,IAA8B,MAA9B,CAAA,eAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA,mCAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,KAAA;QAEA,MAAA,CAAA,WAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;IAGA,CAAA;IACA,0CAAA,GAAA,UAAA,OAAA,EAAA,IAAA,EAAA,YAAA;QACA,MAAA,CAAA,YAAA,IAAA,EAAA,CAAA;;;;;IAKA,CAAA;;CAlBA;AAmBS;;GDzCT;;;;;;;;;;;;;GAeA;AACA,IADa,UACb,GAAA,IAAA,CAAA;AACA,IADa,eACb,GAAA,UAAA,CAAA;AACA,IADa,eACb,GAAA,UAAA,CAAA;AACA,IADa,cACb,GAAA,WAAA,CAAA;;;;;AAKA,IAAA,qBAAA,GAAA,eAAA,CAAA;AACA;;;GAGA;AAJA,4BAAA,KAAA;IAME,EAAF,CAAA,CAAA,OAAA,KAAA,IAAA,QAJS,CAAsB;QAK/B,MAAA,CAAA,KAAA,CAAA;;;;;;AAMA;;;;GAIA;AACA,+BAAA,KAAA,EAAA,IAAA;IACA,MAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QACA,KAAA,GAAA;;;;;;;;;;;;;;;;;;;;;GAwBA;AACA,6BAAA,GAAA,EAAA,MAAA,EAAA,mBAAA;IACA,IAAA,gBAAA,CAAA,KAAA,GAAA,0EAAA,CAAA;IACA,IAAI,gBAlBgB,CAkBpB,QAAA,CAAA;IACA,IAAA,gBAlBkB,CAkBlB,KAAA,GAAA,CAAA,CAAA;IACA,IAAA,gBAAA,CAAA,MAlBwB,GAAG,EAkB3B,CAAA;IACA,EAAA,CAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,OAAA,GAlBqC,GAkBrC,CAAA,KAAA,CAlBsC,KAkBtC,CAAA,CAAA;QAEI,EAAJ,CAAA,CAAA,OAAA,KAAA,IAAA,CAAA,CAAA,CAlBU;YAmBF,MAAR,CAAA,IAAA,CAAA,iCAAA,GAAA,mBAAA,CAAA,CAAA;YACM,MAAN,CAAA,EAAA,QAAA,EAAA,CAAA,EAAA,KAAA,EAlBc,CAAsB,EAkBpC,MAAA,EAlB0C,EAkB1C,EAAA,CAAA;QACA,CAAK;QAED,QAAJ,GAAA,qBAlBU,CAkBV,UAlB8B,CAAC,OAkB/B,CAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,UAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,UAAN,IAAA,IAlBe,CAAU,CAkBzB,CAAA;YACA,KAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;QAlBA,IAAA,gBAAA,CAAA,SAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QAmBI,EAAJ,CAAA,CAAA,SAAA,CAAA,CAlBwB,CAAA;YAmBxB,MAAA,GAAA,SAAA,CAAA;QAEM,CAlBC;IAmBP,CAAA;IACA,IAAI,CAAJ,CAAA;QACI,QAAJ,GAAA,CAlBS,GAAU,CAAA,CAkBnB;IACA,CAAA;IACA,EAAA,CAAA,CAAA,CAAA,mBAAA,CAlBuB,CAkBvB,CAAA;QACA,IAAA,gBAAA,CAAA,cAAA,GAAA,KAAA,CAAA;QACI,IAAI,gBAAR,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA;QACA,EAAA,CAAA,CAAM,QAAN,GAlBc,CAAI,CAkBlB,CAAA,CAAA;YACM,MAAN,CAAA,IAAA,CAAA,kEAAA,CAAA,CAAA;YACA,cAAA,GAAA,IAAA,CAAA;QACI,CAAJ;QACA,EAAA,CAAA,CAAM,KAAN,GAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,+DAAA,CAAA,CAAA;YACA,cAAA,GAAA,IAAA,CAAA;QAEA,CAAA;QACA,EAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA;;;;;;AAMA;;;;;;;;;AASA,CAAA;AACA;;;GAGA;AAzBA,yBAAA,MAAA;IA0BA,IAAA,gBAAA,CAzBuB,gBAyBvB,GAAA,EAAA,CAAA;IACA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,IAzBS,IAyBT,OAAA,UAAA,CAAA,IAAA,EAAA,KAAA,EAAA,gBAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACA,CAAA;;;;;;;;;;;;AAaA,oBAAA,MAAA,EAAA,aAAA,EAAA,WAAA;IAAA,4BAAA,EAAA,gBAAA;IACA,EAAA,CAAA,CAAA,aAAA,CAAA,CAAiB,CA9BC;QA+BlB,2CAAA;QACA,0CAAA;QA9BA,iCAAA;QA+BI,GAAJ,CAAA,CAAA,IAAA,gBAAA,CAAA,IAAA,IAAA,MAAA,CAAA,CAAA,CAAA;YACA,WAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;;;;;;AAMA;;;;GAIA;AACA,mBAAA,OAAA,EAAA,MAAA;IACA,EAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;;;AAMA;;;;GAIA;AACA,qBAAA,OAAA,EAAA,MAAA;IACA,EAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;;AAKA,CAAA;AAEA;;;GAEA;AACA,iCAAA,KAAA;IACE,EAAF,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,KAAA,CAAA,MAAA,IAAA,CAAA,CAAA;;;;;;;;;;;GAYA;AACA,6BAAA,KAAA,EAAA,OAAA,EAAA,MAAA;IACA,IAAA,gBAAA,CAAA,MAAA,GAAA,OAAA,CAAA,MAAA,IAAA,EAAA,CAAA;IACA,EAAA,CAAA,CAAA,OAAA,KAAA,KAAA,QAAA,CAAA;QACA,MAAA,CAAA;IAEA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;QACA,OAAA,CAAA,OAAA,CAAA,UAAA,OAAA;YACA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,MAAA,CAAA,IAAA,CAAA,iDAAA,OAAA,iCAAA,CAAA,CAAA;;;;;;;;;;;;AAaA,2BAAA,KAAA,EApDiB,MAoDjB,EApDgC,MAoDhC;IACA,IAAA,gBAAA,CAAA,QAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA;IACA,IAAA,gBApDoB,CAoDpB,GAAA,GAAA,QAAA,CAAA,OAAA,CAAA,WAAA,EAAA,UAAA,CAAA,EAAA,OAAA;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QACI,kFAAJ;QACA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;YAGA,QAAA,GAAA,EAAA,CApDgB;QAqDhB,CAAA;;;;;AAKA,CAAA;AACA;;;GAGA;AACA,yBAAA,QAAA;IACA,IAAA,gBAAA,CAAA,GAAA,GAAA,EAAA,CAAA;IACE,IAAF,gBAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,EAAA,CAAA;IACA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;;;;;;AAMA;;;;GAKA;AACA,+BAAA,MAAA,EAAA,WAAA;IACA,EAAA,CAAA,CAAI,MAAJ,CAAA,MAAA,CAAA,CAAA,CAAA;QACI,IAAM,gBAAV,CA3DqB,IA2DrB,GAAA,MAAA,CAAA,MAAA,CAAA;QACA,EAAA,CAAA,CAAM,CAAN,WAAA,CAAA,MAAA,CAAA,CAAA,CA3De;YA4Df,WAAA,CA3DY,MAAQ,GA2DpB,EAAA,CA3DwB;QA4DxB,CAAA;QACA,IAAA,gBAAA,CAAA,IAAA,GAAA,WAAA,CAAA,MAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YACA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA;YAEA,CAAA;;;;;AAKA,IAAA,gBAAA,GAAA,eAAA,CAAA;AACA;;;GDpRA;;;;;;;;;;;;;;;;GA+BA;AAAA,IAAA,uBAAA,GAAA,EAAA,CAAA;AADA;;;;;;;;;;;;OAaA;IACA,mBAAA,GAAA,UAAA,GAAA,EAAA,OAAA,IAAA,CAAA;;QAEA;;;;;;;;;;;IAaA;;;;OApBA;IAHA,oBAAA,IAAA,EAAA,MAAgC,EAAhC,WAAA;QAAA,YACS,iBAAT;QA0BA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;;;;IAMA;;;;;;;;AAqBA;IAAA,oCAAA;IA3CA;;;OAAA;;;;;;;IAkDA;;;;;;;CAPA,CAAA,GAAA;AAwBA;IAAA,yCAAA;IAGA;;;OAhEA;IACA,uBAAA,QAA4B,EAAE,SAA9B;QAAA,YAgEA,iBAAA;;;;;;;IAMA;;;;;;IAkBA,oBAAA;AAAA,CAAA,AA5BA,CAAA,GAAA,GA4BA;AA7EA;IAAA,uCAAA;IAAA;;;;;;;;IAmFA;;;;;;IAYA,kBAAA;AAAA,CAAA,AA/FA,CAAA,GAAA,GA+FA;AAzFA;IAAA,oCAAA;IAAA;;;;;;;;IA+FA;;;;;;;CA/FA,CAAA,GAAA;AA4GA;IAtGgDC,sCAsGhD;IAtGA;;;OAAA;;;;;;;IA6GA;;;;;;;CAPA,CAtGgDA,GAsGhD;;;IAwBA;;;;OApHA;IAJA,kBAAA,MAAA,EAAS,MAAT,EAAuB,MAAvB;QAAA,YA6HA,iBAAA;;;;;;;IAMA;;;;;;IAkBA,eAAA;AAAA,CAAA,OAAA;AAzIA;IAAA,wCAAA;IAAA;;;;;;;;IA+IA;;;;;;IAYA,mBAAA;AAAA,CAAA,AA3JA,CAAA,GAAA,GA2JA;AArJA;IAAA,wCAAA;IAAA;;;;;;;;IA2JA;;;;;;;CA3JA,CAAA,GAAA;;;;;;IA0KA;;;;;;IAMA,sBAAA;AAAA,CAAA,OAAA;AApKA;IAAA,yCAAA;IAAA;;;;;;;;IA0KA;;;;;;;CA1KA,CAAA,GAAA;;;;;;;;;OASA;IACA,kBAAA,QAAa,EAAb,KAAA,EAAA,QAAA,EAAA,WAAA,EAAA,SAAA;QAAA,YAA0C,iBAA1C;QAwLA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;;;IAMA;;;;;;;;AAuBA;IA7MqD,sCA6MrD;IA7MA;;;OAAA;;;;;;;IAoNA;;;;;;;CAPA,CA7MqD,GA6MrD;;;IAuBA;;;;OA7NA;IAkOA,mBAAA,QAAA,EAAA,KAAA,EAAA,MAAA;QAAA,sBAAA,EAAA,SAAA;QAAA,uBAAA,EAAA,aAAA;QAAA;;;;;;IAMA;;;;;;IAgBA,gBAAA;AAAA,CAAA,OAAA;AAhPA;IAAA,4CAAA;IAAA;;;;;;;;IAsPA;;;;;;;CAtPA,CAAA,SAAA;;;;;;;;;;;;;GD9GA;AACA,4BAAA,OAAA,EANsB,IAMtB,EAAA,OAAA;IACA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,aAAA;YACA,MAAA,CAAA,OAAA,CAAA,YAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,WANa;YAOb,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,gBAAA;YACA,MAAA,CAAA,OAAA,CAAA,eAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,cAAA;YACA,MAAA,CAAA,OAAA,CAAA,aAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,WANa;YAOb,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,aAAA;YACA,MAAA,CAAA,OAAA,CAAA,YAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,eAAA;YACA,MAAA,CAAA,OAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,WANa;YAOb,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,eAAA;YACA,MAAA,CAAA,OAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,CAAA,CAAA,kBAAA;YACA,MAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA,CAAA,gBAAA;YACA,MAAA,CAAA,OAAA,CAAA,eAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA,CAAA,WANgB;YAOhB,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,KAAA,EAAA,CAAA,aAAA;;QD9DA;;;;;;;;;;;;AAcA;;;;GAKA;AACA,6BAAA,eAAA,EAAA,MALgD;IAMhD,IAAA,gBAAA,CAAA,WAAA,GAAA,EAAA,CAAA;IALA,EAAA,CAAA,CAAA,OAAA,eAAA,IAAA,QAAA,CAAA,CAAA,CAAA;QAMI,CAAJ,CAAA,eAAA,CAAA,CAAA;aACA,KAAA,CAAA,SAAA,CAAA;aACA,OAAA,CAAA,UAAA,GAAA,IAAA,OAAA,uBAAA,CAAA,GAAA,EAAA,WAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACA,CAAA;;;;;;;;;;;GAYA;AACA,iCAAA,QAVsC,EAAG,WAUzC,EAAA,MAAA;IACA,EAAA,CAAA,CAAI,QAAJ,CAAA,CAAA,CAVY,IAUZ,GAAA,CAAA,CAAA,CAAA;QACI,QAAJ,GAAA,mBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;IACA,CAAG;IAED,IAAF,gBAAA,CAVQ,KAUR,GAAA,QAAA,CAVoB,KAUpB,CAAA,yCAAA,CAAA,CAAA;IACE,EAAF,CAAA,CAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAVoB;QAWpB,MAAA,CAAA,IAAA,CAAA,0CAAA,QAAA,wBAAA,CAAA,CAAA;QACA,MAAA,CAAa,WAAb,CAAA;IAEE,CAAF;IACE,IAAF,gBAAA,CAVsB,SAUtB,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,IAAA,gBAAA,CAAA,SAAA,GAAA,KAAA,CAVsB,CAAoB,CAU1C,CAAA;IACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,WAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,SAAA,EAAA,OAAA,CAAA,CAAA,CAAA;;;;;;AAMA;;;;GAIA;AACA,6BAAA,KAAA,EAAA,MAAA;IACA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;QACA,KAAA,QAAA;YACM,MAAN,CAda,WAcb,CAAA;QACA,KAAA,QAAA;YACA,MAAA,CAAA,WAAA,CAAA;;;;;;AAMA;;;;GAKA;AACA,8BAAA,GAAA,EAlB6B,GAAA;IAmB7B,MAAA,CAAA,UAAA,SAAA,EAAA,OAAA;QACI,IAAI,gBAAR,CAAA,QAAA,GAAA,GAAA,IAAA,SAAA,IAAA,GAAA,IAAA,SAAA,CAAA;QACA,IAAM,gBAAN,CAAA,QAAA,GAAA,GAAA,IAlBmC,SAkBnC,IAAA,GAAA,IAAA,OAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,QAAA,IAAA,OAAA,SAAA,KAAA,SAAA,CAAA,CAAA,CAAA;YAEA,QAAA,GAlBW,SAkBX,GAlBuB,GAkBvB,KAAA,MAAA,GAAA,GAAA,KAAA,OAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,QAAA,IAAA,OAAA,OAAA,KAAA,SAAA,CAAA,CAAA,CAAA;;QDzFA,CAAA;;;;;;;;;;;;;AAwBA;;;;GAMA;AACA,2BAAA,QAAA,EAAA,MAAA;IACA,MAAA,CAAA,IAAA,0BAAA,EAAA,CAAA,KAAA,CA+BkD,QA/BlD,EAAA,MAAA,CAAA,CAAA;AACA,CAAA;AACA,IAAA,WAAA,GAAA,QAAA,CAAA;;;;;;;IAqYA,CAAA;IA/XA;;;;OAIA;;;;;IA6BG,CAAH;IAvBA;;;OAGA;IACA,kEAAA,GAAA,UAAA,OAAA;;;;;;IAMA;;;;OAKA;IACA,iDAAY,GAAZ,UAoBa,QApBb,EAAA,OAAA;QAAA;QACA,IAAM,gBAAN,CAAA,UAAA,GAAA,OAAA,CAAA,UAAA,GAAA,CAAA,CAAA;QACA,IAAM,gBAAN,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,GAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,WAAA,GAAA,EAAA,CAAA;QACA,QAAQ,CAAR,WAAA,CAAA,OAoBoB,CAAS,UAAA,GApB7B;YACA,KAAA,CAAA,6BAAA,CAAA,OAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAU,IAAV,IAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,UAAA,GAAA,CAAA,GAAA,CAAA,CAAA;gBACQ,IAAR,gBAAA,CAAA,IAAA,GAAA,UAAA,CAAA,IAAA,CAAA;gBACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA;oBAoBA,UAAA,CAAsB,IAAtB,GAAA,CAAA,CAAA;oBAnBA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAoB2B,EApB3B,OAAA,CAAA,CAAA,CAAA;gBACQ,CAAR,CAAA,CAAA;gBACQ,UAAQ,CAAhB,IAAA,GAAA,IAAA,CAAA;YACA,CAAA;YACA,IAAA,CAAA,EAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;gBAoBA,IAAA,gBAAA,CAAA,UAAA,GAAA,KAAA,CAAA,eAAA,CAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,OAAA,CAAA,CAAA;gBAnBQ,UAAR,IAoBiB,UApBjB,CAAA,UAAA,CAAA;gBAEA,QAAA,IAAA,UAAA,CAAA,QAAA,CAAA;gBACA,WAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;YACA,CAAA;YAqBQ,IApBR,CAAA,CAAA;gBACA,OAAA,CAAA,MAAA,CAAA,IAoBqB,CAAW,yEApBhC,CAAA,CAAA;YAqBQ,CApBR;QACI,CAAJ,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,GAAA,GAAA,IAAA,UAAA,CAAA,QAAA,CAAA,IAAA,EAAA,MAAA,EAAA,WAAA,CAAA,CAAA;;;;;;IAMA;;;;;;;;IAQA;;;;OAKA;IACA,oDAAA,GAAA,UAAA,QAAA,EAAA,OAYgB;QAXZ,OAAJ,CAAA,UAAA,GAAA,CAAA,CAAA;QACI,OAAJ,CAAA,QAAA,GAAA,CAAA,CAAA;QACI,IAAJ,gBAYmB,CAAQ,KAZ3B,GAY4B,kBAZ5B,CAAA,IAAA,EAAA,uBAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,QAAA,GAAA,mBAAA,CAAA,QAAA,CAAA,IAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,GAAA,GAAA,IAAA,aAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;;;;;;IAMA;;;;OAKA;;;;;;;IAMA;;;;OAIA;IACA,+CAAA,GAAA,UAAA,QAAA,EAAA,OAAA;QAAA;QACA,IAAA,gBAAA,CAIqB,WAJrB,GAAA,OAAA,CAAA,WAAA,CAAA;QACA,IAAM,gBAIgB,CAJtB,YAAA,GAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA;YAEA,OAAA,CAAA,WAAA,GAAA,WAAA,CAAA;YACA,IAAA,gBAIU,CAJV,QAAA,GAAA,kBAAA,CAAA,KAAA,EAAA,IAAA,EAAA,OAAA,CAAA,CAAA;YAKQ,YAJR,GAAA,IAAA,CAAA,GAAA,CAAA,YAI4C,EAJ5C,OAIqD,CAJrD,WAAA,CAAA,CAAA;YACA,MAIW,CAAI,QAJf,CAAA;QACA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAMA;IACA,iDAAA,GAAA,UAAA,QAAS,EAAT,OAAA;QACA,IAAA,gBAAA,CAAA,SAAqB,GAArB,kBAAA,CAAA,QAAA,CAAA,OAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,OAAA,CAAA,qBAAA,GAAA,SAAA,CAAA;QAAA,IAAA,gBAAA,CAAA,MAAA,CAAA;QACA,IAAM,gBAAN,CAAA,aAAA,GAAA,QAAA,CAAA,MAAA,GAAA,QAAA,CAAA,MAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,aAAN,CAAA,IAAA,IAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;YACM,MAAN,GAAA,IAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,CAAA,aAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YACA,IAAQ,gBAAR,CAAA,eAAA,GAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;YACA,IAAA,gBAAsB,CAAC,OAAvB,GAAA,KAAA,CAAA;YACA,EAAA,CAAA,CAAA,CAAS,eAAT,CAAA,CAAA,CAAA;gBACQ,OAAR,GAAA,IAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,YAAA,GAAA,EAAA,CAAA;gBACA,EAAA,CAAa,CAAC,SAAd,CAAA,MAAA,CAAA,CAAA,CAAA;oBACA,YAAA,CAAA,QAAA,CAAA,GAAA,SAAA,CAAA,MAA6B,CAAU;gBACvC,CAAA;gBACA,eAAe,GAAf,KAAA,CAAA,YAAA,CAAA,CAAA;YACA,CAAA;YAEA,OAAA,CAAA,WAAA,IAAA,SAAoC,CAAK,QAAzC,GAAA,SAAA,CAAA,KAAA,CAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;YACA,QAAA,CAAA,WAAA,GAAA,OAAA,CAAA;;;;;;IAMA;;;;OAIA;;;;;;IAFG;;;;OAaH;IACA,kDAAA,GAAA,UAAA,QAAA,EAAA,OAAA;QACA,IAAA,gBAAkB,CARC,MAQnB,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;YARA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,OAAA,CAAA,UAAA,UAAA;gBASA,EAAA,CAAA,CAAA,OAAA,UAAA,IAAA,QAAA,CAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAA,UAAA,IAAA,UAAA,CAAA,CAAA,CAAA;wBACA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA;oBARA,CAAe;oBASL,IAAV,CAAA,CAAgB;wBAChB,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,qCAAA,UAAA,qBAAA,CAAA,CAAA;oBACA,CAAA;gBACA,CAAA;gBARA,IAAA,CAAA,CAAA;oBASA,MAAA,CAAA,IAAA,CARmB,gBAQnB,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA;gBACA,CAAA;YAEA,CAAA,CAAA,CAAA;QACI,CAAJ;QACA,IAAM,CAAN,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MARc,CAQd,CAAA;QACA,CAAA;QACA,IAAA,gBARoB,CAQpB,eAAA,GAAA,IAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,SAAA;YACA,EAAA,CAAA,CAAA,QAAA,CAAA,SAAA,CARiB,CAAS,CAQ1B,CAAA;gBACA,IAAA,gBAAA,CAAA,QAAA,GAAA,CAAA,SAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,QAAA,CAAA,QAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;oBACA,eAAA,GAAA,CAAA,MAAA,CAAA,CAAA;oBACA,OAAA,QAAA,CAAA,QAAA,CAAA,CAAA;;;;;;IANG;;;;OAgBH;IACA,sDAAe,GAAf,UAAA,GAZmB,EAYnB,OAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,qBAAA,CAAA;QAEI,IAZI,gBAYR,CAAA,OAAA,GAAA,OAAA,CAAA,WAAA,CAAA;QACA,IAAM,gBAZW,CAYjB,SAAA,GAAA,OAAA,CAAA,WAAA,CAAA;QAZA,EAAA,CAAA,CAAA,OAAA,IAAoC,SAApC,GAAA,CAAA,CAAA,CAAA,CAAA;YAcM,SAAN,IAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA,CAAA;QACA,CAAA;QACA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YACA,EAAA,CAAA,CAAQ,OAAR,KAAA,IAAA,QAAA,CAAA;gBACQ,MAAR,CAAA;YACA,MAAA,CAAA,IAAA,CAAc,KAAd,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBACA,IAAA,gBAAA,CAAA,eAAA,GAZyC,OAYzC,CAAA,eAAA,CAAA,CAAA,CAAA,OAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAZqB,CAAM,cAY3B,GAAA,eAAA,CAAA,IAAA,CAAA,CAAA;gBAEA,IAAA,gBAAA,CAAA,oBAAA,GAAA,IAAA,CAAA;gBACA,EAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA;;;;wBAKA,oBAAA,GAZqC,KAYrC,CAAA;oBACA,CAAA;oBAEY,yDAAZ;oBACU,6DAAV;oBACA,kDAAA;oBAEY,SAAZ,GAAA,cAAA,CAAA,SAAA,CAAA;gBACA,CAAA;gBACA,EAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA;oBACA,eAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA,WAAA,EAAA,OAAA,SAAA,EAAA,CAAA;gBACA,CAAA;gBACA,EAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAKA;IAEA,mDAAA,GAAA,UAAA,QAAA,EAAA,OAAA;QAAA;QAEI,EAAJ,CAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;YACA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,0DAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,YAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ;QACI,IAAJ,gBAAA,CAAA,mBAAA,GAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,yBAAA,GAhB4C,CAAK,CAgBjD;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,EAhBY,CAgBZ;QACA,IAAM,gBAAN,CAAA,iBAAA,GAAA,KAfgB,CAehB;QAEA,IAAM,gBAAN,CAAA,mBAAA,GAAA,KAAA,CAAA;QACA,IAAM,gBAAN,CAhBuB,cAgBvB,GAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAhBkC,QAgBlC,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,MAAA;YACA,IAAA,gBAhBuB,CAgBvB,QAhBgC,GAgBhC,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,MAAA,IAAA,IAAA,GAAA,QAAA,CAAA,MAAA,GAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;YACM,IAAN,gBAAA,CAAA,MAAA,GAAA,CAAA,CAAA;YACM,EAAN,CAAA,CAAA,SAAA,IAAuB,IAAvB,CAAA,CAAA,CAAA;gBACA,yBAAA,EAAA,CAAA;gBACA,MAAA,GAhBmB,QAAQ,CAgB3B,MAAA,GAAA,SAAA,CAAA;YACM,CAAN;YACA,mBAAA,GAAA,mBAAA,IAAA,MAAA,GAAA,CAAA,IAAA,MAAA,GAAA,CAAA,CAAA;YAEQ,iBAAR,GAAA,iBAAA,IAAA,MAAA,GAAA,cAAA,CAAA;YACM,cAhBS,GAgBf,MAAA,CAAA;YACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YAEQ,MAAR,CAAA,QAAA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;YAEA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,6DAAA,CAAA,CAAA;QACI,CAAJ;QACI,EAAJ,CAAA,CAAQ,iBAAR,CAAA,CAAA,CAAA;YACM,OAAO,CAhBC,MAAC,CAAM,IAAC,CAAI,sDAgB1B,CAAA,CAAA;QACA,CAAK;QAhBL,IAAA,gBAAA,CAAA,MAAA,GAAA,QAAA,CAA+C,KAA/C,CAAA,MAAA,CAAA;QAiBA,IAAM,gBAAN,CAAA,eAAA,GAAA,CAAA,CAhBwB;QAiBxB,EAAA,CAAA,CAAA,yBAAA,GAAA,CAAA,IAAA,yBAAA,GAAA,MAAA,CAAA,CAAA,CAAA;YAEA,OAAA,CAAA,MAAA,CAAA,IAhBU,CAgBV,uEAAA,CAAA,CAAA;QACI,CAAJ;QACI,IAAJ,CAAA,EAAA,CAAA,CAAA,yBAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,eAAA,GAAA,mBAhB4B,GAgB5B,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;QACI,CAAJ;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,MAAA,GAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,WAAA,GAAA,OAAA,CAAA,WAAA,CAhBoC;QAiBpC,IAAA,gBAAA,CAhBe,qBAgBf,GAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,eAAA,GAAA,qBAAA,CAAA,QAAA,CAAA;QACA,SAhBW,CAgBX,OAAA,CAAA,UAAA,EAAA,EAAA,CAAA;YACM,IAAN,gBAAA,CAAA,MAAA,GAAA,eAAA,GAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,GAAA,CAAA,GAAA,CAAA,eAAA,GAAA,CAAA,CAAA,CAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,qBAAA,GAAA,MAAA,GAAA,eAAA,CAAA;YAEA,OAhBe,CAgBf,WAhBe,GAgBf,WAAA,GAAA,qBAAA,CAAA,KAAA,GAAA,qBAAA,CAAA;YACA,qBAAA,CAAA,QAAA,GAAA,qBAAA,CAAA;;;;;;IAMA;;;;OAKA;IACA,mDAAA,GAAA,UAAA,QAAA,EAAA,OAAA;;;;;;IAMA;;;;OAKA;IACA,sDAAA,GAAA,UAAA,QAAA,EAAA,OAAA;;;;;;IAMA;;;;OAKA;IACA,oDAAA,GAAA,UAAA,QAAA,EAAA,OAAA;;;;;;IAMA;;;;OAKA;IACA,+CAhCU,GAgCV,UAhCU,QAAE,EAAS,OAgCrB;QACI,IAAJ,gBAAA,CAAA,cAAA,GAAA,CAAA,CAAA,OAAA,CAAA,oBAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,QAhCkC,CAgClC,OAAA,IAAA,EAAA,CAAA,CAAA,CAhCwD;QAiCpD,OAAJ,CAAA,UAAA,EAAA,CAAA;QAEI,OAAJ,CAAA,YAAA,GAhCU,QAAQ,CAgClB;QACA,IAAA,yCAAA,EAAW,gBAAX,EAAA,mBAhCgC,CAgChC;QACI,OAAO,CAhCC,oBAAC;YAkCb,cAAA,CAAA,MAAA,GAAA,CAhCgB,cAgChB,GAAA,GAAA,GAhCuC,QAAQ,CAgC/C,GAAA,QAhC2D,CAAA;QAiCvD,eAAJ,CAAA,OAhC2B,CAgC3B,eAAA,EAhC6C,OAgC7C,CAAA,oBAAA,EAAA,EAAA,CAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,KAAA,GAAA,kBAhCqD,CAgCrD,IAAA,EAhCsD,uBAgCtD,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,OAAA,CAAA,CAAA;QACI,OAhCO,CAgCX,YAAA,GAAA,IAAA,CAAA;QACA,OAAA,CAAA,oBAAA,GAAA,cAAA,CAAA;;;;;;IAMA;;;;OAKA;IACA,iDAAA,GAAA,UAAA,QApCmB,EAAG,OAAO;QAqC7B,EAAA,CAAA,CAAQ,CAAR,OAAA,CAAA,YAAA,CApCuB,CAAQ,CAoC/B;YACA,OAAA,CAAA,MAAA,CAAA,IApCU,CAoCV,8CAAA,CAAA,CAAA;QAEI,CAAJ;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,QAAA,CAAA,OAAA,KAAA,MAAA;YACA,EAAA,QAAA,EAAA,CAAA,EAAA,KAAA,EAAA,CAAA,EAAA,MAAA,EAAA,MAAA,EAAA;;;;;IAKA,iCAAA;AAAA,CAAA,IAAA;AACA;;;GAGA;AAEA,2BAAA,QAAA;IACA,IAAA,gBAAA,CAAA,YAAA,GAAA,QAvC6C,CAAe,KAuC5D,CAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,KAAA,IAAA,OAAA,KAAA,IAAA,UAAA,EAAA,CAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;IACA,EAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAvC2B;QAwC3B,QAvCmB,GAuCnB,QAAA,CAAA,OAvC4C,CAuC5C,gBAAA,EAvC4C,EAuC5C,CAvCkE,CAuClE;IACA,CAAA;IAEE,QAvCO,GAuCT,QAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,cAAA,CAAA;SACA,OAAA,CAAA,iBAAA,EAAA,cAAA,CAAA;;;;;AAKA,CAAA;AACA;;;;;;AAmBA,CAAA;AA3CA;IATA;;OAEA;IACA,oCAAA,MAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAA,CAAA;QACS,IAAT,CAAA,UAAA,GAAA,CAAA,CAAA;QACS,IAAT,CAAA,QAAA,GAAS,CAAT,CAAA;QACS,IAAT,CAAA,iBAAA,GAA2F,IAA3F,CAAA;QACS,IAAT,CAAA,YAAA,GAA+C,IAA/C,CAAA;QACA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA;QA4CA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA,WAAA,GAAA,CAAA,CAAA;;;;IA2BA,iCAAA;AAAA,CAAA,AAzEA,IAyEA;AACA;;;GAIA;AACA,uBAtEmB,MAsEnB;IACA,EAAA,CAAA,CAAA,OAAA,MAAA,IAtEmB,QAsEnB,CAAA;QACA,MAAA,CAAA,IAAA,CAAA;IACA,IAAA,gBAAA,CAtEiB,MAsEjB,GAAA,IAAA,CAAA;IACA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,UAAA,UAAA;YACA,EAAA,CAAA,CAAA,QAAA,CAAA,UAAA,CAAA,IAAA,UAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,UAAA,CAAA,CAAA;gBAtEA,MAAsB,GAAtB,UAAA,CAAA,gBAAA,CAAA,CAAyC,GAAzC,CAAA,QAAkE,CAAlE,CAAA,CAAA,CAAA;gBAuEA,OAAA,GAAA,CAAA,QAAA,CAAA,CAAA;YACA,CAAA;QACI,CAAJ,CAAA,CAAA;IACA,CAAG;IACD,IAAF,CAAA,EAtES,CAsET,CAAA,QAAA,CAAA,MAAA,CAAA,IAAA,MAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,MAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;;;;;AAOA;;;;GAIA;AA7EA,4BAAA,KAAA,EAAA,MAAA;IA8EA,IAAI,gBAAJ,CAAA,OAAA,GAAA,IA7EU,CA6EV;IACA,EAAA,CAAA,CAAI,KAAJ,CAAA,cA7Ee,CA6Ef,UAAA,CAAA,CAAA,CAAA,CAAA;QACA,OAAA,GAAA,CAAA,KAAA,CAAA,CAAA;IAEE,CAAF;IACE,IAAF,CAAA,EAAA,CAAA,CAAA,OAAA,KAAA,IAAA,QAAA,CAAA,CA7EoB,CA6EpB;QACM,IAAN,gBAAA,CAAA,QAAA,GAAA,aAAA,CAAA,gBAAA,CAAA,CAAA,KAAA,CAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA;QACI,MAAJ,CA7EW,IAAI,SA6Ef,CAAA,gBA7EyC,CAAC,CA6E1C,KAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;IACA,CAAG;IAED,IAAF,gBAAA,CAAA,QAAA,GA7EuB,CAAc,KA6ErC,CAAA,CAAA;IACE,IAAF,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,IA7EwD,CAAQ,UAAA,CA6EhE,IAAA,OAAA,CA7EiE,CAAM,MA6EvE,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IACA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;GAGA;AACA,mCAAA,OAAA;IACA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;QAhFA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,CAAA;QAiFI,EAAJ,CAAA,CAAA,OAAA,CAhFc,QAgFd,CAAA,CAAA,CAAA,CAAA;YACA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,eAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;;QD3iBA,OAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;GAyCA;AACA,mCAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,cAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,WAAA;IAAA,uBAAA,EAAA,aAAA;IAAA,4BAAA,EAAA,mBAAA;IACA,MAAA,CAAA;QACI,IAAJ,EAAA,CAAA,CAAY,uBAAZ;QACI,OAAJ,SAAA;QACI,SAAS,WAAA;QACb,aAAA,eAAA;QACA,cAAA,gBAAA;;QDhDA,KAAA,OAAA;;;;;;;;;;;;;;IAeA,CAAA;IACA;;;OAGA;IAHA,uCAAA,GAAA,UAAA,OAAA;QAIA,IAAM,gBAAN,CAHqB,YAGrB,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,IAAA,CAAA,MAHwB,CAGxB,OAAA,CAAA,CAAA;QACA,CAAA;;;;;;IAMA;;;;OAIA;IACA,sCAAA,GAAA,UAAA,OAAA,EAAA,YAAA;QACA,IAAA,gBAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;OAIA;IACA,mCAAA,GAAA,UAAA,OAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ODUA;IAEA,wDA+Dc,GA/Dd,UAAA,MAAA,EAAA,WAAA,EAAA,GAAA,EAAA,cAAA,EAAA,WAAA,EAAA,OAAA,EAAA,eAAA,EAAA,MAAA;QAAA,uBAAA,EAAA,WAAA;;QAGI,IAAJ,gBAAA,CA+DU,OA/DV,GAAA,IAAA,wBAAA,CAAA,MA+DgD,EA/DhD,WA+DoE,EA/DpE,eA+DqE,EAAiB,MA/DtF,EAAA,EAAA,CAAA,CAAA;QACI,OAAJ,CAAA,OAAA,GAAA,OAAA,CAAA;QACA,OAAA,CAAA,eAAA,CAAA,SAAA,CAAA,CAAA,cAAA,CAAA,EA+DqC,IA/DrC,EA+DsC,OA/DtC,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;QACA,GAAA,CAAM,KA+DK,CA/DX,IAAA,EAAA,OAAA,CAAA,CAAA;QACA,qDAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA,UAAA,QAAA,IAAA,OAAA,QAAA,CAAA,iBAAA,EAAA,EAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,MAAA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YAEA,IAAA,gBA+D8B,CA/D9B,EAAA,GAAA,SAAA,CAAA,SAAA,CA+DwD,MA/DxD,GA+DiE,CA/DjE,CAAA,CAAA;YAC8B,EAA9B,CAAA,CAAA,CAAA,EAAA,CAAA,uBAAA,EAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,SAAA,CAAA,CAAA,WAAA,CAAA,EAAA,IAAA,EAAA,OAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;;;;;;IAMA;;;;;;;;IAQA;;;;;;;;IAQA;;;;;;;;IAQA;;;;OAIA;IACA,2DAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QAEA,IAAA,gBAAA,CAAA,mBAAA,GAAA,OAAA,CAAA,eAAA,CAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;;;YAGA,IAAA,gBAAA,CAAA,SAAA,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,CAAA,qBAAA,CAAA,mBAAA,EAAA,YAAA,EAAA,gBAAA,CAAA,CAAA,YAAA,CAAA,OAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,SAAA,IAAA,OAAA,CAAA,CAAA,CAAA;gBAgDY,uEA/CZ;gBACA,2BAAA;;;;;;IAMA;;;;OAIA;IACA,yDAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QACA,IAAA,gBAAA,CAAA,YAAA,GAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;IAeA,+DAAA,GAAA,UAAA,YAAA,EAAA,OAAA,EAsCqB,OAtCrB;QACI,IAAJ,gBAAA,CAsCU,SAtCV,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACI,IAAI,gBAsCgB,CAtCxB,YAAA,GAAA,SAAA,CAAA;QACA,6DAAA;QACA,uCAAA;QAEA,IAAA,gBAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,IAAA,IAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,IAAA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,IAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAsC2F,IAtC3F,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YACA,YAAA,CAAA,OAAA,CAAA,UAAA,WAAA;gBAEA,IAAA,gBAAA,CAAA,kBAAA,GAAA,OAAA,CAAA,2BAAA,CAAA,WAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA;gBACA,YAAA;;;;;;IAMA;;;;OAIA;;;;;;IAMA;;;;OAKA;IACA,uDAAA,GAAA,UA8BY,GA9BZ,EAAA,OAAA;QAAA;QACA,IAAA,gBAAA,CAAA,eAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QAEA,IAAM,gBAAN,CA8BmB,GA9BnB,GAAA,OAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,GAAA,CAAA,OAAA,CAAA;QACA,EAAA,CAAA,CAAA,OAAA,IAAa,CA8BC,OA9Bd,CAAA,MAAA,IAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YACA,GAAA,GAAA,OAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;YACA,GAAA,CAAA,wBAAA,EAAA,CAAA;YAEA,EAAA,CAAA,CAAQ,OAAR,CAAA,KAAA,IAAA,IAAA,CAAA,CA8Bc,CA9Bd;gBACQ,EAAR,CAAW,CA8BC,GA9BZ,CAAA,YAAA,YAAA,QAAA,CAAA,CAAA,CAAA;oBACA,GAAA,CAAA,eAAA,CAAA,qBAAA,EAAA,CAAA;oBACA,GAAA,CAAA,YAAA,GAAA,0BAAA,CAAA;gBAgCY,CA9BZ;gBA+BU,IAAM,gBA9BhB,CAAA,KAAA,GA8BsC,kBA9BtC,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;;YAGM,CAAN;;;;YAKM,mFAAN;YACA,GAAA,CAAQ,eAAR,CAAA,qBAAA,EAAA,CAAA;YACA,8DAAA;YACA,4DAAA;YAEA,6DAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,eAAA,GAAA,eAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAKA;IACA,oDAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QAAA;QACA,IAAA,gBAAA,CAAA,cAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBAAoB,CA0BC,YA1BrB,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,GAAA,CAAA,OAAA,IAAA,GAAA,CAAA,OAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QAEA,GAAA,CAAM,KAAN,CAAA,OAAA,CA0BoB,UAAA,CA1BpB;YACM,IAAN,gBAAA,CA0B2B,YA1B3B,GA0B4C,OA1B5C,CAAA,gBAAA,CAAA,GA0B0D,CAAe,OA1BzE,CAAA,CAAA;YACM,EAAN,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;gBACA,YAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;;;;YAKA,cAAA,CAAA,IACQ,CADR,YAAA,CAAA,eAAA,CAAA,CAAA;QAEI,CAAJ,CAAA,CAAA;QACI,6DAAJ;QACA,8DAAA;;;;;;IAMA;;;;OAIA;IACA,qDAAA,GAAA,UAAA,GAsBa,EAtBb,OAAA;QACA,EAAA,CAAA,CAAA,GAAA,YAAA,gBAAA,CAAA,CAAA,CAAA;YAsBA,IAAA,gBAAA,CAAA,QAAA,GAAA,OAAA,CAAA,MAAA;gBArBA,iBAAA,CAsBwB,GAtBxB,CAAA,KAsB6B,EAAS,OAAO,CAtB7C,MAAA,EAsBkD,OAtBlD,CAsBwD,MAAY,CAtBpE;gBACA,GAAA,CAAA,KAAA,CAAA,QAAA,EAAA,CAAA;YACA,MAAA,CAAA,aAAA,CAAA,QAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,sDAAA,GAAA,UAAA,GAAc,EAAd,OAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAkBUA,QAAA,GAAQ,OAlBlB,CAAA,eAAA,CAAA;QACI,EAAJ,CAAA,CAAQA,OAAR,CAkBS,KAlBT,CAAA,CAAA,CAAA;YACM,OAAN,CAAA,aAAA,CAAA,OAAA,CAkBiC,KAlBjC,CAAA,CAkBiC;YAjBjC,QAAA,CAAA,qBAAA,EAAA,CAAA;QAkBA,CAAA;QAjBA,IAAA,gBAAA,CAAA,QAAA,GAkBoC,GAlBpC,CAAA,KAkB6C,CAAC;QAjB9C,EAAA,CAAA,CAAM,QAAN,YAAA,YAAA,CAAA,CAkBsBA,CAlBtB;YACM,IAAN,CAAA,cAAA,CAAA,QAAA,EAkBgB,OAlBhB,CAAA,CAAA;QACA,CAAK;QAED,IAAJ,CAAA,CAAA;YACA,OAAA,CAAA,aAAA,CAkB2B,OAlB3B,CAAA,QAAA,CAAA,CAAA;YACA,IAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,QAAA,CAAA,EAAA,OAAA,CAAA,CAAA;;;;;;IAMA;;;;;IAMA,oDAAA,GAAA,UAAA,GAAA,EAcU,OAdV;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;QAEI,iDAAJ;QACI,4EAAJ;QACA,EAAA,CAAA,CAAM,CAAN,OAAc,IAAd,QAAA,CAAA,yBAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,QAAA,CAAA,YAAA,EAAA,CAAA;QAcA,CAAA;QAbA,IAAA,gBAcgB,CAAS,MAdzB,GAAA,CAc8B,OAd9B,IAAA,OAcqD,CAdrD,MAAA,CAc6D,IAd7D,GAc6D,CAAQ,MAdrE,CAcsE;QAbtE,EAAA,CAAA,CAAA,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA;YAEA,QAAA,CAAA,cAc+B,CAd/B,MAAA,CAAA,CAAA;QACA,CAAA;;;;;;IAMA;;;;OAIA;IACA,wDAAA,GAAA,UAAA,GAAA,EAAA,OAUU;QATN,IAAJ,gBAAA,CAU2B,qBAAA,GAV3B,CAAA,CAAA,OAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAUwB,SAVxB,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,qBAAA,CAAA,QAAA,CAAA;QACA,IAAA,gBAAA,CAAA,YAAA,GAAA,OAAA,CAAA,gBAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,aAAA,GAUoC,YAVpC,CAUiD,eAVjD,CAUiE;QATjE,aAAA,CAAA,MAAA,GAAA,qBAAA,CAAA,MAAA,CAAA;QACA,GAUO,CAVP,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;YAIA,aAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,MAU6B,EAV7B,OAAA,CAAA,MAUwE,EAVxE,OAAA,CAAA,OAAA,CAAA,CAAA;;;QAII,qEAAJ;QACI,uDAAJ;QACA,OAAA,CAAA,eAAA,CAAA,4BAAA,CAAA,aAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,oDAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QAAA;QAEI,uEAAJ;QACA,oEAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,OAAA,CAAA,eAAA,CAAA,WAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,GAAA,CAAA,OAAA,IAAA,EAAA,CAM6B,CAA2B,CANxD;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QAEI,EAAJ,CAAA,CAAA,KAAA,IAAA,CAAA,OAMQ,CANR,YAAA,YAMiC,QANjC;YACA,CAAA,SAAA,IAAA,CAAA,IAMU,OAAO,CANjB,eAAA,CAAA,yBAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAO2B,CAP3B,CAO4C;YAH5C,OAAA,CAAA,eAAA,CAMgC,qBANhC,EAAA,CAAA;YACA,OAAA,CAAA,YAAA,GAAA,0BAAA,CAAA;QACI,CAAJ;QAEA,IAAM,gBAAN,CAAA,YAMkC,GANlC,SAAA,CAAA;QACA,IAAA,gBAAA,CAAA,IAMY,GANZ,OAAA,CAAA,WAM2B,CAAQ,GANnC,CAAA,QAAA,EAAA,GAMoD,CANpD,gBAAA,EAAA,GAM0E,CAN1E,KAAA,EAAA,GAAA,CAAA,WAAA,EAAA,OAAA,CAAA,QAAA,GAAA,IAAA,GAAA,KAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;QACA,OAAA,CAAU,iBAAV,GAAA,IAAA,CAAA,MAAA,CAAA;QACA,IAAA,gBAAoB,CAMC,mBAAe,GANpC,IAAA,CAAA;QACA,IAAA,CAAO,OAAP,CAAA,UAAA,OAAA,EAAA,CAAA;YAEM,OAAN,CAAA,iBAAA,GAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAA2B,YAA3B,GAM8B,OAN9B,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;gBAQU,YANV,CAAA,aAAA,CAAA,KAAA,CAAA,CAMgC;;;;YAD1B,CAAN;YAEM,GAAN,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;YACM,+DAAN;YACA,0EAAA;YAEA,wDAAA;YACA,YAAA,CAAA,eAAA,CAAA,qBAAA,EAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,YAAA,CAAA,eAAA,CAAA,WAAA,CAAA;YAEQ,YAAR,GAAA,IAMS,CANT,GAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,OAAA,CAAA,iBAAA,GAM8B,CAN9B,CAAA;QACA,OAAA,CAAA,iBAAA,GAAA,CAAA,CAAA;QAEI,OAAO,CAMC,wBANZ,CAAA,YAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,sDAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QACI,IAAJ,gBAAA,CAAA,aAAA,GAAA,CAE2B,CAF3B,OAEmC,CAFnC,aAAA,CAAA,CAAA,CAEoC;QAAhC,IAAJ,gBAAA,CAAA,EAAA,GAAA,OAAA,CAAA,eAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,OAAA,GAAA,GAAA,CAAA,OAAA,CAAA;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAEgB,OAFhB,GAAA,QAAA,GAAA,CAAA,OAAA,CAAA,iBAAA,GAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,QAAA,GAAA,OAAA,CAAA,iBAAA,CAAA;QACA,IAAM,gBAAN,CAAA,kBAAA,GAAA,OAAA,CAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,OAAA,CAAA,MAAA,CAAA;QACA,MAAA,CAAA,CAAQ,kBAAR,CAAA,CAAA,CAEgB;YADhB,KAAA,SAAA;gBACA,KAAA,GAAA,OAAA,GAAA,KAAA,CAAA;gBAEA,KAAA,CAAA;YACQ,KAEC,MAFT;gBACA,KAEe,GAFf,aAAA,CAAA,kBAAA,CAAA;gBACA,KAAA,CAAA;QAEI,CAAJ;QACI,IAAJ,gBAAA,CAAA,QAAA,GAEsC,OAFtC,CAAA,eAAA,CAAA;QACI,EAAJ,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;;;;QAMI,OAAJ,CAAA,YAAA,GAAA,GAAA,CAAA;QACA,0BAAA;QACA,2DAAA;QACA,kEAAA;QAMA,iEAAA;QACA,aAAA,CAAA,kBAAA;;;;;;;;;;;;;;OAeA;IACA,kCAAA,OAAA,EAAa,OAAb,EAAa,eAAb,EAAA,MAAA,EAAA,SAAA,EAAA,eAAA;QAbS,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QAES,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QACS,IAAT,CAAA,eAA6B,GAA7B,eAAA,CAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAS,CAAT;QACS,IAAT,CAAA,SAAA,GAAqC,SAArC,CAAA;QACS,IAAT,CAAA,aAAA,GAAA,IAAqC,CAAA;QAC5B,IAAT,CAAA,qBAAqC,GAArC,IAAA,CAAA;QACS,IAAT,CAAA,YAAA,GAAA,0BAAA,CAAA;QAaI,IAAI,CAAC,eAAe,GAAG,CAA3B,CAAA;QACI,IAAJ,CAAA,OAAA,GAAmB,EAAnB,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,CAAA,CAAA;;;;QALG,SASH,CAAA,IAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CATsC;;;;;;;;;;IAetC;;;;OAIA;;yBA6BA;QA1BI,EAAJ,CAAA,CAAQ,CAAR,OAAA,CAAA;YACM,MAAN,CAAA;QACA,IAAA,gBAAA,CAAA,UAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QAEI,IAAI,gBAbY,CAapB,eAAA,GAAA,IAAA,CAAA,OAAA,CAAA;QACA,yFAAA;QACA,EAAA,CAAA,CAAA,UAAA,CAAA,QAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YAEA,CAAA,CAAA,eAAA,CAAA,CAbU,CAaV,QAbU,GAAY,kBAAkB,CAaxC,UAAA,CAAA,QAAA,CAAA,CAAA;QACI,CAAJ;QACA,EAAA,CAAA,CAAM,UAAN,CAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YACM,eAAN,CAAA,KAb2B,GAa3B,kBAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,UAAA,CAAA,MAAA,CAAA;QAEA,EAAA,CAAA,CAAM,SAAN,CAAA,CAbc,CAAI;YAclB,IAAQ,gBAAR,CAbc,gBAad,GAAA,CAAA,CAb+B,eAAe,CAAc,MAa5D,CAbmE,CAanE,CAAA;YACA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAwB;gBACxB,gBAAA,GAAA,IAAA,CAAA,OAAA,CAAA,MAAA,GAAA,EAAA,CAAA;YACA,CAAO;YACP,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBACA,EAAA,CAAA,CAAA,CAAA,YAAA,IAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;QAXA,CAAA;IAgBA,CAAA;IACA;;OAEA;IACA,+CAAQ,GAAR;QACA,IAAA,gBAAA,CAAA,OAAA,GAfiC,EAejC,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,WAAA,GAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA;YACA,EAAA,CAfW,CAeX,WAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA;;;;;;;;;;;OAcA;IAEA,mDAAA,GAAA,UAAA,OAAA,EAAA,OAAA,EApB0C,OAoB1C;QAAA,wBAAA,EAAA,cAAA;QACI,IAAJ,gBApB0B,CAoB1B,MApB2B,GAoB3B,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,OAAA,GApBqC,IAoBrC,wBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,IAAA,CAAA,eAAA,EAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,IAAA,CAAA,CAAA,CAAA,CAAA;QACI,OAAO,CApBC,YAoBZ,GAAA,IAAA,CApBgC,YAoBhC,CAAA;QACI,OAAO,CApBC,qBAAqB,GAoBjC,IAAA,CAAA,qBAAA,CAAA;QACI,OAAJ,CAAA,OAAA,GAAA,IApByB,CAAE,YAoB3B,EAAA,CAAA;QACI,OApBO,CAoBX,aAAA,CAAA,OAAA,CAAA,CAAA;QACA,OAAA,CAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,2DAAA,GAAA,UAAA,OAAA;;;;;;;;;;;OAaA;IACA,8DAAA,GAAA,UAAA,WAAA,EAAA,QAAA,EAAA,KAAA;QACI,IAAJ,gBAAA,CA5BU,cAAc,GA4BxB;YAGQ,QAAR,EA5BU,QA4BV,IAAA,IAAA,GAAA,QAAA,GAAA,WAAA,CAAA,QAAA;YACA,KAAA,EAAA,IAAA,CAAA,eAAA,CAAA,WAAA,GAAA,CAAA,KAAA,IAAA,IAAA,GAAA,KAAA,GAAA,CAAA,CAAA,GAAA,WAAA,CAAA,KAAA;YACA,MAAA,EAAA,EAAA;;;;;IAKA,CAAA;IACA;;;;;;IAMA,CAAA;;;;OAIA;IACA,gDAAA,GAAA,UAAA,KAAA;;;;;;;;;;;;;;OAgBA;IACA,8CAAA,GAAA,UAAA,QAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,WAAA,EAAA,QAAA,EAAA,MAAA;QACA,IAAM,gBAAN,CAAA,OAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAM,WAAN,CA1Ce,CAAI,CA0CnB;YACA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;QAEI,CAAJ;QACA,EAAA,CAAA,CAAM,QAAN,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YAEA,IAAA,gBAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,CAAA;YACA,OAAA,CAAA,IAAA,OAAA,OAAA,EAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EAAA,KAAA,CAAA,EAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,QAAA,IAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAAA,IAAA,CAAA,cAAA,gBAAA,mDAAA,gBAAA,yDAAA,CAAA,CAAA;;;;;;;IAgDA;;;;OAxFA;IAEA,yBAAA,OAAA,EAAA,SAAA,EAA6C,4BAA7C;QACU,IAAV,CAAA,OAAA,GAAA,OAAA,CAAA;QACU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAkD;QACxC,IAAV,CAAA,4BAAA,GAAA,4BAAA,CAAA;QAGU,IAAV,CAAA,QAAA,GAAA,CAAA,CAAA;QACU,IAAV,CAAA,iBAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,gBAAA,GAAA,EAAA,CAAA;QAkFI,IAAI,CAAC,UAAT,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,IAAM,CAAN,aAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,cAAA,GAAA,EAAA,CAAA;QAEI,IAAI,CAAC,SAAT,GAAA,EAAA,CAAA;QACI,IAAI,CAAC,yBAAT,GAAqC,IAArC,CAAA;QACI,EAAJ,CAAA,CAAQ,CAAC,IAAI,CAAC,4BAAd,CAAA,CAAA,CAAA;YACM,IAAI,CAAC,4BAA4B,GAAvC,IAAA,GAAA,EAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,oBAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA;QACI,IAAI,CAAC,qBAAT,GAAA,IAAA,CAAA,4BAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;;;;QAIA,IAAA,CAAA,aAAA,EAAA,CAAA;IACA,CAAA;IACA;;OAEA;IACA,2CAAA,GAAA;QACA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA,CAAA;YACA,KAAA,CAAA;gBACA,MAAA,CAAA,KAAA,CAAA;YACA,KAAA,CAAA;;;;QAIA,CAAA;;;;OAIA;;IAKA,sBAAA,wCAAA;;;;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;;;OAAA;;;;;IAKA,uCAAA,GAAA,UAAA,KAAA;QAEI,sEAAJ;QACA,yEAAA;QACA,6EAAA;QACA,kEAAA;QACA,IAAA,gBAAA,CAAA,eAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,IAAA,CAAA,IAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,MAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,QAAA,IAAA,eAAA,CAAA,CAAA,CAAA;YAtFA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA;YAuFM,EAAN,CAAA,CAAU,eAAV,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,qBAAA,EAAA,CAAA;YACA,CAAA;;;;;;IAMA;;;;;;;QApFA,MAAA,CAAA,IAAA,eAAA,CAAA,OAAA,EAAA,WAAA,IAAA,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,4BAAA,CAAA,CAAA;IA6FA,CAAA;IACA;;OAEA;IACA,uCA5FU,GA4FV;QACA,EAAA,CAAA,CAAM,IAAI,CA5FC,gBAAC,CA4FZ,CAAA,CA5F8B;YA6FxB,IAAI,CA5FC,iBA4FX,GA5F+B,IA4F/B,CAAA,gBAAA,CAAA;QACA,CAAK;QACL,IAAA,CAAA,gBAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;;;QAIA,CAAA;IACA,CAAA;IACA;;;;;;IAMA,CAAA;IACA;;;OAGA;;;;;;IA/FG;;;;OAyGH;;;;QAIA,IAAA,CAAA,aAAA,CAvGG,IAuGH,CAvG8B,GAuG9B,EAAA,IAAA,EAvGqC,IAuGrC,CAAA,WAAA,EAAA,KAAA,OAAA,EAAA,CAvG2C;;;;;IA4G3C,iDAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,yBAAA,KAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;IACA;;;;;;;;;;QAUI,sDAAJ;QACA,4DAAA;QACA,yDAAA;QACA,2DAAA;QACI,qEAAJ;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;;;;;;;;;OAeA;IACA,mCAAA,GAAA,UAAA,KAAA,EAAA,MAAA,EAAA,MAAA,EAhHU,OAgHV;QAAA,iBAgBA;QAfI,EAAJ,CAAA,CAAA,MAAA,CAhHY,CAAI,CAgHhB;YACM,IAAN,CAAA,iBAAA,CAhHY,QAgHZ,CAAA,GAAA,MAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,CAAA,IAAA,EAAA,CAAA;QACA,IAAA,gBAhHc,CAAS,MAgHvB,GAAA,aAAA,CAAA,KAAA,EAAA,IAhHqC,CAAqB,qBAgH1D,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAY,MAAZ,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;YACA,IAAA,gBAAA,CAAA,GAAA,GAAA,iBAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA;YACA,KAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA;YACM,EAAN,CAAA,CAAU,CAhHC,KAgHX,CAAA,oBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,qBAAA,CAAA,cAAA,CAAA,IAAA,CAAA;oBACA,KAAA,CAAA,qBAAA,CAAA,IAAA,CAAA;;;;QAIA,CAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAjHA;IAoHA,+CAAA,GAAA;QAAA,iBAiBA;QAfI,IAlHM,gBAkHV,CAAA,MAAA,GAAA,IAAA,CAAA,cAAA,CAAA;QACA,IAAA,gBAAA,CAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,KAlHK,CAkHX,MAAA,IAAA,CAAA,CAAA;YACA,MAAA,CAAA;QAEI,IAAJ,CAAA,cAAA,GAAA,EAAA,CAAA;QACA,KAAA,CAAA,OAlHY,CAAI,UAAA,IAkHhB;YACA,IAAA,gBAAA,CAAA,GAlH8B,GAkH9B,MAAA,CAAA,IAAA,CAAA,CAAA;YACA,KAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA;QACA,CAAK,CAlHC,CAAC;QAmHP,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;QAIA,CAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAEA;IACA,+CAAA,GAAA;QAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;QAIA,CAAA,CAAA,CAAA;;;;OAIA;IACA,0CAAA,GAAA,cAAA,MAxHU,CAwHV,IAAA,CAAA,UAxHoC,CAwHpC,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;IAIA,sBAAI,uCAAJ;QAHA;;WAEA;aACA;YACA,IAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,CAAA;;;;;QAKA,CAAA;;;OAAA;IACA;;;OAGA;IACA,sDAAA,GAAA,UA3Hc,QA2Hd;QAAA,iBAQA;QAPA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,KAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,QAAA,GAAA,QAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;;;;QAIA,CAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAEA;IAEA,wCAAA,GAAA;QAAA;QACI,IAAI,CA7HC,qBA6HT,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,aAAA,GAAA,IA7H4B,GA6H5B,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,cAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IA7HoC,KAAK,CAAC,IA6H1C,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CA7HqBD,cA6HrB,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAuB,QAAvB,EA7HiC,IA6HjC;YACA,IAAA,gBAAA,CAAA,aAAA,GAAA,UAAA,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;YA7HA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,OAAwC,CAAxC,UAAA,IAAA;gBA8HA,IAAA,gBA7H0B,CAAG,KAAK,GA6HlC,aAAA,CAAA,IAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,KAAA,IAAA,UAAA,CAAA,CAAA,CAAA;oBACA,aAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;gBACU,CA7HC;gBA8HH,IAAR,CAAA,EAAA,CAAA,CAAA,KA7HsB,IA6HtB,UAAA,CAAA,CA7HkC,CA6HlC;oBACA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;gBACA,CAAA;YACA,CAAA,CAAA,CAAA;YAEA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;gBACA,aAAA,CAAA,QAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA,QA7HgD,CA6HhD;;YAGQ,cAAR,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,QAAA,GAAA,aAAA,CAAA,IAAA,GAAA,eAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBA7HwB,CA6HxB,SAAA,GAAA,cAAA,CAAA,IAAA,GAAA,eAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,GAAA,EAAA,CAAA;QACA,0FAAA;QACA,EAAA,CAAA,CAAM,OAAN,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,GAAA,GAAA,cAAA,CAAA,CAAA,CAAA,CAAA;YAEA,IAAA,gBAAA,CAAA,GAAA,GACQ,OADR,CAAA,GAAA,CA5Hc,CA4Hd;YAGA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA;YACA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA;YAEA,cAAA,GAAA,CAAA,GA7HC,EA6HD,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;OAzHA;IACA,4BAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,cAAA,EAAA,OAAA,EAAA,wBAAA;QAAA,yCAAA,EAAA,gCAAA;QAAA,YACc,kBAAd,OAAA,EAAA,OAAA,CAAA,KAAA,CAAc;QAuKV,KAAI,CAAC,OAAO,GAAG,OAAnB,CAAA;QACA,KAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;QAIA,KAAA,CAAA,OAAA,GAAA,EAAA,QAvKwC,EAuKxC,OAAA,CAAA,QAAA,EAAA,KAvKgE,EAuKhE,OAAA,CAAA,KAAA,EAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,CAAA;;;;;OAIA;IACA,8CAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAzKQ,SAyKR,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;IACA;;OAEA;IACA,2CAAA,GAAA;QACA,IAAM,gBAAN,CAAA,SAAA,GAAA,IAAA,CAzKY,SAyKZ,CAAA;;QAGA,EAAA,CAAA,CAAM,IAAN,CAAA,wBAAA,IAAA,KAAA,CAzKY,CAyKZ,CAAA;YACM,IAAN,gBAAA,CAAA,YAAA,GAAA,EAAA,CAAA;YACM,IAAN,gBAAA,CAAA,SAAA,GAzKyC,QAyKzC,GAAA,KAAA,CAAA;YAEM,IAAN,gBAAA,CAzKY,WAyKZ,GAAA,KAzK+B,GAyK/B,SAAA,CAAA;YACM,mEAAN;YACM,IAAN,gBAAA,CAAA,gBAAA,GAAA,UAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;eAqBA;YACA,oEAAA;YACA,IAAU,gBAAV,CAAA,KAAA,GAzKmC,SAyKnC,CAAA,MAAA,GAAA,CAAA,CAAA;YACA,GAAA,CAAQ,CAAR,IAAA,gBAzK6B,CAAC,CAyK9B,GAAA,CAAA,EAAA,CAAA,IAAA,KAAA,EAAA,CAAA,EAAA,EAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,EAAA,GAAA,UAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;;gBAGA,IAAA,gBAAA,CAAA,cAAA,GAAA,KAAA,GAAA,SAAA,GAAA,QAAA,CAAA;gBACA,EAAA,CAAA,QAAA,CAAA,GAAA,WAAA,CAAA,cAAA,GAAA,SAAA,CAAA,CAAA;gBACA,YAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;YAEM,CAAN;YACA,yDAAA;YAEA,QAAA,GAAA,SAAA,CAAA;YAGA,KAAA,GAAA,CAAA,CAAA;YACA,MAAA,GAAA,EAAA,CAAA;YAEA,SAAA,GAAA,YAAA,CAAA;;;;;;AAoBA;;;;;;;;;;AASA;;;;GAIA;AACA,uBAAA,KAjMsB,EAiMtB,SAAA;IACA,IAAA,gBAjMoB,CAiMpB,MAjMqB,GAiMrB,EAjM6B,CAiM7B;IACA,IAAA,gBAAA,CAAA,aAAA,CAAA;IAjMA,KAAA,CAAA,OAAA,CAAA,UAAA,KAAA;QAkMA,EAAA,CAAA,CAAM,KAAN,KAAgB,GAAhB,CAAA,CAAA,CAAA;YACA,aAAA,GAAA,aAAA,IAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;YACA,aAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;;QDjjCA,CAAA;;;;;;;;;;;AAwBA;IAJA;;;OAOA;IACA,mBAAA,OAAA,EAAA,KAAwB;QACxB,IAAM,CAAN,OAAA,GAAA,OAAA,CAAA;QACA,IAAA,MAAA,GAAA,EAAA,CAAA;QACI,IAAJ,GAAA,GAAA,iBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;OAkBA;IACA,kCAAA,GAAA,UAAA,OAAA,EAAA,cAAA,EAAA,iBAAA,EAXmB,OAWnB,EAAA,eAV+B;QAY3B,IAAJ,gBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,cAAA,CAAA,GAAA,eAAA,CAAA,cAAA,CAAA,GAAA,CAAA,cAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,IAXY,GAWZ,KAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,eAXiE,CAWjE,iBAAA,CAAA,GAAA,CAAA,iBAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAXuB,EAAa,CAWpC;QACA,eAAA,GAAA,eAAA,IAAA,IAAA,qBAAA,EAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,MAAA,GAAA,uBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAAA,CAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,OAAA,EAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,YAAA,GAAA,iCAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;QD3DA,CAAA;;;;CCwBA;;;;;;;;;;;;;;;;;GDHA,CAAA;IAAA;IACA,CAAA;IAAA,+BAAA;AAAA,CAAA,AADA,IACA;AAEA;;GAIA;AACA;IAAA;;;ID7BA,0DAAA,GAAA,UAAA,oBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,MAAA;;;;CC6BA;;;;;;;;;;;;;IDZA;;;;;;;;;;;;;;OAiBA;IACA,0DAXe,GAWf,UAAA,oBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,MAAA;QACA,IAAA,gBAAA,CAAA,IAAA,GAAA,EAAA,CAAA;QAXA,IAAA,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,QAAA,EAAA,CAAA,IAAA,EAAA,CAAA;QAYA,EAAA,CAAA,CAAA,oBAAA,CAAA,kBAAA,CAAA,IAXc,KAWd,KAAA,CAAA,IAX8C,KAW9C,KAAA,GAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAQ,OAAR,KAAA,KAAA,QAXiC,CAWjC,CAAA,CAAA;gBACA,IAAU,GAAV,IAXiB,CAWjB;YACA,CAAA;YACA,IAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,iBAAA,GAAA,KAAA,CAAA,KAAA,CAAA,wBAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,iBAAA,IAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBACA,MAAA,CAAA,IAAA,CAAA,yCAAA,oBAAA,SAAA,KAAA,CAAA,CAAA;gBACA,CAAA;YAEA,CAAA;QAEA,CAAA;;;;;;KAKA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;;;GAGA;;ID1DA,IAAA,gBAAA,CAAA,GAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;GAgDA;AACA,qCAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,eAAA,EAAA,aAAA,EAAA,cAAA;IACA,MAAA,CAAA;QACI,IAAJ,EAAA,CAAA,CAAA,yBAAA;QACI,OAAO,SAAA;QACP,WAAJ,aAAA;QACI,mBAAJ,qBAAA;QACI,SAAJ,WAAA;QACI,UAAJ,YAAA;QACI,OAAJ,SAAA;QACA,QAAA,UAAA;QACA,SAAA,WAAA;;QD3DA,aAAA,eAAA;;;;;;;;;;;;IAyBA;;;;OAHA;;;;;;IAYA;;;;;;;;;;;;;;;;OAoBA;IAEA,0CAAA,GAAA,UAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,SAAA,EAAA,OAAA,EAAA,eAAA;QACI,IAAJ,gBAAA,CAlBU,gBAkBV,GAAA,qBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAjByB,IAAM,EAiB/B,EAAA,OAAA,IAAA,EAAA,CAAA,CAAA;QAII,IAAJ,gBAAA,CAAA,iBAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,YAAA,CAAA,YAAA,CAAA,IAlB2D,iBAkB3D,CAAA;QACA,IAAA,gBAAA,CAAA,eAAA,GAAA,IAAA,CAAA,YAAA,CAAA,SAAA,CAAA,IAAA,iBAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QAEI,IAAJ,gBAAA,CAlBU,SAkBV,GAAA,uBAAA,CAAA,MAAA,EAAA,OAAA,EAAA,IAAA,CAAA,GAAA,CAAA,SAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QACI,EAAJ,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,YAAA,GAAA,iCAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YACA,MAlBc,IAkBd,KAAA,CAlBuB,YAkBvB,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,WAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,IAAQ,gBAAR,CAAA,YAAA,GAAA,IAAA,GAlBuC,EAkBvC,CAAA;QAEA,IAAA,gBAAA,CAAA,eAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACA,SAAA,CAAA,OAAA,CAAA,UAAA,EAAA;YAEM,IAAN,gBAAA,CAAA,GAAA,GAAA,EAAA,CAAA,OAAA,CAAA;YACA,IAAA,gBAAA,CAlByB,QAkBzB,GAAA,eAAA,CAAA,WAAA,EAAA,GAAA,EAAA,EAAA,CAAA,CAAA;YACA,EAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,QAAA,CAAA,IAAA,CAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,SAAA,GAAA,eAAA,CAAA,YAAA,EAAA,GAAA,EAAA,EAAA,CAAA,CAAA;YAEA,EAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,SAAA,CAAA,IAAA,CAAA,GAAA,IAlBgC,EAkBhC,CAlBgC,CAAgB,CAkBhD;YACA,EAAA,CAlBW,CAkBX,GAAA,KAAA,OAAA,CAAA,CAAA,CAAA;gBAIA,eAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;YACA,CAAA;QAEA,CAAA,CAAA,CAAA;;;;;;;;;;;GD1FA;;;;;;;;;;;;;;;;;;IA0BA,MAAA,CAAA,IAAA,gBAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAAA;;;;;AAQA;IALA;;;OAFA;IASA,0BAAA,IAAA,EAAA,GAAA;QAAA;QACA,IAAM,CAAN,IAAA,GAAA,IAAkB,CAAlB;QACA,IAAM,CAAN,GAAU,GAAV,GAAgB,CAAhB;QACA,IAAA,CAAA,mBAAA,GAAA,EAA6B,CAA7B;QACA,IAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QACA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,GAAA;YACA,IAAA,GAAA,GAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA;YACA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,UAAA;gBAEA,EAAA,CAAA,CAAA,OAAA,UAAiC,IAAjC,QAAA,CAA8C,CAAC,CAAC;oBAChD,UAA0B,CAAC,UAA3B,EAAA,KAA4C,EAA5C,GAAiD,CAAjD,CAAA;gBAEA,CAAA;YACM,CAAN,CAAA,CAAA;QACA,CAAK,CAAC,CAAC;QAEH,iBAAJ,CAAA,IAAA,CAA2B,MAA3B,EAAA,MAAA,EAAA,GAAA,CAAA,CAAA;QACA,iBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,GAAA,CAAA,CAAA;;;;QAJG,IAQH,CAAA,kBAAA,GAAA,wBARuD,CAAE,IAQzD,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;IAMA;;;;OAIA;IAEA,0CAAA,GAAA,UAAA,YAAA,EAAA,SAAA;;;;;CAnCA;AAqDA;;;;GAKA;AACA,kCAAA,WAAA,EAAA,MAAA;;;;;;;;;;;GAWA;AACA,2BAAA,GAAA,EAAA,IAAA,EAAA,IAAA;IAnCA,EAAA,CAAA,CAAA,GAAA,CAAA,cAAA,CAAA,IAAkB,CAAc,CAAhC,CAAA,CAAA;QAoCI,EAAJ,CAAO,CAnCC,CAmCR,GAnCS,CAAI,cAmCb,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QACA,CAAA;;ID5GA,IAAA,CAAA,EAAA,CAAA,CAAA,GAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;AA6BA;IAHA;;;OAHA;IACA,iCAAA,OAAsC,EAAA,WAAtC;QAEA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA;;;;;;IAUA;;;;OAIA;IAEA,0CAAA,GAAA,UAAA,EAAA,EAAA,QAAA;QARA,IAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QASA,IAAA,gBAAA,CARwB,GAQxB,GAAA,iBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,MAAA,IAAA,KAAA,CAAA,gEAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;;;;;;;;;;OAcA;;;;;;;;;;;OAYA;IAEA,wCAAA,GAAA,UAAA,EAAA,EAlBS,OAkBT,EAAA,OAAA;QAAA,iBAqCA;QArCA,wBAAA,EAAA,YAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QAEA,IAAA,gBAAA,CAAA,GAAA,GAlB4B,IAkB5B,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,YAAA,CAAA;QACA,IAAA,gBAAA,CAAA,aAlBoC,GAkBpC,IAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,GAlBS,CAkBT,CAAA,CAAA;YACA,YAAA,GAAA,uBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;YAlBA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBAmBA,IAAA,gBAAA,CAAA,MAAA,GAAA,eAAA,CAAA,aAAA,EAAA,IAAA,CAAA,OAAA,EAAA,EAlByF,CAAC,CAkB1F;gBACA,IAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,MAAA,CAAA,IAAA,CAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA;QAEI,CAAJ;QACA,IAAM,CAAN,CAAA;YAEA,MAAA,CAAA,IAAA,CAAA,sEAAA,CAAA,CAAA;YAEA,YAAA,GAAA,EAlBmB,CAAO;QAmB1B,CAAA;QAEA,EAlBM,CAAC,CAkBP,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YAEA,MAAA,IAAA,KAAA,CAAA,iEAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,aAAA,CAAA,OAAA,CAAA,UAAA,MAlB+B,EAkB/B,OAAA;YACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA,EAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA;QACI,IAAJ,gBAAA,CAAA,OAlB4B,GAkB5B,YAAA,CAAA,GAAA,CAAA,UAAA,CAAA;YACA,IAAA,gBAAA,CAlB2B,MAkB3B,GAlBwC,aAkBxC,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;YAEQ,MAAR,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,EAAA,EAAA,EAAA,MAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,mBAAA,CAAA,OAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,yCAAA,GAAA,UAAQ,EAAR;QACA,IAAA,gBAAA,CArBoB,MAAO,GAAM,IAqBjC,CAAA,UAAA,CAAA,EAAA,CAAA,CAAA;QACA,MAAA,CAAA,OAAA,EAAA,CAAA;QACA,OAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;;;;IAnBG,CAAH;IAyBA;;;OAGA;IACA,4CAAA,GAAA,UAxBW,EAwBX;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;OAaA;IACA,wCAAA,GAAA,UAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA;;;;;;;;;;;;OAYA;IAEA,yCAAA,GAAA,UAAQ,EAAR,EAAA,OApCmB,EAoCnB,OAAA,EAAA,IAAA;QACA,EAAA,CAAA,CAAM,OAAN,IAAA,UAAA,CAAA,CApCY,CAoCZ;YACM,IAAI,CApCC,QAoCX,CApCmB,EAAG,EAoCtB,gBApCwC,CAoCxC,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,MAAN,CAAa;QACb,CAAK;QAED,EAAJ,CAAA,CAAA,OAAA,IAAA,QApCU,CAoCV,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;YACM,IAAN,CApCW,MAAA,CAoCX,EAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,MAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,CAAA;QACA,MAAA,CAAA,CAAQ,OApCO,CAoCf,CAAA,CAAA;YACA,KAAA,MAAA;gBACA,MAAA,CAAA,IAAA,EAAA,CAAA;gBACQ,KAAR,CAAc;YACd,KAAA,OAAA;gBACA,MAAA,CAAA,KAAA,EAAA,CAAA;gBACQ,KAAR,CAAc;YACd,KAAA,OAAA;gBACA,MAAA,CAAA,KAAA,EAAA,CAAA;gBACQ,KAAR,CAAc;YACd,KAAA,SAAA;gBACA,MAAA,CApCW,OAoCX,EAAA,CAAA;gBACQ,KAAR,CAAc;YACd,KAAA,QAAA;gBACA,MAAA,CAAA,MAAA,EAAA,CAAA;gBACQ,KAAR,CAAc;YACd,KAAA,MAAA;gBACA,MAAA,CAAA,IAAA,EAAA,CAAA;gBACQ,KApCK,CAoCb;YACA,KAAA,aAAA;gBACA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA;YACA,KAAA,SAAA;;gBD/LA,KAAA,CAAA;;;;CC6BA;;;;;;;GDJA;AACA,IAAA,kBAAsB,GAAtB,EAAA,CAAA;AACA,IAAA,kBAAA,GAAA;IACA,WAAA,EAAA,EAAA;IACE,aADa,EAAA,IACf;IACE,YAAF,EAAA,KADiB;IAEf,oBAAF,EAAA,KAAA;CACA,CAAA;AACA,IAAA,0BAAA,GAAA;IAkBA,WAAA,EAAA,EAAA;IAQA,aAAA,EAAA,IAAA;;;;AAMA,IAAA,YAAA,GAFqB,cAErB,CAAA;AACA;IACA;;OAEA;IACA,oBAAA,KAAA;QACA,IAAA,KAAa,GAAb,KAAqB,IAArB,KAA8B,CAA9B,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,KAAA,GAAA,KAAqB,GAArB,KAAiD,CAAjD,OAAA,CAAA,GAAA,KAAA,CAAA;QACA,IAAA,CAAA,KAAA,GAAA,qBAAA,CAAA,KAAA,CAAA,CAAA;QAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YACM,IAAN,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA,CAAA;YACA,OAAA,OAAA,CAAA,OAAA,CAAA,CAAA;YACQ,IAAR,CAAa,OAAb,GAAA,OAA8B,CAA9B;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YACA,IAAA,CAAA,OAAA,GAAA,EAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,kCAAA,GAAA,UAAA,OAAA;QACA,IAAA,gBALoB,CAKpB,SAAA,GAAA,OAAA,CAAA,MAAA,CAAA;QACA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,WAAA,GAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBACA,EAAA,CAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;oBACA,WAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;gBAHa,CAKb;YASA,CAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;;CA1CA;;;;;IAqDA;;;;OAtBA;IAEA,sCAAA,EAAA,EAAA,WAAA,EAAA,OAAA;QACU,IAAV,CAAA,EAAA,GAAA,EAAA,CAAA;QAEU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QAqBI,IAAI,CAAC,OAAT,GAAA,OAA0B,CAA1B;QACI,IAAJ,CAAA,OAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;OAcA;IACA,6CAAA,GAAA,UAAA,OArBY,EAqBZ,IAAA,EAAA,KAAA,EAAA,QAAA;QAAA;QAEA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YAEQ,MAAR,IAAA,KAAA,CAAA,uDAAA,KAAA,2CAAA,IAAA,sBAAA,CAAA,CAAA;QACA,CAAA;QAEA,EAAA,CAAA,CAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YAEA,MAAA,IAAA,KAAA,CAAA,iDAAA,IAAA,gDAAA,CAAA,CAAA;QACI,CAAJ;QACI,EAAJ,CAAA,CAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YAEA,MAAA,IAAA,KAAA,CAAA,4CAAA,KAAA,uCAAA,IArByF,yBAqBzF,CAAA,CAAA;QACI,CAAJ;QACA,IAAA,gBArBwB,CAqBxB,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;QACA,IAAA,gBArBwB,CAqBxB,IAAA,GAAA,EAAA,IAAA,MAAA,EAAA,KAAA,OAAA,EArB+C,QAqB/C,UAAA,EArBqD,CAAK;QAsB1D,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,kBAAA,GAAA,eAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;QAEI,EAAJ,CAAA,CAAA,CAAA,kBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;QAIA,CAAA;QACA,MAAA,CAAA;YACA,kEAAA;YACA,kEAAA;YACA,kEAAA;YAEA,KAAQ,CAAR,OAAA,CArBc,UAAK,CAAS;gBAsB5B,IAAA,gBAAA,CAAA,KAAA,GAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBACA,SAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;gBACA,CAAA;gBACA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IAzBA,+CAAA,GAAA,UAAW,IAAX,EAAA,GAAA;QA0BA,EAAA,CAAA,CAAM,IAAI,CAzBC,SAAC,CAAS,IAAC,CAAI,CAyB1B,CAAA,CAzB6B;YA0BvB,QAAN;YACA,MAAA,CAAA,KAAA,CAAA;QACA,CAAA;;;;;IAvBG,CAAH;IA6BA;;;OAGA;IACA,kDA5BW,GA4BX,UAAA,IAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;OAcA;IACA,8CAAA,GAAA,UAAM,OAAN,EAlCe,WAkCf,EAAA,KAAA,EAAA,iBAAA;QAAA,iBA2EA;QA3EA,kCAAA,EAAA,wBAAA;QACA,IAAA,gBAlCwB,CAkCxB,OAAA,GAAA,IAAA,CAAA,WAAA,CAAA,WAAA,CAlCqD,CAAY;QAmCjE,IAAA,gBAAA,CAAA,MAAA,GAlCoB,IAAgB,yBAkCpC,CAAA,IAAA,CAlCqE,EAAA,EAAI,WAkCzE,EAAA,OAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QAEI,EAAJ,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA,CAAA;YACA,QAAA,CAAA,OAAA,EAAA,oBAAA,CAAA,CAAA;YAEA,QAAA,CAAA,OAAA,EAAA,oBAAA,GAlC2B,GAkC3B,GAAA,WAAA,CAAA,CAAA;YACQ,IAAR,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,EAAA,kBAAA,GAAA,EAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,kBAAA,CAAA,WAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,OAlCwB,GAkCxB,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA;QAEI,IAAJ,gBAAA,CAAA,KAAA,GAAA,KAAA,IAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,CAAN,KAAA,IAAA,SAAA,CAAA,CAAA,CAAA;YACA,OAAA,CAAA,aAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA;QAlCA,CAAA;QAmCA,kBAlCoB,CAkCpB,WAAA,CAAA,GAAA,OAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YAEA,SAAA,GAAA,mBAAA,CAAA;QAEI,CAAJ;;;;;QAKA,gBAAA,CAAA,OAAA,CAAA,UAAA,MAAA;YACA,6EAAA;YACA,0EAAA;YACA,wEAAA;YAEA,sEAAA;YACA,EAAA,CAAA,CAAA,MAAA,CAAA,WAAA,IAAA,KAAA,CAAA,EAAA,IAlCQ,MAkCR,CAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,MAAA,CAlCU,OAkCV,EAAA,CAAA;YACM,CAAN;QAlCA,CAAA,CAAA,CAAA;QAmCA,IAAM,gBAAN,CAAA,UAAA,GAAA,OAAA,CAAA,eAAA,CAAA,SAAA,CAAA,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;QACA,IAAM,gBAAN,CAAA,oBAAA,GAAA,KAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA;YAEQ,EAAR,CAAA,CAAA,CAAA,iBAAA,CAAA;gBACA,MAAA,CAAA;YAGQ,UAAR,GAAA,OAAA,CAlCU,kBAkCV,CAAA;YACM,oBAAN,GAAA,IAAA,CAAA;QACA,CAAK;QAED,IAAJ,CAAA,OAAA,CAlCY,kBAkCZ,EAAA,CAAA;QACA,IAAM,CAAN,MAAA,CAAA,IAlCkB,CAkClB,EAAA,OAAA,SAAA,EAAA,WAAA,aAAA,EAAA,UAAA,YAAA,EAAA,SAAA,WAAA,EAAA,OAAA,SAAA,EAAA,MAAA,QAAA,EAAA,oBAAA,sBAAA,EAAA,CAAA,CAAA;QAEA,EAAA,CAAA,CAAM,CAAN,oBAlCU,CAkCV,CAAA,CAAA;YACM,QAAN,CAlCW,OAAW,EAkCtB,sBAAA,CAAA,CAAA;QACA,CAAA;QACA,MAAA,CAAA,MAAA,CAAA;YAEM,WAAN,CAAA,OAAA,EAAA,sBAAA,CAAA,CAlC4B;YAmCtB,IAAI,gBAAV,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAQ,KAAR,IAAA,CAAA,CAAA,CAAA,CAAA;gBACQ,KAAI,CAAZ,OAAA,CAlCqB,MAkCrB,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;gBAEA,EAAA,CAAA,CAlCU,OAAQ,IAkClB,CAAA,CAAA,CAlCuB,CAAM;oBAmC7B,OAAA,CAAA,MAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;gBAEA,CAAA;YACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAKA;IAEA,iDAAA,GAAA,UAAA,IAAA;QAAA,iBAMA;QALA,OAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,wDAAA,GAAA,UAAA,OAAA;QACA,IAAM,CAAN,OAAA,CAAA,eAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,cAAA,GAAA,IAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;IAaA,yDAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,OAAA;QAAA;QAAA,wBAAA,EAAA,eAAA;QACA,IAAA,CAAA,OAAU,CAAV,MAAiB,CA7CC,KA6ClB,CAAA,WAAA,EAAA,mBAAA,EAAA,IAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA;YACA,EAAA,CAAA,CAAA,OAAA,IAAA,aAAA,CAAA,GAAA,EAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;gBAEQ,IAAR,gBAAA,CAAA,OA7CsC,GA6CtC,KAAA,CAAA,OAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;gBACA,sEAAA;gBA7CA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;oBA8CY,OAAZ,CAAA,UA7Cc,CAAiB,GAAC,EAAI,OA6CpC,EAAA,IAAA,CAAA,CAAA;gBACA,CAAA;gBACA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;YACA,CAAA;;;;;;;;;;;OAYA;IAEA,iDAAA,GAAA,UAAA,OAAA,EAAA,OAlDU,EAkDV,YAAA;QAAA;QACI,IAAJ,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA;QACA,EAAA,CAAA,CAAM,CAAN,YAAA,IAAA,OAAA,CAAA,iBAAA,CAAA,CAAA,CAAA;YACM,IAAN,CAAA,kBAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAlD0C,CAkD1C,CAAA;;;QAGA,EAAA,CAAA,CAAA,aAlDiB,CAkDjB,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAc,aAAd,CAAA,CAAA,OAAA,CAAA,UAAA,WAAA;gBACA,6DAAA;gBACA,yDAAA;gBACA,EAAA,CAAA,CAAA,KAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,EAAA,WAAA,EAAA,UAAA,EAAA,KAAA,CAAA,CAAA;oBAEA,EAAA,CAlDW,CAAO,MAAC,CAkDnB,CAlD0B,CAkD1B;wBACA,SAAA,CAAA,IAAA,CAAA,MAlDgB,CAAoB,CAkDpC;oBACA,CAAA;gBACQ,CAAR;YACA,CAAO,CAAP,CAAA;YACA,EAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;gBAIA,MAAA,CAAA;YACQ,CAAR;QACA,CAAA;;;;;YAOM,IAAN,gBAlD4B,CAkD5B,cAlD2C,GAkD3C,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,MAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA;YACA,mEAAA;YACA,kEAAA;YAlDA,kEAAA;YAmDA,yDAAA;YACA,EAAA,CAAA,CAAQ,cAAR,IAAA,cAAA,CAlDgC,MAkDhC,CAAA,CAAA,CAAA;gBACA,iCAAA,GAAA,IAAA,CAAA;YACA,CAAA;YACA,IAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,OAAA,CAAA;gBACA,OAAA,MAAA,GAAA,MAAA,CAAA,UAAA,EAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,QAAA,GAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA;wBACA,iCAAA,GAAA,IAAA,CAAA;;;;;QAMI,CAAJ;QACI,iEAAJ;QACA,kEAAA;QACA,kEAAA;QACA,mEAAA;QACA,IAAA,gBAAA,CAAA,SAlDgC,GAkDhC,IAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;QAlDA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YAmDA,IAAA,gBAAA,CAlDyB,iBAkDzB,GAAA,IAAA,GAAA,EAAA,CAAA;YAEA,SAAA,CAAA,OAAA,CAAA,UAAA,QAAA;gBACQ,IAAR,gBAAA,CAlDc,WAkDd,GAAA,QAAA,CAAA,IAAA,CAAA;gBACQ,EAAR,CAAA,CAAA,iBAAA,CAAA,GAlDc,CAkDd,WAAA,CAlDc,CAkDd;oBACA,MAAA,CAAA;gBACQ,iBAAR,CAAA,GAAA,CAAA,WAAA,CAAA,CAlDwB;gBAmDhB,IAAR,gBAAA,CAlDc,OAkDd,GAAA,KAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAlD2B;gBAoDnB,IAAR,gBAAA,CAAA,UAlDwC,GAkDxC,OAAA,CAAA,kBAAA,CAAA;gBACQ,IAAR,gBAAA,CAAA,aAAA,GAAA,CAAA,CAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,SAAA,GAAA,aAAA,CAAA,WAAA,CAAA,IAAA,mBAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,UAAA,CAAA,UAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,yBAAA,CAAA,KAAA,CAAA,EAAA,EAAA,WAAA,EAAA,OAAA,CAAA,CAAA;gBACA,KAAU,CAAV,OAAA,CAAmB,kBAAnB,EAAA,CAAA;gBACA,KAAU,CAAV,MAAiB,CAAjB,IAAA,CAAA;oBACU,OAAV,SAAA;oBACU,WAAV,aAAA;oBACA,UAAA,YAAA;oBACA,SAAA,WAAA;oBACA,OAAA,SAAA;;;iBAIA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA;QACA,CAAK;QAlDL,sFAAA;;;YAqDM,MAAM,CAlDC,oBAkDb,CAlD8B,IAkD9B,CAAA,EAAA,EAAA,OAAA,EAlDoC,KAkDpC,EAAA,OAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAM,CAAN,CAAA;YACA,+CAAA;YACA,kCAAA;;;;;;IAMA;;;;;IAKA,iDAAA,GAAA,UAAA,OAAA,EAAA,MAAA,IAAA,QAAA,CAAA,OAAA,EAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;IAzDA,6DAAA,GAAA,UAAA,WAAA;QAAA,iBAoGA;QAzCA,IAAA,gBAAA,CAAA,YAAA,GAAA,EAAA,CAAA;QACA,IAAM,CAAN,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YACM,IAAN,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;YACA,EAAA,CAAA,CAAQ,MAAR,CAAA,SAAA,CAzDmB;gBA0DnB,MAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAzD8B;YA2D9B,IAAA,gBAAA,CAzD0B,SAyD1B,GAAA,KAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;gBACA,SAAA,CAAA,OAAA,CAAA,UAAA,QAAA;oBACA,EAAA,CAAA,CAAA,QAAA,CAAA,IAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;wBACA,IAAA,gBAAA,CAAA,SAAA,GAAA,kBAAA,CAAA,OAAA,EAAA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;wBAEA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,WAAA,CAAA;wBACA,cAAA,CAzDsB,KAyDtB,CAAA,MAAA,EAAA,QAAA,CAAA,KAAA,EAAA,SAAA,EAAA,QAAA,CAAA,QAAA,CAAA,CAAA;;;YAGA,CAAA;YACA,EAAA,CAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,OAAA,CAAA,UAAA,CAAA;oBAzDA,yEAAA;oBA0DA,2BAAA;oBACA,MAAA,CAAA,OAAA,EAAA,CAAA;gBACA,CAAA,CAAA,CAAA;YAEQ,CAzDC;YA2DT,IAAA,CAAA,CAAA;;;QAGA,CAAA,CAAA,CAAA;QACA,IAAM,CAAN,MAAA,GAAA,EAAA,CAAA;QACA,MAAA,CAAA,YAzDqB,CAyDrB,IAAA,CAzD2B,UAAA,CAyD3B,EAAA,CAAA;YACA,sCAAA;YACA,2CAAA;YACM,IAAN,gBAAA,CAAA,EAAA,GAAA,CAAA,CAAA,UAAA,CAAA,GAzDkD,CAAC,QAyDnD,CAzD4D;YA0D5D,IAAA,gBAAA,CAAA,EAAA,GAAA,CAAA,CAAA,UAAA,CAAA,GAAA,CAAA,QAAA,CAAA;YACA,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;;;;;IAOA,CAAA;IACA;;;OAEA;IACA,0DAAA,GAAA,UA/De,OA+Df;QACI,IAAJ,gBA/DwB,CA+DxB,YAAA,GAAA,KAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,YAAA,GAAA,IAAA,CAAA;QAEA,YAAA;;;;;;AAmDA;IArFA;;;OArBA;IACA,mCAAA,MAAA,EAAA,WAAA;QACS,IAAT,CAAA,MAAA,GAAA,MAAA,CAAA;QACS,IAAT,CAAA,WAAA,GAAA,WAA+B,CAA/B;QACS,IAAT,CAAA,OAAA,GAAA,EAAA,CAAA;QACS,IAAT,CAAA,eAAA,GAAS,IAAqB,GAA9B,EAAA,CAAA;QAEU,IAAV,CAAA,gBAAU,GAAiE,IAA3E,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,uBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,eAAwC,GAAxC,IAAA,GAAA,EAAA,CAAA;QACU,IAAV,CAAA,eAAA,GAAyC,CAAG,CAA5C;QAES,IAAT,CAAA,kBAAA,GAAA,CAAA,CAAS;QACA,IAAT,CAAA,gBAAA,GAAA,EAAA,CAAS;QACA,IAAT,CAAA,cAAA,GAAA,EAAA,CAAA;QAGS,IAAT,CAAA,SAAA,GAAA,EAAA,CAAA;QAIA,IAAA,CAAA,aAAA,GAAA,EAAA,CAAA;;;;;;IAgFA;;;;OAUA;IACA,sDAAA,GAAA,UAAA,OAAA,EAAA,OAAA,IAxFiD,IAwFjD,CAAA,iBAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;IAIA,sBAAA,oDAAA;QAHA;;WAEA;aACA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,EAAA,CAAA;YACA,IAAA,CAAO,cAAP,CAAA,OAAA,CAAA,UAAA,EAAA;gBACA,EAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,MAAA;oBACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;IAMA;;;;OAIA;IA5FA,mDAAA,GAAA,UAAA,WAAA,EAAA,WAAA;;;;QAgGA,CAAA;;;;;;YAOM,kEAAN;YACA,+DAAA;YACA,kEAAA;YACA,oEAAA;;;;;;IA1FG;;;;OAoGH;IACA,yDAAA,GAAA,UAAA,EAAA,EAAA,WAAA;QACA,IAAA,gBAAA,CAhGkB,KAgGlB,GAAA,IAAA,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAc,CAhGC;YAiGf,IAAA,gBAhGkB,CAAK,KAgGvB,GAAA,KAAA,CAAA;YACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,KAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,aAAA,GAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,eAAA,CAAA,aAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAhGkB,CAgGlB,cAAA,CAAA,MAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;oBACY,KAAZ,GAAA,IAAA,CAAA;oBACA,KAAA,CAAA;gBACA,CAAA;YAhGA,CAAW;YAiGL,EAAN,CAAA,CAAU,CAhGC,KAgGX,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;YAEQ,CAhGC;QAiGL,CAAJ;QACA,IAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,4CAAA,GAAA,UAAA,WAAA,EAAA,WAAA;QACA,IAAA,gBAAA,CAAA,EAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;;;;;;;;;;;OAWA;IACA,mDAAA,GAAA,UAAA,WAAA,EAAA,IAAA,EAAA,OAAA;;;;;;IAMA;;;;OAKA;IACA,2CAAA,GAAA,UAAM,WAAN,EAAA,OAAA;QAAA,iBAlGA;QAmGA,EAAA,CAAA,CAAM,CAAN,WA7GkB,CA6GlB;YACM,MAAN,CAAA;QACA,IAAA,gBAAA,CAAA,EAAA,GAAA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,CAAA;QACA,IAAA,CAAA,UAAA,CAAA;YACA,KAAA,CAAA,uBAAA,CAAA,MAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA;YACA,OAAA,KAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;YAEQ,IAAR,gBAAA,CAAA,KAAA,GAAA,KA7G2C,CA6G3C,cA7GoD,CAAO,OA6G3D,CAAA,EAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;;;;IA3GG,CAAH;;;;;;;;;;;;OA4HA;IACA,2CAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA;QACA,EAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;IAaA,8CAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA;QACI,EAAJ,CAAA,CAAQ,CAAR,aAAA,CAAA,OAAA,CAAA,CAAA;YACM,MAAN,CAAa;QACb,8EAAA;;;;YAKQ,OAAR,CAAA,aAAA,GAAA,KAAA,CAAA;QACA,CAAA;QACA,6DAAA;;QAGI,iEAAJ;QACA,EAAA,CAAA,CAAM,WAAN,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,CAAA,UAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;QACA,CAAA;;;;;IAKA,CAAA;;;;;;;;;;;;OAYA;IAEA,8CAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,OAAA,EAAA,YAAA;QACI,EAAJ,CAAA,CAAQ,CAAR,aAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;YACM,IAAN,CAAA,kBArI6B,CAqI7B,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,MAAA,CAAA;QArIA,CAAA;QAsIA,IAAA,gBAAA,CAAA,EAAA,GAAA,WAAA,GAAA,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA;QACA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA,YAAA,CAAA,CAAA;;;;;;;;;;;;OAYA;IACA,wDAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,OAAA;QACA,IAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;QACA,OAAA,CAAA,YAAA,CAAA,GAAA;;;;;;;;;;;;;OAeA;IACA,0CAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,QAAA;;;;;;IAhJG;;;;;;;IA+JH,CAAA;IACA;;;OAGA;IACA,0DAAA,GAAA,UAAA,gBAAA;QAAA,iBAiCA;;;;YA7BA,EAAA,CAAA,CAAA,OAAA,CAAc,CAAd,CAAA;gBACA,OAAA,CAAY,OAzJO,CAyJnB,UAAA,MAAA;oBACA,+EAAA;oBAzJA,4EAAA;oBA0JA,oEAAA;oBACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBACA,MAAA,CAAA,gBAAA,GAAA,IAAA,CAAA;oBACA,CAAA;oBACA,IAAA,CAAA,CAAA;wBAxJW,MAyJX,CAAA,OAAA,EAAA,CAAA;oBACA,CAAA;gBACA,CAAA,CAAA,CAAA;YACA,CAAA;YAEQ,IAAR,gBAAA,CAAA,QAAA,GAAA,KAzJ6C,CAyJ7C,eAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAzJgD,EAAhD,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA;gBA2JY,MAAZ,CAzJe,IAyJf,CAAA,QAzJ4B,CAAK,CAyJjC,OAAA,CAAA,UAAA,WAAA,IAAA,OAAA,QAAA,CAAA,WAAA,CAzJ0E,GAyJ1E,mBAAA,EAAA,CAAA,CAAA,CAAA;YACQ,CAAR;QACA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAQ,CAAR,uBAAA,CAAA,IAAA,IAzJwB,CAyJxB,CAAA;YACA,MAAA,CAAA;QACA,QAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAzJ0B,CAyJ1B,gBAzJ2C,EAyJ3C,qBAAA,EAAA,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA;gBACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;;;QAIA,CAAA;IACA,CAAA;IACA;;OAEA;IA3JA,qDAAA,GAAA;QAAA,iBAoKA;QARA,MAAA,CAAA,IAAA,OA3JkB,CA2JlB,UAAA,OAAA;YACA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,MAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,cAAA,OAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,oDAAA,GAAA,UAAA,OAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,OAAA,IAAA,OAAA,CAAA,aA9JmB,CA8JnB,CAAA,CAAA;YACA,8CAAA;YACA,OAAA,CAAU,YAAV,CAAA,GAAA,kBAAA,CAAA;YACA,EAAA,CAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,CAAA;gBACU,IAAV,gBAAA,CAAA,EAAA,GAAA,IAAA,CAAA,eAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;oBACA,EAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,yCAAK,GAAL,UAAA,WAAA;QAAA,iBAhIA;QAgIA,4BAAA,EAAA,eAAA,CAAA;QAEI,IAAI,gBAAR,CAAA,OAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAjKU,eAiKV,CAAA,IAAA,CAAA,CAAA,CAjKoC;YAkK9B,IAAN,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,EAjKsC,EAiKtC,OAAA,IAAA,OAAA,KAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,eAAA,CAAA,KAAA,EAAA,CAAA;QAjKA,CAAA;QAkKA,EAAA,CAAA,CAAM,IAAN,CAAW,cAAX,CAAA,MAjKe;YAkKf,CAAA,IAAA,CAAA,kBAAA,IAAA,IAAA,CAAA,sBAAA,CAAA,MAAA,CAAA,CAjK8B,CAAsB,CAAC;YAkKrD,OAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YAEQ,GAAR,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,OAjKyC,GAiKzC,IAAA,CAAA,sBAAA,CAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,OAjKyC,CAAA,CAAE;YAkKnC,CAjKC;QAkKL,CAAJ;QAEI,IAAI,CAAR,kBAjK2B,GAiK3B,CAAA,CAAA;;;;QAIA,IAAM,CAAN,SAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAM,IAAI,CAjKC,aAAC,CAiKZ,MAAA,CAAA,CAAA,CAAA;YAEM,2CAAN;YACA,iDAAA;YACA,8CAAA;YAjKA,IAAA,gBAAA,CAAA,UAAA,GAAA,IAAA,CAAA,aAAA,CAAA;YAkKA,IAAQ,CAAR,aAAA,GAjKyB,EAAC,CAiK1B;YACA,EAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,mBAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,cAAA,UAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,OAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA;;;;;IA/JG,CAAH;IAqKA;;;OAGA;IACA,oDAAA,GAAA,UAAA,WAAA;QAAA;QACI,IAAJ,gBAAA,CApKU,YAoKV,GAAA,IApKU,qBAoKV,EAAA,CAAA;QACI,IAAJ,gBAAA,CApKU,cAoKV,GAAA,EAAA,CApKU;QAsKN,IAAJ,gBAAA,CApKU,iBAoKV,GAAA,IApKiC,GAoKjC,EAAA,CAAA;QACI,IAAJ,gBAAA,CApKU,kBAoKV,GApKsC,EAoKtC,CAAA;QACA,IAAA,gBAAA,CAAA,eApKqC,GAoKrC,IAAA,GAAA,EAAA,CAAA;QACA,IAAU,gBAAV,CAAA,mBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;;;;YAKA,IAAA,CAAA,sBApKiB,CAoKjB,MAAA,CAAA,oBApK8C,CAAA,IAAK,CAoKnD,sBAAA,CAAA,CAAA;YACM,EAAN,CAAA;QACA,oEAAA;QAEI,oEAAJ;QACI,0CAAJ;QACI,GAAJ,CAAA,CAAS,IAAT,gBAAA,CApKa,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,aAoKxB,CAAA,MAAA,EAAA,CAAA,EAAA,EApK8B,CAAsB;YAqK9C,QAAN,CAAA,aAAA,CApKY,CAoKZ,CAAA,EAAA,eAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,CApK4C;QAqK5C,IAAA,gBAAA,CAAA,2BAAA,GAAA,EAAA,CAAA;QACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CApKuB,CAAI,GAoK3B,CAAA,EAAA,CApK4B,GAoK5B,IAAA,CAAA,sBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,OApK2C,CAoK3C,YAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,OAAA,IAAA,OAAA,CAAA,aAAA,CAAA,CAAA,CAAA;gBACA,QAAA,CAAA,OAAA,EAAA,eAAA,CAAA,CAAA;gBACA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;gBAEA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,YAAA,CApKiB,CAAK,CAoKtB;oBACA,2BAAA,CAAA,IAAA,CAAA,OApKqC,CAAC,CAAC;gBAqKvC,CAAA;YACA,CAAA;QAEA,CAAA;QACA,GAAA,CAAA,CAAA,IAAA,gBApKyB,CAAA,CAoKzB,GApK2B,IAoK3B,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,EApKuD,CAoKvD,IAAA,CAAA,EAAA,CApKgE,EAoKhE,EAAA,CAAA;YACA,IAAA,gBApKkB,CAoKlB,EApK2B,GAoK3B,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA;gBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;gBAEQ,IAAR,gBAAA,CApKc,OAoKd,GAAA,KAAA,CAAA,OAAA,CAAA;gBACQ,EAAR,CAAA,CAAY,CApKC,QAoKb,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,eAAA,CAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;oBApK0B,MAA1B,CAA0B,OAA1B,EAAA,CAAA;;;gBAwKQ,IAAR,gBAAA,CAAA,WAAA,GAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAU,CAAV,WAAA,CAAA;oBACU,MAAM,CApKC;gBAqKjB,sEAAA;gBACA,6DAAA;gBACA,EAAA,CAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,CAAA,CAAA;;;;;;gBAOQ,6EAAR;gBAEQ,4EAAR;gBAEQ,4EAAR;gBAEQ,gFAAR;gBAEQ,yCAAR;gBAGQ,WAAW,CApKC,SAoKpB,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,OAAA,EAAA,CAAA,uBAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CAAA;gBACA,YAAA,CAAA,MAAA,CAAA,OApKgB,EAoKhB,WAAA,CApKwB,SAoKxB,CAAA,CAAA;gBACA,IAAA,gBApK4B,CAoK5B,KAAA,GAAA,EAAA,WAAA,aAAA,EAAA,MAAA,QAAA,EAAA,OAAA,SAAA,EAAA,CAAA;gBACA,kBAAA,CAAA,IAAA,CAAA,KApKgB,CAoKhB,CAAA;gBACA,WAAA,CAAgB,eAAhB,CAAA,OAAA,CAAA,UAAA,OAAA,IAAA,OAAA,eAAA,CAAA,eAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA;gBACA,WAAA,CAAc,aAAd,CAAA,OAAA,CAAA,UApKmC,SAoKnC,EAAA,OAAA;oBACA,IAAA,gBAAA,CAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAY,KAAK,CApKC,MAoKlB,CApKmB,CAAO,CAoK1B;wBACA,IAAA,gBAAA,CAAA,QAAA,GAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;wBACA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA;4BAlKoB,mBAoKpB,CAAA,GApK2C,CAAC,OAoK5C,EApK6C,QAoK7C,GApKuD,IAoKvD,GAAA,EAAA,CAAA,CAAA;wBACA,CAAA;wBACA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,QApKoC,CAoKpC,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA;oBACU,CAAV;gBACA,CAAA,CAAA,CAAA;gBACA,WAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA,OAAA;oBACU,IApKM,gBAAgB,CAoKhC,KApKgC,GAoKhC,MApK4C,CAAI,IAoKhD,CAAA,SAAA,CAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBACA,oBAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,GAAA,IAAA,GAAA,EAAA,CAAA,CAAA;;;gBAIA,CAAA,CAAA,CAAA;YACA,CAAS,CAAT,CAAA;QACA,CAAA;QACA,0EAAA;QACA,2CAAA;QACA,IAAA,gBAAA,CAAA,2BAAA,GAAA,EAAA,CAAA;QACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;YAEA,IAAA,gBAAA,CAAA,OAAA,GAAA,aAAA,CApKsC,CAoKtC,CAAA,CApKsC;YAqKtC,EAAA,CAAA,CAAA,CAAA,YApKQ,CAoKR,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBACA,2BAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;YACM,CAAN;QACA,CAAA;QACA,IAAA,gBAAA,CAAA,qBApKsC,GAoKtC,IAAA,GAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,oBAnKkB,GAmKlB,EAAA,CAnKmB;QAqKnB,kBAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA;YAEA,EAAA,CAAA,CAAA,YAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBACA,oBAAA,CAAA,OAAA,CApKsB,OAAO,CAoK7B,CAAA;gBACA,KAAA,CAAA,qBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,WAAA,EAAA,KAAA,CAAA,WAAA,EAAA,qBAAA,CAAA,CAAA;YAEM,CAAN;QAEA,CAAK,CApKC,CAAC;QAsKH,cAAJ,CAAA,OApK0B,CAoK1B,UAAA,MApK2B;;YAuK3B,IAAA,gBAAA,CAAA,eAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,KAAA,EAAA,MAAA,CAAA,WAAA,EAAA,MAAA,CAAA,WAAA,EAAA,IAAA,CAAA,CAAA;YACQ,eAAR,CAAA,OAAA,CAAA,UAAA,UAAA,IAAA,eAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAnKqF,CAmKrF,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEA,CAAA,CAAA,CAAA;;QAGI,+BAAJ;QAGI,IAAJ,gBAAA,CApKU,YAoKV,GAAA,mBAAA,CAAA,IAAA;YACA,qBAAA,CAAA,IAAA,CAAA,MAAA,EApKuD,2BAoKvD,EAAA,mBAAA,EAAA,UAAA,CAAA;YACA,IAAA,GAAA,EAAA,CAAA;QACA,gCAAA;;;QAGA,IAAA,gBAAA,CAAA,UApKmC,GAoKnC,EAAA,CAAA;QACA,kBAAA,CAAA,OAAA,CAAA,UAAA,KAAA;YAGA,IAAA,uBAAA,EAAA,qBAAA,EApK6B,+BAAC,CAoK9B;YAEA,oEAAA;YACA,yEApK2D;YAqK3D,EAAA,CAAA,CAAA,YAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CApKgB,CAoKhB;gBACA,IAAA,gBAAA,CAAA,WAAA,GAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,WAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,aAAA,CAAA,CAAA;gBApKA,MAAA,CAAA,aAAA,CAAA,WAAA,CAAA,CAAA;gBAqKA,IAAU,gBApKgB,CAoK1B,iBAAA,GAAA,IAAA,CAAA;gBACA,GAAA,CAAA,CAAA,IAAA,gBAA6B,CAA7B,CAAA,GAAA,CAAA,EAAA,CApKgC,GAoKhC,oBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAA,MAAA,KAAA,OAAA,CAAA;wBACA,KAAA,CAAA;oBAEY,EAAZ,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,eAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;wBACA,iBAAA,GAAA,MAAA,CAAA;wBACc,KAAd,CAAA;oBACA,CAAA;gBACA,CAAA;gBACA,EAAA,CAAA,CAAU,iBAAV,CAAA,CApK0B,CAAI;oBAqK9B,IAAA,gBAAA,CAAA,aAAA,GAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,iBAAA,CAAA,CAAA;oBApKA,EAAA,CAAA,CAAA,aAAA,IAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBAqKA,MAAA,CAAqB,YApKO,GAoK5B,mBAAA,CAAA,aAAA,CAAA,CAAA;oBACA,CAAA;oBACA,cAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;gBApKA,CAAa;gBAqKL,IAAR,CAAA,CAAA;oBACA,WAAA,CApKgB,IAoKhB,CAAA,MAAA,CAAA,CAAA;gBACQ,CAAR;YACA,CAAO;YACP,IAAA,CAAA,CAAA;gBAEA,WApKuB,CAoKvB,OAAA,EAAA,WAAA,CAAA,UAAA,CAAA,CAAA;gBACA,MAAA,CAAA,SAAA,CAAA,cAAA,OAAA,SAAA,CAAA,OAAA,EAAA,WAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA,CAAA;gBACU,UAAV,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,UAAA,CAAA,OAAA,CAAA,UAAA,MAAA;YACA,IAAA,gBAAA,CAAA,iBAAA,GAAA,iBAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;;;;YAKA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,yDAAA;QACA,4DAAA;QApKA,iDAAA;QAqKA,cAAc,CApKC,OAAC,CAoKhB,UAAA,MAAA;YACA,EAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA,CAAA,CAAA;gBACA,MAAA,CAAA,YAAA,CAAA,SAAA,CAAA,cAAA,OAAA,MAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;;;YAKA,CAAS;QACT,CAAA,CAAA,CAAA;QACA,yDAAA;;;;YAKM,IAAN,gBApKqB,CAAQ,OAoK7B,GAAA,aAAA,CAAA,CAAA,CAAA,CAAA;YApKA,IAAA,gBAAA,CAAA,OAAA,GAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;YAsKM,+DAAN;;;;gBAKU,QAAV,CAAA;YACA,IAAQ,gBAAR,CAAA,OAAA,GAAA,EAAA,CAAA;YACA,gEAAA;YACA,+DAAA;YACA,6CAAA;YAEA,EAAA,CAAA,CAAQ,eAAR,CAAA,IAAA,CApKY,CAoKZ,CAAA;gBACQ,IAAR,gBAAA,CAAA,oBAAA,GAAA,eAAA,CAAA,GAAA,CApKkD,OAAY,CAoK9D,CAAA;gBACA,EAAA,CAAA,CAAU,oBAAV,IAAA,oBAAA,CAAA,MAAA,CAAA,CApK+B,CAAgB;oBAqKrC,OAAV,CAAA,IAAA,OAAU,OAAV,EAAA,oBApKgC,EAoKhC;gBACA,CAAA;gBACA,IAAA,gBAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,qBAAA,EAAA,IAAA,CAAA,CAAA;gBACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,oBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA,CAAA;oBACA,IAAA,gBAAA,CAAA,cAAA,GAAA,eAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACA,EAAA,CApKW,CAAO,cAoKlB,IAAA,cAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBACA,OAAA,CAAA,IAAA,OAAA,OAAA,EAAA,cAAA,EAAA;oBACA,CAAA;gBApKA,CAAa;YAqKb,CAAA;YACA,EAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,6BAAA,CAAA,IAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YAEA,CAAA;YACM,IAAI,CApKC,CAoKX;gBACA,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;YACA,CAAA;QAEA,CAAA;QACA,WAAA,CAAY,OAAZ,CApKc,UAAA,MAoKd;YACA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACM,MAAM,CApKC,MAAK,CAAE;gBAqKpB,MAAA,CAAA,OAAA,EAAA,CAAA;gBAEA,IApKkB,gBAoKlB,CAAA,KAAA,GAAA,KAAA,CApKqC,OAoKrC,CApKkD,OAoKlD,CAAA,MAAA,CAAA,CAAA;gBAEA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;OArKA;IAyKA,uDAAA,GAAA,UAAA,WAAA,EAxK+B,OAwK/B;QAxKA,IAA4C,gBAA5C,CAAA,YAAA,GAAA,KAAA,CAAA;QAyKI,IAAJ,gBAAA,CAAA,OAAA,GAxKqC,CAwKrC,OAAA,CAAA,YAAA,CAAA,CAAA,CAAA;QAxKA,EAAA,CAAA,CAAmD,OAAnD,IAAA,OAAA,CAAkE,aAAlE,CAAA;YAyKQ,YAAR,GAAA,IAAA,CAxKc;QAAd,EAAA,CAAA,CAA2C,IAA3C,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAyKA,YAAA,GAAA,IAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;;IAKA,8CAAA,GAAA,UAAA,QAAA,IAAA,IAAA,CAAA,SA9KkD,CA8KlD,IA9KuD,CA8KvD,QAAA,CAAA,CAAA,CAAA,CA9KwD;;;;;;;;;;;;;OA6LxD;IACA,uDAAe,GAAf,UAAA,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,WAAA,EAAA,YAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;YArLA,IAAA,gBAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAsLM,EAAN,CAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;gBACU,OAAV,GAAA,qBAAA,CAAA;YACA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YArLA,IAAA,gBAAA,CAAA,cAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YAsLA,EAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA;gBArLA,IAAA,gBAAA,CAAA,oBAAA,GAAA,CAAA,YAAA,IAAA,YAAA,IAAA,UAAA,CAAA;gBAsLA,cAAA,CAAA,OAAA,CArLwB,UAAA,MAqLxB;oBACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA;wBACA,MAAA,CAAA;oBACA,EAAA,CAAA,CAAA,CAAA,oBAAA,IAAA,MAAA,CAAA,WAAA,IAAA,WAAA,CAAA;wBACA,MAAA,CAAA;oBACA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;gBACQ,CAAR,CAAA,CAAA;YArLA,CAAA;QAsLA,CAAA;QArLA,EAAA,CAAA,CAAA,WAAA,IAAqE,WAArE,CAAA,CAAA,CAAA;YAsLA,OAAA,GAAA,OAAA,CAAA,MAAA,CAAA,UAAA,MAAA;gBACA,EAAA,CAAA,CAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,WAAA,CAAA;oBACA,MAAA,CAAA,KAAA,CAAA;gBACA,EAAA,CAAA,CAAA,WAAA,IAAA,WAAA,IAAA,MAAA,CAAA,WAAA,CAAA;oBACA,MAAA,CAAA,KAAA,CAAA;;;;;;;;;;;OAcA;IACA,yDAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA,qBAAA;QAAA;;;QAII,WAAJ,CAAA,WA1LU,CA0LV,OAAA,EAAA,WAAA,CAAA,UAzLoB,CAyLpB,CAAA;QAEI,IAAJ,gBAAA,CA1LU,WA0LV,GAAA,WAAA,CAAA,WAAA,CAAA;QAGI,IAAJ,gBA1L0B,CA0L1B,WAAA,GAAA,WAAA,CAAA,OAAA,CAAA;QACA,sEAAA;QACA,oEAAA;QACA,IAAA,gBAAA,CAAA,iBAAA,GAAA,WAAA,CAAA,mBA1LuC,GA0LvC,SAAA,GA1L0E,WA0L1E,CAAA;QACA,IAAA,gBAAA,CAAA,iBAAA,GAAA,WAAA,CAAA,mBAAA,GACiB,SADjB,GAAA,WAAA,CAAA;QAEA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,mBAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,mBAAA,CAAA,OAAA,CA1LmC;YA2LnC,IAAA,gBAAA,CAAA,gBAAA,GAAA,OAAA,KAAA,WAAA,CAAA;YACA,IAAA,gBAAA,CAAA,OAAA,GAAA,eAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,EAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,eAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA;YACA,eAAA,CA1LiB,OAAK,CAAM,UAAA,MA0L5B;gBACA,IAAA,gBAAA,CAAA,UAAA,GAAA,CAAA,MAAA,CAAA,aAAA,EAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,UAAA,CAAA,aAAA,CAAA,CAAA,CAAA;oBACA,UAAA,CAAA,aAAA,EAAA,CAAA;;;;;;;;;;;;;;;IAoBA,mDAAA,GAAA,UAAA,WAlMU,EAkMV,WAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,aAAA;QAAA;QACI,IAAJ,gBAAA,CAlMU,WAkMV,GAAA,WAAA,CAlMoC,WAkMpC,CAAA;QACI,IAAJ,gBAAA,CAlMU,WAkMV,GAlMU,WAkMV,CAlMuC,OAkMvC,CAAA;QACI,0DAAJ;QACA,2DAAA;;QAGA,IAAA,gBAAA,CAAA,mBAAA,GAlM8B,IAkM9B,GAAA,EAAA,CAAA;QACA,IAAA,gBAAA,CAAA,cAAA,GAAA,IAAA,GAlM8B,EAkM9B,CAAA;QAlMA,IAAA,gBAAA,CAAA,aAAA,GAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,mBAAA;YAoMM,IAAN,gBAAA,CAlMY,OAkMZ,GAAA,mBAAA,CAAA,OAAA,CAAA;YACM,0EAAN;YACM,IAAN,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,YAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAQ,OAAR,IAAA,OAAA,CAA2B,oBAA3B,CAAA;gBACQ,MAAR,CAAA,IAAA,mBAAA,EAAA,CAAA;YACA,IAAA,gBAAA,CAAA,gBAAA,GAAA,OAAA,KAAA,WAAA,CAAA;YACA,IAAA,gBAAA,CAAA,eAAA,GAAA,kBAAA,CAAA;YACA,EAAA,CAAA,CAAA,CAAS,mBAAT,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;gBACA,mBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,gBAAA,GAAA,qBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;oBACA,eAlMY,GAkMZ,gBAAA,CAAA,GAAA,CAAA,UAAA,CAAA,IAAA,OAAA,CAlMwB,CAmMd,aAlMa,EAiMvB,EAAA,CAAA,CAAA,CAjMuB;gBAoMvB,CAAA;;;YAIM,IAAN,gBAAA,CAlMW,UAkMX,GAAA,aAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;YACA,IAAA,gBAlMwB,CAAG,SAAS,GAkMpC,kBAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,WAAA,EAAA,OAAA,EAAA,mBAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA;YACA,IAAA,gBAAA,CAAA,MAAA,GAAA,KAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,SAAA,EAAA,eAAA,CAAA,CAAA;YAEM,yEAAN;YACA,oFAlM6D;YAmM7D,EAAA,CAAA,CAAQ,mBAAR,CAAA,WAAA,IAlM2C,iBAkM3C,CAAA,CAAA,CAAA;gBACQ,cAAR,CAAA,GAlM0B,CAkM1B,OAAA,CAAA,CAAA;YACA,CAAO;YAED,EAAN,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,aAAA,GAAA,IAAA,yBAAA,CAAA,WAAA,EAAA,WAAA,EAAA,OAAA,CAAA,CAAA;gBAEA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA;gBACA,iBAlM2B,CAkM3B,IAAA,CAAA,aAAA,CAAA,CAAA;YACM,CAAN;YACA,MAAA,CAAA,MAAA,CAAA;QAEI,CAAJ,CAAA,CAAA;QACI,iBAAJ,CAAA,OAAA,CAAA,UAAA,MAlMmB;YAmMnB,eAAA,CAAA,KAAA,CAAA,uBAAA,EAAA,MAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACM,MAAN,CAAA,MAAA,CAAA,cAlM0B,OAAA,kBAkM1B,CAlM6C,KAkM7C,CAAA,uBAAA,EAAA,MAAA,CAAA,OAlMyF,EAAE,MAkM3F,CAAA,EAlM0B,CAkM1B,CAAA,CAAA;QACA,CAAA,CAAA,CAAA;QACA,mBAAA,CAAA,OAAA,CAAA,UAAA,OAAA,IAAA,OAAA,QAAA,CAAA,OAAA,EAAA,sBAAA,CAAA,EAAA,CAAA,CAAA,CAAA;;;YAIA,mBACQ,CADR,OAAA,CAAA,UAAA,OAAA,IAAA,OAAA,WAAA,CAAA,OAAA,EAAA,sBAAA,CAAA,EAAA,CAAA,CAjMsE,CAAI;YAoM1E,SAlMW,CAAO,WAkMlB,EAAA,WAAA,CAAA,QAAA,CAAA,CAAA;QACA,CAAA,CAAA,CAAA;;;;;;;;;;;;;QAkBI,EAAJ,CAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,OAAA,EAAA,SAAA,EAAA,WAAA,CAAA,QAAA,EAAA,WAAA,CAAA,KAAA,EAAA,WAAA,CAAA,MAAA,EAAA,eAAA,CAAA,CAAA;QACA,CAAA;QAEA,mEAAA;;;;;CAlxBA;;IAm0BA;;;;OAvPA;IACA,mCAAA,WAAA,EAAA,WAAgC,EAAhC,OAAA;QAEU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QACU,IAAV,CAAA,WAAA,GAAA,WAAA,CAAA;QAGS,IAAT,CAAA,OAAA,GAAA,OAAA,CAAA;QAEA,IAAA,CAAA,OAAA,GAAA,IAAA,mBAAA,EAAA,CAAA;;;;QAEG,IAkPH,CAAA,gBAAA,GAAA,KAAA,CAAA;;;;;WAIA;;;;;IAKA,sBAAA,gDAvPG;;;;aAuPH,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;;;OAAA;IACA;;;OAGA;IACA,iDAvPW,GAuPX,UAAA,MAAA;QAAA;QAEA,EAvPM,CAAC,CAuPP,IAAA,CAAA,mBAAA,CAAA;YACQ,MAAR,CAAA;QACI,IAAI,CAvPC,OAuPT,GAAA,MAAA,CAAA;QACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA;;;;QAIA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA;;;;;;IAvPG;;;;;;;IAoQH,CAAA;IACA;;;OAGA;IACA,0CAAA,GAAA,UAAA,EAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,2CAAA,GAAA,UAAA,EAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,6CAAA,GAAA,UAAA,EAAA;;;;QApQG,IAwQH,CAxQiB,OAwQjB,CAAA,SAxQ+B,CAwQ/B,EAxQqC,CAwQrC,CAxQsC;;;;OA4QtC;;;;OAIA;;;;OAIA;;;;OAIA;;;;OAIA;;;;OAIA;IACA,0CAAA,GAAA,cAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;IACA;;;;;QAKA,IAAA,CAAA,OAtRwB,CAsRxB,OAAA,EAtRkC,CAsRlC;;;;;IAKA,yCAAA,GAAA,cAAA,CAzRG,IAyRH,CAAA,MAAA,IAAA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;;;;QAIA,CAAA;;;;OAIA;IACA,+CAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;;QAEA;;;;;;;;;;;;;;GA+BA;AACA,4BAAA,GAAA,EAAA,GAxTyB,EAAO,KAwThC;IACA,IAAA,gBAAA,CAAA,aAAA,CAxTc;IAyTd,EAAA,CAAA,CAAA,GAAA,YAAA,GAAA,CAAA,CAAA,CAAqB;QACrB,aAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,aAAN,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAQ,aAAR,CAxToB,MAwTpB,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,KAAA,GAAA,aAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;gBACA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA;YAxTA,EAAA,CAAA,CAAA,aAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gBAyTA,GAAA,CAAA,MAAA,CAxToB,GAwTpB,CAxTyB,CAAG;YAyTpB,CAAR;QACA,CAAA;IACA,CAAA;IACA,IAAA,CAAA,CAAA;QACA,aAAA,GAAA,GAAA,CAAA,GAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAM,aAAN,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAQ,aAAR,CAxToB,MAwTpB,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,KAAA,GAAA,aAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;gBACA,aAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA;YACA,EAAA,CAAA,CAAA,aAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gBACA,OAAA,GAAA,CAAA,GAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;GAGA;AACA,+BAAA,KAAA;IACA,MAAA,CAAA,CAAA,OAAA,KAAA,CAAA,CAAA,CAAA;QACA,KAAA,SAAA;;;;;AAKA,CAAA;AACA;;;;;;AAMA,CAAA;AACA;;;;;;;AAOA;;;;GAIA;;;;;;;;;;;;GAeA;AACA,+BAAA,MAAA,EAAA,QAAA,EAAA,eAAA,EAAA,YAAA;IACA,IAAA,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,GAAA,CAAA,UAAA,OAAA,IAAA,OAAA,YAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CA3UwD,CA2UxD;;;QAIA,IAAA,gBAAA,CA3UoB,MA2UpB,GAAA,EA3UoC,CAAA;QA4UpC,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA;YACA,IAAA,gBAAA,CAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA,EAAA,YAAA,CAAA,CAAA;YACA,6EAAA;YACA,gDAAA;YACA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,OAAA,CAAA,YAAA,CA3UmC,GA2UnC,0BAAA,CAAA;YACA,CAAA;QACA,CAAA,CAAA,CAAA;;;;;AAKA,CAAA;AACA;;;GAGA;AACA,8BAAA,KAAA;IA/TA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,GAAA,CAAA,KAAA,CAAA,CAAA;IAgUA,IAAA,gBAAA,CAAA,kBAAA,GAAA,IAAA,GAAA,EAAA,CAAA;IA/TA,IAAA,gBAA6C,CAAM,MAAnD,CAAA;IAgUA,MAAA,GAAA,UAAA,IAAA;QA/TA,EAAA,CAAA,CAAiD,CAAjD,IAAA,CAAA;YAgUQ,MA/TC,CAAM,IAAC,CAAI;QAgUpB,EAAA,CAAA,CAAM,OAAN,CAAA,GAAA,CAAA,IAAA,CAAA,UA/T8B,CAAI,CAAC;YAgU7B,MAAN,CA/Ta,KAAK,CA+TlB;QACA,EAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;YACA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA;QAEA,CAAA;;;;;;AAMA;;;;GAjUA;AAqUA,uBAAA,OAAA,EAAA,SApUoB;IAqUpB,EAAA,CAAA,CAAI,OApUO,CAoUX,SAAA,CApUsB,CAoUtB,CAAA;QACA,MAAA,CAAA,OAAA,CAAA,SAAA,CAAA,QAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;;;;;;AAMA;;;;GArUA;AAyUA,kBAAA,OAAA,EAAA,SAxUQ;IAyUR,EAAA,CAAA,CAAI,OAAJ,CAAA,SAAA,CAAA,CAAA,CAAA;QACA,OAAA,CAAA,SAAA,CAAA,GAxUgB,CAAQ,SAwUxB,CAAA,CAAA;IACA,CAAA;IACA,IAAI,CAAJ,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;;;;;AAMA;;;;GAzUA;AA6UA,qBAAA,OAAA,EAAA,SA5UkD;IA6UlD,EAAA,CAAA,CAAI,OAAJ,CAAA,SAAA,CAAA,CAAA,CAAA;QACA,OAAA,CAAA,SAAA,CA5Ua,MA4Ub,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IACA,IAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;;;;IAIA,CAAA;AACA,CAAA;AACA;;GAEA;AACA;;;;;;;;;;;GDlrDA;;;;;;;;;;;AA2BA;IALA;;;OAOA;IAEA,yBAAA,MAAA,EAAA,UAAA;QAAA;QACA,IAAQ,CAAC,aAAT,GAAmC,EAAnC,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,UAAA,OAAA,EAAA,OAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;OAeA;IACA,yCAAA,GAAA,UAAA,WAAA,EAAA,WAAA,EAAA,WAAA,EAPkB,IAOlB,EAAA,QAAA;QACA,IAAA,gBAAA,CAPyB,QAOzB,GAAA,WAAA,GAAA,GAAA,GAAA,IAAA,CAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,CAAA,aAAA,CACsC,QADtC,CAAA,CAAA;QAEA,EAAA,CAAA,CAAA,CAAO,OAAP,CAAA,CAAA,CAAA;YACM,IAAN,gBAPgB,CAAa,MAAM,GAAA,EAAK,CAOxC;YACM,IAAN,gBAAA,CAAA,GAAA,GAAA,CAAA,iBAAA,CAAA,gBAAA,CAAA,CAAA,QAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,MAAA,IAAA,KAP2B,CAO3B,6BAP4C,IAO5C,gEAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;YACA,CAAA;;;;;;IAMA;;;;;;;;IAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCA;IACA,qCAAA,GAAA,UAAU,WAAV,EAAA,OAAA,EAAA,QAAA,EAAA,KAAA;QACA,aAAA;QACA,EAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,CAAA,CAAA;YACA,IAAA,mCAAA,EAhCW,UAgCX,EAAA,cAAA,CAAA;YACA,IAAA,gBAAA,CAAA,IAAA,GAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;OAeA;IACA,gCAAA,GAAA,UAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA;QACI,WAvCO;QAwCX,EAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,CAAA,CAAA;;;;;IAKA,CAAA;;;;OAIA;IACA,+BAAA,GAAA,UAAA,WAAA;QAAA,4BAAA,EAAA,eAAA,CAAA;QAAA,IAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;IAAA,CAAA;;QACA;;;;;iBAKA,MAAA,CAAA,gBAAA,CA9C6C,CAAK,IA8ClD,CAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA;QACA,CAAA;;;OAAA;;;;;;CAtHA;;;;;;;;;;;;;;ODOA;IApBA,6BAAA,OAAU,EAAV,SAAA,EAAA,OAAA,EAAA,eAAA;QAAA,gCAAA,EAAA,oBAAA;QAAA,iBAwCA;QAvCU,IAAV,CAAA,OAAA,GAAA,OAAuC,CAAvC;QACU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAIU,IAAV,CAAA,OAAA,GAAA,OAAA,CAAA;QACU,IAAV,CAAA,eAAA,GAA4B,eAA5B,CAAA;QACU,IAAV,CAAA,UAAA,GAAA,EAAA,CAAqB;QACX,IAAV,CAAA,WAAA,GAAA,EAAA,CAAA;QAES,IAAT,CAAA,aAAA,GAAA,EAAA,CAAA;QAES,IAAT,CAAA,YAAS,GAAqC,KAAK,CAAnD;QAES,IAAT,CAAA,SAAA,GAAA,KAAA,CAAmE;QAW/D,IAAI,CAAC,QAAT,GAAA,KAAA,CAAA;QACI,IAAI,CAAC,UAAT,GAAA,KAAA,CAAA;QACI,IAAI,CAAC,IAAI,GAAG,CAAhB,CAAA;QAEI,IAAI,CAAC,YAAT,GAAA,IAA4B,CAAC;QACzB,IAAJ,CAAA,eAAA,GAA2B,EAA3B,CAAA;QACA,IAAM,CAAN,SAAgB,GAAG,OAAO,CAA1B,UAAA,CAAA,CAAA;QACA,IAAM,CAAN,MAAa,GAAb,OAAA,CAAwB,OAAxB,CAAA,IAAA,CAAA,CAAsC;QACtC,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,MAAA,CAAA;QACA,IAAA,CAAA,cAAA,GAAA,EAAA,CAAA;;;;QAHA,CAAA,CAAA,CAAA;IAQA,CAAA;IACA;;OAEA;IACA,uCAAA,GAAA;QACA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;QALG,CASH;IACA,CAAA;IACA;;;;;QANA,IAAA,CAAA,yBAAA,EAAA,CAAA;IAYA,CAAA;IAXA;;OAcA;IACA,0CAAA,GAAA;QAAA;QACI,EAAJ,CAAA,CAAQ,IAAR,CAAA,YAAA,CAXS;YAYH,MAAN,CAAA;QACA,IAAM,CAAN,YAAA,GAAA,IAAA,CAXU;QAYV,IAAA,gBAAwB,CAXC,SAWzB,GAXkC,IAWlC,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,MAAA,IAAA,OAAA,UAAA,CAAA,MAAA,EAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,kBAAA,GAX+B,MAAmB,CAWlD,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,kBAAA,CAAA,MAA2B,CAXC,CAW5B,CAAA;YACA,IAAA,gBAAA,CAAA,kBAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAQ,gBAAgB,CAXC,mBAWzB,GAAA,EAAA,CAAA;YACA,kBAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBAEU,EAAV,CAAA,CAAA,CAAA,kBAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;oBACA,mBAAA,CAAA,IAAA,CAXc,IAWd,CAAA,CAAA;;gBAEQ,kBAAR,CAAA,IAAA,CAAA,GAAA,KAAA,CAXqB,cAWrB,CAAA,IAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,mBAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA;;oBAGA,IAAA,gBAAA,CAAA,EAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;oBACA,mBAAA,CAAA,OAAA,CAAA,UAAA,IAAA;wBAEA,EAAA,CAXmB,IAWnB,CAAA,GAAA,aAXyB,CAAoB,MAAC,CAAI,OAAC,EAAQ,IAW3D,CAAA,CAAA;oBACA,CAAA,CAAA,CAAA;gBACA,CAAA;gBAPA,2BAAA;gBACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA;;iBAMA;YACA,CAAA;;;;QATA,IAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,cAAA,OAAA,KAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;IAeA;;OAEA;IAbA,uDAAA,GAAA;QAcA,0EAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,oBAAA,EAAA,CAAA;;;;;;;;;;;;OAYA;;;;QAhBG,MAoBH,CAAA,CAAA,OAAA,CAAA,SApB2B,CAAK,CAoBhC,SApByC,EAoBzC,OAAA,CAAA,CAAA,CAAA;;IAKA,sBAAA,0CAAA;;;;aAAA,cAvBkC,MAuBlC,CAAA,IAAA,CAvBwC,OAuBxC,CAvByD,CAAE,CAAC;;;OAAA;;;;;IA4B5D,qCA1BG,GA0BH,UAAA,EAAA,IAAA,IA1BsC,CA0BtC,WAAA,CAAA,IAAA,CA1BuD,EAAG,CA0B1D,CA1B2D,CA0B3D,CAAA;;;;;IAKA,oCAAA,GAAA,UAAA,EA7BG,IA6BH,IAAA,CAAA,UAAA,CAAA,IAAA,CA7B0C,EA6B1C,CAAA,CAAA,CA7BwD,CAAI;;;;OAiC5D;IACA,uCA/BS,GA+BT,UAAA,EAAA,IAAA,IAAA,CA/BU,aA+BV,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACA;;OAEA;IACA,kCAAA,GAAA;QACA,IAAA,CAAA,YAAA,EAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,CA/BC,IA+BT,CAAA,UA/BwB,EA+BxB,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,WAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,OAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;;;QAIA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA;IACA,CAAA;IACA;;;;;QAKA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,CAAA;IACA,CAAA;IACA;;OAEA;;;;QAIA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA;IACA,CAAA;IACA;;OAEA;IACA,mCAAA,GAAA;;;;QAnCA,IAAA,CAAA,QAAA,GAAG,KAAH,CAAA;IAwCA,CAAA;IACA;;OAEA;;;;QAIA,CAAA;IACA,CAAA;IACA;;;;;QAKA,IAAA,CAAA,IAAA,EA3C0B,CA2C1B;;;;OAIA;IACA,wCAAA,GAAA,cAAA,MAAA,CAAA,IA7C0B,CA6C1B,QAAA,CAAA,CAAA,CAAA;IACA;;OAEA;IACA,qCAAA,GAAA;QACA,EAAA,CAAA,CAAM,CAAN,IA7CW,CA6CX,UAAA,CAAA,CA7CY,CA6CZ;YACA,IAAA,CAAA,oBAAA,EAAA,CAAA;YACA,IAAA,CAAA,SAAA,EAAA,CAAA;;;;;IAKA,CAAA;;;;OAIA;;;;OAIA;;IAKA,sBAAI,0CAAJ;;;WADA;aACA,cAAA,MAAA,CAAA,IAAA,CAAA,MAtDqD,GAAG,IAsDxD,CAAA,SAAA,CAAA,CAAA,CAAA;;;OAAA;IACA;;OAEA;IACA,2CAAA,GAAA;QAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,EAAA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAS,UAAT,EAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;gBACA,EAAA,CAAA,CAAA,IAAA,IAAA,QAAA,CAAA,CAAA,CAAA;oBACA,MAAA,CAAA,IAtD2B,CAsD3B;wBACA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,aAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;gBACA,CAAA;YAEA,CAAA,CAAA,CAAA;;;;;;AA8CA;;;;GD/RA;;;;;;;;;;;;;IA6EA,CAAA;IAzDA;;;;;;;;IAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoCA,qCAAA,GAAA,UAAQ,OAAR,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,MAAA,EAAA,eAAA;QAAA,gCAAA,EAAA,oBAAA;QACA,IAAA,gBAAA,CAAA,IA3B6B,GAAG,KA2BhC,IAAA,CAAA,GAAA,MAAA,GAAA,UAAA,CAAA;QACA,IAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,QAAA,UAAA,EAAA,KAAA,OAAA,EAAA,IAAA,MAAA,EAAA,CAAA;QAEI,sEA3B2E;QA6B3E,wEAAJ;QACA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;YACA,aAAA,CAAA,QAAA,CAAA,GAAA,MAAA,CAAA;;;;IAIA,CAAA;IACA,0BAAA;AAAA,CAAA,IAAA;AACA;;GD9EA;;;;;;;;;;;;;;;;;GDYG;;;;;;;;;;;;GDAA;;;;;;;;;;"}