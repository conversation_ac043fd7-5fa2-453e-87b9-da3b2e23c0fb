{"version": 3, "file": "version.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["upgrade/version.ts"], "names": [], "mappings": ";;AAAA,mCAA8B;AAC9B,iCAAwC;AACxC,6CAAyC;AACzC,2BAA4C;AAC5C,6BAA6B;AAE7B,6CAA2C;AAC3C,kDAA4C;AAE5C,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AAGnC;IACE,MAAM,CAAC,eAAU,CAAC,gBAAM,CAAC,sBAAsB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;WACzD,eAAU,CAAC,gBAAM,CAAC,sBAAsB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;WACzD,eAAU,CAAC,gBAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;WACvD,eAAU,CAAC,gBAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;WACrD,eAAU,CAAC,gBAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;WACrD,eAAU,CAAC,gBAAM,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC,CAAC;AAC7D,CAAC;AAGD;IAEE,YAAoB,WAAmB,IAAI;QAAvB,aAAQ,GAAR,QAAQ,CAAe;QADnC,YAAO,GAAW,IAAI,CAAC;QAE7B,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI,IAAI,eAAM,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,CAAC,CAAC;IAC/C,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC;IAC7C,kBAAkB,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;IACvD,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC;IAE5C,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtE,sBAAsB,CAAC,KAAa;QAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1E,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAEtE,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEpC,MAAM,CAAC,WAAW;QAChB,IAAI,WAAW,GAAQ,IAAI,CAAC;QAE5B,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;gBAClD,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE;gBACtB,aAAa,EAAE,CAAC,GAAQ,EAAE,QAAgB;oBACxC,WAAW,GAAG,GAAG,CAAC;gBACpB,CAAC;aACF,CAAC,CAAC;YACH,EAAE,CAAC,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC1C,CAAC;gBAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,8BAA8B;QAChC,CAAC;QAGD,MAAM,UAAU,GAAG,kBAAS,CAAC,cAAc,EAAE,CAAC;QAE9C,EAAE,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,UAAU,GAAG,iBAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,mCAAmC,CAAC,WAAmB;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,4BAA4B,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAG,eAAU,CAAC,eAAe,CAAC;cACvC,IAAI,CAAC,KAAK,CAAC,iBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC;cAC5E,IAAI,CAAC;QAET,4BAA4B;QAC5B,EAAE,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YAC1C,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,cAAM,CAAC,2DAA2D,CAAC,CAAC,CAAC;YACpF,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,mFAAmF;gBACnF,cAAc;gBACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,IAAI,eAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;oBACnE,OAAO,CAAC,KAAK,CAAC,YAAI,CAAC,WAAG,CAAC,0BAAY,CAAA;;;;;WAKlC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,YAAI,CAAC,WAAG,CAAC,0BAAY,CAAA;;OAElC,CAAC,CAAC,CAAC,CAAC;YACL,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,wBAAwB;QAC7B,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,KAAK,CAAC,YAAI,CAAC,WAAG,CAAC,IAAI,GAAG,0BAAY,CAAA;;;;;;;OAOzC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,qCAAqC;YACrC,EAAE,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;gBAC1B,OAAO,CAAC,KAAK,CAAC,YAAI,CAAC,cAAM,CAAC,IAAI,GAAG,0BAAY,CAAA;;;;SAI5C,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,YAAY;QACjB,yFAAyF;QACzF,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEtC,EAAE,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACjC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC;YACd,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC9B,MAAM,CAAC,KAAK,CAAC;YACf,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpE,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;gBAElC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBAC/B,MAAM,CAAC,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC;QAED,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;CACF;AA3ID,0BA2IC"}