{"_args": [["arrify@1.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "arrify@1.0.1", "_id": "arrify@1.0.1", "_inBundle": false, "_integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "_location": "/arrify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "arrify@1.0.1", "name": "arrify", "escapedName": "arrify", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/anymatch", "/globby", "/maximatch", "/ts-node"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/arrify/-/arrify-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/arrify/issues"}, "description": "Convert a value to an array", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/arrify#readme", "keywords": ["array", "arr", "arrify", "arrayify", "convert", "value"], "license": "MIT", "name": "arrify", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/arrify.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.1"}