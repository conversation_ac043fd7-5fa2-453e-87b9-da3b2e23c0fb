{"title": "WebP image format", "description": "Image format that supports lossy and lossless compression, as well as animation and alpha transparency.", "spec": "https://developers.google.com/speed/webp/", "status": "unoff", "links": [{"url": "https://developers.google.com/speed/webp/", "title": "Official website"}, {"url": "http://antimatter15.github.io/weppy/demo.html", "title": "Polyfill for browsers with WebM support"}, {"url": "http://libwebpjs.appspot.com/", "title": "Decoder in JS"}, {"url": "http://webpjs.appspot.com/", "title": "Polyfill for browsers with or without WebM support (i.e. IE6-IE9, Safari/iOS version 6.1 and below; Firefox versions 24 and bel"}, {"url": "https://developers.google.com/speed/webp/faq#which_web_browsers_natively_support_webp", "title": "Official website FAQ - Which web browsers natively support WebP?"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p", "53": "p", "54": "p", "55": "p", "56": "p", "57": "p"}, "chrome": {"4": "n", "5": "n", "6": "p", "7": "p", "8": "p", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "p", "11": "p", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "a #1", "4.1": "a #1", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "a #1", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "p"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Both [Safari](https://www.cnet.com/news/apple-ios-macos-tests-googles-webp-graphics-to-speed-up-web/) & [Firefox](https://bugzilla.mozilla.org/show_bug.cgi?id=1294490) are experimenting with supporting WebP images.\r\n\r\nAnimated WebP images are supported in Chrome 32+ and Opera 19+.", "notes_by_num": {"1": "Partial support in older Chrome, Opera and Android refers to browsers not supporting lossless and alpha versions of WebP."}, "usage_perc_y": 73.75, "usage_perc_a": 0.27, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "webpimageformat", "chrome_id": "6471725441089536,4785074604081152", "firefox_id": "", "webkit_id": "", "shown": true}