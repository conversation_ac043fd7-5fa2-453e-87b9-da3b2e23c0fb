{"/Users/<USER>/sandbox/cli-width/index.js": {"path": "/Users/<USER>/sandbox/cli-width/index.js", "s": {"1": 1, "2": 1, "3": 1, "4": 6, "5": 1, "6": 5, "7": 5, "8": 1, "9": 4, "10": 2, "11": 2, "12": 1, "13": 3}, "b": {"1": [1, 5], "2": [1, 4], "3": [2, 2], "4": [1, 1]}, "f": {"1": 6}, "fnMap": {"1": {"name": "cli<PERSON><PERSON><PERSON>", "line": 6, "loc": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 25}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 28, "column": 1}}, "4": {"start": {"line": 7, "column": 2}, "end": {"line": 27, "column": 3}}, "5": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 45}}, "6": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 29}}, "7": {"start": {"line": 13, "column": 4}, "end": {"line": 26, "column": 5}}, "8": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 36}}, "9": {"start": {"line": 17, "column": 6}, "end": {"line": 23, "column": 7}}, "10": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 56}}, "11": {"start": {"line": 20, "column": 8}, "end": {"line": 22, "column": 9}}, "12": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 23}}, "13": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 34}}}, "branchMap": {"1": {"line": 7, "type": "if", "locations": [{"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 2}}, {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 2}}]}, "2": {"line": 12, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 4}}, {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 4}}]}, "3": {"line": 15, "type": "if", "locations": [{"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 6}}, {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 6}}]}, "4": {"line": 18, "type": "if", "locations": [{"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 8}}, {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 8}}]}}}}