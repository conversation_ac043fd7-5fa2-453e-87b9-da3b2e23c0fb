{"_args": [["contra@1.9.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "contra@1.9.4", "_id": "contra@1.9.4", "_inBundle": false, "_integrity": "sha1-9TveQtfltZhcrk2ZqNYQUm3o8o0=", "_location": "/contra", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "contra@1.9.4", "name": "contra", "escapedName": "contra", "rawSpec": "1.9.4", "saveSpec": null, "fetchSpec": "1.9.4"}, "_requiredBy": ["/dragula"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/contra/-/contra-1.9.4.tgz", "_spec": "1.9.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bevacqua.io"}, "bugs": {"url": "https://github.com/bevacqua/contra/issues"}, "dependencies": {"atoa": "1.0.0", "ticky": "1.0.1"}, "description": "Asynchronous flow control with a functional taste to it", "devDependencies": {"assert": "~1.1.0", "browserify": "10.2.4", "jshint": "~2.4.1", "jshint-stylish": "~0.1.5", "jshint-tap": "0.0.1", "mocha": "~1.17.0", "uglify-js": "2.4.23"}, "homepage": "https://github.com/bevacqua/contra", "keywords": ["a", "async", "asynchronous", "control", "flow", "generator", "promises", "q"], "license": "MIT", "main": "contra.js", "name": "contra", "repository": {"type": "git", "url": "git://github.com/bevacqua/contra.git"}, "scripts": {"build": "browserify -s contra -do dist/contra.js contra.js && uglifyjs -m -c -o dist/contra.min.js dist/contra.js", "build-shim": "browserify -do dist/contra.shim.js contra.shim.js && uglifyjs -m -c -o dist/contra.shim.min.js dist/contra.shim.js", "deploy": "npm run build && npm run build-shim && npm run test && npm run deployment", "deployment": "git add dist && npm version ${BUMP:-\"patch\"} --no-git-tag-version && git add package.json && git commit -am \"Autogenerated pre-deployment commit\" && bower version ${BUMP:-\"patch\"} && git reset HEAD~2 && git add . && git commit -am \"Release $(cat package.json | jq -r .version)\" && git push --tags && npm publish && git push", "test": "mocha --reporter tap && jshint --reporter node_modules/jshint-tap/jshint-tap.js test/*.js"}, "testling": {"browsers": {"android-browser": [4.2], "chrome": [15, 20, 25, 30, 5, "canary"], "firefox": [10, 15, 20, 25, 3.6, "nightly"], "ie": [10, 6, 7, 8, 9], "ipad": [6], "iphone": [6], "opera": [15, 16, 17, "next"], "safari": [4, 5.1, 6]}, "files": ["contra.js", "contra.shim.js", "test/*.js"], "harness": "mocha"}, "version": "1.9.4"}