{"_args": [["ms@0.7.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ms@0.7.1", "_id": "ms@0.7.1", "_inBundle": false, "_integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "_location": "/compression/ms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ms@0.7.1", "name": "ms", "escapedName": "ms", "rawSpec": "0.7.1", "saveSpec": null, "fetchSpec": "0.7.1"}, "_requiredBy": ["/compression/debug"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ms/-/ms-0.7.1.tgz", "_spec": "0.7.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "description": "Tiny ms conversion utility", "devDependencies": {"expect.js": "*", "mocha": "*", "serve": "*"}, "homepage": "https://github.com/guille/ms.js#readme", "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git://github.com/guille/ms.js.git"}, "version": "0.7.1"}