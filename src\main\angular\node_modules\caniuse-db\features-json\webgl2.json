{"title": "WebGL 2.0", "description": "Next version of WebGL. Based on OpenGL ES 3.0.", "spec": "https://www.khronos.org/registry/webgl/specs/latest/2.0/", "status": "other", "links": [{"url": "https://blog.mozilla.org/futurereleases/2015/03/03/an-early-look-at-webgl-2/", "title": "Firefox blog post"}, {"url": "https://www.khronos.org/webgl/wiki/Getting_a_WebGL_Implementation", "title": "Getting a WebGL Implementation"}], "bugs": [], "categories": ["<PERSON><PERSON>"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n d #1 #2", "26": "n d #1 #2", "27": "n d #1 #2", "28": "n d #1 #2", "29": "n d #1 #2", "30": "n d #1 #2", "31": "n d #1 #2", "32": "n d #1 #2", "33": "n d #1 #2", "34": "n d #1 #2", "35": "n d #1 #2", "36": "n d #1 #2", "37": "n d #1 #2", "38": "n d #1 #2", "39": "n d #1 #2", "40": "n d #1 #2", "41": "n d #1 #2", "42": "n d #1", "43": "n d #1", "44": "n d #1", "45": "n d #1 #5", "46": "n d #1 #5", "47": "n d #1 #5", "48": "n d #1 #5", "49": "n d #1 #5", "50": "n d #1 #5", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n d #3", "44": "n d #3", "45": "n d #3", "46": "n d #3", "47": "n d #3", "48": "n d #3", "49": "n d #3", "50": "n d #3", "51": "n d #3", "52": "n d #3", "53": "n d #3", "54": "n d #3", "55": "n d #3", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n d #4", "11": "n d #4", "TP": "n d #4"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n d #3"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Can be enabled in Firefox by setting the about:config preference webgl.enable-prototype-webgl2 to true", "2": "WebGL2 context is accessed from \"experimental-webgl2\" rather than \"webgl2\"", "3": "Can be enabled in Chrome by passing the \"--enable-unsafe-es3-apis\" flag when starting the browser through the command line", "4": "Can be enabled via the \"Experimental Features\" developer menu", "5": "Enabled by default for Nightly and Dev Edition"}, "usage_perc_y": 57.32, "usage_perc_a": 0, "ucprefix": false, "parent": "canvas", "keywords": "web gl", "ie_id": "webgl20", "chrome_id": "6694359164518400", "firefox_id": "webgl-2", "webkit_id": "specification-webgl-2", "shown": true}