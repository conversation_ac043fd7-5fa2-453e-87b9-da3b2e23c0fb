{"title": "Form validation", "description": "Method of setting required fields and field types without requiring JavaScript. This includes preventing forms from being submitted when appropriate, the `checkValidity()` method as well as support for the `:invalid`, `:valid`, and `:required` CSS pseudo-classes.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#client-side-form-validation", "status": "ls", "links": [{"url": "https://www.webplatform.org/docs/html/attributes/required", "title": "WebPlatform Docs"}, {"url": "https://webkit.org/blog/7099/html-interactive-form-validation/", "title": "WebKit Blog: HTML Interactive Form Validation"}], "bugs": [{"description": "IE10 and 11 [have a problem](http://stackoverflow.com/questions/20241415/html5-number-input-field-step-attribute-broken-in-internet-explorer-10-and-inter) validating number fields in combination with the `step` attribute and certain values"}, {"description": "In Chrome (tested in 45) inputs marked as disabled or hidden while also marked as required are [incorrectly](https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled) being considered for constraint validation. https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled"}, {"description": "[IE & Edge do not support `:valid` on `form` elements.](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/8114184/) Firefox<51 seemed to only match `:valid` on `form`s after child element values have changed from an invalid to valid state; see [bug #1285425](https://bugzilla.mozilla.org/show_bug.cgi?id=1285425)."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "a #1", "4.2-4.3": "a #1", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #3"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "a #1", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "a #1", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a #2", "11": "a #2"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to lack of notice when form with required fields is attempted to be submitted. See [WebKit bug](https://bugs.webkit.org/show_bug.cgi?id=28649).", "2": "Partial support in IE10 mobile refers to lack of warning when blocking submission.", "3": "Partial support in Opera Mini refers to only supporting the CSS pseudo classes."}, "usage_perc_y": 89.01, "usage_perc_a": 8.44, "ucprefix": false, "parent": "forms", "keywords": "", "ie_id": "", "chrome_id": "6091813840486400", "firefox_id": "", "webkit_id": "feature-html-interactive-form-validation", "shown": true}