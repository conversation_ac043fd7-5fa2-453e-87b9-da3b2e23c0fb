{"version": 3, "file": "main.spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/main.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,uBAAyB;AACzB,2BAA6B;AAE7B,oCAAiC;AAEjC,+CAA2C;AAE3C,QAAQ,CAAC,aAAa,EAAE;IACtB,IAAI,QAAgB,CAAC;IACrB,IAAI,KAAkD,CAAC;IAEvD,UAAU,CAAC;QACT,QAAQ,GAAG,0BAAW,EAAE,CAAC;QACzB,KAAK,GAAG,UAAC,QAAgB,EAAE,OAAe;YACxC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAC,CAAC;QAChF,CAAC,CAAC;QACF,KAAK,CAAC,eAAe,EAAE,oDAAoD,CAAC,CAAC;QAC7E,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QACzC,KAAK,CAAC,cAAc,EAAE,8DAGrB,CAAC,CAAC;QACH,KAAK,CAAC,SAAS,EAAE,sQAYhB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,iBAAiB,GAAW;QAC1B,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAQ,GAAK,CAAC,EAAE,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAC,CAAC;IAC3F,CAAC;IAED,EAAE,CAAC,0CAA0C,EAAE;QAC7C,WAAI,CAAC,WAAW,EAAE,IAAW,CAAC;aACzB,IAAI,CAAC,cAAM,OAAA,IAAI,CAAC,qBAAqB,CAAC,EAA3B,CAA2B,CAAC;aACvC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,EAArC,CAAqC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE,UAAC,IAAI;QACpC,KAAK,CAAC,eAAe,EAAE,2XAarB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,2CAA2C;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACxC,yCAAyC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAC5D,iCAAiC;YACjC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACxC,kCAAkC;YAClC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC5D,iCAAiC;YACjC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;YACjE,IAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC7C,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,UAAC,IAAI;QACxE,IAAM,MAAM,GAAG;YACb,IAAI,EAAE,QAAQ,GAAG,gBAAgB;YACjC,QAAQ,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBAClC,eAAe,EAAE;oBACf,sBAAsB,EAAE,IAAI;oBAC5B,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,OAAO;oBACf,WAAW,EAAE,IAAI;oBACjB,gBAAgB,EAAE,MAAM;oBACxB,MAAM,EAAE,QAAQ;iBACjB;gBACD,sBAAsB,EAAE,EAAC,0BAA0B,EAAE,IAAI,EAAC;gBAC1D,KAAK,EAAE,CAAC,SAAS,CAAC;aACnB,CAAC,CAAC;SACJ,CAAC;QAEF,WAAI,CAAC,MAAM,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACnB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,yCAAyC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAC5D,iCAAiC;YACjC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACxC,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE,UAAC,IAAI;QAC3C,KAAK,CAAC,eAAe,EAAE,sfAgBrB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,8BAA8B;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACpC,qCAAqC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAjE,CAAiE,CAAC,CAAC,OAAO,EAAE,CAAC;YAC1F,MAAM,CAAC,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,EAAxD,CAAwD,CAAC,CAAC,OAAO,EAAE,CAAC;YACjF,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE,UAAC,IAAI;QAC9D,KAAK,CAAC,eAAe,EAAE,ufAgBrB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,8BAA8B;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACpC,qCAAqC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,EAAxD,CAAwD,CAAC,CAAC,OAAO,EAAE,CAAC;YACjF,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+DAA+D,EAAE,UAAC,IAAI;QACvE,KAAK,CAAC,eAAe,EAAE,6VAYrB,CAAC,CAAC;QACJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,2CAA2C,EAAE,UAAC,IAAI;QACpD,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,KAAK,CAAC,UAAQ,CAAC,QAAK,EAAE,0GAGA,CAAC,8DAGtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,eAAe,EAAE,uaAcrB,CAAC,CAAC;QACJ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzB,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5B,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,uCAAuC,EAAE,UAAC,IAAI;QAChD,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,KAAK,CAAC,UAAQ,CAAC,QAAK,EAAE,0GAGA,CAAC,8DAGtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,eAAe,EAAE,sXAarB,CAAC,CAAC;QACJ,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAExB,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC3B,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,UAAC,IAAI;QAC1C,KAAK,CAAC,eAAe,EAAE,yZAcrB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAClD,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,UAAC,IAAI;QACzC,KAAK,CAAC,eAAe,EAAE,yZAcrB,CAAC,CAAC;QACJ,8DAA8D;QAC9D,8DAA8D;QAC9D,iCAAiC;QACjC,IAAM,cAAc,GAChB,oJAAgI,CAAC;QACrI,IAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/E,KAAK,CAAC,SAAS,EAAE,qEAC+B,gBAAkB,CAAC,CAAC;QAEpE,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACrD,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,UAAC,IAAI;QACxE,KAAK,CAAC,eAAe,EAAE,yZAcrB,CAAC,CAAC;QACJ,8DAA8D;QAC9D,8DAA8D;QAC9D,iCAAiC;QACjC,IAAM,cAAc,GAChB,iJAA6H,CAAC;QAClI,IAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/E,KAAK,CAAC,SAAS,EAAE,qEAC+B,gBAAkB,CAAC,CAAC;QAEpE,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACrD,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE,UAAC,IAAI;QAC5D,KAAK,CAAC,eAAe,EAAE,4ZAcrB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YACnE,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0DAA0D,EAAE,UAAC,IAAI;QAClE,KAAK,CAAC,eAAe,EAAE,2ZAcrB,CAAC,CAAC;QAEJ,WAAI,CAAC,QAAQ,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC;aACrB,IAAI,CAAC;YACJ,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,yCAAuC,CAAC,CAAC;YACtE,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AAEL,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\n\nimport {main} from '../src/main';\n\nimport {makeTempDir} from './test_support';\n\ndescribe('tsc-wrapped', () => {\n  let basePath: string;\n  let write: (fileName: string, content: string) => void;\n\n  beforeEach(() => {\n    basePath = makeTempDir();\n    write = (fileName: string, content: string) => {\n      fs.writeFileSync(path.join(basePath, fileName), content, {encoding: 'utf-8'});\n    };\n    write('decorators.ts', '/** @Annotation */ export var Component: Function;');\n    fs.mkdirSync(path.join(basePath, 'dep'));\n    write('dep/index.ts', `\n      export const A = 1;\n      export const B = 2;\n    `);\n    write('test.ts', `\n      import {Component} from './decorators';\n      export * from './dep';\n\n      @Component({})\n      export class Comp {\n        /**\n         * Comment that is\n         * multiple lines\n         */\n        method(x: string): void {}\n      }\n    `);\n  });\n\n  function readOut(ext: string) {\n    return fs.readFileSync(path.join(basePath, 'built', `test.${ext}`), {encoding: 'utf-8'});\n  }\n\n  it('should report error if project not found', () => {\n    main('not-exist', null as any)\n        .then(() => fail('should report error'))\n        .catch(e => expect(e.message).toContain('ENOENT'));\n  });\n\n  it('should pre-process sources', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es2015\"\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js');\n          // No helpers since decorators were lowered\n          expect(out).not.toContain('__decorate');\n          // Expand `export *` and fix index import\n          expect(out).toContain(`export { A, B } from './dep/index'`);\n          // Annotated for Closure compiler\n          expect(out).toContain('* @param {?} x');\n          // Comments should stay multi-line\n          expect(out).not.toContain('Comment that is multiple lines');\n          // Decorator is now an annotation\n          expect(out).toMatch(/Comp.decorators = \\[\\s+\\{ type: Component/);\n          const decl = readOut('d.ts');\n          expect(decl).toContain('declare class Comp');\n          const metadata = readOut('metadata.json');\n          expect(metadata).toContain('\"Comp\":{\"__symbolic\":\"class\"');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should pre-process sources using config from vinyl like object', (done) => {\n    const config = {\n      path: basePath + '/tsconfig.json',\n      contents: new Buffer(JSON.stringify({\n        compilerOptions: {\n          experimentalDecorators: true,\n          types: [],\n          outDir: 'built',\n          declaration: true,\n          moduleResolution: 'node',\n          target: 'es2015'\n        },\n        angularCompilerOptions: {annotateForClosureCompiler: true},\n        files: ['test.ts']\n      }))\n    };\n\n    main(config, {basePath})\n        .then(() => {\n          const out = readOut('js');\n          // Expand `export *` and fix index import\n          expect(out).toContain(`export { A, B } from './dep/index'`);\n          // Annotated for Closure compiler\n          expect(out).toContain('* @param {?} x');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should allow all options disabled', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": false,\n        \"module\": \"es2015\",\n        \"moduleResolution\": \"node\"\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": false,\n        \"annotationsAs\": \"decorators\",\n        \"skipMetadataEmit\": true,\n        \"skipTemplateCodegen\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js');\n          // TypeScript's decorator emit\n          expect(out).toContain('__decorate');\n          // Not annotated for Closure compiler\n          expect(out).not.toContain('* @param {?} x');\n          expect(() => fs.accessSync(path.join(basePath, 'built', 'test.metadata.json'))).toThrow();\n          expect(() => fs.accessSync(path.join(basePath, 'built', 'test.d.ts'))).toThrow();\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should allow all options disabled with metadata emit', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": false,\n        \"module\": \"es2015\",\n        \"moduleResolution\": \"node\"\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": false,\n        \"annotationsAs\": \"decorators\",\n        \"skipMetadataEmit\": false,\n        \"skipTemplateCodegen\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js');\n          // TypeScript's decorator emit\n          expect(out).toContain('__decorate');\n          // Not annotated for Closure compiler\n          expect(out).not.toContain('* @param {?} x');\n          expect(() => fs.accessSync(path.join(basePath, 'built', 'test.d.ts'))).toThrow();\n          const metadata = readOut('metadata.json');\n          expect(metadata).toContain('\"Comp\":{\"__symbolic\":\"class\"');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should allow JSDoc annotations without decorator downleveling', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true,\n        \"annotationsAs\": \"decorators\"\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n    main(basePath, {basePath}).then(() => done()).catch(e => done.fail(e));\n  });\n\n  xit('should run quickly (performance baseline)', (done) => {\n    for (let i = 0; i < 1000; i++) {\n      write(`input${i}.ts`, `\n        import {Component} from './decorators';\n        @Component({})\n        export class Input${i} {\n          private __brand: string;\n        }\n      `);\n    }\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"diagnostics\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": false,\n        \"annotationsAs\": \"decorators\",\n        \"skipMetadataEmit\": true\n      },\n      \"include\": [\"input*.ts\"]\n    }`);\n    console.time('BASELINE');\n\n    main(basePath, {basePath})\n        .then(() => {\n          console.timeEnd('BASELINE');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  xit('should run quickly (performance test)', (done) => {\n    for (let i = 0; i < 1000; i++) {\n      write(`input${i}.ts`, `\n        import {Component} from './decorators';\n        @Component({})\n        export class Input${i} {\n          private __brand: string;\n        }\n      `);\n    }\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"diagnostics\": true,\n        \"skipLibCheck\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"include\": [\"input*.ts\"]\n    }`);\n    console.time('TSICKLE');\n\n    main(basePath, {basePath})\n        .then(() => {\n          console.timeEnd('TSICKLE');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should produce valid source maps', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es2015\",\n        \"sourceMap\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js.map');\n          expect(out).toContain('\"sources\":[\"../test.ts\"]');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should accept input source maps', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es2015\",\n        \"sourceMap\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n    // Provide a file called test.ts that has an inline source map\n    // which says that it was transpiled from a file other_test.ts\n    // with exactly the same content.\n    const inputSourceMap =\n        `{\"version\":3,\"sources\":[\"other_test.ts\"],\"names\":[],\"mappings\":\"AAAA,MAAM,EAAE,EAAE,CAAC\",\"file\":\"../test.ts\",\"sourceRoot\":\"\"}`;\n    const encodedSourceMap = new Buffer(inputSourceMap, 'utf8').toString('base64');\n    write('test.ts', `const x = 3;\n//# sourceMappingURL=data:application/json;base64,${encodedSourceMap}`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js.map');\n          expect(out).toContain('\"sources\":[\"other_test.ts\"]');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it(`should accept input source maps that don't match the file name`, (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es2015\",\n        \"sourceMap\": true\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n    // Provide a file called test.ts that has an inline source map\n    // which says that it was transpiled from a file other_test.ts\n    // with exactly the same content.\n    const inputSourceMap =\n        `{\"version\":3,\"sources\":[\"other_test.ts\"],\"names\":[],\"mappings\":\"AAAA,MAAM,EAAE,EAAE,CAAC\",\"file\":\"test.ts\",\"sourceRoot\":\"\"}`;\n    const encodedSourceMap = new Buffer(inputSourceMap, 'utf8').toString('base64');\n    write('test.ts', `const x = 3;\n//# sourceMappingURL=data:application/json;base64,${encodedSourceMap}`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const out = readOut('js.map');\n          expect(out).toContain('\"sources\":[\"other_test.ts\"]');\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should expand shorthand imports for ES2015 modules', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es2015\",\n        \"module\": \"es2015\"\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const fileOutput = readOut('js');\n          expect(fileOutput).toContain(`export { A, B } from './dep/index'`);\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n  it('should expand shorthand imports for ES5 CommonJS modules', (done) => {\n    write('tsconfig.json', `{\n      \"compilerOptions\": {\n        \"experimentalDecorators\": true,\n        \"types\": [],\n        \"outDir\": \"built\",\n        \"declaration\": true,\n        \"moduleResolution\": \"node\",\n        \"target\": \"es5\",\n        \"module\": \"commonjs\"\n      },\n      \"angularCompilerOptions\": {\n        \"annotateForClosureCompiler\": true\n      },\n      \"files\": [\"test.ts\"]\n    }`);\n\n    main(basePath, {basePath})\n        .then(() => {\n          const fileOutput = readOut('js');\n          expect(fileOutput).toContain(`var index_1 = require(\"./dep/index\");`);\n          done();\n        })\n        .catch(e => done.fail(e));\n  });\n\n});\n"]}