[{"__symbolic": "module", "version": 3, "metadata": {"BoundPropertyType": {"DEFAULT": 0, "LITERAL_ATTR": 1, "ANIMATION": 2}, "BoundProperty": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "ASTWithSource"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 40, "character": 74, "context": {"typeName": "BoundPropertyType"}}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "BindingParser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../expression_parser/parser", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../ml_parser/interpolation_config", "name": "InterpolationConfig"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompilePipeSummary"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}], "getUsedPipes": [{"__symbolic": "method"}], "createDirectiveHostPropertyAsts": [{"__symbolic": "method"}], "createDirectiveHostEventAsts": [{"__symbolic": "method"}], "parseInterpolation": [{"__symbolic": "method"}], "parseInlineTemplateBinding": [{"__symbolic": "method"}], "_parseTemplateBindings": [{"__symbolic": "method"}], "parseLiteralAttr": [{"__symbolic": "method"}], "parsePropertyBinding": [{"__symbolic": "method"}], "parsePropertyInterpolation": [{"__symbolic": "method"}], "_parsePropertyAst": [{"__symbolic": "method"}], "_parseAnimation": [{"__symbolic": "method"}], "_parseBinding": [{"__symbolic": "method"}], "createElementPropertyAst": [{"__symbolic": "method"}], "parseEvent": [{"__symbolic": "method"}], "_parseAnimationEvent": [{"__symbolic": "method"}], "_parseEvent": [{"__symbolic": "method"}], "_parseAction": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}], "_reportExpressionParserErrors": [{"__symbolic": "method"}], "_checkPipes": [{"__symbolic": "method"}], "_validatePropertyOrAttributeName": [{"__symbolic": "method"}]}}, "PipeCollector": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "RecursiveAstVisitor"}, "members": {"visitPipe": [{"__symbolic": "method"}]}}, "calcPossibleSecurityContexts": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"BoundPropertyType": {"DEFAULT": 0, "LITERAL_ATTR": 1, "ANIMATION": 2}, "BoundProperty": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "ASTWithSource"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 40, "character": 74, "context": {"typeName": "BoundPropertyType"}}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}]}}, "BindingParser": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../expression_parser/parser", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../ml_parser/interpolation_config", "name": "InterpolationConfig"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../compile_metadata", "name": "CompilePipeSummary"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../parse_util", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}]}], "getUsedPipes": [{"__symbolic": "method"}], "createDirectiveHostPropertyAsts": [{"__symbolic": "method"}], "createDirectiveHostEventAsts": [{"__symbolic": "method"}], "parseInterpolation": [{"__symbolic": "method"}], "parseInlineTemplateBinding": [{"__symbolic": "method"}], "_parseTemplateBindings": [{"__symbolic": "method"}], "parseLiteralAttr": [{"__symbolic": "method"}], "parsePropertyBinding": [{"__symbolic": "method"}], "parsePropertyInterpolation": [{"__symbolic": "method"}], "_parsePropertyAst": [{"__symbolic": "method"}], "_parseAnimation": [{"__symbolic": "method"}], "_parseBinding": [{"__symbolic": "method"}], "createElementPropertyAst": [{"__symbolic": "method"}], "parseEvent": [{"__symbolic": "method"}], "_parseAnimationEvent": [{"__symbolic": "method"}], "_parseEvent": [{"__symbolic": "method"}], "_parseAction": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}], "_reportExpressionParserErrors": [{"__symbolic": "method"}], "_checkPipes": [{"__symbolic": "method"}], "_validatePropertyOrAttributeName": [{"__symbolic": "method"}]}}, "PipeCollector": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../expression_parser/ast", "name": "RecursiveAstVisitor"}, "members": {"visitPipe": [{"__symbolic": "method"}]}}, "calcPossibleSecurityContexts": {"__symbolic": "function"}}}]