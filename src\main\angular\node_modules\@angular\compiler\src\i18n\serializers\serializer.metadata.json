[{"__symbolic": "module", "version": 3, "metadata": {"Serializer": {"__symbolic": "class", "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}], "createNameMapper": [{"__symbolic": "method"}]}}, "PlaceholderMapper": {"__symbolic": "interface"}, "SimplePlaceholderMapper": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../i18n_ast", "name": "RecurseVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../i18n_ast", "name": "Message"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 47, "character": 54}]}], "toPublicName": [{"__symbolic": "method"}], "toInternalName": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}], "visitPlaceholderName": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Serializer": {"__symbolic": "class", "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}], "createNameMapper": [{"__symbolic": "method"}]}}, "PlaceholderMapper": {"__symbolic": "interface"}, "SimplePlaceholderMapper": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "../i18n_ast", "name": "RecurseVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../i18n_ast", "name": "Message"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 47, "character": 54}]}], "toPublicName": [{"__symbolic": "method"}], "toInternalName": [{"__symbolic": "method"}], "visitText": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}], "visitPlaceholderName": [{"__symbolic": "method"}]}}}}]