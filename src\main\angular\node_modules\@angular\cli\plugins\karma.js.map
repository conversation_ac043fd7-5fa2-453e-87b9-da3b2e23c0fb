{"version": 3, "file": "karma.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["plugins/karma.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,yBAAyB;AACzB,6BAA6B;AAC7B,mCAAmC;AACnC,MAAM,oBAAoB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAG/D,uEAAsF;AACtF,2EAAqE;AAErE,MAAM,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,CAAC;AAE5E,IAAI,OAAO,GAAU,EAAE,CAAC;AACxB,IAAI,SAAS,GAAG,KAAK,CAAC;AAEtB,qBAAqB,IAAY;IAC/B,IAAI,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACzC,CAAC;IAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,sCAAsC;AACtC,uBAAuB,KAAY,EAAE,QAAe,EAAE,OAAO,GAAG,KAAK;IACnE,MAAM,QAAQ,GAAG;QACf,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;KACd,CAAC;IAEF,MAAM,cAAc,GAAG,QAAQ;SAE5B,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;SAEpE,GAAG,CAAC,IAAI,IAAI,mBAAM,QAAQ,EAAK,IAAI,EAAG,CAAC,CAAC;IAE3C,mDAAmD;IACnD,uDAAuD;IACvD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACZ,KAAK,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,CAAC;IACnC,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;IAChC,CAAC;AACH,CAAC;AAED,MAAM,IAAI,GAAQ,CAAC,MAAW,EAAE,OAAY,EAAE,kBAAuB;IACnE,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3D,MAAM,UAAU,GAAuB,MAAM,CAAC,MAAM,CAAC;QACnD,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;KACf,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IAEtB,6EAA6E;IAC7E,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QACtC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAgB;YACxC,+CAA+C;YAC/C,OAAO,GAAG,OAAO,OAAO,KAAK,QAAQ,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;YACpE,gBAAgB;YAChB,oDAAoD;YACpD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC3D,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;YAElC,4BAA4B;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,CAAC;YAC3E,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAEzE,6EAA6E;YAC7E,yFAAyF;YACzF,IAAI,YAAoB,EAAE,SAAiB,CAAC;YAC5C,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC7B,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACzD,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,6EAA6E;gBAC7E,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC7D,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAExC,CAAC;YACD,8CAA8C;YAC9C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,QAAQ,GAAG,YAAY,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sBAAsB;IACtB,MAAM,aAAa,GAAG,IAAI,uCAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;IACjF,MAAM,uBAAuB,GAAG;QAC9B,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;QACvC,UAAU,EAAE,mBAAmB;QAC/B,KAAK,EAAE;YACL,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,KAAK;SACpB;KACF,CAAC;IAEF,0DAA0D;IAC1D,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QACrB,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,kDAAsB,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,8BAA8B;IAC9B,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IAC9D,MAAM,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAE5F,6EAA6E;IAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa;QAC5C,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wDAAwD;IACxD,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;IAC1C,EAAE,CAAC,CAAC,UAAU,CAAC,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,wFAAwF;IACxF,yBAAyB;IACzB,MAAM,CAAC,iBAAiB,GAAG,GAAG,SAAS,qBAAqB,CAAC;IAC7D,MAAM,CAAC,eAAe,GAAG,GAAG,SAAS,mBAAmB,CAAC;IAEzD,2BAA2B;IAC3B,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,EAAE,CAAC;IACxD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAElD,0DAA0D;IAC1D,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;IAElC,iFAAiF;IACjF,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC;IAC3B,wDAAwD;IACxD,aAAa,CAAC,MAAM,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAChD,aAAa,CAAC,MAAM,CAAC,UAAU,GAAG,mBAAmB,CAAC;IAEtD,IAAI,QAAa,CAAC;IAClB,IAAI,CAAC;QACH,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;IACpC,CAAC;IAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACd,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QACD,MAAM,CAAC,CAAC;IACV,CAAC;IAED,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI;QACpD,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAM,EAAE,QAAoB;YAC1D,SAAS,GAAG,IAAI,CAAC;YAEjB,EAAE,CAAC,CAAC,OAAO,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC;gBACnC,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,KAAU;QACjC,qDAAqD;QACrD,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1C,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,SAAS,GAAG,KAAK,CAAC;YAClB,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC9B,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,IAAI,oBAAoB,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;IAE/E,sCAAsC;IACtC,kBAAkB,CAAC,IAAI,CAAC;QACtB,QAAQ,EAAE,wBAAwB;QAClC,OAAO,EAAE,iBAAiB,GAAQ,EAAE,GAAQ;YAC1C,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;gBACnB,8CAA8C;gBAC9C,qFAAqF;gBACrF,MAAM,WAAW,GAAG;oBAClB,mCAAmC;oBACnC,sCAAsC;oBACtC,oCAAoC;oBACpC,mCAAmC;iBACpC,CAAC;gBACF,EAAE,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;oBACrB,GAAG,CAAC,GAAG,EAAE,CAAC;gBACZ,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;oBACrB,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAS;QAC3B,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,OAAO,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,oBAAoB,CAAC,CAAC;AAE3D,iEAAiE;AACjE,MAAM,YAAY,GAAQ,MAAM,CAAC,OAAY,EAAE,KAAa,EAAE,IAAS,KAAK,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAChG,YAAY,CAAC,OAAO,GAAG,EAAE,CAAC;AAE1B,wDAAwD;AACxD;IACE,MAAM,CAAC,UAAU,QAAa,EAAE,SAAc,EAAE,IAAgB;QAC9D,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,EAAE,CAAC;QACT,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,wDAAwD;AACxD,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,wBAAwB,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;IAC3C,2BAA2B,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;IACtD,8BAA8B,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;CAC5D,CAAC,CAAC"}