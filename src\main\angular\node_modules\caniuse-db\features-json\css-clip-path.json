{"title": "CSS clip-path property (for HTML)", "description": "Method of defining the visible region of an HTML element using SVG or a shape definition.", "spec": "http://www.w3.org/TR/css-masking-1/#the-clip-path", "status": "cr", "links": [{"url": "http://css-tricks.com/almanac/properties/c/clip/", "title": "CSS Tricks article"}, {"url": "http://codepen.io/dubrod/details/myNNyW/", "title": "Codepen Example Clipping an Image with a Polygon"}, {"url": "http://lab.iamvdo.me/css-svg-masks", "title": "Visual test cases"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1 #3", "48": "a #1 #3", "49": "a #1 #3", "50": "a #1 #3", "51": "a #1 #3", "52": "a #1 #3", "53": "a #1 #3", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "a x #2", "25": "a x #2", "26": "a x #2", "27": "a x #2", "28": "a x #2", "29": "a x #2", "30": "a x #2", "31": "a x #2", "32": "a x #2", "33": "a x #2", "34": "a x #2", "35": "a x #2", "36": "a x #2", "37": "a x #2", "38": "a x #2", "39": "a x #2", "40": "a x #2", "41": "a x #2", "42": "a x #2", "43": "a x #2", "44": "a x #2", "45": "a x #2", "46": "a x #2", "47": "a x #2", "48": "a x #2", "49": "a x #2", "50": "a x #2", "51": "a x #2", "52": "a x #2", "53": "a x #2", "54": "a x #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2", "62": "a #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "a x #2", "7.1": "a x #2", "8": "a x #2", "9": "a x #2", "9.1": "a x #2", "10": "a x #2", "10.1": "a x #2", "11": "a x #2", "TP": "a x #2"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #2", "16": "a x #2", "17": "a x #2", "18": "a x #2", "19": "a x #2", "20": "a x #2", "21": "a x #2", "22": "a x #2", "23": "a x #2", "24": "a x #2", "25": "a x #2", "26": "a x #2", "27": "a x #2", "28": "a x #2", "29": "a x #2", "30": "a x #2", "31": "a x #2", "32": "a x #2", "33": "a x #2", "34": "a x #2", "35": "a x #2", "36": "a x #2", "37": "a x #2", "38": "a x #2", "39": "a x #2", "40": "a x #2", "41": "a x #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "a x #2", "8": "a x #2", "8.1-8.4": "a x #2", "9.0-9.2": "a x #2", "9.3": "a x #2", "10.0-10.2": "a x #2", "10.3": "a x #2", "11": "a x #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a x #2", "4.4.3-4.4.4": "a x #2", "56": "a #2"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #2"}, "and_chr": {"59": "a #2"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a x #2"}, "samsung": {"4": "a x #2", "5": "a x #2"}, "and_qq": {"1.2": "a x #2"}, "baidu": {"7.12": "a #2"}}, "notes": "Support refers to the `clip-path` CSS property on HTML elements specifically. Support for `clip-path` in SVG is supported in all browsers with [basic SVG](#feat=svg) support.", "notes_by_num": {"1": "Partial support refers to only supporting the `url()` syntax.", "2": "Partial support refers to supporting shapes and the `url(#foo)` syntax for inline SVG, but not shapes in external SVGs.", "3": "Supports shapes behind the `layout.css.clip-path-shapes.enabled` flag"}, "usage_perc_y": 1.48, "usage_perc_a": 86.92, "ucprefix": false, "parent": "css-masks", "keywords": "clippath", "ie_id": "", "chrome_id": "", "firefox_id": "css-clip-path", "webkit_id": "", "shown": true}