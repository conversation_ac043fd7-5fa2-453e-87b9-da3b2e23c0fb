{"version": 3, "file": "entry_resolver.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/entry_resolver.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,+BAA0B;AAC1B,iCAAiC;AAEjC,yCAAkD;AAGlD,sCAAsC,QAAgC,EAChC,UAAkB,EAClB,IAAqB,EACrB,OAAmB;IACvD,mBAAmB;IACnB,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;SAC1E,IAAI,CAAC,CAAC,EAAuB;QAC5B,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;IAC/C,CAAC,CAAC,CAAC;IACL,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACd,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAC3B,CAAC;IAED,gFAAgF;IAChF,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;SACzE,GAAG,CAAC,IAAI,IAAI,IAA4B,CAAC,CAAC;IAE7C,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;YACvF,QAAQ,CAAC;QACX,CAAC;QAED,MAAM,UAAU,GAAI,IAAI,CAAC,eAAoC,CAAC,IAAI,CAAC;QACnE,MAAM,cAAc,GAAG,EAAE,CAAC,iBAAiB,CACzC,UAAU,EAAE,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC;QACrE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC;QAC9D,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACvB,MAAM,cAAc,GAAG,IAAI,iCAAsB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACzE,MAAM,WAAW,GAAG,4BAA4B,CAAC,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5F,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChB,MAAM,CAAC,WAAW,CAAC;YACrB,CAAC;YACD,QAAQ,CAAC;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,YAA+B,CAAC;QACrD,GAAG,CAAC,CAAC,MAAM,SAAS,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACzC,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;gBACtC,8DAA8D;gBAC9D,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBACtC,MAAM,WAAW,GAAG,WAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC7C,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;wBAC/B,MAAM,aAAa,GAAG,IAAI,iCAAsB,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;wBAC7E,MAAM,WAAW,GAAG,4BAA4B,CAC9C,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;wBAC5C,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;4BAChB,MAAM,CAAC,WAAW,CAAC;wBACrB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,oEAAoE;gBACpE,MAAM,MAAM,GAAG,IAAI,iCAAsB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjE,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;qBACxE,IAAI,CAAC,CAAC,EAAuB;oBAC5B,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;gBAC/C,CAAC,CAAC,CAAC;gBAEL,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBACd,MAAM,CAAC,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,IAAI,CAAC;AACd,CAAC;AAED,6BAA6B,QAAgC,EAChC,UAAkB,EAClB,IAAqB,EACrB,OAAmB;IAC9C,gFAAgF;IAChF,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;SACzE,GAAG,CAAC,IAAI,IAAI,IAA4B,CAAC,CAAC;IAE7C,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAChD,QAAQ,CAAC;QACX,CAAC;QACD,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;YAC9D,QAAQ,CAAC;QACX,CAAC;QAED,MAAM,cAAc,GAAG,EAAE,CAAC,iBAAiB,CACxC,IAAI,CAAC,eAAoC,CAAC,IAAI,EAC/C,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC;QACzD,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtF,QAAQ,CAAC;QACX,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC;QAC9D,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;YAC1E,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAmC,CAAC;YACtE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;gBACpC,4BAA4B;gBAC5B,MAAM,CAAC,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;YAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAgC,CAAC;YACnE,GAAG,CAAC,CAAC,MAAM,SAAS,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACzC,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;oBACtC,uDAAuD;oBACvD,MAAM,MAAM,GAAG,IAAI,iCAAsB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;oBACjE,MAAM,WAAW,GAAG,4BAA4B,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;oBACpF,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;wBAChB,MAAM,CAAC,WAAW,CAAC;oBACrB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IACD,MAAM,CAAC,IAAI,CAAC;AACd,CAAC;AAGD,oCAA2C,QAAgB,EAChB,IAAqB,EACrB,OAAmB;IAC5D,MAAM,MAAM,GAAG,IAAI,iCAAsB,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAEnE,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC;SACzF,GAAG,CAAC,IAAI,IAAI,IAAyB,CAAC;SACtC,MAAM,CAAC,IAAI;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,UAAyC,CAAC;QAC9D,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,wBAAwB;eACrD,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU;eAC5C,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,iBAAiB;mBAClC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,wBAAwB,CAAC,CAAC;IAC3D,CAAC,CAAC;SACD,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAkB,CAAC;SAC/C,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAEzD,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,8DAA8D;cAC1E,iEAAiE;cACjE,yBAAyB,CAAC,CAAC;IACjC,CAAC;IACD,MAAM,mBAAmB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/E,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,mBAAmB,EAAE,CAAC;IACjE,CAAC;IAED,6EAA6E;IAC7E,MAAM,IAAI,KAAK,CAAC,8DAA8D;UAC1E,iEAAiE;UACjE,yBAAyB,CAAC,CAAC;AACjC,CAAC;AAhCD,gEAgCC"}