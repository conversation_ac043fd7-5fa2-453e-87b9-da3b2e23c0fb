{"_args": [["@types/selenium-webdriver@2.53.42", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@types/selenium-webdriver@2.53.42", "_id": "@types/selenium-webdriver@2.53.42", "_inBundle": false, "_integrity": "sha1-dMt3+2BS7a/yqJhN2v2I1BnyXKw=", "_location": "/@types/selenium-webdriver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/selenium-webdriver@2.53.42", "name": "@types/selenium-webdriver", "escapedName": "@types%2fselenium-webdriver", "scope": "@types", "rawSpec": "2.53.42", "saveSpec": null, "fetchSpec": "2.53.42"}, "_requiredBy": ["/protractor", "/webdriver-js-extender"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/selenium-webdriver/-/selenium-webdriver-2.53.42.tgz", "_spec": "2.53.42", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/BillArmstrong"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kuniwak"}, {"name": "<PERSON>", "url": "https://github.com/cnishina"}], "dependencies": {}, "description": "TypeScript definitions for Selenium WebDriverJS", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/selenium-webdriver", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.1", "typesPublisherContentHash": "90291f2d0499e824d20917259b981a283efa8d5b7cf100c2d7d9a2b9a1f5c07c", "version": "2.53.42"}