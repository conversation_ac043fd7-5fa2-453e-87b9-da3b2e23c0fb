{"_args": [["base64-arraybuffer@0.1.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "base64-arraybuffer@0.1.5", "_id": "base64-arraybuffer@0.1.5", "_inBundle": false, "_integrity": "sha1-c5JncZI7Whl0etZmqlzUv5xunOg=", "_location": "/base64-arraybuffer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64-arraybuffer@0.1.5", "name": "base64-arraybuffer", "escapedName": "base64-arraybuffer", "rawSpec": "0.1.5", "saveSpec": null, "fetchSpec": "0.1.5"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz", "_spec": "0.1.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hertzen.com"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "description": "Encode/decode base64 data into ArrayBuffers", "devDependencies": {"grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1", "grunt-contrib-watch": "^0.6.1"}, "engines": {"node": ">= 0.6.0"}, "homepage": "https://github.com/niklasvh/base64-arraybuffer", "keywords": [], "licenses": [{"type": "MIT", "url": "https://github.com/niklasvh/base64-arraybuffer/blob/master/LICENSE-MIT"}], "main": "lib/base64-arraybuffer", "name": "base64-arraybuffer", "repository": {"type": "git", "url": "git+https://github.com/niklasvh/base64-arraybuffer.git"}, "scripts": {"test": "grunt nodeunit"}, "version": "0.1.5"}