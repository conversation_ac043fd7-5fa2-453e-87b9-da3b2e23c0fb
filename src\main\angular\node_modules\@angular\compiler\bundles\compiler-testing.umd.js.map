{"version": 3, "file": "compiler-testing.umd.js", "sources": ["../../../../packages/compiler/testing/index.ts", "../../../../packages/compiler/testing/src/testing.ts", "../../../../packages/compiler/testing/src/metadata_overrider.ts", "../../../../packages/compiler/testing/src/pipe_resolver_mock.ts", "../../../../packages/compiler/testing/src/ng_module_resolver_mock.ts", "../../../../packages/compiler/testing/src/directive_resolver_mock.ts", "../../../../packages/compiler/testing/src/schema_registry_mock.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the compiler/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all APIs of the compiler package.\n *\n * <div class=\"callout is-critical\">\n *   <header>Unstable APIs</header>\n *   <p>\n *     All compiler apis are currently considered experimental and private!\n *   </p>\n *   <p>\n *     We expect the APIs in this package to keep on changing. Do not rely on them.\n *   </p>\n * </div>\n */\nexport * from './schema_registry_mock';\nexport * from './directive_resolver_mock';\nexport * from './ng_module_resolver_mock';\nexport * from './pipe_resolver_mock';\n\nimport {createPlatformFactory, ModuleWithComponentFactories, Injectable, CompilerOptions, COMPILER_OPTIONS, CompilerFactory, ComponentFactory, NgModuleFactory, Injector, NgModule, Component, Directive, Pipe, Type, PlatformRef, ɵstringify} from '@angular/core';\nimport {MetadataOverride, ɵTestingCompilerFactory as TestingCompilerFactory, ɵTestingCompiler as TestingCompiler} from '@angular/core/testing';\nimport {platformCoreDynamic, JitCompiler, DirectiveResolver, NgModuleResolver, PipeResolver, CompileMetadataResolver} from '@angular/compiler';\nimport {MockDirectiveResolver} from './directive_resolver_mock';\nimport {MockNgModuleResolver} from './ng_module_resolver_mock';\nimport {MockPipeResolver} from './pipe_resolver_mock';\nimport {MetadataOverrider} from './metadata_overrider';\n\n\nexport class TestingCompilerFactoryImpl implements TestingCompilerFactory {\n  constructor(private _compilerFactory: CompilerFactory) {}\n\n  createTestingCompiler(options: CompilerOptions[]): TestingCompiler {\n    const compiler = <JitCompiler>this._compilerFactory.createCompiler(options);\n    return new TestingCompilerImpl(\n        compiler, compiler.injector.get(MockDirectiveResolver),\n        compiler.injector.get(MockPipeResolver), compiler.injector.get(MockNgModuleResolver),\n        compiler.injector.get(CompileMetadataResolver));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: CompilerFactory, },\n];\n}\n\nexport class TestingCompilerImpl implements TestingCompiler {\n  private _overrider = new MetadataOverrider();\n  constructor(\n      private _compiler: JitCompiler, private _directiveResolver: MockDirectiveResolver,\n      private _pipeResolver: MockPipeResolver, private _moduleResolver: MockNgModuleResolver,\n      private _metadataResolver: CompileMetadataResolver) {}\n  get injector(): Injector { return this._compiler.injector; }\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    return this._compiler.compileModuleSync(moduleType);\n  }\n\n  compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    return this._compiler.compileModuleAsync(moduleType);\n  }\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    return this._compiler.compileModuleAndAllComponentsSync(moduleType);\n  }\n\n  compileModuleAndAllComponentsAsync<T>(moduleType: Type<T>):\n      Promise<ModuleWithComponentFactories<T>> {\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType);\n  }\n\n  getNgContentSelectors(component: Type<any>): string[] {\n    return this._compiler.getNgContentSelectors(component);\n  }\n\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> {\n    return this._compiler.getComponentFactory(component);\n  }\n\n  checkOverrideAllowed(type: Type<any>) {\n    if (this._compiler.hasAotSummary(type)) {\n      throw new Error(`${ɵstringify(type)} was AOT compiled, so its metadata cannot be changed.`);\n    }\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this.checkOverrideAllowed(ngModule);\n    const oldMetadata = this._moduleResolver.resolve(ngModule, false);\n    this._moduleResolver.setNgModule(\n        ngModule, this._overrider.overrideMetadata(NgModule, oldMetadata, override));\n  }\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.checkOverrideAllowed(directive);\n    const oldMetadata = this._directiveResolver.resolve(directive, false);\n    this._directiveResolver.setDirective(\n        directive, this._overrider.overrideMetadata(Directive, oldMetadata !, override));\n  }\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.checkOverrideAllowed(component);\n    const oldMetadata = this._directiveResolver.resolve(component, false);\n    this._directiveResolver.setDirective(\n        component, this._overrider.overrideMetadata(Component, oldMetadata !, override));\n  }\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.checkOverrideAllowed(pipe);\n    const oldMetadata = this._pipeResolver.resolve(pipe, false);\n    this._pipeResolver.setPipe(pipe, this._overrider.overrideMetadata(Pipe, oldMetadata, override));\n  }\n  loadAotSummaries(summaries: () => any[]) { this._compiler.loadAotSummaries(summaries); }\n  clearCache(): void { this._compiler.clearCache(); }\n  clearCacheFor(type: Type<any>) { this._compiler.clearCacheFor(type); }\n}\n\n/**\n * Platform for dynamic tests\n *\n * @experimental\n */\nexport const platformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef =\n    createPlatformFactory(platformCoreDynamic, 'coreDynamicTesting', [\n      {\n        provide: COMPILER_OPTIONS,\n        useValue: {\n          providers: [\n            MockPipeResolver,\n            {provide: PipeResolver, useExisting: MockPipeResolver},\n            MockDirectiveResolver,\n            {provide: DirectiveResolver, useExisting: MockDirectiveResolver},\n            MockNgModuleResolver,\n            {provide: NgModuleResolver, useExisting: MockNgModuleResolver},\n          ]\n        },\n        multi: true\n      },\n      {provide: TestingCompilerFactory, useClass: TestingCompilerFactoryImpl}\n    ]);\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵstringify as stringify} from '@angular/core';\nimport {MetadataOverride} from '@angular/core/testing';\n\ntype StringMap = {\n  [key: string]: any\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n      metadataClass: {new (options: T): C;}, oldMetadata: C, override: MetadataOverride<T>): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => props[prop] = (<any>oldMetadata)[prop]);\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (removeValue instanceof Array) {\n      removeValue.forEach(\n          (value: any) => { removeObjects.add(_propHashKey(prop, value, references)); });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (propValue instanceof Array) {\n      metadata[prop] = propValue.filter(\n          (value: any) => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && propValue instanceof Array) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  const replacer = (key: any, value: any) => {\n    if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, PipeResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, Pipe, Type} from '@angular/core';\n\n\nexport class MockPipeResolver extends PipeResolver {\n  private _pipes = new Map<Type<any>, Pipe>();\n\n  constructor(private _injector: Injector, refector: CompileReflector) { super(refector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(pipe: Type<any>) { this._compiler.clearCacheFor(pipe); }\n\n  /**\n   * Overrides the {@link Pipe} for a pipe.\n   */\n  setPipe(type: Type<any>, metadata: Pipe): void {\n    this._pipes.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link Pipe} for a pipe:\n   * - Set the {@link Pipe} to the overridden view when it exists or fallback to the\n   * default\n   * `PipeResolver`, see `setPipe`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): Pipe {\n    let metadata = this._pipes.get(type);\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound) !;\n    }\n    return metadata;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, NgModuleResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, NgModule, Type} from '@angular/core';\n\n\nexport class MockNgModuleResolver extends NgModuleResolver {\n  private _ngModules = new Map<Type<any>, NgModule>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  /**\n   * Overrides the {@link NgModule} for a module.\n   */\n  setNgModule(type: Type<any>, metadata: NgModule): void {\n    this._ngModules.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link NgModule} for a module:\n   * - Set the {@link NgModule} to the overridden view when it exists or fallback to the\n   * default\n   * `NgModuleResolver`, see `setNgModule`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): NgModule {\n    return this._ngModules.get(type) || super.resolve(type, throwIfNotFound) !;\n  }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CompileReflector, DirectiveResolver} from '@angular/compiler';\nimport {Compiler, Component, Directive, Injectable, Injector, Provider, Type, resolveForwardRef, ɵViewMetadata as ViewMetadata} from '@angular/core';\n\n\n\n/**\n * An implementation of {@link DirectiveResolver} that allows overriding\n * various properties of directives.\n */\n\nexport class MockDirectiveResolver extends DirectiveResolver {\n  private _directives = new Map<Type<any>, Directive>();\n  private _providerOverrides = new Map<Type<any>, any[]>();\n  private _viewProviderOverrides = new Map<Type<any>, any[]>();\n  private _views = new Map<Type<any>, ViewMetadata>();\n  private _inlineTemplates = new Map<Type<any>, string>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\n\n  resolve(type: Type<any>): Directive;\n  resolve(type: Type<any>, throwIfNotFound: true): Directive;\n  resolve(type: Type<any>, throwIfNotFound: boolean): Directive|null;\n  resolve(type: Type<any>, throwIfNotFound = true): Directive|null {\n    let metadata = this._directives.get(type) || null;\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound);\n    }\n    if (!metadata) {\n      return null;\n    }\n\n    const providerOverrides = this._providerOverrides.get(type);\n    const viewProviderOverrides = this._viewProviderOverrides.get(type);\n\n    let providers = metadata.providers;\n    if (providerOverrides != null) {\n      const originalViewProviders: Provider[] = metadata.providers || [];\n      providers = originalViewProviders.concat(providerOverrides);\n    }\n\n    if (metadata instanceof Component) {\n      let viewProviders = metadata.viewProviders;\n      if (viewProviderOverrides != null) {\n        const originalViewProviders: Provider[] = metadata.viewProviders || [];\n        viewProviders = originalViewProviders.concat(viewProviderOverrides);\n      }\n\n      let view = this._views.get(type) || metadata;\n      let animations = view.animations;\n      let templateUrl: string|undefined = view.templateUrl;\n\n      let inlineTemplate = this._inlineTemplates.get(type);\n      if (inlineTemplate) {\n        templateUrl = undefined;\n      } else {\n        inlineTemplate = view.template;\n      }\n\n      return new Component({\n        selector: metadata.selector,\n        inputs: metadata.inputs,\n        outputs: metadata.outputs,\n        host: metadata.host,\n        exportAs: metadata.exportAs,\n        moduleId: metadata.moduleId,\n        queries: metadata.queries,\n        changeDetection: metadata.changeDetection,\n        providers: providers,\n        viewProviders: viewProviders,\n        entryComponents: metadata.entryComponents,\n        template: inlineTemplate,\n        templateUrl: templateUrl,\n        animations: animations,\n        styles: view.styles,\n        styleUrls: view.styleUrls,\n        encapsulation: view.encapsulation,\n        interpolation: view.interpolation\n      });\n    }\n\n    return new Directive({\n      selector: metadata.selector,\n      inputs: metadata.inputs,\n      outputs: metadata.outputs,\n      host: metadata.host,\n      providers: providers,\n      exportAs: metadata.exportAs,\n      queries: metadata.queries\n    });\n  }\n\n  /**\n   * Overrides the {@link Directive} for a directive.\n   */\n  setDirective(type: Type<any>, metadata: Directive): void {\n    this._directives.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  setProvidersOverride(type: Type<any>, providers: Provider[]): void {\n    this._providerOverrides.set(type, providers);\n    this._clearCacheFor(type);\n  }\n\n  setViewProvidersOverride(type: Type<any>, viewProviders: Provider[]): void {\n    this._viewProviderOverrides.set(type, viewProviders);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Overrides the {@link ViewMetadata} for a component.\n   */\n  setView(component: Type<any>, view: ViewMetadata): void {\n    this._views.set(component, view);\n    this._clearCacheFor(component);\n  }\n  /**\n   * Overrides the inline template for a component - other configuration remains unchanged.\n   */\n  setInlineTemplate(component: Type<any>, template: string): void {\n    this._inlineTemplates.set(component, template);\n    this._clearCacheFor(component);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\nfunction flattenArray(tree: any[], out: Array<Type<any>|any[]>): void {\n  if (tree == null) return;\n  for (let i = 0; i < tree.length; i++) {\n    const item = resolveForwardRef(tree[i]);\n    if (Array.isArray(item)) {\n      flattenArray(item, out);\n    } else {\n      out.push(item);\n    }\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementSchemaRegistry} from '@angular/compiler';\nimport {SchemaMetadata, SecurityContext} from '@angular/core';\n\nexport class MockSchemaRegistry implements ElementSchemaRegistry {\n  constructor(\n      public existingProperties: {[key: string]: boolean},\n      public attrPropMapping: {[key: string]: string},\n      public existingElements: {[key: string]: boolean}, public invalidProperties: Array<string>,\n      public invalidAttributes: Array<string>) {}\n\n  hasProperty(tagName: string, property: string, schemas: SchemaMetadata[]): boolean {\n    const value = this.existingProperties[property];\n    return value === void 0 ? true : value;\n  }\n\n  hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean {\n    const value = this.existingElements[tagName.toLowerCase()];\n    return value === void 0 ? true : value;\n  }\n\n  allKnownElementNames(): string[] { return Object.keys(this.existingElements); }\n\n  securityContext(selector: string, property: string, isAttribute: boolean): SecurityContext {\n    return SecurityContext.NONE;\n  }\n\n  getMappedPropName(attrName: string): string { return this.attrPropMapping[attrName] || attrName; }\n\n  getDefaultComponentElementName(): string { return 'ng-component'; }\n\n  validateProperty(name: string): {error: boolean, msg?: string} {\n    if (this.invalidProperties.indexOf(name) > -1) {\n      return {error: true, msg: `Binding to property '${name}' is disallowed for security reasons`};\n    } else {\n      return {error: false};\n    }\n  }\n\n  validateAttribute(name: string): {error: boolean, msg?: string} {\n    if (this.invalidAttributes.indexOf(name) > -1) {\n      return {\n        error: true,\n        msg: `Binding to attribute '${name}' is disallowed for security reasons`\n      };\n    } else {\n      return {error: false};\n    }\n  }\n\n  normalizeAnimationStyleProperty(propName: string): string { return propName; }\n  normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string|number):\n      {error: string, value: string} {\n    return {error: null !, value: val.toString()};\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["NgModuleResolver", "DirectiveResolver", "PipeResolver", "COMPILER_OPTIONS", "createPlatformFactory", "platformCoreDynamic", "<PERSON><PERSON>", "Component", "Directive", "NgModule", "ɵstringify", "CompilerFactory", "Injectable", "CompileMetadataResolver", "Compiler", "tslib_1.__extends", "SecurityContext"], "mappings": ";;;;;;AOAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;;;;;;;;AD7IP,IAAA,kBAAA,IAAA,YAAA;IACA,SAAA,kBAAA,CAAA,kBAAA,EAAA,eAA6C,EAA7C,gBAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA;QAAA,IAAA,CAAA,kBAAA,GAAA,kBAAA,CAAA;QAEA,IAAA,CAAA,eAAA,GAAA,eAAA,CAAA;QACI,IAAJ,CAAA,gBAAA,GAAA,gBAAA,CAAA;QACI,IAAJ,CAAA,iBAA0B,GAA1B,iBAAA,CAAA;QACA,IAAA,CAAA,iBAAA,GAAA,iBAAA,CAAA;KAEA;IACA,kBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA,QAAA,EAAA,OAAA,EAAA;QACI,IAAJ,KAAA,GAAA,IAAA,CAAA,kBAAA,CAA0C,QAA1C,CAAA,CAAA;QACA,OAAA,KAAA,KAAA,KAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;KAEA,CAAA;IAEE,kBAAF,CAAA,SAAA,CAAA,UAAA,GAAE,UAAF,OAAA,EAAA,WAAA,EAAA;QACI,IAAJ,KAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA;QACA,OAAA,KAAA,KAAA,KAAA,CAAA,GAAA,IAAA,GAAA,KAAA,CAAA;KAEA,CAAA;IAEE,kBAAF,CAAA,SAAA,CAAA,oBAAA,GAAE,YAAF,EAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CAAA;IAEE,kBAAF,CAAA,SAAA,CAAA,eAAA,GAAE,UAAgB,QAAlB,EAAA,QAAA,EAAA,WAAA,EAAA;QACI,OAAJgB,6BAAA,CAAA,IAA+B,CAA/B;KACA,CAAA;IACA,kBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,QAAA,EAAA,EAAA,OAAA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,IAAA,QAAA,CAAA,EAAA,CAAA;IAAA,kBAAA,CAAA,SAAA,CAAA,8BAAA,GAAA,YAAA,EAAA,OAAA,cAAA,CAAA,EAAA,CAAA;IACA,kBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAc,IAAd,EAAA;QACA,IAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA;YACA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,uBAAA,GAAA,IAAA,GAAA,sCAAA,EAAA,CAAA;SAEA;aACA;YACM,OAAO,EAAb,KAAA,EAAA,KAAA,EAAA,CAAA;SACA;KACA,CAAA;IACA,kBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA;YAAA,OAAA;gBACA,KAAc,EAAd,IAAA;gBACA,GAAA,EAAA,wBAAA,GAAA,IAAA,GAAA,sCAAA;aACA,CAAA;SAEA;aACA;YAEA,OAAA,EAAA,KAAA,EAAyB,KAAzB,EAAgC,CAAhC;SACA;KACA,CAAA;;ID9DA,kBAAA,CAAA,SAAA,CAAA,4BAAA,GAAA,UAAA,aAAA,EAAA,gBAAA,EAAA,GAAA,EAAA;;;;CCeA,EAAA,CAAA,CAAA;;;;;;;;;;;;ADIA,IAAA,qBAAA,IAAA,UAAA,MAAA,EAAA;IAAAD,SAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;IACA,SAAA,qBAAA,CAAA,SAAA,EAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GACU,MADV,CAAA,IAAA,CAAA,IAAA,EACA,SAAA,CAAA,IADA,IAAA,CAeA;QAbU,KAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAEA,KAAA,CAAA,WAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QAEc,KAAd,CAAA,kBAAA,GAAkD,IAAlD,GAAA,EAA2D,CAAC;QAE5D,KAAA,CAAA,sBAAA,GAAA,IAAA,GAAA,EAAgE,CAAhE;QAKA,KAAU,CAAV,MAAA,GAAA,IAAA,GAAA,EAAA,CAAA;QACI,KAAI,CAAR,gBAAA,GAAA,IAAA,GAAmC,EAAnC,CAAA;;KACA;IACA,MAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;QAAA,GAAA,EAAA,YAAA,EAAA,OAAuB,IAAvB,CAAA,SAAqC,CAArC,GAAA,CAAAD,sBAAA,CAAA,CAAoD,EAAE;;;KAAtD,CAAA,CAAsD;IACtD,qBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,SAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,aAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA;IACA,qBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,IAAR,EAAA,eAAA,EAAA;QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAA,GAAA,IAAA,CAAA,EAAA;QACA,IAAM,QAAN,GAAiB,IAAjB,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;QACA,IAAA,CAAA,QAAA,EAAA;YAEA,QAAA,GAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,eAAsD,CAAtD,CAAA;SACA;QAEI,IAAI,CAAR,QAAiB,EAAjB;YACQ,OAAR,IAAA,CAAA;SACA;QACA,IAAA,iBAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,qBAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QAEI,IAAI,SAAR,GAAA,QAA4B,CAA5B,SAAA,CAAuC;QACvC,IAAM,iBAAiB,IAAvB,IAAA,EAAA;YACM,IAAN,qBAAA,GAAA,QAAA,CAAA,SAAA,IAAA,EAAA,CAAA;YACA,SAAA,GAAA,qBAAA,CAAkD,MAAlD,CAAA,iBAAA,CAAA,CAA4E;SAC5E;QACA,IAAA,QAAA,YAAAP,uBAAA,EAAA;YAEM,IAAI,aAAV,GAAA,QAAA,CAAA,aAAA,CAAA;YACM,IAAI,qBAAV,IAAA,IAAuC,EAAvC;gBACU,IAAV,qBAAA,GAA0D,QAA1D,CAAA,aAAA,IAAA,EAAA,CAAA;gBAEU,aAAV,GAAA,qBAAA,CAAgD,MAAhD,CAAA,qBAAA,CAAA,CAAA;aACA;YACA,IAAQ,IAAR,GAAA,IAAmB,CAAnB,MAAA,CAAA,GAAA,CAA+B,IAA/B,CAAA,IAAA,QAAA,CAAA;YACA,IAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAAA;YAAA,IAAA,WAAA,GAAA,IAAA,CAAA,WAAA,CAAA;YACA,IAAQ,cAAc,GAAG,IAAI,CAAC,gBAA9B,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;YACA,IAAA,cAAA,EAAA;gBAEA,WAAA,GAAA,SAAA,CAAA;aACA;iBACA;gBACQ,cAAR,GAAyB,IAAzB,CAAA,QAAA,CAAA;aACA;YACA,OAAA,IAAAA,uBAAA,CAAA;gBACQ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,MAAR,EAAA,QAAA,CAAyB,MAAzB;gBACQ,OAAR,EAAA,QAAyB,CAAzB,OAAiC;gBACzB,IAAR,EAAA,QAAA,CAAA,IAAA;gBACQ,QAAR,EAAA,QAAA,CAAA,QAAA;gBACQ,QAAR,EAAA,QAAA,CAAA,QAAA;gBACQ,OAAR,EAAA,QAAA,CAAA,OAAA;gBACQ,eAAR,EAAA,QAAA,CAAA,eAAA;gBACQ,SAAR,EAAA,SAAA;gBACQ,aAAa,EAArB,aAAA;gBACQ,eAAe,EAAvB,QAAiC,CAAjC,eAAA;gBACQ,QAAR,EAAA,cAAA;gBACQ,WAAR,EAAqB,WAArB;gBACA,UAAA,EAAA,UAAA;gBACA,MAAA,EAAA,IAAA,CAAA,MAAA;gBAEA,SAAA,EAAA,IAAA,CAAA,SAAA;gBACA,aAAA,EAAyB,IAAzB,CAAA,aAAA;gBACA,aAAuB,EAAvB,IAA6B,CAA7B,aAAA;aACA,CAAA,CAAA;SACA;QACA,OAAA,IAAAC,uBAAA,CAAA;YACM,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,MAAN,EAAA,QAAA,CAAuB,MAAvB;YACA,OAAA,EAAA,QAAA,CAAA,OAAA;YACA,IAAA,EAAA,QAAA,CAAA,IAAA;;;;SAKA,CAAA,CAAA;KACA,CAAA;;;;IAKA,qBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,IAAA,EAAA,QAA2B,EAA3B;QACI,IAAI,CAAC,WAAT,CAAA,GAAwB,CAAxB,IAA6B,EAA7B,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;KAEA,CAAA;IACA,qBAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,UAAA,IAAA,EAAA,SAAoC,EAApC;QACI,IAAI,CAAC,kBAAT,CAA4B,GAA5B,CAAA,IAAA,EAAA,SAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;;;;QAKA,IAAU,CAAV,cAAkD,CAAlD,IAAA,CAAA,CAAA;KACA,CAAA;;;;;;QAMA,IAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA;KACA,CAAA;;;;IAGA,qBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,SAAA,EAAA,QAAA,EAAA;QACA,IAAQ,CAAR,gBAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA;;IAEO,OAAP,qBAAA,CAAA;CAAA,CAvHAP,mCAAA,CAuHA,CAAA,CAAA;AACA,qBAAA,CAAA,UAAA,GAAA;IACA,EAAC,IAAI,EAAEW,wBAAP,EAAA;CACC,CAAC;;AD7IF,qBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;AAeA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAAAG,SAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;;QAMA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;;QADA,KAAA,CAAA,UAAA,GAAA,IAAiD,GAAjD,EAAA,CAAA;;KACA;;;;;;;;;;;;;;;;QAiBA,OAAA,IAAA,CAAA,UAAA,CAAiB,GAA0B,CAA3C,IAAA,CAAA,IAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,eAAA,CAAA,CAAA;KACA,CAAA;IACA,MAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAAD,sBAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;IAEO,OAAP,oBAAA,CAAA;CAAA,CA3BAd,kCAAA,CA2BA,CAAA,CAAA;AACA,oBAAe,CAAf,UAAA,GAAA;IACA,EAAC,IAAI,EAAEY,wBAAP,EAAA;CACC,CAAC;;AD7CF,oBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;AAeA,IAAA,gBAAA,IAAA,UAAA,MAAA,EAAA;IAAAG,SAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAEE,SAAF,gBAAA,CAAA,SAAA,EAAA,QAAA,EAAA;QAAE,IAAF,KAAA,GAEA,MAFA,CAAA,IAAA,CAAA,IAAA,EAEA,QAAA,CAAwC,IAFxC,IAAA,CAAA;;;;;IAOE,MAAF,CAAA,cAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,WAAA,EAAA;QAAA,GAAA,EAAE,YAAF,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAAD,sBAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IACA,gBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAe,IAAI,EAAnB,EAAA,IAAA,CAAA,SAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;IAeA,gBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,IAAA,EAAA,eAAA,EAAA;QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAA,GAAA,IAAA,CAAA,EAAA;QACA,IAAA,QAAA,GAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;;YACA,QAAA,GAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,eAAA,CAAA,CAAA;SACA;QACA,OAAA,QAAA,CAAA;;IAEO,OAAP,gBAAA,CAAA;CAAA,CA/BAZ,8BAAA,CA+BA,CAAA,CAAA;AACA,gBAAA,CAAA,UAAA,GAAA;IACA,EAAC,IAAI,EAAEU,wBAAP,EAAA;CACC,CAAC;;ADjDF,gBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;KAuBA;;;;;IAOA,iBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,aAAjB,EAAA,WAAA,EAAA,QAAA,EAAA;QACA,IAAA,KAAA,GAAA,EAAkB,CAAC;QACnB,IAAA,WAAA,EAAA;YACA,WAAA,CAAA,WAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,KAAA,CAAA,IAAA,CAAA,GAAA,WAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SACA;QACA,IAAA,QAAA,CAAA,GAAA,EAAA;YACQ,IAAR,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,GAAA,EAAA;gBACA,MAAA,IAAoB,KAApB,CAA0B,4BAA1B,GAAAF,wBAAA,CAAA,aAAA,CAAA,GAAA,oBAAA,CAAA,CAAA;aACA;YACQ,WAAR,CAAoB,KAApB,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA;SACA;QACA,IAAA,QAAA,CAAA,MAAA,EAAA;YACA,cAAA,CAAA,KAA4B,EAA5B,QAAA,CAAA,MAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA;SACA;QACA,IAAA,QAAA,CAAA,GAAA,EAAA;YAEA,WAAA,CAAwB,KAAxB,EAAA,QAAA,CAAwD,GAAxD,CAAA,CAAA;SACA;QACA,OAAa,IAAI,aAAjB,CAAA,KAAA,CAAA,CAAA;KACA,CAAA;IACA,OAAA,iBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,SAAA,cAAA,CAAkB,QACR,EADV,MAAA,EAAA,UAAA,EAAA;IAEA,IAAA,aAAA,GAAA,IAAA,GAAA,EAAA,CAAA;IAAA,IAAA,OAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,WAAmB,GAAnB,MAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,WAAA,YAAA,KAAA,EAAA;YACA,WAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA,EAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SAEO;aACP;YACQ,aAAR,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,WAAA,EAAA,UAAA,CAAA,CAAA,CAAA;SACA;KAEA,CAAA;IAVA,KAAA,IAAA,IAAA,IAAA,MAAA,EAAA;QAAA,OAAA,CAAA,IAAA,CAAA,CAAA;KAUA;IAAA,IAAA,OAAA,GAAA,UAAA,IAAA,EAAA;QACA,IAAA,SAAA,GAAA,QAA4B,CAA5B,IAAA,CAAA,CAAA;QACA,IAAA,SAAA,YAAyB,KAAzB,EAAA;YACA,QAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,MAAA,CAAA,UAAA,KAAA,EAAA,EAAA,OAAA,CAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SACK;aACL;YACA,IAAA,aAAA,CAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA,EAAA;gBAEA,QAAA,CAAA,IAAwC,CAAxC,GAAA,SAAA,CAAA;aACA;SACA;KACA,CAAA;IAXA,KAAA,IAAA,IAAA,IAAA,QAAA,EAAA;QAAA,OAAA,CAAA,IAAA,CAAA,CAAA;KAWA;CACA;AACA,SAAA,WAAA,CAAe,QAAQ,EAAvB,GAAA,EAAA;IACA,KAAK,IAAL,IAAA,IAAA,GAAA,EAAA;QAAA,IAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,SAAA,GAAoB,QAApB,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,SAAA,IAAA,IAAA,IAAA,SAAA,YAAA,KAAA,EAAA;YACA,QAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA;SACA;aAEA;YACA,QAAA,CAAA,IAAA,CAAwB,GAAxB,QAAA,CAAA;SACA;KACG;CACF;AAED,SAAA,WAAA,CAAA,QAAA,EAAA,GAAA,EAAA;IACE,KAAF,IAAA,IAAA,IAAA,GAAA,EAAA;QACI,QAAJ,CAAA,IAAA,CAAA,GAAA,GAAA,CAAyB,IAAzB,CAAA,CAAA;KACA;CACA;AACA,SAAA,YAAA,CAAA,QAAA,EAAA,SAAA,EAAA,UAAA,EAAA;IACA,IAAA,QAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAA;QAEA,IAAA,OAAA,KAAA,KAAA,UAAA,EAAuC;YACvC,KAAA,GAAA,mBAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;SAEA;QACM,OAAN,KAAA,CAAA;KACA,CAAA;IACA,OAAA,QAAA,GAAA,GAAA,GAAqB,IAAI,CAAC,SAA1B,CAAA,SAA6C,EAAE,QAA/C,CAAA,CAAA;CACA;AACA,SAAA,mBAAA,CAAA,GAAA,EAAA,UAAA,EAAA;IACE,IAAF,EAAA,GAAW,UAAX,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;IACA,IAAA,CAAA,EAAA,EAAA;QAGA,EAAA,GAAA,EAAA,GAAAA,wBAAA,CAAA,GAAA,CAAA,GAAA,gBAAA,EAAA,CAAA;QACA,UAA0B,CAA1B,GAAA,CAAA,GAAA,EAAA,EAAA,CAAA,CAAA;;IAEE,OAAO,EAAT,CAAA;CACA;AACA,SAAA,WAAA,CAAA,GAAA,EAAA;IACA,IAAA,KAAA,GAAA,EAAA,CAAA;;;QAIM,IAAN,CAAW,IAAX,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;SACA;KACA,CAAA,CAAA;;IAEA,IAAA,KAAA,GAAQ,GAAR,CAAA;IACA,OAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,KAAA,CAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;YACA,IAAA,IAAA,GAAA,MAAA,CAAA,wBAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;YACA,IAAc,CAAC,SAAf,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,IAAA,IAAA,KAAA,IAAA,IAAA,EAAA;gBACA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;;SDlIA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,IAAA,0BAAA,IAAA,YAAA;IACA,SAAA,0BAAA,CAAA,gBAAA,EAAA;QAIA,IAAA,CAAA,gBAAA,GAAA,gBAAA,CAAA;;IACA,0BAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,OAAA,EAAA;QACA,IAAA,QAAkB,GAAlB,IAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,OAAA,CAAA,CAAA;QACA,OAAA,IAAA,mBAAA,CAAA,QAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,qBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAAG,yCAAA,CAAA,CAAA,CAAA;;IAEO,OAAP,0BAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,0BAAA,CAAsB,UAAtB,GAAA;IACA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CAGA,CAAA;;AAGA,0BAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IAAA,EAAA,IAAA,EAAAD,6BAAA,GAAA;CACA,CAAA,EAAA,CAAA;AAAA,IAAA,mBAAA,IAAA,YAAA;IACA,SAAA,mBAAA,CAAA,SAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA;QAJU,IAAV,CAAA,SAAA,GAAA,SAAA,CAAA;QAIA,IAAA,CAAA,kBAAA,GAAA,kBAAA,CAAA;QACM,IAAN,CAAA,aAAA,GAAA,aAAkD,CAAC;QAEnD,IAAA,CAAA,eAAA,GAAA,eAAA,CAAA;QACI,IAAJ,CAAA,iBAA0B,GAA1B,iBAAA,CAAA;QACA,IAAA,CAAA,UAAA,GAAA,IAAA,iBAAA,EAAA,CAAA;KAEA;IACA,MAAA,CAAA,cAAA,CAAI,mBAAJ,CAAA,SAAA,EAAA,UAAA,EAAA;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,UAAA,EAAA;QACA,OAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,CAA0D,UAA1D,CAAA,CAAA;KACA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,UAAA,EAAA;QAEA,OAAA,IAAA,CAAA,SAAA,CAAA,kBAAA,CAA2D,UAA3D,CAAA,CAAA;KAEA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,iCAAA,GAAA,UAAA,UAAA,EAAA;QAEA,OAAA,IAAA,CAAA,SAAA,CAAA,iCAAA,CAAA,UAAA,CAAA,CAAA;KACA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,kCAAA,GAAA,UAAA,UAAA,EAAA;QAEA,OAAA,IAAA,CAAA,SAAA,CAAA,kCAAA,CAAA,UAAA,CAAA,CAAA;KACA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,SAAA,EAAA;QAEA,OAAA,IAAA,CAAA,SAAsC,CAAtC,qBAAA,CAAA,SAAA,CAAA,CAAA;KACA,CAAA;IACA,mBAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,SAAA,EAAA;QACA,OAAA,IAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,CAAA;KACG,CAAH;IAEE,mBAAF,CAAA,SAAA,CAAA,oBAAA,GAAE,UAAF,IAAsC,EAAtC;QACI,IAAI,IAAR,CAAA,SAAA,CAAA,aAAA,CAAA,IAAA,CAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAAD,wBAAA,CAAA,IAAA,CAAA,GAAA,uDAAA,CAAA,CAAA;SACA;KAEG,CAAH;IACE,mBAAF,CAAA,SAAA,CAAA,cAAA,GAAE,UAAF,QAAA,EAAA,QAAA,EAAA;QACI,IAAI,CAAC,oBAAoB,CAAC,QAA9B,CAAuC,CAAC;QACpC,IAAM,WAAW,GAAG,IAAI,CAAC,eAA7B,CAAA,OAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;QACI,IAAI,CAAC,eAAT,CAAA,WAAA,CAAA,QAAA,EAAA,IAAA,CACmB,UADnB,CAAA,gBAAA,CAAAD,sBAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;KAEG,CAAH;IACE,mBAAF,CAAA,SAAA,CAAA,iBAAmB,GAAjB,UAAkB,SAAoB,EAAE,QAAqC,EAA/E;QACI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACrC,IAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAChC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAACD,uBAAS,EAAE,WAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KACtF,CAAH;IACE,mBAAF,CAAA,SAAA,CAAA,iBAA8B,GAA5B,UAAF,SAAgE,EAAhE,QAAA,EAAA;QACI,IAAI,CAAC,oBAAoB,CAAC,SAA9B,CAAA,CAAA;QACI,IAAM,WAAW,GAAG,IAAI,CAAC,kBAA7B,CAAA,OAAuD,CAAvD,SAAA,EAAA,KAAA,CAAA,CAAA;QACI,IAAI,CAAC,kBAAT,CAAA,YAAA,CAAyC,SAAzC,EAAoD,IAApD,CAAA,UAAA,CAAA,gBAAA,CAAAD,uBAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;KACG,CAAH;IACE,mBAAF,CAAA,SAAA,CAAA,YAAA,GAAE,UAAF,IAAmB,EAAnB,QAAA,EAAA;QACA,IAAA,CAAA,oBAAqC,CAAC,IAAtC,CAAA,CAAA;QACA,IAAA,WAAA,GAAA,IAAA,CAAA,aAAA,CAAA,OAA+D,CAAC,IAAI,EAAE,KAAtE,CAAA,CAAA;QACA,IAAA,CAAA,aAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,CAAA,gBAAA,CAAAD,kBAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;;;;;;CA5DA,EAAA,CAAA,CAAA;;;;;;AAyEA,IAAA,0BAA4B,GAA5BF,mCAAA,CAAAC,qCAAA,EAAA,oBAAA,EAAA;IACA;QACA,OAAA,EAAAF,8BAAA;QACA,QAAY,EAAC;YACb,SAAA,EAAA;gBACY,gBAAZ;gBACA,EAAA,OAAA,EAAAD,8BAAA,EAAA,WAAA,EAAA,gBAAA,EAAA;gBACA,qBAAA;gBACA,EAAA,OAAA,EAAAD,mCAAA,EAAA,WAAA,EAAA,qBAAA,EAAA;gBACA,oBAAA;gBACA,EAAA,OAAA,EAAAD,kCAAA,EAAA,WAAA,EAAA,oBAP6E,EAO7E;aACA;;QDhJA,KAAA,EAAA,IAAA;;;;;;;;;;;;;;;"}