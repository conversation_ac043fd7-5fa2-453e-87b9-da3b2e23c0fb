{"_args": [["@angular/platform-browser@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/platform-browser@4.2.5", "_id": "@angular/platform-browser@4.2.5", "_inBundle": false, "_integrity": "sha1-2d3+D4EITpjvJKefSF27ES54oMQ=", "_location": "/@angular/platform-browser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/platform-browser@4.2.5", "name": "@angular/platform-browser", "escapedName": "@angular%2fplatform-browser", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/platform-browser/-/platform-browser-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - library for using Angular in a web browser", "es2015": "./@angular/platform-browser.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/platform-browser.umd.js", "module": "./@angular/platform-browser.es5.js", "name": "@angular/platform-browser", "peerDependencies": {"@angular/core": "4.2.5", "@angular/common": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./platform-browser.d.ts", "version": "4.2.5"}