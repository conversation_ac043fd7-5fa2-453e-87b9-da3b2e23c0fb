{"title": "Touch events", "description": "Method of registering when, where and how the interface is touched, for devices with a touch screen. These DOM events are similar to mousedown, mousemove, etc.", "spec": "http://www.w3.org/TR/touch-events/", "status": "rec", "links": [{"url": "http://www.quirksmode.org/mobile/tableTouch.html", "title": "Detailed support tables"}, {"url": "http://www.quirksmode.org/m/tests/drag2.html", "title": "Multi-touch demo"}, {"url": "http://schepers.cc/getintouch", "title": "Information on the spec development"}, {"url": "http://msdn.microsoft.com/en-us/library/ie/hh673557(v=vs.85).aspx", "title": "Internet Explorer's gesture and touch implementation."}, {"url": "http://github.com/CamHenlin/TouchPolyfill", "title": "Touch polyfill for supporting touch events on Internet Explorer"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/Touch_events", "title": "MDN – Touch events"}], "bugs": [{"description": "Beware, android 2.3 and below do not detect multiple touches."}, {"description": "Firefox 4 & 5 support a [non-standard](https://hacks.mozilla.org/2010/08/firefox4-beta3/) implementation to achieve the same."}], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "p", "11": "p"}, "edge": {"12": "n d #3", "13": "n d #3", "14": "n d #3", "15": "n d #3", "16": "n d #3"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "n d #1", "26": "n d #1", "27": "n d #1", "28": "n d #1", "29": "n d #1", "30": "n d #1", "31": "n d #1", "32": "n d #1", "33": "n d #1", "34": "n d #1", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "n d #1", "45": "n d #1", "46": "n d #1", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "n", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "p", "11": "a #2"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Internet Explorer implements Pointer Events specification which supports more input devices than Touch Events one.\r\n\r\nThere is a library on GitHub that is working toward bringing W3C touch events to IE 10 and 11: https://github.com/CamHenlin/TouchPolyfill \r\n\r\nRemoved support in Firefox refers to desktop Firefox only.", "notes_by_num": {"1": "Can be enabled in Firefox using the dom.w3c_touch_events.enabled flag (disabled by default for site compatibility reasons)", "2": "Supported on IE11 Mobile for phones with \"[Windows Phone 8.1 Update](http://blogs.msdn.com/b/ie/archive/2014/07/31/the-mobile-web-should-just-work-for-everyone.aspx)\"", "3": "Can be enabled in Edge using the \"Enable touch events\" flag under `about:flags`"}, "usage_perc_y": 85.76, "usage_perc_a": 0.42, "ucprefix": false, "parent": "", "keywords": "touchstart,touchend,touchmove,touchenter,touchleave,touchcancel", "ie_id": "touchevents", "chrome_id": "6156165603917824", "firefox_id": "", "webkit_id": "", "shown": true}