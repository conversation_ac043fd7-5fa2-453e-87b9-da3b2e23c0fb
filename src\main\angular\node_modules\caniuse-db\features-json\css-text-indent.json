{"title": "CSS text-indent", "description": "The `text-indent` property applies indentation to lines of inline content in a block.", "spec": "https://drafts.csswg.org/css-text-3/#text-indent-property", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-indent", "title": "Mozilla Developer Network (MDN) documentation - CSS text-indent"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=784648", "title": "Firefox support bug"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=112755", "title": "WebKit support bug"}, {"url": "https://www.sitepoint.com/css-image-replacement-text-indent-negative-margins-and-more/", "title": "Article on using text-indent for image replacement"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1"}, "chrome": {"4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1 #2", "39": "a #1 #2", "40": "a #1 #2", "41": "a #1 #2", "42": "a #1 #2", "43": "a #1 #2", "44": "a #1 #2", "45": "a #1 #2", "46": "a #1 #2", "47": "a #1 #2", "48": "a #1 #2", "49": "a #1 #2", "50": "a #1 #2", "51": "a #1 #2", "52": "a #1 #2", "53": "a #1 #2", "54": "a #1 #2", "55": "a #1 #2", "56": "a #1 #2", "57": "a #1 #2", "58": "a #1 #2", "59": "a #1 #2", "60": "a #1 #2", "61": "a #1 #2", "62": "a #1 #2"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "a #1", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #1", "TP": "a #1"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1 #2", "26": "a #1 #2", "27": "a #1 #2", "28": "a #1 #2", "29": "a #1 #2", "30": "a #1 #2", "31": "a #1 #2", "32": "a #1 #2", "33": "a #1 #2", "34": "a #1 #2", "35": "a #1 #2", "36": "a #1 #2", "37": "a #1 #2", "38": "a #1 #2", "39": "a #1 #2", "40": "a #1 #2", "41": "a #1 #2", "42": "a #1 #2", "43": "a #1 #2", "44": "a #1 #2", "45": "a #1 #2", "46": "a #1 #2", "47": "a #1 #2", "48": "a #1 #2"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #1", "4.2-4.3": "a #1", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11": "a #1"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "a #1", "2.2": "a #1", "2.3": "a #1", "3": "a #1", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "a #1", "4.4.3-4.4.4": "a #1", "56": "a #1"}, "bb": {"7": "a #1", "10": "a #1"}, "op_mob": {"10": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "a #1", "12.1": "a #1", "37": "a #1 #2"}, "and_chr": {"59": "a #1 #2"}, "and_ff": {"54": "a #1"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.4": "a #1"}, "samsung": {"4": "a #1", "5": "a #1 #2"}, "and_qq": {"1.2": "a #1 #2"}, "baidu": {"7.12": "a #1 #2"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to supporting a `<length>` value, but not the `each-line` or `hanging` keywords.", "2": "Support for `each-line` & `hanging` is available behind the Experimental Web Platform features flag"}, "usage_perc_y": 0, "usage_perc_a": 98.1, "ucprefix": false, "parent": "", "keywords": "each-line,hanging", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}