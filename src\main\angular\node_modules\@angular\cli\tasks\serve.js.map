{"version": 3, "file": "serve.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["tasks/serve.ts"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,6BAA6B;AAC7B,+BAA+B;AAC/B,mCAAmC;AACnC,2BAA2B;AAC3B,6CAAoD;AACpD,2DAAwE;AACxE,6DAA8D;AAE9D,6CAA6C;AAC7C,sDAA0D;AAE1D,MAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACvD,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACrD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAE3B,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,gBAAkC,EAAE,aAAkB;QACnE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnB,IAAI,eAAoB,CAAC;QACzB,MAAM,aAAa,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC;QACrD,MAAM,SAAS,GAAG,4BAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAEzD,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,CAAC;QACnE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,IAAI,WAAW,CAAC,iDAAiD,CAAC,CAAC;QAC3E,CAAC;QACD,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3D,MAAM,IAAI,WAAW,CAAC,0DAA0D,CAAC,CAAC;QACpF,CAAC;QACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,iFAAiF;YACjF,SAAS,EAAE,EAAE;SACd,CAAC;QAEF,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAEtE,IAAI,aAAa,GAAG,IAAI,mCAAkB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtF,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC;YAC/B,QAAQ,EAAE,gBAAgB,CAAC,GAAG,GAAG,OAAO,GAAG,MAAM;YACjD,QAAQ,EAAE,gBAAgB,CAAC,IAAI,KAAK,SAAS,GAAG,WAAW,GAAG,gBAAgB,CAAC,IAAI;YACnF,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE;SACvC,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtC,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;YACd,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;;;SAG1B,CAAC,CAAC;QACP,CAAC;QAED,IAAI,aAAa,GAAG,aAAa,CAAC;QAClC,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;YAChC,IAAI,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;YAC7C,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAClC,UAAU,GAAG,GAAG,gBAAgB,CAAC,GAAG,GAAG,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,CAAC;YAC5E,CAAC;YACD,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxC,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC;YAC7C,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QAED,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;YAChC,qEAAqE;YACrE,qEAAqE;YACrE,IAAI,WAAW,GAAG;gBAChB,6BAA6B,aAAa,EAAE;aAC7C,CAAC;YACF,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzB,MAAM,cAAc,GAAG,4DAA4D,CAAC;gBACpF,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;YAChB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;SACzB,CAAC,CAAC;gBACH,EAAE,CAAC,SAAS,CAAC,2DAA2D,CAAC,CAAC;gBAC1E,EAAE,CAAC,SAAS,CAAC,wEAAwE,CAAC,CAAC;gBACvF,EAAE,CAAC,SAAS,CAAC,wDAAwD,CAAC,CAAC;gBACvE,EAAE,CAAC,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;gBACpD,EAAE,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;gBACnE,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC3C,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;gBACrE,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;oBAChC,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;cAChB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;;WAEzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YAAC,CAAC;YACjE,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,CAAC;QACnD,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5B,yEAAyE;YACzE,4CAA4C;YAC5C,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC5B,KAAK,EAAE,CAAC,QAAa;oBACnB,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE;wBACnC,QAAQ,CAAC,eAAe,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;oBAClD,CAAC,CAAC,CAAC;gBACL,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QAEzC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAClB,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,6BAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpE,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,EAAE,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAChF,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC7B,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,MAAM,OAAO,GAAG,oBAAoB,GAAG,SAAS,GAAG,kBAAkB,CAAC;gBACtE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,MAAM,GAAW,IAAI,CAAC;QAC1B,IAAI,OAAO,GAAW,IAAI,CAAC;QAC3B,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC3E,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5B,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,MAAM,6BAA6B,GAA0C;YAC3E,OAAO,EAAE,EAAE,6BAA6B,EAAE,GAAG,EAAE;YAC/C,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;gBAC5B,cAAc,EAAE,IAAI;gBACpB,iBAAiB,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC;aAC1D;YACD,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,gBAAgB,CAAC,MAAM,KAAK,YAAY;YAClD,YAAY,EAAE;gBACZ,IAAI,EAAE,gBAAgB,CAAC,IAAI;aAC5B;YACD,KAAK,EAAE,gBAAgB,CAAC,GAAG;YAC3B,OAAO,EAAE;gBACP,MAAM,EAAE,gBAAgB,CAAC,MAAM,KAAK,aAAa;gBACjD,QAAQ,EAAE,KAAK;aAChB;YACD,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,gBAAgB,CAAC,UAAU;YACnC,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;SACpD,CAAC;QAEF,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;YACtC,6BAA6B,CAAC,GAAG,GAAG,MAAM,CAAC;YAC3C,6BAA6B,CAAC,IAAI,GAAG,OAAO,CAAC;QAC/C,CAAC;QAED,6BAA6B,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;QAEzD,8DAA8D;QAC9D,EAAE,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/B,6BAA6B,CAAC,UAAU,GAAG,gBAAgB,CAAC,SAAS,CAAC;YACrE,6BAA6B,CAAC,kBAA0B,CAAC,KAAK;gBAC7D,gBAAgB,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;QACvD,CAAC;QAED,EAAE,CAAC,CAAC,gBAAgB,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC;YAC7C,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,0BAAY,CAAA;;;;;;;OAOlC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAO,CAAA;;mDAEe,gBAAgB,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI;6BACpE,aAAa;;KAErC,CAAC,CAAC,CAAC;QAEJ,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,6BAA6B,CAAC,CAAC;QACpF,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,QAAQ,EAAE,MAAM;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,CAAC,GAAQ,EAAE,MAAW;gBAChF,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACR,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC1B,GAAG,CAAC,aAAa,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAU;YAChB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,wCAAwC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC7F,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC"}