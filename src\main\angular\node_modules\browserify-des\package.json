{"_args": [["browserify-des@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-des@1.0.0", "_id": "browserify-des@1.0.0", "_inBundle": false, "_integrity": "sha1-2qJ3cXRwki7S/hhZQRihdUOXId0=", "_location": "/browserify-des", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-des@1.0.0", "name": "browserify-des", "escapedName": "browserify-des", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/browserify-cipher"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-des/-/browserify-des-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/crypto-browserify/browserify-des/issues"}, "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1"}, "description": "browserify-des ===", "devDependencies": {"standard": "^5.3.1", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "homepage": "https://github.com/crypto-browserify/browserify-des#readme", "license": "MIT", "main": "index.js", "name": "browserify-des", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/browserify-des.git"}, "scripts": {"test": "standard && node test.js | tspec"}, "version": "1.0.0"}