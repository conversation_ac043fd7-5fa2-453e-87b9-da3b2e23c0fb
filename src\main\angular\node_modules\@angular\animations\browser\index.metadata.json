{"__symbolic": "module", "version": 3, "metadata": {"AnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"NOOP": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "NoopAnimationDriver"}}}}, "ɵAnimation": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "AnimationDriver"}, {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationMetadata"}]}], "buildTimelines": [{"__symbolic": "method"}]}}, "ɵAnimationStyleNormalizer": {"__symbolic": "class", "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵNoopAnimationStyleNormalizer": {"__symbolic": "class", "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵWebAnimationsStyleNormalizer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵAnimationStyleNormalizer"}, "members": {"normalizePropertyName": [{"__symbolic": "method"}], "normalizeStyleValue": [{"__symbolic": "method"}]}}, "ɵNoopAnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "ɵAnimationEngine": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "AnimationDriver"}, {"__symbolic": "reference", "name": "ɵAnimationStyleNormalizer"}]}], "registerTrigger": [{"__symbolic": "method"}], "register": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "onInsert": [{"__symbolic": "method"}], "onRemove": [{"__symbolic": "method"}], "setProperty": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}], "flush": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}]}}, "ɵWebAnimationsDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "ɵsupportsWebAnimations": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 48, "character": 9, "module": "./src/render/web_animations/web_animations_driver"}, "right": "undefined"}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 48, "character": 43, "module": "./src/render/web_animations/web_animations_driver"}, "right": "function"}}}, "ɵWebAnimationsPlayer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 32, "character": 45, "module": "./src/render/web_animations/web_animations_player"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 33, "character": 22, "module": "./src/render/web_animations/web_animations_player"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵWebAnimationsPlayer"}]}]}], "_onFinish": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "_buildPlayer": [{"__symbolic": "method"}], "_preparePlayerBeforeStart": [{"__symbolic": "method"}], "_triggerWebAnimation": [{"__symbolic": "method"}], "onStart": [{"__symbolic": "method"}], "onDone": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "pause": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "_resetDomPlayerState": [{"__symbolic": "method"}], "restart": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "getPosition": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}]}}}, "origins": {"AnimationDriver": "./src/render/animation_driver", "ɵAnimation": "./src/dsl/animation", "ɵAnimationStyleNormalizer": "./src/dsl/style_normalization/animation_style_normalizer", "ɵNoopAnimationStyleNormalizer": "./src/dsl/style_normalization/animation_style_normalizer", "ɵWebAnimationsStyleNormalizer": "./src/dsl/style_normalization/web_animations_style_normalizer", "ɵNoopAnimationDriver": "./src/render/animation_driver", "ɵAnimationEngine": "./src/render/animation_engine_next", "ɵWebAnimationsDriver": "./src/render/web_animations/web_animations_driver", "ɵsupportsWebAnimations": "./src/render/web_animations/web_animations_driver", "ɵWebAnimationsPlayer": "./src/render/web_animations/web_animations_player"}, "importAs": "@angular/animations/browser"}