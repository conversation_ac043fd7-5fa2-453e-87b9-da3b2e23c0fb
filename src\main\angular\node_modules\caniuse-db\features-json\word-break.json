{"title": "CSS3 word-break", "description": "Property to prevent or allow words to be broken over multiple lines between letters.", "spec": "http://www.w3.org/TR/css3-text/#word-break", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en/CSS/word-break", "title": "Mozilla Developer Network (MDN) documentation - CSS word-break"}, {"url": "https://www.webplatform.org/docs/css/properties/word-break", "title": "WebPlatform Docs"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a", "3.2": "a", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "a", "2.2": "a", "2.3": "a", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "y"}, "bb": {"7": "a", "10": "a"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to supporting the `break-all` value, but not the `keep-all` value.\r\n\r\nChrome, Safari and other WebKit/Blink browsers also support the unofficial `break-word` value which is treated like `word-wrap: break-word`.", "notes_by_num": {}, "usage_perc_y": 82.38, "usage_perc_a": 12.37, "ucprefix": false, "parent": "", "keywords": "break-all,keep-all", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}