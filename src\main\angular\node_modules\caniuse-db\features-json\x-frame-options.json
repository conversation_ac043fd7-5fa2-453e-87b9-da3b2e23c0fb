{"title": "X-Frame-Options HTTP header", "description": "An HTTP header which indicates whether the browser should allow the webpage to be displayed in a frame within another webpage. Used as a defense against clickjacking attacks.", "spec": "https://tools.ietf.org/html/rfc7034", "status": "other", "links": [{"url": "http://erlend.oftedal.no/blog/tools/xframeoptions/", "title": "X-Frame-Options Compatibility Test"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options", "title": "Mozilla Developer Network (MDN) documentation - X-Frame-Options"}, {"url": "https://www.owasp.org/index.php/Clickjacking_Defense_Cheat_Sheet", "title": "OWASP Clickjacking Defense Cheat Sheet"}, {"url": "https://blogs.msdn.microsoft.com/ieinternals/2010/03/30/combating-clickjacking-with-x-frame-options/", "title": "Combating ClickJacking With X-Frame-Options - IEInternals"}, {"url": "https://blogs.msdn.microsoft.com/ie/2009/01/27/ie8-security-part-vii-clickjacking-defenses/", "title": "IE8 Security Part VII: ClickJacking Defenses - IEBlog"}], "bugs": [], "categories": ["Security"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "u", "16": "u", "17": "u", "18": "u", "19": "u", "20": "u", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "a", "10.1": "a", "11": "a", "TP": "a"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "u", "10.5": "u", "10.6": "u", "11": "u", "11.1": "u", "11.5": "u", "11.6": "a", "12": "a", "12.1": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "a", "10": "a"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "a", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "Partial support refers to not supporting the `ALLOW-FROM` option.\r\nThe `X-Frame-Options` header has been obsoleted by [the `frame-ancestors` directive](https://www.w3.org/TR/CSP2/#directive-frame-ancestors) from Content Security Policy Level 2.", "notes_by_num": {}, "usage_perc_y": 11.4, "usage_perc_a": 83.25, "ucprefix": false, "parent": "", "keywords": "x-frame-options,frame,options,header,clickjacking", "ie_id": "", "chrome_id": "5760041927835648", "firefox_id": "", "webkit_id": "", "shown": true}