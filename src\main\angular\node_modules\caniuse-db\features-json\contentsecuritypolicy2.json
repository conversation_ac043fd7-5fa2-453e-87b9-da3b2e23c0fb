{"title": "Content Security Policy Level 2", "description": "Mitigate cross-site scripting attacks by whitelisting allowed sources of script, style, and other resources. CSP 2 adds hash-source, nonce-source, and five new directives", "spec": "http://www.w3.org/TR/CSP/", "status": "cr", "links": [{"url": "http://html5rocks.com/en/tutorials/security/content-security-policy/", "title": "HTML5Rocks article"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP", "title": "Mozilla Developer Network (MDN) documentation - Content Security Policy"}], "bugs": [], "categories": ["Security"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #2", "36": "a #3", "37": "a #3", "38": "a #3", "39": "a #3", "40": "a #3", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #7", "46": "a #7", "47": "a #7", "48": "a #7", "49": "a #7", "50": "a #7", "51": "a #7", "52": "a #7", "53": "a #7", "54": "a #7", "55": "a #7", "56": "a #7", "57": "a #7"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "a #4", "37": "a #4", "38": "a #4", "39": "a #5", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "a #4", "24": "a #4", "25": "a #4", "26": "a #5", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "a #6"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Firefox 31-34 is missing the plugin-types, child-src, frame-ancestors, base-uri, and form-action directives.", "2": "Firefox 35 is missing the plugin-types, child-src, frame-ancestors, and form-action directives.", "3": "Firefox 36-44 is missing the plugin-types and child-src directives.", "4": "Chrome 36-38 & Opera 23-25 are missing the plugin-types, child-src, frame-ancestors, base-uri, and form-action directives.", "5": "Chrome 39 and Opera 26 are missing the plugin-types, child-src, base-uri, and form-action directives.", "6": "Firefox 38 on Android is missing the child-src directive.", "7": "Firefox 45+ is missing the plugin-types directive."}, "usage_perc_y": 70.85, "usage_perc_a": 5.86, "ucprefix": false, "parent": "", "keywords": "csp,header,nonce,hash", "ie_id": "contentsecuritypolicylevel2", "chrome_id": "4957003285790720", "firefox_id": "", "webkit_id": "specification-content-security-policy-level-2", "shown": true}