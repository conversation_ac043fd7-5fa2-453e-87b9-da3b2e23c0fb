{"_args": [["@ngtools/json-schema@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@ngtools/json-schema@1.1.0", "_id": "@ngtools/json-schema@1.1.0", "_inBundle": false, "_integrity": "sha1-w6DFRNYjkqzCgTpCyKDcb1j4aSI=", "_location": "/@ngtools/json-schema", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@ngtools/json-schema@1.1.0", "name": "@ngtools/json-schema", "escapedName": "@ngtools%2fjson-schema", "scope": "@ngtools", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@ngtools/json-schema/-/json-schema-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "dependencies": {}, "description": "Schema validating and reading for configurations, similar to Angular CLI config.", "engines": {"node": ">= 4.1.0", "npm": ">= 3.0.0"}, "homepage": "https://github.com/angular/angular-cli/tree/master/packages/@ngtools/json-schema", "keywords": ["angular", "json", "json-schema", "schema", "config"], "license": "MIT", "main": "./src/index.js", "name": "@ngtools/json-schema", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular-cli.git"}, "typings": "src/index.d.ts", "version": "1.1.0"}