[{"__symbolic": "module", "version": 3, "metadata": {"HtmlTagDefinition": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 25, "character": 31}]}], "requireExtraParent": [{"__symbolic": "method"}], "isClosedByChild": [{"__symbolic": "method"}]}}, "getHtmlTagDefinition": {"__symbolic": "function", "parameters": ["tagName"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "index", "expression": {"base": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "meta": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "area": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "embed": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "link": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "img": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "input": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "param": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "hr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "br": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "source": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "track": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "wbr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "p": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["address", "article", "aside", "blockquote", "div", "dl", "fieldset", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "header", "hgroup", "hr", "main", "nav", "ol", "p", "pre", "section", "table", "ul"], "closedByParent": true}]}, "thead": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody", "tfoot"]}]}, "tbody": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody", "tfoot"], "closedByParent": true}]}, "tfoot": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody"], "closedByParent": true}]}, "tr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tr"], "requiredParents": ["tbody", "tfoot", "thead"], "closedByParent": true}]}, "td": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["td", "th"], "closedByParent": true}]}, "th": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["td", "th"], "closedByParent": true}]}, "col": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"requiredParents": ["colgroup"], "isVoid": true}]}, "svg": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"implicitNamespacePrefix": "svg"}]}, "math": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"implicitNamespacePrefix": "math"}]}, "li": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["li"], "closedByParent": true}]}, "dt": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["dt", "dd"]}]}, "dd": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["dt", "dd"], "closedByParent": true}]}, "rb": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "rt": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "rtc": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rtc", "rp"], "closedByParent": true}]}, "rp": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "optgroup": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["optgroup"], "closedByParent": true}]}, "option": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["option", "optgroup"], "closedByParent": true}]}, "pre": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"ignoreFirstLf": true}]}, "listing": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"ignoreFirstLf": true}]}, "style": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "RAW_TEXT"}}]}, "script": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "RAW_TEXT"}}]}, "title": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "ESCAPABLE_RAW_TEXT"}}]}, "textarea": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "ESCAPABLE_RAW_TEXT"}, "ignoreFirstLf": true}]}, "$quoted$": ["base", "meta", "area", "embed", "link", "img", "input", "param", "hr", "br", "source", "track", "wbr", "p", "thead", "tbody", "tfoot", "tr", "td", "th", "col", "svg", "math", "li", "dt", "dd", "rb", "rt", "rtc", "rp", "optgroup", "option", "pre", "listing", "style", "script", "title", "textarea"]}, "index": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "tagName"}, "member": "toLowerCase"}}}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"HtmlTagDefinition": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 25, "character": 31}]}], "requireExtraParent": [{"__symbolic": "method"}], "isClosedByChild": [{"__symbolic": "method"}]}}, "getHtmlTagDefinition": {"__symbolic": "function", "parameters": ["tagName"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "index", "expression": {"base": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "meta": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "area": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "embed": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "link": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "img": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "input": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "param": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "hr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "br": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "source": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "track": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "wbr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"isVoid": true}]}, "p": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["address", "article", "aside", "blockquote", "div", "dl", "fieldset", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "header", "hgroup", "hr", "main", "nav", "ol", "p", "pre", "section", "table", "ul"], "closedByParent": true}]}, "thead": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody", "tfoot"]}]}, "tbody": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody", "tfoot"], "closedByParent": true}]}, "tfoot": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tbody"], "closedByParent": true}]}, "tr": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["tr"], "requiredParents": ["tbody", "tfoot", "thead"], "closedByParent": true}]}, "td": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["td", "th"], "closedByParent": true}]}, "th": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["td", "th"], "closedByParent": true}]}, "col": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"requiredParents": ["colgroup"], "isVoid": true}]}, "svg": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"implicitNamespacePrefix": "svg"}]}, "math": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"implicitNamespacePrefix": "math"}]}, "li": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["li"], "closedByParent": true}]}, "dt": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["dt", "dd"]}]}, "dd": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["dt", "dd"], "closedByParent": true}]}, "rb": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "rt": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "rtc": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rtc", "rp"], "closedByParent": true}]}, "rp": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["rb", "rt", "rtc", "rp"], "closedByParent": true}]}, "optgroup": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["optgroup"], "closedByParent": true}]}, "option": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"closedByChildren": ["option", "optgroup"], "closedByParent": true}]}, "pre": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"ignoreFirstLf": true}]}, "listing": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"ignoreFirstLf": true}]}, "style": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "RAW_TEXT"}}]}, "script": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "RAW_TEXT"}}]}, "title": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "ESCAPABLE_RAW_TEXT"}}]}, "textarea": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}, "arguments": [{"contentType": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "./tags", "name": "TagContentType"}, "member": "ESCAPABLE_RAW_TEXT"}, "ignoreFirstLf": true}]}}, "index": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "tagName"}, "member": "toLowerCase"}}}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "HtmlTagDefinition"}}}}}}]