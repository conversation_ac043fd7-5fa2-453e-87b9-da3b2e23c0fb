"use strict";
var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var sourceMappingVisitor_1 = require("../sourceMappingVisitor");
var RecursiveAngularExpressionVisitor = (function (_super) {
    __extends(RecursiveAngularExpressionVisitor, _super);
    function RecursiveAngularExpressionVisitor(sourceFile, options, context, basePosition) {
        var _this = _super.call(this, sourceFile, options, context.template.template, basePosition) || this;
        _this.context = context;
        _this.basePosition = basePosition;
        _this.preDefinedVariables = [];
        return _this;
    }
    RecursiveAngularExpressionVisitor.prototype.visit = function (ast, context) {
        ast.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitBinary = function (ast, context) {
        ast.left.visit(this);
        ast.right.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitChain = function (ast, context) { return this.visitAll(ast.expressions, context); };
    RecursiveAngularExpressionVisitor.prototype.visitConditional = function (ast, context) {
        ast.condition.visit(this);
        ast.trueExp.visit(this);
        ast.falseExp.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitPipe = function (ast, context) {
        ast.exp.visit(this);
        this.visitAll(ast.args, context);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitFunctionCall = function (ast, context) {
        ast.target.visit(this);
        this.visitAll(ast.args, context);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitImplicitReceiver = function (ast, context) { return null; };
    RecursiveAngularExpressionVisitor.prototype.visitInterpolation = function (ast, context) {
        var _this = this;
        ast.expressions.forEach(function (e, i) { return _this.visit(e, context); });
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitKeyedRead = function (ast, context) {
        ast.obj.visit(this);
        ast.key.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitKeyedWrite = function (ast, context) {
        ast.obj.visit(this);
        ast.key.visit(this);
        ast.value.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitLiteralArray = function (ast, context) {
        return this.visitAll(ast.expressions, context);
    };
    RecursiveAngularExpressionVisitor.prototype.visitLiteralMap = function (ast, context) { return this.visitAll(ast.values, context); };
    RecursiveAngularExpressionVisitor.prototype.visitLiteralPrimitive = function (ast, context) { return null; };
    RecursiveAngularExpressionVisitor.prototype.visitMethodCall = function (ast, context) {
        ast.receiver.visit(this);
        return this.visitAll(ast.args, context);
    };
    RecursiveAngularExpressionVisitor.prototype.visitPrefixNot = function (ast, context) {
        ast.expression.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitPropertyRead = function (ast, context) {
        ast.receiver.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitPropertyWrite = function (ast, context) {
        ast.receiver.visit(this);
        ast.value.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitSafePropertyRead = function (ast, context) {
        ast.receiver.visit(this);
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitSafeMethodCall = function (ast, context) {
        ast.receiver.visit(this);
        return this.visitAll(ast.args, context);
    };
    RecursiveAngularExpressionVisitor.prototype.visitAll = function (asts, context) {
        var _this = this;
        asts.forEach(function (ast) { return ast.visit(_this, context); });
        return null;
    };
    RecursiveAngularExpressionVisitor.prototype.visitQuote = function (ast, context) { return null; };
    return RecursiveAngularExpressionVisitor;
}(sourceMappingVisitor_1.SourceMappingVisitor));
exports.RecursiveAngularExpressionVisitor = RecursiveAngularExpressionVisitor;
