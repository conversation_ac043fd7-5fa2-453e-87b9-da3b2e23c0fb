{"_args": [["color@0.11.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "color@0.11.4", "_id": "color@0.11.4", "_inBundle": false, "_integrity": "sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q=", "_location": "/color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color@0.11.4", "name": "color", "escapedName": "color", "rawSpec": "0.11.4", "saveSpec": null, "fetchSpec": "0.11.4"}, "_requiredBy": ["/colormin"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/color/-/color-0.11.4.tgz", "_spec": "0.11.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON>", "<PERSON>"], "bugs": {"url": "https://github.com/Qix-/color/issues"}, "dependencies": {"clone": "^1.0.2", "color-convert": "^1.3.0", "color-string": "^0.3.0"}, "description": "Color conversion and manipulation with CSS string support", "devDependencies": {"mocha": "^2.2.5", "xo": "^0.12.1"}, "files": ["CHANGELOG.md", "LICENSE", "index.js"], "homepage": "https://github.com/Qix-/color#readme", "keywords": ["color", "colour", "css"], "license": "MIT", "name": "color", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color.git"}, "scripts": {"pretest": "xo", "test": "mocha"}, "version": "0.11.4", "xo": {"rules": {"no-cond-assign": 0, "new-cap": 0}}}