<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> src/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">50.94% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>27/53</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/20</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">21.43% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>3/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">45.24% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>19/42</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="angular2TextMask.ts"><a href="angular2TextMask.ts.html">angular2TextMask.ts</a></td>
	<td data-value="50.94" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 50%;"></div><div class="cover-empty" style="width:50%;"></div></div></td>
	<td data-value="50.94" class="pct medium">50.94%</td>
	<td data-value="53" class="abs medium">27/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="21.43" class="pct low">21.43%</td>
	<td data-value="14" class="abs low">3/14</td>
	<td data-value="45.24" class="pct low">45.24%</td>
	<td data-value="42" class="abs low">19/42</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Apr 20 2018 19:52:09 GMT+0100 (BST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
