{"title": "WAI-ARIA Accessibility features", "description": "Method of providing ways for people with disabilities to use dynamic web content and web applications.", "spec": "http://www.w3.org/TR/wai-aria/", "status": "rec", "links": [{"url": "http://www.w3.org/WAI/intro/aria", "title": "Information page"}, {"url": "http://www.paciellogroup.com/blog/2011/10/browser-assistive-technology-tests-redux/", "title": "Links to various test results"}, {"url": "http://en.wikipedia.org/wiki/WAI-ARIA", "title": "Wikipedia"}, {"url": "http://www.alistapart.com/articles/the-accessibility-of-wai-aria/", "title": "ALA Article"}, {"url": "http://zufelt.ca/blog/are-you-confused-html5-and-wai-aria-yet", "title": "HTML5/WAI-ARIA information"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "a", "9": "a", "10": "a", "11": "a"}, "edge": {"12": "a", "13": "a", "14": "a", "15": "a", "16": "a"}, "firefox": {"2": "a", "3": "a", "3.5": "a", "3.6": "a", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a"}, "safari": {"3.1": "n", "3.2": "n", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "a", "10.1": "a", "11": "a", "TP": "a"}, "opera": {"9": "n", "9.5-9.6": "a", "10.0-10.1": "a", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "a"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "a", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "a"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "Support for ARIA is rather complex and currently is not fully supported in any browser. For detailed information on partial support see the [ARIA 1.0 Implementation Report](http://www.w3.org/WAI/ARIA/1.0/CR/implementation-report)\r\n", "notes_by_num": {}, "usage_perc_y": 0, "usage_perc_a": 88.38, "ucprefix": false, "parent": "", "keywords": "wai,aria", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}