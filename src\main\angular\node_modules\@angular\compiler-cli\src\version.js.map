{"version": 3, "file": "version.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/version.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH;;;;GAIG;AAEH,sCAAsC;AACtC;;GAEG;AACU,QAAA,OAAO,GAAG,IAAI,cAAO,CAAC,mBAAmB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\nimport {Version} from '@angular/core';\n/**\n * @stable\n */\nexport const VERSION = new Version('4.2.5');\n"]}