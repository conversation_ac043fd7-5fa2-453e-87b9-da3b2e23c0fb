{"_args": [["camel-case@3.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "camel-case@3.0.0", "_id": "camel-case@3.0.0", "_inBundle": false, "_integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "_location": "/camel-case", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "camel-case@3.0.0", "name": "camel-case", "escapedName": "camel-case", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/html-minifier"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/camel-case/-/camel-case-3.0.0.tgz", "_spec": "3.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/camel-case/issues"}, "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}, "description": "Camel case a string", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^2.2.1", "standard": "^7.1.2"}, "files": ["camel-case.js", "camel-case.d.ts", "LICENSE"], "homepage": "https://github.com/blakeembrey/camel-case", "keywords": ["camel", "case", "camelcase", "camel-case", "dash", "hyphen", "dot", "underscore", "lodash", "separator", "string", "text", "convert"], "license": "MIT", "main": "camel-case.js", "name": "camel-case", "repository": {"type": "git", "url": "git://github.com/blakeembrey/camel-case.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test-spec": "mocha -- -R spec --bail"}, "typings": "camel-case.d.ts", "version": "3.0.0"}