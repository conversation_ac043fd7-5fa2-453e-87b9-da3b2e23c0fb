{"_args": [["any-promise@1.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "any-promise@1.3.0", "_id": "any-promise@1.3.0", "_inBundle": false, "_integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "_location": "/any-promise", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "any-promise@1.3.0", "name": "any-promise", "escapedName": "any-promise", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/tsconfig"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/any-promise/-/any-promise-1.3.0.tgz", "_spec": "1.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>"}, "browser": {"./register.js": "./register-shim.js"}, "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "dependencies": {}, "description": "Resolve any installed ES6 compatible promise", "devDependencies": {"ava": "^0.14.0", "bluebird": "^3.0.0", "es6-promise": "^3.0.0", "is-promise": "^2.0.0", "lie": "^3.0.0", "mocha": "^2.0.0", "native-promise-only": "^0.8.0", "phantomjs-prebuilt": "^2.0.0", "pinkie": "^2.0.0", "promise": "^7.0.0", "q": "^1.0.0", "rsvp": "^3.0.0", "vow": "^0.4.0", "when": "^3.0.0", "zuul": "^3.0.0"}, "homepage": "http://github.com/kevinbeaty/any-promise", "keywords": ["promise", "es6"], "license": "MIT", "main": "index.js", "name": "any-promise", "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "scripts": {"test": "ava"}, "typings": "index.d.ts", "version": "1.3.0"}