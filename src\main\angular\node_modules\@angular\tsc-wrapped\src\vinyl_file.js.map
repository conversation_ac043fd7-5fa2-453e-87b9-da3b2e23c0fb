{"version": 3, "file": "vinyl_file.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/vinyl_file.ts"], "names": [], "mappings": ";;AAeA,qBAA4B,GAAQ;IAClC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC;AAC7E,CAAC;AAFD,kCAEC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport interface VinylFile extends Object {\n  // Absolute path to the virtual file\n  path: string;\n\n  // Content of the virtual file\n  contents: Buffer;\n}\n\nexport function isVinylFile(obj: any): obj is VinylFile {\n  return (typeof obj === 'object') && ('path' in obj) && ('contents' in obj);\n}"]}