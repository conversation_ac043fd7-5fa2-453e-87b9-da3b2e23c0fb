/**
 * Silos Integration JavaScript for JSP
 * Converted from Angular TypeScript logic
 */

// Mock user data service - replace with your actual user data retrieval logic
const userDataService = {
    getUserData: function() {
        // This should be replaced with your actual user data retrieval logic
        // For JSP, this might come from session attributes, request parameters, or AJAX calls
        return {
            username: 'your_username', // Replace with actual username retrieval
            profile: 'B<PERSON><PERSON><PERSON>', // Replace with actual profile retrieval
            branch: 'your_branch' // Replace with actual branch retrieval
        };
    }
};

// Mock message service - replace with your actual error handling logic
const msgService = {
    showError: function(message, title) {
        // Replace with your actual error display logic
        // For JSP, this might be alert, custom modal, or server-side error handling
        alert(title + ': ' + message);
    }
};

/**
 * Main function to check user profile and redirect to Silos if needed
 */
function checkAndRedirectToSilos() {
    const userData = userDataService.getUserData();
    console.log(userData);
    console.log(userData.profile);
    console.log(userDataService.getUserData().profile);
   
    if (userData && userData.profile === "BIOSUG") {
        redirectToSilos();
    }
}

/**
 * Redirect to Silos with authentication
 */
async function redirectToSilos() {
    const userData = userDataService.getUserData();
    console.log(userData.username);

    try {
        const response = await getAuthData(
            userData.username,
            "ZA0",
            userData.branch
        );
        console.log(response);
        if (response && response !== null) {
            dataToSilos(response);
        }
    } catch (error) {
        console.log(error.message);
        console.log(error.status);
        console.log(error.statusText);
        console.error("Authentication failed:", error);
        msgService.showError("Authentication failed", "Error");
    }
}

/**
 * Get authentication data from the server
 */
async function getAuthData(userId, applicationCode, branchCode) {
    // Updated URL for UJ environment testing
    const url = `https://ubz-uj.collaudo.usinet.it/UBZ-ESA-RS/service/userService/v1/users/${userId}/${applicationCode}/${branchCode}`;

    const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    };

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: headers,
            cache: 'no-cache'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error in getAuthData:', error);
        throw error;
    }
}

/**
 * Send data to Silos authentication endpoint
 */
async function dataToSilos(dataSilos) {
    const url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

    const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    };

    const postData = JSON.stringify(dataSilos);

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: postData,
            credentials: 'include', // equivalent to withCredentials: true
            cache: 'no-cache'
        });

        console.log("Response status:", response.status);

        if (response.status === 200) {
            // Redirect to the response URL or the original URL
            window.location.replace(response.url || url);
        } else {
            console.error("Authentication failed with status:", response.status);
            msgService.showError("Authentication failed", "Error");
        }
    } catch (error) {
        console.error("Authentication error:", error);
        msgService.showError("Authentication failed", "Error");
    }
}

// Export functions for global access (if needed)
if (typeof window !== 'undefined') {
    window.checkAndRedirectToSilos = checkAndRedirectToSilos;
    window.redirectToSilos = redirectToSilos;
    window.getAuthData = getAuthData;
    window.dataToSilos = dataToSilos;
}