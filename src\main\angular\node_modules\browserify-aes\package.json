{"_args": [["browserify-aes@1.0.6", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "browserify-aes@1.0.6", "_id": "browserify-aes@1.0.6", "_inBundle": false, "_integrity": "sha1-Xncl297x/Vkw1OurSFZ85FHEigo=", "_location": "/browserify-aes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-aes@1.0.6", "name": "browserify-aes", "escapedName": "browserify-aes", "rawSpec": "1.0.6", "saveSpec": null, "fetchSpec": "1.0.6"}, "_requiredBy": ["/browserify-cipher", "/parse-asn1"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/browserify-aes/-/browserify-aes-1.0.6.tgz", "_spec": "1.0.6", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": "", "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/browserify-aes/issues"}, "dependencies": {"buffer-xor": "^1.0.2", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "inherits": "^2.0.1"}, "description": "aes, for browserify", "devDependencies": {"standard": "^3.7.3", "tap-spec": "^1.0.0", "tape": "^3.0.0"}, "directories": {"test": "test"}, "homepage": "https://github.com/crypto-browserify/browserify-aes", "keywords": ["aes", "crypto", "browserify"], "license": "MIT", "main": "index.js", "name": "browserify-aes", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/browserify-aes.git"}, "scripts": {"test": "standard && node test/index.js|tspec"}, "version": "1.0.6"}