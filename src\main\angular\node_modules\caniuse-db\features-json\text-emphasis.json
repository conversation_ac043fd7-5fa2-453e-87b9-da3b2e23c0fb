{"title": "text-emphasis styling", "description": "Method of using small symbols next to each glyph to emphasize a run of text, commonly used in East Asian languages. The `text-emphasis` shorthand, and its `text-emphasis-style` and `text-emphasis-color` longhands, can be used to apply marks to the text. The `text-emphasis-position` property, which inherits separately, allows setting the emphasis marks' position with respect to the text.", "spec": "https://drafts.csswg.org/css-text-decor-3/#text-emphasis-property", "status": "cr", "links": [{"url": "https://github.com/zmmbreeze/jquery.emphasis/", "title": "A javascript fallback for CSS3 emphasis mark."}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6514536-text-decoration-styling", "title": "Microsoft Edge feature request on UserVoice"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/text-emphasis", "title": "Mozilla Developer Network (MDN) documentation - text-emphasis"}], "bugs": [{"description": "Chrome on Android occasionally has issues rendering emphasis glyphs correctly."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n d #2", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "a x #1", "7": "a x #1", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a x #1", "4.4.3-4.4.4": "a x #1", "56": "a x #1"}, "bb": {"7": "n", "10": "a x #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #1"}, "and_chr": {"59": "a x #1"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a x #1"}, "samsung": {"4": "a x #1", "5": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "Some old WebKit browsers (like Chrome 24) support `-webkit-text-emphasis`, but does not support CJK languages and is therefore considered unsupported.", "notes_by_num": {"1": "Partial support refers to incorrect support for `-webkit-text-emphasis-position`. These browsers support `over` and `under` as values, but not the added `left` and `right` values required by the spec.", "2": "Can be enabled in Firefox using the `layout.css.text-emphasis.enabled` flag"}, "usage_perc_y": 17.5, "usage_perc_a": 70.24, "ucprefix": false, "parent": "", "keywords": "text-emphasis,text-emphasis-position,text-emphasis-style,text-emphasis-color", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}