{"version": 3, "file": "animations.umd.min.js", "sources": ["../../../../packages/animations/src/players/animation_player.ts", "../../../../packages/animations/src/players/animation_group_player.ts", "../../../../packages/animations/src/animation_builder.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {scheduleMicroTask} from '../util';\n\n/**\n * AnimationPlayer controls an animation sequence that was produced from a programmatic animation.\n * (see {@link AnimationBuilder AnimationBuilder} for more information on how to create programmatic\n * animations.)\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationPlayer {\n  onDone(fn: () => void): void;\n  onStart(fn: () => void): void;\n  onDestroy(fn: () => void): void;\n  init(): void;\n  hasStarted(): boolean;\n  play(): void;\n  pause(): void;\n  restart(): void;\n  finish(): void;\n  destroy(): void;\n  reset(): void;\n  setPosition(p: any /** TODO #9100 */): void;\n  getPosition(): number;\n  parentPlayer: AnimationPlayer|null;\n  readonly totalTime: number;\n  beforeDestroy?: () => any;\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finished = false;\npublic parentPlayer: AnimationPlayer|null = null;\npublic totalTime = 0;\nconstructor() {}\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ninit(): void {}\n/**\n * @return {?}\n */\nplay(): void {\n    if (!this.hasStarted()) {\n      this.triggerMicrotask();\n      this._onStart();\n    }\n    this._started = true;\n  }\n/**\n * @return {?}\n */\ntriggerMicrotask() { scheduleMicroTask(() => this._onFinish()); }\n/**\n * @return {?}\n */\nprivate _onStart() {\n    this._onStartFns.forEach(fn => fn());\n    this._onStartFns = [];\n  }\n/**\n * @return {?}\n */\npause(): void {}\n/**\n * @return {?}\n */\nrestart(): void {}\n/**\n * @return {?}\n */\nfinish(): void { this._onFinish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      if (!this.hasStarted()) {\n        this._onStart();\n      }\n      this.finish();\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @return {?}\n */\nreset(): void {}\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void {}\n/**\n * @return {?}\n */\ngetPosition(): number { return 0; }\n}\n\nfunction NoopAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nNoopAnimationPlayer.prototype._onDoneFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._onStartFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._onDestroyFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._started;\n/** @type {?} */\nNoopAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nNoopAnimationPlayer.prototype._finished;\n/** @type {?} */\nNoopAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nNoopAnimationPlayer.prototype.totalTime;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {scheduleMicroTask} from '../util';\nimport {AnimationPlayer} from './animation_player';\nexport class AnimationGroupPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _onDestroyFns: Function[] = [];\npublic parentPlayer: AnimationPlayer|null = null;\npublic totalTime: number = 0;\n/**\n * @param {?} _players\n */\nconstructor(private _players: AnimationPlayer[]) {\n    let doneCount = 0;\n    let destroyCount = 0;\n    let startCount = 0;\n    const total = this._players.length;\n\n    if (total == 0) {\n      scheduleMicroTask(() => this._onFinish());\n    } else {\n      this._players.forEach(player => {\n        player.parentPlayer = this;\n        player.onDone(() => {\n          if (++doneCount >= total) {\n            this._onFinish();\n          }\n        });\n        player.onDestroy(() => {\n          if (++destroyCount >= total) {\n            this._onDestroy();\n          }\n        });\n        player.onStart(() => {\n          if (++startCount >= total) {\n            this._onStart();\n          }\n        });\n      });\n    }\n\n    this.totalTime = this._players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void { this._players.forEach(player => player.init()); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @return {?}\n */\nprivate _onStart() {\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nhasStarted() { return this._started; }\n/**\n * @return {?}\n */\nplay() {\n    if (!this.parentPlayer) {\n      this.init();\n    }\n    this._onStart();\n    this._players.forEach(player => player.play());\n  }\n/**\n * @return {?}\n */\npause(): void { this._players.forEach(player => player.pause()); }\n/**\n * @return {?}\n */\nrestart(): void { this._players.forEach(player => player.restart()); }\n/**\n * @return {?}\n */\nfinish(): void {\n    this._onFinish();\n    this._players.forEach(player => player.finish());\n  }\n/**\n * @return {?}\n */\ndestroy(): void { this._onDestroy(); }\n/**\n * @return {?}\n */\nprivate _onDestroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._onFinish();\n      this._players.forEach(player => player.destroy());\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._players.forEach(player => player.reset());\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void {\n    const /** @type {?} */ timeAtPosition = p * this.totalTime;\n    this._players.forEach(player => {\n      const /** @type {?} */ position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n      player.setPosition(position);\n    });\n  }\n/**\n * @return {?}\n */\ngetPosition(): number {\n    let /** @type {?} */ min = 0;\n    this._players.forEach(player => {\n      const /** @type {?} */ p = player.getPosition();\n      min = Math.min(p, min);\n    });\n    return min;\n  }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] { return this._players; }\n}\n\nfunction AnimationGroupPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationGroupPlayer.prototype._onDoneFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype._onStartFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype._finished;\n/** @type {?} */\nAnimationGroupPlayer.prototype._started;\n/** @type {?} */\nAnimationGroupPlayer.prototype._destroyed;\n/** @type {?} */\nAnimationGroupPlayer.prototype._onDestroyFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype.parentPlayer;\n/** @type {?} */\nAnimationGroupPlayer.prototype.totalTime;\n/** @type {?} */\nAnimationGroupPlayer.prototype._players;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions} from './animation_metadata';\nimport {AnimationPlayer} from './players/animation_player';\n/**\n * AnimationBuilder is an injectable service that is available when the {\\@link\n * BrowserAnimationsModule BrowserAnimationsModule} or {\\@link NoopAnimationsModule\n * NoopAnimationsModule} modules are used within an application.\n * \n * The purpose if this service is to produce an animation sequence programmatically within an\n * angular component or directive.\n * \n * Programmatic animations are first built and then a player is created when the build animation is\n * attached to an element.\n * \n * ```ts\n * // remember to include the BrowserAnimationsModule module for this to work...\n * import {AnimationBuilder} from '\\@angular/animations';\n * \n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n * \n *   makeAnimation(element: any) {\n *     // first build the animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n * \n *     // then create a player from it\n *     const player = myAnimation.create(element);\n * \n *     player.play();\n *   }\n * }\n * ```\n * \n * When an animation is built an instance of {\\@link AnimationFactory AnimationFactory} will be\n * returned. Using that an {\\@link AnimationPlayer AnimationPlayer} can be created which can then be\n * used to start the animation.\n * \n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport abstract class AnimationBuilder {\n/**\n * @abstract\n * @param {?} animation\n * @return {?}\n */\nbuild(animation: AnimationMetadata|AnimationMetadata[]) {}\n}\n/**\n * An instance of `AnimationFactory` is returned from {\\@link AnimationBuilder#build\n * AnimationBuilder.build}.\n * \n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport abstract class AnimationFactory {\n/**\n * @abstract\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(element: any, options?: AnimationOptions) {}\n}\n"], "names": ["NoopAnimationPlayer", "this", "_onDestroyFns", "_started", "totalTime", "_finished", "prototype", "onStart", "fn", "_onStartFns", "push", "init", "play", "finish", "_onFinish", "destroy", "_onStart", "reset", "AnimationGroupPlayer", "_players", "_this", "_onDoneFns", "_destroyed", "parentPlayer", "doneCount", "destroyCount", "startCount", "total", "length", "scheduleMicroTask", "for<PERSON>ach", "player", "onDone", "onDestroy", "_onD<PERSON>roy", "reduce", "time", "Math", "max", "hasStarted", "setPosition", "p", "timeAtPosition", "min", "get", "exports", "module", "factory"], "mappings": ";;;;;0BEAA,gBAAA6C,UAAA,mBAAAC,QAAAC,QAAAF;;;;;;;;;;;;;;;;;;;;;gTF+CA7C,oBAAA,iFACAC,KAAGC,iBAIHD,KAAAE,UAAA,8DAIAF,KAAAG,UAAA,4DAMAH,KAAAI,sGAUAL,oBAAAM,UAAAC,QAAA,SAAAC,IAAAP,KAAAQ,YAAAC,KAAAF,6OAkBAR,oBAAAM,UAAAK,KAAA,aAIAX,oBAAAM,UAAAM,KAAA,wEAKAX,KAAAE,UAAA,oWAwBAH,oBAAAM,UAAAO,OAAA,WAAAZ,KAAAa,aAIAd,oBAAAM,UAAAS,QAAA,mEArCAd,KAAAe,uGAmDAhB,oBAAAM,UAAAW,MAAA,2JC9HAC,qBAAA,WAKA,QAAAA,sBAAAC,UAII,GAAIC,OAARnB,IACIA,MAAJkB,SAAAA,SACIlB,KAAJoB,cACIpB,KAAJQ,eAEIR,KAAJI,WAAA,EACAJ,KAAAE,UAAA,EACAF,KAAAqB,YAAA,EAAArB,KAAAC,iBACAD,KAAAsB,aAAA,KACAtB,KAAAG,UAAA,CACA,IAAAoB,WAAA,EACAC,aAAA,EACAC,WAAA,EACAC,MAAA1B,KAAAkB,SAAAS,MACA,IAAAD,MACAE,kBAAA,WAAA,MAAAT,OAAAN,cAGAb,KAAAkB,SAAAW,QAAA,SAAAC,QACAA,OAAAR,aAAAH,MACQW,OAAOC,OAAf,aACgBR,WAAhBG,OACYP,MAAKN,cAGjBiB,OAAAE,UAAA,aACAR,cAAAE,OAEAP,MAAyBc,8DAEzBd,MAAAJ,eAQAf,KAAAG,UAAAH,KAAAkB,SAAAgB,OAAA,SAAAC,KAAAL,QAAA,MAAAM,MAAAC,IAAAF,KAAAL,OAAA3B,YAAA,SAKAc,sBAAAZ,UAAAQ,UAAA,sHAUAI,qBAAAZ,UAAAK,KAAA,WAAAV,KAVckB,SAUdW,QAAA,SAAAC,QAAA,MAAAA,QAAApB,UAKAO,qBAAAZ,UAAAC,QAAA,SAAAC,IAAAP,KAAAQ,YAAAC,KAAAF,wDAKAP,KAAAsC,0QAmBArB,qBAAAZ,UAAAiC,WAAA,WAAA,MAAAtC,MAAAE,UAIAe,qBAAAZ,UAAAM,KAAA,+ZAoBAX,KAAAkB,SAAAW,QAAA,SAAAC,QAAA,MAAAA,QAAAlB,YAKAK,qBAAAZ,UAAAS,QAAA,WAAAd,KAAAiC,qFA1BAjC,KAAAqB,YAAA,EAiCArB,KAAAa,2JASAI,qBAAAZ,UAAAW,MAAA,iIAUAC,qBAAAZ,UAAAkC,YAAA,SAAAC,GACA,GAAAC,gBAAAD,EAAAxC,KAAAG,mNA7BG,GAAHuC,KAAA,QAwCA1C,MAAAkB,SAAAW,QAAA,SAAAC,8HAvJAa,IAAA,WAAA,MAAA3C,MAAAkB"}