{"title": "CSS font-smooth", "description": "Controls the application of anti-aliasing when fonts are rendered.", "spec": "http://www.w3.org/TR/WD-font/#font-smooth", "status": "unoff", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/font-smooth", "title": "Mozilla Developer Network (MDN) documentation - font-smooth"}, {"url": "http://www.w3.org/TR/WD-font/#font-smooth", "title": "Old version of W3C recommendation containing font-smooth"}], "bugs": [{"description": "Chrome briefly removed and then re-instated support for -webkit-font-smoothing in 2012."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "a x #2 #3", "26": "a x #2 #3", "27": "a x #2 #3", "28": "a x #2 #3", "29": "a x #2 #3", "30": "a x #2 #3", "31": "a x #2 #3", "32": "a x #2 #3", "33": "a x #2 #3", "34": "a x #2 #3", "35": "a x #2 #3", "36": "a x #2 #3", "37": "a x #2 #3", "38": "a x #2 #3", "39": "a x #2 #3", "40": "a x #2 #3", "41": "a x #2 #3", "42": "a x #2 #3", "43": "a x #2 #3", "44": "a x #2 #3", "45": "a x #2 #3", "46": "a x #2 #3", "47": "a x #2 #3", "48": "a x #2 #3", "49": "a x #2 #3", "50": "a x #2 #3", "51": "a x #2 #3", "52": "a x #2 #3", "53": "a x #2 #3", "54": "a x #2 #3", "55": "a x #2 #3", "56": "a x #2 #3", "57": "a x #2 #3"}, "chrome": {"4": "n", "5": "a x #1 #3", "6": "a x #1 #3", "7": "a x #1 #3", "8": "a x #1 #3", "9": "a x #1 #3", "10": "a x #1 #3", "11": "a x #1 #3", "12": "a x #1 #3", "13": "a x #1 #3", "14": "a x #1 #3", "15": "a x #1 #3", "16": "a x #1 #3", "17": "a x #1 #3", "18": "a x #1 #3", "19": "a x #1 #3", "20": "a x #1 #3", "21": "a x #1 #3", "22": "a x #1 #3", "23": "a x #1 #3", "24": "a x #1 #3", "25": "a x #1 #3", "26": "a x #1 #3", "27": "a x #1 #3", "28": "a x #1 #3", "29": "a x #1 #3", "30": "a x #1 #3", "31": "a x #1 #3", "32": "a x #1 #3", "33": "a x #1 #3", "34": "a x #1 #3", "35": "a x #1 #3", "36": "a x #1 #3", "37": "a x #1 #3", "38": "a x #1 #3", "39": "a x #1 #3", "40": "a x #1 #3", "41": "a x #1 #3", "42": "a x #1 #3", "43": "a x #1 #3", "44": "a x #1 #3", "45": "a x #1 #3", "46": "a x #1 #3", "47": "a x #1 #3", "48": "a x #1 #3", "49": "a x #1 #3", "50": "a x #1 #3", "51": "a x #1 #3", "52": "a x #1 #3", "53": "a x #1 #3", "54": "a x #1 #3", "55": "a x #1 #3", "56": "a x #1 #3", "57": "a x #1 #3", "58": "a x #1 #3", "59": "a x #1 #3", "60": "a x #1 #3", "61": "a x #1 #3", "62": "a x #1 #3"}, "safari": {"3.1": "n", "3.2": "n", "4": "a x #1 #3", "5": "a x #1 #3", "5.1": "a x #1 #3", "6": "a x #1 #3", "6.1": "a x #1 #3", "7": "a x #1 #3", "7.1": "a x #1 #3", "8": "a x #1 #3", "9": "a x #1 #3", "9.1": "a x #1 #3", "10": "a x #1 #3", "10.1": "a x #1 #3", "11": "a x #1 #3", "TP": "a x #1 #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #1 #3", "16": "a x #1 #3", "17": "a x #1 #3", "18": "a x #1 #3", "19": "a x #1 #3", "20": "a x #1 #3", "21": "a x #1 #3", "22": "a x #1 #3", "23": "a x #1 #3", "24": "a x #1 #3", "25": "a x #1 #3", "26": "a x #1 #3", "27": "a x #1 #3", "28": "a x #1 #3", "29": "a x #1 #3", "30": "a x #1 #3", "31": "a x #1 #3", "32": "a x #1 #3", "33": "a x #1 #3", "34": "a x #1 #3", "35": "a x #1 #3", "36": "a x #1 #3", "37": "a x #1 #3", "38": "a x #1 #3", "39": "a x #1 #3", "40": "a x #1 #3", "41": "a x #1 #3", "42": "a x #1 #3", "43": "a x #1 #3", "44": "a x #1 #3", "45": "a x #1 #3", "46": "a x #1 #3", "47": "a x #1 #3", "48": "a x #1 #3"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "Though present in early (2002) drafts of CSS3 Fonts, `font-smooth` has been removed from this specification and is currently not on the standard track.", "notes_by_num": {"1": "Webkit implements something similar with a different name `-webkit-font-smoothing` and different values: `none`, `antialiased` and `subpixel-antialiased`.", "2": "Firefox implements something similar with a different name `-moz-osx-font-smoothing` and different values: `auto`, `inherit`, `unset`, `grayscale`.", "3": "Works only on Mac OS X platform."}, "usage_perc_y": 0, "usage_perc_a": 34.85, "ucprefix": false, "parent": "", "keywords": "font smooth,font smoothing,-webkit-font-smoothing,-moz-osx-font-smoothing", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}