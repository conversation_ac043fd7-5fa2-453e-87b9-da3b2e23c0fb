{"_args": [["ajv-keywords@1.5.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ajv-keywords@1.5.1", "_id": "ajv-keywords@1.5.1", "_inBundle": false, "_integrity": "sha1-MU3QpLM2j609/NxU7eYXG4htrzw=", "_location": "/ajv-keywords", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ajv-keywords@1.5.1", "name": "ajv-keywords", "escapedName": "ajv-keywords", "rawSpec": "1.5.1", "saveSpec": null, "fetchSpec": "1.5.1"}, "_requiredBy": ["/webpack"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ajv-keywords/-/ajv-keywords-1.5.1.tgz", "_spec": "1.5.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "description": "Custom JSON-Schema keywords for ajv validator", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "files": ["index.js", "keywords"], "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "keywords": ["JSON-Schema", "ajv", "keywords"], "license": "MIT", "main": "index.js", "name": "ajv-keywords", "peerDependencies": {"ajv": ">=4.10.0"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "eslint": "eslint index.js keywords/*.js", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec", "test-spec": "mocha spec/*.spec.js -R spec"}, "version": "1.5.1"}