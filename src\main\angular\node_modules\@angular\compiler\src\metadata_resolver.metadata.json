[{"__symbolic": "module", "version": 3, "metadata": {"ERROR_COLLECTOR_TOKEN": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "CompileMetadataResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}], null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "ERROR_COLLECTOR_TOKEN"}]}]], "parameters": [{"__symbolic": "reference", "module": "./config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "./ng_module_resolver", "name": "NgModuleResolver"}, {"__symbolic": "reference", "module": "./directive_resolver", "name": "DirectiveResolver"}, {"__symbolic": "reference", "module": "./pipe_resolver", "name": "PipeResolver"}, {"__symbolic": "reference", "module": "./summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "./schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "module": "./directive_normalizer", "name": "DirectiveNormalizer"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}, {"__symbolic": "reference", "module": "./aot/static_symbol", "name": "StaticSymbolCache"}, {"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 56, "character": 75, "context": {"typeName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}]}], "getReflector": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "_createProxyClass": [{"__symbolic": "method"}], "getGeneratedClass": [{"__symbolic": "method"}], "getComponentViewClass": [{"__symbolic": "method"}], "getHostComponentViewClass": [{"__symbolic": "method"}], "getHostComponentType": [{"__symbolic": "method"}], "getRendererType": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "initComponentFactory": [{"__symbolic": "method"}], "_loadSummary": [{"__symbolic": "method"}], "loadDirectiveMetadata": [{"__symbolic": "method"}], "getNonNormalizedDirectiveMetadata": [{"__symbolic": "method"}], "getDirectiveMetadata": [{"__symbolic": "method"}], "getDirectiveSummary": [{"__symbolic": "method"}], "isDirective": [{"__symbolic": "method"}], "isPipe": [{"__symbolic": "method"}], "isNgModule": [{"__symbolic": "method"}], "getNgModuleSummary": [{"__symbolic": "method"}], "loadNgModuleDirectiveAndPipeMetadata": [{"__symbolic": "method"}], "getNgModuleMetadata": [{"__symbolic": "method"}], "_checkSelfImport": [{"__symbolic": "method"}], "_getTypeDescriptor": [{"__symbolic": "method"}], "_addTypeToModule": [{"__symbolic": "method"}], "_getTransitiveNgModuleMetadata": [{"__symbolic": "method"}], "_getIdentifierMetadata": [{"__symbolic": "method"}], "isInjectable": [{"__symbolic": "method"}], "getInjectableSummary": [{"__symbolic": "method"}], "_getInjectableMetadata": [{"__symbolic": "method"}], "_getTypeMetadata": [{"__symbolic": "method"}], "_getFactoryMetadata": [{"__symbolic": "method"}], "getPipeMetadata": [{"__symbolic": "method"}], "getPipeSummary": [{"__symbolic": "method"}], "getOrLoadPipeMetadata": [{"__symbolic": "method"}], "_loadPipeMetadata": [{"__symbolic": "method"}], "_getDependenciesMetadata": [{"__symbolic": "method"}], "_getTokenMetadata": [{"__symbolic": "method"}], "_getProvidersMetadata": [{"__symbolic": "method"}], "_validateProvider": [{"__symbolic": "method"}], "_getEntryComponentsFromProvider": [{"__symbolic": "method"}], "_getEntryComponentMetadata": [{"__symbolic": "method"}], "getProviderMetadata": [{"__symbolic": "method"}], "_getQueriesMetadata": [{"__symbolic": "method"}], "_queryVarBindings": [{"__symbolic": "method"}], "_getQueryMetadata": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ERROR_COLLECTOR_TOKEN": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "CompileMetadataResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}], null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "ERROR_COLLECTOR_TOKEN"}]}]], "parameters": [{"__symbolic": "reference", "module": "./config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "./ng_module_resolver", "name": "NgModuleResolver"}, {"__symbolic": "reference", "module": "./directive_resolver", "name": "DirectiveResolver"}, {"__symbolic": "reference", "module": "./pipe_resolver", "name": "PipeResolver"}, {"__symbolic": "reference", "module": "./summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "./schema/element_schema_registry", "name": "ElementSchemaRegistry"}, {"__symbolic": "reference", "module": "./directive_normalizer", "name": "DirectiveNormalizer"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}, {"__symbolic": "reference", "module": "./aot/static_symbol", "name": "StaticSymbolCache"}, {"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 56, "character": 75, "context": {"typeName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}]}], "getReflector": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "_createProxyClass": [{"__symbolic": "method"}], "getGeneratedClass": [{"__symbolic": "method"}], "getComponentViewClass": [{"__symbolic": "method"}], "getHostComponentViewClass": [{"__symbolic": "method"}], "getHostComponentType": [{"__symbolic": "method"}], "getRendererType": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "initComponentFactory": [{"__symbolic": "method"}], "_loadSummary": [{"__symbolic": "method"}], "loadDirectiveMetadata": [{"__symbolic": "method"}], "getNonNormalizedDirectiveMetadata": [{"__symbolic": "method"}], "getDirectiveMetadata": [{"__symbolic": "method"}], "getDirectiveSummary": [{"__symbolic": "method"}], "isDirective": [{"__symbolic": "method"}], "isPipe": [{"__symbolic": "method"}], "isNgModule": [{"__symbolic": "method"}], "getNgModuleSummary": [{"__symbolic": "method"}], "loadNgModuleDirectiveAndPipeMetadata": [{"__symbolic": "method"}], "getNgModuleMetadata": [{"__symbolic": "method"}], "_checkSelfImport": [{"__symbolic": "method"}], "_getTypeDescriptor": [{"__symbolic": "method"}], "_addTypeToModule": [{"__symbolic": "method"}], "_getTransitiveNgModuleMetadata": [{"__symbolic": "method"}], "_getIdentifierMetadata": [{"__symbolic": "method"}], "isInjectable": [{"__symbolic": "method"}], "getInjectableSummary": [{"__symbolic": "method"}], "_getInjectableMetadata": [{"__symbolic": "method"}], "_getTypeMetadata": [{"__symbolic": "method"}], "_getFactoryMetadata": [{"__symbolic": "method"}], "getPipeMetadata": [{"__symbolic": "method"}], "getPipeSummary": [{"__symbolic": "method"}], "getOrLoadPipeMetadata": [{"__symbolic": "method"}], "_loadPipeMetadata": [{"__symbolic": "method"}], "_getDependenciesMetadata": [{"__symbolic": "method"}], "_getTokenMetadata": [{"__symbolic": "method"}], "_getProvidersMetadata": [{"__symbolic": "method"}], "_validateProvider": [{"__symbolic": "method"}], "_getEntryComponentsFromProvider": [{"__symbolic": "method"}], "_getEntryComponentMetadata": [{"__symbolic": "method"}], "getProviderMetadata": [{"__symbolic": "method"}], "_getQueriesMetadata": [{"__symbolic": "method"}], "_queryVarBindings": [{"__symbolic": "method"}], "_getQueryMetadata": [{"__symbolic": "method"}], "_reportError": [{"__symbolic": "method"}]}}}}]