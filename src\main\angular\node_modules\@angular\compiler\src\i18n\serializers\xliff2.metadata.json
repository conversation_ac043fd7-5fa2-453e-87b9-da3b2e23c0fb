[{"__symbolic": "module", "version": 3, "metadata": {"Xliff2": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./serializer", "name": "Serializer"}, "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Xliff2": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./serializer", "name": "Serializer"}, "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}]}}}}]