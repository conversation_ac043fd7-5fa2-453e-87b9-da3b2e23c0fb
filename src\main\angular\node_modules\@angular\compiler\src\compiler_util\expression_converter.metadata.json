[{"__symbolic": "module", "version": 3, "metadata": {"EventHandlerVars": {"__symbolic": "class", "statics": {"event": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../output/output_ast", "name": "variable"}, "arguments": ["$event"]}}}, "LocalResolver": {"__symbolic": "interface"}, "ConvertActionBindingResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../output/output_ast", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../output/output_ast", "name": "ReadVarExpr"}]}]}}, "convertActionBinding": {"__symbolic": "function"}, "BuiltinConverter": {"__symbolic": "interface"}, "BuiltinConverterFactory": {"__symbolic": "interface"}, "convertPropertyBindingBuiltins": {"__symbolic": "function", "parameters": ["converterFactory", "ast"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 112, "character": 9, "context": {"name": "convertBuiltins"}}}, "ConvertPropertyBindingResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../output/output_ast", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../output/output_ast", "name": "Expression"}]}]}}, "convertPropertyBinding": {"__symbolic": "function"}, "temporaryDeclaration": {"__symbolic": "function", "parameters": ["bindingId", "temporary<PERSON><PERSON>ber"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 117, "character": 9, "context": {"name": "<PERSON><PERSON><PERSON>"}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"EventHandlerVars": {"__symbolic": "class", "statics": {"event": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../output/output_ast", "name": "variable"}, "arguments": ["$event"]}}}, "LocalResolver": {"__symbolic": "interface"}, "ConvertActionBindingResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../output/output_ast", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../output/output_ast", "name": "ReadVarExpr"}]}]}}, "convertActionBinding": {"__symbolic": "function"}, "BuiltinConverter": {"__symbolic": "interface"}, "BuiltinConverterFactory": {"__symbolic": "interface"}, "convertPropertyBindingBuiltins": {"__symbolic": "function", "parameters": ["converterFactory", "ast"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 112, "character": 9, "context": {"name": "convertBuiltins"}}}, "ConvertPropertyBindingResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "../output/output_ast", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../output/output_ast", "name": "Expression"}]}]}}, "convertPropertyBinding": {"__symbolic": "function"}, "temporaryDeclaration": {"__symbolic": "function", "parameters": ["bindingId", "temporary<PERSON><PERSON>ber"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 117, "character": 9, "context": {"name": "<PERSON><PERSON><PERSON>"}}}}}]