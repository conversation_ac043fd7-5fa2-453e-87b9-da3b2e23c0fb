{"_args": [["caniuse-db@1.0.30000697", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "caniuse-db@1.0.30000697", "_id": "caniuse-db@1.0.30000697", "_inBundle": false, "_integrity": "sha1-IM5qnO7vTvShXcjoDy6PuQSejXc=", "_location": "/caniuse-db", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "caniuse-db@1.0.30000697", "name": "caniuse-db", "escapedName": "caniuse-db", "rawSpec": "1.0.30000697", "saveSpec": null, "fetchSpec": "1.0.30000697"}, "_requiredBy": ["/autoprefixer", "/browserslist", "/caniuse-api"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/caniuse-db/-/caniuse-db-1.0.30000697.tgz", "_spec": "1.0.30000697", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Fyrd/caniuse/issues"}, "description": "Raw browser/feature support data from caniuse.com", "homepage": "https://github.com/Fyrd/caniuse#readme", "keywords": ["support", "css", "js", "html5", "svg"], "license": "CC-BY-4.0", "name": "caniuse-db", "repository": {"type": "git", "url": "git+https://github.com/Fyrd/caniuse.git"}, "version": "1.0.30000697"}