{"version": 3, "file": "bundler.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/bundler.ts"], "names": [], "mappings": ";;AAAA;;;;;;GAMG;AACH,2BAA6B;AAC7B,+BAAiC;AAEjC,yCAA8C;AAC9C,mCAAskB;AAGtkB,mDAAmD;AACnD,IAAM,kBAAkB,GAAG,4BAA4B,CAAC;AA0DxD;IAOE,yBACY,IAAY,EAAU,QAA0B,EAAU,IAAyB;QAAnF,SAAI,GAAJ,IAAI,CAAQ;QAAU,aAAQ,GAAR,QAAQ,CAAkB;QAAU,SAAI,GAAJ,IAAI,CAAqB;QAPvF,cAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;QACtC,kBAAa,GAAG,IAAI,GAAG,EAA0B,CAAC;QAClD,YAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;QAM5C,IAAI,CAAC,UAAU,GAAG,OAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAG,CAAC;IAC/C,CAAC;IAED,2CAAiB,GAAjB;QACE,gGAAgG;QAChG,eAAe;QACf,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,uEAAuE;QACvE,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAClD,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aAC9B,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,SAAS,EAA3B,CAA2B,CAAC;aACxC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC;YACJ,WAAW,EAAE,CAAC,CAAC,WAAW;YAC1B,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI;YACxB,MAAM,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM;SAC7B,CAAC,EAJG,CAIH,CAAC,CAAC;QAC9B,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aAC9B,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAZ,CAAY,CAAC;aACzB,MAAM,CAA2B,UAAC,CAAC,EAAE,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;YAC/D,MAAM,CAAC,CAAC,CAAC;QACX,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3B,MAAM,CAAC;YACL,QAAQ,EACJ,EAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAO,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAC;YACxF,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAEM,6BAAa,GAApB,UAAqB,UAAkB,EAAE,IAAY;QACnD,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAEO,qCAAW,GAAnB,UAAoB,UAAkB;QACpC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,EAAE,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAM,cAAc,GAAG,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,mCAAS,GAAjB,UAAkB,UAAkB;QAApC,iBA4DC;QA3DC,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,MAAM,GAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAEpD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,GAAG,EAAE,CAAC;QAEZ,IAAM,YAAY,GAAG,UAAC,cAAsB,EAAE,QAAgB;YAC5D,IAAM,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC;YACrC,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC;QAClC,CAAC,CAAC;QAEF,iDAAiD;QACjD,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC9B,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAChC,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAClC,EAAE,CAAC,CAAC,oDAA2C,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACtD,yEAAyE;oBACzE,IAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpD,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC5B,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,sDAAsD;oBACtD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;QACH,CAAC;QAED,6CAA6C;QAC7C,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7B,GAAG,CAAC,CAA4B,UAAc,EAAd,KAAA,MAAM,CAAC,OAAO,EAAd,cAAc,EAAd,IAAc;gBAAzC,IAAM,iBAAiB,SAAA;gBAC1B,IAAM,UAAU,GAAG,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACrE,2EAA2E;gBAC3E,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC7B,iDAAiD;oBACjD,GAAG,CAAC,CAAqB,UAAwB,EAAxB,KAAA,iBAAiB,CAAC,MAAM,EAAxB,cAAwB,EAAxB,IAAwB;wBAA5C,IAAM,UAAU,SAAA;wBACnB,IAAM,MAAI,GAAG,OAAO,UAAU,IAAI,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;wBAC1E,IAAM,QAAQ,GAAG,OAAO,UAAU,IAAI,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,EAAE,CAAC;wBAC5E,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;qBACzD;gBACH,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,4CAA4C;oBAC5C,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBACnD,GAAG,CAAC,CAAyB,UAAe,EAAf,mCAAe,EAAf,6BAAe,EAAf,IAAe;wBAAvC,IAAM,cAAc,wBAAA;wBACvB,IAAM,MAAI,GAAG,cAAc,CAAC,IAAI,CAAC;wBACjC,YAAY,CAAC,cAAc,EAAE,MAAI,CAAC,CAAC;qBACpC;gBACH,CAAC;aACF;QACH,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErC,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACK,6CAAmB,GAA3B,UAA4B,eAAyB;QACnD,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC;QACzC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEO,4CAAkB,GAA1B,UAA2B,MAAc;QACvC,IAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QACzC,IAAM,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACjD,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjD,IAAM,eAAe,GAAG,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC;QAC7D,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAEO,oCAAU,GAAlB,UAAmB,eAAyB;QAA5C,iBAmCC;QAlCC,IAAM,MAAM,GAAkB,EAAE,CAAC;QAEjC,IAAM,aAAa,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC,CAAC;QAChE,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB;YACE,OAAO,IAAI,EAAE,CAAC;gBACZ,IAAI,MAAM,GAAa,EAAE,CAAC;gBAC1B,IAAI,KAAK,GAAG,WAAW,EAAE,CAAC;gBAC1B,IAAI,IAAI,GAAG,kBAAkB,CAAC;gBAC9B,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACnC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC1C,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1C,CAAC;gBACD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACzB,IAAM,QAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/B,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,QAAM,CAAC,CAAC;oBAAC,MAAM,CAAC,QAAM,CAAC;YAChD,CAAC;QACH,CAAC;QAED,eAAe,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAA1B,CAA0B,CAAC,CAAC;QAE9D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,MAAM;YAChD,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtB,IAAI,MAAI,GAAG,MAAM,CAAC,IAAI,CAAC;gBACvB,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;oBAC5C,MAAI,GAAG,cAAc,EAAE,CAAC;oBACxB,MAAM,CAAC,WAAW,GAAG,MAAI,CAAC;gBAC5B,CAAC;gBACD,MAAM,CAAC,MAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,uCAAa,GAArB,UAAsB,MAAc;QAClC,IAAM,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAE/C,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;YAChC,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC;YAClC,IAAM,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;YAChD,IAAM,QAAM,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACpD,EAAE,CAAC,CAAC,QAAM,CAAC,CAAC,CAAC;gBACX,IAAM,KAAK,GAAG,QAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAChD,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACjD,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,sCAAY,GAApB,UAAqB,UAAkB,EAAE,KAAoB;QAC3D,EAAE,CAAC,CAAC,wBAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;QACD,EAAE,CAAC,CAAC,2BAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;QACD,EAAE,CAAC,CAAC,4BAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEO,sCAAY,GAApB,UAAqB,UAAkB,EAAE,KAAoB;QAA7D,iBAUC;QATC,MAAM,CAAC;YACL,UAAU,EAAE,OAAO;YACnB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC;YAC1D,UAAU,EACN,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,EAArC,CAAqC,CAAC;YACxF,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC;YACvD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC;SACzE,CAAC;IACJ,CAAC;IAEO,wCAAc,GAAtB,UAAuB,UAAkB,EAAE,OAAoB;QAA/D,iBAOC;QANC,IAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,GAAG,CAAC,CAAC,IAAM,MAAI,IAAI,OAAO,CAAC,CAAC,CAAC;YAC3B,IAAM,KAAK,GAAG,OAAO,CAAC,MAAI,CAAC,CAAC;YAC5B,MAAM,CAAC,MAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAC;QACnE,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,uCAAa,GAArB,UAAsB,UAAkB,EAAE,MAAsB;QAAhE,iBAgBC;QAfC,IAAM,MAAM,GAAmB,EAAC,UAAU,EAAE,MAAM,CAAC,UAAU,EAAC,CAAC;QAC/D,MAAM,CAAC,UAAU;YACb,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,EAArC,CAAqC,CAAC,CAAC;QAC3F,EAAE,CAAC,CAAC,yBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAyB,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB;gBACvE,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAC1B,UAAA,CAAC,IAAI,OAAA,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,EAArC,CAAqC,CAAC,EAAtD,CAAsD,CAAC,CAAC;YACrE,EAAE,CAAC,CAAC,8BAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAClC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;oBACrB,MAA8B,CAAC,UAAU;wBACtC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,EAArC,CAAqC,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,wCAAc,GAAtB,UAAuB,UAAkB,EAAE,OAAwB;QACjE,IAAI,MAAM,GAAoB,EAAE,CAAC;QACjC,GAAG,CAAC,CAAC,IAAM,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC;YAC1B,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,2BAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAC5F,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,yCAAe,GAAvB,UAAwB,UAAkB,EAAE,KAAuB;QAAnE,iBAOC;QANC,MAAM,CAAC;YACL,UAAU,EAAE,UAAU;YACtB,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,EAAhC,CAAgC,CAAC;YACrF,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,sCAAY,GAApB,UAAqB,UAAkB,EAAE,KAAoB;QAA7D,iBAqBC;QApBC,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QACD,EAAE,CAAC,CAAC,wBAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;QACD,EAAE,CAAC,CAAC,qCAA4B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;QACD,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,EAAhC,CAAgC,CAAC,CAAC;QAC1D,CAAC;QAED,qCAAqC;QACrC,IAAM,MAAM,GAAG,KAAuB,CAAC;QACvC,IAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,GAAG,CAAC,CAAC,IAAM,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,2CAAiB,GAAzB,UACI,UAAkB,EAAE,KACX;QACX,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACV,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACzB,KAAK,OAAO;oBACV,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAsB,CAAC,CAAC;gBAC/D,KAAK,WAAW;oBACd,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAA4C,CAAC,CAAC;gBACzF;oBACE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QACD,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;IAEO,sCAAY,GAApB,UAAqB,MAAc,EAAE,KAAoB;QACvD,MAAM,CAAC;YACL,UAAU,EAAE,OAAO;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,QAAA;SAC/B,CAAC;IACJ,CAAC;IAEO,0CAAgB,GAAxB,UAAyB,UAAkB,EAAE,KAA0C;QAAvF,iBAyFC;QAvFC,IAAM,eAAe,GAAG,UAAC,MAAc;YACrC,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YACvC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACvC,oFAAoF;gBACpF,kCAAkC;gBAClC,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC3B,MAAM,CAAC;oBACL,UAAU,EAAE,WAAW;oBACvB,IAAI,IAAI;wBACN,2DAA2D;wBAC3D,IAAM,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;wBAC/C,EAAE,CAAC,CAAC,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;4BACtC,MAAM,KAAK,CAAC,8CAA8C,CAAC,CAAC;wBAC9D,CAAC;wBACD,MAAM,CAAC,eAAe,CAAC,SAAS,GAAG,eAAe,CAAC,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC;oBACxF,CAAC;iBACF,CAAC;YACJ,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,qFAAqF;gBACrF,4BAA4B;gBAC5B,MAAM,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,EAAC,CAAC;YACvF,CAAC;QACH,CAAC,CAAC;QAEF,EAAE,CAAC,CAAC,4CAAmC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC9C,EAAE,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACnE,8CAA8C;gBAC9C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YACzE,CAAC;YAED,oEAAoE;YACpE,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpB,MAAM,CAAC;oBACL,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,EAAhC,CAAgC,CAAC;iBACtE,CAAC;YACJ,CAAC;YAED,gFAAgF;YAChF,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,EAAE,CAAC,CAAC,oDAA2C,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACvD,2FAA2F;YAC3F,0FAA0F;YAC1F,uFAAuF;YACvF,4FAA4F;YAC5F,yFAAyF;YACzF,SAAS;YAET,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjC,2FAA2F;gBAC3F,2BAA2B;gBAC3B,IAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACjE,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC;gBAClC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC;YACnF,CAAC;YAED,+DAA+D;YAC/D,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpB,mEAAmE;gBACnE,MAAM,CAAC;oBACL,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,EAAhC,CAAgC,CAAC;iBACtE,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,EAAE,CAAC,CAAC,4CAAmC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,8FAA8F;YAC9F,kBAAkB;YAClB,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC;oBACL,UAAU,EAAE,OAAO;oBACnB,OAAO,EAAE,sCAAsC;oBAC/C,OAAO,EAAE,EAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAC;iBAChC,CAAC;YACJ,CAAC;YAED,kDAAkD;YAClD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,+CAAqB,GAA7B,UAA8B,UAAkB,EAAE,KAAiC;QAEjF,IAAM,MAAM,GAA+B,EAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAC,CAAC;QAC1E,GAAG,CAAC,CAAC,IAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;YACvB,MAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAG,KAAa,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5E,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,kCAAQ,GAAhB,UAAiB,MAAc,EAAE,IAAY;QAC3C,IAAM,SAAS,GAAM,MAAM,SAAI,IAAM,CAAC;QACtC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,MAAM,GAAG,EAAC,MAAM,QAAA,EAAE,IAAI,MAAA,EAAC,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACxC,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAEO,2CAAiB,GAAzB,UAA0B,MAAc,EAAE,IAAY;QACpD,mCAAmC;QACnC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IACH,sBAAC;AAAD,CAAC,AA7aD,IA6aC;AA7aY,0CAAe;AA+a5B;IAGE,6BAAoB,IAAqB;QAArB,SAAI,GAAJ,IAAI,CAAiB;QAFjC,cAAS,GAAG,IAAI,6BAAiB,EAAE,CAAC;IAEA,CAAC;IAE7C,4CAAc,GAAd,UAAe,QAAgB;QAC7B,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACrF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IACH,0BAAC;AAAD,CAAC,AATD,IASC;AATY,kDAAmB;AAWhC,uBAAuB,UAAkB,EAAE,IAAY;IACrD,EAAE,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;QAC3E,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACxD,4EAA4E;YAC5E,UAAU,GAAG,MAAI,IAAI,CAAC,GAAG,GAAG,UAAY,CAAC;QAC3C,CAAC;QACD,oFAAoF;QACpF,kDAAkD;QAClD,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IACD,MAAM,CAAC,UAAU,CAAC;AACpB,CAAC;AAED,qBAAqB,CAAM;IACzB,MAAM,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;AAC1E,CAAC;AAED,uBAAuB,MAAc;IACnC,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;AAC3E,CAAC;AAED,8BAA8B,MAAc;IAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;AACxE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport {MetadataCollector} from './collector';\nimport {ClassMetadata, ConstructorMetadata, FunctionMetadata, MemberMetadata, MetadataEntry, MetadataError, MetadataImportedSymbolReferenceExpression, MetadataMap, MetadataObject, MetadataSymbolicExpression, MetadataSymbolicReferenceExpression, MetadataValue, MethodMetadata, ModuleMetadata, VERSION, isClassMetadata, isConstructorMetadata, isFunctionMetadata, isInterfaceMetadata, isMetadataError, isMetadataGlobalReferenceExpression, isMetadataImportedSymbolReferenceExpression, isMetadataModuleReferenceExpression, isMetadataSymbolicExpression, isMethodMetadata} from './schema';\n\n\n// The character set used to produce private names.\nconst PRIVATE_NAME_CHARS = 'abcdefghijklmnopqrstuvwxyz';\n\ninterface Symbol {\n  module: string;\n  name: string;\n\n  // Produced by indirectly by exportAll() for symbols re-export another symbol.\n  exports?: Symbol;\n\n  // Produced by indirectly by exportAll() for symbols are re-exported by another symbol.\n  reexportedAs?: Symbol;\n\n  // Produced by canonicalizeSymbols() for all symbols. A symbol is private if it is not\n  // exported by the index.\n  isPrivate?: boolean;\n\n  // Produced by canonicalizeSymbols() for all symbols. This is the one symbol that\n  // respresents all other symbols and is the only symbol that, among all the re-exported\n  // aliases, whose fields can be trusted to contain the correct information.\n  // For private symbols this is the declaration symbol. For public symbols this is the\n  // symbol that is exported.\n  canonicalSymbol?: Symbol;\n\n  // Produced by canonicalizeSymbols() for all symbols. This the symbol that originally\n  // declared the value and should be used to fetch the value.\n  declaration?: Symbol;\n\n  // A symbol is referenced if it is exported from index or referenced by the value of\n  // a referenced symbol's value.\n  referenced?: boolean;\n\n  // Only valid for referenced canonical symbols. Produces by convertSymbols().\n  value?: MetadataEntry;\n\n  // Only valid for referenced private symbols. It is the name to use to import the symbol from\n  // the bundle index. Produce by assignPrivateNames();\n  privateName?: string;\n}\n\nexport interface BundleEntries { [name: string]: MetadataEntry; }\n\nexport interface BundlePrivateEntry {\n  privateName: string;\n  name: string;\n  module: string;\n}\n\nexport interface BundledModule {\n  metadata: ModuleMetadata;\n  privates: BundlePrivateEntry[];\n}\n\nexport interface MetadataBundlerHost { getMetadataFor(moduleName: string): ModuleMetadata; }\n\ntype StaticsMetadata = {\n  [name: string]: MetadataValue | FunctionMetadata;\n};\n\nexport class MetadataBundler {\n  private symbolMap = new Map<string, Symbol>();\n  private metadataCache = new Map<string, ModuleMetadata>();\n  private exports = new Map<string, Symbol[]>();\n  private rootModule: string;\n  private exported: Set<Symbol>;\n\n  constructor(\n      private root: string, private importAs: string|undefined, private host: MetadataBundlerHost) {\n    this.rootModule = `./${path.basename(root)}`;\n  }\n\n  getMetadataBundle(): BundledModule {\n    // Export the root module. This also collects the transitive closure of all values referenced by\n    // the exports.\n    const exportedSymbols = this.exportAll(this.rootModule);\n    this.canonicalizeSymbols(exportedSymbols);\n    // TODO: exports? e.g. a module re-exports a symbol from another bundle\n    const metadata = this.getEntries(exportedSymbols);\n    const privates = Array.from(this.symbolMap.values())\n                         .filter(s => s.referenced && s.isPrivate)\n                         .map(s => ({\n                                privateName: s.privateName,\n                                name: s.declaration.name,\n                                module: s.declaration.module\n                              }));\n    const origins = Array.from(this.symbolMap.values())\n                        .filter(s => s.referenced)\n                        .reduce<{[name: string]: string}>((p, s) => {\n                          p[s.isPrivate ? s.privateName : s.name] = s.declaration.module;\n                          return p;\n                        }, {});\n    return {\n      metadata:\n          {__symbolic: 'module', version: VERSION, metadata, origins, importAs: this.importAs},\n      privates\n    };\n  }\n\n  static resolveModule(importName: string, from: string): string {\n    return resolveModule(importName, from);\n  }\n\n  private getMetadata(moduleName: string): ModuleMetadata {\n    let result = this.metadataCache.get(moduleName);\n    if (!result) {\n      if (moduleName.startsWith('.')) {\n        const fullModuleName = resolveModule(moduleName, this.root);\n        result = this.host.getMetadataFor(fullModuleName);\n      }\n      this.metadataCache.set(moduleName, result);\n    }\n    return result;\n  }\n\n  private exportAll(moduleName: string): Symbol[] {\n    const module = this.getMetadata(moduleName);\n    let result: Symbol[] = this.exports.get(moduleName);\n\n    if (result) {\n      return result;\n    }\n\n    result = [];\n\n    const exportSymbol = (exportedSymbol: Symbol, exportAs: string) => {\n      const symbol = this.symbolOf(moduleName, exportAs);\n      result.push(symbol);\n      exportedSymbol.reexportedAs = symbol;\n      symbol.exports = exportedSymbol;\n    };\n\n    // Export all the symbols defined in this module.\n    if (module && module.metadata) {\n      for (let key in module.metadata) {\n        const data = module.metadata[key];\n        if (isMetadataImportedSymbolReferenceExpression(data)) {\n          // This is a re-export of an imported symbol. Record this as a re-export.\n          const exportFrom = resolveModule(data.module, moduleName);\n          this.exportAll(exportFrom);\n          const symbol = this.symbolOf(exportFrom, data.name);\n          exportSymbol(symbol, key);\n        } else {\n          // Record that this symbol is exported by this module.\n          result.push(this.symbolOf(moduleName, key));\n        }\n      }\n    }\n\n    // Export all the re-exports from this module\n    if (module && module.exports) {\n      for (const exportDeclaration of module.exports) {\n        const exportFrom = resolveModule(exportDeclaration.from, moduleName);\n        // Record all the exports from the module even if we don't use it directly.\n        this.exportAll(exportFrom);\n        if (exportDeclaration.export) {\n          // Re-export all the named exports from a module.\n          for (const exportItem of exportDeclaration.export) {\n            const name = typeof exportItem == 'string' ? exportItem : exportItem.name;\n            const exportAs = typeof exportItem == 'string' ? exportItem : exportItem.as;\n            exportSymbol(this.symbolOf(exportFrom, name), exportAs);\n          }\n        } else {\n          // Re-export all the symbols from the module\n          const exportedSymbols = this.exportAll(exportFrom);\n          for (const exportedSymbol of exportedSymbols) {\n            const name = exportedSymbol.name;\n            exportSymbol(exportedSymbol, name);\n          }\n        }\n      }\n    }\n    this.exports.set(moduleName, result);\n\n    return result;\n  }\n\n  /**\n   * Fill in the canonicalSymbol which is the symbol that should be imported by factories.\n   * The canonical symbol is the one exported by the index file for the bundle or definition\n   * symbol for private symbols that are not exported by bundle index.\n   */\n  private canonicalizeSymbols(exportedSymbols: Symbol[]) {\n    const symbols = Array.from(this.symbolMap.values());\n    this.exported = new Set(exportedSymbols);\n    symbols.forEach(this.canonicalizeSymbol, this);\n  }\n\n  private canonicalizeSymbol(symbol: Symbol) {\n    const rootExport = getRootExport(symbol);\n    const declaration = getSymbolDeclaration(symbol);\n    const isPrivate = !this.exported.has(rootExport);\n    const canonicalSymbol = isPrivate ? declaration : rootExport;\n    symbol.isPrivate = isPrivate;\n    symbol.declaration = declaration;\n    symbol.canonicalSymbol = canonicalSymbol;\n  }\n\n  private getEntries(exportedSymbols: Symbol[]): BundleEntries {\n    const result: BundleEntries = {};\n\n    const exportedNames = new Set(exportedSymbols.map(s => s.name));\n    let privateName = 0;\n\n    function newPrivateName(): string {\n      while (true) {\n        let digits: string[] = [];\n        let index = privateName++;\n        let base = PRIVATE_NAME_CHARS;\n        while (!digits.length || index > 0) {\n          digits.unshift(base[index % base.length]);\n          index = Math.floor(index / base.length);\n        }\n        digits.unshift('\\u0275');\n        const result = digits.join('');\n        if (!exportedNames.has(result)) return result;\n      }\n    }\n\n    exportedSymbols.forEach(symbol => this.convertSymbol(symbol));\n\n    Array.from(this.symbolMap.values()).forEach(symbol => {\n      if (symbol.referenced) {\n        let name = symbol.name;\n        if (symbol.isPrivate && !symbol.privateName) {\n          name = newPrivateName();\n          symbol.privateName = name;\n        }\n        result[name] = symbol.value;\n      }\n    });\n\n    return result;\n  }\n\n  private convertSymbol(symbol: Symbol) {\n    const canonicalSymbol = symbol.canonicalSymbol;\n\n    if (!canonicalSymbol.referenced) {\n      canonicalSymbol.referenced = true;\n      const declaration = canonicalSymbol.declaration;\n      const module = this.getMetadata(declaration.module);\n      if (module) {\n        const value = module.metadata[declaration.name];\n        if (value && !declaration.name.startsWith('___')) {\n          canonicalSymbol.value = this.convertEntry(declaration.module, value);\n        }\n      }\n    }\n  }\n\n  private convertEntry(moduleName: string, value: MetadataEntry): MetadataEntry {\n    if (isClassMetadata(value)) {\n      return this.convertClass(moduleName, value);\n    }\n    if (isFunctionMetadata(value)) {\n      return this.convertFunction(moduleName, value);\n    }\n    if (isInterfaceMetadata(value)) {\n      return value;\n    }\n    return this.convertValue(moduleName, value);\n  }\n\n  private convertClass(moduleName: string, value: ClassMetadata): ClassMetadata {\n    return {\n      __symbolic: 'class',\n      arity: value.arity,\n      extends: this.convertExpression(moduleName, value.extends),\n      decorators:\n          value.decorators && value.decorators.map(d => this.convertExpression(moduleName, d)),\n      members: this.convertMembers(moduleName, value.members),\n      statics: value.statics && this.convertStatics(moduleName, value.statics)\n    };\n  }\n\n  private convertMembers(moduleName: string, members: MetadataMap): MetadataMap {\n    const result: MetadataMap = {};\n    for (const name in members) {\n      const value = members[name];\n      result[name] = value.map(v => this.convertMember(moduleName, v));\n    }\n    return result;\n  }\n\n  private convertMember(moduleName: string, member: MemberMetadata) {\n    const result: MemberMetadata = {__symbolic: member.__symbolic};\n    result.decorators =\n        member.decorators && member.decorators.map(d => this.convertExpression(moduleName, d));\n    if (isMethodMetadata(member)) {\n      (result as MethodMetadata).parameterDecorators = member.parameterDecorators &&\n          member.parameterDecorators.map(\n              d => d && d.map(p => this.convertExpression(moduleName, p)));\n      if (isConstructorMetadata(member)) {\n        if (member.parameters) {\n          (result as ConstructorMetadata).parameters =\n              member.parameters.map(p => this.convertExpression(moduleName, p));\n        }\n      }\n    }\n    return result;\n  }\n\n  private convertStatics(moduleName: string, statics: StaticsMetadata): StaticsMetadata {\n    let result: StaticsMetadata = {};\n    for (const key in statics) {\n      const value = statics[key];\n      result[key] = isFunctionMetadata(value) ? this.convertFunction(moduleName, value) : value;\n    }\n    return result;\n  }\n\n  private convertFunction(moduleName: string, value: FunctionMetadata): FunctionMetadata {\n    return {\n      __symbolic: 'function',\n      parameters: value.parameters,\n      defaults: value.defaults && value.defaults.map(v => this.convertValue(moduleName, v)),\n      value: this.convertValue(moduleName, value.value)\n    };\n  }\n\n  private convertValue(moduleName: string, value: MetadataValue): MetadataValue {\n    if (isPrimitive(value)) {\n      return value;\n    }\n    if (isMetadataError(value)) {\n      return this.convertError(moduleName, value);\n    }\n    if (isMetadataSymbolicExpression(value)) {\n      return this.convertExpression(moduleName, value);\n    }\n    if (Array.isArray(value)) {\n      return value.map(v => this.convertValue(moduleName, v));\n    }\n\n    // Otherwise it is a metadata object.\n    const object = value as MetadataObject;\n    const result: MetadataObject = {};\n    for (const key in object) {\n      result[key] = this.convertValue(moduleName, object[key]);\n    }\n    return result;\n  }\n\n  private convertExpression(\n      moduleName: string, value: MetadataSymbolicExpression|MetadataError|\n      undefined): MetadataSymbolicExpression|MetadataError|undefined {\n    if (value) {\n      switch (value.__symbolic) {\n        case 'error':\n          return this.convertError(moduleName, value as MetadataError);\n        case 'reference':\n          return this.convertReference(moduleName, value as MetadataSymbolicReferenceExpression);\n        default:\n          return this.convertExpressionNode(moduleName, value);\n      }\n    }\n    return value;\n  }\n\n  private convertError(module: string, value: MetadataError): MetadataError {\n    return {\n      __symbolic: 'error',\n      message: value.message,\n      line: value.line,\n      character: value.character,\n      context: value.context, module\n    };\n  }\n\n  private convertReference(moduleName: string, value: MetadataSymbolicReferenceExpression):\n      MetadataSymbolicReferenceExpression|MetadataError {\n    const createReference = (symbol: Symbol): MetadataSymbolicReferenceExpression => {\n      const declaration = symbol.declaration;\n      if (declaration.module.startsWith('.')) {\n        // Reference to a symbol defined in the module. Ensure it is converted then return a\n        // references to the final symbol.\n        this.convertSymbol(symbol);\n        return {\n          __symbolic: 'reference',\n          get name() {\n            // Resolved lazily because private names are assigned late.\n            const canonicalSymbol = symbol.canonicalSymbol;\n            if (canonicalSymbol.isPrivate == null) {\n              throw Error('Invalid state: isPrivate was not initialized');\n            }\n            return canonicalSymbol.isPrivate ? canonicalSymbol.privateName : canonicalSymbol.name;\n          }\n        };\n      } else {\n        // The symbol was a re-exported symbol from another module. Return a reference to the\n        // original imported symbol.\n        return {__symbolic: 'reference', name: declaration.name, module: declaration.module};\n      }\n    };\n\n    if (isMetadataGlobalReferenceExpression(value)) {\n      const metadata = this.getMetadata(moduleName);\n      if (metadata && metadata.metadata && metadata.metadata[value.name]) {\n        // Reference to a symbol defined in the module\n        return createReference(this.canonicalSymbolOf(moduleName, value.name));\n      }\n\n      // If a reference has arguments, the arguments need to be converted.\n      if (value.arguments) {\n        return {\n          __symbolic: 'reference',\n          name: value.name,\n          arguments: value.arguments.map(a => this.convertValue(moduleName, a))\n        };\n      }\n\n      // Global references without arguments (such as to Math or JSON) are unmodified.\n      return value;\n    }\n\n    if (isMetadataImportedSymbolReferenceExpression(value)) {\n      // References to imported symbols are separated into two, references to bundled modules and\n      // references to modules external to the bundle. If the module reference is relative it is\n      // assumed to be in the bundle. If it is Global it is assumed to be outside the bundle.\n      // References to symbols outside the bundle are left unmodified. References to symbol inside\n      // the bundle need to be converted to a bundle import reference reachable from the bundle\n      // index.\n\n      if (value.module.startsWith('.')) {\n        // Reference is to a symbol defined inside the module. Convert the reference to a reference\n        // to the canonical symbol.\n        const referencedModule = resolveModule(value.module, moduleName);\n        const referencedName = value.name;\n        return createReference(this.canonicalSymbolOf(referencedModule, referencedName));\n      }\n\n      // Value is a reference to a symbol defined outside the module.\n      if (value.arguments) {\n        // If a reference has arguments the arguments need to be converted.\n        return {\n          __symbolic: 'reference',\n          name: value.name,\n          module: value.module,\n          arguments: value.arguments.map(a => this.convertValue(moduleName, a))\n        };\n      }\n      return value;\n    }\n\n    if (isMetadataModuleReferenceExpression(value)) {\n      // Cannot support references to bundled modules as the internal modules of a bundle are erased\n      // by the bundler.\n      if (value.module.startsWith('.')) {\n        return {\n          __symbolic: 'error',\n          message: 'Unsupported bundled module reference',\n          context: {module: value.module}\n        };\n      }\n\n      // References to unbundled modules are unmodified.\n      return value;\n    }\n  }\n\n  private convertExpressionNode(moduleName: string, value: MetadataSymbolicExpression):\n      MetadataSymbolicExpression {\n    const result: MetadataSymbolicExpression = {__symbolic: value.__symbolic};\n    for (const key in value) {\n      (result as any)[key] = this.convertValue(moduleName, (value as any)[key]);\n    }\n    return result;\n  }\n\n  private symbolOf(module: string, name: string): Symbol {\n    const symbolKey = `${module}:${name}`;\n    let symbol = this.symbolMap.get(symbolKey);\n    if (!symbol) {\n      symbol = {module, name};\n      this.symbolMap.set(symbolKey, symbol);\n    }\n    return symbol;\n  }\n\n  private canonicalSymbolOf(module: string, name: string): Symbol {\n    // Ensure the module has been seen.\n    this.exportAll(module);\n    const symbol = this.symbolOf(module, name);\n    if (!symbol.canonicalSymbol) {\n      this.canonicalizeSymbol(symbol);\n    }\n    return symbol;\n  }\n}\n\nexport class CompilerHostAdapter implements MetadataBundlerHost {\n  private collector = new MetadataCollector();\n\n  constructor(private host: ts.CompilerHost) {}\n\n  getMetadataFor(fileName: string): ModuleMetadata {\n    const sourceFile = this.host.getSourceFile(fileName + '.ts', ts.ScriptTarget.Latest);\n    return this.collector.getMetadata(sourceFile);\n  }\n}\n\nfunction resolveModule(importName: string, from: string): string {\n  if (importName.startsWith('.') && from) {\n    let normalPath = path.normalize(path.join(path.dirname(from), importName));\n    if (!normalPath.startsWith('.') && from.startsWith('.')) {\n      // path.normalize() preserves leading '../' but not './'. This adds it back.\n      normalPath = `.${path.sep}${normalPath}`;\n    }\n    // Replace windows path delimiters with forward-slashes. Otherwise the paths are not\n    // TypeScript compatible when building the bundle.\n    return normalPath.replace(/\\\\/g, '/');\n  }\n  return importName;\n}\n\nfunction isPrimitive(o: any): o is boolean|string|number {\n  return o === null || (typeof o !== 'function' && typeof o !== 'object');\n}\n\nfunction getRootExport(symbol: Symbol): Symbol {\n  return symbol.reexportedAs ? getRootExport(symbol.reexportedAs) : symbol;\n}\n\nfunction getSymbolDeclaration(symbol: Symbol): Symbol {\n  return symbol.exports ? getSymbolDeclaration(symbol.exports) : symbol;\n}\n"]}