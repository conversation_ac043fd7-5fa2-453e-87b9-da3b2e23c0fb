{"_args": [["babel-types@6.25.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-types@6.25.0", "_id": "babel-types@6.25.0", "_inBundle": false, "_integrity": "sha1-cK+ySNVmDl0Y+BHZHIMDtUE0oY4=", "_location": "/babel-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-types@6.25.0", "name": "babel-types", "escapedName": "babel-types", "rawSpec": "6.25.0", "saveSpec": null, "fetchSpec": "6.25.0"}, "_requiredBy": ["/babel-generator", "/babel-template", "/babel-traverse", "/istanbul-lib-instrument"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-types/-/babel-types-6.25.0.tgz", "_spec": "6.25.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-runtime": "^6.22.0", "esutils": "^2.0.2", "lodash": "^4.2.0", "to-fast-properties": "^1.0.1"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "devDependencies": {"babylon": "^6.17.2"}, "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-types", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-types"}, "version": "6.25.0"}