{"_args": [["component-bind@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "component-bind@1.0.0", "_id": "component-bind@1.0.0", "_inBundle": false, "_integrity": "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E=", "_location": "/component-bind", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "component-bind@1.0.0", "name": "component-bind", "escapedName": "component-bind", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/component-bind/-/component-bind-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/component/bind/issues"}, "component": {"scripts": {"bind/index.js": "index.js"}}, "description": "function binding utility", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/component/bind#readme", "keywords": ["bind", "utility"], "name": "component-bind", "repository": {"type": "git", "url": "git+https://github.com/component/bind.git"}, "version": "1.0.0"}