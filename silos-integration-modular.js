/**
 * Silos Integration JavaScript Module for JSP
 * Converted from Angular TypeScript logic
 * 
 * Usage:
 * 1. Include this script in your JSP file
 * 2. Configure the userDataProvider and errorHandler functions
 * 3. Call SilosIntegration.init() or SilosIntegration.checkAndRedirect()
 */

var SilosIntegration = (function() {
    'use strict';

    // Configuration object - customize these based on your JSP environment
    var config = {
        // User data provider function - customize this
        userDataProvider: function() {
            // Default implementation - replace with your actual logic
            // In JSP, you might get this from session attributes, request parameters, etc.
            return {
                username: window.currentUser ? window.currentUser.username : 'default_user',
                profile: window.currentUser ? window.currentUser.profile : 'DEFAULT',
                branch: window.currentUser ? window.currentUser.branch : 'default_branch'
            };
        },

        // Error handler function - customize this
        errorHandler: function(message, title) {
            // Default implementation - replace with your preferred error handling
            console.error(title + ':', message);
            alert(title + ': ' + message);
        },

        // API endpoints
        authServiceUrl: 'https://ubz-uj.collaudo.usinet.it/UBZ-ESA-RS/service/userService/v1/users',
        silosAuthUrl: 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication',
        
        // Application code for authentication
        applicationCode: 'ZA0',
        
        // Profile that triggers Silos redirect
        targetProfile: 'BIOSUG'
    };

    /**
     * Set custom configuration
     */
    function configure(options) {
        if (options) {
            Object.keys(options).forEach(function(key) {
                if (config.hasOwnProperty(key)) {
                    config[key] = options[key];
                }
            });
        }
    }

    /**
     * Get user data using the configured provider
     */
    function getUserData() {
        return config.userDataProvider();
    }

    /**
     * Show error using the configured error handler
     */
    function showError(message, title) {
        config.errorHandler(message, title || 'Error');
    }

    /**
     * Main function to check user profile and redirect to Silos if needed
     */
    function checkAndRedirectToSilos() {
        var userData = getUserData();
        console.log('User data:', userData);
        console.log('User profile:', userData.profile);
       
        if (userData && userData.profile === config.targetProfile) {
            console.log('Profile matches target, redirecting to Silos...');
            redirectToSilos();
        } else {
            console.log('Profile does not match target (' + config.targetProfile + '), no redirect needed');
        }
    }

    /**
     * Redirect to Silos with authentication
     */
    async function redirectToSilos() {
        var userData = getUserData();
        console.log('Redirecting to Silos for user:', userData.username);

        try {
            var response = await getAuthData(
                userData.username,
                config.applicationCode,
                userData.branch
            );
            console.log('Auth response:', response);
            if (response && response !== null) {
                await dataToSilos(response);
            }
        } catch (error) {
            console.error('Authentication failed:', error);
            showError('Authentication failed: ' + (error.message || 'Unknown error'), 'Authentication Error');
        }
    }

    /**
     * Get authentication data from the server
     */
    async function getAuthData(userId, applicationCode, branchCode) {
        var url = config.authServiceUrl + '/' + userId + '/' + applicationCode + '/' + branchCode;
        console.log('Getting auth data from:', url);

        var headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };

        try {
            var response = await fetch(url, {
                method: 'GET',
                headers: headers,
                cache: 'no-cache'
            });

            if (!response.ok) {
                throw new Error('HTTP error! status: ' + response.status + ' - ' + response.statusText);
            }

            return await response.json();
        } catch (error) {
            console.error('Error in getAuthData:', error);
            throw error;
        }
    }

    /**
     * Send data to Silos authentication endpoint
     */
    async function dataToSilos(dataSilos) {
        console.log('Sending data to Silos:', dataSilos);

        var headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };

        var postData = JSON.stringify(dataSilos);

        try {
            var response = await fetch(config.silosAuthUrl, {
                method: 'POST',
                headers: headers,
                body: postData,
                credentials: 'include',
                cache: 'no-cache'
            });

            console.log('Silos response status:', response.status);

            if (response.status === 200) {
                console.log('Authentication successful, redirecting...');
                // Redirect to the response URL or the original URL
                window.location.replace(response.url || config.silosAuthUrl);
            } else {
                console.error('Authentication failed with status:', response.status);
                showError('Authentication failed with status: ' + response.status, 'Authentication Error');
            }
        } catch (error) {
            console.error('Authentication error:', error);
            showError('Authentication failed: ' + (error.message || 'Network error'), 'Authentication Error');
        }
    }

    /**
     * Initialize the integration (can be called on page load)
     */
    function init(options) {
        if (options) {
            configure(options);
        }
        
        // Auto-check and redirect if needed
        checkAndRedirectToSilos();
    }

    // Public API
    return {
        init: init,
        configure: configure,
        checkAndRedirect: checkAndRedirectToSilos,
        redirectToSilos: redirectToSilos,
        getUserData: getUserData
    };
})();

// Auto-initialize when DOM is ready (if not already initialized)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        // Only auto-init if not already configured
        if (typeof window.silosIntegrationConfigured === 'undefined') {
            SilosIntegration.init();
        }
    });
} else {
    // DOM is already ready
    if (typeof window.silosIntegrationConfigured === 'undefined') {
        SilosIntegration.init();
    }
}