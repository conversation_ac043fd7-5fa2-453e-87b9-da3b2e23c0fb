{"_args": [["blocking-proxy@0.0.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "blocking-proxy@0.0.5", "_id": "blocking-proxy@0.0.5", "_inBundle": false, "_integrity": "sha1-RikF4Nz76pcPQao3Ij3anAexkSs=", "_location": "/blocking-proxy", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "blocking-proxy@0.0.5", "name": "blocking-proxy", "escapedName": "blocking-proxy", "rawSpec": "0.0.5", "saveSpec": null, "fetchSpec": "0.0.5"}, "_requiredBy": ["/protractor"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/blocking-proxy/-/blocking-proxy-0.0.5.tgz", "_spec": "0.0.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/angular/jasminewd/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"minimist": "^1.2.0"}, "description": "WebDriver Proxy for testing rich clients. It block certain calls until Angular is done updating the page under test.", "devDependencies": {"@types/jasmine": "^2.2.33", "@types/minimist": "^1.1.29", "@types/nock": "^8.2.0", "@types/node": "^6.0.45", "@types/rimraf": "0.0.28", "@types/selenium-webdriver": "^2.53.39", "body-parser": "1.14.2", "clang-format": "^1.0.34", "gulp": "^3.9.1", "gulp-clang-format": "^1.0.23", "gulp-tslint": "^7.0.1", "jasmine": "2.3.2", "jasmine-co": "^1.2.2", "jasmine-ts": "0.0.3", "jshint": "2.9.1", "nock": "^9.0.2", "rimraf": "^2.5.4", "run-sequence": "^1.2.2", "selenium-mock": "^0.1.5", "selenium-webdriver": "3.0.1", "ts-node": "^2.0.0", "tslint": "^4.3.1", "tslint-eslint-rules": "^3.1.0", "typescript": "~2.0", "vrsource-tslint-rules": "^0.14.1", "webdriver-manager": "^10.2.2"}, "engines": {"node": ">=6.9.x"}, "homepage": "https://github.com/angular/blocking-proxy", "jshintConfig": {"esversion": 6}, "keywords": ["test", "testing", "webdriver", "webdriverjs", "selenium"], "license": "MIT", "main": "built/lib/index.js", "name": "blocking-proxy", "repository": {"type": "git", "url": "git://github.com/angular/jasminewd.git"}, "scripts": {"prepublish": "gulp prepublish", "start": "node built/lib/bin.js", "test": "JASMINE_CONFIG_PATH=spec/jasmine_unit.json jasmine-ts", "test:auto": "find lib spec | entr npm test", "test:e2e": "JASMINE_CONFIG_PATH=spec/jasmine_e2e.json jasmine-ts", "testapp": "cd testapp && npm start", "webdriver": "webdriver-manager update && webdriver-manager start"}, "typings": "built/lib/index.d.ts", "version": "0.0.5"}