{"version": 3, "file": "forms.js", "sources": ["../../../../packages/forms/index.ts", "../../../../packages/forms/public_api.ts", "../../../../packages/forms/src/forms.ts", "../../../../packages/forms/src/form_providers.ts", "../../../../packages/forms/src/directives.ts", "../../../../packages/forms/src/directives/ng_no_validate_directive.ts", "../../../../packages/forms/src/version.ts", "../../../../packages/forms/src/form_builder.ts", "../../../../packages/forms/src/directives/validators.ts", "../../../../packages/forms/src/directives/reactive_directives/form_control_name.ts", "../../../../packages/forms/src/directives/reactive_directives/form_group_name.ts", "../../../../packages/forms/src/directives/reactive_directives/form_group_directive.ts", "../../../../packages/forms/src/directives/reactive_directives/form_control_directive.ts", "../../../../packages/forms/src/directives/reactive_errors.ts", "../../../../packages/forms/src/directives/ng_model.ts", "../../../../packages/forms/src/directives/ng_model_group.ts", "../../../../packages/forms/src/directives/template_driven_errors.ts", "../../../../packages/forms/src/directives/error_examples.ts", "../../../../packages/forms/src/directives/ng_form.ts", "../../../../packages/forms/src/model.ts", "../../../../packages/forms/src/directives/ng_control_status.ts", "../../../../packages/forms/src/directives/abstract_form_group_directive.ts", "../../../../packages/forms/src/directives/shared.ts", "../../../../packages/forms/src/directives/select_multiple_control_value_accessor.ts", "../../../../packages/forms/src/directives/select_control_value_accessor.ts", "../../../../packages/forms/src/directives/range_value_accessor.ts", "../../../../packages/forms/src/directives/radio_control_value_accessor.ts", "../../../../packages/forms/src/directives/ng_control.ts", "../../../../packages/forms/src/directives/number_value_accessor.ts", "../../../../packages/forms/src/directives/normalize_validator.ts", "../../../../packages/forms/src/directives/default_value_accessor.ts", "../../../../packages/forms/src/directives/checkbox_value_accessor.ts", "../../../../packages/forms/src/directives/control_value_accessor.ts", "../../../../packages/forms/src/validators.ts", "../../../../packages/forms/src/directives/control_container.ts", "../../../../packages/forms/src/directives/abstract_control_directive.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {AbstractControlDirective,AbstractFormGroupDirective,CheckboxControlValueAccessor,ControlContainer,ControlValueAccessor,NG_VALUE_ACCESSOR,COMPOSITION_BUFFER_MODE,DefaultValueAccessor,Form,NgControl,NgControlStatus,NgControlStatusGroup,NgForm,NgModel,NgModelGroup,RadioControlValueAccessor,FormControlDirective,FormControlName,FormGroupDirective,FormArrayName,FormGroupName,NgSelectOption,SelectControlValueAccessor,SelectMultipleControlValueAccessor,AsyncValidator,AsyncValidatorFn,CheckboxRequiredValidator,EmailValidator,MaxLengthValidator,MinLengthValidator,PatternValidator,RequiredValidator,ValidationErrors,Validator,ValidatorFn,FormBuilder,AbstractControl,FormArray,FormControl,FormGroup,NG_ASYNC_VALIDATORS,NG_VALIDATORS,Validators,VERSION,FormsModule,ReactiveFormsModule} from './public_api';\n\nexport {InternalFormsSharedModule as ɵba,REACTIVE_DRIVEN_DIRECTIVES as ɵz,SHARED_FORM_DIRECTIVES as ɵx,TEMPLATE_DRIVEN_DIRECTIVES as ɵy} from './src/directives';\nexport {CHECKBOX_VALUE_ACCESSOR as ɵa} from './src/directives/checkbox_value_accessor';\nexport {DEFAULT_VALUE_ACCESSOR as ɵb} from './src/directives/default_value_accessor';\nexport {AbstractControlStatus as ɵc,ngControlStatusHost as ɵd} from './src/directives/ng_control_status';\nexport {formDirectiveProvider as ɵe} from './src/directives/ng_form';\nexport {formControlBinding as ɵf} from './src/directives/ng_model';\nexport {modelGroupProvider as ɵg} from './src/directives/ng_model_group';\nexport {NgNoValidate as ɵbf} from './src/directives/ng_no_validate_directive';\nexport {NUMBER_VALUE_ACCESSOR as ɵbb,NumberValueAccessor as ɵbc} from './src/directives/number_value_accessor';\nexport {RADIO_VALUE_ACCESSOR as ɵh,RadioControlRegistry as ɵi} from './src/directives/radio_control_value_accessor';\nexport {RANGE_VALUE_ACCESSOR as ɵbd,RangeValueAccessor as ɵbe} from './src/directives/range_value_accessor';\nexport {formControlBinding as ɵj} from './src/directives/reactive_directives/form_control_directive';\nexport {controlNameBinding as ɵk} from './src/directives/reactive_directives/form_control_name';\nexport {formDirectiveProvider as ɵl} from './src/directives/reactive_directives/form_group_directive';\nexport {formArrayNameProvider as ɵn,formGroupNameProvider as ɵm} from './src/directives/reactive_directives/form_group_name';\nexport {SELECT_VALUE_ACCESSOR as ɵo} from './src/directives/select_control_value_accessor';\nexport {NgSelectMultipleOption as ɵq,SELECT_MULTIPLE_VALUE_ACCESSOR as ɵp} from './src/directives/select_multiple_control_value_accessor';\nexport {CHECKBOX_REQUIRED_VALIDATOR as ɵs,EMAIL_VALIDATOR as ɵt,MAX_LENGTH_VALIDATOR as ɵv,MIN_LENGTH_VALIDATOR as ɵu,PATTERN_VALIDATOR as ɵw,REQUIRED_VALIDATOR as ɵr} from './src/directives/validators';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the forms package.\n */\nexport {AbstractControlDirective,AbstractFormGroupDirective,CheckboxControlValueAccessor,ControlContainer,ControlValueAccessor,NG_VALUE_ACCESSOR,COMPOSITION_BUFFER_MODE,DefaultValueAccessor,Form,NgControl,NgControlStatus,NgControlStatusGroup,NgForm,NgModel,NgModelGroup,RadioControlValueAccessor,FormControlDirective,FormControlName,FormGroupDirective,FormArrayName,FormGroupName,NgSelectOption,SelectControlValueAccessor,SelectMultipleControlValueAccessor,AsyncValidator,AsyncValidatorFn,CheckboxRequiredValidator,EmailValidator,MaxLengthValidator,MinLengthValidator,PatternValidator,RequiredValidator,ValidationErrors,Validator,ValidatorFn,FormBuilder,AbstractControl,FormArray,FormControl,FormGroup,NG_ASYNC_VALIDATORS,NG_VALIDATORS,Validators,VERSION,FormsModule,ReactiveFormsModule} from './src/forms';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module is used for handling user input, by defining and building a {@link FormGroup} that\n * consists of {@link FormControl} objects, and mapping them onto the DOM. {@link FormControl}\n * objects can then be used to read information from the form DOM elements.\n *\n * Forms providers are not included in default providers; you must import these providers\n * explicitly.\n */\n\n\nexport {AbstractControlDirective} from './directives/abstract_control_directive';\nexport {AbstractFormGroupDirective} from './directives/abstract_form_group_directive';\nexport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nexport {ControlContainer} from './directives/control_container';\nexport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './directives/control_value_accessor';\nexport {COMPOSITION_BUFFER_MODE, DefaultValueAccessor} from './directives/default_value_accessor';\nexport {Form} from './directives/form_interface';\nexport {NgControl} from './directives/ng_control';\nexport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nexport {NgForm} from './directives/ng_form';\nexport {NgModel} from './directives/ng_model';\nexport {NgModelGroup} from './directives/ng_model_group';\nexport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nexport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nexport {FormControlName} from './directives/reactive_directives/form_control_name';\nexport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nexport {FormArrayName} from './directives/reactive_directives/form_group_name';\nexport {FormGroupName} from './directives/reactive_directives/form_group_name';\nexport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nexport {SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\nexport {AsyncValidator, AsyncValidatorFn, CheckboxRequiredValidator, EmailValidator, MaxLengthValidator, MinLengthValidator, PatternValidator, RequiredValidator, ValidationErrors, Validator, ValidatorFn} from './directives/validators';\nexport {FormBuilder} from './form_builder';\nexport {AbstractControl, FormArray, FormControl, FormGroup} from './model';\nexport {NG_ASYNC_VALIDATORS, NG_VALIDATORS, Validators} from './validators';\nexport {VERSION} from './version';\n\nexport {FormsModule,ReactiveFormsModule} from './form_providers';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule} from '@angular/core';\n\nimport {InternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES, TEMPLATE_DRIVEN_DIRECTIVES} from './directives';\nimport {RadioControlRegistry} from './directives/radio_control_value_accessor';\nimport {FormBuilder} from './form_builder';\n/**\n * The ng module for forms.\n * \\@stable\n */\nexport class FormsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: TEMPLATE_DRIVEN_DIRECTIVES,\n  providers: [RadioControlRegistry],\n  exports: [InternalFormsSharedModule, TEMPLATE_DRIVEN_DIRECTIVES]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction FormsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nFormsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormsModule.ctorParameters;\n}\n\n/**\n * The ng module for reactive forms.\n * \\@stable\n */\nexport class ReactiveFormsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: [REACTIVE_DRIVEN_DIRECTIVES],\n  providers: [FormBuilder, RadioControlRegistry],\n  exports: [InternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction ReactiveFormsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nReactiveFormsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nReactiveFormsModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule, Type} from '@angular/core';\n\nimport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nimport {DefaultValueAccessor} from './directives/default_value_accessor';\nimport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nimport {NgForm} from './directives/ng_form';\nimport {NgModel} from './directives/ng_model';\nimport {NgModelGroup} from './directives/ng_model_group';\nimport {NgNoValidate} from './directives/ng_no_validate_directive';\nimport {NumberValueAccessor} from './directives/number_value_accessor';\nimport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nimport {RangeValueAccessor} from './directives/range_value_accessor';\nimport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nimport {FormControlName} from './directives/reactive_directives/form_control_name';\nimport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nimport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nimport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nimport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\nimport {CheckboxRequiredValidator, EmailValidator, MaxLengthValidator, MinLengthValidator, PatternValidator, RequiredValidator} from './directives/validators';\n\nexport {CheckboxControlValueAccessor} from './directives/checkbox_value_accessor';\nexport {ControlValueAccessor} from './directives/control_value_accessor';\nexport {DefaultValueAccessor} from './directives/default_value_accessor';\nexport {NgControl} from './directives/ng_control';\nexport {NgControlStatus, NgControlStatusGroup} from './directives/ng_control_status';\nexport {NgForm} from './directives/ng_form';\nexport {NgModel} from './directives/ng_model';\nexport {NgModelGroup} from './directives/ng_model_group';\nexport {NumberValueAccessor} from './directives/number_value_accessor';\nexport {RadioControlValueAccessor} from './directives/radio_control_value_accessor';\nexport {RangeValueAccessor} from './directives/range_value_accessor';\nexport {FormControlDirective} from './directives/reactive_directives/form_control_directive';\nexport {FormControlName} from './directives/reactive_directives/form_control_name';\nexport {FormGroupDirective} from './directives/reactive_directives/form_group_directive';\nexport {FormArrayName, FormGroupName} from './directives/reactive_directives/form_group_name';\nexport {NgSelectOption, SelectControlValueAccessor} from './directives/select_control_value_accessor';\nexport {NgSelectMultipleOption, SelectMultipleControlValueAccessor} from './directives/select_multiple_control_value_accessor';\n\nexport const /** @type {?} */ SHARED_FORM_DIRECTIVES: Type<any>[] = [\n  NgNoValidate,\n  NgSelectOption,\n  NgSelectMultipleOption,\n  DefaultValueAccessor,\n  NumberValueAccessor,\n  RangeValueAccessor,\n  CheckboxControlValueAccessor,\n  SelectControlValueAccessor,\n  SelectMultipleControlValueAccessor,\n  RadioControlValueAccessor,\n  NgControlStatus,\n  NgControlStatusGroup,\n  RequiredValidator,\n  MinLengthValidator,\n  MaxLengthValidator,\n  PatternValidator,\n  CheckboxRequiredValidator,\n  EmailValidator,\n];\n\nexport const /** @type {?} */ TEMPLATE_DRIVEN_DIRECTIVES: Type<any>[] = [NgModel, NgModelGroup, NgForm];\n\nexport const /** @type {?} */ REACTIVE_DRIVEN_DIRECTIVES: Type<any>[] =\n    [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName];\n/**\n * Internal module used for sharing directives between FormsModule and ReactiveFormsModule\n */\nexport class InternalFormsSharedModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: SHARED_FORM_DIRECTIVES,\n  exports: SHARED_FORM_DIRECTIVES,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction InternalFormsSharedModule_tsickle_Closure_declarations() {\n/** @type {?} */\nInternalFormsSharedModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nInternalFormsSharedModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive} from '@angular/core';\n/**\n * \\@whatItDoes Adds `novalidate` attribute to all forms by default.\n * \n * `novalidate` is used to disable browser's native form validation.\n * \n * If you want to use native validation with Angular forms, just add `ngNativeValidate` attribute:\n * \n * ```\n * <form ngNativeValidate></form>\n * ```\n * \n * \\@experimental\n */\nexport class NgNoValidate {\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: 'form:not([ngNoForm]):not([ngNativeValidate])',\n  host: {'novalidate': ''},\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction NgNoValidate_tsickle_Closure_declarations() {\n/** @type {?} */\nNgNoValidate.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgNoValidate.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {AsyncValidatorFn, ValidatorFn} from './directives/validators';\nimport {AbstractControl, FormArray, FormControl, FormGroup} from './model';\n/**\n * \\@whatItDoes Creates an {\\@link AbstractControl} from a user-specified configuration.\n * \n * It is essentially syntactic sugar that shortens the `new FormGroup()`,\n * `new FormControl()`, and `new FormArray()` boilerplate that can build up in larger\n * forms.\n * \n * \\@howToUse \n * \n * To use, inject `FormBuilder` into your component class. You can then call its methods\n * directly.\n * \n * {\\@example forms/ts/formBuilder/form_builder_example.ts region='Component'}\n * \n *  * **npm package**: `\\@angular/forms`\n * \n *  * **NgModule**: {\\@link ReactiveFormsModule}\n * \n * \\@stable\n */\nexport class FormBuilder {\n/**\n * Construct a new {\\@link FormGroup} with the given map of configuration.\n * Valid keys for the `extra` parameter map are `validator` and `asyncValidator`.\n * \n * See the {\\@link FormGroup} constructor for more details.\n * @param {?} controlsConfig\n * @param {?=} extra\n * @return {?}\n */\ngroup(controlsConfig: {[key: string]: any}, extra: {[key: string]: any}|null = null): FormGroup {\n    const /** @type {?} */ controls = this._reduceControls(controlsConfig);\n    const /** @type {?} */ validator: ValidatorFn = extra != null ? extra['validator'] : null;\n    const /** @type {?} */ asyncValidator: AsyncValidatorFn = extra != null ? extra['asyncValidator'] : null;\n    return new FormGroup(controls, validator, asyncValidator);\n  }\n/**\n * Construct a new {\\@link FormControl} with the given `formState`,`validator`, and\n * `asyncValidator`.\n * \n * `formState` can either be a standalone value for the form control or an object\n * that contains both a value and a disabled status.\n * \n * @param {?} formState\n * @param {?=} validator\n * @param {?=} asyncValidator\n * @return {?}\n */\ncontrol(\n      formState: Object, validator?: ValidatorFn|ValidatorFn[]|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null): FormControl {\n    return new FormControl(formState, validator, asyncValidator);\n  }\n/**\n * Construct a {\\@link FormArray} from the given `controlsConfig` array of\n * configuration, with the given optional `validator` and `asyncValidator`.\n * @param {?} controlsConfig\n * @param {?=} validator\n * @param {?=} asyncValidator\n * @return {?}\n */\narray(\n      controlsConfig: any[], validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null): FormArray {\n    const /** @type {?} */ controls = controlsConfig.map(c => this._createControl(c));\n    return new FormArray(controls, validator, asyncValidator);\n  }\n/**\n * \\@internal\n * @param {?} controlsConfig\n * @return {?}\n */\n_reduceControls(controlsConfig: {[k: string]: any}): {[key: string]: AbstractControl} {\n    const /** @type {?} */ controls: {[key: string]: AbstractControl} = {};\n    Object.keys(controlsConfig).forEach(controlName => {\n      controls[controlName] = this._createControl(controlsConfig[controlName]);\n    });\n    return controls;\n  }\n/**\n * \\@internal\n * @param {?} controlConfig\n * @return {?}\n */\n_createControl(controlConfig: any): AbstractControl {\n    if (controlConfig instanceof FormControl || controlConfig instanceof FormGroup ||\n        controlConfig instanceof FormArray) {\n      return controlConfig;\n\n    } else if (Array.isArray(controlConfig)) {\n      const /** @type {?} */ value = controlConfig[0];\n      const /** @type {?} */ validator: ValidatorFn = controlConfig.length > 1 ? controlConfig[1] : null;\n      const /** @type {?} */ asyncValidator: AsyncValidatorFn = controlConfig.length > 2 ? controlConfig[2] : null;\n      return this.control(value, validator, asyncValidator);\n\n    } else {\n      return this.control(controlConfig);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction FormBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nFormBuilder.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormBuilder.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Input, OnChanges, Provider, SimpleChanges, forwardRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {AbstractControl} from '../model';\nimport {NG_VALIDATORS, Validators} from '../validators';\n\n/** @experimental */\nexport type ValidationErrors = {\n  [key: string]: any\n};\n\n/**\n * An interface that can be implemented by classes that can act as validators.\n *\n * ## Usage\n *\n * ```typescript\n * @Directive({\n *   selector: '[custom-validator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: CustomValidatorDirective, multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(c: Control): {[key: string]: any} {\n *     return {\"custom\": true};\n *   }\n * }\n * ```\n *\n * @stable\n */\nexport interface Validator {\n  validate(c: AbstractControl): ValidationErrors|null;\n  registerOnValidatorChange?(fn: () => void): void;\n}\n\n/** @experimental */\nexport interface AsyncValidator extends Validator {\n  validate(c: AbstractControl): Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n\nexport const /** @type {?} */ REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => RequiredValidator),\n  multi: true\n};\n\nexport const /** @type {?} */ CHECKBOX_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => CheckboxRequiredValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `required` validator to any controls marked with the\n * `required` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input name=\"fullName\" ngModel required>\n * ```\n * \n * \\@stable\n */\nexport class RequiredValidator implements Validator {\nprivate _required: boolean;\nprivate _onChange: () => void;\n/**\n * @return {?}\n */\nget required(): boolean|string { return this._required; }\n/**\n * @param {?} value\n * @return {?}\n */\nset required(value: boolean|string) {\n    this._required = value != null && value !== false && `${value}` !== 'false';\n    if (this._onChange) this._onChange();\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.required ? Validators.required(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      ':not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]',\n  providers: [REQUIRED_VALIDATOR],\n  host: {'[attr.required]': 'required ? \"\" : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'required': [{ type: Input },],\n};\n}\n\nfunction RequiredValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nRequiredValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRequiredValidator.ctorParameters;\n/** @type {?} */\nRequiredValidator.propDecorators;\n/** @type {?} */\nRequiredValidator.prototype._required;\n/** @type {?} */\nRequiredValidator.prototype._onChange;\n}\n\n/**\n * A Directive that adds the `required` validator to checkbox controls marked with the\n * `required` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input type=\"checkbox\" name=\"active\" ngModel required>\n * ```\n * \n * \\@experimental\n */\nexport class CheckboxRequiredValidator extends RequiredValidator {\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.required ? Validators.requiredTrue(c) : null;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=checkbox][required][formControlName],input[type=checkbox][required][formControl],input[type=checkbox][required][ngModel]',\n  providers: [CHECKBOX_REQUIRED_VALIDATOR],\n  host: {'[attr.required]': 'required ? \"\" : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction CheckboxRequiredValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nCheckboxRequiredValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCheckboxRequiredValidator.ctorParameters;\n}\n\n/**\n * Provider which adds {\\@link EmailValidator} to {\\@link NG_VALIDATORS}.\n */\nexport const EMAIL_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => EmailValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `email` validator to controls marked with the\n * `email` attribute, via the {\\@link NG_VALIDATORS} binding.\n * \n * ### Example\n * \n * ```\n * <input type=\"email\" name=\"email\" ngModel email>\n * <input type=\"email\" name=\"email\" ngModel email=\"true\">\n * <input type=\"email\" name=\"email\" ngModel [email]=\"true\">\n * ```\n * \n * \\@experimental\n */\nexport class EmailValidator implements Validator {\nprivate _enabled: boolean;\nprivate _onChange: () => void;\n/**\n * @param {?} value\n * @return {?}\n */\nset email(value: boolean|string) {\n    this._enabled = value === '' || value === true || value === 'true';\n    if (this._onChange) this._onChange();\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this._enabled ? Validators.email(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[email][formControlName],[email][formControl],[email][ngModel]',\n  providers: [EMAIL_VALIDATOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'email': [{ type: Input },],\n};\n}\n\nfunction EmailValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nEmailValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nEmailValidator.ctorParameters;\n/** @type {?} */\nEmailValidator.propDecorators;\n/** @type {?} */\nEmailValidator.prototype._enabled;\n/** @type {?} */\nEmailValidator.prototype._onChange;\n}\n\n\n/**\n * @stable\n */\nexport interface ValidatorFn { (c: AbstractControl): ValidationErrors|null; }\n\n/**\n * @stable\n */\nexport interface AsyncValidatorFn {\n  (c: AbstractControl): Promise<ValidationErrors|null>|Observable<ValidationErrors|null>;\n}\n/**\n * Provider which adds {\\@link MinLengthValidator} to {\\@link NG_VALIDATORS}.\n * \n * ## Example:\n * \n * {\\@example common/forms/ts/validators/validators.ts region='min'}\n */\nexport const MIN_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinLengthValidator),\n  multi: true\n};\n/**\n * A directive which installs the {\\@link MinLengthValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `minlength` attribute.\n * \n * \\@stable\n */\nexport class MinLengthValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   minlength: string;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('minlength' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.minlength == null ? null : this._validator(c);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void {\n    this._validator = Validators.minLength(parseInt(this.minlength, 10));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[minlength][formControlName],[minlength][formControl],[minlength][ngModel]',\n  providers: [MIN_LENGTH_VALIDATOR],\n  host: {'[attr.minlength]': 'minlength ? minlength : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'minlength': [{ type: Input },],\n};\n}\n\nfunction MinLengthValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nMinLengthValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMinLengthValidator.ctorParameters;\n/** @type {?} */\nMinLengthValidator.propDecorators;\n/** @type {?} */\nMinLengthValidator.prototype._validator;\n/** @type {?} */\nMinLengthValidator.prototype._onChange;\n/** @type {?} */\nMinLengthValidator.prototype.minlength;\n}\n\n/**\n * Provider which adds {\\@link MaxLengthValidator} to {\\@link NG_VALIDATORS}.\n * \n * ## Example:\n * \n * {\\@example common/forms/ts/validators/validators.ts region='max'}\n */\nexport const MAX_LENGTH_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxLengthValidator),\n  multi: true\n};\n/**\n * A directive which installs the {\\@link MaxLengthValidator} for any `formControlName,\n * `formControl`,\n * or control with `ngModel` that also has a `maxlength` attribute.\n * \n * \\@stable\n */\nexport class MaxLengthValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   maxlength: string;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('maxlength' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null {\n    return this.maxlength != null ? this._validator(c) : null;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void {\n    this._validator = Validators.maxLength(parseInt(this.maxlength, 10));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]',\n  providers: [MAX_LENGTH_VALIDATOR],\n  host: {'[attr.maxlength]': 'maxlength ? maxlength : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'maxlength': [{ type: Input },],\n};\n}\n\nfunction MaxLengthValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nMaxLengthValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMaxLengthValidator.ctorParameters;\n/** @type {?} */\nMaxLengthValidator.propDecorators;\n/** @type {?} */\nMaxLengthValidator.prototype._validator;\n/** @type {?} */\nMaxLengthValidator.prototype._onChange;\n/** @type {?} */\nMaxLengthValidator.prototype.maxlength;\n}\n\n\n\nexport const /** @type {?} */ PATTERN_VALIDATOR: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => PatternValidator),\n  multi: true\n};\n/**\n * A Directive that adds the `pattern` validator to any controls marked with the\n * `pattern` attribute, via the {\\@link NG_VALIDATORS} binding. Uses attribute value\n * as the regex to validate Control value against.  Follows pattern attribute\n * semantics; i.e. regex must match entire Control value.\n * \n * ### Example\n * \n * ```\n * <input [name]=\"fullName\" pattern=\"[a-zA-Z ]*\" ngModel>\n * ```\n * \\@stable\n */\nexport class PatternValidator implements Validator,\n    OnChanges {\nprivate _validator: ValidatorFn;\nprivate _onChange: () => void;\n\n   pattern: string|RegExp;\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('pattern' in changes) {\n      this._createValidator();\n      if (this._onChange) this._onChange();\n    }\n  }\n/**\n * @param {?} c\n * @return {?}\n */\nvalidate(c: AbstractControl): ValidationErrors|null { return this._validator(c); }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnValidatorChange(fn: () => void): void { this._onChange = fn; }\n/**\n * @return {?}\n */\nprivate _createValidator(): void { this._validator = Validators.pattern(this.pattern); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[pattern][formControlName],[pattern][formControl],[pattern][ngModel]',\n  providers: [PATTERN_VALIDATOR],\n  host: {'[attr.pattern]': 'pattern ? pattern : null'}\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'pattern': [{ type: Input },],\n};\n}\n\nfunction PatternValidator_tsickle_Closure_declarations() {\n/** @type {?} */\nPatternValidator.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPatternValidator.ctorParameters;\n/** @type {?} */\nPatternValidator.propDecorators;\n/** @type {?} */\nPatternValidator.prototype._validator;\n/** @type {?} */\nPatternValidator.prototype._onChange;\n/** @type {?} */\nPatternValidator.prototype.pattern;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Self, SimpleChanges, SkipSelf, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, controlPath, isPropertyUpdated, selectValueAccessor} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nimport {FormGroupDirective} from './form_group_directive';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nexport const /** @type {?} */ controlNameBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlName)\n};\n/**\n * \\@whatItDoes Syncs a {\\@link FormControl} in an existing {\\@link FormGroup} to a form control\n * element by name.\n * \n * In other words, this directive ensures that any values written to the {\\@link FormControl}\n * instance programmatically will be written to the DOM element (model -> view). Conversely,\n * any values written to the DOM element through user input will be reflected in the\n * {\\@link FormControl} instance (view -> model).\n * \n * \\@howToUse \n * \n * This directive is designed to be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the {\\@link FormControl} instance you want to\n * link, and will look for a {\\@link FormControl} registered with that name in the\n * closest {\\@link FormGroup} or {\\@link FormArray} above it.\n * \n * **Access the control**: You can access the {\\@link FormControl} associated with\n * this directive by using the {\\@link AbstractControl#get} method.\n * Ex: `this.form.get('first');`\n * \n * **Get value**: the `value` property is always synced and available on the {\\@link FormControl}.\n * See a full list of available properties in {\\@link AbstractControl}.\n * \n *  **Set value**: You can set an initial value for the control when instantiating the\n *  {\\@link FormControl}, or you can set it programmatically later using\n *  {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the control, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * In this example, we create form controls for first name and last name.\n * \n * {\\@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n * \n * To see `formControlName` examples with different form control types, see:\n * \n * * Radio buttons: {\\@link RadioControlValueAccessor}\n * * Selects: {\\@link SelectControlValueAccessor}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: {\\@link ReactiveFormsModule}\n * \n *  \\@stable\n */\nexport class FormControlName extends NgControl implements OnChanges, OnDestroy {\nprivate _added = false;\n/**\n * \\@internal\n */\nviewModel: any;\n/**\n * \\@internal\n */\n_control: FormControl;\n\n   name: string;\n\n  // TODO(kara):  Replace ngModel with reactive API\n   model: any;\n   update = new EventEmitter();\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nset isDisabled(isDisabled: boolean) { ReactiveErrors.disabledAttrWarning(); }\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(\n         parent: ControlContainer,\n         validators: Array<Validator|ValidatorFn>,\n         asyncValidators:\n          Array<AsyncValidator|AsyncValidatorFn>,\n         valueAccessors: ControlValueAccessor[]) {\n    super();\n    this._parent = parent;\n    this._rawValidators = validators || [];\n    this._rawAsyncValidators = asyncValidators || [];\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    if (!this._added) this._setUpControl();\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      this.viewModel = this.model;\n      this.formDirective.updateModel(this, this.model);\n    }\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeControl(this);\n    }\n  }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n/**\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, /** @type {?} */(( this._parent))); }\n/**\n * @return {?}\n */\nget formDirective(): any { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn {\n    return /** @type {?} */(( composeAsyncValidators(this._rawAsyncValidators)));\n  }\n/**\n * @return {?}\n */\nget control(): FormControl { return this._control; }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n    if (!(this._parent instanceof FormGroupName) &&\n        this._parent instanceof AbstractFormGroupDirective) {\n      ReactiveErrors.ngModelGroupException();\n    } else if (\n        !(this._parent instanceof FormGroupName) && !(this._parent instanceof FormGroupDirective) &&\n        !(this._parent instanceof FormArrayName)) {\n      ReactiveErrors.controlParentException();\n    }\n  }\n/**\n * @return {?}\n */\nprivate _setUpControl() {\n    this._checkParentType();\n    this._control = this.formDirective.addControl(this);\n    if (this.control.disabled && /** @type {?} */(( this.valueAccessor)).setDisabledState) { /** @type {?} */(( /** @type {?} */((\n      this.valueAccessor)).setDisabledState))(true);\n    }\n    this._added = true;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControlName]', providers: [controlNameBinding]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formControlName', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n};\n}\n\nfunction FormControlName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormControlName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormControlName.ctorParameters;\n/** @type {?} */\nFormControlName.propDecorators;\n/** @type {?} */\nFormControlName.prototype._added;\n/**\n * \\@internal\n * @type {?}\n */\nFormControlName.prototype.viewModel;\n/**\n * \\@internal\n * @type {?}\n */\nFormControlName.prototype._control;\n/** @type {?} */\nFormControlName.prototype.name;\n/** @type {?} */\nFormControlName.prototype.model;\n/** @type {?} */\nFormControlName.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Host, Inject, Input, OnDestroy, OnInit, Optional, Self, SkipSelf, forwardRef} from '@angular/core';\n\nimport {FormArray} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {AbstractFormGroupDirective} from '../abstract_form_group_directive';\nimport {ControlContainer} from '../control_container';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, controlPath} from '../shared';\nimport {AsyncValidatorFn, ValidatorFn} from '../validators';\n\nimport {FormGroupDirective} from './form_group_directive';\n\nexport const /** @type {?} */ formGroupNameProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupName)\n};\n/**\n * \\@whatItDoes Syncs a nested {\\@link FormGroup} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive can only be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the nested {\\@link FormGroup} you want to link, and\n * will look for a {\\@link FormGroup} registered with that name in the parent\n * {\\@link FormGroup} instance you passed into {\\@link FormGroupDirective}.\n * \n * Nested form groups can come in handy when you want to validate a sub-group of a\n * form separately from the rest or when you'd like to group the values of certain\n * controls into their own nested object.\n * \n * **Access the group**: You can access the associated {\\@link FormGroup} using the\n * {\\@link AbstractControl#get} method. Ex: `this.form.get('name')`.\n * \n * You can also access individual controls within the group using dot syntax.\n * Ex: `this.form.get('name.first')`\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormGroup}. See a full list of available properties in {\\@link AbstractControl}.\n * \n * **Set the value**: You can set an initial value for each child control when instantiating\n * the {\\@link FormGroup}, or you can set it programmatically later using\n * {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the group, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * {\\@example forms/ts/nestedFormGroup/nested_form_group_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n * \\@stable\n */\nexport class FormGroupName extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {\n    if (_hasInvalidParent(this._parent)) {\n      ReactiveErrors.groupParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formGroupName]', providers: [formGroupNameProvider]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formGroupName', ] },],\n};\n}\n\nfunction FormGroupName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroupName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormGroupName.ctorParameters;\n/** @type {?} */\nFormGroupName.propDecorators;\n/** @type {?} */\nFormGroupName.prototype.name;\n}\n\n\nexport const /** @type {?} */ formArrayNameProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormArrayName)\n};\n/**\n * \\@whatItDoes Syncs a nested {\\@link FormArray} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive is designed to be used with a parent {\\@link FormGroupDirective} (selector:\n * `[formGroup]`).\n * \n * It accepts the string name of the nested {\\@link FormArray} you want to link, and\n * will look for a {\\@link FormArray} registered with that name in the parent\n * {\\@link FormGroup} instance you passed into {\\@link FormGroupDirective}.\n * \n * Nested form arrays can come in handy when you have a group of form controls but\n * you're not sure how many there will be. Form arrays allow you to create new\n * form controls dynamically.\n * \n * **Access the array**: You can access the associated {\\@link FormArray} using the\n * {\\@link AbstractControl#get} method on the parent {\\@link FormGroup}.\n * Ex: `this.form.get('cities')`.\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormArray}. See a full list of available properties in {\\@link AbstractControl}.\n * \n * **Set the value**: You can set an initial value for each child control when instantiating\n * the {\\@link FormArray}, or you can set the value programmatically later using the\n * {\\@link FormArray}'s {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue}\n * methods.\n * \n * **Listen to value**: If you want to listen to changes in the value of the array, you can\n * subscribe to the {\\@link FormArray}'s {\\@link AbstractControl#valueChanges} event.  You can also\n * listen to its {\\@link AbstractControl#statusChanges} event to be notified when the validation\n * status is re-calculated.\n * \n * **Add new controls**: You can add new controls to the {\\@link FormArray} dynamically by\n * calling its {\\@link FormArray#push} method.\n *  Ex: `this.form.get('cities').push(new FormControl());`\n * \n * ### Example\n * \n * {\\@example forms/ts/nestedFormArray/nested_form_array_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n * \\@stable\n */\nexport class FormArrayName extends ControlContainer implements OnInit, OnDestroy {\n/**\n * \\@internal\n */\n_parent: ControlContainer;\n/**\n * \\@internal\n */\n_validators: any[];\n/**\n * \\@internal\n */\n_asyncValidators: any[];\n\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._checkParentType(); /** @type {?} */((\n    this.formDirective)).addFormArray(this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeFormArray(this);\n    }\n  }\n/**\n * @return {?}\n */\nget control(): FormArray { return /** @type {?} */(( this.formDirective)).getFormArray(this); }\n/**\n * @return {?}\n */\nget formDirective(): FormGroupDirective|null {\n    return this._parent ? /** @type {?} */(( <FormGroupDirective>this._parent.formDirective)) : null;\n  }\n/**\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, this._parent); }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._validators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n    return composeAsyncValidators(this._asyncValidators);\n  }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n    if (_hasInvalidParent(this._parent)) {\n      ReactiveErrors.arrayParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formArrayName]', providers: [formArrayNameProvider]}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['formArrayName', ] },],\n};\n}\n\nfunction FormArrayName_tsickle_Closure_declarations() {\n/** @type {?} */\nFormArrayName.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormArrayName.ctorParameters;\n/** @type {?} */\nFormArrayName.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._parent;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._validators;\n/**\n * \\@internal\n * @type {?}\n */\nFormArrayName.prototype._asyncValidators;\n/** @type {?} */\nFormArrayName.prototype.name;\n}\n\n/**\n * @param {?} parent\n * @return {?}\n */\nfunction _hasInvalidParent(parent: ControlContainer): boolean {\n  return !(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) &&\n      !(parent instanceof FormArrayName);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Input, OnChanges, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\nimport {FormArray, FormControl, FormGroup} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS, Validators} from '../../validators';\nimport {ControlContainer} from '../control_container';\nimport {Form} from '../form_interface';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {cleanUpControl, composeAsyncValidators, composeValidators, setUpControl, setUpFormContainer} from '../shared';\n\nimport {FormControlName} from './form_control_name';\nimport {FormArrayName, FormGroupName} from './form_group_name';\n\nexport const /** @type {?} */ formDirectiveProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupDirective)\n};\n/**\n * \\@whatItDoes Binds an existing {\\@link FormGroup} to a DOM element.\n * \n * \\@howToUse \n * \n * This directive accepts an existing {\\@link FormGroup} instance. It will then use this\n * {\\@link FormGroup} instance to match any child {\\@link FormControl}, {\\@link FormGroup},\n * and {\\@link FormArray} instances to child {\\@link FormControlName}, {\\@link FormGroupName},\n * and {\\@link FormArrayName} directives.\n * \n * **Set value**: You can set the form's initial value when instantiating the\n * {\\@link FormGroup}, or you can set it programmatically later using the {\\@link FormGroup}'s\n * {\\@link AbstractControl#setValue} or {\\@link AbstractControl#patchValue} methods.\n * \n * **Listen to value**: If you want to listen to changes in the value of the form, you can subscribe\n * to the {\\@link FormGroup}'s {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * its {\\@link AbstractControl#statusChanges} event to be notified when the validation status is\n * re-calculated.\n * \n * Furthermore, you can listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event will be emitted with the original form\n * submission event.\n * \n * ### Example\n * \n * In this example, we create form controls for first name and last name.\n * \n * {\\@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: {\\@link ReactiveFormsModule}\n * \n *  \\@stable\n */\nexport class FormGroupDirective extends ControlContainer implements Form,\n    OnChanges {\nprivate _submitted: boolean = false;\nprivate _oldForm: FormGroup;\n  directives: FormControlName[] = [];\n\n   form: FormGroup = /** @type {?} */(( null));\n   ngSubmit = new EventEmitter();\n/**\n * @param {?} _validators\n * @param {?} _asyncValidators\n */\nconstructor(\nprivate _validators: any[],\nprivate _asyncValidators: any[]) {\n    super();\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    this._checkFormPresent();\n    if (changes.hasOwnProperty('form')) {\n      this._updateValidators();\n      this._updateDomValue();\n      this._updateRegistrations();\n    }\n  }\n/**\n * @return {?}\n */\nget submitted(): boolean { return this._submitted; }\n/**\n * @return {?}\n */\nget formDirective(): Form { return this; }\n/**\n * @return {?}\n */\nget control(): FormGroup { return this.form; }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @param {?} dir\n * @return {?}\n */\naddControl(dir: FormControlName): FormControl {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpControl(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n    this.directives.push(dir);\n    return ctrl;\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetControl(dir: FormControlName): FormControl { return /** @type {?} */(( <FormControl>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveControl(dir: FormControlName): void { remove(this.directives, dir); }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormGroup(dir: FormGroupName): void {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormGroup(dir: FormGroupName): void {}\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormGroup(dir: FormGroupName): FormGroup { return /** @type {?} */(( <FormGroup>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormArray(dir: FormArrayName): void {\n    const /** @type {?} */ ctrl: any = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    ctrl.updateValueAndValidity({emitEvent: false});\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormArray(dir: FormArrayName): void {}\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormArray(dir: FormArrayName): FormArray { return /** @type {?} */(( <FormArray>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @param {?} value\n * @return {?}\n */\nupdateModel(dir: FormControlName, value: any): void {\n    const /** @type {?} */ ctrl  = /** @type {?} */(( <FormControl>this.form.get(dir.path)));\n    ctrl.setValue(value);\n  }\n/**\n * @param {?} $event\n * @return {?}\n */\nonSubmit($event: Event): boolean {\n    this._submitted = true;\n    this.ngSubmit.emit($event);\n    return false;\n  }\n/**\n * @return {?}\n */\nonReset(): void { this.resetForm(); }\n/**\n * @param {?=} value\n * @return {?}\n */\nresetForm(value: any = undefined): void {\n    this.form.reset(value);\n    this._submitted = false;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateDomValue() {\n    this.directives.forEach(dir => {\n      const /** @type {?} */ newCtrl: any = this.form.get(dir.path);\n      if (dir._control !== newCtrl) {\n        cleanUpControl(dir._control, dir);\n        if (newCtrl) setUpControl(newCtrl, dir);\n        dir._control = newCtrl;\n      }\n    });\n\n    this.form._updateTreeValidity({emitEvent: false});\n  }\n/**\n * @return {?}\n */\nprivate _updateRegistrations() {\n    this.form._registerOnCollectionChange(() => this._updateDomValue());\n    if (this._oldForm) this._oldForm._registerOnCollectionChange(() => {});\n    this._oldForm = this.form;\n  }\n/**\n * @return {?}\n */\nprivate _updateValidators() {\n    const /** @type {?} */ sync = composeValidators(this._validators);\n    this.form.validator = Validators.compose([ /** @type {?} */((this.form.validator)), /** @type {?} */(( sync))]);\n\n    const /** @type {?} */ async = composeAsyncValidators(this._asyncValidators);\n    this.form.asyncValidator = Validators.composeAsync([ /** @type {?} */((this.form.asyncValidator)), /** @type {?} */(( async))]);\n  }\n/**\n * @return {?}\n */\nprivate _checkFormPresent() {\n    if (!this.form) {\n      ReactiveErrors.missingFormException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[formGroup]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  exportAs: 'ngForm'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'form': [{ type: Input, args: ['formGroup', ] },],\n'ngSubmit': [{ type: Output },],\n};\n}\n\nfunction FormGroupDirective_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroupDirective.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormGroupDirective.ctorParameters;\n/** @type {?} */\nFormGroupDirective.propDecorators;\n/** @type {?} */\nFormGroupDirective.prototype._submitted;\n/** @type {?} */\nFormGroupDirective.prototype._oldForm;\n/** @type {?} */\nFormGroupDirective.prototype.directives;\n/** @type {?} */\nFormGroupDirective.prototype.form;\n/** @type {?} */\nFormGroupDirective.prototype.ngSubmit;\n/** @type {?} */\nFormGroupDirective.prototype._validators;\n/** @type {?} */\nFormGroupDirective.prototype._asyncValidators;\n}\n\n/**\n * @template T\n * @param {?} list\n * @param {?} el\n * @return {?}\n */\nfunction remove<T>(list: T[], el: T): void {\n  const /** @type {?} */ index = list.indexOf(el);\n  if (index > -1) {\n    list.splice(index, 1);\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Input, OnChanges, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../../validators';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '../control_value_accessor';\nimport {NgControl} from '../ng_control';\nimport {ReactiveErrors} from '../reactive_errors';\nimport {composeAsyncValidators, composeValidators, isPropertyUpdated, selectValueAccessor, setUpControl} from '../shared';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from '../validators';\n\nexport const /** @type {?} */ formControlBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlDirective)\n};\n/**\n * \\@whatItDoes Syncs a standalone {\\@link FormControl} instance to a form control element.\n * \n * In other words, this directive ensures that any values written to the {\\@link FormControl}\n * instance programmatically will be written to the DOM element (model -> view). Conversely,\n * any values written to the DOM element through user input will be reflected in the\n * {\\@link FormControl} instance (view -> model).\n * \n * \\@howToUse \n * \n * Use this directive if you'd like to create and manage a {\\@link FormControl} instance directly.\n * Simply create a {\\@link FormControl}, save it to your component class, and pass it into the\n * {\\@link FormControlDirective}.\n * \n * This directive is designed to be used as a standalone control.  Unlike {\\@link FormControlName},\n * it does not require that your {\\@link FormControl} instance be part of any parent\n * {\\@link FormGroup}, and it won't be registered to any {\\@link FormGroupDirective} that\n * exists above it.\n * \n * **Get the value**: the `value` property is always synced and available on the\n * {\\@link FormControl} instance. See a full list of available properties in\n * {\\@link AbstractControl}.\n * \n * **Set the value**: You can pass in an initial value when instantiating the {\\@link FormControl},\n * or you can set it programmatically later using {\\@link AbstractControl#setValue} or\n * {\\@link AbstractControl#patchValue}.\n * \n * **Listen to value**: If you want to listen to changes in the value of the control, you can\n * subscribe to the {\\@link AbstractControl#valueChanges} event.  You can also listen to\n * {\\@link AbstractControl#statusChanges} to be notified when the validation status is\n * re-calculated.\n * \n * ### Example\n * \n * {\\@example forms/ts/simpleFormControl/simple_form_control_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `ReactiveFormsModule`\n * \n *  \\@stable\n */\nexport class FormControlDirective extends NgControl implements OnChanges {\n  viewModel: any;\n\n   form: FormControl;\n   model: any;\n   update = new EventEmitter();\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nset isDisabled(isDisabled: boolean) { ReactiveErrors.disabledAttrWarning(); }\n/**\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(   validators: Array<Validator|ValidatorFn>,\n                 asyncValidators: Array<AsyncValidator|AsyncValidatorFn>,\n                \n              valueAccessors: ControlValueAccessor[]) {\n                super();\n                this._rawValidators = validators || [];\n                this._rawAsyncValidators = asyncValidators || [];\n                this.valueAccessor = selectValueAccessor(this, valueAccessors);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n                if (this._isControlChanged(changes)) {\n                  setUpControl(this.form, this);\n                  if (this.control.disabled && /** @type {?} */(( this.valueAccessor)).setDisabledState) { /** @type {?} */(( /** @type {?} */((\n                    this.valueAccessor)).setDisabledState))(true);\n                  }\n                  this.form.updateValueAndValidity({emitEvent: false});\n                }\n                if (isPropertyUpdated(changes, this.viewModel)) {\n                  this.form.setValue(this.model);\n                  this.viewModel = this.model;\n                }\n              }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n                return composeAsyncValidators(this._rawAsyncValidators);\n              }\n/**\n * @return {?}\n */\nget control(): FormControl { return this.form; }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n                this.viewModel = newValue;\n                this.update.emit(newValue);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _isControlChanged(changes: {[key: string]: any}): boolean {\n                return changes.hasOwnProperty('form');\n              }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControl]', providers: [formControlBinding], exportAs: 'ngForm'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'form': [{ type: Input, args: ['formControl', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n};\n}\n\nfunction FormControlDirective_tsickle_Closure_declarations() {\n/** @type {?} */\nFormControlDirective.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nFormControlDirective.ctorParameters;\n/** @type {?} */\nFormControlDirective.propDecorators;\n/** @type {?} */\nFormControlDirective.prototype.viewModel;\n/** @type {?} */\nFormControlDirective.prototype.form;\n/** @type {?} */\nFormControlDirective.prototype.model;\n/** @type {?} */\nFormControlDirective.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {FormErrorExamples as Examples} from './error_examples';\nexport class ReactiveErrors {\n/**\n * @return {?}\n */\nstatic controlParentException(): void {\n    throw new Error(\n        `formControlName must be used with a parent formGroup directive.  You'll want to add a formGroup\n       directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${Examples.formControlName}`);\n  }\n/**\n * @return {?}\n */\nstatic ngModelGroupException(): void {\n    throw new Error(\n        `formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents\n       that also have a \"form\" prefix: formGroupName, formArrayName, or formGroup.\n\n       Option 1:  Update the parent to be formGroupName (reactive form strategy)\n\n        ${Examples.formGroupName}\n\n        Option 2: Use ngModel instead of formControlName (template-driven strategy)\n\n        ${Examples.ngModelGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic missingFormException(): void {\n    throw new Error(`formGroup expects a FormGroup instance. Please pass one in.\n\n       Example:\n\n       ${Examples.formControlName}`);\n  }\n/**\n * @return {?}\n */\nstatic groupParentException(): void {\n    throw new Error(\n        `formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${Examples.formGroupName}`);\n  }\n/**\n * @return {?}\n */\nstatic arrayParentException(): void {\n    throw new Error(\n        `formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup\n       directive and pass it an existing FormGroup instance (you can create one in your class).\n\n        Example:\n\n        ${Examples.formArrayName}`);\n  }\n/**\n * @return {?}\n */\nstatic disabledAttrWarning(): void {\n    console.warn(`\n      It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true\n      when you set up this control in your component class, the disabled attribute will actually be set in the DOM for\n      you. We recommend using this approach to avoid 'changed after checked' errors.\n       \n      Example: \n      form = new FormGroup({\n        first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),\n        last: new FormControl('Drew', Validators.required)\n      });\n    `);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Host, Inject, Input, OnChanges, OnDestroy, Optional, Output, Self, SimpleChanges, forwardRef} from '@angular/core';\n\nimport {FormControl} from '../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\nimport {NgForm} from './ng_form';\nimport {NgModelGroup} from './ng_model_group';\nimport {composeAsyncValidators, composeValidators, controlPath, isPropertyUpdated, selectValueAccessor, setUpControl} from './shared';\nimport {TemplateDrivenErrors} from './template_driven_errors';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n\nexport const /** @type {?} */ formControlBinding: any = {\n  provide: NgControl,\n  useExisting: forwardRef(() => NgModel)\n};\n/**\n * `ngModel` forces an additional change detection run when its inputs change:\n * E.g.:\n * ```\n * <div>{{myModel.valid}}</div>\n * <input [(ngModel)]=\"myValue\" #myModel=\"ngModel\">\n * ```\n * I.e. `ngModel` can export itself on the element and then be used in the template.\n * Normally, this would result in expressions before the `input` that use the exported directive\n * to have and old value as they have been\n * dirty checked before. As this is a very common case for `ngModel`, we added this second change\n * detection run.\n * \n * Notes:\n * - this is just one extra run no matter how many `ngModel` have been changed.\n * - this is a general problem when using `exportAs` for directives!\n */\nconst resolvedPromise = Promise.resolve(null);\n/**\n * \\@whatItDoes Creates a {\\@link FormControl} instance from a domain model and binds it\n * to a form control element.\n * \n * The {\\@link FormControl} instance will track the value, user interaction, and\n * validation status of the control and keep the view synced with the model. If used\n * within a parent form, the directive will also register itself with the form as a child\n * control.\n * \n * \\@howToUse \n * \n * This directive can be used by itself or as part of a larger form. All you need is the\n * `ngModel` selector to activate it.\n * \n * It accepts a domain model as an optional {\\@link Input}. If you have a one-way binding\n * to `ngModel` with `[]` syntax, changing the value of the domain model in the component\n * class will set the value in the view. If you have a two-way binding with `[()]` syntax\n * (also known as 'banana-box syntax'), the value in the UI will always be synced back to\n * the domain model in your class as well.\n * \n * If you wish to inspect the properties of the associated {\\@link FormControl} (like\n * validity state), you can also export the directive into a local template variable using\n * `ngModel` as the key (ex: `#myVar=\"ngModel\"`). You can then access the control using the\n * directive's `control` property, but most properties you'll need (like `valid` and `dirty`)\n * will fall through to the control anyway, so you can access them directly. You can see a\n * full list of properties directly available in {\\@link AbstractControlDirective}.\n * \n * The following is an example of a simple standalone control using `ngModel`:\n * \n * {\\@example forms/ts/simpleNgModel/simple_ng_model_example.ts region='Component'}\n * \n * When using the `ngModel` within `<form>` tags, you'll also need to supply a `name` attribute\n * so that the control can be registered with the parent form under that name.\n * \n * It's worth noting that in the context of a parent form, you often can skip one-way or\n * two-way binding because the parent form will sync the value for you. You can access\n * its properties by exporting it into a local template variable using `ngForm` (ex:\n * `#f=\"ngForm\"`). Then you can pass it where it needs to go on submit.\n * \n * If you do need to populate initial values into your form, using a one-way binding for\n * `ngModel` tends to be sufficient as long as you use the exported form's value rather\n * than the domain model's value on submit.\n * \n * Take a look at an example of using `ngModel` within a form:\n * \n * {\\@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n * \n * To see `ngModel` examples with different form control types, see:\n * \n * * Radio buttons: {\\@link RadioControlValueAccessor}\n * * Selects: {\\@link SelectControlValueAccessor}\n * \n * **npm package**: `\\@angular/forms`\n * \n * **NgModule**: `FormsModule`\n * \n *  \\@stable\n */\nexport class NgModel extends NgControl implements OnChanges,\n    OnDestroy {\n/**\n * \\@internal\n */\n_control = new FormControl();\n/**\n * \\@internal\n */\n_registered = false;\n  viewModel: any;\n\n   name: string;\n   isDisabled: boolean;\n   model: any;\n   options: {name?: string, standalone?: boolean};\n\n   update = new EventEmitter();\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n * @param {?} valueAccessors\n */\nconstructor(  parent: ControlContainer,\n                 validators: Array<Validator|ValidatorFn>,\n                 asyncValidators: Array<AsyncValidator|AsyncValidatorFn>,\n                \n              valueAccessors: ControlValueAccessor[]) {\n                super();\n                this._parent = parent;\n                this._rawValidators = validators || [];\n                this._rawAsyncValidators = asyncValidators || [];\n                this.valueAccessor = selectValueAccessor(this, valueAccessors);\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n                this._checkForErrors();\n                if (!this._registered) this._setUpControl();\n                if ('isDisabled' in changes) {\n                  this._updateDisabled(changes);\n                }\n\n                if (isPropertyUpdated(changes, this.viewModel)) {\n                  this._updateValue(this.model);\n                  this.viewModel = this.model;\n                }\n              }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.formDirective && this.formDirective.removeControl(this); }\n/**\n * @return {?}\n */\nget control(): FormControl { return this._control; }\n/**\n * @return {?}\n */\nget path(): string[] {\n                return this._parent ? controlPath(this.name, this._parent) : [this.name];\n              }\n/**\n * @return {?}\n */\nget formDirective(): any { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._rawValidators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n                return composeAsyncValidators(this._rawAsyncValidators);\n              }\n/**\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any): void {\n                this.viewModel = newValue;\n                this.update.emit(newValue);\n              }\n/**\n * @return {?}\n */\nprivate _setUpControl(): void {\n                this._isStandalone() ? this._setUpStandalone() :\n                                       this.formDirective.addControl(this);\n                this._registered = true;\n              }\n/**\n * @return {?}\n */\nprivate _isStandalone(): boolean {\n                return !this._parent || !!(this.options && this.options.standalone);\n              }\n/**\n * @return {?}\n */\nprivate _setUpStandalone(): void {\n                setUpControl(this._control, this);\n                this._control.updateValueAndValidity({emitEvent: false});\n              }\n/**\n * @return {?}\n */\nprivate _checkForErrors(): void {\n                if (!this._isStandalone()) {\n                  this._checkParentType();\n                }\n                this._checkName();\n              }\n/**\n * @return {?}\n */\nprivate _checkParentType(): void {\n                if (!(this._parent instanceof NgModelGroup) &&\n                    this._parent instanceof AbstractFormGroupDirective) {\n                  TemplateDrivenErrors.formGroupNameException();\n                } else if (\n                    !(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n                  TemplateDrivenErrors.modelParentException();\n                }\n              }\n/**\n * @return {?}\n */\nprivate _checkName(): void {\n                if (this.options && this.options.name) this.name = this.options.name;\n\n                if (!this._isStandalone() && !this.name) {\n                  TemplateDrivenErrors.missingNameException();\n                }\n              }\n/**\n * @param {?} value\n * @return {?}\n */\nprivate _updateValue(value: any): void {\n                resolvedPromise.then(\n                    () => { this.control.setValue(value, {emitViewToModelChange: false}); });\n              }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _updateDisabled(changes: SimpleChanges) {\n                const /** @type {?} */ disabledValue = changes['isDisabled'].currentValue;\n\n                const /** @type {?} */ isDisabled =\n                    disabledValue === '' || (disabledValue && disabledValue !== 'false');\n\n                resolvedPromise.then(() => {\n                  if (isDisabled && !this.control.disabled) {\n                    this.control.disable();\n                  } else if (!isDisabled && this.control.disabled) {\n                    this.control.enable();\n                  }\n                });\n              }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[ngModel]:not([formControlName]):not([formControl])',\n  providers: [formControlBinding],\n  exportAs: 'ngModel'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Optional }, { type: Host }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALUE_ACCESSOR, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input },],\n'isDisabled': [{ type: Input, args: ['disabled', ] },],\n'model': [{ type: Input, args: ['ngModel', ] },],\n'options': [{ type: Input, args: ['ngModelOptions', ] },],\n'update': [{ type: Output, args: ['ngModelChange', ] },],\n};\n}\n\nfunction NgModel_tsickle_Closure_declarations() {\n/** @type {?} */\nNgModel.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgModel.ctorParameters;\n/** @type {?} */\nNgModel.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nNgModel.prototype._control;\n/**\n * \\@internal\n * @type {?}\n */\nNgModel.prototype._registered;\n/** @type {?} */\nNgModel.prototype.viewModel;\n/** @type {?} */\nNgModel.prototype.name;\n/** @type {?} */\nNgModel.prototype.isDisabled;\n/** @type {?} */\nNgModel.prototype.model;\n/** @type {?} */\nNgModel.prototype.options;\n/** @type {?} */\nNgModel.prototype.update;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Host, Inject, Input, OnD<PERSON>roy, OnInit, Optional, Self, SkipSelf, forwardRef} from '@angular/core';\n\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {ControlContainer} from './control_container';\nimport {NgForm} from './ng_form';\nimport {TemplateDrivenErrors} from './template_driven_errors';\n\nexport const /** @type {?} */ modelGroupProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgModelGroup)\n};\n/**\n * \\@whatItDoes Creates and binds a {\\@link FormGroup} instance to a DOM element.\n * \n * \\@howToUse \n * \n * This directive can only be used as a child of {\\@link NgForm} (or in other words,\n * within `<form>` tags).\n * \n * Use this directive if you'd like to create a sub-group within a form. This can\n * come in handy if you want to validate a sub-group of your form separately from\n * the rest of your form, or if some values in your domain model make more sense to\n * consume together in a nested object.\n * \n * Pass in the name you'd like this sub-group to have and it will become the key\n * for the sub-group in the form's full value. You can also export the directive into\n * a local template variable using `ngModelGroup` (ex: `#myGroup=\"ngModelGroup\"`).\n * \n * {\\@example forms/ts/ngModelGroup/ng_model_group_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `FormsModule`\n * \n * \\@stable\n */\nexport class NgModelGroup extends AbstractFormGroupDirective implements OnInit, OnDestroy {\n   name: string;\n/**\n * @param {?} parent\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n        parent: ControlContainer,\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this._parent = parent;\n    this._validators = validators;\n    this._asyncValidators = asyncValidators;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {\n    if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm)) {\n      TemplateDrivenErrors.modelGroupParentException();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngModelGroup]', providers: [modelGroupProvider], exportAs: 'ngModelGroup'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Host }, { type: SkipSelf }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input, args: ['ngModelGroup', ] },],\n};\n}\n\nfunction NgModelGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nNgModelGroup.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgModelGroup.ctorParameters;\n/** @type {?} */\nNgModelGroup.propDecorators;\n/** @type {?} */\nNgModelGroup.prototype.name;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {FormErrorExamples as Examples} from './error_examples';\nexport class TemplateDrivenErrors {\n/**\n * @return {?}\n */\nstatic modelParentException(): void {\n    throw new Error(`\n      ngModel cannot be used to register form controls with a parent formGroup directive.  Try using\n      formGroup's partner directive \"formControlName\" instead.  Example:\n\n      ${Examples.formControlName}\n\n      Or, if you'd like to avoid registering this form control, indicate that it's standalone in ngModelOptions:\n\n      Example:\n\n      ${Examples.ngModelWithFormGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic formGroupNameException(): void {\n    throw new Error(`\n      ngModel cannot be used to register form controls with a parent formGroupName or formArrayName directive.\n\n      Option 1: Use formControlName instead of ngModel (reactive strategy):\n\n      ${Examples.formGroupName}\n\n      Option 2:  Update ngModel's parent be ngModelGroup (template-driven strategy):\n\n      ${Examples.ngModelGroup}`);\n  }\n/**\n * @return {?}\n */\nstatic missingNameException() {\n    throw new Error(\n        `If ngModel is used within a form tag, either the name attribute must be set or the form\n      control must be defined as 'standalone' in ngModelOptions.\n\n      Example 1: <input [(ngModel)]=\"person.firstName\" name=\"first\">\n      Example 2: <input [(ngModel)]=\"person.firstName\" [ngModelOptions]=\"{standalone: true}\">`);\n  }\n/**\n * @return {?}\n */\nstatic modelGroupParentException() {\n    throw new Error(`\n      ngModelGroup cannot be used with a parent formGroup directive.\n\n      Option 1: Use formGroupName instead of ngModelGroup (reactive strategy):\n\n      ${Examples.formGroupName}\n\n      Option 2:  Use a regular form tag instead of the formGroup directive (template-driven strategy):\n\n      ${Examples.ngModelGroup}`);\n  }\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const FormErrorExamples = {\n  formControlName: `\n    <div [formGroup]=\"myGroup\">\n      <input formControlName=\"firstName\">\n    </div>\n\n    In your class:\n\n    this.myGroup = new FormGroup({\n       firstName: new FormControl()\n    });`,\n\n  formGroupName: `\n    <div [formGroup]=\"myGroup\">\n       <div formGroupName=\"person\">\n          <input formControlName=\"firstName\">\n       </div>\n    </div>\n\n    In your class:\n\n    this.myGroup = new FormGroup({\n       person: new FormGroup({ firstName: new FormControl() })\n    });`,\n\n  formArrayName: `\n    <div [formGroup]=\"myGroup\">\n      <div formArrayName=\"cities\">\n        <div *ngFor=\"let city of cityArray.controls; index as i\">\n          <input [formControlName]=\"i\">\n        </div>\n      </div>\n    </div>\n\n    In your class:\n\n    this.cityArray = new FormArray([new FormControl('SF')]);\n    this.myGroup = new FormGroup({\n      cities: this.cityArray\n    });`,\n\n  ngModelGroup: `\n    <form>\n       <div ngModelGroup=\"person\">\n          <input [(ngModel)]=\"person.name\" name=\"firstName\">\n       </div>\n    </form>`,\n\n  ngModelWithFormGroup: `\n    <div [formGroup]=\"myGroup\">\n       <input formControlName=\"firstName\">\n       <input [(ngModel)]=\"showMoreControls\" [ngModelOptions]=\"{standalone: true}\">\n    </div>\n  `\n};\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EventEmitter, Inject, Optional, Self, forwardRef} from '@angular/core';\n\nimport {AbstractControl, FormControl, FormGroup} from '../model';\nimport {NG_ASYNC_VALIDATORS, NG_VALIDATORS} from '../validators';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {NgControl} from './ng_control';\nimport {NgModel} from './ng_model';\nimport {NgModelGroup} from './ng_model_group';\nimport {composeAsyncValidators, composeValidators, setUpControl, setUpFormContainer} from './shared';\n\nexport const /** @type {?} */ formDirectiveProvider: any = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgForm)\n};\n\nconst /** @type {?} */ resolvedPromise = Promise.resolve(null);\n/**\n * \\@whatItDoes Creates a top-level {\\@link FormGroup} instance and binds it to a form\n * to track aggregate form value and validation status.\n * \n * \\@howToUse \n * \n * As soon as you import the `FormsModule`, this directive becomes active by default on\n * all `<form>` tags.  You don't need to add a special selector.\n * \n * You can export the directive into a local template variable using `ngForm` as the key\n * (ex: `#myForm=\"ngForm\"`). This is optional, but useful.  Many properties from the underlying\n * {\\@link FormGroup} instance are duplicated on the directive itself, so a reference to it\n * will give you access to the aggregate value and validity status of the form, as well as\n * user interaction properties like `dirty` and `touched`.\n * \n * To register child controls with the form, you'll want to use {\\@link NgModel} with a\n * `name` attribute.  You can also use {\\@link NgModelGroup} if you'd like to create\n * sub-groups within the form.\n * \n * You can listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event will be emitted with the original form\n * submission event.\n * \n * {\\@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n * \n * * **npm package**: `\\@angular/forms`\n * \n * * **NgModule**: `FormsModule`\n * \n *  \\@stable\n */\nexport class NgForm extends ControlContainer implements Form {\nprivate _submitted: boolean = false;\n\n  form: FormGroup;\n  ngSubmit = new EventEmitter();\n/**\n * @param {?} validators\n * @param {?} asyncValidators\n */\nconstructor(\n         validators: any[],\n         asyncValidators: any[]) {\n    super();\n    this.form =\n        new FormGroup({}, composeValidators(validators), composeAsyncValidators(asyncValidators));\n  }\n/**\n * @return {?}\n */\nget submitted(): boolean { return this._submitted; }\n/**\n * @return {?}\n */\nget formDirective(): Form { return this; }\n/**\n * @return {?}\n */\nget control(): FormGroup { return this.form; }\n/**\n * @return {?}\n */\nget path(): string[] { return []; }\n/**\n * @return {?}\n */\nget controls(): {[key: string]: AbstractControl} { return this.form.controls; }\n/**\n * @param {?} dir\n * @return {?}\n */\naddControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      dir._control = /** @type {?} */(( <FormControl>container.registerControl(dir.name, dir.control)));\n      setUpControl(dir.control, dir);\n      dir.control.updateValueAndValidity({emitEvent: false});\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetControl(dir: NgModel): FormControl { return /** @type {?} */(( <FormControl>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveControl(dir: NgModel): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\naddFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      const /** @type {?} */ group = new FormGroup({});\n      setUpFormContainer(group, dir);\n      container.registerControl(dir.name, group);\n      group.updateValueAndValidity({emitEvent: false});\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\nremoveFormGroup(dir: NgModelGroup): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n/**\n * @param {?} dir\n * @return {?}\n */\ngetFormGroup(dir: NgModelGroup): FormGroup { return /** @type {?} */(( <FormGroup>this.form.get(dir.path))); }\n/**\n * @param {?} dir\n * @param {?} value\n * @return {?}\n */\nupdateModel(dir: NgControl, value: any): void {\n    resolvedPromise.then(() => {\n      const /** @type {?} */ ctrl = /** @type {?} */(( <FormControl>this.form.get( /** @type {?} */((dir.path)))));\n      ctrl.setValue(value);\n    });\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nsetValue(value: {[key: string]: any}): void { this.control.setValue(value); }\n/**\n * @param {?} $event\n * @return {?}\n */\nonSubmit($event: Event): boolean {\n    this._submitted = true;\n    this.ngSubmit.emit($event);\n    return false;\n  }\n/**\n * @return {?}\n */\nonReset(): void { this.resetForm(); }\n/**\n * @param {?=} value\n * @return {?}\n */\nresetForm(value: any = undefined): void {\n    this.form.reset(value);\n    this._submitted = false;\n  }\n/**\n * \\@internal\n * @param {?} path\n * @return {?}\n */\n_findContainer(path: string[]): FormGroup {\n    path.pop();\n    return path.length ? /** @type {?} */(( <FormGroup>this.form.get(path))) : this.form;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: 'form:not([ngNoForm]):not([formGroup]),ngForm,[ngForm]',\n  providers: [formDirectiveProvider],\n  host: {'(submit)': 'onSubmit($event)', '(reset)': 'onReset()'},\n  outputs: ['ngSubmit'],\n  exportAs: 'ngForm'\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_VALIDATORS, ] }, ]},\n{type: Array, decorators: [{ type: Optional }, { type: Self }, { type: Inject, args: [NG_ASYNC_VALIDATORS, ] }, ]},\n];\n}\n\nfunction NgForm_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForm.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgForm.ctorParameters;\n/** @type {?} */\nNgForm.prototype._submitted;\n/** @type {?} */\nNgForm.prototype.form;\n/** @type {?} */\nNgForm.prototype.ngSubmit;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {EventEmitter} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {composeAsyncValidators, composeValidators} from './directives/shared';\nimport {AsyncValidatorFn, ValidationErrors, ValidatorFn} from './directives/validators';\nimport {toObservable} from './validators';\n/**\n * Indicates that a FormControl is valid, i.e. that no errors exist in the input value.\n */\nexport const VALID = 'VALID';\n/**\n * Indicates that a FormControl is invalid, i.e. that an error exists in the input value.\n */\nexport const INVALID = 'INVALID';\n/**\n * Indicates that a FormControl is pending, i.e. that async validation is occurring and\n * errors are not yet available for the input value.\n */\nexport const PENDING = 'PENDING';\n/**\n * Indicates that a FormControl is disabled, i.e. that the control is exempt from ancestor\n * calculations of validity or value.\n */\nexport const DISABLED = 'DISABLED';\n/**\n * @param {?} control\n * @param {?} path\n * @param {?} delimiter\n * @return {?}\n */\nfunction _find(control: AbstractControl, path: Array<string|number>| string, delimiter: string) {\n  if (path == null) return null;\n\n  if (!(path instanceof Array)) {\n    path = ( /** @type {?} */((<string>path))).split(delimiter);\n  }\n  if (path instanceof Array && (path.length === 0)) return null;\n\n  return ( /** @type {?} */((<Array<string|number>>path))).reduce((v: AbstractControl, name) => {\n    if (v instanceof FormGroup) {\n      return v.controls[name] || null;\n    }\n\n    if (v instanceof FormArray) {\n      return v.at( /** @type {?} */((<number>name))) || null;\n    }\n\n    return null;\n  }, control);\n}\n/**\n * @param {?=} validator\n * @return {?}\n */\nfunction coerceToValidator(validator?: ValidatorFn | ValidatorFn[] | null): ValidatorFn|null {\n  return Array.isArray(validator) ? composeValidators(validator) : validator || null;\n}\n/**\n * @param {?=} asyncValidator\n * @return {?}\n */\nfunction coerceToAsyncValidator(asyncValidator?: AsyncValidatorFn | AsyncValidatorFn[] | null):\n    AsyncValidatorFn|null {\n  return Array.isArray(asyncValidator) ? composeAsyncValidators(asyncValidator) :\n                                         asyncValidator || null;\n}\n/**\n * \\@whatItDoes This is the base class for {\\@link FormControl}, {\\@link FormGroup}, and\n * {\\@link FormArray}.\n * \n * It provides some of the shared behavior that all controls and groups of controls have, like\n * running validators, calculating status, and resetting state. It also defines the properties\n * that are shared between all sub-classes, like `value`, `valid`, and `dirty`. It shouldn't be\n * instantiated directly.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class AbstractControl {\n/**\n * \\@internal\n */\n_value: any;\n/**\n * \\@internal\n */\n_onCollectionChange = () => {};\nprivate _valueChanges: EventEmitter<any>;\nprivate _statusChanges: EventEmitter<any>;\nprivate _status: string;\nprivate _errors: ValidationErrors|null;\nprivate _pristine: boolean = true;\nprivate _touched: boolean = false;\nprivate _parent: FormGroup|FormArray;\nprivate _asyncValidationSubscription: any;\n/**\n * @param {?} validator\n * @param {?} asyncValidator\n */\nconstructor(public validator: ValidatorFn|null,\npublic asyncValidator: AsyncValidatorFn|null) {}\n/**\n * The value of the control.\n * @return {?}\n */\nget value(): any { return this._value; }\n/**\n * The parent control.\n * @return {?}\n */\nget parent(): FormGroup|FormArray { return this._parent; }\n/**\n * The validation status of the control. There are four possible\n * validation statuses:\n * \n * * **VALID**:  control has passed all validation checks\n * * **INVALID**: control has failed at least one validation check\n * * **PENDING**: control is in the midst of conducting a validation check\n * * **DISABLED**: control is exempt from validation checks\n * \n * These statuses are mutually exclusive, so a control cannot be\n * both valid AND invalid or invalid AND disabled.\n * @return {?}\n */\nget status(): string { return this._status; }\n/**\n * A control is `valid` when its `status === VALID`.\n * \n * In order to have this status, the control must have passed all its\n * validation checks.\n * @return {?}\n */\nget valid(): boolean { return this._status === VALID; }\n/**\n * A control is `invalid` when its `status === INVALID`.\n * \n * In order to have this status, the control must have failed\n * at least one of its validation checks.\n * @return {?}\n */\nget invalid(): boolean { return this._status === INVALID; }\n/**\n * A control is `pending` when its `status === PENDING`.\n * \n * In order to have this status, the control must be in the\n * middle of conducting a validation check.\n * @return {?}\n */\nget pending(): boolean { return this._status == PENDING; }\n/**\n * A control is `disabled` when its `status === DISABLED`.\n * \n * Disabled controls are exempt from validation checks and\n * are not included in the aggregate value of their ancestor\n * controls.\n * @return {?}\n */\nget disabled(): boolean { return this._status === DISABLED; }\n/**\n * A control is `enabled` as long as its `status !== DISABLED`.\n * \n * In other words, it has a status of `VALID`, `INVALID`, or\n * `PENDING`.\n * @return {?}\n */\nget enabled(): boolean { return this._status !== DISABLED; }\n/**\n * Returns any errors generated by failing validation. If there\n * are no errors, it will return null.\n * @return {?}\n */\nget errors(): ValidationErrors|null { return this._errors; }\n/**\n * A control is `pristine` if the user has not yet changed\n * the value in the UI.\n * \n * Note that programmatic changes to a control's value will\n * *not* mark it dirty.\n * @return {?}\n */\nget pristine(): boolean { return this._pristine; }\n/**\n * A control is `dirty` if the user has changed the value\n * in the UI.\n * \n * Note that programmatic changes to a control's value will\n * *not* mark it dirty.\n * @return {?}\n */\nget dirty(): boolean { return !this.pristine; }\n/**\n * A control is marked `touched` once the user has triggered\n * a `blur` event on it.\n * @return {?}\n */\nget touched(): boolean { return this._touched; }\n/**\n * A control is `untouched` if the user has not yet triggered\n * a `blur` event on it.\n * @return {?}\n */\nget untouched(): boolean { return !this._touched; }\n/**\n * Emits an event every time the value of the control changes, in\n * the UI or programmatically.\n * @return {?}\n */\nget valueChanges(): Observable<any> { return this._valueChanges; }\n/**\n * Emits an event every time the validation status of the control\n * is re-calculated.\n * @return {?}\n */\nget statusChanges(): Observable<any> { return this._statusChanges; }\n/**\n * Sets the synchronous validators that are active on this control.  Calling\n * this will overwrite any existing sync validators.\n * @param {?} newValidator\n * @return {?}\n */\nsetValidators(newValidator: ValidatorFn|ValidatorFn[]|null): void {\n    this.validator = coerceToValidator(newValidator);\n  }\n/**\n * Sets the async validators that are active on this control. Calling this\n * will overwrite any existing async validators.\n * @param {?} newValidator\n * @return {?}\n */\nsetAsyncValidators(newValidator: AsyncValidatorFn|AsyncValidatorFn[]): void {\n    this.asyncValidator = coerceToAsyncValidator(newValidator);\n  }\n/**\n * Empties out the sync validator list.\n * @return {?}\n */\nclearValidators(): void { this.validator = null; }\n/**\n * Empties out the async validator list.\n * @return {?}\n */\nclearAsyncValidators(): void { this.asyncValidator = null; }\n/**\n * Marks the control as `touched`.\n * \n * This will also mark all direct ancestors as `touched` to maintain\n * the model.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsTouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = true;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsTouched(opts);\n    }\n  }\n/**\n * Marks the control as `untouched`.\n * \n * If the control has any children, it will also mark all children as `untouched`\n * to maintain the model, and re-calculate the `touched` status of all parent\n * controls.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsUntouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = false;\n\n    this._forEachChild(\n        (control: AbstractControl) => { control.markAsUntouched({onlySelf: true}); });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n/**\n * Marks the control as `dirty`.\n * \n * This will also mark all direct ancestors as `dirty` to maintain\n * the model.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsDirty(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = false;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsDirty(opts);\n    }\n  }\n/**\n * Marks the control as `pristine`.\n * \n * If the control has any children, it will also mark all children as `pristine`\n * to maintain the model, and re-calculate the `pristine` status of all parent\n * controls.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsPristine(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = true;\n\n    this._forEachChild((control: AbstractControl) => { control.markAsPristine({onlySelf: true}); });\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n/**\n * Marks the control as `pending`.\n * @param {?=} opts\n * @return {?}\n */\nmarkAsPending(opts: {onlySelf?: boolean} = {}): void {\n    this._status = PENDING;\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsPending(opts);\n    }\n  }\n/**\n * Disables the control. This means the control will be exempt from validation checks and\n * excluded from the aggregate value of any parent. Its status is `DISABLED`.\n * \n * If the control has children, all children will be disabled to maintain the model.\n * @param {?=} opts\n * @return {?}\n */\ndisable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._status = DISABLED;\n    this._errors = null;\n    this._forEachChild((control: AbstractControl) => { control.disable({onlySelf: true}); });\n    this._updateValue();\n\n    if (opts.emitEvent !== false) {\n      this._valueChanges.emit(this._value);\n      this._statusChanges.emit(this._status);\n    }\n\n    this._updateAncestors(!!opts.onlySelf);\n    this._onDisabledChange.forEach((changeFn) => changeFn(true));\n  }\n/**\n * Enables the control. This means the control will be included in validation checks and\n * the aggregate value of its parent. Its status is re-calculated based on its value and\n * its validators.\n * \n * If the control has children, all children will be enabled.\n * @param {?=} opts\n * @return {?}\n */\nenable(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._status = VALID;\n    this._forEachChild((control: AbstractControl) => { control.enable({onlySelf: true}); });\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n\n    this._updateAncestors(!!opts.onlySelf);\n    this._onDisabledChange.forEach((changeFn) => changeFn(false));\n  }\n/**\n * @param {?} onlySelf\n * @return {?}\n */\nprivate _updateAncestors(onlySelf: boolean) {\n    if (this._parent && !onlySelf) {\n      this._parent.updateValueAndValidity();\n      this._parent._updatePristine();\n      this._parent._updateTouched();\n    }\n  }\n/**\n * @param {?} parent\n * @return {?}\n */\nsetParent(parent: FormGroup|FormArray): void { this._parent = parent; }\n/**\n * Sets the value of the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any, options?: Object) {}\n/**\n * Patches the value of the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any, options?: Object) {}\n/**\n * Resets the control. Abstract method (implemented in sub-classes).\n * @abstract\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value?: any, options?: Object) {}\n/**\n * Re-calculates the value and validation status of the control.\n * \n * By default, it will also update the value and validity of its ancestors.\n * @param {?=} opts\n * @return {?}\n */\nupdateValueAndValidity(opts: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._setInitialStatus();\n    this._updateValue();\n\n    if (this.enabled) {\n      this._cancelExistingSubscription();\n      this._errors = this._runValidator();\n      this._status = this._calculateStatus();\n\n      if (this._status === VALID || this._status === PENDING) {\n        this._runAsyncValidator(opts.emitEvent);\n      }\n    }\n\n    if (opts.emitEvent !== false) {\n      this._valueChanges.emit(this._value);\n      this._statusChanges.emit(this._status);\n    }\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n    }\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updateTreeValidity(opts: {emitEvent?: boolean} = {emitEvent: true}) {\n    this._forEachChild((ctrl: AbstractControl) => ctrl._updateTreeValidity(opts));\n    this.updateValueAndValidity({onlySelf: true, emitEvent: opts.emitEvent});\n  }\n/**\n * @return {?}\n */\nprivate _setInitialStatus() { this._status = this._allControlsDisabled() ? DISABLED : VALID; }\n/**\n * @return {?}\n */\nprivate _runValidator(): ValidationErrors|null {\n    return this.validator ? this.validator(this) : null;\n  }\n/**\n * @param {?=} emitEvent\n * @return {?}\n */\nprivate _runAsyncValidator(emitEvent?: boolean): void {\n    if (this.asyncValidator) {\n      this._status = PENDING;\n      const /** @type {?} */ obs = toObservable(this.asyncValidator(this));\n      this._asyncValidationSubscription =\n          obs.subscribe((errors: ValidationErrors | null) => this.setErrors(errors, {emitEvent}));\n    }\n  }\n/**\n * @return {?}\n */\nprivate _cancelExistingSubscription(): void {\n    if (this._asyncValidationSubscription) {\n      this._asyncValidationSubscription.unsubscribe();\n    }\n  }\n/**\n * Sets errors on a form control.\n * \n * This is used when validations are run manually by the user, rather than automatically.\n * \n * Calling `setErrors` will also update the validity of the parent control.\n * \n * ### Example\n * \n * ```\n * const login = new FormControl(\"someLogin\");\n * login.setErrors({\n *   \"notUnique\": true\n * });\n * \n * expect(login.valid).toEqual(false);\n * expect(login.errors).toEqual({\"notUnique\": true});\n * \n * login.setValue(\"someOtherLogin\");\n * \n * expect(login.valid).toEqual(true);\n * ```\n * @param {?} errors\n * @param {?=} opts\n * @return {?}\n */\nsetErrors(errors: ValidationErrors|null, opts: {emitEvent?: boolean} = {}): void {\n    this._errors = errors;\n    this._updateControlsErrors(opts.emitEvent !== false);\n  }\n/**\n * Retrieves a child control given the control's name or path.\n * \n * Paths can be passed in as an array or a string delimited by a dot.\n * \n * To get a control nested within a `person` sub-group:\n * \n * * `this.form.get('person.name');`\n * \n * -OR-\n * \n * * `this.form.get(['person', 'name']);`\n * @param {?} path\n * @return {?}\n */\nget(path: Array<string|number>|string): AbstractControl|null { return _find(this, path, '.'); }\n/**\n * Returns true if the control with the given path has the error specified. Otherwise\n * returns null or undefined.\n * \n * If no path is given, it checks for the error on the present control.\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\ngetError(errorCode: string, path?: string[]): any {\n    const /** @type {?} */ control = path ? this.get(path) : this;\n    return control && control._errors ? control._errors[errorCode] : null;\n  }\n/**\n * Returns true if the control with the given path has the error specified. Otherwise\n * returns false.\n * \n * If no path is given, it checks for the error on the present control.\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\nhasError(errorCode: string, path?: string[]): boolean { return !!this.getError(errorCode, path); }\n/**\n * Retrieves the top-level ancestor of this control.\n * @return {?}\n */\nget root(): AbstractControl {\n    let /** @type {?} */ x: AbstractControl = this;\n\n    while (x._parent) {\n      x = x._parent;\n    }\n\n    return x;\n  }\n/**\n * \\@internal\n * @param {?} emitEvent\n * @return {?}\n */\n_updateControlsErrors(emitEvent: boolean): void {\n    this._status = this._calculateStatus();\n\n    if (emitEvent) {\n      this._statusChanges.emit(this._status);\n    }\n\n    if (this._parent) {\n      this._parent._updateControlsErrors(emitEvent);\n    }\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_initObservables() {\n    this._valueChanges = new EventEmitter();\n    this._statusChanges = new EventEmitter();\n  }\n/**\n * @return {?}\n */\nprivate _calculateStatus(): string {\n    if (this._allControlsDisabled()) return DISABLED;\n    if (this._errors) return INVALID;\n    if (this._anyControlsHaveStatus(PENDING)) return PENDING;\n    if (this._anyControlsHaveStatus(INVALID)) return INVALID;\n    return VALID;\n  }\n/**\n * \\@internal\n * @abstract\n * @return {?}\n */\n_updateValue() {}\n/**\n * \\@internal\n * @abstract\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function) {}\n/**\n * \\@internal\n * @abstract\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function) {}\n/**\n * \\@internal\n * @abstract\n * @return {?}\n */\n_allControlsDisabled() {}\n/**\n * \\@internal\n * @param {?} status\n * @return {?}\n */\n_anyControlsHaveStatus(status: string): boolean {\n    return this._anyControls((control: AbstractControl) => control.status === status);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_anyControlsDirty(): boolean {\n    return this._anyControls((control: AbstractControl) => control.dirty);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_anyControlsTouched(): boolean {\n    return this._anyControls((control: AbstractControl) => control.touched);\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updatePristine(opts: {onlySelf?: boolean} = {}): void {\n    this._pristine = !this._anyControlsDirty();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts);\n    }\n  }\n/**\n * \\@internal\n * @param {?=} opts\n * @return {?}\n */\n_updateTouched(opts: {onlySelf?: boolean} = {}): void {\n    this._touched = this._anyControlsTouched();\n\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts);\n    }\n  }\n/**\n * \\@internal\n */\n_onDisabledChange: Function[] = [];\n/**\n * \\@internal\n * @param {?} formState\n * @return {?}\n */\n_isBoxedValue(formState: any): boolean {\n    return typeof formState === 'object' && formState !== null &&\n        Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;\n  }\n/**\n * \\@internal\n * @param {?} fn\n * @return {?}\n */\n_registerOnCollectionChange(fn: () => void): void { this._onCollectionChange = fn; }\n}\n\nfunction AbstractControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._value;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._onCollectionChange;\n/** @type {?} */\nAbstractControl.prototype._valueChanges;\n/** @type {?} */\nAbstractControl.prototype._statusChanges;\n/** @type {?} */\nAbstractControl.prototype._status;\n/** @type {?} */\nAbstractControl.prototype._errors;\n/** @type {?} */\nAbstractControl.prototype._pristine;\n/** @type {?} */\nAbstractControl.prototype._touched;\n/** @type {?} */\nAbstractControl.prototype._parent;\n/** @type {?} */\nAbstractControl.prototype._asyncValidationSubscription;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractControl.prototype._onDisabledChange;\n/** @type {?} */\nAbstractControl.prototype.validator;\n/** @type {?} */\nAbstractControl.prototype.asyncValidator;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validation status of an individual form control.\n * \n * It is one of the three fundamental building blocks of Angular forms, along with\n * {\\@link FormGroup} and {\\@link FormArray}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormControl}, you can pass in an initial value as the\n * first argument. Example:\n * \n * ```ts\n * const ctrl = new FormControl('some value');\n * console.log(ctrl.value);     // 'some value'\n * ```\n * \n * You can also initialize the control with a form state object on instantiation,\n * which includes both the value and whether or not the control is disabled.\n * You can't use the value key without the disabled key; both are required\n * to use this way of initialization.\n * \n * ```ts\n * const ctrl = new FormControl({value: 'n/a', disabled: true});\n * console.log(ctrl.value);     // 'n/a'\n * console.log(ctrl.status);   // 'DISABLED'\n * ```\n * \n * To include a sync validator (or an array of sync validators) with the control,\n * pass it in as the second argument. Async validators are also supported, but\n * have to be passed in separately as the third arg.\n * \n * ```ts\n * const ctrl = new FormControl('', Validators.required);\n * console.log(ctrl.value);     // ''\n * console.log(ctrl.status);   // 'INVALID'\n * ```\n * \n * See its superclass, {\\@link AbstractControl}, for more properties and methods.\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormControl extends AbstractControl {\n/**\n * \\@internal\n */\n_onChange: Function[] = [];\n/**\n * @param {?=} formState\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\n      formState: any = null, validator?: ValidatorFn|ValidatorFn[]|null,\n      asyncValidator?: AsyncValidatorFn|AsyncValidatorFn[]|null) {\n    super(coerceToValidator(validator), coerceToAsyncValidator(asyncValidator));\n    this._applyFormState(formState);\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n    this._initObservables();\n  }\n/**\n * Set the value of the form control to `value`.\n * \n * If `onlySelf` is `true`, this change will only affect the validation of this `FormControl`\n * and not its parent component. This defaults to false.\n * \n * If `emitEvent` is `true`, this\n * change will cause a `valueChanges` event on the `FormControl` to be emitted. This defaults\n * to true (as it falls through to `updateValueAndValidity`).\n * \n * If `emitModelToViewChange` is `true`, the view will be notified about the new value\n * via an `onChange` event. This is the default behavior if `emitModelToViewChange` is not\n * specified.\n * \n * If `emitViewToModelChange` is `true`, an ngModelChange event will be fired to update the\n * model.  This is the default behavior if `emitViewToModelChange` is not specified.\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  } = {}): void {\n    this._value = value;\n    if (this._onChange.length && options.emitModelToViewChange !== false) {\n      this._onChange.forEach(\n          (changeFn) => changeFn(this._value, options.emitViewToModelChange !== false));\n    }\n    this.updateValueAndValidity(options);\n  }\n/**\n * Patches the value of a control.\n * \n * This function is functionally the same as {\\@link FormControl#setValue} at this level.\n * It exists for symmetry with {\\@link FormGroup#patchValue} on `FormGroups` and `FormArrays`,\n * where it does behave differently.\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any, options: {\n    onlySelf?: boolean,\n    emitEvent?: boolean,\n    emitModelToViewChange?: boolean,\n    emitViewToModelChange?: boolean\n  } = {}): void {\n    this.setValue(value, options);\n  }\n/**\n * Resets the form control. This means by default:\n * \n * * it is marked as `pristine`\n * * it is marked as `untouched`\n * * value is set to null\n * \n * You can also reset to a specific form state by passing through a standalone\n * value or a form state object that contains both a value and a disabled state\n * (these are the only two properties that cannot be calculated).\n * \n * Ex:\n * \n * ```ts\n * this.control.reset('Nancy');\n * \n * console.log(this.control.value);  // 'Nancy'\n * ```\n * \n * OR\n * \n * ```\n * this.control.reset({value: 'Nancy', disabled: true});\n * \n * console.log(this.control.value);  // 'Nancy'\n * console.log(this.control.status);  // 'DISABLED'\n * ```\n * @param {?=} formState\n * @param {?=} options\n * @return {?}\n */\nreset(formState: any = null, options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._applyFormState(formState);\n    this.markAsPristine(options);\n    this.markAsUntouched(options);\n    this.setValue(this._value, options);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue() {}\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean { return false; }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean { return this.disabled; }\n/**\n * Register a listener for change events.\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: Function): void { this._onChange.push(fn); }\n/**\n * \\@internal\n * @return {?}\n */\n_clearChangeFns(): void {\n    this._onChange = [];\n    this._onDisabledChange = [];\n    this._onCollectionChange = () => {};\n  }\n/**\n * Register a listener for disabled events.\n * @param {?} fn\n * @return {?}\n */\nregisterOnDisabledChange(fn: (isDisabled: boolean) => void): void {\n    this._onDisabledChange.push(fn);\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function): void {}\n/**\n * @param {?} formState\n * @return {?}\n */\nprivate _applyFormState(formState: any) {\n    if (this._isBoxedValue(formState)) {\n      this._value = formState.value;\n      formState.disabled ? this.disable({onlySelf: true, emitEvent: false}) :\n                           this.enable({onlySelf: true, emitEvent: false});\n    } else {\n      this._value = formState;\n    }\n  }\n}\n\nfunction FormControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nFormControl.prototype._onChange;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validity state of a group of {\\@link FormControl}\n * instances.\n * \n * A `FormGroup` aggregates the values of each child {\\@link FormControl} into one object,\n * with each control name as the key.  It calculates its status by reducing the statuses\n * of its children. For example, if one of the controls in a group is invalid, the entire\n * group becomes invalid.\n * \n * `FormGroup` is one of the three fundamental building blocks used to define forms in Angular,\n * along with {\\@link FormControl} and {\\@link FormArray}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormGroup}, pass in a collection of child controls as the first\n * argument. The key for each child will be the name under which it is registered.\n * \n * ### Example\n * \n * ```\n * const form = new FormGroup({\n *   first: new FormControl('Nancy', Validators.minLength(2)),\n *   last: new FormControl('Drew'),\n * });\n * \n * console.log(form.value);   // {first: 'Nancy', last; 'Drew'}\n * console.log(form.status);  // 'VALID'\n * ```\n * \n * You can also include group-level validators as the second arg, or group-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n * \n * ### Example\n * \n * ```\n * const form = new FormGroup({\n *   password: new FormControl('', Validators.minLength(2)),\n *   passwordConfirm: new FormControl('', Validators.minLength(2)),\n * }, passwordMatchValidator);\n * \n * \n * function passwordMatchValidator(g: FormGroup) {\n *    return g.get('password').value === g.get('passwordConfirm').value\n *       ? null : {'mismatch': true};\n * }\n * ```\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormGroup extends AbstractControl {\n/**\n * @param {?} controls\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\npublic controls: {[key: string]: AbstractControl}, validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null) {\n    super(validator || null, asyncValidator || null);\n    this._initObservables();\n    this._setUpControls();\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n  }\n/**\n * Registers a control with the group's list of controls.\n * \n * This method does not update value or validity of the control, so for\n * most cases you'll want to use {\\@link FormGroup#addControl} instead.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\nregisterControl(name: string, control: AbstractControl): AbstractControl {\n    if (this.controls[name]) return this.controls[name];\n    this.controls[name] = control;\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n    return control;\n  }\n/**\n * Add a control to this group.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\naddControl(name: string, control: AbstractControl): void {\n    this.registerControl(name, control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Remove a control from this group.\n * @param {?} name\n * @return {?}\n */\nremoveControl(name: string): void {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete (this.controls[name]);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Replace an existing control.\n * @param {?} name\n * @param {?} control\n * @return {?}\n */\nsetControl(name: string, control: AbstractControl): void {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete (this.controls[name]);\n    if (control) this.registerControl(name, control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Check whether there is an enabled control with the given name in the group.\n * \n * It will return false for disabled controls. If you'd like to check for\n * existence in the group only, use {\\@link AbstractControl#get} instead.\n * @param {?} controlName\n * @return {?}\n */\ncontains(controlName: string): boolean {\n    return this.controls.hasOwnProperty(controlName) && this.controls[controlName].enabled;\n  }\n/**\n *  Sets the value of the {\\@link FormGroup}. It accepts an object that matches\n *  the structure of the group, with control names as keys.\n * \n * This method performs strict checks, so it will throw an error if you try\n * to set the value of a control that doesn't exist or if you exclude the\n * value of a control.\n * \n *  ### Example\n * \n *  ```\n *  const form = new FormGroup({\n *     first: new FormControl(),\n *     last: new FormControl()\n *  });\n *  console.log(form.value);   // {first: null, last: null}\n * \n *  form.setValue({first: 'Nancy', last: 'Drew'});\n *  console.log(form.value);   // {first: 'Nancy', last: 'Drew'}\n * \n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: {[key: string]: any}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}):\n      void {\n    this._checkAllValuesPresent(value);\n    Object.keys(value).forEach(name => {\n      this._throwIfControlMissing(name);\n      this.controls[name].setValue(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n *  Patches the value of the {\\@link FormGroup}. It accepts an object with control\n *  names as keys, and will do its best to match the values to the correct controls\n *  in the group.\n * \n *  It accepts both super-sets and sub-sets of the group without throwing an error.\n * \n *  ### Example\n * \n *  ```\n *  const form = new FormGroup({\n *     first: new FormControl(),\n *     last: new FormControl()\n *  });\n *  console.log(form.value);   // {first: null, last: null}\n * \n *  form.patchValue({first: 'Nancy'});\n *  console.log(form.value);   // {first: 'Nancy', last: null}\n * \n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: {[key: string]: any}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}):\n      void {\n    Object.keys(value).forEach(name => {\n      if (this.controls[name]) {\n        this.controls[name].patchValue(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n * Resets the {\\@link FormGroup}. This means by default:\n * \n * * The group and all descendants are marked `pristine`\n * * The group and all descendants are marked `untouched`\n * * The value of all descendants will be null or null maps\n * \n * You can also reset to a specific form state by passing in a map of states\n * that matches the structure of your form, with control names as keys. The state\n * can be a standalone value or a form state object with both a value and a disabled\n * status.\n * \n * ### Example\n * \n * ```ts\n * this.form.reset({first: 'name', last: 'last name'});\n * \n * console.log(this.form.value);  // {first: 'name', last: 'last name'}\n * ```\n * \n * - OR -\n * \n * ```\n * this.form.reset({\n *   first: {value: 'name', disabled: true},\n *   last: 'last'\n * });\n * \n * console.log(this.form.value);  // {first: 'name', last: 'last name'}\n * console.log(this.form.get('first').status);  // 'DISABLED'\n * ```\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value: any = {}, options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._forEachChild((control: AbstractControl, name: string) => {\n      control.reset(value[name], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n    this._updatePristine(options);\n    this._updateTouched(options);\n  }\n/**\n * The aggregate value of the {\\@link FormGroup}, including any disabled controls.\n * \n * If you'd like to include all values regardless of disabled status, use this method.\n * Otherwise, the `value` property is the best way to get the value of the group.\n * @return {?}\n */\ngetRawValue(): any {\n    return this._reduceChildren(\n        {}, (acc: {[k: string]: AbstractControl}, control: AbstractControl, name: string) => {\n          acc[name] = control instanceof FormControl ? control.value : ( /** @type {?} */((<any>control))).getRawValue();\n          return acc;\n        });\n  }\n/**\n * \\@internal\n * @param {?} name\n * @return {?}\n */\n_throwIfControlMissing(name: string): void {\n    if (!Object.keys(this.controls).length) {\n      throw new Error(`\n        There are no form controls registered with this group yet.  If you're using ngModel,\n        you may want to check next tick (e.g. use setTimeout).\n      `);\n    }\n    if (!this.controls[name]) {\n      throw new Error(`Cannot find form control with name: ${name}.`);\n    }\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: (v: any, k: string) => void): void {\n    Object.keys(this.controls).forEach(k => cb(this.controls[k], k));\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_setUpControls(): void {\n    this._forEachChild((control: AbstractControl) => {\n      control.setParent(this);\n      control._registerOnCollectionChange(this._onCollectionChange);\n    });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue(): void { this._value = this._reduceValue(); }\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean {\n    let /** @type {?} */ res = false;\n    this._forEachChild((control: AbstractControl, name: string) => {\n      res = res || (this.contains(name) && condition(control));\n    });\n    return res;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_reduceValue() {\n    return this._reduceChildren(\n        {}, (acc: {[k: string]: AbstractControl}, control: AbstractControl, name: string) => {\n          if (control.enabled || this.disabled) {\n            acc[name] = control.value;\n          }\n          return acc;\n        });\n  }\n/**\n * \\@internal\n * @param {?} initValue\n * @param {?} fn\n * @return {?}\n */\n_reduceChildren(initValue: any, fn: Function) {\n    let /** @type {?} */ res = initValue;\n    this._forEachChild(\n        (control: AbstractControl, name: string) => { res = fn(res, control, name); });\n    return res;\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean {\n    for (const /** @type {?} */ controlName of Object.keys(this.controls)) {\n      if (this.controls[controlName].enabled) {\n        return false;\n      }\n    }\n    return Object.keys(this.controls).length > 0 || this.disabled;\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_checkAllValuesPresent(value: any): void {\n    this._forEachChild((control: AbstractControl, name: string) => {\n      if (value[name] === undefined) {\n        throw new Error(`Must supply a value for form control with name: '${name}'.`);\n      }\n    });\n  }\n}\n\nfunction FormGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nFormGroup.prototype.controls;\n}\n\n/**\n * \\@whatItDoes Tracks the value and validity state of an array of {\\@link FormControl},\n * {\\@link FormGroup} or {\\@link FormArray} instances.\n * \n * A `FormArray` aggregates the values of each child {\\@link FormControl} into an array.\n * It calculates its status by reducing the statuses of its children. For example, if one of\n * the controls in a `FormArray` is invalid, the entire array becomes invalid.\n * \n * `FormArray` is one of the three fundamental building blocks used to define forms in Angular,\n * along with {\\@link FormControl} and {\\@link FormGroup}.\n * \n * \\@howToUse \n * \n * When instantiating a {\\@link FormArray}, pass in an array of child controls as the first\n * argument.\n * \n * ### Example\n * \n * ```\n * const arr = new FormArray([\n *   new FormControl('Nancy', Validators.minLength(2)),\n *   new FormControl('Drew'),\n * ]);\n * \n * console.log(arr.value);   // ['Nancy', 'Drew']\n * console.log(arr.status);  // 'VALID'\n * ```\n * \n * You can also include array-level validators as the second arg, or array-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n * \n * ### Adding or removing controls\n * \n * To change the controls in the array, use the `push`, `insert`, or `removeAt` methods\n * in `FormArray` itself. These methods ensure the controls are properly tracked in the\n * form's hierarchy. Do not modify the array of `AbstractControl`s used to instantiate\n * the `FormArray` directly, as that will result in strange and unexpected behavior such\n * as broken change detection.\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class FormArray extends AbstractControl {\n/**\n * @param {?} controls\n * @param {?=} validator\n * @param {?=} asyncValidator\n */\nconstructor(\npublic controls: AbstractControl[], validator?: ValidatorFn|null,\n      asyncValidator?: AsyncValidatorFn|null) {\n    super(validator || null, asyncValidator || null);\n    this._initObservables();\n    this._setUpControls();\n    this.updateValueAndValidity({onlySelf: true, emitEvent: false});\n  }\n/**\n * Get the {\\@link AbstractControl} at the given `index` in the array.\n * @param {?} index\n * @return {?}\n */\nat(index: number): AbstractControl { return this.controls[index]; }\n/**\n * Insert a new {\\@link AbstractControl} at the end of the array.\n * @param {?} control\n * @return {?}\n */\npush(control: AbstractControl): void {\n    this.controls.push(control);\n    this._registerControl(control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Insert a new {\\@link AbstractControl} at the given `index` in the array.\n * @param {?} index\n * @param {?} control\n * @return {?}\n */\ninsert(index: number, control: AbstractControl): void {\n    this.controls.splice(index, 0, control);\n\n    this._registerControl(control);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Remove the control at the given `index` in the array.\n * @param {?} index\n * @return {?}\n */\nremoveAt(index: number): void {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Replace an existing control.\n * @param {?} index\n * @param {?} control\n * @return {?}\n */\nsetControl(index: number, control: AbstractControl): void {\n    if (this.controls[index]) this.controls[index]._registerOnCollectionChange(() => {});\n    this.controls.splice(index, 1);\n\n    if (control) {\n      this.controls.splice(index, 0, control);\n      this._registerControl(control);\n    }\n\n    this.updateValueAndValidity();\n    this._onCollectionChange();\n  }\n/**\n * Length of the control array.\n * @return {?}\n */\nget length(): number { return this.controls.length; }\n/**\n *  Sets the value of the {\\@link FormArray}. It accepts an array that matches\n *  the structure of the control.\n * \n * This method performs strict checks, so it will throw an error if you try\n * to set the value of a control that doesn't exist or if you exclude the\n * value of a control.\n * \n *  ### Example\n * \n *  ```\n *  const arr = new FormArray([\n *     new FormControl(),\n *     new FormControl()\n *  ]);\n *  console.log(arr.value);   // [null, null]\n * \n *  arr.setValue(['Nancy', 'Drew']);\n *  console.log(arr.value);   // ['Nancy', 'Drew']\n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\nsetValue(value: any[], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._checkAllValuesPresent(value);\n    value.forEach((newValue: any, index: number) => {\n      this._throwIfControlMissing(index);\n      this.at(index).setValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n *  Patches the value of the {\\@link FormArray}. It accepts an array that matches the\n *  structure of the control, and will do its best to match the values to the correct\n *  controls in the group.\n * \n *  It accepts both super-sets and sub-sets of the array without throwing an error.\n * \n *  ### Example\n * \n *  ```\n *  const arr = new FormArray([\n *     new FormControl(),\n *     new FormControl()\n *  ]);\n *  console.log(arr.value);   // [null, null]\n * \n *  arr.patchValue(['Nancy']);\n *  console.log(arr.value);   // ['Nancy', null]\n *  ```\n * @param {?} value\n * @param {?=} options\n * @return {?}\n */\npatchValue(value: any[], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    value.forEach((newValue: any, index: number) => {\n      if (this.at(index)) {\n        this.at(index).patchValue(newValue, {onlySelf: true, emitEvent: options.emitEvent});\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n/**\n * Resets the {\\@link FormArray}. This means by default:\n * \n * * The array and all descendants are marked `pristine`\n * * The array and all descendants are marked `untouched`\n * * The value of all descendants will be null or null maps\n * \n * You can also reset to a specific form state by passing in an array of states\n * that matches the structure of the control. The state can be a standalone value\n * or a form state object with both a value and a disabled status.\n * \n * ### Example\n * \n * ```ts\n * this.arr.reset(['name', 'last name']);\n * \n * console.log(this.arr.value);  // ['name', 'last name']\n * ```\n * \n * - OR -\n * \n * ```\n * this.arr.reset([\n *   {value: 'name', disabled: true},\n *   'last'\n * ]);\n * \n * console.log(this.arr.value);  // ['name', 'last name']\n * console.log(this.arr.get(0).status);  // 'DISABLED'\n * ```\n * @param {?=} value\n * @param {?=} options\n * @return {?}\n */\nreset(value: any = [], options: {onlySelf?: boolean, emitEvent?: boolean} = {}): void {\n    this._forEachChild((control: AbstractControl, index: number) => {\n      control.reset(value[index], {onlySelf: true, emitEvent: options.emitEvent});\n    });\n    this.updateValueAndValidity(options);\n    this._updatePristine(options);\n    this._updateTouched(options);\n  }\n/**\n * The aggregate value of the array, including any disabled controls.\n * \n * If you'd like to include all values regardless of disabled status, use this method.\n * Otherwise, the `value` property is the best way to get the value of the array.\n * @return {?}\n */\ngetRawValue(): any[] {\n    return this.controls.map((control: AbstractControl) => {\n      return control instanceof FormControl ? control.value : ( /** @type {?} */((<any>control))).getRawValue();\n    });\n  }\n/**\n * \\@internal\n * @param {?} index\n * @return {?}\n */\n_throwIfControlMissing(index: number): void {\n    if (!this.controls.length) {\n      throw new Error(`\n        There are no form controls registered with this array yet.  If you're using ngModel,\n        you may want to check next tick (e.g. use setTimeout).\n      `);\n    }\n    if (!this.at(index)) {\n      throw new Error(`Cannot find form control at index ${index}`);\n    }\n  }\n/**\n * \\@internal\n * @param {?} cb\n * @return {?}\n */\n_forEachChild(cb: Function): void {\n    this.controls.forEach((control: AbstractControl, index: number) => { cb(control, index); });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_updateValue(): void {\n    this._value = this.controls.filter((control) => control.enabled || this.disabled)\n                      .map((control) => control.value);\n  }\n/**\n * \\@internal\n * @param {?} condition\n * @return {?}\n */\n_anyControls(condition: Function): boolean {\n    return this.controls.some((control: AbstractControl) => control.enabled && condition(control));\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_setUpControls(): void {\n    this._forEachChild((control: AbstractControl) => this._registerControl(control));\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_checkAllValuesPresent(value: any): void {\n    this._forEachChild((control: AbstractControl, i: number) => {\n      if (value[i] === undefined) {\n        throw new Error(`Must supply a value for form control at index: ${i}.`);\n      }\n    });\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_allControlsDisabled(): boolean {\n    for (const /** @type {?} */ control of this.controls) {\n      if (control.enabled) return false;\n    }\n    return this.controls.length > 0 || this.disabled;\n  }\n/**\n * @param {?} control\n * @return {?}\n */\nprivate _registerControl(control: AbstractControl) {\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n  }\n}\n\nfunction FormArray_tsickle_Closure_declarations() {\n/** @type {?} */\nFormArray.prototype.controls;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, Self} from '@angular/core';\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {NgControl} from './ng_control';\nexport class AbstractControlStatus {\nprivate _cd: AbstractControlDirective;\n/**\n * @param {?} cd\n */\nconstructor(cd: AbstractControlDirective) { this._cd = cd; }\n/**\n * @return {?}\n */\nget ngClassUntouched(): boolean { return this._cd.control ? this._cd.control.untouched : false; }\n/**\n * @return {?}\n */\nget ngClassTouched(): boolean { return this._cd.control ? this._cd.control.touched : false; }\n/**\n * @return {?}\n */\nget ngClassPristine(): boolean { return this._cd.control ? this._cd.control.pristine : false; }\n/**\n * @return {?}\n */\nget ngClassDirty(): boolean { return this._cd.control ? this._cd.control.dirty : false; }\n/**\n * @return {?}\n */\nget ngClassValid(): boolean { return this._cd.control ? this._cd.control.valid : false; }\n/**\n * @return {?}\n */\nget ngClassInvalid(): boolean { return this._cd.control ? this._cd.control.invalid : false; }\n/**\n * @return {?}\n */\nget ngClassPending(): boolean { return this._cd.control ? this._cd.control.pending : false; }\n}\n\nfunction AbstractControlStatus_tsickle_Closure_declarations() {\n/** @type {?} */\nAbstractControlStatus.prototype._cd;\n}\n\n\nexport const /** @type {?} */ ngControlStatusHost = {\n  '[class.ng-untouched]': 'ngClassUntouched',\n  '[class.ng-touched]': 'ngClassTouched',\n  '[class.ng-pristine]': 'ngClassPristine',\n  '[class.ng-dirty]': 'ngClassDirty',\n  '[class.ng-valid]': 'ngClassValid',\n  '[class.ng-invalid]': 'ngClassInvalid',\n  '[class.ng-pending]': 'ngClassPending',\n};\n/**\n * Directive automatically applied to Angular form controls that sets CSS classes\n * based on control status (valid/invalid/dirty/etc).\n * \n * \\@stable\n */\nexport class NgControlStatus extends AbstractControlStatus {\n/**\n * @param {?} cd\n */\nconstructor( cd: NgControl) { super(cd); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[formControlName],[ngModel],[formControl]', host: ngControlStatusHost}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgControl, decorators: [{ type: Self }, ]},\n];\n}\n\nfunction NgControlStatus_tsickle_Closure_declarations() {\n/** @type {?} */\nNgControlStatus.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgControlStatus.ctorParameters;\n}\n\n/**\n * Directive automatically applied to Angular form groups that sets CSS classes\n * based on control status (valid/invalid/dirty/etc).\n * \n * \\@stable\n */\nexport class NgControlStatusGroup extends AbstractControlStatus {\n/**\n * @param {?} cd\n */\nconstructor( cd: ControlContainer) { super(cd); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      '[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]',\n  host: ngControlStatusHost\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ControlContainer, decorators: [{ type: Self }, ]},\n];\n}\n\nfunction NgControlStatusGroup_tsickle_Closure_declarations() {\n/** @type {?} */\nNgControlStatusGroup.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgControlStatusGroup.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {On<PERSON><PERSON>roy, OnInit} from '@angular/core';\n\nimport {FormGroup} from '../model';\n\nimport {ControlContainer} from './control_container';\nimport {Form} from './form_interface';\nimport {composeAsyncValidators, composeValidators, controlPath} from './shared';\nimport {AsyncValidatorFn, ValidatorFn} from './validators';\n/**\n * This is a base class for code shared between {\\@link NgModelGroup} and {\\@link FormGroupName}.\n * \n * \\@stable\n */\nexport class AbstractFormGroupDirective extends ControlContainer implements OnInit, OnDestroy {\n/**\n * \\@internal\n */\n_parent: ControlContainer;\n/**\n * \\@internal\n */\n_validators: any[];\n/**\n * \\@internal\n */\n_asyncValidators: any[];\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._checkParentType(); /** @type {?} */((\n    this.formDirective)).addFormGroup(this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this.formDirective) {\n      this.formDirective.removeFormGroup(this);\n    }\n  }\n/**\n * Get the {\\@link FormGroup} backing this binding.\n * @return {?}\n */\nget control(): FormGroup { return /** @type {?} */(( this.formDirective)).getFormGroup(this); }\n/**\n * Get the path to this control group.\n * @return {?}\n */\nget path(): string[] { return controlPath(this.name, this._parent); }\n/**\n * Get the {\\@link Form} to which this group belongs.\n * @return {?}\n */\nget formDirective(): Form|null { return this._parent ? this._parent.formDirective : null; }\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return composeValidators(this._validators); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null {\n    return composeAsyncValidators(this._asyncValidators);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_checkParentType(): void {}\n}\n\nfunction AbstractFormGroupDirective_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._parent;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._validators;\n/**\n * \\@internal\n * @type {?}\n */\nAbstractFormGroupDirective.prototype._asyncValidators;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {FormArray, FormControl, FormGroup} from '../model';\nimport {Validators} from '../validators';\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {AbstractFormGroupDirective} from './abstract_form_group_directive';\nimport {CheckboxControlValueAccessor} from './checkbox_value_accessor';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor} from './control_value_accessor';\nimport {DefaultValueAccessor} from './default_value_accessor';\nimport {NgControl} from './ng_control';\nimport {normalizeAsyncValidator, normalizeValidator} from './normalize_validator';\nimport {NumberValueAccessor} from './number_value_accessor';\nimport {RadioControlValueAccessor} from './radio_control_value_accessor';\nimport {RangeValueAccessor} from './range_value_accessor';\nimport {FormArrayName} from './reactive_directives/form_group_name';\nimport {SelectControlValueAccessor} from './select_control_value_accessor';\nimport {SelectMultipleControlValueAccessor} from './select_multiple_control_value_accessor';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @param {?} name\n * @param {?} parent\n * @return {?}\n */\nexport function controlPath(name: string, parent: ControlContainer): string[] {\n  return [... /** @type {?} */((parent.path)), name];\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function setUpControl(control: FormControl, dir: NgControl): void {\n  if (!control) _throwError(dir, 'Cannot find control with');\n  if (!dir.valueAccessor) _throwError(dir, 'No value accessor for form control with');\n\n  control.validator = Validators.compose([ /** @type {?} */((control.validator)), dir.validator]);\n  control.asyncValidator = Validators.composeAsync([ /** @type {?} */((control.asyncValidator)), dir.asyncValidator]); /** @type {?} */((\n  dir.valueAccessor)).writeValue(control.value); /** @type {?} */((\n\n  // view -> model\n  dir.valueAccessor)).registerOnChange((newValue: any) => {\n    dir.viewToModelUpdate(newValue);\n    control.markAsDirty();\n    control.setValue(newValue, {emitModelToViewChange: false});\n  }); /** @type {?} */((\n\n  // touched\n  dir.valueAccessor)).registerOnTouched(() => control.markAsTouched());\n\n  control.registerOnChange((newValue: any, emitModelEvent: boolean) => { /** @type {?} */((\n    // control -> view\n    dir.valueAccessor)).writeValue(newValue);\n\n    // control -> ngModel\n    if (emitModelEvent) dir.viewToModelUpdate(newValue);\n  });\n\n  if ( /** @type {?} */((dir.valueAccessor)).setDisabledState) {\n    control.registerOnDisabledChange(\n        (isDisabled: boolean) => { /** @type {?} */(( /** @type {?} */(( dir.valueAccessor)).setDisabledState))(isDisabled); });\n  }\n\n  // re-run validation when validator binding changes, e.g. minlength=3 -> minlength=4\n  dir._rawValidators.forEach((validator: Validator | ValidatorFn) => {\n    if (( /** @type {?} */((<Validator>validator))).registerOnValidatorChange) /** @type {?} */((\n      ( /** @type {?} */((<Validator>validator))).registerOnValidatorChange))(() => control.updateValueAndValidity());\n  });\n\n  dir._rawAsyncValidators.forEach((validator: AsyncValidator | AsyncValidatorFn) => {\n    if (( /** @type {?} */((<Validator>validator))).registerOnValidatorChange) /** @type {?} */((\n      ( /** @type {?} */((<Validator>validator))).registerOnValidatorChange))(() => control.updateValueAndValidity());\n  });\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function cleanUpControl(control: FormControl, dir: NgControl) { /** @type {?} */((\n  dir.valueAccessor)).registerOnChange(() => _noControlError(dir)); /** @type {?} */((\n  dir.valueAccessor)).registerOnTouched(() => _noControlError(dir));\n\n  dir._rawValidators.forEach((validator: any) => {\n    if (validator.registerOnValidatorChange) {\n      validator.registerOnValidatorChange(null);\n    }\n  });\n\n  dir._rawAsyncValidators.forEach((validator: any) => {\n    if (validator.registerOnValidatorChange) {\n      validator.registerOnValidatorChange(null);\n    }\n  });\n\n  if (control) control._clearChangeFns();\n}\n/**\n * @param {?} control\n * @param {?} dir\n * @return {?}\n */\nexport function setUpFormContainer(\n    control: FormGroup | FormArray, dir: AbstractFormGroupDirective | FormArrayName) {\n  if (control == null) _throwError(dir, 'Cannot find control with');\n  control.validator = Validators.compose([control.validator, dir.validator]);\n  control.asyncValidator = Validators.composeAsync([control.asyncValidator, dir.asyncValidator]);\n}\n/**\n * @param {?} dir\n * @return {?}\n */\nfunction _noControlError(dir: NgControl) {\n  return _throwError(dir, 'There is no FormControl instance attached to form control element with');\n}\n/**\n * @param {?} dir\n * @param {?} message\n * @return {?}\n */\nfunction _throwError(dir: AbstractControlDirective, message: string): void {\n  let /** @type {?} */ messageEnd: string;\n  if ( /** @type {?} */((dir.path)).length > 1) {\n    messageEnd = `path: '${ /** @type {?} */((dir.path)).join(' -> ')}'`;\n  } else if ( /** @type {?} */((dir.path))[0]) {\n    messageEnd = `name: '${dir.path}'`;\n  } else {\n    messageEnd = 'unspecified name attribute';\n  }\n  throw new Error(`${message} ${messageEnd}`);\n}\n/**\n * @param {?} validators\n * @return {?}\n */\nexport function composeValidators(validators: Array<Validator|Function>): ValidatorFn|null {\n  return validators != null ? Validators.compose(validators.map(normalizeValidator)) : null;\n}\n/**\n * @param {?} validators\n * @return {?}\n */\nexport function composeAsyncValidators(validators: Array<Validator|Function>): AsyncValidatorFn|\n    null {\n  return validators != null ? Validators.composeAsync(validators.map(normalizeAsyncValidator)) :\n                              null;\n}\n/**\n * @param {?} changes\n * @param {?} viewModel\n * @return {?}\n */\nexport function isPropertyUpdated(changes: {[key: string]: any}, viewModel: any): boolean {\n  if (!changes.hasOwnProperty('model')) return false;\n  const /** @type {?} */ change = changes['model'];\n\n  if (change.isFirstChange()) return true;\n  return !looseIdentical(viewModel, change.currentValue);\n}\n\nconst /** @type {?} */ BUILTIN_ACCESSORS = [\n  CheckboxControlValueAccessor,\n  RangeValueAccessor,\n  NumberValueAccessor,\n  SelectControlValueAccessor,\n  SelectMultipleControlValueAccessor,\n  RadioControlValueAccessor,\n];\n/**\n * @param {?} valueAccessor\n * @return {?}\n */\nexport function isBuiltInAccessor(valueAccessor: ControlValueAccessor): boolean {\n  return BUILTIN_ACCESSORS.some(a => valueAccessor.constructor === a);\n}\n/**\n * @param {?} dir\n * @param {?} valueAccessors\n * @return {?}\n */\nexport function selectValueAccessor(\n    dir: NgControl, valueAccessors: ControlValueAccessor[]): ControlValueAccessor|null {\n  if (!valueAccessors) return null;\n\n  let /** @type {?} */ defaultAccessor: ControlValueAccessor|undefined = undefined;\n  let /** @type {?} */ builtinAccessor: ControlValueAccessor|undefined = undefined;\n  let /** @type {?} */ customAccessor: ControlValueAccessor|undefined = undefined;\n  valueAccessors.forEach((v: ControlValueAccessor) => {\n    if (v.constructor === DefaultValueAccessor) {\n      defaultAccessor = v;\n\n    } else if (isBuiltInAccessor(v)) {\n      if (builtinAccessor)\n        _throwError(dir, 'More than one built-in value accessor matches form control with');\n      builtinAccessor = v;\n\n    } else {\n      if (customAccessor)\n        _throwError(dir, 'More than one custom value accessor matches form control with');\n      customAccessor = v;\n    }\n  });\n\n  if (customAccessor) return customAccessor;\n  if (builtinAccessor) return builtinAccessor;\n  if (defaultAccessor) return defaultAccessor;\n\n  _throwError(dir, 'No valid value accessor for form control with');\n  return null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Host, Input, OnDestroy, Optional, Provider, Renderer, forwardRef, ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ SELECT_MULTIPLE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectMultipleControlValueAccessor),\n  multi: true\n};\n/**\n * @param {?} id\n * @param {?} value\n * @return {?}\n */\nfunction _buildValueString(id: string, value: any): string {\n  if (id == null) return `${value}`;\n  if (typeof value === 'string') value = `'${value}'`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n/**\n * @param {?} valueString\n * @return {?}\n */\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n\n/** Mock interface for HTML Options */\ninterface HTMLOption {\n  value: string;\n  selected: boolean;\n}\n/**\n * Mock interface for HTMLCollection\n * @abstract\n */\nabstract class HTMLCollection {\n  length: number;\n/**\n * @abstract\n * @param {?} _\n * @return {?}\n */\nitem(_: number) {}\n}\n\nfunction HTMLCollection_tsickle_Closure_declarations() {\n/** @type {?} */\nHTMLCollection.prototype.length;\n}\n\n/**\n * The accessor for writing a value and listening to changes on a select element.\n * \n *  ### Caveat: Options selection\n * \n * Angular uses object identity to select options. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n * \n * To customize the default option comparison algorithm, `<select multiple>` supports `compareWith`\n * input. `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects options by the return value of the function.\n * \n * #### Syntax\n * \n * ```\n * <select multiple [compareWith]=\"compareFn\"  [(ngModel)]=\"selectedCountries\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n * \n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n * \n * \\@stable\n */\nexport class SelectMultipleControlValueAccessor implements ControlValueAccessor {\n  value: any;\n/**\n * \\@internal\n */\n_optionMap: Map<string, NgSelectMultipleOption> = new Map<string, NgSelectMultipleOption>();\n/**\n * \\@internal\n */\n_idCounter: number = 0;\n\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} fn\n * @return {?}\n */\nset compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function') {\n      throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\nprivate _compareWith: (o1: any, o2: any) => boolean = looseIdentical;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this.value = value;\n    let /** @type {?} */ optionSelectedStateSetter: (opt: NgSelectMultipleOption, o: any) => void;\n    if (Array.isArray(value)) {\n      // convert values to ids\n      const /** @type {?} */ ids = value.map((v) => this._getOptionId(v));\n      optionSelectedStateSetter = (opt, o) => { opt._setSelected(ids.indexOf(o.toString()) > -1); };\n    } else {\n      optionSelectedStateSetter = (opt, o) => { opt._setSelected(false); };\n    }\n    this._optionMap.forEach(optionSelectedStateSetter);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (value: any) => any): void {\n    this.onChange = (_: any) => {\n      const /** @type {?} */ selected: Array<any> = [];\n      if (_.hasOwnProperty('selectedOptions')) {\n        const /** @type {?} */ options: HTMLCollection = _.selectedOptions;\n        for (let /** @type {?} */ i = 0; i < options.length; i++) {\n          const /** @type {?} */ opt: any = options.item(i);\n          const /** @type {?} */ val: any = this._getOptionValue(opt.value);\n          selected.push(val);\n        }\n      }\n      // Degrade on IE\n      else {\n        const /** @type {?} */ options: HTMLCollection = /** @type {?} */(( <HTMLCollection>_.options));\n        for (let /** @type {?} */ i = 0; i < options.length; i++) {\n          const /** @type {?} */ opt: HTMLOption = options.item(i);\n          if (opt.selected) {\n            const /** @type {?} */ val: any = this._getOptionValue(opt.value);\n            selected.push(val);\n          }\n        }\n      }\n      this.value = selected;\n      fn(selected);\n    };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => any): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_registerOption(value: NgSelectMultipleOption): string {\n    const /** @type {?} */ id: string = (this._idCounter++).toString();\n    this._optionMap.set(id, value);\n    return id;\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_getOptionId(value: any): string|null {\n    for (const /** @type {?} */ id of Array.from(this._optionMap.keys())) {\n      if (this._compareWith( /** @type {?} */((this._optionMap.get(id)))._value, value)) return id;\n    }\n    return null;\n  }\n/**\n * \\@internal\n * @param {?} valueString\n * @return {?}\n */\n_getOptionValue(valueString: string): any {\n    const /** @type {?} */ id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? /** @type {?} */(( this._optionMap.get(id)))._value : valueString;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'select[multiple][formControlName],select[multiple][formControl],select[multiple][ngModel]',\n  host: {'(change)': 'onChange($event.target)', '(blur)': 'onTouched()'},\n  providers: [SELECT_MULTIPLE_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'compareWith': [{ type: Input },],\n};\n}\n\nfunction SelectMultipleControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nSelectMultipleControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSelectMultipleControlValueAccessor.ctorParameters;\n/** @type {?} */\nSelectMultipleControlValueAccessor.propDecorators;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.value;\n/**\n * \\@internal\n * @type {?}\n */\nSelectMultipleControlValueAccessor.prototype._optionMap;\n/**\n * \\@internal\n * @type {?}\n */\nSelectMultipleControlValueAccessor.prototype._idCounter;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.onChange;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._compareWith;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._renderer;\n/** @type {?} */\nSelectMultipleControlValueAccessor.prototype._elementRef;\n}\n\n/**\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n * \n * ### Example\n * \n * ```\n * <select multiple name=\"city\" ngModel>\n *   <option *ngFor=\"let c of cities\" [value]=\"c\"></option>\n * </select>\n * ```\n */\nexport class NgSelectMultipleOption implements OnDestroy {\n  id: string;\n/**\n * \\@internal\n */\n_value: any;\n/**\n * @param {?} _element\n * @param {?} _renderer\n * @param {?} _select\n */\nconstructor(\nprivate _element: ElementRef,\nprivate _renderer: Renderer,\nprivate _select: SelectMultipleControlValueAccessor) {\n    if (this._select) {\n      this.id = this._select._registerOption(this);\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset ngValue(value: any) {\n    if (this._select == null) return;\n    this._value = value;\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset value(value: any) {\n    if (this._select) {\n      this._value = value;\n      this._setElementValue(_buildValueString(this.id, value));\n      this._select.writeValue(this._select.value);\n    } else {\n      this._setElementValue(value);\n    }\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_setElementValue(value: string): void {\n    this._renderer.setElementProperty(this._element.nativeElement, 'value', value);\n  }\n/**\n * \\@internal\n * @param {?} selected\n * @return {?}\n */\n_setSelected(selected: boolean) {\n    this._renderer.setElementProperty(this._element.nativeElement, 'selected', selected);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'option'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ElementRef, },\n{type: Renderer, },\n{type: SelectMultipleControlValueAccessor, decorators: [{ type: Optional }, { type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngValue': [{ type: Input, args: ['ngValue', ] },],\n'value': [{ type: Input, args: ['value', ] },],\n};\n}\n\nfunction NgSelectMultipleOption_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSelectMultipleOption.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSelectMultipleOption.ctorParameters;\n/** @type {?} */\nNgSelectMultipleOption.propDecorators;\n/** @type {?} */\nNgSelectMultipleOption.prototype.id;\n/**\n * \\@internal\n * @type {?}\n */\nNgSelectMultipleOption.prototype._value;\n/** @type {?} */\nNgSelectMultipleOption.prototype._element;\n/** @type {?} */\nNgSelectMultipleOption.prototype._renderer;\n/** @type {?} */\nNgSelectMultipleOption.prototype._select;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Host, Input, OnD<PERSON>roy, Optional, Provider, Renderer, forwardRef, ɵlooseIdentical as looseIdentical} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ SELECT_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectControlValueAccessor),\n  multi: true\n};\n/**\n * @param {?} id\n * @param {?} value\n * @return {?}\n */\nfunction _buildValueString(id: string | null, value: any): string {\n  if (id == null) return `${value}`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\n/**\n * @param {?} valueString\n * @return {?}\n */\nfunction _extractId(valueString: string): string {\n  return valueString.split(':')[0];\n}\n/**\n * \\@whatItDoes Writes values and listens to changes on a select element.\n * \n * Used by {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName}\n * to keep the view synced with the {\\@link FormControl} model.\n * \n * \\@howToUse \n * \n * If you have imported the {\\@link FormsModule} or the {\\@link ReactiveFormsModule}, this\n * value accessor will be active on any select control that has a form directive. You do\n * **not** need to add a special selector to activate it.\n * \n * ### How to use select controls with form directives\n * \n * To use a select in a template-driven form, simply add an `ngModel` and a `name`\n * attribute to the main `<select>` tag.\n * \n * If your option values are simple strings, you can bind to the normal `value` property\n * on the option.  If your option values happen to be objects (and you'd like to save the\n * selection in your form as an object), use `ngValue` instead:\n * \n * {\\@example forms/ts/selectControl/select_control_example.ts region='Component'}\n * \n * In reactive forms, you'll also want to add your form directive (`formControlName` or\n * `formControl`) on the main `<select>` tag. Like in the former example, you have the\n * choice of binding to the  `value` or `ngValue` property on the select's options.\n * \n * {\\@example forms/ts/reactiveSelectControl/reactive_select_control_example.ts region='Component'}\n * \n * ### Caveat: Option selection\n * \n * Angular uses object identity to select option. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n * \n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects option by the return value of the function.\n * \n * #### Syntax\n * \n * ```\n * <select [compareWith]=\"compareFn\"  [(ngModel)]=\"selectedCountries\">\n *     <option *ngFor=\"let country of countries\" [ngValue]=\"country\">\n *         {{country.name}}\n *     </option>\n * </select>\n * \n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n * \n * Note: We listen to the 'change' event because 'input' events aren't fired\n * for selects in Firefox and IE:\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1024350\n * https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/4660045/\n * \n * * **npm package**: `\\@angular/forms`\n * \n * \\@stable\n */\nexport class SelectControlValueAccessor implements ControlValueAccessor {\n  value: any;\n/**\n * \\@internal\n */\n_optionMap: Map<string, any> = new Map<string, any>();\n/**\n * \\@internal\n */\n_idCounter: number = 0;\n\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} fn\n * @return {?}\n */\nset compareWith(fn: (o1: any, o2: any) => boolean) {\n    if (typeof fn !== 'function') {\n      throw new Error(`compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\nprivate _compareWith: (o1: any, o2: any) => boolean = looseIdentical;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this.value = value;\n    const /** @type {?} */ id: string|null = this._getOptionId(value);\n    if (id == null) {\n      this._renderer.setElementProperty(this._elementRef.nativeElement, 'selectedIndex', -1);\n    }\n    const /** @type {?} */ valueString = _buildValueString(id, value);\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', valueString);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (value: any) => any): void {\n    this.onChange = (valueString: string) => {\n      this.value = valueString;\n      fn(this._getOptionValue(valueString));\n    };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => any): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_registerOption(): string { return (this._idCounter++).toString(); }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_getOptionId(value: any): string|null {\n    for (const /** @type {?} */ id of Array.from(this._optionMap.keys())) {\n      if (this._compareWith(this._optionMap.get(id), value)) return id;\n    }\n    return null;\n  }\n/**\n * \\@internal\n * @param {?} valueString\n * @return {?}\n */\n_getOptionValue(valueString: string): any {\n    const /** @type {?} */ id: string = _extractId(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id) : valueString;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]',\n  host: {'(change)': 'onChange($event.target.value)', '(blur)': 'onTouched()'},\n  providers: [SELECT_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'compareWith': [{ type: Input },],\n};\n}\n\nfunction SelectControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nSelectControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSelectControlValueAccessor.ctorParameters;\n/** @type {?} */\nSelectControlValueAccessor.propDecorators;\n/** @type {?} */\nSelectControlValueAccessor.prototype.value;\n/**\n * \\@internal\n * @type {?}\n */\nSelectControlValueAccessor.prototype._optionMap;\n/**\n * \\@internal\n * @type {?}\n */\nSelectControlValueAccessor.prototype._idCounter;\n/** @type {?} */\nSelectControlValueAccessor.prototype.onChange;\n/** @type {?} */\nSelectControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nSelectControlValueAccessor.prototype._compareWith;\n/** @type {?} */\nSelectControlValueAccessor.prototype._renderer;\n/** @type {?} */\nSelectControlValueAccessor.prototype._elementRef;\n}\n\n/**\n * \\@whatItDoes Marks `<option>` as dynamic, so Angular can be notified when options change.\n * \n * \\@howToUse \n * \n * See docs for {\\@link SelectControlValueAccessor} for usage examples.\n * \n * \\@stable\n */\nexport class NgSelectOption implements OnDestroy {\n  id: string;\n/**\n * @param {?} _element\n * @param {?} _renderer\n * @param {?} _select\n */\nconstructor(\nprivate _element: ElementRef,\nprivate _renderer: Renderer,\nprivate _select: SelectControlValueAccessor) {\n    if (this._select) this.id = this._select._registerOption();\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset ngValue(value: any) {\n    if (this._select == null) return;\n    this._select._optionMap.set(this.id, value);\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset value(value: any) {\n    this._setElementValue(value);\n    if (this._select) this._select.writeValue(this._select.value);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_setElementValue(value: string): void {\n    this._renderer.setElementProperty(this._element.nativeElement, 'value', value);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'option'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ElementRef, },\n{type: Renderer, },\n{type: SelectControlValueAccessor, decorators: [{ type: Optional }, { type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngValue': [{ type: Input, args: ['ngValue', ] },],\n'value': [{ type: Input, args: ['value', ] },],\n};\n}\n\nfunction NgSelectOption_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSelectOption.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSelectOption.ctorParameters;\n/** @type {?} */\nNgSelectOption.propDecorators;\n/** @type {?} */\nNgSelectOption.prototype.id;\n/** @type {?} */\nNgSelectOption.prototype._element;\n/** @type {?} */\nNgSelectOption.prototype._renderer;\n/** @type {?} */\nNgSelectOption.prototype._select;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Provider, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ RANGE_VALUE_ACCESSOR: Provider = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RangeValueAccessor),\n  multi: true\n};\n/**\n * The accessor for writing a range value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"range\" [(ngModel)]=\"age\" >\n *  ```\n */\nexport class RangeValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', parseFloat(value));\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => { fn(value == '' ? null : parseFloat(value)); };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]',\n  host: {\n    '(change)': 'onChange($event.target.value)',\n    '(input)': 'onChange($event.target.value)',\n    '(blur)': 'onTouched()'\n  },\n  providers: [RANGE_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction RangeValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nRangeValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRangeValueAccessor.ctorParameters;\n/** @type {?} */\nRangeValueAccessor.prototype.onChange;\n/** @type {?} */\nRangeValueAccessor.prototype.onTouched;\n/** @type {?} */\nRangeValueAccessor.prototype._renderer;\n/** @type {?} */\nRangeValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Injectable, Injector, Input, OnDestroy, OnInit, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\nimport {NgControl} from './ng_control';\n\nexport const /** @type {?} */ RADIO_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RadioControlValueAccessor),\n  multi: true\n};\n/**\n * Internal class used by Angular to uncheck radio buttons with the matching name.\n */\nexport class RadioControlRegistry {\nprivate _accessors: any[] = [];\n/**\n * @param {?} control\n * @param {?} accessor\n * @return {?}\n */\nadd(control: NgControl, accessor: RadioControlValueAccessor) {\n    this._accessors.push([control, accessor]);\n  }\n/**\n * @param {?} accessor\n * @return {?}\n */\nremove(accessor: RadioControlValueAccessor) {\n    for (let /** @type {?} */ i = this._accessors.length - 1; i >= 0; --i) {\n      if (this._accessors[i][1] === accessor) {\n        this._accessors.splice(i, 1);\n        return;\n      }\n    }\n  }\n/**\n * @param {?} accessor\n * @return {?}\n */\nselect(accessor: RadioControlValueAccessor) {\n    this._accessors.forEach((c) => {\n      if (this._isSameGroup(c, accessor) && c[1] !== accessor) {\n        c[1].fireUncheck(accessor.value);\n      }\n    });\n  }\n/**\n * @param {?} controlPair\n * @param {?} accessor\n * @return {?}\n */\nprivate _isSameGroup(\n      controlPair: [NgControl, RadioControlValueAccessor],\n      accessor: RadioControlValueAccessor): boolean {\n    if (!controlPair[0].control) return false;\n    return controlPair[0]._parent === accessor._control._parent &&\n        controlPair[1].name === accessor.name;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction RadioControlRegistry_tsickle_Closure_declarations() {\n/** @type {?} */\nRadioControlRegistry.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRadioControlRegistry.ctorParameters;\n/** @type {?} */\nRadioControlRegistry.prototype._accessors;\n}\n\n/**\n * \\@whatItDoes Writes radio control values and listens to radio control changes.\n * \n * Used by {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName}\n * to keep the view synced with the {\\@link FormControl} model.\n * \n * \\@howToUse \n * \n * If you have imported the {\\@link FormsModule} or the {\\@link ReactiveFormsModule}, this\n * value accessor will be active on any radio control that has a form directive. You do\n * **not** need to add a special selector to activate it.\n * \n * ### How to use radio buttons with form directives\n * \n * To use radio buttons in a template-driven form, you'll want to ensure that radio buttons\n * in the same group have the same `name` attribute.  Radio buttons with different `name`\n * attributes do not affect each other.\n * \n * {\\@example forms/ts/radioButtons/radio_button_example.ts region='TemplateDriven'}\n * \n * When using radio buttons in a reactive form, radio buttons in the same group should have the\n * same `formControlName`. You can also add a `name` attribute, but it's optional.\n * \n * {\\@example forms/ts/reactiveRadioButtons/reactive_radio_button_example.ts region='Reactive'}\n * \n *  * **npm package**: `\\@angular/forms`\n * \n *  \\@stable\n */\nexport class RadioControlValueAccessor implements ControlValueAccessor,\n    OnDestroy, OnInit {\n/**\n * \\@internal\n */\n_state: boolean;\n/**\n * \\@internal\n */\n_control: NgControl;\n/**\n * \\@internal\n */\n_fn: Function;\n  onChange = () => {};\n  onTouched = () => {};\n\n   name: string;\n   formControlName: string;\n   value: any;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n * @param {?} _registry\n * @param {?} _injector\n */\nconstructor(\nprivate _renderer: Renderer,\nprivate _elementRef: ElementRef,\nprivate _registry: RadioControlRegistry,\nprivate _injector: Injector) {}\n/**\n * @return {?}\n */\nngOnInit(): void {\n    this._control = this._injector.get(NgControl);\n    this._checkName();\n    this._registry.add(this._control, this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this._registry.remove(this); }\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._state = value === this.value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'checked', this._state);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => {}): void {\n    this._fn = fn;\n    this.onChange = () => {\n      fn(this.value);\n      this._registry.select(this);\n    };\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nfireUncheck(value: any): void { this.writeValue(value); }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => {}): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * @return {?}\n */\nprivate _checkName(): void {\n    if (this.name && this.formControlName && this.name !== this.formControlName) {\n      this._throwNameError();\n    }\n    if (!this.name && this.formControlName) this.name = this.formControlName;\n  }\n/**\n * @return {?}\n */\nprivate _throwNameError(): void {\n    throw new Error(`\n      If you define both a name and a formControlName attribute on your radio button, their values\n      must match. Ex: <input type=\"radio\" formControlName=\"food\" name=\"food\">\n    `);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=radio][formControlName],input[type=radio][formControl],input[type=radio][ngModel]',\n  host: {'(change)': 'onChange()', '(blur)': 'onTouched()'},\n  providers: [RADIO_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n{type: RadioControlRegistry, },\n{type: Injector, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'name': [{ type: Input },],\n'formControlName': [{ type: Input },],\n'value': [{ type: Input },],\n};\n}\n\nfunction RadioControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nRadioControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRadioControlValueAccessor.ctorParameters;\n/** @type {?} */\nRadioControlValueAccessor.propDecorators;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._state;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._control;\n/**\n * \\@internal\n * @type {?}\n */\nRadioControlValueAccessor.prototype._fn;\n/** @type {?} */\nRadioControlValueAccessor.prototype.onChange;\n/** @type {?} */\nRadioControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nRadioControlValueAccessor.prototype.name;\n/** @type {?} */\nRadioControlValueAccessor.prototype.formControlName;\n/** @type {?} */\nRadioControlValueAccessor.prototype.value;\n/** @type {?} */\nRadioControlValueAccessor.prototype._renderer;\n/** @type {?} */\nRadioControlValueAccessor.prototype._elementRef;\n/** @type {?} */\nRadioControlValueAccessor.prototype._registry;\n/** @type {?} */\nRadioControlValueAccessor.prototype._injector;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {ControlContainer} from './control_container';\nimport {ControlValueAccessor} from './control_value_accessor';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @return {?}\n */\nfunction unimplemented(): any {\n  throw new Error('unimplemented');\n}\n/**\n * A base class that all control directive extend.\n * It binds a {\\@link FormControl} object to a DOM element.\n * \n * Used internally by Angular forms.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class NgControl extends AbstractControlDirective {\n/**\n * \\@internal\n */\n_parent: ControlContainer|null = null;\n  name: string|null = null;\n  valueAccessor: ControlValueAccessor|null = null;\n/**\n * \\@internal\n */\n_rawValidators: Array<Validator|ValidatorFn> = [];\n/**\n * \\@internal\n */\n_rawAsyncValidators: Array<AsyncValidator|AsyncValidatorFn> = [];\n/**\n * @return {?}\n */\nget validator(): ValidatorFn|null { return /** @type {?} */(( <ValidatorFn>unimplemented())); }\n/**\n * @return {?}\n */\nget asyncValidator(): AsyncValidatorFn|null { return /** @type {?} */(( <AsyncValidatorFn>unimplemented())); }\n/**\n * @abstract\n * @param {?} newValue\n * @return {?}\n */\nviewToModelUpdate(newValue: any) {}\n}\n\nfunction NgControl_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._parent;\n/** @type {?} */\nNgControl.prototype.name;\n/** @type {?} */\nNgControl.prototype.valueAccessor;\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._rawValidators;\n/**\n * \\@internal\n * @type {?}\n */\nNgControl.prototype._rawAsyncValidators;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ NUMBER_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => NumberValueAccessor),\n  multi: true\n};\n/**\n * The accessor for writing a number value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"number\" [(ngModel)]=\"age\">\n *  ```\n */\nexport class NumberValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: Renderer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: number): void {\n    // The value needs to be normalized for IE9, otherwise it is set to 'null' when null\n    const /** @type {?} */ normalizedValue = value == null ? '' : value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', normalizedValue);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: number|null) => void): void {\n    this.onChange = (value) => { fn(value == '' ? null : parseFloat(value)); };\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=number][formControlName],input[type=number][formControl],input[type=number][ngModel]',\n  host: {\n    '(change)': 'onChange($event.target.value)',\n    '(input)': 'onChange($event.target.value)',\n    '(blur)': 'onTouched()'\n  },\n  providers: [NUMBER_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction NumberValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nNumberValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNumberValueAccessor.ctorParameters;\n/** @type {?} */\nNumberValueAccessor.prototype.onChange;\n/** @type {?} */\nNumberValueAccessor.prototype.onTouched;\n/** @type {?} */\nNumberValueAccessor.prototype._renderer;\n/** @type {?} */\nNumberValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AbstractControl} from '../model';\nimport {AsyncValidator, AsyncValidatorFn, Validator, ValidatorFn} from './validators';\n/**\n * @param {?} validator\n * @return {?}\n */\nexport function normalizeValidator(validator: ValidatorFn | Validator): ValidatorFn {\n  if (( /** @type {?} */((<Validator>validator))).validate) {\n    return (c: AbstractControl) => ( /** @type {?} */((<Validator>validator))).validate(c);\n  } else {\n    return /** @type {?} */(( <ValidatorFn>validator));\n  }\n}\n/**\n * @param {?} validator\n * @return {?}\n */\nexport function normalizeAsyncValidator(validator: AsyncValidatorFn | AsyncValidator):\n    AsyncValidatorFn {\n  if (( /** @type {?} */((<AsyncValidator>validator))).validate) {\n    return (c: AbstractControl) => ( /** @type {?} */((<AsyncValidator>validator))).validate(c);\n  } else {\n    return /** @type {?} */(( <AsyncValidatorFn>validator));\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Inject, InjectionToken, Optional, Renderer, forwardRef} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ DEFAULT_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => DefaultValueAccessor),\n  multi: true\n};\n/**\n * We must check whether the agent is Android because composition events\n * behave differently between iOS and Android.\n * @return {?}\n */\nfunction _isAndroid(): boolean {\n  const /** @type {?} */ userAgent = getDOM() ? getDOM().getUserAgent() : '';\n  return /android (\\d+)/.test(userAgent.toLowerCase());\n}\n/**\n * Turn this mode on if you want form directives to buffer IME input until compositionend\n * \\@experimental\n */\nexport const COMPOSITION_BUFFER_MODE = new InjectionToken<boolean>('CompositionEventMode');\n/**\n * The default accessor for writing a value and listening to changes that is used by the\n * {\\@link NgModel}, {\\@link FormControlDirective}, and {\\@link FormControlName} directives.\n * \n *  ### Example\n *  ```\n *  <input type=\"text\" name=\"searchQuery\" ngModel>\n *  ```\n * \n *  \\@stable\n */\nexport class DefaultValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * Whether the user is creating a composition string (IME events).\n */\nprivate _composing = false;\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n * @param {?} _compositionMode\n */\nconstructor(\nprivate _renderer: Renderer,\nprivate _elementRef: ElementRef,\nprivate _compositionMode: boolean) {\n    if (this._compositionMode == null) {\n      this._compositionMode = !_isAndroid();\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    const /** @type {?} */ normalizedValue = value == null ? '' : value;\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'value', normalizedValue);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => void): void { this.onChange = fn; }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => void): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\n_handleInput(value: any): void {\n    if (!this._compositionMode || (this._compositionMode && !this._composing)) {\n      this.onChange(value);\n    }\n  }\n/**\n * @return {?}\n */\n_compositionStart(): void { this._composing = true; }\n/**\n * @param {?} value\n * @return {?}\n */\n_compositionEnd(value: any): void {\n    this._composing = false;\n    this._compositionMode && this.onChange(value);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]',\n  // TODO: vsavkin replace the above selector with the one below it once\n  // https://github.com/angular/angular/issues/3011 is implemented\n  // selector: '[ngModel],[formControl],[formControlName]',\n  host: {\n    '(input)': '_handleInput($event.target.value)',\n    '(blur)': 'onTouched()',\n    '(compositionstart)': '_compositionStart()',\n    '(compositionend)': '_compositionEnd($event.target.value)'\n  },\n  providers: [DEFAULT_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [COMPOSITION_BUFFER_MODE, ] }, ]},\n];\n}\n\nfunction DefaultValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nDefaultValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDefaultValueAccessor.ctorParameters;\n/** @type {?} */\nDefaultValueAccessor.prototype.onChange;\n/** @type {?} */\nDefaultValueAccessor.prototype.onTouched;\n/**\n * Whether the user is creating a composition string (IME events).\n * @type {?}\n */\nDefaultValueAccessor.prototype._composing;\n/** @type {?} */\nDefaultValueAccessor.prototype._renderer;\n/** @type {?} */\nDefaultValueAccessor.prototype._elementRef;\n/** @type {?} */\nDefaultValueAccessor.prototype._compositionMode;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, ElementRef, Renderer, forwardRef} from '@angular/core';\n\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from './control_value_accessor';\n\nexport const /** @type {?} */ CHECKBOX_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => CheckboxControlValueAccessor),\n  multi: true,\n};\n/**\n * The accessor for writing a value and listening to changes on a checkbox input element.\n * \n *  ### Example\n *  ```\n *  <input type=\"checkbox\" name=\"rememberLogin\" ngModel>\n *  ```\n * \n *  \\@stable\n */\nexport class CheckboxControlValueAccessor implements ControlValueAccessor {\n  onChange = (_: any) => {};\n  onTouched = () => {};\n/**\n * @param {?} _renderer\n * @param {?} _elementRef\n */\nconstructor(private _renderer: <PERSON>derer,\nprivate _elementRef: ElementRef) {}\n/**\n * @param {?} value\n * @return {?}\n */\nwriteValue(value: any): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'checked', value);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnChange(fn: (_: any) => {}): void { this.onChange = fn; }\n/**\n * @param {?} fn\n * @return {?}\n */\nregisterOnTouched(fn: () => {}): void { this.onTouched = fn; }\n/**\n * @param {?} isDisabled\n * @return {?}\n */\nsetDisabledState(isDisabled: boolean): void {\n    this._renderer.setElementProperty(this._elementRef.nativeElement, 'disabled', isDisabled);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector:\n      'input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]',\n  host: {'(change)': 'onChange($event.target.checked)', '(blur)': 'onTouched()'},\n  providers: [CHECKBOX_VALUE_ACCESSOR]\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Renderer, },\n{type: ElementRef, },\n];\n}\n\nfunction CheckboxControlValueAccessor_tsickle_Closure_declarations() {\n/** @type {?} */\nCheckboxControlValueAccessor.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCheckboxControlValueAccessor.ctorParameters;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype.onChange;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype.onTouched;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype._renderer;\n/** @type {?} */\nCheckboxControlValueAccessor.prototype._elementRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * A bridge between a control and a native element.\n *\n * A `ControlValueAccessor` abstracts the operations of writing a new value to a\n * DOM element representing an input control.\n *\n * Please see {@link DefaultValueAccessor} for more information.\n *\n * @stable\n */\nexport interface ControlValueAccessor {\n  /**\n   * Write a new value to the element.\n   */\n  writeValue(obj: any): void;\n\n  /**\n   * Set the function to be called when the control receives a change event.\n   */\n  registerOnChange(fn: any): void;\n\n  /**\n   * Set the function to be called when the control receives a touch event.\n   */\n  registerOnTouched(fn: any): void;\n\n  /**\n   * This function is called when the control status changes to or from \"DISABLED\".\n   * Depending on the value, it will enable or disable the appropriate DOM element.\n   *\n   * @param isDisabled\n   */\n  setDisabledState?(isDisabled: boolean): void;\n}\n/**\n * Used to provide a {\\@link ControlValueAccessor} for form controls.\n * \n * See {\\@link DefaultValueAccessor} for how to implement one.\n * \\@stable\n */\nexport const NG_VALUE_ACCESSOR = new InjectionToken<ControlValueAccessor>('NgValueAccessor');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken, ɵisObservable as isObservable, ɵisPromise as isPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {forkJoin} from 'rxjs/observable/forkJoin';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {map} from 'rxjs/operator/map';\nimport {AsyncValidatorFn, ValidationErrors, Validator, ValidatorFn} from './directives/validators';\nimport {AbstractControl, FormControl} from './model';\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isEmptyInputValue(value: any): boolean {\n  // we don't check for string here so it also works with arrays\n  return value == null || value.length === 0;\n}\n/**\n * Providers for validators to be used for {\\@link FormControl}s in a form.\n * \n * Provide this using `multi: true` to add validators.\n * \n * \\@stable\n */\nexport const NG_VALIDATORS = new InjectionToken<Array<Validator|Function>>('NgValidators');\n/**\n * Providers for asynchronous validators to be used for {\\@link FormControl}s\n * in a form.\n * \n * Provide this using `multi: true` to add validators.\n * \n * See {\\@link NG_VALIDATORS} for more details.\n * \n * \\@stable\n */\nexport const NG_ASYNC_VALIDATORS =\n    new InjectionToken<Array<Validator|Function>>('NgAsyncValidators');\n\nconst /** @type {?} */ EMAIL_REGEXP =\n    /^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+(\\.[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/;\n/**\n * Provides a set of validators used by form controls.\n * \n * A validator is a function that processes a {\\@link FormControl} or collection of\n * controls and returns a map of errors. A null map means that validation has passed.\n * \n * ### Example\n * \n * ```typescript\n * var loginControl = new FormControl(\"\", Validators.required)\n * ```\n * \n * \\@stable\n */\nexport class Validators {\n/**\n * Validator that requires controls to have a value greater than a number.\n * @param {?} min\n * @return {?}\n */\nstatic min(min: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value) || isEmptyInputValue(min)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value = parseFloat(control.value);\n      // Controls with NaN values after parsing should be treated as not having a\n      // minimum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-min\n      return !isNaN(value) && value < min ? {'min': {'min': min, 'actual': control.value}} : null;\n    };\n  }\n/**\n * Validator that requires controls to have a value less than a number.\n * @param {?} max\n * @return {?}\n */\nstatic max(max: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value) || isEmptyInputValue(max)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value = parseFloat(control.value);\n      // Controls with NaN values after parsing should be treated as not having a\n      // maximum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-max\n      return !isNaN(value) && value > max ? {'max': {'max': max, 'actual': control.value}} : null;\n    };\n  }\n/**\n * Validator that requires controls to have a non-empty value.\n * @param {?} control\n * @return {?}\n */\nstatic required(control: AbstractControl): ValidationErrors|null {\n    return isEmptyInputValue(control.value) ? {'required': true} : null;\n  }\n/**\n * Validator that requires control value to be true.\n * @param {?} control\n * @return {?}\n */\nstatic requiredTrue(control: AbstractControl): ValidationErrors|null {\n    return control.value === true ? null : {'required': true};\n  }\n/**\n * Validator that performs email validation.\n * @param {?} control\n * @return {?}\n */\nstatic email(control: AbstractControl): ValidationErrors|null {\n    return EMAIL_REGEXP.test(control.value) ? null : {'email': true};\n  }\n/**\n * Validator that requires controls to have a value of a minimum length.\n * @param {?} minLength\n * @return {?}\n */\nstatic minLength(minLength: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ length: number = control.value ? control.value.length : 0;\n      return length < minLength ?\n          {'minlength': {'requiredLength': minLength, 'actualLength': length}} :\n          null;\n    };\n  }\n/**\n * Validator that requires controls to have a value of a maximum length.\n * @param {?} maxLength\n * @return {?}\n */\nstatic maxLength(maxLength: number): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      const /** @type {?} */ length: number = control.value ? control.value.length : 0;\n      return length > maxLength ?\n          {'maxlength': {'requiredLength': maxLength, 'actualLength': length}} :\n          null;\n    };\n  }\n/**\n * Validator that requires a control to match a regex to its value.\n * @param {?} pattern\n * @return {?}\n */\nstatic pattern(pattern: string|RegExp): ValidatorFn {\n    if (!pattern) return Validators.nullValidator;\n    let /** @type {?} */ regex: RegExp;\n    let /** @type {?} */ regexStr: string;\n    if (typeof pattern === 'string') {\n      regexStr = `^${pattern}$`;\n      regex = new RegExp(regexStr);\n    } else {\n      regexStr = pattern.toString();\n      regex = pattern;\n    }\n    return (control: AbstractControl): ValidationErrors | null => {\n      if (isEmptyInputValue(control.value)) {\n        return null;  // don't validate empty values to allow optional controls\n      }\n      const /** @type {?} */ value: string = control.value;\n      return regex.test(value) ? null :\n                                 {'pattern': {'requiredPattern': regexStr, 'actualValue': value}};\n    };\n  }\n/**\n * No-op validator.\n * @param {?} c\n * @return {?}\n */\nstatic nullValidator(c: AbstractControl): ValidationErrors|null { return null; }\n\n  /**\n   * Compose multiple validators into a single function that returns the union\n   * of the individual error maps.\n   */\n  static compose(validators: null): null;\n  static compose(validators: (ValidatorFn|null|undefined)[]): ValidatorFn|null;\n/**\n * @param {?} validators\n * @return {?}\n */\nstatic compose(validators: (ValidatorFn|null|undefined)[]|null): ValidatorFn|null {\n    if (!validators) return null;\n    const /** @type {?} */ presentValidators: ValidatorFn[] = /** @type {?} */(( validators.filter(isPresent) as any));\n    if (presentValidators.length == 0) return null;\n\n    return function(control: AbstractControl) {\n      return _mergeErrors(_executeValidators(control, presentValidators));\n    };\n  }\n/**\n * @param {?} validators\n * @return {?}\n */\nstatic composeAsync(validators: (AsyncValidatorFn|null)[]): AsyncValidatorFn|null {\n    if (!validators) return null;\n    const /** @type {?} */ presentValidators: AsyncValidatorFn[] = /** @type {?} */(( validators.filter(isPresent) as any));\n    if (presentValidators.length == 0) return null;\n\n    return function(control: AbstractControl) {\n      const /** @type {?} */ observables = _executeAsyncValidators(control, presentValidators).map(toObservable);\n      return map.call(forkJoin(observables), _mergeErrors);\n    };\n  }\n}\n/**\n * @param {?} o\n * @return {?}\n */\nfunction isPresent(o: any): boolean {\n  return o != null;\n}\n/**\n * @param {?} r\n * @return {?}\n */\nexport function toObservable(r: any): Observable<any> {\n  const /** @type {?} */ obs = isPromise(r) ? fromPromise(r) : r;\n  if (!(isObservable(obs))) {\n    throw new Error(`Expected validator to return Promise or Observable.`);\n  }\n  return obs;\n}\n/**\n * @param {?} control\n * @param {?} validators\n * @return {?}\n */\nfunction _executeValidators(control: AbstractControl, validators: ValidatorFn[]): any[] {\n  return validators.map(v => v(control));\n}\n/**\n * @param {?} control\n * @param {?} validators\n * @return {?}\n */\nfunction _executeAsyncValidators(control: AbstractControl, validators: AsyncValidatorFn[]): any[] {\n  return validators.map(v => v(control));\n}\n/**\n * @param {?} arrayOfErrors\n * @return {?}\n */\nfunction _mergeErrors(arrayOfErrors: ValidationErrors[]): ValidationErrors|null {\n  const /** @type {?} */ res: {[key: string]: any} =\n      arrayOfErrors.reduce((res: ValidationErrors | null, errors: ValidationErrors | null) => {\n        return errors != null ? {... /** @type {?} */((res)), ...errors} : /** @type {?} */(( res));\n      }, {});\n  return Object.keys(res).length === 0 ? null : res;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AbstractControlDirective} from './abstract_control_directive';\nimport {Form} from './form_interface';\n/**\n * A directive that contains multiple {\\@link NgControl}s.\n * \n * Only used by the forms module.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class ControlContainer extends AbstractControlDirective {\n  name: string;\n/**\n * Get the form to which this container belongs.\n * @return {?}\n */\nget formDirective(): Form|null { return null; }\n/**\n * Get the path to this container.\n * @return {?}\n */\nget path(): string[]|null { return null; }\n}\n\nfunction ControlContainer_tsickle_Closure_declarations() {\n/** @type {?} */\nControlContainer.prototype.name;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Observable} from 'rxjs/Observable';\nimport {AbstractControl} from '../model';\nimport {ValidationErrors} from './validators';\n/**\n * Base class for control directives.\n * \n * Only used internally in the forms module.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class AbstractControlDirective {\n/**\n * @abstract\n * @return {?}\n */\ncontrol() {}\n/**\n * @return {?}\n */\nget value(): any { return this.control ? this.control.value : null; }\n/**\n * @return {?}\n */\nget valid(): boolean|null { return this.control ? this.control.valid : null; }\n/**\n * @return {?}\n */\nget invalid(): boolean|null { return this.control ? this.control.invalid : null; }\n/**\n * @return {?}\n */\nget pending(): boolean|null { return this.control ? this.control.pending : null; }\n/**\n * @return {?}\n */\nget errors(): ValidationErrors|null { return this.control ? this.control.errors : null; }\n/**\n * @return {?}\n */\nget pristine(): boolean|null { return this.control ? this.control.pristine : null; }\n/**\n * @return {?}\n */\nget dirty(): boolean|null { return this.control ? this.control.dirty : null; }\n/**\n * @return {?}\n */\nget touched(): boolean|null { return this.control ? this.control.touched : null; }\n/**\n * @return {?}\n */\nget untouched(): boolean|null { return this.control ? this.control.untouched : null; }\n/**\n * @return {?}\n */\nget disabled(): boolean|null { return this.control ? this.control.disabled : null; }\n/**\n * @return {?}\n */\nget enabled(): boolean|null { return this.control ? this.control.enabled : null; }\n/**\n * @return {?}\n */\nget statusChanges(): Observable<any>|null {\n    return this.control ? this.control.statusChanges : null;\n  }\n/**\n * @return {?}\n */\nget valueChanges(): Observable<any>|null {\n    return this.control ? this.control.valueChanges : null;\n  }\n/**\n * @return {?}\n */\nget path(): string[]|null { return null; }\n/**\n * @param {?=} value\n * @return {?}\n */\nreset(value: any = undefined): void {\n    if (this.control) this.control.reset(value);\n  }\n/**\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\nhasError(errorCode: string, path?: string[]): boolean {\n    return this.control ? this.control.hasError(errorCode, path) : false;\n  }\n/**\n * @param {?} errorCode\n * @param {?=} path\n * @return {?}\n */\ngetError(errorCode: string, path?: string[]): any {\n    return this.control ? this.control.getError(errorCode, path) : null;\n  }\n}\n"], "names": ["formDirectiveProvider", "formControlBinding", "Examples", "resolvedPromise", "looseIdentical", "_buildValueString", "_extractId", "getDOM", "isObservable", "isPromise"], "mappings": ";;;;;;AmCAA;;;;;;;;;;;;;;;AAoBA,AAAA,MAAA,wBAAA,CAAA;;;;;IAKA,OALgB,GAKhB,GALgB;;;;IAShB,IAPG,KAAA,GAOH,EAPqB,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,KAAC,GAAO,IAAA,CAAK,EAAC;;;;IAWtE,IATG,KAAA,GASH,EAT8B,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,KAAC,GAAO,IAAA,CAAK,EAAC;;;;IAa/E,IAXG,OAAA,GAWH,EAXgC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,OAAC,GAAS,IAAA,CAAK,EAAC;;;;IAenF,IAbG,OAAA,GAaH,EAbgC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,OAAC,GAAS,IAAA,CAAK,EAAC;;;;IAiBnF,IAfG,MAAA,GAeH,EAfwC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,MAAC,GAAQ,IAAA,CAAK,EAAC;;;;IAmB1F,IAjBG,QAAA,GAiBH,EAjBiC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,QAAC,GAAU,IAAA,CAAK,EAAC;;;;IAqBrF,IAnBG,KAAA,GAmBH,EAnB8B,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,KAAC,GAAO,IAAA,CAAK,EAAC;;;;IAuB/E,IArBG,OAAA,GAqBH,EArBgC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,OAAC,GAAS,IAAA,CAAK,EAAC;;;;IAyBnF,IAvBG,SAAA,GAuBH,EAvBkC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,SAAC,GAAW,IAAA,CAAK,EAAC;;;;IA2BvF,IAzBG,QAAA,GAyBH,EAzBiC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,QAAC,GAAU,IAAA,CAAK,EAAC;;;;IA6BrF,IA3BG,OAAA,GA2BH,EA3BgC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,OAAC,GAAS,IAAA,CAAK,EAAC;;;;IA+BnF,IA7BG,aAAA,GA6BH;QACI,OA7BO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,aAAC,GAAe,IAAA,CAAK;KA8BzD;;;;IAIH,IA/BG,YAAA,GA+BH;QACI,OA/BO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,YAAC,GAAc,IAAA,CAAK;KAgCxD;;;;IAIH,IAjCG,IAAA,GAiCH,EAjC8B,OAAO,IAAA,CAAK,EAAC;;;;;IAsC3C,KApCG,CAAA,KAoCH,GApCG,SAAA,EAoCH;QACI,IAAI,IApCC,CAAI,OAAC;YAAQ,IAAA,CAAK,OAAC,CAAO,KAAC,CAAK,KAAC,CAAK,CAAC;KAqC7C;;;;;;IAMH,QAxCG,CAAA,SAAA,EAAA,IAAA,EAwCH;QACI,OAxCO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,QAAC,CAAQ,SAAC,EAAU,IAAA,CAAK,GAAG,KAAA,CAAM;KAyCtE;;;;;;IAMH,QA5CG,CAAA,SAAA,EAAA,IAAA,EA4CH;QACI,OA5CO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,QAAC,CAAQ,SAAC,EAAU,IAAA,CAAK,GAAG,IAAA,CAAK;KA6CrE;CACF;;AD7GD;;;;;;;AASA,AAEA;;;;;;;;AAQA,AAAA,MAAA,gBAAC,SAAA,wBAAA,CAAD;;;;;IAMA,IAAG,aAAA,GAAH,EAAmC,OAAO,IAAA,CAAK,EAAC;;;;;IAKhD,IAAG,IAAA,GAAH,EAA8B,OAAO,IAAA,CAAK,EAAC;CAC1C,AAED,AAGC;;ADpCD;;;;;;;AASA,AAEA,AACA,AACA,AAGA;;;;AAIA,SAAA,iBAAA,CAJC,KAAA,EAID;;IAEE,OAJO,KAAA,IAAS,IAAA,IAAQ,KAAA,CAAM,MAAC,KAAU,CAAA,CAAE;CAK5C;;;;;;;;AAQD,AAHC,MAAA,aAAA,GAAA,IAAA,cAAA,CAAA,cAAA,CAAA,CAAA;;;;;;;;;;;AAcD,AAFC,MAAA,mBAAA,GAGG,IAAI,cAAc,CAA4B,mBAAmB,CAAC,CAAC;AAEvE,MAFM,YAAA,GAGF,4LAA4L,CAFC;;;;;;;;;;;;;;;AAiBjM,AAAA,MAAA,UAAA,CAAA;;;;;;IAMA,OAHG,GAAA,CAAA,GAAA,EAGH;QACI,OAHO,CAAA,OAAU,KAGrB;YACM,IAAI,iBAHC,CAAiB,OAAC,CAAO,KAAC,CAAK,IAAI,iBAAA,CAAkB,GAAC,CAAG,EAAE;gBAI9D,OAHO,IAAA,CAAK;aAIb;YACD,uBAHM,KAAA,GAAQ,UAAA,CAAW,OAAC,CAAO,KAAC,CAAK,CAAC;;;YAMxC,OAHO,CAAA,KAAE,CAAK,KAAC,CAAK,IAAI,KAAA,GAAQ,GAAA,GAAM,EAAA,KAAE,EAAM,EAAA,KAAE,EAAM,GAAA,EAAK,QAAA,EAAU,OAAA,CAAQ,KAAC,EAAK,EAAC,GAAG,IAAA,CAAK;SAI7F,CAHC;KAIH;;;;;;IAMH,OAJG,GAAA,CAAA,GAAA,EAIH;QACI,OAJO,CAAA,OAAU,KAIrB;YACM,IAAI,iBAJC,CAAiB,OAAC,CAAO,KAAC,CAAK,IAAI,iBAAA,CAAkB,GAAC,CAAG,EAAE;gBAK9D,OAJO,IAAA,CAAK;aAKb;YACD,uBAJM,KAAA,GAAQ,UAAA,CAAW,OAAC,CAAO,KAAC,CAAK,CAAC;;;YAOxC,OAJO,CAAA,KAAE,CAAK,KAAC,CAAK,IAAI,KAAA,GAAQ,GAAA,GAAM,EAAA,KAAE,EAAM,EAAA,KAAE,EAAM,GAAA,EAAK,QAAA,EAAU,OAAA,CAAQ,KAAC,EAAK,EAAC,GAAG,IAAA,CAAK;SAK7F,CAJC;KAKH;;;;;;IAMH,OALG,QAAA,CAAA,OAAA,EAKH;QACI,OALO,iBAAA,CAAkB,OAAC,CAAO,KAAC,CAAK,GAAG,EAAA,UAAE,EAAW,IAAA,EAAK,GAAG,IAAA,CAAK;KAMrE;;;;;;IAMH,OANG,YAAA,CAAA,OAAA,EAMH;QACI,OANO,OAAA,CAAQ,KAAC,KAAS,IAAA,GAAO,IAAA,GAAO,EAAA,UAAE,EAAW,IAAA,EAAK,CAAC;KAO3D;;;;;;IAMH,OAPG,KAAA,CAAA,OAAA,EAOH;QACI,OAPO,YAAA,CAAa,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,GAAG,IAAA,GAAO,EAAA,OAAE,EAAQ,IAAA,EAAK,CAAC;KAQlE;;;;;;IAMH,OARG,SAAA,CAAA,SAAA,EAQH;QACI,OARO,CAAA,OAAU,KAQrB;YACM,IAAI,iBARC,CAAiB,OAAC,CAAO,KAAC,CAAK,EAAE;gBASpC,OARO,IAAA,CAAK;aASb;YACD,uBARM,MAAA,GAAiB,OAAA,CAAQ,KAAC,GAAO,OAAA,CAAQ,KAAC,CAAK,MAAC,GAAQ,CAAA,CAAE;YAShE,OARO,MAAA,GAAS,SAAA;gBASZ,EAAC,WARC,EAAY,EAAA,gBAAE,EAAiB,SAAA,EAAW,cAAA,EAAgB,MAAA,EAAO,EAAC;gBASpE,IAAI,CARC;SASV,CARC;KASH;;;;;;IAMH,OATG,SAAA,CAAA,SAAA,EASH;QACI,OATO,CAAA,OAAU,KASrB;YACM,uBATM,MAAA,GAAiB,OAAA,CAAQ,KAAC,GAAO,OAAA,CAAQ,KAAC,CAAK,MAAC,GAAQ,CAAA,CAAE;YAUhE,OATO,MAAA,GAAS,SAAA;gBAUZ,EAAC,WATC,EAAY,EAAA,gBAAE,EAAiB,SAAA,EAAW,cAAA,EAAgB,MAAA,EAAO,EAAC;gBAUpE,IAAI,CATC;SAUV,CATC;KAUH;;;;;;IAMH,OAVG,OAAA,CAAA,OAAA,EAUH;QACI,IAAI,CAVC,OAAC;YAAQ,OAAO,UAAA,CAAW,aAAC,CAAa;QAW9C,qBAVI,KAAO,CAAO;QAWlB,qBAVI,QAAU,CAAO;QAWrB,IAAI,OAVO,OAAA,KAAY,QAAA,EAAU;YAW/B,QAAQ,GAVG,CAUjB,CAAA,EAViB,OAAK,CAUtB,CAAA,CAV6B,CAAG;YAW1B,KAAK,GAVG,IAAI,MAAA,CAAO,QAAC,CAAQ,CAAC;SAW9B;aAVM;YAWL,QAAQ,GAVG,OAAA,CAAQ,QAAC,EAAQ,CAAE;YAW9B,KAAK,GAVG,OAAA,CAAQ;SAWjB;QACD,OAVO,CAAA,OAAU,KAUrB;YACM,IAAI,iBAVC,CAAiB,OAAC,CAAO,KAAC,CAAK,EAAE;gBAWpC,OAVO,IAAA,CAAK;aAWb;YACD,uBAVM,KAAA,GAAgB,OAAA,CAAQ,KAAC,CAAK;YAWpC,OAVO,KAAA,CAAM,IAAC,CAAI,KAAC,CAAK,GAAG,IAAA;gBAWA,EAAC,SAVC,EAAU,EAAA,iBAAE,EAAkB,QAAA,EAAU,aAAA,EAAe,KAAA,EAAM,EAAC,CAAC;SAW7F,CAVC;KAWH;;;;;;IAMH,OAXG,aAAA,CAAA,CAAA,EAWH,EAXoE,OAAO,IAAA,CAAK,EAAC;;;;;IAuBjF,OAfG,OAAA,CAAA,UAAA,EAeH;QACI,IAAI,CAfC,UAAC;YAAW,OAAO,IAAA,CAAK;QAgB7B,uBAfM,iBAAA,IAAmC,UAAA,CAAW,MAAC,CAAM,SAAC,CAAa,CAAA,CAAI;QAgB7E,IAAI,iBAfC,CAAiB,MAAC,IAAS,CAAA;YAAG,OAAO,IAAA,CAAK;QAiB/C,OAfO,UAAA,OAAkB,EAe7B;YACM,OAfO,YAAA,CAAa,kBAAC,CAAkB,OAAC,EAAQ,iBAAA,CAAkB,CAAC,CAAC;SAgBrE,CAfC;KAgBH;;;;;IAKH,OAlBG,YAAA,CAAA,UAAA,EAkBH;QACI,IAAI,CAlBC,UAAC;YAAW,OAAO,IAAA,CAAK;QAmB7B,uBAlBM,iBAAA,IAAwC,UAAA,CAAW,MAAC,CAAM,SAAC,CAAa,CAAA,CAAI;QAmBlF,IAAI,iBAlBC,CAAiB,MAAC,IAAS,CAAA;YAAG,OAAO,IAAA,CAAK;QAoB/C,OAlBO,UAAA,OAAkB,EAkB7B;YACM,uBAlBM,WAAA,GAAc,uBAAA,CAAwB,OAAC,EAAQ,iBAAA,CAAkB,CAAC,GAAC,CAAG,YAAC,CAAY,CAAC;YAmB1F,OAlBO,GAAA,CAAI,IAAC,CAAI,QAAC,CAAQ,WAAC,CAAW,EAAE,YAAA,CAAa,CAAC;SAmBtD,CAlBC;KAmBH;CACF;;;;;AAKD,SAAA,SAAA,CArBC,CAAA,EAqBD;IACE,OArBO,CAAA,IAAK,IAAA,CAAK;CAsBlB;;;;;AAKD,AAAA,SAAA,YAAA,CAxBC,CAAA,EAwBD;IACE,uBAxBM,GAAA,GAAMS,UAAA,CAAU,CAAC,CAAC,GAAG,WAAA,CAAY,CAAC,CAAC,GAAG,CAAA,CAAE;IAyB9C,IAAI,EAxBED,aAAC,CAAY,GAAC,CAAG,CAAC,EAAE;QAyBxB,MAxBM,IAAI,KAAA,CAAM,CAwBpB,mDAAA,CAxBqB,CAAqD,CAAC;KAyBxE;IACD,OAxBO,GAAA,CAAI;CAyBZ;;;;;;AAMD,SAAA,kBAAA,CA5BC,OAAA,EAAA,UAAA,EA4BD;IACE,OA5BO,UAAA,CAAW,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,OAAC,CAAO,CAAC,CAAC;CA6BxC;;;;;;AAMD,SAAA,uBAAA,CAhCC,OAAA,EAAA,UAAA,EAgCD;IACE,OAhCO,UAAA,CAAW,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,OAAC,CAAO,CAAC,CAAC;CAiCxC;;;;;AAKD,SAAA,YAAA,CAnCC,aAAA,EAmCD;IACE,uBAnCM,GAAA,GAoCF,aAAa,CAnCC,MAAC,CAAM,CAAC,GAAwB,EAAM,MAA2B,KAkCrF;QAEQ,OAnCO,MAAA,IAAU,IAAA,GAmCzB,MAAA,CAAA,MAAA,CAAA,EAAA,qBAnCkC,GAAG,IAAM,MAAI,CAmC/C,KAnCwD,GAAA,EAAA,CAAM;KAoCvD,EAnCE,EAAA,CAAG,CAAC;IAoCX,OAnCO,MAAA,CAAO,IAAC,CAAI,GAAC,CAAG,CAAC,MAAC,KAAU,CAAA,GAAI,IAAA,GAAO,GAAA,CAAI;CAoCnD;;ADjQD;;;;;;;AASA,AAoCA;;;;;;AAMA,AAAC,MAAA,iBAAA,GAAA,IAAA,cAAA,CAAA,iBAAA,CAAA,CAAA;;ADnDD;;;;;;;AASA,AAEA,AAEA,AADO,MAAM,uBAAA,GAA+B;IAE1C,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,4BAAA,CAA6B;IAE3D,KAAA,EADO,IAAA;CAER,CADC;;;;;;;;;;;AAYF,AAAA,MAAA,4BAAA,CAAA;;;;;IAOA,WAAA,CAFsB,SAAW,EAAkB,WAAa,EAEhE;QAFsB,IAAtB,CAAA,SAAsB,GAAA,SAAA,CAAW;QAAkB,IAAnD,CAAA,WAAmD,GAAA,WAAA,CAAa;QAJ9D,IAAF,CAAA,QAAU,GACG,CAAA,CAAI,KADjB,GACyB,CAAG;QAA1B,IAAF,CAAA,SAAW,GACG,MADd,GACoB,CAAG;KAEoD;;;;;IAQ3E,UANG,CAAA,KAAA,EAMH;QACI,IAAI,CANC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,SAAA,EAAW,KAAA,CAAM,CAAC;KAOrF;;;;;IAKH,gBAVG,CAAA,EAAA,EAUH,EAV+C,IAAA,CAAK,QAAC,GAAU,EAAA,CAAG,EAAC;;;;;IAenE,iBAdG,CAAA,EAAA,EAcH,EAd0C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IAmB/D,gBAjBG,CAAA,UAAA,EAiBH;QACI,IAAI,CAjBC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KAkB3F;;AAhBI,4BAAP,CAAA,UAAO,GAAoC;IAkB3C,EAjBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAkBvB,QAAQ,EACJ,uGAAuG;gBAC3G,IAAI,EAjBE,EAAA,UAAE,EAAW,iCAAA,EAAmC,QAAA,EAAU,aAAA,EAAc;gBAkB9E,SAAS,EAjBE,CAAA,uBAAE,CAAuB;aAkBrC,EAjBC,EAAG;CAkBJ,CAjBC;;;;AAED,4BAAD,CAAA,cAAC,GAAA,MAAA;IAoBD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC,AAGF,AAgBC;;AD9FD;;;;;;;AASA,AACA,AACA,AAEA,AADO,MAAM,sBAAA,GAA8B;IAEzC,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,oBAAA,CAAqB;IAEnD,KAAC,EADM,IAAA;CAER,CADC;;;;;;AAOF,SAAA,UAAA,GAAA;IACE,uBADM,SAAA,GAAYD,OAAA,EAAO,GAAIA,OAAA,EAAO,CAAE,YAAC,EAAY,GAAI,EAAA,CAAG;IAE1D,OADO,eAAA,CAAgB,IAAC,CAAI,SAAC,CAAS,WAAC,EAAW,CAAE,CAAC;CAEtD;;;;;AAKD,AAAC,MAAA,uBAAA,GAAA,IAAA,cAAA,CAAA,sBAAA,CAAA,CAAA;;;;;;;;;;;;AAYD,AAAA,MAAA,oBAAA,CAAA;;;;;;IAYA,WAAA,CAFc,SAAW,EAAkB,WAAa,EACxC,gBAAkB,EAClC;QAFc,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;QAAkB,IAA3C,CAAA,WAA2C,GAAA,WAAA,CAAa;QACxC,IAAhB,CAAA,gBAAgB,GAAA,gBAAA,CAAkB;QAVhC,IAAF,CAAA,QAAU,GAEG,CAAA,CAAI,KAFjB,GAEyB,CAAG;QAD1B,IAAF,CAAA,SAAW,GAEG,MAFd,GAEoB,CAAG;;;;QAGpB,IAAH,CAAA,UAAG,GAAA,KAAA,CAAA;QASC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG,CAAC,UAAU,EAAE,CAAC;SACvC;KACF;;;;;IAKH,UAPG,CAAA,KAAA,EAOH;QACI,uBAPM,eAAA,GAAkB,KAAA,IAAS,IAAA,GAAO,EAAA,GAAK,KAAA,CAAM;QAQnD,IAAI,CAPC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,OAAA,EAAS,eAAA,CAAgB,CAAC;KAQ7F;;;;;IAKH,gBAVG,CAAA,EAAA,EAUH,EAViD,IAAA,CAAK,QAAC,GAAU,EAAA,CAAG,EAAC;;;;;IAerE,iBAdG,CAAA,EAAA,EAcH,EAd4C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IAmBjE,gBAjBG,CAAA,UAAA,EAiBH;QACI,IAAI,CAjBC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KAkB3F;;;;;IAKH,YApBG,CAAA,KAAA,EAoBH;QACI,IAAI,CApBC,IAAC,CAAI,gBAAC,KAAmB,IAAE,CAAI,gBAAC,IAAmB,CAAA,IAAE,CAAI,UAAC,CAAU,EAAE;YAqBzE,IAAI,CApBC,QAAC,CAAQ,KAAC,CAAK,CAAC;SAqBtB;KACF;;;;IAIH,iBAtBG,GAsBH,EAtB8B,IAAA,CAAK,UAAC,GAAY,IAAA,CAAK,EAAC;;;;;IA2BtD,eAzBG,CAAA,KAAA,EAyBH;QARE,IAAA,CAhBO,UAAC,GAAY,KAAA,CAAM;QA0BxB,IAAI,CAzBC,gBAAC,IAAmB,IAAA,CAAK,QAAC,CAAQ,KAAC,CAAK,CAAC;KA0B/C;;AAxBI,oBAAP,CAAA,UAAO,GAAoC;IA0B3C,EAzBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA0BvB,QAAQ,EACJ,8MAA8M;;;;gBAIlN,IAAI,EAzBE;oBA0BJ,SAAS,EAzBE,mCAAA;oBA0BX,QAAQ,EAzBE,aAAA;oBA0BV,oBAAoB,EAzBE,qBAAA;oBA0BtB,kBAAkB,EAzBE,sCAAA;iBA0BrB;gBACD,SAAS,EAzBE,CAAA,sBAAE,CAAsB;aA0BpC,EAzBC,EAAG;CA0BJ,CAzBC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IA4BD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,uBAAuB,EAAG,EAAE,EAAG,EAAC;CACzG,CAAC,AAGF,AAuBC;;AD9JD;;;;;;;;;;;AAeA,AAAA,SAAA,kBAAA,CAJC,SAAA,EAID;IACE,IAAI,EAJY,SAAC,GAAU,QAAC,EAAS;QAKnC,OAJO,CAAA,CAAI,KAAoB,EAAY,SAAC,GAAU,QAAC,CAAQ,CAAC,CAAC,CAAC;KAKnE;SAJM;QAKL,QAJoB,SAAC,EAAS;KAK/B;CACF;;;;;AAKD,AAAA,SAAA,uBAAA,CAPC,SAAA,EAOD;IAEE,IAAI,EAPiB,SAAC,GAAU,QAAC,EAAS;QAQxC,OAPO,CAAA,CAAI,KAAoB,EAAiB,SAAC,GAAU,QAAC,CAAQ,CAAC,CAAC,CAAC;KAQxE;SAPM;QAQL,QAPyB,SAAC,EAAS;KAQpC;CACF;;ADjCD;;;;;;;AASA,AAEA,AAEA,AADO,MAAM,qBAAA,GAA6B;IAExC,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,mBAAA,CAAoB;IAElD,KAAA,EADO,IAAA;CAER,CADC;;;;;;;;;;AAWF,AAAA,MAAA,mBAAA,CAAA;;;;;IAOA,WAAA,CAFsB,SAAW,EAAkB,WAAa,EAEhE;QAFsB,IAAtB,CAAA,SAAsB,GAAA,SAAA,CAAW;QAAkB,IAAnD,CAAA,WAAmD,GAAA,WAAA,CAAa;QAJ9D,IAAF,CAAA,QAAU,GACG,CAAA,CAAI,KADjB,GACyB,CAAG;QAA1B,IAAF,CAAA,SAAW,GACG,MADd,GACoB,CAAG;KAEoD;;;;;IAQ3E,UANG,CAAA,KAAA,EAMH;;QAEI,uBANM,eAAA,GAAkB,KAAA,IAAS,IAAA,GAAO,EAAA,GAAK,KAAA,CAAM;QAOnD,IAAI,CANC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,OAAA,EAAS,eAAA,CAAgB,CAAC;KAO7F;;;;;IAKH,gBATG,CAAA,EAAA,EASH;QACI,IAAI,CATC,QAAC,GAAU,CAAA,KAAE,KAStB,EATiC,EAAA,CAAG,KAAC,IAAQ,EAAA,GAAK,IAAA,GAAO,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC,EAAC,CAAE;KAU5E;;;;;IAKH,iBAbG,CAAA,EAAA,EAaH,EAb4C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IAkBjE,gBAhBG,CAAA,UAAA,EAgBH;QACI,IAAI,CAhBC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KAiB3F;;AAfI,mBAAP,CAAA,UAAO,GAAoC;IAiB3C,EAhBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAiBvB,QAAQ,EACJ,iGAAiG;gBACrG,IAAI,EAhBE;oBAiBJ,UAAU,EAhBE,+BAAA;oBAiBZ,SAAS,EAhBE,+BAAA;oBAiBX,QAAQ,EAhBE,aAAA;iBAiBX;gBACD,SAAS,EAhBE,CAAA,qBAAE,CAAqB;aAiBnC,EAhBC,EAAG;CAiBJ,CAhBC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA;IAmBD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC,AAGF,AAgBC;;ADrGD;;;;;;;AAUA,AAIA;;;AAGA,SAAA,aAAA,GAAA;IACE,MAHM,IAAI,KAAA,CAAM,eAAC,CAAe,CAAC;CAIlC;;;;;;;;;;AAUD,AAAA,MAAA,SAHC,SAAA,wBAAA,CAGD;IAAA,WAAA,GAAA;;;;;QAIA,IAAA,CAAA,OALG,GAAA,IAAA,CAAA;QAMD,IAAF,CAAA,IAAM,GALgB,IAAA,CAAK;QAMzB,IAAF,CAAA,aAAe,GAL8B,IAAA,CAAK;;;;QASlD,IAAA,CAAA,cAPG,GAAA,EAAA,CAAA;;;;QAWH,IAAA,CAAA,mBATG,GAAA,EAAA,CAAA;KAwBF;;;;IAXD,IAXG,SAAA,GAWH,EAXsC,QAAoB,aAAC,EAAa,EAAE,EAAC;;;;IAe3E,IAdG,cAAA,GAcH,EAdgD,QAAyB,aAAC,EAAa,EAAE,EAAC;;;;;;IAoB1F,iBAlBY,CAAA,QAAA,EAkBZ,GAlBY;CAmBX,AAED,AAoBC;;ADhFD;;;;;;;AASA,AAEA,AACA,AAEA,AADO,MAAM,oBAAA,GAA4B;IAEvC,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,yBAAA,CAA0B;IAExD,KAAA,EADO,IAAA;CAER,CADC;;;;AAKF,AAAA,MAAA,oBAAA,CAAA;IAAA,WAAA,GAAA;QAEU,IAAV,CAAA,UAAU,GAAoB,EAAA,CAAG;KAmDhC;;;;;;IA9CD,GAHG,CAAA,OAAA,EAAA,QAAA,EAGH;QACI,IAAI,CAHC,UAAC,CAAU,IAAC,CAAI,CAAC,OAAC,EAAQ,QAAA,CAAS,CAAC,CAAC;KAI3C;;;;;IAKH,MANG,CAAA,QAAA,EAMH;QACI,KAAK,qBANI,CAAA,GAAI,IAAA,CAAK,UAAC,CAAU,MAAC,GAAQ,CAAA,EAAG,CAAA,IAAK,CAAA,EAAG,EAAA,CAAG,EAAE;YAOpD,IAAI,IANC,CAAI,UAAC,CAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAA,EAAU;gBAOtC,IAAI,CANC,UAAC,CAAU,MAAC,CAAM,CAAC,EAAE,CAAA,CAAE,CAAC;gBAO7B,OAAO;aACR;SACF;KACF;;;;;IAKH,MATG,CAAA,QAAA,EASH;QACI,IAAI,CATC,UAAC,CAAU,OAAC,CAAO,CAAC,CAAC,KAS9B;YACM,IAAI,IATC,CAAI,YAAC,CAAY,CAAC,EAAE,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAC,KAAK,QAAA,EAAU;gBAUvD,CAAC,CATC,CAAC,CAAC,CAAC,WAAC,CAAW,QAAC,CAAQ,KAAC,CAAK,CAAC;aAUlC;SACF,CATC,CAAC;KAUJ;;;;;;IAPA,YAAA,CAcG,WAAmD,EACnD,QAAmC,EAfzC;QAgBI,IAAI,CAbC,WAAC,CAAW,CAAC,CAAC,CAAC,OAAC;YAAQ,OAAO,KAAA,CAAM;QAc1C,OAbO,WAAA,CAAY,CAAC,CAAC,CAAC,OAAC,KAAW,QAAA,CAAS,QAAC,CAAQ,OAAC;YAcjD,WAAW,CAbC,CAAC,CAAC,CAAC,IAAC,KAAQ,QAAA,CAAS,IAAC,CAAI;KAc3C;;AAZI,oBAAP,CAAA,UAAO,GAAoC;IAc3C,EAbE,IAAA,EAAM,UAAA,EAAW;CAclB,CAbC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA,EAgBA,CAAC;AAGF,AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,AAAA,MAAA,yBAAA,CAAA;;;;;;;IA0BA,WAAA,CApCc,SAAW,EAAkB,WAAa,EAC1C,SAAW,EAA8B,SAAW,EAmClE;QApCc,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;QAAkB,IAA3C,CAAA,WAA2C,GAAA,WAAA,CAAa;QAC1C,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;QAA8B,IAAvD,CAAA,SAAuD,GAAA,SAAA,CAAW;QAuBhE,IAAF,CAAA,QAAU,GAhCG,MAgCb,GAhCmB,CAAG;QAiCpB,IAAF,CAAA,SAAW,GAhCG,MAgCd,GAhCoB,CAAG;KAQoD;;;;IA2C3E,QAzCG,GAyCH;QACI,IAAI,CAzCC,QAAC,GAAU,IAAA,CAAK,SAAC,CAAS,GAAC,CAAG,SAAC,CAAS,CAAC;QA0C9C,IAAI,CAzCC,UAAC,EAAU,CAAE;QA0ClB,IAAI,CAzCC,SAAC,CAAS,GAAC,CAAG,IAAC,CAAI,QAAC,EAAS,IAAA,CAAK,CAAC;KA0CzC;;;;IAIH,WA3CG,GA2CH,EA3CwB,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,IAAC,CAAI,CAAC,EAAC;;;;;IAgDrD,UA9CG,CAAA,KAAA,EA8CH;QACI,IAAI,CA9CC,MAAC,GAAQ,KAAA,KAAU,IAAA,CAAK,KAAC,CAAK;QA+CnC,IAAI,CA9CC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,SAAA,EAAW,IAAA,CAAK,MAAC,CAAM,CAAC;KA+C3F;;;;;IAKH,gBAjDG,CAAA,EAAA,EAiDH;QACI,IAAI,CAjDC,GAAC,GAAK,EAAA,CAAG;QAkDd,IAAI,CAjDC,QAAC,GAAU,MAiDpB;YACM,EAAE,CAjDC,IAAC,CAAI,KAAC,CAAK,CAAC;YAkDf,IAAI,CAjDC,SAAC,CAAS,MAAC,CAAM,IAAC,CAAI,CAAC;SAkD7B,CAjDC;KAkDH;;;;;IAKH,WApDG,CAAA,KAAA,EAoDH,EApDkC,IAAA,CAAK,UAAC,CAAU,KAAC,CAAK,CAAC,EAAC;;;;;IAyD1D,iBAvDG,CAAA,EAAA,EAuDH,EAvD0C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IA4D/D,gBA1DG,CAAA,UAAA,EA0DH;QACI,IAAI,CA1DC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KA2D3F;;;;IAxDA,UAAA,GAAH;QA6DI,IAAI,IA5DC,CAAI,IAAC,IAAO,IAAA,CAAK,eAAC,IAAkB,IAAA,CAAK,IAAC,KAAQ,IAAA,CAAK,eAAC,EAAgB;YA6D3E,IAAI,CA5DC,eAAC,EAAe,CAAE;SA6DxB;QACD,IAAI,CA5DC,IAAC,CAAI,IAAC,IAAO,IAAA,CAAK,eAAC;YAAgB,IAAA,CAAK,IAAC,GAAM,IAAA,CAAK,eAAC,CAAe;KA6D1E;;;;IA1DA,eAAA,GAAH;QA+DI,MA9DM,IAAI,KAAA,CAAM,CA8DpB;;;IAGA,CAAK,CA9DC,CAAC;KA+DJ;;AA7DI,yBAAP,CAAA,UAAO,GAAoC;IA+D3C,EA9DE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA+DvB,QAAQ,EACJ,8FAA8F;gBAClG,IAAI,EA9DE,EAAA,UAAE,EAAW,YAAA,EAAc,QAAA,EAAU,aAAA,EAAc;gBA+DzD,SAAS,EA9DE,CAAA,oBAAE,CAAoB;aA+DlC,EA9DC,EAAG;CA+DJ,CA9DC;;;;AAED,yBAAD,CAAA,cAAC,GAAA,MAAA;IAiED,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,oBAAoB,GAAG;IAC9B,EAAC,IAAI,EAAE,QAAQ,GAAG;CACjB,CAAC;AA/DK,yBAAP,CAAA,cAAO,GAAyD;IAiEhE,MAAM,EAhEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAiEzB,iBAAiB,EAhEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAiEpC,OAAO,EAhEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAiEzB,CAhEC,AAmEF,AA2CC;;AD1RD;;;;;;;AASA,AAEA,AAEA,AADO,MAAM,oBAAA,GAAiC;IAE5C,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,kBAAA,CAAmB;IAEjD,KAAA,EADO,IAAA;CAER,CADC;;;;;;;;;;AAWF,AAAA,MAAA,kBAAA,CAAA;;;;;IAOA,WAAA,CAFsB,SAAW,EAAkB,WAAa,EAEhE;QAFsB,IAAtB,CAAA,SAAsB,GAAA,SAAA,CAAW;QAAkB,IAAnD,CAAA,WAAmD,GAAA,WAAA,CAAa;QAJ9D,IAAF,CAAA,QAAU,GACG,CAAA,CAAI,KADjB,GACyB,CAAG;QAA1B,IAAF,CAAA,SAAW,GACG,MADd,GACoB,CAAG;KAEoD;;;;;IAQ3E,UANG,CAAA,KAAA,EAMH;QACI,IAAI,CANC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,OAAA,EAAS,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC;KAO/F;;;;;IAKH,gBATG,CAAA,EAAA,EASH;QACI,IAAI,CATC,QAAC,GAAU,CAAA,KAAE,KAStB,EATiC,EAAA,CAAG,KAAC,IAAQ,EAAA,GAAK,IAAA,GAAO,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC,EAAC,CAAE;KAU5E;;;;;IAKH,iBAZG,CAAA,EAAA,EAYH,EAZ4C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IAiBjE,gBAfG,CAAA,UAAA,EAeH;QACI,IAAI,CAfC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KAgB3F;;AAdI,kBAAP,CAAA,UAAO,GAAoC;IAgB3C,EAfE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAQvB,QAAA,EASI,8FAA8F;gBAClG,IAAI,EAfE;oBAgBJ,UAAU,EAfE,+BAAA;oBAgBZ,SAAS,EAfE,+BAAA;oBAgBX,QAAQ,EAfE,aAAA;iBAgBX;gBACD,SAAS,EAfE,CAAA,oBAAE,CAAoB;aAgBlC,EAfC,EAAG;CAgBJ,CAfC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA;IAkBD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC,AAGF,AAgBC;;ADnGD;;;;;;;AASA,AACA,AAEA,AADO,MAAM,qBAAA,GAAkC;IAE7C,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,0BAAA,CAA2B;IAEzD,KAAC,EADM,IAAA;CAER,CADC;;;;;;AAOF,SAAA,iBAAA,CALC,EAAA,EAAA,KAAA,EAKD;IACE,IAAI,EALC,IAAK,IAAA;QAAM,OAAO,CAAzB,EAAyB,KAAI,CAA7B,CAAkC,CAAE;IAMlC,IAAI,KALC,IAAQ,OAAO,KAAA,KAAU,QAAA;QAAU,KAAA,GAAQ,QAAA,CAAS;IAMzD,OALO,CAKT,EALS,EAAI,CAKb,EAAA,EALe,KAAK,CAKpB,CALyB,CAAE,KAAC,CAAK,CAAC,EAAE,EAAA,CAAG,CAAC;CAMvC;;;;;AAKD,SAAA,UAAA,CARC,WAAA,EAQD;IACE,OARO,WAAA,CAAY,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC;CASlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgED,AAAA,MAAA,0BAAA,CAAA;;;;;IA4BA,WAAA,CAdsB,SAAW,EAAkB,WAAa,EAchE;QAdsB,IAAtB,CAAA,SAAsB,GAAA,SAAA,CAAW;QAAkB,IAAnD,CAAA,WAAmD,GAAA,WAAA,CAAa;;;;QAThE,IAAA,CAAA,UARG,GAAA,IAAA,GAAA,EAAA,CAAA;;;;QAYH,IAAA,CAAA,UAVG,GAAA,CAAA,CAAA;QAYD,IAAF,CAAA,QAAU,GAVG,CAAA,CAAI,KAUjB,GAVyB,CAAG;QAW1B,IAAF,CAAA,SAAW,GAVG,MAUd,GAVoB,CAAG;QAUb,IAAV,CAAA,YAAU,GAA8CH,eAAA,CAAe;KAEI;;;;;IAG3E,IAZG,WAAA,CAAA,EAAA,EAYH;QACI,IAAI,OAZO,EAAA,KAAO,UAAA,EAAY;YAa5B,MAZM,IAAI,KAAA,CAAM,CAYtB,6CAAA,EAZuB,IAAgD,CAAI,SAAC,CAAS,EAAC,CAAE,CAYxF,CAZyF,CAAE,CAAC;SAavF;QACD,IAAI,CAZC,YAAC,GAAc,EAAA,CAAG;KAaxB;;;;;IAYH,UAlBG,CAAA,KAAA,EAkBH;QACI,IAAI,CAlBC,KAAC,GAAO,KAAA,CAAM;QAmBnB,uBAlBM,EAAA,GAAkB,IAAA,CAAK,YAAC,CAAY,KAAC,CAAK,CAAC;QAmBjD,IAAI,EAlBC,IAAK,IAAA,EAAM;YAmBd,IAAI,CAlBC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,eAAA,EAAiB,CAAA,CAAE,CAAC,CAAC;SAmBxF;QACD,uBAlBM,WAAA,GAAc,iBAAA,CAAkB,EAAC,EAAG,KAAA,CAAM,CAAC;QAmBjD,IAAI,CAlBC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,OAAA,EAAS,WAAA,CAAY,CAAC;KAmBzF;;;;;IAKH,gBArBG,CAAA,EAAA,EAqBH;QACI,IAAI,CArBC,QAAC,GAAU,CAAA,WAAc,KAqBlC;YACM,IAAI,CArBC,KAAC,GAAO,WAAA,CAAY;YAsBzB,EAAE,CArBC,IAAC,CAAI,eAAC,CAAe,WAAC,CAAW,CAAC,CAAC;SAsBvC,CArBC;KAsBH;;;;;IAKH,iBAzBG,CAAA,EAAA,EAyBH,EAzB2C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IA8BhE,gBA5BG,CAAA,UAAA,EA4BH;QACI,IAAI,CA5BC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KA6B3F;;;;;IAKH,eA9BG,GA8BH,EA9B8B,OAAO,CAAA,IAAE,CAAI,UAAC,EAAU,EAAG,QAAC,EAAQ,CAAE,EAAC;;;;;;IAoCrE,YAjCG,CAAA,KAAA,EAiCH;QACI,KAAK,uBAjCM,EAAA,IAAM,KAAA,CAAM,IAAC,CAAI,IAAC,CAAI,UAAC,CAAU,IAAC,EAAI,CAAE,EAAE;YAkCnD,IAAI,IAjCC,CAAI,YAAC,CAAY,IAAC,CAAI,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,EAAE,KAAA,CAAM;gBAAE,OAAO,EAAA,CAAG;SAkClE;QACD,OAjCO,IAAA,CAAK;KAkCb;;;;;;IAMH,eApCG,CAAA,WAAA,EAoCH;QACI,uBApCM,EAAA,GAAa,UAAA,CAAW,WAAC,CAAW,CAAC;QAqC3C,OApCO,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,GAAG,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,GAAG,WAAA,CAAY;KAqCxE;;AAnCI,0BAAP,CAAA,UAAO,GAAoC;IAqC3C,EApCE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAqCvB,QAAQ,EACJ,6GAA6G;gBACjH,IAAI,EApCE,EAAA,UAAE,EAAW,+BAAA,EAAiC,QAAA,EAAU,aAAA,EAAc;gBAqC5E,SAAS,EApCE,CAAA,qBAAE,CAAqB;aAqCnC,EApCC,EAAG;CAqCJ,CApCC;;;;AAED,0BAAD,CAAA,cAAC,GAAA,MAAA;IAuCD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC;AArCK,0BAAP,CAAA,cAAO,GAAyD;IAuChE,aAAa,EAtCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAuC/B,CAtCC;AAyCF,AAkCA;;;;;;;;;AASA,AAAA,MAAA,cAAA,CAAA;;;;;;IAOA,WAAA,CA1Ec,QAAU,EAAoB,SAAW,EACvC,OAAS,EAyEzB;QA1Ec,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QAAoB,IAA5C,CAAA,SAA4C,GAAA,SAAA,CAAW;QACvC,IAAhB,CAAA,OAAgB,GAAA,OAAA,CAAS;QA6ErB,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;KAC5D;;;;;IAKH,IA9EG,OAAA,CAAA,KAAA,EA8EH;QACI,IAAI,IA9EC,CAAI,OAAC,IAAU,IAAA;YAAM,OAAA;QA+E1B,IAAI,CA9EC,OAAC,CAAO,UAAC,CAAU,GAAC,CAAG,IAAC,CAAI,EAAC,EAAG,KAAA,CAAM,CAAC;QA+E5C,IAAI,CA9EC,gBAAC,CAAgB,iBAAC,CAAiB,IAAC,CAAI,EAAC,EAAG,KAAA,CAAM,CAAC,CAAC;QA+EzD,IAAI,CA9EC,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;KA+E7C;;;;;IAKH,IAhFG,KAAA,CAAA,KAAA,EAgFH;QACI,IAAI,CAhFC,gBAAC,CAAgB,KAAC,CAAK,CAAC;QAiF7B,IAAI,IAhFC,CAAI,OAAC;YAAQ,IAAA,CAAK,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;KAiF/D;;;;;;IAMH,gBAnFG,CAAA,KAAA,EAmFH;QACI,IAAI,CAnFC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,QAAC,CAAQ,aAAC,EAAc,OAAA,EAAS,KAAA,CAAM,CAAC;KAoFhF;;;;IAIH,WArFG,GAqFH;QACI,IAAI,IArFC,CAAI,OAAC,EAAQ;YAsFhB,IAAI,CArFC,OAAC,CAAO,UAAC,CAAU,MAAC,CAAM,IAAC,CAAI,EAAC,CAAE,CAAC;YAsFxC,IAAI,CArFC,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;SAsF7C;KACF;;AApFI,cAAP,CAAA,UAAO,GAAoC;IAsF3C,EArFE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,QAAA,EAAS,EAAC,EAAG;CAsFjD,CArFC;;;;AAED,cAAD,CAAA,cAAC,GAAA,MAAA;IAwFD,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,0BAA0B,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CACrF,CAAC;AAtFK,cAAP,CAAA,cAAO,GAAyD;IAwFhE,SAAS,EAvFE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,SAAE,EAAS,EAAG,EAAE;IAwFjD,OAAO,EAvFE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,OAAE,EAAO,EAAG,EAAE;CAwF5C,CAvFC,AA0FF,AAkBC;;AD7UD;;;;;;;AASA,AACA,AAEA,AADO,MAAM,8BAAA,GAA2C;IAEtD,OAAO,EADE,iBAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,kCAAA,CAAmC;IAEjE,KAAC,EADM,IAAA;CAER,CADC;;;;;;AAOF,SAAAC,mBAAA,CALC,EAAA,EAAA,KAAA,EAKD;IACE,IAAI,EALC,IAAK,IAAA;QAAM,OAAO,CAAzB,EAAyB,KAAI,CAA7B,CAAkC,CAAE;IAMlC,IAAI,OALO,KAAA,KAAU,QAAA;QAAU,KAAA,GAAQ,CAAzC,CAAA,EAAyC,KAAK,CAA9C,CAAA,CAAmD,CAAG;IAMpD,IAAI,KALC,IAAQ,OAAO,KAAA,KAAU,QAAA;QAAU,KAAA,GAAQ,QAAA,CAAS;IAMzD,OALO,CAKT,EALS,EAAI,CAKb,EAAA,EALe,KAAK,CAKpB,CALyB,CAAE,KAAC,CAAK,CAAC,EAAE,EAAA,CAAG,CAAC;CAMvC;;;;;AAKD,SAAAC,YAAA,CARC,WAAA,EAQD;IACE,OARO,WAAA,CAAY,KAAC,CAAK,GAAC,CAAG,CAAC,CAAC,CAAC,CAAC;CASlC;AAOD,AAcA,AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,AAAA,MAAA,kCAAA,CAAA;;;;;IA4BA,WAAA,CA3BsB,SAAW,EAAkB,WAAa,EA2BhE;QA3BsB,IAAtB,CAAA,SAAsB,GAAA,SAAA,CAAW;QAAkB,IAAnD,CAAA,WAAmD,GAAA,WAAA,CAAa;;;;QAIhE,IAAA,CAAA,UArBG,GAAA,IAAA,GAAA,EAAA,CAAA;;;;QAyBH,IAAA,CAAA,UAvBG,GAAA,CAAA,CAAA;QAyBD,IAAF,CAAA,QAAU,GAvBG,CAAA,CAAI,KAuBjB,GAvByB,CAAG;QAwB1B,IAAF,CAAA,SAAW,GAvBG,MAuBd,GAvBoB,CAAG;QAUb,IAAV,CAAA,YAAU,GAA8CF,eAAA,CAAe;KAEI;;;;;IAgB3E,IAzBG,WAAA,CAAA,EAAA,EAyBH;QACI,IAAI,OAzBO,EAAA,KAAO,UAAA,EAAY;YA0B5B,MAzBM,IAAI,KAAA,CAAM,CAyBtB,6CAAA,EAzBuB,IAAgD,CAAI,SAAC,CAAS,EAAC,CAAE,CAyBxF,CAzByF,CAAE,CAAC;SA0BvF;QACD,IAAI,CAzBC,YAAC,GAAc,EAAA,CAAG;KA0BxB;;;;;IAYH,UA/BG,CAAA,KAAA,EA+BH;QACI,IAAI,CA/BC,KAAC,GAAO,KAAA,CAAM;QAgCnB,qBA/BI,yBAAoE,CAAK;QAgC7E,IAAI,KA/BC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;;YAiCxB,uBA/BM,GAAA,GAAM,KAAA,CAAM,GAAC,CAAG,CAAC,CAAC,KAAK,IAAA,CAAK,YAAC,CAAY,CAAC,CAAC,CAAC,CAAC;YAgCnD,yBAAyB,GA/BG,CAAA,GAAE,EAAI,CAAA,KA+BxC,EA/BgD,GAAA,CAAI,YAAC,CAAY,GAAC,CAAG,OAAC,CAAO,CAAC,CAAC,QAAC,EAAQ,CAAE,GAAG,CAAA,CAAE,CAAC,CAAC,EAAC,CAAE;SAgC/F;aA/BM;YAgCL,yBAAyB,GA/BG,CAAA,GAAE,EAAI,CAAA,KA+BxC,EA/BgD,GAAA,CAAI,YAAC,CAAY,KAAC,CAAK,CAAC,EAAC,CAAE;SAgCtE;QACD,IAAI,CA/BC,UAAC,CAAU,OAAC,CAAO,yBAAC,CAAyB,CAAC;KAgCpD;;;;;IAKH,gBAlCG,CAAA,EAAA,EAkCH;QACI,IAAI,CAlCC,QAAC,GAAU,CAAA,CAAI,KAkCxB;YACM,uBAlCM,QAAA,GAAuB,EAAA,CAAG;YAmChC,IAAI,CAlCC,CAAC,cAAC,CAAc,iBAAC,CAAiB,EAAE;gBAmCvC,uBAlCM,OAAA,GAA0B,CAAA,CAAE,eAAC,CAAe;gBAmClD,KAAK,qBAlCI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,OAAA,CAAQ,MAAC,EAAO,CAAA,EAAE,EAAG;oBAmCvC,uBAlCM,GAAA,GAAW,OAAA,CAAQ,IAAC,CAAI,CAAC,CAAC,CAAC;oBAmCjC,uBAlCM,GAAA,GAAW,IAAA,CAAK,eAAC,CAAe,GAAC,CAAG,KAAC,CAAK,CAAC;oBAmCjD,QAAQ,CAlCC,IAAC,CAAI,GAAC,CAAG,CAAC;iBAmCpB;aACF;iBAhCI;gBAmCH,uBAlCM,OAAA,IAA0C,CAAC,CAAC,OAAC,CAAA,CAAO;gBAmC1D,KAAK,qBAlCI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,OAAA,CAAQ,MAAC,EAAO,CAAA,EAAE,EAAG;oBAmCvC,uBAlCM,GAAA,GAAkB,OAAA,CAAQ,IAAC,CAAI,CAAC,CAAC,CAAC;oBAmCxC,IAAI,GAlCC,CAAG,QAAC,EAAS;wBAmChB,uBAlCM,GAAA,GAAW,IAAA,CAAK,eAAC,CAAe,GAAC,CAAG,KAAC,CAAK,CAAC;wBAmCjD,QAAQ,CAlCC,IAAC,CAAI,GAAC,CAAG,CAAC;qBAmCpB;iBACF;aACF;YACD,IAAI,CAlCC,KAAC,GAAO,QAAA,CAAS;YAmCtB,EAAE,CAlCC,QAAC,CAAQ,CAAC;SAmCd,CAlCC;KAmCH;;;;;IAKH,iBAtCG,CAAA,EAAA,EAsCH,EAtC2C,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;;IA2ChE,gBAzCG,CAAA,UAAA,EAyCH;QACI,IAAI,CAzCC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,WAAC,CAAW,aAAC,EAAc,UAAA,EAAY,UAAA,CAAW,CAAC;KA0C3F;;;;;;IAMH,eA5CG,CAAA,KAAA,EA4CH;QACI,uBA5CM,EAAA,GAAa,CAAA,IAAE,CAAI,UAAC,EAAU,EAAG,QAAC,EAAQ,CAAE;QA6ClD,IAAI,CA5CC,UAAC,CAAU,GAAC,CAAG,EAAC,EAAG,KAAA,CAAM,CAAC;QA6C/B,OA5CO,EAAA,CAAG;KA6CX;;;;;;IAMH,YA/CG,CAAA,KAAA,EA+CH;QACI,KAAK,uBA/CM,EAAA,IAAM,KAAA,CAAM,IAAC,CAAI,IAAC,CAAI,UAAC,CAAU,IAAC,EAAI,CAAE,EAAE;YAgDnD,IAAI,IA/CC,CAAI,YAAC,kBAAY,EAAA,IAAC,CAAI,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,GAAG,MAAC,EAAO,KAAA,CAAM;gBAAE,OAAO,EAAA,CAAG;SAgD3E;QACD,OA/CO,IAAA,CAAK;KAgDb;;;;;;IAMH,eAlDG,CAAA,WAAA,EAkDH;QACI,uBAlDM,EAAA,GAAaE,YAAA,CAAW,WAAC,CAAW,CAAC;QAmD3C,OAlDO,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,GAAC,EAAE,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,EAAC,CAAE,GAAG,MAAC,GAAQ,WAAA,CAAY;KAmDjF;;AAjDI,kCAAP,CAAA,UAAO,GAAoC;IAmD3C,EAlDE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAmDvB,QAAQ,EACJ,2FAA2F;gBAC/F,IAAI,EAlDE,EAAA,UAAE,EAAW,yBAAA,EAA2B,QAAA,EAAU,aAAA,EAAc;gBAmDtE,SAAS,EAlDE,CAAA,8BAAE,CAA8B;aAmD5C,EAlDC,EAAG;CAmDJ,CAlDC;;;;AAED,kCAAD,CAAA,cAAC,GAAA,MAAA;IAqDD,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC;AAnDK,kCAAP,CAAA,cAAO,GAAyD;IAqDhE,aAAa,EApDE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAqD/B,CApDC;AAuDF,AAkCA;;;;;;;;;;;AAWA,AAAA,MAAA,sBAAA,CAAA;;;;;;IAWA,WAAA,CA1Fc,QAAU,EAAoB,SAAW,EACvC,OAAS,EAyFzB;QA1Fc,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QAAoB,IAA5C,CAAA,SAA4C,GAAA,SAAA,CAAW;QACvC,IAAhB,CAAA,OAAgB,GAAA,OAAA,CAAS;QA6FrB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC9C;KACF;;;;;IAKH,IA9FG,OAAA,CAAA,KAAA,EA8FH;QACI,IAAI,IA9FC,CAAI,OAAC,IAAU,IAAA;YAAM,OAAA;QA+F1B,IAAI,CA9FC,MAAC,GAAQ,KAAA,CAAM;QA+FpB,IAAI,CA9FC,gBAAC,CAAgBD,mBAAC,CAAiB,IAAC,CAAI,EAAC,EAAG,KAAA,CAAM,CAAC,CAAC;QA+FzD,IAAI,CA9FC,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;KA+F7C;;;;;IAKH,IAhGG,KAAA,CAAA,KAAA,EAgGH;QACI,IAAI,IAhGC,CAAI,OAAC,EAAQ;YAiGhB,IAAI,CAhGC,MAAC,GAAQ,KAAA,CAAM;YAiGpB,IAAI,CAhGC,gBAAC,CAAgBA,mBAAC,CAAiB,IAAC,CAAI,EAAC,EAAG,KAAA,CAAM,CAAC,CAAC;YAiGzD,IAAI,CAhGC,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;SAiG7C;aAhGM;YAiGL,IAAI,CAhGC,gBAAC,CAAgB,KAAC,CAAK,CAAC;SAiG9B;KACF;;;;;;IAMH,gBAnGG,CAAA,KAAA,EAmGH;QACI,IAAI,CAnGC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,QAAC,CAAQ,aAAC,EAAc,OAAA,EAAS,KAAA,CAAM,CAAC;KAoGhF;;;;;;IAMH,YAtGG,CAAA,QAAA,EAsGH;QACI,IAAI,CAtGC,SAAC,CAAS,kBAAC,CAAkB,IAAC,CAAI,QAAC,CAAQ,aAAC,EAAc,UAAA,EAAY,QAAA,CAAS,CAAC;KAuGtF;;;;IAIH,WAxGG,GAwGH;QACI,IAAI,IAxGC,CAAI,OAAC,EAAQ;YAyGhB,IAAI,CAxGC,OAAC,CAAO,UAAC,CAAU,MAAC,CAAM,IAAC,CAAI,EAAC,CAAE,CAAC;YAyGxC,IAAI,CAxGC,OAAC,CAAO,UAAC,CAAU,IAAC,CAAI,OAAC,CAAO,KAAC,CAAK,CAAC;SAyG7C;KACF;;AAvGI,sBAAP,CAAA,UAAO,GAAoC;IAyG3C,EAxGE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,QAAA,EAAS,EAAC,EAAG;CAyGjD,CAxGC;;;;AAED,sBAAD,CAAA,cAAC,GAAA,MAAA;IA2GD,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,kCAAkC,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CAC7F,CAAC;AAzGK,sBAAP,CAAA,cAAO,GAAyD;IA2GhE,SAAS,EA1GE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,SAAE,EAAS,EAAG,EAAE;IA2GjD,OAAO,EA1GE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,OAAE,EAAO,EAAG,EAAE;CA2G5C,CA1GC,AA6GF,AAuBC;;AD5XD;;;;;;;AASA,AAEA,AAGA,AAGA,AAEA,AACA,AACA,AACA,AAEA,AACA,AAEA;;;;;AAKA,AAAA,SAAA,WAAA,CAJC,IAAA,EAAA,MAAA,EAID;IACE,OAJO,CAAA,KAAE,MAAG,CAAM,IAAC,EAAA,EAAO,IAAA,CAAK,CAAC;CAKjC;;;;;;AAMD,AAAA,SAAA,YAAA,CARC,OAAA,EAAA,GAAA,EAQD;IACE,IAAI,CARC,OAAC;QAAQ,WAAA,CAAY,GAAC,EAAI,0BAAA,CAA2B,CAAC;IAS3D,IAAI,CARC,GAAC,CAAG,aAAC;QAAc,WAAA,CAAY,GAAC,EAAI,yCAAA,CAA0C,CAAC;IAUpF,OAAO,CARC,SAAC,GAAW,UAAA,CAAW,OAAC,CAAO,oBAAC,OAAC,CAAO,SAAC,IAAY,GAAA,CAAI,SAAC,CAAS,CAAC,CAAC;IAS7E,OAAO,CARC,cAAC,GAAgB,UAAA,CAAW,YAAC,CAAY,oBAAC,OAAC,CAAO,cAAC,IAAiB,GAAA,CAAI,cAAC,CAAc,CAAC,CAAC;IAAA,EASjG,GAAG,CARC,aAAC,GAAe,UAAC,CAAU,OAAC,CAAO,KAAC,CAAK,CAAC;IAAA;;IAW9C,GAAG,CARC,aAAC,GAAe,gBAAC,CAAgB,CAAC,QAAU,KAQlD;QACI,GAAG,CARC,iBAAC,CAAiB,QAAC,CAAQ,CAAC;QAShC,OAAO,CARC,WAAC,EAAW,CAAE;QAStB,OAAO,CARC,QAAC,CAAQ,QAAC,EAAS,EAAA,qBAAE,EAAsB,KAAA,EAAM,CAAC,CAAC;KAS5D,CARC,CAAC;IAAA;;IAWH,GAAG,CARC,aAAC,GAAe,iBAAC,CAAiB,MAAM,OAAA,CAAQ,aAAC,EAAa,CAAE,CAAC;IAUrE,OAAO,CARC,gBAAC,CAAgB,CAAC,QAAU,EAAK,cAAgB,KAQ3D;QARuE;;QAUnE,GAAG,CARC,aAAC,GAAe,UAAC,CAAU,QAAC,CAAQ,CAAC;;QAWzC,IAAI,cARC;YAAe,GAAA,CAAI,iBAAC,CAAiB,QAAC,CAAQ,CAAC;KASrD,CARC,CAAC;IAUH,IAAqB,EAAE,GARlB,CAAG,aAAC,GAAe,gBAAC,EAAiB;QASxC,OAAO,CARC,wBAAC,CASL,CAAC,UARY,KAOrB,mBAPiC,EAAA,EAAE,GAAA,CAAI,aAAC,GAAe,gBAAC,GAAkB,UAAC,CAAU,CAAC,EAAC,CAAE,CAAC;KASvF;;IAGD,GAAG,CARC,cAAC,CAAc,OAAC,CAAO,CAAC,SAAuB,KAQrD;QACI,IAAI,EARY,SAAC,GAAU,yBAAC;YAAyB,EASnD,EARY,SAAC,GAAU,yBAAC,GAA2B,MAAM,OAAA,CAAQ,sBAAC,EAAsB,CAAE,CAAC;KAS9F,CARC,CAAC;IAUH,GAAG,CARC,mBAAC,CAAmB,OAAC,CAAO,CAAC,SAA4B,KAQ/D;QACI,IAAI,EARY,SAAC,GAAU,yBAAC;YAAyB,EASnD,EARY,SAAC,GAAU,yBAAC,GAA2B,MAAM,OAAA,CAAQ,sBAAC,EAAsB,CAAE,CAAC;KAS9F,CARC,CAAC;CASJ;;;;;;AAMD,AAAA,SAAA,cAAA,CAZC,OAAA,EAAA,GAAA,EAYD;IAZqE,EAanE,GAAG,CAZC,aAAC,GAAe,gBAAC,CAAgB,MAAM,eAAA,CAAgB,GAAC,CAAG,CAAC,CAAC;IAAA,EAajE,GAAG,CAZC,aAAC,GAAe,iBAAC,CAAiB,MAAM,eAAA,CAAgB,GAAC,CAAG,CAAC,CAAC;IAclE,GAAG,CAZC,cAAC,CAAc,OAAC,CAAO,CAAC,SAAW,KAYzC;QACI,IAAI,SAZC,CAAS,yBAAC,EAA0B;YAavC,SAAS,CAZC,yBAAC,CAAyB,IAAC,CAAI,CAAC;SAa3C;KACF,CAZC,CAAC;IAcH,GAAG,CAZC,mBAAC,CAAmB,OAAC,CAAO,CAAC,SAAW,KAY9C;QACI,IAAI,SAZC,CAAS,yBAAC,EAA0B;YAavC,SAAS,CAZC,yBAAC,CAAyB,IAAC,CAAI,CAAC;SAa3C;KACF,CAZC,CAAC;IAcH,IAAI,OAZC;QAAQ,OAAA,CAAQ,eAAC,EAAe,CAAE;CAaxC;;;;;;AAMD,AAAA,SAAA,kBAAA,CACI,OAA8B,EAAE,GAA+C,EADnF;IAEE,IAAI,OAhBC,IAAU,IAAA;QAAM,WAAA,CAAY,GAAC,EAAI,0BAAA,CAA2B,CAAC;IAiBlE,OAAO,CAhBC,SAAC,GAAW,UAAA,CAAW,OAAC,CAAO,CAAC,OAAC,CAAO,SAAC,EAAU,GAAA,CAAI,SAAC,CAAS,CAAC,CAAC;IAiB3E,OAAO,CAhBC,cAAC,GAAgB,UAAA,CAAW,YAAC,CAAY,CAAC,OAAC,CAAO,cAAC,EAAe,GAAA,CAAI,cAAC,CAAc,CAAC,CAAC;CAiBhG;;;;;AAKD,SAAA,eAAA,CAnBC,GAAA,EAmBD;IACE,OAnBO,WAAA,CAAY,GAAC,EAAI,wEAAA,CAAyE,CAAC;CAoBnG;;;;;;AAMD,SAAA,WAAA,CAvBC,GAAA,EAAA,OAAA,EAuBD;IACE,qBAvBI,UAAY,CAAO;IAwBvB,IAAqB,EAAE,GAvBlB,CAAG,IAAC,GAAM,MAAC,GAAQ,CAAA,EAAG;QAwBzB,UAAU,GAvBG,CAuBjB,OAAA,mBAvBiB,EAAA,GAAW,CAAG,IAAC,GAAK,IAAC,CAAI,MAAC,CAAM,CAuBjD,CAAA,CAvBkD,CAAG;KAwBlD;SAvBM,IAAA,EAAA,GAAK,CAAG,IAAC,GAAM,CAAC,CAAC,EAAE;QAwBxB,UAAU,GAvBG,CAuBjB,OAAA,EAvBiB,GAAW,CAAG,IAAC,CAuBhC,CAAA,CAvBoC,CAAG;KAwBpC;SAvBM;QAwBL,UAAU,GAvBG,4BAAA,CAA6B;KAwB3C;IACD,MAvBM,IAAI,KAAA,CAAM,CAuBlB,EAvBmB,OAAG,CAuBtB,CAAA,EAvB6B,UAAI,CAuBjC,CAvB2C,CAAE,CAAC;CAwB7C;;;;;AAKD,AAAA,SAAA,iBAAA,CA1BC,UAAA,EA0BD;IACE,OA1BO,UAAA,IAAc,IAAA,GAAO,UAAA,CAAW,OAAC,CAAO,UAAC,CAAU,GAAC,CAAG,kBAAC,CAAkB,CAAC,GAAG,IAAA,CAAK;CA2B3F;;;;;AAKD,AAAA,SAAA,sBAAA,CA7BC,UAAA,EA6BD;IAEE,OA7BO,UAAA,IAAc,IAAA,GAAO,UAAA,CAAW,YAAC,CAAY,UAAC,CAAU,GAAC,CAAG,uBAAC,CAAuB,CAAC;QA8BhE,IAAI,CA7BC;CA8BlC;;;;;;AAMD,AAAA,SAAA,iBAAA,CAjCC,OAAA,EAAA,SAAA,EAiCD;IACE,IAAI,CAjCC,OAAC,CAAO,cAAC,CAAc,OAAC,CAAO;QAAE,OAAO,KAAA,CAAM;IAkCnD,uBAjCM,MAAA,GAAS,OAAA,CAAQ,OAAC,CAAO,CAAC;IAmChC,IAAI,MAjCC,CAAM,aAAC,EAAa;QAAG,OAAO,IAAA,CAAK;IAkCxC,OAjCO,CAAAD,eAAE,CAAc,SAAC,EAAU,MAAA,CAAO,YAAC,CAAY,CAAC;CAkCxD;AAED,MAjCM,iBAAA,GAAoB;IAkCxB,4BAA4B;IAC5B,kBAAkB;IAClB,mBAAmB;IACnB,0BAA0B;IAC1B,kCAAkC;IAClC,yBAAyB;CAC1B,CAjCC;;;;;AAsCF,AAAA,SAAA,iBAAA,CApCC,aAAA,EAoCD;IACE,OApCO,iBAAA,CAAkB,IAAC,CAAI,CAAC,IAAI,aAAA,CAAc,WAAC,KAAe,CAAA,CAAE,CAAC;CAqCrE;;;;;;AAMD,AAAA,SAAA,mBAAA,CACI,GAAc,EAAE,cAAsC,EAD1D;IAEE,IAAI,CAvCC,cAAC;QAAe,OAAO,IAAA,CAAK;IAyCjC,qBAvCI,eAAA,GAAkD,SAAA,CAAU;IAwChE,qBAvCI,eAAA,GAAkD,SAAA,CAAU;IAwChE,qBAvCI,cAAA,GAAiD,SAAA,CAAU;IAwC/D,cAAc,CAvCC,OAAC,CAAO,CAAC,CAAG,KAuC7B;QACI,IAAI,CAvCC,CAAC,WAAC,KAAe,oBAAA,EAAsB;YAwC1C,eAAe,GAvCG,CAAA,CAAE;SAyCrB;aAvCM,IAAA,iBAAK,CAAiB,CAAC,CAAC,EAAE;YAwC/B,IAAI,eAvCC;gBAwCH,WAAW,CAvCC,GAAC,EAAI,iEAAA,CAAkE,CAAC;YAwCtF,eAAe,GAvCG,CAAA,CAAE;SAyCrB;aAvCM;YAwCL,IAAI,cAvCC;gBAwCH,WAAW,CAvCC,GAAC,EAAI,+DAAA,CAAgE,CAAC;YAwCpF,cAAc,GAvCG,CAAA,CAAE;SAwCpB;KACF,CAvCC,CAAC;IAyCH,IAAI,cAvCC;QAAe,OAAO,cAAA,CAAe;IAwC1C,IAAI,eAvCC;QAAgB,OAAO,eAAA,CAAgB;IAwC5C,IAAI,eAvCC;QAAgB,OAAO,eAAA,CAAgB;IAyC5C,WAAW,CAvCC,GAAC,EAAI,+CAAA,CAAgD,CAAC;IAwClE,OAvCO,IAAA,CAAK;CAwCb;;ADzND;;;;;;;AAaA,AAEA,AAEA;;;;;AAKA,AAAA,MAAA,0BAEC,SAAA,gBAAA,CAFD;;;;IAgBA,QAJG,GAIH;QACI,IAAI,CAJC,gBAAC,EAAgB,CAAE;QAAA,EAKxB,IAAI,CAJC,aAAC,GAAe,YAAC,CAAY,IAAC,CAAI,CAAC;KAKzC;;;;IAIH,WANG,GAMH;QACI,IAAI,IANC,CAAI,aAAC,EAAc;YAOtB,IAAI,CANC,aAAC,CAAa,eAAC,CAAe,IAAC,CAAI,CAAC;SAO1C;KACF;;;;;IAKH,IANG,OAAA,GAMH,EAN6B,OAAA,EAAO,IAAA,CAAK,aAAC,GAAe,YAAC,CAAY,IAAC,CAAI,CAAC,EAAC;;;;;IAW7E,IANG,IAAA,GAMH,EANyB,OAAO,WAAA,CAAY,IAAC,CAAI,IAAC,EAAK,IAAA,CAAK,OAAC,CAAO,CAAC,EAAC;;;;;IAWtE,IANG,aAAA,GAMH,EANmC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,aAAC,GAAe,IAAA,CAAK,EAAC;;;;IAU5F,IARG,SAAA,GAQH,EARsC,OAAO,iBAAA,CAAkB,IAAC,CAAI,WAAC,CAAW,CAAC,EAAC;;;;IAYlF,IAVG,cAAA,GAUH;QACI,OAVO,sBAAA,CAAuB,IAAC,CAAI,gBAAC,CAAgB,CAAC;KAWtD;;;;;IAKH,gBAZG,GAYH,GAZ2B;CAa1B,AAED,AAgBC;;ADlGD;;;;;;;AASA,AAGA,AACA,AACA,AAAA,MAAA,qBAAA,CAAA;;;;IAKA,WAAA,CAFG,EAAe,EAElB,EAF2C,IAAA,CAAA,GAAA,GAAA,EAAA,CAAA,EAAA;;;;IAM3C,IAJG,gBAAA,GAIH,EAJoC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,SAAC,GAAW,KAAA,CAAM,EAAC;;;;IAQlG,IAPG,cAAA,GAOH,EAPkC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,OAAC,GAAS,KAAA,CAAM,EAAC;;;;IAW9F,IAVG,eAAA,GAUH,EAVmC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,QAAC,GAAU,KAAA,CAAM,EAAC;;;;IAchG,IAbG,YAAA,GAaH,EAbgC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,KAAC,GAAO,KAAA,CAAM,EAAC;;;;IAiB1F,IAhBG,YAAA,GAgBH,EAhBgC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,KAAC,GAAO,KAAA,CAAM,EAAC;;;;IAoB1F,IAnBG,cAAA,GAmBH,EAnBkC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,OAAC,GAAS,KAAA,CAAM,EAAC;;;;IAuB9F,IAtBG,cAAA,GAsBH,EAtBkC,OAAO,IAAA,CAAK,GAAC,CAAG,OAAC,GAAS,IAAA,CAAK,GAAC,CAAG,OAAC,CAAO,OAAC,GAAS,KAAA,CAAM,EAAC;CAuB7F;AAED,AAMA,AA5BO,MAAM,mBAAA,GAAsB;IA6BjC,sBAAsB,EA5BE,kBAAA;IA6BxB,oBAAoB,EA5BE,gBAAA;IA6BtB,qBAAqB,EA5BE,iBAAA;IA6BvB,kBAAkB,EA5BE,cAAA;IA6BpB,kBAAkB,EA5BE,cAAA;IA6BpB,oBAAoB,EA5BE,gBAAA;IA6BtB,oBAAoB,EA5BE,gBAAA;CA6BvB,CA5BC;;;;;;;AAmCF,AAAA,MAAA,eA1BC,SAAA,qBAAA,CA0BD;;;;IAIA,WAAA,CA7Be,EAAI,EA6BnB,EA7B6B,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA;;AACtB,eAAP,CAAA,UAAO,GAAoC;IA8B3C,EA7BE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,2CAAA,EAA6C,IAAA,EAAM,mBAAA,EAAoB,EAAC,EAAG;CA8B/G,CA7BC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IAgCD,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CAChD,CAAC;AAGF,AAUA;;;;;;AAMA,AAAA,MAAA,oBAxCC,SAAA,qBAAA,CAwCD;;;;IAIA,WAAA,CA3Ce,EAAI,EA2CnB,EA3CoC,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA;;AAC7B,oBAAP,CAAA,UAAO,GAAoC;IA4C3C,EA3CE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA4CvB,QAAQ,EACJ,0FAA0F;gBAC9F,IAAI,EA3CE,mBAAA;aA4CP,EA3CC,EAAG;CA4CJ,CA3CC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IA8CD,EAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;CACvD,CAAC,AAGF,AAQC;;ADnID;;;;;;;AASA,AAEA,AAEA,AACA;;;AAGA,AAAC,MAAA,KAAA,GAAA,OAAA,CAAA;;;;AAID,AACC,MAAA,OAAA,GAAA,SAAA,CAAA;;;;;AAID,AAEC,MAAA,OAAA,GAAA,SAAA,CAAA;;;;;AAGD,AAGC,MAAA,QAAA,GAAA,UAAA,CAAA;;;;;;;AAID,SAAA,KAAA,CAFC,OAAA,EAAA,IAAA,EAAA,SAAA,EAED;IACE,IAAI,IAFC,IAAO,IAAA;QAAM,OAAO,IAAA,CAAK;IAI9B,IAAI,EAFE,IAAC,YAAe,KAAA,CAAM,EAAE;QAG5B,IAAI,GAFG,EAAS,IAAC,GAAK,KAAC,CAAK,SAAC,CAAS,CAAC;KAGxC;IACD,IAAI,IAFC,YAAe,KAAA,KAAS,IAAE,CAAI,MAAC,KAAU,CAAA,CAAE;QAAE,OAAO,IAAA,CAAK;IAI9D,OAFO,EAAuB,IAAC,GAAK,MAAC,CAAM,CAAC,CAAG,EAAiB,IAAA,KAElE;QACI,IAAI,CAFC,YAAY,SAAA,EAAW;YAG1B,OAFO,CAAA,CAAE,QAAC,CAAQ,IAAC,CAAI,IAAI,IAAA,CAAK;SAGjC;QAED,IAAI,CAFC,YAAY,SAAA,EAAW;YAG1B,OAFO,CAAA,CAAE,EAAC,mBAAU,IAAC,EAAI,IAAI,IAAA,CAAK;SAGnC;QAED,OAFO,IAAA,CAAK;KAGb,EAFE,OAAA,CAAQ,CAAC;CAGb;;;;;AAKD,SAAA,iBAAA,CALC,SAAA,EAKD;IACE,OALO,KAAA,CAAM,OAAC,CAAO,SAAC,CAAS,GAAG,iBAAA,CAAkB,SAAC,CAAS,GAAG,SAAA,IAAa,IAAA,CAAK;CAMpF;;;;;AAKD,SAAA,sBAAA,CARC,cAAA,EAQD;IAEE,OARO,KAAA,CAAM,OAAC,CAAO,cAAC,CAAc,GAAG,sBAAA,CAAuB,cAAC,CAAc;QAStC,cAAc,IARI,IAAA,CAAK;CAS/D;;;;;;;;;;;;;AAaD,AAAA,MAAA,eAAA,CAAA;;;;;IAqBA,WAAA,CAdqB,SAAwB,EAAY,cAAkC,EAc3F;QAdqB,IAArB,CAAA,SAAqB,GAAA,SAAA,CAAwB;QAAY,IAAzD,CAAA,cAAyD,GAAA,cAAA,CAAkC;;;;QAC3F,IAAA,CAAA,mBAZG,GAAA,MAYH,GAZG,CAAA;QAMO,IAAV,CAAA,SAAU,GAAqB,IAAA,CAAK;QAC1B,IAAV,CAAA,QAAU,GAAoB,KAAA,CAAM;;;;QAmkBpC,IAAA,CAAA,iBA7FG,GAAA,EAAA,CAAA;KAle4F;;;;;IAoB/F,IAfG,KAAA,GAeH,EAfqB,OAAO,IAAA,CAAK,MAAC,CAAM,EAAC;;;;;IAoBzC,IAfG,MAAA,GAeH,EAfsC,OAAO,IAAA,CAAK,OAAC,CAAO,EAAC;;;;;;;;;;;;;;IA6B3D,IAfG,MAAA,GAeH,EAfyB,OAAO,IAAA,CAAK,OAAC,CAAO,EAAC;;;;;;;;IAuB9C,IAfG,KAAA,GAeH,EAfyB,OAAO,IAAA,CAAK,OAAC,KAAW,KAAA,CAAM,EAAC;;;;;;;;IAuBxD,IAfG,OAAA,GAeH,EAf2B,OAAO,IAAA,CAAK,OAAC,KAAW,OAAA,CAAQ,EAAC;;;;;;;;IAuB5D,IAfG,OAAA,GAeH,EAf2B,OAAO,IAAA,CAAK,OAAC,IAAU,OAAA,CAAQ,EAAC;;;;;;;;;IAwB3D,IAfG,QAAA,GAeH,EAf4B,OAAO,IAAA,CAAK,OAAC,KAAW,QAAA,CAAS,EAAC;;;;;;;;IAuB9D,IAfG,OAAA,GAeH,EAf2B,OAAO,IAAA,CAAK,OAAC,KAAW,QAAA,CAAS,EAAC;;;;;;IAqB7D,IAfG,MAAA,GAeH,EAfwC,OAAO,IAAA,CAAK,OAAC,CAAO,EAAC;;;;;;;;;IAwB7D,IAfG,QAAA,GAeH,EAf4B,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;;;;;IAwBnD,IAfG,KAAA,GAeH,EAfyB,OAAO,CAAA,IAAE,CAAI,QAAC,CAAQ,EAAC;;;;;;IAqBhD,IAfG,OAAA,GAeH,EAf2B,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;;;IAqBjD,IAfG,SAAA,GAeH,EAf6B,OAAO,CAAA,IAAE,CAAI,QAAC,CAAQ,EAAC;;;;;;IAqBpD,IAfG,YAAA,GAeH,EAfwC,OAAO,IAAA,CAAK,aAAC,CAAa,EAAC;;;;;;IAqBnE,IAfG,aAAA,GAeH,EAfyC,OAAO,IAAA,CAAK,cAAC,CAAc,EAAC;;;;;;;IAsBrE,aAhBG,CAAA,YAAA,EAgBH;QACI,IAAI,CAhBC,SAAC,GAAW,iBAAA,CAAkB,YAAC,CAAY,CAAC;KAiBlD;;;;;;;IAOH,kBAjBG,CAAA,YAAA,EAiBH;QACI,IAAI,CAjBC,cAAC,GAAgB,sBAAA,CAAuB,YAAC,CAAY,CAAC;KAkB5D;;;;;IAKH,eAjBG,GAiBH,EAjB4B,IAAA,CAAK,SAAC,GAAW,IAAA,CAAK,EAAC;;;;;IAsBnD,oBAjBG,GAiBH,EAjBiC,IAAA,CAAK,cAAC,GAAgB,IAAA,CAAK,EAAC;;;;;;;;;IA0B7D,aAlBG,CAAA,IAkBH,GAlBG,EAAA,EAkBH;QACI,IAAI,CAlBC,QAAC,GAAU,IAAA,CAAK;QAoBrB,IAAI,IAlBC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAmBlC,IAAI,CAlBC,OAAC,CAAO,aAAC,CAAa,IAAC,CAAI,CAAC;SAmBlC;KACF;;;;;;;;;;IAUH,eAnBG,CAAA,IAmBH,GAnBG,EAAA,EAmBH;QACI,IAAI,CAnBC,QAAC,GAAU,KAAA,CAAM;QAqBtB,IAAI,CAnBC,aAAC,CAoBF,CAAC,OAnBS,KAkBlB,EAlBwC,OAAA,CAAQ,eAAC,CAAe,EAAC,QAAC,EAAS,IAAA,EAAK,CAAC,CAAC,EAAC,CAAE,CAAC;QAqBlF,IAAI,IAnBC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAoBlC,IAAI,CAnBC,OAAC,CAAO,cAAC,CAAc,IAAC,CAAI,CAAC;SAoBnC;KACF;;;;;;;;;IASH,WApBG,CAAA,IAoBH,GApBG,EAAA,EAoBH;QACI,IAAI,CApBC,SAAC,GAAW,KAAA,CAAM;QAsBvB,IAAI,IApBC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAqBlC,IAAI,CApBC,OAAC,CAAO,WAAC,CAAW,IAAC,CAAI,CAAC;SAqBhC;KACF;;;;;;;;;;IAUH,cArBG,CAAA,IAqBH,GArBG,EAAA,EAqBH;QACI,IAAI,CArBC,SAAC,GAAW,IAAA,CAAK;QAuBtB,IAAI,CArBC,aAAC,CAAa,CAAC,OAAS,KAqBjC,EArBuD,OAAA,CAAQ,cAAC,CAAc,EAAC,QAAC,EAAS,IAAA,EAAK,CAAC,CAAC,EAAC,CAAE,CAAC;QAuBhG,IAAI,IArBC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAsBlC,IAAI,CArBC,OAAC,CAAO,eAAC,CAAe,IAAC,CAAI,CAAC;SAsBpC;KACF;;;;;;IAMH,aAtBG,CAAA,IAsBH,GAtBG,EAAA,EAsBH;QACI,IAAI,CAtBC,OAAC,GAAS,OAAA,CAAQ;QAwBvB,IAAI,IAtBC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAuBlC,IAAI,CAtBC,OAAC,CAAO,aAAC,CAAa,IAAC,CAAI,CAAC;SAuBlC;KACF;;;;;;;;;IASH,OAvBG,CAAA,IAuBH,GAvBG,EAAA,EAuBH;QACI,IAAI,CAvBC,OAAC,GAAS,QAAA,CAAS;QAwBxB,IAAI,CAvBC,OAAC,GAAS,IAAA,CAAK;QAwBpB,IAAI,CAvBC,aAAC,CAAa,CAAC,OAAS,KAuBjC,EAvBuD,OAAA,CAAQ,OAAC,CAAO,EAAC,QAAC,EAAS,IAAA,EAAK,CAAC,CAAC,EAAC,CAAE,CAAC;QAwBzF,IAAI,CAvBC,YAAC,EAAY,CAAE;QAyBpB,IAAI,IAvBC,CAAI,SAAC,KAAa,KAAA,EAAO;YAwB5B,IAAI,CAvBC,aAAC,CAAa,IAAC,CAAI,IAAC,CAAI,MAAC,CAAM,CAAC;YAwBrC,IAAI,CAvBC,cAAC,CAAc,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;SAwBxC;QAED,IAAI,CAvBC,gBAAC,CAAgB,CAAC,CAAC,IAAC,CAAI,QAAC,CAAQ,CAAC;QAwBvC,IAAI,CAvBC,iBAAC,CAAiB,OAAC,CAAO,CAAC,QAAC,KAAY,QAAA,CAAS,IAAC,CAAI,CAAC,CAAC;KAwB9D;;;;;;;;;;IAUH,MAxBG,CAAA,IAwBH,GAxBG,EAAA,EAwBH;QACI,IAAI,CAxBC,OAAC,GAAS,KAAA,CAAM;QAyBrB,IAAI,CAxBC,aAAC,CAAa,CAAC,OAAS,KAwBjC,EAxBuD,OAAA,CAAQ,MAAC,CAAM,EAAC,QAAC,EAAS,IAAA,EAAK,CAAC,CAAC,EAAC,CAAE,CAAC;QAyBxF,IAAI,CAxBC,sBAAC,CAAsB,EAAC,QAAC,EAAS,IAAA,EAAM,SAAA,EAAW,IAAA,CAAK,SAAC,EAAS,CAAC,CAAC;QA0BzE,IAAI,CAxBC,gBAAC,CAAgB,CAAC,CAAC,IAAC,CAAI,QAAC,CAAQ,CAAC;QAyBvC,IAAI,CAxBC,iBAAC,CAAiB,OAAC,CAAO,CAAC,QAAC,KAAY,QAAA,CAAS,KAAC,CAAK,CAAC,CAAC;KAyB/D;;;;;IAtBA,gBAAA,CAAA,QAAA,EAAH;QA4BI,IAAI,IA3BC,CAAI,OAAC,IAAU,CAAA,QAAE,EAAS;YA4B7B,IAAI,CA3BC,OAAC,CAAO,sBAAC,EAAsB,CAAE;YA4BtC,IAAI,CA3BC,OAAC,CAAO,eAAC,EAAe,CAAE;YA4B/B,IAAI,CA3BC,OAAC,CAAO,cAAC,EAAc,CAAE;SA4B/B;KACF;;;;;IAKH,SA9BG,CAAA,MAAA,EA8BH,EA9BiD,IAAA,CAAK,OAAC,GAAS,MAAA,CAAO,EAAC;;;;;;;;IAsCxE,QAjCY,CAAA,KAAA,EAAA,OAAA,EAiCZ,GAjCY;;;;;;;;IAyCZ,UApCY,CAAA,KAAA,EAAA,OAAA,EAoCZ,GApCY;;;;;;;;IA4CZ,KAvCY,CAAA,KAAA,EAAA,OAAA,EAuCZ,GAvCY;;;;;;;;IA+CZ,sBAxCG,CAAA,IAwCH,GAxCG,EAAA,EAwCH;QACI,IAAI,CAxCC,iBAAC,EAAiB,CAAE;QAyCzB,IAAI,CAxCC,YAAC,EAAY,CAAE;QA0CpB,IAAI,IAxCC,CAAI,OAAC,EAAQ;YAyChB,IAAI,CAxCC,2BAAC,EAA2B,CAAE;YAyCnC,IAAI,CAxCC,OAAC,GAAS,IAAA,CAAK,aAAC,EAAa,CAAE;YAyCpC,IAAI,CAxCC,OAAC,GAAS,IAAA,CAAK,gBAAC,EAAgB,CAAE;YA0CvC,IAAI,IAxCC,CAAI,OAAC,KAAW,KAAA,IAAS,IAAA,CAAK,OAAC,KAAW,OAAA,EAAS;gBAyCtD,IAAI,CAxCC,kBAAC,CAAkB,IAAC,CAAI,SAAC,CAAS,CAAC;aAyCzC;SACF;QAED,IAAI,IAxCC,CAAI,SAAC,KAAa,KAAA,EAAO;YAyC5B,IAAI,CAxCC,aAAC,CAAa,IAAC,CAAI,IAAC,CAAI,MAAC,CAAM,CAAC;YAyCrC,IAAI,CAxCC,cAAC,CAAc,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;SAyCxC;QAED,IAAI,IAxCC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YAyClC,IAAI,CAxCC,OAAC,CAAO,sBAAC,CAAsB,IAAC,CAAI,CAAC;SAyC3C;KACF;;;;;;IAMH,mBA3CG,CAAA,IA2CH,GA3CG,EAAA,SAAA,EAAA,IAAA,EAAA,EA2CH;QACI,IAAI,CA3CC,aAAC,CAAa,CAAC,IAAM,KAAoB,IAAA,CAAK,mBAAC,CAAmB,IAAC,CAAI,CAAC,CAAC;QA4C9E,IAAI,CA3CC,sBAAC,CAAsB,EAAC,QAAC,EAAS,IAAA,EAAM,SAAA,EAAW,IAAA,CAAK,SAAC,EAAS,CAAC,CAAC;KA4C1E;;;;IAzCA,iBAAA,GAAH,EAAgC,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,oBAAC,EAAoB,GAAI,QAAA,GAAW,KAAA,CAAM,EAAC;;;;IAE5F,aAAA,GAAH;QAgDI,OA/CO,IAAA,CAAK,SAAC,GAAW,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,GAAG,IAAA,CAAK;KAgDrD;;;;;IA7CA,kBAAA,CAAA,SAAA,EAAH;QAmDI,IAAI,IAlDC,CAAI,cAAC,EAAe;YAmDvB,IAAI,CAlDC,OAAC,GAAS,OAAA,CAAQ;YAmDvB,uBAlDM,GAAA,GAAM,YAAA,CAAa,IAAC,CAAI,cAAC,CAAc,IAAC,CAAI,CAAC,CAAC;YAmDpD,IAAI,CAlDC,4BAAC;gBAmDF,GAAG,CAlDC,SAAC,CAAS,CAAC,MAA2B,KAAS,IAAA,CAAK,SAAC,CAAS,MAAC,EAAO,EAAA,SAAE,EAAS,CAAC,CAAC,CAAC;SAmD7F;KACF;;;;IAhDA,2BAAA,GAAH;QAqDI,IAAI,IApDC,CAAI,4BAAC,EAA6B;YAqDrC,IAAI,CApDC,4BAAC,CAA4B,WAAC,EAAW,CAAE;SAqDjD;KACF;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BH,SAtDG,CAAA,MAAA,EAAA,IAsDH,GAtDG,EAAA,EAsDH;QACI,IAAI,CAtDC,OAAC,GAAS,MAAA,CAAO;QAuDtB,IAAI,CAtDC,qBAAC,CAAqB,IAAC,CAAI,SAAC,KAAa,KAAA,CAAM,CAAC;KAuDtD;;;;;;;;;;;;;;;;IAgBH,GAvDG,CAAA,IAAA,EAuDH,EAvDiE,OAAO,KAAA,CAAM,IAAC,EAAK,IAAA,EAAM,GAAA,CAAI,CAAC,EAAC;;;;;;;;;;IAiEhG,QAzDG,CAAA,SAAA,EAAA,IAAA,EAyDH;QACI,uBAzDM,OAAA,GAAU,IAAA,GAAO,IAAA,CAAK,GAAC,CAAG,IAAC,CAAI,GAAG,IAAA,CAAK;QA0D7C,OAzDO,OAAA,IAAW,OAAA,CAAQ,OAAC,GAAS,OAAA,CAAQ,OAAC,CAAO,SAAC,CAAS,GAAG,IAAA,CAAK;KA0DvE;;;;;;;;;;IAUH,QA3DG,CAAA,SAAA,EAAA,IAAA,EA2DH,EA3D0D,OAAO,CAAA,CAAE,IAAC,CAAI,QAAC,CAAQ,SAAC,EAAU,IAAA,CAAK,CAAC,EAAC;;;;;IAgEnG,IA3DG,IAAA,GA2DH;QACI,qBA3DI,CAAA,GAAqB,IAAA,CAAK;QA6D9B,OAAO,CA3DC,CAAC,OAAC,EAAQ;YA4DhB,CAAC,GA3DG,CAAA,CAAE,OAAC,CAAO;SA4Df;QAED,OA3DO,CAAA,CAAE;KA4DV;;;;;;IAMH,qBA9DG,CAAA,SAAA,EA8DH;QACI,IAAI,CA9DC,OAAC,GAAS,IAAA,CAAK,gBAAC,EAAgB,CAAE;QAgEvC,IAAI,SA9DC,EAAU;YA+Db,IAAI,CA9DC,cAAC,CAAc,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;SA+DxC;QAED,IAAI,IA9DC,CAAI,OAAC,EAAQ;YA+DhB,IAAI,CA9DC,OAAC,CAAO,qBAAC,CAAqB,SAAC,CAAS,CAAC;SA+D/C;KACF;;;;;IAKH,gBAhEG,GAgEH;QACI,IAAI,CAhEC,aAAC,GAAe,IAAI,YAAA,EAAa,CAAE;QAiExC,IAAI,CAhEC,cAAC,GAAgB,IAAI,YAAA,EAAa,CAAE;KAiE1C;;;;IA7DA,gBAAA,GAAH;QAkEI,IAAI,IAjEC,CAAI,oBAAC,EAAoB;YAAG,OAAO,QAAA,CAAS;QAkEjD,IAAI,IAjEC,CAAI,OAAC;YAAQ,OAAO,OAAA,CAAQ;QAkEjC,IAAI,IAjEC,CAAI,sBAAC,CAAsB,OAAC,CAAO;YAAE,OAAO,OAAA,CAAQ;QAkEzD,IAAI,IAjEC,CAAI,sBAAC,CAAsB,OAAC,CAAO;YAAE,OAAO,OAAA,CAAQ;QAkEzD,OAjEO,KAAA,CAAM;KAkEd;;;;;;IAMH,YApEY,GAoEZ,GApEY;;;;;;;IA2EZ,aAxEY,CAAA,EAAA,EAwEZ,GAxEY;;;;;;;IA+EZ,YA5EY,CAAA,SAAA,EA4EZ,GA5EY;;;;;;IAkFZ,oBA/EY,GA+EZ,GA/EY;;;;;;IAqFZ,sBAlFG,CAAA,MAAA,EAkFH;QACI,OAlFO,IAAA,CAAK,YAAC,CAAY,CAAC,OAAS,KAAoB,OAAA,CAAQ,MAAC,KAAU,MAAA,CAAO,CAAC;KAmFnF;;;;;IAKH,iBApFG,GAoFH;QACI,OApFO,IAAA,CAAK,YAAC,CAAY,CAAC,OAAS,KAAoB,OAAA,CAAQ,KAAC,CAAK,CAAC;KAqFvE;;;;;IAKH,mBAtFG,GAsFH;QACI,OAtFO,IAAA,CAAK,YAAC,CAAY,CAAC,OAAS,KAAoB,OAAA,CAAQ,OAAC,CAAO,CAAC;KAuFzE;;;;;;IAMH,eAzFG,CAAA,IAyFH,GAzFG,EAAA,EAyFH;QACI,IAAI,CAzFC,SAAC,GAAW,CAAA,IAAE,CAAI,iBAAC,EAAiB,CAAE;QA2F3C,IAAI,IAzFC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YA0FlC,IAAI,CAzFC,OAAC,CAAO,eAAC,CAAe,IAAC,CAAI,CAAC;SA0FpC;KACF;;;;;;IAMH,cA5FG,CAAA,IA4FH,GA5FG,EAAA,EA4FH;QACI,IAAI,CA5FC,QAAC,GAAU,IAAA,CAAK,mBAAC,EAAmB,CAAE;QA8F3C,IAAI,IA5FC,CAAI,OAAC,IAAU,CAAA,IAAE,CAAI,QAAC,EAAS;YA6FlC,IAAI,CA5FC,OAAC,CAAO,cAAC,CAAc,IAAC,CAAI,CAAC;SA6FnC;KACF;;;;;;IAUH,aAhGG,CAAA,SAAA,EAgGH;QACI,OAhGO,OAAO,SAAA,KAAc,QAAA,IAAY,SAAA,KAAc,IAAA;YAiGlD,MAAM,CAhGC,IAAC,CAAI,SAAC,CAAS,CAAC,MAAC,KAAU,CAAA,IAAK,OAAA,IAAW,SAAA,IAAa,UAAA,IAAc,SAAA,CAAU;KAiG5F;;;;;;IAMH,2BAnGG,CAAA,EAAA,EAmGH,EAnGsD,IAAA,CAAK,mBAAC,GAAqB,EAAA,CAAG,EAAC;CAoGpF;AAED,AAsCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,AAAA,MAAA,WAzIC,SAAA,eAAA,CAyID;;;;;;IAUA,WAAA,CACM,SADN,GA9IuB,IAAA,EAAM,SAAuC,EAgJ9D,cA/IsD,EA6I5D;QAGI,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,sBAAsB,CAAC,cAAc,CAAC,CAAC,CAAC;;;;QAThF,IAAA,CAAA,SA3IG,GAAA,EAAA,CAAA;QAqJC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB;;;;;;;;;;;;;;;;;;;;;IAqBH,QAjJG,CAAA,KAAA,EAAA,OAiJH,GAKM,EAAE,EALR;QAMI,IAAI,CAjJC,MAAC,GAAQ,KAAA,CAAM;QAkJpB,IAAI,IAjJC,CAAI,SAAC,CAAS,MAAC,IAAS,OAAA,CAAQ,qBAAC,KAAyB,KAAA,EAAO;YAkJpE,IAAI,CAjJC,SAAC,CAAS,OAAC,CAkJZ,CAAC,QAjJC,KAAY,QAAA,CAAS,IAAC,CAAI,MAAC,EAAO,OAAA,CAAQ,qBAAC,KAAyB,KAAA,CAAM,CAAC,CAAC;SAkJnF;QACD,IAAI,CAjJC,sBAAC,CAAsB,OAAC,CAAO,CAAC;KAkJtC;;;;;;;;;;;IAWH,UAnJG,CAAA,KAAA,EAAA,OAmJH,GAKM,EAAE,EALR;QAMI,IAAI,CAnJC,QAAC,CAAQ,KAAC,EAAM,OAAA,CAAQ,CAAC;KAoJ/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCH,KArJG,CAAA,SAqJH,GArJG,IAAA,EAAA,OAqJH,GArJG,EAAA,EAqJH;QACI,IAAI,CArJC,eAAC,CAAe,SAAC,CAAS,CAAC;QAsJhC,IAAI,CArJC,cAAC,CAAc,OAAC,CAAO,CAAC;QAsJ7B,IAAI,CArJC,eAAC,CAAe,OAAC,CAAO,CAAC;QAsJ9B,IAAI,CArJC,QAAC,CAAQ,IAAC,CAAI,MAAC,EAAO,OAAA,CAAQ,CAAC;KAsJrC;;;;;IAKH,YArJG,GAqJH,GArJiB;;;;;;IA2JjB,YAtJG,CAAA,SAAA,EAsJH,EAtJ+C,OAAO,KAAA,CAAM,EAAC;;;;;IA2J7D,oBAtJG,GAsJH,EAtJoC,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;;;IA4J1D,gBAvJG,CAAA,EAAA,EAuJH,EAvJyC,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,EAAC,CAAE,CAAC,EAAC;;;;;IA4JlE,eAvJG,GAuJH;QACI,IAAI,CAvJC,SAAC,GAAW,EAAA,CAAG;QAwJpB,IAAI,CAvJC,iBAAC,GAAmB,EAAA,CAAG;QAwJ5B,IAAI,CAvJC,mBAAC,GAAqB,MAuJ/B,GAvJqC,CAAG;KAwJrC;;;;;;IAMH,wBAxJG,CAAA,EAAA,EAwJH;QACI,IAAI,CAxJC,iBAAC,CAAiB,IAAC,CAAI,EAAC,CAAE,CAAC;KAyJjC;;;;;;IAMH,aAzJG,CAAA,EAAA,EAyJH,GAzJoC;;;;;IAEjC,eAAA,CAAA,SAAA,EAAH;QA6JI,IAAI,IA5JC,CAAI,aAAC,CAAa,SAAC,CAAS,EAAE;YA6JjC,IAAI,CA5JC,MAAC,GAAQ,SAAA,CAAU,KAAC,CAAK;YA6J9B,SAAS,CA5JC,QAAC,GAAU,IAAA,CAAK,OAAC,CAAO,EAAC,QAAC,EAAS,IAAA,EAAM,SAAA,EAAW,KAAA,EAAM,CAAC;gBA6JhD,IAAI,CA5JC,MAAC,CAAM,EAAC,QAAC,EAAS,IAAA,EAAM,SAAA,EAAW,KAAA,EAAM,CAAC,CAAC;SA6JtE;aA5JM;YA6JL,IAAI,CA5JC,MAAC,GAAQ,SAAA,CAAU;SA6JzB;KACF;CACF;AAED,AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,AAAA,MAAA,SApKC,SAAA,eAAA,CAoKD;;;;;;IAMA,WAAA,CAxKa,QAA0C,EAAE,SAAyB,EA0K5E,cAzKmC,EAuKzC;QAGI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE,cAAc,IAAI,IAAI,CAAC,CAAC;QA3KxC,IAAb,CAAA,QAAa,GAAA,QAAA,CAA0C;QA4KnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;KACjE;;;;;;;;;;IAUH,eA3KG,CAAA,IAAA,EAAA,OAAA,EA2KH;QACI,IAAI,IA3KC,CAAI,QAAC,CAAQ,IAAC,CAAI;YAAE,OAAO,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,CAAC;QA4KpD,IAAI,CA3KC,QAAC,CAAQ,IAAC,CAAI,GAAG,OAAA,CAAQ;QA4K9B,OAAO,CA3KC,SAAC,CAAS,IAAC,CAAI,CAAC;QA4KxB,OAAO,CA3KC,2BAAC,CAA2B,IAAC,CAAI,mBAAC,CAAmB,CAAC;QA4K9D,OA3KO,OAAA,CAAQ;KA4KhB;;;;;;;IAOH,UA7KG,CAAA,IAAA,EAAA,OAAA,EA6KH;QACI,IAAI,CA7KC,eAAC,CAAe,IAAC,EAAK,OAAA,CAAQ,CAAC;QA8KpC,IAAI,CA7KC,sBAAC,EAAsB,CAAE;QA8K9B,IAAI,CA7KC,mBAAC,EAAmB,CAAE;KA8K5B;;;;;;IAMH,aA9KG,CAAA,IAAA,EA8KH;QACI,IAAI,IA9KC,CAAI,QAAC,CAAQ,IAAC,CAAI;YAAE,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,CAAC,2BAAC,CAA2B,MAA7E,GAAmF,CAAG,CAAC;QA+KnF,QA9KO,IAAE,CAAI,QAAC,CAAQ,IAAC,CAAI,CAAC,CAAC;QA+K7B,IAAI,CA9KC,sBAAC,EAAsB,CAAE;QA+K9B,IAAI,CA9KC,mBAAC,EAAmB,CAAE;KA+K5B;;;;;;;IAOH,UAhLG,CAAA,IAAA,EAAA,OAAA,EAgLH;QACI,IAAI,IAhLC,CAAI,QAAC,CAAQ,IAAC,CAAI;YAAE,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,CAAC,2BAAC,CAA2B,MAA7E,GAAmF,CAAG,CAAC;QAiLnF,QAhLO,IAAE,CAAI,QAAC,CAAQ,IAAC,CAAI,CAAC,CAAC;QAiL7B,IAAI,OAhLC;YAAQ,IAAA,CAAK,eAAC,CAAe,IAAC,EAAK,OAAA,CAAQ,CAAC;QAiLjD,IAAI,CAhLC,sBAAC,EAAsB,CAAE;QAiL9B,IAAI,CAhLC,mBAAC,EAAmB,CAAE;KAiL5B;;;;;;;;;IASH,QAjLG,CAAA,WAAA,EAiLH;QACI,OAjLO,IAAA,CAAK,QAAC,CAAQ,cAAC,CAAc,WAAC,CAAW,IAAI,IAAA,CAAK,QAAC,CAAQ,WAAC,CAAW,CAAC,OAAC,CAAO;KAkLxF;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BH,QAnLG,CAAA,KAAA,EAAA,OAmLH,GAnLG,EAAA,EAmLH;QAEI,IAAI,CAnLC,sBAAC,CAAsB,KAAC,CAAK,CAAC;QAoLnC,MAAM,CAnLC,IAAC,CAAI,KAAC,CAAK,CAAC,OAAC,CAAO,IAAC,IAmLhC;YACM,IAAI,CAnLC,sBAAC,CAAsB,IAAC,CAAI,CAAC;YAoLlC,IAAI,CAnLC,QAAC,CAAQ,IAAC,CAAI,CAAC,QAAC,CAAQ,KAAC,CAAK,IAAC,CAAI,EAAE,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;SAoL3F,CAnLC,CAAC;QAoLH,IAAI,CAnLC,sBAAC,CAAsB,OAAC,CAAO,CAAC;KAoLtC;;;;;;;;;;;;;;;;;;;;;;;;;IAyBH,UArLG,CAAA,KAAA,EAAA,OAqLH,GArLG,EAAA,EAqLH;QAEI,MAAM,CArLC,IAAC,CAAI,KAAC,CAAK,CAAC,OAAC,CAAO,IAAC,IAqLhC;YACM,IAAI,IArLC,CAAI,QAAC,CAAQ,IAAC,CAAI,EAAE;gBAsLvB,IAAI,CArLC,QAAC,CAAQ,IAAC,CAAI,CAAC,UAAC,CAAU,KAAC,CAAK,IAAC,CAAI,EAAE,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;aAsL7F;SACF,CArLC,CAAC;QAsLH,IAAI,CArLC,sBAAC,CAAsB,OAAC,CAAO,CAAC;KAsLtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoCH,KAvLG,CAAA,KAuLH,GAvLG,EAAA,EAAA,OAuLH,GAvLG,EAAA,EAuLH;QACI,IAAI,CAvLC,aAAC,CAAa,CAAC,OAAS,EAAiB,IAAM,KAuLxD;YACM,OAAO,CAvLC,KAAC,CAAK,KAAC,CAAK,IAAC,CAAI,EAAE,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;SAwL5E,CAvLC,CAAC;QAwLH,IAAI,CAvLC,sBAAC,CAAsB,OAAC,CAAO,CAAC;QAwLrC,IAAI,CAvLC,eAAC,CAAe,OAAC,CAAO,CAAC;QAwL9B,IAAI,CAvLC,cAAC,CAAc,OAAC,CAAO,CAAC;KAwL9B;;;;;;;;IAQH,WAvLG,GAuLH;QACI,OAvLO,IAAA,CAAK,eAAC,CAwLT,EAAE,EAvLE,CAAA,GAAoC,EAAE,OAAS,EAAiB,IAAM,KAsLlF;YAEU,GAAG,CAvLC,IAAC,CAAI,GAAG,OAAA,YAAmB,WAAA,GAAc,OAAA,CAAQ,KAAC,GAAO,EAAM,OAAC,GAAQ,WAAC,EAAW,CAAE;YAwL1F,OAvLO,GAAA,CAAI;SAwLZ,CAvLC,CAAC;KAwLR;;;;;;IAMH,sBA1LG,CAAA,IAAA,EA0LH;QACI,IAAI,CA1LC,MAAC,CAAM,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,CAAC,MAAC,EAAO;YA2LtC,MA1LM,IAAI,KAAA,CAAM,CA0LtB;;;MAGA,CAAO,CA1LC,CAAC;SA2LJ;QACD,IAAI,CA1LC,IAAC,CAAI,QAAC,CAAQ,IAAC,CAAI,EAAE;YA2LxB,MA1LM,IAAI,KAAA,CAAM,CA0LtB,oCAAA,EA1LuB,IAAuC,CA0L9D,CAAA,CA1LkE,CAAG,CAAC;SA2LjE;KACF;;;;;;IAMH,aA7LG,CAAA,EAAA,EA6LH;QACI,MAAM,CA7LC,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,CAAC,OAAC,CAAO,CAAC,IAAI,EAAA,CAAG,IAAC,CAAI,QAAC,CAAQ,CAAC,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC;KA8LlE;;;;;IAKH,cA/LG,GA+LH;QACI,IAAI,CA/LC,aAAC,CAAa,CAAC,OAAS,KA+LjC;YACM,OAAO,CA/LC,SAAC,CAAS,IAAC,CAAI,CAAC;YAgMxB,OAAO,CA/LC,2BAAC,CAA2B,IAAC,CAAI,mBAAC,CAAmB,CAAC;SAgM/D,CA/LC,CAAC;KAgMJ;;;;;IAKH,YAjMG,GAiMH,EAjMyB,IAAA,CAAK,MAAC,GAAQ,IAAA,CAAK,YAAC,EAAY,CAAE,EAAC;;;;;;IAuM5D,YApMG,CAAA,SAAA,EAoMH;QACI,qBApMI,GAAA,GAAM,KAAA,CAAM;QAqMhB,IAAI,CApMC,aAAC,CAAa,CAAC,OAAS,EAAiB,IAAM,KAoMxD;YACM,GAAG,GApMG,GAAA,KAAO,IAAE,CAAI,QAAC,CAAQ,IAAC,CAAI,IAAI,SAAA,CAAU,OAAC,CAAO,CAAC,CAAC;SAqM1D,CApMC,CAAC;QAqMH,OApMO,GAAA,CAAI;KAqMZ;;;;;IAKH,YAtMG,GAsMH;QACI,OAtMO,IAAA,CAAK,eAAC,CAuMT,EAAE,EAtME,CAAA,GAAoC,EAAE,OAAS,EAAiB,IAAM,KAqMlF;YAEU,IAAI,OAtMC,CAAO,OAAC,IAAU,IAAA,CAAK,QAAC,EAAS;gBAuMpC,GAAG,CAtMC,IAAC,CAAI,GAAG,OAAA,CAAQ,KAAC,CAAK;aAuM3B;YACD,OAtMO,GAAA,CAAI;SAuMZ,CAtMC,CAAC;KAuMR;;;;;;;IAOH,eA1MG,CAAA,SAAA,EAAA,EAAA,EA0MH;QACI,qBA1MI,GAAA,GAAM,SAAA,CAAU;QA2MpB,IAAI,CA1MC,aAAC,CA2MF,CAAC,OA1MS,EAAiB,IAAM,KAyMzC,EAzMsD,GAAA,GAAM,EAAA,CAAG,GAAC,EAAI,OAAA,EAAS,IAAA,CAAK,CAAC,EAAC,CAAE,CAAC;QA2MnF,OA1MO,GAAA,CAAI;KA2MZ;;;;;IAKH,oBA5MG,GA4MH;QACI,KAAK,uBA5MM,WAAA,IAAe,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,EAAE;YA6MpD,IAAI,IA5MC,CAAI,QAAC,CAAQ,WAAC,CAAW,CAAC,OAAC,EAAQ;gBA6MtC,OA5MO,KAAA,CAAM;aA6Md;SACF;QACD,OA5MO,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,CAAC,MAAC,GAAQ,CAAA,IAAK,IAAA,CAAK,QAAC,CAAQ;KA6M/D;;;;;;IAMH,sBA/MG,CAAA,KAAA,EA+MH;QACI,IAAI,CA/MC,aAAC,CAAa,CAAC,OAAS,EAAiB,IAAM,KA+MxD;YACM,IAAI,KA/MC,CAAK,IAAC,CAAI,KAAK,SAAA,EAAW;gBAgN7B,MA/MM,IAAI,KAAA,CAAM,CA+MxB,iDAAA,EA/MyB,IAAoD,CA+M7E,EAAA,CA/MiF,CAAI,CAAC;aAgN/E;SACF,CA/MC,CAAC;KAgNJ;CACF;AAED,AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,AAAA,MAAA,SApNC,SAAA,eAAA,CAoND;;;;;;IAMA,WAAA,CAxNa,QAA0B,EAAG,SAAyB,EA0N7D,cAzNmC,EAuNzC;QAGI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE,cAAc,IAAI,IAAI,CAAC,CAAC;QA3NxC,IAAb,CAAA,QAAa,GAAA,QAAA,CAA0B;QA4NnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,sBAAsB,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;KACjE;;;;;;IAMH,EA1NG,CAAA,KAAA,EA0NH,EA1NuC,OAAO,IAAA,CAAK,QAAC,CAAQ,KAAC,CAAK,CAAC,EAAC;;;;;;IAgOpE,IA3NG,CAAA,OAAA,EA2NH;QACI,IAAI,CA3NC,QAAC,CAAQ,IAAC,CAAI,OAAC,CAAO,CAAC;QA4N5B,IAAI,CA3NC,gBAAC,CAAgB,OAAC,CAAO,CAAC;QA4N/B,IAAI,CA3NC,sBAAC,EAAsB,CAAE;QA4N9B,IAAI,CA3NC,mBAAC,EAAmB,CAAE;KA4N5B;;;;;;;IAOH,MA7NG,CAAA,KAAA,EAAA,OAAA,EA6NH;QACI,IAAI,CA7NC,QAAC,CAAQ,MAAC,CAAM,KAAC,EAAM,CAAA,EAAG,OAAA,CAAQ,CAAC;QA+NxC,IAAI,CA7NC,gBAAC,CAAgB,OAAC,CAAO,CAAC;QA8N/B,IAAI,CA7NC,sBAAC,EAAsB,CAAE;QA8N9B,IAAI,CA7NC,mBAAC,EAAmB,CAAE;KA8N5B;;;;;;IAMH,QA9NG,CAAA,KAAA,EA8NH;QACI,IAAI,IA9NC,CAAI,QAAC,CAAQ,KAAC,CAAK;YAAE,IAAA,CAAK,QAAC,CAAQ,KAAC,CAAK,CAAC,2BAAC,CAA2B,MAA/E,GAAqF,CAAG,CAAC;QA+NrF,IAAI,CA9NC,QAAC,CAAQ,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;QA+N/B,IAAI,CA9NC,sBAAC,EAAsB,CAAE;QA+N9B,IAAI,CA9NC,mBAAC,EAAmB,CAAE;KA+N5B;;;;;;;IAOH,UAhOG,CAAA,KAAA,EAAA,OAAA,EAgOH;QACI,IAAI,IAhOC,CAAI,QAAC,CAAQ,KAAC,CAAK;YAAE,IAAA,CAAK,QAAC,CAAQ,KAAC,CAAK,CAAC,2BAAC,CAA2B,MAA/E,GAAqF,CAAG,CAAC;QAiOrF,IAAI,CAhOC,QAAC,CAAQ,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;QAkO/B,IAAI,OAhOC,EAAQ;YAiOX,IAAI,CAhOC,QAAC,CAAQ,MAAC,CAAM,KAAC,EAAM,CAAA,EAAG,OAAA,CAAQ,CAAC;YAiOxC,IAAI,CAhOC,gBAAC,CAAgB,OAAC,CAAO,CAAC;SAiOhC;QAED,IAAI,CAhOC,sBAAC,EAAsB,CAAE;QAiO9B,IAAI,CAhOC,mBAAC,EAAmB,CAAE;KAiO5B;;;;;IAKH,IAhOG,MAAA,GAgOH,EAhOyB,OAAO,IAAA,CAAK,QAAC,CAAQ,MAAC,CAAM,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAyPtD,QAlOG,CAAA,KAAA,EAAA,OAkOH,GAlOG,EAAA,EAkOH;QACI,IAAI,CAlOC,sBAAC,CAAsB,KAAC,CAAK,CAAC;QAmOnC,KAAK,CAlOC,OAAC,CAAO,CAAC,QAAU,EAAK,KAAO,KAkOzC;YACM,IAAI,CAlOC,sBAAC,CAAsB,KAAC,CAAK,CAAC;YAmOnC,IAAI,CAlOC,EAAC,CAAE,KAAC,CAAK,CAAC,QAAC,CAAQ,QAAC,EAAS,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;SAmOnF,CAlOC,CAAC;QAmOH,IAAI,CAlOC,sBAAC,CAAsB,OAAC,CAAO,CAAC;KAmOtC;;;;;;;;;;;;;;;;;;;;;;;;IAwBH,UApOG,CAAA,KAAA,EAAA,OAoOH,GApOG,EAAA,EAoOH;QACI,KAAK,CApOC,OAAC,CAAO,CAAC,QAAU,EAAK,KAAO,KAoOzC;YACM,IAAI,IApOC,CAAI,EAAC,CAAE,KAAC,CAAK,EAAE;gBAqOlB,IAAI,CApOC,EAAC,CAAE,KAAC,CAAK,CAAC,UAAC,CAAU,QAAC,EAAS,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;aAqOrF;SACF,CApOC,CAAC;QAqOH,IAAI,CApOC,sBAAC,CAAsB,OAAC,CAAO,CAAC;KAqOtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCH,KAtOG,CAAA,KAsOH,GAtOG,EAAA,EAAA,OAsOH,GAtOG,EAAA,EAsOH;QACI,IAAI,CAtOC,aAAC,CAAa,CAAC,OAAS,EAAiB,KAAO,KAsOzD;YACM,OAAO,CAtOC,KAAC,CAAK,KAAC,CAAK,KAAC,CAAK,EAAE,EAAA,QAAE,EAAS,IAAA,EAAM,SAAA,EAAW,OAAA,CAAQ,SAAC,EAAS,CAAC,CAAC;SAuO7E,CAtOC,CAAC;QAuOH,IAAI,CAtOC,sBAAC,CAAsB,OAAC,CAAO,CAAC;QAuOrC,IAAI,CAtOC,eAAC,CAAe,OAAC,CAAO,CAAC;QAuO9B,IAAI,CAtOC,cAAC,CAAc,OAAC,CAAO,CAAC;KAuO9B;;;;;;;;IAQH,WAtOG,GAsOH;QACI,OAtOO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,CAAC,OAAS,KAsOvC;YACM,OAtOO,OAAA,YAAmB,WAAA,GAAc,OAAA,CAAQ,KAAC,GAAO,EAAM,OAAC,GAAQ,WAAC,EAAW,CAAE;SAuOtF,CAtOC,CAAC;KAuOJ;;;;;;IAMH,sBAzOG,CAAA,KAAA,EAyOH;QACI,IAAI,CAzOC,IAAC,CAAI,QAAC,CAAQ,MAAC,EAAO;YA0OzB,MAzOM,IAAI,KAAA,CAAM,CAyOtB;;;MAGA,CAAO,CAzOC,CAAC;SA0OJ;QACD,IAAI,CAzOC,IAAC,CAAI,EAAC,CAAE,KAAC,CAAK,EAAE;YA0OnB,MAzOM,IAAI,KAAA,CAAM,CAyOtB,kCAAA,EAzOuB,KAAqC,CAyO5D,CAzOiE,CAAE,CAAC;SA0O/D;KACF;;;;;;IAMH,aA5OG,CAAA,EAAA,EA4OH;QACI,IAAI,CA5OC,QAAC,CAAQ,OAAC,CAAO,CAAC,OAAS,EAAiB,KAAO,KA4O5D,EA5OyE,EAAA,CAAG,OAAC,EAAQ,KAAA,CAAM,CAAC,EAAC,CAAE,CAAC;KA6O7F;;;;;IAKH,YA9OG,GA8OH;QACI,IAAI,CA9OC,MAAC,GAAQ,IAAA,CAAK,QAAC,CAAQ,MAAC,CAAM,CAAC,OAAC,KAAW,OAAA,CAAQ,OAAC,IAAU,IAAA,CAAK,QAAC,CAAQ;aA+O9D,GA9OC,CAAG,CAAC,OAAC,KAAW,OAAA,CAAQ,KAAC,CAAK,CAAC;KA+OpD;;;;;;IAMH,YAjPG,CAAA,SAAA,EAiPH;QACI,OAjPO,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,CAAC,OAAS,KAAoB,OAAA,CAAQ,OAAC,IAAU,SAAA,CAAU,OAAC,CAAO,CAAC,CAAC;KAkPhG;;;;;IAKH,cAnPG,GAmPH;QACI,IAAI,CAnPC,aAAC,CAAa,CAAC,OAAS,KAAoB,IAAA,CAAK,gBAAC,CAAgB,OAAC,CAAO,CAAC,CAAC;KAoPlF;;;;;;IAMH,sBAtPG,CAAA,KAAA,EAsPH;QACI,IAAI,CAtPC,aAAC,CAAa,CAAC,OAAS,EAAiB,CAAG,KAsPrD;YACM,IAAI,KAtPC,CAAK,CAAC,CAAC,KAAK,SAAA,EAAW;gBAuP1B,MAtPM,IAAI,KAAA,CAAM,CAsPxB,+CAAA,EAtPyB,CAAkD,CAsP3E,CAAA,CAtP4E,CAAG,CAAC;aAuPzE;SACF,CAtPC,CAAC;KAuPJ;;;;;IAKH,oBAxPG,GAwPH;QACI,KAAK,uBAxPM,OAAA,IAAW,IAAA,CAAK,QAAC,EAAS;YAyPnC,IAAI,OAxPC,CAAO,OAAC;gBAAQ,OAAO,KAAA,CAAM;SAyPnC;QACD,OAxPO,IAAA,CAAK,QAAC,CAAQ,MAAC,GAAQ,CAAA,IAAK,IAAA,CAAK,QAAC,CAAQ;KAyPlD;;;;;IAtPA,gBAAA,CAAA,OAAA,EAAH;QA4PI,OAAO,CA3PC,SAAC,CAAS,IAAC,CAAI,CAAC;QA4PxB,OAAO,CA3PC,2BAAC,CAA2B,IAAC,CAAI,mBAAC,CAAmB,CAAC;KA4P/D;CACF,AAED,AAGC;;ADrlDD;;;;;;;AASA,AAEA,AACA,AAEA,AAKA,AAEA,AADO,MAAM,qBAAA,GAA6B;IAExC,OAAO,EADE,gBAAA;IAET,WAAC,EADY,UAAA,CAAW,MAAM,MAAA,CAAO;CAEtC,CADC;AAGF,MADM,eAAA,GAAkB,OAAA,CAAQ,OAAC,CAAO,IAAC,CAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiC9C,AAAA,MAAA,MACC,SAAA,gBAAA,CADD;;;;;IASA,WAAA,CACS,UAFgB,EAGhB,eAFqB,EAA9B;QAGI,KAAK,EAAE,CAAC;QAVF,IAAV,CAAA,UAAU,GAAsB,KAAA,CAAM;QAEpC,IAAF,CAAA,QAAU,GACG,IAAI,YAAA,EAAa,CAAE;QAQ5B,IAAI,CAAC,IAAI;YACL,IAAI,SAAS,CAAC,EAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,EAAE,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC;KAC/F;;;;IAIH,IAJG,SAAA,GAIH,EAJ6B,OAAO,IAAA,CAAK,UAAC,CAAU,EAAC;;;;IAQrD,IANG,aAAA,GAMH,EAN8B,OAAO,IAAA,CAAK,EAAC;;;;IAU3C,IARG,OAAA,GAQH,EAR6B,OAAO,IAAA,CAAK,IAAC,CAAI,EAAC;;;;IAY/C,IAVG,IAAA,GAUH,EAVyB,OAAO,EAAA,CAAG,EAAC;;;;IAcpC,IAZG,QAAA,GAYH,EAZqD,OAAO,IAAA,CAAK,IAAC,CAAI,QAAC,CAAQ,EAAC;;;;;IAiBhF,UAfG,CAAA,GAAA,EAeH;QACI,eAAe,CAfC,IAAC,CAAI,MAezB;YACM,uBAfM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,IAAC,CAAI,CAAC;YAgBhD,GAAG,CAfC,QAAC,IAAuB,SAAC,CAAS,eAAC,CAAe,GAAC,CAAG,IAAC,EAAK,GAAA,CAAI,OAAC,CAAO,CAAA,CAAC;YAgB7E,YAAY,CAfC,GAAC,CAAG,OAAC,EAAQ,GAAA,CAAI,CAAC;YAgB/B,GAAG,CAfC,OAAC,CAAO,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;SAgBxD,CAfC,CAAC;KAgBJ;;;;;IAKH,UAlBG,CAAA,GAAA,EAkBH,EAlB0C,QAAoB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,EAAC,EAAC;;;;;IAuBvF,aArBG,CAAA,GAAA,EAqBH;QACI,eAAe,CArBC,IAAC,CAAI,MAqBzB;YACM,uBArBM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,IAAC,CAAI,CAAC;YAsBhD,IAAI,SArBC,EAAU;gBAsBb,SAAS,CArBC,aAAC,CAAa,GAAC,CAAG,IAAC,CAAI,CAAC;aAsBnC;SACF,CArBC,CAAC;KAsBJ;;;;;IAKH,YAxBG,CAAA,GAAA,EAwBH;QACI,eAAe,CAxBC,IAAC,CAAI,MAwBzB;YACM,uBAxBM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,IAAC,CAAI,CAAC;YAyBhD,uBAxBM,KAAA,GAAQ,IAAI,SAAA,CAAU,EAAC,CAAE,CAAC;YAyBhC,kBAAkB,CAxBC,KAAC,EAAM,GAAA,CAAI,CAAC;YAyB/B,SAAS,CAxBC,eAAC,CAAe,GAAC,CAAG,IAAC,EAAK,KAAA,CAAM,CAAC;YAyB3C,KAAK,CAxBC,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;SAyBlD,CAxBC,CAAC;KAyBJ;;;;;IAKH,eA3BG,CAAA,GAAA,EA2BH;QACI,eAAe,CA3BC,IAAC,CAAI,MA2BzB;YACM,uBA3BM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,IAAC,CAAI,CAAC;YA4BhD,IAAI,SA3BC,EAAU;gBA4Bb,SAAS,CA3BC,aAAC,CAAa,GAAC,CAAG,IAAC,CAAI,CAAC;aA4BnC;SACF,CA3BC,CAAC;KA4BJ;;;;;IAKH,YA9BG,CAAA,GAAA,EA8BH,EA9B+C,QAAkB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,EAAC,EAAC;;;;;;IAoC1F,WAlCG,CAAA,GAAA,EAAA,KAAA,EAkCH;QACI,eAAe,CAlCC,IAAC,CAAI,MAkCzB;YACM,uBAlCM,IAAA,IAAoB,IAAC,CAAI,IAAC,CAAI,GAAC,oBAAG,GAAC,CAAG,IAAC,GAAM,CAAA,CAAC;YAmCpD,IAAI,CAlCC,QAAC,CAAQ,KAAC,CAAK,CAAC;SAmCtB,CAlCC,CAAC;KAmCJ;;;;;IAKH,QArCG,CAAA,KAAA,EAqCH,EArCgD,IAAA,CAAK,OAAC,CAAO,QAAC,CAAQ,KAAC,CAAK,CAAC,EAAC;;;;;IA0C9E,QAxCG,CAAA,MAAA,EAwCH;QACI,IAAI,CAxCC,UAAC,GAAY,IAAA,CAAK;QAyCvB,IAAI,CAxCC,QAAC,CAAQ,IAAC,CAAI,MAAC,CAAM,CAAC;QAyC3B,OAxCO,KAAA,CAAM;KAyCd;;;;IAIH,OA1CG,GA0CH,EA1CoB,IAAA,CAAK,SAAC,EAAS,CAAE,EAAC;;;;;IA+CtC,SA7CG,CAAA,KA6CH,GA7CG,SAAA,EA6CH;QACI,IAAI,CA7CC,IAAC,CAAI,KAAC,CAAK,KAAC,CAAK,CAAC;QA8CvB,IAAI,CA7CC,UAAC,GAAY,KAAA,CAAM;KA8CzB;;;;;;IAMH,cAhDG,CAAA,IAAA,EAgDH;QACI,IAAI,CAhDC,GAAC,EAAG,CAAE;QAiDX,OAhDO,IAAA,CAAK,MAAC,IAAmB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,IAAC,CAAI,IAAG,IAAA,CAAK,IAAC,CAAI;KAiDjE;;AA/CI,MAAP,CAAA,UAAO,GAAoC;IAiD3C,EAhDE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAiDvB,QAAQ,EAhDE,uDAAA;gBAiDV,SAAS,EAhDE,CAAA,qBAAE,CAAqB;gBAiDlC,IAAI,EAhDE,EAAA,UAAE,EAAW,kBAAA,EAAoB,SAAA,EAAW,WAAA,EAAY;gBAiD9D,OAAO,EAhDE,CAAA,UAAE,CAAU;gBAiDrB,QAAQ,EAhDE,QAAA;aAiDX,EAhDC,EAAG;CAiDJ,CAhDC;;;;AAED,MAAD,CAAA,cAAC,GAAA,MAAA;IAmDD,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;CACjH,CAAC,AAGF,AAcC;;ADtOD;;;;;;;AAOA,AAAC,MAAA,iBAAA,GAAA;IACC,eAAe,EAAE,CAAnB;;;;;;;;;OASA,CAAQ;IAEN,aAAa,EAAE,CAAjB;;;;;;;;;;;OAWA,CAAQ;IAEN,aAAa,EAAE,CAAjB;;;;;;;;;;;;;;OAcA,CAAQ;IAEN,YAAY,EAAE,CAAhB;;;;;WAKA,CAAY;IAEV,oBAAoB,EAAE,CAAxB;;;;;EAKA,CAAG;CACF,CAAC;;AD9DF;;;;;;;AASA,AACA,AAAA,MAAA,oBAAA,CAAA;;;;IAIA,OAHG,oBAAA,GAGH;QACI,MAHM,IAAI,KAAA,CAAM,CAGpB;;;;MAIA,EAAQF,iBAHC,CAAQ,eAAC,CAGlB;;;;;;MAMA,EAAQA,iBAHC,CAAQ,oBAAC,CAGlB,CAHsC,CAAE,CAAC;KAItC;;;;IAIH,OALG,sBAAA,GAKH;QACI,MALM,IAAI,KAAA,CAAM,CAKpB;;;;;MAKA,EAAQA,iBALC,CAAQ,aAAC,CAKlB;;;;MAIA,EAAQA,iBALC,CAAQ,YAAC,CAKlB,CAL8B,CAAE,CAAC;KAM9B;;;;IAIH,OAPG,oBAAA,GAOH;QACI,MAPM,IAAI,KAAA,CAQN,CADR;;;;6FAKA,CAA8F,CAPC,CAAC;KAQ7F;;;;IAIH,OATG,yBAAA,GASH;QACI,MATM,IAAI,KAAA,CAAM,CASpB;;;;;MAKA,EAAQA,iBATC,CAAQ,aAAC,CASlB;;;;MAIA,EAAQA,iBATC,CAAQ,YAAC,CASlB,CAT8B,CAAE,CAAC;KAU9B;CACF;;ADpED;;;;;;;AASA,AAEA,AAEA,AACA,AACA,AACA,AAEA,AADO,MAAM,kBAAA,GAA0B;IAErC,OAAO,EADE,gBAAA;IAET,WAAA,EADa,UAAA,CAAW,MAAM,YAAA,CAAa;CAE5C,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BF,AAAA,MAAA,YACC,SAAA,0BAAA,CADD;;;;;;IAOA,WAAA,CACQ,MAHQ,EAIP,UAHgB,EAIhB,eAHqB,EAA9B;QAII,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;KACzC;;;;;IAKH,gBALG,GAKH;QACI,IAAI,EALE,IAAC,CAAI,OAAC,YAAkB,YAAA,CAAa,IAAI,EAAE,IAAC,CAAI,OAAC,YAAkB,MAAA,CAAO,EAAE;YAMhF,oBAAoB,CALC,yBAAC,EAAyB,CAAE;SAMlD;KACF;;AAJI,YAAP,CAAA,UAAO,GAAoC;IAM3C,EALE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,gBAAA,EAAkB,SAAA,EAAW,CAAA,kBAAE,CAAkB,EAAE,QAAA,EAAU,cAAA,EAAe,EAAC,EAAG;CAMpH,CALC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IAQD,EAAC,IAZC,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA;IAaF,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;CACjH,CAAC;AANK,YAAP,CAAA,cAAO,GAAyD;IAQhE,MAAM,EAPE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,cAAE,EAAc,EAAG,EAAE;CAQlD,CAPC,AAUF,AAYC;;ADpGD;;;;;;;AASA,AAEA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAGA,AADO,MAAM,kBAAA,GAA0B;IAErC,OAAO,EADE,SAAA;IAET,WAAC,EADY,UAAA,CAAW,MAAM,OAAA,CAAQ;CAEvC,CADC;;;;;;;;;;;;;;;;;;AAmBF,MAACC,iBAAA,GAAA,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DD,AAAA,MAAA,OAEC,SAAA,SAAA,CAFD;;;;;;;IAwBA,WAAA,CAPe,MAAS,EAQP,UAPwC,EAQxC,eAPuD,EAS1D,cAPqC,EAGnD;QAKgB,KAAK,EAAE,CAAC;;;;QAxBxB,IAAA,CAAA,QAAG,GAAA,IAAA,WAAA,EAAA,CAAA;;;;QAIH,IAAA,CAAA,WAFG,GAAA,KAAA,CAAA;QAUA,IAAH,CAAA,MAAS,GAFG,IAAI,YAAA,EAAa,CAAE;QAef,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,GAAG,eAAe,IAAI,EAAE,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;;;;IAKf,WAVe,CAAA,OAAA,EAUf;QACgB,IAAI,CAVC,eAAC,EAAe,CAAE;QAWvB,IAAI,CAVC,IAAC,CAAI,WAAC;YAAY,IAAA,CAAK,aAAC,EAAa,CAAE;QAW5C,IAAI,YAVC,IAAe,OAAA,EAAS;YAW3B,IAAI,CAVC,eAAC,CAAe,OAAC,CAAO,CAAC;SAW/B;QAED,IAAI,iBAVC,CAAiB,OAAC,EAAQ,IAAA,CAAK,SAAC,CAAS,EAAE;YAW9C,IAAI,CAVC,YAAC,CAAY,IAAC,CAAI,KAAC,CAAK,CAAC;YAW9B,IAAI,CAVC,SAAC,GAAW,IAAA,CAAK,KAAC,CAAK;SAW7B;KACF;;;;IAIf,WAZe,GAYf,EAZoC,IAAA,CAAK,aAAC,IAAgB,IAAA,CAAK,aAAC,CAAa,aAAC,CAAa,IAAC,CAAI,CAAC,EAAC;;;;IAgBlG,IAde,OAAA,GAcf,EAd2C,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;IAkBjE,IAhBe,IAAA,GAgBf;QACgB,OAhBO,IAAA,CAAK,OAAC,GAAS,WAAA,CAAY,IAAC,CAAI,IAAC,EAAK,IAAA,CAAK,OAAC,CAAO,GAAG,CAAA,IAAE,CAAI,IAAC,CAAI,CAAC;KAiB1E;;;;IAIf,IAlBe,aAAA,GAkBf,EAlByC,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,aAAC,GAAe,IAAA,CAAK,EAAC;;;;IAsBlG,IApBe,SAAA,GAoBf,EApBkD,OAAO,iBAAA,CAAkB,IAAC,CAAI,cAAC,CAAc,CAAC,EAAC;;;;IAwBjG,IAtBe,cAAA,GAsBf;QACgB,OAtBO,sBAAA,CAAuB,IAAC,CAAI,mBAAC,CAAmB,CAAC;KAuBzD;;;;;IAKf,iBAzBe,CAAA,QAAA,EAyBf;QACgB,IAAI,CAzBC,SAAC,GAAW,QAAA,CAAS;QA0B1B,IAAI,CAzBC,MAAC,CAAM,IAAC,CAAI,QAAC,CAAQ,CAAC;KA0B5B;;;;IAvBA,aAAA,GAAf;QA4BgB,IAAI,CA3BC,aAAC,EAAa,GAAI,IAAA,CAAK,gBAAC,EAAgB;YA4BtB,IAAI,CA3BC,aAAC,CAAa,UAAC,CAAU,IAAC,CAAI,CAAC;QA4B3D,IAAI,CA3BC,WAAC,GAAa,IAAA,CAAK;KA4BzB;;;;IAzBA,aAAA,GAAf;QA8BgB,OA7BO,CAAA,IAAE,CAAI,OAAC,IAAU,CAAA,EAAG,IAAC,CAAI,OAAC,IAAU,IAAA,CAAK,OAAC,CAAO,UAAC,CAAU,CAAC;KA8BrE;;;;IA3BA,gBAAA,GAAf;QAgCgB,YAAY,CA/BC,IAAC,CAAI,QAAC,EAAS,IAAA,CAAK,CAAC;QAgClC,IAAI,CA/BC,QAAC,CAAQ,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;KAgC1D;;;;IA7BA,eAAA,GAAf;QAkCgB,IAAI,CAjCC,IAAC,CAAI,aAAC,EAAa,EAAG;YAkCzB,IAAI,CAjCC,gBAAC,EAAgB,CAAE;SAkCzB;QACD,IAAI,CAjCC,UAAC,EAAU,CAAE;KAkCnB;;;;IA/BA,gBAAA,GAAf;QAoCgB,IAAI,EAnCE,IAAC,CAAI,OAAC,YAAkB,YAAA,CAAa;YAoCvC,IAAI,CAnCC,OAAC,YAAkB,0BAAA,EAA4B;YAoCtD,oBAAoB,CAnCC,sBAAC,EAAsB,CAAE;SAoC/C;aAnCM,IAoCH,EAnCE,IAAC,CAAI,OAAC,YAAkB,YAAA,CAAa,IAAI,EAAE,IAAC,CAAI,OAAC,YAAkB,MAAA,CAAO,EAAE;YAoChF,oBAAoB,CAnCC,oBAAC,EAAoB,CAAE;SAoC7C;KACF;;;;IAjCA,UAAA,GAAf;QAsCgB,IAAI,IArCC,CAAI,OAAC,IAAU,IAAA,CAAK,OAAC,CAAO,IAAC;YAAK,IAAA,CAAK,IAAC,GAAM,IAAA,CAAK,OAAC,CAAO,IAAC,CAAI;QAuCrE,IAAI,CArCC,IAAC,CAAI,aAAC,EAAa,IAAK,CAAA,IAAE,CAAI,IAAC,EAAK;YAsCvC,oBAAoB,CArCC,oBAAC,EAAoB,CAAE;SAsC7C;KACF;;;;;IAnCA,YAAA,CAAA,KAAA,EAAf;QAyCgBA,iBAAe,CAxCC,IAAC,CAyCb,MADpB,EAvC4B,IAAA,CAAK,OAAC,CAAO,QAAC,CAAQ,KAAC,EAAM,EAAA,qBAAE,EAAsB,KAAA,EAAM,CAAC,CAAC,EAAC,CAAE,CAAC;KAyC9E;;;;;IAtCA,eAAA,CAAA,OAAA,EAAf;QA4CgB,uBA3CM,aAAA,GAAgB,OAAA,CAAQ,YAAC,CAAY,CAAC,YAAC,CAAY;QA6CzD,uBA3CM,UAAA,GA4CF,aAAa,KA3CK,EAAA,KAAM,aAAE,IAAgB,aAAA,KAAkB,OAAA,CAAQ,CAAC;QA6CzEA,iBAAe,CA3CC,IAAC,CAAI,MA2CrC;YACkB,IAAI,UA3CC,IAAa,CAAA,IAAE,CAAI,OAAC,CAAO,QAAC,EAAS;gBA4CxC,IAAI,CA3CC,OAAC,CAAO,OAAC,EAAO,CAAE;aA4CxB;iBA3CM,IAAA,CAAK,UAAC,IAAa,IAAA,CAAK,OAAC,CAAO,QAAC,EAAS;gBA4C/C,IAAI,CA3CC,OAAC,CAAO,MAAC,EAAM,CAAE;aA4CvB;SACF,CA3CC,CAAC;KA4CJ;;AA1CR,OAAP,CAAA,UAAO,GAAoC;IA4C3C,EA3CE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA4CvB,QAAQ,EA3CE,qDAAA;gBA4CV,SAAS,EA3CE,CAAA,kBAAE,CAAkB;gBA4C/B,QAAQ,EA3CE,SAAA;aA4CX,EA3CC,EAAG;CA4CJ,CA3CC;;;;AAED,OAAD,CAAA,cAAC,GAAA,MAAA;IA8CD,EAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAG,EAAC;IAC5E,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;IAClH,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,iBAAiB,EAAG,EAAE,EAAG,EAAC;CAC/G,CAAC;AA5CK,OAAP,CAAA,cAAO,GAAyD;IA8ChE,MAAM,EA7CE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IA8CzB,YAAY,EA7CE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,UAAE,EAAU,EAAG,EAAE;IA8CrD,OAAO,EA7CE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,SAAE,EAAS,EAAG,EAAE;IA8C/C,SAAS,EA7CE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,gBAAE,EAAgB,EAAG,EAAE;IA8CxD,QAAQ,EA7CE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,CAAA,eAAE,EAAe,EAAG,EAAE;CA8CtD,CA7CC,AAgDF,AAgCC;;ADtUD;;;;;;;AAUA,AACA,AAAA,MAAA,cAAA,CAAA;;;;IAIA,OAHG,sBAAA,GAGH;QACI,MAHM,IAAI,KAAA,CAIN,CADR;;;;;MAMA,EAAQD,iBAHC,CAAQ,eAAC,CAGlB,CAHiC,CAAE,CAAC;KAIjC;;;;IAIH,OALG,qBAAA,GAKH;QACI,MALM,IAAI,KAAA,CAMN,CADR;;;;;QAMA,EAAUA,iBALC,CAAQ,aAAC,CAKpB;;;;QAIA,EAAUA,iBALC,CAAQ,YAAC,CAKpB,CALgC,CAAE,CAAC;KAMhC;;;;IAIH,OARG,oBAAA,GAQH;QACI,MARM,IAAI,KAAA,CAAM,CAQpB;;;;OAIA,EAASA,iBARC,CAAQ,eAAC,CAQnB,CARkC,CAAE,CAAC;KASlC;;;;IAIH,OAVG,oBAAA,GAUH;QACI,MAVM,IAAI,KAAA,CAWN,CADR;;;;;MAMA,EAAQA,iBAVC,CAAQ,aAAC,CAUlB,CAV+B,CAAE,CAAC;KAW/B;;;;IAIH,OAZG,oBAAA,GAYH;QACI,MAZM,IAAI,KAAA,CAaN,CADR;;;;;QAMA,EAAUA,iBAZC,CAAQ,aAAC,CAYpB,CAZiC,CAAE,CAAC;KAajC;;;;IAIH,OAdG,mBAAA,GAcH;QACI,OAAO,CAdC,IAAC,CAAI,CAcjB;;;;;;;;;;IAUA,CAAK,CAdC,CAAC;KAeJ;CACF;;AD1FD;;;;;;;AASA,AAGA,AACA,AACA,AACA,AACA,AAGA,AADO,MAAMD,oBAAA,GAA0B;IAErC,OAAO,EADE,SAAA;IAET,WAAA,EADa,UAAA,CAAW,MAAM,oBAAA,CAAqB;CAEpD,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CF,AAAA,MAAA,oBAEC,SAAA,SAAA,CAFD;;;;;;IAgBA,WAAA,CAJe,UAA0C,EAKxC,eAJuD,EAM1D,cAJqC,EACnD;QAIgB,KAAK,EAAE,CAAC;QAfrB,IAAH,CAAA,MAAS,GAEG,IAAI,YAAA,EAAa,CAAE;QAcf,IAAI,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,GAAG,eAAe,IAAI,EAAE,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;;;;IAdf,IAAG,UAAA,CAAA,UAAA,EAAH,EAAwC,cAAA,CAAe,mBAAC,EAAmB,CAAE,EAAC;;;;;IAmB9E,WAPe,CAAA,OAAA,EAOf;QACgB,IAAI,IAPC,CAAI,iBAAC,CAAiB,OAAC,CAAO,EAAE;YAQnC,YAAY,CAPC,IAAC,CAAI,IAAC,EAAK,IAAA,CAAK,CAAC;YAQ9B,IAAI,IAPC,CAAI,OAAC,CAAO,QAAC,IAAQ,EAAG,IAAA,CAAK,aAAC,GAAe,gBAAC,EAAiB;gBAAA,EAAA,EAQlE,IAAI,CAPC,aAAC,GAAe,gBAAC,GAAkB,IAAC,CAAI,CAAC;aAQ/C;YACD,IAAI,CAPC,IAAC,CAAI,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;SAQtD;QACD,IAAI,iBAPC,CAAiB,OAAC,EAAQ,IAAA,CAAK,SAAC,CAAS,EAAE;YAQ9C,IAAI,CAPC,IAAC,CAAI,QAAC,CAAQ,IAAC,CAAI,KAAC,CAAK,CAAC;YAQ/B,IAAI,CAPC,SAAC,GAAW,IAAA,CAAK,KAAC,CAAK;SAQ7B;KACF;;;;IAIf,IATe,IAAA,GASf,EATqC,OAAO,EAAA,CAAG,EAAC;;;;IAahD,IAXe,SAAA,GAWf,EAXkD,OAAO,iBAAA,CAAkB,IAAC,CAAI,cAAC,CAAc,CAAC,EAAC;;;;IAejG,IAbe,cAAA,GAaf;QACgB,OAbO,sBAAA,CAAuB,IAAC,CAAI,mBAAC,CAAmB,CAAC;KAczD;;;;IAIf,IAfe,OAAA,GAef,EAf2C,OAAO,IAAA,CAAK,IAAC,CAAI,EAAC;;;;;IAoB7D,iBAlBe,CAAA,QAAA,EAkBf;QACgB,IAAI,CAlBC,SAAC,GAAW,QAAA,CAAS;QAmB1B,IAAI,CAlBC,MAAC,CAAM,IAAC,CAAI,QAAC,CAAQ,CAAC;KAmB5B;;;;;IAhBA,iBAAA,CAAA,OAAA,EAAf;QAsBgB,OArBO,OAAA,CAAQ,cAAC,CAAc,MAAC,CAAM,CAAC;KAsBvC;;AApBR,oBAAP,CAAA,UAAO,GAAoC;IAsB3C,EArBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,eAAA,EAAiB,SAAA,EAAW,CAAAA,oBAAE,CAAkB,EAAE,QAAA,EAAU,QAAA,EAAS,EAAC,EAAG;CAsB7G,CArBC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA;IAwBD,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;IAClH,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,iBAAiB,EAAG,EAAE,EAAG,EAAC;CAC/G,CAAC;AAtBK,oBAAP,CAAA,cAAO,GAAyD;IAwBhE,MAAM,EAvBE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,aAAE,EAAa,EAAG,EAAE;IAwBlD,OAAO,EAvBE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,SAAE,EAAS,EAAG,EAAE;IAwB/C,QAAQ,EAvBE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,CAAA,eAAE,EAAe,EAAG,EAAE;IAwBvD,YAAY,EAvBE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,UAAE,EAAU,EAAG,EAAE;CAwBpD,CAvBC,AA0BF,AAkBC;;ADjLD;;;;;;;AASA,AAEA,AACA,AAEA,AACA,AAKA,AADO,MAAMD,uBAAA,GAA6B;IAExC,OAAO,EADE,gBAAA;IAET,WAAA,EADa,UAAA,CAAW,MAAM,kBAAA,CAAmB;CAElD,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCF,AAAA,MAAA,kBACC,SAAA,gBAAA,CADD;;;;;IAYA,WAAA,CADiB,WAAiB,EACjB,gBAAsB,EAAvC;QAGI,KAAK,EAAE,CAAC;QAJK,IAAjB,CAAA,WAAiB,GAAA,WAAA,CAAiB;QACjB,IAAjB,CAAA,gBAAiB,GAAA,gBAAA,CAAsB;QAT7B,IAAV,CAAA,UAAU,GAAsB,KAAA,CAAM;QACpC,IAAF,CAAA,UAAY,GACsB,EAAA,CAAG;QAClC,IAAH,CAAA,IAAO,KACc,IAAA,EAAA,CAAO;QAAzB,IAAH,CAAA,QAAW,GACG,IAAI,YAAA,EAAa,CAAE;KAQ9B;;;;;IAKH,WALG,CAAA,OAAA,EAKH;QACI,IAAI,CALC,iBAAC,EAAiB,CAAE;QAMzB,IAAI,OALC,CAAO,cAAC,CAAc,MAAC,CAAM,EAAE;YAMlC,IAAI,CALC,iBAAC,EAAiB,CAAE;YAMzB,IAAI,CALC,eAAC,EAAe,CAAE;YAMvB,IAAI,CALC,oBAAC,EAAoB,CAAE;SAM7B;KACF;;;;IAIH,IAPG,SAAA,GAOH,EAP6B,OAAO,IAAA,CAAK,UAAC,CAAU,EAAC;;;;IAWrD,IATG,aAAA,GASH,EAT8B,OAAO,IAAA,CAAK,EAAC;;;;IAa3C,IAXG,OAAA,GAWH,EAX6B,OAAO,IAAA,CAAK,IAAC,CAAI,EAAC;;;;IAe/C,IAbG,IAAA,GAaH,EAbyB,OAAO,EAAA,CAAG,EAAC;;;;;IAkBpC,UAhBG,CAAA,GAAA,EAgBH;QACI,uBAhBM,IAAA,GAAY,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,CAAC;QAiB1C,YAAY,CAhBC,IAAC,EAAK,GAAA,CAAI,CAAC;QAiBxB,IAAI,CAhBC,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;QAiBhD,IAAI,CAhBC,UAAC,CAAU,IAAC,CAAI,GAAC,CAAG,CAAC;QAiB1B,OAhBO,IAAA,CAAK;KAiBb;;;;;IAKH,UAnBG,CAAA,GAAA,EAmBH,EAnBkD,QAAoB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,EAAC,EAAC;;;;;IAwB/F,aAtBG,CAAA,GAAA,EAsBH,EAtB8C,MAAA,CAAO,IAAC,CAAI,UAAC,EAAW,GAAA,CAAI,CAAC,EAAC;;;;;IA2B5E,YAzBG,CAAA,GAAA,EAyBH;QACI,uBAzBM,IAAA,GAAY,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,CAAC;QA0B1C,kBAAkB,CAzBC,IAAC,EAAK,GAAA,CAAI,CAAC;QA0B9B,IAAI,CAzBC,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;KA0BjD;;;;;IAKH,eA5BG,CAAA,GAAA,EA4BH,GA5B4C;;;;;IAiC5C,YA/BG,CAAA,GAAA,EA+BH,EA/BgD,QAAkB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,EAAC,EAAC;;;;;IAoC3F,YAlCG,CAAA,GAAA,EAkCH;QACI,uBAlCM,IAAA,GAAY,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,CAAC;QAmC1C,kBAAkB,CAlCC,IAAC,EAAK,GAAA,CAAI,CAAC;QAmC9B,IAAI,CAlCC,sBAAC,CAAsB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;KAmCjD;;;;;IAKH,eArCG,CAAA,GAAA,EAqCH,GArC4C;;;;;IA0C5C,YAxCG,CAAA,GAAA,EAwCH,EAxCgD,QAAkB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,EAAC,EAAC;;;;;;IA8C3F,WA5CG,CAAA,GAAA,EAAA,KAAA,EA4CH;QACI,uBA5CM,IAAA,IAAqB,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,CAAA,CAAC;QA6CnD,IAAI,CA5CC,QAAC,CAAQ,KAAC,CAAK,CAAC;KA6CtB;;;;;IAKH,QA/CG,CAAA,MAAA,EA+CH;QACI,IAAI,CA/CC,UAAC,GAAY,IAAA,CAAK;QAgDvB,IAAI,CA/CC,QAAC,CAAQ,IAAC,CAAI,MAAC,CAAM,CAAC;QAgD3B,OA/CO,KAAA,CAAM;KAgDd;;;;IAIH,OAjDG,GAiDH,EAjDoB,IAAA,CAAK,SAAC,EAAS,CAAE,EAAC;;;;;IAsDtC,SApDG,CAAA,KAoDH,GApDG,SAAA,EAoDH;QACI,IAAI,CApDC,IAAC,CAAI,KAAC,CAAK,KAAC,CAAK,CAAC;QAqDvB,IAAI,CApDC,UAAC,GAAY,KAAA,CAAM;KAyC1B;;;;;IAiBF,eAtDG,GAsDH;QAXI,IAAA,CA1CK,UAAC,CAAU,OAAC,CAAO,GAAC,IA0C7B;YACE,uBA1CU,OAAA,GAAe,IAAA,CAAK,IAAC,CAAI,GAAC,CAAG,GAAC,CAAG,IAAC,CAAI,CAAC;YAuD7C,IAAI,GAtDC,CAAG,QAAC,KAAY,OAAA,EAAS;gBAuD5B,cAAc,CAtDC,GAAC,CAAG,QAAC,EAAS,GAAA,CAAI,CAAC;gBAuDlC,IAAI,OAtDC;oBAAQ,YAAA,CAAa,OAAC,EAAQ,GAAA,CAAI,CAAC;gBAuDxC,GAAG,CAtDC,QAAC,GAAU,OAAA,CAAQ;aAuDxB;SACF,CAtDC,CAAC;QAwDH,IAAI,CAtDC,IAAC,CAAI,mBAAC,CAAmB,EAAC,SAAC,EAAU,KAAA,EAAM,CAAC,CAAC;KAuDnD;;;;IApDA,oBAAA,GAAH;QAyDI,IAAI,CAxDC,IAAC,CAAI,2BAAC,CAA2B,MAAM,IAAA,CAAK,eAAC,EAAe,CAAE,CAAC;QAyDpE,IAAI,IAxDC,CAAI,QAAC;YAAS,IAAA,CAAK,QAAC,CAAQ,2BAAC,CAA2B,MAAjE,GAAuE,CAAG,CAAC;QAyDvE,IAAI,CAxDC,QAAC,GAAU,IAAA,CAAK,IAAC,CAAI;KAyD3B;;;;IAtDA,iBAAA,GAAH;QA2DI,uBA1DM,IAAA,GAAO,iBAAA,CAAkB,IAAC,CAAI,WAAC,CAAW,CAAC;QA2DjD,IAAI,CA1DC,IAAC,CAAI,SAAC,GAAW,UAAA,CAAW,OAAC,CAAO,oBAAC,IAAC,CAAI,IAAC,CAAI,SAAC,uBAAY,IAAA,GAAO,CAAC,CAAC;QA4D1E,uBA1DM,KAAA,GAAQ,sBAAA,CAAuB,IAAC,CAAI,gBAAC,CAAgB,CAAC;QA2D5D,IAAI,CA1DC,IAAC,CAAI,cAAC,GAAgB,UAAA,CAAW,YAAC,CAAY,oBAAC,IAAC,CAAI,IAAC,CAAI,cAAC,uBAAiB,KAAA,GAAQ,CAAC,CAAC;KA2D3F;;;;IAxDA,iBAAA,GAAH;QA6DI,IAAI,CA5DC,IAAC,CAAI,IAAC,EAAK;YA6Dd,cAAc,CA5DC,oBAAC,EAAoB,CAAE;SA6DvC;KACF;;AA3DI,kBAAP,CAAA,UAAO,GAAoC;IA6D3C,EA5DE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA6DvB,QAAQ,EA5DE,aAAA;gBA6DV,SAAS,EA5DE,CAAAA,uBAAE,CAAqB;gBA6DlC,IAAI,EA5DE,EAAA,UAAE,EAAW,kBAAA,EAAoB,SAAA,EAAW,WAAA,EAAY;gBA6D9D,QAAQ,EA5DE,QAAA;aA6DX,EA5DC,EAAG;CA6DJ,CA5DC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA;IA+DD,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;CACjH,CAAC;AA7DK,kBAAP,CAAA,cAAO,GAAyD;IA+DhE,MAAM,EA9DE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,WAAE,EAAW,EAAG,EAAE;IA+DhD,UAAU,EA9DE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAO,EAAE;CA+D7B,CA9DC;AAiEF,AA0BA;;;;;;AAMA,SAAA,MAAA,CA9FC,IAAA,EAAA,EAAA,EA8FD;IACE,uBA1GM,KAAA,GAAQ,IAAA,CAAK,OAAC,CAAO,EAAC,CAAE,CAAC;IA2G/B,IAAI,KA1GC,GAAO,CAAA,CAAE,EAAE;QA2Gd,IAAI,CA1GC,MAAC,CAAM,KAAC,EAAM,CAAA,CAAE,CAAC;KA2GvB;CACF;;ADrSD;;;;;;;AASA,AAGA,AACA,AACA,AACA,AACA,AAGA,AAEA,AADO,MAAM,qBAAA,GAA6B;IAExC,OAAO,EADE,gBAAA;IAET,WAAA,EADa,UAAA,CAAW,MAAM,aAAA,CAAc;CAE7C,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CF,AAAA,MAAA,aACC,SAAA,0BAAA,CADD;;;;;;IAOA,WAAA,CACS,MAHQ,EAIR,UAHgB,EAIhB,eAHqB,EAA9B;QAII,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;KACzC;;;;;IAKH,gBALG,GAKH;QACI,IAAI,iBALC,CAAiB,IAAC,CAAI,OAAC,CAAO,EAAE;YAMnC,cAAc,CALC,oBAAC,EAAoB,CAAE;SAMvC;KACF;;AAJI,aAAP,CAAA,UAAO,GAAoC;IAM3C,EALE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,iBAAA,EAAmB,SAAA,EAAW,CAAA,qBAAE,CAAqB,EAAC,EAAC,EAAG;CAM9F,CALC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA;IAQD,EAAC,IAZC,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA;IAaF,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAZE,KAAA,EAAM,UAAA,EAAA,CAAA,EAAA,IAAA,EAAsB,QAAO,EAAE,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,EAAA;CAa3C,CAAC;AANK,aAAP,CAAA,cAAO,GAAyD;IAQhE,MAZE,EAKM,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,eAAE,EAAe,EAAG,EAAE;CAQnD,CAPC;AAUF,AAeA,AAlCO,MAAM,qBAAA,GAA6B;IAmCxC,OAAO,EAlCE,gBAAA;IAmCT,WAAW,EAlCE,UAAA,CAAW,MAAM,aAAA,CAAc;CAmC7C,CAlCC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFF,AAAA,MAAA,aApBC,SAAA,gBAAA,CAoBD;;;;;;IAoBA,WAAA,CACS,MA5BQ,EA6BR,UA5BgB,EA6BhB,eA5BqB,EAyB9B;QAII,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;KACzC;;;;IAIH,QA9BG,GA8BH;QACI,IAAI,CA9BC,gBAAC,EAAgB,CAAE;QAAA,EA+BxB,IAAI,CA9BC,aAAC,GAAe,YAAC,CAAY,IAAC,CAAI,CAAC;KA+BzC;;;;IAIH,WAhCG,GAgCH;QACI,IAAI,IAhCC,CAAI,aAAC,EAAc;YAiCtB,IAAI,CAhCC,aAAC,CAAa,eAAC,CAAe,IAAC,CAAI,CAAC;SAiC1C;KACF;;;;IAIH,IAlCG,OAAA,GAkCH,EAlC6B,OAAA,EAAO,IAAA,CAAK,aAAC,GAAe,YAAC,CAAY,IAAC,CAAI,CAAC,EAAC;;;;IAsC7E,IApCG,aAAA,GAoCH;QACI,OApCO,IAAA,CAAK,OAAC,IAA6B,IAAC,CAAI,OAAC,CAAO,aAAC,IAAe,IAAA,CAAK;KAqC7E;;;;IAIH,IAtCG,IAAA,GAsCH,EAtCyB,OAAO,WAAA,CAAY,IAAC,CAAI,IAAC,EAAK,IAAA,CAAK,OAAC,CAAO,CAAC,EAAC;;;;IA0CtE,IAxCG,SAAA,GAwCH,EAxCsC,OAAO,iBAAA,CAAkB,IAAC,CAAI,WAAC,CAAW,CAAC,EAAC;;;;IA4ClF,IA1CG,cAAA,GA0CH;QACI,OA1CO,sBAAA,CAAuB,IAAC,CAAI,gBAAC,CAAgB,CAAC;KA2CtD;;;;IAxCA,gBAAA,GAAH;QA6CI,IAAI,iBA5CC,CAAiB,IAAC,CAAI,OAAC,CAAO,EAAE;YA6CnC,cAAc,CA5CC,oBAAC,EAAoB,CAAE;SA6CvC;KACF;;AA3CI,aAAP,CAAA,UAAO,GAAoC;IA6C3C,EA5CE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,iBAAA,EAAmB,SAAA,EAAW,CAAA,qBAAE,CAAqB,EAAC,EAAC,EAAG;CA6C9F,CA5CC;;;;AAED,aAAD,CAAA,cAAC,GAAA,MAAA;IA+CD,EAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAG,EAAC;IAChG,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;CACjH,CAAC;AA7CK,aAAP,CAAA,cAAO,GAAyD;IA+ChE,MAAM,EA9CE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,eAAE,EAAe,EAAG,EAAE;CA+CnD,CA9CC;AAiDF,AA6BA;;;;AAIA,SAAA,iBAAA,CA/EC,MAAA,EA+ED;IACE,OAvGO,EAAE,MAAC,YAAiB,aAAA,CAAc,IAAI,EAAE,MAAC,YAAiB,kBAAA,CAAmB;QAwGhF,EAvGE,MAAC,YAAiB,aAAA,CAAc,CAAC;CAwGxC;;ADhTD;;;;;;;AASA,AAGA,AACA,AACA,AACA,AACA,AACA,AACA,AAGA,AACA,AAEA,AADO,MAAM,kBAAA,GAA0B;IAErC,OAAO,EADE,SAAA;IAET,WAAA,EADa,UAAA,CAAW,MAAM,eAAA,CAAgB;CAE/C,CADC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDF,AAAA,MAAA,eACC,SAAA,SAAA,CADD;;;;;;;IA2BA,WAAA,CACS,MAXQ,EAYR,UAXwC,EAYxC,eAVuC,EAYvC,cAXqC,EAM9C;QAMI,KAAK,EAAE,CAAC;QA/BF,IAAV,CAAA,MAAU,GAAS,KAAA,CAAM;QAatB,IAAH,CAAA,MAAS,GAHG,IAAI,YAAA,EAAa,CAAE;QAsB3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,GAAG,eAAe,IAAI,EAAE,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAChE;;;;;IAlBH,IANG,UAAA,CAAA,UAAA,EAMH,EANwC,cAAA,CAAe,mBAAC,EAAmB,CAAE,EAAC;;;;;IA6B9E,WAdG,CAAA,OAAA,EAcH;QACI,IAAI,CAdC,IAAC,CAAI,MAAC;YAAO,IAAA,CAAK,aAAC,EAAa,CAAE;QAevC,IAAI,iBAdC,CAAiB,OAAC,EAAQ,IAAA,CAAK,SAAC,CAAS,EAAE;YAe9C,IAAI,CAdC,SAAC,GAAW,IAAA,CAAK,KAAC,CAAK;YAe5B,IAAI,CAdC,aAAC,CAAa,WAAC,CAAW,IAAC,EAAK,IAAA,CAAK,KAAC,CAAK,CAAC;SAelD;KACF;;;;IAIH,WAhBG,GAgBH;QACI,IAAI,IAhBC,CAAI,aAAC,EAAc;YAiBtB,IAAI,CAhBC,aAAC,CAAa,aAAC,CAAa,IAAC,CAAI,CAAC;SAiBxC;KACF;;;;;IAKH,iBAnBG,CAAA,QAAA,EAmBH;QACI,IAAI,CAnBC,SAAC,GAAW,QAAA,CAAS;QAoB1B,IAAI,CAnBC,MAAC,CAAM,IAAC,CAAI,QAAC,CAAQ,CAAC;KAoB5B;;;;IAIH,IArBG,IAAA,GAqBH,EArByB,OAAO,WAAA,CAAY,IAAC,CAAI,IAAC,qBAAK,IAAA,CAAK,OAAC,GAAS,CAAC,EAAC;;;;IAyBxE,IAvBG,aAAA,GAuBH,EAvB6B,OAAO,IAAA,CAAK,OAAC,GAAS,IAAA,CAAK,OAAC,CAAO,aAAC,GAAe,IAAA,CAAK,EAAC;;;;IA2BtF,IAzBG,SAAA,GAyBH,EAzBsC,OAAO,iBAAA,CAAkB,IAAC,CAAI,cAAC,CAAc,CAAC,EAAC;;;;IA6BrF,IA3BG,cAAA,GA2BH;QACI,SA3BO,sBAAA,CAAuB,IAAC,CAAI,mBAAC,CAAmB,GAAG;KA4B3D;;;;IAIH,IA7BG,OAAA,GA6BH,EA7B+B,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;IAElD,gBAAA,GAAH;QAgCI,IAAI,EA/BE,IAAC,CAAI,OAAC,YAAkB,aAAA,CAAc;YAgCxC,IAAI,CA/BC,OAAC,YAAkB,0BAAA,EAA4B;YAgCtD,cAAc,CA/BC,qBAAC,EAAqB,CAAE;SAgB3C;aAfS,IAgCH,EA/BE,IAAC,CAAI,OAAC,YAAkB,aAAA,CAAc,IAAI,EAAE,IAAC,CAAI,OAAC,YAAkB,kBAAA,CAAmB;YAgCzF,EA/BE,IAAC,CAAI,OAAC,YAAkB,aAAA,CAAc,EAAE;YAgC5C,cAAc,CA/BC,sBAAC,EAAsB,CAAE;SAgCzC;KACF;;;;IA7BA,aAAA,GAAH;QAkCI,IAAI,CAjCC,gBAAC,EAAgB,CAAE;QAkCxB,IAAI,CAjCC,QAAC,GAAU,IAAA,CAAK,aAAC,CAAa,UAAC,CAAU,IAAC,CAAI,CAAC;QAkCpD,IAAI,IAjCC,CAAI,OAAC,CAAO,QAAC,IAAQ,EAAG,IAAA,CAAK,aAAC,GAAe,gBAAC,EAAiB;YAAA,EAAA,EAkClE,IAAI,CAjCC,aAAC,GAAe,gBAAC,GAAkB,IAAC,CAAI,CAAC;SAkC/C;QACD,IAAI,CAjCC,MAAC,GAAQ,IAAA,CAAK;KAkCpB;;AAhCI,eAAP,CAAA,UAAO,GAAoC;IAkC3C,EAjCE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,mBAAA,EAAqB,SAAA,EAAW,CAAA,kBAAE,CAAkB,EAAC,EAAC,EAAG;CAkC7F,CAjCC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IAoCD,EAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAG,EAAC;IAChG,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAG,EAAE,EAAG,EAAC;IAC5G,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,mBAAmB,EAAG,EAAE,EAAG,EAAC;IAClH,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,iBAAiB,EAAG,EAAE,EAAG,EAAC;CAC/G,CAAC;AAlCK,eAAP,CAAA,cAAO,GAAyD;IAoChE,MAAM,EAnCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,iBAAE,EAAiB,EAAG,EAAE;IAoCtD,OAAO,EAnCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,SAAE,EAAS,EAAG,EAAE;IAoC/C,QAAQ,EAnCE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,CAAA,eAAE,EAAe,EAAG,EAAE;IAoCvD,YAAY,EAnCE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,CAAA,UAAE,EAAU,EAAG,EAAE;CAoCpD,CAnCC,AAsCF,AA4BC;;AD/OD;;;;;;;AASA,AAGA,AAoCA,AADO,MAAM,kBAAA,GAA+B;IAE1C,OAAO,EADE,aAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,iBAAA,CAAkB;IAEhD,KAAC,EADM,IAAA;CAER,CADC;AAGF,AADO,MAAM,2BAAA,GAAwC;IAEnD,OAAO,EADE,aAAA;IAET,WAAO,EADM,UAAA,CAAW,MAAM,yBAAA,CAA0B;IAExD,KAAA,EADO,IAAA;CAER,CADC;;;;;;;;;;;;;AAcF,AAAA,MAAA,iBAAA,CAAA;;;;IAMA,IACG,QAAA,GADH,EACmC,OAAO,IAAA,CAAK,SAAC,CAAS,EAAC;;;;;IAI1D,IAFG,QAAA,CAAA,KAAA,EAEH;QACI,IAAI,CAFC,SAAC,GAAW,KAAA,IAAS,IAAA,IAAQ,KAAA,KAAU,KAAA,IAAS,CAEzD,EAFyD,KAAI,CAE7D,CAFkE,KAAM,OAAA,CAAQ;QAG5E,IAAI,IAFC,CAAI,SAAC;YAAU,IAAA,CAAK,SAAC,EAAS,CAAE;KAGtC;;;;;IAKH,QALG,CAAA,CAAA,EAKH;QACI,OALO,IAAA,CAAK,QAAC,GAAU,UAAA,CAAW,QAAC,CAAQ,CAAC,CAAC,GAAG,IAAA,CAAK;KAMtD;;;;;IAKH,yBARG,CAAA,EAAA,EAQH,EARoD,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;AAClE,iBAAP,CAAA,UAAO,GAAoC;IAS3C,EARE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBASvB,QAAQ,EACJ,wIAAwI;gBAC5I,SAAS,EARE,CAAA,kBAAE,CAAkB;gBAS/B,IAAI,EARE,EAAA,iBAAE,EAAkB,sBAAA,EAAuB;aASlD,EARC,EAAG;CASJ,CARC;;;;AAED,iBAAD,CAAA,cAAC,GAAA,MAAA,EAWA,CAAC;AATK,iBAAP,CAAA,cAAO,GAAyD;IAWhE,UAAU,EAVE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAW5B,CAVC;AAaF,AAgBA;;;;;;;;;;;;AAYA,AAAA,MAAA,yBAxBC,SAAA,iBAAA,CAwBD;;;;;IAKA,QA5BG,CAAA,CAAA,EA4BH;QACI,OA5BO,IAAA,CAAK,QAAC,GAAU,UAAA,CAAW,YAAC,CAAY,CAAC,CAAC,GAAG,IAAA,CAAK;KA6B1D;;AA3BI,yBAAP,CAAA,UAAO,GAAoC;IA6B3C,EA5BE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA6BvB,QAAQ,EACJ,qIAAqI;gBACzI,SAAS,EA5BE,CAAA,2BAAE,CAA2B;gBA6BxC,IAAI,EA5BE,EAAA,iBAAE,EAAkB,sBAAA,EAAuB;aA6BlD,EA5BC,EAAG;CA6BJ,CA5BC;;;;AAED,yBAAD,CAAA,cAAC,GAAA,MAAA,EA+BA,CAAC;AAGF,AAUA;;;AAGA,AAxCC,MAAA,eAAA,GAAA;IAyCC,OAAO,EAAE,aAAa;IACtB,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;IAC7C,KAAK,EAAE,IAAI;CACZ,CAAC;;;;;;;;;;;;;;;AAeF,AAAA,MAAA,cAAA,CAAA;;;;;IAOA,IAxCG,KAAA,CAAA,KAAA,EAwCH;QACI,IAAI,CAxCC,QAAC,GAAU,KAAA,KAAU,EAAA,IAAM,KAAA,KAAU,IAAA,IAAQ,KAAA,KAAU,MAAA,CAAO;QAyCnE,IAAI,IAxCC,CAAI,SAAC;YAAU,IAAA,CAAK,SAAC,EAAS,CAAE;KAyCtC;;;;;IAKH,QA3CG,CAAA,CAAA,EA2CH;QACI,OA3CO,IAAA,CAAK,QAAC,GAAU,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC,GAAG,IAAA,CAAK;KA4CnD;;;;;IAKH,yBA9CG,CAAA,EAAA,EA8CH,EA9CoD,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;AAClE,cAAP,CAAA,UAAO,GAAoC;IA+C3C,EA9CE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA+CvB,QAAQ,EA9CE,gEAAA;gBA+CV,SAAS,EA9CE,CAAA,eAAE,CAAe;aA+C7B,EA9CC,EAAG;CA+CJ,CA9CC;;;;AAED,cAAD,CAAA,cAAC,GAAA,MAAA,EAiDA,CAAC;AA/CK,cAAP,CAAA,cAAO,GAAyD;IAiDhE,OAAO,EAhDE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAiDzB,CAhDC;AAmDF,AA4BA;;;;;;;AAOA,AAhEC,MAAA,oBAAA,GAAA;IAiEC,OAAO,EAAE,aAAa;IACtB,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;IACjD,KAAK,EAAE,IAAI;CACZ,CAAC;;;;;;;AAOF,AAAA,MAAA,kBAAA,CAAA;;;;;IAUA,WAjEG,CAAA,OAAA,EAiEH;QACI,IAAI,WAjEC,IAAc,OAAA,EAAS;YAkE1B,IAAI,CAjEC,gBAAC,EAAgB,CAAE;YAkExB,IAAI,IAjEC,CAAI,SAAC;gBAAU,IAAA,CAAK,SAAC,EAAS,CAAE;SAkEtC;KACF;;;;;IAKH,QApEG,CAAA,CAAA,EAoEH;QACI,OApEO,IAAA,CAAK,SAAC,IAAY,IAAA,GAAO,IAAA,GAAO,IAAA,CAAK,UAAC,CAAU,CAAC,CAAC,CAAC;KAqE3D;;;;;IAKH,yBAvEG,CAAA,EAAA,EAuEH,EAvEoD,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;IAEtE,gBAAA,GAAH;QA0EI,IAAI,CAzEC,UAAC,GAAY,UAAA,CAAW,SAAC,CAAS,QAAC,CAAQ,IAAC,CAAI,SAAC,EAAU,EAAA,CAAG,CAAC,CAAC;KAgCvE;;AA9BK,kBAAP,CAAA,UAAO,GAAoC;IA0E3C,EAzEE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBA0EvB,QA1CK,EA/BK,4EAAA;gBA0EV,SA1CS,EA/BE,CAAA,oBAAE,CAAoB;gBA0EjC,IAAI,EAzEE,EAAA,kBAAE,EAAmB,8BAAA,EAA+B;aA0E3D,EAzEC,EAAG;CA0EJ,CAzEC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA,EA4EA,CAAC;AA1EK,kBAAP,CAAA,cAAO,GAAyD;IA4EhE,WAAW,EA3EE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CA4E7B,CA3EC;AA8EF,AAkBA;;;;;;;AAOA,AA7FC,MAAA,oBAAA,GAAA;IA8FC,OAAO,EAAE,aAAa;IACtB,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;IACjD,KAAK,EAAE,IAAI;CACZ,CAAC;;;;;;;;AAQF,AAAA,MAAA,kBAAA,CAAA;;;;;IAUA,WA9FG,CAAA,OAAA,EA8FH;QACI,IAAI,WA9FC,IAAc,OAAA,EAAS;YA+F1B,IAAI,CA9FC,gBAAC,EAAgB,CAAE;YA+FxB,IAAI,IA9FC,CAAI,SAAC;gBAAU,IAAA,CAAK,SAAC,EAAS,CAAE;SA+FtC;KACF;;;;;IAKH,QAjGG,CAAA,CAAA,EAiGH;QACI,OAjGO,IAAA,CAAK,SAAC,IAAY,IAAA,GAAO,IAAA,CAAK,UAAC,CAAU,CAAC,CAAC,GAAG,IAAA,CAAK;KAkG3D;;;;;IAKH,yBApGG,CAAA,EAAA,EAoGH,EApGoD,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;IAEtE,gBAAA,GAAH;QAuGI,IAAI,CAtGC,UAAC,GAAY,UAAA,CAAW,SAAC,CAAS,QAAC,CAAQ,IAAC,CAAI,SAAC,EAAU,EAAA,CAAG,CAAC,CAAC;KAuGtE;;AArGI,kBAAP,CAAA,UAAO,GAAoC;IAuG3C,EAtGE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAuGvB,QAAQ,EAtGE,4EAAA;gBAuGV,SAAS,EAtGE,CAAA,oBAAE,CAAoB;gBAuGjC,IAAI,EAtGE,EAAA,kBAAE,EAAmB,8BAAA,EAA+B;aAuG3D,EAtGC,EAAG;CAuGJ,CAtGC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA,EAyGA,CAAC;AAvGK,kBAAP,CAAA,cAAO,GAAyD;IAyGhE,WAAW,EAxGE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAyG7B,CAxGC;AA2GF,AAoBA,AArKO,MAAM,iBAAA,GAAyB;IAsKpC,OAAO,EArKE,aAAA;IAsKT,WAAW,EArKE,UAAA,CAAW,MAAM,gBAAA,CAAiB;IAsK/C,KAAK,EArKE,IAAA;CAsKR,CArKC;;;;;;;;;;;;;;AAmLF,AAAA,MAAA,gBAAA,CAAA;;;;;IAUA,WA3HG,CAAA,OAAA,EA2HH;QACI,IAAI,SA3HC,IAAY,OAAA,EAAS;YA4HxB,IAAI,CA3HC,gBAAC,EAAgB,CAAE;YA4HxB,IAAI,IA3HC,CAAI,SAAC;gBAAU,IAAA,CAAK,SAAC,EAAS,CAAE;SA4HtC;KACF;;;;;IAKH,QA9HG,CAAA,CAAA,EA8HH,EA9HwD,OAAO,IAAA,CAAK,UAAC,CAAU,CAAC,CAAC,CAAC,EAAC;;;;;IAmInF,yBAjIG,CAAA,EAAA,EAiIH,EAjIoD,IAAA,CAAK,SAAC,GAAW,EAAA,CAAG,EAAC;;;;IAEtE,gBAAA,GAAH,EAAqC,IAAA,CAAK,UAAC,GAAY,UAAA,CAAW,OAAC,CAAO,IAAC,CAAI,OAAC,CAAO,CAAC,EAAC;;AAClF,gBAAP,CAAA,UAAO,GAAoC;IAoI3C,EAnIE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAoIvB,QAAQ,EAnIE,sEAAA;gBAoIV,SAAS,EAnIE,CAAA,iBAAE,CAAiB;gBAoI9B,IAAI,EAnIE,EAAA,gBAAE,EAAiB,0BAAA,EAA2B;aAoIrD,EAnIC,EAAG;CAoIJ,CAnIC;;;;AAED,gBAAD,CAAA,cAAC,GAAA,MAAA,EAsIA,CAAC;AApIK,gBAAP,CAAA,cAAO,GAAyD;IAsIhE,SAAS,EArIE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CAsI3B,CArIC,AAwIF,AAgBC;;AD5gBD;;;;;;;AASA,AAGA,AACA;;;;;;;;;;;;;;;;;;;;AAoBA,AAAA,MAAA,WAAA,CAAA;;;;;;;;;;IAUA,KAFG,CAAA,cAAA,EAAA,KAEH,GAFG,IAAA,EAEH;QACI,uBAFM,QAAA,GAAW,IAAA,CAAK,eAAC,CAAe,cAAC,CAAc,CAAC;QAGtD,uBAFM,SAAA,GAAyB,KAAA,IAAS,IAAA,GAAO,KAAA,CAAM,WAAC,CAAW,GAAG,IAAA,CAAK;QAGzE,uBAFM,cAAA,GAAmC,KAAA,IAAS,IAAA,GAAO,KAAA,CAAM,gBAAC,CAAgB,GAAG,IAAA,CAAK;QAGxF,OAFO,IAAI,SAAA,CAAU,QAAC,EAAS,SAAA,EAAW,cAAA,CAAe,CAAC;KAG3D;;;;;;;;;;;;;IAaH,OANG,CAOG,SAAiB,EAAE,SAA0C,EAC7D,cAAyD,EAF/D;QAGI,OANO,IAAI,WAAA,CAAY,SAAC,EAAU,SAAA,EAAW,cAAA,CAAe,CAAC;KAO9D;;;;;;;;;IASH,KATG,CAUG,cAAqB,EAAE,SAA4B,EACnD,cAAsC,EAF5C;QAGI,uBATM,QAAA,GAAW,cAAA,CAAe,GAAC,CAAG,CAAC,IAAI,IAAA,CAAK,cAAC,CAAc,CAAC,CAAC,CAAC,CAAC;QAUjE,OATO,IAAI,SAAA,CAAU,QAAC,EAAS,SAAA,EAAW,cAAA,CAAe,CAAC;KAU3D;;;;;;IAMH,eAZG,CAAA,cAAA,EAYH;QACI,uBAZM,QAAA,GAA6C,EAAA,CAAG;QAatD,MAAM,CAZC,IAAC,CAAI,cAAC,CAAc,CAAC,OAAC,CAAO,WAAC,IAYzC;YACM,QAAQ,CAZC,WAAC,CAAW,GAAG,IAAA,CAAK,cAAC,CAAc,cAAC,CAAc,WAAC,CAAW,CAAC,CAAC;SAa1E,CAZC,CAAC;QAaH,OAZO,QAAA,CAAS;KAajB;;;;;;IAMH,cAfG,CAAA,aAAA,EAeH;QACI,IAAI,aAfC,YAAwB,WAAA,IAAe,aAAA,YAAyB,SAAA;YAgBjE,aAAa,YAfY,SAAA,EAAW;YAgBtC,OAfO,aAAA,CAAc;SAiBtB;aAfM,IAAA,KAAK,CAAK,OAAC,CAAO,aAAC,CAAa,EAAE;YAU3C,uBATU,KAAA,GAAQ,aAAA,CAAc,CAAC,CAAC,CAAC;YAgB/B,uBAfM,SAAA,GAAyB,aAAA,CAAc,MAAC,GAAQ,CAAA,GAAI,aAAA,CAAc,CAAC,CAAC,GAAG,IAAA,CAAK;YAgBlF,uBAfM,cAAA,GAAmC,aAAA,CAAc,MAAC,GAAQ,CAAA,GAAI,aAAA,CAAc,CAAC,CAAC,GAAG,IAAA,CAAK;YAgB5F,OAfO,IAAA,CAAK,OAAC,CAAO,KAAC,EAAM,SAAA,EAAW,cAAA,CAAe,CAAC;SAiBvD;aAfM;YAgBL,OAfO,IAAA,CAAK,OAAC,CAAO,aAAC,CAAa,CAAC;SAgBpC;KACF;;AAdI,WAAP,CAAA,UAAO,GAAoC;IAgB3C,EAfE,IAAA,EAAM,UAAA,EAAW;CAgBlB,CAfC;;;;AAED,WAAD,CAAA,cAAC,GAAA,MAAA,EAkBA,CAAC,AAGF,AAQC;;ADlID;;;;;;;;;;;;AAeA,AACA;;;AAGA,AADC,MAAA,OAAA,GAAA,IAAA,OAAA,CAAA,mBAAA,CAAA,CAAA;;ADlBD;;;;;;;AASA,AACA;;;;;;;;;;;;;AAaA,AAAA,MAAA,YAAA,CAAA;;AAEO,YAAP,CAAA,UAAO,GAAoC;IAA3C,EACE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAAvB,QAAQ,EACE,8CAAA;gBAAV,IAAI,EACE,EAAA,YAAE,EAAa,EAAA,EAAG;aAAzB,EACC,EAAG;CAAJ,CACC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA,EAEA,CANC,AASF,AAQC;;AD7CD;;;;;;;AASA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AADO,MAAM,sBAAA,GAAsC;IAEjD,YAAY;IACZ,cAAc;IACd,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,4BAA4B;IAC5B,0BAA0B;IAC1B,kCAA2B;IAC3B,yBAAiB;IACjB,eAAe;IACf,oBAAmB;IACnB,iBAAiB;IACjB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,yBAAe;IACf,cAAC;CACF,CADC;AAGF,AADO,MAAM,0BAAA,GAA0C,CAAA,OAAE,EAAQ,YAAA,EAAc,MAAA,CAAO,CAAC;AAGvF,AADO,MAAM,0BAAA,GAET,CAAC,oBADC,EAAqB,kBAAA,EAAoB,eAAA,EAAiB,aAAA,EAAe,aAAA,CAAc,CAAC;;;;AAK9F,AAAA,MAAA,yBAAA,CAAA;;AAEO,yBAAP,CAAA,UAAO,GAAoC;IAA3C,EACE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAAtB,YAAY,EACE,sBAAA;gBAAd,OAAO,EACE,sBAAA;aAAV,EACC,EAAG;CAAJ,CACC;;;;AAED,yBAAD,CAAA,cAAC,GAAA,MAAA,EAEA,CANC,AASF,AAQC;;ADjGD;;;;;;;AASA,AAEA,AACA,AACA,AACA;;;;AAIA,AAAA,MAAA,WAAA,CAAA;;AAGO,WAAP,CAAA,UAAO,GAAoC;IAD3C,EAEE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBADtB,YAAY,EAEE,0BAAA;gBADd,SAAS,EAEE,CAAA,oBAAE,CAAoB;gBADjC,OAAO,EAEE,CAAA,yBAAE,EAA0B,0BAAA,CAA2B;aADjE,EAEC,EAAG;CADJ,CAEC;;;;AAED,WAAD,CAAA,cAAC,GAAA,MAAA,EACA,CAAC;AAGF,AAUA;;;;AAIA,AAAA,MAAA,mBAAA,CAAA;;AARO,mBAAP,CAAA,UAAO,GAAoC;IAU3C,EATE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAUtB,YAAY,EATE,CAAA,0BAAE,CAA0B;gBAU1C,SAAS,EATE,CAAA,WAAE,EAAY,oBAAA,CAAqB;gBAU9C,OAAO,EATE,CAAA,yBAAE,EAA0B,0BAAA,CAA2B;aAUjE,EATC,EAAG;CAUJ,CATC;;;;AAED,mBAAD,CAAA,cAAC,GAAA,MAAA,EAYA,CAAC,AAGF,AAQC;;ADtED;;;;;;;;;;;;;;;;GAiBG,AAGH,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAAiE;;AD9CjE;;;;;;;;;;;;AAaA,AAAuyB;0EAE7tB;;ADf1E;;GAEG,AAEH,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAAuK;;"}