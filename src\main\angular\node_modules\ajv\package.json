{"_args": [["ajv@5.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ajv@5.2.0", "_id": "ajv@5.2.0", "_inBundle": false, "_integrity": "sha1-wXNQJMXaLvdcwZBxMHPUTwmL9IY=", "_location": "/ajv", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ajv@5.2.0", "name": "ajv", "escapedName": "ajv", "rawSpec": "5.2.0", "saveSpec": null, "fetchSpec": "5.2.0"}, "_requiredBy": ["/schema-utils"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ajv/-/ajv-5.2.0.tgz", "_spec": "5.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/ajv/issues"}, "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^0.1.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "description": "Another JSON Schema Validator", "devDependencies": {"ajv-async": "^0.1.0", "bluebird": "^3.1.5", "brfs": "^1.4.3", "browserify": "^14.1.0", "chai": "^4.0.1", "coveralls": "^2.11.4", "del-cli": "^0.2.1", "dot": "^1.0.3", "eslint": "^3.2.2", "gh-pages-generator": "^0.2.0", "glob": "^7.0.0", "if-node-version": "^1.0.0", "js-beautify": "^1.5.6", "jshint": "^2.9.4", "json-schema-test": "^1.3.0", "karma": "^1.0.0", "karma-chrome-launcher": "^2.0.0", "karma-mocha": "^1.1.1", "karma-phantomjs-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "mocha": "^3.0.0", "nodent": "^3.0.17", "nyc": "^11.0.2", "phantomjs-prebuilt": "^2.1.4", "pre-commit": "^1.1.1", "regenerator": "0.9.7", "require-globify": "^1.3.0", "typescript": "^2.0.3", "uglify-js": "^3.0.8", "watch": "^1.0.0"}, "files": ["lib/", "dist/", "scripts/", "LICENSE", ".tonic_example.js"], "homepage": "https://github.com/epoberezkin/ajv", "keywords": ["JSON", "schema", "validator", "validation", "jsonschema", "json-schema", "json-schema-validator", "json-schema-validation"], "license": "MIT", "main": "lib/ajv.js", "name": "ajv", "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv.git"}, "scripts": {"build": "del-cli lib/dotjs/*.js && node scripts/compile-dots.js", "bundle": "node ./scripts/bundle.js . Ajv pure_getters", "bundle-all": "del-cli dist && npm run bundle && npm run bundle-regenerator && npm run bundle-nodent", "bundle-beautify": "node ./scripts/bundle.js js-beautify", "bundle-nodent": "node ./scripts/bundle.js nodent", "bundle-regenerator": "node ./scripts/bundle.js regenerator", "eslint": "if-node-version \">=4\" eslint lib/*.js lib/compile/*.js spec scripts", "jshint": "jshint lib/*.js lib/**/*.js --exclude lib/dotjs/**/*", "prepublish": "npm run build && npm run bundle-all", "test": "npm run jshint && npm run eslint && npm run test-ts && npm run build && npm run test-cov && if-node-version 4 npm run test-browser", "test-browser": "del-cli .browser && npm run bundle-all && scripts/prepare-tests && npm run test-karma", "test-cov": "nyc npm run test-spec", "test-debug": "mocha spec/*.spec.js --debug-brk -R spec", "test-fast": "AJV_FAST_TEST=true npm run test-spec", "test-karma": "karma start --single-run --browsers PhantomJS", "test-spec": "mocha spec/*.spec.js -R spec $(if-node-version 7 echo --harmony-async-await)", "test-ts": "tsc --target ES5 --noImplicitAny lib/ajv.d.ts", "watch": "watch 'npm run build' ./lib/dot"}, "tonicExampleFilename": ".tonic_example.js", "typings": "lib/ajv.d.ts", "version": "5.2.0"}