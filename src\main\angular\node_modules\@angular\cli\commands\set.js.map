{"version": 3, "file": "set.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["commands/set.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,6CAA6C;AAC7C,6CAAsC;AAEtC,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAO3D,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAChC,IAAI,EAAE,KAAK;IACX,WAAW,EAAE,mCAAmC;IAChD,KAAK,EAAE,YAAY;IAEnB,gBAAgB,EAAE;QAChB;YACE,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,2EAA2E;SACzF;KACF;IAED,SAAS,EAAE,UAAU,GAAW;QAC9B,EAAE,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,OAAO,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,IAAI,WAAW,CAAC,2BAA2B,GAAG,GAAG,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IACD,QAAQ,EAAE,UAAU,GAAW;QAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,IAAI,WAAW,CAAC,0BAA0B,GAAG,GAAG,CAAC,CAAC;QAC1D,CAAC;QACD,MAAM,CAAC,CAAC,GAAG,CAAC;IACd,CAAC;IAED,GAAG,EAAE,UAAU,cAA0B,EAAE,OAAiB;QAC1D,MAAM,CAAC,IAAI,OAAO,CAAO,OAAO;YAC9B,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,GAAG,kBAAS,CAAC,UAAU,EAAE,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC;YACxF,EAAE,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC;gBACpB,MAAM,IAAI,WAAW,CAAC,4DAA4D;sBAC9E,iCAAiC,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC;YAEnC,EAAE,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC;gBAC3B,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC9C,EAAE,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC;oBAC3B,MAAM,IAAI,WAAW,CAAC,uBAAuB,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,KAAK,GAAQ,QAAQ,CAAC;YAC1B,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACb,KAAK,SAAS;oBAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAAC,KAAK,CAAC;gBACxD,KAAK,QAAQ;oBAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAAC,KAAK,CAAC;gBACtD,KAAK,QAAQ;oBAAE,KAAK,GAAG,QAAQ,CAAC;oBAAC,KAAK,CAAC;gBAEvC,SAAS,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC;YAED,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnC,qCAAqC;gBACrC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,cAAc,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5B,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACf,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAEH,6BAA6B,QAAgB,EAAE,MAAc;IACzD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;IAC7D,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,EAAE,CAAC,CAAC,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;IACjD,CAAC;IAED,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,EAAE,CAAC,CAAC,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;IACjD,CAAC;IACD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,qBAAO,CAAA;yCACK,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED,oBAAoB,QAAgB,EAAE,IAAY;IAChD,IAAI,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACf,MAAM,IAAI,WAAW,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED,kBAAe,UAAU,CAAC"}