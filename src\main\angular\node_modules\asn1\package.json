{"_args": [["asn1@0.2.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "asn1@0.2.3", "_id": "asn1@0.2.3", "_inBundle": false, "_integrity": "sha1-2sh4dxPJlmhJ/IGAd36+nB3fO4Y=", "_location": "/asn1", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "asn1@0.2.3", "name": "asn1", "escapedName": "asn1", "rawSpec": "0.2.3", "saveSpec": null, "fetchSpec": "0.2.3"}, "_requiredBy": ["/sshpk"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/asn1/-/asn1-0.2.3.tgz", "_spec": "0.2.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mcavage/node-asn1/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "Contains parsers and serializers for ASN.1 (currently BER only)", "devDependencies": {"tap": "0.4.8"}, "homepage": "https://github.com/mcavage/node-asn1#readme", "license": "MIT", "main": "lib/index.js", "name": "asn1", "repository": {"type": "git", "url": "git://github.com/mcavage/node-asn1.git"}, "scripts": {"test": "tap ./tst"}, "version": "0.2.3"}