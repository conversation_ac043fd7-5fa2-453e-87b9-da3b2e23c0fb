{"version": 3, "file": "change.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["lib/ast-tools/change.ts"], "names": [], "mappings": ";;AAAA,yBAA0B;AAC1B,uCAAwC;AAExC,MAAM,QAAQ,GAAI,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAsC,CAAC;AAC9E,MAAM,SAAS,GAAI,SAAS,CAAC,EAAE,CAAC,SAAS,CAAsC,CAAC;AAOnE,QAAA,QAAQ,GAAS;IAC5B,KAAK,EAAE,CAAC,IAAY,EAAE,OAAe,KAAK,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;IAC1E,IAAI,EAAE,CAAC,IAAY,KAAK,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;CAC/C,CAAC;AAmBF;;GAEG;AACH;IAAA;QACE,gBAAW,GAAG,eAAe,CAAC;QAC9B,UAAK,GAAG,QAAQ,CAAC;QACjB,SAAI,GAAW,IAAI,CAAC;IAEtB,CAAC;IADC,KAAK,KAAK,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;CACtC;AALD,gCAKC;AAED;;;;GAIG;AACH;IAIE,YAAY,GAAG,OAA8B;QAC3C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,EAAE,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,8BAA8B;QAC9B,EAAE,CAAC,CAAC,MAAM,YAAY,UAAU,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC;QACT,CAAC;QACD,4DAA4D;QAC5D,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;QAC3B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED,IAAI,WAAW;QACb,MAAM,CAAC,gBAAgB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/E,CAAC;IACD,+CAA+C;IAC/C,IAAI,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,IAAU;QACd,MAAM,CAAC,IAAI,CAAC,QAAQ;aACjB,IAAI,CAAC,CAAC,CAAS,EAAE,CAAS,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjD,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM;YACtB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1B,CAAC;CACF;AArCD,kCAqCC;AAGD;;GAEG;AACH;IAKE,YAAmB,IAAY,EAAU,GAAW,EAAU,KAAa;QAAxD,SAAI,GAAJ,IAAI,CAAQ;QAAU,QAAG,GAAH,GAAG,CAAQ;QAAU,UAAK,GAAL,KAAK,CAAQ;QACzE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,YAAY,KAAK,kBAAkB,GAAG,OAAO,IAAI,EAAE,CAAC;QACvE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAU;QACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO;YACtC,IAAI,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvBD,oCAuBC;AAED;;GAEG;AACH;IAKE,YAAmB,IAAY,EAAU,GAAW,EAAU,QAAgB;QAA3D,SAAI,GAAJ,IAAI,CAAQ;QAAU,QAAG,GAAH,GAAG,CAAQ;QAAU,aAAQ,GAAR,QAAQ,CAAQ;QAC5E,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,QAAQ,kBAAkB,GAAG,OAAO,IAAI,EAAE,CAAC;QACzE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,IAAU;QACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO;YACtC,IAAI,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChE,8DAA8D;YAC9D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AArBD,oCAqBC;AAED;;GAEG;AACH;IAIE,YAAmB,IAAY,EAAU,GAAW,EAAU,OAAe,EACzD,OAAe;QADhB,SAAI,GAAJ,IAAI,CAAQ;QAAU,QAAG,GAAH,GAAG,CAAQ;QAAU,YAAO,GAAP,OAAO,CAAQ;QACzD,YAAO,GAAP,OAAO,CAAQ;QACjC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,YAAY,OAAO,kBAAkB,GAAG,OAAO,IAAI,SAAS,OAAO,EAAE,CAAC;QACzF,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,IAAU;QACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjE,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEzE,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC1B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qBAAqB,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;YACvF,CAAC;YACD,6DAA6D;YAC7D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA1BD,sCA0BC"}