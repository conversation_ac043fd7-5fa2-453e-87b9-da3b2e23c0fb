{"_args": [["block-stream@0.0.9", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "block-stream@0.0.9", "_id": "block-stream@0.0.9", "_inBundle": false, "_integrity": "sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=", "_location": "/block-stream", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "block-stream@0.0.9", "name": "block-stream", "escapedName": "block-stream", "rawSpec": "0.0.9", "saveSpec": null, "fetchSpec": "0.0.9"}, "_requiredBy": ["/tar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/block-stream/-/block-stream-0.0.9.tgz", "_spec": "0.0.9", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/block-stream/issues"}, "dependencies": {"inherits": "~2.0.0"}, "description": "a stream of blocks", "devDependencies": {"tap": "^5.7.1"}, "engines": {"node": "0.4 || >=0.5.8"}, "files": ["block-stream.js"], "homepage": "https://github.com/isaacs/block-stream#readme", "license": "ISC", "main": "block-stream.js", "name": "block-stream", "repository": {"type": "git", "url": "git://github.com/isaacs/block-stream.git"}, "scripts": {"test": "tap test/*.js --cov"}, "version": "0.0.9"}