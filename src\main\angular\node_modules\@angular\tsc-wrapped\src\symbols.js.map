{"version": 3, "file": "symbols.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/symbols.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AAIjC;IAGE,iBAAoB,UAAyB;QAAzB,eAAU,GAAV,UAAU,CAAe;IAAG,CAAC;IAEjD,yBAAO,GAAP,UAAQ,IAAY,IAA6B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjF,wBAAM,GAAN,UAAO,IAAY,EAAE,KAAoB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7E,qBAAG,GAAH,UAAI,IAAY,IAAa,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE7D,sBAAY,4BAAO;aAAnB;YACE,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACZ,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAyB,CAAC;gBAC1D,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBACzB,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC;YACD,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAEO,8BAAY,GAApB;QAAA,iBA4EC;QA3EC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,iDAAiD;QACjD,IAAM,WAAW,GAAG,UAAC,CAAS,IAAK,OAAA,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,EAA7B,CAA6B,CAAC;QACjE,IAAM,KAAK,GAAG,UAAC,IAAa;YAC1B,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;oBACxC,IAAM,uBAAuB,GAA+B,IAAI,CAAC;oBACjE,EAAE,CAAC,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI;wBAC5C,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC;wBAC1C,IAAM,iBAAiB,GACS,uBAAuB,CAAC,eAAe,CAAC;wBACxE,yDAAyD;wBACzD,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;4BACzC,uEAAuE;4BACvE,2EAA2E;4BAC3E,8EAA8E;4BAC9E,mFAAmF;4BACnF,mBAAmB;4BACnB,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,iBAAiB,CAAC;4BACxD,iBAAiB,CAAC,MAAM,GAAG,KAAI,CAAC,UAAiB,CAAC;wBACpD,CAAC;wBACD,IAAM,MAAI,GAAG,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;wBACjE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,MAAI,EAAC,CAAC,CAAC;oBAC1F,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,OAAO,CAAC,GAAG,CACP,uBAAuB,CAAC,IAAI,CAAC,IAAI,EACjC,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAC,CAAC,CAAC;oBACnE,CAAC;oBACD,KAAK,CAAC;gBACR,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;oBAClC,IAAM,UAAU,GAAyB,IAAI,CAAC;oBAC9C,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;wBAC7B,iFAAiF;wBACjF,KAAK,CAAC;oBACR,CAAC;oBACD,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvC,uDAAuD;wBACvD,UAAU,CAAC,eAAe,CAAC,MAAM,GAAG,UAAU,CAAC;wBAC/C,UAAU,CAAC,MAAM,GAAG,KAAI,CAAC,UAAU,CAAC;oBACtC,CAAC;oBACD,IAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC/D,EAAE,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;wBACjC,sFAAsF;wBACtF,OAAO,CAAC,GAAG,CACP,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EACjC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;oBAC9D,CAAC;oBACD,IAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC;oBACvD,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACb,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;4BACtB,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;gCAC7B,gFAAgF;gCAChF,GAAG,CAAC,CAAkB,UAAoC,EAApC,KAAkB,QAAS,CAAC,QAAQ,EAApC,cAAoC,EAApC,IAAoC;oCAArD,IAAM,OAAO,SAAA;oCAChB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;wCAC7B,UAAU,EAAE,WAAW;wCACvB,MAAM,EAAE,IAAI;wCACZ,IAAI,EAAE,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI;qCAC3E,CAAC,CAAC;iCACJ;gCACD,KAAK,CAAC;4BACR,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;gCAChC,+DAA+D;gCAC/D,OAAO,CAAC,GAAG,CACc,QAAS,CAAC,IAAI,CAAC,IAAI,EACxC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;gCAC7C,KAAK,CAAC;wBACV,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC;YACV,CAAC;YACD,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC;QACF,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACpB,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IACH,cAAC;AAAD,CAAC,AAlGD,IAkGC;AAlGY,0BAAO;AAoGpB,0BAA0B,OAAmC;IAC3D,0CAA0C;IAC1C,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM;QAC9F,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa;QAC9F,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY;QACrF,mBAAmB,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc;QAC7F,cAAc,CAAC;SACX,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,MAAA,EAAC,CAAC,EAAlD,CAAkD,CAAC,CAAC;AAC3E,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\nimport {MetadataValue} from './schema';\n\nexport class Symbols {\n  private _symbols: Map<string, MetadataValue>;\n\n  constructor(private sourceFile: ts.SourceFile) {}\n\n  resolve(name: string): MetadataValue|undefined { return this.symbols.get(name); }\n\n  define(name: string, value: MetadataValue) { this.symbols.set(name, value); }\n\n  has(name: string): boolean { return this.symbols.has(name); }\n\n  private get symbols(): Map<string, MetadataValue> {\n    let result = this._symbols;\n    if (!result) {\n      result = this._symbols = new Map<string, MetadataValue>();\n      populateBuiltins(result);\n      this.buildImports();\n    }\n    return result;\n  }\n\n  private buildImports(): void {\n    const symbols = this._symbols;\n    // Collect the imported symbols into this.symbols\n    const stripQuotes = (s: string) => s.replace(/^['\"]|['\"]$/g, '');\n    const visit = (node: ts.Node) => {\n      switch (node.kind) {\n        case ts.SyntaxKind.ImportEqualsDeclaration:\n          const importEqualsDeclaration = <ts.ImportEqualsDeclaration>node;\n          if (importEqualsDeclaration.moduleReference.kind ===\n              ts.SyntaxKind.ExternalModuleReference) {\n            const externalReference =\n                <ts.ExternalModuleReference>importEqualsDeclaration.moduleReference;\n            // An `import <identifier> = require(<module-specifier>);\n            if (!externalReference.expression.parent) {\n              // The `parent` field of a node is set by the TypeScript binder (run as\n              // part of the type checker). Setting it here allows us to call `getText()`\n              // even if the `SourceFile` was not type checked (which looks for `SourceFile`\n              // in the parent chain). This doesn't damage the node as the binder unconditionally\n              // sets the parent.\n              externalReference.expression.parent = externalReference;\n              externalReference.parent = this.sourceFile as any;\n            }\n            const from = stripQuotes(externalReference.expression.getText());\n            symbols.set(importEqualsDeclaration.name.text, {__symbolic: 'reference', module: from});\n          } else {\n            symbols.set(\n                importEqualsDeclaration.name.text,\n                {__symbolic: 'error', message: `Unsupported import syntax`});\n          }\n          break;\n        case ts.SyntaxKind.ImportDeclaration:\n          const importDecl = <ts.ImportDeclaration>node;\n          if (!importDecl.importClause) {\n            // An `import <module-specifier>` clause which does not bring symbols into scope.\n            break;\n          }\n          if (!importDecl.moduleSpecifier.parent) {\n            // See note above in the `ImportEqualDeclaration` case.\n            importDecl.moduleSpecifier.parent = importDecl;\n            importDecl.parent = this.sourceFile;\n          }\n          const from = stripQuotes(importDecl.moduleSpecifier.getText());\n          if (importDecl.importClause.name) {\n            // An `import <identifier> form <module-specifier>` clause. Record the defualt symbol.\n            symbols.set(\n                importDecl.importClause.name.text,\n                {__symbolic: 'reference', module: from, default: true});\n          }\n          const bindings = importDecl.importClause.namedBindings;\n          if (bindings) {\n            switch (bindings.kind) {\n              case ts.SyntaxKind.NamedImports:\n                // An `import { [<identifier> [, <identifier>] } from <module-specifier>` clause\n                for (const binding of (<ts.NamedImports>bindings).elements) {\n                  symbols.set(binding.name.text, {\n                    __symbolic: 'reference',\n                    module: from,\n                    name: binding.propertyName ? binding.propertyName.text : binding.name.text\n                  });\n                }\n                break;\n              case ts.SyntaxKind.NamespaceImport:\n                // An `input * as <identifier> from <module-specifier>` clause.\n                symbols.set(\n                    (<ts.NamespaceImport>bindings).name.text,\n                    {__symbolic: 'reference', module: from});\n                break;\n            }\n          }\n          break;\n      }\n      ts.forEachChild(node, visit);\n    };\n    if (this.sourceFile) {\n      ts.forEachChild(this.sourceFile, visit);\n    }\n  }\n}\n\nfunction populateBuiltins(symbols: Map<string, MetadataValue>) {\n  // From lib.core.d.ts (all \"define const\")\n  ['Object', 'Function', 'String', 'Number', 'Array', 'Boolean', 'Map', 'NaN', 'Infinity', 'Math',\n   'Date', 'RegExp', 'Error', 'Error', 'EvalError', 'RangeError', 'ReferenceError', 'SyntaxError',\n   'TypeError', 'URIError', 'JSON', 'ArrayBuffer', 'DataView', 'Int8Array', 'Uint8Array',\n   'Uint8ClampedArray', 'Uint16Array', 'Int16Array', 'Int32Array', 'Uint32Array', 'Float32Array',\n   'Float64Array']\n      .forEach(name => symbols.set(name, {__symbolic: 'reference', name}));\n}\n"]}