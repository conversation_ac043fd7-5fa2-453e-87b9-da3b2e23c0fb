[{"__symbolic": "module", "version": 3, "metadata": {"digest": {"__symbolic": "function", "parameters": ["message"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "id"}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sha1"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "serializeNodes"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "nodes"}]}, "member": "join"}, "arguments": [""]}, "right": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": "[", "right": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "meaning"}}, "right": "]"}}]}}}, "decimalDigest": {"__symbolic": "function"}, "serializeNodes": {"__symbolic": "function", "parameters": ["nodes"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 64, "character": 19}}, "sha1": {"__symbolic": "function"}, "fingerprint": {"__symbolic": "function"}, "computeMsgId": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"digest": {"__symbolic": "function", "parameters": ["message"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "id"}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sha1"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "serializeNodes"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "nodes"}]}, "member": "join"}, "arguments": [""]}, "right": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": "[", "right": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "message"}, "member": "meaning"}}, "right": "]"}}]}}}, "decimalDigest": {"__symbolic": "function"}, "serializeNodes": {"__symbolic": "function", "parameters": ["nodes"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 64, "character": 19}}, "sha1": {"__symbolic": "function"}, "fingerprint": {"__symbolic": "function"}, "computeMsgId": {"__symbolic": "function"}}}]