[{"__symbolic": "module", "version": 3, "metadata": {}, "exports": [{"from": "./version", "export": ["VERSION"]}, {"from": "./template_parser/template_ast"}, {"from": "./template_parser/template_parser", "export": ["TEMPLATE_TRANSFORMS"]}, {"from": "./config", "export": ["CompilerConfig"]}, {"from": "./compile_metadata"}, {"from": "./aot/compiler_factory"}, {"from": "./aot/compiler"}, {"from": "./aot/generated_file"}, {"from": "./aot/compiler_options"}, {"from": "./aot/compiler_host"}, {"from": "./aot/static_reflector"}, {"from": "./aot/static_symbol"}, {"from": "./aot/static_symbol_resolver"}, {"from": "./aot/summary_resolver"}, {"from": "./ast_path"}, {"from": "./summary_resolver"}, {"from": "./jit/compiler", "export": ["JitCompiler"]}, {"from": "./jit/compiler_factory"}, {"from": "./jit/jit_reflector"}, {"from": "./compile_reflector"}, {"from": "./url_resolver"}, {"from": "./resource_loader"}, {"from": "./directive_resolver", "export": ["DirectiveResolver"]}, {"from": "./pipe_resolver", "export": ["PipeResolver"]}, {"from": "./ng_module_resolver", "export": ["NgModuleResolver"]}, {"from": "./ml_parser/interpolation_config", "export": ["DEFAULT_INTERPOLATION_CONFIG", "InterpolationConfig"]}, {"from": "./schema/element_schema_registry"}, {"from": "./i18n/index"}, {"from": "./directive_normalizer"}, {"from": "./expression_parser/ast"}, {"from": "./expression_parser/lexer"}, {"from": "./expression_parser/parser"}, {"from": "./metadata_resolver"}, {"from": "./ml_parser/ast"}, {"from": "./ml_parser/html_parser"}, {"from": "./ml_parser/html_tags"}, {"from": "./ml_parser/interpolation_config"}, {"from": "./ml_parser/tags"}, {"from": "./ng_module_compiler", "export": ["NgModuleCompiler"]}, {"from": "./output/output_ast", "export": ["AssertNotNull", "BinaryOperator", "BinaryOperatorExpr", "BuiltinMethod", "BuiltinVar", "CastExpr", "ClassStmt", "CommaExpr", "CommentStmt", "ConditionalExpr", "DeclareFunctionStmt", "DeclareVarStmt", "ExpressionStatement", "ExpressionVisitor", "ExternalExpr", "ExternalReference", "FunctionExpr", "IfStmt", "InstantiateExpr", "InvokeFunctionExpr", "InvokeMethodExpr", "LiteralArrayExpr", "LiteralExpr", "LiteralMapExpr", "NotExpr", "ReadKeyExpr", "ReadPropExpr", "ReadVarExpr", "ReturnStatement", "StatementVisitor", "ThrowStmt", "TryCatchStmt", "WriteKeyExpr", "WritePropExpr", "WriteVarExpr", "StmtModifier", "Statement"]}, {"from": "./output/abstract_emitter", "export": ["EmitterVisitorContext"]}, {"from": "./output/ts_emitter"}, {"from": "./parse_util"}, {"from": "./schema/dom_element_schema_registry"}, {"from": "./selector"}, {"from": "./style_compiler"}, {"from": "./template_parser/template_parser"}, {"from": "./view_compiler/view_compiler", "export": ["ViewCompiler"]}, {"from": "./util", "export": ["isSyntaxError", "syntaxError"]}]}, {"__symbolic": "module", "version": 1, "metadata": {}, "exports": [{"from": "./version", "export": ["VERSION"]}, {"from": "./template_parser/template_ast"}, {"from": "./template_parser/template_parser", "export": ["TEMPLATE_TRANSFORMS"]}, {"from": "./config", "export": ["CompilerConfig"]}, {"from": "./compile_metadata"}, {"from": "./aot/compiler_factory"}, {"from": "./aot/compiler"}, {"from": "./aot/generated_file"}, {"from": "./aot/compiler_options"}, {"from": "./aot/compiler_host"}, {"from": "./aot/static_reflector"}, {"from": "./aot/static_symbol"}, {"from": "./aot/static_symbol_resolver"}, {"from": "./aot/summary_resolver"}, {"from": "./ast_path"}, {"from": "./summary_resolver"}, {"from": "./jit/compiler", "export": ["JitCompiler"]}, {"from": "./jit/compiler_factory"}, {"from": "./jit/jit_reflector"}, {"from": "./compile_reflector"}, {"from": "./url_resolver"}, {"from": "./resource_loader"}, {"from": "./directive_resolver", "export": ["DirectiveResolver"]}, {"from": "./pipe_resolver", "export": ["PipeResolver"]}, {"from": "./ng_module_resolver", "export": ["NgModuleResolver"]}, {"from": "./ml_parser/interpolation_config", "export": ["DEFAULT_INTERPOLATION_CONFIG", "InterpolationConfig"]}, {"from": "./schema/element_schema_registry"}, {"from": "./i18n/index"}, {"from": "./directive_normalizer"}, {"from": "./expression_parser/ast"}, {"from": "./expression_parser/lexer"}, {"from": "./expression_parser/parser"}, {"from": "./metadata_resolver"}, {"from": "./ml_parser/ast"}, {"from": "./ml_parser/html_parser"}, {"from": "./ml_parser/html_tags"}, {"from": "./ml_parser/interpolation_config"}, {"from": "./ml_parser/tags"}, {"from": "./ng_module_compiler", "export": ["NgModuleCompiler"]}, {"from": "./output/output_ast", "export": ["AssertNotNull", "BinaryOperator", "BinaryOperatorExpr", "BuiltinMethod", "BuiltinVar", "CastExpr", "ClassStmt", "CommaExpr", "CommentStmt", "ConditionalExpr", "DeclareFunctionStmt", "DeclareVarStmt", "ExpressionStatement", "ExpressionVisitor", "ExternalExpr", "ExternalReference", "FunctionExpr", "IfStmt", "InstantiateExpr", "InvokeFunctionExpr", "InvokeMethodExpr", "LiteralArrayExpr", "LiteralExpr", "LiteralMapExpr", "NotExpr", "ReadKeyExpr", "ReadPropExpr", "ReadVarExpr", "ReturnStatement", "StatementVisitor", "ThrowStmt", "TryCatchStmt", "WriteKeyExpr", "WritePropExpr", "WriteVarExpr", "StmtModifier", "Statement"]}, {"from": "./output/abstract_emitter", "export": ["EmitterVisitorContext"]}, {"from": "./output/ts_emitter"}, {"from": "./parse_util"}, {"from": "./schema/dom_element_schema_registry"}, {"from": "./selector"}, {"from": "./style_compiler"}, {"from": "./template_parser/template_parser"}, {"from": "./view_compiler/view_compiler", "export": ["ViewCompiler"]}, {"from": "./util", "export": ["isSyntaxError", "syntaxError"]}]}]