[{"__symbolic": "module", "version": 3, "metadata": {"ExtractorHost": {"__symbolic": "interface"}, "Extractor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "../aot/static_symbol_resolver", "name": "StaticSymbolResolver"}, {"__symbolic": "reference", "module": "./message_bundle", "name": "MessageBundle"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}]}], "extract": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ExtractorHost": {"__symbolic": "interface"}, "Extractor": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "../aot/static_symbol_resolver", "name": "StaticSymbolResolver"}, {"__symbolic": "reference", "module": "./message_bundle", "name": "MessageBundle"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}]}], "extract": [{"__symbolic": "method"}]}}}}]