[{"__symbolic": "module", "version": 3, "metadata": {"TypeModifier": {"Const": 0}, "Type": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 17, "character": 32, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}], "hasModifier": [{"__symbolic": "method"}]}}, "BuiltinTypeName": {"Dynamic": 0, "Bool": 1, "String": 2, "Int": 3, "Number": 4, "Function": 5, "Inferred": 6}, "BuiltinType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 27, "context": {"typeName": "BuiltinTypeName"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 55, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "ExpressionType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 47, "character": 51, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "ArrayType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 55, "character": 43, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "MapType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 64, "character": 57, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "DYNAMIC_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Dynamic"}]}, "INFERRED_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Inferred"}]}, "BOOL_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Bool"}]}, "INT_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Int"}]}, "NUMBER_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Number"}]}, "STRING_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "String"}]}, "FUNCTION_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Function"}]}, "TypeVisitor": {"__symbolic": "interface"}, "BinaryOperator": {"Equals": 0, "NotEquals": 1, "Identical": 2, "NotIdentical": 3, "Minus": 4, "Plus": 5, "Divide": 6, "Multiply": 7, "Modulo": 8, "And": 9, "Or": 10, "Lower": 11, "LowerEquals": 12, "Bigger": 13, "BiggerEquals": 14}, "Expression": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "prop": [{"__symbolic": "method"}], "key": [{"__symbolic": "method"}], "callMethod": [{"__symbolic": "method"}], "callFn": [{"__symbolic": "method"}], "instantiate": [{"__symbolic": "method"}], "conditional": [{"__symbolic": "method"}], "equals": [{"__symbolic": "method"}], "notEquals": [{"__symbolic": "method"}], "identical": [{"__symbolic": "method"}], "notIdentical": [{"__symbolic": "method"}], "minus": [{"__symbolic": "method"}], "plus": [{"__symbolic": "method"}], "divide": [{"__symbolic": "method"}], "multiply": [{"__symbolic": "method"}], "modulo": [{"__symbolic": "method"}], "and": [{"__symbolic": "method"}], "or": [{"__symbolic": "method"}], "lower": [{"__symbolic": "method"}], "lowerEquals": [{"__symbolic": "method"}], "bigger": [{"__symbolic": "method"}], "biggerEquals": [{"__symbolic": "method"}], "isBlank": [{"__symbolic": "method"}], "cast": [{"__symbolic": "method"}], "toStmt": [{"__symbolic": "method"}]}}, "BuiltinVar": {"This": 0, "Super": 1, "CatchError": 2, "CatchStack": 3}, "ReadVarExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 214, "character": 27, "context": {"typeName": "BuiltinVar"}}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "WriteVarExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "toDeclStmt": [{"__symbolic": "method"}]}}, "WriteKeyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "WritePropExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "BuiltinMethod": {"ConcatArray": 0, "SubscribeObservable": 1, "Bind": 2}, "InvokeMethodExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 292, "character": 50, "context": {"typeName": "BuiltinMethod"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "InvokeFunctionExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "InstantiateExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "LiteralExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExternalExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ExternalReference"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Type"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExternalReference": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "ConditionalExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "NotExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "AssertNotNull": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "CastExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "FnParam": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}]}]}}, "FunctionExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "toDeclStmt": [{"__symbolic": "method"}]}}, "BinaryOperatorExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 425, "character": 23, "context": {"typeName": "BinaryOperator"}}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ReadPropExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "ReadKeyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "LiteralArrayExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "LiteralMapEntry": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "boolean"}]}]}}, "LiteralMapExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "LiteralMapEntry"}]}, {"__symbolic": "reference", "name": "MapType"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "CommaExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExpressionVisitor": {"__symbolic": "interface"}, "THIS_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "This"}, null, null]}, "SUPER_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "Super"}, null, null]}, "CATCH_ERROR_VAR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "CatchError"}, null, null]}, "CATCH_STACK_VAR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "CatchStack"}, null, null]}, "NULL_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [null, null, null]}, "TYPED_NULL_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [null, {"__symbolic": "reference", "name": "INFERRED_TYPE"}, null]}, "StmtModifier": {"Final": 0, "Private": 1, "Exported": 2}, "Statement": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 544, "character": 26, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}], "hasModifier": [{"__symbolic": "method"}]}}, "DeclareVarStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 559, "character": 17, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "DeclareFunctionStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 573, "character": 35, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ExpressionStatement": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ReturnStatement": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "AbstractClassPart": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 605, "character": 59, "context": {"typeName": "StmtModifier"}}]}]}], "hasModifier": [{"__symbolic": "method"}]}}, "ClassField": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 615, "character": 64, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassMethod": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 624, "character": 35, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassGetter": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 633, "character": 17, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassField"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassGetter"}]}, {"__symbolic": "reference", "name": "ClassMethod"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassMethod"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 643, "character": 48, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "IfStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "CommentStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "TryCatchStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ThrowStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "StatementVisitor": {"__symbolic": "interface"}, "AstTransformer": {"__symbolic": "class", "members": {"transformExpr": [{"__symbolic": "method"}], "transformStmt": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "RecursiveAstVisitor": {"__symbolic": "class", "members": {"visitReadVarExpr": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "findReadVarNames": {"__symbolic": "function"}, "applySourceSpanToStatementIfNeeded": {"__symbolic": "function"}, "applySourceSpanToExpressionIfNeeded": {"__symbolic": "function"}, "variable": {"__symbolic": "function", "parameters": ["name", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "reference", "name": "name"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "importExpr": {"__symbolic": "function", "parameters": ["id", "typeParams", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ExternalExpr"}, "arguments": [{"__symbolic": "reference", "name": "id"}, null, {"__symbolic": "reference", "name": "typeParams"}, {"__symbolic": "reference", "name": "sourceSpan"}]}, "defaults": [null, null, null]}, "importType": {"__symbolic": "function", "parameters": ["id", "typeParams", "typeModifiers"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "reference", "name": "id"}, "right": null}, "thenExpression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "expressionType"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "importExpr"}, "arguments": [{"__symbolic": "reference", "name": "id"}, {"__symbolic": "reference", "name": "typeParams"}, null]}, {"__symbolic": "reference", "name": "typeModifiers"}]}, "elseExpression": null}, "defaults": [null, null, null]}, "expressionType": {"__symbolic": "function", "parameters": ["expr", "typeModifiers"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "reference", "name": "expr"}, "right": null}, "thenExpression": {"__symbolic": "error", "message": "Expression form not supported", "line": 1143, "character": 24}, "elseExpression": null}, "defaults": [null, null]}, "literalArr": {"__symbolic": "function", "parameters": ["values", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralArrayExpr"}, "arguments": [{"__symbolic": "reference", "name": "values"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "literalMap": {"__symbolic": "function", "parameters": ["values", "type", "quoted"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 1156, "character": 17}, "defaults": [null, null, false]}, "not": {"__symbolic": "function", "parameters": ["expr", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "NotExpr"}, "arguments": [{"__symbolic": "reference", "name": "expr"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "assertNotNull": {"__symbolic": "function", "parameters": ["expr", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "AssertNotNull"}, "arguments": [{"__symbolic": "reference", "name": "expr"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "fn": {"__symbolic": "function", "parameters": ["params", "body", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "FunctionExpr"}, "arguments": [{"__symbolic": "reference", "name": "params"}, {"__symbolic": "reference", "name": "body"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "literal": {"__symbolic": "function", "parameters": ["value", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [{"__symbolic": "reference", "name": "value"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TypeModifier": {"Const": 0}, "Type": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 17, "character": 32, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}], "hasModifier": [{"__symbolic": "method"}]}}, "BuiltinTypeName": {"Dynamic": 0, "Bool": 1, "String": 2, "Int": 3, "Number": 4, "Function": 5, "Inferred": 6}, "BuiltinType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 27, "context": {"typeName": "BuiltinTypeName"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 55, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "ExpressionType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 47, "character": 51, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "ArrayType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 55, "character": 43, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "MapType": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Type"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 64, "character": 57, "context": {"typeName": "TypeModifier"}}]}]}], "visitType": [{"__symbolic": "method"}]}}, "DYNAMIC_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Dynamic"}]}, "INFERRED_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Inferred"}]}, "BOOL_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Bool"}]}, "INT_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Int"}]}, "NUMBER_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Number"}]}, "STRING_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "String"}]}, "FUNCTION_TYPE": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "BuiltinType"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinTypeName"}, "member": "Function"}]}, "TypeVisitor": {"__symbolic": "interface"}, "BinaryOperator": {"Equals": 0, "NotEquals": 1, "Identical": 2, "NotIdentical": 3, "Minus": 4, "Plus": 5, "Divide": 6, "Multiply": 7, "Modulo": 8, "And": 9, "Or": 10, "Lower": 11, "LowerEquals": 12, "Bigger": 13, "BiggerEquals": 14}, "Expression": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "prop": [{"__symbolic": "method"}], "key": [{"__symbolic": "method"}], "callMethod": [{"__symbolic": "method"}], "callFn": [{"__symbolic": "method"}], "instantiate": [{"__symbolic": "method"}], "conditional": [{"__symbolic": "method"}], "equals": [{"__symbolic": "method"}], "notEquals": [{"__symbolic": "method"}], "identical": [{"__symbolic": "method"}], "notIdentical": [{"__symbolic": "method"}], "minus": [{"__symbolic": "method"}], "plus": [{"__symbolic": "method"}], "divide": [{"__symbolic": "method"}], "multiply": [{"__symbolic": "method"}], "modulo": [{"__symbolic": "method"}], "and": [{"__symbolic": "method"}], "or": [{"__symbolic": "method"}], "lower": [{"__symbolic": "method"}], "lowerEquals": [{"__symbolic": "method"}], "bigger": [{"__symbolic": "method"}], "biggerEquals": [{"__symbolic": "method"}], "isBlank": [{"__symbolic": "method"}], "cast": [{"__symbolic": "method"}], "toStmt": [{"__symbolic": "method"}]}}, "BuiltinVar": {"This": 0, "Super": 1, "CatchError": 2, "CatchStack": 3}, "ReadVarExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 214, "character": 27, "context": {"typeName": "BuiltinVar"}}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "WriteVarExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "toDeclStmt": [{"__symbolic": "method"}]}}, "WriteKeyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "WritePropExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "BuiltinMethod": {"ConcatArray": 0, "SubscribeObservable": 1, "Bind": 2}, "InvokeMethodExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 292, "character": 50, "context": {"typeName": "BuiltinMethod"}}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "InvokeFunctionExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "InstantiateExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "LiteralExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExternalExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ExternalReference"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Type"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExternalReference": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}]}}, "ConditionalExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "NotExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "AssertNotNull": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "CastExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "FnParam": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}]}]}}, "FunctionExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "toDeclStmt": [{"__symbolic": "method"}]}}, "BinaryOperatorExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 425, "character": 23, "context": {"typeName": "BinaryOperator"}}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ReadPropExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "ReadKeyExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}]}}, "LiteralArrayExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "LiteralMapEntry": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "boolean"}]}]}}, "LiteralMapExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "LiteralMapEntry"}]}, {"__symbolic": "reference", "name": "MapType"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "CommaExpr": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Expression"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Expression"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitExpression": [{"__symbolic": "method"}]}}, "ExpressionVisitor": {"__symbolic": "interface"}, "THIS_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "This"}, null, null]}, "SUPER_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "Super"}, null, null]}, "CATCH_ERROR_VAR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "CatchError"}, null, null]}, "CATCH_STACK_VAR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "BuiltinVar"}, "member": "CatchStack"}, null, null]}, "NULL_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [null, null, null]}, "TYPED_NULL_EXPR": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [null, {"__symbolic": "reference", "name": "INFERRED_TYPE"}, null]}, "StmtModifier": {"Final": 0, "Private": 1, "Exported": 2}, "Statement": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 544, "character": 26, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}], "hasModifier": [{"__symbolic": "method"}]}}, "DeclareVarStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 559, "character": 17, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "DeclareFunctionStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 573, "character": 35, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ExpressionStatement": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ReturnStatement": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "AbstractClassPart": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 605, "character": 59, "context": {"typeName": "StmtModifier"}}]}]}], "hasModifier": [{"__symbolic": "method"}]}}, "ClassField": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 615, "character": 64, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassMethod": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "FnParam"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 624, "character": 35, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassGetter": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "AbstractClassPart"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Type"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 633, "character": 17, "context": {"typeName": "StmtModifier"}}]}]}]}}, "ClassStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassField"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassGetter"}]}, {"__symbolic": "reference", "name": "ClassMethod"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ClassMethod"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 643, "character": 48, "context": {"typeName": "StmtModifier"}}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "IfStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "CommentStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "TryCatchStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "Statement"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "ThrowStmt": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "Statement"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Expression"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visitStatement": [{"__symbolic": "method"}]}}, "StatementVisitor": {"__symbolic": "interface"}, "AstTransformer": {"__symbolic": "class", "members": {"transformExpr": [{"__symbolic": "method"}], "transformStmt": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "RecursiveAstVisitor": {"__symbolic": "class", "members": {"visitReadVarExpr": [{"__symbolic": "method"}], "visitWriteVarExpr": [{"__symbolic": "method"}], "visitWriteKeyExpr": [{"__symbolic": "method"}], "visitWritePropExpr": [{"__symbolic": "method"}], "visitInvokeMethodExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitInstantiateExpr": [{"__symbolic": "method"}], "visitLiteralExpr": [{"__symbolic": "method"}], "visitExternalExpr": [{"__symbolic": "method"}], "visitConditionalExpr": [{"__symbolic": "method"}], "visitNotExpr": [{"__symbolic": "method"}], "visitAssertNotNullExpr": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitBinaryOperatorExpr": [{"__symbolic": "method"}], "visitReadPropExpr": [{"__symbolic": "method"}], "visitReadKeyExpr": [{"__symbolic": "method"}], "visitLiteralArrayExpr": [{"__symbolic": "method"}], "visitLiteralMapExpr": [{"__symbolic": "method"}], "visitCommaExpr": [{"__symbolic": "method"}], "visitAllExpressions": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitExpressionStmt": [{"__symbolic": "method"}], "visitReturnStmt": [{"__symbolic": "method"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "visitIfStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "visitThrowStmt": [{"__symbolic": "method"}], "visitCommentStmt": [{"__symbolic": "method"}], "visitAllStatements": [{"__symbolic": "method"}]}}, "findReadVarNames": {"__symbolic": "function"}, "applySourceSpanToStatementIfNeeded": {"__symbolic": "function"}, "applySourceSpanToExpressionIfNeeded": {"__symbolic": "function"}, "variable": {"__symbolic": "function", "parameters": ["name", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ReadVarExpr"}, "arguments": [{"__symbolic": "reference", "name": "name"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "importExpr": {"__symbolic": "function", "parameters": ["id", "typeParams", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ExternalExpr"}, "arguments": [{"__symbolic": "reference", "name": "id"}, null, {"__symbolic": "reference", "name": "typeParams"}, {"__symbolic": "reference", "name": "sourceSpan"}]}, "defaults": [null, null, null]}, "importType": {"__symbolic": "function", "parameters": ["id", "typeParams", "typeModifiers"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "reference", "name": "id"}, "right": null}, "thenExpression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "expressionType"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "importExpr"}, "arguments": [{"__symbolic": "reference", "name": "id"}, {"__symbolic": "reference", "name": "typeParams"}, null]}, {"__symbolic": "reference", "name": "typeModifiers"}]}, "elseExpression": null}, "defaults": [null, null, null]}, "expressionType": {"__symbolic": "function", "parameters": ["expr", "typeModifiers"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "reference", "name": "expr"}, "right": null}, "thenExpression": {"__symbolic": "error", "message": "Expression form not supported", "line": 1143, "character": 24}, "elseExpression": null}, "defaults": [null, null]}, "literalArr": {"__symbolic": "function", "parameters": ["values", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralArrayExpr"}, "arguments": [{"__symbolic": "reference", "name": "values"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "literalMap": {"__symbolic": "function", "parameters": ["values", "type", "quoted"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 1156, "character": 17}, "defaults": [null, null, false]}, "not": {"__symbolic": "function", "parameters": ["expr", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "NotExpr"}, "arguments": [{"__symbolic": "reference", "name": "expr"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "assertNotNull": {"__symbolic": "function", "parameters": ["expr", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "AssertNotNull"}, "arguments": [{"__symbolic": "reference", "name": "expr"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "fn": {"__symbolic": "function", "parameters": ["params", "body", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "FunctionExpr"}, "arguments": [{"__symbolic": "reference", "name": "params"}, {"__symbolic": "reference", "name": "body"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}, "literal": {"__symbolic": "function", "parameters": ["value", "type", "sourceSpan"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiteralExpr"}, "arguments": [{"__symbolic": "reference", "name": "value"}, {"__symbolic": "reference", "name": "type"}, {"__symbolic": "reference", "name": "sourceSpan"}]}}}}]