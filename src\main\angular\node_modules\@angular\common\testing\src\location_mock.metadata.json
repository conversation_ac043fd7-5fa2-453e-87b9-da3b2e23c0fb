[{"__symbolic": "module", "version": 3, "metadata": {"SpyLocation": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"setInitialPath": [{"__symbolic": "method"}], "setBaseHref": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "isCurrentPathEqualTo": [{"__symbolic": "method"}], "simulateUrlPop": [{"__symbolic": "method"}], "simulateHashChange": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "go": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "subscribe": [{"__symbolic": "method"}], "normalize": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"SpyLocation": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"setInitialPath": [{"__symbolic": "method"}], "setBaseHref": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "isCurrentPathEqualTo": [{"__symbolic": "method"}], "simulateUrlPop": [{"__symbolic": "method"}], "simulateHashChange": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "go": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "subscribe": [{"__symbolic": "method"}], "normalize": [{"__symbolic": "method"}]}}}}]