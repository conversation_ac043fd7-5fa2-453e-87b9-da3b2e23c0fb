"use strict";
var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var config_1 = require("../config");
var abstractResolver_1 = require("./abstractResolver");
var path_1 = require("path");
var syntaxKind_1 = require("../../util/syntaxKind");
var kinds = syntaxKind_1.current();
var UrlResolver = (function (_super) {
    __extends(UrlResolver, _super);
    function UrlResolver(pathResolver) {
        var _this = _super.call(this) || this;
        _this.pathResolver = pathResolver;
        return _this;
    }
    UrlResolver.prototype.resolve = function (d) {
        var _this = this;
        var templateUrl = this.getTemplateUrl(d);
        var styleUrls = this.getStyleUrls(d);
        var targetPath = this.getProgramFilePath(d);
        if (targetPath) {
            var componentPath_1 = path_1.dirname(targetPath);
            return {
                templateUrl: config_1.Config.resolveUrl(this.pathResolver.resolve(templateUrl, componentPath_1), d),
                styleUrls: styleUrls.map(function (p) {
                    return config_1.Config.resolveUrl(_this.pathResolver.resolve(p, componentPath_1), d);
                })
            };
        }
        else {
            return {
                templateUrl: config_1.Config.resolveUrl(null, d),
                styleUrls: []
            };
        }
    };
    UrlResolver.prototype.getProgramFilePath = function (d) {
        var current = d;
        while (current) {
            if (current.kind === kinds.SourceFile) {
                return current.path || current.fileName;
            }
            current = current.parent;
        }
        return undefined;
    };
    return UrlResolver;
}(abstractResolver_1.AbstractResolver));
exports.UrlResolver = UrlResolver;
