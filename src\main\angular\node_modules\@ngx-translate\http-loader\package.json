{"_args": [["@ngx-translate/http-loader@0.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@ngx-translate/http-loader@0.0.3", "_id": "@ngx-translate/http-loader@0.0.3", "_inBundle": false, "_integrity": "sha1-g0bI0tb2MCVGAQKWaPF6vir+ips=", "_location": "/@ngx-translate/http-loader", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@ngx-translate/http-loader@0.0.3", "name": "@ngx-translate/http-loader", "escapedName": "@ngx-translate%2fhttp-loader", "scope": "@ngx-translate", "rawSpec": "0.0.3", "saveSpec": null, "fetchSpec": "0.0.3"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@ngx-translate/http-loader/-/http-loader-0.0.3.tgz", "_spec": "0.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ngx-translate/http-loader/issues"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "description": "A loader for ngx-translate that loads translations using http", "devDependencies": {"@angular/common": "2.4.7", "@angular/compiler": "2.4.7", "@angular/compiler-cli": "2.4.7", "@angular/core": "2.4.7", "@angular/http": "2.4.7", "@angular/platform-browser": "2.4.7", "@angular/platform-browser-dynamic": "2.4.7", "@angular/platform-server": "2.4.7", "@ngx-translate/core": "6.0.0", "@types/hammerjs": "2.0.34", "@types/jasmine": "2.5.41", "@types/node": "7.0.4", "awesome-typescript-loader": "3.0.0-beta.18", "clean-webpack-plugin": "0.1.15", "codelyzer": "2.0.0-beta.4", "commitizen": "2.9.5", "core-js": "2.4.1", "cz-conventional-changelog": "1.2.0", "istanbul-instrumenter-loader": "0.2.0", "jasmine-core": "2.5.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-coverage": "1.1.1", "karma-jasmine": "1.1.0", "karma-mocha-reporter": "2.2.2", "karma-remap-coverage": "0.1.2", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "2.0.2", "loader-utils": "0.2.16", "reflect-metadata": "0.1.9", "rimraf": "2.5.4", "rxjs": "5.1.1", "semantic-release": "6.3.5", "source-map-loader": "0.1.6", "ts-helpers": "1.1.2", "tslint": "4.4.2", "tslint-loader": "3.4.2", "typescript": "2.0.10", "webpack": "2.2.1", "zone.js": "0.7.7"}, "homepage": "https://github.com/ngx-translate/http-loader#readme", "keywords": ["angular", "angular 2", "i18n", "translate", "ngx-translate"], "license": "MIT", "main": "bundles/http-loader.umd.js", "module": "index.js", "name": "@ngx-translate/http-loader", "peerDependencies": {"@ngx-translate/core": "^6.0.0", "@angular/http": ">=2.0.0 || >=4.0.0-beta.0"}, "repository": {"type": "git", "url": "git+https://github.com/ngx-translate/http-loader.git"}, "scripts": {"build": "webpack", "clean": "rimraf bundles coverage src/**/*.d.ts src/**/*.metadata.json src/**/*.js tests/**/*.d.ts tests/**/*.metadata.json tests/**/*.js index.d.ts index.metadata.json index.js", "prepublish": "ngc && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "test": "karma start", "test-watch": "karma start --singleRun=false --autoWatch=true"}, "typings": "index.d.ts", "version": "0.0.3"}