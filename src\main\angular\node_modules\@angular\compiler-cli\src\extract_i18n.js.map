{"version": 3, "file": "extract_i18n.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/extract_i18n.ts"], "names": [], "mappings": ";;;AAcA,4BAA0B;AAE1B,0CAA4C;AAG5C,yCAAsC;AAEtC,iBACI,SAAqC,EAAE,UAAwC,EAC/E,OAAmB,EAAE,IAAqB;IAC5C,MAAM,CAAC,qBAAS,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC;SAC/D,OAAO,CAAC,UAAU,CAAC,UAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,cAAc;AACd,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC;IAC9C,IAAM,UAAU,GAAG,IAAI,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAC1D,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC;SACjD,IAAI,CAAC,UAAC,QAAa,IAAK,OAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAtB,CAAsB,CAAC;SAC/C,KAAK,CAAC,UAAC,CAAM;QACZ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["#!/usr/bin/env node\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n/**\n * Extract i18n messages from source code\n */\n// Must be imported first, because Angular decorators throw on load.\nimport 'reflect-metadata';\n\nimport * as tsc from '@angular/tsc-wrapped';\nimport * as ts from 'typescript';\n\nimport {Extractor} from './extractor';\n\nfunction extract(\n    ngOptions: tsc.AngularCompilerOptions, cliOptions: tsc.I18nExtractionCliOptions,\n    program: ts.Program, host: ts.CompilerHost) {\n  return Extractor.create(ngOptions, program, host, cliOptions.locale)\n      .extract(cliOptions.i18nFormat !, cliOptions.outFile);\n}\n\n// Entry point\nif (require.main === module) {\n  const args = require('minimist')(process.argv.slice(2));\n  const project = args.p || args.project || '.';\n  const cliOptions = new tsc.I18nExtractionCliOptions(args);\n  tsc.main(project, cliOptions, extract, {noEmit: true})\n      .then((exitCode: any) => process.exit(exitCode))\n      .catch((e: any) => {\n        console.error(e.stack);\n        console.error('Extraction failed');\n        process.exit(1);\n      });\n}\n"]}