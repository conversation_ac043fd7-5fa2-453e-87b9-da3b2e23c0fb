{"title": "Alternate stylesheet", "description": "Ability to support alternative styles to a HTML page.", "spec": "https://html.spec.whatwg.org/multipage/semantics.html#link-type-stylesheet", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/Alternative_style_sheets#Specifications", "title": "Mozilla Developer Network (MDN) documentation - alternate stylesheet"}, {"url": "https://www.w3.org/Style/Examples/007/alternatives.en.html", "title": "W3C CSS tips & tricks article"}, {"url": "https://chrome.google.com/webstore/detail/style-chooser/daodklicmmjhcacgkjpianadkdkbkbce", "title": "Third party Chrome extension"}, {"url": "https://css-tricks.com/examples/AlternateStyleSheets/", "title": "Demo page"}], "bugs": [], "categories": ["HTML5", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "y", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "u", "16": "u", "17": "u", "18": "u", "19": "u", "20": "u", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "u", "27": "u", "28": "u", "29": "u", "30": "u", "31": "u", "32": "u", "33": "u", "34": "u", "35": "u", "36": "u", "37": "u", "38": "u", "39": "u", "40": "u", "41": "u", "42": "u", "43": "u", "44": "u", "45": "u", "46": "u", "47": "u", "48": "u"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "u"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "u", "37": "u"}, "and_chr": {"59": "u"}, "and_ff": {"54": "u"}, "ie_mob": {"10": "u", "11": "u"}, "and_uc": {"11.4": "u"}, "samsung": {"4": "u", "5": "u"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "u"}}, "notes": "", "notes_by_num": {}, "usage_perc_y": 10.05, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "alternate stylesheet,alternative stylesheet,alternate css,alternative css,alternate style,alternative style", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": false}