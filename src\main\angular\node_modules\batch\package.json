{"_args": [["batch@0.6.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "batch@0.6.1", "_id": "batch@0.6.1", "_inBundle": false, "_integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "_location": "/batch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "batch@0.6.1", "name": "batch", "escapedName": "batch", "rawSpec": "0.6.1", "saveSpec": null, "fetchSpec": "0.6.1"}, "_requiredBy": ["/serve-index"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/batch/-/batch-0.6.1.tgz", "_spec": "0.6.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"emitter": "events"}, "bugs": {"url": "https://github.com/visionmedia/batch/issues"}, "description": "Simple async batch with concurrency control and progress reporting.", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/batch#readme", "license": "MIT", "main": "index", "name": "batch", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/batch.git"}, "version": "0.6.1"}