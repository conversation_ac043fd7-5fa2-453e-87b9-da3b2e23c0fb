{"version": 3, "file": "index_writer.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/index_writer.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAIH,IAAM,YAAY,GAAG,qDAGpB,CAAC;AAIF,+BAAsC,KAAa,EAAE,QAA8B;IACjF,IAAM,OAAO,GAAa,CAAC,YAAY,CAAC,CAAC;IAEzC,mCAAmC;IACnC,OAAO,CAAC,IAAI,CAAC,oBAAkB,KAAK,OAAI,EAAE,EAAE,CAAC,CAAC;IAE9C,uBAAuB;IACvB,IAAM,OAAO,GAAG,IAAI,GAAG,EAAgC,CAAC;IAExD,GAAG,CAAC,CAAgB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ;QAAvB,IAAM,KAAK,iBAAA;QACd,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACrB;IAGD,IAAM,cAAc,GAAG,OAAO,CAAC,UAAC,CAAqB,IAAK,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;IAClE,IAAM,cAAc,GAAG,OAAO,CAAC,UAAC,CAAW,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,EAAJ,CAAI,CAAC,CAAC;IACtD,IAAM,cAAc,GAChB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;SACd,GAAG,CAAC,UAAC,EAAiB;YAAhB,cAAM,EAAE,eAAO;QAAM,OAAU,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAAhD,CAAgD,CAAC;SAC5E,IAAI,CAAC,cAAc,CAAC,CAAC;IAE9B,GAAG,CAAC,CAA4B,UAAc,EAAd,iCAAc,EAAd,4BAAc,EAAd,IAAc;QAAnC,IAAA,yBAAiB,EAAhB,gBAAM,EAAE,eAAO;QACzB,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAG,CAAC,CAAC,IAAI,YAAO,CAAC,CAAC,WAAa,EAA/B,CAA+B,CAAC,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,aAAW,OAAO,gBAAW,QAAM,OAAI,CAAC,CAAC;KACvD;IAED,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAhCD,sDAgCC;AAED,iBAAuB,MAAmB;IACxC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC;QACV,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,IAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BundlePrivateEntry} from './bundler';\n\nconst INDEX_HEADER = `/**\n * Generated bundle index. Do not edit.\n */\n`;\n\ntype MapEntry = [string, BundlePrivateEntry[]];\n\nexport function privateEntriesToIndex(index: string, privates: BundlePrivateEntry[]): string {\n  const results: string[] = [INDEX_HEADER];\n\n  // Export all of the index symbols.\n  results.push(`export * from '${index}';`, '');\n\n  // Simplify the exports\n  const exports = new Map<string, BundlePrivateEntry[]>();\n\n  for (const entry of privates) {\n    let entries = exports.get(entry.module);\n    if (!entries) {\n      entries = [];\n      exports.set(entry.module, entries);\n    }\n    entries.push(entry);\n  }\n\n\n  const compareEntries = compare((e: BundlePrivateEntry) => e.name);\n  const compareModules = compare((e: MapEntry) => e[0]);\n  const orderedExports =\n      Array.from(exports)\n          .map(([module, entries]) => <MapEntry>[module, entries.sort(compareEntries)])\n          .sort(compareModules);\n\n  for (const [module, entries] of orderedExports) {\n    let symbols = entries.map(e => `${e.name} as ${e.privateName}`);\n    results.push(`export {${symbols}} from '${module}';`);\n  }\n\n  return results.join('\\n');\n}\n\nfunction compare<E, T>(select: (e: E) => T): (a: E, b: E) => number {\n  return (a, b) => {\n    const ak = select(a);\n    const bk = select(b);\n    return ak > bk ? 1 : ak < bk ? -1 : 0;\n  };\n}"]}