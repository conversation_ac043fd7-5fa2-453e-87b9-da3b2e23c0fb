{"_args": [["caniuse-api@1.6.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "caniuse-api@1.6.1", "_id": "caniuse-api@1.6.1", "_inBundle": false, "_integrity": "sha1-tTTnxzTE+B7F++isoq0kNUuWLGw=", "_location": "/caniuse-api", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "caniuse-api@1.6.1", "name": "caniuse-api", "escapedName": "caniuse-api", "rawSpec": "1.6.1", "saveSpec": null, "fetchSpec": "1.6.1"}, "_requiredBy": ["/postcss-merge-rules"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/caniuse-api/-/caniuse-api-1.6.1.tgz", "_spec": "1.6.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "authors": ["n<PERSON><PERSON>", "MoOx"], "babel": {"presets": ["babel-preset-latest"]}, "bugs": {"url": "https://github.com/nyalab/caniuse-api/issues"}, "dependencies": {"browserslist": "^1.3.6", "caniuse-db": "^1.0.30000529", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}, "description": "request the caniuse data to check browsers compatibilities", "devDependencies": {"babel-cli": "^6.22.2", "babel-eslint": "^5.0.0", "babel-preset-latest": "^6.22.0", "babel-tape-runner": "^2.0.1", "jshint": "^2.5.10", "npmpub": "^3.1.0", "tap-spec": "^4.1.1", "tape": "^4.6.0"}, "files": ["dist"], "homepage": "https://github.com/nyalab/caniuse-api#readme", "keywords": ["caniuse", "browserslist"], "license": "MIT", "main": "dist/index.js", "name": "caniuse-api", "repository": {"type": "git", "url": "git+https://github.com/nyalab/caniuse-api.git"}, "scripts": {"build": "babel src --out-dir dist", "lint": "jshint src", "prepublish": "npm run build", "release": "npmpub", "test": "npm run lint && babel-tape-runner test/*.js | tap-spec"}, "version": "1.6.1"}