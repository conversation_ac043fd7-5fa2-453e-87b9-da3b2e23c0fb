{"_args": [["process-nextick-args@2.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "process-nextick-args@2.0.1", "_id": "process-nextick-args@2.0.1", "_inBundle": false, "_integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "_location": "/bl/process-nextick-args", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "process-nextick-args@2.0.1", "name": "process-nextick-args", "escapedName": "process-nextick-args", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/bl/readable-stream"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": "", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "description": "process.nextTick but always with args", "devDependencies": {"tap": "~0.2.6"}, "files": ["index.js"], "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "license": "MIT", "main": "index.js", "name": "process-nextick-args", "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "scripts": {"test": "node test.js"}, "version": "2.0.1"}