{"_args": [["@ngx-translate/core@6.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@ngx-translate/core@6.0.1", "_id": "@ngx-translate/core@6.0.1", "_inBundle": false, "_integrity": "sha1-fHqAB3/rmU/IFbZ6cgZa8E05Tv4=", "_location": "/@ngx-translate/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@ngx-translate/core@6.0.1", "name": "@ngx-translate/core", "escapedName": "@ngx-translate%2fcore", "scope": "@ngx-translate", "rawSpec": "6.0.1", "saveSpec": null, "fetchSpec": "6.0.1"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@ngx-translate/core/-/core-6.0.1.tgz", "_spec": "6.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ngx-translate/core/issues"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "description": "The internationalization (i18n) library for Angular 2+", "devDependencies": {"@angular/animations": "4.0.0-rc.6", "@angular/common": "4.0.0-rc.6", "@angular/compiler": "4.0.0-rc.6", "@angular/compiler-cli": "4.0.0-rc.6", "@angular/core": "4.0.0-rc.6", "@angular/platform-browser": "4.0.0-rc.6", "@angular/platform-browser-dynamic": "4.0.0-rc.6", "@angular/platform-server": "4.0.0-rc.6", "@angular/router": "4.0.0-rc.6", "@types/hammerjs": "2.0.34", "@types/jasmine": "2.5.46", "@types/node": "7.0.10", "awesome-typescript-loader": "3.1.2", "clean-webpack-plugin": "0.1.16", "codelyzer": "3.0.0-beta.4", "commitizen": "2.9.6", "core-js": "2.4.1", "cz-conventional-changelog": "2.0.0", "istanbul-instrumenter-loader": "0.2.0", "jasmine-core": "2.5.2", "karma": "1.5.0", "karma-chrome-launcher": "2.0.0", "karma-coverage": "1.1.1", "karma-jasmine": "1.1.0", "karma-mocha-reporter": "2.2.3", "karma-remap-coverage": "0.1.4", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "2.0.3", "loader-utils": "1.1.0", "reflect-metadata": "0.1.10", "rimraf": "2.6.1", "rxjs": "5.2.0", "semantic-release": "6.3.5", "source-map-loader": "0.2.0", "ts-helpers": "1.1.2", "tslint": "4.5.1", "tslint-loader": "3.4.3", "typescript": "2.2.1", "webpack": "2.3.1", "zone.js": "0.8.5"}, "homepage": "https://github.com/ocombe/ngx-translate", "keywords": ["angular", "angular 2", "translate", "i18n"], "license": "MIT", "main": "bundles/core.umd.js", "module": "index.js", "name": "@ngx-translate/core", "peerDependencies": {"@angular/core": ">=2.0.0 || >=4.0.0-beta.0"}, "repository": {"type": "git", "url": "git+https://github.com/ngx-translate/core.git"}, "scripts": {"build": "webpack", "clean": "rimraf bundles coverage src/**/*.d.ts src/**/*.metadata.json src/**/*.js tests/**/*.d.ts tests/**/*.metadata.json tests/**/*.js index.d.ts index.metadata.json index.js", "commit": "npm run prepublish && npm test && git-cz", "prepublish": "ngc && npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "test": "karma start", "test-watch": "karma start --singleRun=false --autoWatch=true"}, "typings": "index.d.ts", "version": "6.0.1"}