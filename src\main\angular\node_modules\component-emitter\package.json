{"_args": [["component-emitter@1.1.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "component-emitter@1.1.2", "_id": "component-emitter@1.1.2", "_inBundle": false, "_integrity": "sha1-KWWU8nU9qmOZbSrwjRWpURbJrsM=", "_location": "/component-emitter", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "component-emitter@1.1.2", "name": "component-emitter", "escapedName": "component-emitter", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/socket.io-parser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/component-emitter/-/component-emitter-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/component/emitter/issues"}, "component": {"scripts": {"emitter/index.js": "index.js"}}, "description": "Event emitter", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/component/emitter#readme", "main": "index.js", "name": "component-emitter", "repository": {"type": "git", "url": "git+https://github.com/component/emitter.git"}, "scripts": {"test": "make test"}, "version": "1.1.2"}