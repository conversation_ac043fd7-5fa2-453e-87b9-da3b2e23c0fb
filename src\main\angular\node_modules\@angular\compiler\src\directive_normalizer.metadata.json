[{"__symbolic": "module", "version": 3, "metadata": {"PrenormalizedTemplateMetadata": {"__symbolic": "interface"}, "DirectiveNormalizer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./resource_loader", "name": "Resource<PERSON><PERSON>der"}, {"__symbolic": "reference", "module": "./url_resolver", "name": "UrlResolver"}, {"__symbolic": "reference", "module": "./ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "module": "./config", "name": "CompilerConfig"}]}], "clearCache": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "_fetch": [{"__symbolic": "method"}], "normalizeTemplate": [{"__symbolic": "method"}], "normalizeTemplateOnly": [{"__symbolic": "method"}], "normalizeLoadedTemplate": [{"__symbolic": "method"}], "normalizeExternalStylesheets": [{"__symbolic": "method"}], "_loadMissingExternalStylesheets": [{"__symbolic": "method"}], "normalizeStylesheet": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"PrenormalizedTemplateMetadata": {"__symbolic": "interface"}, "DirectiveNormalizer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./resource_loader", "name": "Resource<PERSON><PERSON>der"}, {"__symbolic": "reference", "module": "./url_resolver", "name": "UrlResolver"}, {"__symbolic": "reference", "module": "./ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "module": "./config", "name": "CompilerConfig"}]}], "clearCache": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "_fetch": [{"__symbolic": "method"}], "normalizeTemplate": [{"__symbolic": "method"}], "normalizeTemplateOnly": [{"__symbolic": "method"}], "normalizeLoadedTemplate": [{"__symbolic": "method"}], "normalizeExternalStylesheets": [{"__symbolic": "method"}], "_loadMissingExternalStylesheets": [{"__symbolic": "method"}], "normalizeStylesheet": [{"__symbolic": "method"}]}}}}]