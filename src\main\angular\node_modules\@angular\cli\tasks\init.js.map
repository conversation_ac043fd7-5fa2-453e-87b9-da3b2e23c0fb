{"version": 3, "file": "init.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["tasks/init.ts"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,gDAAwC;AACxC,sDAA8C;AAC9C,8EAAyE;AACzE,8EAAmE;AACnE,6CAA2C;AAE3C,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACrD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,kBAAkB,GAAG,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC5F,MAAM,OAAO,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC7C,MAAM,gBAAgB,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AAG7E,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,cAAmB,EAAE,OAAiB;QACnD,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1B,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC;YAC5C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;QAEH,wDAAwD;QACxD,mDAAmD;QACnD,IAAI,OAAY,CAAC;QACjB,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC;YACrC,OAAO,GAAG,IAAI,OAAO,CAAC;gBACpB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,cAAc,GAAG,kBAAS,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAEpE,IAAI,UAAe,CAAC;QACpB,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;YAChC,UAAU,GAAG,IAAI,qBAAU,CAAC;gBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,cAAc;aACf,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAY,CAAC;QACjB,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3B,OAAO,GAAG,IAAI,kBAAO,CAAC;gBACpB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,cAAc;aACf,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAEzF,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACjB,MAAM,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,uBAAuB;gBAC9D,wFAAwF;gBACxF,kCAAkC,CAAC;YAErC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,WAAW;YACpB,WAAW,EAAE,OAAO,IAAI,EAAE;YAC1B,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;YAC3B,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK;YAC7C,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,kBAAkB,EAAE,CAAC,aAAa,CAAC;YACnC,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,SAAS,EAAE,cAAc,CAAC,SAAS;SACpC,CAAC;QAEF,2CAAmB,CAAC,WAAW,CAAC,CAAC;QAEjC,aAAa,CAAC,SAAS,GAAG,kBAAkB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEtE,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC;aACvC,IAAI,CAAC;YACJ,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChC,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC;YACJ,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC;gBACrC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC;YACJ,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC3B,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC;YACJ,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC1D,MAAM,CAAC,uCAAe,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC;YACJ,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,WAAW,yBAAyB,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACP,CAAC;CACF,CAAC,CAAC"}