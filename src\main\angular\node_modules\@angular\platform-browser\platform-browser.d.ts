/**
 * Generated bundle index. Do not edit.
 */
export * from './public_api';
export { _document as ɵb, errorHandler as ɵa } from './src/browser';
export { GenericBrowserDomAdapter as ɵh } from './src/browser/generic_browser_adapter';
export { SERVER_TRANSITION_PROVIDERS as ɵg, appInitializerFactory as ɵf } from './src/browser/server-transition';
export { _createNgProbe as ɵc } from './src/dom/debug/ng_probe';
export { EventManagerPlugin as ɵd } from './src/dom/events/event_manager';
export { DomSanitizerImpl as ɵe } from './src/security/dom_sanitization_service';
