{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/router/testing/index.ts", "../../../../../packages/router/testing/src/testing.ts", "../../../../../packages/router/testing/src/router_testing_module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\nexport * from './router_testing_module';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {MockLocationStrategy, SpyLocation} from '@angular/common/testing';\nimport {Compiler, Injectable, Injector, ModuleWithProviders, NgModule, NgModuleFactory, NgModuleFactoryLoader, Optional} from '@angular/core';\nimport {ChildrenOutletContexts, NoPreloading, PreloadingStrategy, ROUTES, Route, Router, RouterModule, Routes, UrlHandlingStrategy, UrlSerializer, provideRoutes, ɵROUTER_PROVIDERS as ROUTER_PROVIDERS, ɵflatten as flatten} from '@angular/router';\n\n\n\n/**\n * @whatItDoes Allows to simulate the loading of ng modules in tests.\n *\n * @howToUse\n *\n * ```\n * const loader = TestBed.get(NgModuleFactoryLoader);\n *\n * @Component({template: 'lazy-loaded'})\n * class LazyLoadedComponent {}\n * @NgModule({\n *   declarations: [LazyLoadedComponent],\n *   imports: [RouterModule.forChild([{path: 'loaded', component: LazyLoadedComponent}])]\n * })\n *\n * class LoadedModule {}\n *\n * // sets up stubbedModules\n * loader.stubbedModules = {lazyModule: LoadedModule};\n *\n * router.resetConfig([\n *   {path: 'lazy', loadChildren: 'lazyModule'},\n * ]);\n *\n * router.navigateByUrl('/lazy/loaded');\n * ```\n *\n * @stable\n */\n\nexport class SpyNgModuleFactoryLoader implements NgModuleFactoryLoader {\n  /**\n   * @docsNotRequired\n   */\n  private _stubbedModules: {[path: string]: Promise<NgModuleFactory<any>>} = {};\n\n  /**\n   * @docsNotRequired\n   */\n  set stubbedModules(modules: {[path: string]: any}) {\n    const res: {[path: string]: any} = {};\n    for (const t of Object.keys(modules)) {\n      res[t] = this.compiler.compileModuleAsync(modules[t]);\n    }\n    this._stubbedModules = res;\n  }\n\n  /**\n   * @docsNotRequired\n   */\n  get stubbedModules(): {[path: string]: any} { return this._stubbedModules; }\n\n  constructor(private compiler: Compiler) {}\n\n  load(path: string): Promise<NgModuleFactory<any>> {\n    if (this._stubbedModules[path]) {\n      return this._stubbedModules[path];\n    } else {\n      return <any>Promise.reject(new Error(`Cannot find module ${path}`));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Compiler, },\n];\n}\n\n/**\n * Router setup factory function used for testing.\n *\n * @stable\n */\nexport function setupTestingRouter(\n    urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts, location: Location,\n    loader: NgModuleFactoryLoader, compiler: Compiler, injector: Injector, routes: Route[][],\n    urlHandlingStrategy?: UrlHandlingStrategy) {\n  const router = new Router(\n      null !, urlSerializer, contexts, location, injector, loader, compiler, flatten(routes));\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n  return router;\n}\n\n/**\n * @whatItDoes Sets up the router to be used for testing.\n *\n * @howToUse\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}])]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @description\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of {@link Location}, {@link LocationStrategy}, and {@link\n * NgModuleFactoryLoader}.\n *\n * @stable\n */\n\nexport class RouterTestingModule {\n  static withRoutes(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterTestingModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [RouterModule],\n  providers: [\n    ROUTER_PROVIDERS, {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n    {provide: NgModuleFactoryLoader, useClass: SpyNgModuleFactoryLoader}, {\n      provide: Router,\n      useFactory: setupTestingRouter,\n      deps: [\n        UrlSerializer, ChildrenOutletContexts, Location, NgModuleFactoryLoader, Compiler, Injector,\n        ROUTES, [UrlHandlingStrategy, new Optional()]\n      ]\n    },\n    {provide: PreloadingStrategy, useExisting: NoPreloading}, provideRoutes([])\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["ROUTER_PROVIDERS"], "mappings": ";;;;;AEAA,OAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,iBAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDA,kCAAA,QAAA;QAkBA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;QAbM,IAAN,CAAA,eAAA,GAAA,EAAA,CAAA;IACA,CAAA;IAIA,sBAAI,oDAAJ;QAWA;;WAEA;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA;QAhBA;;WAEA;aACA,UAAA,OAAA;YACA,IAAA,GAAA,GAAA,EAAA,CAAA;;;;;YAKM,IAAN,CAAA,eAAA,GAAA,GAAuD,CAAvD;QAIE,CAAF;;;OAAA;IAIA,uCAAA,GAAA,UAAA,IAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,CAAA;;QACA,IAAA,CAAA,CAAA;YACQ,MAAR,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,KAAA,CAAA,wBAAA,IAAA,CAAA,CAAA,CAAA;QACA,CAAA;;IAEO,+BAAP;AAAO,CAAP,IAAA;AACA,wBAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAA,UAAA,EAAA;;;;;;AAQA;;;;GAQA;AACA,4BAAA,aAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,mBAAA;IACA,IAAA,MAAA,GAAA,IAAA,MAAA,CAAA,IAAA,EAAA,aAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCO;IAAA;IAIPA,CAAAA;IAHA,8BAAA,GAAA,UAAkB,MAAM;QACxB,MAAA,CAAA,EAAA,QAAW,EAAX,mBAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;IACA,CAAA;IACAA,0BAAAA;AAAAA,CAAAA,AAJO,IAIPA;AACA,mBAAA,CAAI,UAAJ,GAAA;IACA,EAAA,IAAA,EAAA,QAAI,EAAC,IAAL,EAAA,CAAY;gBACZ,OAAA,EAAA,CAAA,YAAA,CAAqB;gBACrB,SAAA,EAAA;oBACA,iBAAA,EAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,WAAA,EAAA;oBACA,EAAA,OAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA;oBACA,EAAA,OAAA,EAAA,qBAAA,EAAA,QAA0C,EAA1C,wBAAA,EAAA,EAAA;wBACA,OAAA,EAAA,MAAA;wBACA,UAAA,EAAA,kBAAA;wBACA,IAAA,EAAA;4BACA,aAAA,EAAA,sBAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,QAAA;4BACA,MAAA,EAAA,CAAA,mBAAA,EAAA,IAAA,QAAA,EAAA,CAAA;yBACA;;oBAEA,EAAA,OAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,YAAA,EAAA,EAAA,aAAA,CAAA,EAAA,CAAA;;aDvJA,EAAA,EAAA;;;;;;;;;;GAYG;;;;;;;;;;;;GDAA;;;;;;;"}