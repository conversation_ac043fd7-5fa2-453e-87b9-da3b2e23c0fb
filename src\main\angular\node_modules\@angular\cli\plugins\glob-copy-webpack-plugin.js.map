{"version": 3, "file": "glob-copy-webpack-plugin.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["plugins/glob-copy-webpack-plugin.ts"], "names": [], "mappings": ";;AAAA,yBAAyB;AACzB,6BAA6B;AAC7B,6BAA6B;AAC7B,uCAAuC;AAEvC,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClD,MAAM,WAAW,GAAQ,SAAS,CAAC,IAAI,CAAC,CAAC;AACzC,MAAM,WAAW,GAAQ,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAE5C,qBAAqB,IAAY;IAC/B,IAAI,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACzC,CAAC;IAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAmBD,2CAA2C;AAC3C,kBAAkB,WAAgB,EAAE,KAAY;IAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IACpE,8EAA8E;IAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAE5F,gCAAgC;IAChC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,kCAAkC;IAClC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;SACzB,IAAI,CAAC,CAAC,IAAS,KAAK,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG;QACpD,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI;QACrB,MAAM,EAAE,MAAM,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC;KACxC,CAAC,CAAC;AACP,CAAC;AAED;IACE,YAAoB,OAAqC;QAArC,YAAO,GAAP,OAAO,CAA8B;IAAI,CAAC;IAE9D,KAAK,CAAC,QAAa;QACjB,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7C,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QAE/D,yDAAyD;QACzD,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;QAEzB,oBAAoB;QACpB,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO;YAC7B,+CAA+C;YAC/C,OAAO,GAAG,OAAO,OAAO,KAAK,QAAQ,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;YACpE,eAAe;YACf,gEAAgE;YAChE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC9D,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;YAClC,iCAAiC;YACjC,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;YACxC,CAAC;YACD,MAAM,CAAC,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,WAAgB,EAAE,EAAO;YAChD,oDAAoD;YACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAgB,KAAK,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;YAC3E,uCAAuC;YACvC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;iBAE9E,IAAI,CAAC,CAAC,WAAqB,KAAK,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;gBACvD,UAAU,EAAE,OAAO,CAAC,KAAK;gBACzB,eAAe,EAAE,OAAO,CAAC,MAAM;gBAC/B,YAAY,EAAE,GAAG;aAClB,CAAC,CAAC,CAAC;iBACH,IAAI,CAAC,CAAC,KAAY,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;iBACtC,KAAK,CAAC,MAAM,CAAC,CACjB,CAAC,CAAC;YAEH,sBAAsB;YACtB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;iBAEf,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;iBAEnC,IAAI,CAAC,MAAM,IACV,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAY,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;iBACzE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAnDD,sDAmDC"}