[{"__symbolic": "module", "version": 3, "metadata": {"NoopAnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "AnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"NOOP": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "NoopAnimationDriver"}}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"NoopAnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}}, "AnimationDriver": {"__symbolic": "class", "members": {"matchesElement": [{"__symbolic": "method"}], "containsElement": [{"__symbolic": "method"}], "query": [{"__symbolic": "method"}], "computeStyle": [{"__symbolic": "method"}], "animate": [{"__symbolic": "method"}]}, "statics": {"NOOP": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "NoopAnimationDriver"}}}}}}]