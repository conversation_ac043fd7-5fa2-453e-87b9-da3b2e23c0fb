{"version": 3, "file": "common-testing.umd.js", "sources": ["../../../../packages/common/testing/src/mock_location_strategy.ts", "../../../../packages/common/testing/src/testing.ts", "../../../../packages/common/testing/src/location_mock.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @stable\n */\n\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  constructor() { super(); }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n\n  path(includeHash: boolean = false): string { return this.internalPath; }\n\n  prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  pushState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  replaceState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  onPopState(fn: (value: any) => void): void { this._subject.subscribe({next: fn}); }\n\n  getBaseHref(): string { return this.internalBaseHref; }\n\n  back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  forward(): void { throw 'not implemented'; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\nexport {SpyLocation} from './location_mock';\nexport {MockLocationStrategy} from './mock_location_strategy';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @experimental\n */\n\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '')];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _baseHref: string = '';\n  /** @internal */\n  _platformStrategy: LocationStrategy = null !;\n\n  setInitialPath(url: string) { this._history[this._historyIndex].path = url; }\n\n  setBaseHref(url: string) { this._baseHref = url; }\n\n  path(): string { return this._history[this._historyIndex].path; }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath =\n        this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n  }\n\n  simulateUrlPop(pathname: string) { this._subject.emit({'url': pathname, 'pop': true}); }\n\n  simulateHashChange(pathname: string) {\n    // Because we don't prevent the native event, the browser will independently update the path\n    this.setInitialPath(pathname);\n    this.urlChanges.push('hash: ' + pathname);\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._baseHref + url;\n  }\n\n  go(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query));\n    this._historyIndex = this._history.length - 1;\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push(url);\n    this._subject.emit({'url': url, 'pop': false});\n  }\n\n  replaceState(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push('replace: ' + url);\n  }\n\n  forward() {\n    if (this._historyIndex < (this._history.length - 1)) {\n      this._historyIndex++;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  subscribe(\n      onNext: (value: any) => void, onThrow?: ((error: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  normalize(url: string): string { return null !; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass LocationState {\n  path: string;\n  query: string;\n  constructor(path: string, query: string) {\n    this.path = path;\n    this.query = query;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["Injectable", "LocationStrategy", "EventEmitter", "tslib_1.__extends"], "mappings": ";;;;;;AGAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;;;;;;;;;;;;;;IDrIP,SAAA,WAAA,GAAA;;QAEE,IAAF,CAAA,QAAA,GAAA,CAAsB,IAAtB,aAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;;QA+FA,IAAA,CAAA,QAAA,GAAA,IAAAE,0BAAA,EAAA,CAAA;;QAzFA,IAAA,CAAA,SAAA,GAAA,EAAiC,CAAC;;QAIlC,IAAA,CAAA,iBAAA,GAAA,IAAA,CAAA;KACA;IACA,WAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,GAAkB,EAAlB,EAAA,IACa,CADb,QAAA,CAAA,IAAA,CAC4B,aAAa,CADzC,CAAA,IAC+C,GAD/C,GAAA,CAAA,EAAA,CAAA;IAGA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UAAA,GAAA,EAAA,EAAmB,IAAI,CAAvB,SAAA,GAAoC,GAApC,CAAA,EAA0C,CAA1C;IACA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA;IAEE,WAAF,CAAA,SAAA,CAAA,oBAAA,GAAE,UAAF,IAAA,EAAqC,KAArC,EAAA;QAAqC,IAArC,KAAA,KAAA,KAAA,CAAA,EAAqC,EAAA,KAArC,GAAA,EAAA,CAAA,EAAA;QAEA,IAAA,SAAqB,GAArB,IAAA,CAAqC,QAArC,CAAA,GAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,GAAA,IAAA,CAAA;;QAEI,OAAJ,QAAA,IAAuB,SAAS,IAAhC,KAAA,CAAA,MAAA,GAAA,CAAA,IAAA,GAAA,GAAA,KAAA,IAAA,EAAA,CAAA,CAAA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,QAAuB,EAAvB,EAAA,IAAA,CAA+B,QAAQ,CAAvC,IAAA,CAAA,EAAA,KAAoD,EAAE,QAAQ,EAA9D,KAAA,EAAA,IAAA,EAA4E,CAAC,CAA7E,EAAA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,QAAA,EAAA;;QAGI,IAAI,CAAR,cAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAM,CAAN,UAAA,CAAkB,IAAI,CAAtB,QAAA,GAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,CAAA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,GAAA,EAAA;QAEA,IAAA,GAAA,CAAA,MAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACQ,GAAG,GAAX,GAAA,GAAA,GAAA,CAAA;SAEA;QACA,OAAA,IAAA,CAAA,SAAA,GAA0B,GAA1B,CAAA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,EAAA,GAAA,UAAA,IAAA,EAAS,KAAT,EAAA;QAAS,IAAT,KAAA,KAAA,KAAA,CAAA,EAAS,EAAA,KAAT,GAAA,EAAA,CAAA,EAAA;QACI,IAAI,GAAR,IAAA,CAAA,kBAAA,CAAA,IAAuC,CAAvC,CAAA;QAEI,IAAJ,IAAA,CAAA,aAA0B,GAA1B,CAA8B,EAA9B;YACQ,IAAR,CAAA,QAAqB,CAAC,MAAtB,CAAA,IAAA,CAAkC,aAAlC,GAAA,CAAmD,CAAC,CAApD;SACA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,aAAA,GAAA,IAA6B,CAAC,QAA9B,CAAuC,MAAvC,GAAA,CAAA,CAAkD;QAC9C,IAAJ,aAAA,GAAA,IAA8B,CAA9B,QAAA,CAAA,IAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA;QACI,IAAI,aAAR,CAAsB,IAAtB,IAA6B,IAA7B,IAAA,aAAiD,CAAC,KAAlD,IAAA,KAAA,EAAA;YACA,OAAA;SAEA;QACI,IAAJ,GAAA,GAAgB,IAAhB,IAAA,KAAA,CAAA,MAAA,GAAuC,CAAC,IAAxC,GAAA,GAAA,KAAA,IAAA,EAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,UAAA,CAAoB,IAAI,CAAC,GAAzB,CAAA,CAAA;QACI,IAAI,CAAR,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAgC,GAAhC,EAAA,KAAA,EAAA,KAAiD,EAAjD,CAAA,CAAA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,IAAA,EAAA,KAAA,EAAA;QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAA,GAAA,EAAA,CAAA,EAAA;QAEI,IAAJ,GAAW,IAAX,CAAgB,kBAAhB,CAAA,IAAA,CAAA,CAAA;QACI,IAAJ,OAAiB,GAAG,IAApB,CAAyB,QAAzB,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;QAEI,IAAJ,OAAA,CAAgB,IAAI,IAAI,IAAxB,IAAA,OAAuC,CAAC,KAAxC,IAAA,KAAA,EAAA;YACQ,OAAR;SACA;QAEA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA;QACI,OAAJ,CAAY,KAAZ,GAAA,KAAA,CAA0B;QAC1B,IAAA,GAAW,GAAX,IAAA,IAAA,KAA2B,CAA3B,MAAA,GAAA,CAAA,IAAA,GAAA,GAAA,KAAA,IAAA,EAAA,CAAA,CAAA;QACA,IAAM,CAAN,UAAA,CAAA,IAAA,CAAA,WAAiC,GAAjC,GAAA,CAAA,CAAA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA;QAEM,IAAN,IAAA,CAAA,aAAA,IAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA;YACQ,IAAI,CAAC,aAAa,EAA1B,CAA6B;YACvB,IAAI,CAAC,QAAX,CAAA,IAAwB,CAAxB,EAA2B,KAA3B,EAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;SACA;KACA,CAAA;IACA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA;QAEA,IAAA,IAAA,CAAA,aAAA,GAAA,CAAA,EAAA;YAGA,IAAA,CAAA,aAAA,EAAA,CAAA;YACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;SAEA;;IACA,WAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,MAAA,EAAiB,OAAjB,EAAA,QAAA,EAAA;QACA,OAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,CAAA;KACA,CAAA;;IAEO,OAAP,WAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AAIA,WAAA,CAAA,UAAA,GAAA;IAGE,EAAF,IAAA,EAAAF,wBAA4B,EAA5B;CACA,CAAA;;AAEA,WAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;AACA,IAAA,aAAA,IAAA,YAAA;;QFjIA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;CEiIA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;AF1GA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAAAG,SAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;IACA,SAAA,oBAAA,GAAA;QAAA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAUA;QARE,KAAF,CAAA,gBAAA,GAAA,GAAA,CAAA;QACA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA;QAEA,KAAA,CAAA,aAAA,GAAA,EAAA,CAAA;QACI,KAAI,CAAC,UAAT,GAAA,EAAwB,CAAxB;;QAEA,KAAA,CAAA,QAAA,GAAA,IAAAD,0BAAA,EAAA,CAAA;;KAEA;IAEE,oBAAF,CAAA,SAAA,CAAA,gBAAA,GAAE,UAAF,GAAA,EAAA;QACI,IAAI,CAAR,YAAA,GAAA,GAA2B,CAAC;QAC5B,IAAM,CAAN,QAAA,CAAA,IAAA,CAAA,IAAA,kBAAA,CAAA,IAAA,CAAA,IAAA,EAAuD,CAAC,CAAC,CAAC;KAC1D,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAA,WAAA,EAAA;QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,KAAA,CAAA,EAAA;QAAA,OAAmC,IAAnC,CAAA,YAAA,CAAA;KAAA,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,QAAA,EAAA;QAEA,IAAA,QAAA,CAAA,UAAA,CAAmD,GAAnD,CAAA,IAAA,IAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;YACQ,OAAR,IAAA,CAAA,gBAAA,GAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;SAEA;QACI,OAAJ,IAAA,CAAA,gBAAA,GAAA,QAAA,CAAA;KAEA,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAA,GAAA,EAAA,KAAoB,EAApB,IAAA,EAAA,KAAA,EAAA;QACA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA;QAEA,IAAA,GAAe,GAAQ,IAAvB,IAAA,KAAoD,CAApD,MAAmE,GAAnE,CAAA,IAAA,GAAA,GAAA,KAAA,IAAA,EAAA,CAAA,CAAA;QACI,IAAI,CAAC,YAAT,GAAA,GAAA,CAAA;QAEI,IAAM,WAAV,GAAwB,IAAxB,CAA6B,kBAAkB,CAA/C,GAAA,CAAA,CAAA;QACI,IAAI,CAAC,UAAT,CAAA,IAAwB,CAAxB,WAAA,CAAA,CAAA;KAEA,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAyB,IAAzB,EAAA,KAAoC,EAApC;QACA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA;QAEA,IAAY,GAAyB,GAArC,IAAA,IAAA,KAAA,CAA4D,MAA5D,GAAA,CAAsE,IAAtE,GAA4E,GAA5E,KAAmF,IAAnF,EAAA,CAAA,CAAA;QAEA,IAAA,CAAA,YAAA,GAAA,GAAqC,CAAC;QAEhC,IAAN,WAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA;QACI,IAAI,CAAR,UAAA,CAAA,IAAwB,CAAxB,WAAoC,GAApC,WAAA,CAAA,CAAA;KACA,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,EAAA,EAAA,EAAA,IAAmB,CAAnB,QAAA,CAAA,SAAsC,CAAtC,EAAA,IAAA,EAA+C,EAA/C,EAAmD,CAAnD,CAAA,EAAuD,CAAvD;IACA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,CAAA;IACA,oBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA;QACA,IAAA,IAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;YAEA,IAAoB,CAApB,UAAA,CAAA,GAAA,EAAA,CAAA;;YACA,IAAA,CAAA,gBAAA,CAA2C,OAA3C,CAAA,CAAA;SACA;KACA,CAAA;;IAEO,OAAP,oBAAA,CAAA;CAAA,CAzDAD,gCAAA,CAyDA,CAAA,CAAA;AAIA,oBAAA,CAAA,UAAA,GAAA;IAGE,EAAF,IAAA,EAAAD,wBAAmC,EAAnC;CAAA,CAAA;;AADA,oBAAiB,CAAjB,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;AACA,IAAA,kBAAA,IAAA,YAAA;IACA,SAAA,kBAAA,CAAA,MAAA,EAAA;;QCxFA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA;;;;CDuFA,EAAA,CAAA,CAAA;;;;;;;"}