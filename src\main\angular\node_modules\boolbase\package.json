{"_args": [["boolbase@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "boolbase@1.0.0", "_id": "boolbase@1.0.0", "_inBundle": false, "_integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "_location": "/boolbase", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "boolbase@1.0.0", "name": "boolbase", "escapedName": "boolbase", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/css-select", "/nth-check"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/boolbase/-/boolbase-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/boolbase/issues"}, "description": "two functions: One that returns true, one that returns false", "homepage": "https://github.com/fb55/boolbase", "keywords": ["boolean", "function"], "license": "ISC", "main": "index.js", "name": "boolbase", "repository": {"type": "git", "url": "git+https://github.com/fb55/boolbase.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.0"}