{"version": 3, "file": "test_support.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/test_support.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,uBAAyB;AACzB,uBAAyB;AACzB,2BAA6B;AAE7B,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAEtD,uBAA8B,IAAY,EAAE,QAAgB;IAC1D,+BAA+B;IAC/B,IAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAChD,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAO,EAAE,SAAI,IAAM,CAAC,CAAC;IAClD,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,CAAC;AACZ,CAAC;AAND,sCAMC;AAED;IACE,IAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAChD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAO,EAAI,CAAC,CAAC;IAC3C,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,CAAC,GAAG,CAAC;AACb,CAAC;AALD,kCAKC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as fs from 'fs';\nimport * as os from 'os';\nimport * as path from 'path';\n\nconst tmpdir = process.env.TEST_TMPDIR || os.tmpdir();\n\nexport function writeTempFile(name: string, contents: string): string {\n  // TEST_TMPDIR is set by bazel.\n  const id = (Math.random() * 1000000).toFixed(0);\n  const fn = path.join(tmpdir, `tmp.${id}.${name}`);\n  fs.writeFileSync(fn, contents);\n  return fn;\n}\n\nexport function makeTempDir(): string {\n  const id = (Math.random() * 1000000).toFixed(0);\n  const dir = path.join(tmpdir, `tmp.${id}`);\n  fs.mkdirSync(dir);\n  return dir;\n}\n"]}