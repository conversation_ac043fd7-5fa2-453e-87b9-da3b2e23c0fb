{"version": 3, "file": "animations-browser.umd.min.js", "sources": ["../../../../packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../packages/animations/browser/src/render/animation_engine_next.ts", "../../../../packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../packages/animations/browser/src/dsl/animation.ts", "../../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../packages/animations/browser/src/util.ts", "../../../../packages/animations/browser/src/dsl/animation_ast.ts", "../../../../packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/animations/browser/src/render/shared.ts", "../../../../packages/animations/browser/src/render/animation_driver.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../packages/animations/browser/src/dsl/animation_dsl_visitor.ts", "../../../../packages/animations/browser/src/dsl/animation_transition_expr.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_PLAYER_ARRAY: AnimationPlayer[] = [];\nconst /** @type {?} */ NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst /** @type {?} */ NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: null,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\nexport interface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nexport const /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n\nexport interface ElementAnimationState {\n  setForRemoval: any;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n}\nexport class StateValue {\npublic value: string;\npublic options: AnimationOptions;\n/**\n * @param {?} input\n */\nconstructor(input: any) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      const options = copyObj(input as any);\n      delete options['value'];\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n/**\n * @param {?} options\n * @return {?}\n */\nabsorbOptions(options: AnimationOptions) {\n    const /** @type {?} */ newParams = options.params;\n    if (newParams) {\n      const /** @type {?} */ oldParams = /** @type {?} */(( this.options.params));\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nfunction StateValue_tsickle_Closure_declarations() {\n/** @type {?} */\nStateValue.prototype.value;\n/** @type {?} */\nStateValue.prototype.options;\n}\n\n\nexport const /** @type {?} */ VOID_VALUE = 'void';\nexport const /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport const /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nexport class AnimationTransitionNamespace {\npublic players: TransitionAnimationPlayer[] = [];\nprivate _triggers: {[triggerName: string]: AnimationTrigger} = {};\nprivate _queue: QueueInstruction[] = [];\nprivate _elementListeners = new Map<any, TriggerListener[]>();\nprivate _hostClassName: string;\n/**\n * @param {?} id\n * @param {?} hostElement\n * @param {?} _engine\n */\nconstructor(\npublic id: string,\npublic hostElement: any,\nprivate _engine: TransitionAnimationEngine) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n/**\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.hasOwnProperty(name)) {\n      throw new Error(\n          `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw new Error(\n          `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw new Error(\n          `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n    }\n\n    const /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n    const /** @type {?} */ data = {name, phase, callback};\n    listeners.push(data);\n\n    const /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n    if (!triggersWithStates.hasOwnProperty(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates[name] = null;\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const /** @type {?} */ index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers[name]) {\n          delete triggersWithStates[name];\n        }\n      });\n    };\n  }\n/**\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nregister(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers[name]) {\n      // throw\n      return false;\n    } else {\n      this._triggers[name] = ast;\n      return true;\n    }\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate _getTrigger(name: string) {\n    const /** @type {?} */ trigger = this._triggers[name];\n    if (!trigger) {\n      throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n    }\n    return trigger;\n  }\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} value\n * @param {?=} defaultToFallback\n * @return {?}\n */\ntrigger(element: any, triggerName: string, value: any, defaultToFallback: boolean = true):\n      TransitionAnimationPlayer|undefined {\n    const /** @type {?} */ trigger = this._getTrigger(triggerName);\n    const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = {});\n    }\n\n    let /** @type {?} */ fromState = triggersWithStates[triggerName];\n    const /** @type {?} */ toState = new StateValue(value);\n\n    const /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates[triggerName] = toState;\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    } else if (fromState === DELETED_STATE_VALUE) {\n      return player;\n    }\n\n    const /** @type {?} */ playersOnElement: TransitionAnimationPlayer[] =\n        getOrSetAsInMap(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new palyer)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n    let /** @type {?} */ isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push(\n        {element, triggerName, transition, fromState, toState, player, isFallbackTransition});\n\n    if (!isFallbackTransition) {\n      addClass(element, NG_ANIMATING_CLASSNAME);\n    }\n\n    player.onDone(() => {\n      removeClass(element, NG_ANIMATING_CLASSNAME);\n\n      let /** @type {?} */ index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const /** @type {?} */ players = this._engine.playersByElement.get(element);\n      if (players) {\n        let /** @type {?} */ index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\nderegister(name: string) {\n    delete this._triggers[name];\n\n    this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n          element, listeners.filter(entry => { return entry.name != name; }));\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nclearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n/**\n * @param {?} rootElement\n * @param {?} context\n * @param {?=} animate\n * @return {?}\n */\nprivate _destroyInnerNodes(rootElement: any, context: any, animate: boolean = false) {\n    this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n      if (animate && containsClass(elm, this._hostClassName)) {\n        const /** @type {?} */ innerNs = this._engine.namespacesByHostElement.get(elm);\n\n        // special case for a host element with animations on the same element\n        if (innerNs) {\n          innerNs.removeNode(elm, context, true);\n        }\n\n        this.removeNode(elm, context, true);\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n  }\n/**\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(element: any, context: any, doNotRecurse?: boolean): void {\n    const /** @type {?} */ engine = this._engine;\n\n    if (!doNotRecurse && element.childElementCount) {\n      this._destroyInnerNodes(element, context, true);\n    }\n\n    const /** @type {?} */ triggerStates = engine.statesByElement.get(element);\n    if (triggerStates) {\n      const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n      Object.keys(triggerStates).forEach(triggerName => {\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers[triggerName]) {\n          const /** @type {?} */ player = this.trigger(element, triggerName, VOID_VALUE, false);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        engine.markElementAsRemoved(this.id, element, true, context);\n        optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n        return;\n      }\n    }\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let /** @type {?} */ containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const /** @type {?} */ currentPlayers =\n          engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue fowards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let /** @type {?} */ parent = element;\n        while (parent = parent.parentNode) {\n          const /** @type {?} */ triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    const /** @type {?} */ listeners = this._elementListeners.get(element);\n    if (listeners) {\n      const /** @type {?} */ visitedTriggers = new Set<string>();\n      listeners.forEach(listener => {\n        const /** @type {?} */ triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const /** @type {?} */ trigger = this._triggers[triggerName];\n        const /** @type {?} */ transition = trigger.fallbackTransition;\n        const /** @type {?} */ elementStates = /** @type {?} */(( engine.statesByElement.get(element)));\n        const /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n        const /** @type {?} */ toState = new StateValue(VOID_VALUE);\n        const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      // we do this after the flush has occurred such\n      // that the callbacks can be fired\n      engine.afterFlush(() => this.clearElementCache(element));\n      engine.destroyInnerAnimations(element);\n      engine._onRemovalComplete(element, context);\n    }\n  }\n/**\n * @param {?} element\n * @param {?} parent\n * @return {?}\n */\ninsertNode(element: any, parent: any): void { addClass(element, this._hostClassName); }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\ndrainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const /** @type {?} */ instructions: QueueInstruction[] = [];\n    this._queue.forEach(entry => {\n      const /** @type {?} */ player = entry.player;\n      if (player.destroyed) return;\n\n      const /** @type {?} */ element = entry.element;\n      const /** @type {?} */ listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const /** @type {?} */ baseEvent = makeAnimationEvent(\n                element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            ( /** @type {?} */((baseEvent as any)))['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const /** @type {?} */ d0 = a.transition.ast.depCount;\n      const /** @type {?} */ d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n/**\n * @param {?} context\n * @return {?}\n */\ndestroy(context: any) {\n    this.players.forEach(p => p.destroy());\n    this._destroyInnerNodes(this.hostElement, context);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nelementContainsData(element: any): boolean {\n    let /** @type {?} */ containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData =\n        (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n}\n\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionNamespace.prototype.players;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._triggers;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._queue;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._elementListeners;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._hostClassName;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.id;\n/** @type {?} */\nAnimationTransitionNamespace.prototype.hostElement;\n/** @type {?} */\nAnimationTransitionNamespace.prototype._engine;\n}\n\n\nexport interface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\nexport class TransitionAnimationEngine {\npublic players: TransitionAnimationPlayer[] = [];\npublic newHostElements = new Map<any, AnimationTransitionNamespace>();\npublic playersByElement = new Map<any, TransitionAnimationPlayer[]>();\npublic playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\npublic statesByElement = new Map<any, {[triggerName: string]: StateValue}>();\npublic totalAnimations = 0;\npublic totalQueuedPlayers = 0;\nprivate _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\nprivate _namespaceList: AnimationTransitionNamespace[] = [];\nprivate _flushFns: (() => any)[] = [];\nprivate _whenQuietFns: (() => any)[] = [];\npublic namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\npublic collectedEnterElements: any[] = [];\npublic collectedLeaveElements: any[] = [];\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\n_onRemovalComplete(element: any, context: any) { this.onRemovalComplete(element, context); }\n/**\n * @param {?} driver\n * @param {?} _normalizer\n */\nconstructor(public driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @return {?}\n */\nget queuedPlayers(): TransitionAnimationPlayer[] {\n    const /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\ncreateNamespace(namespaceId: string, hostElement: any) {\n    const /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (hostElement.parentNode) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is apart of the animation code, it\n      // may or may not be inserted by a parent node that is an of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n/**\n * @param {?} ns\n * @param {?} hostElement\n * @return {?}\n */\nprivate _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const /** @type {?} */ limit = this._namespaceList.length - 1;\n    if (limit >= 0) {\n      let /** @type {?} */ found = false;\n      for (let /** @type {?} */ i = limit; i >= 0; i--) {\n        const /** @type {?} */ nextNamespace = this._namespaceList[i];\n        if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n          this._namespaceList.splice(i + 1, 0, ns);\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        this._namespaceList.splice(0, 0, ns);\n      }\n    } else {\n      this._namespaceList.push(ns);\n    }\n\n    this.namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} name\n * @param {?} trigger\n * @return {?}\n */\nregisterTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n\n    const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n      const /** @type {?} */ index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _fetchNamespace(id: string) { return this._namespaceLookup[id]; }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\ntrigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      this._fetchNamespace(namespaceId).trigger(element, name, value);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\ninsertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      this._fetchNamespace(namespaceId).insertNode(element, parent);\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n/**\n * @param {?} element\n * @return {?}\n */\ncollectEnterElement(element: any) { this.collectedEnterElements.push(element); }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @param {?=} doNotRecurse\n * @return {?}\n */\nremoveNode(namespaceId: string, element: any, context: any, doNotRecurse?: boolean): void {\n    if (!isElementNode(element)) {\n      this._onRemovalComplete(element, context);\n      return;\n    }\n\n    const /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n    if (ns) {\n      ns.removeNode(element, context, doNotRecurse);\n    } else {\n      this.markElementAsRemoved(namespaceId, element, false, context);\n    }\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?=} hasAnimation\n * @param {?=} context\n * @return {?}\n */\nmarkElementAsRemoved(namespaceId: string, element: any, hasAnimation?: boolean, context?: any) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context, hasAnimation,\n      removedBeforeQueried: false\n    };\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} name\n * @param {?} phase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, name: string, phase: string,\n      callback: (event: any) => boolean): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n/**\n * @param {?} entry\n * @param {?} subTimelines\n * @return {?}\n */\nprivate _buildInstruction(entry: QueueInstruction, subTimelines: ElementInstructionMap) {\n    return entry.transition.build(\n        this.driver, entry.element, entry.fromState.value, entry.toState.value,\n        entry.toState.options, subTimelines);\n  }\n/**\n * @param {?} containerElement\n * @return {?}\n */\ndestroyInnerAnimations(containerElement: any) {\n    let /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => {\n      const /** @type {?} */ players = this.playersByElement.get(element);\n      if (players) {\n        players.forEach(player => {\n          // special case for when an element is set for destruction, but hasn't started.\n          // in this situation we want to delay the destruction until the flush occurs\n          // so that any event listeners attached to the player are triggered.\n          if (player.queued) {\n            player.markedForDestroy = true;\n          } else {\n            player.destroy();\n          }\n        });\n      }\n      const /** @type {?} */ stateMap = this.statesByElement.get(element);\n      if (stateMap) {\n        Object.keys(stateMap).forEach(triggerName => stateMap[triggerName] = DELETED_STATE_VALUE);\n      }\n    });\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    if (elements.length) {\n      elements.forEach(element => {\n        const /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n          players.forEach(player => player.finish());\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nprocessLeaveNode(element: any) {\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1) {\n    let /** @type {?} */ players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this._namespaceList.length &&\n        (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      players = this._flushAnimations(microtaskId);\n    } else {\n      for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n        const /** @type {?} */ element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const /** @type {?} */ quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n/**\n * @param {?} microtaskId\n * @return {?}\n */\nprivate _flushAnimations(microtaskId: number): TransitionAnimationPlayer[] {\n    const /** @type {?} */ subTimelines = new ElementInstructionMap();\n    const /** @type {?} */ skippedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const /** @type {?} */ queuedInstructions: QueuedTransition[] = [];\n    const /** @type {?} */ queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const /** @type {?} */ allPreStyleElements = new Map<any, Set<string>>();\n    const /** @type {?} */ allPostStyleElements = new Map<any, Set<string>>();\n\n    const /** @type {?} */ bodyNode = getBodyNode();\n    const /** @type {?} */ allEnterNodes: any[] = this.collectedEnterElements.length ?\n        this.collectedEnterElements.filter(createIsRootFilterFn(this.collectedEnterElements)) :\n        [];\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      addClass(allEnterNodes[i], ENTER_CLASSNAME);\n    }\n\n    const /** @type {?} */ allLeaveNodes: any[] = [];\n    const /** @type {?} */ leaveNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n      const /** @type {?} */ element = this.collectedLeaveElements[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n      if (details && details.setForRemoval) {\n        addClass(element, LEAVE_CLASSNAME);\n        allLeaveNodes.push(element);\n        if (!details.hasAnimation) {\n          leaveNodesWithoutAnimations.push(element);\n        }\n      }\n    }\n\n    for (let /** @type {?} */ i = this._namespaceList.length - 1; i >= 0; i--) {\n      const /** @type {?} */ ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const /** @type {?} */ player = entry.player;\n\n        const /** @type {?} */ element = entry.element;\n        if (!bodyNode || !this.driver.containsElement(bodyNode, element)) {\n          player.destroy();\n          return;\n        }\n\n        const /** @type {?} */ instruction = this._buildInstruction(entry, subTimelines);\n        if (!instruction) return;\n\n        // if a unmatched transition is queued to go then it SHOULD NOT render\n        // an animation and cancel the previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub trigger\n        // then it will instruct the timeline builder to not add a player delay, but\n        // instead stretch the first keyframe gap up until the animation starts. The\n        // reason this is important is to prevent extra initialization styles from being\n        // required by the user in the animation.\n        instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n\n        subTimelines.append(element, instruction.timelines);\n\n        const /** @type {?} */ tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach(\n            element => getOrSetAsInMap(queriedElements, element, []).push(player));\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          if (props.length) {\n            let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPreStyleElements.get(element)));\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set<string>());\n            }\n            props.forEach(prop => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          const /** @type {?} */ props = Object.keys(stringMap);\n          let /** @type {?} */ setVal: Set<string> = /** @type {?} */(( allPostStyleElements.get(element)));\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set<string>());\n          }\n          props.forEach(prop => setVal.add(prop));\n        });\n      });\n    }\n\n    // these can only be detected here since we have a map of all the elements\n    // that have animations attached to them...\n    const /** @type {?} */ enterNodesWithoutAnimations: any[] = [];\n    for (let /** @type {?} */ i = 0; i < allEnterNodes.length; i++) {\n      const /** @type {?} */ element = allEnterNodes[i];\n      if (!subTimelines.has(element)) {\n        enterNodesWithoutAnimations.push(element);\n      }\n    }\n\n    const /** @type {?} */ allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    let /** @type {?} */ sortedParentElements: any[] = [];\n    queuedInstructions.forEach(entry => {\n      const /** @type {?} */ element = entry.element;\n      if (subTimelines.has(element)) {\n        sortedParentElements.unshift(element);\n        this._beforeAnimationBuild(\n            entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n\n    skippedPlayers.forEach(player => {\n      const /** @type {?} */ element = player.element;\n      const /** @type {?} */ previousPlayers =\n          this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(\n          prevPlayer => { getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer); });\n    });\n\n    allPreviousPlayersMap.forEach(players => players.forEach(player => player.destroy()));\n\n    // PRE STAGE: fill the ! styles\n    const /** @type {?} */ preStylesMap = allPreStyleElements.size ?\n        cloakAndComputeStyles(\n            this.driver, enterNodesWithoutAnimations, allPreStyleElements, PRE_STYLE) :\n        new Map<any, ɵStyleData>();\n\n    // POST STAGE: fill the * styles\n    const /** @type {?} */ postStylesMap = cloakAndComputeStyles(\n        this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n\n    const /** @type {?} */ rootPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ subPlayers: TransitionAnimationPlayer[] = [];\n    queuedInstructions.forEach(entry => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        const /** @type {?} */ innerPlayer = this._buildAnimation(\n            player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap,\n            postStylesMap);\n        player.setRealPlayer(innerPlayer);\n\n        let /** @type {?} */ parentHasPriority: any = null;\n        for (let /** @type {?} */ i = 0; i < sortedParentElements.length; i++) {\n          const /** @type {?} */ parent = sortedParentElements[i];\n          if (parent === element) break;\n          if (this.driver.containsElement(parent, element)) {\n            parentHasPriority = parent;\n            break;\n          }\n        }\n\n        if (parentHasPriority) {\n          const /** @type {?} */ parentPlayers = this.playersByElement.get(parentHasPriority);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        } else {\n          rootPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        subPlayers.push(player);\n      }\n    });\n\n    subPlayers.forEach(player => {\n      const /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.parentPlayer.onDestroy(() => player.destroy());\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let /** @type {?} */ i = 0; i < allLeaveNodes.length; i++) {\n      const /** @type {?} */ element = allLeaveNodes[i];\n      const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let /** @type {?} */ players: AnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n          let /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      if (players.length) {\n        removeNodesAfterAnimationDone(this, element, players);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const /** @type {?} */ index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    allEnterNodes.forEach(element => removeClass(element, ENTER_CLASSNAME));\n\n    return rootPlayers;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @return {?}\n */\nelementContainsData(namespaceId: string, element: any) {\n    let /** @type {?} */ containsData = false;\n    const /** @type {?} */ details = /** @type {?} */(( element[REMOVAL_FLAG] as ElementAnimationState));\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlush(callback: () => any) { this._flushFns.push(callback); }\n/**\n * @param {?} callback\n * @return {?}\n */\nafterFlushAnimationsDone(callback: () => any) { this._whenQuietFns.push(callback); }\n/**\n * @param {?} element\n * @param {?} isQueriedElement\n * @param {?=} namespaceId\n * @param {?=} triggerName\n * @param {?=} toStateValue\n * @return {?}\n */\nprivate _getPreviousPlayers(\n      element: string, isQueriedElement: boolean, namespaceId?: string, triggerName?: string,\n      toStateValue?: any): TransitionAnimationPlayer[] {\n    let /** @type {?} */ players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const /** @type {?} */ isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @return {?}\n */\nprivate _beforeAnimationBuild(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>) {\n    // it's important to do this step before destroying the players\n    // so that the onDone callback below won't fire before this\n    eraseStyles(instruction.element, instruction.fromStyles);\n\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const /** @type {?} */ targetNameSpaceId: string|undefined =\n        instruction.isRemovalTransition ? undefined : namespaceId;\n    const /** @type {?} */ targetTriggerName: string|undefined =\n        instruction.isRemovalTransition ? undefined : triggerName;\n\n    instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      const /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n      const /** @type {?} */ previousPlayers = this._getPreviousPlayers(\n          element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const /** @type {?} */ realPlayer = /** @type {?} */(( player.getRealPlayer() as any));\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        players.push(player);\n      });\n    });\n  }\n/**\n * @param {?} namespaceId\n * @param {?} instruction\n * @param {?} allPreviousPlayersMap\n * @param {?} skippedPlayersMap\n * @param {?} preStylesMap\n * @param {?} postStylesMap\n * @return {?}\n */\nprivate _buildAnimation(\n      namespaceId: string, instruction: AnimationTransitionInstruction,\n      allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n      skippedPlayersMap: Map<any, AnimationPlayer[]>, preStylesMap: Map<any, ɵStyleData>,\n      postStylesMap: Map<any, ɵStyleData>): AnimationPlayer {\n    const /** @type {?} */ triggerName = instruction.triggerName;\n    const /** @type {?} */ rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const /** @type {?} */ allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const /** @type {?} */ allConsumedElements = new Set<any>();\n    const /** @type {?} */ allSubElements = new Set<any>();\n    const /** @type {?} */ allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const /** @type {?} */ element = timelineInstruction.element;\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const /** @type {?} */ details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer();\n\n      const /** @type {?} */ isQueriedElement = element !== rootElement;\n      let /** @type {?} */ previousPlayers: AnimationPlayer[] = EMPTY_PLAYER_ARRAY;\n      if (!allConsumedElements.has(element)) {\n        allConsumedElements.add(element);\n        const /** @type {?} */ _previousPlayers = allPreviousPlayersMap.get(element);\n        if (_previousPlayers) {\n          previousPlayers = _previousPlayers.map(p => p.getRealPlayer());\n        }\n      }\n      const /** @type {?} */ preStyles = preStylesMap.get(element);\n      const /** @type {?} */ postStyles = postStylesMap.get(element);\n      const /** @type {?} */ keyframes = normalizeKeyframes(\n          this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles,\n          postStyles);\n      const /** @type {?} */ player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach(player => {\n      getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(\n        element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n\n    return player;\n  }\n/**\n * @param {?} instruction\n * @param {?} keyframes\n * @param {?} previousPlayers\n * @return {?}\n */\nprivate _buildPlayer(\n      instruction: AnimationTimelineInstruction, keyframes: ɵStyleData[],\n      previousPlayers: AnimationPlayer[]): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n          instruction.element, keyframes, instruction.duration, instruction.delay,\n          instruction.easing, previousPlayers);\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer();\n  }\n}\n\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationEngine.prototype.players;\n/** @type {?} */\nTransitionAnimationEngine.prototype.newHostElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.playersByQueriedElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.statesByElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalAnimations;\n/** @type {?} */\nTransitionAnimationEngine.prototype.totalQueuedPlayers;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceLookup;\n/** @type {?} */\nTransitionAnimationEngine.prototype._namespaceList;\n/** @type {?} */\nTransitionAnimationEngine.prototype._flushFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype._whenQuietFns;\n/** @type {?} */\nTransitionAnimationEngine.prototype.namespacesByHostElement;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedEnterElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.collectedLeaveElements;\n/** @type {?} */\nTransitionAnimationEngine.prototype.onRemovalComplete;\n/** @type {?} */\nTransitionAnimationEngine.prototype.driver;\n/** @type {?} */\nTransitionAnimationEngine.prototype._normalizer;\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\nprivate _player: AnimationPlayer = new NoopAnimationPlayer();\nprivate _containsRealPlayer = false;\nprivate _queuedCallbacks: {[name: string]: (() => any)[]} = {};\nprivate _destroyed = false;\npublic parentPlayer: AnimationPlayer;\npublic markedForDestroy: boolean = false;\n/**\n * @param {?} namespaceId\n * @param {?} triggerName\n * @param {?} element\n */\nconstructor(public namespaceId: string,\npublic triggerName: string,\npublic element: any) {}\n/**\n * @return {?}\n */\nget queued() { return this._containsRealPlayer == false; }\n/**\n * @return {?}\n */\nget destroyed() { return this._destroyed; }\n/**\n * @param {?} player\n * @return {?}\n */\nsetRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    Object.keys(this._queuedCallbacks).forEach(phase => {\n      this._queuedCallbacks[phase].forEach(\n          callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks = {};\n    this._containsRealPlayer = true;\n  }\n/**\n * @return {?}\n */\ngetRealPlayer() { return this._player; }\n/**\n * @param {?} name\n * @param {?} callback\n * @return {?}\n */\nprivate _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n/**\n * @return {?}\n */\ninit(): void { this._player.init(); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this.queued ? false : this._player.hasStarted(); }\n/**\n * @return {?}\n */\nplay(): void { !this.queued && this._player.play(); }\n/**\n * @return {?}\n */\npause(): void { !this.queued && this._player.pause(); }\n/**\n * @return {?}\n */\nrestart(): void { !this.queued && this._player.restart(); }\n/**\n * @return {?}\n */\nfinish(): void { this._player.finish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._destroyed = true;\n    this._player.destroy();\n  }\n/**\n * @return {?}\n */\nreset(): void { !this.queued && this._player.reset(); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: any): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n/**\n * @return {?}\n */\ngetPosition(): number { return this.queued ? 0 : this._player.getPosition(); }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._player.totalTime; }\n}\n\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAnimationPlayer.prototype._player;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._containsRealPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._queuedCallbacks;\n/** @type {?} */\nTransitionAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.markedForDestroy;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.namespaceId;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.triggerName;\n/** @type {?} */\nTransitionAnimationPlayer.prototype.element;\n}\n\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map: Map<any, any[]>| {[key: string]: any}, key: any, value: any) {\n  let /** @type {?} */ currentValues: any[]|null|undefined;\n  if (map instanceof Map) {\n    currentValues = map.get(key);\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        map.delete(key);\n      }\n    }\n  } else {\n    currentValues = map[key];\n    if (currentValues) {\n      if (currentValues.length) {\n        const /** @type {?} */ index = currentValues.indexOf(value);\n        currentValues.splice(index, 1);\n      }\n      if (currentValues.length == 0) {\n        delete map[key];\n      }\n    }\n  }\n  return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value: any): string {\n  switch (typeof value) {\n    case 'boolean':\n      return value ? '1' : '0';\n    default:\n      return value != null ? value.toString() : null;\n  }\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element: any, value?: string) {\n  const /** @type {?} */ oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n/**\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(\n    driver: AnimationDriver, elements: any[], elementPropsMap: Map<any, Set<string>>,\n    defaultStyle: string): Map<any, ɵStyleData> {\n  const /** @type {?} */ cloakVals = elements.map(element => cloakElement(element));\n  const /** @type {?} */ valuesMap = new Map<any, ɵStyleData>();\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const /** @type {?} */ styles: ɵStyleData = {};\n    props.forEach(prop => {\n      const /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  elements.forEach((element, i) => cloakElement(element, cloakVals[i]));\n  return valuesMap;\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction createIsRootFilterFn(nodes: any): (node: any) => boolean {\n  const /** @type {?} */ nodeSet = new Set(nodes);\n  const /** @type {?} */ knownRootContainer = new Set();\n  let /** @type {?} */ isRoot: (node: any) => boolean;\n  isRoot = node => {\n    if (!node) return true;\n    if (nodeSet.has(node.parentNode)) return false;\n    if (knownRootContainer.has(node.parentNode)) return true;\n    if (isRoot(node.parentNode)) {\n      knownRootContainer.add(node);\n      return true;\n    }\n    return false;\n  };\n  return isRoot;\n}\n\nconst /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element: any, className: string): boolean {\n  if (element.classList) {\n    return element.classList.contains(className);\n  } else {\n    const /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n    return classes && classes[className];\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (!classes) {\n      classes = element[CLASSES_CACHE_KEY] = {};\n    }\n    classes[className] = true;\n  }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element: any, className: string) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    let /** @type {?} */ classes: {[className: string]: boolean} = element[CLASSES_CACHE_KEY];\n    if (classes) {\n      delete classes[className];\n    }\n  }\n}\n/**\n * @return {?}\n */\nfunction getBodyNode(): any|null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(\n    engine: TransitionAnimationEngine, element: any, players: AnimationPlayer[]) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationPlayer, AnimationTriggerMetadata} from '@angular/animations';\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\nexport class AnimationEngine {\nprivate _transitionEngine: TransitionAnimationEngine;\nprivate _timelineEngine: TimelineAnimationEngine;\nprivate _triggerCache: {[key: string]: AnimationTrigger} = {};\npublic onRemovalComplete = (element: any, context: any) => {};\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    this._transitionEngine = new TransitionAnimationEngine(driver, normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(driver, normalizer);\n\n    this._transitionEngine.onRemovalComplete =\n        (element: any, context: any) => { this.onRemovalComplete(element, context); }\n  }\n/**\n * @param {?} componentId\n * @param {?} namespaceId\n * @param {?} hostElement\n * @param {?} name\n * @param {?} metadata\n * @return {?}\n */\nregisterTrigger(\n      componentId: string, namespaceId: string, hostElement: any, name: string,\n      metadata: AnimationTriggerMetadata): void {\n    const /** @type {?} */ cacheKey = componentId + '-' + name;\n    let /** @type {?} */ trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const /** @type {?} */ errors: any[] = [];\n      const /** @type {?} */ ast = /** @type {?} */(( buildAnimationAst( /** @type {?} */((metadata as AnimationMetadata)), errors) as TriggerAst));\n      if (errors.length) {\n        throw new Error(\n            `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n      }\n      trigger = buildTrigger(name, ast);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} hostElement\n * @return {?}\n */\nregister(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} context\n * @return {?}\n */\ndestroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} parent\n * @param {?} insertBefore\n * @return {?}\n */\nonInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} context\n * @return {?}\n */\nonRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} property\n * @param {?} value\n * @return {?}\n */\nsetProperty(namespaceId: string, element: any, property: string, value: any): boolean {\n    // @@property\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const /** @type {?} */ args = /** @type {?} */(( value as any[]));\n      this._timelineEngine.command(id, element, action, args);\n      return false;\n    }\n    return this._transitionEngine.trigger(namespaceId, element, property, value);\n  }\n/**\n * @param {?} namespaceId\n * @param {?} element\n * @param {?} eventName\n * @param {?} eventPhase\n * @param {?} callback\n * @return {?}\n */\nlisten(\n      namespaceId: string, element: any, eventName: string, eventPhase: string,\n      callback: (event: any) => any): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n/**\n * @param {?=} microtaskId\n * @return {?}\n */\nflush(microtaskId: number = -1): void { this._transitionEngine.flush(microtaskId); }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] {\n    return ( /** @type {?} */((this._transitionEngine.players as AnimationPlayer[])))\n        .concat( /** @type {?} */((this._timelineEngine.players as AnimationPlayer[])));\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this._transitionEngine.whenRenderingDone(); }\n}\n\nfunction AnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationEngine.prototype._transitionEngine;\n/** @type {?} */\nAnimationEngine.prototype._timelineEngine;\n/** @type {?} */\nAnimationEngine.prototype._triggerCache;\n/** @type {?} */\nAnimationEngine.prototype.onRemovalComplete;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer} from '@angular/animations';\n\nimport {copyStyles, eraseStyles, setStyles} from '../../util';\n\nimport {DOMAnimation} from './dom_animation';\nexport class WebAnimationsPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _player: DOMAnimation;\nprivate _duration: number;\nprivate _delay: number;\nprivate _initialized = false;\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finalKeyframe: {[key: string]: string | number};\npublic time = 0;\npublic parentPlayer: AnimationPlayer|null = null;\npublic previousStyles: {[styleName: string]: string | number};\npublic currentSnapshot: {[styleName: string]: string | number} = {};\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @param {?=} previousPlayers\n */\nconstructor(\npublic element: any,\npublic keyframes: {[key: string]: string | number}[],\npublic options: {[key: string]: string | number},\nprivate previousPlayers: WebAnimationsPlayer[] = []) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n\n    this.previousStyles = {};\n    previousPlayers.forEach(player => {\n      let styles = player.currentSnapshot;\n      Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n    });\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n/**\n * @return {?}\n */\nprivate _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const /** @type {?} */ keyframes = this.keyframes.map(styles => copyStyles(styles, false));\n    const /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n    if (previousStyleProps.length) {\n      let /** @type {?} */ startingKeyframe = keyframes[0];\n      let /** @type {?} */ missingStyleProps: string[] = [];\n      previousStyleProps.forEach(prop => {\n        if (!startingKeyframe.hasOwnProperty(prop)) {\n          missingStyleProps.push(prop);\n        }\n        startingKeyframe[prop] = this.previousStyles[prop];\n      });\n\n      if (missingStyleProps.length) {\n        const /** @type {?} */ self = this;\n        // tslint:disable-next-line\n        for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n          let /** @type {?} */ kf = keyframes[i];\n          missingStyleProps.forEach(function(prop) {\n            kf[prop] = _computeStyle(self.element, prop);\n          });\n        }\n      }\n    }\n\n    this._player = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n    this._player.addEventListener('finish', () => this._onFinish());\n  }\n/**\n * @return {?}\n */\nprivate _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this._player.pause();\n    }\n  }\n/**\n * \\@internal\n * @param {?} element\n * @param {?} keyframes\n * @param {?} options\n * @return {?}\n */\n_triggerWebAnimation(element: any, keyframes: any[], options: any): DOMAnimation {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return /** @type {?} */(( element['animate'](keyframes, options) as DOMAnimation));\n  }\n/**\n * @return {?}\n */\nget domPlayer() { return this._player; }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nplay(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n    this._player.play();\n  }\n/**\n * @return {?}\n */\npause(): void {\n    this.init();\n    this._player.pause();\n  }\n/**\n * @return {?}\n */\nfinish(): void {\n    this.init();\n    this._onFinish();\n    this._player.finish();\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @return {?}\n */\nprivate _resetDomPlayerState() {\n    if (this._player) {\n      this._player.cancel();\n    }\n  }\n/**\n * @return {?}\n */\nrestart(): void {\n    this.reset();\n    this.play();\n  }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._resetDomPlayerState();\n      this._onFinish();\n      this._destroyed = true;\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._player.currentTime = p * this.time; }\n/**\n * @return {?}\n */\ngetPosition(): number { return this._player.currentTime / this.time; }\n/**\n * @return {?}\n */\nget totalTime(): number { return this._delay + this._duration; }\n/**\n * @return {?}\n */\nbeforeDestroy() {\n    const /** @type {?} */ styles: {[key: string]: string | number} = {};\n    if (this.hasStarted()) {\n      Object.keys(this._finalKeyframe).forEach(prop => {\n        if (prop != 'offset') {\n          styles[prop] =\n              this._finished ? this._finalKeyframe[prop] : _computeStyle(this.element, prop);\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n}\n\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDoneFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onStartFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._onDestroyFns;\n/** @type {?} */\nWebAnimationsPlayer.prototype._player;\n/** @type {?} */\nWebAnimationsPlayer.prototype._duration;\n/** @type {?} */\nWebAnimationsPlayer.prototype._delay;\n/** @type {?} */\nWebAnimationsPlayer.prototype._initialized;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finished;\n/** @type {?} */\nWebAnimationsPlayer.prototype._started;\n/** @type {?} */\nWebAnimationsPlayer.prototype._destroyed;\n/** @type {?} */\nWebAnimationsPlayer.prototype._finalKeyframe;\n/** @type {?} */\nWebAnimationsPlayer.prototype.time;\n/** @type {?} */\nWebAnimationsPlayer.prototype.parentPlayer;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousStyles;\n/** @type {?} */\nWebAnimationsPlayer.prototype.currentSnapshot;\n/** @type {?} */\nWebAnimationsPlayer.prototype.element;\n/** @type {?} */\nWebAnimationsPlayer.prototype.keyframes;\n/** @type {?} */\nWebAnimationsPlayer.prototype.options;\n/** @type {?} */\nWebAnimationsPlayer.prototype.previousPlayers;\n}\n\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element: any, prop: string): string {\n  return ( /** @type {?} */((<any>window.getComputedStyle(element))))[prop];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../animation_driver';\nimport {containsElement, invokeQuery, matchesElement} from '../shared';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\nexport class WebAnimationsDriver implements AnimationDriver {\n/**\n * @param {?} element\n * @param {?} selector\n * @return {?}\n */\nmatchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n/**\n * @param {?} elm1\n * @param {?} elm2\n * @return {?}\n */\ncontainsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n/**\n * @param {?} element\n * @param {?} selector\n * @param {?} multi\n * @return {?}\n */\nquery(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n/**\n * @param {?} element\n * @param {?} prop\n * @param {?=} defaultValue\n * @return {?}\n */\ncomputeStyle(element: any, prop: string, defaultValue?: string): string {\n    return /** @type {?} */(( ( /** @type {?} */((window.getComputedStyle(element) as any)))[prop] as string));\n  }\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @param {?=} previousPlayers\n * @return {?}\n */\nanimate(\n      element: any, keyframes: ɵStyleData[], duration: number, delay: number, easing: string,\n      previousPlayers: AnimationPlayer[] = []): WebAnimationsPlayer {\n    const /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n    const /** @type {?} */ playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */(( <WebAnimationsPlayer[]>previousPlayers.filter(\n        player => { return player instanceof WebAnimationsPlayer; })));\n    return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n  }\n}\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n  return typeof Element !== 'undefined' && typeof( /** @type {?} */((<any>Element))).prototype['animate'] === 'function';\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateChildOptions, AnimateTimings, AnimationOptions, AnimationQueryOptions, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, AstVisitor, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTimelineInstruction, createTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(\n    driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData = {},\n    finalStyles: ɵStyleData = {}, options: AnimationOptions,\n    subInstructions?: ElementInstructionMap, errors: any[] = []): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n      driver, rootElement, ast, startingStyles, finalStyles, options, subInstructions, errors);\n}\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} startingStyles\n * @param {?} finalStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nbuildKeyframes(\n      driver: AnimationDriver, rootElement: any, ast: Ast, startingStyles: ɵStyleData,\n      finalStyles: ɵStyleData, options: AnimationOptions, subInstructions?: ElementInstructionMap,\n      errors: any[] = []): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, errors, []);\n    context.options = options;\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    ast.visit(this, context);\n\n    // this checks to see if an actual animation happened\n    const /** @type {?} */ timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    if (timelines.length && Object.keys(finalStyles).length) {\n      const /** @type {?} */ tl = timelines[timelines.length - 1];\n      if (!tl.allowOnlyTimelineStyles()) {\n        tl.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n                              [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n    if (elementInstructions) {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n      const /** @type {?} */ endTime = this._visitSubInstructions(\n          elementInstructions, innerContext, /** @type {?} */(( innerContext.options as AnimateChildOptions)));\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} instructions\n * @param {?} context\n * @param {?} options\n * @return {?}\n */\nprivate _visitSubInstructions(\n      instructions: AnimationTimelineInstruction[], context: AnimationTimelineContext,\n      options: AnimateChildOptions): number {\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    let /** @type {?} */ furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const /** @type {?} */ instructionTimings =\n            context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime =\n            Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ subContextCount = context.subContextCount;\n    let /** @type {?} */ ctx = context;\n    const /** @type {?} */ options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode instanceof StyleAst) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const /** @type {?} */ delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => s.visit(this, ctx));\n\n      // this is here just incase the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ innerTimelines: TimelineBuilder[] = [];\n    let /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach(s => {\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      s.visit(this, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(\n        timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if (ast instanceof DynamicTimingAst) {\n      const /** @type {?} */ strValue = context.params ?\n          interpolateParams(ast.value, context.params, context.errors) :\n          ast.value.toString();\n      return resolveTiming(strValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timings = context.currentAnimateTimings = this.visitTiming(ast.timings, context);\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const /** @type {?} */ style = ast.style;\n    if (style instanceof KeyframesAst) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle( /** @type {?} */((style as StyleAst)), context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ timeline = context.currentTimeline;\n    const /** @type {?} */ timings = /** @type {?} */(( context.currentAnimateTimings));\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.getCurrentStyleProperties().length) {\n      timeline.forwardFrame();\n    }\n\n    const /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ startTime = ( /** @type {?} */((context.currentTimeline))).duration;\n    const /** @type {?} */ duration = currentAnimateTimings.duration;\n    const /** @type {?} */ innerContext = context.createSubContext();\n    const /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach(step => {\n      const /** @type {?} */ offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n    const /** @type {?} */ options = /** @type {?} */(( (ast.options || {}) as AnimationQueryOptions));\n    const /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode instanceof StyleAst ||\n                  (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let /** @type {?} */ furthestTime = startTime;\n    const /** @type {?} */ elms = context.invokeQuery(\n        ast.selector, ast.originalSelector, ast.limit, ast.includeSelf,\n        options.optional ? true : false, context.errors);\n\n    context.currentQueryTotal = elms.length;\n    let /** @type {?} */ sameElementTimeline: TimelineBuilder|null = null;\n    elms.forEach((element, i) => {\n\n      context.currentQueryIndex = i;\n      const /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      ast.animation.visit(this, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const /** @type {?} */ parentContext = /** @type {?} */(( context.parentContext));\n    const /** @type {?} */ tl = context.currentTimeline;\n    const /** @type {?} */ timings = ast.timings;\n    const /** @type {?} */ duration = Math.abs(timings.duration);\n    const /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n    let /** @type {?} */ delay = duration * context.currentQueryIndex;\n\n    let /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const /** @type {?} */ timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const /** @type {?} */ startingTime = timeline.currentTime;\n    ast.animation.visit(this, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n        (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number; value: string | number;\n};\n\nconst /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */(( <Ast>{}));\nexport class AnimationTimelineContext {\npublic parentContext: AnimationTimelineContext|null = null;\npublic currentTimeline: TimelineBuilder;\npublic currentAnimateTimings: AnimateTimings|null = null;\npublic previousNode: Ast = DEFAULT_NOOP_PREVIOUS_NODE;\npublic subContextCount = 0;\npublic options: AnimationOptions = {};\npublic currentQueryIndex: number = 0;\npublic currentQueryTotal: number = 0;\npublic currentStaggerTime: number = 0;\n/**\n * @param {?} _driver\n * @param {?} element\n * @param {?} subInstructions\n * @param {?} errors\n * @param {?} timelines\n * @param {?=} initialTimeline\n */\nconstructor(\nprivate _driver: AnimationDriver,\npublic element: any,\npublic subInstructions: ElementInstructionMap,\npublic errors: any[],\npublic timelines: TimelineBuilder[], initialTimeline?: TimelineBuilder) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(element, 0);\n    timelines.push(this.currentTimeline);\n  }\n/**\n * @return {?}\n */\nget params() { return this.options.params; }\n/**\n * @param {?} options\n * @param {?=} skipIfExists\n * @return {?}\n */\nupdateOptions(options: AnimationOptions|null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const /** @type {?} */ newOptions = /** @type {?} */(( options as any));\n    let /** @type {?} */ optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      ( /** @type {?} */((optionsToUpdate as any))).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const /** @type {?} */ newParams = newOptions.params;\n    if (newParams) {\n      let /** @type {?} */ paramsToUpdate: {[name: string]: any} = /** @type {?} */(( optionsToUpdate.params));\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n/**\n * @return {?}\n */\nprivate _copyOptions() {\n    const /** @type {?} */ options: AnimationOptions = {};\n    if (this.options) {\n      const /** @type {?} */ oldParams = this.options.params;\n      if (oldParams) {\n        const /** @type {?} */ params: {[name: string]: any} = options['params'] = {};\n        Object.keys(this.options.params).forEach(name => { params[name] = oldParams[name]; });\n      }\n    }\n    return options;\n  }\n/**\n * @param {?=} options\n * @param {?=} element\n * @param {?=} newTime\n * @return {?}\n */\ncreateSubContext(options: AnimationOptions|null = null, element?: any, newTime?: number):\n      AnimationTimelineContext {\n    const /** @type {?} */ target = element || this.element;\n    const /** @type {?} */ context = new AnimationTimelineContext(\n        this._driver, target, this.subInstructions, this.errors, this.timelines,\n        this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n/**\n * @param {?=} newTime\n * @return {?}\n */\ntransformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n/**\n * @param {?} instruction\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nappendInstructionToTimeline(\n      instruction: AnimationTimelineInstruction, duration: number|null,\n      delay: number|null): AnimateTimings {\n    const /** @type {?} */ updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const /** @type {?} */ builder = new SubTimelineBuilder(\n        instruction.element, instruction.keyframes, instruction.preStyleProps,\n        instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nincrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n/**\n * @param {?} selector\n * @param {?} originalSelector\n * @param {?} limit\n * @param {?} includeSelf\n * @param {?} optional\n * @param {?} errors\n * @return {?}\n */\ninvokeQuery(\n      selector: string, originalSelector: string, limit: number, includeSelf: boolean,\n      optional: boolean, errors: any[]): any[] {\n    let /** @type {?} */ results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {  // if :self is only used then the selector is empty\n      const /** @type {?} */ multi = limit != 1;\n      results.push(...this._driver.query(this.element, selector, multi));\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(\n          `\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n    }\n    return results;\n  }\n}\n\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTimelineContext.prototype.parentContext;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentTimeline;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationTimelineContext.prototype.previousNode;\n/** @type {?} */\nAnimationTimelineContext.prototype.subContextCount;\n/** @type {?} */\nAnimationTimelineContext.prototype.options;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryIndex;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentQueryTotal;\n/** @type {?} */\nAnimationTimelineContext.prototype.currentStaggerTime;\n/** @type {?} */\nAnimationTimelineContext.prototype._driver;\n/** @type {?} */\nAnimationTimelineContext.prototype.element;\n/** @type {?} */\nAnimationTimelineContext.prototype.subInstructions;\n/** @type {?} */\nAnimationTimelineContext.prototype.errors;\n/** @type {?} */\nAnimationTimelineContext.prototype.timelines;\n}\n\nexport class TimelineBuilder {\npublic duration: number = 0;\npublic easing: string|null;\nprivate _previousKeyframe: ɵStyleData = {};\nprivate _currentKeyframe: ɵStyleData = {};\nprivate _keyframes = new Map<number, ɵStyleData>();\nprivate _styleSummary: {[prop: string]: StyleAtTime} = {};\nprivate _localTimelineStyles: ɵStyleData;\nprivate _globalTimelineStyles: ɵStyleData;\nprivate _pendingStyles: ɵStyleData = {};\nprivate _backFill: ɵStyleData = {};\nprivate _currentEmptyStepKeyframe: ɵStyleData|null = null;\n/**\n * @param {?} element\n * @param {?} startTime\n * @param {?=} _elementTimelineStylesLookup\n */\nconstructor(\npublic element: any,\npublic startTime: number,\nprivate _elementTimelineStylesLookup?: Map<any, ɵStyleData>) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleData>();\n    }\n\n    this._localTimelineStyles = Object.create(this._backFill, {});\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element) !;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.getCurrentStyleProperties().length > 0;\n      default:\n        return true;\n    }\n  }\n/**\n * @return {?}\n */\ngetCurrentStyleProperties(): string[] { return Object.keys(this._currentKeyframe); }\n/**\n * @return {?}\n */\nget currentTime() { return this.startTime + this.duration; }\n/**\n * @param {?} delay\n * @return {?}\n */\ndelayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n/**\n * @param {?} element\n * @param {?=} currentTime\n * @return {?}\n */\nfork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n        element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n/**\n * @return {?}\n */\nprivate _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = /** @type {?} */(( this._keyframes.get(this.duration)));\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = Object.create(this._backFill, {});\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n/**\n * @return {?}\n */\nforwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} time\n * @return {?}\n */\nforwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n/**\n * @param {?} prop\n * @param {?} value\n * @return {?}\n */\nprivate _updateStyle(prop: string, value: string|number) {\n    this._localTimelineStyles[prop] = value;\n    this._globalTimelineStyles[prop] = value;\n    this._styleSummary[prop] = {time: this.currentTime, value};\n  }\n/**\n * @return {?}\n */\nallowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n/**\n * @param {?} easing\n * @return {?}\n */\napplyEmptyStep(easing: string|null) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    Object.keys(this._globalTimelineStyles).forEach(prop => {\n      this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n      this._currentKeyframe[prop] = AUTO_STYLE;\n    });\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n/**\n * @param {?} input\n * @param {?} easing\n * @param {?} errors\n * @param {?=} options\n * @return {?}\n */\nsetStyles(\n      input: (ɵStyleData|string)[], easing: string|null, errors: any[],\n      options?: AnimationOptions) {\n    if (easing) {\n      this._previousKeyframe['easing'] = easing;\n    }\n\n    const /** @type {?} */ params = (options && options.params) || {};\n    const /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n      this._pendingStyles[prop] = val;\n      if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n        this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n            this._globalTimelineStyles[prop] :\n            AUTO_STYLE;\n      }\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\napplyStylesToKeyframe() {\n    const /** @type {?} */ styles = this._pendingStyles;\n    const /** @type {?} */ props = Object.keys(styles);\n    if (props.length == 0) return;\n\n    this._pendingStyles = {};\n\n    props.forEach(prop => {\n      const /** @type {?} */ val = styles[prop];\n      this._currentKeyframe[prop] = val;\n    });\n\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      if (!this._currentKeyframe.hasOwnProperty(prop)) {\n        this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n      }\n    });\n  }\n/**\n * @return {?}\n */\nsnapshotCurrentStyles() {\n    Object.keys(this._localTimelineStyles).forEach(prop => {\n      const /** @type {?} */ val = this._localTimelineStyles[prop];\n      this._pendingStyles[prop] = val;\n      this._updateStyle(prop, val);\n    });\n  }\n/**\n * @return {?}\n */\ngetFinalKeyframe() { return this._keyframes.get(this.duration); }\n/**\n * @return {?}\n */\nget properties() {\n    const /** @type {?} */ properties: string[] = [];\n    for (let /** @type {?} */ prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n/**\n * @param {?} timeline\n * @return {?}\n */\nmergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    Object.keys(timeline._styleSummary).forEach(prop => {\n      const /** @type {?} */ details0 = this._styleSummary[prop];\n      const /** @type {?} */ details1 = timeline._styleSummary[prop];\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const /** @type {?} */ preStyleProps = new Set<string>();\n    const /** @type {?} */ postStyleProps = new Set<string>();\n    const /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let /** @type {?} */ finalKeyframes: ɵStyleData[] = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n      Object.keys(finalKeyframe).forEach(prop => {\n        const /** @type {?} */ value = finalKeyframe[prop];\n        if (value == PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value == AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe['offset'] = time / this.duration;\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const /** @type {?} */ preProps: string[] = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const /** @type {?} */ postProps: string[] = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const /** @type {?} */ kf0 = finalKeyframes[0];\n      const /** @type {?} */ kf1 = copyObj(kf0);\n      kf0['offset'] = 0;\n      kf1['offset'] = 1;\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n        this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime,\n        this.easing, false);\n  }\n}\n\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineBuilder.prototype.duration;\n/** @type {?} */\nTimelineBuilder.prototype.easing;\n/** @type {?} */\nTimelineBuilder.prototype._previousKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._currentKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype._keyframes;\n/** @type {?} */\nTimelineBuilder.prototype._styleSummary;\n/** @type {?} */\nTimelineBuilder.prototype._localTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._globalTimelineStyles;\n/** @type {?} */\nTimelineBuilder.prototype._pendingStyles;\n/** @type {?} */\nTimelineBuilder.prototype._backFill;\n/** @type {?} */\nTimelineBuilder.prototype._currentEmptyStepKeyframe;\n/** @type {?} */\nTimelineBuilder.prototype.element;\n/** @type {?} */\nTimelineBuilder.prototype.startTime;\n/** @type {?} */\nTimelineBuilder.prototype._elementTimelineStylesLookup;\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\npublic timings: AnimateTimings;\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} timings\n * @param {?=} _stretchStartingKeyframe\n */\nconstructor(\npublic element: any,\npublic keyframes: ɵStyleData[],\npublic preStyleProps: string[],\npublic postStyleProps: string[], timings: AnimateTimings,\nprivate _stretchStartingKeyframe: boolean = false) {\n    super(element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n/**\n * @return {?}\n */\ncontainsAnimation(): boolean { return this.keyframes.length > 1; }\n/**\n * @return {?}\n */\nbuildKeyframes(): AnimationTimelineInstruction {\n    let /** @type {?} */ keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const /** @type {?} */ newKeyframes: ɵStyleData[] = [];\n      const /** @type {?} */ totalTime = duration + delay;\n      const /** @type {?} */ startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n      newFirstKeyframe['offset'] = 0;\n      newKeyframes.push(newFirstKeyframe);\n\n      const /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n      oldFirstKeyframe['offset'] = roundOffset(startingGap);\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still renderered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const /** @type {?} */ limit = keyframes.length - 1;\n      for (let /** @type {?} */ i = 1; i <= limit; i++) {\n        let /** @type {?} */ kf = copyStyles(keyframes[i], false);\n        const /** @type {?} */ oldOffset = /** @type {?} */(( kf['offset'] as number));\n        const /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n        kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n        this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing,\n        true);\n  }\n}\n\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nSubTimelineBuilder.prototype.timings;\n/** @type {?} */\nSubTimelineBuilder.prototype.element;\n/** @type {?} */\nSubTimelineBuilder.prototype.keyframes;\n/** @type {?} */\nSubTimelineBuilder.prototype.preStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype.postStyleProps;\n/** @type {?} */\nSubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input: (ɵStyleData | string)[], allStyles: ɵStyleData) {\n  const /** @type {?} */ styles: ɵStyleData = {};\n  let /** @type {?} */ allProperties: string[];\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || Object.keys(allStyles);\n      allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n    } else {\n      copyStyles( /** @type {?} */((token as ɵStyleData)), false, styles);\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {normalizeStyles} from '../util';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class Animation {\nprivate _animationAst: Ast;\n/**\n * @param {?} _driver\n * @param {?} input\n */\nconstructor(private _driver: AnimationDriver, input: AnimationMetadata|AnimationMetadata[]) {\n    const errors: any[] = [];\n    const ast = buildAnimationAst(input, errors);\n    if (errors.length) {\n      const errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    this._animationAst = ast;\n  }\n/**\n * @param {?} element\n * @param {?} startingStyles\n * @param {?} destinationStyles\n * @param {?} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuildTimelines(\n      element: any, startingStyles: ɵStyleData|ɵStyleData[],\n      destinationStyles: ɵStyleData|ɵStyleData[], options: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[] {\n    const /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */((\n                                                  <ɵStyleData>startingStyles));\n    const /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */((\n                                                    <ɵStyleData>destinationStyles));\n    const /** @type {?} */ errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const /** @type {?} */ result = buildAnimationTimelines(\n        this._driver, element, this._animationAst, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n    return result;\n  }\n}\n\nfunction Animation_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimation.prototype._animationAst;\n/** @type {?} */\nAnimation.prototype._driver;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @experimental Animation support is experimental.\n */\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: string[]): string;\n  abstract normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: string[]): string { return propertyName; }\n\n  normalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n/**\n * @param {?} propertyName\n * @param {?} errors\n * @return {?}\n */\nnormalizePropertyName(propertyName: string, errors: string[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n/**\n * @param {?} userProvidedProperty\n * @param {?} normalizedProperty\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nnormalizeStyleValue(\n      userProvidedProperty: string, normalizedProperty: string, value: string|number,\n      errors: string[]): string {\n    let /** @type {?} */ unit: string = '';\n    const /** @type {?} */ strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n\nconst /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap(\n    'width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n        .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys: string[]): {[key: string]: boolean} {\n  const /** @type {?} */ map: {[key: string]: boolean} = {};\n  keys.forEach(key => map[key] = true);\n  return map;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationOptions, ɵStyleData} from '@angular/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetAsInMap} from '../render/shared';\nimport {iteratorToArray, mergeAnimationOptions} from '../util';\n\nimport {TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {AnimationTransitionInstruction, createTransitionInstruction} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nexport class AnimationTransitionFactory {\n/**\n * @param {?} _triggerName\n * @param {?} ast\n * @param {?} _stateStyles\n */\nconstructor(\nprivate _triggerName: string,\npublic ast: TransitionAst,\nprivate _stateStyles: {[stateName: string]: ɵStyleData}) {}\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatch(currentState: any, nextState: any): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n  }\n/**\n * @param {?} driver\n * @param {?} element\n * @param {?} currentState\n * @param {?} nextState\n * @param {?=} options\n * @param {?=} subInstructions\n * @return {?}\n */\nbuild(\n      driver: AnimationDriver, element: any, currentState: any, nextState: any,\n      options?: AnimationOptions,\n      subInstructions?: ElementInstructionMap): AnimationTransitionInstruction|undefined {\n    const /** @type {?} */ animationOptions = mergeAnimationOptions(this.ast.options || {}, options || {});\n\n    const /** @type {?} */ backupStateStyles = this._stateStyles['*'] || {};\n    const /** @type {?} */ currentStateStyles = this._stateStyles[currentState] || backupStateStyles;\n    const /** @type {?} */ nextStateStyles = this._stateStyles[nextState] || backupStateStyles;\n\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ timelines = buildAnimationTimelines(\n        driver, element, this.ast.animation, currentStateStyles, nextStateStyles, animationOptions,\n        subInstructions, errors);\n\n    if (errors.length) {\n      const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n      throw new Error(errorMessage);\n    }\n\n    const /** @type {?} */ preStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ postStyleMap = new Map<any, {[prop: string]: boolean}>();\n    const /** @type {?} */ queriedElements = new Set<any>();\n    timelines.forEach(tl => {\n      const /** @type {?} */ elm = tl.element;\n      const /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n      tl.preStyleProps.forEach(prop => preProps[prop] = true);\n\n      const /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n      tl.postStyleProps.forEach(prop => postProps[prop] = true);\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    const /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(\n        element, this._triggerName, currentState, nextState, nextState === 'void',\n        currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap,\n        postStyleMap);\n  }\n}\n\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTransitionFactory.prototype._triggerName;\n/** @type {?} */\nAnimationTransitionFactory.prototype.ast;\n/** @type {?} */\nAnimationTransitionFactory.prototype._stateStyles;\n}\n\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(\n    matchFns: TransitionMatcherFn[], currentState: any, nextState: any): boolean {\n  return matchFns.some(fn => fn(currentState, nextState));\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\n\nimport {copyStyles} from '../util';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationTransitionFactory} from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name: string, ast: TriggerAst): AnimationTrigger {\n  return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class AnimationTrigger {\npublic transitionFactories: AnimationTransitionFactory[] = [];\npublic fallbackTransition: AnimationTransitionFactory;\npublic states: {[stateName: string]: ɵStyleData} = {};\n/**\n * @param {?} name\n * @param {?} ast\n */\nconstructor(public name: string,\npublic ast: TriggerAst) {\n    ast.states.forEach(ast => {\n      const obj = this.states[ast.name] = {};\n      ast.style.styles.forEach(styleTuple => {\n        if (typeof styleTuple == 'object') {\n          copyStyles(styleTuple as ɵStyleData, false, obj);\n        }\n      });\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n/**\n * @return {?}\n */\nget containsQueries() { return this.ast.queryCount > 0; }\n/**\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nmatchTransition(currentState: any, nextState: any): AnimationTransitionFactory|null {\n    const /** @type {?} */ entry = this.transitionFactories.find(f => f.match(currentState, nextState));\n    return entry || null;\n  }\n}\n\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationTrigger.prototype.transitionFactories;\n/** @type {?} */\nAnimationTrigger.prototype.fallbackTransition;\n/** @type {?} */\nAnimationTrigger.prototype.states;\n/** @type {?} */\nAnimationTrigger.prototype.name;\n/** @type {?} */\nAnimationTrigger.prototype.ast;\n}\n\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(\n    triggerName: string, states: {[stateName: string]: ɵStyleData}): AnimationTransitionFactory {\n  const /** @type {?} */ matchers = [(fromState: any, toState: any) => true];\n  const /** @type {?} */ animation = new SequenceAst([]);\n  const /** @type {?} */ transition = new TransitionAst(matchers, animation);\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj: {[key: string]: any}, key1: string, key2: string) {\n  if (obj.hasOwnProperty(key1)) {\n    if (!obj.hasOwnProperty(key2)) {\n      obj[key2] = obj[key1];\n    }\n  } else if (obj.hasOwnProperty(key2)) {\n    obj[key1] = obj[key2];\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimationMetadata, AnimationOptions, AnimationPlayer, ɵStyleData} from '@angular/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\n\nimport {AnimationDriver} from './animation_driver';\nimport {getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer} from './shared';\n\nconst /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nexport class TimelineAnimationEngine {\nprivate _animations: {[id: string]: Ast} = {};\nprivate _playersById: {[id: string]: AnimationPlayer} = {};\npublic players: AnimationPlayer[] = [];\n/**\n * @param {?} _driver\n * @param {?} _normalizer\n */\nconstructor(private _driver: AnimationDriver,\nprivate _normalizer: AnimationStyleNormalizer) {}\n/**\n * @param {?} id\n * @param {?} metadata\n * @return {?}\n */\nregister(id: string, metadata: AnimationMetadata|AnimationMetadata[]) {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = buildAnimationAst(metadata, errors);\n    if (errors.length) {\n      throw new Error(\n          `Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    } else {\n      this._animations[id] = ast;\n    }\n  }\n/**\n * @param {?} i\n * @param {?} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nprivate _buildPlayer(\n      i: AnimationTimelineInstruction, preStyles: ɵStyleData,\n      postStyles?: ɵStyleData): AnimationPlayer {\n    const /** @type {?} */ element = i.element;\n    const /** @type {?} */ keyframes = normalizeKeyframes(\n        this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const /** @type {?} */ errors: any[] = [];\n    const /** @type {?} */ ast = this._animations[id];\n    let /** @type {?} */ instructions: AnimationTimelineInstruction[];\n\n    const /** @type {?} */ autoStylesMap = new Map<any, ɵStyleData>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n          this._driver, element, ast, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n        inst.postStyleProps.forEach(prop => styles[prop] = null);\n      });\n    } else {\n      errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw new Error(\n          `Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      Object.keys(styles).forEach(\n          prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n    });\n\n    const /** @type {?} */ players = instructions.map(i => {\n      const /** @type {?} */ styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, {}, styles);\n    });\n    const /** @type {?} */ player = optimizeGroupPlayer(players);\n    this._playersById[id] = player;\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\ndestroy(id: string) {\n    const /** @type {?} */ player = this._getPlayer(id);\n    player.destroy();\n    delete this._playersById[id];\n    const /** @type {?} */ index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nprivate _getPlayer(id: string): AnimationPlayer {\n    const /** @type {?} */ player = this._playersById[id];\n    if (!player) {\n      throw new Error(`Unable to find the timeline player referenced by ${id}`);\n    }\n    return player;\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(id: string, element: string, eventName: string, callback: (event: any) => any):\n      () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\ncommand(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, /** @type {?} */(( args[0] as AnimationMetadata | AnimationMetadata[])));\n      return;\n    }\n\n    if (command == 'create') {\n      const /** @type {?} */ options = /** @type {?} */(( (args[0] || {}) as AnimationOptions));\n      this.create(id, element, options);\n      return;\n    }\n\n    const /** @type {?} */ player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat( /** @type {?} */((args[0] as string))));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nTimelineAnimationEngine.prototype._animations;\n/** @type {?} */\nTimelineAnimationEngine.prototype._playersById;\n/** @type {?} */\nTimelineAnimationEngine.prototype.players;\n/** @type {?} */\nTimelineAnimationEngine.prototype._driver;\n/** @type {?} */\nTimelineAnimationEngine.prototype._normalizer;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationMetadata, AnimationOptions, sequence, ɵStyleData} from '@angular/animations';\n\nexport const /** @type {?} */ ONE_SECOND = 1000;\n\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const /** @type {?} */ matches = ( /** @type {?} */((value as string))).match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:  // ms or something else\n      return value;\n  }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(\n    timings: string | number | AnimateTimings, errors: any[], allowNegativeValues?: boolean) {\n  return timings.hasOwnProperty('duration') ? /** @type {?} */((\n      <AnimateTimings>timings)) :\n      parseTimeExpression( /** @type {?} */((<string|number>timings)), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(\n    exp: string | number, errors: string[], allowNegativeValues?: boolean): AnimateTimings {\n  const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let /** @type {?} */ duration: number;\n  let /** @type {?} */ delay: number = 0;\n  let /** @type {?} */ easing: string = '';\n  if (typeof exp === 'string') {\n    const /** @type {?} */ matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(`The provided timing value \"${exp}\" is invalid.`);\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const /** @type {?} */ delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n    }\n\n    const /** @type {?} */ easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = /** @type {?} */(( <number>exp));\n  }\n\n  if (!allowNegativeValues) {\n    let /** @type {?} */ containsErrors = false;\n    let /** @type {?} */ startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(`Duration values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(`Delay values below 0 are not allowed for this animation step.`);\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n    }\n  }\n\n  return {duration, delay, easing};\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(\n    obj: {[key: string]: any}, destination: {[key: string]: any} = {}): {[key: string]: any} {\n  Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n  return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles: ɵStyleData | ɵStyleData[]): ɵStyleData {\n  const /** @type {?} */ normalizedStyles: ɵStyleData = {};\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, false, normalizedStyles));\n  } else {\n    copyStyles(styles, false, normalizedStyles);\n  }\n  return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(\n    styles: ɵStyleData, readPrototype: boolean, destination: ɵStyleData = {}): ɵStyleData {\n  if (readPrototype) {\n    // we make use of a for-in loop so that the\n    // prototypically inherited properties are\n    // revealed from the backFill map\n    for (let /** @type {?} */ prop in styles) {\n      destination[prop] = styles[prop];\n    }\n  } else {\n    copyObj(styles, destination);\n  }\n  return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = styles[prop];\n    });\n  }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element: any, styles: ɵStyleData) {\n  if (element['style']) {\n    Object.keys(styles).forEach(prop => {\n      const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    });\n  }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps: AnimationMetadata | AnimationMetadata[]):\n    AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return /** @type {?} */(( steps as AnimationMetadata));\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(\n    value: string | number, options: AnimationOptions, errors: any[]) {\n  const /** @type {?} */ params = options.params || {};\n  if (typeof value !== 'string') return;\n\n  const /** @type {?} */ matches = value.toString().match(PARAM_REGEX);\n  if (matches) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(\n            `Unable to resolve the local animation param ${varName} in the given list of values`);\n      }\n    });\n  }\n}\n\nconst /** @type {?} */ PARAM_REGEX = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(\n    value: string | number, params: {[name: string]: any}, errors: any[]): string|number {\n  const /** @type {?} */ original = value.toString();\n  const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n    let /** @type {?} */ localVal = params[varName];\n    // this means that the value was never overidden by the data passed in by the user\n    if (!params.hasOwnProperty(varName)) {\n      errors.push(`Please provide a value for the animation param ${varName}`);\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator: any): any[] {\n  const /** @type {?} */ arr: any[] = [];\n  let /** @type {?} */ item = iterator.next();\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n  return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(\n    source: AnimationOptions, destination: AnimationOptions): AnimationOptions {\n  if (source.params) {\n    const /** @type {?} */ p0 = source.params;\n    if (!destination.params) {\n      destination.params = {};\n    }\n    const /** @type {?} */ p1 = destination.params;\n    Object.keys(p0).forEach(param => {\n      if (!p1.hasOwnProperty(param)) {\n        p1[param] = p0[param];\n      }\n    });\n  }\n  return destination;\n}\n\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimateTimings, AnimationOptions, ɵStyleData} from '@angular/animations';\n\nconst /** @type {?} */ EMPTY_ANIMATION_OPTIONS: AnimationOptions = {};\n\nexport interface AstVisitor {\n  visitTrigger(ast: TriggerAst, context: any): any;\n  visitState(ast: StateAst, context: any): any;\n  visitTransition(ast: TransitionAst, context: any): any;\n  visitSequence(ast: SequenceAst, context: any): any;\n  visitGroup(ast: GroupAst, context: any): any;\n  visitAnimate(ast: AnimateAst, context: any): any;\n  visitStyle(ast: StyleAst, context: any): any;\n  visitKeyframes(ast: KeyframesAst, context: any): any;\n  visitReference(ast: ReferenceAst, context: any): any;\n  visitAnimateChild(ast: AnimateChildAst, context: any): any;\n  visitAnimateRef(ast: AnimateRefAst, context: any): any;\n  visitQuery(ast: QueryAst, context: any): any;\n  visitStagger(ast: StaggerAst, context: any): any;\n  visitTiming(ast: TimingAst, context: any): any;\n}\n/**\n * @abstract\n */\nexport abstract class Ast {\n/**\n * @abstract\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nvisit(ast: AstVisitor, context: any) {}\npublic options: AnimationOptions = EMPTY_ANIMATION_OPTIONS;\n/**\n * @return {?}\n */\nget params(): {[name: string]: any}|null { return this.options['params'] || null; }\n}\n\nfunction Ast_tsickle_Closure_declarations() {\n/** @type {?} */\nAst.prototype.options;\n}\n\nexport class TriggerAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} name\n * @param {?} states\n * @param {?} transitions\n */\nconstructor(public name: string,\npublic states: StateAst[],\npublic transitions: TransitionAst[]) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTrigger(this, context); }\n}\n\nfunction TriggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTriggerAst.prototype.queryCount;\n/** @type {?} */\nTriggerAst.prototype.depCount;\n/** @type {?} */\nTriggerAst.prototype.name;\n/** @type {?} */\nTriggerAst.prototype.states;\n/** @type {?} */\nTriggerAst.prototype.transitions;\n}\n\nexport class StateAst extends Ast {\n/**\n * @param {?} name\n * @param {?} style\n */\nconstructor(public name: string,\npublic style: StyleAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitState(this, context); }\n}\n\nfunction StateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStateAst.prototype.name;\n/** @type {?} */\nStateAst.prototype.style;\n}\n\nexport class TransitionAst extends Ast {\npublic queryCount: number = 0;\npublic depCount: number = 0;\n/**\n * @param {?} matchers\n * @param {?} animation\n */\nconstructor(\npublic matchers: ((fromState: string, toState: string) => boolean)[],\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTransition(this, context); }\n}\n\nfunction TransitionAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTransitionAst.prototype.queryCount;\n/** @type {?} */\nTransitionAst.prototype.depCount;\n/** @type {?} */\nTransitionAst.prototype.matchers;\n/** @type {?} */\nTransitionAst.prototype.animation;\n}\n\nexport class SequenceAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitSequence(this, context); }\n}\n\nfunction SequenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nSequenceAst.prototype.steps;\n}\n\nexport class GroupAst extends Ast {\n/**\n * @param {?} steps\n */\nconstructor(public steps: Ast[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitGroup(this, context); }\n}\n\nfunction GroupAst_tsickle_Closure_declarations() {\n/** @type {?} */\nGroupAst.prototype.steps;\n}\n\nexport class AnimateAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} style\n */\nconstructor(public timings: TimingAst,\npublic style: StyleAst|KeyframesAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimate(this, context); }\n}\n\nfunction AnimateAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateAst.prototype.timings;\n/** @type {?} */\nAnimateAst.prototype.style;\n}\n\nexport class StyleAst extends Ast {\npublic isEmptyStep = false;\n/**\n * @param {?} styles\n * @param {?} easing\n * @param {?} offset\n */\nconstructor(\npublic styles: (ɵStyleData|string)[],\npublic easing: string|null,\npublic offset: number|null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStyle(this, context); }\n}\n\nfunction StyleAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStyleAst.prototype.isEmptyStep;\n/** @type {?} */\nStyleAst.prototype.styles;\n/** @type {?} */\nStyleAst.prototype.easing;\n/** @type {?} */\nStyleAst.prototype.offset;\n}\n\nexport class KeyframesAst extends Ast {\n/**\n * @param {?} styles\n */\nconstructor(public styles: StyleAst[]) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitKeyframes(this, context); }\n}\n\nfunction KeyframesAst_tsickle_Closure_declarations() {\n/** @type {?} */\nKeyframesAst.prototype.styles;\n}\n\nexport class ReferenceAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitReference(this, context); }\n}\n\nfunction ReferenceAst_tsickle_Closure_declarations() {\n/** @type {?} */\nReferenceAst.prototype.animation;\n}\n\nexport class AnimateChildAst extends Ast {\nconstructor() { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateChild(this, context); }\n}\nexport class AnimateRefAst extends Ast {\n/**\n * @param {?} animation\n */\nconstructor(public animation: ReferenceAst) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitAnimateRef(this, context); }\n}\n\nfunction AnimateRefAst_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimateRefAst.prototype.animation;\n}\n\nexport class QueryAst extends Ast {\npublic originalSelector: string;\n/**\n * @param {?} selector\n * @param {?} limit\n * @param {?} optional\n * @param {?} includeSelf\n * @param {?} animation\n */\nconstructor(\npublic selector: string,\npublic limit: number,\npublic optional: boolean,\npublic includeSelf: boolean,\npublic animation: Ast) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitQuery(this, context); }\n}\n\nfunction QueryAst_tsickle_Closure_declarations() {\n/** @type {?} */\nQueryAst.prototype.originalSelector;\n/** @type {?} */\nQueryAst.prototype.selector;\n/** @type {?} */\nQueryAst.prototype.limit;\n/** @type {?} */\nQueryAst.prototype.optional;\n/** @type {?} */\nQueryAst.prototype.includeSelf;\n/** @type {?} */\nQueryAst.prototype.animation;\n}\n\nexport class StaggerAst extends Ast {\n/**\n * @param {?} timings\n * @param {?} animation\n */\nconstructor(public timings: AnimateTimings,\npublic animation: Ast) { super(); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitStagger(this, context); }\n}\n\nfunction StaggerAst_tsickle_Closure_declarations() {\n/** @type {?} */\nStaggerAst.prototype.timings;\n/** @type {?} */\nStaggerAst.prototype.animation;\n}\n\nexport class TimingAst extends Ast {\n/**\n * @param {?} duration\n * @param {?=} delay\n * @param {?=} easing\n */\nconstructor(\npublic duration: number,\npublic delay: number = 0,\npublic easing: string|null = null) {\n    super();\n  }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction TimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nTimingAst.prototype.duration;\n/** @type {?} */\nTimingAst.prototype.delay;\n/** @type {?} */\nTimingAst.prototype.easing;\n}\n\nexport class DynamicTimingAst extends TimingAst {\n/**\n * @param {?} value\n */\nconstructor(public value: string) { super(0, 0, ''); }\n/**\n * @param {?} visitor\n * @param {?} context\n * @return {?}\n */\nvisit(visitor: AstVisitor, context: any): any { return visitor.visitTiming(this, context); }\n}\n\nfunction DynamicTimingAst_tsickle_Closure_declarations() {\n/** @type {?} */\nDynamicTimingAst.prototype.value;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AUTO_STYLE, AnimateTimings, AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationOptions, AnimationQueryMetadata, AnimationQueryOptions, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata, style, ɵStyleData} from '@angular/animations';\n\nimport {getOrSetAsInMap} from '../render/shared';\nimport {ENTER_SELECTOR, LEAVE_SELECTOR, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, copyObj, normalizeAnimationEntry, resolveTiming, validateStyleParams} from '../util';\n\nimport {AnimateAst, AnimateChildAst, AnimateRefAst, Ast, DynamicTimingAst, GroupAst, KeyframesAst, QueryAst, ReferenceAst, SequenceAst, StaggerAst, StateAst, StyleAst, TimingAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationDslVisitor, visitAnimationNode} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst /** @type {?} */ SELF_TOKEN = ':self';\nconst /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(\n    metadata: AnimationMetadata | AnimationMetadata[], errors: any[]): Ast {\n  return new AnimationAstBuilderVisitor().build(metadata, errors);\n}\n\nconst /** @type {?} */ LEAVE_TOKEN = ':leave';\nconst /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\nconst /** @type {?} */ ENTER_TOKEN = ':enter';\nconst /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst /** @type {?} */ ROOT_SELECTOR = '';\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n/**\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nbuild(metadata: AnimationMetadata|AnimationMetadata[], errors: any[]): Ast {\n    const /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    return /** @type {?} */(( visitAnimationNode(this, normalizeAnimationEntry(metadata), context) as Ast));\n  }\n/**\n * @param {?} context\n * @return {?}\n */\nprivate _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = {};\n    context.collectedStyles[ROOT_SELECTOR] = {};\n    context.currentTime = 0;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTrigger(metadata: AnimationTriggerMetadata, context: AnimationAstBuilderContext):\n      TriggerAst {\n    let /** @type {?} */ queryCount = context.queryCount = 0;\n    let /** @type {?} */ depCount = context.depCount = 0;\n    const /** @type {?} */ states: StateAst[] = [];\n    const /** @type {?} */ transitions: TransitionAst[] = [];\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const /** @type {?} */ stateDef = /** @type {?} */(( def as AnimationStateMetadata));\n        const /** @type {?} */ name = stateDef.name;\n        name.split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const /** @type {?} */ transition = this.visitTransition( /** @type {?} */((def as AnimationTransitionMetadata)), context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(\n            'only state() and transition() definitions can sit inside of a trigger()');\n      }\n    });\n    const /** @type {?} */ ast = new TriggerAst(metadata.name, states, transitions);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = queryCount;\n    ast.depCount = depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    return new StateAst(metadata.name, this.visitStyle(metadata.styles, context));\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitTransition(metadata: AnimationTransitionMetadata, context: AnimationAstBuilderContext):\n      TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n    const /** @type {?} */ ast = new TransitionAst(matchers, entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    ast.queryCount = context.queryCount;\n    ast.depCount = context.depCount;\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitSequence(metadata: AnimationSequenceMetadata, context: AnimationAstBuilderContext):\n      SequenceAst {\n    const /** @type {?} */ ast = new SequenceAst(metadata.steps.map(s => visitAnimationNode(this, s, context)));\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const /** @type {?} */ currentTime = context.currentTime;\n    let /** @type {?} */ furthestTime = 0;\n    const /** @type {?} */ steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const /** @type {?} */ innerAst = visitAnimationNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    const /** @type {?} */ ast = new GroupAst(steps);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimate(metadata: AnimationAnimateMetadata, context: AnimationAstBuilderContext):\n      AnimateAst {\n    const /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n\n    let /** @type {?} */ styles: StyleAst|KeyframesAst;\n    let /** @type {?} */ styleMetadata: AnimationMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styles = this.visitKeyframes( /** @type {?} */((styleMetadata as AnimationKeyframesSequenceMetadata)), context);\n    } else {\n      let /** @type {?} */ styleMetadata = /** @type {?} */(( metadata.styles as AnimationStyleMetadata));\n      let /** @type {?} */ isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const /** @type {?} */ newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const /** @type {?} */ styleAst = this.visitStyle(styleMetadata, context);\n      styleAst.isEmptyStep = isEmpty;\n      styles = styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return new AnimateAst(timingAst, styles);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nprivate _makeStyleAst(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext):\n      StyleAst {\n    const /** @type {?} */ styles: (ɵStyleData | string)[] = [];\n    if (Array.isArray(metadata.styles)) {\n      ( /** @type {?} */((metadata.styles as(ɵStyleData | string)[]))).forEach(styleTuple => {\n        if (typeof styleTuple == 'string') {\n          if (styleTuple == AUTO_STYLE) {\n            styles.push( /** @type {?} */((styleTuple as string)));\n          } else {\n            context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n          }\n        } else {\n          styles.push( /** @type {?} */((styleTuple as ɵStyleData)));\n        }\n      })\n    } else {\n      styles.push(metadata.styles);\n    }\n\n    let /** @type {?} */ collectedEasing: string|null = null;\n    styles.forEach(styleData => {\n      if (isObject(styleData)) {\n        const /** @type {?} */ styleMap = /** @type {?} */(( styleData as ɵStyleData));\n        const /** @type {?} */ easing = styleMap['easing'];\n        if (easing) {\n          collectedEasing = /** @type {?} */(( easing as string));\n          delete styleMap['easing'];\n        }\n      }\n    });\n    return new StyleAst(styles, collectedEasing, metadata.offset);\n  }\n/**\n * @param {?} ast\n * @param {?} context\n * @return {?}\n */\nprivate _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const /** @type {?} */ timings = context.currentAnimateTimings;\n    let /** @type {?} */ endTime = context.currentTime;\n    let /** @type {?} */ startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple == 'string') return;\n\n      Object.keys(tuple).forEach(prop => {\n        const /** @type {?} */ collectedStyles = context.collectedStyles[ /** @type {?} */((context.currentQuerySelector))];\n        const /** @type {?} */ collectedEntry = collectedStyles[prop];\n        let /** @type {?} */ updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime &&\n              endTime <= collectedEntry.endTime) {\n            context.errors.push(\n                `The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles[prop] = {startTime, endTime};\n        }\n\n        if (context.options) {\n          validateStyleParams(tuple[prop], context.options, context.errors);\n        }\n      });\n    });\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitKeyframes(metadata: AnimationKeyframesSequenceMetadata, context: AnimationAstBuilderContext):\n      KeyframesAst {\n    if (!context.currentAnimateTimings) {\n      context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n      return new KeyframesAst([]);\n    }\n\n    const /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n\n    let /** @type {?} */ totalKeyframesWithOffsets = 0;\n    const /** @type {?} */ offsets: number[] = [];\n    let /** @type {?} */ offsetsOutOfOrder = false;\n    let /** @type {?} */ keyframesOutOfRange = false;\n    let /** @type {?} */ previousOffset: number = 0;\n\n    const /** @type {?} */ keyframes: StyleAst[] = metadata.steps.map(styles => {\n      const /** @type {?} */ style = this._makeStyleAst(styles, context);\n      let /** @type {?} */ offsetVal: number|null =\n          style.offset != null ? style.offset : consumeOffset(style.styles);\n      let /** @type {?} */ offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(`Please ensure that all keyframe offsets are in order`);\n    }\n\n    const /** @type {?} */ length = metadata.steps.length;\n    let /** @type {?} */ generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const /** @type {?} */ limit = length - 1;\n    const /** @type {?} */ currentTime = context.currentTime;\n    const /** @type {?} */ currentAnimateTimings = /** @type {?} */(( context.currentAnimateTimings));\n    const /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n      const /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n    });\n\n    return new KeyframesAst(keyframes);\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitReference(metadata: AnimationReferenceMetadata, context: AnimationAstBuilderContext):\n      ReferenceAst {\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const /** @type {?} */ ast = new ReferenceAst(entry);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateChild(metadata: AnimationAnimateChildMetadata, context: AnimationAstBuilderContext):\n      AnimateChildAst {\n    context.depCount++;\n    const /** @type {?} */ ast = new AnimateChildAst();\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitAnimateRef(metadata: AnimationAnimateRefMetadata, context: AnimationAstBuilderContext):\n      AnimateRefAst {\n    const /** @type {?} */ animation = this.visitReference(metadata.animation, context);\n    const /** @type {?} */ ast = new AnimateRefAst(animation);\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const /** @type {?} */ parentSelector = /** @type {?} */(( context.currentQuerySelector));\n    const /** @type {?} */ options = /** @type {?} */(( (metadata.options || {}) as AnimationQueryOptions));\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector =\n        parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n    getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n\n    const /** @type {?} */ entry = visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    const /** @type {?} */ ast = new QueryAst(selector, options.limit || 0, !!options.optional, includeSelf, entry);\n    ast.originalSelector = metadata.selector;\n    ast.options = normalizeAnimationOptions(metadata.options);\n    return ast;\n  }\n/**\n * @param {?} metadata\n * @param {?} context\n * @return {?}\n */\nvisitStagger(metadata: AnimationStaggerMetadata, context: AnimationAstBuilderContext):\n      StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(`stagger() can only be used inside of query()`);\n    }\n    const /** @type {?} */ timings = metadata.timings === 'full' ?\n        {duration: 0, delay: 0, easing: 'full'} :\n        resolveTiming(metadata.timings, context.errors, true);\n    const /** @type {?} */ animation =\n        visitAnimationNode(this, normalizeAnimationEntry(metadata.animation), context);\n    return new StaggerAst(timings, animation);\n  }\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  selector = selector.replace(ENTER_TOKEN_REGEX, ENTER_SELECTOR)\n                 .replace(LEAVE_TOKEN_REGEX, LEAVE_SELECTOR)\n                 .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n                 .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n                 .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any}|null {\n  return obj ? copyObj(obj) : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number; endTime: number;\n};\nexport class AnimationAstBuilderContext {\npublic queryCount: number = 0;\npublic depCount: number = 0;\npublic currentTransition: AnimationTransitionMetadata|null = null;\npublic currentQuery: AnimationQueryMetadata|null = null;\npublic currentQuerySelector: string|null = null;\npublic currentAnimateTimings: TimingAst|null = null;\npublic currentTime: number = 0;\npublic collectedStyles: {[selectorName: string]: {[propName: string]: StyleTimeTuple}} = {};\npublic options: AnimationOptions|null = null;\n/**\n * @param {?} errors\n */\nconstructor(public errors: any[]) {}\n}\n\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationAstBuilderContext.prototype.queryCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.depCount;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTransition;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuery;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentQuerySelector;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentAnimateTimings;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.currentTime;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.collectedStyles;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.options;\n/** @type {?} */\nAnimationAstBuilderContext.prototype.errors;\n}\n\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles: ɵStyleData | string | (ɵStyleData | string)[]): number|null {\n  if (typeof styles == 'string') return null;\n\n  let /** @type {?} */ offset: number|null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n        const /** @type {?} */ obj = /** @type {?} */(( styleTuple as ɵStyleData));\n        offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n        delete obj['offset'];\n      }\n    });\n  } else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n    const /** @type {?} */ obj = /** @type {?} */(( styles as ɵStyleData));\n    offset = parseFloat( /** @type {?} */((obj['offset'] as string)));\n    delete obj['offset'];\n  }\n  return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value: any): boolean {\n  return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: any[]) {\n  let /** @type {?} */ timings: AnimateTimings|null = null;\n  if (value.hasOwnProperty('duration')) {\n    timings = /** @type {?} */(( value as AnimateTimings));\n  } else if (typeof value == 'number') {\n    const /** @type {?} */ duration = resolveTiming( /** @type {?} */((value as number)), errors).duration;\n    return new TimingAst( /** @type {?} */((value as number)), 0, '');\n  }\n\n  const /** @type {?} */ strValue = /** @type {?} */(( value as string));\n  const /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    return new DynamicTimingAst(strValue);\n  }\n\n  timings = timings || resolveTiming(strValue, errors);\n  return new TimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = copyObj(options);\n    if (options['params']) {\n      options['params'] = /** @type {?} */(( normalizeParams(options['params'])));\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nexport class ElementInstructionMap {\nprivate _map = new Map<any, AnimationTimelineInstruction[]>();\n/**\n * @param {?} element\n * @return {?}\n */\nconsume(element: any): AnimationTimelineInstruction[] {\n    let /** @type {?} */ instructions = this._map.get(element);\n    if (instructions) {\n      this._map.delete(element);\n    } else {\n      instructions = [];\n    }\n    return instructions;\n  }\n/**\n * @param {?} element\n * @param {?} instructions\n * @return {?}\n */\nappend(element: any, instructions: AnimationTimelineInstruction[]) {\n    let /** @type {?} */ existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n/**\n * @param {?} element\n * @return {?}\n */\nhas(element: any): boolean { return this._map.has(element); }\n/**\n * @return {?}\n */\nclear() { this._map.clear(); }\n}\n\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n/** @type {?} */\nElementInstructionMap.prototype._map;\n}\n\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AUTO_STYLE, AnimationEvent, AnimationPlayer, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE, ɵStyleData} from '@angular/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {AnimationDriver} from '../../src/render/animation_driver';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n    driver: AnimationDriver, normalizer: AnimationStyleNormalizer, element: any,\n    keyframes: ɵStyleData[], preStyles: ɵStyleData = {},\n    postStyles: ɵStyleData = {}): ɵStyleData[] {\n  const errors: string[] = [];\n  const normalizedKeyframes: ɵStyleData[] = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleData|null = null;\n  keyframes.forEach(kf => {\n    const offset = kf['offset'] as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleData = (isSameOffset && previousKeyframe) || {};\n    Object.keys(kf).forEach(prop => {\n      let normalizedProp = prop;\n      let normalizedValue = kf[prop];\n      if (normalizedValue == PRE_STYLE) {\n        normalizedValue = preStyles[prop];\n      } else if (normalizedValue == AUTO_STYLE) {\n        normalizedValue = postStyles[prop];\n      } else if (prop != 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(prop, errors);\n        normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, kf[prop], errors);\n      }\n      normalizedKeyframe[normalizedProp] = normalizedValue;\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    const LINE_START = '\\n - ';\n    throw new Error(\n        `Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n    player: AnimationPlayer, eventName: string, event: AnimationEvent | undefined,\n    callback: (event: any) => any) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n      break;\n    case 'destroy':\n      player.onDestroy(\n          () => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n    e: AnimationEvent, phaseName?: string, totalTime?: number): AnimationEvent {\n  const event = makeAnimationEvent(\n      e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName,\n      totalTime == undefined ? e.totalTime : totalTime);\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n    element: any, triggerName: string, fromState: string, toState: string, phaseName: string = '',\n    totalTime: number = 0): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime};\n}\n\nexport function getOrSetAsInMap(\n    map: Map<any, any>| {[key: string]: any}, key: any, defaultValue: any) {\n  let value: any;\n  if (map instanceof Map) {\n    value = map.get(key);\n    if (!value) {\n      map.set(key, value = defaultValue);\n    }\n  } else {\n    value = map[key];\n    if (!value) {\n      value = map[key] = defaultValue;\n    }\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.substr(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains: (elm1: any, elm2: any) => boolean = (elm1: any, elm2: any) => false;\nlet _matches: (element: any, selector: string) => boolean = (element: any, selector: string) =>\n    false;\nlet _query: (element: any, selector: string, multi: boolean) => any[] =\n    (element: any, selector: string, multi: boolean) => {\n      return [];\n    };\n\nif (typeof Element != 'undefined') {\n  // this is well supported in all browsers\n  _contains = (elm1: any, elm2: any) => { return elm1.contains(elm2) as boolean; };\n\n  if (Element.prototype.matches) {\n    _matches = (element: any, selector: string) => element.matches(selector);\n  } else {\n    const proto = Element.prototype as any;\n    const fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n        proto.oMatchesSelector || proto.webkitMatchesSelector;\n    if (fn) {\n      _matches = (element: any, selector: string) => fn.apply(element, [selector]);\n    }\n  }\n\n  _query = (element: any, selector: string, multi: boolean): any[] => {\n    let results: any[] = [];\n    if (multi) {\n      results.push(...element.querySelectorAll(selector));\n    } else {\n      const elm = element.querySelector(selector);\n      if (elm) {\n        results.push(elm);\n      }\n    }\n    return results;\n  };\n}\n\nexport const matchesElement = _matches;\nexport const containsElement = _contains;\nexport const invokeQuery = _query;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '@angular/animations';\n\nimport {containsElement, invokeQuery, matchesElement} from './shared';\n\n\n/**\n * @experimental\n */\nexport class NoopAnimationDriver implements AnimationDriver {\n  matchesElement(element: any, selector: string): boolean {\n    return matchesElement(element, selector);\n  }\n\n  containsElement(elm1: any, elm2: any): boolean { return containsElement(elm1, elm2); }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing: string, previousPlayers: any[] = []): AnimationPlayer {\n    return new NoopAnimationPlayer();\n  }\n}\n\n/**\n * @experimental\n */\nexport abstract class AnimationDriver {\n  static NOOP: AnimationDriver = new NoopAnimationDriver();\n\n  abstract matchesElement(element: any, selector: string): boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n      element: any, keyframes: {[key: string]: string | number}[], duration: number, delay: number,\n      easing?: string|null, previousPlayers?: any[]): any;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleData;\n  toState: string;\n  toStyles: ɵStyleData;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, {[prop: string]: boolean}>;\n  postStyleProps: Map<any, {[prop: string]: boolean}>;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @return {?}\n */\nexport function createTransitionInstruction(\n    element: any, triggerName: string, fromState: string, toState: string,\n    isRemovalTransition: boolean, fromStyles: ɵStyleData, toStyles: ɵStyleData,\n    timelines: AnimationTimelineInstruction[], queriedElements: any[],\n    preStyleProps: Map<any, {[prop: string]: boolean}>,\n    postStyleProps: Map<any, {[prop: string]: boolean}>): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵStyleData} from '@angular/animations';\nimport {AnimationEngineInstruction, AnimationTransitionInstructionType} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: ɵStyleData[];\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string|null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(\n    element: any, keyframes: ɵStyleData[], preStyleProps: string[], postStyleProps: string[],\n    duration: number, delay: number, easing: string | null = null,\n    subTimeline: boolean = false): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay, easing, subTimeline\n  };\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationAnimateChildMetadata, AnimationAnimateMetadata, AnimationAnimateRefMetadata, AnimationGroupMetadata, AnimationKeyframesSequenceMetadata, AnimationMetadata, AnimationMetadataType, AnimationQueryMetadata, AnimationReferenceMetadata, AnimationSequenceMetadata, AnimationStaggerMetadata, AnimationStateMetadata, AnimationStyleMetadata, AnimationTransitionMetadata, AnimationTriggerMetadata} from '@angular/animations';\n\nexport interface AnimationDslVisitor {\n  visitTrigger(ast: AnimationTriggerMetadata, context: any): any;\n  visitState(ast: AnimationStateMetadata, context: any): any;\n  visitTransition(ast: AnimationTransitionMetadata, context: any): any;\n  visitSequence(ast: AnimationSequenceMetadata, context: any): any;\n  visitGroup(ast: AnimationGroupMetadata, context: any): any;\n  visitAnimate(ast: AnimationAnimateMetadata, context: any): any;\n  visitStyle(ast: AnimationStyleMetadata, context: any): any;\n  visitKeyframes(ast: AnimationKeyframesSequenceMetadata, context: any): any;\n  visitReference(ast: AnimationReferenceMetadata, context: any): any;\n  visitAnimateChild(ast: AnimationAnimateChildMetadata, context: any): any;\n  visitAnimateRef(ast: AnimationAnimateRefMetadata, context: any): any;\n  visitQuery(ast: AnimationQueryMetadata, context: any): any;\n  visitStagger(ast: AnimationStaggerMetadata, context: any): any;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitAnimationNode(\n    visitor: AnimationDslVisitor, node: AnimationMetadata, context: any) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger( /** @type {?} */((node as AnimationTriggerMetadata)), context);\n    case AnimationMetadataType.State:\n      return visitor.visitState( /** @type {?} */((node as AnimationStateMetadata)), context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition( /** @type {?} */((node as AnimationTransitionMetadata)), context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence( /** @type {?} */((node as AnimationSequenceMetadata)), context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup( /** @type {?} */((node as AnimationGroupMetadata)), context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate( /** @type {?} */((node as AnimationAnimateMetadata)), context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes( /** @type {?} */((node as AnimationKeyframesSequenceMetadata)), context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle( /** @type {?} */((node as AnimationStyleMetadata)), context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference( /** @type {?} */((node as AnimationReferenceMetadata)), context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild( /** @type {?} */((node as AnimationAnimateChildMetadata)), context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef( /** @type {?} */((node as AnimationAnimateRefMetadata)), context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery( /** @type {?} */((node as AnimationQueryMetadata)), context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger( /** @type {?} */((node as AnimationStaggerMetadata)), context);\n    default:\n      throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n  }\n}\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (fromState: any, toState: any) => boolean;\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(\n    transitionValue: string | TransitionMatcherFn, errors: string[]): TransitionMatcherFn[] {\n  const /** @type {?} */ expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    ( /** @type {?} */((<string>transitionValue)))\n        .split(/\\s*,\\s*/)\n        .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push( /** @type {?} */((<TransitionMatcherFn>transitionValue)));\n  }\n  return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(\n    eventStr: string, expressions: TransitionMatcherFn[], errors: string[]) {\n  if (eventStr[0] == ':') {\n    eventStr = parseAnimationAlias(eventStr, errors);\n  }\n  const /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n    return expressions;\n  }\n\n  const /** @type {?} */ fromState = match[1];\n  const /** @type {?} */ separator = match[2];\n  const /** @type {?} */ toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias: string, errors: string[]): string {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    default:\n      errors.push(`The transition alias value \"${alias}\" is not supported`);\n      return '* => *';\n  }\n}\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  return (fromState: any, toState: any): boolean => {\n    let /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? lhs === 'true' : lhs === 'false';\n    }\n    if (!rhsMatch && typeof toState === 'boolean') {\n      rhsMatch = toState ? rhs === 'true' : rhs === 'false';\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n"], "names": ["skippedPlayers", "push", "player", "rootPlayers", "eraseStyles", "element", "instruction", "fromStyles", "onDestroy", "setStyles", "to<PERSON><PERSON>les", "subPlayers", "for<PERSON>ach", "playersFor<PERSON>lement", "skippedPlayersMap", "get", "parentPlayer", "destroy", "details", "REMOVAL_FLAG", "players", "queriedElements", "size", "queriedPlayerResults", "length", "apply", "queriedInnerElements", "this", "driver", "query", "NG_ANIMATING_SELECTOR", "j", "queriedPlayers", "removeNodesAfterAnimationDone", "processLeaveNode", "_this", "TransitionAnimationEngine", "prototype", "elementContainsData", "namespaceId", "containsData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "_getPreviousPlayers", "isQueriedElement", "triggerName", "toStateValue", "queriedElementPlayers", "elementPlayers", "players<PERSON>y<PERSON><PERSON>", "isRemovalAnimation_1", "VOID_VALUE", "queued", "_beforeAnimationBuild", "allPreviousPlayersMap", "rootElement", "targetNameSpaceId", "isRemovalTransition", "undefined", "targetTriggerName", "timelines", "map", "timelineInstruction", "getOrSetAsInMap", "allConsumedElements", "Set", "allSubElements", "allNewPlayers", "removed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_angular_animations", "NoopAnimationPlayer", "previousPlayers", "EMPTY_PLAYER_ARRAY", "_previousPlayers", "p", "getRealPlayer", "postStyles", "postStylesMap", "keyframes", "normalizeKeyframes", "_normalizer", "preStyles", "_buildPlayer", "subTimeline", "add", "wrappedPlayer", "TransitionAnimationPlayer", "setRealPlayer", "allQueriedPlayers", "onDone", "deleteOrUnsetInMap", "addClass", "NG_ANIMATING_CLASSNAME", "_player", "fn", "onStart", "AnimationEngine", "registerTrigger", "componentId", "hostElement", "name", "metadata", "cache<PERSON>ey", "trigger", "_triggerCache", "eventName", "char<PERSON>t", "_transitionEngine", "WebAnimationsPlayer", "options", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_initialized", "_finished", "_started", "_destroyed", "time", "_duration", "_delay", "previousStyles", "_onFinish", "_preparePlayerBeforeStart", "styles", "copyStyles", "previousStyleProps", "Object", "keys", "missingStyleProps_1", "prop", "startingKeyframe_1", "hasOwnProperty", "self_1", "_loop_1", "addEventListener", "_triggerWebAnimation", "hasStarted", "finish", "reset", "play", "setPosition", "currentTime", "getPosition", "configurable", "<PERSON><PERSON><PERSON><PERSON>", "_finalKeyframe", "fill", "delay", "easing", "playerOptions", "TimelineBuilder", "getFinalKeyframe", "_keyframes", "duration", "defineProperty", "properties", "details0", "_styleSummary", "details1", "timeline", "buildKeyframes", "applyStylesToKeyframe", "preStyleProps", "postStyleProps", "isEmpty", "ɵPRE_STYLE", "keyframe", "finalKeyframe", "value", "AUTO_STYLE", "finalKeyframes", "preProps", "iteratorToArray", "values", "postProps", "SubTimelineBuilder", "containsAnimation", "_stretchStartingKeyframe", "limit", "i", "kf", "timeAtKeyframe", "oldOffset", "roundOffset", "totalTime", "newKeyframes", "Animation", "buildTimelines", "startingStyles", "destinationStyles", "subInstructions", "start", "Array", "isArray", "normalizeStyles", "dest", "ElementInstructionMap", "Error", "errorMessage", "AnimationStyleNormalizer", "NoopAnimationStyleNormalizer", "normalizePropertyName", "propertyName", "errors", "WebAnimationsStyleNormalizer", "normalizeStyleValue", "userProvidedProperty", "normalizedProperty", "unit", "strVal", "toString", "trim", "DIMENSIONAL_PROP_MAP", "valAndSuffixMatch", "match", "AnimationTransitionFactory", "build", "currentState", "nextState", "animationOptions", "mergeAnimationOptions", "ast", "backupStateStyles", "_stateStyles", "currentStateStyles", "nextStateStyles", "buildAnimationTimelines", "animation", "join", "preStyleMap", "Map", "postStyleMap", "tl", "elm", "AnimationTrigger", "transitionFactories", "states", "obj", "style", "styleTuple", "balanceProperties", "matchTransition", "TimelineAnimationEngine", "create", "id", "instructions", "_animations", "autoStylesMap", "_driver", "EMPTY_INSTRUCTION_MAP", "inst", "computeStyle", "optimizeGroupPlayer", "_playersById", "command", "args", "register", "_getPlayer", "pause", "restart", "init", "NULL_REMOVAL_STATE", "setForRemoval", "hasAnimation", "StateValue", "input", "isObj", "normalizeTriggerValue", "copyObj", "absorbOptions", "newParams", "params", "oldParams_2", "AnimationTransitionNamespace", "_engine", "listen", "phase", "callback", "_triggers", "isTriggerEventValid", "triggersWithStates", "statesByElement", "afterFlush", "defaultToFallback", "_getTrigger", "NG_TRIGGER_CLASSNAME", "set", "fromState", "toState", "playersOnElement", "transition", "isFallbackTransition", "fallbackTransition", "totalQueuedPlayers", "_queue", "removeClass", "index", "indexOf", "splice", "_destroyInnerNodes", "context", "animate", "NG_TRIGGER_SELECTOR", "containsClass", "_hostClassName", "innerNs", "namespacesByHostElement", "engine", "doNotRecurse", "childElementCount", "triggerStates", "players_1", "currentPlayers", "containsPotentialParentTransition", "listeners", "_elementListeners", "visitedTriggers_1", "listener", "elementStates", "DEFAULT_STATE_VALUE", "insertNode", "parent", "drainQueuedTransitions", "microtaskId", "entry", "destroyed", "baseEvent", "makeAnimationEvent", "listenOnPlayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "d0", "d1", "find", "newHostElements", "totalAnimations", "_onRemovalComplete", "onRemovalComplete", "_balanceNamespaceList", "ns", "_namespaceList", "found", "nextNamespace", "containsElement", "_fetchNamespace", "removeNode", "isElementNode", "destroyInnerAnimations", "containerElement", "stateMap", "DELETED_STATE_VALUE", "whenRenderingDone", "clearElementCache", "flush", "clear", "collectedLeaveElements", "_flushAnimations", "_flushFns", "_whenQuietFns", "subTimelines", "allPreStyleElements", "collectedEnterElements", "filter", "createIsRootFilterFn", "allEnterNodes", "ENTER_CLASSNAME", "allLeaveNodes", "leaveNodesWithoutAnimations", "LEAVE_CLASSNAME", "bodyNode", "_buildInstruction", "stretchStartingKeyframe", "append", "tuple", "queuedInstructions", "stringMap", "props", "setVal_1", "setVal", "allPostStyleElements", "enterNodesWithoutAnimations", "sortedParentElements", "prevPlayer", "cloakAndComputeStyles", "innerPlayer", "_buildAnimation", "preStylesMap", "parentHasPriority", "parentPlayers", "ENTER_SELECTOR", "__extends", "TransitionAst", "_super", "ENTER_TOKEN", "AnimationAstBuilderVisitor", "visitTrigger", "queryCount", "depCount", "transitions", "definitions", "def", "_resetContextStyleTimingState", "type", "stateDef_1", "split", "n", "visitState", "visitTransition", "visitGroup", "furthestTime", "steps", "step", "visitAnimate", "timingAst", "constructTimingAst", "timings", "currentAnimateTimings", "styleMetadata", "visitKeyframes", "styleMetadata_1", "newStyleData", "_makeStyleAst", "collectedEasing", "styleData", "isObject", "_validateStyleAst", "endTime", "startTime", "updateCollectedStyle", "collectedEntry", "KeyframesAst", "MAX_KEYFRAME_OFFSET", "totalKeyframesWithOffsets", "offsets", "offsetsOutOfOrder", "keyframesOutOfRange", "previousOffset", "style$$1", "offsetVal", "offset", "consumeOffset", "generatedOffset", "animateDuration", "visit<PERSON><PERSON><PERSON>", "parentSelector", "<PERSON><PERSON><PERSON><PERSON>", "_a", "normalizeSelector", "selector", "includeSelf", "currentQuerySelector", "visitStagger", "resolveTiming", "AnimationAstBuilderContext", "existingInstructions", "_map", "AnimationTimelineBuilderVisitor", "finalStyles", "AnimationTimelineContext", "currentTimeline", "visit", "visitAnimateChild", "elementInstructions", "consume", "_visitSubInstructions", "resolveTimingValue", "visitSequence", "subContextCount", "ctx", "createSubContext", "transformIntoNewTimeline", "snapshotCurrentStyles", "innerTimelines", "innerContext", "delayNextStep", "visitTiming", "incrementTime", "visitStyle", "getCurrentStyleProperties", "<PERSON><PERSON><PERSON><PERSON>", "innerTimeline", "previousNode", "StyleAst", "DEFAULT_NOOP_PREVIOUS_NODE", "elms", "invoke<PERSON><PERSON>y", "originalSelector", "optional", "currentQueryTotal", "sameElementTimeline", "Math", "max", "parentContext", "abs", "maxTime", "currentQueryIndex", "staggerTransformer", "initialTimeline", "updateOptions", "skipIfExists", "newOptions", "optionsToUpdate", "paramsToUpdate_1", "_copyOptions", "newTime", "target", "fork", "results", "_elementTimelineStylesLookup", "_previousKeyframe", "_currentKeyframe", "_pendingStyles", "_backFill", "_currentEmptyStepKeyframe", "_globalTimelineStyles", "_loadKeyframe", "hasPreStyleStep", "flattenStyles", "_localTimelineStyles", "key1", "key", "currentV<PERSON>ues", "delete", "elements", "elementPropsMap", "defaultStyle", "cloakVals", "cloakElement", "nodes", "isRoot", "nodeSet", "knownRootContainer", "node", "parentNode", "className", "classList", "classes", "CLASSES_CACHE_KEY", "extendStatics", "setPrototypeOf", "__proto__", "d", "_contains", "elm1", "elm2", "_query", "multi", "Element", "contains", "matches", "_matches", "proto", "fn_1", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "querySelectorAll", "querySelector", "NoopAnimationDriver", "matchesElement", "defaultValue", "createTransitionInstruction", "createTimelineInstruction", "allStyles", "allProperties", "token", "visitAnimationNode", "visitor", "visitReference", "visitAnimateRef", "parseTransitionExpr", "transitionValue", "expressions", "parseInnerTransitionStr", "eventStr", "parseAnimationAlias", "alias", "makeLambdaFromStates", "lhs", "rhs", "lhsMatch", "ANY_STATE", "hasAmpersand", "SELF_TOKEN", "replace", "LEAVE_TOKEN_REGEX", "LEAVE_SELECTOR", "parseFloat", "isDynamic", "normalizeAnimationOptions", "ɵAnimationGroupPlayer", "normalizer", "normalizedKeyframes", "previousKeyframe", "isSameOffset", "normalizedKeyframe", "normalizedProp", "normalizedValue", "LINE_START", "event", "copyAnimationEvent", "e", "phaseName", "data", "parseTimelineCommand", "separatorPos", "action", "substr", "parseTimeExpression", "exp", "allowNegativeValues", "regex", "_convertTimeValueToMS", "delayMatch", "floor", "easingVal", "containsErrors", "startIndex", "destination", "readPrototype", "validateStyleParams", "interpolateParams", "str", "original", "PARAM_REGEX", "_", "varName", "localVal", "source", "p0_1", "exports", "module", "factory", "require"], "mappings": ";;;;;0BeAA,gBAAA0nB,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA;;;;;;;;;;;;ACuBA,QAAAjY,qBAAAxO,SAIA,OAAAA,QAAAI,QACA,IAAA,GACA,MAAA,IAAA6C,qBAAAC,mBACA,KAAA,GACA,MAAAlD,SAAA,EAAA,SAAA,MAAA,IAAAiD,qBAAA6gB,sBAAA9jB,UAGA,QAAA2D,oBAAAnD,OAAAujB,WAAA9kB,QAAAyE,UAAAG,UAAAL,YACA,SAAIK,YAAwBA,cAC5B,SAAAL,aAAAA,cACA,IAAA+H,WACAyY,uBACA9J,gBAAA,EACA+J,iBAAA,IA6BA,IA7BAvgB,UAAAlE,QAAA,SAAAwK,IACA,GAAAqQ,QAAArQ,GAAA,OACAka,aAAA7J,QAAAH,eAAAiK,mBAAAD,cAAAD,oBACAvd,QAAAC,KAAAqD,IAAAxK,QAAA,SAAAqH,MACA,GAAAud,gBAAAvd,KACAwd,gBAAAra,GAAAnD,KACAwd,kBAAAphB,oBAAAgG,WACAob,gBAAAxgB,UAAAgD,MAEAwd,iBAAAphB,oBAAAoG,WACAgb,gBAAA7gB,WAAAqD,MAEA,UAAAA,OACAud,eAAAL,WAAA1Y,sBAAAxE,KAAA0E,QACA8Y,gBAAAN,WAAAtY,oBAAA5E,KAAAud,eAAApa,GAAAnD,MAAA0E,SAEA4Y,mBAAAC,gBAAAC,kBAIAH,cACAF,oBAAAnlB,KAAAslB,oBAKAF,iBAAAE,mBACAjK,eAAAG,SAEA9O,OAAAnL,OAAA,CACA,GAAAkkB,YAAA,OACA,MAAA,IAAArZ,OAAA,iDAAAqZ,WAAA/Y,OAAAyB,KAAAsX,aAEA,MAAAN,qBAGA,QAAA5Q,gBAAAtU,OAAAuG,UAAAkf,MAAAtU,UACA,OAAA5K,WACA,IAAA,QAEAvG,OAAA6F,QAAA,WAAA,MAAAsL,UAAAsU,OAAAC,mBAAAD,MAAA,QAAAzlB,OAAAsL,aAEA,MAGA,KAAA,OACAtL,OAAAuF,OAAA,WAAA,MAAA4L,UAAAsU,OAAAC,mBAAAD,MAAA,OAAAzlB,OAAAsL,aACA,MACA,KAAA,UACAtL,OAAAM,UAAA,WAAA,MAAA6Q,UAAAsU,OAAAC,mBAAAD,MAAA,UAAAzlB,OAAAsL,eAOA,QAAAoa,oBAAAC,EAAAC,UAAAta,WAEA,GAAAma,OAAApR,mBAAAsR,EAAAxlB,QAAAwlB,EAAAhjB,YAAAgjB,EAAA9T,UAAA8T,EAAA7T,QAAA8T,WAAAD,EAAAC,UAAApiB,QAAA8H,UAAAqa,EAAAra,UAAAA,WAEAua,KAAAF,EAAA,KAEA,OADA,OAAAE,OAAAJ,MAAA,MAAAI,MACAJ,MAEA,QAAApR,oBAAAlU,QAAAwC,YAAAkP,UAAAC,QAAA8T,UAAAta,WAEA,MADA,UAAAsa,YAAAA,UAAA,IACA,SAAAta,YAAAA,UAAA,IAAAnL,QAAAA,QAAAwC,YAAAA,YAAAkP,UAAAA,UAAAC,QAAAA,QAAA8T,UAAAA,UAAAta,UAAAA,WAEA,QAAAzH,iBAAAF,IAAA6c,IAAA0C,cACA,GAAA5Y,MAgBI,OAfJ3G,eAAAyK,MACA9D,MAAA3G,IAAA9C,IAAA2f,KACAlW,OACA3G,IAAAiO,IAAA4O,IAAAlW,MAAA4Y,gBAKA5Y,MAAc3G,IAAd6c,KACAlW,QACAA,MAAA3G,IAAA6c,KAAA0C,eAKA5Y,MAGA,QAAAwb,sBAAAlW,SAEI,GAAJmW,cAAAnW,QAAA2C,QAAA,0CAEAyT,OAAApW,QAAAqW,OAA0BF,aAA1B,EAEE,QAAF7W,GAAA8W,8eL5DA,QAAAE,qBAAAC,IAAA1Z,OAAA2Z,qBAEA,GAEA7c,UAFA8c,MAAA,2EAGApd,MAAA,EACAC,OAAA,EACA,IAAA,gBAAAid,KAAA,CAEI,GAAJ9D,SAAA8D,IAAA/Y,MAAAiZ,MACI,IAAJ,OAAAhE,QAEA,MADM5V,QAAN1M,KAAA,8BAAAomB,IAAA,kBACA5c,SAAA,EAAAN,MAAA,EAAAC,OAAA,GAjBAK,UAAA+c,sBAAAzB,WAAAxC,QAAA,IAAAA,QAAA,GAmBI,IAAJkE,YAAAlE,QAAA,EACA,OAAAkE,aAhBAtd,MAAAqd,sBAAA5H,KAAA8H,MAAA3B,WAAA0B,aAAAlE,QAAA,IAoBA,IAAAoE,WAAApE,QAAA,EACAoE,aACAvd,OAAAud,eAIAld,UAAA,GAEA,KAAA6c,oBAAA,CACA,GAAAM,iBAAA,EACAC,WAAAla,OAAAnL,MACAiI,UAAA,IACAkD,OAAA1M,KAAA,oEAEA2mB,gBAAA,irBA0CA,GADA,SAAIE,cAAJA,gBACAC,yVAiCAjf,OAAAC,KAAAJ,QAAA/G,QAAA,SAAAqH,mNA0BA,QAAA+e,qBAAAxc,MAAA3D,QAAA8F,QACA,GAAAoE,QAAAlK,QAAAkK,UACA,IAAA,gBAAAvG,OAAA,kOAoBA,QAAAyc,mBAAAzc,MAAAuG,OAAApE,sCAGAua,IAAAC,SAAAvC,QAAAwC,YAAA,SAAAC,EAAAC,SACA,GAAAC,UAAAxW,OAAAuW,kUA8BA,QAAA1Z,uBAAA4Z,OAAAV,aACA,GAAAU,OAAAzW,OAAA,CACA,GAAA0W,MAAAD,OAAAzW,MACA+V,aAAA/V,SACA+V,YAAA/V;;;;;;;ASlOA,QAAA2S,oBAAAC,QAAArC,KAAA1O,SACA,OAAA0O,KAAAlI,MACA,IAAA,GACA,MAAAuK,SAAA9K,aAAA,KAAAjG,QACA,KAAA,GACA,MAAA+Q,SAAAnK,WAAA,KAAA5G,QACA,KAAA,GACA,MAAA+Q,SAAAlK,gBAAA,KAAA7G,QACA,KAAA,GACA,MAAA+Q,SAAAvG,cAAA,KAAAxK,QACA,KAAA,GACA,MAAA+Q,SAAAjK,WAAA,KAAA9G,QACA,KAAA,GACA,MAAA+Q,SAAA7J,aAAA,KAAAlH,QACA,KAAA,GACA,MAAA+Q,SAAAvJ,eAAA,KAAAxH,QACA,KAAA,GACA,MAAA+Q,SAAA5F,WAAA,KAAAnL,QACA,KAAA,GACA,MAAA+Q,SAAAC,eAAA,KAAAhR,QACA,KAAA,GACA,MAAA+Q,SAAA5G,kBAAA,KAAAnK,iBC9DA,MAAA+Q,SAAAE,gBAAA,KAAAjR,6LAwBA,QAAAkR,qBAAAC,gBAAApX,QACA,GAAAqX,qNAmBA,QAAAC,yBAAAC,SAAAF,YAAArX,QACA,KAAAuX,SAAA,KACAA,SAAAC,oBAAAD,SAAAvX,QAGE,IAVoBW,OAUtB4W,SAAA5W,MAAA,0CACA,IAAA,MAAAA,OAAAA,MAAA9L,OAAA,EAEA,MADAmL,QAAA1M,KAAA,uCAAAikB,SAAA,sBACAF,kSAgBA,QAAAG,qBAAAC,MAAAzX,oLAkBA,QAAA0X,sBAAAC,IAlBuBC,KAmBvB,MAAA,UAAAxS,UAAAC,SACA,GAAAwS,UAAAF,KAAAG,WAAAH,KAAAvS,sDRzFAyS,WAAA,iBAAAzS,6QAmbA,QAAAkK,mBAAAC,UACA,GAAAwI,gBAAAxI,SAAA5C,MAAA,WAAAvE,KAAA,SAAA0O,OAAA,MAAAA,QAAAkB,+IAKAC,QAAAC,kBAAAC,qQA6DA,QAAApJ,eAAA/T,QACA,GAAA,gBAAAA,QACA,MAAA,KAtEA,IAAA8T,QAAA,IAuEA,IAAAzP,MAAAC,QAAAtE,QACAA,OAAA/G,QAAA,SAAAmO,YACA,GAAA2L,SAAA3L,aAAAA,WAAA5G,eAAA,UAAA,CACA,GAAA0G,KAAA,UACA4M,QAAAsJ,WAAAlW,IAAA,cACAA,KAAA,qEAKA,GAAAA,KAAA,kJAkBA,QAAAmL,oBAAAxP,MAAAmC,QACA,GAAAsN,SAAA,IACA,IAAIzP,MAAJrC,eAAA,YACA8R,QAAA,UAGA,IAAA,gBAAAzP,OAAA,CACA8R,cAAA,MAAA3P,QAAAlD,wJAKA,OAAAub,yJASA,QAAAC,2BAAApe,eACAA,mCM3iBAA,QAAA;;;;;;;AA6CA,QAAAyc,2BAAAjjB,QAAAyE,UAAAoF,cAAAC,eAAAV,SAAAN,MAAAC,OAAAjE,aAGA,MAFA,UAAIiE,SAAJA,OAAA,MACA,SAAAjE,cAAAA,aAAA,WL/CA9E,QAAAA,qsBV8iCA,QAAAkgB,eAAA9P,MAAA8S,WACA,GACAC,eADA7b,gBAEA8I,OAAA7P,QAAA,SAAA6iB,oBCjjCAD,cAAAA,eAAA1b,OAAAC,KAAAwb;;;;;;;AasDA,QAAAF,6BAAAhjB,QAAAwC,YAAAkP,UAAAC,QAAAvO,oBAAAlD,WAAAG,SAAAkD,UAAAvC,gBAAA6I,cAAAC,gBACA,OACIiP,KAAJ,EACI/Y,QAAJA,QACAwC,YAAAA,YACAY,oBAAAA,wCV3DAlD,WAAAA;;;;;;;gCCkCA,MAAA,IAAAmO,kBAAAtI,KAAAyH,8SClCAgB,IAAA1G,eAAAsY,sGVqhDA,QAAA/a,oBAAA7B,IAAA6c,IAAAlW,OACA,GAAAmW,cACA,IAAA9c,cAAAyK,MAvTA,GAwTAqS,cAAA9c,IAAA9C,IAAA2f,KAxTA,CAyTA,GAAAC,cAAAnf,OAAA,CACA,GAAAgR,OAAAmO,cAAAlO,QAAAjI,MACAmW,eAAAjO,OAAAF,MAAA,GAEA,GAAAmO,cAAAnf,QACAqC,IAAA+c,OAAAF,UAMA,IADAC,cAAA9c,IAAA6c,KACA,CACA,GAAAC,cAAAnf,OAAA,qGAKAqC,KAAA6c,qFAYA,IAAA,wWAyCA,QAAAzI,uBAAArW,OAAAif,SAAAC,gBAAAC,cACA,GAAAC,WAAAH,SAAAhd,IAAA,SAAAxD,SAAA,MAAA4gB,cAAA5gB,oFAGA,GAAAsH,UAEA+P,OAAA9W,QAAA,SAAAqH,MACA,GAAAuC,OAAA7C,OAAAM,MAAArG,OAAA+N,aAAAtP,QAAA4H,KAAA8Y,sNAlTA,QAAAlK,sBAAAqK,OAgUA,GAEAC,QAFAC,QAAA,GAAAnd,KAAAid,OACAG,mBAAA,GAAApd,WAEAkd,QAAA,SAAAG,MACA,OAAAA,OAEAF,QAAA1e,IAAA4e,KAAAC,gBAGAF,mBAAA3e,IAAA4e,KAAAC,kQA4BA,QAAA5b,UAAAtF,QAAAmhB,WACA,GAAAnhB,QAAAohB,+JAeA,QAAAlP,aAAAlS,QAAAmhB,wEAIA,CACA,GAAAE,SAAArhB,QAAAshB,geezpDA,GAAIC,eAAJ9Z,OAAA+Z,iBACAC,uBAAA9V,QAA2C,SAA3C+V,EAAAnN,GAAAmN,EAAAD,UAAAlN,IACI,SAAJmN,EAAAnN,GAAA,IAAA,GAA+BlQ,KAA/BkQ,GAAAA,EAA6CzM,eAA7CzD,KAAAqd,EAAkErd,GAAlEkQ,EAAAlQ,KCiHAsd,UAAA,SAAAC,KAAAC,MAAA,OAAA,iDACAC,OAAA,SAAA9hB,QAAA6b,SAAAkG,OACA,SAEA,IAAA,mBAAAC,SAAA,CAGA,GADAL,UAAA,SAAAC,KAAAC,MAAA,MAAAD,MAAAK,SAAAJ,OACAG,QAAAhgB,UAAAkgB,QAEAC,SAAA,SAAAniB,QAA0D6b,UAA1D,MAAA7b,SAAAkiB,QAAArG,eAEA,CACA,GAAAuG,OAAAJ,QAAAhgB,UACAqgB,KAAAD,MAAAE,iBAAAF,MAAAG,oBAAAH,MAAAI,mBAAAJ,MAAAK,kBAAAL,MAAAM,qBACML,QACAF,SAAN,SAAAniB,QAAA6b,UAAA,MAAAwG,MAAAjhB,MAAApB,SAAA6b,aAGAiG,OAAA,SAAA9hB,QAAA6b,SAAAkG,OACA,GAAAvC,WACA,IAAAuC,MACAvC,QAAA5f,KAAAwB,MAAAoe,QAAAxf,QAAA2iB,iBAAA9G,eAGA,CACA,GAAAzN,KAAApO,QAAA4iB,cAAA/G,eCjKA2D,QAAA5f,KAAAwO,8FA0BAyU,oBAAA,WACA,QAAAA,8BAGAA,qBAAA7gB,UAAA8gB,eAAA,SAAA9iB,QAAA6b,UAGA,MAAAiH,gBAAA9iB,QAAA6b,WAEAgH,oBAAA7gB,UAAAoT,gBAAA,SAAAwM,KAAAC,MAAA,MAAAzM,iBAAAwM,KAAAC,uMAKA,MAAAkB,eAAA,IAlBAF,oBAAA7gB,UAAAwQ,QAAA,SAAAxS,QAAAyE,UAAA2E,SAAAN,MAAAC,OAAA7E;;;;;;;yENEAgU,eAAA,iJAAAzW,sBAAA,+sCC8BA0W,WAAAC,cAAAC,65HCiXAC,YAAA,umBAjWAC,2BAAAvW,UAAAwW,aAAA,SAAAxS,SAAAuM,SACA,GAAAzQ,OAAAR,KACAmX,WAAAlG,QAAAkG,WAAA,EACAC,SAAAnG,QAAAmG,SAAA,EACAnK,UACAoK,cACA3S,UAAA4S,YAAArY,QAAA,SAAAsY,KACA,GAmBA/W,MAAAgX,8BAAAvG,SAnBA,GAAAsG,IAAAE,KAAA,CACA,GAAAC,YAAA,IACAjT,KAAAiT,WAAAjT,IACAA,MAAAkT,MAAA,WAAA1Y,QAAA,SAAA2Y,GACAF,WAAAjT,KAAAmT,EAoBA3K,OAAA3O,KAAAkC,MAAAqX,WAAAH,WAAAzG,YAjBAyG,WAAAjT,KAAAA,SAEA,IAAA,GAAA8S,IAAAE,KAAA,CACA,GAAAlH,YAAA/P,MAAAsX,gBAAA,IAAA7G,QACAkG,aAAA5G,WAAA4G,WACAC,UAAA7G,WAAA6G,SACAC,YAAA/Y,KAAAiS,ubA0BA0G,2BAAAvW,UAAAoX,gBAAA,SAAApT,SAAAuM,+mBA2BAgG,2BAAAvW,UAAAqX,WAAA,SAAArT,SAAAuM,SACA,GAAAzQ,OAAAR,KAKAkH,YAAA+J,QAAA/J,YAHA8Q,aAAA,EACAC,MAAAvT,SAAAuT,MAAA/V,IAAA,SAAAgW,qSAiBAjB,2BAAAvW,UAAAyX,aAAA,SAAAzT,SAAAuM,SACA,GAAAmH,WAAAC,mBAAA3T,SAAA4T,QAAArH,QAAAjG,OACAiG,SAAAsH,sBAAAH,SACA,IAAApS,QACAwS,cAAA9T,SAAAsB,OAAAtB,SAAAsB,OAAAtD,oBAAAyK,SACA,IAAA,GAAAqL,cAAAf,KACAzR,OAAAhG,KAAAyY,eAAA,cAAAxH,aAEA,CACA,GAAAyH,iBAAAhU,SAAA,OACA+D,SAAA,CACA,KAAAiQ,gBAAA,CACAjQ,SAAA,CACA,IAAAkQ,gBAEAP,WAAA3Q,SACAkR,aAAA,OAAAP,UAAA3Q,+cA2BAwP,2BAAAvW,UAAAkY,cAAA,SAAAlU,SAAAuM,SACA,GAAAjL,UARAqE,OAAAC,QAAA5F,SAAAsB,QASAtB,SAAA,OAAAzF,QAAA,SAAAmO,YACA,gBAAAA,YACAA,YAAA1K,oBAAAoG,WACA9C,OAAA1H,KAAA,YAEA2S,QAAAjG,OAAA1M,KAAA,mCAAA8O,WAAA,oBAKApH,OAAA1H,KAAA,cAKA0H,OAAA1H,KAAAoG,SAAAsB,OAEA,IAAA6S,iBAAA,WACA7S,QAAA/G,QAAA,SAAA6Z,WACA,GAAAC,SAAAD,WAAA,gKAiBA7B,2BAAAvW,UAAAsY,kBAAA,SAAA9M,IAAA+E,SACA,GAAAqH,SAAArH,QAAAsH,sBACAU,QAAAhI,QAAA/J,YACAgS,UAAAjI,QAAA/J,WACAoR,UAAAY,UAAA,IACAA,WAAAZ,QAAAxQ,SAAAwQ,QAAA9Q,OAEA0E,IAAAlG,OAAA/G,QAAA,SAZ2B2W,OAc3B,gBAAAA,iKAMAuD,sBAAA,CACAC,+WAMAD,sBAAA,GAKAD,UAAAE,eAAAF,qLAkBAjC,2BAAAvW,UAAA+X,eAAA,SAAA/T,SAAAuM,SACI,GAAJzQ,OAAAR,IAEI,KAAJiR,QAAAsH,sBAEA,MADAtH,SAAAjG,OAAA1M,KAAA,4DACA,GAAA+a,iBAGA,IAhBuBC,qBAgBvB,EACAC,0BAAA,EACAC,WACAC,mBAAA,EACAC,qBAAA,EACuBC,eAAvB,EACAxW,UAAAuB,SAAAuT,MAAA/V,IAAA,SAAA8D,QACA,GAhB2B4T,UAgB3BpZ,MAAAoY,cAAA5S,OAAAiL,SACA4I,UAAA,MAAAD,SAAAE,OAAAF,SAAAE,OAAAC,cAAAH,SAAA5T,QACA8T,OAAA,CAYQ,OAVR,OAAAD,YACAN,4BACAO,OAAAF,SAAAE,OAAAD,WAGAH,oBAAAA,qBAAAI,OAAA,GAAAA,OAAA,EACAL,kBAAAA,mBAAAK,OAAAH,eAEAA,eAAAG,OACAN,QAAAlb,KAAAwb,QACAF,UAEAF,sBAhBAzI,QAAAjG,OAAA1M,KAAA,+DAkBAmb,mBAEAxI,QAAAjG,OAAA1M,KAAA,uDAEA,IAAAuB,QAAA6E,SAAAuT,MAAApY,OACAma,gBAAA,CACAT,2BAAA,GAAAA,0BAAA1Z,OACAoR,QAAAjG,OAAA1M,KAAA,yEAEA,GAAAib,4BACAS,gBAAAV,qBAAAzZ,OAAA,GAEA,IAAA0J,OAAA1J,OAAA,EACAqH,YAAA+J,QAAA/J,YAEAqR,sBAAAtH,QAAA,sBACAgJ,gBAAA1B,sBAAAzQ,4jCAsDAmP,2BAAAvW,UAAAwZ,WAAA,SAAAxV,SAAAuM,SACI,GAAJkJ,gBAAAlJ,QAAA,qBAEA/L,QAAAR,SAAAQ,WACI+L,SAAJkG,aACIlG,QAAJmJ,aAAA1V,QACI,IAAJ2V,IAAAC,kBAAA5V,SAAA6V,UAAAA,SAAAF,GAAA,GAAAG,YAAAH,GAAA,EACApJ,SAAAwJ,wfAkBAxD,2BAAAvW,UAAAga,aAAA,SAAAhW,SAAAuM,0IAKAnJ,SAAA,EAAAN,MAAA,EAAAC,OAAA,QAAAkT,cAAAjW,SAAA4T,QAAArH,QAAAjG,QAAA,kKAVA4P,2BAAA,WAIA,QAAAA,4BAAA5P,QACAhL,KAAAgL,OAAAA,OA4CAhL,KAAAmX,WAAA,EAEAnX,KAAAoX,SAAA,oFA2BApX,KAAAuY,sBAAA,KAAAvY,KAAAkH,YAAA,gaCncA,GAAA2T,sBAAA7a,KAAA8a,KAAA1b,IAAAV,6bVsBAqc,iCAAAra,UAAA2H,eAAA,SAAApI,OAAA2B,YAAAsK,IAAAjC,eAAA+Q,YAAA9V,QAAAiF,gBAAAa,QACA,SAAMA,SAANA,qEAEA,IAAAiG,SAAA,GAAAgK,0BAAAhb,OAAA2B,YAAAuI,gBAAAa,UACAiG,SAAA/L,QAAAA,QAEA+L,QAAAiK,gBAAApc,WAAAmL,gBAAA,KAAAgH,QAAAjG,OAAA9F,SACAgH,IAA8BiP,MAA9Bnb,KAAAiR,iqBA0CA8J,gCAAAra,UAAA0a,kBAAA,SAAAlP,IAAA+E,SACA,GAAAoK,qBAAApK,QAAA9G,gBAAAmR,QAAArK,QAAAvS,4nBAmCAqc,gCAAAra,UAAA6a,sBAAA,SAAA7N,aAAAuD,QAAA/L,SACA,GAAAgU,WAAAjI,QAAAiK,gBAAAhU,YACA8Q,aAAAkB,UAIApR,SAAA,MAAA5C,QAAA4C,SAAA0T,mBAAAtW,QAAA4C,UAAA,ufA2BAiT,gCAAAra,UAAA+a,cAAA,SAAAvP,IAAA+E,SACA,GAAAzQ,OAAAR,KACA0b,gBAAAzK,QAAAyK,gBAEAC,IAAA1K,QACA/L,QAAAgH,IAAAhH,OACA,IAAAA,UAAAA,QAAAkK,QAAAlK,QAAAsC,SACAmU,IAAA1K,QAAA2K,iBAAA1W,SAEAyW,IAAAE,2BA+BA,MAAU3W,QAAVsC,OAAA,wCA3BAmU,IAAAT,gBAAAY,kWA4BAf,gCA0BoBra,UA1BpBqX,WAAA,SAAA7L,IAAA+E,SACA,GAAMzQ,OAANR,KACA+b,kBACA/D,aAAA/G,QAAAiK,gBAAAhU,6KAKAM,QAEAwU,aAAAC,cAAAzU,+VAiBAuT,gCAAAra,UAAAwb,YAAA,SAAAhQ,IAAA+E,wQAiBA8J,gCAAAra,UAAAyX,aAAA,SAAAjM,IAAA+E,SAkBA,GAAAqH,SAAArH,QAAAsH,sBAAAvY,KAAAkc,YAAAhQ,IAAAoM,QAAArH,SAjBA7I,SAkBoC6I,QAlBpCiK,eACM5C,SAAN9Q,QACMyJ,QAANkL,cAAA7D,QAkBgB9Q,OAjBhBY,SAAA0T,wBAGA,IAAAlC,UAAA1N,IAAAiB,KACAyM,oBAAAP,8NAkBA0B,gCAAAra,UAAA0b,WAAA,SAAAlQ,IAAA+E,SACA,GAAA7I,UAAA6I,QAAAiK,gBAcA5C,QAAArH,QAAA,uBAVAqH,SAAAlQ,SAAAiU,4BAAAxc,QACAuI,SAAAkU,6MAiBAvB,gCAAAra,UAAA+X,eAAA,SAAAvM,IAAA+E,SACA,GAAAsH,uBAAAtH,QAAA,sBACAiI,UAAAjI,QAAA,gBAAAnJ,yFAIAyU,cAAAP,aAAAd,yKAMAqB,cAAAzd,UAAAoZ,KAAAlS,OAAAkS,KAAAzQ,OAAAwJ,QAAAjG,OAAAiG,QAAA/L,oMAgBA6V,gCAAAra,UAAAwZ,WAAA,SAAAhO,IAAA+E,SACA,GAAAzQ,OAAAR,KAOAkZ,UAAAjI,QAAAiK,gBAAAhU,YACAhC,QAAAgH,IAAAhH,YACAsC,MAAAtC,QAAAsC,MAAAgU,mBAAAtW,QAAAsC,OAAA,CAEAA,SAAAyJ,QAAAuL,uBAAAC,WACA,GAAAvD,WAAAjI,QAAAiK,gBAM2BmB,4BAN3Bxc,UACAoR,QAAAiK,gBAAAY,wBACA7K,QAAAuL,aAAAE,2BAGA,IAAA1E,cAAAkB,UACAyD,KAAA1L,QAAA2L,YAAA1Q,IAAAqO,SAAArO,IAAA2Q,iBAAA3Q,IAAA3C,MAAA2C,IAAAsO,cAAAtV,QAAA4X,SAAA7L,QAAAjG,OACAiG,SAAA8L,kBAAAJ,KAAA9c,MAQA,IANAmd,qBAMA,gIADAxV,QAEAwU,aAAAC,cAAAzU,qFAMA0E,IAAAM,UAAA2O,MAAA3a,MAAAwb,cAKAA,aAAAd,gBAAA5S,uBAEA,IAAA2Q,SAAA+C,aAAAd,gBAAAhU,WACA8Q,cAAAiF,KAAAC,IAAAlF,aAAAiB,6RAiBA8B,gCAAAra,UAAAga,aAAA,SAAAxO,IAAA+E,SACA,GAAAkM,eAAAlM,QAAA,cACApE,GAAAoE,QAAAiK,gBACA5C,QAAApM,IAAAoM,QACAxQ,SAAAmV,KAAAG,IAAA9E,QAAAxQ,UACAuV,QAAAvV,UAAAmJ,QAAA8L,kBAAA,GAEAvV,MAAAM,SAAAmJ,QAAAqM,kBACAC,mBAAAjF,QAAAxQ,SAAA,EAAA,UAAAwQ,QAAA7Q,MACA,QAAA8V,oBACA,IAAA,UAEA/V,MAAA6V,QAAA7V,KACA,MACA,KAAA,kFAMAA,8GAUAyJ,QAAAuL,aAAAtQ,8NAQA,QAAA+O,0BAAApN,QAAAnP,QAAAyL,gBAAAa,OAAA/I,UAAAub,iBACSxd,KAAT6N,QAAAA,QACS7N,KAATtB,QAAAA,QACSsB,KAATmK,gBAAAA,gBAaInK,KAAKgL,OAATA,OACIhL,KAAJiC,UAAAA,UACAjC,KAAAmd,cAAA,yGALGnd,KAAHkF,sVA0BA+V,yBAAAva,UAAA+c,cAAA,SAAAvY,QAAAwY,4BAEA,IAAAxY,QAAA,CAGA,GAAAyY,YAAA,QACAC,gBAAA5d,KAAAkF,OAEA,OAAAyY,WAAA7V,WACA,gBAAAA,SAAA0T,mBAAAmC,WAAA7V,WAGA,MAAA6V,WAAAnW,QACAoW,gBAAApW,MAAAgU,mBAAAmC,WAAAnW,OAEA,IAAA2H,WAAAwO,WAAAvO,MACA,IAAAD,UAAA,CACA,GAAA0O,kBAAAD,gBAAA,mEAXAzX,OAAAC,KAAA+I,WAAAlQ,QAAA,SAAAwF,MAgBAiZ,cAAAG,iBAAArX,eAAA/B,sGASAwW,yBAAAva,UAAAod,aAAA,0OAqBA7C,yBAAAva,UAAAkb,iBAAA,SAAA1W,QAAAxG,QAAAqf,SACA,SAAA7Y,UAAAA,QAAA,KACI,IAAJ8Y,QAAAtf,SAAAsB,KAAAtB,QACAuS,QAAA,GAAAgK,0BAAAjb,KAAA6N,QAAAmQ,OAAAhe,KAAAmK,gBAAAnK,KAAAgL,OAAAhL,KAAAiC,UAAAjC,KAAAkb,gBAAA+C,KAAAD,OAAAD,SAAA,8KAKA9M,QAAAqM,kBAAAtd,KAAAsd,koBA8BA7V,OAAA,qeAmCAwT,yBAAAva,UAAAkc,YAAA,SAAArC,SAAAsC,iBAAAtT,MAAAiR,YAAAsC,SAAA9R,QAEA,GAAAkT,WAKA,IAJA1D,aACA0D,QAAA5f,KAAA0B,KAAAtB,SAGA6b,SAAA1a,OAAA,EAAA,gWA/BA,QAAA8H,iBAAAjJ,QAAAwa,UAAAiF,8BACUne,KAAVtB,QAAAA,QAkFIsB,KAAKkZ,UAATA,UACAlZ,KAAAme,6BAAAA,6BACAne,KAAA8H,SAAA,EAEI9H,KAAKoe,qBACLpe,KAAKqe,oBACLre,KAAK6H,WAAT,GAAA8E,KACA3M,KAAAkI,iBACAlI,KAAAse,kBACAte,KAAAue,aACIve,KAAKwe,0BAAT,KACAxe,KAAAme,qIAIAne,KAAAye,sBAAAze,KAAAme,6BAAA/e,IAAAV,SACAsB,KAAAye,uJAIAze,KAAA0e,kHAQA,OAAA,6EASA/W,gBAAAjH,UAAA2b,0BAAA,WAAA,MAAAlW,QAAAC,KAAApG,KAAAqe,sKAWA1W,gBAAAjH,UAAAub,cAAA,SAAAzU,OAIA,GAAAmX,iBAAA,GAAA3e,KAAA6H,WAAAlI,MAAAwG,OAAAC,KAAApG,KAAAse,gBAAAze,mNAmBA,oCAAA,GAAA8H,iBAAAjJ,QAAAwI,aAAAlH,KAAAkH,YAAAlH,KAAAme,wJASAne,KAAAqe,iBAAAre,KAAA6H,WAAAzI,IAAAY,KAAA8H,ygBA8BA9H,KAAAkI,cAAA5B,OAAAZ,KAAA1F,KAAAkH,YAAA2B,MAAAA,ugBAoCAlB,gBAAAjH,UAAA5B,UAAA,SAAAgQ,MAAArH,OAAAuD,OAAA9F,SACA,GAAA1E,OAAAR,IACAyH,UACAzH,KAAAoe,kBAAA,OAAA3W,OAEA,IAAA2H,QAAAlK,SAAAA,QAAAkK,WACApJ,OAAA4Y,cAAA9P,MAAA9O,KAAAye,sBACAtY,QAAAC,KAAAJ,QAAA/G,QAAA,SAAAqH,6IAIA9F,MAAA+d,UAAAjY,MAAA9F,MAAAie,sBAAAjY,eAAAF,MACA9F,MAAAie,sBAAAnY,sEASAqB,gBAAAjH,UAAA4H,sBAAA,WAEI,GAAJ9H,OAAAR,KACAgG,OAAAhG,KAAAse,eACAvI,MAAA5P,OAAAC,KAAAJ,OACA,IAAA+P,MAAAlW,SAEAG,KAAAse,uGAKAnY,OAAAC,KAAApG,KAAA6e,sBAAA5f,QAAA,SAAAqH;gEASA,GAAA9F,OAAAR,2KASA2H,gBAAAjH,UAAAkH,iBAAA,WAAA,MAAA5H,MAAA6H,WAAAzI,IAAAY,KAAA8H,WACA3B,OAAA4B,eAAAJ,gBAAAjH,UAAA,6BAKA,GAAAsH,kRAYA,GAAAC,UAAAzH,MAAA0H,cAAA5B,MACA6B,SAAAC,SAAAF,cAAA5B,2FASAqB,gBAAAjH,UAAA2H,eAAA,WACA,GAAA7H,OAAAR,IACAA,MAAAsI,uBACA,IAAAC,eAAA,GAAAjG,KA7HAkG,eAAwC,GAAxClG,KA8HAmG,QAAA,IAAAzI,KAAA6H,WAAAlI,MAAA,IAAAK,KAAA8H,SACAY,iBACA1I,MAAA6H,WAAA5I,QAAA,SAAA0J,SAAAjD,MA5HA,GAAAkD,eAAA3C,WAAA0C,UAAA,EA8HAxC,QAAAC,KAAAwC,eAAA3J,QAAA,SAAAqH,MACA,GAAAuC,OAAAD,cAAAtC,KACAuC,QAAAnG,oBAAAgG,WACAH,cAAA9E,IAAA6C,MAGAuC,OAAAnG,oBAAAoG,uCAKAL,UACAG,cAAA,OAAAlD,KAAAlF,MAAAsH,UAEMiB,eAANzK,KAAAsK,gBAGA,IAAAI,UAAAT,cAAA5I,KAAAsJ,gBAAAV,cAAAW,aAGAC,UAAAX,eAAA7I,KAAAsJ,gBAAAT,eAAAU,YAGA,IAAAT,QAAA,yfA0DA,OALAjI,OAAA9B,QAAAA,oPAKA8B,kDAKA4I,mBAAA1I,UAAA2I,kBAAA,WAAA,MAAArJ,MAAAmD,UAAAtD,OAAA,GAKAuJ,mBAAA1I,UAAA2H,eAAA,WAEA,GAAAlF,WAAAnD,KAAAmD,8EAEA,IAAMnD,KAANsJ,0BAAA9B,MAAA,0TA8BA,KAAA,GADA+B,OAAApG,UAAAtD,OAAA,EACA2J,EAAA,EAAAA,GAAAD,MAAAC,IAAA,CAEA,GAAAC,IAAAxD,WAAA9C,UAAAqG,IAAA,uBAGAE,eAAAlC,MAAAmC,UAAA7B,QAGA2B,IAAA,OAAAG,YAAAF,eAAAG,WACAC,aAAAxL,KAAAmL,+NC1+BAM,UAAA,qQA6BAA,WAAArJ,UAAAsJ,eAAA,SAAAtL,QAAAuL,eAAAC,kBAAAhF,QAAAiF,iBACA,GAAAC,OAAAC,MAAAC,QAAAL,gBAAAM,gBAAAN,gBAAA,eACAO,KAAAH,MAAAC,QAAAJ,mBAAAK,gBAAAL,mBAAA,2BC3DAC,iBAAAA,iBAAA,GAAAM,gODwBA,MAAA,IAAAC,OAAAC,mHCKA,MAAAC,oHAAAC,8BAAAnK,UAAAoK,sBAAA,SAAAC,aAAAC,QAAA,MAAAD,igBCUAE,6BAAAvK,UAAAwK,oBAAA,SAAAC,qBAAAC,mBAAAvC,MAAAmC,QACA,GAAAK,MAAA,GACAC,OAAAzC,MAAA0C,WAAAC,MACA,IAAAC,qBAAAL,qBAAA,IAAAvC,OAAA,MAAAA,MACA,GAAA,gBAAAA,OACAwC,KAAA,SAGA,CAEA,GAAAK,mBAAA7C,MAAA8C,MAAA,gKAKA,MAAAL,QAAAD,kwBCUAO,2BAAAlL,UAAAmL,MAAA,SAAA5L,OAAAvB,QAAAoN,aAAAC,UAAA7G,QAAAiF,iBAEI,GAlBM6B,kBAkBVC,sBAAAjM,KAAAkM,IAAAhH,YAAAA,aACAiH,kBAAAnM,KAAAoM,aAAA,SACAC,mBAAArM,KAAAoM,aAAAN,eAAAK,kBACAG,gBAAAtM,KAAAoM,aAAAL,YAAAI,kBACAnB,UACA/I,UAAAsK,wBAAAtM,OAAAvB,QAAAsB,KAAAkM,IAAAM,UAAAH,mBAAAC,gBAAAN,iBAAA7B,gBAAAa,OACA,IAAAA,OAAAnL,OAAA,CAEA,GAAA8K,cAAA,+BAAAK,OAAAyB,KAAA,KACA,MAAA,IAAA/B,OAAAC,cAGA,GAAA+B,aAAA,GAAAC,KACAC,aAAA,GAAAD,KACAjN,gBAAA,GAAA4C,IAEAL,WAAAhD,QAAA,SAAA4N,IACA,GAAAC,KAAAD,GAAAnO,QAIAsK,SAAA5G,gBAAAsK,YAAAI,OACAD,IAAAtE,cAAAtJ,QAAA,SAAAqH,MAAA,MAAA0C,UAAA1C,OAAA,GAEA,IAAA6C,WAAA/G,gBAAAwK,aAAAE,0ZCtDAC,iBAAA,WAIA,QAAAA,kBAAAtI,KAAAyH,KACA,GAAA1L,OAAAR,IACAA,MAAAyE,KAAAA,KACAzE,KAAAkM,IAAAA,IAEAlM,KAAAgN,uBACAhN,KAAAiN,UAEAf,IAAAe,OAAAhO,QAAA,SAAAiN,KACA,GAAAgB,KAAA1M,MAAAyM,OAAAf,IAAAzH,QACAyH,KAAAiB,MAAAnH,OAAA/G,QAAA,SAAAmO,YAEA,gBAAAA,aACAnH,WAAAmH,YAAA,EAAAF,SAJGG,kBAAHrN,KAQAiN,OARA,OAAA,yYAfAF,iBAAArM,UAAA4M,gBAAA,SAAAxB,aAAAC,gMCVAwB,wBAAA,+sBAqDAA,wBAAA7M,UAAA8M,OAAA,SAAAC,GAAA/O,QAAAwG,SACA,GAAA1E,OAAAR,IACA,UAAAkF,UAAAA,WACA,IACAwI,cADA1C,UAlBAkB,IAAAlM,KAAA2N,YAAAF,IAoBAG,cAAA,GAAAjB,IAiBA,IAhBAT,KAEAwB,aAAAnB,wBAAAvM,KAAA6N,QAAAnP,QAAAwN,UAAAhH,QAAA4I,sBAAA9C,QACA0C,aAAAzO,QAAA,SAAA8O,MAEA,GAAA/H,QAAA5D,gBAAAwL,cAAAG,KAAArP,WAEAqP,MAAAvF,eAAAvJ,QAAA,SAAAqH,MAAA,MAAAN,QAAAM,MAAA,WAMA0E,OAAA1M,KAAA,uEACAoP,iBAEA1C,OAAAnL,OACA,KAAA,IAAA6K,OAAA,+DAAAM,OAAAyB,KAAA,MAGAmB,eAAA3O,QAAA,SAAA+G,OAAAtH,SACAyH,OAAAC,KAAAJ,QAAA/G,QAAA,SAAAqH,MAAAN,OAAAM,MAAA9F,MAAAqN,QAAAG,aAAAtP,QAAA4H,KAAA5D,oBAAAoG,0IAMAvK,OAAA0P,oBAAAxO,mPAXAO,MAAAkO,aAAAT,0eAgEAF,wBAAA7M,UAAAyN,QAAA,SAAAV,GAAA/O,QAAAyP,QAAAC,MACA,GAAA,YAAAD,QAEA,WADMnO,MApCKqO,SAoCXZ,GAAAW,KAAA,GAGA,IAAA,UAAAD,QAAA,CACA,GAAAjJ,SAAAkJ,KAAA,MAEA,YADApO,MAAAwN,OAAAC,GAAA/O,QAAAwG,SAGA,GAAA3G,QAAAyB,KAAAsO,WAAAb,GACA,QAAAU,SACA,IAAA,OACA5P,OAAAyI,MACA,MACA,KAAA,QACAzI,OAAAgQ,OACA,MACA,KAAA,QACAhQ,OAAAwI,OACA,MACA,KAAA,UACAxI,OAAAiQ,SACA,MACA,KAAA,SACAjQ,OAAAuI,QACA,MACA,KAAA,OACAvI,OAAAkQ,YV/LA,KAAA,0EU6BAzO,KAAAV,QAAAmO,iCVEE5K,sBACF6L,oBACA9N,YAAA,GAkBA+N,cAAA,KAQAC,cAAA,uDAMAhO,YAAA,GACA+N,cAAA,8CAIAnP,aAAA,eACAqP,WAAA,WAGA,QAAAA,YAAAC,OACA,GAAAC,OAAAD,OAAAA,MAAAtI,eAAA,SACQqC,MAARkG,MAAAD,MAAA,MAAAA,KAEA,IADA9O,KAAA6I,MAAAmG,sBAAAnG,OACAkG,MAAA,CACA,GAAA7J,SAAA+J,QAAAH,qDAKA9O,MAAAkF,+DAUA2J,YAAAnO,UAAAwO,cAAA,SAAAhK,SACA,GAAAiK,WAAAjK,QAAAkK,MAEA,IAAAD,UAAA,CASA,GAAAE,aAAArP,KAAAkF,QAAA,MACAiB,QAAAC,KAAA+I,WAAAlQ,QAAA,SAAAqH,MACA,MAAA+I,YAAA/I,oNAiBA,QAAAgJ,8BAAA7B,GAAAjJ,YAAA+K,uOAsBAD,8BAAA5O,UAAA8O,OAAA,SAAA9Q,QAAA+F,KAAAgL,MAAAC,UAEA,GAAAlP,OAAAR,IACA,KAAAA,KAAA2P,UAAAnJ,eAAA/B,MACA,KAAA,IAAAiG,OAAA,oDAAA+E,MAAA,oCAAAhL,KAAA,oBAGA,IAAA,MAAAgL,OAAA,GAAAA,MAAA5P,OACA,KAAA,IAAA6K,OAAA,8CAAAjG,KAAA,6CAEA,KAAAmL,oBAAAH,OACA,KAAA,IAAA/E,OAAA,yCAAA+E,MAAA,gCAAAhL,KAAA,2JAMA,IAAAoL,oBAAAzN,gBAAApC,KAAAuP,QAAAO,gBAAApR,WAOA,OANAmR,oBAAArJ,eAAA/B,8HAMA,WAIAjE,MAAA+O,QAAAQ,WAAA,geA2CAT,6BAAA5O,UAAAkE,QAAA,SAAAlG,QAAAwC,YAAA2H,MAAAmH,mBAEI,GAAJxP,OAAAR,IACA,UAAAgQ,oBAAAA,mBAAA,EAEA,IAAApL,SAAA5E,KAAAiQ,YAAA/O,aACA3C,OAAA,GAAAoF,2BAAA3D,KAAAyN,GAAAvM,YAAAxC,SACAmR,mBAAA7P,KAAAuP,QAAAO,gBAAA1Q,IAAAV,QACAmR,sBAEA7L,SAAAtF,QAAAwR,sBAEAlM,SAAAtF,QAAAwR,qBAAA,IAAAhP,aACMlB,KAANuP,QAAAO,gBAAAK,IAAAzR,QAAAmR,uBAjCA,IAAAO,WAAAP,mBAAA3O,aAmCAmP,QAAA,GAAAxB,YAAAhG,OACAkG,MAAAlG,OAAAA,MAAArC,eAAA,aAEAuI,OAAAqB,WAEAC,QAAAnB,cAAAkB,UAAAlL,8NAWAoL,kBAAArR,QAAA,SAAAV,QAIAA,OAAAqC,aAAAJ,MAAAiN,IAAAlP,OAAA2C,aAAAA,aAAA3C,OAAAkD,QACAlD,OAAAe,WAMA,IAAAiR,YAAA3L,QAAA0I,gBAAA8C,UAAAvH,MAAAwH,QAAAxH,OACA2H,sBAAA,CACA,KAAAD,WAAA,CAEA,IAAAP,kBACA,MAEMO,YAAN3L,QAlCU6L,mBAmCJD,sBAAN,QAEAxQ,MAAAuP,QAAAmB,qBAEA1Q,KAAA2Q,OAAArS,MAAAI,QAAAA,QAAAwC,YAAAA,YAAAqP,WAAAA,WAAAH,UAAAA,UAAAC,QAAAA,QAAA9R,OAAAA,OAAAiS,qBAAAA,uBACAA,sBACAxM,SAAAtF,QAAAuF,wBAEA1F,OAAAuF,OAAA,WACA8M,YAAAlS,QAAAuF,uBACA,IAAA4M,OAAArQ,MAAAf,QAAAqR,QAAAvS,OACAsS,QAAA,GAEArQ,MAAAf,QAAAsR,OAAAF,MAAA,EAGA,IAAApR,SAAAe,MAAA+O,QAAAjO,iBAAAlC,IAAAV,QACA,IAAAe,QAAA,oOAmBAO,MAAA2P,UAAAlL,4lBAyBA6K,6BAAA5O,UAAAsQ,mBAAA,SAAApP,YAAAqP,QAAAC,uBA5CA,UAAAA,UAAAA,SAAA,GA8CAlR,KAAAuP,QAAAtP,OAAAC,MAAA0B,YAAAuP,qBAAA,GAAAlS,QAAA,SAAA6N,KACA,GAAAoE,SAAAE,cAAAtE,IAAAtM,MAAA6Q,gBAAA,CACA,GAAAC,SAAA9Q,MAAA+O,QAAAgC,wBAAAnS,IAAA0N,+NAqBA0E,OAAAxR,KAAAuP,SACAkC,cAAA/S,QAAAgT,mBACA1R,KAAAgR,mBAAAtS,QAAAuS,SAAA,wDAGA,IAAAU,cAAA,CACA,GAAAC,aAaA,IAXAzL,OAAAC,KAlDkBuL,eAkDlB1S,QAAA,SAAAiC,aAGA,GAAAV,MAAAmP,UAAAzO,aAAA,CACA,GAAA3C,QAAAiC,MAAAoE,QAAAlG,QAAAwC,YAAAM,YAAA,EACAjD,mCAMAqT,UAAA/R,0OAWA,GAAAgS,gBAAAL,OAAA/R,QAAAI,OAAA2R,OAAA1Q,wBAAA1B,IAAAV,WAKA,IAAAmT,gBAAAA,eAAAhS,OACAiS,mCAAA,sHAQAA,mCAAA,UAUA,GAAAC,WAlDc/R,KAkDdgS,kBAAA5S,IAAAV,QACA,IAAAqT,UAAA,CACA,GAAAE,mBAAA,GAAA3P,IACAyP,WAAA9S,QAAA,SAAAiT,UAEQ,GAARhR,aAAAgR,SAAAzN,IACQ,KAARwN,kBAAAlR,IAAAG,aAAQ,CAER+Q,kBAAAxO,IAAAvC,YACA,IAAA0D,SAAApE,MAAAmP,UAAAzO,aACAqP,WAAA3L,QAAA6L,mBACA0B,cAAAX,OAAA1B,gBAAA1Q,IAAAV,SACA0R,UAAA+B,cAAAjR,cAAAkR,oBACA/B,QAAA,GAAAxB,YAAArN,YACAjD,OAAA,GAAAoF,2BAAAnD,MAAAiN,GAAAvM,YAAAxC,QACA8B,OAAA+O,QAAAmB,qBACAlQ,MAAAmQ,OAAArS,8CAIAiS,WAAAA,WACAH,UAAAA,UACAC,QAAAA,ySAnCAf,6BAAA5O,UAAA2R,WAAA,SAAA3T,QAAA4T,QAAAtO,SAAAtF,QAAAsB,KAAAqR,iBA8DA/B,6BAAA5O,UAAA6R,uBAAA,SAAAC,aACA,GAAAhS,OAAAR,KACA0N,eA+BA,OA7BA1N,MAAA2Q,OAAA1R,QAAA,SAAAwT,OACA,GAAAlU,QAAAkU,MAAAlU,MACA,KAAAA,OAAAmU,UAAA,CAEA,GAAAhU,SAAA+T,MAAA/T,QAEAqT,UAAAvR,MAAAwR,kBAAA5S,IAAAV,QACAqT,sFAGA,GAAAY,WAAAC,mBAAAlU,QAAA+T,MAAAvR,YAAAuR,MAAArC,UAAAvH,MAAA4J,MAAApC,QAAAxH,MACA,WAAA,MAAA2J,YACAK,eAAAJ,MAAAlU,OAAA2T,SAAAzC,MAAAkD,UAAAT,SAAAxC,aAGAnR,OAAAuU,iBAvDAtS,MAAA+O,QAAAQ,WAAA,0DAoEA/P,KAAA2Q,UACAjD,aAAAqF,KAAA,SAAAC,EAAAC,gEAKA,OAAA,IAAAC,IAAA,GAAAC,maAoEAnT,KAAA2Q,OAAAyC,KAAA,SAAAX,OAAA,MAAAA,OAAA/T,UAAAA,WAAAmC,8CAzGAJ,0BAAA,WAMA,QAAAA,2BAAAR,OAAAoD,aACUrD,KAAVC,OAAAA,OACUD,KAAVqD,YAAAA,YACUrD,KAAVP,WAESO,KAATqT,gBAAA,GAAA1G,KACS3M,KAATsB,iBAAA,GAAAqL,KACS3M,KAATc,wBAAA,GAAA6L,KAGS3M,KAAT8P,gBAAA,GAAAnD,KAIA3M,KAAAsT,gBAAA,gRA+FA7S,2BAAAC,UAAA6S,mBAAA,SAAA7U,QAAAuS,SAAAjR,KAAAwT,kBAAA9U,QAAAuS,UACA9K,OAAA4B,eAAAtH,0BAAAC,UAAA,iBAIAtB,IAAA,4hBAwCAqB,0BAAAC,UAAA+S,sBAAA,SAAAC,GAAAlP,aACA,GAAA+E,OAAAvJ,KAAA2T,eAAA9T,OAAA,CACA,IAAA0J,OAAA,EAhGkB,CAkGlB,IAAA,GADAqK,QAAA,EACApK,EAAAD,MAAAC,GAAA,EAAAA,IAAA,CACA,GAAAqK,eAAA7T,KAAA2T,eAAAnK,EAhGA,IAAAxJ,KAAAC,OAAA6T,gBAAAD,cAAArP,YAAAA,aAAA,CAiGAxE,KAAA2T,eAAA5C,OAAAvH,EAAA,EAAA,EAAAkK,IACAE,OAAA,CA9FA,mgBA0IAnT,0BAAAC,UAAApB,QAAA,SAAAsB,YAAAqQ,SACA,GAAAzQ,OAAAR,IACA,IAAAY,YAAA,CAGA,GAAA8S,IAAA1T,KAAA+T,gBAAAnT,kLA3GAiQ,QAAA,6kBAyJMtR,QAANoP,eAAA,oPAyBAlO,0BAAAC,UAAAsT,WAAA,SAAApT,YAAAlC,QAAAuS,QAAAQ,cACA,IAAAwC,cAAAvV,oBACAsB,MAAAuT,mBAAA7U,QAAAuS,q2BAuDAxQ,0BAAAC,UAAAwT,uBAAA,SAAAC,kBACA,GAAA3T,OAAAR,2JAGAP,UACAA,QAAAR,QAAA,SAAAV,QAIAA,OAAAkD,OACAlD,OAAAuU,kBAAA,EAtJAvU,OAAAe,WA8JA,IAAA8U,UAAA5T,MAAAsP,gBAAA1Q,IAAAV,QACA0V,WACAjO,OAAAC,KAAAgO,UAAAnV,QAAA,SAAAiC,aAAA,MAAAkT,UAAAlT,aAAAmT,wBAGA,GAAArU,KAAAc,wBAAAnB,iIAKA,GAAAF,SAAAe,MAAAM,wBAAA1B,IAAAV,QACAe,wEAOAgB,0BAAAC,UAAA4T,kBAAA,qLAeA7T,0BAAAC,UAAAH,iBAAA,SAAA7B,SACA,GAAAa,SAAAb,QAAAc,aACA,IAAAD,SAAAA,QAAAoP,cAAA,IAEAjQ,QAAAc,cAAAkP,kIAKAgF,GAAAa,kBAAA7V,kEASA+B,0BAAAC,UAAA8T,MAAA,SAAAhC,aACA,GAAAhS,OAAAR,IAjKA,UAAAwS,cAAAA,aAAA,EAkKA,IAAA/S,WAMA,IALAO,KAAAqT,gBAAA1T,OACAK,KAAAqT,gBAAApU,QAAA,SAAAyU,GAAAhV,SAAA,MAAA8B,OAAAiT,sBAAAC,GAAAhV,WACAsB,KAAAqT,gBAAAoB,SAGQzU,KAAR2T,eAAA9T,SACAG,KAAA0Q,oBAAA1Q,KAAA0U,uBAAA7U,QACAJ,QAAAO,KAAA2U,iBAAAnC,iBAIQ,KAAR,GAAAhJ,GAAA,EAAAA,EAAAxJ,KAAA0U,uBAAA7U,OAAA2J,IAAA,2EAUA,GALAxJ,KAAA0Q,mBAAA,8HA7JA1Q,KAAA4U,aAkKA5U,KAAA6U,cAAAhV,OAAA,uNAkBAY,0BAAAC,UAAAiU,iBAAA,SAAAnC,aAkBA,IAAA,GAjBAhS,OAAAR,KACA8U,aAAA,GAAArK,yFAKA/K,gBAAA,GAAAiN,KACAoI,oBAAA,GAAApI,0GAKA3M,KAAAgV,uBAAAC,OAAAC,qBAAAlV,KAAAgV,4BA/JuBxL,EAoKvB,EAAAA,EAAA2L,cAAAtV,OAAA2J,IACAxF,SAAAmR,cAAA3L,GAAA4L,gBAIA,KAAA,GAFAC,kBACAC,+BACA9L,EAAA,EAAAA,EAAAxJ,KAAA0U,uBAAA7U,OAAA2J,IAAA,CAEA,GAAA9K,SApKsBsB,KAoKtB0U,uBAAAlL,GACAjK,QAAAb,QAAAc,aACAD,UAAAA,QAAAoP,gBACA3K,SAAAtF,QAAA6W,iBAEAF,cAAA/W,KAAAI,SACAa,QAAAqP,cACA0G,4BAAAhX,KAAAI,UAKA,IAAA,GAAA8K,GAAAxJ,KAAA2T,eAAA9T,OAAA,EAAA2J,GAAA,EAAAA,IAAA,CApKA,GAAAkK,IAAA1T,KAAA2T,eAAAnK,0FAwKA9K,QAAA+T,MAAA/T,OACA,KAAA8W,WAAAhV,MAAAP,OAAA6T,gBAAA0B,SAAA9W,oBACUH,QApKOe,SAuKjB,IAAAX,aAAA6B,MAAAiV,kBAAAhD,MAAAqC,+PAuBAnW,aAAcsD,UAAdhD,QAAA,SAAA4N,IAAA,MAAAA,IAAA6I,yBAAA,IACAZ,aAAAa,OAAAjX,QAAAC,YAAAsD,UACA,IAAA2T,QAAAjX,YAAAA,YAAAJ,OAAAA,OAAAG,QAAAA,QACAmX,oBAAAvX,KAAAsX,OACAjX,YAAAe,gBAAAT,QAAA,SAAAP,SAAA,MAAA0D,iBAAA1C,gBAAAhB,YAAAJ,KAAAC,UAlKAI,YAAoB4J,cAApBtJ,QAAA,SAAA6W,UAAApX,SAqKA,GAAAqX,OAAA5P,OAAAC,KAAA0P,UACA,IAAAC,MAAAlW,OAAA,CACA,GAAAmW,UAAAjB,oBAAA3V,IAAAV,QACAsX,WACAjB,oBAAA5E,IAAAzR,QAAAsX,SAAA,GAAA1T,MAEAyT,MAAA9W,QAAA,SAAAqH,MAAA,MAAA0P,UAAAvS,IAAA6C,2GAMA2P,OAAAC,qBAAA9W,IAAAV,QACAuX,SACAC,qBAAA/F,IAAAzR,QAAAuX,OAAA,GAAA3T,kEAUA,IAAA,GADA6T,gCACA3M,EAAA,EAAAA,EAAA2L,cAAAtV,OAAA2J,IAAA,CACA,GAAA9K,SAAAyW,cAAA3L,EACAsL,cAAA/T,IAAArC,UAEAyX,4BAAA7X,KAAAI,SAIA,GAAAiD,uBAAA,GAAAgL,KACAyJ,uBAEAP,oBAAA5W,QAAA,SAAAwT,OAEA,GAAA/T,SAAA+T,MAAA/T,OAEAoW,cAAA/T,IAAArC,iDAGA8B,MAAAkB,sBAAA+Q,MAAAlU,OAAAqC,YAAA6R,MAAA9T,YAAAgD,6FASAiB,gBAAApC,MAAAQ,oBAAAtC,SAAA,EAAAH,OAAAqC,YAAArC,OAAA2C,YAAA,KACA0B,iBAAA3D,QAAA,SAAAoX,YAAAjU,gBAAAT,sBAAAjD,YAAAJ,KAAA+X,8KAKAC,sBAAAtW,KApKmCC,OAoKnCkW,4BAAApB,oBAAArS,oBAAAgG,YACA,GAAAiE,qJAOA3N,aACA6W,oBAAA5W,QAAA,SAAAwT,OApKA,GAAA/T,SAAA+T,MAAA/T,QAAAH,OAAAkU,MAAAlU,OAAAI,YAAA8T,MAAA9T,WAuKA,IAAAmW,aAAA/T,IAAArC,SAAA,CACA,GAAA6X,aAAA/V,MAAAgW,gBAAAjY,OAAAqC,YAAAjC,YAAAgD,sBAAAxC,kBAAAsX,aAAAvT,cACA3E,QAAAqF,cAAA2S,YAGA,KAAA,GADAG,mBAAA,KACAlN,EAAA,EAAAA,EAAA4M,qBAAAvW,OAAA2J,IAAA,CACA,GAAA8I,QAAA8D,qBAAA5M,EACA,IAAA8I,SAAA5T,QACA,KACU,IAAV8B,MAAAP,OAAA6T,gBAAAxB,OAAA5T,SAAA,CACAgY,kBAAApE,MApKA,QAuKA,GAAAoE,kBAAA,CApKA,GAAAC,eAAAnW,MAAAc,iBAAAlC,IAAAsX,kBAqKAC,gBAAAA,cAAA9W,SACAtB,OAAAc,aAAA4O,oBAAA0I;AAEAtY,eAAAC,KAAAC,YAIAC,aAAAF,KAAAC,YAIAE,aAAAC,QAAAC,YAAAC,YACAL,OAAAM,UAAA,WAAA,MAAAC,WAAAJ,QAAAC,YAAAI,sCAKAC,WAAAC,QAAA,SAAAV,QACA,GAAAW,mBAAAC,kBAAAC,IAAAb,OAAAG,6LAUAH,OAAAc,aACAd,OAAAc,aAAAR,UAAA,WAAA,MAAAN,QAAAe,oGAaAC,QAAAb,QAAAc,kDAOA,GAAAC,WAIA,IAAAC,gBAAAC,KAAA,CACA,GAAAC,sBAAAF,gBAAAN,IAAAV,QACAkB,uBAAAA,qBAAAC,QACAJ,QAAAnB,KAAAwB,MAAAL,QAAAG,qBAlKA,KAAA,GAoKAG,sBAAAC,KAAAC,OAAAC,MAAAxB,QAAAyB,uBAAA,GApKAC,EAAA,EAAAA,EAAAL,qBAAAF,OAAAO,IAAA,CAqKA,GAAAC,gBAAAX,gBAAAN,IAAAW,qBAAAK,GACAC,iBAAAA,eAAAR,QACAJ,QAAAnB,KAAAwB,MAAAL,QAAAY,iBAKAZ,QAAAI,OAEAS,8BAAAN,KAAAtB,QAAAe,SAGAO,KAAAO,iBAAA7B,gBAKAF,aAAAS,QAAA,SAAAV,QACAiC,MAAAf,QAAAnB,KAAAC,6OA5JAkC,0BAAAC,UAAAC,oBAAA,SAAAC,YAAAlC,SAyKA,GAAAmC,eAAA,EACAtB,QAAAb,QAAAc,6HAKAQ,KAAAc,wBAAAC,IAAArC,mXAyBA+B,0BAAAC,UAAAM,oBAAA,SAAAtC,QAAAuC,iBAAAL,YAAAM,YAAAC,cACA,GAAA1B,WACA,IAAAwB,iBAAA,CACA,GAAAG,uBAAApB,KAAAc,wBAAA1B,IAAAV,QArLA0C,yBAsLA3B,QAAA2B,2BAEA,CACA,GAAAC,gBAAArB,KAAAsB,iBAAAlC,IAAAV,QACA,IAAA2C,eAAA,CACA,GAAAE,uBAAAJ,cAAAA,cAAAK,UACAH,gBAAApC,QAAA,SAAAV,QACAA,OAAAkD,SACAF,sBAAAhD,OAAA2C,aAAAA,cACAzB,QAAAnB,KAAAC,kBAIAqC,aAAAM,0KAqBAT,0BAAAC,UAAAgB,sBAAA,SAAAd,YAAAjC,YAAAgD,uBAGI,GAAJnB,OAAAR,IAGAvB,aAAAE,YAAAD,QAAAC,YAAAC,WACA,IAAAsC,aAAAvC,YAAAuC,YAEAU,YAAAjD,YAAAD,QAGAmD,kBAAAlD,YAAAmD,oBAAAC,OAAAnB,YACAoB,kBAAArD,YAAAmD,oBAAAC,OAAAb,WACAvC,aAAAsD,UAAAC,IAAA,SAAAC,qBACA,GAAAzD,SAAAyD,oBAAAzD,QACAuC,iBAAAvC,UAAAkD,YACAnC,QAAA2C,gBAAAT,sBAAAjD,8cA2BAwC,YAAAvC,YAAAuC,YACAU,YAAAjD,YAAAD,6BAIA2D,oBAAA,GAAAC,KACAC,eAAA,GAAAD,KACAE,cAAA7D,YAAAsD,UAAAC,IAAA,SAAAC,qBACA,GAAAzD,SAAAyD,oBAAAzD,QAEAa,QAAAb,QAAAc,aACA,IAAAD,SAAAA,QAAAkD,qBACA,MAAA,IAAAC,qBAAAC,mBACA,IAAA1B,kBAAAvC,UAAAkD,YACAgB,gBAAAC,kBAGA,KAAAR,oBAAAtB,IAAArC,SAAA,yFAIAoE,oBACAF,gBAAAE,iBAAAZ,IAAA,SAAAa,GAAA,MAAAA,GAAAC,2DAKAC,WAAAC,cAAA9D,IAAAV,SACAyE,UAAAC,mBAAA5C,MAAAP,OAAAO,MAAA6C,YAAA3E,QAAAyD,oBAAAgB,UAAAG,UAAAL,YACA1E,OAAAiC,MAAA+C,aAAApB,oBAAAgB,UAAAP,gBAQA,IAHAT,oBAAAqB,aAAArE,mBACAoD,eAAAkB,IAAA/E,SAEAuC,iBAAA,CAEA,GAAAyC,eAAA,GAAAC,2BAAA/C,YAAAM,YAAAxC,QACAgF,eAAAE,cAAArF,QACAsF,kBAAAvF,KAAAoF,eAEA,MAAAnF,mIAKAA,OAAAuF,OAAA,WAAA,MAAAC,oBAAAvD,MAAAM,wBAAAvC,OAAAG,QAAAH,YAIA8D,oBAAApD,QAAA,SAAAP,SAAA,MAAAsF,UAAAtF,QAAAuF,+0BArKGjE,KAkPHtB,QAlPAA,8cAqQAiF,0BAAAjD,UAAAkD,cAAA,SAAArF,kDAIAyB,KAAAkE,QAAA3F,mcAuBAoF,0BAAAjD,UAAAoD,OAAA,SAAAK,sEAUAR,0BAAAjD,UAAA0D,QAAA,SAAAD,iyBA6CAnE,KAAAkE,QAAA5E,sgBCl8CA+E,gBAAA,8YAwBAA,iBAAA3D,UAAA4D,gBAAA,SAAAC,YAAA3D,YAAA4D,YAAAC,KAAAC,UACA,GAAAC,UAAAJ,YAAA,IAAAE,KACAG,QAAA5E,KAAA6E,cAAAF,SACA,KAAAC,QAAA,uwCA2EA,GAAA,KAAAE,UAAAC,OAAA,GAAA,waAYA,MAAA/E,MAAAgF,kBAAA,0OC7HA,QAAAC,qBAAAvG,QAAAyE,UAAA+B,QAAAtC,iBACA,SAAAA,kBAAAA,mBACU,IAAVpC,OAAAR,IACUA,MAAVtB,QAAAA,QAESsB,KAATmD,UAAAA,UAESnD,KAATkF,QAAAA,QAESlF,KAAT4C,gBAAmEA,gBAW/D5C,KAAKmF,cACLnF,KAAKoF,eACLpF,KAAKqF,iBAELrF,KAAKsF,cAAT,EACItF,KAAJuF,WAAA,EACAvF,KAAAwF,UAAA,EACAxF,KAAAyF,YAAA,EACAzF,KAAA0F,KAAA,EACA1F,KAAAX,aAAA,0FAHAW,KAAA0F,KAAA1F,KAAA2F,UAAA3F,KAAA4F,OAQA5F,KAAA6F,iMASAZ,qBAAAvE,UAAAoF,UAAA,WACA9F,KAAAuF,wKAWAvF,KAAA+F,6BAIAd,oBAAAvE,UAAA6C,aAAA,WACA,GAAA/C,OAAAR,IACA,KAAAA,KAAAsF,aAAA,CAEAtF,KAAAsF,cAAA,CACA,IAAAnC,WAAAnD,KAAAmD,UAAAjB,IAAA,SAAA8D,QAAA,MAAAC,YAAAD,QAAA,KAEAE,mBAAAC,OAAAC,KAAApG,KAAA6F,eACA,IAAAK,mBAAArG,OAAA,qCAEAwG,sBASA,IARAH,mBAAAjH,QAAA,SAAAqH,MACAC,mBAAAC,eAAAF,OACAD,oBAAA/H,KAAAgI,MAGAC,mBAAAD,MAAA9F,MAAAqF,eAAAS,QAGAD,oBAAAxG,cACA4G,QAAAzG,KACA0G,QAAA,8BANAL,qBAAApH,QAAA,SAAAqH,4OAEAtG,KAAAkE,QAAAyC,iBAAA,SAAA,WAAA,MAAAnG,OAAAsF,iJAqCAb,oBAAAvE,UAAAkG,qBAAA,SAAAlI,QAAAyE,UAAA+B,yLAUAD,oBAAAvE,UAAA0D,QAAA,SAAAD,IAAAnE,KAAAoF,YAAA9G,KAAA6F,KAKAc,oBAAAvE,UAAAoD,OA/BS,SA+BTK,IAAAnE,KAAAmF,WAAA7G,KAAA6F,KAKAc,oBAAAvE,UAAA7B,UAAA,SAAAsF,IAAAnE,KAAAqF,cAAA/G,KAAA6F,uEAMAnE,KAAA6G,eACA7G,KAAAoF,YAAAnG,QAAA,SAAAkF,IAAA,MAAAA,iOAmBAnE,KAAAkE,QAAA4C,UAKA7B,oBAAAvE,UAAAqG,MAAA,+PAmBA/G,KAAAgH,QAKA/B,oBAAAvE,UAAAmG,WAAA,WAAA,MAAA7G,MAAAwF,yGAOAxF,KAAA8F,6GAaAb,oBAAAvE,UAAAuG,YAAA,SAAAlE,GAAA/C,KAAAkE,QAAAgD,YAAAnE,EAAA/C,KAAA0F,MAAAT,oBAAAvE,UAAAyG,YAtDwD,WAsDxD,MAAAnH,MAAAkE,QAAAgD,YAAAlH,KAAA0F,uIAIA0B,cAAA,IAIAnC,oBAAAvE,UAAA2G,cAAA,WACA,GAAA7G,OAAAR,KACAgG,SACAhG,MAAA6G,cAEAV,OAAAC,KAAApG,KAAAsH,gBAAArI,QAAA,SAAAqH,2xBC1KA,SAAA1D,kBAAAA,mBACA,IAAA2E,MAAA,GAAAC,MAAA,OAAA,kEAIAC,UACAC,cAAA,OAAAD"}