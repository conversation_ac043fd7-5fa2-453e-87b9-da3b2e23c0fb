{"version": 3, "file": "ngtools_api.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/ngtools_api.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAEH;;;;;GAKG;AAEH,8CAAgI;AAIhI,qCAAwC;AACxC,iDAA+F;AAC/F,yCAAsC;AACtC,+CAAsD;AACtD,yEAAmE;AA6CnE;;;GAGG;AACH;IAAsD,2DAA2B;IAC/E,iDACY,aAAgD,EAAE,IAA6B;QAD3F,YAEE,kBAAM,IAAI,CAAC,SACZ;QAFW,mBAAa,GAAb,aAAa,CAAmC;;IAE5D,CAAC;IAED,8DAAY,GAAZ,UAAa,IAAY,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,8CAAC;AAAD,CAAC,AAPD,CAAsD,2CAA2B,GAOhF;AAGD;;;GAGG;AACH;IAAA;IAyEA,CAAC;IAxEC;;;OAGG;IACI,gCAAO,GAAd,UAAe,OAAgD;QAC7D,IAAM,WAAW,GACb,IAAI,uCAAuC,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpF,IAAM,UAAU,GAAkB;YAChC,UAAU,EAAE,OAAO,CAAC,UAAY;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAU;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAQ;YACxB,kBAAkB,EAAE,OAAO,CAAC,kBAAoB;YAChD,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QACF,IAAM,SAAS,GAAG,OAAO,CAAC,sBAAsB,CAAC;QACjD,EAAE,CAAC,CAAC,SAAS,CAAC,qBAAqB,KAAK,SAAS,CAAC,CAAC,CAAC;YAClD,mBAAmB;YACnB,SAAS,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAC1C,CAAC;QAED,6BAA6B;QAC7B,IAAM,aAAa,GACf,uBAAa,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAE5F,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,uCAAc,GAArB,UAAsB,OAAuD;QAE3E,IAAM,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;QAC9D,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,IAAM,oBAAoB,GAAG,IAAI,2CAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAM,cAAc,GAChB,CAAC,CAAC,sBAAsB,CAAC,QAAQ,IAAI,sBAAsB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACpF,IAAM,cAAc,GAAoB,cAAc;YAClD,IAAI,kDAAsB,CAAC,OAAO,EAAE,sBAAsB,EAAE,oBAAoB,CAAC;YACjF,IAAI,4BAAY,CAAC,OAAO,EAAE,sBAAsB,EAAE,oBAAoB,CAAC,CAAC;QAE5E,IAAM,WAAW,GAAG,IAAI,4BAAiB,EAAE,CAAC;QAC5C,IAAM,eAAe,GAAG,IAAI,6BAAkB,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC5E,IAAM,cAAc,GAAG,IAAI,+BAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QAC9F,IAAM,eAAe,GAAG,IAAI,0BAAe,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAC7E,IAAM,QAAQ,GAAG,qCAAsB,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QAE9F,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAC/B,UAAC,GAA0C,EAAE,KAAa;YACxD,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACI,oCAAW,GAAlB,UAAmB,OAAoD;QACrE,IAAM,WAAW,GACb,IAAI,uCAAuC,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpF,6BAA6B;QAC7B,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC;QACtC,IAAM,SAAS,GAAG,qBAAS,CAAC,MAAM,CAC9B,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAExF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,UAAY,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;IAC1E,CAAC;IACH,+BAAC;AAAD,CAAC,AAzED,IAyEC;AAzEY,4DAAwB", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This is a private API for the ngtools toolkit.\n *\n * This API should be stable for NG 2. It can be removed in NG 4..., but should be replaced by\n * something else.\n */\n\nimport {AotCompilerHost, AotSummaryResolver, StaticReflector, StaticSymbolCache, StaticSymbolResolver} from '@angular/compiler';\nimport {AngularCompilerOptions, NgcCliOptions} from '@angular/tsc-wrapped';\nimport * as ts from 'typescript';\n\nimport {CodeGenerator} from './codegen';\nimport {CompilerHost, CompilerHostContext, ModuleResolutionHostAdapter} from './compiler_host';\nimport {Extractor} from './extractor';\nimport {listLazyRoutesOfModule} from './ngtools_impl';\nimport {PathMappedCompilerHost} from './path_mapped_compiler_host';\n\nexport interface NgTools_InternalApi_NG2_CodeGen_Options {\n  basePath: string;\n  compilerOptions: ts.CompilerOptions;\n  program: ts.Program;\n  host: ts.CompilerHost;\n\n  angularCompilerOptions: AngularCompilerOptions;\n\n  // i18n options.\n  i18nFormat?: string;\n  i18nFile?: string;\n  locale?: string;\n  missingTranslation?: string;\n\n  readResource: (fileName: string) => Promise<string>;\n\n  // Every new property under this line should be optional.\n}\n\nexport interface NgTools_InternalApi_NG2_ListLazyRoutes_Options {\n  program: ts.Program;\n  host: ts.CompilerHost;\n  angularCompilerOptions: AngularCompilerOptions;\n  entryModule: string;\n\n  // Every new property under this line should be optional.\n}\n\nexport interface NgTools_InternalApi_NG_2_LazyRouteMap { [route: string]: string; }\n\nexport interface NgTools_InternalApi_NG2_ExtractI18n_Options {\n  basePath: string;\n  compilerOptions: ts.CompilerOptions;\n  program: ts.Program;\n  host: ts.CompilerHost;\n  angularCompilerOptions: AngularCompilerOptions;\n  i18nFormat?: string;\n  readResource: (fileName: string) => Promise<string>;\n  // Every new property under this line should be optional.\n  locale?: string;\n  outFile?: string;\n}\n\n/**\n * A ModuleResolutionHostAdapter that overrides the readResource() method with the one\n * passed in the interface.\n */\nclass CustomLoaderModuleResolutionHostAdapter extends ModuleResolutionHostAdapter {\n  constructor(\n      private _readResource: (path: string) => Promise<string>, host: ts.ModuleResolutionHost) {\n    super(host);\n  }\n\n  readResource(path: string) { return this._readResource(path); }\n}\n\n\n/**\n * @internal\n * @private\n */\nexport class NgTools_InternalApi_NG_2 {\n  /**\n   * @internal\n   * @private\n   */\n  static codeGen(options: NgTools_InternalApi_NG2_CodeGen_Options): Promise<any> {\n    const hostContext: CompilerHostContext =\n        new CustomLoaderModuleResolutionHostAdapter(options.readResource, options.host);\n    const cliOptions: NgcCliOptions = {\n      i18nFormat: options.i18nFormat !,\n      i18nFile: options.i18nFile !,\n      locale: options.locale !,\n      missingTranslation: options.missingTranslation !,\n      basePath: options.basePath\n    };\n    const ngOptions = options.angularCompilerOptions;\n    if (ngOptions.enableSummariesForJit === undefined) {\n      // default to false\n      ngOptions.enableSummariesForJit = false;\n    }\n\n    // Create the Code Generator.\n    const codeGenerator =\n        CodeGenerator.create(ngOptions, cliOptions, options.program, options.host, hostContext);\n\n    return codeGenerator.codegen();\n  }\n\n  /**\n   * @internal\n   * @private\n   */\n  static listLazyRoutes(options: NgTools_InternalApi_NG2_ListLazyRoutes_Options):\n      NgTools_InternalApi_NG_2_LazyRouteMap {\n    const angularCompilerOptions = options.angularCompilerOptions;\n    const program = options.program;\n\n    const moduleResolutionHost = new ModuleResolutionHostAdapter(options.host);\n    const usePathMapping =\n        !!angularCompilerOptions.rootDirs && angularCompilerOptions.rootDirs.length > 0;\n    const ngCompilerHost: AotCompilerHost = usePathMapping ?\n        new PathMappedCompilerHost(program, angularCompilerOptions, moduleResolutionHost) :\n        new CompilerHost(program, angularCompilerOptions, moduleResolutionHost);\n\n    const symbolCache = new StaticSymbolCache();\n    const summaryResolver = new AotSummaryResolver(ngCompilerHost, symbolCache);\n    const symbolResolver = new StaticSymbolResolver(ngCompilerHost, symbolCache, summaryResolver);\n    const staticReflector = new StaticReflector(summaryResolver, symbolResolver);\n    const routeMap = listLazyRoutesOfModule(options.entryModule, ngCompilerHost, staticReflector);\n\n    return Object.keys(routeMap).reduce(\n        (acc: NgTools_InternalApi_NG_2_LazyRouteMap, route: string) => {\n          acc[route] = routeMap[route].absoluteFilePath;\n          return acc;\n        },\n        {});\n  }\n\n  /**\n   * @internal\n   * @private\n   */\n  static extractI18n(options: NgTools_InternalApi_NG2_ExtractI18n_Options): Promise<any> {\n    const hostContext: CompilerHostContext =\n        new CustomLoaderModuleResolutionHostAdapter(options.readResource, options.host);\n\n    // Create the i18n extractor.\n    const locale = options.locale || null;\n    const extractor = Extractor.create(\n        options.angularCompilerOptions, options.program, options.host, locale, hostContext);\n\n    return extractor.extract(options.i18nFormat !, options.outFile || null);\n  }\n}\n"]}