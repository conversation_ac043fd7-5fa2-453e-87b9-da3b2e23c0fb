{"_args": [["better-assert@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "better-assert@1.0.2", "_id": "better-assert@1.0.2", "_inBundle": false, "_integrity": "sha1-QIZrnhueC1W0gYlDEeaPr/rrxSI=", "_location": "/better-assert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "better-assert@1.0.2", "name": "better-assert", "escapedName": "better-assert", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/parsejson", "/parseqs", "/parseuri"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/better-assert/-/better-assert-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/better-assert/issues"}, "contributors": [{"name": "TonyH<PERSON>", "email": "<EMAIL>"}, {"name": "ForbesLindesay"}], "dependencies": {"callsite": "1.0.0"}, "description": "Better assertions for node, reporting the expr, filename, lineno etc", "engines": {"node": "*"}, "homepage": "https://github.com/visionmedia/better-assert#readme", "keywords": ["assert", "stack", "trace", "debug"], "main": "index", "name": "better-assert", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/better-assert.git"}, "version": "1.0.2"}