{"version": 3, "file": "codegen.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/codegen.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH;;;GAGG;AACH,4CAA8C;AAC9C,sCAAyD;AAEzD,yBAAgC;AAGhC,iDAA+F;AAC/F,yEAAmE;AAEnE,IAAM,oBAAoB,GAAG,SAAS,CAAC;AAEvC,IAAM,QAAQ,GAAG,8MAOhB,CAAC;AAEF;IACE,uBACY,OAA+B,EAAU,OAAmB,EAC7D,IAAqB,EAAU,QAA8B,EAC5D,cAA4B;QAF5B,YAAO,GAAP,OAAO,CAAwB;QAAU,YAAO,GAAP,OAAO,CAAY;QAC7D,SAAI,GAAJ,IAAI,CAAiB;QAAU,aAAQ,GAAR,QAAQ,CAAsB;QAC5D,mBAAc,GAAd,cAAc,CAAc;IAAG,CAAC;IAE5C,+BAAO,GAAP;QAAA,iBAKC;QAJC,MAAM,CAAC,IAAI,CAAC,QAAQ;aACf,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,GAAG,CAClD,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC,QAAQ,CAAC,EAArD,CAAqD,CAAC,CAAC;aAChE,IAAI,CAAC,UAAA,eAAe,IAAI,OAAA,KAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAA1B,CAA0B,CAAC,CAAC;IAC3D,CAAC;IAED,mCAAW,GAAX;QAAA,iBAIC;QAHC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,GAAG,CAC/E,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC,QAAQ,CAAC,EAArD,CAAqD,CAAC,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEO,4BAAI,GAAZ,UAAa,eAA2C;QAAxD,iBASC;QARC,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QACrE,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAA,eAAe;YACzC,IAAM,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAC1E,IAAM,QAAQ,GAAG,KAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACnF,IAAM,MAAM,GAAG,eAAe,CAAC,MAAM,IAAI,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC1F,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,cAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,oBAAM,GAAb,UACI,OAA+B,EAAE,UAAyB,EAAE,OAAmB,EAC/E,cAA+B,EAAE,mBAAyC,EAC1E,cAA6B;QAC/B,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACpB,IAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACzE,IAAM,OAAO,GAAG,mBAAmB,IAAI,IAAI,2CAA2B,CAAC,cAAc,CAAC,CAAC;YACvF,cAAc,GAAG,cAAc,GAAG,IAAI,kDAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;gBACrD,IAAI,4BAAY,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAChF,CAAC;QACD,IAAI,YAAY,GAAW,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACvB,MAAM,IAAI,KAAK,CACX,2BAAyB,UAAU,CAAC,QAAQ,wDAAqD,CAAC,CAAC;YACzG,CAAC;YACD,YAAY,GAAG,iBAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,kBAAkB,GAAG,iCAA0B,CAAC,OAAO,CAAC;QAC5D,EAAE,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBACtC,KAAK,OAAO;oBACV,kBAAkB,GAAG,iCAA0B,CAAC,KAAK,CAAC;oBACtD,KAAK,CAAC;gBACR,KAAK,SAAS;oBACZ,kBAAkB,GAAG,iCAA0B,CAAC,OAAO,CAAC;oBACxD,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,kBAAkB,GAAG,iCAA0B,CAAC,MAAM,CAAC;oBACvD,KAAK,CAAC;gBACR;oBACE,MAAM,IAAI,KAAK,CACX,4CAA0C,UAAU,CAAC,kBAAkB,4CAAyC,CAAC,CAAC;YAC1H,CAAC;QACH,CAAC;QACM,IAAA;;;;;;mBAAqB,CAMzB;QACH,MAAM,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;IAC1F,CAAC;IACH,oBAAC;AAAD,CAAC,AA1ED,IA0EC;AA1EY,sCAAa", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Transform template html and css into executable code.\n * Intended to be used in a build step.\n */\nimport * as compiler from '@angular/compiler';\nimport {MissingTranslationStrategy} from '@angular/core';\nimport {AngularCompilerOptions, NgcCliOptions} from '@angular/tsc-wrapped';\nimport {readFileSync} from 'fs';\nimport * as ts from 'typescript';\n\nimport {CompilerHost, CompilerHostContext, ModuleResolutionHostAdapter} from './compiler_host';\nimport {PathMappedCompilerHost} from './path_mapped_compiler_host';\n\nconst GENERATED_META_FILES = /\\.json$/;\n\nconst PREAMBLE = `/**\n * @fileoverview This file is generated by the Angular template compiler.\n * Do not edit.\n * @suppress {suspiciousCode,uselessCode,missingProperties,missingOverride}\n */\n /* tslint:disable */\n\n`;\n\nexport class CodeGenerator {\n  constructor(\n      private options: AngularCompilerOptions, private program: ts.Program,\n      public host: ts.CompilerHost, private compiler: compiler.AotCompiler,\n      private ngCompilerHost: CompilerHost) {}\n\n  codegen(): Promise<string[]> {\n    return this.compiler\n        .analyzeModulesAsync(this.program.getSourceFiles().map(\n            sf => this.ngCompilerHost.getCanonicalFileName(sf.fileName)))\n        .then(analyzedModules => this.emit(analyzedModules));\n  }\n\n  codegenSync(): string[] {\n    const analyzed = this.compiler.analyzeModulesSync(this.program.getSourceFiles().map(\n        sf => this.ngCompilerHost.getCanonicalFileName(sf.fileName)));\n    return this.emit(analyzed);\n  }\n\n  private emit(analyzedModules: compiler.NgAnalyzedModules) {\n    const generatedModules = this.compiler.emitAllImpls(analyzedModules);\n    return generatedModules.map(generatedModule => {\n      const sourceFile = this.program.getSourceFile(generatedModule.srcFileUrl);\n      const emitPath = this.ngCompilerHost.calculateEmitPath(generatedModule.genFileUrl);\n      const source = generatedModule.source || compiler.toTypeScript(generatedModule, PREAMBLE);\n      this.host.writeFile(emitPath, source, false, () => {}, [sourceFile]);\n      return emitPath;\n    });\n  }\n\n  static create(\n      options: AngularCompilerOptions, cliOptions: NgcCliOptions, program: ts.Program,\n      tsCompilerHost: ts.CompilerHost, compilerHostContext?: CompilerHostContext,\n      ngCompilerHost?: CompilerHost): CodeGenerator {\n    if (!ngCompilerHost) {\n      const usePathMapping = !!options.rootDirs && options.rootDirs.length > 0;\n      const context = compilerHostContext || new ModuleResolutionHostAdapter(tsCompilerHost);\n      ngCompilerHost = usePathMapping ? new PathMappedCompilerHost(program, options, context) :\n                                        new CompilerHost(program, options, context);\n    }\n    let transContent: string = '';\n    if (cliOptions.i18nFile) {\n      if (!cliOptions.locale) {\n        throw new Error(\n            `The translation file (${cliOptions.i18nFile}) locale must be provided. Use the --locale option.`);\n      }\n      transContent = readFileSync(cliOptions.i18nFile, 'utf8');\n    }\n    let missingTranslation = MissingTranslationStrategy.Warning;\n    if (cliOptions.missingTranslation) {\n      switch (cliOptions.missingTranslation) {\n        case 'error':\n          missingTranslation = MissingTranslationStrategy.Error;\n          break;\n        case 'warning':\n          missingTranslation = MissingTranslationStrategy.Warning;\n          break;\n        case 'ignore':\n          missingTranslation = MissingTranslationStrategy.Ignore;\n          break;\n        default:\n          throw new Error(\n              `Unknown option for missingTranslation (${cliOptions.missingTranslation}). Use either error, warning or ignore.`);\n      }\n    }\n    const {compiler: aotCompiler} = compiler.createAotCompiler(ngCompilerHost, {\n      translations: transContent,\n      i18nFormat: cliOptions.i18nFormat,\n      locale: cliOptions.locale, missingTranslation,\n      enableLegacyTemplate: options.enableLegacyTemplate !== false,\n      enableSummariesForJit: options.enableSummariesForJit !== false,\n    });\n    return new CodeGenerator(options, program, tsCompilerHost, aotCompiler, ngCompilerHost);\n  }\n}\n"]}