{"title": "Web Storage - name/value pairs", "description": "Method of storing data locally like cookies, but for larger amounts of data (sessionStorage and localStorage, used to fall under HTML5).", "spec": "https://html.spec.whatwg.org/multipage/webstorage.html#storage", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Web_Storage_API", "title": "Mozilla Developer Network (MDN) documentation - Web Storage"}, {"url": "http://code.google.com/p/sessionstorage/", "title": "Support library"}, {"url": "http://html5demos.com/storage", "title": "Simple demo"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/features.js#native-localstorage;native-sessionstorage", "title": "has.js test"}, {"url": "https://www.webplatform.org/docs/apis/web-storage/Storage/localStorage", "title": "WebPlatform Docs"}], "bugs": [{"description": "IE 8 and 9 store data based only on hostname, ignoring the scheme (http vs https) and port number as required by the specification."}, {"description": "In iOS 5 & 6 localStorage data is stored in a location that may occasionally be cleared out by the OS."}, {"description": "In private browsing mode, Safari, iOS Safari and the Android browser (not include Chrome for Android) do not support setting sessionStorage or localStorage."}, {"description": "In IE attempting to access localStorage on HTML files served from the file system results in the localStorage object being `undefined`"}, {"description": "Internet Explorer does not support storing most of the ASCII characters with codes under x20."}, {"description": "The \"storage\" event is completely wrong in IE:\r\nIE10 : The storage event is fired even on the originating document where it occurred. Causes problems with multiple windows websites, and huge problems with iframes.\r\nIE11 : The storage event's oldValue and newValue are identical (newValue is supposed to contain the storage's updated value).\r\n\r\nPartial workaround: regularly probe the storage's value and compare with the last known value on all pages where you want to listen to this event, and track the last submitted value to determine if the modification was triggered locally. "}, {"description": "IE10 in Windows 8 has an issue where localStorage can fail with the error message \"SCRIPT5: Access is denied\" if \"integrity\" settings are not set correctly. [see details](http://stackoverflow.com/a/20848924)"}, {"description": "Storing large amounts of data in Safari (on OSX & iOS) can result in freezing the browser [see bug](https://bugs.webkit.org/show_bug.cgi?id=149585)"}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "a", "3": "a", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "n", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {}, "usage_perc_y": 94.92, "usage_perc_a": 0.02, "ucprefix": false, "parent": "", "keywords": "webstorage,local storage", "ie_id": "webstorage", "chrome_id": "5345825534246912", "firefox_id": "", "webkit_id": "", "shown": true}