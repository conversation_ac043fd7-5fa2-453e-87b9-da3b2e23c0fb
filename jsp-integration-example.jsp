<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Silos Integration Example</title>
</head>
<body>
    <h1>Silos Integration Example</h1>
    
    <!-- Your JSP content here -->
    <p>This page demonstrates the Silos integration logic converted from Angular to JavaScript.</p>
    
    <!-- Include the modular Silos integration script -->
    <script src="silos-integration-modular.js"></script>
    
    <script>
        // Mark as configured to prevent auto-initialization
        window.silosIntegrationConfigured = true;
        
        // Configure the Silos integration with JSP-specific data providers
        SilosIntegration.configure({
            // Custom user data provider that gets data from JSP session/request
            userDataProvider: function() {
                return {
                    username: '<%= session.getAttribute("username") != null ? session.getAttribute("username") : "test_user" %>',
                    profile: '<%= session.getAttribute("profile") != null ? session.getAttribute("profile") : "BIOSUG" %>',
                    branch: '<%= session.getAttribute("branch") != null ? session.getAttribute("branch") : "test_branch" %>'
                };
            },
            
            // Custom error handler
            errorHandler: function(message, title) {
                console.error(title + ':', message);
                // You can customize this based on your error handling needs
                // For example, show a custom modal, send to server logging, etc.
                alert(title + ': ' + message);
            },
            
            // You can also override other configuration options if needed
            targetProfile: 'BIOSUG',
            applicationCode: 'ZA0'
        });
        
        // Initialize the integration
        SilosIntegration.init();
        
        // Example functions for manual testing
        function manualCheck() {
            SilosIntegration.checkAndRedirect();
        }
        
        function showCurrentUserData() {
            var userData = SilosIntegration.getUserData();
            alert('Current user data: ' + JSON.stringify(userData, null, 2));
        }
        
        function manualRedirect() {
            SilosIntegration.redirectToSilos();
        }
    </script>

    <!-- Testing buttons -->
    <div style="margin-top: 20px;">
        <h3>Testing Functions:</h3>
        <button onclick="manualCheck()">Manual Check and Redirect</button>
        <button onclick="showCurrentUserData()">Show Current User Data</button>
        <button onclick="manualRedirect()">Force Redirect to Silos</button>
    </div>

    <!-- Instructions -->
    <div style="margin-top: 30px; padding: 20px; background-color: #f0f0f0; border-radius: 5px;">
        <h3>Integration Instructions:</h3>
        <ol>
            <li><strong>Include the script:</strong> Add the <code>silos-integration-modular.js</code> file to your JSP page</li>
            <li><strong>Configure user data:</strong> Customize the <code>userDataProvider</code> function to retrieve user data from your JSP session, request attributes, or database</li>
            <li><strong>Configure error handling:</strong> Customize the <code>errorHandler</code> function to match your application's error handling strategy</li>
            <li><strong>Initialize:</strong> Call <code>SilosIntegration.init()</code> to start the integration</li>
        </ol>
        
        <h4>Key Changes from Angular:</h4>
        <ul>
            <li>Replaced Angular HTTP client with native <code>fetch()</code> API</li>
            <li>Removed TypeScript types and converted to vanilla JavaScript</li>
            <li>Replaced Angular services with configurable functions</li>
            <li>Added JSP-compatible user data retrieval using session attributes</li>
            <li>Made the code modular and reusable across different JSP pages</li>
        </ul>
        
        <h4>Customization Points:</h4>
        <ul>
            <li><strong>User Data:</strong> Modify <code>userDataProvider</code> to get username, profile, and branch from your JSP environment</li>
            <li><strong>Error Handling:</strong> Customize <code>errorHandler</code> for your preferred error display method</li>
            <li><strong>URLs:</strong> Update API endpoints if they differ in your environment</li>
            <li><strong>Profile Check:</strong> Change <code>targetProfile</code> if you need to check for different profiles</li>
        </ul>
    </div>

</body>
</html>