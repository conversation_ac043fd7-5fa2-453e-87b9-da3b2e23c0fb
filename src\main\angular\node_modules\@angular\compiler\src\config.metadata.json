[{"__symbolic": "module", "version": 3, "metadata": {"CompilerConfig": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 24, "character": 30}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"CompilerConfig": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 24, "character": 30}]}]}}}}]