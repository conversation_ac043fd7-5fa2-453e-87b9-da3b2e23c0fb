[{"__symbolic": "module", "version": 3, "metadata": {"TestComponentRenderer": {"__symbolic": "class", "members": {"insertRootElement": [{"__symbolic": "method"}]}}, "ComponentFixtureAutoDetect": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ComponentFixtureAutoDetect"]}, "ComponentFixtureNoNgZone": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ComponentFixtureNoNgZone"]}, "TestBed": {"__symbolic": "class", "members": {"initTestEnvironment": [{"__symbolic": "method"}], "resetTestEnvironment": [{"__symbolic": "method"}], "resetTestingModule": [{"__symbolic": "method"}], "configureCompiler": [{"__symbolic": "method"}], "configureTestingModule": [{"__symbolic": "method"}], "compileComponents": [{"__symbolic": "method"}], "_initIfNeeded": [{"__symbolic": "method"}], "_createCompilerAndModule": [{"__symbolic": "method"}], "_assertNotInstantiated": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "execute": [{"__symbolic": "method"}], "overrideModule": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "overrideProvider": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "createComponent": [{"__symbolic": "method"}]}, "statics": {"compileComponents": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "compileComponents"}}}, "get": {"__symbolic": "function", "parameters": ["token", "notFoundValue"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "get"}, "arguments": [{"__symbolic": "reference", "name": "token"}, {"__symbolic": "reference", "name": "notFoundValue"}]}, "defaults": [null, {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, "member": "THROW_IF_NOT_FOUND"}]}, "createComponent": {"__symbolic": "function", "parameters": ["component"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "createComponent"}, "arguments": [{"__symbolic": "reference", "name": "component"}]}}}}, "getTestBed": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "binop", "operator": "=", "left": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 459, "character": 4, "context": {"name": "_testBed"}}, "right": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 459, "character": 4, "context": {"name": "_testBed"}}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "TestBed"}}}}}, "inject": {"__symbolic": "function"}, "InjectSetupWrapper": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 515, "character": 34}]}], "_addModule": [{"__symbolic": "method"}], "inject": [{"__symbolic": "method"}]}}, "withModule": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"TestComponentRenderer": {"__symbolic": "class", "members": {"insertRootElement": [{"__symbolic": "method"}]}}, "ComponentFixtureAutoDetect": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ComponentFixtureAutoDetect"]}, "ComponentFixtureNoNgZone": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ComponentFixtureNoNgZone"]}, "TestBed": {"__symbolic": "class", "members": {"initTestEnvironment": [{"__symbolic": "method"}], "resetTestEnvironment": [{"__symbolic": "method"}], "resetTestingModule": [{"__symbolic": "method"}], "configureCompiler": [{"__symbolic": "method"}], "configureTestingModule": [{"__symbolic": "method"}], "compileComponents": [{"__symbolic": "method"}], "_initIfNeeded": [{"__symbolic": "method"}], "_createCompilerAndModule": [{"__symbolic": "method"}], "_assertNotInstantiated": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "execute": [{"__symbolic": "method"}], "overrideModule": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "overrideProvider": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "createComponent": [{"__symbolic": "method"}]}, "statics": {"compileComponents": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "compileComponents"}}}, "get": {"__symbolic": "function", "parameters": ["token", "notFoundValue"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "get"}, "arguments": [{"__symbolic": "reference", "name": "token"}, {"__symbolic": "reference", "name": "notFoundValue"}]}, "defaults": [null, {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, "member": "THROW_IF_NOT_FOUND"}]}, "createComponent": {"__symbolic": "function", "parameters": ["component"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "getTestBed"}}, "member": "createComponent"}, "arguments": [{"__symbolic": "reference", "name": "component"}]}}}}, "getTestBed": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "binop", "operator": "=", "left": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 459, "character": 4, "context": {"name": "_testBed"}}, "right": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "error", "message": "Reference to a local symbol", "line": 459, "character": 4, "context": {"name": "_testBed"}}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "TestBed"}}}}}, "inject": {"__symbolic": "function"}, "InjectSetupWrapper": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 515, "character": 34}]}], "_addModule": [{"__symbolic": "method"}], "inject": [{"__symbolic": "method"}]}}, "withModule": {"__symbolic": "function"}}}]