{"title": "CSS unicode-bidi property", "description": "The unicode-bidi CSS property together with the direction property relates to the handling of bidirectional text in a document.", "spec": "https://drafts.csswg.org/css-writing-modes-3/#unicode-bidi", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/unicode-bidi", "title": "Mozilla Developer Network (MDN) documentation - CSS unicode-bidi"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a x #2", "11": "a x #2", "12": "a x #2", "13": "a x #2", "14": "a x #2", "15": "a x #2", "16": "a x #2", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a x #3", "18": "a x #3", "19": "a x #3", "20": "a x #3", "21": "a x #3", "22": "a x #3", "23": "a x #3", "24": "a x #3", "25": "a x #3", "26": "a x #3", "27": "a x #3", "28": "a x #3", "29": "a x #3", "30": "a x #3", "31": "a x #3", "32": "a x #3", "33": "a x #3", "34": "a x #3", "35": "a x #3", "36": "a x #3", "37": "a x #3", "38": "a x #3", "39": "a x #3", "40": "a x #3", "41": "a x #3", "42": "a x #3", "43": "a x #3", "44": "a x #3", "45": "a x #3", "46": "a x #3", "47": "a x #3", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "a #1", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a x #3", "9.1": "a x #3", "10": "a x #3", "10.1": "a x #3", "11": "a x #3", "TP": "a x #3"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #1", "4.2-4.3": "a #1", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a x #3", "9.3": "a x #3", "10.0-10.2": "a x #3", "10.3": "a x #3", "11": "a x #3"}, "op_mini": {"all": "u"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "u", "4.1": "u", "4.2-4.3": "u", "4.4": "u", "4.4.3-4.4.4": "u", "56": "y"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "u", "37": "u"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.4": "u"}, "samsung": {"4": "u", "5": "y"}, "and_qq": {"1.2": "u"}, "baidu": {"7.12": "u"}}, "notes": "", "notes_by_num": {"1": "Only normal, embed and bidi-override values support", "2": "isolate keyword could be used together with bidi-override instead of isolate-override value support", "3": "Only normal, embed, bidi-override and isolate values support"}, "usage_perc_y": 59.53, "usage_perc_a": 20.51, "ucprefix": false, "parent": "", "keywords": "css,writing,direction,i18n,vertical,ltr,rtl", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": false}