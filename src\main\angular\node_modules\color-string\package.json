{"_args": [["color-string@0.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "color-string@0.3.0", "_id": "color-string@0.3.0", "_inBundle": false, "_integrity": "sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE=", "_location": "/color-string", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color-string@0.3.0", "name": "color-string", "escapedName": "color-string", "rawSpec": "0.3.0", "saveSpec": null, "fetchSpec": "0.3.0"}, "_requiredBy": ["/color"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/color-string/-/color-string-0.3.0.tgz", "_spec": "0.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/harthur/color-string/issues"}, "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"color-name": "^1.0.0"}, "description": "Parser and generator for CSS color strings", "devDependencies": {}, "homepage": "https://github.com/harthur/color-string#readme", "keywords": ["color", "colour", "rgb", "css"], "license": "MIT", "main": "./color-string", "name": "color-string", "repository": {"type": "git", "url": "git+ssh://**************/harthur/color-string.git"}, "scripts": {"test": "node test/basic.js"}, "version": "0.3.0"}