[{"__symbolic": "module", "version": 3, "metadata": {"TranslateModuleConfig": {"__symbolic": "interface"}, "TranslateModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"declarations": [{"__symbolic": "reference", "module": "./src/translate.pipe", "name": "TranslatePipe"}, {"__symbolic": "reference", "module": "./src/translate.directive", "name": "TranslateDirective"}], "exports": [{"__symbolic": "reference", "module": "./src/translate.pipe", "name": "TranslatePipe"}, {"__symbolic": "reference", "module": "./src/translate.directive", "name": "TranslateDirective"}]}]}], "statics": {"forRoot": {"__symbolic": "function", "parameters": ["config"], "value": {"ngModule": {"__symbolic": "reference", "name": "TranslateModule"}, "providers": [{"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "loader"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "TranslateFakeLoader"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "parser"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "TranslateDefault<PERSON><PERSON><PERSON>"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "missingTranslation<PERSON><PERSON><PERSON>"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "MissingTranslationHandler"}, "useClass": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "FakeMissingTranslationHandler"}}}, {"__symbolic": "reference", "module": "./src/translate.store", "name": "TranslateStore"}, {"provide": {"__symbolic": "reference", "module": "./src/translate.service", "name": "USE_STORE"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "isolate"}}, {"__symbolic": "reference", "module": "./src/translate.service", "name": "TranslateService"}]}, "defaults": [{}]}, "forChild": {"__symbolic": "function", "parameters": ["config"], "value": {"ngModule": {"__symbolic": "reference", "name": "TranslateModule"}, "providers": [{"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "loader"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "TranslateFakeLoader"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "parser"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "TranslateDefault<PERSON><PERSON><PERSON>"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "missingTranslation<PERSON><PERSON><PERSON>"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "MissingTranslationHandler"}, "useClass": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "FakeMissingTranslationHandler"}}}, {"provide": {"__symbolic": "reference", "module": "./src/translate.service", "name": "USE_STORE"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "isolate"}}, {"__symbolic": "reference", "module": "./src/translate.service", "name": "TranslateService"}]}, "defaults": [{}]}}}}, "exports": [{"from": "./src/translate.loader"}, {"from": "./src/translate.service"}, {"from": "./src/missing-translation-handler"}, {"from": "./src/translate.parser"}, {"from": "./src/translate.directive"}, {"from": "./src/translate.pipe"}]}, {"__symbolic": "module", "version": 1, "metadata": {"TranslateModuleConfig": {"__symbolic": "interface"}, "TranslateModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"declarations": [{"__symbolic": "reference", "module": "./src/translate.pipe", "name": "TranslatePipe"}, {"__symbolic": "reference", "module": "./src/translate.directive", "name": "TranslateDirective"}], "exports": [{"__symbolic": "reference", "module": "./src/translate.pipe", "name": "TranslatePipe"}, {"__symbolic": "reference", "module": "./src/translate.directive", "name": "TranslateDirective"}]}]}], "statics": {"forRoot": {"__symbolic": "function", "parameters": ["config"], "value": {"ngModule": {"__symbolic": "reference", "name": "TranslateModule"}, "providers": [{"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "loader"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "TranslateFakeLoader"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "parser"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "TranslateDefault<PERSON><PERSON><PERSON>"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "missingTranslation<PERSON><PERSON><PERSON>"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "MissingTranslationHandler"}, "useClass": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "FakeMissingTranslationHandler"}}}, {"__symbolic": "reference", "module": "./src/translate.store", "name": "TranslateStore"}, {"provide": {"__symbolic": "reference", "module": "./src/translate.service", "name": "USE_STORE"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "isolate"}}, {"__symbolic": "reference", "module": "./src/translate.service", "name": "TranslateService"}]}, "defaults": [{}]}, "forChild": {"__symbolic": "function", "parameters": ["config"], "value": {"ngModule": {"__symbolic": "reference", "name": "TranslateModule"}, "providers": [{"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "loader"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "Translate<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.loader", "name": "TranslateFakeLoader"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "parser"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./src/translate.parser", "name": "TranslateDefault<PERSON><PERSON><PERSON>"}}}, {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "missingTranslation<PERSON><PERSON><PERSON>"}, "right": {"provide": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "MissingTranslationHandler"}, "useClass": {"__symbolic": "reference", "module": "./src/missing-translation-handler", "name": "FakeMissingTranslationHandler"}}}, {"provide": {"__symbolic": "reference", "module": "./src/translate.service", "name": "USE_STORE"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "isolate"}}, {"__symbolic": "reference", "module": "./src/translate.service", "name": "TranslateService"}]}, "defaults": [{}]}}}}, "exports": [{"from": "./src/translate.loader"}, {"from": "./src/translate.service"}, {"from": "./src/missing-translation-handler"}, {"from": "./src/translate.parser"}, {"from": "./src/translate.directive"}, {"from": "./src/translate.pipe"}]}]