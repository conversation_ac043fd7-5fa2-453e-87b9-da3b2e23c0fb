{"_args": [["typescript@2.3.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "typescript@2.3.4", "_id": "typescript@2.3.4", "_inBundle": false, "_integrity": "sha1-PTgyGCgjHkNPKHUUlZw3qCtin0I=", "_location": "/@angular/cli/typescript", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "typescript@2.3.4", "name": "typescript", "escapedName": "typescript", "rawSpec": "2.3.4", "saveSpec": null, "fetchSpec": "2.3.4"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/typescript/-/typescript-2.3.4.tgz", "_spec": "2.3.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "Microsoft Corp."}, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "browser": {"buffer": false, "fs": false, "os": false, "path": false}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "description": "TypeScript is a language for application scale JavaScript development", "devDependencies": {"@types/browserify": "latest", "@types/chai": "latest", "@types/convert-source-map": "latest", "@types/del": "latest", "@types/glob": "latest", "@types/gulp": "latest", "@types/gulp-concat": "latest", "@types/gulp-help": "latest", "@types/gulp-newer": "latest", "@types/gulp-sourcemaps": "latest", "@types/gulp-typescript": "latest", "@types/merge2": "latest", "@types/minimatch": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/node": "latest", "@types/q": "latest", "@types/run-sequence": "latest", "@types/through2": "latest", "browserify": "latest", "chai": "latest", "convert-source-map": "latest", "del": "latest", "gulp": "latest", "gulp-clone": "latest", "gulp-concat": "latest", "gulp-help": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-sourcemaps": "latest", "gulp-typescript": "3.1.5", "into-stream": "latest", "istanbul": "latest", "jake": "latest", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "q": "latest", "run-sequence": "latest", "sorcery": "latest", "through2": "latest", "travis-fold": "latest", "ts-node": "latest", "tslint": "next", "typescript": "^2.3.3"}, "engines": {"node": ">=4.2.0"}, "homepage": "http://typescriptlang.org/", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "license": "Apache-2.0", "main": "./lib/typescript.js", "name": "typescript", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/TypeScript.git"}, "scripts": {"build": "npm run build:compiler && npm run build:tests", "build:compiler": "jake local", "build:tests": "jake tests", "clean": "jake clean", "gulp": "gulp", "jake": "jake", "lint": "jake lint", "pretest": "jake tests", "setup-hooks": "node scripts/link-hooks.js", "start": "node lib/tsc", "test": "jake runtests-parallel"}, "typings": "./lib/typescript.d.ts", "version": "2.3.4"}