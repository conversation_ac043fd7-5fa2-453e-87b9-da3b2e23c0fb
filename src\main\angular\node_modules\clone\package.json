{"_args": [["clone@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "clone@1.0.2", "_id": "clone@1.0.2", "_inBundle": false, "_integrity": "sha1-Jgt6meux7f4kdTgXX3gyQ8sZ0Uk=", "_location": "/clone", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clone@1.0.2", "name": "clone", "escapedName": "clone", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/color"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/clone/-/clone-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "dependencies": {}, "description": "deep cloning of objects and arrays", "devDependencies": {"nodeunit": "~0.9.0"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/pvorb/node-clone#readme", "license": "MIT", "main": "clone.js", "name": "clone", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "scripts": {"test": "nodeunit test.js"}, "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.2"}