[{"__symbolic": "module", "version": 3, "metadata": {"AstPath": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 26, "character": 28, "context": {"typeName": "T"}}]}, {"__symbolic": "reference", "name": "number"}]}], "parentOf": [{"__symbolic": "method"}], "childOf": [{"__symbolic": "method"}], "first": [{"__symbolic": "method"}], "push": [{"__symbolic": "method"}], "pop": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"AstPath": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 26, "character": 28, "context": {"typeName": "T"}}]}, {"__symbolic": "reference", "name": "number"}]}], "parentOf": [{"__symbolic": "method"}], "childOf": [{"__symbolic": "method"}], "first": [{"__symbolic": "method"}], "push": [{"__symbolic": "method"}], "pop": [{"__symbolic": "method"}]}}}}]