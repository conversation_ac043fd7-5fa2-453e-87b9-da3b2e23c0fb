[{"__symbolic": "module", "version": 3, "metadata": {"NgModuleResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isNgModule": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"NgModuleResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isNgModule": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}]