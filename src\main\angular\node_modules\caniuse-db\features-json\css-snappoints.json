{"title": "CSS Scroll snap points", "description": "CSS technique that allows customizable scrolling experiences like pagination of carousels by setting defined snap points.", "spec": "http://www.w3.org/TR/css-snappoints-1/", "status": "cr", "links": [{"url": "http://generatedcontent.org/post/66817675443/setting-native-like-scrolling-offsets-in-css-with", "title": "Blog post"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Scroll_Snap_Points", "title": "Mozilla Developer Network (MDN) documentation - CSS Scroll snap points"}, {"url": "https://github.com/ckrack/scrollsnap-polyfill", "title": "Polyfill"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a x #1 #5 #6", "11": "a x #2 #5 #6"}, "edge": {"12": "a x #2 #5 #6", "13": "a x #2 #5 #6", "14": "a x #2 #5 #6", "15": "a x #2 #5 #6", "16": "a x #2 #5 #6"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "a #5", "40": "a #5", "41": "a #5", "42": "a #5", "43": "a #5", "44": "a #5", "45": "a #5", "46": "a #5", "47": "a #5", "48": "a #5", "49": "a #5", "50": "a #5", "51": "a #5", "52": "a #5", "53": "a #5", "54": "a #5", "55": "a #5", "56": "a #5", "57": "a #5"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "a x #4 #5", "9.1": "a x #4 #5", "10": "a x #4 #5", "10.1": "a x #4 #5", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "a x #4 #5", "9.3": "a x #4 #5", "10.0-10.2": "a x #4 #5", "10.3": "a x #4 #5", "11": "a x #4 #5"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "a #5"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "Works in the iOS WKWebView, but not UIWebView.", "notes_by_num": {"1": "Partial support in IE10 refers to support limited to touch screens.", "2": "Partial support in IE11 [documented here](https://dl.dropboxusercontent.com/u/444684/openwebref/CSS/scroll-snap-points/support.html)", "4": "Partial support in Safari refers to not supporting the `none` keyword in `scroll-snap-points-x`, `scroll-snap-points-y` and `scroll-snap-coordinate`, and length keywords (`top`, `right`, etc.) in `scroll-snap-destination` and `scroll-snap-coordinate`.", "5": "Supports properties from an [older version](https://www.w3.org/TR/2015/WD-css-snappoints-1-20150326/) of the spec.", "6": "Partial support in IE & Edge refers to not supporting `scroll-snap-coordinate` and `scroll-snap-destination`."}, "usage_perc_y": 0, "usage_perc_a": 22.38, "ucprefix": false, "parent": "", "keywords": "scroll-snap-points-x,scroll-snap-points-y,scroll-snap-type,scroll-snap-destination,scroll-snap-coordinate,scroll-snap-margin,scroll-snap-align", "ie_id": "cssscrollsnappoints", "chrome_id": "5721832506261504", "firefox_id": "", "webkit_id": "specification-css-scroll-snap-points-module-level-1", "shown": true}