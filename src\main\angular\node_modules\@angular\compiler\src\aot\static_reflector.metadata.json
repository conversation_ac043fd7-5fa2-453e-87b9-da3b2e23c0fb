[{"__symbolic": "module", "version": 3, "metadata": {"StaticReflector": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "reference", "module": "./static_symbol_resolver", "name": "StaticSymbolResolver"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 49, "character": 28}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 50, "character": 30}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 51, "character": 30}]}], "componentModuleUrl": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}], "findDeclaration": [{"__symbolic": "method"}], "findSymbolDeclaration": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "parameters": [{"__symbolic": "method"}], "_methodNames": [{"__symbolic": "method"}], "findParentType": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "_registerDecoratorOrConstructor": [{"__symbolic": "method"}], "_registerFunction": [{"__symbolic": "method"}], "initializeConversionMap": [{"__symbolic": "method"}], "getStaticSymbol": [{"__symbolic": "method"}], "reportError": [{"__symbolic": "method"}], "trySimplify": [{"__symbolic": "method"}], "simplify": [{"__symbolic": "method"}], "getTypeMetadata": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"StaticReflector": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "reference", "module": "./static_symbol_resolver", "name": "StaticSymbolResolver"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 49, "character": 28}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 50, "character": 30}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 51, "character": 30}]}], "componentModuleUrl": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}], "findDeclaration": [{"__symbolic": "method"}], "findSymbolDeclaration": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "parameters": [{"__symbolic": "method"}], "_methodNames": [{"__symbolic": "method"}], "findParentType": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "_registerDecoratorOrConstructor": [{"__symbolic": "method"}], "_registerFunction": [{"__symbolic": "method"}], "initializeConversionMap": [{"__symbolic": "method"}], "getStaticSymbol": [{"__symbolic": "method"}], "reportError": [{"__symbolic": "method"}], "trySimplify": [{"__symbolic": "method"}], "simplify": [{"__symbolic": "method"}], "getTypeMetadata": [{"__symbolic": "method"}]}}}}]