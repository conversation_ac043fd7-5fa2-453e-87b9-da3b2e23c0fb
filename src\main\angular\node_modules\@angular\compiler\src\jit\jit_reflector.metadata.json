[{"__symbolic": "module", "version": 3, "metadata": {"JitReflector": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "componentModuleUrl": [{"__symbolic": "method"}], "parameters": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"JitReflector": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "componentModuleUrl": [{"__symbolic": "method"}], "parameters": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}]}}}}]