{"version": 3, "file": "compiler_host.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/compiler_host.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAEH,yBAAiC;AACjC,6BAA+B;AAC/B,+BAAiC;AAGjC,yCAA8C;AAG9C,2BAAkC,CAAkB;IAClD,IAAM,IAAI,GAA6B;QACrC,mBAAmB,EAAE,cAAM,OAAA,EAAE,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAA5B,CAA4B;QACvD,UAAU,EAAE,cAAM,OAAA,EAAE,CAAC,GAAG,CAAC,OAAO,EAAd,CAAc;QAChC,oBAAoB,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,EAAD,CAAC;KACvC,CAAC;IACF,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AAPD,8CAOC;AAED;;;GAGG;AACH;IACE,wBAAsB,QAAyB;QAA/C,iBAAmD;QAA7B,aAAQ,GAAR,QAAQ,CAAiB;QAC/C,kBAAa,GACT,UAAC,QAAgB,EAAE,eAAgC,EAAE,OAAmC;YACpF,OAAA,KAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC;QAA/D,CAA+D,CAAC;QAExE,yBAAoB,GAAG,cAAM,OAAA,KAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAApC,CAAoC,CAAC;QAClE,0BAAqB,GAAG,UAAC,OAA2B;YAChD,OAAA,KAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC;QAA5C,CAA4C,CAAC;QACjD,0BAAqB,GAAG,cAAM,OAAA,KAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAArC,CAAqC,CAAC;QACpE,cAAS,GAAyB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC1D,wBAAmB,GAAG,cAAM,OAAA,KAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,EAAnC,CAAmC,CAAC;QAChE,mBAAc,GAAG,UAAC,IAAY;YAC1B,OAAC,KAAI,CAAC,QAAgB,CAAC,cAAc,GAAE,KAAI,CAAC,QAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAE,EAAE;QAArF,CAAqF,CAAC;QAC1F,yBAAoB,GAAG,UAAC,QAAgB,IAAK,OAAA,KAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAA5C,CAA4C,CAAC;QAC1F,8BAAyB,GAAG,cAAM,OAAA,KAAI,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAzC,CAAyC,CAAC;QAC5E,eAAU,GAAG,cAAM,OAAA,KAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAA1B,CAA0B,CAAC;QAC9C,eAAU,GAAG,UAAC,QAAgB,IAAK,OAAA,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAlC,CAAkC,CAAC;QACtE,aAAQ,GAAG,UAAC,QAAgB,IAAK,OAAA,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAhC,CAAgC,CAAC;QAClE,UAAK,GAAG,UAAC,CAAS,IAAK,OAAA,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAtB,CAAsB,CAAC;QAC9C,oBAAe,GAAG,UAAC,aAAqB,IAAK,OAAA,KAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,EAA5C,CAA4C,CAAC;IAnBxC,CAAC;IAoBrD,qBAAC;AAAD,CAAC,AArBD,IAqBC;AArBqB,wCAAc;AAuBpC,IAAM,aAAa,GAAG,iCAAiC,CAAC;AACxD,IAAM,GAAG,GAAG,UAAU,CAAC;AAEvB;IAAwC,sCAAc;IAGpD,4BACI,QAAyB,EAAU,SAAoB,EAAU,YAAqB;QAD1F,YAEE,kBAAM,QAAQ,CAAC,SAChB;QAFsC,eAAS,GAAT,SAAS,CAAW;QAAU,kBAAY,GAAZ,YAAY,CAAS;QAHlF,uBAAiB,GAAG,IAAI,6BAAiB,CAAC,EAAC,WAAW,EAAE,IAAI,EAAC,CAAC,CAAC;QAC/D,wBAAkB,GAAG,IAAI,6BAAiB,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CAAC;QAgCjE,eAAS,GACL,UAAC,QAAgB,EAAE,IAAY,EAAE,kBAA2B,EAC3D,OAAmC,EAAE,WAA6B;YACjE,IAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,EAAE,CAAC,CAAC,KAAI,CAAC,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC/B,yFAAyF;gBACzF,KAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACpF,CAAC;YACD,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACV,yFAAyF;gBACzF,cAAc;gBACd,MAAM,CAAC;YACT,CAAC;YAED,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC;YACT,CAAC;YAED,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,IAAI,KAAK,CACX,0EAA0E;oBAC1E,iCAAiC,CAAC,CAAC;YACzC,CAAC;YACD,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBAC1E,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAA;;IAzDL,CAAC;IAEO,0CAAa,GAArB,UAAsB,YAAoB,EAAE,UAAyB;QACnE,4FAA4F;QAC5F,WAAW;QACX,EAAE,CAAC,CAAS,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,IAAM,MAAI,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAErE,gFAAgF;YAChF,4EAA4E;YAC5E,iFAAiF;YACjF,qFAAqF;YACrF,IAAI,eAAe,GAAG,UAAU,CAAC;YACjC,OAAQ,eAAuB,CAAC,QAAQ,EAAE,CAAC;gBACzC,eAAe,GAAI,eAAuB,CAAC,QAAQ,CAAC;YACtD,CAAC;YAED,IAAM,QAAQ,GACV,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC7F,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC9E,IAAM,SAAS,GAAqB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,CAAC;YAC3E,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBACrB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC/C,kBAAa,CAAC,MAAI,EAAE,YAAY,EAAE,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;IAgCH,yBAAC;AAAD,CAAC,AAhED,CAAwC,cAAc,GAgErD;AAhEY,gDAAkB;AAkE/B;IAAwC,sCAAc;IAKpD,4BACI,QAAyB,EACzB,cAAiE;QAFrE,YAGE,kBAAM,QAAQ,CAAC,SAIhB;QAED,gBAAU,GAAG,UAAC,QAAgB;YAExB,MAAM,CAAC,gBAAS,CAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,wBAAwB;gBACvD,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAA;QAEL,cAAQ,GACJ,UAAC,QAAgB;YACf,MAAM,CAAC,gBAAS,CAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,wBAAwB;gBACvD,KAAI,CAAC,YAAY;gBACjB,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAA;QAEL,mBAAa,GACT,UAAC,QAAgB,EAAE,eAAgC,EAClD,OAAmC;YAClC,EAAE,CAAC,CAAC,gBAAS,CAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBACzD,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAI,CAAC,YAAY,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YACjF,CAAC;YACD,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;QACzE,CAAC,CAAA;QAEwC,eAAS,GAC9C,UAAC,QAAgB,EAAE,IAAY,EAAE,kBAA2B,EAC3D,OAAmC,EAAE,WAA6B;YACjE,KAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YAClF,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC;gBAC7D,gBAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBACxE,wEAAwE;gBACxE,IAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;gBAC7D,kBAAa,CAAC,YAAY,EAAE,KAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAA;QArCP,KAAI,CAAC,wBAAwB,GAAG,gBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/D,KAAI,CAAC,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC;QAC3C,KAAI,CAAC,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC;;IAC/C,CAAC;IAmCH,yBAAC;AAAD,CAAC,AA/CD,CAAwC,cAAc,GA+CrD;AA/CY,gDAAkB", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {writeFileSync} from 'fs';\nimport {normalize} from 'path';\nimport * as ts from 'typescript';\n\nimport NgOptions from './options';\nimport {MetadataCollector} from './collector';\nimport {ModuleMetadata} from './schema';\n\nexport function formatDiagnostics(d: ts.Diagnostic[]): string {\n  const host: ts.FormatDiagnosticsHost = {\n    getCurrentDirectory: () => ts.sys.getCurrentDirectory(),\n    getNewLine: () => ts.sys.newLine,\n    getCanonicalFileName: (f: string) => f\n  };\n  return ts.formatDiagnostics(d, host);\n}\n\n/**\n * Implementation of CompilerHost that forwards all methods to another instance.\n * Useful for partial implementations to override only methods they care about.\n */\nexport abstract class DelegatingHost implements ts.CompilerHost {\n  constructor(protected delegate: ts.CompilerHost) {}\n  getSourceFile =\n      (fileName: string, languageVersion: ts.ScriptTarget, onError?: (message: string) => void) =>\n          this.delegate.getSourceFile(fileName, languageVersion, onError);\n\n  getCancellationToken = () => this.delegate.getCancellationToken();\n  getDefaultLibFileName = (options: ts.CompilerOptions) =>\n      this.delegate.getDefaultLibFileName(options);\n  getDefaultLibLocation = () => this.delegate.getDefaultLibLocation();\n  writeFile: ts.WriteFileCallback = this.delegate.writeFile;\n  getCurrentDirectory = () => this.delegate.getCurrentDirectory();\n  getDirectories = (path: string): string[] =>\n      (this.delegate as any).getDirectories?(this.delegate as any).getDirectories(path): [];\n  getCanonicalFileName = (fileName: string) => this.delegate.getCanonicalFileName(fileName);\n  useCaseSensitiveFileNames = () => this.delegate.useCaseSensitiveFileNames();\n  getNewLine = () => this.delegate.getNewLine();\n  fileExists = (fileName: string) => this.delegate.fileExists(fileName);\n  readFile = (fileName: string) => this.delegate.readFile(fileName);\n  trace = (s: string) => this.delegate.trace(s);\n  directoryExists = (directoryName: string) => this.delegate.directoryExists(directoryName);\n}\n\nconst IGNORED_FILES = /\\.ngfactory\\.js$|\\.ngstyle\\.js$/;\nconst DTS = /\\.d\\.ts$/;\n\nexport class MetadataWriterHost extends DelegatingHost {\n  private metadataCollector = new MetadataCollector({quotedNames: true});\n  private metadataCollector1 = new MetadataCollector({version: 1});\n  constructor(\n      delegate: ts.CompilerHost, private ngOptions: NgOptions, private emitAllFiles: boolean) {\n    super(delegate);\n  }\n\n  private writeMetadata(emitFilePath: string, sourceFile: ts.SourceFile) {\n    // TODO: replace with DTS filePath when https://github.com/Microsoft/TypeScript/pull/8412 is\n    // released\n    if (/*DTS*/ /\\.js$/.test(emitFilePath)) {\n      const path = emitFilePath.replace(/*DTS*/ /\\.js$/, '.metadata.json');\n\n      // Beginning with 2.1, TypeScript transforms the source tree before emitting it.\n      // We need the original, unmodified, tree which might be several levels back\n      // depending on the number of transforms performed. All SourceFile's prior to 2.1\n      // will appear to be the original source since they didn't include an original field.\n      let collectableFile = sourceFile;\n      while ((collectableFile as any).original) {\n        collectableFile = (collectableFile as any).original;\n      }\n\n      const metadata =\n          this.metadataCollector.getMetadata(collectableFile, !!this.ngOptions.strictMetadataEmit);\n      const metadata1 = this.metadataCollector1.getMetadata(collectableFile, false);\n      const metadatas: ModuleMetadata[] = [metadata, metadata1].filter(e => !!e);\n      if (metadatas.length) {\n        const metadataText = JSON.stringify(metadatas);\n        writeFileSync(path, metadataText, {encoding: 'utf-8'});\n      }\n    }\n  }\n\n  writeFile: ts.WriteFileCallback =\n      (fileName: string, data: string, writeByteOrderMark: boolean,\n       onError?: (message: string) => void, sourceFiles?: ts.SourceFile[]) => {\n        const isDts = /\\.d\\.ts$/.test(fileName);\n        if (this.emitAllFiles || isDts) {\n          // Let the original file be written first; this takes care of creating parent directories\n          this.delegate.writeFile(fileName, data, writeByteOrderMark, onError, sourceFiles);\n        }\n        if (isDts) {\n          // TODO: remove this early return after https://github.com/Microsoft/TypeScript/pull/8412\n          // is released\n          return;\n        }\n\n        if (IGNORED_FILES.test(fileName)) {\n          return;\n        }\n\n        if (!sourceFiles) {\n          throw new Error(\n              'Metadata emit requires the sourceFiles are passed to WriteFileCallback. ' +\n              'Update to TypeScript ^1.9.0-dev');\n        }\n        if (sourceFiles.length > 1) {\n          throw new Error('Bundled emit with --out is not supported');\n        }\n        if (!this.ngOptions.skipMetadataEmit && !this.ngOptions.flatModuleOutFile) {\n          this.writeMetadata(fileName, sourceFiles[0]);\n        }\n      }\n}\n\nexport class SyntheticIndexHost extends DelegatingHost {\n  private normalSyntheticIndexName: string;\n  private indexContent: string;\n  private indexMetadata: string;\n\n  constructor(\n      delegate: ts.CompilerHost,\n      syntheticIndex: {name: string, content: string, metadata: string}) {\n    super(delegate);\n    this.normalSyntheticIndexName = normalize(syntheticIndex.name);\n    this.indexContent = syntheticIndex.content;\n    this.indexMetadata = syntheticIndex.metadata;\n  }\n\n  fileExists = (fileName: string):\n      boolean => {\n        return normalize(fileName) == this.normalSyntheticIndexName ||\n            this.delegate.fileExists(fileName);\n      }\n\n  readFile =\n      (fileName: string) => {\n        return normalize(fileName) == this.normalSyntheticIndexName ?\n            this.indexContent :\n            this.delegate.readFile(fileName);\n      }\n\n  getSourceFile =\n      (fileName: string, languageVersion: ts.ScriptTarget,\n       onError?: (message: string) => void) => {\n        if (normalize(fileName) == this.normalSyntheticIndexName) {\n          return ts.createSourceFile(fileName, this.indexContent, languageVersion, true);\n        }\n        return this.delegate.getSourceFile(fileName, languageVersion, onError);\n      }\n\n                                               writeFile: ts.WriteFileCallback =\n          (fileName: string, data: string, writeByteOrderMark: boolean,\n           onError?: (message: string) => void, sourceFiles?: ts.SourceFile[]) => {\n            this.delegate.writeFile(fileName, data, writeByteOrderMark, onError, sourceFiles);\n            if (fileName.match(DTS) && sourceFiles && sourceFiles.length == 1 &&\n                normalize(sourceFiles[0].fileName) == this.normalSyntheticIndexName) {\n              // If we are writing the synthetic index, write the metadata along side.\n              const metadataName = fileName.replace(DTS, '.metadata.json');\n              writeFileSync(metadataName, this.indexMetadata, 'utf8');\n            }\n          }\n}"]}