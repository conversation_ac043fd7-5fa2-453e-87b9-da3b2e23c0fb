{"title": "Spellcheck attribute", "description": "Attribute for `input`/`textarea` fields to enable/disable the browser's spellchecker.", "spec": "https://html.spec.whatwg.org/multipage/interaction.html#spelling-and-grammar-checking", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Controlling_spell_checking_in_HTML_formsControlling_spell_checking_in_HTML_forms", "title": "Mozilla Developer Network (MDN) documentation - Controlling spell checking"}], "bugs": [{"description": "Browsers can behave differently on when they should check spelling (e.g. immediately or only on focus)"}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "y", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "a"}, "android": {"2.1": "a", "2.2": "a", "2.3": "a", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "a", "10": "y"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "a", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "a"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "a"}}, "notes": "The partial support in mobile browsers results from their OS generally having built-in spell checking instead of using the wavy underline to indicate misspelled words. `spellcheck=\"false\"` does not seem to have any effect in these browsers.\r\n\r\nBrowsers have different behavior in how they deal with spellchecking in combination with the the `lang` attribute. Generally spelling is based on the browser's language, not the language of the document.", "notes_by_num": {}, "usage_perc_y": 39.86, "usage_perc_a": 57.61, "ucprefix": false, "parent": "", "keywords": "spelling", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}