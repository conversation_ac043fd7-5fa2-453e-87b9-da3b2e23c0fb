{"_args": [["argparse@1.0.9", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "argparse@1.0.9", "_id": "argparse@1.0.9", "_inBundle": false, "_integrity": "sha1-c9g7wmP4bpf4zE9rrhsOkKfSLIY=", "_location": "/argparse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "argparse@1.0.9", "name": "<PERSON><PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON><PERSON>", "rawSpec": "1.0.9", "saveSpec": null, "fetchSpec": "1.0.9"}, "_requiredBy": ["/js-yaml"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/argparse/-/argparse-1.0.9.tgz", "_spec": "1.0.9", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {"sprintf-js": "~1.0.2"}, "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "devDependencies": {"eslint": "^2.13.1", "istanbul": "^0.4.5", "mocha": "^3.1.0", "ndoc": "^5.0.1"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/nodeca/argparse#readme", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "license": "MIT", "name": "<PERSON><PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "version": "1.0.9"}