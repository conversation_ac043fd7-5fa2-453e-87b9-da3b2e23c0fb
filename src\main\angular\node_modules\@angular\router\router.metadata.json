{"__symbolic": "module", "version": 3, "metadata": {"ɵa": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ROUTER_FORROOT_GUARD"]}, "ɵb": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgProbeToken"}, "arguments": ["Router", {"__symbolic": "reference", "name": "Router"}]}}, "ɵc": {"__symbolic": "function", "parameters": ["platformLocationStrategy", "baseHref", "options"], "defaults": [null, null, {}], "value": {"__symbolic": "if", "condition": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "options"}, "member": "useHash"}, "thenExpression": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/common", "name": "HashLocationStrategy"}, "arguments": [{"__symbolic": "reference", "name": "platformLocationStrategy"}, {"__symbolic": "reference", "name": "baseHref"}]}, "elseExpression": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/common", "name": "PathLocationStrategy"}, "arguments": [{"__symbolic": "reference", "name": "platformLocationStrategy"}, {"__symbolic": "reference", "name": "baseHref"}]}}}, "ɵd": {"__symbolic": "function"}, "ɵe": {"__symbolic": "function"}, "ɵf": {"__symbolic": "function", "parameters": ["router"], "value": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "router"}, "member": "routerState"}, "member": "root"}}, "ɵg": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}]}], "appInitializer": [{"__symbolic": "method"}], "bootstrapListener": [{"__symbolic": "method"}], "isLegacyEnabled": [{"__symbolic": "method"}], "isLegacyDisabled": [{"__symbolic": "method"}]}}, "ɵh": {"__symbolic": "function", "parameters": ["r"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "r"}, "member": "appInitializer"}, "member": "bind"}, "arguments": [{"__symbolic": "reference", "name": "r"}]}}, "ɵi": {"__symbolic": "function", "parameters": ["r"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "r"}, "member": "bootstrapListener"}, "member": "bind"}, "arguments": [{"__symbolic": "reference", "name": "r"}]}}, "ɵj": {"__symbolic": "function", "parameters": [], "value": [{"__symbolic": "reference", "name": "ɵg"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_INITIALIZER"}, "multi": true, "useFactory": {"__symbolic": "reference", "name": "ɵh"}, "deps": [{"__symbolic": "reference", "name": "ɵg"}]}, {"provide": {"__symbolic": "reference", "name": "ROUTER_INITIALIZER"}, "useFactory": {"__symbolic": "reference", "name": "ɵi"}, "deps": [{"__symbolic": "reference", "name": "ɵg"}]}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "APP_BOOTSTRAP_LISTENER"}, "multi": true, "useExisting": {"__symbolic": "reference", "name": "ROUTER_INITIALIZER"}}]}, "Route": {"__symbolic": "interface"}, "RouterLink": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": ":not(a)[routerLink]"}]}], "members": {"queryParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "fragment": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "queryParamsHandling": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "preserveFragment": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "skipLocationChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "replaceUrl": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Attribute"}, "arguments": ["tabindex"]}], null, null], "parameters": [{"__symbolic": "reference", "name": "Router"}, {"__symbolic": "reference", "name": "ActivatedRoute"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}]}], "routerLink": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "preserveQueryParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "onClick": [{"__symbolic": "method", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostListener"}, "arguments": ["click"]}]}]}}, "RouterLinkWithHref": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "a[routerLink]"}]}], "members": {"target": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostBinding"}, "arguments": ["attr.target"]}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "queryParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "fragment": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "queryParamsHandling": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "preserveFragment": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "skipLocationChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "replaceUrl": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "href": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostBinding"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Router"}, {"__symbolic": "reference", "name": "ActivatedRoute"}, {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}]}], "routerLink": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "preserveQueryParams": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngOnChanges": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "onClick": [{"__symbolic": "method", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "HostListener"}, "arguments": ["click", ["$event.button", "$event.ctrlKey", "$event.metaKey", "$event.shiftKey"]]}]}], "updateTargetUrlAndHref": [{"__symbolic": "method"}]}}, "RouterLinkActive": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[routerLinkActive]", "exportAs": "routerLinkActive"}]}], "members": {"links": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren"}, "arguments": [{"__symbolic": "reference", "name": "RouterLink"}, {"descendants": true}]}]}], "linksWithHrefs": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren"}, "arguments": [{"__symbolic": "reference", "name": "RouterLinkWithHref"}, {"descendants": true}]}]}], "routerLinkActiveOptions": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Router"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "ngAfterContentInit": [{"__symbolic": "method"}], "routerLinkActive": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngOnChanges": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "update": [{"__symbolic": "method"}], "isLinkActive": [{"__symbolic": "method"}], "hasActiveLinks": [{"__symbolic": "method"}]}}, "RouterOutlet": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "router-outlet", "exportAs": "outlet"}]}], "members": {"activateEvents": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}, "arguments": ["activate"]}]}], "deactivateEvents": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}, "arguments": ["deactivate"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Attribute"}, "arguments": ["name"]}], null], "parameters": [{"__symbolic": "reference", "name": "ChildrenOutletContexts"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "attach": [{"__symbolic": "method"}], "deactivate": [{"__symbolic": "method"}], "activateWith": [{"__symbolic": "method"}]}}, "NavigationCancel": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "NavigationEnd": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "NavigationError": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "any"}]}], "toString": [{"__symbolic": "method"}]}}, "NavigationStart": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "RouteConfigLoadEnd": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Route"}]}], "toString": [{"__symbolic": "method"}]}}, "RouteConfigLoadStart": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Route"}]}], "toString": [{"__symbolic": "method"}]}}, "RoutesRecognized": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "RouterStateSnapshot"}]}], "toString": [{"__symbolic": "method"}]}}, "CanActivate": {"__symbolic": "interface"}, "CanActivateChild": {"__symbolic": "interface"}, "CanDeactivate": {"__symbolic": "interface"}, "CanLoad": {"__symbolic": "interface"}, "Resolve": {"__symbolic": "interface"}, "RouteReuseStrategy": {"__symbolic": "class", "members": {"shouldDetach": [{"__symbolic": "method"}], "store": [{"__symbolic": "method"}], "shouldAttach": [{"__symbolic": "method"}], "retrieve": [{"__symbolic": "method"}], "shouldReuseRoute": [{"__symbolic": "method"}]}}, "NavigationExtras": {"__symbolic": "interface"}, "Router": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Type", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "UrlSerializer"}, {"__symbolic": "reference", "name": "ChildrenOutletContexts"}, {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, {"__symbolic": "reference", "name": "Routes"}]}], "resetRootComponentType": [{"__symbolic": "method"}], "initialNavigation": [{"__symbolic": "method"}], "setUpLocationChangeListener": [{"__symbolic": "method"}], "triggerEvent": [{"__symbolic": "method"}], "resetConfig": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "createUrlTree": [{"__symbolic": "method"}], "navigateByUrl": [{"__symbolic": "method"}], "navigate": [{"__symbolic": "method"}], "serializeUrl": [{"__symbolic": "method"}], "parseUrl": [{"__symbolic": "method"}], "isActive": [{"__symbolic": "method"}], "removeEmptyProps": [{"__symbolic": "method"}], "processNavigations": [{"__symbolic": "method"}], "scheduleNavigation": [{"__symbolic": "method"}], "executeScheduledNavigation": [{"__symbolic": "method"}], "runNavigate": [{"__symbolic": "method"}], "resetUrlToCurrentUrlTree": [{"__symbolic": "method"}]}}, "ROUTES": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ROUTES"]}, "ExtraOptions": {"__symbolic": "interface"}, "ROUTER_CONFIGURATION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["ROUTER_CONFIGURATION"]}, "ROUTER_INITIALIZER": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["Router Initializer"]}, "RouterModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"declarations": [{"__symbolic": "reference", "name": "RouterOutlet"}, {"__symbolic": "reference", "name": "RouterLink"}, {"__symbolic": "reference", "name": "RouterLinkWithHref"}, {"__symbolic": "reference", "name": "RouterLinkActive"}], "exports": [{"__symbolic": "reference", "name": "RouterOutlet"}, {"__symbolic": "reference", "name": "RouterLink"}, {"__symbolic": "reference", "name": "RouterLinkWithHref"}, {"__symbolic": "reference", "name": "RouterLinkActive"}]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "ɵa"}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}]], "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "Router"}]}]}, "statics": {"forRoot": {"__symbolic": "function", "parameters": ["routes", "config"], "value": {"ngModule": {"__symbolic": "reference", "name": "RouterModule"}, "providers": [{"__symbolic": "reference", "name": "ɵROUTER_PROVIDERS"}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "provideRoutes"}, "arguments": [{"__symbolic": "reference", "name": "routes"}]}, {"provide": {"__symbolic": "reference", "name": "ɵa"}, "useFactory": {"__symbolic": "reference", "name": "ɵd"}, "deps": [[{"__symbolic": "reference", "name": "Router"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf"}}]]}, {"provide": {"__symbolic": "reference", "name": "ROUTER_CONFIGURATION"}, "useValue": {"__symbolic": "if", "condition": {"__symbolic": "reference", "name": "config"}, "thenExpression": {"__symbolic": "reference", "name": "config"}, "elseExpression": {}}}, {"provide": {"__symbolic": "reference", "module": "@angular/common", "name": "LocationStrategy"}, "useFactory": {"__symbolic": "reference", "name": "ɵc"}, "deps": [{"__symbolic": "reference", "module": "@angular/common", "name": "PlatformLocation"}, [{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "APP_BASE_HREF"}]}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}], {"__symbolic": "reference", "name": "ROUTER_CONFIGURATION"}]}, {"provide": {"__symbolic": "reference", "name": "PreloadingStrategy"}, "useExisting": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "reference", "name": "config"}, "right": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "preloadingStrategy"}}, "thenExpression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "config"}, "member": "preloadingStrategy"}, "elseExpression": {"__symbolic": "reference", "name": "NoPreloading"}}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgProbeToken"}, "multi": true, "useFactory": {"__symbolic": "reference", "name": "ɵb"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "ɵj"}}]}}, "forChild": {"__symbolic": "function", "parameters": ["routes"], "value": {"ngModule": {"__symbolic": "reference", "name": "RouterModule"}, "providers": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "provideRoutes"}, "arguments": [{"__symbolic": "reference", "name": "routes"}]}]}}}}, "provideRoutes": {"__symbolic": "function", "parameters": ["routes"], "value": [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "ANALYZE_FOR_ENTRY_COMPONENTS"}, "multi": true, "useValue": {"__symbolic": "reference", "name": "routes"}}, {"provide": {"__symbolic": "reference", "name": "ROUTES"}, "multi": true, "useValue": {"__symbolic": "reference", "name": "routes"}}]}, "ChildrenOutletContexts": {"__symbolic": "class", "members": {"onChildOutletCreated": [{"__symbolic": "method"}], "onChildOutletDestroyed": [{"__symbolic": "method"}], "onOutletDeactivated": [{"__symbolic": "method"}], "onOutletReAttached": [{"__symbolic": "method"}], "getOrCreateContext": [{"__symbolic": "method"}], "getContext": [{"__symbolic": "method"}]}}, "OutletContext": {"__symbolic": "class", "members": {}}, "NoPreloading": {"__symbolic": "class", "members": {"preload": [{"__symbolic": "method"}]}}, "PreloadAllModules": {"__symbolic": "class", "members": {"preload": [{"__symbolic": "method"}]}}, "PreloadingStrategy": {"__symbolic": "class", "members": {"preload": [{"__symbolic": "method"}]}}, "RouterPreloader": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Router"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "name": "PreloadingStrategy"}]}], "setUpPreloading": [{"__symbolic": "method"}], "preload": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "processRoutes": [{"__symbolic": "method"}], "preloadConfig": [{"__symbolic": "method"}]}}, "ActivatedRoute": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Observable", "module": "rxjs/Observable", "arguments": [{"__symbolic": "reference", "name": "Data"}]}, {"__symbolic": "reference", "name": "Observable", "module": "rxjs/Observable", "arguments": [{"__symbolic": "reference", "name": "Data"}]}, {"__symbolic": "reference", "name": "Observable", "module": "rxjs/Observable", "arguments": [{"__symbolic": "reference", "name": "Data"}]}, {"__symbolic": "reference", "name": "Observable", "module": "rxjs/Observable", "arguments": [{"__symbolic": "reference", "name": "Data"}]}, {"__symbolic": "reference", "name": "Observable", "module": "rxjs/Observable", "arguments": [{"__symbolic": "reference", "name": "Data"}]}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "ActivatedRouteSnapshot"}]}], "toString": [{"__symbolic": "method"}]}}, "ActivatedRouteSnapshot": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "UrlSegment"}]}, {"__symbolic": "reference", "name": "Params"}, {"__symbolic": "reference", "name": "Params"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Data"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Type", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Route"}, {"__symbolic": "reference", "name": "UrlSegmentGroup"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "ResolveData"}]}], "toString": [{"__symbolic": "method"}]}}, "RouterState": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵk"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵl"}, {"__symbolic": "reference", "name": "RouterStateSnapshot"}]}], "toString": [{"__symbolic": "method"}]}}, "RouterStateSnapshot": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵk"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "ɵl"}]}], "toString": [{"__symbolic": "method"}]}}, "PRIMARY_OUTLET": "primary", "ParamMap": {"__symbolic": "interface"}, "convertToParamMap": {"__symbolic": "function", "parameters": ["params"], "value": {"__symbolic": "error", "message": "Reference to non-exported class", "line": 61, "character": 0, "context": {"className": "ParamsAsMap"}, "module": "./src/shared"}}, "UrlHandlingStrategy": {"__symbolic": "class", "members": {"shouldProcessUrl": [{"__symbolic": "method"}], "extract": [{"__symbolic": "method"}], "merge": [{"__symbolic": "method"}]}}, "DefaultUrlSerializer": {"__symbolic": "class", "members": {"parse": [{"__symbolic": "method"}], "serialize": [{"__symbolic": "method"}]}}, "UrlSegment": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 196, "character": 25, "module": "./src/url_tree"}]}], "toString": [{"__symbolic": "method"}]}}, "UrlSegmentGroup": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "UrlSegment"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 147, "character": 23, "module": "./src/url_tree"}]}], "hasChildren": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "UrlSerializer": {"__symbolic": "class", "members": {"parse": [{"__symbolic": "method"}], "serialize": [{"__symbolic": "method"}]}}, "UrlTree": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "UrlSegmentGroup"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 113, "character": 26, "module": "./src/url_tree"}, {"__symbolic": "reference", "name": "string"}]}], "toString": [{"__symbolic": "method"}]}}, "VERSION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Version"}, "arguments": ["4.2.5"]}, "ɵROUTER_PROVIDERS": [{"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, {"provide": {"__symbolic": "reference", "name": "UrlSerializer"}, "useClass": {"__symbolic": "reference", "name": "DefaultUrlSerializer"}}, {"provide": {"__symbolic": "reference", "name": "Router"}, "useFactory": {"__symbolic": "reference", "name": "ɵe"}, "deps": [{"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationRef"}, {"__symbolic": "reference", "name": "UrlSerializer"}, {"__symbolic": "reference", "name": "ChildrenOutletContexts"}, {"__symbolic": "reference", "module": "@angular/common", "name": "Location"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Compiler"}, {"__symbolic": "reference", "name": "ROUTES"}, {"__symbolic": "reference", "name": "ROUTER_CONFIGURATION"}, [{"__symbolic": "reference", "name": "UrlHandlingStrategy"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}], [{"__symbolic": "reference", "name": "RouteReuseStrategy"}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}]]}, {"__symbolic": "reference", "name": "ChildrenOutletContexts"}, {"provide": {"__symbolic": "reference", "name": "ActivatedRoute"}, "useFactory": {"__symbolic": "reference", "name": "ɵf"}, "deps": [{"__symbolic": "reference", "name": "Router"}]}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactoryLoader"}, "useClass": {"__symbolic": "reference", "module": "@angular/core", "name": "SystemJsNgModuleLoader"}}, {"__symbolic": "reference", "name": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "name": "NoPreloading"}, {"__symbolic": "reference", "name": "PreloadAllModules"}, {"provide": {"__symbolic": "reference", "name": "ROUTER_CONFIGURATION"}, "useValue": {"enableTracing": false}}], "ɵflatten": {"__symbolic": "function", "parameters": ["arr"], "value": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "Array"}, "member": "prototype"}, "member": "concat"}, "member": "apply"}, "arguments": [[], {"__symbolic": "reference", "name": "arr"}]}}, "ɵk": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵl"}]}], "parent": [{"__symbolic": "method"}], "children": [{"__symbolic": "method"}], "firstChild": [{"__symbolic": "method"}], "siblings": [{"__symbolic": "method"}], "pathFromRoot": [{"__symbolic": "method"}]}}, "ɵl": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 86, "character": 28, "context": {"typeName": "T"}, "module": "./src/utils/tree"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "ɵl"}]}]}], "toString": [{"__symbolic": "method"}]}}}, "origins": {"ɵa": "./src/router_module", "ɵb": "./src/router_module", "ɵc": "./src/router_module", "ɵd": "./src/router_module", "ɵe": "./src/router_module", "ɵf": "./src/router_module", "ɵg": "./src/router_module", "ɵh": "./src/router_module", "ɵi": "./src/router_module", "ɵj": "./src/router_module", "Data": "./src/config", "LoadChildren": "./src/config", "LoadChildrenCallback": "./src/config", "ResolveData": "./src/config", "Route": "./src/config", "Routes": "./src/config", "RunGuardsAndResolvers": "./src/config", "RouterLink": "./src/directives/router_link", "RouterLinkWithHref": "./src/directives/router_link", "RouterLinkActive": "./src/directives/router_link_active", "RouterOutlet": "./src/directives/router_outlet", "Event": "./src/events", "NavigationCancel": "./src/events", "NavigationEnd": "./src/events", "NavigationError": "./src/events", "NavigationStart": "./src/events", "RouteConfigLoadEnd": "./src/events", "RouteConfigLoadStart": "./src/events", "RoutesRecognized": "./src/events", "CanActivate": "./src/interfaces", "CanActivateChild": "./src/interfaces", "CanDeactivate": "./src/interfaces", "CanLoad": "./src/interfaces", "Resolve": "./src/interfaces", "DetachedRouteHandle": "./src/route_reuse_strategy", "RouteReuseStrategy": "./src/route_reuse_strategy", "NavigationExtras": "./src/router", "Router": "./src/router", "ROUTES": "./src/router_config_loader", "ExtraOptions": "./src/router_module", "ROUTER_CONFIGURATION": "./src/router_module", "ROUTER_INITIALIZER": "./src/router_module", "RouterModule": "./src/router_module", "provideRoutes": "./src/router_module", "ChildrenOutletContexts": "./src/router_outlet_context", "OutletContext": "./src/router_outlet_context", "NoPreloading": "./src/router_preloader", "PreloadAllModules": "./src/router_preloader", "PreloadingStrategy": "./src/router_preloader", "RouterPreloader": "./src/router_preloader", "ActivatedRoute": "./src/router_state", "ActivatedRouteSnapshot": "./src/router_state", "RouterState": "./src/router_state", "RouterStateSnapshot": "./src/router_state", "PRIMARY_OUTLET": "./src/shared", "ParamMap": "./src/shared", "Params": "./src/shared", "convertToParamMap": "./src/shared", "UrlHandlingStrategy": "./src/url_handling_strategy", "DefaultUrlSerializer": "./src/url_tree", "UrlSegment": "./src/url_tree", "UrlSegmentGroup": "./src/url_tree", "UrlSerializer": "./src/url_tree", "UrlTree": "./src/url_tree", "VERSION": "./src/version", "ɵROUTER_PROVIDERS": "./src/router_module", "ɵflatten": "./src/utils/collection", "ɵk": "./src/utils/tree", "ɵl": "./src/utils/tree"}, "importAs": "@angular/router"}