{"_args": [["anymatch@1.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "anymatch@1.3.0", "_id": "anymatch@1.3.0", "_inBundle": false, "_integrity": "sha1-o+Uvo5FoyCX/V7AkgSbOWo/5VQc=", "_location": "/anymatch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "anymatch@1.3.0", "name": "anymatch", "escapedName": "anymatch", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/chokidar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/anymatch/-/anymatch-1.3.0.tgz", "_spec": "1.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "url": "http://github.com/es128"}, "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.5"}, "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.13", "mocha": "^2.2.4"}, "files": ["index.js"], "homepage": "https://github.com/es128/anymatch", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "license": "ISC", "name": "anymatch", "repository": {"type": "git", "url": "git+https://github.com/es128/anymatch.git"}, "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "version": "1.3.0"}