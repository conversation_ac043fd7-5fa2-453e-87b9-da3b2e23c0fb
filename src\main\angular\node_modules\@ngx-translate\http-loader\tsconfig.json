{"compilerOptions": {"noImplicitAny": true, "module": "es2015", "target": "es5", "emitDecoratorMetadata": true, "experimentalDecorators": true, "declaration": true, "moduleResolution": "node", "noUnusedLocals": true, "types": ["<PERSON><PERSON><PERSON>", "jasmine", "node"], "lib": ["es2015", "dom"]}, "files": ["index.ts", "./src/http-loader.ts", "tests/http-loader.spec.ts"], "exclude": ["node_modules", "bundles"], "angularCompilerOptions": {"strictMetadataEmit": true, "skipTemplateCodegen": true}}