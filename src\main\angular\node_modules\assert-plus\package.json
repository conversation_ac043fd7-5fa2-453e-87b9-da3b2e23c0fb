{"_args": [["assert-plus@0.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "assert-plus@0.2.0", "_id": "assert-plus@0.2.0", "_inBundle": false, "_integrity": "sha1-104bh+ev/A24qttwIfP+SBAasjQ=", "_location": "/assert-plus", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "assert-plus@0.2.0", "name": "assert-plus", "escapedName": "assert-plus", "rawSpec": "0.2.0", "saveSpec": null, "fetchSpec": "0.2.0"}, "_requiredBy": ["/http-signature"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/assert-plus/-/assert-plus-0.2.0.tgz", "_spec": "0.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "Extra assertions on top of node's assert module", "devDependencies": {"faucet": "0.0.1", "tape": "4.2.2"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/mcavage/node-assert-plus#readme", "license": "MIT", "main": "./assert.js", "name": "assert-plus", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/mcavage/node-assert-plus.git"}, "scripts": {"test": "tape tests/*.js | ./node_modules/.bin/faucet"}, "version": "0.2.0"}