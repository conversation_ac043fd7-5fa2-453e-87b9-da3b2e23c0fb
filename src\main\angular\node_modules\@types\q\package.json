{"_args": [["@types/q@0.0.32", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@types/q@0.0.32", "_id": "@types/q@0.0.32", "_inBundle": false, "_integrity": "sha1-vShOV8hPEyXacCur/IKlMoGQwMU=", "_location": "/@types/q", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/q@0.0.32", "name": "@types/q", "escapedName": "@types%2fq", "scope": "@types", "rawSpec": "0.0.32", "saveSpec": null, "fetchSpec": "0.0.32"}, "_requiredBy": ["/protractor"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/q/-/q-0.0.32.tgz", "_spec": "0.0.32", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/bnemetchek"}, "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "dependencies": {}, "description": "TypeScript definitions for Q", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/q", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typesPublisherContentHash": "6597b89d57d9ceec9ef1e6af32e2fd02f4582abaf309f8a76f6746ee1af830d5", "typings": "index.d.ts", "version": "0.0.32"}