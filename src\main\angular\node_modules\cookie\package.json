{"_args": [["cookie@0.3.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cookie@0.3.1", "_id": "cookie@0.3.1", "_inBundle": false, "_integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "_location": "/cookie", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie@0.3.1", "name": "cookie", "escapedName": "cookie", "rawSpec": "0.3.1", "saveSpec": null, "fetchSpec": "0.3.1"}, "_requiredBy": ["/engine.io", "/express"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cookie/-/cookie-0.3.1.tgz", "_spec": "0.3.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP server cookie parsing and serialization", "devDependencies": {"istanbul": "0.4.3", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "0.3.1"}