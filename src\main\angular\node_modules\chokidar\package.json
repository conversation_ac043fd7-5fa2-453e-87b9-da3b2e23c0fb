{"_args": [["chokidar@1.7.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "chokidar@1.7.0", "_id": "chokidar@1.7.0", "_inBundle": false, "_integrity": "sha1-eY5ol3gVHIB2tLNg5e3SjNortGg=", "_location": "/chokidar", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "chokidar@1.7.0", "name": "chokidar", "escapedName": "chokidar", "rawSpec": "1.7.0", "saveSpec": null, "fetchSpec": "1.7.0"}, "_requiredBy": ["/karma", "/watchpack", "/webpack-dev-server"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/chokidar/-/chokidar-1.7.0.tgz", "_spec": "1.7.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "http://paulmillr.com"}, "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "fsevents": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0"}, "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/paulmillr/chokidar", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "license": "MIT", "name": "chokidar", "optionalDependencies": {"fsevents": "^1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "scripts": {"ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls", "test": "istanbul test node_modules/mocha/bin/_mocha"}, "version": "1.7.0"}