{"version": 3, "file": "webpack-config.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["models/webpack-config.ts"], "names": [], "mappings": ";;AAAA,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAC9C,qCAAqC;AAErC,uDAQ2B;AAE3B,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAQ7B;IAGE,YAAY,YAA0B,EAAE,SAAc;QAEpD,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAExC,MAAM,UAAU,GAAG,kBAAS,CAAC,cAAc,EAAE,CAAC;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7C,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACjD,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACpD,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAE1D,IAAI,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;IACtD,CAAC;IAEM,WAAW;QAChB,IAAI,cAAc,GAAG;YACnB,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC;YACzB,kCAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;YAC1B,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;SAC/B,CAAC;QAEF,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YAC5D,MAAM,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG;kBACrD,8BAAY,CAAC,IAAI,CAAC,GAAG,CAAC;kBACtB,iCAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,cAAc,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,eAAe,CAAC,oBAA0C;QAC/D,MAAM,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YACjD,KAAK,aAAa;gBAChB,MAAM,CAAC,8BAAY,CAAC,oBAAoB,CAAC,CAAC;YAC5C,KAAK,YAAY;gBACf,MAAM,CAAC,+BAAa,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,yBAAyB;IAClB,oBAAoB,CAAC,YAA0B;QACpD,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,aAAa,CAAC;QAC3D,EAAE,CAAC,CAAC,YAAY,CAAC,MAAM,KAAK,aAAa,IAAI,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC;YAClF,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED,qCAAqC;IAC9B,iBAAiB,CAAC,YAA0B;QACjD,MAAM,cAAc,GAAuC;YACzD,WAAW,EAAE;gBACX,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,OAAO;gBACtB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,KAAK;aAClB;YACD,UAAU,EAAE;gBACV,WAAW,EAAE,MAAM;gBACnB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,IAAI;aACV;SACF,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC;IAC9E,CAAC;IAED,0CAA0C;IACnC,YAAY,CAAC,YAA0B,EAAE,SAAc;QAC5D,MAAM,gBAAgB,GAAG;YACvB,UAAU,EAAE,SAAS,CAAC,MAAM;YAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;SAC7B,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAC3D,CAAC;IAEM,oBAAoB,CAAC,SAAc;QACxC,MAAM,iBAAiB,GAAQ;YAC7B,YAAY,EAAE,SAAS,CAAC,QAAQ;YAChC,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,8EAA8E;QAC9E,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC/C,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,CAAC,SAAS,CAAC;IACnB,CAAC;CACF;AAnGD,gDAmGC"}