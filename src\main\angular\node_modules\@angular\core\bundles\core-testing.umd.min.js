/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],factory):factory((global.ng=global.ng||{},global.ng.core=global.ng.core||{},global.ng.core.testing=global.ng.core.testing||{}),global.ng.core)}(this,function(exports,_angular_core){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}function async(fn){return _global.jasmine?function(done){done||(done=function(){},done.fail=function(e){throw e}),runInTestZone(fn,this,done,function(err){return"string"==typeof err?done.fail(new Error(err)):void done.fail(err)})}:function(){var _this=this;return new Promise(function(finishCallback,failCallback){runInTestZone(fn,_this,finishCallback,failCallback)})}}function runInTestZone(fn,context,finishCallback,failCallback){var currentZone=Zone.current,AsyncTestZoneSpec=Zone.AsyncTestZoneSpec;if(void 0===AsyncTestZoneSpec)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/dist/async-test.js");var ProxyZoneSpec=Zone.ProxyZoneSpec;if(void 0===ProxyZoneSpec)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/dist/proxy.js");var proxyZoneSpec=ProxyZoneSpec.get();ProxyZoneSpec.assertPresent();var proxyZone=Zone.current.getZoneWith("ProxyZoneSpec"),previousDelegate=proxyZoneSpec.getDelegate();return proxyZone.parent.run(function(){var testZoneSpec=new AsyncTestZoneSpec(function(){currentZone.run(function(){proxyZoneSpec.getDelegate()==testZoneSpec&&proxyZoneSpec.setDelegate(previousDelegate),finishCallback()})},function(error){currentZone.run(function(){proxyZoneSpec.getDelegate()==testZoneSpec&&proxyZoneSpec.setDelegate(previousDelegate),failCallback(error)})},"test");proxyZoneSpec.setDelegate(testZoneSpec)}),Zone.current.runGuarded(fn,context)}function scheduleMicroTask(fn){Zone.current.scheduleMicroTask("scheduleMicrotask",fn)}function resetFakeAsyncZone(){_fakeAsyncTestZoneSpec=null,ProxyZoneSpec.assertPresent().resetDelegate()}function fakeAsync(fn){return function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i]=arguments[_i];var proxyZoneSpec=ProxyZoneSpec.assertPresent();if(_inFakeAsyncCall)throw new Error("fakeAsync() calls can not be nested");_inFakeAsyncCall=!0;try{if(!_fakeAsyncTestZoneSpec){if(proxyZoneSpec.getDelegate()instanceof FakeAsyncTestZoneSpec)throw new Error("fakeAsync() calls can not be nested");_fakeAsyncTestZoneSpec=new FakeAsyncTestZoneSpec}var res=void 0,lastProxyZoneSpec=proxyZoneSpec.getDelegate();proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);try{res=fn.apply(this,args),flushMicrotasks()}finally{proxyZoneSpec.setDelegate(lastProxyZoneSpec)}if(_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length>0)throw new Error(_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length+" periodic timer(s) still in the queue.");if(_fakeAsyncTestZoneSpec.pendingTimers.length>0)throw new Error(_fakeAsyncTestZoneSpec.pendingTimers.length+" timer(s) still in the queue.");return res}finally{_inFakeAsyncCall=!1,resetFakeAsyncZone()}}}function _getFakeAsyncZoneSpec(){if(null==_fakeAsyncTestZoneSpec)throw new Error("The code should be running in the fakeAsync zone to call this function");return _fakeAsyncTestZoneSpec}function tick(millis){void 0===millis&&(millis=0),_getFakeAsyncZoneSpec().tick(millis)}function flush(maxTurns){return _getFakeAsyncZoneSpec().flush(maxTurns)}function discardPeriodicTasks(){var zoneSpec=_getFakeAsyncZoneSpec();zoneSpec.pendingPeriodicTimers;zoneSpec.pendingPeriodicTimers.length=0}function flushMicrotasks(){_getFakeAsyncZoneSpec().flushMicrotasks()}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function unimplemented(){throw Error("unimplemented")}function getTestBed(){return _testBed=_testBed||new TestBed}function inject(tokens,fn){var testBed=getTestBed();return tokens.indexOf(AsyncTestCompleter)>=0?function(){var _this=this;return testBed.compileComponents().then(function(){var completer=testBed.get(AsyncTestCompleter);return testBed.execute(tokens,fn,_this),completer.promise})}:function(){return testBed.execute(tokens,fn,this)}}function withModule(moduleDef,fn){return fn?function(){var testBed=getTestBed();return moduleDef&&testBed.configureTestingModule(moduleDef),fn.apply(this)}:new InjectSetupWrapper(function(){return moduleDef})}function getComponentType(error){return error[_angular_core.ɵERROR_COMPONENT_TYPE]}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},_global="undefined"==typeof window?global:window,ComponentFixture=function(){function ComponentFixture(componentRef,ngZone,_autoDetect){var _this=this;this.componentRef=componentRef,this.ngZone=ngZone,this._autoDetect=_autoDetect,this._isStable=!0,this._isDestroyed=!1,this._resolve=null,this._promise=null,this._onUnstableSubscription=null,this._onStableSubscription=null,this._onMicrotaskEmptySubscription=null,this._onErrorSubscription=null,this.changeDetectorRef=componentRef.changeDetectorRef,this.elementRef=componentRef.location,this.debugElement=_angular_core.getDebugNode(this.elementRef.nativeElement),this.componentInstance=componentRef.instance,this.nativeElement=this.elementRef.nativeElement,this.componentRef=componentRef,this.ngZone=ngZone,ngZone&&(this._onUnstableSubscription=ngZone.onUnstable.subscribe({next:function(){_this._isStable=!1}}),this._onMicrotaskEmptySubscription=ngZone.onMicrotaskEmpty.subscribe({next:function(){_this._autoDetect&&_this.detectChanges(!0)}}),this._onStableSubscription=ngZone.onStable.subscribe({next:function(){_this._isStable=!0,null!==_this._promise&&scheduleMicroTask(function(){ngZone.hasPendingMacrotasks||null!==_this._promise&&(_this._resolve(!0),_this._resolve=null,_this._promise=null)})}}),this._onErrorSubscription=ngZone.onError.subscribe({next:function(error){throw error}}))}return ComponentFixture.prototype._tick=function(checkNoChanges){this.changeDetectorRef.detectChanges(),checkNoChanges&&this.checkNoChanges()},ComponentFixture.prototype.detectChanges=function(checkNoChanges){var _this=this;void 0===checkNoChanges&&(checkNoChanges=!0),null!=this.ngZone?this.ngZone.run(function(){_this._tick(checkNoChanges)}):this._tick(checkNoChanges)},ComponentFixture.prototype.checkNoChanges=function(){this.changeDetectorRef.checkNoChanges()},ComponentFixture.prototype.autoDetectChanges=function(autoDetect){if(void 0===autoDetect&&(autoDetect=!0),null==this.ngZone)throw new Error("Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set");this._autoDetect=autoDetect,this.detectChanges()},ComponentFixture.prototype.isStable=function(){return this._isStable&&!this.ngZone.hasPendingMacrotasks},ComponentFixture.prototype.whenStable=function(){var _this=this;return this.isStable()?Promise.resolve(!1):null!==this._promise?this._promise:(this._promise=new Promise(function(res){_this._resolve=res}),this._promise)},ComponentFixture.prototype._getRenderer=function(){return void 0===this._renderer&&(this._renderer=this.componentRef.injector.get(_angular_core.RendererFactory2,null)),this._renderer},ComponentFixture.prototype.whenRenderingDone=function(){var renderer=this._getRenderer();return renderer&&renderer.whenRenderingDone?renderer.whenRenderingDone():this.whenStable()},ComponentFixture.prototype.destroy=function(){this._isDestroyed||(this.componentRef.destroy(),null!=this._onUnstableSubscription&&(this._onUnstableSubscription.unsubscribe(),this._onUnstableSubscription=null),null!=this._onStableSubscription&&(this._onStableSubscription.unsubscribe(),this._onStableSubscription=null),null!=this._onMicrotaskEmptySubscription&&(this._onMicrotaskEmptySubscription.unsubscribe(),this._onMicrotaskEmptySubscription=null),null!=this._onErrorSubscription&&(this._onErrorSubscription.unsubscribe(),this._onErrorSubscription=null),this._isDestroyed=!0)},ComponentFixture}(),FakeAsyncTestZoneSpec=Zone.FakeAsyncTestZoneSpec,ProxyZoneSpec=Zone.ProxyZoneSpec,_fakeAsyncTestZoneSpec=null,_inFakeAsyncCall=!1,AsyncTestCompleter=function(){function AsyncTestCompleter(){var _this=this;this._promise=new Promise(function(res,rej){_this._resolve=res,_this._reject=rej})}return AsyncTestCompleter.prototype.done=function(value){this._resolve(value)},AsyncTestCompleter.prototype.fail=function(error,stackTrace){this._reject(error)},Object.defineProperty(AsyncTestCompleter.prototype,"promise",{get:function(){return this._promise},enumerable:!0,configurable:!0}),AsyncTestCompleter}(),TestingCompiler=function(_super){function TestingCompiler(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(TestingCompiler,_super),Object.defineProperty(TestingCompiler.prototype,"injector",{get:function(){throw unimplemented()},enumerable:!0,configurable:!0}),TestingCompiler.prototype.overrideModule=function(module,overrides){throw unimplemented()},TestingCompiler.prototype.overrideDirective=function(directive,overrides){throw unimplemented()},TestingCompiler.prototype.overrideComponent=function(component,overrides){throw unimplemented()},TestingCompiler.prototype.overridePipe=function(directive,overrides){throw unimplemented()},TestingCompiler.prototype.loadAotSummaries=function(summaries){throw unimplemented()},TestingCompiler.prototype.getComponentFactory=function(component){throw unimplemented()},TestingCompiler}(_angular_core.Compiler),TestingCompilerFactory=function(){function TestingCompilerFactory(){}return TestingCompilerFactory}(),UNDEFINED=new Object,TestComponentRenderer=function(){function TestComponentRenderer(){}return TestComponentRenderer.prototype.insertRootElement=function(rootElementId){},TestComponentRenderer}(),_nextRootElementId=0,ComponentFixtureAutoDetect=new _angular_core.InjectionToken("ComponentFixtureAutoDetect"),ComponentFixtureNoNgZone=new _angular_core.InjectionToken("ComponentFixtureNoNgZone"),TestBed=function(){function TestBed(){this._instantiated=!1,this._compiler=null,this._moduleRef=null,this._moduleFactory=null,this._compilerOptions=[],this._moduleOverrides=[],this._componentOverrides=[],this._directiveOverrides=[],this._pipeOverrides=[],this._providers=[],this._declarations=[],this._imports=[],this._schemas=[],this._activeFixtures=[],this._aotSummaries=function(){return[]},this.platform=null,this.ngModule=null}return TestBed.initTestEnvironment=function(ngModule,platform,aotSummaries){var testBed=getTestBed();return testBed.initTestEnvironment(ngModule,platform,aotSummaries),testBed},TestBed.resetTestEnvironment=function(){getTestBed().resetTestEnvironment()},TestBed.resetTestingModule=function(){return getTestBed().resetTestingModule(),TestBed},TestBed.configureCompiler=function(config){return getTestBed().configureCompiler(config),TestBed},TestBed.configureTestingModule=function(moduleDef){return getTestBed().configureTestingModule(moduleDef),TestBed},TestBed.compileComponents=function(){return getTestBed().compileComponents()},TestBed.overrideModule=function(ngModule,override){return getTestBed().overrideModule(ngModule,override),TestBed},TestBed.overrideComponent=function(component,override){return getTestBed().overrideComponent(component,override),TestBed},TestBed.overrideDirective=function(directive,override){return getTestBed().overrideDirective(directive,override),TestBed},TestBed.overridePipe=function(pipe,override){return getTestBed().overridePipe(pipe,override),TestBed},TestBed.overrideTemplate=function(component,template){return getTestBed().overrideComponent(component,{set:{template:template,templateUrl:null}}),TestBed},TestBed.overrideProvider=function(token,provider){return getTestBed().overrideProvider(token,provider),TestBed},TestBed.get=function(token,notFoundValue){return void 0===notFoundValue&&(notFoundValue=_angular_core.Injector.THROW_IF_NOT_FOUND),getTestBed().get(token,notFoundValue)},TestBed.createComponent=function(component){return getTestBed().createComponent(component)},TestBed.prototype.initTestEnvironment=function(ngModule,platform,aotSummaries){if(this.platform||this.ngModule)throw new Error("Cannot set base providers because it has already been called");this.platform=platform,this.ngModule=ngModule,aotSummaries&&(this._aotSummaries=aotSummaries)},TestBed.prototype.resetTestEnvironment=function(){this.resetTestingModule(),this.platform=null,this.ngModule=null,this._aotSummaries=function(){return[]}},TestBed.prototype.resetTestingModule=function(){_angular_core.ɵclearProviderOverrides(),this._compiler=null,this._moduleOverrides=[],this._componentOverrides=[],this._directiveOverrides=[],this._pipeOverrides=[],this._moduleRef=null,this._moduleFactory=null,this._compilerOptions=[],this._providers=[],this._declarations=[],this._imports=[],this._schemas=[],this._instantiated=!1,this._activeFixtures.forEach(function(fixture){try{fixture.destroy()}catch(e){console.error("Error during cleanup of component",fixture.componentInstance)}}),this._activeFixtures=[]},TestBed.prototype.configureCompiler=function(config){this._assertNotInstantiated("TestBed.configureCompiler","configure the compiler"),this._compilerOptions.push(config)},TestBed.prototype.configureTestingModule=function(moduleDef){this._assertNotInstantiated("TestBed.configureTestingModule","configure the test module"),moduleDef.providers&&(_a=this._providers).push.apply(_a,moduleDef.providers),moduleDef.declarations&&(_b=this._declarations).push.apply(_b,moduleDef.declarations),moduleDef.imports&&(_c=this._imports).push.apply(_c,moduleDef.imports),moduleDef.schemas&&(_d=this._schemas).push.apply(_d,moduleDef.schemas);var _a,_b,_c,_d},TestBed.prototype.compileComponents=function(){var _this=this;if(this._moduleFactory||this._instantiated)return Promise.resolve(null);var moduleType=this._createCompilerAndModule();return this._compiler.compileModuleAndAllComponentsAsync(moduleType).then(function(moduleAndComponentFactories){_this._moduleFactory=moduleAndComponentFactories.ngModuleFactory})},TestBed.prototype._initIfNeeded=function(){if(!this._instantiated){if(!this._moduleFactory)try{var moduleType=this._createCompilerAndModule();this._moduleFactory=this._compiler.compileModuleAndAllComponentsSync(moduleType).ngModuleFactory}catch(e){throw getComponentType(e)?new Error("This test module uses the component "+_angular_core.ɵstringify(getComponentType(e))+' which is using a "templateUrl" or "styleUrls", but they were never compiled. Please call "TestBed.compileComponents" before your test.'):e}var ngZone=new _angular_core.NgZone({enableLongStackTrace:!0}),ngZoneInjector=_angular_core.ReflectiveInjector.resolveAndCreate([{provide:_angular_core.NgZone,useValue:ngZone}],this.platform.injector);this._moduleRef=this._moduleFactory.create(ngZoneInjector),this._moduleRef.injector.get(_angular_core.ApplicationInitStatus).runInitializers(),this._instantiated=!0}},TestBed.prototype._createCompilerAndModule=function(){var _this=this,providers=this._providers.concat([{provide:TestBed,useValue:this}]),declarations=this._declarations,imports=[this.ngModule,this._imports],schemas=this._schemas,DynamicTestModule=function(){function DynamicTestModule(){}return DynamicTestModule}();DynamicTestModule.decorators=[{type:_angular_core.NgModule,args:[{providers:providers,declarations:declarations,imports:imports,schemas:schemas}]}],DynamicTestModule.ctorParameters=function(){return[]};var compilerFactory=this.platform.injector.get(TestingCompilerFactory);return this._compiler=compilerFactory.createTestingCompiler(this._compilerOptions.concat([{useDebug:!0}])),this._compiler.loadAotSummaries(this._aotSummaries),this._moduleOverrides.forEach(function(entry){return _this._compiler.overrideModule(entry[0],entry[1])}),this._componentOverrides.forEach(function(entry){return _this._compiler.overrideComponent(entry[0],entry[1])}),this._directiveOverrides.forEach(function(entry){return _this._compiler.overrideDirective(entry[0],entry[1])}),this._pipeOverrides.forEach(function(entry){return _this._compiler.overridePipe(entry[0],entry[1])}),DynamicTestModule},TestBed.prototype._assertNotInstantiated=function(methodName,methodDescription){if(this._instantiated)throw new Error("Cannot "+methodDescription+" when the test module has already been instantiated. "+("Make sure you are not using `inject` before `"+methodName+"`."))},TestBed.prototype.get=function(token,notFoundValue){if(void 0===notFoundValue&&(notFoundValue=_angular_core.Injector.THROW_IF_NOT_FOUND),this._initIfNeeded(),token===TestBed)return this;var result=this._moduleRef.injector.get(token,UNDEFINED);return result===UNDEFINED?this._compiler.injector.get(token,notFoundValue):result},TestBed.prototype.execute=function(tokens,fn,context){var _this=this;this._initIfNeeded();var params=tokens.map(function(t){return _this.get(t)});return fn.apply(context,params)},TestBed.prototype.overrideModule=function(ngModule,override){this._assertNotInstantiated("overrideModule","override module metadata"),this._moduleOverrides.push([ngModule,override])},TestBed.prototype.overrideComponent=function(component,override){this._assertNotInstantiated("overrideComponent","override component metadata"),this._componentOverrides.push([component,override])},TestBed.prototype.overrideDirective=function(directive,override){this._assertNotInstantiated("overrideDirective","override directive metadata"),this._directiveOverrides.push([directive,override])},TestBed.prototype.overridePipe=function(pipe,override){this._assertNotInstantiated("overridePipe","override pipe metadata"),this._pipeOverrides.push([pipe,override])},TestBed.prototype.overrideProvider=function(token,provider){var value,flags=0;provider.useFactory?(flags|=1024,value=provider.useFactory):(flags|=256,value=provider.useValue);var deps=(provider.deps||[]).map(function(dep){var depToken,depFlags=0;return Array.isArray(dep)?dep.forEach(function(entry){entry instanceof _angular_core.Optional?depFlags|=2:entry instanceof _angular_core.SkipSelf?depFlags|=1:depToken=entry}):depToken=dep,[depFlags,depToken]});_angular_core.ɵoverrideProvider({token:token,flags:flags,deps:deps,value:value})},TestBed.prototype.createComponent=function(component){var _this=this;this._initIfNeeded();var componentFactory=this._compiler.getComponentFactory(component);if(!componentFactory)throw new Error("Cannot create the component "+_angular_core.ɵstringify(component)+" as it was not imported into the testing module!");var noNgZone=this.get(ComponentFixtureNoNgZone,!1),autoDetect=this.get(ComponentFixtureAutoDetect,!1),ngZone=noNgZone?null:this.get(_angular_core.NgZone,null),testComponentRenderer=this.get(TestComponentRenderer),rootElId="root"+_nextRootElementId++;testComponentRenderer.insertRootElement(rootElId);var initComponent=function(){var componentRef=componentFactory.create(_angular_core.Injector.NULL,[],"#"+rootElId,_this._moduleRef);return new ComponentFixture(componentRef,ngZone,autoDetect)},fixture=ngZone?ngZone.run(initComponent):initComponent();return this._activeFixtures.push(fixture),fixture},TestBed}(),_testBed=null,InjectSetupWrapper=function(){function InjectSetupWrapper(_moduleDef){this._moduleDef=_moduleDef}return InjectSetupWrapper.prototype._addModule=function(){var moduleDef=this._moduleDef();moduleDef&&getTestBed().configureTestingModule(moduleDef)},InjectSetupWrapper.prototype.inject=function(tokens,fn){var self=this;return function(){return self._addModule(),inject(tokens,fn).call(this)}},InjectSetupWrapper}(),_global$1="undefined"==typeof window?global:window;_global$1.beforeEach&&_global$1.beforeEach(function(){TestBed.resetTestingModule(),resetFakeAsyncZone()});var __core_private_testing_placeholder__="";exports.async=async,exports.ComponentFixture=ComponentFixture,exports.resetFakeAsyncZone=resetFakeAsyncZone,exports.fakeAsync=fakeAsync,exports.tick=tick,exports.flush=flush,exports.discardPeriodicTasks=discardPeriodicTasks,exports.flushMicrotasks=flushMicrotasks,exports.TestComponentRenderer=TestComponentRenderer,exports.ComponentFixtureAutoDetect=ComponentFixtureAutoDetect,exports.ComponentFixtureNoNgZone=ComponentFixtureNoNgZone,exports.TestBed=TestBed,exports.getTestBed=getTestBed,exports.inject=inject,exports.InjectSetupWrapper=InjectSetupWrapper,exports.withModule=withModule,exports.__core_private_testing_placeholder__=__core_private_testing_placeholder__,exports.ɵTestingCompiler=TestingCompiler,exports.ɵTestingCompilerFactory=TestingCompilerFactory,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=core-testing.umd.min.js.map
