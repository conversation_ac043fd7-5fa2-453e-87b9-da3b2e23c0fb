[{"__symbolic": "module", "version": 3, "metadata": {"DOMTestComponentRenderer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "TestComponent<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "insertRootElement": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"DOMTestComponentRenderer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "TestComponent<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "insertRootElement": [{"__symbolic": "method"}]}}}}]