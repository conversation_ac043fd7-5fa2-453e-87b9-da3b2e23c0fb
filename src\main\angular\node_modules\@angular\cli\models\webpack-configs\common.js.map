{"version": 3, "file": "common.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["models/webpack-configs/common.ts"], "names": [], "mappings": ";;AAAA,mCAAmC;AACnC,6BAA6B;AAC7B,qFAA+E;AAC/E,mCAAgE;AAGhE,MAAM,cAAc,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAG7D;;;;;;;;;;GAUG;AAEH,yBAAgC,GAAyB;IACvD,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;IAErD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAE9D,IAAI,YAAY,GAAU,EAAE,CAAC;IAC7B,IAAI,UAAU,GAAU,EAAE,CAAC;IAC3B,IAAI,WAAW,GAAgC,EAAE,CAAC;IAElD,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACnB,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QACxB,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,2BAA2B;IAC3B,MAAM,UAAU,GAAG,2BAAmB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAEnE,yBAAyB;IACzB,EAAE,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,aAAa,GAAG,wBAAgB,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAE9E,mCAAmC;QACnC,aAAa,CAAC,OAAO,CAAC,MAAM;YAC1B,IAAI,UAAU,GAAG,iBAAiB,MAAM,CAAC,IAAI,EAAE,CAAC;YAChD,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACrB,YAAY,CAAC,IAAI,CAAC,IAAI,gDAAqB,CAAC;YAC1C,QAAQ,EAAE,SAAS,CAAC,MAAM;YAC1B,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE;SAChE,CAAC,CAAC,CAAC;IACN,CAAC;IAED,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1B,YAAY,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,EAAE,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,sBAAsB,CAAC;YACnD,QAAQ,EAAE,mBAAmB;YAC7B,sBAAsB,EAAE,iBAAiB;YACzC,8BAA8B,EAAE,wBAAwB;YACxD,UAAU,EAAE,aAAa;SAC1B,CAAC,CAAC,CAAC;IACN,CAAC;IAED,MAAM,CAAC;QACL,OAAO,EAAE;YACP,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YAC1B,OAAO,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;YACtC,QAAQ,EAAE,CAAC,YAAY,CAAC,gBAAgB;SACzC;QACD,aAAa,EAAE;YACb,OAAO,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;SACvC;QACD,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE;YACN,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC;YACxD,UAAU,EAAE,YAAY,CAAC,SAAS;YAClC,QAAQ,EAAE,SAAS,UAAU,CAAC,KAAK,YAAY;YAC/C,aAAa,EAAE,OAAO,UAAU,CAAC,KAAK,WAAW;SAClD;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;gBACtF,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE;gBAC1C,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE;gBACzC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,0BAA0B,UAAU,CAAC,IAAI,QAAQ,EAAE;gBACnF;oBACE,IAAI,EAAE,kDAAkD;oBACxD,MAAM,EAAE,yBAAyB,UAAU,CAAC,IAAI,oBAAoB;iBACrE;aACF,CAAC,MAAM,CAAC,UAAU,CAAC;SACrB;QACD,OAAO,EAAE;YACP,IAAI,OAAO,CAAC,oBAAoB,EAAE;SACnC,CAAC,MAAM,CAAC,YAAY,CAAC;QACtB,IAAI,EAAE;YACJ,EAAE,EAAE,OAAO;YACX,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,OAAO;YACf,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,OAAO;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,KAAK;YACb,cAAc,EAAE,KAAK;YACrB,YAAY,EAAE,KAAK;SACpB;KACF,CAAC;AACJ,CAAC;AAjGD,0CAiGC"}