{"_args": [["@angular/compiler@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/compiler@4.2.5", "_id": "@angular/compiler@4.2.5", "_inBundle": false, "_integrity": "sha1-tIZ0x0VrKw3xBy1w5OZnr4bN34M=", "_location": "/@angular/compiler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/compiler@4.2.5", "name": "@angular/compiler", "escapedName": "@angular%2fcompiler", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/", "/angular-svg-round-progressbar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/compiler/-/compiler-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - the compiler library", "es2015": "./@angular/compiler.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/compiler.umd.js", "module": "./@angular/compiler.es5.js", "name": "@angular/compiler", "peerDependencies": {"@angular/core": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./compiler.d.ts", "version": "4.2.5"}