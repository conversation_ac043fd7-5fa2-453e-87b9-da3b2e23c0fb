[{"__symbolic": "module", "version": 3, "metadata": {"RoundProgressModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule"}], "declarations": [{"__symbolic": "reference", "module": "./round-progress.component", "name": "RoundProgressComponent"}], "exports": [{"__symbolic": "reference", "module": "./round-progress.component", "name": "RoundProgressComponent"}], "providers": [{"__symbolic": "reference", "module": "./round-progress.service", "name": "RoundProgressService"}, {"__symbolic": "reference", "module": "./round-progress.ease", "name": "RoundProgressEase"}, {"__symbolic": "reference", "module": "./round-progress.config", "name": "RoundProgressConfig"}]}]}]}}, "exports": [{"from": "./round-progress.component"}, {"from": "./round-progress.service"}, {"from": "./round-progress.ease"}, {"from": "./round-progress.config"}]}, {"__symbolic": "module", "version": 1, "metadata": {"RoundProgressModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule"}], "declarations": [{"__symbolic": "reference", "module": "./round-progress.component", "name": "RoundProgressComponent"}], "exports": [{"__symbolic": "reference", "module": "./round-progress.component", "name": "RoundProgressComponent"}], "providers": [{"__symbolic": "reference", "module": "./round-progress.service", "name": "RoundProgressService"}, {"__symbolic": "reference", "module": "./round-progress.ease", "name": "RoundProgressEase"}, {"__symbolic": "reference", "module": "./round-progress.config", "name": "RoundProgressConfig"}]}]}]}}, "exports": [{"from": "./round-progress.component"}, {"from": "./round-progress.service"}, {"from": "./round-progress.ease"}, {"from": "./round-progress.config"}]}]