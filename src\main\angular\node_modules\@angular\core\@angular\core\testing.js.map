{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/core/testing/index.ts", "../../../../../packages/core/testing/src/testing.ts", "../../../../../packages/core/testing/src/private_export_testing.ts", "../../../../../packages/core/testing/src/before_each.ts", "../../../../../packages/core/testing/src/test_bed.ts", "../../../../../packages/core/testing/src/test_compiler.ts", "../../../../../packages/core/testing/src/async_test_completer.ts", "../../../../../packages/core/testing/src/fake_async.ts", "../../../../../packages/core/testing/src/component_fixture.ts", "../../../../../packages/core/testing/src/async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './async';\nexport * from './component_fixture';\nexport * from './fake_async';\nexport * from './test_bed';\nexport * from './before_each';\nexport * from './metadata_override';\nexport * from './private_export_testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {TestingCompiler as ɵTestingCompiler, TestingCompilerFactory as ɵTestingCompilerFactory} from './test_compiler';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n\nimport {resetFakeAsyncZone} from './fake_async';\nimport {TestBed} from './test_bed';\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n// Reset the test providers and the fake async zone before each test.\nif (_global.beforeEach) {\n  _global.beforeEach(() => {\n    TestBed.resetTestingModule();\n    resetFakeAsyncZone();\n  });\n}\n\n// TODO(juliemr): remove this, only used because we need to export something to have compilation\n// work.\nexport const __core_private_testing_placeholder__ = '';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ApplicationInitStatus, CompilerOptions, Component, Directive, InjectionToken, Injector, ModuleWithComponentFactories, NgModule, NgModuleFactory, NgModuleRef, NgZone, Optional, Pipe, PlatformRef, Provider, ReflectiveInjector, SchemaMetadata, SkipSelf, Type, ɵDepFlags as DepFlags, ɵERROR_COMPONENT_TYPE, ɵNodeFlags as NodeFlags, ɵclearProviderOverrides as clearProviderOverrides, ɵoverrideProvider as overrideProvider, ɵstringify as stringify} from '@angular/core';\n\nimport {AsyncTestCompleter} from './async_test_completer';\nimport {ComponentFixture} from './component_fixture';\nimport {MetadataOverride} from './metadata_override';\nimport {TestingCompiler, TestingCompilerFactory} from './test_compiler';\n\nconst UNDEFINED = new Object();\n\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @experimental\n */\nexport class TestComponentRenderer {\n  insertRootElement(rootElementId: string) {}\n}\n\nlet _nextRootElementId = 0;\n\n/**\n * @experimental\n */\nexport const ComponentFixtureAutoDetect =\n    new InjectionToken<boolean[]>('ComponentFixtureAutoDetect');\n\n/**\n * @experimental\n */\nexport const ComponentFixtureNoNgZone = new InjectionToken<boolean[]>('ComponentFixtureNoNgZone');\n\n/**\n * @experimental\n */\nexport type TestModuleMetadata = {\n  providers?: any[],\n  declarations?: any[],\n  imports?: any[],\n  schemas?: Array<SchemaMetadata|any[]>,\n};\n\n/**\n * @whatItDoes Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n * @description\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @stable\n */\nexport class TestBed implements Injector {\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  static initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]): TestBed {\n    const testBed = getTestBed();\n    testBed.initTestEnvironment(ngModule, platform, aotSummaries);\n    return testBed;\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  static resetTestEnvironment() { getTestBed().resetTestEnvironment(); }\n\n  static resetTestingModule(): typeof TestBed {\n    getTestBed().resetTestingModule();\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default compiler providers and settings\n   * which are defined in test_injector.js\n   */\n  static configureCompiler(config: {providers?: any[]; useJit?: boolean;}): typeof TestBed {\n    getTestBed().configureCompiler(config);\n    return TestBed;\n  }\n\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef: TestModuleMetadata): typeof TestBed {\n    getTestBed().configureTestingModule(moduleDef);\n    return TestBed;\n  }\n\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents(): Promise<any> { return getTestBed().compileComponents(); }\n\n  static overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): typeof TestBed {\n    getTestBed().overrideModule(ngModule, override);\n    return TestBed;\n  }\n\n  static overrideComponent(component: Type<any>, override: MetadataOverride<Component>):\n      typeof TestBed {\n    getTestBed().overrideComponent(component, override);\n    return TestBed;\n  }\n\n  static overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>):\n      typeof TestBed {\n    getTestBed().overrideDirective(directive, override);\n    return TestBed;\n  }\n\n  static overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): typeof TestBed {\n    getTestBed().overridePipe(pipe, override);\n    return TestBed;\n  }\n\n  static overrideTemplate(component: Type<any>, template: string): typeof TestBed {\n    getTestBed().overrideComponent(component, {set: {template, templateUrl: null !}});\n    return TestBed;\n  }\n\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  static overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  static overrideProvider(token: any, provider: {useValue: any;}): void;\n  static overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): typeof TestBed {\n    getTestBed().overrideProvider(token, provider as any);\n    return TestBed;\n  }\n\n  static get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    return getTestBed().get(token, notFoundValue);\n  }\n\n  static createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    return getTestBed().createComponent(component);\n  }\n\n  private _instantiated: boolean = false;\n\n  private _compiler: TestingCompiler = null !;\n  private _moduleRef: NgModuleRef<any> = null !;\n  private _moduleFactory: NgModuleFactory<any> = null !;\n\n  private _compilerOptions: CompilerOptions[] = [];\n\n  private _moduleOverrides: [Type<any>, MetadataOverride<NgModule>][] = [];\n  private _componentOverrides: [Type<any>, MetadataOverride<Component>][] = [];\n  private _directiveOverrides: [Type<any>, MetadataOverride<Directive>][] = [];\n  private _pipeOverrides: [Type<any>, MetadataOverride<Pipe>][] = [];\n\n  private _providers: Provider[] = [];\n  private _declarations: Array<Type<any>|any[]|any> = [];\n  private _imports: Array<Type<any>|any[]|any> = [];\n  private _schemas: Array<SchemaMetadata|any[]> = [];\n  private _activeFixtures: ComponentFixture<any>[] = [];\n\n  private _aotSummaries: () => any[] = () => [];\n\n  platform: PlatformRef = null !;\n\n  ngModule: Type<any>|Type<any>[] = null !;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @experimental\n   */\n  initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef, aotSummaries?: () => any[]) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    this.platform = platform;\n    this.ngModule = ngModule;\n    if (aotSummaries) {\n      this._aotSummaries = aotSummaries;\n    }\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @experimental\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this.platform = null !;\n    this.ngModule = null !;\n    this._aotSummaries = () => [];\n  }\n\n  resetTestingModule() {\n    clearProviderOverrides();\n    this._compiler = null !;\n    this._moduleOverrides = [];\n    this._componentOverrides = [];\n    this._directiveOverrides = [];\n    this._pipeOverrides = [];\n\n    this._moduleRef = null !;\n    this._moduleFactory = null !;\n    this._compilerOptions = [];\n    this._providers = [];\n    this._declarations = [];\n    this._imports = [];\n    this._schemas = [];\n    this._instantiated = false;\n    this._activeFixtures.forEach((fixture) => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        console.error('Error during cleanup of component', fixture.componentInstance);\n      }\n    });\n    this._activeFixtures = [];\n  }\n\n  configureCompiler(config: {providers?: any[], useJit?: boolean}) {\n    this._assertNotInstantiated('TestBed.configureCompiler', 'configure the compiler');\n    this._compilerOptions.push(config);\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata) {\n    this._assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    if (moduleDef.providers) {\n      this._providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.declarations) {\n      this._declarations.push(...moduleDef.declarations);\n    }\n    if (moduleDef.imports) {\n      this._imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.schemas) {\n      this._schemas.push(...moduleDef.schemas);\n    }\n  }\n\n  compileComponents(): Promise<any> {\n    if (this._moduleFactory || this._instantiated) {\n      return Promise.resolve(null);\n    }\n\n    const moduleType = this._createCompilerAndModule();\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType)\n        .then((moduleAndComponentFactories) => {\n          this._moduleFactory = moduleAndComponentFactories.ngModuleFactory;\n        });\n  }\n\n  private _initIfNeeded() {\n    if (this._instantiated) {\n      return;\n    }\n    if (!this._moduleFactory) {\n      try {\n        const moduleType = this._createCompilerAndModule();\n        this._moduleFactory =\n            this._compiler.compileModuleAndAllComponentsSync(moduleType).ngModuleFactory;\n      } catch (e) {\n        if (getComponentType(e)) {\n          throw new Error(\n              `This test module uses the component ${stringify(getComponentType(e))} which is using a \"templateUrl\" or \"styleUrls\", but they were never compiled. ` +\n              `Please call \"TestBed.compileComponents\" before your test.`);\n        } else {\n          throw e;\n        }\n      }\n    }\n    const ngZone = new NgZone({enableLongStackTrace: true});\n    const ngZoneInjector = ReflectiveInjector.resolveAndCreate(\n        [{provide: NgZone, useValue: ngZone}], this.platform.injector);\n    this._moduleRef = this._moduleFactory.create(ngZoneInjector);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core. So casting to any\n    // before accessing it.\n    (this._moduleRef.injector.get(ApplicationInitStatus) as any).runInitializers();\n    this._instantiated = true;\n  }\n\n  private _createCompilerAndModule(): Type<any> {\n    const providers = this._providers.concat([{provide: TestBed, useValue: this}]);\n    const declarations = this._declarations;\n    const imports = [this.ngModule, this._imports];\n    const schemas = this._schemas;\n\n    \n    class DynamicTestModule {\n    static decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{providers, declarations, imports, schemas}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\n    const compilerFactory: TestingCompilerFactory =\n        this.platform.injector.get(TestingCompilerFactory);\n    this._compiler =\n        compilerFactory.createTestingCompiler(this._compilerOptions.concat([{useDebug: true}]));\n    this._compiler.loadAotSummaries(this._aotSummaries);\n    this._moduleOverrides.forEach((entry) => this._compiler.overrideModule(entry[0], entry[1]));\n    this._componentOverrides.forEach(\n        (entry) => this._compiler.overrideComponent(entry[0], entry[1]));\n    this._directiveOverrides.forEach(\n        (entry) => this._compiler.overrideDirective(entry[0], entry[1]));\n    this._pipeOverrides.forEach((entry) => this._compiler.overridePipe(entry[0], entry[1]));\n    return DynamicTestModule;\n  }\n\n  private _assertNotInstantiated(methodName: string, methodDescription: string) {\n    if (this._instantiated) {\n      throw new Error(\n          `Cannot ${methodDescription} when the test module has already been instantiated. ` +\n          `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n\n  get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND) {\n    this._initIfNeeded();\n    if (token === TestBed) {\n      return this;\n    }\n    // Tests can inject things from the ng module and from the compiler,\n    // but the ng module can't inject things from the compiler and vice versa.\n    const result = this._moduleRef.injector.get(token, UNDEFINED);\n    return result === UNDEFINED ? this._compiler.injector.get(token, notFoundValue) : result;\n  }\n\n  execute(tokens: any[], fn: Function, context?: any): any {\n    this._initIfNeeded();\n    const params = tokens.map(t => this.get(t));\n    return fn.apply(context, params);\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this._assertNotInstantiated('overrideModule', 'override module metadata');\n    this._moduleOverrides.push([ngModule, override]);\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this._assertNotInstantiated('overrideComponent', 'override component metadata');\n    this._componentOverrides.push([component, override]);\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this._assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this._directiveOverrides.push([directive, override]);\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this._assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this._pipeOverrides.push([pipe, override]);\n  }\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): void;\n  overrideProvider(token: any, provider: {useValue: any;}): void;\n  overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): void {\n    let flags: NodeFlags = 0;\n    let value: any;\n    if (provider.useFactory) {\n      flags |= NodeFlags.TypeFactoryProvider;\n      value = provider.useFactory;\n    } else {\n      flags |= NodeFlags.TypeValueProvider;\n      value = provider.useValue;\n    }\n    const deps = (provider.deps || []).map((dep) => {\n      let depFlags: DepFlags = DepFlags.None;\n      let depToken: any;\n      if (Array.isArray(dep)) {\n        dep.forEach((entry: any) => {\n          if (entry instanceof Optional) {\n            depFlags |= DepFlags.Optional;\n          } else if (entry instanceof SkipSelf) {\n            depFlags |= DepFlags.SkipSelf;\n          } else {\n            depToken = entry;\n          }\n        });\n      } else {\n        depToken = dep;\n      }\n      return [depFlags, depToken];\n    });\n    overrideProvider({token, flags, deps, value});\n  }\n\n  createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    this._initIfNeeded();\n    const componentFactory = this._compiler.getComponentFactory(component);\n\n    if (!componentFactory) {\n      throw new Error(\n          `Cannot create the component ${stringify(component)} as it was not imported into the testing module!`);\n    }\n\n    const noNgZone = this.get(ComponentFixtureNoNgZone, false);\n    const autoDetect: boolean = this.get(ComponentFixtureAutoDetect, false);\n    const ngZone: NgZone = noNgZone ? null : this.get(NgZone, null);\n    const testComponentRenderer: TestComponentRenderer = this.get(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n\n    const initComponent = () => {\n      const componentRef =\n          componentFactory.create(Injector.NULL, [], `#${rootElId}`, this._moduleRef);\n      return new ComponentFixture<T>(componentRef, ngZone, autoDetect);\n    };\n\n    const fixture = !ngZone ? initComponent() : ngZone.run(initComponent);\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n}\n\nlet _testBed: TestBed = null !;\n\n/**\n * @experimental\n */\nexport function getTestBed() {\n  return _testBed = _testBed || new TestBed();\n}\n\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * Notes:\n * - inject is currently a function because of some Traceur limitation the syntax should\n * eventually\n *   becomes `it('...', @Inject (object: AClass, async: AsyncTestCompleter) => { ... });`\n *\n * @stable\n */\nexport function inject(tokens: any[], fn: Function): () => any {\n  const testBed = getTestBed();\n  if (tokens.indexOf(AsyncTestCompleter) >= 0) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      // Return an async test method that returns a Promise if AsyncTestCompleter is one of\n      // the injected tokens.\n      return testBed.compileComponents().then(() => {\n        const completer: AsyncTestCompleter = testBed.get(AsyncTestCompleter);\n        testBed.execute(tokens, fn, this);\n        return completer.promise;\n      });\n    };\n  } else {\n    // Not using an arrow function to preserve context passed from call site\n    return function() { return testBed.execute(tokens, fn, this); };\n  }\n}\n\n/**\n * @experimental\n */\nexport class InjectSetupWrapper {\n  constructor(private _moduleDef: () => TestModuleMetadata) {}\n\n  private _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      getTestBed().configureTestingModule(moduleDef);\n    }\n  }\n\n  inject(tokens: any[], fn: Function): () => any {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\n\n/**\n * @experimental\n */\nexport function withModule(moduleDef: TestModuleMetadata): InjectSetupWrapper;\nexport function withModule(moduleDef: TestModuleMetadata, fn: Function): () => any;\nexport function withModule(moduleDef: TestModuleMetadata, fn?: Function | null): (() => any)|\n    InjectSetupWrapper {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function() {\n      const testBed = getTestBed();\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\nfunction getComponentType(error: Error): Function {\n  return (error as any)[ɵERROR_COMPONENT_TYPE];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Compiler, CompilerOptions, Component, ComponentFactory, Directive, Injector, NgModule, Pipe, Type} from '@angular/core';\n\nimport {MetadataOverride} from './metadata_override';\n\nfunction unimplemented(): any {\n  throw Error('unimplemented');\n}\n\n/**\n * Special interface to the compiler only used by testing\n *\n * @experimental\n */\nexport class TestingCompiler extends Compiler {\n  get injector(): Injector { throw unimplemented(); }\n  overrideModule(module: Type<any>, overrides: MetadataOverride<NgModule>): void {\n    throw unimplemented();\n  }\n  overrideDirective(directive: Type<any>, overrides: MetadataOverride<Directive>): void {\n    throw unimplemented();\n  }\n  overrideComponent(component: Type<any>, overrides: MetadataOverride<Component>): void {\n    throw unimplemented();\n  }\n  overridePipe(directive: Type<any>, overrides: MetadataOverride<Pipe>): void {\n    throw unimplemented();\n  }\n  /**\n   * Allows to pass the compile summary from AOT compilation to the JIT compiler,\n   * so that it can use the code generated by AOT.\n   */\n  loadAotSummaries(summaries: () => any[]) { throw unimplemented(); };\n\n  /**\n   * Gets the component factory for the given component.\n   * This assumes that the component has been compiled before calling this call using\n   * `compileModuleAndAllComponents*`.\n   */\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> { throw unimplemented(); }\n}\n\n/**\n * A factory for creating a Compiler\n *\n * @experimental\n */\nexport abstract class TestingCompilerFactory {\n  abstract createTestingCompiler(options?: CompilerOptions[]): TestingCompiler;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injectable completer that allows signaling completion of an asynchronous test. Used internally.\n */\nexport class AsyncTestCompleter {\n  private _resolve: (result: any) => void;\n  private _reject: (err: any) => void;\n  private _promise: Promise<any> = new Promise((res, rej) => {\n    this._resolve = res;\n    this._reject = rej;\n  });\n  done(value?: any) { this._resolve(value); }\n\n  fail(error?: any, stackTrace?: string) { this._reject(error); }\n\n  get promise(): Promise<any> { return this._promise; }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst FakeAsyncTestZoneSpec = (Zone as any)['FakeAsyncTestZoneSpec'];\ntype ProxyZoneSpec = {\n  setDelegate(delegateSpec: ZoneSpec): void; getDelegate(): ZoneSpec; resetDelegate(): void;\n};\nconst ProxyZoneSpec: {get(): ProxyZoneSpec; assertPresent: () => ProxyZoneSpec} =\n    (Zone as any)['ProxyZoneSpec'];\n\nlet _fakeAsyncTestZoneSpec: any = null;\n\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nexport function resetFakeAsyncZone() {\n  _fakeAsyncTestZoneSpec = null;\n  ProxyZoneSpec.assertPresent().resetDelegate();\n}\n\nlet _inFakeAsyncCall = false;\n\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @returns {Function} The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nexport function fakeAsync(fn: Function): (...args: any[]) => any {\n  // Not using an arrow function to preserve context passed from call site\n  return function(...args: any[]) {\n    const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n    if (_inFakeAsyncCall) {\n      throw new Error('fakeAsync() calls can not be nested');\n    }\n    _inFakeAsyncCall = true;\n    try {\n      if (!_fakeAsyncTestZoneSpec) {\n        if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n          throw new Error('fakeAsync() calls can not be nested');\n        }\n\n        _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n      }\n\n      let res: any;\n      const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n      proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n      try {\n        res = fn.apply(this, args);\n        flushMicrotasks();\n      } finally {\n        proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n            `periodic timer(s) still in the queue.`);\n      }\n\n      if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n        throw new Error(\n            `${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n      }\n      return res;\n    } finally {\n      _inFakeAsyncCall = false;\n      resetFakeAsyncZone();\n    }\n  };\n}\n\nfunction _getFakeAsyncZoneSpec(): any {\n  if (_fakeAsyncTestZoneSpec == null) {\n    throw new Error('The code should be running in the fakeAsync zone to call this function');\n  }\n  return _fakeAsyncTestZoneSpec;\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * ## Example\n *\n * {@example testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nexport function tick(millis: number = 0): void {\n  _getFakeAsyncZoneSpec().tick(millis);\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns {number} The simulated time elapsed, in millis.\n *\n * @experimental\n */\nexport function flush(maxTurns?: number): number {\n  return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nexport function discardPeriodicTasks(): void {\n  const zoneSpec = _getFakeAsyncZoneSpec();\n  const pendingTimers = zoneSpec.pendingPeriodicTimers;\n  zoneSpec.pendingPeriodicTimers.length = 0;\n}\n\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nexport function flushMicrotasks(): void {\n  _getFakeAsyncZoneSpec().flushMicrotasks();\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectorRef, ComponentRef, DebugElement, ElementRef, NgZone, RendererFactory2, getDebugNode} from '@angular/core';\n\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @stable\n */\nexport class ComponentFixture<T> {\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement: DebugElement;\n\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance: T;\n\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement: any;\n\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef: ElementRef;\n\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef: ChangeDetectorRef;\n\n  private _renderer: RendererFactory2|null|undefined;\n  private _isStable: boolean = true;\n  private _isDestroyed: boolean = false;\n  private _resolve: ((result: any) => void)|null = null;\n  private _promise: Promise<any>|null = null;\n  private _onUnstableSubscription: any /** TODO #9100 */ = null;\n  private _onStableSubscription: any /** TODO #9100 */ = null;\n  private _onMicrotaskEmptySubscription: any /** TODO #9100 */ = null;\n  private _onErrorSubscription: any /** TODO #9100 */ = null;\n\n  constructor(\n      public componentRef: ComponentRef<T>, public ngZone: NgZone|null,\n      private _autoDetect: boolean) {\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = <DebugElement>getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n    this.ngZone = ngZone;\n\n    if (ngZone) {\n      this._onUnstableSubscription =\n          ngZone.onUnstable.subscribe({next: () => { this._isStable = false; }});\n      this._onMicrotaskEmptySubscription = ngZone.onMicrotaskEmpty.subscribe({\n        next: () => {\n          if (this._autoDetect) {\n            // Do a change detection run with checkNoChanges set to true to check\n            // there are no changes on the second run.\n            this.detectChanges(true);\n          }\n        }\n      });\n      this._onStableSubscription = ngZone.onStable.subscribe({\n        next: () => {\n          this._isStable = true;\n          // Check whether there is a pending whenStable() completer to resolve.\n          if (this._promise !== null) {\n            // If so check whether there are no pending macrotasks before resolving.\n            // Do this check in the next tick so that ngZone gets a chance to update the state of\n            // pending macrotasks.\n            scheduleMicroTask(() => {\n              if (!ngZone.hasPendingMacrotasks) {\n                if (this._promise !== null) {\n                  this._resolve !(true);\n                  this._resolve = null;\n                  this._promise = null;\n                }\n              }\n            });\n          }\n        }\n      });\n\n      this._onErrorSubscription =\n          ngZone.onError.subscribe({next: (error: any) => { throw error; }});\n    }\n  }\n\n  private _tick(checkNoChanges: boolean) {\n    this.changeDetectorRef.detectChanges();\n    if (checkNoChanges) {\n      this.checkNoChanges();\n    }\n  }\n\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges: boolean = true): void {\n    if (this.ngZone != null) {\n      // Run the change detection inside the NgZone so that any async tasks as part of the change\n      // detection are captured by the zone and can be waited for in isStable.\n      this.ngZone.run(() => { this._tick(checkNoChanges); });\n    } else {\n      // Running without zone. Just do the change detection.\n      this._tick(checkNoChanges);\n    }\n  }\n\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges(): void { this.changeDetectorRef.checkNoChanges(); }\n\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   */\n  autoDetectChanges(autoDetect: boolean = true) {\n    if (this.ngZone == null) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set');\n    }\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable(): boolean { return this._isStable && !this.ngZone !.hasPendingMacrotasks; }\n\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable(): Promise<any> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    } else if (this._promise !== null) {\n      return this._promise;\n    } else {\n      this._promise = new Promise(res => { this._resolve = res; });\n      return this._promise;\n    }\n  }\n\n\n  private _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer as RendererFactory2 | null;\n  }\n\n  /**\n    * Get a promise that resolves when the ui state is stable following animations.\n    */\n  whenRenderingDone(): Promise<any> {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n\n  /**\n   * Trigger component destruction.\n   */\n  destroy(): void {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      if (this._onUnstableSubscription != null) {\n        this._onUnstableSubscription.unsubscribe();\n        this._onUnstableSubscription = null;\n      }\n      if (this._onStableSubscription != null) {\n        this._onStableSubscription.unsubscribe();\n        this._onStableSubscription = null;\n      }\n      if (this._onMicrotaskEmptySubscription != null) {\n        this._onMicrotaskEmptySubscription.unsubscribe();\n        this._onMicrotaskEmptySubscription = null;\n      }\n      if (this._onErrorSubscription != null) {\n        this._onErrorSubscription.unsubscribe();\n        this._onErrorSubscription = null;\n      }\n      this._isDestroyed = true;\n    }\n  }\n}\n\nfunction scheduleMicroTask(fn: Function) {\n  Zone.current.scheduleMicroTask('scheduleMicrotask', fn);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\ndeclare var global: any;\n\nconst _global = <any>(typeof window === 'undefined' ? global : window);\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', async(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * });\n * ```\n *\n * @stable\n */\nexport function async(fn: Function): (done: any) => any {\n  // If we're running using the Jasmine test framework, adapt to call the 'done'\n  // function when asynchronous activity is finished.\n  if (_global.jasmine) {\n    // Not using an arrow function to preserve context passed from call site\n    return function(done: any) {\n      if (!done) {\n        // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n        // fake it here and assume sync.\n        done = function() {};\n        done.fail = function(e: any) { throw e; };\n      }\n      runInTestZone(fn, this, done, (err: any) => {\n        if (typeof err === 'string') {\n          return done.fail(new Error(<string>err));\n        } else {\n          done.fail(err);\n        }\n      });\n    };\n  }\n  // Otherwise, return a promise which will resolve when asynchronous activity\n  // is finished. This will be correctly consumed by the Mocha framework with\n  // it('...', async(myFn)); or can be used in a custom framework.\n  // Not using an arrow function to preserve context passed from call site\n  return function() {\n    return new Promise<void>((finishCallback, failCallback) => {\n      runInTestZone(fn, this, finishCallback, failCallback);\n    });\n  };\n}\n\nfunction runInTestZone(\n    fn: Function, context: any, finishCallback: Function, failCallback: Function) {\n  const currentZone = Zone.current;\n  const AsyncTestZoneSpec = (Zone as any)['AsyncTestZoneSpec'];\n  if (AsyncTestZoneSpec === undefined) {\n    throw new Error(\n        'AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/async-test.js');\n  }\n  const ProxyZoneSpec = (Zone as any)['ProxyZoneSpec'] as {\n    get(): {setDelegate(spec: ZoneSpec): void; getDelegate(): ZoneSpec;};\n    assertPresent: () => void;\n  };\n  if (ProxyZoneSpec === undefined) {\n    throw new Error(\n        'ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/dist/proxy.js');\n  }\n  const proxyZoneSpec = ProxyZoneSpec.get();\n  ProxyZoneSpec.assertPresent();\n  // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n  // If we do it in ProxyZone then we will get to infinite recursion.\n  const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n  const previousDelegate = proxyZoneSpec.getDelegate();\n  proxyZone.parent.run(() => {\n    const testZoneSpec: ZoneSpec = new AsyncTestZoneSpec(\n        () => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            finishCallback();\n          });\n        },\n        (error: any) => {\n          // Need to restore the original zone.\n          currentZone.run(() => {\n            if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n              // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n              proxyZoneSpec.setDelegate(previousDelegate);\n            }\n            failCallback(error);\n          });\n        },\n        'test');\n    proxyZoneSpec.setDelegate(testZoneSpec);\n  });\n  return Zone.current.runGuarded(fn, context);\n}\n"], "names": ["_global", "stringify", "override<PERSON><PERSON><PERSON>", "clearProviderOverrides"], "mappings": ";;ASAA;;;;;;;AAUA,MAAM,OAAO,IAAS,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;AAmBvE,AAAA,SAAA,KAAA,CAAsB,EAAY,EAAlC;;;IAGE,IAAI,OAAO,CAAC,OAAO,EAAE;;QAEnB,OAAO,UAAS,IAAS,EAA7B;YACM,IAAI,CAAC,IAAI,EAAE;;;gBAGT,IAAI,GAAG,YAAf,GAA4B,CAAC;gBACrB,IAAI,CAAC,IAAI,GAAG,UAAS,CAAM,EAAnC,EAAuC,MAAM,CAAC,CAAC,EAAE,CAAC;aAC3C;YACD,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAQ,KAA7C;gBACQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;oBAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAS,GAAG,CAAC,CAAC,CAAC;iBAC1C;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAChB;aACF,CAAC,CAAC;SACJ,CAAC;KACH;;;;;IAKD,OAAO,YAAT;QACI,OAAO,IAAI,OAAO,CAAO,CAAC,cAAc,EAAE,YAAY,KAA1D;YACM,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;SACvD,CAAC,CAAC;KACJ,CAAC;CACH;AAED,SAAA,aAAA,CACI,EAAY,EAAE,OAAY,EAAE,cAAwB,EAAE,YAAsB,EADhF;IAEE,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;IACjC,MAAM,iBAAiB,GAAI,IAAY,CAAC,mBAAmB,CAAC,CAAC;IAC7D,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,IAAI,KAAK,CACX,kFAAkF;YAClF,4EAA4E,CAAC,CAAC;KACnF;IACD,MAAM,aAAa,GAAI,IAAY,CAAC,eAAe,CAGlD,CAAC;IACF,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,MAAM,IAAI,KAAK,CACX,8EAA8E;YAC9E,uEAAuE,CAAC,CAAC;KAC9E;IACD,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC;IAC1C,aAAa,CAAC,aAAa,EAAE,CAAC;;;IAG9B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;IACrD,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,MAAvB;QACI,MAAM,YAAY,GAAa,IAAI,iBAAiB,CAChD,MADR;;YAGU,WAAW,CAAC,GAAG,CAAC,MAA1B;gBACY,IAAI,aAAa,CAAC,WAAW,EAAE,IAAI,YAAY,EAAE;;oBAE/C,aAAa,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;iBAC7C;gBACD,cAAc,EAAE,CAAC;aAClB,CAAC,CAAC;SACJ,EACD,CAAC,KAAU,KADnB;;YAGU,WAAW,CAAC,GAAG,CAAC,MAA1B;gBACY,IAAI,aAAa,CAAC,WAAW,EAAE,IAAI,YAAY,EAAE;;oBAE/C,aAAa,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;iBAC7C;gBACD,YAAY,CAAC,KAAK,CAAC,CAAC;aACrB,CAAC,CAAC;SACJ,EACD,MAAM,CAAC,CAAC;QACZ,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;KACzC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;CAC7C;;AD/GD;;;;;;;AAQA,AAGA;;;;;AAKA,AAAA,MAAA,gBAAA,CAAA;IAoCE,WAAF,CACa,YAA6B,EAAS,MAAmB,EACxD,WAAoB,EAFlC;QACa,IAAb,CAAA,YAAyB,GAAZ,YAAY,CAAiB;QAAS,IAAnD,CAAA,MAAyD,GAAN,MAAM,CAAa;QACxD,IAAd,CAAA,WAAyB,GAAX,WAAW,CAAS;QAXxB,IAAV,CAAA,SAAmB,GAAY,IAAI,CAAC;QAC1B,IAAV,CAAA,YAAsB,GAAY,KAAK,CAAC;QAC9B,IAAV,CAAA,QAAkB,GAAiC,IAAI,CAAC;QAC9C,IAAV,CAAA,QAAkB,GAAsB,IAAI,CAAC;QACnC,IAAV,CAAA,uBAAiC,GAA0B,IAAI,CAAC;QACtD,IAAV,CAAA,qBAA+B,GAA0B,IAAI,CAAC;QACpD,IAAV,CAAA,6BAAuC,GAA0B,IAAI,CAAC;QAC5D,IAAV,CAAA,oBAA8B,GAA0B,IAAI,CAAC;QAKzD,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,YAAY,GAAiB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QAC9E,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,uBAAuB;gBACxB,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,MAA7C,EAAqD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,EAAC,CAAC,CAAC;YAC3E,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACrE,IAAI,EAAE,MAAd;oBACU,IAAI,IAAI,CAAC,WAAW,EAAE;;;wBAGpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;qBAC1B;iBACF;aACF,CAAC,CAAC;YACH,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACrD,IAAI,EAAE,MAAd;oBACU,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;oBAEtB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;;;;wBAI1B,iBAAiB,CAAC,MAA9B;4BACc,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gCAChC,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;oCAC1B,IAAI,CAAC,QAAU,CAAC,IAAI,CAAC,CAAC;oCACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;oCACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iCACtB;6BACF;yBACF,CAAC,CAAC;qBACJ;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,oBAAoB;gBACrB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,CAAC,KAAU,KAArD,EAA4D,MAAM,KAAK,CAAC,EAAE,EAAC,CAAC,CAAC;SACxE;KACF;IAEO,KAAK,CAAC,cAAuB,EAAvC;QACI,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;QACvC,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;KACF;;;;IAKD,aAAa,CAAC,cAAhB,GAA0C,IAAI,EAA9C;QACI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;;;YAGvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAtB,EAA8B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC;SACxD;aAAM;;YAEL,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;SAC5B;KACF;;;;IAKD,cAAc,GAAhB,EAA2B,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,EAAE;;;;;;IAOnE,iBAAiB,CAAC,UAApB,GAA0C,IAAI,EAA9C;QACI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;;;;IAMD,QAAQ,GAAV,EAAwB,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAQ,CAAC,oBAAoB,CAAC,EAAE;;;;;;;IAQrF,UAAU,GAAZ;QACI,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,GAAG,IAArC,EAA2C,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;KACF;IAGO,YAAY,GAAtB;QACI,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC,SAAoC,CAAC;KAClD;;;;IAKD,iBAAiB,GAAnB;QACI,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACrC,IAAI,QAAQ,IAAI,QAAQ,CAAC,iBAAiB,EAAE;YAC1C,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;SACrC;QACD,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;KAC1B;;;;IAKD,OAAO,GAAT;QACI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,EAAE;gBACxC,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;gBAC3C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,EAAE;gBACtC,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;gBACzC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,EAAE;gBAC9C,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;aAC3C;YACD,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,EAAE;gBACrC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;gBACxC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;aAClC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;KACF;CACF;AAED,SAAA,iBAAA,CAA2B,EAAY,EAAvC;IACE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;CACzD;;ADnND;;;;;;;AASA,MAAM,qBAAqB,GAAI,IAAY,CAAC,uBAAuB,CAAC,CAAC;AAIrE,MAAM,aAAa,GACd,IAAY,CAAC,eAAe,CAAC,CAAC;AAEnC,IAAI,sBAAsB,GAAQ,IAAI,CAAC;;;;;;;AAQvC,AAAA,SAAA,kBAAA,GAAA;IACE,sBAAsB,GAAG,IAAI,CAAC;IAC9B,aAAa,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,CAAC;CAC/C;AAED,IAAI,gBAAgB,GAAG,KAAK,CAAC;;;;;;;;;;;;;;;;;;;AAoB7B,AAAA,SAAA,SAAA,CAA0B,EAAY,EAAtC;;IAEE,OAAO,UAAS,GAAG,IAAW,EAAhC;QACI,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QACpD,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACxD;QACD,gBAAgB,GAAG,IAAI,CAAC;QACxB,IAAI;YACF,IAAI,CAAC,sBAAsB,EAAE;gBAC3B,IAAI,aAAa,CAAC,WAAW,EAAE,YAAY,qBAAqB,EAAE;oBAChE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;iBACxD;gBAED,sBAAsB,GAAG,IAAI,qBAAqB,EAAE,CAAC;aACtD;YAED,IAAI,GAAQ,CAAC;YACb,MAAM,iBAAiB,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;YACtD,aAAa,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;YAClD,IAAI;gBACF,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC3B,eAAe,EAAE,CAAC;aACnB;oBAAS;gBACR,aAAa,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;aAC9C;YAED,IAAI,sBAAsB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3D,MAAM,IAAI,KAAK,CACX,CADZ,EACe,sBAAsB,CAAC,qBAAqB,CAAC,MAAM,CADlE,CAAA,CACqE;oBACzD,CAAZ,qCAAA,CAAmD,CAAC,CAAC;aAC9C;YAED,IAAI,sBAAsB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnD,MAAM,IAAI,KAAK,CACX,CADZ,EACe,sBAAsB,CAAC,aAAa,CAAC,MAAM,CAD1D,6BAAA,CACyF,CAAC,CAAC;aACpF;YACD,OAAO,GAAG,CAAC;SACZ;gBAAS;YACR,gBAAgB,GAAG,KAAK,CAAC;YACzB,kBAAkB,EAAE,CAAC;SACtB;KACF,CAAC;CACH;AAED,SAAA,qBAAA,GAAA;IACE,IAAI,sBAAsB,IAAI,IAAI,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;KAC3F;IACD,OAAO,sBAAsB,CAAC;CAC/B;;;;;;;;;;;;;AAcD,AAAA,SAAA,IAAA,CAAqB,MAArB,GAAsC,CAAC,EAAvC;IACE,qBAAqB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACtC;;;;;;;;;;;AAYD,AAAA,SAAA,KAAA,CAAsB,QAAiB,EAAvC;IACE,OAAO,qBAAqB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;CAChD;;;;;;AAOD,AAAA,SAAA,oBAAA,GAAA;IACE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;IACzC,MAAM,aAAa,GAAG,QAAQ,CAAC,qBAAqB,CAAC;IACrD,QAAQ,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;CAC3C;;;;;;AAOD,AAAA,SAAA,eAAA,GAAA;IACE,qBAAqB,EAAE,CAAC,eAAe,EAAE,CAAC;CAC3C;;ADrJD;;;;;;;;;;;;;;;;IAWA,AALA,MAAA,kBAAA,CAAA;IAKA,WAAA,GAAA;QAGU,IAAV,CAAA,QAAkB,GAAiB,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,KAAxD;YACI,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;SACpB,CAAC,CAAC;KAMJ;IALC,IAAI,CAAC,KAAW,EAAlB,EAAsB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;IAE3C,IAAI,CAAC,KAAW,EAAE,UAAmB,EAAvC,EAA2C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAE/D,IAAI,OAAO,GAAb,EAAgC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;CACtD;;ADvBD;;;;;;;AAQA,AAIA,SAAA,aAAA,GAAA;IACE,MAAM,KAAK,CAAC,eAAe,CAAC,CAAC;CAC9B;;;;;;AAOD,AAAA,MAAA,eAA6B,SAAQ,QAAQ,CAA7C;IACE,IAAI,QAAQ,GAAd,EAA6B,MAAM,aAAa,EAAE,CAAC,EAAE;IACnD,cAAc,CAAC,MAAiB,EAAE,SAAqC,EAAzE;QACI,MAAM,aAAa,EAAE,CAAC;KACvB;IACD,iBAAiB,CAAC,SAAoB,EAAE,SAAsC,EAAhF;QACI,MAAM,aAAa,EAAE,CAAC;KACvB;IACD,iBAAiB,CAAC,SAAoB,EAAE,SAAsC,EAAhF;QACI,MAAM,aAAa,EAAE,CAAC;KACvB;IACD,YAAY,CAAC,SAAoB,EAAE,SAAiC,EAAtE;QACI,MAAM,aAAa,EAAE,CAAC;KACvB;;;;;IAKD,gBAAgB,CAAC,SAAsB,EAAzC,EAA6C,MAAM,aAAa,EAAE,CAAC,EAAE;;;;;;;IAOnE,mBAAmB,CAAI,SAAkB,EAA3C,EAAoE,MAAM,aAAa,EAAE,CAAC,EAAE;CAC3F;;;;;;AAOD,AAAA,MAAA,sBAAA,CAAA;CAEC;;ADxDD;;;;;;;AAQA,AAEA,AACA,AAEA,AAEA,MAAM,SAAA,GAAY,IAAI,MAAA,EAAO,CAAE;;;;;;AAO/B,AAAA,MAAA,qBAAA,CAAA;IACE,iBAAiB,CAAC,aAAqB,EAAzC,GAA6C;CAC5C;AAED,IAAI,kBAAA,GAAqB,CAAA,CAAE;;;;AAK3B,AAAO,MAAM,0BAAA,GACT,IAAI,cAAA,CAAyB,4BAAE,CAA4B,CAAC;;;;AAKhE,AAAO,MAAM,wBAAA,GAA2B,IAAI,cAAA,CAAyB,0BAAE,CAA0B,CAAC;;;;;;;;;;AAqBlG,AAAA,MAAA,OAAA,CAAA;IAAA,WAAA,GAAA;QA+GU,IAAV,CAAA,aAAuB,GAAY,KAAK,CAAC;QAE/B,IAAV,CAAA,SAAmB,GAAoB,IAAM,CAAC;QACpC,IAAV,CAAA,UAAoB,GAAqB,IAAM,CAAC;QACtC,IAAV,CAAA,cAAwB,GAAyB,IAAM,CAAC;QAE9C,IAAV,CAAA,gBAA0B,GAAsB,EAAE,CAAC;QAEzC,IAAV,CAAA,gBAA0B,GAA8C,EAAE,CAAC;QACjE,IAAV,CAAA,mBAA6B,GAA+C,EAAE,CAAC;QACrE,IAAV,CAAA,mBAA6B,GAA+C,EAAE,CAAC;QACrE,IAAV,CAAA,cAAwB,GAA0C,EAAE,CAAC;QAE3D,IAAV,CAAA,UAAoB,GAAe,EAAE,CAAC;QAC5B,IAAV,CAAA,aAAuB,GAA+B,EAAE,CAAC;QAC/C,IAAV,CAAA,QAAkB,GAA+B,EAAE,CAAC;QAC1C,IAAV,CAAA,QAAkB,GAAgC,EAAE,CAAC;QAC3C,IAAV,CAAA,eAAyB,GAA4B,EAAE,CAAC;QAE9C,IAAV,CAAA,aAAuB,GAAgB,MAAM,EAAE,CAAC;QAE9C,IAAF,CAAA,QAAU,GAAgB,IAAM,CAAC;QAE/B,IAAF,CAAA,QAAU,GAA0B,IAAM,CAAC;KA+Q1C;;;;;;;;;;;;;;IAvYC,OAAO,mBAAmB,CACtB,QAA+B,EAAE,QAAqB,EAAE,YAA0B,EADxF;QAEI,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;QAC7B,OAAO,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC9D,OAAO,OAAO,CAAC;KAChB;;;;;;IAOD,OAAO,oBAAoB,GAA7B,EAAkC,UAAU,EAAE,CAAC,oBAAoB,EAAE,CAAC,EAAE;IAEtE,OAAO,kBAAkB,GAA3B;QACI,UAAU,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC;KAChB;;;;;IAMD,OAAO,iBAAiB,CAAC,MAA8C,EAAzE;QACI,UAAU,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACvC,OAAO,OAAO,CAAC;KAChB;;;;;IAMD,OAAO,sBAAsB,CAAC,SAA6B,EAA7D;QACI,UAAU,EAAE,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC;KAChB;;;;;;IAOD,OAAO,iBAAiB,GAA1B,EAA6C,OAAO,UAAU,EAAE,CAAC,iBAAiB,EAAE,CAAC,EAAE;IAErF,OAAO,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAjF;QACI,UAAU,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAtF;QAEI,UAAU,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAtF;QAEI,UAAU,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAvE;QACI,UAAU,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,gBAAgB,CAAC,SAAoB,EAAE,QAAgB,EAAhE;QACI,UAAU,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,EAAC,QAAQ,EAAE,WAAW,EAAE,IAAM,EAAC,EAAC,CAAC,CAAC;QAClF,OAAO,OAAO,CAAC;KAChB;IAWD,OAAO,gBAAgB,CAAC,KAAU,EAAE,QAInC,EAJH;QAKI,UAAU,EAAE,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAe,CAAC,CAAC;QACtD,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,GAAG,CAAC,KAAU,EAAE,aAAzB,GAA8C,QAAQ,CAAC,kBAAkB,EAAzE;QACI,OAAO,UAAU,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;KAC/C;IAED,OAAO,eAAe,CAAI,SAAkB,EAA9C;QACI,OAAO,UAAU,EAAE,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;KAChD;;;;;;;;;;;;;;IAwCD,mBAAmB,CACf,QAA+B,EAAE,QAAqB,EAAE,YAA0B,EADxF;QAEI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;SACjF;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;SACnC;KACF;;;;;;IAOD,oBAAoB,GAAtB;QACI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAM,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAM,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC;KAC/B;IAED,kBAAkB,GAApB;QACIG,uBAAsB,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,IAAM,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,IAAI,CAAC,UAAU,GAAG,IAAM,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAM,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,KAAzC;YACM,IAAI;gBACF,OAAO,CAAC,OAAO,EAAE,CAAC;aACnB;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;aAC/E;SACF,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;KAC3B;IAED,iBAAiB,CAAC,MAA6C,EAAjE;QACI,IAAI,CAAC,sBAAsB,CAAC,2BAA2B,EAAE,wBAAwB,CAAC,CAAC;QACnF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACpC;IAED,sBAAsB,CAAC,SAA6B,EAAtD;QACI,IAAI,CAAC,sBAAsB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC,CAAC;QAC3F,IAAI,SAAS,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;SAC9C;QACD,IAAI,SAAS,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;SACpD;QACD,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;SAC1C;QACD,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;SAC1C;KACF;IAED,iBAAiB,GAAnB;QACI,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,EAAE;YAC7C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9B;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,kCAAkC,CAAC,UAAU,CAAC;aAC/D,IAAI,CAAC,CAAC,2BAA2B,KAA1C;YACU,IAAI,CAAC,cAAc,GAAG,2BAA2B,CAAC,eAAe,CAAC;SACnE,CAAC,CAAC;KACR;IAEO,aAAa,GAAvB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI;gBACF,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACnD,IAAI,CAAC,cAAc;oBACf,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC;aAClF;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE;oBACvB,MAAM,IAAI,KAAK,CACX,CADd,oCAAA,EACqDF,UAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CADnF,8EAAA,CACmK;wBACrJ,CAAd,yDAAA,CAAyE,CAAC,CAAC;iBAClE;qBAAM;oBACL,MAAM,CAAC,CAAC;iBACT;aACF;SACF;QACD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,EAAC,oBAAoB,EAAE,IAAI,EAAC,CAAC,CAAC;QACxD,MAAM,cAAc,GAAG,kBAAkB,CAAC,gBAAgB,CACtD,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;;;QAG5D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAS,CAAC,eAAe,EAAE,CAAC;QAC/E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;IAEO,wBAAwB,GAAlC;QACI,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAG9B,MAAJ,iBAAA,CAAA;;QACW,iBAAX,CAAA,UAAqB,GAA0B;YAC/C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAC,EAAG,EAAE;SACxE,CAAC;;QAEK,iBAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;QAGE,MAAM,eAAe,GACjB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS;YACV,eAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAC5B,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAC5B,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF,OAAO,iBAAiB,CAAC;KAC1B;IAEO,sBAAsB,CAAC,UAAkB,EAAE,iBAAyB,EAA9E;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,IAAI,KAAK,CACX,CADV,OAAA,EACoB,iBAAiB,CADrC,qDAAA,CAC4F;gBAClF,CAAV,gDAAA,EAA6D,UAAU,CAAvE,GAAA,CAA4E,CAAC,CAAC;SACzE;KACF;IAED,GAAG,CAAC,KAAU,EAAE,aAAlB,GAAuC,QAAQ,CAAC,kBAAkB,EAAlE;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;;;QAGD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC9D,OAAO,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,MAAM,CAAC;KAC1F;IAED,OAAO,CAAC,MAAa,EAAE,EAAY,EAAE,OAAa,EAApD;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAA1E;QACI,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;QAC1E,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;KAClD;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAA/E;QACI,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;KACtD;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAA/E;QACI,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;KACtD;IAED,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAhE;QACI,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5C;IAUD,gBAAgB,CAAC,KAAU,EAAE,QAI5B,EAJH;QAKI,IAAI,KAAK,GAAc,CAAC,CAAC;QACzB,IAAI,KAAU,CAAC;QACf,IAAI,QAAQ,CAAC,UAAU,EAAE;YACvB,KAAK,IAAX,IAAA,2BAA6C;YACvC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;SAC7B;aAAM;YACL,KAAK,IAAX,GAAA,yBAA2C;YACrC,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC;SAC3B;QACD,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,KAA/C;YACM,IAAI,QAAQ,GAAlB,CAAA,CAA6C;YACvC,IAAI,QAAa,CAAC;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,GAAG,CAAC,OAAO,CAAC,CAAC,KAAU,KAA/B;oBACU,IAAI,KAAK,YAAY,QAAQ,EAAE;wBAC7B,QAAQ,IAApB,CAAA,gBAA0C;qBAC/B;yBAAM,IAAI,KAAK,YAAY,QAAQ,EAAE;wBACpC,QAAQ,IAApB,CAAA,gBAA0C;qBAC/B;yBAAM;wBACL,QAAQ,GAAG,KAAK,CAAC;qBAClB;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,QAAQ,GAAG,GAAG,CAAC;aAChB;YACD,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC7B,CAAC,CAAC;QACHC,iBAAgB,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC/C;IAED,eAAe,CAAI,SAAkB,EAAvC;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAEvE,IAAI,CAAC,gBAAgB,EAAE;YACrB,MAAM,IAAI,KAAK,CACX,CADV,4BAAA,EACyCD,UAAS,CAAC,SAAS,CAAC,CAD7D,gDAAA,CAC+G,CAAC,CAAC;SAC5G;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAY,IAAI,CAAC,GAAG,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACxE,MAAM,MAAM,GAAW,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,qBAAqB,GAA0B,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACrF,MAAM,QAAQ,GAAG,CAArB,IAAA,EAA4B,kBAAkB,EAAE,CAAhD,CAAkD,CAAC;QAC/C,qBAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAElD,MAAM,aAAa,GAAG,MAA1B;YACM,MAAM,YAAY,GACd,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,CADrD,CAAA,EACyD,QAAQ,CADjE,CACmE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChF,OAAO,IAAI,gBAAgB,CAAI,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SAClE,CAAC;QAEF,MAAM,OAAO,GAAG,CAAC,MAAM,GAAG,aAAa,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;KAChB;CACF;AAED,IANI,QAAA,GAAoB,IAAK,CAAE;;;;AAW/B,AAAA,SAAA,UAAA,GAAA;IACE,OANO,QAAA,GAAW,QAAA,IAAY,IAAI,OAAA,EAAQ,CAAE;CAO7C;;;;;;;;;;;;;;;;;;;;;;;;;AA0BD,AAAA,SAAA,MAAA,CANuB,MAAY,EAAG,EAAI,EAM1C;IACE,MANM,OAAA,GAAU,UAAA,EAAW,CAAE;IAO7B,IAAI,MANC,CAAM,OAAC,CAAO,kBAAC,CAAkB,IAAI,CAAA,EAAG;;QAQ3C,OANO,YAMX;;;YAGM,OANO,OAAA,CAAQ,iBAAC,EAAiB,CAAE,IAAC,CAAI,MAM9C;gBACQ,MANM,SAAA,GAAgC,OAAA,CAAQ,GAAC,CAAG,kBAAC,CAAkB,CAAC;gBAOtE,OAAO,CANC,OAAC,CAAO,MAAC,EAAO,EAAA,EAAI,IAAA,CAAK,CAAC;gBAOlC,OANO,SAAA,CAAU,OAAC,CAAO;aAO1B,CANC,CAAC;SAOJ,CANC;KAOH;SANM;;QAQL,OANO,YAMX,EANwB,OAAO,OAAA,CAAQ,OAAC,CAAO,MAAC,EAAO,EAAA,EAAI,IAAA,CAAK,CAAC,EAAC,CAAE;KAOjE;CACF;;;;AAKD,AAAA,MAAA,kBAAA,CAAA;IACE,WAAF,CAAsB,UAAoC,EAA1D;QAAsB,IAAtB,CAAA,UAAgC,GAAV,UAAU,CAA0B;KAAI;IAEpD,UAAU,GAApB;QACI,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACpC,IAAI,SAAS,EAAE;YACb,UAAU,EAAE,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;SAChD;KACF;IAED,MAAM,CAAC,MAAa,EAAE,EAAY,EAApC;QACI,MAAM,IAAI,GAAG,IAAI,CAAC;;QAElB,OAAO,YAAX;YACM,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtC,CAAC;KACH;CACF;AAOD,AAAA,SAAA,UAAA,CAN2B,SAAW,EAAoB,EAAgB,EAM1E;IAEE,IAAI,EANC,EAAG;;QAQN,OANO,YAMX;YACM,MANM,OAAA,GAAU,UAAA,EAAW,CAAE;YAO7B,IAAI,SANC,EAAU;gBAOb,OAAO,CANC,sBAAC,CAAsB,SAAC,CAAS,CAAC;aAO3C;YACD,OANO,EAAA,CAAG,KAAC,CAAK,IAAC,CAAI,CAAC;SAOvB,CANC;KAOH;IACD,OANO,IAAI,kBAAA,CAAmB,MAAM,SAAA,CAAU,CAAC;CAOhD;AAED,SAAA,gBAAA,CAN0B,KAAO,EAMjC;IACE,OANO,KAAc,CAAC,qBAAC,CAAqB,CAAC;CAO9C;;ADljBD;;;;;;;;;;;;AAcA,AACA,AAIA,MAAMD,SAAO,IAAS,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;;AAGvE,IAAIA,SAAO,CAAC,UAAU,EAAE;IACtBA,SAAO,CAAC,UAAU,CAAC,MAArB;QACI,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC7B,kBAAkB,EAAE,CAAC;KACtB,CAAC,CAAC;CACJ;;;AAID,AAAO,MAAM,oCAAoC,GAAG,EAAE,CAAC;;AD/BvD;;;;;;GAMG,AAEH,AAAuH;;ADRvH;;;;;;;;;;;GAYG,AAEH,AACA,AACA,AACA,AACA,AAEA,AAAyC;;ADpBzC;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}