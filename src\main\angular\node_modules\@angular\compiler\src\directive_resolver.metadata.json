[{"__symbolic": "module", "version": 3, "metadata": {"DirectiveResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isDirective": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "_mergeWithPropertyMetadata": [{"__symbolic": "method"}], "_extractPublicName": [{"__symbolic": "method"}], "_dedupeBindings": [{"__symbolic": "method"}], "_merge": [{"__symbolic": "method"}]}}, "findLast": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"DirectiveResolver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./compile_reflector", "name": "CompileReflector"}]}], "isDirective": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "_mergeWithPropertyMetadata": [{"__symbolic": "method"}], "_extractPublicName": [{"__symbolic": "method"}], "_dedupeBindings": [{"__symbolic": "method"}], "_merge": [{"__symbolic": "method"}]}}, "findLast": {"__symbolic": "function"}}}]