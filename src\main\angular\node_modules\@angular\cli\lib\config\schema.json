{"$schema": "http://json-schema.org/schema", "id": "https://github.com/angular/angular-cli/blob/master/packages/@angular/cli/lib/config/schema.json#CliConfig", "title": "Angular CLI Config Sc<PERSON>a", "type": "object", "properties": {"$schema": {"type": "string"}, "project": {"description": "The global configuration of the project.", "type": "object", "properties": {"name": {"description": "The name of the project.", "type": "string"}, "ejected": {"description": "Whether or not this project was ejected.", "type": "boolean", "default": false}}, "additionalProperties": false}, "apps": {"description": "Properties of the different applications in this project.", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the app."}, "root": {"type": "string", "description": "The root directory of the app."}, "outDir": {"type": "string", "default": "dist/", "description": "The output directory for build results."}, "assets": {"type": "array", "description": "List of application assets.", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"glob": {"type": "string", "default": "", "description": "The pattern to match."}, "input": {"type": "string", "default": "", "description": "The dir to search within."}, "output": {"type": "string", "default": "", "description": "The output path (relative to the outDir)."}}, "additionalProperties": false}]}, "default": []}, "deployUrl": {"type": "string", "description": "URL where files will be deployed."}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "index": {"type": "string", "default": "index.html", "description": "The name of the start HTML file."}, "main": {"type": "string", "description": "The name of the main entry-point file."}, "polyfills": {"type": "string", "description": "The name of the polyfills file."}, "test": {"type": "string", "description": "The name of the test entry-point file."}, "tsconfig": {"type": "string", "default": "tsconfig.app.json", "description": "The name of the TypeScript configuration file."}, "testTsconfig": {"type": "string", "description": "The name of the TypeScript configuration file for unit tests."}, "prefix": {"type": "string", "description": "The prefix to apply to generated selectors."}, "serviceWorker": {"description": "Experimental support for a service worker from @angular/service-worker.", "type": "boolean", "default": false}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"input": {"type": "string"}}, "additionalProperties": true}]}, "additionalProperties": false}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to project root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"input": {"type": "string"}}, "additionalProperties": true, "required": ["input"]}]}, "additionalProperties": false}, "environmentSource": {"description": "Source file for environment config.", "type": "string"}, "environments": {"description": "Name and corresponding file for environment config.", "type": "object", "additionalProperties": true}}, "additionalProperties": false}, "additionalProperties": false}, "e2e": {"type": "object", "description": "Configuration for end-to-end tests.", "properties": {"protractor": {"type": "object", "properties": {"config": {"description": "Path to the config file.", "type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "lint": {"description": "Properties to be passed to TSLint.", "type": "array", "items": {"type": "object", "properties": {"files": {"description": "File glob(s) to lint.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "default": []}, "project": {"description": "Location of the tsconfig.json project file. Will also use as files to lint if 'files' property not present.", "type": "string"}, "tslintConfig": {"description": "Location of the tslint.json configuration.", "type": "string"}, "exclude": {"description": "File glob(s) to ignore.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "default": []}}, "required": ["project"], "additionalProperties": false}}, "test": {"description": "Configuration for unit tests.", "type": "object", "properties": {"karma": {"type": "object", "properties": {"config": {"description": "Path to the karma config file.", "type": "string"}}, "additionalProperties": false}, "codeCoverage": {"type": "object", "properties": {"exclude": {"description": "Globs to exclude from code coverage.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}}, "additionalProperties": false}, "defaults": {"description": "Specify the default values for generating.", "type": "object", "properties": {"styleExt": {"description": "The file extension to be used for style files.", "type": "string"}, "poll": {"description": "How often to check for file updates.", "type": "number"}, "lintFix": {"description": "Use lint to fix files after generation", "type": "boolean", "default": false}, "class": {"description": "Options for generating a class.", "type": "object", "properties": {"spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": false}}}, "component": {"description": "Options for generating a component.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": false}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": true}, "inlineStyle": {"description": "Specifies if the style will be in the ts file.", "type": "boolean", "default": false}, "inlineTemplate": {"description": "Specifies if the template will be in the ts file.", "type": "boolean", "default": false}, "viewEncapsulation": {"description": "Specifies the view encapsulation strategy.", "enum": ["Emulated", "Native", "None"], "type": "string"}, "changeDetection": {"description": "Specifies the change detection strategy.", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "type": "string"}}}, "directive": {"description": "Options for generating a directive.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": true}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": true}}}, "guard": {"description": "Options for generating a guard.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": true}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": true}}}, "interface": {"description": "Options for generating an interface.", "type": "object", "properties": {"prefix": {"description": "Prefix to apply to interface names. (i.e. I)", "type": "string", "default": ""}}}, "module": {"description": "Options for generating a module.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": false}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": false}}}, "pipe": {"description": "Options for generating a pipe.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": true}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": true}}}, "service": {"description": "Options for generating a service.", "type": "object", "properties": {"flat": {"description": "Flag to indicate if a dir is created.", "type": "boolean", "default": true}, "spec": {"description": "Specifies if a spec file is generated.", "type": "boolean", "default": true}}}, "serve": {"description": "Properties to be passed to the serve command.", "type": "object", "properties": {"port": {"description": "The port the application will be served on.", "type": "number", "default": 4200}, "host": {"description": "The host the application will be served on.", "type": "string", "default": "localhost"}, "ssl": {"description": "Enables ssl for the application.", "type": "boolean", "default": false}, "sslKey": {"description": "The ssl key used by the server.", "type": "string", "default": "ssl/server.key"}, "sslCert": {"description": "The ssl certificate used by the server.", "type": "string", "default": "ssl/server.crt"}}}}, "additionalProperties": false}, "packageManager": {"description": "Specify which package manager tool to use.", "enum": ["npm", "cnpm", "yarn", "default"], "default": "default", "type": "string"}, "warnings": {"description": "Allow people to disable console warnings.", "type": "object", "properties": {"nodeDeprecation": {"description": "Show a warning when the node version is incompatible.", "type": "boolean", "default": true}, "packageDeprecation": {"description": "Show a warning when the user installed angular-cli.", "type": "boolean", "default": true}, "versionMismatch": {"description": "Show a warning when the global version is newer than the local one.", "type": "boolean", "default": true}}}}, "additionalProperties": false}