[{"__symbolic": "module", "version": 3, "metadata": {"Message": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 22, "character": 49}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 35}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "MessageSpan": {"__symbolic": "interface"}, "Node": {"__symbolic": "interface"}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Container": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Icu": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 69, "character": 68}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "TagPlaceholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 77, "character": 40}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Placeholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "IcuPlaceholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Visitor": {"__symbolic": "interface"}, "CloneVisitor": {"__symbolic": "class", "members": {"visitText": [{"__symbolic": "method"}], "visitContainer": [{"__symbolic": "method"}], "visitIcu": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}]}}, "RecurseVisitor": {"__symbolic": "class", "members": {"visitText": [{"__symbolic": "method"}], "visitContainer": [{"__symbolic": "method"}], "visitIcu": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Message": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 22, "character": 49}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 35}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "MessageSpan": {"__symbolic": "interface"}, "Node": {"__symbolic": "interface"}, "Text": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Container": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Icu": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 69, "character": 68}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "TagPlaceholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 77, "character": 40}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Placeholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "IcuPlaceholder": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "../parse_util", "name": "ParseSourceSpan"}]}], "visit": [{"__symbolic": "method"}]}}, "Visitor": {"__symbolic": "interface"}, "CloneVisitor": {"__symbolic": "class", "members": {"visitText": [{"__symbolic": "method"}], "visitContainer": [{"__symbolic": "method"}], "visitIcu": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}]}}, "RecurseVisitor": {"__symbolic": "class", "members": {"visitText": [{"__symbolic": "method"}], "visitContainer": [{"__symbolic": "method"}], "visitIcu": [{"__symbolic": "method"}], "visitTagPlaceholder": [{"__symbolic": "method"}], "visitPlaceholder": [{"__symbolic": "method"}], "visitIcuPlaceholder": [{"__symbolic": "method"}]}}}}]