{"version": 3, "file": "router.umd.js", "sources": ["../../../../packages/router/src/version.ts", "../../../../packages/router/src/router_module.ts", "../../../../packages/router/src/router_preloader.ts", "../../../../packages/router/src/directives/router_outlet.ts", "../../../../packages/router/src/router_outlet_context.ts", "../../../../packages/router/src/directives/router_link_active.ts", "../../../../packages/router/src/directives/router_link.ts", "../../../../packages/router/src/router.ts", "../../../../packages/router/src/url_handling_strategy.ts", "../../../../packages/router/src/router_config_loader.ts", "../../../../packages/router/src/route_reuse_strategy.ts", "../../../../packages/router/src/recognize.ts", "../../../../packages/router/src/create_url_tree.ts", "../../../../packages/router/src/create_router_state.ts", "../../../../packages/router/src/router_state.ts", "../../../../packages/router/src/utils/tree.ts", "../../../../packages/router/src/apply_redirects.ts", "../../../../packages/router/src/url_tree.ts", "../../../../packages/router/src/utils/collection.ts", "../../../../packages/router/src/config.ts", "../../../../packages/router/src/shared.ts", "../../../../packages/router/src/events.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {APP_BASE_HREF, HashLocationStrategy, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation} from '@angular/common';\nimport {ANALYZE_FOR_ENTRY_COMPONENTS, APP_BOOTSTRAP_LISTENER, APP_INITIALIZER, ApplicationRef, Compiler, ComponentRef, Inject, Injectable, InjectionToken, Injector, ModuleWithProviders, NgModule, NgModuleFactoryLoader, NgProbeToken, Optional, Provider, SkipSelf, SystemJsNgModuleLoader} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {Subject} from 'rxjs/Subject';\nimport {of } from 'rxjs/observable/of';\n\nimport {Route, Routes} from './config';\nimport {RouterLink, RouterLinkWithHref} from './directives/router_link';\nimport {RouterLinkActive} from './directives/router_link_active';\nimport {RouterOutlet} from './directives/router_outlet';\nimport {RouteReuseStrategy} from './route_reuse_strategy';\nimport {ErrorHandler, Router} from './router';\nimport {ROUTES} from './router_config_loader';\nimport {ChildrenOutletContexts} from './router_outlet_context';\nimport {NoPreloading, PreloadAllModules, PreloadingStrategy, RouterPreloader} from './router_preloader';\nimport {ActivatedRoute} from './router_state';\nimport {UrlHandlingStrategy} from './url_handling_strategy';\nimport {DefaultUrlSerializer, UrlSerializer} from './url_tree';\nimport {flatten} from './utils/collection';\n/**\n * \\@whatItDoes Contains a list of directives\n * \\@stable\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkWithHref, RouterLinkActive];\n/**\n * \\@whatItDoes Is used in DI to configure the router.\n * \\@stable\n */\nexport const ROUTER_CONFIGURATION = new InjectionToken<ExtraOptions>('ROUTER_CONFIGURATION');\n/**\n * \\@docsNotRequired\n */\nexport const ROUTER_FORROOT_GUARD = new InjectionToken<void>('ROUTER_FORROOT_GUARD');\n\nexport const /** @type {?} */ ROUTER_PROVIDERS: Provider[] = [\n  Location,\n  {provide: UrlSerializer, useClass: DefaultUrlSerializer},\n  {\n    provide: Router,\n    useFactory: setupRouter,\n    deps: [\n      ApplicationRef, UrlSerializer, ChildrenOutletContexts, Location, Injector,\n      NgModuleFactoryLoader, Compiler, ROUTES, ROUTER_CONFIGURATION,\n      [UrlHandlingStrategy, new Optional()], [RouteReuseStrategy, new Optional()]\n    ]\n  },\n  ChildrenOutletContexts,\n  {provide: ActivatedRoute, useFactory: rootRoute, deps: [Router]},\n  {provide: NgModuleFactoryLoader, useClass: SystemJsNgModuleLoader},\n  RouterPreloader,\n  NoPreloading,\n  PreloadAllModules,\n  {provide: ROUTER_CONFIGURATION, useValue: {enableTracing: false}},\n];\n/**\n * @return {?}\n */\nexport function routerNgProbeToken() {\n  return new NgProbeToken('Router', Router);\n}\n/**\n * \\@whatItDoes Adds router directives and providers.\n * \n * \\@howToUse \n * \n * RouterModule can be imported multiple times: once per lazily-loaded bundle.\n * Since the router deals with a global shared resource--location, we cannot have\n * more than one router service active.\n * \n * That is why there are two ways to create the module: `RouterModule.forRoot` and\n * `RouterModule.forChild`.\n * \n * * `forRoot` creates a module that contains all the directives, the given routes, and the router\n *   service itself.\n * * `forChild` creates a module that contains all the directives and the given routes, but does not\n *   include the router service.\n * \n * When registered at the root, the module should be used as follows\n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forRoot(ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * For submodules and lazy loaded submodules the module should be used as follows:\n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forChild(ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * \\@description \n * \n * Managing state transitions is one of the hardest parts of building applications. This is\n * especially true on the web, where you also need to ensure that the state is reflected in the URL.\n * In addition, we often want to split applications into multiple bundles and load them on demand.\n * Doing this transparently is not trivial.\n * \n * The Angular router solves these problems. Using the router, you can declaratively specify\n * application states, manage state transitions while taking care of the URL, and load bundles on\n * demand.\n * \n * [Read this developer guide](https://angular.io/docs/ts/latest/guide/router.html) to get an\n * overview of how the router should be used.\n * \n * \\@stable\n */\nexport class RouterModule {\n/**\n * @param {?} guard\n * @param {?} router\n */\nconstructor(  guard: any,  router: Router) {}\n/**\n * Creates a module with all the router providers and directives. It also optionally sets up an\n * application listener to perform an initial navigation.\n * \n * Options:\n * * `enableTracing` makes the router log all its internal events to the console.\n * * `useHash` enables the location strategy that uses the URL fragment instead of the history\n * API.\n * * `initialNavigation` disables the initial navigation.\n * * `errorHandler` provides a custom error handler.\n * @param {?} routes\n * @param {?=} config\n * @return {?}\n */\nstatic forRoot(routes: Routes, config?: ExtraOptions): ModuleWithProviders {\n    return {\n      ngModule: RouterModule,\n      providers: [\n        ROUTER_PROVIDERS,\n        provideRoutes(routes),\n        {\n          provide: ROUTER_FORROOT_GUARD,\n          useFactory: provideForRootGuard,\n          deps: [[Router, new Optional(), new SkipSelf()]]\n        },\n        {provide: ROUTER_CONFIGURATION, useValue: config ? config : {}},\n        {\n          provide: LocationStrategy,\n          useFactory: provideLocationStrategy,\n          deps: [\n            PlatformLocation, [new Inject(APP_BASE_HREF), new Optional()], ROUTER_CONFIGURATION\n          ]\n        },\n        {\n          provide: PreloadingStrategy,\n          useExisting: config && config.preloadingStrategy ? config.preloadingStrategy :\n                                                             NoPreloading\n        },\n        {provide: NgProbeToken, multi: true, useFactory: routerNgProbeToken},\n        provideRouterInitializer(),\n      ],\n    };\n  }\n/**\n * Creates a module with all the router directives and a provider registering routes.\n * @param {?} routes\n * @return {?}\n */\nstatic forChild(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{declarations: ROUTER_DIRECTIVES, exports: ROUTER_DIRECTIVES}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [ROUTER_FORROOT_GUARD, ] }, ]},\n{type: Router, decorators: [{ type: Optional }, ]},\n];\n}\n\nfunction RouterModule_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterModule.ctorParameters;\n}\n\n/**\n * @param {?} platformLocationStrategy\n * @param {?} baseHref\n * @param {?=} options\n * @return {?}\n */\nexport function provideLocationStrategy(\n    platformLocationStrategy: PlatformLocation, baseHref: string, options: ExtraOptions = {}) {\n  return options.useHash ? new HashLocationStrategy(platformLocationStrategy, baseHref) :\n                           new PathLocationStrategy(platformLocationStrategy, baseHref);\n}\n/**\n * @param {?} router\n * @return {?}\n */\nexport function provideForRootGuard(router: Router): any {\n  if (router) {\n    throw new Error(\n        `RouterModule.forRoot() called twice. Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n/**\n * \\@whatItDoes Registers routes.\n * \n * \\@howToUse \n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forChild(ROUTES)],\n *   providers: [provideRoutes(EXTRA_ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * \\@stable\n * @param {?} routes\n * @return {?}\n */\nexport function provideRoutes(routes: Routes): any {\n  return [\n    {provide: ANALYZE_FOR_ENTRY_COMPONENTS, multi: true, useValue: routes},\n    {provide: ROUTES, multi: true, useValue: routes},\n  ];\n}\n\n/**\n * @whatItDoes Represents an option to configure when the initial navigation is performed.\n *\n * @description\n * * 'enabled' - the initial navigation starts before the root component is created.\n * The bootstrap is blocked until the initial navigation is complete.\n * * 'disabled' - the initial navigation is not performed. The location listener is set up before\n * the root component gets created.\n * * 'legacy_enabled'- the initial navigation starts after the root component has been created.\n * The bootstrap is not blocked until the initial navigation is complete. @deprecated\n * * 'legacy_disabled'- the initial navigation is not performed. The location listener is set up\n * after @deprecated\n * the root component gets created.\n * * `true` - same as 'legacy_enabled'. @deprecated since v4\n * * `false` - same as 'legacy_disabled'. @deprecated since v4\n *\n * The 'enabled' option should be used for applications unless there is a reason to have\n * more control over when the router starts its initial navigation due to some complex\n * initialization logic. In this case, 'disabled' should be used.\n *\n * The 'legacy_enabled' and 'legacy_disabled' should not be used for new applications.\n *\n * @experimental\n */\nexport type InitialNavigation =\n    true | false | 'enabled' | 'disabled' | 'legacy_enabled' | 'legacy_disabled';\n\n/**\n * @whatItDoes Represents options to configure the router.\n *\n * @stable\n */\nexport interface ExtraOptions {\n  /**\n   * Makes the router log all its internal events to the console.\n   */\n  enableTracing?: boolean;\n\n  /**\n   * Enables the location strategy that uses the URL fragment instead of the history API.\n   */\n  useHash?: boolean;\n\n  /**\n   * Disables the initial navigation.\n   */\n  initialNavigation?: InitialNavigation;\n\n  /**\n   * A custom error handler.\n   */\n  errorHandler?: ErrorHandler;\n\n  /**\n   * Configures a preloading strategy. See {@link PreloadAllModules}.\n   */\n  preloadingStrategy?: any;\n}\n/**\n * @param {?} ref\n * @param {?} urlSerializer\n * @param {?} contexts\n * @param {?} location\n * @param {?} injector\n * @param {?} loader\n * @param {?} compiler\n * @param {?} config\n * @param {?=} opts\n * @param {?=} urlHandlingStrategy\n * @param {?=} routeReuseStrategy\n * @return {?}\n */\nexport function setupRouter(\n    ref: ApplicationRef, urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts,\n    location: Location, injector: Injector, loader: NgModuleFactoryLoader, compiler: Compiler,\n    config: Route[][], opts: ExtraOptions = {}, urlHandlingStrategy?: UrlHandlingStrategy,\n    routeReuseStrategy?: RouteReuseStrategy) {\n  const /** @type {?} */ router = new Router(\n      null, urlSerializer, contexts, location, injector, loader, compiler, flatten(config));\n\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n\n  if (routeReuseStrategy) {\n    router.routeReuseStrategy = routeReuseStrategy;\n  }\n\n  if (opts.errorHandler) {\n    router.errorHandler = opts.errorHandler;\n  }\n\n  if (opts.enableTracing) {\n    const /** @type {?} */ dom = getDOM();\n    router.events.subscribe(e => {\n      dom.logGroup(`Router Event: ${( /** @type {?} */((<any>e.constructor))).name}`);\n      dom.log(e.toString());\n      dom.log(e);\n      dom.logGroupEnd();\n    });\n  }\n\n  return router;\n}\n/**\n * @param {?} router\n * @return {?}\n */\nexport function rootRoute(router: Router): ActivatedRoute {\n  return router.routerState.root;\n}\n/**\n * To initialize the router properly we need to do in two steps:\n * \n * We need to start the navigation in a APP_INITIALIZER to block the bootstrap if\n * a resolver or a guards executes asynchronously. Second, we need to actually run\n * activation in a BOOTSTRAP_LISTENER. We utilize the afterPreactivation\n * hook provided by the router to do that.\n * \n * The router navigation starts, reaches the point when preactivation is done, and then\n * pauses. It waits for the hook to be resolved. We then resolve it only in a bootstrap listener.\n */\nexport class RouterInitializer {\nprivate initNavigation: boolean = false;\nprivate resultOfPreactivationDone = new Subject<void>();\n/**\n * @param {?} injector\n */\nconstructor(private injector: Injector) {}\n/**\n * @return {?}\n */\nappInitializer(): Promise<any> {\n    const /** @type {?} */ p: Promise<any> = this.injector.get(LOCATION_INITIALIZED, Promise.resolve(null));\n    return p.then(() => {\n      let /** @type {?} */ resolve: Function = /** @type {?} */(( null));\n      const /** @type {?} */ res = new Promise(r => resolve = r);\n      const /** @type {?} */ router = this.injector.get(Router);\n      const /** @type {?} */ opts = this.injector.get(ROUTER_CONFIGURATION);\n\n      if (this.isLegacyDisabled(opts) || this.isLegacyEnabled(opts)) {\n        resolve(true);\n\n      } else if (opts.initialNavigation === 'disabled') {\n        router.setUpLocationChangeListener();\n        resolve(true);\n\n      } else if (opts.initialNavigation === 'enabled') {\n        router.hooks.afterPreactivation = () => {\n          // only the initial navigation should be delayed\n          if (!this.initNavigation) {\n            this.initNavigation = true;\n            resolve(true);\n            return this.resultOfPreactivationDone;\n\n            // subsequent navigations should not be delayed\n          } else {\n            return /** @type {?} */(( of (null) as any));\n          }\n        };\n        router.initialNavigation();\n\n      } else {\n        throw new Error(`Invalid initialNavigation options: '${opts.initialNavigation}'`);\n      }\n\n      return res;\n    });\n  }\n/**\n * @param {?} bootstrappedComponentRef\n * @return {?}\n */\nbootstrapListener(bootstrappedComponentRef: ComponentRef<any>): void {\n    const /** @type {?} */ opts = this.injector.get(ROUTER_CONFIGURATION);\n    const /** @type {?} */ preloader = this.injector.get(RouterPreloader);\n    const /** @type {?} */ router = this.injector.get(Router);\n    const /** @type {?} */ ref = this.injector.get(ApplicationRef);\n\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n\n    if (this.isLegacyEnabled(opts)) {\n      router.initialNavigation();\n    } else if (this.isLegacyDisabled(opts)) {\n      router.setUpLocationChangeListener();\n    }\n\n    preloader.setUpPreloading();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    this.resultOfPreactivationDone.next( /** @type {?} */((null)));\n    this.resultOfPreactivationDone.complete();\n  }\n/**\n * @param {?} opts\n * @return {?}\n */\nprivate isLegacyEnabled(opts: ExtraOptions): boolean {\n    return opts.initialNavigation === 'legacy_enabled' || opts.initialNavigation === true ||\n        opts.initialNavigation === undefined;\n  }\n/**\n * @param {?} opts\n * @return {?}\n */\nprivate isLegacyDisabled(opts: ExtraOptions): boolean {\n    return opts.initialNavigation === 'legacy_disabled' || opts.initialNavigation === false;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n];\n}\n\nfunction RouterInitializer_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterInitializer.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterInitializer.ctorParameters;\n/** @type {?} */\nRouterInitializer.prototype.initNavigation;\n/** @type {?} */\nRouterInitializer.prototype.resultOfPreactivationDone;\n/** @type {?} */\nRouterInitializer.prototype.injector;\n}\n\n/**\n * @param {?} r\n * @return {?}\n */\nexport function getAppInitializer(r: RouterInitializer) {\n  return r.appInitializer.bind(r);\n}\n/**\n * @param {?} r\n * @return {?}\n */\nexport function getBootstrapListener(r: RouterInitializer) {\n  return r.bootstrapListener.bind(r);\n}\n/**\n * A token for the router initializer that will be called after the app is bootstrapped.\n * \n * \\@experimental\n */\nexport const ROUTER_INITIALIZER =\n    new InjectionToken<(compRef: ComponentRef<any>) => void>('Router Initializer');\n/**\n * @return {?}\n */\nexport function provideRouterInitializer() {\n  return [\n    RouterInitializer,\n    {\n      provide: APP_INITIALIZER,\n      multi: true,\n      useFactory: getAppInitializer,\n      deps: [RouterInitializer]\n    },\n    {provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener, deps: [RouterInitializer]},\n    {provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER},\n  ];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n*@license\n*Copyright Google Inc. All Rights Reserved.\n*\n*Use of this source code is governed by an MIT-style license that can be\n*found in the LICENSE file at https://angular.io/license\n*/\n\n\nimport {Compiler, Injectable, Injector, NgModuleFactoryLoader, NgModuleRef, OnDestroy} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Subscription} from 'rxjs/Subscription';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {_catch} from 'rxjs/operator/catch';\nimport {concatMap} from 'rxjs/operator/concatMap';\nimport {filter} from 'rxjs/operator/filter';\nimport {mergeAll} from 'rxjs/operator/mergeAll';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {LoadedRouterConfig, Route, Routes} from './config';\nimport {Event, NavigationEnd, RouteConfigLoadEnd, RouteConfigLoadStart} from './events';\nimport {Router} from './router';\nimport {RouterConfigLoader} from './router_config_loader';\n/**\n * \\@whatItDoes Provides a preloading strategy.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class PreloadingStrategy {\n/**\n * @abstract\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>) {}\n}\n/**\n * \\@whatItDoes Provides a preloading strategy that preloads all modules as quickly as possible.\n * \n * \\@howToUse \n * \n * ```\n * RouteModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n * \n * \\@experimental\n */\nexport class PreloadAllModules implements PreloadingStrategy {\n/**\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>): Observable<any> {\n    return _catch.call(fn(), () => of (null));\n  }\n}\n/**\n * \\@whatItDoes Provides a preloading strategy that does not preload any modules.\n * \n * \\@description \n * \n * This strategy is enabled by default.\n * \n * \\@experimental\n */\nexport class NoPreloading implements PreloadingStrategy {\n/**\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>): Observable<any> { return of (null); }\n}\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n * \n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n * \n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n * \n * \\@stable\n */\nexport class RouterPreloader implements OnDestroy {\nprivate loader: RouterConfigLoader;\nprivate subscription: Subscription;\n/**\n * @param {?} router\n * @param {?} moduleLoader\n * @param {?} compiler\n * @param {?} injector\n * @param {?} preloadingStrategy\n */\nconstructor(\nprivate router: Router, moduleLoader: NgModuleFactoryLoader, compiler: Compiler,\nprivate injector: Injector,\nprivate preloadingStrategy: PreloadingStrategy) {\n    const onStartLoad = (r: Route) => router.triggerEvent(new RouteConfigLoadStart(r));\n    const onEndLoad = (r: Route) => router.triggerEvent(new RouteConfigLoadEnd(r));\n\n    this.loader = new RouterConfigLoader(moduleLoader, compiler, onStartLoad, onEndLoad);\n  };\n/**\n * @return {?}\n */\nsetUpPreloading(): void {\n    const /** @type {?} */ navigations$ = filter.call(this.router.events, (e: Event) => e instanceof NavigationEnd);\n    this.subscription = concatMap.call(navigations$, () => this.preload()).subscribe(() => {});\n  }\n/**\n * @return {?}\n */\npreload(): Observable<any> {\n    const /** @type {?} */ ngModule = this.injector.get(NgModuleRef);\n    return this.processRoutes(ngModule, this.router.config);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.subscription.unsubscribe(); }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @return {?}\n */\nprivate processRoutes(ngModule: NgModuleRef<any>, routes: Routes): Observable<void> {\n    const /** @type {?} */ res: Observable<any>[] = [];\n    for (const /** @type {?} */ route of routes) {\n      // we already have the config loaded, just recurse\n      if (route.loadChildren && !route.canLoad && route._loadedConfig) {\n        const /** @type {?} */ childConfig = route._loadedConfig;\n        res.push(this.processRoutes(childConfig.module, childConfig.routes));\n\n        // no config loaded, fetch the config\n      } else if (route.loadChildren && !route.canLoad) {\n        res.push(this.preloadConfig(ngModule, route));\n\n        // recurse into children\n      } else if (route.children) {\n        res.push(this.processRoutes(ngModule, route.children));\n      }\n    }\n    return mergeAll.call(from(res));\n  }\n/**\n * @param {?} ngModule\n * @param {?} route\n * @return {?}\n */\nprivate preloadConfig(ngModule: NgModuleRef<any>, route: Route): Observable<void> {\n    return this.preloadingStrategy.preload(route, () => {\n      const /** @type {?} */ loaded$ = this.loader.load(ngModule.injector, route);\n      return mergeMap.call(loaded$, (config: LoadedRouterConfig) => {\n        route._loadedConfig = config;\n        return this.processRoutes(config.module, config.routes);\n      });\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: NgModuleFactoryLoader, },\n{type: Compiler, },\n{type: Injector, },\n{type: PreloadingStrategy, },\n];\n}\n\nfunction RouterPreloader_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterPreloader.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterPreloader.ctorParameters;\n/** @type {?} */\nRouterPreloader.prototype.loader;\n/** @type {?} */\nRouterPreloader.prototype.subscription;\n/** @type {?} */\nRouterPreloader.prototype.router;\n/** @type {?} */\nRouterPreloader.prototype.injector;\n/** @type {?} */\nRouterPreloader.prototype.preloadingStrategy;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Attribute, ChangeDetectorRef, ComponentFactoryResolver, ComponentRef, Directive, EventEmitter, Injector, OnDestroy, OnInit, Output, ViewContainerRef} from '@angular/core';\n\nimport {ChildrenOutletContexts} from '../router_outlet_context';\nimport {ActivatedRoute} from '../router_state';\nimport {PRIMARY_OUTLET} from '../shared';\n/**\n * \\@whatItDoes Acts as a placeholder that Ang<PERSON> dynamically fills based on the current router\n * state.\n * \n * \\@howToUse \n * \n * ```\n * <router-outlet></router-outlet>\n * <router-outlet name='left'></router-outlet>\n * <router-outlet name='right'></router-outlet>\n * ```\n * \n * A router outlet will emit an activate event any time a new component is being instantiated,\n * and a deactivate event when it is being destroyed.\n * \n * ```\n * <router-outlet\n *   (activate)='onActivate($event)'\n *   (deactivate)='onDeactivate($event)'></router-outlet>\n * ```\n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterOutlet implements OnDestroy, OnInit {\nprivate activated: ComponentRef<any>|null = null;\nprivate _activatedRoute: ActivatedRoute|null = null;\nprivate name: string;\n\n   activateEvents = new EventEmitter<any>();\n   deactivateEvents = new EventEmitter<any>();\n/**\n * @param {?} parentContexts\n * @param {?} location\n * @param {?} resolver\n * @param {?} name\n * @param {?} changeDetector\n */\nconstructor(\nprivate parentContexts: ChildrenOutletContexts,\nprivate location: ViewContainerRef,\nprivate resolver: ComponentFactoryResolver,  name: string,\nprivate changeDetector: ChangeDetectorRef) {\n    this.name = name || PRIMARY_OUTLET;\n    parentContexts.onChildOutletCreated(this.name, this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.parentContexts.onChildOutletDestroyed(this.name); }\n/**\n * @return {?}\n */\nngOnInit(): void {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const /** @type {?} */ context = this.parentContexts.getContext(this.name);\n      if (context && context.route) {\n        if (context.attachRef) {\n          // `attachRef` is populated when there is an existing component to mount\n          this.attach(context.attachRef, context.route);\n        } else {\n          // otherwise the component defined in the configuration is created\n          this.activateWith(context.route, context.resolver || null);\n        }\n      }\n    }\n  }\n/**\n * @deprecated since v4 *\n * @return {?}\n */\nget locationInjector(): Injector { return this.location.injector; }\n/**\n * @deprecated since v4 *\n * @return {?}\n */\nget locationFactoryResolver(): ComponentFactoryResolver { return this.resolver; }\n/**\n * @return {?}\n */\nget isActivated(): boolean { return !!this.activated; }\n/**\n * @return {?}\n */\nget component(): Object {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    return this.activated.instance;\n  }\n/**\n * @return {?}\n */\nget activatedRoute(): ActivatedRoute {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    return /** @type {?} */(( this._activatedRoute as ActivatedRoute));\n  }\n/**\n * @return {?}\n */\nget activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n/**\n * Called when the `RouteReuseStrategy` instructs to detach the subtree\n * @return {?}\n */\ndetach(): ComponentRef<any> {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    this.location.detach();\n    const /** @type {?} */ cmp = this.activated;\n    this.activated = null;\n    this._activatedRoute = null;\n    return cmp;\n  }\n/**\n * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n * @param {?} ref\n * @param {?} activatedRoute\n * @return {?}\n */\nattach(ref: ComponentRef<any>, activatedRoute: ActivatedRoute) {\n    this.activated = ref;\n    this._activatedRoute = activatedRoute;\n    this.location.insert(ref.hostView);\n  }\n/**\n * @return {?}\n */\ndeactivate(): void {\n    if (this.activated) {\n      const /** @type {?} */ c = this.component;\n      this.activated.destroy();\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n/**\n * @param {?} activatedRoute\n * @param {?} resolver\n * @return {?}\n */\nactivateWith(activatedRoute: ActivatedRoute, resolver: ComponentFactoryResolver|null) {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    const /** @type {?} */ snapshot = activatedRoute._futureSnapshot;\n    const /** @type {?} */ component = /** @type {?} */(( <any> /** @type {?} */((snapshot._routeConfig)).component));\n    resolver = resolver || this.resolver;\n    const /** @type {?} */ factory = resolver.resolveComponentFactory(component);\n    const /** @type {?} */ childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n    const /** @type {?} */ injector = new OutletInjector(activatedRoute, childContexts, this.location.injector);\n    this.activated = this.location.createComponent(factory, this.location.length, injector);\n    // Calling `markForCheck` to make sure we will run the change detection when the\n    // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n    this.changeDetector.markForCheck();\n    this.activateEvents.emit(this.activated.instance);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'router-outlet', exportAs: 'outlet'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ChildrenOutletContexts, },\n{type: ViewContainerRef, },\n{type: ComponentFactoryResolver, },\n{type: undefined, decorators: [{ type: Attribute, args: ['name', ] }, ]},\n{type: ChangeDetectorRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'activateEvents': [{ type: Output, args: ['activate', ] },],\n'deactivateEvents': [{ type: Output, args: ['deactivate', ] },],\n};\n}\n\nfunction RouterOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterOutlet.ctorParameters;\n/** @type {?} */\nRouterOutlet.propDecorators;\n/** @type {?} */\nRouterOutlet.prototype.activated;\n/** @type {?} */\nRouterOutlet.prototype._activatedRoute;\n/** @type {?} */\nRouterOutlet.prototype.name;\n/** @type {?} */\nRouterOutlet.prototype.activateEvents;\n/** @type {?} */\nRouterOutlet.prototype.deactivateEvents;\n/** @type {?} */\nRouterOutlet.prototype.parentContexts;\n/** @type {?} */\nRouterOutlet.prototype.location;\n/** @type {?} */\nRouterOutlet.prototype.resolver;\n/** @type {?} */\nRouterOutlet.prototype.changeDetector;\n}\n\nclass OutletInjector implements Injector {\n/**\n * @param {?} route\n * @param {?} childContexts\n * @param {?} parent\n */\nconstructor(\nprivate route: ActivatedRoute,\nprivate childContexts: ChildrenOutletContexts,\nprivate parent: Injector) {}\n/**\n * @param {?} token\n * @param {?=} notFoundValue\n * @return {?}\n */\nget(token: any, notFoundValue?: any): any {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n\n    return this.parent.get(token, notFoundValue);\n  }\n}\n\nfunction OutletInjector_tsickle_Closure_declarations() {\n/** @type {?} */\nOutletInjector.prototype.route;\n/** @type {?} */\nOutletInjector.prototype.childContexts;\n/** @type {?} */\nOutletInjector.prototype.parent;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentFactoryResolver, ComponentRef} from '@angular/core';\n\nimport {RouterOutlet} from './directives/router_outlet';\nimport {ActivatedRoute} from './router_state';\n/**\n * Store contextual information about a {\\@link RouterOutlet}\n * \n * \\@stable\n */\nexport class OutletContext {\n  outlet: RouterOutlet|null = null;\n  route: ActivatedRoute|null = null;\n  resolver: ComponentFactoryResolver|null = null;\n  children = new ChildrenOutletContexts();\n  attachRef: ComponentRef<any>|null = null;\n}\n\nfunction OutletContext_tsickle_Closure_declarations() {\n/** @type {?} */\nOutletContext.prototype.outlet;\n/** @type {?} */\nOutletContext.prototype.route;\n/** @type {?} */\nOutletContext.prototype.resolver;\n/** @type {?} */\nOutletContext.prototype.children;\n/** @type {?} */\nOutletContext.prototype.attachRef;\n}\n\n/**\n * Store contextual information about the children (= nested) {\\@link RouterOutlet}\n * \n * \\@stable\n */\nexport class ChildrenOutletContexts {\nprivate contexts = new Map<string, OutletContext>();\n/**\n * Called when a `RouterOutlet` directive is instantiated\n * @param {?} childName\n * @param {?} outlet\n * @return {?}\n */\nonChildOutletCreated(childName: string, outlet: RouterOutlet): void {\n    const /** @type {?} */ context = this.getOrCreateContext(childName);\n    context.outlet = outlet;\n    this.contexts.set(childName, context);\n  }\n/**\n * Called when a `RouterOutlet` directive is destroyed.\n * We need to keep the context as the outlet could be destroyed inside a NgIf and might be\n * re-created later.\n * @param {?} childName\n * @return {?}\n */\nonChildOutletDestroyed(childName: string): void {\n    const /** @type {?} */ context = this.getContext(childName);\n    if (context) {\n      context.outlet = null;\n    }\n  }\n/**\n * Called when the corresponding route is deactivated during navigation.\n * Because the component get destroyed, all children outlet are destroyed.\n * @return {?}\n */\nonOutletDeactivated(): Map<string, OutletContext> {\n    const /** @type {?} */ contexts = this.contexts;\n    this.contexts = new Map();\n    return contexts;\n  }\n/**\n * @param {?} contexts\n * @return {?}\n */\nonOutletReAttached(contexts: Map<string, OutletContext>) { this.contexts = contexts; }\n/**\n * @param {?} childName\n * @return {?}\n */\ngetOrCreateContext(childName: string): OutletContext {\n    let /** @type {?} */ context = this.getContext(childName);\n\n    if (!context) {\n      context = new OutletContext();\n      this.contexts.set(childName, context);\n    }\n\n    return context;\n  }\n/**\n * @param {?} childName\n * @return {?}\n */\ngetContext(childName: string): OutletContext|null { return this.contexts.get(childName) || null; }\n}\n\nfunction ChildrenOutletContexts_tsickle_Closure_declarations() {\n/** @type {?} */\nChildrenOutletContexts.prototype.contexts;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AfterContentInit, ChangeDetectorRef, ContentChildren, Directive, ElementRef, Input, OnChanges, OnDestroy, QueryList, Renderer, SimpleChanges} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {NavigationEnd} from '../events';\nimport {Router} from '../router';\nimport {RouterLink, RouterLinkWithHref} from './router_link';\n/**\n * \\@whatItDoes Lets you add a CSS class to an element when the link's route becomes active.\n * \n * \\@howToUse \n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n * \n * \\@description \n * \n * The RouterLinkActive directive lets you add a CSS class to an element when the link's route\n * becomes active.\n * \n * Consider the following example:\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n * \n * When the url is either '/user' or '/user/bob', the active-link class will\n * be added to the `a` tag. If the url changes, the class will be removed.\n * \n * You can set more than one class, as follows:\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n * \n * You can configure RouterLinkActive by passing `exact: true`. This will add the classes\n * only when the url matches the link exactly.\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n * \n * You can assign the RouterLinkActive instance to a template variable and directly check\n * the `isActive` status.\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n * \n * Finally, you can apply the RouterLinkActive directive to an ancestor of a RouterLink.\n * \n * ```\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n * \n * This will set the active-link class on the div tag if the url is either '/user/jim' or\n * '/user/bob'.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterLinkActive implements OnChanges,\n    OnDestroy, AfterContentInit {\n   links: QueryList<RouterLink>;\n  \n  linksWithHrefs: QueryList<RouterLinkWithHref>;\nprivate classes: string[] = [];\nprivate subscription: Subscription;\nprivate active: boolean = false;\n\n   routerLinkActiveOptions: {exact: boolean} = {exact: false};\n/**\n * @param {?} router\n * @param {?} element\n * @param {?} renderer\n * @param {?} cdr\n */\nconstructor(\nprivate router: Router,\nprivate element: ElementRef,\nprivate renderer: Renderer,\nprivate cdr: ChangeDetectorRef) {\n    this.subscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n/**\n * @return {?}\n */\nget isActive(): boolean { return this.active; }\n/**\n * @return {?}\n */\nngAfterContentInit(): void {\n    this.links.changes.subscribe(_ => this.update());\n    this.linksWithHrefs.changes.subscribe(_ => this.update());\n    this.update();\n  }\n/**\n * @param {?} data\n * @return {?}\n */\nset routerLinkActive(data: string[]|string) {\n    const /** @type {?} */ classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter(c => !!c);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void { this.update(); }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.subscription.unsubscribe(); }\n/**\n * @return {?}\n */\nprivate update(): void {\n    if (!this.links || !this.linksWithHrefs || !this.router.navigated) return;\n    const /** @type {?} */ hasActiveLinks = this.hasActiveLinks();\n\n    // react only when status has changed to prevent unnecessary dom updates\n    if (this.active !== hasActiveLinks) {\n      this.classes.forEach(\n          c => this.renderer.setElementClass(this.element.nativeElement, c, hasActiveLinks));\n      Promise.resolve(hasActiveLinks).then(active => this.active = active);\n    }\n  }\n/**\n * @param {?} router\n * @return {?}\n */\nprivate isLinkActive(router: Router): (link: (RouterLink|RouterLinkWithHref)) => boolean {\n    return (link: RouterLink | RouterLinkWithHref) =>\n               router.isActive(link.urlTree, this.routerLinkActiveOptions.exact);\n  }\n/**\n * @return {?}\n */\nprivate hasActiveLinks(): boolean {\n    return this.links.some(this.isLinkActive(this.router)) ||\n        this.linksWithHrefs.some(this.isLinkActive(this.router));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[routerLinkActive]',\n  exportAs: 'routerLinkActive',\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ElementRef, },\n{type: Renderer, },\n{type: ChangeDetectorRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'links': [{ type: ContentChildren, args: [RouterLink, {descendants: true}, ] },],\n'linksWithHrefs': [{ type: ContentChildren, args: [RouterLinkWithHref, {descendants: true}, ] },],\n'routerLinkActiveOptions': [{ type: Input },],\n'routerLinkActive': [{ type: Input },],\n};\n}\n\nfunction RouterLinkActive_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLinkActive.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLinkActive.ctorParameters;\n/** @type {?} */\nRouterLinkActive.propDecorators;\n/** @type {?} */\nRouterLinkActive.prototype.links;\n/** @type {?} */\nRouterLinkActive.prototype.linksWithHrefs;\n/** @type {?} */\nRouterLinkActive.prototype.classes;\n/** @type {?} */\nRouterLinkActive.prototype.subscription;\n/** @type {?} */\nRouterLinkActive.prototype.active;\n/** @type {?} */\nRouterLinkActive.prototype.routerLinkActiveOptions;\n/** @type {?} */\nRouterLinkActive.prototype.router;\n/** @type {?} */\nRouterLinkActive.prototype.element;\n/** @type {?} */\nRouterLinkActive.prototype.renderer;\n/** @type {?} */\nRouterLinkActive.prototype.cdr;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {LocationStrategy} from '@angular/common';\nimport {Attribute, Directive, ElementRef, HostBinding, HostListener, Input, OnChanges, OnDestroy, Renderer, isDevMode} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\n\nimport {QueryParamsHandling} from '../config';\nimport {NavigationEnd} from '../events';\nimport {Router} from '../router';\nimport {ActivatedRoute} from '../router_state';\nimport {UrlTree} from '../url_tree';\n/**\n * \\@whatItDoes Lets you link to specific parts of your app.\n * \n * \\@howToUse \n * \n * Consider the following route configuration:\n * `[{ path: 'user/:name', component: UserCmp }]`\n * \n * When linking to this `user/:name` route, you can write:\n * `<a routerLink='/user/bob'>link to user component</a>`\n * \n * \\@description \n * \n * The RouterLink directives let you link to specific parts of your app.\n * \n * When the link is static, you can use the directive as follows:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n * \n * If you use dynamic values to generate the link, you can pass an array of path\n * segments, followed by the params for each segment.\n * \n * For instance `['/team', teamId, 'user', userName, {details: true}]`\n * means that we want to generate a link to `/team/11/user/bob;details=true`.\n * \n * Multiple static segments can be merged into one\n * (e.g., `['/team/11/user', userName, {details: true}]`).\n * \n * The first segment name can be prepended with `/`, `./`, or `../`:\n * * If the first segment begins with `/`, the router will look up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router will\n *   instead look in the children of the current activated route.\n * * And if the first segment begins with `../`, the router will go up one level.\n * \n * You can set query params and fragment as follows:\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * RouterLink will use these to generate this link: `/user/bob#education?debug=true`.\n * \n * (Deprecated in v4.0.0 use `queryParamsHandling` instead) You can also tell the\n * directive to preserve the current query params and fragment:\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" preserveQueryParams preserveFragment>\n *   link to user component\n * </a>\n * ```\n * \n * You can tell the directive to how to handle queryParams, available options are:\n *  - 'merge' merge the queryParams into the current queryParams\n *  - 'preserve' prserve the current queryParams\n *  - default / '' use the queryParams only\n *  same options for {\\@link NavigationExtras#queryParamsHandling}\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n * \n * The router link directive always treats the provided input as a delta to the current url.\n * \n * For instance, if the current url is `/user/(box//aux:team)`.\n * \n * Then the following link `<a [routerLink]=\"['/user/jim']\">Jim</a>` will generate the link\n * `/user/(jim//aux:team)`.\n * \n * \\@ngModule RouterModule\n * \n * See {\\@link Router#createUrlTree} for more information.\n * \n * \\@stable\n */\nexport class RouterLink {\n   queryParams: {[k: string]: any};\n   fragment: string;\n   queryParamsHandling: QueryParamsHandling;\n   preserveFragment: boolean;\n   skipLocationChange: boolean;\n   replaceUrl: boolean;\nprivate commands: any[] = [];\nprivate preserve: boolean;\n/**\n * @param {?} router\n * @param {?} route\n * @param {?} tabIndex\n * @param {?} renderer\n * @param {?} el\n */\nconstructor(\nprivate router: Router,\nprivate route: ActivatedRoute,\n       tabIndex: string, renderer: Renderer, el: ElementRef) {\n    if (tabIndex == null) {\n      renderer.setElementAttribute(el.nativeElement, 'tabindex', '0');\n    }\n  }\n/**\n * @param {?} commands\n * @return {?}\n */\nset routerLink(commands: any[]|string) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n/**\n * @deprecated 4.0.0 use `queryParamsHandling` instead.\n * @param {?} value\n * @return {?}\n */\nset preserveQueryParams(value: boolean) {\n    if (isDevMode() && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated!, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n/**\n * @return {?}\n */\nonClick(): boolean {\n    const /** @type {?} */ extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl),\n    };\n    this.router.navigateByUrl(this.urlTree, extras);\n    return true;\n  }\n/**\n * @return {?}\n */\nget urlTree(): UrlTree {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      preserveQueryParams: attrBoolValue(this.preserve),\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment),\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: ':not(a)[routerLink]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ActivatedRoute, },\n{type: undefined, decorators: [{ type: Attribute, args: ['tabindex', ] }, ]},\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'queryParams': [{ type: Input },],\n'fragment': [{ type: Input },],\n'queryParamsHandling': [{ type: Input },],\n'preserveFragment': [{ type: Input },],\n'skipLocationChange': [{ type: Input },],\n'replaceUrl': [{ type: Input },],\n'routerLink': [{ type: Input },],\n'preserveQueryParams': [{ type: Input },],\n'onClick': [{ type: HostListener, args: ['click', ] },],\n};\n}\n\nfunction RouterLink_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLink.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLink.ctorParameters;\n/** @type {?} */\nRouterLink.propDecorators;\n/** @type {?} */\nRouterLink.prototype.queryParams;\n/** @type {?} */\nRouterLink.prototype.fragment;\n/** @type {?} */\nRouterLink.prototype.queryParamsHandling;\n/** @type {?} */\nRouterLink.prototype.preserveFragment;\n/** @type {?} */\nRouterLink.prototype.skipLocationChange;\n/** @type {?} */\nRouterLink.prototype.replaceUrl;\n/** @type {?} */\nRouterLink.prototype.commands;\n/** @type {?} */\nRouterLink.prototype.preserve;\n/** @type {?} */\nRouterLink.prototype.router;\n/** @type {?} */\nRouterLink.prototype.route;\n}\n\n/**\n * \\@whatItDoes Lets you link to specific parts of your app.\n * \n * See {\\@link RouterLink} for more information.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterLinkWithHref implements OnChanges, OnDestroy {\n    target: string;\n   queryParams: {[k: string]: any};\n   fragment: string;\n   queryParamsHandling: QueryParamsHandling;\n   preserveFragment: boolean;\n   skipLocationChange: boolean;\n   replaceUrl: boolean;\nprivate commands: any[] = [];\nprivate subscription: Subscription;\nprivate preserve: boolean;\n\n  // the url displayed on the anchor element.\n   href: string;\n/**\n * @param {?} router\n * @param {?} route\n * @param {?} locationStrategy\n */\nconstructor(\nprivate router: Router,\nprivate route: ActivatedRoute,\nprivate locationStrategy: LocationStrategy) {\n    this.subscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.updateTargetUrlAndHref();\n      }\n    });\n  }\n/**\n * @param {?} commands\n * @return {?}\n */\nset routerLink(commands: any[]|string) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset preserveQueryParams(value: boolean) {\n    if (isDevMode() && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: {}): any { this.updateTargetUrlAndHref(); }\n/**\n * @return {?}\n */\nngOnDestroy(): any { this.subscription.unsubscribe(); }\n/**\n * @param {?} button\n * @param {?} ctrlKey\n * @param {?} metaKey\n * @param {?} shiftKey\n * @return {?}\n */\nonClick(button: number, ctrlKey: boolean, metaKey: boolean, shiftKey: boolean): boolean {\n    if (button !== 0 || ctrlKey || metaKey || shiftKey) {\n      return true;\n    }\n\n    if (typeof this.target === 'string' && this.target != '_self') {\n      return true;\n    }\n\n    const /** @type {?} */ extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl),\n    };\n    this.router.navigateByUrl(this.urlTree, extras);\n    return false;\n  }\n/**\n * @return {?}\n */\nprivate updateTargetUrlAndHref(): void {\n    this.href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree));\n  }\n/**\n * @return {?}\n */\nget urlTree(): UrlTree {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      preserveQueryParams: attrBoolValue(this.preserve),\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment),\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'a[routerLink]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ActivatedRoute, },\n{type: LocationStrategy, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'target': [{ type: HostBinding, args: ['attr.target', ] },{ type: Input },],\n'queryParams': [{ type: Input },],\n'fragment': [{ type: Input },],\n'queryParamsHandling': [{ type: Input },],\n'preserveFragment': [{ type: Input },],\n'skipLocationChange': [{ type: Input },],\n'replaceUrl': [{ type: Input },],\n'href': [{ type: HostBinding },],\n'routerLink': [{ type: Input },],\n'preserveQueryParams': [{ type: Input },],\n'onClick': [{ type: HostListener, args: ['click', ['$event.button', '$event.ctrlKey', '$event.metaKey', '$event.shiftKey'], ] },],\n};\n}\n\nfunction RouterLinkWithHref_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLinkWithHref.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLinkWithHref.ctorParameters;\n/** @type {?} */\nRouterLinkWithHref.propDecorators;\n/** @type {?} */\nRouterLinkWithHref.prototype.target;\n/** @type {?} */\nRouterLinkWithHref.prototype.queryParams;\n/** @type {?} */\nRouterLinkWithHref.prototype.fragment;\n/** @type {?} */\nRouterLinkWithHref.prototype.queryParamsHandling;\n/** @type {?} */\nRouterLinkWithHref.prototype.preserveFragment;\n/** @type {?} */\nRouterLinkWithHref.prototype.skipLocationChange;\n/** @type {?} */\nRouterLinkWithHref.prototype.replaceUrl;\n/** @type {?} */\nRouterLinkWithHref.prototype.commands;\n/** @type {?} */\nRouterLinkWithHref.prototype.subscription;\n/** @type {?} */\nRouterLinkWithHref.prototype.preserve;\n/** @type {?} */\nRouterLinkWithHref.prototype.href;\n/** @type {?} */\nRouterLinkWithHref.prototype.router;\n/** @type {?} */\nRouterLinkWithHref.prototype.route;\n/** @type {?} */\nRouterLinkWithHref.prototype.locationStrategy;\n}\n\n/**\n * @param {?} s\n * @return {?}\n */\nfunction attrBoolValue(s: any): boolean {\n  return s === '' || !!s;\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Location} from '@angular/common';\nimport {Compiler, Injector, NgModuleFactoryLoader, NgModuleRef, Type, isDevMode} from '@angular/core';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {concatMap} from 'rxjs/operator/concatMap';\nimport {every} from 'rxjs/operator/every';\nimport {first} from 'rxjs/operator/first';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {reduce} from 'rxjs/operator/reduce';\n\nimport {applyRedirects} from './apply_redirects';\nimport {LoadedRouterConfig, QueryParamsHandling, ResolveData, Route, Routes, RunGuardsAndResolvers, validateConfig} from './config';\nimport {createRouterState} from './create_router_state';\nimport {createUrlTree} from './create_url_tree';\nimport {Event, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, RouteConfigLoadEnd, RouteConfigLoadStart, RoutesRecognized} from './events';\nimport {recognize} from './recognize';\nimport {DefaultRouteReuseStrategy, DetachedRouteHandleInternal, RouteReuseStrategy} from './route_reuse_strategy';\nimport {RouterConfigLoader} from './router_config_loader';\nimport {ChildrenOutletContexts, OutletContext} from './router_outlet_context';\nimport {ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot, advanceActivatedRoute, createEmptyState, equalParamsAndUrlSegments, inheritedParamsDataResolve} from './router_state';\nimport {Params, isNavigationCancelingError} from './shared';\nimport {DefaultUrlHandlingStrategy, UrlHandlingStrategy} from './url_handling_strategy';\nimport {UrlSerializer, UrlTree, containsTree, createEmptyUrlTree} from './url_tree';\nimport {andObservables, forEach, shallowEqual, waitForMap, wrapIntoObservable} from './utils/collection';\nimport {TreeNode} from './utils/tree';\n\ndeclare let Zone: any;\n\n/**\n * @whatItDoes Represents the extra options used during navigation.\n *\n * @stable\n */\nexport interface NavigationExtras {\n  /**\n  * Enables relative navigation from the current ActivatedRoute.\n  *\n  * Configuration:\n  *\n  * ```\n  * [{\n  *   path: 'parent',\n  *   component: ParentComponent,\n  *   children: [{\n  *     path: 'list',\n  *     component: ListComponent\n  *   },{\n  *     path: 'child',\n  *     component: ChildComponent\n  *   }]\n  * }]\n  * ```\n  *\n  * Navigate to list route from child route:\n  *\n  * ```\n  *  @Component({...})\n  *  class ChildComponent {\n  *    constructor(private router: Router, private route: ActivatedRoute) {}\n  *\n  *    go() {\n  *      this.router.navigate(['../list'], { relativeTo: this.route });\n  *    }\n  *  }\n  * ```\n  */\n  relativeTo?: ActivatedRoute|null;\n\n  /**\n  * Sets query parameters to the URL.\n  *\n  * ```\n  * // Navigate to /results?page=1\n  * this.router.navigate(['/results'], { queryParams: { page: 1 } });\n  * ```\n  */\n  queryParams?: Params|null;\n\n  /**\n  * Sets the hash fragment for the URL.\n  *\n  * ```\n  * // Navigate to /results#top\n  * this.router.navigate(['/results'], { fragment: 'top' });\n  * ```\n  */\n  fragment?: string;\n\n  /**\n  * Preserves the query parameters for the next navigation.\n  *\n  * deprecated, use `queryParamsHandling` instead\n  *\n  * ```\n  * // Preserve query params from /results?page=1 to /view?page=1\n  * this.router.navigate(['/view'], { preserveQueryParams: true });\n  * ```\n  *\n  * @deprecated since v4\n  */\n  preserveQueryParams?: boolean;\n\n  /**\n  *  config strategy to handle the query parameters for the next navigation.\n  *\n  * ```\n  * // from /results?page=1 to /view?page=1&page=2\n  * this.router.navigate(['/view'], { queryParams: { page: 2 },  queryParamsHandling: \"merge\" });\n  * ```\n  */\n  queryParamsHandling?: QueryParamsHandling|null;\n  /**\n  * Preserves the fragment for the next navigation\n  *\n  * ```\n  * // Preserve fragment from /results#top to /view#top\n  * this.router.navigate(['/view'], { preserveFragment: true });\n  * ```\n  */\n  preserveFragment?: boolean;\n  /**\n  * Navigates without pushing a new state into history.\n  *\n  * ```\n  * // Navigate silently to /view\n  * this.router.navigate(['/view'], { skipLocationChange: true });\n  * ```\n  */\n  skipLocationChange?: boolean;\n  /**\n  * Navigates while replacing the current state in history.\n  *\n  * ```\n  * // Navigate to /view\n  * this.router.navigate(['/view'], { replaceUrl: true });\n  * ```\n  */\n  replaceUrl?: boolean;\n}\n\n/**\n * @whatItDoes Error handler that is invoked when a navigation errors.\n *\n * @description\n * If the handler returns a value, the navigation promise will be resolved with this value.\n * If the handler throws an exception, the navigation promise will be rejected with\n * the exception.\n *\n * @stable\n */\nexport type ErrorHandler = (error: any) => any;\n/**\n * @param {?} error\n * @return {?}\n */\nfunction defaultErrorHandler(error: any): any {\n  throw error;\n}\n\ntype NavigationSource = 'imperative' | 'popstate' | 'hashchange';\n\ntype NavigationParams = {\n  id: number,\n  rawUrl: UrlTree,\n  extras: NavigationExtras,\n  resolve: any,\n  reject: any,\n  promise: Promise<boolean>,\n  source: NavigationSource,\n};\n\n/**\n * @internal\n */\nexport type RouterHook = (snapshot: RouterStateSnapshot) => Observable<void>;\n/**\n * \\@internal\n * @param {?} snapshot\n * @return {?}\n */\nfunction defaultRouterHook(snapshot: RouterStateSnapshot): Observable<void> {\n  return /** @type {?} */(( of (null) as any));\n}\n/**\n * \\@whatItDoes Provides the navigation and url manipulation capabilities.\n * \n * See {\\@link Routes} for more details and examples.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class Router {\nprivate currentUrlTree: UrlTree;\nprivate rawUrlTree: UrlTree;\nprivate navigations = new BehaviorSubject<NavigationParams>( /** @type {?} */((null)));\nprivate routerEvents = new Subject<Event>();\nprivate currentRouterState: RouterState;\nprivate locationSubscription: Subscription;\nprivate navigationId: number = 0;\nprivate configLoader: RouterConfigLoader;\nprivate ngModule: NgModuleRef<any>;\n/**\n * Error handler that is invoked when a navigation errors.\n * \n * See {\\@link ErrorHandler} for more information.\n */\nerrorHandler: ErrorHandler = defaultErrorHandler;\n/**\n * Indicates if at least one navigation happened.\n */\nnavigated: boolean = false;\n/**\n * Used by RouterModule. This allows us to\n * pause the navigation either before preactivation or after it.\n * \\@internal\n */\nhooks: {beforePreactivation: RouterHook, afterPreactivation: RouterHook} = {\n    beforePreactivation: defaultRouterHook,\n    afterPreactivation: defaultRouterHook\n  };\n/**\n * Extracts and merges URLs. Used for AngularJS to Angular migrations.\n */\nurlHandlingStrategy: UrlHandlingStrategy = new DefaultUrlHandlingStrategy();\n\n  routeReuseStrategy: RouteReuseStrategy = new DefaultRouteReuseStrategy();\n/**\n * @param {?} rootComponentType\n * @param {?} urlSerializer\n * @param {?} rootContexts\n * @param {?} location\n * @param {?} injector\n * @param {?} loader\n * @param {?} compiler\n * @param {?} config\n */\nconstructor(\nprivate rootComponentType: Type<any>|null,\nprivate urlSerializer: UrlSerializer,\nprivate rootContexts: ChildrenOutletContexts,\nprivate location: Location, injector: Injector,\n      loader: NgModuleFactoryLoader, compiler: Compiler,\npublic config: Routes) {\n    const onLoadStart = (r: Route) => this.triggerEvent(new RouteConfigLoadStart(r));\n    const onLoadEnd = (r: Route) => this.triggerEvent(new RouteConfigLoadEnd(r));\n\n    this.ngModule = injector.get(NgModuleRef);\n\n    this.resetConfig(config);\n    this.currentUrlTree = createEmptyUrlTree();\n    this.rawUrlTree = this.currentUrlTree;\n\n    this.configLoader = new RouterConfigLoader(loader, compiler, onLoadStart, onLoadEnd);\n    this.currentRouterState = createEmptyState(this.currentUrlTree, this.rootComponentType);\n    this.processNavigations();\n  }\n/**\n * \\@internal \n * TODO: this should be removed once the constructor of the router made internal\n * @param {?} rootComponentType\n * @return {?}\n */\nresetRootComponentType(rootComponentType: Type<any>): void {\n    this.rootComponentType = rootComponentType;\n    // TODO: vsavkin router 4.0 should make the root component set to null\n    // this will simplify the lifecycle of the router.\n    this.currentRouterState.root.component = this.rootComponentType;\n  }\n/**\n * Sets up the location change listener and performs the initial navigation.\n * @return {?}\n */\ninitialNavigation(): void {\n    this.setUpLocationChangeListener();\n    if (this.navigationId === 0) {\n      this.navigateByUrl(this.location.path(true), {replaceUrl: true});\n    }\n  }\n/**\n * Sets up the location change listener.\n * @return {?}\n */\nsetUpLocationChangeListener(): void {\n    // Zone.current.wrap is needed because of the issue with RxJS scheduler,\n    // which does not work properly with zone.js in IE and Safari\n    if (!this.locationSubscription) {\n      this.locationSubscription = /** @type {?} */(( <any>this.location.subscribe(Zone.current.wrap((change: any) => {\n        const /** @type {?} */ rawUrlTree = this.urlSerializer.parse(change['url']);\n        const /** @type {?} */ source: NavigationSource = change['type'] === 'popstate' ? 'popstate' : 'hashchange';\n        setTimeout(() => { this.scheduleNavigation(rawUrlTree, source, {replaceUrl: true}); }, 0);\n      }))));\n    }\n  }\n/**\n * The current route state\n * @return {?}\n */\nget routerState(): RouterState { return this.currentRouterState; }\n/**\n * The current url\n * @return {?}\n */\nget url(): string { return this.serializeUrl(this.currentUrlTree); }\n/**\n * An observable of router events\n * @return {?}\n */\nget events(): Observable<Event> { return this.routerEvents; }\n/**\n * \\@internal\n * @param {?} e\n * @return {?}\n */\ntriggerEvent(e: Event) { this.routerEvents.next(e); }\n/**\n * Resets the configuration used for navigation and generating links.\n * \n * ### Usage\n * \n * ```\n * router.resetConfig([\n *  { path: 'team/:id', component: TeamCmp, children: [\n *    { path: 'simple', component: SimpleCmp },\n *    { path: 'user/:name', component: UserCmp }\n *  ]}\n * ]);\n * ```\n * @param {?} config\n * @return {?}\n */\nresetConfig(config: Routes): void {\n    validateConfig(config);\n    this.config = config;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\nngOnDestroy() { this.dispose(); }\n/**\n * Disposes of the router\n * @return {?}\n */\ndispose(): void {\n    if (this.locationSubscription) {\n      this.locationSubscription.unsubscribe();\n      this.locationSubscription = /** @type {?} */(( null));\n    }\n  }\n/**\n * Applies an array of commands to the current url tree and creates a new url tree.\n * \n * When given an activate route, applies the given commands starting from the route.\n * When not given a route, applies the given command starting from the root.\n * \n * ### Usage\n * \n * ```\n * // create /team/33/user/11\n * router.createUrlTree(['/team', 33, 'user', 11]);\n * \n * // create /team/33;expand=true/user/11\n * router.createUrlTree(['/team', 33, {expand: true}, 'user', 11]);\n * \n * // you can collapse static segments like this (this works only with the first passed-in value):\n * router.createUrlTree(['/team/33/user', userId]);\n * \n * // If the first segment can contain slashes, and you do not want the router to split it, you\n * // can do the following:\n * \n * router.createUrlTree([{segmentPath: '/one/two'}]);\n * \n * // create /team/33/(user/11//right:chat)\n * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: 'chat'}}]);\n * \n * // remove the right secondary node\n * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: null}}]);\n * \n * // assuming the current url is `/team/33/user/11` and the route points to `user/11`\n * \n * // navigate to /team/33/user/11/details\n * router.createUrlTree(['details'], {relativeTo: route});\n * \n * // navigate to /team/33/user/22\n * router.createUrlTree(['../22'], {relativeTo: route});\n * \n * // navigate to /team/44/user/22\n * router.createUrlTree(['../../team/44/user/22'], {relativeTo: route});\n * ```\n * @param {?} commands\n * @param {?=} navigationExtras\n * @return {?}\n */\ncreateUrlTree(commands: any[], navigationExtras: NavigationExtras = {}): UrlTree {\n    const {relativeTo,          queryParams,         fragment,\n           preserveQueryParams, queryParamsHandling, preserveFragment} = navigationExtras;\n    if (isDevMode() && preserveQueryParams && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n    }\n    const /** @type {?} */ a = relativeTo || this.routerState.root;\n    const /** @type {?} */ f = preserveFragment ? this.currentUrlTree.fragment : fragment;\n    let /** @type {?} */ q: Params|null = null;\n    if (queryParamsHandling) {\n      switch (queryParamsHandling) {\n        case 'merge':\n          q = {...this.currentUrlTree.queryParams, ...queryParams};\n          break;\n        case 'preserve':\n          q = this.currentUrlTree.queryParams;\n          break;\n        default:\n          q = queryParams || null;\n      }\n    } else {\n      q = preserveQueryParams ? this.currentUrlTree.queryParams : queryParams || null;\n    }\n    return createUrlTree(a, this.currentUrlTree, commands, /** @type {?} */(( q)), /** @type {?} */(( f)));\n  }\n/**\n * Navigate based on the provided url. This navigation is always absolute.\n * \n * Returns a promise that:\n * - resolves to 'true' when navigation succeeds,\n * - resolves to 'false' when navigation fails,\n * - is rejected when an error happens.\n * \n * ### Usage\n * \n * ```\n * router.navigateByUrl(\"/team/33/user/11\");\n * \n * // Navigate without updating the URL\n * router.navigateByUrl(\"/team/33/user/11\", { skipLocationChange: true });\n * ```\n * \n * In opposite to `navigate`, `navigateByUrl` takes a whole URL\n * and does not apply any delta to the current one.\n * @param {?} url\n * @param {?=} extras\n * @return {?}\n */\nnavigateByUrl(url: string|UrlTree, extras: NavigationExtras = {skipLocationChange: false}):\n      Promise<boolean> {\n    const /** @type {?} */ urlTree = url instanceof UrlTree ? url : this.parseUrl(url);\n    const /** @type {?} */ mergedTree = this.urlHandlingStrategy.merge(urlTree, this.rawUrlTree);\n\n    return this.scheduleNavigation(mergedTree, 'imperative', extras);\n  }\n/**\n * Navigate based on the provided array of commands and a starting point.\n * If no starting route is provided, the navigation is absolute.\n * \n * Returns a promise that:\n * - resolves to 'true' when navigation succeeds,\n * - resolves to 'false' when navigation fails,\n * - is rejected when an error happens.\n * \n * ### Usage\n * \n * ```\n * router.navigate(['team', 33, 'user', 11], {relativeTo: route});\n * \n * // Navigate without updating the URL\n * router.navigate(['team', 33, 'user', 11], {relativeTo: route, skipLocationChange: true});\n * ```\n * \n * In opposite to `navigateByUrl`, `navigate` always takes a delta that is applied to the current\n * URL.\n * @param {?} commands\n * @param {?=} extras\n * @return {?}\n */\nnavigate(commands: any[], extras: NavigationExtras = {skipLocationChange: false}):\n      Promise<boolean> {\n    validateCommands(commands);\n    if (typeof extras.queryParams === 'object' && extras.queryParams !== null) {\n      extras.queryParams = this.removeEmptyProps(extras.queryParams);\n    }\n    return this.navigateByUrl(this.createUrlTree(commands, extras), extras);\n  }\n/**\n * Serializes a {\\@link UrlTree} into a string\n * @param {?} url\n * @return {?}\n */\nserializeUrl(url: UrlTree): string { return this.urlSerializer.serialize(url); }\n/**\n * Parses a string into a {\\@link UrlTree}\n * @param {?} url\n * @return {?}\n */\nparseUrl(url: string): UrlTree { return this.urlSerializer.parse(url); }\n/**\n * Returns whether the url is activated\n * @param {?} url\n * @param {?} exact\n * @return {?}\n */\nisActive(url: string|UrlTree, exact: boolean): boolean {\n    if (url instanceof UrlTree) {\n      return containsTree(this.currentUrlTree, url, exact);\n    }\n\n    const /** @type {?} */ urlTree = this.urlSerializer.parse(url);\n    return containsTree(this.currentUrlTree, urlTree, exact);\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate removeEmptyProps(params: Params): Params {\n    return Object.keys(params).reduce((result: Params, key: string) => {\n      const /** @type {?} */ value: any = params[key];\n      if (value !== null && value !== undefined) {\n        result[key] = value;\n      }\n      return result;\n    }, {});\n  }\n/**\n * @return {?}\n */\nprivate processNavigations(): void {\n    concatMap\n        .call(\n            this.navigations,\n            (nav: NavigationParams) => {\n              if (nav) {\n                this.executeScheduledNavigation(nav);\n                // a failed navigation should not stop the router from processing\n                // further navigations => the catch\n                return nav.promise.catch(() => {});\n              } else {\n                return /** @type {?} */(( <any>of (null)));\n              }\n            })\n        .subscribe(() => {});\n  }\n/**\n * @param {?} rawUrl\n * @param {?} source\n * @param {?} extras\n * @return {?}\n */\nprivate scheduleNavigation(rawUrl: UrlTree, source: NavigationSource, extras: NavigationExtras):\n      Promise<boolean> {\n    const /** @type {?} */ lastNavigation = this.navigations.value;\n\n    // If the user triggers a navigation imperatively (e.g., by using navigateByUrl),\n    // and that navigation results in 'replaceState' that leads to the same URL,\n    // we should skip those.\n    if (lastNavigation && source !== 'imperative' && lastNavigation.source === 'imperative' &&\n        lastNavigation.rawUrl.toString() === rawUrl.toString()) {\n      return Promise.resolve(true);  // return value is not used\n    }\n\n    // Because of a bug in IE and Edge, the location class fires two events (popstate and\n    // hashchange) every single time. The second one should be ignored. Otherwise, the URL will\n    // flicker.\n    if (lastNavigation && source == 'hashchange' && lastNavigation.source === 'popstate' &&\n        lastNavigation.rawUrl.toString() === rawUrl.toString()) {\n      return Promise.resolve(true);  // return value is not used\n    }\n\n    let /** @type {?} */ resolve: any = null;\n    let /** @type {?} */ reject: any = null;\n\n    const /** @type {?} */ promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n\n    const /** @type {?} */ id = ++this.navigationId;\n    this.navigations.next({id, source, rawUrl, extras, resolve, reject, promise});\n\n    // Make sure that the error is propagated even though `processNavigations` catch\n    // handler does not rethrow\n    return promise.catch((e: any) => Promise.reject(e));\n  }\n/**\n * @param {?} __0\n * @return {?}\n */\nprivate executeScheduledNavigation({id, rawUrl, extras, resolve, reject}: NavigationParams):\n      void {\n    const /** @type {?} */ url = this.urlHandlingStrategy.extract(rawUrl);\n    const /** @type {?} */ urlTransition = !this.navigated || url.toString() !== this.currentUrlTree.toString();\n\n    if (urlTransition && this.urlHandlingStrategy.shouldProcessUrl(rawUrl)) {\n      this.routerEvents.next(new NavigationStart(id, this.serializeUrl(url)));\n      Promise.resolve()\n          .then(\n              (_) => this.runNavigate(\n                  url, rawUrl, !!extras.skipLocationChange, !!extras.replaceUrl, id, null))\n          .then(resolve, reject);\n\n      // we cannot process the current URL, but we could process the previous one =>\n      // we need to do some cleanup\n    } else if (\n        urlTransition && this.rawUrlTree &&\n        this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree)) {\n      this.routerEvents.next(new NavigationStart(id, this.serializeUrl(url)));\n      Promise.resolve()\n          .then(\n              (_) => this.runNavigate(\n                  url, rawUrl, false, false, id,\n                  createEmptyState(url, this.rootComponentType).snapshot))\n          .then(resolve, reject);\n\n    } else {\n      this.rawUrlTree = rawUrl;\n      resolve(null);\n    }\n  }\n/**\n * @param {?} url\n * @param {?} rawUrl\n * @param {?} shouldPreventPushState\n * @param {?} shouldReplaceUrl\n * @param {?} id\n * @param {?} precreatedState\n * @return {?}\n */\nprivate runNavigate(\n      url: UrlTree, rawUrl: UrlTree, shouldPreventPushState: boolean, shouldReplaceUrl: boolean,\n      id: number, precreatedState: RouterStateSnapshot|null): Promise<boolean> {\n    if (id !== this.navigationId) {\n      this.location.go(this.urlSerializer.serialize(this.currentUrlTree));\n      this.routerEvents.next(new NavigationCancel(\n          id, this.serializeUrl(url),\n          `Navigation ID ${id} is not equal to the current navigation id ${this.navigationId}`));\n      return Promise.resolve(false);\n    }\n\n    return new Promise((resolvePromise, rejectPromise) => {\n      // create an observable of the url and route state snapshot\n      // this operation do not result in any side effects\n      let /** @type {?} */ urlAndSnapshot$: Observable<{appliedUrl: UrlTree, snapshot: RouterStateSnapshot}>;\n      if (!precreatedState) {\n        const /** @type {?} */ moduleInjector = this.ngModule.injector;\n        const /** @type {?} */ redirectsApplied$ =\n            applyRedirects(moduleInjector, this.configLoader, this.urlSerializer, url, this.config);\n\n        urlAndSnapshot$ = mergeMap.call(redirectsApplied$, (appliedUrl: UrlTree) => {\n          return map.call(\n              recognize(\n                  this.rootComponentType, this.config, appliedUrl, this.serializeUrl(appliedUrl)),\n              (snapshot: any) => {\n\n                this.routerEvents.next(new RoutesRecognized(\n                    id, this.serializeUrl(url), this.serializeUrl(appliedUrl), snapshot));\n\n                return {appliedUrl, snapshot};\n              });\n        });\n      } else {\n        urlAndSnapshot$ = of ({appliedUrl: url, snapshot: precreatedState});\n      }\n\n      const /** @type {?} */ beforePreactivationDone$ = mergeMap.call(\n          urlAndSnapshot$, (p: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            return map.call(this.hooks.beforePreactivation(p.snapshot), () => p);\n          });\n\n      // run preactivation: guards and data resolvers\n      let /** @type {?} */ preActivation: PreActivation;\n\n      const /** @type {?} */ preactivationTraverse$ = map.call(\n          beforePreactivationDone$,\n          ({appliedUrl, snapshot}: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            const /** @type {?} */ moduleInjector = this.ngModule.injector;\n            preActivation =\n                new PreActivation(snapshot, this.currentRouterState.snapshot, moduleInjector);\n            preActivation.traverse(this.rootContexts);\n            return {appliedUrl, snapshot};\n          });\n\n      const /** @type {?} */ preactivationCheckGuards$ = mergeMap.call(\n          preactivationTraverse$,\n          ({appliedUrl, snapshot}: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            if (this.navigationId !== id) return of (false);\n\n            return map.call(preActivation.checkGuards(), (shouldActivate: boolean) => {\n              return {appliedUrl: appliedUrl, snapshot: snapshot, shouldActivate: shouldActivate};\n            });\n          });\n\n      const /** @type {?} */ preactivationResolveData$ = mergeMap.call(preactivationCheckGuards$, (p: any) => {\n        if (this.navigationId !== id) return of (false);\n\n        if (p.shouldActivate) {\n          return map.call(preActivation.resolveData(), () => p);\n        } else {\n          return of (p);\n        }\n      });\n\n      const /** @type {?} */ preactivationDone$ = mergeMap.call(preactivationResolveData$, (p: any) => {\n        return map.call(this.hooks.afterPreactivation(p.snapshot), () => p);\n      });\n\n\n      // create router state\n      // this operation has side effects => route state is being affected\n      const /** @type {?} */ routerState$ =\n          map.call(preactivationDone$, ({appliedUrl, snapshot, shouldActivate}: any) => {\n            if (shouldActivate) {\n              const /** @type {?} */ state =\n                  createRouterState(this.routeReuseStrategy, snapshot, this.currentRouterState);\n              return {appliedUrl, state, shouldActivate};\n            } else {\n              return {appliedUrl, state: null, shouldActivate};\n            }\n          });\n\n\n      // applied the new router state\n      // this operation has side effects\n      let /** @type {?} */ navigationIsSuccessful: boolean;\n      const /** @type {?} */ storedState = this.currentRouterState;\n      const /** @type {?} */ storedUrl = this.currentUrlTree;\n\n      routerState$\n          .forEach(({appliedUrl, state, shouldActivate}: any) => {\n            if (!shouldActivate || id !== this.navigationId) {\n              navigationIsSuccessful = false;\n              return;\n            }\n\n            this.currentUrlTree = appliedUrl;\n            this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, rawUrl);\n\n            this.currentRouterState = state;\n\n            if (!shouldPreventPushState) {\n              const /** @type {?} */ path = this.urlSerializer.serialize(this.rawUrlTree);\n              if (this.location.isCurrentPathEqualTo(path) || shouldReplaceUrl) {\n                this.location.replaceState(path);\n              } else {\n                this.location.go(path);\n              }\n            }\n\n            new ActivateRoutes(this.routeReuseStrategy, state, storedState)\n                .activate(this.rootContexts);\n\n            navigationIsSuccessful = true;\n          })\n          .then(\n              () => {\n                if (navigationIsSuccessful) {\n                  this.navigated = true;\n                  this.routerEvents.next(new NavigationEnd(\n                      id, this.serializeUrl(url), this.serializeUrl(this.currentUrlTree)));\n                  resolvePromise(true);\n                } else {\n                  this.resetUrlToCurrentUrlTree();\n                  this.routerEvents.next(new NavigationCancel(id, this.serializeUrl(url), ''));\n                  resolvePromise(false);\n                }\n              },\n              (e: any) => {\n                if (isNavigationCancelingError(e)) {\n                  this.resetUrlToCurrentUrlTree();\n                  this.navigated = true;\n                  this.routerEvents.next(\n                      new NavigationCancel(id, this.serializeUrl(url), e.message));\n                  resolvePromise(false);\n                } else {\n                  this.routerEvents.next(new NavigationError(id, this.serializeUrl(url), e));\n                  try {\n                    resolvePromise(this.errorHandler(e));\n                  } catch ( /** @type {?} */ee) {\n                    rejectPromise(ee);\n                  }\n                }\n\n                this.currentRouterState = storedState;\n                this.currentUrlTree = storedUrl;\n                this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, rawUrl);\n                this.location.replaceState(this.serializeUrl(this.rawUrlTree));\n              });\n    });\n  }\n/**\n * @return {?}\n */\nprivate resetUrlToCurrentUrlTree(): void {\n    const /** @type {?} */ path = this.urlSerializer.serialize(this.rawUrlTree);\n    this.location.replaceState(path);\n  }\n}\n\nfunction Router_tsickle_Closure_declarations() {\n/** @type {?} */\nRouter.prototype.currentUrlTree;\n/** @type {?} */\nRouter.prototype.rawUrlTree;\n/** @type {?} */\nRouter.prototype.navigations;\n/** @type {?} */\nRouter.prototype.routerEvents;\n/** @type {?} */\nRouter.prototype.currentRouterState;\n/** @type {?} */\nRouter.prototype.locationSubscription;\n/** @type {?} */\nRouter.prototype.navigationId;\n/** @type {?} */\nRouter.prototype.configLoader;\n/** @type {?} */\nRouter.prototype.ngModule;\n/**\n * Error handler that is invoked when a navigation errors.\n * \n * See {\\@link ErrorHandler} for more information.\n * @type {?}\n */\nRouter.prototype.errorHandler;\n/**\n * Indicates if at least one navigation happened.\n * @type {?}\n */\nRouter.prototype.navigated;\n/**\n * Used by RouterModule. This allows us to\n * pause the navigation either before preactivation or after it.\n * \\@internal\n * @type {?}\n */\nRouter.prototype.hooks;\n/**\n * Extracts and merges URLs. Used for AngularJS to Angular migrations.\n * @type {?}\n */\nRouter.prototype.urlHandlingStrategy;\n/** @type {?} */\nRouter.prototype.routeReuseStrategy;\n/** @type {?} */\nRouter.prototype.rootComponentType;\n/** @type {?} */\nRouter.prototype.urlSerializer;\n/** @type {?} */\nRouter.prototype.rootContexts;\n/** @type {?} */\nRouter.prototype.location;\n/** @type {?} */\nRouter.prototype.config;\n}\n\nclass CanActivate {\n/**\n * @param {?} path\n */\nconstructor(public path: ActivatedRouteSnapshot[]) {}\n/**\n * @return {?}\n */\nget route(): ActivatedRouteSnapshot { return this.path[this.path.length - 1]; }\n}\n\nfunction CanActivate_tsickle_Closure_declarations() {\n/** @type {?} */\nCanActivate.prototype.path;\n}\n\nclass CanDeactivate {\n/**\n * @param {?} component\n * @param {?} route\n */\nconstructor(public component: Object|null,\npublic route: ActivatedRouteSnapshot) {}\n}\n\nfunction CanDeactivate_tsickle_Closure_declarations() {\n/** @type {?} */\nCanDeactivate.prototype.component;\n/** @type {?} */\nCanDeactivate.prototype.route;\n}\n\nexport class PreActivation {\nprivate canActivateChecks: CanActivate[] = [];\nprivate canDeactivateChecks: CanDeactivate[] = [];\n/**\n * @param {?} future\n * @param {?} curr\n * @param {?} moduleInjector\n */\nconstructor(\nprivate future: RouterStateSnapshot,\nprivate curr: RouterStateSnapshot,\nprivate moduleInjector: Injector) {}\n/**\n * @param {?} parentContexts\n * @return {?}\n */\ntraverse(parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ futureRoot = this.future._root;\n    const /** @type {?} */ currRoot = this.curr ? this.curr._root : null;\n    this.traverseChildRoutes(futureRoot, currRoot, parentContexts, [futureRoot.value]);\n  }\n/**\n * @return {?}\n */\ncheckGuards(): Observable<boolean> {\n    if (this.canDeactivateChecks.length === 0 && this.canActivateChecks.length === 0) {\n      return of (true);\n    }\n    const /** @type {?} */ canDeactivate$ = this.runCanDeactivateChecks();\n    return mergeMap.call(\n        canDeactivate$,\n        (canDeactivate: boolean) => canDeactivate ? this.runCanActivateChecks() : of (false));\n  }\n/**\n * @return {?}\n */\nresolveData(): Observable<any> {\n    if (this.canActivateChecks.length === 0) return of (null);\n    const /** @type {?} */ checks$ = from(this.canActivateChecks);\n    const /** @type {?} */ runningChecks$ =\n        concatMap.call(checks$, (check: CanActivate) => this.runResolve(check.route));\n    return reduce.call(runningChecks$, (_: any, __: any) => _);\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @param {?} futurePath\n * @return {?}\n */\nprivate traverseChildRoutes(\n      futureNode: TreeNode<ActivatedRouteSnapshot>, currNode: TreeNode<ActivatedRouteSnapshot>|null,\n      contexts: ChildrenOutletContexts|null, futurePath: ActivatedRouteSnapshot[]): void {\n    const /** @type {?} */ prevChildren = nodeChildrenAsMap(currNode);\n\n    // Process the children of the future route\n    futureNode.children.forEach(c => {\n      this.traverseRoutes(c, prevChildren[c.value.outlet], contexts, futurePath.concat([c.value]));\n      delete prevChildren[c.value.outlet];\n    });\n\n    // Process any children left from the current route (not active for the future route)\n    forEach(\n        prevChildren, (v: TreeNode<ActivatedRouteSnapshot>, k: string) =>\n                          this.deactivateRouteAndItsChildren(v, /** @type {?} */(( contexts)).getContext(k)));\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContexts\n * @param {?} futurePath\n * @return {?}\n */\nprivate traverseRoutes(\n      futureNode: TreeNode<ActivatedRouteSnapshot>, currNode: TreeNode<ActivatedRouteSnapshot>,\n      parentContexts: ChildrenOutletContexts|null, futurePath: ActivatedRouteSnapshot[]): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n    const /** @type {?} */ context = parentContexts ? parentContexts.getContext(futureNode.value.outlet) : null;\n\n    // reusing the node\n    if (curr && future._routeConfig === curr._routeConfig) {\n      if (this.shouldRunGuardsAndResolvers(\n              curr, future, /** @type {?} */(( future._routeConfig)).runGuardsAndResolvers)) {\n        this.canActivateChecks.push(new CanActivate(futurePath));\n        const /** @type {?} */ outlet = /** @type {?} */(( /** @type {?} */(( context)).outlet));\n        this.canDeactivateChecks.push(new CanDeactivate(outlet.component, curr));\n      } else {\n        // we need to set the data\n        future.data = curr.data;\n        future._resolvedData = curr._resolvedData;\n      }\n\n      // If we have a component, we need to go through an outlet.\n      if (future.component) {\n        this.traverseChildRoutes(\n            futureNode, currNode, context ? context.children : null, futurePath);\n\n        // if we have a componentless route, we recurse but keep the same outlet map.\n      } else {\n        this.traverseChildRoutes(futureNode, currNode, parentContexts, futurePath);\n      }\n    } else {\n      if (curr) {\n        this.deactivateRouteAndItsChildren(currNode, context);\n      }\n\n      this.canActivateChecks.push(new CanActivate(futurePath));\n      // If we have a component, we need to go through an outlet.\n      if (future.component) {\n        this.traverseChildRoutes(futureNode, null, context ? context.children : null, futurePath);\n\n        // if we have a componentless route, we recurse but keep the same outlet map.\n      } else {\n        this.traverseChildRoutes(futureNode, null, parentContexts, futurePath);\n      }\n    }\n  }\n/**\n * @param {?} curr\n * @param {?} future\n * @param {?} mode\n * @return {?}\n */\nprivate shouldRunGuardsAndResolvers(\n      curr: ActivatedRouteSnapshot, future: ActivatedRouteSnapshot,\n      mode: RunGuardsAndResolvers|undefined): boolean {\n    switch (mode) {\n      case 'always':\n        return true;\n\n      case 'paramsOrQueryParamsChange':\n        return !equalParamsAndUrlSegments(curr, future) ||\n            !shallowEqual(curr.queryParams, future.queryParams);\n\n      case 'paramsChange':\n      default:\n        return !equalParamsAndUrlSegments(curr, future);\n    }\n  }\n/**\n * @param {?} route\n * @param {?} context\n * @return {?}\n */\nprivate deactivateRouteAndItsChildren(\n      route: TreeNode<ActivatedRouteSnapshot>, context: OutletContext|null): void {\n    const /** @type {?} */ children = nodeChildrenAsMap(route);\n    const /** @type {?} */ r = route.value;\n\n    forEach(children, (node: TreeNode<ActivatedRouteSnapshot>, childName: string) => {\n      if (!r.component) {\n        this.deactivateRouteAndItsChildren(node, context);\n      } else if (context) {\n        this.deactivateRouteAndItsChildren(node, context.children.getContext(childName));\n      } else {\n        this.deactivateRouteAndItsChildren(node, null);\n      }\n    });\n\n    if (!r.component) {\n      this.canDeactivateChecks.push(new CanDeactivate(null, r));\n    } else if (context && context.outlet && context.outlet.isActivated) {\n      this.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, r));\n    } else {\n      this.canDeactivateChecks.push(new CanDeactivate(null, r));\n    }\n  }\n/**\n * @return {?}\n */\nprivate runCanDeactivateChecks(): Observable<boolean> {\n    const /** @type {?} */ checks$ = from(this.canDeactivateChecks);\n    const /** @type {?} */ runningChecks$ = mergeMap.call(\n        checks$, (check: CanDeactivate) => this.runCanDeactivate(check.component, check.route));\n    return every.call(runningChecks$, (result: boolean) => result === true);\n  }\n/**\n * @return {?}\n */\nprivate runCanActivateChecks(): Observable<boolean> {\n    const /** @type {?} */ checks$ = from(this.canActivateChecks);\n    const /** @type {?} */ runningChecks$ = mergeMap.call(\n        checks$, (check: CanActivate) => andObservables(from(\n                     [this.runCanActivateChild(check.path), this.runCanActivate(check.route)])));\n    return every.call(runningChecks$, (result: boolean) => result === true);\n  }\n/**\n * @param {?} future\n * @return {?}\n */\nprivate runCanActivate(future: ActivatedRouteSnapshot): Observable<boolean> {\n    const /** @type {?} */ canActivate = future._routeConfig ? future._routeConfig.canActivate : null;\n    if (!canActivate || canActivate.length === 0) return of (true);\n    const /** @type {?} */ obs = map.call(from(canActivate), (c: any) => {\n      const /** @type {?} */ guard = this.getToken(c, future);\n      let /** @type {?} */ observable: Observable<boolean>;\n      if (guard.canActivate) {\n        observable = wrapIntoObservable(guard.canActivate(future, this.future));\n      } else {\n        observable = wrapIntoObservable(guard(future, this.future));\n      }\n      return first.call(observable);\n    });\n    return andObservables(obs);\n  }\n/**\n * @param {?} path\n * @return {?}\n */\nprivate runCanActivateChild(path: ActivatedRouteSnapshot[]): Observable<boolean> {\n    const /** @type {?} */ future = path[path.length - 1];\n\n    const /** @type {?} */ canActivateChildGuards = path.slice(0, path.length - 1)\n                                       .reverse()\n                                       .map(p => this.extractCanActivateChild(p))\n                                       .filter(_ => _ !== null);\n\n    return andObservables(map.call(from(canActivateChildGuards), (d: any) => {\n      const /** @type {?} */ obs = map.call(from(d.guards), (c: any) => {\n        const /** @type {?} */ guard = this.getToken(c, d.node);\n        let /** @type {?} */ observable: Observable<boolean>;\n        if (guard.canActivateChild) {\n          observable = wrapIntoObservable(guard.canActivateChild(future, this.future));\n        } else {\n          observable = wrapIntoObservable(guard(future, this.future));\n        }\n        return first.call(observable);\n      });\n      return andObservables(obs);\n    }));\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nprivate extractCanActivateChild(p: ActivatedRouteSnapshot):\n      {node: ActivatedRouteSnapshot, guards: any[]}|null {\n    const /** @type {?} */ canActivateChild = p._routeConfig ? p._routeConfig.canActivateChild : null;\n    if (!canActivateChild || canActivateChild.length === 0) return null;\n    return {node: p, guards: canActivateChild};\n  }\n/**\n * @param {?} component\n * @param {?} curr\n * @return {?}\n */\nprivate runCanDeactivate(component: Object|null, curr: ActivatedRouteSnapshot):\n      Observable<boolean> {\n    const /** @type {?} */ canDeactivate = curr && curr._routeConfig ? curr._routeConfig.canDeactivate : null;\n    if (!canDeactivate || canDeactivate.length === 0) return of (true);\n    const /** @type {?} */ canDeactivate$ = mergeMap.call(from(canDeactivate), (c: any) => {\n      const /** @type {?} */ guard = this.getToken(c, curr);\n      let /** @type {?} */ observable: Observable<boolean>;\n      if (guard.canDeactivate) {\n        observable =\n            wrapIntoObservable(guard.canDeactivate(component, curr, this.curr, this.future));\n      } else {\n        observable = wrapIntoObservable(guard(component, curr, this.curr, this.future));\n      }\n      return first.call(observable);\n    });\n    return every.call(canDeactivate$, (result: any) => result === true);\n  }\n/**\n * @param {?} future\n * @return {?}\n */\nprivate runResolve(future: ActivatedRouteSnapshot): Observable<any> {\n    const /** @type {?} */ resolve = future._resolve;\n    return map.call(this.resolveNode(resolve, future), (resolvedData: any): any => {\n      future._resolvedData = resolvedData;\n      future.data = {...future.data, ...inheritedParamsDataResolve(future).resolve};\n      return null;\n    });\n  }\n/**\n * @param {?} resolve\n * @param {?} future\n * @return {?}\n */\nprivate resolveNode(resolve: ResolveData, future: ActivatedRouteSnapshot): Observable<any> {\n    return waitForMap(resolve, (k, v) => {\n      const /** @type {?} */ resolver = this.getToken(v, future);\n      return resolver.resolve ? wrapIntoObservable(resolver.resolve(future, this.future)) :\n                                wrapIntoObservable(resolver(future, this.future));\n    });\n  }\n/**\n * @param {?} token\n * @param {?} snapshot\n * @return {?}\n */\nprivate getToken(token: any, snapshot: ActivatedRouteSnapshot): any {\n    const /** @type {?} */ config = closestLoadedConfig(snapshot);\n    const /** @type {?} */ injector = config ? config.module.injector : this.moduleInjector;\n    return injector.get(token);\n  }\n}\n\nfunction PreActivation_tsickle_Closure_declarations() {\n/** @type {?} */\nPreActivation.prototype.canActivateChecks;\n/** @type {?} */\nPreActivation.prototype.canDeactivateChecks;\n/** @type {?} */\nPreActivation.prototype.future;\n/** @type {?} */\nPreActivation.prototype.curr;\n/** @type {?} */\nPreActivation.prototype.moduleInjector;\n}\n\nclass ActivateRoutes {\n/**\n * @param {?} routeReuseStrategy\n * @param {?} futureState\n * @param {?} currState\n */\nconstructor(\nprivate routeReuseStrategy: RouteReuseStrategy,\nprivate futureState: RouterState,\nprivate currState: RouterState) {}\n/**\n * @param {?} parentContexts\n * @return {?}\n */\nactivate(parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ futureRoot = this.futureState._root;\n    const /** @type {?} */ currRoot = this.currState ? this.currState._root : null;\n\n    this.deactivateChildRoutes(futureRoot, currRoot, parentContexts);\n    advanceActivatedRoute(this.futureState.root);\n    this.activateChildRoutes(futureRoot, currRoot, parentContexts);\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @return {?}\n */\nprivate deactivateChildRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>|null,\n      contexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ children: {[outletName: string]: TreeNode<ActivatedRoute>} = nodeChildrenAsMap(currNode);\n\n    // Recurse on the routes active in the future state to de-activate deeper children\n    futureNode.children.forEach(futureChild => {\n      const /** @type {?} */ childOutletName = futureChild.value.outlet;\n      this.deactivateRoutes(futureChild, children[childOutletName], contexts);\n      delete children[childOutletName];\n    });\n\n    // De-activate the routes that will not be re-used\n    forEach(children, (v: TreeNode<ActivatedRoute>, childName: string) => {\n      this.deactivateRouteAndItsChildren(v, contexts);\n    });\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContext\n * @return {?}\n */\nprivate deactivateRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>,\n      parentContext: ChildrenOutletContexts): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n\n    if (future === curr) {\n      // Reusing the node, check to see if the children need to be de-activated\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const /** @type {?} */ context = parentContext.getContext(future.outlet);\n        if (context) {\n          this.deactivateChildRoutes(futureNode, currNode, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.deactivateChildRoutes(futureNode, currNode, parentContext);\n      }\n    } else {\n      if (curr) {\n        // Deactivate the current route which will not be re-used\n        this.deactivateRouteAndItsChildren(currNode, parentContext);\n      }\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate deactivateRouteAndItsChildren(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    if (this.routeReuseStrategy.shouldDetach(route.value.snapshot)) {\n      this.detachAndStoreRouteSubtree(route, parentContexts);\n    } else {\n      this.deactivateRouteAndOutlet(route, parentContexts);\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate detachAndStoreRouteSubtree(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ context = parentContexts.getContext(route.value.outlet);\n    if (context && context.outlet) {\n      const /** @type {?} */ componentRef = context.outlet.detach();\n      const /** @type {?} */ contexts = context.children.onOutletDeactivated();\n      this.routeReuseStrategy.store(route.value.snapshot, {componentRef, route, contexts});\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate deactivateRouteAndOutlet(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ context = parentContexts.getContext(route.value.outlet);\n\n    if (context) {\n      const /** @type {?} */ children: {[outletName: string]: any} = nodeChildrenAsMap(route);\n      const /** @type {?} */ contexts = route.value.component ? context.children : parentContexts;\n\n      forEach(children, (v: any, k: string) => {this.deactivateRouteAndItsChildren(v, contexts)});\n\n      if (context.outlet) {\n        // Destroy the component\n        context.outlet.deactivate();\n        // Destroy the contexts for all the outlets that were in the component\n        context.children.onOutletDeactivated();\n      }\n    }\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @return {?}\n */\nprivate activateChildRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>|null,\n      contexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ children: {[outlet: string]: any} = nodeChildrenAsMap(currNode);\n    futureNode.children.forEach(\n        c => { this.activateRoutes(c, children[c.value.outlet], contexts); });\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContexts\n * @return {?}\n */\nprivate activateRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>,\n      parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n\n    advanceActivatedRoute(future);\n\n    // reusing the node\n    if (future === curr) {\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const /** @type {?} */ context = parentContexts.getOrCreateContext(future.outlet);\n        this.activateChildRoutes(futureNode, currNode, context.children);\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, currNode, parentContexts);\n      }\n    } else {\n      if (future.component) {\n        // if we have a normal route, we need to place the component into the outlet and recurse.\n        const /** @type {?} */ context = parentContexts.getOrCreateContext(future.outlet);\n\n        if (this.routeReuseStrategy.shouldAttach(future.snapshot)) {\n          const /** @type {?} */ stored =\n              ( /** @type {?} */((<DetachedRouteHandleInternal>this.routeReuseStrategy.retrieve(future.snapshot))));\n          this.routeReuseStrategy.store(future.snapshot, null);\n          context.children.onOutletReAttached(stored.contexts);\n          context.attachRef = stored.componentRef;\n          context.route = stored.route.value;\n          if (context.outlet) {\n            // Attach right away when the outlet has already been instantiated\n            // Otherwise attach from `RouterOutlet.ngOnInit` when it is instantiated\n            context.outlet.attach(stored.componentRef, stored.route.value);\n          }\n          advanceActivatedRouteNodeAndItsChildren(stored.route);\n        } else {\n          const /** @type {?} */ config = parentLoadedConfig(future.snapshot);\n          const /** @type {?} */ cmpFactoryResolver = config ? config.module.componentFactoryResolver : null;\n\n          context.route = future;\n          context.resolver = cmpFactoryResolver;\n          if (context.outlet) {\n            // Activate the outlet when it has already been instantiated\n            // Otherwise it will get activated from its `ngOnInit` when instantiated\n            context.outlet.activateWith(future, cmpFactoryResolver);\n          }\n\n          this.activateChildRoutes(futureNode, null, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, null, parentContexts);\n      }\n    }\n  }\n}\n\nfunction ActivateRoutes_tsickle_Closure_declarations() {\n/** @type {?} */\nActivateRoutes.prototype.routeReuseStrategy;\n/** @type {?} */\nActivateRoutes.prototype.futureState;\n/** @type {?} */\nActivateRoutes.prototype.currState;\n}\n\n/**\n * @param {?} node\n * @return {?}\n */\nfunction advanceActivatedRouteNodeAndItsChildren(node: TreeNode<ActivatedRoute>): void {\n  advanceActivatedRoute(node.value);\n  node.children.forEach(advanceActivatedRouteNodeAndItsChildren);\n}\n/**\n * @param {?} snapshot\n * @return {?}\n */\nfunction parentLoadedConfig(snapshot: ActivatedRouteSnapshot): LoadedRouterConfig|null {\n  for (let /** @type {?} */ s = snapshot.parent; s; s = s.parent) {\n    const /** @type {?} */ route = s._routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n    if (route && route.component) return null;\n  }\n\n  return null;\n}\n/**\n * @param {?} snapshot\n * @return {?}\n */\nfunction closestLoadedConfig(snapshot: ActivatedRouteSnapshot): LoadedRouterConfig|null {\n  if (!snapshot) return null;\n\n  for (let /** @type {?} */ s = snapshot.parent; s; s = s.parent) {\n    const /** @type {?} */ route = s._routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n  }\n\n  return null;\n}\n/**\n * @template T\n * @param {?} node\n * @return {?}\n */\nfunction nodeChildrenAsMap<T extends{outlet: string}>(node: TreeNode<T>| null) {\n  const /** @type {?} */ map: {[outlet: string]: TreeNode<T>} = {};\n\n  if (node) {\n    node.children.forEach(child => map[child.value.outlet] = child);\n  }\n\n  return map;\n}\n/**\n * @param {?} commands\n * @return {?}\n */\nfunction validateCommands(commands: string[]): void {\n  for (let /** @type {?} */ i = 0; i < commands.length; i++) {\n    const /** @type {?} */ cmd = commands[i];\n    if (cmd == null) {\n      throw new Error(`The requested path contains ${cmd} segment at index ${i}`);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {UrlTree} from './url_tree';\n/**\n * \\@whatItDoes Provides a way to migrate AngularJS applications to Angular.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class UrlHandlingStrategy {\n/**\n * Tells the router if this URL should be processed.\n * \n * When it returns true, the router will execute the regular navigation.\n * When it returns false, the router will set the router state to an empty state.\n * As a result, all the active components will be destroyed.\n * \n * @abstract\n * @param {?} url\n * @return {?}\n */\nshouldProcessUrl(url: UrlTree) {}\n/**\n * Extracts the part of the URL that should be handled by the router.\n * The rest of the URL will remain untouched.\n * @abstract\n * @param {?} url\n * @return {?}\n */\nextract(url: UrlTree) {}\n/**\n * Merges the URL fragment with the rest of the URL.\n * @abstract\n * @param {?} newUrlPart\n * @param {?} rawUrl\n * @return {?}\n */\nmerge(newUrlPart: UrlTree, rawUrl: UrlTree) {}\n}\n/**\n * \\@experimental\n */\nexport class DefaultUrlHandlingStrategy implements UrlHandlingStrategy {\n/**\n * @param {?} url\n * @return {?}\n */\nshouldProcessUrl(url: UrlTree): boolean { return true; }\n/**\n * @param {?} url\n * @return {?}\n */\nextract(url: UrlTree): UrlTree { return url; }\n/**\n * @param {?} newUrlPart\n * @param {?} wholeUrl\n * @return {?}\n */\nmerge(newUrlPart: UrlTree, wholeUrl: UrlTree): UrlTree { return newUrlPart; }\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Compiler, InjectionToken, Injector, NgModuleFactory, NgModuleFactoryLoader, NgModuleRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {of } from 'rxjs/observable/of';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {LoadChildren, LoadedRouterConfig, Route} from './config';\nimport {flatten, wrapIntoObservable} from './utils/collection';\n/**\n * \\@docsNotRequired\n * \\@experimental\n */\nexport const ROUTES = new InjectionToken<Route[][]>('ROUTES');\nexport class RouterConfigLoader {\n/**\n * @param {?} loader\n * @param {?} compiler\n * @param {?=} onLoadStartListener\n * @param {?=} onLoadEndListener\n */\nconstructor(\nprivate loader: NgModuleFactoryLoader,\nprivate compiler: Compiler,\nprivate onLoadStartListener?: (r: Route) => void,\nprivate onLoadEndListener?: (r: Route) => void) {}\n/**\n * @param {?} parentInjector\n * @param {?} route\n * @return {?}\n */\nload(parentInjector: Injector, route: Route): Observable<LoadedRouterConfig> {\n    if (this.onLoadStartListener) {\n      this.onLoadStartListener(route);\n    }\n\n    const /** @type {?} */ moduleFactory$ = this.loadModuleFactory( /** @type {?} */((route.loadChildren)));\n\n    return map.call(moduleFactory$, (factory: NgModuleFactory<any>) => {\n      if (this.onLoadEndListener) {\n        this.onLoadEndListener(route);\n      }\n\n      const /** @type {?} */ module = factory.create(parentInjector);\n\n      return new LoadedRouterConfig(flatten(module.injector.get(ROUTES)), module);\n    });\n  }\n/**\n * @param {?} loadChildren\n * @return {?}\n */\nprivate loadModuleFactory(loadChildren: LoadChildren): Observable<NgModuleFactory<any>> {\n    if (typeof loadChildren === 'string') {\n      return fromPromise(this.loader.load(loadChildren));\n    } else {\n      return mergeMap.call(wrapIntoObservable(loadChildren()), (t: any) => {\n        if (t instanceof NgModuleFactory) {\n          return of (t);\n        } else {\n          return fromPromise(this.compiler.compileModuleAsync(t));\n        }\n      });\n    }\n  }\n}\n\nfunction RouterConfigLoader_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterConfigLoader.prototype.loader;\n/** @type {?} */\nRouterConfigLoader.prototype.compiler;\n/** @type {?} */\nRouterConfigLoader.prototype.onLoadStartListener;\n/** @type {?} */\nRouterConfigLoader.prototype.onLoadEndListener;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentRef} from '@angular/core';\n\nimport {OutletContext} from './router_outlet_context';\nimport {ActivatedRoute, ActivatedRouteSnapshot} from './router_state';\nimport {TreeNode} from './utils/tree';\n\n/**\n * @whatItDoes Represents the detached route tree.\n *\n * This is an opaque value the router will give to a custom route reuse strategy\n * to store and retrieve later on.\n *\n * @experimental\n */\nexport type DetachedRouteHandle = {};\n\n/** @internal */\nexport type DetachedRouteHandleInternal = {\n  contexts: Map<string, OutletContext>,\n  componentRef: ComponentRef<any>,\n  route: TreeNode<ActivatedRoute>,\n};\n/**\n * \\@whatItDoes Provides a way to customize when activated routes get reused.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class RouteReuseStrategy {\n/**\n * Determines if this route (and its subtree) should be detached to be reused later\n * @abstract\n * @param {?} route\n * @return {?}\n */\nshouldDetach(route: ActivatedRouteSnapshot) {}\n/**\n * Stores the detached route.\n * \n * Storing a `null` value should erase the previously stored value.\n * @abstract\n * @param {?} route\n * @param {?} handle\n * @return {?}\n */\nstore(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle|null) {}\n/**\n * Determines if this route (and its subtree) should be reattached\n * @abstract\n * @param {?} route\n * @return {?}\n */\nshouldAttach(route: ActivatedRouteSnapshot) {}\n/**\n * Retrieves the previously stored route\n * @abstract\n * @param {?} route\n * @return {?}\n */\nretrieve(route: ActivatedRouteSnapshot) {}\n/**\n * Determines if a route should be reused\n * @abstract\n * @param {?} future\n * @param {?} curr\n * @return {?}\n */\nshouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot) {}\n}\n/**\n * Does not detach any subtrees. Reuses routes as long as their route config is the same.\n */\nexport class DefaultRouteReuseStrategy implements RouteReuseStrategy {\n/**\n * @param {?} route\n * @return {?}\n */\nshouldDetach(route: ActivatedRouteSnapshot): boolean { return false; }\n/**\n * @param {?} route\n * @param {?} detachedTree\n * @return {?}\n */\nstore(route: ActivatedRouteSnapshot, detachedTree: DetachedRouteHandle): void {}\n/**\n * @param {?} route\n * @return {?}\n */\nshouldAttach(route: ActivatedRouteSnapshot): boolean { return false; }\n/**\n * @param {?} route\n * @return {?}\n */\nretrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle|null { return null; }\n/**\n * @param {?} future\n * @param {?} curr\n * @return {?}\n */\nshouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {\n    return future.routeConfig === curr.routeConfig;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {of } from 'rxjs/observable/of';\n\nimport {Data, ResolveData, Route, Routes} from './config';\nimport {ActivatedRouteSnapshot, RouterStateSnapshot, inheritedParamsDataResolve} from './router_state';\nimport {PRIMARY_OUTLET, defaultUrlMatcher} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree, mapChildrenIntoArray} from './url_tree';\nimport {forEach, last} from './utils/collection';\nimport {TreeNode} from './utils/tree';\nclass NoMatch {}\n/**\n * @param {?} rootComponentType\n * @param {?} config\n * @param {?} urlTree\n * @param {?} url\n * @return {?}\n */\nexport function recognize(\n    rootComponentType: Type<any>| null, config: Routes, urlTree: UrlTree,\n    url: string): Observable<RouterStateSnapshot> {\n  return new Recognizer(rootComponentType, config, urlTree, url).recognize();\n}\nclass Recognizer {\n/**\n * @param {?} rootComponentType\n * @param {?} config\n * @param {?} urlTree\n * @param {?} url\n */\nconstructor(\nprivate rootComponentType: Type<any>|null,\nprivate config: Routes,\nprivate urlTree: UrlTree,\nprivate url: string) {}\n/**\n * @return {?}\n */\nrecognize(): Observable<RouterStateSnapshot> {\n    try {\n      const /** @type {?} */ rootSegmentGroup = split(this.urlTree.root, [], [], this.config).segmentGroup;\n\n      const /** @type {?} */ children = this.processSegmentGroup(this.config, rootSegmentGroup, PRIMARY_OUTLET);\n\n      const /** @type {?} */ root = new ActivatedRouteSnapshot(\n          [], Object.freeze({}), Object.freeze(this.urlTree.queryParams), /** @type {?} */(( this.urlTree.fragment)),\n          {}, PRIMARY_OUTLET, this.rootComponentType, null, this.urlTree.root, -1, {});\n\n      const /** @type {?} */ rootNode = new TreeNode<ActivatedRouteSnapshot>(root, children);\n      const /** @type {?} */ routeState = new RouterStateSnapshot(this.url, rootNode);\n      this.inheritParamsAndData(routeState._root);\n      return of (routeState);\n\n    } catch ( /** @type {?} */e) {\n      return new Observable<RouterStateSnapshot>(\n          (obs: Observer<RouterStateSnapshot>) => obs.error(e));\n    }\n  }\n/**\n * @param {?} routeNode\n * @return {?}\n */\ninheritParamsAndData(routeNode: TreeNode<ActivatedRouteSnapshot>): void {\n    const /** @type {?} */ route = routeNode.value;\n\n    const /** @type {?} */ i = inheritedParamsDataResolve(route);\n    route.params = Object.freeze(i.params);\n    route.data = Object.freeze(i.data);\n\n    routeNode.children.forEach(n => this.inheritParamsAndData(n));\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @param {?} outlet\n * @return {?}\n */\nprocessSegmentGroup(config: Route[], segmentGroup: UrlSegmentGroup, outlet: string):\n      TreeNode<ActivatedRouteSnapshot>[] {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return this.processChildren(config, segmentGroup);\n    }\n\n    return this.processSegment(config, segmentGroup, segmentGroup.segments, outlet);\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @return {?}\n */\nprocessChildren(config: Route[], segmentGroup: UrlSegmentGroup):\n      TreeNode<ActivatedRouteSnapshot>[] {\n    const /** @type {?} */ children = mapChildrenIntoArray(\n        segmentGroup, (child, childOutlet) => this.processSegmentGroup(config, child, childOutlet));\n    checkOutletNameUniqueness(children);\n    sortActivatedRouteSnapshots(children);\n    return children;\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprocessSegment(\n      config: Route[], segmentGroup: UrlSegmentGroup, segments: UrlSegment[],\n      outlet: string): TreeNode<ActivatedRouteSnapshot>[] {\n    for (const /** @type {?} */ r of config) {\n      try {\n        return this.processSegmentAgainstRoute(r, segmentGroup, segments, outlet);\n      } catch ( /** @type {?} */e) {\n        if (!(e instanceof NoMatch)) throw e;\n      }\n    }\n    if (this.noLeftoversInUrl(segmentGroup, segments, outlet)) {\n      return [];\n    }\n\n    throw new NoMatch();\n  }\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate noLeftoversInUrl(segmentGroup: UrlSegmentGroup, segments: UrlSegment[], outlet: string):\n      boolean {\n    return segments.length === 0 && !segmentGroup.children[outlet];\n  }\n/**\n * @param {?} route\n * @param {?} rawSegment\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprocessSegmentAgainstRoute(\n      route: Route, rawSegment: UrlSegmentGroup, segments: UrlSegment[],\n      outlet: string): TreeNode<ActivatedRouteSnapshot>[] {\n    if (route.redirectTo) throw new NoMatch();\n\n    if ((route.outlet || PRIMARY_OUTLET) !== outlet) throw new NoMatch();\n\n    if (route.path === '**') {\n      const /** @type {?} */ params = segments.length > 0 ? /** @type {?} */(( last(segments))).parameters : {};\n      const /** @type {?} */ snapshot = new ActivatedRouteSnapshot(\n          segments, params, Object.freeze(this.urlTree.queryParams), /** @type {?} */(( this.urlTree.fragment)),\n          getData(route), outlet, /** @type {?} */(( route.component)), route, getSourceSegmentGroup(rawSegment),\n          getPathIndexShift(rawSegment) + segments.length, getResolve(route));\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, [])];\n    }\n\n    const {consumedSegments, parameters, lastChild} = match(rawSegment, route, segments);\n    const /** @type {?} */ rawSlicedSegments = segments.slice(lastChild);\n    const /** @type {?} */ childConfig = getChildConfig(route);\n\n    const {segmentGroup, slicedSegments} =\n        split(rawSegment, consumedSegments, rawSlicedSegments, childConfig);\n\n    const /** @type {?} */ snapshot = new ActivatedRouteSnapshot(\n        consumedSegments, parameters, Object.freeze(this.urlTree.queryParams), /** @type {?} */((\n        this.urlTree.fragment)), getData(route), outlet, /** @type {?} */(( route.component)), route,\n        getSourceSegmentGroup(rawSegment), getPathIndexShift(rawSegment) + consumedSegments.length,\n        getResolve(route));\n\n\n    if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n      const /** @type {?} */ children = this.processChildren(childConfig, segmentGroup);\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, children)];\n    }\n\n    if (childConfig.length === 0 && slicedSegments.length === 0) {\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, [])];\n    }\n\n    const /** @type {?} */ children = this.processSegment(childConfig, segmentGroup, slicedSegments, PRIMARY_OUTLET);\n    return [new TreeNode<ActivatedRouteSnapshot>(snapshot, children)];\n  }\n}\n\nfunction Recognizer_tsickle_Closure_declarations() {\n/** @type {?} */\nRecognizer.prototype.rootComponentType;\n/** @type {?} */\nRecognizer.prototype.config;\n/** @type {?} */\nRecognizer.prototype.urlTree;\n/** @type {?} */\nRecognizer.prototype.url;\n}\n\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction sortActivatedRouteSnapshots(nodes: TreeNode<ActivatedRouteSnapshot>[]): void {\n  nodes.sort((a, b) => {\n    if (a.value.outlet === PRIMARY_OUTLET) return -1;\n    if (b.value.outlet === PRIMARY_OUTLET) return 1;\n    return a.value.outlet.localeCompare(b.value.outlet);\n  });\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getChildConfig(route: Route): Route[] {\n  if (route.children) {\n    return route.children;\n  }\n\n  if (route.loadChildren) {\n    return /** @type {?} */(( route._loadedConfig)).routes;\n  }\n\n  return [];\n}\n/**\n * @param {?} segmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nfunction match(segmentGroup: UrlSegmentGroup, route: Route, segments: UrlSegment[]) {\n  if (route.path === '') {\n    if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {\n      throw new NoMatch();\n    }\n\n    return {consumedSegments: [], lastChild: 0, parameters: {}};\n  }\n\n  const /** @type {?} */ matcher = route.matcher || defaultUrlMatcher;\n  const /** @type {?} */ res = matcher(segments, segmentGroup, route);\n  if (!res) throw new NoMatch();\n\n  const /** @type {?} */ posParams: {[n: string]: string} = {};\n  forEach( /** @type {?} */((res.posParams)), (v: UrlSegment, k: string) => { posParams[k] = v.path; });\n  const /** @type {?} */ parameters = {...posParams, ...res.consumed[res.consumed.length - 1].parameters};\n\n  return {consumedSegments: res.consumed, lastChild: res.consumed.length, parameters};\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction checkOutletNameUniqueness(nodes: TreeNode<ActivatedRouteSnapshot>[]): void {\n  const /** @type {?} */ names: {[k: string]: ActivatedRouteSnapshot} = {};\n  nodes.forEach(n => {\n    const /** @type {?} */ routeWithSameOutletName = names[n.value.outlet];\n    if (routeWithSameOutletName) {\n      const /** @type {?} */ p = routeWithSameOutletName.url.map(s => s.toString()).join('/');\n      const /** @type {?} */ c = n.value.url.map(s => s.toString()).join('/');\n      throw new Error(`Two segments cannot have the same outlet name: '${p}' and '${c}'.`);\n    }\n    names[n.value.outlet] = n.value;\n  });\n}\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction getSourceSegmentGroup(segmentGroup: UrlSegmentGroup): UrlSegmentGroup {\n  let /** @type {?} */ s = segmentGroup;\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n  }\n  return s;\n}\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction getPathIndexShift(segmentGroup: UrlSegmentGroup): number {\n  let /** @type {?} */ s = segmentGroup;\n  let /** @type {?} */ res = (s._segmentIndexShift ? s._segmentIndexShift : 0);\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n    res += (s._segmentIndexShift ? s._segmentIndexShift : 0);\n  }\n  return res - 1;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} slicedSegments\n * @param {?} config\n * @return {?}\n */\nfunction split(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], slicedSegments: UrlSegment[],\n    config: Route[]) {\n  if (slicedSegments.length > 0 &&\n      containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        consumedSegments, createChildrenForEmptyPaths(\n                              segmentGroup, consumedSegments, config,\n                              new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {segmentGroup: s, slicedSegments: []};\n  }\n\n  if (slicedSegments.length === 0 &&\n      containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(\n                                   segmentGroup, slicedSegments, config, segmentGroup.children));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {segmentGroup: s, slicedSegments};\n  }\n\n  const /** @type {?} */ s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);\n  s._sourceSegment = segmentGroup;\n  s._segmentIndexShift = consumedSegments.length;\n  return {segmentGroup: s, slicedSegments};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @param {?} children\n * @return {?}\n */\nfunction addEmptyPathsToChildrenIfNeeded(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[],\n    children: {[name: string]: UrlSegmentGroup}): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  for (const /** @type {?} */ r of routes) {\n    if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      const /** @type {?} */ s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n      s._segmentIndexShift = segmentGroup.segments.length;\n      res[getOutlet(r)] = s;\n    }\n  }\n  return {...children, ...res};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} routes\n * @param {?} primarySegment\n * @return {?}\n */\nfunction createChildrenForEmptyPaths(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], routes: Route[],\n    primarySegment: UrlSegmentGroup): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  res[PRIMARY_OUTLET] = primarySegment;\n  primarySegment._sourceSegment = segmentGroup;\n  primarySegment._segmentIndexShift = consumedSegments.length;\n\n  for (const /** @type {?} */ r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      const /** @type {?} */ s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n      s._segmentIndexShift = consumedSegments.length;\n      res[getOutlet(r)] = s;\n    }\n  }\n  return res;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathMatchesWithNamedOutlets(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(\n      r => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathMatches(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r));\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} r\n * @return {?}\n */\nfunction emptyPathMatch(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], r: Route): boolean {\n  if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n\n  return r.path === '' && r.redirectTo === undefined;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getOutlet(route: Route): string {\n  return route.outlet || PRIMARY_OUTLET;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getData(route: Route): Data {\n  return route.data || {};\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getResolve(route: Route): ResolveData {\n  return route.resolve || {};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ActivatedRoute} from './router_state';\nimport {PRIMARY_OUTLET, Params} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree} from './url_tree';\nimport {forEach, last, shallowEqual} from './utils/collection';\n/**\n * @param {?} route\n * @param {?} urlTree\n * @param {?} commands\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nexport function createUrlTree(\n    route: ActivatedRoute, urlTree: UrlTree, commands: any[], queryParams: Params,\n    fragment: string): UrlTree {\n  if (commands.length === 0) {\n    return tree(urlTree.root, urlTree.root, urlTree, queryParams, fragment);\n  }\n\n  const /** @type {?} */ nav = computeNavigation(commands);\n\n  if (nav.toRoot()) {\n    return tree(urlTree.root, new UrlSegmentGroup([], {}), urlTree, queryParams, fragment);\n  }\n\n  const /** @type {?} */ startingPosition = findStartingPosition(nav, urlTree, route);\n\n  const /** @type {?} */ segmentGroup = startingPosition.processChildren ?\n      updateSegmentGroupChildren(\n          startingPosition.segmentGroup, startingPosition.index, nav.commands) :\n      updateSegmentGroup(startingPosition.segmentGroup, startingPosition.index, nav.commands);\n  return tree(startingPosition.segmentGroup, segmentGroup, urlTree, queryParams, fragment);\n}\n/**\n * @param {?} command\n * @return {?}\n */\nfunction isMatrixParams(command: any): boolean {\n  return typeof command === 'object' && command != null && !command.outlets && !command.segmentPath;\n}\n/**\n * @param {?} oldSegmentGroup\n * @param {?} newSegmentGroup\n * @param {?} urlTree\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nfunction tree(\n    oldSegmentGroup: UrlSegmentGroup, newSegmentGroup: UrlSegmentGroup, urlTree: UrlTree,\n    queryParams: Params, fragment: string): UrlTree {\n  let /** @type {?} */ qp: any = {};\n  if (queryParams) {\n    forEach(queryParams, (value: any, name: any) => {\n      qp[name] = Array.isArray(value) ? value.map((v: any) => `${v}`) : `${value}`;\n    });\n  }\n\n  if (urlTree.root === oldSegmentGroup) {\n    return new UrlTree(newSegmentGroup, qp, fragment);\n  }\n\n  return new UrlTree(replaceSegment(urlTree.root, oldSegmentGroup, newSegmentGroup), qp, fragment);\n}\n/**\n * @param {?} current\n * @param {?} oldSegment\n * @param {?} newSegment\n * @return {?}\n */\nfunction replaceSegment(\n    current: UrlSegmentGroup, oldSegment: UrlSegmentGroup,\n    newSegment: UrlSegmentGroup): UrlSegmentGroup {\n  const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n  forEach(current.children, (c: UrlSegmentGroup, outletName: string) => {\n    if (c === oldSegment) {\n      children[outletName] = newSegment;\n    } else {\n      children[outletName] = replaceSegment(c, oldSegment, newSegment);\n    }\n  });\n  return new UrlSegmentGroup(current.segments, children);\n}\nclass Navigation {\n/**\n * @param {?} isAbsolute\n * @param {?} numberOfDoubleDots\n * @param {?} commands\n */\nconstructor(\npublic isAbsolute: boolean,\npublic numberOfDoubleDots: number,\npublic commands: any[]) {\n    if (isAbsolute && commands.length > 0 && isMatrixParams(commands[0])) {\n      throw new Error('Root segment cannot have matrix parameters');\n    }\n\n    const cmdWithOutlet = commands.find(c => typeof c === 'object' && c != null && c.outlets);\n    if (cmdWithOutlet && cmdWithOutlet !== last(commands)) {\n      throw new Error('{outlets:{}} has to be the last command');\n    }\n  }\n/**\n * @return {?}\n */\npublic toRoot(): boolean {\n    return this.isAbsolute && this.commands.length === 1 && this.commands[0] == '/';\n  }\n}\n\nfunction Navigation_tsickle_Closure_declarations() {\n/** @type {?} */\nNavigation.prototype.isAbsolute;\n/** @type {?} */\nNavigation.prototype.numberOfDoubleDots;\n/** @type {?} */\nNavigation.prototype.commands;\n}\n\n/**\n * Transforms commands to a normalized `Navigation`\n * @param {?} commands\n * @return {?}\n */\nfunction computeNavigation(commands: any[]): Navigation {\n  if ((typeof commands[0] === 'string') && commands.length === 1 && commands[0] === '/') {\n    return new Navigation(true, 0, commands);\n  }\n\n  let /** @type {?} */ numberOfDoubleDots = 0;\n  let /** @type {?} */ isAbsolute = false;\n\n  const /** @type {?} */ res: any[] = commands.reduce((res, cmd, cmdIdx) => {\n    if (typeof cmd === 'object' && cmd != null) {\n      if (cmd.outlets) {\n        const /** @type {?} */ outlets: {[k: string]: any} = {};\n        forEach(cmd.outlets, (commands: any, name: string) => {\n          outlets[name] = typeof commands === 'string' ? commands.split('/') : commands;\n        });\n        return [...res, {outlets}];\n      }\n\n      if (cmd.segmentPath) {\n        return [...res, cmd.segmentPath];\n      }\n    }\n\n    if (!(typeof cmd === 'string')) {\n      return [...res, cmd];\n    }\n\n    if (cmdIdx === 0) {\n      cmd.split('/').forEach((urlPart, partIndex) => {\n        if (partIndex == 0 && urlPart === '.') {\n          // skip './a'\n        } else if (partIndex == 0 && urlPart === '') {  //  '/a'\n          isAbsolute = true;\n        } else if (urlPart === '..') {  //  '../a'\n          numberOfDoubleDots++;\n        } else if (urlPart != '') {\n          res.push(urlPart);\n        }\n      });\n\n      return res;\n    }\n\n    return [...res, cmd];\n  }, []);\n\n  return new Navigation(isAbsolute, numberOfDoubleDots, res);\n}\nclass Position {\n/**\n * @param {?} segmentGroup\n * @param {?} processChildren\n * @param {?} index\n */\nconstructor(\npublic segmentGroup: UrlSegmentGroup,\npublic processChildren: boolean,\npublic index: number) {\n  }\n}\n\nfunction Position_tsickle_Closure_declarations() {\n/** @type {?} */\nPosition.prototype.segmentGroup;\n/** @type {?} */\nPosition.prototype.processChildren;\n/** @type {?} */\nPosition.prototype.index;\n}\n\n/**\n * @param {?} nav\n * @param {?} tree\n * @param {?} route\n * @return {?}\n */\nfunction findStartingPosition(nav: Navigation, tree: UrlTree, route: ActivatedRoute): Position {\n  if (nav.isAbsolute) {\n    return new Position(tree.root, true, 0);\n  }\n\n  if (route.snapshot._lastPathIndex === -1) {\n    return new Position(route.snapshot._urlSegment, true, 0);\n  }\n\n  const /** @type {?} */ modifier = isMatrixParams(nav.commands[0]) ? 0 : 1;\n  const /** @type {?} */ index = route.snapshot._lastPathIndex + modifier;\n  return createPositionApplyingDoubleDots(\n      route.snapshot._urlSegment, index, nav.numberOfDoubleDots);\n}\n/**\n * @param {?} group\n * @param {?} index\n * @param {?} numberOfDoubleDots\n * @return {?}\n */\nfunction createPositionApplyingDoubleDots(\n    group: UrlSegmentGroup, index: number, numberOfDoubleDots: number): Position {\n  let /** @type {?} */ g = group;\n  let /** @type {?} */ ci = index;\n  let /** @type {?} */ dd = numberOfDoubleDots;\n  while (dd > ci) {\n    dd -= ci;\n    g = /** @type {?} */(( g.parent));\n    if (!g) {\n      throw new Error('Invalid number of \\'../\\'');\n    }\n    ci = g.segments.length;\n  }\n  return new Position(g, false, ci - dd);\n}\n/**\n * @param {?} command\n * @return {?}\n */\nfunction getPath(command: any): any {\n  if (typeof command === 'object' && command != null && command.outlets) {\n    return command.outlets[PRIMARY_OUTLET];\n  }\n  return `${command}`;\n}\n/**\n * @param {?} commands\n * @return {?}\n */\nfunction getOutlets(commands: any[]): {[k: string]: any[]} {\n  if (!(typeof commands[0] === 'object')) return {[PRIMARY_OUTLET]: commands};\n  if (commands[0].outlets === undefined) return {[PRIMARY_OUTLET]: commands};\n  return commands[0].outlets;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction updateSegmentGroup(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  if (!segmentGroup) {\n    segmentGroup = new UrlSegmentGroup([], {});\n  }\n  if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n    return updateSegmentGroupChildren(segmentGroup, startIndex, commands);\n  }\n\n  const /** @type {?} */ m = prefixedWith(segmentGroup, startIndex, commands);\n  const /** @type {?} */ slicedCommands = commands.slice(m.commandIndex);\n  if (m.match && m.pathIndex < segmentGroup.segments.length) {\n    const /** @type {?} */ g = new UrlSegmentGroup(segmentGroup.segments.slice(0, m.pathIndex), {});\n    g.children[PRIMARY_OUTLET] =\n        new UrlSegmentGroup(segmentGroup.segments.slice(m.pathIndex), segmentGroup.children);\n    return updateSegmentGroupChildren(g, 0, slicedCommands);\n  } else if (m.match && slicedCommands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else if (m.match && !segmentGroup.hasChildren()) {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  } else if (m.match) {\n    return updateSegmentGroupChildren(segmentGroup, 0, slicedCommands);\n  } else {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  }\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction updateSegmentGroupChildren(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  if (commands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else {\n    const /** @type {?} */ outlets = getOutlets(commands);\n    const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n\n    forEach(outlets, (commands: any, outlet: string) => {\n      if (commands !== null) {\n        children[outlet] = updateSegmentGroup(segmentGroup.children[outlet], startIndex, commands);\n      }\n    });\n\n    forEach(segmentGroup.children, (child: UrlSegmentGroup, childOutlet: string) => {\n      if (outlets[childOutlet] === undefined) {\n        children[childOutlet] = child;\n      }\n    });\n    return new UrlSegmentGroup(segmentGroup.segments, children);\n  }\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction prefixedWith(segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]) {\n  let /** @type {?} */ currentCommandIndex = 0;\n  let /** @type {?} */ currentPathIndex = startIndex;\n\n  const /** @type {?} */ noMatch = {match: false, pathIndex: 0, commandIndex: 0};\n  while (currentPathIndex < segmentGroup.segments.length) {\n    if (currentCommandIndex >= commands.length) return noMatch;\n    const /** @type {?} */ path = segmentGroup.segments[currentPathIndex];\n    const /** @type {?} */ curr = getPath(commands[currentCommandIndex]);\n    const /** @type {?} */ next =\n        currentCommandIndex < commands.length - 1 ? commands[currentCommandIndex + 1] : null;\n\n    if (currentPathIndex > 0 && curr === undefined) break;\n\n    if (curr && next && (typeof next === 'object') && next.outlets === undefined) {\n      if (!compare(curr, next, path)) return noMatch;\n      currentCommandIndex += 2;\n    } else {\n      if (!compare(curr, {}, path)) return noMatch;\n      currentCommandIndex++;\n    }\n    currentPathIndex++;\n  }\n\n  return {match: true, pathIndex: currentPathIndex, commandIndex: currentCommandIndex};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction createNewSegmentGroup(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  const /** @type {?} */ paths = segmentGroup.segments.slice(0, startIndex);\n\n  let /** @type {?} */ i = 0;\n  while (i < commands.length) {\n    if (typeof commands[i] === 'object' && commands[i].outlets !== undefined) {\n      const /** @type {?} */ children = createNewSegmentChildren(commands[i].outlets);\n      return new UrlSegmentGroup(paths, children);\n    }\n\n    // if we start with an object literal, we need to reuse the path part from the segment\n    if (i === 0 && isMatrixParams(commands[0])) {\n      const /** @type {?} */ p = segmentGroup.segments[startIndex];\n      paths.push(new UrlSegment(p.path, commands[0]));\n      i++;\n      continue;\n    }\n\n    const /** @type {?} */ curr = getPath(commands[i]);\n    const /** @type {?} */ next = (i < commands.length - 1) ? commands[i + 1] : null;\n    if (curr && next && isMatrixParams(next)) {\n      paths.push(new UrlSegment(curr, stringify(next)));\n      i += 2;\n    } else {\n      paths.push(new UrlSegment(curr, {}));\n      i++;\n    }\n  }\n  return new UrlSegmentGroup(paths, {});\n}\n/**\n * @param {?} outlets\n * @return {?}\n */\nfunction createNewSegmentChildren(outlets: {[name: string]: any}): any {\n  const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n  forEach(outlets, (commands: any, outlet: string) => {\n    if (commands !== null) {\n      children[outlet] = createNewSegmentGroup(new UrlSegmentGroup([], {}), 0, commands);\n    }\n  });\n  return children;\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction stringify(params: {[key: string]: any}): {[key: string]: string} {\n  const /** @type {?} */ res: {[key: string]: string} = {};\n  forEach(params, (v: any, k: string) => res[k] = `${v}`);\n  return res;\n}\n/**\n * @param {?} path\n * @param {?} params\n * @param {?} segment\n * @return {?}\n */\nfunction compare(path: string, params: {[key: string]: any}, segment: UrlSegment): boolean {\n  return path == segment.path && shallowEqual(params, segment.parameters);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\n\nimport {DetachedRouteHandleInternal, RouteReuseStrategy} from './route_reuse_strategy';\nimport {ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot} from './router_state';\nimport {TreeNode} from './utils/tree';\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?} prevState\n * @return {?}\n */\nexport function createRouterState(\n    routeReuseStrategy: RouteReuseStrategy, curr: RouterStateSnapshot,\n    prevState: RouterState): RouterState {\n  const /** @type {?} */ root = createNode(routeReuseStrategy, curr._root, prevState ? prevState._root : undefined);\n  return new RouterState(root, curr);\n}\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?=} prevState\n * @return {?}\n */\nfunction createNode(\n    routeReuseStrategy: RouteReuseStrategy, curr: TreeNode<ActivatedRouteSnapshot>,\n    prevState?: TreeNode<ActivatedRoute>): TreeNode<ActivatedRoute> {\n  // reuse an activated route that is currently displayed on the screen\n  if (prevState && routeReuseStrategy.shouldReuseRoute(curr.value, prevState.value.snapshot)) {\n    const /** @type {?} */ value = prevState.value;\n    value._futureSnapshot = curr.value;\n    const /** @type {?} */ children = createOrReuseChildren(routeReuseStrategy, curr, prevState);\n    return new TreeNode<ActivatedRoute>(value, children);\n\n    // retrieve an activated route that is used to be displayed, but is not currently displayed\n  } else if (routeReuseStrategy.retrieve(curr.value)) {\n    const /** @type {?} */ tree: TreeNode<ActivatedRoute> =\n        ( /** @type {?} */((<DetachedRouteHandleInternal>routeReuseStrategy.retrieve(curr.value)))).route;\n    setFutureSnapshotsOfActivatedRoutes(curr, tree);\n    return tree;\n\n  } else {\n    const /** @type {?} */ value = createActivatedRoute(curr.value);\n    const /** @type {?} */ children = curr.children.map(c => createNode(routeReuseStrategy, c));\n    return new TreeNode<ActivatedRoute>(value, children);\n  }\n}\n/**\n * @param {?} curr\n * @param {?} result\n * @return {?}\n */\nfunction setFutureSnapshotsOfActivatedRoutes(\n    curr: TreeNode<ActivatedRouteSnapshot>, result: TreeNode<ActivatedRoute>): void {\n  if (curr.value.routeConfig !== result.value.routeConfig) {\n    throw new Error('Cannot reattach ActivatedRouteSnapshot created from a different route');\n  }\n  if (curr.children.length !== result.children.length) {\n    throw new Error('Cannot reattach ActivatedRouteSnapshot with a different number of children');\n  }\n  result.value._futureSnapshot = curr.value;\n  for (let /** @type {?} */ i = 0; i < curr.children.length; ++i) {\n    setFutureSnapshotsOfActivatedRoutes(curr.children[i], result.children[i]);\n  }\n}\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?} prevState\n * @return {?}\n */\nfunction createOrReuseChildren(\n    routeReuseStrategy: RouteReuseStrategy, curr: TreeNode<ActivatedRouteSnapshot>,\n    prevState: TreeNode<ActivatedRoute>) {\n  return curr.children.map(child => {\n    for (const /** @type {?} */ p of prevState.children) {\n      if (routeReuseStrategy.shouldReuseRoute(p.value.snapshot, child.value)) {\n        return createNode(routeReuseStrategy, child, p);\n      }\n    }\n    return createNode(routeReuseStrategy, child);\n  });\n}\n/**\n * @param {?} c\n * @return {?}\n */\nfunction createActivatedRoute(c: ActivatedRouteSnapshot) {\n  return new ActivatedRoute(\n      new BehaviorSubject(c.url), new BehaviorSubject(c.params), new BehaviorSubject(c.queryParams),\n      new BehaviorSubject(c.fragment), new BehaviorSubject(c.data), c.outlet, c.component, c);\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Observable} from 'rxjs/Observable';\nimport {map} from 'rxjs/operator/map';\n\nimport {Data, ResolveData, Route} from './config';\nimport {PRIMARY_OUTLET, ParamMap, Params, convertToParamMap} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree, equalSegments} from './url_tree';\nimport {shallowEqual, shallowEqualArrays} from './utils/collection';\nimport {Tree, TreeNode} from './utils/tree';\n/**\n * \\@whatItDoes Represents the state of the router.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const root: ActivatedRoute = state.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n * \n * \\@description \n * RouterState is a tree of activated routes. Every node in this tree knows about the \"consumed\" URL\n * segments, the extracted parameters, and the resolved data.\n * \n * See {\\@link ActivatedRoute} for more information.\n * \n * \\@stable\n */\nexport class RouterState extends Tree<ActivatedRoute> {\n/**\n * \\@internal\n * @param {?} root\n * @param {?} snapshot\n */\nconstructor(\n      root: TreeNode<ActivatedRoute>,\npublic snapshot: RouterStateSnapshot) {\n    super(root);\n    setRouterState(<RouterState>this, root);\n  }\n/**\n * @return {?}\n */\ntoString(): string { return this.snapshot.toString(); }\n}\n\nfunction RouterState_tsickle_Closure_declarations() {\n/**\n * The current snapshot of the router state\n * @type {?}\n */\nRouterState.prototype.snapshot;\n}\n\n/**\n * @param {?} urlTree\n * @param {?} rootComponent\n * @return {?}\n */\nexport function createEmptyState(urlTree: UrlTree, rootComponent: Type<any>| null): RouterState {\n  const /** @type {?} */ snapshot = createEmptyStateSnapshot(urlTree, rootComponent);\n  const /** @type {?} */ emptyUrl = new BehaviorSubject([new UrlSegment('', {})]);\n  const /** @type {?} */ emptyParams = new BehaviorSubject({});\n  const /** @type {?} */ emptyData = new BehaviorSubject({});\n  const /** @type {?} */ emptyQueryParams = new BehaviorSubject({});\n  const /** @type {?} */ fragment = new BehaviorSubject('');\n  const /** @type {?} */ activated = new ActivatedRoute(\n      emptyUrl, emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent,\n      snapshot.root);\n  activated.snapshot = snapshot.root;\n  return new RouterState(new TreeNode<ActivatedRoute>(activated, []), snapshot);\n}\n/**\n * @param {?} urlTree\n * @param {?} rootComponent\n * @return {?}\n */\nexport function createEmptyStateSnapshot(\n    urlTree: UrlTree, rootComponent: Type<any>| null): RouterStateSnapshot {\n  const /** @type {?} */ emptyParams = {};\n  const /** @type {?} */ emptyData = {};\n  const /** @type {?} */ emptyQueryParams = {};\n  const /** @type {?} */ fragment = '';\n  const /** @type {?} */ activated = new ActivatedRouteSnapshot(\n      [], emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, null,\n      urlTree.root, -1, {});\n  return new RouterStateSnapshot('', new TreeNode<ActivatedRouteSnapshot>(activated, []));\n}\n/**\n * \\@whatItDoes Contains the information about a route associated with a component loaded in an\n * outlet.\n * An `ActivatedRoute` can also be used to traverse the router state tree.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({...}) \n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: Observable<string> = route.params.map(p => p.id);\n *     const url: Observable<string> = route.url.map(segments => segments.join(''));\n *     // route.data includes both `data` and `resolve`\n *     const user = route.data.map(d => d.user);\n *   }\n * }\n * ```\n * \n * \\@stable\n */\nexport class ActivatedRoute {\n/**\n * The current snapshot of this route\n */\nsnapshot: ActivatedRouteSnapshot;\n/**\n * \\@internal\n */\n_futureSnapshot: ActivatedRouteSnapshot;\n/**\n * \\@internal\n */\n_routerState: RouterState;\n/**\n * \\@internal\n */\n_paramMap: Observable<ParamMap>;\n/**\n * \\@internal\n */\n_queryParamMap: Observable<ParamMap>;\n/**\n * \\@internal\n * @param {?} url\n * @param {?} params\n * @param {?} queryParams\n * @param {?} fragment\n * @param {?} data\n * @param {?} outlet\n * @param {?} component\n * @param {?} futureSnapshot\n */\nconstructor(\npublic url: Observable<UrlSegment[]>,\npublic params: Observable<Params>,\npublic queryParams: Observable<Params>,\npublic fragment: Observable<string>,\npublic data: Observable<Data>,\npublic outlet: string,\npublic component: Type<any>|string|null, futureSnapshot: ActivatedRouteSnapshot) {\n    this._futureSnapshot = futureSnapshot;\n  }\n/**\n * The configuration used to match this route\n * @return {?}\n */\nget routeConfig(): Route|null { return this._futureSnapshot.routeConfig; }\n/**\n * The root of the router state\n * @return {?}\n */\nget root(): ActivatedRoute { return this._routerState.root; }\n/**\n * The parent of this route in the router state tree\n * @return {?}\n */\nget parent(): ActivatedRoute|null { return this._routerState.parent(this); }\n/**\n * The first child of this route in the router state tree\n * @return {?}\n */\nget firstChild(): ActivatedRoute|null { return this._routerState.firstChild(this); }\n/**\n * The children of this route in the router state tree\n * @return {?}\n */\nget children(): ActivatedRoute[] { return this._routerState.children(this); }\n/**\n * The path from the root of the router state tree to this route\n * @return {?}\n */\nget pathFromRoot(): ActivatedRoute[] { return this._routerState.pathFromRoot(this); }\n/**\n * @return {?}\n */\nget paramMap(): Observable<ParamMap> {\n    if (!this._paramMap) {\n      this._paramMap = map.call(this.params, (p: Params): ParamMap => convertToParamMap(p));\n    }\n    return this._paramMap;\n  }\n/**\n * @return {?}\n */\nget queryParamMap(): Observable<ParamMap> {\n    if (!this._queryParamMap) {\n      this._queryParamMap =\n          map.call(this.queryParams, (p: Params): ParamMap => convertToParamMap(p));\n    }\n    return this._queryParamMap;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    return this.snapshot ? this.snapshot.toString() : `Future(${this._futureSnapshot})`;\n  }\n}\n\nfunction ActivatedRoute_tsickle_Closure_declarations() {\n/**\n * The current snapshot of this route\n * @type {?}\n */\nActivatedRoute.prototype.snapshot;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._futureSnapshot;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._routerState;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._paramMap;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._queryParamMap;\n/**\n * An observable of the URL segments matched by this route\n * @type {?}\n */\nActivatedRoute.prototype.url;\n/**\n * An observable of the matrix parameters scoped to this route\n * @type {?}\n */\nActivatedRoute.prototype.params;\n/**\n * An observable of the query parameters shared by all the routes\n * @type {?}\n */\nActivatedRoute.prototype.queryParams;\n/**\n * An observable of the URL fragment shared by all the routes\n * @type {?}\n */\nActivatedRoute.prototype.fragment;\n/**\n * An observable of the static and resolved data of this route.\n * @type {?}\n */\nActivatedRoute.prototype.data;\n/**\n * The outlet name of the route. It's a constant\n * @type {?}\n */\nActivatedRoute.prototype.outlet;\n/** @type {?} */\nActivatedRoute.prototype.component;\n}\n\n\n/** @internal */\nexport type Inherited = {\n  params: Params,\n  data: Data,\n  resolve: Data,\n};\n/**\n * \\@internal\n * @param {?} route\n * @return {?}\n */\nexport function inheritedParamsDataResolve(route: ActivatedRouteSnapshot): Inherited {\n  const /** @type {?} */ pathToRoot = route.pathFromRoot;\n\n  let /** @type {?} */ inhertingStartingFrom = pathToRoot.length - 1;\n\n  while (inhertingStartingFrom >= 1) {\n    const /** @type {?} */ current = pathToRoot[inhertingStartingFrom];\n    const /** @type {?} */ parent = pathToRoot[inhertingStartingFrom - 1];\n    // current route is an empty path => inherits its parent's params and data\n    if (current.routeConfig && current.routeConfig.path === '') {\n      inhertingStartingFrom--;\n\n      // parent is componentless => current route should inherit its params and data\n    } else if (!parent.component) {\n      inhertingStartingFrom--;\n\n    } else {\n      break;\n    }\n  }\n\n  return pathToRoot.slice(inhertingStartingFrom).reduce((res, curr) => {\n    const /** @type {?} */ params = {...res.params, ...curr.params};\n    const /** @type {?} */ data = {...res.data, ...curr.data};\n    const /** @type {?} */ resolve = {...res.resolve, ...curr._resolvedData};\n    return {params, data, resolve};\n  }, /** @type {?} */(( <any>{params: {}, data: {}, resolve: {}})));\n}\n/**\n * \\@whatItDoes Contains the information about a route associated with a component loaded in an\n * outlet\n * at a particular moment in time. ActivatedRouteSnapshot can also be used to traverse the router\n * state tree.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'./my-component.html'}) \n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: string = route.snapshot.params.id;\n *     const url: string = route.snapshot.url.join('');\n *     const user = route.snapshot.data.user;\n *   }\n * }\n * ```\n * \n * \\@stable\n */\nexport class ActivatedRouteSnapshot {\n/**\n * \\@internal *\n */\n_routeConfig: Route|null;\n/**\n * \\@internal *\n */\n_urlSegment: UrlSegmentGroup;\n/**\n * \\@internal\n */\n_lastPathIndex: number;\n/**\n * \\@internal\n */\n_resolve: ResolveData;\n/**\n * \\@internal\n */\n_resolvedData: Data;\n/**\n * \\@internal\n */\n_routerState: RouterStateSnapshot;\n/**\n * \\@internal\n */\n_paramMap: ParamMap;\n/**\n * \\@internal\n */\n_queryParamMap: ParamMap;\n/**\n * \\@internal\n * @param {?} url\n * @param {?} params\n * @param {?} queryParams\n * @param {?} fragment\n * @param {?} data\n * @param {?} outlet\n * @param {?} component\n * @param {?} routeConfig\n * @param {?} urlSegment\n * @param {?} lastPathIndex\n * @param {?} resolve\n */\nconstructor(\npublic url: UrlSegment[],\npublic params: Params,\npublic queryParams: Params,\npublic fragment: string,\npublic data: Data,\npublic outlet: string,\npublic component: Type<any>|string|null, routeConfig: Route|null, urlSegment: UrlSegmentGroup,\n      lastPathIndex: number, resolve: ResolveData) {\n    this._routeConfig = routeConfig;\n    this._urlSegment = urlSegment;\n    this._lastPathIndex = lastPathIndex;\n    this._resolve = resolve;\n  }\n/**\n * The configuration used to match this route\n * @return {?}\n */\nget routeConfig(): Route|null { return this._routeConfig; }\n/**\n * The root of the router state\n * @return {?}\n */\nget root(): ActivatedRouteSnapshot { return this._routerState.root; }\n/**\n * The parent of this route in the router state tree\n * @return {?}\n */\nget parent(): ActivatedRouteSnapshot|null { return this._routerState.parent(this); }\n/**\n * The first child of this route in the router state tree\n * @return {?}\n */\nget firstChild(): ActivatedRouteSnapshot|null { return this._routerState.firstChild(this); }\n/**\n * The children of this route in the router state tree\n * @return {?}\n */\nget children(): ActivatedRouteSnapshot[] { return this._routerState.children(this); }\n/**\n * The path from the root of the router state tree to this route\n * @return {?}\n */\nget pathFromRoot(): ActivatedRouteSnapshot[] { return this._routerState.pathFromRoot(this); }\n/**\n * @return {?}\n */\nget paramMap(): ParamMap {\n    if (!this._paramMap) {\n      this._paramMap = convertToParamMap(this.params);\n    }\n    return this._paramMap;\n  }\n/**\n * @return {?}\n */\nget queryParamMap(): ParamMap {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n    return this._queryParamMap;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    const /** @type {?} */ url = this.url.map(segment => segment.toString()).join('/');\n    const /** @type {?} */ matched = this._routeConfig ? this._routeConfig.path : '';\n    return `Route(url:'${url}', path:'${matched}')`;\n  }\n}\n\nfunction ActivatedRouteSnapshot_tsickle_Closure_declarations() {\n/**\n * \\@internal *\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._routeConfig;\n/**\n * \\@internal *\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._urlSegment;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._lastPathIndex;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._resolve;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._resolvedData;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._routerState;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._paramMap;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._queryParamMap;\n/**\n * The URL segments matched by this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.url;\n/**\n * The matrix parameters scoped to this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.params;\n/**\n * The query parameters shared by all the routes\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.queryParams;\n/**\n * The URL fragment shared by all the routes\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.fragment;\n/**\n * The static and resolved data of this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.data;\n/**\n * The outlet name of the route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.outlet;\n/**\n * The component of the route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.component;\n}\n\n/**\n * \\@whatItDoes Represents the state of the router at a moment in time.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const snapshot: RouterStateSnapshot = state.snapshot;\n *     const root: ActivatedRouteSnapshot = snapshot.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n * \n * \\@description \n * RouterStateSnapshot is a tree of activated route snapshots. Every node in this tree knows about\n * the \"consumed\" URL segments, the extracted parameters, and the resolved data.\n * \n * \\@stable\n */\nexport class RouterStateSnapshot extends Tree<ActivatedRouteSnapshot> {\n/**\n * \\@internal\n * @param {?} url\n * @param {?} root\n */\nconstructor(\npublic url: string, root: TreeNode<ActivatedRouteSnapshot>) {\n    super(root);\n    setRouterState(<RouterStateSnapshot>this, root);\n  }\n/**\n * @return {?}\n */\ntoString(): string { return serializeNode(this._root); }\n}\n\nfunction RouterStateSnapshot_tsickle_Closure_declarations() {\n/**\n * The url from which this snapshot was created\n * @type {?}\n */\nRouterStateSnapshot.prototype.url;\n}\n\n/**\n * @template U, T\n * @param {?} state\n * @param {?} node\n * @return {?}\n */\nfunction setRouterState<U, T extends{_routerState: U}>(state: U, node: TreeNode<T>): void {\n  node.value._routerState = state;\n  node.children.forEach(c => setRouterState(state, c));\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction serializeNode(node: TreeNode<ActivatedRouteSnapshot>): string {\n  const /** @type {?} */ c = node.children.length > 0 ? ` { ${node.children.map(serializeNode).join(\", \")} } ` : '';\n  return `${node.value}${c}`;\n}\n/**\n * The expectation is that the activate route is created with the right set of parameters.\n * So we push new values into the observables only when they are not the initial values.\n * And we detect that by checking if the snapshot field is set.\n * @param {?} route\n * @return {?}\n */\nexport function advanceActivatedRoute(route: ActivatedRoute): void {\n  if (route.snapshot) {\n    const /** @type {?} */ currentSnapshot = route.snapshot;\n    const /** @type {?} */ nextSnapshot = route._futureSnapshot;\n    route.snapshot = nextSnapshot;\n    if (!shallowEqual(currentSnapshot.queryParams, nextSnapshot.queryParams)) {\n      ( /** @type {?} */((<any>route.queryParams))).next(nextSnapshot.queryParams);\n    }\n    if (currentSnapshot.fragment !== nextSnapshot.fragment) {\n      ( /** @type {?} */((<any>route.fragment))).next(nextSnapshot.fragment);\n    }\n    if (!shallowEqual(currentSnapshot.params, nextSnapshot.params)) {\n      ( /** @type {?} */((<any>route.params))).next(nextSnapshot.params);\n    }\n    if (!shallowEqualArrays(currentSnapshot.url, nextSnapshot.url)) {\n      ( /** @type {?} */((<any>route.url))).next(nextSnapshot.url);\n    }\n    if (!shallowEqual(currentSnapshot.data, nextSnapshot.data)) {\n      ( /** @type {?} */((<any>route.data))).next(nextSnapshot.data);\n    }\n  } else {\n    route.snapshot = route._futureSnapshot;\n\n    // this is for resolved data\n    ( /** @type {?} */((<any>route.data))).next(route._futureSnapshot.data);\n  }\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function equalParamsAndUrlSegments(\n    a: ActivatedRouteSnapshot, b: ActivatedRouteSnapshot): boolean {\n  const /** @type {?} */ equalUrlParams = shallowEqual(a.params, b.params) && equalSegments(a.url, b.url);\n  const /** @type {?} */ parentsMismatch = !a.parent !== !b.parent;\n\n  return equalUrlParams && !parentsMismatch &&\n      (!a.parent || equalParamsAndUrlSegments(a.parent, /** @type {?} */(( b.parent))));\n}", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport class Tree<T> {\n/**\n * \\@internal\n */\n_root: TreeNode<T>;\n/**\n * @param {?} root\n */\nconstructor(root: TreeNode<T>) { this._root = root; }\n/**\n * @return {?}\n */\nget root(): T { return this._root.value; }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nparent(t: T): T|null {\n    const /** @type {?} */ p = this.pathFromRoot(t);\n    return p.length > 1 ? p[p.length - 2] : null;\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nchildren(t: T): T[] {\n    const /** @type {?} */ n = findNode(t, this._root);\n    return n ? n.children.map(t => t.value) : [];\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nfirstChild(t: T): T|null {\n    const /** @type {?} */ n = findNode(t, this._root);\n    return n && n.children.length > 0 ? n.children[0].value : null;\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nsiblings(t: T): T[] {\n    const /** @type {?} */ p = findPath(t, this._root);\n    if (p.length < 2) return [];\n\n    const /** @type {?} */ c = p[p.length - 2].children.map(c => c.value);\n    return c.filter(cc => cc !== t);\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\npathFromRoot(t: T): T[] { return findPath(t, this._root).map(s => s.value); }\n}\n\nfunction Tree_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nTree.prototype._root;\n}\n\n/**\n * @template T\n * @param {?} value\n * @param {?} node\n * @return {?}\n */\nfunction findNode<T>(value: T, node: TreeNode<T>): TreeNode<T>|null {\n  if (value === node.value) return node;\n\n  for (const /** @type {?} */ child of node.children) {\n    const /** @type {?} */ node = findNode(value, child);\n    if (node) return node;\n  }\n\n  return null;\n}\n/**\n * @template T\n * @param {?} value\n * @param {?} node\n * @return {?}\n */\nfunction findPath<T>(value: T, node: TreeNode<T>): TreeNode<T>[] {\n  if (value === node.value) return [node];\n\n  for (const /** @type {?} */ child of node.children) {\n    const /** @type {?} */ path = findPath(value, child);\n    if (path.length) {\n      path.unshift(node);\n      return path;\n    }\n  }\n\n  return [];\n}\nexport class TreeNode<T> {\n/**\n * @param {?} value\n * @param {?} children\n */\nconstructor(public value: T,\npublic children: TreeNode<T>[]) {}\n/**\n * @return {?}\n */\ntoString(): string { return `TreeNode(${this.value})`; }\n}\n\nfunction TreeNode_tsickle_Closure_declarations() {\n/** @type {?} */\nTreeNode.prototype.value;\n/** @type {?} */\nTreeNode.prototype.children;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injector, NgModuleRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {_catch} from 'rxjs/operator/catch';\nimport {concatAll} from 'rxjs/operator/concatAll';\nimport {first} from 'rxjs/operator/first';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {EmptyError} from 'rxjs/util/EmptyError';\n\nimport {LoadedRouterConfig, Route, Routes} from './config';\nimport {RouterConfigLoader} from './router_config_loader';\nimport {PRIMARY_OUTLET, Params, defaultUrlMatcher, navigationCancelingError} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree} from './url_tree';\nimport {andObservables, forEach, waitForMap, wrapIntoObservable} from './utils/collection';\nclass NoMatch {\npublic segmentGroup: UrlSegmentGroup|null;\n/**\n * @param {?=} segmentGroup\n */\nconstructor(segmentGroup?: UrlSegmentGroup) { this.segmentGroup = segmentGroup || null; }\n}\n\nfunction NoMatch_tsickle_Closure_declarations() {\n/** @type {?} */\nNoMatch.prototype.segmentGroup;\n}\n\nclass AbsoluteRedirect {\n/**\n * @param {?} urlTree\n */\nconstructor(public urlTree: UrlTree) {}\n}\n\nfunction AbsoluteRedirect_tsickle_Closure_declarations() {\n/** @type {?} */\nAbsoluteRedirect.prototype.urlTree;\n}\n\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction noMatch(segmentGroup: UrlSegmentGroup): Observable<UrlSegmentGroup> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new NoMatch(segmentGroup)));\n}\n/**\n * @param {?} newTree\n * @return {?}\n */\nfunction absoluteRedirect(newTree: UrlTree): Observable<any> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new AbsoluteRedirect(newTree)));\n}\n/**\n * @param {?} redirectTo\n * @return {?}\n */\nfunction namedOutletsRedirect(redirectTo: string): Observable<any> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new Error(\n          `Only absolute redirects can have named outlets. redirectTo: '${redirectTo}'`)));\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction canLoadFails(route: Route): Observable<LoadedRouterConfig> {\n  return new Observable<LoadedRouterConfig>(\n      (obs: Observer<LoadedRouterConfig>) => obs.error(navigationCancelingError(\n          `Cannot load children because the guard of the route \"path: '${route.path}'\" returned false`)));\n}\n/**\n * Returns the `UrlTree` with the redirection applied.\n * \n * Lazy modules are loaded along the way.\n * @param {?} moduleInjector\n * @param {?} configLoader\n * @param {?} urlSerializer\n * @param {?} urlTree\n * @param {?} config\n * @return {?}\n */\nexport function applyRedirects(\n    moduleInjector: Injector, configLoader: RouterConfigLoader, urlSerializer: UrlSerializer,\n    urlTree: UrlTree, config: Routes): Observable<UrlTree> {\n  return new ApplyRedirects(moduleInjector, configLoader, urlSerializer, urlTree, config).apply();\n}\nclass ApplyRedirects {\nprivate allowRedirects: boolean = true;\nprivate ngModule: NgModuleRef<any>;\n/**\n * @param {?} moduleInjector\n * @param {?} configLoader\n * @param {?} urlSerializer\n * @param {?} urlTree\n * @param {?} config\n */\nconstructor(\n      moduleInjector: Injector,\nprivate configLoader: RouterConfigLoader,\nprivate urlSerializer: UrlSerializer,\nprivate urlTree: UrlTree,\nprivate config: Routes) {\n    this.ngModule = moduleInjector.get(NgModuleRef);\n  }\n/**\n * @return {?}\n */\napply(): Observable<UrlTree> {\n    const /** @type {?} */ expanded$ =\n        this.expandSegmentGroup(this.ngModule, this.config, this.urlTree.root, PRIMARY_OUTLET);\n    const /** @type {?} */ urlTrees$ = map.call(\n        expanded$, (rootSegmentGroup: UrlSegmentGroup) => this.createUrlTree(\n                       rootSegmentGroup, this.urlTree.queryParams, /** @type {?} */(( this.urlTree.fragment))));\n    return _catch.call(urlTrees$, (e: any) => {\n      if (e instanceof AbsoluteRedirect) {\n        // after an absolute redirect we do not apply any more redirects!\n        this.allowRedirects = false;\n        // we need to run matching, so we can fetch all lazy-loaded modules\n        return this.match(e.urlTree);\n      }\n\n      if (e instanceof NoMatch) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} tree\n * @return {?}\n */\nprivate match(tree: UrlTree): Observable<UrlTree> {\n    const /** @type {?} */ expanded$ =\n        this.expandSegmentGroup(this.ngModule, this.config, tree.root, PRIMARY_OUTLET);\n    const /** @type {?} */ mapped$ = map.call(\n        expanded$, (rootSegmentGroup: UrlSegmentGroup) =>\n                       this.createUrlTree(rootSegmentGroup, tree.queryParams, /** @type {?} */(( tree.fragment))));\n    return _catch.call(mapped$, (e: any): Observable<UrlTree> => {\n      if (e instanceof NoMatch) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} e\n * @return {?}\n */\nprivate noMatchError(e: NoMatch): any {\n    return new Error(`Cannot match any routes. URL Segment: '${e.segmentGroup}'`);\n  }\n/**\n * @param {?} rootCandidate\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nprivate createUrlTree(rootCandidate: UrlSegmentGroup, queryParams: Params, fragment: string):\n      UrlTree {\n    const /** @type {?} */ root = rootCandidate.segments.length > 0 ?\n        new UrlSegmentGroup([], {[PRIMARY_OUTLET]: rootCandidate}) :\n        rootCandidate;\n    return new UrlTree(root, queryParams, fragment);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} segmentGroup\n * @param {?} outlet\n * @return {?}\n */\nprivate expandSegmentGroup(\n      ngModule: NgModuleRef<any>, routes: Route[], segmentGroup: UrlSegmentGroup,\n      outlet: string): Observable<UrlSegmentGroup> {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return map.call(\n          this.expandChildren(ngModule, routes, segmentGroup),\n          (children: any) => new UrlSegmentGroup([], children));\n    }\n\n    return this.expandSegment(ngModule, segmentGroup, routes, segmentGroup.segments, outlet, true);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} segmentGroup\n * @return {?}\n */\nprivate expandChildren(\n      ngModule: NgModuleRef<any>, routes: Route[],\n      segmentGroup: UrlSegmentGroup): Observable<{[name: string]: UrlSegmentGroup}> {\n    return waitForMap(\n        segmentGroup.children,\n        (childOutlet, child) => this.expandSegmentGroup(ngModule, routes, child, childOutlet));\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} segments\n * @param {?} outlet\n * @param {?} allowRedirects\n * @return {?}\n */\nprivate expandSegment(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[],\n      segments: UrlSegment[], outlet: string,\n      allowRedirects: boolean): Observable<UrlSegmentGroup> {\n    const /** @type {?} */ routes$ = of (...routes);\n    const /** @type {?} */ processedRoutes$ = map.call(routes$, (r: any) => {\n      const /** @type {?} */ expanded$ = this.expandSegmentAgainstRoute(\n          ngModule, segmentGroup, routes, r, segments, outlet, allowRedirects);\n      return _catch.call(expanded$, (e: any) => {\n        if (e instanceof NoMatch) {\n          return of (null);\n        }\n\n        throw e;\n      });\n    });\n    const /** @type {?} */ concattedProcessedRoutes$ = concatAll.call(processedRoutes$);\n    const /** @type {?} */ first$ = first.call(concattedProcessedRoutes$, (s: any) => !!s);\n    return _catch.call(first$, (e: any, _: any): Observable<UrlSegmentGroup> => {\n      if (e instanceof EmptyError) {\n        if (this.noLeftoversInUrl(segmentGroup, segments, outlet)) {\n          return of (new UrlSegmentGroup([], {}));\n        }\n\n        throw new NoMatch(segmentGroup);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate noLeftoversInUrl(segmentGroup: UrlSegmentGroup, segments: UrlSegment[], outlet: string):\n      boolean {\n    return segments.length === 0 && !segmentGroup.children[outlet];\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} paths\n * @param {?} outlet\n * @param {?} allowRedirects\n * @return {?}\n */\nprivate expandSegmentAgainstRoute(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      paths: UrlSegment[], outlet: string, allowRedirects: boolean): Observable<UrlSegmentGroup> {\n    if (getOutlet(route) !== outlet) {\n      return noMatch(segmentGroup);\n    }\n\n    if (route.redirectTo === undefined) {\n      return this.matchSegmentAgainstRoute(ngModule, segmentGroup, route, paths);\n    }\n\n    if (allowRedirects && this.allowRedirects) {\n      return this.expandSegmentAgainstRouteUsingRedirect(\n          ngModule, segmentGroup, routes, route, paths, outlet);\n    }\n\n    return noMatch(segmentGroup);\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate expandSegmentAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      segments: UrlSegment[], outlet: string): Observable<UrlSegmentGroup> {\n    if (route.path === '**') {\n      return this.expandWildCardWithParamsAgainstRouteUsingRedirect(\n          ngModule, routes, route, outlet);\n    }\n\n    return this.expandRegularSegmentAgainstRouteUsingRedirect(\n        ngModule, segmentGroup, routes, route, segments, outlet);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} route\n * @param {?} outlet\n * @return {?}\n */\nprivate expandWildCardWithParamsAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, routes: Route[], route: Route,\n      outlet: string): Observable<UrlSegmentGroup> {\n    const /** @type {?} */ newTree = this.applyRedirectCommands([], /** @type {?} */(( route.redirectTo)), {});\n    if ( /** @type {?} */((route.redirectTo)).startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return mergeMap.call(this.lineralizeSegments(route, newTree), (newSegments: UrlSegment[]) => {\n      const /** @type {?} */ group = new UrlSegmentGroup(newSegments, {});\n      return this.expandSegment(ngModule, group, routes, newSegments, outlet, false);\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate expandRegularSegmentAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      segments: UrlSegment[], outlet: string): Observable<UrlSegmentGroup> {\n    const {matched, consumedSegments, lastChild, positionalParamSegments} =\n        match(segmentGroup, route, segments);\n    if (!matched) return noMatch(segmentGroup);\n\n    const /** @type {?} */ newTree = this.applyRedirectCommands(\n        consumedSegments, /** @type {?} */(( route.redirectTo)), /** @type {?} */(( <any>positionalParamSegments)));\n    if ( /** @type {?} */((route.redirectTo)).startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return mergeMap.call(this.lineralizeSegments(route, newTree), (newSegments: UrlSegment[]) => {\n      return this.expandSegment(\n          ngModule, segmentGroup, routes, newSegments.concat(segments.slice(lastChild)), outlet,\n          false);\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} rawSegmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nprivate matchSegmentAgainstRoute(\n      ngModule: NgModuleRef<any>, rawSegmentGroup: UrlSegmentGroup, route: Route,\n      segments: UrlSegment[]): Observable<UrlSegmentGroup> {\n    if (route.path === '**') {\n      if (route.loadChildren) {\n        return map.call(\n            this.configLoader.load(ngModule.injector, route), (cfg: LoadedRouterConfig) => {\n              route._loadedConfig = cfg;\n              return new UrlSegmentGroup(segments, {});\n            });\n      }\n\n      return of (new UrlSegmentGroup(segments, {}));\n    }\n\n    const {matched, consumedSegments, lastChild} = match(rawSegmentGroup, route, segments);\n    if (!matched) return noMatch(rawSegmentGroup);\n\n    const /** @type {?} */ rawSlicedSegments = segments.slice(lastChild);\n    const /** @type {?} */ childConfig$ = this.getChildConfig(ngModule, route);\n\n    return mergeMap.call(childConfig$, (routerConfig: LoadedRouterConfig) => {\n      const /** @type {?} */ childModule = routerConfig.module;\n      const /** @type {?} */ childConfig = routerConfig.routes;\n\n      const {segmentGroup, slicedSegments} =\n          split(rawSegmentGroup, consumedSegments, rawSlicedSegments, childConfig);\n\n      if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n        const /** @type {?} */ expanded$ = this.expandChildren(childModule, childConfig, segmentGroup);\n        return map.call(\n            expanded$, (children: any) => new UrlSegmentGroup(consumedSegments, children));\n      }\n\n      if (childConfig.length === 0 && slicedSegments.length === 0) {\n        return of (new UrlSegmentGroup(consumedSegments, {}));\n      }\n\n      const /** @type {?} */ expanded$ = this.expandSegment(\n          childModule, segmentGroup, childConfig, slicedSegments, PRIMARY_OUTLET, true);\n      return map.call(\n          expanded$, (cs: UrlSegmentGroup) =>\n                         new UrlSegmentGroup(consumedSegments.concat(cs.segments), cs.children));\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} route\n * @return {?}\n */\nprivate getChildConfig(ngModule: NgModuleRef<any>, route: Route): Observable<LoadedRouterConfig> {\n    if (route.children) {\n      // The children belong to the same module\n      return of (new LoadedRouterConfig(route.children, ngModule));\n    }\n\n    if (route.loadChildren) {\n      // lazy children belong to the loaded module\n      if (route._loadedConfig !== undefined) {\n        return of (route._loadedConfig);\n      }\n\n      return mergeMap.call(runCanLoadGuard(ngModule.injector, route), (shouldLoad: boolean) => {\n\n        if (shouldLoad) {\n          return map.call(\n              this.configLoader.load(ngModule.injector, route), (cfg: LoadedRouterConfig) => {\n                route._loadedConfig = cfg;\n                return cfg;\n              });\n        }\n\n        return canLoadFails(route);\n      });\n    }\n\n    return of (new LoadedRouterConfig([], ngModule));\n  }\n/**\n * @param {?} route\n * @param {?} urlTree\n * @return {?}\n */\nprivate lineralizeSegments(route: Route, urlTree: UrlTree): Observable<UrlSegment[]> {\n    let /** @type {?} */ res: UrlSegment[] = [];\n    let /** @type {?} */ c = urlTree.root;\n    while (true) {\n      res = res.concat(c.segments);\n      if (c.numberOfChildren === 0) {\n        return of (res);\n      }\n\n      if (c.numberOfChildren > 1 || !c.children[PRIMARY_OUTLET]) {\n        return namedOutletsRedirect( /** @type {?} */((route.redirectTo)));\n      }\n\n      c = c.children[PRIMARY_OUTLET];\n    }\n  }\n/**\n * @param {?} segments\n * @param {?} redirectTo\n * @param {?} posParams\n * @return {?}\n */\nprivate applyRedirectCommands(\n      segments: UrlSegment[], redirectTo: string, posParams: {[k: string]: UrlSegment}): UrlTree {\n    return this.applyRedirectCreatreUrlTree(\n        redirectTo, this.urlSerializer.parse(redirectTo), segments, posParams);\n  }\n/**\n * @param {?} redirectTo\n * @param {?} urlTree\n * @param {?} segments\n * @param {?} posParams\n * @return {?}\n */\nprivate applyRedirectCreatreUrlTree(\n      redirectTo: string, urlTree: UrlTree, segments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlTree {\n    const /** @type {?} */ newRoot = this.createSegmentGroup(redirectTo, urlTree.root, segments, posParams);\n    return new UrlTree(\n        newRoot, this.createQueryParams(urlTree.queryParams, this.urlTree.queryParams),\n        urlTree.fragment);\n  }\n/**\n * @param {?} redirectToParams\n * @param {?} actualParams\n * @return {?}\n */\nprivate createQueryParams(redirectToParams: Params, actualParams: Params): Params {\n    const /** @type {?} */ res: Params = {};\n    forEach(redirectToParams, (v: any, k: string) => {\n      const /** @type {?} */ copySourceValue = typeof v === 'string' && v.startsWith(':');\n      if (copySourceValue) {\n        const /** @type {?} */ sourceName = v.substring(1);\n        res[k] = actualParams[sourceName];\n      } else {\n        res[k] = v;\n      }\n    });\n    return res;\n  }\n/**\n * @param {?} redirectTo\n * @param {?} group\n * @param {?} segments\n * @param {?} posParams\n * @return {?}\n */\nprivate createSegmentGroup(\n      redirectTo: string, group: UrlSegmentGroup, segments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlSegmentGroup {\n    const /** @type {?} */ updatedSegments = this.createSegments(redirectTo, group.segments, segments, posParams);\n\n    let /** @type {?} */ children: {[n: string]: UrlSegmentGroup} = {};\n    forEach(group.children, (child: UrlSegmentGroup, name: string) => {\n      children[name] = this.createSegmentGroup(redirectTo, child, segments, posParams);\n    });\n\n    return new UrlSegmentGroup(updatedSegments, children);\n  }\n/**\n * @param {?} redirectTo\n * @param {?} redirectToSegments\n * @param {?} actualSegments\n * @param {?} posParams\n * @return {?}\n */\nprivate createSegments(\n      redirectTo: string, redirectToSegments: UrlSegment[], actualSegments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlSegment[] {\n    return redirectToSegments.map(\n        s => s.path.startsWith(':') ? this.findPosParam(redirectTo, s, posParams) :\n                                      this.findOrReturn(s, actualSegments));\n  }\n/**\n * @param {?} redirectTo\n * @param {?} redirectToUrlSegment\n * @param {?} posParams\n * @return {?}\n */\nprivate findPosParam(\n      redirectTo: string, redirectToUrlSegment: UrlSegment,\n      posParams: {[k: string]: UrlSegment}): UrlSegment {\n    const /** @type {?} */ pos = posParams[redirectToUrlSegment.path.substring(1)];\n    if (!pos)\n      throw new Error(\n          `Cannot redirect to '${redirectTo}'. Cannot find '${redirectToUrlSegment.path}'.`);\n    return pos;\n  }\n/**\n * @param {?} redirectToUrlSegment\n * @param {?} actualSegments\n * @return {?}\n */\nprivate findOrReturn(redirectToUrlSegment: UrlSegment, actualSegments: UrlSegment[]): UrlSegment {\n    let /** @type {?} */ idx = 0;\n    for (const /** @type {?} */ s of actualSegments) {\n      if (s.path === redirectToUrlSegment.path) {\n        actualSegments.splice(idx);\n        return s;\n      }\n      idx++;\n    }\n    return redirectToUrlSegment;\n  }\n}\n\nfunction ApplyRedirects_tsickle_Closure_declarations() {\n/** @type {?} */\nApplyRedirects.prototype.allowRedirects;\n/** @type {?} */\nApplyRedirects.prototype.ngModule;\n/** @type {?} */\nApplyRedirects.prototype.configLoader;\n/** @type {?} */\nApplyRedirects.prototype.urlSerializer;\n/** @type {?} */\nApplyRedirects.prototype.urlTree;\n/** @type {?} */\nApplyRedirects.prototype.config;\n}\n\n/**\n * @param {?} moduleInjector\n * @param {?} route\n * @return {?}\n */\nfunction runCanLoadGuard(moduleInjector: Injector, route: Route): Observable<boolean> {\n  const /** @type {?} */ canLoad = route.canLoad;\n  if (!canLoad || canLoad.length === 0) return of (true);\n\n  const /** @type {?} */ obs = map.call(from(canLoad), (injectionToken: any) => {\n    const /** @type {?} */ guard = moduleInjector.get(injectionToken);\n    return wrapIntoObservable(guard.canLoad ? guard.canLoad(route) : guard(route));\n  });\n\n  return andObservables(obs);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nfunction match(segmentGroup: UrlSegmentGroup, route: Route, segments: UrlSegment[]): {\n  matched: boolean,\n  consumedSegments: UrlSegment[],\n  lastChild: number,\n  positionalParamSegments: {[k: string]: UrlSegment}\n} {\n  if (route.path === '') {\n    if ((route.pathMatch === 'full') && (segmentGroup.hasChildren() || segments.length > 0)) {\n      return {matched: false, consumedSegments: [], lastChild: 0, positionalParamSegments: {}};\n    }\n\n    return {matched: true, consumedSegments: [], lastChild: 0, positionalParamSegments: {}};\n  }\n\n  const /** @type {?} */ matcher = route.matcher || defaultUrlMatcher;\n  const /** @type {?} */ res = matcher(segments, segmentGroup, route);\n\n  if (!res) {\n    return {\n      matched: false, consumedSegments: /** @type {?} */(( <any[]>[])), lastChild: 0, positionalParamSegments: {},\n    }\n  }\n\n  return {\n    matched: true,\n    consumedSegments: /** @type {?} */(( res.consumed)),\n    lastChild: /** @type {?} */(( res.consumed.length)),\n    positionalParamSegments: /** @type {?} */(( res.posParams)),\n  };\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} slicedSegments\n * @param {?} config\n * @return {?}\n */\nfunction split(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], slicedSegments: UrlSegment[],\n    config: Route[]) {\n  if (slicedSegments.length > 0 &&\n      containsEmptyPathRedirectsWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        consumedSegments, createChildrenForEmptySegments(\n                              config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    return {segmentGroup: mergeTrivialChildren(s), slicedSegments: []};\n  }\n\n  if (slicedSegments.length === 0 &&\n      containsEmptyPathRedirects(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        segmentGroup.segments, addEmptySegmentsToChildrenIfNeeded(\n                                   segmentGroup, slicedSegments, config, segmentGroup.children));\n    return {segmentGroup: mergeTrivialChildren(s), slicedSegments};\n  }\n\n  return {segmentGroup, slicedSegments};\n}\n/**\n * @param {?} s\n * @return {?}\n */\nfunction mergeTrivialChildren(s: UrlSegmentGroup): UrlSegmentGroup {\n  if (s.numberOfChildren === 1 && s.children[PRIMARY_OUTLET]) {\n    const /** @type {?} */ c = s.children[PRIMARY_OUTLET];\n    return new UrlSegmentGroup(s.segments.concat(c.segments), c.children);\n  }\n\n  return s;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @param {?} children\n * @return {?}\n */\nfunction addEmptySegmentsToChildrenIfNeeded(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[],\n    children: {[name: string]: UrlSegmentGroup}): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  for (const /** @type {?} */ r of routes) {\n    if (isEmptyPathRedirect(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      res[getOutlet(r)] = new UrlSegmentGroup([], {});\n    }\n  }\n  return {...children, ...res};\n}\n/**\n * @param {?} routes\n * @param {?} primarySegmentGroup\n * @return {?}\n */\nfunction createChildrenForEmptySegments(\n    routes: Route[], primarySegmentGroup: UrlSegmentGroup): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  res[PRIMARY_OUTLET] = primarySegmentGroup;\n  for (const /** @type {?} */ r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      res[getOutlet(r)] = new UrlSegmentGroup([], {});\n    }\n  }\n  return res;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathRedirectsWithNamedOutlets(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(\n      r => isEmptyPathRedirect(segmentGroup, segments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathRedirects(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(r => isEmptyPathRedirect(segmentGroup, segments, r));\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} r\n * @return {?}\n */\nfunction isEmptyPathRedirect(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], r: Route): boolean {\n  if ((segmentGroup.hasChildren() || segments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n\n  return r.path === '' && r.redirectTo !== undefined;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getOutlet(route: Route): string {\n  return route.outlet || PRIMARY_OUTLET;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {PRIMARY_OUTLET, ParamMap, convertToParamMap} from './shared';\nimport {forEach, shallowEqual} from './utils/collection';\n/**\n * @return {?}\n */\nexport function createEmptyUrlTree() {\n  return new UrlTree(new UrlSegmentGroup([], {}), {}, null);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @param {?} exact\n * @return {?}\n */\nexport function containsTree(container: UrlTree, containee: UrlTree, exact: boolean): boolean {\n  if (exact) {\n    return equalQueryParams(container.queryParams, containee.queryParams) &&\n        equalSegmentGroups(container.root, containee.root);\n  }\n\n  return containsQueryParams(container.queryParams, containee.queryParams) &&\n      containsSegmentGroup(container.root, containee.root);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction equalQueryParams(\n    container: {[k: string]: string}, containee: {[k: string]: string}): boolean {\n  return shallowEqual(container, containee);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction equalSegmentGroups(container: UrlSegmentGroup, containee: UrlSegmentGroup): boolean {\n  if (!equalPath(container.segments, containee.segments)) return false;\n  if (container.numberOfChildren !== containee.numberOfChildren) return false;\n  for (const /** @type {?} */ c in containee.children) {\n    if (!container.children[c]) return false;\n    if (!equalSegmentGroups(container.children[c], containee.children[c])) return false;\n  }\n  return true;\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction containsQueryParams(\n    container: {[k: string]: string}, containee: {[k: string]: string}): boolean {\n  return Object.keys(containee).length <= Object.keys(container).length &&\n      Object.keys(containee).every(key => containee[key] === container[key]);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction containsSegmentGroup(container: UrlSegmentGroup, containee: UrlSegmentGroup): boolean {\n  return containsSegmentGroupHelper(container, containee, containee.segments);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @param {?} containeePaths\n * @return {?}\n */\nfunction containsSegmentGroupHelper(\n    container: UrlSegmentGroup, containee: UrlSegmentGroup, containeePaths: UrlSegment[]): boolean {\n  if (container.segments.length > containeePaths.length) {\n    const /** @type {?} */ current = container.segments.slice(0, containeePaths.length);\n    if (!equalPath(current, containeePaths)) return false;\n    if (containee.hasChildren()) return false;\n    return true;\n\n  } else if (container.segments.length === containeePaths.length) {\n    if (!equalPath(container.segments, containeePaths)) return false;\n    for (const /** @type {?} */ c in containee.children) {\n      if (!container.children[c]) return false;\n      if (!containsSegmentGroup(container.children[c], containee.children[c])) return false;\n    }\n    return true;\n\n  } else {\n    const /** @type {?} */ current = containeePaths.slice(0, container.segments.length);\n    const /** @type {?} */ next = containeePaths.slice(container.segments.length);\n    if (!equalPath(container.segments, current)) return false;\n    if (!container.children[PRIMARY_OUTLET]) return false;\n    return containsSegmentGroupHelper(container.children[PRIMARY_OUTLET], containee, next);\n  }\n}\n/**\n * \\@whatItDoes Represents the parsed URL.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree =\n *       router.parseUrl('/team/33/(user/victor//support:help)?debug=true#fragment');\n *     const f = tree.fragment; // return 'fragment'\n *     const q = tree.queryParams; // returns {debug: 'true'}\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments; // returns 2 segments 'team' and '33'\n *     g.children[PRIMARY_OUTLET].segments; // returns 2 segments 'user' and 'victor'\n *     g.children['support'].segments; // return 1 segment 'help'\n *   }\n * }\n * ```\n * \n * \\@description \n * \n * Since a router state is a tree, and the URL is nothing but a serialized state, the URL is a\n * serialized tree.\n * UrlTree is a data structure that provides a lot of affordances in dealing with URLs\n * \n * \\@stable\n */\nexport class UrlTree {\n/**\n * \\@internal\n */\n_queryParamMap: ParamMap;\n/**\n * \\@internal\n * @param {?} root\n * @param {?} queryParams\n * @param {?} fragment\n */\nconstructor(\npublic root: UrlSegmentGroup,\npublic queryParams: {[key: string]: string},\npublic fragment: string|null) {}\n/**\n * @return {?}\n */\nget queryParamMap(): ParamMap {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n    return this._queryParamMap;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return DEFAULT_SERIALIZER.serialize(this); }\n}\n\nfunction UrlTree_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlTree.prototype._queryParamMap;\n/**\n * The root segment group of the URL tree\n * @type {?}\n */\nUrlTree.prototype.root;\n/**\n * The query params of the URL\n * @type {?}\n */\nUrlTree.prototype.queryParams;\n/**\n * The fragment of the URL\n * @type {?}\n */\nUrlTree.prototype.fragment;\n}\n\n/**\n * \\@whatItDoes Represents the parsed URL segment group.\n * \n * See {\\@link UrlTree} for more information.\n * \n * \\@stable\n */\nexport class UrlSegmentGroup {\n/**\n * \\@internal\n */\n_sourceSegment: UrlSegmentGroup;\n/**\n * \\@internal\n */\n_segmentIndexShift: number;\n/**\n * The parent node in the url tree\n */\nparent: UrlSegmentGroup|null = null;\n/**\n * @param {?} segments\n * @param {?} children\n */\nconstructor(\npublic segments: UrlSegment[],\npublic children: {[key: string]: UrlSegmentGroup}) {\n    forEach(children, (v: any, k: any) => v.parent = this);\n  }\n/**\n * Wether the segment has child segments\n * @return {?}\n */\nhasChildren(): boolean { return this.numberOfChildren > 0; }\n/**\n * Number of child segments\n * @return {?}\n */\nget numberOfChildren(): number { return Object.keys(this.children).length; }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return serializePaths(this); }\n}\n\nfunction UrlSegmentGroup_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegmentGroup.prototype._sourceSegment;\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegmentGroup.prototype._segmentIndexShift;\n/**\n * The parent node in the url tree\n * @type {?}\n */\nUrlSegmentGroup.prototype.parent;\n/**\n * The URL segments of this group. See {\\@link UrlSegment} for more information\n * @type {?}\n */\nUrlSegmentGroup.prototype.segments;\n/**\n * The list of children of this group\n * @type {?}\n */\nUrlSegmentGroup.prototype.children;\n}\n\n/**\n * \\@whatItDoes Represents a single URL segment.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree = router.parseUrl('/team;id=33');\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments;\n *     s[0].path; // returns 'team'\n *     s[0].parameters; // returns {id: 33}\n *   }\n * }\n * ```\n * \n * \\@description \n * \n * A UrlSegment is a part of a URL between the two slashes. It contains a path and the matrix\n * parameters associated with the segment.\n * \n * \\@stable\n */\nexport class UrlSegment {\n/**\n * \\@internal\n */\n_parameterMap: ParamMap;\n/**\n * @param {?} path\n * @param {?} parameters\n */\nconstructor(\npublic path: string,\npublic parameters: {[name: string]: string}) {}\n/**\n * @return {?}\n */\nget parameterMap() {\n    if (!this._parameterMap) {\n      this._parameterMap = convertToParamMap(this.parameters);\n    }\n    return this._parameterMap;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return serializePath(this); }\n}\n\nfunction UrlSegment_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegment.prototype._parameterMap;\n/**\n * The path part of a URL segment\n * @type {?}\n */\nUrlSegment.prototype.path;\n/**\n * The matrix parameters associated with a segment\n * @type {?}\n */\nUrlSegment.prototype.parameters;\n}\n\n/**\n * @param {?} as\n * @param {?} bs\n * @return {?}\n */\nexport function equalSegments(as: UrlSegment[], bs: UrlSegment[]): boolean {\n  return equalPath(as, bs) && as.every((a, i) => shallowEqual(a.parameters, bs[i].parameters));\n}\n/**\n * @param {?} as\n * @param {?} bs\n * @return {?}\n */\nexport function equalPath(as: UrlSegment[], bs: UrlSegment[]): boolean {\n  if (as.length !== bs.length) return false;\n  return as.every((a, i) => a.path === bs[i].path);\n}\n/**\n * @template T\n * @param {?} segment\n * @param {?} fn\n * @return {?}\n */\nexport function mapChildrenIntoArray<T>(\n    segment: UrlSegmentGroup, fn: (v: UrlSegmentGroup, k: string) => T[]): T[] {\n  let /** @type {?} */ res: T[] = [];\n  forEach(segment.children, (child: UrlSegmentGroup, childOutlet: string) => {\n    if (childOutlet === PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  forEach(segment.children, (child: UrlSegmentGroup, childOutlet: string) => {\n    if (childOutlet !== PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  return res;\n}\n/**\n * \\@whatItDoes Serializes and deserializes a URL string into a URL tree.\n * \n * \\@description The url serialization strategy is customizable. You can\n * make all URLs case insensitive by providing a custom UrlSerializer.\n * \n * See {\\@link DefaultUrlSerializer} for an example of a URL serializer.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class UrlSerializer {\n/**\n * Parse a url into a {\\@link UrlTree}\n * @abstract\n * @param {?} url\n * @return {?}\n */\nparse(url: string) {}\n/**\n * Converts a {\\@link UrlTree} into a url\n * @abstract\n * @param {?} tree\n * @return {?}\n */\nserialize(tree: UrlTree) {}\n}\n/**\n * \\@whatItDoes A default implementation of the {\\@link UrlSerializer}.\n * \n * \\@description \n * \n * Example URLs:\n * \n * ```\n * /inbox/33(popup:compose)\n * /inbox/33;open=true/messages/44\n * ```\n * \n * DefaultUrlSerializer uses parentheses to serialize secondary segments (e.g., popup:compose), the\n * colon syntax to specify the outlet, and the ';parameter=value' syntax (e.g., open=true) to\n * specify route specific parameters.\n * \n * \\@stable\n */\nexport class DefaultUrlSerializer implements UrlSerializer {\n/**\n * Parses a url into a {\\@link UrlTree}\n * @param {?} url\n * @return {?}\n */\nparse(url: string): UrlTree {\n    const /** @type {?} */ p = new UrlParser(url);\n    return new UrlTree(p.parseRootSegment(), p.parseQueryParams(), p.parseFragment());\n  }\n/**\n * Converts a {\\@link UrlTree} into a url\n * @param {?} tree\n * @return {?}\n */\nserialize(tree: UrlTree): string {\n    const /** @type {?} */ segment = `/${serializeSegment(tree.root, true)}`;\n    const /** @type {?} */ query = serializeQueryParams(tree.queryParams);\n    const /** @type {?} */ fragment = typeof tree.fragment === `string` ? `#${encodeURI( /** @type {?} */((tree.fragment)))}` : '';\n\n    return `${segment}${query}${fragment}`;\n  }\n}\n\nconst /** @type {?} */ DEFAULT_SERIALIZER = new DefaultUrlSerializer();\n/**\n * @param {?} segment\n * @return {?}\n */\nexport function serializePaths(segment: UrlSegmentGroup): string {\n  return segment.segments.map(p => serializePath(p)).join('/');\n}\n/**\n * @param {?} segment\n * @param {?} root\n * @return {?}\n */\nfunction serializeSegment(segment: UrlSegmentGroup, root: boolean): string {\n  if (!segment.hasChildren()) {\n    return serializePaths(segment);\n  }\n\n  if (root) {\n    const /** @type {?} */ primary = segment.children[PRIMARY_OUTLET] ?\n        serializeSegment(segment.children[PRIMARY_OUTLET], false) :\n        '';\n    const /** @type {?} */ children: string[] = [];\n\n    forEach(segment.children, (v: UrlSegmentGroup, k: string) => {\n      if (k !== PRIMARY_OUTLET) {\n        children.push(`${k}:${serializeSegment(v, false)}`);\n      }\n    });\n\n    return children.length > 0 ? `${primary}(${children.join('//')})` : primary;\n\n  } else {\n    const /** @type {?} */ children = mapChildrenIntoArray(segment, (v: UrlSegmentGroup, k: string) => {\n      if (k === PRIMARY_OUTLET) {\n        return [serializeSegment(segment.children[PRIMARY_OUTLET], false)];\n      }\n\n      return [`${k}:${serializeSegment(v, false)}`];\n\n    });\n\n    return `${serializePaths(segment)}/(${children.join('//')})`;\n  }\n}\n/**\n * @param {?} s\n * @return {?}\n */\nexport function encode(s: string): string {\n  return encodeURIComponent(s);\n}\n/**\n * @param {?} s\n * @return {?}\n */\nexport function decode(s: string): string {\n  return decodeURIComponent(s);\n}\n/**\n * @param {?} path\n * @return {?}\n */\nexport function serializePath(path: UrlSegment): string {\n  return `${encode(path.path)}${serializeParams(path.parameters)}`;\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction serializeParams(params: {[key: string]: string}): string {\n  return Object.keys(params).map(key => `;${encode(key)}=${encode(params[key])}`).join('');\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction serializeQueryParams(params: {[key: string]: any}): string {\n  const /** @type {?} */ strParams: string[] = Object.keys(params).map((name) => {\n    const /** @type {?} */ value = params[name];\n    return Array.isArray(value) ? value.map(v => `${encode(name)}=${encode(v)}`).join('&') :\n                                  `${encode(name)}=${encode(value)}`;\n  });\n\n  return strParams.length ? `?${strParams.join(\"&\")}` : '';\n}\n\nconst /** @type {?} */ SEGMENT_RE = /^[^\\/()?;=&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchSegments(str: string): string {\n  const /** @type {?} */ match = str.match(SEGMENT_RE);\n  return match ? match[0] : '';\n}\n\nconst /** @type {?} */ QUERY_PARAM_RE = /^[^=?&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchQueryParams(str: string): string {\n  const /** @type {?} */ match = str.match(QUERY_PARAM_RE);\n  return match ? match[0] : '';\n}\n\nconst /** @type {?} */ QUERY_PARAM_VALUE_RE = /^[^?&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchUrlQueryParamValue(str: string): string {\n  const /** @type {?} */ match = str.match(QUERY_PARAM_VALUE_RE);\n  return match ? match[0] : '';\n}\nclass UrlParser {\nprivate remaining: string;\n/**\n * @param {?} url\n */\nconstructor(private url: string) { this.remaining = url; }\n/**\n * @return {?}\n */\nparseRootSegment(): UrlSegmentGroup {\n    this.consumeOptional('/');\n\n    if (this.remaining === '' || this.peekStartsWith('?') || this.peekStartsWith('#')) {\n      return new UrlSegmentGroup([], {});\n    }\n\n    // The root segment group never has segments\n    return new UrlSegmentGroup([], this.parseChildren());\n  }\n/**\n * @return {?}\n */\nparseQueryParams(): {[key: string]: any} {\n    const /** @type {?} */ params: {[key: string]: any} = {};\n    if (this.consumeOptional('?')) {\n      do {\n        this.parseQueryParam(params);\n      } while (this.consumeOptional('&'));\n    }\n    return params;\n  }\n/**\n * @return {?}\n */\nparseFragment(): string|null {\n    return this.consumeOptional('#') ? decodeURI(this.remaining) : null;\n  }\n/**\n * @return {?}\n */\nprivate parseChildren(): {[outlet: string]: UrlSegmentGroup} {\n    if (this.remaining === '') {\n      return {};\n    }\n\n    this.consumeOptional('/');\n\n    const /** @type {?} */ segments: UrlSegment[] = [];\n    if (!this.peekStartsWith('(')) {\n      segments.push(this.parseSegment());\n    }\n\n    while (this.peekStartsWith('/') && !this.peekStartsWith('//') && !this.peekStartsWith('/(')) {\n      this.capture('/');\n      segments.push(this.parseSegment());\n    }\n\n    let /** @type {?} */ children: {[outlet: string]: UrlSegmentGroup} = {};\n    if (this.peekStartsWith('/(')) {\n      this.capture('/');\n      children = this.parseParens(true);\n    }\n\n    let /** @type {?} */ res: {[outlet: string]: UrlSegmentGroup} = {};\n    if (this.peekStartsWith('(')) {\n      res = this.parseParens(false);\n    }\n\n    if (segments.length > 0 || Object.keys(children).length > 0) {\n      res[PRIMARY_OUTLET] = new UrlSegmentGroup(segments, children);\n    }\n\n    return res;\n  }\n/**\n * @return {?}\n */\nprivate parseSegment(): UrlSegment {\n    const /** @type {?} */ path = matchSegments(this.remaining);\n    if (path === '' && this.peekStartsWith(';')) {\n      throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);\n    }\n\n    this.capture(path);\n    return new UrlSegment(decode(path), this.parseMatrixParams());\n  }\n/**\n * @return {?}\n */\nprivate parseMatrixParams(): {[key: string]: any} {\n    const /** @type {?} */ params: {[key: string]: any} = {};\n    while (this.consumeOptional(';')) {\n      this.parseParam(params);\n    }\n    return params;\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate parseParam(params: {[key: string]: any}): void {\n    const /** @type {?} */ key = matchSegments(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let /** @type {?} */ value: any = '';\n    if (this.consumeOptional('=')) {\n      const /** @type {?} */ valueMatch = matchSegments(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    params[decode(key)] = decode(value);\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate parseQueryParam(params: {[key: string]: any}): void {\n    const /** @type {?} */ key = matchQueryParams(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let /** @type {?} */ value: any = '';\n    if (this.consumeOptional('=')) {\n      const /** @type {?} */ valueMatch = matchUrlQueryParamValue(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    const /** @type {?} */ decodedKey = decode(key);\n    const /** @type {?} */ decodedVal = decode(value);\n\n    if (params.hasOwnProperty(decodedKey)) {\n      // Append to existing values\n      let /** @type {?} */ currentVal = params[decodedKey];\n      if (!Array.isArray(currentVal)) {\n        currentVal = [currentVal];\n        params[decodedKey] = currentVal;\n      }\n      currentVal.push(decodedVal);\n    } else {\n      // Create a new value\n      params[decodedKey] = decodedVal;\n    }\n  }\n/**\n * @param {?} allowPrimary\n * @return {?}\n */\nprivate parseParens(allowPrimary: boolean): {[outlet: string]: UrlSegmentGroup} {\n    const /** @type {?} */ segments: {[key: string]: UrlSegmentGroup} = {};\n    this.capture('(');\n\n    while (!this.consumeOptional(')') && this.remaining.length > 0) {\n      const /** @type {?} */ path = matchSegments(this.remaining);\n\n      const /** @type {?} */ next = this.remaining[path.length];\n\n      // if is is not one of these characters, then the segment was unescaped\n      // or the group was not closed\n      if (next !== '/' && next !== ')' && next !== ';') {\n        throw new Error(`Cannot parse url '${this.url}'`);\n      }\n\n      let /** @type {?} */ outletName: string = /** @type {?} */(( undefined));\n      if (path.indexOf(':') > -1) {\n        outletName = path.substr(0, path.indexOf(':'));\n        this.capture(outletName);\n        this.capture(':');\n      } else if (allowPrimary) {\n        outletName = PRIMARY_OUTLET;\n      }\n\n      const /** @type {?} */ children = this.parseChildren();\n      segments[outletName] = Object.keys(children).length === 1 ? children[PRIMARY_OUTLET] :\n                                                                  new UrlSegmentGroup([], children);\n      this.consumeOptional('//');\n    }\n\n    return segments;\n  }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate peekStartsWith(str: string): boolean { return this.remaining.startsWith(str); }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate consumeOptional(str: string): boolean {\n    if (this.peekStartsWith(str)) {\n      this.remaining = this.remaining.substring(str.length);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate capture(str: string): void {\n    if (!this.consumeOptional(str)) {\n      throw new Error(`Expected \"${str}\".`);\n    }\n  }\n}\n\nfunction UrlParser_tsickle_Closure_declarations() {\n/** @type {?} */\nUrlParser.prototype.remaining;\n/** @type {?} */\nUrlParser.prototype.url;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModuleFactory, ɵisObservable as isObservable, ɵisPromise as isPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {of } from 'rxjs/observable/of';\nimport {concatAll} from 'rxjs/operator/concatAll';\nimport {every} from 'rxjs/operator/every';\nimport * as l from 'rxjs/operator/last';\nimport {map} from 'rxjs/operator/map';\nimport {mergeAll} from 'rxjs/operator/mergeAll';\nimport {PRIMARY_OUTLET} from '../shared';\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function shallowEqualArrays(a: any[], b: any[]): boolean {\n  if (a.length !== b.length) return false;\n  for (let /** @type {?} */ i = 0; i < a.length; ++i) {\n    if (!shallowEqual(a[i], b[i])) return false;\n  }\n  return true;\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function shallowEqual(a: {[x: string]: any}, b: {[x: string]: any}): boolean {\n  const /** @type {?} */ k1 = Object.keys(a);\n  const /** @type {?} */ k2 = Object.keys(b);\n  if (k1.length != k2.length) {\n    return false;\n  }\n  let /** @type {?} */ key: string;\n  for (let /** @type {?} */ i = 0; i < k1.length; i++) {\n    key = k1[i];\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * @template T\n * @param {?} arr\n * @return {?}\n */\nexport function flatten<T>(arr: T[][]): T[] {\n  return Array.prototype.concat.apply([], arr);\n}\n/**\n * @template T\n * @param {?} a\n * @return {?}\n */\nexport function last<T>(a: T[]): T|null {\n  return a.length > 0 ? a[a.length - 1] : null;\n}\n/**\n * @param {?} bools\n * @return {?}\n */\nexport function and(bools: boolean[]): boolean {\n  return !bools.some(v => !v);\n}\n/**\n * @template K, V\n * @param {?} map\n * @param {?} callback\n * @return {?}\n */\nexport function forEach<K, V>(map: {[key: string]: V}, callback: (v: V, k: string) => void): void {\n  for (const /** @type {?} */ prop in map) {\n    if (map.hasOwnProperty(prop)) {\n      callback(map[prop], prop);\n    }\n  }\n}\n/**\n * @template A, B\n * @param {?} obj\n * @param {?} fn\n * @return {?}\n */\nexport function waitForMap<A, B>(\n    obj: {[k: string]: A}, fn: (k: string, a: A) => Observable<B>): Observable<{[k: string]: B}> {\n  if (Object.keys(obj).length === 0) {\n    return of ({})\n  }\n\n  const /** @type {?} */ waitHead: Observable<B>[] = [];\n  const /** @type {?} */ waitTail: Observable<B>[] = [];\n  const /** @type {?} */ res: {[k: string]: B} = {};\n\n  forEach(obj, (a: A, k: string) => {\n    const /** @type {?} */ mapped = map.call(fn(k, a), (r: B) => res[k] = r);\n    if (k === PRIMARY_OUTLET) {\n      waitHead.push(mapped);\n    } else {\n      waitTail.push(mapped);\n    }\n  });\n\n  const /** @type {?} */ concat$ = concatAll.call(of (...waitHead, ...waitTail));\n  const /** @type {?} */ last$ = l.last.call(concat$);\n  return map.call(last$, () => res);\n}\n/**\n * @param {?} observables\n * @return {?}\n */\nexport function andObservables(observables: Observable<Observable<any>>): Observable<boolean> {\n  const /** @type {?} */ merged$ = mergeAll.call(observables);\n  return every.call(merged$, (result: any) => result === true);\n}\n/**\n * @template T\n * @param {?} value\n * @return {?}\n */\nexport function wrapIntoObservable<T>(value: T | NgModuleFactory<T>| Promise<T>| Observable<T>):\n    Observable<T> {\n  if (isObservable(value)) {\n    return value;\n  }\n\n  if (isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    // Required ie when a Resolver returns a AngularJS `$q` promise to correctly trigger the\n    // change detection.\n    return fromPromise(Promise.resolve(value));\n  }\n\n  return of (value);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModuleFactory, NgModuleRef, Type} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {PRIMARY_OUTLET} from './shared';\nimport {UrlSegment, UrlSegmentGroup} from './url_tree';\n\n/**\n * @whatItDoes Represents router configuration.\n *\n * @description\n * `Routes` is an array of route configurations. Each one has the following properties:\n *\n * - `path` is a string that uses the route matcher DSL.\n * - `pathMatch` is a string that specifies the matching strategy.\n * - `matcher` defines a custom strategy for path matching and supersedes `path` and `pathMatch`.\n * - `component` is a component type.\n * - `redirectTo` is the url fragment which will replace the current matched segment.\n * - `outlet` is the name of the outlet the component should be placed into.\n * - `canActivate` is an array of DI tokens used to look up CanActivate handlers. See\n *   {@link CanActivate} for more info.\n * - `canActivateChild` is an array of DI tokens used to look up CanActivateChild handlers. See\n *   {@link CanActivateChild} for more info.\n * - `canDeactivate` is an array of DI tokens used to look up CanDeactivate handlers. See\n *   {@link CanDeactivate} for more info.\n * - `canLoad` is an array of DI tokens used to look up CanLoad handlers. See\n *   {@link CanLoad} for more info.\n * - `data` is additional data provided to the component via `ActivatedRoute`.\n * - `resolve` is a map of DI tokens used to look up data resolvers. See {@link Resolve} for more\n *   info.\n * - `runGuardsAndResolvers` defines when guards and resolvers will be run. By default they run only\n *    when the matrix parameters of the route change. When set to `paramsOrQueryParamsChange` they\n *    will also run when query params change. And when set to `always`, they will run every time.\n * - `children` is an array of child route definitions.\n * - `loadChildren` is a reference to lazy loaded child routes. See {@link LoadChildren} for more\n *   info.\n *\n * ### Simple Configuration\n *\n * ```\n * [{\n *   path: 'team/:id',\n  *  component: Team,\n *   children: [{\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11/user/bob`, the router will create the team component with the user\n * component in it.\n *\n * ### Multiple Outlets\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team\n * }, {\n *   path: 'chat/:user',\n *   component: Chat\n *   outlet: 'aux'\n * }]\n * ```\n *\n * When navigating to `/team/11(aux:chat/jim)`, the router will create the team component next to\n * the chat component. The chat component will be placed into the aux outlet.\n *\n * ### Wild Cards\n *\n * ```\n * [{\n *   path: '**',\n *   component: Sink\n * }]\n * ```\n *\n * Regardless of where you navigate to, the router will instantiate the sink component.\n *\n * ### Redirects\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: 'legacy/user/:name',\n *     redirectTo: 'user/:name'\n *   }, {\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to '/team/11/legacy/user/jim', the router will change the url to\n * '/team/11/user/jim', and then will instantiate the team component with the user component\n * in it.\n *\n * If the `redirectTo` value starts with a '/', then it is an absolute redirect. E.g., if in the\n * example above we change the `redirectTo` to `/user/:name`, the result url will be '/user/jim'.\n *\n * ### Empty Path\n *\n * Empty-path route configurations can be used to instantiate components that do not 'consume'\n * any url segments. Let's look at the following configuration:\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: '',\n *     component: AllUsers\n *   }, {\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11`, the router will instantiate the AllUsers component.\n *\n * Empty-path routes can have children.\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: '',\n *     component: WrapperCmp,\n *     children: [{\n *       path: 'user/:name',\n *       component: User\n *     }]\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11/user/jim`, the router will instantiate the wrapper component with\n * the user component in it.\n *\n * An empty path route inherits its parent's params and data. This is because it cannot have its\n * own params, and, as a result, it often uses its parent's params and data as its own.\n *\n * ### Matching Strategy\n *\n * By default the router will look at what is left in the url, and check if it starts with\n * the specified path (e.g., `/team/11/user` starts with `team/:id`).\n *\n * We can change the matching strategy to make sure that the path covers the whole unconsumed url,\n * which is akin to `unconsumedUrl === path` or `$` regular expressions.\n *\n * This is particularly important when redirecting empty-path routes.\n *\n * ```\n * [{\n *   path: '',\n *   pathMatch: 'prefix', //default\n *   redirectTo: 'main'\n * }, {\n *   path: 'main',\n *   component: Main\n * }]\n * ```\n *\n * Since an empty path is a prefix of any url, even when navigating to '/main', the router will\n * still apply the redirect.\n *\n * If `pathMatch: full` is provided, the router will apply the redirect if and only if navigating to\n * '/'.\n *\n * ```\n * [{\n *   path: '',\n *   pathMatch: 'full',\n *   redirectTo: 'main'\n * }, {\n *   path: 'main',\n *   component: Main\n * }]\n * ```\n *\n * ### Componentless Routes\n *\n * It is useful at times to have the ability to share parameters between sibling components.\n *\n * Say we have two components--ChildCmp and AuxCmp--that we want to put next to each other and both\n * of them require some id parameter.\n *\n * One way to do that would be to have a bogus parent component, so both the siblings can get the id\n * parameter from it. This is not ideal. Instead, you can use a componentless route.\n *\n * ```\n * [{\n *    path: 'parent/:id',\n *    children: [\n *      { path: 'a', component: MainChild },\n *      { path: 'b', component: AuxChild, outlet: 'aux' }\n *    ]\n * }]\n * ```\n *\n * So when navigating to `parent/10/(a//aux:b)`, the route will instantiate the main child and aux\n * child components next to each other. In this example, the application component\n * has to have the primary and aux outlets defined.\n *\n * The router will also merge the `params`, `data`, and `resolve` of the componentless parent into\n * the `params`, `data`, and `resolve` of the children. This is done because there is no component\n * that can inject the activated route of the componentless parent.\n *\n * This is especially useful when child components are defined as follows:\n *\n * ```\n * [{\n *    path: 'parent/:id',\n *    children: [\n *      { path: '', component: MainChild },\n *      { path: '', component: AuxChild, outlet: 'aux' }\n *    ]\n * }]\n * ```\n *\n * With this configuration in place, navigating to '/parent/10' will create the main child and aux\n * components.\n *\n * ### Lazy Loading\n *\n * Lazy loading speeds up our application load time by splitting it into multiple bundles, and\n * loading them on demand. The router is designed to make lazy loading simple and easy. Instead of\n * providing the children property, you can provide the `loadChildren` property, as follows:\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   loadChildren: 'team'\n * }]\n * ```\n *\n * The router will use registered NgModuleFactoryLoader to fetch an NgModule associated with 'team'.\n * Then it will extract the set of routes defined in that NgModule, and will transparently add\n * those routes to the main configuration.\n *\n * @stable use Routes\n */\nexport type Routes = Route[];\n\n/**\n * @whatItDoes Represents the results of the URL matching.\n *\n * * `consumed` is an array of the consumed URL segments.\n * * `posParams` is a map of positional parameters.\n *\n * @experimental\n */\nexport type UrlMatchResult = {\n  consumed: UrlSegment[]; posParams?: {[name: string]: UrlSegment};\n};\n\n/**\n * @whatItDoes A function matching URLs\n *\n * @description\n *\n * A custom URL matcher can be provided when a combination of `path` and `pathMatch` isn't\n * expressive enough.\n *\n * For instance, the following matcher matches html files.\n *\n * ```\n * function htmlFiles(url: UrlSegment[]) {\n *  return url.length === 1 && url[0].path.endsWith('.html') ? ({consumed: url}) : null;\n * }\n *\n * const routes = [{ matcher: htmlFiles, component: HtmlCmp }];\n * ```\n *\n * @experimental\n */\nexport type UrlMatcher = (segments: UrlSegment[], group: UrlSegmentGroup, route: Route) =>\n    UrlMatchResult;\n\n/**\n * @whatItDoes Represents the static data associated with a particular route.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type Data = {\n  [name: string]: any\n};\n\n/**\n * @whatItDoes Represents the resolved data associated with a particular route.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type ResolveData = {\n  [name: string]: any\n};\n\n/**\n * @whatItDoes The type of `loadChildren`.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type LoadChildrenCallback = () =>\n    Type<any>| NgModuleFactory<any>| Promise<Type<any>>| Observable<Type<any>>;\n\n/**\n * @whatItDoes The type of `loadChildren`.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type LoadChildren = string | LoadChildrenCallback;\n\n/**\n * @whatItDoes The type of `queryParamsHandling`.\n * See {@link RouterLink} for more details.\n * @stable\n */\nexport type QueryParamsHandling = 'merge' | 'preserve' | '';\n\n/**\n * @whatItDoes The type of `runGuardsAndResolvers`.\n * See {@link Routes} for more details.\n * @experimental\n */\nexport type RunGuardsAndResolvers = 'paramsChange' | 'paramsOrQueryParamsChange' | 'always';\n\n/**\n * See {@link Routes} for more details.\n * @stable\n */\nexport interface Route {\n  path?: string;\n  pathMatch?: string;\n  matcher?: UrlMatcher;\n  component?: Type<any>;\n  redirectTo?: string;\n  outlet?: string;\n  canActivate?: any[];\n  canActivateChild?: any[];\n  canDeactivate?: any[];\n  canLoad?: any[];\n  data?: Data;\n  resolve?: ResolveData;\n  children?: Routes;\n  loadChildren?: LoadChildren;\n  runGuardsAndResolvers?: RunGuardsAndResolvers;\n  /**\n   * Filled for routes with `loadChildren` once the module has been loaded\n   * @internal\n   */\n  _loadedConfig?: LoadedRouterConfig;\n}\nexport class LoadedRouterConfig {\n/**\n * @param {?} routes\n * @param {?} module\n */\nconstructor(public routes: Route[],\npublic module: NgModuleRef<any>) {}\n}\n\nfunction LoadedRouterConfig_tsickle_Closure_declarations() {\n/** @type {?} */\nLoadedRouterConfig.prototype.routes;\n/** @type {?} */\nLoadedRouterConfig.prototype.module;\n}\n\n/**\n * @param {?} config\n * @param {?=} parentPath\n * @return {?}\n */\nexport function validateConfig(config: Routes, parentPath: string = ''): void {\n  // forEach doesn't iterate undefined values\n  for (let /** @type {?} */ i = 0; i < config.length; i++) {\n    const /** @type {?} */ route: Route = config[i];\n    const /** @type {?} */ fullPath: string = getFullPath(parentPath, route);\n    validateNode(route, fullPath);\n  }\n}\n/**\n * @param {?} route\n * @param {?} fullPath\n * @return {?}\n */\nfunction validateNode(route: Route, fullPath: string): void {\n  if (!route) {\n    throw new Error(`\n      Invalid configuration of route '${fullPath}': Encountered undefined route.\n      The reason might be an extra comma.\n\n      Example:\n      const routes: Routes = [\n        { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n        { path: 'dashboard',  component: DashboardComponent },, << two commas\n        { path: 'detail/:id', component: HeroDetailComponent }\n      ];\n    `);\n  }\n  if (Array.isArray(route)) {\n    throw new Error(`Invalid configuration of route '${fullPath}': Array cannot be specified`);\n  }\n  if (!route.component && (route.outlet && route.outlet !== PRIMARY_OUTLET)) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': a componentless route cannot have a named outlet set`);\n  }\n  if (route.redirectTo && route.children) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and children cannot be used together`);\n  }\n  if (route.redirectTo && route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and loadChildren cannot be used together`);\n  }\n  if (route.children && route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': children and loadChildren cannot be used together`);\n  }\n  if (route.redirectTo && route.component) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and component cannot be used together`);\n  }\n  if (route.path && route.matcher) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': path and matcher cannot be used together`);\n  }\n  if (route.redirectTo === void 0 && !route.component && !route.children && !route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}'. One of the following must be provided: component, redirectTo, children or loadChildren`);\n  }\n  if (route.path === void 0 && route.matcher === void 0) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': routes must have either a path or a matcher specified`);\n  }\n  if (typeof route.path === 'string' && route.path.charAt(0) === '/') {\n    throw new Error(`Invalid configuration of route '${fullPath}': path cannot start with a slash`);\n  }\n  if (route.path === '' && route.redirectTo !== void 0 && route.pathMatch === void 0) {\n    const /** @type {?} */ exp =\n        `The default value of 'pathMatch' is 'prefix', but often the intent is to use 'full'.`;\n    throw new Error(\n        `Invalid configuration of route '{path: \"${fullPath}\", redirectTo: \"${route.redirectTo}\"}': please provide 'pathMatch'. ${exp}`);\n  }\n  if (route.pathMatch !== void 0 && route.pathMatch !== 'full' && route.pathMatch !== 'prefix') {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': pathMatch can only be set to 'prefix' or 'full'`);\n  }\n  if (route.children) {\n    validateConfig(route.children, fullPath);\n  }\n}\n/**\n * @param {?} parentPath\n * @param {?} currentRoute\n * @return {?}\n */\nfunction getFullPath(parentPath: string, currentRoute: Route): string {\n  if (!currentRoute) {\n    return parentPath;\n  }\n  if (!parentPath && !currentRoute.path) {\n    return '';\n  } else if (parentPath && !currentRoute.path) {\n    return `${parentPath}/`;\n  } else if (!parentPath && currentRoute.path) {\n    return currentRoute.path;\n  } else {\n    return `${parentPath}/${currentRoute.path}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {Route, UrlMatchResult} from './config';\nimport {UrlSegment, UrlSegmentGroup} from './url_tree';\n/**\n * \\@whatItDoes Name of the primary outlet.\n * \n * \\@stable\n */\nexport const PRIMARY_OUTLET = 'primary';\n\n/**\n * A collection of parameters.\n *\n * @stable\n */\nexport type Params = {\n  [key: string]: any\n};\n\n/**\n * Matrix and Query parameters.\n *\n * `ParamMap` makes it easier to work with parameters as they could have either a single value or\n * multiple value. Because this should be known by the user, calling `get` or `getAll` returns the\n * correct type (either `string` or `string[]`).\n *\n * The API is inspired by the URLSearchParams interface.\n * see https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams\n *\n * @stable\n */\nexport interface ParamMap {\n  has(name: string): boolean;\n  /**\n   * Return a single value for the given parameter name:\n   * - the value when the parameter has a single value,\n   * - the first value if the parameter has multiple values,\n   * - `null` when there is no such parameter.\n   */\n  get(name: string): string|null;\n  /**\n   * Return an array of values for the given parameter name.\n   *\n   * If there is no such parameter, an empty array is returned.\n   */\n  getAll(name: string): string[];\n\n  /** Name of the parameters */\n  readonly keys: string[];\n}\nclass ParamsAsMap implements ParamMap {\nprivate params: Params;\n/**\n * @param {?} params\n */\nconstructor(params: Params) { this.params = params || {}; }\n/**\n * @param {?} name\n * @return {?}\n */\nhas(name: string): boolean { return this.params.hasOwnProperty(name); }\n/**\n * @param {?} name\n * @return {?}\n */\nget(name: string): string|null {\n    if (this.has(name)) {\n      const /** @type {?} */ v = this.params[name];\n      return Array.isArray(v) ? v[0] : v;\n    }\n\n    return null;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\ngetAll(name: string): string[] {\n    if (this.has(name)) {\n      const /** @type {?} */ v = this.params[name];\n      return Array.isArray(v) ? v : [v];\n    }\n\n    return [];\n  }\n/**\n * @return {?}\n */\nget keys(): string[] { return Object.keys(this.params); }\n}\n\nfunction ParamsAsMap_tsickle_Closure_declarations() {\n/** @type {?} */\nParamsAsMap.prototype.params;\n}\n\n/**\n * Convert a {\\@link Params} instance to a {\\@link ParamMap}.\n * \n * \\@stable\n * @param {?} params\n * @return {?}\n */\nexport function convertToParamMap(params: Params): ParamMap {\n  return new ParamsAsMap(params);\n}\n\nconst /** @type {?} */ NAVIGATION_CANCELING_ERROR = 'ngNavigationCancelingError';\n/**\n * @param {?} message\n * @return {?}\n */\nexport function navigationCancelingError(message: string) {\n  const /** @type {?} */ error = Error('NavigationCancelingError: ' + message);\n  ( /** @type {?} */((error as any)))[NAVIGATION_CANCELING_ERROR] = true;\n  return error;\n}\n/**\n * @param {?} error\n * @return {?}\n */\nexport function isNavigationCancelingError(error: Error) {\n  return ( /** @type {?} */((error as any)))[NAVIGATION_CANCELING_ERROR];\n}\n/**\n * @param {?} segments\n * @param {?} segmentGroup\n * @param {?} route\n * @return {?}\n */\nexport function defaultUrlMatcher(\n    segments: UrlSegment[], segmentGroup: UrlSegmentGroup, route: Route): UrlMatchResult|null {\n  const /** @type {?} */ parts = /** @type {?} */(( route.path)).split('/');\n\n  if (parts.length > segments.length) {\n    // The actual URL is shorter than the config, no match\n    return null;\n  }\n\n  if (route.pathMatch === 'full' &&\n      (segmentGroup.hasChildren() || parts.length < segments.length)) {\n    // The config is longer than the actual URL but we are looking for a full match, return null\n    return null;\n  }\n\n  const /** @type {?} */ posParams: {[key: string]: UrlSegment} = {};\n\n  // Check each config part against the actual URL\n  for (let /** @type {?} */ index = 0; index < parts.length; index++) {\n    const /** @type {?} */ part = parts[index];\n    const /** @type {?} */ segment = segments[index];\n    const /** @type {?} */ isParameter = part.startsWith(':');\n    if (isParameter) {\n      posParams[part.substring(1)] = segment;\n    } else if (part !== segment.path) {\n      // The actual URL part does not match the config, no match\n      return null;\n    }\n  }\n\n  return {consumed: segments.slice(0, parts.length), posParams};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Route} from './config';\nimport {RouterStateSnapshot} from './router_state';\n/**\n * \\@whatItDoes Represents an event triggered when a navigation starts.\n * \n * \\@stable\n */\nexport class NavigationStart {\n/**\n * @param {?} id\n * @param {?} url\n */\nconstructor(\npublic id: number,\npublic url: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return `NavigationStart(id: ${this.id}, url: '${this.url}')`; }\n}\n\nfunction NavigationStart_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationStart.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationStart.prototype.url;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation ends successfully.\n * \n * \\@stable\n */\nexport class NavigationEnd {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} urlAfterRedirects\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic urlAfterRedirects: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`;\n  }\n}\n\nfunction NavigationEnd_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.urlAfterRedirects;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation is canceled.\n * \n * \\@stable\n */\nexport class NavigationCancel {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} reason\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic reason: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return `NavigationCancel(id: ${this.id}, url: '${this.url}')`; }\n}\n\nfunction NavigationCancel_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.reason;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation fails due to an unexpected error.\n * \n * \\@stable\n */\nexport class NavigationError {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} error\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic error: any) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`;\n  }\n}\n\nfunction NavigationError_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.error;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when routes are recognized.\n * \n * \\@stable\n */\nexport class RoutesRecognized {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} urlAfterRedirects\n * @param {?} state\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic urlAfterRedirects: string,\npublic state: RouterStateSnapshot) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n\nfunction RoutesRecognized_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.urlAfterRedirects;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.state;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered before lazy loading a route config.\n * \n * \\@experimental\n */\nexport class RouteConfigLoadStart {\n/**\n * @param {?} route\n */\nconstructor(public route: Route) {}\n/**\n * @return {?}\n */\ntoString(): string { return `RouteConfigLoadStart(path: ${this.route.path})`; }\n}\n\nfunction RouteConfigLoadStart_tsickle_Closure_declarations() {\n/** @type {?} */\nRouteConfigLoadStart.prototype.route;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a route has been lazy loaded.\n * \n * \\@experimental\n */\nexport class RouteConfigLoadEnd {\n/**\n * @param {?} route\n */\nconstructor(public route: Route) {}\n/**\n * @return {?}\n */\ntoString(): string { return `RouteConfigLoadEnd(path: ${this.route.path})`; }\n}\n\nfunction RouteConfigLoadEnd_tsickle_Closure_declarations() {\n/** @type {?} */\nRouteConfigLoadEnd.prototype.route;\n}\n\n\n/**\n * @whatItDoes Represents a router event.\n *\n * One of:\n * - {@link NavigationStart},\n * - {@link NavigationEnd},\n * - {@link NavigationCancel},\n * - {@link NavigationError},\n * - {@link RoutesRecognized},\n * - {@link RouteConfigLoadStart},\n * - {@link RouteConfigLoadEnd}\n *\n * @stable\n */\nexport type Event = NavigationStart | NavigationEnd | NavigationCancel | NavigationError |\n    RoutesRecognized | RouteConfigLoadStart | RouteConfigLoadEnd;\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["APP_INITIALIZER", "InjectionToken", "Injectable", "ApplicationRef", "of", "LOCATION_INITIALIZED", "Subject", "ɵgetDOM", "NgModule", "PlatformLocation", "Inject", "APP_BASE_HREF", "Optional", "LocationStrategy", "SkipSelf", "NgModuleFactoryLoader", "SystemJsNgModuleLoader", "Compiler", "Location", "Injector", "mergeMap", "concatMap", "ChangeDetectorRef", "Attribute", "ComponentFactoryResolver", "ViewContainerRef", "Directive", "ContentChildren", "<PERSON><PERSON><PERSON>", "ElementRef", "HostBinding", "Input", "map", "from", "every", "isDevMode", "NgModuleRef", "fromPromise", "NgModuleFactory", "BehaviorSubject", "EmptyError", "_catch", "first", "concatAll", "ɵisPromise"], "mappings": ";;;;;;AsBAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;;;;;;;;;;;;;ADvIP,IAAA,eAAA,IAAA,YAAA;;;;;;;;KAOA;;;;;;;CAPA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;KA0CA;;;;;;;;;;;;;;;;;;;;;;;;;KA0CA;;;;;;;;;;;;;;;;;;;;;;;KAwCA;;;;;;;;;;;;;;;;;;;;;;IA9CA,SAAA,gBAAA,CAAA,EAAA,EAAA,GAAA,EAAA,iBAAA,EAAA,KAAA,EAAA;;;;;KA0FA;;;;;;;;;;;;;;;AA5EA,IAAA,oBAAA,IAAA,YAAA;;;;;QAqHA,IAnHG,CAmHH,KAAA,GAAA,KAAA,CAAA;KACA;;;;;;CAtHA,EAAA,CAAA,CAAA;;;;;;AAWA,IAAA,kBAAA,IAAA,YAAA;;;;;QA+HA,IA7HG,CA6HH,KAAA,GAAA,KAAA,CAAA;KACA;;;;;;CAhIA,EAAA,CAAA,CAAA;;;;;;;;;;;;;AD5DA,IAAA,cAAA,GAAA,SAAA,CAAA;;;;;IAKA,SAAA,WAAA,CAH+B,MAG/B,EAAA;QAAA,IAAA,CAAA,MAAA,GAAA,MAAA,IAHiE,EAGjE,CAAA;KAHsE;;;;;IAQtE,WAAA,CAAA,SAAA,CAAA,GANG,GAMH,UANG,IAAA,EAMH,EAAA,OAAA,IAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;;;;;IAMA,WAAA,CAAA,SAAA,CAAA,GAAA,GAAA,UAAI,IAAJ,EAAA;QACA,IAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;;;;;KAKA,CAAA;;;;;IAMA,WAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,IATW,EASX;QACA,IAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;;;;QAPG,OAWH,EAXyB,CAWzB;KACA,CAAA;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAA,0BAAA,GAAA,4BAAA,CAAA;;;;;;;;;CASA;;;;;;;;;;;;;;AAgBA,SAAA,iBAAA,CAAA,QAAA,EAAA,YAAA,EAAA,KAAA,EAAA;IAEE,qBAAF,KAAA,GAAA,EAAA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA;IACA,IAAA,KAAA,CAAA,MAAA,GAAA,QAAA,CAAA,MAAA,EAAA;;QAEI,OA5BO,IAAA,CAAK;KA6Bb;IAED,IAAF,KAAA,CAAA,SAAA,KAAA,MAAA;;;QAII,OAAJ,IAAA,CAAA;KACA;IACA,qBAAA,SAAA,GAAA,EAAA,CA5BU;;IA8BV,KAAA,qBAAA,KAAA,GA5BgC,CAAC,EA4BjC,KAAA,GAAA,KAAA,CAAA,MAAA,EAAA,KAAA,EAAA,EAAA;QACA,qBAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA;QA5BA,qBAAA,OAAiC,GAAjC,QAAA,CAAA,KAAA,CAAA,CAAA;;QA8BA,IAAM,WA5BO,EA4Bb;YACA,SAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,GAAA,OAAA,CAAA;SACA;aA1BS,IA4BT,IA5BW,KA4BX,OAAA,CAAA,IA5BmC,EA4BnC;;;SDzKA;;;;;;;;;;;AAkXA,IAAA,kBAAA,IAAA,YAAA;;;;;IAIA,SAAA,kBAAA,CAAA,MAAA,EAAA,MAAA,EAAA;;;;;CAJA,EAAA,CAAA,CAAA;;;;;;AAqBA,SAAA,cAAA,CAAA,MAAA,EAAA,UAAA,EAAA;IAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAA,GAAA,EAAA,CAAA,EAAA;;IAEA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;;;;;;;;;;;;;QAuBA,MAAA,IAAA,KAAA,CAAA,0CAAA,GAAA,QAAA,GAAA,iWAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA;QACM,MArBM,IAqBZ,KAAA,CArBa,kCAqBb,GAAA,QAAA,GAAA,8BAAA,CAAA,CAAA;KACA;IAEA,IAAA,CAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,MAAA,IAAA,KAAA,CAAA,MAAA,KAAA,cAAA,CAAA,EAAA;QACM,MArBM,IAqBZ,KAAA,CArBa,kCAqBb,GAAA,QAAA,GAAA,yDAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,QAAA,EAAA;QACM,MArBM,IAqBZ,KAAA,CAAA,kCAAA,GAAA,QAAA,GAAA,oDAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,YAAA,EAAA;QACM,MArBM,IAqBZ,KAAA,CArBa,kCAqBb,GAAA,QAAA,GAAA,wDAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,YAAA,EAAA;QACM,MArBM,IAAC,KAqBb,CAAA,kCAAA,GAAA,QAAA,GAAA,sDAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,SAAA,EAAA;QACM,MArBM,IAqBZ,KAAA,CArBa,kCAqBb,GArByD,QAqBzD,GAAA,qDAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,IAAA,IAAA,KAAA,CAAA,OAAA,EAAA;QACM,MArBM,IAAC,KAAQ,CAqBrB,kCAAA,GArByD,QAqBzD,GAAA,6CAAA,CAAA,CAAA;KACA;IAEA,IAAA,KAAA,CAAA,UAAA,KAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,KAAA,CAAA,YAAA,EAAA;QACM,MAAN,IAAA,KAAA,CAAA,kCAAA,GArBoD,QAAa,GAqBjE,2FAAA,CAAA,CAAA;KACA;IACA,IAAA,KAAA,CAAA,IAAA,KAAA,KAAA,CAAA,IAAA,KAAA,CAAA,OAAA,KAAA,KAAA,CAAA,EAAA;QACM,MArBM,IAAC,KAAQ,CAqBrB,kCAAA,GAAA,QAAA,GAAA,0DAAA,CAAA,CAAA;KACA;IAEA,IAAI,OAAJ,KAAA,CAAA,IAAA,KAAA,QAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KACmD,GADnD,EAAA;QAEA,MAAA,IAAA,KAAA,CAAA,kCAAA,GAAA,QAAA,GAAA,mCAAA,CAAA,CAAA;KACA;IACA,IAAI,KAAJ,CArBU,IAAI,KAAA,EAqBd,IAAA,KAAA,CAAA,UAAA,KAAA,KAAA,CAAA,IAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,EAAA;QAEA,qBAAA,GAAA,GAAA,sFAAA,CAAA;QACM,MArBM,IAqBZ,KAAA,CArBsB,2CAqBtB,GAAA,QAAA,GAAA,oBAAA,GAAA,KAAA,CAAA,UAAA,GAAA,oCAAA,GAAA,GAAA,CAAA,CAAA;KACA;IACA,IAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,IAAA,KAAA,CAAA,SAAA,KAAA,MAAA,IAAA,KAAA,CAAA,SAAA,KAAA,QAAA,EAAA;QACA,MAAA,IAAA,KAAA,CAAA,kCAAA,GAAA,QAAA,GAAA,oDAAA,CAAA,CAAA;;;;;;;;;;;AAWA,SAAA,WAAA,CAAA,UAAA,EAAA,YAAA,EAAA;IACA,IAAA,CAAA,YAAA,EAAA;QAzBA,OAAA,UAAA,CAAA;KA0BA;IACA,IAAA,CAAA,UAAA,IAAA,CAAA,YAAA,CAAA,IAAA,EAAA;QAzBA,OAAA,EAAA,CAAA;KA0BA;SACA,IAAA,UAAA,IAAA,CAAA,YAAA,CAAA,IAAA,EAAA;QAzBA,OAAA,UAAA,GAAA,GAAA,CAAA;KA0BA;SACA,IAAA,CAAA,UAAA,IAAA,YAAA,CAAA,IAAA,EAAA;QACA,OAAA,YAAA,CAAA,IAAA,CAAA;;SDneA;;;;;;;;;;;;;;;;AAsBA,SAAA,kBAAA,CAAA,CAAA,EAAA,CAAA,EAAA;IAMA,IAAA,CAAA,CAAA,MAAA,KAAA,CAAA,CAAA,MAAA;QACA,OALS,KAKT,CAAA;IACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,EAAA;;;;;;;;;;;AAWA,SAAA,YAAA,CAAA,CAAA,EAAA,CAAA,EAAA;IACE,qBAAF,EATkB,GASlB,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACE,qBAAF,EAAA,GAAA,MATkB,CASlB,IATsB,CAAG,CASzB,CAAA,CAAA;IACA,IAAI,EAAJ,CAAO,MATM,IASb,EAAA,CAAA,MAAA,EAAA;QACI,OAAJ,KAAA,CAAA;KACA;IACA,qBAAA,GAAA,CAAA;IACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;QACA,GATS,GAST,EATc,CASd,CAAA,CAAA,CAAA;QACA,IAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;CAqBA;;;;;;;;;;;AAcA,SAAA,OAAA,CAAA,MAAA,EAAA,QAAA,EAAA;IACA,KAAA,qBAAA,IAAA,IAAA,MAAA,EAAA;;;;;;;;;;;;AAcA,SAAA,UAAA,CAAA,GAAA,EAAA,EA9BQ,EA8BR;IACE,IAAF,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,MAAA,KA9BmC,CA8BnC,EAAA;QAEA,OA9BWI,qBAAI,CAAA,EA8Bf,CA9BsB,CAAG;KA+BzB;IACA,qBAAA,QAAA,GAAA,EAAA,CAAA;IACA,qBAAA,QAAA,GAAA,EAAA,CAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IA9BA,OAAA,CAAA,GAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA;QA+BA,qBAAA,MAAA,GAAA4B,qBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,UAAA,CAAA,EAAA,EAAA,OAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,CAAA,KAAA,cAAA,EAAA;YACA,QAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;SAEA;aACA;YACA,QA9BkB,CA8BlB,IA9BmB,CA8BnB,MAAA,CA9B+B,CA8B/B;SACA;;;;;CAKA;;;;;;;;;;;;;;;;;KAmBA;IACA,IAAAY,wBAAA,CAAA,KAAA,CAAA,EAAA;;;;QD5IA,OAAAP,uCAAA,CAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAAA,YAAA,CAAA,SAR6B,EAQ7B,SAAA,EAAA,KAAA,EAAA;IACA,IAAM,KAAN,EAAA;QACA,OAAA,gBAAA,CAAA,SAAA,CAAA,WAAA,EAAA,SAAA,CAAA,WAAA,CAAA;;;;;;;;;;;;;;;;;;;AAkBA,SAAA,kBAAA,CAAA,SAhBkB,EAgBlB,SAAA,EAAA;IACA,IAAI,CAAJ,SAAA,CAAA,SAAA,CAAA,QAAA,EAAA,SAAA,CAAA,QAAA,CAAA;QAhBA,OAAA,KAAA,CAAA;IAiBA,IAAI,SAAJ,CAAA,gBAAA,KAAA,SAAA,CAAA,gBAhB6D;QAA7D,OAAA,KAAA,CAAA;IAiBA,KAAA,qBAAA,CAAA,IAAA,SAAA,CAAA,QAAA,EAAA;QACA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,CAAA;YACA,OAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,SAAA,0BAAA,CAAA,SAAA,EAAA,SAAA,EAAA,cAAA,EAAA;IA7BA,IAAA,SAAA,CAAA,QAAA,CAAA,MAAA,GAAA,cAAA,CAAA,MAAA,EAAA;QA8BI,qBAAJ,OAAA,GAAA,SAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,cAAA,CAAA,MAAA,CAAA,CAAA;QAEA,IAAA,CAAA,SAAA,CAAA,OAAA,EAAA,cAAA,CAAA;YA7BA,OAAA,KAAA,CAAA;QA8BI,IAAI,SAAR,CA7BU,WAAmB,EA6B7B;YA7BwD,OAAO,KAAA,CAAM;QA8BjE,OAAJ,IAAA,CAAA;KACA;SA7BA,IAAA,SAAA,CAAyC,QAAzC,CAAA,MAAA,KAAA,cAAA,CAAA,MAAA,EAAA;QA8BA,IAAM,CAAN,SAAA,CAAA,SAAA,CAAA,QAAA,EAAA,cA7BmD,CAAC;YAApD,OAAA,KAAA,CAAA;QA8BA,KAAA,qBAAA,CAAA,IAAA,SAAA,CAAA,QAAA,EAAA;YACA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,CAAA;gBAEA,OAAA,KAAA,CAAA;YA7BA,IAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,SAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;gBA8BA,OAAA,KAAA,CAAA;SACA;QACI,OAAJ,IAAA,CAAA;KA7BA;SA8BA;QA7BA,qBAAA,OAAA,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA,EAAA,SAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;QA8BI,qBAAJ,IAAA,GAAA,cA7BgD,CA6BhD,KAAA,CAAA,SAAA,CAAA,QAAA,CA7B0E,MA6B1E,CAAA,CAAA;QACA,IAAA,CAAA,SAAA,CAAA,SAAA,CAAA,QAAA,EAAA,OAAA,CAAA;YACA,OAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAeG,IAiCH,CAAA,QAjCG,GAiCH,QAAA,CAAA;KACA;IAIA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,SAAA,EAAA,eAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;AAkDA,IAAA,eAAA,IAAA,YAAA;;;;;;QASA,IAAA,KAAA,GAAA,IAAA,CAAA;QAdA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;QAQI,IAAJ,CAAA,QAAA,GAAoB,QAApB,CAAA;;;;;;KAMA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,gBArEsD,GAqEtD,CArEuD,CAAI,EAqE3D,CAAA;IAKA,MAAA,CAAA,cAAA,CAAA,eAAA,CAAA,SAAA,EAAA,kBAvE8B,EAuE9B;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,MAAA,CAvE8C,IAAM,CAuEpD,IAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;CAnBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,IAAA,UAAA,IAAA,YAAA;;;;;;;QAhGG,IAsGH,CAAA,UAAA,GAAA,UAAA,CAAA;KACA;IAIA,MAAA,CAAA,cAAA,CAAA,UAAA,CAAA,SAAA,EAAA,cAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;;;CAhBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,SAAA,oBAAA,CAAA,OAAA,EAAA,EAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IACE,OAAO,CAxIC,OAAC,CAAO,QAAC,EAAS,UAAA,KAAQ,EAAiB,WAAa,EAwIlE;QACI,IAAI,WAxIC,KAAe,cAAA,EAAgB;YAyIlC,GAAG,GAxIG,GAAA,CAAI,MAAC,CAAM,EAAC,CAAE,KAAC,EAAM,WAAA,CAAY,CAAC,CAAC;SAyI1C;KACF,CAxIC,CAAC;IAyIH,OAxIO,CAwIT,OAAA,CAAA,QAAA,EAAA,UAAA,KAAA,EAAA,WAAA,EAAA;QACA,IAAA,WAAA,KAAA,cAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmEA,oBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,IAAA,EAAA;QACA,qBAAA,OAAA,GAAA,GAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;QAEA,qBAtJ+B,KAsJ/B,GAAA,oBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;;;;;;AAKA,IAAA,kBAzJC,GAyJD,IAzJC,oBAyJD,EAAA,CAAA;;;;;;;;;;;;;AAcA,SAAA,gBAAA,CAAA,OAAA,EAAA,IAAA,EAAA;IACA,IAAA,CAAA,OAAA,CAAA,WAAwB,EAAxB,EAAA;QACA,OA7JW,cA6JX,CAAA,OAAA,CAAA,CAAA;KACA;IAEA,IAAI,IAAJ,EAAA;QACA,qBAAA,OA7JgC,GA6JhC,OAAA,CAAA,QAAA,CAAA,cAAA,CAAA;YACA,gBAAA,CA7JkB,OA6JlB,CAAA,QAAA,CAAA,cAAA,CAAA,EA7JkD,KAAU,CA6J5D;YACA,EAAA,CAAA;QACA,qBAAA,UAAA,GAAA,EAAA,CAAA;QAEI,OA7JO,CA6JX,OA7JW,CAAS,QA6JpB,EA7J6B,UA6J7B,CAAA,EAAA,CAAA,EAAA;YAEA,IAAA,CAAA,KAAA,cAAA,EAAA;gBA7JA,UAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,GAAA,gBAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;aA8JA;SACA,CAAA,CAAA;QACA,OAAA,UA7Je,CAAA,MA6Jf,GAAA,CAAA,GA7JiB,OA6JjB,GAAA,GAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GA7JiE,GA6JjE,OA7J2E,CA6J3E;KACA;SAEA;QAEA,qBAAA,QAAA,GAAA,oBAAA,CAAA,OAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA;YAEA,IAAA,CAAA,KAAA,cAAA,EAAA;gBACA,OAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,QAAA,CAAA,cAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;aACA;;;;;CAKA;;;;;;;CAOA;;;;;;;CAOA;;;;;;;CAOA;;;;;;;CAOA;;;;;AAKA,SAAA,oBAAA,CAAA,MAAA,EAAA;IAEE,qBA5KkB,SA4KpB,GAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CA5KwD,CA4KxD,GAAA,CAAA,UAAA,IAAA,EAAA;QACA,qBAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,CAAA;QAEA,OAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CA5KmB,GA4KnB,KAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,MAAA,CAAA,IAAA,CAAA,GAAA,GAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;;;;;AAKA,IAAA,UAAA,GAAA,gBAAA,CAAA;;;;;;;;;AAUA,IAAA,cAAA,GAAA,WAAA,CAAA;;;;;;;;;AAUA,IAAA,oBAAA,GAAA,UAAA,CAAA;;;;;;;;CASA;AAvLA,IAAA,SAAA,IAAA,YAAA;;;;;;QA2LA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA;KACA;;;;;QAOI,IAAJ,CAAA,eAAA,CAAA,GAAA,CAAA,CAzLe;QA0Lf,IAAA,IAAA,CAAA,SAAA,KAAA,EAAA,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;;;;QAIA,OAAA,IAAA,eAAA,CAAA,EAAA,EAAA,IAAA,CAAA,aAAA,EAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,SAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;QACI,IAAJ,IAAA,CAAA,eAAA,CAAA,GAAA,CAAA,EAAA;YACA,GAAA;;;;QAIA,OAAA,MAAA,CAAA;KACA,CAAA;;;;;QA1LA,OAAA,IAAA,CAAA,eAAA,CAAA,GAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA;KAgMA,CAAA;;;;IAMA,SAAA,CAAA,SAAA,CAAA,aAAA,GAAA,YAAA;QACI,IAAI,IAAR,CA/LU,SA+LV,KAAA,EA/L6B,EA+L7B;YACM,OAAN,EA/Le,CA+Lf;SACK;QAED,IAAJ,CAAA,eAAA,CAAA,GAAA,CAAA,CA/LiB;QAgMjB,qBAAA,QAAA,GAAA,EAAA,CAAA;QACA,IAAM,CAAN,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;YACA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,CAAA;SAEA;QACI,OAAJ,IAAA,CAAA,cAAA,CAAA,GAAA,CA/LmC,IA+LnC,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA;YACM,IAAI,CA/LC,OAAC,CAAO,GAAC,CAAG,CAAC;YAgMlB,QAAQ,CAAd,IAAA,CAAA,IAAA,CAAA,YAAA,EA/LuC,CAAC,CA+LxC;SACK;QAED,qBA/LI,QAA2C,GA+LnD,EAAA,CAAA;QACI,IAAI,IA/LC,CAAI,cAAC,CAAc,IAAI,CA+LhC,EAAA;YACM,IAAN,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;YACA,QAAA,GAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA;SAEA;QACA,qBAAA,GAAA,GAAA,EAAA,CA/LgC;QAgMhC,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;YAEA,GA/LW,GAAA,IA+LX,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;SACA;;;;QA3LA,OAAA,GAAA,CAAA;KAgMA,CAAA;;;;IAKA,SAAA,CAAA,SAAA,CAAA,YAAA,GAAA,YAAA;QACI,qBAAJ,IAAA,GAAA,aAAA,CAAA,IAAA,CAAA,SA/L8C,CA+L9C,CA/L+D;QAgM/D,IAAA,IAAA,KAAA,EAAA,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;;;;QA7LA,OAAA,IAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,CAAA;KAkMA,CAAA;;;;IAIA,SAAA,CAAA,SAAA,CAAA,iBAjMW,GAiMX,YAAA;QACA,qBAAA,MAAA,GAAA,EAAA,CAAA;;;;;KA/LA,CAAA;;;;;IAyMA,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,MApMiB,EAoMjB;QACI,qBAAJ,GApMQ,GAAa,aAoMrB,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;QACI,IAAI,CAAR,GApMS,EAoMT;YACM,OAAN;SACA;QACA,IAAA,CAAA,OAAA,CAAa,GApMG,CAoMhB,CAAA;QACA,qBApMqB,KAAC,GAoMtB,EAAA,CAAA;QACA,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,CAAA,EAAA;YACA,qBAAA,UAAA,GAAA,aAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;YAEA,IAAA,UApMsB,EAoMtB;gBACA,KAAA,GAAA,UAAA,CAAA;;;;;KAjMA,CAAA;;;;;IA2MA,SAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAtMU,MAAY,EAsMtB;QACI,qBAAJ,GAtMQ,GAAa,gBAsMrB,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;QACI,IAAI,CAAR,GAtMS,EAsMT;YACM,OAAN;SACA;QACA,IAAA,CAAA,OAAA,CAAa,GAtMG,CAsMhB,CAAA;QACA,qBAtMqB,KAAC,GAsMtB,EAAA,CAAA;QACA,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,CAAA,EAAA;YACA,qBAAA,UAAA,GAAA,uBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;YAEA,IAAA,UAAA,EAAA;gBACA,KAAA,GAAA,UAAA,CAAA;gBAEA,IAAA,CAAA,OAAA,CAAA,KAtM8B,CAsM9B,CAAA;;SAEA;QACA,qBAtMyB,UAAC,GAAY,MAsMtC,CAAA,GAAA,CAAA,CAAA;QACA,qBAAA,UAtMiC,GAsMjC,MAAA,CAAA,KAAA,CAAA,CAAA;QACA,IAAA,MAAA,CAAA,cAtMgB,CAAU,UAsM1B,CAAA,EAtM6B;;YAwMvB,qBAAN,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA;YACA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;gBAtMA,UAAA,GAAA,CAAA,UAAA,CAAA,CAAA;;aAwMA;YACA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;SACA;;;;;KAnMA,CAAA;;;;;IA+MA,SAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,YAAA,EAAA;;;QAIA,OAAA,CAAU,IAxMC,CAwMX,eAxM0B,CAwM1B,GAAA,CAxMmC,IAwMnC,IAAA,CAAA,SAAA,CAxMmD,MAwMnD,GAAA,CAAA,EAAA;YACA,qBAAA,IAAA,GAAA,aAAA,CAAA,IAAA,CAxMmD,SAwMnD,CAAA,CAAA;YACA,qBAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;YAIA,IAAQ,IAAR,KAAA,GAAA,IAAA,IAAA,KAxMiC,GAAG,IAAA,IAwMpC,KAxMiD,GAAC,EAAI;gBAyM9C,MAAR,IAAA,KAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA;aACA;YACA,qBAAA,UAAA,KAAA,SAAA,EAAA,CAAA;YAxMA,IAAA,IAAA,CAAa,OAAb,CAAA,GAAA,CAAkB,GAAlB,CAAA,CAAA,EAAA;gBAyMQ,UAAU,GAxMG,IAwMrB,CAAA,MAAA,CAAA,CAAA,EAxMoC,IAwMpC,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBACA,IAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA;gBAEA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;aACA;iBACA,IAAA,YAAA,EAxMsE;gBAyM5D,UAAV,GAAA,cAAA,CAAA;aACA;YAEA,qBAAA,QAAA,GAAA,IAAA,CAAA,aAAA,EAAA,CAAA;YACA,QAAA,CAAA,UAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,MAAA,KAAA,CAAA,GAAA,QAAA,CAAA,cAAA,CAAA;;;;;KAtMA,CAAA;;;;;IAGG,SAAH,CAAA,SAAA,CAAA,cAAA,GAAG,UAAA,GAAH,EAAA,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA;;;;;IAkNA,SAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UA7MW,GA6MX,EAAA;QACA,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,EAAA;;;;;KA3MA,CAAA;;;;;IAqNA,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,GAAA,EAAA;;YD/vBA,MAAA,IAAA,KAAA,CAAA,aAAA,GAAA,GAAA,GAAA,KAAA,CAAA,CAAA;;;;CCwXA,EAAA,CAAA,CAAA;;;;;;;;ADxVA,IAAA,OAAA,IAAA,YAAA;;;;;;;IAWA,OAAA,OAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AAVA,IAAA,gBAAA,IAAA,YAAA;;;;;;;IAsBA,OAAA,gBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;CAQA;;;;;;;CAQA;;;;;;;CASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAVA,SAAA,cAAA,CAAA,cAAoC,EAApC,YAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA;QA+CI,IAAI,CAAC,YAAT,GAAA,YAAA,CAAA;QACA,IAAA,CAAA,aAAA,GAAA,aAAA,CAAA;;;;QAIA,IAAA,CAAA,QAAA,GAAA,cAAA,CAAA,GAAA,CAAAD,yBAAA,CAAA,CAAA;KACA;;;;;QAtBA,IAAA,KAAA,GAAA,IAAA,CAAA;QA8BA,qBAAA,SAAA,GA3C8B,IA2C9B,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,cAAA,CAAA,CAAA;;QAEA,OAAAK,0BAAA,CAAA,IAAA,CA3Ce,SAAa,EA2C5B,UAAA,CAAA,EAAA;YACA,IAAA,CAAA,YAAA,gBAAA,EAAA;;gBAGQ,KAAR,CAAA,cAAA,GAAA,KA3CmC,CA2CnC;;gBAGA,OAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;aACA;YACA,IAAA,CAAA,YAAA,OAAA,EAAA;;;;;KAzCA,CAAA;;;;;IAsDA,cAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAvCA;QAwCA,qBAAA,SAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,IAAA,EAAA,cAAA,CAAA,CAAA;QAEA,qBAAA,OAAA,GAAAT,qBAAA,CAAA,IAAA,CAAA,SAAA,EAAA,UAAA,gBAAA,EAAA,EAAA,OAAA,KAAA,CAAA,aAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,WAAA,qBAAA,IAAA,CAAA,QAAA,GAAA,CAAA,EAAA,CAAA,CAAA;QACA,OAAAS,0BAAA,CAAA,IAAA,CAAA,OAAA,EAAA,UAAA,CAAA,EAAA;YACA,IAAA,CAAA,YAAA,OAAA,EAAA;;;;;KA5CA,CAAA;;;;;;;;;;;;;;IAgEA,cAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,aAAA,EAAA,WAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;IAkBA,cAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,QAAA,EAAA,MAAA,EAAA,YAAA,EAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;IAgCA,cAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,QAAA,EAAA,YAxEyB,EAAS,MAwElC,EAAA,QAAA,EAAA,MAAA,EAAA,cAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,OAAA,GAAArC,qBAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,MAAA,CAAA,CAAA;QACA,qBAAA,gBAAA,GAAA4B,qBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,UAAA,CAAA,EAAA;YAEA,qBAAA,SAAA,GAAA,KAAA,CAAA,yBAAA,CAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,EAAA,QAAA,EAAA,MAAA,EAAA,cAAA,CAAA,CAAA;YACA,OAAAS,0BAAA,CAAA,IAAA,CAAA,SAAA,EAAA,UAAA,CAAA,EAAA;gBACA,IAAA,CAAA,YAAA,OAAA,EAAA;oBACA,OAAArC,qBAAA,CAAA,IAAA,CAAA,CAAA;iBACA;gBACA,MAxEkB,CAwElB,CAAA;aACA,CAAA,CAAA;SACA,CAAA,CAAA;QACA,qBAAA,yBAxE6C,GAAKuC,iCAwElD,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA;QACA,qBAAA,MAAA,GAAAD,yBAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,EAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QAEA,OAAAD,0BAAA,CAxEc,IAAI,CAwElB,MAxEkB,EAwElB,UAAA,CAAA,EAAA,CAAA,EAAA;YACA,IAAA,CAAA,YAAAD,+BAAA,EAAA;gBAEA,IAxEc,KAwEd,CAAA,gBAAA,CAAA,YAAA,EAAA,QAAA,EAAA,MAAA,CAAA,EAAA;oBACA,OAAApC,qBAAA,CAAA,IAAA,eAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;iBACA;;;;;;;;;;;;;;;;;;;;;;;;;IA6BA,cAAA,CAAA,SAAA,CAAA,yBAAA,GAAA,UAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,EAAA,MAAA,EAAA,cAAA,EAAA;QACA,IAAA,SAAA,CAAA,KAAA,CAAA,KAAA,MAAA,EAAA;YAEQ,OAAR,OAtFS,CAsFT,YAAA,CAAA,CAAA;SACA;QAEA,IAAA,KAAA,CAAA,UAAA,KAAA,SAAA,EAAA;YAEA,OAAA,IAtFmB,CAsFnB,wBAAA,CAAA,QAAA,EAAA,YAAA,EAAA,KAAA,EAAA,KAAA,CAAA,CAAA;SACA;;;;;;;;;;;;;;;IAoBA,cAAA,CAAA,SAAA,CAAA,sCAAA,GAAA,UAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA,MAAA,EAAA;;;;;;;;;;;;;IAgBA,cAAA,CAAA,SAAA,CAAA,iDApGiD,GAoGjD,UAAA,QAAA,EAAA,MApGkE,EAoGlE,KAAA,EAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA,qBAAA,CAAA,EAAA,qBAAA,KAAA,CAAA,UAAA,IAAA,EAAA,CAAA,CAAA;QACA,IAAM,EAAN,KApGa,CAoGb,UAAA,GAAA,UAAA,CAAA,GAAA,CApG0C,EAoG1C;YACA,OAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;SACA;;;;;;;;;;;;;;;IAmBA,cAAA,CAAA,SAAA,CAAA,6CAAA,GAAA,UA5G4C,QA4G5C,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAA,EAAA,GAAA,KAAA,CAAA,YAAA,EAAA,KAAA,EAAA,QAAA,CAAA,EAAA,OAAA,GAAA,EAAA,CAAA,OAAA,EAAA,gBAAA,GAAA,EAAA,CAAA,gBAAA,EA5G+B,SA4G/B,GAAA,EAAA,CAAA,SAAA,EAAA,uBAAA,GAAA,EAAA,CAAA,uBAAA,CAAA;QACA,IAAA,CAAA,OAAA;YAEA,OAAA,OAAA,CAAA,YAAA,CAAA,CAAA;QACA,qBAAA,OACU,GADV,IAAA,CACkB,qBADlB,CA3GkC,gBA2GlC,qBAAA,KAAA,CAAA,UAAA,sBAAA,uBAAA,EAAA,CAAA;QAGA,IAAA,EAAA,KAAA,CAAA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACA,OAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;IAgBA,cAAA,CAAA,SAAA,CAAA,wBAAA,GAAA,UAAA,QAAA,EAAA,eAAA,EAAA,KAlHuD,EAkHvD,QAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAA,KAAa,CAlHC,IAkHd,KAAA,IAAA,EAAA;YACA,IAAA,KAAA,CAAA,YAAA,EAAA;gBAEA,OAAA4B,qBAlHqB,CAkHrB,IAAA,CAAA,IAAA,CAAA,YAAA,CAlHsC,IAAS,CAAG,QAkHlD,CAAA,QAAA,EAAA,KAAA,CAAA,EAAA,UAAA,GAAA,EAAA;oBACA,KAAA,CAAA,aAAA,GAAA,GAAA,CAAA;oBAEA,OAAA,IAAA,eAAA,CAAA,QAAA,EAlHmD,EAkHnD,CAAA,CAAA;iBACA,CAAA,CAAA;aAlHA;YAoHA,OAAA5B,qBAAA,CAAA,IAAA,eAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA;SACA;QAEA,IAAA,EAAA,GAAA,KAAA,CAAA,eAAA,EAAA,KAAA,EAAA,QAAA,CAAA,EAAA,OAAA,GAAA,EAAA,CAAA,OAlHW,EAkHX,gBAAA,GAAA,EAAA,CAAA,gBAlH0B,EAAa,SAkHvC,GAAA,EAAA,CAAA,SAAA,CAAA;QACA,IAAM,CAAN,OAAA;YACM,OAAN,OAAA,CAAA,eAAA,CAAA,CAAA;QAEA,qBAAA,iBAlH2B,GAkH3B,QAjHgB,CAiHhB,KAAA,CAAA,SAAA,CAjHiC,CAiHjC;QAGA,qBAlHyB,YAAW,GAkHpC,IAAA,CAAA,cAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;QACA,OAAAgB,+BAAA,CAAA,IAAA,CAAA,YAAA,EAAA,UAAA,YAlH+B,EAkH/B;YACA,qBAAA,WAAA,GAAA,YAjH8C,CAiH9C,MAAA,CAAA;YAEA,qBAAA,WAAA,GAAA,YAAA,CAAA,MAAA,CAAA;YAEA,IAAA,EAAA,GAAA,KAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,EAAA,YAAA,GAAA,EAAA,CAAA,YAAA,EAlHuB,cAkHvB,GAAA,EAAA,CAAA,cAAA,CAAA;YACA,IAAQ,cAlHe,CAkHvB,MAAA,KAAA,CAAA,IAAA,YAAA,CAAA,WAAA,EAAA,EAAA;gBACA,qBAAA,WAAA,GAAA,KAAA,CAAA,cAAA,CAAA,WAAA,EAAA,WAAA,EAAA,YAAA,CAAA,CAAA;gBAEA,OAAAY,qBAAA,CAAA,IAAA,CAAA,WAAA,EAAA,UAlHY,QAAiB,EAkH7B,EAAA,OAAA,IAAA,eAAA,CACqB,gBAlHgB,EAiHrC,QAAA,CAjHqC,CAiHrC,EAjHqC,CAiHrC,CAjHkD;aAmHlD;YAGA,IAAA,WAAA,CAAA,MAAA,KAAA,CAAA,IAAA,cAAA,CAAA,MAAA,KAAA,CAAA,EAAA;gBACA,OAAA5B,qBAAA,CAAA,IAAA,eAAA,CAAA,gBAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;;;;;;;;;;IAYA,cAAA,CAAA,SAAA,CAAA,cAtHc,GAsHd,UAAA,QAAA,EAAA,KAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;;YAGA,OAAAA,qBAAA,CAAA,IAtHkB,kBAsHlB,CAtH0B,KAsH1B,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,CAAA;SACA;QAEA,IAAM,KAAN,CAAA,YAAA,EAtHuB;;YAyHvB,IAAA,KAAA,CAAA,aAtHsB,KAuHJ,SADlB,EAAA;gBAEA,OAAAA,qBAAA,CAAA,KAAA,CAAA,aAAA,CAtHsC,CAsHtC;aACA;YACA,OAAAgB,+BAAA,CAAA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,QAAA,EAAA,KAAA,CAAA,EAAA,UAAA,UAAA,EAAA;gBACA,IAAA,UAAA,EAAA;oBAEA,OAAAY,qBAAA,CAAA,IAtHe,CAAa,KAsH5B,CAtH6B,YAsH7B,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA,EAAA,KAAA,CAAA,EAAA,UAAA,GAAA,EAAA;wBACA,KAAA,CAAA,aAAA,GAAA,GAAA,CAAA;wBACA,OAAA,GAAA,CAAA;qBAEA,CAtHmB,CAsHnB;iBACA;;;;;;;;;;;IAWA,cAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QACA,qBA1HoB,GA0HpB,GAAA,EAAA,CAAA;QACA,qBAAA,CAAA,GAAA,OAAA,CAAA,IAAA,CAAA;QAEA,OAAA,IAAA,EAAA;YACA,GAAA,GAAA,GAAA,CAAA,MAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;YACA,IAAA,CAAA,CAAA,gBAAA,KAAA,CAAA,EAAA;gBAxHU,OA0HV5B,qBAAA,CA1Ha,GA0Hb,CAAA,CAAA;aACA;YACA,IAAA,CAAA,CAAA,gBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,QAAA,CAAA,cAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqCA,cAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,gBAAA,EAAA,YAzI2B,EAyI3B;QACA,qBAAA,GAAA,GAzIiB,EAyIjB,CAAA;QACA,OAAA,CAAA,gBAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA;YAzIA,qBAAA,eAAA,GAAA,OAAA,CAAA,KAAA,QAAA,IAAA,CAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA;YA0IA,IAAQ,eAAR,EAAA;gBACA,qBAAA,UAAA,GAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;gBACA,GAAA,CAAA,CAAA,CAAA,GAAA,YAAA,CAAA,UAAA,CAAA,CAAA;aACA;iBACA;;;;;;;;;;;;;IAgBA,cAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,UAAA,EAAA,KAAA,EAAA,QAAA,EAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEI,qBAAJ,eAAA,GAAA,IAAA,CAAA,cAAA,CAAA,UAAA,EAAA,KAAA,CAAA,QAAA,EAAA,QAAA,EAAA,SAAA,CAAA,CAAA;QACA,qBAAA,QAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;IA6BA,cAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,UAAA,EAAA,oBAAA,EAAA,SAAA,EAAA;;;;;;;;;;;IAWA,cAAA,CAAA,SAAA,CAAA,YAAQ,GAAR,UAAA,oBAAA,EAAA,cAAA,EAAA;QACA,qBAAA,GAAA,GAAA,CAAA,CAAA;QACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,gBAAA,GAAA,cAAA,EAAA,EAAA,GAAA,gBAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,CAAA,GAAA,gBAAA,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,CAAA,CAAA,IAAA,KAAA,oBAAA,CAAA,IAAA,EAAA;gBACA,cAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;gBACA,OAAA,CAAA,CAAA;aACA;YAEA,GAAA,EAAA,CAAA;;;;;;;;;;;AAyBA,SAAA,eAAA,CAAA,cAlLkB,EAkLlB,KAAA,EAAA;IACA,qBAAA,OAlL8B,GAkL9B,KAAA,CAAA,OAlL8C,CAkL9C;IACA,IAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,KAAA,CAAA;QAEA,OAAAA,qBAAA,CAAA,IAAA,CAAA,CAAA;IACA,qBAAA,GAAA,GAAA4B,qBAAA,CAAA,IAAA,CAAAC,yBAAA,CAAA,OAAA,CAAA,EAAA,UAAA,cAAA,EAAA;;;;;;;;;;;;AAkBA,SAAA,KAAA,CAvLW,YAuLX,EAAA,KAAA,EAAA,QAAA,EAAA;IACA,IAAA,KAAA,CAAA,IAAA,KAAA,EAAA,EAAA;QAEA,IAAA,CAAA,KAAA,CAAA,SAAA,KAAA,MAAA,MAAA,YAAA,CAAA,WAAA,EAvLmC,IAuLnC,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA;YACA,OAAA,EAAA,OAAA,EAvLQ,KAuLR,EAAA,gBAAA,EAAA,EAAA,EAAA,SAAA,EAvL8C,CAAM,EAuLpD,uBAAA,EAAA,EAAA,EAAA,CAAA;SArLO;QAwLH,OAvLO,EAuLX,OAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,SAAA,EAAA,CAAA,EAAA,uBAAA,EAAA,EAAA,EAAA,CAAA;KACA;IACA,qBAAA,OAAA,GAAA,KAAA,CAAA,OAAA,IAAA,iBAAA,CAAA;IACA,qBAAA,GAAA,GAAA,OAAA,CAAA,QAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAAA;IAEE,IAAF,CAAA,GAAA,EAAA;QACI,OAAO;YACX,OAAA,EAAA,KAAA,EAAA,gBAAA,oBAAA,EAAA,CAAA,EAAA,SAAA,EAAA,CAAA,EAAA,uBAAA,EAAA,EAAA;SACA,CAAA;KACA;IACA,OAAA;QACA,OAAA,EAAA,IAAA;;;;;;;;;;;;;AAiBA,SAAA,KAAA,CAAA,YAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,MAAA,EAAA;IAEE,IAAI,cA7LC,CAAc,MAAC,GA6LtB,CAAA;QACM,0CAAN,CAAA,YA7L+C,EAAgB,cA6L/D,EAAA,MAAA,CAAA,EAAA;QACI,qBA7LM,CAAA,GAAI,IAAI,eAAA,CA8LV,gBADR,EAAA,8BAAA,CAAA,MAAA,EA5L+B,IA4L/B,eAAA,CAAA,cAAA,EAAA,YAAA,CAAA,QAAA,CAAA,CAAA,CA3LuF,CAAQ;QA8L3F,OA7LO,EAAA,YAAE,EAAa,oBAAA,CAAqB,CAAC,CAAC,EAAE,cAAA,EAAe,EA6LlE,EAAA,CAAA;KACG;IAED,IAAF,cAAA,CAAA,MAAA,KAAA,CAAA;QACA,0BAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,CAAA,EAAA;;;;;CAKA;;;;;AAMA,SAAA,oBAAA,CAAA,CAAA,EAAA;IACA,IAAA,CAAA,CAAA,gBAAA,KAAA,CAAA,IAAA,CAAA,CAAA,QAAA,CAAA,cAAA,CAAA,EAAA;;;;;;;;;;;;;AAeA,SAAA,kCAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IACE,KAtMY,IAsMd,EAAA,GAAA,CAAA,EAtMc,QAsMd,GAAA,MAAA,EAtMc,EAsMd,GAAA,QAAA,CAAA,MAAA,EAtMc,EAsMd,EAAA,EAAA;QAAA,IAAA,CAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,mBAAA,CAAA,YAAA,EAAA,cAAA,EAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;;;;;;;;;;;AAYA,SAAA,8BAAA,CAAA,MAAA,EAAA,mBAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IACA,GAAA,CAAA,cAAA,CAAA,GAAA,mBAAA,CAAA;IACE,KAAF,IAAA,EAAA,GAAA,CAAA,EAAA,QAAA,GAAA,MAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;QAAA,IAAA,CAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,CAAA,CAAA,IAAA,KAAA,EAAA,IAAA,SAAA,CAAA,CAAA,CAAA,KAAA,cAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,SAAA,mBAAA,CAAA,YAAA,EAAA,QAAA,EAAA,CAAA,EAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDvrBA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,CAAA,EAAA;QACA,qBAAA,CAAA,GAAA,QAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;AA4BA,SAAA,QAAA,CAAA,KAAA,EAAA,IAAA,EAAA;IAvBA,IAAA,KAAA,KAAA,IAAA,CAAqB,KAArB;QAwBA,OAAA,IAAA,CAAA;IAEE,KAAF,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,IAAA,CAAA,QAAA,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;QAAA,IAAA,KAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACA,qBAAA,MAAA,GAAA,QAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;AAYA,SAAA,QAAA,CAAA,KA3Bc,EAAO,IA2BrB,EAAA;IACA,IAAA,KAAA,KAAA,IAAA,CAAA,KAAA;QACA,OAAA,CAAA,IAAA,CAAA,CAAA;IACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,IAAA,CAAA,QAAA,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;QAAK,IAAL,KAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACA,qBAAA,IAAA,GAAA,QAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;QAEA,IAAA,IAAA,CAAA,MAAA,EAAA;YACA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;YACA,OAAA,IAAA,CAAA;;;;;AAKA,IAAA,QAAA,IAAA,YAAA;;;;;;;QAKA,IAjCG,CAiCH,QAAA,GAjC8B,QAiC9B,CAAA;KACA;;;;;;CANA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QDvDA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,IAAA,IAAA,CAAA;;QADA,cAAA,CAAA,KAAA,EAJmC,IAInC,CAAA,CAAA;;KACA;;;;;;;;;;;;AAoBA,SAAA,gBAAA,CAAA,OAAA,EAAA,aAAA,EAAA;IACE,qBAjBM,QAAA,GAAW,wBAAwB,CAiB3C,OAAA,EAAA,aAAA,CAAA,CAAA;IACE,qBAjBM,QAiBR,GAAA,IAAAM,oCAjBwB,CAkBlB,CADN,IAAA,UAAA,CAAA,EAAA,EAAA,EAAA,CAhB6B,CAgB7B,CAAA,CAAA;IAGE,qBAAF,WAjBiC,GAiBjC,IAAAA,oCAAA,CAAA,EAAA,CAAA,CAAA;IACE,qBAjBuB,SAiBzB,GAjB6B,IAiB7BA,oCAAA,CAAA,EAAA,CAAA,CAAA;IACA,qBAAA,gBAAA,GAAA,IAAAA,oCAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;AAYA,SAAA,wBAAA,CAAA,OAAA,EArBwB,aAqBxB,EAAA;IAGE,qBAAF,WAAA,GAAA,EArByC,CAqBzC;IACA,qBAAA,SAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyBA,SAAA,cAAA,CAAA,GAAA,EAAA,MAAA,EAAA,WAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,cAAA,EAAA;QAEa,IAAb,CAAA,GAAA,GAAa,GAAA,CAAb;QAGa,IAAb,CAAA,MAAA,GAAa,MAAb,CAAA;QAgCI,IAAI,CAAC,WAAT,GAAA,WAAA,CAAA;QACA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;KAKA;IAKA,MAAA,CAAA,cAAA,CAnCG,cAmCH,CAAA,SAAA,EAAA,aAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAnCsC,OAmCtC,IAAA,CAAA,eAAA,CAAA,WAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CArCG,cAqCH,CAAA,SAAA,EAAA,MAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CAvCG,cAuCH,CAAA,SAAA,EAAA,QAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAvCiD,CAuCjD,YAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,EAvC8E;;;KAA9E,CAAA,CAA8E;IA4C9E,MAAA,CAAA,cAAA,CAzCG,cAyCH,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,UAAA,CAzCwE,IAAM,CAyC9E,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CA3CG,cA2CH,CAAA,SAAA,EAAA,UAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IA3CgD,CA2ChD,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EA3C+E;;;KAA/E,CAAA,CAA+E;IAgD/E,MAAA,CAAA,cAAA,CAAI,cAAJ,CAAA,SAAA,EAAA,cAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,cAAA,CAAA,SAAA,EAAA,UAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;YA3CG,OA+CH,IAAA,CAAA,SAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAI,cAAJ,CAAA,SAAA,EAAA,eAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;YACA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA;;;;YAIA,OAAA,IAAA,CAAA,cAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;AAmFA,SAAA,0BAAA,CAAA,KAAA,EAAA;;IAEA,qBAAA,qBAAA,GAAA,UAjHoD,CAiHpD,MAjH4D,GAiH5D,CAAA,CAAA;IACA,OAAA,qBAAA,IAjH8B,CAiH9B,EAAA;;QAGA,qBAAA,MAAA,GAAA,UAAA,CAAA,qBAAA,GAAA,CAAA,CAAA,CAAA;;QACA,IAAM,OAAN,CAAA,WAAA,IAjH4B,OAiH5B,CAAA,WAAA,CAAA,IAAA,KAAA,EAAA,EAAA;YAEA,qBAAA,EAAA,CAAA;;SACA;aACA,IAAA,CAAA,MAAA,CAAA,SAAA,EAAA;YACA,qBAAA,EAAA,CAAA;SAEA;aACA;YACA,MAAA;SACA;KACA;IACA,OAAA,UAAA,CAAA,KAjHW,CAiHX,qBAjHiC,CAiHjC,CAAA,MAAA,CAjH0C,UAiH1C,GAjH6C,EAAC,IAiH9C,EAAA;QACA,qBAAA,MAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,MAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA7DA,SAAA,sBAAA,CAAA,GAAA,EAAA,MAAA,EAAA,WAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,EAAA,OAAA,EAAA;QAEa,IAAb,CAAA,GAAA,GAAa,GAAA,CAAb;QAEa,IAAb,CAAA,MAAA,GAAa,MAAb,CAAA;QAuII,IAAI,CAAC,WAAT,GAAA,WAAA,CAAmC;QAC/B,IAAI,CAAC,QAAT,GAAoB,QAApB,CAAA;QACI,IAAI,CAAC,IAAT,GAAA,IAAA,CAAA;QACI,IAAI,CAAC,MAAT,GAAA,MAAA,CAAA;QACA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA;;;;;KAKA;IAKA,MAAA,CAAA,cAAA,CAzIG,sBAyIH,CAAA,SAAA,EAAA,aAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAzI8C,OAyI9C,IAAA,CAAA,YAAA,CAzIsE,EAyItE;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CA3IG,sBA2IH,CAAA,SAAA,EAAA,MAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CA7IG,sBA6IH,CAAA,SAAA,EAAA,QAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IA7IyD,CA6IzD,YAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,EA7IsF;;;KAAtF,CAAA,CAAsF;IAkJtF,MAAA,CAAA,cAAA,CA/IG,sBA+IH,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,UAAA,CA/IgF,IAAM,CA+ItF,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CAjJG,sBAiJH,CAAA,SAAA,EAAA,UAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAjJwD,CAiJxD,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAjJuF;;;KAAvF,CAAA,CAAuF;IAsJvF,MAAA,CAAA,cAAA,CAAI,sBAAJ,CAAA,SAAA,EAAA,cAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,SAAA,EAAA,UAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;YAjJG,OAqJH,IAAA,CAAA,SAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,SAAA,EAAA,eAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;YAIA,OAAA,IAAA,CAAA,cAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;IAIA,sBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA;QAEA,qBAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,UAAA,OAAA,EAAA,EAAA,OAAA,OAAA,CAAA,QAAA,EAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAsHA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,IAAA,IAAA,CAAA;;QADA,cAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;;KACA;;;;;;;;;;;;;;;;CAwBA;;;;;;;;;;;;;;;;AAgBA,SAAA,qBAAA,CAAA,KAAA,EAAA;IACA,IAAA,KAAA,CA5PY,QA4PZ,EAAA;QACA,qBAAA,eAAA,GAAA,KAAA,CAAA,QAAA,CAAA;QACI,qBAAJ,YAAA,GAAA,KAAA,CAAA,eAAA,CAAA;QACA,KAAA,CA5PY,QA4PZ,GAAA,YAAA,CAAA;QACA,IAAA,CAAA,YAAA,CAAA,eAAA,CAAA,WAAA,EAAA,YAAA,CAAA,WAAA,CAAA,EAAA;YACQ,EAAR,KAAA,CAAA,WAAA,GAAA,IAAA,CAAA,YAAA,CAAA,WA5P2D,CA4P3D,CAAA;SACA;QACA,IAAA,eAAA,CAAA,QAAA,KAAA,YAAA,CAAA,QAAA,EAAA;YACQ,EAAR,KAAA,CAAA,QAAA,GA5PU,IA4PV,CAAA,YA5P4C,CA4P5C,QAAA,CAAA,CAAA;SACA;QACA,IAAA,CAAA,YAAA,CAAA,eAAA,CAAA,MAAA,EAAA,YAAA,CAAA,MAAA,CAAA,EAAA;YACQ,EAAR,KAAA,CAAA,MA5PsB,GA4PtB,IAAA,CAAA,YA5PuC,CA4PvC,MAAA,CAAA,CAAA;SACA;QACA,IAAA,CAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,EAAA,YAAA,CAAA,GAAA,CAAA,EAAA;YACA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA;SA5PS;QA6PL,IAAJ,CAAS,YA5PY,CA4PrB,eAAA,CAAA,IA5P4B,EA4P5B,YAAA,CAAA,IAAA,CAAA,EAAA;;SAGA;KACG;SACH;;;;;;;;;;;AAaA,SAAA,yBAAA,CAAA,CAAA,EAAA,CAAA,EAAA;;IDjpBA,qBAAA,eAAA,GAAA,CAAA,CAAA,CAAA,MAAA,KAAA,CAAA,CAAA,CAAA,MAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,SAAA,UAAA,CAAA,kBAAA,EAAA,IAAA,EAAA,SAAA,EAAA;;;QAIA,qBAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA;QAXA,KAAS,CAAT,eAAA,GAAgC,IAAhC,CAAA,KAAA,CAAA;QAYI,qBAXM,QAWV,GAAA,qBAAA,CAAA,kBAV+E,EAU/E,IAVqF,EAUrF,SAAA,CAAA,CAAA;QAEI,OAAJ,IAAA,QAAA,CAAA,KAAA,EAAA,QAAuC,CAXC,CAWxC;;KAGG;SAXM,IAAT,kBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA;QAYI,qBAXM,MAWV,GAAA,EAAA,kBAAA,CAXkB,QAWlB,CAAA,IAXmD,CAWnD,KAAA,CAAA,GAAA,KAAA,CAAA;QACI,mCAAJ,CAAA,IAAA,EAAA,MAAA,CAX2B,CAAQ;QAY/B,OAXO,MAAI,CAWf;KACG;SACH;;;;;;;;;;;AAYA,SAAA,mCAAA,CAAA,IAAA,EAAA,MAAA,EAAA;IACA,IAAA,IAAA,CAAA,KAAA,CAAA,WAAA,KAAA,MAAA,CAAA,KAAA,CAAA,WAAA,EAAA;QACA,MAAA,IAAA,KAAA,CAAA,uEAAA,CAAA,CAAA;KACA;IACA,IAAI,IAAJ,CAAA,QAAA,CAAA,MAAA,KAAA,MAAA,CAAA,QAfyC,CAAI,MAe7C,EAf8C;QAgB9C,MAAA,IAAA,KAAA,CAAA,4EAAA,CAAA,CAAA;KACA;;;;;;;;;;;;AAcA,SAAA,qBAAA,CAAA,kBAAA,EAAA,IAAA,EAAA,SAAA,EAAA;IACA,OAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;QACI,KAAJ,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,SAAA,CApB0C,QAoB1C,EAAA,EAAA,GAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,CAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,kBAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,KAAA,CAAA,QAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA;gBACA,OAAA,UAAA,CAAA,kBAAA,EAAA,KAAA,EAAA,CAAA,CAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;ADjEA,SAAA,aAAA,CAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,QAAA,EAAA;IACA,IAAI,QAAJ,CAAA,MAAA,KARwB,CAQxB,EAAA;QACA,OAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,OAAA,CAAA,IAAA,EAAA,OAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA;KAEA;IAEE,qBARM,GAQR,GAAA,iBAAA,CAAA,QARwC,CAQxC,CAAA;IACA,IAAM,GAAN,CAAA,MAAA,EAAA,EAAA;QAEM,OAAN,IAAA,CAAA,OARyB,CAQzB,IAAA,EAAA,IAAA,eAAA,CAAA,EAR2C,EAAa,EAQxD,CAAA,EAAA,OAAA,EAAA,WAAA,EARgF,QAQhF,CARqF,CAAQ;KAS7F;IACA,qBAAA,gBAAA,GAAA,oBAAA,CAAA,GAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;;;;AAkBA,SAAA,IAAA,CAAA,eAAA,EAAA,eAAA,EAAA,OAAA,EAAA,WAAA,EAAA,QAAA,EAAA;IACA,qBAAA,EAAA,GAAA,EAAA,CAAA;IAEE,IAAI,WAAN,EAAA;QACI,OAlBO,CAkBX,WAlBuB,EAkBvB,UAAA,KAAA,EAAA,IAAA,EAAA;YACA,EAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,EAAA,GAAA,CAAA,CAAA,EAAA,CAAA,GAAA,EAAA,GAAA,KAAA,CAAA;SAEA,CAAA,CAlBS;KAmBT;;;;;;;;;;;;AAcA,SAAA,cAAA,CAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA;IAvBA,qBAAA,QAAA,GAAA,EAAA,CAAA;IAwBA,OAAA,CAAM,OAAN,CAAc,QAAd,EAAA,UAvBgB,CAAU,EAuB1B,UAAA,EAAA;QACA,IAAA,CAAA,KAAA,UAAA,EAAA;YACA,QAAA,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;SACA;aACA;YACA,QAAA,CAAA,UAAA,CAAA,GAAA,cAAA,CAAA,CAAA,EAAA,UAAA,EAAA,UAAA,CAAA,CAAA;;;;;;;;;;;IAWA,SAAA,UAAA,CAAA,UAAA,EAAsB,kBAAtB,EAAA,QAAA,EAAA;QACA,IAAA,CAAA,UAAA,GAAA,UAAA,CAAA;QAEI,IAAJ,CAAA,kBAAA,GAAA,kBAAA,CAAA;QACI,IAAI,CAAR,QAAA,GAAA,QAAA,CAAA;QACA,IAAM,UAAU,IAAhB,QAAA,CAAA,MAAA,GAAA,CAAA,IAAA,cAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAA,4CAAA,CAAA,CAAA;SACA;;;;SA3BA;KAgCA;;;;;;;;;;;;;;AAwBA,SAAA,iBAAA,CAAA,QA5CM,EA4CN;IAEE,IAAF,CAAA,OAAA,QAAA,CAAA,CAAA,CA5CQ,KA4CR,QAAA,KAAA,QA5CuC,CA4CvC,MA5CgD,KA4ChD,CA5CgD,IA4ChD,QAAA,CAAA,CAAA,CAAA,KAAA,GAAA,EAAA;QACI,OAAJ,IA5Ce,UA4Cf,CAAA,IAAA,EAAA,CAAA,EA5CmC,QA4CnC,CAAA,CAAA;KACA;IACA,qBAAA,kBAAA,GA5Cc,CA4Cd,CAAA;IACA,qBAAA,UA5CqB,GAAQ,KA4C7B,CAAA;IACA,qBAAiB,GAAjB,GA5CuB,QA4CvB,CAAA,MAAA,CAAA,UAAA,GAAA,EAAA,GAAA,EAAA,MAAA,EAAA;QACA,IAAA,OA5CW,GA4CX,KAAA,QAAA,IAAA,GAAA,IAAA,IAAA,EAAA;YACA,IAAQ,GAAR,CAAA,OA5CiB,EA4CjB;gBACA,qBAAA,SAAA,GAAA,EAAA,CAAA;gBAEU,OAAV,CAAA,GAAA,CAAA,OAAA,EAAA,UAAA,QAAA,EAAA,IAAA,EAAA;oBACA,SA5CiB,CA4CjB,IA5CwB,CA4CxB,GA5C4B,OA4C5B,QAAA,KAAA,QAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,QAAA,CAAA;iBACA,CAAA,CAAA;gBACA,OAAA,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,CAAA,CAAA;aAEA;YACM,IAAN,GA5Ca,CAAA,WAAS,EAAK;gBA6C3B,OAAA,GAAA,CAAA,MAAA,CAAA,CAAA,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA;aAEA;SACA;QACA,IAAA,EAAA,OAAA,GAAA,KA5Ca,QA4Cb,CA5C8B,EA4C9B;;SAEA;QA5CA,IAAA,MAAA,KAAA,CAAA,EAAA;YA6CA,GAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA,SAAA,EAAA;gBACA,IAAA,SAAA,IAAA,CAAA,IAAA,OAAA,KAAA,GAAA,EAAA;;iBACA;qBACA,IAAA,SAAA,IAAA,CAAA,IAAA,OAAA,KAAA,EAAA,EAAA;oBA5CA,UAAA,GAAA,IAAA,CAA8B;iBA6C9B;qBACA,IAAA,OAAA,KAAA,IAAA,EAAA;oBACA,kBAAA,EAAA,CAAA;iBAEA;qBACA,IAAA,OAAA,IAAA,EAAA,EAAA;oBAEA,GAAA,CA5CoB,IAAI,CAAC,OA4CzB,CAAA,CAAA;iBACA;aAEA,CAAA,CA5Ca;YA6Cb,OAAA,GAAA,CAAA;SACA;;;;;;;;;;;IAWA,SAAA,QAAA,CAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA;QAEA,IAAA,CAAA,YAAA,GAAA,YAAA,CAAA;;;;;;;;;;;;AAqBA,SAAA,oBAAA,CAAA,GAjEyB,EAiEzB,IAAA,EAAA,KAAA,EAAA;IACA,IAAA,GAAA,CAAA,UAAA,EAAA;QAEA,OAAA,IAAA,QAjEQ,CAiER,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;KACA;IACE,IAAF,KAAA,CAAA,QAAA,CAAA,cAAA,KAAA,CAjES,CAkEH,EADN;QAEA,OAAA,IAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;AAaA,SAAA,gCAAA,CAAA,KAAA,EAAA,KAAA,EAAA,kBAAA,EAAA;IACA,qBAtEmB,CAsEnB,GAAA,KAAA,CAAA;IACA,qBAAA,EAAA,GAAA,KAAA,CAAA;IACA,qBAAA,EAtEgB,GAsEhB,kBAAA,CAAA;IACA,OAAA,EAAA,GAAA,EAAA,EAAA;QACI,EAAE,IAtEG,EAsET,CAAA;QACA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA;QACA,IAAA,CAAA,CAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAA,2BAAA,CAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,OAAA,CAAA,OAAA,EAAA;;;;;CAKA;;;;;AAGA,SAAA,UAAA,CA5EkB,QA4ElB,EAAA;IACA,IAAA,EAAA,OAAA,QAAA,CAAA,CAAA,CAAA,KAAA,QAAA,CAAA;;;;;;;;;;;;;AAaA,SAAA,kBAAA,CAAA,YAAA,EAjFsC,UAiFtC,EAjFuC,QAiFvC,EAAA;IACA,IAAA,CAAA,YAAA,EAAA;QAEA,YAAA,GAAA,IAjFQ,eAiFR,CAjFY,EAiFZ,EAAA,EAAA,CAAA,CAAA;KACA;IACE,IAAI,YAjFW,CAAE,QAiFnB,CAjFoB,MAiFpB,KAAA,CAAA,IAjF4C,YAiF5C,CAAA,WAAA,EAAA,EAAA;QACI,OAAJ,0BAAA,CAAA,YAjFkB,EAiFlB,UAAA,EAjF+C,QAAC,CAAQ,CAiFxD;KACA;IACA,qBAAA,CAAA,GAjFY,YAiFZ,CAjF6B,YAiF7B,EAAA,UAAA,EAAA,QAAA,CAAA,CAAA;IACA,qBAAA,cAjFW,GAiFX,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;IACA,IAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,SAAA,GAAA,YAAA,CAAA,QAAA,CAAA,MAAA,EAAA;QAjFA,qBAAA,CAAA,GAAA,IAAuC,eAAvC,CAAA,YAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,SAAA,CAAA,EAAA,EAAA,CAAA,CAAA;QAkFI,CAAJ,CAAA,QAAA,CAjFe,cAiFf,CAjFe;YAkFf,IAAA,eAAA,CAAA,YAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,YAAA,CAAA,QAAA,CAAA,CAAA;QAjFA,OAAe,0BAAf,CAAA,CAAA,EAAA,CAAA,EAAA,cAAA,CAAA,CAAA;KAkFA;SACA,IAAA,CAAA,CAAA,KAAA,IAAA,cAAA,CAAA,MAAA,KAAA,CAAA,EAAA;QAjFA,OAAe,IAAf,eAAA,CAAA,YAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA;KAkFA;SACA,IAAA,CAAA,CAAA,KAAA,IAAA,CAAA,YAAA,CAAA,WAAA,EAAA,EAAA;QAjFA,OAAA,qBAAA,CAAA,YAAA,EAAA,UAAA,EAAA,QAAA,CAAA,CAAA;KAkFA;SACA,IAAA,CAAA,CAAA,KAAA,EAAA;QACA,OAAA,0BAAA,CAAA,YAAA,EAAA,CAAA,EAAA,cAAA,CAAA,CAAA;;;;;;;;;;;;AAYA,SAAA,0BAAA,CAAA,YAAA,EAAA,UAtFgC,EAAS,QAsFzC,EAAA;IACA,IAAI,QAAJ,CAAA,MAAA,KAAA,CAAA,EAtFU;QAwFN,OAAO,IAAX,eAtFgC,CAsFhC,YAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA;KACA;SACA;QACA,qBAAA,SAAA,GAAA,UAAA,CAAA,QAAA,CAAA,CAAA;QACA,qBAAA,UAAA,GAAA,EAAA,CAAA;QAEI,OAAO,CAtFC,SAsFZ,EAAA,UAAA,QAAA,EAAA,MAAA,EAAA;YACM,IAAI,QAtFQ,KAsFlB,IAAA,EAtFmB;gBAuFX,UAAQ,CAtFC,MAsFjB,CAAA,GAAA,kBAAA,CAAA,YAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA,UAAA,EAAA,QAAA,CAAA,CAAA;aACO;SACF,CAtFC,CAAC;QAuFH,OAtFO,CAsFX,YAAA,CAAA,QAAA,EAAA,UAAA,KAAA,EAtFgC,WAAsB,EAsFtD;YACA,IAAA,SAAA,CAAA,WAAA,CAAA,KAAA,SAAA,EAAA;gBACA,UAAA,CAAA,WAAA,CAAA,GAAA,KAAA,CAAA;;;;;;;;;;;;AAaA,SAAA,YAAA,CAAA,YAAA,EAAA,UAAA,EAAA,QAAA,EAAA;IA3FA,qBAAA,mBAAA,GAAA,CAAA,CAAA;IA4FA,qBAAA,gBAAA,GAAA,UAAA,CAAA;IACA,qBAAA,OAAA,GAAA,EAAA,KAAA,EA3FyB,KA2FzB,EAAA,SAAA,EAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,CAAA;IACA,OAAA,gBAAA,GAAA,YAAA,CAAA,QAAA,CAAA,MAAA,EA1F8B;QA6F1B,IAAI,mBA3FmB,IA2F3B,QAAA,CAAA,MAAA;YA3FoD,OAApD,OAAA,CAAA;QA6FI,qBAAJ,IAAA,GAAA,YAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,CAAA;QACA,qBA3FyB,IAAA,GA2FzB,OAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;QA3FA,qBAAqD,IAArD,GAAA,mBAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,GAAA,QAAA,CAAA,mBAAA,GAAA,CAAA,CAAA,GAAA,IAAA,CAAA;QA4FA,IAAM,gBAAN,GAAyB,CAAzB,IA3F6B,IA2F7B,KAAA,SAAA;YACA,MAAA;QA3FA,IAAA,IAAA,IAAA,IAAA,KAAA,OAAA,IAAA,KAAA,QAAA,CAAA,IAAA,IAAA,CAAA,OAAA,KAAA,SAAA,EAAA;YA4FM,IAAI,CA3FC,OAAC,CAAO,IAAC,EAAK,IAAI,EA2F7B,IAAA,CAAA;gBA3FoC,OAAO,OAAA,CAAQ;YA4F7C,mBAAmB,IAAzB,CAAA,CAAA;SACK;aACL;YACA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,EAAA,EAAA,IAAA,CAAA;gBAEA,OAAA,OAAA,CAAA;YACA,mBAAA,EAAA,CAAA;;;;;;;;;;;;AAcA,SAAA,qBAAA,CAAA,YAhGY,EAgGZ,UAAA,EAAA,QAAA,EAAA;IACA,qBAAA,KAAA,GAAA,YAAA,CAAA,QAhGkD,CAgGlD,KAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA;IACA,qBAAA,CAAA,GAAA,CAAA,CAAA;;QAGI,IAAI,OAhGM,QAgGd,CAAA,CAAA,CAAA,KAAA,QAAA,IAhG2C,QAgG3C,CAAA,CAAA,CAAA,CAAA,OAAA,KAAA,SAAA,EAAA;YACM,qBAhGM,QAgGZ,GAAA,wBAAA,CAhGuC,QAgGvC,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;YACM,OAAN,IAhGiB,eAAe,CAAC,KAAE,EAAK,QAAA,CAAS,CAAC;SAiGlD;;QAEA,IAAA,CAAA,KAAA,CAAA,IAAA,cAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA;YAEA,qBAhGU,CAgGV,GAAA,YAAA,CAAA,QAAA,CAAA,UAAA,CAAA,CAAA;YACA,KAAA,CAAA,IAAA,CAAA,IAAA,UAAA,CAhGiB,CAAA,CAAE,IAgGnB,EAAA,QAAA,CAAA,CAAA,CAAA,CAhGgC,CAgGhC,CAAA;YACQ,CAAR,EAAA,CAhGS;YAiGH,SAAN;SACA;QACA,qBAAA,IAAA,GAAA,OAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAhGA,qBAAA,IAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,IAAA,QAAA,CAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAA,CAAA;QAiGA,IAAM,IAAN,IAAA,IAAA,IAAA,cAhGiC,CAgGjC,IAhGyC,CAAC,EAgG1C;YACM,KAAN,CAAA,IAAA,CAAA,IAAA,UAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,IAAA,CAAA,CAAA;SACA;aACA;YACA,KAAA,CAAA,IAAA,CAAA,IAAA,UAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,wBAAA,CAAA,OAAA,EAAA;IACA,qBAAA,QAAA,GAAA,EAAA,CAAA;IACE,OAnGO,CAmGT,OAnGS,EAmGT,UAAA,QAAA,EAAA,MAAA,EAAA;QACA,IAAA,QAAA,KAAA,IAAA,EAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ID1XA,SAAA,UAAA,CAAA,iBAAA,EAAA,MAAA,EAAA,OAAA,EAAA,GAAA,EAAA;;;;QAiBA,IAAA,CAfG,GAeH,GAAA,GAAA,CAAA;KACA;;;;IASA,UAAA,CAAA,SAAA,CAAA,SAAA,GAAA,YAAA;QACA,IAAM;YACA,qBAAN,gBAAA,GAf4C,OAe5C,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,YAAA,CAAA;YACM,qBAAN,QAAA,GAAA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,CAAA;YAEA,qBAAA,IAAA,GAAA,IAAA,sBAAA,CAAA,EAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,CAAA,EAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAAA,qBAAA,IAAA,CAAA,OAAA,CAAA,QAAA,IAAA,EAAA,EAAA,cAAA,EAAA,IAAA,CAAA,iBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA;YAfA,qBAAA,QAAA,GAAA,IAAA,QAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;YAgBM,qBACK,UADX,GAAA,IAduD,mBAcvD,CAAA,IAAA,CAAA,GAAA,EAAA,QAAA,CAAA,CAAA;YAEA,IAAA,CAAA,oBAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;YACA,OAAAnC,qBAAA,CAAA,UAAA,CAAA,CAAA;;;;;KAKA,CAAA;;;;;IAOA,UAAA,CAAA,SAAA,CAAA,oBAAA,GAAA,UAAA,SAlBwB,EAkBxB;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA;;;;;;;;;;;;IAcA,UAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,MAAA,EAAA,YAAA,EAAA,MAAA,EAAA;;;;;;;;;;;IAaA,UAAA,CAAA,SAAA,CAAA,eAAA,GAAA,UAAA,MAAA,EAAA,YAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;IAnBA,UAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAQ,MAAR,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA;QAkCA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,QAAA,GAAA,MAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;YAAA,IAAA,CAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;YAjCA,IAAA;gBAkCA,OAAA,IAAA,CAAA,0BAAA,CAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,CAAA,CAAA;aACA;YACQ,OAAR,CAAA,EAAA;gBACA,IAAA,EAjCgB,CAiChB,YAAA,SAAA,CAAA;oBACA,MAAA,CAAA,CAAA;aAEA;SACA;;;;;;;;;;;;;;;;;;;;;;IAyBA,UAAA,CAAA,SAAA,CAAA,0BAAA,GAAA,UA5CuB,KA4CvB,EAAA,UAAA,EAAA,QAAA,EAAA,MAAA,EAAA;QACA,IAAM,KAAN,CAAA,UAAA;YACM,MAAN,IAAA,SAAA,EAAA,CAAA;QAIA,IAAM,CAAN,KAAA,CA5Ca,MA4Cb,IAAA,cA5C6D,MA4C7D,MAAA;YACA,MAAA,IAAA,SAAA,EAAA,CAAA;QAEI,IAAJ,KAAA,CAAA,IAAA,KAAA,IAAA,EAAA;YACA,qBAAA,MAAA,GAAA,QAAA,CAAA,MA5C8B,GA4C9B,CAAA,GA5C6C,EA4C7C,MAAA,CA5C8C,QA4C9C,CAAA,GAAA,UAAA,GAAA,EAAA,CAAA;YACA,qBAAA,UAAA,GAAA,IAAA,sBAAA,CAAA,QAAA,EAAA,MAAA,EAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAAA,qBAAA,IAAA,CAAA,OAAA,CAAA,QAAA,IAAA,OAAA,CAAA,KAAA,CAAA,EAAA,MAAA,qBAAA,KAAA,CAAA,SAAA,IAAA,KAAA,EAAA,qBAAA,CAAA,UAAA,CAAA,EAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YAEA,OAAA,CAAA,IAAA,QAAA,CAAA,UAAA,EAAA,EAAA,CA5CwC,CA4CxC,CAAA;SAGA;QAOA,IAAA,EAAA,GAAA,OAAA,CAAA,UAAA,EAAA,KAAA,EAAA,QAAA,CAAA,EAAA,gBAAA,GAAA,EAAA,CAAA,gBAAA,EAAA,UAAA,GAAA,EAAA,CAAA,UAAA,EAAA,SAAA,GAAA,EAAA,CAAA,SA5CuC,CA4CvC;QACA,qBAAA,iBAAA,GA5C4B,QA4C5B,CAAA,KAAA,CA5C6B,SA4C7B,CAAA,CAAA;QACA,qBAAA,WAAA,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA;QACA,IAAA,EAAA,GAAA,OAAA,CAAA,UAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,EAAA,YAAA,GAAA,EAAA,CAAA,YAAA,EAAA,cAAA,GAAA,EAAA,CAAA,cAAA,CAAA;QAEI,qBAAJ,QAAA,GAAA,IAAA,sBAAA,CA5CiE,gBA4CjE,EAAA,UAAA,EAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAAA,qBAAA,IAAA,CAAA,OAAA,CAAA,QAAA,IAAA,OAAA,CAAA,KAAA,CAAA,EAAA,MAAA,qBAAA,KAAA,CAAA,SAAA,IAAA,KAAA,EAAA,qBAAA,CAAA,UAAA,CAAA,EAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,gBAAA,CAAA,MAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;QACA,IAAM,cAAN,CAAA,MA5CkD,KA4ClD,CAAA,IA5C6D,YA4C7D,CAAA,WAAA,EAAA,EAAA;YACA,qBAAA,UAAA,GAAA,IAAA,CAAA,eAAA,CAAA,WAAA,EAAA,YAAA,CAAA,CAAA;YAEA,OAAA,CAAA,IAAA,QAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;SACA;QACA,IAAA,WAAA,CAAA,MAAA,KAAA,CAAA,IAAA,cAAA,CAAA,MAAA,KAAA,CAAA,EAAA;YACA,OAAA,CAAA,IAAA,QAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA;SAEA;;;;IAeA,OAAA,UAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;AAxDA,SAAA,2BAAA,CAAA,KAAA,EAAA;IA4DA,KAAA,CAAA,IAAA,CA3DW,UAAA,CAAE,EA2Db,CAAA,EAAA;QACA,IAAA,CAAA,CAAA,KAAA,CAAA,MAAA,KAAA,cAAA;YACA,OAAA,CAAA,CAAA,CAAA;;;;;CAKA;;;;;AAMA,SAAA,cAAA,CAAA,KAAA,EAAA;IACA,IAAA,KAAA,CAAA,QAAA,EAAA;QAEA,OAAA,KAAA,CAAA,QAAA,CAAA;KACA;;;;;;;;;;;;AAaA,SAAA,OAAA,CAnEW,YAmEX,EAAA,KAAA,EAAA,QAAA,EAAA;IACA,IAAA,KAAA,CAAA,IAAA,KAAA,EAAA,EAAA;QAEA,IAAA,KAAA,CAAA,SAnEQ,KAmER,MAAA,KAnEwB,YAmExB,CAAA,WAAA,EAAA,IAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA;YACA,MAAA,IAAA,SAAA,EAnEc,CAmEd;SAlEO;QAAK,OAAZ,EAAA,gBAAA,EAAA,EAAA,EAAA,SAAA,EAAA,CAAA,EAAA,UAAA,EAAA,EAAA,EAAA,CAAA;KAqEA;IACE,qBAAF,OAnEW,GAmEX,KAAA,CAAA,OAnE+B,IAmE/B,iBAnEsE,CAAC;IAoErE,qBAnEM,GAmER,GAAA,OAAA,CAAA,QAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAnEwC;IAqEtC,IAAF,CAAA,GAAA;QACA,MAAA,IAAA,SAAA,EAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,yBAAA,CAtEY,KAsEZ,EAAA;IACA,qBAAA,KAAA,GAtEY,EAsEZ,CAAA;IACA,KAAA,CAAA,OAAA,CAtEY,UAAA,CAsEZ,EAAA;QACA,qBAAA,uBAAA,GAAA,KAAA,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;QACI,IAAJ,uBAAA,EAAA;YACA,qBAAA,CAAA,GAAA,uBAAA,CAAA,GAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;YACA,qBAAA,CAAA,GAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,qBAAA,CAAA,YAAA,EAAA;IACA,qBAAA,CAAA,GAAA,YAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,iBAAA,CAAA,YA5EmC,EA4EnC;IACA,qBAAA,CAAA,GAAA,YAAA,CAAA;IACE,qBAAF,GAAA,IAAA,CAAA,CAAA,kBAAA,GAAA,CAAA,CAAA,kBAAA,GAAA,CAAA,CAAA,CAAA;IACA,OAAA,CAAA,CAAA,cAAA,EAAA;;;;;;;;;;;;;AAkBA,SAAA,OAAA,CAAA,YAAA,EAlF2B,gBAAA,EAkF3B,cAAA,EAAA,MAAA,EAAA;IACA,IAAI,cAAJ,CAAA,MAlFa,GAAa,CAkF1B;QACA,wCAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,CAAA,EAAA;QAEM,qBAAN,GAAA,GAlFgC,IAkFhC,eAAA,CAAA,gBAAA,EAAA,2BAAA,CAAA,YAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,IAAA,eAAA,CAAA,cAAA,EAAA,YAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;QACM,GAAN,CAAA,cAAA,GAAA,YAAA,CAAA;QACI,GAAJ,CAAA,kBAAA,GAlFU,gBAkFV,CAAA,MAlFkB,CAmFV;QAEJ,OAAJ,EAAA,YAAA,EAAA,GAAA,EAAA,cAAA,EAAA,EAAA,EAAA,CAAA;KACA;IACA,IAAI,cAAJ,CAAA,MAlFa,KAAgB,CAkF7B;QACA,wBAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,CAAA,EAAA;QAEA,qBAlFY,GAkFZ,GAlFgB,IAkFhB,eAAA,CAAA,YAAA,CAAA,QAAA,EAAA,+BAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,YAAA,CAAA,QAAA,CAAA,CAAA,CAAA;QACA,GAAA,CAAA,cAAA,GAAA,YAAA,CAAA;QACA,GAAA,CAAA,kBAAA,GAAA,gBAAA,CAAA,MAAA,CAAA;QACA,OAAA,EAAA,YAAA,EAAA,GAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA;KACA;;;;;;;;;;;;;AAeA,SAAA,+BAAA,CAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAxF6B,CAwF7B;IACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,QAAA,GAAA,MAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;QAAA,IAAA,CAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,cAAA,CAAA,YAAA,EAAA,cAAA,EAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;YACA,qBAAA,CAAA,GAAA,IAAA,eAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA;YACA,CAAA,CAAA,cAAA,GAAA,YAAA,CAxF+B;YAyF/B,CAAA,CAAA,kBAAA,GAAA,YAAA,CAAA,QAAA,CAAA,MAAA,CAAA;;;;;;;;;;;;;AAgBA,SAAA,2BAAA,CA9FkB,YA8FlB,EAAA,gBAAA,EAAA,MAAA,EAAA,cAAA,EAAA;IACA,qBAAA,GAAA,GAAA,EAAA,CAAA;IACA,GAAA,CAAA,cAAA,CAAA,GAAA,cAAA,CAAA;IACA,cAAA,CAAA,cAAA,GAAA,YAAA,CAAA;IACA,cAAA,CAAA,kBAAA,GAAA,gBAAA,CAAA,MAAA,CAAA;IACA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,QAAA,GAAA,MAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;QAAA,IAAA,CAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;QACA,IAAA,CAAA,CAAA,IAAA,KAAA,EAAA,IAAA,WAAA,CAAA,CAAA,CAAA,KAAA,cAAA,EAAA;YACA,qBAAA,CAAA,GAAA,IAAA,eAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA;YACA,CAAA,CA9FS,cA8FT,GAAA,YAAA,CAAA;YACA,CAAA,CAAA,kBAAA,GAAA,gBAAA,CAAA,MAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,SAAA,cAAA,CAAA,YAAA,EAAA,cAAA,EAAA,CAAA,EAAA;;;;;CAKA;;;;;;;CAOA;;;;;;;CAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ID5VA,kBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,MAAA,EAAA,IAAA,EAAA,GAAA,CAAA;;;;;;AAKA,IAAA,yBAAA,IAAA,YAAA;IAAA,SAAA,yBAAA,GAAA;;;;;;;;;;;;IAWA,yBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,KAAA,EAlCG,YAkCH,EAAA,GAAA,CAAA;;;;;IAKA,yBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,KAtCsE,EAsCtE,EAAA,OAtC6E,KAsC7E,CAAA,EAAA,CAAA;;;;;;;;;;;IDtGA,yBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,MAAA,EAAA,IAAA,EAAA;;;;CCsFA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;ID3DA,SAAA,kBAAA,CAAA,MAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA;;;;;;;;;;;IAmBA,kBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAA,cAVgB,EAUhB,KAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CACA;QAAA,IAAM,IAAI,CAAV,mBAAA,EAVgB;YAWhB,IAAQ,CAAR,mBAAA,CAAA,KAAA,CAAA,CAVgC;SAWhC;QAEA,qBAAA,cAAA,GAAA,IAV6B,CAU7B,iBAAA,oBAAA,KAAA,CAAA,YAAA,GAAA,CAAA;QAEA,OAAA4B,qBAAA,CAVa,IAAI,CAUjB,cAAA,EAAA,UAViB,OAUjB,EAAA;YACA,IAAA,KAAA,CAAA,iBAAA,EAAA;gBACA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,CAAA;;;;;KARA,CAAA;;;;;IAiBA,kBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,YAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAA,OAAA,YAAA,KAAA,QAAA,EAAA;YACA,OAAAK,uCAboB,CAAE,IAatB,CAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA;SACA;aAbA;YAcA,OAAAjB,+BAbiB,CAajB,IAAA,CAAA,kBAAA,CAbmC,YAanC,EAAA,CAAA,EAAA,UAAA,CAb4C,EAa5C;gBACA,IAAA,CAAA,YAAAkB,6BAAA,EAAA;oBACA,OAAAlC,qBAAA,CAAA,CAAA,CAAA,CAAA;iBACA;qBACA;oBACA,OAAAiC,uCAAA,CAAA,KAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;aDzEA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDA,mBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,UAAA,UAAA,EAAA,MAAA,EAAA,GAAA,CAAA;;;;;;AAKA,IAAA,0BAAA,IAAA,YAAA;IAAA,SAAA,0BAAA,GAAA;;;;;;IAKA,0BAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,GAAA,EAAA,EAAA,OAAA,IAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;CALA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDuMA,SAAA,MAAA,CAAA,iBAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QA/CU,IAAV,CAAA,iBAAA,GAAA,iBAAA,CAAA;QACU,IAAV,CAAA,aAAA,GAAA,aAA8C,CAA9C;QAIU,IAAV,CAAA,YAAU,GAAuB,YAAjC,CAAA;;;;;;;;;;;;;;;;;;;;;;;YA2BA,kBAAA,EAIG,iBAJH;SAEA,CAAA;;;;QAuBI,IAAI,CAAC,mBAAmB,GAA5B,IAAA,0BAAA,EAAA,CAAA;QACI,IAAI,CAAC,kBAAT,GAAA,IAAA,yBAAA,EAAA,CAAA;QACI,IAAJ,WAAA,GAAA,UAAA,CAA0B,EAA1B,EAAA,OAAA,KAAA,CAAA,YAAA,CAAA,IAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QAEI,IAAJ,SAAA,GAAA,UAAA,CAAwB,EAAxB,EAAA,OAAA,KAAA,CAAA,YAA8C,CAAC,IAA/C,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,IAAI,CAAC,QAAT,GAAA,QAAA,CAAA,GAAA,CAAAD,yBAAA,CAAA,CAA8C;QAC1C,IAAI,CAAC,WAAT,CAAA,MAA2B,CAA3B,CAA6B;QAC7B,IAAA,CAAA,cAAA,GAAA,kBAAA,EAAA,CAAA;;;;;;;;;;;;IAYA,MAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,iBAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,YAAA;;;;;KAKA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,2BAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAUA;;;QAPA,IAAA,CAAO,IALG,CAKV,oBAAA,EAAA;YACA,IAAA,CAAA,oBAAA,IAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,UAAA,MAAA,EAAA;gBACA,qBAAA,UAAA,GAAA,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;;;;KAKA,CAAA;IAKA,MAAA,CAAA,cAAA,CATG,MASH,CAAA,SAAA,EAAA,aAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,kBAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CAXG,MAWH,CAAA,SAAA,EAAA,KAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAX8D,cAW9D,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,IAAA,CAAA,OAAA,EAAA,CAAA,EAAA,CAAA;;;;;IAKA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmDA,MAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,QAAA,EAAA,gBArB4B,EAqB5B;QAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,gBAAA,GAAA,EArB4B,CAqB5B,EAAA;QACA,IAAA,UAAA,GAAA,gBAAA,CAAA,UAAA,EAAA,WAAA,GAAA,gBAAA,CAAA,WAAA,EAAA,QAAA,GAAA,gBAAA,CAAA,QAAA,EArBc,mBAqBd,GAAA,gBAAA,CAAA,mBAAA,EAAA,mBAAA,GAAA,gBAAA,CAAA,mBAAA,EArBgE,gBAqBhE,GAAA,gBAAA,CAAA,gBAAA,CAAA;QACI,IAAJD,uBAAA,EAAA,IAAA,mBAAA,KAAA,OAAA,CAAA,KAAA,OAAA,CAAA,IAAA,CAAA,EAAA;YACQ,OAAR,CAAA,IAAA,CAAA,qEAAA,CAAA,CAAA;SACA;QACA,qBAAA,CAAA,GAAA,UAAA,IAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;QACA,qBAAA,CAAA,GAAA,gBAAA,GAAA,IAAA,CAAA,cArBuC,CAqBvC,QAAA,GAAA,QAAA,CAAA;QACA,qBAAA,CAAA,GAAA,IAAA,CAAA;QACA,IAAA,mBArBa,EAqBb;YACA,QAAU,mBAAV;gBACA,KAAA,OAAA;oBACA,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAA,CAAA,cAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;oBACU,MAAV;gBACA,KAAA,UAAA;oBACA,CAAA,GAAA,IAAA,CAAA,cAAA,CAAA,WAAA,CAAA;oBArBA,MAAA;gBACU;oBAsBV,CAAA,GAAA,WAAA,IAAA,IAAA,CAAA;aACA;SACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6DA,MAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,QAAA,EAAA,MAzBkD,EAyBlD;QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAA,GAAA,EAzB8B,kBAAM,EAyBpC,KAAA,EAzBkD,CAyBlD,EAAA;QACA,gBAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;IAyBA,MAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAA;QACA,IAAA,GAAA,YAAA,OAAA,EAAA;;;;;KAjCA,CAAA;;;;;IA2CA,MAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,MAAA,EAAA;QACA,OAAA,MAAA,CAAA,IAtCoB,CAsCpB,MAAA,CAAA,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA,GAAA,EAAA;YACA,qBAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,CAAA;YACA,IAAA,KAAA,KAAA,IAAA,IAAA,KAAA,KAAA,SAAA,EAAA;;;;SApCA,EAAA,EAAA,CAAA,CAAA;KAyCA,CAAA;;;;;;;aAQA,IAAA,CAAA,IAAA,CAxCuB,WAAK,EAwC5B,UAAA,GAxCoC,EAwCpC;YACA,IAAA,GAAA,EAAA;gBAxCA,KAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA;;;gBA2CA,OAAA,GAAA,CAAA,OAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA;aACS;iBACT;;;;;;;;;;;;IAcA,MAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA;QACA,qBAAA,cAAA,GAAA,IAAA,CAAA,WA7CqD,CA6CrD,KAAA,CAAA;;;;;;YAOQ,OAAR,OA7CS,CA6CT,OAAA,CAAA,IAAA,CA7CoC,CA6CpC;SACA;;;;QAKI,IAAJ,cAAA,IAAA,MAAA,IAAA,YAAA,IAAA,cAAA,CAAA,MAAA,KAAA,UAAA;YAEA,cAAA,CAAA,MAAA,CAAA,QAAA,EAAA,KAAA,MAAA,CAAA,QAAA,EAAA,EAAA;YACM,OAAO,OA7CO,CA6CpB,OAAA,CAAA,IAAA,CAAA,CAAA;SACA;QACA,qBAAA,OAAA,GAAA,IAAA,CAAA;QAEI,qBAAJ,MAAA,GA7Ce,IAAG,CAAI;QA8ClB,qBAAJ,OAAA,GAAA,IAAA,OA7C+C,CA6C/C,UAAA,GAAA,EAAA,GAAA,EAAA;;;SAIA,CAAA,CAAA;QACA,qBAAA,EAAA,GAAA,EAAA,IAAA,CAAA,YAAA,CAAA;;;;;KA3CA,CAAA;;;;;IAuDA,MAAA,CAAA,SAAA,CAAA,0BAAA,GAAA,UAAA,EAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAAA,IAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA,MAAA,GAAA,EAAA,CAAA,MAAA,EAAA,MAAA,GAAA,EAAA,CAAA,MAAA,EAAA,OAAA,GAAA,EAAA,CAAA,OAAA,EAAA,MAAA,GAAA,EAAA,CAAA,MAAA,CAAA;QACA,qBAAA,GA/C0B,GA+C1B,IAAA,CAAA,mBA9CiC,CA8CjC,OAAA,CAAA,MAAA,CAAA,CAAA;QAGA,qBAhDyB,aAgDzB,GAAA,CAAA,IAAA,CAAA,SAAA,IAAA,GAAA,CAAA,QAAA,EAAA,KAAA,IAAA,CAAA,cAAA,CAAA,QAAA,EAAA,CAAA;;;YAIA,OAAA,CAAA,OAAA,EAAA;iBACQ,IAjDR,CAAA,UAAA,CAAA,EAiDA,EAjDA,OAAA,KAAA,CACyB,WADzB,CAAA,GAC+B,EAD/B,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,kBAAA,EAAA,CAAA,CAAA,MAAA,CAAA,UAAA,EAAA,EAAA,EAAA,IAAA,CAAA,CAiDA,EAjDA,CAAA;iBAEa,IAgDb,CAAA,OAAA,EAAA,MAhDiC,CAgDjC,CAAA;;;SAGA;aAIA,IAAW,aAAX,IAAA,IAhDgC,CAAC,UAgDjC;YAEA,IAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA;YAhDA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,eAAA,CAAA,EAAA,EAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YAiDM,OAAN,CAAA,OAhDY,EAgDZ;iBACA,IAAA,CAAA,UAAA,CAhDe,EAgDf,EAAA,OAAA,KAAA,CAAA,WAAA,CAAA,GAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,EAAA,EAAA,EAAA,gBAAA,CAAA,GAAA,EAAA,KAAA,CAAA,iBAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA;iBACA,IAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;SACA;;;;;;;;;;;;;;;IAmBA,MAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,GAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,eAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CA4JA;QA1JI,IAAJ,EAAA,KAxDe,IAwDf,CAAA,YAAA,EAAA;;;YAGM,OAAN,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;SACA;QACA,OAAA,IAAA,OAAA,CAAA,UAAA,cAAA,EAAA,aAAA,EAAA;;;YAKA,qBAAA,eAAA,CAAA;YAKA,IAAA,CAAA,eAAA,EAAA;gBAGA,qBAAA,cAxDoC,GAAU,KAwD9C,CAAA,QAAA,CAAA,QAAA,CAAA;gBACA,qBAAA,iBAAA,GAAA,cAAA,CAAA,cAAA,EAAA,KAAA,CAAA,YAAA,EAAA,KAAA,CAAA,aAAA,EAAA,GAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA;gBACA,eAAA,GAAAf,+BAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,UAAA,UAAA,EAAA;oBACA,OAAAY,qBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,iBAAA,EAAA,KAAA,CAAA,MAAA,EAAA,UAAA,EAAA,KAAA,CAAA,YAAA,CAAA,UAAA,CAAA,CAAA,EAAA,UAAA,QAAA,EAAA;wBAxDA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,gBAAA,CAAA,EAAA,EAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,EAAA,KAAA,CAAA,YAAA,CAAA,UAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA;wBAyDA,OAAuB,EAAvB,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAxDgD;qBAyDhD,CAAA,CAAA;iBAEA,CAAA,CAAA;aAEA;iBACA;;aAGA;YAEM,qBAxDM,wBAwDZ,GAAAZ,+BAAA,CAAA,IAAA,CAAA,eAAA,EACkC,UADlC,CAEU,EAFV;gBAGY,OAAZY,qBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,YAAA,EAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA;aACA,CAAA,CAAA;;YAEA,qBAAA,aAAA,CAAA;YACA,qBAxDqB,sBAwDrB,GAAAA,qBAAA,CAAA,IAAA,CAAA,wBAAA,EAAA,UAAA,EAAA,EAAA;gBAAA,IAAA,UAAA,GAAA,EAAA,CAAA,UAAA,EAAA,QAAA,GAAA,EAAA,CAAA,QAAA,CAAA;gBACA,qBAAA,cAAA,GAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA;gBAEA,aAAA;oBAGgB,IAxDC,aAAK,CAwDtB,QAAA,EAAA,KAAA,CAAA,kBAAA,CAAA,QAAA,EAAA,cAAA,CAAA,CAAA;gBAxDA,aAAiD,CAAG,QAApD,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA;gBA0DY,OAxDO,EAwDnB,UAAA,EAAA,UAAA,EAAA,QAxD6B,EAwD7B,QAxD6B,EAwD7B,CAAA;aACA,CAAA,CAAA;YACA,qBAAA,yBAAA,GAAAZ,+BAAA,CAAA,IAAA,CAAA,sBAAA,EAAA,UAAA,EAAA,EAAA;gBAAA,IAAA,UAAA,GAAA,EAAA,CAAA,UAAA,EAAA,QAAA,GAAA,EAAA,CAAA,QAAA,CAAA;gBACA,IAAA,KAAA,CAAA,YAAA,KAAA,EAAA;oBAEA,OAAAhB,qBAAA,CAAA,KAxDY,CAwDZ,CAAA;gBACQ,OAAR4B,qBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,WAAA,EAAA,EAAA,UAAA,cAAA,EAAA;oBAxDsC,OAAO,EAAA,UAA7C,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA;iBA0DA,CAAA,CAAA;aACA,CAAA,CAAA;YACA,qBAAA,yBAAA,GAAAZ,+BAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,EAAA;gBAxDA,IAAA,KAAA,CAAA,YAAA,KAAA,EAAA;oBAyDU,OAxDOhB,qBAAA,CAAG,KAwDpB,CAAA,CAAA;gBACA,IAAA,CAAA,CAAA,cAAA,EAAA;oBACA,OAAA4B,qBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,WAAA,EAAA,EAAA,YAAA,EAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA;iBAEA;qBACA;oBACA,OAAA5B,qBAAA,CAAA,CAAA,CAAA,CAAA;;;YAKM,qBAxDM,kBAyDC,GADbgB,+BAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,EAAA;gBAEY,OAAZY,qBAAA,CAAA,IAAA,CAAA,KAxDgC,CAwDhC,KAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,YAAA,EAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA;aACA,CAAA,CAAA;;;YArDA,qBAAA,YAAA,GAAAA,qBAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,UAAA,EAAA,EAAA;gBAAA,IAAA,UAAA,GAAA,EAAA,CAAA,UAAA,EAAA,QAAA,GAAA,EAAA,CAAA,QAAA,EAAA,cAAA,GAAA,EAAA,CAAA,cAAA,CAAA;gBAyDA,IAAc,cAAd,EAAA;oBACA,qBAAA,KAAA,GAAA,iBAAA,CAAA,KAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,KAAA,CAAA,kBAAA,CAAA,CAAA;oBACA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,KAAA,EAAA,KAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA;;;oBAKA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA;iBACA;aACA,CAAA,CAAA;;;YAIA,qBAAA,sBAAA,CAAA;YACA,qBAAA,WAAA,GAAA,KAAA,CAAA,kBAAA,CAAA;YACA,qBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA;YACA,YAAA;iBAEA,OAAA,CAAA,UAAA,EAAA,EAAA;gBAAA,IAAA,UAAA,GAAA,EAAA,CAAA,UAxDkC,EAwDlC,KAAA,GAAA,EAAA,CAAA,KAAA,EAAA,cAAA,GAAA,EAAA,CAAA,cAAA,CAAA;gBACY,IAAI,CAxDC,cAwDjB,IAxDmC,EAwDnC,KAAA,KAAA,CAAA,YAAA,EAxD6D;oBA0D7C,sBAxDsB,GAwDtC,KAAA,CAAA;oBAEgB,OAAhB;iBACA;gBACA,KAAc,CAAd,cAAA,GAxDgC,UAwDhC,CAAA;gBACA,KAAA,CAAA,UAAA,GAAA,KAxD8B,CAwD9B,mBAAA,CAAA,KAAA,CAAA,KAAA,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;gBACA,KAAA,CAAe,kBAAf,GAAA,KAAA,CAAA;gBAxDA,IAAA,CAAA,sBAAA,EAAA;oBAyDA,qBAAA,IAxDuC,GAwDvC,KAAA,CAAA,aAAA,CAAA,SAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;oBACA,IAAA,KAAA,CAAA,QAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,IAAA,gBAAA,EAAA;wBACA,KAAA,CAAA,QAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA;qBAEA;yBACA;wBAEA,KAAA,CAAA,QAAA,CAAkC,EAAlC,CAxDqC,IAAA,CAAK,CAwD1C;qBACA;iBACW;gBAEK,IAAI,cAApB,CAAA,KAAA,CAAA,kBAAA,EAAA,KAAA,EAAA,WAAA,CAAA;qBACA,QAAA,CAAA,KAxDwB,CAwDxB,YAAA,CAAA,CAAA;gBACA,sBAxDoC,GAwDpC,IAAA,CAAA;aAEA,CAAA;iBACiB,IAAjB,CAAA,YAAA;gBAxDA,IAAA,sBAAA,EAAA;oBAyDkB,KAAI,CAxDC,SAwDvB,GAAA,IAAA,CAAA;oBACkB,KAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,aAwD7C,CAAA,EAxD6C,EAwD7C,KAAA,CAAA,YAAA,CAAA,GAxDoF,CAwDpF,EAxDqF,KAwDrF,CAxD0F,YAwD1F,CAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;oBACkB,cAAc,CAxDC,IAwDjC,CAxDkC,CAAK;iBAyDtB;qBACjB;oBAEoB,KAApB,CAAA,wBAxDiD,EAAE,CAwDnD;oBACkB,KAAI,CAxDC,YAwDvB,CAAA,IAAA,CAAA,IAAA,gBAAA,CAAA,EAAA,EAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA;oBACkB,cAxDM,CAwDxB,KAAA,CAxDmC,CAAK;iBAyDxC;aAEA,EAAA,UAAA,CAAA,EAAA;gBACA,IAAA,0BAAA,CAAA,CAAA,CAAA,EAAA;oBAxDA,KAAA,CAAA,wBAAA,EAAA,CAAA;oBAyDkB,KAAI,CAxDC,SAwDvB,GAxDwB,IAwDxB,CAxDqC;oBAyDnB,KAxDI,CAwDtB,YAAA,CAAA,IAAA,CAAA,IAAA,gBAAA,CAAA,EAAA,EAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;oBACA,cAAA,CAAA,KAAA,CAAA,CAAA;iBACA;qBAxDA;oBAyDA,KAAoB,CAApB,YAAiC,CAxDC,IAAI,CAwDtC,IAAA,eAAA,CAAA,EAAA,EAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;oBACA,IAAA;wBACA,cAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA;qBAtDqB;oBAyDD,OAApB,EAAA,EAAA;wBACA,aAAA,CAxDkC,EAwDlC,CAAA,CAAA;qBAvDqB;iBAyDrB;gBACA,KAAA,CAAA,kBAAA,GAAA,WAAA,CAAA;gBACA,KAAA,CAAA,cAAA,GAAA,SAAA,CAAA;;;;SAtDA,CAAA,CAAA;KA2DA,CAAA;;;;IAKA,MAAA,CAAA,SAAA,CAAA,wBAAA,GAAA,YAAA;;;;IA6DA,OAAA,MAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AArHA,IAAA,WAAA,IAAA,YAAA;;;;;QACG,IAwHH,CAxHG,IAwHH,GAAA,IAAA,CAxH+C;KAyH/C;;;;;;;;;;CA1HA,EAAA,CAAA,CAAA;AAsIA,IAAA,aAAA,IAAA,YAAA;;;;;IAIA,SAAA,aAAA,CAAA,SAAA,EAAA,KAAA,EAAA;;;;;CAJA,EAAA,CAAA,CAAA;;;;;;;IA5HA,SAAA,aAAA,CAAA,MAAA,EAAA,IAAA,EAAA,cAAA,EAAA;QAIA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA;;;;;KAmJA;;;;;;;;QAQA,IAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,aAAA,CAAA,SAAA,CAAA,WAnJW,GAmJX,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAQA;QALA,IAAA,IAAA,CAAA,mBAAA,CAAA,MAAA,KAAA,CAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,MAAA,KAAA,CAAA,EAAA;;;;QAIA,OArJGZ,+BAqJH,CAAA,IAAA,CAAA,cAAA,EAAA,UAAA,aAAA,EAAA,EAAA,OAAA,aAAA,GAAA,KAAA,CAAA,oBAAA,EAAA,GAAAhB,qBAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,aAAA,CAAA,SAAA,CAAA,WArJW,GAqJX,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,IAAA,IAAA,CAAA,iBAAA,CAAA,MAAA,KAAA,CAAA;;;;;;;;;;;;;IAgBA,aAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,UA3J4B,EA2J5B,QAAA,EA3JmC,QA2JnC,EAAA,UAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,YAAA,GAAA,iBAAA,CAAA,QAAA,CAAA,CAAA;;QAGI,UAAJ,CAAA,QAAA,CACoB,OA3J2C,CA0J/D,UAAA,CAAA,EAAA;YAGA,KAAA,CAAA,cAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;IAgBA,aAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,UAAA,EAAA,QAjKwB,EAiKxB,cAAA,EAAA,UAAA,EAAA;QACA,qBAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA;QAEA,qBAAA,IAAA,GAjKc,QAiKd,GAAA,QAAA,CAAA,KAAA,GAAA,IAjKqD,CAAU;QAkK/D,qBAAA,OAAA,GAAA,cAAA,GAAA,cAAA,CAAA,UAAA,CAAA,UAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA;;QAEA,IAAA,IAAA,IAAA,MAAA,CAAA,YAAA,KAAA,IAAA,CAAA,YAAA,EAAA;YAjKA,IAAA,IAAA,CAAA,2BAAA,CAAA,IAAA,EAAA,MAAA,mBAAA,EAAA,MAAA,CAAA,YAAA,GAAA,qBAAA,CAAA,EAAA;;gBAmKQ,qBAjKoB,MAiK5B,KAAA,EAAA,OAAA,GAAA,MAAA,EAAA,CAAA;gBACQ,IAAR,CAAA,mBAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,MAAA,CAAA,SAAA,EAAA,IAAA,CAAA,CAAA,CAAA;aACO;;;gBAIC,MAAR,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAjKc;;aAqKP;;YACP,IAAQ,MAAR,CAAA,SAAA,EAAA;gBACA,IAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,QAAA,EAAA,OAAA,GAAA,OAAA,CAAA,QAAA,GAAA,IAAA,EAAA,UAAA,CAAA,CAAA;;aAhKW;iBAkKX;gBACQ,IAAI,CAjKC,mBAiKb,CAAA,UAjK2C,EAiK3C,QAjKqD,EAiKrD,cAAA,EAAA,UAAA,CAAA,CAAA;aACO;SAEP;;YAEM,IAAI,IAAV,EAjKW;gBAkKH,IAAI,CAjKC,6BAiKb,CAjKkC,QAAiB,EAiKnD,OAAA,CAjK6D,CAiK7D;;YAGA,IAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,IAAA,WAAA,CAAA,UAAA,CAAA,CAAA,CAAA;;YACA,IAAQ,MAAR,CAAA,SAAA,EAAA;gBACA,IAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,IAAA,EAAA,OAAA,GAAA,OAAA,CAAA,QAAA,GAAA,IAAA,EAAA,UAAA,CAAA,CAAA;;aAEA;;;;;;;;;;;;IAeA,aAAA,CAAA,SAAA,CAAA,2BAAA,GAAA,UAAA,IAAA,EAAA,MAAA,EAAA,IAtK0C,EAsK1C;QACA,QAAA,IAAY;YAEN,KAtKK,QAsKX;gBACA,OAAA,IAAA,CAAA;YACA,KAAA,2BAAA;gBACA,OAAA,CAAA,yBAAA,CAAA,IAAA,EAAA,MAAA,CAAA;oBACA,CAAA,YAAA,CAAA,IAAA,CAAA,WAAA,EAAA,MAAA,CAAA,WAAA,CAAA,CAAA;;;;;;;;;;;IAaA,aAAA,CAAA,SAAA,CAAA,6BAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAoBA;QAnBA,qBAAA,QAAA,GAAA,iBAAA,CAAA,KAAA,CAAA,CAAA;QA1KA,qBAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA;QA2KA,OAAA,CAAQ,QAAR,EAAA,UAAA,IAAA,EAAA,SAAA,EAAA;YACA,IAAA,CAAA,CAAA,CAAA,SAAA,EAAA;gBA1KA,KAAA,CAAA,6BAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;aA2KA;iBACA,IAAA,OAAA,EAAA;gBACA,KAAA,CAAA,6BAAA,CAAA,IAAA,EAAA,OAAA,CAAA,QAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA,CAAA;aAxKS;iBACE;gBA2KX,KAAA,CAAA,6BAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;aA1KW;SA2KX,CAAA,CAAA;QACA,IAAA,CAAA,CAAA,CAAA,SAAA,EAAA;YA1KA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA;SA2KA;aACA,IAAA,OAAA,IAAA,OAAA,CAAA,MAAA,IAAA,OAAA,CAAA,MAAA,CAAA,WAAA,EAAA;YACA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,OAAA,CAAA,MAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA;;;;SAxKA;KA6KA,CAAA;;;;;QASA,IAAA,KAAA,GAAA,IAAA,CAAA;;;QA/KA,OAAA8B,yBAAA,CAAA,IAAA,CAAA,cAAA,EAAA,UAAA,MAAA,EAAA,EAAA,OAAA,MAAA,KAAA,IAAA,CAAA,EAAA,CAAA,CAAA;KA+KA,CAAA;;;;;QAvKA,IAAA,KAAA,GAAA,IAAA,CAAA;;;;KAAA,CAAA;;;;;IAqLA,aAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CArKA;QAsKA,qBAAA,WAAA,GAAA,MAAA,CAAA,YAAA,GAAA,MAAA,CAAA,YAAA,CAAA,WAAA,GAAA,IAAA,CAAA;QACA,IAAM,CAAN,WAAA,IAAA,WAAA,CAAA,MAAA,KAAA,CAAA;YACA,OAAA9B,qBAAA,CAAA,IAAkB,CAAlB,CAAA;QACA,qBAAA,GAAA,GAAA4B,qBAAA,CAAA,IAAA,CAAAC,yBAAA,CAAA,WAAA,CAAA,EAAA,UAAA,CAAA,EAAA;YAjLA,qBAAA,KAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;YAkLA,qBAAA,UAAA,CAAA;YACA,IAAA,KAAA,CAAA,WAAA,EAAA;gBACA,UAAA,GAjLoB,kBAiLpB,CAAA,KAAA,CAAA,WAAA,CAAA,MAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;aACA;iBACA;gBACA,UAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;KA/KA,CAAA;;;;;IA0LA,aAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,IAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAnKA;QAqKI,qBAAJ,MAAA,GAAA,IApLwC,CAoLxC,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;QACA,qBAAA,sBAAA,GApLiC,IAoLjC,CAAA,KApL2C,CAAA,CAAI,EAoL/C,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA;aACA,OAAA,EAAA;aACA,GAAQ,CAAR,UAAA,CAAA,EAAA,EAAA,OAAA,KAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;aACA,MAAA,CAAY,UAAA,CAAZ,EAAA,EApLa,OAAA,CAAK,KAoLlB,IAAA,CAAA,EAAA,CAAA,CAAA;QACA,OAAA,cAAA,CAAoBD,qBApLG,CAoLvB,IAAA,CAAAC,yBAAA,CAAA,sBAAA,CAAA,EAAA,UAAA,CAAA,EAAA;YACA,qBAAA,GAAA,GAAAD,qBAAA,CAAA,IAAA,CAAAC,yBAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,UAAA,CAAA,EAAA;gBApLA,qBAAA,KAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA;gBAqLA,qBAAA,UAAA,CAAA;gBACA,IAAA,KAAA,CAAA,gBAAA,EAAA;oBACA,UAAA,GApLsB,kBAoLtB,CAAA,KAAA,CAAA,gBAAA,CAAA,MAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;iBACA;qBACA;oBACA,UAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;iBACA;;;;;KAlLA,CAAA;;;;;IA4LA,aAAA,CAAA,SAAA,CAAA,uBAAA,GAAA,UAAA,CAAA,EAAA;;;;;;;;;;;IAWA,aAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,SAAA,EAAA,IAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CA9KA;QA+KA,qBAAA,aA3L0C,GA2L1C,IAAA,IAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA,aAAA,GAAA,IAAA,CAAA;QACA,IAAM,CAAN,aAAA,IAAA,aAAA,CAAA,MAAA,KAAA,CAAA;YACA,OAAA7B,qBAAA,CAAA,IAAkB,CAAlB,CAAA;QACA,qBAAA,cA3LqC,GA2LrCgB,+BAAA,CAAA,IAAA,CAAAa,yBAAA,CAAA,aA3LoE,CAAK,EA2LzE,UAAA,CA3L0E,EA2L1E;YACA,qBAAA,KAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA;YA3LA,qBAAA,UAAA,CAAA;YA4LA,IAAQ,KAAR,CAAA,aAAA,EAAA;gBACA,UAAA;oBACA,kBAAA,CAAA,KAAA,CAAA,aAAA,CAAA,SAAA,EAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;aACA;iBACA;gBACA,UAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,SAAA,EAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;;;KAzLA,CAAA;;;;;IAmMA,aAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,MAAA,EAAA;QACA,qBAAA,OAAA,GAAA,MAAA,CAAA,QAAA,CAAA;QACA,OAAAD,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,OAAA,EAAA,MAAA,CAAA,EAAA,UAAA,YAAA,EAAA;;;;;;;;;;;IAWA,aAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,OAAA,EAAA,MAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,OAAA,UAAA,CAAA,OAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA;;;;;;;;;;;IAWA,aAAA,CAAA,SAAA,CAAA,QAAA,GAAA,UAAA,KAAA,EAAA,QAAA,EAAA;QAEA,qBAAA,MAAA,GAAA,mBAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;;;;;;;;KA2BA;;;;;IAMA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UA5NS,cA4NT,EAAA;QACA,qBAAA,UAAA,GAAA,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA;;;;;;;;;;;;IAeA,cAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,UAAA,EAAA,QAAA,EAAA,QAhOkD,EAgOlD;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,QAAA,GAAA,iBAAA,CAAA,QAAA,CAAA,CAAA;;;YAIA,qBAAA,eAAA,GAAA,WAAA,CAAA,KAAA,CAAA,MAAA,CAAA;YACM,KAAI,CAhOC,gBAgOX,CAAA,WAAA,EAhOyC,QAgOzC,CAAA,eAAA,CAAA,EAAA,QAAA,CAAA,CAAA;YACA,OAAA,QAAA,CAAA,eAAA,CAAA,CAAA;SACA,CAAA,CAAA;;;;;;;;;;;;IAeA,cAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,UAAA,EAAA,QAAA,EAAA,aAAA,EAAA;;QAEA,qBAAA,IAAA,GAAA,QArOc,GAAU,QAqOxB,CAAA,KArOsC,GAqOtC,IAAA,CAAA;QACA,IAAA,MAAA,KAAA,IArOa,EAAQ;;YAuOrB,IAAA,MAAA,CAAA,SAAA,EAAA;;gBApOA,qBAAA,OAAA,GAAA,aAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;;oBAuOY,IAAZ,CAAA,qBAAA,CAAA,UAAA,EAAA,QAAA,EAAA,OAAA,CAAA,QAAA,CAAA,CAAA;iBACA;aACA;iBArOA;;;aAwOA;SACA;aACA;YACA,IAAA,IAAA,EAAA;;;;;;;;;;;IAWA,cAAA,CAAA,SAAA,CAAA,6BAAA,GAAA,UAAA,KAAA,EAzOY,cAyOZ,EAAA;QACA,IAAA,IAAA,CAAA,kBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,EAAA;YACA,IAAA,CAAA,0BAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;;;;;;;;;;;IAYA,cAAA,CAAA,SAAA,CAAA,0BAAA,GAAA,UAAA,KA7O8B,EA6O9B,cAAA,EAAA;QACA,qBAAA,OAAA,GAAA,cAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;QACA,IAAA,OAAA,IAAA,OAAA,CAAA,MAAA,EAAA;;;;;;;;;;;IAcA,cAAA,CAAA,SAAA,CAAA,wBAjPe,GAiPf,UAAA,KAjPiC,EAiPjC,cAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEA,qBAAA,OAAA,GAAA,cAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;;YAEA,qBAAA,QAjPkC,GAiPlC,iBAAA,CAAA,KAAA,CAAA,CAAA;;YAEA,OAAA,CAAA,QAAA,EAAA,UAAA,CAjPiB,EAiPjB,CAAA,EAAA,EAAA,KAAA,CAAA,6BAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACA,IAAA,OAAA,CAAA,MAAA,EAAA;;gBAEA,OAAA,CAAA,MAAA,CAAA,UAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;IA6BA,cAAA,CAAA,SAAA,CAAA,cA3PS,GA2PT,UAAA,UA3PyB,EA2PzB,QAAA,EAAA,cAAA,EAAA;QACA,qBAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA;;QAEA,qBAAA,CAAA,MAAA,CAAA,CAAA;;QAEA,IAAA,MAAA,KAAA,IAAA,EAAA;YA3PA,IAAA,MAAA,CAAA,SAAA,EAAA;;gBA6PQ,qBAAR,OAAA,GAAA,cA3PuD,CA2PvD,kBAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;gBACA,IAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,QAAA,EAAA,OAAA,CAAA,QAAA,CAAA,CAAA;aACA;iBA3PA;;;aA8PA;SAEA;aACA;YAEA,IAAA,MAAA,CAAA,SAAA,EAAA;;gBAEA,qBAAA,OA3P8B,GA2P9B,cAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;gBACA,IAAU,IAAV,CAAA,kBA3PiC,CA2PjC,YAAA,CAAA,MAAA,CAAA,QAAA,CAAA,EAAA;oBACU,qBAAV,MAAA,KAAA,IAAA,CAAA,kBAAA,CAAA,QAAA,CAAA,MAAA,CAAA,QAAA,CAAA,EAAA,CAAA;;;oBAGA,OAAA,CAAA,SAAA,GAAA,MA3PkC,CA2PlC,YAAA,CAAA;oBACA,OAAA,CAAA,KAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA;oBACU,IAAV,OAAA,CAAA,MAAA,EAAA;;;wBAEA,OAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MA3PyB,CA2PzB,YAAA,EAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CA3PoD,CAAQ;qBA4P5D;oBAEU,uCAAV,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;iBACA;qBACA;;;oBAGA,OAAA,CAAA,KAAA,GAAA,MAAA,CAAA;oBACA,OAAA,CAAA,QAAA,GAAA,kBAAA,CAAA;oBAEU,IAAI,OAAd,CAAA,MAAA,EAAA;;;wBAzPA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA,MAAA,EAAA,kBAAA,CAAA,CAAA;;oBA6PY,IAAZ,CAAA,mBAAA,CAAA,UAAA,EA3P6C,IA2P7C,EAAA,OAAA,CAAA,QAAA,CAAA,CAAA;iBACA;aACA;iBACA;;gBAGA,IAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,IAAA,EAAA,cAAA,CAAA,CAAA;;;;IAaA,OAAA,cAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;;CAQA;;;;;AAIA,SAAA,kBAAA,CAAA,QA3QwB,EA2QxB;IA3QA,KAAA,qBAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,MAAA,EAAA;QA4QA,qBAAA,KAAA,GAAA,CAAA,CAAA,YAAA,CAAA;QAEA,IAAA,KAAA,IAAA,KAAA,CAAA,aAAA;YACA,OAAA,KAAA,CAAA,aAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,mBAAA,CAAA,QAAA,EAAA;IA9QA,IAAA,CAAA,QAAA;QA+QA,OAAA,IAAA,CAAA;IAEE,KAAF,qBAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,MAAA,EAAA;QACA,qBAAA,KAAA,GAAA,CAAA,CAAA,YAAA,CAAA;;;;;;;;;;;AAaA,SAAA,iBAAA,CAAA,IAAA,EAAA;IACA,qBAAA,MAAA,GAAA,EAAA,CAAA;;;;;CAKA;;;;;AAKA,SAAA,gBAAA,CAAA,QAAA,EAAA;IACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;QACA,qBAAA,GAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA;;YD18CA,MAAA,IAAA,KAAA,CAAA,8BAAA,GAAA,GAAA,GAAA,oBAAA,GAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoHA,SAAA,UAAA,CAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAmC,QAAnC,EAAA,EAAA,EAAA;QACA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA;QACA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;;;;;KAKA;IAIA,MAAA,CAAA,cAAA,CAAA,UAAA,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,QAAA,EAAA;YACA,IAAA,QAAA,IAAA,IAAA,EAAA;gBACA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,QAAA,GAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;IAWA,MAAA,CAAA,cAAA,CAAA,UAAA,CAAA,SAAA,EAAA,qBAAA,EAAA;;;;;;QAAA,GAAA,EAAA,UAAA,KAAA,EAAA;;;;YAIA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;IAIA,UAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA;QACI,qBAAJ,MAAA,GAAA;YACA,kBAAA,EAAA,aAAA,CAAA,IAAA,CAAA,kBAAA,CAAA;;;;QAPG,OAAA,IAWH,CAAA;KACA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,UAAA,CAAA,SAAA,EAAA,SAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;YACA,OAAA,IAAA,CAAA,MAAA,CAAA,aAAA,CAAA,IAAA,CAAA,QAAA,EAAA;gBACM,UAAN,EAAA,IAAsB,CAAtB,KAAA;gBACA,WAAA,EAAA,IAAA,CAAA,WAAA;gBACA,QAAA,EAAA,IAAA,CAAA,QAAA;;gBAVA,mBAAA,EAAA,IAAA,CAAA,mBAAA;gBACQ,gBAWR,EAXyB,aAWzB,CAAA,IAAA,CAAA,gBAX2D,CAW3D;aACA,CAAA,CAAA;;;;;;;;IATA,EAAA,IAAA,EAAAN,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,EAAA,EAAA;CAcA,CAAA;;;;AAIA,UAAK,CAAL,cAAoB,GAApB,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAA,MAAA,GAAA;IAZA,EAAA,IAAA,EAAA,cAAA,GAAA;IAcA,EAAA,IAAA,EAAA,SAAA,EAAA,UAbwB,EAAM,CAa9B,EAAA,IAAA,EAAAH,uBAAA,EAAA,IAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAAK,sBAAA,GAbe;IAcf,EAAA,IAAA,EAAAC,wBAAA,GAAqB;CACrB,CAAA,EAAA,CAAA;AACA,UAAA,CAAA,cAAA,GAAA;IACA,aAAA,EAbc,CAad,EAAA,IAAA,EAAAE,mBAAA,EAAA,EAAA;IACA,UAAA,EAAY,CAAZ,EAbc,IAad,EAbiBA,mBAajB,EAbuB,EAAM;IAc7B,qBAAqB,EAbE,CAAA,EAAG,IAAA,EAAMA,mBAAA,EAAM,EAAE;IAcxC,kBAbc,EAAM,CAapB,EAAA,IAAA,EAAAA,mBAbkC,EAalC,EAbkC;IAclC,oBAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;IAGA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;IAgEA,SAAA,kBAAA,CAAA,MAAA,EAAA,KAAA,EAAA,gBAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAUA;QATA,IAAM,CAAN,MAAA,GAAA,MAAA,CAAuB;QACvB,IAAA,CAAA,KAAA,GAAa,KAAb,CAAA;QACA,IAAA,CAAO,gBAAP,GAAA,gBAAA,CAAA;QACA,IAAA,CAAA,QAAA,GAAA,EAAA,CAAA;QACA,IAAA,CAAA,YAAA,GAAA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,EAAA;;;;;KAKA;IAIA,MAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,SAAA,EAAA,YAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,QAAA,EAAA;YACA,IAAA,QAAA,IAAA,IAAA,EAAA;gBACA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,QAAA,GAAA,CAAA,QAAA,CAAA,CAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;IAKA,MAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,SAAA,EAAA,qBAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,KAAA,EAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;IAkBA,kBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAM,MAAN,EAAA,OAAA,EAAA,OAAA,EAAA,QAAA,EAAA;QACA,IAAA,MAAA,KAAA,CAAA,IAAA,OAAA,IAAA,OAAA,IAAA,QAAA,EAAA;YAEA,OAAA,IAAA,CAAA;SACA;QACA,IAAM,OAAN,IAAA,CAhEkB,MAgElB,KAAA,QAhEqC,IAgErC,IAAA,CAAA,MAAA,IAAA,OAAA,EAAA;YACA,OAAA,IAAA,CAAA;SACA;QACI,qBAAJ,MAAA,GAAA;YACA,kBAAA,EAAA,aAAA,CAAA,IAAA,CAAA,kBAAA,CAAA;;;;QA9DA,OAAA,KAAA,CAAA;KAmEA,CAAA;;;;;QA/DG,IAoEH,CAAA,IAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;KACA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,SAAA,EAAA,SAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;YACA,OAAA,IAAA,CAAA,MAAA,CAAA,aAAA,CAAA,IAAA,CAAA,QAAA,EAAA;gBACM,UAAN,EAAA,IAAsB,CAAtB,KAAA;gBACA,WAAA,EAAA,IAAA,CAAA,WAAA;gBACA,QAAA,EAAA,IAAA,CAAA,QAAA;;gBAnEA,mBAAA,EAAA,IAAA,CAAA,mBAAA;gBACQ,gBAoER,EApEyB,aAoEzB,CAAA,IAAA,CAAA,gBAAA,CAAA;aACA,CAAA,CAAA;;;;;;;;IAlEA,EAAA,IAAA,EAAAL,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAC,eAAD,EAAA,EAAA,EAAA;CAuEA,CAAA;;;;AAlEO,kBAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAuEA,EAAA,IAAA,EAAQ,MAAR,GAtEa;IAuEb,EAAA,IAAA,EAAA,cAtEkB,GAsElB;IACA,EAAA,IAAA,EAAAb,gCAAA,GAAA;CACA,CAAA,EAAA,CAAA;AACA,kBAAA,CAAA,cAtE6B,GAsE7B;IACA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAAiB,yBAAA,EAAA,IAtE+B,EAAM,CAsErC,aAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAAC,mBAAA,EAAA,EAAA;IACA,aAAA,EAtEc,CAsEd,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;IACA,UAAA,EAAA,CAAA,EAtEW,IAsEX,EAAAA,mBAAA,EAtEiB,EAAY;IAuE7B,qBAtEiB,EAAM,CAsEvB,EAAA,IAtE6B,EAAEA,mBAsE/B,EAAA,EAAA;IACA,kBAAA,EAAA,CAAqB,EAtEE,IAsEvB,EAAAA,mBAAA,EAAA,EAAA;IACA,oBAtEoB,EAsEpB,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAtEkC,EAAM;IAuExC,YAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;IAGA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAAD,yBAAA,EAAA,EAAA;;;;CA4CA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDhUA,SAAA,gBAAA,CAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,GAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAEU,IAAV,CAAA,MAAU,GAAkB,MAAM,CAAlC;QAAG,IAAH,CAAA,OAAA,GAAA,OAAA,CAAA;QAYI,IAAI,CAAC,QAAT,GAAA,QAAA,CAAA;QACA,IAAM,CAAN,GAAU,GAAV,GAAA,CAAA;QACA,IAAA,CAAA,OAAY,GAAZ,EAAA,CAAA;QACA,IAAA,CAAO,MAAP,GAAA,KAAA,CAAA;QACA,IAAA,CAAA,uBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA,CAAA;QACA,IAAA,CAAA,YAAA,GAAA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,EAAA;;;;SAIA,CAAA,CAAA;;IAKA,MAAA,CAAA,cAAA,CAAI,gBAAJ,CAAA,SAAA,EAAA,UAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,MAAA,CAAA,EATsC;;;KAAtC,CAAA,CAAsC;;;;;QAiBtC,IAAA,KAAA,GAAA,IAAA,CAAA;;;;KAAA,CAAA;;;;;;;;;SAQA;;;KAAA,CAAA,CAAA;;;;;;;;;IASA,gBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,IAAA,CAAA,YAAA,CAAA,WAAA,EAnB+C,CAAA,EAmB/C,CAAA;;;;IAIA,gBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAZA;QAaA,IAAM,CAAN,IAnBW,CAmBX,KAAA,IAAA,CAAA,IAnBoB,CAoBV,cADV,IAlBqB,CAAQ,IAkB7B,CAAA,MAAA,CAAA,SAlBkD;YAoB5C,OAAO;QACb,qBAAA,cAAA,GAAA,IAAA,CAAA,cAAA,EAAA,CAAA;;;;;;KAhBA,CAAA;;;;;;QA8BA,IAAA,KAAA,GAAA,IAAA,CAAA;QAzBA,OAAA,UAAA,IAAA,EAAA,EAAA,OAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,uBAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA;KAyBA,CAAA;;;;IArBA,gBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,YAAA;QAyBA,OAAA,IAAA,CAAA,KAAA,CAAA,IAxByB,CAwBzB,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;YACA,IAAE,CAAF,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;KACA,CAAA;IACA,OAAA,gBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,gBAAA,CAAA,UAAA,GAAA;;;;aAtBA,EAAA,EAAA;CA2BA,CAAA;;;;AAIA,gBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IAzBA,EAAA,IAAA,EAAA,MAAA,GAAA;IA2BA,EAAA,IAAA,EAAAD,wBA1BkB,GA0BlB;IACA,EAAA,IAAA,EAAAD,sBAAgB,GA1BE;IA2BlB,EAAA,IAAA,EAAAN,+BAAyB,GA1BE;CA2B3B,CAAA,EAAA,CAAA;AACA,gBAAA,CAAA,cAAA,GAAA;;IDrLA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAAK,6BAAA,EAAA,IAAA,EAAA,CAAA,kBAAA,EAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;AAsBA,IAAA,aAAA,IAAA,YAAA;IACA,SAAA,aAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA;QAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA;;;;;CAJA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+CA,sBAAA,CAAA,SAAA,CAAA,sBAAA,GAAA,UAAA,SAAA,EAAA;;;;;;;;;;;;;;;KAeA,CAAA;;;;;IAKA,sBAAA,CAAA,SAAA,CAAA,kBAtBG,GAsBH,UAtBG,QAsBH,EAAA,EAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA,CAAA;;;;;IAMA,sBAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,SAAA,EAAA;QAEI,qBAAJ,OAAA,GAAA,IAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA;QACA,IAAA,CAAA,OAAA,EAAA;;;;;KAKA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ID/DA,SAAA,YAAA,CAAA,cAAmD,EAAnD,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAA,EAAA;QACU,IAAV,CAAA,cAAA,GAAA,cAAA,CAAA;QAEG,IAAH,CAAA,QAAA,GAAA,QAAA,CAAA;QACG,IAAH,CAAA,QAAA,GAAA,QACsB,CADtB;QAaI,IAAI,CAAC,cAAT,GAAA,cAAA,CAAA;QACI,IAAJ,CAAA,SAAkB,GAAlB,IAAA,CAAA;QACA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA;;;;QAIA,cAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;;;;;IAKA,YAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,IAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;;;;IAIA,YAAA,CAAA,SAAA,CAAA,QAAM,GAAN,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAVa,EAUb;;;YAGA,qBAAA,OAAA,GAAA,IAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;YAVA,IAAA,OAAA,IAAA,OAAA,CAAA,KAAA,EAAA;;;oBAaA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;iBACA;qBACA;;;;;;KAMA,CAAA;IAKA,MAAA,CAAA,cAAA,CAfG,YAeH,CAAA,SAAA,EAAA,kBAAA,EAAA;;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAf4D,IAe5D,CAAA,QAAA,CAAA,QAAA,CAfkF,EAelF;;;KAAA,CAAA,CAAA;;;;;;;;;;IASA,MAAA,CAAA,cAAA,CAAI,YAAJ,CAAA,SAAA,EAAA,aAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;YAfG,OAqBH,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA;SACA;;;KAAA,CAAA,CAAA;;;;;;;;YAjBG,QAuBH,IAAA,CAAA,eAAA,EAAA;SACA;;;KAAA,CAAA,CAAA;IAIA,MAAA,CAAA,cAAA,CAAA,YAAA,CAAA,SAAA,EAAA,oBAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;IAIA,YAAA,CAAA,SAAA,CAAA,MAAA,GAAA,YAAA;QACI,IAAI,CAvBC,IAuBT,CAAA,SAAA;YACA,MAvBW,IAuBX,KAAA,CAAA,yBAAA,CAAA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,MAAA,EAAA,CAAA;;;;;;;;;;;;;;;QAeA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA;KACA,CAAA;;;;IAIA,YAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA;QACA,IAAM,IAAI,CA3BC,SA2BX,EAAA;YACA,qBAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA;YACA,IAAA,CAAA,SAAA,CAAA,OAAA,EAAA,CAAA;;;;;;;;;;;IAWA,YAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,cA/BU,EA+BV,QAAA,EAAA;QACI,IAAJ,IAAA,CAAA,WAAA,EAAA;YACA,MAAA,IAAA,KA/Be,CA+Bf,6CAAA,CAAA,CAAA;SACA;QACI,IAAJ,CAAA,eAAA,GA/BU,cA+BV,CAAA;QACI,qBA/BM,QAAA,GAAW,cA+BrB,CAAA,eAAA,CAAA;QACI,qBAAJ,SAAA,IAAA,EAAA,QA/BoC,CAAe,YA+BnD,GAAA,SAAA,CAAA,CAAA;;;QAGI,qBAAJ,aAAA,GAAA,IAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,QAAA,CAAA;QACI,qBAAJ,QAAA,GAAA,IAAA,cAAA,CAAA,cAAA,EAAA,aAAA,EAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,OAAA,EAAA,IAAA,CAAA,QAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;;;QAEA,IA/BQ,CA+BR,cA/BmB,CA+BnB,YA/B4B,EAAS,CA+BrC;QACA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA;;;;;IA7BA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAC,EAAD,CAAC,EAAD,QAAA,EAAA,eAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA;CAkCA,CAAA;;;;AAIA,YAAO,CAAP,cAAA,GAAA,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAA,sBAAA,GAAA;IAhCA,EAAA,IAAA,EAAAD,8BAAA,GAAA;IAkCA,EAAA,IAAA,EAAAD,sCAAA,GAjCmC;IAkCnC,EAAA,IAAA,EAAA,SAAA,EAAA,UAjC6B,EAiC7B,CAAA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAA,EAAA,CAjCyD,MAiCzD,EAAA,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAAD,+BAAA,GAAA;CAGA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;IAmDA,cAAA,CAAA,SAAA,CAAA,GAAA,GAAA,UAAA,KAAA,EAAA,aAAA,EAAA;QACA,IAAA,KAAA,KAAA,cAAA,EAAA;YAEA,OAzEW,IAyEX,CAAA,KAAA,CAzEwB;SA0ExB;QACA,IAAA,KAAA,KAAA,sBAAA,EAAA;;SD5PA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuGA,SAAA,eAAA,CAAA,MAAA,EAAA,YAAA,EAAA,QAAA,EAAA,QAAA,EAAA,kBAA+E,EAA/E;QAEI,IAAI,CAAC,MAAM,GAAG,MAAlB,CAAA;QACA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;;;;;KAIA;IACA,AAAA;;;;;QAOA,IAAA,KAAA,GAAA,IAAA,CAAA;;QADA,IAAA,CAAA,YAAA,GAAAD,iCAAA,CAAA,IAAA,CAAA,YAAA,EAAA,YAAA,EAAA,OAAA,KAAA,CAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,SAAA,CAAA,YAAA,GAAA,CAAA,CAAA;KACA,CAAA;;;;;;QAMA,OA1BG,IA0BH,CA1BwB,aA0BxB,CAAA,QAAA,EAAA,IAAA,CA1B2C,MA0B3C,CAAA,MAAA,CAAA,CAAA;;;;;;;;;;;IAWA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,QAAA,EAAA,MAAA,EAAA;QACA,qBAAA,GAAA,GAAA,EAAA,CAAA;;;;YA3BA,IAAA,KAAa,CAAb,YAAA,IAAA,CAAwB,KAAe,CAAvC,OAAA,IAAA,KAAA,CAAA,aAAA,EAAA;gBAoBE,qBAAF,WAAA,GAAA,KAAA,CAnB8C,aAmB9C,CAAA;;;aAhBA;iBA+BA,IAAA,KAAA,CAAA,YAAA,IA9BuB,CAAa,KA8BpC,CAAA,OAAA,EA9B8C;gBA+B9C,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;aAEA;iBACA,IAAA,KAAA,CAAA,QAAA,EAAA;;;;;;;;;;;IAWA,eAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,QAAA,EAlCoB,KAkCpB,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,OAlCS,IAkCT,CAAA,kBAAA,CAAA,OAAA,CAAA,KAAA,EAAA,YAAA;YACA,qBAAA,OAAA,GAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;YACA,OAAAD,+BAAA,CAAA,IAAA,CAAA,OAAA,EAAA,UAAA,MAAA,EAAA;;gBAjCA,OAAA,KAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,EAAA,MAAA,CAAA,MAAA,CAAA,CAAA;aAmCA,CAAA,CAAA;SACA,CAAA,CAAA;;;;;IAhCA,EAAA,IAAA,EAAAlB,wBAAA,EAAA;CAqCA,CAAA;;;;AAIA,eAAA,CAAA,cAAyB,GAAG,YAA5B,EAAA,OAAA;IACA,EAAA,IAAA,EAAA,MAAA,GAAA;;ID/KA,EAAA,IAAA,EAAAe,sBAAA,GAAA;;;CC8KA,CAAA,EAAA,CAAA;;;;;;;;;;;;ADzIA,IAEC,iBAFD,GAEC,CAFD,YAAA,EAAA,UAAA,EAAA,kBAEC,EAFD,gBAAA,CAAA,CAAA;;;;;AAMA,IAGa,oBAHb,GAAA,IAAAhB,4BAAA,CAAA,sBAAA,CAAA,CAAA;;;;AAIA,IAAA,oBAAA,GAAA,IAAAA,4BAAA,CAAA,sBAAA,CAAA,CAAA;AACA,IAAA,gBAAA,GAAA;IACAiB,wBAAI;IACJ,EAAA,OAAA,EAAA,aAGsB,EAHtB,QAAA,EAAA,oBAAA,EAAA;IACA;QACA,OAAA,EAAA,MAAA;QACA,UAAA,EAAA,WAAA;QACA,IAAA,EAAA;YACAf,4BAAQ,EAAR,aAAA,EAAA,sBAAA,EAAAe,wBAAA,EAAAC,sBAAA;YACAJ,mCAAA,EAAAE,sBAGwC,EAHxC,MAAA,EAAA,oBAAA;YACA,CAGI,mBAHJ,EAAA,IAGmCL,sBAAA,EAAU,CAH7C,EAAA,CAAA,kBAG6C,EAAuB,IAHpEA,sBAAA,EAAA,CAAA;SACA;KACA;IACE,sBAAF;IACE,EAAC,OAGC,EAAQ,cAHZ,EAAA,UAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,MAAA,CAAA,EAAA;IACA,EAAA,OAAA,EAAAG,mCAAA,EAAA,QAAA,EAAAC,oCAAA,EAAA;;;;IAIA,EAAA,OAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,EAAA,aAAA,EAAA,KAAA,EAAA,EAAA;CACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,IAAA,YAAA,IAAA,YAAA;;;;;;;;;;;;;;;;;;;;;IAoBA,YAAA,CAAA,OAAA,GAAA,UAAA,MAAA,EAAA,MAAA,EAAA;QACA,OAAA;YACA,QAAU,EAAV,YAAA;YACA,SAAA,EAAA;gBACA,gBAAA;gBACA,aAAA,CAAA,MAAA,CAAA;gBACQ;oBACR,OAAA,EAAA,oBAAA;oBACU,UAAV,EAAA,mBAAA;oBACU,IAAV,EAAA,CAAA,CAAA,MAAA,EAAA,IAAAJ,sBAAA,EAAA,EAAA,IAAAE,sBAAA,EAAA,CAAA,CAAA;iBACA;gBACA,EAAA,OAAA,EAAA,oBAFmC,EAEnC,QAAA,EAAA,MAAA,GAFwD,MAAM,GAE9D,EAAA,EAAA;gBACA;oBACA,OAAA,EAAAD,gCAAA;oBACA,UAAA,EAAA,uBAAA;oBACU,IAAV,EAAA;wBACAJ,gCAAA,EAAA,CAFiC,IAEjCC,oBAAA,CAAAC,6BAAA,CAFyC,EAEzC,IAAAC,sBAAA,EAAA,CAAA,EAAA,oBAAA;qBACA;iBACS;gBACD;oBACR,OAAA,EAAA,kBAAA;oBACA,WAAA,EAAA,MAAA,IAAA,MAAA,CAAA,kBAAA,GAAA,MAAA,CAAA,kBAAA;wBACA,YAAA;iBACA;;;;;;;;;;;IAUA,YAAA,CAAA,QAAA,GAAA,UAHQ,MAAU,EAGlB;QACA,OAAA,EAAA,QAAA,EAAA,YAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;;;CAtDA,EAAA,CAAA,CAAA;;IAqDA,EAAA,IAAA,EAAAJ,sBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,EAAA;CAMA,CAAA;;;;;;;;;;;;;;;;;;CA8BA;;;;;AAMA,SAAA,mBAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAA,aAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;AAsFA,SAAA,WAAA,CAAA,GAAA,EA9CO,aA8CP,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,IAAA,EAAA,mBAAA,EAAA,kBAAA,EAAA;IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;IACA,qBAAA,MA9CY,GAAoB,IA8ChC,MAAA,CAAA,IAAA,EAAA,aAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA;IACA,IAAA,mBAAA,EAAA;QAEM,MAAN,CAAA,mBAAA,GAAA,mBAAA,CAAA;KACA;IACA,IAAA,kBAAA,EAAA;QAEM,MAAN,CAAA,kBAAA,GAAA,kBAAA,CAAA;KACA;IACA,IAAI,IAAJ,CAAA,YAAA,EAAA;QACA,MAAA,CAAS,YAAT,GAAA,IAAA,CAAA,YAAA,CAAA;KACA;IACA,IAAA,IAAM,CAAN,aAAA,EAAA;QACA,qBAAA,KAAA,GAAAD,gCAAA,EAAA,CAAA;QACA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,EAAA;YACA,KAAA,CAAA,QAAA,CAAA,gBAAA,GAAA,EAAA,CAAA,CAAA,WAAA,GAAA,IAAA,CAAA,CAAA;YAEA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,QAAA,EAAA,CAAA,CAAA;YACA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;AArBA,IAAA,iBAAA,IAAA,YAAA;;;;;;;QA6CA,IAAA,CAAA,yBAAA,GAAA,IAAAD,oBAAA,EAAA,CAAA;KACA;;;;IAIA,iBAAA,CAAA,SAAA,CAAA,cAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAoCA;QAnCA,qBAAA,CAAA,GA3CY,IAAA,CA2CZ,QAAA,CAAA,GAAA,CAAAD,oCAAA,EAAA,OA3C2D,CA2C3D,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QAEA,OAAA,CAAU,CAAV,IA3Ce,CA2Cf,YAAA;YACA,qBAAA,OAAA,KAAA,IAAA,EAAA,CAAA;YAEA,qBAAA,GAAA,GAAA,IAAA,OAAA,CAAA,UAAA,CAAA,EAAA,EAAA,OAAA,OAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YA3CA,qBAAA,MAAA,GAAA,KAA4C,CAA5C,QAAA,CAA4C,GAA5C,CAAA,MAAA,CAAA,CAAA;YA4CA,qBAAA,IAAA,GAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,CAAA;YACA,IAAQ,KAAR,CAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,eAAA,CAAA,IAAA,CAAA,EAAA;gBAEA,OAAA,CAAA,IAAA,CAAA,CAAA;aA3CA;iBA4CA,IAAA,IAAA,CAAA,iBAAA,KAAA,UAAA,EAAA;;gBAEA,OAAA,CAAc,IAAd,CA3CgB,CAAI;aA4CpB;iBACA,IAAA,IAAA,CAAA,iBAAA,KAAA,SAAA,EAAA;gBACA,MAAA,CAAA,KAAA,CAAA,kBAAA,GAAA,YAAA;;oBAGA,IAAA,CAAA,KAAA,CAAA,cAAA,EAAA;wBA3CA,KAAA,CAAA,cAAA,GAAA,IAAA,CAAA;wBA4CY,OAAZ,CA3CmB,IA2CnB,CAAA,CAAA;wBACA,OAAA,KAAA,CAAA,yBAAA,CAAA;;qBAEA;yBAEA;wBA3CA,QAAAD,qBAAA,CAAA,IAAA,CAAA,EAAA;qBA4CA;iBAdA,CAAA;gBAiBA,MA3Ca,CAAI,iBA2CjB,EAAA,CAAA;aACA;iBAdA;;;;;KAoBA,CAAA;;;;;IAMA,iBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,wBAAA,EAAA;QACA,qBAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,oBAAA,CAAA,CAAA;QACA,qBAAA,SAAA,GAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,eAAA,CAAA,CAAA;QAEI,qBAAJ,MA9C8B,GAAM,IA8CpC,CAAA,QAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA;QACA,qBAAA,GAAA,GAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAAD,4BAAA,CAAA,CAAA;QACA,IAAA,wBAAA,KAAA,GAAA,CAAA,UAAA,CAAA,CAAA,CAAA,EAAA;YA9CA,OAAA;SA+CA;QACA,IAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,EAAA;YAEA,MA9Cc,CA8Cd,iBA9CgC,EA8ChC,CAAA;SACA;aA7CS,IA8CT,IAAA,CAAA,gBA9CU,CAAyB,IAAC,CA8CpC,EAAA;YAdM,MAeN,CAAA,2BAAA,EAAA,CA9C4C;SA+C5C;;;;;KA5CA,CAAA;;;;;;;;KAKA,CAAA;;;;;IAwDA,iBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,IAAA,EAAA;QACA,OAAA,IAAA,CAAA,iBAAA,KAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,KAAA,KAAA,CAAA;;;CA5HA,EAAA,CAAA,CAAA;;IA0EA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CAuDA,CAAA;;;;;;CAwBA,CAAA,EAAA,CAAA;;;;;;;CAOA;;;;;;;;;;;;;AAcA,IAAA,kBAAA,GAAA,IAAAD,4BAAA,CAAA,oBAAA,CAAA,CAAA;;;;AAIA,SAAA,wBAAA,GAAA;IACA,OAAA;QACA,iBAAA;QACA;YACA,OA7Fc,EA6FdD,6BAAA;YACA,KA7FM,EAAQ,IA6Fd;YACA,UAAA,EAAA,iBAAA;YACA,IAAA,EAAA,CAAA,iBAAA,CAAA;;QDrgBA,EAAA,OAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,CAAA,iBAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}