{"version": 3, "file": "animations.js", "sources": ["../../../../../packages/platform-browser/animations/index.ts", "../../../../../packages/platform-browser/animations/public_api.ts", "../../../../../packages/platform-browser/animations/src/animations.ts", "../../../../../packages/platform-browser/animations/src/private_export.ts", "../../../../../packages/platform-browser/animations/src/module.ts", "../../../../../packages/platform-browser/animations/src/providers.ts", "../../../../../packages/platform-browser/animations/src/animation_renderer.ts", "../../../../../packages/platform-browser/animations/src/animation_builder.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {BrowserAnimationsModule,NoopAnimationsModule,ɵBrowserAnimationBuilder,ɵBrowserAnimationFactory,ɵAnimationRenderer,ɵAnimationRendererFactory} from './public_api';\n\nexport {BaseAnimationRenderer as ɵa} from './src/animation_renderer';\nexport {BROWSER_ANIMATIONS_PROVIDERS as ɵf,BROWSER_NOOP_ANIMATIONS_PROVIDERS as ɵg,InjectableAnimationEngine as ɵb,instantiateDefaultStyleNormalizer as ɵd,instantiateRendererFactory as ɵe,instantiateSupportedAnimationDriver as ɵc} from './src/providers';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the animation package.\n */\nexport {BrowserAnimationsModule,NoopAnimationsModule,ɵBrowserAnimationBuilder,ɵBrowserAnimationFactory,ɵAnimationRenderer,ɵAnimationRendererFactory} from './src/animations';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {BrowserAnimationsModule, NoopAnimationsModule} from './module';\n\nexport {ɵBrowserAnimationBuilder,ɵBrowserAnimationFactory,ɵAnimationRenderer,ɵAnimationRendererFactory} from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport {BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory} from './animation_builder';\nexport {AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory} from './animation_renderer';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {BrowserModule} from '@angular/platform-browser';\n\nimport {BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS} from './providers';\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class BrowserAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationsModule.ctorParameters;\n}\n\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction NoopAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nNoopAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNoopAnimationsModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AnimationBuilder} from '@angular/animations';\nimport {AnimationDriver, ɵAnimationEngine as AnimationEngine, ɵAnimationStyleNormalizer as AnimationStyleNormalizer, ɵNoopAnimationDriver as NoopAnimationDriver, ɵWebAnimationsDriver as WebAnimationsDriver, ɵWebAnimationsStyleNormalizer as WebAnimationsStyleNormalizer, ɵsupportsWebAnimations as supportsWebAnimations} from '@angular/animations/browser';\nimport {Injectable, NgZone, Provider, RendererFactory2} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '@angular/platform-browser';\n\nimport {BrowserAnimationBuilder} from './animation_builder';\nimport {AnimationRendererFactory} from './animation_renderer';\nexport class InjectableAnimationEngine extends AnimationEngine {\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    super(driver, normalizer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: AnimationDriver, },\n{type: AnimationStyleNormalizer, },\n];\n}\n\nfunction InjectableAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nInjectableAnimationEngine.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nInjectableAnimationEngine.ctorParameters;\n}\n\n/**\n * @return {?}\n */\nexport function instantiateSupportedAnimationDriver() {\n  if (supportsWebAnimations()) {\n    return new WebAnimationsDriver();\n  }\n  return new NoopAnimationDriver();\n}\n/**\n * @return {?}\n */\nexport function instantiateDefaultStyleNormalizer() {\n  return new WebAnimationsStyleNormalizer();\n}\n/**\n * @param {?} renderer\n * @param {?} engine\n * @param {?} zone\n * @return {?}\n */\nexport function instantiateRendererFactory(\n    renderer: DomRendererFactory2, engine: AnimationEngine, zone: NgZone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst /** @type {?} */ SHARED_ANIMATION_PROVIDERS: Provider[] = [\n  {provide: AnimationBuilder, useClass: BrowserAnimationBuilder},\n  {provide: AnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer},\n  {provide: AnimationEngine, useClass: InjectableAnimationEngine}, {\n    provide: RendererFactory2,\n    useFactory: instantiateRendererFactory,\n    deps: [DomRendererFactory2, AnimationEngine, NgZone]\n  }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nexport const BROWSER_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useFactory: instantiateSupportedAnimationDriver},\n  ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nexport const BROWSER_NOOP_ANIMATIONS_PROVIDERS: Provider[] =\n    [{provide: AnimationDriver, useClass: NoopAnimationDriver}, ...SHARED_ANIMATION_PROVIDERS];\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTriggerMetadata} from '@angular/animations';\nimport {ɵAnimationEngine as AnimationEngine} from '@angular/animations/browser';\nimport {Injectable, NgZone, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2} from '@angular/core';\nexport class AnimationRendererFactory implements RendererFactory2 {\nprivate _currentId: number = 0;\nprivate _microtaskId: number = 1;\nprivate _animationCallbacksBuffer: [(e: any) => any, any][] = [];\nprivate _rendererCache = new Map<Renderer2, BaseAnimationRenderer>();\n/**\n * @param {?} delegate\n * @param {?} engine\n * @param {?} _zone\n */\nconstructor(\nprivate delegate: RendererFactory2,\nprivate engine: AnimationEngine,\nprivate _zone: NgZone) {\n    engine.onRemovalComplete = (element: any, delegate: Renderer2) => {\n      // Note: if an component element has a leave animation, and the component\n      // a host leave animation, the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      if (delegate && delegate.parentNode(element)) {\n        delegate.removeChild(element.parentNode, element);\n      }\n    };\n  }\n/**\n * @param {?} hostElement\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(hostElement: any, type: RendererType2): Renderer2 {\n    const /** @type {?} */ EMPTY_NAMESPACE_ID = '';\n\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const /** @type {?} */ delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let /** @type {?} */ renderer: BaseAnimationRenderer|undefined = this._rendererCache.get(delegate);\n      if (!renderer) {\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine);\n        // only cache this result when the base renderer is used\n        this._rendererCache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n\n    const /** @type {?} */ componentId = type.id;\n    const /** @type {?} */ namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n\n    this.engine.register(namespaceId, hostElement);\n    const /** @type {?} */ animationTriggers = /** @type {?} */(( type.data['animation'] as AnimationTriggerMetadata[]));\n    animationTriggers.forEach(\n        trigger => this.engine.registerTrigger(\n            componentId, namespaceId, hostElement, trigger.name, trigger));\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n/**\n * @return {?}\n */\nbegin() {\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n/**\n * @return {?}\n */\nprivate _scheduleCountTask() {\n    Zone.current.scheduleMicroTask('incremenet the animation microtask', () => this._microtaskId++);\n  }\n/**\n * @param {?} count\n * @param {?} fn\n * @param {?} data\n * @return {?}\n */\nscheduleListenerCallback(count: number, fn: (e: any) => any, data: any) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n/**\n * @return {?}\n */\nend() {\n    this._zone.runOutsideAngular(() => {\n      this._scheduleCountTask();\n      this.engine.flush(this._microtaskId);\n    });\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this.engine.whenRenderingDone(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n{type: AnimationEngine, },\n{type: NgZone, },\n];\n}\n\nfunction AnimationRendererFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRendererFactory.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAnimationRendererFactory.ctorParameters;\n/** @type {?} */\nAnimationRendererFactory.prototype._currentId;\n/** @type {?} */\nAnimationRendererFactory.prototype._microtaskId;\n/** @type {?} */\nAnimationRendererFactory.prototype._animationCallbacksBuffer;\n/** @type {?} */\nAnimationRendererFactory.prototype._rendererCache;\n/** @type {?} */\nAnimationRendererFactory.prototype.delegate;\n/** @type {?} */\nAnimationRendererFactory.prototype.engine;\n/** @type {?} */\nAnimationRendererFactory.prototype._zone;\n}\n\nexport class BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\n      protected namespaceId: string,\npublic delegate: Renderer2,\npublic engine: AnimationEngine) {\n    this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode !(n) : null;\n  }\n/**\n * @return {?}\n */\nget data() { return this.delegate.data; }\n\n  destroyNode: ((n: any) => void)|null;\n/**\n * @return {?}\n */\ndestroy(): void {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n  }\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string|null|undefined) {\n    return this.delegate.createElement(name, namespace);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string) { return this.delegate.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string) { return this.delegate.createText(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, true);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: any) { return this.delegate.selectRootElement(selectorOrNode); }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any) { return this.delegate.parentNode(node); }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any) { return this.delegate.nextSibling(node); }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string|null|undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string|null|undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { this.delegate.addClass(el, name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { this.delegate.removeClass(el, name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?=} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?=} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    this.delegate.setProperty(el, name, value);\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { this.delegate.setValue(node, value); }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction BaseAnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseAnimationRenderer.prototype.destroyNode;\n/** @type {?} */\nBaseAnimationRenderer.prototype.namespaceId;\n/** @type {?} */\nBaseAnimationRenderer.prototype.delegate;\n/** @type {?} */\nBaseAnimationRenderer.prototype.engine;\n}\n\nexport class AnimationRenderer extends BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} factory\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\npublic factory: AnimationRendererFactory, namespaceId: string, delegate: Renderer2,\n      engine: AnimationEngine) {\n    super(namespaceId, delegate, engine);\n    this.namespaceId = namespaceId;\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == '@') {\n      name = name.substr(1);\n      this.engine.setProperty(this.namespaceId, el, name, value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, eventName: string, callback: (event: any) => any):\n      () => void {\n    if (eventName.charAt(0) == '@') {\n      const /** @type {?} */ element = resolveElementFromTarget(target);\n      let /** @type {?} */ name = eventName.substr(1);\n      let /** @type {?} */ phase = '';\n      if (name.charAt(0) != '@') {  // transition-specific\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const /** @type {?} */ countId = ( /** @type {?} */((event as any)))['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction AnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRenderer.prototype.factory;\n}\n\n/**\n * @param {?} target\n * @return {?}\n */\nfunction resolveElementFromTarget(target: 'window' | 'document' | 'body' | any): any {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\n/**\n * @param {?} triggerName\n * @return {?}\n */\nfunction parseTriggerCallbackName(triggerName: string) {\n  const /** @type {?} */ dotIndex = triggerName.indexOf('.');\n  const /** @type {?} */ trigger = triggerName.substring(0, dotIndex);\n  const /** @type {?} */ phase = triggerName.substr(dotIndex + 1);\n  return [trigger, phase];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationBuilder, AnimationFactory, AnimationMetadata, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, sequence} from '@angular/animations';\nimport {Injectable, RendererFactory2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {AnimationRenderer} from './animation_renderer';\nexport class BrowserAnimationBuilder extends AnimationBuilder {\nprivate _nextAnimationId = 0;\nprivate _renderer: AnimationRenderer;\n/**\n * @param {?} rootRenderer\n */\nconstructor(rootRenderer: RendererFactory2) {\n    super();\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {animation: []}\n    } as RendererType2;\n    this._renderer = rootRenderer.createRenderer(document.body, typeData) as AnimationRenderer;\n  }\n/**\n * @param {?} animation\n * @return {?}\n */\nbuild(animation: AnimationMetadata|AnimationMetadata[]): AnimationFactory {\n    const /** @type {?} */ id = this._nextAnimationId.toString();\n    this._nextAnimationId++;\n    const /** @type {?} */ entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n];\n}\n\nfunction BrowserAnimationBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationBuilder.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationBuilder.ctorParameters;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._nextAnimationId;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._renderer;\n}\n\nexport class BrowserAnimationFactory extends AnimationFactory {\n/**\n * @param {?} _id\n * @param {?} _renderer\n */\nconstructor(private _id: string,\nprivate _renderer: AnimationRenderer) { super(); }\n/**\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(element: any, options?: AnimationOptions): AnimationPlayer {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\n\nfunction BrowserAnimationFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationFactory.prototype._id;\n/** @type {?} */\nBrowserAnimationFactory.prototype._renderer;\n}\n\nexport class RendererAnimationPlayer implements AnimationPlayer {\npublic parentPlayer: AnimationPlayer|null = null;\nprivate _started = false;\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} options\n * @param {?} _renderer\n */\nconstructor(\npublic id: string,\npublic element: any, options: AnimationOptions,\nprivate _renderer: AnimationRenderer) {\n    this._command('create', options);\n  }\n/**\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nprivate _listen(eventName: string, callback: (event: any) => any): () => void {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n/**\n * @param {?} command\n * @param {...?} args\n * @return {?}\n */\nprivate _command(command: string, ...args: any[]) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._listen('done', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._listen('start', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._listen('destroy', fn); }\n/**\n * @return {?}\n */\ninit(): void { this._command('init'); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\nplay(): void {\n    this._command('play');\n    this._started = true;\n  }\n/**\n * @return {?}\n */\npause(): void { this._command('pause'); }\n/**\n * @return {?}\n */\nrestart(): void { this._command('restart'); }\n/**\n * @return {?}\n */\nfinish(): void { this._command('finish'); }\n/**\n * @return {?}\n */\ndestroy(): void { this._command('destroy'); }\n/**\n * @return {?}\n */\nreset(): void { this._command('reset'); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._command('setPosition', p); }\n/**\n * @return {?}\n */\ngetPosition(): number { return 0; }\npublic totalTime = 0;\n}\n\nfunction RendererAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nRendererAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nRendererAnimationPlayer.prototype._started;\n/** @type {?} */\nRendererAnimationPlayer.prototype.totalTime;\n/** @type {?} */\nRendererAnimationPlayer.prototype.id;\n/** @type {?} */\nRendererAnimationPlayer.prototype.element;\n/** @type {?} */\nRendererAnimationPlayer.prototype._renderer;\n}\n\n/**\n * @param {?} renderer\n * @param {?} element\n * @param {?} id\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\nfunction issueAnimationCommand(\n    renderer: AnimationRenderer, element: any, id: string, command: string, args: any[]): any {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["NoopAnimationDriver", "DomRendererFactory2", "AnimationEngine", "AnimationStyleNormalizer", "WebAnimationsStyleNormalizer", "WebAnimationsDriver", "supportsWebAnimations"], "mappings": ";;;;;AOAA;;;;;;;AAQA,AACA,AAGA,AAAA,MAAA,uBACC,SAAA,gBAAA,CADD;;;;IAMA,WAAA,CADG,YAAyB,EAC5B;QACI,KAAK,EAAE,CAAC;QALF,IAAV,CAAA,gBAAU,GAAmB,CAAA,CAAE;QAM3B,MAAM,QAAQ,GAAG;YACf,EAAE,EAAE,GAAG;YACP,aAAa,EAAE,iBAAiB,CAAC,IAAI;YACrC,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,EAAC,SAAS,EAAE,EAAE,EAAC;SACL,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAsB,CAAC;KAC5F;;;;;IAKH,KAJG,CAAA,SAAA,EAIH;QACI,uBAJM,EAAA,GAAK,IAAA,CAAK,gBAAC,CAAgB,QAAC,EAAQ,CAAE;QAK5C,IAAI,CAJC,gBAAC,EAAgB,CAAE;QAKxB,uBAJM,KAAA,GAAQ,KAAA,CAAM,OAAC,CAAO,SAAC,CAAS,GAAG,QAAA,CAAS,SAAC,CAAS,GAAG,SAAA,CAAU;QAKzE,qBAAqB,CAJC,IAAC,CAAI,SAAC,EAAU,IAAA,EAAM,EAAA,EAAI,UAAA,EAAY,CAAA,KAAE,CAAK,CAAC,CAAC;QAKrE,OAJO,IAAI,uBAAA,CAAwB,EAAC,EAAG,IAAA,CAAK,SAAC,CAAS,CAAC;KAKxD;;AAHI,uBAAP,CAAA,UAAO,GAAoC;IAK3C,EAJE,IAAA,EAAM,UAAA,EAAW;CAKlB,CAJC;;;;AAED,uBAAD,CAAA,cAAC,GAAA,MAAA;IAOD,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;AAGF,AAcA,AAAA,MAAA,uBApBC,SAAA,gBAAA,CAoBD;;;;;IAKA,WAAA,CAxBsB,GAAK,EAAgB,SAAW,EAwBtD;QAxBwE,KAAA,EAAA,CAAA;QAAlD,IAAtB,CAAA,GAAsB,GAAA,GAAA,CAAK;QAAgB,IAA3C,CAAA,SAA2C,GAAA,SAAA,CAAW;KAAkB;;;;;;IA+BxE,MA7BG,CAAA,OAAA,EAAA,OAAA,EA6BH;QACI,OA7BO,IAAI,uBAAA,CAAwB,IAAC,CAAI,GAAC,EAAI,OAAA,EAAS,OAAA,IAAW,EAAA,EAAI,IAAA,CAAK,SAAC,CAAS,CAAC;KA8BtF;CACF;AAED,AAOA,AAAA,MAAA,uBAAA,CAAA;;;;;;;IASA,WAAA,CAxCa,EAAI,EAAe,OAAS,EAAK,OAAS,EACzC,SAAW,EAuCzB;QAxCa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAAe,IAAhC,CAAA,OAAgC,GAAA,OAAA,CAAS;QAC3B,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;QALhB,IAAT,CAAA,YAAS,GAAqC,IAAA,CAAK;QACzC,IAAV,CAAA,QAAU,GAAW,KAAA,CAAM;QA6ClB,IAAT,CAAA,SAAS,GAAY,CAAA,CAAE;QAEnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KAClC;;;;;;IAxCA,OAAA,CAAA,SAAA,EAAA,QAAA,EAAH;QA+CI,OA9CO,IAAA,CAAK,SAAC,CAAS,MAAC,CAAM,IAAC,CAAI,OAAC,EAAQ,CA8C/C,EAAA,EA9C+C,IAAM,CAAI,EAAC,CA8C1D,CAAA,EA9C4D,SAAI,CA8ChE,CA9CyE,EAAG,QAAA,CAAS,CAAC;KA+CnF;;;;;;IA5CA,QAAA,CAAA,OAAA,EAAA,GAAA,IAAA,EAAH;QAmDI,OAlDO,qBAAA,CAAsB,IAAC,CAAI,SAAC,EAAU,IAAA,CAAK,OAAC,EAAQ,IAAA,CAAK,EAAC,EAAG,OAAA,EAAS,IAAA,CAAK,CAAC;KAmDpF;;;;;IAKH,MArDG,CAAA,EAAA,EAqDH,EArDiC,IAAA,CAAK,OAAC,CAAO,MAAC,EAAO,EAAA,CAAG,CAAC,EAAC;;;;;IA0D3D,OAxDG,CAAA,EAAA,EAwDH,EAxDkC,IAAA,CAAK,OAAC,CAAO,OAAC,EAAQ,EAAA,CAAG,CAAC,EAAC;;;;;IA6D7D,SA3DG,CAAA,EAAA,EA2DH,EA3DoC,IAAA,CAAK,OAAC,CAAO,SAAC,EAAU,EAAA,CAAG,CAAC,EAAC;;;;IA+DjE,IA7DG,GA6DH,EA7DiB,IAAA,CAAK,QAAC,CAAQ,MAAC,CAAM,CAAC,EAAC;;;;IAiExC,UA/DG,GA+DH,EA/D0B,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;IAmEhD,IAjEG,GAiEH;QACI,IAAI,CAjEC,QAAC,CAAQ,MAAC,CAAM,CAAC;QAkEtB,IAAI,CAjEC,QAAC,GAAU,IAAA,CAAK;KAkEtB;;;;IAIH,KAnEG,GAmEH,EAnEkB,IAAA,CAAK,QAAC,CAAQ,OAAC,CAAO,CAAC,EAAC;;;;IAuE1C,OArEG,GAqEH,EArEoB,IAAA,CAAK,QAAC,CAAQ,SAAC,CAAS,CAAC,EAAC;;;;IAyE9C,MAvEG,GAuEH,EAvEmB,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ,CAAC,EAAC;;;;IA2E5C,OAzEG,GAyEH,EAzEoB,IAAA,CAAK,QAAC,CAAQ,SAAC,CAAS,CAAC,EAAC;;;;IA6E9C,KA3EG,GA2EH,EA3EkB,IAAA,CAAK,QAAC,CAAQ,OAAC,CAAO,CAAC,EAAC;;;;;IAgF1C,WA9EG,CAAA,CAAA,EA8EH,EA9EiC,IAAA,CAAK,QAAC,CAAQ,aAAC,EAAc,CAAA,CAAE,CAAC,EAAC;;;;IAkFlE,WAhFG,GAgFH,EAhF0B,OAAO,CAAA,CAAE,EAAC;CAkFnC;AAED,AAeA;;;;;;;;AAQA,SAAA,qBAAA,CACI,QAA2B,EAAE,OAAY,EAAE,EAAU,EAAE,OAAe,EAAE,IAAW,EADvF;IAEE,OA7GO,QAAA,CAAS,WAAC,CAAW,OAAC,EAAQ,CA6GvC,EAAA,EA7GuC,EAAM,CA6G7C,CAAA,EA7G+C,OAAI,CA6GnD,CA7G0D,EAAG,IAAA,CAAK,CAAC;CA8GlE;;AD/MD;;;;;;;AASA,AACA,AACA,AAAA,MAAA,wBAAA,CAAA;;;;;;IAUA,WAAA,CAFc,QAAU,EAA0B,MAAQ,EAAyB,KAAO,EAE1F;QAFc,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QAA0B,IAAlD,CAAA,MAAkD,GAAA,MAAA,CAAQ;QAAyB,IAAnF,CAAA,KAAmF,GAAA,KAAA,CAAO;QANhF,IAAV,CAAA,UAAU,GAAqB,CAAA,CAAE;QACvB,IAAV,CAAA,YAAU,GAAuB,CAAA,CAAE;QACzB,IAAV,CAAA,yBAAU,GAAsD,EAAA,CAAG;QACzD,IAAV,CAAA,cAAU,GAAiB,IAAI,GAAA,EAAqC,CAAG;QASnE,MAAM,CAAC,iBAAiB,GAAG,CAAC,OAAY,EAAE,QAAmB,KAAjE;;;;;YAKM,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC5C,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;aACnD;SACF,CAAC;KACH;;;;;;IAMH,cATG,CAAA,WAAA,EAAA,IAAA,EASH;QACI,uBATM,kBAAA,GAAqB,EAAA,CAAG;;;QAa9B,uBATM,QAAA,GAAW,IAAA,CAAK,QAAC,CAAQ,cAAC,CAAc,WAAC,EAAY,IAAA,CAAK,CAAC;QAUjE,IAAI,CATC,WAAC,IAAc,CAAA,IAAE,IAAO,CAAA,IAAE,CAAI,IAAC,IAAO,CAAA,IAAE,CAAI,IAAC,CAAI,WAAC,CAAW,EAAE;YAUlE,qBATI,QAAA,GAA4C,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,QAAC,CAAQ,CAAC;YAUlF,IAAI,CATC,QAAC,EAAS;gBAUb,QAAQ,GATG,IAAI,qBAAA,CAAsB,kBAAC,EAAmB,QAAA,EAAU,IAAA,CAAK,MAAC,CAAM,CAAC;;gBAWhF,IAAI,CATC,cAAC,CAAc,GAAC,CAAG,QAAC,EAAS,QAAA,CAAS,CAAC;aAU7C;YACD,OATO,QAAA,CAAS;SAUjB;QAED,uBATM,WAAA,GAAc,IAAA,CAAK,EAAC,CAAE;QAU5B,uBATM,WAAA,GAAc,IAAA,CAAK,EAAC,GAAI,GAAA,GAAM,IAAA,CAAK,UAAC,CAAU;QAUpD,IAAI,CATC,UAAC,EAAU,CAAE;QAWlB,IAAI,CATC,MAAC,CAAM,QAAC,CAAQ,WAAC,EAAY,WAAA,CAAY,CAAC;QAU/C,uBATM,iBAAA,IAAoB,IAAA,CAAK,IAAC,CAAI,WAAC,CAAwC,CAAA,CAAE;QAU/E,iBAAiB,CATC,OAAC,CAUf,OAAO,IATI,IAAA,CAAK,MAAC,CAAM,eAAC,CAUpB,WAAW,EATE,WAAA,EAAa,WAAA,EAAa,OAAA,CAAQ,IAAC,EAAK,OAAA,CAAQ,CAAC,CAAC;QAUvE,OATO,IAAI,iBAAA,CAAkB,IAAC,EAAK,WAAA,EAAa,QAAA,EAAU,IAAA,CAAK,MAAC,CAAM,CAAC;KAUxE;;;;IAIH,KAXG,GAWH;QACI,IAAI,IAXC,CAAI,QAAC,CAAQ,KAAC,EAAM;YAYvB,IAAI,CAXC,QAAC,CAAQ,KAAC,EAAK,CAAE;SAYvB;KACF;;;;IATA,kBAAA,GAAH;QAcI,IAAI,CAbC,OAAC,CAAO,iBAAC,CAAiB,oCAAC,EAAqC,MAAM,IAAA,CAAK,YAAC,EAAY,CAAE,CAAC;KAcjG;;;;;;;IAOH,wBAjBG,CAAA,KAAA,EAAA,EAAA,EAAA,IAAA,EAiBH;QACI,IAAI,KAjBC,IAAQ,CAAA,IAAK,KAAA,GAAQ,IAAA,CAAK,YAAC,EAAa;YAkB3C,IAAI,CAjBC,KAAC,CAAK,GAAC,CAAG,MAAM,EAAA,CAAG,IAAC,CAAI,CAAC,CAAC;YAkB/B,OAAO;SACR;QAED,IAAI,IAjBC,CAAI,yBAAC,CAAyB,MAAC,IAAS,CAAA,EAAG;YAkB9C,OAAO,CAjBC,OAAC,CAAO,IAAC,CAAI,CAAC,IAAC,CAAI,MAiBjC;gBACQ,IAAI,CAjBC,KAAC,CAAK,GAAC,CAAG,MAiBvB;oBACU,IAAI,CAjBC,yBAAC,CAAyB,OAAC,CAAO,KAAC,IAiBlD;wBACY,MAjBM,CAAA,EAAE,EAAG,IAAA,CAAK,GAAG,KAAA,CAAM;wBAkBzB,EAAE,CAjBC,IAAC,CAAI,CAAC;qBAkBV,CAjBC,CAAC;oBAkBH,IAAI,CAjBC,yBAAC,GAA2B,EAAA,CAAG;iBAkBrC,CAjBC,CAAC;aAkBJ,CAjBC,CAAC;SAkBJ;QAED,IAAI,CAjBC,yBAAC,CAAyB,IAAC,CAAI,CAAC,EAAC,EAAG,IAAA,CAAK,CAAC,CAAC;KAkBjD;;;;IAIH,GAnBG,GAmBH;QACI,IAAI,CAnBC,KAAC,CAAK,iBAAC,CAAiB,MAmBjC;YACM,IAAI,CAnBC,kBAAC,EAAkB,CAAE;YAoB1B,IAAI,CAnBC,MAAC,CAAM,KAAC,CAAK,IAAC,CAAI,YAAC,CAAY,CAAC;SAoBtC,CAnBC,CAAC;QAoBH,IAAI,IAnBC,CAAI,QAAC,CAAQ,GAAC,EAAI;YAoBrB,IAAI,CAnBC,QAAC,CAAQ,GAAC,EAAG,CAAE;SAoBrB;KACF;;;;IAIH,iBArBG,GAqBH,EArBsC,OAAO,IAAA,CAAK,MAAC,CAAM,iBAAC,EAAiB,CAAE,EAAC;;AACvE,wBAAP,CAAA,UAAO,GAAoC;IAsB3C,EArBE,IAAA,EAAM,UAAA,EAAW;CAsBlB,CArBC;;;;AAED,wBAAD,CAAA,cAAC,GAAA,MAAA;IAwBD,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAEE,gBAAe,GAAG;IACzB,EAAC,IAAI,EAAE,MAAM,GAAG;CACf,CAAC;AAGF,AAwBA,AAAA,MAAA,qBAAA,CAAA;;;;;;IAMA,WAAA,CAnDgB,WAAa,EAAe,QAAU,EAAkB,MAAQ,EAmDhF;QAnDgB,IAAhB,CAAA,WAAgB,GAAA,WAAA,CAAa;QAAe,IAA5C,CAAA,QAA4C,GAAA,QAAA,CAAU;QAAkB,IAAxE,CAAA,MAAwE,GAAA,MAAA,CAAQ;QAuD5E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACxF;;;;IAIH,IAxDG,IAAA,GAwDH,EAxDe,OAAO,IAAA,CAAK,QAAC,CAAQ,IAAC,CAAI,EAAC;;;;IA8D1C,OA1DG,GA0DH;QACI,IAAI,CA1DC,MAAC,CAAM,OAAC,CAAO,IAAC,CAAI,WAAC,EAAY,IAAA,CAAK,QAAC,CAAQ,CAAC;QA2DrD,IAAI,CA1DC,QAAC,CAAQ,OAAC,EAAO,CAAE;KA2DzB;;;;;;IAMH,aA9DG,CAAA,IAAA,EAAA,SAAA,EA8DH;QACI,OA9DO,IAAA,CAAK,QAAC,CAAQ,aAAC,CAAa,IAAC,EAAK,SAAA,CAAU,CAAC;KA+DrD;;;;;IAKH,aAjEG,CAAA,KAAA,EAiEH,EAjEiC,OAAO,IAAA,CAAK,QAAC,CAAQ,aAAC,CAAa,KAAC,CAAK,CAAC,EAAC;;;;;IAsE5E,UApEG,CAAA,KAAA,EAoEH,EApE8B,OAAO,IAAA,CAAK,QAAC,CAAQ,UAAC,CAAU,KAAC,CAAK,CAAC,EAAC;;;;;;IA0EtE,WAxEG,CAAA,MAAA,EAAA,QAAA,EAwEH;QACI,IAAI,CAxEC,QAAC,CAAQ,WAAC,CAAW,MAAC,EAAO,QAAA,CAAS,CAAC;QAyE5C,IAAI,CAxEC,MAAC,CAAM,QAAC,CAAQ,IAAC,CAAI,WAAC,EAAY,QAAA,EAAU,MAAA,EAAQ,KAAA,CAAM,CAAC;KAyEjE;;;;;;;IAOH,YA7EG,CAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EA6EH;QACI,IAAI,CA7EC,QAAC,CAAQ,YAAC,CAAY,MAAC,EAAO,QAAA,EAAU,QAAA,CAAS,CAAC;QAqEzD,IAAA,CApEO,MAAC,CAAM,QAAC,CAAQ,IAAC,CAAI,WAAC,EAAY,QAAA,EAAU,MAAA,EAAQ,IAAA,CAAK,CAAC;KA8EhE;;;;;;IAMH,WAjFG,CAAA,MAAA,EAAA,QAAA,EAiFH;QACI,IAAI,CAjFC,MAAC,CAAM,QAAC,CAAQ,IAAC,CAAI,WAAC,EAAY,QAAA,EAAU,IAAA,CAAK,QAAC,CAAQ,CAAC;KAkFjE;;;;;IAKH,iBApFG,CAAA,cAAA,EAoFH,EApF2C,OAAO,IAAA,CAAK,QAAC,CAAQ,iBAAC,CAAiB,cAAC,CAAc,CAAC,EAAC;;;;;IAyFnG,UAvFG,CAAA,IAAA,EAuFH,EAvF0B,OAAO,IAAA,CAAK,QAAC,CAAQ,UAAC,CAAU,IAAC,CAAI,CAAC,EAAC;;;;;IA4FjE,WA1FG,CAAA,IAAA,EA0FH,EA1F2B,OAAO,IAAA,CAAK,QAAC,CAAQ,WAAC,CAAW,IAAC,CAAI,CAAC,EAAC;;;;;;;;IAkGnE,YAhGG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,SAAA,EAgGH;QACI,IAAI,CAhGC,QAAC,CAAQ,YAAC,CAAY,EAAC,EAAG,IAAA,EAAM,KAAA,EAAO,SAAA,CAAU,CAAC;KAiGxD;;;;;;;IAOH,eArGG,CAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EAqGH;QACI,IAAI,CArGC,QAAC,CAAQ,eAAC,CAAe,EAAC,EAAG,IAAA,EAAM,SAAA,CAAU,CAAC;KAsGpD;;;;;;IAMH,QAzGG,CAAA,EAAA,EAAA,IAAA,EAyGH,EAzG0C,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ,EAAC,EAAG,IAAA,CAAK,CAAC,EAAC;;;;;;IA+G5E,WA7GG,CAAA,EAAA,EAAA,IAAA,EA6GH,EA7G6C,IAAA,CAAK,QAAC,CAAQ,WAAC,CAAW,EAAC,EAAG,IAAA,CAAK,CAAC,EAAC;;;;;;;;IAqHlF,QAnHG,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,EAmHH;QACI,IAAI,CAnHC,QAAC,CAAQ,QAAC,CAAQ,EAAC,EAAG,KAAA,EAAO,KAAA,EAAO,KAAA,CAAM,CAAC;KAoHjD;;;;;;;IAOH,WAxHG,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA,EAwHH;QACI,IAAI,CAxHC,QAAC,CAAQ,WAAC,CAAW,EAAC,EAAG,KAAA,EAAO,KAAA,CAAM,CAAC;KAyH7C;;;;;;;IAOH,WA7HG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EA6HH;QACI,IAAI,CA7HC,QAAC,CAAQ,WAAC,CAAW,EAAC,EAAG,IAAA,EAAM,KAAA,CAAM,CAAC;KA8H5C;;;;;;IAMH,QAjIG,CAAA,IAAA,EAAA,KAAA,EAiIH,EAjI6C,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ,IAAC,EAAK,KAAA,CAAM,CAAC,EAAC;;;;;;;IAwIlF,MAtIG,CAAA,MAAA,EAAA,SAAA,EAAA,QAAA,EAsIH;QACI,OAtIO,IAAA,CAAK,QAAC,CAAQ,MAAC,CAAM,MAAC,EAAO,SAAA,EAAW,QAAA,CAAS,CAAC;KAuI1D;CACF;AAED,AAWA,AAAA,MAAA,iBAjJC,SAAA,qBAAA,CAiJD;;;;;;;IAOA,WAAA,CAtJa,OAAS,EAA0B,WAAa,EAAQ,QAAU,EAwJzE,MAvJQ,EAqJd;QAGI,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAzJ5B,IAAb,CAAA,OAAa,GAAA,OAAA,CAAS;QA0JlB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAChC;;;;;;;IAOH,WA5JG,CAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EA4JH;QACI,IAAI,IA5JC,CAAI,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,EAAK;YA6JzB,IAAI,GA5JG,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC,CAAC;YA6JtB,IAAI,CA5JC,MAAC,CAAM,WAAC,CAAW,IAAC,CAAI,WAAC,EAAY,EAAA,EAAI,IAAA,EAAM,KAAA,CAAM,CAAC;SA6J5D;aA5JM;YA6JL,IAAI,CA5JC,QAAC,CAAQ,WAAC,CAAW,EAAC,EAAG,IAAA,EAAM,KAAA,CAAM,CAAC;SA6J5C;KACF;;;;;;;IAOH,MAjKG,CAAA,MAAA,EAAA,SAAA,EAAA,QAAA,EAiKH;QAEI,IAAI,SAjKC,CAAS,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,EAAK;YAkK9B,uBAjKM,OAAA,GAAU,wBAAA,CAAyB,MAAC,CAAM,CAAC;YAkKjD,qBAjKI,IAAA,GAAO,SAAA,CAAU,MAAC,CAAM,CAAC,CAAC,CAAC;YAkK/B,qBAjKI,KAAA,GAAQ,EAAA,CAAG;YAkKf,IAAI,IAjKC,CAAI,MAAC,CAAM,CAAC,CAAC,IAAI,GAAA,EAAK;gBAkKzB,CAAC,IAjKC,EAAK,KAAA,CAAM,GAAG,wBAAA,CAAyB,IAAC,CAAI,CAAC;aAkKhD;YACD,OAjKO,IAAA,CAAK,MAAC,CAAM,MAAC,CAAM,IAAC,CAAI,WAAC,EAAY,OAAA,EAAS,IAAA,EAAM,KAAA,EAAO,KAAA,IAiKxE;gBACQ,uBAjKM,OAAA,GAAU,EAAA,KAAU,GAAK,OAAC,CAAO,IAAI,CAAA,CAAE,CAAC;gBAkK9C,IAAI,CAjKC,OAAC,CAAO,wBAAC,CAAwB,OAAC,EAAQ,QAAA,EAAU,KAAA,CAAM,CAAC;aAkKjE,CAjKC,CAAC;SAkKJ;QACD,OAjKO,IAAA,CAAK,QAAC,CAAQ,MAAC,CAAM,MAAC,EAAO,SAAA,EAAW,QAAA,CAAS,CAAC;KAkK1D;CACF;AAED,AAKA;;;;AAIA,SAAA,wBAAA,CA1KC,MAAA,EA0KD;IACE,QAAQ,MAnLC;QAoLP,KAnLK,MAAA;YAoLH,OAnLO,QAAA,CAAS,IAAC,CAAI;QAoLvB,KAnLK,UAAA;YAoLH,OAnLO,QAAA,CAAS;QAoLlB,KAnLK,QAAA;YAoLH,OAnLO,MAAA,CAAO;QAoLhB;YACE,OAnLO,MAAA,CAAO;KAoLjB;CACF;;;;;AAKD,SAAA,wBAAA,CA7KC,WAAA,EA6KD;IACE,uBAtLM,QAAA,GAAW,WAAA,CAAY,OAAC,CAAO,GAAC,CAAG,CAAC;IAuL1C,uBAtLM,OAAA,GAAU,WAAA,CAAY,SAAC,CAAS,CAAC,EAAE,QAAA,CAAS,CAAC;IAuLnD,uBAtLM,KAAA,GAAQ,WAAA,CAAY,MAAC,CAAM,QAAC,GAAU,CAAA,CAAE,CAAC;IAuL/C,OAtLO,CAAA,OAAE,EAAQ,KAAA,CAAM,CAAC;CAuLzB;;AD7ZD;;;;;;;AASA,AACA,AACA,AACA,AAEA,AACA,AACA,AAAA,MAAA,yBACC,SAAAA,gBAAA,CADD;;;;;IAKA,WAAA,CAHG,MAAmB,EAAiB,UAAY,EAGnD;QACI,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;KAC3B;;AAFI,yBAAP,CAAA,UAAO,GAAoC;IAI3C,EAHE,IAAA,EAAM,UAAA,EAAW;CAIlB,CAHC;;;;AAED,yBAAD,CAAA,cAAC,GAAA,MAAA;IAMD,EAAC,IAAI,EARE,eAAS,GAAA;IAShB,EAAC,IAAI,EAREC,yBAAwB,GAAE;CAShC,CAAC;AAGF,AAUA;;;AAGA,AAAA,SAAA,mCAAA,GAAA;IACE,IAREG,sBAlBG,EAAqB,EAAG;QA2B3B,OA1BO,IAAID,oBAAA,EAAoB,CAAE;KA2BlC;IACD,OA1BO,IAAIL,oBAAA,EAAoB,CAAE;CA2BlC;;;;AAID,AAAA,SAAA,iCAAA,GAAA;IACE,OA5BO,IAAII,6BAAA,EAA6B,CAAE;CA6B3C;;;;;;;AAOD,AAAA,SAAA,0BAAA,CACI,QAA6B,EAAE,MAAuB,EAAE,IAAY,EADxE;IAEE,OAjCO,IAAI,wBAAA,CAAyB,QAAC,EAAS,MAAA,EAAQ,IAAA,CAAK,CAAC;CAkC7D;AAED,MAjCM,0BAAA,GAAyC;IAkC7C,EAAC,OAjCC,EAAQ,gBAAA,EAAkB,QAAA,EAAU,uBAAA,EAAwB;IAkC9D,EAAC,OAjCC,EAAQD,yBAAA,EAA0B,UAAA,EAAY,iCAAA,EAAkC;IAkClF,EAAC,OAjCC,EAAQD,gBAAA,EAAiB,QAAA,EAAU,yBAAA,EAA0B,EAAE;QAkC/D,OAAO,EAjCE,gBAAA;QAkCT,UAAU,EAjCE,0BAAA;QAkCZ,IAAI,EAjCE,CAAAD,oBAAE,EAAoBC,gBAAA,EAAiB,MAAA,CAAO;KAkCrD;CACF,CAjCC;;;;;AAsCF,AAxBC,MAAA,4BAAA,GAAA;IAyBC,EAAC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,mCAAmC,EAAC;IAC3E,GAAG,0BAA0B;CAC9B,CAAC;;;;;AAKF,AAvBC,MAAA,iCAAA,GAwBG,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAEF,oBAAmB,EAAC,EAAE,GAAG,0BAA0B,CAAC,CAAC;;AD9F/F;;;;;;;AAQA,AACA,AAEA,AACA;;;AAGA,AAAA,MAAA,uBAAA,CAAA;;AAEO,uBAAP,CAAA,UAAO,GAAoC;IAA3C,EACE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAAtB,OAAO,EACE,CAAA,aAAE,CAAa;gBAAxB,SAAS,EACE,4BAAA;aAAZ,EACC,EAAG;CAAJ,CACC;;;;AAED,uBAAD,CAAA,cAAC,GAAA,MAAA,EAEA,CAAC;AAGF,AAUA;;;AAGA,AAAA,MAAA,oBAAA,CAAA;;AATO,oBAAP,CAAA,UAAO,GAAoC;IAW3C,EAVE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA;gBAWtB,OAAO,EAVE,CAAA,aAAE,CAAa;gBAWxB,SAAS,EAVE,iCAAA;aAWZ,EAVC,EAAG;CAWJ,CAVC;;;;AAED,oBAAD,CAAA,cAAC,GAAA,MAAA,EAaA,CAAC,AAGF,AAQC;;ADhED;;;;;;GAMG,AACH,AACA,AAAuG;;ADRvG;;;;;;;;;;;GAYG,AACH,AAEA,AAAgI;;ADfhI;;;;;;;;;;;GAYG,AACH,AAA6K;;ADb7K;;GAEG,AAEH,AAEA,AACA,AAAsO;;"}