[{"__symbolic": "module", "version": 3, "metadata": {"CssSelector": {"__symbolic": "class", "members": {"isElementSelector": [{"__symbolic": "method"}], "hasElementSelector": [{"__symbolic": "method"}], "setElement": [{"__symbolic": "method"}], "getMatchingElementTemplate": [{"__symbolic": "method"}], "addAttribute": [{"__symbolic": "method"}], "addClassName": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "SelectorMatcher": {"__symbolic": "class", "members": {"addSelectables": [{"__symbolic": "method"}], "_addSelectable": [{"__symbolic": "method"}], "_addTerminal": [{"__symbolic": "method"}], "_addPartial": [{"__symbolic": "method"}], "match": [{"__symbolic": "method"}], "_matchTerminal": [{"__symbolic": "method"}], "_matchPartial": [{"__symbolic": "method"}]}}, "SelectorListContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CssSelector"}]}]}]}}, "SelectorContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CssSelector"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "SelectorListContext"}]}], "finalize": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"CssSelector": {"__symbolic": "class", "members": {"isElementSelector": [{"__symbolic": "method"}], "hasElementSelector": [{"__symbolic": "method"}], "setElement": [{"__symbolic": "method"}], "getMatchingElementTemplate": [{"__symbolic": "method"}], "addAttribute": [{"__symbolic": "method"}], "addClassName": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "SelectorMatcher": {"__symbolic": "class", "members": {"addSelectables": [{"__symbolic": "method"}], "_addSelectable": [{"__symbolic": "method"}], "_addTerminal": [{"__symbolic": "method"}], "_addPartial": [{"__symbolic": "method"}], "match": [{"__symbolic": "method"}], "_matchTerminal": [{"__symbolic": "method"}], "_matchPartial": [{"__symbolic": "method"}]}}, "SelectorListContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CssSelector"}]}]}]}}, "SelectorContext": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CssSelector"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "SelectorListContext"}]}], "finalize": [{"__symbolic": "method"}]}}}}]