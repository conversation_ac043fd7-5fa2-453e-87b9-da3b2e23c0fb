{"_args": [["alphanum-sort@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "alphanum-sort@1.0.2", "_id": "alphanum-sort@1.0.2", "_inBundle": false, "_integrity": "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=", "_location": "/alphanum-sort", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "alphanum-sort@1.0.2", "name": "alphanum-sort", "escapedName": "alphanum-sort", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/postcss-minify-params", "/postcss-minify-selectors", "/postcss-unique-selectors"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/alphanum-sort/-/alphanum-sort-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/TrySound/alphanum-sort/issues"}, "description": "Alphanumeric sorting algorithm", "devDependencies": {"eslint": "^1.5.1", "javascript-natural-sort": "^0.7.1", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "files": ["lib"], "homepage": "https://github.com/TrySound/alphanum-sort", "keywords": ["sort", "alphanum", "alphanumeric", "natural", "human"], "license": "MIT", "main": "lib/index.js", "name": "alphanum-sort", "repository": {"type": "git", "url": "git+https://github.com/TrySound/alphanum-sort.git"}, "scripts": {"test": "eslint lib test.js && tape test.js | tap-spec"}, "version": "1.0.2"}