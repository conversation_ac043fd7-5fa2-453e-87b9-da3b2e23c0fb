{"_args": [["bytes@2.3.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "bytes@2.3.0", "_id": "bytes@2.3.0", "_inBundle": false, "_integrity": "sha1-1baAoWW2IBc5rLYRVCqrwtjOsHA=", "_location": "/bytes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bytes@2.3.0", "name": "bytes", "escapedName": "bytes", "rawSpec": "2.3.0", "saveSpec": null, "fetchSpec": "2.3.0"}, "_requiredBy": ["/compression"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/bytes/-/bytes-2.3.0.tgz", "_spec": "2.3.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Utility to parse a string bytes to bytes and vice-versa", "devDependencies": {"mocha": "1.21.5"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "homepage": "https://github.com/visionmedia/bytes.js#readme", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "license": "MIT", "name": "bytes", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "scripts": {"test": "mocha --check-leaks --reporter spec"}, "version": "2.3.0"}