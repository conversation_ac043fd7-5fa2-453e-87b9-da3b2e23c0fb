{"title": "Toolbar/context menu", "description": "Method of defining a toolbar menu, a context menu or a list of (interactive) options using the <menu> element.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#the-menu-element", "status": "ls", "links": [{"url": "https://bug617528.bugzilla.mozilla.org/attachment.cgi?id=554309", "title": "Demo"}, {"url": "https://github.com/swisnl/jQuery-contextMenu", "title": "jQuery polyfill"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/events.js#event-contextmenu", "title": "has.js test"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=746087", "title": "Bug on Firefox support"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6508881-html5-menu-tag-contextmenu-attribute", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n d #3", "42": "n d #3", "43": "n d #3", "44": "n d #3", "45": "n d #3", "46": "n d #3", "47": "n d #3", "48": "n d #2", "49": "n d #2", "50": "n d #2", "51": "n d #2", "52": "n d #5", "53": "n d #5", "54": "n d #5", "55": "n d #5", "56": "n d #5", "57": "n d #5", "58": "n d #5", "59": "n d #5", "60": "n d #5", "61": "n d #5", "62": "n d #5"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n d #2", "36": "n d #2", "37": "n d #2", "38": "n d #2", "39": "n d #5", "40": "n d #5", "41": "n d #5", "42": "n d #5", "43": "n d #5", "44": "n d #5", "45": "n d #5", "46": "n d #5", "47": "n d #5", "48": "n d #5"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "n"}, "and_ff": {"54": "a #1 #4"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n d #5"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Partial support in Firefox refers to being limited to context menus, not toolbar menus.", "2": "Context menus supported in Chromium under the \"Experimental Web Platform features\" flag", "3": "Context menus with a non-compliant syntax (`type=popup`) supported in Chromium under the \"Experimental Web Platform features\" flag", "4": "Nested menus are not supported", "5": "Context menus supported in Chromium with the \"enable-blink-features=ContextMenu\" command line flag"}, "usage_perc_y": 0, "usage_perc_a": 5.86, "ucprefix": false, "parent": "", "keywords": "contextmenu,menuitem,command", "ie_id": "menuelement", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}