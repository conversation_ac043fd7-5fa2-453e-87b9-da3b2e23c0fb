{"title": "CSS box-decoration-break", "description": "Controls whether the box's margins, borders, padding, and other decorations wrap the broken edges of the box fragments (when the box is split by a break (page/column/region/line).", "spec": "http://www.w3.org/TR/css3-break/#break-decoration", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/box-decoration-break", "title": "Mozilla Developer Network (MDN) documentation - CSS box-decoration-break"}, {"url": "http://jsbin.com/xojoro/edit?css,output", "title": "Demo of effect on box border"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6514472-box-decoration-break", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "a x #1", "7": "a x #1", "7.1": "a x #1", "8": "a x #1", "9": "a x #1", "9.1": "a x #1", "10": "a x #1", "10.1": "a x #1", "11": "a x #1", "TP": "a x #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "y #1", "11.1": "y #1", "11.5": "y #1", "11.6": "y #1", "12": "y #1", "12.1": "y #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "a x #1", "8": "a x #1", "8.1-8.4": "a x #1", "9.0-9.2": "a x #1", "9.3": "a x #1", "10.0-10.2": "a x #1", "10.3": "a x #1", "11": "a x #1"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a x #1", "4.4.3-4.4.4": "a x #1", "56": "a x #1"}, "bb": {"7": "n", "10": "a x #1"}, "op_mob": {"10": "n", "11": "y #1", "11.1": "y #1", "11.5": "y #1", "12": "y #1", "12.1": "y #1", "37": "a x #1"}, "and_chr": {"59": "a x #1"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "a x #1", "5": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to working for inline elements but not across column or page breaks."}, "usage_perc_y": 14.91, "usage_perc_a": 76.51, "ucprefix": false, "parent": "", "keywords": "box-decoration,box decoration,break", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}