{"version": 3, "file": "platform-browser-testing.umd.min.js", "sources": ["../../../../packages/platform-browser/testing/src/browser_util.ts", "../../../../packages/platform-browser/testing/src/browser.ts", "../../../../packages/platform-browser/testing/src/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone, ɵglobal as global} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\n\nexport let browserDetection: BrowserDetection;\n\nexport class BrowserDetection {\n  private _overrideUa: string|null;\n  private get _ua(): string {\n    if (typeof this._overrideUa === 'string') {\n      return this._overrideUa;\n    }\n\n    return getDOM() ? getDOM().getUserAgent() : '';\n  }\n\n  static setup() { browserDetection = new BrowserDetection(null); }\n\n  constructor(ua: string|null) { this._overrideUa = ua; }\n\n  get isFirefox(): boolean { return this._ua.indexOf('Firefox') > -1; }\n\n  get isAndroid(): boolean {\n    return this._ua.indexOf('Mozilla/5.0') > -1 && this._ua.indexOf('Android') > -1 &&\n        this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Chrome') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isEdge(): boolean { return this._ua.indexOf('Edge') > -1; }\n\n  get isIE(): boolean { return this._ua.indexOf('Trident') > -1; }\n\n  get isWebkit(): boolean {\n    return this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Edge') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isIOS7(): boolean {\n    return (this._ua.indexOf('iPhone OS 7') > -1 || this._ua.indexOf('iPad OS 7') > -1) &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isSlow(): boolean { return this.isAndroid || this.isIE || this.isIOS7; }\n\n  // The Intl API is only natively supported in Chrome, Firefox, IE11 and Edge.\n  // This detector is needed in tests to make the difference between:\n  // 1) IE11/Edge: they have a native Intl API, but with some discrepancies\n  // 2) IE9/IE10: they use the polyfill, and so no discrepancies\n  get supportsNativeIntlApi(): boolean {\n    return !!(<any>global).Intl && (<any>global).Intl !== (<any>global).IntlPolyfill;\n  }\n\n  get isChromeDesktop(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Mobile Safari') == -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n\n  // \"Old Chrome\" means Chrome 3X, where there are some discrepancies in the Intl API.\n  // Android 4.4 and 5.X have such browsers by default (respectively 30 and 39).\n  get isOldChrome(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Chrome/3') > -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n}\n\nBrowserDetection.setup();\n\nexport function dispatchEvent(element: any, eventType: any): void {\n  getDOM().dispatchEvent(element, getDOM().createEvent(eventType));\n}\n\nexport function el(html: string): HTMLElement {\n  return <HTMLElement>getDOM().firstChild(getDOM().content(getDOM().createTemplate(html)));\n}\n\nexport function normalizeCSS(css: string): string {\n  return css.replace(/\\s+/g, ' ')\n      .replace(/:\\s/g, ':')\n      .replace(/'/g, '\"')\n      .replace(/ }/g, '}')\n      .replace(/url\\((\\\"|\\s)(.+)(\\\"|\\s)\\)(\\s*)/g, (...match: string[]) => `url(\"${match[2]}\")`)\n      .replace(/\\[(.+)=([^\"\\]]+)\\]/g, (...match: string[]) => `[${match[1]}=\"${match[2]}\"]`);\n}\n\nconst _singleTagWhitelist = ['br', 'hr', 'input'];\nexport function stringifyElement(el: any /** TODO #9100 */): string {\n  let result = '';\n  if (getDOM().isElementNode(el)) {\n    const tagName = getDOM().tagName(el).toLowerCase();\n\n    // Opening tag\n    result += `<${tagName}`;\n\n    // Attributes in an ordered way\n    const attributeMap = getDOM().attributeMap(el);\n    const keys: string[] = Array.from(attributeMap.keys()).sort();\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const attValue = attributeMap.get(key);\n      if (typeof attValue !== 'string') {\n        result += ` ${key}`;\n      } else {\n        result += ` ${key}=\"${attValue}\"`;\n      }\n    }\n    result += '>';\n\n    // Children\n    const childrenRoot = getDOM().templateAwareRoot(el);\n    const children = childrenRoot ? getDOM().childNodes(childrenRoot) : [];\n    for (let j = 0; j < children.length; j++) {\n      result += stringifyElement(children[j]);\n    }\n\n    // Closing tag\n    if (_singleTagWhitelist.indexOf(tagName) == -1) {\n      result += `</${tagName}>`;\n    }\n  } else if (getDOM().isCommentNode(el)) {\n    result += `<!--${getDOM().nodeValue(el)}-->`;\n  } else {\n    result += getDOM().getText(el);\n  }\n\n  return result;\n}\n\nexport function createNgZone(): NgZone {\n  return new NgZone({enableLongStackTrace: true});\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {APP_ID, NgModule, NgZone, PLATFORM_INITIALIZER, PlatformRef, Provider, createPlatformFactory, platformCore} from '@angular/core';\nimport {BrowserModule, ɵBrowserDomAdapter as BrowserDomAdapter, ɵELEMENT_PROBE_PROVIDERS as ELEMENT_PROBE_PROVIDERS} from '@angular/platform-browser';\nimport {BrowserDetection, createNgZone} from './browser_util';\n\nfunction initBrowserTests() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserDetection.setup();\n}\n\nconst _TEST_BROWSER_PLATFORM_PROVIDERS: Provider[] =\n    [{provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true}];\n\n/**\n * Platform for testing\n *\n * @stable\n */\nexport const platformBrowserTesting =\n    createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @stable\n */\n\nexport class BrowserTestingModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    ELEMENT_PROBE_PROVIDERS,\n    {provide: NgZone, useFactory: createNgZone},\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\nexport * from './browser';\n"], "names": ["browserDetection", "BrowserDetection", "ua", "this", "_overrideUa", "Object", "defineProperty", "prototype", "get", "_angular_platformBrowser", "ɵgetDOM", "getUserAgent", "enumerable", "setup", "_ua", "indexOf", "configurable", "ELEMENT_PROBE_PROVIDERS", "BrowserTestingModule", "type", "_angular_core", "NgModule", "args", "providers"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAqBA,GAAAA,kBAAAC,iBAAA,WAEA,QAAAA,kBAAAC,IAEAC,KAAAC,YAAAF,SAIAG,QAAAC,eAAAL,iBAAAM,UAAA,OACAC,IAAA,oEACAC,yBAAAC,UAAAD,yBAAAC,UAAiEC,eAAjE,IAEAC,YAAA,oBAAAX,iBAAAY,MAAA,WAAAb,iBAAA,GAAAC,kBAAA,OAEEI,OAAFC,eAAML,iBAANM,UAAA,aAAAC,IAAE,WAAF,MAAAL,MAAAW,IAAAC,QAAA,YAAA,GAEAH,YAAA,EAEAI,cAAA,IAEAX,OAAAC,eAAAL,iBAAAM,UAAA,uGAAAJ,KAAAW,IAAAC,QAAA,gBAAA,GAAAZ,KAAAW,IAAAC,QAAA,YAAA,GACAZ,KAAAW,IAAAC,QAAA,cAAA,mCAEEV,OAAFC,eAAML,iBAANM,UAAA,UAAAC,IAAE,WAAF,MAAAL,MAAAW,IAAAC,QAAA,SAAA,mCACAV,OAAAC,eAAIL,iBAAJM,UAAA,QAAAC,IAAA,WAAA,MAAAL,MAAAW,IAAAC,QAAA,YAAA,GACAH,YAAA,EACAI,cAAA,gFAEA,MAAAb,MAAAW,IAAAC,QAAA,gBAAA,GAAAZ,KAAAW,IAAAC,QAAA,UAAA,6NAMAZ,KAAAW,IAAAC,QAAA,cAAA,mCAMAV,OAAAC,eAAAL,iBAAAM,UAAA,wGAAAF,OAAAC,eAAAL,iBAAAM,UAAA,6IAKAK,YAAA,EACAI,cAAA,uFACA,MAAAb,MAAAW,IAAAC,QAAA,WAAA,GAAAZ,KAAAW,IAAAC,QAAA,mBAAA,GAIAZ,KAAAW,IAAAC,QAAA,UAAA,iBAAAC,cAAA,IAMAX,OAAAC,eAAAL,iBAAAM,UAAA,8BAIA,MAAAJ,MAAAW,IAAAC,QAAA,WAAA,GAAAZ,KAAAW,IAAAC,QAAA,aAAA,GASAZ,KAAAW,IAAAC,QAAA,UAAA,GA2CAH,YAAA,iUC/FAK,qBAAAA,WACA,QAAAC,yBAEA,MAAAA,2DAGAC,KAAAC,cAAAC,SAAAC,wDC7CAC"}