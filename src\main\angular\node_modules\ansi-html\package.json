{"_args": [["ansi-html@0.0.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ansi-html@0.0.7", "_id": "ansi-html@0.0.7", "_inBundle": false, "_integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4=", "_location": "/ansi-html", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ansi-html@0.0.7", "name": "ansi-html", "escapedName": "ansi-html", "rawSpec": "0.0.7", "saveSpec": null, "fetchSpec": "0.0.7"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ansi-html/-/ansi-html-0.0.7.tgz", "_spec": "0.0.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bin": {"ansi-html": "bin/ansi-html"}, "bugs": {"url": "https://github.com/Tjatse/ansi-html/issues"}, "dependencies": {}, "description": "An elegant lib that converts the chalked (ANSI) text to HTML.", "devDependencies": {"chai": "^1.9.1", "chalk": "^1.1.3", "lodash": "^2.4.2", "mocha": "^1.21.4"}, "engines": ["node >= 0.8.0"], "homepage": "https://github.com/Tjatse/ansi-html", "keywords": ["ansi", "ansi html", "chalk html"], "license": "Apache-2.0", "main": "index.js", "name": "ansi-html", "repository": {"type": "git", "url": "git://github.com/Tjatse/ansi-html.git"}, "scripts": {"test": "mocha -R spec -t 5000"}, "standard": {"ignore": [], "globals": ["describe", "it", "before", "after"]}, "version": "0.0.7"}