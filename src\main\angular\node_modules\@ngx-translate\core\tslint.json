{"rulesDirectory": ["node_modules/codelyzer"], "rules": {"class-name": true, "curly": true, "forin": true, "indent": [true, "spaces"], "label-position": true, "member-access": false, "no-arg": true, "no-bitwise": true, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-construct": true, "no-duplicate-variable": true, "no-empty": false, "no-eval": true, "no-inferrable-types": false, "no-shadowed-variable": true, "no-string-literal": false, "no-unused-expression": true, "no-use-before-declare": true, "object-literal-sort-keys": false, "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-whitespace"], "radix": true, "semicolon": ["always"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "variable-name": false, "use-input-property-decorator": true, "use-output-property-decorator": true, "use-host-property-decorator": false, "use-pipe-transform-interface": true}}