{"_args": [["array-uniq@1.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "array-uniq@1.0.3", "_id": "array-uniq@1.0.3", "_inBundle": false, "_integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "_location": "/array-uniq", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-uniq@1.0.3", "name": "array-uniq", "escapedName": "array-uniq", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/array-union"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/array-uniq/-/array-uniq-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/array-uniq/issues"}, "description": "Create an array without duplicates", "devDependencies": {"ava": "*", "es6-set": "^0.1.0", "require-uncached": "^1.0.2", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/array-uniq#readme", "keywords": ["array", "arr", "set", "uniq", "unique", "es6", "duplicate", "remove"], "license": "MIT", "name": "array-uniq", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/array-uniq.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.3"}