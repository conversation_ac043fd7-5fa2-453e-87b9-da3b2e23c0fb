[{"__symbolic": "module", "version": 3, "metadata": {"AotCompiler": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "./compiler_host", "name": "AotCompilerHost"}, {"__symbolic": "reference", "module": "./static_reflector", "name": "StaticReflector"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}, {"__symbolic": "reference", "module": "../template_parser/template_parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../style_compiler", "name": "StyleCompiler"}, {"__symbolic": "reference", "module": "../view_compiler/view_compiler", "name": "ViewCompiler"}, {"__symbolic": "reference", "module": "../ng_module_compiler", "name": "NgModuleCompiler"}, {"__symbolic": "reference", "module": "../output/abstract_emitter", "name": "OutputEmitter"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "./static_symbol_resolver", "name": "StaticSymbolResolver"}]}], "clearCache": [{"__symbolic": "method"}], "analyzeModulesSync": [{"__symbolic": "method"}], "analyzeModulesAsync": [{"__symbolic": "method"}], "emitAllStubs": [{"__symbolic": "method"}], "emitAllImpls": [{"__symbolic": "method"}], "_compileStubFile": [{"__symbolic": "method"}], "_compileImplFile": [{"__symbolic": "method"}], "_createSummary": [{"__symbolic": "method"}], "_compileModule": [{"__symbolic": "method"}], "_compileComponentFactory": [{"__symbolic": "method"}], "_compileComponent": [{"__symbolic": "method"}], "_createOutputContext": [{"__symbolic": "method"}], "_codegenStyles": [{"__symbolic": "method"}], "_codegenSourceModule": [{"__symbolic": "method"}]}}, "NgAnalyzedModules": {"__symbolic": "interface"}, "NgAnalyzeModulesHost": {"__symbolic": "interface"}, "analyzeNgModules": {"__symbolic": "function"}, "analyzeAndValidateNgModules": {"__symbolic": "function"}, "extractProgramSymbols": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"AotCompiler": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "./compiler_host", "name": "AotCompilerHost"}, {"__symbolic": "reference", "module": "./static_reflector", "name": "StaticReflector"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}, {"__symbolic": "reference", "module": "../template_parser/template_parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../style_compiler", "name": "StyleCompiler"}, {"__symbolic": "reference", "module": "../view_compiler/view_compiler", "name": "ViewCompiler"}, {"__symbolic": "reference", "module": "../ng_module_compiler", "name": "NgModuleCompiler"}, {"__symbolic": "reference", "module": "../output/abstract_emitter", "name": "OutputEmitter"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "./static_symbol_resolver", "name": "StaticSymbolResolver"}]}], "clearCache": [{"__symbolic": "method"}], "analyzeModulesSync": [{"__symbolic": "method"}], "analyzeModulesAsync": [{"__symbolic": "method"}], "emitAllStubs": [{"__symbolic": "method"}], "emitAllImpls": [{"__symbolic": "method"}], "_compileStubFile": [{"__symbolic": "method"}], "_compileImplFile": [{"__symbolic": "method"}], "_createSummary": [{"__symbolic": "method"}], "_compileModule": [{"__symbolic": "method"}], "_compileComponentFactory": [{"__symbolic": "method"}], "_compileComponent": [{"__symbolic": "method"}], "_createOutputContext": [{"__symbolic": "method"}], "_codegenStyles": [{"__symbolic": "method"}], "_codegenSourceModule": [{"__symbolic": "method"}]}}, "NgAnalyzedModules": {"__symbolic": "interface"}, "NgAnalyzeModulesHost": {"__symbolic": "interface"}, "analyzeNgModules": {"__symbolic": "function"}, "analyzeAndValidateNgModules": {"__symbolic": "function"}, "extractProgramSymbols": {"__symbolic": "function"}}}]