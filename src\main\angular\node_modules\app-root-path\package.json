{"_args": [["app-root-path@2.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "app-root-path@2.0.1", "_id": "app-root-path@2.0.1", "_inBundle": false, "_integrity": "sha1-zWLc+OT9WkF+/GZNLlsQZTxlG0Y=", "_location": "/app-root-path", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "app-root-path@2.0.1", "name": "app-root-path", "escapedName": "app-root-path", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/codelyzer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/app-root-path/-/app-root-path-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "http://cmorrell.com"}, "browser": "browser-shim.js", "bugs": {"url": "https://github.com/inxilpro/node-app-root-path/issues"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg", "post-merge": "npm install", "post-rewrite": "npm install"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "description": "Determine an app's root path from anywhere inside the app", "devDependencies": {"codecov": "^1.0.1", "coveralls": "^2.11.2", "cracks": "^3.1.2", "cz-conventional-changelog": "^1.2.0", "ghooks": "^1.3.2", "istanbul": "^0.3.4", "mocha": "^2.0.1", "mocha-lcov-reporter": "0.0.1", "mockery": "^1.7.0", "nyc": "^8.1.0", "semantic-release": "^4.3.5", "validate-commit-msg": "^2.8.0"}, "engines": {"node": ">= 4.0.0"}, "homepage": "https://github.com/inxilpro/node-app-root-path", "keywords": ["root", "path", "utility", "util", "node", "module", "modules", "node_modules", "require", "app"], "license": "MIT", "main": "index.js", "name": "app-root-path", "release": {"branch": "master"}, "repository": {"type": "git", "url": "git+https://github.com/inxilpro/node-app-root-path.git"}, "scripts": {"release": "semantic-release pre && npm publish && semantic-release post", "report-coverage": "npm test && nyc report --reporter=text-lcov > coverage.lcov && codecov", "test": "nyc mocha -R spec"}, "version": "2.0.1"}