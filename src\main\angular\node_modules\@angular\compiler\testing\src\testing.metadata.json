[{"__symbolic": "module", "version": 3, "metadata": {"TestingCompilerFactoryImpl": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "CompilerFactory"}]}], "createTestingCompiler": [{"__symbolic": "method"}]}}, "TestingCompilerImpl": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "JitCompiler"}, {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}, {"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}, {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileMetadataResolver"}]}], "compileModuleSync": [{"__symbolic": "method"}], "compileModuleAsync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsSync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsAsync": [{"__symbolic": "method"}], "getNgContentSelectors": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "checkOverrideAllowed": [{"__symbolic": "method"}], "overrideModule": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}]}}, "platformCoreDynamicTesting": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "platformCoreDynamic"}, "coreDynamicTesting", [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {"providers": [{"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "PipeResolver"}, "useExisting": {"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}}, {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "DirectiveResolver"}, "useExisting": {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}}, {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "NgModuleResolver"}, "useExisting": {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}}]}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "ɵTestingCompilerFactory"}, "useClass": {"__symbolic": "reference", "name": "TestingCompilerFactoryImpl"}}]]}}, "exports": [{"from": "./schema_registry_mock"}, {"from": "./directive_resolver_mock"}, {"from": "./ng_module_resolver_mock"}, {"from": "./pipe_resolver_mock"}]}, {"__symbolic": "module", "version": 1, "metadata": {"TestingCompilerFactoryImpl": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "CompilerFactory"}]}], "createTestingCompiler": [{"__symbolic": "method"}]}}, "TestingCompilerImpl": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "JitCompiler"}, {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}, {"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}, {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileMetadataResolver"}]}], "compileModuleSync": [{"__symbolic": "method"}], "compileModuleAsync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsSync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsAsync": [{"__symbolic": "method"}], "getNgContentSelectors": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "checkOverrideAllowed": [{"__symbolic": "method"}], "overrideModule": [{"__symbolic": "method"}], "overrideDirective": [{"__symbolic": "method"}], "overrideComponent": [{"__symbolic": "method"}], "overridePipe": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}]}}, "platformCoreDynamicTesting": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler", "name": "platformCoreDynamic"}, "coreDynamicTesting", [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {"providers": [{"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "PipeResolver"}, "useExisting": {"__symbolic": "reference", "module": "./pipe_resolver_mock", "name": "MockPipeResolver"}}, {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "DirectiveResolver"}, "useExisting": {"__symbolic": "reference", "module": "./directive_resolver_mock", "name": "MockDirectiveResolver"}}, {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}, {"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "NgModuleResolver"}, "useExisting": {"__symbolic": "reference", "module": "./ng_module_resolver_mock", "name": "MockNgModuleResolver"}}]}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "ɵTestingCompilerFactory"}, "useClass": {"__symbolic": "reference", "name": "TestingCompilerFactoryImpl"}}]]}}, "exports": [{"from": "./schema_registry_mock"}, {"from": "./directive_resolver_mock"}, {"from": "./ng_module_resolver_mock"}, {"from": "./pipe_resolver_mock"}]}]