/**
 * Generated bundle index. Do not edit.
 */
export * from './public_api';
export { ROUTER_FORROOT_GUARD as ɵa, RouterInitializer as ɵg, getAppInitializer as ɵh, getBootstrapListener as ɵi, provideForRootGuard as ɵd, provideLocationStrategy as ɵc, provideRouterInitializer as ɵj, rootRoute as ɵf, routerNgProbeToken as ɵb, setupRouter as ɵe } from './src/router_module';
export { Tree as ɵk, TreeNode as ɵl } from './src/utils/tree';
