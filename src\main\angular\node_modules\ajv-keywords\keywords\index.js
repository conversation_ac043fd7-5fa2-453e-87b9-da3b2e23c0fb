'use strict';

module.exports = {
  'instanceof': require('./instanceof'),
  propertyNames: require('./propertyNames'),
  range: require('./range'),
  regexp: require('./regexp'),
  'typeof': require('./typeof'),
  dynamicDefaults: require('./dynamicDefaults'),
  'if': require('./if'),
  prohibited: require('./prohibited'),
  deepProperties: require('./deepProperties'),
  deepRequired: require('./deepRequired')
  // formatMinimum: require('./formatMinimum'),
  // formatMaximum: require('./formatMaximum'),
  // patternRequired: require('./patternRequired'),
  // 'switch': require('./switch')
};
