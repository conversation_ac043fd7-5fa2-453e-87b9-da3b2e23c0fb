import * as <PERSON><PERSON> from 'tslint';
import * as ts from 'typescript';
export declare abstract class SelectorRule extends Lint.Rules.AbstractRule {
    isMultiPrefix: boolean;
    prefixArguments: string;
    cssSelectorProperty: string;
    handleType: string;
    private typeValidator;
    private prefixValidator;
    private nameValidator;
    private FAILURE_PREFIX;
    private isMultiSelectors;
    constructor(ruleName: string, value: any, disabledIntervals: Lint.IDisabledInterval[]);
    getPrefixFailure(): string;
    validateType(selector: string): boolean;
    validateName(selector: any): boolean;
    validatePrefix(selector: any): boolean;
    apply(sourceFile: ts.SourceFile): Lint.RuleFailure[];
    abstract getTypeFailure(): any;
    abstract getNameFailure(): any;
    protected abstract getSinglePrefixFailure(): any;
    protected abstract getManyPrefixFailure(): any;
    private setNameValidator(name);
    private setMultiPrefix(prefix);
    private setPrefixArguments(prefix);
    private setPrefixValidator(prefix, name);
    private setPrefixFailure();
    private setTypeValidator(type);
}
export declare class SelectorValidatorWalker extends Lint.RuleWalker {
    private rule;
    constructor(sourceFile: ts.SourceFile, rule: SelectorRule);
    visitClassDeclaration(node: ts.ClassDeclaration): void;
    private validateDecorator(className, decorator);
    private validateSelector(className, arg);
    private validateProperty(p);
    private isSupportedKind(kind);
    private extractMainSelector(i);
}
