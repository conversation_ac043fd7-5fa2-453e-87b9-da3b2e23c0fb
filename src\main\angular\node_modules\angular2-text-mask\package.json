{"_args": [["angular2-text-mask@8.0.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "angular2-text-mask@8.0.5", "_id": "angular2-text-mask@8.0.5", "_inBundle": false, "_integrity": "sha512-s6fqiQtIa16v0FzMhk1Y5LApAoq/0okKwtyCW70/UkHbRdRkN/uHa2LplX7F/SyTJFsYZJTRzH8IlpaMbQcccw==", "_location": "/angular2-text-mask", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "angular2-text-mask@8.0.5", "name": "angular2-text-mask", "escapedName": "angular2-text-mask", "rawSpec": "8.0.5", "saveSpec": null, "fetchSpec": "8.0.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/angular2-text-mask/-/angular2-text-mask-8.0.5.tgz", "_spec": "8.0.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/text-mask/text-mask/issues"}, "dependencies": {"text-mask-core": "^5.0.0"}, "description": "Angular 2 directive for input text masking", "devDependencies": {"@angular/common": "^2.1.2", "@angular/compiler": "^2.1.2", "@angular/compiler-cli": "^2.1.2", "@angular/core": "^2.1.2", "@angular/forms": "^2.1.2", "@angular/http": "^2.1.2", "@angular/platform-browser": "^2.1.2", "@angular/platform-browser-dynamic": "^2.1.2", "@angular/platform-server": "^2.1.2", "@types/core-js": "^0.9.34", "@types/jasmine": "^2.5.52", "@types/node": "^6.0.45", "awesome-typescript-loader": "^1.0.0", "concurrently": "^3.0.0", "core-js": "^2.4.1", "jasmine-core": "^2.6.4", "karma": "^1.7.0", "karma-cli": "^1.0.1", "karma-jasmine": "^1.1.0", "karma-phantomjs-launcher": "^1.0.4", "karma-typescript": "^3.0.4", "lite-server": "^2.2.2", "reflect-metadata": "^0.1.8", "rxjs": "^5.0.1", "systemjs": "0.19.39", "ts-node": "~1.2.0", "tslint": "^3.13.0", "typescript": "~2.3.0", "zone.js": "^0.7.2"}, "homepage": "https://github.com/text-mask/text-mask/tree/master/angular2/#readme", "keywords": ["angular2", "ng2", "angular2 component", "angular2 directive", "text mask", "input mask", "string mask", "input formatting", "text formatting", "string formatting"], "license": "Unlicense", "main": "dist/angular2TextMask.js", "name": "angular2-text-mask", "repository": {"type": "git", "url": "git+https://github.com/text-mask/text-mask.git"}, "scripts": {"build": "ngc -p tsconfig.json && rm -rf aot", "ci": "npm run test && npm run loud-lint", "lint": "tslint -c tslint.json src/*.ts test/**/*.ts example/**/*.ts || true", "loud-lint": "tslint -c tslint.json src/*.ts test/**/*.ts example/**/*.ts", "start": "tsc -p example/tsconfig.json && concurrently \"tsc -w -p example/tsconfig.json\" lite-server ", "test": "karma start"}, "typings": "./dist/angular2TextMask.d.ts", "version": "8.0.5"}