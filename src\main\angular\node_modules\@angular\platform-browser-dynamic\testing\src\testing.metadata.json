[{"__symbolic": "module", "version": 3, "metadata": {"platformBrowserDynamicTesting": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler/testing", "name": "platformCoreDynamicTesting"}, "browserDynamicTesting", {"__symbolic": "reference", "module": "@angular/platform-browser-dynamic", "name": "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"}]}, "BrowserDynamicTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/platform-browser/testing", "name": "BrowserTestingModule"}], "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "TestComponent<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./dom_test_component_renderer", "name": "DOMTestComponentRenderer"}}]}]}]}}, "exports": [{"from": "./private_export_testing"}]}, {"__symbolic": "module", "version": 1, "metadata": {"platformBrowserDynamicTesting": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/compiler/testing", "name": "platformCoreDynamicTesting"}, "browserDynamicTesting", {"__symbolic": "reference", "module": "@angular/platform-browser-dynamic", "name": "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"}]}, "BrowserDynamicTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"exports": [{"__symbolic": "reference", "module": "@angular/platform-browser/testing", "name": "BrowserTestingModule"}], "providers": [{"provide": {"__symbolic": "reference", "module": "@angular/core/testing", "name": "TestComponent<PERSON><PERSON><PERSON>"}, "useClass": {"__symbolic": "reference", "module": "./dom_test_component_renderer", "name": "DOMTestComponentRenderer"}}]}]}]}}, "exports": [{"from": "./private_export_testing"}]}]