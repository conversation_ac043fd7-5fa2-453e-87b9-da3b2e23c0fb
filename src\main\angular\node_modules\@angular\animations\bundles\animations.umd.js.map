{"version": 3, "file": "animations.umd.js", "sources": ["../../../../packages/animations/src/players/animation_group_player.ts", "../../../../packages/animations/src/players/animation_player.ts", "../../../../packages/animations/src/animation_metadata.ts", "../../../../packages/animations/src/animation_builder.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {scheduleMicroTask} from '../util';\nimport {AnimationPlayer} from './animation_player';\nexport class AnimationGroupPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _finished = false;\nprivate _started = false;\nprivate _destroyed = false;\nprivate _onDestroyFns: Function[] = [];\npublic parentPlayer: AnimationPlayer|null = null;\npublic totalTime: number = 0;\n/**\n * @param {?} _players\n */\nconstructor(private _players: AnimationPlayer[]) {\n    let doneCount = 0;\n    let destroyCount = 0;\n    let startCount = 0;\n    const total = this._players.length;\n\n    if (total == 0) {\n      scheduleMicroTask(() => this._onFinish());\n    } else {\n      this._players.forEach(player => {\n        player.parentPlayer = this;\n        player.onDone(() => {\n          if (++doneCount >= total) {\n            this._onFinish();\n          }\n        });\n        player.onDestroy(() => {\n          if (++destroyCount >= total) {\n            this._onDestroy();\n          }\n        });\n        player.onStart(() => {\n          if (++startCount >= total) {\n            this._onStart();\n          }\n        });\n      });\n    }\n\n    this.totalTime = this._players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n  }\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @return {?}\n */\ninit(): void { this._players.forEach(player => player.init()); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @return {?}\n */\nprivate _onStart() {\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nhasStarted() { return this._started; }\n/**\n * @return {?}\n */\nplay() {\n    if (!this.parentPlayer) {\n      this.init();\n    }\n    this._onStart();\n    this._players.forEach(player => player.play());\n  }\n/**\n * @return {?}\n */\npause(): void { this._players.forEach(player => player.pause()); }\n/**\n * @return {?}\n */\nrestart(): void { this._players.forEach(player => player.restart()); }\n/**\n * @return {?}\n */\nfinish(): void {\n    this._onFinish();\n    this._players.forEach(player => player.finish());\n  }\n/**\n * @return {?}\n */\ndestroy(): void { this._onDestroy(); }\n/**\n * @return {?}\n */\nprivate _onDestroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._onFinish();\n      this._players.forEach(player => player.destroy());\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @return {?}\n */\nreset(): void {\n    this._players.forEach(player => player.reset());\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void {\n    const /** @type {?} */ timeAtPosition = p * this.totalTime;\n    this._players.forEach(player => {\n      const /** @type {?} */ position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n      player.setPosition(position);\n    });\n  }\n/**\n * @return {?}\n */\ngetPosition(): number {\n    let /** @type {?} */ min = 0;\n    this._players.forEach(player => {\n      const /** @type {?} */ p = player.getPosition();\n      min = Math.min(p, min);\n    });\n    return min;\n  }\n/**\n * @return {?}\n */\nget players(): AnimationPlayer[] { return this._players; }\n}\n\nfunction AnimationGroupPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationGroupPlayer.prototype._onDoneFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype._onStartFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype._finished;\n/** @type {?} */\nAnimationGroupPlayer.prototype._started;\n/** @type {?} */\nAnimationGroupPlayer.prototype._destroyed;\n/** @type {?} */\nAnimationGroupPlayer.prototype._onDestroyFns;\n/** @type {?} */\nAnimationGroupPlayer.prototype.parentPlayer;\n/** @type {?} */\nAnimationGroupPlayer.prototype.totalTime;\n/** @type {?} */\nAnimationGroupPlayer.prototype._players;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {scheduleMicroTask} from '../util';\n\n/**\n * AnimationPlayer controls an animation sequence that was produced from a programmatic animation.\n * (see {@link AnimationBuilder AnimationBuilder} for more information on how to create programmatic\n * animations.)\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationPlayer {\n  onDone(fn: () => void): void;\n  onStart(fn: () => void): void;\n  onDestroy(fn: () => void): void;\n  init(): void;\n  hasStarted(): boolean;\n  play(): void;\n  pause(): void;\n  restart(): void;\n  finish(): void;\n  destroy(): void;\n  reset(): void;\n  setPosition(p: any /** TODO #9100 */): void;\n  getPosition(): number;\n  parentPlayer: AnimationPlayer|null;\n  readonly totalTime: number;\n  beforeDestroy?: () => any;\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationPlayer implements AnimationPlayer {\nprivate _onDoneFns: Function[] = [];\nprivate _onStartFns: Function[] = [];\nprivate _onDestroyFns: Function[] = [];\nprivate _started = false;\nprivate _destroyed = false;\nprivate _finished = false;\npublic parentPlayer: AnimationPlayer|null = null;\npublic totalTime = 0;\nconstructor() {}\n/**\n * @return {?}\n */\nprivate _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._onStartFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._onDoneFns.push(fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._onDestroyFns.push(fn); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\ninit(): void {}\n/**\n * @return {?}\n */\nplay(): void {\n    if (!this.hasStarted()) {\n      this.triggerMicrotask();\n      this._onStart();\n    }\n    this._started = true;\n  }\n/**\n * @return {?}\n */\ntriggerMicrotask() { scheduleMicroTask(() => this._onFinish()); }\n/**\n * @return {?}\n */\nprivate _onStart() {\n    this._onStartFns.forEach(fn => fn());\n    this._onStartFns = [];\n  }\n/**\n * @return {?}\n */\npause(): void {}\n/**\n * @return {?}\n */\nrestart(): void {}\n/**\n * @return {?}\n */\nfinish(): void { this._onFinish(); }\n/**\n * @return {?}\n */\ndestroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      if (!this.hasStarted()) {\n        this._onStart();\n      }\n      this.finish();\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n/**\n * @return {?}\n */\nreset(): void {}\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void {}\n/**\n * @return {?}\n */\ngetPosition(): number { return 0; }\n}\n\nfunction NoopAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nNoopAnimationPlayer.prototype._onDoneFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._onStartFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._onDestroyFns;\n/** @type {?} */\nNoopAnimationPlayer.prototype._started;\n/** @type {?} */\nNoopAnimationPlayer.prototype._destroyed;\n/** @type {?} */\nNoopAnimationPlayer.prototype._finished;\n/** @type {?} */\nNoopAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nNoopAnimationPlayer.prototype.totalTime;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport interface ɵStyleData { [key: string]: string|number; }\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are created internally\n * within the Angular animation DSL.\n *\n * @experimental Animation support is experimental.\n */\nexport declare type AnimateTimings = {\n  duration: number,\n  delay: number,\n  easing: string | null\n};\n\n/**\n * `AnimationOptions` represents options that can be passed into most animation DSL methods.\n * When options are provided, the delay value of an animation can be changed and animation input\n * parameters can be passed in to change styling and timing data when an animation is started.\n *\n * The following animation DSL functions are able to accept animation option data:\n *\n * - {@link transition transition()}\n * - {@link sequence sequence()}\n * - {@link group group()}\n * - {@link query query()}\n * - {@link animation animation()}\n * - {@link useAnimation useAnimation()}\n * - {@link animateChild animateChild()}\n *\n * Programmatic animations built using {@link AnimationBuilder the AnimationBuilder service} also\n * make use of AnimationOptions.\n *\n * @experimental Animation support is experimental.\n */\nexport declare interface AnimationOptions {\n  delay?: number|string;\n  params?: {[name: string]: any};\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are created internally\n * within the Angular animation DSL when {@link animateChild animateChild()} is used.\n *\n * @experimental Animation support is experimental.\n */\nexport declare interface AnimateChildOptions extends AnimationOptions { duration?: number|string; }\n\n/**\n * Metadata representing the entry of animations. Usages of this enum are created\n * each time an animation DSL function is used.\n *\n * @experimental Animation support is experimental.\n */\nexport const enum AnimationMetadataType {\n  State = 0,\n  Transition = 1,\n  Sequence = 2,\n  Group = 3,\n  Animate = 4,\n  Keyframes = 5,\n  Style = 6,\n  Trigger = 7,\n  Reference = 8,\n  AnimateChild = 9,\n  AnimateRef = 10,\n  Query = 11,\n  Stagger = 12\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport const AUTO_STYLE = '*';\n\n/**\n * @experimental Animation support is experimental.\n */\nexport interface AnimationMetadata { type: AnimationMetadataType; }\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link trigger trigger animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationTriggerMetadata extends AnimationMetadata {\n  name: string;\n  definitions: AnimationMetadata[];\n  options: {params?: {[name: string]: any}}|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link state state animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationStateMetadata extends AnimationMetadata {\n  name: string;\n  styles: AnimationStyleMetadata;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link transition transition animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationTransitionMetadata extends AnimationMetadata {\n  expr: string;\n  animation: AnimationMetadata|AnimationMetadata[];\n  options: AnimationOptions|null;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport interface AnimationReferenceMetadata extends AnimationMetadata {\n  animation: AnimationMetadata|AnimationMetadata[];\n  options: AnimationOptions|null;\n}\n\n/**\n * @experimental Animation support is experimental.\n */\nexport interface AnimationQueryMetadata extends AnimationMetadata {\n  selector: string;\n  animation: AnimationMetadata|AnimationMetadata[];\n  options: AnimationQueryOptions|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link keyframes keyframes animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationKeyframesSequenceMetadata extends AnimationMetadata {\n  steps: AnimationStyleMetadata[];\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link style style animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationStyleMetadata extends AnimationMetadata {\n  styles: '*'|{[key: string]: string | number}|Array<{[key: string]: string | number}|'*'>;\n  offset: number|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link animate animate animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationAnimateMetadata extends AnimationMetadata {\n  timings: string|number|AnimateTimings;\n  styles: AnimationStyleMetadata|AnimationKeyframesSequenceMetadata|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link animateChild animateChild animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationAnimateChildMetadata extends AnimationMetadata {\n  options: AnimationOptions|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link useAnimation useAnimation animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationAnimateRefMetadata extends AnimationMetadata {\n  animation: AnimationReferenceMetadata;\n  options: AnimationOptions|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link sequence sequence animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationSequenceMetadata extends AnimationMetadata {\n  steps: AnimationMetadata[];\n  options: AnimationOptions|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link group group animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport interface AnimationGroupMetadata extends AnimationMetadata {\n  steps: AnimationMetadata[];\n  options: AnimationOptions|null;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link query query animation function} is called.\n *\n * @experimental Animation support is experimental.\n */\nexport declare interface AnimationQueryOptions extends AnimationOptions {\n  optional?: boolean;\n  limit?: number;\n}\n\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {@link stagger stagger animation function} is called.\n *\n* @experimental Animation support is experimental.\n*/\nexport interface AnimationStaggerMetadata extends AnimationMetadata {\n  timings: string|number;\n  animation: AnimationMetadata|AnimationMetadata[];\n}\n/**\n * `trigger` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `trigger` Creates an animation trigger which will a list of {\\@link state state} and {\\@link\n * transition transition} entries that will be evaluated when the expression bound to the trigger\n * changes.\n * \n * Triggers are registered within the component annotation data under the {\\@link\n * Component#animations animations section}. An animation trigger can be placed on an element\n * within a template by referencing the name of the trigger followed by the expression value that the\n * trigger is bound to (in the form of `[\\@triggerName]=\"expression\"`.\n * \n * ### Usage\n * \n * `trigger` will create an animation trigger reference based on the provided `name` value. The\n * provided `animation` value is expected to be an array consisting of {\\@link state state} and {\\@link\n * transition transition} declarations.\n * \n * ```typescript\n * \\@Component({ \n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n * \n * The template associated with this component will make use of the `myAnimationTrigger` animation\n * trigger by binding to an element within its template code.\n * \n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [\\@myAnimationTrigger]=\"myStatusExp\">...</div>\n * tools/gulp-tasks/validate-commit-message.js ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} definitions\n * @return {?}\n */\nexport function trigger(name: string, definitions: AnimationMetadata[]): AnimationTriggerMetadata {\n  return {type: AnimationMetadataType.Trigger, name, definitions, options: {}};\n}\n/**\n * `animate` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `animate` specifies an animation step that will apply the provided `styles` data for a given\n * amount of time based on the provided `timing` expression value. Calls to `animate` are expected\n * to be used within {\\@link sequence an animation sequence}, {\\@link group group}, or {\\@link\n * transition transition}.\n * \n * ### Usage\n * \n * The `animate` function accepts two input parameters: `timing` and `styles`:\n * \n * - `timing` is a string based value that can be a combination of a duration with optional delay\n * and easing values. The format for the expression breaks down to `duration delay easing`\n * (therefore a value such as `1s 100ms ease-out` will be parse itself into `duration=1000,\n * delay=100, easing=ease-out`. If a numeric value is provided then that will be used as the\n * `duration` value in millisecond form.\n * - `styles` is the style input data which can either be a call to {\\@link style style} or {\\@link\n * keyframes keyframes}. If left empty then the styles from the destination state will be collected\n * and used (this is useful when describing an animation step that will complete an animation by\n * {\\@link transition#the-final-animate-call animating to the final state}).\n * \n * ```typescript\n * // various functions for specifying timing data\n * animate(500, style(...))\n * animate(\"1s\", style(...))\n * animate(\"100ms 0.5s\", style(...))\n * animate(\"5s ease\", style(...))\n * animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\", style(...))\n * \n * // either style() of keyframes() can be used\n * animate(500, style({ background: \"red\" }))\n * animate(500, keyframes([\n *   style({ background: \"blue\" })),\n *   style({ background: \"red\" }))\n * ])\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} timings\n * @param {?=} styles\n * @return {?}\n */\nexport function animate(\n    timings: string | number, styles: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata |\n        null = null): AnimationAnimateMetadata {\n  return {type: AnimationMetadataType.Animate, styles, timings};\n}\n/**\n * `group` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `group` specifies a list of animation steps that are all run in parallel. Grouped animations are\n * useful when a series of styles must be animated/closed off at different starting/ending times.\n * \n * The `group` function can either be used within a {\\@link sequence sequence} or a {\\@link transition\n * transition} and it will only continue to the next instruction once all of the inner animation\n * steps have completed.\n * \n * ### Usage\n * \n * The `steps` data that is passed into the `group` animation function can either consist of {\\@link\n * style style} or {\\@link animate animate} function calls. Each call to `style()` or `animate()`\n * within a group will be executed instantly (use {\\@link keyframes keyframes} or a {\\@link\n * animate#usage animate() with a delay value} to offset styles to be applied at a later time).\n * \n * ```typescript\n * group([\n *   animate(\"1s\", { background: \"black\" }))\n *   animate(\"2s\", { color: \"white\" }))\n * ])\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function group(\n    steps: AnimationMetadata[], options: AnimationOptions | null = null): AnimationGroupMetadata {\n  return {type: AnimationMetadataType.Group, steps, options};\n}\n/**\n * `sequence` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `sequence` Specifies a list of animation steps that are run one by one. (`sequence` is used by\n * default when an array is passed as animation data into {\\@link transition transition}.)\n * \n * The `sequence` function can either be used within a {\\@link group group} or a {\\@link transition\n * transition} and it will only continue to the next instruction once each of the inner animation\n * steps have completed.\n * \n * To perform animation styling in parallel with other animation steps then have a look at the\n * {\\@link group group} animation function.\n * \n * ### Usage\n * \n * The `steps` data that is passed into the `sequence` animation function can either consist of\n * {\\@link style style} or {\\@link animate animate} function calls. A call to `style()` will apply the\n * provided styling data immediately while a call to `animate()` will apply its styling data over a\n * given time depending on its timing data.\n * \n * ```typescript\n * sequence([\n *   style({ opacity: 0 })),\n *   animate(\"1s\", { opacity: 1 }))\n * ])\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function sequence(steps: AnimationMetadata[], options: AnimationOptions | null = null):\n    AnimationSequenceMetadata {\n  return {type: AnimationMetadataType.Sequence, steps, options};\n}\n/**\n * `style` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `style` declares a key/value object containing CSS properties/styles that can then be used for\n * {\\@link state animation states}, within an {\\@link sequence animation sequence}, or as styling data\n * for both {\\@link animate animate} and {\\@link keyframes keyframes}.\n * \n * ### Usage\n * \n * `style` takes in a key/value string map as data and expects one or more CSS property/value pairs\n * to be defined.\n * \n * ```typescript\n * // string values are used for css properties\n * style({ background: \"red\", color: \"blue\" })\n * \n * // numerical (pixel) values are also supported\n * style({ width: 100, height: 0 })\n * ```\n * \n * #### Auto-styles (using `*`)\n * \n * When an asterix (`*`) character is used as a value then it will be detected from the element\n * being animated and applied as animation data when the animation starts.\n * \n * This feature proves useful for a state depending on layout and/or environment factors; in such\n * cases the styles are calculated just before the animation starts.\n * \n * ```typescript\n * // the steps below will animate from 0 to the\n * // actual height of the element\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} tokens\n * @return {?}\n */\nexport function style(\n    tokens: '*' | {[key: string]: string | number} |\n    Array<'*'|{[key: string]: string | number}>): AnimationStyleMetadata {\n  return {type: AnimationMetadataType.Style, styles: tokens, offset: null};\n}\n/**\n * `state` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `state` declares an animation state within the given trigger. When a state is active within a\n * component then its associated styles will persist on the element that the trigger is attached to\n * (even when the animation ends).\n * \n * To animate between states, have a look at the animation {\\@link transition transition} DSL\n * function. To register states to an animation trigger please have a look at the {\\@link trigger\n * trigger} function.\n * \n * #### The `void` state\n * \n * The `void` state value is a reserved word that angular uses to determine when the element is not\n * apart of the application anymore (e.g. when an `ngIf` evaluates to false then the state of the\n * associated element is void).\n * \n * #### The `*` (default) state\n * \n * The `*` state (when styled) is a fallback state that will be used if the state that is being\n * animated is not declared within the trigger.\n * \n * ### Usage\n * \n * `state` will declare an animation state with its associated styles\n * within the given trigger.\n * \n * - `stateNameExpr` can be one or more state names separated by commas.\n * - `styles` refers to the {\\@link style styling data} that will be persisted on the element once\n * the state has been reached.\n * \n * ```typescript\n * // \"void\" is a reserved name for a state and is used to represent\n * // the state in which an element is detached from from the application.\n * state(\"void\", style({ height: 0 }))\n * \n * // user-defined states\n * state(\"closed\", style({ height: 0 }))\n * state(\"open, visible\", style({ height: \"*\" }))\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} styles\n * @return {?}\n */\nexport function state(name: string, styles: AnimationStyleMetadata): AnimationStateMetadata {\n  return {type: AnimationMetadataType.State, name, styles};\n}\n/**\n * `keyframes` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `keyframes` specifies a collection of {\\@link style style} entries each optionally characterized\n * by an `offset` value.\n * \n * ### Usage\n * \n * The `keyframes` animation function is designed to be used alongside the {\\@link animate animate}\n * animation function. Instead of applying animations from where they are currently to their\n * destination, keyframes can describe how each style entry is applied and at what point within the\n * animation arc (much like CSS Keyframe Animations do).\n * \n * For each `style()` entry an `offset` value can be set. Doing so allows to specifiy at what\n * percentage of the animate time the styles will be applied.\n * \n * ```typescript\n * // the provided offset values describe when each backgroundColor value is applied.\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n * \n * Alternatively, if there are no `offset` values used within the style entries then the offsets\n * will be calculated automatically.\n * \n * ```typescript\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @return {?}\n */\nexport function keyframes(steps: AnimationStyleMetadata[]): AnimationKeyframesSequenceMetadata {\n  return {type: AnimationMetadataType.Keyframes, steps};\n}\n/**\n * `transition` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n * \n * `transition` declares the {\\@link sequence sequence of animation steps} that will be run when the\n * provided `stateChangeExpr` value is satisfied. The `stateChangeExpr` consists of a `state1 =>\n * state2` which consists of two known states (use an asterix (`*`) to refer to a dynamic starting\n * and/or ending state).\n * \n * A function can also be provided as the `stateChangeExpr` argument for a transition and this\n * function will be executed each time a state change occurs. If the value returned within the\n * function is true then the associated animation will be run.\n * \n * Animation transitions are placed within an {\\@link trigger animation trigger}. For an transition\n * to animate to a state value and persist its styles then one or more {\\@link state animation\n * states} is expected to be defined.\n * \n * ### Usage\n * \n * An animation transition is kicked off the `stateChangeExpr` predicate evaluates to true based on\n * what the previous state is and what the current state has become. In other words, if a transition\n * is defined that matches the old/current state criteria then the associated animation will be\n * triggered.\n * \n * ```typescript\n * // all transition/state changes are defined within an animation trigger\n * trigger(\"myAnimationTrigger\", [\n *   // if a state is defined then its styles will be persisted when the\n *   // animation has fully completed itself\n *   state(\"on\", style({ background: \"green\" })),\n *   state(\"off\", style({ background: \"grey\" })),\n * \n *   // a transition animation that will be kicked off when the state value\n *   // bound to \"myAnimationTrigger\" changes from \"on\" to \"off\"\n *   transition(\"on => off\", animate(500)),\n * \n *   // it is also possible to do run the same animation for both directions\n *   transition(\"on <=> off\", animate(500)),\n * \n *   // or to define multiple states pairs separated by commas\n *   transition(\"on => off, off => void\", animate(500)),\n * \n *   // this is a catch-all state change for when an element is inserted into\n *   // the page and the destination state is unknown\n *   transition(\"void => *\", [\n *     style({ opacity: 0 }),\n *     animate(500)\n *   ]),\n * \n *   // this will capture a state change between any states\n *   transition(\"* => *\", animate(\"1s 0s\")),\n * \n *   // you can also go full out and include a function\n *   transition((fromState, toState) => {\n *     // when `true` then it will allow the animation below to be invoked\n *     return fromState == \"off\" && toState == \"on\";\n *   }, animate(\"1s 0s\"))\n * ])\n * ```\n * \n * The template associated with this component will make use of the `myAnimationTrigger` animation\n * trigger by binding to an element within its template code.\n * \n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [\\@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n * \n * #### The final `animate` call\n * \n * If the final step within the transition steps is a call to `animate()` that **only** uses a\n * timing value with **no style data** then it will be automatically used as the final animation arc\n * for the element to animate itself to the final state. This involves an automatic mix of\n * adding/removing CSS styles so that the element will be in the exact state it should be for the\n * applied state to be presented correctly.\n * \n * ```\n * // start off by hiding the element, but make sure that it animates properly to whatever state\n * // is currently active for \"myAnimationTrigger\"\n * transition(\"void => *\", [\n *   style({ opacity: 0 }),\n *   animate(500)\n * ])\n * ```\n * \n * ### Transition Aliases (`:enter` and `:leave`)\n * \n * Given that enter (insertion) and leave (removal) animations are so common, the `transition`\n * function accepts both `:enter` and `:leave` values which are aliases for the `void => *` and `*\n * => void` state changes.\n * \n * ```\n * transition(\":enter\", [\n *   style({ opacity: 0 }),\n *   animate(500, style({ opacity: 1 }))\n * ])\n * transition(\":leave\", [\n *   animate(500, style({ opacity: 0 }))\n * ])\n * ```\n * \n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n * \n * \\@experimental Animation support is experimental.\n * @param {?} stateChangeExpr\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function transition(\n    stateChangeExpr: string, steps: AnimationMetadata | AnimationMetadata[],\n    options: AnimationOptions | null = null): AnimationTransitionMetadata {\n  return {type: AnimationMetadataType.Transition, expr: stateChangeExpr, animation: steps, options};\n}\n/**\n * `animation` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language.\n * \n * `var myAnimation = animation(...)` is designed to produce a reusable animation that can be later\n * invoked in another animation or sequence. Reusable animations are designed to make use of\n * animation parameters and the produced animation can be used via the `useAnimation` method.\n * \n * ```\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *     style({ opacity: '{{ end }}'))\n * ], { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n * \n * If parameters are attached to an animation then they act as **default parameter values**. When an\n * animation is invoked via `useAnimation` then parameter values are allowed to be passed in\n * directly. If any of the passed in parameter values are missing then the default values will be\n * used.\n * \n * ```\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n * \n * If one or more parameter values are missing before animated then an error will be thrown.\n * \n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function animation(\n    steps: AnimationMetadata | AnimationMetadata[],\n    options: AnimationOptions | null = null): AnimationReferenceMetadata {\n  return {type: AnimationMetadataType.Reference, animation: steps, options};\n}\n/**\n * `animateChild` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It works by allowing a queried element to execute its own\n * animation within the animation sequence.\n * \n * Each time an animation is triggered in angular, the parent animation\n * will always get priority and any child animations will be blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations and then allow the animations to run using `animateChild`.\n * \n * The example HTML code below shows both parent and child elements that have animation\n * triggers that will execute at the same time.\n * \n * ```html\n * <!-- parent-child.component.html -->\n * <button (click)=\"exp =! exp\">Toggle</button>\n * <hr>\n * \n * <div [\\@parentAnimation]=\"exp\">\n *   <header>Hello</header>\n *   <div [\\@childAnimation]=\"exp\">\n *       one\n *   </div>\n *   <div [\\@childAnimation]=\"exp\">\n *       two\n *   </div>\n *   <div [\\@childAnimation]=\"exp\">\n *       three\n *   </div>\n * </div>\n * ```\n * \n * Now when the `exp` value changes to true, only the `parentAnimation` animation will animate\n * because it has priority. However, using `query` and `animateChild` each of the inner animations\n * can also fire:\n * \n * ```ts\n * // parent-child.component.ts\n * import {trigger, transition, animate, style, query, animateChild} from '\\@angular/animations';\n * \\@Component({ \n *   selector: 'parent-child-component',\n *   animations: [\n *     trigger('parentAnimation', [\n *       transition('false => true', [\n *         query('header', [\n *           style({ opacity: 0 }),\n *           animate(500, style({ opacity: 1 }))\n *         ]),\n *         query('\\@childAnimation', [\n *           animateChild()\n *         ])\n *       ])\n *     ]),\n *     trigger('childAnimation', [\n *       transition('false => true', [\n *         style({ opacity: 0 }),\n *         animate(500, style({ opacity: 1 }))\n *       ])\n *     ])\n *   ]\n * })\n * class ParentChildCmp {\n *   exp: boolean = false;\n * }\n * ```\n * \n * In the animation code above, when the `parentAnimation` transition kicks off it first queries to\n * find the header element and fades it in. It then finds each of the sub elements that contain the\n * `\\@childAnimation` trigger and then allows for their animations to fire.\n * \n * This example can be further extended by using stagger:\n * \n * ```ts\n * query('\\@childAnimation', stagger(100, [\n *   animateChild()\n * ]))\n * ```\n * \n * Now each of the sub animations start off with respect to the `100ms` staggering step.\n * \n * ## The first frame of child animations\n * When sub animations are executed using `animateChild` the animation engine will always apply the\n * first frame of every sub animation immediately at the start of the animation sequence. This way\n * the parent animation does not need to set any initial styling data on the sub elements before the\n * sub animations kick off.\n * \n * In the example above the first frame of the `childAnimation`'s `false => true` transition\n * consists of a style of `opacity: 0`. This is applied immediately when the `parentAnimation`\n * animation transition sequence starts. Only then when the `\\@childAnimation` is queried and called\n * with `animateChild` will it then animate to its destination of `opacity: 1`.\n * \n * Note that this feature designed to be used alongside {\\@link query query()} and it will only work\n * with animations that are assigned using the Angular animation DSL (this means that CSS keyframes\n * and transitions are not handled by this API).\n * \n * \\@experimental Animation support is experimental.\n * @param {?=} options\n * @return {?}\n */\nexport function animateChild(options: AnimateChildOptions | null = null):\n    AnimationAnimateChildMetadata {\n  return {type: AnimationMetadataType.AnimateChild, options};\n}\n/**\n * `useAnimation` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It is used to kick off a reusable animation that is created using {\\@link\n * animation animation()}.\n * \n * \\@experimental Animation support is experimental.\n * @param {?} animation\n * @param {?=} options\n * @return {?}\n */\nexport function useAnimation(\n    animation: AnimationReferenceMetadata,\n    options: AnimationOptions | null = null): AnimationAnimateRefMetadata {\n  return {type: AnimationMetadataType.AnimateRef, animation, options};\n}\n/**\n * `query` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language.\n * \n * query() is used to find one or more inner elements within the current element that is\n * being animated within the sequence. The provided animation steps are applied\n * to the queried element (by default, an array is provided, then this will be\n * treated as an animation sequence).\n * \n * ### Usage\n * \n * query() is designed to collect mutiple elements and works internally by using\n * `element.querySelectorAll`. An additional options object can be provided which\n * can be used to limit the total amount of items to be collected.\n * \n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n * \n * query(), by default, will throw an error when zero items are found. If a query\n * has the `optional` flag set to true then this error will be ignored.\n * \n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n * \n * ### Special Selector Values\n * \n * The selector value within a query can collect elements that contain angular-specific\n * characteristics\n * using special pseudo-selectors tokens.\n * \n * These include:\n * \n *  - Querying for newly inserted/removed elements using `query(\":enter\")`/`query(\":leave\")`\n *  - Querying all currently animating elements using `query(\":animating\")`\n *  - Querying elements that contain an animation trigger using `query(\"\\@triggerName\")`\n *  - Querying all elements that contain an animation triggers using `query(\"\\@*\")`\n *  - Including the current element into the animation sequence using `query(\":self\")`\n * \n * \n *  Each of these pseudo-selector tokens can be merged together into a combined query selector\n * string:\n * \n *  ```\n *  query(':self, .record:enter, .record:leave, \\@subTrigger', [...])\n *  ```\n * \n * ### Demo\n * \n * ```\n * \\@Component({ \n *   selector: 'inner',\n *   template: `\n *     <div [\\@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n * \n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 })),\n *        query('.content', animate(1000, style({ opacity: 1 })),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n * \n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n * \n * \\@experimental Animation support is experimental.\n * @param {?} selector\n * @param {?} animation\n * @param {?=} options\n * @return {?}\n */\nexport function query(\n    selector: string, animation: AnimationMetadata | AnimationMetadata[],\n    options: AnimationQueryOptions | null = null): AnimationQueryMetadata {\n  return {type: AnimationMetadataType.Query, selector, animation, options};\n}\n/**\n * `stagger` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It is designed to be used inside of an animation {\\@link query query()}\n * and works by issuing a timing gap between after each queried item is animated.\n * \n * ### Usage\n * \n * In the example below there is a container element that wraps a list of items stamped out\n * by an ngFor. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n * \n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [\\@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n * \n * The component code for this looks as such:\n * \n * ```ts\n * import {trigger, transition, style, animate, query, stagger} from '\\@angular/animations';\n * \\@Component({ \n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *        //...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n * \n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n * \n *   hideItems() {\n *     this.items = [];\n *   }\n * \n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *   }\n * }\n * ```\n * \n * And now for the animation trigger code:\n * \n * ```ts\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n * \n * Now each time the items are added/removed then either the opacity\n * fade-in animation will run or each removed item will be faded out.\n * When either of these animations occur then a stagger effect will be\n * applied after each item's animation is started.\n * \n * \\@experimental Animation support is experimental.\n * @param {?} timings\n * @param {?} animation\n * @return {?}\n */\nexport function stagger(\n    timings: string | number,\n    animation: AnimationMetadata | AnimationMetadata[]): AnimationStaggerMetadata {\n  return {type: AnimationMetadataType.Stagger, timings, animation};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationMetadata, AnimationOptions} from './animation_metadata';\nimport {AnimationPlayer} from './players/animation_player';\n/**\n * AnimationBuilder is an injectable service that is available when the {\\@link\n * BrowserAnimationsModule BrowserAnimationsModule} or {\\@link NoopAnimationsModule\n * NoopAnimationsModule} modules are used within an application.\n * \n * The purpose if this service is to produce an animation sequence programmatically within an\n * angular component or directive.\n * \n * Programmatic animations are first built and then a player is created when the build animation is\n * attached to an element.\n * \n * ```ts\n * // remember to include the BrowserAnimationsModule module for this to work...\n * import {AnimationBuilder} from '\\@angular/animations';\n * \n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n * \n *   makeAnimation(element: any) {\n *     // first build the animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n * \n *     // then create a player from it\n *     const player = myAnimation.create(element);\n * \n *     player.play();\n *   }\n * }\n * ```\n * \n * When an animation is built an instance of {\\@link AnimationFactory AnimationFactory} will be\n * returned. Using that an {\\@link AnimationPlayer AnimationPlayer} can be created which can then be\n * used to start the animation.\n * \n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport abstract class AnimationBuilder {\n/**\n * @abstract\n * @param {?} animation\n * @return {?}\n */\nbuild(animation: AnimationMetadata|AnimationMetadata[]) {}\n}\n/**\n * An instance of `AnimationFactory` is returned from {\\@link AnimationBuilder#build\n * AnimationBuilder.build}.\n * \n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport abstract class AnimationFactory {\n/**\n * @abstract\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(element: any, options?: AnimationOptions) {}\n}\n"], "names": [], "mappings": ";;;;;;AGAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KD8EA;;;;;;;;IAAA,OAAA,gBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADpCA,IAAA,mBAAA,IAAA,YAAA;IACA,SAAA,mBAAA,GAAA;QACU,IAAV,CAAA,UAAA,GAAA,EAAA,CAAA;QACS,IAAT,CAAA,WAAA,GAAA,EAAA,CAAA;QACS,IAAT,CAAA,aAAqB,GAArB,EAAA,CAAA;QACA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA;;;;QACA,IAAA,CAAG,SAAH,GAAA,CAAA,CAAA;KAIA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,OAXG,GAWH,UAAA,EAAA,EAAA,EAAA,IAXsC,CAWtC,WAAA,CAAA,IAAA,CAXuD,EAAG,CAW1D,CAX2D,EAW3D,CAAA;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EAfG,EAeH,EAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAf0C,EAe1C,CAAA,CAAA,EAf4D,CAe5D;;;;;;;;;;;;;IAaA,mBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA,GAAA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,EAAA;;;;QAIA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,YAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QA3BqB,iBA2BrB,CA3BsC,YAAtC,EA2BA,OAAA,KAAA,CAAA,SAAA,EAAA,CA3BA,EA2BA,CAAA,CAAA;KAAA,CAAA;;;;;;QAMA,IAAA,CAAA,WAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;IAaA,mBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,YAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAAA,EAAA,CAAA;;;;IAIA,mBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA;QACA,IAAM,CAAN,IAtCW,CAsCX,UAAA,EAAA;YACM,IAAI,CAtCC,UAsCX,GAtCY,IAsCZ,CAAA;YACM,IAAI,CAtCC,IAsCX,CAAA,UAAA,EAAA,EAtC8B;gBAuC9B,IAAA,CAAA,QAAA,EAAA,CAAA;aACA;;;;SArCG;;;;;IA8CH,mBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,YAAA,GA7CG,CA6CH;;;;;IAKA,mBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,CAAA,EAAA,GAAA,CAAA;;;;;;CAnGA,EAAA,CAAA,CAAA;;;;;;;;ADpBA,IAAA,oBAAA,IAAA,YAAA;;;;IAPA,SAAA,oBAAA,CAAA,QAAqB,EAArB;QAAA,IAAA,KAAA,GAAA,IAAA,CA2CA;QA1CU,IAAV,CAAA,QAAA,GAAA,QAA6B,CAA7B;QACU,IAAV,CAAA,UAAA,GAAU,EAAV,CAAsC;QAE7B,IAAT,CAAA,WAAA,GAAA,EAAA,CAAA;QACS,IAAT,CAAA,SAAS,GAAoB,KAA7B,CAAA;QAII,IAAI,CAAR,QAAiB,GAAG,KAApB,CAAA;QACI,IAAI,CAAR,UAAA,GAAA,KAAA,CAAA;QACI,IAAI,CAAR,aAAsB,GAAtB,EAAA,CAAA;QACI,IAAJ,CAAA,YAAA,GAAA,IAAA,CAAA;QAEI,IAAI,CAAR,SAAkB,GAAlB,CAAA,CAAA;QACA,IAAM,SAAN,GAAA,CAAA,CAAA;QACA,IAAA,YAAA,GAAA,CAAA,CAAA;QAAA,IAAA,UAAA,GAAA,CAAA,CAAA;QACA,IAAA,KAAA,GAAA,IAAoB,CAApB,QAAA,CAAA,MAAA,CAAA;QACA,IAAA,KAAA,IAAA,CAAc,EAAd;YACA,iBAAqB,CAAC,YAAtB,EAAA,OAAA,KAAA,CAAA,SAAA,EAAA,CAAA,EAAA,CAAA,CAAA;SACA;aACA;YACA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;gBACA,MAAA,CAAA,YAAA,GAAA,KAAA,CAAA;gBACQ,MAAM,CAAC,MAAf,CAAA,YAAA;oBACU,IAAI,EAAE,SAAhB,IAAA,KAAA,EAAA;wBACY,KAAI,CAAC,SAAjB,EAAA,CAA6B;qBAClB;iBACF,CAAC,CAAC;gBACH,MAAM,CAAC,SAAf,CAAA,YAAA;oBACU,IAAI,EAAE,YAAhB,IAAA,KAAqC,EAArC;wBACY,KAAI,CAAC,UAAU,EAA3B,CAAA;qBACW;iBACF,CAAC,CAAC;gBACX,MAAA,CAAA,OAAA,CAAA,YAAA;oBACA,IAAA,EAAA,UAAA,IAAA,KAAA,EAAA;wBAEA,KAAA,CAAyB,QAAzB,EAAmC,CAAnC;qBACA;;;;QACA,IAAA,CAAG,SAAH,GAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA,MAAA,EAAA,EAAA,OAAA,IAAA,CAAA,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA;KAIA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,EAAA;;;;SAIA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA,EAAA,IAAA,CARkC,QAQlC,CAAA,OARwC,CAAW,UAAA,MAQnD,EAAA,EAAA,OAAA,MAAA,CAAA,IAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,EAAR,EAAA,EAVc,IAUd,CAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,EAAA;;;;;KAKA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,UAAA,EAhBG,EAgBH,EAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAhB0C,EAgB1C,CAAA,CAAA,EAhB4D,CAgB5D;;;;;;;;;IASA,oBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA,EAAA,OAAA,IApBe,CAoBf,QAAA,CAAA,EAAA,CAAA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QACA,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA;;;;QAIA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,MAtBwC,EAsBxC,EAAA,OAAA,MAtBkD,CAsBlD,IAAA,EAtByD,CAsBzD,EAtByD,CAsBzD,CAAA;;;;;;;;;IASA,oBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,EAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA,EAAA,OAAA,MAAA,CAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;;;;;;QAMA,IAAA,CAAA,QA5ByB,CA4BzB,OAAA,CAAA,UAAA,MA5BuC,EA4BvC,EAAA,OAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,EAAA,IAAA,CAAA,UAAA,EAAA,CAAA,EAAA,CAAA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA;QACA,IAAM,CAAN,IA9BW,CA8BX,UAAA,EA9BY;YA+BZ,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA;YACA,IAAA,CAAA,SAAA,EAAA,CAAA;;;;SA5BG;KAiCH,CAAA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,KAAA,GAAA,YAAA;;;;;KAKA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,CAAA,EAAA;QACA,qBAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA;;;;SAIA,CAAA,CAAA;KACA,CAAA;;;;IAIA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;QACI,qBAAJ,GAAA,GAAA,CAAA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;;;;QAnCG,OAAA,GAuCH,CAAA;KACA,CAAA;;;;;;;;;;CAvJA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}