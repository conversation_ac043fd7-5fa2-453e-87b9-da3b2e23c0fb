{"title": "CSS Reflections", "description": "Method of displaying a reflection of an element", "spec": "http://webkit.org/blog/182/css-reflections/", "status": "unoff", "links": [{"url": "http://webkit.org/blog/182/css-reflections/", "title": "Webkit blog post"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/7930470-support-css-reflections", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n"}, "chrome": {"4": "y x", "5": "y x", "6": "y x", "7": "y x", "8": "y x", "9": "y x", "10": "y x", "11": "y x", "12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y x", "52": "y x", "53": "y x", "54": "y x", "55": "y x", "56": "y x", "57": "y x", "58": "y x", "59": "y x", "60": "y x", "61": "y x", "62": "y x"}, "safari": {"3.1": "n", "3.2": "n", "4": "y x", "5": "y x", "5.1": "y x", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x"}, "ios_saf": {"3.2": "y x", "4.0-4.1": "y x", "4.2-4.3": "y x", "5.0-5.1": "y x", "6.0-6.1": "y x", "7.0-7.1": "y x", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y x", "9.3": "y x", "10.0-10.2": "y x", "10.3": "y x", "11": "y x"}, "op_mini": {"all": "n"}, "android": {"2.1": "y x", "2.2": "y x", "2.3": "y x", "3": "y x", "4": "y x", "4.1": "y x", "4.2-4.3": "y x", "4.4": "y x", "4.4.3-4.4.4": "y x", "56": "y x"}, "bb": {"7": "y x", "10": "y x"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y x"}, "and_chr": {"59": "y x"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "y x", "5": "y x"}, "and_qq": {"1.2": "y x"}, "baidu": {"7.12": "y x"}}, "notes": "Similar effect can be achieved in Firefox 4+ using the -moz-element() background property", "notes_by_num": {}, "usage_perc_y": 74.14, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "box-reflect", "ie_id": "", "chrome_id": "5627300510957568", "firefox_id": "", "webkit_id": "", "shown": true}