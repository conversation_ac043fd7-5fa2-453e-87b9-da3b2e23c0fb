/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core"),require("@angular/compiler"),require("@angular/core/testing")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/compiler","@angular/core/testing"],factory):factory((global.ng=global.ng||{},global.ng.compiler=global.ng.compiler||{},global.ng.compiler.testing=global.ng.compiler.testing||{}),global.ng.core,global.ng.compiler,global.ng.core.testing)}(this,function(exports,_angular_core,_angular_compiler,_angular_core_testing){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}function removeMetadata(metadata,remove,references){var removeObjects=new Set,_loop_1=function(prop){var removeValue=remove[prop];removeValue instanceof Array?removeValue.forEach(function(value){removeObjects.add(_propHashKey(prop,value,references))}):removeObjects.add(_propHashKey(prop,removeValue,references))};for(var prop in remove)_loop_1(prop);var _loop_2=function(prop){var propValue=metadata[prop];propValue instanceof Array?metadata[prop]=propValue.filter(function(value){return!removeObjects.has(_propHashKey(prop,value,references))}):removeObjects.has(_propHashKey(prop,propValue,references))&&(metadata[prop]=void 0)};for(var prop in metadata)_loop_2(prop)}function addMetadata(metadata,add){for(var prop in add){var addValue=add[prop],propValue=metadata[prop];null!=propValue&&propValue instanceof Array?metadata[prop]=propValue.concat(addValue):metadata[prop]=addValue}}function setMetadata(metadata,set){for(var prop in set)metadata[prop]=set[prop]}function _propHashKey(propName,propValue,references){var replacer=function(key,value){return"function"==typeof value&&(value=_serializeReference(value,references)),value};return propName+":"+JSON.stringify(propValue,replacer)}function _serializeReference(ref,references){var id=references.get(ref);return id||(id=""+_angular_core.ɵstringify(ref)+_nextReferenceId++,references.set(ref,id)),id}function _valueProps(obj){var props=[];Object.keys(obj).forEach(function(prop){prop.startsWith("_")||props.push(prop)});for(var proto=obj;proto=Object.getPrototypeOf(proto);)Object.keys(proto).forEach(function(protoProp){var desc=Object.getOwnPropertyDescriptor(proto,protoProp);!protoProp.startsWith("_")&&desc&&"get"in desc&&props.push(protoProp)});return props}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},MockSchemaRegistry=function(){function MockSchemaRegistry(existingProperties,attrPropMapping,existingElements,invalidProperties,invalidAttributes){this.existingProperties=existingProperties,this.attrPropMapping=attrPropMapping,this.existingElements=existingElements,this.invalidProperties=invalidProperties,this.invalidAttributes=invalidAttributes}return MockSchemaRegistry.prototype.hasProperty=function(tagName,property,schemas){var value=this.existingProperties[property];return void 0===value||value},MockSchemaRegistry.prototype.hasElement=function(tagName,schemaMetas){var value=this.existingElements[tagName.toLowerCase()];return void 0===value||value},MockSchemaRegistry.prototype.allKnownElementNames=function(){return Object.keys(this.existingElements)},MockSchemaRegistry.prototype.securityContext=function(selector,property,isAttribute){return _angular_core.SecurityContext.NONE},MockSchemaRegistry.prototype.getMappedPropName=function(attrName){return this.attrPropMapping[attrName]||attrName},MockSchemaRegistry.prototype.getDefaultComponentElementName=function(){return"ng-component"},MockSchemaRegistry.prototype.validateProperty=function(name){return this.invalidProperties.indexOf(name)>-1?{error:!0,msg:"Binding to property '"+name+"' is disallowed for security reasons"}:{error:!1}},MockSchemaRegistry.prototype.validateAttribute=function(name){return this.invalidAttributes.indexOf(name)>-1?{error:!0,msg:"Binding to attribute '"+name+"' is disallowed for security reasons"}:{error:!1}},MockSchemaRegistry.prototype.normalizeAnimationStyleProperty=function(propName){return propName},MockSchemaRegistry.prototype.normalizeAnimationStyleValue=function(camelCaseProp,userProvidedProp,val){return{error:null,value:val.toString()}},MockSchemaRegistry}(),MockDirectiveResolver=function(_super){function MockDirectiveResolver(_injector,reflector){var _this=_super.call(this,reflector)||this;return _this._injector=_injector,_this._directives=new Map,_this._providerOverrides=new Map,_this._viewProviderOverrides=new Map,_this._views=new Map,_this._inlineTemplates=new Map,_this}return __extends(MockDirectiveResolver,_super),Object.defineProperty(MockDirectiveResolver.prototype,"_compiler",{get:function(){return this._injector.get(_angular_core.Compiler)},enumerable:!0,configurable:!0}),MockDirectiveResolver.prototype._clearCacheFor=function(component){this._compiler.clearCacheFor(component)},MockDirectiveResolver.prototype.resolve=function(type,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0);var metadata=this._directives.get(type)||null;if(metadata||(metadata=_super.prototype.resolve.call(this,type,throwIfNotFound)),!metadata)return null;var providerOverrides=this._providerOverrides.get(type),viewProviderOverrides=this._viewProviderOverrides.get(type),providers=metadata.providers;if(null!=providerOverrides){var originalViewProviders=metadata.providers||[];providers=originalViewProviders.concat(providerOverrides)}if(metadata instanceof _angular_core.Component){var viewProviders=metadata.viewProviders;if(null!=viewProviderOverrides){var originalViewProviders=metadata.viewProviders||[];viewProviders=originalViewProviders.concat(viewProviderOverrides)}var view=this._views.get(type)||metadata,animations=view.animations,templateUrl=view.templateUrl,inlineTemplate=this._inlineTemplates.get(type);return inlineTemplate?templateUrl=void 0:inlineTemplate=view.template,new _angular_core.Component({selector:metadata.selector,inputs:metadata.inputs,outputs:metadata.outputs,host:metadata.host,exportAs:metadata.exportAs,moduleId:metadata.moduleId,queries:metadata.queries,changeDetection:metadata.changeDetection,providers:providers,viewProviders:viewProviders,entryComponents:metadata.entryComponents,template:inlineTemplate,templateUrl:templateUrl,animations:animations,styles:view.styles,styleUrls:view.styleUrls,encapsulation:view.encapsulation,interpolation:view.interpolation})}return new _angular_core.Directive({selector:metadata.selector,inputs:metadata.inputs,outputs:metadata.outputs,host:metadata.host,providers:providers,exportAs:metadata.exportAs,queries:metadata.queries})},MockDirectiveResolver.prototype.setDirective=function(type,metadata){this._directives.set(type,metadata),this._clearCacheFor(type)},MockDirectiveResolver.prototype.setProvidersOverride=function(type,providers){this._providerOverrides.set(type,providers),this._clearCacheFor(type)},MockDirectiveResolver.prototype.setViewProvidersOverride=function(type,viewProviders){this._viewProviderOverrides.set(type,viewProviders),this._clearCacheFor(type)},MockDirectiveResolver.prototype.setView=function(component,view){this._views.set(component,view),this._clearCacheFor(component)},MockDirectiveResolver.prototype.setInlineTemplate=function(component,template){this._inlineTemplates.set(component,template),this._clearCacheFor(component)},MockDirectiveResolver}(_angular_compiler.DirectiveResolver);MockDirectiveResolver.decorators=[{type:_angular_core.Injectable}],MockDirectiveResolver.ctorParameters=function(){return[{type:_angular_core.Injector},{type:_angular_compiler.CompileReflector}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var MockNgModuleResolver=function(_super){function MockNgModuleResolver(_injector,reflector){var _this=_super.call(this,reflector)||this;return _this._injector=_injector,_this._ngModules=new Map,_this}return __extends(MockNgModuleResolver,_super),MockNgModuleResolver.prototype.setNgModule=function(type,metadata){this._ngModules.set(type,metadata),this._clearCacheFor(type)},MockNgModuleResolver.prototype.resolve=function(type,throwIfNotFound){return void 0===throwIfNotFound&&(throwIfNotFound=!0),this._ngModules.get(type)||_super.prototype.resolve.call(this,type,throwIfNotFound)},Object.defineProperty(MockNgModuleResolver.prototype,"_compiler",{get:function(){return this._injector.get(_angular_core.Compiler)},enumerable:!0,configurable:!0}),MockNgModuleResolver.prototype._clearCacheFor=function(component){this._compiler.clearCacheFor(component)},MockNgModuleResolver}(_angular_compiler.NgModuleResolver);MockNgModuleResolver.decorators=[{type:_angular_core.Injectable}],MockNgModuleResolver.ctorParameters=function(){return[{type:_angular_core.Injector},{type:_angular_compiler.CompileReflector}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var MockPipeResolver=function(_super){function MockPipeResolver(_injector,refector){var _this=_super.call(this,refector)||this;return _this._injector=_injector,_this._pipes=new Map,_this}return __extends(MockPipeResolver,_super),Object.defineProperty(MockPipeResolver.prototype,"_compiler",{get:function(){return this._injector.get(_angular_core.Compiler)},enumerable:!0,configurable:!0}),MockPipeResolver.prototype._clearCacheFor=function(pipe){this._compiler.clearCacheFor(pipe)},MockPipeResolver.prototype.setPipe=function(type,metadata){this._pipes.set(type,metadata),this._clearCacheFor(type)},MockPipeResolver.prototype.resolve=function(type,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0);var metadata=this._pipes.get(type);return metadata||(metadata=_super.prototype.resolve.call(this,type,throwIfNotFound)),metadata},MockPipeResolver}(_angular_compiler.PipeResolver);MockPipeResolver.decorators=[{type:_angular_core.Injectable}],MockPipeResolver.ctorParameters=function(){return[{type:_angular_core.Injector},{type:_angular_compiler.CompileReflector}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var _nextReferenceId=0,MetadataOverrider=function(){function MetadataOverrider(){this._references=new Map}return MetadataOverrider.prototype.overrideMetadata=function(metadataClass,oldMetadata,override){var props={};if(oldMetadata&&_valueProps(oldMetadata).forEach(function(prop){return props[prop]=oldMetadata[prop]}),override.set){if(override.remove||override.add)throw new Error("Cannot set and add/remove "+_angular_core.ɵstringify(metadataClass)+" at the same time!");setMetadata(props,override.set)}return override.remove&&removeMetadata(props,override.remove,this._references),override.add&&addMetadata(props,override.add),new metadataClass(props)},MetadataOverrider}(),TestingCompilerFactoryImpl=function(){function TestingCompilerFactoryImpl(_compilerFactory){this._compilerFactory=_compilerFactory}return TestingCompilerFactoryImpl.prototype.createTestingCompiler=function(options){var compiler=this._compilerFactory.createCompiler(options);return new TestingCompilerImpl(compiler,compiler.injector.get(MockDirectiveResolver),compiler.injector.get(MockPipeResolver),compiler.injector.get(MockNgModuleResolver),compiler.injector.get(_angular_compiler.CompileMetadataResolver))},TestingCompilerFactoryImpl}();TestingCompilerFactoryImpl.decorators=[{type:_angular_core.Injectable}],TestingCompilerFactoryImpl.ctorParameters=function(){return[{type:_angular_core.CompilerFactory}]};var TestingCompilerImpl=function(){function TestingCompilerImpl(_compiler,_directiveResolver,_pipeResolver,_moduleResolver,_metadataResolver){this._compiler=_compiler,this._directiveResolver=_directiveResolver,this._pipeResolver=_pipeResolver,this._moduleResolver=_moduleResolver,this._metadataResolver=_metadataResolver,this._overrider=new MetadataOverrider}return Object.defineProperty(TestingCompilerImpl.prototype,"injector",{get:function(){return this._compiler.injector},enumerable:!0,configurable:!0}),TestingCompilerImpl.prototype.compileModuleSync=function(moduleType){return this._compiler.compileModuleSync(moduleType)},TestingCompilerImpl.prototype.compileModuleAsync=function(moduleType){return this._compiler.compileModuleAsync(moduleType)},TestingCompilerImpl.prototype.compileModuleAndAllComponentsSync=function(moduleType){return this._compiler.compileModuleAndAllComponentsSync(moduleType)},TestingCompilerImpl.prototype.compileModuleAndAllComponentsAsync=function(moduleType){return this._compiler.compileModuleAndAllComponentsAsync(moduleType)},TestingCompilerImpl.prototype.getNgContentSelectors=function(component){return this._compiler.getNgContentSelectors(component)},TestingCompilerImpl.prototype.getComponentFactory=function(component){return this._compiler.getComponentFactory(component)},TestingCompilerImpl.prototype.checkOverrideAllowed=function(type){if(this._compiler.hasAotSummary(type))throw new Error(_angular_core.ɵstringify(type)+" was AOT compiled, so its metadata cannot be changed.")},TestingCompilerImpl.prototype.overrideModule=function(ngModule,override){this.checkOverrideAllowed(ngModule);var oldMetadata=this._moduleResolver.resolve(ngModule,!1);this._moduleResolver.setNgModule(ngModule,this._overrider.overrideMetadata(_angular_core.NgModule,oldMetadata,override))},TestingCompilerImpl.prototype.overrideDirective=function(directive,override){this.checkOverrideAllowed(directive);var oldMetadata=this._directiveResolver.resolve(directive,!1);this._directiveResolver.setDirective(directive,this._overrider.overrideMetadata(_angular_core.Directive,oldMetadata,override))},TestingCompilerImpl.prototype.overrideComponent=function(component,override){this.checkOverrideAllowed(component);var oldMetadata=this._directiveResolver.resolve(component,!1);this._directiveResolver.setDirective(component,this._overrider.overrideMetadata(_angular_core.Component,oldMetadata,override))},TestingCompilerImpl.prototype.overridePipe=function(pipe,override){this.checkOverrideAllowed(pipe);var oldMetadata=this._pipeResolver.resolve(pipe,!1);this._pipeResolver.setPipe(pipe,this._overrider.overrideMetadata(_angular_core.Pipe,oldMetadata,override))},TestingCompilerImpl.prototype.loadAotSummaries=function(summaries){this._compiler.loadAotSummaries(summaries)},TestingCompilerImpl.prototype.clearCache=function(){this._compiler.clearCache()},TestingCompilerImpl.prototype.clearCacheFor=function(type){this._compiler.clearCacheFor(type)},TestingCompilerImpl}(),platformCoreDynamicTesting=_angular_core.createPlatformFactory(_angular_compiler.platformCoreDynamic,"coreDynamicTesting",[{provide:_angular_core.COMPILER_OPTIONS,useValue:{providers:[MockPipeResolver,{provide:_angular_compiler.PipeResolver,useExisting:MockPipeResolver},MockDirectiveResolver,{provide:_angular_compiler.DirectiveResolver,useExisting:MockDirectiveResolver},MockNgModuleResolver,{provide:_angular_compiler.NgModuleResolver,useExisting:MockNgModuleResolver}]},multi:!0},{provide:_angular_core_testing.ɵTestingCompilerFactory,useClass:TestingCompilerFactoryImpl}]);exports.TestingCompilerFactoryImpl=TestingCompilerFactoryImpl,exports.TestingCompilerImpl=TestingCompilerImpl,exports.platformCoreDynamicTesting=platformCoreDynamicTesting,exports.MockSchemaRegistry=MockSchemaRegistry,exports.MockDirectiveResolver=MockDirectiveResolver,exports.MockNgModuleResolver=MockNgModuleResolver,exports.MockPipeResolver=MockPipeResolver,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=compiler-testing.umd.min.js.map
