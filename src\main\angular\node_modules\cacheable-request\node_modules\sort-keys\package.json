{"_args": [["sort-keys@2.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "sort-keys@2.0.0", "_id": "sort-keys@2.0.0", "_inBundle": false, "_integrity": "sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=", "_location": "/cacheable-request/sort-keys", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "sort-keys@2.0.0", "name": "sort-keys", "escapedName": "sort-keys", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/cacheable-request/normalize-url"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/sort-keys/-/sort-keys-2.0.0.tgz", "_spec": "2.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/sort-keys/issues"}, "dependencies": {"is-plain-obj": "^1.0.0"}, "description": "Sort the keys of an object", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/sort-keys#readme", "keywords": ["sort", "object", "keys", "obj", "key", "stable", "deterministic", "deep", "recursive", "recursively"], "license": "MIT", "name": "sort-keys", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/sort-keys.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}