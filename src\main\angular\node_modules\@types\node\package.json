{"_args": [["@types/node@6.0.79", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@types/node@6.0.79", "_id": "@types/node@6.0.79", "_inBundle": false, "_integrity": "sha512-7F3/P6MkTPA0QxOstRqfcnoReCUy5V/QG92cyBoZSPnqdX44L8TtNELSVfN56gAttm3YWj9cEi8FRIPVq0WmeQ==", "_location": "/@types/node", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/node@6.0.79", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "6.0.79", "saveSpec": null, "fetchSpec": "6.0.79"}, "_requiredBy": ["/", "/protractor"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/node/-/node-6.0.79.tgz", "_spec": "6.0.79", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "Microsoft TypeScript", "url": "http://typescriptlang.org"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}], "dependencies": {}, "description": "TypeScript definitions for Node.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/node", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.0", "typesPublisherContentHash": "a787df23164e625f92729cc98509600c21efabd96916c7b42e9e0c57525e91d1", "version": "6.0.79"}