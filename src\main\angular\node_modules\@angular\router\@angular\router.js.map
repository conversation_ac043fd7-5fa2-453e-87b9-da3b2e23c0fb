{"version": 3, "file": "router.js", "sources": ["../../../../packages/router/index.ts", "../../../../packages/router/public_api.ts", "../../../../packages/router/src/index.ts", "../../../../packages/router/src/private_export.ts", "../../../../packages/router/src/version.ts", "../../../../packages/router/src/router_module.ts", "../../../../packages/router/src/router_preloader.ts", "../../../../packages/router/src/directives/router_outlet.ts", "../../../../packages/router/src/router_outlet_context.ts", "../../../../packages/router/src/directives/router_link_active.ts", "../../../../packages/router/src/directives/router_link.ts", "../../../../packages/router/src/router.ts", "../../../../packages/router/src/url_handling_strategy.ts", "../../../../packages/router/src/router_config_loader.ts", "../../../../packages/router/src/route_reuse_strategy.ts", "../../../../packages/router/src/recognize.ts", "../../../../packages/router/src/create_url_tree.ts", "../../../../packages/router/src/create_router_state.ts", "../../../../packages/router/src/router_state.ts", "../../../../packages/router/src/utils/tree.ts", "../../../../packages/router/src/apply_redirects.ts", "../../../../packages/router/src/url_tree.ts", "../../../../packages/router/src/utils/collection.ts", "../../../../packages/router/src/config.ts", "../../../../packages/router/src/shared.ts", "../../../../packages/router/src/events.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport {Data,LoadChildren,LoadChildrenCallback,ResolveData,Route,Routes,RunGuardsAndResolvers,RouterLink,RouterLinkWithHref,RouterLinkActive,RouterOutlet,Event,NavigationCancel,NavigationEnd,NavigationError,NavigationStart,RouteConfigLoadEnd,RouteConfigLoadStart,RoutesRecognized,CanActivate,CanActivateChild,CanDeactivate,CanLoad,Resolve,DetachedRouteHandle,RouteReuseStrategy,NavigationExtras,Router,ROUTES,ExtraOptions,ROUTER_CONFIGURATION,ROUTER_INITIALIZER,RouterModule,provideRoutes,ChildrenOutletContexts,OutletContext,NoPreloading,PreloadAllModules,PreloadingStrategy,RouterPreloader,ActivatedRoute,ActivatedRouteSnapshot,RouterState,RouterStateSnapshot,PRIMARY_OUTLET,ParamMap,Params,convertToParamMap,UrlHandlingStrategy,DefaultUrlSerializer,UrlSegment,UrlSegmentGroup,UrlSerializer,UrlTree,VERSION,ɵROUTER_PROVIDERS,ɵflatten} from './public_api';\n\nexport {ROUTER_FORROOT_GUARD as ɵa,RouterInitializer as ɵg,getAppInitializer as ɵh,getBootstrapListener as ɵi,provideForRootGuard as ɵd,provideLocationStrategy as ɵc,provideRouterInitializer as ɵj,rootRoute as ɵf,routerNgProbeToken as ɵb,setupRouter as ɵe} from './src/router_module';\nexport {Tree as ɵk,TreeNode as ɵl} from './src/utils/tree';", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router package.\n */\nexport {Data,LoadChildren,LoadChildrenCallback,ResolveData,Route,Routes,RunGuardsAndResolvers,RouterLink,RouterLinkWithHref,RouterLinkActive,RouterOutlet,Event,NavigationCancel,NavigationEnd,NavigationError,NavigationStart,RouteConfigLoadEnd,RouteConfigLoadStart,RoutesRecognized,CanActivate,CanActivateChild,CanDeactivate,CanLoad,Resolve,DetachedRouteHandle,RouteReuseStrategy,NavigationExtras,Router,ROUTES,ExtraOptions,ROUTER_CONFIGURATION,ROUTER_INITIALIZER,RouterModule,provideRoutes,ChildrenOutletContexts,OutletContext,NoPreloading,PreloadAllModules,PreloadingStrategy,RouterPreloader,ActivatedRoute,ActivatedRouteSnapshot,RouterState,RouterStateSnapshot,PRIMARY_OUTLET,ParamMap,Params,convertToParamMap,UrlHandlingStrategy,DefaultUrlSerializer,UrlSegment,UrlSegmentGroup,UrlSerializer,UrlTree,VERSION,ɵROUTER_PROVIDERS,ɵflatten} from './src/index';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nexport {Data, LoadChildren, LoadChildrenCallback, ResolveData, Route, Routes, RunGuardsAndResolvers} from './config';\nexport {RouterLink, RouterLinkWithHref} from './directives/router_link';\nexport {RouterLinkActive} from './directives/router_link_active';\nexport {RouterOutlet} from './directives/router_outlet';\nexport {Event, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, RouteConfigLoadEnd, RouteConfigLoadStart, RoutesRecognized} from './events';\nexport {CanActivate, CanActivateChild, CanDeactivate, CanLoad, Resolve} from './interfaces';\nexport {DetachedRouteHandle, RouteReuseStrategy} from './route_reuse_strategy';\nexport {NavigationExtras, Router} from './router';\nexport {ROUTES} from './router_config_loader';\nexport {ExtraOptions, ROUTER_CONFIGURATION, ROUTER_INITIALIZER, RouterModule, provideRoutes} from './router_module';\nexport {ChildrenOutletContexts, OutletContext} from './router_outlet_context';\nexport {NoPreloading, PreloadAllModules, PreloadingStrategy, RouterPreloader} from './router_preloader';\nexport {ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot} from './router_state';\nexport {PRIMARY_OUTLET, ParamMap, Params, convertToParamMap} from './shared';\nexport {UrlHandlingStrategy} from './url_handling_strategy';\nexport {DefaultUrlSerializer, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree} from './url_tree';\nexport {VERSION} from './version';\n\nexport {ɵROUTER_PROVIDERS,ɵflatten} from './private_export';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nexport {ROUTER_PROVIDERS as ɵROUTER_PROVIDERS} from './router_module';\nexport {flatten as ɵflatten} from './utils/collection';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {APP_BASE_HREF, HashLocationStrategy, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation} from '@angular/common';\nimport {ANALYZE_FOR_ENTRY_COMPONENTS, APP_BOOTSTRAP_LISTENER, APP_INITIALIZER, ApplicationRef, Compiler, ComponentRef, Inject, Injectable, InjectionToken, Injector, ModuleWithProviders, NgModule, NgModuleFactoryLoader, NgProbeToken, Optional, Provider, SkipSelf, SystemJsNgModuleLoader} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {Subject} from 'rxjs/Subject';\nimport {of } from 'rxjs/observable/of';\n\nimport {Route, Routes} from './config';\nimport {RouterLink, RouterLinkWithHref} from './directives/router_link';\nimport {RouterLinkActive} from './directives/router_link_active';\nimport {RouterOutlet} from './directives/router_outlet';\nimport {RouteReuseStrategy} from './route_reuse_strategy';\nimport {ErrorHandler, Router} from './router';\nimport {ROUTES} from './router_config_loader';\nimport {ChildrenOutletContexts} from './router_outlet_context';\nimport {NoPreloading, PreloadAllModules, PreloadingStrategy, RouterPreloader} from './router_preloader';\nimport {ActivatedRoute} from './router_state';\nimport {UrlHandlingStrategy} from './url_handling_strategy';\nimport {DefaultUrlSerializer, UrlSerializer} from './url_tree';\nimport {flatten} from './utils/collection';\n/**\n * \\@whatItDoes Contains a list of directives\n * \\@stable\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkWithHref, RouterLinkActive];\n/**\n * \\@whatItDoes Is used in DI to configure the router.\n * \\@stable\n */\nexport const ROUTER_CONFIGURATION = new InjectionToken<ExtraOptions>('ROUTER_CONFIGURATION');\n/**\n * \\@docsNotRequired\n */\nexport const ROUTER_FORROOT_GUARD = new InjectionToken<void>('ROUTER_FORROOT_GUARD');\n\nexport const /** @type {?} */ ROUTER_PROVIDERS: Provider[] = [\n  Location,\n  {provide: UrlSerializer, useClass: DefaultUrlSerializer},\n  {\n    provide: Router,\n    useFactory: setupRouter,\n    deps: [\n      ApplicationRef, UrlSerializer, ChildrenOutletContexts, Location, Injector,\n      NgModuleFactoryLoader, Compiler, ROUTES, ROUTER_CONFIGURATION,\n      [UrlHandlingStrategy, new Optional()], [RouteReuseStrategy, new Optional()]\n    ]\n  },\n  ChildrenOutletContexts,\n  {provide: ActivatedRoute, useFactory: rootRoute, deps: [Router]},\n  {provide: NgModuleFactoryLoader, useClass: SystemJsNgModuleLoader},\n  RouterPreloader,\n  NoPreloading,\n  PreloadAllModules,\n  {provide: ROUTER_CONFIGURATION, useValue: {enableTracing: false}},\n];\n/**\n * @return {?}\n */\nexport function routerNgProbeToken() {\n  return new NgProbeToken('Router', Router);\n}\n/**\n * \\@whatItDoes Adds router directives and providers.\n * \n * \\@howToUse \n * \n * RouterModule can be imported multiple times: once per lazily-loaded bundle.\n * Since the router deals with a global shared resource--location, we cannot have\n * more than one router service active.\n * \n * That is why there are two ways to create the module: `RouterModule.forRoot` and\n * `RouterModule.forChild`.\n * \n * * `forRoot` creates a module that contains all the directives, the given routes, and the router\n *   service itself.\n * * `forChild` creates a module that contains all the directives and the given routes, but does not\n *   include the router service.\n * \n * When registered at the root, the module should be used as follows\n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forRoot(ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * For submodules and lazy loaded submodules the module should be used as follows:\n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forChild(ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * \\@description \n * \n * Managing state transitions is one of the hardest parts of building applications. This is\n * especially true on the web, where you also need to ensure that the state is reflected in the URL.\n * In addition, we often want to split applications into multiple bundles and load them on demand.\n * Doing this transparently is not trivial.\n * \n * The Angular router solves these problems. Using the router, you can declaratively specify\n * application states, manage state transitions while taking care of the URL, and load bundles on\n * demand.\n * \n * [Read this developer guide](https://angular.io/docs/ts/latest/guide/router.html) to get an\n * overview of how the router should be used.\n * \n * \\@stable\n */\nexport class RouterModule {\n/**\n * @param {?} guard\n * @param {?} router\n */\nconstructor(  guard: any,  router: Router) {}\n/**\n * Creates a module with all the router providers and directives. It also optionally sets up an\n * application listener to perform an initial navigation.\n * \n * Options:\n * * `enableTracing` makes the router log all its internal events to the console.\n * * `useHash` enables the location strategy that uses the URL fragment instead of the history\n * API.\n * * `initialNavigation` disables the initial navigation.\n * * `errorHandler` provides a custom error handler.\n * @param {?} routes\n * @param {?=} config\n * @return {?}\n */\nstatic forRoot(routes: Routes, config?: ExtraOptions): ModuleWithProviders {\n    return {\n      ngModule: RouterModule,\n      providers: [\n        ROUTER_PROVIDERS,\n        provideRoutes(routes),\n        {\n          provide: ROUTER_FORROOT_GUARD,\n          useFactory: provideForRootGuard,\n          deps: [[Router, new Optional(), new SkipSelf()]]\n        },\n        {provide: ROUTER_CONFIGURATION, useValue: config ? config : {}},\n        {\n          provide: LocationStrategy,\n          useFactory: provideLocationStrategy,\n          deps: [\n            PlatformLocation, [new Inject(APP_BASE_HREF), new Optional()], ROUTER_CONFIGURATION\n          ]\n        },\n        {\n          provide: PreloadingStrategy,\n          useExisting: config && config.preloadingStrategy ? config.preloadingStrategy :\n                                                             NoPreloading\n        },\n        {provide: NgProbeToken, multi: true, useFactory: routerNgProbeToken},\n        provideRouterInitializer(),\n      ],\n    };\n  }\n/**\n * Creates a module with all the router directives and a provider registering routes.\n * @param {?} routes\n * @return {?}\n */\nstatic forChild(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{declarations: ROUTER_DIRECTIVES, exports: ROUTER_DIRECTIVES}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [ROUTER_FORROOT_GUARD, ] }, ]},\n{type: Router, decorators: [{ type: Optional }, ]},\n];\n}\n\nfunction RouterModule_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterModule.ctorParameters;\n}\n\n/**\n * @param {?} platformLocationStrategy\n * @param {?} baseHref\n * @param {?=} options\n * @return {?}\n */\nexport function provideLocationStrategy(\n    platformLocationStrategy: PlatformLocation, baseHref: string, options: ExtraOptions = {}) {\n  return options.useHash ? new HashLocationStrategy(platformLocationStrategy, baseHref) :\n                           new PathLocationStrategy(platformLocationStrategy, baseHref);\n}\n/**\n * @param {?} router\n * @return {?}\n */\nexport function provideForRootGuard(router: Router): any {\n  if (router) {\n    throw new Error(\n        `RouterModule.forRoot() called twice. Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n/**\n * \\@whatItDoes Registers routes.\n * \n * \\@howToUse \n * \n * ```\n * \\@NgModule({ \n *   imports: [RouterModule.forChild(ROUTES)],\n *   providers: [provideRoutes(EXTRA_ROUTES)]\n * })\n * class MyNgModule {}\n * ```\n * \n * \\@stable\n * @param {?} routes\n * @return {?}\n */\nexport function provideRoutes(routes: Routes): any {\n  return [\n    {provide: ANALYZE_FOR_ENTRY_COMPONENTS, multi: true, useValue: routes},\n    {provide: ROUTES, multi: true, useValue: routes},\n  ];\n}\n\n/**\n * @whatItDoes Represents an option to configure when the initial navigation is performed.\n *\n * @description\n * * 'enabled' - the initial navigation starts before the root component is created.\n * The bootstrap is blocked until the initial navigation is complete.\n * * 'disabled' - the initial navigation is not performed. The location listener is set up before\n * the root component gets created.\n * * 'legacy_enabled'- the initial navigation starts after the root component has been created.\n * The bootstrap is not blocked until the initial navigation is complete. @deprecated\n * * 'legacy_disabled'- the initial navigation is not performed. The location listener is set up\n * after @deprecated\n * the root component gets created.\n * * `true` - same as 'legacy_enabled'. @deprecated since v4\n * * `false` - same as 'legacy_disabled'. @deprecated since v4\n *\n * The 'enabled' option should be used for applications unless there is a reason to have\n * more control over when the router starts its initial navigation due to some complex\n * initialization logic. In this case, 'disabled' should be used.\n *\n * The 'legacy_enabled' and 'legacy_disabled' should not be used for new applications.\n *\n * @experimental\n */\nexport type InitialNavigation =\n    true | false | 'enabled' | 'disabled' | 'legacy_enabled' | 'legacy_disabled';\n\n/**\n * @whatItDoes Represents options to configure the router.\n *\n * @stable\n */\nexport interface ExtraOptions {\n  /**\n   * Makes the router log all its internal events to the console.\n   */\n  enableTracing?: boolean;\n\n  /**\n   * Enables the location strategy that uses the URL fragment instead of the history API.\n   */\n  useHash?: boolean;\n\n  /**\n   * Disables the initial navigation.\n   */\n  initialNavigation?: InitialNavigation;\n\n  /**\n   * A custom error handler.\n   */\n  errorHandler?: ErrorHandler;\n\n  /**\n   * Configures a preloading strategy. See {@link PreloadAllModules}.\n   */\n  preloadingStrategy?: any;\n}\n/**\n * @param {?} ref\n * @param {?} urlSerializer\n * @param {?} contexts\n * @param {?} location\n * @param {?} injector\n * @param {?} loader\n * @param {?} compiler\n * @param {?} config\n * @param {?=} opts\n * @param {?=} urlHandlingStrategy\n * @param {?=} routeReuseStrategy\n * @return {?}\n */\nexport function setupRouter(\n    ref: ApplicationRef, urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts,\n    location: Location, injector: Injector, loader: NgModuleFactoryLoader, compiler: Compiler,\n    config: Route[][], opts: ExtraOptions = {}, urlHandlingStrategy?: UrlHandlingStrategy,\n    routeReuseStrategy?: RouteReuseStrategy) {\n  const /** @type {?} */ router = new Router(\n      null, urlSerializer, contexts, location, injector, loader, compiler, flatten(config));\n\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n\n  if (routeReuseStrategy) {\n    router.routeReuseStrategy = routeReuseStrategy;\n  }\n\n  if (opts.errorHandler) {\n    router.errorHandler = opts.errorHandler;\n  }\n\n  if (opts.enableTracing) {\n    const /** @type {?} */ dom = getDOM();\n    router.events.subscribe(e => {\n      dom.logGroup(`Router Event: ${( /** @type {?} */((<any>e.constructor))).name}`);\n      dom.log(e.toString());\n      dom.log(e);\n      dom.logGroupEnd();\n    });\n  }\n\n  return router;\n}\n/**\n * @param {?} router\n * @return {?}\n */\nexport function rootRoute(router: Router): ActivatedRoute {\n  return router.routerState.root;\n}\n/**\n * To initialize the router properly we need to do in two steps:\n * \n * We need to start the navigation in a APP_INITIALIZER to block the bootstrap if\n * a resolver or a guards executes asynchronously. Second, we need to actually run\n * activation in a BOOTSTRAP_LISTENER. We utilize the afterPreactivation\n * hook provided by the router to do that.\n * \n * The router navigation starts, reaches the point when preactivation is done, and then\n * pauses. It waits for the hook to be resolved. We then resolve it only in a bootstrap listener.\n */\nexport class RouterInitializer {\nprivate initNavigation: boolean = false;\nprivate resultOfPreactivationDone = new Subject<void>();\n/**\n * @param {?} injector\n */\nconstructor(private injector: Injector) {}\n/**\n * @return {?}\n */\nappInitializer(): Promise<any> {\n    const /** @type {?} */ p: Promise<any> = this.injector.get(LOCATION_INITIALIZED, Promise.resolve(null));\n    return p.then(() => {\n      let /** @type {?} */ resolve: Function = /** @type {?} */(( null));\n      const /** @type {?} */ res = new Promise(r => resolve = r);\n      const /** @type {?} */ router = this.injector.get(Router);\n      const /** @type {?} */ opts = this.injector.get(ROUTER_CONFIGURATION);\n\n      if (this.isLegacyDisabled(opts) || this.isLegacyEnabled(opts)) {\n        resolve(true);\n\n      } else if (opts.initialNavigation === 'disabled') {\n        router.setUpLocationChangeListener();\n        resolve(true);\n\n      } else if (opts.initialNavigation === 'enabled') {\n        router.hooks.afterPreactivation = () => {\n          // only the initial navigation should be delayed\n          if (!this.initNavigation) {\n            this.initNavigation = true;\n            resolve(true);\n            return this.resultOfPreactivationDone;\n\n            // subsequent navigations should not be delayed\n          } else {\n            return /** @type {?} */(( of (null) as any));\n          }\n        };\n        router.initialNavigation();\n\n      } else {\n        throw new Error(`Invalid initialNavigation options: '${opts.initialNavigation}'`);\n      }\n\n      return res;\n    });\n  }\n/**\n * @param {?} bootstrappedComponentRef\n * @return {?}\n */\nbootstrapListener(bootstrappedComponentRef: ComponentRef<any>): void {\n    const /** @type {?} */ opts = this.injector.get(ROUTER_CONFIGURATION);\n    const /** @type {?} */ preloader = this.injector.get(RouterPreloader);\n    const /** @type {?} */ router = this.injector.get(Router);\n    const /** @type {?} */ ref = this.injector.get(ApplicationRef);\n\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n\n    if (this.isLegacyEnabled(opts)) {\n      router.initialNavigation();\n    } else if (this.isLegacyDisabled(opts)) {\n      router.setUpLocationChangeListener();\n    }\n\n    preloader.setUpPreloading();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    this.resultOfPreactivationDone.next( /** @type {?} */((null)));\n    this.resultOfPreactivationDone.complete();\n  }\n/**\n * @param {?} opts\n * @return {?}\n */\nprivate isLegacyEnabled(opts: ExtraOptions): boolean {\n    return opts.initialNavigation === 'legacy_enabled' || opts.initialNavigation === true ||\n        opts.initialNavigation === undefined;\n  }\n/**\n * @param {?} opts\n * @return {?}\n */\nprivate isLegacyDisabled(opts: ExtraOptions): boolean {\n    return opts.initialNavigation === 'legacy_disabled' || opts.initialNavigation === false;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n];\n}\n\nfunction RouterInitializer_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterInitializer.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterInitializer.ctorParameters;\n/** @type {?} */\nRouterInitializer.prototype.initNavigation;\n/** @type {?} */\nRouterInitializer.prototype.resultOfPreactivationDone;\n/** @type {?} */\nRouterInitializer.prototype.injector;\n}\n\n/**\n * @param {?} r\n * @return {?}\n */\nexport function getAppInitializer(r: RouterInitializer) {\n  return r.appInitializer.bind(r);\n}\n/**\n * @param {?} r\n * @return {?}\n */\nexport function getBootstrapListener(r: RouterInitializer) {\n  return r.bootstrapListener.bind(r);\n}\n/**\n * A token for the router initializer that will be called after the app is bootstrapped.\n * \n * \\@experimental\n */\nexport const ROUTER_INITIALIZER =\n    new InjectionToken<(compRef: ComponentRef<any>) => void>('Router Initializer');\n/**\n * @return {?}\n */\nexport function provideRouterInitializer() {\n  return [\n    RouterInitializer,\n    {\n      provide: APP_INITIALIZER,\n      multi: true,\n      useFactory: getAppInitializer,\n      deps: [RouterInitializer]\n    },\n    {provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener, deps: [RouterInitializer]},\n    {provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER},\n  ];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n*@license\n*Copyright Google Inc. All Rights Reserved.\n*\n*Use of this source code is governed by an MIT-style license that can be\n*found in the LICENSE file at https://angular.io/license\n*/\n\n\nimport {Compiler, Injectable, Injector, NgModuleFactoryLoader, NgModuleRef, OnDestroy} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Subscription} from 'rxjs/Subscription';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {_catch} from 'rxjs/operator/catch';\nimport {concatMap} from 'rxjs/operator/concatMap';\nimport {filter} from 'rxjs/operator/filter';\nimport {mergeAll} from 'rxjs/operator/mergeAll';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {LoadedRouterConfig, Route, Routes} from './config';\nimport {Event, NavigationEnd, RouteConfigLoadEnd, RouteConfigLoadStart} from './events';\nimport {Router} from './router';\nimport {RouterConfigLoader} from './router_config_loader';\n/**\n * \\@whatItDoes Provides a preloading strategy.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class PreloadingStrategy {\n/**\n * @abstract\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>) {}\n}\n/**\n * \\@whatItDoes Provides a preloading strategy that preloads all modules as quickly as possible.\n * \n * \\@howToUse \n * \n * ```\n * RouteModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n * \n * \\@experimental\n */\nexport class PreloadAllModules implements PreloadingStrategy {\n/**\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>): Observable<any> {\n    return _catch.call(fn(), () => of (null));\n  }\n}\n/**\n * \\@whatItDoes Provides a preloading strategy that does not preload any modules.\n * \n * \\@description \n * \n * This strategy is enabled by default.\n * \n * \\@experimental\n */\nexport class NoPreloading implements PreloadingStrategy {\n/**\n * @param {?} route\n * @param {?} fn\n * @return {?}\n */\npreload(route: Route, fn: () => Observable<any>): Observable<any> { return of (null); }\n}\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n * \n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n * \n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n * \n * \\@stable\n */\nexport class RouterPreloader implements OnDestroy {\nprivate loader: RouterConfigLoader;\nprivate subscription: Subscription;\n/**\n * @param {?} router\n * @param {?} moduleLoader\n * @param {?} compiler\n * @param {?} injector\n * @param {?} preloadingStrategy\n */\nconstructor(\nprivate router: Router, moduleLoader: NgModuleFactoryLoader, compiler: Compiler,\nprivate injector: Injector,\nprivate preloadingStrategy: PreloadingStrategy) {\n    const onStartLoad = (r: Route) => router.triggerEvent(new RouteConfigLoadStart(r));\n    const onEndLoad = (r: Route) => router.triggerEvent(new RouteConfigLoadEnd(r));\n\n    this.loader = new RouterConfigLoader(moduleLoader, compiler, onStartLoad, onEndLoad);\n  };\n/**\n * @return {?}\n */\nsetUpPreloading(): void {\n    const /** @type {?} */ navigations$ = filter.call(this.router.events, (e: Event) => e instanceof NavigationEnd);\n    this.subscription = concatMap.call(navigations$, () => this.preload()).subscribe(() => {});\n  }\n/**\n * @return {?}\n */\npreload(): Observable<any> {\n    const /** @type {?} */ ngModule = this.injector.get(NgModuleRef);\n    return this.processRoutes(ngModule, this.router.config);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.subscription.unsubscribe(); }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @return {?}\n */\nprivate processRoutes(ngModule: NgModuleRef<any>, routes: Routes): Observable<void> {\n    const /** @type {?} */ res: Observable<any>[] = [];\n    for (const /** @type {?} */ route of routes) {\n      // we already have the config loaded, just recurse\n      if (route.loadChildren && !route.canLoad && route._loadedConfig) {\n        const /** @type {?} */ childConfig = route._loadedConfig;\n        res.push(this.processRoutes(childConfig.module, childConfig.routes));\n\n        // no config loaded, fetch the config\n      } else if (route.loadChildren && !route.canLoad) {\n        res.push(this.preloadConfig(ngModule, route));\n\n        // recurse into children\n      } else if (route.children) {\n        res.push(this.processRoutes(ngModule, route.children));\n      }\n    }\n    return mergeAll.call(from(res));\n  }\n/**\n * @param {?} ngModule\n * @param {?} route\n * @return {?}\n */\nprivate preloadConfig(ngModule: NgModuleRef<any>, route: Route): Observable<void> {\n    return this.preloadingStrategy.preload(route, () => {\n      const /** @type {?} */ loaded$ = this.loader.load(ngModule.injector, route);\n      return mergeMap.call(loaded$, (config: LoadedRouterConfig) => {\n        route._loadedConfig = config;\n        return this.processRoutes(config.module, config.routes);\n      });\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: NgModuleFactoryLoader, },\n{type: Compiler, },\n{type: Injector, },\n{type: PreloadingStrategy, },\n];\n}\n\nfunction RouterPreloader_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterPreloader.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterPreloader.ctorParameters;\n/** @type {?} */\nRouterPreloader.prototype.loader;\n/** @type {?} */\nRouterPreloader.prototype.subscription;\n/** @type {?} */\nRouterPreloader.prototype.router;\n/** @type {?} */\nRouterPreloader.prototype.injector;\n/** @type {?} */\nRouterPreloader.prototype.preloadingStrategy;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Attribute, ChangeDetectorRef, ComponentFactoryResolver, ComponentRef, Directive, EventEmitter, Injector, OnDestroy, OnInit, Output, ViewContainerRef} from '@angular/core';\n\nimport {ChildrenOutletContexts} from '../router_outlet_context';\nimport {ActivatedRoute} from '../router_state';\nimport {PRIMARY_OUTLET} from '../shared';\n/**\n * \\@whatItDoes Acts as a placeholder that Ang<PERSON> dynamically fills based on the current router\n * state.\n * \n * \\@howToUse \n * \n * ```\n * <router-outlet></router-outlet>\n * <router-outlet name='left'></router-outlet>\n * <router-outlet name='right'></router-outlet>\n * ```\n * \n * A router outlet will emit an activate event any time a new component is being instantiated,\n * and a deactivate event when it is being destroyed.\n * \n * ```\n * <router-outlet\n *   (activate)='onActivate($event)'\n *   (deactivate)='onDeactivate($event)'></router-outlet>\n * ```\n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterOutlet implements OnDestroy, OnInit {\nprivate activated: ComponentRef<any>|null = null;\nprivate _activatedRoute: ActivatedRoute|null = null;\nprivate name: string;\n\n   activateEvents = new EventEmitter<any>();\n   deactivateEvents = new EventEmitter<any>();\n/**\n * @param {?} parentContexts\n * @param {?} location\n * @param {?} resolver\n * @param {?} name\n * @param {?} changeDetector\n */\nconstructor(\nprivate parentContexts: ChildrenOutletContexts,\nprivate location: ViewContainerRef,\nprivate resolver: ComponentFactoryResolver,  name: string,\nprivate changeDetector: ChangeDetectorRef) {\n    this.name = name || PRIMARY_OUTLET;\n    parentContexts.onChildOutletCreated(this.name, this);\n  }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.parentContexts.onChildOutletDestroyed(this.name); }\n/**\n * @return {?}\n */\nngOnInit(): void {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const /** @type {?} */ context = this.parentContexts.getContext(this.name);\n      if (context && context.route) {\n        if (context.attachRef) {\n          // `attachRef` is populated when there is an existing component to mount\n          this.attach(context.attachRef, context.route);\n        } else {\n          // otherwise the component defined in the configuration is created\n          this.activateWith(context.route, context.resolver || null);\n        }\n      }\n    }\n  }\n/**\n * @deprecated since v4 *\n * @return {?}\n */\nget locationInjector(): Injector { return this.location.injector; }\n/**\n * @deprecated since v4 *\n * @return {?}\n */\nget locationFactoryResolver(): ComponentFactoryResolver { return this.resolver; }\n/**\n * @return {?}\n */\nget isActivated(): boolean { return !!this.activated; }\n/**\n * @return {?}\n */\nget component(): Object {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    return this.activated.instance;\n  }\n/**\n * @return {?}\n */\nget activatedRoute(): ActivatedRoute {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    return /** @type {?} */(( this._activatedRoute as ActivatedRoute));\n  }\n/**\n * @return {?}\n */\nget activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n/**\n * Called when the `RouteReuseStrategy` instructs to detach the subtree\n * @return {?}\n */\ndetach(): ComponentRef<any> {\n    if (!this.activated) throw new Error('Outlet is not activated');\n    this.location.detach();\n    const /** @type {?} */ cmp = this.activated;\n    this.activated = null;\n    this._activatedRoute = null;\n    return cmp;\n  }\n/**\n * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n * @param {?} ref\n * @param {?} activatedRoute\n * @return {?}\n */\nattach(ref: ComponentRef<any>, activatedRoute: ActivatedRoute) {\n    this.activated = ref;\n    this._activatedRoute = activatedRoute;\n    this.location.insert(ref.hostView);\n  }\n/**\n * @return {?}\n */\ndeactivate(): void {\n    if (this.activated) {\n      const /** @type {?} */ c = this.component;\n      this.activated.destroy();\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n/**\n * @param {?} activatedRoute\n * @param {?} resolver\n * @return {?}\n */\nactivateWith(activatedRoute: ActivatedRoute, resolver: ComponentFactoryResolver|null) {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    const /** @type {?} */ snapshot = activatedRoute._futureSnapshot;\n    const /** @type {?} */ component = /** @type {?} */(( <any> /** @type {?} */((snapshot._routeConfig)).component));\n    resolver = resolver || this.resolver;\n    const /** @type {?} */ factory = resolver.resolveComponentFactory(component);\n    const /** @type {?} */ childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n    const /** @type {?} */ injector = new OutletInjector(activatedRoute, childContexts, this.location.injector);\n    this.activated = this.location.createComponent(factory, this.location.length, injector);\n    // Calling `markForCheck` to make sure we will run the change detection when the\n    // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n    this.changeDetector.markForCheck();\n    this.activateEvents.emit(this.activated.instance);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'router-outlet', exportAs: 'outlet'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ChildrenOutletContexts, },\n{type: ViewContainerRef, },\n{type: ComponentFactoryResolver, },\n{type: undefined, decorators: [{ type: Attribute, args: ['name', ] }, ]},\n{type: ChangeDetectorRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'activateEvents': [{ type: Output, args: ['activate', ] },],\n'deactivateEvents': [{ type: Output, args: ['deactivate', ] },],\n};\n}\n\nfunction RouterOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterOutlet.ctorParameters;\n/** @type {?} */\nRouterOutlet.propDecorators;\n/** @type {?} */\nRouterOutlet.prototype.activated;\n/** @type {?} */\nRouterOutlet.prototype._activatedRoute;\n/** @type {?} */\nRouterOutlet.prototype.name;\n/** @type {?} */\nRouterOutlet.prototype.activateEvents;\n/** @type {?} */\nRouterOutlet.prototype.deactivateEvents;\n/** @type {?} */\nRouterOutlet.prototype.parentContexts;\n/** @type {?} */\nRouterOutlet.prototype.location;\n/** @type {?} */\nRouterOutlet.prototype.resolver;\n/** @type {?} */\nRouterOutlet.prototype.changeDetector;\n}\n\nclass OutletInjector implements Injector {\n/**\n * @param {?} route\n * @param {?} childContexts\n * @param {?} parent\n */\nconstructor(\nprivate route: ActivatedRoute,\nprivate childContexts: ChildrenOutletContexts,\nprivate parent: Injector) {}\n/**\n * @param {?} token\n * @param {?=} notFoundValue\n * @return {?}\n */\nget(token: any, notFoundValue?: any): any {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n\n    return this.parent.get(token, notFoundValue);\n  }\n}\n\nfunction OutletInjector_tsickle_Closure_declarations() {\n/** @type {?} */\nOutletInjector.prototype.route;\n/** @type {?} */\nOutletInjector.prototype.childContexts;\n/** @type {?} */\nOutletInjector.prototype.parent;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentFactoryResolver, ComponentRef} from '@angular/core';\n\nimport {RouterOutlet} from './directives/router_outlet';\nimport {ActivatedRoute} from './router_state';\n/**\n * Store contextual information about a {\\@link RouterOutlet}\n * \n * \\@stable\n */\nexport class OutletContext {\n  outlet: RouterOutlet|null = null;\n  route: ActivatedRoute|null = null;\n  resolver: ComponentFactoryResolver|null = null;\n  children = new ChildrenOutletContexts();\n  attachRef: ComponentRef<any>|null = null;\n}\n\nfunction OutletContext_tsickle_Closure_declarations() {\n/** @type {?} */\nOutletContext.prototype.outlet;\n/** @type {?} */\nOutletContext.prototype.route;\n/** @type {?} */\nOutletContext.prototype.resolver;\n/** @type {?} */\nOutletContext.prototype.children;\n/** @type {?} */\nOutletContext.prototype.attachRef;\n}\n\n/**\n * Store contextual information about the children (= nested) {\\@link RouterOutlet}\n * \n * \\@stable\n */\nexport class ChildrenOutletContexts {\nprivate contexts = new Map<string, OutletContext>();\n/**\n * Called when a `RouterOutlet` directive is instantiated\n * @param {?} childName\n * @param {?} outlet\n * @return {?}\n */\nonChildOutletCreated(childName: string, outlet: RouterOutlet): void {\n    const /** @type {?} */ context = this.getOrCreateContext(childName);\n    context.outlet = outlet;\n    this.contexts.set(childName, context);\n  }\n/**\n * Called when a `RouterOutlet` directive is destroyed.\n * We need to keep the context as the outlet could be destroyed inside a NgIf and might be\n * re-created later.\n * @param {?} childName\n * @return {?}\n */\nonChildOutletDestroyed(childName: string): void {\n    const /** @type {?} */ context = this.getContext(childName);\n    if (context) {\n      context.outlet = null;\n    }\n  }\n/**\n * Called when the corresponding route is deactivated during navigation.\n * Because the component get destroyed, all children outlet are destroyed.\n * @return {?}\n */\nonOutletDeactivated(): Map<string, OutletContext> {\n    const /** @type {?} */ contexts = this.contexts;\n    this.contexts = new Map();\n    return contexts;\n  }\n/**\n * @param {?} contexts\n * @return {?}\n */\nonOutletReAttached(contexts: Map<string, OutletContext>) { this.contexts = contexts; }\n/**\n * @param {?} childName\n * @return {?}\n */\ngetOrCreateContext(childName: string): OutletContext {\n    let /** @type {?} */ context = this.getContext(childName);\n\n    if (!context) {\n      context = new OutletContext();\n      this.contexts.set(childName, context);\n    }\n\n    return context;\n  }\n/**\n * @param {?} childName\n * @return {?}\n */\ngetContext(childName: string): OutletContext|null { return this.contexts.get(childName) || null; }\n}\n\nfunction ChildrenOutletContexts_tsickle_Closure_declarations() {\n/** @type {?} */\nChildrenOutletContexts.prototype.contexts;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AfterContentInit, ChangeDetectorRef, ContentChildren, Directive, ElementRef, Input, OnChanges, OnDestroy, QueryList, Renderer, SimpleChanges} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {NavigationEnd} from '../events';\nimport {Router} from '../router';\nimport {RouterLink, RouterLinkWithHref} from './router_link';\n/**\n * \\@whatItDoes Lets you add a CSS class to an element when the link's route becomes active.\n * \n * \\@howToUse \n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n * \n * \\@description \n * \n * The RouterLinkActive directive lets you add a CSS class to an element when the link's route\n * becomes active.\n * \n * Consider the following example:\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n * \n * When the url is either '/user' or '/user/bob', the active-link class will\n * be added to the `a` tag. If the url changes, the class will be removed.\n * \n * You can set more than one class, as follows:\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n * \n * You can configure RouterLinkActive by passing `exact: true`. This will add the classes\n * only when the url matches the link exactly.\n * \n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n * \n * You can assign the RouterLinkActive instance to a template variable and directly check\n * the `isActive` status.\n * ```\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n * \n * Finally, you can apply the RouterLinkActive directive to an ancestor of a RouterLink.\n * \n * ```\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n * \n * This will set the active-link class on the div tag if the url is either '/user/jim' or\n * '/user/bob'.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterLinkActive implements OnChanges,\n    OnDestroy, AfterContentInit {\n   links: QueryList<RouterLink>;\n  \n  linksWithHrefs: QueryList<RouterLinkWithHref>;\nprivate classes: string[] = [];\nprivate subscription: Subscription;\nprivate active: boolean = false;\n\n   routerLinkActiveOptions: {exact: boolean} = {exact: false};\n/**\n * @param {?} router\n * @param {?} element\n * @param {?} renderer\n * @param {?} cdr\n */\nconstructor(\nprivate router: Router,\nprivate element: ElementRef,\nprivate renderer: Renderer,\nprivate cdr: ChangeDetectorRef) {\n    this.subscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n/**\n * @return {?}\n */\nget isActive(): boolean { return this.active; }\n/**\n * @return {?}\n */\nngAfterContentInit(): void {\n    this.links.changes.subscribe(_ => this.update());\n    this.linksWithHrefs.changes.subscribe(_ => this.update());\n    this.update();\n  }\n/**\n * @param {?} data\n * @return {?}\n */\nset routerLinkActive(data: string[]|string) {\n    const /** @type {?} */ classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter(c => !!c);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void { this.update(); }\n/**\n * @return {?}\n */\nngOnDestroy(): void { this.subscription.unsubscribe(); }\n/**\n * @return {?}\n */\nprivate update(): void {\n    if (!this.links || !this.linksWithHrefs || !this.router.navigated) return;\n    const /** @type {?} */ hasActiveLinks = this.hasActiveLinks();\n\n    // react only when status has changed to prevent unnecessary dom updates\n    if (this.active !== hasActiveLinks) {\n      this.classes.forEach(\n          c => this.renderer.setElementClass(this.element.nativeElement, c, hasActiveLinks));\n      Promise.resolve(hasActiveLinks).then(active => this.active = active);\n    }\n  }\n/**\n * @param {?} router\n * @return {?}\n */\nprivate isLinkActive(router: Router): (link: (RouterLink|RouterLinkWithHref)) => boolean {\n    return (link: RouterLink | RouterLinkWithHref) =>\n               router.isActive(link.urlTree, this.routerLinkActiveOptions.exact);\n  }\n/**\n * @return {?}\n */\nprivate hasActiveLinks(): boolean {\n    return this.links.some(this.isLinkActive(this.router)) ||\n        this.linksWithHrefs.some(this.isLinkActive(this.router));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{\n  selector: '[routerLinkActive]',\n  exportAs: 'routerLinkActive',\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ElementRef, },\n{type: Renderer, },\n{type: ChangeDetectorRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'links': [{ type: ContentChildren, args: [RouterLink, {descendants: true}, ] },],\n'linksWithHrefs': [{ type: ContentChildren, args: [RouterLinkWithHref, {descendants: true}, ] },],\n'routerLinkActiveOptions': [{ type: Input },],\n'routerLinkActive': [{ type: Input },],\n};\n}\n\nfunction RouterLinkActive_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLinkActive.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLinkActive.ctorParameters;\n/** @type {?} */\nRouterLinkActive.propDecorators;\n/** @type {?} */\nRouterLinkActive.prototype.links;\n/** @type {?} */\nRouterLinkActive.prototype.linksWithHrefs;\n/** @type {?} */\nRouterLinkActive.prototype.classes;\n/** @type {?} */\nRouterLinkActive.prototype.subscription;\n/** @type {?} */\nRouterLinkActive.prototype.active;\n/** @type {?} */\nRouterLinkActive.prototype.routerLinkActiveOptions;\n/** @type {?} */\nRouterLinkActive.prototype.router;\n/** @type {?} */\nRouterLinkActive.prototype.element;\n/** @type {?} */\nRouterLinkActive.prototype.renderer;\n/** @type {?} */\nRouterLinkActive.prototype.cdr;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {LocationStrategy} from '@angular/common';\nimport {Attribute, Directive, ElementRef, HostBinding, HostListener, Input, OnChanges, OnDestroy, Renderer, isDevMode} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\n\nimport {QueryParamsHandling} from '../config';\nimport {NavigationEnd} from '../events';\nimport {Router} from '../router';\nimport {ActivatedRoute} from '../router_state';\nimport {UrlTree} from '../url_tree';\n/**\n * \\@whatItDoes Lets you link to specific parts of your app.\n * \n * \\@howToUse \n * \n * Consider the following route configuration:\n * `[{ path: 'user/:name', component: UserCmp }]`\n * \n * When linking to this `user/:name` route, you can write:\n * `<a routerLink='/user/bob'>link to user component</a>`\n * \n * \\@description \n * \n * The RouterLink directives let you link to specific parts of your app.\n * \n * When the link is static, you can use the directive as follows:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n * \n * If you use dynamic values to generate the link, you can pass an array of path\n * segments, followed by the params for each segment.\n * \n * For instance `['/team', teamId, 'user', userName, {details: true}]`\n * means that we want to generate a link to `/team/11/user/bob;details=true`.\n * \n * Multiple static segments can be merged into one\n * (e.g., `['/team/11/user', userName, {details: true}]`).\n * \n * The first segment name can be prepended with `/`, `./`, or `../`:\n * * If the first segment begins with `/`, the router will look up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router will\n *   instead look in the children of the current activated route.\n * * And if the first segment begins with `../`, the router will go up one level.\n * \n * You can set query params and fragment as follows:\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * RouterLink will use these to generate this link: `/user/bob#education?debug=true`.\n * \n * (Deprecated in v4.0.0 use `queryParamsHandling` instead) You can also tell the\n * directive to preserve the current query params and fragment:\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" preserveQueryParams preserveFragment>\n *   link to user component\n * </a>\n * ```\n * \n * You can tell the directive to how to handle queryParams, available options are:\n *  - 'merge' merge the queryParams into the current queryParams\n *  - 'preserve' prserve the current queryParams\n *  - default / '' use the queryParams only\n *  same options for {\\@link NavigationExtras#queryParamsHandling}\n * \n * ```\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n * \n * The router link directive always treats the provided input as a delta to the current url.\n * \n * For instance, if the current url is `/user/(box//aux:team)`.\n * \n * Then the following link `<a [routerLink]=\"['/user/jim']\">Jim</a>` will generate the link\n * `/user/(jim//aux:team)`.\n * \n * \\@ngModule RouterModule\n * \n * See {\\@link Router#createUrlTree} for more information.\n * \n * \\@stable\n */\nexport class RouterLink {\n   queryParams: {[k: string]: any};\n   fragment: string;\n   queryParamsHandling: QueryParamsHandling;\n   preserveFragment: boolean;\n   skipLocationChange: boolean;\n   replaceUrl: boolean;\nprivate commands: any[] = [];\nprivate preserve: boolean;\n/**\n * @param {?} router\n * @param {?} route\n * @param {?} tabIndex\n * @param {?} renderer\n * @param {?} el\n */\nconstructor(\nprivate router: Router,\nprivate route: ActivatedRoute,\n       tabIndex: string, renderer: Renderer, el: ElementRef) {\n    if (tabIndex == null) {\n      renderer.setElementAttribute(el.nativeElement, 'tabindex', '0');\n    }\n  }\n/**\n * @param {?} commands\n * @return {?}\n */\nset routerLink(commands: any[]|string) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n/**\n * @deprecated 4.0.0 use `queryParamsHandling` instead.\n * @param {?} value\n * @return {?}\n */\nset preserveQueryParams(value: boolean) {\n    if (isDevMode() && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated!, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n/**\n * @return {?}\n */\nonClick(): boolean {\n    const /** @type {?} */ extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl),\n    };\n    this.router.navigateByUrl(this.urlTree, extras);\n    return true;\n  }\n/**\n * @return {?}\n */\nget urlTree(): UrlTree {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      preserveQueryParams: attrBoolValue(this.preserve),\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment),\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: ':not(a)[routerLink]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ActivatedRoute, },\n{type: undefined, decorators: [{ type: Attribute, args: ['tabindex', ] }, ]},\n{type: Renderer, },\n{type: ElementRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'queryParams': [{ type: Input },],\n'fragment': [{ type: Input },],\n'queryParamsHandling': [{ type: Input },],\n'preserveFragment': [{ type: Input },],\n'skipLocationChange': [{ type: Input },],\n'replaceUrl': [{ type: Input },],\n'routerLink': [{ type: Input },],\n'preserveQueryParams': [{ type: Input },],\n'onClick': [{ type: HostListener, args: ['click', ] },],\n};\n}\n\nfunction RouterLink_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLink.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLink.ctorParameters;\n/** @type {?} */\nRouterLink.propDecorators;\n/** @type {?} */\nRouterLink.prototype.queryParams;\n/** @type {?} */\nRouterLink.prototype.fragment;\n/** @type {?} */\nRouterLink.prototype.queryParamsHandling;\n/** @type {?} */\nRouterLink.prototype.preserveFragment;\n/** @type {?} */\nRouterLink.prototype.skipLocationChange;\n/** @type {?} */\nRouterLink.prototype.replaceUrl;\n/** @type {?} */\nRouterLink.prototype.commands;\n/** @type {?} */\nRouterLink.prototype.preserve;\n/** @type {?} */\nRouterLink.prototype.router;\n/** @type {?} */\nRouterLink.prototype.route;\n}\n\n/**\n * \\@whatItDoes Lets you link to specific parts of your app.\n * \n * See {\\@link RouterLink} for more information.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class RouterLinkWithHref implements OnChanges, OnDestroy {\n    target: string;\n   queryParams: {[k: string]: any};\n   fragment: string;\n   queryParamsHandling: QueryParamsHandling;\n   preserveFragment: boolean;\n   skipLocationChange: boolean;\n   replaceUrl: boolean;\nprivate commands: any[] = [];\nprivate subscription: Subscription;\nprivate preserve: boolean;\n\n  // the url displayed on the anchor element.\n   href: string;\n/**\n * @param {?} router\n * @param {?} route\n * @param {?} locationStrategy\n */\nconstructor(\nprivate router: Router,\nprivate route: ActivatedRoute,\nprivate locationStrategy: LocationStrategy) {\n    this.subscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.updateTargetUrlAndHref();\n      }\n    });\n  }\n/**\n * @param {?} commands\n * @return {?}\n */\nset routerLink(commands: any[]|string) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n/**\n * @param {?} value\n * @return {?}\n */\nset preserveQueryParams(value: boolean) {\n    if (isDevMode() && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: {}): any { this.updateTargetUrlAndHref(); }\n/**\n * @return {?}\n */\nngOnDestroy(): any { this.subscription.unsubscribe(); }\n/**\n * @param {?} button\n * @param {?} ctrlKey\n * @param {?} metaKey\n * @param {?} shiftKey\n * @return {?}\n */\nonClick(button: number, ctrlKey: boolean, metaKey: boolean, shiftKey: boolean): boolean {\n    if (button !== 0 || ctrlKey || metaKey || shiftKey) {\n      return true;\n    }\n\n    if (typeof this.target === 'string' && this.target != '_self') {\n      return true;\n    }\n\n    const /** @type {?} */ extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl),\n    };\n    this.router.navigateByUrl(this.urlTree, extras);\n    return false;\n  }\n/**\n * @return {?}\n */\nprivate updateTargetUrlAndHref(): void {\n    this.href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree));\n  }\n/**\n * @return {?}\n */\nget urlTree(): UrlTree {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      preserveQueryParams: attrBoolValue(this.preserve),\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment),\n    });\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: 'a[routerLink]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Router, },\n{type: ActivatedRoute, },\n{type: LocationStrategy, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'target': [{ type: HostBinding, args: ['attr.target', ] },{ type: Input },],\n'queryParams': [{ type: Input },],\n'fragment': [{ type: Input },],\n'queryParamsHandling': [{ type: Input },],\n'preserveFragment': [{ type: Input },],\n'skipLocationChange': [{ type: Input },],\n'replaceUrl': [{ type: Input },],\n'href': [{ type: HostBinding },],\n'routerLink': [{ type: Input },],\n'preserveQueryParams': [{ type: Input },],\n'onClick': [{ type: HostListener, args: ['click', ['$event.button', '$event.ctrlKey', '$event.metaKey', '$event.shiftKey'], ] },],\n};\n}\n\nfunction RouterLinkWithHref_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterLinkWithHref.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nRouterLinkWithHref.ctorParameters;\n/** @type {?} */\nRouterLinkWithHref.propDecorators;\n/** @type {?} */\nRouterLinkWithHref.prototype.target;\n/** @type {?} */\nRouterLinkWithHref.prototype.queryParams;\n/** @type {?} */\nRouterLinkWithHref.prototype.fragment;\n/** @type {?} */\nRouterLinkWithHref.prototype.queryParamsHandling;\n/** @type {?} */\nRouterLinkWithHref.prototype.preserveFragment;\n/** @type {?} */\nRouterLinkWithHref.prototype.skipLocationChange;\n/** @type {?} */\nRouterLinkWithHref.prototype.replaceUrl;\n/** @type {?} */\nRouterLinkWithHref.prototype.commands;\n/** @type {?} */\nRouterLinkWithHref.prototype.subscription;\n/** @type {?} */\nRouterLinkWithHref.prototype.preserve;\n/** @type {?} */\nRouterLinkWithHref.prototype.href;\n/** @type {?} */\nRouterLinkWithHref.prototype.router;\n/** @type {?} */\nRouterLinkWithHref.prototype.route;\n/** @type {?} */\nRouterLinkWithHref.prototype.locationStrategy;\n}\n\n/**\n * @param {?} s\n * @return {?}\n */\nfunction attrBoolValue(s: any): boolean {\n  return s === '' || !!s;\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Location} from '@angular/common';\nimport {Compiler, Injector, NgModuleFactoryLoader, NgModuleRef, Type, isDevMode} from '@angular/core';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {concatMap} from 'rxjs/operator/concatMap';\nimport {every} from 'rxjs/operator/every';\nimport {first} from 'rxjs/operator/first';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {reduce} from 'rxjs/operator/reduce';\n\nimport {applyRedirects} from './apply_redirects';\nimport {LoadedRouterConfig, QueryParamsHandling, ResolveData, Route, Routes, RunGuardsAndResolvers, validateConfig} from './config';\nimport {createRouterState} from './create_router_state';\nimport {createUrlTree} from './create_url_tree';\nimport {Event, NavigationCancel, NavigationEnd, NavigationError, NavigationStart, RouteConfigLoadEnd, RouteConfigLoadStart, RoutesRecognized} from './events';\nimport {recognize} from './recognize';\nimport {DefaultRouteReuseStrategy, DetachedRouteHandleInternal, RouteReuseStrategy} from './route_reuse_strategy';\nimport {RouterConfigLoader} from './router_config_loader';\nimport {ChildrenOutletContexts, OutletContext} from './router_outlet_context';\nimport {ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot, advanceActivatedRoute, createEmptyState, equalParamsAndUrlSegments, inheritedParamsDataResolve} from './router_state';\nimport {Params, isNavigationCancelingError} from './shared';\nimport {DefaultUrlHandlingStrategy, UrlHandlingStrategy} from './url_handling_strategy';\nimport {UrlSerializer, UrlTree, containsTree, createEmptyUrlTree} from './url_tree';\nimport {andObservables, forEach, shallowEqual, waitForMap, wrapIntoObservable} from './utils/collection';\nimport {TreeNode} from './utils/tree';\n\ndeclare let Zone: any;\n\n/**\n * @whatItDoes Represents the extra options used during navigation.\n *\n * @stable\n */\nexport interface NavigationExtras {\n  /**\n  * Enables relative navigation from the current ActivatedRoute.\n  *\n  * Configuration:\n  *\n  * ```\n  * [{\n  *   path: 'parent',\n  *   component: ParentComponent,\n  *   children: [{\n  *     path: 'list',\n  *     component: ListComponent\n  *   },{\n  *     path: 'child',\n  *     component: ChildComponent\n  *   }]\n  * }]\n  * ```\n  *\n  * Navigate to list route from child route:\n  *\n  * ```\n  *  @Component({...})\n  *  class ChildComponent {\n  *    constructor(private router: Router, private route: ActivatedRoute) {}\n  *\n  *    go() {\n  *      this.router.navigate(['../list'], { relativeTo: this.route });\n  *    }\n  *  }\n  * ```\n  */\n  relativeTo?: ActivatedRoute|null;\n\n  /**\n  * Sets query parameters to the URL.\n  *\n  * ```\n  * // Navigate to /results?page=1\n  * this.router.navigate(['/results'], { queryParams: { page: 1 } });\n  * ```\n  */\n  queryParams?: Params|null;\n\n  /**\n  * Sets the hash fragment for the URL.\n  *\n  * ```\n  * // Navigate to /results#top\n  * this.router.navigate(['/results'], { fragment: 'top' });\n  * ```\n  */\n  fragment?: string;\n\n  /**\n  * Preserves the query parameters for the next navigation.\n  *\n  * deprecated, use `queryParamsHandling` instead\n  *\n  * ```\n  * // Preserve query params from /results?page=1 to /view?page=1\n  * this.router.navigate(['/view'], { preserveQueryParams: true });\n  * ```\n  *\n  * @deprecated since v4\n  */\n  preserveQueryParams?: boolean;\n\n  /**\n  *  config strategy to handle the query parameters for the next navigation.\n  *\n  * ```\n  * // from /results?page=1 to /view?page=1&page=2\n  * this.router.navigate(['/view'], { queryParams: { page: 2 },  queryParamsHandling: \"merge\" });\n  * ```\n  */\n  queryParamsHandling?: QueryParamsHandling|null;\n  /**\n  * Preserves the fragment for the next navigation\n  *\n  * ```\n  * // Preserve fragment from /results#top to /view#top\n  * this.router.navigate(['/view'], { preserveFragment: true });\n  * ```\n  */\n  preserveFragment?: boolean;\n  /**\n  * Navigates without pushing a new state into history.\n  *\n  * ```\n  * // Navigate silently to /view\n  * this.router.navigate(['/view'], { skipLocationChange: true });\n  * ```\n  */\n  skipLocationChange?: boolean;\n  /**\n  * Navigates while replacing the current state in history.\n  *\n  * ```\n  * // Navigate to /view\n  * this.router.navigate(['/view'], { replaceUrl: true });\n  * ```\n  */\n  replaceUrl?: boolean;\n}\n\n/**\n * @whatItDoes Error handler that is invoked when a navigation errors.\n *\n * @description\n * If the handler returns a value, the navigation promise will be resolved with this value.\n * If the handler throws an exception, the navigation promise will be rejected with\n * the exception.\n *\n * @stable\n */\nexport type ErrorHandler = (error: any) => any;\n/**\n * @param {?} error\n * @return {?}\n */\nfunction defaultErrorHandler(error: any): any {\n  throw error;\n}\n\ntype NavigationSource = 'imperative' | 'popstate' | 'hashchange';\n\ntype NavigationParams = {\n  id: number,\n  rawUrl: UrlTree,\n  extras: NavigationExtras,\n  resolve: any,\n  reject: any,\n  promise: Promise<boolean>,\n  source: NavigationSource,\n};\n\n/**\n * @internal\n */\nexport type RouterHook = (snapshot: RouterStateSnapshot) => Observable<void>;\n/**\n * \\@internal\n * @param {?} snapshot\n * @return {?}\n */\nfunction defaultRouterHook(snapshot: RouterStateSnapshot): Observable<void> {\n  return /** @type {?} */(( of (null) as any));\n}\n/**\n * \\@whatItDoes Provides the navigation and url manipulation capabilities.\n * \n * See {\\@link Routes} for more details and examples.\n * \n * \\@ngModule RouterModule\n * \n * \\@stable\n */\nexport class Router {\nprivate currentUrlTree: UrlTree;\nprivate rawUrlTree: UrlTree;\nprivate navigations = new BehaviorSubject<NavigationParams>( /** @type {?} */((null)));\nprivate routerEvents = new Subject<Event>();\nprivate currentRouterState: RouterState;\nprivate locationSubscription: Subscription;\nprivate navigationId: number = 0;\nprivate configLoader: RouterConfigLoader;\nprivate ngModule: NgModuleRef<any>;\n/**\n * Error handler that is invoked when a navigation errors.\n * \n * See {\\@link ErrorHandler} for more information.\n */\nerrorHandler: ErrorHandler = defaultErrorHandler;\n/**\n * Indicates if at least one navigation happened.\n */\nnavigated: boolean = false;\n/**\n * Used by RouterModule. This allows us to\n * pause the navigation either before preactivation or after it.\n * \\@internal\n */\nhooks: {beforePreactivation: RouterHook, afterPreactivation: RouterHook} = {\n    beforePreactivation: defaultRouterHook,\n    afterPreactivation: defaultRouterHook\n  };\n/**\n * Extracts and merges URLs. Used for AngularJS to Angular migrations.\n */\nurlHandlingStrategy: UrlHandlingStrategy = new DefaultUrlHandlingStrategy();\n\n  routeReuseStrategy: RouteReuseStrategy = new DefaultRouteReuseStrategy();\n/**\n * @param {?} rootComponentType\n * @param {?} urlSerializer\n * @param {?} rootContexts\n * @param {?} location\n * @param {?} injector\n * @param {?} loader\n * @param {?} compiler\n * @param {?} config\n */\nconstructor(\nprivate rootComponentType: Type<any>|null,\nprivate urlSerializer: UrlSerializer,\nprivate rootContexts: ChildrenOutletContexts,\nprivate location: Location, injector: Injector,\n      loader: NgModuleFactoryLoader, compiler: Compiler,\npublic config: Routes) {\n    const onLoadStart = (r: Route) => this.triggerEvent(new RouteConfigLoadStart(r));\n    const onLoadEnd = (r: Route) => this.triggerEvent(new RouteConfigLoadEnd(r));\n\n    this.ngModule = injector.get(NgModuleRef);\n\n    this.resetConfig(config);\n    this.currentUrlTree = createEmptyUrlTree();\n    this.rawUrlTree = this.currentUrlTree;\n\n    this.configLoader = new RouterConfigLoader(loader, compiler, onLoadStart, onLoadEnd);\n    this.currentRouterState = createEmptyState(this.currentUrlTree, this.rootComponentType);\n    this.processNavigations();\n  }\n/**\n * \\@internal \n * TODO: this should be removed once the constructor of the router made internal\n * @param {?} rootComponentType\n * @return {?}\n */\nresetRootComponentType(rootComponentType: Type<any>): void {\n    this.rootComponentType = rootComponentType;\n    // TODO: vsavkin router 4.0 should make the root component set to null\n    // this will simplify the lifecycle of the router.\n    this.currentRouterState.root.component = this.rootComponentType;\n  }\n/**\n * Sets up the location change listener and performs the initial navigation.\n * @return {?}\n */\ninitialNavigation(): void {\n    this.setUpLocationChangeListener();\n    if (this.navigationId === 0) {\n      this.navigateByUrl(this.location.path(true), {replaceUrl: true});\n    }\n  }\n/**\n * Sets up the location change listener.\n * @return {?}\n */\nsetUpLocationChangeListener(): void {\n    // Zone.current.wrap is needed because of the issue with RxJS scheduler,\n    // which does not work properly with zone.js in IE and Safari\n    if (!this.locationSubscription) {\n      this.locationSubscription = /** @type {?} */(( <any>this.location.subscribe(Zone.current.wrap((change: any) => {\n        const /** @type {?} */ rawUrlTree = this.urlSerializer.parse(change['url']);\n        const /** @type {?} */ source: NavigationSource = change['type'] === 'popstate' ? 'popstate' : 'hashchange';\n        setTimeout(() => { this.scheduleNavigation(rawUrlTree, source, {replaceUrl: true}); }, 0);\n      }))));\n    }\n  }\n/**\n * The current route state\n * @return {?}\n */\nget routerState(): RouterState { return this.currentRouterState; }\n/**\n * The current url\n * @return {?}\n */\nget url(): string { return this.serializeUrl(this.currentUrlTree); }\n/**\n * An observable of router events\n * @return {?}\n */\nget events(): Observable<Event> { return this.routerEvents; }\n/**\n * \\@internal\n * @param {?} e\n * @return {?}\n */\ntriggerEvent(e: Event) { this.routerEvents.next(e); }\n/**\n * Resets the configuration used for navigation and generating links.\n * \n * ### Usage\n * \n * ```\n * router.resetConfig([\n *  { path: 'team/:id', component: TeamCmp, children: [\n *    { path: 'simple', component: SimpleCmp },\n *    { path: 'user/:name', component: UserCmp }\n *  ]}\n * ]);\n * ```\n * @param {?} config\n * @return {?}\n */\nresetConfig(config: Routes): void {\n    validateConfig(config);\n    this.config = config;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\nngOnDestroy() { this.dispose(); }\n/**\n * Disposes of the router\n * @return {?}\n */\ndispose(): void {\n    if (this.locationSubscription) {\n      this.locationSubscription.unsubscribe();\n      this.locationSubscription = /** @type {?} */(( null));\n    }\n  }\n/**\n * Applies an array of commands to the current url tree and creates a new url tree.\n * \n * When given an activate route, applies the given commands starting from the route.\n * When not given a route, applies the given command starting from the root.\n * \n * ### Usage\n * \n * ```\n * // create /team/33/user/11\n * router.createUrlTree(['/team', 33, 'user', 11]);\n * \n * // create /team/33;expand=true/user/11\n * router.createUrlTree(['/team', 33, {expand: true}, 'user', 11]);\n * \n * // you can collapse static segments like this (this works only with the first passed-in value):\n * router.createUrlTree(['/team/33/user', userId]);\n * \n * // If the first segment can contain slashes, and you do not want the router to split it, you\n * // can do the following:\n * \n * router.createUrlTree([{segmentPath: '/one/two'}]);\n * \n * // create /team/33/(user/11//right:chat)\n * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: 'chat'}}]);\n * \n * // remove the right secondary node\n * router.createUrlTree(['/team', 33, {outlets: {primary: 'user/11', right: null}}]);\n * \n * // assuming the current url is `/team/33/user/11` and the route points to `user/11`\n * \n * // navigate to /team/33/user/11/details\n * router.createUrlTree(['details'], {relativeTo: route});\n * \n * // navigate to /team/33/user/22\n * router.createUrlTree(['../22'], {relativeTo: route});\n * \n * // navigate to /team/44/user/22\n * router.createUrlTree(['../../team/44/user/22'], {relativeTo: route});\n * ```\n * @param {?} commands\n * @param {?=} navigationExtras\n * @return {?}\n */\ncreateUrlTree(commands: any[], navigationExtras: NavigationExtras = {}): UrlTree {\n    const {relativeTo,          queryParams,         fragment,\n           preserveQueryParams, queryParamsHandling, preserveFragment} = navigationExtras;\n    if (isDevMode() && preserveQueryParams && /** @type {?} */(( <any>console)) && /** @type {?} */(( <any>console.warn))) {\n      console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n    }\n    const /** @type {?} */ a = relativeTo || this.routerState.root;\n    const /** @type {?} */ f = preserveFragment ? this.currentUrlTree.fragment : fragment;\n    let /** @type {?} */ q: Params|null = null;\n    if (queryParamsHandling) {\n      switch (queryParamsHandling) {\n        case 'merge':\n          q = {...this.currentUrlTree.queryParams, ...queryParams};\n          break;\n        case 'preserve':\n          q = this.currentUrlTree.queryParams;\n          break;\n        default:\n          q = queryParams || null;\n      }\n    } else {\n      q = preserveQueryParams ? this.currentUrlTree.queryParams : queryParams || null;\n    }\n    return createUrlTree(a, this.currentUrlTree, commands, /** @type {?} */(( q)), /** @type {?} */(( f)));\n  }\n/**\n * Navigate based on the provided url. This navigation is always absolute.\n * \n * Returns a promise that:\n * - resolves to 'true' when navigation succeeds,\n * - resolves to 'false' when navigation fails,\n * - is rejected when an error happens.\n * \n * ### Usage\n * \n * ```\n * router.navigateByUrl(\"/team/33/user/11\");\n * \n * // Navigate without updating the URL\n * router.navigateByUrl(\"/team/33/user/11\", { skipLocationChange: true });\n * ```\n * \n * In opposite to `navigate`, `navigateByUrl` takes a whole URL\n * and does not apply any delta to the current one.\n * @param {?} url\n * @param {?=} extras\n * @return {?}\n */\nnavigateByUrl(url: string|UrlTree, extras: NavigationExtras = {skipLocationChange: false}):\n      Promise<boolean> {\n    const /** @type {?} */ urlTree = url instanceof UrlTree ? url : this.parseUrl(url);\n    const /** @type {?} */ mergedTree = this.urlHandlingStrategy.merge(urlTree, this.rawUrlTree);\n\n    return this.scheduleNavigation(mergedTree, 'imperative', extras);\n  }\n/**\n * Navigate based on the provided array of commands and a starting point.\n * If no starting route is provided, the navigation is absolute.\n * \n * Returns a promise that:\n * - resolves to 'true' when navigation succeeds,\n * - resolves to 'false' when navigation fails,\n * - is rejected when an error happens.\n * \n * ### Usage\n * \n * ```\n * router.navigate(['team', 33, 'user', 11], {relativeTo: route});\n * \n * // Navigate without updating the URL\n * router.navigate(['team', 33, 'user', 11], {relativeTo: route, skipLocationChange: true});\n * ```\n * \n * In opposite to `navigateByUrl`, `navigate` always takes a delta that is applied to the current\n * URL.\n * @param {?} commands\n * @param {?=} extras\n * @return {?}\n */\nnavigate(commands: any[], extras: NavigationExtras = {skipLocationChange: false}):\n      Promise<boolean> {\n    validateCommands(commands);\n    if (typeof extras.queryParams === 'object' && extras.queryParams !== null) {\n      extras.queryParams = this.removeEmptyProps(extras.queryParams);\n    }\n    return this.navigateByUrl(this.createUrlTree(commands, extras), extras);\n  }\n/**\n * Serializes a {\\@link UrlTree} into a string\n * @param {?} url\n * @return {?}\n */\nserializeUrl(url: UrlTree): string { return this.urlSerializer.serialize(url); }\n/**\n * Parses a string into a {\\@link UrlTree}\n * @param {?} url\n * @return {?}\n */\nparseUrl(url: string): UrlTree { return this.urlSerializer.parse(url); }\n/**\n * Returns whether the url is activated\n * @param {?} url\n * @param {?} exact\n * @return {?}\n */\nisActive(url: string|UrlTree, exact: boolean): boolean {\n    if (url instanceof UrlTree) {\n      return containsTree(this.currentUrlTree, url, exact);\n    }\n\n    const /** @type {?} */ urlTree = this.urlSerializer.parse(url);\n    return containsTree(this.currentUrlTree, urlTree, exact);\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate removeEmptyProps(params: Params): Params {\n    return Object.keys(params).reduce((result: Params, key: string) => {\n      const /** @type {?} */ value: any = params[key];\n      if (value !== null && value !== undefined) {\n        result[key] = value;\n      }\n      return result;\n    }, {});\n  }\n/**\n * @return {?}\n */\nprivate processNavigations(): void {\n    concatMap\n        .call(\n            this.navigations,\n            (nav: NavigationParams) => {\n              if (nav) {\n                this.executeScheduledNavigation(nav);\n                // a failed navigation should not stop the router from processing\n                // further navigations => the catch\n                return nav.promise.catch(() => {});\n              } else {\n                return /** @type {?} */(( <any>of (null)));\n              }\n            })\n        .subscribe(() => {});\n  }\n/**\n * @param {?} rawUrl\n * @param {?} source\n * @param {?} extras\n * @return {?}\n */\nprivate scheduleNavigation(rawUrl: UrlTree, source: NavigationSource, extras: NavigationExtras):\n      Promise<boolean> {\n    const /** @type {?} */ lastNavigation = this.navigations.value;\n\n    // If the user triggers a navigation imperatively (e.g., by using navigateByUrl),\n    // and that navigation results in 'replaceState' that leads to the same URL,\n    // we should skip those.\n    if (lastNavigation && source !== 'imperative' && lastNavigation.source === 'imperative' &&\n        lastNavigation.rawUrl.toString() === rawUrl.toString()) {\n      return Promise.resolve(true);  // return value is not used\n    }\n\n    // Because of a bug in IE and Edge, the location class fires two events (popstate and\n    // hashchange) every single time. The second one should be ignored. Otherwise, the URL will\n    // flicker.\n    if (lastNavigation && source == 'hashchange' && lastNavigation.source === 'popstate' &&\n        lastNavigation.rawUrl.toString() === rawUrl.toString()) {\n      return Promise.resolve(true);  // return value is not used\n    }\n\n    let /** @type {?} */ resolve: any = null;\n    let /** @type {?} */ reject: any = null;\n\n    const /** @type {?} */ promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n\n    const /** @type {?} */ id = ++this.navigationId;\n    this.navigations.next({id, source, rawUrl, extras, resolve, reject, promise});\n\n    // Make sure that the error is propagated even though `processNavigations` catch\n    // handler does not rethrow\n    return promise.catch((e: any) => Promise.reject(e));\n  }\n/**\n * @param {?} __0\n * @return {?}\n */\nprivate executeScheduledNavigation({id, rawUrl, extras, resolve, reject}: NavigationParams):\n      void {\n    const /** @type {?} */ url = this.urlHandlingStrategy.extract(rawUrl);\n    const /** @type {?} */ urlTransition = !this.navigated || url.toString() !== this.currentUrlTree.toString();\n\n    if (urlTransition && this.urlHandlingStrategy.shouldProcessUrl(rawUrl)) {\n      this.routerEvents.next(new NavigationStart(id, this.serializeUrl(url)));\n      Promise.resolve()\n          .then(\n              (_) => this.runNavigate(\n                  url, rawUrl, !!extras.skipLocationChange, !!extras.replaceUrl, id, null))\n          .then(resolve, reject);\n\n      // we cannot process the current URL, but we could process the previous one =>\n      // we need to do some cleanup\n    } else if (\n        urlTransition && this.rawUrlTree &&\n        this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree)) {\n      this.routerEvents.next(new NavigationStart(id, this.serializeUrl(url)));\n      Promise.resolve()\n          .then(\n              (_) => this.runNavigate(\n                  url, rawUrl, false, false, id,\n                  createEmptyState(url, this.rootComponentType).snapshot))\n          .then(resolve, reject);\n\n    } else {\n      this.rawUrlTree = rawUrl;\n      resolve(null);\n    }\n  }\n/**\n * @param {?} url\n * @param {?} rawUrl\n * @param {?} shouldPreventPushState\n * @param {?} shouldReplaceUrl\n * @param {?} id\n * @param {?} precreatedState\n * @return {?}\n */\nprivate runNavigate(\n      url: UrlTree, rawUrl: UrlTree, shouldPreventPushState: boolean, shouldReplaceUrl: boolean,\n      id: number, precreatedState: RouterStateSnapshot|null): Promise<boolean> {\n    if (id !== this.navigationId) {\n      this.location.go(this.urlSerializer.serialize(this.currentUrlTree));\n      this.routerEvents.next(new NavigationCancel(\n          id, this.serializeUrl(url),\n          `Navigation ID ${id} is not equal to the current navigation id ${this.navigationId}`));\n      return Promise.resolve(false);\n    }\n\n    return new Promise((resolvePromise, rejectPromise) => {\n      // create an observable of the url and route state snapshot\n      // this operation do not result in any side effects\n      let /** @type {?} */ urlAndSnapshot$: Observable<{appliedUrl: UrlTree, snapshot: RouterStateSnapshot}>;\n      if (!precreatedState) {\n        const /** @type {?} */ moduleInjector = this.ngModule.injector;\n        const /** @type {?} */ redirectsApplied$ =\n            applyRedirects(moduleInjector, this.configLoader, this.urlSerializer, url, this.config);\n\n        urlAndSnapshot$ = mergeMap.call(redirectsApplied$, (appliedUrl: UrlTree) => {\n          return map.call(\n              recognize(\n                  this.rootComponentType, this.config, appliedUrl, this.serializeUrl(appliedUrl)),\n              (snapshot: any) => {\n\n                this.routerEvents.next(new RoutesRecognized(\n                    id, this.serializeUrl(url), this.serializeUrl(appliedUrl), snapshot));\n\n                return {appliedUrl, snapshot};\n              });\n        });\n      } else {\n        urlAndSnapshot$ = of ({appliedUrl: url, snapshot: precreatedState});\n      }\n\n      const /** @type {?} */ beforePreactivationDone$ = mergeMap.call(\n          urlAndSnapshot$, (p: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            return map.call(this.hooks.beforePreactivation(p.snapshot), () => p);\n          });\n\n      // run preactivation: guards and data resolvers\n      let /** @type {?} */ preActivation: PreActivation;\n\n      const /** @type {?} */ preactivationTraverse$ = map.call(\n          beforePreactivationDone$,\n          ({appliedUrl, snapshot}: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            const /** @type {?} */ moduleInjector = this.ngModule.injector;\n            preActivation =\n                new PreActivation(snapshot, this.currentRouterState.snapshot, moduleInjector);\n            preActivation.traverse(this.rootContexts);\n            return {appliedUrl, snapshot};\n          });\n\n      const /** @type {?} */ preactivationCheckGuards$ = mergeMap.call(\n          preactivationTraverse$,\n          ({appliedUrl, snapshot}: {appliedUrl: string, snapshot: RouterStateSnapshot}) => {\n            if (this.navigationId !== id) return of (false);\n\n            return map.call(preActivation.checkGuards(), (shouldActivate: boolean) => {\n              return {appliedUrl: appliedUrl, snapshot: snapshot, shouldActivate: shouldActivate};\n            });\n          });\n\n      const /** @type {?} */ preactivationResolveData$ = mergeMap.call(preactivationCheckGuards$, (p: any) => {\n        if (this.navigationId !== id) return of (false);\n\n        if (p.shouldActivate) {\n          return map.call(preActivation.resolveData(), () => p);\n        } else {\n          return of (p);\n        }\n      });\n\n      const /** @type {?} */ preactivationDone$ = mergeMap.call(preactivationResolveData$, (p: any) => {\n        return map.call(this.hooks.afterPreactivation(p.snapshot), () => p);\n      });\n\n\n      // create router state\n      // this operation has side effects => route state is being affected\n      const /** @type {?} */ routerState$ =\n          map.call(preactivationDone$, ({appliedUrl, snapshot, shouldActivate}: any) => {\n            if (shouldActivate) {\n              const /** @type {?} */ state =\n                  createRouterState(this.routeReuseStrategy, snapshot, this.currentRouterState);\n              return {appliedUrl, state, shouldActivate};\n            } else {\n              return {appliedUrl, state: null, shouldActivate};\n            }\n          });\n\n\n      // applied the new router state\n      // this operation has side effects\n      let /** @type {?} */ navigationIsSuccessful: boolean;\n      const /** @type {?} */ storedState = this.currentRouterState;\n      const /** @type {?} */ storedUrl = this.currentUrlTree;\n\n      routerState$\n          .forEach(({appliedUrl, state, shouldActivate}: any) => {\n            if (!shouldActivate || id !== this.navigationId) {\n              navigationIsSuccessful = false;\n              return;\n            }\n\n            this.currentUrlTree = appliedUrl;\n            this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, rawUrl);\n\n            this.currentRouterState = state;\n\n            if (!shouldPreventPushState) {\n              const /** @type {?} */ path = this.urlSerializer.serialize(this.rawUrlTree);\n              if (this.location.isCurrentPathEqualTo(path) || shouldReplaceUrl) {\n                this.location.replaceState(path);\n              } else {\n                this.location.go(path);\n              }\n            }\n\n            new ActivateRoutes(this.routeReuseStrategy, state, storedState)\n                .activate(this.rootContexts);\n\n            navigationIsSuccessful = true;\n          })\n          .then(\n              () => {\n                if (navigationIsSuccessful) {\n                  this.navigated = true;\n                  this.routerEvents.next(new NavigationEnd(\n                      id, this.serializeUrl(url), this.serializeUrl(this.currentUrlTree)));\n                  resolvePromise(true);\n                } else {\n                  this.resetUrlToCurrentUrlTree();\n                  this.routerEvents.next(new NavigationCancel(id, this.serializeUrl(url), ''));\n                  resolvePromise(false);\n                }\n              },\n              (e: any) => {\n                if (isNavigationCancelingError(e)) {\n                  this.resetUrlToCurrentUrlTree();\n                  this.navigated = true;\n                  this.routerEvents.next(\n                      new NavigationCancel(id, this.serializeUrl(url), e.message));\n                  resolvePromise(false);\n                } else {\n                  this.routerEvents.next(new NavigationError(id, this.serializeUrl(url), e));\n                  try {\n                    resolvePromise(this.errorHandler(e));\n                  } catch ( /** @type {?} */ee) {\n                    rejectPromise(ee);\n                  }\n                }\n\n                this.currentRouterState = storedState;\n                this.currentUrlTree = storedUrl;\n                this.rawUrlTree = this.urlHandlingStrategy.merge(this.currentUrlTree, rawUrl);\n                this.location.replaceState(this.serializeUrl(this.rawUrlTree));\n              });\n    });\n  }\n/**\n * @return {?}\n */\nprivate resetUrlToCurrentUrlTree(): void {\n    const /** @type {?} */ path = this.urlSerializer.serialize(this.rawUrlTree);\n    this.location.replaceState(path);\n  }\n}\n\nfunction Router_tsickle_Closure_declarations() {\n/** @type {?} */\nRouter.prototype.currentUrlTree;\n/** @type {?} */\nRouter.prototype.rawUrlTree;\n/** @type {?} */\nRouter.prototype.navigations;\n/** @type {?} */\nRouter.prototype.routerEvents;\n/** @type {?} */\nRouter.prototype.currentRouterState;\n/** @type {?} */\nRouter.prototype.locationSubscription;\n/** @type {?} */\nRouter.prototype.navigationId;\n/** @type {?} */\nRouter.prototype.configLoader;\n/** @type {?} */\nRouter.prototype.ngModule;\n/**\n * Error handler that is invoked when a navigation errors.\n * \n * See {\\@link ErrorHandler} for more information.\n * @type {?}\n */\nRouter.prototype.errorHandler;\n/**\n * Indicates if at least one navigation happened.\n * @type {?}\n */\nRouter.prototype.navigated;\n/**\n * Used by RouterModule. This allows us to\n * pause the navigation either before preactivation or after it.\n * \\@internal\n * @type {?}\n */\nRouter.prototype.hooks;\n/**\n * Extracts and merges URLs. Used for AngularJS to Angular migrations.\n * @type {?}\n */\nRouter.prototype.urlHandlingStrategy;\n/** @type {?} */\nRouter.prototype.routeReuseStrategy;\n/** @type {?} */\nRouter.prototype.rootComponentType;\n/** @type {?} */\nRouter.prototype.urlSerializer;\n/** @type {?} */\nRouter.prototype.rootContexts;\n/** @type {?} */\nRouter.prototype.location;\n/** @type {?} */\nRouter.prototype.config;\n}\n\nclass CanActivate {\n/**\n * @param {?} path\n */\nconstructor(public path: ActivatedRouteSnapshot[]) {}\n/**\n * @return {?}\n */\nget route(): ActivatedRouteSnapshot { return this.path[this.path.length - 1]; }\n}\n\nfunction CanActivate_tsickle_Closure_declarations() {\n/** @type {?} */\nCanActivate.prototype.path;\n}\n\nclass CanDeactivate {\n/**\n * @param {?} component\n * @param {?} route\n */\nconstructor(public component: Object|null,\npublic route: ActivatedRouteSnapshot) {}\n}\n\nfunction CanDeactivate_tsickle_Closure_declarations() {\n/** @type {?} */\nCanDeactivate.prototype.component;\n/** @type {?} */\nCanDeactivate.prototype.route;\n}\n\nexport class PreActivation {\nprivate canActivateChecks: CanActivate[] = [];\nprivate canDeactivateChecks: CanDeactivate[] = [];\n/**\n * @param {?} future\n * @param {?} curr\n * @param {?} moduleInjector\n */\nconstructor(\nprivate future: RouterStateSnapshot,\nprivate curr: RouterStateSnapshot,\nprivate moduleInjector: Injector) {}\n/**\n * @param {?} parentContexts\n * @return {?}\n */\ntraverse(parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ futureRoot = this.future._root;\n    const /** @type {?} */ currRoot = this.curr ? this.curr._root : null;\n    this.traverseChildRoutes(futureRoot, currRoot, parentContexts, [futureRoot.value]);\n  }\n/**\n * @return {?}\n */\ncheckGuards(): Observable<boolean> {\n    if (this.canDeactivateChecks.length === 0 && this.canActivateChecks.length === 0) {\n      return of (true);\n    }\n    const /** @type {?} */ canDeactivate$ = this.runCanDeactivateChecks();\n    return mergeMap.call(\n        canDeactivate$,\n        (canDeactivate: boolean) => canDeactivate ? this.runCanActivateChecks() : of (false));\n  }\n/**\n * @return {?}\n */\nresolveData(): Observable<any> {\n    if (this.canActivateChecks.length === 0) return of (null);\n    const /** @type {?} */ checks$ = from(this.canActivateChecks);\n    const /** @type {?} */ runningChecks$ =\n        concatMap.call(checks$, (check: CanActivate) => this.runResolve(check.route));\n    return reduce.call(runningChecks$, (_: any, __: any) => _);\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @param {?} futurePath\n * @return {?}\n */\nprivate traverseChildRoutes(\n      futureNode: TreeNode<ActivatedRouteSnapshot>, currNode: TreeNode<ActivatedRouteSnapshot>|null,\n      contexts: ChildrenOutletContexts|null, futurePath: ActivatedRouteSnapshot[]): void {\n    const /** @type {?} */ prevChildren = nodeChildrenAsMap(currNode);\n\n    // Process the children of the future route\n    futureNode.children.forEach(c => {\n      this.traverseRoutes(c, prevChildren[c.value.outlet], contexts, futurePath.concat([c.value]));\n      delete prevChildren[c.value.outlet];\n    });\n\n    // Process any children left from the current route (not active for the future route)\n    forEach(\n        prevChildren, (v: TreeNode<ActivatedRouteSnapshot>, k: string) =>\n                          this.deactivateRouteAndItsChildren(v, /** @type {?} */(( contexts)).getContext(k)));\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContexts\n * @param {?} futurePath\n * @return {?}\n */\nprivate traverseRoutes(\n      futureNode: TreeNode<ActivatedRouteSnapshot>, currNode: TreeNode<ActivatedRouteSnapshot>,\n      parentContexts: ChildrenOutletContexts|null, futurePath: ActivatedRouteSnapshot[]): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n    const /** @type {?} */ context = parentContexts ? parentContexts.getContext(futureNode.value.outlet) : null;\n\n    // reusing the node\n    if (curr && future._routeConfig === curr._routeConfig) {\n      if (this.shouldRunGuardsAndResolvers(\n              curr, future, /** @type {?} */(( future._routeConfig)).runGuardsAndResolvers)) {\n        this.canActivateChecks.push(new CanActivate(futurePath));\n        const /** @type {?} */ outlet = /** @type {?} */(( /** @type {?} */(( context)).outlet));\n        this.canDeactivateChecks.push(new CanDeactivate(outlet.component, curr));\n      } else {\n        // we need to set the data\n        future.data = curr.data;\n        future._resolvedData = curr._resolvedData;\n      }\n\n      // If we have a component, we need to go through an outlet.\n      if (future.component) {\n        this.traverseChildRoutes(\n            futureNode, currNode, context ? context.children : null, futurePath);\n\n        // if we have a componentless route, we recurse but keep the same outlet map.\n      } else {\n        this.traverseChildRoutes(futureNode, currNode, parentContexts, futurePath);\n      }\n    } else {\n      if (curr) {\n        this.deactivateRouteAndItsChildren(currNode, context);\n      }\n\n      this.canActivateChecks.push(new CanActivate(futurePath));\n      // If we have a component, we need to go through an outlet.\n      if (future.component) {\n        this.traverseChildRoutes(futureNode, null, context ? context.children : null, futurePath);\n\n        // if we have a componentless route, we recurse but keep the same outlet map.\n      } else {\n        this.traverseChildRoutes(futureNode, null, parentContexts, futurePath);\n      }\n    }\n  }\n/**\n * @param {?} curr\n * @param {?} future\n * @param {?} mode\n * @return {?}\n */\nprivate shouldRunGuardsAndResolvers(\n      curr: ActivatedRouteSnapshot, future: ActivatedRouteSnapshot,\n      mode: RunGuardsAndResolvers|undefined): boolean {\n    switch (mode) {\n      case 'always':\n        return true;\n\n      case 'paramsOrQueryParamsChange':\n        return !equalParamsAndUrlSegments(curr, future) ||\n            !shallowEqual(curr.queryParams, future.queryParams);\n\n      case 'paramsChange':\n      default:\n        return !equalParamsAndUrlSegments(curr, future);\n    }\n  }\n/**\n * @param {?} route\n * @param {?} context\n * @return {?}\n */\nprivate deactivateRouteAndItsChildren(\n      route: TreeNode<ActivatedRouteSnapshot>, context: OutletContext|null): void {\n    const /** @type {?} */ children = nodeChildrenAsMap(route);\n    const /** @type {?} */ r = route.value;\n\n    forEach(children, (node: TreeNode<ActivatedRouteSnapshot>, childName: string) => {\n      if (!r.component) {\n        this.deactivateRouteAndItsChildren(node, context);\n      } else if (context) {\n        this.deactivateRouteAndItsChildren(node, context.children.getContext(childName));\n      } else {\n        this.deactivateRouteAndItsChildren(node, null);\n      }\n    });\n\n    if (!r.component) {\n      this.canDeactivateChecks.push(new CanDeactivate(null, r));\n    } else if (context && context.outlet && context.outlet.isActivated) {\n      this.canDeactivateChecks.push(new CanDeactivate(context.outlet.component, r));\n    } else {\n      this.canDeactivateChecks.push(new CanDeactivate(null, r));\n    }\n  }\n/**\n * @return {?}\n */\nprivate runCanDeactivateChecks(): Observable<boolean> {\n    const /** @type {?} */ checks$ = from(this.canDeactivateChecks);\n    const /** @type {?} */ runningChecks$ = mergeMap.call(\n        checks$, (check: CanDeactivate) => this.runCanDeactivate(check.component, check.route));\n    return every.call(runningChecks$, (result: boolean) => result === true);\n  }\n/**\n * @return {?}\n */\nprivate runCanActivateChecks(): Observable<boolean> {\n    const /** @type {?} */ checks$ = from(this.canActivateChecks);\n    const /** @type {?} */ runningChecks$ = mergeMap.call(\n        checks$, (check: CanActivate) => andObservables(from(\n                     [this.runCanActivateChild(check.path), this.runCanActivate(check.route)])));\n    return every.call(runningChecks$, (result: boolean) => result === true);\n  }\n/**\n * @param {?} future\n * @return {?}\n */\nprivate runCanActivate(future: ActivatedRouteSnapshot): Observable<boolean> {\n    const /** @type {?} */ canActivate = future._routeConfig ? future._routeConfig.canActivate : null;\n    if (!canActivate || canActivate.length === 0) return of (true);\n    const /** @type {?} */ obs = map.call(from(canActivate), (c: any) => {\n      const /** @type {?} */ guard = this.getToken(c, future);\n      let /** @type {?} */ observable: Observable<boolean>;\n      if (guard.canActivate) {\n        observable = wrapIntoObservable(guard.canActivate(future, this.future));\n      } else {\n        observable = wrapIntoObservable(guard(future, this.future));\n      }\n      return first.call(observable);\n    });\n    return andObservables(obs);\n  }\n/**\n * @param {?} path\n * @return {?}\n */\nprivate runCanActivateChild(path: ActivatedRouteSnapshot[]): Observable<boolean> {\n    const /** @type {?} */ future = path[path.length - 1];\n\n    const /** @type {?} */ canActivateChildGuards = path.slice(0, path.length - 1)\n                                       .reverse()\n                                       .map(p => this.extractCanActivateChild(p))\n                                       .filter(_ => _ !== null);\n\n    return andObservables(map.call(from(canActivateChildGuards), (d: any) => {\n      const /** @type {?} */ obs = map.call(from(d.guards), (c: any) => {\n        const /** @type {?} */ guard = this.getToken(c, d.node);\n        let /** @type {?} */ observable: Observable<boolean>;\n        if (guard.canActivateChild) {\n          observable = wrapIntoObservable(guard.canActivateChild(future, this.future));\n        } else {\n          observable = wrapIntoObservable(guard(future, this.future));\n        }\n        return first.call(observable);\n      });\n      return andObservables(obs);\n    }));\n  }\n/**\n * @param {?} p\n * @return {?}\n */\nprivate extractCanActivateChild(p: ActivatedRouteSnapshot):\n      {node: ActivatedRouteSnapshot, guards: any[]}|null {\n    const /** @type {?} */ canActivateChild = p._routeConfig ? p._routeConfig.canActivateChild : null;\n    if (!canActivateChild || canActivateChild.length === 0) return null;\n    return {node: p, guards: canActivateChild};\n  }\n/**\n * @param {?} component\n * @param {?} curr\n * @return {?}\n */\nprivate runCanDeactivate(component: Object|null, curr: ActivatedRouteSnapshot):\n      Observable<boolean> {\n    const /** @type {?} */ canDeactivate = curr && curr._routeConfig ? curr._routeConfig.canDeactivate : null;\n    if (!canDeactivate || canDeactivate.length === 0) return of (true);\n    const /** @type {?} */ canDeactivate$ = mergeMap.call(from(canDeactivate), (c: any) => {\n      const /** @type {?} */ guard = this.getToken(c, curr);\n      let /** @type {?} */ observable: Observable<boolean>;\n      if (guard.canDeactivate) {\n        observable =\n            wrapIntoObservable(guard.canDeactivate(component, curr, this.curr, this.future));\n      } else {\n        observable = wrapIntoObservable(guard(component, curr, this.curr, this.future));\n      }\n      return first.call(observable);\n    });\n    return every.call(canDeactivate$, (result: any) => result === true);\n  }\n/**\n * @param {?} future\n * @return {?}\n */\nprivate runResolve(future: ActivatedRouteSnapshot): Observable<any> {\n    const /** @type {?} */ resolve = future._resolve;\n    return map.call(this.resolveNode(resolve, future), (resolvedData: any): any => {\n      future._resolvedData = resolvedData;\n      future.data = {...future.data, ...inheritedParamsDataResolve(future).resolve};\n      return null;\n    });\n  }\n/**\n * @param {?} resolve\n * @param {?} future\n * @return {?}\n */\nprivate resolveNode(resolve: ResolveData, future: ActivatedRouteSnapshot): Observable<any> {\n    return waitForMap(resolve, (k, v) => {\n      const /** @type {?} */ resolver = this.getToken(v, future);\n      return resolver.resolve ? wrapIntoObservable(resolver.resolve(future, this.future)) :\n                                wrapIntoObservable(resolver(future, this.future));\n    });\n  }\n/**\n * @param {?} token\n * @param {?} snapshot\n * @return {?}\n */\nprivate getToken(token: any, snapshot: ActivatedRouteSnapshot): any {\n    const /** @type {?} */ config = closestLoadedConfig(snapshot);\n    const /** @type {?} */ injector = config ? config.module.injector : this.moduleInjector;\n    return injector.get(token);\n  }\n}\n\nfunction PreActivation_tsickle_Closure_declarations() {\n/** @type {?} */\nPreActivation.prototype.canActivateChecks;\n/** @type {?} */\nPreActivation.prototype.canDeactivateChecks;\n/** @type {?} */\nPreActivation.prototype.future;\n/** @type {?} */\nPreActivation.prototype.curr;\n/** @type {?} */\nPreActivation.prototype.moduleInjector;\n}\n\nclass ActivateRoutes {\n/**\n * @param {?} routeReuseStrategy\n * @param {?} futureState\n * @param {?} currState\n */\nconstructor(\nprivate routeReuseStrategy: RouteReuseStrategy,\nprivate futureState: RouterState,\nprivate currState: RouterState) {}\n/**\n * @param {?} parentContexts\n * @return {?}\n */\nactivate(parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ futureRoot = this.futureState._root;\n    const /** @type {?} */ currRoot = this.currState ? this.currState._root : null;\n\n    this.deactivateChildRoutes(futureRoot, currRoot, parentContexts);\n    advanceActivatedRoute(this.futureState.root);\n    this.activateChildRoutes(futureRoot, currRoot, parentContexts);\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @return {?}\n */\nprivate deactivateChildRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>|null,\n      contexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ children: {[outletName: string]: TreeNode<ActivatedRoute>} = nodeChildrenAsMap(currNode);\n\n    // Recurse on the routes active in the future state to de-activate deeper children\n    futureNode.children.forEach(futureChild => {\n      const /** @type {?} */ childOutletName = futureChild.value.outlet;\n      this.deactivateRoutes(futureChild, children[childOutletName], contexts);\n      delete children[childOutletName];\n    });\n\n    // De-activate the routes that will not be re-used\n    forEach(children, (v: TreeNode<ActivatedRoute>, childName: string) => {\n      this.deactivateRouteAndItsChildren(v, contexts);\n    });\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContext\n * @return {?}\n */\nprivate deactivateRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>,\n      parentContext: ChildrenOutletContexts): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n\n    if (future === curr) {\n      // Reusing the node, check to see if the children need to be de-activated\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const /** @type {?} */ context = parentContext.getContext(future.outlet);\n        if (context) {\n          this.deactivateChildRoutes(futureNode, currNode, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.deactivateChildRoutes(futureNode, currNode, parentContext);\n      }\n    } else {\n      if (curr) {\n        // Deactivate the current route which will not be re-used\n        this.deactivateRouteAndItsChildren(currNode, parentContext);\n      }\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate deactivateRouteAndItsChildren(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    if (this.routeReuseStrategy.shouldDetach(route.value.snapshot)) {\n      this.detachAndStoreRouteSubtree(route, parentContexts);\n    } else {\n      this.deactivateRouteAndOutlet(route, parentContexts);\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate detachAndStoreRouteSubtree(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ context = parentContexts.getContext(route.value.outlet);\n    if (context && context.outlet) {\n      const /** @type {?} */ componentRef = context.outlet.detach();\n      const /** @type {?} */ contexts = context.children.onOutletDeactivated();\n      this.routeReuseStrategy.store(route.value.snapshot, {componentRef, route, contexts});\n    }\n  }\n/**\n * @param {?} route\n * @param {?} parentContexts\n * @return {?}\n */\nprivate deactivateRouteAndOutlet(\n      route: TreeNode<ActivatedRoute>, parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ context = parentContexts.getContext(route.value.outlet);\n\n    if (context) {\n      const /** @type {?} */ children: {[outletName: string]: any} = nodeChildrenAsMap(route);\n      const /** @type {?} */ contexts = route.value.component ? context.children : parentContexts;\n\n      forEach(children, (v: any, k: string) => {this.deactivateRouteAndItsChildren(v, contexts)});\n\n      if (context.outlet) {\n        // Destroy the component\n        context.outlet.deactivate();\n        // Destroy the contexts for all the outlets that were in the component\n        context.children.onOutletDeactivated();\n      }\n    }\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} contexts\n * @return {?}\n */\nprivate activateChildRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>|null,\n      contexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ children: {[outlet: string]: any} = nodeChildrenAsMap(currNode);\n    futureNode.children.forEach(\n        c => { this.activateRoutes(c, children[c.value.outlet], contexts); });\n  }\n/**\n * @param {?} futureNode\n * @param {?} currNode\n * @param {?} parentContexts\n * @return {?}\n */\nprivate activateRoutes(\n      futureNode: TreeNode<ActivatedRoute>, currNode: TreeNode<ActivatedRoute>,\n      parentContexts: ChildrenOutletContexts): void {\n    const /** @type {?} */ future = futureNode.value;\n    const /** @type {?} */ curr = currNode ? currNode.value : null;\n\n    advanceActivatedRoute(future);\n\n    // reusing the node\n    if (future === curr) {\n      if (future.component) {\n        // If we have a normal route, we need to go through an outlet.\n        const /** @type {?} */ context = parentContexts.getOrCreateContext(future.outlet);\n        this.activateChildRoutes(futureNode, currNode, context.children);\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, currNode, parentContexts);\n      }\n    } else {\n      if (future.component) {\n        // if we have a normal route, we need to place the component into the outlet and recurse.\n        const /** @type {?} */ context = parentContexts.getOrCreateContext(future.outlet);\n\n        if (this.routeReuseStrategy.shouldAttach(future.snapshot)) {\n          const /** @type {?} */ stored =\n              ( /** @type {?} */((<DetachedRouteHandleInternal>this.routeReuseStrategy.retrieve(future.snapshot))));\n          this.routeReuseStrategy.store(future.snapshot, null);\n          context.children.onOutletReAttached(stored.contexts);\n          context.attachRef = stored.componentRef;\n          context.route = stored.route.value;\n          if (context.outlet) {\n            // Attach right away when the outlet has already been instantiated\n            // Otherwise attach from `RouterOutlet.ngOnInit` when it is instantiated\n            context.outlet.attach(stored.componentRef, stored.route.value);\n          }\n          advanceActivatedRouteNodeAndItsChildren(stored.route);\n        } else {\n          const /** @type {?} */ config = parentLoadedConfig(future.snapshot);\n          const /** @type {?} */ cmpFactoryResolver = config ? config.module.componentFactoryResolver : null;\n\n          context.route = future;\n          context.resolver = cmpFactoryResolver;\n          if (context.outlet) {\n            // Activate the outlet when it has already been instantiated\n            // Otherwise it will get activated from its `ngOnInit` when instantiated\n            context.outlet.activateWith(future, cmpFactoryResolver);\n          }\n\n          this.activateChildRoutes(futureNode, null, context.children);\n        }\n      } else {\n        // if we have a componentless route, we recurse but keep the same outlet map.\n        this.activateChildRoutes(futureNode, null, parentContexts);\n      }\n    }\n  }\n}\n\nfunction ActivateRoutes_tsickle_Closure_declarations() {\n/** @type {?} */\nActivateRoutes.prototype.routeReuseStrategy;\n/** @type {?} */\nActivateRoutes.prototype.futureState;\n/** @type {?} */\nActivateRoutes.prototype.currState;\n}\n\n/**\n * @param {?} node\n * @return {?}\n */\nfunction advanceActivatedRouteNodeAndItsChildren(node: TreeNode<ActivatedRoute>): void {\n  advanceActivatedRoute(node.value);\n  node.children.forEach(advanceActivatedRouteNodeAndItsChildren);\n}\n/**\n * @param {?} snapshot\n * @return {?}\n */\nfunction parentLoadedConfig(snapshot: ActivatedRouteSnapshot): LoadedRouterConfig|null {\n  for (let /** @type {?} */ s = snapshot.parent; s; s = s.parent) {\n    const /** @type {?} */ route = s._routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n    if (route && route.component) return null;\n  }\n\n  return null;\n}\n/**\n * @param {?} snapshot\n * @return {?}\n */\nfunction closestLoadedConfig(snapshot: ActivatedRouteSnapshot): LoadedRouterConfig|null {\n  if (!snapshot) return null;\n\n  for (let /** @type {?} */ s = snapshot.parent; s; s = s.parent) {\n    const /** @type {?} */ route = s._routeConfig;\n    if (route && route._loadedConfig) return route._loadedConfig;\n  }\n\n  return null;\n}\n/**\n * @template T\n * @param {?} node\n * @return {?}\n */\nfunction nodeChildrenAsMap<T extends{outlet: string}>(node: TreeNode<T>| null) {\n  const /** @type {?} */ map: {[outlet: string]: TreeNode<T>} = {};\n\n  if (node) {\n    node.children.forEach(child => map[child.value.outlet] = child);\n  }\n\n  return map;\n}\n/**\n * @param {?} commands\n * @return {?}\n */\nfunction validateCommands(commands: string[]): void {\n  for (let /** @type {?} */ i = 0; i < commands.length; i++) {\n    const /** @type {?} */ cmd = commands[i];\n    if (cmd == null) {\n      throw new Error(`The requested path contains ${cmd} segment at index ${i}`);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {UrlTree} from './url_tree';\n/**\n * \\@whatItDoes Provides a way to migrate AngularJS applications to Angular.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class UrlHandlingStrategy {\n/**\n * Tells the router if this URL should be processed.\n * \n * When it returns true, the router will execute the regular navigation.\n * When it returns false, the router will set the router state to an empty state.\n * As a result, all the active components will be destroyed.\n * \n * @abstract\n * @param {?} url\n * @return {?}\n */\nshouldProcessUrl(url: UrlTree) {}\n/**\n * Extracts the part of the URL that should be handled by the router.\n * The rest of the URL will remain untouched.\n * @abstract\n * @param {?} url\n * @return {?}\n */\nextract(url: UrlTree) {}\n/**\n * Merges the URL fragment with the rest of the URL.\n * @abstract\n * @param {?} newUrlPart\n * @param {?} rawUrl\n * @return {?}\n */\nmerge(newUrlPart: UrlTree, rawUrl: UrlTree) {}\n}\n/**\n * \\@experimental\n */\nexport class DefaultUrlHandlingStrategy implements UrlHandlingStrategy {\n/**\n * @param {?} url\n * @return {?}\n */\nshouldProcessUrl(url: UrlTree): boolean { return true; }\n/**\n * @param {?} url\n * @return {?}\n */\nextract(url: UrlTree): UrlTree { return url; }\n/**\n * @param {?} newUrlPart\n * @param {?} wholeUrl\n * @return {?}\n */\nmerge(newUrlPart: UrlTree, wholeUrl: UrlTree): UrlTree { return newUrlPart; }\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Compiler, InjectionToken, Injector, NgModuleFactory, NgModuleFactoryLoader, NgModuleRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {of } from 'rxjs/observable/of';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {LoadChildren, LoadedRouterConfig, Route} from './config';\nimport {flatten, wrapIntoObservable} from './utils/collection';\n/**\n * \\@docsNotRequired\n * \\@experimental\n */\nexport const ROUTES = new InjectionToken<Route[][]>('ROUTES');\nexport class RouterConfigLoader {\n/**\n * @param {?} loader\n * @param {?} compiler\n * @param {?=} onLoadStartListener\n * @param {?=} onLoadEndListener\n */\nconstructor(\nprivate loader: NgModuleFactoryLoader,\nprivate compiler: Compiler,\nprivate onLoadStartListener?: (r: Route) => void,\nprivate onLoadEndListener?: (r: Route) => void) {}\n/**\n * @param {?} parentInjector\n * @param {?} route\n * @return {?}\n */\nload(parentInjector: Injector, route: Route): Observable<LoadedRouterConfig> {\n    if (this.onLoadStartListener) {\n      this.onLoadStartListener(route);\n    }\n\n    const /** @type {?} */ moduleFactory$ = this.loadModuleFactory( /** @type {?} */((route.loadChildren)));\n\n    return map.call(moduleFactory$, (factory: NgModuleFactory<any>) => {\n      if (this.onLoadEndListener) {\n        this.onLoadEndListener(route);\n      }\n\n      const /** @type {?} */ module = factory.create(parentInjector);\n\n      return new LoadedRouterConfig(flatten(module.injector.get(ROUTES)), module);\n    });\n  }\n/**\n * @param {?} loadChildren\n * @return {?}\n */\nprivate loadModuleFactory(loadChildren: LoadChildren): Observable<NgModuleFactory<any>> {\n    if (typeof loadChildren === 'string') {\n      return fromPromise(this.loader.load(loadChildren));\n    } else {\n      return mergeMap.call(wrapIntoObservable(loadChildren()), (t: any) => {\n        if (t instanceof NgModuleFactory) {\n          return of (t);\n        } else {\n          return fromPromise(this.compiler.compileModuleAsync(t));\n        }\n      });\n    }\n  }\n}\n\nfunction RouterConfigLoader_tsickle_Closure_declarations() {\n/** @type {?} */\nRouterConfigLoader.prototype.loader;\n/** @type {?} */\nRouterConfigLoader.prototype.compiler;\n/** @type {?} */\nRouterConfigLoader.prototype.onLoadStartListener;\n/** @type {?} */\nRouterConfigLoader.prototype.onLoadEndListener;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentRef} from '@angular/core';\n\nimport {OutletContext} from './router_outlet_context';\nimport {ActivatedRoute, ActivatedRouteSnapshot} from './router_state';\nimport {TreeNode} from './utils/tree';\n\n/**\n * @whatItDoes Represents the detached route tree.\n *\n * This is an opaque value the router will give to a custom route reuse strategy\n * to store and retrieve later on.\n *\n * @experimental\n */\nexport type DetachedRouteHandle = {};\n\n/** @internal */\nexport type DetachedRouteHandleInternal = {\n  contexts: Map<string, OutletContext>,\n  componentRef: ComponentRef<any>,\n  route: TreeNode<ActivatedRoute>,\n};\n/**\n * \\@whatItDoes Provides a way to customize when activated routes get reused.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class RouteReuseStrategy {\n/**\n * Determines if this route (and its subtree) should be detached to be reused later\n * @abstract\n * @param {?} route\n * @return {?}\n */\nshouldDetach(route: ActivatedRouteSnapshot) {}\n/**\n * Stores the detached route.\n * \n * Storing a `null` value should erase the previously stored value.\n * @abstract\n * @param {?} route\n * @param {?} handle\n * @return {?}\n */\nstore(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle|null) {}\n/**\n * Determines if this route (and its subtree) should be reattached\n * @abstract\n * @param {?} route\n * @return {?}\n */\nshouldAttach(route: ActivatedRouteSnapshot) {}\n/**\n * Retrieves the previously stored route\n * @abstract\n * @param {?} route\n * @return {?}\n */\nretrieve(route: ActivatedRouteSnapshot) {}\n/**\n * Determines if a route should be reused\n * @abstract\n * @param {?} future\n * @param {?} curr\n * @return {?}\n */\nshouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot) {}\n}\n/**\n * Does not detach any subtrees. Reuses routes as long as their route config is the same.\n */\nexport class DefaultRouteReuseStrategy implements RouteReuseStrategy {\n/**\n * @param {?} route\n * @return {?}\n */\nshouldDetach(route: ActivatedRouteSnapshot): boolean { return false; }\n/**\n * @param {?} route\n * @param {?} detachedTree\n * @return {?}\n */\nstore(route: ActivatedRouteSnapshot, detachedTree: DetachedRouteHandle): void {}\n/**\n * @param {?} route\n * @return {?}\n */\nshouldAttach(route: ActivatedRouteSnapshot): boolean { return false; }\n/**\n * @param {?} route\n * @return {?}\n */\nretrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle|null { return null; }\n/**\n * @param {?} future\n * @param {?} curr\n * @return {?}\n */\nshouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {\n    return future.routeConfig === curr.routeConfig;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {of } from 'rxjs/observable/of';\n\nimport {Data, ResolveData, Route, Routes} from './config';\nimport {ActivatedRouteSnapshot, RouterStateSnapshot, inheritedParamsDataResolve} from './router_state';\nimport {PRIMARY_OUTLET, defaultUrlMatcher} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree, mapChildrenIntoArray} from './url_tree';\nimport {forEach, last} from './utils/collection';\nimport {TreeNode} from './utils/tree';\nclass NoMatch {}\n/**\n * @param {?} rootComponentType\n * @param {?} config\n * @param {?} urlTree\n * @param {?} url\n * @return {?}\n */\nexport function recognize(\n    rootComponentType: Type<any>| null, config: Routes, urlTree: UrlTree,\n    url: string): Observable<RouterStateSnapshot> {\n  return new Recognizer(rootComponentType, config, urlTree, url).recognize();\n}\nclass Recognizer {\n/**\n * @param {?} rootComponentType\n * @param {?} config\n * @param {?} urlTree\n * @param {?} url\n */\nconstructor(\nprivate rootComponentType: Type<any>|null,\nprivate config: Routes,\nprivate urlTree: UrlTree,\nprivate url: string) {}\n/**\n * @return {?}\n */\nrecognize(): Observable<RouterStateSnapshot> {\n    try {\n      const /** @type {?} */ rootSegmentGroup = split(this.urlTree.root, [], [], this.config).segmentGroup;\n\n      const /** @type {?} */ children = this.processSegmentGroup(this.config, rootSegmentGroup, PRIMARY_OUTLET);\n\n      const /** @type {?} */ root = new ActivatedRouteSnapshot(\n          [], Object.freeze({}), Object.freeze(this.urlTree.queryParams), /** @type {?} */(( this.urlTree.fragment)),\n          {}, PRIMARY_OUTLET, this.rootComponentType, null, this.urlTree.root, -1, {});\n\n      const /** @type {?} */ rootNode = new TreeNode<ActivatedRouteSnapshot>(root, children);\n      const /** @type {?} */ routeState = new RouterStateSnapshot(this.url, rootNode);\n      this.inheritParamsAndData(routeState._root);\n      return of (routeState);\n\n    } catch ( /** @type {?} */e) {\n      return new Observable<RouterStateSnapshot>(\n          (obs: Observer<RouterStateSnapshot>) => obs.error(e));\n    }\n  }\n/**\n * @param {?} routeNode\n * @return {?}\n */\ninheritParamsAndData(routeNode: TreeNode<ActivatedRouteSnapshot>): void {\n    const /** @type {?} */ route = routeNode.value;\n\n    const /** @type {?} */ i = inheritedParamsDataResolve(route);\n    route.params = Object.freeze(i.params);\n    route.data = Object.freeze(i.data);\n\n    routeNode.children.forEach(n => this.inheritParamsAndData(n));\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @param {?} outlet\n * @return {?}\n */\nprocessSegmentGroup(config: Route[], segmentGroup: UrlSegmentGroup, outlet: string):\n      TreeNode<ActivatedRouteSnapshot>[] {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return this.processChildren(config, segmentGroup);\n    }\n\n    return this.processSegment(config, segmentGroup, segmentGroup.segments, outlet);\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @return {?}\n */\nprocessChildren(config: Route[], segmentGroup: UrlSegmentGroup):\n      TreeNode<ActivatedRouteSnapshot>[] {\n    const /** @type {?} */ children = mapChildrenIntoArray(\n        segmentGroup, (child, childOutlet) => this.processSegmentGroup(config, child, childOutlet));\n    checkOutletNameUniqueness(children);\n    sortActivatedRouteSnapshots(children);\n    return children;\n  }\n/**\n * @param {?} config\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprocessSegment(\n      config: Route[], segmentGroup: UrlSegmentGroup, segments: UrlSegment[],\n      outlet: string): TreeNode<ActivatedRouteSnapshot>[] {\n    for (const /** @type {?} */ r of config) {\n      try {\n        return this.processSegmentAgainstRoute(r, segmentGroup, segments, outlet);\n      } catch ( /** @type {?} */e) {\n        if (!(e instanceof NoMatch)) throw e;\n      }\n    }\n    if (this.noLeftoversInUrl(segmentGroup, segments, outlet)) {\n      return [];\n    }\n\n    throw new NoMatch();\n  }\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate noLeftoversInUrl(segmentGroup: UrlSegmentGroup, segments: UrlSegment[], outlet: string):\n      boolean {\n    return segments.length === 0 && !segmentGroup.children[outlet];\n  }\n/**\n * @param {?} route\n * @param {?} rawSegment\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprocessSegmentAgainstRoute(\n      route: Route, rawSegment: UrlSegmentGroup, segments: UrlSegment[],\n      outlet: string): TreeNode<ActivatedRouteSnapshot>[] {\n    if (route.redirectTo) throw new NoMatch();\n\n    if ((route.outlet || PRIMARY_OUTLET) !== outlet) throw new NoMatch();\n\n    if (route.path === '**') {\n      const /** @type {?} */ params = segments.length > 0 ? /** @type {?} */(( last(segments))).parameters : {};\n      const /** @type {?} */ snapshot = new ActivatedRouteSnapshot(\n          segments, params, Object.freeze(this.urlTree.queryParams), /** @type {?} */(( this.urlTree.fragment)),\n          getData(route), outlet, /** @type {?} */(( route.component)), route, getSourceSegmentGroup(rawSegment),\n          getPathIndexShift(rawSegment) + segments.length, getResolve(route));\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, [])];\n    }\n\n    const {consumedSegments, parameters, lastChild} = match(rawSegment, route, segments);\n    const /** @type {?} */ rawSlicedSegments = segments.slice(lastChild);\n    const /** @type {?} */ childConfig = getChildConfig(route);\n\n    const {segmentGroup, slicedSegments} =\n        split(rawSegment, consumedSegments, rawSlicedSegments, childConfig);\n\n    const /** @type {?} */ snapshot = new ActivatedRouteSnapshot(\n        consumedSegments, parameters, Object.freeze(this.urlTree.queryParams), /** @type {?} */((\n        this.urlTree.fragment)), getData(route), outlet, /** @type {?} */(( route.component)), route,\n        getSourceSegmentGroup(rawSegment), getPathIndexShift(rawSegment) + consumedSegments.length,\n        getResolve(route));\n\n\n    if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n      const /** @type {?} */ children = this.processChildren(childConfig, segmentGroup);\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, children)];\n    }\n\n    if (childConfig.length === 0 && slicedSegments.length === 0) {\n      return [new TreeNode<ActivatedRouteSnapshot>(snapshot, [])];\n    }\n\n    const /** @type {?} */ children = this.processSegment(childConfig, segmentGroup, slicedSegments, PRIMARY_OUTLET);\n    return [new TreeNode<ActivatedRouteSnapshot>(snapshot, children)];\n  }\n}\n\nfunction Recognizer_tsickle_Closure_declarations() {\n/** @type {?} */\nRecognizer.prototype.rootComponentType;\n/** @type {?} */\nRecognizer.prototype.config;\n/** @type {?} */\nRecognizer.prototype.urlTree;\n/** @type {?} */\nRecognizer.prototype.url;\n}\n\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction sortActivatedRouteSnapshots(nodes: TreeNode<ActivatedRouteSnapshot>[]): void {\n  nodes.sort((a, b) => {\n    if (a.value.outlet === PRIMARY_OUTLET) return -1;\n    if (b.value.outlet === PRIMARY_OUTLET) return 1;\n    return a.value.outlet.localeCompare(b.value.outlet);\n  });\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getChildConfig(route: Route): Route[] {\n  if (route.children) {\n    return route.children;\n  }\n\n  if (route.loadChildren) {\n    return /** @type {?} */(( route._loadedConfig)).routes;\n  }\n\n  return [];\n}\n/**\n * @param {?} segmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nfunction match(segmentGroup: UrlSegmentGroup, route: Route, segments: UrlSegment[]) {\n  if (route.path === '') {\n    if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {\n      throw new NoMatch();\n    }\n\n    return {consumedSegments: [], lastChild: 0, parameters: {}};\n  }\n\n  const /** @type {?} */ matcher = route.matcher || defaultUrlMatcher;\n  const /** @type {?} */ res = matcher(segments, segmentGroup, route);\n  if (!res) throw new NoMatch();\n\n  const /** @type {?} */ posParams: {[n: string]: string} = {};\n  forEach( /** @type {?} */((res.posParams)), (v: UrlSegment, k: string) => { posParams[k] = v.path; });\n  const /** @type {?} */ parameters = {...posParams, ...res.consumed[res.consumed.length - 1].parameters};\n\n  return {consumedSegments: res.consumed, lastChild: res.consumed.length, parameters};\n}\n/**\n * @param {?} nodes\n * @return {?}\n */\nfunction checkOutletNameUniqueness(nodes: TreeNode<ActivatedRouteSnapshot>[]): void {\n  const /** @type {?} */ names: {[k: string]: ActivatedRouteSnapshot} = {};\n  nodes.forEach(n => {\n    const /** @type {?} */ routeWithSameOutletName = names[n.value.outlet];\n    if (routeWithSameOutletName) {\n      const /** @type {?} */ p = routeWithSameOutletName.url.map(s => s.toString()).join('/');\n      const /** @type {?} */ c = n.value.url.map(s => s.toString()).join('/');\n      throw new Error(`Two segments cannot have the same outlet name: '${p}' and '${c}'.`);\n    }\n    names[n.value.outlet] = n.value;\n  });\n}\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction getSourceSegmentGroup(segmentGroup: UrlSegmentGroup): UrlSegmentGroup {\n  let /** @type {?} */ s = segmentGroup;\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n  }\n  return s;\n}\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction getPathIndexShift(segmentGroup: UrlSegmentGroup): number {\n  let /** @type {?} */ s = segmentGroup;\n  let /** @type {?} */ res = (s._segmentIndexShift ? s._segmentIndexShift : 0);\n  while (s._sourceSegment) {\n    s = s._sourceSegment;\n    res += (s._segmentIndexShift ? s._segmentIndexShift : 0);\n  }\n  return res - 1;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} slicedSegments\n * @param {?} config\n * @return {?}\n */\nfunction split(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], slicedSegments: UrlSegment[],\n    config: Route[]) {\n  if (slicedSegments.length > 0 &&\n      containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        consumedSegments, createChildrenForEmptyPaths(\n                              segmentGroup, consumedSegments, config,\n                              new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {segmentGroup: s, slicedSegments: []};\n  }\n\n  if (slicedSegments.length === 0 &&\n      containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(\n                                   segmentGroup, slicedSegments, config, segmentGroup.children));\n    s._sourceSegment = segmentGroup;\n    s._segmentIndexShift = consumedSegments.length;\n    return {segmentGroup: s, slicedSegments};\n  }\n\n  const /** @type {?} */ s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);\n  s._sourceSegment = segmentGroup;\n  s._segmentIndexShift = consumedSegments.length;\n  return {segmentGroup: s, slicedSegments};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @param {?} children\n * @return {?}\n */\nfunction addEmptyPathsToChildrenIfNeeded(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[],\n    children: {[name: string]: UrlSegmentGroup}): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  for (const /** @type {?} */ r of routes) {\n    if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      const /** @type {?} */ s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n      s._segmentIndexShift = segmentGroup.segments.length;\n      res[getOutlet(r)] = s;\n    }\n  }\n  return {...children, ...res};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} routes\n * @param {?} primarySegment\n * @return {?}\n */\nfunction createChildrenForEmptyPaths(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], routes: Route[],\n    primarySegment: UrlSegmentGroup): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  res[PRIMARY_OUTLET] = primarySegment;\n  primarySegment._sourceSegment = segmentGroup;\n  primarySegment._segmentIndexShift = consumedSegments.length;\n\n  for (const /** @type {?} */ r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      const /** @type {?} */ s = new UrlSegmentGroup([], {});\n      s._sourceSegment = segmentGroup;\n      s._segmentIndexShift = consumedSegments.length;\n      res[getOutlet(r)] = s;\n    }\n  }\n  return res;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathMatchesWithNamedOutlets(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(\n      r => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathMatches(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(r => emptyPathMatch(segmentGroup, slicedSegments, r));\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} r\n * @return {?}\n */\nfunction emptyPathMatch(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], r: Route): boolean {\n  if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n\n  return r.path === '' && r.redirectTo === undefined;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getOutlet(route: Route): string {\n  return route.outlet || PRIMARY_OUTLET;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getData(route: Route): Data {\n  return route.data || {};\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getResolve(route: Route): ResolveData {\n  return route.resolve || {};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ActivatedRoute} from './router_state';\nimport {PRIMARY_OUTLET, Params} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree} from './url_tree';\nimport {forEach, last, shallowEqual} from './utils/collection';\n/**\n * @param {?} route\n * @param {?} urlTree\n * @param {?} commands\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nexport function createUrlTree(\n    route: ActivatedRoute, urlTree: UrlTree, commands: any[], queryParams: Params,\n    fragment: string): UrlTree {\n  if (commands.length === 0) {\n    return tree(urlTree.root, urlTree.root, urlTree, queryParams, fragment);\n  }\n\n  const /** @type {?} */ nav = computeNavigation(commands);\n\n  if (nav.toRoot()) {\n    return tree(urlTree.root, new UrlSegmentGroup([], {}), urlTree, queryParams, fragment);\n  }\n\n  const /** @type {?} */ startingPosition = findStartingPosition(nav, urlTree, route);\n\n  const /** @type {?} */ segmentGroup = startingPosition.processChildren ?\n      updateSegmentGroupChildren(\n          startingPosition.segmentGroup, startingPosition.index, nav.commands) :\n      updateSegmentGroup(startingPosition.segmentGroup, startingPosition.index, nav.commands);\n  return tree(startingPosition.segmentGroup, segmentGroup, urlTree, queryParams, fragment);\n}\n/**\n * @param {?} command\n * @return {?}\n */\nfunction isMatrixParams(command: any): boolean {\n  return typeof command === 'object' && command != null && !command.outlets && !command.segmentPath;\n}\n/**\n * @param {?} oldSegmentGroup\n * @param {?} newSegmentGroup\n * @param {?} urlTree\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nfunction tree(\n    oldSegmentGroup: UrlSegmentGroup, newSegmentGroup: UrlSegmentGroup, urlTree: UrlTree,\n    queryParams: Params, fragment: string): UrlTree {\n  let /** @type {?} */ qp: any = {};\n  if (queryParams) {\n    forEach(queryParams, (value: any, name: any) => {\n      qp[name] = Array.isArray(value) ? value.map((v: any) => `${v}`) : `${value}`;\n    });\n  }\n\n  if (urlTree.root === oldSegmentGroup) {\n    return new UrlTree(newSegmentGroup, qp, fragment);\n  }\n\n  return new UrlTree(replaceSegment(urlTree.root, oldSegmentGroup, newSegmentGroup), qp, fragment);\n}\n/**\n * @param {?} current\n * @param {?} oldSegment\n * @param {?} newSegment\n * @return {?}\n */\nfunction replaceSegment(\n    current: UrlSegmentGroup, oldSegment: UrlSegmentGroup,\n    newSegment: UrlSegmentGroup): UrlSegmentGroup {\n  const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n  forEach(current.children, (c: UrlSegmentGroup, outletName: string) => {\n    if (c === oldSegment) {\n      children[outletName] = newSegment;\n    } else {\n      children[outletName] = replaceSegment(c, oldSegment, newSegment);\n    }\n  });\n  return new UrlSegmentGroup(current.segments, children);\n}\nclass Navigation {\n/**\n * @param {?} isAbsolute\n * @param {?} numberOfDoubleDots\n * @param {?} commands\n */\nconstructor(\npublic isAbsolute: boolean,\npublic numberOfDoubleDots: number,\npublic commands: any[]) {\n    if (isAbsolute && commands.length > 0 && isMatrixParams(commands[0])) {\n      throw new Error('Root segment cannot have matrix parameters');\n    }\n\n    const cmdWithOutlet = commands.find(c => typeof c === 'object' && c != null && c.outlets);\n    if (cmdWithOutlet && cmdWithOutlet !== last(commands)) {\n      throw new Error('{outlets:{}} has to be the last command');\n    }\n  }\n/**\n * @return {?}\n */\npublic toRoot(): boolean {\n    return this.isAbsolute && this.commands.length === 1 && this.commands[0] == '/';\n  }\n}\n\nfunction Navigation_tsickle_Closure_declarations() {\n/** @type {?} */\nNavigation.prototype.isAbsolute;\n/** @type {?} */\nNavigation.prototype.numberOfDoubleDots;\n/** @type {?} */\nNavigation.prototype.commands;\n}\n\n/**\n * Transforms commands to a normalized `Navigation`\n * @param {?} commands\n * @return {?}\n */\nfunction computeNavigation(commands: any[]): Navigation {\n  if ((typeof commands[0] === 'string') && commands.length === 1 && commands[0] === '/') {\n    return new Navigation(true, 0, commands);\n  }\n\n  let /** @type {?} */ numberOfDoubleDots = 0;\n  let /** @type {?} */ isAbsolute = false;\n\n  const /** @type {?} */ res: any[] = commands.reduce((res, cmd, cmdIdx) => {\n    if (typeof cmd === 'object' && cmd != null) {\n      if (cmd.outlets) {\n        const /** @type {?} */ outlets: {[k: string]: any} = {};\n        forEach(cmd.outlets, (commands: any, name: string) => {\n          outlets[name] = typeof commands === 'string' ? commands.split('/') : commands;\n        });\n        return [...res, {outlets}];\n      }\n\n      if (cmd.segmentPath) {\n        return [...res, cmd.segmentPath];\n      }\n    }\n\n    if (!(typeof cmd === 'string')) {\n      return [...res, cmd];\n    }\n\n    if (cmdIdx === 0) {\n      cmd.split('/').forEach((urlPart, partIndex) => {\n        if (partIndex == 0 && urlPart === '.') {\n          // skip './a'\n        } else if (partIndex == 0 && urlPart === '') {  //  '/a'\n          isAbsolute = true;\n        } else if (urlPart === '..') {  //  '../a'\n          numberOfDoubleDots++;\n        } else if (urlPart != '') {\n          res.push(urlPart);\n        }\n      });\n\n      return res;\n    }\n\n    return [...res, cmd];\n  }, []);\n\n  return new Navigation(isAbsolute, numberOfDoubleDots, res);\n}\nclass Position {\n/**\n * @param {?} segmentGroup\n * @param {?} processChildren\n * @param {?} index\n */\nconstructor(\npublic segmentGroup: UrlSegmentGroup,\npublic processChildren: boolean,\npublic index: number) {\n  }\n}\n\nfunction Position_tsickle_Closure_declarations() {\n/** @type {?} */\nPosition.prototype.segmentGroup;\n/** @type {?} */\nPosition.prototype.processChildren;\n/** @type {?} */\nPosition.prototype.index;\n}\n\n/**\n * @param {?} nav\n * @param {?} tree\n * @param {?} route\n * @return {?}\n */\nfunction findStartingPosition(nav: Navigation, tree: UrlTree, route: ActivatedRoute): Position {\n  if (nav.isAbsolute) {\n    return new Position(tree.root, true, 0);\n  }\n\n  if (route.snapshot._lastPathIndex === -1) {\n    return new Position(route.snapshot._urlSegment, true, 0);\n  }\n\n  const /** @type {?} */ modifier = isMatrixParams(nav.commands[0]) ? 0 : 1;\n  const /** @type {?} */ index = route.snapshot._lastPathIndex + modifier;\n  return createPositionApplyingDoubleDots(\n      route.snapshot._urlSegment, index, nav.numberOfDoubleDots);\n}\n/**\n * @param {?} group\n * @param {?} index\n * @param {?} numberOfDoubleDots\n * @return {?}\n */\nfunction createPositionApplyingDoubleDots(\n    group: UrlSegmentGroup, index: number, numberOfDoubleDots: number): Position {\n  let /** @type {?} */ g = group;\n  let /** @type {?} */ ci = index;\n  let /** @type {?} */ dd = numberOfDoubleDots;\n  while (dd > ci) {\n    dd -= ci;\n    g = /** @type {?} */(( g.parent));\n    if (!g) {\n      throw new Error('Invalid number of \\'../\\'');\n    }\n    ci = g.segments.length;\n  }\n  return new Position(g, false, ci - dd);\n}\n/**\n * @param {?} command\n * @return {?}\n */\nfunction getPath(command: any): any {\n  if (typeof command === 'object' && command != null && command.outlets) {\n    return command.outlets[PRIMARY_OUTLET];\n  }\n  return `${command}`;\n}\n/**\n * @param {?} commands\n * @return {?}\n */\nfunction getOutlets(commands: any[]): {[k: string]: any[]} {\n  if (!(typeof commands[0] === 'object')) return {[PRIMARY_OUTLET]: commands};\n  if (commands[0].outlets === undefined) return {[PRIMARY_OUTLET]: commands};\n  return commands[0].outlets;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction updateSegmentGroup(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  if (!segmentGroup) {\n    segmentGroup = new UrlSegmentGroup([], {});\n  }\n  if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n    return updateSegmentGroupChildren(segmentGroup, startIndex, commands);\n  }\n\n  const /** @type {?} */ m = prefixedWith(segmentGroup, startIndex, commands);\n  const /** @type {?} */ slicedCommands = commands.slice(m.commandIndex);\n  if (m.match && m.pathIndex < segmentGroup.segments.length) {\n    const /** @type {?} */ g = new UrlSegmentGroup(segmentGroup.segments.slice(0, m.pathIndex), {});\n    g.children[PRIMARY_OUTLET] =\n        new UrlSegmentGroup(segmentGroup.segments.slice(m.pathIndex), segmentGroup.children);\n    return updateSegmentGroupChildren(g, 0, slicedCommands);\n  } else if (m.match && slicedCommands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else if (m.match && !segmentGroup.hasChildren()) {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  } else if (m.match) {\n    return updateSegmentGroupChildren(segmentGroup, 0, slicedCommands);\n  } else {\n    return createNewSegmentGroup(segmentGroup, startIndex, commands);\n  }\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction updateSegmentGroupChildren(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  if (commands.length === 0) {\n    return new UrlSegmentGroup(segmentGroup.segments, {});\n  } else {\n    const /** @type {?} */ outlets = getOutlets(commands);\n    const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n\n    forEach(outlets, (commands: any, outlet: string) => {\n      if (commands !== null) {\n        children[outlet] = updateSegmentGroup(segmentGroup.children[outlet], startIndex, commands);\n      }\n    });\n\n    forEach(segmentGroup.children, (child: UrlSegmentGroup, childOutlet: string) => {\n      if (outlets[childOutlet] === undefined) {\n        children[childOutlet] = child;\n      }\n    });\n    return new UrlSegmentGroup(segmentGroup.segments, children);\n  }\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction prefixedWith(segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]) {\n  let /** @type {?} */ currentCommandIndex = 0;\n  let /** @type {?} */ currentPathIndex = startIndex;\n\n  const /** @type {?} */ noMatch = {match: false, pathIndex: 0, commandIndex: 0};\n  while (currentPathIndex < segmentGroup.segments.length) {\n    if (currentCommandIndex >= commands.length) return noMatch;\n    const /** @type {?} */ path = segmentGroup.segments[currentPathIndex];\n    const /** @type {?} */ curr = getPath(commands[currentCommandIndex]);\n    const /** @type {?} */ next =\n        currentCommandIndex < commands.length - 1 ? commands[currentCommandIndex + 1] : null;\n\n    if (currentPathIndex > 0 && curr === undefined) break;\n\n    if (curr && next && (typeof next === 'object') && next.outlets === undefined) {\n      if (!compare(curr, next, path)) return noMatch;\n      currentCommandIndex += 2;\n    } else {\n      if (!compare(curr, {}, path)) return noMatch;\n      currentCommandIndex++;\n    }\n    currentPathIndex++;\n  }\n\n  return {match: true, pathIndex: currentPathIndex, commandIndex: currentCommandIndex};\n}\n/**\n * @param {?} segmentGroup\n * @param {?} startIndex\n * @param {?} commands\n * @return {?}\n */\nfunction createNewSegmentGroup(\n    segmentGroup: UrlSegmentGroup, startIndex: number, commands: any[]): UrlSegmentGroup {\n  const /** @type {?} */ paths = segmentGroup.segments.slice(0, startIndex);\n\n  let /** @type {?} */ i = 0;\n  while (i < commands.length) {\n    if (typeof commands[i] === 'object' && commands[i].outlets !== undefined) {\n      const /** @type {?} */ children = createNewSegmentChildren(commands[i].outlets);\n      return new UrlSegmentGroup(paths, children);\n    }\n\n    // if we start with an object literal, we need to reuse the path part from the segment\n    if (i === 0 && isMatrixParams(commands[0])) {\n      const /** @type {?} */ p = segmentGroup.segments[startIndex];\n      paths.push(new UrlSegment(p.path, commands[0]));\n      i++;\n      continue;\n    }\n\n    const /** @type {?} */ curr = getPath(commands[i]);\n    const /** @type {?} */ next = (i < commands.length - 1) ? commands[i + 1] : null;\n    if (curr && next && isMatrixParams(next)) {\n      paths.push(new UrlSegment(curr, stringify(next)));\n      i += 2;\n    } else {\n      paths.push(new UrlSegment(curr, {}));\n      i++;\n    }\n  }\n  return new UrlSegmentGroup(paths, {});\n}\n/**\n * @param {?} outlets\n * @return {?}\n */\nfunction createNewSegmentChildren(outlets: {[name: string]: any}): any {\n  const /** @type {?} */ children: {[key: string]: UrlSegmentGroup} = {};\n  forEach(outlets, (commands: any, outlet: string) => {\n    if (commands !== null) {\n      children[outlet] = createNewSegmentGroup(new UrlSegmentGroup([], {}), 0, commands);\n    }\n  });\n  return children;\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction stringify(params: {[key: string]: any}): {[key: string]: string} {\n  const /** @type {?} */ res: {[key: string]: string} = {};\n  forEach(params, (v: any, k: string) => res[k] = `${v}`);\n  return res;\n}\n/**\n * @param {?} path\n * @param {?} params\n * @param {?} segment\n * @return {?}\n */\nfunction compare(path: string, params: {[key: string]: any}, segment: UrlSegment): boolean {\n  return path == segment.path && shallowEqual(params, segment.parameters);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\n\nimport {DetachedRouteHandleInternal, RouteReuseStrategy} from './route_reuse_strategy';\nimport {ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot} from './router_state';\nimport {TreeNode} from './utils/tree';\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?} prevState\n * @return {?}\n */\nexport function createRouterState(\n    routeReuseStrategy: RouteReuseStrategy, curr: RouterStateSnapshot,\n    prevState: RouterState): RouterState {\n  const /** @type {?} */ root = createNode(routeReuseStrategy, curr._root, prevState ? prevState._root : undefined);\n  return new RouterState(root, curr);\n}\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?=} prevState\n * @return {?}\n */\nfunction createNode(\n    routeReuseStrategy: RouteReuseStrategy, curr: TreeNode<ActivatedRouteSnapshot>,\n    prevState?: TreeNode<ActivatedRoute>): TreeNode<ActivatedRoute> {\n  // reuse an activated route that is currently displayed on the screen\n  if (prevState && routeReuseStrategy.shouldReuseRoute(curr.value, prevState.value.snapshot)) {\n    const /** @type {?} */ value = prevState.value;\n    value._futureSnapshot = curr.value;\n    const /** @type {?} */ children = createOrReuseChildren(routeReuseStrategy, curr, prevState);\n    return new TreeNode<ActivatedRoute>(value, children);\n\n    // retrieve an activated route that is used to be displayed, but is not currently displayed\n  } else if (routeReuseStrategy.retrieve(curr.value)) {\n    const /** @type {?} */ tree: TreeNode<ActivatedRoute> =\n        ( /** @type {?} */((<DetachedRouteHandleInternal>routeReuseStrategy.retrieve(curr.value)))).route;\n    setFutureSnapshotsOfActivatedRoutes(curr, tree);\n    return tree;\n\n  } else {\n    const /** @type {?} */ value = createActivatedRoute(curr.value);\n    const /** @type {?} */ children = curr.children.map(c => createNode(routeReuseStrategy, c));\n    return new TreeNode<ActivatedRoute>(value, children);\n  }\n}\n/**\n * @param {?} curr\n * @param {?} result\n * @return {?}\n */\nfunction setFutureSnapshotsOfActivatedRoutes(\n    curr: TreeNode<ActivatedRouteSnapshot>, result: TreeNode<ActivatedRoute>): void {\n  if (curr.value.routeConfig !== result.value.routeConfig) {\n    throw new Error('Cannot reattach ActivatedRouteSnapshot created from a different route');\n  }\n  if (curr.children.length !== result.children.length) {\n    throw new Error('Cannot reattach ActivatedRouteSnapshot with a different number of children');\n  }\n  result.value._futureSnapshot = curr.value;\n  for (let /** @type {?} */ i = 0; i < curr.children.length; ++i) {\n    setFutureSnapshotsOfActivatedRoutes(curr.children[i], result.children[i]);\n  }\n}\n/**\n * @param {?} routeReuseStrategy\n * @param {?} curr\n * @param {?} prevState\n * @return {?}\n */\nfunction createOrReuseChildren(\n    routeReuseStrategy: RouteReuseStrategy, curr: TreeNode<ActivatedRouteSnapshot>,\n    prevState: TreeNode<ActivatedRoute>) {\n  return curr.children.map(child => {\n    for (const /** @type {?} */ p of prevState.children) {\n      if (routeReuseStrategy.shouldReuseRoute(p.value.snapshot, child.value)) {\n        return createNode(routeReuseStrategy, child, p);\n      }\n    }\n    return createNode(routeReuseStrategy, child);\n  });\n}\n/**\n * @param {?} c\n * @return {?}\n */\nfunction createActivatedRoute(c: ActivatedRouteSnapshot) {\n  return new ActivatedRoute(\n      new BehaviorSubject(c.url), new BehaviorSubject(c.params), new BehaviorSubject(c.queryParams),\n      new BehaviorSubject(c.fragment), new BehaviorSubject(c.data), c.outlet, c.component, c);\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type} from '@angular/core';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Observable} from 'rxjs/Observable';\nimport {map} from 'rxjs/operator/map';\n\nimport {Data, ResolveData, Route} from './config';\nimport {PRIMARY_OUTLET, ParamMap, Params, convertToParamMap} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlTree, equalSegments} from './url_tree';\nimport {shallowEqual, shallowEqualArrays} from './utils/collection';\nimport {Tree, TreeNode} from './utils/tree';\n/**\n * \\@whatItDoes Represents the state of the router.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const root: ActivatedRoute = state.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n * \n * \\@description \n * RouterState is a tree of activated routes. Every node in this tree knows about the \"consumed\" URL\n * segments, the extracted parameters, and the resolved data.\n * \n * See {\\@link ActivatedRoute} for more information.\n * \n * \\@stable\n */\nexport class RouterState extends Tree<ActivatedRoute> {\n/**\n * \\@internal\n * @param {?} root\n * @param {?} snapshot\n */\nconstructor(\n      root: TreeNode<ActivatedRoute>,\npublic snapshot: RouterStateSnapshot) {\n    super(root);\n    setRouterState(<RouterState>this, root);\n  }\n/**\n * @return {?}\n */\ntoString(): string { return this.snapshot.toString(); }\n}\n\nfunction RouterState_tsickle_Closure_declarations() {\n/**\n * The current snapshot of the router state\n * @type {?}\n */\nRouterState.prototype.snapshot;\n}\n\n/**\n * @param {?} urlTree\n * @param {?} rootComponent\n * @return {?}\n */\nexport function createEmptyState(urlTree: UrlTree, rootComponent: Type<any>| null): RouterState {\n  const /** @type {?} */ snapshot = createEmptyStateSnapshot(urlTree, rootComponent);\n  const /** @type {?} */ emptyUrl = new BehaviorSubject([new UrlSegment('', {})]);\n  const /** @type {?} */ emptyParams = new BehaviorSubject({});\n  const /** @type {?} */ emptyData = new BehaviorSubject({});\n  const /** @type {?} */ emptyQueryParams = new BehaviorSubject({});\n  const /** @type {?} */ fragment = new BehaviorSubject('');\n  const /** @type {?} */ activated = new ActivatedRoute(\n      emptyUrl, emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent,\n      snapshot.root);\n  activated.snapshot = snapshot.root;\n  return new RouterState(new TreeNode<ActivatedRoute>(activated, []), snapshot);\n}\n/**\n * @param {?} urlTree\n * @param {?} rootComponent\n * @return {?}\n */\nexport function createEmptyStateSnapshot(\n    urlTree: UrlTree, rootComponent: Type<any>| null): RouterStateSnapshot {\n  const /** @type {?} */ emptyParams = {};\n  const /** @type {?} */ emptyData = {};\n  const /** @type {?} */ emptyQueryParams = {};\n  const /** @type {?} */ fragment = '';\n  const /** @type {?} */ activated = new ActivatedRouteSnapshot(\n      [], emptyParams, emptyQueryParams, fragment, emptyData, PRIMARY_OUTLET, rootComponent, null,\n      urlTree.root, -1, {});\n  return new RouterStateSnapshot('', new TreeNode<ActivatedRouteSnapshot>(activated, []));\n}\n/**\n * \\@whatItDoes Contains the information about a route associated with a component loaded in an\n * outlet.\n * An `ActivatedRoute` can also be used to traverse the router state tree.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({...}) \n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: Observable<string> = route.params.map(p => p.id);\n *     const url: Observable<string> = route.url.map(segments => segments.join(''));\n *     // route.data includes both `data` and `resolve`\n *     const user = route.data.map(d => d.user);\n *   }\n * }\n * ```\n * \n * \\@stable\n */\nexport class ActivatedRoute {\n/**\n * The current snapshot of this route\n */\nsnapshot: ActivatedRouteSnapshot;\n/**\n * \\@internal\n */\n_futureSnapshot: ActivatedRouteSnapshot;\n/**\n * \\@internal\n */\n_routerState: RouterState;\n/**\n * \\@internal\n */\n_paramMap: Observable<ParamMap>;\n/**\n * \\@internal\n */\n_queryParamMap: Observable<ParamMap>;\n/**\n * \\@internal\n * @param {?} url\n * @param {?} params\n * @param {?} queryParams\n * @param {?} fragment\n * @param {?} data\n * @param {?} outlet\n * @param {?} component\n * @param {?} futureSnapshot\n */\nconstructor(\npublic url: Observable<UrlSegment[]>,\npublic params: Observable<Params>,\npublic queryParams: Observable<Params>,\npublic fragment: Observable<string>,\npublic data: Observable<Data>,\npublic outlet: string,\npublic component: Type<any>|string|null, futureSnapshot: ActivatedRouteSnapshot) {\n    this._futureSnapshot = futureSnapshot;\n  }\n/**\n * The configuration used to match this route\n * @return {?}\n */\nget routeConfig(): Route|null { return this._futureSnapshot.routeConfig; }\n/**\n * The root of the router state\n * @return {?}\n */\nget root(): ActivatedRoute { return this._routerState.root; }\n/**\n * The parent of this route in the router state tree\n * @return {?}\n */\nget parent(): ActivatedRoute|null { return this._routerState.parent(this); }\n/**\n * The first child of this route in the router state tree\n * @return {?}\n */\nget firstChild(): ActivatedRoute|null { return this._routerState.firstChild(this); }\n/**\n * The children of this route in the router state tree\n * @return {?}\n */\nget children(): ActivatedRoute[] { return this._routerState.children(this); }\n/**\n * The path from the root of the router state tree to this route\n * @return {?}\n */\nget pathFromRoot(): ActivatedRoute[] { return this._routerState.pathFromRoot(this); }\n/**\n * @return {?}\n */\nget paramMap(): Observable<ParamMap> {\n    if (!this._paramMap) {\n      this._paramMap = map.call(this.params, (p: Params): ParamMap => convertToParamMap(p));\n    }\n    return this._paramMap;\n  }\n/**\n * @return {?}\n */\nget queryParamMap(): Observable<ParamMap> {\n    if (!this._queryParamMap) {\n      this._queryParamMap =\n          map.call(this.queryParams, (p: Params): ParamMap => convertToParamMap(p));\n    }\n    return this._queryParamMap;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    return this.snapshot ? this.snapshot.toString() : `Future(${this._futureSnapshot})`;\n  }\n}\n\nfunction ActivatedRoute_tsickle_Closure_declarations() {\n/**\n * The current snapshot of this route\n * @type {?}\n */\nActivatedRoute.prototype.snapshot;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._futureSnapshot;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._routerState;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._paramMap;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRoute.prototype._queryParamMap;\n/**\n * An observable of the URL segments matched by this route\n * @type {?}\n */\nActivatedRoute.prototype.url;\n/**\n * An observable of the matrix parameters scoped to this route\n * @type {?}\n */\nActivatedRoute.prototype.params;\n/**\n * An observable of the query parameters shared by all the routes\n * @type {?}\n */\nActivatedRoute.prototype.queryParams;\n/**\n * An observable of the URL fragment shared by all the routes\n * @type {?}\n */\nActivatedRoute.prototype.fragment;\n/**\n * An observable of the static and resolved data of this route.\n * @type {?}\n */\nActivatedRoute.prototype.data;\n/**\n * The outlet name of the route. It's a constant\n * @type {?}\n */\nActivatedRoute.prototype.outlet;\n/** @type {?} */\nActivatedRoute.prototype.component;\n}\n\n\n/** @internal */\nexport type Inherited = {\n  params: Params,\n  data: Data,\n  resolve: Data,\n};\n/**\n * \\@internal\n * @param {?} route\n * @return {?}\n */\nexport function inheritedParamsDataResolve(route: ActivatedRouteSnapshot): Inherited {\n  const /** @type {?} */ pathToRoot = route.pathFromRoot;\n\n  let /** @type {?} */ inhertingStartingFrom = pathToRoot.length - 1;\n\n  while (inhertingStartingFrom >= 1) {\n    const /** @type {?} */ current = pathToRoot[inhertingStartingFrom];\n    const /** @type {?} */ parent = pathToRoot[inhertingStartingFrom - 1];\n    // current route is an empty path => inherits its parent's params and data\n    if (current.routeConfig && current.routeConfig.path === '') {\n      inhertingStartingFrom--;\n\n      // parent is componentless => current route should inherit its params and data\n    } else if (!parent.component) {\n      inhertingStartingFrom--;\n\n    } else {\n      break;\n    }\n  }\n\n  return pathToRoot.slice(inhertingStartingFrom).reduce((res, curr) => {\n    const /** @type {?} */ params = {...res.params, ...curr.params};\n    const /** @type {?} */ data = {...res.data, ...curr.data};\n    const /** @type {?} */ resolve = {...res.resolve, ...curr._resolvedData};\n    return {params, data, resolve};\n  }, /** @type {?} */(( <any>{params: {}, data: {}, resolve: {}})));\n}\n/**\n * \\@whatItDoes Contains the information about a route associated with a component loaded in an\n * outlet\n * at a particular moment in time. ActivatedRouteSnapshot can also be used to traverse the router\n * state tree.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'./my-component.html'}) \n * class MyComponent {\n *   constructor(route: ActivatedRoute) {\n *     const id: string = route.snapshot.params.id;\n *     const url: string = route.snapshot.url.join('');\n *     const user = route.snapshot.data.user;\n *   }\n * }\n * ```\n * \n * \\@stable\n */\nexport class ActivatedRouteSnapshot {\n/**\n * \\@internal *\n */\n_routeConfig: Route|null;\n/**\n * \\@internal *\n */\n_urlSegment: UrlSegmentGroup;\n/**\n * \\@internal\n */\n_lastPathIndex: number;\n/**\n * \\@internal\n */\n_resolve: ResolveData;\n/**\n * \\@internal\n */\n_resolvedData: Data;\n/**\n * \\@internal\n */\n_routerState: RouterStateSnapshot;\n/**\n * \\@internal\n */\n_paramMap: ParamMap;\n/**\n * \\@internal\n */\n_queryParamMap: ParamMap;\n/**\n * \\@internal\n * @param {?} url\n * @param {?} params\n * @param {?} queryParams\n * @param {?} fragment\n * @param {?} data\n * @param {?} outlet\n * @param {?} component\n * @param {?} routeConfig\n * @param {?} urlSegment\n * @param {?} lastPathIndex\n * @param {?} resolve\n */\nconstructor(\npublic url: UrlSegment[],\npublic params: Params,\npublic queryParams: Params,\npublic fragment: string,\npublic data: Data,\npublic outlet: string,\npublic component: Type<any>|string|null, routeConfig: Route|null, urlSegment: UrlSegmentGroup,\n      lastPathIndex: number, resolve: ResolveData) {\n    this._routeConfig = routeConfig;\n    this._urlSegment = urlSegment;\n    this._lastPathIndex = lastPathIndex;\n    this._resolve = resolve;\n  }\n/**\n * The configuration used to match this route\n * @return {?}\n */\nget routeConfig(): Route|null { return this._routeConfig; }\n/**\n * The root of the router state\n * @return {?}\n */\nget root(): ActivatedRouteSnapshot { return this._routerState.root; }\n/**\n * The parent of this route in the router state tree\n * @return {?}\n */\nget parent(): ActivatedRouteSnapshot|null { return this._routerState.parent(this); }\n/**\n * The first child of this route in the router state tree\n * @return {?}\n */\nget firstChild(): ActivatedRouteSnapshot|null { return this._routerState.firstChild(this); }\n/**\n * The children of this route in the router state tree\n * @return {?}\n */\nget children(): ActivatedRouteSnapshot[] { return this._routerState.children(this); }\n/**\n * The path from the root of the router state tree to this route\n * @return {?}\n */\nget pathFromRoot(): ActivatedRouteSnapshot[] { return this._routerState.pathFromRoot(this); }\n/**\n * @return {?}\n */\nget paramMap(): ParamMap {\n    if (!this._paramMap) {\n      this._paramMap = convertToParamMap(this.params);\n    }\n    return this._paramMap;\n  }\n/**\n * @return {?}\n */\nget queryParamMap(): ParamMap {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n    return this._queryParamMap;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    const /** @type {?} */ url = this.url.map(segment => segment.toString()).join('/');\n    const /** @type {?} */ matched = this._routeConfig ? this._routeConfig.path : '';\n    return `Route(url:'${url}', path:'${matched}')`;\n  }\n}\n\nfunction ActivatedRouteSnapshot_tsickle_Closure_declarations() {\n/**\n * \\@internal *\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._routeConfig;\n/**\n * \\@internal *\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._urlSegment;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._lastPathIndex;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._resolve;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._resolvedData;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._routerState;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._paramMap;\n/**\n * \\@internal\n * @type {?}\n */\nActivatedRouteSnapshot.prototype._queryParamMap;\n/**\n * The URL segments matched by this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.url;\n/**\n * The matrix parameters scoped to this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.params;\n/**\n * The query parameters shared by all the routes\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.queryParams;\n/**\n * The URL fragment shared by all the routes\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.fragment;\n/**\n * The static and resolved data of this route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.data;\n/**\n * The outlet name of the route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.outlet;\n/**\n * The component of the route\n * @type {?}\n */\nActivatedRouteSnapshot.prototype.component;\n}\n\n/**\n * \\@whatItDoes Represents the state of the router at a moment in time.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const state: RouterState = router.routerState;\n *     const snapshot: RouterStateSnapshot = state.snapshot;\n *     const root: ActivatedRouteSnapshot = snapshot.root;\n *     const child = root.firstChild;\n *     const id: Observable<string> = child.params.map(p => p.id);\n *     //...\n *   }\n * }\n * ```\n * \n * \\@description \n * RouterStateSnapshot is a tree of activated route snapshots. Every node in this tree knows about\n * the \"consumed\" URL segments, the extracted parameters, and the resolved data.\n * \n * \\@stable\n */\nexport class RouterStateSnapshot extends Tree<ActivatedRouteSnapshot> {\n/**\n * \\@internal\n * @param {?} url\n * @param {?} root\n */\nconstructor(\npublic url: string, root: TreeNode<ActivatedRouteSnapshot>) {\n    super(root);\n    setRouterState(<RouterStateSnapshot>this, root);\n  }\n/**\n * @return {?}\n */\ntoString(): string { return serializeNode(this._root); }\n}\n\nfunction RouterStateSnapshot_tsickle_Closure_declarations() {\n/**\n * The url from which this snapshot was created\n * @type {?}\n */\nRouterStateSnapshot.prototype.url;\n}\n\n/**\n * @template U, T\n * @param {?} state\n * @param {?} node\n * @return {?}\n */\nfunction setRouterState<U, T extends{_routerState: U}>(state: U, node: TreeNode<T>): void {\n  node.value._routerState = state;\n  node.children.forEach(c => setRouterState(state, c));\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction serializeNode(node: TreeNode<ActivatedRouteSnapshot>): string {\n  const /** @type {?} */ c = node.children.length > 0 ? ` { ${node.children.map(serializeNode).join(\", \")} } ` : '';\n  return `${node.value}${c}`;\n}\n/**\n * The expectation is that the activate route is created with the right set of parameters.\n * So we push new values into the observables only when they are not the initial values.\n * And we detect that by checking if the snapshot field is set.\n * @param {?} route\n * @return {?}\n */\nexport function advanceActivatedRoute(route: ActivatedRoute): void {\n  if (route.snapshot) {\n    const /** @type {?} */ currentSnapshot = route.snapshot;\n    const /** @type {?} */ nextSnapshot = route._futureSnapshot;\n    route.snapshot = nextSnapshot;\n    if (!shallowEqual(currentSnapshot.queryParams, nextSnapshot.queryParams)) {\n      ( /** @type {?} */((<any>route.queryParams))).next(nextSnapshot.queryParams);\n    }\n    if (currentSnapshot.fragment !== nextSnapshot.fragment) {\n      ( /** @type {?} */((<any>route.fragment))).next(nextSnapshot.fragment);\n    }\n    if (!shallowEqual(currentSnapshot.params, nextSnapshot.params)) {\n      ( /** @type {?} */((<any>route.params))).next(nextSnapshot.params);\n    }\n    if (!shallowEqualArrays(currentSnapshot.url, nextSnapshot.url)) {\n      ( /** @type {?} */((<any>route.url))).next(nextSnapshot.url);\n    }\n    if (!shallowEqual(currentSnapshot.data, nextSnapshot.data)) {\n      ( /** @type {?} */((<any>route.data))).next(nextSnapshot.data);\n    }\n  } else {\n    route.snapshot = route._futureSnapshot;\n\n    // this is for resolved data\n    ( /** @type {?} */((<any>route.data))).next(route._futureSnapshot.data);\n  }\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function equalParamsAndUrlSegments(\n    a: ActivatedRouteSnapshot, b: ActivatedRouteSnapshot): boolean {\n  const /** @type {?} */ equalUrlParams = shallowEqual(a.params, b.params) && equalSegments(a.url, b.url);\n  const /** @type {?} */ parentsMismatch = !a.parent !== !b.parent;\n\n  return equalUrlParams && !parentsMismatch &&\n      (!a.parent || equalParamsAndUrlSegments(a.parent, /** @type {?} */(( b.parent))));\n}", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport class Tree<T> {\n/**\n * \\@internal\n */\n_root: TreeNode<T>;\n/**\n * @param {?} root\n */\nconstructor(root: TreeNode<T>) { this._root = root; }\n/**\n * @return {?}\n */\nget root(): T { return this._root.value; }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nparent(t: T): T|null {\n    const /** @type {?} */ p = this.pathFromRoot(t);\n    return p.length > 1 ? p[p.length - 2] : null;\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nchildren(t: T): T[] {\n    const /** @type {?} */ n = findNode(t, this._root);\n    return n ? n.children.map(t => t.value) : [];\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nfirstChild(t: T): T|null {\n    const /** @type {?} */ n = findNode(t, this._root);\n    return n && n.children.length > 0 ? n.children[0].value : null;\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\nsiblings(t: T): T[] {\n    const /** @type {?} */ p = findPath(t, this._root);\n    if (p.length < 2) return [];\n\n    const /** @type {?} */ c = p[p.length - 2].children.map(c => c.value);\n    return c.filter(cc => cc !== t);\n  }\n/**\n * \\@internal\n * @param {?} t\n * @return {?}\n */\npathFromRoot(t: T): T[] { return findPath(t, this._root).map(s => s.value); }\n}\n\nfunction Tree_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nTree.prototype._root;\n}\n\n/**\n * @template T\n * @param {?} value\n * @param {?} node\n * @return {?}\n */\nfunction findNode<T>(value: T, node: TreeNode<T>): TreeNode<T>|null {\n  if (value === node.value) return node;\n\n  for (const /** @type {?} */ child of node.children) {\n    const /** @type {?} */ node = findNode(value, child);\n    if (node) return node;\n  }\n\n  return null;\n}\n/**\n * @template T\n * @param {?} value\n * @param {?} node\n * @return {?}\n */\nfunction findPath<T>(value: T, node: TreeNode<T>): TreeNode<T>[] {\n  if (value === node.value) return [node];\n\n  for (const /** @type {?} */ child of node.children) {\n    const /** @type {?} */ path = findPath(value, child);\n    if (path.length) {\n      path.unshift(node);\n      return path;\n    }\n  }\n\n  return [];\n}\nexport class TreeNode<T> {\n/**\n * @param {?} value\n * @param {?} children\n */\nconstructor(public value: T,\npublic children: TreeNode<T>[]) {}\n/**\n * @return {?}\n */\ntoString(): string { return `TreeNode(${this.value})`; }\n}\n\nfunction TreeNode_tsickle_Closure_declarations() {\n/** @type {?} */\nTreeNode.prototype.value;\n/** @type {?} */\nTreeNode.prototype.children;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injector, NgModuleRef} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {from} from 'rxjs/observable/from';\nimport {of } from 'rxjs/observable/of';\nimport {_catch} from 'rxjs/operator/catch';\nimport {concatAll} from 'rxjs/operator/concatAll';\nimport {first} from 'rxjs/operator/first';\nimport {map} from 'rxjs/operator/map';\nimport {mergeMap} from 'rxjs/operator/mergeMap';\nimport {EmptyError} from 'rxjs/util/EmptyError';\n\nimport {LoadedRouterConfig, Route, Routes} from './config';\nimport {RouterConfigLoader} from './router_config_loader';\nimport {PRIMARY_OUTLET, Params, defaultUrlMatcher, navigationCancelingError} from './shared';\nimport {UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree} from './url_tree';\nimport {andObservables, forEach, waitForMap, wrapIntoObservable} from './utils/collection';\nclass NoMatch {\npublic segmentGroup: UrlSegmentGroup|null;\n/**\n * @param {?=} segmentGroup\n */\nconstructor(segmentGroup?: UrlSegmentGroup) { this.segmentGroup = segmentGroup || null; }\n}\n\nfunction NoMatch_tsickle_Closure_declarations() {\n/** @type {?} */\nNoMatch.prototype.segmentGroup;\n}\n\nclass AbsoluteRedirect {\n/**\n * @param {?} urlTree\n */\nconstructor(public urlTree: UrlTree) {}\n}\n\nfunction AbsoluteRedirect_tsickle_Closure_declarations() {\n/** @type {?} */\nAbsoluteRedirect.prototype.urlTree;\n}\n\n/**\n * @param {?} segmentGroup\n * @return {?}\n */\nfunction noMatch(segmentGroup: UrlSegmentGroup): Observable<UrlSegmentGroup> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new NoMatch(segmentGroup)));\n}\n/**\n * @param {?} newTree\n * @return {?}\n */\nfunction absoluteRedirect(newTree: UrlTree): Observable<any> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new AbsoluteRedirect(newTree)));\n}\n/**\n * @param {?} redirectTo\n * @return {?}\n */\nfunction namedOutletsRedirect(redirectTo: string): Observable<any> {\n  return new Observable<UrlSegmentGroup>(\n      (obs: Observer<UrlSegmentGroup>) => obs.error(new Error(\n          `Only absolute redirects can have named outlets. redirectTo: '${redirectTo}'`)));\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction canLoadFails(route: Route): Observable<LoadedRouterConfig> {\n  return new Observable<LoadedRouterConfig>(\n      (obs: Observer<LoadedRouterConfig>) => obs.error(navigationCancelingError(\n          `Cannot load children because the guard of the route \"path: '${route.path}'\" returned false`)));\n}\n/**\n * Returns the `UrlTree` with the redirection applied.\n * \n * Lazy modules are loaded along the way.\n * @param {?} moduleInjector\n * @param {?} configLoader\n * @param {?} urlSerializer\n * @param {?} urlTree\n * @param {?} config\n * @return {?}\n */\nexport function applyRedirects(\n    moduleInjector: Injector, configLoader: RouterConfigLoader, urlSerializer: UrlSerializer,\n    urlTree: UrlTree, config: Routes): Observable<UrlTree> {\n  return new ApplyRedirects(moduleInjector, configLoader, urlSerializer, urlTree, config).apply();\n}\nclass ApplyRedirects {\nprivate allowRedirects: boolean = true;\nprivate ngModule: NgModuleRef<any>;\n/**\n * @param {?} moduleInjector\n * @param {?} configLoader\n * @param {?} urlSerializer\n * @param {?} urlTree\n * @param {?} config\n */\nconstructor(\n      moduleInjector: Injector,\nprivate configLoader: RouterConfigLoader,\nprivate urlSerializer: UrlSerializer,\nprivate urlTree: UrlTree,\nprivate config: Routes) {\n    this.ngModule = moduleInjector.get(NgModuleRef);\n  }\n/**\n * @return {?}\n */\napply(): Observable<UrlTree> {\n    const /** @type {?} */ expanded$ =\n        this.expandSegmentGroup(this.ngModule, this.config, this.urlTree.root, PRIMARY_OUTLET);\n    const /** @type {?} */ urlTrees$ = map.call(\n        expanded$, (rootSegmentGroup: UrlSegmentGroup) => this.createUrlTree(\n                       rootSegmentGroup, this.urlTree.queryParams, /** @type {?} */(( this.urlTree.fragment))));\n    return _catch.call(urlTrees$, (e: any) => {\n      if (e instanceof AbsoluteRedirect) {\n        // after an absolute redirect we do not apply any more redirects!\n        this.allowRedirects = false;\n        // we need to run matching, so we can fetch all lazy-loaded modules\n        return this.match(e.urlTree);\n      }\n\n      if (e instanceof NoMatch) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} tree\n * @return {?}\n */\nprivate match(tree: UrlTree): Observable<UrlTree> {\n    const /** @type {?} */ expanded$ =\n        this.expandSegmentGroup(this.ngModule, this.config, tree.root, PRIMARY_OUTLET);\n    const /** @type {?} */ mapped$ = map.call(\n        expanded$, (rootSegmentGroup: UrlSegmentGroup) =>\n                       this.createUrlTree(rootSegmentGroup, tree.queryParams, /** @type {?} */(( tree.fragment))));\n    return _catch.call(mapped$, (e: any): Observable<UrlTree> => {\n      if (e instanceof NoMatch) {\n        throw this.noMatchError(e);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} e\n * @return {?}\n */\nprivate noMatchError(e: NoMatch): any {\n    return new Error(`Cannot match any routes. URL Segment: '${e.segmentGroup}'`);\n  }\n/**\n * @param {?} rootCandidate\n * @param {?} queryParams\n * @param {?} fragment\n * @return {?}\n */\nprivate createUrlTree(rootCandidate: UrlSegmentGroup, queryParams: Params, fragment: string):\n      UrlTree {\n    const /** @type {?} */ root = rootCandidate.segments.length > 0 ?\n        new UrlSegmentGroup([], {[PRIMARY_OUTLET]: rootCandidate}) :\n        rootCandidate;\n    return new UrlTree(root, queryParams, fragment);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} segmentGroup\n * @param {?} outlet\n * @return {?}\n */\nprivate expandSegmentGroup(\n      ngModule: NgModuleRef<any>, routes: Route[], segmentGroup: UrlSegmentGroup,\n      outlet: string): Observable<UrlSegmentGroup> {\n    if (segmentGroup.segments.length === 0 && segmentGroup.hasChildren()) {\n      return map.call(\n          this.expandChildren(ngModule, routes, segmentGroup),\n          (children: any) => new UrlSegmentGroup([], children));\n    }\n\n    return this.expandSegment(ngModule, segmentGroup, routes, segmentGroup.segments, outlet, true);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} segmentGroup\n * @return {?}\n */\nprivate expandChildren(\n      ngModule: NgModuleRef<any>, routes: Route[],\n      segmentGroup: UrlSegmentGroup): Observable<{[name: string]: UrlSegmentGroup}> {\n    return waitForMap(\n        segmentGroup.children,\n        (childOutlet, child) => this.expandSegmentGroup(ngModule, routes, child, childOutlet));\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} segments\n * @param {?} outlet\n * @param {?} allowRedirects\n * @return {?}\n */\nprivate expandSegment(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[],\n      segments: UrlSegment[], outlet: string,\n      allowRedirects: boolean): Observable<UrlSegmentGroup> {\n    const /** @type {?} */ routes$ = of (...routes);\n    const /** @type {?} */ processedRoutes$ = map.call(routes$, (r: any) => {\n      const /** @type {?} */ expanded$ = this.expandSegmentAgainstRoute(\n          ngModule, segmentGroup, routes, r, segments, outlet, allowRedirects);\n      return _catch.call(expanded$, (e: any) => {\n        if (e instanceof NoMatch) {\n          return of (null);\n        }\n\n        throw e;\n      });\n    });\n    const /** @type {?} */ concattedProcessedRoutes$ = concatAll.call(processedRoutes$);\n    const /** @type {?} */ first$ = first.call(concattedProcessedRoutes$, (s: any) => !!s);\n    return _catch.call(first$, (e: any, _: any): Observable<UrlSegmentGroup> => {\n      if (e instanceof EmptyError) {\n        if (this.noLeftoversInUrl(segmentGroup, segments, outlet)) {\n          return of (new UrlSegmentGroup([], {}));\n        }\n\n        throw new NoMatch(segmentGroup);\n      }\n\n      throw e;\n    });\n  }\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate noLeftoversInUrl(segmentGroup: UrlSegmentGroup, segments: UrlSegment[], outlet: string):\n      boolean {\n    return segments.length === 0 && !segmentGroup.children[outlet];\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} paths\n * @param {?} outlet\n * @param {?} allowRedirects\n * @return {?}\n */\nprivate expandSegmentAgainstRoute(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      paths: UrlSegment[], outlet: string, allowRedirects: boolean): Observable<UrlSegmentGroup> {\n    if (getOutlet(route) !== outlet) {\n      return noMatch(segmentGroup);\n    }\n\n    if (route.redirectTo === undefined) {\n      return this.matchSegmentAgainstRoute(ngModule, segmentGroup, route, paths);\n    }\n\n    if (allowRedirects && this.allowRedirects) {\n      return this.expandSegmentAgainstRouteUsingRedirect(\n          ngModule, segmentGroup, routes, route, paths, outlet);\n    }\n\n    return noMatch(segmentGroup);\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate expandSegmentAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      segments: UrlSegment[], outlet: string): Observable<UrlSegmentGroup> {\n    if (route.path === '**') {\n      return this.expandWildCardWithParamsAgainstRouteUsingRedirect(\n          ngModule, routes, route, outlet);\n    }\n\n    return this.expandRegularSegmentAgainstRouteUsingRedirect(\n        ngModule, segmentGroup, routes, route, segments, outlet);\n  }\n/**\n * @param {?} ngModule\n * @param {?} routes\n * @param {?} route\n * @param {?} outlet\n * @return {?}\n */\nprivate expandWildCardWithParamsAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, routes: Route[], route: Route,\n      outlet: string): Observable<UrlSegmentGroup> {\n    const /** @type {?} */ newTree = this.applyRedirectCommands([], /** @type {?} */(( route.redirectTo)), {});\n    if ( /** @type {?} */((route.redirectTo)).startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return mergeMap.call(this.lineralizeSegments(route, newTree), (newSegments: UrlSegment[]) => {\n      const /** @type {?} */ group = new UrlSegmentGroup(newSegments, {});\n      return this.expandSegment(ngModule, group, routes, newSegments, outlet, false);\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} segmentGroup\n * @param {?} routes\n * @param {?} route\n * @param {?} segments\n * @param {?} outlet\n * @return {?}\n */\nprivate expandRegularSegmentAgainstRouteUsingRedirect(\n      ngModule: NgModuleRef<any>, segmentGroup: UrlSegmentGroup, routes: Route[], route: Route,\n      segments: UrlSegment[], outlet: string): Observable<UrlSegmentGroup> {\n    const {matched, consumedSegments, lastChild, positionalParamSegments} =\n        match(segmentGroup, route, segments);\n    if (!matched) return noMatch(segmentGroup);\n\n    const /** @type {?} */ newTree = this.applyRedirectCommands(\n        consumedSegments, /** @type {?} */(( route.redirectTo)), /** @type {?} */(( <any>positionalParamSegments)));\n    if ( /** @type {?} */((route.redirectTo)).startsWith('/')) {\n      return absoluteRedirect(newTree);\n    }\n\n    return mergeMap.call(this.lineralizeSegments(route, newTree), (newSegments: UrlSegment[]) => {\n      return this.expandSegment(\n          ngModule, segmentGroup, routes, newSegments.concat(segments.slice(lastChild)), outlet,\n          false);\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} rawSegmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nprivate matchSegmentAgainstRoute(\n      ngModule: NgModuleRef<any>, rawSegmentGroup: UrlSegmentGroup, route: Route,\n      segments: UrlSegment[]): Observable<UrlSegmentGroup> {\n    if (route.path === '**') {\n      if (route.loadChildren) {\n        return map.call(\n            this.configLoader.load(ngModule.injector, route), (cfg: LoadedRouterConfig) => {\n              route._loadedConfig = cfg;\n              return new UrlSegmentGroup(segments, {});\n            });\n      }\n\n      return of (new UrlSegmentGroup(segments, {}));\n    }\n\n    const {matched, consumedSegments, lastChild} = match(rawSegmentGroup, route, segments);\n    if (!matched) return noMatch(rawSegmentGroup);\n\n    const /** @type {?} */ rawSlicedSegments = segments.slice(lastChild);\n    const /** @type {?} */ childConfig$ = this.getChildConfig(ngModule, route);\n\n    return mergeMap.call(childConfig$, (routerConfig: LoadedRouterConfig) => {\n      const /** @type {?} */ childModule = routerConfig.module;\n      const /** @type {?} */ childConfig = routerConfig.routes;\n\n      const {segmentGroup, slicedSegments} =\n          split(rawSegmentGroup, consumedSegments, rawSlicedSegments, childConfig);\n\n      if (slicedSegments.length === 0 && segmentGroup.hasChildren()) {\n        const /** @type {?} */ expanded$ = this.expandChildren(childModule, childConfig, segmentGroup);\n        return map.call(\n            expanded$, (children: any) => new UrlSegmentGroup(consumedSegments, children));\n      }\n\n      if (childConfig.length === 0 && slicedSegments.length === 0) {\n        return of (new UrlSegmentGroup(consumedSegments, {}));\n      }\n\n      const /** @type {?} */ expanded$ = this.expandSegment(\n          childModule, segmentGroup, childConfig, slicedSegments, PRIMARY_OUTLET, true);\n      return map.call(\n          expanded$, (cs: UrlSegmentGroup) =>\n                         new UrlSegmentGroup(consumedSegments.concat(cs.segments), cs.children));\n    });\n  }\n/**\n * @param {?} ngModule\n * @param {?} route\n * @return {?}\n */\nprivate getChildConfig(ngModule: NgModuleRef<any>, route: Route): Observable<LoadedRouterConfig> {\n    if (route.children) {\n      // The children belong to the same module\n      return of (new LoadedRouterConfig(route.children, ngModule));\n    }\n\n    if (route.loadChildren) {\n      // lazy children belong to the loaded module\n      if (route._loadedConfig !== undefined) {\n        return of (route._loadedConfig);\n      }\n\n      return mergeMap.call(runCanLoadGuard(ngModule.injector, route), (shouldLoad: boolean) => {\n\n        if (shouldLoad) {\n          return map.call(\n              this.configLoader.load(ngModule.injector, route), (cfg: LoadedRouterConfig) => {\n                route._loadedConfig = cfg;\n                return cfg;\n              });\n        }\n\n        return canLoadFails(route);\n      });\n    }\n\n    return of (new LoadedRouterConfig([], ngModule));\n  }\n/**\n * @param {?} route\n * @param {?} urlTree\n * @return {?}\n */\nprivate lineralizeSegments(route: Route, urlTree: UrlTree): Observable<UrlSegment[]> {\n    let /** @type {?} */ res: UrlSegment[] = [];\n    let /** @type {?} */ c = urlTree.root;\n    while (true) {\n      res = res.concat(c.segments);\n      if (c.numberOfChildren === 0) {\n        return of (res);\n      }\n\n      if (c.numberOfChildren > 1 || !c.children[PRIMARY_OUTLET]) {\n        return namedOutletsRedirect( /** @type {?} */((route.redirectTo)));\n      }\n\n      c = c.children[PRIMARY_OUTLET];\n    }\n  }\n/**\n * @param {?} segments\n * @param {?} redirectTo\n * @param {?} posParams\n * @return {?}\n */\nprivate applyRedirectCommands(\n      segments: UrlSegment[], redirectTo: string, posParams: {[k: string]: UrlSegment}): UrlTree {\n    return this.applyRedirectCreatreUrlTree(\n        redirectTo, this.urlSerializer.parse(redirectTo), segments, posParams);\n  }\n/**\n * @param {?} redirectTo\n * @param {?} urlTree\n * @param {?} segments\n * @param {?} posParams\n * @return {?}\n */\nprivate applyRedirectCreatreUrlTree(\n      redirectTo: string, urlTree: UrlTree, segments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlTree {\n    const /** @type {?} */ newRoot = this.createSegmentGroup(redirectTo, urlTree.root, segments, posParams);\n    return new UrlTree(\n        newRoot, this.createQueryParams(urlTree.queryParams, this.urlTree.queryParams),\n        urlTree.fragment);\n  }\n/**\n * @param {?} redirectToParams\n * @param {?} actualParams\n * @return {?}\n */\nprivate createQueryParams(redirectToParams: Params, actualParams: Params): Params {\n    const /** @type {?} */ res: Params = {};\n    forEach(redirectToParams, (v: any, k: string) => {\n      const /** @type {?} */ copySourceValue = typeof v === 'string' && v.startsWith(':');\n      if (copySourceValue) {\n        const /** @type {?} */ sourceName = v.substring(1);\n        res[k] = actualParams[sourceName];\n      } else {\n        res[k] = v;\n      }\n    });\n    return res;\n  }\n/**\n * @param {?} redirectTo\n * @param {?} group\n * @param {?} segments\n * @param {?} posParams\n * @return {?}\n */\nprivate createSegmentGroup(\n      redirectTo: string, group: UrlSegmentGroup, segments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlSegmentGroup {\n    const /** @type {?} */ updatedSegments = this.createSegments(redirectTo, group.segments, segments, posParams);\n\n    let /** @type {?} */ children: {[n: string]: UrlSegmentGroup} = {};\n    forEach(group.children, (child: UrlSegmentGroup, name: string) => {\n      children[name] = this.createSegmentGroup(redirectTo, child, segments, posParams);\n    });\n\n    return new UrlSegmentGroup(updatedSegments, children);\n  }\n/**\n * @param {?} redirectTo\n * @param {?} redirectToSegments\n * @param {?} actualSegments\n * @param {?} posParams\n * @return {?}\n */\nprivate createSegments(\n      redirectTo: string, redirectToSegments: UrlSegment[], actualSegments: UrlSegment[],\n      posParams: {[k: string]: UrlSegment}): UrlSegment[] {\n    return redirectToSegments.map(\n        s => s.path.startsWith(':') ? this.findPosParam(redirectTo, s, posParams) :\n                                      this.findOrReturn(s, actualSegments));\n  }\n/**\n * @param {?} redirectTo\n * @param {?} redirectToUrlSegment\n * @param {?} posParams\n * @return {?}\n */\nprivate findPosParam(\n      redirectTo: string, redirectToUrlSegment: UrlSegment,\n      posParams: {[k: string]: UrlSegment}): UrlSegment {\n    const /** @type {?} */ pos = posParams[redirectToUrlSegment.path.substring(1)];\n    if (!pos)\n      throw new Error(\n          `Cannot redirect to '${redirectTo}'. Cannot find '${redirectToUrlSegment.path}'.`);\n    return pos;\n  }\n/**\n * @param {?} redirectToUrlSegment\n * @param {?} actualSegments\n * @return {?}\n */\nprivate findOrReturn(redirectToUrlSegment: UrlSegment, actualSegments: UrlSegment[]): UrlSegment {\n    let /** @type {?} */ idx = 0;\n    for (const /** @type {?} */ s of actualSegments) {\n      if (s.path === redirectToUrlSegment.path) {\n        actualSegments.splice(idx);\n        return s;\n      }\n      idx++;\n    }\n    return redirectToUrlSegment;\n  }\n}\n\nfunction ApplyRedirects_tsickle_Closure_declarations() {\n/** @type {?} */\nApplyRedirects.prototype.allowRedirects;\n/** @type {?} */\nApplyRedirects.prototype.ngModule;\n/** @type {?} */\nApplyRedirects.prototype.configLoader;\n/** @type {?} */\nApplyRedirects.prototype.urlSerializer;\n/** @type {?} */\nApplyRedirects.prototype.urlTree;\n/** @type {?} */\nApplyRedirects.prototype.config;\n}\n\n/**\n * @param {?} moduleInjector\n * @param {?} route\n * @return {?}\n */\nfunction runCanLoadGuard(moduleInjector: Injector, route: Route): Observable<boolean> {\n  const /** @type {?} */ canLoad = route.canLoad;\n  if (!canLoad || canLoad.length === 0) return of (true);\n\n  const /** @type {?} */ obs = map.call(from(canLoad), (injectionToken: any) => {\n    const /** @type {?} */ guard = moduleInjector.get(injectionToken);\n    return wrapIntoObservable(guard.canLoad ? guard.canLoad(route) : guard(route));\n  });\n\n  return andObservables(obs);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} route\n * @param {?} segments\n * @return {?}\n */\nfunction match(segmentGroup: UrlSegmentGroup, route: Route, segments: UrlSegment[]): {\n  matched: boolean,\n  consumedSegments: UrlSegment[],\n  lastChild: number,\n  positionalParamSegments: {[k: string]: UrlSegment}\n} {\n  if (route.path === '') {\n    if ((route.pathMatch === 'full') && (segmentGroup.hasChildren() || segments.length > 0)) {\n      return {matched: false, consumedSegments: [], lastChild: 0, positionalParamSegments: {}};\n    }\n\n    return {matched: true, consumedSegments: [], lastChild: 0, positionalParamSegments: {}};\n  }\n\n  const /** @type {?} */ matcher = route.matcher || defaultUrlMatcher;\n  const /** @type {?} */ res = matcher(segments, segmentGroup, route);\n\n  if (!res) {\n    return {\n      matched: false, consumedSegments: /** @type {?} */(( <any[]>[])), lastChild: 0, positionalParamSegments: {},\n    }\n  }\n\n  return {\n    matched: true,\n    consumedSegments: /** @type {?} */(( res.consumed)),\n    lastChild: /** @type {?} */(( res.consumed.length)),\n    positionalParamSegments: /** @type {?} */(( res.posParams)),\n  };\n}\n/**\n * @param {?} segmentGroup\n * @param {?} consumedSegments\n * @param {?} slicedSegments\n * @param {?} config\n * @return {?}\n */\nfunction split(\n    segmentGroup: UrlSegmentGroup, consumedSegments: UrlSegment[], slicedSegments: UrlSegment[],\n    config: Route[]) {\n  if (slicedSegments.length > 0 &&\n      containsEmptyPathRedirectsWithNamedOutlets(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        consumedSegments, createChildrenForEmptySegments(\n                              config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));\n    return {segmentGroup: mergeTrivialChildren(s), slicedSegments: []};\n  }\n\n  if (slicedSegments.length === 0 &&\n      containsEmptyPathRedirects(segmentGroup, slicedSegments, config)) {\n    const /** @type {?} */ s = new UrlSegmentGroup(\n        segmentGroup.segments, addEmptySegmentsToChildrenIfNeeded(\n                                   segmentGroup, slicedSegments, config, segmentGroup.children));\n    return {segmentGroup: mergeTrivialChildren(s), slicedSegments};\n  }\n\n  return {segmentGroup, slicedSegments};\n}\n/**\n * @param {?} s\n * @return {?}\n */\nfunction mergeTrivialChildren(s: UrlSegmentGroup): UrlSegmentGroup {\n  if (s.numberOfChildren === 1 && s.children[PRIMARY_OUTLET]) {\n    const /** @type {?} */ c = s.children[PRIMARY_OUTLET];\n    return new UrlSegmentGroup(s.segments.concat(c.segments), c.children);\n  }\n\n  return s;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} slicedSegments\n * @param {?} routes\n * @param {?} children\n * @return {?}\n */\nfunction addEmptySegmentsToChildrenIfNeeded(\n    segmentGroup: UrlSegmentGroup, slicedSegments: UrlSegment[], routes: Route[],\n    children: {[name: string]: UrlSegmentGroup}): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  for (const /** @type {?} */ r of routes) {\n    if (isEmptyPathRedirect(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {\n      res[getOutlet(r)] = new UrlSegmentGroup([], {});\n    }\n  }\n  return {...children, ...res};\n}\n/**\n * @param {?} routes\n * @param {?} primarySegmentGroup\n * @return {?}\n */\nfunction createChildrenForEmptySegments(\n    routes: Route[], primarySegmentGroup: UrlSegmentGroup): {[name: string]: UrlSegmentGroup} {\n  const /** @type {?} */ res: {[name: string]: UrlSegmentGroup} = {};\n  res[PRIMARY_OUTLET] = primarySegmentGroup;\n  for (const /** @type {?} */ r of routes) {\n    if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {\n      res[getOutlet(r)] = new UrlSegmentGroup([], {});\n    }\n  }\n  return res;\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathRedirectsWithNamedOutlets(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(\n      r => isEmptyPathRedirect(segmentGroup, segments, r) && getOutlet(r) !== PRIMARY_OUTLET);\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} routes\n * @return {?}\n */\nfunction containsEmptyPathRedirects(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], routes: Route[]): boolean {\n  return routes.some(r => isEmptyPathRedirect(segmentGroup, segments, r));\n}\n/**\n * @param {?} segmentGroup\n * @param {?} segments\n * @param {?} r\n * @return {?}\n */\nfunction isEmptyPathRedirect(\n    segmentGroup: UrlSegmentGroup, segments: UrlSegment[], r: Route): boolean {\n  if ((segmentGroup.hasChildren() || segments.length > 0) && r.pathMatch === 'full') {\n    return false;\n  }\n\n  return r.path === '' && r.redirectTo !== undefined;\n}\n/**\n * @param {?} route\n * @return {?}\n */\nfunction getOutlet(route: Route): string {\n  return route.outlet || PRIMARY_OUTLET;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {PRIMARY_OUTLET, ParamMap, convertToParamMap} from './shared';\nimport {forEach, shallowEqual} from './utils/collection';\n/**\n * @return {?}\n */\nexport function createEmptyUrlTree() {\n  return new UrlTree(new UrlSegmentGroup([], {}), {}, null);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @param {?} exact\n * @return {?}\n */\nexport function containsTree(container: UrlTree, containee: UrlTree, exact: boolean): boolean {\n  if (exact) {\n    return equalQueryParams(container.queryParams, containee.queryParams) &&\n        equalSegmentGroups(container.root, containee.root);\n  }\n\n  return containsQueryParams(container.queryParams, containee.queryParams) &&\n      containsSegmentGroup(container.root, containee.root);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction equalQueryParams(\n    container: {[k: string]: string}, containee: {[k: string]: string}): boolean {\n  return shallowEqual(container, containee);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction equalSegmentGroups(container: UrlSegmentGroup, containee: UrlSegmentGroup): boolean {\n  if (!equalPath(container.segments, containee.segments)) return false;\n  if (container.numberOfChildren !== containee.numberOfChildren) return false;\n  for (const /** @type {?} */ c in containee.children) {\n    if (!container.children[c]) return false;\n    if (!equalSegmentGroups(container.children[c], containee.children[c])) return false;\n  }\n  return true;\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction containsQueryParams(\n    container: {[k: string]: string}, containee: {[k: string]: string}): boolean {\n  return Object.keys(containee).length <= Object.keys(container).length &&\n      Object.keys(containee).every(key => containee[key] === container[key]);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @return {?}\n */\nfunction containsSegmentGroup(container: UrlSegmentGroup, containee: UrlSegmentGroup): boolean {\n  return containsSegmentGroupHelper(container, containee, containee.segments);\n}\n/**\n * @param {?} container\n * @param {?} containee\n * @param {?} containeePaths\n * @return {?}\n */\nfunction containsSegmentGroupHelper(\n    container: UrlSegmentGroup, containee: UrlSegmentGroup, containeePaths: UrlSegment[]): boolean {\n  if (container.segments.length > containeePaths.length) {\n    const /** @type {?} */ current = container.segments.slice(0, containeePaths.length);\n    if (!equalPath(current, containeePaths)) return false;\n    if (containee.hasChildren()) return false;\n    return true;\n\n  } else if (container.segments.length === containeePaths.length) {\n    if (!equalPath(container.segments, containeePaths)) return false;\n    for (const /** @type {?} */ c in containee.children) {\n      if (!container.children[c]) return false;\n      if (!containsSegmentGroup(container.children[c], containee.children[c])) return false;\n    }\n    return true;\n\n  } else {\n    const /** @type {?} */ current = containeePaths.slice(0, container.segments.length);\n    const /** @type {?} */ next = containeePaths.slice(container.segments.length);\n    if (!equalPath(container.segments, current)) return false;\n    if (!container.children[PRIMARY_OUTLET]) return false;\n    return containsSegmentGroupHelper(container.children[PRIMARY_OUTLET], containee, next);\n  }\n}\n/**\n * \\@whatItDoes Represents the parsed URL.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree =\n *       router.parseUrl('/team/33/(user/victor//support:help)?debug=true#fragment');\n *     const f = tree.fragment; // return 'fragment'\n *     const q = tree.queryParams; // returns {debug: 'true'}\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments; // returns 2 segments 'team' and '33'\n *     g.children[PRIMARY_OUTLET].segments; // returns 2 segments 'user' and 'victor'\n *     g.children['support'].segments; // return 1 segment 'help'\n *   }\n * }\n * ```\n * \n * \\@description \n * \n * Since a router state is a tree, and the URL is nothing but a serialized state, the URL is a\n * serialized tree.\n * UrlTree is a data structure that provides a lot of affordances in dealing with URLs\n * \n * \\@stable\n */\nexport class UrlTree {\n/**\n * \\@internal\n */\n_queryParamMap: ParamMap;\n/**\n * \\@internal\n * @param {?} root\n * @param {?} queryParams\n * @param {?} fragment\n */\nconstructor(\npublic root: UrlSegmentGroup,\npublic queryParams: {[key: string]: string},\npublic fragment: string|null) {}\n/**\n * @return {?}\n */\nget queryParamMap(): ParamMap {\n    if (!this._queryParamMap) {\n      this._queryParamMap = convertToParamMap(this.queryParams);\n    }\n    return this._queryParamMap;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return DEFAULT_SERIALIZER.serialize(this); }\n}\n\nfunction UrlTree_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlTree.prototype._queryParamMap;\n/**\n * The root segment group of the URL tree\n * @type {?}\n */\nUrlTree.prototype.root;\n/**\n * The query params of the URL\n * @type {?}\n */\nUrlTree.prototype.queryParams;\n/**\n * The fragment of the URL\n * @type {?}\n */\nUrlTree.prototype.fragment;\n}\n\n/**\n * \\@whatItDoes Represents the parsed URL segment group.\n * \n * See {\\@link UrlTree} for more information.\n * \n * \\@stable\n */\nexport class UrlSegmentGroup {\n/**\n * \\@internal\n */\n_sourceSegment: UrlSegmentGroup;\n/**\n * \\@internal\n */\n_segmentIndexShift: number;\n/**\n * The parent node in the url tree\n */\nparent: UrlSegmentGroup|null = null;\n/**\n * @param {?} segments\n * @param {?} children\n */\nconstructor(\npublic segments: UrlSegment[],\npublic children: {[key: string]: UrlSegmentGroup}) {\n    forEach(children, (v: any, k: any) => v.parent = this);\n  }\n/**\n * Wether the segment has child segments\n * @return {?}\n */\nhasChildren(): boolean { return this.numberOfChildren > 0; }\n/**\n * Number of child segments\n * @return {?}\n */\nget numberOfChildren(): number { return Object.keys(this.children).length; }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return serializePaths(this); }\n}\n\nfunction UrlSegmentGroup_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegmentGroup.prototype._sourceSegment;\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegmentGroup.prototype._segmentIndexShift;\n/**\n * The parent node in the url tree\n * @type {?}\n */\nUrlSegmentGroup.prototype.parent;\n/**\n * The URL segments of this group. See {\\@link UrlSegment} for more information\n * @type {?}\n */\nUrlSegmentGroup.prototype.segments;\n/**\n * The list of children of this group\n * @type {?}\n */\nUrlSegmentGroup.prototype.children;\n}\n\n/**\n * \\@whatItDoes Represents a single URL segment.\n * \n * \\@howToUse \n * \n * ```\n * \\@Component({templateUrl:'template.html'}) \n * class MyComponent {\n *   constructor(router: Router) {\n *     const tree: UrlTree = router.parseUrl('/team;id=33');\n *     const g: UrlSegmentGroup = tree.root.children[PRIMARY_OUTLET];\n *     const s: UrlSegment[] = g.segments;\n *     s[0].path; // returns 'team'\n *     s[0].parameters; // returns {id: 33}\n *   }\n * }\n * ```\n * \n * \\@description \n * \n * A UrlSegment is a part of a URL between the two slashes. It contains a path and the matrix\n * parameters associated with the segment.\n * \n * \\@stable\n */\nexport class UrlSegment {\n/**\n * \\@internal\n */\n_parameterMap: ParamMap;\n/**\n * @param {?} path\n * @param {?} parameters\n */\nconstructor(\npublic path: string,\npublic parameters: {[name: string]: string}) {}\n/**\n * @return {?}\n */\nget parameterMap() {\n    if (!this._parameterMap) {\n      this._parameterMap = convertToParamMap(this.parameters);\n    }\n    return this._parameterMap;\n  }\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return serializePath(this); }\n}\n\nfunction UrlSegment_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nUrlSegment.prototype._parameterMap;\n/**\n * The path part of a URL segment\n * @type {?}\n */\nUrlSegment.prototype.path;\n/**\n * The matrix parameters associated with a segment\n * @type {?}\n */\nUrlSegment.prototype.parameters;\n}\n\n/**\n * @param {?} as\n * @param {?} bs\n * @return {?}\n */\nexport function equalSegments(as: UrlSegment[], bs: UrlSegment[]): boolean {\n  return equalPath(as, bs) && as.every((a, i) => shallowEqual(a.parameters, bs[i].parameters));\n}\n/**\n * @param {?} as\n * @param {?} bs\n * @return {?}\n */\nexport function equalPath(as: UrlSegment[], bs: UrlSegment[]): boolean {\n  if (as.length !== bs.length) return false;\n  return as.every((a, i) => a.path === bs[i].path);\n}\n/**\n * @template T\n * @param {?} segment\n * @param {?} fn\n * @return {?}\n */\nexport function mapChildrenIntoArray<T>(\n    segment: UrlSegmentGroup, fn: (v: UrlSegmentGroup, k: string) => T[]): T[] {\n  let /** @type {?} */ res: T[] = [];\n  forEach(segment.children, (child: UrlSegmentGroup, childOutlet: string) => {\n    if (childOutlet === PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  forEach(segment.children, (child: UrlSegmentGroup, childOutlet: string) => {\n    if (childOutlet !== PRIMARY_OUTLET) {\n      res = res.concat(fn(child, childOutlet));\n    }\n  });\n  return res;\n}\n/**\n * \\@whatItDoes Serializes and deserializes a URL string into a URL tree.\n * \n * \\@description The url serialization strategy is customizable. You can\n * make all URLs case insensitive by providing a custom UrlSerializer.\n * \n * See {\\@link DefaultUrlSerializer} for an example of a URL serializer.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class UrlSerializer {\n/**\n * Parse a url into a {\\@link UrlTree}\n * @abstract\n * @param {?} url\n * @return {?}\n */\nparse(url: string) {}\n/**\n * Converts a {\\@link UrlTree} into a url\n * @abstract\n * @param {?} tree\n * @return {?}\n */\nserialize(tree: UrlTree) {}\n}\n/**\n * \\@whatItDoes A default implementation of the {\\@link UrlSerializer}.\n * \n * \\@description \n * \n * Example URLs:\n * \n * ```\n * /inbox/33(popup:compose)\n * /inbox/33;open=true/messages/44\n * ```\n * \n * DefaultUrlSerializer uses parentheses to serialize secondary segments (e.g., popup:compose), the\n * colon syntax to specify the outlet, and the ';parameter=value' syntax (e.g., open=true) to\n * specify route specific parameters.\n * \n * \\@stable\n */\nexport class DefaultUrlSerializer implements UrlSerializer {\n/**\n * Parses a url into a {\\@link UrlTree}\n * @param {?} url\n * @return {?}\n */\nparse(url: string): UrlTree {\n    const /** @type {?} */ p = new UrlParser(url);\n    return new UrlTree(p.parseRootSegment(), p.parseQueryParams(), p.parseFragment());\n  }\n/**\n * Converts a {\\@link UrlTree} into a url\n * @param {?} tree\n * @return {?}\n */\nserialize(tree: UrlTree): string {\n    const /** @type {?} */ segment = `/${serializeSegment(tree.root, true)}`;\n    const /** @type {?} */ query = serializeQueryParams(tree.queryParams);\n    const /** @type {?} */ fragment = typeof tree.fragment === `string` ? `#${encodeURI( /** @type {?} */((tree.fragment)))}` : '';\n\n    return `${segment}${query}${fragment}`;\n  }\n}\n\nconst /** @type {?} */ DEFAULT_SERIALIZER = new DefaultUrlSerializer();\n/**\n * @param {?} segment\n * @return {?}\n */\nexport function serializePaths(segment: UrlSegmentGroup): string {\n  return segment.segments.map(p => serializePath(p)).join('/');\n}\n/**\n * @param {?} segment\n * @param {?} root\n * @return {?}\n */\nfunction serializeSegment(segment: UrlSegmentGroup, root: boolean): string {\n  if (!segment.hasChildren()) {\n    return serializePaths(segment);\n  }\n\n  if (root) {\n    const /** @type {?} */ primary = segment.children[PRIMARY_OUTLET] ?\n        serializeSegment(segment.children[PRIMARY_OUTLET], false) :\n        '';\n    const /** @type {?} */ children: string[] = [];\n\n    forEach(segment.children, (v: UrlSegmentGroup, k: string) => {\n      if (k !== PRIMARY_OUTLET) {\n        children.push(`${k}:${serializeSegment(v, false)}`);\n      }\n    });\n\n    return children.length > 0 ? `${primary}(${children.join('//')})` : primary;\n\n  } else {\n    const /** @type {?} */ children = mapChildrenIntoArray(segment, (v: UrlSegmentGroup, k: string) => {\n      if (k === PRIMARY_OUTLET) {\n        return [serializeSegment(segment.children[PRIMARY_OUTLET], false)];\n      }\n\n      return [`${k}:${serializeSegment(v, false)}`];\n\n    });\n\n    return `${serializePaths(segment)}/(${children.join('//')})`;\n  }\n}\n/**\n * @param {?} s\n * @return {?}\n */\nexport function encode(s: string): string {\n  return encodeURIComponent(s);\n}\n/**\n * @param {?} s\n * @return {?}\n */\nexport function decode(s: string): string {\n  return decodeURIComponent(s);\n}\n/**\n * @param {?} path\n * @return {?}\n */\nexport function serializePath(path: UrlSegment): string {\n  return `${encode(path.path)}${serializeParams(path.parameters)}`;\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction serializeParams(params: {[key: string]: string}): string {\n  return Object.keys(params).map(key => `;${encode(key)}=${encode(params[key])}`).join('');\n}\n/**\n * @param {?} params\n * @return {?}\n */\nfunction serializeQueryParams(params: {[key: string]: any}): string {\n  const /** @type {?} */ strParams: string[] = Object.keys(params).map((name) => {\n    const /** @type {?} */ value = params[name];\n    return Array.isArray(value) ? value.map(v => `${encode(name)}=${encode(v)}`).join('&') :\n                                  `${encode(name)}=${encode(value)}`;\n  });\n\n  return strParams.length ? `?${strParams.join(\"&\")}` : '';\n}\n\nconst /** @type {?} */ SEGMENT_RE = /^[^\\/()?;=&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchSegments(str: string): string {\n  const /** @type {?} */ match = str.match(SEGMENT_RE);\n  return match ? match[0] : '';\n}\n\nconst /** @type {?} */ QUERY_PARAM_RE = /^[^=?&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchQueryParams(str: string): string {\n  const /** @type {?} */ match = str.match(QUERY_PARAM_RE);\n  return match ? match[0] : '';\n}\n\nconst /** @type {?} */ QUERY_PARAM_VALUE_RE = /^[^?&#]+/;\n/**\n * @param {?} str\n * @return {?}\n */\nfunction matchUrlQueryParamValue(str: string): string {\n  const /** @type {?} */ match = str.match(QUERY_PARAM_VALUE_RE);\n  return match ? match[0] : '';\n}\nclass UrlParser {\nprivate remaining: string;\n/**\n * @param {?} url\n */\nconstructor(private url: string) { this.remaining = url; }\n/**\n * @return {?}\n */\nparseRootSegment(): UrlSegmentGroup {\n    this.consumeOptional('/');\n\n    if (this.remaining === '' || this.peekStartsWith('?') || this.peekStartsWith('#')) {\n      return new UrlSegmentGroup([], {});\n    }\n\n    // The root segment group never has segments\n    return new UrlSegmentGroup([], this.parseChildren());\n  }\n/**\n * @return {?}\n */\nparseQueryParams(): {[key: string]: any} {\n    const /** @type {?} */ params: {[key: string]: any} = {};\n    if (this.consumeOptional('?')) {\n      do {\n        this.parseQueryParam(params);\n      } while (this.consumeOptional('&'));\n    }\n    return params;\n  }\n/**\n * @return {?}\n */\nparseFragment(): string|null {\n    return this.consumeOptional('#') ? decodeURI(this.remaining) : null;\n  }\n/**\n * @return {?}\n */\nprivate parseChildren(): {[outlet: string]: UrlSegmentGroup} {\n    if (this.remaining === '') {\n      return {};\n    }\n\n    this.consumeOptional('/');\n\n    const /** @type {?} */ segments: UrlSegment[] = [];\n    if (!this.peekStartsWith('(')) {\n      segments.push(this.parseSegment());\n    }\n\n    while (this.peekStartsWith('/') && !this.peekStartsWith('//') && !this.peekStartsWith('/(')) {\n      this.capture('/');\n      segments.push(this.parseSegment());\n    }\n\n    let /** @type {?} */ children: {[outlet: string]: UrlSegmentGroup} = {};\n    if (this.peekStartsWith('/(')) {\n      this.capture('/');\n      children = this.parseParens(true);\n    }\n\n    let /** @type {?} */ res: {[outlet: string]: UrlSegmentGroup} = {};\n    if (this.peekStartsWith('(')) {\n      res = this.parseParens(false);\n    }\n\n    if (segments.length > 0 || Object.keys(children).length > 0) {\n      res[PRIMARY_OUTLET] = new UrlSegmentGroup(segments, children);\n    }\n\n    return res;\n  }\n/**\n * @return {?}\n */\nprivate parseSegment(): UrlSegment {\n    const /** @type {?} */ path = matchSegments(this.remaining);\n    if (path === '' && this.peekStartsWith(';')) {\n      throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);\n    }\n\n    this.capture(path);\n    return new UrlSegment(decode(path), this.parseMatrixParams());\n  }\n/**\n * @return {?}\n */\nprivate parseMatrixParams(): {[key: string]: any} {\n    const /** @type {?} */ params: {[key: string]: any} = {};\n    while (this.consumeOptional(';')) {\n      this.parseParam(params);\n    }\n    return params;\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate parseParam(params: {[key: string]: any}): void {\n    const /** @type {?} */ key = matchSegments(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let /** @type {?} */ value: any = '';\n    if (this.consumeOptional('=')) {\n      const /** @type {?} */ valueMatch = matchSegments(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    params[decode(key)] = decode(value);\n  }\n/**\n * @param {?} params\n * @return {?}\n */\nprivate parseQueryParam(params: {[key: string]: any}): void {\n    const /** @type {?} */ key = matchQueryParams(this.remaining);\n    if (!key) {\n      return;\n    }\n    this.capture(key);\n    let /** @type {?} */ value: any = '';\n    if (this.consumeOptional('=')) {\n      const /** @type {?} */ valueMatch = matchUrlQueryParamValue(this.remaining);\n      if (valueMatch) {\n        value = valueMatch;\n        this.capture(value);\n      }\n    }\n\n    const /** @type {?} */ decodedKey = decode(key);\n    const /** @type {?} */ decodedVal = decode(value);\n\n    if (params.hasOwnProperty(decodedKey)) {\n      // Append to existing values\n      let /** @type {?} */ currentVal = params[decodedKey];\n      if (!Array.isArray(currentVal)) {\n        currentVal = [currentVal];\n        params[decodedKey] = currentVal;\n      }\n      currentVal.push(decodedVal);\n    } else {\n      // Create a new value\n      params[decodedKey] = decodedVal;\n    }\n  }\n/**\n * @param {?} allowPrimary\n * @return {?}\n */\nprivate parseParens(allowPrimary: boolean): {[outlet: string]: UrlSegmentGroup} {\n    const /** @type {?} */ segments: {[key: string]: UrlSegmentGroup} = {};\n    this.capture('(');\n\n    while (!this.consumeOptional(')') && this.remaining.length > 0) {\n      const /** @type {?} */ path = matchSegments(this.remaining);\n\n      const /** @type {?} */ next = this.remaining[path.length];\n\n      // if is is not one of these characters, then the segment was unescaped\n      // or the group was not closed\n      if (next !== '/' && next !== ')' && next !== ';') {\n        throw new Error(`Cannot parse url '${this.url}'`);\n      }\n\n      let /** @type {?} */ outletName: string = /** @type {?} */(( undefined));\n      if (path.indexOf(':') > -1) {\n        outletName = path.substr(0, path.indexOf(':'));\n        this.capture(outletName);\n        this.capture(':');\n      } else if (allowPrimary) {\n        outletName = PRIMARY_OUTLET;\n      }\n\n      const /** @type {?} */ children = this.parseChildren();\n      segments[outletName] = Object.keys(children).length === 1 ? children[PRIMARY_OUTLET] :\n                                                                  new UrlSegmentGroup([], children);\n      this.consumeOptional('//');\n    }\n\n    return segments;\n  }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate peekStartsWith(str: string): boolean { return this.remaining.startsWith(str); }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate consumeOptional(str: string): boolean {\n    if (this.peekStartsWith(str)) {\n      this.remaining = this.remaining.substring(str.length);\n      return true;\n    }\n    return false;\n  }\n/**\n * @param {?} str\n * @return {?}\n */\nprivate capture(str: string): void {\n    if (!this.consumeOptional(str)) {\n      throw new Error(`Expected \"${str}\".`);\n    }\n  }\n}\n\nfunction UrlParser_tsickle_Closure_declarations() {\n/** @type {?} */\nUrlParser.prototype.remaining;\n/** @type {?} */\nUrlParser.prototype.url;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModuleFactory, ɵisObservable as isObservable, ɵisPromise as isPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {fromPromise} from 'rxjs/observable/fromPromise';\nimport {of } from 'rxjs/observable/of';\nimport {concatAll} from 'rxjs/operator/concatAll';\nimport {every} from 'rxjs/operator/every';\nimport * as l from 'rxjs/operator/last';\nimport {map} from 'rxjs/operator/map';\nimport {mergeAll} from 'rxjs/operator/mergeAll';\nimport {PRIMARY_OUTLET} from '../shared';\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function shallowEqualArrays(a: any[], b: any[]): boolean {\n  if (a.length !== b.length) return false;\n  for (let /** @type {?} */ i = 0; i < a.length; ++i) {\n    if (!shallowEqual(a[i], b[i])) return false;\n  }\n  return true;\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nexport function shallowEqual(a: {[x: string]: any}, b: {[x: string]: any}): boolean {\n  const /** @type {?} */ k1 = Object.keys(a);\n  const /** @type {?} */ k2 = Object.keys(b);\n  if (k1.length != k2.length) {\n    return false;\n  }\n  let /** @type {?} */ key: string;\n  for (let /** @type {?} */ i = 0; i < k1.length; i++) {\n    key = k1[i];\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * @template T\n * @param {?} arr\n * @return {?}\n */\nexport function flatten<T>(arr: T[][]): T[] {\n  return Array.prototype.concat.apply([], arr);\n}\n/**\n * @template T\n * @param {?} a\n * @return {?}\n */\nexport function last<T>(a: T[]): T|null {\n  return a.length > 0 ? a[a.length - 1] : null;\n}\n/**\n * @param {?} bools\n * @return {?}\n */\nexport function and(bools: boolean[]): boolean {\n  return !bools.some(v => !v);\n}\n/**\n * @template K, V\n * @param {?} map\n * @param {?} callback\n * @return {?}\n */\nexport function forEach<K, V>(map: {[key: string]: V}, callback: (v: V, k: string) => void): void {\n  for (const /** @type {?} */ prop in map) {\n    if (map.hasOwnProperty(prop)) {\n      callback(map[prop], prop);\n    }\n  }\n}\n/**\n * @template A, B\n * @param {?} obj\n * @param {?} fn\n * @return {?}\n */\nexport function waitForMap<A, B>(\n    obj: {[k: string]: A}, fn: (k: string, a: A) => Observable<B>): Observable<{[k: string]: B}> {\n  if (Object.keys(obj).length === 0) {\n    return of ({})\n  }\n\n  const /** @type {?} */ waitHead: Observable<B>[] = [];\n  const /** @type {?} */ waitTail: Observable<B>[] = [];\n  const /** @type {?} */ res: {[k: string]: B} = {};\n\n  forEach(obj, (a: A, k: string) => {\n    const /** @type {?} */ mapped = map.call(fn(k, a), (r: B) => res[k] = r);\n    if (k === PRIMARY_OUTLET) {\n      waitHead.push(mapped);\n    } else {\n      waitTail.push(mapped);\n    }\n  });\n\n  const /** @type {?} */ concat$ = concatAll.call(of (...waitHead, ...waitTail));\n  const /** @type {?} */ last$ = l.last.call(concat$);\n  return map.call(last$, () => res);\n}\n/**\n * @param {?} observables\n * @return {?}\n */\nexport function andObservables(observables: Observable<Observable<any>>): Observable<boolean> {\n  const /** @type {?} */ merged$ = mergeAll.call(observables);\n  return every.call(merged$, (result: any) => result === true);\n}\n/**\n * @template T\n * @param {?} value\n * @return {?}\n */\nexport function wrapIntoObservable<T>(value: T | NgModuleFactory<T>| Promise<T>| Observable<T>):\n    Observable<T> {\n  if (isObservable(value)) {\n    return value;\n  }\n\n  if (isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    // Required ie when a Resolver returns a AngularJS `$q` promise to correctly trigger the\n    // change detection.\n    return fromPromise(Promise.resolve(value));\n  }\n\n  return of (value);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModuleFactory, NgModuleRef, Type} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {PRIMARY_OUTLET} from './shared';\nimport {UrlSegment, UrlSegmentGroup} from './url_tree';\n\n/**\n * @whatItDoes Represents router configuration.\n *\n * @description\n * `Routes` is an array of route configurations. Each one has the following properties:\n *\n * - `path` is a string that uses the route matcher DSL.\n * - `pathMatch` is a string that specifies the matching strategy.\n * - `matcher` defines a custom strategy for path matching and supersedes `path` and `pathMatch`.\n * - `component` is a component type.\n * - `redirectTo` is the url fragment which will replace the current matched segment.\n * - `outlet` is the name of the outlet the component should be placed into.\n * - `canActivate` is an array of DI tokens used to look up CanActivate handlers. See\n *   {@link CanActivate} for more info.\n * - `canActivateChild` is an array of DI tokens used to look up CanActivateChild handlers. See\n *   {@link CanActivateChild} for more info.\n * - `canDeactivate` is an array of DI tokens used to look up CanDeactivate handlers. See\n *   {@link CanDeactivate} for more info.\n * - `canLoad` is an array of DI tokens used to look up CanLoad handlers. See\n *   {@link CanLoad} for more info.\n * - `data` is additional data provided to the component via `ActivatedRoute`.\n * - `resolve` is a map of DI tokens used to look up data resolvers. See {@link Resolve} for more\n *   info.\n * - `runGuardsAndResolvers` defines when guards and resolvers will be run. By default they run only\n *    when the matrix parameters of the route change. When set to `paramsOrQueryParamsChange` they\n *    will also run when query params change. And when set to `always`, they will run every time.\n * - `children` is an array of child route definitions.\n * - `loadChildren` is a reference to lazy loaded child routes. See {@link LoadChildren} for more\n *   info.\n *\n * ### Simple Configuration\n *\n * ```\n * [{\n *   path: 'team/:id',\n  *  component: Team,\n *   children: [{\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11/user/bob`, the router will create the team component with the user\n * component in it.\n *\n * ### Multiple Outlets\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team\n * }, {\n *   path: 'chat/:user',\n *   component: Chat\n *   outlet: 'aux'\n * }]\n * ```\n *\n * When navigating to `/team/11(aux:chat/jim)`, the router will create the team component next to\n * the chat component. The chat component will be placed into the aux outlet.\n *\n * ### Wild Cards\n *\n * ```\n * [{\n *   path: '**',\n *   component: Sink\n * }]\n * ```\n *\n * Regardless of where you navigate to, the router will instantiate the sink component.\n *\n * ### Redirects\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: 'legacy/user/:name',\n *     redirectTo: 'user/:name'\n *   }, {\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to '/team/11/legacy/user/jim', the router will change the url to\n * '/team/11/user/jim', and then will instantiate the team component with the user component\n * in it.\n *\n * If the `redirectTo` value starts with a '/', then it is an absolute redirect. E.g., if in the\n * example above we change the `redirectTo` to `/user/:name`, the result url will be '/user/jim'.\n *\n * ### Empty Path\n *\n * Empty-path route configurations can be used to instantiate components that do not 'consume'\n * any url segments. Let's look at the following configuration:\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: '',\n *     component: AllUsers\n *   }, {\n *     path: 'user/:name',\n *     component: User\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11`, the router will instantiate the AllUsers component.\n *\n * Empty-path routes can have children.\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   children: [{\n *     path: '',\n *     component: WrapperCmp,\n *     children: [{\n *       path: 'user/:name',\n *       component: User\n *     }]\n *   }]\n * }]\n * ```\n *\n * When navigating to `/team/11/user/jim`, the router will instantiate the wrapper component with\n * the user component in it.\n *\n * An empty path route inherits its parent's params and data. This is because it cannot have its\n * own params, and, as a result, it often uses its parent's params and data as its own.\n *\n * ### Matching Strategy\n *\n * By default the router will look at what is left in the url, and check if it starts with\n * the specified path (e.g., `/team/11/user` starts with `team/:id`).\n *\n * We can change the matching strategy to make sure that the path covers the whole unconsumed url,\n * which is akin to `unconsumedUrl === path` or `$` regular expressions.\n *\n * This is particularly important when redirecting empty-path routes.\n *\n * ```\n * [{\n *   path: '',\n *   pathMatch: 'prefix', //default\n *   redirectTo: 'main'\n * }, {\n *   path: 'main',\n *   component: Main\n * }]\n * ```\n *\n * Since an empty path is a prefix of any url, even when navigating to '/main', the router will\n * still apply the redirect.\n *\n * If `pathMatch: full` is provided, the router will apply the redirect if and only if navigating to\n * '/'.\n *\n * ```\n * [{\n *   path: '',\n *   pathMatch: 'full',\n *   redirectTo: 'main'\n * }, {\n *   path: 'main',\n *   component: Main\n * }]\n * ```\n *\n * ### Componentless Routes\n *\n * It is useful at times to have the ability to share parameters between sibling components.\n *\n * Say we have two components--ChildCmp and AuxCmp--that we want to put next to each other and both\n * of them require some id parameter.\n *\n * One way to do that would be to have a bogus parent component, so both the siblings can get the id\n * parameter from it. This is not ideal. Instead, you can use a componentless route.\n *\n * ```\n * [{\n *    path: 'parent/:id',\n *    children: [\n *      { path: 'a', component: MainChild },\n *      { path: 'b', component: AuxChild, outlet: 'aux' }\n *    ]\n * }]\n * ```\n *\n * So when navigating to `parent/10/(a//aux:b)`, the route will instantiate the main child and aux\n * child components next to each other. In this example, the application component\n * has to have the primary and aux outlets defined.\n *\n * The router will also merge the `params`, `data`, and `resolve` of the componentless parent into\n * the `params`, `data`, and `resolve` of the children. This is done because there is no component\n * that can inject the activated route of the componentless parent.\n *\n * This is especially useful when child components are defined as follows:\n *\n * ```\n * [{\n *    path: 'parent/:id',\n *    children: [\n *      { path: '', component: MainChild },\n *      { path: '', component: AuxChild, outlet: 'aux' }\n *    ]\n * }]\n * ```\n *\n * With this configuration in place, navigating to '/parent/10' will create the main child and aux\n * components.\n *\n * ### Lazy Loading\n *\n * Lazy loading speeds up our application load time by splitting it into multiple bundles, and\n * loading them on demand. The router is designed to make lazy loading simple and easy. Instead of\n * providing the children property, you can provide the `loadChildren` property, as follows:\n *\n * ```\n * [{\n *   path: 'team/:id',\n *   component: Team,\n *   loadChildren: 'team'\n * }]\n * ```\n *\n * The router will use registered NgModuleFactoryLoader to fetch an NgModule associated with 'team'.\n * Then it will extract the set of routes defined in that NgModule, and will transparently add\n * those routes to the main configuration.\n *\n * @stable use Routes\n */\nexport type Routes = Route[];\n\n/**\n * @whatItDoes Represents the results of the URL matching.\n *\n * * `consumed` is an array of the consumed URL segments.\n * * `posParams` is a map of positional parameters.\n *\n * @experimental\n */\nexport type UrlMatchResult = {\n  consumed: UrlSegment[]; posParams?: {[name: string]: UrlSegment};\n};\n\n/**\n * @whatItDoes A function matching URLs\n *\n * @description\n *\n * A custom URL matcher can be provided when a combination of `path` and `pathMatch` isn't\n * expressive enough.\n *\n * For instance, the following matcher matches html files.\n *\n * ```\n * function htmlFiles(url: UrlSegment[]) {\n *  return url.length === 1 && url[0].path.endsWith('.html') ? ({consumed: url}) : null;\n * }\n *\n * const routes = [{ matcher: htmlFiles, component: HtmlCmp }];\n * ```\n *\n * @experimental\n */\nexport type UrlMatcher = (segments: UrlSegment[], group: UrlSegmentGroup, route: Route) =>\n    UrlMatchResult;\n\n/**\n * @whatItDoes Represents the static data associated with a particular route.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type Data = {\n  [name: string]: any\n};\n\n/**\n * @whatItDoes Represents the resolved data associated with a particular route.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type ResolveData = {\n  [name: string]: any\n};\n\n/**\n * @whatItDoes The type of `loadChildren`.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type LoadChildrenCallback = () =>\n    Type<any>| NgModuleFactory<any>| Promise<Type<any>>| Observable<Type<any>>;\n\n/**\n * @whatItDoes The type of `loadChildren`.\n * See {@link Routes} for more details.\n * @stable\n */\nexport type LoadChildren = string | LoadChildrenCallback;\n\n/**\n * @whatItDoes The type of `queryParamsHandling`.\n * See {@link RouterLink} for more details.\n * @stable\n */\nexport type QueryParamsHandling = 'merge' | 'preserve' | '';\n\n/**\n * @whatItDoes The type of `runGuardsAndResolvers`.\n * See {@link Routes} for more details.\n * @experimental\n */\nexport type RunGuardsAndResolvers = 'paramsChange' | 'paramsOrQueryParamsChange' | 'always';\n\n/**\n * See {@link Routes} for more details.\n * @stable\n */\nexport interface Route {\n  path?: string;\n  pathMatch?: string;\n  matcher?: UrlMatcher;\n  component?: Type<any>;\n  redirectTo?: string;\n  outlet?: string;\n  canActivate?: any[];\n  canActivateChild?: any[];\n  canDeactivate?: any[];\n  canLoad?: any[];\n  data?: Data;\n  resolve?: ResolveData;\n  children?: Routes;\n  loadChildren?: LoadChildren;\n  runGuardsAndResolvers?: RunGuardsAndResolvers;\n  /**\n   * Filled for routes with `loadChildren` once the module has been loaded\n   * @internal\n   */\n  _loadedConfig?: LoadedRouterConfig;\n}\nexport class LoadedRouterConfig {\n/**\n * @param {?} routes\n * @param {?} module\n */\nconstructor(public routes: Route[],\npublic module: NgModuleRef<any>) {}\n}\n\nfunction LoadedRouterConfig_tsickle_Closure_declarations() {\n/** @type {?} */\nLoadedRouterConfig.prototype.routes;\n/** @type {?} */\nLoadedRouterConfig.prototype.module;\n}\n\n/**\n * @param {?} config\n * @param {?=} parentPath\n * @return {?}\n */\nexport function validateConfig(config: Routes, parentPath: string = ''): void {\n  // forEach doesn't iterate undefined values\n  for (let /** @type {?} */ i = 0; i < config.length; i++) {\n    const /** @type {?} */ route: Route = config[i];\n    const /** @type {?} */ fullPath: string = getFullPath(parentPath, route);\n    validateNode(route, fullPath);\n  }\n}\n/**\n * @param {?} route\n * @param {?} fullPath\n * @return {?}\n */\nfunction validateNode(route: Route, fullPath: string): void {\n  if (!route) {\n    throw new Error(`\n      Invalid configuration of route '${fullPath}': Encountered undefined route.\n      The reason might be an extra comma.\n\n      Example:\n      const routes: Routes = [\n        { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n        { path: 'dashboard',  component: DashboardComponent },, << two commas\n        { path: 'detail/:id', component: HeroDetailComponent }\n      ];\n    `);\n  }\n  if (Array.isArray(route)) {\n    throw new Error(`Invalid configuration of route '${fullPath}': Array cannot be specified`);\n  }\n  if (!route.component && (route.outlet && route.outlet !== PRIMARY_OUTLET)) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': a componentless route cannot have a named outlet set`);\n  }\n  if (route.redirectTo && route.children) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and children cannot be used together`);\n  }\n  if (route.redirectTo && route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and loadChildren cannot be used together`);\n  }\n  if (route.children && route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': children and loadChildren cannot be used together`);\n  }\n  if (route.redirectTo && route.component) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': redirectTo and component cannot be used together`);\n  }\n  if (route.path && route.matcher) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': path and matcher cannot be used together`);\n  }\n  if (route.redirectTo === void 0 && !route.component && !route.children && !route.loadChildren) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}'. One of the following must be provided: component, redirectTo, children or loadChildren`);\n  }\n  if (route.path === void 0 && route.matcher === void 0) {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': routes must have either a path or a matcher specified`);\n  }\n  if (typeof route.path === 'string' && route.path.charAt(0) === '/') {\n    throw new Error(`Invalid configuration of route '${fullPath}': path cannot start with a slash`);\n  }\n  if (route.path === '' && route.redirectTo !== void 0 && route.pathMatch === void 0) {\n    const /** @type {?} */ exp =\n        `The default value of 'pathMatch' is 'prefix', but often the intent is to use 'full'.`;\n    throw new Error(\n        `Invalid configuration of route '{path: \"${fullPath}\", redirectTo: \"${route.redirectTo}\"}': please provide 'pathMatch'. ${exp}`);\n  }\n  if (route.pathMatch !== void 0 && route.pathMatch !== 'full' && route.pathMatch !== 'prefix') {\n    throw new Error(\n        `Invalid configuration of route '${fullPath}': pathMatch can only be set to 'prefix' or 'full'`);\n  }\n  if (route.children) {\n    validateConfig(route.children, fullPath);\n  }\n}\n/**\n * @param {?} parentPath\n * @param {?} currentRoute\n * @return {?}\n */\nfunction getFullPath(parentPath: string, currentRoute: Route): string {\n  if (!currentRoute) {\n    return parentPath;\n  }\n  if (!parentPath && !currentRoute.path) {\n    return '';\n  } else if (parentPath && !currentRoute.path) {\n    return `${parentPath}/`;\n  } else if (!parentPath && currentRoute.path) {\n    return currentRoute.path;\n  } else {\n    return `${parentPath}/${currentRoute.path}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\nimport {Route, UrlMatchResult} from './config';\nimport {UrlSegment, UrlSegmentGroup} from './url_tree';\n/**\n * \\@whatItDoes Name of the primary outlet.\n * \n * \\@stable\n */\nexport const PRIMARY_OUTLET = 'primary';\n\n/**\n * A collection of parameters.\n *\n * @stable\n */\nexport type Params = {\n  [key: string]: any\n};\n\n/**\n * Matrix and Query parameters.\n *\n * `ParamMap` makes it easier to work with parameters as they could have either a single value or\n * multiple value. Because this should be known by the user, calling `get` or `getAll` returns the\n * correct type (either `string` or `string[]`).\n *\n * The API is inspired by the URLSearchParams interface.\n * see https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams\n *\n * @stable\n */\nexport interface ParamMap {\n  has(name: string): boolean;\n  /**\n   * Return a single value for the given parameter name:\n   * - the value when the parameter has a single value,\n   * - the first value if the parameter has multiple values,\n   * - `null` when there is no such parameter.\n   */\n  get(name: string): string|null;\n  /**\n   * Return an array of values for the given parameter name.\n   *\n   * If there is no such parameter, an empty array is returned.\n   */\n  getAll(name: string): string[];\n\n  /** Name of the parameters */\n  readonly keys: string[];\n}\nclass ParamsAsMap implements ParamMap {\nprivate params: Params;\n/**\n * @param {?} params\n */\nconstructor(params: Params) { this.params = params || {}; }\n/**\n * @param {?} name\n * @return {?}\n */\nhas(name: string): boolean { return this.params.hasOwnProperty(name); }\n/**\n * @param {?} name\n * @return {?}\n */\nget(name: string): string|null {\n    if (this.has(name)) {\n      const /** @type {?} */ v = this.params[name];\n      return Array.isArray(v) ? v[0] : v;\n    }\n\n    return null;\n  }\n/**\n * @param {?} name\n * @return {?}\n */\ngetAll(name: string): string[] {\n    if (this.has(name)) {\n      const /** @type {?} */ v = this.params[name];\n      return Array.isArray(v) ? v : [v];\n    }\n\n    return [];\n  }\n/**\n * @return {?}\n */\nget keys(): string[] { return Object.keys(this.params); }\n}\n\nfunction ParamsAsMap_tsickle_Closure_declarations() {\n/** @type {?} */\nParamsAsMap.prototype.params;\n}\n\n/**\n * Convert a {\\@link Params} instance to a {\\@link ParamMap}.\n * \n * \\@stable\n * @param {?} params\n * @return {?}\n */\nexport function convertToParamMap(params: Params): ParamMap {\n  return new ParamsAsMap(params);\n}\n\nconst /** @type {?} */ NAVIGATION_CANCELING_ERROR = 'ngNavigationCancelingError';\n/**\n * @param {?} message\n * @return {?}\n */\nexport function navigationCancelingError(message: string) {\n  const /** @type {?} */ error = Error('NavigationCancelingError: ' + message);\n  ( /** @type {?} */((error as any)))[NAVIGATION_CANCELING_ERROR] = true;\n  return error;\n}\n/**\n * @param {?} error\n * @return {?}\n */\nexport function isNavigationCancelingError(error: Error) {\n  return ( /** @type {?} */((error as any)))[NAVIGATION_CANCELING_ERROR];\n}\n/**\n * @param {?} segments\n * @param {?} segmentGroup\n * @param {?} route\n * @return {?}\n */\nexport function defaultUrlMatcher(\n    segments: UrlSegment[], segmentGroup: UrlSegmentGroup, route: Route): UrlMatchResult|null {\n  const /** @type {?} */ parts = /** @type {?} */(( route.path)).split('/');\n\n  if (parts.length > segments.length) {\n    // The actual URL is shorter than the config, no match\n    return null;\n  }\n\n  if (route.pathMatch === 'full' &&\n      (segmentGroup.hasChildren() || parts.length < segments.length)) {\n    // The config is longer than the actual URL but we are looking for a full match, return null\n    return null;\n  }\n\n  const /** @type {?} */ posParams: {[key: string]: UrlSegment} = {};\n\n  // Check each config part against the actual URL\n  for (let /** @type {?} */ index = 0; index < parts.length; index++) {\n    const /** @type {?} */ part = parts[index];\n    const /** @type {?} */ segment = segments[index];\n    const /** @type {?} */ isParameter = part.startsWith(':');\n    if (isParameter) {\n      posParams[part.substring(1)] = segment;\n    } else if (part !== segment.path) {\n      // The actual URL part does not match the config, no match\n      return null;\n    }\n  }\n\n  return {consumed: segments.slice(0, parts.length), posParams};\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Route} from './config';\nimport {RouterStateSnapshot} from './router_state';\n/**\n * \\@whatItDoes Represents an event triggered when a navigation starts.\n * \n * \\@stable\n */\nexport class NavigationStart {\n/**\n * @param {?} id\n * @param {?} url\n */\nconstructor(\npublic id: number,\npublic url: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return `NavigationStart(id: ${this.id}, url: '${this.url}')`; }\n}\n\nfunction NavigationStart_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationStart.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationStart.prototype.url;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation ends successfully.\n * \n * \\@stable\n */\nexport class NavigationEnd {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} urlAfterRedirects\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic urlAfterRedirects: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`;\n  }\n}\n\nfunction NavigationEnd_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationEnd.prototype.urlAfterRedirects;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation is canceled.\n * \n * \\@stable\n */\nexport class NavigationCancel {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} reason\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic reason: string) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string { return `NavigationCancel(id: ${this.id}, url: '${this.url}')`; }\n}\n\nfunction NavigationCancel_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationCancel.prototype.reason;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a navigation fails due to an unexpected error.\n * \n * \\@stable\n */\nexport class NavigationError {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} error\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic error: any) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`;\n  }\n}\n\nfunction NavigationError_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nNavigationError.prototype.error;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when routes are recognized.\n * \n * \\@stable\n */\nexport class RoutesRecognized {\n/**\n * @param {?} id\n * @param {?} url\n * @param {?} urlAfterRedirects\n * @param {?} state\n */\nconstructor(\npublic id: number,\npublic url: string,\npublic urlAfterRedirects: string,\npublic state: RouterStateSnapshot) {}\n/**\n * \\@docsNotRequired\n * @return {?}\n */\ntoString(): string {\n    return `RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`;\n  }\n}\n\nfunction RoutesRecognized_tsickle_Closure_declarations() {\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.id;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.url;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.urlAfterRedirects;\n/**\n * \\@docsNotRequired\n * @type {?}\n */\nRoutesRecognized.prototype.state;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered before lazy loading a route config.\n * \n * \\@experimental\n */\nexport class RouteConfigLoadStart {\n/**\n * @param {?} route\n */\nconstructor(public route: Route) {}\n/**\n * @return {?}\n */\ntoString(): string { return `RouteConfigLoadStart(path: ${this.route.path})`; }\n}\n\nfunction RouteConfigLoadStart_tsickle_Closure_declarations() {\n/** @type {?} */\nRouteConfigLoadStart.prototype.route;\n}\n\n/**\n * \\@whatItDoes Represents an event triggered when a route has been lazy loaded.\n * \n * \\@experimental\n */\nexport class RouteConfigLoadEnd {\n/**\n * @param {?} route\n */\nconstructor(public route: Route) {}\n/**\n * @return {?}\n */\ntoString(): string { return `RouteConfigLoadEnd(path: ${this.route.path})`; }\n}\n\nfunction RouteConfigLoadEnd_tsickle_Closure_declarations() {\n/** @type {?} */\nRouteConfigLoadEnd.prototype.route;\n}\n\n\n/**\n * @whatItDoes Represents a router event.\n *\n * One of:\n * - {@link NavigationStart},\n * - {@link NavigationEnd},\n * - {@link NavigationCancel},\n * - {@link NavigationError},\n * - {@link RoutesRecognized},\n * - {@link RouteConfigLoadStart},\n * - {@link RouteConfigLoadEnd}\n *\n * @stable\n */\nexport type Event = NavigationStart | NavigationEnd | NavigationCancel | NavigationError |\n    RoutesRecognized | RouteConfigLoadStart | RouteConfigLoadEnd;\n"], "names": ["getDOM", "map", "getOutlet", "split", "NoMatch", "match", "last", "isPromise", "isObservable", "l.last"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AyBAA;;;;;;;;;;;;AAgBA,AAAA,MAAA,eAAA,CAAA;;;;;IAKA,WAAA,CAFa,EAAI,EAEJ,GAAK,EAAlB;QAFa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAEJ,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;KAAO;;;;;IAOzB,QAJG,GAIH,EAJuB,OAAO,CAI9B,oBAAA,EAJ8B,IAAwB,CAAI,EAAC,CAI3D,QAAA,EAJ6D,IAAW,CAAI,GAAC,CAI7E,EAAA,CAJgF,CAAI,EAAC;CAKpF;AAED,AAaA;;;;;AAKA,AAAA,MAAA,aAAA,CAAA;;;;;;IAMA,WAAA,CApBa,EAAI,EAEJ,GAAK,EAEL,iBAAmB,EAgBhC;QApBa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAEJ,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;QAEL,IAAb,CAAA,iBAAa,GAAA,iBAAA,CAAmB;KAAO;;;;;IAwBvC,QArBG,GAqBH;QACI,OArBO,CAqBX,kBAAA,EArBW,IAAsB,CAAI,EAAC,CAqBtC,QAAA,EArBwC,IAAW,CAAI,GAAC,CAqBxD,uBAAA,EArB2D,IAA0B,CAAI,iBAAC,CAqB1F,EAAA,CArB2G,CAAI;KAsB5G;CACF;AAED,AAkBA;;;;;AAKA,AAAA,MAAA,gBAAA,CAAA;;;;;;IAMA,WAAA,CA1Ca,EAAI,EAEJ,GAAK,EAEL,MAAQ,EAsCrB;QA1Ca,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAEJ,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;QAEL,IAAb,CAAA,MAAa,GAAA,MAAA,CAAQ;KAAO;;;;;IA8C5B,QA3CG,GA2CH,EA3CuB,OAAO,CA2C9B,qBAAA,EA3C8B,IAAyB,CAAI,EAAC,CA2C5D,QAAA,EA3C8D,IAAW,CAAI,GAAC,CA2C9E,EAAA,CA3CiF,CAAI,EAAC;CA4CrF;AAED,AAkBA;;;;;AAKA,AAAA,MAAA,eAAA,CAAA;;;;;;IAMA,WAAA,CAhEa,EAAI,EAEJ,GAAK,EAEL,KAAO,EA4DpB;QAhEa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAEJ,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;QAEL,IAAb,CAAA,KAAa,GAAA,KAAA,CAAO;KAAI;;;;;IAoExB,QAjEG,GAiEH;QACI,OAjEO,CAiEX,oBAAA,EAjEW,IAAwB,CAAI,EAAC,CAiExC,QAAA,EAjE0C,IAAW,CAAI,GAAC,CAiE1D,UAAA,EAjE6D,IAAa,CAAI,KAAC,CAiE/E,CAAA,CAjEoF,CAAG;KAkEpF;CACF;AAED,AAkBA;;;;;AAKA,AAAA,MAAA,gBAAA,CAAA;;;;;;;IAOA,WAAA,CAvFa,EAAI,EAEJ,GAAK,EAEL,iBAAmB,EAEnB,KAAO,EAiFpB;QAvFa,IAAb,CAAA,EAAa,GAAA,EAAA,CAAI;QAEJ,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;QAEL,IAAb,CAAA,iBAAa,GAAA,iBAAA,CAAmB;QAEnB,IAAb,CAAA,KAAa,GAAA,KAAA,CAAO;KAAoB;;;;;IA0FxC,QAvFG,GAuFH;QACI,OAvFO,CAuFX,qBAAA,EAvFW,IAAyB,CAAI,EAAC,CAuFzC,QAAA,EAvF2C,IAAW,CAAI,GAAC,CAuF3D,uBAAA,EAvF8D,IAA0B,CAAI,iBAAC,CAuF7F,UAAA,EAvF8G,IAAa,CAAI,KAAC,CAuFhI,CAAA,CAvFqI,CAAG;KAwFrI;CACF;AAED,AAuBA;;;;;AAKA,AAAA,MAAA,oBAAA,CAAA;;;;IAIA,WAAA,CAjHqB,KAAO,EAiH5B;QAjHqB,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAO;KAAM;;;;IAqHlC,QAnHG,GAmHH,EAnHuB,OAAO,CAmH9B,2BAAA,EAnH8B,IAA+B,CAAI,KAAC,CAAK,IAAC,CAmHxE,CAAA,CAnH4E,CAAG,EAAC;CAoH/E;AAED,AAKA;;;;;AAKA,AAAA,MAAA,kBAAA,CAAA;;;;IAIA,WAAA,CA3HqB,KAAO,EA2H5B;QA3HqB,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAO;KAAM;;;;IA+HlC,QA7HG,GA6HH,EA7HuB,OAAO,CA6H9B,yBAAA,EA7H8B,IAA6B,CAAI,KAAC,CAAK,IAAC,CA6HtE,CAAA,CA7H0E,CAAG,EAAC;CA8H7E,AAED,AAGC;;ADjQD;;;;;;;;;;;;AAiBA,AACC,MAAA,cAAA,GAAA,SAAA,CAAA;AAyCD,MAAA,WAAA,CAAA;;;;IAKA,WAAA,CAAG,MAAmB,EAAtB,EAA6B,IAAA,CAAA,MAAA,GAAA,MAAA,IAAA,EAAA,CAAA,EAAA;;;;;IAK7B,GAHG,CAAA,IAAA,EAGH,EAH+B,OAAO,IAAA,CAAK,MAAC,CAAM,cAAC,CAAc,IAAC,CAAI,CAAC,EAAC;;;;;IAQxE,GANG,CAAA,IAAA,EAMH;QACI,IAAI,IANC,CAAI,GAAC,CAAG,IAAC,CAAI,EAAE;YAOlB,uBANM,CAAA,GAAI,IAAA,CAAK,MAAC,CAAM,IAAC,CAAI,CAAC;YAO5B,OANO,KAAA,CAAM,OAAC,CAAO,CAAC,CAAC,GAAG,CAAA,CAAE,CAAC,CAAC,GAAG,CAAA,CAAE;SAOpC;QAED,OANO,IAAA,CAAK;KAOb;;;;;IAKH,MATG,CAAA,IAAA,EASH;QACI,IAAI,IATC,CAAI,GAAC,CAAG,IAAC,CAAI,EAAE;YAUlB,uBATM,CAAA,GAAI,IAAA,CAAK,MAAC,CAAM,IAAC,CAAI,CAAC;YAU5B,OATO,KAAA,CAAM,OAAC,CAAO,CAAC,CAAC,GAAG,CAAA,GAAI,CAAA,CAAE,CAAC,CAAC;SAUnC;QAED,OATO,EAAA,CAAG;KAUX;;;;IAIH,IAXG,IAAA,GAWH,EAXyB,OAAO,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,MAAC,CAAM,CAAC,EAAC;CAYzD;AAED,AAKA;;;;;;;AAOA,AAAA,SAAA,iBAAA,CAlBC,MAAA,EAkBD;IACE,OAlBO,IAAI,WAAA,CAAY,MAAC,CAAM,CAAC;CAmBhC;AAED,MAlBM,0BAAA,GAA6B,4BAAA,CAA6B;;;;;AAuBhE,AAAA,SAAA,wBAAA,CArBC,OAAA,EAqBD;IACE,uBArBM,KAAA,GAAQ,KAAA,CAAM,4BAAC,GAA8B,OAAA,CAAQ,CAAC;IAsB5D,EAAoB,KArBV,GAAK,0BAAC,CAA0B,GAAG,IAAA,CAAK;IAsBlD,OArBO,KAAA,CAAM;CAsBd;;;;;AAKD,AAAA,SAAA,0BAAA,CAxBC,KAAA,EAwBD;IACE,OAxBO,EAAA,KAAU,GAAK,0BAAC,CAA0B,CAAC;CAyBnD;;;;;;;AAOD,AAAA,SAAA,iBAAA,CACI,QAAsB,EAAE,YAA6B,EAAE,KAAY,EADvE;IAEE,uBA5BM,KAAA,GAAM,EAAE,KAAA,CAAM,IAAC,GAAM,KAAC,CAAK,GAAC,CAAG,CAAC;IA8BtC,IAAI,KA5BC,CAAK,MAAC,GAAQ,QAAA,CAAS,MAAC,EAAO;;QA8BlC,OA5BO,IAAA,CAAK;KA6Bb;IAED,IAAI,KA5BC,CAAK,SAAC,KAAa,MAAA;SA6BnB,YA5BC,CAAY,WAAC,EAAW,IAAK,KAAA,CAAM,MAAC,GAAQ,QAAA,CAAS,MAAC,CAAM,EAAE;;QA8BlE,OA5BO,IAAA,CAAK;KA6Bb;IAED,uBA5BM,SAAA,GAAyC,EAAA,CAAG;;IA+BlD,KAAK,qBA5BI,KAAA,GAAQ,CAAA,EAAG,KAAA,GAAQ,KAAA,CAAM,MAAC,EAAO,KAAA,EAAM,EAAG;QA6BjD,uBA5BM,IAAA,GAAO,KAAA,CAAM,KAAC,CAAK,CAAC;QA6B1B,uBA5BM,OAAA,GAAU,QAAA,CAAS,KAAC,CAAK,CAAC;QA6BhC,uBA5BM,WAAA,GAAc,IAAA,CAAK,UAAC,CAAU,GAAC,CAAG,CAAC;QA6BzC,IAAI,WA5BC,EAAY;YA6Bf,SAAS,CA5BC,IAAC,CAAI,SAAC,CAAS,CAAC,CAAC,CAAC,GAAG,OAAA,CAAQ;SA6BxC;aA5BM,IAAA,IAAK,KAAQ,OAAA,CAAQ,IAAC,EAAK;;YA8BhC,OA5BO,IAAA,CAAK;SA6Bb;KACF;IAED,OA5BO,EAAA,QAAE,EAAS,QAAA,CAAS,KAAC,CAAK,CAAC,EAAE,KAAA,CAAM,MAAC,CAAM,EAAE,SAAA,EAAU,CAAC;CA6B/D;;AD1KD;;;;;;;AAWA,AAkWA,AAAA,MAAA,kBAAA,CAAA;;;;;IAKA,WAAA,CAJqB,MAAc,EAAU,MAAwB,EAIrE;QAJqB,IAArB,CAAA,MAAqB,GAAA,MAAA,CAAc;QAAU,IAA7C,CAAA,MAA6C,GAAA,MAAA,CAAwB;KAAC;CAMrE;AAED,AAOA;;;;;AAKA,AAAA,SAAA,cAAA,CAjBC,MAAA,EAAA,UAiBD,GAjBC,EAAA,EAiBD;;IAEE,KAAK,qBAjBI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,MAAA,CAAO,MAAC,EAAO,CAAA,EAAE,EAAG;QAkBtC,uBAjBM,KAAA,GAAe,MAAA,CAAO,CAAC,CAAC,CAAC;QAkB/B,uBAjBM,QAAA,GAAmB,WAAA,CAAY,UAAC,EAAW,KAAA,CAAM,CAAC;QAkBxD,YAAY,CAjBC,KAAC,EAAM,QAAA,CAAS,CAAC;KAkB/B;CACF;;;;;;AAMD,SAAA,YAAA,CArBC,KAAA,EAAA,QAAA,EAqBD;IACE,IAAI,CArBC,KAAC,EAAM;QAsBV,MArBM,IAAI,KAAA,CAAM,CAqBpB;sCACA,EAAwC,QArBC,CAqBzC;;;;;;;;;IASA,CAAK,CArBC,CAAC;KAsBJ;IACD,IAAI,KArBC,CAAK,OAAC,CAAO,KAAC,CAAK,EAAE;QAsBxB,MArBM,IAAI,KAAA,CAAM,CAqBpB,gCAAA,EArBqB,QAAmC,CAqBxD,4BAAA,CArBgE,CAA8B,CAAC;KAsB5F;IACD,IAAI,CArBC,KAAC,CAAK,SAAC,KAAY,KAAE,CAAK,MAAC,IAAS,KAAA,CAAM,MAAC,KAAU,cAAA,CAAe,EAAE;QAsBzE,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,uDAAA,CApBoD,CAAyD,CAAC;KAsB3G;IACD,IAAI,KArBC,CAAK,UAAC,IAAa,KAAA,CAAM,QAAC,EAAS;QAsBtC,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,kDAAA,CApBoD,CAAoD,CAAC;KAsBtG;IACD,IAAI,KArBC,CAAK,UAAC,IAAa,KAAA,CAAM,YAAC,EAAa;QAsB1C,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,sDAAA,CApBoD,CAAwD,CAAC;KAsB1G;IACD,IAAI,KArBC,CAAK,QAAC,IAAW,KAAA,CAAM,YAAC,EAAa;QAsBxC,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,oDAAA,CApBoD,CAAsD,CAAC;KAsBxG;IACD,IAAI,KArBC,CAAK,UAAC,IAAa,KAAA,CAAM,SAAC,EAAU;QAsBvC,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,mDAAA,CApBoD,CAAqD,CAAC;KAsBvG;IACD,IAAI,KArBC,CAAK,IAAC,IAAO,KAAA,CAAM,OAAC,EAAQ;QAsB/B,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,2CAAA,CApBoD,CAA6C,CAAC;KAsB/F;IACD,IAAI,KArBC,CAAK,UAAC,KAAc,KAAK,CAAA,IAAK,CAAA,KAAE,CAAK,SAAC,IAAY,CAAA,KAAE,CAAK,QAAC,IAAW,CAAA,KAAE,CAAK,YAAC,EAAa;QAsB7F,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,yFAAA,CApBoD,CAA2F,CAAC;KAsB7I;IACD,IAAI,KArBC,CAAK,IAAC,KAAQ,KAAK,CAAA,IAAK,KAAA,CAAM,OAAC,KAAW,KAAK,CAAA,EAAG;QAsBrD,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,wDAAA,CApBoD,CAA0D,CAAC;KAsB5G;IACD,IAAI,OArBO,KAAA,CAAM,IAAC,KAAQ,QAAA,IAAY,KAAA,CAAM,IAAC,CAAI,MAAC,CAAM,CAAC,CAAC,KAAK,GAAA,EAAK;QAsBlE,MArBM,IAAI,KAAA,CAAM,CAqBpB,gCAAA,EArBqB,QAAmC,CAqBxD,iCAAA,CArBgE,CAAmC,CAAC;KAsBjG;IACD,IAAI,KArBC,CAAK,IAAC,KAAQ,EAAA,IAAM,KAAA,CAAM,UAAC,KAAc,KAAK,CAAA,IAAK,KAAA,CAAM,SAAC,KAAa,KAAK,CAAA,EAAG;QAsBlF,uBArBM,GAAA,GAsBF,CADR,oFAAA,CAC8F,CArBC;QAsB3F,MArBM,IAAI,KAAA,CAsBN,CADR,wCAAA,EACmD,QArBC,CAoBpD,gBAAA,EApB4D,KAAmB,CAAK,UAAC,CAoBrF,iCAAA,EApB+F,GAAoC,CAoBnI,CApBsI,CAAE,CAAC;KAsBtI;IACD,IAAI,KArBC,CAAK,SAAC,KAAa,KAAK,CAAA,IAAK,KAAA,CAAM,SAAC,KAAa,MAAA,IAAU,KAAA,CAAM,SAAC,KAAa,QAAA,EAAU;QAsB5F,MArBM,IAAI,KAAA,CAsBN,CADR,gCAAA,EAC2C,QArBC,CAoB5C,kDAAA,CApBoD,CAAoD,CAAC;KAsBtG;IACD,IAAI,KArBC,CAAK,QAAC,EAAS;QAsBlB,cAAc,CArBC,KAAC,CAAK,QAAC,EAAS,QAAA,CAAS,CAAC;KAsB1C;CACF;;;;;;AAMD,SAAA,WAAA,CAzBC,UAAA,EAAA,YAAA,EAyBD;IACE,IAAI,CAzBC,YAAC,EAAa;QA0BjB,OAzBO,UAAA,CAAW;KA0BnB;IACD,IAAI,CAzBC,UAAC,IAAa,CAAA,YAAE,CAAY,IAAC,EAAK;QA0BrC,OAzBO,EAAA,CAAG;KA0BX;SAzBM,IAAA,UAAK,IAAa,CAAA,YAAE,CAAY,IAAC,EAAK;QA0B3C,OAzBO,CAyBX,EAzBW,UAAI,CAyBf,CAAA,CAzByB,CAAG;KA0BzB;SAzBM,IAAA,CAAK,UAAC,IAAa,YAAA,CAAa,IAAC,EAAK;QA0B3C,OAzBO,YAAA,CAAa,IAAC,CAAI;KA0B1B;SAzBM;QA0BL,OAzBO,CAyBX,EAzBW,UAAI,CAyBf,CAAA,EAzByB,YAAI,CAAY,IAAC,CAyB1C,CAzB8C,CAAE;KA0B7C;CACF;;ADneD;;;;;;;AASA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA;;;;;AAKA,AAAA,SAAA,kBAAA,CALC,CAAA,EAAA,CAAA,EAKD;IACE,IAAI,CALC,CAAC,MAAC,KAAU,CAAA,CAAE,MAAC;QAAO,OAAO,KAAA,CAAM;IAMxC,KAAK,qBALI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,CAAA,CAAE,MAAC,EAAO,EAAA,CAAG,EAAE;QAMjC,IAAI,CALC,YAAC,CAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC,CAAC;YAAE,OAAO,KAAA,CAAM;KAM7C;IACD,OALO,IAAA,CAAK;CAMb;;;;;;AAMD,AAAA,SAAA,YAAA,CATC,CAAA,EAAA,CAAA,EASD;IACE,uBATM,EAAA,GAAK,MAAA,CAAO,IAAC,CAAI,CAAC,CAAC,CAAC;IAU1B,uBATM,EAAA,GAAK,MAAA,CAAO,IAAC,CAAI,CAAC,CAAC,CAAC;IAU1B,IAAI,EATC,CAAE,MAAC,IAAS,EAAA,CAAG,MAAC,EAAO;QAU1B,OATO,KAAA,CAAM;KAUd;IACD,qBATI,GAAK,CAAO;IAUhB,KAAK,qBATI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,EAAA,CAAG,MAAC,EAAO,CAAA,EAAE,EAAG;QAUlC,GAAG,GATG,EAAA,CAAG,CAAC,CAAC,CAAC;QAUZ,IAAI,CATC,CAAC,GAAC,CAAG,KAAK,CAAA,CAAE,GAAC,CAAG,EAAE;YAUrB,OATO,KAAA,CAAM;SAUd;KACF;IACD,OATO,IAAA,CAAK;CAUb;;;;;;AAMD,AAAA,SAAA,OAAA,CAbC,GAAA,EAaD;IACE,OAbO,KAAA,CAAM,SAAC,CAAS,MAAC,CAAM,KAAC,CAAK,EAAC,EAAG,GAAA,CAAI,CAAC;CAc9C;;;;;;AAMD,AAAA,SAAAM,MAAA,CAjBC,CAAA,EAiBD;IACE,OAjBO,CAAA,CAAE,MAAC,GAAQ,CAAA,GAAI,CAAA,CAAE,CAAC,CAAC,MAAC,GAAQ,CAAA,CAAE,GAAG,IAAA,CAAK;CAkB9C;;;;;AAKD,AAAA,AAEC;;;;;;;AAOD,AAAA,SAAA,OAAA,CAzBCL,MAAA,EAAA,QAAA,EAyBD;IACE,KAAK,uBAzBM,IAAA,IAAQA,MAAA,EAAK;QA0BtB,IAAIA,MAzBC,CAAG,cAAC,CAAc,IAAC,CAAI,EAAE;YA0B5B,QAAQ,CAzBCA,MAAC,CAAG,IAAC,CAAI,EAAE,IAAA,CAAK,CAAC;SA0B3B;KACF;CACF;;;;;;;AAOD,AAAA,SAAA,UAAA,CACI,GAAqB,EAAE,EAAsC,EADjE;IAEE,IAAI,MA9BC,CAAM,IAAC,CAAI,GAAC,CAAG,CAAC,MAAC,KAAU,CAAA,EAAG;QA+BjC,OA9BO,EAAA,CAAG,EAAE,CAAE,CAAA;KA+Bf;IAED,uBA9BM,QAAA,GAA4B,EAAA,CAAG;IA+BrC,uBA9BM,QAAA,GAA4B,EAAA,CAAG;IA+BrC,uBA9BM,GAAA,GAAwB,EAAA,CAAG;IAgCjC,OAAO,CA9BC,GAAC,EAAI,CAAA,CAAI,EAAG,CAAG,KA8BzB;QACI,uBA9BM,MAAA,GAAS,GAAA,CAAI,IAAC,CAAI,EAAC,CAAE,CAAC,EAAE,CAAA,CAAE,EAAE,CAAA,CAAI,KAAM,GAAA,CAAI,CAAC,CAAC,GAAG,CAAA,CAAE,CAAC;QA+BxD,IAAI,CA9BC,KAAK,cAAA,EAAgB;YA+BxB,QAAQ,CA9BC,IAAC,CAAI,MAAC,CAAM,CAAC;SA+BvB;aA9BM;YA+BL,QAAQ,CA9BC,IAAC,CAAI,MAAC,CAAM,CAAC;SA+BvB;KACF,CA9BC,CAAC;IAgCH,uBA9BM,OAAA,GAAU,SAAA,CAAU,IAAC,CAAI,EAAC,CAAE,GAAE,QAAG,EAAS,GAAA,QAAI,CAAQ,CAAC,CAAC;IA+B9D,uBA9BM,KAAA,GAAQQ,IAAG,CAAI,IAAC,CAAI,OAAC,CAAO,CAAC;IA+BnC,OA9BO,GAAA,CAAI,IAAC,CAAI,KAAC,EAAM,MAAM,GAAA,CAAI,CAAC;CA+BnC;;;;;AAKD,AAAA,SAAA,cAAA,CAjCC,WAAA,EAiCD;IACE,uBAjCM,OAAA,GAAU,QAAA,CAAS,IAAC,CAAI,WAAC,CAAW,CAAC;IAkC3C,OAjCO,KAAA,CAAM,IAAC,CAAI,OAAC,EAAQ,CAAA,MAAS,KAAQ,MAAA,KAAW,IAAA,CAAK,CAAC;CAkC9D;;;;;;AAMD,AAAA,SAAA,kBAAA,CArCC,KAAA,EAqCD;IAEE,IAAID,aArCC,CAAY,KAAC,CAAK,EAAE;QAsCvB,OArCO,KAAA,CAAM;KAsCd;IAED,IAAID,UArCC,CAAS,KAAC,CAAK,EAAE;;;;QAyCpB,OArCO,WAAA,CAAY,OAAC,CAAO,OAAC,CAAO,KAAC,CAAK,CAAC,CAAC;KAsC5C;IAED,OArCO,EAAA,CAAG,KAAE,CAAK,CAAC;CAsCnB;;AD/ID;;;;;;;AASA,AACA,AACA;;;AAGA,AAAA,SAAA,kBAAA,GAAA;IACE,OAHO,IAAI,OAAA,CAAQ,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,EAAE,EAAA,EAAI,IAAA,CAAK,CAAC;CAI3D;;;;;;;AAOD,AAAA,SAAA,YAAA,CARC,SAAA,EAAA,SAAA,EAAA,KAAA,EAQD;IACE,IAAI,KARC,EAAM;QAST,OARO,gBAAA,CAAiB,SAAC,CAAS,WAAC,EAAY,SAAA,CAAU,WAAC,CAAW;YASjE,kBAAkB,CARC,SAAC,CAAS,IAAC,EAAK,SAAA,CAAU,IAAC,CAAI,CAAC;KASxD;IAED,OARO,mBAAA,CAAoB,SAAC,CAAS,WAAC,EAAY,SAAA,CAAU,WAAC,CAAW;QASpE,oBAAoB,CARC,SAAC,CAAS,IAAC,EAAK,SAAA,CAAU,IAAC,CAAI,CAAC;CAS1D;;;;;;AAMD,SAAA,gBAAA,CACI,SAAgC,EAAE,SAAgC,EADtE;IAEE,OAZO,YAAA,CAAa,SAAC,EAAU,SAAA,CAAU,CAAC;CAa3C;;;;;;AAMD,SAAA,kBAAA,CAhBC,SAAA,EAAA,SAAA,EAgBD;IACE,IAAI,CAhBC,SAAC,CAAS,SAAC,CAAS,QAAC,EAAS,SAAA,CAAU,QAAC,CAAQ;QAAE,OAAO,KAAA,CAAM;IAiBrE,IAAI,SAhBC,CAAS,gBAAC,KAAoB,SAAA,CAAU,gBAAC;QAAiB,OAAO,KAAA,CAAM;IAiB5E,KAAK,uBAhBM,CAAA,IAAK,SAAA,CAAU,QAAC,EAAS;QAiBlC,IAAI,CAhBC,SAAC,CAAS,QAAC,CAAQ,CAAC,CAAC;YAAE,OAAO,KAAA,CAAM;QAiBzC,IAAI,CAhBC,kBAAC,CAAkB,SAAC,CAAS,QAAC,CAAQ,CAAC,CAAC,EAAE,SAAA,CAAU,QAAC,CAAQ,CAAC,CAAC,CAAC;YAAE,OAAO,KAAA,CAAM;KAiBrF;IACD,OAhBO,IAAA,CAAK;CAiBb;;;;;;AAMD,SAAA,mBAAA,CACI,SAAgC,EAAE,SAAgC,EADtE;IAEE,OApBO,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,CAAC,MAAC,IAAS,MAAA,CAAO,IAAC,CAAI,SAAC,CAAS,CAAC,MAAC;QAqB5D,MAAM,CApBC,IAAC,CAAI,SAAC,CAAS,CAAC,KAAC,CAAK,GAAC,IAAM,SAAA,CAAU,GAAC,CAAG,KAAK,SAAA,CAAU,GAAC,CAAG,CAAC,CAAC;CAqB5E;;;;;;AAMD,SAAA,oBAAA,CAxBC,SAAA,EAAA,SAAA,EAwBD;IACE,OAxBO,0BAAA,CAA2B,SAAC,EAAU,SAAA,EAAW,SAAA,CAAU,QAAC,CAAQ,CAAC;CAyB7E;;;;;;;AAOD,SAAA,0BAAA,CACI,SAA0B,EAAE,SAA0B,EAAE,cAA4B,EADxF;IAEE,IAAI,SA7BC,CAAS,QAAC,CAAQ,MAAC,GAAQ,cAAA,CAAe,MAAC,EAAO;QA8BrD,uBA7BM,OAAA,GAAU,SAAA,CAAU,QAAC,CAAQ,KAAC,CAAK,CAAC,EAAE,cAAA,CAAe,MAAC,CAAM,CAAC;QA8BnE,IAAI,CA7BC,SAAC,CAAS,OAAC,EAAQ,cAAA,CAAe;YAAE,OAAO,KAAA,CAAM;QA8BtD,IAAI,SA7BC,CAAS,WAAC,EAAW;YAAG,OAAO,KAAA,CAAM;QA8B1C,OA7BO,IAAA,CAAK;KA+Bb;SA7BM,IAAA,SAAK,CAAS,QAAC,CAAQ,MAAC,KAAU,cAAA,CAAe,MAAC,EAAO;QA8B9D,IAAI,CA7BC,SAAC,CAAS,SAAC,CAAS,QAAC,EAAS,cAAA,CAAe;YAAE,OAAO,KAAA,CAAM;QA8BjE,KAAK,uBA7BM,CAAA,IAAK,SAAA,CAAU,QAAC,EAAS;YA8BlC,IAAI,CA7BC,SAAC,CAAS,QAAC,CAAQ,CAAC,CAAC;gBAAE,OAAO,KAAA,CAAM;YA8BzC,IAAI,CA7BC,oBAAC,CAAoB,SAAC,CAAS,QAAC,CAAQ,CAAC,CAAC,EAAE,SAAA,CAAU,QAAC,CAAQ,CAAC,CAAC,CAAC;gBAAE,OAAO,KAAA,CAAM;SA8BvF;QACD,OA7BO,IAAA,CAAK;KA+Bb;SA7BM;QA8BL,uBA7BM,OAAA,GAAU,cAAA,CAAe,KAAC,CAAK,CAAC,EAAE,SAAA,CAAU,QAAC,CAAQ,MAAC,CAAM,CAAC;QA8BnE,uBA7BM,IAAA,GAAO,cAAA,CAAe,KAAC,CAAK,SAAC,CAAS,QAAC,CAAQ,MAAC,CAAM,CAAC;QA8B7D,IAAI,CA7BC,SAAC,CAAS,SAAC,CAAS,QAAC,EAAS,OAAA,CAAQ;YAAE,OAAO,KAAA,CAAM;QA8B1D,IAAI,CA7BC,SAAC,CAAS,QAAC,CAAQ,cAAC,CAAc;YAAE,OAAO,KAAA,CAAM;QA8BtD,OA7BO,0BAAA,CAA2B,SAAC,CAAS,QAAC,CAAQ,cAAC,CAAc,EAAE,SAAA,EAAW,IAAA,CAAK,CAAC;KA8BxF;CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BD,AAAA,MAAA,OAAA,CAAA;;;;;;;IAWA,WAAA,CAhCa,IAAM,EAEN,WAAoC,EAEpC,QAAkB,EA4B/B;QAhCa,IAAb,CAAA,IAAa,GAAA,IAAA,CAAM;QAEN,IAAb,CAAA,WAAa,GAAA,WAAA,CAAoC;QAEpC,IAAb,CAAA,QAAa,GAAA,QAAA,CAAkB;KAAI;;;;IAmCnC,IAjCG,aAAA,GAiCH;QACI,IAAI,CAjCC,IAAC,CAAI,cAAC,EAAe;YAkCxB,IAAI,CAjCC,cAAC,GAAgB,iBAAA,CAAkB,IAAC,CAAI,WAAC,CAAW,CAAC;SAkC3D;QACD,OAjCO,IAAA,CAAK,cAAC,CAAc;KAkC5B;;;;;IAKH,QAnCG,GAmCH,EAnCuB,OAAO,kBAAA,CAAmB,SAAC,CAAS,IAAC,CAAI,CAAC,EAAC;CAoCjE;AAED,AAuBA;;;;;;;AAOA,AAAA,MAAA,eAAA,CAAA;;;;;IAiBA,WAAA,CAjEa,QAAqB,EAErB,QAA0C,EA+DvD;QAjEa,IAAb,CAAA,QAAa,GAAA,QAAA,CAAqB;QAErB,IAAb,CAAA,QAAa,GAAA,QAAA,CAA0C;;;;QA0DvD,IAAA,CAAA,MAhEG,GAAA,IAAA,CAAA;QAwEC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAM,EAAE,CAAM,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;KACxD;;;;;IAKH,WAnEG,GAmEH,EAnE2B,OAAO,IAAA,CAAK,gBAAC,GAAkB,CAAA,CAAE,EAAC;;;;;IAwE7D,IArEG,gBAAA,GAqEH,EArEmC,OAAO,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,QAAC,CAAQ,CAAC,MAAC,CAAM,EAAC;;;;;IA0E7E,QAvEG,GAuEH,EAvEuB,OAAO,cAAA,CAAe,IAAC,CAAI,CAAC,EAAC;CAwEnD;AAED,AA4BA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,AAAA,MAAA,UAAA,CAAA;;;;;IASA,WAAA,CArGa,IAAM,EAGN,UAAoC,EAkGjD;QArGa,IAAb,CAAA,IAAa,GAAA,IAAA,CAAM;QAGN,IAAb,CAAA,UAAa,GAAA,UAAA,CAAoC;KAAC;;;;IAwGlD,IAtGG,YAAA,GAsGH;QACI,IAAI,CAtGC,IAAC,CAAI,aAAC,EAAc;YAuGvB,IAAI,CAtGC,aAAC,GAAe,iBAAA,CAAkB,IAAC,CAAI,UAAC,CAAU,CAAC;SAuGzD;QACD,OAtGO,IAAA,CAAK,aAAC,CAAa;KAuG3B;;;;;IAKH,QAxGG,GAwGH,EAxGuB,OAAO,aAAA,CAAc,IAAC,CAAI,CAAC,EAAC;CAyGlD;AAED,AAkBA;;;;;AAKA,AAAA,SAAA,aAAA,CA/HC,EAAA,EAAA,EAAA,EA+HD;IACE,OA/HO,SAAA,CAAU,EAAC,EAAG,EAAA,CAAG,IAAI,EAAA,CAAG,KAAC,CAAK,CAAC,CAAC,EAAE,CAAA,KAAM,YAAA,CAAa,CAAC,CAAC,UAAC,EAAW,EAAA,CAAG,CAAC,CAAC,CAAC,UAAC,CAAU,CAAC,CAAC;CAgI9F;;;;;;AAMD,AAAA,SAAA,SAAA,CAnIC,EAAA,EAAA,EAAA,EAmID;IACE,IAAI,EAnIC,CAAE,MAAC,KAAU,EAAA,CAAG,MAAC;QAAO,OAAO,KAAA,CAAM;IAoI1C,OAnIO,EAAA,CAAG,KAAC,CAAK,CAAC,CAAC,EAAE,CAAA,KAAM,CAAA,CAAE,IAAC,KAAQ,EAAA,CAAG,CAAC,CAAC,CAAC,IAAC,CAAI,CAAC;CAoIlD;;;;;;;AAOD,AAAA,SAAA,oBAAA,CACI,OAAwB,EAAE,EAA0C,EADxE;IAEE,qBAxII,GAAA,GAAW,EAAA,CAAG;IAyIlB,OAAO,CAxIC,OAAC,CAAO,QAAC,EAAS,CAAA,KAAQ,EAAiB,WAAa,KAwIlE;QACI,IAAI,WAxIC,KAAe,cAAA,EAAgB;YAyIlC,GAAG,GAxIG,GAAA,CAAI,MAAC,CAAM,EAAC,CAAE,KAAC,EAAM,WAAA,CAAY,CAAC,CAAC;SAyI1C;KACF,CAxIC,CAAC;IAyIH,OAAO,CAxIC,OAAC,CAAO,QAAC,EAAS,CAAA,KAAQ,EAAiB,WAAa,KAwIlE;QACI,IAAI,WAxIC,KAAe,cAAA,EAAgB;YAyIlC,GAAG,GAxIG,GAAA,CAAI,MAAC,CAAM,EAAC,CAAE,KAAC,EAAM,WAAA,CAAY,CAAC,CAAC;SAyI1C;KACF,CAxIC,CAAC;IAyIH,OAxIO,GAAA,CAAI;CAyIZ;;;;;;;;;;;;AAYD,AAAA,MAAA,aAAA,CAAA;;;;;;;IAOA,KA5IY,CAAA,GAAA,EA4IZ,GA5IY;;;;;;;IAmJZ,SAhJY,CAAA,IAAA,EAgJZ,GAhJY;CAiJX;;;;;;;;;;;;;;;;;;;AAmBD,AAAA,MAAA,oBAAA,CAAA;;;;;;IAMA,KAnJG,CAAA,GAAA,EAmJH;QACI,uBAnJM,CAAA,GAAI,IAAI,SAAA,CAAU,GAAC,CAAG,CAAC;QAoJ7B,OAnJO,IAAI,OAAA,CAAQ,CAAC,CAAC,gBAAC,EAAgB,EAAG,CAAA,CAAE,gBAAC,EAAgB,EAAG,CAAA,CAAE,aAAC,EAAa,CAAE,CAAC;KAoJnF;;;;;;IAMH,SAtJG,CAAA,IAAA,EAsJH;QACI,uBAtJM,OAAA,GAAU,CAsJpB,CAAA,EAtJoB,gBAAK,CAAgB,IAAC,CAAI,IAAC,EAAK,IAAA,CAAK,CAsJzD,CAtJ0D,CAAE;QAuJxD,uBAtJM,KAAA,GAAQ,oBAAA,CAAqB,IAAC,CAAI,WAAC,CAAW,CAAC;QAuJrD,uBAtJM,QAAA,GAAW,OAAO,IAAA,CAAK,QAAC,KAAY,CAsJ9C,MAAA,CAtJ8C,GAAW,CAsJzD,CAAA,EAtJyD,SAAK,oBAAS,IAAC,CAAI,QAAC,GAAU,CAsJvF,CAtJwF,GAAI,EAAA,CAAG;QAwJ3F,OAtJO,CAsJX,EAtJW,OAAI,CAsJf,EAtJsB,KAAG,CAsJzB,EAtJ8B,QAAG,CAsJjC,CAtJyC,CAAE;KAuJxC;CACF;AAED,MAtJM,kBAAA,GAAqB,IAAI,oBAAA,EAAqB,CAAE;;;;;AA2JtD,AAAA,SAAA,cAAA,CAzJC,OAAA,EAyJD;IACE,OAzJO,OAAA,CAAQ,QAAC,CAAQ,GAAC,CAAG,CAAC,IAAI,aAAA,CAAc,CAAC,CAAC,CAAC,CAAC,IAAC,CAAI,GAAC,CAAG,CAAC;CA0J9D;;;;;;AAMD,SAAA,gBAAA,CA7JC,OAAA,EAAA,IAAA,EA6JD;IACE,IAAI,CA7JC,OAAC,CAAO,WAAC,EAAW,EAAG;QA8J1B,OA7JO,cAAA,CAAe,OAAC,CAAO,CAAC;KA8JhC;IAED,IAAI,IA7JC,EAAK;QA8JR,uBA7JM,OAAA,GAAU,OAAA,CAAQ,QAAC,CAAQ,cAAC,CAAc;YA8J5C,gBAAgB,CA7JC,OAAC,CAAO,QAAC,CAAQ,cAAC,CAAc,EAAE,KAAA,CAAM;YA8JzD,EAAE,CA7JC;QA8JP,uBA7JM,QAAA,GAAqB,EAAA,CAAG;QA+J9B,OAAO,CA7JC,OAAC,CAAO,QAAC,EAAS,CAAA,CAAI,EAAiB,CAAG,KA6JtD;YACM,IAAI,CA7JC,KAAK,cAAA,EAAgB;gBA8JxB,QAAQ,CA7JC,IAAC,CAAI,CA6JtB,EA7JuB,CAAG,CA6J1B,CAAA,EA7J2B,gBAAI,CAAgB,CAAC,EAAE,KAAA,CAAM,CA6JxD,CA7JyD,CAAE,CAAC;aA8JrD;SACF,CA7JC,CAAC;QA+JH,OA7JO,QAAA,CAAS,MAAC,GAAQ,CAAA,GAAI,CA6JjC,EA7JiC,OAAI,CA6JrC,CAAA,EA7J4C,QAAI,CAAQ,IAAC,CAAI,IAAC,CAAI,CA6JlE,CAAA,CA7JmE,GAAK,OAAA,CAAQ;KA+J7E;SA7JM;QA8JL,uBA7JM,QAAA,GAAW,oBAAA,CAAqB,OAAC,EAAQ,CAAA,CAAI,EAAiB,CAAG,KA6J3E;YACM,IAAI,CA7JC,KAAK,cAAA,EAAgB;gBA8JxB,OA7JO,CAAA,gBAAE,CAAgB,OAAC,CAAO,QAAC,CAAQ,cAAC,CAAc,EAAE,KAAA,CAAM,CAAC,CAAC;aA8JpE;YAED,OA7JO,CAAA,CA6Jb,EA7Je,CAAG,CA6JlB,CAAA,EA7JmB,gBAAI,CAAgB,CAAC,EAAE,KAAA,CAAM,CA6JhD,CA7JiD,CAAE,CAAC;SA+J/C,CA7JC,CAAC;QA+JH,OA7JO,CA6JX,EA7JW,cAAI,CAAc,OAAC,CAAO,CA6JrC,EAAA,EA7JsC,QAAK,CAAQ,IAAC,CAAI,IAAC,CAAI,CA6J7D,CAAA,CA7J8D,CAAG;KA8J9D;CACF;;;;;AAKD,AAAA,SAAA,MAAA,CAhKC,CAAA,EAgKD;IACE,OAhKO,kBAAA,CAAmB,CAAC,CAAC,CAAC;CAiK9B;;;;;AAKD,AAAA,SAAA,MAAA,CAnKC,CAAA,EAmKD;IACE,OAnKO,kBAAA,CAAmB,CAAC,CAAC,CAAC;CAoK9B;;;;;AAKD,AAAA,SAAA,aAAA,CAtKC,IAAA,EAsKD;IACE,OAtKO,CAsKT,EAtKS,MAAI,CAAM,IAAC,CAAI,IAAC,CAAI,CAsK7B,EAtK8B,eAAG,CAAe,IAAC,CAAI,UAAC,CAAU,CAsKhE,CAtKiE,CAAE;CAuKlE;;;;;AAKD,SAAA,eAAA,CAzKC,MAAA,EAyKD;IACE,OAzKO,MAAA,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC,GAAC,CAAG,GAAC,IAAM,CAyKxC,CAAA,EAzKwC,MAAK,CAAM,GAAC,CAAG,CAyKvD,CAAA,EAzKwD,MAAI,CAAM,MAAC,CAAM,GAAC,CAAG,CAAC,CAyK9E,CAzK+E,CAAE,CAAC,IAAC,CAAI,EAAC,CAAE,CAAC;CA0K1F;;;;;AAKD,SAAA,oBAAA,CA5KC,MAAA,EA4KD;IACE,uBA5KM,SAAA,GAAsB,MAAA,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC,GAAC,CAAG,CAAC,IAAC,KA4KxD;QACI,uBA5KM,KAAA,GAAQ,MAAA,CAAO,IAAC,CAAI,CAAC;QA6K3B,OA5KO,KAAA,CAAM,OAAC,CAAO,KAAC,CAAK,GAAG,KAAA,CAAM,GAAC,CAAG,CAAC,IAAI,CA4KjD,EA5KiD,MAAI,CAAM,IAAC,CAAI,CA4KhE,CAAA,EA5KiE,MAAI,CAAM,CAAC,CAAC,CA4K7E,CA5K8E,CAAE,CAAC,IAAC,CAAI,GAAC,CAAG;YA6KxD,CAAlC,EAAqC,MA5KC,CAAM,IAAC,CAAI,CA4KjD,CAAA,EA5KkD,MAAI,CAAM,KAAC,CAAK,CA4KlE,CA5KmE,CAAE;KA6KlE,CA5KC,CAAC;IA8KH,OA5KO,SAAA,CAAU,MAAC,GAAQ,CA4K5B,CAAA,EA5K4B,SAAK,CAAS,IAAC,CAAI,GAAC,CAAG,CA4KnD,CA5KoD,GAAI,EAAA,CAAG;CA6K1D;AAED,MA5KM,UAAA,GAAa,gBAAA,CAAiB;;;;;AAiLpC,SAAA,aAAA,CAhLC,GAAA,EAgLD;IACE,uBAhLM,KAAA,GAAQ,GAAA,CAAI,KAAC,CAAK,UAAC,CAAU,CAAC;IAiLpC,OAhLO,KAAA,GAAQ,KAAA,CAAM,CAAC,CAAC,GAAG,EAAA,CAAG;CAiL9B;AAED,MAhLM,cAAA,GAAiB,WAAA,CAAY;;;;;AAqLnC,SAAA,gBAAA,CAnLC,GAAA,EAmLD;IACE,uBAnLM,KAAA,GAAQ,GAAA,CAAI,KAAC,CAAK,cAAC,CAAc,CAAC;IAoLxC,OAnLO,KAAA,GAAQ,KAAA,CAAM,CAAC,CAAC,GAAG,EAAA,CAAG;CAoL9B;AAED,MAnLM,oBAAA,GAAuB,UAAA,CAAW;;;;;AAwLxC,SAAA,uBAAA,CAtLC,GAAA,EAsLD;IACE,uBAtLM,KAAA,GAAQ,GAAA,CAAI,KAAC,CAAK,oBAAC,CAAoB,CAAC;IAuL9C,OAtLO,KAAA,GAAQ,KAAA,CAAM,CAAC,CAAC,GAAG,EAAA,CAAG;CAuL9B;AACD,MAAA,SAAA,CAAA;;;;IAKA,WAAA,CAvLsB,GAAK,EAuL3B;QAvLsB,IAAtB,CAAA,GAAsB,GAAA,GAAA,CAAK;QAAO,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA;KAAA;;;;IA2LlC,gBAzLG,GAyLH;QACI,IAAI,CAzLC,eAAC,CAAe,GAAC,CAAG,CAAC;QA2L1B,IAAI,IAzLC,CAAI,SAAC,KAAa,EAAA,IAAM,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,IAAI,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,EAAE;YA0LjF,OAzLO,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;SA0LpC;;QAGD,OAzLO,IAAI,eAAA,CAAgB,EAAC,EAAG,IAAA,CAAK,aAAC,EAAa,CAAE,CAAC;KA0LtD;;;;IAIH,gBA3LG,GA2LH;QACI,uBA3LM,MAAA,GAA+B,EAAA,CAAG;QA4LxC,IAAI,IA3LC,CAAI,eAAC,CAAe,GAAC,CAAG,EAAE;YA4L7B,GA3LG;gBA4LD,IAAI,CA3LC,eAAC,CAAe,MAAC,CAAM,CAAC;aA4L9B,QA3LC,IAAQ,CAAI,eAAC,CAAe,GAAC,CAAG,EAAC;SA4LpC;QACD,OA3LO,MAAA,CAAO;KA4Lf;;;;IAIH,aA7LG,GA6LH;QACI,OA7LO,IAAA,CAAK,eAAC,CAAe,GAAC,CAAG,GAAG,SAAA,CAAU,IAAC,CAAI,SAAC,CAAS,GAAG,IAAA,CAAK;KA8LrE;;;;IA3LA,aAAA,GAAH;QAgMI,IAAI,IA/LC,CAAI,SAAC,KAAa,EAAA,EAAI;YAgMzB,OA/LO,EAAA,CAAG;SAgMX;QAED,IAAI,CA/LC,eAAC,CAAe,GAAC,CAAG,CAAC;QAiM1B,uBA/LM,QAAA,GAAyB,EAAA,CAAG;QAgMlC,IAAI,CA/LC,IAAC,CAAI,cAAC,CAAc,GAAC,CAAG,EAAE;YAgM7B,QAAQ,CA/LC,IAAC,CAAI,IAAC,CAAI,YAAC,EAAY,CAAE,CAAC;SAgMpC;QAED,OAAO,IA/LC,CAAI,cAAC,CAAc,GAAC,CAAG,IAAI,CAAA,IAAE,CAAI,cAAC,CAAc,IAAC,CAAI,IAAI,CAAA,IAAE,CAAI,cAAC,CAAc,IAAC,CAAI,EAAE;YAgM3F,IAAI,CA/LC,OAAC,CAAO,GAAC,CAAG,CAAC;YAgMlB,QAAQ,CA/LC,IAAC,CAAI,IAAC,CAAI,YAAC,EAAY,CAAE,CAAC;SAgMpC;QAED,qBA/LI,QAAA,GAAgD,EAAA,CAAG;QAgMvD,IAAI,IA/LC,CAAI,cAAC,CAAc,IAAC,CAAI,EAAE;YAgM7B,IAAI,CA/LC,OAAC,CAAO,GAAC,CAAG,CAAC;YAgMlB,QAAQ,GA/LG,IAAA,CAAK,WAAC,CAAW,IAAC,CAAI,CAAC;SAgMnC;QAED,qBA/LI,GAAA,GAA2C,EAAA,CAAG;QAgMlD,IAAI,IA/LC,CAAI,cAAC,CAAc,GAAC,CAAG,EAAE;YAgM5B,GAAG,GA/LG,IAAA,CAAK,WAAC,CAAW,KAAC,CAAK,CAAC;SAgM/B;QAED,IAAI,QA/LC,CAAQ,MAAC,GAAQ,CAAA,IAAK,MAAA,CAAO,IAAC,CAAI,QAAC,CAAQ,CAAC,MAAC,GAAQ,CAAA,EAAG;YAgM3D,GAAG,CA/LC,cAAC,CAAc,GAAG,IAAI,eAAA,CAAgB,QAAC,EAAS,QAAA,CAAS,CAAC;SAgM/D;QAED,OA/LO,GAAA,CAAI;KAgMZ;;;;IA3LA,YAAA,GAAH;QAgMI,uBA/LM,IAAA,GAAO,aAAA,CAAc,IAAC,CAAI,SAAC,CAAS,CAAC;QAgM3C,IAAI,IA/LC,KAAQ,EAAA,IAAM,IAAA,CAAK,cAAC,CAAc,GAAC,CAAG,EAAE;YAgM3C,MA/LM,IAAI,KAAA,CAAM,CA+LtB,gDAAA,EA/LuB,IAAmD,CAAI,SAAC,CA+L/E,EAAA,CA/LwF,CAAI,CAAC;SAgMxF;QAED,IAAI,CA/LC,OAAC,CAAO,IAAC,CAAI,CAAC;QAgMnB,OA/LO,IAAI,UAAA,CAAW,MAAC,CAAM,IAAC,CAAI,EAAE,IAAA,CAAK,iBAAC,EAAiB,CAAE,CAAC;KAgM/D;;;;IA7LA,iBAAA,GAAH;QAkMI,uBAjMM,MAAA,GAA+B,EAAA,CAAG;QAkMxC,OAAO,IAjMC,CAAI,eAAC,CAAe,GAAC,CAAG,EAAE;YAkMhC,IAAI,CAjMC,UAAC,CAAU,MAAC,CAAM,CAAC;SAkMzB;QACD,OAjMO,MAAA,CAAO;KAkMf;;;;;IA/LA,UAAA,CAAA,MAAA,EAAH;QAqMI,uBApMM,GAAA,GAAM,aAAA,CAAc,IAAC,CAAI,SAAC,CAAS,CAAC;QAqM1C,IAAI,CApMC,GAAC,EAAI;YAqMR,OAAO;SACR;QACD,IAAI,CApMC,OAAC,CAAO,GAAC,CAAG,CAAC;QAqMlB,qBApMI,KAAA,GAAa,EAAA,CAAG;QAqMpB,IAAI,IApMC,CAAI,eAAC,CAAe,GAAC,CAAG,EAAE;YAqM7B,uBApMM,UAAA,GAAa,aAAA,CAAc,IAAC,CAAI,SAAC,CAAS,CAAC;YAqMjD,IAAI,UApMC,EAAW;gBAqMd,KAAK,GApMG,UAAA,CAAW;gBAqMnB,IAAI,CApMC,OAAC,CAAO,KAAC,CAAK,CAAC;aAqMrB;SACF;QAED,MAAM,CApMC,MAAC,CAAM,GAAC,CAAG,CAAC,GAAG,MAAA,CAAO,KAAC,CAAK,CAAC;KAqMrC;;;;;IAjMA,eAAA,CAAA,MAAA,EAAH;QAuMI,uBAtMM,GAAA,GAAM,gBAAA,CAAiB,IAAC,CAAI,SAAC,CAAS,CAAC;QAuM7C,IAAI,CAtMC,GAAC,EAAI;YAuMR,OAAO;SACR;QACD,IAAI,CAtMC,OAAC,CAAO,GAAC,CAAG,CAAC;QAuMlB,qBAtMI,KAAA,GAAa,EAAA,CAAG;QAuMpB,IAAI,IAtMC,CAAI,eAAC,CAAe,GAAC,CAAG,EAAE;YAuM7B,uBAtMM,UAAA,GAAa,uBAAA,CAAwB,IAAC,CAAI,SAAC,CAAS,CAAC;YAuM3D,IAAI,UAtMC,EAAW;gBAuMd,KAAK,GAtMG,UAAA,CAAW;gBAuMnB,IAAI,CAtMC,OAAC,CAAO,KAAC,CAAK,CAAC;aAuMrB;SACF;QAED,uBAtMM,UAAA,GAAa,MAAA,CAAO,GAAC,CAAG,CAAC;QAuM/B,uBAtMM,UAAA,GAAa,MAAA,CAAO,KAAC,CAAK,CAAC;QAwMjC,IAAI,MAtMC,CAAM,cAAC,CAAc,UAAC,CAAU,EAAE;;YAwMrC,qBAtMI,UAAA,GAAa,MAAA,CAAO,UAAC,CAAU,CAAC;YAuMpC,IAAI,CAtMC,KAAC,CAAK,OAAC,CAAO,UAAC,CAAU,EAAE;gBAuM9B,UAAU,GAtMG,CAAA,UAAE,CAAU,CAAC;gBAuM1B,MAAM,CAtMC,UAAC,CAAU,GAAG,UAAA,CAAW;aAuMjC;YACD,UAAU,CAtMC,IAAC,CAAI,UAAC,CAAU,CAAC;SAuM7B;aAtMM;;YAwML,MAAM,CAtMC,UAAC,CAAU,GAAG,UAAA,CAAW;SAuMjC;KACF;;;;;IAnMA,WAAA,CAAA,YAAA,EAAH;QAyMI,uBAxMM,QAAA,GAA6C,EAAA,CAAG;QAyMtD,IAAI,CAxMC,OAAC,CAAO,GAAC,CAAG,CAAC;QA0MlB,OAAO,CAxMC,IAAC,CAAI,eAAC,CAAe,GAAC,CAAG,IAAI,IAAA,CAAK,SAAC,CAAS,MAAC,GAAQ,CAAA,EAAG;YAyM9D,uBAxMM,IAAA,GAAO,aAAA,CAAc,IAAC,CAAI,SAAC,CAAS,CAAC;YA0M3C,uBAxMM,IAAA,GAAO,IAAA,CAAK,SAAC,CAAS,IAAC,CAAI,MAAC,CAAM,CAAC;;;YA4MzC,IAAI,IAxMC,KAAQ,GAAA,IAAO,IAAA,KAAS,GAAA,IAAO,IAAA,KAAS,GAAA,EAAK;gBAyMhD,MAxMM,IAAI,KAAA,CAAM,CAwMxB,kBAAA,EAxMyB,IAAqB,CAAI,GAAC,CAwMnD,CAAA,CAxMsD,CAAG,CAAC;aAyMnD;YAED,qBAxMI,UAAA,KAAqB,SAAA,EAAA,CAAY;YAyMrC,IAAI,IAxMC,CAAI,OAAC,CAAO,GAAC,CAAG,GAAG,CAAA,CAAE,EAAE;gBAyM1B,UAAU,GAxMG,IAAA,CAAK,MAAC,CAAM,CAAC,EAAE,IAAA,CAAK,OAAC,CAAO,GAAC,CAAG,CAAC,CAAC;gBAyM/C,IAAI,CAxMC,OAAC,CAAO,UAAC,CAAU,CAAC;gBAyMzB,IAAI,CAxMC,OAAC,CAAO,GAAC,CAAG,CAAC;aAyMnB;iBAxMM,IAAA,YAAK,EAAa;gBAyMvB,UAAU,GAxMG,cAAA,CAAe;aAyM7B;YAED,uBAxMM,QAAA,GAAW,IAAA,CAAK,aAAC,EAAa,CAAE;YAyMtC,QAAQ,CAxMC,UAAC,CAAU,GAAG,MAAA,CAAO,IAAC,CAAI,QAAC,CAAQ,CAAC,MAAC,KAAU,CAAA,GAAI,QAAA,CAAS,cAAC,CAAc;gBAyMxB,IAxMI,eAAA,CAAgB,EAAC,EAAG,QAAA,CAAS,CAAC;YAyM9F,IAAI,CAxMC,eAAC,CAAe,IAAC,CAAI,CAAC;SAyM5B;QAED,OAxMO,QAAA,CAAS;KAyMjB;;;;;IAtMA,cAAA,CAAA,GAAA,EAAH,EAAiD,OAAO,IAAA,CAAK,SAAC,CAAS,UAAC,CAAU,GAAC,CAAG,CAAC,EAAC;;;;;IAGrF,eAAA,CAAA,GAAA,EAAH;QA8MI,IAAI,IA7MC,CAAI,cAAC,CAAc,GAAC,CAAG,EAAE;YA8M5B,IAAI,CA7MC,SAAC,GAAW,IAAA,CAAK,SAAC,CAAS,SAAC,CAAS,GAAC,CAAG,MAAC,CAAM,CAAC;YA8MtD,OA7MO,IAAA,CAAK;SA8Mb;QACD,OA7MO,KAAA,CAAM;KA8Md;;;;;IA3MA,OAAA,CAAA,GAAA,EAAH;QAiNI,IAAI,CAhNC,IAAC,CAAI,eAAC,CAAe,GAAC,CAAG,EAAE;YAiN9B,MAhNM,IAAI,KAAA,CAAM,CAgNtB,UAAA,EAhNuB,GAAa,CAgNpC,EAAA,CAhNuC,CAAI,CAAC;SAiNvC;KACF;CACF,AAED,AAKC;;ADtwBD;;;;;;;AASA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAEA,AACA,AACA,AACA,MAAA,OAAA,CAAA;;;;IAKA,WAAA,CAFG,YAA0B,EAE7B,EAF6C,IAAA,CAAA,YAAA,GAAA,YAAA,IAAA,IAAA,CAAA,EAAA;CAG5C;AAED,AAKA,MAAA,gBAAA,CAAA;;;;IAIA,WAAA,CAVqB,OAAS,EAU9B;QAVqB,IAArB,CAAA,OAAqB,GAAA,OAAA,CAAS;KAAQ;CAWrC;AAED,AAKA;;;;AAIA,SAAA,OAAA,CAnBC,YAAA,EAmBD;IACE,OAnBO,IAAI,UAAA,CAoBP,CAAC,GAnB8B,KAAK,GAAA,CAAI,KAAC,CAAK,IAAI,OAAA,CAAQ,YAAC,CAAY,CAAC,CAAC,CAAC;CAoB/E;;;;;AAKD,SAAA,gBAAA,CAtBC,OAAA,EAsBD;IACE,OAtBO,IAAI,UAAA,CAuBP,CAAC,GAtB8B,KAAK,GAAA,CAAI,KAAC,CAAK,IAAI,gBAAA,CAAiB,OAAC,CAAO,CAAC,CAAC,CAAC;CAuBnF;;;;;AAKD,SAAA,oBAAA,CAzBC,UAAA,EAyBD;IACE,OAzBO,IAAI,UAAA,CA0BP,CAAC,GAzB8B,KAAK,GAAA,CAAI,KAAC,CAAK,IAAI,KAAA,CA0B9C,CAFV,6DAAA,EAE0E,UAzBC,CAuB3E,CAAA,CAvBqF,CAAG,CAAC,CAAC,CAAC;CA0B1F;;;;;AAKD,SAAA,YAAA,CA5BC,KAAA,EA4BD;IACE,OA5BO,IAAI,UAAA,CA6BP,CAAC,GA5BiC,KAAK,GAAA,CAAI,KAAC,CAAK,wBAAC,CA6B9C,CAFV,4DAAA,EAEyE,KA5BC,CAAK,IAAC,CA0BhF,iBAAA,CA1BoF,CAAmB,CAAC,CAAC,CAAC;CA6BzG;;;;;;;;;;;;AAYD,AAAA,SAAA,cAAA,CACI,cAAwB,EAAE,YAAgC,EAAE,aAA4B,EACxF,OAAgB,EAAE,MAAc,EAFpC;IAGE,OAjCO,IAAI,cAAA,CAAe,cAAC,EAAe,YAAA,EAAc,aAAA,EAAe,OAAA,EAAS,MAAA,CAAO,CAAC,KAAC,EAAK,CAAE;CAkCjG;AACD,MAAA,cAAA,CAAA;;;;;;;;IAUA,WAAA,CACM,cAtCgB,EAAkB,YAAc,EACxC,aAAe,EAAuB,OAAS,EAAiB,MAAQ,EAoCtF;QArCwC,IAAxC,CAAA,YAAwC,GAAA,YAAA,CAAc;QACxC,IAAd,CAAA,aAAc,GAAA,aAAA,CAAe;QAAuB,IAApD,CAAA,OAAoD,GAAA,OAAA,CAAS;QAAiB,IAA9E,CAAA,MAA8E,GAAA,MAAA,CAAQ;QAL5E,IAAV,CAAA,cAAU,GAA0B,IAAA,CAAK;QA+CrC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;KACjD;;;;IAIH,KA3CG,GA2CH;QACI,uBA3CM,SAAA,GA4CF,IAAI,CA3CC,kBAAC,CAAkB,IAAC,CAAI,QAAC,EAAS,IAAA,CAAK,MAAC,EAAO,IAAA,CAAK,OAAC,CAAO,IAAC,EAAK,cAAA,CAAe,CAAC;QA4C3F,uBA3CM,SAAA,GAAY,GAAA,CAAI,IAAC,CA4CnB,SAAS,EA3CE,CAAA,gBAAmB,KAAoB,IAAA,CAAK,aAAC,CA4CzC,gBAAgB,EA3CE,IAAA,CAAK,OAAC,CAAO,WAAC,qBAAY,IAAA,CAAK,OAAC,CAAO,QAAC,GAAU,CAAC,CAAC;QA4CzF,OA3CO,MAAA,CAAO,IAAC,CAAI,SAAC,EAAU,CAAA,CAAI,KA2CtC;YACM,IAAI,CA3CC,YAAY,gBAAA,EAAkB;;gBA6CjC,IAAI,CA3CC,cAAC,GAAgB,KAAA,CAAM;;gBA6C5B,OA3CO,IAAA,CAAK,KAAC,CAAK,CAAC,CAAC,OAAC,CAAO,CAAC;aA4C9B;YAED,IAAI,CA3CC,YAAY,OAAA,EAAS;gBA4CxB,MA3CM,IAAA,CAAK,YAAC,CAAY,CAAC,CAAC,CAAC;aA4C5B;YAED,MA3CM,CAAA,CAAE;SA4CT,CA3CC,CAAC;KA4CJ;;;;;IAzCA,KAAA,CAAA,IAAA,EAAH;QA+CI,uBA9CM,SAAA,GA+CF,IAAI,CA9CC,kBAAC,CAAkB,IAAC,CAAI,QAAC,EAAS,IAAA,CAAK,MAAC,EAAO,IAAA,CAAK,IAAC,EAAK,cAAA,CAAe,CAAC;QA+CnF,uBA9CM,OAAA,GAAU,GAAA,CAAI,IAAC,CA+CjB,SAAS,EA9CE,CAAA,gBAAmB,KA+Cf,IAAI,CA9CC,aAAC,CAAa,gBAAC,EAAiB,IAAA,CAAK,WAAC,qBAAY,IAAA,CAAK,QAAC,GAAU,CAAC,CAAC;QA+C5F,OA9CO,MAAA,CAAO,IAAC,CAAI,OAAC,EAAQ,CAAA,CAAI,KA8CpC;YACM,IAAI,CA9CC,YAAY,OAAA,EAAS;gBA+CxB,MA9CM,IAAA,CAAK,YAAC,CAAY,CAAC,CAAC,CAAC;aA+C5B;YAED,MA9CM,CAAA,CAAE;SA+CT,CA9CC,CAAC;KA+CJ;;;;;IA5CA,YAAA,CAAA,CAAA,EAAH;QAkDI,OAjDO,IAAI,KAAA,CAAM,CAiDrB,uCAAA,EAjDsB,CAA0C,CAAC,YAAC,CAiDlE,CAAA,CAjD8E,CAAG,CAAC;KAkD/E;;;;;;;IA/CA,aAAA,CAAA,aAAA,EAAA,WAAA,EAAA,QAAA,EAAH;QAwDI,uBAtDM,IAAA,GAAO,aAAA,CAAc,QAAC,CAAQ,MAAC,GAAQ,CAAA;YAuDzC,IAtDI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAE,cAAC,GAAgB,aAAA,EAAc,CAAC;YAuD1D,aAAa,CAtDC;QAuDlB,OAtDO,IAAI,OAAA,CAAQ,IAAC,EAAK,WAAA,EAAa,QAAA,CAAS,CAAC;KAuDjD;;;;;;;;IApDA,kBAAA,CA6DG,QAA0B,EAAE,MAAe,EAAE,YAA6B,EAC1E,MAAc,EA9DpB;QA+DI,IAAI,YA5DC,CAAY,QAAC,CAAQ,MAAC,KAAU,CAAA,IAAK,YAAA,CAAa,WAAC,EAAW,EAAG;YA6DpE,OA5DO,GAAA,CAAI,IAAC,CA6DR,IAAI,CA5DC,cAAC,CAAc,QAAC,EAAS,MAAA,EAAQ,YAAA,CAAa,EA6DnD,CAAC,QA5DU,KAAQ,IAAI,eAAA,CAAgB,EAAC,EAAG,QAAA,CAAS,CAAC,CAAC;SA6D3D;QAED,OA5DO,IAAA,CAAK,aAAC,CAAa,QAAC,EAAS,YAAA,EAAc,MAAA,EAAQ,YAAA,CAAa,QAAC,EAAS,MAAA,EAAQ,IAAA,CAAK,CAAC;KA6DhG;;;;;;;IAzDA,cAAA,CAiEG,QAA0B,EAAE,MAAe,EAC3C,YAA6B,EAlEnC;QAmEI,OAhEO,UAAA,CAiEH,YAAY,CAhEC,QAAC,EAiEd,CAAC,WAhEC,EAAY,KAAA,KAAU,IAAA,CAAK,kBAAC,CAAkB,QAAC,EAAS,MAAA,EAAQ,KAAA,EAAO,WAAA,CAAY,CAAC,CAAC;KAiE5F;;;;;;;;;;IA9DA,aAAA,CAyEG,QAA0B,EAAE,YAA6B,EAAE,MAAe,EAC1E,QAAsB,EAAE,MAAc,EACtC,cAAuB,EA3E7B;QA4EI,uBAxEM,OAAA,GAAU,EAAA,CAAG,GAAE,MAAG,CAAM,CAAC;QAyE/B,uBAxEM,gBAAA,GAAmB,GAAA,CAAI,IAAC,CAAI,OAAC,EAAQ,CAAA,CAAI,KAwEnD;YACM,uBAxEM,SAAA,GAAY,IAAA,CAAK,yBAAC,CAyEpB,QAAQ,EAxEE,YAAA,EAAc,MAAA,EAAQ,CAAA,EAAG,QAAA,EAAU,MAAA,EAAQ,cAAA,CAAe,CAAC;YAyEzE,OAxEO,MAAA,CAAO,IAAC,CAAI,SAAC,EAAU,CAAA,CAAI,KAwExC;gBACQ,IAAI,CAxEC,YAAY,OAAA,EAAS;oBAyExB,OAxEO,EAAA,CAAG,IAAE,CAAI,CAAC;iBAyElB;gBAED,MAxEM,CAAA,CAAE;aAyET,CAxEC,CAAC;SAyEJ,CAxEC,CAAC;QAyEH,uBAxEM,yBAAA,GAA4B,SAAA,CAAU,IAAC,CAAI,gBAAC,CAAgB,CAAC;QAyEnE,uBAxEM,MAAA,GAAS,KAAA,CAAM,IAAC,CAAI,yBAAC,EAA0B,CAAA,CAAI,KAAQ,CAAA,CAAE,CAAC,CAAC,CAAC;QAyEtE,OAxEO,MAAA,CAAO,IAAC,CAAI,MAAC,EAAO,CAAA,CAAI,EAAK,CAAG,KAwE3C;YACM,IAAI,CAxEC,YAAY,UAAA,EAAY;gBAyE3B,IAAI,IAxEC,CAAI,gBAAC,CAAgB,YAAC,EAAa,QAAA,EAAU,MAAA,CAAO,EAAE;oBAyEzD,OAxEO,EAAA,CAAG,IAAK,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC,CAAC;iBAyEzC;gBAED,MAxEM,IAAI,OAAA,CAAQ,YAAC,CAAY,CAAC;aAyEjC;YAED,MAxEM,CAAA,CAAE;SAyET,CAxEC,CAAC;KAyEJ;;;;;;;IAtEA,gBAAA,CAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAH;QA+EI,OA7EO,QAAA,CAAS,MAAC,KAAU,CAAA,IAAK,CAAA,YAAE,CAAY,QAAC,CAAQ,MAAC,CAAM,CAAC;KA8EhE;;;;;;;;;;;IA3EA,yBAAA,CAuFG,QAA0B,EAAE,YAA6B,EAAE,MAAe,EAAE,KAAY,EACxF,KAAmB,EAAE,MAAc,EAAE,cAAuB,EAxFlE;QAyFI,IAAI,SAtFC,CAAS,KAAC,CAAK,KAAK,MAAA,EAAQ;YAuF/B,OAtFO,OAAA,CAAQ,YAAC,CAAY,CAAC;SAuF9B;QAED,IAAI,KAtFC,CAAK,UAAC,KAAc,SAAA,EAAW;YAuFlC,OAtFO,IAAA,CAAK,wBAAC,CAAwB,QAAC,EAAS,YAAA,EAAc,KAAA,EAAO,KAAA,CAAM,CAAC;SAuF5E;QAED,IAAI,cAtFC,IAAiB,IAAA,CAAK,cAAC,EAAe;YAuFzC,OAtFO,IAAA,CAAK,sCAAC,CAuFT,QAAQ,EAtFE,YAAA,EAAc,MAAA,EAAQ,KAAA,EAAO,KAAA,EAAO,MAAA,CAAO,CAAC;SAuF3D;QAED,OAtFO,OAAA,CAAQ,YAAC,CAAY,CAAC;KAuF9B;;;;;;;;;;IApFA,sCAAA,CA+FG,QAA0B,EAAE,YAA6B,EAAE,MAAe,EAAE,KAAY,EACxF,QAAsB,EAAE,MAAc,EAhG5C;QAiGI,IAAI,KA9FC,CAAK,IAAC,KAAQ,IAAA,EAAM;YA+FvB,OA9FO,IAAA,CAAK,iDAAC,CA+FT,QAAQ,EA9FE,MAAA,EAAQ,KAAA,EAAO,MAAA,CAAO,CAAC;SA+FtC;QAED,OA9FO,IAAA,CAAK,6CAAC,CA+FT,QAAQ,EA9FE,YAAA,EAAc,MAAA,EAAQ,KAAA,EAAO,QAAA,EAAU,MAAA,CAAO,CAAC;KA+F9D;;;;;;;;IA5FA,iDAAA,CAqGG,QAA0B,EAAE,MAAe,EAAE,KAAY,EACzD,MAAc,EAtGpB;QAuGI,uBApGM,OAAA,GAAU,IAAA,CAAK,qBAAC,CAAqB,EAAC,qBAAG,KAAA,CAAM,UAAC,IAAa,EAAA,CAAG,CAAC;QAqGvE,IAAqB,EAAE,KApGlB,CAAK,UAAC,GAAY,UAAC,CAAU,GAAC,CAAG,EAAE;YAqGtC,OApGO,gBAAA,CAAiB,OAAC,CAAO,CAAC;SAqGlC;QAED,OApGO,QAAA,CAAS,IAAC,CAAI,IAAC,CAAI,kBAAC,CAAkB,KAAC,EAAM,OAAA,CAAQ,EAAE,CAAA,WAAyB,KAoG3F;YACM,uBApGM,KAAA,GAAQ,IAAI,eAAA,CAAgB,WAAC,EAAY,EAAA,CAAG,CAAC;YAqGnD,OApGO,IAAA,CAAK,aAAC,CAAa,QAAC,EAAS,KAAA,EAAO,MAAA,EAAQ,WAAA,EAAa,MAAA,EAAQ,KAAA,CAAM,CAAC;SAqGhF,CApGC,CAAC;KAqGJ;;;;;;;;;;IAlGA,6CAAA,CA6GG,QAA0B,EAAE,YAA6B,EAAE,MAAe,EAAE,KAAY,EACxF,QAAsB,EAAE,MAAc,EA9G5C;QA+GI,MA5GM,EAAA,OAAE,EAAQ,gBAAA,EAAkB,SAAA,EAAW,uBAAA,EAAwB,GA6GjE,KAAK,CA5GC,YAAC,EAAa,KAAA,EAAO,QAAA,CAAS,CAAC;QA6GzC,IAAI,CA5GC,OAAC;YAAQ,OAAO,OAAA,CAAQ,YAAC,CAAY,CAAC;QA8G3C,uBA5GM,OAAA,GAAU,IAAA,CAAK,qBAAC,CA6GlB,gBAAgB,qBA5GE,KAAA,CAAM,UAAC,sBAAkB,uBAAC,EAAuB,CAAC;QA6GxE,IAAqB,EAAE,KA5GlB,CAAK,UAAC,GAAY,UAAC,CAAU,GAAC,CAAG,EAAE;YA6GtC,OA5GO,gBAAA,CAAiB,OAAC,CAAO,CAAC;SA6GlC;QAED,OA5GO,QAAA,CAAS,IAAC,CAAI,IAAC,CAAI,kBAAC,CAAkB,KAAC,EAAM,OAAA,CAAQ,EAAE,CAAA,WAAyB,KA4G3F;YACM,OA5GO,IAAA,CAAK,aAAC,CA6GT,QAAQ,EA5GE,YAAA,EAAc,MAAA,EAAQ,WAAA,CAAY,MAAC,CAAM,QAAC,CAAQ,KAAC,CAAK,SAAC,CAAS,CAAC,EAAE,MAAA,EA6G/E,KAAK,CA5GC,CAAC;SA6GZ,CA5GC,CAAC;KA6GJ;;;;;;;;IA1GA,wBAAA,CAmHG,QAA0B,EAAE,eAAgC,EAAE,KAAY,EAC1E,QAAsB,EApH5B;QAqHI,IAAI,KAlHC,CAAK,IAAC,KAAQ,IAAA,EAAM;YAmHvB,IAAI,KAlHC,CAAK,YAAC,EAAa;gBAmHtB,OAlHO,GAAA,CAAI,IAAC,CAmHR,IAAI,CAlHC,YAAC,CAAY,IAAC,CAAI,QAAC,CAAQ,QAAC,EAAS,KAAA,CAAM,EAAE,CAAA,GAAM,KAiHpE;oBAEc,KAAK,CAlHC,aAAC,GAAe,GAAA,CAAI;oBAmH1B,OAlHO,IAAI,eAAA,CAAgB,QAAC,EAAS,EAAA,CAAG,CAAC;iBAmH1C,CAlHC,CAAC;aAmHR;YAED,OAlHO,EAAA,CAAG,IAAK,eAAA,CAAgB,QAAC,EAAS,EAAA,CAAG,CAAC,CAAC;SAmH/C;QAED,MAlHM,EAAA,OAAE,EAAQ,gBAAA,EAAkB,SAAA,EAAU,GAAG,KAAA,CAAM,eAAC,EAAgB,KAAA,EAAO,QAAA,CAAS,CAAC;QAmHvF,IAAI,CAlHC,OAAC;YAAQ,OAAO,OAAA,CAAQ,eAAC,CAAe,CAAC;QAoH9C,uBAlHM,iBAAA,GAAoB,QAAA,CAAS,KAAC,CAAK,SAAC,CAAS,CAAC;QAmHpD,uBAlHM,YAAA,GAAe,IAAA,CAAK,cAAC,CAAc,QAAC,EAAS,KAAA,CAAM,CAAC;QAoH1D,OAlHO,QAAA,CAAS,IAAC,CAAI,YAAC,EAAa,CAAA,YAAe,KAkHtD;YACM,uBAlHM,WAAA,GAAc,YAAA,CAAa,MAAC,CAAM;YAmHxC,uBAlHM,WAAA,GAAc,YAAA,CAAa,MAAC,CAAM;YAoHxC,MAlHM,EAAA,YAAE,EAAa,cAAA,EAAe,GAmHhC,KAAK,CAlHC,eAAC,EAAgB,gBAAA,EAAkB,iBAAA,EAAmB,WAAA,CAAY,CAAC;YAoH7E,IAAI,cAlHC,CAAc,MAAC,KAAU,CAAA,IAAK,YAAA,CAAa,WAAC,EAAW,EAAG;gBAmH7D,uBAlHM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc,WAAC,EAAY,WAAA,EAAa,YAAA,CAAa,CAAC;gBAmH9E,OAlHO,GAAA,CAAI,IAAC,CAmHR,SAAS,EAlHE,CAAA,QAAW,KAAQ,IAAI,eAAA,CAAgB,gBAAC,EAAiB,QAAA,CAAS,CAAC,CAAC;aAmHpF;YAED,IAAI,WAlHC,CAAW,MAAC,KAAU,CAAA,IAAK,cAAA,CAAe,MAAC,KAAU,CAAA,EAAG;gBAmH3D,OAlHO,EAAA,CAAG,IAAK,eAAA,CAAgB,gBAAC,EAAiB,EAAA,CAAG,CAAC,CAAC;aAmHvD;YAED,uBAlHM,SAAA,GAAY,IAAA,CAAK,aAAC,CAmHpB,WAAW,EAlHE,YAAA,EAAc,WAAA,EAAa,cAAA,EAAgB,cAAA,EAAgB,IAAA,CAAK,CAAC;YAmHlF,OAlHO,GAAA,CAAI,IAAC,CAmHR,SAAS,EAlHE,CAAA,EAAK,KAmHD,IAlHI,eAAA,CAAgB,gBAAC,CAAgB,MAAC,CAAM,EAAC,CAAE,QAAC,CAAQ,EAAE,EAAA,CAAG,QAAC,CAAQ,CAAC,CAAC;SAmH5F,CAlHC,CAAC;KAmHJ;;;;;;IAhHA,cAAA,CAAA,QAAA,EAAA,KAAA,EAAH;QAuHI,IAAI,KAtHC,CAAK,QAAC,EAAS;;YAwHlB,OAtHO,EAAA,CAAG,IAAK,kBAAA,CAAmB,KAAC,CAAK,QAAC,EAAS,QAAA,CAAS,CAAC,CAAC;SAuH9D;QAED,IAAI,KAtHC,CAAK,YAAC,EAAa;;YAwHtB,IAAI,KAtHC,CAAK,aAAC,KAAiB,SAAA,EAAW;gBAuHrC,OAtHO,EAAA,CAAG,KAAE,CAAK,aAAC,CAAa,CAAC;aAuHjC;YAED,OAtHO,QAAA,CAAS,IAAC,CAAI,eAAC,CAAe,QAAC,CAAQ,QAAC,EAAS,KAAA,CAAM,EAAE,CAAA,UAAa,KAsHnF;gBAEQ,IAAI,UAtHC,EAAW;oBAuHd,OAtHO,GAAA,CAAI,IAAC,CAuHR,IAAI,CAtHC,YAAC,CAAY,IAAC,CAAI,QAAC,CAAQ,QAAC,EAAS,KAAA,CAAM,EAAE,CAAA,GAAM,KAqHtE;wBAEgB,KAAK,CAtHC,aAAC,GAAe,GAAA,CAAI;wBAuH1B,OAtHO,GAAA,CAAI;qBAuHZ,CAtHC,CAAC;iBAuHR;gBAED,OAtHO,YAAA,CAAa,KAAC,CAAK,CAAC;aAuH5B,CAtHC,CAAC;SAuHJ;QAED,OAtHO,EAAA,CAAG,IAAK,kBAAA,CAAmB,EAAC,EAAG,QAAA,CAAS,CAAC,CAAC;KAuHlD;;;;;;IApHA,kBAAA,CAAA,KAAA,EAAA,OAAA,EAAH;QA2HI,qBA1HI,GAAA,GAAoB,EAAA,CAAG;QA2H3B,qBA1HI,CAAA,GAAI,OAAA,CAAQ,IAAC,CAAI;QA2HrB,OAAO,IA1HC,EAAK;YA2HX,GAAG,GA1HG,GAAA,CAAI,MAAC,CAAM,CAAC,CAAC,QAAC,CAAQ,CAAC;YA2H7B,IAAI,CA1HC,CAAC,gBAAC,KAAoB,CAAA,EAAG;gBA2H5B,OA1HO,EAAA,CAAG,GAAE,CAAG,CAAC;aA2HjB;YAED,IAAI,CA1HC,CAAC,gBAAC,GAAkB,CAAA,IAAK,CAAA,CAAE,CAAC,QAAC,CAAQ,cAAC,CAAc,EAAE;gBA2HzD,OA1HO,oBAAA,oBAAqB,KAAC,CAAK,UAAC,GAAY,CAAC;aA2HjD;YAED,CAAC,GA1HG,CAAA,CAAE,QAAC,CAAQ,cAAC,CAAc,CAAC;SA2HhC;KACF;;;;;;;IAxHA,qBAAA,CAgIG,QAAsB,EAAE,UAAkB,EAAE,SAAoC,EAhItF;QAiII,OA/HO,IAAA,CAAK,2BAAC,CAgIT,UAAU,EA/HE,IAAA,CAAK,aAAC,CAAa,KAAC,CAAK,UAAC,CAAU,EAAE,QAAA,EAAU,SAAA,CAAU,CAAC;KAgI5E;;;;;;;;IA7HA,2BAAA,CAsIG,UAAkB,EAAE,OAAgB,EAAE,QAAsB,EAC5D,SAAoC,EAvI1C;QAwII,uBArIM,OAAA,GAAU,IAAA,CAAK,kBAAC,CAAkB,UAAC,EAAW,OAAA,CAAQ,IAAC,EAAK,QAAA,EAAU,SAAA,CAAU,CAAC;QAsIvF,OArIO,IAAI,OAAA,CAsIP,OAAO,EArIE,IAAA,CAAK,iBAAC,CAAiB,OAAC,CAAO,WAAC,EAAY,IAAA,CAAK,OAAC,CAAO,WAAC,CAAW,EAsI9E,OAAO,CArIC,QAAC,CAAQ,CAAC;KAsIvB;;;;;;IAnIA,iBAAA,CAAA,gBAAA,EAAA,YAAA,EAAH;QA0II,uBAzIM,GAAA,GAAc,EAAA,CAAG;QA0IvB,OAAO,CAzIC,gBAAC,EAAiB,CAAA,CAAI,EAAK,CAAG,KAyI1C;YACM,uBAzIM,eAAA,GAAkB,OAAO,CAAA,KAAM,QAAA,IAAY,CAAA,CAAE,UAAC,CAAU,GAAC,CAAG,CAAC;YA0InE,IAAI,eAzIC,EAAgB;gBA0InB,uBAzIM,UAAA,GAAa,CAAA,CAAE,SAAC,CAAS,CAAC,CAAC,CAAC;gBA0IlC,GAAG,CAzIC,CAAC,CAAC,GAAG,YAAA,CAAa,UAAC,CAAU,CAAC;aA0InC;iBAzIM;gBA0IL,GAAG,CAzIC,CAAC,CAAC,GAAG,CAAA,CAAE;aA0IZ;SACF,CAzIC,CAAC;QA0IH,OAzIO,GAAA,CAAI;KA0IZ;;;;;;;;IAvIA,kBAAA,CAgJG,UAAkB,EAAE,KAAsB,EAAE,QAAsB,EAClE,SAAoC,EAjJ1C;QAkJI,uBA/IM,eAAA,GAAkB,IAAA,CAAK,cAAC,CAAc,UAAC,EAAW,KAAA,CAAM,QAAC,EAAS,QAAA,EAAU,SAAA,CAAU,CAAC;QAiJ7F,qBA/II,QAAA,GAA2C,EAAA,CAAG;QAgJlD,OAAO,CA/IC,KAAC,CAAK,QAAC,EAAS,CAAA,KAAQ,EAAiB,IAAM,KA+I3D;YACM,QAAQ,CA/IC,IAAC,CAAI,GAAG,IAAA,CAAK,kBAAC,CAAkB,UAAC,EAAW,KAAA,EAAO,QAAA,EAAU,SAAA,CAAU,CAAC;SAgJlF,CA/IC,CAAC;QAiJH,OA/IO,IAAI,eAAA,CAAgB,eAAC,EAAgB,QAAA,CAAS,CAAC;KAgJvD;;;;;;;;IA7IA,cAAA,CAsJG,UAAkB,EAAE,kBAAgC,EAAE,cAA4B,EAClF,SAAoC,EAvJ1C;QAwJI,OArJO,kBAAA,CAAmB,GAAC,CAsJvB,CAAC,IArJI,CAAA,CAAE,IAAC,CAAI,UAAC,CAAU,GAAC,CAAG,GAAG,IAAA,CAAK,YAAC,CAAY,UAAC,EAAW,CAAA,EAAG,SAAA,CAAU;YAsJ3C,IAAI,CArJC,YAAC,CAAY,CAAC,EAAE,cAAA,CAAe,CAAC,CAAC;KAsJzE;;;;;;;IAnJA,YAAA,CA2JG,UAAkB,EAAE,oBAAgC,EACpD,SAAoC,EA5J1C;QA6JI,uBA1JM,GAAA,GAAM,SAAA,CAAU,oBAAC,CAAoB,IAAC,CAAI,SAAC,CAAS,CAAC,CAAC,CAAC,CAAC;QA2J9D,IAAI,CA1JC,GAAC;YA2JJ,MA1JM,IAAI,KAAA,CA2JN,CADV,oBAAA,EACiC,UA1JC,CAyJlC,gBAAA,EAzJ4C,oBAAmB,CAAoB,IAAC,CAyJpF,EAAA,CAzJwF,CAAI,CAAC;QA2JzF,OA1JO,GAAA,CAAI;KA2JZ;;;;;;IAxJA,YAAA,CAAA,oBAAA,EAAA,cAAA,EAAH;QA+JI,qBA9JI,GAAA,GAAM,CAAA,CAAE;QA+JZ,KAAK,uBA9JM,CAAA,IAAK,cAAA,EAAgB;YA+J9B,IAAI,CA9JC,CAAC,IAAC,KAAQ,oBAAA,CAAqB,IAAC,EAAK;gBA+JxC,cAAc,CA9JC,MAAC,CAAM,GAAC,CAAG,CAAC;gBA+J3B,OA9JO,CAAA,CAAE;aA+JV;YACD,GAAG,EA9JC,CAAE;SA+JP;QACD,OA9JO,oBAAA,CAAqB;KA+J7B;CACF;AAED,AAeA;;;;;AAKA,SAAA,eAAA,CAlLC,cAAA,EAAA,KAAA,EAkLD;IACE,uBAlLM,OAAA,GAAU,KAAA,CAAM,OAAC,CAAO;IAmL9B,IAAI,CAlLC,OAAC,IAAU,OAAA,CAAQ,MAAC,KAAU,CAAA;QAAG,OAAO,EAAA,CAAG,IAAE,CAAI,CAAC;IAoLvD,uBAlLM,GAAA,GAAM,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,OAAC,CAAO,EAAE,CAAA,cAAiB,KAkLvD;QACI,uBAlLM,KAAA,GAAQ,cAAA,CAAe,GAAC,CAAG,cAAC,CAAc,CAAC;QAmLjD,OAlLO,kBAAA,CAAmB,KAAC,CAAK,OAAC,GAAS,KAAA,CAAM,OAAC,CAAO,KAAC,CAAK,GAAG,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC;KAmLhF,CAlLC,CAAC;IAoLH,OAlLO,cAAA,CAAe,GAAC,CAAG,CAAC;CAmL5B;;;;;;;AAOD,SAAA,KAAA,CAvLC,YAAA,EAAA,KAAA,EAAA,QAAA,EAuLD;IAME,IAAI,KAvLC,CAAK,IAAC,KAAQ,EAAA,EAAI;QAwLrB,IAAI,CAvLC,KAAC,CAAK,SAAC,KAAa,MAAA,MAAW,YAAE,CAAY,WAAC,EAAW,IAAK,QAAA,CAAS,MAAC,GAAQ,CAAA,CAAE,EAAE;YAwLvF,OAvLO,EAAA,OAAE,EAAQ,KAAA,EAAO,gBAAA,EAAkB,EAAA,EAAI,SAAA,EAAW,CAAA,EAAG,uBAAA,EAAyB,EAAA,EAAG,CAAC;SAwL1F;QAED,OAvLO,EAAA,OAAE,EAAQ,IAAA,EAAM,gBAAA,EAAkB,EAAA,EAAI,SAAA,EAAW,CAAA,EAAG,uBAAA,EAAyB,EAAA,EAAG,CAAC;KAwLzF;IAED,uBAvLM,OAAA,GAAU,KAAA,CAAM,OAAC,IAAU,iBAAA,CAAkB;IAwLnD,uBAvLM,GAAA,GAAM,OAAA,CAAQ,QAAC,EAAS,YAAA,EAAc,KAAA,CAAM,CAAC;IAyLnD,IAAI,CAvLC,GAAC,EAAI;QAwLR,OAvLO;YAwLL,OAAO,EAvLE,KAAA,EAAO,gBAAA,oBAAyB,EAAC,CAAA,EAAG,SAAA,EAAW,CAAA,EAAG,uBAAA,EAAyB,EAAA;SAwLrF,CAAA;KACF;IAED,OAvLO;QAwLL,OAAO,EAvLE,IAAA;QAwLT,gBAAgB,qBAvLE,GAAA,CAAI,QAAC,EAAA;QAwLvB,SAAS,qBAvLE,GAAA,CAAI,QAAC,CAAQ,MAAC,EAAA;QAwLzB,uBAAuB,qBAvLE,GAAA,CAAI,SAAC,EAAA;KAwL/B,CAvLC;CAwLH;;;;;;;;AAQD,SAAA,KAAA,CACI,YAA6B,EAAE,gBAA8B,EAAE,cAA4B,EAC3F,MAAe,EAFnB;IAGE,IAAI,cA7LC,CAAc,MAAC,GAAQ,CAAA;QA8LxB,0CAA0C,CA7LC,YAAC,EAAa,cAAA,EAAgB,MAAA,CAAO,EAAE;QA8LpF,uBA7LM,CAAA,GAAI,IAAI,eAAA,CA8LV,gBAAgB,EA7LE,8BAAA,CA8LI,MAAM,EA7LE,IAAI,eAAA,CAAgB,cAAC,EAAe,YAAA,CAAa,QAAC,CAAQ,CAAC,CAAC,CAAC;QA8L/F,OA7LO,EAAA,YAAE,EAAa,oBAAA,CAAqB,CAAC,CAAC,EAAE,cAAA,EAAgB,EAAA,EAAG,CAAC;KA8LpE;IAED,IAAI,cA7LC,CAAc,MAAC,KAAU,CAAA;QA8L1B,0BAA0B,CA7LC,YAAC,EAAa,cAAA,EAAgB,MAAA,CAAO,EAAE;QA8LpE,uBA7LM,CAAA,GAAI,IAAI,eAAA,CA8LV,YAAY,CA7LC,QAAC,EAAS,kCAAA,CA8LI,YAAY,EA7LE,cAAA,EAAgB,MAAA,EAAQ,YAAA,CAAa,QAAC,CAAQ,CAAC,CAAC;QA8L7F,OA7LO,EAAA,YAAE,EAAa,oBAAA,CAAqB,CAAC,CAAC,EAAE,cAAA,EAAe,CAAC;KA8LhE;IAED,OA7LO,EAAA,YAAE,EAAa,cAAA,EAAe,CAAC;CA8LvC;;;;;AAKD,SAAA,oBAAA,CAhMC,CAAA,EAgMD;IACE,IAAI,CAhMC,CAAC,gBAAC,KAAoB,CAAA,IAAK,CAAA,CAAE,QAAC,CAAQ,cAAC,CAAc,EAAE;QAiM1D,uBAhMM,CAAA,GAAI,CAAA,CAAE,QAAC,CAAQ,cAAC,CAAc,CAAC;QAiMrC,OAhMO,IAAI,eAAA,CAAgB,CAAC,CAAC,QAAC,CAAQ,MAAC,CAAM,CAAC,CAAC,QAAC,CAAQ,EAAE,CAAA,CAAE,QAAC,CAAQ,CAAC;KAiMvE;IAED,OAhMO,CAAA,CAAE;CAiMV;;;;;;;;AAQD,SAAA,kCAAA,CACI,YAA6B,EAAE,cAA4B,EAAE,MAAe,EAC5E,QAA2C,EAF/C;IAGE,uBAtMM,GAAA,GAAyC,EAAA,CAAG;IAuMlD,KAAK,uBAtMM,CAAA,IAAK,MAAA,EAAQ;QAuMtB,IAAI,mBAtMC,CAAmB,YAAC,EAAa,cAAA,EAAgB,CAAA,CAAE,IAAI,CAAA,QAAE,CAAQ,SAAC,CAAS,CAAC,CAAC,CAAC,EAAE;YAuMnF,GAAG,CAtMC,SAAC,CAAS,CAAC,CAAC,CAAC,GAAG,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;SAuMjD;KACF;IACD,OAAF,MAAA,CAAA,MAAA,CAAA,EAAA,EAtMW,QAAG,EAAS,GAAI,CAsM3B,CAtM+B;CAuM9B;;;;;;AAMD,SAAA,8BAAA,CACI,MAAe,EAAE,mBAAoC,EADzD;IAEE,uBA1MM,GAAA,GAAyC,EAAA,CAAG;IA2MlD,GAAG,CA1MC,cAAC,CAAc,GAAG,mBAAA,CAAoB;IA2M1C,KAAK,uBA1MM,CAAA,IAAK,MAAA,EAAQ;QA2MtB,IAAI,CA1MC,CAAC,IAAC,KAAQ,EAAA,IAAM,SAAA,CAAU,CAAC,CAAC,KAAK,cAAA,EAAgB;YA2MpD,GAAG,CA1MC,SAAC,CAAS,CAAC,CAAC,CAAC,GAAG,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;SA2MjD;KACF;IACD,OA1MO,GAAA,CAAI;CA2MZ;;;;;;;AAOD,SAAA,0CAAA,CACI,YAA6B,EAAE,QAAsB,EAAE,MAAe,EAD1E;IAEE,OA/MO,MAAA,CAAO,IAAC,CAgNX,CAAC,IA/MI,mBAAA,CAAoB,YAAC,EAAa,QAAA,EAAU,CAAA,CAAE,IAAI,SAAA,CAAU,CAAC,CAAC,KAAK,cAAA,CAAe,CAAC;CAgN7F;;;;;;;AAOD,SAAA,0BAAA,CACI,YAA6B,EAAE,QAAsB,EAAE,MAAe,EAD1E;IAEE,OApNO,MAAA,CAAO,IAAC,CAAI,CAAC,IAAI,mBAAA,CAAoB,YAAC,EAAa,QAAA,EAAU,CAAA,CAAE,CAAC,CAAC;CAqNzE;;;;;;;AAOD,SAAA,mBAAA,CACI,YAA6B,EAAE,QAAsB,EAAE,CAAQ,EADnE;IAEE,IAAI,CAzNC,YAAC,CAAY,WAAC,EAAW,IAAK,QAAA,CAAS,MAAC,GAAQ,CAAA,KAAM,CAAA,CAAE,SAAC,KAAa,MAAA,EAAQ;QA0NjF,OAzNO,KAAA,CAAM;KA0Nd;IAED,OAzNO,CAAA,CAAE,IAAC,KAAQ,EAAA,IAAM,CAAA,CAAE,UAAC,KAAc,SAAA,CAAU;CA0NpD;;;;;AAKD,SAAA,SAAA,CA5NC,KAAA,EA4ND;IACE,OA5NO,KAAA,CAAM,MAAC,IAAS,cAAA,CAAe;CA6NvC;;ADlvBD;;;;;;;AAOA,AAAA,MAAA,IAAA,CAAA;;;;IAQA,WAAA,CAJG,IAA4B,EAI/B,EAJgC,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,EAAA;;;;IAQhC,IANG,IAAA,GAMH,EANkB,OAAO,IAAA,CAAK,KAAC,CAAK,KAAC,CAAK,EAAC;;;;;;IAY3C,MAPG,CAAA,CAAA,EAOH;QACI,uBAPM,CAAA,GAAI,IAAA,CAAK,YAAC,CAAY,CAAC,CAAC,CAAC;QAQ/B,OAPO,CAAA,CAAE,MAAC,GAAQ,CAAA,GAAI,CAAA,CAAE,CAAC,CAAC,MAAC,GAAQ,CAAA,CAAE,GAAG,IAAA,CAAK;KAQ9C;;;;;;IAMH,QARG,CAAA,CAAA,EAQH;QACI,uBARM,CAAA,GAAI,QAAA,CAAS,CAAC,EAAE,IAAA,CAAK,KAAC,CAAK,CAAC;QASlC,OARO,CAAA,GAAI,CAAA,CAAE,QAAC,CAAQ,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,KAAC,CAAK,GAAG,EAAA,CAAG;KAS9C;;;;;;IAMH,UATG,CAAA,CAAA,EASH;QACI,uBATM,CAAA,GAAI,QAAA,CAAS,CAAC,EAAE,IAAA,CAAK,KAAC,CAAK,CAAC;QAUlC,OATO,CAAA,IAAK,CAAA,CAAE,QAAC,CAAQ,MAAC,GAAQ,CAAA,GAAI,CAAA,CAAE,QAAC,CAAQ,CAAC,CAAC,CAAC,KAAC,GAAO,IAAA,CAAK;KAUhE;;;;;;IAMH,QAVG,CAAA,CAAA,EAUH;QACI,uBAVM,CAAA,GAAI,QAAA,CAAS,CAAC,EAAE,IAAA,CAAK,KAAC,CAAK,CAAC;QAWlC,IAAI,CAVC,CAAC,MAAC,GAAQ,CAAA;YAAG,OAAO,EAAA,CAAG;QAY5B,uBAVM,CAAA,GAAI,CAAA,CAAE,CAAC,CAAC,MAAC,GAAQ,CAAA,CAAE,CAAC,QAAC,CAAQ,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,KAAC,CAAK,CAAC;QAWrD,OAVO,CAAA,CAAE,MAAC,CAAM,EAAC,IAAK,EAAA,KAAO,CAAA,CAAE,CAAC;KAWjC;;;;;;IAMH,YAXG,CAAA,CAAA,EAWH,EAX4B,OAAO,QAAA,CAAS,CAAC,EAAE,IAAA,CAAK,KAAC,CAAK,CAAC,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,KAAC,CAAK,CAAC,EAAC;CAY7E;AAED,AAQA;;;;;;AAMA,SAAA,QAAA,CAvBC,KAAA,EAAA,IAAA,EAuBD;IACE,IAAI,KAvBC,KAAS,IAAA,CAAK,KAAC;QAAM,OAAO,IAAA,CAAK;IAyBtC,KAAK,uBAvBM,KAAA,IAAS,IAAA,CAAK,QAAC,EAAS;QAwBjC,uBAvBM,IAAA,GAAO,QAAA,CAAS,KAAC,EAAM,KAAA,CAAM,CAAC;QAwBpC,IAAI,IAvBC;YAAK,OAAO,IAAA,CAAK;KAwBvB;IAED,OAvBO,IAAA,CAAK;CAwBb;;;;;;;AAOD,SAAA,QAAA,CA3BC,KAAA,EAAA,IAAA,EA2BD;IACE,IAAI,KA3BC,KAAS,IAAA,CAAK,KAAC;QAAM,OAAO,CAAA,IAAE,CAAI,CAAC;IA6BxC,KAAK,uBA3BM,KAAA,IAAS,IAAA,CAAK,QAAC,EAAS;QA4BjC,uBA3BM,IAAA,GAAO,QAAA,CAAS,KAAC,EAAM,KAAA,CAAM,CAAC;QA4BpC,IAAI,IA3BC,CAAI,MAAC,EAAO;YA4Bf,IAAI,CA3BC,OAAC,CAAO,IAAC,CAAI,CAAC;YA4BnB,OA3BO,IAAA,CAAK;SA4Bb;KACF;IAED,OA3BO,EAAA,CAAG;CA4BX;AACD,AAAA,MAAA,QAAA,CAAA;;;;;IAKA,WAAA,CA9BqB,KAAO,EAAU,QAAsB,EA8B5D;QA9BqB,IAArB,CAAA,KAAqB,GAAA,KAAA,CAAO;QAAU,IAAtC,CAAA,QAAsC,GAAA,QAAA,CAAsB;KAAE;;;;IAmC9D,QAjCG,GAiCH,EAjCuB,OAAO,CAiC9B,SAAA,EAjC8B,IAAa,CAAI,KAAC,CAiChD,CAAA,CAjCqD,CAAG,EAAC;CAkCxD,AAED,AAKC;;ADjID;;;;;;;AAUA,AAEA,AAGA,AACA,AACA,AACA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,AAAA,MAAA,WACC,SAAA,IAAA,CADD;;;;;;IAMA,WAAA,CACM,IAH8B,EAEvB,QAAU,EAAvB;QAGI,KAAK,CAAC,IAAI,CAAC,CAAC;QAHH,IAAb,CAAA,QAAa,GAAA,QAAA,CAAU;QAInB,cAAc,CAAc,IAAI,EAAE,IAAI,CAAC,CAAC;KACzC;;;;IAIH,QAJG,GAIH,EAJuB,OAAO,IAAA,CAAK,QAAC,CAAQ,QAAC,EAAQ,CAAE,EAAC;CAKvD;AAED,AAQA;;;;;AAKA,AAAA,SAAA,gBAAA,CAjBC,OAAA,EAAA,aAAA,EAiBD;IACE,uBAjBM,QAAA,GAAW,wBAAA,CAAyB,OAAC,EAAQ,aAAA,CAAc,CAAC;IAkBlE,uBAjBM,QAAA,GAAW,IAAI,eAAA,CAAgB,CAAC,IAAI,UAAA,CAAW,EAAC,EAAG,EAAA,CAAG,CAAC,CAAC,CAAC;IAkB/D,uBAjBM,WAAA,GAAc,IAAI,eAAA,CAAgB,EAAC,CAAE,CAAC;IAkB5C,uBAjBM,SAAA,GAAY,IAAI,eAAA,CAAgB,EAAC,CAAE,CAAC;IAkB1C,uBAjBM,gBAAA,GAAmB,IAAI,eAAA,CAAgB,EAAC,CAAE,CAAC;IAkBjD,uBAjBM,QAAA,GAAW,IAAI,eAAA,CAAgB,EAAC,CAAE,CAAC;IAkBzC,uBAjBM,SAAA,GAAY,IAAI,cAAA,CAkBlB,QAAQ,EAjBE,WAAA,EAAa,gBAAA,EAAkB,QAAA,EAAU,SAAA,EAAW,cAAA,EAAgB,aAAA,EAkB9E,QAAQ,CAjBC,IAAC,CAAI,CAAC;IAkBnB,SAAS,CAjBC,QAAC,GAAU,QAAA,CAAS,IAAC,CAAI;IAkBnC,OAjBO,IAAI,WAAA,CAAY,IAAI,QAAA,CAAwB,SAAE,EAAU,EAAA,CAAG,EAAE,QAAA,CAAS,CAAC;CAkB/E;;;;;;AAMD,AAAA,SAAA,wBAAA,CACI,OAAgB,EAAE,aAA8B,EADpD;IAEE,uBArBM,WAAA,GAAc,EAAA,CAAG;IAsBvB,uBArBM,SAAA,GAAY,EAAA,CAAG;IAsBrB,uBArBM,gBAAA,GAAmB,EAAA,CAAG;IAsB5B,uBArBM,QAAA,GAAW,EAAA,CAAG;IAsBpB,uBArBM,SAAA,GAAY,IAAI,sBAAA,CAsBlB,EAAE,EArBE,WAAA,EAAa,gBAAA,EAAkB,QAAA,EAAU,SAAA,EAAW,cAAA,EAAgB,aAAA,EAAe,IAAA,EAsBvF,OAAO,CArBC,IAAC,EAAK,CAAA,CAAE,EAAE,EAAA,CAAG,CAAC;IAsB1B,OArBO,IAAI,mBAAA,CAAoB,EAAC,EAAG,IAAI,QAAA,CAAgC,SAAE,EAAU,EAAA,CAAG,CAAC,CAAC;CAsBzF;;;;;;;;;;;;;;;;;;;;;;AAsBD,AAAA,MAAA,cAAA,CAAA;;;;;;;;;;;;IAgCA,WAAA,CArCa,GAA6B,EAE7B,MAA0B,EAE1B,WAA+B,EAE/B,QAA4B,EAE5B,IAAsB,EAEtB,MAAQ,EAGR,SAA6B,EAAK,cAAgB,EAwB/D;QArCa,IAAb,CAAA,GAAa,GAAA,GAAA,CAA6B;QAE7B,IAAb,CAAA,MAAa,GAAA,MAAA,CAA0B;QAE1B,IAAb,CAAA,WAAa,GAAA,WAAA,CAA+B;QAE/B,IAAb,CAAA,QAAa,GAAA,QAAA,CAA4B;QAE5B,IAAb,CAAA,IAAa,GAAA,IAAA,CAAsB;QAEtB,IAAb,CAAA,MAAa,GAAA,MAAA,CAAQ;QAGR,IAAb,CAAA,SAAa,GAAA,SAAA,CAA6B;QAgCtC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;KACvC;;;;;IAKH,IAjCG,WAAA,GAiCH,EAjCkC,OAAO,IAAA,CAAK,eAAC,CAAe,WAAC,CAAW,EAAC;;;;;IAsC3E,IAnCG,IAAA,GAmCH,EAnC+B,OAAO,IAAA,CAAK,YAAC,CAAY,IAAC,CAAI,EAAC;;;;;IAwC9D,IArCG,MAAA,GAqCH,EArCsC,OAAO,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,IAAC,CAAI,CAAC,EAAC;;;;;IA0C7E,IAvCG,UAAA,GAuCH,EAvC0C,OAAO,IAAA,CAAK,YAAC,CAAY,UAAC,CAAU,IAAC,CAAI,CAAC,EAAC;;;;;IA4CrF,IAzCG,QAAA,GAyCH,EAzCqC,OAAO,IAAA,CAAK,YAAC,CAAY,QAAC,CAAQ,IAAC,CAAI,CAAC,EAAC;;;;;IA8C9E,IA3CG,YAAA,GA2CH,EA3CyC,OAAO,IAAA,CAAK,YAAC,CAAY,YAAC,CAAY,IAAC,CAAI,CAAC,EAAC;;;;IA+CtF,IA7CG,QAAA,GA6CH;QACI,IAAI,CA7CC,IAAC,CAAI,SAAC,EAAU;YA8CnB,IAAI,CA7CC,SAAC,GAAW,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,MAAC,EAAO,CAAA,CAAI,KAAqB,iBAAA,CAAkB,CAAC,CAAC,CAAC,CAAC;SA8CvF;QACD,OA7CO,IAAA,CAAK,SAAC,CAAS;KA8CvB;;;;IAIH,IA/CG,aAAA,GA+CH;QACI,IAAI,CA/CC,IAAC,CAAI,cAAC,EAAe;YAgDxB,IAAI,CA/CC,cAAC;gBAgDF,GAAG,CA/CC,IAAC,CAAI,IAAC,CAAI,WAAC,EAAY,CAAA,CAAI,KAAqB,iBAAA,CAAkB,CAAC,CAAC,CAAC,CAAC;SAgD/E;QACD,OA/CO,IAAA,CAAK,cAAC,CAAc;KAgD5B;;;;IAIH,QAjDG,GAiDH;QACI,OAjDO,IAAA,CAAK,QAAC,GAAU,IAAA,CAAK,QAAC,CAAQ,QAAC,EAAQ,GAAI,CAiDtD,OAAA,EAjDsD,IAAW,CAAI,eAAC,CAiDtE,CAAA,CAjDqF,CAAG;KAkDrF;CACF;AAED,AAmEA;;;;;AAKA,AAAA,SAAA,0BAAA,CAjHC,KAAA,EAiHD;IACE,uBAjHM,UAAA,GAAa,KAAA,CAAM,YAAC,CAAY;IAmHtC,qBAjHI,qBAAA,GAAwB,UAAA,CAAW,MAAC,GAAQ,CAAA,CAAE;IAmHlD,OAAO,qBAjHC,IAAwB,CAAA,EAAG;QAkHjC,uBAjHM,OAAA,GAAU,UAAA,CAAW,qBAAC,CAAqB,CAAC;QAkHlD,uBAjHM,MAAA,GAAS,UAAA,CAAW,qBAAC,GAAuB,CAAA,CAAE,CAAC;;QAmHrD,IAAI,OAjHC,CAAO,WAAC,IAAc,OAAA,CAAQ,WAAC,CAAW,IAAC,KAAQ,EAAA,EAAI;YAkH1D,qBAAqB,EAjHC,CAAE;;SAoHzB;aAjHM,IAAA,CAAK,MAAC,CAAM,SAAC,EAAU;YAkH5B,qBAAqB,EAjHC,CAAE;SAmHzB;aAjHM;YAkHL,MAAM;SACP;KACF;IAED,OAjHO,UAAA,CAAW,KAAC,CAAK,qBAAC,CAAqB,CAAC,MAAC,CAAM,CAAC,GAAC,EAAI,IAAA,KAiH9D;QACI,uBAjHM,MAAA,GAiHV,MAAA,CAAA,MAAA,CAAA,EAAA,EAjHqB,GAAG,CAAG,MAAC,EAAO,IAAI,CAAI,MAAC,CAAM,CAAC;QAkH/C,uBAjHM,IAAA,GAiHV,MAAA,CAAA,MAAA,CAAA,EAAA,EAjHmB,GAAG,CAAG,IAAC,EAAK,IAAI,CAAI,IAAC,CAAI,CAAC;QAkHzC,uBAjHM,OAAA,GAiHV,MAAA,CAAA,MAAA,CAAA,EAAA,EAjHsB,GAAG,CAAG,OAAC,EAAQ,IAAI,CAAI,aAAC,CAAa,CAAC;QAkHxD,OAjHO,EAAA,MAAE,EAAO,IAAA,EAAM,OAAA,EAAQ,CAAC;KAkHhC,oBAjHO,EAAC,MAAC,EAAO,EAAA,EAAI,IAAA,EAAM,EAAA,EAAI,OAAA,EAAS,EAAA,EAAG,EAAC,CAAC;CAkH9C;;;;;;;;;;;;;;;;;;;;;;AAsBD,AAAA,MAAA,sBAAA,CAAA;;;;;;;;;;;;;;;IA+CA,WAAA,CA1Ia,GAAgB,EAEhB,MAAQ,EAER,WAAa,EAEb,QAAU,EAEV,IAAM,EAEN,MAAQ,EAER,SAA6B,EAAK,WAAoB,EAAK,UAAY,EAsI9E,aArIe,EAAQ,OAAS,EA6HtC;QA1Ia,IAAb,CAAA,GAAa,GAAA,GAAA,CAAgB;QAEhB,IAAb,CAAA,MAAa,GAAA,MAAA,CAAQ;QAER,IAAb,CAAA,WAAa,GAAA,WAAA,CAAa;QAEb,IAAb,CAAA,QAAa,GAAA,QAAA,CAAU;QAEV,IAAb,CAAA,IAAa,GAAA,IAAA,CAAM;QAEN,IAAb,CAAA,MAAa,GAAA,MAAA,CAAQ;QAER,IAAb,CAAA,SAAa,GAAA,SAAA,CAA6B;QAuItC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;KACzB;;;;;IAKH,IAvIG,WAAA,GAuIH,EAvIkC,OAAO,IAAA,CAAK,YAAC,CAAY,EAAC;;;;;IA4I5D,IAzIG,IAAA,GAyIH,EAzIuC,OAAO,IAAA,CAAK,YAAC,CAAY,IAAC,CAAI,EAAC;;;;;IA8ItE,IA3IG,MAAA,GA2IH,EA3I8C,OAAO,IAAA,CAAK,YAAC,CAAY,MAAC,CAAM,IAAC,CAAI,CAAC,EAAC;;;;;IAgJrF,IA7IG,UAAA,GA6IH,EA7IkD,OAAO,IAAA,CAAK,YAAC,CAAY,UAAC,CAAU,IAAC,CAAI,CAAC,EAAC;;;;;IAkJ7F,IA/IG,QAAA,GA+IH,EA/I6C,OAAO,IAAA,CAAK,YAAC,CAAY,QAAC,CAAQ,IAAC,CAAI,CAAC,EAAC;;;;;IAoJtF,IAjJG,YAAA,GAiJH,EAjJiD,OAAO,IAAA,CAAK,YAAC,CAAY,YAAC,CAAY,IAAC,CAAI,CAAC,EAAC;;;;IAqJ9F,IAnJG,QAAA,GAmJH;QACI,IAAI,CAnJC,IAAC,CAAI,SAAC,EAAU;YAoJnB,IAAI,CAnJC,SAAC,GAAW,iBAAA,CAAkB,IAAC,CAAI,MAAC,CAAM,CAAC;SAoJjD;QACD,OAnJO,IAAA,CAAK,SAAC,CAAS;KAoJvB;;;;IAIH,IArJG,aAAA,GAqJH;QACI,IAAI,CArJC,IAAC,CAAI,cAAC,EAAe;YAsJxB,IAAI,CArJC,cAAC,GAAgB,iBAAA,CAAkB,IAAC,CAAI,WAAC,CAAW,CAAC;SAsJ3D;QACD,OArJO,IAAA,CAAK,cAAC,CAAc;KAsJ5B;;;;IAIH,QAvJG,GAuJH;QACI,uBAvJM,GAAA,GAAM,IAAA,CAAK,GAAC,CAAG,GAAC,CAAG,OAAC,IAAU,OAAA,CAAQ,QAAC,EAAQ,CAAE,CAAC,IAAC,CAAI,GAAC,CAAG,CAAC;QAwJlE,uBAvJM,OAAA,GAAU,IAAA,CAAK,YAAC,GAAc,IAAA,CAAK,YAAC,CAAY,IAAC,GAAM,EAAA,CAAG;QAwJhE,OAvJO,CAuJX,WAAA,EAvJW,GAAe,CAuJ1B,SAAA,EAvJ6B,OAAY,CAuJzC,EAAA,CAvJgD,CAAI;KAwJjD;CACF;AAED,AA8EA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,AAAA,MAAA,mBArOC,SAAA,IAAA,CAqOD;;;;;;IAMA,WAAA,CAvOa,GAAK,EAAQ,IAAsC,EAuOhE;QAEI,KAAK,CAAC,IAAI,CAAC,CAAC;QAzOH,IAAb,CAAA,GAAa,GAAA,GAAA,CAAK;QA0Od,cAAc,CAAsB,IAAI,EAAE,IAAI,CAAC,CAAC;KACjD;;;;IAIH,QA1OG,GA0OH,EA1OuB,OAAO,aAAA,CAAc,IAAC,CAAI,KAAC,CAAK,CAAC,EAAC;CA2OxD;AAED,AAQA;;;;;;AAMA,SAAA,cAAA,CAxPC,KAAA,EAAA,IAAA,EAwPD;IACE,IAAI,CAxPC,KAAC,CAAK,YAAC,GAAc,KAAA,CAAM;IAyPhC,IAAI,CAxPC,QAAC,CAAQ,OAAC,CAAO,CAAC,IAAI,cAAA,CAAe,KAAC,EAAM,CAAA,CAAE,CAAC,CAAC;CAyPtD;;;;;AAKD,SAAA,aAAA,CA3PC,IAAA,EA2PD;IACE,uBA3PM,CAAA,GAAI,IAAA,CAAK,QAAC,CAAQ,MAAC,GAAQ,CAAA,GAAI,CA2PvC,GAAA,EA3PuC,IAAO,CAAI,QAAC,CAAQ,GAAC,CAAG,aAAC,CAAa,CAAC,IAAC,CAAI,IAAC,CAAI,CA2PxF,GAAA,CA3PyF,GAAO,EAAA,CAAG;IA4PjG,OA3PO,CA2PT,EA3PS,IAAI,CAAI,KAAC,CA2PlB,EA3PuB,CAAG,CA2P1B,CA3P2B,CAAE;CA4P5B;;;;;;;;AAQD,AAAA,SAAA,qBAAA,CA5PC,KAAA,EA4PD;IACE,IAAI,KA5PC,CAAK,QAAC,EAAS;QA6PlB,uBA5PM,eAAA,GAAkB,KAAA,CAAM,QAAC,CAAQ;QA6PvC,uBA5PM,YAAA,GAAe,KAAA,CAAM,eAAC,CAAe;QA6P3C,KAAK,CA5PC,QAAC,GAAU,YAAA,CAAa;QA6P9B,IAAI,CA5PC,YAAC,CAAY,eAAC,CAAe,WAAC,EAAY,YAAA,CAAa,WAAC,CAAW,EAAE;YA6PxE,EA5PM,KAAC,CAAK,WAAC,GAAY,IAAC,CAAI,YAAC,CAAY,WAAC,CAAW,CAAC;SA6PzD;QACD,IAAI,eA5PC,CAAe,QAAC,KAAY,YAAA,CAAa,QAAC,EAAS;YA6PtD,EA5PM,KAAC,CAAK,QAAC,GAAS,IAAC,CAAI,YAAC,CAAY,QAAC,CAAQ,CAAC;SA6PnD;QACD,IAAI,CA5PC,YAAC,CAAY,eAAC,CAAe,MAAC,EAAO,YAAA,CAAa,MAAC,CAAM,EAAE;YA6P9D,EA5PM,KAAC,CAAK,MAAC,GAAO,IAAC,CAAI,YAAC,CAAY,MAAC,CAAM,CAAC;SA6P/C;QACD,IAAI,CA5PC,kBAAC,CAAkB,eAAC,CAAe,GAAC,EAAI,YAAA,CAAa,GAAC,CAAG,EAAE;YA6P9D,EA5PM,KAAC,CAAK,GAAC,GAAI,IAAC,CAAI,YAAC,CAAY,GAAC,CAAG,CAAC;SA6PzC;QACD,IAAI,CA5PC,YAAC,CAAY,eAAC,CAAe,IAAC,EAAK,YAAA,CAAa,IAAC,CAAI,EAAE;YA6P1D,EA5PM,KAAC,CAAK,IAAC,GAAK,IAAC,CAAI,YAAC,CAAY,IAAC,CAAI,CAAC;SA6P3C;KACF;SA5PM;QA6PL,KAAK,CA5PC,QAAC,GAAU,KAAA,CAAM,eAAC,CAAe;;QA+PvC,EA5PM,KAAC,CAAK,IAAC,GAAK,IAAC,CAAI,KAAC,CAAK,eAAC,CAAe,IAAC,CAAI,CAAC;KA6PpD;CACF;;;;;;AAMD,AAAA,SAAA,yBAAA,CACI,CAAyB,EAAE,CAAyB,EADxD;IAEE,uBA/PM,cAAA,GAAiB,YAAA,CAAa,CAAC,CAAC,MAAC,EAAO,CAAA,CAAE,MAAC,CAAM,IAAI,aAAA,CAAc,CAAC,CAAC,GAAC,EAAI,CAAA,CAAE,GAAC,CAAG,CAAC;IAgQvF,uBA/PM,eAAA,GAAkB,CAAA,CAAE,CAAC,MAAC,KAAU,CAAA,CAAE,CAAC,MAAC,CAAM;IAiQhD,OA/PO,cAAA,IAAkB,CAAA,eAAE;SAgQtB,CA/PC,CAAC,CAAC,MAAC,IAAS,yBAAA,CAA0B,CAAC,CAAC,MAAC,qBAAO,CAAA,CAAE,MAAC,GAAQ,CAAC,CAAC;CAgQpE;;ADjpBD;;;;;;;AASA,AAGA,AACA,AACA;;;;;;AAMA,AAAA,SAAA,iBAAA,CACI,kBAAsC,EAAE,IAAyB,EACjE,SAAsB,EAF1B;IAGE,uBANM,IAAA,GAAO,UAAA,CAAW,kBAAC,EAAmB,IAAA,CAAK,KAAC,EAAM,SAAA,GAAY,SAAA,CAAU,KAAC,GAAO,SAAA,CAAU,CAAC;IAOjG,OANO,IAAI,WAAA,CAAY,IAAC,EAAK,IAAA,CAAK,CAAC;CAOpC;;;;;;;AAOD,SAAA,UAAA,CACI,kBAAsC,EAAE,IAAsC,EAC9E,SAAoC,EAFxC;;IAIE,IAAI,SAXC,IAAY,kBAAA,CAAmB,gBAAC,CAAgB,IAAC,CAAI,KAAC,EAAM,SAAA,CAAU,KAAC,CAAK,QAAC,CAAQ,EAAE;QAY1F,uBAXM,KAAA,GAAQ,SAAA,CAAU,KAAC,CAAK;QAY9B,KAAK,CAXC,eAAC,GAAiB,IAAA,CAAK,KAAC,CAAK;QAYnC,uBAXM,QAAA,GAAW,qBAAA,CAAsB,kBAAC,EAAmB,IAAA,EAAM,SAAA,CAAU,CAAC;QAY5E,OAXO,IAAI,QAAA,CAAwB,KAAE,EAAM,QAAA,CAAS,CAAC;;KActD;SAXM,IAAA,kBAAK,CAAkB,QAAC,CAAQ,IAAC,CAAI,KAAC,CAAK,EAAE;QAYlD,uBAXM,IAAA,GAYF,EAX8B,kBAAC,CAAkB,QAAC,CAAQ,IAAC,CAAI,KAAC,CAAK,GAAE,KAAC,CAAK;QAYjF,mCAAmC,CAXC,IAAC,EAAK,IAAA,CAAK,CAAC;QAYhD,OAXO,IAAA,CAAK;KAab;SAXM;QAYL,uBAXM,KAAA,GAAQ,oBAAA,CAAqB,IAAC,CAAI,KAAC,CAAK,CAAC;QAY/C,uBAXM,QAAA,GAAW,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,CAAC,IAAI,UAAA,CAAW,kBAAC,EAAmB,CAAA,CAAE,CAAC,CAAC;QAY3E,OAXO,IAAI,QAAA,CAAwB,KAAE,EAAM,QAAA,CAAS,CAAC;KAYtD;CACF;;;;;;AAMD,SAAA,mCAAA,CACI,IAAsC,EAAE,MAAgC,EAD5E;IAEE,IAAI,IAfC,CAAI,KAAC,CAAK,WAAC,KAAe,MAAA,CAAO,KAAC,CAAK,WAAC,EAAY;QAgBvD,MAfM,IAAI,KAAA,CAAM,uEAAC,CAAuE,CAAC;KAgB1F;IACD,IAAI,IAfC,CAAI,QAAC,CAAQ,MAAC,KAAU,MAAA,CAAO,QAAC,CAAQ,MAAC,EAAO;QAgBnD,MAfM,IAAI,KAAA,CAAM,4EAAC,CAA4E,CAAC;KAgB/F;IACD,MAAM,CAfC,KAAC,CAAK,eAAC,GAAiB,IAAA,CAAK,KAAC,CAAK;IAgB1C,KAAK,qBAfI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,IAAA,CAAK,QAAC,CAAQ,MAAC,EAAO,EAAA,CAAG,EAAE;QAgB7C,mCAAmC,CAfC,IAAC,CAAI,QAAC,CAAQ,CAAC,CAAC,EAAE,MAAA,CAAO,QAAC,CAAQ,CAAC,CAAC,CAAC,CAAC;KAgB3E;CACF;;;;;;;AAOD,SAAA,qBAAA,CACI,kBAAsC,EAAE,IAAsC,EAC9E,SAAmC,EAFvC;IAGE,OApBO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,KAAC,IAoB5B;QACI,KAAK,uBApBM,CAAA,IAAK,SAAA,CAAU,QAAC,EAAS;YAqBlC,IAAI,kBApBC,CAAkB,gBAAC,CAAgB,CAAC,CAAC,KAAC,CAAK,QAAC,EAAS,KAAA,CAAM,KAAC,CAAK,EAAE;gBAqBtE,OApBO,UAAA,CAAW,kBAAC,EAAmB,KAAA,EAAO,CAAA,CAAE,CAAC;aAqBjD;SACF;QACD,OApBO,UAAA,CAAW,kBAAC,EAAmB,KAAA,CAAM,CAAC;KAqB9C,CApBC,CAAC;CAqBJ;;;;;AAKD,SAAA,oBAAA,CAvBC,CAAA,EAuBD;IACE,OAvBO,IAAI,cAAA,CAwBP,IAvBI,eAAA,CAAgB,CAAC,CAAC,GAAC,CAAG,EAAE,IAAI,eAAA,CAAgB,CAAC,CAAC,MAAC,CAAM,EAAE,IAAI,eAAA,CAAgB,CAAC,CAAC,WAAC,CAAW,EAwB7F,IAvBI,eAAA,CAAgB,CAAC,CAAC,QAAC,CAAQ,EAAE,IAAI,eAAA,CAAgB,CAAC,CAAC,IAAC,CAAI,EAAE,CAAA,CAAE,MAAC,EAAO,CAAA,CAAE,SAAC,EAAU,CAAA,CAAE,CAAC;CAwB7F;;ADnGD;;;;;;;AAUA,AACA,AACA,AACA;;;;;;;;AAQA,AAAA,SAAA,aAAA,CACI,KAAqB,EAAE,OAAgB,EAAE,QAAe,EAAE,WAAmB,EAC7E,QAAgB,EAFpB;IAGE,IAAI,QARC,CAAQ,MAAC,KAAU,CAAA,EAAG;QASzB,OARO,IAAA,CAAK,OAAC,CAAO,IAAC,EAAK,OAAA,CAAQ,IAAC,EAAK,OAAA,EAAS,WAAA,EAAa,QAAA,CAAS,CAAC;KASzE;IAED,uBARM,GAAA,GAAM,iBAAA,CAAkB,QAAC,CAAQ,CAAC;IAUxC,IAAI,GARC,CAAG,MAAC,EAAM,EAAG;QAShB,OARO,IAAA,CAAK,OAAC,CAAO,IAAC,EAAK,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,EAAE,OAAA,EAAS,WAAA,EAAa,QAAA,CAAS,CAAC;KASxF;IAED,uBARM,gBAAA,GAAmB,oBAAA,CAAqB,GAAC,EAAI,OAAA,EAAS,KAAA,CAAM,CAAC;IAUnE,uBARM,YAAA,GAAe,gBAAA,CAAiB,eAAC;QASnC,0BAA0B,CACtB,gBAAgB,CARC,YAAC,EAAa,gBAAA,CAAiB,KAAC,EAAM,GAAA,CAAI,QAAC,CAAQ;QASxE,kBAAkB,CARC,gBAAC,CAAgB,YAAC,EAAa,gBAAA,CAAiB,KAAC,EAAM,GAAA,CAAI,QAAC,CAAQ,CAAC;IAS5F,OARO,IAAA,CAAK,gBAAC,CAAgB,YAAC,EAAa,YAAA,EAAc,OAAA,EAAS,WAAA,EAAa,QAAA,CAAS,CAAC;CAS1F;;;;;AAKD,SAAA,cAAA,CAXC,OAAA,EAWD;IACE,OAXO,OAAO,OAAA,KAAY,QAAA,IAAY,OAAA,IAAW,IAAA,IAAQ,CAAA,OAAE,CAAO,OAAC,IAAU,CAAA,OAAE,CAAO,WAAC,CAAW;CAYnG;;;;;;;;;AASD,SAAA,IAAA,CACI,eAAgC,EAAE,eAAgC,EAAE,OAAgB,EACpF,WAAmB,EAAE,QAAgB,EAFzC;IAGE,qBAlBI,EAAA,GAAU,EAAA,CAAG;IAmBjB,IAAI,WAlBC,EAAY;QAmBf,OAAO,CAlBC,WAAC,EAAY,CAAA,KAAQ,EAAK,IAAM,KAkB5C;YACM,EAAE,CAlBC,IAAC,CAAI,GAAG,KAAA,CAAM,OAAC,CAAO,KAAC,CAAK,GAAG,KAAA,CAAM,GAAC,CAAG,CAAC,CAAG,KAAQ,CAkB9D,EAlB8D,CAAI,CAkBlE,CAlBmE,CAAE,GAAG,CAkBxE,EAlBwE,KAAI,CAkB5E,CAlBiF,CAAE;SAmB9E,CAlBC,CAAC;KAmBJ;IAED,IAAI,OAlBC,CAAO,IAAC,KAAQ,eAAA,EAAiB;QAmBpC,OAlBO,IAAI,OAAA,CAAQ,eAAC,EAAgB,EAAA,EAAI,QAAA,CAAS,CAAC;KAmBnD;IAED,OAlBO,IAAI,OAAA,CAAQ,cAAC,CAAc,OAAC,CAAO,IAAC,EAAK,eAAA,EAAiB,eAAA,CAAgB,EAAE,EAAA,EAAI,QAAA,CAAS,CAAC;CAmBlG;;;;;;;AAOD,SAAA,cAAA,CACI,OAAwB,EAAE,UAA2B,EACrD,UAA2B,EAF/B;IAGE,uBAvBM,QAAA,GAA6C,EAAA,CAAG;IAwBtD,OAAO,CAvBC,OAAC,CAAO,QAAC,EAAS,CAAA,CAAI,EAAiB,UAAY,KAuB7D;QACI,IAAI,CAvBC,KAAK,UAAA,EAAY;YAwBpB,QAAQ,CAvBC,UAAC,CAAU,GAAG,UAAA,CAAW;SAwBnC;aAvBM;YAwBL,QAAQ,CAvBC,UAAC,CAAU,GAAG,cAAA,CAAe,CAAC,EAAE,UAAA,EAAY,UAAA,CAAW,CAAC;SAwBlE;KACF,CAvBC,CAAC;IAwBH,OAvBO,IAAI,eAAA,CAAgB,OAAC,CAAO,QAAC,EAAS,QAAA,CAAS,CAAC;CAwBxD;AACD,MAAA,UAAA,CAAA;;;;;;IAMA,WAAA,CA1Ba,UAAY,EAAgB,kBAAoB,EAAe,QAAc,EA0B1F;QA1Ba,IAAb,CAAA,UAAa,GAAA,UAAA,CAAY;QAAgB,IAAzC,CAAA,kBAAyC,GAAA,kBAAA,CAAoB;QAAe,IAA5E,CAAA,QAA4E,GAAA,QAAA,CAAc;QA8BtF,IAAI,UAAU,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YACpE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QAC1F,IAAI,aAAa,IAAI,aAAa,KAAKD,MAAI,CAAC,QAAQ,CAAC,EAAE;YACrD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;KACF;;;;IA3BA,MAAA,GAAH;QAgCI,OA/BO,IAAA,CAAK,UAAC,IAAa,IAAA,CAAK,QAAC,CAAQ,MAAC,KAAU,CAAA,IAAK,IAAA,CAAK,QAAC,CAAQ,CAAC,CAAC,IAAI,GAAA,CAAI;KAgCjF;CACF;AAED,AASA;;;;;AAKA,SAAA,iBAAA,CA5CC,QAAA,EA4CD;IACE,IAAI,CA5CC,OAAO,QAAA,CAAS,CAAC,CAAC,KAAK,QAAA,KAAa,QAAA,CAAS,MAAC,KAAU,CAAA,IAAK,QAAA,CAAS,CAAC,CAAC,KAAK,GAAA,EAAK;QA6CrF,OA5CO,IAAI,UAAA,CAAW,IAAC,EAAK,CAAA,EAAG,QAAA,CAAS,CAAC;KA6C1C;IAED,qBA5CI,kBAAA,GAAqB,CAAA,CAAE;IA6C3B,qBA5CI,UAAA,GAAa,KAAA,CAAM;IA8CvB,uBA5CM,GAAA,GAAa,QAAA,CAAS,MAAC,CAAM,CAAC,GAAC,EAAI,GAAA,EAAK,MAAA,KA4ChD;QACI,IAAI,OA5CO,GAAA,KAAQ,QAAA,IAAY,GAAA,IAAO,IAAA,EAAM;YA6C1C,IAAI,GA5CC,CAAG,OAAC,EAAQ;gBA6Cf,uBA5CM,OAAA,GAA8B,EAAA,CAAG;gBA6CvC,OAAO,CA5CC,GAAC,CAAG,OAAC,EAAQ,CAAA,QAAW,EAAK,IAAM,KA4CnD;oBACU,OAAO,CA5CC,IAAC,CAAI,GAAG,OAAO,QAAA,KAAa,QAAA,GAAW,QAAA,CAAS,KAAC,CAAK,GAAC,CAAG,GAAG,QAAA,CAAS;iBA6C/E,CA5CC,CAAC;gBA6CH,OA5CO,CAAA,GAAE,GAAG,EAAI,EAAA,OAAE,EAAO,CAAC,CAAC;aA6C5B;YAED,IAAI,GA5CC,CAAG,WAAC,EAAY;gBA6CnB,OA5CO,CAAA,GAAE,GAAG,EAAI,GAAA,CAAI,WAAC,CAAW,CAAC;aA6ClC;SACF;QAED,IAAI,EA5CE,OAAO,GAAA,KAAQ,QAAA,CAAS,EAAE;YA6C9B,OA5CO,CAAA,GAAE,GAAG,EAAI,GAAA,CAAI,CAAC;SA6CtB;QAED,IAAI,MA5CC,KAAU,CAAA,EAAG;YA6ChB,GAAG,CA5CC,KAAC,CAAK,GAAC,CAAG,CAAC,OAAC,CAAO,CAAC,OAAC,EAAQ,SAAA,KA4CvC;gBACQ,IAAI,SA5CC,IAAY,CAAA,IAAK,OAAA,KAAY,GAAA,EAAK;;iBA8CtC;qBA5CM,IAAA,SAAK,IAAY,CAAA,IAAK,OAAA,KAAY,EAAA,EAAI;oBA6C3C,UAAU,GA5CG,IAAA,CAAK;iBA6CnB;qBA5CM,IAAA,OAAK,KAAW,IAAA,EAAM;oBA6C3B,kBAAkB,EA5CC,CAAE;iBA6CtB;qBA5CM,IAAA,OAAK,IAAU,EAAA,EAAI;oBA6CxB,GAAG,CA5CC,IAAC,CAAI,OAAC,CAAO,CAAC;iBA6CnB;aACF,CA5CC,CAAC;YA8CH,OA5CO,GAAA,CAAI;SA6CZ;QAED,OA5CO,CAAA,GAAE,GAAG,EAAI,GAAA,CAAI,CAAC;KA6CtB,EA5CE,EAAA,CAAG,CAAC;IA8CP,OA5CO,IAAI,UAAA,CAAW,UAAC,EAAW,kBAAA,EAAoB,GAAA,CAAI,CAAC;CA6C5D;AACD,MAAA,QAAA,CAAA;;;;;;IAMA,WAAA,CA/Ca,YAAc,EAAwB,eAAiB,EAAgB,KAAO,EA+C3F;QA/Ca,IAAb,CAAA,YAAa,GAAA,YAAA,CAAc;QAAwB,IAAnD,CAAA,eAAmD,GAAA,eAAA,CAAiB;QAAgB,IAApF,CAAA,KAAoF,GAAA,KAAA,CAAO;KAmDxF;CACF;AAED,AASA;;;;;;AAMA,SAAA,oBAAA,CAjEC,GAAA,EAAA,IAAA,EAAA,KAAA,EAiED;IACE,IAAI,GAjEC,CAAG,UAAC,EAAW;QAkElB,OAjEO,IAAI,QAAA,CAAS,IAAC,CAAI,IAAC,EAAK,IAAA,EAAM,CAAA,CAAE,CAAC;KAkEzC;IAED,IAAI,KAjEC,CAAK,QAAC,CAAQ,cAAC,KAAkB,CAAA,CAAE,EAAE;QAkExC,OAjEO,IAAI,QAAA,CAAS,KAAC,CAAK,QAAC,CAAQ,WAAC,EAAY,IAAA,EAAM,CAAA,CAAE,CAAC;KAkE1D;IAED,uBAjEM,QAAA,GAAW,cAAA,CAAe,GAAC,CAAG,QAAC,CAAQ,CAAC,CAAC,CAAC,GAAG,CAAA,GAAI,CAAA,CAAE;IAkEzD,uBAjEM,KAAA,GAAQ,KAAA,CAAM,QAAC,CAAQ,cAAC,GAAgB,QAAA,CAAS;IAkEvD,OAjEO,gCAAA,CAkEH,KAAK,CAjEC,QAAC,CAAQ,WAAC,EAAY,KAAA,EAAO,GAAA,CAAI,kBAAC,CAAkB,CAAC;CAkEhE;;;;;;;AAOD,SAAA,gCAAA,CACI,KAAsB,EAAE,KAAa,EAAE,kBAA0B,EADrE;IAEE,qBAtEI,CAAA,GAAI,KAAA,CAAM;IAuEd,qBAtEI,EAAA,GAAK,KAAA,CAAM;IAuEf,qBAtEI,EAAA,GAAK,kBAAA,CAAmB;IAuE5B,OAAO,EAtEC,GAAI,EAAA,EAAI;QAuEd,EAAE,IAtEI,EAAA,CAAG;QAuET,CAAC,KAtEG,CAAA,CAAE,MAAC,EAAA,CAAQ;QAuEf,IAAI,CAtEC,CAAC,EAAE;YAuEN,MAtEM,IAAI,KAAA,CAAM,2BAAC,CAA2B,CAAC;SAuE9C;QACD,EAAE,GAtEG,CAAA,CAAE,QAAC,CAAQ,MAAC,CAAM;KAuExB;IACD,OAtEO,IAAI,QAAA,CAAS,CAAC,EAAE,KAAA,EAAO,EAAA,GAAK,EAAA,CAAG,CAAC;CAuExC;;;;;AAKD,SAAA,OAAA,CAzEC,OAAA,EAyED;IACE,IAAI,OAzEO,OAAA,KAAY,QAAA,IAAY,OAAA,IAAW,IAAA,IAAQ,OAAA,CAAQ,OAAC,EAAQ;QA0ErE,OAzEO,OAAA,CAAQ,OAAC,CAAO,cAAC,CAAc,CAAC;KA0ExC;IACD,OAzEO,CAyET,EAzES,OAAI,CAyEb,CAzEoB,CAAE;CA0ErB;;;;;AAKD,SAAA,UAAA,CA5EC,QAAA,EA4ED;IACE,IAAI,EA5EE,OAAO,QAAA,CAAS,CAAC,CAAC,KAAK,QAAA,CAAS;QAAE,OAAO,EAAA,CAAE,cAAC,GAAgB,QAAA,EAAS,CAAC;IA6E5E,IAAI,QA5EC,CAAQ,CAAC,CAAC,CAAC,OAAC,KAAW,SAAA;QAAW,OAAO,EAAA,CAAE,cAAC,GAAgB,QAAA,EAAS,CAAC;IA6E3E,OA5EO,QAAA,CAAS,CAAC,CAAC,CAAC,OAAC,CAAO;CA6E5B;;;;;;;AAOD,SAAA,kBAAA,CACI,YAA6B,EAAE,UAAkB,EAAE,QAAe,EADtE;IAEE,IAAI,CAjFC,YAAC,EAAa;QAkFjB,YAAY,GAjFG,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;KAkF5C;IACD,IAAI,YAjFC,CAAY,QAAC,CAAQ,MAAC,KAAU,CAAA,IAAK,YAAA,CAAa,WAAC,EAAW,EAAG;QAkFpE,OAjFO,0BAAA,CAA2B,YAAC,EAAa,UAAA,EAAY,QAAA,CAAS,CAAC;KAkFvE;IAED,uBAjFM,CAAA,GAAI,YAAA,CAAa,YAAC,EAAa,UAAA,EAAY,QAAA,CAAS,CAAC;IAkF3D,uBAjFM,cAAA,GAAiB,QAAA,CAAS,KAAC,CAAK,CAAC,CAAC,YAAC,CAAY,CAAC;IAkFtD,IAAI,CAjFC,CAAC,KAAC,IAAQ,CAAA,CAAE,SAAC,GAAW,YAAA,CAAa,QAAC,CAAQ,MAAC,EAAO;QAkFzD,uBAjFM,CAAA,GAAI,IAAI,eAAA,CAAgB,YAAC,CAAY,QAAC,CAAQ,KAAC,CAAK,CAAC,EAAE,CAAA,CAAE,SAAC,CAAS,EAAE,EAAA,CAAG,CAAC;QAkF/E,CAAC,CAjFC,QAAC,CAAQ,cAAC,CAAc;YAkFtB,IAjFI,eAAA,CAAgB,YAAC,CAAY,QAAC,CAAQ,KAAC,CAAK,CAAC,CAAC,SAAC,CAAS,EAAE,YAAA,CAAa,QAAC,CAAQ,CAAC;QAkFzF,OAjFO,0BAAA,CAA2B,CAAC,EAAE,CAAA,EAAG,cAAA,CAAe,CAAC;KAkFzD;SAjFM,IAAA,CAAK,CAAC,KAAC,IAAQ,cAAA,CAAe,MAAC,KAAU,CAAA,EAAG;QAkFjD,OAjFO,IAAI,eAAA,CAAgB,YAAC,CAAY,QAAC,EAAS,EAAA,CAAG,CAAC;KAkFvD;SAjFM,IAAA,CAAK,CAAC,KAAC,IAAQ,CAAA,YAAE,CAAY,WAAC,EAAW,EAAG;QAkFjD,OAjFO,qBAAA,CAAsB,YAAC,EAAa,UAAA,EAAY,QAAA,CAAS,CAAC;KAkFlE;SAjFM,IAAA,CAAK,CAAC,KAAC,EAAM;QAkFlB,OAjFO,0BAAA,CAA2B,YAAC,EAAa,CAAA,EAAG,cAAA,CAAe,CAAC;KAkFpE;SAjFM;QAkFL,OAjFO,qBAAA,CAAsB,YAAC,EAAa,UAAA,EAAY,QAAA,CAAS,CAAC;KAkFlE;CACF;;;;;;;AAOD,SAAA,0BAAA,CACI,YAA6B,EAAE,UAAkB,EAAE,QAAe,EADtE;IAEE,IAAI,QAtFC,CAAQ,MAAC,KAAU,CAAA,EAAG;QAuFzB,OAtFO,IAAI,eAAA,CAAgB,YAAC,CAAY,QAAC,EAAS,EAAA,CAAG,CAAC;KAuFvD;SAtFM;QAuFL,uBAtFM,OAAA,GAAU,UAAA,CAAW,QAAC,CAAQ,CAAC;QAuFrC,uBAtFM,QAAA,GAA6C,EAAA,CAAG;QAwFtD,OAAO,CAtFC,OAAC,EAAQ,CAAA,QAAW,EAAK,MAAQ,KAsF7C;YACM,IAAI,QAtFC,KAAY,IAAA,EAAM;gBAuFrB,QAAQ,CAtFC,MAAC,CAAM,GAAG,kBAAA,CAAmB,YAAC,CAAY,QAAC,CAAQ,MAAC,CAAM,EAAE,UAAA,EAAY,QAAA,CAAS,CAAC;aAuF5F;SACF,CAtFC,CAAC;QAwFH,OAAO,CAtFC,YAAC,CAAY,QAAC,EAAS,CAAA,KAAQ,EAAiB,WAAa,KAsFzE;YACM,IAAI,OAtFC,CAAO,WAAC,CAAW,KAAK,SAAA,EAAW;gBAuFtC,QAAQ,CAtFC,WAAC,CAAW,GAAG,KAAA,CAAM;aAuF/B;SACF,CAtFC,CAAC;QAuFH,OAtFO,IAAI,eAAA,CAAgB,YAAC,CAAY,QAAC,EAAS,QAAA,CAAS,CAAC;KAuF7D;CACF;;;;;;;AAOD,SAAA,YAAA,CA3FC,YAAA,EAAA,UAAA,EAAA,QAAA,EA2FD;IACE,qBA3FI,mBAAA,GAAsB,CAAA,CAAE;IA4F5B,qBA3FI,gBAAA,GAAmB,UAAA,CAAW;IA6FlC,uBA3FM,OAAA,GAAU,EAAA,KAAE,EAAM,KAAA,EAAO,SAAA,EAAW,CAAA,EAAG,YAAA,EAAc,CAAA,EAAE,CAAC;IA4F9D,OAAO,gBA3FC,GAAkB,YAAA,CAAa,QAAC,CAAQ,MAAC,EAAO;QA4FtD,IAAI,mBA3FC,IAAsB,QAAA,CAAS,MAAC;YAAO,OAAO,OAAA,CAAQ;QA4F3D,uBA3FM,IAAA,GAAO,YAAA,CAAa,QAAC,CAAQ,gBAAC,CAAgB,CAAC;QA4FrD,uBA3FM,IAAA,GAAO,OAAA,CAAQ,QAAC,CAAQ,mBAAC,CAAmB,CAAC,CAAC;QA4FpD,uBA3FM,IAAA,GA4FF,mBAAmB,GA3FG,QAAA,CAAS,MAAC,GAAQ,CAAA,GAAI,QAAA,CAAS,mBAAC,GAAqB,CAAA,CAAE,GAAG,IAAA,CAAK;QA6FzF,IAAI,gBA3FC,GAAkB,CAAA,IAAK,IAAA,KAAS,SAAA;YAAW,MAAA;QA6FhD,IAAI,IA3FC,IAAO,IAAA,KAAQ,OAAQ,IAAA,KAAS,QAAA,CAAS,IAAI,IAAA,CAAK,OAAC,KAAW,SAAA,EAAW;YA4F5E,IAAI,CA3FC,OAAC,CAAO,IAAC,EAAK,IAAA,EAAM,IAAA,CAAK;gBAAE,OAAO,OAAA,CAAQ;YA4F/C,mBAAmB,IA3FI,CAAA,CAAE;SA4F1B;aA3FM;YA4FL,IAAI,CA3FC,OAAC,CAAO,IAAC,EAAK,EAAA,EAAI,IAAA,CAAK;gBAAE,OAAO,OAAA,CAAQ;YA4F7C,mBAAmB,EA3FC,CAAE;SA4FvB;QACD,gBAAgB,EA3FC,CAAE;KA4FpB;IAED,OA3FO,EAAA,KAAE,EAAM,IAAA,EAAM,SAAA,EAAW,gBAAA,EAAkB,YAAA,EAAc,mBAAA,EAAoB,CAAC;CA4FtF;;;;;;;AAOD,SAAA,qBAAA,CACI,YAA6B,EAAE,UAAkB,EAAE,QAAe,EADtE;IAEE,uBAhGM,KAAA,GAAQ,YAAA,CAAa,QAAC,CAAQ,KAAC,CAAK,CAAC,EAAE,UAAA,CAAW,CAAC;IAkGzD,qBAhGI,CAAA,GAAI,CAAA,CAAE;IAiGV,OAAO,CAhGC,GAAG,QAAA,CAAS,MAAC,EAAO;QAiG1B,IAAI,OAhGO,QAAA,CAAS,CAAC,CAAC,KAAK,QAAA,IAAY,QAAA,CAAS,CAAC,CAAC,CAAC,OAAC,KAAW,SAAA,EAAW;YAiGxE,uBAhGM,QAAA,GAAW,wBAAA,CAAyB,QAAC,CAAQ,CAAC,CAAC,CAAC,OAAC,CAAO,CAAC;YAiG/D,OAhGO,IAAI,eAAA,CAAgB,KAAC,EAAM,QAAA,CAAS,CAAC;SAiG7C;;QAGD,IAAI,CAhGC,KAAK,CAAA,IAAK,cAAA,CAAe,QAAC,CAAQ,CAAC,CAAC,CAAC,EAAE;YAiG1C,uBAhGM,CAAA,GAAI,YAAA,CAAa,QAAC,CAAQ,UAAC,CAAU,CAAC;YAiG5C,KAAK,CAhGC,IAAC,CAAI,IAAI,UAAA,CAAW,CAAC,CAAC,IAAC,EAAK,QAAA,CAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAiGhD,CAAC,EAhGC,CAAE;YAiGJ,SAAS;SACV;QAED,uBAhGM,IAAA,GAAO,OAAA,CAAQ,QAAC,CAAQ,CAAC,CAAC,CAAC,CAAC;QAiGlC,uBAhGM,IAAA,GAAO,CAAA,CAAE,GAAG,QAAA,CAAS,MAAC,GAAQ,CAAA,IAAK,QAAA,CAAS,CAAC,GAAG,CAAA,CAAE,GAAG,IAAA,CAAK;QAiGhE,IAAI,IAhGC,IAAO,IAAA,IAAQ,cAAA,CAAe,IAAC,CAAI,EAAE;YAiGxC,KAAK,CAhGC,IAAC,CAAI,IAAI,UAAA,CAAW,IAAC,EAAK,SAAA,CAAU,IAAC,CAAI,CAAC,CAAC,CAAC;YAiGlD,CAAC,IAhGI,CAAA,CAAE;SAiGR;aAhGM;YAiGL,KAAK,CAhGC,IAAC,CAAI,IAAI,UAAA,CAAW,IAAC,EAAK,EAAA,CAAG,CAAC,CAAC;YAiGrC,CAAC,EAhGC,CAAE;SAiGL;KACF;IACD,OAhGO,IAAI,eAAA,CAAgB,KAAC,EAAM,EAAA,CAAG,CAAC;CAiGvC;;;;;AAKD,SAAA,wBAAA,CAnGC,OAAA,EAmGD;IACE,uBAnGM,QAAA,GAA6C,EAAA,CAAG;IAoGtD,OAAO,CAnGC,OAAC,EAAQ,CAAA,QAAW,EAAK,MAAQ,KAmG3C;QACI,IAAI,QAnGC,KAAY,IAAA,EAAM;YAoGrB,QAAQ,CAnGC,MAAC,CAAM,GAAG,qBAAA,CAAsB,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,EAAE,CAAA,EAAG,QAAA,CAAS,CAAC;SAoGpF;KACF,CAnGC,CAAC;IAoGH,OAnGO,QAAA,CAAS;CAoGjB;;;;;AAKD,SAAA,SAAA,CAtGC,MAAA,EAsGD;IACE,uBAtGM,GAAA,GAA+B,EAAA,CAAG;IAuGxC,OAAO,CAtGC,MAAC,EAAO,CAAA,CAAI,EAAK,CAAG,KAAW,GAAA,CAAI,CAAC,CAAC,GAAG,CAsGlD,EAtGkD,CAAI,CAsGtD,CAtGuD,CAAE,CAAC;IAuGxD,OAtGO,GAAA,CAAI;CAuGZ;;;;;;;AAOD,SAAA,OAAA,CA3GC,IAAA,EAAA,MAAA,EAAA,OAAA,EA2GD;IACE,OA3GO,IAAA,IAAQ,OAAA,CAAQ,IAAC,IAAO,YAAA,CAAa,MAAC,EAAO,OAAA,CAAQ,UAAC,CAAU,CAAC;CA4GzE;;ADtaD;;;;;;;AAUA,AAEA,AAGA,AACA,AACA,AACA,AACA,AACA,MAAAF,SAAA,CAAA;CAAgB;;;;;;;;AAQhB,AAAA,SAAA,SAAA,CACI,iBAAkC,EAAE,MAAc,EAAE,OAAgB,EACpE,GAAW,EAFf;IAGE,OANO,IAAI,UAAA,CAAW,iBAAC,EAAkB,MAAA,EAAQ,OAAA,EAAS,GAAA,CAAI,CAAC,SAAC,EAAS,CAAE;CAO5E;AACD,MAAA,UAAA,CAAA;;;;;;;IAOA,WAAA,CAVc,iBAA8B,EAAa,MAAQ,EAAgB,OAAS,EAC5E,GAAK,EASnB;QAVc,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAA8B;QAAa,IAAzD,CAAA,MAAyD,GAAA,MAAA,CAAQ;QAAgB,IAAjF,CAAA,OAAiF,GAAA,OAAA,CAAS;QAC5E,IAAd,CAAA,GAAc,GAAA,GAAA,CAAK;KAAO;;;;IAiB1B,SAfG,GAeH;QACI,IAfI;YAgBF,uBAfM,gBAAA,GAAmBD,OAAA,CAAM,IAAC,CAAI,OAAC,CAAO,IAAC,EAAK,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,MAAC,CAAM,CAAC,YAAC,CAAY;YAiBpF,uBAfM,QAAA,GAAW,IAAA,CAAK,mBAAC,CAAmB,IAAC,CAAI,MAAC,EAAO,gBAAA,EAAkB,cAAA,CAAe,CAAC;YAiBzF,uBAfM,IAAA,GAAO,IAAI,sBAAA,CAgBb,EAAE,EAfE,MAAA,CAAO,MAAC,CAAM,EAAC,CAAE,EAAE,MAAA,CAAO,MAAC,CAAM,IAAC,CAAI,OAAC,CAAO,WAAC,CAAW,qBAAE,IAAA,CAAK,OAAC,CAAO,QAAC,IAgB9E,EAAE,EAfE,cAAA,EAAgB,IAAA,CAAK,iBAAC,EAAkB,IAAA,EAAM,IAAA,CAAK,OAAC,CAAO,IAAC,EAAK,CAAA,CAAE,EAAE,EAAA,CAAG,CAAC;YAiBjF,uBAfM,QAAA,GAAW,IAAI,QAAA,CAAgC,IAAE,EAAK,QAAA,CAAS,CAAC;YAgBtE,uBAfM,UAAA,GAAa,IAAI,mBAAA,CAAoB,IAAC,CAAI,GAAC,EAAI,QAAA,CAAS,CAAC;YAgB/D,IAAI,CAfC,oBAAC,CAAoB,UAAC,CAAU,KAAC,CAAK,CAAC;YAgB5C,OAfO,EAAA,CAAG,UAAE,CAAU,CAAC;SAiBxB;QAfC,OAAA,CAAQ,EAAE;YAgBV,OAfO,IAAI,UAAA,CAgBP,CAAC,GAfkC,KAAK,GAAA,CAAI,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC;SAgB3D;KACF;;;;;IAKH,oBAlBG,CAAA,SAAA,EAkBH;QACI,uBAlBM,KAAA,GAAQ,SAAA,CAAU,KAAC,CAAK;QAoB9B,uBAlBM,CAAA,GAAI,0BAAA,CAA2B,KAAC,CAAK,CAAC;QAmB5C,KAAK,CAlBC,MAAC,GAAQ,MAAA,CAAO,MAAC,CAAM,CAAC,CAAC,MAAC,CAAM,CAAC;QAmBvC,KAAK,CAlBC,IAAC,GAAM,MAAA,CAAO,MAAC,CAAM,CAAC,CAAC,IAAC,CAAI,CAAC;QAoBnC,SAAS,CAlBC,QAAC,CAAQ,OAAC,CAAO,CAAC,IAAI,IAAA,CAAK,oBAAC,CAAoB,CAAC,CAAC,CAAC,CAAC;KAmB/D;;;;;;;IAOH,mBAvBG,CAAA,MAAA,EAAA,YAAA,EAAA,MAAA,EAuBH;QAEI,IAAI,YAvBC,CAAY,QAAC,CAAQ,MAAC,KAAU,CAAA,IAAK,YAAA,CAAa,WAAC,EAAW,EAAG;YAwBpE,OAvBO,IAAA,CAAK,eAAC,CAAe,MAAC,EAAO,YAAA,CAAa,CAAC;SAwBnD;QAED,OAvBO,IAAA,CAAK,cAAC,CAAc,MAAC,EAAO,YAAA,EAAc,YAAA,CAAa,QAAC,EAAS,MAAA,CAAO,CAAC;KAwBjF;;;;;;IAMH,eA3BG,CAAA,MAAA,EAAA,YAAA,EA2BH;QAEI,uBA3BM,QAAA,GAAW,oBAAA,CA4Bb,YAAY,EA3BE,CAAA,KAAE,EAAM,WAAA,KAAgB,IAAA,CAAK,mBAAC,CAAmB,MAAC,EAAO,KAAA,EAAO,WAAA,CAAY,CAAC,CAAC;QA4BhG,yBAAyB,CA3BC,QAAC,CAAQ,CAAC;QA4BpC,2BAA2B,CA3BC,QAAC,CAAQ,CAAC;QA4BtC,OA3BO,QAAA,CAAS;KA4BjB;;;;;;;;IAQH,cAjCG,CAkCG,MAAe,EAAE,YAA6B,EAAE,QAAsB,EACtE,MAAc,EAFpB;QAGI,KAAK,uBAjCM,CAAA,IAAK,MAAA,EAAQ;YAkCtB,IAjCI;gBAkCF,OAjCO,IAAA,CAAK,0BAAC,CAA0B,CAAC,EAAE,YAAA,EAAc,QAAA,EAAU,MAAA,CAAO,CAAC;aAkC3E;YAjCC,OAAA,CAAQ,EAAE;gBAkCV,IAAI,EAjCE,CAAC,YAAYC,SAAA,CAAQ;oBAAE,MAAM,CAAA,CAAE;aAkCtC;SACF;QACD,IAAI,IAjCC,CAAI,gBAAC,CAAgB,YAAC,EAAa,QAAA,EAAU,MAAA,CAAO,EAAE;YAkCzD,OAjCO,EAAA,CAAG;SAkCX;QAED,MAjCM,IAAIA,SAAA,EAAQ,CAAE;KAkCrB;;;;;;;IA/BA,gBAAA,CAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAH;QAwCI,OAtCO,QAAA,CAAS,MAAC,KAAU,CAAA,IAAK,CAAA,YAAE,CAAY,QAAC,CAAQ,MAAC,CAAM,CAAC;KAuChE;;;;;;;;IAQH,0BA5CG,CA6CG,KAAY,EAAE,UAA2B,EAAE,QAAsB,EACjE,MAAc,EAFpB;QAGI,IAAI,KA5CC,CAAK,UAAC;YAAW,MAAM,IAAIA,SAAA,EAAQ,CAAE;QA8C1C,IAAI,CA5CC,KAAC,CAAK,MAAC,IAAS,cAAA,MAAoB,MAAA;YAAQ,MAAM,IAAIA,SAAA,EAAQ,CAAE;QA8CrE,IAAI,KA5CC,CAAK,IAAC,KAAQ,IAAA,EAAM;YA6CvB,uBA5CM,MAAA,GAAS,QAAA,CAAS,MAAC,GAAQ,CAAA,GAAE,EAAEE,MAAA,CAAK,QAAC,CAAQ,GAAG,UAAC,GAAY,EAAA,CAAG;YA6CtE,uBA5CM,QAAA,GAAW,IAAI,sBAAA,CA6CjB,QAAQ,EA5CE,MAAA,EAAQ,MAAA,CAAO,MAAC,CAAM,IAAC,CAAI,OAAC,CAAO,WAAC,CAAW,qBAAE,IAAA,CAAK,OAAC,CAAO,QAAC,IA6CzE,OAAO,CA5CC,KAAC,CAAK,EAAE,MAAA,qBAAQ,KAAA,CAAM,SAAC,IAAY,KAAA,EAAO,qBAAA,CAAsB,UAAC,CAAU,EA6CnF,iBAAiB,CA5CC,UAAC,CAAU,GAAG,QAAA,CAAS,MAAC,EAAO,UAAA,CAAW,KAAC,CAAK,CAAC,CAAC;YA6CxE,OA5CO,CAAA,IAAK,QAAA,CAAgC,QAAE,EAAS,EAAA,CAAG,CAAC,CAAC;SA6C7D;QAED,MA5CM,EAAA,gBAAE,EAAiB,UAAA,EAAY,SAAA,EAAU,GAAGD,OAAA,CAAM,UAAC,EAAW,KAAA,EAAO,QAAA,CAAS,CAAC;QA6CrF,uBA5CM,iBAAA,GAAoB,QAAA,CAAS,KAAC,CAAK,SAAC,CAAS,CAAC;QA6CpD,uBA5CM,WAAA,GAAc,cAAA,CAAe,KAAC,CAAK,CAAC;QA8C1C,MA5CM,EAAA,YAAE,EAAa,cAAA,EAAe,GA6ChCF,OAAK,CA5CC,UAAC,EAAW,gBAAA,EAAkB,iBAAA,EAAmB,WAAA,CAAY,CAAC;QA8CxE,uBA5CM,QAAA,GAAW,IAAI,sBAAA,CA6CjB,gBAAgB,EA5CE,UAAA,EAAY,MAAA,CAAO,MAAC,CAAM,IAAC,CAAI,OAAC,CAAO,WAAC,CAAW,qBA6CrE,IAAI,CA5CC,OAAC,CAAO,QAAC,IAAW,OAAA,CAAQ,KAAC,CAAK,EAAE,MAAA,qBAAQ,KAAA,CAAM,SAAC,IAAY,KAAA,EA6CpE,qBAAqB,CA5CC,UAAC,CAAU,EAAE,iBAAA,CAAkB,UAAC,CAAU,GAAG,gBAAA,CAAiB,MAAC,EA6CrF,UAAU,CA5CC,KAAC,CAAK,CAAC,CAAC;QA+CvB,IAAI,cA5CC,CAAc,MAAC,KAAU,CAAA,IAAK,YAAA,CAAa,WAAC,EAAW,EAAG;YA6C7D,uBA5CM,QAAA,GAAW,IAAA,CAAK,eAAC,CAAe,WAAC,EAAY,YAAA,CAAa,CAAC;YA6CjE,OA5CO,CAAA,IAAK,QAAA,CAAgC,QAAE,EAAS,QAAA,CAAS,CAAC,CAAC;SA6CnE;QAED,IAAI,WA5CC,CAAW,MAAC,KAAU,CAAA,IAAK,cAAA,CAAe,MAAC,KAAU,CAAA,EAAG;YA6C3D,OA5CO,CAAA,IAAK,QAAA,CAAgC,QAAE,EAAS,EAAA,CAAG,CAAC,CAAC;SA6C7D;QAED,uBA5CM,QAAA,GAAW,IAAA,CAAK,cAAC,CAAc,WAAC,EAAY,YAAA,EAAc,cAAA,EAAgB,cAAA,CAAe,CAAC;QA6ChG,OA5CO,CAAA,IAAK,QAAA,CAAgC,QAAE,EAAS,QAAA,CAAS,CAAC,CAAC;KA6CnE;CACF;AAED,AAWA;;;;AAIA,SAAA,2BAAA,CA3DC,KAAA,EA2DD;IACE,KAAK,CA3DC,IAAC,CAAI,CAAC,CAAC,EAAE,CAAA,KA2DjB;QACI,IAAI,CA3DC,CAAC,KAAC,CAAK,MAAC,KAAU,cAAA;YAAgB,OAAO,CAAA,CAAE,CAAC;QA4DjD,IAAI,CA3DC,CAAC,KAAC,CAAK,MAAC,KAAU,cAAA;YAAgB,OAAO,CAAA,CAAE;QA4DhD,OA3DO,CAAA,CAAE,KAAC,CAAK,MAAC,CAAM,aAAC,CAAa,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,CAAC;KA4DrD,CA3DC,CAAC;CA4DJ;;;;;AAKD,SAAA,cAAA,CA9DC,KAAA,EA8DD;IACE,IAAI,KA9DC,CAAK,QAAC,EAAS;QA+DlB,OA9DO,KAAA,CAAM,QAAC,CAAQ;KA+DvB;IAED,IAAI,KA9DC,CAAK,YAAC,EAAa;QA+DtB,OAAuB,EA9DhB,KAAA,CAAM,aAAC,GAAe,MAAC,CAAM;KA+DrC;IAED,OA9DO,EAAA,CAAG;CA+DX;;;;;;;AAOD,SAAAE,OAAA,CAnEC,YAAA,EAAA,KAAA,EAAA,QAAA,EAmED;IACE,IAAI,KAnEC,CAAK,IAAC,KAAQ,EAAA,EAAI;QAoErB,IAAI,KAnEC,CAAK,SAAC,KAAa,MAAA,KAAU,YAAE,CAAY,WAAC,EAAW,IAAK,QAAA,CAAS,MAAC,GAAQ,CAAA,CAAE,EAAE;YAoErF,MAnEM,IAAID,SAAA,EAAQ,CAAE;SAoErB;QAED,OAnEO,EAAA,gBAAE,EAAiB,EAAA,EAAI,SAAA,EAAW,CAAA,EAAG,UAAA,EAAY,EAAA,EAAG,CAAC;KAoE7D;IAED,uBAnEM,OAAA,GAAU,KAAA,CAAM,OAAC,IAAU,iBAAA,CAAkB;IAoEnD,uBAnEM,GAAA,GAAM,OAAA,CAAQ,QAAC,EAAS,YAAA,EAAc,KAAA,CAAM,CAAC;IAoEnD,IAAI,CAnEC,GAAC;QAAI,MAAM,IAAIA,SAAA,EAAQ,CAAE;IAqE9B,uBAnEM,SAAA,GAAmC,EAAA,CAAG;IAoE5C,OAAO,oBAnEC,GAAC,CAAG,SAAC,IAAY,CAAA,CAAI,EAAY,CAAG,KAmE9C,EAnE2D,SAAA,CAAU,CAAC,CAAC,GAAG,CAAA,CAAE,IAAC,CAAI,EAAC,CAAE,CAAC;IAoEnF,uBAnEM,UAAA,GAmER,MAAA,CAAA,MAAA,CAAA,EAAA,EAnEuB,SAAG,EAAU,GAAI,CAAG,QAAC,CAAQ,GAAC,CAAG,QAAC,CAAQ,MAAC,GAAQ,CAAA,CAAE,CAAC,UAAC,CAAU,CAAC;IAqEvF,OAnEO,EAAA,gBAAE,EAAiB,GAAA,CAAI,QAAC,EAAS,SAAA,EAAW,GAAA,CAAI,QAAC,CAAQ,MAAC,EAAO,UAAA,EAAW,CAAC;CAoErF;;;;;AAKD,SAAA,yBAAA,CAtEC,KAAA,EAsED;IACE,uBAtEM,KAAA,GAA+C,EAAA,CAAG;IAuExD,KAAK,CAtEC,OAAC,CAAO,CAAC,IAsEjB;QACI,uBAtEM,uBAAA,GAA0B,KAAA,CAAM,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,CAAC;QAuEtD,IAAI,uBAtEC,EAAwB;YAuE3B,uBAtEM,CAAA,GAAI,uBAAA,CAAwB,GAAC,CAAG,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,QAAC,EAAQ,CAAE,CAAC,IAAC,CAAI,GAAC,CAAG,CAAC;YAuEvE,uBAtEM,CAAA,GAAI,CAAA,CAAE,KAAC,CAAK,GAAC,CAAG,GAAC,CAAG,CAAC,IAAI,CAAA,CAAE,QAAC,EAAQ,CAAE,CAAC,IAAC,CAAI,GAAC,CAAG,CAAC;YAuEvD,MAtEM,IAAI,KAAA,CAAM,CAsEtB,gDAAA,EAtEuB,CAAmD,CAsE1E,OAAA,EAtE2E,CAAU,CAsErF,EAAA,CAtEsF,CAAI,CAAC;SAuEtF;QACD,KAAK,CAtEC,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,GAAG,CAAA,CAAE,KAAC,CAAK;KAuEjC,CAtEC,CAAC;CAuEJ;;;;;AAKD,SAAA,qBAAA,CAzEC,YAAA,EAyED;IACE,qBAzEI,CAAA,GAAI,YAAA,CAAa;IA0ErB,OAAO,CAzEC,CAAC,cAAC,EAAe;QA0EvB,CAAC,GAzEG,CAAA,CAAE,cAAC,CAAc;KA0EtB;IACD,OAzEO,CAAA,CAAE;CA0EV;;;;;AAKD,SAAA,iBAAA,CA5EC,YAAA,EA4ED;IACE,qBA5EI,CAAA,GAAI,YAAA,CAAa;IA6ErB,qBA5EI,GAAA,IAAM,CAAE,CAAC,kBAAC,GAAoB,CAAA,CAAE,kBAAC,GAAoB,CAAA,CAAE,CAAC;IA6E5D,OAAO,CA5EC,CAAC,cAAC,EAAe;QA6EvB,CAAC,GA5EG,CAAA,CAAE,cAAC,CAAc;QA6ErB,GAAG,KA5EI,CAAE,CAAC,kBAAC,GAAoB,CAAA,CAAE,kBAAC,GAAoB,CAAA,CAAE,CAAC;KA6E1D;IACD,OA5EO,GAAA,GAAM,CAAA,CAAE;CA6EhB;;;;;;;;AAQD,SAAAD,OAAA,CACI,YAA6B,EAAE,gBAA8B,EAAE,cAA4B,EAC3F,MAAe,EAFnB;IAGE,IAAI,cAlFC,CAAc,MAAC,GAAQ,CAAA;QAmFxB,wCAAwC,CAlFC,YAAC,EAAa,cAAA,EAAgB,MAAA,CAAO,EAAE;QAmFlF,uBAlFM,CAAA,GAAI,IAAI,eAAA,CAmFV,gBAAgB,EAlFE,2BAAA,CAmFI,YAAY,EAlFE,gBAAA,EAAkB,MAAA,EAmFhC,IAlFI,eAAA,CAAgB,cAAC,EAAe,YAAA,CAAa,QAAC,CAAQ,CAAC,CAAC,CAAC;QAmFvF,CAAC,CAlFC,cAAC,GAAgB,YAAA,CAAa;QAmFhC,CAAC,CAlFC,kBAAC,GAAoB,gBAAA,CAAiB,MAAC,CAAM;QAmF/C,OAlFO,EAAA,YAAE,EAAa,CAAA,EAAG,cAAA,EAAgB,EAAA,EAAG,CAAC;KAmF9C;IAED,IAAI,cAlFC,CAAc,MAAC,KAAU,CAAA;QAmF1B,wBAAwB,CAlFC,YAAC,EAAa,cAAA,EAAgB,MAAA,CAAO,EAAE;QAmFlE,uBAlFM,CAAA,GAAI,IAAI,eAAA,CAmFV,YAAY,CAlFC,QAAC,EAAS,+BAAA,CAmFI,YAAY,EAlFE,cAAA,EAAgB,MAAA,EAAQ,YAAA,CAAa,QAAC,CAAQ,CAAC,CAAC;QAmF7F,CAAC,CAlFC,cAAC,GAAgB,YAAA,CAAa;QAmFhC,CAAC,CAlFC,kBAAC,GAAoB,gBAAA,CAAiB,MAAC,CAAM;QAmF/C,OAlFO,EAAA,YAAE,EAAa,CAAA,EAAG,cAAA,EAAe,CAAC;KAmF1C;IAED,uBAlFM,CAAA,GAAI,IAAI,eAAA,CAAgB,YAAC,CAAY,QAAC,EAAS,YAAA,CAAa,QAAC,CAAQ,CAAC;IAmF5E,CAAC,CAlFC,cAAC,GAAgB,YAAA,CAAa;IAmFhC,CAAC,CAlFC,kBAAC,GAAoB,gBAAA,CAAiB,MAAC,CAAM;IAmF/C,OAlFO,EAAA,YAAE,EAAa,CAAA,EAAG,cAAA,EAAe,CAAC;CAmF1C;;;;;;;;AAQD,SAAA,+BAAA,CACI,YAA6B,EAAE,cAA4B,EAAE,MAAe,EAC5E,QAA2C,EAF/C;IAGE,uBAxFM,GAAA,GAAyC,EAAA,CAAG;IAyFlD,KAAK,uBAxFM,CAAA,IAAK,MAAA,EAAQ;QAyFtB,IAAI,cAxFC,CAAc,YAAC,EAAa,cAAA,EAAgB,CAAA,CAAE,IAAI,CAAA,QAAE,CAAQD,WAAC,CAAS,CAAC,CAAC,CAAC,EAAE;YAyF9E,uBAxFM,CAAA,GAAI,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;YAyFtC,CAAC,CAxFC,cAAC,GAAgB,YAAA,CAAa;YAyFhC,CAAC,CAxFC,kBAAC,GAAoB,YAAA,CAAa,QAAC,CAAQ,MAAC,CAAM;YAyFpD,GAAG,CAxFCA,WAAC,CAAS,CAAC,CAAC,CAAC,GAAG,CAAA,CAAE;SAyFvB;KACF;IACD,OAAF,MAAA,CAAA,MAAA,CAAA,EAAA,EAxFW,QAAG,EAAS,GAAI,CAwF3B,CAxF+B;CAyF9B;;;;;;;;AAQD,SAAA,2BAAA,CACI,YAA6B,EAAE,gBAA8B,EAAE,MAAe,EAC9E,cAA+B,EAFnC;IAGE,uBA9FM,GAAA,GAAyC,EAAA,CAAG;IA+FlD,GAAG,CA9FC,cAAC,CAAc,GAAG,cAAA,CAAe;IA+FrC,cAAc,CA9FC,cAAC,GAAgB,YAAA,CAAa;IA+F7C,cAAc,CA9FC,kBAAC,GAAoB,gBAAA,CAAiB,MAAC,CAAM;IAgG5D,KAAK,uBA9FM,CAAA,IAAK,MAAA,EAAQ;QA+FtB,IAAI,CA9FC,CAAC,IAAC,KAAQ,EAAA,IAAMA,WAAA,CAAU,CAAC,CAAC,KAAK,cAAA,EAAgB;YA+FpD,uBA9FM,CAAA,GAAI,IAAI,eAAA,CAAgB,EAAC,EAAG,EAAA,CAAG,CAAC;YA+FtC,CAAC,CA9FC,cAAC,GAAgB,YAAA,CAAa;YA+FhC,CAAC,CA9FC,kBAAC,GAAoB,gBAAA,CAAiB,MAAC,CAAM;YA+F/C,GAAG,CA9FCA,WAAC,CAAS,CAAC,CAAC,CAAC,GAAG,CAAA,CAAE;SA+FvB;KACF;IACD,OA9FO,GAAA,CAAI;CA+FZ;;;;;;;AAOD,SAAA,wCAAA,CACI,YAA6B,EAAE,cAA4B,EAAE,MAAe,EADhF;IAEE,OAnGO,MAAA,CAAO,IAAC,CAoGX,CAAC,IAnGI,cAAA,CAAe,YAAC,EAAa,cAAA,EAAgB,CAAA,CAAE,IAAIA,WAAA,CAAU,CAAC,CAAC,KAAK,cAAA,CAAe,CAAC;CAoG9F;;;;;;;AAOD,SAAA,wBAAA,CACI,YAA6B,EAAE,cAA4B,EAAE,MAAe,EADhF;IAEE,OAxGO,MAAA,CAAO,IAAC,CAAI,CAAC,IAAI,cAAA,CAAe,YAAC,EAAa,cAAA,EAAgB,CAAA,CAAE,CAAC,CAAC;CAyG1E;;;;;;;AAOD,SAAA,cAAA,CACI,YAA6B,EAAE,cAA4B,EAAE,CAAQ,EADzE;IAEE,IAAI,CA7GC,YAAC,CAAY,WAAC,EAAW,IAAK,cAAA,CAAe,MAAC,GAAQ,CAAA,KAAM,CAAA,CAAE,SAAC,KAAa,MAAA,EAAQ;QA8GvF,OA7GO,KAAA,CAAM;KA8Gd;IAED,OA7GO,CAAA,CAAE,IAAC,KAAQ,EAAA,IAAM,CAAA,CAAE,UAAC,KAAc,SAAA,CAAU;CA8GpD;;;;;AAKD,SAAAA,WAAA,CAhHC,KAAA,EAgHD;IACE,OAhHO,KAAA,CAAM,MAAC,IAAS,cAAA,CAAe;CAiHvC;;;;;AAKD,SAAA,OAAA,CAnHC,KAAA,EAmHD;IACE,OAnHO,KAAA,CAAM,IAAC,IAAO,EAAA,CAAG;CAoHzB;;;;;AAKD,SAAA,UAAA,CAtHC,KAAA,EAsHD;IACE,OAtHO,KAAA,CAAM,OAAC,IAAU,EAAA,CAAG;CAuH5B;;AD/aD;;;;;;;;;;;;;AAqCA,AAAA,MAAA,kBAAA,CAAA;;;;;;;IAOA,YANY,CAAA,KAAA,EAMZ,GANY;;;;;;;;;;IAgBZ,KATY,CAAA,KAAA,EAAA,MAAA,EASZ,GATY;;;;;;;IAgBZ,YAbY,CAAA,KAAA,EAaZ,GAbY;;;;;;;IAoBZ,QAjBY,CAAA,KAAA,EAiBZ,GAjBY;;;;;;;;IAyBZ,gBAtBY,CAAA,MAAA,EAAA,IAAA,EAsBZ,GAtBY;CAuBX;;;;AAID,AAAA,MAAA,yBAAA,CAAA;;;;;IAKA,YAzBG,CAAA,KAAA,EAyBH,EAzByD,OAAO,KAAA,CAAM,EAAC;;;;;;IA+BvE,KA9BG,CAAA,KAAA,EAAA,YAAA,EA8BH,GA9BgF;;;;;IAmChF,YAlCG,CAAA,KAAA,EAkCH,EAlCyD,OAAO,KAAA,CAAM,EAAC;;;;;IAuCvE,QAtCG,CAAA,KAAA,EAsCH,EAtCsE,OAAO,IAAA,CAAK,EAAC;;;;;;IA4CnF,gBA3CG,CAAA,MAAA,EAAA,IAAA,EA2CH;QACI,OA3CO,MAAA,CAAO,WAAC,KAAe,IAAA,CAAK,WAAC,CAAW;KA4ChD;CACF;;AD/GD;;;;;;;AASA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA;;;;AAIA,AAAC,MAAA,MAAA,GAAA,IAAA,cAAA,CAAA,QAAA,CAAA,CAAA;AACD,AAAA,MAAA,kBAAA,CAAA;;;;;;;IAOA,WAAA,CAJc,MAAQ,EAA+B,QAAU,EACjD,mBAAoC,EACpC,iBAAkC,EAEhD;QAJc,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAA+B,IAArD,CAAA,QAAqD,GAAA,QAAA,CAAU;QACjD,IAAd,CAAA,mBAAc,GAAA,mBAAA,CAAoC;QACpC,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAAkC;KAAK;;;;;;IAYrD,IAVG,CAAA,cAAA,EAAA,KAAA,EAUH;QACI,IAAI,IAVC,CAAI,mBAAC,EAAoB;YAW5B,IAAI,CAVC,mBAAC,CAAmB,KAAC,CAAK,CAAC;SAWjC;QAED,uBAVM,cAAA,GAAiB,IAAA,CAAK,iBAAC,oBAAiB,KAAC,CAAK,YAAC,GAAc,CAAC;QAYpE,OAVO,GAAA,CAAI,IAAC,CAAI,cAAC,EAAe,CAAA,OAA8B,KAUlE;YACM,IAAI,IAVC,CAAI,iBAAC,EAAkB;gBAW1B,IAAI,CAVC,iBAAC,CAAiB,KAAC,CAAK,CAAC;aAW/B;YAED,uBAVM,MAAA,GAAS,OAAA,CAAQ,MAAC,CAAM,cAAC,CAAc,CAAC;YAY9C,OAVO,IAAI,kBAAA,CAAmB,OAAC,CAAO,MAAC,CAAM,QAAC,CAAQ,GAAC,CAAG,MAAC,CAAM,CAAC,EAAE,MAAA,CAAO,CAAC;SAW7E,CAVC,CAAC;KAWJ;;;;;IARA,iBAAA,CAAA,YAAA,EAAH;QAcI,IAAI,OAbO,YAAA,KAAiB,QAAA,EAAU;YAcpC,OAbO,WAAA,CAAY,IAAC,CAAI,MAAC,CAAM,IAAC,CAAI,YAAC,CAAY,CAAC,CAAC;SAcpD;aAbM;YAcL,OAbO,QAAA,CAAS,IAAC,CAAI,kBAAC,CAAkB,YAAC,EAAY,CAAE,EAAE,CAAA,CAAI,KAanE;gBACQ,IAAI,CAbC,YAAY,eAAA,EAAiB;oBAchC,OAbO,EAAA,CAAG,CAAE,CAAC,CAAC;iBAcf;qBAbM;oBAcL,OAbO,WAAA,CAAY,IAAC,CAAI,QAAC,CAAQ,kBAAC,CAAkB,CAAC,CAAC,CAAC,CAAC;iBAczD;aACF,CAbC,CAAC;SAcJ;KACF;CACF,AAED,AASC;;ADpFD;;;;;;;;;;;;;AAgBA,AAAA,MAAA,mBAAA,CAAA;;;;;;;;;;;;IAYA,gBAJY,CAAA,GAAA,EAIZ,GAJY;;;;;;;;IAYZ,OANY,CAAA,GAAA,EAMZ,GANY;;;;;;;;IAcZ,KATY,CAAA,UAAA,EAAA,MAAA,EASZ,GATY;CAUX;;;;AAID,AAAA,MAAA,0BAAA,CAAA;;;;;IAKA,gBAZG,CAAA,GAAA,EAYH,EAZ4C,OAAO,IAAA,CAAK,EAAC;;;;;IAiBzD,OAhBG,CAAA,GAAA,EAgBH,EAhBmC,OAAO,GAAA,CAAI,EAAC;;;;;;IAsB/C,KArBG,CAAA,UAAA,EAAA,QAAA,EAqBH,EArB2D,OAAO,UAAA,CAAW,EAAC;CAsB7E;;ADlED;;;;;;;AAUA,AACA,AAEA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AAgIA;;;;AAIA,SAAA,mBAAA,CAJC,KAAA,EAID;IACE,MAJM,KAAA,CAAM;CAKb;;;;;;AAuBD,SAAA,iBAAA,CALC,QAAA,EAKD;IACE,QALO,EAAA,CAAG,IAAE,CAAQ,EAAI;CAMzB;;;;;;;;;;AAUD,AAAA,MAAA,MAAA,CAAA;;;;;;;;;;;IA6CA,WAAA,CAAc,iBAA8B,EAAa,aAAe,EAC1D,YAAc,EAAgC,QAAU,EAAU,QAAU,EAIpF,MAHQ,EAAuB,QAAU,EAAiB,MAAQ,EAFxE;QAAc,IAAd,CAAA,iBAAc,GAAA,iBAAA,CAA8B;QAAa,IAAzD,CAAA,aAAyD,GAAA,aAAA,CAAe;QAC1D,IAAd,CAAA,YAAc,GAAA,YAAA,CAAc;QAAgC,IAA5D,CAAA,QAA4D,GAAA,QAAA,CAAU;QACN,IAAhE,CAAA,MAAgE,GAAA,MAAA,CAAQ;QA/C9D,IAAV,CAAA,WAAU,GAAc,IAAI,eAAA,oBAAiC,IAAE,GAAM,CAAC;QAC5D,IAAV,CAAA,YAAU,GAAe,IAAI,OAAA,EAAc,CAAG;QAIpC,IAAV,CAAA,YAAU,GAAuB,CAAA,CAAE;;;;;;QAUnC,IAAA,CAAA,YADG,GAAA,mBAAA,CAAA;;;;QAKH,IAAA,CAAA,SAEG,GAAA,KAAA,CAAA;;;;;;QAIH,IAAA,CAAA,KAGG,GAAA;YAFC,mBAAmB,EAAE,iBAAiB;YACtC,kBAAkB,EAAE,iBAAiB;SACtC,CAAC;;;;QAIJ,IAAA,CAAA,mBAIG,GAAA,IAAA,0BAAA,EAAA,CAAA;QAFD,IAAF,CAAA,kBAAoB,GAIuB,IAAI,yBAAA,EAA0B,CAAE;QAcvE,MAAM,WAAW,GAAG,CAAC,CAAQ,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,MAAM,SAAS,GAAG,CAAC,CAAQ,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE1C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,kBAAkB,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;QAEtC,IAAI,CAAC,YAAY,GAAG,IAAI,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QACrF,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxF,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;;;;;;;IAOH,sBALG,CAAA,iBAAA,EAKH;QACI,IAAI,CALC,iBAAC,GAAmB,iBAAA,CAAkB;;;QAQ3C,IAAI,CALC,kBAAC,CAAkB,IAAC,CAAI,SAAC,GAAW,IAAA,CAAK,iBAAC,CAAiB;KAMjE;;;;;IAKH,iBALG,GAKH;QACI,IAAI,CALC,2BAAC,EAA2B,CAAE;QAMnC,IAAI,IALC,CAAI,YAAC,KAAgB,CAAA,EAAG;YAM3B,IAAI,CALC,aAAC,CAAa,IAAC,CAAI,QAAC,CAAQ,IAAC,CAAI,IAAC,CAAI,EAAE,EAAA,UAAE,EAAW,IAAA,EAAK,CAAC,CAAC;SAMlE;KACF;;;;;IAKH,2BALG,GAKH;;;QAGI,IAAI,CALC,IAAC,CAAI,oBAAC,EAAqB;YAM9B,IAAI,CALC,oBAAC,IAA2B,IAAC,CAAI,QAAC,CAAQ,SAAC,CAAS,IAAC,CAAI,OAAC,CAAO,IAAC,CAAI,CAAC,MAAQ,KAK1F;gBACQ,uBALM,UAAA,GAAa,IAAA,CAAK,aAAC,CAAa,KAAC,CAAK,MAAC,CAAM,KAAC,CAAK,CAAC,CAAC;gBAM3D,uBALM,MAAA,GAA2B,MAAA,CAAO,MAAC,CAAM,KAAK,UAAA,GAAa,UAAA,GAAa,YAAA,CAAa;gBAM3F,UAAU,CALC,MAKnB,EAL2B,IAAA,CAAK,kBAAC,CAAkB,UAAC,EAAW,MAAA,EAAQ,EAAA,UAAE,EAAW,IAAA,EAAK,CAAC,CAAC,EAAC,EAAG,CAAA,CAAE,CAAC;aAM3F,CALC,CAAC,CAAA,CAAC;SAML;KACF;;;;;IAKH,IAPG,WAAA,GAOH,EAPmC,OAAO,IAAA,CAAK,kBAAC,CAAkB,EAAC;;;;;IAYnE,IATG,GAAA,GASH,EATsB,OAAO,IAAA,CAAK,YAAC,CAAY,IAAC,CAAI,cAAC,CAAc,CAAC,EAAC;;;;;IAcrE,IAXG,MAAA,GAWH,EAXoC,OAAO,IAAA,CAAK,YAAC,CAAY,EAAC;;;;;;IAiB9D,YAdG,CAAA,CAAA,EAcH,EAd2B,IAAA,CAAK,YAAC,CAAY,IAAC,CAAI,CAAC,CAAC,CAAC,EAAC;;;;;;;;;;;;;;;;;IA+BtD,WAfG,CAAA,MAAA,EAeH;QACI,cAAc,CAfC,MAAC,CAAM,CAAC;QAgBvB,IAAI,CAfC,MAAC,GAAQ,MAAA,CAAO;KAgBtB;;;;;IAKH,WAjBG,GAiBH,EAjBkB,IAAA,CAAK,OAAC,EAAO,CAAE,EAAC;;;;;IAsBlC,OAnBG,GAmBH;QACI,IAAI,IAnBC,CAAI,oBAAC,EAAqB;YAoB7B,IAAI,CAnBC,oBAAC,CAAoB,WAAC,EAAW,CAAE;YAoBxC,IAAI,CAnBC,oBAAC,KAAsB,IAAA,EAAA,CAAO;SAoBpC;KACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6CH,aArBG,CAAA,QAAA,EAAA,gBAqBH,GArBG,EAAA,EAqBH;QACI,MArBM,EAAA,UAAE,EAAW,WAAA,EAAsB,QAAA,EAsBlC,mBAAmB,EArBE,mBAAA,EAAqB,gBAAA,EAAiB,GAAG,gBAAA,CAAiB;QAsBtF,IAAI,SArBC,EAAS,IAAK,mBAAA,KAA4B,OAAC,CAAA,KAAe,OAAC,CAAO,IAAC,CAAA,EAAK;YAsB3E,OAAO,CArBC,IAAC,CAAI,qEAAC,CAAqE,CAAC;SAsBrF;QACD,uBArBM,CAAA,GAAI,UAAA,IAAc,IAAA,CAAK,WAAC,CAAW,IAAC,CAAI;QAsB9C,uBArBM,CAAA,GAAI,gBAAA,GAAmB,IAAA,CAAK,cAAC,CAAc,QAAC,GAAU,QAAA,CAAS;QAsBrE,qBArBI,CAAA,GAAiB,IAAA,CAAK;QAsB1B,IAAI,mBArBC,EAAoB;YAsBvB,QAAQ,mBArBC;gBAsBP,KArBK,OAAA;oBAsBH,CAAC,GAAX,MAAA,CAAA,MAAA,CAAA,EAAA,EArBgB,IAAG,CAAI,cAAC,CAAc,WAAC,EAAY,WAAI,CAAW,CAAC;oBAsBzD,MAAM;gBACR,KArBK,UAAA;oBAsBH,CAAC,GArBG,IAAA,CAAK,cAAC,CAAc,WAAC,CAAW;oBAsBpC,MAAM;gBACR;oBACE,CAAC,GArBG,WAAA,IAAe,IAAA,CAAK;aAsB3B;SACF;aArBM;YAsBL,CAAC,GArBG,mBAAA,GAAsB,IAAA,CAAK,cAAC,CAAc,WAAC,GAAa,WAAA,IAAe,IAAA,CAAK;SAsBjF;QACD,OArBO,aAAA,CAAc,CAAC,EAAE,IAAA,CAAK,cAAC,EAAe,QAAA,qBAAU,CAAA,uBAAK,CAAA,GAAI,CAAC;KAsBlE;;;;;;;;;;;;;;;;;;;;;;;;IAwBH,aAvBG,CAAA,GAAA,EAAA,MAuBH,GAvBG,EAAA,kBAAA,EAAA,KAAA,EAAA,EAuBH;QAEI,uBAvBM,OAAA,GAAU,GAAA,YAAe,OAAA,GAAU,GAAA,GAAM,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,CAAC;QAwBlE,uBAvBM,UAAA,GAAa,IAAA,CAAK,mBAAC,CAAmB,KAAC,CAAK,OAAC,EAAQ,IAAA,CAAK,UAAC,CAAU,CAAC;QAyB5E,OAvBO,IAAA,CAAK,kBAAC,CAAkB,UAAC,EAAW,YAAA,EAAc,MAAA,CAAO,CAAC;KAwBlE;;;;;;;;;;;;;;;;;;;;;;;;;IAyBH,QAzBG,CAAA,QAAA,EAAA,MAyBH,GAzBG,EAAA,kBAAA,EAAA,KAAA,EAAA,EAyBH;QAEI,gBAAgB,CAzBC,QAAC,CAAQ,CAAC;QA0B3B,IAAI,OAzBO,MAAA,CAAO,WAAC,KAAe,QAAA,IAAY,MAAA,CAAO,WAAC,KAAe,IAAA,EAAM;YA0BzE,MAAM,CAzBC,WAAC,GAAa,IAAA,CAAK,gBAAC,CAAgB,MAAC,CAAM,WAAC,CAAW,CAAC;SA0BhE;QACD,OAzBO,IAAA,CAAK,aAAC,CAAa,IAAC,CAAI,aAAC,CAAa,QAAC,EAAS,MAAA,CAAO,EAAE,MAAA,CAAO,CAAC;KA0BzE;;;;;;IAMH,YA5BG,CAAA,GAAA,EA4BH,EA5BuC,OAAO,IAAA,CAAK,aAAC,CAAa,SAAC,CAAS,GAAC,CAAG,CAAC,EAAC;;;;;;IAkCjF,QA/BG,CAAA,GAAA,EA+BH,EA/BmC,OAAO,IAAA,CAAK,aAAC,CAAa,KAAC,CAAK,GAAC,CAAG,CAAC,EAAC;;;;;;;IAsCzE,QAnCG,CAAA,GAAA,EAAA,KAAA,EAmCH;QACI,IAAI,GAnCC,YAAc,OAAA,EAAS;YAoC1B,OAnCO,YAAA,CAAa,IAAC,CAAI,cAAC,EAAe,GAAA,EAAK,KAAA,CAAM,CAAC;SAoCtD;QAED,uBAnCM,OAAA,GAAU,IAAA,CAAK,aAAC,CAAa,KAAC,CAAK,GAAC,CAAG,CAAC;QAoC9C,OAnCO,YAAA,CAAa,IAAC,CAAI,cAAC,EAAe,OAAA,EAAS,KAAA,CAAM,CAAC;KAoC1D;;;;;IAjCA,gBAAA,CAAA,MAAA,EAAH;QAuCI,OAtCO,MAAA,CAAO,IAAC,CAAI,MAAC,CAAM,CAAC,MAAC,CAAM,CAAC,MAAQ,EAAQ,GAAK,KAsC5D;YACM,uBAtCM,KAAA,GAAa,MAAA,CAAO,GAAC,CAAG,CAAC;YAuC/B,IAAI,KAtCC,KAAS,IAAA,IAAQ,KAAA,KAAU,SAAA,EAAW;gBAuCzC,MAAM,CAtCC,GAAC,CAAG,GAAG,KAAA,CAAM;aAuCrB;YACD,OAtCO,MAAA,CAAO;SAuCf,EAtCE,EAAA,CAAG,CAAC;KAuCR;;;;IApCA,kBAAA,GAAH;QAyCI,SAAS;aACJ,IAxCC,CAyCE,IAAI,CAxCC,WAAC,EAyCN,CAAC,GAxCK,KAsClB;YAGc,IAAI,GAxCC,EAAI;gBAyCP,IAAI,CAxCC,0BAAC,CAA0B,GAAC,CAAG,CAAC;;;gBA2CrC,OAxCO,GAAA,CAAI,OAAC,CAAO,KAAC,CAAK,MAwCzC,GAxC+C,CAAG,CAAC;aAyCpC;iBAxCM;gBAyCL,QAxCY,EAAC,CAAE,IAAE,CAAI,EAAC;aAyCvB;SACF,CAxCC;aAyCL,SAxCC,CAAS,MAwCnB,GAxCyB,CAAG,CAAC;KAyC1B;;;;;;;IAtCA,kBAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAH;QA+CI,uBA7CM,cAAA,GAAiB,IAAA,CAAK,WAAC,CAAW,KAAC,CAAK;;;;QAkD9C,IAAI,cA7CC,IAAiB,MAAA,KAAW,YAAA,IAAgB,cAAA,CAAe,MAAC,KAAU,YAAA;YA8CvE,cAAc,CA7CC,MAAC,CAAM,QAAC,EAAQ,KAAM,MAAA,CAAO,QAAC,EAAQ,EAAG;YA8C1D,OA7CO,OAAA,CAAQ,OAAC,CAAO,IAAC,CAAI,CAAC;SA8C9B;;;;QAKD,IAAI,cA7CC,IAAiB,MAAA,IAAU,YAAA,IAAgB,cAAA,CAAe,MAAC,KAAU,UAAA;YA8CtE,cAAc,CA7CC,MAAC,CAAM,QAAC,EAAQ,KAAM,MAAA,CAAO,QAAC,EAAQ,EAAG;YA8C1D,OA7CO,OAAA,CAAQ,OAAC,CAAO,IAAC,CAAI,CAAC;SA8C9B;QAED,qBA7CI,OAAA,GAAe,IAAA,CAAK;QA8CxB,qBA7CI,MAAA,GAAc,IAAA,CAAK;QA+CvB,uBA7CM,OAAA,GAAU,IAAI,OAAA,CAAQ,CAAC,GAAC,EAAI,GAAA,KA6CtC;YACM,OAAO,GA7CG,GAAA,CAAI;YA8Cd,MAAM,GA7CG,GAAA,CAAI;SA8Cd,CA7CC,CAAC;QA+CH,uBA7CM,EAAA,GAAK,EAAA,IAAG,CAAI,YAAC,CAAY;QA8C/B,IAAI,CA7CC,WAAC,CAAW,IAAC,CAAI,EAAC,EAAC,EAAG,MAAA,EAAQ,MAAA,EAAQ,MAAA,EAAQ,OAAA,EAAS,MAAA,EAAQ,OAAA,EAAQ,CAAC,CAAC;;;QAiD9E,OA7CO,OAAA,CAAQ,KAAC,CAAK,CAAC,CAAG,KAAQ,OAAA,CAAQ,MAAC,CAAM,CAAC,CAAC,CAAC,CAAC;KA8CrD;;;;;IA3CA,0BAAA,CAAA,EAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,EAAH;QAkDI,uBAhDM,GAAA,GAAM,IAAA,CAAK,mBAAC,CAAmB,OAAC,CAAO,MAAC,CAAM,CAAC;QAiDrD,uBAhDM,aAAA,GAAgB,CAAA,IAAE,CAAI,SAAC,IAAY,GAAA,CAAI,QAAC,EAAQ,KAAM,IAAA,CAAK,cAAC,CAAc,QAAC,EAAQ,CAAE;QAkD3F,IAAI,aAhDC,IAAgB,IAAA,CAAK,mBAAC,CAAmB,gBAAC,CAAgB,MAAC,CAAM,EAAE;YAiDtE,IAAI,CAhDC,YAAC,CAAY,IAAC,CAAI,IAAI,eAAA,CAAgB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,CAAC,CAAC,CAAC;YAiDxE,OAAO,CAhDC,OAAC,EAAO;iBAiDX,IAhDC,CAiDE,CAAC,CAhDC,KAAK,IAAA,CAAK,WAAC,CAiDT,GAAG,EAhDE,MAAA,EAAQ,CAAA,CAAE,MAAC,CAAM,kBAAC,EAAmB,CAAA,CAAE,MAAC,CAAM,UAAC,EAAW,EAAA,EAAI,IAAA,CAAK,CAAC;iBAiDhF,IAhDC,CAAI,OAAC,EAAQ,MAAA,CAAO,CAAC;;;SAoD5B;aAhDM,IAiDH,aAAa,IAhDI,IAAA,CAAK,UAAC;YAiDvB,IAAI,CAhDC,mBAAC,CAAmB,gBAAC,CAAgB,IAAC,CAAI,UAAC,CAAU,EAAE;YAiD9D,IAAI,CAhDC,YAAC,CAAY,IAAC,CAAI,IAAI,eAAA,CAAgB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,CAAC,CAAC,CAAC;YAiDxE,OAAO,CAhDC,OAAC,EAAO;iBAiDX,IAhDC,CAiDE,CAAC,CAhDC,KAAK,IAAA,CAAK,WAAC,CAiDT,GAAG,EAhDE,MAAA,EAAQ,KAAA,EAAO,KAAA,EAAO,EAAA,EAiD3B,gBAAgB,CAhDC,GAAC,EAAI,IAAA,CAAK,iBAAC,CAAiB,CAAC,QAAC,CAAQ,CAAC;iBAiD/D,IAhDC,CAAI,OAAC,EAAQ,MAAA,CAAO,CAAC;SAkD5B;aAhDM;YAiDL,IAAI,CAhDC,UAAC,GAAY,MAAA,CAAO;YAiDzB,OAAO,CAhDC,IAAC,CAAI,CAAC;SAiDf;KACF;;;;;;;;;;IA9CA,WAAA,CAyDG,GAAY,EAAE,MAAe,EAAE,sBAA+B,EAAE,gBAAyB,EACzF,EAAU,EAAE,eAAyC,EA1D3D;QA2DI,IAAI,EAxDC,KAAM,IAAA,CAAK,YAAC,EAAa;YAyD5B,IAAI,CAxDC,QAAC,CAAQ,EAAC,CAAE,IAAC,CAAI,aAAC,CAAa,SAAC,CAAS,IAAC,CAAI,cAAC,CAAc,CAAC,CAAC;YAyDpE,IAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,gBAAA,CAyDvB,EAAE,EAxDE,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAyD1B,CAFV,cAAA,EAE2B,EAxDC,CAsD5B,2CAAA,EAtD8B,IAA8C,CAAI,YAAC,CAsDjF,CAtD6F,CAAE,CAAC,CAAC;YAyD3F,OAxDO,OAAA,CAAQ,OAAC,CAAO,KAAC,CAAK,CAAC;SAyD/B;QAED,OAxDO,IAAI,OAAA,CAAQ,CAAC,cAAC,EAAe,aAAA,KAwDxC;;;YAGM,qBAxDI,eAAiF,CAAC;YAyDtF,IAAI,CAxDC,eAAC,EAAgB;gBAyDpB,uBAxDM,cAAA,GAAiB,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ;gBAyD9C,uBAxDM,iBAAA,GAyDF,cAAc,CAxDC,cAAC,EAAe,IAAA,CAAK,YAAC,EAAa,IAAA,CAAK,aAAC,EAAc,GAAA,EAAK,IAAA,CAAK,MAAC,CAAM,CAAC;gBA0D5F,eAAe,GAxDG,QAAA,CAAS,IAAC,CAAI,iBAAC,EAAkB,CAAA,UAAa,KAwDxE;oBACU,OAxDO,GAAA,CAAI,IAAC,CAyDR,SAAS,CACL,IAAI,CAxDC,iBAAC,EAAkB,IAAA,CAAK,MAAC,EAAO,UAAA,EAAY,IAAA,CAAK,YAAC,CAAY,UAAC,CAAU,CAAC,EAyDnF,CAAC,QAxDU,KAqDzB;wBAKgB,IAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,gBAAA,CAyDvB,EAAE,EAxDE,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAAE,IAAA,CAAK,YAAC,CAAY,UAAC,CAAU,EAAE,QAAA,CAAS,CAAC,CAAC;wBA0D1E,OAxDO,EAAA,UAAE,EAAW,QAAA,EAAS,CAAC;qBAyD/B,CAxDC,CAAC;iBAyDR,CAxDC,CAAC;aAyDJ;iBAxDM;gBAyDL,eAAe,GAxDG,EAAA,CAAG,EAAE,UAAC,EAAW,GAAA,EAAK,QAAA,EAAU,eAAA,EAAgB,CAAC,CAAC;aAyDrE;YAED,uBAxDM,wBAAA,GAA2B,QAAA,CAAS,IAAC,CAyDvC,eAAe,EAxDE,CAAA,CAAuD,KAuDlF;gBAEY,OAxDO,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,KAAC,CAAK,mBAAC,CAAmB,CAAC,CAAC,QAAC,CAAQ,EAAE,MAAM,CAAA,CAAE,CAAC;aAyDtE,CAxDC,CAAC;;YA2DP,qBAxDI,aAAe,CAAc;YA0DjC,uBAxDM,sBAAA,GAAyB,GAAA,CAAI,IAAC,CAyDhC,wBAAwB,EACxB,CAAC,EAxDC,UAAC,EAAW,QAAA,EAA8D,KAsDtF;gBAGY,uBAxDM,cAAA,GAAiB,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ;gBAyD9C,aAAa;oBACT,IAxDI,aAAA,CAAc,QAAC,EAAS,IAAA,CAAK,kBAAC,CAAkB,QAAC,EAAS,cAAA,CAAe,CAAC;gBAyDlF,aAAa,CAxDC,QAAC,CAAQ,IAAC,CAAI,YAAC,CAAY,CAAC;gBAyD1C,OAxDO,EAAA,UAAE,EAAW,QAAA,EAAS,CAAC;aAyD/B,CAxDC,CAAC;YA0DP,uBAxDM,yBAAA,GAA4B,QAAA,CAAS,IAAC,CAyDxC,sBAAsB,EACtB,CAAC,EAxDC,UAAC,EAAW,QAAA,EAA8D,KAsDtF;gBAGY,IAAI,IAxDC,CAAI,YAAC,KAAgB,EAAA;oBAAI,OAAO,EAAA,CAAG,KAAE,CAAK,CAAC;gBA0DhD,OAxDO,GAAA,CAAI,IAAC,CAAI,aAAC,CAAa,WAAC,EAAW,EAAG,CAAA,cAAiB,KAwD1E;oBACc,OAxDO,EAAA,UAAE,EAAW,UAAA,EAAY,QAAA,EAAU,QAAA,EAAU,cAAA,EAAgB,cAAA,EAAe,CAAC;iBAyDrF,CAxDC,CAAC;aAyDJ,CAxDC,CAAC;YA0DP,uBAxDM,yBAAA,GAA4B,QAAA,CAAS,IAAC,CAAI,yBAAC,EAA0B,CAAA,CAAI,KAwDrF;gBACQ,IAAI,IAxDC,CAAI,YAAC,KAAgB,EAAA;oBAAI,OAAO,EAAA,CAAG,KAAE,CAAK,CAAC;gBA0DhD,IAAI,CAxDC,CAAC,cAAC,EAAe;oBAyDpB,OAxDO,GAAA,CAAI,IAAC,CAAI,aAAC,CAAa,WAAC,EAAW,EAAG,MAAM,CAAA,CAAE,CAAC;iBAyDvD;qBAxDM;oBAyDL,OAxDO,EAAA,CAAG,CAAE,CAAC,CAAC;iBAyDf;aACF,CAxDC,CAAC;YA0DH,uBAxDM,kBAAA,GAAqB,QAAA,CAAS,IAAC,CAAI,yBAAC,EAA0B,CAAA,CAAI,KAwD9E;gBACQ,OAxDO,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,KAAC,CAAK,kBAAC,CAAkB,CAAC,CAAC,QAAC,CAAQ,EAAE,MAAM,CAAA,CAAE,CAAC;aAyDrE,CAxDC,CAAC;;;YA6DH,uBAxDM,YAAA,GAyDF,GAAG,CAxDC,IAAC,CAAI,kBAAC,EAAmB,CAAA,EAAE,UAAC,EAAW,QAAA,EAAU,cAAA,EAAiB,KAuDhF;gBAEY,IAAI,cAxDC,EAAe;oBAyDlB,uBAxDM,KAAA,GAyDF,iBAAiB,CAxDC,IAAC,CAAI,kBAAC,EAAmB,QAAA,EAAU,IAAA,CAAK,kBAAC,CAAkB,CAAC;oBAyDlF,OAxDO,EAAA,UAAE,EAAW,KAAA,EAAO,cAAA,EAAe,CAAC;iBAyD5C;qBAxDM;oBAyDL,OAxDO,EAAA,UAAE,EAAW,KAAA,EAAO,IAAA,EAAM,cAAA,EAAe,CAAC;iBAyDlD;aACF,CAxDC,CAAC;;;YA6DP,qBAxDI,sBAAwB,CAAQ;YAyDpC,uBAxDM,WAAA,GAAc,IAAA,CAAK,kBAAC,CAAkB;YAyD5C,uBAxDM,SAAA,GAAY,IAAA,CAAK,cAAC,CAAc;YA0DtC,YAAY;iBACP,OAxDC,CAAO,CAAC,EAAC,UAAC,EAAW,KAAA,EAAO,cAAA,EAAiB,KAwDzD;gBACY,IAAI,CAxDC,cAAC,IAAiB,EAAA,KAAO,IAAA,CAAK,YAAC,EAAa;oBAyD/C,sBAAsB,GAxDG,KAAA,CAAM;oBAyD/B,OAAO;iBACR;gBAED,IAAI,CAxDC,cAAC,GAAgB,UAAA,CAAW;gBAyDjC,IAAI,CAxDC,UAAC,GAAY,IAAA,CAAK,mBAAC,CAAmB,KAAC,CAAK,IAAC,CAAI,cAAC,EAAe,MAAA,CAAO,CAAC;gBA0D9E,IAAI,CAxDC,kBAAC,GAAoB,KAAA,CAAM;gBA0DhC,IAAI,CAxDC,sBAAC,EAAuB;oBAyD3B,uBAxDM,IAAA,GAAO,IAAA,CAAK,aAAC,CAAa,SAAC,CAAS,IAAC,CAAI,UAAC,CAAU,CAAC;oBAyD3D,IAAI,IAxDC,CAAI,QAAC,CAAQ,oBAAC,CAAoB,IAAC,CAAI,IAAI,gBAAA,EAAkB;wBAyDhE,IAAI,CAxDC,QAAC,CAAQ,YAAC,CAAY,IAAC,CAAI,CAAC;qBAyDlC;yBAxDM;wBAyDL,IAAI,CAxDC,QAAC,CAAQ,EAAC,CAAE,IAAC,CAAI,CAAC;qBAyDxB;iBACF;gBAED,IAxDI,cAAA,CAAe,IAAC,CAAI,kBAAC,EAAmB,KAAA,EAAO,WAAA,CAAY;qBAyD1D,QAxDC,CAAQ,IAAC,CAAI,YAAC,CAAY,CAAC;gBA0DjC,sBAAsB,GAxDG,IAAA,CAAK;aAyD/B,CAxDC;iBAyDD,IAxDC,CAyDE,MADd;gBAEgB,IAAI,sBAxDC,EAAuB;oBAyD1B,IAAI,CAxDC,SAAC,GAAW,IAAA,CAAK;oBAyDtB,IAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,aAAA,CAyDvB,EAAE,EAxDE,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAAE,IAAA,CAAK,YAAC,CAAY,IAAC,CAAI,cAAC,CAAc,CAAC,CAAC,CAAC;oBAyDzE,cAAc,CAxDC,IAAC,CAAI,CAAC;iBAyDtB;qBAxDM;oBAyDL,IAAI,CAxDC,wBAAC,EAAwB,CAAE;oBAyDhC,IAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,gBAAA,CAAiB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAAE,EAAA,CAAG,CAAC,CAAC;oBAyD7E,cAAc,CAxDC,KAAC,CAAK,CAAC;iBAyDvB;aACF,EACD,CAAC,CAxDG,KAuDlB;gBAEgB,IAAI,0BAxDC,CAA0B,CAAC,CAAC,EAAE;oBAyDjC,IAAI,CAxDC,wBAAC,EAAwB,CAAE;oBAyDhC,IAAI,CAxDC,SAAC,GAAW,IAAA,CAAK;oBAyDtB,IAAI,CAxDC,YAAC,CAAY,IAAC,CAyDf,IAxDI,gBAAA,CAAiB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAAE,CAAA,CAAE,OAAC,CAAO,CAAC,CAAC;oBAyDjE,cAAc,CAxDC,KAAC,CAAK,CAAC;iBAyDvB;qBAxDM;oBAyDL,IAAI,CAxDC,YAAC,CAAY,IAAC,CAAI,IAAI,eAAA,CAAgB,EAAC,EAAG,IAAA,CAAK,YAAC,CAAY,GAAC,CAAG,EAAE,CAAA,CAAE,CAAC,CAAC;oBAyD3E,IAxDI;wBAyDF,cAAc,CAxDC,IAAC,CAAI,YAAC,CAAY,CAAC,CAAC,CAAC,CAAC;qBAyDtC;oBAxDC,OAAA,EAAQ,EAAG;wBAyDX,aAAa,CAxDC,EAAC,CAAE,CAAC;qBAyDnB;iBACF;gBAED,IAAI,CAxDC,kBAAC,GAAoB,WAAA,CAAY;gBAyDtC,IAAI,CAxDC,cAAC,GAAgB,SAAA,CAAU;gBAyDhC,IAAI,CAxDC,UAAC,GAAY,IAAA,CAAK,mBAAC,CAAmB,KAAC,CAAK,IAAC,CAAI,cAAC,EAAe,MAAA,CAAO,CAAC;gBAyD9E,IAAI,CAxDC,QAAC,CAAQ,YAAC,CAAY,IAAC,CAAI,YAAC,CAAY,IAAC,CAAI,UAAC,CAAU,CAAC,CAAC;aAyDhE,CAxDC,CAAC;SAyDZ,CAxDC,CAAC;KAyDJ;;;;IAtDA,wBAAA,GAAH;QA2DI,uBA1DM,IAAA,GAAO,IAAA,CAAK,aAAC,CAAa,SAAC,CAAS,IAAC,CAAI,UAAC,CAAU,CAAC;QA2D3D,IAAI,CA1DC,QAAC,CAAQ,YAAC,CAAY,IAAC,CAAI,CAAC;KA2DlC;CACF;AAED,AAyDA,MAAA,WAAA,CAAA;;;;IAIA,WAAA,CArHqB,IAA6B,EAqHlD;QArHqB,IAArB,CAAA,IAAqB,GAAA,IAAA,CAA6B;KAAE;;;;IAyHpD,IAxHG,KAAA,GAwHH,EAxHwC,OAAO,IAAA,CAAK,IAAC,CAAI,IAAC,CAAI,IAAC,CAAI,MAAC,GAAQ,CAAA,CAAE,CAAC,EAAC;CAyH/E;AAED,AAKA,MAAA,aAAA,CAAA;;;;;IAKA,WAAA,CAjIqB,SAAmB,EAAY,KAAO,EAiI3D;QAjIqB,IAArB,CAAA,SAAqB,GAAA,SAAA,CAAmB;QAAY,IAApD,CAAA,KAAoD,GAAA,KAAA,CAAO;KAAuB;CAmIjF;AAED,AAOA,AAAA,MAAA,aAAA,CAAA;;;;;;IAQA,WAAA,CA5Ic,MAAQ,EAA6B,IAAM,EAC3C,cAAgB,EA2I9B;QA5Ic,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAA6B,IAAnD,CAAA,IAAmD,GAAA,IAAA,CAAM;QAC3C,IAAd,CAAA,cAAc,GAAA,cAAA,CAAgB;QALpB,IAAV,CAAA,iBAAU,GAAmC,EAAA,CAAG;QACtC,IAAV,CAAA,mBAAU,GAAuC,EAAA,CAAG;KAIb;;;;;IAmJvC,QAjJG,CAAA,cAAA,EAiJH;QACI,uBAjJM,UAAA,GAAa,IAAA,CAAK,MAAC,CAAM,KAAC,CAAK;QAkJrC,uBAjJM,QAAA,GAAW,IAAA,CAAK,IAAC,GAAM,IAAA,CAAK,IAAC,CAAI,KAAC,GAAO,IAAA,CAAK;QAkJpD,IAAI,CAjJC,mBAAC,CAAmB,UAAC,EAAW,QAAA,EAAU,cAAA,EAAgB,CAAA,UAAE,CAAU,KAAC,CAAK,CAAC,CAAC;KAkJpF;;;;IAIH,WAnJG,GAmJH;QACI,IAAI,IAnJC,CAAI,mBAAC,CAAmB,MAAC,KAAU,CAAA,IAAK,IAAA,CAAK,iBAAC,CAAiB,MAAC,KAAU,CAAA,EAAG;YAoJhF,OAnJO,EAAA,CAAG,IAAE,CAAI,CAAC;SAoJlB;QACD,uBAnJM,cAAA,GAAiB,IAAA,CAAK,sBAAC,EAAsB,CAAE;QAoJrD,OAnJO,QAAA,CAAS,IAAC,CAoJb,cAAc,EACd,CAAC,aAnJe,KAAY,aAAA,GAAgB,IAAA,CAAK,oBAAC,EAAoB,GAAI,EAAA,CAAG,KAAE,CAAK,CAAC,CAAC;KAoJ3F;;;;IAIH,WArJG,GAqJH;QACI,IAAI,IArJC,CAAI,iBAAC,CAAiB,MAAC,KAAU,CAAA;YAAG,OAAO,EAAA,CAAG,IAAE,CAAI,CAAC;QAsJ1D,uBArJM,OAAA,GAAU,IAAA,CAAK,IAAC,CAAI,iBAAC,CAAiB,CAAC;QAsJ7C,uBArJM,cAAA,GAsJF,SAAS,CArJC,IAAC,CAAI,OAAC,EAAQ,CAAA,KAAQ,KAAgB,IAAA,CAAK,UAAC,CAAU,KAAC,CAAK,KAAC,CAAK,CAAC,CAAC;QAsJlF,OArJO,MAAA,CAAO,IAAC,CAAI,cAAC,EAAe,CAAA,CAAI,EAAK,EAAI,KAAQ,CAAA,CAAE,CAAC;KAsJ5D;;;;;;;;IAnJA,mBAAA,CA4JG,UAA4C,EAAE,QAA+C,EAC7F,QAAqC,EAAE,UAAoC,EA7JjF;QA8JI,uBA3JM,YAAA,GAAe,iBAAA,CAAkB,QAAC,CAAQ,CAAC;;QA8JjD,UAAU,CA3JC,QAAC,CAAQ,OAAC,CAAO,CAAC,IA2JjC;YACM,IAAI,CA3JC,cAAC,CAAc,CAAC,EAAE,YAAA,CAAa,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,EAAE,QAAA,EAAU,UAAA,CAAW,MAAC,CAAM,CAAC,CAAC,CAAC,KAAC,CAAK,CAAC,CAAC,CAAC;YA4J7F,OA3JO,YAAA,CAAa,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,CAAC;SA4JrC,CA3JC,CAAC;;QA8JH,OAAO,CACH,YAAY,EA3JE,CAAA,CAAoC,EAAE,CAAG,KA4JrC,IAAI,CA3JC,6BAAC,CAA6B,CAAC,mBAAC,EAAC,QAAA,GAAW,UAAC,CAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KA4JxF;;;;;;;;IAzJA,cAAA,CAkKG,UAA4C,EAAE,QAA0C,EACxF,cAA2C,EAAE,UAAoC,EAnKvF;QAoKI,uBAjKM,MAAA,GAAS,UAAA,CAAW,KAAC,CAAK;QAkKhC,uBAjKM,IAAA,GAAO,QAAA,GAAW,QAAA,CAAS,KAAC,GAAO,IAAA,CAAK;QAkK9C,uBAjKM,OAAA,GAAU,cAAA,GAAiB,cAAA,CAAe,UAAC,CAAU,UAAC,CAAU,KAAC,CAAK,MAAC,CAAM,GAAG,IAAA,CAAK;;QAoK3F,IAAI,IAjKC,IAAO,MAAA,CAAO,YAAC,KAAgB,IAAA,CAAK,YAAC,EAAa;YAkKrD,IAAI,IAjKC,CAAI,2BAAC,CAkKF,IAAI,EAjKE,MAAA,mBAAO,EAAC,MAAA,CAAO,YAAC,GAAc,qBAAC,CAAqB,EAAE;gBAkKlE,IAAI,CAjKC,iBAAC,CAAiB,IAAC,CAAI,IAAI,WAAA,CAAY,UAAC,CAAU,CAAC,CAAC;gBAkKzD,uBAjKM,MAAA,KAAO,EAAE,OAAA,GAAU,MAAC,EAAA,CAAQ;gBAkKlC,IAAI,CAjKC,mBAAC,CAAmB,IAAC,CAAI,IAAI,aAAA,CAAc,MAAC,CAAM,SAAC,EAAU,IAAA,CAAK,CAAC,CAAC;aAkK1E;iBAjKM;;gBAmKL,MAAM,CAjKC,IAAC,GAAM,IAAA,CAAK,IAAC,CAAI;gBAkKxB,MAAM,CAjKC,aAAC,GAAe,IAAA,CAAK,aAAC,CAAa;aAkK3C;;YAGD,IAAI,MAjKC,CAAM,SAAC,EAAU;gBAkKpB,IAAI,CAjKC,mBAAC,CAkKF,UAAU,EAjKE,QAAA,EAAU,OAAA,GAAU,OAAA,CAAQ,QAAC,GAAU,IAAA,EAAM,UAAA,CAAW,CAAC;;aAoK1E;iBAjKM;gBAkKL,IAAI,CAjKC,mBAAC,CAAmB,UAAC,EAAW,QAAA,EAAU,cAAA,EAAgB,UAAA,CAAW,CAAC;aAkK5E;SACF;aAjKM;YAkKL,IAAI,IAjKC,EAAK;gBAkKR,IAAI,CAjKC,6BAAC,CAA6B,QAAC,EAAS,OAAA,CAAQ,CAAC;aAkKvD;YAED,IAAI,CAjKC,iBAAC,CAAiB,IAAC,CAAI,IAAI,WAAA,CAAY,UAAC,CAAU,CAAC,CAAC;;YAmKzD,IAAI,MAjKC,CAAM,SAAC,EAAU;gBAkKpB,IAAI,CAjKC,mBAAC,CAAmB,UAAC,EAAW,IAAA,EAAM,OAAA,GAAU,OAAA,CAAQ,QAAC,GAAU,IAAA,EAAM,UAAA,CAAW,CAAC;;aAoK3F;iBAjKM;gBAkKL,IAAI,CAjKC,mBAAC,CAAmB,UAAC,EAAW,IAAA,EAAM,cAAA,EAAgB,UAAA,CAAW,CAAC;aAkKxE;SACF;KACF;;;;;;;IA/JA,2BAAA,CAuKG,IAA4B,EAAE,MAA8B,EAC5D,IAAqC,EAxK3C;QAyKI,QAAQ,IAtKC;YAuKP,KAtKK,QAAA;gBAuKH,OAtKO,IAAA,CAAK;YAwKd,KAtKK,2BAAA;gBAuKH,OAtKO,CAAA,yBAAE,CAAyB,IAAC,EAAK,MAAA,CAAO;oBAuK3C,CAAC,YAtKC,CAAY,IAAC,CAAI,WAAC,EAAY,MAAA,CAAO,WAAC,CAAW,CAAC;YAwK1D,KAtKK,cAAA,CAAe;YAuKpB;gBACE,OAtKO,CAAA,yBAAE,CAAyB,IAAC,EAAK,MAAA,CAAO,CAAC;SAuKnD;KACF;;;;;;IApKA,6BAAA,CA2KG,KAAuC,EAAE,OAA2B,EA3K1E;QA4KI,uBA1KM,QAAA,GAAW,iBAAA,CAAkB,KAAC,CAAK,CAAC;QA2K1C,uBA1KM,CAAA,GAAI,KAAA,CAAM,KAAC,CAAK;QA4KtB,OAAO,CA1KC,QAAC,EAAS,CAAA,IAAuC,EAAE,SAAW,KA0K1E;YACM,IAAI,CA1KC,CAAC,CAAC,SAAC,EAAU;gBA2KhB,IAAI,CA1KC,6BAAC,CAA6B,IAAC,EAAK,OAAA,CAAQ,CAAC;aA2KnD;iBA1KM,IAAA,OAAK,EAAQ;gBA2KlB,IAAI,CA1KC,6BAAC,CAA6B,IAAC,EAAK,OAAA,CAAQ,QAAC,CAAQ,UAAC,CAAU,SAAC,CAAS,CAAC,CAAC;aA2KlF;iBA1KM;gBA2KL,IAAI,CA1KC,6BAAC,CAA6B,IAAC,EAAK,IAAA,CAAK,CAAC;aA2KhD;SACF,CA1KC,CAAC;QA4KH,IAAI,CA1KC,CAAC,CAAC,SAAC,EAAU;YA2KhB,IAAI,CA1KC,mBAAC,CAAmB,IAAC,CAAI,IAAI,aAAA,CAAc,IAAC,EAAK,CAAA,CAAE,CAAC,CAAC;SA2K3D;aA1KM,IAAA,OAAK,IAAU,OAAA,CAAQ,MAAC,IAAS,OAAA,CAAQ,MAAC,CAAM,WAAC,EAAY;YA2KlE,IAAI,CA1KC,mBAAC,CAAmB,IAAC,CAAI,IAAI,aAAA,CAAc,OAAC,CAAO,MAAC,CAAM,SAAC,EAAU,CAAA,CAAE,CAAC,CAAC;SA2K/E;aA1KM;YA2KL,IAAI,CA1KC,mBAAC,CAAmB,IAAC,CAAI,IAAI,aAAA,CAAc,IAAC,EAAK,CAAA,CAAE,CAAC,CAAC;SA2K3D;KACF;;;;IAxKA,sBAAA,GAAH;QA6KI,uBA5KM,OAAA,GAAU,IAAA,CAAK,IAAC,CAAI,mBAAC,CAAmB,CAAC;QA6K/C,uBA5KM,cAAA,GAAiB,QAAA,CAAS,IAAC,CA6K7B,OAAO,EA5KE,CAAA,KAAQ,KAAkB,IAAA,CAAK,gBAAC,CAAgB,KAAC,CAAK,SAAC,EAAU,KAAA,CAAM,KAAC,CAAK,CAAC,CAAC;QA6K5F,OA5KO,KAAA,CAAM,IAAC,CAAI,cAAC,EAAe,CAAA,MAAS,KAAY,MAAA,KAAW,IAAA,CAAK,CAAC;KA6KzE;;;;IA1KA,oBAAA,GAAH;QA+KI,uBA9KM,OAAA,GAAU,IAAA,CAAK,IAAC,CAAI,iBAAC,CAAiB,CAAC;QA+K7C,uBA9KM,cAAA,GAAiB,QAAA,CAAS,IAAC,CA+K7B,OAAO,EA9KE,CAAA,KAAQ,KAAgB,cAAA,CAAe,IAAC,CA+KpC,CAAC,IA9KC,CAAI,mBAAC,CAAmB,KAAC,CAAK,IAAC,CAAI,EAAE,IAAA,CAAK,cAAC,CAAc,KAAC,CAAK,KAAC,CAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QA+K7F,OA9KO,KAAA,CAAM,IAAC,CAAI,cAAC,EAAe,CAAA,MAAS,KAAY,MAAA,KAAW,IAAA,CAAK,CAAC;KA+KzE;;;;;IA5KA,cAAA,CAAA,MAAA,EAAH;QAkLI,uBAjLM,WAAA,GAAc,MAAA,CAAO,YAAC,GAAc,MAAA,CAAO,YAAC,CAAY,WAAC,GAAa,IAAA,CAAK;QAkLjF,IAAI,CAjLC,WAAC,IAAc,WAAA,CAAY,MAAC,KAAU,CAAA;YAAG,OAAO,EAAA,CAAG,IAAE,CAAI,CAAC;QAkL/D,uBAjLM,GAAA,GAAM,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,WAAC,CAAW,EAAE,CAAA,CAAI,KAiLhD;YACM,uBAjLM,KAAA,GAAQ,IAAA,CAAK,QAAC,CAAQ,CAAC,EAAE,MAAA,CAAO,CAAC;YAkLvC,qBAjLI,UAA+B,CAAC;YAkLpC,IAAI,KAjLC,CAAK,WAAC,EAAY;gBAkLrB,UAAU,GAjLG,kBAAA,CAAmB,KAAC,CAAK,WAAC,CAAW,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;aAkLzE;iBAjLM;gBAkLL,UAAU,GAjLG,kBAAA,CAAmB,KAAC,CAAK,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;aAkL7D;YACD,OAjLO,KAAA,CAAM,IAAC,CAAI,UAAC,CAAU,CAAC;SAkL/B,CAjLC,CAAC;QAkLH,OAjLO,cAAA,CAAe,GAAC,CAAG,CAAC;KAkL5B;;;;;IA/KA,mBAAA,CAAA,IAAA,EAAH;QAqLI,uBApLM,MAAA,GAAS,IAAA,CAAK,IAAC,CAAI,MAAC,GAAQ,CAAA,CAAE,CAAC;QAsLrC,uBApLM,sBAAA,GAAyB,IAAA,CAAK,KAAC,CAAK,CAAC,EAAE,IAAA,CAAK,MAAC,GAAQ,CAAA,CAAE;aAqLzB,OApLC,EAAO;aAqLR,GApLC,CAAG,CAAC,IAAI,IAAA,CAAK,uBAAC,CAAuB,CAAC,CAAC,CAAC;aAqLzC,MApLC,CAAM,CAAC,IAAI,CAAA,KAAM,IAAA,CAAK,CAAC;QAsL5D,OApLO,cAAA,CAAe,GAAC,CAAG,IAAC,CAAI,IAAC,CAAI,sBAAC,CAAsB,EAAE,CAAA,CAAI,KAoLrE;YACM,uBApLM,GAAA,GAAM,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,CAAC,CAAC,MAAC,CAAM,EAAE,CAAA,CAAI,KAoL/C;gBACQ,uBApLM,KAAA,GAAQ,IAAA,CAAK,QAAC,CAAQ,CAAC,EAAE,CAAA,CAAE,IAAC,CAAI,CAAC;gBAqLvC,qBApLI,UAA+B,CAAC;gBAqLpC,IAAI,KApLC,CAAK,gBAAC,EAAiB;oBAqL1B,UAAU,GApLG,kBAAA,CAAmB,KAAC,CAAK,gBAAC,CAAgB,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;iBAqL9E;qBApLM;oBAqLL,UAAU,GApLG,kBAAA,CAAmB,KAAC,CAAK,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;iBAqL7D;gBACD,OApLO,KAAA,CAAM,IAAC,CAAI,UAAC,CAAU,CAAC;aAqL/B,CApLC,CAAC;YAqLH,OApLO,cAAA,CAAe,GAAC,CAAG,CAAC;SAqL5B,CApLC,CAAC,CAAC;KAqLL;;;;;IAlLA,uBAAA,CAAA,CAAA,EAAH;QAyLI,uBAvLM,gBAAA,GAAmB,CAAA,CAAE,YAAC,GAAc,CAAA,CAAE,YAAC,CAAY,gBAAC,GAAkB,IAAA,CAAK;QAwLjF,IAAI,CAvLC,gBAAC,IAAmB,gBAAA,CAAiB,MAAC,KAAU,CAAA;YAAG,OAAO,IAAA,CAAK;QAwLpE,OAvLO,EAAA,IAAE,EAAK,CAAA,EAAG,MAAA,EAAQ,gBAAA,EAAiB,CAAC;KAwL5C;;;;;;IArLA,gBAAA,CAAA,SAAA,EAAA,IAAA,EAAH;QA6LI,uBA3LM,aAAA,GAAgB,IAAA,IAAQ,IAAA,CAAK,YAAC,GAAc,IAAA,CAAK,YAAC,CAAY,aAAC,GAAe,IAAA,CAAK;QA4LzF,IAAI,CA3LC,aAAC,IAAgB,aAAA,CAAc,MAAC,KAAU,CAAA;YAAG,OAAO,EAAA,CAAG,IAAE,CAAI,CAAC;QA4LnE,uBA3LM,cAAA,GAAiB,QAAA,CAAS,IAAC,CAAI,IAAC,CAAI,aAAC,CAAa,EAAE,CAAA,CAAI,KA2LlE;YACM,uBA3LM,KAAA,GAAQ,IAAA,CAAK,QAAC,CAAQ,CAAC,EAAE,IAAA,CAAK,CAAC;YA4LrC,qBA3LI,UAA+B,CAAC;YA4LpC,IAAI,KA3LC,CAAK,aAAC,EAAc;gBA4LvB,UAAU;oBACN,kBAAkB,CA3LC,KAAC,CAAK,aAAC,CAAa,SAAC,EAAU,IAAA,EAAM,IAAA,CAAK,IAAC,EAAK,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;aA4LtF;iBA3LM;gBA4LL,UAAU,GA3LG,kBAAA,CAAmB,KAAC,CAAK,SAAC,EAAU,IAAA,EAAM,IAAA,CAAK,IAAC,EAAK,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;aA4LjF;YACD,OA3LO,KAAA,CAAM,IAAC,CAAI,UAAC,CAAU,CAAC;SA4L/B,CA3LC,CAAC;QA4LH,OA3LO,KAAA,CAAM,IAAC,CAAI,cAAC,EAAe,CAAA,MAAS,KAAQ,MAAA,KAAW,IAAA,CAAK,CAAC;KA4LrE;;;;;IAzLA,UAAA,CAAA,MAAA,EAAH;QA+LI,uBA9LM,OAAA,GAAU,MAAA,CAAO,QAAC,CAAQ;QA+LhC,OA9LO,GAAA,CAAI,IAAC,CAAI,IAAC,CAAI,WAAC,CAAW,OAAC,EAAQ,MAAA,CAAO,EAAE,CAAA,YAAe,KA8LtE;YACM,MAAM,CA9LC,aAAC,GAAe,YAAA,CAAa;YA+LpC,MAAM,CA9LC,IAAC,GA8Ld,MAAA,CAAA,MAAA,CAAA,EAAA,EA9LsB,MAAG,CAAM,IAAC,EAAK,0BAAI,CAA0B,MAAC,CAAM,CAAC,OAAC,CAAO,CAAC;YA+L9E,OA9LO,IAAA,CAAK;SA+Lb,CA9LC,CAAC;KA+LJ;;;;;;IA5LA,WAAA,CAAA,OAAA,EAAA,MAAA,EAAH;QAmMI,OAlMO,UAAA,CAAW,OAAC,EAAQ,CAAA,CAAE,EAAE,CAAA,KAkMnC;YACM,uBAlMM,QAAA,GAAW,IAAA,CAAK,QAAC,CAAQ,CAAC,EAAE,MAAA,CAAO,CAAC;YAmM1C,OAlMO,QAAA,CAAS,OAAC,GAAS,kBAAA,CAAmB,QAAC,CAAQ,OAAC,CAAO,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC;gBAmMzD,kBAAkB,CAlMC,QAAC,CAAQ,MAAC,EAAO,IAAA,CAAK,MAAC,CAAM,CAAC,CAAC;SAmM7E,CAlMC,CAAC;KAmMJ;;;;;;IAhMA,QAAA,CAAA,KAAA,EAAA,QAAA,EAAH;QAuMI,uBAtMM,MAAA,GAAS,mBAAA,CAAoB,QAAC,CAAQ,CAAC;QAuM7C,uBAtMM,QAAA,GAAW,MAAA,GAAS,MAAA,CAAO,MAAC,CAAM,QAAC,GAAU,IAAA,CAAK,cAAC,CAAc;QAuMvE,OAtMO,QAAA,CAAS,GAAC,CAAG,KAAC,CAAK,CAAC;KAuM5B;CACF;AAED,AAaA,MAAA,cAAA,CAAA;;;;;;IAMA,WAAA,CAvNc,kBAAoB,EAA4B,WAAa,EAC7D,SAAW,EAsNzB;QAvNc,IAAd,CAAA,kBAAc,GAAA,kBAAA,CAAoB;QAA4B,IAA9D,CAAA,WAA8D,GAAA,WAAA,CAAa;QAC7D,IAAd,CAAA,SAAc,GAAA,SAAA,CAAW;KAAY;;;;;IA8NrC,QA5NG,CAAA,cAAA,EA4NH;QACI,uBA5NM,UAAA,GAAa,IAAA,CAAK,WAAC,CAAW,KAAC,CAAK;QA6N1C,uBA5NM,QAAA,GAAW,IAAA,CAAK,SAAC,GAAW,IAAA,CAAK,SAAC,CAAS,KAAC,GAAO,IAAA,CAAK;QA8N9D,IAAI,CA5NC,qBAAC,CAAqB,UAAC,EAAW,QAAA,EAAU,cAAA,CAAe,CAAC;QA6NjE,qBAAqB,CA5NC,IAAC,CAAI,WAAC,CAAW,IAAC,CAAI,CAAC;QA6N7C,IAAI,CA5NC,mBAAC,CAAmB,UAAC,EAAW,QAAA,EAAU,cAAA,CAAe,CAAC;KA6NhE;;;;;;;IAzNA,qBAAA,CAiOG,UAAoC,EAAE,QAAuC,EAC7E,QAAgC,EAlOtC;QAmOI,uBAhOM,QAAA,GAA6D,iBAAA,CAAkB,QAAC,CAAQ,CAAC;;QAmO/F,UAAU,CAhOC,QAAC,CAAQ,OAAC,CAAO,WAAC,IAgOjC;YACM,uBAhOM,eAAA,GAAkB,WAAA,CAAY,KAAC,CAAK,MAAC,CAAM;YAiOjD,IAAI,CAhOC,gBAAC,CAAgB,WAAC,EAAY,QAAA,CAAS,eAAC,CAAe,EAAE,QAAA,CAAS,CAAC;YAiOxE,OAhOO,QAAA,CAAS,eAAC,CAAe,CAAC;SAiOlC,CAhOC,CAAC;;QAmOH,OAAO,CAhOC,QAAC,EAAS,CAAA,CAA4B,EAAE,SAAW,KAgO/D;YACM,IAAI,CAhOC,6BAAC,CAA6B,CAAC,EAAE,QAAA,CAAS,CAAC;SAiOjD,CAhOC,CAAC;KAiOJ;;;;;;;IA9NA,gBAAA,CAsOG,UAAoC,EAAE,QAAkC,EACxE,aAAqC,EAvO3C;QAwOI,uBArOM,MAAA,GAAS,UAAA,CAAW,KAAC,CAAK;QAsOhC,uBArOM,IAAA,GAAO,QAAA,GAAW,QAAA,CAAS,KAAC,GAAO,IAAA,CAAK;QAuO9C,IAAI,MArOC,KAAU,IAAA,EAAM;;YAuOnB,IAAI,MArOC,CAAM,SAAC,EAAU;;gBAuOpB,uBArOM,OAAA,GAAU,aAAA,CAAc,UAAC,CAAU,MAAC,CAAM,MAAC,CAAM,CAAC;gBAsOxD,IAAI,OArOC,EAAQ;oBAsOX,IAAI,CArOC,qBAAC,CAAqB,UAAC,EAAW,QAAA,EAAU,OAAA,CAAQ,QAAC,CAAQ,CAAC;iBAsOpE;aACF;iBArOM;;gBAuOL,IAAI,CArOC,qBAAC,CAAqB,UAAC,EAAW,QAAA,EAAU,aAAA,CAAc,CAAC;aAsOjE;SACF;aArOM;YAsOL,IAAI,IArOC,EAAK;;gBAuOR,IAAI,CArOC,6BAAC,CAA6B,QAAC,EAAS,aAAA,CAAc,CAAC;aAsO7D;SACF;KACF;;;;;;IAnOA,6BAAA,CA0OG,KAA+B,EAAE,cAAsC,EA1O7E;QA2OI,IAAI,IAzOC,CAAI,kBAAC,CAAkB,YAAC,CAAY,KAAC,CAAK,KAAC,CAAK,QAAC,CAAQ,EAAE;YA0O9D,IAAI,CAzOC,0BAAC,CAA0B,KAAC,EAAM,cAAA,CAAe,CAAC;SA0OxD;aAzOM;YA0OL,IAAI,CAzOC,wBAAC,CAAwB,KAAC,EAAM,cAAA,CAAe,CAAC;SA0OtD;KACF;;;;;;IAvOA,0BAAA,CA8OG,KAA+B,EAAE,cAAsC,EA9O7E;QA+OI,uBA7OM,OAAA,GAAU,cAAA,CAAe,UAAC,CAAU,KAAC,CAAK,KAAC,CAAK,MAAC,CAAM,CAAC;QA8O9D,IAAI,OA7OC,IAAU,OAAA,CAAQ,MAAC,EAAO;YA8O7B,uBA7OM,YAAA,GAAe,OAAA,CAAQ,MAAC,CAAM,MAAC,EAAM,CAAE;YA8O7C,uBA7OM,QAAA,GAAW,OAAA,CAAQ,QAAC,CAAQ,mBAAC,EAAmB,CAAE;YA8OxD,IAAI,CA7OC,kBAAC,CAAkB,KAAC,CAAK,KAAC,CAAK,KAAC,CAAK,QAAC,EAAS,EAAA,YAAE,EAAa,KAAA,EAAO,QAAA,EAAS,CAAC,CAAC;SA8OtF;KACF;;;;;;IA3OA,wBAAA,CAkPG,KAA+B,EAAE,cAAsC,EAlP7E;QAmPI,uBAjPM,OAAA,GAAU,cAAA,CAAe,UAAC,CAAU,KAAC,CAAK,KAAC,CAAK,MAAC,CAAM,CAAC;QAmP9D,IAAI,OAjPC,EAAQ;YAkPX,uBAjPM,QAAA,GAAwC,iBAAA,CAAkB,KAAC,CAAK,CAAC;YAkPvE,uBAjPM,QAAA,GAAW,KAAA,CAAM,KAAC,CAAK,SAAC,GAAW,OAAA,CAAQ,QAAC,GAAU,cAAA,CAAe;YAmP3E,OAAO,CAjPC,QAAC,EAAS,CAAA,CAAI,EAAK,CAAG,KAiPpC,EAjP+C,IAAE,CAAI,6BAAC,CAA6B,CAAC,EAAE,QAAA,CAAS,CAAA,EAAC,CAAC,CAAC;YAmP5F,IAAI,OAjPC,CAAO,MAAC,EAAO;;gBAmPlB,OAAO,CAjPC,MAAC,CAAM,UAAC,EAAU,CAAE;;gBAmP5B,OAAO,CAjPC,QAAC,CAAQ,mBAAC,EAAmB,CAAE;aAkPxC;SACF;KACF;;;;;;;IA/OA,mBAAA,CAuPG,UAAoC,EAAE,QAAuC,EAC7E,QAAgC,EAxPtC;QAyPI,uBAtPM,QAAA,GAAoC,iBAAA,CAAkB,QAAC,CAAQ,CAAC;QAuPtE,UAAU,CAtPC,QAAC,CAAQ,OAAC,CAuPjB,CAAC,IADT,EArPe,IAAA,CAAK,cAAC,CAAc,CAAC,EAAE,QAAA,CAAS,CAAC,CAAC,KAAC,CAAK,MAAC,CAAM,EAAE,QAAA,CAAS,CAAC,EAAC,CAAE,CAAC;KAuP3E;;;;;;;IApPA,cAAA,CA4PG,UAAoC,EAAE,QAAkC,EACxE,cAAsC,EA7P5C;QA8PI,uBA3PM,MAAA,GAAS,UAAA,CAAW,KAAC,CAAK;QA4PhC,uBA3PM,IAAA,GAAO,QAAA,GAAW,QAAA,CAAS,KAAC,GAAO,IAAA,CAAK;QA6P9C,qBAAqB,CA3PC,MAAC,CAAM,CAAC;;QA8P9B,IAAI,MA3PC,KAAU,IAAA,EAAM;YA4PnB,IAAI,MA3PC,CAAM,SAAC,EAAU;;gBA6PpB,uBA3PM,OAAA,GAAU,cAAA,CAAe,kBAAC,CAAkB,MAAC,CAAM,MAAC,CAAM,CAAC;gBA4PjE,IAAI,CA3PC,mBAAC,CAAmB,UAAC,EAAW,QAAA,EAAU,OAAA,CAAQ,QAAC,CAAQ,CAAC;aA4PlE;iBA3PM;;gBA6PL,IAAI,CA3PC,mBAAC,CAAmB,UAAC,EAAW,QAAA,EAAU,cAAA,CAAe,CAAC;aA4PhE;SACF;aA3PM;YA4PL,IAAI,MA3PC,CAAM,SAAC,EAAU;;gBA6PpB,uBA3PM,OAAA,GAAU,cAAA,CAAe,kBAAC,CAAkB,MAAC,CAAM,MAAC,CAAM,CAAC;gBA6PjE,IAAI,IA3PC,CAAI,kBAAC,CAAkB,YAAC,CAAY,MAAC,CAAM,QAAC,CAAQ,EAAE;oBA4PzD,uBA3PM,MAAA,KAC4B,IAAC,CAAI,kBAAC,CAAkB,QAAC,CAAQ,MAAC,CAAM,QAAC,CAAQ,EAAC,CAAC;oBA4PrF,IAAI,CA3PC,kBAAC,CAAkB,KAAC,CAAK,MAAC,CAAM,QAAC,EAAS,IAAA,CAAK,CAAC;oBA4PrD,OAAO,CA3PC,QAAC,CAAQ,kBAAC,CAAkB,MAAC,CAAM,QAAC,CAAQ,CAAC;oBA4PrD,OAAO,CA3PC,SAAC,GAAW,MAAA,CAAO,YAAC,CAAY;oBA4PxC,OAAO,CA3PC,KAAC,GAAO,MAAA,CAAO,KAAC,CAAK,KAAC,CAAK;oBA4PnC,IAAI,OA3PC,CAAO,MAAC,EAAO;;;wBA8PlB,OAAO,CA3PC,MAAC,CAAM,MAAC,CAAM,MAAC,CAAM,YAAC,EAAa,MAAA,CAAO,KAAC,CAAK,KAAC,CAAK,CAAC;qBA4PhE;oBACD,uCAAuC,CA3PC,MAAC,CAAM,KAAC,CAAK,CAAC;iBA4PvD;qBA3PM;oBA4PL,uBA3PM,MAAA,GAAS,kBAAA,CAAmB,MAAC,CAAM,QAAC,CAAQ,CAAC;oBA4PnD,uBA3PM,kBAAA,GAAqB,MAAA,GAAS,MAAA,CAAO,MAAC,CAAM,wBAAC,GAA0B,IAAA,CAAK;oBA6PlF,OAAO,CA3PC,KAAC,GAAO,MAAA,CAAO;oBA4PvB,OAAO,CA3PC,QAAC,GAAU,kBAAA,CAAmB;oBA4PtC,IAAI,OA3PC,CAAO,MAAC,EAAO;;;wBA8PlB,OAAO,CA3PC,MAAC,CAAM,YAAC,CAAY,MAAC,EAAO,kBAAA,CAAmB,CAAC;qBA4PzD;oBAED,IAAI,CA3PC,mBAAC,CAAmB,UAAC,EAAW,IAAA,EAAM,OAAA,CAAQ,QAAC,CAAQ,CAAC;iBA4P9D;aACF;iBA3PM;;gBA6PL,IAAI,CA3PC,mBAAC,CAAmB,UAAC,EAAW,IAAA,EAAM,cAAA,CAAe,CAAC;aA4P5D;SACF;KACF;CACF;AAED,AASA;;;;AAIA,SAAA,uCAAA,CAxQC,IAAA,EAwQD;IACE,qBAAqB,CAxQC,IAAC,CAAI,KAAC,CAAK,CAAC;IAyQlC,IAAI,CAxQC,QAAC,CAAQ,OAAC,CAAO,uCAAC,CAAuC,CAAC;CAyQhE;;;;;AAKD,SAAA,kBAAA,CA3QC,QAAA,EA2QD;IACE,KAAK,qBA3QI,CAAA,GAAI,QAAA,CAAS,MAAC,EAAO,CAAA,EAAG,CAAA,GAAI,CAAA,CAAE,MAAC,EAAO;QA4Q7C,uBA3QM,KAAA,GAAQ,CAAA,CAAE,YAAC,CAAY;QA4Q7B,IAAI,KA3QC,IAAQ,KAAA,CAAM,aAAC;YAAc,OAAO,KAAA,CAAM,aAAC,CAAa;QA4Q7D,IAAI,KA3QC,IAAQ,KAAA,CAAM,SAAC;YAAU,OAAO,IAAA,CAAK;KA4Q3C;IAED,OA3QO,IAAA,CAAK;CA4Qb;;;;;AAKD,SAAA,mBAAA,CA9QC,QAAA,EA8QD;IACE,IAAI,CA9QC,QAAC;QAAS,OAAO,IAAA,CAAK;IAgR3B,KAAK,qBA9QI,CAAA,GAAI,QAAA,CAAS,MAAC,EAAO,CAAA,EAAG,CAAA,GAAI,CAAA,CAAE,MAAC,EAAO;QA+Q7C,uBA9QM,KAAA,GAAQ,CAAA,CAAE,YAAC,CAAY;QA+Q7B,IAAI,KA9QC,IAAQ,KAAA,CAAM,aAAC;YAAc,OAAO,KAAA,CAAM,aAAC,CAAa;KA+Q9D;IAED,OA9QO,IAAA,CAAK;CA+Qb;;;;;;AAMD,SAAA,iBAAA,CAjRC,IAAA,EAiRD;IACE,uBAjRMD,MAAA,GAAuC,EAAA,CAAG;IAmRhD,IAAI,IAjRC,EAAK;QAkRR,IAAI,CAjRC,QAAC,CAAQ,OAAC,CAAO,KAAC,IAAQA,MAAA,CAAI,KAAC,CAAK,KAAC,CAAK,MAAC,CAAM,GAAG,KAAA,CAAM,CAAC;KAkRjE;IAED,OAjROA,MAAA,CAAI;CAkRZ;;;;;AAKD,SAAA,gBAAA,CApRC,QAAA,EAoRD;IACE,KAAK,qBApRI,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,QAAA,CAAS,MAAC,EAAO,CAAA,EAAE,EAAG;QAqRxC,uBApRM,GAAA,GAAM,QAAA,CAAS,CAAC,CAAC,CAAC;QAqRxB,IAAI,GApRC,IAAM,IAAA,EAAM;YAqRf,MApRM,IAAI,KAAA,CAAM,CAoRtB,4BAAA,EApRuB,GAA+B,CAoRtD,kBAAA,EApRyD,CAAqB,CAoR9E,CApR+E,CAAE,CAAC;SAqR7E;KACF;CACF;;AD18CD;;;;;;;AASA,AACA,AAIA,AACA,AACA,AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,AAAA,MAAA,UAAA,CAAA;;;;;;;;IAgBA,WAAA,CAJc,MAAQ,EAAgB,KAAO,EAOtC,QANU,EAAQ,QAAU,EAAU,EAAI,EAGjD;QAJc,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAAgB,IAAtC,CAAA,KAAsC,GAAA,KAAA,CAAO;QAJnC,IAAV,CAAA,QAAU,GAAkB,EAAA,CAAG;QAY3B,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;SACjE;KACF;;;;;IAKH,IARG,UAAA,CAAA,QAAA,EAQH;QACI,IAAI,QARC,IAAW,IAAA,EAAM;YASpB,IAAI,CARC,QAAC,GAAU,KAAA,CAAM,OAAC,CAAO,QAAC,CAAQ,GAAG,QAAA,GAAW,CAAA,QAAE,CAAQ,CAAC;SASjE;aARM;YASL,IAAI,CARC,QAAC,GAAU,EAAA,CAAG;SASpB;KACF;;;;;;IAMH,IARG,mBAAA,CAAA,KAAA,EAQH;QACI,IAAI,SARC,EAAS,KAAU,OAAC,CAAA,KAAe,OAAC,CAAO,IAAC,CAAA,EAAK;YASpD,OAAO,CARC,IAAC,CAAI,sEAAC,CAAsE,CAAC;SAStF;QACD,IAAI,CARC,QAAC,GAAU,KAAA,CAAM;KASvB;;;;IAIH,OATG,GASH;QACI,uBATM,MAAA,GAAS;YAUb,kBAAkB,EATE,aAAA,CAAc,IAAC,CAAI,kBAAC,CAAkB;YAU1D,UAAU,EATE,aAAA,CAAc,IAAC,CAAI,UAAC,CAAU;SAU3C,CATC;QAUF,IAAI,CATC,MAAC,CAAM,aAAC,CAAa,IAAC,CAAI,OAAC,EAAQ,MAAA,CAAO,CAAC;QAUhD,OATO,IAAA,CAAK;KAUb;;;;IAIH,IAXG,OAAA,GAWH;QACI,OAXO,IAAA,CAAK,MAAC,CAAM,aAAC,CAAa,IAAC,CAAI,QAAC,EAAS;YAY9C,UAAU,EAXE,IAAA,CAAK,KAAC;YAYlB,WAAW,EAXE,IAAA,CAAK,WAAC;YAYnB,QAAQ,EAXE,IAAA,CAAK,QAAC;YAYhB,mBAAmB,EAXE,aAAA,CAAc,IAAC,CAAI,QAAC,CAAQ;YAYjD,mBAAmB,EAXE,IAAA,CAAK,mBAAC;YAY3B,gBAAgB,EAXE,aAAA,CAAc,IAAC,CAAI,gBAAC,CAAgB;SAYvD,CAXC,CAAC;KAYJ;;AAVI,UAAP,CAAA,UAAO,GAAoC;IAY3C,EAXE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,qBAAA,EAAsB,EAAC,EAAG;CAY9D,CAXC;;;;AAED,UAAD,CAAA,cAAC,GAAA,MAAA;IAcD,EAAC,IAAI,EAAE,MAAM,GAAG;IAChB,EAAC,IAAI,EAAE,cAAc,GAAG;IACxB,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,UAAU,EAAG,EAAE,EAAG,EAAC;IAC5E,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,UAAU,GAAG;CACnB,CAAC;AAZK,UAAP,CAAA,cAAO,GAAyD;IAchE,aAAa,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAchC,UAAU,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAc7B,qBAAqB,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAcxC,kBAAkB,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAcrC,oBAAoB,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAcvC,YAAY,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAc/B,YAAY,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAc/B,qBAAqB,EAbE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAcxC,SAAS,EAbE,CAAA,EAAG,IAAA,EAAM,YAAA,EAAc,IAAA,EAAM,CAAA,OAAE,EAAO,EAAG,EAAE;CAcrD,CAbC;AAgBF,AAgCA;;;;;;;;;AASA,AAAA,MAAA,kBAAA,CAAA;;;;;;IAmBA,WAAA,CA/Cc,MAAQ,EAAgB,KAAO,EAC/B,gBAAkB,EA8ChC;QA/Cc,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAAgB,IAAtC,CAAA,KAAsC,GAAA,KAAA,CAAO;QAC/B,IAAd,CAAA,gBAAc,GAAA,gBAAA,CAAkB;QATtB,IAAV,CAAA,QAAU,GAAkB,EAAA,CAAG;QA2D3B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAjD;YACM,IAAI,CAAC,YAAY,aAAa,EAAE;gBAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;aAC/B;SACF,CAAC,CAAC;KACJ;;;;;IAKH,IAnDG,UAAA,CAAA,QAAA,EAmDH;QACI,IAAI,QAnDC,IAAW,IAAA,EAAM;YAoDpB,IAAI,CAnDC,QAAC,GAAU,KAAA,CAAM,OAAC,CAAO,QAAC,CAAQ,GAAG,QAAA,GAAW,CAAA,QAAE,CAAQ,CAAC;SAoDjE;aAnDM;YAoDL,IAAI,CAnDC,QAAC,GAAU,EAAA,CAAG;SAoDpB;KACF;;;;;IAKH,IArDG,mBAAA,CAAA,KAAA,EAqDH;QACI,IAAI,SArDC,EAAS,KAAU,OAAC,CAAA,KAAe,OAAC,CAAO,IAAC,CAAA,EAAK;YAsDpD,OAAO,CArDC,IAAC,CAAI,qEAAC,CAAqE,CAAC;SAsDrF;QACD,IAAI,CArDC,QAAC,GAAU,KAAA,CAAM;KAsDvB;;;;;IAKH,WAxDG,CAAA,OAAA,EAwDH,EAxDkC,IAAA,CAAK,sBAAC,EAAsB,CAAE,EAAC;;;;IA4DjE,WA3DG,GA2DH,EA3DuB,IAAA,CAAK,YAAC,CAAY,WAAC,EAAW,CAAE,EAAC;;;;;;;;IAmExD,OAhEG,CAAA,MAAA,EAAA,OAAA,EAAA,OAAA,EAAA,QAAA,EAgEH;QACI,IAAI,MAhEC,KAAU,CAAA,IAAK,OAAA,IAAW,OAAA,IAAW,QAAA,EAAU;YAiElD,OAhEO,IAAA,CAAK;SAiEb;QAED,IAAI,OAhEO,IAAA,CAAK,MAAC,KAAU,QAAA,IAAY,IAAA,CAAK,MAAC,IAAS,OAAA,EAAS;YAiE7D,OAhEO,IAAA,CAAK;SAiEb;QAED,uBAhEM,MAAA,GAAS;YAiEb,kBAAkB,EAhEE,aAAA,CAAc,IAAC,CAAI,kBAAC,CAAkB;YAiE1D,UAAU,EAhEE,aAAA,CAAc,IAAC,CAAI,UAAC,CAAU;SAiE3C,CAhEC;QAiEF,IAAI,CAhEC,MAAC,CAAM,aAAC,CAAa,IAAC,CAAI,OAAC,EAAQ,MAAA,CAAO,CAAC;QAiEhD,OAhEO,KAAA,CAAM;KAiEd;;;;IA9DA,sBAAA,GAAH;QAmEI,IAAI,CAlEC,IAAC,GAAM,IAAA,CAAK,gBAAC,CAAgB,kBAAC,CAAkB,IAAC,CAAI,MAAC,CAAM,YAAC,CAAY,IAAC,CAAI,OAAC,CAAO,CAAC,CAAC;KAmE9F;;;;IAIH,IApEG,OAAA,GAoEH;QACI,OApEO,IAAA,CAAK,MAAC,CAAM,aAAC,CAAa,IAAC,CAAI,QAAC,EAAS;YAqE9C,UAAU,EApEE,IAAA,CAAK,KAAC;YAqElB,WAAW,EApEE,IAAA,CAAK,WAAC;YAqEnB,QAAQ,EApEE,IAAA,CAAK,QAAC;YAqEhB,mBAAmB,EApEE,aAAA,CAAc,IAAC,CAAI,QAAC,CAAQ;YAqEjD,mBAAmB,EApEE,IAAA,CAAK,mBAAC;YAqE3B,gBAAgB,EApEE,aAAA,CAAc,IAAC,CAAI,gBAAC,CAAgB;SAqEvD,CApEC,CAAC;KAqEJ;;AAnEI,kBAAP,CAAA,UAAO,GAAoC;IAqE3C,EApEE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,eAAA,EAAgB,EAAC,EAAG;CAqExD,CApEC;;;;AAED,kBAAD,CAAA,cAAC,GAAA,MAAA;IAuED,EAAC,IAAI,EAAE,MAAM,GAAG;IAChB,EAAC,IAAI,EAAE,cAAc,GAAG;IACxB,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;AArEK,kBAAP,CAAA,cAAO,GAAyD;IAuEhE,QAAQ,EAtEE,CAAA,EAAG,IAAA,EAAM,WAAA,EAAa,IAAA,EAAM,CAAA,aAAE,EAAa,EAAG,EAAE,EAAE,IAAA,EAAM,KAAA,EAAM,EAAE;IAuE1E,aAAa,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuEhC,UAAU,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuE7B,qBAAqB,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuExC,kBAAkB,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuErC,oBAAoB,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuEvC,YAAY,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuE/B,MAAM,EAtEE,CAAA,EAAG,IAAA,EAAM,WAAA,EAAY,EAAE;IAuE/B,YAAY,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuE/B,qBAAqB,EAtEE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IAuExC,SAAS,EAtEE,CAAA,EAAG,IAAA,EAAM,YAAA,EAAc,IAAA,EAAM,CAAA,OAAE,EAAQ,CAAA,eAAE,EAAgB,gBAAA,EAAkB,gBAAA,EAAkB,iBAAA,CAAkB,EAAC,EAAG,EAAE;CAuE/H,CAtEC;AAyEF,AAwCA;;;;AAIA,SAAA,aAAA,CAlHC,CAAA,EAkHD;IACE,OA9JO,CAAA,KAAM,EAAA,IAAM,CAAA,CAAE,CAAC,CAAC;CA+JxB;;ADrZD;;;;;;;AASA,AAEA,AACA,AACA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,AAAA,MAAA,gBAAA,CAAA;;;;;;;IAgBA,WAAA,CAFc,MAAQ,EAAgB,OAAS,EAAoB,QAAU,EAC/D,GAAK,EACnB;QAFc,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QAAgB,IAAtC,CAAA,OAAsC,GAAA,OAAA,CAAS;QAAoB,IAAnE,CAAA,QAAmE,GAAA,QAAA,CAAU;QAC/D,IAAd,CAAA,GAAc,GAAA,GAAA,CAAK;QART,IAAV,CAAA,OAAU,GAAoB,EAAA,CAAG;QAEvB,IAAV,CAAA,MAAU,GAAkB,KAAA,CAAM;QAA/B,IAAH,CAAA,uBAA0B,GAEqB,EAAA,KAAE,EAAM,KAAA,EAAM,CAAC;QAU1D,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAjD;YACM,IAAI,CAAC,YAAY,aAAa,EAAE;gBAC9B,IAAI,CAAC,MAAM,EAAE,CAAC;aACf;SACF,CAAC,CAAC;KACJ;;;;IAIH,IAPG,QAAA,GAOH,EAP4B,OAAO,IAAA,CAAK,MAAC,CAAM,EAAC;;;;IAWhD,kBATG,GASH;QACI,IAAI,CATC,KAAC,CAAK,OAAC,CAAO,SAAC,CAAS,CAAC,IAAI,IAAA,CAAK,MAAC,EAAM,CAAE,CAAC;QAUjD,IAAI,CATC,cAAC,CAAc,OAAC,CAAO,SAAC,CAAS,CAAC,IAAI,IAAA,CAAK,MAAC,EAAM,CAAE,CAAC;QAU1D,IAAI,CATC,MAAC,EAAM,CAAE;KAUf;;;;;IAKH,IAXG,gBAAA,CAAA,IAAA,EAWH;QACI,uBAXM,OAAA,GAAU,KAAA,CAAM,OAAC,CAAO,IAAC,CAAI,GAAG,IAAA,GAAO,IAAA,CAAK,KAAC,CAAK,GAAC,CAAG,CAAC;QAY7D,IAAI,CAXC,OAAC,GAAS,OAAA,CAAQ,MAAC,CAAM,CAAC,IAAI,CAAA,CAAE,CAAC,CAAC,CAAC;KAYzC;;;;;IAKH,WAdG,CAAA,OAAA,EAcH,EAd8C,IAAA,CAAK,MAAC,EAAM,CAAE,EAAC;;;;IAkB7D,WAjBG,GAiBH,EAjBwB,IAAA,CAAK,YAAC,CAAY,WAAC,EAAW,CAAE,EAAC;;;;IAEtD,MAAA,GAAH;QAoBI,IAAI,CAnBC,IAAC,CAAI,KAAC,IAAQ,CAAA,IAAE,CAAI,cAAC,IAAiB,CAAA,IAAE,CAAI,MAAC,CAAM,SAAC;YAAU,OAAA;QAoBnE,uBAnBM,cAAA,GAAiB,IAAA,CAAK,cAAC,EAAc,CAAE;;QAsB7C,IAAI,IAnBC,CAAI,MAAC,KAAU,cAAA,EAAgB;YAoBlC,IAAI,CAnBC,OAAC,CAAO,OAAC,CAoBV,CAAC,IAnBI,IAAA,CAAK,QAAC,CAAQ,eAAC,CAAe,IAAC,CAAI,OAAC,CAAO,aAAC,EAAc,CAAA,EAAG,cAAA,CAAe,CAAC,CAAC;YAoBvF,OAAO,CAnBC,OAAC,CAAO,cAAC,CAAc,CAAC,IAAC,CAAI,MAAC,IAAS,IAAA,CAAK,MAAC,GAAQ,MAAA,CAAO,CAAC;SAoBtE;KACF;;;;;IAjBA,YAAA,CAAA,MAAA,EAAH;QAuBI,OAtBO,CAAA,IAAoB,KAuBhB,MAAM,CAtBC,QAAC,CAAQ,IAAC,CAAI,OAAC,EAAQ,IAAA,CAAK,uBAAC,CAAuB,KAAC,CAAK,CAAC;KAuB9E;;;;IApBA,cAAA,GAAH;QAyBI,OAxBO,IAAA,CAAK,KAAC,CAAK,IAAC,CAAI,IAAC,CAAI,YAAC,CAAY,IAAC,CAAI,MAAC,CAAM,CAAC;YAyBlD,IAAI,CAxBC,cAAC,CAAc,IAAC,CAAI,IAAC,CAAI,YAAC,CAAY,IAAC,CAAI,MAAC,CAAM,CAAC,CAAC;KAyB9D;;AAvBI,gBAAP,CAAA,UAAO,GAAoC;IAyB3C,EAxBE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA;gBAyBvB,QAAQ,EAxBE,oBAAA;gBAyBV,QAAQ,EAxBE,kBAAA;aAyBX,EAxBC,EAAG;CAyBJ,CAxBC;;;;AAED,gBAAD,CAAA,cAAC,GAAA,MAAA;IA2BD,EAAC,IAAI,EAAE,MAAM,GAAG;IAChB,EAAC,IAAI,EAAE,UAAU,GAAG;IACpB,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,iBAAiB,GAAG;CAC1B,CAAC;AAzBK,gBAAP,CAAA,cAAO,GAAyD;IA2BhE,OAAO,EA1BE,CAAA,EAAG,IAAA,EAAM,eAAA,EAAiB,IAAA,EAAM,CAAA,UAAE,EAAW,EAAA,WAAE,EAAY,IAAA,EAAK,EAAC,EAAG,EAAE;IA2B/E,gBAAgB,EA1BE,CAAA,EAAG,IAAA,EAAM,eAAA,EAAiB,IAAA,EAAM,CAAA,kBAAE,EAAmB,EAAA,WAAE,EAAY,IAAA,EAAK,EAAC,EAAG,EAAE;IA2BhG,yBAAyB,EA1BE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;IA2B5C,kBAAkB,EA1BE,CAAA,EAAG,IAAA,EAAM,KAAA,EAAM,EAAE;CA2BpC,CA1BC,AA6BF,AA8BC;;ADtND;;;;;;;;;;;;AAkBA,AAAA,MAAA,aAAA,CAAA;IAAA,WAAA,GAAA;QACE,IAAF,CAAA,MAAQ,GACsB,IAAA,CAAK;QAAjC,IAAF,CAAA,KAAO,GACwB,IAAA,CAAK;QAAlC,IAAF,CAAA,QAAU,GACkC,IAAA,CAAK;QAA/C,IAAF,CAAA,QAAU,GACG,IAAI,sBAAA,EAAuB,CAAE;QAAxC,IAAF,CAAA,SAAW,GAC2B,IAAA,CAAK;KAA1C;CAAA;AAED,AAaA;;;;;AAKA,AAAA,MAAA,sBAAA,CAAA;IAAA,WAAA,GAAA;QAVU,IAAV,CAAA,QAAU,GAAW,IAAI,GAAA,EAA0B,CAAG;KAsErD;;;;;;;IApDD,oBAfG,CAAA,SAAA,EAAA,MAAA,EAeH;QACI,uBAfM,OAAA,GAAU,IAAA,CAAK,kBAAC,CAAkB,SAAC,CAAS,CAAC;QAgBnD,OAAO,CAfC,MAAC,GAAQ,MAAA,CAAO;QAgBxB,IAAI,CAfC,QAAC,CAAQ,GAAC,CAAG,SAAC,EAAU,OAAA,CAAQ,CAAC;KAgBvC;;;;;;;;IAQH,sBAhBG,CAAA,SAAA,EAgBH;QACI,uBAhBM,OAAA,GAAU,IAAA,CAAK,UAAC,CAAU,SAAC,CAAS,CAAC;QAiB3C,IAAI,OAhBC,EAAQ;YAiBX,OAAO,CAhBC,MAAC,GAAQ,IAAA,CAAK;SAiBvB;KACF;;;;;;IAMH,mBAhBG,GAgBH;QACI,uBAhBM,QAAA,GAAW,IAAA,CAAK,QAAC,CAAQ;QAiB/B,IAAI,CAhBC,QAAC,GAAU,IAAI,GAAA,EAAI,CAAE;QAiB1B,OAhBO,QAAA,CAAS;KAiBjB;;;;;IAKH,kBAnBG,CAAA,QAAA,EAmBH,EAnB6D,IAAA,CAAK,QAAC,GAAU,QAAA,CAAS,EAAC;;;;;IAwBvF,kBAtBG,CAAA,SAAA,EAsBH;QACI,qBAtBI,OAAA,GAAU,IAAA,CAAK,UAAC,CAAU,SAAC,CAAS,CAAC;QAwBzC,IAAI,CAtBC,OAAC,EAAQ;YAuBZ,OAAO,GAtBG,IAAI,aAAA,EAAc,CAAE;YAuB9B,IAAI,CAtBC,QAAC,CAAQ,GAAC,CAAG,SAAC,EAAU,OAAA,CAAQ,CAAC;SAuBvC;QAED,OAtBO,OAAA,CAAQ;KAuBhB;;;;;IAKH,UAzBG,CAAA,SAAA,EAyBH,EAzBsD,OAAO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,SAAC,CAAS,IAAI,IAAA,CAAK,EAAC;CA0BlG,AAED,AAGC;;AD7GD;;;;;;;AASA,AAEA,AACA,AACA,AACA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAA,MAAA,YAAA,CAAA;;;;;;;;IAcA,WAAA,CAJc,cAAgB,EAAgC,QAAU,EAC1D,QAAU,EAA0B,IAAO,EAC3C,cAAgB,EAE9B;QAJc,IAAd,CAAA,cAAc,GAAA,cAAA,CAAgB;QAAgC,IAA9D,CAAA,QAA8D,GAAA,QAAA,CAAU;QAC1D,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QACV,IAAd,CAAA,cAAc,GAAA,cAAA,CAAgB;QAVpB,IAAV,CAAA,SAAU,GAAoC,IAAA,CAAK;QACzC,IAAV,CAAA,eAAU,GAAuC,IAAA,CAAK;QAEnD,IAAH,CAAA,cAAiB,GACG,IAAI,YAAA,EAAiB,CAAG;QAAzC,IAAH,CAAA,gBAAmB,GACG,IAAI,YAAA,EAAiB,CAAG;QAY1C,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,cAAc,CAAC;QACnC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACtD;;;;IAIH,WARG,GAQH,EARwB,IAAA,CAAK,cAAC,CAAc,sBAAC,CAAsB,IAAC,CAAI,IAAC,CAAI,CAAC,EAAC;;;;IAY/E,QAVG,GAUH;QACI,IAAI,CAVC,IAAC,CAAI,SAAC,EAAU;;;YAanB,uBAVM,OAAA,GAAU,IAAA,CAAK,cAAC,CAAc,UAAC,CAAU,IAAC,CAAI,IAAC,CAAI,CAAC;YAW1D,IAAI,OAVC,IAAU,OAAA,CAAQ,KAAC,EAAM;gBAW5B,IAAI,OAVC,CAAO,SAAC,EAAU;;oBAYrB,IAAI,CAVC,MAAC,CAAM,OAAC,CAAO,SAAC,EAAU,OAAA,CAAQ,KAAC,CAAK,CAAC;iBAW/C;qBAVM;;oBAYL,IAAI,CAVC,YAAC,CAAY,OAAC,CAAO,KAAC,EAAM,OAAA,CAAQ,QAAC,IAAW,IAAA,CAAK,CAAC;iBAW5D;aACF;SACF;KACF;;;;;IAKH,IAZG,gBAAA,GAYH,EAZqC,OAAO,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ,EAAC;;;;;IAiBpE,IAfG,uBAAA,GAeH,EAf4D,OAAO,IAAA,CAAK,QAAC,CAAQ,EAAC;;;;IAmBlF,IAjBG,WAAA,GAiBH,EAjB+B,OAAO,CAAA,CAAE,IAAC,CAAI,SAAC,CAAS,EAAC;;;;IAqBxD,IAnBG,SAAA,GAmBH;QACI,IAAI,CAnBC,IAAC,CAAI,SAAC;YAAU,MAAM,IAAI,KAAA,CAAM,yBAAC,CAAyB,CAAC;QAoBhE,OAnBO,IAAA,CAAK,SAAC,CAAS,QAAC,CAAQ;KAoBhC;;;;IAIH,IArBG,cAAA,GAqBH;QACI,IAAI,CArBC,IAAC,CAAI,SAAC;YAAU,MAAM,IAAI,KAAA,CAAM,yBAAC,CAAyB,CAAC;QAsBhE,QArBO,IAAA,CAAK,eAAmB,EAAe;KAsB/C;;;;IAIH,IAvBG,kBAAA,GAuBH;QACI,IAAI,IAvBC,CAAI,eAAC,EAAgB;YAwBxB,OAvBO,IAAA,CAAK,eAAC,CAAe,QAAC,CAAQ,IAAC,CAAI;SAwB3C;QACD,OAvBO,EAAA,CAAG;KAwBX;;;;;IAKH,MAvBG,GAuBH;QACI,IAAI,CAvBC,IAAC,CAAI,SAAC;YAAU,MAAM,IAAI,KAAA,CAAM,yBAAC,CAAyB,CAAC;QAwBhE,IAAI,CAvBC,QAAC,CAAQ,MAAC,EAAM,CAAE;QAwBvB,uBAvBM,GAAA,GAAM,IAAA,CAAK,SAAC,CAAS;QAwB3B,IAAI,CAvBC,SAAC,GAAW,IAAA,CAAK;QAwBtB,IAAI,CAvBC,eAAC,GAAiB,IAAA,CAAK;QAwB5B,OAvBO,GAAA,CAAI;KAwBZ;;;;;;;IAOH,MAzBG,CAAA,GAAA,EAAA,cAAA,EAyBH;QACI,IAAI,CAzBC,SAAC,GAAW,GAAA,CAAI;QA0BrB,IAAI,CAzBC,eAAC,GAAiB,cAAA,CAAe;QA0BtC,IAAI,CAzBC,QAAC,CAAQ,MAAC,CAAM,GAAC,CAAG,QAAC,CAAQ,CAAC;KA0BpC;;;;IAIH,UA3BG,GA2BH;QACI,IAAI,IA3BC,CAAI,SAAC,EAAU;YA4BlB,uBA3BM,CAAA,GAAI,IAAA,CAAK,SAAC,CAAS;YA4BzB,IAAI,CA3BC,SAAC,CAAS,OAAC,EAAO,CAAE;YA4BzB,IAAI,CA3BC,SAAC,GAAW,IAAA,CAAK;YA4BtB,IAAI,CA3BC,eAAC,GAAiB,IAAA,CAAK;YA4B5B,IAAI,CA3BC,gBAAC,CAAgB,IAAC,CAAI,CAAC,CAAC,CAAC;SA4B/B;KACF;;;;;;IAMH,YA/BG,CAAA,cAAA,EAAA,QAAA,EA+BH;QACI,IAAI,IA/BC,CAAI,WAAC,EAAY;YAgCpB,MA/BM,IAAI,KAAA,CAAM,6CAAC,CAA6C,CAAC;SAgChE;QACD,IAAI,CA/BC,eAAC,GAAiB,cAAA,CAAe;QAgCtC,uBA/BM,QAAA,GAAW,cAAA,CAAe,eAAC,CAAe;QAgChD,uBA/BM,SAAA,IAAiB,EAAA,QAAC,CAAQ,YAAC,GAAc,SAAC,CAAA,CAAS;QAgCzD,QAAQ,GA/BG,QAAA,IAAY,IAAA,CAAK,QAAC,CAAQ;QAgCrC,uBA/BM,OAAA,GAAU,QAAA,CAAS,uBAAC,CAAuB,SAAC,CAAS,CAAC;QAgC5D,uBA/BM,aAAA,GAAgB,IAAA,CAAK,cAAC,CAAc,kBAAC,CAAkB,IAAC,CAAI,IAAC,CAAI,CAAC,QAAC,CAAQ;QAgCjF,uBA/BM,QAAA,GAAW,IAAI,cAAA,CAAe,cAAC,EAAe,aAAA,EAAe,IAAA,CAAK,QAAC,CAAQ,QAAC,CAAQ,CAAC;QAgC3F,IAAI,CA/BC,SAAC,GAAW,IAAA,CAAK,QAAC,CAAQ,eAAC,CAAe,OAAC,EAAQ,IAAA,CAAK,QAAC,CAAQ,MAAC,EAAO,QAAA,CAAS,CAAC;;;QAkCxF,IAAI,CA/BC,cAAC,CAAc,YAAC,EAAY,CAAE;QAgCnC,IAAI,CA/BC,cAAC,CAAc,IAAC,CAAI,IAAC,CAAI,SAAC,CAAS,QAAC,CAAQ,CAAC;KAgCnD;;AA9BI,YAAP,CAAA,UAAO,GAAoC;IAgC3C,EA/BE,IAAA,EAAM,SAAA,EAAW,IAAA,EAAM,CAAA,EAAE,QAAC,EAAS,eAAA,EAAiB,QAAA,EAAU,QAAA,EAAS,EAAC,EAAG;CAgC5E,CA/BC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IAkCD,EAAC,IAAI,EAAE,sBAAsB,GAAG;IAChC,EAAC,IAAI,EAAE,gBAAgB,GAAG;IAC1B,EAAC,IAAI,EAAE,wBAAwB,GAAG;IAClC,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,MAAM,EAAG,EAAE,EAAG,EAAC;IACxE,EAAC,IAAI,EAAE,iBAAiB,GAAG;CAC1B,CAAC;AAhCK,YAAP,CAAA,cAAO,GAAyD;IAkChE,gBAAgB,EAjCE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,CAAA,UAAE,EAAU,EAAG,EAAE;IAkC1D,kBAAkB,EAjCE,CAAA,EAAG,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,CAAA,YAAE,EAAY,EAAG,EAAE;CAkC7D,CAjCC;AAoCF,AA8BA,MAAA,cAAA,CAAA;;;;;;IAMA,WAAA,CAnEc,KAAO,EAAwB,aAAe,EAC9C,MAAQ,EAkEtB;QAnEc,IAAd,CAAA,KAAc,GAAA,KAAA,CAAO;QAAwB,IAA7C,CAAA,aAA6C,GAAA,aAAA,CAAe;QAC9C,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;KAAS;;;;;;IA2E/B,GAzEG,CAAA,KAAA,EAAA,aAAA,EAyEH;QACI,IAAI,KAzEC,KAAS,cAAA,EAAgB;YA0E5B,OAzEO,IAAA,CAAK,KAAC,CAAK;SA0EnB;QAED,IAAI,KAzEC,KAAS,sBAAA,EAAwB;YA0EpC,OAzEO,IAAA,CAAK,aAAC,CAAa;SA0E3B;QAED,OAzEO,IAAA,CAAK,MAAC,CAAM,GAAC,CAAG,KAAC,EAAM,aAAA,CAAc,CAAC;KA0E9C;CACF,AAED,AAOC;;ADrQD;;;;;;;AASA,AAGA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA;;;;;;AAMA,AAAA,MAAA,kBAAA,CAAA;;;;;;;IAOA,OAPY,CAAA,KAAA,EAAA,EAAA,EAOZ,GAPY;CAQX;;;;;;;;;;;;AAYD,AAAA,MAAA,iBAAA,CAAA;;;;;;IAMA,OAXG,CAAA,KAAA,EAAA,EAAA,EAWH;QACI,OAXO,MAAA,CAAO,IAAC,CAAI,EAAC,EAAE,EAAG,MAAM,EAAA,CAAG,IAAE,CAAI,CAAC,CAAC;KAY3C;CACF;;;;;;;;;;AAUD,AAAA,MAAA,YAAA,CAAA;;;;;;IAMA,OAfG,CAAA,KAAA,EAAA,EAAA,EAeH,EAfsE,OAAO,EAAA,CAAG,IAAE,CAAI,CAAC,EAAC;CAgBvF;;;;;;;;;;;;;AAaD,AAAA,MAAA,eAAA,CAAA;;;;;;;;IAUA,WAAA,CAlBc,MAAQ,EAAQ,YAAc,EAAuB,QAAU,EAC/D,QAAU,EAAkB,kBAAoB,EAiB9D;QAlBc,IAAd,CAAA,MAAc,GAAA,MAAA,CAAQ;QACR,IAAd,CAAA,QAAc,GAAA,QAAA,CAAU;QAAkB,IAA1C,CAAA,kBAA0C,GAAA,kBAAA,CAAoB;QAqB1D,MAAM,WAAW,GAAG,CAAC,CAAQ,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnF,MAAM,SAAS,GAAG,CAAC,CAAQ,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAkB,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;KACtF;;;;;IAIH,eAtBG,GAsBH;QACI,uBAtBM,YAAA,GAAe,MAAA,CAAO,IAAC,CAAI,IAAC,CAAI,MAAC,CAAM,MAAC,EAAO,CAAA,CAAI,KAAU,CAAA,YAAa,aAAA,CAAc,CAAC;QAuB/F,IAAI,CAtBC,YAAC,GAAc,SAAA,CAAU,IAAC,CAAI,YAAC,EAAa,MAAM,IAAA,CAAK,OAAC,EAAO,CAAE,CAAC,SAAC,CAAS,MAsBrF,GAtB2F,CAAG,CAAC;KAuB5F;;;;IAIH,OAxBG,GAwBH;QACI,uBAxBM,QAAA,GAAW,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,WAAC,CAAW,CAAC;QAyBhD,OAxBO,IAAA,CAAK,aAAC,CAAa,QAAC,EAAS,IAAA,CAAK,MAAC,CAAM,MAAC,CAAM,CAAC;KAyBzD;;;;IAIH,WA1BG,GA0BH,EA1BwB,IAAA,CAAK,YAAC,CAAY,WAAC,EAAW,CAAE,EAAC;;;;;;IAEtD,aAAA,CAAA,QAAA,EAAA,MAAA,EAAH;QA+BI,uBA9BM,GAAA,GAAyB,EAAA,CAAG;QA+BlC,KAAK,uBA9BM,KAAA,IAAS,MAAA,EAAQ;;YAgC1B,IAAI,KA9BC,CAAK,YAAC,IAAe,CAAA,KAAE,CAAK,OAAC,IAAU,KAAA,CAAM,aAAC,EAAc;gBA+B/D,uBA9BM,WAAA,GAAc,KAAA,CAAM,aAAC,CAAa;gBA+BxC,GAAG,CA9BC,IAAC,CAAI,IAAC,CAAI,aAAC,CAAa,WAAC,CAAW,MAAC,EAAO,WAAA,CAAY,MAAC,CAAM,CAAC,CAAC;;aAiCtE;iBA9BM,IAAA,KAAK,CAAK,YAAC,IAAe,CAAA,KAAE,CAAK,OAAC,EAAQ;gBAoBrD,GAAA,CAnBU,IAAC,CAAI,IAAC,CAAI,aAAC,CAAa,QAAC,EAAS,KAAA,CAAM,CAAC,CAAC;;aAiC/C;iBA9BM,IAAA,KAAK,CAAK,QAAC,EAAS;gBA+BzB,GAAG,CA9BC,IAAC,CAAI,IAAC,CAAI,aAAC,CAAa,QAAC,EAAS,KAAA,CAAM,QAAC,CAAQ,CAAC,CAAC;aA+BxD;SACF;QACD,OA9BO,QAAA,CAAS,IAAC,CAAI,IAAC,CAAI,GAAC,CAAG,CAAC,CAAC;KA+BjC;;;;;;IA5BA,aAAA,CAAA,QAAA,EAAA,KAAA,EAAH;QAmCI,OAlCO,IAAA,CAAK,kBAAC,CAAkB,OAAC,CAAO,KAAC,EAAM,MAkClD;YACM,uBAlCM,OAAA,GAAU,IAAA,CAAK,MAAC,CAAM,IAAC,CAAI,QAAC,CAAQ,QAAC,EAAS,KAAA,CAAM,CAAC;YAmC3D,OAlCO,QAAA,CAAS,IAAC,CAAI,OAAC,EAAQ,CAAA,MAAS,KAkC7C;gBACQ,KAAK,CAlCC,aAAC,GAAe,MAAA,CAAO;gBAmC7B,OAlCO,IAAA,CAAK,aAAC,CAAa,MAAC,CAAM,MAAC,EAAO,MAAA,CAAO,MAAC,CAAM,CAAC;aAmCzD,CAlCC,CAAC;SAmCJ,CAlCC,CAAC;KAmCJ;;AAjCI,eAAP,CAAA,UAAO,GAAoC;IAmC3C,EAlCE,IAAA,EAAM,UAAA,EAAW;CAmClB,CAlCC;;;;AAED,eAAD,CAAA,cAAC,GAAA,MAAA;IAqCD,EAAC,IAAI,EAAE,MAAM,GAAG;IAChB,EAAC,IAAI,EAAE,qBAAqB,GAAG;IAC/B,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,kBAAkB,GAAG;CAC3B,CAAC,AAGF,AAkBC;;ADpMD;;;;;;;AASA,AACA,AACA,AACA,AACA,AAGA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA;;;;AAIA,MACC,iBAAA,GAAA,CAAA,YAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,gBAAA,CAAA,CAAA;;;;;AAID,AAEC,MAAA,oBAAA,GAAA,IAAA,cAAA,CAAA,sBAAA,CAAA,CAAA;;;;AAED,AAGC,MAAA,oBAAA,GAAA,IAAA,cAAA,CAAA,sBAAA,CAAA,CAAA;AADD,AAGO,MAAM,gBAAA,GAA+B;IAF1C,QAAK;IACL,EAAC,OAGC,EAAQ,aAAA,EAAe,QAAA,EAAU,oBAAA,EAAqB;IAFxD;QACE,OAAO,EAGE,MAAA;QAFT,UAAQ,EAGI,WAAA;QAFZ,IAAA,EAGM;YAFJ,cAAc,EAGE,aAAA,EAAe,sBAAA,EAAwB,QAAA,EAAU,QAAA;YAFjE,qBAAU,EAGa,QAAA,EAAU,MAAA,EAAQ,oBAAA;YAFzC,CAAC,mBAGC,EAAoB,IAAI,QAAA,EAAS,CAAE,EAAE,CAAA,kBAAE,EAAmB,IAAI,QAAA,EAAS,CAAE;SAF5E;KACF;IACD,sBAAM;IACN,EAAC,OAGC,EAAQ,cAAA,EAAgB,UAAA,EAAY,SAAA,EAAW,IAAA,EAAM,CAAA,MAAE,CAAM,EAAC;IAFhE,EAAC,OAGC,EAAQ,qBAAA,EAAuB,QAAA,EAAU,sBAAA,EAAuB;IAFlE,eAAe;IACf,YAAU;IACV,iBAAU;IACV,EAAC,OAGC,EAAQ,oBAAA,EAAsB,QAAA,EAAU,EAAA,aAAE,EAAc,KAAA,EAAM,EAAC;CAFlE,CAGC;;;;AACF,AAAA,SAAA,kBAAA,GAAA;IACE,OACO,IAAI,YAAA,CAAa,QAAC,EAAS,MAAA,CAAO,CAAC;CAA3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDD,AAAA,MAAA,YAAA,CAAA;;;;;IAKA,WAAA,CAAe,KAAQ,EAAK,MAAS,EAArC,GAA4C;;;;;;;;;;;;;;;IAe5C,OAFG,OAAA,CAAA,MAAA,EAAA,MAAA,EAEH;QACI,OAFO;YAGL,QAAQ,EAFE,YAAA;YAGV,SAAS,EAFE;gBAGT,gBAAgB;gBAChB,aAAa,CAFC,MAAC,CAAM;gBAGrB;oBACE,OAAO,EAFE,oBAAA;oBAGT,UAAU,EAFE,mBAAA;oBAGZ,IAAI,EAFE,CAAA,CAAE,MAAC,EAAO,IAAI,QAAA,EAAS,EAAG,IAAI,QAAA,EAAS,CAAE,CAAC;iBAGjD;gBACD,EAAC,OAFC,EAAQ,oBAAA,EAAsB,QAAA,EAAU,MAAA,GAAS,MAAA,GAAS,EAAA,EAAG;gBAG/D;oBACE,OAAO,EAFE,gBAAA;oBAGT,UAAU,EAFE,uBAAA;oBAGZ,IAAI,EAFE;wBAGJ,gBAAgB,EAFE,CAAA,IAAK,MAAA,CAAO,aAAC,CAAa,EAAE,IAAI,QAAA,EAAS,CAAE,EAAE,oBAAA;qBAGhE;iBACF;gBACD;oBACE,OAAO,EAFE,kBAAA;oBAGT,WAAW,EAFE,MAAA,IAAU,MAAA,CAAO,kBAAC,GAAoB,MAAA,CAAO,kBAAC;wBAGR,YAAY;iBAChE;gBACD,EAAC,OAFC,EAAQ,YAAA,EAAc,KAAA,EAAO,IAAA,EAAM,UAAA,EAAY,kBAAA,EAAmB;gBAGpE,wBAAwB,EAFC;aAG1B;SACF,CAFC;KAGH;;;;;;IAMH,OAHG,QAAA,CAAA,MAAA,EAGH;QACI,OAHO,EAAA,QAAE,EAAS,YAAA,EAAc,SAAA,EAAW,CAAA,aAAE,CAAa,MAAC,CAAM,CAAC,EAAC,CAAC;KAIrE;;AAFI,YAAP,CAAA,UAAO,GAAoC;IAI3C,EAHE,IAAA,EAAM,QAAA,EAAU,IAAA,EAAM,CAAA,EAAE,YAAC,EAAa,iBAAA,EAAmB,OAAA,EAAS,iBAAA,EAAkB,EAAC,EAAG;CAIzF,CAHC;;;;AAED,YAAD,CAAA,cAAC,GAAA,MAAA;IAMD,EAAC,IAAI,EARE,SAAS,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,QAAyB,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA,EAAA;IASzC,EAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EARE,IAAA,EAAA,QAAA,EAAA,EAAkB,EAAA;CAS/C,CAAC;AAGF,AAUA;;;;;;AAMA,AAAA,SAAA,uBAAA,CACI,wBAA0C,EAAE,QAAgB,EAAE,OADlE,GAC0F,EAAE,EAD5F;IAEE,OA7BO,OAAA,CAAQ,OAAC,GAAS,IAAI,oBAAA,CAAqB,wBAAC,EAAyB,QAAA,CAAS;QA8B5D,IA7BI,oBAAA,CAAqB,wBAAC,EAAyB,QAAA,CAAS,CAAC;CA8BvF;;;;;AAKD,AAAA,SAAA,mBAAA,CAxBC,MAAA,EAwBD;IACE,IARI,MAxBC,EAAO;QAiCV,MAhCM,IAAI,KAAA,CAyBT,CAOL,oGAAA,CAPK,CAxB0G,CAAC;KAyB9G;IASA,OAhCO,SAAA,CAAU;CAiClB;;;;;;;;;;;;;;;;;;AAkBD,AAAA,SAAA,aAAA,CAzBC,MAAA,EAyBD;IACE,OAjCO;QAkCL,EAAC,OAjCC,EAAQ,4BAAA,EAA8B,KAAA,EAAO,IAAA,EAAM,QAAA,EAAU,MAAA,EAAO;QAkCtE,EAAC,OAjCC,EAAQ,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,QAAA,EAAU,MAAA,EAAO;KAkCjD,CAjCC;CAkCH;;;;;;;;;;;;;;;AA0ED,AAAA,SAAA,WAAA,CACI,GAAmB,EAAE,aAA4B,EAAE,QAAgC,EACnF,QAAkB,EAAE,QAAkB,EAAE,MAA6B,EAAE,QAAkB,EACzF,MAAiB,EAAE,IAHvB,GAG4C,EAAE,EAAE,mBAAyC,EACrF,kBAAuC,EAJ3C;IAKE,uBA9CM,MAAA,GAAS,IAAI,MAAA,CA+Cf,IAAI,EA9CE,aAAA,EAAe,QAAA,EAAU,QAAA,EAAU,QAAA,EAAU,MAAA,EAAQ,QAAA,EAAU,OAAA,CAAQ,MAAC,CAAM,CAAC,CAAC;IAgD1F,IAAI,mBA9CC,EAAoB;QA+CvB,MAAM,CA9CC,mBAAC,GAAqB,mBAAA,CAAoB;KA+ClD;IAED,IAAI,kBA9CC,EAAmB;QA+CtB,MAAM,CA9CC,kBAAC,GAAoB,kBAAA,CAAmB;KA+ChD;IAED,IAAI,IA9CC,CAAI,YAAC,EAAa;QA+CrB,MAAM,CA9CC,YAAC,GAAc,IAAA,CAAK,YAAC,CAAY;KA+CzC;IAED,IAAI,IA9CC,CAAI,aAAC,EAAc;QA+CtB,uBA9CM,GAAA,GAAMD,OAAA,EAAO,CAAE;QA+CrB,MAAM,CA9CC,MAAC,CAAM,SAAC,CAAS,CAAC,IA8C7B;YACM,GAAG,CA9CC,QAAC,CAAQ,CA8CnB,cAAA,EA9CoB,EAAsB,CAAC,CAAC,WAAC,GAAY,IAAC,CA8C1D,CA9C8D,CAAE,CAAC;YA+C3D,GAAG,CA9CC,GAAC,CAAG,CAAC,CAAC,QAAC,EAAQ,CAAE,CAAC;YA+CtB,GAAG,CA9CC,GAAC,CAAG,CAAC,CAAC,CAAC;YA+CX,GAAG,CA9CC,WAAC,EAAW,CAAE;SA+CnB,CA9CC,CAAC;KA+CJ;IAED,OA9CO,MAAA,CAAO;CA+Cf;;;;;AAKD,AAAA,SAAA,SAAA,CAzCC,MAAA,EAyCD;IACE,OAjDO,MAAA,CAAO,WAAC,CAAW,IAAC,CAAI;CAkDhC;;;;;;;;;;;;AAYD,AAAA,MAAA,iBAAA,CAAA;;;;IAMA,WAAA,CAzCsB,QAAU,EAyChC;QAzCsB,IAAtB,CAAA,QAAsB,GAAA,QAAA,CAAU;QAHtB,IAAV,CAAA,cAAU,GAA0B,KAAA,CAAM;QAChC,IAAV,CAAA,yBAAU,GAA4B,IAAI,OAAA,EAAa,CAAG;KAEjB;;;;IA6CzC,cA3CG,GA2CH;QACI,uBA3CM,CAAA,GAAkB,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,oBAAC,EAAqB,OAAA,CAAQ,OAAC,CAAO,IAAC,CAAI,CAAC,CAAC;QA4CvF,OA3CO,CAAA,CAAE,IAAC,CAAI,MA2ClB;YACM,qBA3CI,OAAA,KAAoB,IAAA,EAAA,CAAO;YA4C/B,uBA3CM,GAAA,GAAM,IAAI,OAAA,CAAQ,CAAC,IAAI,OAAA,GAAU,CAAA,CAAE,CAAC;YA4C1C,uBA3CM,MAAA,GAAS,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,MAAC,CAAM,CAAC;YA4CzC,uBA3CM,IAAA,GAAO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,oBAAC,CAAoB,CAAC;YA6CrD,IAAI,IA3CC,CAAI,gBAAC,CAAgB,IAAC,CAAI,IAAI,IAAA,CAAK,eAAC,CAAe,IAAC,CAAI,EAAE;gBA4C7D,OAAO,CA3CC,IAAC,CAAI,CAAC;aA6Cf;iBA3CM,IAAA,IAAK,CAAI,iBAAC,KAAqB,UAAA,EAAY;gBA4ChD,MAAM,CA3CC,2BAAC,EAA2B,CAAE;gBA4CrC,OAAO,CA3CC,IAAC,CAAI,CAAC;aA6Cf;iBA3CM,IAAA,IAAK,CAAI,iBAAC,KAAqB,SAAA,EAAW;gBA4C/C,MAAM,CA3CC,KAAC,CAAK,kBAAC,GAAoB,MA2C1C;;oBAEU,IAAI,CA3CC,IAAC,CAAI,cAAC,EAAe;wBA4CxB,IAAI,CA3CC,cAAC,GAAgB,IAAA,CAAK;wBA4C3B,OAAO,CA3CC,IAAC,CAAI,CAAC;wBA4Cd,OA3CO,IAAA,CAAK,yBAAC,CAAyB;;qBA8CvC;yBA3CM;wBA4CL,QA3CO,EAAA,CAAG,IAAE,CAAQ,EAAI;qBA4CzB;iBACF,CA3CC;gBA4CF,MAAM,CA3CC,iBAAC,EAAiB,CAAE;aA6C5B;iBA3CM;gBA4CL,MA3CM,IAAI,KAAA,CAAM,CA2CxB,oCAAA,EA3CyB,IAAuC,CAAI,iBAAC,CA2CrE,CAAA,CA3CsF,CAAG,CAAC;aA6BxF;YAiBI,OA3CO,GAAA,CAAI;SA4CZ,CA3CC,CAAC;KA6BL;;;;;IAoBF,iBA9CG,CAAA,wBAAA,EA8CH;QACI,uBA9CM,IAAA,GAAO,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,oBAAC,CAAoB,CAAC;QA+CrD,uBA9CM,SAAA,GAAY,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,eAAC,CAAe,CAAC;QA+CrD,uBA9CM,MAAA,GAAS,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,MAAC,CAAM,CAAC;QA+CzC,uBA9CM,GAAA,GAAM,IAAA,CAAK,QAAC,CAAQ,GAAC,CAAG,cAAC,CAAc,CAAC;QAgD9C,IAfG,wBA/BE,KAA4B,GAAA,CAAI,UAAC,CAAU,CAAC,CAAC,EAAE;YA+ClD,OAfE;SAgBH;QAED,IAAI,IA9CC,CAAI,eAAC,CAAe,IAAC,CAAI,EAAE;YA+C9B,MAAM,CA9CC,iBAAC,EAAiB,CAAE;SA+C5B;aA9CM,IAAA,IAAK,CAAI,gBAAC,CAAgB,IAAC,CAAI,EAAE;YA+CtC,MAAM,CA9CC,2BAAC,EAA2B,CAAE;SA+CtC;QAED,SAfQ,CA/BE,eAAC,EAAe,CAAE;QA+C5B,MAfG,CA/BI,sBAAC,CAAsB,GAAC,CAAG,cAAC,CAAc,CAAC,CAAC,CAAC,CAAC;QA+CrD,IAfE,CA/BG,yBAAC,CAAyB,IAAC,oBAAI,IAAC,GAAM,CAAC;QA+C5C,IAfE,CA/BG,yBAAC,CAAyB,QAAC,EAAQ,CAAE;KA+C3C;;;;;IA5CA,eAAA,CAAA,IAAA,EAAH;QAkDI,OAjDO,IAAA,CAAK,iBAAC,KAAqB,gBAAA,IAAoB,IAAA,CAAK,iBAAC,KAAqB,IAAA;YAkD7E,IAAI,CAjDC,iBAAC,KAAqB,SAAA,CAAU;KAkD1C;;;;;IA/CA,gBAAA,CAAA,IAAA,EAAH;QAqDI,OApDO,IAAA,CAAK,iBAAC,KAAqB,iBAAA,IAAqB,IAAA,CAAK,iBAAC,KAAqB,KAAA,CAAM;KAqDzF;;AAnDI,iBAAP,CAAA,UAAO,GAAoC;IAqD3C,EApDE,IAAA,EAAM,UAAA,EAAW;CAqDlB,CApDC;;;;AAED,iBAAD,CAAA,cAAC,GAAA,MAAA;IAuDD,EAAC,IAAI,EAAE,QAAQ,GAAG;CACjB,CAAC;AAGF,AAgBA;;;;AAIA,AAAA,SAAA,iBAAA,CA1EC,CAAA,EA0ED;IACE,OAzFO,CAAA,CAAE,cAAC,CAAc,IAAC,CAAI,CAAC,CAAC,CAAC;CA0FjC;;;;;AAKD,AAAA,SAAA,oBAAA,CA7EC,CAAA,EA6ED;IACE,OA5FO,CAAA,CAAE,iBAAC,CAAiB,IAAC,CAAI,CAAC,CAAC,CAAC;CA6FpC;;;;;;AAMD,AA5EC,MAAA,kBAAA,GA6EG,IAAI,cAAc,CAAuC,oBAAoB,CAAC,CAAC;;;;AAInF,AAAA,SAAA,wBAAA,GAAA;IACE,OA7FO;QA8FL,iBAAiB;QACjB;YACE,OAAO,EA7FE,eAAA;YA8FT,KAAK,EA7FE,IAAA;YA8FP,UAAU,EA7FE,iBAAA;YA8FZ,IAAI,EA7FE,CAAA,iBAAE,CAAiB;SA8F1B;QACD,EAAC,OA7FC,EAAQ,kBAAA,EAAoB,UAAA,EAAY,oBAAA,EAAsB,IAAA,EAAM,CAAA,iBAAE,CAAiB,EAAC;QA8F1F,EAAC,OA7FC,EAAQ,sBAAA,EAAwB,KAAA,EAAO,IAAA,EAAM,WAAA,EAAa,kBAAA,EAAmB;KA8FhF,CA7FC;CA8FH;;ADrgBD;;;;;;;;;;;;AAeA,AACA;;;AAGA,AADC,MAAA,OAAA,GAAA,IAAA,OAAA,CAAA,mBAAA,CAAA,CAAA;;ADlBD;;;;;;GAMG,AAGH,AACA,AAA4B;;ADV5B;;;;;;GAMG,AAIH,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AAA4D;;AD3B5D;;;;;;;;;;;;AAaA,AAAw1B;0EAE9wB;;ADf1E;;GAEG,AAEH,AAEA,AACA,AAAkC;;"}