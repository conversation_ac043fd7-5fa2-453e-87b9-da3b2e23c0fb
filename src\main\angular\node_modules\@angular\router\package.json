{"_args": [["@angular/router@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/router@4.2.5", "_id": "@angular/router@4.2.5", "_inBundle": false, "_integrity": "sha1-fzZAiUk6saBurF8MYowPn9sRAI8=", "_location": "/@angular/router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/router@4.2.5", "name": "@angular/router", "escapedName": "@angular%2frouter", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/router/-/router-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - the routing library", "es2015": "./@angular/router.js", "homepage": "https://github.com/angular/angular/blob/master/modules/%40angular/router/README.md", "keywords": ["angular", "router"], "license": "MIT", "main": "./bundles/router.umd.js", "module": "./@angular/router.es5.js", "name": "@angular/router", "peerDependencies": {"@angular/core": "4.2.5", "@angular/common": "4.2.5", "@angular/platform-browser": "4.2.5", "rxjs": "^5.0.1"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./router.d.ts", "version": "4.2.5"}