[{"__symbolic": "module", "version": 3, "metadata": {"DomElementSchemaRegistry": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./element_schema_registry", "name": "ElementSchemaRegistry"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "hasProperty": [{"__symbolic": "method"}], "hasElement": [{"__symbolic": "method"}], "securityContext": [{"__symbolic": "method"}], "getMappedPropName": [{"__symbolic": "method"}], "getDefaultComponentElementName": [{"__symbolic": "method"}], "validateProperty": [{"__symbolic": "method"}], "validateAttribute": [{"__symbolic": "method"}], "allKnownElementNames": [{"__symbolic": "method"}], "normalizeAnimationStyleProperty": [{"__symbolic": "method"}], "normalizeAnimationStyleValue": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"DomElementSchemaRegistry": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./element_schema_registry", "name": "ElementSchemaRegistry"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "hasProperty": [{"__symbolic": "method"}], "hasElement": [{"__symbolic": "method"}], "securityContext": [{"__symbolic": "method"}], "getMappedPropName": [{"__symbolic": "method"}], "getDefaultComponentElementName": [{"__symbolic": "method"}], "validateProperty": [{"__symbolic": "method"}], "validateAttribute": [{"__symbolic": "method"}], "allKnownElementNames": [{"__symbolic": "method"}], "normalizeAnimationStyleProperty": [{"__symbolic": "method"}], "normalizeAnimationStyleValue": [{"__symbolic": "method"}]}}}}]