{"_args": [["@angular/tsc-wrapped@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "@angular/tsc-wrapped@4.2.5", "_id": "@angular/tsc-wrapped@4.2.5", "_inBundle": false, "_integrity": "sha1-Ci/CMwYXgNK+QCmWGHh4wng4t+M=", "_location": "/@angular/tsc-wrapped", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/tsc-wrapped@4.2.5", "name": "@angular/tsc-wrapped", "escapedName": "@angular%2ftsc-wrapped", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/@angular/compiler-cli"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/tsc-wrapped/-/tsc-wrapped-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/angular/angular/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"tsickle": "^0.21.0"}, "description": "Wraps the tsc CLI, allowing extensions.", "homepage": "https://github.com/angular/angular/blob/master/tools/@angular/tsc-wrapped", "license": "MIT", "name": "@angular/tsc-wrapped", "peerDependencies": {"typescript": "^2.1.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "version": "4.2.5"}