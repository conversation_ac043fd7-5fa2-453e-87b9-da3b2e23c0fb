{"_args": [["amdefine@1.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "amdefine@1.0.1", "_id": "amdefine@1.0.1", "_inBundle": false, "_integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "_location": "/amdefine", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "amdefine@1.0.1", "name": "amdefine", "escapedName": "amdefine", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/handlebars/source-map", "/istanbul-reports/source-map", "/scss-tokenizer/source-map", "/source-map-loader/source-map", "/stylus/source-map"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/amdefine/-/amdefine-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/jrburke"}, "bugs": {"url": "https://github.com/jrburke/amdefine/issues"}, "description": "Provide AMD's define() API for declaring modules in the AMD format", "engines": {"node": ">=0.4.2"}, "homepage": "http://github.com/jrburke/amdefine", "license": "BSD-3-<PERSON><PERSON> OR MIT", "main": "./amdefine.js", "name": "amdefine", "repository": {"type": "git", "url": "git+https://github.com/jrburke/amdefine.git"}, "version": "1.0.1"}