[{"__symbolic": "module", "version": 3, "metadata": {"HtmlParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parser", "name": "<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "parse": [{"__symbolic": "method"}]}}}, "exports": [{"from": "./parser", "export": ["ParseTreeResult", "TreeError"]}]}, {"__symbolic": "module", "version": 1, "metadata": {"HtmlParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./parser", "name": "<PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "parse": [{"__symbolic": "method"}]}}}, "exports": [{"from": "./parser", "export": ["ParseTreeResult", "TreeError"]}]}]