{"_args": [["caw@2.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "caw@2.0.1", "_id": "caw@2.0.1", "_inBundle": false, "_integrity": "sha512-Cg8/ZSBEa8ZVY9HspcGUYaK63d/bN7rqS3CYCzEGUxuYv6UlmcjzDUz2fCFFHyTvUW5Pk0I+3hkA3iXlIj6guA==", "_location": "/caw", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "caw@2.0.1", "name": "caw", "escapedName": "caw", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/download"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/caw/-/caw-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/caw/issues"}, "dependencies": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}, "description": "Construct HTTP/HTTPS agents for tunneling proxies", "devDependencies": {"ava": "*", "create-cert": "^1.0.4", "get-port": "^3.1.0", "got": "^7.0.0", "pify": "^3.0.0", "proxyquire": "^1.7.9", "sinon": "^2.3.1", "universal-url": "1.0.0-alpha", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/kevva/caw#readme", "keywords": ["http", "https", "proxy", "tunnel"], "license": "MIT", "name": "caw", "repository": {"type": "git", "url": "git+https://github.com/kevva/caw.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1", "xo": {"rules": {"ava/no-skip-test": 0}}}