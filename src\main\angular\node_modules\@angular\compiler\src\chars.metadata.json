[{"__symbolic": "module", "version": 3, "metadata": {"$EOF": 0, "$TAB": 9, "$LF": 10, "$VTAB": 11, "$FF": 12, "$CR": 13, "$SPACE": 32, "$BANG": 33, "$DQ": 34, "$HASH": 35, "$$": 36, "$PERCENT": 37, "$AMPERSAND": 38, "$SQ": 39, "$LPAREN": 40, "$RPAREN": 41, "$STAR": 42, "$PLUS": 43, "$COMMA": 44, "$MINUS": 45, "$PERIOD": 46, "$SLASH": 47, "$COLON": 58, "$SEMICOLON": 59, "$LT": 60, "$EQ": 61, "$GT": 62, "$QUESTION": 63, "$0": 48, "$9": 57, "$A": 65, "$E": 69, "$F": 70, "$X": 88, "$Z": 90, "$LBRACKET": 91, "$BACKSLASH": 92, "$RBRACKET": 93, "$CARET": 94, "$_": 95, "$a": 97, "$e": 101, "$f": 102, "$n": 110, "$r": 114, "$t": 116, "$u": 117, "$v": 118, "$x": 120, "$z": 122, "$LBRACE": 123, "$BAR": 124, "$RBRACE": 125, "$NBSP": 160, "$PIPE": 124, "$TILDA": 126, "$AT": 64, "$BT": 96, "isWhitespace": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 9}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 32}}, "right": {"__symbolic": "binop", "operator": "==", "left": {"__symbolic": "reference", "name": "code"}, "right": 160}}}, "isDigit": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "<=", "left": 48, "right": {"__symbolic": "reference", "name": "code"}}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 57}}}, "isAsciiLetter": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 97}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 122}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 65}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 90}}}}, "isAsciiHexDigit": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 97}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 102}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 65}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 70}}}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isDigit"}, "arguments": [{"__symbolic": "reference", "name": "code"}]}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"$EOF": 0, "$TAB": 9, "$LF": 10, "$VTAB": 11, "$FF": 12, "$CR": 13, "$SPACE": 32, "$BANG": 33, "$DQ": 34, "$HASH": 35, "$$": 36, "$PERCENT": 37, "$AMPERSAND": 38, "$SQ": 39, "$LPAREN": 40, "$RPAREN": 41, "$STAR": 42, "$PLUS": 43, "$COMMA": 44, "$MINUS": 45, "$PERIOD": 46, "$SLASH": 47, "$COLON": 58, "$SEMICOLON": 59, "$LT": 60, "$EQ": 61, "$GT": 62, "$QUESTION": 63, "$0": 48, "$9": 57, "$A": 65, "$E": 69, "$F": 70, "$X": 88, "$Z": 90, "$LBRACKET": 91, "$BACKSLASH": 92, "$RBRACKET": 93, "$CARET": 94, "$_": 95, "$a": 97, "$e": 101, "$f": 102, "$n": 110, "$r": 114, "$t": 116, "$u": 117, "$v": 118, "$x": 120, "$z": 122, "$LBRACE": 123, "$BAR": 124, "$RBRACE": 125, "$NBSP": 160, "$PIPE": 124, "$TILDA": 126, "$AT": 64, "$BT": 96, "isWhitespace": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 9}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 32}}, "right": {"__symbolic": "binop", "operator": "==", "left": {"__symbolic": "reference", "name": "code"}, "right": 160}}}, "isDigit": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "<=", "left": 48, "right": {"__symbolic": "reference", "name": "code"}}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 57}}}, "isAsciiLetter": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 97}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 122}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 65}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 90}}}}, "isAsciiHexDigit": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 97}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 102}}, "right": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": ">=", "left": {"__symbolic": "reference", "name": "code"}, "right": 65}, "right": {"__symbolic": "binop", "operator": "<=", "left": {"__symbolic": "reference", "name": "code"}, "right": 70}}}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isDigit"}, "arguments": [{"__symbolic": "reference", "name": "code"}]}}}}}]