[{"__symbolic": "module", "version": 3, "metadata": {"GeneratedFile": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "toTypeScript": {"__symbolic": "function"}}}, {"__symbolic": "module", "version": 1, "metadata": {"GeneratedFile": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "toTypeScript": {"__symbolic": "function"}}}]