[{"__symbolic": "module", "version": 3, "metadata": {"RoundProgressComponent": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component"}, "arguments": [{"selector": "round-progress", "template": "\n    <svg xmlns=\"http://www.w3.org/2000/svg\" [attr.viewBox]=\"_viewBox\">\n      <circle\n        fill=\"none\"\n        [attr.cx]=\"radius\"\n        [attr.cy]=\"radius\"\n        [attr.r]=\"radius - stroke / 2\"\n        [style.stroke]=\"resolveColor(background)\"\n        [style.stroke-width]=\"stroke\"/>\n\n      <path\n        #path\n        fill=\"none\"\n        [style.stroke-width]=\"stroke\"\n        [style.stroke]=\"resolveColor(color)\"\n        [style.stroke-linecap]=\"rounded ? 'round' : ''\"\n        [attr.transform]=\"getPathTransform()\"/>\n    </svg>\n  ", "host": {"role": "progressbar", "[attr.aria-valuemin]": "current", "[attr.aria-valuemax]": "max", "[style.width]": "responsive ? '' : _diameter + 'px'", "[style.height]": "_elementHeight", "[style.padding-bottom]": "_paddingBottom", "[class.responsive]": "responsive", "$quoted$": ["role", "[attr.aria-valuemin]", "[attr.aria-valuemax]", "[style.width]", "[style.height]", "[style.padding-bottom]", "[class.responsive]"]}, "styles": [":host {\n      display: block;\n      position: relative;\n      overflow: hidden;\n    }", ":host.responsive {\n      width: 100%;\n      padding-bottom: 100%;\n    }", ":host.responsive > svg {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n    }"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./round-progress.service", "name": "RoundProgressService"}, {"__symbolic": "reference", "module": "./round-progress.ease", "name": "RoundProgressEase"}, {"__symbolic": "reference", "module": "./round-progress.config", "name": "RoundProgressConfig"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}]}], "_animateChange": [{"__symbolic": "method"}], "_setPath": [{"__symbolic": "method"}], "_clamp": [{"__symbolic": "method"}], "getPathTransform": [{"__symbolic": "method"}], "resolveColor": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "_path": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild"}, "arguments": ["path"]}]}], "current": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "max": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "radius": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "animation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "animationDelay": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "duration": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "stroke": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "color": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "background": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "responsive": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "clockwise": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "semicircle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "rounded": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "onRender": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"RoundProgressComponent": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component"}, "arguments": [{"selector": "round-progress", "template": "\n    <svg xmlns=\"http://www.w3.org/2000/svg\" [attr.viewBox]=\"_viewBox\">\n      <circle\n        fill=\"none\"\n        [attr.cx]=\"radius\"\n        [attr.cy]=\"radius\"\n        [attr.r]=\"radius - stroke / 2\"\n        [style.stroke]=\"resolveColor(background)\"\n        [style.stroke-width]=\"stroke\"/>\n\n      <path\n        #path\n        fill=\"none\"\n        [style.stroke-width]=\"stroke\"\n        [style.stroke]=\"resolveColor(color)\"\n        [style.stroke-linecap]=\"rounded ? 'round' : ''\"\n        [attr.transform]=\"getPathTransform()\"/>\n    </svg>\n  ", "host": {"role": "progressbar", "[attr.aria-valuemin]": "current", "[attr.aria-valuemax]": "max", "[style.width]": "responsive ? '' : _diameter + 'px'", "[style.height]": "_elementHeight", "[style.padding-bottom]": "_paddingBottom", "[class.responsive]": "responsive"}, "styles": [":host {\n      display: block;\n      position: relative;\n      overflow: hidden;\n    }", ":host.responsive {\n      width: 100%;\n      padding-bottom: 100%;\n    }", ":host.responsive > svg {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n    }"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./round-progress.service", "name": "RoundProgressService"}, {"__symbolic": "reference", "module": "./round-progress.ease", "name": "RoundProgressEase"}, {"__symbolic": "reference", "module": "./round-progress.config", "name": "RoundProgressConfig"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}]}], "_animateChange": [{"__symbolic": "method"}], "_setPath": [{"__symbolic": "method"}], "_clamp": [{"__symbolic": "method"}], "getPathTransform": [{"__symbolic": "method"}], "resolveColor": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "_path": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild"}, "arguments": ["path"]}]}], "current": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "max": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "radius": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "animation": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "animationDelay": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "duration": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "stroke": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "color": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "background": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "responsive": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "clockwise": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "semicircle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "rounded": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "onRender": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}}]}]}}}}]