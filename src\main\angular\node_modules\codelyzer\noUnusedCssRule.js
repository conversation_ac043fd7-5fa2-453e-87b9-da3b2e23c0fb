"use strict";
var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var Lint = require("tslint");
var ng2Walker_1 = require("./angular/ng2Walker");
var utils_1 = require("./util/utils");
var basicCssAstVisitor_1 = require("./angular/styles/basicCssAstVisitor");
var basicTemplateAstVisitor_1 = require("./angular/templates/basicTemplateAstVisitor");
var compiler_1 = require("@angular/compiler");
var templateParser_1 = require("./angular/templates/templateParser");
var logger_1 = require("./util/logger");
var ngVersion_1 = require("./util/ngVersion");
var CssSelectorTokenizer = require('css-selector-tokenizer');
var getSymbolName = function (t) {
    var expr = t.expression;
    if (t.expression && t.expression.name) {
        expr = t.expression.name;
    }
    return expr.text;
};
var isEncapsulationEnabled = function (encapsulation) {
    if (!encapsulation) {
        return true;
    }
    else {
        if (getSymbolName(encapsulation) !== 'ViewEncapsulation') {
            return false;
        }
        else {
            var encapsulationType = encapsulation.name.text;
            if (/^(Emulated|Native)$/.test(encapsulationType)) {
                return true;
            }
        }
    }
    return false;
};
var lang = require('cssauron')({
    tag: function (node) {
        return (node.name || '').toLowerCase();
    },
    contents: function (node) { return ''; },
    id: function (node) {
        return this.attr(node, 'id');
    },
    'class': function (node) {
        var classBindings = (node.inputs || [])
            .filter(function (b) { return b.type === compiler_1.PropertyBindingType.Class; })
            .map(function (b) { return b.name; }).join(' ');
        var classAttr = node.attrs.filter(function (a) { return a.name.toLowerCase() === 'class'; }).pop();
        var staticClasses = '';
        if (classAttr) {
            staticClasses = classAttr.value + ' ';
        }
        return staticClasses + classBindings;
    },
    parent: function (node) {
        return node.parentNode;
    },
    children: function (node) {
        return node.children;
    },
    attr: function (node, attr) {
        var targetAttr = node.attrs.filter(function (a) { return a.name === attr; }).pop();
        if (targetAttr) {
            return targetAttr.value;
        }
        return undefined;
    }
});
var ElementVisitor = (function (_super) {
    __extends(ElementVisitor, _super);
    function ElementVisitor() {
        return _super.apply(this, arguments) || this;
    }
    ElementVisitor.prototype.visitElement = function (ast, fn) {
        var _this = this;
        fn(ast);
        ast.children.forEach(function (c) {
            if (c instanceof compiler_1.ElementAst) {
                c.parentNode = ast;
            }
            _this.visit(c, fn);
        });
    };
    return ElementVisitor;
}(basicTemplateAstVisitor_1.BasicTemplateAstVisitor));
var hasSelector = function (s, type) {
    if (!s) {
        return false;
    }
    if (s.type === 'selector' || s.type === 'selectors') {
        return (s.nodes || []).some(function (n) { return hasSelector(n, type); });
    }
    else {
        return s.type === type;
    }
};
var dynamicFilters = {
    id: function (ast, selector) {
        return (ast.inputs || []).some(function (i) { return i.name === 'id'; });
    },
    attribute: function (ast, selector) {
        return (ast.inputs || []).some(function (i) { return i.type === compiler_1.PropertyBindingType.Attribute; });
    },
    'class': function (ast, selector) {
        return (ast.inputs || []).some(function (i) { return i.name === 'className' || i.name === 'ngClass'; });
    }
};
var ElementFilterVisitor = (function (_super) {
    __extends(ElementFilterVisitor, _super);
    function ElementFilterVisitor() {
        return _super.apply(this, arguments) || this;
    }
    ElementFilterVisitor.prototype.shouldVisit = function (ast, strategies, selectorTypes) {
        var _this = this;
        return Object.keys(strategies).every(function (s) {
            var strategy = strategies[s];
            return !selectorTypes[s] || !strategy(ast);
        }) && (ast.children || [])
            .every(function (c) { return ast instanceof compiler_1.ElementAst && _this.shouldVisit(c, strategies, selectorTypes); });
    };
    return ElementFilterVisitor;
}(basicTemplateAstVisitor_1.BasicTemplateAstVisitor));
var Rule = (function (_super) {
    __extends(Rule, _super);
    function Rule() {
        return _super.apply(this, arguments) || this;
    }
    Rule.prototype.apply = function (sourceFile) {
        return this.applyWithWalker(new UnusedCssNg2Visitor(sourceFile, this.getOptions(), {
            cssVisitorCtrl: UnusedCssVisitor
        }));
    };
    return Rule;
}(Lint.Rules.AbstractRule));
Rule.FAILURE = 'The %s "%s" that you\'re trying to access does not exist in the class declaration.';
exports.Rule = Rule;
var UnusedCssVisitor = (function (_super) {
    __extends(UnusedCssVisitor, _super);
    function UnusedCssVisitor() {
        return _super.apply(this, arguments) || this;
    }
    UnusedCssVisitor.prototype.visitCssSelectorRule = function (ast) {
        var _this = this;
        try {
            var match = ast.selectors.some(function (s) { return _this.visitCssSelector(s); });
            if (!match) {
                this.addFailure(this.createFailure(ast.start.offset, ast.end.offset - ast.start.offset, 'Unused styles'));
            }
        }
        catch (e) {
            logger_1.logger.error(e);
        }
        return true;
    };
    UnusedCssVisitor.prototype.visitCssSelector = function (ast) {
        var parts = [];
        for (var i = 0; i < ast.selectorParts.length; i += 1) {
            var c = ast.selectorParts[i];
            c.strValue = c.strValue.split('::').shift();
            if (c.strValue.endsWith('/') ||
                c.strValue.endsWith('>')) {
                parts.push(c.strValue);
                break;
            }
            else if (!c.strValue.startsWith(':')) {
                parts.push(c.strValue);
            }
        }
        if (!parts.length || !this.templateAst) {
            return true;
        }
        var strippedSelector = parts.map(function (s) { return s.replace(/\/|>$/, '').trim(); }).join(' ');
        var elementFilterVisitor = new ElementFilterVisitor(this.getSourceFile(), this._originalOptions, this.context, 0);
        var tokenized = CssSelectorTokenizer.parse(strippedSelector);
        var selectorTypesCache = Object.keys(dynamicFilters).reduce(function (a, key) {
            a[key] = hasSelector(tokenized, key);
            return a;
        }, {});
        if (!elementFilterVisitor.shouldVisit(this.templateAst, dynamicFilters, selectorTypesCache)) {
            return true;
        }
        var matchFound = false;
        var selector = function (element) {
            if (lang(strippedSelector)(element)) {
                matchFound = true;
                return true;
            }
            return false;
        };
        var visitor = new ElementVisitor(this.getSourceFile(), this._originalOptions, this.context, 0);
        visitor.visit(this.templateAst, selector);
        return matchFound;
    };
    return UnusedCssVisitor;
}(basicCssAstVisitor_1.BasicCssAstVisitor));
var UnusedCssNg2Visitor = (function (_super) {
    __extends(UnusedCssNg2Visitor, _super);
    function UnusedCssNg2Visitor() {
        return _super.apply(this, arguments) || this;
    }
    UnusedCssNg2Visitor.prototype.visitClassDeclaration = function (declaration) {
        var _this = this;
        var d = utils_1.getComponentDecorator(declaration);
        if (d) {
            var meta_1 = this._metadataReader.read(declaration);
            this.visitNg2Component(meta_1);
            if (meta_1.template && meta_1.template.template) {
                try {
                    var ElementAstCtr_1 = compiler_1.ElementAst;
                    ngVersion_1.SemVerDSL
                        .gte('4.0.0-beta.8', function () {
                        _this.templateAst =
                            new ElementAstCtr_1('*', [], [], [], [], [], [], false, [], templateParser_1.parseTemplate(meta_1.template.template.code), 0, null, null);
                    })
                        .else(function () {
                        _this.templateAst =
                            new ElementAstCtr_1('*', [], [], [], [], [], [], false, templateParser_1.parseTemplate(meta_1.template.template.code), 0, null, null);
                    });
                }
                catch (e) {
                    logger_1.logger.error('Cannot parse the template', e);
                }
            }
        }
        _super.prototype.visitClassDeclaration.call(this, declaration);
    };
    UnusedCssNg2Visitor.prototype.visitNg2StyleHelper = function (style, context, styleMetadata, baseStart) {
        var _this = this;
        if (!style) {
            return;
        }
        else {
            var file = this.getContextSourceFile(styleMetadata.url, styleMetadata.style.source);
            var visitor = new UnusedCssVisitor(file, this._originalOptions, context, styleMetadata, baseStart);
            visitor.templateAst = this.templateAst;
            var d = utils_1.getComponentDecorator(context.controller);
            var encapsulation = utils_1.getDecoratorPropertyInitializer(d, 'encapsulation');
            if (isEncapsulationEnabled(encapsulation)) {
                style.visit(visitor);
                visitor.getFailures().forEach(function (f) { return _this.addFailure(f); });
            }
        }
    };
    return UnusedCssNg2Visitor;
}(ng2Walker_1.Ng2Walker));
exports.UnusedCssNg2Visitor = UnusedCssNg2Visitor;
