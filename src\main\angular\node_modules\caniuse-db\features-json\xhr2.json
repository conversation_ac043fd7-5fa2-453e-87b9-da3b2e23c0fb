{"title": "XMLHttpRequest advanced features", "description": "Adds more functionality to XHR (aka AJAX) requests like file uploads, transfer progress information and the ability to send form data. Previously known as [XMLHttpRequest Level 2](http://www.w3.org/TR/2012/WD-XMLHttpRequest-20120117/), these features now appear simply in the XMLHttpRequest spec.", "spec": "https://xhr.spec.whatwg.org/", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en/XMLHttpRequest/FormData", "title": "Mozilla Developer Network (MDN) documentation - FormData"}, {"url": "https://github.com/3nr1c/jUri.js", "title": "Polyfill for FormData object"}, {"url": "https://www.webplatform.org/docs/apis/xhr/XMLHttpRequest", "title": "WebPlatform Docs"}], "bugs": [{"description": "Firefox 3.5 and 3.6 partial support refers to only including support for the progress event."}, {"description": "WebKit versions 535 and older (r103502) do not implement the onloadend event"}, {"description": "IE10 and 11 do not support synchronous requests, with support being phased out in other browsers too. This is due to [detrimental effects](https://xhr.spec.whatwg.org/#sync-warning) resulting from making them."}, {"description": "The \"progress\" event is reported to not work in Chrome for iOS "}, {"description": "In Internet Explorer the `timeout` property may only be set after calling `open` and before calling `send`."}], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a #1", "11": "a #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "a #1 #2 #3", "3.6": "a #1 #2 #3", "4": "a #1 #2 #3", "5": "a #1 #2 #3", "6": "a #1 #2", "7": "a #1 #2", "8": "a #1 #2", "9": "a #1 #2", "10": "a #2", "11": "a #2", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "a #1 #2", "8": "a #1 #2", "9": "a #1 #2", "10": "a #1 #2", "11": "a #1 #2", "12": "a #1 #2", "13": "a #1 #2", "14": "a #1 #2", "15": "a #1 #2", "16": "a #1 #2", "17": "a #1 #2", "18": "a #1 #2", "19": "a #1 #2", "20": "a #1 #2", "21": "a #1 #2", "22": "a #1 #2", "23": "a #1 #2", "24": "a #1 #2", "25": "a #1 #2", "26": "a #1 #2", "27": "a #1 #2", "28": "a #1 #2", "29": "a #1", "30": "a #1", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "a #1 #2", "5.1": "a #1 #2", "6": "a #1 #2", "6.1": "a #1", "7": "a #1", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "y", "12.1": "y", "15": "a #1", "16": "a #1", "17": "a #1", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #1 #2", "6.0-6.1": "a #1 #2", "7.0-7.1": "a #1", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a #1 #2 #3", "4": "a #1 #2 #3", "4.1": "a #1 #2 #3", "4.2-4.3": "a #1 #2 #3", "4.4": "a #1 #2", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "a #1 #2", "10": "a #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to not supporting `json` as `responseType`", "2": "Partial support refers to not supporting `.timeout` and `.ontimeout`", "3": "Partial support refers to not supporting `blob` as `responseType`"}, "usage_perc_y": 88.5, "usage_perc_a": 5.85, "ucprefix": false, "parent": "", "keywords": "formdata", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}