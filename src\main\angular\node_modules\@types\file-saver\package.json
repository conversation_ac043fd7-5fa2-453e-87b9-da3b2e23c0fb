{"_args": [["@types/file-saver@0.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@types/file-saver@0.0.0", "_id": "@types/file-saver@0.0.0", "_inBundle": false, "_integrity": "sha1-HZVFAmGYYtQ1RETUM4X7io+kR4o=", "_location": "/@types/file-saver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/file-saver@0.0.0", "name": "@types/file-saver", "escapedName": "@types%2ffile-saver", "scope": "@types", "rawSpec": "0.0.0", "saveSpec": null, "fetchSpec": "0.0.0"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/file-saver/-/file-saver-0.0.0.tgz", "_spec": "0.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "dependencies": {}, "description": "TypeScript definitions for FileSaver.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/file-saver", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typesPublisherContentHash": "cd4f41b0e88ec7b3ec180a92073a606bef75e54e5bdad528218bd630f419465c", "typings": "index.d.ts", "version": "0.0.0"}