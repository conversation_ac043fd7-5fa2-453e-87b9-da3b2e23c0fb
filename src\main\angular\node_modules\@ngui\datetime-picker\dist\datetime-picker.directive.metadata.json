{"__symbolic": "module", "version": 1, "metadata": {"NguiDatetimePickerDirective": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngui-datetime-picker]", "providers": [{"__symbolic": "reference", "module": "./datetime", "name": "NguiDatetime"}]}]}], "members": {"dateFormat": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["date-format"]}]}], "parseFormat": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["parse-format"]}]}], "dateOnly": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["date-only"]}]}], "timeOnly": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["time-only"]}]}], "closeOnSelect": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["close-on-select"]}]}], "defaultValue": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["default-value"]}]}], "minuteStep": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["minute-step"]}]}], "minDate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["min-date"]}]}], "maxDate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["max-date"]}]}], "minHour": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["min-hour"]}]}], "maxHour": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["max-hour"]}]}], "disabledDates": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["disabled-dates"]}]}], "showCloseLayer": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["show-close-layer"]}]}], "showTodayShortcut": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["show-today-shortcut"]}]}], "showWeekNumbers": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["show-week-numbers"]}]}], "formControlName": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "isDraggable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["is-draggable"]}]}], "ngModel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["ngModel"]}]}], "ngModelChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}, "arguments": ["ngModelChange"]}]}], "valueChanged$": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}, "arguments": ["valueChanged"]}]}], "popupClosed$": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output"}, "arguments": ["popupClosed"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Host"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf"}}]], "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "module": "@angular/forms", "name": "ControlContainer"}]}], "normalizeInput": [{"__symbolic": "method"}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterViewInit": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "updateDatepicker": [{"__symbolic": "method"}], "setInputElDateValue": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "elementIn": [{"__symbolic": "method"}], "styleDatetimePicker": [{"__symbolic": "method"}], "drag_over": [{"__symbolic": "method"}]}}}}