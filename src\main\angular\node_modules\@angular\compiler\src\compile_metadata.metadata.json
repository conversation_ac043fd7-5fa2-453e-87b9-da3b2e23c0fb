[{"__symbolic": "module", "version": 3, "metadata": {"CompileAnimationEntryMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}]}]}]}}, "CompileAnimationStateMetadata": {"__symbolic": "class"}, "CompileAnimationStateDeclarationMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}}, "CompileAnimationStateTransitionMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 39, "character": 50}, {"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}}, "CompileAnimationMetadata": {"__symbolic": "class"}, "CompileAnimationKeyframesSequenceMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}]}}, "CompileAnimationStyleMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 34}]}]}]}}, "CompileAnimationAnimateMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}}, "CompileAnimationWithStepsMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "CompileAnimationSequenceMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationWithStepsMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "CompileAnimationGroupMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationWithStepsMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "identifierName": {"__symbolic": "function"}, "identifierModuleUrl": {"__symbolic": "function"}, "viewClassName": {"__symbolic": "function", "parameters": ["compType", "embeddedTemplateIndex"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": "View_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}, "right": "_"}, "right": {"__symbolic": "reference", "name": "embeddedTemplateIndex"}}}, "rendererTypeName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": "RenderType_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}}, "hostViewClassName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": "HostView_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}}, "dirWrapperClassName": {"__symbolic": "function", "parameters": ["dirType"], "value": {"__symbolic": "binop", "operator": "+", "left": "Wrapper_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "dirType"}}]}}}, "componentFactoryName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}, "right": "NgFactory"}}, "ProxyClass": {"__symbolic": "interface"}, "CompileIdentifierMetadata": {"__symbolic": "interface"}, "CompileSummaryKind": {"Pipe": 0, "Directive": 1, "NgModule": 2, "Injectable": 3}, "CompileTypeSummary": {"__symbolic": "interface"}, "CompileDiDependencyMetadata": {"__symbolic": "interface"}, "CompileProviderMetadata": {"__symbolic": "interface"}, "CompileFactoryMetadata": {"__symbolic": "interface"}, "tokenName": {"__symbolic": "function", "parameters": ["token"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "token"}, "member": "value"}, "right": null}, "thenExpression": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 80, "character": 9, "context": {"name": "_sanitizeIdentifier"}}, "elseExpression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "token"}, "member": "identifier"}]}}}, "tokenReference": {"__symbolic": "function"}, "CompileTokenMetadata": {"__symbolic": "interface"}, "CompileTypeMetadata": {"__symbolic": "interface"}, "CompileQueryMetadata": {"__symbolic": "interface"}, "CompileStylesheetMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 228, "character": 19}]}]}}, "CompileTemplateSummary": {"__symbolic": "interface"}, "CompileTemplateMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 259, "character": 73}]}], "toSummary": [{"__symbolic": "method"}]}}, "CompileEntryComponentMetadata": {"__symbolic": "interface"}, "CompileDirectiveSummary": {"__symbolic": "interface"}, "CompileDirectiveMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 431, "character": 96}]}], "toSummary": [{"__symbolic": "method"}]}}, "createHostComponentMeta": {"__symbolic": "function"}, "CompilePipeSummary": {"__symbolic": "interface"}, "CompilePipeMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 553, "character": 34}]}], "toSummary": [{"__symbolic": "method"}]}}, "CompileNgModuleSummary": {"__symbolic": "interface"}, "CompileNgModuleMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 614, "character": 64}]}], "toSummary": [{"__symbolic": "method"}]}}, "TransitiveCompileNgModuleMetadata": {"__symbolic": "class", "members": {"addProvider": [{"__symbolic": "method"}], "addDirective": [{"__symbolic": "method"}], "addExportedDirective": [{"__symbolic": "method"}], "addPipe": [{"__symbolic": "method"}], "addExportedPipe": [{"__symbolic": "method"}], "addModule": [{"__symbolic": "method"}], "addEntryComponent": [{"__symbolic": "method"}]}}, "ProviderMeta": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 729, "character": 86}]}]}}, "flatten": {"__symbolic": "function", "parameters": ["list"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 748, "character": 21}}, "sourceUrl": {"__symbolic": "function", "parameters": ["url"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 757, "character": 21}}, "templateSourceUrl": {"__symbolic": "function"}, "sharedStylesheetJitUrl": {"__symbolic": "function"}, "ngModuleJitUrl": {"__symbolic": "function", "parameters": ["moduleMeta"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sourceUrl"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "moduleMeta"}, "member": "type"}]}, "right": "/module.ngfactory.js"}]}}, "templateJitUrl": {"__symbolic": "function", "parameters": ["ngModuleType", "compMeta"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sourceUrl"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "reference", "name": "ngModuleType"}]}, "right": "/"}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "compMeta"}, "member": "type"}]}}, "right": ".ngfactory.js"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"CompileAnimationEntryMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}]}]}]}}, "CompileAnimationStateMetadata": {"__symbolic": "class"}, "CompileAnimationStateDeclarationMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}}, "CompileAnimationStateTransitionMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationStateMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 39, "character": 50}, {"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}}, "CompileAnimationMetadata": {"__symbolic": "class"}, "CompileAnimationKeyframesSequenceMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}]}}, "CompileAnimationStyleMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Expression form not supported", "line": 54, "character": 34}]}]}]}}, "CompileAnimationAnimateMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "CompileAnimationStyleMetadata"}]}]}}, "CompileAnimationWithStepsMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "CompileAnimationSequenceMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationWithStepsMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "CompileAnimationGroupMetadata": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "CompileAnimationWithStepsMetadata"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "CompileAnimationMetadata"}]}]}]}}, "identifierName": {"__symbolic": "function"}, "identifierModuleUrl": {"__symbolic": "function"}, "viewClassName": {"__symbolic": "function", "parameters": ["compType", "embeddedTemplateIndex"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": "View_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}, "right": "_"}, "right": {"__symbolic": "reference", "name": "embeddedTemplateIndex"}}}, "rendererTypeName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": "RenderType_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}}, "hostViewClassName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": "HostView_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}}}, "dirWrapperClassName": {"__symbolic": "function", "parameters": ["dirType"], "value": {"__symbolic": "binop", "operator": "+", "left": "Wrapper_", "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "dirType"}}]}}}, "componentFactoryName": {"__symbolic": "function", "parameters": ["compType"], "value": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"reference": {"__symbolic": "reference", "name": "compType"}}]}, "right": "NgFactory"}}, "ProxyClass": {"__symbolic": "interface"}, "CompileIdentifierMetadata": {"__symbolic": "interface"}, "CompileSummaryKind": {"Pipe": 0, "Directive": 1, "NgModule": 2, "Injectable": 3}, "CompileTypeSummary": {"__symbolic": "interface"}, "CompileDiDependencyMetadata": {"__symbolic": "interface"}, "CompileProviderMetadata": {"__symbolic": "interface"}, "CompileFactoryMetadata": {"__symbolic": "interface"}, "tokenName": {"__symbolic": "function", "parameters": ["token"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "token"}, "member": "value"}, "right": null}, "thenExpression": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 80, "character": 9, "context": {"name": "_sanitizeIdentifier"}}, "elseExpression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "token"}, "member": "identifier"}]}}}, "tokenReference": {"__symbolic": "function"}, "CompileTokenMetadata": {"__symbolic": "interface"}, "CompileTypeMetadata": {"__symbolic": "interface"}, "CompileQueryMetadata": {"__symbolic": "interface"}, "CompileStylesheetMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 228, "character": 19}]}]}}, "CompileTemplateSummary": {"__symbolic": "interface"}, "CompileTemplateMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 259, "character": 73}]}], "toSummary": [{"__symbolic": "method"}]}}, "CompileEntryComponentMetadata": {"__symbolic": "interface"}, "CompileDirectiveSummary": {"__symbolic": "interface"}, "CompileDirectiveMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 431, "character": 96}]}], "toSummary": [{"__symbolic": "method"}]}}, "createHostComponentMeta": {"__symbolic": "function"}, "CompilePipeSummary": {"__symbolic": "interface"}, "CompilePipeMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 553, "character": 34}]}], "toSummary": [{"__symbolic": "method"}]}}, "CompileNgModuleSummary": {"__symbolic": "interface"}, "CompileNgModuleMetadata": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 614, "character": 64}]}], "toSummary": [{"__symbolic": "method"}]}}, "TransitiveCompileNgModuleMetadata": {"__symbolic": "class", "members": {"addProvider": [{"__symbolic": "method"}], "addDirective": [{"__symbolic": "method"}], "addExportedDirective": [{"__symbolic": "method"}], "addPipe": [{"__symbolic": "method"}], "addExportedPipe": [{"__symbolic": "method"}], "addModule": [{"__symbolic": "method"}], "addEntryComponent": [{"__symbolic": "method"}]}}, "ProviderMeta": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 729, "character": 86}]}]}}, "flatten": {"__symbolic": "function", "parameters": ["list"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 748, "character": 21}}, "sourceUrl": {"__symbolic": "function", "parameters": ["url"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 757, "character": 21}}, "templateSourceUrl": {"__symbolic": "function"}, "sharedStylesheetJitUrl": {"__symbolic": "function"}, "ngModuleJitUrl": {"__symbolic": "function", "parameters": ["moduleMeta"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sourceUrl"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "moduleMeta"}, "member": "type"}]}, "right": "/module.ngfactory.js"}]}}, "templateJitUrl": {"__symbolic": "function", "parameters": ["ngModuleType", "compMeta"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "sourceUrl"}, "arguments": [{"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "binop", "operator": "+", "left": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "reference", "name": "ngModuleType"}]}, "right": "/"}, "right": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "identifierName"}, "arguments": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "compMeta"}, "member": "type"}]}}, "right": ".ngfactory.js"}]}}}}]