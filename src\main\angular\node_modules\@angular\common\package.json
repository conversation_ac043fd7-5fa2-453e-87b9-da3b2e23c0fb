{"_args": [["@angular/common@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/common@4.2.5", "_id": "@angular/common@4.2.5", "_inBundle": false, "_integrity": "sha1-TVCpW0RM1Yz3BvDandFAfTuDNi4=", "_location": "/@angular/common", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/common@4.2.5", "name": "@angular/common", "escapedName": "@angular%2fcommon", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/", "/angular-svg-round-progressbar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/common/-/common-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - commonly needed directives and services", "es2015": "./@angular/common.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/common.umd.js", "module": "./@angular/common.es5.js", "name": "@angular/common", "peerDependencies": {"@angular/core": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./common.d.ts", "version": "4.2.5"}