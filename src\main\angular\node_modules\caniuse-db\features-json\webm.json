{"title": "WebM video format", "description": "Multimedia format designed to provide a royalty-free, high-quality open video compression format for use with HTML5 video. WebM supports the video codec VP8 and VP9.", "spec": "http://www.webmproject.org/", "status": "other", "links": [{"url": "https://tools.google.com/dlpage/webmmf", "title": "Codec for IE9 support"}, {"url": "http://www.broken-links.com/2010/09/01/playing-webm-in-safari-with-plugins/", "title": "Info on supporting WebM in Safari"}, {"url": "http://webmproject.org", "title": "Official website"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/video.js#video-webm", "title": "has.js test"}, {"url": "http://perian.org/", "title": "Perian :Mac OSX Webm Codec install"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "p", "10": "p", "11": "p"}, "edge": {"12": "p", "13": "p", "14": "a", "15": "a", "16": "a"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "p", "4": "p", "5": "p", "5.1": "p", "6": "p", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "p", "11": "p", "TP": "p"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "a", "15": "a", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "a", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "p", "11": "p"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "a", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "<PERSON> work in IE9+ and Safari/MacOSX provided the user has the WebM codecs installed. Partial support indicates that at least one codec is supported but not all. MS Edge supports VP9 from MSE sources, also progressive sources are not supported by Edge.", "notes_by_num": {}, "usage_perc_y": 70.6, "usage_perc_a": 7.35, "ucprefix": false, "parent": "video", "keywords": "<PERSON><PERSON><PERSON>", "ie_id": "webmcontainer,vp9videoplayback", "chrome_id": "6362186595172352", "firefox_id": "", "webkit_id": "", "shown": true}