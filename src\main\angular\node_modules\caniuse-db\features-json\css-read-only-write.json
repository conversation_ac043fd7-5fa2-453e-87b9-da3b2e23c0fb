{"title": "CSS :read-only and :read-write selectors", "description": ":read-only and :read-write pseudo-classes to match elements which are considered user-alterable", "spec": "https://html.spec.whatwg.org/multipage/scripting.html#selector-read-only", "status": "ls", "links": [{"url": "https://css-tricks.com/almanac/selectors/r/read-write-read/", "title": "CSS Tricks article"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/%3Aread-only", "title": "MDN :read-only"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:read-write", "title": "Mozilla Developer Network (MDN) documentation - CSS :read-write"}, {"url": "https://drafts.csswg.org/selectors-4/#rw-pseudos", "title": "Selectors Level 4 § The Mutability Pseudo-classes: :read-only and :read-write"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=312971", "title": "Firefox feature request bug"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "u", "16": "u", "17": "u", "18": "u", "19": "u", "20": "u", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "u", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y x", "52": "y x", "53": "y x", "54": "y x", "55": "y x", "56": "y x", "57": "y x"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "u", "16": "u", "17": "u", "18": "u", "19": "u", "20": "u", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "u", "27": "u", "28": "u", "29": "u", "30": "u", "31": "u", "32": "u", "33": "u", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #1", "62": "a #1"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "u", "6": "u", "6.1": "u", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #1", "TP": "a #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "u", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11": "a #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "a #1"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a #1"}, "and_chr": {"59": "a #1"}, "and_ff": {"54": "y x"}, "ie_mob": {"10": "u", "11": "u"}, "and_uc": {"11.4": "u"}, "samsung": {"4": "n", "5": "a #1"}, "and_qq": {"1.2": "a #1"}, "baidu": {"7.12": "a #1"}}, "notes": "", "notes_by_num": {"1": "Supports selector only for input and textarea fields, but not for contenteditable"}, "usage_perc_y": 6.91, "usage_perc_a": 67.81, "ucprefix": false, "parent": "", "keywords": "css,selector,read-only,read-write", "ie_id": "cssmutabilitypseudoclasses", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": false}