{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/platform-browser/testing/index.ts", "../../../../../packages/platform-browser/testing/src/testing.ts", "../../../../../packages/platform-browser/testing/src/browser.ts", "../../../../../packages/platform-browser/testing/src/browser_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\nexport * from './browser';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {APP_ID, NgModule, NgZone, PLATFORM_INITIALIZER, PlatformRef, Provider, createPlatformFactory, platformCore} from '@angular/core';\nimport {BrowserModule, ɵBrowserDomAdapter as BrowserDomAdapter, ɵELEMENT_PROBE_PROVIDERS as ELEMENT_PROBE_PROVIDERS} from '@angular/platform-browser';\nimport {BrowserDetection, createNgZone} from './browser_util';\n\nfunction initBrowserTests() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserDetection.setup();\n}\n\nconst _TEST_BROWSER_PLATFORM_PROVIDERS: Provider[] =\n    [{provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true}];\n\n/**\n * Platform for testing\n *\n * @stable\n */\nexport const platformBrowserTesting =\n    createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @stable\n */\n\nexport class BrowserTestingModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    ELEMENT_PROBE_PROVIDERS,\n    {provide: NgZone, useFactory: createNgZone},\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone, ɵglobal as global} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\n\nexport let browserDetection: BrowserDetection;\n\nexport class BrowserDetection {\n  private _overrideUa: string|null;\n  private get _ua(): string {\n    if (typeof this._overrideUa === 'string') {\n      return this._overrideUa;\n    }\n\n    return getDOM() ? getDOM().getUserAgent() : '';\n  }\n\n  static setup() { browserDetection = new BrowserDetection(null); }\n\n  constructor(ua: string|null) { this._overrideUa = ua; }\n\n  get isFirefox(): boolean { return this._ua.indexOf('Firefox') > -1; }\n\n  get isAndroid(): boolean {\n    return this._ua.indexOf('Mozilla/5.0') > -1 && this._ua.indexOf('Android') > -1 &&\n        this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Chrome') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isEdge(): boolean { return this._ua.indexOf('Edge') > -1; }\n\n  get isIE(): boolean { return this._ua.indexOf('Trident') > -1; }\n\n  get isWebkit(): boolean {\n    return this._ua.indexOf('AppleWebKit') > -1 && this._ua.indexOf('Edge') == -1 &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isIOS7(): boolean {\n    return (this._ua.indexOf('iPhone OS 7') > -1 || this._ua.indexOf('iPad OS 7') > -1) &&\n        this._ua.indexOf('IEMobile') == -1;\n  }\n\n  get isSlow(): boolean { return this.isAndroid || this.isIE || this.isIOS7; }\n\n  // The Intl API is only natively supported in Chrome, Firefox, IE11 and Edge.\n  // This detector is needed in tests to make the difference between:\n  // 1) IE11/Edge: they have a native Intl API, but with some discrepancies\n  // 2) IE9/IE10: they use the polyfill, and so no discrepancies\n  get supportsNativeIntlApi(): boolean {\n    return !!(<any>global).Intl && (<any>global).Intl !== (<any>global).IntlPolyfill;\n  }\n\n  get isChromeDesktop(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Mobile Safari') == -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n\n  // \"Old Chrome\" means Chrome 3X, where there are some discrepancies in the Intl API.\n  // Android 4.4 and 5.X have such browsers by default (respectively 30 and 39).\n  get isOldChrome(): boolean {\n    return this._ua.indexOf('Chrome') > -1 && this._ua.indexOf('Chrome/3') > -1 &&\n        this._ua.indexOf('Edge') == -1;\n  }\n}\n\nBrowserDetection.setup();\n\nexport function dispatchEvent(element: any, eventType: any): void {\n  getDOM().dispatchEvent(element, getDOM().createEvent(eventType));\n}\n\nexport function el(html: string): HTMLElement {\n  return <HTMLElement>getDOM().firstChild(getDOM().content(getDOM().createTemplate(html)));\n}\n\nexport function normalizeCSS(css: string): string {\n  return css.replace(/\\s+/g, ' ')\n      .replace(/:\\s/g, ':')\n      .replace(/'/g, '\"')\n      .replace(/ }/g, '}')\n      .replace(/url\\((\\\"|\\s)(.+)(\\\"|\\s)\\)(\\s*)/g, (...match: string[]) => `url(\"${match[2]}\")`)\n      .replace(/\\[(.+)=([^\"\\]]+)\\]/g, (...match: string[]) => `[${match[1]}=\"${match[2]}\"]`);\n}\n\nconst _singleTagWhitelist = ['br', 'hr', 'input'];\nexport function stringifyElement(el: any /** TODO #9100 */): string {\n  let result = '';\n  if (getDOM().isElementNode(el)) {\n    const tagName = getDOM().tagName(el).toLowerCase();\n\n    // Opening tag\n    result += `<${tagName}`;\n\n    // Attributes in an ordered way\n    const attributeMap = getDOM().attributeMap(el);\n    const keys: string[] = Array.from(attributeMap.keys()).sort();\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const attValue = attributeMap.get(key);\n      if (typeof attValue !== 'string') {\n        result += ` ${key}`;\n      } else {\n        result += ` ${key}=\"${attValue}\"`;\n      }\n    }\n    result += '>';\n\n    // Children\n    const childrenRoot = getDOM().templateAwareRoot(el);\n    const children = childrenRoot ? getDOM().childNodes(childrenRoot) : [];\n    for (let j = 0; j < children.length; j++) {\n      result += stringifyElement(children[j]);\n    }\n\n    // Closing tag\n    if (_singleTagWhitelist.indexOf(tagName) == -1) {\n      result += `</${tagName}>`;\n    }\n  } else if (getDOM().isCommentNode(el)) {\n    result += `<!--${getDOM().nodeValue(el)}-->`;\n  } else {\n    result += getDOM().getText(el);\n  }\n\n  return result;\n}\n\nexport function createNgZone(): NgZone {\n  return new NgZone({enableLongStackTrace: true});\n}\n"], "names": ["ELEMENT_PROBE_PROVIDERS"], "mappings": ";;;;;;;;;;;;;GGiBA;AACA,IAAA,gBAAA,CAAA;AAEA;IAYA,0BAAY,EAAZ;QAAA,IAAA,CAAA,WAAA,GAAA,EAAA,CAAwC;IAAC,CAAC;IAX1C,sBAAA,iCAAA;aAAA;YAEA,EAAA,CAAS,CAAT,OAAA,IAAA,CAAA,WAAA,KAAsC,QAAtC,CAAA,CAAA,CAAA;gBAEA,MAA6B,CAA7B,IAAA,CAAA,WAAA,CAAA;YAEM,CAAN;YAEM,MAAN,CAAA,OAAA,EAAA,GAAA,OAAA,EAAA,CAAA,YAAA,EAAA,GAAA,EAAA,CAAA;QACA,CAAA;;;OAAA;IACA,sBAAY,GAAZ,cAAiB,gBAAjB,GAAA,IAAA,gBAAwD,CAAC,IAAzD,CAAA,CAAA,CAAgE,CAAC;IAEjE,sBAAA,uCAAA;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;;OAAA;IAEE,sBAAI,uCAAN;aAAE;YAEI,MAAN,CAAA,IAAA,CAAA,GAAA,CAA+B,OAA/B,CAAuC,aAAvC,CAAA,GAAA,CAAyD,CAAC,IAAI,IAAI,CAAlE,GAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA;gBAEA,IAAc,CAAd,GAAA,CAAA,OAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;gBACA,IAAA,CAAA,GAAgB,CAAhB,OAAA,CAAA,UAAA,CAAA,IAAA,CAAyC,CAAC,CAA1C;QACA,CAAA;;;OAAA;IACA,sBAAA,oCAAA;aAAA,cAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;;OAAA;IAEE,sBAAI,kCAAN;aAAE,cAAF,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;;OAAA;IACA,sBAAI,sCAAQ;aAAZ;YACA,MAAA,CAAA,IAAA,CAAgB,GAAhB,CAAA,OAAA,CAAA,aAAyC,CAAC,GAA1C,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;gBACA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QAEE,CAAF;;;OAAA;;;;;;;;;IAME,sBAAI,oCAAN;aAAE,cAAF,MAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;;OAAA;IAMA,sBAAA,mDAAyB;QALzB,6EAAA;QACA,mEAAA;QAEE,yEAAF;QACA,8DAA8D;aAC9D;YACA,MAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,IAAA,OAAA,CAAA,IAAA,KAAA,OAAA,CAAA,YAAA,CAAA;;;;;;;YAIM,MAAN,CAAA,IAAiB,CAAjB,GAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,eAAA,CAAA,IAAA,CAAA,CAAA;gBACA,IAAA,CAAA,GAAgB,CAAhB,OAAA,CAAA,MAAA,CAAA,IAAoC,CAAC,CAArC,CAAA;QACA,CAAA;;;OAAA;IAIA,sBAAA,yCAAA;QAHA,oFAAA;QACA,8EAAA;aAEA;YAEA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA;gBAIA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA;QAIA,CAAA;;;OAAA;IASA,uBAAA;AAAA,CAAA,AAvEA,IAuEA;AA2CA,gBAAA,CAAA,KAAA,EAAA,CAAA;;;;;;;;;;GDtHA;;;;;;AAQA;;;;;;AASA;;;;GAGA;AACA;IAAA;IACA,CAAA;IAAA,2BAAA;AAAA,CAAA,AADA,IACA;AACAA,oBAAIA,CAAJA,UAAAA,GAAAA;IACA,EAAA,IAAA,EAAA,QAAI,EAAC,IAAL,EAAA,CAAY;gBACZ,OAAA,EAAA,CAAA,aAAA,CAAA;gBACA,SAAA,EAAA;oBACA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,GAAA,EAAA;;oBAEA,EAAA,OAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA;;aD7CA,EAAA,EAAA;;;;;;;;;;GAYG;;;;;;;;;;;;GDAA;;;;;;;"}