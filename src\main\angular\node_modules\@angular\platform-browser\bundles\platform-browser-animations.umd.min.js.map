{"version": 3, "file": "platform-browser-animations.umd.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../../../packages/platform-browser/animations/src/animation_renderer.ts", "../../../../packages/platform-browser/animations/src/providers.ts", "../../../../packages/platform-browser/animations/src/animation_builder.ts", "../../../../packages/platform-browser/animations/src/module.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationTriggerMetadata} from '@angular/animations';\nimport {ɵAnimationEngine as AnimationEngine} from '@angular/animations/browser';\nimport {Injectable, NgZone, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2} from '@angular/core';\nexport class AnimationRendererFactory implements RendererFactory2 {\nprivate _currentId: number = 0;\nprivate _microtaskId: number = 1;\nprivate _animationCallbacksBuffer: [(e: any) => any, any][] = [];\nprivate _rendererCache = new Map<Renderer2, BaseAnimationRenderer>();\n/**\n * @param {?} delegate\n * @param {?} engine\n * @param {?} _zone\n */\nconstructor(\nprivate delegate: RendererFactory2,\nprivate engine: AnimationEngine,\nprivate _zone: NgZone) {\n    engine.onRemovalComplete = (element: any, delegate: Renderer2) => {\n      // Note: if an component element has a leave animation, and the component\n      // a host leave animation, the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      if (delegate && delegate.parentNode(element)) {\n        delegate.removeChild(element.parentNode, element);\n      }\n    };\n  }\n/**\n * @param {?} hostElement\n * @param {?} type\n * @return {?}\n */\ncreateRenderer(hostElement: any, type: RendererType2): Renderer2 {\n    const /** @type {?} */ EMPTY_NAMESPACE_ID = '';\n\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const /** @type {?} */ delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let /** @type {?} */ renderer: BaseAnimationRenderer|undefined = this._rendererCache.get(delegate);\n      if (!renderer) {\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine);\n        // only cache this result when the base renderer is used\n        this._rendererCache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n\n    const /** @type {?} */ componentId = type.id;\n    const /** @type {?} */ namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n\n    this.engine.register(namespaceId, hostElement);\n    const /** @type {?} */ animationTriggers = /** @type {?} */(( type.data['animation'] as AnimationTriggerMetadata[]));\n    animationTriggers.forEach(\n        trigger => this.engine.registerTrigger(\n            componentId, namespaceId, hostElement, trigger.name, trigger));\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n/**\n * @return {?}\n */\nbegin() {\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n/**\n * @return {?}\n */\nprivate _scheduleCountTask() {\n    Zone.current.scheduleMicroTask('incremenet the animation microtask', () => this._microtaskId++);\n  }\n/**\n * @param {?} count\n * @param {?} fn\n * @param {?} data\n * @return {?}\n */\nscheduleListenerCallback(count: number, fn: (e: any) => any, data: any) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n/**\n * @return {?}\n */\nend() {\n    this._zone.runOutsideAngular(() => {\n      this._scheduleCountTask();\n      this.engine.flush(this._microtaskId);\n    });\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n/**\n * @return {?}\n */\nwhenRenderingDone(): Promise<any> { return this.engine.whenRenderingDone(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n{type: AnimationEngine, },\n{type: NgZone, },\n];\n}\n\nfunction AnimationRendererFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRendererFactory.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAnimationRendererFactory.ctorParameters;\n/** @type {?} */\nAnimationRendererFactory.prototype._currentId;\n/** @type {?} */\nAnimationRendererFactory.prototype._microtaskId;\n/** @type {?} */\nAnimationRendererFactory.prototype._animationCallbacksBuffer;\n/** @type {?} */\nAnimationRendererFactory.prototype._rendererCache;\n/** @type {?} */\nAnimationRendererFactory.prototype.delegate;\n/** @type {?} */\nAnimationRendererFactory.prototype.engine;\n/** @type {?} */\nAnimationRendererFactory.prototype._zone;\n}\n\nexport class BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\n      protected namespaceId: string,\npublic delegate: Renderer2,\npublic engine: AnimationEngine) {\n    this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode !(n) : null;\n  }\n/**\n * @return {?}\n */\nget data() { return this.delegate.data; }\n\n  destroyNode: ((n: any) => void)|null;\n/**\n * @return {?}\n */\ndestroy(): void {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n  }\n/**\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\ncreateElement(name: string, namespace?: string|null|undefined) {\n    return this.delegate.createElement(name, namespace);\n  }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateComment(value: string) { return this.delegate.createComment(value); }\n/**\n * @param {?} value\n * @return {?}\n */\ncreateText(value: string) { return this.delegate.createText(value); }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @return {?}\n */\nappendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n/**\n * @param {?} parent\n * @param {?} newChild\n * @param {?} refChild\n * @return {?}\n */\ninsertBefore(parent: any, newChild: any, refChild: any): void {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, true);\n  }\n/**\n * @param {?} parent\n * @param {?} oldChild\n * @return {?}\n */\nremoveChild(parent: any, oldChild: any): void {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n/**\n * @param {?} selectorOrNode\n * @return {?}\n */\nselectRootElement(selectorOrNode: any) { return this.delegate.selectRootElement(selectorOrNode); }\n/**\n * @param {?} node\n * @return {?}\n */\nparentNode(node: any) { return this.delegate.parentNode(node); }\n/**\n * @param {?} node\n * @return {?}\n */\nnextSibling(node: any) { return this.delegate.nextSibling(node); }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @param {?=} namespace\n * @return {?}\n */\nsetAttribute(el: any, name: string, value: string, namespace?: string|null|undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?=} namespace\n * @return {?}\n */\nremoveAttribute(el: any, name: string, namespace?: string|null|undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\naddClass(el: any, name: string): void { this.delegate.addClass(el, name); }\n/**\n * @param {?} el\n * @param {?} name\n * @return {?}\n */\nremoveClass(el: any, name: string): void { this.delegate.removeClass(el, name); }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?} value\n * @param {?=} flags\n * @return {?}\n */\nsetStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n/**\n * @param {?} el\n * @param {?} style\n * @param {?=} flags\n * @return {?}\n */\nremoveStyle(el: any, style: string, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    this.delegate.setProperty(el, name, value);\n  }\n/**\n * @param {?} node\n * @param {?} value\n * @return {?}\n */\nsetValue(node: any, value: string): void { this.delegate.setValue(node, value); }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction BaseAnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseAnimationRenderer.prototype.destroyNode;\n/** @type {?} */\nBaseAnimationRenderer.prototype.namespaceId;\n/** @type {?} */\nBaseAnimationRenderer.prototype.delegate;\n/** @type {?} */\nBaseAnimationRenderer.prototype.engine;\n}\n\nexport class AnimationRenderer extends BaseAnimationRenderer implements Renderer2 {\n/**\n * @param {?} factory\n * @param {?} namespaceId\n * @param {?} delegate\n * @param {?} engine\n */\nconstructor(\npublic factory: AnimationRendererFactory, namespaceId: string, delegate: Renderer2,\n      engine: AnimationEngine) {\n    super(namespaceId, delegate, engine);\n    this.namespaceId = namespaceId;\n  }\n/**\n * @param {?} el\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nsetProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == '@') {\n      name = name.substr(1);\n      this.engine.setProperty(this.namespaceId, el, name, value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n/**\n * @param {?} target\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nlisten(target: 'window'|'document'|'body'|any, eventName: string, callback: (event: any) => any):\n      () => void {\n    if (eventName.charAt(0) == '@') {\n      const /** @type {?} */ element = resolveElementFromTarget(target);\n      let /** @type {?} */ name = eventName.substr(1);\n      let /** @type {?} */ phase = '';\n      if (name.charAt(0) != '@') {  // transition-specific\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const /** @type {?} */ countId = ( /** @type {?} */((event as any)))['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\n\nfunction AnimationRenderer_tsickle_Closure_declarations() {\n/** @type {?} */\nAnimationRenderer.prototype.factory;\n}\n\n/**\n * @param {?} target\n * @return {?}\n */\nfunction resolveElementFromTarget(target: 'window' | 'document' | 'body' | any): any {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\n/**\n * @param {?} triggerName\n * @return {?}\n */\nfunction parseTriggerCallbackName(triggerName: string) {\n  const /** @type {?} */ dotIndex = triggerName.indexOf('.');\n  const /** @type {?} */ trigger = triggerName.substring(0, dotIndex);\n  const /** @type {?} */ phase = triggerName.substr(dotIndex + 1);\n  return [trigger, phase];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {AnimationBuilder} from '@angular/animations';\nimport {AnimationDriver, ɵAnimationEngine as AnimationEngine, ɵAnimationStyleNormalizer as AnimationStyleNormalizer, ɵNoopAnimationDriver as NoopAnimationDriver, ɵWebAnimationsDriver as WebAnimationsDriver, ɵWebAnimationsStyleNormalizer as WebAnimationsStyleNormalizer, ɵsupportsWebAnimations as supportsWebAnimations} from '@angular/animations/browser';\nimport {Injectable, NgZone, Provider, RendererFactory2} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '@angular/platform-browser';\n\nimport {BrowserAnimationBuilder} from './animation_builder';\nimport {AnimationRendererFactory} from './animation_renderer';\nexport class InjectableAnimationEngine extends AnimationEngine {\n/**\n * @param {?} driver\n * @param {?} normalizer\n */\nconstructor(driver: AnimationDriver, normalizer: AnimationStyleNormalizer) {\n    super(driver, normalizer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: AnimationDriver, },\n{type: AnimationStyleNormalizer, },\n];\n}\n\nfunction InjectableAnimationEngine_tsickle_Closure_declarations() {\n/** @type {?} */\nInjectableAnimationEngine.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nInjectableAnimationEngine.ctorParameters;\n}\n\n/**\n * @return {?}\n */\nexport function instantiateSupportedAnimationDriver() {\n  if (supportsWebAnimations()) {\n    return new WebAnimationsDriver();\n  }\n  return new NoopAnimationDriver();\n}\n/**\n * @return {?}\n */\nexport function instantiateDefaultStyleNormalizer() {\n  return new WebAnimationsStyleNormalizer();\n}\n/**\n * @param {?} renderer\n * @param {?} engine\n * @param {?} zone\n * @return {?}\n */\nexport function instantiateRendererFactory(\n    renderer: DomRendererFactory2, engine: AnimationEngine, zone: NgZone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst /** @type {?} */ SHARED_ANIMATION_PROVIDERS: Provider[] = [\n  {provide: AnimationBuilder, useClass: BrowserAnimationBuilder},\n  {provide: AnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer},\n  {provide: AnimationEngine, useClass: InjectableAnimationEngine}, {\n    provide: RendererFactory2,\n    useFactory: instantiateRendererFactory,\n    deps: [DomRendererFactory2, AnimationEngine, NgZone]\n  }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nexport const BROWSER_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useFactory: instantiateSupportedAnimationDriver},\n  ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nexport const BROWSER_NOOP_ANIMATIONS_PROVIDERS: Provider[] =\n    [{provide: AnimationDriver, useClass: NoopAnimationDriver}, ...SHARED_ANIMATION_PROVIDERS];\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationBuilder, AnimationFactory, AnimationMetadata, AnimationOptions, AnimationPlayer, NoopAnimationPlayer, sequence} from '@angular/animations';\nimport {Injectable, RendererFactory2, RendererType2, ViewEncapsulation} from '@angular/core';\n\nimport {AnimationRenderer} from './animation_renderer';\nexport class BrowserAnimationBuilder extends AnimationBuilder {\nprivate _nextAnimationId = 0;\nprivate _renderer: AnimationRenderer;\n/**\n * @param {?} rootRenderer\n */\nconstructor(rootRenderer: RendererFactory2) {\n    super();\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {animation: []}\n    } as RendererType2;\n    this._renderer = rootRenderer.createRenderer(document.body, typeData) as AnimationRenderer;\n  }\n/**\n * @param {?} animation\n * @return {?}\n */\nbuild(animation: AnimationMetadata|AnimationMetadata[]): AnimationFactory {\n    const /** @type {?} */ id = this._nextAnimationId.toString();\n    this._nextAnimationId++;\n    const /** @type {?} */ entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: RendererFactory2, },\n];\n}\n\nfunction BrowserAnimationBuilder_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationBuilder.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationBuilder.ctorParameters;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._nextAnimationId;\n/** @type {?} */\nBrowserAnimationBuilder.prototype._renderer;\n}\n\nexport class BrowserAnimationFactory extends AnimationFactory {\n/**\n * @param {?} _id\n * @param {?} _renderer\n */\nconstructor(private _id: string,\nprivate _renderer: AnimationRenderer) { super(); }\n/**\n * @param {?} element\n * @param {?=} options\n * @return {?}\n */\ncreate(element: any, options?: AnimationOptions): AnimationPlayer {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\n\nfunction BrowserAnimationFactory_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationFactory.prototype._id;\n/** @type {?} */\nBrowserAnimationFactory.prototype._renderer;\n}\n\nexport class RendererAnimationPlayer implements AnimationPlayer {\npublic parentPlayer: AnimationPlayer|null = null;\nprivate _started = false;\n/**\n * @param {?} id\n * @param {?} element\n * @param {?} options\n * @param {?} _renderer\n */\nconstructor(\npublic id: string,\npublic element: any, options: AnimationOptions,\nprivate _renderer: AnimationRenderer) {\n    this._command('create', options);\n  }\n/**\n * @param {?} eventName\n * @param {?} callback\n * @return {?}\n */\nprivate _listen(eventName: string, callback: (event: any) => any): () => void {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n/**\n * @param {?} command\n * @param {...?} args\n * @return {?}\n */\nprivate _command(command: string, ...args: any[]) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDone(fn: () => void): void { this._listen('done', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonStart(fn: () => void): void { this._listen('start', fn); }\n/**\n * @param {?} fn\n * @return {?}\n */\nonDestroy(fn: () => void): void { this._listen('destroy', fn); }\n/**\n * @return {?}\n */\ninit(): void { this._command('init'); }\n/**\n * @return {?}\n */\nhasStarted(): boolean { return this._started; }\n/**\n * @return {?}\n */\nplay(): void {\n    this._command('play');\n    this._started = true;\n  }\n/**\n * @return {?}\n */\npause(): void { this._command('pause'); }\n/**\n * @return {?}\n */\nrestart(): void { this._command('restart'); }\n/**\n * @return {?}\n */\nfinish(): void { this._command('finish'); }\n/**\n * @return {?}\n */\ndestroy(): void { this._command('destroy'); }\n/**\n * @return {?}\n */\nreset(): void { this._command('reset'); }\n/**\n * @param {?} p\n * @return {?}\n */\nsetPosition(p: number): void { this._command('setPosition', p); }\n/**\n * @return {?}\n */\ngetPosition(): number { return 0; }\npublic totalTime = 0;\n}\n\nfunction RendererAnimationPlayer_tsickle_Closure_declarations() {\n/** @type {?} */\nRendererAnimationPlayer.prototype.parentPlayer;\n/** @type {?} */\nRendererAnimationPlayer.prototype._started;\n/** @type {?} */\nRendererAnimationPlayer.prototype.totalTime;\n/** @type {?} */\nRendererAnimationPlayer.prototype.id;\n/** @type {?} */\nRendererAnimationPlayer.prototype.element;\n/** @type {?} */\nRendererAnimationPlayer.prototype._renderer;\n}\n\n/**\n * @param {?} renderer\n * @param {?} element\n * @param {?} id\n * @param {?} command\n * @param {?} args\n * @return {?}\n */\nfunction issueAnimationCommand(\n    renderer: AnimationRenderer, element: any, id: string, command: string, args: any[]): any {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {BrowserModule} from '@angular/platform-browser';\n\nimport {BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS} from './providers';\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class BrowserAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserAnimationsModule.ctorParameters;\n}\n\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationsModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  imports: [BrowserModule],\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction NoopAnimationsModule_tsickle_Closure_declarations() {\n/** @type {?} */\nNoopAnimationsModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNoopAnimationsModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["exports", "module", "factory", "require", "resolveElementFromTarget", "target", "instantiateSupportedAnimationDriver", "instantiateRendererFactory", "renderer", "engine", "zone", "ɵAnimationEngine", "extendStatics", "Object", "setPrototypeOf", "__proto__", "Array", "d", "b", "p", "hasOwnProperty", "BrowserAnimationBuilder", "_super", "<PERSON><PERSON><PERSON><PERSON>", "styles", "__extends", "prototype", "build", "animation", "entry", "isArray", "_angular_animations", "sequence", "BrowserAnimationFactory", "id", "this", "_renderer", "args", "RendererAnimationPlayer", "onDone", "fn", "_listen", "reset", "_command", "_zone", "_this", "EMPTY_NAMESPACE_ID", "hostElement", "type", "data", "_rendererCache", "get", "delegate", "BaseAnimationRenderer", "set", "namespaceId", "_currentId", "AnimationRendererFactory", "scheduleListenerCallback", "count", "_microtaskId", "run", "_animationCallbacksBuffer", "length", "Promise", "resolve", "then", "push", "_scheduleCountTask", "whenRenderingDone", "_angular_core", "Injectable", "selectRootElement", "selectorOrNode", "<PERSON><PERSON><PERSON><PERSON>", "listen", "eventName", "callback", "char<PERSON>t", "element", "name", "substr", "phase", "_a", "parseTriggerCallbackName", "call", "driver", "normalizer", "InjectableAnimationEngine", "_angular_animations_browser", "ɵAnimationStyleNormalizer", "provide", "useClass", "BrowserAnimationsModule", "decorators", "ctorParameters", "NoopAnimationsModule"], "mappings": ";;;;;0BAAA,gBAAAA,UAAA,mBAAAC,QAAAC,QAAAF,QAAAG,QAAA,iBAAAA,QAAA,6BAAAA,QAAA,uBAAAA,QAAA,q1BCkZA,QAAAC,0BAAAC,QACA,OAAAA,sEAKA,KAAA,wOC7VA,QAAAC,+TAoBA,QAAAC,4BAAAC,SAAAC,OAAAC,MACA,MAAA,IAAAC,0BAAAH,SAAAC,OAAAC,MF3DA,GAAIE,eAAJC,OAAAC,iBACAC,uBAAAC,QAA2C,SAA3CC,EAAAC,GAAAD,EAAAF,UAAAG,IACI,SAAJD,EAAAC,GAAA,IAAA,GAA+BC,KAA/BD,GAAAA,EAA6CE,eAA7CD,KAAAF,EAAkEE,GAAlED,EAAAC,KGDAE,wBAAA,SAAAC,QAKA,QAAAD,yBAAAE,iJAKAC,sHAVAC,WAAAJ,wBAAAC,QAmBAD,wBAAAK,UAAAC,MAAA,SAAAC,0EAtBA,IAAAC,OAAAb,MAAAc,QAAAF,WAAAG,oBAAAC,SAAAJ,WAAAA,SAoBA,yEAAA,GAAAK,yBAAAC,GAAAC,KAAAC,+nCAoFAC,gJAUAC,wBAAAZ,UAAAa,OAAA,SAAAC,IAAAL,KAAAM,QAAA,OAAAD,ktBA8CAF,wBAAAZ,UAAAgB,MAAA,WAAAP,KAAAQ,SAAA,yUFrJAR,KAAAS,MAAAA,4UAqBA,GAAAC,OAAAV,KACAW,mBAAA,0DAKA,MAAAC,aAAAC,MAAAA,KAAAC,MAAAD,KAAAC,KAAA,WAAA,CARA,GAAAzC,UAAA2B,KAAAe,eAAAC,IAAAC,gBAWA5C,YACAA,SAAA,GAAA6C,uBAAAP,mBAAAM,SAAAjB,KAAA1B,QAIA0B,KAAAe,eAAAI,IAAAF,SAAA5C,4CAKA+C,YAAAP,KAAAd,GAAA,IAAAC,KAAAqB,UACArB,MAAAqB,olBA0BAC,yBAAA/B,UAAAgC,yBAAA,SAAAC,MAAAnB,GAAAS,MACA,GAAAJ,OAAAV,IACA,OAAAwB,QAAA,GAAAA,MAAAxB,KAAAyB,iBACAzB,MAAAS,MAAAiB,IAAA,WAAA,MAAArB,IAAAS,SAGA,GAAAd,KAAA2B,0BAAAC,QAEAC,QAAAC,QAAA,MAAAC,KAAA,WACArB,MAAAD,MAAAiB,IAAA,4GAKAhB,MAAAiB,qCAIA3B,MAAA2B,0BAAAK,MAAA3B,GAAAS,kHAOAJ,MAAAuB,sGAQAX,yBAAA/B,UAAA2C,kBAAA,WAAA,MAAAlC,MAAA1B,OAAA4D,uFAMArB,KAAAsB,cAAAC,gSA8CApC,KAAAoB,YAAAA,ytCA8DAF,sBAAA3B,UAAA8C,kBAAA,SAAAC,gBAAA,MAAAtC,MAAAiB,SA1F6DoB,kBA0F7DC,gsDAmIAC,kBAAAhD,UAAAiD,OAAA,SAAAtE,OAAAuE,UAAAC,UACA,GAAAhC,OAAYV,IACZ,IAAA,KAAAyC,UAAAE,OAAA,GAAA,CACA,GAAAC,SAAA3E,yBAAAC,QACA2E,KAAAJ,UAAAK,OAAA,GACAC,MAAA,SACA,KAAAF,KAAAF,OAAA,KAEAK,GAAAC,yBAAAJ,MAAAA,KAAAG,GAAA,GAAAD,MAAAC,GAAA,0PAcA9B,wHCpXA,MAAA/B,QAAA+D,KAAAlD,KAAAmD,OAAAC,aAAApD,WAAAV,WAAA+D,0BAAAlE,uQA6BA0B,KAAAyC,4BAAAC,2PA+BAC,QAAAF,4BAAA9E,iBAAAiF,SAAAJ,gkBEhEAK,wBAAA,gFAQAA,yBAAAC,4IAiBAD,wBAAAE,eAAA,WAAA,SAEA,IAAAC,sBAAA"}