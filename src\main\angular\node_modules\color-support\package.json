{"_args": [["color-support@1.1.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "color-support@1.1.3", "_id": "color-support@1.1.3", "_inBundle": false, "_integrity": "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==", "_location": "/color-support", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color-support@1.1.3", "name": "color-support", "escapedName": "color-support", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/fancy-log"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/color-support/-/color-support-1.1.3.tgz", "_spec": "1.1.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"color-support": "bin.js"}, "browser": "browser.js", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "description": "A module which will endeavor to guess your terminal's level of color support.", "devDependencies": {"tap": "^10.3.3"}, "files": ["browser.js", "index.js", "bin.js"], "homepage": "https://github.com/isaacs/color-support#readme", "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "license": "ISC", "main": "index.js", "name": "color-support", "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100 -J"}, "version": "1.1.3"}