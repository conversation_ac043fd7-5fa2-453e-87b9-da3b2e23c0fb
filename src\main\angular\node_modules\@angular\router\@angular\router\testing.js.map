{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/router/testing/index.ts", "../../../../../packages/router/testing/src/testing.ts", "../../../../../packages/router/testing/src/router_testing_module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\nexport * from './router_testing_module';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {MockLocationStrategy, SpyLocation} from '@angular/common/testing';\nimport {Compiler, Injectable, Injector, ModuleWithProviders, NgModule, NgModuleFactory, NgModuleFactoryLoader, Optional} from '@angular/core';\nimport {ChildrenOutletContexts, NoPreloading, PreloadingStrategy, ROUTES, Route, Router, RouterModule, Routes, UrlHandlingStrategy, UrlSerializer, provideRoutes, ɵROUTER_PROVIDERS as ROUTER_PROVIDERS, ɵflatten as flatten} from '@angular/router';\n\n\n\n/**\n * @whatItDoes Allows to simulate the loading of ng modules in tests.\n *\n * @howToUse\n *\n * ```\n * const loader = TestBed.get(NgModuleFactoryLoader);\n *\n * @Component({template: 'lazy-loaded'})\n * class LazyLoadedComponent {}\n * @NgModule({\n *   declarations: [LazyLoadedComponent],\n *   imports: [RouterModule.forChild([{path: 'loaded', component: LazyLoadedComponent}])]\n * })\n *\n * class LoadedModule {}\n *\n * // sets up stubbedModules\n * loader.stubbedModules = {lazyModule: LoadedModule};\n *\n * router.resetConfig([\n *   {path: 'lazy', loadChildren: 'lazyModule'},\n * ]);\n *\n * router.navigateByUrl('/lazy/loaded');\n * ```\n *\n * @stable\n */\n\nexport class SpyNgModuleFactoryLoader implements NgModuleFactoryLoader {\n  /**\n   * @docsNotRequired\n   */\n  private _stubbedModules: {[path: string]: Promise<NgModuleFactory<any>>} = {};\n\n  /**\n   * @docsNotRequired\n   */\n  set stubbedModules(modules: {[path: string]: any}) {\n    const res: {[path: string]: any} = {};\n    for (const t of Object.keys(modules)) {\n      res[t] = this.compiler.compileModuleAsync(modules[t]);\n    }\n    this._stubbedModules = res;\n  }\n\n  /**\n   * @docsNotRequired\n   */\n  get stubbedModules(): {[path: string]: any} { return this._stubbedModules; }\n\n  constructor(private compiler: Compiler) {}\n\n  load(path: string): Promise<NgModuleFactory<any>> {\n    if (this._stubbedModules[path]) {\n      return this._stubbedModules[path];\n    } else {\n      return <any>Promise.reject(new Error(`Cannot find module ${path}`));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Compiler, },\n];\n}\n\n/**\n * Router setup factory function used for testing.\n *\n * @stable\n */\nexport function setupTestingRouter(\n    urlSerializer: UrlSerializer, contexts: ChildrenOutletContexts, location: Location,\n    loader: NgModuleFactoryLoader, compiler: Compiler, injector: Injector, routes: Route[][],\n    urlHandlingStrategy?: UrlHandlingStrategy) {\n  const router = new Router(\n      null !, urlSerializer, contexts, location, injector, loader, compiler, flatten(routes));\n  if (urlHandlingStrategy) {\n    router.urlHandlingStrategy = urlHandlingStrategy;\n  }\n  return router;\n}\n\n/**\n * @whatItDoes Sets up the router to be used for testing.\n *\n * @howToUse\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestModule({\n *     imports: [\n *       RouterTestingModule.withRoutes(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}])]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @description\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of {@link Location}, {@link LocationStrategy}, and {@link\n * NgModuleFactoryLoader}.\n *\n * @stable\n */\n\nexport class RouterTestingModule {\n  static withRoutes(routes: Routes): ModuleWithProviders {\n    return {ngModule: RouterTestingModule, providers: [provideRoutes(routes)]};\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [RouterModule],\n  providers: [\n    ROUTER_PROVIDERS, {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n    {provide: NgModuleFactoryLoader, useClass: SpyNgModuleFactoryLoader}, {\n      provide: Router,\n      useFactory: setupTestingRouter,\n      deps: [\n        UrlSerializer, ChildrenOutletContexts, Location, NgModuleFactoryLoader, Compiler, Injector,\n        ROUTES, [UrlHandlingStrategy, new Optional()]\n      ]\n    },\n    {provide: PreloadingStrategy, useExisting: NoPreloading}, provideRoutes([])\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["ROUTER_PROVIDERS", "flatten"], "mappings": ";;;;;AEAA;;;;;;;AAQA,AACA,AACA,AACA,AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,AAAA,MAAA,wBAAA,CAAA;IAsBE,WAAF,CAAsB,QAAkB,EAAxC;QAAsB,IAAtB,CAAA,QAA8B,GAAR,QAAQ,CAAU;;;;QAlB9B,IAAV,CAAA,eAAyB,GAAoD,EAAE,CAAC;KAkBpC;;;;IAb1C,IAAI,cAAc,CAAC,OAA8B,EAAnD;QACI,MAAM,GAAG,GAA0B,EAAE,CAAC;QACtC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACpC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;KAC5B;;;;IAKD,IAAI,cAAc,GAApB,EAAgD,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE;IAI5E,IAAI,CAAC,IAAY,EAAnB;QACI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,OAAY,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAA3C,mBAAA,EAAiE,IAAI,CAArE,CAAuE,CAAC,CAAC,CAAC;SACrE;KACF;;AACI,wBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,wBAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,QAAQ,GAAG;CACjB,CAAC;;;;;;AAQF,AAAA,SAAA,kBAAA,CACI,aAPe,EAAe,QAAU,EAAwB,QAAU,EAQ1E,MAPQ,EAAuB,QAAU,EAAU,QAAU,EAAU,MAAgB,EAQvF,mBAPsB,EAI1B;IAIE,MAPM,MAAA,GAAS,IAAI,MAAA,CAQf,IAPK,EAAG,aAAA,EAAe,QAAA,EAAU,QAAA,EAAU,QAAA,EAAU,MAAA,EAAQ,QAAA,EAAUC,QAAA,CAAQ,MAAC,CAAM,CAAC,CAAC;IAQ5F,IAAI,mBAPC,EAAoB;QAQvB,MAAM,CAPC,mBAAC,GAAqB,mBAAA,CAAoB;KAQlD;IACD,OAPO,MAAA,CAAO;CAQf;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BD,AAAA,MAAA,mBAAA,CAAA;IACE,OAAO,UAAU,CAAC,MAAc,EAAlC;QACI,OAAO,EAAC,QAAQ,EAAE,mBAAmB,EAAE,SAAS,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC;KAC5E;;AACI,mBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;gBACvB,OAAO,EAAE,CAAC,YAAY,CAAC;gBACvB,SAAS,EAAE;oBACTD,iBAAgB,EAAE,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAC;oBAC5D,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;oBAC3D,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC,EAAE;wBACpE,OAAO,EAAE,MAAM;wBACf,UAAU,EAAE,kBAAkB;wBAC9B,IAAI,EAAE;4BACJ,aAAa,EAAE,sBAAsB,EAAE,QAAQ,EAAE,qBAAqB,EAAE,QAAQ,EAAE,QAAQ;4BAC1F,MAAM,EAAE,CAAC,mBAAmB,EAAE,IAAI,QAAQ,EAAE,CAAC;yBAC9C;qBACF;oBACD,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,YAAY,EAAC,EAAE,aAAa,CAAC,EAAE,CAAC;iBAC5E;aACF,EAAG,EAAE;CACL,CAAC;;AAEK,mBAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;;ADxJF;;;;;;;;;;;GAYG,AACH,AAAwC;;ADbxC;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}