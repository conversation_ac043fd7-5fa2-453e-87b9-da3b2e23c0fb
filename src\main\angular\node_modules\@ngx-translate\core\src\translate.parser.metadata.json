[{"__symbolic": "module", "version": 3, "metadata": {"TranslateParser": {"__symbolic": "class", "members": {"interpolate": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}]}}, "TranslateDefaultParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"interpolate": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslateParser": {"__symbolic": "class", "members": {"interpolate": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}]}}, "TranslateDefaultParser": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"interpolate": [{"__symbolic": "method"}], "getValue": [{"__symbolic": "method"}]}}}}]