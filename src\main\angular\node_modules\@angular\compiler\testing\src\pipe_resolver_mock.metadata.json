[{"__symbolic": "module", "version": 3, "metadata": {"MockPipeResolver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "PipeResolver"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileReflector"}]}], "_clearCacheFor": [{"__symbolic": "method"}], "setPipe": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MockPipeResolver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "PipeResolver"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileReflector"}]}], "_clearCacheFor": [{"__symbolic": "method"}], "setPipe": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}]}}}}]