{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/main.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,uBAAyB;AACzB,2BAA6B;AAC7B,iCAAmC;AACnC,+BAAiC;AAEjC,qCAA+D;AAC/D,6CAAyC;AACzC,iDAAuE;AACvE,+CAAqD;AAErD,6BAAiC;AACjC,2CAAoD;AAEpD,6BAAgC;AAAxB,0BAAA,SAAS,CAAA;AAEjB,IAAM,GAAG,GAAG,UAAU,CAAC;AACvB,IAAM,MAAM,GAAG,UAAU,CAAC;AAC1B,IAAM,MAAM,GAAG,OAAO,CAAC;AAUvB,cACI,OAA2B,EAAE,UAAsB,EAAE,OAA0B,EAC/E,OAA4B;IAC9B,IAAI,CAAC;QACH,IAAI,UAAU,GAAG,OAAO,CAAC;QACzB,oCAAoC;QACpC,EAAE,CAAC,CAAC,wBAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,qEAAqE;QACrE,IAAM,UAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,CAAC;QAEhF,8DAA8D;QAC1D,IAAA,8DAAuE,EAAtE,oBAAM,EAAE,0BAAS,CAAsD;QAC5E,WAAS,CAAC,QAAQ,GAAG,UAAQ,CAAC;QAC9B,IAAI,eAAa,GAAa,QAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxD,IAAM,eAAa,GAAG,UAAC,IAAqB,EAAE,UAAuB;YACnE,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,eAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAM,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACpF,CAAC,CAAC;QACF,IAAM,sBAAoB,GAAG,UAAC,WAAmB;YAC/C,EAAE,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,UAAQ,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACjE,eAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,aAAW,GAAI,QAAM,CAAC,OAAe,CAAC,WAAW,CAAC;QACxD,EAAE,CAAC,CAAC,aAAW,CAAC;YAAE,EAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;QAElD,IAAI,MAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,QAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvD,+EAA+E;QAC/E,gDAAgD;QAChD,EAAE,CAAC,CAAC,WAAS,CAAC,iBAAiB,IAAI,CAAC,WAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC/D,IAAM,KAAK,GAAG,QAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAZ,CAAY,CAAC,CAAC;YACzD,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,WAAK,CAAC,CAAC;wBACL,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,MAAM,EAAE,IAAI;wBACZ,WAAW,EACP,oGAAoG;wBACxG,QAAQ,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK;wBACrC,IAAI,EAAE,CAAC;qBACR,CAAC,CAAC,CAAC;YACN,CAAC;YACD,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC9C,IAAM,OAAO,GACT,IAAI,yBAAe,CAAC,WAAW,EAAE,WAAS,CAAC,YAAY,EAAE,IAAI,6BAAmB,CAAC,MAAI,CAAC,CAAC,CAAC;YAC5F,EAAE,CAAC,CAAC,aAAW,CAAC;gBAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtD,IAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACnD,EAAE,CAAC,CAAC,aAAW,CAAC;gBAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YACzD,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAM,MAAI,GACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,WAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAC7F,IAAM,YAAY,GAAG,OAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAG,CAAC;YACvD,IAAM,OAAO,GAAG,oCAAqB,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC7E,MAAI,GAAG,IAAI,kCAAkB,CAAC,MAAI,EAAE,EAAC,IAAI,QAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,UAAA,EAAC,CAAC,CAAC;YAC/D,sBAAoB,CAAC,MAAI,CAAC,CAAC;QAC7B,CAAC;QAED,IAAM,0BAA0B,GACV,EAAC,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAC,CAAC;QAE5F,IAAM,WAAW,GAAwB;YACvC,2BAA2B,EAAE,UAAC,QAAQ,IAAK,OAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAzB,CAAyB;YACpE,gBAAgB,EAAE,UAAC,OAAO,EAAE,UAAU,IAAK,OAAA,EAAE,EAAF,CAAE;YAC7C,2BAA2B,EAAE,UAAC,QAAQ,IAAK,OAAA,KAAK,EAAL,CAAK;YAChD,kBAAkB,EAAE,UAAC,QAAQ,IAAK,OAAA,QAAQ,EAAR,CAAQ;SAC3C,CAAC;QAEF,IAAM,qBAAmB,GACrB,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAI,EAAE,WAAS,EAAE,0BAA0B,EAAE,WAAW,CAAC,CAAC;QAE9F,IAAM,SAAO,GAAG,eAAa,CAAC,qBAAmB,CAAC,CAAC;QAEnD,IAAM,MAAM,GAAG,SAAO,CAAC,qBAAqB,EAAE,CAAC;QAC/C,WAAK,CAAC,MAAM,CAAC,CAAC;QAEd,EAAE,CAAC,CAAC,WAAS,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9C,OAAO,GAAG,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC;QACtC,CAAC;QAED,EAAE,CAAC,CAAC,aAAW,CAAC;YAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,CAAC,OAAO,CAAC,WAAS,EAAE,UAAU,EAAE,SAAO,EAAE,MAAI,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ;YACjE,EAAE,CAAC,CAAC,aAAW,CAAC;gBAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE/C,wFAAwF;YACxF,EAAE,CAAC,CAAC,WAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACzC,QAAQ,CAAC,OAAO,CAAC,UAAA,WAAW,IAAI,OAAA,sBAAoB,CAAC,WAAW,CAAC,EAAjC,CAAiC,CAAC,CAAC;YACrE,CAAC;YACD,IAAI,eAAe,GAAoB,qBAAmB,CAAC;YAC3D,EAAE,CAAC,CAAC,CAAC,WAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAChC,iFAAiF;gBACjF,sCAAsC;gBACtC,IAAM,WAAW,GACb,WAAS,CAAC,aAAa,KAAK,YAAY,IAAI,CAAC,WAAS,CAAC,0BAA0B,CAAC;gBACtF,eAAe,GAAG,IAAI,kCAAkB,CAAC,qBAAmB,EAAE,WAAS,EAAE,WAAW,CAAC,CAAC;YACxF,CAAC;YAED,qFAAqF;YACrF,IAAI,kBAAkB,GAAG,eAAa,CAAC,eAAe,EAAE,SAAO,CAAC,CAAC;YACjE,SAAG,CAAC,SAAS,CAAC,MAAI,EAAE,kBAAkB,CAAC,CAAC;YAExC,IAAI,gBAAgB,GAAG,kBAAkB,CAAC;YAE1C,EAAE,CAAC,CAAC,WAAS,CAAC,aAAa,KAAK,YAAY,CAAC,CAAC,CAAC;gBAC7C,EAAE,CAAC,CAAC,aAAW,CAAC;oBAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9C,qBAAmB,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC1F,kFAAkF;gBAClF,iBAAiB;gBACjB,gBAAgB,GAAG,eAAa,CAAC,qBAAmB,CAAC,CAAC;gBACtD,WAAK,CAAC,qBAAmB,CAAC,WAAW,CAAC,CAAC;gBACvC,EAAE,CAAC,CAAC,aAAW,CAAC;oBAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnD,CAAC;YAED,EAAE,CAAC,CAAC,WAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACzC,EAAE,CAAC,CAAC,aAAW,CAAC;oBAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1C,qBAAmB,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChF,gBAAgB,GAAG,eAAa,CAAC,qBAAmB,CAAC,CAAC;gBACtD,WAAK,CAAC,qBAAmB,CAAC,WAAW,CAAC,CAAC;gBACvC,EAAE,CAAC,CAAC,aAAW,CAAC;oBAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/C,CAAC;YAED,yBAAyB;YACzB,SAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE3B,wCAAwC;YACxC,+DAA+D;YAC/D,gDAAgD;YAChD,8EAA8E;YAC9E,wBAAwB;YACxB,SAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAE7B,EAAE,CAAC,CAAC,aAAW,CAAC,CAAC,CAAC;gBACf,EAAU,CAAC,WAAW,CAAC,cAAc,CAClC,UAAC,IAAY,EAAE,QAAgB,IAAO,OAAO,CAAC,KAAK,CAAC,QAAM,IAAI,UAAK,QAAQ,OAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC;AAnJD,oBAmJC;AAED,kBAAkB;AAClB,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAM,MAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAA,gCAAsD,EAArD,oBAAO,EAAE,kBAAM,CAAuC;IAC3D,WAAK,CAAC,MAAM,CAAC,CAAC;IACd,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC;IACvC,mFAAmF;IACnF,IAAM,UAAU,GAAG,IAAI,wBAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC;SACnC,IAAI,CAAC,UAAC,QAAa,IAAK,OAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAtB,CAAsB,CAAC;SAC/C,KAAK,CAAC,UAAC,CAAM;QACZ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as tsickle from 'tsickle';\nimport * as ts from 'typescript';\n\nimport {CompilerHostAdapter, MetadataBundler} from './bundler';\nimport {CliOptions} from './cli_options';\nimport {MetadataWriterHost, SyntheticIndexHost} from './compiler_host';\nimport {privateEntriesToIndex} from './index_writer';\nimport NgOptions from './options';\nimport {check, tsc} from './tsc';\nimport {isVinylFile, VinylFile} from './vinyl_file';\n\nexport {UserError} from './tsc';\n\nconst DTS = /\\.d\\.ts$/;\nconst JS_EXT = /(\\.js|)$/;\nconst TS_EXT = /\\.ts$/;\n\nexport interface CodegenExtension {\n  /**\n   * Returns the generated file names.\n   */\n  (ngOptions: NgOptions, cliOptions: CliOptions, program: ts.Program,\n   host: ts.CompilerHost): Promise<string[]>;\n}\n\nexport function main(\n    project: string | VinylFile, cliOptions: CliOptions, codegen?: CodegenExtension,\n    options?: ts.CompilerOptions): Promise<any> {\n  try {\n    let projectDir = project;\n    // project is vinyl like file object\n    if (isVinylFile(project)) {\n      projectDir = path.dirname(project.path);\n    }\n    // project is path to project file\n    else if (fs.lstatSync(project).isFile()) {\n      projectDir = path.dirname(project);\n    }\n\n    // file names in tsconfig are resolved relative to this absolute path\n    const basePath = path.resolve(process.cwd(), cliOptions.basePath || projectDir);\n\n    // read the configuration options from wherever you store them\n    let {parsed, ngOptions} = tsc.readConfiguration(project, basePath, options);\n    ngOptions.basePath = basePath;\n    let rootFileNames: string[] = parsed.fileNames.slice(0);\n    const createProgram = (host: ts.CompilerHost, oldProgram?: ts.Program) => {\n      return ts.createProgram(rootFileNames.slice(0), parsed.options, host, oldProgram);\n    };\n    const addGeneratedFileName = (genFileName: string) => {\n      if (genFileName.startsWith(basePath) && TS_EXT.exec(genFileName)) {\n        rootFileNames.push(genFileName);\n      }\n    };\n\n    const diagnostics = (parsed.options as any).diagnostics;\n    if (diagnostics) (ts as any).performance.enable();\n\n    let host = ts.createCompilerHost(parsed.options, true);\n\n    // If the compilation is a flat module index then produce the flat module index\n    // metadata and the synthetic flat module index.\n    if (ngOptions.flatModuleOutFile && !ngOptions.skipMetadataEmit) {\n      const files = parsed.fileNames.filter(f => !DTS.test(f));\n      if (files.length != 1) {\n        check([{\n          file: null,\n          start: null,\n          length: null,\n          messageText:\n              'Angular compiler option \"flatModuleIndex\" requires one and only one .ts file in the \"files\" field.',\n          category: ts.DiagnosticCategory.Error,\n          code: 0\n        }]);\n      }\n      const file = files[0];\n      const indexModule = file.replace(/\\.ts$/, '');\n      const bundler =\n          new MetadataBundler(indexModule, ngOptions.flatModuleId, new CompilerHostAdapter(host));\n      if (diagnostics) console.time('NG flat module index');\n      const metadataBundle = bundler.getMetadataBundle();\n      if (diagnostics) console.timeEnd('NG flat module index');\n      const metadata = JSON.stringify(metadataBundle.metadata);\n      const name =\n          path.join(path.dirname(indexModule), ngOptions.flatModuleOutFile.replace(JS_EXT, '.ts'));\n      const libraryIndex = `./${path.basename(indexModule)}`;\n      const content = privateEntriesToIndex(libraryIndex, metadataBundle.privates);\n      host = new SyntheticIndexHost(host, {name, content, metadata});\n      addGeneratedFileName(name);\n    }\n\n    const tsickleCompilerHostOptions:\n        tsickle.Options = {googmodule: false, untyped: true, convertIndexImportShorthand: true};\n\n    const tsickleHost: tsickle.TsickleHost = {\n      shouldSkipTsickleProcessing: (fileName) => /\\.d\\.ts$/.test(fileName),\n      pathToModuleName: (context, importPath) => '',\n      shouldIgnoreWarningsForPath: (filePath) => false,\n      fileNameToModuleId: (fileName) => fileName,\n    };\n\n    const tsickleCompilerHost =\n        new tsickle.TsickleCompilerHost(host, ngOptions, tsickleCompilerHostOptions, tsickleHost);\n\n    const program = createProgram(tsickleCompilerHost);\n\n    const errors = program.getOptionsDiagnostics();\n    check(errors);\n\n    if (ngOptions.skipTemplateCodegen || !codegen) {\n      codegen = () => Promise.resolve([]);\n    }\n\n    if (diagnostics) console.time('NG codegen');\n    return codegen(ngOptions, cliOptions, program, host).then((genFiles) => {\n      if (diagnostics) console.timeEnd('NG codegen');\n\n      // Add the generated files to the configuration so they will become part of the program.\n      if (ngOptions.alwaysCompileGeneratedCode) {\n        genFiles.forEach(genFileName => addGeneratedFileName(genFileName));\n      }\n      let definitionsHost: ts.CompilerHost = tsickleCompilerHost;\n      if (!ngOptions.skipMetadataEmit) {\n        // if tsickle is not not used for emitting, but we do use the MetadataWriterHost,\n        // it also needs to emit the js files.\n        const emitJsFiles =\n            ngOptions.annotationsAs === 'decorators' && !ngOptions.annotateForClosureCompiler;\n        definitionsHost = new MetadataWriterHost(tsickleCompilerHost, ngOptions, emitJsFiles);\n      }\n\n      // Create a new program since codegen files were created after making the old program\n      let programWithCodegen = createProgram(definitionsHost, program);\n      tsc.typeCheck(host, programWithCodegen);\n\n      let programForJsEmit = programWithCodegen;\n\n      if (ngOptions.annotationsAs !== 'decorators') {\n        if (diagnostics) console.time('NG downlevel');\n        tsickleCompilerHost.reconfigureForRun(programForJsEmit, tsickle.Pass.DECORATOR_DOWNLEVEL);\n        // A program can be re-used only once; save the programWithCodegen to be reused by\n        // metadataWriter\n        programForJsEmit = createProgram(tsickleCompilerHost);\n        check(tsickleCompilerHost.diagnostics);\n        if (diagnostics) console.timeEnd('NG downlevel');\n      }\n\n      if (ngOptions.annotateForClosureCompiler) {\n        if (diagnostics) console.time('NG JSDoc');\n        tsickleCompilerHost.reconfigureForRun(programForJsEmit, tsickle.Pass.CLOSURIZE);\n        programForJsEmit = createProgram(tsickleCompilerHost);\n        check(tsickleCompilerHost.diagnostics);\n        if (diagnostics) console.timeEnd('NG JSDoc');\n      }\n\n      // Emit *.js and *.js.map\n      tsc.emit(programForJsEmit);\n\n      // Emit *.d.ts and maybe *.metadata.json\n      // Not in the same emit pass with above, because tsickle erases\n      // decorators which we want to read or document.\n      // Do this emit second since TypeScript will create missing directories for us\n      // in the standard emit.\n      tsc.emit(programWithCodegen);\n\n      if (diagnostics) {\n        (ts as any).performance.forEachMeasure(\n            (name: string, duration: number) => { console.error(`TS ${name}: ${duration}ms`); });\n      }\n    });\n  } catch (e) {\n    return Promise.reject(e);\n  }\n}\n\n// CLI entry point\nif (require.main === module) {\n  const args = process.argv.slice(2);\n  let {options, errors} = (ts as any).parseCommandLine(args);\n  check(errors);\n  const project = options.project || '.';\n  // TODO(alexeagle): command line should be TSC-compatible, remove \"CliOptions\" here\n  const cliOptions = new CliOptions(require('minimist')(args));\n  main(project, cliOptions, null, options)\n      .then((exitCode: any) => process.exit(exitCode))\n      .catch((e: any) => {\n        console.error(e.stack);\n        console.error('Compilation failed');\n        process.exit(1);\n      });\n}\n"]}