{"version": 3, "file": "http-testing.umd.js", "sources": ["../../../../packages/http/testing/src/mock_backend.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Connection, ConnectionBackend, ReadyState, Request, Response} from '@angular/http';\nimport {ReplaySubject} from 'rxjs/ReplaySubject';\nimport {Subject} from 'rxjs/Subject';\nimport {take} from 'rxjs/operator/take';\n/**\n * \n * Mock Connection to represent a {\\@link Connection} for tests.\n * \n * \\@experimental\n */\nexport class MockConnection implements Connection {\n/**\n * Describes the state of the connection, based on `XMLHttpRequest.readyState`, but with\n * additional states. For example, state 5 indicates an aborted connection.\n */\nreadyState: ReadyState;\n/**\n * {\\@link Request} instance used to create the connection.\n */\nrequest: Request;\n/**\n * {\\@link EventEmitter} of {\\@link Response}. Can be subscribed to in order to be notified when a\n * response is available.\n */\nresponse: ReplaySubject<Response>;\n/**\n * @param {?} req\n */\nconstructor(req: Request) {\n    this.response = <any>take.call(new ReplaySubject(1), 1);\n    this.readyState = ReadyState.Open;\n    this.request = req;\n  }\n/**\n * Sends a mock response to the connection. This response is the value that is emitted to the\n * {\\@link EventEmitter} returned by {\\@link Http}.\n * \n * ### Example\n * \n * ```\n * var connection;\n * backend.connections.subscribe(c => connection = c);\n * http.request('data.json').subscribe(res => console.log(res.text()));\n * connection.mockRespond(new Response(new ResponseOptions({ body: 'fake response' }))); //logs\n * 'fake response'\n * ```\n * \n * @param {?} res\n * @return {?}\n */\nmockRespond(res: Response) {\n    if (this.readyState === ReadyState.Done || this.readyState === ReadyState.Cancelled) {\n      throw new Error('Connection has already been resolved');\n    }\n    this.readyState = ReadyState.Done;\n    this.response.next(res);\n    this.response.complete();\n  }\n/**\n * Not yet implemented!\n * \n * Sends the provided {\\@link Response} to the `downloadObserver` of the `Request`\n * associated with this connection.\n * @param {?} res\n * @return {?}\n */\nmockDownload(res: Response) {\n    // this.request.downloadObserver.onNext(res);\n    // if (res.bytesLoaded === res.totalBytes) {\n    //   this.request.downloadObserver.onCompleted();\n    // }\n  }\n/**\n * Emits the provided error object as an error to the {\\@link Response} {\\@link EventEmitter}\n * returned\n * from {\\@link Http}.\n * \n * ### Example\n * \n * ```\n * var connection;\n * backend.connections.subscribe(c => connection = c);\n * http.request('data.json').subscribe(res => res, err => console.log(err)));\n * connection.mockError(new Error('error'));\n * ```\n * \n * @param {?=} err\n * @return {?}\n */\nmockError(err?: Error) {\n    // Matches ResourceLoader semantics\n    this.readyState = ReadyState.Done;\n    this.response.error(err);\n  }\n}\n\nfunction MockConnection_tsickle_Closure_declarations() {\n/**\n * Describes the state of the connection, based on `XMLHttpRequest.readyState`, but with\n * additional states. For example, state 5 indicates an aborted connection.\n * @type {?}\n */\nMockConnection.prototype.readyState;\n/**\n * {\\@link Request} instance used to create the connection.\n * @type {?}\n */\nMockConnection.prototype.request;\n/**\n * {\\@link EventEmitter} of {\\@link Response}. Can be subscribed to in order to be notified when a\n * response is available.\n * @type {?}\n */\nMockConnection.prototype.response;\n}\n\n/**\n * A mock backend for testing the {\\@link Http} service.\n * \n * This class can be injected in tests, and should be used to override providers\n * to other backends, such as {\\@link XHRBackend}.\n * \n * ### Example\n * \n * ```\n * import {Injectable, ReflectiveInjector} from '\\@angular/core';\n * import {async, fakeAsync, tick} from '\\@angular/core/testing';\n * import {BaseRequestOptions, ConnectionBackend, Http, RequestOptions} from '\\@angular/http';\n * import {Response, ResponseOptions} from '\\@angular/http';\n * import {MockBackend, MockConnection} from '\\@angular/http/testing';\n * \n * const HERO_ONE = 'HeroNrOne';\n * const HERO_TWO = 'WillBeAlwaysTheSecond';\n * \n * \\@Injectable() \n * class HeroService {\n *   constructor(private http: Http) {}\n * \n *   getHeroes(): Promise<String[]> {\n *     return this.http.get('myservices.de/api/heroes')\n *         .toPromise()\n *         .then(response => response.json().data)\n *         .catch(e => this.handleError(e));\n *   }\n * \n *   private handleError(error: any): Promise<any> {\n *     console.error('An error occurred', error);\n *     return Promise.reject(error.message || error);\n *   }\n * }\n * \n * describe('MockBackend HeroService Example', () => {\n *   beforeEach(() => {\n *     this.injector = ReflectiveInjector.resolveAndCreate([\n *       {provide: ConnectionBackend, useClass: MockBackend},\n *       {provide: RequestOptions, useClass: BaseRequestOptions},\n *       Http,\n *       HeroService,\n *     ]);\n *     this.heroService = this.injector.get(HeroService);\n *     this.backend = this.injector.get(ConnectionBackend) as MockBackend;\n *     this.backend.connections.subscribe((connection: any) => this.lastConnection = connection);\n *   });\n * \n *   it('getHeroes() should query current service url', () => {\n *     this.heroService.getHeroes();\n *     expect(this.lastConnection).toBeDefined('no http service connection at all?');\n *     expect(this.lastConnection.request.url).toMatch(/api\\/heroes$/, 'url invalid');\n *   });\n * \n *   it('getHeroes() should return some heroes', fakeAsync(() => {\n *        let result: String[];\n *        this.heroService.getHeroes().then((heroes: String[]) => result = heroes);\n *        this.lastConnection.mockRespond(new Response(new ResponseOptions({\n *          body: JSON.stringify({data: [HERO_ONE, HERO_TWO]}),\n *        })));\n *        tick();\n *        expect(result.length).toEqual(2, 'should contain given amount of heroes');\n *        expect(result[0]).toEqual(HERO_ONE, ' HERO_ONE should be the first hero');\n *        expect(result[1]).toEqual(HERO_TWO, ' HERO_TWO should be the second hero');\n *      }));\n * \n *   it('getHeroes() while server is down', fakeAsync(() => {\n *        let result: String[];\n *        let catchedError: any;\n *        this.heroService.getHeroes()\n *            .then((heroes: String[]) => result = heroes)\n *            .catch((error: any) => catchedError = error);\n *        this.lastConnection.mockRespond(new Response(new ResponseOptions({\n *          status: 404,\n *          statusText: 'URL not Found',\n *        })));\n *        tick();\n *        expect(result).toBeUndefined();\n *        expect(catchedError).toBeDefined();\n *      }));\n * });\n * ```\n * \n * This method only exists in the mock implementation, not in real Backends.\n * \n * \\@experimental\n */\nexport class MockBackend implements ConnectionBackend {\n/**\n * {\\@link EventEmitter}\n * of {\\@link MockConnection} instances that have been created by this backend. Can be subscribed\n * to in order to respond to connections.\n * \n * ### Example\n * \n * ```\n * import {ReflectiveInjector} from '\\@angular/core';\n * import {fakeAsync, tick} from '\\@angular/core/testing';\n * import {BaseRequestOptions, ConnectionBackend, Http, RequestOptions} from '\\@angular/http';\n * import {Response, ResponseOptions} from '\\@angular/http';\n * import {MockBackend, MockConnection} from '\\@angular/http/testing';\n * \n * it('should get a response', fakeAsync(() => {\n *      let connection:\n *          MockConnection;  // this will be set when a new connection is emitted from the\n *                           // backend.\n *      let text: string;    // this will be set from mock response\n *      let injector = ReflectiveInjector.resolveAndCreate([\n *        {provide: ConnectionBackend, useClass: MockBackend},\n *        {provide: RequestOptions, useClass: BaseRequestOptions},\n *        Http,\n *      ]);\n *      let backend = injector.get(ConnectionBackend);\n *      let http = injector.get(Http);\n *      backend.connections.subscribe((c: MockConnection) => connection = c);\n *      http.request('something.json').toPromise().then((res: any) => text = res.text());\n *      connection.mockRespond(new Response(new ResponseOptions({body: 'Something'})));\n *      tick();\n *      expect(text).toBe('Something');\n *    }));\n * ```\n * \n * This property only exists in the mock implementation, not in real Backends.\n */\nconnections: any;\n/**\n * An array representation of `connections`. This array will be updated with each connection that\n * is created by this backend.\n * \n * This property only exists in the mock implementation, not in real Backends.\n */\nconnectionsArray: MockConnection[];\n/**\n * {\\@link EventEmitter} of {\\@link MockConnection} instances that haven't yet been resolved (i.e.\n * with a `readyState`\n * less than 4). Used internally to verify that no connections are pending via the\n * `verifyNoPendingRequests` method.\n * \n * This property only exists in the mock implementation, not in real Backends.\n */\npendingConnections: any;\nconstructor() {\n    this.connectionsArray = [];\n    this.connections = new Subject();\n    this.connections.subscribe(\n        (connection: MockConnection) => this.connectionsArray.push(connection));\n    this.pendingConnections = new Subject();\n  }\n/**\n * Checks all connections, and raises an exception if any connection has not received a response.\n * \n * This method only exists in the mock implementation, not in real Backends.\n * @return {?}\n */\nverifyNoPendingRequests() {\n    let /** @type {?} */ pending = 0;\n    this.pendingConnections.subscribe((c: MockConnection) => pending++);\n    if (pending > 0) throw new Error(`${pending} pending connections to be resolved`);\n  }\n/**\n * Can be used in conjunction with `verifyNoPendingRequests` to resolve any not-yet-resolve\n * connections, if it's expected that there are connections that have not yet received a response.\n * \n * This method only exists in the mock implementation, not in real Backends.\n * @return {?}\n */\nresolveAllConnections() { this.connections.subscribe((c: MockConnection) => c.readyState = 4); }\n/**\n * Creates a new {\\@link MockConnection}. This is equivalent to calling `new\n * MockConnection()`, except that it also will emit the new `Connection` to the `connections`\n * emitter of this `MockBackend` instance. This method will usually only be used by tests\n * against the framework itself, not by end-users.\n * @param {?} req\n * @return {?}\n */\ncreateConnection(req: Request): MockConnection {\n    if (!req || !(req instanceof Request)) {\n      throw new Error(`createConnection requires an instance of Request, got ${req}`);\n    }\n    const /** @type {?} */ connection = new MockConnection(req);\n    this.connections.next(connection);\n    return connection;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction MockBackend_tsickle_Closure_declarations() {\n/** @type {?} */\nMockBackend.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nMockBackend.ctorParameters;\n/**\n * {\\@link EventEmitter}\n * of {\\@link MockConnection} instances that have been created by this backend. Can be subscribed\n * to in order to respond to connections.\n * \n * ### Example\n * \n * ```\n * import {ReflectiveInjector} from '\\@angular/core';\n * import {fakeAsync, tick} from '\\@angular/core/testing';\n * import {BaseRequestOptions, ConnectionBackend, Http, RequestOptions} from '\\@angular/http';\n * import {Response, ResponseOptions} from '\\@angular/http';\n * import {MockBackend, MockConnection} from '\\@angular/http/testing';\n * \n * it('should get a response', fakeAsync(() => {\n *      let connection:\n *          MockConnection;  // this will be set when a new connection is emitted from the\n *                           // backend.\n *      let text: string;    // this will be set from mock response\n *      let injector = ReflectiveInjector.resolveAndCreate([\n *        {provide: ConnectionBackend, useClass: MockBackend},\n *        {provide: RequestOptions, useClass: BaseRequestOptions},\n *        Http,\n *      ]);\n *      let backend = injector.get(ConnectionBackend);\n *      let http = injector.get(Http);\n *      backend.connections.subscribe((c: MockConnection) => connection = c);\n *      http.request('something.json').toPromise().then((res: any) => text = res.text());\n *      connection.mockRespond(new Response(new ResponseOptions({body: 'Something'})));\n *      tick();\n *      expect(text).toBe('Something');\n *    }));\n * ```\n * \n * This property only exists in the mock implementation, not in real Backends.\n * @type {?}\n */\nMockBackend.prototype.connections;\n/**\n * An array representation of `connections`. This array will be updated with each connection that\n * is created by this backend.\n * \n * This property only exists in the mock implementation, not in real Backends.\n * @type {?}\n */\nMockBackend.prototype.connectionsArray;\n/**\n * {\\@link EventEmitter} of {\\@link MockConnection} instances that haven't yet been resolved (i.e.\n * with a `readyState`\n * less than 4). Used internally to verify that no connections are pending via the\n * `verifyNoPendingRequests` method.\n * \n * This property only exists in the mock implementation, not in real Backends.\n * @type {?}\n */\nMockBackend.prototype.pendingConnections;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["Injectable", "Request", "ReadyState"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;AAuCA,IAAA,cAAA,IAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BA,cAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,GAAA,EAAA;QACI,IAAI,IAAR,CAAA,UAAA,KAE2BE,wBAF3B,CAAA,IAAA,IAAA,IAAA,CAAA,UAAA,KAAAA,wBAAA,CAAA,SAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAA,sCAAA,CAAA,CAAA;;;;;;;;;;;;;;IAcA,cAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,GAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;IAuBA,cAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,GAAA,EAAA;;;;;;CAjEA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyOA,IAAA,WAAA,IAAA,YAAA;IACA,SAAA,WAAA,GAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;IAWA,WAAA,CAAA,SAAA,CAAA,uBAAA,GAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;IAsBA,WAAA,CAAA,SAAA,CAAA,gBAAA,GAAA,UAAA,GAlBU,EAkBV;QACI,IAAJ,CAAA,GAAA,IAAA,EAAA,GAlBW,YAkBXD,qBAAA,CAAA,EAAA;YACA,MAAA,IAAA,KAAA,CAAA,wDAAA,GAAA,GAAA,CAAA,CAAA;;QAjBA,qBAAA,UAAA,GAAA,IAAA,cAAA,CAAA,GAAA,CAAA,CAAA;QAmBA,IAlBQ,CAkBR,WAlBmB,CAkBnB,IAAA,CAAA,UAAA,CAAA,CAAA;QACA,OAAA,UAAA,CAAA;;;CAvCA,EAAA,CAAA,CAAA;;IAuBA,EAAA,IAAA,EAAAD,wBAAA,EAAA;;;;;;;;;;;;"}