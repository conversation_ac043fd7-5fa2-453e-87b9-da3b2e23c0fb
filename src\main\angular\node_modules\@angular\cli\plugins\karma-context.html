<!DOCTYPE html>
<!--
This is the execution context.
Loaded within the iframe.
Reloaded before every execution run.
-->
<html>

<head>
  <title></title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
</head>

<body>
  <!-- The scripts need to be in the body DOM element, as some test running frameworks need the body
       to have already been created so they can insert their magic into it. For example, if loaded
       before body, Angular Scenario test framework fails to find the body and crashes and burns in
       an epic manner. -->
  <script src="context.js"></script>
  <script type="text/javascript">
    // Configure our Karma and set up bindings
    %CLIENT_CONFIG%
      window.__karma__.setupContext(window);

    // All served files with the latest timestamps
    %MAPPINGS%
  </script>
  <script type="text/javascript" src="_karma_webpack_/inline.bundle.js" crossorigin="anonymous"></script>
  <script type="text/javascript" src="_karma_webpack_/polyfills.bundle.js" crossorigin="anonymous"></script>
  <!-- Dynamically replaced with <script> tags -->
  %SCRIPTS%
  <script type="text/javascript" src="_karma_webpack_/scripts.bundle.js" crossorigin="anonymous"></script>
  <script type="text/javascript" src="_karma_webpack_/vendor.bundle.js" crossorigin="anonymous"></script>
  <script type="text/javascript" src="_karma_webpack_/main.bundle.js" crossorigin="anonymous"></script>
  <script type="text/javascript">
      window.__karma__.loaded();
  </script>
</body>

</html>
