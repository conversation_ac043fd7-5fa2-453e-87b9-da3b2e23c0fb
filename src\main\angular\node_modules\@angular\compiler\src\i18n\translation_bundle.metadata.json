[{"__symbolic": "module", "version": 3, "metadata": {"TranslationBundle": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 22, "character": 33}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 21}, {"__symbolic": "error", "message": "Expression form not supported", "line": 24, "character": 29}, {"__symbolic": "reference", "module": "@angular/core", "name": "MissingTranslationStrategy"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "get": [{"__symbolic": "method"}], "has": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslationBundle": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 22, "character": 33}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 21}, {"__symbolic": "error", "message": "Expression form not supported", "line": 24, "character": 29}, {"__symbolic": "reference", "module": "@angular/core", "name": "MissingTranslationStrategy"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "get": [{"__symbolic": "method"}], "has": [{"__symbolic": "method"}]}}}}]