<html>
  <head>
    <meta charset="utf-8">
    <title>Clone Test-Suite (Browser)</title>
  </head>
  <body>
    <script>
      var data = document.location.search.substr(1).split('&');
      try {
        ctx = parent[data[0]];
        eval(decodeURIComponent(data[1]));
        window.results = results;
      } catch(e) {
        var extra = '';
        if (e.name == 'SecurityError')
          extra = 'This test suite needs to be run on an http server.';
        alert('Apart Context iFrame Error\n' + e + '\n\n' + extra);
        throw e;
      }
    </script>
  </body>
</html>
