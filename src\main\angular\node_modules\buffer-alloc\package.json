{"_args": [["buffer-alloc@1.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-alloc@1.2.0", "_id": "buffer-alloc@1.2.0", "_inBundle": false, "_integrity": "sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==", "_location": "/buffer-alloc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-alloc@1.2.0", "name": "buffer-alloc", "escapedName": "buffer-alloc", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/tar-stream"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-alloc/-/buffer-alloc-1.2.0.tgz", "_spec": "1.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/LinusU/buffer-alloc/issues"}, "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}, "description": "A [ponyfill](https://ponyfill.com) for `Buffer.alloc`.", "devDependencies": {"standard": "^7.1.2"}, "files": ["index.js"], "homepage": "https://github.com/LinusU/buffer-alloc#readme", "keywords": ["alloc", "allocate", "buffer alloc", "buffer allocate", "buffer"], "license": "MIT", "name": "buffer-alloc", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-alloc.git"}, "scripts": {"test": "standard && node test"}, "version": "1.2.0"}