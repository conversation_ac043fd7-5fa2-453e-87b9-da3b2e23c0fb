{"version": 3, "file": "cli_options.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/cli_options.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;GAMG;AACH;IAEE,oBAAY,EAAsC;YAArC,gBAAe,EAAf,oCAAe;QAA0B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAAC,CAAC;IACnF,iBAAC;AAAD,CAAC,AAHD,IAGC;AAHY,gCAAU;AAKvB;IAA8C,4CAAU;IAKtD,kCAAY,EAIX;YAJY,kBAAiB,EAAjB,sCAAiB,EAAE,cAAa,EAAb,kCAAa,EAAE,eAAc,EAAd,mCAAc;QAA7D,YAKE,kBAAM,EAAE,CAAC,SAIV;QAHC,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IACzB,CAAC;IACH,+BAAC;AAAD,CAAC,AAfD,CAA8C,UAAU,GAevD;AAfY,4DAAwB;AAiBrC;IAAmC,iCAAU;IAM3C,uBAAY,EAOX;YAPY,kBAAiB,EAAjB,sCAAiB,EAAE,gBAAe,EAAf,oCAAe,EAAE,cAAa,EAAb,kCAAa,EAAE,0BAAyB,EAAzB,8CAAyB,EAC5E,gBAAe,EAAf,oCAAe;QAD5B,YAQE,kBAAM,EAAC,QAAQ,EAAE,QAAQ,EAAC,CAAC,SAK5B;QAJC,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;;IAC/C,CAAC;IACH,oBAAC;AAAD,CAAC,AApBD,CAAmC,UAAU,GAoB5C;AApBY,sCAAa", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport class CliOptions {\n  public basePath: string;\n  constructor({basePath = null}: {basePath?: string}) { this.basePath = basePath; }\n}\n\nexport class I18nExtractionCliOptions extends CliOptions {\n  i18nFormat: string|null;\n  locale: string|null;\n  outFile: string|null;\n\n  constructor({i18nFormat = null, locale = null, outFile = null}: {\n    i18nFormat?: string,\n    locale?: string,\n    outFile?: string,\n  }) {\n    super({});\n    this.i18nFormat = i18nFormat;\n    this.locale = locale;\n    this.outFile = outFile;\n  }\n}\n\nexport class NgcCliOptions extends CliOptions {\n  public i18nFormat: string;\n  public i18nFile: string;\n  public locale: string;\n  public missingTranslation: string;\n\n  constructor({i18nFormat = null, i18nFile = null, locale = null, missingTranslation = null,\n               basePath = null}: {\n    i18nFormat?: string,\n    i18nFile?: string,\n    locale?: string,\n    missingTranslation?: string,\n    basePath?: string\n  }) {\n    super({basePath: basePath});\n    this.i18nFormat = i18nFormat;\n    this.i18nFile = i18nFile;\n    this.locale = locale;\n    this.missingTranslation = missingTranslation;\n  }\n}\n"]}