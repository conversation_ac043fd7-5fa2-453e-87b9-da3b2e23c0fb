{"_args": [["async-foreach@0.1.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "async-foreach@0.1.3", "_id": "async-foreach@0.1.3", "_inBundle": false, "_integrity": "sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=", "_location": "/async-foreach", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "async-foreach@0.1.3", "name": "async-foreach", "escapedName": "async-foreach", "rawSpec": "0.1.3", "saveSpec": null, "fetchSpec": "0.1.3"}, "_requiredBy": ["/node-sass"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/async-foreach/-/async-foreach-0.1.3.tgz", "_spec": "0.1.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "bugs": {"url": "https://github.com/cowboy/javascript-sync-async-foreach/issues"}, "dependencies": {}, "description": "An optionally-asynchronous forEach with an interesting interface.", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "http://github.com/cowboy/javascript-sync-async-foreach", "keywords": ["array", "loop", "sync", "async", "foreach"], "main": "lib/foreach", "name": "async-foreach", "repository": {"type": "git", "url": "git://github.com/cowboy/javascript-sync-async-foreach.git"}, "version": "0.1.3"}