{"version": 3, "file": "http.es5.js", "sources": ["../../../../packages/http/public_api.ts", "../../../../packages/http/src/index.ts", "../../../../packages/http/src/version.ts", "../../../../packages/http/src/http_module.ts", "../../../../packages/http/src/http.ts", "../../../../packages/http/src/static_request.ts", "../../../../packages/http/src/base_request_options.ts", "../../../../packages/http/src/backends/xhr_backend.ts", "../../../../packages/http/src/backends/jsonp_backend.ts", "../../../../packages/http/src/backends/browser_jsonp.ts", "../../../../packages/http/src/static_response.ts", "../../../../packages/http/src/body.ts", "../../../../packages/http/src/url_search_params.ts", "../../../../packages/http/src/http_utils.ts", "../../../../packages/http/src/interfaces.ts", "../../../../packages/http/src/base_response_options.ts", "../../../../packages/http/src/headers.ts", "../../../../packages/http/src/enums.ts", "../../../../packages/http/src/backends/browser_xhr.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the http package.\n */\nexport {BrowserXhr,JSONPBackend,JSONPConnection,CookieXSRFStrategy,XHRBackend,XHRConnection,BaseRequestOptions,RequestOptions,BaseResponseOptions,ResponseOptions,ReadyState,RequestMethod,ResponseContentType,ResponseType,Headers,Http,Jsonp,HttpModule,JsonpModule,Connection,ConnectionBackend,RequestOptionsArgs,ResponseOptionsArgs,XSRFStrategy,Request,Response,QueryEncoder,URLSearchParams,VERSION} from './src/index';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {BrowserXhr} from './backends/browser_xhr';\nexport {JSONPBackend, JSONPConnection} from './backends/jsonp_backend';\nexport {CookieXSRFStrategy, XHRBackend, XHRConnection} from './backends/xhr_backend';\nexport {BaseRequestOptions, RequestOptions} from './base_request_options';\nexport {BaseResponseOptions, ResponseOptions} from './base_response_options';\nexport {ReadyState, RequestMethod, ResponseContentType, ResponseType} from './enums';\nexport {Headers} from './headers';\nexport {Http, Jsonp} from './http';\nexport {HttpModule, JsonpModule} from './http_module';\nexport {Connection, ConnectionBackend, RequestOptionsArgs, ResponseOptionsArgs, XSRFStrategy} from './interfaces';\nexport {Request} from './static_request';\nexport {Response} from './static_response';\nexport {QueryEncoder, URLSearchParams} from './url_search_params';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n\nimport {Version} from '@angular/core';\n/**\n * \\@stable\n */\nexport const VERSION = new Version('4.2.5');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * The http module provides services to perform http requests. To get started, see the {@link Http}\n * class.\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {BrowserJsonp} from './backends/browser_jsonp';\nimport {BrowserXhr} from './backends/browser_xhr';\nimport {JSONPBackend, JSONPBackend_} from './backends/jsonp_backend';\nimport {CookieXSRFStrategy, XHRBackend} from './backends/xhr_backend';\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {BaseResponseOptions, ResponseOptions} from './base_response_options';\nimport {Http, Jsonp} from './http';\nimport {XSRFStrategy} from './interfaces';\n/**\n * @return {?}\n */\nexport function _createDefaultCookieXSRFStrategy() {\n  return new CookieXSRFStrategy();\n}\n/**\n * @param {?} xhrBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function httpFactory(xhrBackend: XHRBackend, requestOptions: RequestOptions): Http {\n  return new Http(xhrBackend, requestOptions);\n}\n/**\n * @param {?} jsonpBackend\n * @param {?} requestOptions\n * @return {?}\n */\nexport function jsonpFactory(jsonpBackend: JSONPBackend, requestOptions: RequestOptions): Jsonp {\n  return new Jsonp(jsonpBackend, requestOptions);\n}\n/**\n * The module that includes http's providers\n * \n * \\@experimental\n */\nexport class HttpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Http, useFactory: httpFactory, deps: [XHRBackend, RequestOptions]},\n    BrowserXhr,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    XHRBackend,\n    {provide: XSRFStrategy, useFactory: _createDefaultCookieXSRFStrategy},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction HttpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nHttpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttpModule.ctorParameters;\n}\n\n/**\n * The module that includes jsonp's providers\n * \n * \\@experimental\n */\nexport class JsonpModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  providers: [\n    // TODO(pascal): use factory type annotations once supported in DI\n    // issue: https://github.com/angular/angular/issues/3183\n    {provide: Jsonp, useFactory: jsonpFactory, deps: [JSONPBackend, RequestOptions]},\n    BrowserJsonp,\n    {provide: RequestOptions, useClass: BaseRequestOptions},\n    {provide: ResponseOptions, useClass: BaseResponseOptions},\n    {provide: JSONPBackend, useClass: JSONPBackend_},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction JsonpModule_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonpModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonpModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\n\nimport {BaseRequestOptions, RequestOptions} from './base_request_options';\nimport {RequestMethod} from './enums';\nimport {ConnectionBackend, RequestArgs, RequestOptionsArgs} from './interfaces';\nimport {Request} from './static_request';\nimport {Response} from './static_response';\n/**\n * @param {?} backend\n * @param {?} request\n * @return {?}\n */\nfunction httpRequest(backend: ConnectionBackend, request: Request): Observable<Response> {\n  return backend.createConnection(request).response;\n}\n/**\n * @param {?} defaultOpts\n * @param {?} providedOpts\n * @param {?} method\n * @param {?} url\n * @return {?}\n */\nfunction mergeOptions(\n    defaultOpts: BaseRequestOptions, providedOpts: RequestOptionsArgs | undefined,\n    method: RequestMethod, url: string): RequestArgs {\n  const /** @type {?} */ newOptions = defaultOpts;\n  if (providedOpts) {\n    // Hack so Dart can used named parameters\n    return /** @type {?} */(( newOptions.merge(new RequestOptions({\n      method: providedOpts.method || method,\n      url: providedOpts.url || url,\n      search: providedOpts.search,\n      params: providedOpts.params,\n      headers: providedOpts.headers,\n      body: providedOpts.body,\n      withCredentials: providedOpts.withCredentials,\n      responseType: providedOpts.responseType\n    })) as RequestArgs));\n  }\n\n  return /** @type {?} */(( newOptions.merge(new RequestOptions({method, url})) as RequestArgs));\n}\n/**\n * Performs http requests using `XMLHttpRequest` as the default backend.\n * \n * `Http` is available as an injectable class, with methods to perform http requests. Calling\n * `request` returns an `Observable` which will emit a single {\\@link Response} when a\n * response is received.\n * \n * ### Example\n * \n * ```typescript\n * import {Http, HTTP_PROVIDERS} from '\\@angular/http';\n * import 'rxjs/add/operator/map'\n * \\@Component({ \n *   selector: 'http-app',\n *   viewProviders: [HTTP_PROVIDERS],\n *   templateUrl: 'people.html'\n * })\n * class PeopleComponent {\n *   constructor(http: Http) {\n *     http.get('people.json')\n *       // Call map on the response observable to get the parsed people object\n *       .map(res => res.json())\n *       // Subscribe to the observable to get the parsed people object and attach it to the\n *       // component\n *       .subscribe(people => this.people = people);\n *   }\n * }\n * ```\n * \n * \n * ### Example\n * \n * ```\n * http.get('people.json').subscribe((res:Response) => this.people = res.json());\n * ```\n * \n * The default construct used to perform requests, `XMLHttpRequest`, is abstracted as a \"Backend\" (\n * {\\@link XHRBackend} in this case), which could be mocked with dependency injection by replacing\n * the {\\@link XHRBackend} provider, as in the following example:\n * \n * ### Example\n * \n * ```typescript\n * import {BaseRequestOptions, Http} from '\\@angular/http';\n * import {MockBackend} from '\\@angular/http/testing';\n * var injector = Injector.resolveAndCreate([\n *   BaseRequestOptions,\n *   MockBackend,\n *   {provide: Http, useFactory:\n *       function(backend, defaultOptions) {\n *         return new Http(backend, defaultOptions);\n *       },\n *       deps: [MockBackend, BaseRequestOptions]}\n * ]);\n * var http = injector.get(Http);\n * http.get('request-from-mock-backend.json').subscribe((res:Response) => doSomething(res));\n * ```\n * \n * \\@experimental\n */\nexport class Http {\n/**\n * @param {?} _backend\n * @param {?} _defaultOptions\n */\nconstructor(protected _backend: ConnectionBackend, protected _defaultOptions: RequestOptions) {}\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      responseObservable = httpRequest(\n          this._backend,\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url)))));\n    } else if (url instanceof Request) {\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\n/**\n * Performs a request with `get` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nget(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, url)));\n  }\n/**\n * Performs a request with `post` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npost(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Post,\n        url)));\n  }\n/**\n * Performs a request with `put` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\nput(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Put,\n        url)));\n  }\n/**\n * Performs a request with `delete` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\ndelete (url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Delete, url)));\n  }\n/**\n * Performs a request with `patch` http method.\n * @param {?} url\n * @param {?} body\n * @param {?=} options\n * @return {?}\n */\npatch(url: string, body: any, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(new Request(mergeOptions(\n        this._defaultOptions.merge(new RequestOptions({body: body})), options, RequestMethod.Patch,\n        url)));\n  }\n/**\n * Performs a request with `head` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nhead(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Head, url)));\n  }\n/**\n * Performs a request with `options` http method.\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\noptions(url: string, options?: RequestOptionsArgs): Observable<Response> {\n    return this.request(\n        new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Options, url)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Http_tsickle_Closure_declarations() {\n/** @type {?} */\nHttp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHttp.ctorParameters;\n/** @type {?} */\nHttp.prototype._backend;\n/** @type {?} */\nHttp.prototype._defaultOptions;\n}\n\n/**\n * \\@experimental\n */\nexport class Jsonp extends Http {\n/**\n * @param {?} backend\n * @param {?} defaultOptions\n */\nconstructor(backend: ConnectionBackend, defaultOptions: RequestOptions) {\n    super(backend, defaultOptions);\n  }\n/**\n * Performs any type of http request. First argument is required, and can either be a url or\n * a {\\@link Request} instance. If the first argument is a url, an optional {\\@link RequestOptions}\n * object can be provided as the 2nd argument. The options object will be merged with the values\n * of {\\@link BaseRequestOptions} before performing the request.\n * \n * \\@security Regular XHR is the safest alternative to JSONP for most applications, and is\n * supported by all current browsers. Because JSONP creates a `<script>` element with\n * contents retrieved from a remote source, attacker-controlled data introduced by an untrusted\n * source could expose your application to XSS risks. Data exposed by JSONP may also be\n * readable by malicious third-party websites. In addition, JSONP introduces potential risk for\n * future security issues (e.g. content sniffing).  For more detail, see the\n * [Security Guide](http://g.co/ng/security).\n * @param {?} url\n * @param {?=} options\n * @return {?}\n */\nrequest(url: string|Request, options?: RequestOptionsArgs): Observable<Response> {\n    let /** @type {?} */ responseObservable: any;\n    if (typeof url === 'string') {\n      url =\n          new Request(mergeOptions(this._defaultOptions, options, RequestMethod.Get, /** @type {?} */(( <string>url))));\n    }\n    if (url instanceof Request) {\n      if (url.method !== RequestMethod.Get) {\n        throw new Error('JSONP requests must use GET request method.');\n      }\n      responseObservable = httpRequest(this._backend, url);\n    } else {\n      throw new Error('First argument must be a url string or Request instance.');\n    }\n    return responseObservable;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ConnectionBackend, },\n{type: RequestOptions, },\n];\n}\n\nfunction Jsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Body} from './body';\nimport {ContentType, RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates `Request` instances from provided values.\n * \n * The Request's interface is inspired by the Request constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#request-class),\n * but is considered a static value whose body can be accessed many times. There are other\n * differences in the implementation, but this is the most significant.\n * \n * `Request` instances are typically created by higher-level classes, like {\\@link Http} and\n * {\\@link Jsonp}, but it may occasionally be useful to explicitly create `Request` instances.\n * One such example is when creating services that wrap higher-level services, like {\\@link Http},\n * where it may be useful to generate a `Request` with arbitrary headers and search params.\n * \n * ```typescript\n * import {Injectable, Injector} from '\\@angular/core';\n * import {HTTP_PROVIDERS, Http, Request, RequestMethod} from '\\@angular/http';\n * \n * \\@Injectable() \n * class AutoAuthenticator {\n *   constructor(public http:Http) {}\n *   request(url:string) {\n *     return this.http.request(new Request({\n *       method: RequestMethod.Get,\n *       url: url,\n *       search: 'password=123'\n *     }));\n *   }\n * }\n * \n * var injector = Injector.resolveAndCreate([HTTP_PROVIDERS, AutoAuthenticator]);\n * var authenticator = injector.get(AutoAuthenticator);\n * authenticator.request('people.json').subscribe(res => {\n *   //URL should have included '?password=123'\n *   console.log('people', res.json());\n * });\n * ```\n * \n * \\@experimental\n */\nexport class Request extends Body {\n/**\n * Http method with which to perform the request.\n */\nmethod: RequestMethod;\n/**\n * {\\@link Headers} instance\n */\nheaders: Headers;\n/**\n * Url of the remote resource\n */\nurl: string;\n/**\n * Type of the request body *\n */\nprivate contentType: ContentType;\n/**\n * Enable use credentials\n */\nwithCredentials: boolean;\n/**\n * Buffer to store the response\n */\nresponseType: ResponseContentType;\n/**\n * @param {?} requestOptions\n */\nconstructor(requestOptions: RequestArgs) {\n    super();\n    // TODO: assert that url is present\n    const url = requestOptions.url;\n    this.url = requestOptions.url !;\n    const paramsArg = requestOptions.params || requestOptions.search;\n    if (paramsArg) {\n      let params: string;\n      if (typeof paramsArg === 'object' && !(paramsArg instanceof URLSearchParams)) {\n        params = urlEncodeParams(paramsArg).toString();\n      } else {\n        params = paramsArg.toString();\n      }\n      if (params.length > 0) {\n        let prefix = '?';\n        if (this.url.indexOf('?') != -1) {\n          prefix = (this.url[this.url.length - 1] == '&') ? '' : '&';\n        }\n        // TODO: just delete search-query-looking string in url?\n        this.url = url + prefix + params;\n      }\n    }\n    this._body = requestOptions.body;\n    this.method = normalizeMethodName(requestOptions.method !);\n    // TODO(jeffbcross): implement behavior\n    // Defaults to 'omit', consistent with browser\n    this.headers = new Headers(requestOptions.headers);\n    this.contentType = this.detectContentType();\n    this.withCredentials = requestOptions.withCredentials !;\n    this.responseType = requestOptions.responseType !;\n  }\n/**\n * Returns the content type enum based on header options.\n * @return {?}\n */\ndetectContentType(): ContentType {\n    switch (this.headers.get('content-type')) {\n      case 'application/json':\n        return ContentType.JSON;\n      case 'application/x-www-form-urlencoded':\n        return ContentType.FORM;\n      case 'multipart/form-data':\n        return ContentType.FORM_DATA;\n      case 'text/plain':\n      case 'text/html':\n        return ContentType.TEXT;\n      case 'application/octet-stream':\n        return this._body instanceof ArrayBuffer ? ContentType.ARRAY_BUFFER : ContentType.BLOB;\n      default:\n        return this.detectContentTypeFromBody();\n    }\n  }\n/**\n * Returns the content type of request's body based on its type.\n * @return {?}\n */\ndetectContentTypeFromBody(): ContentType {\n    if (this._body == null) {\n      return ContentType.NONE;\n    } else if (this._body instanceof URLSearchParams) {\n      return ContentType.FORM;\n    } else if (this._body instanceof FormData) {\n      return ContentType.FORM_DATA;\n    } else if (this._body instanceof Blob) {\n      return ContentType.BLOB;\n    } else if (this._body instanceof ArrayBuffer) {\n      return ContentType.ARRAY_BUFFER;\n    } else if (this._body && typeof this._body === 'object') {\n      return ContentType.JSON;\n    } else {\n      return ContentType.TEXT;\n    }\n  }\n/**\n * Returns the request's body according to its type. If body is undefined, return\n * null.\n * @return {?}\n */\ngetBody(): any {\n    switch (this.contentType) {\n      case ContentType.JSON:\n        return this.text();\n      case ContentType.FORM:\n        return this.text();\n      case ContentType.FORM_DATA:\n        return this._body;\n      case ContentType.TEXT:\n        return this.text();\n      case ContentType.BLOB:\n        return this.blob();\n      case ContentType.ARRAY_BUFFER:\n        return this.arrayBuffer();\n      default:\n        return null;\n    }\n  }\n}\n\nfunction Request_tsickle_Closure_declarations() {\n/**\n * Http method with which to perform the request.\n * @type {?}\n */\nRequest.prototype.method;\n/**\n * {\\@link Headers} instance\n * @type {?}\n */\nRequest.prototype.headers;\n/**\n * Url of the remote resource\n * @type {?}\n */\nRequest.prototype.url;\n/**\n * Type of the request body *\n * @type {?}\n */\nRequest.prototype.contentType;\n/**\n * Enable use credentials\n * @type {?}\n */\nRequest.prototype.withCredentials;\n/**\n * Buffer to store the response\n * @type {?}\n */\nRequest.prototype.responseType;\n}\n\n/**\n * @param {?} params\n * @return {?}\n */\nfunction urlEncodeParams(params: {[key: string]: any}): URLSearchParams {\n  const /** @type {?} */ searchParams = new URLSearchParams();\n  Object.keys(params).forEach(key => {\n    const /** @type {?} */ value = params[key];\n    if (value && Array.isArray(value)) {\n      value.forEach(element => searchParams.append(key, element.toString()));\n    } else {\n      searchParams.append(key, value.toString());\n    }\n  });\n  return searchParams;\n}\n\nconst /** @type {?} */ noop = function() {};\nconst /** @type {?} */ w = typeof window == 'object' ? window : noop;\nconst /** @type {?} */ FormData = ( /** @type {?} */((w as any)) /** TODO #9100 */)['FormData'] || noop;\nconst /** @type {?} */ Blob = ( /** @type {?} */((w as any)) /** TODO #9100 */)['Blob'] || noop;\nexport const /** @type {?} */ ArrayBuffer: ArrayBufferConstructor =\n    ( /** @type {?} */((w as any)) /** TODO #9100 */)['ArrayBuffer'] || noop;\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {RequestMethod, ResponseContentType} from './enums';\nimport {Headers} from './headers';\nimport {normalizeMethodName} from './http_utils';\nimport {RequestOptionsArgs} from './interfaces';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Creates a request options object to be optionally provided when instantiating a\n * {\\@link Request}.\n * \n * This class is based on the `RequestInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#requestinit).\n * \n * All values are null by default. Typical defaults can be found in the {\\@link BaseRequestOptions}\n * class, which sub-classes `RequestOptions`.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * });\n * const req = new Request(options);\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class RequestOptions {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n */\nmethod: RequestMethod|string|null;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n */\nheaders: Headers|null;\n/**\n * Body to be used when creating a {\\@link Request}.\n */\nbody: any;\n/**\n * Url with which to perform a {\\@link Request}.\n */\nurl: string|null;\n/**\n * Search parameters to be included in a {\\@link Request}.\n */\nparams: URLSearchParams;\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @return {?}\n */\nget search(): URLSearchParams { return this.params; }\n/**\n * @deprecated from 4.0.0. Use params instead.\n * @param {?} params\n * @return {?}\n */\nset search(params: URLSearchParams) { this.params = params; }\n/**\n * Enable use credentials for a {\\@link Request}.\n */\nwithCredentials: boolean|null;\n  /*\n   * Select a buffer to store the response, such as ArrayBuffer, Blob, Json (or Document)\n   */\n  responseType: ResponseContentType|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: RequestOptionsArgs = {}) {\n    const {method, headers, body, url, search, params, withCredentials, responseType} = opts;\n    this.method = method != null ? normalizeMethodName(method) : null;\n    this.headers = headers != null ? headers : null;\n    this.body = body != null ? body : null;\n    this.url = url != null ? url : null;\n    this.params = this._mergeSearchParams(params || search);\n    this.withCredentials = withCredentials != null ? withCredentials : null;\n    this.responseType = responseType != null ? responseType : null;\n  }\n/**\n * Creates a copy of the `RequestOptions` instance, using the optional input as values to override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * Note that `headers` and `search` will override existing values completely if present in\n * the `options` object. If these values should be merged, it should be done prior to calling\n * `merge` on the `RequestOptions` instance.\n * \n * ```typescript\n * import {RequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new RequestOptions({\n *   method: RequestMethod.Post\n * });\n * const req = new Request(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: RequestOptionsArgs): RequestOptions {\n    return new RequestOptions({\n      method: options && options.method != null ? options.method : this.method,\n      headers: options && options.headers != null ? options.headers : new Headers(this.headers),\n      body: options && options.body != null ? options.body : this.body,\n      url: options && options.url != null ? options.url : this.url,\n      params: options && this._mergeSearchParams(options.params || options.search),\n      withCredentials: options && options.withCredentials != null ? options.withCredentials :\n                                                                    this.withCredentials,\n      responseType: options && options.responseType != null ? options.responseType :\n                                                              this.responseType\n    });\n  }\n/**\n * @param {?=} params\n * @return {?}\n */\nprivate _mergeSearchParams(params?: string|URLSearchParams|{[key: string]: any | any[]}|\n                             null): URLSearchParams {\n    if (!params) return this.params;\n\n    if (params instanceof URLSearchParams) {\n      return params.clone();\n    }\n\n    if (typeof params === 'string') {\n      return new URLSearchParams(params);\n    }\n\n    return this._parseParams(params);\n  }\n/**\n * @param {?=} objParams\n * @return {?}\n */\nprivate _parseParams(objParams: {[key: string]: any | any[]} = {}): URLSearchParams {\n    const /** @type {?} */ params = new URLSearchParams();\n    Object.keys(objParams).forEach((key: string) => {\n      const /** @type {?} */ value: any|any[] = objParams[key];\n      if (Array.isArray(value)) {\n        value.forEach((item: any) => this._appendParam(key, item, params));\n      } else {\n        this._appendParam(key, value, params);\n      }\n    });\n    return params;\n  }\n/**\n * @param {?} key\n * @param {?} value\n * @param {?} params\n * @return {?}\n */\nprivate _appendParam(key: string, value: any, params: URLSearchParams): void {\n    if (typeof value !== 'string') {\n      value = JSON.stringify(value);\n    }\n    params.append(key, value);\n  }\n}\n\nfunction RequestOptions_tsickle_Closure_declarations() {\n/**\n * Http method with which to execute a {\\@link Request}.\n * Acceptable methods are defined in the {\\@link RequestMethod} enum.\n * @type {?}\n */\nRequestOptions.prototype.method;\n/**\n * {\\@link Headers} to be attached to a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.headers;\n/**\n * Body to be used when creating a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.body;\n/**\n * Url with which to perform a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.url;\n/**\n * Search parameters to be included in a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.params;\n/**\n * Enable use credentials for a {\\@link Request}.\n * @type {?}\n */\nRequestOptions.prototype.withCredentials;\n/** @type {?} */\nRequestOptions.prototype.responseType;\n}\n\n/**\n * Subclass of {\\@link RequestOptions}, with default values.\n * \n * Default values:\n *  * method: {\\@link RequestMethod RequestMethod.Get}\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link RequestOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create and send {\\@link Request Requests}.\n * \n * ```typescript\n * import {BaseRequestOptions, RequestOptions} from '\\@angular/http';\n * \n * class MyOptions extends BaseRequestOptions {\n *   search: string = 'coreTeam=true';\n * }\n * \n * {provide: RequestOptions, useClass: MyOptions};\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Request}\n * object.\n * \n * ```\n * import {BaseRequestOptions, Request, RequestMethod} from '\\@angular/http';\n * \n * const options = new BaseRequestOptions();\n * const req = new Request(options.merge({\n *   method: RequestMethod.Post,\n *   url: 'https://google.com'\n * }));\n * console.log('req.method:', RequestMethod[req.method]); // Post\n * console.log('options.url:', options.url); // null\n * console.log('req.url:', req.url); // https://google.com\n * ```\n * \n * \\@experimental\n */\nexport class BaseRequestOptions extends RequestOptions {\nconstructor() { super({method: RequestMethod.Get, headers: new Headers()}); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseRequestOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseRequestOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseRequestOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {ɵgetDOM as getDOM} from '@angular/platform-browser';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\nimport {ResponseOptions} from '../base_response_options';\nimport {ContentType, ReadyState, RequestMethod, ResponseContentType, ResponseType} from '../enums';\nimport {Headers} from '../headers';\nimport {getResponseURL, isSuccess} from '../http_utils';\nimport {Connection, ConnectionBackend, XSRFStrategy} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\nimport {BrowserXhr} from './browser_xhr';\n\nconst /** @type {?} */ XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Creates connections using `XMLHttpRequest`. Given a fully-qualified\n * request, an `XHRConnection` will immediately create an `XMLHttpRequest` object and send the\n * request.\n * \n * This class would typically not be created or interacted with directly inside applications, though\n * the {\\@link MockConnection} may be interacted with in tests.\n * \n * \\@experimental\n */\nexport class XHRConnection implements Connection {\n  request: Request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n */\nresponse: Observable<Response>;\n  readyState: ReadyState;\n/**\n * @param {?} req\n * @param {?} browserXHR\n * @param {?=} baseResponseOptions\n */\nconstructor(req: Request, browserXHR: BrowserXhr, baseResponseOptions?: ResponseOptions) {\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n      const _xhr: XMLHttpRequest = browserXHR.build();\n      _xhr.open(RequestMethod[req.method].toUpperCase(), req.url);\n      if (req.withCredentials != null) {\n        _xhr.withCredentials = req.withCredentials;\n      }\n      // load event handler\n      const onLoad = () => {\n        // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n        let status: number = _xhr.status === 1223 ? 204 : _xhr.status;\n\n        let body: any = null;\n\n        // HTTP 204 means no content\n        if (status !== 204) {\n          // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n          // response/responseType properties were introduced in ResourceLoader Level2 spec\n          // (supported by IE10)\n          body = (typeof _xhr.response === 'undefined') ? _xhr.responseText : _xhr.response;\n\n          // Implicitly strip a potential XSSI prefix.\n          if (typeof body === 'string') {\n            body = body.replace(XSSI_PREFIX, '');\n          }\n        }\n\n        // fix status code when it is 0 (0 status is undocumented).\n        // Occurs when accessing file resources or on Android 4.1 stock browser\n        // while retrieving files from application cache.\n        if (status === 0) {\n          status = body ? 200 : 0;\n        }\n\n        const headers: Headers = Headers.fromResponseHeaderString(_xhr.getAllResponseHeaders());\n        // IE 9 does not provide the way to get URL of response\n        const url = getResponseURL(_xhr) || req.url;\n        const statusText: string = _xhr.statusText || 'OK';\n\n        let responseOptions = new ResponseOptions({body, status, headers, statusText, url});\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        const response = new Response(responseOptions);\n        response.ok = isSuccess(status);\n        if (response.ok) {\n          responseObserver.next(response);\n          // TODO(gdi2290): defer complete if array buffer until done\n          responseObserver.complete();\n          return;\n        }\n        responseObserver.error(response);\n      };\n      // error event handler\n      const onError = (err: ErrorEvent) => {\n        let responseOptions = new ResponseOptions({\n          body: err,\n          type: ResponseType.Error,\n          status: _xhr.status,\n          statusText: _xhr.statusText,\n        });\n        if (baseResponseOptions != null) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      this.setDetectedContentType(req, _xhr);\n\n      if (req.headers == null) {\n        req.headers = new Headers();\n      }\n      if (!req.headers.has('Accept')) {\n        req.headers.append('Accept', 'application/json, text/plain, */*');\n      }\n      req.headers.forEach((values, name) => _xhr.setRequestHeader(name !, values.join(',')));\n\n      // Select the correct buffer type to store the response\n      if (req.responseType != null && _xhr.responseType != null) {\n        switch (req.responseType) {\n          case ResponseContentType.ArrayBuffer:\n            _xhr.responseType = 'arraybuffer';\n            break;\n          case ResponseContentType.Json:\n            _xhr.responseType = 'json';\n            break;\n          case ResponseContentType.Text:\n            _xhr.responseType = 'text';\n            break;\n          case ResponseContentType.Blob:\n            _xhr.responseType = 'blob';\n            break;\n          default:\n            throw new Error('The selected responseType is not supported');\n        }\n      }\n\n      _xhr.addEventListener('load', onLoad);\n      _xhr.addEventListener('error', onError);\n\n      _xhr.send(this.request.getBody());\n\n      return () => {\n        _xhr.removeEventListener('load', onLoad);\n        _xhr.removeEventListener('error', onError);\n        _xhr.abort();\n      };\n    });\n  }\n/**\n * @param {?} req\n * @param {?} _xhr\n * @return {?}\n */\nsetDetectedContentType(req: any /** TODO Request */, _xhr: any /** XMLHttpRequest */) {\n    // Skip if a custom Content-Type header is provided\n    if (req.headers != null && req.headers.get('Content-Type') != null) {\n      return;\n    }\n\n    // Set the detected content type\n    switch (req.contentType) {\n      case ContentType.NONE:\n        break;\n      case ContentType.JSON:\n        _xhr.setRequestHeader('content-type', 'application/json');\n        break;\n      case ContentType.FORM:\n        _xhr.setRequestHeader('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        break;\n      case ContentType.TEXT:\n        _xhr.setRequestHeader('content-type', 'text/plain');\n        break;\n      case ContentType.BLOB:\n        const /** @type {?} */ blob = req.blob();\n        if (blob.type) {\n          _xhr.setRequestHeader('content-type', blob.type);\n        }\n        break;\n    }\n  }\n}\n\nfunction XHRConnection_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRConnection.prototype.request;\n/**\n * Response {\\@link EventEmitter} which emits a single {\\@link Response} value on load event of\n * `XMLHttpRequest`.\n * @type {?}\n */\nXHRConnection.prototype.response;\n/** @type {?} */\nXHRConnection.prototype.readyState;\n}\n\n/**\n * `XSRFConfiguration` sets up Cross Site Request Forgery (XSRF) protection for the application\n * using a cookie. See https://www.owasp.org/index.php/Cross-Site_Request_Forgery_(CSRF)\n * for more information on XSRF.\n * \n * Applications can configure custom cookie and header names by binding an instance of this class\n * with different `cookieName` and `headerName` values. See the main HTTP documentation for more\n * details.\n * \n * \\@experimental\n */\nexport class CookieXSRFStrategy implements XSRFStrategy {\n/**\n * @param {?=} _cookieName\n * @param {?=} _headerName\n */\nconstructor(\nprivate _cookieName: string = 'XSRF-TOKEN',\nprivate _headerName: string = 'X-XSRF-TOKEN') {}\n/**\n * @param {?} req\n * @return {?}\n */\nconfigureRequest(req: Request): void {\n    const /** @type {?} */ xsrfToken = getDOM().getCookie(this._cookieName);\n    if (xsrfToken) {\n      req.headers.set(this._headerName, xsrfToken);\n    }\n  }\n}\n\nfunction CookieXSRFStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nCookieXSRFStrategy.prototype._cookieName;\n/** @type {?} */\nCookieXSRFStrategy.prototype._headerName;\n}\n\n/**\n * Creates {\\@link XHRConnection} instances.\n * \n * This class would typically not be used by end users, but could be\n * overridden if a different backend implementation should be used,\n * such as in a node backend.\n * \n * ### Example\n * \n * ```\n * import {Http, MyNodeBackend, HTTP_PROVIDERS, BaseRequestOptions} from '\\@angular/http';\n * \\@Component({ \n *   viewProviders: [\n *     HTTP_PROVIDERS,\n *     {provide: Http, useFactory: (backend, options) => {\n *       return new Http(backend, options);\n *     }, deps: [MyNodeBackend, BaseRequestOptions]}]\n * })\n * class MyComponent {\n *   constructor(http:Http) {\n *     http.request('people.json').subscribe(res => this.people = res.json());\n *   }\n * }\n * ```\n * \\@experimental\n */\nexport class XHRBackend implements ConnectionBackend {\n/**\n * @param {?} _browserXHR\n * @param {?} _baseResponseOptions\n * @param {?} _xsrfStrategy\n */\nconstructor(\nprivate _browserXHR: BrowserXhr,\nprivate _baseResponseOptions: ResponseOptions,\nprivate _xsrfStrategy: XSRFStrategy) {}\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): XHRConnection {\n    this._xsrfStrategy.configureRequest(request);\n    return new XHRConnection(request, this._browserXHR, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserXhr, },\n{type: ResponseOptions, },\n{type: XSRFStrategy, },\n];\n}\n\nfunction XHRBackend_tsickle_Closure_declarations() {\n/** @type {?} */\nXHRBackend.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nXHRBackend.ctorParameters;\n/** @type {?} */\nXHRBackend.prototype._browserXHR;\n/** @type {?} */\nXHRBackend.prototype._baseResponseOptions;\n/** @type {?} */\nXHRBackend.prototype._xsrfStrategy;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Observer} from 'rxjs/Observer';\n\nimport {ResponseOptions} from '../base_response_options';\nimport {ReadyState, RequestMethod, ResponseType} from '../enums';\nimport {Connection, ConnectionBackend} from '../interfaces';\nimport {Request} from '../static_request';\nimport {Response} from '../static_response';\n\nimport {BrowserJsonp} from './browser_jsonp';\n\nconst /** @type {?} */ JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\nconst /** @type {?} */ JSONP_ERR_WRONG_METHOD = 'JSONP requests must use GET request method.';\n/**\n * Abstract base class for an in-flight JSONP request.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPConnection implements Connection {\n/**\n * The {\\@link ReadyState} of this request.\n */\nreadyState: ReadyState;\n/**\n * The outgoing HTTP request.\n */\nrequest: Request;\n/**\n * An observable that completes with the response, when the request is finished.\n */\nresponse: Observable<Response>;\n/**\n * Callback called when the JSONP request completes, to notify the application\n * of the new data.\n * @abstract\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {}\n}\n\nfunction JSONPConnection_tsickle_Closure_declarations() {\n/**\n * The {\\@link ReadyState} of this request.\n * @type {?}\n */\nJSONPConnection.prototype.readyState;\n/**\n * The outgoing HTTP request.\n * @type {?}\n */\nJSONPConnection.prototype.request;\n/**\n * An observable that completes with the response, when the request is finished.\n * @type {?}\n */\nJSONPConnection.prototype.response;\n}\n\nexport class JSONPConnection_ extends JSONPConnection {\nprivate _id: string;\nprivate _script: Element;\nprivate _responseData: any;\nprivate _finished: boolean = false;\n/**\n * @param {?} req\n * @param {?} _dom\n * @param {?=} baseResponseOptions\n */\nconstructor(\n      req: Request,\nprivate _dom: BrowserJsonp,\nprivate baseResponseOptions?: ResponseOptions) {\n    super();\n    if (req.method !== RequestMethod.Get) {\n      throw new TypeError(JSONP_ERR_WRONG_METHOD);\n    }\n    this.request = req;\n    this.response = new Observable<Response>((responseObserver: Observer<Response>) => {\n\n      this.readyState = ReadyState.Loading;\n      const id = this._id = _dom.nextRequestID();\n\n      _dom.exposeConnection(id, this);\n\n      // Workaround Dart\n      // url = url.replace(/=JSONP_CALLBACK(&|$)/, `generated method`);\n      const callback = _dom.requestCallback(this._id);\n      let url: string = req.url;\n      if (url.indexOf('=JSONP_CALLBACK&') > -1) {\n        url = url.replace('=JSONP_CALLBACK&', `=${callback}&`);\n      } else if (url.lastIndexOf('=JSONP_CALLBACK') === url.length - '=JSONP_CALLBACK'.length) {\n        url = url.substring(0, url.length - '=JSONP_CALLBACK'.length) + `=${callback}`;\n      }\n\n      const script = this._script = _dom.build(url);\n\n      const onLoad = (event: Event) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        if (!this._finished) {\n          let responseOptions =\n              new ResponseOptions({body: JSONP_ERR_NO_CALLBACK, type: ResponseType.Error, url});\n          if (baseResponseOptions) {\n            responseOptions = baseResponseOptions.merge(responseOptions);\n          }\n          responseObserver.error(new Response(responseOptions));\n          return;\n        }\n\n        let responseOptions = new ResponseOptions({body: this._responseData, url});\n        if (this.baseResponseOptions) {\n          responseOptions = this.baseResponseOptions.merge(responseOptions);\n        }\n\n        responseObserver.next(new Response(responseOptions));\n        responseObserver.complete();\n      };\n\n      const onError = (error: Error) => {\n        if (this.readyState === ReadyState.Cancelled) return;\n        this.readyState = ReadyState.Done;\n        _dom.cleanup(script);\n        let responseOptions = new ResponseOptions({body: error.message, type: ResponseType.Error});\n        if (baseResponseOptions) {\n          responseOptions = baseResponseOptions.merge(responseOptions);\n        }\n        responseObserver.error(new Response(responseOptions));\n      };\n\n      script.addEventListener('load', onLoad);\n      script.addEventListener('error', onError);\n\n      _dom.send(script);\n\n      return () => {\n        this.readyState = ReadyState.Cancelled;\n        script.removeEventListener('load', onLoad);\n        script.removeEventListener('error', onError);\n        this._dom.cleanup(script);\n      };\n    });\n  }\n/**\n * @param {?=} data\n * @return {?}\n */\nfinished(data?: any) {\n    // Don't leak connections\n    this._finished = true;\n    this._dom.removeConnection(this._id);\n    if (this.readyState === ReadyState.Cancelled) return;\n    this._responseData = data;\n  }\n}\n\nfunction JSONPConnection__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPConnection_.prototype._id;\n/** @type {?} */\nJSONPConnection_.prototype._script;\n/** @type {?} */\nJSONPConnection_.prototype._responseData;\n/** @type {?} */\nJSONPConnection_.prototype._finished;\n/** @type {?} */\nJSONPConnection_.prototype._dom;\n/** @type {?} */\nJSONPConnection_.prototype.baseResponseOptions;\n}\n\n/**\n * A {\\@link ConnectionBackend} that uses the JSONP strategy of making requests.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class JSONPBackend extends ConnectionBackend {}\nexport class JSONPBackend_ extends JSONPBackend {\n/**\n * @param {?} _browserJSONP\n * @param {?} _baseResponseOptions\n */\nconstructor(private _browserJSONP: BrowserJsonp,\nprivate _baseResponseOptions: ResponseOptions) {\n    super();\n  }\n/**\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: Request): JSONPConnection {\n    return new JSONPConnection_(request, this._browserJSONP, this._baseResponseOptions);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: BrowserJsonp, },\n{type: ResponseOptions, },\n];\n}\n\nfunction JSONPBackend__tsickle_Closure_declarations() {\n/** @type {?} */\nJSONPBackend_.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJSONPBackend_.ctorParameters;\n/** @type {?} */\nJSONPBackend_.prototype._browserJSONP;\n/** @type {?} */\nJSONPBackend_.prototype._baseResponseOptions;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nlet /** @type {?} */ _nextRequestId = 0;\nexport const /** @type {?} */ JSONP_HOME = '__ng_jsonp__';\nlet /** @type {?} */ _jsonpConnections: {[key: string]: any}|null = null;\n/**\n * @return {?}\n */\nfunction _getJsonpConnections(): {[key: string]: any} {\n  const /** @type {?} */ w: {[key: string]: any} = typeof window == 'object' ? window : {};\n  if (_jsonpConnections === null) {\n    _jsonpConnections = w[JSONP_HOME] = {};\n  }\n  return _jsonpConnections;\n}\nexport class BrowserJsonp {\n/**\n * @param {?} url\n * @return {?}\n */\nbuild(url: string): any {\n    const /** @type {?} */ node = document.createElement('script');\n    node.src = url;\n    return node;\n  }\n/**\n * @return {?}\n */\nnextRequestID(): string { return `__req${_nextRequestId++}`; }\n/**\n * @param {?} id\n * @return {?}\n */\nrequestCallback(id: string): string { return `${JSONP_HOME}.${id}.finished`; }\n/**\n * @param {?} id\n * @param {?} connection\n * @return {?}\n */\nexposeConnection(id: string, connection: any) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = connection;\n  }\n/**\n * @param {?} id\n * @return {?}\n */\nremoveConnection(id: string) {\n    const /** @type {?} */ connections = _getJsonpConnections();\n    connections[id] = null;\n  }\n/**\n * @param {?} node\n * @return {?}\n */\nsend(node: any) { document.body.appendChild( /** @type {?} */((<Node>(node)))); }\n/**\n * @param {?} node\n * @return {?}\n */\ncleanup(node: any) {\n    if (node.parentNode) {\n      node.parentNode.removeChild( /** @type {?} */((<Node>(node))));\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserJsonp_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserJsonp.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserJsonp.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n\n\nimport {ResponseOptions} from './base_response_options';\nimport {Body} from './body';\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\n/**\n * Creates `Response` instances from provided values.\n * \n * Though this object isn't\n * usually instantiated by end-users, it is the primary object interacted with when it comes time to\n * add data to a view.\n * \n * ### Example\n * \n * ```\n * http.request('my-friends.txt').subscribe(response => this.friends = response.text());\n * ```\n * \n * The Response's interface is inspired by the Response constructor defined in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#response-class), but is considered a static value whose body\n * can be accessed many times. There are other differences in the implementation, but this is the\n * most significant.\n * \n * \\@experimental\n */\nexport class Response extends Body {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n */\ntype: ResponseType;\n/**\n * True if the response's status is within 200-299\n */\nok: boolean;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n */\nurl: string;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n */\nstatus: number;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n */\nstatusText: string|null;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n */\nbytesLoaded: number;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n */\ntotalBytes: number;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n */\nheaders: Headers|null;\n/**\n * @param {?} responseOptions\n */\nconstructor(responseOptions: ResponseOptions) {\n    super();\n    this._body = responseOptions.body;\n    this.status = responseOptions.status !;\n    this.ok = (this.status >= 200 && this.status <= 299);\n    this.statusText = responseOptions.statusText;\n    this.headers = responseOptions.headers;\n    this.type = responseOptions.type !;\n    this.url = responseOptions.url !;\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    return `Response with status: ${this.status} ${this.statusText} for URL: ${this.url}`;\n  }\n}\n\nfunction Response_tsickle_Closure_declarations() {\n/**\n * One of \"basic\", \"cors\", \"default\", \"error\", or \"opaque\".\n * \n * Defaults to \"default\".\n * @type {?}\n */\nResponse.prototype.type;\n/**\n * True if the response's status is within 200-299\n * @type {?}\n */\nResponse.prototype.ok;\n/**\n * URL of response.\n * \n * Defaults to empty string.\n * @type {?}\n */\nResponse.prototype.url;\n/**\n * Status code returned by server.\n * \n * Defaults to 200.\n * @type {?}\n */\nResponse.prototype.status;\n/**\n * Text representing the corresponding reason phrase to the `status`, as defined in [ietf rfc 2616\n * section 6.1.1](https://tools.ietf.org/html/rfc2616#section-6.1.1)\n * \n * Defaults to \"OK\"\n * @type {?}\n */\nResponse.prototype.statusText;\n/**\n * Non-standard property\n * \n * Denotes how many of the response body's bytes have been loaded, for example if the response is\n * the result of a progress event.\n * @type {?}\n */\nResponse.prototype.bytesLoaded;\n/**\n * Non-standard property\n * \n * Denotes how many bytes are expected in the final response body.\n * @type {?}\n */\nResponse.prototype.totalBytes;\n/**\n * Headers object based on the `Headers` class in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#headers-class).\n * @type {?}\n */\nResponse.prototype.headers;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {stringToArrayBuffer} from './http_utils';\nimport {URLSearchParams} from './url_search_params';\n/**\n * HTTP request body used by both {\\@link Request} and {\\@link Response}\n * https://fetch.spec.whatwg.org/#body\n * @abstract\n */\nexport abstract class Body {\n/**\n * \\@internal\n */\nprotected _body: any;\n/**\n * Attempts to return body as parsed `JSON` object, or raises an exception.\n * @return {?}\n */\njson(): any {\n    if (typeof this._body === 'string') {\n      return JSON.parse( /** @type {?} */((<string>this._body)));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return JSON.parse(this.text());\n    }\n\n    return this._body;\n  }\n/**\n * Returns the body as a string, presuming `toString()` can be called on the response body.\n * \n * When decoding an `ArrayBuffer`, the optional `encodingHint` parameter determines how the\n * bytes in the buffer will be interpreted. Valid values are:\n * \n * - `legacy` - incorrectly interpret the bytes as UTF-16 (technically, UCS-2). Only characters\n *   in the Basic Multilingual Plane are supported, surrogate pairs are not handled correctly.\n *   In addition, the endianness of the 16-bit octet pairs in the `ArrayBuffer` is not taken\n *   into consideration. This is the default behavior to avoid breaking apps, but should be\n *   considered deprecated.\n * \n * - `iso-8859` - interpret the bytes as ISO-8859 (which can be used for ASCII encoded text).\n * @param {?=} encodingHint\n * @return {?}\n */\ntext(encodingHint: 'legacy'|'iso-8859' = 'legacy'): string {\n    if (this._body instanceof URLSearchParams) {\n      return this._body.toString();\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      switch (encodingHint) {\n        case 'legacy':\n          return String.fromCharCode.apply(null, new Uint16Array( /** @type {?} */((this._body as ArrayBuffer))));\n        case 'iso-8859':\n          return String.fromCharCode.apply(null, new Uint8Array( /** @type {?} */((this._body as ArrayBuffer))));\n        default:\n          throw new Error(`Invalid value for encodingHint: ${encodingHint}`);\n      }\n    }\n\n    if (this._body == null) {\n      return '';\n    }\n\n    if (typeof this._body === 'object') {\n      return JSON.stringify(this._body, null, 2);\n    }\n\n    return this._body.toString();\n  }\n/**\n * Return the body as an ArrayBuffer\n * @return {?}\n */\narrayBuffer(): ArrayBuffer {\n    if (this._body instanceof ArrayBuffer) {\n      return /** @type {?} */(( <ArrayBuffer>this._body));\n    }\n\n    return stringToArrayBuffer(this.text());\n  }\n/**\n * Returns the request's body as a Blob, assuming that body exists.\n * @return {?}\n */\nblob(): Blob {\n    if (this._body instanceof Blob) {\n      return /** @type {?} */(( <Blob>this._body));\n    }\n\n    if (this._body instanceof ArrayBuffer) {\n      return new Blob([this._body]);\n    }\n\n    throw new Error('The request body isn\\'t either a blob or an array buffer');\n  }\n}\n\nfunction Body_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nBody.prototype._body;\n}\n\n", "\n/**\n * @license \n * Copyright Google Inc. All Rights Reserved.\n * \n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @param {?=} rawParams\n * @return {?}\n */\nfunction paramParser(rawParams: string = ''): Map<string, string[]> {\n  const /** @type {?} */ map = new Map<string, string[]>();\n  if (rawParams.length > 0) {\n    const /** @type {?} */ params: string[] = rawParams.split('&');\n    params.forEach((param: string) => {\n      const /** @type {?} */ eqIdx = param.indexOf('=');\n      const [key, val]: string[] =\n          eqIdx == -1 ? [param, ''] : [param.slice(0, eqIdx), param.slice(eqIdx + 1)];\n      const /** @type {?} */ list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * \\@experimental \n * \n */\nexport class QueryEncoder {\n/**\n * @param {?} k\n * @return {?}\n */\nencodeKey(k: string): string { return standardEncoding(k); }\n/**\n * @param {?} v\n * @return {?}\n */\nencodeValue(v: string): string { return standardEncoding(v); }\n}\n/**\n * @param {?} v\n * @return {?}\n */\nfunction standardEncoding(v: string): string {\n  return encodeURIComponent(v)\n      .replace(/%40/gi, '@')\n      .replace(/%3A/gi, ':')\n      .replace(/%24/gi, '$')\n      .replace(/%2C/gi, ',')\n      .replace(/%3B/gi, ';')\n      .replace(/%2B/gi, '+')\n      .replace(/%3D/gi, '=')\n      .replace(/%3F/gi, '?')\n      .replace(/%2F/gi, '/');\n}\n/**\n * Map-like representation of url search parameters, based on\n * [URLSearchParams](https://url.spec.whatwg.org/#urlsearchparams) in the url living standard,\n * with several extensions for merging URLSearchParams objects:\n *   - setAll()\n *   - appendAll()\n *   - replaceAll()\n * \n * This class accepts an optional second parameter of ${\\@link QueryEncoder},\n * which is used to serialize parameters before making a request. By default,\n * `QueryEncoder` encodes keys and values of parameters using `encodeURIComponent`,\n * and then un-encodes certain characters that are allowed to be part of the query\n * according to IETF RFC 3986: https://tools.ietf.org/html/rfc3986.\n * \n * These are the characters that are not encoded: `! $ \\' ( ) * + , ; A 9 - . _ ~ ? /`\n * \n * If the set of allowed query characters is not acceptable for a particular backend,\n * `QueryEncoder` can be subclassed and provided as the 2nd argument to URLSearchParams.\n * \n * ```\n * import {URLSearchParams, QueryEncoder} from '\\@angular/http';\n * class MyQueryEncoder extends QueryEncoder {\n *   encodeKey(k: string): string {\n *     return myEncodingFunction(k);\n *   }\n * \n *   encodeValue(v: string): string {\n *     return myEncodingFunction(v);\n *   }\n * }\n * \n * let params = new URLSearchParams('', new MyQueryEncoder());\n * ```\n * \\@experimental\n */\nexport class URLSearchParams {\n  paramsMap: Map<string, string[]>;\n/**\n * @param {?=} rawParams\n * @param {?=} queryEncoder\n */\nconstructor(\npublic rawParams: string = '',\nprivate queryEncoder: QueryEncoder = new QueryEncoder()) {\n    this.paramsMap = paramParser(rawParams);\n  }\n/**\n * @return {?}\n */\nclone(): URLSearchParams {\n    const /** @type {?} */ clone = new URLSearchParams('', this.queryEncoder);\n    clone.appendAll(this);\n    return clone;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\nhas(param: string): boolean { return this.paramsMap.has(param); }\n/**\n * @param {?} param\n * @return {?}\n */\nget(param: string): string|null {\n    const /** @type {?} */ storedParam = this.paramsMap.get(param);\n\n    return Array.isArray(storedParam) ? storedParam[0] : null;\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ngetAll(param: string): string[] { return this.paramsMap.get(param) || []; }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nset(param: string, val: string) {\n    if (val === void 0 || val === null) {\n      this.delete(param);\n      return;\n    }\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.length = 0;\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nsetAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      list.push(value[0]);\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} param\n * @param {?} val\n * @return {?}\n */\nappend(param: string, val: string): void {\n    if (val === void 0 || val === null) return;\n    const /** @type {?} */ list = this.paramsMap.get(param) || [];\n    list.push(val);\n    this.paramsMap.set(param, list);\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nappendAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @param {?} searchParams\n * @return {?}\n */\nreplaceAll(searchParams: URLSearchParams) {\n    searchParams.paramsMap.forEach((value, param) => {\n      const /** @type {?} */ list = this.paramsMap.get(param) || [];\n      list.length = 0;\n      for (let /** @type {?} */ i = 0; i < value.length; ++i) {\n        list.push(value[i]);\n      }\n      this.paramsMap.set(param, list);\n    });\n  }\n/**\n * @return {?}\n */\ntoString(): string {\n    const /** @type {?} */ paramsList: string[] = [];\n    this.paramsMap.forEach((values, k) => {\n      values.forEach(\n          v => paramsList.push(\n              this.queryEncoder.encodeKey(k) + '=' + this.queryEncoder.encodeValue(v)));\n    });\n    return paramsList.join('&');\n  }\n/**\n * @param {?} param\n * @return {?}\n */\ndelete (param: string): void { this.paramsMap.delete(param); }\n}\n\nfunction URLSearchParams_tsickle_Closure_declarations() {\n/** @type {?} */\nURLSearchParams.prototype.paramsMap;\n/** @type {?} */\nURLSearchParams.prototype.rawParams;\n/** @type {?} */\nURLSearchParams.prototype.queryEncoder;\n}\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {RequestMethod} from './enums';\n/**\n * @param {?} method\n * @return {?}\n */\nexport function normalizeMethodName(method: string | RequestMethod): RequestMethod {\n  if (typeof method !== 'string') return method;\n\n  switch (method.toUpperCase()) {\n    case 'GET':\n      return RequestMethod.Get;\n    case 'POST':\n      return RequestMethod.Post;\n    case 'PUT':\n      return RequestMethod.Put;\n    case 'DELETE':\n      return RequestMethod.Delete;\n    case 'OPTIONS':\n      return RequestMethod.Options;\n    case 'HEAD':\n      return RequestMethod.Head;\n    case 'PATCH':\n      return RequestMethod.Patch;\n  }\n  throw new Error(`Invalid request method. The method \"${method}\" is not supported.`);\n}\n\nexport const /** @type {?} */ isSuccess = (status: number): boolean => (status >= 200 && status < 300);\n/**\n * @param {?} xhr\n * @return {?}\n */\nexport function getResponseURL(xhr: any): string|null {\n  if ('responseURL' in xhr) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer8(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint8Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n/**\n * @param {?} input\n * @return {?}\n */\nexport function stringToArrayBuffer(input: String): ArrayBuffer {\n  const /** @type {?} */ view = new Uint16Array(input.length);\n  for (let /** @type {?} */ i = 0, /** @type {?} */ strLen = input.length; i < strLen; i++) {\n    view[i] = input.charCodeAt(i);\n  }\n  return view.buffer;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ReadyState, RequestMethod, ResponseContentType, ResponseType} from './enums';\nimport {Headers} from './headers';\nimport {Request} from './static_request';\nimport {URLSearchParams} from './url_search_params';\n/**\n * Abstract class from which real backends are derived.\n * \n * The primary purpose of a `ConnectionBackend` is to create new connections to fulfill a given\n * {\\@link Request}.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class ConnectionBackend {\n/**\n * @abstract\n * @param {?} request\n * @return {?}\n */\ncreateConnection(request: any) {} }\n/**\n * Abstract class from which real connections are derived.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class Connection {\n  readyState: ReadyState;\n  request: Request;\n  response: any;  // TODO: generic of <Response>;\n}\n\nfunction Connection_tsickle_Closure_declarations() {\n/** @type {?} */\nConnection.prototype.readyState;\n/** @type {?} */\nConnection.prototype.request;\n/** @type {?} */\nConnection.prototype.response;\n}\n\n/**\n * An XSRFStrategy configures XSRF protection (e.g. via headers) on an HTTP request.\n * \n * \\@experimental\n * @abstract\n */\nexport abstract class XSRFStrategy {\n/**\n * @abstract\n * @param {?} req\n * @return {?}\n */\nconfigureRequest(req: Request) {} }\n\n/**\n * Interface for options to construct a RequestOptions, based on\n * [RequestInit](https://fetch.spec.whatwg.org/#requestinit) from the Fetch spec.\n *\n * @experimental\n */\nexport interface RequestOptionsArgs {\n  url?: string|null;\n  method?: string|RequestMethod|null;\n  /** @deprecated from 4.0.0. Use params instead. */\n  search?: string|URLSearchParams|{[key: string]: any | any[]}|null;\n  params?: string|URLSearchParams|{[key: string]: any | any[]}|null;\n  headers?: Headers|null;\n  body?: any;\n  withCredentials?: boolean|null;\n  responseType?: ResponseContentType|null;\n}\n\n/**\n * Required structure when constructing new Request();\n */\nexport interface RequestArgs extends RequestOptionsArgs { url: string|null; }\n\n/**\n * Interface for options to construct a Response, based on\n * [ResponseInit](https://fetch.spec.whatwg.org/#responseinit) from the Fetch spec.\n *\n * @experimental\n */\nexport interface ResponseOptionsArgs {\n  body?: string|Object|FormData|ArrayBuffer|Blob|null;\n  status?: number|null;\n  statusText?: string|null;\n  headers?: Headers|null;\n  type?: ResponseType|null;\n  url?: string|null;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n\nimport {ResponseType} from './enums';\nimport {Headers} from './headers';\nimport {ResponseOptionsArgs} from './interfaces';\n/**\n * Creates a response options object to be optionally provided when instantiating a\n * {\\@link Response}.\n * \n * This class is based on the `ResponseInit` description in the [Fetch\n * Spec](https://fetch.spec.whatwg.org/#responseinit).\n * \n * All values are null by default. Typical defaults can be found in the\n * {\\@link BaseResponseOptions} class, which sub-classes `ResponseOptions`.\n * \n * This class may be used in tests to build {\\@link Response Responses} for\n * mock responses (see {\\@link MockBackend}).\n * \n * ### Example ([live demo](http://plnkr.co/edit/P9Jkk8e8cz6NVzbcxEsD?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: '{\"name\":\"<PERSON>\"}'\n * });\n * var res = new Response(options);\n * \n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * ```\n * \n * \\@experimental\n */\nexport class ResponseOptions {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n */\nbody: string|Object|ArrayBuffer|Blob|null;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n */\nstatus: number|null;\n/**\n * Response {\\@link Headers headers}\n */\nheaders: Headers|null;\n/**\n * \\@internal\n */\nstatusText: string|null;\n/**\n * \\@internal\n */\ntype: ResponseType|null;\n  url: string|null;\n/**\n * @param {?=} opts\n */\nconstructor(opts: ResponseOptionsArgs = {}) {\n    const {body, status, headers, statusText, type, url} = opts;\n    this.body = body != null ? body : null;\n    this.status = status != null ? status : null;\n    this.headers = headers != null ? headers : null;\n    this.statusText = statusText != null ? statusText : null;\n    this.type = type != null ? type : null;\n    this.url = url != null ? url : null;\n  }\n/**\n * Creates a copy of the `ResponseOptions` instance, using the optional input as values to\n * override\n * existing values. This method will not change the values of the instance on which it is being\n * called.\n * \n * This may be useful when sharing a base `ResponseOptions` object inside tests,\n * where certain properties may change from test to test.\n * \n * ### Example ([live demo](http://plnkr.co/edit/1lXquqFfgduTFBWjNoRE?p=preview))\n * \n * ```typescript\n * import {ResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new ResponseOptions({\n *   body: {name: 'Jeff'}\n * });\n * var res = new Response(options.merge({\n *   url: 'https://google.com'\n * }));\n * console.log('options.url:', options.url); // null\n * console.log('res.json():', res.json()); // Object {name: \"Jeff\"}\n * console.log('res.url:', res.url); // https://google.com\n * ```\n * @param {?=} options\n * @return {?}\n */\nmerge(options?: ResponseOptionsArgs): ResponseOptions {\n    return new ResponseOptions({\n      body: options && options.body != null ? options.body : this.body,\n      status: options && options.status != null ? options.status : this.status,\n      headers: options && options.headers != null ? options.headers : this.headers,\n      statusText: options && options.statusText != null ? options.statusText : this.statusText,\n      type: options && options.type != null ? options.type : this.type,\n      url: options && options.url != null ? options.url : this.url,\n    });\n  }\n}\n\nfunction ResponseOptions_tsickle_Closure_declarations() {\n/**\n * String, Object, ArrayBuffer or Blob representing the body of the {\\@link Response}.\n * @type {?}\n */\nResponseOptions.prototype.body;\n/**\n * Http {\\@link http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html status code}\n * associated with the response.\n * @type {?}\n */\nResponseOptions.prototype.status;\n/**\n * Response {\\@link Headers headers}\n * @type {?}\n */\nResponseOptions.prototype.headers;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.statusText;\n/**\n * \\@internal\n * @type {?}\n */\nResponseOptions.prototype.type;\n/** @type {?} */\nResponseOptions.prototype.url;\n}\n\n/**\n * Subclass of {\\@link ResponseOptions}, with default values.\n * \n * Default values:\n *  * status: 200\n *  * headers: empty {\\@link Headers} object\n * \n * This class could be extended and bound to the {\\@link ResponseOptions} class\n * when configuring an {\\@link Injector}, in order to override the default options\n * used by {\\@link Http} to create {\\@link Response Responses}.\n * \n * ### Example ([live demo](http://plnkr.co/edit/qv8DLT?p=preview))\n * \n * ```typescript\n * import {provide} from '\\@angular/core';\n * import {bootstrap} from '\\@angular/platform-browser/browser';\n * import {HTTP_PROVIDERS, Headers, Http, BaseResponseOptions, ResponseOptions} from\n * '\\@angular/http';\n * import {App} from './myapp';\n * \n * class MyOptions extends BaseResponseOptions {\n *   headers:Headers = new Headers({network: 'github'});\n * }\n * \n * bootstrap(App, [HTTP_PROVIDERS, {provide: ResponseOptions, useClass: MyOptions}]);\n * ```\n * \n * The options could also be extended when manually creating a {\\@link Response}\n * object.\n * \n * ### Example ([live demo](http://plnkr.co/edit/VngosOWiaExEtbstDoix?p=preview))\n * \n * ```\n * import {BaseResponseOptions, Response} from '\\@angular/http';\n * \n * var options = new BaseResponseOptions();\n * var res = new Response(options.merge({\n *   body: 'Angular',\n *   headers: new Headers({framework: 'angular'})\n * }));\n * console.log('res.headers.get(\"framework\"):', res.headers.get('framework')); // angular\n * console.log('res.text():', res.text()); // Angular;\n * ```\n * \n * \\@experimental\n */\nexport class BaseResponseOptions extends ResponseOptions {\nconstructor() {\n    super({status: 200, statusText: 'Ok', type: ResponseType.Default, headers: new Headers()});\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BaseResponseOptions_tsickle_Closure_declarations() {\n/** @type {?} */\nBaseResponseOptions.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBaseResponseOptions.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\n/**\n * Polyfill for [Headers](https://developer.mozilla.org/en-US/docs/Web/API/Headers/Headers), as\n * specified in the [Fetch Spec](https://fetch.spec.whatwg.org/#headers-class).\n * \n * The only known difference between this `Headers` implementation and the spec is the\n * lack of an `entries` method.\n * \n * ### Example\n * \n * ```\n * import {Headers} from '\\@angular/http';\n * \n * var firstHeaders = new Headers();\n * firstHeaders.append('Content-Type', 'image/jpeg');\n * console.log(firstHeaders.get('Content-Type')) //'image/jpeg'\n * \n * // Create headers from Plain Old JavaScript Object\n * var secondHeaders = new Headers({\n *   'X-My-Custom-Header': 'Angular'\n * });\n * console.log(secondHeaders.get('X-My-Custom-Header')); //'Angular'\n * \n * var thirdHeaders = new Headers(secondHeaders);\n * console.log(thirdHeaders.get('X-My-Custom-Header')); //'Angular'\n * ```\n * \n * \\@experimental\n */\nexport class Headers {\n/**\n * \\@internal header names are lower case\n */\n_headers: Map<string, string[]> = new Map();\n/**\n * \\@internal map lower case names to actual names\n */\n_normalizedNames: Map<string, string> = new Map();\n/**\n * @param {?=} headers\n */\nconstructor(headers?: Headers|{[name: string]: any}|null) {\n    if (!headers) {\n      return;\n    }\n\n    if (headers instanceof Headers) {\n      headers.forEach((values: string[], name: string) => {\n        values.forEach(value => this.append(name, value));\n      });\n      return;\n    }\n\n    Object.keys(headers).forEach((name: string) => {\n      const values: string[] = Array.isArray(headers[name]) ? headers[name] : [headers[name]];\n      this.delete(name);\n      values.forEach(value => this.append(name, value));\n    });\n  }\n/**\n * Returns a new Headers instance from the given DOMString of Response Headers\n * @param {?} headersString\n * @return {?}\n */\nstatic fromResponseHeaderString(headersString: string): Headers {\n    const /** @type {?} */ headers = new Headers();\n\n    headersString.split('\\n').forEach(line => {\n      const /** @type {?} */ index = line.indexOf(':');\n      if (index > 0) {\n        const /** @type {?} */ name = line.slice(0, index);\n        const /** @type {?} */ value = line.slice(index + 1).trim();\n        headers.set(name, value);\n      }\n    });\n\n    return headers;\n  }\n/**\n * Appends a header to existing list of header values for a given header name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nappend(name: string, value: string): void {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      this.set(name, value);\n    } else {\n      values.push(value);\n    }\n  }\n/**\n * Deletes all header values for the given name.\n * @param {?} name\n * @return {?}\n */\ndelete (name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n    this._normalizedNames.delete(lcName);\n    this._headers.delete(lcName);\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nforEach(fn: (values: string[], name: string|undefined, headers: Map<string, string[]>) => void):\n      void {\n    this._headers.forEach(\n        (values, lcName) => fn(values, this._normalizedNames.get(lcName), this._headers));\n  }\n/**\n * Returns first header that matches given name.\n * @param {?} name\n * @return {?}\n */\nget(name: string): string|null {\n    const /** @type {?} */ values = this.getAll(name);\n\n    if (values === null) {\n      return null;\n    }\n\n    return values.length > 0 ? values[0] : null;\n  }\n/**\n * Checks for existence of header by given name.\n * @param {?} name\n * @return {?}\n */\nhas(name: string): boolean { return this._headers.has(name.toLowerCase()); }\n/**\n * Returns the names of the headers\n * @return {?}\n */\nkeys(): string[] { return Array.from(this._normalizedNames.values()); }\n/**\n * Sets or overrides header value for given name.\n * @param {?} name\n * @param {?} value\n * @return {?}\n */\nset(name: string, value: string|string[]): void {\n    if (Array.isArray(value)) {\n      if (value.length) {\n        this._headers.set(name.toLowerCase(), [value.join(',')]);\n      }\n    } else {\n      this._headers.set(name.toLowerCase(), [value]);\n    }\n    this.mayBeSetNormalizedName(name);\n  }\n/**\n * Returns values of all headers.\n * @return {?}\n */\nvalues(): string[][] { return Array.from(this._headers.values()); }\n/**\n * @return {?}\n */\ntoJSON(): {[name: string]: any} {\n    const /** @type {?} */ serialized: {[name: string]: string[]} = {};\n\n    this._headers.forEach((values: string[], name: string) => {\n      const /** @type {?} */ split: string[] = [];\n      values.forEach(v => split.push(...v.split(',')));\n      serialized[ /** @type {?} */((this._normalizedNames.get(name)))] = split;\n    });\n\n    return serialized;\n  }\n/**\n * Returns list of header values for a given name.\n * @param {?} name\n * @return {?}\n */\ngetAll(name: string): string[]|null {\n    return this.has(name) ? this._headers.get(name.toLowerCase()) || null : null;\n  }\n/**\n * This method is not implemented.\n * @return {?}\n */\nentries() { throw new Error('\"entries\" method is not implemented on Headers class'); }\n/**\n * @param {?} name\n * @return {?}\n */\nprivate mayBeSetNormalizedName(name: string): void {\n    const /** @type {?} */ lcName = name.toLowerCase();\n\n    if (!this._normalizedNames.has(lcName)) {\n      this._normalizedNames.set(lcName, name);\n    }\n  }\n}\n\nfunction Headers_tsickle_Closure_declarations() {\n/**\n * \\@internal header names are lower case\n * @type {?}\n */\nHeaders.prototype._headers;\n/**\n * \\@internal map lower case names to actual names\n * @type {?}\n */\nHeaders.prototype._normalizedNames;\n}\n\n", "\nexport type RequestMethod = number;\nexport let RequestMethod: any = {};\nRequestMethod.Get = 0;\nRequestMethod.Post = 1;\nRequestMethod.Put = 2;\nRequestMethod.Delete = 3;\nRequestMethod.Options = 4;\nRequestMethod.Head = 5;\nRequestMethod.Patch = 6;\nRequestMethod[RequestMethod.Get] = \"Get\";\nRequestMethod[RequestMethod.Post] = \"Post\";\nRequestMethod[RequestMethod.Put] = \"Put\";\nRequestMethod[RequestMethod.Delete] = \"Delete\";\nRequestMethod[RequestMethod.Options] = \"Options\";\nRequestMethod[RequestMethod.Head] = \"Head\";\nRequestMethod[RequestMethod.Patch] = \"Patch\";\n\nexport type ReadyState = number;\nexport let ReadyState: any = {};\nReadyState.Unsent = 0;\nReadyState.Open = 1;\nReadyState.HeadersReceived = 2;\nReadyState.Loading = 3;\nReadyState.Done = 4;\nReadyState.Cancelled = 5;\nReadyState[ReadyState.Unsent] = \"Unsent\";\nReadyState[ReadyState.Open] = \"Open\";\nReadyState[ReadyState.HeadersReceived] = \"HeadersReceived\";\nReadyState[ReadyState.Loading] = \"Loading\";\nReadyState[ReadyState.Done] = \"Done\";\nReadyState[ReadyState.Cancelled] = \"Cancelled\";\n\nexport type ResponseType = number;\nexport let ResponseType: any = {};\nResponseType.Basic = 0;\nResponseType.Cors = 1;\nResponseType.Default = 2;\nResponseType.Error = 3;\nResponseType.Opaque = 4;\nResponseType[ResponseType.Basic] = \"Basic\";\nResponseType[ResponseType.Cors] = \"Cors\";\nResponseType[ResponseType.Default] = \"Default\";\nResponseType[ResponseType.Error] = \"Error\";\nResponseType[ResponseType.Opaque] = \"Opaque\";\n\nexport type ContentType = number;\nexport let ContentType: any = {};\nContentType.NONE = 0;\nContentType.JSON = 1;\nContentType.FORM = 2;\nContentType.FORM_DATA = 3;\nContentType.TEXT = 4;\nContentType.BLOB = 5;\nContentType.ARRAY_BUFFER = 6;\nContentType[ContentType.NONE] = \"NONE\";\nContentType[ContentType.JSON] = \"JSON\";\nContentType[ContentType.FORM] = \"FORM\";\nContentType[ContentType.FORM_DATA] = \"FORM_DATA\";\nContentType[ContentType.TEXT] = \"TEXT\";\nContentType[ContentType.BLOB] = \"BLOB\";\nContentType[ContentType.ARRAY_BUFFER] = \"ARRAY_BUFFER\";\n\nexport type ResponseContentType = number;\nexport let ResponseContentType: any = {};\nResponseContentType.Text = 0;\nResponseContentType.Json = 1;\nResponseContentType.ArrayBuffer = 2;\nResponseContentType.Blob = 3;\nResponseContentType[ResponseContentType.Text] = \"Text\";\nResponseContentType[ResponseContentType.Json] = \"Json\";\nResponseContentType[ResponseContentType.ArrayBuffer] = \"ArrayBuffer\";\nResponseContentType[ResponseContentType.Blob] = \"Blob\";\n\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Injectable} from '@angular/core';\n/**\n * A backend for http that uses the `XMLHttpRequest` browser API.\n * \n * Take care not to evaluate this in non-browser contexts.\n * \n * \\@experimental\n */\nexport class BrowserXhr {\nconstructor() {}\n/**\n * @return {?}\n */\nbuild(): any { return /** @type {?} */(( <any>(new XMLHttpRequest()))); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction BrowserXhr_tsickle_Closure_declarations() {\n/** @type {?} */\nBrowserXhr.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nBrowserXhr.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;GkBAA;;;;;;;;;;;;;;;;;;AAsBA;;;IADA;;OAIA;;;CAHA;;IAGA,EAAA,IAAA,EAAA,UAAA,EAAA;;ADvBO;;GAEP;AACA,UAAA,CAAA,cAAA,GAAA,cAAA,OAAA,EAAA,EAAA,CAAA,CAAA;AAEA,IAAA,aAAA,GAAA,EAAA,CAAA;AACA,aAAa,CAAC,GAAd,GAAA,CAAqB,CAAC;AACtB,aAAa,CAAC,IAAd,GAAA,CAAsB,CAAC;AACvB,aAAa,CAAC,GAAd,GAAA,CAAA,CAAA;AACA,aAAa,CAAC,MAAd,GAAA,CAAA,CAAA;AACA,aAAa,CAAC,OAAd,GAAA,CAAA,CAAA;AACA,aAAa,CAAC,IAAd,GAAA,CAAA,CAAA;AACA,aAAa,CAAC,KAAd,GAAA,CAAA,CAAA;AACA,aAAa,CAAC,aAAa,CAAC,GAA5B,CAAgC,GAAhC,KAAA,CAAA;AACA,aAAa,CAAC,aAAa,CAAC,IAA5B,CAAiC,GAAjC,MAAA,CAAA;AAGA,aAAA,CAAqB,aAArB,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA;AACA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,QAAA,CAAA;AACA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,SAAA,CAAA;AACA,aAAA,CAAA,aAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA;AACA,aAAA,CAAA,aAAA,CAAA,KAAA,CAAA,GAAA,OAAA,CAAA;AACA,IAAA,UAAA,GAAA,EAAmB,CAAC;AACpB,UAAU,CAAC,MAAX,GAAoB,CAApB,CAAA;AACA,UAAU,CAAC,IAAX,GAAA,CAAA,CAAA;AACA,UAAU,CAAC,eAAe,GAA1B,CAA8B,CAA9B;AACA,UAAU,CAAC,OAAX,GAAqB,CAAC,CAAtB;AACA,UAAU,CAAC,IAAX,GAAA,CAAA,CAAA;AACA,UAAU,CAAC,SAAX,GAAA,CAAA,CAAA;AACA,UAAU,CAAC,UAAU,CAAC,MAAtB,CAAA,GAAgC,QAAhC,CAAA;AAGA,UAAA,CAAA,UAAiC,CAAC,IAAlC,CAAA,GAAA,MAAA,CAAA;AACA,UAAA,CAAA,UAAqB,CAAC,eAAtB,CAAA,GAAA,iBAAA,CAAA;AACA,UAAA,CAAA,UAAqB,CAAC,OAAtB,CAAA,GAAA,SAAA,CAAA;AACA,UAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA;AACA,UAAA,CAAA,UAAqB,CAAC,SAAtB,CAAA,GAAA,WAAA,CAAA;AACA,IAAA,YAAA,GAAmB,EAAnB,CAAsB;AACtB,YAAY,CAAC,KAAb,GAAA,CAAA,CAAA;AACA,YAAY,CAAC,IAAb,GAAA,CAAA,CAAA;AACA,YAAY,CAAC,OAAb,GAAA,CAAA,CAAyB;AACzB,YAAY,CAAC,KAAb,GAAA,CAAA,CAAA;AACA,YAAY,CAAC,MAAb,GAAA,CAAA,CAAA;AAGA,YAAA,CAAA,YAAA,CAAA,KAAA,CAAA,GAAA,OAAA,CAAA;AACA,YAAY,CAAZ,YAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA;AACA,YAAY,CAAZ,YAAA,CAAA,OAAA,CAAA,GAAA,SAAA,CAAA;AACA,YAAY,CAAZ,YAAA,CAAA,KAAA,CAAA,GAAA,OAAA,CAAA;AACA,YAAY,CAAZ,YAAyB,CAAC,MAA1B,CAAA,GAAA,QAAA,CAAA;AACA,IAAA,WAAA,GAAA,EAAoB,CAAC;AACrB,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AACrB,WAAW,CAAC,IAAZ,GAAA,CAAA,CAAA;AACA,WAAW,CAAC,IAAZ,GAAA,CAAA,CAAA;AACA,WAAW,CAAC,SAAZ,GAAwB,CAAxB,CAAA;AACA,WAAW,CAAC,IAAZ,GAAA,CAAA,CAAA;AACA,WAAW,CAAC,IAAZ,GAAA,CAAA,CAAA;AACA,WAAW,CAAC,YAAY,GAAxB,CAA4B,CAAC;AAC7B,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,IAAxB,CAAA,GAAA,MAAA,CAAA;AAGA,WAAA,CAAA,WAA8B,CAA9B,IAAwC,CAAC,GAAzC,MAAA,CAAA;AACA,WAAA,CAAA,WAAA,CAAwB,SAAxB,CAAA,GAAA,WAAA,CAAA;AACA,WAAA,CAAA,WAAA,CAAwB,IAAI,CAAC,GAA7B,MAAA,CAAA;AACA,WAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA;AACA,WAAA,CAAA,WAAA,CAAwB,YAAxB,CAAA,GAAA,cAAA,CAAA;AACA,IAAA,mBAAA,GAAA,EAAA,CAAA;AACA,mBAAmB,CAAC,IAApB,GAAA,CAAA,CAAA;AACA,mBAAmB,CAAC,IAApB,GAAA,CAAA,CAAA;AACA,mBAAmB,CAAC,WAApB,GAAA,CAAA,CAAA;;ADvEA,mBAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,GAAA,MAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCA;;;;OARA;;;;;WAIA;QAKI,IAAI,CAAC,QAAT,GAAA,IAAA,GAAA,EAAA,CAAA;QACA;;WAGA;QACA,IAAM,CAAN,gBAAuB,GAAvB,IAAA,GAAA,EAAqD,CAArD;QACA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAc,CAAC;YACf,MAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,OAAA,YAAA,OAAA,CAAA,CAAA,CAAA;YAEA,OAAe,CAAC,OAAO,CAAC,UAAC,MAAzB,EAAiC,IAAjC;gBACA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA,IAA4C,OAAA,KAA5C,CAAA,MAAA,CAAA,IAAA,EAA8D,KAA9D,CAAA,EAA4C,CAA5C,CAAqE,CAAC;YAChE,CAAN,CAAA,CAAA;YACM,MAAM,CAAC;QACb,CAAK;QACL,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,UAAA,IAAA;;;;;;IAMA;;;;OAKA;IACA,gCAAA,GAAA,UAAA,aAAA;QACA,IAAA,gBAAA,CAAA,OAAA,GAAA,IAAA,OAC2B,EAD3B,CAAA;QACA,aAAA,CAAA,KACiB,CAAG,IAAC,CADrB,CAC0B,OAAO,CADjC,UAAA,IAAA;YACA,IAAA,gBAAA,CAAA,KAAA,GAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,IACW,gBADX,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;gBACA,IAAA,gBAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA;;;;;;;;;;;OAYA;IADA,wBAAA,GAAA,UAAA,IAAA,EAAA,KAAA;QAEA,IAAA,gBADwB,CAAC,MACzB,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,CAAA,CAAA;YACA,IAAA,CAAA,GAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;;;;;IAKA,CAAA;IAEA;;;;;;;;IAQA;;;;OAKA;IAEA,qBAAA,GAAA,UAAI,IAAJ;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;;;;;;IAMA;;;;;IAKA,qBAAA,GAAA,UAPG,IAOH,IAAA,MAAA,CAAA,IAAA,CAAA,QAP4C,CAO5C,GAAA,CAAA,IAAA,CAAA,WAAA,EAP8D,CAO9D,CAPoE,CAAE,CAAC;;;;;;;;;;;OAkBvE;IACA,qBAAA,GAAA,UAAA,IAAA,EAAA,KAAA;QATA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YAUM,EAAN,CAAA,CAAU,KAAV,CAAA,MAAA,CATqB,CAAG,CASxB;gBACA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACQ,CATC;QAUT,CAAA;;;;;IAKA,CAAA;;;;OAIA;IACA,wBAAA,GAAA,cAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAPmD,CAAG,MAOtD,EAAA,CAAA,CAAA,CAAA,CAAA;IAEA;;OAEA;IACA,wBAAA,GAAA;QAAA;QACA,IAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,CAAA;QAEI,IAAJ,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA,IAAA;YACA,IAAA,gBAAA,CAAA,KAAA,GAAA,EAAA,CAAA;;;;;;IAMA;;;;;;;IAOA,CAAA;;;;;IANG,yBAAH,GAAG,cAAH,MAAA,IAAG,KAAA,CAAH,sDAAA,CAAA,CAAA,CAAA,CAAA;IAYA;;;OAIA;IACA,wCAAA,GAAA,UAAA,IAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA;;YDpMA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoEA;AACA;IACA;;OAEA;IACA,yBAAA,IAAA;QAAA,qBAAA,EAAA,SAAA;QACA,IAAA,gBAAgB,EAAhB,oBAAwB,EAAxB,sBAAA,EAAmC,4BAAnC,EAAA,gBAAA,EAAA,cAAA,CAAA;QACI,IAAI,CAAC,IAAT,GAAA,IAAA,IAAA,IAAA,GAAA,IAAmC,GAAnC,IAAA,CAAA;QACA,IAAA,CAAA,MAAA,GAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCA;IACA,+BAAA,GAAA,UAAA,OAAA;QACA,MAAA,CAAA,IAAA,eAAA,CAAA;YACM,IAAN,EAAA,OAAA,IAAA,OAF8B,CAE9B,IAAA,IAAA,IAAA,GAAA,OAAA,CAAA,IAF0D,GAE1D,IAAA,CAFgE,IAEhE;YACA,MAAA,EAAA,OAAA,IAAA,OAAA,CAAA,MAAA,IAAA,IAAA,GAAA,OAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA;YACA,OAAA,EAAA,OAAA,IAAA,OAAA,CAAA,OAAA,IAAA,IAAA,GAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA;YACA,UAAA,EAAA,OAAA,IAAA,OAAA,CAAA,UAAA,IAAA,IAAA,GAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA;YAEA,IAAA,EAAA,OAAA,IAAA,OAAA,CAAA,IAAA,IAAA,IAAA,GAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA;;;;;CA/CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGO;IAAP,+CAAA;IAiCA;eACA,kBAAA,EAAA,MAAA,EAAA,GAAA,EAAA,UAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,CAAA,OAAA,EAAA,OAAA,EAAA,IAAA,OAAA,EAAA,EAAA,CAAA;;;CAlCO,CAAP,eAAA;;IAIA,EAAA,IAAA,EAAA,UAAA,EAAA;;ADzKA;;;;;;;;;;;;;;;;;;;;;;;IA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCA;;;;;;;;;;;;;;GDhDA;AACA;;;GAGA;AACA,6BAAA,MAAA;IACA,EAAA,CAAA,CAAI,OAAJ,MAAA,KAAA,QAAA,CAAA;QACA,MAAA,CAAA,MAAA,CAAA;IACA,MAAA,CAAA,CAAA,MAJS,CAIT,WAAA,EAAA,CAAA,CAAA,CAAA;QACA,KAAA,KAAA;YACA,MAAA,CAAA,aAAA,CAAA,GAAA,CAAA;QACA,KAAA,MAJa;YAKb,MAAA,CAAA,aAAA,CAAA,IAAA,CAAA;QACA,KAAA,KAAA;YACA,MAAA,CAJS,aAIT,CAAA,GAAA,CAAA;QACA,KAAA,QAAA;YACA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA;QACA,KAAA,SAAA;YACA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA;QACA,KAAA,MAJY;YAKZ,MAAA,CAAA,aAAA,CAAA,IAAA,CAAA;QAEA,KAAA,OAAA;;;;;AAKA,IAAA,SAAA,GAAA,UAAA,MAAA,IAAA,OAAA,CAAA,MAAA,IAAA,GAAA,IAAA,MAAA,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AACA;;;GAGA;AACA,wBAAA,GAAA;IACA,EAAA,CAAA,CAAA,aAAA,IAAA,GAAA,CAAA,CAAA,CAAA;QACA,MAAA,CAPS,GAOT,CAAA,WAAA,CAAA;IACA,CAAA;;;;;AAKA,CAAA;;;;;AAYA;;;GAGA;AACA,6BAAA,KAAA;IACA,IAAA,gBAAA,CAAA,IAAA,GAAA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;;QDtEA,IAAA,CAAA,CAAA,CAAA,GAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;GAaA;AACA,qBAAA,SAFY;IAEZ,0BAAA,EAAA,cAFY;IAGZ,IAAA,gBAAA,CAFkB,GAElB,GAAA,IAAA,GADmB,EAAE,CACrB;IAEA,EAAA,CAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;QACA,MAAA,CAAS,OAAT,CAFe,UAEf,KAFmB;YAGnB,IAAA,gBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;YACA,IAAA,gFAAA,EAAA,WAAA,EAAA,WAAA,CAAA;YACA,IAAA,gBAAA,CAAA,IAAA,GAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA;YACA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;;;;;AAKA,CAAA;;;;;AAKA;IAAA;IAWA,CAAA;;;;;IANA,gCAAA,GAAA,UAAA,CAVG,IAUH,MAAA,CAAA,gBAAA,CAAA,CAV0C,CAAiB,CAAC,CAAC,CAAC;IAW9D;;;;;IAKA,mBAAA;AAAA,CAAA,AAXA,IAWA;AACA;;;GAGA;AACA,0BAbwB,CAaxB;IACA,MAAA,CAAA,kBAbwB,CAAI,CAa5B,CAAA;SACO,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAcrB,OAbC,CAAO,OAAC,EAAQ,GAAA,CAAI;SAc5B,OAAA,CAAA,OAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;IAfA;;;OAmBA;;;;;;QAIA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAEA;;;;;IAKA,CAAA;;;;;IAKA,6BAzBG,GAyBH,UAzBG,KAAA,IAyBH,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;;;;;IAQA,CAAA;;;;;;IAMA;;;;OAIA;IACA,6BAAA,GAAA,UAAI,KAAJ,EAAA,GAAA;QACI,EAAJ,CAAA,CAAQ,GAAR,KAAA,KAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,CAAA,CAAA;YACQ,IAAR,CAhCU,MAAS,CAgCnB,KAAA,CAAA,CAAA;YACQ,MAAR,CAAA;QACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,gCAAA,GAAA,UAAA,YAAA;QAAA;QACA,YAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA,KAAA;YACA,IAAA,gBAAA,CAAA,IAAA,GAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;;;;;;IAMA;;;;OAGA;IACA,gCAAA,GAAA,UAAA,KAAA,EAAA,GAAA;QACA,EAAA,CAAA,CAAA,GAAA,KAAA,KAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,mCAAO,GAAP,UAAA,YAAA;QAAA,iBAQA;QAPA,YAAA,CAAA,SA7BsB,CAAG,OAAO,CA6BhC,UAAA,KAAA,EAAA,KAAA;YACA,IAAA,gBAAA,CAAA,IAAA,GAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;YACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,EAAA,CAAA,EAAA,CAAA;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,oCAAA,GAAA,UAAA,YAAA;QAAA,iBASA;QARA,YAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA,KAAA;YACM,IAAN,gBAAA,CAAA,IAAA,GAxBgC,KAwBhC,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;YACA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA;YACA,GAAA,CAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,EAAA,CAAA,EAAA,CAAA;;;;QAIA,CAAA,CAAA,CAAA;IACA,CAAA;IACA;;OAIA;IACA,kCAAA,GAAA;QAAA,iBAMA;QALA,IAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,CAAA;;;;;IAKA,CAAA;IACA;;;;;;CAlHA;;;;;;;;ADlFA;;;;;AASA;IAAA;;IACA;;;OAIA;IACA,mBAAA,GAAA;QACA,EAAA,CAAA,CAAA,OAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAAW,IAAX,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;QACA,CAAA;;;;;;;;;;;;;;;;;;;;;OAsBA;IACA,mBAAA,GAAA,UAAA,YADe;QACf,6BAAA,EAAA,uBADe;QAEf,EAAA,CAAA,CAAA,IAAQ,CAAR,KAAA,YAAA,eAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YADwB,WACxB,CADyB,CAAY,CACrC;YACA,MAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;gBACA,KAAA,QAAA;oBACA,MAAA,CAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,WAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBACA,KAAA,UAAA;oBAEA,MADsB,CACtB,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,UAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBACA;oBACA,MAAA,IAAA,KAAA,CAAA,qCAAA,YAAA,CAAA,CAAA;YAEQ,CAAR;QACA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CADW,EACX,CAAA;QACA,CAAA;;;;;IAKA,CAAA;IACA;;;OAIA;IACA,0BAAA,GAAA;;;;;IAKA,CAAA;IACA;;;OAIA;IACA,mBAAA,GAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,IAAA,CAAA,CAAA,CAAA;YAEA,MADc,CACd,CAAA,IADoB,CACpB,KAAA,CAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,WAAA,CAAA,CAAA,CAAA;;QDxGA,CAAA;;;;CCyBA;;;;;;;;;;;;;;;;;;;;;;;;;;;GD6DA;AACA;IAAA,oCAAA;IACA;;OAEA;IACA,kBAAA,eAAA;QAAA,YACI,iBAAJ,SAQA;QAPI,KAAI,CAAC,KAAT,GAAA,eAAgC,CAAhC,IAAuC,CAAvC;QACI,KAAI,CAAC,MAAM,GAAf,eAAA,CAAoC,MAApC,CAAA;QACA,KAAA,CAAA,EAAA,GAAA,CAAA,KAAA,CAAA,MAAA,IAAA,GAAA,IAAA,KAAA,CAAA,MAAA,IAAA,GAAA,CAAA,CAAA;;;;QAIA,KAHG,CAGH,GAAA,GAAA,eAAA,CAAA,GAAA,CAAA;;IACA,CAAA;IACA;;;IDrGA,2BAAA,GAAA;;;;CCuFA,CAAA,IAAA;;;;;;;;;ADtEA,IAAA,UAAA,GAAA,cAAA,CAAA;AACA,IAAE,iBAAF,GAAA,IAHQ,CAGR;AACA;;GAEA;AACA;IACA,IAAA,gBAAA,CAAA,CAAA,GAAA,OAAA,MAAA,IAAA,QAAA,GAAA,MAAA,GAAA,EAAA,CAAA;IACA,EAAA,CAAA,CAAA,iBAAA,KAAA,IAAA,CAAA,CAAA,CAAA;;;;;AAKA;IAAA;;IACA;;;OAGA;;;;QAIA,MAAA,CAAA,IAAA,CAAA;;;;;IAKA,oCAAA,GAAA,cARG,MAQH,CAAA,UAAA,cAAA,EAAA,CAAA,CAAA,CAAA;;;;;;IAMA;;;;;;;;IAQA,CAAA;IACA;;;;;;;IAOA,CAAA;;;;;IAKA,2BAAA,GAAA,UAAA,IAAA,IAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA;;;OAGA;;QAlBA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;YACQ,IAmBR,CAAA,UAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;;;CA/CA;;IA8BA,EAAA,IAAA,EAAA,UAAA,EAAA;;AD3DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgFA;;;;OAzBA;IA8BA,0BAAY,GAAZ,EAAA,IAAA,EAAuB,mBAAmB;QAA1C,YACA,iBAAA,SAyEA;QAxEA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA;QACI,KAAI,CAAC,mBAAT,GAAA,mBAAA,CAAA;QACI,KAAI,CAAC,SAAT,GAAA,KAAA,CAAA;QAEA,EAAA,CAAA,CAAM,GAAN,CAAU,MAAV,KAAqB,aAAa,CAAC,GAAnC,CAAA,CAAA,CAAA;YACM,MAAM,IAAZ,SAAyB,CAAzB,sBAAgD,CAAC,CAAjD;QAEA,CAAA;;;YAIM,KAAN,CAAA,UAAA,GAAA,UAAA,CAAA,OAAA,CAA2C;YACrC,IAAN,EAAA,GAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,aAAA,EAAA,CAAA;YACM,IAAI,CAAV,gBAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAAA;YACA,kBAAA;YACA,iEAAA;YAAA,IAAA,QAAA,GAAA,IAAA,CAAA,eAAA,CAAA,KAAkD,CAAC,GAAnD,CAAA,CAAwD;YACxD,IAAQ,GAAG,GAAG,GAAG,CAAC,GAAlB,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBAEA,GAAA,GAAA,GAAA,CAAA,OAAA,CAAA,kBAA8C,EAA9C,MAAoD,QAApD,MAAA,CAAA,CAAA;YAEM,CAAN;YACA,IAAQ,CAAR,EAAA,CAAY,CAAZ,GAAgB,CAAC,WAAjB,CAAA,iBAAA,CAAA,KAAoD,GAApD,CAAA,MAAA,GAAA,iBAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBAAA,GAAA,GAAA,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,GAAA,CAAA,MAAA,GAAA,iBAAA,CAAA,MAAA,CAAA,IAAA,MAAA,QAAA,CAAA,CAAA;YACA,CAAA;YACA,IAAA,MAAA,GAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;YACA,IAAA,MAAA,GAAA,UAAA,KAAA;gBACA,EAAA,CAAA,CAAU,KAAI,CAAd,UAAA,KAAA,UAAA,CAAA,SAAA,CACiC;oBACvB,MAAV,CAAA;gBACA,KAAA,CAAA,UAAA,GAAA,UAAA,CAAA,IAAA,CAAA;gBACA,IAAA,CAAW,OAAX,CAAA,MAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAU,CAAV,KAAA,CAAA,SAAA,CAA0B,CAAC,CAA3B;oBACU,IAAV,iBAAA,GAAA,IAAA,eAAA,CAAA,EAAA,IAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,YAAA,CAAA,KAAA,EAAA,GAAA,KAAA,EAAA,CAAA,CAAA;oBACA,EAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;wBAEA,iBAAA,GAAkC,mBAAlC,CAAA,KAAA,CAAA,iBAAA,CAA2E,CAA3E;oBACY,CAAZ;oBACU,gBAAV,CAAA,KAAgC,CAAC,IAAjC,QAAA,CAAA,iBAAA,CAAA,CAAA,CAAA;oBACA,MAAA,CAAA;gBAEQ,CAAR;gBACQ,IAAR,eAAA,GAAA,IAAA,eAAA,CAAA,EAAA,IAAA,EAAA,KAAA,CAAA,aAAA,EAAA,GAAA,KAAA,EAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;oBAEA,eAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;gBACQ,CAAR;gBAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA;gBACQ,gBAAR,CAAA,QAAA,EAAA,CAAoC;YACpC,CAAA,CAAA;YACA,IAAA,OAAA,GAAA,UAAA,KAAA;gBACQ,EAAR,CAAA,CAAY,KAAZ,CAAA,UAAA,KAAA,UAAA,CAAA,SAAA,CAAA;oBACU,MAAV,CAAA;gBACA,KAAA,CAAA,UAAA,GAAA,UAAA,CAAA,IAAA,CAAA;gBACQ,IAAR,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;gBACA,IAAA,eAAA,GAAA,IAAA,eAAA,CAAA,EAAA,IAAA,EAAA,KAAA,CAAA,OAAA,EAAA,IAAA,EAAA,YAAA,CAAA,KAAA,EAAA,CAAA,CAAA;gBAEA,EAAY,CAAC,CAAb,mBAAA,CAAA,CAAA,CAAoC;oBACpC,eAA6B,GAA7B,mBAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;gBAEU,CAAC;gBAEX,gBAAA,CAAA,KAAA,CAAA,IAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA;YACA,MAAA,CAAA,gBAAA,CAAA,MAAkC,EAAlC,MAAA,CAA2C,CAA3C;YACA,MAAA,CAAA,gBAAA,CAAA,OAAmC,EAAnC,OAA4C,CAA5C,CAAA;YACA,IAAQ,CAAR,IAAa,CAAb,MAAA,CAAA,CAAA;YACA,MAAA,CAAA;gBACA,KAAA,CAAA,UAAA,GAAA,UAAA,CAAA,SAAA,CAAA;gBACA,MAAA,CAAA,mBAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;;;;;;IAKA,CAAA;;;;OAIA;IA5BA,mCAAkD,GAAlD,UAAA,IAAA;QAqBE,yBApBuB;QA6BzB,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;QACA,IAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;QAEA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,KAAA,UAAA,CAAA,SAAA,CAAA;;;;;;;;;;;;;;;;;;;AA2BA;IAAA,yCAAA;IAEA;;;OACA;;8CAKA;;;;IAAA,CAAA;IACA;;;OAhDA;IAmDA,wCAAA,GAAA,UAAA,OAAA;QACA,MAAA,CAAA,IAAA,gBAAA,CAAA,OAAA,EAAA,IAAA,CAAA,aAAA,EAAA,IAAA,CAAA,oBAAA,CAAA,CAAA;;;CAbA,CAAA,YAAA;;IAnCA,EAAA,IAAA,EAAA,UAAA,EAAA;CAqDA,CAAA;AACA;;;ADtNA,aAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;;;;;;;;;;;;;;IA8CA;;;;OAIA;IACA,uBAAU,GAAG,EAAb,UAAA,EAAA,mBAAA;QAAA;QACA,IAAA,CAAA,OAAY,GAAZ,GAAA,CAAA;QACA,IAAA,CAAO,QAAP,GAAA,IAAA,UAAA,CAAA,UAAA,gBAAA;;YAEM,IAAN,CAAA,IAAA,CAAA,aAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA,WAAA,EAAA,EAAA,GAAA,CAAA,GAAA,CAAA,CAAA;;gBAEQ,IAAI,CAAZ,eAAA,GAAA,GAAA,CAAA,eAA0D,CAA1D;YAEA,CAAA;;YAGA,IAAA,MAAA,GAAA;;;;gBAIA,4BAAA;;oBAGU,mFAAV;oBACA,iFAAA;oBACA,sBAAA;oBACA,IAAA,GAAA,CAAA,OAAA,IAAA,CAAA,QAAA,KAAA,WAAA,CAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,QAAA,CAAA;;;;oBAKY,CAAZ;gBACA,CAAA;gBACA,2DAAA;gBAEQ,uEAAR;;gBAEQ,EAAR,CAAA,CAAA,MAAA,KAAA,CAAA,CAAA,CAAA,CAAA;oBACA,MAAA,GAAA,IAAA,GAAA,GAAuC,GAAvC,CAAA,CAAA;gBAEQ,CAAR;gBACQ,IAAR,OAAA,GAAA,OAA+B,CAA/B,wBAAA,CAAA,IAAA,CAAA,qBAAA,EAAA,CAAA,CAAA;gBACA,uDAAA;gBACA,IAAA,GAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,GAAA,CAAA,GAAA,CAAA;gBACQ,IAAM,UAAd,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,CAAA;gBACQ,IAAR,eAAA,GAAA,IAAA,eAAA,CAAA,EAAA,IAAA,MAAA,EAAA,MAAA,QAAA,EAAA,OAAA,SAAA,EAAA,UAAA,YAAA,EAAA,GAAA,KAAA,EAAA,CAAA,CAAA;gBACQ,EAAR,CAAA,CAAY,mBAAZ,IAAA,IAAA,CAAA,CAAA,CAAA;oBACU,eAAV,GAAA,mBAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;;gBAEA,IAAA,QAAA,GAAA,IAA2B,QAAQ,CAAnC,eAAA,CAAA,CAAA;gBACA,QAAA,CAAA,EAAiB,GAAjB,SAAA,CAAA,MAAA,CAAA,CAAA;gBACA,EAAA,CAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA,CAAA;oBACA,gBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;oBACA,2DAAA;;oBAEA,MAAA,CAAA;gBACQ,CAAR;gBACA,gBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;YACA,CAAA,CAAA;YACA,sBAAA;YACA,IAAA,OAAA,GAAA,UAAA,GAAsB;gBACtB,IAAA,eAAA,GAAA,IAAA,eAAA,CAAA;oBACY,IAAZ,EAAA,GAAA;oBACU,IAAV,EAAA,YAA4B,CAA5B,KAAA;oBACA,MAAA,EAAA,IAAA,CAAA,MAAA;oBACA,UAAA,EAAwB,IAAxB,CAAA,UAAA;iBACA,CAAA,CAAA;gBAEU,EAAV,CAAA,CAAA,mBAAiC,IAAI,IAArC,CAAA,CAA2C,CAAC;oBAE9B,eAAe,GAA7B,mBAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;gBACQ,CAAR;gBACA,gBAAA,CAAA,KAAA,CAAA,IAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA;YACA,KAAQ,CAAR,sBAAA,CAAA,GAAmC,EAAE,IAArC,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA;gBACU,GAAV,CAAA,OAAA,GAAA,IAAA,OAAmC,EAAnC,CAAA;;YAGM,EAAN,CAAA,CAAU,CAAV,GAAc,CAAd,OAAA,CAAA,GAA0B,CAA1B,QAAA,CAAA,CAAA,CAAsC,CAAtC;gBACQ,GAAR,CAAA,OAAmB,CAAC,MAApB,CAAA,QAAA,EAAA,mCAAA,CAAA,CAAA;YACA,CAAA;YACA,GAAA,CAAA,OAAA,CAAY,OAAZ,CAAA,UAAA,MAAA,EAA6B,IAA7B,IAAA,OAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,uDAAA;YACA,EAAA,CAAA,CAAA,GAAA,CAAU,YAAV,IAAA,IAAA,IAAkC,IAAlC,CAAuC,YAAvC,IAAA,IAAA,CAAA,CAAA,CAAA;gBACA,MAAA,CAAA,CAAY,GAAZ,CAAgB,YAAhB,CAA6B,CAA7B,CAAA;oBACA,KAAA,mBAAA,CAAA,WAAA;wBACA,IAAA,CAAA,YAAA,GAAkC,aAAlC,CAAA;wBACY,KAAK,CAAjB;oBACA,KAAA,mBAAA,CAAA,IAAA;wBACA,IAAA,CAAA,YAAA,GAAkC,MAAlC,CAAA;wBACY,KAAK,CAAjB;oBACA,KAAA,mBAAA,CAAA,IAAA;wBACA,IAAA,CAAA,YAAA,GAAA,MAAA,CAAA;wBACY,KAAZ,CAAkB;oBAClB,KAAA,mBAAA,CAAA,IAAA;wBACA,IAAA,CAAA,YAAA,GAAA,MAAA,CAAA;wBAEA,KAAA,CAAA;oBACA;wBAEA,MAAA,IAA4B,KAA5B,CAAA,4CAAA,CAAA,CAAA;gBAEA,CAAA;YACA,CAAA;YACA,IAAQ,CAAR,gBAAA,CAAA,MAAgC,EAAhC,MAAwC,CAAxC,CAA0C;YAC1C,IAAQ,CAAR,gBAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YACA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA,EAAA,CAAA,CAAA;YACA,MAAA,CAAA;gBACA,IAAA,CAAA,mBAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;;;;;;IAMA;;;;OAIA;;QAGI,mDAAJ;QACA,EAAA,CAAA,CAAM,GAAN,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,CAAA,GAAA,CAAA,cAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA;QACA,CAAA;QACA,gCAAA;QACA,MAAA,CAAA,CAAQ,GAAR,CAAA,WAAA,CAAA,CAAA,CAAA;YACM,KATK,WAAA,CAAY,IAAC;gBAUhB,KATK,CASb;YACA,KAAA,WAAA,CAAA,IAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,kBAAA,CAAA,CAAA;gBACQ,KATK,CASb;YACA,KAAA,WAAA,CAAA,IAAA;gBACA,IAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,iDAAA,CAAA,CAAA;gBACQ,KAAR,CAAA;YACA,KAAA,WAAA,CATkB,IASlB;gBACA,IAAU,CAAV,gBAAA,CAAA,cAAA,EAAA,YAAA,CATsD,CAAI;gBAU1D,KAAA,CAAA;YACA,KAAA,WAAA,CAAA,IAAA;gBACA,IAAA,gBAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA;gBACA,EAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;oBACA,IAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;gBAEA,CAAA;;;;;;;;;;;;;;;;;AA6BA;IAzBA;;;;;;;;;IAgCA,CAAA;IACA;;;OAGA;IACA,6CAAA,GAAA,UAAA,GAAA;QACA,IAAA,gBAAA,CAAA,SAAA,GAAA,OAAA,EAAA,CAAA,SAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;QAEA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;CAfA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDA;;;;OAvCA;;;;;IA+CA,CAAA;IACA;;;;IA1CA,qCAAA,GAAA,UAAO,OAAP;QA8CA,IA7CQ,CA6CR,aAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA;QACA,MAAA,CAAA,IAAA,aAAA,CAAA,OAAA,EAAA,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,oBAAA,CAAA,CAAA;;;;;IA3CA,EAAA,IAAA,EAAA,UAAA,EAAA;CAgDA,CAAA;AACA;;GAEA;;IDvSA,EAAA,IAAA,EAAA,UAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;IAoBA;;OAEA;IACA,wBAAY,IAAZ;QAAY,qBAAA,EAAA,SAAZ;QACA,IAAA,oBAAkB,EAAlB,sBAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,oBAAA,EAAgD,oBAAhD,EAAA,sCAAA,EAAA,gCAAA,CAAA;QACI,IAAI,CAAC,MAAT,GAAA,MAAwB,IAAxB,IAAA,GAAA,mBAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA;QACI,IAAI,CAAC,OAAT,GAAA,OAAA,IAAA,IAAA,GAAA,OAA4C,GAAG,IAA/C,CAAA;QACA,IAAA,CAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA;;;;;;;;;;;;QArBA;;;;WAYA;aACA,UAAA,MAAA,IAAA,IAAA,CAAA,MAAA,GAAkC,MAAlC,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsCA;IACA,8BAAA,GAAA,UAAA,OAJW;QAKX,MAAA,CAAA,IAAA,cAAA,CAAA;YACM,MAAN,EAAA,OAAqB,IAArB,OAAA,CAAA,MAAA,IAAA,IAAA,GAAA,OAJ2C,CAI3C,MAAA,GAAA,IAAA,CAAA,MAAA;YACA,OAAA,EAJyE,OAIzE,IAAA,OAAA,CAAA,OAAA,IAAA,IAAA,GAAA,OAAA,CAAA,OAAA,GAAA,IAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;YACM,IAAN,EAAA,OAAA,IAAA,OAAA,CAJ+B,IAI/B,IAJuC,IAIvC,GAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAJsE;YAKtE,GAAA,EAAA,OAAA,IAAA,OAAA,CAAA,GAAA,IAAA,IAAA,GAAA,OAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA;YACA,MAAA,EAAA,OAAA,IAAA,IAAA,CAAA,kBAAA,CAAA,OAAA,CAAA,MAAA,IAAA,OAAA,CAAA,MAAA,CAAA;YACA,eAAA,EAAA,OAAA,IAAA,OAAA,CAAA,eAAA,IAAA,IAAA,GAAA,OAAA,CAAA,eAAA;;;;;IAFG,CAAH;IASA;;;OAGA;IACA,2CAAA,GAAA,UAAA,MAAA;QAEI,EAAJ,CAAA,CAAQ,CAAR,MAPe,CAOf;YACM,MAAN,CAPa,IAAI,CAOjB,MAAA,CAAA;QACA,EAAA,CAAA,CAAA,MAAA,YAAA,eAAA,CAAA,CAAA,CAAA;YAEA,MAAA,CAPW,MAOX,CAAA,KAAA,EAP6B,CAO7B;QACA,CAAA;;;;;IALG,CAAH;IAWA;;;OAGA;IACA,qCAAQ,GAAR,UAAA,SAVuB;QAUvB;QAAA,0BAAA,EAAA,cAVuB;QAWvB,IAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,eAAA,EAAA,CAAA;QAVA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA;YAWA,IAAA,gBAV0B,CAU1B,KAAA,GAAA,SAVsC,CAAO,GAU7C,CAAA,CAAA;YACA,EAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;gBACA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,IAAA,OAAA,KAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACA,CAAA;YACA,IAAA,CAAA,CAAA;;;;;;;;;;;OAWA;IACA,qCAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAA,MAAA;QACA,EAAA,CAAA,CAAA,OAAA,KAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YAEA,KAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;;;;;CAlHA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgMA;AACA;IAAA,8CAAA;;;;;CAAA,CAAA,cAAA;;IAhDA,EAAA,IAAA,EAAA,UAAA,EAAA;;ADnNA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkFA;AACA;IAAA,mCAAA;;;OAGA;IACA,iBAAA,cAAA;QAAA,YACI,iBAAJ,SA6BA;QA5BA,mCAAA;QACA,IAAA,GAAA,GAAA,cAAA,CAAA,GAAA,CAAA;QACA,KAAA,CAAA,GAAQ,GAAR,cAAA,CAAA,GAAA,CAAA;QACA,IAAA,SAAA,GAAA,cAAA,CAAA,MAAA,IAAA,cAAA,CAAA,MAAA,CAAA;QAAA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;YACA,IAAQ,MAAM,SAAA,CAAd;YACA,EAAA,CAAA,CAAA,OAAA,SAAA,KAAA,QAAA,IAAA,CAAA,CAAA,SAAA,YAAA,eAAA,CAAA,CAAA,CAAA,CAAA;gBACU,MAAM,GAAhB,eAAA,CAAA,SAAA,CAAA,CAAA,QAAA,EAAA,CAAA;YACA,CAAA;YACA,IAAQ,CAAR,CAAA;gBACA,MAAA,GAAA,SAAwB,CAAC,QAAQ,EAAjC,CAAA;YACA,CAAA;;gBAEQ,IAAI,MAAZ,GAAA,GAAA,CAAyB;gBACzB,EAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACA,MAAA,GAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA,IAAA,GAAA,CAAA,GAAA,EAAA,GAAA,GAAA,CAAA;gBACA,CAAA;gBACA,wDAAA;;;QAGI,CAAJ;QACI,KAAI,CAAC,KAAT,GAAA,cAAA,CAAA,IAAA,CAAA;QACI,KAAI,CAAC,MAAT,GAAA,mBAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA;QACI,uCAAJ;QACA,8CAAA;;;;;;IAKA,CAAA;IACA;;;OAGA;IACA,mCAAA,GAAA;QACA,MAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA;YACA,KAAA,kBAT2B;gBAU3B,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;YACM,KATK,mCASX;gBACQ,MAAR,CATe,WAAA,CAAY,IAAC,CAAI;YAU1B,KATK,qBASX;gBACQ,MAAR,CATe,WASf,CAAA,SAAA,CATqCA;YAU/B,KAAN,YAAA,CAAA;YACA,KAAA,WAToB;gBAUpB,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;YACA,KAAA,0BAAA;;;;;IAKA,CAAA;IACA;;;OAPA;IAUA,2CAAA,GAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA;YATA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;QAUA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,eAAA,CAAA,CAAA,CAAA;YATA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;QAUA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,QAAA,CAAA,CAAA,CAAA;YATA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA;QAUA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,MAAA,CAAA,CAAA,CAAA;YATA,MAAA,CAAA,WAAA,CAA6B,IAA7B,CAAA;QAUA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,YAAA,aAAA,CAAA,CAAA,CAAA;YATA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA;QAUA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,IAAA,OAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;;;;;;IAMA;;;;OAIA;IACA,yBAAA,GAAA;QACA,MAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA;YACA,KAAA,WAToB,CASpB,IATqB;gBAUrB,MAAA,CAAA,IAAA,CATW,IASX,EAAA,CAAA;YACA,KAAA,WAToB,CASpB,IAAA;gBACA,MAAA,CAAA,IAAA,CATW,IASX,EAAA,CAAA;YACA,KAAA,WAToB,CASpB,SAAA;gBACA,MAAA,CAAA,IAAA,CATW,KASX,CAAA;YACA,KAAA,WAToB,CASpB,IAAA;gBACA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;YACA,KAAA,WAToB,CASpB,IAAA;gBACA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;YACA,KAAA,WAAA,CAAA,YAAA;gBACA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA;YAEA;;;;IAqCA,cAAA;AAAA,CAAA,AAtIA,CAAA,IAAA,GAsIA;AACA;;;GAGA;AACA,yBA9Ca,MA8Cb;IACA,IAAA,gBAAA,CAAA,YAAA,GAAA,IAAA,eAAA,EAAA,CAAA;IA9CA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA;QA+CA,IAAA,gBAAA,CA9CoB,KA8CpB,GAAA,MAAA,CAAA,GAAA,CAAA,CA9CsC;QA+CtC,EAAA,CAAA,CAAA,KAAA,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;YACA,KAAA,CAAA,OAAA,CAAA,UAAA,OAAA,IAAA,OAAA,YAAA,CAAA,MAAA,CAAA,GAAA,EAAA,OAAA,CAAA,QAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,CAAA;YAEA,YAAA,CAAA,MAAA,CAAA,GAAA,EAAA,KAAA,CAAA,QAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,CA9CM,CA8CN;IACA,MAAA,CAAA,YAAA,CAAA;AACA,CAAA;;AD1OA,IAAA,CAAA,GAAA,OAAA,MAAA,IAAA,QAAA,GAAA,MAAA,GAAA,IAAA,CAAA;;;;;;;;;;;AAsBA;;;;;;;;;;;;;;GAgBA;AACA,sBAAA,WAX2B,EAW3B,YAAA,EAXqC,MAWrC,EAAA,GAAA;IACA,IAAA,gBAAA,CAAA,UAX+B,GAAA,WAW/B,CAAA;IACA,EAAA,CAAA,CAAA,YAXc,CAWd,CAAA,CAAA;QACA,yCAAA;QACA,MAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAXe,IAWf,cAAA,CAAA;YACM,MAXM,EAWZ,YAAA,CAAA,MAAA,IAAA,MAAA;YACM,GAAN,EAAA,YAXuB,CAWvB,GAAA,IAAA,GAAA;YACM,MAAN,EAAA,YAAA,CAAA,MAXiC;YAYjC,MAAA,EAAA,YAAA,CAAA,MAAA;YACA,OAAA,EAAA,YAAA,CAAA,OAAA;YATS,IAWT,EAAA,YAAA,CAAA,IAAA;YACA,eAAA,EAAA,YAAA,CAAA,eAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;IAbA;;;;;;;;;;;;;;;;OA6BA;IAfA,sBAAA,GAAA,UAAA,GAAA,EAAW,OAAX;QAgBA,IAAM,gBAAN,CAAA,kBAAA,CAAA;QACA,EAAA,CAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,CAAA,CAAA;YAfA,kBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,QAAA,EAAA,IAAA,OAAA,CAAA,YAAA,CAAA,IAAA,CAAA,eAAA,EAAA,OAAA,EAAA,aAAA,CAAA,GAAA,EAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAgBA,CAAA;QACA,IAAA,CAAA,EAAA,CAAA,CAAA,GAAA,YAAA,OAAA,CAAA,CAAA,CAAA;YACA,kBAAA,GAfW,WAeX,CAAA,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,CAAA;QACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6CA;IAiCA,sBAAA,GAAA,UAhCQ,GAgCR,EAAA,OAhCmB;QAiCnB,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,OAAA,CAAA,YAAA,CAAA,IAAA,CAAA,eAAA,EAAA,OAAA,EAAA,aAAA,CAAA,OAAA,EAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;;CArGA;;IAuEA,EAAA,IAAA,EAAA,UAAC,EAAD;CAmCA,CAAA;AACA;;GAIA;;;IAiBA,EAAA,IAAA,EAAA,cAAA,GAAA;;;;;AAKA;IAAA,iCAAA;IACA;;;;;;;;;;;;;;;;;;;;;;;OAuBA;IACA,uBAAA,GAAA,UAAA,GAAA,EAAA,OAAA;QACI,IAAI,gBAAR,CAAA,kBAAA,CAAA;QACA,EAAA,CAAA,CAAM,OApDK,GAoDX,KAAA,QAAA,CAAA,CAAA,CAAA;YACA,GAAA;gBACA,IAAA,OAAA,CAAA,YAAA,CAAA,IAAA,CAAA,eAAA,EAAA,OAAA,EAAA,aAAA,CAAA,GAAA,EAAA,gBAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,EAAA,CAAA,CAAA,GAAA,YAAA,OAAA,CAAA,CAAA,CAAA;YApDA,EAAA,CAAA,CAAA,GAAA,CAAA,MAAA,KAAA,aAAA,CAAA,GAAA,CAAA,CAAA,CAAA;gBAqDA,MApDgB,IAoDhB,KAAA,CAAA,6CAAA,CAAA,CAAA;YACA,CAAA;YACA,kBAAA,GApDW,WAoDX,CAAA,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,CAAA;QACA,CAAA;;YAnDA,MAAA,IAAA,KAAA,CAAA,0DAAA,CAAA,CAAA;QAqDA,CAAA;QACA,MAAA,CAAA,kBAAA,CAAA;;;CAtCA,CAAA,IAAA;;IAZA,EAAA,IAAA,EAAA,UAAA,EAAA;CAuDA,CAAA;AACA;;;ADtSA,KAAA,CAAA,cAAA,GAAA,cAAA,OAAA;;;GAAA;;;;;;;;;;;;;GA6BA;AACA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;GAGA;;;;;;AAGA,UAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAA,QAAI,EAAJ,IAAA,EAAA,CAAA;gBACA,SAAA,EAAA;oBACI,kEAAJ;oBACI,wDAAJ;oBACI,EAAC,OAPC,EAAQ,IAOd,EAAA,UAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,UAAA,EAAA,cAAA,CAAA,EAAA;oBACA,UAAA;oBACA,EAAA,OAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA;oBACA,EAAA,OAAA,EAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA;;;;aALA,EAAA,EAAA;CAaA,CAAA;;;;;AAeA;;;;GAGA;;;;;;AAGA,WAAA,CAAA,UAAK,GAAL;IACA,EAAA,IAAA,EAAA,QAAI,EAAJ,IAAA,EAAA,CAAA;gBACA,SAAA,EAAA;oBACI,kEAAJ;oBACI,wDAAJ;oBACA,EAAA,OAAA,EAAA,KAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,CAAA,YAAA,EAAA,cAAA,CAAA,EAAA;oBACA,YAAA;oBACA,EAAA,OAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA;;;;aAhBA,EAAA,EAAA;;ADtFA;;;;;;;;;;;AAeA;;;;;ADfA;;;;;;;;;;;;;;;;;;ADaA;;;;;;;;;;"}