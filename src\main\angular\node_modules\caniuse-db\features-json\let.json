{"title": "let", "description": "Declares a variable with block level scope", "spec": "http://www.ecma-international.org/ecma-262/6.0/#sec-let-and-const-declarations", "status": "other", "links": [{"url": "http://generatedcontent.org/post/54444832868/variables-and-constants-in-es6", "title": "Variables and Constants in ES6"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/let", "title": "Mozilla Developer Network (MDN) documentation - let"}], "bugs": [{"description": "[Mozilla bug for supporting the standard version of let](https://bugzilla.mozilla.org/show_bug.cgi?id=932517)"}], "categories": ["JS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y"}, "firefox": {"2": "n d #1", "3": "n d #1", "3.5": "n d #1", "3.6": "n d #1", "4": "n d #1", "5": "n d #1", "6": "n d #1", "7": "n d #1", "8": "n d #1", "9": "n d #1", "10": "n d #1", "11": "n d #1", "12": "n d #1", "13": "n d #1", "14": "n d #1", "15": "n d #1", "16": "n d #1", "17": "n d #1", "18": "n d #1", "19": "n d #1", "20": "n d #1", "21": "n d #1", "22": "n d #1", "23": "n d #1", "24": "n d #1", "25": "n d #1", "26": "n d #1", "27": "n d #1", "28": "n d #1", "29": "n d #1", "30": "n d #1", "31": "n d #1", "32": "n d #1", "33": "n d #1", "34": "n d #1", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n d #2", "20": "n d #2", "21": "n d #2", "22": "n d #2", "23": "n d #2", "24": "n d #2", "25": "n d #2", "26": "n d #2", "27": "n d #2", "28": "n d #2", "29": "n d #2", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "n d #2", "35": "n d #2", "36": "n d #2", "37": "n d #2", "38": "n d #2", "39": "n d #2", "40": "n d #2", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #3", "46": "a #3", "47": "a #3", "48": "a #3", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n d #2", "16": "n d #2", "17": "n d #2", "18": "n d #2", "19": "n d #2", "20": "n d #2", "21": "n d #2", "22": "n d #2", "23": "n d #2", "24": "n d #2", "25": "n d #2", "26": "n d #2", "27": "n d #2", "28": "a #3", "29": "a #3", "30": "a #3", "31": "a #3", "32": "a #3", "33": "a #3", "34": "a #3", "35": "a #3", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "a #3", "5": "y"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "a #3"}}, "notes": "", "notes_by_num": {"1": "Supports a non-standard version that can only be used in script elements with a type attribute of `application/javascript;version=1.7`. As other browsers do not support these types of `script` tags this makes support useless for cross-browser support.", "2": "Requires the ‘Experimental Javascript features’ flag to be enabled", "3": "Only supported in strict mode"}, "usage_perc_y": 75.91, "usage_perc_a": 4.62, "ucprefix": false, "parent": "", "keywords": "ES6,variable,block,scope", "ie_id": "", "chrome_id": "4645595339816960", "firefox_id": "", "webkit_id": "", "shown": true}