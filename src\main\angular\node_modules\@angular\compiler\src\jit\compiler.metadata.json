[{"__symbolic": "module", "version": 3, "metadata": {"JitCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}, {"__symbolic": "reference", "module": "../template_parser/template_parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../style_compiler", "name": "StyleCompiler"}, {"__symbolic": "reference", "module": "../view_compiler/view_compiler", "name": "ViewCompiler"}, {"__symbolic": "reference", "module": "../ng_module_compiler", "name": "NgModuleCompiler"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "Type", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}, {"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "compileModuleSync": [{"__symbolic": "method"}], "compileModuleAsync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsSync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsAsync": [{"__symbolic": "method"}], "getNgContentSelectors": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "hasAotSummary": [{"__symbolic": "method"}], "_filterJitIdentifiers": [{"__symbolic": "method"}], "_compileModuleAndComponents": [{"__symbolic": "method"}], "_compileModuleAndAllComponents": [{"__symbolic": "method"}], "_loadModules": [{"__symbolic": "method"}], "_compileModule": [{"__symbolic": "method"}], "_compileComponents": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "_createCompiledHostTemplate": [{"__symbolic": "method"}], "_createCompiledTemplate": [{"__symbolic": "method"}], "_compileTemplate": [{"__symbolic": "method"}], "_resolveStylesCompileResult": [{"__symbolic": "method"}], "_resolveAndEvalStylesCompileResult": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"JitCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "../metadata_resolver", "name": "CompileMetadataResolver"}, {"__symbolic": "reference", "module": "../template_parser/template_parser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "../style_compiler", "name": "StyleCompiler"}, {"__symbolic": "reference", "module": "../view_compiler/view_compiler", "name": "ViewCompiler"}, {"__symbolic": "reference", "module": "../ng_module_compiler", "name": "NgModuleCompiler"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "Type", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}, {"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ɵConsole"}]}], "compileModuleSync": [{"__symbolic": "method"}], "compileModuleAsync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsSync": [{"__symbolic": "method"}], "compileModuleAndAllComponentsAsync": [{"__symbolic": "method"}], "getNgContentSelectors": [{"__symbolic": "method"}], "getComponentFactory": [{"__symbolic": "method"}], "loadAotSummaries": [{"__symbolic": "method"}], "hasAotSummary": [{"__symbolic": "method"}], "_filterJitIdentifiers": [{"__symbolic": "method"}], "_compileModuleAndComponents": [{"__symbolic": "method"}], "_compileModuleAndAllComponents": [{"__symbolic": "method"}], "_loadModules": [{"__symbolic": "method"}], "_compileModule": [{"__symbolic": "method"}], "_compileComponents": [{"__symbolic": "method"}], "clearCacheFor": [{"__symbolic": "method"}], "clearCache": [{"__symbolic": "method"}], "_createCompiledHostTemplate": [{"__symbolic": "method"}], "_createCompiledTemplate": [{"__symbolic": "method"}], "_compileTemplate": [{"__symbolic": "method"}], "_resolveStylesCompileResult": [{"__symbolic": "method"}], "_resolveAndEvalStylesCompileResult": [{"__symbolic": "method"}]}}}}]