{"version": 3, "file": "expression_type.js", "sourceRoot": "", "sources": ["../../../../../packages/compiler-cli/src/diagnostics/expression_type.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,8CAA2U;AAE3U,qCAAyF;AAIzF,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,qDAAK,CAAA;IACL,yDAAO,CAAA;AACT,CAAC,EAHW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAGzB;AAED;IACE,wBAAmB,IAAoB,EAAS,OAAe,EAAS,GAAQ;QAA7D,SAAI,GAAJ,IAAI,CAAgB;QAAS,YAAO,GAAP,OAAO,CAAQ;QAAS,QAAG,GAAH,GAAG,CAAK;IAAG,CAAC;IACtF,qBAAC;AAAD,CAAC,AAFD,IAEC;AAFY,wCAAc;AAI3B,sDAAsD;AACtD;IAGE,iBACY,KAAkB,EAAU,KAAkB,EAC9C,OAAqC;QADrC,UAAK,GAAL,KAAK,CAAa;QAAU,UAAK,GAAL,KAAK,CAAa;QAC9C,YAAO,GAAP,OAAO,CAA8B;IAAG,CAAC;IAErD,yBAAO,GAAP,UAAQ,GAAQ,IAAY,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAErD,gCAAc,GAAd,UAAe,GAAQ;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAM,IAAI,GAAW,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,wDAAwD,EAAE,GAAG,CAAC,CAAC;QACpF,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,6BAAW,GAAX,UAAY,GAAW;QAAvB,iBAsIC;QArIC,qCAAqC;QACrC,mBAAmB,IAAiB,EAAE,KAAkB;YACtD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACb,KAAK,qBAAW,CAAC,SAAS,CAAC;gBAC3B,KAAK,qBAAW,CAAC,IAAI;oBACnB,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,qBAAW,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC;YACD,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAED,IAAM,OAAO,GAAG,UAAC,GAAQ,EAAE,SAAiB;YAC1C,IAAM,IAAI,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC/B,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClB,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBAClB,KAAK,IAAI,CAAC;oBACV,KAAK,IAAI,CAAC;oBACV,KAAK,IAAI,CAAC;oBACV,KAAK,IAAI,CAAC;oBACV,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK;wBACR,oBAAoB;wBACpB,KAAK,CAAC;oBACR;wBACE,KAAI,CAAC,WAAW,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;wBACtD,KAAK,CAAC;gBACV,CAAC;gBACD,MAAM,CAAC,KAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC7C,CAAC;YACD,MAAM,CAAC,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;QAClD,IAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvD,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACtD,IAAM,SAAS,GAAG,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAEvD,iEAAiE;QACjE,0DAA0D;QAC1D,wEAAwE;QACxE,IAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,GAAG,SAAS,CAAC;QAC3C,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YACtB,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,IAAI,CAAC;YACV,KAAK,IAAI,CAAC;YACV,KAAK,KAAK,CAAC;YACX,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG;gBACN,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjB,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC5C,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC/C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAC/C,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM;wBAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,MAAM,CAAC,CAAC;oBACvD;wBACE,IAAI,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;wBACxB,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;4BACjB,KAAK,qBAAW,CAAC,GAAG,CAAC;4BACrB,KAAK,qBAAW,CAAC,MAAM;gCACrB,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;gCACrB,KAAK,CAAC;wBACV,CAAC;wBACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;gBACjE,CAAC;YACH,KAAK,GAAG;gBACN,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjB,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC5C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,OAAO,CAAC;oBAChD,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAC/C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,KAAK,CAAC;oBAC9C,KAAK,qBAAW,CAAC,OAAO,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAChD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC/C,KAAK,qBAAW,CAAC,KAAK,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG;wBAC3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;oBACtB,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAC/C,KAAK,qBAAW,CAAC,OAAO,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBACnD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAClD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC/C,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,OAAO,CAAC;oBACnD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAClD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAClD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,KAAK,CAAC;oBACjD,KAAK,qBAAW,CAAC,KAAK,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM;wBAC9C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,MAAM,CAAC,CAAC;oBACvD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM;wBAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,MAAM,CAAC,CAAC;oBACvD,KAAK,qBAAW,CAAC,OAAO,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBACnD,KAAK,qBAAW,CAAC,KAAK,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM;wBAC9C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9D,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,OAAO,CAAC;oBACnD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,KAAK;wBAC9C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC/D;wBACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,iDAAiD,EAAE,GAAG,CAAC,CAAC;gBACpF,CAAC;YACH,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,IAAI,CAAC;YACV,KAAK,IAAI,CAAC;YACV,KAAK,IAAI,CAAC;YACV,KAAK,IAAI,CAAC;YACV,KAAK,KAAK,CAAC;YACX,KAAK,KAAK;gBACR,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjB,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC5C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,OAAO,CAAC;oBAChD,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAC/C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAC/C,KAAK,qBAAW,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAW,CAAC,KAAK,CAAC;oBAC9C,KAAK,qBAAW,CAAC,OAAO,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAChD,KAAK,qBAAW,CAAC,OAAO,IAAI,CAAC,GAAG,qBAAW,CAAC,OAAO,CAAC;oBACpD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC/C,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAClD,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC/C,KAAK,qBAAW,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAW,CAAC,MAAM,CAAC;oBAClD,KAAK,qBAAW,CAAC,KAAK,IAAI,CAAC,GAAG,qBAAW,CAAC,GAAG,CAAC;oBAC9C,KAAK,qBAAW,CAAC,KAAK,IAAI,CAAC,GAAG,qBAAW,CAAC,KAAK;wBAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,OAAO,CAAC,CAAC;oBACxD;wBACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;gBACvF,CAAC;YACH,KAAK,IAAI;gBACP,MAAM,CAAC,SAAS,CAAC;YACnB,KAAK,IAAI;gBACP,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,2BAAyB,GAAG,CAAC,SAAW,EAAE,GAAG,CAAC,CAAC;IACzE,CAAC;IAED,4BAAU,GAAV,UAAW,GAAU;QACnB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,sDAAsD;YACtD,2BAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,2CAA2C;QAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,SAAS,CAAC,CAAC;IAC1D,CAAC;IAED,kCAAgB,GAAhB,UAAiB,GAAgB;QAC/B,2EAA2E;QAC3E,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,2BAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxF,CAAC;IAED,mCAAiB,GAAjB,UAAkB,GAAiB;QAAnC,iBAYC;QAXC,4EAA4E;QAC5E,iFAAiF;QACjF,8EAA8E;QAC9E,WAAW;QACX,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAjB,CAAiB,CAAC,CAAC;QACpD,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAQ,CAAC,CAAC;QAC1C,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QAC7F,IAAM,SAAS,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,EAAE,CAAC,CAAC,SAAS,CAAC;YAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;QACvC,8CAA8C;QAC9C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC;IAChF,CAAC;IAED,uCAAqB,GAArB,UAAsB,GAAqB;QACzC,IAAM,KAAK,GAAG,IAAI,CAAC;QACnB,oDAAoD;QACpD,kEAAkE;QAClE,gCAAgC;QAChC,MAAM,CAAC;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,SAAS;YACrB,OAAO,EAAP,cAAuB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA,CAAC;YAC3C,UAAU,EAAV,cAA0B,MAAM,CAAC,EAAE,CAAC,CAAA,CAAC;YACrC,eAAe,EAAf,UAAgB,KAAK,IAAyB,MAAM,CAAC,SAAS,CAAC,CAAA,CAAC;YAChE,OAAO,EAAP,UAAQ,QAAQ,IAAsB,MAAM,CAAC,SAAS,CAAC,CAAA,CAAC;SACzD,CAAC;IACJ,CAAC;IAED,oCAAkB,GAAlB,UAAmB,GAAkB;QACnC,uDAAuD;QACvD,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,2BAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,gCAAc,GAAd,UAAe,GAAc;QAC3B,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;IAChC,CAAC;IAED,iCAAe,GAAf,UAAgB,GAAe;QAC7B,8DAA8D;QAC9D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,mCAAiB,GAAjB,UAAkB,GAAiB;QAAnC,iBAIC;QAHC,+DAA+D;QAC/D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAC1B,CAAA,KAAA,IAAI,CAAC,KAAK,CAAA,CAAC,YAAY,WAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAArB,CAAqB,CAAC,EAAE,CAAC;;IACzF,CAAC;IAED,iCAAe,GAAf,UAAgB,GAAe;QAC7B,sDAAsD;QACtD,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,2BAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,iCAAiC;QACjC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,uCAAqB,GAArB,UAAsB,GAAqB;QACzC,uEAAuE;QACvE,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;YAClB,KAAK,IAAI,CAAC;YACV,KAAK,KAAK;gBACR,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,OAAO,CAAC,CAAC;YACxD,KAAK,IAAI;gBACP,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,IAAI,CAAC,CAAC;YACrD,KAAK,SAAS;gBACZ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,SAAS,CAAC,CAAC;YAC1D;gBACE,MAAM,CAAC,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;oBACzB,KAAK,QAAQ;wBACX,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,MAAM,CAAC,CAAC;oBACvD,KAAK,QAAQ;wBACX,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,MAAM,CAAC,CAAC;oBACvD;wBACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;gBAC3D,CAAC;QACL,CAAC;IACH,CAAC;IAED,iCAAe,GAAf,UAAgB,GAAe;QAC7B,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IACjE,CAAC;IAED,2BAAS,GAAT,UAAU,GAAgB;QAA1B,iBAUC;QATC,gGAAgG;QAChG,6FAA6F;QAC7F,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACjD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAuB,GAAG,CAAC,IAAI,WAAQ,EAAE,GAAG,CAAC,CAAC;QACjF,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAM,SAAS,GACX,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAjB,CAAiB,CAAC,CAAC,CAAC,CAAC;QACnF,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,iDAAiD,EAAE,GAAG,CAAC,CAAC;QAChG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1B,CAAC;IAED,gCAAc,GAAd,UAAe,GAAc;QAC3B,4CAA4C;QAC5C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,oCAAkB,GAAlB,UAAmB,GAAkB;QACnC,IAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACvD,CAAC;IAED,mCAAiB,GAAjB,UAAkB,GAAiB;QACjC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,oCAAkB,GAAlB,UAAmB,GAAkB;QACnC,8DAA8D;QAC9D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,4BAAU,GAAV,UAAW,GAAU;QACnB,0CAA0C;QAC1C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,qCAAmB,GAAnB,UAAoB,GAAmB;QACrC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAChG,CAAC;IAED,uCAAqB,GAArB,UAAsB,GAAqB;QACzC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAClG,CAAC;IAGD,sBAAY,4BAAO;aAAnB;YACE,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACZ,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,GAAG,CAAC,CAAC;YACtE,CAAC;YACD,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAGD,sBAAY,kCAAa;aAAzB;YACE,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;YACjC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACZ,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAW,CAAC,SAAS,CAAC,CAAC;YAClF,CAAC;YACD,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAEO,mCAAiB,GAAzB,UAA0B,YAAoB,EAAE,GAA8B;QAA9E,iBAcC;QAbC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,4DAA4D;QAC5D,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,qBAAmB,GAAG,CAAC,IAAI,MAAG,EAAE,GAAG,CAAC,CAAC;QAC1E,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,gCAA8B,GAAG,CAAC,IAAI,MAAG,EAAE,GAAG,CAAC,CAAC;QAC1F,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAW,GAAG,CAAC,IAAI,sBAAmB,EAAE,GAAG,CAAC,CAAC;QAChG,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAjB,CAAiB,CAAC,CAAC,CAAC;QACtF,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACb,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,oDAAkD,GAAG,CAAC,IAAM,EAAE,GAAG,CAAC,CAAC;QAC7F,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1B,CAAC;IAEO,qCAAmB,GAA3B,UAA4B,YAAoB,EAAE,GAAkC;QAClF,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,6DAA6D;QAC7D,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAI,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC;YACrC,EAAE,CAAC,CAAC,YAAY,IAAI,UAAU,CAAC,CAAC,CAAC;gBAC/B,YAAY;oBACR,sFAAsF,CAAC;YAC7F,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,8BAA8B,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;YACxE,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,YAAY,GAAG,MAAI,YAAY,WAAQ,CAAC;YAC1C,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,WAAW,CACnB,iBAAe,GAAG,CAAC,IAAI,0BAAqB,YAAY,+BAA4B,EACpF,GAAG,CAAC,CAAC;QACX,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YACnB,IAAI,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC;YACrC,EAAE,CAAC,CAAC,YAAY,IAAI,UAAU,CAAC,CAAC,CAAC;gBAC/B,YAAY,GAAG,eAAe,CAAC;YACjC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,YAAY,GAAG,MAAI,YAAY,MAAG,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,aAAa,CACd,iBAAe,GAAG,CAAC,IAAI,wCAAmC,YAAc,EAAE,GAAG,CAAC,CAAC;QACrF,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,6BAAW,GAAnB,UAAoB,OAAe,EAAE,GAAQ;QAC3C,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAChF,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEO,+BAAa,GAArB,UAAsB,OAAe,EAAE,GAAQ;QAC7C,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAClF,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEO,uBAAK,GAAb,UAAc,MAAc;QAC1B,MAAM,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,qBAAW,CAAC,GAAG;YAC/D,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IACH,cAAC;AAAD,CAAC,AA9YD,IA8YC;AA9YY,0BAAO", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AST, AstVisitor, Binary, BindingPipe, Chain, Conditional, FunctionCall, ImplicitReceiver, Interpolation, KeyedRead, KeyedWrite, LiteralArray, LiteralMap, LiteralPrimitive, MethodCall, NonNullAssert, PrefixNot, PropertyRead, PropertyWrite, Quote, SafeMethodCall, SafePropertyRead, visitAstChildren} from '@angular/compiler';\n\nimport {BuiltinType, Signature, Span, Symbol, SymbolQuery, SymbolTable} from './symbols';\n\nexport interface ExpressionDiagnosticsContext { event?: boolean; }\n\nexport enum DiagnosticKind {\n  Error,\n  Warning,\n}\n\nexport class TypeDiagnostic {\n  constructor(public kind: DiagnosticKind, public message: string, public ast: AST) {}\n}\n\n// AstType calculatetype of the ast given AST element.\nexport class AstType implements AstVisitor {\n  public diagnostics: TypeDiagnostic[];\n\n  constructor(\n      private scope: SymbolTable, private query: SymbolQuery,\n      private context: ExpressionDiagnosticsContext) {}\n\n  getType(ast: AST): Symbol { return ast.visit(this); }\n\n  getDiagnostics(ast: AST): TypeDiagnostic[] {\n    this.diagnostics = [];\n    const type: Symbol = ast.visit(this);\n    if (this.context.event && type.callable) {\n      this.reportWarning('Unexpected callable expression. Expected a method call', ast);\n    }\n    return this.diagnostics;\n  }\n\n  visitBinary(ast: Binary): Symbol {\n    // Treat undefined and null as other.\n    function normalize(kind: BuiltinType, other: BuiltinType): BuiltinType {\n      switch (kind) {\n        case BuiltinType.Undefined:\n        case BuiltinType.Null:\n          return normalize(other, BuiltinType.Other);\n      }\n      return kind;\n    }\n\n    const getType = (ast: AST, operation: string): Symbol => {\n      const type = this.getType(ast);\n      if (type.nullable) {\n        switch (operation) {\n          case '&&':\n          case '||':\n          case '==':\n          case '!=':\n          case '===':\n          case '!==':\n            // Nullable allowed.\n            break;\n          default:\n            this.reportError(`The expression might be null`, ast);\n            break;\n        }\n        return this.query.getNonNullableType(type);\n      }\n      return type;\n    };\n\n    const leftType = getType(ast.left, ast.operation);\n    const rightType = getType(ast.right, ast.operation);\n    const leftRawKind = this.query.getTypeKind(leftType);\n    const rightRawKind = this.query.getTypeKind(rightType);\n    const leftKind = normalize(leftRawKind, rightRawKind);\n    const rightKind = normalize(rightRawKind, leftRawKind);\n\n    // The following swtich implements operator typing similar to the\n    // type production tables in the TypeScript specification.\n    // https://github.com/Microsoft/TypeScript/blob/v1.8.10/doc/spec.md#4.19\n    const operKind = leftKind << 8 | rightKind;\n    switch (ast.operation) {\n      case '*':\n      case '/':\n      case '%':\n      case '-':\n      case '<<':\n      case '>>':\n      case '>>>':\n      case '&':\n      case '^':\n      case '|':\n        switch (operKind) {\n          case BuiltinType.Any << 8 | BuiltinType.Any:\n          case BuiltinType.Number << 8 | BuiltinType.Any:\n          case BuiltinType.Any << 8 | BuiltinType.Number:\n          case BuiltinType.Number << 8 | BuiltinType.Number:\n            return this.query.getBuiltinType(BuiltinType.Number);\n          default:\n            let errorAst = ast.left;\n            switch (leftKind) {\n              case BuiltinType.Any:\n              case BuiltinType.Number:\n                errorAst = ast.right;\n                break;\n            }\n            return this.reportError('Expected a numeric type', errorAst);\n        }\n      case '+':\n        switch (operKind) {\n          case BuiltinType.Any << 8 | BuiltinType.Any:\n          case BuiltinType.Any << 8 | BuiltinType.Boolean:\n          case BuiltinType.Any << 8 | BuiltinType.Number:\n          case BuiltinType.Any << 8 | BuiltinType.Other:\n          case BuiltinType.Boolean << 8 | BuiltinType.Any:\n          case BuiltinType.Number << 8 | BuiltinType.Any:\n          case BuiltinType.Other << 8 | BuiltinType.Any:\n            return this.anyType;\n          case BuiltinType.Any << 8 | BuiltinType.String:\n          case BuiltinType.Boolean << 8 | BuiltinType.String:\n          case BuiltinType.Number << 8 | BuiltinType.String:\n          case BuiltinType.String << 8 | BuiltinType.Any:\n          case BuiltinType.String << 8 | BuiltinType.Boolean:\n          case BuiltinType.String << 8 | BuiltinType.Number:\n          case BuiltinType.String << 8 | BuiltinType.String:\n          case BuiltinType.String << 8 | BuiltinType.Other:\n          case BuiltinType.Other << 8 | BuiltinType.String:\n            return this.query.getBuiltinType(BuiltinType.String);\n          case BuiltinType.Number << 8 | BuiltinType.Number:\n            return this.query.getBuiltinType(BuiltinType.Number);\n          case BuiltinType.Boolean << 8 | BuiltinType.Number:\n          case BuiltinType.Other << 8 | BuiltinType.Number:\n            return this.reportError('Expected a number type', ast.left);\n          case BuiltinType.Number << 8 | BuiltinType.Boolean:\n          case BuiltinType.Number << 8 | BuiltinType.Other:\n            return this.reportError('Expected a number type', ast.right);\n          default:\n            return this.reportError('Expected operands to be a string or number type', ast);\n        }\n      case '>':\n      case '<':\n      case '<=':\n      case '>=':\n      case '==':\n      case '!=':\n      case '===':\n      case '!==':\n        switch (operKind) {\n          case BuiltinType.Any << 8 | BuiltinType.Any:\n          case BuiltinType.Any << 8 | BuiltinType.Boolean:\n          case BuiltinType.Any << 8 | BuiltinType.Number:\n          case BuiltinType.Any << 8 | BuiltinType.String:\n          case BuiltinType.Any << 8 | BuiltinType.Other:\n          case BuiltinType.Boolean << 8 | BuiltinType.Any:\n          case BuiltinType.Boolean << 8 | BuiltinType.Boolean:\n          case BuiltinType.Number << 8 | BuiltinType.Any:\n          case BuiltinType.Number << 8 | BuiltinType.Number:\n          case BuiltinType.String << 8 | BuiltinType.Any:\n          case BuiltinType.String << 8 | BuiltinType.String:\n          case BuiltinType.Other << 8 | BuiltinType.Any:\n          case BuiltinType.Other << 8 | BuiltinType.Other:\n            return this.query.getBuiltinType(BuiltinType.Boolean);\n          default:\n            return this.reportError('Expected the operants to be of similar type or any', ast);\n        }\n      case '&&':\n        return rightType;\n      case '||':\n        return this.query.getTypeUnion(leftType, rightType);\n    }\n\n    return this.reportError(`Unrecognized operator ${ast.operation}`, ast);\n  }\n\n  visitChain(ast: Chain) {\n    if (this.diagnostics) {\n      // If we are producing diagnostics, visit the children\n      visitAstChildren(ast, this);\n    }\n    // The type of a chain is always undefined.\n    return this.query.getBuiltinType(BuiltinType.Undefined);\n  }\n\n  visitConditional(ast: Conditional) {\n    // The type of a conditional is the union of the true and false conditions.\n    if (this.diagnostics) {\n      visitAstChildren(ast, this);\n    }\n    return this.query.getTypeUnion(this.getType(ast.trueExp), this.getType(ast.falseExp));\n  }\n\n  visitFunctionCall(ast: FunctionCall) {\n    // The type of a function call is the return type of the selected signature.\n    // The signature is selected based on the types of the arguments. Angular doesn't\n    // support contextual typing of arguments so this is simpler than TypeScript's\n    // version.\n    const args = ast.args.map(arg => this.getType(arg));\n    const target = this.getType(ast.target !);\n    if (!target || !target.callable) return this.reportError('Call target is not callable', ast);\n    const signature = target.selectSignature(args);\n    if (signature) return signature.result;\n    // TODO: Consider a better error message here.\n    return this.reportError('Unable no compatible signature found for call', ast);\n  }\n\n  visitImplicitReceiver(ast: ImplicitReceiver): Symbol {\n    const _this = this;\n    // Return a pseudo-symbol for the implicit receiver.\n    // The members of the implicit receiver are what is defined by the\n    // scope passed into this class.\n    return {\n      name: '$implict',\n      kind: 'component',\n      language: 'ng-template',\n      type: undefined,\n      container: undefined,\n      callable: false,\n      nullable: false,\n      public: true,\n      definition: undefined,\n      members(): SymbolTable{return _this.scope;},\n      signatures(): Signature[]{return [];},\n      selectSignature(types): Signature | undefined{return undefined;},\n      indexed(argument): Symbol | undefined{return undefined;}\n    };\n  }\n\n  visitInterpolation(ast: Interpolation): Symbol {\n    // If we are producing diagnostics, visit the children.\n    if (this.diagnostics) {\n      visitAstChildren(ast, this);\n    }\n    return this.undefinedType;\n  }\n\n  visitKeyedRead(ast: KeyedRead): Symbol {\n    const targetType = this.getType(ast.obj);\n    const keyType = this.getType(ast.key);\n    const result = targetType.indexed(keyType);\n    return result || this.anyType;\n  }\n\n  visitKeyedWrite(ast: KeyedWrite): Symbol {\n    // The write of a type is the type of the value being written.\n    return this.getType(ast.value);\n  }\n\n  visitLiteralArray(ast: LiteralArray): Symbol {\n    // A type literal is an array type of the union of the elements\n    return this.query.getArrayType(\n        this.query.getTypeUnion(...ast.expressions.map(element => this.getType(element))));\n  }\n\n  visitLiteralMap(ast: LiteralMap): Symbol {\n    // If we are producing diagnostics, visit the children\n    if (this.diagnostics) {\n      visitAstChildren(ast, this);\n    }\n    // TODO: Return a composite type.\n    return this.anyType;\n  }\n\n  visitLiteralPrimitive(ast: LiteralPrimitive) {\n    // The type of a literal primitive depends on the value of the literal.\n    switch (ast.value) {\n      case true:\n      case false:\n        return this.query.getBuiltinType(BuiltinType.Boolean);\n      case null:\n        return this.query.getBuiltinType(BuiltinType.Null);\n      case undefined:\n        return this.query.getBuiltinType(BuiltinType.Undefined);\n      default:\n        switch (typeof ast.value) {\n          case 'string':\n            return this.query.getBuiltinType(BuiltinType.String);\n          case 'number':\n            return this.query.getBuiltinType(BuiltinType.Number);\n          default:\n            return this.reportError('Unrecognized primitive', ast);\n        }\n    }\n  }\n\n  visitMethodCall(ast: MethodCall) {\n    return this.resolveMethodCall(this.getType(ast.receiver), ast);\n  }\n\n  visitPipe(ast: BindingPipe) {\n    // The type of a pipe node is the return type of the pipe's transform method. The table returned\n    // by getPipes() is expected to contain symbols with the corresponding transform method type.\n    const pipe = this.query.getPipes().get(ast.name);\n    if (!pipe) return this.reportError(`No pipe by the name ${ast.name} found`, ast);\n    const expType = this.getType(ast.exp);\n    const signature =\n        pipe.selectSignature([expType].concat(ast.args.map(arg => this.getType(arg))));\n    if (!signature) return this.reportError('Unable to resolve signature for pipe invocation', ast);\n    return signature.result;\n  }\n\n  visitPrefixNot(ast: PrefixNot) {\n    // The type of a prefix ! is always boolean.\n    return this.query.getBuiltinType(BuiltinType.Boolean);\n  }\n\n  visitNonNullAssert(ast: NonNullAssert) {\n    const expressionType = this.getType(ast.expression);\n    return this.query.getNonNullableType(expressionType);\n  }\n\n  visitPropertyRead(ast: PropertyRead) {\n    return this.resolvePropertyRead(this.getType(ast.receiver), ast);\n  }\n\n  visitPropertyWrite(ast: PropertyWrite) {\n    // The type of a write is the type of the value being written.\n    return this.getType(ast.value);\n  }\n\n  visitQuote(ast: Quote) {\n    // The type of a quoted expression is any.\n    return this.query.getBuiltinType(BuiltinType.Any);\n  }\n\n  visitSafeMethodCall(ast: SafeMethodCall) {\n    return this.resolveMethodCall(this.query.getNonNullableType(this.getType(ast.receiver)), ast);\n  }\n\n  visitSafePropertyRead(ast: SafePropertyRead) {\n    return this.resolvePropertyRead(this.query.getNonNullableType(this.getType(ast.receiver)), ast);\n  }\n\n  private _anyType: Symbol;\n  private get anyType(): Symbol {\n    let result = this._anyType;\n    if (!result) {\n      result = this._anyType = this.query.getBuiltinType(BuiltinType.Any);\n    }\n    return result;\n  }\n\n  private _undefinedType: Symbol;\n  private get undefinedType(): Symbol {\n    let result = this._undefinedType;\n    if (!result) {\n      result = this._undefinedType = this.query.getBuiltinType(BuiltinType.Undefined);\n    }\n    return result;\n  }\n\n  private resolveMethodCall(receiverType: Symbol, ast: SafeMethodCall|MethodCall) {\n    if (this.isAny(receiverType)) {\n      return this.anyType;\n    }\n\n    // The type of a method is the selected methods result type.\n    const method = receiverType.members().get(ast.name);\n    if (!method) return this.reportError(`Unknown method '${ast.name}'`, ast);\n    if (!method.type) return this.reportError(`Could not find a type for '${ast.name}'`, ast);\n    if (!method.type.callable) return this.reportError(`Member '${ast.name}' is not callable`, ast);\n    const signature = method.type.selectSignature(ast.args.map(arg => this.getType(arg)));\n    if (!signature)\n      return this.reportError(`Unable to resolve signature for call of method ${ast.name}`, ast);\n    return signature.result;\n  }\n\n  private resolvePropertyRead(receiverType: Symbol, ast: SafePropertyRead|PropertyRead) {\n    if (this.isAny(receiverType)) {\n      return this.anyType;\n    }\n\n    // The type of a property read is the seelcted member's type.\n    const member = receiverType.members().get(ast.name);\n    if (!member) {\n      let receiverInfo = receiverType.name;\n      if (receiverInfo == '$implict') {\n        receiverInfo =\n            'The component declaration, template variable declarations, and element references do';\n      } else if (receiverType.nullable) {\n        return this.reportError(`The expression might be null`, ast.receiver);\n      } else {\n        receiverInfo = `'${receiverInfo}' does`;\n      }\n      return this.reportError(\n          `Identifier '${ast.name}' is not defined. ${receiverInfo} not contain such a member`,\n          ast);\n    }\n    if (!member.public) {\n      let receiverInfo = receiverType.name;\n      if (receiverInfo == '$implict') {\n        receiverInfo = 'the component';\n      } else {\n        receiverInfo = `'${receiverInfo}'`;\n      }\n      this.reportWarning(\n          `Identifier '${ast.name}' refers to a private member of ${receiverInfo}`, ast);\n    }\n    return member.type;\n  }\n\n  private reportError(message: string, ast: AST): Symbol {\n    if (this.diagnostics) {\n      this.diagnostics.push(new TypeDiagnostic(DiagnosticKind.Error, message, ast));\n    }\n    return this.anyType;\n  }\n\n  private reportWarning(message: string, ast: AST): Symbol {\n    if (this.diagnostics) {\n      this.diagnostics.push(new TypeDiagnostic(DiagnosticKind.Warning, message, ast));\n    }\n    return this.anyType;\n  }\n\n  private isAny(symbol: Symbol): boolean {\n    return !symbol || this.query.getTypeKind(symbol) == BuiltinType.Any ||\n        (!!symbol.type && this.isAny(symbol.type));\n  }\n}"]}