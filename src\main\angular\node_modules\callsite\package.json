{"_args": [["callsite@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "callsite@1.0.0", "_id": "callsite@1.0.0", "_inBundle": false, "_integrity": "sha1-KAOY5dZkvXQDi28JBRU+borxvCA=", "_location": "/callsite", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "callsite@1.0.0", "name": "callsite", "escapedName": "callsite", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/better-assert"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/callsite/-/callsite-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "description": "access to v8's CallSites", "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": "*"}, "keywords": ["stack", "trace", "line"], "main": "index", "name": "callsite", "version": "1.0.0"}