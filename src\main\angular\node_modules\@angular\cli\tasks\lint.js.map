{"version": 3, "file": "lint.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["tasks/lint.ts"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,yBAAyB;AACzB,6BAA6B;AAC7B,6BAA6B;AAE7B,gFAA2E;AAE3E,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AASrD;IAAA;QAGE,WAAM,GAAI,OAAO,CAAC;QAClB,WAAM,GAAI,KAAK,CAAC;QAChB,cAAS,GAAI,KAAK,CAAC;IAErB,CAAC;CAAA;AAPD,0CAOC;AAED,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,OAAwB;QACrC,OAAO,qBAAQ,IAAI,eAAe,EAAE,EAAK,OAAO,CAAE,CAAC;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QAE1C,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpB,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,MAAM,GAAG,6CAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAE3C,MAAM,MAAM,GAAG,WAAW;aACvB,GAAG,CAAC,CAAC,MAAM;YACV,IAAI,OAAmB,CAAC;YACxB,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnB,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjD,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACpB,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,wDAAwD,CAAC,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YACD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACtD,MAAM,WAAW,GAAG;gBAClB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,SAAS,EAAE,OAAO,CAAC,MAAM;aAC1B,CAAC;YACF,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YAC5D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEpD,IAAI,aAAqB,CAAC;YAC1B,IAAI,UAAe,CAAC;YACpB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI;gBACjB,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACpD,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;oBAClB,MAAM,CAAC;gBACT,CAAC;gBAED,qDAAqD;gBACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC5C,EAAE,CAAC,CAAC,gBAAgB,KAAK,aAAa,CAAC,CAAC,CAAC;oBACvC,UAAU,GAAG,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;oBACxE,aAAa,GAAG,gBAAgB,CAAC;gBACnC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC5B,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO;YACrB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ;iBAC9B,MAAM,CAAC,CAAC,EAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAO,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzE,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;YAEpD,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClB,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7D,CAAC;YACD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC,EAAE;YACD,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,SAAS;SACjB,CAAC,CAAC;QAEL,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YAC/E,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;YAElC,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/D,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,iEAAiE;QACjE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;kBACjD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpB,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACvD,CAAC;QACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;CACF,CAAC,CAAC;AAEH,wBAAwB,OAAmB,EAAE,UAAyB,EAAE,MAAW;IACjF,IAAI,KAAK,GAAa,EAAE,CAAC;IAEzB,EAAE,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QACrB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAClF,CAAC;IAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACnB,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,WAAW,GAAG,EAAE,CAAC;IAErB,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;QACvB,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;cACrD,UAAU,CAAC,OAAO;cAClB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEzB,WAAW,GAAG,EAAE,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzD,CAAC;IAED,KAAK,GAAG,KAAK;SACV,GAAG,CAAC,CAAC,IAAY,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACnD,MAAM,CAAC,CAAC,CAAW,EAAE,CAAW,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEzD,MAAM,CAAC,KAAK,CAAC;AACf,CAAC;AAED,yBAAyB,IAAY,EAAE,OAAoB;IACzD,IAAI,QAAgB,CAAC;IAErB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/C,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACd,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,sFAAsF;QACtF,IAAI,CAAC;YACH,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,IAAI,WAAW,CAAC,wBAAwB,IAAI,IAAI,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC;AAClB,CAAC"}