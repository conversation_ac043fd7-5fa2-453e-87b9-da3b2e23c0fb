/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { MissingTranslationStrategy, ɵConsole as Console } from '@angular/core';
import * as html from '../ml_parser/ast';
import * as i18n from './i18n_ast';
import { PlaceholderMapper, Serializer } from './serializers/serializer';
/**
 * A container for translated messages
 */
export declare class TranslationBundle {
    private _i18nNodesByMsgId;
    digest: (m: i18n.Message) => string;
    mapperFactory: (m: i18n.Message) => PlaceholderMapper;
    private _i18nToHtml;
    constructor(_i18nNodesByMsgId: {
        [msgId: string]: i18n.Node[];
    }, locale: string | null, digest: (m: i18n.Message) => string, mapperFactory?: (m: i18n.Message) => PlaceholderMapper, missingTranslationStrategy?: MissingTranslationStrategy, console?: Console);
    static load(content: string, url: string, serializer: Serializer, missingTranslationStrategy: MissingTranslationStrategy, console?: Console): TranslationBundle;
    get(srcMsg: i18n.Message): html.Node[];
    has(srcMsg: i18n.Message): boolean;
}
