{"version": 3, "file": "index.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["blueprints/ng/index.ts"], "names": [], "mappings": ";;AAAA,gDAA8C;AAE9C,MAAM,SAAS,GAAK,OAAO,CAAC,sCAAsC,CAAC,CAAC;AACpE,MAAM,IAAI,GAAU,OAAO,CAAC,MAAM,CAAC,CAAC;AACpC,MAAM,WAAW,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACtD,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;AAE3C,kBAAe,SAAS,CAAC,MAAM,CAAC;IAC9B,WAAW,EAAE,EAAE;IAEf,gBAAgB,EAAE;QAChB,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;QACrE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;QAChE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;QAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;QAClD,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;QACxE,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;QAC3E,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;QACpE,EAAE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,8BAA8B;SAC3C;KACH;IAED,aAAa,EAAE,UAAS,OAAY;QAClC,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACxE,MAAM,CAAC,SAAS,CAAC,kBAAkB;gBACjC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,MAAM,EAAE,UAAS,OAAY;QAC3B,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACpB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;YAC9B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;QACpE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,kBAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,KAAK,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAAC;QAC9E,kDAAkD;QAClD,mFAAmF;QACnF,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC;QAE9C,kFAAkF;QAClF,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChF,MAAM,WAAW,GAAI,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAY;aACvE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;aACjD,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QAEzC,MAAM,CAAC;YACL,iBAAiB,EAAE,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YAC7D,eAAe,EAAE,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YAC1D,WAAW,EAAE,WAAW;YACxB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,gBAAgB;YAClC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;IACJ,CAAC;IAED,KAAK,EAAE;QACL,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAkB,CAAC;QAEpD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1C,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1E,CAAC;QACD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;YAC7C,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/E,CAAC;QACD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACzC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3C,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1E,CAAC;QAED,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACzC,MAAM,YAAY,GAAa,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe;gBAChF,oBAAoB,EAAE,SAAS,EAAE,oBAAoB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YACvF,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC1B,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,cAAc,IAAI,MAAM,CAAC,CAAC,CAAC;YACnD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,CAAC,QAAQ,CAAC;IAClB,CAAC;IAED,aAAa,EAAE,UAAU,OAAY;QACnC,yCAAyC;QACzC,MAAM,CAAC;YACL,QAAQ,EAAE;gBACR,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;YAClC,CAAC;YACD,YAAY,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}