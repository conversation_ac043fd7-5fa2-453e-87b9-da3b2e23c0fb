{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/compiler/testing/index.ts", "../../../../../packages/compiler/testing/src/testing.ts", "../../../../../packages/compiler/testing/src/metadata_overrider.ts", "../../../../../packages/compiler/testing/src/pipe_resolver_mock.ts", "../../../../../packages/compiler/testing/src/ng_module_resolver_mock.ts", "../../../../../packages/compiler/testing/src/directive_resolver_mock.ts", "../../../../../packages/compiler/testing/src/schema_registry_mock.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the compiler/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all APIs of the compiler package.\n *\n * <div class=\"callout is-critical\">\n *   <header>Unstable APIs</header>\n *   <p>\n *     All compiler apis are currently considered experimental and private!\n *   </p>\n *   <p>\n *     We expect the APIs in this package to keep on changing. Do not rely on them.\n *   </p>\n * </div>\n */\nexport * from './schema_registry_mock';\nexport * from './directive_resolver_mock';\nexport * from './ng_module_resolver_mock';\nexport * from './pipe_resolver_mock';\n\nimport {createPlatformFactory, ModuleWithComponentFactories, Injectable, CompilerOptions, COMPILER_OPTIONS, CompilerFactory, ComponentFactory, NgModuleFactory, Injector, NgModule, Component, Directive, Pipe, Type, PlatformRef, ɵstringify} from '@angular/core';\nimport {MetadataOverride, ɵTestingCompilerFactory as TestingCompilerFactory, ɵTestingCompiler as TestingCompiler} from '@angular/core/testing';\nimport {platformCoreDynamic, JitCompiler, DirectiveResolver, NgModuleResolver, PipeResolver, CompileMetadataResolver} from '@angular/compiler';\nimport {MockDirectiveResolver} from './directive_resolver_mock';\nimport {MockNgModuleResolver} from './ng_module_resolver_mock';\nimport {MockPipeResolver} from './pipe_resolver_mock';\nimport {MetadataOverrider} from './metadata_overrider';\n\n\nexport class TestingCompilerFactoryImpl implements TestingCompilerFactory {\n  constructor(private _compilerFactory: CompilerFactory) {}\n\n  createTestingCompiler(options: CompilerOptions[]): TestingCompiler {\n    const compiler = <JitCompiler>this._compilerFactory.createCompiler(options);\n    return new TestingCompilerImpl(\n        compiler, compiler.injector.get(MockDirectiveResolver),\n        compiler.injector.get(MockPipeResolver), compiler.injector.get(MockNgModuleResolver),\n        compiler.injector.get(CompileMetadataResolver));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: CompilerFactory, },\n];\n}\n\nexport class TestingCompilerImpl implements TestingCompiler {\n  private _overrider = new MetadataOverrider();\n  constructor(\n      private _compiler: JitCompiler, private _directiveResolver: MockDirectiveResolver,\n      private _pipeResolver: MockPipeResolver, private _moduleResolver: MockNgModuleResolver,\n      private _metadataResolver: CompileMetadataResolver) {}\n  get injector(): Injector { return this._compiler.injector; }\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    return this._compiler.compileModuleSync(moduleType);\n  }\n\n  compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    return this._compiler.compileModuleAsync(moduleType);\n  }\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    return this._compiler.compileModuleAndAllComponentsSync(moduleType);\n  }\n\n  compileModuleAndAllComponentsAsync<T>(moduleType: Type<T>):\n      Promise<ModuleWithComponentFactories<T>> {\n    return this._compiler.compileModuleAndAllComponentsAsync(moduleType);\n  }\n\n  getNgContentSelectors(component: Type<any>): string[] {\n    return this._compiler.getNgContentSelectors(component);\n  }\n\n  getComponentFactory<T>(component: Type<T>): ComponentFactory<T> {\n    return this._compiler.getComponentFactory(component);\n  }\n\n  checkOverrideAllowed(type: Type<any>) {\n    if (this._compiler.hasAotSummary(type)) {\n      throw new Error(`${ɵstringify(type)} was AOT compiled, so its metadata cannot be changed.`);\n    }\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    this.checkOverrideAllowed(ngModule);\n    const oldMetadata = this._moduleResolver.resolve(ngModule, false);\n    this._moduleResolver.setNgModule(\n        ngModule, this._overrider.overrideMetadata(NgModule, oldMetadata, override));\n  }\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.checkOverrideAllowed(directive);\n    const oldMetadata = this._directiveResolver.resolve(directive, false);\n    this._directiveResolver.setDirective(\n        directive, this._overrider.overrideMetadata(Directive, oldMetadata !, override));\n  }\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.checkOverrideAllowed(component);\n    const oldMetadata = this._directiveResolver.resolve(component, false);\n    this._directiveResolver.setDirective(\n        component, this._overrider.overrideMetadata(Component, oldMetadata !, override));\n  }\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.checkOverrideAllowed(pipe);\n    const oldMetadata = this._pipeResolver.resolve(pipe, false);\n    this._pipeResolver.setPipe(pipe, this._overrider.overrideMetadata(Pipe, oldMetadata, override));\n  }\n  loadAotSummaries(summaries: () => any[]) { this._compiler.loadAotSummaries(summaries); }\n  clearCache(): void { this._compiler.clearCache(); }\n  clearCacheFor(type: Type<any>) { this._compiler.clearCacheFor(type); }\n}\n\n/**\n * Platform for dynamic tests\n *\n * @experimental\n */\nexport const platformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef =\n    createPlatformFactory(platformCoreDynamic, 'coreDynamicTesting', [\n      {\n        provide: COMPILER_OPTIONS,\n        useValue: {\n          providers: [\n            MockPipeResolver,\n            {provide: PipeResolver, useExisting: MockPipeResolver},\n            MockDirectiveResolver,\n            {provide: DirectiveResolver, useExisting: MockDirectiveResolver},\n            MockNgModuleResolver,\n            {provide: NgModuleResolver, useExisting: MockNgModuleResolver},\n          ]\n        },\n        multi: true\n      },\n      {provide: TestingCompilerFactory, useClass: TestingCompilerFactoryImpl}\n    ]);\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵstringify as stringify} from '@angular/core';\nimport {MetadataOverride} from '@angular/core/testing';\n\ntype StringMap = {\n  [key: string]: any\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n      metadataClass: {new (options: T): C;}, oldMetadata: C, override: MetadataOverride<T>): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => props[prop] = (<any>oldMetadata)[prop]);\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (removeValue instanceof Array) {\n      removeValue.forEach(\n          (value: any) => { removeObjects.add(_propHashKey(prop, value, references)); });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (propValue instanceof Array) {\n      metadata[prop] = propValue.filter(\n          (value: any) => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && propValue instanceof Array) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  const replacer = (key: any, value: any) => {\n    if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, PipeResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, Pipe, Type} from '@angular/core';\n\n\nexport class MockPipeResolver extends PipeResolver {\n  private _pipes = new Map<Type<any>, Pipe>();\n\n  constructor(private _injector: Injector, refector: CompileReflector) { super(refector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(pipe: Type<any>) { this._compiler.clearCacheFor(pipe); }\n\n  /**\n   * Overrides the {@link Pipe} for a pipe.\n   */\n  setPipe(type: Type<any>, metadata: Pipe): void {\n    this._pipes.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link Pipe} for a pipe:\n   * - Set the {@link Pipe} to the overridden view when it exists or fallback to the\n   * default\n   * `PipeResolver`, see `setPipe`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): Pipe {\n    let metadata = this._pipes.get(type);\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound) !;\n    }\n    return metadata;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CompileReflector, NgModuleResolver} from '@angular/compiler';\nimport {Compiler, Injectable, Injector, NgModule, Type} from '@angular/core';\n\n\nexport class MockNgModuleResolver extends NgModuleResolver {\n  private _ngModules = new Map<Type<any>, NgModule>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  /**\n   * Overrides the {@link NgModule} for a module.\n   */\n  setNgModule(type: Type<any>, metadata: NgModule): void {\n    this._ngModules.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Returns the {@link NgModule} for a module:\n   * - Set the {@link NgModule} to the overridden view when it exists or fallback to the\n   * default\n   * `NgModuleResolver`, see `setNgModule`.\n   */\n  resolve(type: Type<any>, throwIfNotFound = true): NgModule {\n    return this._ngModules.get(type) || super.resolve(type, throwIfNotFound) !;\n  }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CompileReflector, DirectiveResolver} from '@angular/compiler';\nimport {Compiler, Component, Directive, Injectable, Injector, Provider, Type, resolveForwardRef, ɵViewMetadata as ViewMetadata} from '@angular/core';\n\n\n\n/**\n * An implementation of {@link DirectiveResolver} that allows overriding\n * various properties of directives.\n */\n\nexport class MockDirectiveResolver extends DirectiveResolver {\n  private _directives = new Map<Type<any>, Directive>();\n  private _providerOverrides = new Map<Type<any>, any[]>();\n  private _viewProviderOverrides = new Map<Type<any>, any[]>();\n  private _views = new Map<Type<any>, ViewMetadata>();\n  private _inlineTemplates = new Map<Type<any>, string>();\n\n  constructor(private _injector: Injector, reflector: CompileReflector) { super(reflector); }\n\n  private get _compiler(): Compiler { return this._injector.get(Compiler); }\n\n  private _clearCacheFor(component: Type<any>) { this._compiler.clearCacheFor(component); }\n\n  resolve(type: Type<any>): Directive;\n  resolve(type: Type<any>, throwIfNotFound: true): Directive;\n  resolve(type: Type<any>, throwIfNotFound: boolean): Directive|null;\n  resolve(type: Type<any>, throwIfNotFound = true): Directive|null {\n    let metadata = this._directives.get(type) || null;\n    if (!metadata) {\n      metadata = super.resolve(type, throwIfNotFound);\n    }\n    if (!metadata) {\n      return null;\n    }\n\n    const providerOverrides = this._providerOverrides.get(type);\n    const viewProviderOverrides = this._viewProviderOverrides.get(type);\n\n    let providers = metadata.providers;\n    if (providerOverrides != null) {\n      const originalViewProviders: Provider[] = metadata.providers || [];\n      providers = originalViewProviders.concat(providerOverrides);\n    }\n\n    if (metadata instanceof Component) {\n      let viewProviders = metadata.viewProviders;\n      if (viewProviderOverrides != null) {\n        const originalViewProviders: Provider[] = metadata.viewProviders || [];\n        viewProviders = originalViewProviders.concat(viewProviderOverrides);\n      }\n\n      let view = this._views.get(type) || metadata;\n      let animations = view.animations;\n      let templateUrl: string|undefined = view.templateUrl;\n\n      let inlineTemplate = this._inlineTemplates.get(type);\n      if (inlineTemplate) {\n        templateUrl = undefined;\n      } else {\n        inlineTemplate = view.template;\n      }\n\n      return new Component({\n        selector: metadata.selector,\n        inputs: metadata.inputs,\n        outputs: metadata.outputs,\n        host: metadata.host,\n        exportAs: metadata.exportAs,\n        moduleId: metadata.moduleId,\n        queries: metadata.queries,\n        changeDetection: metadata.changeDetection,\n        providers: providers,\n        viewProviders: viewProviders,\n        entryComponents: metadata.entryComponents,\n        template: inlineTemplate,\n        templateUrl: templateUrl,\n        animations: animations,\n        styles: view.styles,\n        styleUrls: view.styleUrls,\n        encapsulation: view.encapsulation,\n        interpolation: view.interpolation\n      });\n    }\n\n    return new Directive({\n      selector: metadata.selector,\n      inputs: metadata.inputs,\n      outputs: metadata.outputs,\n      host: metadata.host,\n      providers: providers,\n      exportAs: metadata.exportAs,\n      queries: metadata.queries\n    });\n  }\n\n  /**\n   * Overrides the {@link Directive} for a directive.\n   */\n  setDirective(type: Type<any>, metadata: Directive): void {\n    this._directives.set(type, metadata);\n    this._clearCacheFor(type);\n  }\n\n  setProvidersOverride(type: Type<any>, providers: Provider[]): void {\n    this._providerOverrides.set(type, providers);\n    this._clearCacheFor(type);\n  }\n\n  setViewProvidersOverride(type: Type<any>, viewProviders: Provider[]): void {\n    this._viewProviderOverrides.set(type, viewProviders);\n    this._clearCacheFor(type);\n  }\n\n  /**\n   * Overrides the {@link ViewMetadata} for a component.\n   */\n  setView(component: Type<any>, view: ViewMetadata): void {\n    this._views.set(component, view);\n    this._clearCacheFor(component);\n  }\n  /**\n   * Overrides the inline template for a component - other configuration remains unchanged.\n   */\n  setInlineTemplate(component: Type<any>, template: string): void {\n    this._inlineTemplates.set(component, template);\n    this._clearCacheFor(component);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: Injector, },\n{type: CompileReflector, },\n];\n}\n\nfunction flattenArray(tree: any[], out: Array<Type<any>|any[]>): void {\n  if (tree == null) return;\n  for (let i = 0; i < tree.length; i++) {\n    const item = resolveForwardRef(tree[i]);\n    if (Array.isArray(item)) {\n      flattenArray(item, out);\n    } else {\n      out.push(item);\n    }\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementSchemaRegistry} from '@angular/compiler';\nimport {SchemaMetadata, SecurityContext} from '@angular/core';\n\nexport class MockSchemaRegistry implements ElementSchemaRegistry {\n  constructor(\n      public existingProperties: {[key: string]: boolean},\n      public attrPropMapping: {[key: string]: string},\n      public existingElements: {[key: string]: boolean}, public invalidProperties: Array<string>,\n      public invalidAttributes: Array<string>) {}\n\n  hasProperty(tagName: string, property: string, schemas: SchemaMetadata[]): boolean {\n    const value = this.existingProperties[property];\n    return value === void 0 ? true : value;\n  }\n\n  hasElement(tagName: string, schemaMetas: SchemaMetadata[]): boolean {\n    const value = this.existingElements[tagName.toLowerCase()];\n    return value === void 0 ? true : value;\n  }\n\n  allKnownElementNames(): string[] { return Object.keys(this.existingElements); }\n\n  securityContext(selector: string, property: string, isAttribute: boolean): SecurityContext {\n    return SecurityContext.NONE;\n  }\n\n  getMappedPropName(attrName: string): string { return this.attrPropMapping[attrName] || attrName; }\n\n  getDefaultComponentElementName(): string { return 'ng-component'; }\n\n  validateProperty(name: string): {error: boolean, msg?: string} {\n    if (this.invalidProperties.indexOf(name) > -1) {\n      return {error: true, msg: `Binding to property '${name}' is disallowed for security reasons`};\n    } else {\n      return {error: false};\n    }\n  }\n\n  validateAttribute(name: string): {error: boolean, msg?: string} {\n    if (this.invalidAttributes.indexOf(name) > -1) {\n      return {\n        error: true,\n        msg: `Binding to attribute '${name}' is disallowed for security reasons`\n      };\n    } else {\n      return {error: false};\n    }\n  }\n\n  normalizeAnimationStyleProperty(propName: string): string { return propName; }\n  normalizeAnimationStyleValue(camelCaseProp: string, userProvidedProp: string, val: string|number):\n      {error: string, value: string} {\n    return {error: null !, value: val.toString()};\n  }\n}\n"], "names": ["TestingCompilerFactory", "stringify"], "mappings": ";;;;AMAA;;;;;;;AASA,AAEA,AAAA,MAAA,kBAAA,CAAA;IACE,WAAF,CACa,kBAA4C,EAC5C,eAAwC,EACxC,gBAA0C,EAAS,iBAAgC,EACnF,iBAAgC,EAJ7C;QACa,IAAb,CAAA,kBAA+B,GAAlB,kBAAkB,CAA0B;QAC5C,IAAb,CAAA,eAA4B,GAAf,eAAe,CAAyB;QACxC,IAAb,CAAA,gBAA6B,GAAhB,gBAAgB,CAA0B;QAAS,IAAhE,CAAA,iBAAiF,GAAjB,iBAAiB,CAAe;QACnF,IAAb,CAAA,iBAA8B,GAAjB,iBAAiB,CAAe;KAAI;IAE/C,WAAW,CAAC,OAAe,EAAE,QAAgB,EAAE,OAAyB,EAA1E;QACI,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAChD,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;KACxC;IAED,UAAU,CAAC,OAAe,EAAE,WAA6B,EAA3D;QACI,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAC3D,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;KACxC;IAED,oBAAoB,GAAtB,EAAqC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE;IAE/E,eAAe,CAAC,QAAgB,EAAE,QAAgB,EAAE,WAAoB,EAA1E;QACI,OAAO,eAAe,CAAC,IAAI,CAAC;KAC7B;IAED,iBAAiB,CAAC,QAAgB,EAApC,EAAgD,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE;IAElG,8BAA8B,GAAhC,EAA6C,OAAO,cAAc,CAAC,EAAE;IAEnE,gBAAgB,CAAC,IAAY,EAA/B;QACI,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAC7C,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAhC,qBAAA,EAAwD,IAAI,CAA5D,oCAAA,CAAkG,EAAC,CAAC;SAC/F;aAAM;YACL,OAAO,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;SACvB;KACF;IAED,iBAAiB,CAAC,IAAY,EAAhC;QACI,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAC7C,OAAO;gBACL,KAAK,EAAE,IAAI;gBACX,GAAG,EAAE,CAAb,sBAAA,EAAsC,IAAI,CAA1C,oCAAA,CAAgF;aACzE,CAAC;SACH;aAAM;YACL,OAAO,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;SACvB;KACF;IAED,+BAA+B,CAAC,QAAgB,EAAlD,EAA8D,OAAO,QAAQ,CAAC,EAAE;IAC9E,4BAA4B,CAAC,aAAqB,EAAE,gBAAwB,EAAE,GAAkB,EAAlG;QAEI,OAAO,EAAC,KAAK,EAAE,IAAM,EAAE,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAC,CAAC;KAC/C;CACF;;AD9DD;;;;;;;AAOA,AACA,AAIA;;;;AAKA,AAAA,MAAA,qBAAmC,SAAQ,iBAAiB,CAA5D;IAOE,WAAF,CAAsB,SAAmB,EAAE,SAA2B,EAAtE;QAA0E,KAAK,CAAC,SAAS,CAAC,CAAC;QAArE,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAU;QAN/B,IAAV,CAAA,WAAqB,GAAG,IAAI,GAAG,EAAwB,CAAC;QAC9C,IAAV,CAAA,kBAA4B,GAAG,IAAI,GAAG,EAAoB,CAAC;QACjD,IAAV,CAAA,sBAAgC,GAAG,IAAI,GAAG,EAAoB,CAAC;QACrD,IAAV,CAAA,MAAgB,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC5C,IAAV,CAAA,gBAA0B,GAAG,IAAI,GAAG,EAAqB,CAAC;KAEmC;IAE3F,IAAY,SAAS,GAAvB,EAAsC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;IAElE,cAAc,CAAC,SAAoB,EAA7C,EAAiD,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE;IAKzF,OAAO,CAAC,IAAe,EAAE,eAAe,GAAG,IAAI,EAAjD;QACI,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,IAAI,CAAC;SACb;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QACnC,IAAI,iBAAiB,IAAI,IAAI,EAAE;YAC7B,MAAM,qBAAqB,GAAe,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC;YACnE,SAAS,GAAG,qBAAqB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;SAC7D;QAED,IAAI,QAAQ,YAAY,SAAS,EAAE;YACjC,IAAI,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;YAC3C,IAAI,qBAAqB,IAAI,IAAI,EAAE;gBACjC,MAAM,qBAAqB,GAAe,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC;gBACvE,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;aACrE;YAED,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC;YAC7C,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACjC,IAAI,WAAW,GAAqB,IAAI,CAAC,WAAW,CAAC;YAErD,IAAI,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,cAAc,EAAE;gBAClB,WAAW,GAAG,SAAS,CAAC;aACzB;iBAAM;gBACL,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;aAChC;YAED,OAAO,IAAI,SAAS,CAAC;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,aAAa;gBAC5B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,QAAQ,EAAE,cAAc;gBACxB,WAAW,EAAE,WAAW;gBACxB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,SAAS,CAAC;YACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;SAC1B,CAAC,CAAC;KACJ;;;;IAKD,YAAY,CAAC,IAAe,EAAE,QAAmB,EAAnD;QACI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;IAED,oBAAoB,CAAC,IAAe,EAAE,SAAqB,EAA7D;QACI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;IAED,wBAAwB,CAAC,IAAe,EAAE,aAAyB,EAArE;QACI,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;;;;IAKD,OAAO,CAAC,SAAoB,EAAE,IAAkB,EAAlD;QACI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;KAChC;;;;IAID,iBAAiB,CAAC,SAAoB,EAAE,QAAgB,EAA1D;QACI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;KAChC;;AACI,qBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,qBAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC,AAGF,AAUC;;AD1JD;;;;;;;AAQA,AACA,AAGA,AAAA,MAAA,oBAAkC,SAAQ,gBAAgB,CAA1D;IAGE,WAAF,CAAsB,SAAmB,EAAE,SAA2B,EAAtE;QAA0E,KAAK,CAAC,SAAS,CAAC,CAAC;QAArE,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAU;QAF/B,IAAV,CAAA,UAAoB,GAAG,IAAI,GAAG,EAAuB,CAAC;KAEuC;;;;IAK3F,WAAW,CAAC,IAAe,EAAE,QAAkB,EAAjD;QACI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;;;;;;;IAQD,OAAO,CAAC,IAAe,EAAE,eAAe,GAAG,IAAI,EAAjD;QACI,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAG,CAAC;KAC5E;IAED,IAAY,SAAS,GAAvB,EAAsC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;IAElE,cAAc,CAAC,SAAoB,EAA7C,EAAiD,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE;;AACpF,oBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,oBAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;;AD7CF;;;;;;;AAQA,AACA,AAGA,AAAA,MAAA,gBAA8B,SAAQ,YAAY,CAAlD;IAGE,WAAF,CAAsB,SAAmB,EAAE,QAA0B,EAArE;QAAyE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAAnE,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAU;QAF/B,IAAV,CAAA,MAAgB,GAAG,IAAI,GAAG,EAAmB,CAAC;KAE6C;IAEzF,IAAY,SAAS,GAAvB,EAAsC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;IAElE,cAAc,CAAC,IAAe,EAAxC,EAA4C,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;;;;IAK/E,OAAO,CAAC,IAAe,EAAE,QAAc,EAAzC;QACI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAC3B;;;;;;;IAQD,OAAO,CAAC,IAAe,EAAE,eAAe,GAAG,IAAI,EAAjD;QACI,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAG,CAAC;SACnD;QACD,OAAO,QAAQ,CAAC;KACjB;;AACI,gBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,gBAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,QAAQ,GAAG;IAClB,EAAC,IAAI,EAAE,gBAAgB,GAAG;CACzB,CAAC;;ADjDF;;;;;;;AAQA,AAOA,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB,AAAA,MAAA,iBAAA,CAAA;IAAA,WAAA,GAAA;QACU,IAAV,CAAA,WAAqB,GAAG,IAAI,GAAG,EAAe,CAAC;KA0B9C;;;;;IArBC,gBAAgB,CACZ,aAAqC,EAAE,WAAc,EAAE,QAA6B,EAD1F;QAEI,MAAM,KAAK,GAAc,EAAE,CAAC;QAC5B,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,GAAS,WAAY,CAAC,IAAI,CAAC,CAAC,CAAC;SACpF;QAED,IAAI,QAAQ,CAAC,GAAG,EAAE;YAChB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,CAAxB,0BAAA,EAAqDC,UAAS,CAAC,aAAa,CAAC,CAA7E,kBAAA,CAAiG,CAAC,CAAC;aAC5F;YACD,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SAClC;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1D;QACD,IAAI,QAAQ,CAAC,GAAG,EAAE;YAChB,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SAClC;QACD,OAAO,IAAI,aAAa,CAAM,KAAK,CAAC,CAAC;KACtC;CACF;AAED,SAAA,cAAA,CAAwB,QAAmB,EAAE,MAAW,EAAE,UAA4B,EAAtF;IACE,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;IACxC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;QACzB,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,WAAW,YAAY,KAAK,EAAE;YAChC,WAAW,CAAC,OAAO,CACf,CAAC,KAAU,KADrB,EAC4B,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACpF;aAAM;YACL,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;SAChE;KACF;IAED,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;QAC3B,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,SAAS,YAAY,KAAK,EAAE;YAC9B,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAC7B,CAAC,KAAU,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;SAChF;aAAM;YACL,IAAI,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;gBAChE,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;aAC5B;SACF;KACF;CACF;AAED,SAAA,WAAA,CAAqB,QAAmB,EAAE,GAAQ,EAAlD;IACE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,YAAY,KAAK,EAAE;YACnD,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC7C;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;SAC3B;KACF;CACF;AAED,SAAA,WAAA,CAAqB,QAAmB,EAAE,GAAQ,EAAlD;IACE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;KAC5B;CACF;AAED,SAAA,YAAA,CAAsB,QAAa,EAAE,SAAc,EAAE,UAA4B,EAAjF;IACE,MAAM,QAAQ,GAAG,CAAC,GAAQ,EAAE,KAAU,KAAxC;QACI,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,KAAK,GAAG,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;SAChD;QACD,OAAO,KAAK,CAAC;KACd,CAAC;IAEF,OAAO,CAAT,EAAY,QAAQ,CAApB,CAAA,EAAwB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAA3D,CAA6D,CAAC;CAC7D;AAED,SAAA,mBAAA,CAA6B,GAAQ,EAAE,UAA4B,EAAnE;IACE,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAAC,EAAE,EAAE;QACP,EAAE,GAAG,CAAT,EAAYA,UAAS,CAAC,GAAG,CAAC,CAA1B,EAA6B,gBAAgB,EAAE,CAA/C,CAAiD,CAAC;QAC9C,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;KACzB;IACD,OAAO,EAAE,CAAC;CACX;AAGD,SAAA,WAAA,CAAqB,GAAQ,EAA7B;IACE,MAAM,KAAK,GAAa,EAAE,CAAC;;IAE3B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAhC;QACI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACzB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;KACF,CAAC,CAAC;;IAGH,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,OAAO,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;QAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAzC;YACM,MAAM,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC/D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBACvD,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACvB;SACF,CAAC,CAAC;KACJ;IACD,OAAO,KAAK,CAAC;CACd;;ADlID;;;;;;;;;;;;;;;;;;;;;;AAuBA,AACA,AACA,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA,AAGA,AAAA,MAAA,0BAAA,CAAA;IACE,WAAF,CAAsB,gBAAiC,EAAvD;QAAsB,IAAtB,CAAA,gBAAsC,GAAhB,gBAAgB,CAAiB;KAAI;IAEzD,qBAAqB,CAAC,OAA0B,EAAlD;QACI,MAAM,QAAQ,GAAgB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC5E,OAAO,IAAI,mBAAmB,CAC1B,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAC,EACtD,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC,EACpF,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC;KACrD;;AACI,0BAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,0BAAP,CAAA,cAAqB,GAAmE,MAAM;IAC9F,EAAC,IAAI,EAAE,eAAe,GAAG;CACxB,CAAC;AAGF,AAAA,MAAA,mBAAA,CAAA;IAEE,WAAF,CACc,SAAsB,EAAU,kBAAyC,EACzE,aAA+B,EAAU,eAAqC,EAC9E,iBAA0C,EAHxD;QACc,IAAd,CAAA,SAAuB,GAAT,SAAS,CAAa;QAAU,IAA9C,CAAA,kBAAgE,GAAlB,kBAAkB,CAAuB;QACzE,IAAd,CAAA,aAA2B,GAAb,aAAa,CAAkB;QAAU,IAAvD,CAAA,eAAsE,GAAf,eAAe,CAAsB;QAC9E,IAAd,CAAA,iBAA+B,GAAjB,iBAAiB,CAAyB;QAJ9C,IAAV,CAAA,UAAoB,GAAG,IAAI,iBAAiB,EAAE,CAAC;KAIa;IAC1D,IAAI,QAAQ,GAAd,EAA6B,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;IAE5D,iBAAiB,CAAI,UAAmB,EAA1C;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;KACrD;IAED,kBAAkB,CAAI,UAAmB,EAA3C;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;KACtD;IACD,iCAAiC,CAAI,UAAmB,EAA1D;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,UAAU,CAAC,CAAC;KACrE;IAED,kCAAkC,CAAI,UAAmB,EAA3D;QAEI,OAAO,IAAI,CAAC,SAAS,CAAC,kCAAkC,CAAC,UAAU,CAAC,CAAC;KACtE;IAED,qBAAqB,CAAC,SAAoB,EAA5C;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;KACxD;IAED,mBAAmB,CAAI,SAAkB,EAA3C;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACtD;IAED,oBAAoB,CAAC,IAAe,EAAtC;QACI,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,CAAtB,EAAyB,UAAU,CAAC,IAAI,CAAC,CAAzC,qDAAA,CAAgG,CAAC,CAAC;SAC7F;KACF;IAED,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAA1E;QACI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,CAAC,WAAW,CAC5B,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;KAClF;IACD,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAA/E;QACI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAChC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KACtF;IACD,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAA/E;QACI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAChC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KACtF;IACD,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAhE;QACI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;KACjG;IACD,gBAAgB,CAAC,SAAsB,EAAzC,EAA6C,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE;IACxF,UAAU,GAAZ,EAAuB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,EAAE;IACnD,aAAa,CAAC,IAAe,EAA/B,EAAmC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;CACvE;;;;;;AAOD,AAPO,MAAM,0BAAA,GAQT,qBAAqB,CAPC,mBAAC,EAAoB,oBAAA,EAAsB;IAQ/D;QACE,OAAO,EAPE,gBAAA;QAQT,QAAQ,EAPE;YAQR,SAAS,EAPE;gBAQT,gBAAgB;gBAChB,EAAC,OAPC,EAAQ,YAAA,EAAc,WAAA,EAAa,gBAAA,EAAiB;gBAQtD,qBAAqB;gBACrB,EAAC,OAPC,EAAQ,iBAAA,EAAmB,WAAA,EAAa,qBAAA,EAAsB;gBAQhE,oBAAoB;gBACpB,EAAC,OAPC,EAAQ,gBAAA,EAAkB,WAAA,EAAa,oBAAA,EAAqB;aAQ/D;SACF;QACD,KAAK,EAPE,IAAA;KAQR;IACD,EAAC,OAPC,EAAQD,uBAAA,EAAwB,QAAA,EAAU,0BAAA,EAA2B;CAQxE,CAPC,CAAC;;ADzIP;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}