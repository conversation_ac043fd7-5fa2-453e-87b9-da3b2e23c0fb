{"_args": [["arraybuffer.slice@0.0.6", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "arraybuffer.slice@0.0.6", "_id": "arraybuffer.slice@0.0.6", "_inBundle": false, "_integrity": "sha1-8zshWfBTKj8xB6JywMz70a0peco=", "_location": "/arraybuffer.slice", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "arraybuffer.slice@0.0.6", "name": "arraybuffer.slice", "escapedName": "arraybuffer.slice", "rawSpec": "0.0.6", "saveSpec": null, "fetchSpec": "0.0.6"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/arraybuffer.slice/-/arraybuffer.slice-0.0.6.tgz", "_spec": "0.0.6", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/rase-/arraybuffer.slice/issues"}, "dependencies": {}, "description": "Exports a function for slicing ArrayBuffers (no polyfilling)", "devDependencies": {"expect.js": "0.2.0", "mocha": "1.17.1"}, "homepage": "https://github.com/rase-/arraybuffer.slice", "name": "arraybuffer.slice", "repository": {"type": "git", "url": "git+ssh://**************/rase-/arraybuffer.slice.git"}, "version": "0.0.6"}