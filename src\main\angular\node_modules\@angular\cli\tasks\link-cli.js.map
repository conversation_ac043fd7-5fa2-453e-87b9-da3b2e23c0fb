{"version": 3, "file": "link-cli.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["tasks/link-cli.ts"], "names": [], "mappings": ";;AAAA,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACrD,+BAA+B;AAC/B,iDAAmC;AAEnC,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE;QACH,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnB,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzC,EAAE,CAAC,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC;YACjC,cAAc,GAAG,KAAK,CAAC;QACzB,CAAC;QAED,MAAM,CAAC,IAAI,OAAO,CAAC,UAAS,OAAO,EAAE,MAAM;YACzC,oBAAI,CAAC,GAAG,cAAc,oBAAoB,EAAE,CAAC,GAAG;gBAC9C,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACR,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,cAAc,sBAAsB,CAAC,CAAC,CAAC;oBAC9E,MAAM,EAAE,CAAC;gBACX,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC;oBAClE,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC"}