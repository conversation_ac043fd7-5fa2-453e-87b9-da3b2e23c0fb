{"_args": [["cacheable-request@2.1.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "cacheable-request@2.1.4", "_id": "cacheable-request@2.1.4", "_inBundle": false, "_integrity": "sha1-DYCIAbY0KtM8kd+dC0TcCbkeXD0=", "_location": "/cacheable-request", "_phantomChildren": {"decode-uri-component": "0.2.0", "is-plain-obj": "1.1.0", "object-assign": "4.1.1", "strict-uri-encode": "1.1.0"}, "_requested": {"type": "version", "registry": true, "raw": "cacheable-request@2.1.4", "name": "cacheable-request", "escapedName": "cacheable-request", "rawSpec": "2.1.4", "saveSpec": null, "fetchSpec": "2.1.4"}, "_requiredBy": ["/download/got"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/cacheable-request/-/cacheable-request-2.1.4.tgz", "_spec": "2.1.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "dependencies": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "devDependencies": {"@keyv/sqlite": "^1.2.6", "ava": "^0.24.0", "coveralls": "^3.0.0", "create-test-server": "^2.0.0", "delay": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "sqlite3": "^3.1.9", "this": "^1.0.2", "xo": "^0.19.0"}, "homepage": "https://github.com/lukechilds/cacheable-request", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "license": "MIT", "main": "src/index.js", "name": "cacheable-request", "repository": {"type": "git", "url": "git+https://github.com/lukechilds/cacheable-request.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "2.1.4", "xo": {"extends": "xo-lukechilds"}}