{"_from": "@types/pdfjs-dist@^0.1.1", "_id": "@types/pdfjs-dist@0.1.2", "_inBundle": false, "_integrity": "sha512-BvRLWz6RCI8FMKbgfdTCadVwimUv8920gLsnBEAkECjtqIy95jtt+G1ebNQE2b8PTnLjJICPpmBOGhgkSsiPKA==", "_location": "/@types/pdfjs-dist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/pdfjs-dist@^0.1.1", "name": "@types/pdfjs-dist", "escapedName": "@types%2fpdfjs-dist", "scope": "@types", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/ng2-pdf-viewer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@types/pdfjs-dist/-/pdfjs-dist-0.1.2.tgz", "_shasum": "91a28961916deab21745482bb26242005fb5b7c7", "_spec": "@types/pdfjs-dist@^0.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular\\node_modules\\ng2-pdf-viewer", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/jbaldwin"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for PDF.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/pdfjs-dist", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.0", "typesPublisherContentHash": "014ea0bcd4b1eeb8ea38021b1f4773e91129b536211ee1af68a1aae83ad6fb8d", "version": "0.1.2"}