[{"__symbolic": "module", "version": 3, "metadata": {"AbstractJsEmitterVisitor": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./abstract_emitter", "name": "AbstractEmitterVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "_visitClassConstructor": [{"__symbolic": "method"}], "_visitClassGetter": [{"__symbolic": "method"}], "_visitClassMethod": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "_visitParams": [{"__symbolic": "method"}], "getBuiltinMethodName": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"AbstractJsEmitterVisitor": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./abstract_emitter", "name": "AbstractEmitterVisitor"}, "members": {"__ctor__": [{"__symbolic": "constructor"}], "visitDeclareClassStmt": [{"__symbolic": "method"}], "_visitClassConstructor": [{"__symbolic": "method"}], "_visitClassGetter": [{"__symbolic": "method"}], "_visitClassMethod": [{"__symbolic": "method"}], "visitReadVarExpr": [{"__symbolic": "method"}], "visitDeclareVarStmt": [{"__symbolic": "method"}], "visitCastExpr": [{"__symbolic": "method"}], "visitInvokeFunctionExpr": [{"__symbolic": "method"}], "visitFunctionExpr": [{"__symbolic": "method"}], "visitDeclareFunctionStmt": [{"__symbolic": "method"}], "visitTryCatchStmt": [{"__symbolic": "method"}], "_visitParams": [{"__symbolic": "method"}], "getBuiltinMethodName": [{"__symbolic": "method"}]}}}}]