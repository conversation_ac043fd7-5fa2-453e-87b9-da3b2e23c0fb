{"version": 3, "file": "testing.js", "sources": ["../../../../../packages/common/testing/index.ts", "../../../../../packages/common/testing/src/testing.ts", "../../../../../packages/common/testing/src/mock_location_strategy.ts", "../../../../../packages/common/testing/src/location_mock.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\nexport {SpyLocation} from './location_mock';\nexport {MockLocationStrategy} from './mock_location_strategy';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @stable\n */\n\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  constructor() { super(); }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n\n  path(includeHash: boolean = false): string { return this.internalPath; }\n\n  prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  pushState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  replaceState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  onPopState(fn: (value: any) => void): void { this._subject.subscribe({next: fn}); }\n\n  getBaseHref(): string { return this.internalBaseHref; }\n\n  back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  forward(): void { throw 'not implemented'; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @experimental\n */\n\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '')];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _baseHref: string = '';\n  /** @internal */\n  _platformStrategy: LocationStrategy = null !;\n\n  setInitialPath(url: string) { this._history[this._historyIndex].path = url; }\n\n  setBaseHref(url: string) { this._baseHref = url; }\n\n  path(): string { return this._history[this._historyIndex].path; }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath =\n        this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n  }\n\n  simulateUrlPop(pathname: string) { this._subject.emit({'url': pathname, 'pop': true}); }\n\n  simulateHashChange(pathname: string) {\n    // Because we don't prevent the native event, the browser will independently update the path\n    this.setInitialPath(pathname);\n    this.urlChanges.push('hash: ' + pathname);\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._baseHref + url;\n  }\n\n  go(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query));\n    this._historyIndex = this._history.length - 1;\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push(url);\n    this._subject.emit({'url': url, 'pop': false});\n  }\n\n  replaceState(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push('replace: ' + url);\n  }\n\n  forward() {\n    if (this._historyIndex < (this._history.length - 1)) {\n      this._historyIndex++;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  subscribe(\n      onNext: (value: any) => void, onThrow?: ((error: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  normalize(url: string): string { return null !; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass LocationState {\n  path: string;\n  query: string;\n  constructor(path: string, query: string) {\n    this.path = path;\n    this.query = query;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": [], "mappings": ";;;AGAA;;;;;;;AASA,AAGA;;;;;AAMA,AAAA,MAAA,WAAA,CAAA;IAAA,WAAA,GAAA;QACE,IAAF,CAAA,UAAY,GAAa,EAAE,CAAC;QAClB,IAAV,CAAA,QAAkB,GAAoB,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,IAAV,CAAA,aAAuB,GAAW,CAAC,CAAC;;QAElC,IAAF,CAAA,QAAU,GAAsB,IAAI,YAAY,EAAE,CAAC;;QAEjD,IAAF,CAAA,SAAW,GAAW,EAAE,CAAC;;QAEvB,IAAF,CAAA,iBAAmB,GAAqB,IAAM,CAAC;KA6F9C;IA3FC,cAAc,CAAC,GAAW,EAA5B,EAAgC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;IAE7E,WAAW,CAAC,GAAW,EAAzB,EAA6B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE;IAElD,IAAI,GAAN,EAAmB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE;IAEjE,oBAAoB,CAAC,IAAY,EAAE,KAArC,GAAqD,EAAE,EAAvD;QACI,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACjF,MAAM,QAAQ,GACV,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAE/F,OAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;KACxE;IAED,cAAc,CAAC,QAAgB,EAAjC,EAAqC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC,EAAE;IAExF,kBAAkB,CAAC,QAAgB,EAArC;;QAEI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAC,CAAC,CAAC;KAC1E;IAED,kBAAkB,CAAC,GAAW,EAAhC;QACI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAC1C,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;SACjB;QACD,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KAC7B;IAED,EAAE,CAAC,IAAY,EAAE,KAAnB,GAAmC,EAAE,EAArC;QACI,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAE9C,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,KAAK,EAAE;YAC9D,OAAO;SACR;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC;KAChD;IAED,YAAY,CAAC,IAAY,EAAE,KAA7B,GAA6C,EAAE,EAA/C;QACI,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE;YAClD,OAAO;SACR;QAED,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAEtB,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;KACzC;IAED,OAAO,GAAT;QACI,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;SACvD;KACF;IAED,IAAI,GAAN;QACI,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;SACvD;KACF;IAED,SAAS,CACL,MAA4B,EAAE,OAAqC,EACnE,QAA4B,EAFlC;QAGI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;KACpF;IAED,SAAS,CAAC,GAAW,EAAvB,EAAmC,OAAO,IAAM,CAAC,EAAE;;AAC5C,WAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,WAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;AAGF,MAAA,aAAA,CAAA;IAGE,WAAF,CAAc,IAAY,EAAE,KAAa,EAAzC;QACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;CACF;;ADjID;;;;;;;AAQA,AACA,AAIA;;;;;;AAOA,AAAA,MAAA,oBAAkC,SAAQ,gBAAgB,CAA1D;IAOE,WAAF,GAAA;QAAkB,KAAK,EAAE,CAAC;QANxB,IAAF,CAAA,gBAAkB,GAAW,GAAG,CAAC;QAC/B,IAAF,CAAA,YAAc,GAAW,GAAG,CAAC;QAC3B,IAAF,CAAA,aAAe,GAAW,EAAE,CAAC;QAC3B,IAAF,CAAA,UAAY,GAAa,EAAE,CAAC;;QAE1B,IAAF,CAAA,QAAU,GAAsB,IAAI,YAAY,EAAE,CAAC;KACvB;IAE1B,gBAAgB,CAAC,GAAW,EAA9B;QACI,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACzD;IAED,IAAI,CAAC,WAAP,GAA8B,KAAK,EAAnC,EAA+C,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE;IAExE,kBAAkB,CAAC,QAAgB,EAArC;QACI,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACtD;QACD,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;KACzC;IAED,SAAS,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAhE;QACI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACnC;IAED,YAAY,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAnE;QACI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;KACjD;IAED,UAAU,CAAC,EAAwB,EAArC,EAA+C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC,EAAE;IAEnF,WAAW,GAAb,EAA0B,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE;IAEvD,IAAI,GAAN;QACI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9F,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAChC;KACF;IAED,OAAO,GAAT,EAAoB,MAAM,iBAAiB,CAAC,EAAE;;AACvC,oBAAP,CAAA,UAAiB,GAA0B;IAC3C,EAAE,IAAI,EAAE,UAAU,EAAE;CACnB,CAAC;;AAEK,oBAAP,CAAA,cAAqB,GAAmE,MAAM,EAC7F,CAAC;AAGF,MAAA,kBAAA,CAAA;IAGE,WAAF,CAAqB,MAAc,EAAnC;QAAqB,IAArB,CAAA,MAA2B,GAAN,MAAM,CAAQ;QAFjC,IAAF,CAAA,GAAK,GAAY,IAAI,CAAC;QACpB,IAAF,CAAA,IAAM,GAAW,UAAU,CAAC;KACW;CACtC;;ADxFD;;;;;;;;;;;GAYG,AACH,AACA,AAA8D;;ADd9D;;;;;;;;;;;GAYG,AAEH,AAA8B;;"}