[{"__symbolic": "module", "version": 3, "metadata": {"MessageBundle": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 25, "character": 30}, {"__symbolic": "reference", "name": "string"}]}], "updateFromTemplate": [{"__symbolic": "method"}], "getMessages": [{"__symbolic": "method"}], "write": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MessageBundle": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../ml_parser/html_parser", "name": "HtmlParser"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 25, "character": 30}, {"__symbolic": "reference", "name": "string"}]}], "updateFromTemplate": [{"__symbolic": "method"}], "getMessages": [{"__symbolic": "method"}], "write": [{"__symbolic": "method"}]}}}}]