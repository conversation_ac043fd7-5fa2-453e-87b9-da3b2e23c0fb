/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],factory):factory((global.ng=global.ng||{},global.ng.common=global.ng.common||{}),global.ng.core)}(this,function(exports,_angular_core){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}function _stripBaseHref(baseHref,url){return baseHref&&url.startsWith(baseHref)?url.substring(baseHref.length):url}function _stripIndexHtml(url){return url.replace(/\/index.html$/,"")}function getPluralCategory(value,cases,ngLocalization){var key="="+value;if(cases.indexOf(key)>-1)return key;if(key=ngLocalization.getPluralCategory(value),cases.indexOf(key)>-1)return key;if(cases.indexOf("other")>-1)return"other";throw new Error('No plural message found for value "'+value+'"')}function getPluralCase(locale,nLike){"string"==typeof nLike&&(nLike=parseInt(nLike,10));var n=nLike,nDecimal=n.toString().replace(/^[^.]*\.?/,""),i=Math.floor(Math.abs(n)),v=nDecimal.length,f=parseInt(nDecimal,10),t=parseInt(n.toString().replace(/^[^.]*\.?|0+$/g,""),10)||0,lang=locale.split("-")[0].toLowerCase();switch(lang){case"af":case"asa":case"az":case"bem":case"bez":case"bg":case"brx":case"ce":case"cgg":case"chr":case"ckb":case"ee":case"el":case"eo":case"es":case"eu":case"fo":case"fur":case"gsw":case"ha":case"haw":case"hu":case"jgo":case"jmc":case"ka":case"kk":case"kkj":case"kl":case"ks":case"ksb":case"ky":case"lb":case"lg":case"mas":case"mgo":case"ml":case"mn":case"nb":case"nd":case"ne":case"nn":case"nnh":case"nyn":case"om":case"or":case"os":case"ps":case"rm":case"rof":case"rwk":case"saq":case"seh":case"sn":case"so":case"sq":case"ta":case"te":case"teo":case"tk":case"tr":case"ug":case"uz":case"vo":case"vun":case"wae":case"xog":return 1===n?Plural.One:Plural.Other;case"ak":case"ln":case"mg":case"pa":case"ti":return n===Math.floor(n)&&n>=0&&n<=1?Plural.One:Plural.Other;case"am":case"as":case"bn":case"fa":case"gu":case"hi":case"kn":case"mr":case"zu":return 0===i||1===n?Plural.One:Plural.Other;case"ar":return 0===n?Plural.Zero:1===n?Plural.One:2===n?Plural.Two:n%100===Math.floor(n%100)&&n%100>=3&&n%100<=10?Plural.Few:n%100===Math.floor(n%100)&&n%100>=11&&n%100<=99?Plural.Many:Plural.Other;case"ast":case"ca":case"de":case"en":case"et":case"fi":case"fy":case"gl":case"it":case"nl":case"sv":case"sw":case"ur":case"yi":return 1===i&&0===v?Plural.One:Plural.Other;case"be":return n%10===1&&n%100!==11?Plural.One:n%10===Math.floor(n%10)&&n%10>=2&&n%10<=4&&!(n%100>=12&&n%100<=14)?Plural.Few:n%10===0||n%10===Math.floor(n%10)&&n%10>=5&&n%10<=9||n%100===Math.floor(n%100)&&n%100>=11&&n%100<=14?Plural.Many:Plural.Other;case"br":return n%10===1&&n%100!==11&&n%100!==71&&n%100!==91?Plural.One:n%10===2&&n%100!==12&&n%100!==72&&n%100!==92?Plural.Two:n%10===Math.floor(n%10)&&(n%10>=3&&n%10<=4||n%10===9)&&!(n%100>=10&&n%100<=19||n%100>=70&&n%100<=79||n%100>=90&&n%100<=99)?Plural.Few:0!==n&&n%1e6===0?Plural.Many:Plural.Other;case"bs":case"hr":case"sr":return 0===v&&i%10===1&&i%100!==11||f%10===1&&f%100!==11?Plural.One:0===v&&i%10===Math.floor(i%10)&&i%10>=2&&i%10<=4&&!(i%100>=12&&i%100<=14)||f%10===Math.floor(f%10)&&f%10>=2&&f%10<=4&&!(f%100>=12&&f%100<=14)?Plural.Few:Plural.Other;case"cs":case"sk":return 1===i&&0===v?Plural.One:i===Math.floor(i)&&i>=2&&i<=4&&0===v?Plural.Few:0!==v?Plural.Many:Plural.Other;case"cy":return 0===n?Plural.Zero:1===n?Plural.One:2===n?Plural.Two:3===n?Plural.Few:6===n?Plural.Many:Plural.Other;case"da":return 1===n||0!==t&&(0===i||1===i)?Plural.One:Plural.Other;case"dsb":case"hsb":return 0===v&&i%100===1||f%100===1?Plural.One:0===v&&i%100===2||f%100===2?Plural.Two:0===v&&i%100===Math.floor(i%100)&&i%100>=3&&i%100<=4||f%100===Math.floor(f%100)&&f%100>=3&&f%100<=4?Plural.Few:Plural.Other;case"ff":case"fr":case"hy":case"kab":return 0===i||1===i?Plural.One:Plural.Other;case"fil":return 0===v&&(1===i||2===i||3===i)||0===v&&i%10!==4&&i%10!==6&&i%10!==9||0!==v&&f%10!==4&&f%10!==6&&f%10!==9?Plural.One:Plural.Other;case"ga":return 1===n?Plural.One:2===n?Plural.Two:n===Math.floor(n)&&n>=3&&n<=6?Plural.Few:n===Math.floor(n)&&n>=7&&n<=10?Plural.Many:Plural.Other;case"gd":return 1===n||11===n?Plural.One:2===n||12===n?Plural.Two:n===Math.floor(n)&&(n>=3&&n<=10||n>=13&&n<=19)?Plural.Few:Plural.Other;case"gv":return 0===v&&i%10===1?Plural.One:0===v&&i%10===2?Plural.Two:0!==v||i%100!==0&&i%100!==20&&i%100!==40&&i%100!==60&&i%100!==80?0!==v?Plural.Many:Plural.Other:Plural.Few;case"he":return 1===i&&0===v?Plural.One:2===i&&0===v?Plural.Two:0!==v||n>=0&&n<=10||n%10!==0?Plural.Other:Plural.Many;case"is":return 0===t&&i%10===1&&i%100!==11||0!==t?Plural.One:Plural.Other;case"ksh":return 0===n?Plural.Zero:1===n?Plural.One:Plural.Other;case"kw":case"naq":case"se":case"smn":return 1===n?Plural.One:2===n?Plural.Two:Plural.Other;case"lag":return 0===n?Plural.Zero:0!==i&&1!==i||0===n?Plural.Other:Plural.One;case"lt":return n%10!==1||n%100>=11&&n%100<=19?n%10===Math.floor(n%10)&&n%10>=2&&n%10<=9&&!(n%100>=11&&n%100<=19)?Plural.Few:0!==f?Plural.Many:Plural.Other:Plural.One;case"lv":case"prg":return n%10===0||n%100===Math.floor(n%100)&&n%100>=11&&n%100<=19||2===v&&f%100===Math.floor(f%100)&&f%100>=11&&f%100<=19?Plural.Zero:n%10===1&&n%100!==11||2===v&&f%10===1&&f%100!==11||2!==v&&f%10===1?Plural.One:Plural.Other;case"mk":return 0===v&&i%10===1||f%10===1?Plural.One:Plural.Other;case"mt":return 1===n?Plural.One:0===n||n%100===Math.floor(n%100)&&n%100>=2&&n%100<=10?Plural.Few:n%100===Math.floor(n%100)&&n%100>=11&&n%100<=19?Plural.Many:Plural.Other;case"pl":return 1===i&&0===v?Plural.One:0===v&&i%10===Math.floor(i%10)&&i%10>=2&&i%10<=4&&!(i%100>=12&&i%100<=14)?Plural.Few:0===v&&1!==i&&i%10===Math.floor(i%10)&&i%10>=0&&i%10<=1||0===v&&i%10===Math.floor(i%10)&&i%10>=5&&i%10<=9||0===v&&i%100===Math.floor(i%100)&&i%100>=12&&i%100<=14?Plural.Many:Plural.Other;case"pt":return n===Math.floor(n)&&n>=0&&n<=2&&2!==n?Plural.One:Plural.Other;case"ro":return 1===i&&0===v?Plural.One:0!==v||0===n||1!==n&&n%100===Math.floor(n%100)&&n%100>=1&&n%100<=19?Plural.Few:Plural.Other;case"ru":case"uk":return 0===v&&i%10===1&&i%100!==11?Plural.One:0===v&&i%10===Math.floor(i%10)&&i%10>=2&&i%10<=4&&!(i%100>=12&&i%100<=14)?Plural.Few:0===v&&i%10===0||0===v&&i%10===Math.floor(i%10)&&i%10>=5&&i%10<=9||0===v&&i%100===Math.floor(i%100)&&i%100>=11&&i%100<=14?Plural.Many:Plural.Other;case"shi":return 0===i||1===n?Plural.One:n===Math.floor(n)&&n>=2&&n<=10?Plural.Few:Plural.Other;case"si":return 0===n||1===n||0===i&&1===f?Plural.One:Plural.Other;case"sl":return 0===v&&i%100===1?Plural.One:0===v&&i%100===2?Plural.Two:0===v&&i%100===Math.floor(i%100)&&i%100>=3&&i%100<=4||0!==v?Plural.Few:Plural.Other;case"tzm":return n===Math.floor(n)&&n>=0&&n<=1||n===Math.floor(n)&&n>=11&&n<=99?Plural.One:Plural.Other;default:return Plural.Other}}function getTypeNameForDebugging(type){return type.name||typeof type}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function invalidPipeArgumentError(type,value){return Error("InvalidPipeArgument: '"+value+"' for pipe '"+_angular_core.ɵstringify(type)+"'")}function titleCaseWord(word){return word?word[0].toUpperCase()+word.substr(1).toLowerCase():word}function digitModifier(inner){return function(date,locale){var result=inner(date,locale);return 1==result.length?"0"+result:result}}function hourClockExtractor(inner){return function(date,locale){return inner(date,locale).split(" ")[1]}}function hourExtractor(inner){return function(date,locale){return inner(date,locale).split(" ")[0]}}function intlDateFormat(date,locale,options){return new Intl.DateTimeFormat(locale,options).format(date).replace(/[\u200e\u200f]/g,"")}function timeZoneGetter(timezone){var options={hour:"2-digit",hour12:!1,timeZoneName:timezone};return function(date,locale){var result=intlDateFormat(date,locale,options);return result?result.substring(3):""}}function hour12Modify(options,value){return options.hour12=value,options}function digitCondition(prop,len){var result={};return result[prop]=2===len?"2-digit":"numeric",result}function nameCondition(prop,len){var result={};return len<4?result[prop]=len>1?"short":"narrow":result[prop]="long",result}function combine(options){return Object.assign.apply(Object,[{}].concat(options))}function datePartGetterFactory(ret){return function(date,locale){return intlDateFormat(date,locale,ret)}}function dateFormatter(format,date,locale){var fn=PATTERN_ALIASES[format];if(fn)return fn(date,locale);var cacheKey=format,parts=DATE_FORMATTER_CACHE.get(cacheKey);if(!parts){parts=[];var match=void 0;DATE_FORMATS_SPLIT.exec(format);for(var _format=format;_format;)match=DATE_FORMATS_SPLIT.exec(_format),match?(parts=parts.concat(match.slice(1)),_format=parts.pop()):(parts.push(_format),_format=null);DATE_FORMATTER_CACHE.set(cacheKey,parts)}return parts.reduce(function(text,part){var fn=DATE_FORMATS[part];return text+(fn?fn(date,locale):partToTime(part))},"")}function partToTime(part){return"''"===part?"'":part.replace(/(^'|'$)/g,"").replace(/''/g,"'")}function formatNumber(pipe,locale,value,style,digits,currency,currencyAsSymbol){if(void 0===currency&&(currency=null),void 0===currencyAsSymbol&&(currencyAsSymbol=!1),null==value)return null;if(value="string"==typeof value&&isNumeric(value)?+value:value,"number"!=typeof value)throw invalidPipeArgumentError(pipe,value);var minInt=void 0,minFraction=void 0,maxFraction=void 0;if(style!==NumberFormatStyle.Currency&&(minInt=1,minFraction=0,maxFraction=3),digits){var parts=digits.match(_NUMBER_FORMAT_REGEXP);if(null===parts)throw new Error(digits+" is not a valid digit info for number pipes");null!=parts[1]&&(minInt=parseIntAutoRadix(parts[1])),null!=parts[3]&&(minFraction=parseIntAutoRadix(parts[3])),null!=parts[5]&&(maxFraction=parseIntAutoRadix(parts[5]))}return NumberFormatter.format(value,locale,style,{minimumIntegerDigits:minInt,minimumFractionDigits:minFraction,maximumFractionDigits:maxFraction,currency:currency,currencyAsSymbol:currencyAsSymbol})}function parseIntAutoRadix(text){var result=parseInt(text);if(isNaN(result))throw new Error("Invalid integer literal when parsing "+text);return result}function isNumeric(value){return!isNaN(value-parseFloat(value))}function isBlank(obj){return null==obj||""===obj}function isDate(obj){return obj instanceof Date&&!isNaN(obj.valueOf())}function isoStringToDate(match){var date=new Date(0),tzHour=0,tzMin=0,dateSetter=match[8]?date.setUTCFullYear:date.setFullYear,timeSetter=match[8]?date.setUTCHours:date.setHours;match[9]&&(tzHour=toInt(match[9]+match[10]),tzMin=toInt(match[9]+match[11])),dateSetter.call(date,toInt(match[1]),toInt(match[2])-1,toInt(match[3]));var h=toInt(match[4]||"0")-tzHour,m=toInt(match[5]||"0")-tzMin,s=toInt(match[6]||"0"),ms=Math.round(1e3*parseFloat("0."+(match[7]||0)));return timeSetter.call(date,h,m,s,ms),date}function toInt(str){return parseInt(str,10)}function isPlatformBrowser(platformId){return platformId===PLATFORM_BROWSER_ID}function isPlatformServer(platformId){return platformId===PLATFORM_SERVER_ID}function isPlatformWorkerApp(platformId){return platformId===PLATFORM_WORKER_APP_ID}function isPlatformWorkerUi(platformId){return platformId===PLATFORM_WORKER_UI_ID}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},PlatformLocation=function(){function PlatformLocation(){}return PlatformLocation.prototype.getBaseHrefFromDOM=function(){},PlatformLocation.prototype.onPopState=function(fn){},PlatformLocation.prototype.onHashChange=function(fn){},PlatformLocation.prototype.pathname=function(){},PlatformLocation.prototype.search=function(){},PlatformLocation.prototype.hash=function(){},PlatformLocation.prototype.replaceState=function(state,title,url){},PlatformLocation.prototype.pushState=function(state,title,url){},PlatformLocation.prototype.forward=function(){},PlatformLocation.prototype.back=function(){},PlatformLocation}(),LOCATION_INITIALIZED=new _angular_core.InjectionToken("Location Initialized"),LocationStrategy=function(){function LocationStrategy(){}return LocationStrategy.prototype.path=function(includeHash){},LocationStrategy.prototype.prepareExternalUrl=function(internal){},LocationStrategy.prototype.pushState=function(state,title,url,queryParams){},LocationStrategy.prototype.replaceState=function(state,title,url,queryParams){},LocationStrategy.prototype.forward=function(){},LocationStrategy.prototype.back=function(){},LocationStrategy.prototype.onPopState=function(fn){},LocationStrategy.prototype.getBaseHref=function(){},LocationStrategy}(),APP_BASE_HREF=new _angular_core.InjectionToken("appBaseHref"),Location=function(){function Location(platformStrategy){var _this=this;this._subject=new _angular_core.EventEmitter,this._platformStrategy=platformStrategy;var browserBaseHref=this._platformStrategy.getBaseHref();this._baseHref=Location.stripTrailingSlash(_stripIndexHtml(browserBaseHref)),this._platformStrategy.onPopState(function(ev){_this._subject.emit({url:_this.path(!0),pop:!0,type:ev.type})})}return Location.prototype.path=function(includeHash){return void 0===includeHash&&(includeHash=!1),this.normalize(this._platformStrategy.path(includeHash))},Location.prototype.isCurrentPathEqualTo=function(path,query){return void 0===query&&(query=""),this.path()==this.normalize(path+Location.normalizeQueryParams(query))},Location.prototype.normalize=function(url){return Location.stripTrailingSlash(_stripBaseHref(this._baseHref,_stripIndexHtml(url)))},Location.prototype.prepareExternalUrl=function(url){return url&&"/"!==url[0]&&(url="/"+url),this._platformStrategy.prepareExternalUrl(url)},Location.prototype.go=function(path,query){void 0===query&&(query=""),this._platformStrategy.pushState(null,"",path,query)},Location.prototype.replaceState=function(path,query){void 0===query&&(query=""),this._platformStrategy.replaceState(null,"",path,query)},Location.prototype.forward=function(){this._platformStrategy.forward()},Location.prototype.back=function(){this._platformStrategy.back()},Location.prototype.subscribe=function(onNext,onThrow,onReturn){return this._subject.subscribe({next:onNext,error:onThrow,complete:onReturn})},Location.normalizeQueryParams=function(params){return params&&"?"!==params[0]?"?"+params:params},Location.joinWithSlash=function(start,end){if(0==start.length)return end;if(0==end.length)return start;var slashes=0;return start.endsWith("/")&&slashes++,end.startsWith("/")&&slashes++,2==slashes?start+end.substring(1):1==slashes?start+end:start+"/"+end},Location.stripTrailingSlash=function(url){var match=url.match(/#|\?|$/),pathEndIdx=match&&match.index||url.length,droppedSlashIdx=pathEndIdx-("/"===url[pathEndIdx-1]?1:0);return url.slice(0,droppedSlashIdx)+url.slice(pathEndIdx)},Location}();Location.decorators=[{type:_angular_core.Injectable}],Location.ctorParameters=function(){return[{type:LocationStrategy}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var HashLocationStrategy=function(_super){function HashLocationStrategy(_platformLocation,_baseHref){var _this=_super.call(this)||this;return _this._platformLocation=_platformLocation,_this._baseHref="",null!=_baseHref&&(_this._baseHref=_baseHref),_this}return __extends(HashLocationStrategy,_super),HashLocationStrategy.prototype.onPopState=function(fn){this._platformLocation.onPopState(fn),this._platformLocation.onHashChange(fn)},HashLocationStrategy.prototype.getBaseHref=function(){return this._baseHref},HashLocationStrategy.prototype.path=function(includeHash){void 0===includeHash&&(includeHash=!1);var path=this._platformLocation.hash;return null==path&&(path="#"),path.length>0?path.substring(1):path},HashLocationStrategy.prototype.prepareExternalUrl=function(internal){var url=Location.joinWithSlash(this._baseHref,internal);return url.length>0?"#"+url:url},HashLocationStrategy.prototype.pushState=function(state,title,path,queryParams){var url=this.prepareExternalUrl(path+Location.normalizeQueryParams(queryParams));0==url.length&&(url=this._platformLocation.pathname),this._platformLocation.pushState(state,title,url)},HashLocationStrategy.prototype.replaceState=function(state,title,path,queryParams){var url=this.prepareExternalUrl(path+Location.normalizeQueryParams(queryParams));0==url.length&&(url=this._platformLocation.pathname),this._platformLocation.replaceState(state,title,url)},HashLocationStrategy.prototype.forward=function(){this._platformLocation.forward()},HashLocationStrategy.prototype.back=function(){this._platformLocation.back()},HashLocationStrategy}(LocationStrategy);HashLocationStrategy.decorators=[{type:_angular_core.Injectable}],HashLocationStrategy.ctorParameters=function(){return[{type:PlatformLocation},{type:void 0,decorators:[{type:_angular_core.Optional},{type:_angular_core.Inject,args:[APP_BASE_HREF]}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var PathLocationStrategy=function(_super){function PathLocationStrategy(_platformLocation,href){var _this=_super.call(this)||this;if(_this._platformLocation=_platformLocation,null==href&&(href=_this._platformLocation.getBaseHrefFromDOM()),null==href)throw new Error("No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.");return _this._baseHref=href,_this}return __extends(PathLocationStrategy,_super),PathLocationStrategy.prototype.onPopState=function(fn){this._platformLocation.onPopState(fn),this._platformLocation.onHashChange(fn)},PathLocationStrategy.prototype.getBaseHref=function(){return this._baseHref},PathLocationStrategy.prototype.prepareExternalUrl=function(internal){return Location.joinWithSlash(this._baseHref,internal)},PathLocationStrategy.prototype.path=function(includeHash){void 0===includeHash&&(includeHash=!1);var pathname=this._platformLocation.pathname+Location.normalizeQueryParams(this._platformLocation.search),hash=this._platformLocation.hash;return hash&&includeHash?""+pathname+hash:pathname},PathLocationStrategy.prototype.pushState=function(state,title,url,queryParams){var externalUrl=this.prepareExternalUrl(url+Location.normalizeQueryParams(queryParams));this._platformLocation.pushState(state,title,externalUrl)},PathLocationStrategy.prototype.replaceState=function(state,title,url,queryParams){var externalUrl=this.prepareExternalUrl(url+Location.normalizeQueryParams(queryParams));this._platformLocation.replaceState(state,title,externalUrl)},PathLocationStrategy.prototype.forward=function(){this._platformLocation.forward()},PathLocationStrategy.prototype.back=function(){this._platformLocation.back()},PathLocationStrategy}(LocationStrategy);PathLocationStrategy.decorators=[{type:_angular_core.Injectable}],PathLocationStrategy.ctorParameters=function(){return[{type:PlatformLocation},{type:void 0,decorators:[{type:_angular_core.Optional},{type:_angular_core.Inject,args:[APP_BASE_HREF]}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgLocalization=function(){function NgLocalization(){}return NgLocalization.prototype.getPluralCategory=function(value){},NgLocalization}(),NgLocaleLocalization=function(_super){function NgLocaleLocalization(locale){var _this=_super.call(this)||this;return _this.locale=locale,_this}return __extends(NgLocaleLocalization,_super),NgLocaleLocalization.prototype.getPluralCategory=function(value){var plural=getPluralCase(this.locale,value);switch(plural){case Plural.Zero:return"zero";case Plural.One:return"one";case Plural.Two:return"two";case Plural.Few:return"few";case Plural.Many:return"many";default:return"other"}},NgLocaleLocalization}(NgLocalization);NgLocaleLocalization.decorators=[{type:_angular_core.Injectable}],NgLocaleLocalization.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.LOCALE_ID]}]}]};var Plural={};Plural.Zero=0,Plural.One=1,Plural.Two=2,Plural.Few=3,Plural.Many=4,Plural.Other=5,Plural[Plural.Zero]="Zero",Plural[Plural.One]="One",Plural[Plural.Two]="Two",Plural[Plural.Few]="Few",Plural[Plural.Many]="Many",Plural[Plural.Other]="Other";/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgClass=function(){function NgClass(_iterableDiffers,_keyValueDiffers,_ngEl,_renderer){this._iterableDiffers=_iterableDiffers,this._keyValueDiffers=_keyValueDiffers,this._ngEl=_ngEl,this._renderer=_renderer,this._initialClasses=[]}return Object.defineProperty(NgClass.prototype,"klass",{set:function(v){this._applyInitialClasses(!0),this._initialClasses="string"==typeof v?v.split(/\s+/):[],this._applyInitialClasses(!1),this._applyClasses(this._rawClass,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(NgClass.prototype,"ngClass",{set:function(v){this._cleanupClasses(this._rawClass),this._iterableDiffer=null,this._keyValueDiffer=null,this._rawClass="string"==typeof v?v.split(/\s+/):v,this._rawClass&&(_angular_core.ɵisListLikeIterable(this._rawClass)?this._iterableDiffer=this._iterableDiffers.find(this._rawClass).create():this._keyValueDiffer=this._keyValueDiffers.find(this._rawClass).create())},enumerable:!0,configurable:!0}),NgClass.prototype.ngDoCheck=function(){if(this._iterableDiffer){var iterableChanges=this._iterableDiffer.diff(this._rawClass);iterableChanges&&this._applyIterableChanges(iterableChanges)}else if(this._keyValueDiffer){var keyValueChanges=this._keyValueDiffer.diff(this._rawClass);keyValueChanges&&this._applyKeyValueChanges(keyValueChanges)}},NgClass.prototype._cleanupClasses=function(rawClassVal){this._applyClasses(rawClassVal,!0),this._applyInitialClasses(!1)},NgClass.prototype._applyKeyValueChanges=function(changes){var _this=this;changes.forEachAddedItem(function(record){return _this._toggleClass(record.key,record.currentValue)}),changes.forEachChangedItem(function(record){return _this._toggleClass(record.key,record.currentValue)}),changes.forEachRemovedItem(function(record){record.previousValue&&_this._toggleClass(record.key,!1)})},NgClass.prototype._applyIterableChanges=function(changes){var _this=this;changes.forEachAddedItem(function(record){if("string"!=typeof record.item)throw new Error("NgClass can only toggle CSS classes expressed as strings, got "+_angular_core.ɵstringify(record.item));_this._toggleClass(record.item,!0)}),changes.forEachRemovedItem(function(record){return _this._toggleClass(record.item,!1)})},NgClass.prototype._applyInitialClasses=function(isCleanup){var _this=this;this._initialClasses.forEach(function(klass){return _this._toggleClass(klass,!isCleanup)})},NgClass.prototype._applyClasses=function(rawClassVal,isCleanup){var _this=this;rawClassVal&&(Array.isArray(rawClassVal)||rawClassVal instanceof Set?rawClassVal.forEach(function(klass){return _this._toggleClass(klass,!isCleanup)}):Object.keys(rawClassVal).forEach(function(klass){null!=rawClassVal[klass]&&_this._toggleClass(klass,!isCleanup)}))},NgClass.prototype._toggleClass=function(klass,enabled){var _this=this;klass=klass.trim(),klass&&klass.split(/\s+/g).forEach(function(klass){_this._renderer.setElementClass(_this._ngEl.nativeElement,klass,!!enabled)})},NgClass}();NgClass.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngClass]"}]}],NgClass.ctorParameters=function(){return[{type:_angular_core.IterableDiffers},{type:_angular_core.KeyValueDiffers},{type:_angular_core.ElementRef},{type:_angular_core.Renderer}]},NgClass.propDecorators={klass:[{type:_angular_core.Input,args:["class"]}],ngClass:[{type:_angular_core.Input}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgComponentOutlet=function(){function NgComponentOutlet(_viewContainerRef){this._viewContainerRef=_viewContainerRef,this._componentRef=null,this._moduleRef=null}return NgComponentOutlet.prototype.ngOnChanges=function(changes){if(this._viewContainerRef.clear(),this._componentRef=null,this.ngComponentOutlet){var elInjector=this.ngComponentOutletInjector||this._viewContainerRef.parentInjector;if(changes.ngComponentOutletNgModuleFactory)if(this._moduleRef&&this._moduleRef.destroy(),this.ngComponentOutletNgModuleFactory){var parentModule=elInjector.get(_angular_core.NgModuleRef);this._moduleRef=this.ngComponentOutletNgModuleFactory.create(parentModule.injector)}else this._moduleRef=null;var componentFactoryResolver=this._moduleRef?this._moduleRef.componentFactoryResolver:elInjector.get(_angular_core.ComponentFactoryResolver),componentFactory=componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);this._componentRef=this._viewContainerRef.createComponent(componentFactory,this._viewContainerRef.length,elInjector,this.ngComponentOutletContent)}},NgComponentOutlet.prototype.ngOnDestroy=function(){this._moduleRef&&this._moduleRef.destroy()},NgComponentOutlet}();NgComponentOutlet.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngComponentOutlet]"}]}],NgComponentOutlet.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef}]},NgComponentOutlet.propDecorators={ngComponentOutlet:[{type:_angular_core.Input}],ngComponentOutletInjector:[{type:_angular_core.Input}],ngComponentOutletContent:[{type:_angular_core.Input}],ngComponentOutletNgModuleFactory:[{type:_angular_core.Input}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgForOfContext=function(){function NgForOfContext($implicit,ngForOf,index,count){this.$implicit=$implicit,this.ngForOf=ngForOf,this.index=index,this.count=count}return Object.defineProperty(NgForOfContext.prototype,"first",{get:function(){return 0===this.index},enumerable:!0,configurable:!0}),Object.defineProperty(NgForOfContext.prototype,"last",{get:function(){return this.index===this.count-1},enumerable:!0,configurable:!0}),Object.defineProperty(NgForOfContext.prototype,"even",{get:function(){return this.index%2===0},enumerable:!0,configurable:!0}),Object.defineProperty(NgForOfContext.prototype,"odd",{get:function(){return!this.even},enumerable:!0,configurable:!0}),NgForOfContext}(),NgForOf=function(){function NgForOf(_viewContainer,_template,_differs){this._viewContainer=_viewContainer,this._template=_template,this._differs=_differs,this._differ=null}return Object.defineProperty(NgForOf.prototype,"ngForTrackBy",{get:function(){return this._trackByFn},set:function(fn){_angular_core.isDevMode()&&null!=fn&&"function"!=typeof fn&&console&&console.warn&&console.warn("trackBy must be a function, but received "+JSON.stringify(fn)+". See https://angular.io/docs/ts/latest/api/common/index/NgFor-directive.html#!#change-propagation for more information."),this._trackByFn=fn},enumerable:!0,configurable:!0}),Object.defineProperty(NgForOf.prototype,"ngForTemplate",{set:function(value){value&&(this._template=value)},enumerable:!0,configurable:!0}),NgForOf.prototype.ngOnChanges=function(changes){if("ngForOf"in changes){var value=changes.ngForOf.currentValue;if(!this._differ&&value)try{this._differ=this._differs.find(value).create(this.ngForTrackBy)}catch(e){throw new Error("Cannot find a differ supporting object '"+value+"' of type '"+getTypeNameForDebugging(value)+"'. NgFor only supports binding to Iterables such as Arrays.")}}},NgForOf.prototype.ngDoCheck=function(){if(this._differ){var changes=this._differ.diff(this.ngForOf);changes&&this._applyChanges(changes)}},NgForOf.prototype._applyChanges=function(changes){var _this=this,insertTuples=[];changes.forEachOperation(function(item,adjustedPreviousIndex,currentIndex){if(null==item.previousIndex){var view=_this._viewContainer.createEmbeddedView(_this._template,new NgForOfContext(null,_this.ngForOf,(-1),(-1)),currentIndex),tuple=new RecordViewTuple(item,view);insertTuples.push(tuple)}else if(null==currentIndex)_this._viewContainer.remove(adjustedPreviousIndex);else{var view=_this._viewContainer.get(adjustedPreviousIndex);_this._viewContainer.move(view,currentIndex);var tuple=new RecordViewTuple(item,view);insertTuples.push(tuple)}});for(var i=0;i<insertTuples.length;i++)this._perViewChange(insertTuples[i].view,insertTuples[i].record);for(var i=0,ilen=this._viewContainer.length;i<ilen;i++){var viewRef=this._viewContainer.get(i);viewRef.context.index=i,viewRef.context.count=ilen}changes.forEachIdentityChange(function(record){var viewRef=_this._viewContainer.get(record.currentIndex);viewRef.context.$implicit=record.item})},NgForOf.prototype._perViewChange=function(view,record){view.context.$implicit=record.item},NgForOf}();NgForOf.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngFor][ngForOf]"}]}],NgForOf.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef},{type:_angular_core.TemplateRef},{type:_angular_core.IterableDiffers}]},NgForOf.propDecorators={ngForOf:[{type:_angular_core.Input}],ngForTrackBy:[{type:_angular_core.Input}],ngForTemplate:[{type:_angular_core.Input}]};var RecordViewTuple=function(){function RecordViewTuple(record,view){this.record=record,this.view=view}return RecordViewTuple}(),NgFor=NgForOf,NgIf=function(){function NgIf(_viewContainer,templateRef){this._viewContainer=_viewContainer,this._context=new NgIfContext,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=templateRef}return Object.defineProperty(NgIf.prototype,"ngIf",{set:function(condition){this._context.$implicit=this._context.ngIf=condition,this._updateView()},enumerable:!0,configurable:!0}),Object.defineProperty(NgIf.prototype,"ngIfThen",{set:function(templateRef){this._thenTemplateRef=templateRef,this._thenViewRef=null,this._updateView()},enumerable:!0,configurable:!0}),Object.defineProperty(NgIf.prototype,"ngIfElse",{set:function(templateRef){this._elseTemplateRef=templateRef,this._elseViewRef=null,this._updateView()},enumerable:!0,configurable:!0}),NgIf.prototype._updateView=function(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))},NgIf}();NgIf.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngIf]"}]}],NgIf.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef},{type:_angular_core.TemplateRef}]},NgIf.propDecorators={ngIf:[{type:_angular_core.Input}],ngIfThen:[{type:_angular_core.Input}],ngIfElse:[{type:_angular_core.Input}]};var NgIfContext=function(){function NgIfContext(){this.$implicit=null,this.ngIf=null}return NgIfContext}(),SwitchView=function(){function SwitchView(_viewContainerRef,_templateRef){this._viewContainerRef=_viewContainerRef,this._templateRef=_templateRef,this._created=!1}return SwitchView.prototype.create=function(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)},SwitchView.prototype.destroy=function(){this._created=!1,this._viewContainerRef.clear()},SwitchView.prototype.enforceState=function(created){created&&!this._created?this.create():!created&&this._created&&this.destroy()},SwitchView}(),NgSwitch=function(){function NgSwitch(){this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}return Object.defineProperty(NgSwitch.prototype,"ngSwitch",{set:function(newValue){this._ngSwitch=newValue,0===this._caseCount&&this._updateDefaultCases(!0)},enumerable:!0,configurable:!0}),NgSwitch.prototype._addCase=function(){return this._caseCount++},NgSwitch.prototype._addDefault=function(view){this._defaultViews||(this._defaultViews=[]),this._defaultViews.push(view)},NgSwitch.prototype._matchCase=function(value){var matched=value==this._ngSwitch;return this._lastCasesMatched=this._lastCasesMatched||matched,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),matched},NgSwitch.prototype._updateDefaultCases=function(useDefault){if(this._defaultViews&&useDefault!==this._defaultUsed){this._defaultUsed=useDefault;for(var i=0;i<this._defaultViews.length;i++){var defaultView=this._defaultViews[i];defaultView.enforceState(useDefault)}}},NgSwitch}();NgSwitch.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngSwitch]"}]}],NgSwitch.ctorParameters=function(){return[]},NgSwitch.propDecorators={ngSwitch:[{type:_angular_core.Input}]};var NgSwitchCase=function(){function NgSwitchCase(viewContainer,templateRef,ngSwitch){this.ngSwitch=ngSwitch,ngSwitch._addCase(),this._view=new SwitchView(viewContainer,templateRef)}return NgSwitchCase.prototype.ngDoCheck=function(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))},NgSwitchCase}();NgSwitchCase.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngSwitchCase]"}]}],NgSwitchCase.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef},{type:_angular_core.TemplateRef},{type:NgSwitch,decorators:[{type:_angular_core.Host}]}]},NgSwitchCase.propDecorators={ngSwitchCase:[{type:_angular_core.Input}]};var NgSwitchDefault=function(){function NgSwitchDefault(viewContainer,templateRef,ngSwitch){ngSwitch._addDefault(new SwitchView(viewContainer,templateRef))}return NgSwitchDefault}();NgSwitchDefault.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngSwitchDefault]"}]}],NgSwitchDefault.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef},{type:_angular_core.TemplateRef},{type:NgSwitch,decorators:[{type:_angular_core.Host}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgPlural=function(){function NgPlural(_localization){this._localization=_localization,this._caseViews={}}return Object.defineProperty(NgPlural.prototype,"ngPlural",{set:function(value){this._switchValue=value,this._updateView()},enumerable:!0,configurable:!0}),NgPlural.prototype.addCase=function(value,switchView){this._caseViews[value]=switchView},NgPlural.prototype._updateView=function(){this._clearViews();var cases=Object.keys(this._caseViews),key=getPluralCategory(this._switchValue,cases,this._localization);this._activateView(this._caseViews[key])},NgPlural.prototype._clearViews=function(){this._activeView&&this._activeView.destroy()},NgPlural.prototype._activateView=function(view){view&&(this._activeView=view,this._activeView.create())},NgPlural}();NgPlural.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngPlural]"}]}],NgPlural.ctorParameters=function(){return[{type:NgLocalization}]},NgPlural.propDecorators={ngPlural:[{type:_angular_core.Input}]};var NgPluralCase=function(){function NgPluralCase(value,template,viewContainer,ngPlural){this.value=value;var isANumber=!isNaN(Number(value));ngPlural.addCase(isANumber?"="+value:value,new SwitchView(viewContainer,template))}return NgPluralCase}();NgPluralCase.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngPluralCase]"}]}],NgPluralCase.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Attribute,args:["ngPluralCase"]}]},{type:_angular_core.TemplateRef},{type:_angular_core.ViewContainerRef},{type:NgPlural,decorators:[{type:_angular_core.Host}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgStyle=function(){function NgStyle(_differs,_ngEl,_renderer){this._differs=_differs,this._ngEl=_ngEl,this._renderer=_renderer}return Object.defineProperty(NgStyle.prototype,"ngStyle",{set:function(v){this._ngStyle=v,!this._differ&&v&&(this._differ=this._differs.find(v).create())},enumerable:!0,configurable:!0}),NgStyle.prototype.ngDoCheck=function(){if(this._differ){var changes=this._differ.diff(this._ngStyle);changes&&this._applyChanges(changes)}},NgStyle.prototype._applyChanges=function(changes){var _this=this;changes.forEachRemovedItem(function(record){return _this._setStyle(record.key,null)}),changes.forEachAddedItem(function(record){return _this._setStyle(record.key,record.currentValue)}),changes.forEachChangedItem(function(record){return _this._setStyle(record.key,record.currentValue)})},NgStyle.prototype._setStyle=function(nameAndUnit,value){var _a=nameAndUnit.split("."),name=_a[0],unit=_a[1];value=null!=value&&unit?""+value+unit:value,this._renderer.setElementStyle(this._ngEl.nativeElement,name,value)},NgStyle}();NgStyle.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngStyle]"}]}],NgStyle.ctorParameters=function(){return[{type:_angular_core.KeyValueDiffers},{type:_angular_core.ElementRef},{type:_angular_core.Renderer}]},NgStyle.propDecorators={ngStyle:[{type:_angular_core.Input}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var NgTemplateOutlet=function(){function NgTemplateOutlet(_viewContainerRef){this._viewContainerRef=_viewContainerRef}return Object.defineProperty(NgTemplateOutlet.prototype,"ngOutletContext",{set:function(context){this.ngTemplateOutletContext=context},enumerable:!0,configurable:!0}),NgTemplateOutlet.prototype.ngOnChanges=function(changes){this._viewRef&&this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._viewRef)),this.ngTemplateOutlet&&(this._viewRef=this._viewContainerRef.createEmbeddedView(this.ngTemplateOutlet,this.ngTemplateOutletContext))},NgTemplateOutlet}();NgTemplateOutlet.decorators=[{type:_angular_core.Directive,args:[{selector:"[ngTemplateOutlet]"}]}],NgTemplateOutlet.ctorParameters=function(){return[{type:_angular_core.ViewContainerRef}]},NgTemplateOutlet.propDecorators={ngTemplateOutletContext:[{type:_angular_core.Input}],ngTemplateOutlet:[{type:_angular_core.Input}],ngOutletContext:[{type:_angular_core.Input}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var COMMON_DIRECTIVES=[NgClass,NgComponentOutlet,NgForOf,NgIf,NgTemplateOutlet,NgStyle,NgSwitch,NgSwitchCase,NgSwitchDefault,NgPlural,NgPluralCase],ObservableStrategy=function(){function ObservableStrategy(){}return ObservableStrategy.prototype.createSubscription=function(async,updateLatestValue){return async.subscribe({next:updateLatestValue,error:function(e){throw e}})},ObservableStrategy.prototype.dispose=function(subscription){subscription.unsubscribe()},ObservableStrategy.prototype.onDestroy=function(subscription){subscription.unsubscribe()},ObservableStrategy}(),PromiseStrategy=function(){function PromiseStrategy(){}return PromiseStrategy.prototype.createSubscription=function(async,updateLatestValue){return async.then(updateLatestValue,function(e){throw e})},PromiseStrategy.prototype.dispose=function(subscription){},PromiseStrategy.prototype.onDestroy=function(subscription){},PromiseStrategy}(),_promiseStrategy=new PromiseStrategy,_observableStrategy=new ObservableStrategy,AsyncPipe=function(){function AsyncPipe(_ref){this._ref=_ref,this._latestValue=null,this._latestReturnedValue=null,this._subscription=null,this._obj=null,this._strategy=null}return AsyncPipe.prototype.ngOnDestroy=function(){this._subscription&&this._dispose()},AsyncPipe.prototype.transform=function(obj){return this._obj?obj!==this._obj?(this._dispose(),this.transform(obj)):this._latestValue===this._latestReturnedValue?this._latestReturnedValue:(this._latestReturnedValue=this._latestValue,_angular_core.WrappedValue.wrap(this._latestValue)):(obj&&this._subscribe(obj),this._latestReturnedValue=this._latestValue,this._latestValue)},AsyncPipe.prototype._subscribe=function(obj){var _this=this;this._obj=obj,this._strategy=this._selectStrategy(obj),this._subscription=this._strategy.createSubscription(obj,function(value){return _this._updateLatestValue(obj,value)})},AsyncPipe.prototype._selectStrategy=function(obj){if(_angular_core.ɵisPromise(obj))return _promiseStrategy;if(_angular_core.ɵisObservable(obj))return _observableStrategy;throw invalidPipeArgumentError(AsyncPipe,obj)},AsyncPipe.prototype._dispose=function(){this._strategy.dispose(this._subscription),this._latestValue=null,this._latestReturnedValue=null,this._subscription=null,this._obj=null},AsyncPipe.prototype._updateLatestValue=function(async,value){async===this._obj&&(this._latestValue=value,this._ref.markForCheck())},AsyncPipe}();AsyncPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"async",pure:!1}]}],AsyncPipe.ctorParameters=function(){return[{type:_angular_core.ChangeDetectorRef}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var LowerCasePipe=function(){function LowerCasePipe(){}return LowerCasePipe.prototype.transform=function(value){if(!value)return value;if("string"!=typeof value)throw invalidPipeArgumentError(LowerCasePipe,value);return value.toLowerCase()},LowerCasePipe}();LowerCasePipe.decorators=[{type:_angular_core.Pipe,args:[{name:"lowercase"}]}],LowerCasePipe.ctorParameters=function(){return[]};var TitleCasePipe=function(){function TitleCasePipe(){}return TitleCasePipe.prototype.transform=function(value){if(!value)return value;if("string"!=typeof value)throw invalidPipeArgumentError(TitleCasePipe,value);return value.split(/\b/g).map(function(word){return titleCaseWord(word)}).join("")},TitleCasePipe}();TitleCasePipe.decorators=[{type:_angular_core.Pipe,args:[{name:"titlecase"}]}],TitleCasePipe.ctorParameters=function(){return[]};var UpperCasePipe=function(){function UpperCasePipe(){}return UpperCasePipe.prototype.transform=function(value){if(!value)return value;if("string"!=typeof value)throw invalidPipeArgumentError(UpperCasePipe,value);return value.toUpperCase()},UpperCasePipe}();UpperCasePipe.decorators=[{type:_angular_core.Pipe,args:[{name:"uppercase"}]}],UpperCasePipe.ctorParameters=function(){return[]};var NumberFormatStyle={};NumberFormatStyle.Decimal=0,NumberFormatStyle.Percent=1,NumberFormatStyle.Currency=2,NumberFormatStyle[NumberFormatStyle.Decimal]="Decimal",NumberFormatStyle[NumberFormatStyle.Percent]="Percent",NumberFormatStyle[NumberFormatStyle.Currency]="Currency";var NumberFormatter=function(){function NumberFormatter(){}return NumberFormatter.format=function(num,locale,style,opts){void 0===opts&&(opts={});var minimumIntegerDigits=opts.minimumIntegerDigits,minimumFractionDigits=opts.minimumFractionDigits,maximumFractionDigits=opts.maximumFractionDigits,currency=opts.currency,_a=opts.currencyAsSymbol,currencyAsSymbol=void 0!==_a&&_a,options={minimumIntegerDigits:minimumIntegerDigits,minimumFractionDigits:minimumFractionDigits,maximumFractionDigits:maximumFractionDigits,style:NumberFormatStyle[style].toLowerCase()};return style==NumberFormatStyle.Currency&&(options.currency="string"==typeof currency?currency:void 0,options.currencyDisplay=currencyAsSymbol?"symbol":"code"),new Intl.NumberFormat(locale,options).format(num)},NumberFormatter}(),DATE_FORMATS_SPLIT=/((?:[^yMLdHhmsazZEwGjJ']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|J+|j+|m+|s+|a|z|Z|G+|w+))(.*)/,PATTERN_ALIASES={yMMMdjms:datePartGetterFactory(combine([digitCondition("year",1),nameCondition("month",3),digitCondition("day",1),digitCondition("hour",1),digitCondition("minute",1),digitCondition("second",1)])),yMdjm:datePartGetterFactory(combine([digitCondition("year",1),digitCondition("month",1),digitCondition("day",1),digitCondition("hour",1),digitCondition("minute",1)])),yMMMMEEEEd:datePartGetterFactory(combine([digitCondition("year",1),nameCondition("month",4),nameCondition("weekday",4),digitCondition("day",1)])),yMMMMd:datePartGetterFactory(combine([digitCondition("year",1),nameCondition("month",4),digitCondition("day",1)])),yMMMd:datePartGetterFactory(combine([digitCondition("year",1),nameCondition("month",3),digitCondition("day",1)])),yMd:datePartGetterFactory(combine([digitCondition("year",1),digitCondition("month",1),digitCondition("day",1)])),jms:datePartGetterFactory(combine([digitCondition("hour",1),digitCondition("second",1),digitCondition("minute",1)])),jm:datePartGetterFactory(combine([digitCondition("hour",1),digitCondition("minute",1)]))},DATE_FORMATS={yyyy:datePartGetterFactory(digitCondition("year",4)),yy:datePartGetterFactory(digitCondition("year",2)),y:datePartGetterFactory(digitCondition("year",1)),MMMM:datePartGetterFactory(nameCondition("month",4)),MMM:datePartGetterFactory(nameCondition("month",3)),MM:datePartGetterFactory(digitCondition("month",2)),M:datePartGetterFactory(digitCondition("month",1)),LLLL:datePartGetterFactory(nameCondition("month",4)),L:datePartGetterFactory(nameCondition("month",1)),dd:datePartGetterFactory(digitCondition("day",2)),d:datePartGetterFactory(digitCondition("day",1)),HH:digitModifier(hourExtractor(datePartGetterFactory(hour12Modify(digitCondition("hour",2),!1)))),H:hourExtractor(datePartGetterFactory(hour12Modify(digitCondition("hour",1),!1))),hh:digitModifier(hourExtractor(datePartGetterFactory(hour12Modify(digitCondition("hour",2),!0)))),h:hourExtractor(datePartGetterFactory(hour12Modify(digitCondition("hour",1),!0))),jj:datePartGetterFactory(digitCondition("hour",2)),j:datePartGetterFactory(digitCondition("hour",1)),mm:digitModifier(datePartGetterFactory(digitCondition("minute",2))),m:datePartGetterFactory(digitCondition("minute",1)),ss:digitModifier(datePartGetterFactory(digitCondition("second",2))),s:datePartGetterFactory(digitCondition("second",1)),sss:datePartGetterFactory(digitCondition("second",3)),EEEE:datePartGetterFactory(nameCondition("weekday",4)),EEE:datePartGetterFactory(nameCondition("weekday",3)),EE:datePartGetterFactory(nameCondition("weekday",2)),E:datePartGetterFactory(nameCondition("weekday",1)),a:hourClockExtractor(datePartGetterFactory(hour12Modify(digitCondition("hour",1),!0))),Z:timeZoneGetter("short"),z:timeZoneGetter("long"),ww:datePartGetterFactory({}),w:datePartGetterFactory({}),G:datePartGetterFactory(nameCondition("era",1)),GG:datePartGetterFactory(nameCondition("era",2)),GGG:datePartGetterFactory(nameCondition("era",3)),GGGG:datePartGetterFactory(nameCondition("era",4))},DATE_FORMATTER_CACHE=new Map,DateFormatter=function(){function DateFormatter(){}return DateFormatter.format=function(date,locale,pattern){return dateFormatter(pattern,date,locale)},DateFormatter}(),_NUMBER_FORMAT_REGEXP=/^(\d+)?\.((\d+)(-(\d+))?)?$/,DecimalPipe=function(){function DecimalPipe(_locale){this._locale=_locale}return DecimalPipe.prototype.transform=function(value,digits){return formatNumber(DecimalPipe,this._locale,value,NumberFormatStyle.Decimal,digits)},DecimalPipe}();DecimalPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"number"}]}],DecimalPipe.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.LOCALE_ID]}]}]};var PercentPipe=function(){function PercentPipe(_locale){this._locale=_locale}return PercentPipe.prototype.transform=function(value,digits){return formatNumber(PercentPipe,this._locale,value,NumberFormatStyle.Percent,digits)},PercentPipe}();PercentPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"percent"}]}],PercentPipe.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.LOCALE_ID]}]}]};var CurrencyPipe=function(){function CurrencyPipe(_locale){this._locale=_locale}return CurrencyPipe.prototype.transform=function(value,currencyCode,symbolDisplay,digits){return void 0===currencyCode&&(currencyCode="USD"),void 0===symbolDisplay&&(symbolDisplay=!1),formatNumber(CurrencyPipe,this._locale,value,NumberFormatStyle.Currency,digits,currencyCode,symbolDisplay)},CurrencyPipe}();CurrencyPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"currency"}]}],CurrencyPipe.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.LOCALE_ID]}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var ISO8601_DATE_REGEX=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,DatePipe=function(){function DatePipe(_locale){this._locale=_locale}return DatePipe.prototype.transform=function(value,pattern){void 0===pattern&&(pattern="mediumDate");var date;if(isBlank(value)||value!==value)return null;if("string"==typeof value&&(value=value.trim()),isDate(value))date=value;else if(isNumeric(value))date=new Date(parseFloat(value));else if("string"==typeof value&&/^(\d{4}-\d{1,2}-\d{1,2})$/.test(value)){var _a=value.split("-").map(function(val){return parseInt(val,10)}),y=_a[0],m=_a[1],d=_a[2];date=new Date(y,m-1,d)}else date=new Date(value);if(!isDate(date)){var match=void 0;if("string"!=typeof value||!(match=value.match(ISO8601_DATE_REGEX)))throw invalidPipeArgumentError(DatePipe,value);date=isoStringToDate(match)}return DateFormatter.format(date,this._locale,DatePipe._ALIASES[pattern]||pattern)},DatePipe}();DatePipe._ALIASES={medium:"yMMMdjms",short:"yMdjm",fullDate:"yMMMMEEEEd",longDate:"yMMMMd",mediumDate:"yMMMd",shortDate:"yMd",mediumTime:"jms",shortTime:"jm"},DatePipe.decorators=[{type:_angular_core.Pipe,args:[{name:"date",pure:!0}]}],DatePipe.ctorParameters=function(){return[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.LOCALE_ID]}]}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var _INTERPOLATION_REGEXP=/#/g,I18nPluralPipe=function(){function I18nPluralPipe(_localization){this._localization=_localization}return I18nPluralPipe.prototype.transform=function(value,pluralMap){if(null==value)return"";if("object"!=typeof pluralMap||null===pluralMap)throw invalidPipeArgumentError(I18nPluralPipe,pluralMap);var key=getPluralCategory(value,Object.keys(pluralMap),this._localization);return pluralMap[key].replace(_INTERPOLATION_REGEXP,value.toString())},I18nPluralPipe}();I18nPluralPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"i18nPlural",pure:!0}]}],I18nPluralPipe.ctorParameters=function(){return[{type:NgLocalization}]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var I18nSelectPipe=function(){function I18nSelectPipe(){}return I18nSelectPipe.prototype.transform=function(value,mapping){if(null==value)return"";if("object"!=typeof mapping||"string"!=typeof value)throw invalidPipeArgumentError(I18nSelectPipe,mapping);return mapping.hasOwnProperty(value)?mapping[value]:mapping.hasOwnProperty("other")?mapping.other:""},I18nSelectPipe}();I18nSelectPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"i18nSelect",pure:!0}]}],I18nSelectPipe.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var JsonPipe=function(){function JsonPipe(){}return JsonPipe.prototype.transform=function(value){return JSON.stringify(value,null,2)},JsonPipe}();JsonPipe.decorators=[{type:_angular_core.Pipe,args:[{name:"json",pure:!1}]}],JsonPipe.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var SlicePipe=function(){function SlicePipe(){}return SlicePipe.prototype.transform=function(value,start,end){if(null==value)return value;if(!this.supports(value))throw invalidPipeArgumentError(SlicePipe,value);return value.slice(start,end)},SlicePipe.prototype.supports=function(obj){return"string"==typeof obj||Array.isArray(obj)},SlicePipe}();SlicePipe.decorators=[{type:_angular_core.Pipe,args:[{name:"slice",pure:!1}]}],SlicePipe.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var COMMON_PIPES=[AsyncPipe,UpperCasePipe,LowerCasePipe,JsonPipe,SlicePipe,DecimalPipe,PercentPipe,TitleCasePipe,CurrencyPipe,DatePipe,I18nPluralPipe,I18nSelectPipe],CommonModule=function(){function CommonModule(){}return CommonModule}();CommonModule.decorators=[{type:_angular_core.NgModule,args:[{declarations:[COMMON_DIRECTIVES,COMMON_PIPES],exports:[COMMON_DIRECTIVES,COMMON_PIPES],providers:[{provide:NgLocalization,useClass:NgLocaleLocalization}]}]}],CommonModule.ctorParameters=function(){return[]};/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var PLATFORM_BROWSER_ID="browser",PLATFORM_SERVER_ID="server",PLATFORM_WORKER_APP_ID="browserWorkerApp",PLATFORM_WORKER_UI_ID="browserWorkerUi",VERSION=new _angular_core.Version("4.2.5");exports.NgLocaleLocalization=NgLocaleLocalization,exports.NgLocalization=NgLocalization,exports.CommonModule=CommonModule,exports.NgClass=NgClass,exports.NgFor=NgFor,exports.NgForOf=NgForOf,exports.NgForOfContext=NgForOfContext,exports.NgIf=NgIf,exports.NgIfContext=NgIfContext,exports.NgPlural=NgPlural,exports.NgPluralCase=NgPluralCase,exports.NgStyle=NgStyle,exports.NgSwitch=NgSwitch,exports.NgSwitchCase=NgSwitchCase,exports.NgSwitchDefault=NgSwitchDefault,exports.NgTemplateOutlet=NgTemplateOutlet,exports.NgComponentOutlet=NgComponentOutlet,exports.AsyncPipe=AsyncPipe,exports.DatePipe=DatePipe,exports.I18nPluralPipe=I18nPluralPipe,exports.I18nSelectPipe=I18nSelectPipe,exports.JsonPipe=JsonPipe,exports.LowerCasePipe=LowerCasePipe,exports.CurrencyPipe=CurrencyPipe,exports.DecimalPipe=DecimalPipe,exports.PercentPipe=PercentPipe,exports.SlicePipe=SlicePipe,exports.UpperCasePipe=UpperCasePipe,exports.TitleCasePipe=TitleCasePipe,exports.ɵPLATFORM_BROWSER_ID=PLATFORM_BROWSER_ID,exports.ɵPLATFORM_SERVER_ID=PLATFORM_SERVER_ID,exports.ɵPLATFORM_WORKER_APP_ID=PLATFORM_WORKER_APP_ID,exports.ɵPLATFORM_WORKER_UI_ID=PLATFORM_WORKER_UI_ID,exports.isPlatformBrowser=isPlatformBrowser,exports.isPlatformServer=isPlatformServer,exports.isPlatformWorkerApp=isPlatformWorkerApp,exports.isPlatformWorkerUi=isPlatformWorkerUi,exports.VERSION=VERSION,exports.PlatformLocation=PlatformLocation,exports.LOCATION_INITIALIZED=LOCATION_INITIALIZED,exports.LocationStrategy=LocationStrategy,exports.APP_BASE_HREF=APP_BASE_HREF,exports.HashLocationStrategy=HashLocationStrategy,exports.PathLocationStrategy=PathLocationStrategy,exports.Location=Location,exports.ɵa=COMMON_DIRECTIVES,exports.ɵb=COMMON_PIPES,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=common.umd.min.js.map
