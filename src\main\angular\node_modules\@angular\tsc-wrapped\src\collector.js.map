{"version": 3, "file": "collector.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/src/collector.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AAEjC,yCAAmD;AACnD,mCAA+vB;AAC/vB,qCAAkC;AAElC,sCAAsC;AACtC,2CAA2C;AAC3C,IAAM,QAAQ,GAAI,EAAU,CAAC,aAAa;IACtC,CAAC,UAAC,IAAa;QACV,OAAA,CAAC,CAAC,CAAE,EAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAI,EAAU,CAAC,aAAa,CAAC,MAAM,CAAC;IAAjF,CAAiF,CAAC;IACvF,CAAC,UAAC,IAAa,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAI,EAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAA/C,CAA+C,CAAC,CAAC;AACzE,IAAM,QAAQ,GAAI,EAAU,CAAC,aAAa;IACtC,CAAC,UAAC,IAAa;QACV,OAAA,CAAC,CAAC,CAAE,EAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAI,EAAU,CAAC,aAAa,CAAC,MAAM,CAAC;IAAjF,CAAiF,CAAC;IACvF,CAAC,UAAC,IAAa,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAI,EAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAA/C,CAA+C,CAAC,CAAC;AAEzE;;GAEG;AACH;IAAA;IAgBA,CAAC;IAAD,uBAAC;AAAD,CAAC,AAhBD,IAgBC;AAhBY,4CAAgB;AAkB7B;;GAEG;AACH;IACE,2BAAoB,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;QAA9B,YAAO,GAAP,OAAO,CAAuB;IAAG,CAAC;IAEtD;;;OAGG;IACI,uCAAW,GAAlB,UAAmB,UAAyB,EAAE,MAAuB;QAAvB,uBAAA,EAAA,cAAuB;QACnE,IAAM,MAAM,GAAG,IAAI,iBAAO,CAAC,UAAU,CAAC,CAAC;QACvC,IAAM,OAAO,GACT,IAAI,GAAG,EAA2E,CAAC;QACvF,IAAM,SAAS,GAAG,IAAI,qBAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/D,IAAI,QAAsF,CAAC;QAC3F,IAAI,OAA+B,CAAC;QAEpC,0BAA0B,aAA2B;YACnD,MAAM,CAA6B,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACtF,CAAC;QAED,qBAA8C,KAAQ,EAAE,IAAa;YACnE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzB,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,kBACI,OAAe,EAAE,IAAc,EAAE,OAAkC;YACrE,MAAM,CAAC,uBAAW,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;QAED,gCACI,mBACoB;YACtB,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9D,IAAM,QAAQ,GAAkB,mBAAmB,CAAC,IAAI,CAAC;gBACzD,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACnC,IAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,CAAC;gBAC9C,EAAE,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC7C,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;wBACrD,IAAM,eAAe,GAAuB,SAAS,CAAC;wBACtD,EAAE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;4BAC/B,IAAM,IAAI,GAAqB;gCAC7B,UAAU,EAAE,UAAU;gCACtB,UAAU,EAAE,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC;gCACnD,KAAK,EAAE,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC;6BAC1D,CAAC;4BACF,EAAE,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,IAAI,IAAI,EAArB,CAAqB,CAAC,CAAC,CAAC,CAAC;gCACpE,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAC9C,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,EAAtD,CAAsD,CAAC,CAAC;4BACnE,CAAC;4BACD,MAAM,CAAC,WAAW,CAAC,EAAC,IAAI,MAAA,EAAE,IAAI,EAAE,YAAY,EAAC,EAAE,mBAAmB,CAAC,CAAC;wBACtE,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,yBAAyB,gBAAqC;YAC5D,IAAM,MAAM,GAAkB,EAAC,UAAU,EAAE,OAAO,EAAC,CAAC;YAEpD,uBAAuB,UAA0B;gBAC/C,EAAE,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC;oBAClC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,SAAS,IAAI,OAAA,gBAAgB,CAAC,SAAS,CAAC,EAA3B,CAA2B,CAAC,CAAC;gBAClE,MAAM,CAAC,SAAS,CAAC;YACnB,CAAC;YAED,uBAAuB,IAAa;gBAElC,IAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC5C,EAAE,CAAC,CAAC,wBAAe,CAAC,MAAM,CAAC,IAAI,8CAAqC,CAAC,MAAM,CAAC;oBACxE,2CAAkC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,MAAM,CAAC;gBAChB,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,MAAM,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,EAAE,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC;gBACrC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,EAAE;oBAC1C,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC1D,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,EAA/C,CAA+C,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,mCAAmC;YACnC,IAAM,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC;YACvD,EAAE,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC;YACvC,CAAC;YAED,uBAAuB;YACvB,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;gBAChC,MAAM,CAAC,UAAU,GAAG,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACjE,CAAC;YAED,oBAAoB;YACpB,IAAI,OAAO,GAAgB,IAAI,CAAC;YAChC,sBAAsB,IAAY,EAAE,QAAwB;gBAC1D,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;gBAC3B,IAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YACvB,CAAC;YAED,gBAAgB;YAChB,IAAI,OAAO,GAAuD,IAAI,CAAC;YACvE,4BAA4B,IAAY,EAAE,KAAuC;gBAC/E,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACxB,CAAC;YAED,GAAG,CAAC,CAAiB,UAAwB,EAAxB,KAAA,gBAAgB,CAAC,OAAO,EAAxB,cAAwB,EAAxB,IAAwB;gBAAxC,IAAM,MAAM,SAAA;gBACf,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpB,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;oBAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;wBAClC,aAAa,GAAG,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;wBAC1D,IAAM,MAAM,GAAmD,MAAM,CAAC;wBACtE,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BACrB,IAAM,SAAS,GAAG,sBAAsB,CAAuB,MAAM,CAAC,CAAC;4BACvE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gCACd,kBAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;4BACrD,CAAC;4BACD,QAAQ,CAAC;wBACX,CAAC;wBACD,IAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;wBAC1D,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;wBACrC,IAAM,sBAAsB,GAAqD,EAAE,CAAC;wBACpF,IAAM,cAAc,GAE8B,EAAE,CAAC;wBACrD,IAAI,gBAAgB,GAAY,KAAK,CAAC;wBACtC,IAAI,gBAAgB,GAAY,KAAK,CAAC;wBACtC,GAAG,CAAC,CAAoB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;4BAA7B,IAAM,SAAS,mBAAA;4BAClB,IAAM,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;4BAC1D,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;4BAC3C,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,CAAC,aAAa,CAAC;4BACvD,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;gCAClB,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oCACnB,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gCACrD,CAAC;gCAAC,IAAI,CAAC,CAAC;oCACN,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCAC5B,CAAC;gCACD,gBAAgB,GAAG,IAAI,CAAC;4BAC1B,CAAC;yBACF;wBACD,IAAM,IAAI,GAAmB,EAAC,UAAU,EAAE,aAAa,GAAG,aAAa,GAAG,QAAQ,EAAC,CAAC;wBACpF,IAAM,MAAI,GAAG,aAAa,GAAG,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACxE,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;4BACrB,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC;wBACrC,CAAC;wBACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;4BACrB,IAAI,CAAC,mBAAmB,GAAG,sBAAsB,CAAC;wBACpD,CAAC;wBACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;4BACC,IAAK,CAAC,UAAU,GAAG,cAAc,CAAC;wBAC1D,CAAC;wBACD,EAAE,CAAC,CAAC,CAAC,wBAAe,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;4BAC3B,YAAY,CAAC,MAAI,EAAE,IAAI,CAAC,CAAC;wBAC3B,CAAC;wBACD,KAAK,CAAC;oBACR,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC;oBACvC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;oBAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;wBAC5B,IAAM,QAAQ,GAA2B,MAAM,CAAC;wBAChD,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;4BACvB,IAAM,MAAI,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;4BAC7C,EAAE,CAAC,CAAC,CAAC,wBAAe,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;gCAC3B,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;oCACzB,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oCAC3D,kBAAkB,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;gCAClC,CAAC;gCAAC,IAAI,CAAC,CAAC;oCACN,kBAAkB,CAAC,MAAI,EAAE,QAAQ,CAAC,0BAA0B,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gCAChF,CAAC;4BACH,CAAC;wBACH,CAAC;wBACD,IAAM,kBAAkB,GAAG,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;wBAC9D,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;4BACvB,IAAM,MAAI,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;4BAC7C,EAAE,CAAC,CAAC,CAAC,wBAAe,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC;gCAC3B,YAAY,CAAC,MAAI,EAAE,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAC,CAAC,CAAC;4BAC/E,CAAC;wBACH,CAAC;wBACD,KAAK,CAAC;gBACV,CAAC;aACF;YACD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,CAAC;YACD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,CAAC;YAED,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,uDAAuD;QACvD,IAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC5C,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,UAAA,IAAI;YAC9B,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;oBAClC,IAAM,iBAAiB,GAAyB,IAAI,CAAC;oBAC9C,IAAA,mDAAe,EAAE,6CAAY,CAAsB;oBAE1D,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;wBACrB,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,IAAI;4BAChC,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;4BAClC,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;4BACnD,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC;oBACL,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAM,oBAAoB,GAAG,UAAC,UAAyB,IAAK,OAAA,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAA9B,CAA8B,CAAC;QAC3F,IAAM,UAAU,GACZ,UAAC,IACkB,IAAK,OAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAjD,CAAiD,CAAC;QAC9E,IAAM,sBAAsB,GAAG,UAAC,UAAyB;YACrD,OAAA,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAAjD,CAAiD,CAAC;QACtD,IAAM,YAAY,GACd,UAAC,IACkB,IAAK,OAAA,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAjC,CAAiC,CAAC;QAG9D,mCAAmC;QACnC,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,UAAA,IAAI;YAC9B,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;oBACjC,IAAM,gBAAgB,GAAwB,IAAI,CAAC;oBACnD,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC1B,IAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC7C,EAAE,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;4BACjC,MAAM,CAAC,MAAM,CACT,SAAS,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,EAAC,CAAC,CAAC;wBAClF,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,MAAM,CAAC,MAAM,CACT,SAAS,EAAE,QAAQ,CAAC,iCAAiC,EAAE,IAAI,EAAE,EAAC,SAAS,WAAA,EAAC,CAAC,CAAC,CAAC;wBACjF,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB;oBACrC,IAAM,oBAAoB,GAA4B,IAAI,CAAC;oBAC3D,EAAE,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9B,IAAM,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;wBACrD,2EAA2E;wBAC3E,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;oBACvE,CAAC;oBACD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;oBACpC,IAAM,mBAAmB,GAA2B,IAAI,CAAC;oBACzD,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;wBACrC,kDAAkD;wBAClD,IAAM,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC;wBAC1C,EAAE,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;4BAC9B,MAAM,CAAC,MAAM,CACT,QAAQ,CAAC,IAAI,EACb,QAAQ,CACJ,sCAAsC,EAAE,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC;wBACpF,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,UAAA,IAAI;YAC9B,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;oBAClC,6BAA6B;oBAC7B,IAAM,iBAAiB,GAAyB,IAAI,CAAC;oBAC9C,IAAA,mDAAe,EAAE,6CAAY,CAAsB;oBAE1D,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;wBACrB,oDAAoD;wBACpD,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;4BACjB,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,IAAI;gCAChC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gCAC5B,2EAA2E;gCAC3E,yBAAyB;gCACzB,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oCACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC;oCAChD,IAAM,KAAK,GAAkB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oCAC9D,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;wCAAC,QAAQ,GAAG,EAAE,CAAC;oCAC7B,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gCAC5C,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBAED,EAAE,CAAC,CAAC,eAAe,IAAI,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;wBAC3E,6DAA6D;wBAC7D,qFAAqF;wBACrF,IAAM,IAAI,GAAsB,eAAgB,CAAC,IAAI,CAAC;wBACtD,IAAM,YAAY,GAAyB,EAAC,IAAI,MAAA,EAAC,CAAC;wBAClD,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;4BACjB,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3C,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,YAAY,GAAG,EAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;gCAClD,IAAI,CAAC,IAAI,CAAC,IAAI,EADlC,CACkC,CAAC,CAAC;wBAClD,CAAC;wBACD,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;4BAAC,OAAO,GAAG,EAAE,CAAC;wBAC3B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC7B,CAAC;oBACD,KAAK,CAAC;gBACR,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;oBACjC,IAAM,gBAAgB,GAAwB,IAAI,CAAC;oBACnD,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC1B,EAAE,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;4BACjC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;gCAAC,QAAQ,GAAG,EAAE,CAAC;4BAC7B,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC;wBAC/E,CAAC;oBACH,CAAC;oBACD,iDAAiD;oBACjD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB;oBACrC,IAAM,oBAAoB,GAA4B,IAAI,CAAC;oBAC3D,EAAE,CAAC,CAAC,oBAAoB,CAAC,IAAI,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAClE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;4BAAC,QAAQ,GAAG,EAAE,CAAC;wBAC7B,QAAQ,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,GAAG,EAAC,UAAU,EAAE,WAAW,EAAC,CAAC;oBAC3E,CAAC;oBACD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;oBACpC,oEAAoE;oBACpE,+DAA+D;oBAC/D,IAAM,mBAAmB,GAA2B,IAAI,CAAC;oBACzD,EAAE,CAAC,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAChE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;4BAAC,QAAQ,GAAG,EAAE,CAAC;wBAC7B,IAAM,MAAI,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC;wBAC/C,IAAM,SAAS,GAAG,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;wBAC9D,QAAQ,CAAC,MAAI,CAAC;4BACV,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAC,UAAU,EAAE,UAAU,EAAC,CAAC;oBAC/E,CAAC;oBACD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;oBAChC,IAAM,eAAe,GAAuB,IAAI,CAAC;oBACjD,EAAE,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAChC,IAAM,eAAe,GAAoC,EAAE,CAAC;wBAC5D,IAAM,QAAQ,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;wBAC/C,IAAI,gBAAgB,GAAkB,CAAC,CAAC;wBACxC,IAAI,cAAc,GAAG,CAAC,CAAC;wBACvB,GAAG,CAAC,CAAiB,UAAuB,EAAvB,KAAA,eAAe,CAAC,OAAO,EAAvB,cAAuB,EAAvB,IAAuB;4BAAvC,IAAM,MAAM,SAAA;4BACf,IAAI,SAAS,SAAe,CAAC;4BAC7B,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;gCACxB,SAAS,GAAG,gBAAgB,CAAC;4BAC/B,CAAC;4BAAC,IAAI,CAAC,CAAC;gCACN,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;4BACzD,CAAC;4BACD,IAAI,MAAI,GAAW,SAAS,CAAC;4BAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;gCACjD,IAAM,UAAU,GAAkB,MAAM,CAAC,IAAI,CAAC;gCAC9C,MAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gCACvB,eAAe,CAAC,MAAI,CAAC,GAAG,SAAS,CAAC;gCAClC,cAAc,EAAE,CAAC;4BACnB,CAAC;4BACD,EAAE,CAAC,CAAC,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC;gCAClC,gBAAgB,GAAG,SAAS,GAAG,CAAC,CAAC;4BACnC,CAAC;4BAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;gCAChB,gBAAgB,GAAG;oCACjB,UAAU,EAAE,QAAQ;oCACpB,QAAQ,EAAE,GAAG;oCACb,IAAI,EAAE;wCACJ,UAAU,EAAE,QAAQ;wCACpB,UAAU,EAAE,WAAW,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAC,EAAE,IAAI,CAAC,EAAE,IAAI,QAAA;qCAC/E;iCACF,CAAC;4BACJ,CAAC;4BAAC,IAAI,CAAC,CAAC;gCACN,gBAAgB;oCACZ,WAAW,CAAC,QAAQ,CAAC,+BAA+B,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;4BAChF,CAAC;yBACF;wBACD,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;4BACnB,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;gCAAC,QAAQ,GAAG,EAAE,CAAC;4BAC7B,QAAQ,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;wBAC1D,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC;gBAER,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;oBAClC,IAAM,iBAAiB,GAAyB,IAAI,CAAC;4CAC1C,mBAAmB;wBAC5B,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;4BAC9D,IAAM,QAAQ,GAAkB,mBAAmB,CAAC,IAAI,CAAC;4BACzD,IAAI,QAAQ,SAAe,CAAC;4BAC5B,EAAE,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;gCACpC,QAAQ,GAAG,SAAS,CAAC,YAAY,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;4BACrE,CAAC;4BAAC,IAAI,CAAC,CAAC;gCACN,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,0BAA0B,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;4BACnF,CAAC;4BACD,IAAI,QAAQ,GAAG,KAAK,CAAC;4BACrB,EAAE,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,mBAAmB,CAAC;gCAC5D,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gCACnC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;oCAAC,QAAQ,GAAG,EAAE,CAAC;gCAC7B,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gCACzE,QAAQ,GAAG,IAAI,CAAC;4BAClB,CAAC;4BACD,EAAE,CAAC,CAAC,OAAO,QAAQ,IAAI,QAAQ,IAAI,OAAO,QAAQ,IAAI,QAAQ;gCAC1D,OAAO,QAAQ,IAAI,SAAS,CAAC,CAAC,CAAC;gCACjC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;4BACzC,CAAC;4BAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gCACrB,EAAE,CAAC,CAAC,QAAQ,IAAI,CAAC,wBAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oCAC3C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;gCAC5D,CAAC;gCAAC,IAAI,CAAC,CAAC;oCACN,MAAM,CAAC,MAAM,CACT,QAAQ,CAAC,IAAI,EACb,WAAW,CACP,QAAQ,CAAC,6BAA6B,EAAE,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAC,CAAC,EACxE,IAAI,CAAC,CAAC,CAAC;gCACjB,CAAC;4BACH,CAAC;wBACH,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,6DAA6D;4BAC7D,sDAAsD;4BACtD,OAAO;4BACP,qDAAqD;4BACrD,qBAAqB;4BACrB,IAAM,QAAM,GAAgC,UAAC,QAAiB;gCAC5D,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;oCACtB,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;wCAC3B,IAAM,MAAI,GAAkB,QAAQ,CAAC;wCACrC,IAAM,QAAQ,GAAG,QAAQ,CAAC,6BAA6B,EAAE,MAAI,CAAC,CAAC;wCAC/D,MAAM,CAAC,MAAM,CAAC,MAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;wCACnC,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4CACnB,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;gDAAC,QAAQ,GAAG,EAAE,CAAC;4CAC7B,QAAQ,CAAC,MAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;wCACjC,CAAC;wCACD,KAAK,CAAC;oCACR,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;wCAC/B,IAAM,cAAc,GAAsB,QAAQ,CAAC;wCACnD,QAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wCAC5B,KAAK,CAAC;oCACR,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC;oCACxC,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;wCACpC,IAAM,QAAQ,GAAsB,QAAQ,CAAC;wCAC5C,QAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAM,CAAC,CAAC;wCAC3C,KAAK,CAAC;gCACV,CAAC;4BACH,CAAC,CAAC;4BACF,QAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBACnC,CAAC;oBACH,CAAC;oBA5DD,GAAG,CAAC,CAA8B,UAA8C,EAA9C,KAAA,iBAAiB,CAAC,eAAe,CAAC,YAAY,EAA9C,cAA8C,EAA9C,IAA8C;wBAA3E,IAAM,mBAAmB,SAAA;gCAAnB,mBAAmB;qBA4D7B;oBACD,KAAK,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC;YACxB,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;gBACZ,QAAQ,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBAChB,gBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC;YACD,IAAM,MAAM,GAAmB;gBAC7B,UAAU,EAAE,QAAQ;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,gBAAO,EAAE,QAAQ,UAAA;aACnD,CAAC;YACF,EAAE,CAAC,CAAC,OAAO,CAAC;gBAAC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AAhdD,IAgdC;AAhdY,8CAAiB;AAkd9B,sEAAsE;AACtE,0BACI,UAAyB,EAAE,OAAoC,EAC/D,QAAyC;IAC3C,IAAI,MAAM,GAAgB,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IAEhG,4BACI,UAAsE;QACxE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAChB,MAAM,CAAC;QACT,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACzC,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,UAAU,KAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtF,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,kBAAkB,CAAO,UAAW,CAAC,CAAC,CAAC,CAAC,EAAxC,CAAwC,CAAC,CAAC;QAChG,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,wBAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC1B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,4CAAmC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC3D,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,SAAS,GAAkB,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC3D,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBACd,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,2BAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1C,gBAAgB,CAAM,UAAU,CAAC,CAAC;QACpC,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,qCAA4B,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9B,KAAK,QAAQ;oBACX,IAAM,gBAAgB,GAAqC,UAAU,CAAC;oBACtE,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC1C,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAC3C,KAAK,CAAC;gBACR,KAAK,MAAM,CAAC;gBACZ,KAAK,KAAK;oBACR,IAAM,cAAc,GAAmC,UAAU,CAAC;oBAClE,kBAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;oBAC9C,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC;wBAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;oBACnF,KAAK,CAAC;gBACR,KAAK,OAAO;oBACV,IAAM,eAAe,GAAoC,UAAU,CAAC;oBACpE,kBAAkB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;oBAC/C,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC1C,KAAK,CAAC;gBACR,KAAK,KAAK;oBACR,IAAM,gBAAgB,GAAqC,UAAU,CAAC;oBACtE,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;oBAC7C,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,IAAM,gBAAgB,GAAqC,UAAU,CAAC;oBACtE,kBAAkB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAChD,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,IAAM,gBAAgB,GAAqC,UAAU,CAAC;oBACtE,kBAAkB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAChD,KAAK,CAAC;gBACR,KAAK,IAAI;oBACP,IAAM,YAAY,GAAiC,UAAU,CAAC;oBAC9D,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC3C,kBAAkB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;oBAChD,kBAAkB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;oBAChD,KAAK,CAAC;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,wBAAwB,SAAwB,EAAE,MAAsB;QACtE,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC;QACD,EAAE,CAAC,CAAC,yBAAgB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACzD,CAAC;QACD,kFAAkF;QAClF,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,IAAI,8BAAqB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/E,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,uBAAuB,SAAwB;QAC7C,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACzB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACnD,CAAC;QACD,EAAE,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC;iBACxC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,EAA5B,CAA4B,CAAC,EAApE,CAAoE,CAAC,CAAC;QAC7F,CAAC;QACD,EAAE,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACxD,IAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7C,EAAE,CAAC,CAAC,2BAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACrC,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,0BAA0B,mBAAqC;QAC7D,EAAE,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9B,IAAM,SAAS,GAAG,MAAM,CAAC;YACzB,EAAE,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC;gBACnC,MAAM,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;gBACrC,EAAE,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC;oBACjC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAb,CAAa,CAAC,CAAC;YAC/D,CAAC;YACD,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,GAAG,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;IAED,0BAA0B,IAAa;QACrC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,CAAC,CAAC,CACJ,IAAI,CAAC,GAAG,IAAI,SAAS;gBACrB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/E,CAAC;QACD,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB,KAAoB;QACvC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS;gBACpC,KAAK,CAAC,SAAS,IAAI,SAAS,GAAG,OAAI,KAAK,CAAC,IAAI,GAAG,CAAC,WAAI,KAAK,CAAC,SAAS,GAAG,CAAC,CAAE;oBAC3C,OAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAE;gBACnD,EAAE,CAAC;YACP,MAAM,IAAI,KAAK,CACX,KAAG,UAAU,CAAC,QAAQ,GAAG,QAAQ,iFAA4E,eAAe,CAAC,KAAK,CAAC,aAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAG,CAAC,CAAC;QAC1K,CAAC;IACH,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;QAC/C,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC;YACH,EAAE,CAAC,CAAC,wBAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,aAAa,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChC,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACH,IAAA,8DAA6E,EAA5E,cAAI,EAAE,wBAAS,CAA8D;oBACpF,MAAM,IAAI,KAAK,CACR,UAAU,CAAC,QAAQ,UAAI,IAAI,GAAG,CAAC,WAAI,SAAS,GAAG,CAAC,wEAAkE,IAAI,cAAS,CAAC,CAAC,OAAS,CAAC,CAAC;gBACrJ,CAAC;gBACD,MAAM,IAAI,KAAK,CACX,iEAA+D,IAAI,aAAQ,CAAC,CAAC,OAAS,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,2CAA2C;AAC3C,iBAAiB,UAAiD;IAChE,IAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,oBAAoB,IAAuC;QACzD,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC1C,IAAM,UAAU,GAAkB,IAAI,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAM,cAAc,GAAsB,IAAI,CAAC;YAC/C,GAAG,CAAC,CAAkB,UAAuB,EAAvB,KAAA,cAAc,CAAC,QAAQ,EAAvB,cAAuB,EAAvB,IAAuB;gBAAxC,IAAM,OAAO,SAAA;gBAChB,IAAM,MAAI,GAAI,OAAe,CAAC,IAAI,CAAC;gBACnC,EAAE,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;oBACT,UAAU,CAAC,MAAI,CAAC,CAAC;gBACnB,CAAC;aACF;QACH,CAAC;IACH,CAAC;IAED,GAAG,CAAC,CAAoB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAM,SAAS,mBAAA;QAClB,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5B;IAED,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAED,yBAAyB,KAAU;IACjC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QACtB,KAAK,iCAAiC;YACpC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC7C,MAAM,CAAC,uCAAqC,KAAK,CAAC,OAAO,CAAC,SAAS,mCAAgC,CAAC;YACtG,CAAC;YACD,KAAK,CAAC;QACR,KAAK,0BAA0B;YAC7B,MAAM,CAAC,kIAAkI,CAAC;QAC5I,KAAK,6BAA6B;YAChC,MAAM,CAAC,uJAAuJ,CAAC;QACjK,KAAK,wBAAwB;YAC3B,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC5C,MAAM,CAAC,4BAA0B,KAAK,CAAC,OAAO,CAAC,QAAU,CAAC;YAC5D,CAAC;YACD,KAAK,CAAC;QACR,KAAK,6BAA6B;YAChC,IAAI,MAAM,GACN,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,uBAAqB,KAAK,CAAC,OAAO,CAAC,IAAI,SAAM,GAAG,GAAG,CAAC;YAC9F,MAAM,CAAC,MAAM;gBACT,qHAAqH,CAAC;QAC5H,KAAK,6BAA6B;YAChC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxC,MAAM,CAAC,iDAA+C,KAAK,CAAC,OAAO,CAAC,IAAI,qCAAkC,CAAC;YAC7G,CAAC;IACL,CAAC;IACD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;AACvB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\nimport {Evaluator, errorSymbol} from './evaluator';\nimport {ClassMetadata, ConstructorMetadata, FunctionMetadata, InterfaceMetadata, MemberMetadata, MetadataEntry, MetadataError, MetadataMap, MetadataSymbolicBinaryExpression, MetadataSymbolicCallExpression, MetadataSymbolicExpression, MetadataSymbolicIfExpression, MetadataSymbolicIndexExpression, MetadataSymbolicPrefixExpression, MetadataSymbolicReferenceExpression, MetadataSymbolicSelectExpression, MetadataSymbolicSpreadExpression, MetadataValue, MethodMetadata, ModuleExportMetadata, ModuleMetadata, VERSION, isClassMetadata, isConstructorMetadata, isFunctionMetadata, isMetadataError, isMetadataGlobalReferenceExpression, isMetadataSymbolicExpression, isMetadataSymbolicReferenceExpression, isMetadataSymbolicSelectExpression, isMethodMetadata} from './schema';\nimport {Symbols} from './symbols';\n\n// In TypeScript 2.1 these flags moved\n// These helpers work for both 2.0 and 2.1.\nconst isExport = (ts as any).ModifierFlags ?\n    ((node: ts.Node) =>\n         !!((ts as any).getCombinedModifierFlags(node) & (ts as any).ModifierFlags.Export)) :\n    ((node: ts.Node) => !!((node.flags & (ts as any).NodeFlags.Export)));\nconst isStatic = (ts as any).ModifierFlags ?\n    ((node: ts.Node) =>\n         !!((ts as any).getCombinedModifierFlags(node) & (ts as any).ModifierFlags.Static)) :\n    ((node: ts.Node) => !!((node.flags & (ts as any).NodeFlags.Static)));\n\n/**\n * A set of collector options to use when collecting metadata.\n */\nexport class CollectorOptions {\n  /**\n   * Version of the metadata to collect.\n   */\n  version?: number;\n\n  /**\n   * Collect a hidden field \"$quoted$\" in objects literals that record when the key was quoted in\n   * the source.\n   */\n  quotedNames?: boolean;\n\n  /**\n   * Do not simplify invalid expressions.\n   */\n  verboseInvalidExpression?: boolean;\n}\n\n/**\n * Collect decorator metadata from a TypeScript module.\n */\nexport class MetadataCollector {\n  constructor(private options: CollectorOptions = {}) {}\n\n  /**\n   * Returns a JSON.stringify friendly form describing the decorators of the exported classes from\n   * the source file that is expected to correspond to a module.\n   */\n  public getMetadata(sourceFile: ts.SourceFile, strict: boolean = false): ModuleMetadata {\n    const locals = new Symbols(sourceFile);\n    const nodeMap =\n        new Map<MetadataValue|ClassMetadata|InterfaceMetadata|FunctionMetadata, ts.Node>();\n    const evaluator = new Evaluator(locals, nodeMap, this.options);\n    let metadata: {[name: string]: MetadataValue | ClassMetadata | FunctionMetadata}|undefined;\n    let exports: ModuleExportMetadata[];\n\n    function objFromDecorator(decoratorNode: ts.Decorator): MetadataSymbolicExpression {\n      return <MetadataSymbolicExpression>evaluator.evaluateNode(decoratorNode.expression);\n    }\n\n    function recordEntry<T extends MetadataEntry>(entry: T, node: ts.Node): T {\n      nodeMap.set(entry, node);\n      return entry;\n    }\n\n    function errorSym(\n        message: string, node?: ts.Node, context?: {[name: string]: string}): MetadataError {\n      return errorSymbol(message, node, context, sourceFile);\n    }\n\n    function maybeGetSimpleFunction(\n        functionDeclaration: ts.FunctionDeclaration |\n        ts.MethodDeclaration): {func: FunctionMetadata, name: string}|undefined {\n      if (functionDeclaration.name.kind == ts.SyntaxKind.Identifier) {\n        const nameNode = <ts.Identifier>functionDeclaration.name;\n        const functionName = nameNode.text;\n        const functionBody = functionDeclaration.body;\n        if (functionBody && functionBody.statements.length == 1) {\n          const statement = functionBody.statements[0];\n          if (statement.kind === ts.SyntaxKind.ReturnStatement) {\n            const returnStatement = <ts.ReturnStatement>statement;\n            if (returnStatement.expression) {\n              const func: FunctionMetadata = {\n                __symbolic: 'function',\n                parameters: namesOf(functionDeclaration.parameters),\n                value: evaluator.evaluateNode(returnStatement.expression)\n              };\n              if (functionDeclaration.parameters.some(p => p.initializer != null)) {\n                func.defaults = functionDeclaration.parameters.map(\n                    p => p.initializer && evaluator.evaluateNode(p.initializer));\n              }\n              return recordEntry({func, name: functionName}, functionDeclaration);\n            }\n          }\n        }\n      }\n    }\n\n    function classMetadataOf(classDeclaration: ts.ClassDeclaration): ClassMetadata {\n      const result: ClassMetadata = {__symbolic: 'class'};\n\n      function getDecorators(decorators: ts.Decorator[]): MetadataSymbolicExpression[] {\n        if (decorators && decorators.length)\n          return decorators.map(decorator => objFromDecorator(decorator));\n        return undefined;\n      }\n\n      function referenceFrom(node: ts.Node): MetadataSymbolicReferenceExpression|MetadataError|\n          MetadataSymbolicSelectExpression {\n        const result = evaluator.evaluateNode(node);\n        if (isMetadataError(result) || isMetadataSymbolicReferenceExpression(result) ||\n            isMetadataSymbolicSelectExpression(result)) {\n          return result;\n        } else {\n          return errorSym('Symbol reference expected', node);\n        }\n      }\n\n      // Add class parents\n      if (classDeclaration.heritageClauses) {\n        classDeclaration.heritageClauses.forEach((hc) => {\n          if (hc.token === ts.SyntaxKind.ExtendsKeyword && hc.types) {\n            hc.types.forEach(type => result.extends = referenceFrom(type.expression));\n          }\n        });\n      }\n\n      // Add arity if the type is generic\n      const typeParameters = classDeclaration.typeParameters;\n      if (typeParameters && typeParameters.length) {\n        result.arity = typeParameters.length;\n      }\n\n      // Add class decorators\n      if (classDeclaration.decorators) {\n        result.decorators = getDecorators(classDeclaration.decorators);\n      }\n\n      // member decorators\n      let members: MetadataMap = null;\n      function recordMember(name: string, metadata: MemberMetadata) {\n        if (!members) members = {};\n        const data = members.hasOwnProperty(name) ? members[name] : [];\n        data.push(metadata);\n        members[name] = data;\n      }\n\n      // static member\n      let statics: {[name: string]: MetadataValue | FunctionMetadata} = null;\n      function recordStaticMember(name: string, value: MetadataValue | FunctionMetadata) {\n        if (!statics) statics = {};\n        statics[name] = value;\n      }\n\n      for (const member of classDeclaration.members) {\n        let isConstructor = false;\n        switch (member.kind) {\n          case ts.SyntaxKind.Constructor:\n          case ts.SyntaxKind.MethodDeclaration:\n            isConstructor = member.kind === ts.SyntaxKind.Constructor;\n            const method = <ts.MethodDeclaration|ts.ConstructorDeclaration>member;\n            if (isStatic(method)) {\n              const maybeFunc = maybeGetSimpleFunction(<ts.MethodDeclaration>method);\n              if (maybeFunc) {\n                recordStaticMember(maybeFunc.name, maybeFunc.func);\n              }\n              continue;\n            }\n            const methodDecorators = getDecorators(method.decorators);\n            const parameters = method.parameters;\n            const parameterDecoratorData: (MetadataSymbolicExpression | MetadataError)[][] = [];\n            const parametersData:\n                (MetadataSymbolicReferenceExpression | MetadataError |\n                 MetadataSymbolicSelectExpression | null)[] = [];\n            let hasDecoratorData: boolean = false;\n            let hasParameterData: boolean = false;\n            for (const parameter of parameters) {\n              const parameterData = getDecorators(parameter.decorators);\n              parameterDecoratorData.push(parameterData);\n              hasDecoratorData = hasDecoratorData || !!parameterData;\n              if (isConstructor) {\n                if (parameter.type) {\n                  parametersData.push(referenceFrom(parameter.type));\n                } else {\n                  parametersData.push(null);\n                }\n                hasParameterData = true;\n              }\n            }\n            const data: MethodMetadata = {__symbolic: isConstructor ? 'constructor' : 'method'};\n            const name = isConstructor ? '__ctor__' : evaluator.nameOf(member.name);\n            if (methodDecorators) {\n              data.decorators = methodDecorators;\n            }\n            if (hasDecoratorData) {\n              data.parameterDecorators = parameterDecoratorData;\n            }\n            if (hasParameterData) {\n              (<ConstructorMetadata>data).parameters = parametersData;\n            }\n            if (!isMetadataError(name)) {\n              recordMember(name, data);\n            }\n            break;\n          case ts.SyntaxKind.PropertyDeclaration:\n          case ts.SyntaxKind.GetAccessor:\n          case ts.SyntaxKind.SetAccessor:\n            const property = <ts.PropertyDeclaration>member;\n            if (isStatic(property)) {\n              const name = evaluator.nameOf(property.name);\n              if (!isMetadataError(name)) {\n                if (property.initializer) {\n                  const value = evaluator.evaluateNode(property.initializer);\n                  recordStaticMember(name, value);\n                } else {\n                  recordStaticMember(name, errorSym('Variable not initialized', property.name));\n                }\n              }\n            }\n            const propertyDecorators = getDecorators(property.decorators);\n            if (propertyDecorators) {\n              const name = evaluator.nameOf(property.name);\n              if (!isMetadataError(name)) {\n                recordMember(name, {__symbolic: 'property', decorators: propertyDecorators});\n              }\n            }\n            break;\n        }\n      }\n      if (members) {\n        result.members = members;\n      }\n      if (statics) {\n        result.statics = statics;\n      }\n\n      return recordEntry(result, classDeclaration);\n    }\n\n    // Collect all exported symbols from an exports clause.\n    const exportMap = new Map<string, string>();\n    ts.forEachChild(sourceFile, node => {\n      switch (node.kind) {\n        case ts.SyntaxKind.ExportDeclaration:\n          const exportDeclaration = <ts.ExportDeclaration>node;\n          const {moduleSpecifier, exportClause} = exportDeclaration;\n\n          if (!moduleSpecifier) {\n            exportClause.elements.forEach(spec => {\n              const exportedAs = spec.name.text;\n              const name = (spec.propertyName || spec.name).text;\n              exportMap.set(name, exportedAs);\n            });\n          }\n      }\n    });\n\n    const isExportedIdentifier = (identifier: ts.Identifier) => exportMap.has(identifier.text);\n    const isExported =\n        (node: ts.FunctionDeclaration | ts.ClassDeclaration | ts.InterfaceDeclaration |\n         ts.EnumDeclaration) => isExport(node) || isExportedIdentifier(node.name);\n    const exportedIdentifierName = (identifier: ts.Identifier) =>\n        exportMap.get(identifier.text) || identifier.text;\n    const exportedName =\n        (node: ts.FunctionDeclaration | ts.ClassDeclaration | ts.InterfaceDeclaration |\n         ts.EnumDeclaration) => exportedIdentifierName(node.name);\n\n\n    // Predeclare classes and functions\n    ts.forEachChild(sourceFile, node => {\n      switch (node.kind) {\n        case ts.SyntaxKind.ClassDeclaration:\n          const classDeclaration = <ts.ClassDeclaration>node;\n          if (classDeclaration.name) {\n            const className = classDeclaration.name.text;\n            if (isExported(classDeclaration)) {\n              locals.define(\n                  className, {__symbolic: 'reference', name: exportedName(classDeclaration)});\n            } else {\n              locals.define(\n                  className, errorSym('Reference to non-exported class', node, {className}));\n            }\n          }\n          break;\n\n        case ts.SyntaxKind.InterfaceDeclaration:\n          const interfaceDeclaration = <ts.InterfaceDeclaration>node;\n          if (interfaceDeclaration.name) {\n            const interfaceName = interfaceDeclaration.name.text;\n            // All references to interfaces should be converted to references to `any`.\n            locals.define(interfaceName, {__symbolic: 'reference', name: 'any'});\n          }\n          break;\n\n        case ts.SyntaxKind.FunctionDeclaration:\n          const functionDeclaration = <ts.FunctionDeclaration>node;\n          if (!isExported(functionDeclaration)) {\n            // Report references to this function as an error.\n            const nameNode = functionDeclaration.name;\n            if (nameNode && nameNode.text) {\n              locals.define(\n                  nameNode.text,\n                  errorSym(\n                      'Reference to a non-exported function', nameNode, {name: nameNode.text}));\n            }\n          }\n          break;\n      }\n    });\n\n    ts.forEachChild(sourceFile, node => {\n      switch (node.kind) {\n        case ts.SyntaxKind.ExportDeclaration:\n          // Record export declarations\n          const exportDeclaration = <ts.ExportDeclaration>node;\n          const {moduleSpecifier, exportClause} = exportDeclaration;\n\n          if (!moduleSpecifier) {\n            // no module specifier -> export {propName as name};\n            if (exportClause) {\n              exportClause.elements.forEach(spec => {\n                const name = spec.name.text;\n                // If the symbol was not already exported, export a reference since it is a\n                // reference to an import\n                if (!metadata || !metadata[name]) {\n                  const propNode = spec.propertyName || spec.name;\n                  const value: MetadataValue = evaluator.evaluateNode(propNode);\n                  if (!metadata) metadata = {};\n                  metadata[name] = recordEntry(value, node);\n                }\n              });\n            }\n          }\n\n          if (moduleSpecifier && moduleSpecifier.kind == ts.SyntaxKind.StringLiteral) {\n            // Ignore exports that don't have string literals as exports.\n            // This is allowed by the syntax but will be flagged as an error by the type checker.\n            const from = (<ts.StringLiteral>moduleSpecifier).text;\n            const moduleExport: ModuleExportMetadata = {from};\n            if (exportClause) {\n              moduleExport.export = exportClause.elements.map(\n                  spec => spec.propertyName ? {name: spec.propertyName.text, as: spec.name.text} :\n                                              spec.name.text);\n            }\n            if (!exports) exports = [];\n            exports.push(moduleExport);\n          }\n          break;\n        case ts.SyntaxKind.ClassDeclaration:\n          const classDeclaration = <ts.ClassDeclaration>node;\n          if (classDeclaration.name) {\n            if (isExported(classDeclaration)) {\n              if (!metadata) metadata = {};\n              metadata[exportedName(classDeclaration)] = classMetadataOf(classDeclaration);\n            }\n          }\n          // Otherwise don't record metadata for the class.\n          break;\n\n        case ts.SyntaxKind.InterfaceDeclaration:\n          const interfaceDeclaration = <ts.InterfaceDeclaration>node;\n          if (interfaceDeclaration.name && isExported(interfaceDeclaration)) {\n            if (!metadata) metadata = {};\n            metadata[exportedName(interfaceDeclaration)] = {__symbolic: 'interface'};\n          }\n          break;\n\n        case ts.SyntaxKind.FunctionDeclaration:\n          // Record functions that return a single value. Record the parameter\n          // names substitution will be performed by the StaticReflector.\n          const functionDeclaration = <ts.FunctionDeclaration>node;\n          if (isExported(functionDeclaration) && functionDeclaration.name) {\n            if (!metadata) metadata = {};\n            const name = exportedName(functionDeclaration);\n            const maybeFunc = maybeGetSimpleFunction(functionDeclaration);\n            metadata[name] =\n                maybeFunc ? recordEntry(maybeFunc.func, node) : {__symbolic: 'function'};\n          }\n          break;\n\n        case ts.SyntaxKind.EnumDeclaration:\n          const enumDeclaration = <ts.EnumDeclaration>node;\n          if (isExported(enumDeclaration)) {\n            const enumValueHolder: {[name: string]: MetadataValue} = {};\n            const enumName = exportedName(enumDeclaration);\n            let nextDefaultValue: MetadataValue = 0;\n            let writtenMembers = 0;\n            for (const member of enumDeclaration.members) {\n              let enumValue: MetadataValue;\n              if (!member.initializer) {\n                enumValue = nextDefaultValue;\n              } else {\n                enumValue = evaluator.evaluateNode(member.initializer);\n              }\n              let name: string = undefined;\n              if (member.name.kind == ts.SyntaxKind.Identifier) {\n                const identifier = <ts.Identifier>member.name;\n                name = identifier.text;\n                enumValueHolder[name] = enumValue;\n                writtenMembers++;\n              }\n              if (typeof enumValue === 'number') {\n                nextDefaultValue = enumValue + 1;\n              } else if (name) {\n                nextDefaultValue = {\n                  __symbolic: 'binary',\n                  operator: '+',\n                  left: {\n                    __symbolic: 'select',\n                    expression: recordEntry({__symbolic: 'reference', name: enumName}, node), name\n                  }\n                };\n              } else {\n                nextDefaultValue =\n                    recordEntry(errorSym('Unsuppported enum member name', member.name), node);\n              }\n            }\n            if (writtenMembers) {\n              if (!metadata) metadata = {};\n              metadata[enumName] = recordEntry(enumValueHolder, node);\n            }\n          }\n          break;\n\n        case ts.SyntaxKind.VariableStatement:\n          const variableStatement = <ts.VariableStatement>node;\n          for (const variableDeclaration of variableStatement.declarationList.declarations) {\n            if (variableDeclaration.name.kind == ts.SyntaxKind.Identifier) {\n              const nameNode = <ts.Identifier>variableDeclaration.name;\n              let varValue: MetadataValue;\n              if (variableDeclaration.initializer) {\n                varValue = evaluator.evaluateNode(variableDeclaration.initializer);\n              } else {\n                varValue = recordEntry(errorSym('Variable not initialized', nameNode), nameNode);\n              }\n              let exported = false;\n              if (isExport(variableStatement) || isExport(variableDeclaration) ||\n                  isExportedIdentifier(nameNode)) {\n                if (!metadata) metadata = {};\n                metadata[exportedIdentifierName(nameNode)] = recordEntry(varValue, node);\n                exported = true;\n              }\n              if (typeof varValue == 'string' || typeof varValue == 'number' ||\n                  typeof varValue == 'boolean') {\n                locals.define(nameNode.text, varValue);\n              } else if (!exported) {\n                if (varValue && !isMetadataError(varValue)) {\n                  locals.define(nameNode.text, recordEntry(varValue, node));\n                } else {\n                  locals.define(\n                      nameNode.text,\n                      recordEntry(\n                          errorSym('Reference to a local symbol', nameNode, {name: nameNode.text}),\n                          node));\n                }\n              }\n            } else {\n              // Destructuring (or binding) declarations are not supported,\n              // var {<identifier>[, <identifier>]+} = <expression>;\n              //   or\n              // var [<identifier>[, <identifier}+] = <expression>;\n              // are not supported.\n              const report: (nameNode: ts.Node) => void = (nameNode: ts.Node) => {\n                switch (nameNode.kind) {\n                  case ts.SyntaxKind.Identifier:\n                    const name = <ts.Identifier>nameNode;\n                    const varValue = errorSym('Destructuring not supported', name);\n                    locals.define(name.text, varValue);\n                    if (isExport(node)) {\n                      if (!metadata) metadata = {};\n                      metadata[name.text] = varValue;\n                    }\n                    break;\n                  case ts.SyntaxKind.BindingElement:\n                    const bindingElement = <ts.BindingElement>nameNode;\n                    report(bindingElement.name);\n                    break;\n                  case ts.SyntaxKind.ObjectBindingPattern:\n                  case ts.SyntaxKind.ArrayBindingPattern:\n                    const bindings = <ts.BindingPattern>nameNode;\n                    (bindings as any).elements.forEach(report);\n                    break;\n                }\n              };\n              report(variableDeclaration.name);\n            }\n          }\n          break;\n      }\n    });\n\n    if (metadata || exports) {\n      if (!metadata)\n        metadata = {};\n      else if (strict) {\n        validateMetadata(sourceFile, nodeMap, metadata);\n      }\n      const result: ModuleMetadata = {\n        __symbolic: 'module',\n        version: this.options.version || VERSION, metadata\n      };\n      if (exports) result.exports = exports;\n      return result;\n    }\n  }\n}\n\n// This will throw if the metadata entry given contains an error node.\nfunction validateMetadata(\n    sourceFile: ts.SourceFile, nodeMap: Map<MetadataEntry, ts.Node>,\n    metadata: {[name: string]: MetadataEntry}) {\n  let locals: Set<string> = new Set(['Array', 'Object', 'Set', 'Map', 'string', 'number', 'any']);\n\n  function validateExpression(\n      expression: MetadataValue | MetadataSymbolicExpression | MetadataError) {\n    if (!expression) {\n      return;\n    } else if (Array.isArray(expression)) {\n      expression.forEach(validateExpression);\n    } else if (typeof expression === 'object' && !expression.hasOwnProperty('__symbolic')) {\n      Object.getOwnPropertyNames(expression).forEach(v => validateExpression((<any>expression)[v]));\n    } else if (isMetadataError(expression)) {\n      reportError(expression);\n    } else if (isMetadataGlobalReferenceExpression(expression)) {\n      if (!locals.has(expression.name)) {\n        const reference = <MetadataValue>metadata[expression.name];\n        if (reference) {\n          validateExpression(reference);\n        }\n      }\n    } else if (isFunctionMetadata(expression)) {\n      validateFunction(<any>expression);\n    } else if (isMetadataSymbolicExpression(expression)) {\n      switch (expression.__symbolic) {\n        case 'binary':\n          const binaryExpression = <MetadataSymbolicBinaryExpression>expression;\n          validateExpression(binaryExpression.left);\n          validateExpression(binaryExpression.right);\n          break;\n        case 'call':\n        case 'new':\n          const callExpression = <MetadataSymbolicCallExpression>expression;\n          validateExpression(callExpression.expression);\n          if (callExpression.arguments) callExpression.arguments.forEach(validateExpression);\n          break;\n        case 'index':\n          const indexExpression = <MetadataSymbolicIndexExpression>expression;\n          validateExpression(indexExpression.expression);\n          validateExpression(indexExpression.index);\n          break;\n        case 'pre':\n          const prefixExpression = <MetadataSymbolicPrefixExpression>expression;\n          validateExpression(prefixExpression.operand);\n          break;\n        case 'select':\n          const selectExpression = <MetadataSymbolicSelectExpression>expression;\n          validateExpression(selectExpression.expression);\n          break;\n        case 'spread':\n          const spreadExpression = <MetadataSymbolicSpreadExpression>expression;\n          validateExpression(spreadExpression.expression);\n          break;\n        case 'if':\n          const ifExpression = <MetadataSymbolicIfExpression>expression;\n          validateExpression(ifExpression.condition);\n          validateExpression(ifExpression.elseExpression);\n          validateExpression(ifExpression.thenExpression);\n          break;\n      }\n    }\n  }\n\n  function validateMember(classData: ClassMetadata, member: MemberMetadata) {\n    if (member.decorators) {\n      member.decorators.forEach(validateExpression);\n    }\n    if (isMethodMetadata(member) && member.parameterDecorators) {\n      member.parameterDecorators.forEach(validateExpression);\n    }\n    // Only validate parameters of classes for which we know that are used with our DI\n    if (classData.decorators && isConstructorMetadata(member) && member.parameters) {\n      member.parameters.forEach(validateExpression);\n    }\n  }\n\n  function validateClass(classData: ClassMetadata) {\n    if (classData.decorators) {\n      classData.decorators.forEach(validateExpression);\n    }\n    if (classData.members) {\n      Object.getOwnPropertyNames(classData.members)\n          .forEach(name => classData.members[name].forEach((m) => validateMember(classData, m)));\n    }\n    if (classData.statics) {\n      Object.getOwnPropertyNames(classData.statics).forEach(name => {\n        const staticMember = classData.statics[name];\n        if (isFunctionMetadata(staticMember)) {\n          validateExpression(staticMember.value);\n        } else {\n          validateExpression(staticMember);\n        }\n      });\n    }\n  }\n\n  function validateFunction(functionDeclaration: FunctionMetadata) {\n    if (functionDeclaration.value) {\n      const oldLocals = locals;\n      if (functionDeclaration.parameters) {\n        locals = new Set(oldLocals.values());\n        if (functionDeclaration.parameters)\n          functionDeclaration.parameters.forEach(n => locals.add(n));\n      }\n      validateExpression(functionDeclaration.value);\n      locals = oldLocals;\n    }\n  }\n\n  function shouldReportNode(node: ts.Node) {\n    if (node) {\n      const nodeStart = node.getStart();\n      return !(\n          node.pos != nodeStart &&\n          sourceFile.text.substring(node.pos, nodeStart).indexOf('@dynamic') >= 0);\n    }\n    return true;\n  }\n\n  function reportError(error: MetadataError) {\n    const node = nodeMap.get(error);\n    if (shouldReportNode(node)) {\n      const lineInfo = error.line != undefined ?\n          error.character != undefined ? `:${error.line + 1}:${error.character + 1}` :\n                                         `:${error.line + 1}` :\n          '';\n      throw new Error(\n          `${sourceFile.fileName}${lineInfo}: Metadata collected contains an error that will be reported at runtime: ${expandedMessage(error)}.\\n  ${JSON.stringify(error)}`);\n    }\n  }\n\n  Object.getOwnPropertyNames(metadata).forEach(name => {\n    const entry = metadata[name];\n    try {\n      if (isClassMetadata(entry)) {\n        validateClass(entry);\n      }\n    } catch (e) {\n      const node = nodeMap.get(entry);\n      if (shouldReportNode(node)) {\n        if (node) {\n          const {line, character} = sourceFile.getLineAndCharacterOfPosition(node.getStart());\n          throw new Error(\n              `${sourceFile.fileName}:${line + 1}:${character + 1}: Error encountered in metadata generated for exported symbol '${name}': \\n ${e.message}`);\n        }\n        throw new Error(\n            `Error encountered in metadata generated for exported symbol ${name}: \\n ${e.message}`);\n      }\n    }\n  });\n}\n\n// Collect parameter names from a function.\nfunction namesOf(parameters: ts.NodeArray<ts.ParameterDeclaration>): string[] {\n  const result: string[] = [];\n\n  function addNamesOf(name: ts.Identifier | ts.BindingPattern) {\n    if (name.kind == ts.SyntaxKind.Identifier) {\n      const identifier = <ts.Identifier>name;\n      result.push(identifier.text);\n    } else {\n      const bindingPattern = <ts.BindingPattern>name;\n      for (const element of bindingPattern.elements) {\n        const name = (element as any).name;\n        if (name) {\n          addNamesOf(name);\n        }\n      }\n    }\n  }\n\n  for (const parameter of parameters) {\n    addNamesOf(parameter.name);\n  }\n\n  return result;\n}\n\nfunction expandedMessage(error: any): string {\n  switch (error.message) {\n    case 'Reference to non-exported class':\n      if (error.context && error.context.className) {\n        return `Reference to a non-exported class ${error.context.className}. Consider exporting the class`;\n      }\n      break;\n    case 'Variable not initialized':\n      return 'Only initialized variables and constants can be referenced because the value of this variable is needed by the template compiler';\n    case 'Destructuring not supported':\n      return 'Referencing an exported destructured variable or constant is not supported by the template compiler. Consider simplifying this to avoid destructuring';\n    case 'Could not resolve type':\n      if (error.context && error.context.typeName) {\n        return `Could not resolve type ${error.context.typeName}`;\n      }\n      break;\n    case 'Function call not supported':\n      let prefix =\n          error.context && error.context.name ? `Calling function '${error.context.name}', f` : 'F';\n      return prefix +\n          'unction calls are not supported. Consider replacing the function or lambda with a reference to an exported function';\n    case 'Reference to a local symbol':\n      if (error.context && error.context.name) {\n        return `Reference to a local (non-exported) symbol '${error.context.name}'. Consider exporting the symbol`;\n      }\n  }\n  return error.message;\n}\n"]}