{"_args": [["atoa@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "atoa@1.0.0", "_id": "atoa@1.0.0", "_inBundle": false, "_integrity": "sha1-DMDpGkgOc4+SPrwQNnZHF3mzSkk=", "_location": "/atoa", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "atoa@1.0.0", "name": "atoa", "escapedName": "atoa", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/contra"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/atoa/-/atoa-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "nicola<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "url": "http://bevacqua.io/"}, "bugs": {"url": "https://github.com/bevacqua/atoa/issues"}, "description": "Creates a true array based on `arraylike`, starting at `startIndex`.", "homepage": "https://github.com/bevacqua/atoa#readme", "license": "MIT", "main": "atoa.js", "name": "atoa", "repository": {"type": "git", "url": "git+https://github.com/bevacqua/atoa.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.0"}