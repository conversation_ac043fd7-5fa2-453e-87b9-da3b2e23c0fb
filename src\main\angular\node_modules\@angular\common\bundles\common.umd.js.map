{"version": 3, "file": "common.umd.js", "sources": ["../../../../packages/common/src/common_module.ts", "../../../../packages/common/src/pipes/index.ts", "../../../../packages/common/src/pipes/slice_pipe.ts", "../../../../packages/common/src/pipes/json_pipe.ts", "../../../../packages/common/src/pipes/i18n_select_pipe.ts", "../../../../packages/common/src/pipes/i18n_plural_pipe.ts", "../../../../packages/common/src/pipes/date_pipe.ts", "../../../../packages/common/src/pipes/number_pipe.ts", "../../../../packages/common/src/pipes/intl.ts", "../../../../packages/common/src/pipes/case_conversion_pipes.ts", "../../../../packages/common/src/pipes/async_pipe.ts", "../../../../packages/common/src/pipes/invalid_pipe_argument_error.ts", "../../../../packages/common/src/directives/index.ts", "../../../../packages/common/src/directives/ng_template_outlet.ts", "../../../../packages/common/src/directives/ng_style.ts", "../../../../packages/common/src/directives/ng_plural.ts", "../../../../packages/common/src/directives/ng_switch.ts", "../../../../packages/common/src/directives/ng_if.ts", "../../../../packages/common/src/directives/ng_for_of.ts", "../../../../packages/common/src/directives/ng_component_outlet.ts", "../../../../packages/common/src/directives/ng_class.ts", "../../../../packages/common/src/localization.ts", "../../../../packages/common/src/location/index.ts", "../../../../packages/common/src/location/path_location_strategy.ts", "../../../../packages/common/src/location/hash_location_strategy.ts", "../../../../packages/common/src/location/location.ts", "../../../../packages/common/src/location/location_strategy.ts", "../../../../packages/common/src/location/platform_location.ts", "../../../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {NgModule} from '@angular/core';\n\nimport {COMMON_DEPRECATED_DIRECTIVES, COMMON_DIRECTIVES} from './directives/index';\nimport {NgLocaleLocalization, NgLocalization} from './localization';\nimport {COMMON_PIPES} from './pipes/index';\n/**\n * The module that includes all the basic Angular directives like {\\@link NgIf}, {\\@link NgForOf}, ...\n * \n * \\@stable\n */\nexport class CommonModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  declarations: [COMMON_DIRECTIVES, COMMON_PIPES],\n  exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n  providers: [\n    {provide: NgLocalization, useClass: NgLocaleLocalization},\n  ],\n}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction CommonModule_tsickle_Closure_declarations() {\n/** @type {?} */\nCommonModule.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCommonModule.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\n\nimport {AsyncPipe} from './async_pipe';\nimport {LowerCasePipe, TitleCasePipe, UpperCasePipe} from './case_conversion_pipes';\nimport {DatePipe} from './date_pipe';\nimport {I18nPluralPipe} from './i18n_plural_pipe';\nimport {I18nSelectPipe} from './i18n_select_pipe';\nimport {JsonPipe} from './json_pipe';\nimport {CurrencyPipe, DecimalPipe, PercentPipe} from './number_pipe';\nimport {SlicePipe} from './slice_pipe';\n\nexport {\n  AsyncPipe,\n  CurrencyPipe,\n  DatePipe,\n  DecimalPipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  LowerCasePipe,\n  PercentPipe,\n  SlicePipe,\n  TitleCasePipe,\n  UpperCasePipe\n};\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport const COMMON_PIPES = [\n  AsyncPipe,\n  UpperCasePipe,\n  LowerCasePipe,\n  JsonPipe,\n  SlicePipe,\n  DecimalPipe,\n  PercentPipe,\n  TitleCasePipe,\n  CurrencyPipe,\n  DatePipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a new List or String containing a subset (slice) of the elements.\n * \\@howToUse `array_or_string_expression | slice:start[:end]`\n * \\@description \n * \n * Where the input expression is a `List` or `String`, and:\n * - `start`: The starting index of the subset to return.\n *   - **a positive integer**: return the item at `start` index and all items after\n *     in the list or string expression.\n *   - **a negative integer**: return the item at `start` index from the end and all items after\n *     in the list or string expression.\n *   - **if positive and greater than the size of the expression**: return an empty list or string.\n *   - **if negative and greater than the size of the expression**: return entire list or string.\n * - `end`: The ending index of the subset to return.\n *   - **omitted**: return all items until the end.\n *   - **if positive**: return all items before `end` index of the list or string.\n *   - **if negative**: return all items before `end` index from the end of the list or string.\n * \n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n * \n * When operating on a [List], the returned list is always a copy even when all\n * the elements are being returned.\n * \n * When operating on a blank value, the pipe returns the blank value.\n * \n * ## List Example\n * \n * This `ngFor` example:\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n * \n * produces the following:\n * \n *     <li>b</li>\n *     <li>c</li>\n * \n * ## String Examples\n * \n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n * \n * \\@stable\n */\nexport class SlicePipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} start\n * @param {?=} end\n * @return {?}\n */\ntransform(value: any, start: number, end?: number): any {\n    if (value == null) return value;\n\n    if (!this.supports(value)) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n\n    return value.slice(start, end);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate supports(obj: any): boolean { return typeof obj === 'string' || Array.isArray(obj); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'slice', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction SlicePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nSlicePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nSlicePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Converts value into JSON string.\n * \\@howToUse `expression | json`\n * \\@description \n * \n * Converts value into string using `JSON.stringify`. Useful for debugging.\n * \n * ### Example\n * {\\@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n * \n * \\@stable\n */\nexport class JsonPipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: any): string { return JSON.stringify(value, null, 2); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'json', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction JsonPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nJsonPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nJsonPipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Generic selector that displays the string that matches the current value.\n * \\@howToUse `expression | i18nSelect:mapping`\n * \\@description \n * \n *  Where `mapping` is an object that indicates the text that should be displayed\n *  for different values of the provided `expression`.\n *  If none of the keys of the mapping match the value of the `expression`, then the content\n *  of the `other` key is returned when present, otherwise an empty string is returned.\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n * \n *  \\@experimental\n */\nexport class I18nSelectPipe implements PipeTransform {\n/**\n * @param {?} value\n * @param {?} mapping\n * @return {?}\n */\ntransform(value: string|null|undefined, mapping: {[key: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n\n    return '';\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nSelect', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction I18nSelectPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nSelectPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nSelectPipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {NgLocalization, getPluralCategory} from '../localization';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _INTERPOLATION_REGEXP: RegExp = /#/g;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Maps a value to a string that pluralizes the value according to locale rules.\n * \\@howToUse `expression | i18nPlural:mapping`\n * \\@description \n * \n *  Where:\n *  - `expression` is a number.\n *  - `mapping` is an object that mimics the ICU format, see\n *    http://userguide.icu-project.org/formatparse/messages\n * \n *  ## Example\n * \n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n * \n * \\@experimental\n */\nexport class I18nPluralPipe implements PipeTransform {\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @param {?} pluralMap\n * @return {?}\n */\ntransform(value: number, pluralMap: {[count: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n\n    const /** @type {?} */ key = getPluralCategory(value, Object.keys(pluralMap), this._localization);\n\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'i18nPlural', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\n}\n\nfunction I18nPluralPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nI18nPluralPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nI18nPluralPipe.ctorParameters;\n/** @type {?} */\nI18nPluralPipe.prototype._localization;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform} from '@angular/core';\nimport {DateFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\nimport {isNumeric} from './number_pipe';\n\nconst /** @type {?} */ ISO8601_DATE_REGEX =\n    /^(\\d{4})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a date according to locale rules.\n * \\@howToUse `date_expression | date[:format]`\n * \\@description \n * \n * Where:\n * - `expression` is a date object or a number (milliseconds since UTC epoch) or an ISO string\n * (https://www.w3.org/TR/NOTE-datetime).\n * - `format` indicates which date/time components to include. The format can be predefined as\n *   shown below or custom as shown in the table.\n *   - `'medium'`: equivalent to `'yMMMdjms'` (e.g. `Sep 3, 2010, 12:05:08 PM` for `en-US`)\n *   - `'short'`: equivalent to `'yMdjm'` (e.g. `9/3/2010, 12:05 PM` for `en-US`)\n *   - `'fullDate'`: equivalent to `'yMMMMEEEEd'` (e.g. `Friday, September 3, 2010` for `en-US`)\n *   - `'longDate'`: equivalent to `'yMMMMd'` (e.g. `September 3, 2010` for `en-US`)\n *   - `'mediumDate'`: equivalent to `'yMMMd'` (e.g. `Sep 3, 2010` for `en-US`)\n *   - `'shortDate'`: equivalent to `'yMd'` (e.g. `9/3/2010` for `en-US`)\n *   - `'mediumTime'`: equivalent to `'jms'` (e.g. `12:05:08 PM` for `en-US`)\n *   - `'shortTime'`: equivalent to `'jm'` (e.g. `12:05 PM` for `en-US`)\n * \n * \n *  | Component | Symbol | Narrow | Short Form   | Long Form         | Numeric   | 2-digit   |\n *  |-----------|:------:|--------|--------------|-------------------|-----------|-----------|\n *  | era       |   G    | G (A)  | GGG (AD)     | GGGG (Anno Domini)| -         | -         |\n *  | year      |   y    | -      | -            | -                 | y (2015)  | yy (15)   |\n *  | month     |   M    | L (S)  | MMM (Sep)    | MMMM (September)  | M (9)     | MM (09)   |\n *  | day       |   d    | -      | -            | -                 | d (3)     | dd (03)   |\n *  | weekday   |   E    | E (S)  | EEE (Sun)    | EEEE (Sunday)     | -         | -         |\n *  | hour      |   j    | -      | -            | -                 | j (13)    | jj (13)   |\n *  | hour12    |   h    | -      | -            | -                 | h (1 PM)  | hh (01 PM)|\n *  | hour24    |   H    | -      | -            | -                 | H (13)    | HH (13)   |\n *  | minute    |   m    | -      | -            | -                 | m (5)     | mm (05)   |\n *  | second    |   s    | -      | -            | -                 | s (9)     | ss (09)   |\n *  | timezone  |   z    | -      | -            | z (Pacific Standard Time)| -  | -         |\n *  | timezone  |   Z    | -      | Z (GMT-8:00) | -                 | -         | -         |\n *  | timezone  |   a    | -      | a (PM)       | -                 | -         | -         |\n * \n * In javascript, only the components specified will be respected (not the ordering,\n * punctuations, ...) and details of the formatting will be dependent on the locale.\n * \n * Timezone of the formatted text will be the local system timezone of the end-user's machine.\n * \n * When the expression is a ISO string without time (e.g. 2016-09-19) the time zone offset is not\n * applied and the formatted text will have the same day, month and year of the expression.\n * \n * WARNINGS:\n * - this pipe is marked as pure hence it will not be re-evaluated when the input is mutated.\n *   Instead users should treat the date as an immutable object and change the reference when the\n *   pipe needs to re-run (this is to avoid reformatting the date on every change detection run\n *   which would be an expensive operation).\n * - this pipe uses the Internationalization API. Therefore it is only reliable in Chrome and Opera\n *   browsers.\n * \n * ### Examples\n * \n * Assuming `dateObj` is (year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11)\n * in the _local_ time and locale is 'en-US':\n * \n * ```\n *     {{ dateObj | date }}               // output is 'Jun 15, 2015'\n *     {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n *     {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n *     {{ dateObj | date:'mmss' }}        // output is '43:11'\n * ```\n * \n * {\\@example common/pipes/ts/date_pipe.ts region='DatePipe'}\n * \n * \\@stable\n */\nexport class DatePipe implements PipeTransform {\n/**\n * \\@internal\n */\nstatic _ALIASES: {[key: string]: string} = {\n    'medium': 'yMMMdjms',\n    'short': 'yMdjm',\n    'fullDate': 'yMMMMEEEEd',\n    'longDate': 'yMMMMd',\n    'mediumDate': 'yMMMd',\n    'shortDate': 'yMd',\n    'mediumTime': 'jms',\n    'shortTime': 'jm'\n  };\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} pattern\n * @return {?}\n */\ntransform(value: any, pattern: string = 'mediumDate'): string|null {\n    let /** @type {?} */ date: Date;\n\n    if (isBlank(value) || value !== value) return null;\n\n    if (typeof value === 'string') {\n      value = value.trim();\n    }\n\n    if (isDate(value)) {\n      date = value;\n    } else if (isNumeric(value)) {\n      date = new Date(parseFloat(value));\n    } else if (typeof value === 'string' && /^(\\d{4}-\\d{1,2}-\\d{1,2})$/.test(value)) {\n/**\n * For ISO Strings without time the day, month and year must be extracted from the ISO String\n * before Date creation to avoid time offset and errors in the new Date.\n * If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n * date, some browsers (e.g. IE 9) will throw an invalid Date error\n * If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n * is applied\n * Note: ISO months are 0 for January, 1 for February, ...\n */\nconst [y, m, d] = value.split('-').map((val: string) => parseInt(val, 10));\n      date = new Date(y, m - 1, d);\n    } else {\n      date = new Date(value);\n    }\n\n    if (!isDate(date)) {\n      let /** @type {?} */ match: RegExpMatchArray|null;\n      if ((typeof value === 'string') && (match = value.match(ISO8601_DATE_REGEX))) {\n        date = isoStringToDate(match);\n      } else {\n        throw invalidPipeArgumentError(DatePipe, value);\n      }\n    }\n\n    return DateFormatter.format(date, this._locale, DatePipe._ALIASES[pattern] || pattern);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'date', pure: true}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DatePipe_tsickle_Closure_declarations() {\n/**\n * \\@internal\n * @type {?}\n */\nDatePipe._ALIASES;\n/** @type {?} */\nDatePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDatePipe.ctorParameters;\n/** @type {?} */\nDatePipe.prototype._locale;\n}\n\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isBlank(obj: any): boolean {\n  return obj == null || obj === '';\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction isDate(obj: any): obj is Date {\n  return obj instanceof Date && !isNaN(obj.valueOf());\n}\n/**\n * @param {?} match\n * @return {?}\n */\nfunction isoStringToDate(match: RegExpMatchArray): Date {\n  const /** @type {?} */ date = new Date(0);\n  let /** @type {?} */ tzHour = 0;\n  let /** @type {?} */ tzMin = 0;\n  const /** @type {?} */ dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const /** @type {?} */ timeSetter = match[8] ? date.setUTCHours : date.setHours;\n\n  if (match[9]) {\n    tzHour = toInt(match[9] + match[10]);\n    tzMin = toInt(match[9] + match[11]);\n  }\n  dateSetter.call(date, toInt(match[1]), toInt(match[2]) - 1, toInt(match[3]));\n  const /** @type {?} */ h = toInt(match[4] || '0') - tzHour;\n  const /** @type {?} */ m = toInt(match[5] || '0') - tzMin;\n  const /** @type {?} */ s = toInt(match[6] || '0');\n  const /** @type {?} */ ms = Math.round(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n/**\n * @param {?} str\n * @return {?}\n */\nfunction toInt(str: string): number {\n  return parseInt(str, 10);\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, LOCALE_ID, Pipe, PipeTransform, Type} from '@angular/core';\nimport {NumberFormatStyle, NumberFormatter} from './intl';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst /** @type {?} */ _NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\n/**\n * @param {?} pipe\n * @param {?} locale\n * @param {?} value\n * @param {?} style\n * @param {?=} digits\n * @param {?=} currency\n * @param {?=} currencyAsSymbol\n * @return {?}\n */\nfunction formatNumber(\n    pipe: Type<any>, locale: string, value: number | string, style: NumberFormatStyle,\n    digits?: string | null, currency: string | null = null,\n    currencyAsSymbol: boolean = false): string|null {\n  if (value == null) return null;\n\n  // Convert strings to numbers\n  value = typeof value === 'string' && isNumeric(value) ? +value : value;\n  if (typeof value !== 'number') {\n    throw invalidPipeArgumentError(pipe, value);\n  }\n\n  let /** @type {?} */ minInt: number|undefined = undefined;\n  let /** @type {?} */ minFraction: number|undefined = undefined;\n  let /** @type {?} */ maxFraction: number|undefined = undefined;\n  if (style !== NumberFormatStyle.Currency) {\n    // rely on Intl default for currency\n    minInt = 1;\n    minFraction = 0;\n    maxFraction = 3;\n  }\n\n  if (digits) {\n    const /** @type {?} */ parts = digits.match(_NUMBER_FORMAT_REGEXP);\n    if (parts === null) {\n      throw new Error(`${digits} is not a valid digit info for number pipes`);\n    }\n    if (parts[1] != null) {  // min integer digits\n      minInt = parseIntAutoRadix(parts[1]);\n    }\n    if (parts[3] != null) {  // min fraction digits\n      minFraction = parseIntAutoRadix(parts[3]);\n    }\n    if (parts[5] != null) {  // max fraction digits\n      maxFraction = parseIntAutoRadix(parts[5]);\n    }\n  }\n\n  return NumberFormatter.format( /** @type {?} */((value as number)), locale, style, {\n    minimumIntegerDigits: minInt,\n    minimumFractionDigits: minFraction,\n    maximumFractionDigits: maxFraction,\n    currency: currency,\n    currencyAsSymbol: currencyAsSymbol,\n  });\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number according to locale rules.\n * \\@howToUse `number_expression | number[:digitInfo]`\n * \n * Formats a number as text. Group sizing and separator and other locale-specific\n * configurations are based on the active locale.\n * \n * where `expression` is a number:\n *  - `digitInfo` is a `string` which has a following format: <br>\n *     <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>\n *   - `minIntegerDigits` is the minimum number of integer digits to use. Defaults to `1`.\n *   - `minFractionDigits` is the minimum number of digits after fraction. Defaults to `0`.\n *   - `maxFractionDigits` is the maximum number of digits after fraction. Defaults to `3`.\n * \n * For more information on the acceptable range for each of these numbers and other\n * details see your native internationalization library.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='NumberPipe'}\n * \n * \\@stable\n */\nexport class DecimalPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(DecimalPipe, this._locale, value, NumberFormatStyle.Decimal, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'number'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction DecimalPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nDecimalPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nDecimalPipe.ctorParameters;\n/** @type {?} */\nDecimalPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as a percentage according to locale rules.\n * \\@howToUse `number_expression | percent[:digitInfo]`\n * \n * \\@description \n * \n * Formats a number as percentage.\n * \n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='PercentPipe'}\n * \n * \\@stable\n */\nexport class PercentPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} digits\n * @return {?}\n */\ntransform(value: any, digits?: string): string|null {\n    return formatNumber(PercentPipe, this._locale, value, NumberFormatStyle.Percent, digits);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'percent'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction PercentPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nPercentPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPercentPipe.ctorParameters;\n/** @type {?} */\nPercentPipe.prototype._locale;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as currency using locale rules.\n * \\@howToUse `number_expression | currency[:currencyCode[:symbolDisplay[:digitInfo]]]`\n * \\@description \n * \n * Use `currency` to format a number as currency.\n * \n * - `currencyCode` is the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code, such\n *    as `USD` for the US dollar and `EUR` for the euro.\n * - `symbolDisplay` is a boolean indicating whether to use the currency symbol or code.\n *   - `true`: use symbol (e.g. `$`).\n *   - `false`(default): use code (e.g. `USD`).\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n * \n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n * \n * ### Example\n * \n * {\\@example common/pipes/ts/number_pipe.ts region='CurrencyPipe'}\n * \n * \\@stable\n */\nexport class CurrencyPipe implements PipeTransform {\n/**\n * @param {?} _locale\n */\nconstructor(\nprivate _locale: string) {}\n/**\n * @param {?} value\n * @param {?=} currencyCode\n * @param {?=} symbolDisplay\n * @param {?=} digits\n * @return {?}\n */\ntransform(\n      value: any, currencyCode: string = 'USD', symbolDisplay: boolean = false,\n      digits?: string): string|null {\n    return formatNumber(\n        CurrencyPipe, this._locale, value, NumberFormatStyle.Currency, digits, currencyCode,\n        symbolDisplay);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'currency'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction CurrencyPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nCurrencyPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nCurrencyPipe.ctorParameters;\n/** @type {?} */\nCurrencyPipe.prototype._locale;\n}\n\n/**\n * @param {?} text\n * @return {?}\n */\nfunction parseIntAutoRadix(text: string): number {\n  const /** @type {?} */ result: number = parseInt(text);\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n  return result;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nexport function isNumeric(value: any): boolean {\n  return !isNaN(value - parseFloat(value));\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "\nexport type NumberFormatStyle = number;\nexport let NumberFormatStyle: any = {};\nNumberFormatStyle.Decimal = 0;\nNumberFormatStyle.Percent = 1;\nNumberFormatStyle.Currency = 2;\nNumberFormatStyle[NumberFormatStyle.Decimal] = \"Decimal\";\nNumberFormatStyle[NumberFormatStyle.Percent] = \"Percent\";\nNumberFormatStyle[NumberFormatStyle.Currency] = \"Currency\";\n\nexport class NumberFormatter {\n/**\n * @param {?} num\n * @param {?} locale\n * @param {?} style\n * @param {?=} opts\n * @return {?}\n */\nstatic format(num: number, locale: string, style: NumberFormatStyle, opts: {\n    minimumIntegerDigits?: number,\n    minimumFractionDigits?: number,\n    maximumFractionDigits?: number,\n    currency?: string|null,\n    currencyAsSymbol?: boolean\n  } = {}): string {\n    const {minimumIntegerDigits, minimumFractionDigits, maximumFractionDigits, currency,\n           currencyAsSymbol = false} = opts;\n    const /** @type {?} */ options: Intl.NumberFormatOptions = {\n      minimumIntegerDigits,\n      minimumFractionDigits,\n      maximumFractionDigits,\n      style: NumberFormatStyle[style].toLowerCase()\n    };\n\n    if (style == NumberFormatStyle.Currency) {\n      options.currency = typeof currency == 'string' ? currency : undefined;\n      options.currencyDisplay = currencyAsSymbol ? 'symbol' : 'code';\n    }\n    return new Intl.NumberFormat(locale, options).format(num);\n  }\n}\n\ntype DateFormatterFn = (date: Date, locale: string) => string;\n\nconst /** @type {?} */ DATE_FORMATS_SPLIT =\n    /((?:[^yMLdHhmsazZEwGjJ']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|J+|j+|m+|s+|a|z|Z|G+|w+))(.*)/;\n\nconst /** @type {?} */ PATTERN_ALIASES: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed during closure compilation.\n  'yMMMdjms': datePartGetterFactory(combine([\n    digitCondition('year', 1),\n    nameCondition('month', 3),\n    digitCondition('day', 1),\n    digitCondition('hour', 1),\n    digitCondition('minute', 1),\n    digitCondition('second', 1),\n  ])),\n  'yMdjm': datePartGetterFactory(combine([\n    digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1),\n    digitCondition('hour', 1), digitCondition('minute', 1)\n  ])),\n  'yMMMMEEEEd': datePartGetterFactory(combine([\n    digitCondition('year', 1), nameCondition('month', 4), nameCondition('weekday', 4),\n    digitCondition('day', 1)\n  ])),\n  'yMMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 4), digitCondition('day', 1)])),\n  'yMMMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), nameCondition('month', 3), digitCondition('day', 1)])),\n  'yMd': datePartGetterFactory(\n      combine([digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1)])),\n  'jms': datePartGetterFactory(combine(\n      [digitCondition('hour', 1), digitCondition('second', 1), digitCondition('minute', 1)])),\n  'jm': datePartGetterFactory(combine([digitCondition('hour', 1), digitCondition('minute', 1)]))\n};\n\nconst /** @type {?} */ DATE_FORMATS: {[format: string]: DateFormatterFn} = {\n  // Keys are quoted so they do not get renamed.\n  'yyyy': datePartGetterFactory(digitCondition('year', 4)),\n  'yy': datePartGetterFactory(digitCondition('year', 2)),\n  'y': datePartGetterFactory(digitCondition('year', 1)),\n  'MMMM': datePartGetterFactory(nameCondition('month', 4)),\n  'MMM': datePartGetterFactory(nameCondition('month', 3)),\n  'MM': datePartGetterFactory(digitCondition('month', 2)),\n  'M': datePartGetterFactory(digitCondition('month', 1)),\n  'LLLL': datePartGetterFactory(nameCondition('month', 4)),\n  'L': datePartGetterFactory(nameCondition('month', 1)),\n  'dd': datePartGetterFactory(digitCondition('day', 2)),\n  'd': datePartGetterFactory(digitCondition('day', 1)),\n  'HH': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), false)))),\n  'H': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), false))),\n  'hh': digitModifier(\n      hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), true)))),\n  'h': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'jj': datePartGetterFactory(digitCondition('hour', 2)),\n  'j': datePartGetterFactory(digitCondition('hour', 1)),\n  'mm': digitModifier(datePartGetterFactory(digitCondition('minute', 2))),\n  'm': datePartGetterFactory(digitCondition('minute', 1)),\n  'ss': digitModifier(datePartGetterFactory(digitCondition('second', 2))),\n  's': datePartGetterFactory(digitCondition('second', 1)),\n  // while ISO 8601 requires fractions to be prefixed with `.` or `,`\n  // we can be just safely rely on using `sss` since we currently don't support single or two digit\n  // fractions\n  'sss': datePartGetterFactory(digitCondition('second', 3)),\n  'EEEE': datePartGetterFactory(nameCondition('weekday', 4)),\n  'EEE': datePartGetterFactory(nameCondition('weekday', 3)),\n  'EE': datePartGetterFactory(nameCondition('weekday', 2)),\n  'E': datePartGetterFactory(nameCondition('weekday', 1)),\n  'a': hourClockExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n  'Z': timeZoneGetter('short'),\n  'z': timeZoneGetter('long'),\n  'ww': datePartGetterFactory({}),  // Week of year, padded (00-53). Week 01 is the week with the\n                                    // first Thursday of the year. not support ?\n  'w':\n      datePartGetterFactory({}),  // Week of year (0-53). Week 1 is the week with the first Thursday\n                                  // of the year not support ?\n  'G': datePartGetterFactory(nameCondition('era', 1)),\n  'GG': datePartGetterFactory(nameCondition('era', 2)),\n  'GGG': datePartGetterFactory(nameCondition('era', 3)),\n  'GGGG': datePartGetterFactory(nameCondition('era', 4))\n};\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction digitModifier(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = inner(date, locale);\n    return result.length == 1 ? '0' + result : result;\n  };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourClockExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[1]; };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourExtractor(inner: DateFormatterFn): DateFormatterFn {\n  return function(date: Date, locale: string): string { return inner(date, locale).split(' ')[0]; };\n}\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} options\n * @return {?}\n */\nfunction intlDateFormat(date: Date, locale: string, options: Intl.DateTimeFormatOptions): string {\n  return new Intl.DateTimeFormat(locale, options).format(date).replace(/[\\u200e\\u200f]/g, '');\n}\n/**\n * @param {?} timezone\n * @return {?}\n */\nfunction timeZoneGetter(timezone: string): DateFormatterFn {\n  // To workaround `Intl` API restriction for single timezone let format with 24 hours\n  const /** @type {?} */ options = {hour: '2-digit', hour12: false, timeZoneName: timezone};\n  return function(date: Date, locale: string): string {\n    const /** @type {?} */ result = intlDateFormat(date, locale, options);\n    // Then extract first 3 letters that related to hours\n    return result ? result.substring(3) : '';\n  };\n}\n/**\n * @param {?} options\n * @param {?} value\n * @return {?}\n */\nfunction hour12Modify(\n    options: Intl.DateTimeFormatOptions, value: boolean): Intl.DateTimeFormatOptions {\n  options.hour12 = value;\n  return options;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction digitCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  result[prop] = len === 2 ? '2-digit' : 'numeric';\n  return result;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction nameCondition(prop: string, len: number): Intl.DateTimeFormatOptions {\n  const /** @type {?} */ result: {[k: string]: string} = {};\n  if (len < 4) {\n    result[prop] = len > 1 ? 'short' : 'narrow';\n  } else {\n    result[prop] = 'long';\n  }\n\n  return result;\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction combine(options: Intl.DateTimeFormatOptions[]): Intl.DateTimeFormatOptions {\n  return ( /** @type {?} */((<any>Object))).assign({}, ...options);\n}\n/**\n * @param {?} ret\n * @return {?}\n */\nfunction datePartGetterFactory(ret: Intl.DateTimeFormatOptions): DateFormatterFn {\n  return (date: Date, locale: string): string => intlDateFormat(date, locale, ret);\n}\n\nconst /** @type {?} */ DATE_FORMATTER_CACHE = new Map<string, string[]>();\n/**\n * @param {?} format\n * @param {?} date\n * @param {?} locale\n * @return {?}\n */\nfunction dateFormatter(format: string, date: Date, locale: string): string {\n  const /** @type {?} */ fn = PATTERN_ALIASES[format];\n\n  if (fn) return fn(date, locale);\n\n  const /** @type {?} */ cacheKey = format;\n  let /** @type {?} */ parts = DATE_FORMATTER_CACHE.get(cacheKey);\n\n  if (!parts) {\n    parts = [];\n    let /** @type {?} */ match: RegExpExecArray|null;\n    DATE_FORMATS_SPLIT.exec(format);\n\n    let /** @type {?} */ _format: string|null = format;\n    while (_format) {\n      match = DATE_FORMATS_SPLIT.exec(_format);\n      if (match) {\n        parts = parts.concat(match.slice(1));\n        _format = /** @type {?} */(( parts.pop()));\n      } else {\n        parts.push(_format);\n        _format = null;\n      }\n    }\n\n    DATE_FORMATTER_CACHE.set(cacheKey, parts);\n  }\n\n  return parts.reduce((text, part) => {\n    const /** @type {?} */ fn = DATE_FORMATS[part];\n    return text + (fn ? fn(date, locale) : partToTime(part));\n  }, '');\n}\n/**\n * @param {?} part\n * @return {?}\n */\nfunction partToTime(part: string): string {\n  return part === '\\'\\'' ? '\\'' : part.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n}\nexport class DateFormatter {\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} pattern\n * @return {?}\n */\nstatic format(date: Date, locale: string, pattern: string): string {\n    return dateFormatter(pattern, date, locale);\n  }\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Pipe, PipeTransform} from '@angular/core';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n/**\n * Transforms text to lowercase.\n * \n * {\\@example  common/pipes/ts/lowerupper_pipe.ts region='LowerUpperPipe' }\n * \n * \\@stable\n */\nexport class LowerCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'lowercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction LowerCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nLowerCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLowerCasePipe.ctorParameters;\n}\n\n/**\n * Helper method to transform a single word to titlecase.\n * \n * \\@stable\n * @param {?} word\n * @return {?}\n */\nfunction titleCaseWord(word: string) {\n  if (!word) return word;\n  return word[0].toUpperCase() + word.substr(1).toLowerCase();\n}\n/**\n * Transforms text to titlecase.\n * \n * \\@stable\n */\nexport class TitleCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n\n    return value.split(/\\b/g).map(word => titleCaseWord(word)).join('');\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'titlecase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction TitleCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nTitleCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nTitleCasePipe.ctorParameters;\n}\n\n/**\n * Transforms text to uppercase.\n * \n * \\@stable\n */\nexport class UpperCasePipe implements PipeTransform {\n/**\n * @param {?} value\n * @return {?}\n */\ntransform(value: string): string {\n    if (!value) return value;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'uppercase'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nfunction UpperCasePipe_tsickle_Closure_declarations() {\n/** @type {?} */\nUpperCasePipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nUpperCasePipe.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, EventEmitter, OnDestroy, Pipe, PipeTransform, WrappedValue, ɵisObservable, ɵisPromise} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {ISubscription} from 'rxjs/Subscription';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\ninterface SubscriptionStrategy {\n  createSubscription(async: Observable<any>|Promise<any>, updateLatestValue: any): ISubscription\n      |Promise<any>;\n  dispose(subscription: ISubscription|Promise<any>): void;\n  onDestroy(subscription: ISubscription|Promise<any>): void;\n}\nclass ObservableStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Observable<any>, updateLatestValue: any): ISubscription {\n    return async.subscribe({next: updateLatestValue, error: (e: any) => { throw e; }});\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: ISubscription): void { subscription.unsubscribe(); }\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: ISubscription): void { subscription.unsubscribe(); }\n}\nclass PromiseStrategy implements SubscriptionStrategy {\n/**\n * @param {?} async\n * @param {?} updateLatestValue\n * @return {?}\n */\ncreateSubscription(async: Promise<any>, updateLatestValue: (v: any) => any): Promise<any> {\n    return async.then(updateLatestValue, e => { throw e; });\n  }\n/**\n * @param {?} subscription\n * @return {?}\n */\ndispose(subscription: Promise<any>): void {}\n/**\n * @param {?} subscription\n * @return {?}\n */\nonDestroy(subscription: Promise<any>): void {}\n}\n\nconst /** @type {?} */ _promiseStrategy = new PromiseStrategy();\nconst /** @type {?} */ _observableStrategy = new ObservableStrategy();\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Unwraps a value from an asynchronous primitive.\n * \\@howToUse `observable_or_promise_expression | async`\n * \\@description \n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks.\n * \n * \n * ## Examples\n * \n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n * \n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n * \n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n * \n * \\@stable\n */\nexport class AsyncPipe implements OnDestroy, PipeTransform {\nprivate _latestValue: any = null;\nprivate _latestReturnedValue: any = null;\nprivate _subscription: ISubscription|Promise<any>|null = null;\nprivate _obj: Observable<any>|Promise<any>|EventEmitter<any>|null = null;\nprivate _strategy: SubscriptionStrategy = /** @type {?} */(( null));\n/**\n * @param {?} _ref\n */\nconstructor(private _ref: ChangeDetectorRef) {}\n/**\n * @return {?}\n */\nngOnDestroy(): void {\n    if (this._subscription) {\n      this._dispose();\n    }\n  }\n\n  transform<T>(obj: null): null;\n  transform<T>(obj: undefined): undefined;\n  transform<T>(obj: Observable<T>): T|null;\n  transform<T>(obj: Promise<T>): T|null;\n/**\n * @param {?} obj\n * @return {?}\n */\ntransform(obj: Observable<any>|Promise<any>|null|undefined): any {\n    if (!this._obj) {\n      if (obj) {\n        this._subscribe(obj);\n      }\n      this._latestReturnedValue = this._latestValue;\n      return this._latestValue;\n    }\n\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform( /** @type {?} */((obj as any)));\n    }\n\n    if (this._latestValue === this._latestReturnedValue) {\n      return this._latestReturnedValue;\n    }\n\n    this._latestReturnedValue = this._latestValue;\n    return WrappedValue.wrap(this._latestValue);\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _subscribe(obj: Observable<any>|Promise<any>|EventEmitter<any>): void {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(\n        obj, (value: Object) => this._updateLatestValue(obj, value));\n  }\n/**\n * @param {?} obj\n * @return {?}\n */\nprivate _selectStrategy(obj: Observable<any>|Promise<any>|EventEmitter<any>): any {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n\n    if (ɵisObservable(obj)) {\n      return _observableStrategy;\n    }\n\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n/**\n * @return {?}\n */\nprivate _dispose(): void {\n    this._strategy.dispose( /** @type {?} */((this._subscription)));\n    this._latestValue = null;\n    this._latestReturnedValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n/**\n * @param {?} async\n * @param {?} value\n * @return {?}\n */\nprivate _updateLatestValue(async: any, value: Object): void {\n    if (async === this._obj) {\n      this._latestValue = value;\n      this._ref.markForCheck();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Pipe, args: [{name: 'async', pure: false}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ChangeDetectorRef, },\n];\n}\n\nfunction AsyncPipe_tsickle_Closure_declarations() {\n/** @type {?} */\nAsyncPipe.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nAsyncPipe.ctorParameters;\n/** @type {?} */\nAsyncPipe.prototype._latestValue;\n/** @type {?} */\nAsyncPipe.prototype._latestReturnedValue;\n/** @type {?} */\nAsyncPipe.prototype._subscription;\n/** @type {?} */\nAsyncPipe.prototype._obj;\n/** @type {?} */\nAsyncPipe.prototype._strategy;\n/** @type {?} */\nAsyncPipe.prototype._ref;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Type, ɵstringify as stringify} from '@angular/core';\n/**\n * @param {?} type\n * @param {?} value\n * @return {?}\n */\nexport function invalidPipeArgumentError(type: Type<any>, value: Object) {\n  return Error(`InvalidPipeArgument: '${value}' for pipe '${stringify(type)}'`);\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Provider} from '@angular/core';\n\nimport {NgClass} from './ng_class';\nimport {NgComponentOutlet} from './ng_component_outlet';\nimport {Ng<PERSON>or, NgForOf, NgForOfContext} from './ng_for_of';\nimport {NgIf, NgIfContext} from './ng_if';\nimport {NgPlural, NgPluralCase} from './ng_plural';\nimport {NgStyle} from './ng_style';\nimport {Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault} from './ng_switch';\nimport {NgTemplateOutlet} from './ng_template_outlet';\n\nexport {\n  NgClass,\n  NgComponentOutlet,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  Ng<PERSON><PERSON><PERSON>,\n  Ng<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>Case,\n  <PERSON><PERSON><PERSON>Default,\n  NgTemplateOutlet\n};\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport const COMMON_DIRECTIVES: Provider[] = [\n  NgClass,\n  NgComponentOutlet,\n  NgForOf,\n  NgIf,\n  NgTemplateOutlet,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgPlural,\n  NgPluralCase,\n];\n/**\n * A collection of deprecated directives that are no longer part of the core module.\n */\nexport const COMMON_DEPRECATED_DIRECTIVES: Provider[] = [NgFor];\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, OnChanges, SimpleChanges, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Inserts an embedded view from a prepared `TemplateRef`\n * \n * \\@howToUse \n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n * \n * \\@description \n * \n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n * \n * Note: using the key `$implicit` in the context object will set it's value as default.\n * \n * ## Example\n * \n * {\\@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n * \n * \\@experimental\n */\nexport class NgTemplateOutlet implements OnChanges {\nprivate _viewRef: EmbeddedViewRef<any>;\npublic ngTemplateOutletContext: Object;\npublic ngTemplateOutlet: TemplateRef<any>;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @deprecated v4.0.0 - Renamed to ngTemplateOutletContext.\n * @param {?} context\n * @return {?}\n */\nset ngOutletContext(context: Object) { this.ngTemplateOutletContext = context; }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    if (this._viewRef) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._viewRef));\n    }\n\n    if (this.ngTemplateOutlet) {\n      this._viewRef = this._viewContainerRef.createEmbeddedView(\n          this.ngTemplateOutlet, this.ngTemplateOutletContext);\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngTemplateOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngTemplateOutletContext': [{ type: Input },],\n'ngTemplateOutlet': [{ type: Input },],\n'ngOutletContext': [{ type: Input },],\n};\n}\n\nfunction NgTemplateOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgTemplateOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgTemplateOutlet.ctorParameters;\n/** @type {?} */\nNgTemplateOutlet.propDecorators;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewRef;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutletContext;\n/** @type {?} */\nNgTemplateOutlet.prototype.ngTemplateOutlet;\n/** @type {?} */\nNgTemplateOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, KeyValueChanges, KeyV<PERSON>ueDiffer, Key<PERSON><PERSON>ue<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Update an HTML element styles.\n * \n * \\@howToUse \n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * \n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The styles are updated according to the value of the expression evaluation:\n * - keys are style names with an optional `.<unit>` suffix (ie 'top.px', 'font-style.em'),\n * - values are the values assigned to those properties (expressed in the given unit).\n * \n * \\@stable\n */\nexport class NgStyle implements DoCheck {\nprivate _ngStyle: {[key: string]: string};\nprivate _differ: KeyValueDiffer<string, string|number>;\n/**\n * @param {?} _differs\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _differs: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset ngStyle(v: {[key: string]: string}) {\n    this._ngStyle = v;\n    if (!this._differ && v) {\n      this._differ = this._differs.find(v).create();\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck() {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this._ngStyle);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: KeyValueChanges<string, string|number>): void {\n    changes.forEachRemovedItem((record) => this._setStyle(record.key, null));\n    changes.forEachAddedItem((record) => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._setStyle(record.key, record.currentValue));\n  }\n/**\n * @param {?} nameAndUnit\n * @param {?} value\n * @return {?}\n */\nprivate _setStyle(nameAndUnit: string, value: string|number|null|undefined): void {\n    const [name, unit] = nameAndUnit.split('.');\n    value = value != null && unit ? `${value}${unit}` : value;\n\n    this._renderer.setElementStyle(this._ngEl.nativeElement, name, /** @type {?} */(( value as string)));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngStyle]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngStyle': [{ type: Input },],\n};\n}\n\nfunction NgStyle_tsickle_Closure_declarations() {\n/** @type {?} */\nNgStyle.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgStyle.ctorParameters;\n/** @type {?} */\nNgStyle.propDecorators;\n/** @type {?} */\nNgStyle.prototype._ngStyle;\n/** @type {?} */\nNgStyle.prototype._differ;\n/** @type {?} */\nNgStyle.prototype._differs;\n/** @type {?} */\nNgStyle.prototype._ngEl;\n/** @type {?} */\nNgStyle.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n\nimport {NgLocalization, getPluralCategory} from '../localization';\n\nimport {SwitchView} from './ng_switch';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n * \n * \\@description \n * \n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n * \n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n * \n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n * \n * \\@experimental\n */\nexport class NgPlural {\nprivate _switchValue: number;\nprivate _activeView: SwitchView;\nprivate _caseViews: {[k: string]: SwitchView} = {};\n/**\n * @param {?} _localization\n */\nconstructor(private _localization: NgLocalization) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngPlural(value: number) {\n    this._switchValue = value;\n    this._updateView();\n  }\n/**\n * @param {?} value\n * @param {?} switchView\n * @return {?}\n */\naddCase(value: string, switchView: SwitchView): void { this._caseViews[value] = switchView; }\n/**\n * @return {?}\n */\nprivate _updateView(): void {\n    this._clearViews();\n\n    const /** @type {?} */ cases = Object.keys(this._caseViews);\n    const /** @type {?} */ key = getPluralCategory(this._switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n/**\n * @return {?}\n */\nprivate _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n/**\n * @param {?} view\n * @return {?}\n */\nprivate _activateView(view: SwitchView) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPlural]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: NgLocalization, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngPlural': [{ type: Input },],\n};\n}\n\nfunction NgPlural_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPlural.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPlural.ctorParameters;\n/** @type {?} */\nNgPlural.propDecorators;\n/** @type {?} */\nNgPlural.prototype._switchValue;\n/** @type {?} */\nNgPlural.prototype._activeView;\n/** @type {?} */\nNgPlural.prototype._caseViews;\n/** @type {?} */\nNgPlural.prototype._localization;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgPlural} when the\n *             given expression matches the plural expression according to CLDR rules.\n * \n * \\@howToUse \n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n * ```\n * \n * See {\\@link NgPlural} for more details and example.\n * \n * \\@experimental\n */\nexport class NgPluralCase {\n/**\n * @param {?} value\n * @param {?} template\n * @param {?} viewContainer\n * @param {?} ngPlural\n */\nconstructor(\npublic value: string, template: TemplateRef<Object>,\n      viewContainer: ViewContainerRef,  ngPlural: NgPlural) {\n    const isANumber: boolean = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngPluralCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Attribute, args: ['ngPluralCase', ] }, ]},\n{type: TemplateRef, },\n{type: ViewContainerRef, },\n{type: NgPlural, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgPluralCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgPluralCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgPluralCase.ctorParameters;\n/** @type {?} */\nNgPluralCase.prototype.value;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\nexport class SwitchView {\nprivate _created = false;\n/**\n * @param {?} _viewContainerRef\n * @param {?} _templateRef\n */\nconstructor(\nprivate _viewContainerRef: ViewContainerRef,\nprivate _templateRef: TemplateRef<Object>) {}\n/**\n * @return {?}\n */\ncreate(): void {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n/**\n * @return {?}\n */\ndestroy(): void {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n/**\n * @param {?} created\n * @return {?}\n */\nenforceState(created: boolean) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n\nfunction SwitchView_tsickle_Closure_declarations() {\n/** @type {?} */\nSwitchView.prototype._created;\n/** @type {?} */\nSwitchView.prototype._viewContainerRef;\n/** @type {?} */\nSwitchView.prototype._templateRef;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds / removes DOM sub-trees when the nest match expressions matches the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n *     <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n * \\@description \n * \n * `NgSwitch` stamps out nested views when their match expression value matches the value of the\n * switch expression.\n * \n * In other words:\n * - you define a container element (where you place the directive with a switch expression on the\n * `[ngSwitch]=\"...\"` attribute)\n * - you define inner views inside the `NgSwitch` and place a `*ngSwitchCase` attribute on the view\n * root elements.\n * \n * Elements within `NgSwitch` but outside of a `NgSwitchCase` or `NgSwitchDefault` directives will\n * be preserved at the location.\n * \n * The `ngSwitchCase` directive informs the parent `NgSwitch` of which view to display when the\n * expression is evaluated.\n * When no matching expression is found on a `ngSwitchCase` view, the `ngSwitchDefault` view is\n * stamped out.\n * \n * \\@stable\n */\nexport class NgSwitch {\nprivate _defaultViews: SwitchView[];\nprivate _defaultUsed = false;\nprivate _caseCount = 0;\nprivate _lastCaseCheckIndex = 0;\nprivate _lastCasesMatched = false;\nprivate _ngSwitch: any;\n/**\n * @param {?} newValue\n * @return {?}\n */\nset ngSwitch(newValue: any) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n/**\n * \\@internal\n * @return {?}\n */\n_addCase(): number { return this._caseCount++; }\n/**\n * \\@internal\n * @param {?} view\n * @return {?}\n */\n_addDefault(view: SwitchView) {\n    if (!this._defaultViews) {\n      this._defaultViews = [];\n    }\n    this._defaultViews.push(view);\n  }\n/**\n * \\@internal\n * @param {?} value\n * @return {?}\n */\n_matchCase(value: any): boolean {\n    const /** @type {?} */ matched = value == this._ngSwitch;\n    this._lastCasesMatched = this._lastCasesMatched || matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n/**\n * @param {?} useDefault\n * @return {?}\n */\nprivate _updateDefaultCases(useDefault: boolean) {\n    if (this._defaultViews && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (let /** @type {?} */ i = 0; i < this._defaultViews.length; i++) {\n        const /** @type {?} */ defaultView = this._defaultViews[i];\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitch]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitch': [{ type: Input },],\n};\n}\n\nfunction NgSwitch_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitch.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitch.ctorParameters;\n/** @type {?} */\nNgSwitch.propDecorators;\n/** @type {?} */\nNgSwitch.prototype._defaultViews;\n/** @type {?} */\nNgSwitch.prototype._defaultUsed;\n/** @type {?} */\nNgSwitch.prototype._caseCount;\n/** @type {?} */\nNgSwitch.prototype._lastCaseCheckIndex;\n/** @type {?} */\nNgSwitch.prototype._lastCasesMatched;\n/** @type {?} */\nNgSwitch.prototype._ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgSwitch} when the\n *             given expression evaluate to respectively the same/different value as the switch\n *             expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * </container-element>\n * ```\n * \\@description \n * \n * Insert the sub-tree when the expression evaluates to the same value as the enclosing switch\n * expression.\n * \n * If multiple match expressions match the switch expression value, all of them are displayed.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchCase implements DoCheck {\nprivate _view: SwitchView;\n\n  \n  ngSwitchCase: any;\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\nprivate ngSwitch: NgSwitch) {\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n/**\n * @return {?}\n */\nngDoCheck() { this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase)); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchCase]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngSwitchCase': [{ type: Input },],\n};\n}\n\nfunction NgSwitchCase_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchCase.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchCase.ctorParameters;\n/** @type {?} */\nNgSwitchCase.propDecorators;\n/** @type {?} */\nNgSwitchCase.prototype._view;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitchCase;\n/** @type {?} */\nNgSwitchCase.prototype.ngSwitch;\n}\n\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a view that is added to the parent {\\@link NgSwitch} when no case expressions\n * match the\n *             switch expression.\n * \n * \\@howToUse \n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-other-element *ngSwitchDefault>...</some-other-element>\n * </container-element>\n * ```\n * \n * \\@description \n * \n * Insert the sub-tree when no case expressions evaluate to the same value as the enclosing switch\n * expression.\n * \n * See {\\@link NgSwitch} for more details and example.\n * \n * \\@stable\n */\nexport class NgSwitchDefault {\n/**\n * @param {?} viewContainer\n * @param {?} templateRef\n * @param {?} ngSwitch\n */\nconstructor(\n      viewContainer: ViewContainerRef, templateRef: TemplateRef<Object>,\n       ngSwitch: NgSwitch) {\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngSwitchDefault]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: NgSwitch, decorators: [{ type: Host }, ]},\n];\n}\n\nfunction NgSwitchDefault_tsickle_Closure_declarations() {\n/** @type {?} */\nNgSwitchDefault.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgSwitchDefault.ctorParameters;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, EmbeddedViewRef, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n/**\n * Conditionally includes a template based on the value of an `expression`.\n * \n * `ngIf` evaluates the `expression` and then renders the `then` or `else` template in its place\n * when expression is truthy or falsy respectively. Typically the:\n *  - `then` template is the inline template of `ngIf` unless bound to a different value.\n *  - `else` template is blank unless it is bound.\n * \n * ## Most common usage\n * \n * The most common usage of the `ngIf` directive is to conditionally show the inline template as\n * seen in this example:\n * {\\@example common/ngIf/ts/module.ts region='NgIfSimple'}\n * \n * ## Showing an alternative template using `else`\n * \n * If it is necessary to display a template when the `expression` is falsy use the `else` template\n * binding as shown. Note that the `else` binding points to a `<ng-template>` labeled `#elseBlock`.\n * The template can be defined anywhere in the component view but is typically placed right after\n * `ngIf` for readability.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfElse'}\n * \n * ## Using non-inlined `then` template\n * \n * Usually the `then` template is the inlined template of the `ngIf`, but it can be changed using\n * a binding (just like `else`). Because `then` and `else` are bindings, the template references can\n * change at runtime as shown in this example.\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n * \n * ## Storing conditional result in a variable\n * \n * A common pattern is that we need to show a set of properties from the same object. If the\n * object is undefined, then we have to use the safe-traversal-operator `?.` to guard against\n * dereferencing a `null` value. This is especially the case when waiting on async data such as\n * when using the `async` pipe as shown in folowing example:\n * \n * ```\n * Hello {{ (userStream|async)?.last }}, {{ (userStream|async)?.first }}!\n * ```\n * \n * There are several inefficiencies in the above example:\n *  - We create multiple subscriptions on `userStream`. One for each `async` pipe, or two in the\n *    example above.\n *  - We cannot display an alternative screen while waiting for the data to arrive asynchronously.\n *  - We have to use the safe-traversal-operator `?.` to access properties, which is cumbersome.\n *  - We have to place the `async` pipe in parenthesis.\n * \n * A better way to do this is to use `ngIf` and store the result of the condition in a local\n * variable as shown in the the example below:\n * \n * {\\@example common/ngIf/ts/module.ts region='NgIfAs'}\n * \n * Notice that:\n *  - We use only one `async` pipe and hence only one subscription gets created.\n *  - `ngIf` stores the result of the `userStream|async` in the local variable `user`.\n *  - The local `user` can then be bound repeatedly in a more efficient way.\n *  - No need to use the safe-traversal-operator `?.` to access properties as `ngIf` will only\n *    display the data if `userStream` returns a value.\n *  - We can display an alternative template while waiting for the data.\n * \n * ### Syntax\n * \n * Simple form:\n * - `<div *ngIf=\"condition\">...</div>`\n * - `<div template=\"ngIf condition\">...</div>`\n * - `<ng-template [ngIf]=\"condition\"><div>...</div></ng-template>`\n * \n * Form with an else block:\n * ```\n * <div *ngIf=\"condition; else elseBlock\">...</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with a `then` and `else` block:\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>...</ng-template>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * Form with storing the value locally:\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n * \n * \\@stable\n */\nexport class NgIf {\nprivate _context: NgIfContext = new NgIfContext();\nprivate _thenTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _elseTemplateRef: TemplateRef<NgIfContext>|null = null;\nprivate _thenViewRef: EmbeddedViewRef<NgIfContext>|null = null;\nprivate _elseViewRef: EmbeddedViewRef<NgIfContext>|null = null;\n/**\n * @param {?} _viewContainer\n * @param {?} templateRef\n */\nconstructor(private _viewContainer: ViewContainerRef, templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n  }\n/**\n * @param {?} condition\n * @return {?}\n */\nset ngIf(condition: any) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfThen(templateRef: TemplateRef<NgIfContext>) {\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @param {?} templateRef\n * @return {?}\n */\nset ngIfElse(templateRef: TemplateRef<NgIfContext>) {\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null;  // clear previous view if any.\n    this._updateView();\n  }\n/**\n * @return {?}\n */\nprivate _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef =\n              this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef =\n              this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n        }\n      }\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngIf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngIf': [{ type: Input },],\n'ngIfThen': [{ type: Input },],\n'ngIfElse': [{ type: Input },],\n};\n}\n\nfunction NgIf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgIf.ctorParameters;\n/** @type {?} */\nNgIf.propDecorators;\n/** @type {?} */\nNgIf.prototype._context;\n/** @type {?} */\nNgIf.prototype._thenTemplateRef;\n/** @type {?} */\nNgIf.prototype._elseTemplateRef;\n/** @type {?} */\nNgIf.prototype._thenViewRef;\n/** @type {?} */\nNgIf.prototype._elseViewRef;\n/** @type {?} */\nNgIf.prototype._viewContainer;\n}\n\n/**\n * \\@stable\n */\nexport class NgIfContext {\npublic $implicit: any = null;\npublic ngIf: any = null;\n}\n\nfunction NgIfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgIfContext.prototype.$implicit;\n/** @type {?} */\nNgIfContext.prototype.ngIf;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ChangeDetectorRef, Directive, DoCheck, EmbeddedViewRef, Input, IterableChangeRecord, IterableChanges, IterableDiffer, IterableDiffers, NgIterable, OnChanges, SimpleChanges, TemplateRef, TrackByFunction, ViewContainerRef, forwardRef, isDevMode} from '@angular/core';\n/**\n * \\@stable\n */\nexport class NgForOfContext<T> {\n/**\n * @param {?} $implicit\n * @param {?} ngForOf\n * @param {?} index\n * @param {?} count\n */\nconstructor(\npublic $implicit: T,\npublic ngForOf: NgIterable<T>,\npublic index: number,\npublic count: number) {}\n/**\n * @return {?}\n */\nget first(): boolean { return this.index === 0; }\n/**\n * @return {?}\n */\nget last(): boolean { return this.index === this.count - 1; }\n/**\n * @return {?}\n */\nget even(): boolean { return this.index % 2 === 0; }\n/**\n * @return {?}\n */\nget odd(): boolean { return !this.even; }\n}\n\nfunction NgForOfContext_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOfContext.prototype.$implicit;\n/** @type {?} */\nNgForOfContext.prototype.ngForOf;\n/** @type {?} */\nNgForOfContext.prototype.index;\n/** @type {?} */\nNgForOfContext.prototype.count;\n}\n\n/**\n * The `NgForOf` directive instantiates a template once per item from an iterable. The context\n * for each instantiated template inherits from the outer context with the given loop variable\n * set to the current item from the iterable.\n * \n * ### Local Variables\n * \n * `NgForOf` provides several exported values that can be aliased to local variables:\n * \n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n * \n * ```\n * <li *ngFor=\"let user of userObservable | async as users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n * \n * ### Change Propagation\n * \n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n * \n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n * * Otherwise, the DOM element for that item will remain the same.\n * \n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls (such as `<input>` elements which accept user input) that are present. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * \n * It is possible for the identities of elements in the iterator to change while the data does not.\n * This can happen, for example, if the iterator produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response will produce objects with\n * different identities, and Angular will tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted). This is an expensive operation and should\n * be avoided if possible.\n * \n * To customize the default tracking algorithm, `NgForOf` supports `trackBy` option.\n * `trackBy` takes a function which has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n * \n * ### Syntax\n * \n * - `<li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>`\n * - `<li template=\"ngFor let item of items; index as i; trackBy: trackByFn\">...</li>`\n * \n * With `<ng-template>` element:\n * \n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n * \n * ### Example\n * \n * See a [live demo](http://plnkr.co/edit/KVuXxDp0qinGDyo307QW?p=preview) for a more detailed\n * example.\n * \n * \\@stable\n */\nexport class NgForOf<T> implements DoCheck, OnChanges {\n   ngForOf: NgIterable<T>;\n/**\n * @param {?} fn\n * @return {?}\n */\nset ngForTrackBy(fn: TrackByFunction<T>) {\n    if (isDevMode() && fn != null && typeof fn !== 'function') {\n      // TODO(vicb): use a log service once there is a public one available\n      if ( /** @type {?} */((<any>console)) && /** @type {?} */(( <any>console.warn))) {\n        console.warn(\n            `trackBy must be a function, but received ${JSON.stringify(fn)}. ` +\n            `See https://angular.io/docs/ts/latest/api/common/index/NgFor-directive.html#!#change-propagation for more information.`);\n      }\n    }\n    this._trackByFn = fn;\n  }\n/**\n * @return {?}\n */\nget ngForTrackBy(): TrackByFunction<T> { return this._trackByFn; }\nprivate _differ: IterableDiffer<T>|null = null;\nprivate _trackByFn: TrackByFunction<T>;\n/**\n * @param {?} _viewContainer\n * @param {?} _template\n * @param {?} _differs\n */\nconstructor(\nprivate _viewContainer: ViewContainerRef,\nprivate _template: TemplateRef<NgForOfContext<T>>,\nprivate _differs: IterableDiffers) {}\n/**\n * @param {?} value\n * @return {?}\n */\nset ngForTemplate(value: TemplateRef<NgForOfContext<T>>) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges): void {\n    if ('ngForOf' in changes) {\n      // React on ngForOf changes only once all inputs have been initialized\n      const /** @type {?} */ value = changes['ngForOf'].currentValue;\n      if (!this._differ && value) {\n        try {\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        } catch ( /** @type {?} */e) {\n          throw new Error(\n              `Cannot find a differ supporting object '${value}' of type '${getTypeNameForDebugging(value)}'. NgFor only supports binding to Iterables such as Arrays.`);\n        }\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._differ) {\n      const /** @type {?} */ changes = this._differ.diff(this.ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyChanges(changes: IterableChanges<T>) {\n    const /** @type {?} */ insertTuples: RecordViewTuple<T>[] = [];\n    changes.forEachOperation(\n        (item: IterableChangeRecord<any>, adjustedPreviousIndex: number, currentIndex: number) => {\n          if (item.previousIndex == null) {\n            const /** @type {?} */ view = this._viewContainer.createEmbeddedView(\n                this._template, new NgForOfContext<T>( /** @type {?} */((null)), this.ngForOf, -1, -1), currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple<T>(item, view);\n            insertTuples.push(tuple);\n          } else if (currentIndex == null) {\n            this._viewContainer.remove(adjustedPreviousIndex);\n          } else {\n            const /** @type {?} */ view = /** @type {?} */(( this._viewContainer.get(adjustedPreviousIndex)));\n            this._viewContainer.move(view, currentIndex);\n            const /** @type {?} */ tuple = new RecordViewTuple(item, /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>view)));\n            insertTuples.push(tuple);\n          }\n        });\n\n    for (let /** @type {?} */ i = 0; i < insertTuples.length; i++) {\n      this._perViewChange(insertTuples[i].view, insertTuples[i].record);\n    }\n\n    for (let /** @type {?} */ i = 0, /** @type {?} */ ilen = this._viewContainer.length; i < ilen; i++) {\n      const /** @type {?} */ viewRef = /** @type {?} */(( <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(i)));\n      viewRef.context.index = i;\n      viewRef.context.count = ilen;\n    }\n\n    changes.forEachIdentityChange((record: any) => {\n      const /** @type {?} */ viewRef = /** @type {?} */((\n          <EmbeddedViewRef<NgForOfContext<T>>>this._viewContainer.get(record.currentIndex)));\n      viewRef.context.$implicit = record.item;\n    });\n  }\n/**\n * @param {?} view\n * @param {?} record\n * @return {?}\n */\nprivate _perViewChange(\n      view: EmbeddedViewRef<NgForOfContext<T>>, record: IterableChangeRecord<any>) {\n    view.context.$implicit = record.item;\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngFor][ngForOf]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n{type: TemplateRef, },\n{type: IterableDiffers, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngForOf': [{ type: Input },],\n'ngForTrackBy': [{ type: Input },],\n'ngForTemplate': [{ type: Input },],\n};\n}\n\nfunction NgForOf_tsickle_Closure_declarations() {\n/** @type {?} */\nNgForOf.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgForOf.ctorParameters;\n/** @type {?} */\nNgForOf.propDecorators;\n/** @type {?} */\nNgForOf.prototype.ngForOf;\n/** @type {?} */\nNgForOf.prototype._differ;\n/** @type {?} */\nNgForOf.prototype._trackByFn;\n/** @type {?} */\nNgForOf.prototype._viewContainer;\n/** @type {?} */\nNgForOf.prototype._template;\n/** @type {?} */\nNgForOf.prototype._differs;\n}\n\nclass RecordViewTuple<T> {\n/**\n * @param {?} record\n * @param {?} view\n */\nconstructor(public record: any,\npublic view: EmbeddedViewRef<NgForOfContext<T>>) {}\n}\n\nfunction RecordViewTuple_tsickle_Closure_declarations() {\n/** @type {?} */\nRecordViewTuple.prototype.record;\n/** @type {?} */\nRecordViewTuple.prototype.view;\n}\n\n\n/**\n * @deprecated from v4.0.0 - Use NgForOf<any> instead.\n */\nexport type NgFor = NgForOf<any>;\n/**\n * @deprecated from v4.0.0 - Use NgForOf instead.\n */\nexport const NgFor = NgForOf;\n/**\n * @param {?} type\n * @return {?}\n */\nexport function getTypeNameForDebugging(type: any): string {\n  return type['name'] || typeof type;\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {ComponentFactoryResolver, ComponentRef, Directive, Injector, Input, NgModuleFactory, NgModuleRef, OnChanges, OnDestroy, Provider, SimpleChanges, Type, ViewContainerRef} from '@angular/core';\n/**\n * Instantiates a single {\\@link Component} type and inserts its Host View into current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n * \n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will get destroyed.\n * \n * ### Fine tune control\n * \n * You can control the component creation process by using the following optional attributes:\n * \n * * `ngComponentOutletInjector`: Optional custom {\\@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n * \n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if exists.\n * \n * * `ngComponentOutletNgModuleFactory`: Optional module factory to allow dynamically loading other\n * module, then load a component from that module.\n * \n * ### Syntax\n * \n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n * \n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n * \n * Customized ngModuleFactory\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModuleFactory: moduleFactory;\">\n * </ng-container>\n * ```\n * ## Example\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n * \n * A more complete example with additional options:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n * A more complete example with ngModuleFactory:\n * \n * {\\@example common/ngComponentOutlet/ts/module.ts region='NgModuleFactoryExample'}\n * \n * \\@experimental\n */\nexport class NgComponentOutlet implements OnChanges, OnDestroy {\n   ngComponentOutlet: Type<any>;\n   ngComponentOutletInjector: Injector;\n   ngComponentOutletContent: any[][];\n   ngComponentOutletNgModuleFactory: NgModuleFactory<any>;\nprivate _componentRef: ComponentRef<any>|null = null;\nprivate _moduleRef: NgModuleRef<any>|null = null;\n/**\n * @param {?} _viewContainerRef\n */\nconstructor(private _viewContainerRef: ViewContainerRef) {}\n/**\n * @param {?} changes\n * @return {?}\n */\nngOnChanges(changes: SimpleChanges) {\n    this._viewContainerRef.clear();\n    this._componentRef = null;\n\n    if (this.ngComponentOutlet) {\n      const /** @type {?} */ elInjector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n      if (changes['ngComponentOutletNgModuleFactory']) {\n        if (this._moduleRef) this._moduleRef.destroy();\n\n        if (this.ngComponentOutletNgModuleFactory) {\n          const /** @type {?} */ parentModule = elInjector.get(NgModuleRef);\n          this._moduleRef = this.ngComponentOutletNgModuleFactory.create(parentModule.injector);\n        } else {\n          this._moduleRef = null;\n        }\n      }\n\n      const /** @type {?} */ componentFactoryResolver = this._moduleRef ? this._moduleRef.componentFactoryResolver :\n                                                         elInjector.get(ComponentFactoryResolver);\n\n      const /** @type {?} */ componentFactory =\n          componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);\n\n      this._componentRef = this._viewContainerRef.createComponent(\n          componentFactory, this._viewContainerRef.length, elInjector,\n          this.ngComponentOutletContent);\n    }\n  }\n/**\n * @return {?}\n */\nngOnDestroy() {\n    if (this._moduleRef) this._moduleRef.destroy();\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngComponentOutlet]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: ViewContainerRef, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'ngComponentOutlet': [{ type: Input },],\n'ngComponentOutletInjector': [{ type: Input },],\n'ngComponentOutletContent': [{ type: Input },],\n'ngComponentOutletNgModuleFactory': [{ type: Input },],\n};\n}\n\nfunction NgComponentOutlet_tsickle_Closure_declarations() {\n/** @type {?} */\nNgComponentOutlet.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgComponentOutlet.ctorParameters;\n/** @type {?} */\nNgComponentOutlet.propDecorators;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutlet;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletInjector;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletContent;\n/** @type {?} */\nNgComponentOutlet.prototype.ngComponentOutletNgModuleFactory;\n/** @type {?} */\nNgComponentOutlet.prototype._componentRef;\n/** @type {?} */\nNgComponentOutlet.prototype._moduleRef;\n/** @type {?} */\nNgComponentOutlet.prototype._viewContainerRef;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Directive, DoCheck, ElementRef, Input, IterableChanges, IterableDiffer, IterableDiffers, KeyValueChanges, KeyValueDiffer, KeyValueDiffers, <PERSON><PERSON><PERSON>, ɵisListLikeIterable as isListLikeIterable, ɵstringify as stringify} from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \n * \\@whatItDoes Adds and removes CSS classes on an HTML element.\n * \n * \\@howToUse \n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n * \n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n * \n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n * \n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n * \n * \\@description \n * \n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n * \n * \\@stable\n */\nexport class NgClass implements DoCheck {\nprivate _iterableDiffer: IterableDiffer<string>|null;\nprivate _keyValueDiffer: KeyValueDiffer<string, any>|null;\nprivate _initialClasses: string[] = [];\nprivate _rawClass: string[]|Set<string>|{[klass: string]: any};\n/**\n * @param {?} _iterableDiffers\n * @param {?} _keyValueDiffers\n * @param {?} _ngEl\n * @param {?} _renderer\n */\nconstructor(\nprivate _iterableDiffers: IterableDiffers,\nprivate _keyValueDiffers: KeyValueDiffers,\nprivate _ngEl: ElementRef,\nprivate _renderer: Renderer) {}\n/**\n * @param {?} v\n * @return {?}\n */\nset klass(v: string) {\n    this._applyInitialClasses(true);\n    this._initialClasses = typeof v === 'string' ? v.split(/\\s+/) : [];\n    this._applyInitialClasses(false);\n    this._applyClasses(this._rawClass, false);\n  }\n/**\n * @param {?} v\n * @return {?}\n */\nset ngClass(v: string|string[]|Set<string>|{[klass: string]: any}) {\n    this._cleanupClasses(this._rawClass);\n\n    this._iterableDiffer = null;\n    this._keyValueDiffer = null;\n\n    this._rawClass = typeof v === 'string' ? v.split(/\\s+/) : v;\n\n    if (this._rawClass) {\n      if (isListLikeIterable(this._rawClass)) {\n        this._iterableDiffer = this._iterableDiffers.find(this._rawClass).create();\n      } else {\n        this._keyValueDiffer = this._keyValueDiffers.find(this._rawClass).create();\n      }\n    }\n  }\n/**\n * @return {?}\n */\nngDoCheck(): void {\n    if (this._iterableDiffer) {\n      const /** @type {?} */ iterableChanges = this._iterableDiffer.diff( /** @type {?} */((this._rawClass as string[])));\n      if (iterableChanges) {\n        this._applyIterableChanges(iterableChanges);\n      }\n    } else if (this._keyValueDiffer) {\n      const /** @type {?} */ keyValueChanges = this._keyValueDiffer.diff( /** @type {?} */((this._rawClass as{[k: string]: any})));\n      if (keyValueChanges) {\n        this._applyKeyValueChanges(keyValueChanges);\n      }\n    }\n  }\n/**\n * @param {?} rawClassVal\n * @return {?}\n */\nprivate _cleanupClasses(rawClassVal: string[]|{[klass: string]: any}): void {\n    this._applyClasses(rawClassVal, true);\n    this._applyInitialClasses(false);\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyKeyValueChanges(changes: KeyValueChanges<string, any>): void {\n    changes.forEachAddedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._toggleClass(record.key, record.currentValue));\n    changes.forEachRemovedItem((record) => {\n      if (record.previousValue) {\n        this._toggleClass(record.key, false);\n      }\n    });\n  }\n/**\n * @param {?} changes\n * @return {?}\n */\nprivate _applyIterableChanges(changes: IterableChanges<string>): void {\n    changes.forEachAddedItem((record) => {\n      if (typeof record.item === 'string') {\n        this._toggleClass(record.item, true);\n      } else {\n        throw new Error(\n            `NgClass can only toggle CSS classes expressed as strings, got ${stringify(record.item)}`);\n      }\n    });\n\n    changes.forEachRemovedItem((record) => this._toggleClass(record.item, false));\n  }\n/**\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyInitialClasses(isCleanup: boolean) {\n    this._initialClasses.forEach(klass => this._toggleClass(klass, !isCleanup));\n  }\n/**\n * @param {?} rawClassVal\n * @param {?} isCleanup\n * @return {?}\n */\nprivate _applyClasses(\n      rawClassVal: string[]|Set<string>|{[klass: string]: any}, isCleanup: boolean) {\n    if (rawClassVal) {\n      if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n        ( /** @type {?} */((<any>rawClassVal))).forEach((klass: string) => this._toggleClass(klass, !isCleanup));\n      } else {\n        Object.keys(rawClassVal).forEach(klass => {\n          if (rawClassVal[klass] != null) this._toggleClass(klass, !isCleanup);\n        });\n      }\n    }\n  }\n/**\n * @param {?} klass\n * @param {?} enabled\n * @return {?}\n */\nprivate _toggleClass(klass: string, enabled: any): void {\n    klass = klass.trim();\n    if (klass) {\n      klass.split(/\\s+/g).forEach(\n          klass => { this._renderer.setElementClass(this._ngEl.nativeElement, klass, !!enabled); });\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Directive, args: [{selector: '[ngClass]'}, ] },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: IterableDiffers, },\n{type: KeyValueDiffers, },\n{type: ElementRef, },\n{type: Renderer, },\n];\nstatic propDecorators: {[key: string]: DecoratorInvocation[]} = {\n'klass': [{ type: Input, args: ['class', ] },],\n'ngClass': [{ type: Input },],\n};\n}\n\nfunction NgClass_tsickle_Closure_declarations() {\n/** @type {?} */\nNgClass.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgClass.ctorParameters;\n/** @type {?} */\nNgClass.propDecorators;\n/** @type {?} */\nNgClass.prototype._iterableDiffer;\n/** @type {?} */\nNgClass.prototype._keyValueDiffer;\n/** @type {?} */\nNgClass.prototype._initialClasses;\n/** @type {?} */\nNgClass.prototype._rawClass;\n/** @type {?} */\nNgClass.prototype._iterableDiffers;\n/** @type {?} */\nNgClass.prototype._keyValueDiffers;\n/** @type {?} */\nNgClass.prototype._ngEl;\n/** @type {?} */\nNgClass.prototype._renderer;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, LOCALE_ID} from '@angular/core';\n/**\n * \\@experimental\n * @abstract\n */\nexport abstract class NgLocalization {\n/**\n * @abstract\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any) {} }\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n * \n * \\@internal\n * @param {?} value\n * @param {?} cases\n * @param {?} ngLocalization\n * @return {?}\n */\nexport function getPluralCategory(\n    value: number, cases: string[], ngLocalization: NgLocalization): string {\n  let /** @type {?} */ key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n */\nexport class NgLocaleLocalization extends NgLocalization {\n/**\n * @param {?} locale\n */\nconstructor( protected locale: string) { super(); }\n/**\n * @param {?} value\n * @return {?}\n */\ngetPluralCategory(value: any): string {\n    const /** @type {?} */ plural = getPluralCase(this.locale, value);\n\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID, ] }, ]},\n];\n}\n\nfunction NgLocaleLocalization_tsickle_Closure_declarations() {\n/** @type {?} */\nNgLocaleLocalization.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nNgLocaleLocalization.ctorParameters;\n/** @type {?} */\nNgLocaleLocalization.prototype.locale;\n}\n\nexport type Plural = number;\nexport let Plural: any = {};\nPlural.Zero = 0;\nPlural.One = 1;\nPlural.Two = 2;\nPlural.Few = 3;\nPlural.Many = 4;\nPlural.Other = 5;\nPlural[Plural.Zero] = \"Zero\";\nPlural[Plural.One] = \"One\";\nPlural[Plural.Two] = \"Two\";\nPlural[Plural.Few] = \"Few\";\nPlural[Plural.Many] = \"Many\";\nPlural[Plural.Other] = \"Other\";\n\n/**\n * Returns the plural case based on the locale\n * \n * \\@experimental\n * @param {?} locale\n * @param {?} nLike\n * @return {?}\n */\nexport function getPluralCase(locale: string, nLike: number | string): Plural {\n  // TODO(vicb): lazy compute\n  if (typeof nLike === 'string') {\n    nLike = parseInt( /** @type {?} */((<string>nLike)), 10);\n  }\n  const /** @type {?} */ n: number = /** @type {?} */(( nLike as number));\n  const /** @type {?} */ nDecimal = n.toString().replace(/^[^.]*\\.?/, '');\n  const /** @type {?} */ i = Math.floor(Math.abs(n));\n  const /** @type {?} */ v = nDecimal.length;\n  const /** @type {?} */ f = parseInt(nDecimal, 10);\n  const /** @type {?} */ t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n\n  const /** @type {?} */ lang = locale.split('-')[0].toLowerCase();\n\n  switch (lang) {\n    case 'af':\n    case 'asa':\n    case 'az':\n    case 'bem':\n    case 'bez':\n    case 'bg':\n    case 'brx':\n    case 'ce':\n    case 'cgg':\n    case 'chr':\n    case 'ckb':\n    case 'ee':\n    case 'el':\n    case 'eo':\n    case 'es':\n    case 'eu':\n    case 'fo':\n    case 'fur':\n    case 'gsw':\n    case 'ha':\n    case 'haw':\n    case 'hu':\n    case 'jgo':\n    case 'jmc':\n    case 'ka':\n    case 'kk':\n    case 'kkj':\n    case 'kl':\n    case 'ks':\n    case 'ksb':\n    case 'ky':\n    case 'lb':\n    case 'lg':\n    case 'mas':\n    case 'mgo':\n    case 'ml':\n    case 'mn':\n    case 'nb':\n    case 'nd':\n    case 'ne':\n    case 'nn':\n    case 'nnh':\n    case 'nyn':\n    case 'om':\n    case 'or':\n    case 'os':\n    case 'ps':\n    case 'rm':\n    case 'rof':\n    case 'rwk':\n    case 'saq':\n    case 'seh':\n    case 'sn':\n    case 'so':\n    case 'sq':\n    case 'ta':\n    case 'te':\n    case 'teo':\n    case 'tk':\n    case 'tr':\n    case 'ug':\n    case 'uz':\n    case 'vo':\n    case 'vun':\n    case 'wae':\n    case 'xog':\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ak':\n    case 'ln':\n    case 'mg':\n    case 'pa':\n    case 'ti':\n      if (n === Math.floor(n) && n >= 0 && n <= 1) return Plural.One;\n      return Plural.Other;\n    case 'am':\n    case 'as':\n    case 'bn':\n    case 'fa':\n    case 'gu':\n    case 'hi':\n    case 'kn':\n    case 'mr':\n    case 'zu':\n      if (i === 0 || n === 1) return Plural.One;\n      return Plural.Other;\n    case 'ar':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return Plural.Many;\n      return Plural.Other;\n    case 'ast':\n    case 'ca':\n    case 'de':\n    case 'en':\n    case 'et':\n    case 'fi':\n    case 'fy':\n    case 'gl':\n    case 'it':\n    case 'nl':\n    case 'sv':\n    case 'sw':\n    case 'ur':\n    case 'yi':\n      if (i === 1 && v === 0) return Plural.One;\n      return Plural.Other;\n    case 'be':\n      if (n % 10 === 1 && !(n % 100 === 11)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 4 &&\n          !(n % 100 >= 12 && n % 100 <= 14))\n        return Plural.Few;\n      if (n % 10 === 0 || n % 10 === Math.floor(n % 10) && n % 10 >= 5 && n % 10 <= 9 ||\n          n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'br':\n      if (n % 10 === 1 && !(n % 100 === 11 || n % 100 === 71 || n % 100 === 91)) return Plural.One;\n      if (n % 10 === 2 && !(n % 100 === 12 || n % 100 === 72 || n % 100 === 92)) return Plural.Two;\n      if (n % 10 === Math.floor(n % 10) && (n % 10 >= 3 && n % 10 <= 4 || n % 10 === 9) &&\n          !(n % 100 >= 10 && n % 100 <= 19 || n % 100 >= 70 && n % 100 <= 79 ||\n            n % 100 >= 90 && n % 100 <= 99))\n        return Plural.Few;\n      if (!(n === 0) && n % 1e6 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'bs':\n    case 'hr':\n    case 'sr':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11))\n        return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n              !(i % 100 >= 12 && i % 100 <= 14) ||\n          f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 &&\n              !(f % 100 >= 12 && f % 100 <= 14))\n        return Plural.Few;\n      return Plural.Other;\n    case 'cs':\n    case 'sk':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0) return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'cy':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === 3) return Plural.Few;\n      if (n === 6) return Plural.Many;\n      return Plural.Other;\n    case 'da':\n      if (n === 1 || !(t === 0) && (i === 0 || i === 1)) return Plural.One;\n      return Plural.Other;\n    case 'dsb':\n    case 'hsb':\n      if (v === 0 && i % 100 === 1 || f % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2 || f % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 ||\n          f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ff':\n    case 'fr':\n    case 'hy':\n    case 'kab':\n      if (i === 0 || i === 1) return Plural.One;\n      return Plural.Other;\n    case 'fil':\n      if (v === 0 && (i === 1 || i === 2 || i === 3) ||\n          v === 0 && !(i % 10 === 4 || i % 10 === 6 || i % 10 === 9) ||\n          !(v === 0) && !(f % 10 === 4 || f % 10 === 6 || f % 10 === 9))\n        return Plural.One;\n      return Plural.Other;\n    case 'ga':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      if (n === Math.floor(n) && n >= 3 && n <= 6) return Plural.Few;\n      if (n === Math.floor(n) && n >= 7 && n <= 10) return Plural.Many;\n      return Plural.Other;\n    case 'gd':\n      if (n === 1 || n === 11) return Plural.One;\n      if (n === 2 || n === 12) return Plural.Two;\n      if (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19)) return Plural.Few;\n      return Plural.Other;\n    case 'gv':\n      if (v === 0 && i % 10 === 1) return Plural.One;\n      if (v === 0 && i % 10 === 2) return Plural.Two;\n      if (v === 0 &&\n          (i % 100 === 0 || i % 100 === 20 || i % 100 === 40 || i % 100 === 60 || i % 100 === 80))\n        return Plural.Few;\n      if (!(v === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'he':\n      if (i === 1 && v === 0) return Plural.One;\n      if (i === 2 && v === 0) return Plural.Two;\n      if (v === 0 && !(n >= 0 && n <= 10) && n % 10 === 0) return Plural.Many;\n      return Plural.Other;\n    case 'is':\n      if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return Plural.One;\n      return Plural.Other;\n    case 'ksh':\n      if (n === 0) return Plural.Zero;\n      if (n === 1) return Plural.One;\n      return Plural.Other;\n    case 'kw':\n    case 'naq':\n    case 'se':\n    case 'smn':\n      if (n === 1) return Plural.One;\n      if (n === 2) return Plural.Two;\n      return Plural.Other;\n    case 'lag':\n      if (n === 0) return Plural.Zero;\n      if ((i === 0 || i === 1) && !(n === 0)) return Plural.One;\n      return Plural.Other;\n    case 'lt':\n      if (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19)) return Plural.One;\n      if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 9 &&\n          !(n % 100 >= 11 && n % 100 <= 19))\n        return Plural.Few;\n      if (!(f === 0)) return Plural.Many;\n      return Plural.Other;\n    case 'lv':\n    case 'prg':\n      if (n % 10 === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19 ||\n          v === 2 && f % 100 === Math.floor(f % 100) && f % 100 >= 11 && f % 100 <= 19)\n        return Plural.Zero;\n      if (n % 10 === 1 && !(n % 100 === 11) || v === 2 && f % 10 === 1 && !(f % 100 === 11) ||\n          !(v === 2) && f % 10 === 1)\n        return Plural.One;\n      return Plural.Other;\n    case 'mk':\n      if (v === 0 && i % 10 === 1 || f % 10 === 1) return Plural.One;\n      return Plural.Other;\n    case 'mt':\n      if (n === 1) return Plural.One;\n      if (n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 10)\n        return Plural.Few;\n      if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19) return Plural.Many;\n      return Plural.Other;\n    case 'pl':\n      if (i === 1 && v === 0) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && !(i === 1) && i % 10 === Math.floor(i % 10) && i % 10 >= 0 && i % 10 <= 1 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 12 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'pt':\n      if (n === Math.floor(n) && n >= 0 && n <= 2 && !(n === 2)) return Plural.One;\n      return Plural.Other;\n    case 'ro':\n      if (i === 1 && v === 0) return Plural.One;\n      if (!(v === 0) || n === 0 ||\n          !(n === 1) && n % 100 === Math.floor(n % 100) && n % 100 >= 1 && n % 100 <= 19)\n        return Plural.Few;\n      return Plural.Other;\n    case 'ru':\n    case 'uk':\n      if (v === 0 && i % 10 === 1 && !(i % 100 === 11)) return Plural.One;\n      if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n          !(i % 100 >= 12 && i % 100 <= 14))\n        return Plural.Few;\n      if (v === 0 && i % 10 === 0 ||\n          v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n          v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 11 && i % 100 <= 14)\n        return Plural.Many;\n      return Plural.Other;\n    case 'shi':\n      if (i === 0 || n === 1) return Plural.One;\n      if (n === Math.floor(n) && n >= 2 && n <= 10) return Plural.Few;\n      return Plural.Other;\n    case 'si':\n      if (n === 0 || n === 1 || i === 0 && f === 1) return Plural.One;\n      return Plural.Other;\n    case 'sl':\n      if (v === 0 && i % 100 === 1) return Plural.One;\n      if (v === 0 && i % 100 === 2) return Plural.Two;\n      if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || !(v === 0))\n        return Plural.Few;\n      return Plural.Other;\n    case 'tzm':\n      if (n === Math.floor(n) && n >= 0 && n <= 1 || n === Math.floor(n) && n >= 11 && n <= 99)\n        return Plural.One;\n      return Plural.Other;\n    // When there is no specification, the default is always \"other\"\n    // Spec: http://cldr.unicode.org/index/cldr-spec/plural-rules\n    // > other (required—general plural form — also used if the language only has a single form)\n    default:\n      return Plural.Other;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {PlatformLocation,LOCATION_INITIALIZED,LocationChangeEvent,LocationChangeListener} from './platform_location';\nexport {LocationStrategy,APP_BASE_HREF} from './location_strategy';\nexport {HashLocationStrategy} from './hash_location_strategy';\nexport {PathLocationStrategy} from './path_location_strategy';\nexport {PopStateEvent,Location} from './location';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL for storing application location data.\n * \\@description \n * `PathLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n * \n * If you're using `PathLocationStrategy`, you must provide a {\\@link APP_BASE_HREF}\n * or add a base element to the document. This URL prefix that will be preserved\n * when generating and recognizing URLs.\n * \n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * Similarly, if you add `<base href='/my/app'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class PathLocationStrategy extends LocationStrategy {\nprivate _baseHref: string;\n/**\n * @param {?} _platformLocation\n * @param {?=} href\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        href?: string) {\n    super();\n\n    if (href == null) {\n      href = this._platformLocation.getBaseHrefFromDOM();\n    }\n\n    if (href == null) {\n      throw new Error(\n          `No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.`);\n    }\n\n    this._baseHref = href;\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    return Location.joinWithSlash(this._baseHref, internal);\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    const /** @type {?} */ pathname = this._platformLocation.pathname +\n        Location.normalizeQueryParams(this._platformLocation.search);\n    const /** @type {?} */ hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {\n    const /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction PathLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nPathLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nPathLocationStrategy.ctorParameters;\n/** @type {?} */\nPathLocationStrategy.prototype._baseHref;\n/** @type {?} */\nPathLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, Optional} from '@angular/core';\n\n\nimport {Location} from './location';\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\n/**\n * \\@whatItDoes Use URL hash for storing application location data.\n * \\@description \n * `HashLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n * \n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n * \n * ### Example\n * \n * {\\@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n * \n * \\@stable\n */\nexport class HashLocationStrategy extends LocationStrategy {\nprivate _baseHref: string = '';\n/**\n * @param {?} _platformLocation\n * @param {?=} _baseHref\n */\nconstructor(\nprivate _platformLocation: PlatformLocation,\n        _baseHref?: string) {\n    super();\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n/**\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener): void {\n    this._platformLocation.onPopState(fn);\n    this._platformLocation.onHashChange(fn);\n  }\n/**\n * @return {?}\n */\ngetBaseHref(): string { return this._baseHref; }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    let /** @type {?} */ path = this._platformLocation.hash;\n    if (path == null) path = '#';\n\n    return path.length > 0 ? path.substring(1) : path;\n  }\n/**\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string): string {\n    const /** @type {?} */ url = Location.joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? ('#' + url) : url;\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url: string|null =\n        this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.pushState(state, title, url);\n  }\n/**\n * @param {?} state\n * @param {?} title\n * @param {?} path\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, path: string, queryParams: string) {\n    let /** @type {?} */ url = this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.replaceState(state, title, url);\n  }\n/**\n * @return {?}\n */\nforward(): void { this._platformLocation.forward(); }\n/**\n * @return {?}\n */\nback(): void { this._platformLocation.back(); }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: PlatformLocation, },\n{type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF, ] }, ]},\n];\n}\n\nfunction HashLocationStrategy_tsickle_Closure_declarations() {\n/** @type {?} */\nHashLocationStrategy.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nHashLocationStrategy.ctorParameters;\n/** @type {?} */\nHashLocationStrategy.prototype._baseHref;\n/** @type {?} */\nHashLocationStrategy.prototype._platformLocation;\n}\n\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {EventEmitter, Injectable} from '@angular/core';\n\nimport {LocationStrategy} from './location_strategy';\n\n/** @experimental */\nexport interface PopStateEvent {\n  pop?: boolean;\n  type?: string;\n  url?: string;\n}\n/**\n * \\@whatItDoes `Location` is a service that applications can use to interact with a browser's URL.\n * \\@description \n * Depending on which {\\@link LocationStrategy} is used, `Location` will either persist\n * to the URL's path or the URL's hash segment.\n * \n * Note: it's better to use {\\@link Router#navigate} service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n * \n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n * \n * ### Example\n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \\@stable\n */\nexport class Location {\n/**\n * \\@internal\n */\n_subject: EventEmitter<any> = new EventEmitter();\n/**\n * \\@internal\n */\n_baseHref: string;\n/**\n * \\@internal\n */\n_platformStrategy: LocationStrategy;\n/**\n * @param {?} platformStrategy\n */\nconstructor(platformStrategy: LocationStrategy) {\n    this._platformStrategy = platformStrategy;\n    const browserBaseHref = this._platformStrategy.getBaseHref();\n    this._baseHref = Location.stripTrailingSlash(_stripIndexHtml(browserBaseHref));\n    this._platformStrategy.onPopState((ev) => {\n      this._subject.emit({\n        'url': this.path(true),\n        'pop': true,\n        'type': ev.type,\n      });\n    });\n  }\n/**\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash: boolean = false): string {\n    return this.normalize(this._platformStrategy.path(includeHash));\n  }\n/**\n * Normalizes the given path and compares to the current normalized path.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nisCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    return this.path() == this.normalize(path + Location.normalizeQueryParams(query));\n  }\n/**\n * Given a string representing a URL, returns the normalized URL path without leading or\n * trailing slashes.\n * @param {?} url\n * @return {?}\n */\nnormalize(url: string): string {\n    return Location.stripTrailingSlash(_stripBaseHref(this._baseHref, _stripIndexHtml(url)));\n  }\n/**\n * Given a string representing a URL, returns the platform-specific external URL path.\n * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n * @param {?} url\n * @return {?}\n */\nprepareExternalUrl(url: string): string {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._platformStrategy.prepareExternalUrl(url);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and pushes a\n * new item onto the platform's history.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\ngo(path: string, query: string = ''): void {\n    this._platformStrategy.pushState(null, '', path, query);\n  }\n/**\n * Changes the browsers URL to the normalized version of the given URL, and replaces\n * the top item on the platform's history stack.\n * @param {?} path\n * @param {?=} query\n * @return {?}\n */\nreplaceState(path: string, query: string = ''): void {\n    this._platformStrategy.replaceState(null, '', path, query);\n  }\n/**\n * Navigates forward in the platform's history.\n * @return {?}\n */\nforward(): void { this._platformStrategy.forward(); }\n/**\n * Navigates back in the platform's history.\n * @return {?}\n */\nback(): void { this._platformStrategy.back(); }\n/**\n * Subscribe to the platform's `popState` events.\n * @param {?} onNext\n * @param {?=} onThrow\n * @param {?=} onReturn\n * @return {?}\n */\nsubscribe(\n      onNext: (value: PopStateEvent) => void, onThrow?: ((exception: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n/**\n * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as\n * is.\n * @param {?} params\n * @return {?}\n */\npublic static normalizeQueryParams(params: string): string {\n    return params && params[0] !== '?' ? '?' + params : params;\n  }\n/**\n * Given 2 parts of a url, join them with a slash if needed.\n * @param {?} start\n * @param {?} end\n * @return {?}\n */\npublic static joinWithSlash(start: string, end: string): string {\n    if (start.length == 0) {\n      return end;\n    }\n    if (end.length == 0) {\n      return start;\n    }\n    let /** @type {?} */ slashes = 0;\n    if (start.endsWith('/')) {\n      slashes++;\n    }\n    if (end.startsWith('/')) {\n      slashes++;\n    }\n    if (slashes == 2) {\n      return start + end.substring(1);\n    }\n    if (slashes == 1) {\n      return start + end;\n    }\n    return start + '/' + end;\n  }\n/**\n * If url has a trailing slash, remove it, otherwise return url as is. This\n * method looks for the first occurence of either #, ?, or the end of the\n * line as `/` characters after any of these should not be replaced.\n * @param {?} url\n * @return {?}\n */\npublic static stripTrailingSlash(url: string): string {\n    const /** @type {?} */ match = url.match(/#|\\?|$/);\n    const /** @type {?} */ pathEndIdx = match && match.index || url.length;\n    const /** @type {?} */ droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n    return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/**\n * @nocollapse\n */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: LocationStrategy, },\n];\n}\n\nfunction Location_tsickle_Closure_declarations() {\n/** @type {?} */\nLocation.decorators;\n/**\n * @nocollapse\n * @type {?}\n */\nLocation.ctorParameters;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._subject;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._baseHref;\n/**\n * \\@internal\n * @type {?}\n */\nLocation.prototype._platformStrategy;\n}\n\n/**\n * @param {?} baseHref\n * @param {?} url\n * @return {?}\n */\nfunction _stripBaseHref(baseHref: string, url: string): string {\n  return baseHref && url.startsWith(baseHref) ? url.substring(baseHref.length) : url;\n}\n/**\n * @param {?} url\n * @return {?}\n */\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\nimport {LocationChangeListener} from './platform_location';\n/**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. Angular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n * \n * This is used under the hood of the {\\@link Location} service.\n * \n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n * \n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n * \n * See these two classes for more.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class LocationStrategy {\n/**\n * @abstract\n * @param {?=} includeHash\n * @return {?}\n */\npath(includeHash?: boolean) {}\n/**\n * @abstract\n * @param {?} internal\n * @return {?}\n */\nprepareExternalUrl(internal: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\npushState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @param {?} queryParams\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string, queryParams: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHref() {}\n}\n/**\n * The `APP_BASE_HREF` token represents the base href to be used with the\n * {\\@link PathLocationStrategy}.\n * \n * If you're using {\\@link PathLocationStrategy}, you must provide a provider to a string\n * representing the URL prefix that should be preserved when generating and recognizing\n * URLs.\n * \n * ### Example\n * \n * ```typescript\n * import {Component, NgModule} from '\\@angular/core';\n * import {APP_BASE_HREF} from '\\@angular/common';\n * \n * \\@NgModule({ \n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n * \n * \\@stable\n */\nexport const APP_BASE_HREF = new InjectionToken<string>('appBaseHref');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {InjectionToken} from '@angular/core';\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n * \n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n * \n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n * \n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n * \n * \\@stable\n * @abstract\n */\nexport abstract class PlatformLocation {\n/**\n * @abstract\n * @return {?}\n */\ngetBaseHrefFromDOM() {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonPopState(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @param {?} fn\n * @return {?}\n */\nonHashChange(fn: LocationChangeListener) {}\n/**\n * @abstract\n * @return {?}\n */\npathname() {}\n/**\n * @abstract\n * @return {?}\n */\nsearch() {}\n/**\n * @abstract\n * @return {?}\n */\nhash() {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\nreplaceState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @param {?} state\n * @param {?} title\n * @param {?} url\n * @return {?}\n */\npushState(state: any, title: string, url: string) {}\n/**\n * @abstract\n * @return {?}\n */\nforward() {}\n/**\n * @abstract\n * @return {?}\n */\nback() {}\n}\n/**\n * \\@whatItDoes indicates when a location is initialized\n * \\@experimental\n */\nexport const LOCATION_INITIALIZED = new InjectionToken<Promise<any>>('Location Initialized');\n\n/**\n * A serializable version of the event from onPopState or onHashChange\n *\n * @experimental\n */\nexport interface LocationChangeEvent { type: string; }\n\n/**\n * @experimental\n */\nexport interface LocationChangeListener { (e: LocationChangeEvent): any; }\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator.throw(value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}"], "names": ["NgModule", "<PERSON><PERSON>", "ɵisPromise", "Input", "ViewContainerRef", "Directive", "ElementRef", "KeyValueDiffers", "TemplateRef", "Host", "Iterable<PERSON><PERSON><PERSON>", "isDevMode", "NgModuleRef", "<PERSON><PERSON><PERSON>", "ɵisListLikeIterable", "Inject", "LOCALE_ID", "Injectable", "tslib_1.__extends", "EventEmitter"], "mappings": ";;;;;;A4BAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADtHP,IAAA,gBAAA,IAAA,YAAA;IAAA,SAAA,gBAAA,GAAA;KA2DA;;;;;;;;;;;;;;;;;IA1CA,gBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,EAAA,EAAA,GAAA,CAAA;;;;;IAKA,gBAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA,GAAA,CAAA;;;;;IAKA,gBAAA,CAAA,SAAA,CAAA,MAAA,GAAA,YAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;IAqBA,gBAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA,GAAA,CAAA;;;;;IAKA,gBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,GAAA,CAAA;;;;;;IAMA,OAAA,gBAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDhCA,gBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UA/BY,KA+BZ,EAAA,KAAA,EAAA,GAAA,EAAA,WAAA,EAAA,GAAA,CAAA;;;;;IAKA,gBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,GAAA,CAAA;;;;;;;;;;;IAWA,gBAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UA5CY,EA4CZ,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDxBA,SAAA,QAAA,CAAA,gBAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAeA;;;;QAXA,IAAM,CAAN,QAAA,GAAA,IAAAmB,0BAAA,EAAA,CAAA;QACA,IAAA,CAAA,iBAAA,GAAyB,gBAAzB,CAAA;QACA,IAAA,eAAA,GAAA,IAAA,CAAA,iBAAA,CAAA,WAAA,EAAA,CAAA;QACA,IAAA,CAAA,SAAc,GAAd,QAAA,CAAA,kBAAA,CAAA,eAAA,CAAA,eAAA,CAAA,CAAA,CAAA;QACA,IAAA,CAAO,iBAAP,CAAA,UAAA,CAAA,UAAA,EAAA,EAAA;YACA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA;gBACA,KAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;;;;KAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCA,QAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,GAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyBA,CAAA;;;;;IAKA,QAAA,CAAA,SAAA,CAAA,OAAA,GAAA,YAAA,EAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,EAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiCA,QAAA,CAAA,aAlBa,GAkBb,UAlBmB,KAkBnB,EAAA,GAAA,EAAA;QACA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;YACA,OAAA,GAAA,CAAA;SACA;QACA,IAAM,GAAN,CAAA,MAlBgB,IAkBhB,CAAA,EAAA;YACA,OAAA,KAAA,CAAA;SACA;QACA,qBAAA,OAAA,GAAA,CAAA,CAAA;QACA,IAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;YACQ,OAlBC,EAkBT,CAAA;SACA;QACA,IAAA,GAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;YACQ,OAlBC,EAkBT,CAAA;SACA;QACA,IAAA,OAAA,IAAA,CAAA,EAAA;YACA,OAAA,KAAA,GAAA,GAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;SACA;;;;;;;;;;;;;IAaA,QAAA,CAAA,kBAAA,GAAA,UAAA,GAAA,EAAA;;QAlBA,qBAAA,UAAA,GAAA,KAAA,IAAA,KAAA,CAAA,KAAA,IAAA,GAAA,CAAA,MAAA,CAAA;QAoBA,qBAAA,eAAA,GAAA,UAAA,IAAA,GAAA,CAAA,UAAA,GAAA,CAAA,CAAA,KAAA,GAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA;QACA,OAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,eAAA,CAAA,GAAA,GAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;;;;;IAjBA,EAAA,IAAA,EAAAF,wBAAA,EAAA;CAsBA,CAAA;;;;;;;;;;;;;;CAyCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADjNA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAEQC,SAAR,CAAA,oBAAA,EAAA,MAAA,CAAA,CAFA;;;;;IAKA,SAAA,oBAAA,CAAU,iBAAV,EAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GACA,MADA,CAAA,IAAA,CAAA,IAAA,CACA,IADA,IAAA,CAOA;QALA,KAAA,CAAA,iBAAA,GAAA,iBAAA,CAAA;;;;;;KAKA;;;;;;;QAOA,IAAA,CAAA,iBAAA,CANiC,YAMjC,CANiD,EAMjD,CAAA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,OAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA;;;;;IALA,oBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA,WAAA,EAAA;QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,KAAA,CAAA,EAAA;;;;;;;KAiBA,CAAA;;;;;;;;;;;;;;;;IAiBA,oBAAA,CAAA,SAAA,CAAA,SAlBS,GAkBT,UAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,WAAA,EAAA;QACA,qBAAA,GAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,IAAA,GAAA,QAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;IAaA,oBAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,KAAA,EAAA,KAAA,EAxB2B,IAwB3B,EAAA,WAAA,EAAA;QACA,qBAAA,GAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,IAAA,GAAA,QAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,CAAA,CAAA;;;;QAIA,IAAA,CAAA,iBAAA,CAAA,YAAA,CAAA,KA1BqD,EAAC,KA0BtD,EAAA,GAAA,CAAA,CAAA;;;;;;;;;;;CAxEA,CAEQ,gBAFR,CAAA,CAAA,CAAA;;IAqDA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CA+BA,CAAA;;;;AD1HA,oBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAEQC,SAAR,CAAA,oBAAA,EAAA,MAAA,CAAA,CAFA;;;;;IAOA,SAAA,oBAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;QAAA,IAAA,KAAA,GAEI,MAFJ,CAAA,IAAA,CAAA,IAAA,CAEA,IAFA,IAAA,CAaA;QAVA,KAAM,CAAN,iBAAA,GAAA,iBAAA,CAAA;QAEA,IAAA,IAAA,IAAA,IAAA,EAAA;YAEQ,IAAR,GAAA,KAAA,CAAA,iBAAA,CAAA,kBAAA,EAAA,CAAA;SACA;;;;;;KAKA;;;;;;;QAOA,IAAA,CAAA,iBAAA,CALiC,YAKjC,CALiD,EAKjD,CAAA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA,EAAA,OAAA,IARG,CAQH,SAAA,CAAA,EAAA,CAAA;;;;;;;KAOA,CAAA;;;;;IAKA,oBAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA,WAAA,EAAA;QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAA,GAAA,KAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;QA0BA,IAAA,CAAA,iBAAA,CAAA,YAAA,CAAA,KAzBqD,EAAC,KAyBtD,EAAA,WAAA,CAAA,CAAA;;;;;;;;;;;CAtEA,CAEQ,gBAFR,CAAA,CAAA,CAAA;;IAoDA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CA8BA,CAAA;;;;ADjIA,oBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;;CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADwCA,SAAA,iBAAA,CAAA,KAAA,EAAA,KAAA,EAAA,cAAA,EAAA;IAEE,qBATmB,GASrB,GAT6B,GAS7B,GAAA,KAAA,CAAA;IACA,IAAI,KAAJ,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA;QACA,OAAA,GAAA,CAAA;KAEA;IACA,GAAA,GAAA,cAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,CAAA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA;QAEA,OAAA,GAAA,CATY;KAUZ;;;;;;;;;;;AAAA,IAAA,oBAAA,IAAA,UAAA,MAAA,EAAA;IAAAC,SAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;;;;;QAeA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA;;;KAAA;;;;;IAMA,oBAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,UAAA,KAbmB,EAanB;QACA,qBAAA,MAAA,GAAA,aAAA,CAAA,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;QACA,QAAA,MAAA;YACA,KAAA,MAbe,CAaf,IAbe;gBAcf,OAbW,MAaX,CAAA;YACA,KAAA,MAbe,CAaf,GAAA;gBANA,OANW,KAAQ,CAMnB;YAQA,KAAA,MAbe,CAaf,GAAA;gBACA,OAAA,KAAA,CAAA;YACA,KAAA,MAbe,CAaf,GAAA;gBACA,OAAA,KAAA,CAAA;YACA,KAAA,MAAA,CAAA,IAAA;;YAZA;gBAcA,OAAA,OAAA,CAAA;SACA;;;CAnCA,CAAA,cAAA,CAAA,CAAA,CAAA;;IAwBA,EAAA,IAAA,EAAAD,wBAAA,EAAA;CAgBA,CAAA;;;;AAmBA,oBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IACA,EAAM,IAAI,EAAV,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAF,oBAAA,EAAA,IAAA,EAAA,CAAAC,uBAAA,EAAA,EAAA,EAAA,EAAA;CACA,CAAA,EAAA,CAAA;AACA,IAAA,MAAA,GAAA,EAPe,CAAA;AAQf,MAAM,CAAC,IAAP,GAAA,CAPe,CAAC;AAQhB,MAAM,CAAC,GAAP,GAPS,CAAK,CAOd;AACA,MAAM,CAAC,GAAP,GAPS,CAAA,CAOT;AACA,MAAM,CAAC,GAAP,GAPS,CAAA,CAOT;AACA,MAAM,CAAC,IAAP,GAPc,CAOd,CAAA;AACA,MAAM,CAAC,KAAP,GAAA,CAAA,CAAA;AACA,MAAM,CAAC,MAPE,CAAK,IAOd,CAPe,GAOf,MAAA,CAAA;;;;;;;;;;;;;;AAeA,SAAA,aAAA,CAAA,MAAA,EAAA,KAAA,EAAA;;IAEE,IAAF,OAAA,KAAA,KAAA,QAAA,EAvCY;QAwCZ,KAAA,GAAA,QAAA,mBAAA,KAAA,GAAA,EAAA,CAAA,CAAA;KACA;IACE,qBAvCM,CAAA,IAuCR,KAAA,CAAA,CAvCY;IAyCV,qBAvCM,QAuCR,GAAA,CAAA,CAvCe,QAuCf,EAvC6B,CAAG,OAuChC,CAAA,WAAA,EAAA,EAAA,CAAA,CAAA;IAEE,qBAAF,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,qBAAA,CAAA,GAAA,QAAA,CAAA,MAAA,CAAA;IACA,qBAAA,CAAA,GAAA,QAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA;IACA,qBAAA,CAAA,GAAA,QAAA,CAAA,CAAA,CAAA,QAAA,EAAA,CAAA,OAAA,CAAA,gBAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,IAAA,CAAA,CAAA;IACA,qBAAA,IAAA,GAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,CAAA;IACA,QAAA,IAAA;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAA,CAAM;QAwCX,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,KAAA,CAAM;QAwCX,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCL,KAvCK,IAuCT,CAvCS;QAwCT,KAAA,IAvCW,CAuCX;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,KAAA,CAvCa;QAwCT,KAvCK,KAAK,CAuCd;QACI,KAvCK,KAAK;YAwCd,IAAA,CAvCS,KAuCT,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW,CAuCX;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,IAAA,CAAA;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA;YAwCT,IAAA,CAvCS,KAuCT,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAuCT;QACA,KAAA,IAvCW,CAuCX;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,IAAA,CAAA;QACI,KAvCK,IAAA,CAuCT;QACA,KAAA,IAvCW;YAAX,IAAmB,CAAnB,KAAA,CAA0B,IAA1B,CAAA,KAAA,CAAkC;gBAwCxB,OAvCM,MAuChB,CAAA,GAAA,CAAA;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAAX,IAAmB,CAAnB,KAAA,CAA0B;gBAwChB,OAvCI,MAuCd,CAAA,IAAA,CAAA;YAvCA,IAA4E,CAA5E,KAAA,CAAmF;gBAwCzE,OAvCI,MAuCd,CAAA,GAvC2B,CAuC3B;YAvCA,IAA6E,CAA7E,KAAA,CAAoF;gBAwCpF,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,GAuCT,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBACA,OAAA,MAAA,CAAA,IAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACI,KAvCK,KAAK,CAuCd;QACI,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAAK;QAwCV,KAvCK,IAAA,CAuCT;QACA,KAAA,IAvCW,CAuCX;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,IAAA,CAAA;QACI,KAvCK,IAAA,CAuCT;QACA,KAAA,IAvCW;YAAX,IAA6C,CAA7C,KAAA,CAAoD,IAApD,CAAA,KAA4D,CAAG;gBAwCrD,OAAV,MAAA,CAAA,GAAA,CAAA;YANA,OAhCa,MAAG,CAgChB,KAhCuB,CAgCvB;QAQA,KAAA,IAAA;YACM,IAAI,CAvCC,GAAG,EAAA,KAAO,CAAA,IAAK,EAuC1B,CAAA,GAvC8B,GAuC9B,KAAA,EAvC0C,CAuC1C;gBACU,OAvCI,MAuCd,CAAA,GAvC2B,CAuC3B;YACA,IAAQ,CAAR,GAAA,EAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBACA,EAAA,CAvCa,GAuCb,GAvCa,IAuCb,EAvCqB,IAuCrB,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACM,IAPG,CAhCE,GAAG,EAAA,KAAO,CAAA,IAAK,CAuC1B,GAAA,EAvCgC,KAuChC,IAAA,CAvCwC,KAAM,CAuC9C,CAAA,GAAA,EAAA,CAAA,IAAA,CAvC0D,GAuC1D,EAvCgE,IAuChE,CAAA,IAAA,CAAA,GAvC4E,EAuC5E,IAAA,CAAA;gBAvCiF,CAAjF,GAAA,GAAwF,KAAxF,IAAA,CAAgG,KAAhG,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBAwCU,OAAV,MAAA,CAAA,IAvC4B,CAAC;YAA7B,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAiCX,IAAS,CAAT,GAAA,EAhCgB,KAgChB,CAAA,IAAA,EAAA,CAhC6B,GAgC7B,GAAA,KAhCwC,EAAA,IAAM,CAAA,GAAI,GAAA,KAgClD,EAAA,IAhC+D,CAgC/D,GAAA,GAAA,KAhC0E,EAgC1E,CAAA;gBACA,OAAA,MAAA,CAAA,GAhCuB,CAgCvB;YAQA,IAAQ,CAAR,GAAA,EAAA,KAAA,CAAA,IAAA,EAvC0B,CAuC1B,GAAA,GAAA,KAAA,EAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA;gBACU,OAAV,MAAA,CAvCwB,GAuCxB,CAvC4B;YAA5B,IAAuC,CAAvC,GAAA,EAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,CAAA;gBAwCA,EAAA,CAvCa,GAuCb,GAvCa,IAuCb,EAvCqB,IAuCrB,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;oBACA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,CAAA;gBANS,OAhCO,MAuChB,CAAA,IAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW,CAuCX;QANA,KAAA,IAAA,CAhCgB;QAwChB,KAAA,IAPW;YACX,IAAA,CAAA,KAhCgB,CAAC,IAgCjB,CAAA,GAAA,EAhC2B,KAgC3B,CAhCiC,IAAI,EAgCrC,CAhCqC,GAgCrC,GAhC4C,KAgC5C,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA;gBAQQ,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAN,CAAA,KAAA,CAAA,IAAA,CAAA,GAvCqB,EAuCrB,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBACA,EAvCc,CAuCd,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,CAvCS,GAuCT,EAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;oBACA,EAvCgB,CAAA,GAuChB,GAAA,IAvC2B,EAuC3B,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBAvC8B,OAAO,MAAA,CAAO,GAAC,CAAG;YAwC1C,OAAN,MAAA,CAvCgB,KAuChB,CAvCsB;QAAtB,KAAA,IAAA,CAAA;QAwCA,KAAA,IAAA;YAvCA,IAAsB,CAAtB,KAAA,CAA6B,IAA7B,CAAA,KAAA,CAAqC;gBAwCrC,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAAA,CAAA;YAvCA,IAAmB,EAAnB,CAAA,KAAA,CAAA,CAAA;gBAwCU,OAvCM,MAuChB,CAAA,IAAA,CAAA;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAAX,IAAmB,CAAnB,KAAA,CAA0B;gBAiCjB,OAhCO,MAuChB,CAAA,IAAA,CAAA;YAvCA,IAAmB,CAAnB,KAAA,CAA0B;gBAwChB,OAvCM,MAuChB,CAAA,GAAA,CAAA;YAvCA,IAAmB,CAAnB,KAAA,CAA0B;gBAwC1B,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA;gBACU,OAvCM,MAAO,CAAC,GAuCxB,CAAA;YAvCA,IAAyD,CAAzD,KAAA,CAAgE;gBAwChE,OAAA,MAAA,CAAA,IAAA,CAAA;YACA,OAvCe,MAuCf,CAAA,KAAA,CAAA;QACI,KAvCK,IAuCT;YACM,IAAI,CAvCC,KAAK,CAAA,IAAK,EAuCrB,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,KAvC0C,CAuC1C,IAAA,CAAA,KAAA,CAAA,CAAA;gBAvCqD,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCjE,OAAN,MAAA,CAAA,KAvCyB,CAuCzB;QAvCA,KAAA,KAAA,CAAA;QAwCA,KAAA,KAAA;YACA,IAAU,CAPC,KAOX,CAvCc,IAuCd,CAvCsB,GAuCtB,GAAA,KAvCiC,CAAC,IAuClC,CAAA,GAAA,GAvC6C,KAuC7C,CAAA;gBACQ,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAAN,CAAA,KAAA,CAAA,IAAA,CAAA,GAvCqB,GAuCrB,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA;gBACA,CAvCS,GAuCT,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACM,OAAN,MAAA,CAAA,KAAA,CAAA;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,IAAA,CAAA;QACI,KAvCK,IAuCT,CAvCS;QAwCT,KAAA,KAAA;YACA,IAAU,CAPC,KAhCK,CAAA,IAAK,CAuCrB,KAvC2B,CAuC3B;gBACU,OAAV,MAAA,CAvCwB,GAAG,CAuC3B;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,KAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;gBANS,CAhCE,KAAK,CAAA,IAuChB,EAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,CAAA;gBAvCmB,EAAnB,CAAA,KAAA,CAAA,CAAA,IAAiC,EAAjC,CAAkC,GAAlC,EAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,CAAA;gBAiCS,OAhCO,MAuChB,CAAA,GAAA,CAAA;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAAX,IAAmD,CAAnD,KAAA,CAA0D;gBAwChD,OAAV,MAAA,CAAA,GAvC2B,CAAC;YAA5B,IAAoD,CAApD,KAAA,CAA2D;gBAwC3D,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvC2B,CAuC3B;YAvCA,IAA+B,CAA/B,KAAA,IAAA,CAAA,KAAA,CAA8C,CAAG,CAAjD,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;gBAwCU,OAvCM,MAuChB,CAAA,IAAA,CAvC2B;YAA3B,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAAX,IAA4E,CAA5E,KAAA,CAAmF,IAAnF,CAAA,KAA2F,EAA3F;gBAwCA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,CAAA,KAAA,EAAA;gBACU,OAvCM,MAuChB,CAAA,GAvCyB,CAuCzB;YAvCA,IAAmC,CAAnC,KAAA,IAAA,CAAA,KAAA,CAAkD,CAAG,CAArD,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA,CAAA;gBAwCU,OAvCM,MAuChB,CAAA,GAvCyB,CAuCzB;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAiCX,IAAA,CAAW,KAAX,CAAA,IAAA,CAAA,GAAA,EAAA,KAhCgC,CAgChC;gBAQQ,OAvCO,MAAA,CAAO,GAAC,CAAG;YAwCpB,IAPG,CAOT,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA;gBAvCsB,OAAO,MAAA,CAAO,GAApC,CAAqC;YAwC/B,IAAN,CAAA,KAAA,CAAA;iBAtCS,CAuCT,GAAA,GAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,IAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvC2B,CAAA;YAA3B,IAA8B,EAA9B,CAAA,KAAA,CAAA,CAAA;gBAwCU,OAvCM,MAuChB,CAAA,IAvC2B,CAuC3B;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAAX,IAA2D,CAA3D,KAAA,CAAkE,IAAlE,CAAA,KAAA,CAA0E;gBAwC1E,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,CAAA,KAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvCyB,CAuCzB;YAvCA,IAAsE,CAAtE,KAAA,CAA6E,IAA7E,EAA6E,CAAO,IAAI,CAAxF,IAAA,CAAA,IAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA;gBAwCA,OAAA,MAAA,CAAA,IAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW;YAAX,IAAmB,CAAnB,KAAA,CAA0B,IAA1B,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA;gBAiCS,OAhCO,MAuChB,CAAA,GAAA,CAAA;YAvCA,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,KAAA;YACA,IAAA,CAvCS,KAuCT,CAAA;gBACA,OAAA,MAAA,CAAA,IAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACM,OAAN,MAAA,CAAA,KAAA,CAAA;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,KAAA,CAAA;QAvCA,KAAA,IAAA,CAAA;QAwCA,KAAA,KAAA;YACA,IAAA,CAAA,KAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAAA,CAAA;YAvCA,IAAmB,CAAnB,KAAA,CAA0B;gBAwChB,OAvCO,MAAK,CAuCtB,GAAA,CAvC4B;YAA5B,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,KAAA;YACA,IAAA,CAvCS,KAuCT,CAAA;gBACU,OAAV,MAAA,CAAA,IAvC4B,CAAC;YAA7B,IAA6D,CAA7D,CAAA,KAAoE,CAApE,IAAA,CAAoE,KAAW,CAA/E,KAAA,EAAA,CAAA,KAAA,CAAA,CAAA;gBAwCU,OAAV,MAAA,CAAA,GAAA,CAAA;YANA,OAhCa,MAAG,CAgChB,KAhCuB,CAgCvB;QAQA,KAAA,IAAA;YACM,IAAI,CAAV,GAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBAvCsB,OAAO,MAAA,CAAO,GAApC,CAAqC;YAwC/B,IAAN,CAAA,GAAA,EAAA,KAAA,IAvCqB,CAAK,KAuC1B,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBACA,EAvCc,CAuCd,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACM,IAAI,EAAV,CAAA,KAAA,CAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,IAvCyB,CAuCzB;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW,CAuCX;QANA,KAAA,KAhCY;YAwCZ,IAAQ,CAAR,GAAA,EAAA,KAAA,CAAA,IAAA,CAvCuB,GAuCvB,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBACA,CAAA,KAAA,CAAA,IAAA,CAAA,GAvCqB,GAuCrB,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBACA,OAAA,MAAA,CAAA,IAAA,CAAA;YACM,IAAI,CAvCC,GAuCX,EAvCgB,KAAK,CAAA,IAuCrB,EAAA,CAAA,GAvCgC,GAuChC,KAAA,EAAA,CAvCyC,IAuCzC,CAvCgD,KAuChD,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA;gBAvCmD,EAAnD,CAAA,KAAA,CAAA,CAAA,IAAiE,CAAjE,GAAqE,EAArE,KAAA,CAAA;gBAwCA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW;YAAX,IAAmB,CAAnB,KAAA,CAA0B,IAA1B,CAAA,GAAA,EAAkC,KAAlC,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA;gBAwCU,OAvCM,MAuChB,CAAA,GAAA,CAvCyB;YAwCzB,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW;YAAX,IAA6E,CAA7E,KAAA,CAAoF;gBAwCpF,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBACU,OAvCM,MAuChB,CAAA,GAvC2B,CAAA;YAA3B,IAA8B,CAA9B,GAAA,GAAqC,KAArC,IAAA,CAA6C,KAA7C,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBAiCS,OAhCO,MAuChB,CAAA,IAAA,CAAA;YACA,OAvCa,MAAG,CAuChB,KAvCuB,CAuCvB;QACA,KAAA,IAAA;YACM,IAPG,CAhCE,KAAK,CAAA,IAAK,CAuCrB,KAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvCyB,CAuCzB;YACA,IAAU,CAPC,KAhCK,CAAA,IAAK,CAAA,GAAI,EAuCzB,KAAA,IAAA,CAvCiC,KAuCjC,CAvCuC,CAAK,GAuC5C,EAAA,CAAA,IAAA,CAAA,GAAA,EAvC4D,IAuC5D,CAAA,IAvCmE,CAuCnE,GAvCyE,EAuCzE,IAAA,CAvC6E;gBAwCrE,EAAR,CAAA,GAAA,GAAA,IAvCe,EAuCf,IAvC2B,CAuC3B,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBANS,CAhCE,KAAK,CAuChB,IAvCqB,CAuCrB,GAAA,EAvC2B,KAuC3B,IAAA,CAAA,KAAA,CAvC2C,CAAA,GAuC3C,EAvCgD,CAuChD,IAAA,CAvCuD,GAuCvD,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBAvCiE,CAAjE,KAAA,CAAwE,IAAxE,CAAA,GAAA,GAAmF,KAAnF,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBAwCA,OAAA,MAAA,CAAA,IAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW;YAAX,IAA8B,CAA9B,KAAA,IAAA,CAAA,KAAA,CAA6C,CAAG,CAAhD,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA;gBAwCU,OAAV,MAAA,CAvCwB,GAuCxB,CAAA;YACA,OAvCa,MAAK,CAAE,KAAI,CAuCxB;QACA,KAAA,IAAA;YACM,IAAN,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YANA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA;gBACE,EAAF,CAAA,KAAA,CAAA,CAAA,IAAA,CAhCyB,GAgCzB,GAAA,KAAA,IAhCwC,CAgCxC,KAhC2C,CAgC3C,CAAA,GAhCmD,GAAG,CAgCtD,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBAhCwD,OAAO,MAAA,CAAO,GAAC,CAAG;YAwCpE,OAAN,MAAA,CAAA,KAvCyB,CAuCzB;QACA,KAAA,IAAA,CAvCY;QAwCZ,KAAA,IAAA;YACM,IAAI,CAvCC,KAAK,CAAA,IAAK,CAAA,GAAI,EAAA,KAAO,CAAA,IAuChC,EAAA,CAAA,GAAA,GAAA,KAAA,EAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvCyB,CAuCzB;YACA,IAAU,CAAC,KAvCK,CAAA,IAAK,CAAA,GAAI,EAuCzB,KAAA,IAAA,CAvCiC,KAuCjC,CAvCuC,CAAK,GAuC5C,EAAA,CAAA,IAAA,CAAA,GAAA,EAvC4D,IAuC5D,CAAA,IAvCmE,CAuCnE,GAvCyE,EAuCzE,IAAA,CAvC6E;gBAwCrE,EAAR,CAAA,GAAA,GAAA,IAvCe,EAuCf,IAvC2B,CAuC3B,GAAA,GAAA,IAAA,EAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,CAAA;gBACU,CAvCC,KAAK,CAAA,IAAK,CAAA,GAuCrB,EAvC2B,KAuC3B,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA;gBAvC8B,CAA9B,KAAA,CAAqC,IAArC,CAAA,GAAA,GAAgD,KAAhD,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA,IAAA,CAAA,GAAA,GAAA,IAAA,EAAA;gBAwCU,OAAV,MAAA,CAAA,IAvC4B,CAAC;YAA7B,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,KAAA;YACA,IAAA,CAvCS,KAuCT,CAAA,IAAA,CAAA,KAAA,CAAA;gBACU,OAvCM,MAuChB,CAAA,GAvC2B,CAAA;YAA3B,IAAoD,CAApD,KAAA,IAAA,CAAA,KAAA,CAAmE,CAAG,CAAtE,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;gBAwCA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,OAAA,MAAA,CAAA,KAAA,CAAA;QACA,KAAA,IAvCW;YAAX,IAAoC,CAApC,KAAA,CAA2C,IAA3C,CAAA,KAAmD,CAAG,IAAtD,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA;gBAwCU,OAvCM,MAuChB,CAAA,GAAA,CAvCyB;YAAzB,OAAA,MAAA,CAAA,KAAA,CAAA;QAwCA,KAAA,IAvCW;YAwCX,IAAQ,CAAR,KAAA,CAvCe,IAuCf,CAAA,GAAA,GAvC0B,KAuC1B,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;YACA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KAAA,CAAA;gBACU,OAAV,MAAA,CAAA,GAvC2B,CAAC;YAwC5B,IAAQ,CAAR,KAAA,CAvCe,IAuCf,CAAA,GAAA,GAvC0B,KAuC1B,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA;gBACA,OAAA,MAAA,CAAA,GAAA,CAAA;;;;gBAIA,OAAA,MAAA,CAAA,GAAA,CAAA;YACM,OAvCO,MAAA,CAAO,KAAC,CAAK;;;;QD9Y1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0CA,SAAA,OAAA,CAAA,gBAAA,EAAyC,gBAAzC,EAAA,KAAA,EAAA,SAAA,EAAA;QAKA,IAAA,CAAA,gBAAA,GAAA,gBAAA,CAAA;;;;;KAWA;IAKA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,SAAA,EAAA,OAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,CAAA,EAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;IAQA,MAAA,CAAA,cAAA,CAAI,OAAJ,CAAA,SAAA,EAAA,SAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAVS,CAAI,EAUb;YACA,IAAM,CAAN,eAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;YACA,IAAA,CAAA,eAAA,GAAA,IAAA,CAVc;YAWd,IAAA,CAAO,eAAP,GAAA,IAAA,CAAA;YAVA,IAAA,CAAA,SAAA,GAAA,OAAA,CAAA,KAAA,QAAA,GAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;YAWA,IAAA,IAAQ,CAAR,SAAA,EAAA;gBACA,IAAAF,iCAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA;oBACA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,MAAA,EAAA,CAAA;iBACA;;;;aAIA;SACA;;;KAAA,CAAA,CAAA;;;;IAIA,OAAA,CAAA,SAAA,CAAA,SAAO,GAAP,YAAA;QACA,IAAA,IAAA,CAAA,eAAA,EAAA;YAZA,qBAAA,eAAA,GAAA,IAAA,CAAA,eAAA,CAAA,IAAA,mBAAA,IAAA,CAAA,SAAA,EAAA,CAAA;YAaM,IAAN,eAAA,EAAA;gBACU,IAAV,CAAA,qBAAA,CAAA,eAAA,CAAA,CAAA;aACA;SACA;aACA,IAAA,IAAA,CAAA,eAAA,EAAA;YACA,qBAAA,eAAA,GAAA,IAAA,CAAA,eAAA,CAAA,IAAA,mBAAA,IAAA,CAAA,SAAA,EAAA,CAAA;;;;;KAVA,CAAA;;;;;;;;KAKA,CAAA;;;;;IAuBA,OAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,OAlBc,EAkBd;QAAA,IAAA,KAAA,GAAA,IAAA,CAbA;QAcA,OAAA,CAAA,gBAAA,CAAA,UAAA,MAAA,EAAA,EAAA,OAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,EAAA,MAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,OAAA,CAAA,kBAAA,CAAA,UAAA,MAAA,EAAA,EAAA,OAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,EAAA,MAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACA,OAAA,CAAA,kBAAA,CAAA,UAAA,MAAA,EAAA;;;;;KAhBA,CAAA;;;;;IAIA,OAAA,CAAA,SAAA,CAAA,qBAAA,GAAA,UAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CASA;QAaA,OAAA,CAAQ,gBACI,CADZ,UAAA,MAAA,EAAA;YAEA,IAAA,OAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;gBACA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;aAEA;iBACA;;;;;KAnBA,CAAA;;;;;;;;;;;;;;IASA,OAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAA,WAAA,EAAA,SAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QAeA,IAAA,WAde,EAcf;YAeA,IAAA,KAAA,CAAA,OAAA,CAAA,WA5B2B,CAAK,IAAI,WA4BpC,YAAA,GAAA,EAAA;gBA5BA,EAAA,WAA+C,GAA/C,OAAA,CAAA,UAAgD,KAAhD,EAAA,EAAA,OAAA,KAAA,CAAA,YAAA,CAAA,KAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA;aA6BA;iBACA;gBACA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;oBACA,IAAA,WAAA,CAAA,KAAA,CAAA,IAAA,IAAA;;;;;;;;;;;IAYA,OAAA,CAAA,SAAA,CAAA,YAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;QA/BA,IAAA,KAAA,EAAA;YACQ,KAgCR,CAAA,KAhCmB,CAgCnB,MAhCyB,CAgCzB,CAhC2B,OAgC3B,CAhC4B,UAAA,KAgC5B,EAhCA,EAgCA,KAhCiD,CAgCjD,SAAA,CAAA,eAAA,CAAA,KAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA;SACA;;;;;IA9BA,EAAA,IAAA,EAAAT,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA;CAmCA,CAAA;;;;AAIA,OAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IAjCA,EAAA,IAAA,EAAAK,6BAAA,GAAA;IAmCA,EAAA,IAAA,EAAAH,6BAlCkB,GAkClB;IACA,EAAA,IAAA,EAAAD,wBAlCc,GAkCd;IACA,EAAA,IAAA,EAAAO,sBAAA,GAAA;CAJA,CAAA,EAAA,CAAA;ADxLA,OAAA,CAAA,cAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA,IAAA,iBAAA,IAAA,YAAA;;;;;;;;KAIA;;;;;IAOA,iBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAU,OAFC,EAEX;QACA,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,CAAA;QAFA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA;QAIA,IAAA,IAAQ,CAAR,iBAAA,EAAA;YACA,qBAAA,UAAA,GAAA,IAAA,CAAA,yBAAA,IAAA,IAF2D,CAE3D,iBAAA,CAAA,cAAA,CAAA;YACA,IAAA,OAAA,CAAc,kCAAd,CAAA,EAAA;gBACA,IAAA,IAAA,CAAA,UAAA;oBAFA,IAAA,CAAA,UAAA,CAAA,OAAA,EAAA,CAAA;gBAGA,IAAU,IAAI,CAFC,gCAEf,EAAA;oBACA,qBAAA,YAAA,GAAA,UAAA,CAAA,GAAA,CAAAD,yBAAA,CAAA,CAAA;oBACA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,gCAAA,CAAA,MAAA,CAAA,YAAA,CAAA,QAAA,CAAA,CAAA;iBAEA;qBACA;oBAEA,IAAA,CAAA,UAFY,GAEZ,IAAA,CAAA;iBACW;aAKX;YACA,qBAAA,wBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAAA,wBAAA;;;;SAIA;KACA,CAAA;;;;IAFA,iBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;QAKA,IAJQ,IAIR,CAAA,UAJmB;YAKnB,IAAA,CAAA,UAAA,CAAA,OAAA,EAAA,CAAA;;;CAzCA,EAAA,CAAA,CAAA;;IAuCA,EAAA,IAAA,EAAAP,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAC,QAAD,EAAA,qBAAA,EAAA,EAAA,EAAA;CAOA,CAAA;;;;AAIA,iBAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IACA,EAAA,IAAA,EAAAD,8BAAA,GAAA;CACA,CAAA,EAAA,CAAA;AACA,iBAAA,CAAA,cAAA,GAAA;;IDhIA,2BAAA,EAAA,CAAA,EAAA,IAAA,EAAAD,mBAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;IAgBA,SAAA,cAAA,CAAA,SAAA,EAAA,OAAA,EAAA,KAAA,EAAA,KAAA,EAAA;;;;QAEG,IAUH,CAVG,KAAsB,GAUzB,KAAA,CAAA;;;;;;;;;;;;;;;;;;IAaA,MAAA,CAAA,cAAA,CAAA,cAAA,CAAA,SAAA,EAAA,MAAA,EAAA;;;;QAAA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,KAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgFA,SAAA,OAAA,CAAA,cAAA,EAAA,SAAA,EAAA,QAAA,EAAA;;;;;KAUA;IAMA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,SAAA,EAAA,cAAY,EAAZ;;;;QAwBA,GAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,UAAA,CAAA,EAAA;;;;;QAxBA,GAAA,EAAA,UAAA,EAAA,EAAA;YACA,IAAAQ,uBAAA,EAAA,IAAA,EAAA,IAAA,IAAA,IAAA,OAAA,EAAA,KAAA,UAAA,EAAA;;gBAEQ,IAAR,CAAA,OAAA,MAAA,OAAA,CAAA,IAAA,CAAA,EAAA;oBACA,OAAA,CAAA,IAAA,CAAA,2CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,GAAA,IAAA;;;;YA3BG,IA+BH,CAAA,UAAA,GAAA,EAAA,CAAA;;;;;IAqBA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,SAAA,EAAA,eAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,KAAA,EAAA;;;;;;;SAOA;;;KAAA,CAAA,CAAA;;;;;IAKA,OAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAQ,OAAR,EAAA;QACA,IAAA,SAAA,IAxCe,OAAC,EAwChB;;YAvCA,qBAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA,CAAA,YAAA,CAAA;YAyCA,IAAA,CAAA,IAAA,CAAA,OAAA,IAAA,KAAA,EAAA;gBAEA,IAAA;oBACA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;iBACA;gBACA,OAAA,CAAA,EAAA;;;;SAIA;KACA,CAAA;;;;IAGA,OAAA,CAAA,SAAA,CAAA,SAAA,GAAA,YAAA;QACA,IAAA,IAAA,CAAA,OAAA,EAAA;;;;;KAxCA,CAAA;;;;;IAoDA,OAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,OAAA,EAAA;QAAA,IAAA,KAAA,GAAA,IAAA,CAAA;QACA,qBAAA,YAAA,GAAA,EAAA,CAAA;QACA,OAAA,CAAA,gBAAA,CAAA,UAAA,IAAA,EAAA,qBAAA,EAAA,YAAA,EAAA;YA7CA,IAAA,IAAA,CAAiB,aAAjB,IAAA,IAAA,EAAA;gBA8CY,qBAAZ,IA7CuC,GA6CvC,KAAA,CAAA,cA7C6D,CAAC,kBA6C9D,CAAA,KAAA,CAAA,SAAA,EAAA,IAAA,cAAA,oBAAA,IAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA;gBACA,qBAAA,KAAA,GAAA,IAAA,eAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;gBA7CA,YAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;aA8CA;iBAbA,IA/BiB,YA+BjB,IAAA,IA/BqC,EA+BrC;gBAeY,KAAZ,CAAA,cAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,CA7C8B;aA8C9B;iBACA;gBACA,qBAAA,IAAA,KAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,qBAAA,CAAA,EAAA,CAAA;gBAEA,KAAA,CAAA,cA7Ca,CA6Cb,IAAA,CA7CoB,IAAI,EA6CxB,YAAA,CAAA,CAAA;gBACU,qBAAV,KA7C2B,GAAc,IA6CzC,eA7CgD,CAAa,IA6C7D,oBAAA,IAAA,EAAA,CAAA;gBACA,YAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;aAZO;SAeP,CAAA,CAAA;QACA,KAAA,qBAAA,CAAA,GA7CgC,CA6ChC,EAAA,CAAA,GAAA,YAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;YACM,IAAN,CAAA,cAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA,YAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA;SAbE;QAgBE,KAAJ,qBAAA,CAAA,GA7CkC,CAAC,mBA6CnC,IAAA,GAAA,IAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,EAAA,CAAA,EAAA,EAAA;YACM,qBA7CM,OAAA,IACkC,IAAC,CAAI,cAAC,CAAc,GAAC,CAAG,CA4CtE,CAAA,CAAA,CAAA;YAEM,OAAO,CA7CC,OAAC,CAAO,KA6CtB,GAAA,CA7CuB,CA6CvB;YACA,OAAA,CAAA,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;SACA;;;;;;;;;;;IAWA,OAAA,CAAA,SAAA,CAAA,cAAA,GAAA,UAAA,IAjDmB,EAiDnB,MAAA,EAAA;QACA,IAAA,CAAA,OAAA,CAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA;;;;;IA/CA,EAAA,IAAA,EAAAN,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA;CAoDA,CAAA;;;;AA/CO,OAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAoDA,EAAA,IAAA,EAAAD,8BAAA,GAnD0B;IAoD1B,EAAA,IAAA,EAAAI,yBAnDmB,GAmDnB;IACA,EAAA,IAAA,EAAAE,6BAAA,GAnD0B;CAoDzB,CAAD,EAAC,CAnDC;AAsDF,OAAA,CAAA,cAwBA,GAxBA;;;;;AA6BA,IAAA,eAAA,IAAA,YAAA;;;;;IAIA,SAAA,eAAA,CAAA,MAAA,EAAA,IAAA,EAAA;;;KAeA;;CAnBA,EAAA,CAAA,CAAA;;;;AAwBA,IAAA,KAAA,GAAA,OAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD7MA,IAAA,IAAA,IAAA,YAAA;;;;;IAJA,SAAA,IAAA,CAAA,cAAA,EAA4D,WAA5D,EAAA;QACU,IAAV,CAAA,cAAA,GAAA,cAAA,CAAA;QAII,IAAI,CAAC,QAAT,GAAA,IAAA,WAAA,EAAA,CAAA;QACA,IAAA,CAAA,gBAAA,GAAA,IAAA,CAAA;;;;;KAKA;;;;;;;;;SAQA;;;KAAA,CAAA,CAAA;;;;;;;;;;SASA;;;KAAA,CAAA,CAAA;;;;;;;;;YADA,IAAA,CAAA,WAAA,EAAA,CAAA;SAUA;;;KAAA,CAAA,CAAA;;;;IAIA,IAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;QACA,IAAA,IAAA,CAAA,QATe,CASf,SAAA,EATgB;YAUhB,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA;gBACA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA,CAAA;gBACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA;gBACA,IAAA,IAAA,CAAA,gBAAA,EAAA;oBATA,IAAA,CAAA,YAAA;wBAUA,IAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA;iBACA;aACA;SACA;aACA;YACA,IAAA,CAAA,IAAA,CAAA,YAAA,EAAA;gBACA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA,CAAA;gBACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA;gBACA,IAAA,IAAA,CAAA,gBAAA,EAAA;oBACA,IAAA,CAAA,YAAA;;iBARA;aAUA;SACA;;;CAvDA,EAAA,CAAA,CAAA;;IAgDA,EAAA,IAAA,EAAAL,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA;CAYA,CAAA;;;;AAIA,IAAA,CAAA,cAXW,GAWX,YAAA,EAXuB,OAAA;IAYvB,EAAA,IAAA,EAAAD,8BAAA,GAAA;IACA,EAAA,IAAA,EAAAI,yBAXe,GAWf;CACC,CAAD,EAAC,CAXC;AAcF,IAAA,CAAA,cAAA,GAAA;;;IA2BA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAL,mBAAA,EAAA,EAAA;CAAA,CAAA;;;;AAGA,IAAA,WAAA,IAAA,YAAA;;QDlNA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;;;;CCkNA,EAAA,CAAA,CAAA;;;;;;;;ADlMA,IAAA,UAAA,IAAA,YAAA;;;;;;;;QAMA,IAAA,CAAA,QAAA,GAAA,KAAA,CAAA;KACA;;;;;;QAMA,IAAA,CAAA,iBAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;KACA,CAAA;;;;;;;KAOA,CAAA;;;;;IAIA,UAAA,CAAA,SAAA,CAAA,YAAU,GAAV,UAXW,OAAC,EAWZ;QACA,IAAA,OAAA,IAAA,CAAA,IAAA,CAAA,QAAA,EAAA;YACA,IAAA,CAAA,MAAA,EAAA,CAAA;SACA;aAEA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,QAAA,EAAA;;;;;CA9BA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,IAAA,QAAA,IAAA,YAAA;IAuFA,SAAA,QAAA,GAAA;;;;;KA9DA;IAKA,MAAA,CAAA,cAAA,CAAA,QAAA,CAAA,SAAA,EAAA,UAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,QAAA,EAAA;;;;;SAKA;;;KAAA,CAAA,CAAA;;;;;;;;;;;IAWA,QAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAA,IAAA,EAAA;;;;;;;;;;;IAWA,QAAA,CAAA,SAAA,CAAA,UAAA,GAAA,UAAA,KAAA,EAAA;QACA,qBAAA,OAAA,GA7BmC,KA6BnC,IAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAM,CAAN,iBAAA,GAAA,IA7B+B,CA6B/B,iBAAA,IAAA,OAAA,CAAA;QACA,IAAA,CAAA,mBAAA,EAAA,CAAA;QACI,IAAJ,IAAA,CAAA,mBAAA,KAAA,IAAA,CAAA,UAAA,EAAA;YACA,IAAA,CAAA,mBAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA;;;;;KA3BA,CAAA;;;;;IAqCA,QAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,UAAA,UAAA,EAAA;QACA,IAAA,IAAA,CAAA,aAAA,IAAA,UAAA,KAAA,IAAA,CAAA,YAAA,EAAA;YACA,IAAA,CAAA,YAAA,GAAA,UAAA,CAAA;YACA,KAAA,qBAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;;gBA/BA,WAAA,CAAA,YAAA,CAAA,UAAA,CAAA,CAAA;aAiCA;SACA;;;CA9EA,EAAA,CAAA,CAAA;;IAgDA,EAAA,IAAA,EAAAE,uBAAA,EAAC,IAAD,EAAA,CAAA,EAAC,QAAD,EAAA,YAAA,EAAA,EAAA,EAAA;CAEA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA0GA,IAAA,CA/DG,KAAa,GA+DhB,IAAA,UAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA;;;;;;;;;IA1DA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAC,EAAD,CAAC,EAAD,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA;CAkEA,CAAA;;;;AA7DO,YAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAkEA,EAAA,IAAA,EAAAD,8BAAA,GAAA;IACA,EAAA,IAAA,EAAAI,yBAAA,GAAA;IAGA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAC,kBAAA,EAAA,EAAA,EAAA;CAtEA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2HA,SAAA,eAAA,CAAA,aAvFyB,EAuFzB,WAvFqC,EAuFrC,QAAA,EAAA;QACA,QAAA,CAAA,WAAA,CAAA,IAAA,UAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;;;;;IArFA,EAAA,IAAA,EAAAJ,uBAAA,EAAA,IAAA,EAAA,CAAC,EAAD,QAAA,EAAA,mBAAA,EAAA,EAAA,EAAA;CA0FA,CAAA;;;;;ID1TA,EAAA,IAAA,EAAAD,8BAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,IAAA,QAAA,IAAA,YAAA;;;;;;;KAKA;;;;;;;;;;;;;;;;;;IAcA,QAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAQ,KAAR,EAAA,UAAA,EAAA,EAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,UAAA,CAAA,EAAA,CAAA;;;;IAKA,QAAA,CAAA,SAAA,CAAA,WAAA,GAAA,YAAA;;;;QANA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA;KAWA,CAAA;;;;;;;KAPA,CAAA;;;;;IAkBA,QAAA,CAAA,SAAA,CAAA,aAAA,GAAA,UAAA,IAAA,EAAA;;YAZA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA;YACQ,IAaR,CAAA,WAAA,CAbyB,MAazB,EAAA,CAAA;SACA;;;CA3CA,EAAA,CAAA,CAAA;;IAgCA,EAAA,IAAA,EAAAC,uBAAA,EAAC,IAAD,EAAA,CAAA,EAAC,QAAD,EAAA,YAAA,EAAA,EAAA,EAAA;CAgBA,CAAA;;;;AAIA,QAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;IAGA,EAAA,IAAA,EAAA,cAAA,GAAA;CAHA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAcA,IAAA,CAAA,KAAA,GAAA,KAA2C,CAA3C;QAyCA,IAAA,SAxCmB,GAwCnB,CAxCmB,KAAQ,CAwC3B,MAAA,CAxC4B,KAwC5B,CAAA,CAAA,CAAA;QACA,QAAA,CAAA,OAAA,CAAA,SAAA,GAAA,GAAA,GAAA,KAAA,GAAA,KAAA,EAAA,IAAA,UAAA,CAAA,aAAA,EAAA,QAAA,CAAA,CAAA,CAAA;;;;;IAtCA,EAAA,IAAA,EAAAA,uBAAA,EAAA,IAAC,EAAD,CAAC,EAAD,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA;CA2CA,CAAA;;;;AAIA,YAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA;;IDzKA,EAAA,IAAA,EAAAG,yBAAA,GAAA;;;CCyKA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KDzHA;IAKA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,SAAA,EAAA,SAAA,EAAA;;;;;QAAA,GAAA,EAAA,UAAA,CAAA,EAAA;;;;aAIA;SACA;;;KAAA,CAAA,CAAA;;;;IAIA,OAAA,CAAA,SAAA,CAAA,SAAO,GAAP,YAAA;QACA,IAAA,IAAA,CAAA,OAAA,EAAA;YACA,qBAAA,OAAA,GAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;;;;;KAPA,CAAA;;;;;;;;;;;;;;;;;QAYA,IAAA,EAAA,GAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,IAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAA2C,IAA3C,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QAiBA,KAAA,GAAA,KAhBQ,IAgBR,IAhByB,IAgBzB,IAAA,GAhB4B,EAgB5B,GAAA,KAAA,GAAA,IAhBkD,GAgBlD,KAAA,CAAA;QACA,IAAA,CAAA,SAAA,CAAA,eAAA,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,EAAA,IAAA,oBAAA,KAAA,EAAA,CAAA;;;;;IAdA,EAAA,IAAA,EAAAH,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA;CAmBA,CAAA;;;;AAdO,OAAP,CAAA,cAAO,GAAyD,YAAhE,EAAA,OAAA;IAmBA,EAAA,IAAA,EAAAE,6BAAA,GAAA;IACA,EAAA,IAAA,EAAAD,wBAAA,GAAA;;CDlGA,CAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAA,gBAAA,IAAA,YAAA;;;;;;;IAUA,MAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,iBAFG,EAEH;;;;;;QAAA,GAAA,EAAA,UAAA,OAAA,EAAA,EAAA,IAAA,CAAA,uBAAA,GAAA,OAAA,CAAA,EAAA;;;KAAA,CAAA,CAAA;;;;;IAMA,gBAAA,CAAA,SAAA,CAAA,WAAA,GAAA,UAAU,OAAV,EAAA;QAEA,IAAA,IAAA,CAAA,QAAA,EAAA;YACA,IAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;;QADA,IAAA,IAAA,CAAA,gBAAA,EAAA;YACQ,IAER,CAAA,QAAA,GAAA,IAF2B,CAE3B,iBAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,uBAAA,CAAA,CAAA;SACA;;;CAtBA,EAAA,CAAA,CAAA;;IAsBA,EAAA,IAAA,EAAAD,uBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAC,EAAD,oBAAA,EAAA,EAAA,EAAA;CAKA,CAAA;;;;AAIA,gBAAA,CAAA,cAJuB,GAIvB,YAAA,EAJmC,OAAA;IAKnC,EAAA,IAAA,EAAAD,8BAAA,GAAA;CACC,CAAD,EAAC,CAJC;;IDvEF,yBAAA,EAAA,CAAA,EAAA,IAAA,EAAAD,mBAAA,EAAA,EAAA;;;;;;;;;;;;;;;AA6CA,IAAA,iBAAA,GAAA;IACE,OAAO;IACP,iBAAF;IACE,OAAF;IACE,IAAF;IACE,gBAAF;IACE,OAAF;IACA,QAAA;;;IAGA,QAAA;;CDvDA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KDiCA,CAAA;;;;;IAKA,kBAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,YAAA,EAAA,EAAA,YAAA,CAAA,WAAA,EAX+D,CAW/D,EAX4E,CAW5E;;;;;;;;;;;;;;;;;;KAeA,CAAA;;;;;IAKA,eAAA,CAAA,SAAA,CAAA,OAAA,GAAA,UAAA,YAAA,EAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAA,SAAA,IAAA,YAAA;;;;IAHA,SAAA,SAAA,CAAA,IAAsE,EAAtE;QACU,IAAV,CAAA,IAAA,GAAA,IAAA,CAAA;QAEA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA;;;;QAwBA,IAAA,CAAA,SAAA,KAAA,IAAA,EAAA,CAAA;KACA;;;;;;;;KAaA,CAAA;;;;;IAKA,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,GA1BW,EA0BX;QACA,IAAM,CAAN,IAAA,CAAA,IAAA,EA1BkB;YA2BlB,IAAA,GAAA,EAAA;gBAEA,IA1BgB,CA0BhB,UA1B2B,CA0B3B,GAAA,CAAA,CAAA;aACA;YACM,IAAN,CAAA,oBAAA,GAAA,IAAA,CAAA,YA1BuC,CAAC;YA2BxC,OAAA,IAAA,CAAA,YAAA,CAAA;SAEA;QACA,IAAM,GAAN,KAAA,IA1BkB,CA0BlB,IAAA,EAAA;YACA,IAAA,CAAA,QAAA,EAAA,CAAA;YAEQ,OAAR,IAAA,CAAA,SA1BU,mBA0BV,GAAA,EAAA,CAAA;SACA;QACA,IAAA,IAAA,CAAA,YAAA,KAAA,IAAA,CAAA,oBAAA,EAAA;;;;;KAxBA,CAAA;;;;;;QAOA,IAAA,KAAA,GAAA,IAAA,CAAA;;;;KAAA,CAAA;;;;;IAsCA,SAAA,CAAA,SAAA,CAAA,eAhCa,GAgCb,UAAA,GAAA,EAAA;QACA,IAAAD,wBAAA,CAAA,GAAA,CAAA,EAAA;YAEA,OAAA,gBAAA,CAAA;SACA;;;;QA9BA,MAAA,wBAAA,CAAA,SAAA,EAAA,GAAA,CAAA,CAAA;KAmCA,CAAA;;;;IAIA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QACA,IAAA,CAAA,SAAA,CAAA,OAAA,oBAAA,IAAA,CAAA,aAAA,GAAA,CAAA;;;;;;;;;;;IAWA,SAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,UAAA,KAAA,EAAA,KAAA,EAAA;;YArCA,IAAA,CAAA,YAAA,GAAA,KAAA,CAAA;YACQ,IAAA,CAsCR,IAAA,CAtCc,YAsCd,EAAA,CAAA;SACA;;;CA3GA,EAAA,CAAA,CAAA;;IAuEA,EAAA,IAAA,EAAAD,kBAAA,EAAA,IAAA,EAAC,CAAD,EAAC,IAAD,EAAC,OAAD,EAAA,IAAA,EAAA,KAAA,EAAA,EAAA,EAAA;CAyCA,CAAA;;;;;;;;;;;;;;;;;;;;;ADtKA,IAAA,aAAA,IAAA,YAAA;IAAA,SAAA,aAAA,GAAA;;;;;;IAIA,aAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA;QACI,IAAJ,CAAA,KAAA;YACA,OAAA,KAAA,CAAA;;YAFA,MAAA,wBAAA,CAAA,aAAA,EAAA,KAAA,CAAA,CAAA;SAIA;QACA,OAAA,KAAA,CAAA,WAAA,EAAA,CAAA;;;CATA,EAAA,CAAA,CAAA;;IAQA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAC,EAAD,WAAA,EAAA,EAAA,EAAA;CASA,CAAA;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAA,aAAA,IAAA,YAAA;IAAA,SAAA,aAAA,GAAA;;;;;;IAIA,aAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA;QAEI,IAAJ,CAAA,KAAA;YACA,OAAA,KAAA,CAAA;;YAjBA,MAAA,wBAAA,CAAA,aAAA,EAAA,KAAA,CAAA,CAAA;SAmBA;QACA,OAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,aAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;;;CAVA,EAAA,CAAA,CAAA;;IANA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAC,EAAD,WAAA,EAAA,EAAA,EAAA;CAwBA,CAAA;;;;;;;;;;AAoBA,IAAA,aAAA,IAAA,YAAA;IAAA,SAAA,aAAA,GAAA;;;;;;IAIA,aAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA;QACI,IAAJ,CAAA,KAAA;YACA,OAAA,KAAA,CAAA;;YAhCA,MAAA,wBAAA,CAAA,aAAA,EAAA,KAAA,CAAA,CAAA;SAkCA;QACA,OAAA,KAAA,CAAA,WAAA,EAAA,CAAA;;;CATA,EAAA,CAAA,CAAA;;IAtBA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAC,EAAD,WAAA,EAAA,EAAA,EAAA;;;;;ADlFA,aAAA,CAAA,cAAA,GAA+B,YAA/B,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;AAEA,IAAA,iBAAA,GAAA,EAAA,CAAA;AACA,iBAAiB,CAAC,OAAlB,GAAA,CAAA,CAAA;AAEA,iBAAA,CAAA,OAAA,GAAA,CAAA,CAAA;;;;;;;KAwCA;;;;;;;;IApBA,eAAA,CAAA,MAAA,GAAA,UAAA,GAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA;QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAA,GAAA,EAAA,CAAA,EAAA;QACA,IAAA,oBAAA,GAAA,IAAA,CAAA,oBAHa,EAGb,qBAAA,GAAA,IAAA,CAAA,qBAAA,EAAA,qBAAA,GAAA,IAAA,CAAA,qBAAA,EAAA,QAAA,GAAA,IAAA,CAAA,QAAA,EAAA,EAAA,GAAA,IAAA,CAAA,gBAAA,EAAA,gBAAA,GAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,GAAA,EAAA,CAAA;QACA,qBAAA,OAAA,GAAA;YAEQ,oBAAR,EAAA,oBAAA;YACM,qBAAN,EAAA,qBAAA;YACM,qBAAN,EAAA,qBAAA;YACA,KAAA,EAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,WAAA,EAAA;SACA,CAAA;QACA,IAAA,KAAA,IAAA,iBAAA,CAAA,QAAA,EAAA;YACA,OAAA,CAAA,QAAA,GAAA,OAAA,QAAA,IAAA,QAAA,GAAA,QAAA,GAAA,SAAA,CAAA;YAIA,OAAA,CAAA,eAAA,GAAA,gBAAA,GAAA,QAAA,GAAA,MAAA,CAAA;SAGA;;KAEA,CAAA;IACA,OAAA,eAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,IAAA,kBAAA,GAAA,qGAAA,CAAA;AACA,IAAA,eAAA,GAAA;;IAEA,UAAA,EAAA,qBAAA,CAAA,OAAA,CAAA;QACI,cAAc,CAHC,MAGnB,EAHoB,CAGpB,CAH6B;QAI7B,aAAA,CAAA,OAAA,EAAA,CAAA,CAAA;QACA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA;QACI,cAAc,CAHC,MAAC,EAAO,CAAA,CAAE;QAIzB,cAAc,CAHC,QAAQ,EAAE,CAG7B,CAH+B;QAI/B,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA;KACA,CAAA,CAAA;IACA,OAAA,EAAA,qBAAA,CAH+B,OAG/B,CAAA;QACI,cAAc,CAHC,MAGnB,EAH0B,CAAE,CAG5B,EAAA,cAAA,CAAA,OAAA,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA;QACA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA;KACA,CAAA,CAAA;IAEE,YAAF,EAAA,qBAAA,CAAA,OAAA,CAAA;QAEA,cAAA,CAAA,MAAA,EAAA,CAHS,CAIH,EADN,aAAA,CAAA,OAFgB,EAEhB,CAAA,CAAA,EAAA,aAAA,CAAA,SAAA,EAAA,CAAA,CAAA;QAEA,cAAA,CAAA,KAAA,EAAA,CAAA,CAHS;KAKT,CAAA,CAAA;IACA,QAAA,EAAA,qBAAA,CAAA,OAAA,CAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,aAAA,CAAA,OAAA,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEA,OAAA,EAAA,qBAAA,CAAA,OAAA,CAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,aAAA,CAAA,OAAA,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;IAEE,KAAF,EAAA,qBAAA,CAHU,OAGV,CAAA,CAAA,cAAA,CAHuD,MAGvD,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,IAAI,EAHE,qBAAA,CAAsB,OAG9B,CAAA,CAAA,cAHqD,CAAA,MAGrD,EAAA,CAAA,CAAA,EAAA,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACA,IAAA,YAAA,GAAA;;IAEE,MAHM,EAGR,qBAAA,CAAA,cAAA,CAAA,MAAA,EAHsD,CAAE,CAAC,CAGzD;IACE,IAAF,EAAA,qBAH6B,CAG7B,cAH4C,CAG5C,MAH6C,EAAQ,CAAA,CAAE,CAAC;IAItD,GAAF,EAAA,qBAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAH+C,CAG/C,CAHuD;IAIrD,MAAF,EAAA,qBAAA,CAAA,aAAA,CAAA,OAHoD,EAAG,CAGvD,CAAA,CAAA;IACE,KAAF,EAAA,qBAH8B,CAG9B,aAH+B,CAAc,OAAO,EAAE,CAAC,CAGvD,CAAA;IACE,IAAF,EAAA,qBAH6B,CAG7B,cAH4C,CAG5C,OAHmD,EAAG,CAGtD,CAAA,CAAA;IACE,GAAF,EAAA,qBAAA,CAAA,cAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;IAEE,MAAF,EAAA,qBAAA,CAAA,aAAA,CAAA,OAAA,EAH4C,CAAY,CAGxD,CAAA;IACE,GAAF,EAAA,qBAAA,CAAA,aAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;IAEE,IAAF,EAAA,qBAAA,CAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAH4C;IAI1C,GAAF,EAAA,qBAAA,CAHQ,cAGR,CAH+B,KAG/B,EAH8C,CAG9C,CAHqD,CAAA;IAInD,IAAF,EAAA,aAAA,CAAA,aAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACE,GAAF,EAAA,aAAA,CAHQ,qBAGR,CAHuB,YAGvB,CAAA,cAHqE,CAAE,MAGvE,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;IACE,IAAF,EAAA,aAAA,CAAA,aAAA,CAAA,qBAHyD,CAGzD,YAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACE,GAAF,EAAA,aAAA,CAHQ,qBAGR,CAHuB,YAGvB,CAAA,cAHqE,CAAE,MAGvE,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;IACE,IAAF,EAAA,qBAH6B,CAG7B,cAH4C,CAG5C,MAAA,EAAA,CAHsD,CAAA,CAAE;;;;IAOtD,IAAF,EAAA,aAAA,CAAA,qBAAA,CAAA,cAH0D,CAAC,QAG3D,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,GAAF,EAAA,qBAAA,CAAA,cAAA,CAAA,QAAA,EAAA,CAH+C,CAG/C,CAHyD;;;;IAOvD,KAHK,EAGP,qBAAA,CAAA,cAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA;IACE,MAAF,EAAA,qBAAA,CAAA,aAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;IACE,KAHK,EAGP,qBAAA,CAAA,aAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;IACE,IAAI,EAHE,qBAAA,CAAsB,aAG9B,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;;IAEE,GAAG,EACC,kBADN,CAAA,qBAAA,CAAA,YAAA,CAAA,cAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;;IAGE,GAAG,EAHE,cAGP,CAAA,MAHO,CAAsB;IAI3B,IAAI,EAHE,qBAAA,CAAsB,EAG9B,CAAA;;IAEE,GAAF,EAAA,qBAAA,CAAA,EAHU,CAAsB;;;;;;CAShC,CAAA;;;;;AAKA,SAAA,aAAA,CAAA,KAAA,EAAA;;;;;CAKA;;;;;;;CAOA;;;;;;;;;;;;;;;;CAgBA;;;;;;;IAOA,qBAAA,OAAA,GAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,KAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA;IACA,OAAA,UAAA,IAAA,EAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA,SAAA,aAAA,CAAA,IAAA,EAAA,GAAA,EAAA;IAgCA,qBAAA,MAAA,GAAA,EAAA,CAAA;IACA,IAAA,GAAA,GAAA,CAAA,EAAA;QAEA,MAAA,CAAA,IAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA,OAAA,GAAA,QAAA,CAAA;KACA;;;;;CAKA;;;;;;;CAOA;;;;;;;;;;;;;;;AAiBA,SAAA,aAAA,CAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA;IAEE,qBAAF,EAAA,GAAA,eAAA,CAAA,MAAA,CAAA,CAAA;IACA,IAAI,EAAJ;QACI,OAAJ,EAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;IACA,qBA1CuB,QA0CvB,GA1C6B,MA0C7B,CAAA;IAEA,qBAAA,KAAA,GAAA,oBAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA;IACA,IAAI,CAAJ,KAAA,EAAA;QACA,KAAA,GAAA,EAAA,CAAA;QACA,qBAAA,KAAA,GAAA,KAAA,CAAA,CAAA;QACA,kBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;QACA,qBAAA,OAAA,GA1C4B,MA0C5B,CAAA;QACA,OAAA,OAAA,EAAA;YA1CA,KAAa,GAAb,kBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;YA2CA,IAAQ,KAAK,EAAb;gBACQ,KAAR,GAAA,KAAA,CA1CkB,MA0ClB,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACA,OAAA,KAAA,KAAA,CAAA,GAAA,EAAA,EAAA,CAAA;aACA;iBAEA;gBACA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;gBAxCS,OAAO,GA0ChB,IAAA,CA1C6B;aA2C7B;SACA;QACA,oBAAA,CAAA,GAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;KACA;;;;;CAKA;;;;;;;;;;;;;;;;;IDtQA,aAAA,CAAA,MAAA,GAAA,UAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAA,YAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA;IAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAA,GAAA,IAAA,CAAA,EAAA;IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,gBAAA,GAAA,KAAA,CAAA,EAAA;IACA,IAAI,KAAJ,IAAA,IAAA;QACA,OAAA,IAAA,CAAA;;IAGE,KAAF,GAAA,OAAA,KAAA,KAAA,QAAA,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,GAAA,KAAA,CAAA;IACE,IAAF,OAAA,KAAA,KAVM,QAUN,EAAA;QACM,MAAN,wBAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;;IAEA,qBAAA,MAAA,GAAA,SAAA,CAAA;IACA,qBAAA,WAAA,GAAA,SAAA,CAAA;IACA,qBAAA,WAAA,GAAA,SAAA,CAAA;IACA,IAAA,KAAA,KAAA,iBAAA,CAAA,QAAA,EAAA;;QAGI,MAAJ,GAAA,CAAA,CAAA;QACI,WAAJ,GAVkB,CAUlB,CAAA;QACA,WAAA,GAVgB,CAUhB,CAAA;KACA;IACA,IAAI,MAAJ,EAAA;QACA,qBAAA,KAAA,GAViC,MAAM,CAAC,KAUxC,CAAA,qBAAA,CAAA,CAAA;QACA,IAAA,KAAA,KAAA,IAAA,EAAA;YACQ,MAVM,IAUd,KAAA,CAAA,MAAA,GAAA,6CAAA,CAAA,CAAA;SACA;QACA,IAAA,KAAA,CAAA,CAAA,CAAA,IAAA,IAAA,EAAA;YACQ,MAVM,GAUd,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SACA;QACA,IAAA,KAAA,CAAA,CAAA,CAAA,IAAA,IAAA,EAAA;YACA,WAAA,GAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAEA;QACI,IAAJ,KAAA,CAAA,CAAA,CAAA,IAAA,IAAwB,EAVE;YAW1B,WAAA,GAAA,iBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SACA;KACA;IACA,OAAA,eAVsB,CAUtB,MAAA,mBAAA,KAAA,GAAA,MAAA,EAAA,KAAA,EAAA;QACA,oBAAA,EAAA,MAAA;QACA,qBAAA,EAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAA,WAAA,IAAA,YAAA;;;;;;;;;;;;IAsBA,WAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,MAhBsB,EAgBtB;QACA,OAAA,YAAA,CAAA,WAAA,EAAA,IAAA,CAAA,OAAA,EAAA,KAAA,EAAA,iBAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;;;CAvBA,EAAA,CAAA,CAAA;;IASA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA;CAmBA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAA,WAAA,IAAA,YAAA;;;;;;;;;;;;IA2CA,WAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,MArCsB,EAqCtB;QACA,OAAA,YAAA,CAAA,WAAA,EAAA,IAAA,CAAA,OAAA,EAAA,KAAA,EAAA,iBAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;;;CA5CA,EAAA,CAAA,CAAA;;IASA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,SAAA,EAAA,EAAA,EAAA;CAwCA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA,IAAA,YAAA,IAAA,YAAA;;;;;;;;;;;;;;IAsEA,YAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,YAAA,EA5D4B,aA4D5B,EAAA,MAAA,EAAA;QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAA,GAAA,KAAA,CAAA,EAAA;QA5D4B,IAA5B,aAAA,KAAA,KAAA,CAAA,EAA4B,EAAA,aAA5B,GAAA,KA4DA,CA5DA,EAAA;QA6DA,OAAA,YAAA,CAAA,YAAA,EAAA,IAAA,CAAA,OAAA,EAAA,KAAA,EAAA,iBAAA,CAAA,QAAA,EAAA,MAAA,EAAA,YAAA,EAAA,aAAA,CAAA,CAAA;;;CAvEA,EAAA,CAAA,CAAA;;IAaA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAC,IAAD,EAAA,UAAA,EAAA,EAAA,EAAA;CA+DA,CAAA;;;;;;CAoBA,CAAA,EAAA,CAAA;;;;;AAKA,SAAA,iBAAA,CAAA,IAAA,EAAA;IACA,qBAAA,MAAA,GAAA,QAAA,CAAA,IAAA,CAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD7KA,IAAA,QAAA,IAAA,YAAA;;;;;;;;;;;;IAeA,QAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,YAAA,CAAA,EAAA;QACA,qBAAA,IAAA,CAAA;QAEI,IAAI,OAPO,CAOf,KAPqB,CAOrB,IAAA,KAAA,KAAA,KAAA;YACM,OAPO,IAOb,CAPa;QAQb,IAAA,OAAA,KAAA,KAAA,QAAA,EAAA;YAPA,KAAW,GAAX,KAAA,CAAgB,IAAhB,EAA0B,CAAK;SAQ/B;QACA,IAAA,MAAA,CAAA,KAAA,CAAA,EAAA;YAPA,IAAA,GAAA,KAAsB,CAAtB;;;;;;;;;;;;;;;YAqBA,IAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,UAAA,GAAA,EAAA,EAAA,OAAA,QAAA,CAAA,GAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;YAEQ,IAAR,GAPU,IAOV,IAPuB,CAOvB,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA;SACA;aACA;YACA,IAAQ,GAAR,IAPe,IAOf,CAAA,KAAA,CAAA,CAAA;SACA;QAPA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;YAQA,qBAAA,KAAA,GAAA,KAAA,CAAA,CAAA;YACA,IAAA,CAAA,OAAA,KAAA,KAAA,QAAA,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,CAAA,EAAA;gBACA,IAAA,GAAA,eAAA,CAAA,KAAA,CAAA,CAAA;aAEA;iBACA;;;;;KA3DA,CAAA;IACA,OAAA,QAAA,CAAA;CAAA,EAAA,CAAA,CAAA;;;;AAIA,QAAA,CAAA,QAAA,GAAA;IACI,QAAJ,EAAA,UAAA;IACI,OAAJ,EAAA,OAAA;IACI,UAAJ,EAAA,YAAA;IACA,UAAA,EAAA,QAAA;IA4CA,YAAA,EAAA,OAAA;IAQA,WAAA,EAAA,KAPc;IAQd,YAAA,EAAA,KAAA;;;;IALA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA;CAUA,CAAA;;;;;;CAyBA,CAAA,EAAA,CAAA;;;;;;;CAOA;;;;;;;CAOA;;;;;AAKA,SAAA,eAAA,CAAA,KAAA,EAAA;IAEE,qBAAF,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA,qBAAA,MA3C2B,GAAG,CA2C9B,CAAA;IACA,qBAAA,KA3C0B,GAAG,CA2C7B,CAAA;IACA,qBAAA,UAAA,GAAA,KAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,WAAA,CAAA;IACE,qBAAF,UA3C+B,GAAO,KA2CtC,CAAA,CAAA,CA3CyC,GA2CzC,IA3CqD,CAAC,WA2CtD,GA3CoE,IA2CpE,CA3CqE,QA2CrE,CAAA;IACE,IAAF,KAAA,CAAA,CAAA,CAAA,EAAA;QACA,MAAA,GAAA,KAAA,CAAA,KA3CQ,CA2CR,CAAA,CA3CY,GA2CZ,KAAA,CAAA,EA3CmB,CAAK,CAAC,CAAC;QA4C1B,KAAA,GAAA,KAAA,CAAA,KA3CQ,CAAA,CA2CR,CAAA,GAAA,KAAA,CAAA,EAAA,CA3CmB,CAAK,CAAC;KA4CzB;IACE,UAAU,CA3CC,IAAC,CAAI,IAAC,EAAK,KA2CxB,CA3C8B,KAAG,CAAG,CAAC,CA2CrC,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,qBAAF,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,GAAA,MAAA,CAAA;IACA,qBAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,GAAA,KAAA,CAAA;;;;;CAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADzLA,IAAA,cAAA,IAAA,YAAA;;;;;;;;;;;;IAaA,cAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,SAAA,EAAA;QAEI,IAAJ,KAAA,IAAA,IAAA;YAEA,OAAA,EAAA,CAAA;QACA,IAAA,OAAA,SAAA,KAAA,QAAA,IAAA,SAAA,KAAA,IAAA,EAAA;;SALA;QAOA,qBANuB,GAMvB,GAAA,iBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,IAAA,CAAA,aAAA,CAAA,CAAA;QACA,OAAA,SAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,EAAA,KAAA,CAAA,QAAA,EAAA,CAAA,CAAA;;;CArBA,EAAA,CAAA,CAAA;;IAiBA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA;CASA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDpBA,cAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,OAAA,EAAA;QAEI,IAAI,KAAR,IAAA,IAAA;YACM,OAJO,EAIb,CAAA;QACA,IAAA,OAAA,OAAA,KAAA,QAAA,IAAA,OAAA,KAAA,KAAA,QAAA,EAAA;YAEQ,MAAR,wBAJgC,CAAO,cAIvC,EAAA,OAAA,CAAA,CAAA;SACA;QACA,IAAA,OAAA,CAAA,cAAA,CAAA,KAAA,CAAA,EAAA;YAEA,OAAA,OAAA,CAAA,KAAA,CAAA,CAAA;SACA;;YAHA,OAAA,OAAA,CAAA,OAAA,CAAA,CAAA;SAKA;QACA,OAAA,EAAA,CAAA;;;;;IAFA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;ADvBA,IAAA,QAAA,IAAA,YAAA;IAAA,SAAA,QAAA,GAAA;;;;;;;;CAAA,EAAA,CAAA,CAAA;;IAEA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,KAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDqCA,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,UAAA,KAAA,EAAA,KAAA,EAAA,GAAA,EAAA;QAEI,IAAJ,KAAA,IAJiB,IAIjB;YACA,OAAA,KAAA,CAAA;;;;;KAFA,CAAA;;;;;;;;;IAKA,EAAA,IAAA,EAAAA,kBAAA,EAAA,IAAA,EAAC,CAAD,EAAC,IAAD,EAAC,OAAD,EAAA,IAAA,EAAA,KAAA,EAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;AD5BA,IAAA,YAAA,GAAA;IACE,SAAF;IACE,aAAF;IACE,aAAa;IACb,QAAF;IACE,SAAF;IACE,WAAF;IACE,WAAF;IACA,aAAA;;IDrDA,QAAA;;;;;;;;;;;;;;;;AAuBA,IAAA,YAAA,IAAA,YAAA;IAAA,SAAA,YAAA,GAAA;KACA;IAAA,OAAA,YAAA,CAAA;CAAA,EAAA,CAAA,CAAA;AACA,YAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAAD,sBAAA,EAAA,IAAA,EAAA,CAAA;gBACA,YAAA,EAAA,CAAA,iBAAA,EAAA,YAAA,CAAA;gBACA,OAAA,EAAA,CAAA,iBAAA,EAAA,YAAA,CAAA;;;;aAMA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}