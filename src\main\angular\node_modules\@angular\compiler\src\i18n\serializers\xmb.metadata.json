[{"__symbolic": "module", "version": 3, "metadata": {"Xmb": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./serializer", "name": "Serializer"}, "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}], "createNameMapper": [{"__symbolic": "method"}]}}, "digest": {"__symbolic": "function", "parameters": ["message"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../digest", "name": "decimalDigest"}, "arguments": [{"__symbolic": "reference", "name": "message"}]}}, "toPublicName": {"__symbolic": "function", "parameters": ["internalName"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 172, "character": 44}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Xmb": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "./serializer", "name": "Serializer"}, "members": {"write": [{"__symbolic": "method"}], "load": [{"__symbolic": "method"}], "digest": [{"__symbolic": "method"}], "createNameMapper": [{"__symbolic": "method"}]}}, "digest": {"__symbolic": "function", "parameters": ["message"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../digest", "name": "decimalDigest"}, "arguments": [{"__symbolic": "reference", "name": "message"}]}}, "toPublicName": {"__symbolic": "function", "parameters": ["internalName"], "value": {"__symbolic": "error", "message": "Expression form not supported", "line": 172, "character": 44}}}}]