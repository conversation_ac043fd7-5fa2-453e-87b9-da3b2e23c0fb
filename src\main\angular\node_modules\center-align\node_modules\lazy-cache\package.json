{"_args": [["lazy-cache@1.0.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "lazy-cache@1.0.4", "_id": "lazy-cache@1.0.4", "_inBundle": false, "_integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "_location": "/center-align/lazy-cache", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lazy-cache@1.0.4", "name": "lazy-cache", "escapedName": "lazy-cache", "rawSpec": "1.0.4", "saveSpec": null, "fetchSpec": "1.0.4"}, "_requiredBy": ["/center-align"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/lazy-cache/-/lazy-cache-1.0.4.tgz", "_spec": "1.0.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/lazy-cache/issues"}, "description": "Cache requires to be lazy-loaded when needed.", "devDependencies": {"ansi-yellow": "^0.1.1", "glob": "^7.0.3", "gulp-format-md": "^0.1.8", "mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/lazy-cache", "keywords": ["cache", "caching", "dependencies", "dependency", "lazy", "require", "requires"], "license": "MIT", "main": "index.js", "name": "lazy-cache", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/lazy-cache.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["lint-deps"]}, "plugins": ["gulp-format-md"], "toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "1.0.4"}