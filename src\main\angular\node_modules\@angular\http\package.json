{"_args": [["@angular/http@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/http@4.2.5", "_id": "@angular/http@4.2.5", "_inBundle": false, "_integrity": "sha1-P/+OXPjogmK6zRyZYwQxLDxaOu8=", "_location": "/@angular/http", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/http@4.2.5", "name": "@angular/http", "escapedName": "@angular%2fhttp", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/http/-/http-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - the http service", "es2015": "./@angular/http.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/http.umd.js", "module": "./@angular/http.es5.js", "name": "@angular/http", "peerDependencies": {"rxjs": "^5.0.1", "@angular/core": "4.2.5", "@angular/platform-browser": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./http.d.ts", "version": "4.2.5"}