[{"__symbolic": "module", "version": 3, "metadata": {"PlaceholderRegistry": {"__symbolic": "class", "members": {"getStartTagPlaceholderName": [{"__symbolic": "method"}], "getCloseTagPlaceholderName": [{"__symbolic": "method"}], "getPlaceholderName": [{"__symbolic": "method"}], "getUniquePlaceholder": [{"__symbolic": "method"}], "_hashTag": [{"__symbolic": "method"}], "_hashClosingTag": [{"__symbolic": "method"}], "_generateUniqueName": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"PlaceholderRegistry": {"__symbolic": "class", "members": {"getStartTagPlaceholderName": [{"__symbolic": "method"}], "getCloseTagPlaceholderName": [{"__symbolic": "method"}], "getPlaceholderName": [{"__symbolic": "method"}], "getUniquePlaceholder": [{"__symbolic": "method"}], "_hashTag": [{"__symbolic": "method"}], "_hashClosingTag": [{"__symbolic": "method"}], "_generateUniqueName": [{"__symbolic": "method"}]}}}}]