{"version": 3, "file": "router-upgrade.umd.js", "sources": ["../../../../packages/router/upgrade/index.ts", "../../../../packages/router/upgrade/src/upgrade.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/upgrade package.\n */\n\nexport * from './src/upgrade';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {APP_BOOTSTRAP_LISTENER, ComponentRef, InjectionToken} from '@angular/core';\nimport {Router} from '@angular/router';\nimport {UpgradeModule} from '@angular/upgrade/static';\n\n\n\n/**\n * @whatItDoes Creates an initializer that in addition to setting up the Angular\n * router sets up the ngRoute integration.\n *\n * @howToUse\n *\n * ```\n * @NgModule({\n *  imports: [\n *   RouterModule.forRoot(SOME_ROUTES),\n *   UpgradeModule\n * ],\n * providers: [\n *   RouterUpgradeInitializer\n * ]\n * })\n * export class AppModule {\n *   ngDoBootstrap() {}\n * }\n * ```\n *\n * @experimental\n */\nexport const RouterUpgradeInitializer = {\n  provide: APP_BOOTSTRAP_LISTENER,\n  multi: true,\n  useFactory: locationSyncBootstrapListener,\n  deps: [UpgradeModule]\n};\n\n/**\n * @internal\n */\nexport function locationSyncBootstrapListener(ngUpgrade: UpgradeModule) {\n  return () => { setUpLocationSync(ngUpgrade); };\n}\n\n/**\n * @whatItDoes Sets up a location synchronization.\n *\n * History.pushState does not fire onPopState, so the Angular location\n * doesn't detect it. The workaround is to attach a location change listener\n *\n * @experimental\n */\nexport function setUpLocationSync(ngUpgrade: UpgradeModule) {\n  if (!ngUpgrade.$injector) {\n    throw new Error(`\n        RouterUpgradeInitializer can be used only after UpgradeModule.bootstrap has been called.\n        Remove RouterUpgradeInitializer and call setUpLocationSync after UpgradeModule.bootstrap.\n      `);\n  }\n\n  const router: Router = ngUpgrade.injector.get(Router);\n  const url = document.createElement('a');\n\n  ngUpgrade.$injector.get('$rootScope')\n      .$on('$locationChangeStart', (_: any, next: string, __: string) => {\n        url.href = next;\n        router.navigateByUrl(url.pathname + url.search);\n      });\n}\n"], "names": ["Router", "UpgradeModule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0CA,IAAA,wBAAA,GAAA;;;;IAKA,IAAA,EAAA,CAAAC,qCAAA,CAAA;CACA,CAAA;;;;;;;;;;;;;;;AAgBA,SAAA,iBAAA,CAAA,SAAA,EAAA;IACA,IAAA,CAAA,SAAA,CAAA,SAAA,EAAA;QAEA,MAAA,IAAA,KAAA,CAAA,+MAIA,CAAA,CAAO;KACP;IACA,IAAA,MAAA,GAAA,SAA4B,CAAC,QAA7B,CAAA,GAAyC,CAAzCD,sBAAgD,CAAhD,CAAA;IACA,IAAA,GAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA;IACA,SAAA,CAAA,SAAA,CAAA,GAAA,CAAA,YAAA,CAAA;;QD3EA,GAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;;;;;;;;"}