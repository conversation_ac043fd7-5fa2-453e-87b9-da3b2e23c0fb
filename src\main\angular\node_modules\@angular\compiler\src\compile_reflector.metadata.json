[{"__symbolic": "module", "version": 3, "metadata": {"CompileReflector": {"__symbolic": "class", "members": {"parameters": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "componentModuleUrl": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"CompileReflector": {"__symbolic": "class", "members": {"parameters": [{"__symbolic": "method"}], "annotations": [{"__symbolic": "method"}], "propMetadata": [{"__symbolic": "method"}], "hasLifecycleHook": [{"__symbolic": "method"}], "componentModuleUrl": [{"__symbolic": "method"}], "resolveExternalReference": [{"__symbolic": "method"}]}}}}]