{"version": 3, "file": "symbols.js", "sourceRoot": "", "sources": ["../../../../../packages/compiler-cli/src/diagnostics/symbols.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAoKH;;;;GAIG;AACH,IAAY,WAwCX;AAxCD,WAAY,WAAW;IACrB;;OAEG;IACH,2CAAG,CAAA;IAEH;;OAEG;IACH,iDAAM,CAAA;IAEN;;OAEG;IACH,iDAAM,CAAA;IAEN;;OAEG;IACH,mDAAO,CAAA;IAEP;;OAEG;IACH,uDAAS,CAAA;IAET;;OAEG;IACH,6CAAI,CAAA;IAEJ;;OAEG;IACH,mDAAO,CAAA;IAEP;;OAEG;IACH,+CAAK,CAAA;AACP,CAAC,EAxCW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAwCtB", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {StaticSymbol} from '@angular/compiler';\n\n/**\n * The range of a span of text in a source file.\n *\n * @experimental\n */\nexport interface Span {\n  /**\n   * The first code-point of the span as an offset relative to the beginning of the source assuming\n   * a UTF-16 encoding.\n   */\n  start: number;\n\n  /**\n   * The first code-point after the span as an offset relative to the beginning of the source\n   * assuming a UTF-16 encoding.\n   */\n  end: number;\n}\n\n/**\n * A file and span.\n */\nexport interface Location {\n  fileName: string;\n  span: Span;\n}\n\n/**\n * A defnition location(s).\n */\nexport type Definition = Location[] | undefined;\n\n/**\n * A symbol describing a language element that can be referenced by expressions\n * in an Angular template.\n *\n * @experimental\n */\nexport interface Symbol {\n  /**\n   * The name of the symbol as it would be referenced in an Angular expression.\n   */\n  readonly name: string;\n\n  /**\n   * The kind of completion this symbol should generate if included.\n   */\n  readonly kind: string;\n\n  /**\n   * The language of the source that defines the symbol. (e.g. typescript for TypeScript,\n   * ng-template for an Angular template, etc.)\n   */\n  readonly language: string;\n\n  /**\n   * A symbol representing type of the symbol.\n   */\n  readonly type: Symbol|undefined;\n\n  /**\n   * A symbol for the container of this symbol. For example, if this is a method, the container\n   * is the class or interface of the method. If no container is appropriate, undefined is\n   * returned.\n   */\n  readonly container: Symbol|undefined;\n\n  /**\n   * The symbol is public in the container.\n   */\n  readonly public: boolean;\n\n  /**\n   * `true` if the symbol can be the target of a call.\n   */\n  readonly callable: boolean;\n\n  /**\n   * The location of the definition of the symbol\n   */\n  readonly definition: Definition|undefined;\n\n  /**\n   * `true` if the symbol is a type that is nullable (can be null or undefined).\n   */\n  readonly nullable: boolean;\n\n  /**\n   * A table of the members of the symbol; that is, the members that can appear\n   * after a `.` in an Angular expression.\n   */\n  members(): SymbolTable;\n\n  /**\n   * The list of overloaded signatures that can be used if the symbol is the\n   * target of a call.\n   */\n  signatures(): Signature[];\n\n  /**\n   * Return which signature of returned by `signatures()` would be used selected\n   * given the `types` supplied. If no signature would match, this method should\n   * return `undefined`.\n   */\n  selectSignature(types: Symbol[]): Signature|undefined;\n\n  /**\n   * Return the type of the expression if this symbol is indexed by `argument`.\n   * If the symbol cannot be indexed, this method should return `undefined`.\n   */\n  indexed(argument: Symbol): Symbol|undefined;\n}\n\n/**\n * A table of `Symbol`s accessible by name.\n *\n * @experimental\n */\nexport interface SymbolTable {\n  /**\n   * The number of symbols in the table.\n   */\n  readonly size: number;\n\n  /**\n   * Get the symbol corresponding to `key` or `undefined` if there is no symbol in the\n   * table by the name `key`.\n   */\n  get(key: string): Symbol|undefined;\n\n  /**\n   * Returns `true` if the table contains a `Symbol` with the name `key`.\n   */\n  has(key: string): boolean;\n\n  /**\n   * Returns all the `Symbol`s in the table. The order should be, but is not required to be,\n   * in declaration order.\n   */\n  values(): Symbol[];\n}\n\n/**\n * A description of a function or method signature.\n *\n * @experimental\n */\nexport interface Signature {\n  /**\n   * The arguments of the signture. The order of `argumetnts.symbols()` must be in the order\n   * of argument declaration.\n   */\n  readonly arguments: SymbolTable;\n\n  /**\n   * The symbol of the signature result type.\n   */\n  readonly result: Symbol;\n}\n\n/**\n * An enumeration of basic types.\n *\n * @experimental\n */\nexport enum BuiltinType {\n  /**\n   * The type is a type that can hold any other type.\n   */\n  Any,\n\n  /**\n   * The type of a string literal.\n   */\n  String,\n\n  /**\n   * The type of a numeric literal.\n   */\n  Number,\n\n  /**\n   * The type of the `true` and `false` literals.\n   */\n  Boolean,\n\n  /**\n   * The type of the `undefined` literal.\n   */\n  Undefined,\n\n  /**\n   * the type of the `null` literal.\n   */\n  Null,\n\n  /**\n   * the type is an unbound type parameter.\n   */\n  Unbound,\n\n  /**\n   * Not a built-in type.\n   */\n  Other\n}\n\n/**\n * The kinds of defintion.\n *\n * @experimental\n */\nexport type DeclarationKind = 'attribute' | 'html attribute' | 'component' | 'element' | 'entity' |\n    'key' | 'method' | 'pipe' | 'property' | 'type' | 'reference' | 'variable';\n\n/**\n * Describes a symbol to type binding used to build a symbol table.\n *\n * @experimental\n */\nexport interface SymbolDeclaration {\n  /**\n   * The name of the symbol in table.\n   */\n  readonly name: string;\n\n  /**\n   * The kind of symbol to declare.\n   */\n  readonly kind: DeclarationKind;\n\n  /**\n   * Type of the symbol. The type symbol should refer to a symbol for a type.\n   */\n  readonly type: Symbol;\n\n  /**\n   * The definion of the symbol if one exists.\n   */\n  readonly definition?: Definition;\n}\n\n/**\n * Information about the pipes that are available for use in a template.\n *\n * @experimental\n */\nexport interface PipeInfo {\n  /**\n   * The name of the pipe.\n   */\n  name: string;\n\n  /**\n   * The static symbol for the pipe's constructor.\n   */\n  symbol: StaticSymbol;\n}\n\n/**\n * A sequence of pipe information.\n *\n * @experimental\n */\nexport type Pipes = PipeInfo[] | undefined;\n\n/**\n * Describes the language context in which an Angular expression is evaluated.\n *\n * @experimental\n */\nexport interface SymbolQuery {\n  /**\n   * Return the built-in type this symbol represents or Other if it is not a built-in type.\n   */\n  getTypeKind(symbol: Symbol): BuiltinType;\n\n  /**\n   * Return a symbol representing the given built-in type.\n   */\n  getBuiltinType(kind: BuiltinType): Symbol;\n\n  /**\n   * Return the symbol for a type that represents the union of all the types given. Any value\n   * of one of the types given should be assignable to the returned type. If no one type can\n   * be constructed then this should be the Any type.\n   */\n  getTypeUnion(...types: Symbol[]): Symbol;\n\n  /**\n   * Return a symbol for an array type that has the `type` as its element type.\n   */\n  getArrayType(type: Symbol): Symbol;\n\n  /**\n   * Return element type symbol for an array type if the `type` is an array type. Otherwise return\n   * undefined.\n   */\n  getElementType(type: Symbol): Symbol|undefined;\n\n  /**\n   * Return a type that is the non-nullable version of the given type. If `type` is already\n   * non-nullable, return `type`.\n   */\n  getNonNullableType(type: Symbol): Symbol;\n\n  /**\n   * Return a symbol table for the pipes that are in scope.\n   */\n  getPipes(): SymbolTable;\n\n  /**\n   * Return the type symbol for the given static symbol.\n   */\n  getTypeSymbol(type: StaticSymbol): Symbol;\n\n  /**\n   * Return the members that are in the context of a type's template reference.\n   */\n  getTemplateContext(type: StaticSymbol): SymbolTable|undefined;\n\n  /**\n   * Produce a symbol table with the given symbols. Used to produce a symbol table\n   * for use with mergeSymbolTables().\n   */\n  createSymbolTable(symbols: SymbolDeclaration[]): SymbolTable;\n\n  /**\n   * Produce a merged symbol table. If the symbol tables contain duplicate entries\n   * the entries of the latter symbol tables will obscure the entries in the prior\n   * symbol tables.\n   *\n   * The symbol tables passed to this routine MUST be produces by the same instance\n   * of SymbolQuery that is being called.\n   */\n  mergeSymbolTable(symbolTables: SymbolTable[]): SymbolTable;\n\n  /**\n   * Return the span of the narrowest non-token node at the given location.\n   */\n  getSpanAt(line: number, column: number): Span|undefined;\n}"]}