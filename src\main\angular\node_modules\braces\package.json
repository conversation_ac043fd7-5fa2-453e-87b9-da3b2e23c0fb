{"_args": [["braces@1.8.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "braces@1.8.5", "_id": "braces@1.8.5", "_inBundle": false, "_integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "_location": "/braces", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "braces@1.8.5", "name": "braces", "escapedName": "braces", "rawSpec": "1.8.5", "saveSpec": null, "fetchSpec": "1.8.5"}, "_requiredBy": ["/micromatch"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/braces/-/braces-1.8.5.tgz", "_spec": "1.8.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "devDependencies": {"benchmarked": "^0.1.5", "brace-expansion": "^1.1.3", "chalk": "^1.1.3", "gulp-format-md": "^0.1.8", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "should": "^8.3.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/braces", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "license": "MIT", "main": "index.js", "name": "braces", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/braces.git"}, "scripts": {"test": "mocha"}, "verb": {"plugins": ["gulp-format-md"], "reflinks": ["verb"], "toc": false, "layout": "default", "lint": {"reflinks": true}, "tasks": ["readme"], "related": {"list": ["micromatch", "expand-range", "fill-range"]}}, "version": "1.8.5"}