{"title": "CSS page-break properties", "description": "Properties to control the way elements are broken across (printed) pages.", "spec": "http://www.w3.org/TR/CSS2/page.html#page-breaks", "status": "rec", "links": [{"url": "https://css-tricks.com/almanac/properties/p/page-break/", "title": "CSS Tricks article"}, {"url": "http://dev.w3.org/csswg/css-break-3/#break-between", "title": "Latest fragmentation specification (includes column & region breaks)"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "a #1 #2 #3", "6": "a #1 #2 #3", "7": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "10": "a #1 #2", "11": "a #1 #2"}, "edge": {"12": "a #1 #2", "13": "a #1 #2", "14": "a #1 #2", "15": "a #1 #2", "16": "a #1 #2"}, "firefox": {"2": "a #1 #2 #3", "3": "a #1 #2 #3", "3.5": "a #1 #2 #3", "3.6": "a #1 #2 #3", "4": "a #1 #2 #3", "5": "a #1 #2 #3", "6": "a #1 #2 #3", "7": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "10": "a #1 #2 #3", "11": "a #1 #2 #3", "12": "a #1 #2 #3", "13": "a #1 #2 #3", "14": "a #1 #2 #3", "15": "a #1 #2 #3", "16": "a #1 #2 #3", "17": "a #1 #2 #3", "18": "a #1 #2 #3", "19": "a #1 #2 #3", "20": "a #1 #2 #3", "21": "a #1 #2 #3", "22": "a #1 #2 #3", "23": "a #1 #2 #3", "24": "a #1 #2 #3", "25": "a #1 #2 #3", "26": "a #1 #2 #3", "27": "a #1 #2 #3", "28": "a #1 #2 #3", "29": "a #1 #2 #3", "30": "a #1 #2 #3", "31": "a #1 #2 #3", "32": "a #1 #2 #3", "33": "a #1 #2 #3", "34": "a #1 #2 #3", "35": "a #1 #2 #3", "36": "a #1 #2 #3", "37": "a #1 #2 #3", "38": "a #1 #2 #3", "39": "a #1 #2 #3", "40": "a #1 #2 #3", "41": "a #1 #2 #3", "42": "a #1 #2 #3", "43": "a #1 #2 #3", "44": "a #1 #2 #3", "45": "a #1 #2 #3", "46": "a #1 #2 #3", "47": "a #1 #2 #3", "48": "a #1 #2 #3", "49": "a #1 #2 #3", "50": "a #1 #2 #3", "51": "a #1 #2 #3", "52": "a #1 #2 #3", "53": "a #1 #2 #3", "54": "a #1 #2 #3", "55": "a #1 #2 #3", "56": "a #1 #2 #3", "57": "a #1 #2 #3"}, "chrome": {"4": "a #1 #2 #3", "5": "a #1 #2 #3", "6": "a #1 #2 #3", "7": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "10": "a #1 #2 #3", "11": "a #1 #2 #3", "12": "a #1 #2 #3", "13": "a #1 #2 #3", "14": "a #1 #2 #3", "15": "a #1 #2 #3", "16": "a #1 #2 #3", "17": "a #1 #2 #3", "18": "a #1 #2 #3", "19": "a #1 #2 #3", "20": "a #1 #2 #3", "21": "a #1 #2 #3", "22": "a #1 #2 #3", "23": "a #1 #2 #3", "24": "a #1 #2 #3", "25": "a #1 #2 #3", "26": "a #1 #2 #3", "27": "a #1 #2 #3", "28": "a #1 #2 #3", "29": "a #1 #2 #3", "30": "a #1 #2 #3", "31": "a #1 #2 #3", "32": "a #1 #2 #3", "33": "a #1 #2 #3", "34": "a #1 #2 #3", "35": "a #1 #2 #3", "36": "a #1 #2 #3", "37": "a #1 #2 #3", "38": "a #1 #2 #3", "39": "a #1 #2 #3", "40": "a #1 #2 #3", "41": "a #1 #2 #3", "42": "a #1 #2 #3", "43": "a #1 #2 #3", "44": "a #1 #2 #3", "45": "a #1 #2 #3", "46": "a #1 #2 #3", "47": "a #1 #2 #3", "48": "a #1 #2 #3", "49": "a #1 #2 #3", "50": "a #1 #2 #3", "51": "a #1 #2 #3", "52": "a #1 #2 #3", "53": "a #1 #2 #3", "54": "a #1 #2 #3", "55": "a #1 #2 #3", "56": "a #1 #2 #3", "57": "a #1 #2 #3", "58": "a #1 #2 #3", "59": "a #1 #2 #3", "60": "a #1 #2 #3", "61": "a #1 #2 #3", "62": "a #1 #2 #3"}, "safari": {"3.1": "a #1 #2 #3", "3.2": "a #1 #2 #3", "4": "a #1 #2 #3", "5": "a #1 #2 #3", "5.1": "a #1 #2 #3", "6": "a #1 #2 #3", "6.1": "a #1 #2 #3", "7": "a #1 #2 #3", "7.1": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "9.1": "a #1 #2 #3", "10": "a #2 #3", "10.1": "a #1 #2 #3", "11": "a #1 #2 #3", "TP": "a #1 #2 #3"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "y #1", "10.5": "y #1", "10.6": "y #1", "11": "y #1", "11.1": "y #1", "11.5": "y #1", "11.6": "y #1", "12": "y #1", "12.1": "y #1", "15": "a #1 #2 #3", "16": "a #1 #2 #3", "17": "a #1 #2 #3", "18": "a #1 #2 #3", "19": "a #1 #2 #3", "20": "a #1 #2 #3", "21": "a #1 #2 #3", "22": "a #1 #2 #3", "23": "a #1 #2 #3", "24": "a #1 #2 #3", "25": "a #1 #2 #3", "26": "a #1 #2 #3", "27": "a #1 #2 #3", "28": "a #1 #2 #3", "29": "a #1 #2 #3", "30": "a #1 #2 #3", "31": "a #1 #2 #3", "32": "a #1 #2 #3", "33": "a #1 #2 #3", "34": "a #1 #2 #3", "35": "a #1 #2 #3", "36": "a #1 #2 #3", "37": "a #1 #2 #3", "38": "a #1 #2 #3", "39": "a #1 #2 #3", "40": "a #1 #2 #3", "41": "a #1 #2 #3", "42": "a #1 #2 #3", "43": "a #1 #2 #3", "44": "a #1 #2 #3", "45": "a #1 #2 #3", "46": "a #1 #2 #3", "47": "a #1 #2 #3", "48": "a #1 #2 #3"}, "ios_saf": {"3.2": "a #1 #2 #3", "4.0-4.1": "a #1 #2 #3", "4.2-4.3": "a #1 #2 #3", "5.0-5.1": "a #1 #2 #3", "6.0-6.1": "a #1 #2 #3", "7.0-7.1": "a #1 #2 #3", "8": "a #1 #2 #3", "8.1-8.4": "a #1 #2 #3", "9.0-9.2": "a #1 #2 #3", "9.3": "a #1 #2 #3", "10.0-10.2": "a #1 #2 #3", "10.3": "a #1 #2 #3", "11": "a #1 #2 #3"}, "op_mini": {"all": "y #1"}, "android": {"2.1": "a #1 #2 #3", "2.2": "a #1 #2 #3", "2.3": "a #1 #2 #3", "3": "a #1 #2 #3", "4": "a #1 #2 #3", "4.1": "a #1 #2 #3", "4.2-4.3": "a #1 #2 #3", "4.4": "a #1 #2 #3", "4.4.3-4.4.4": "a #1 #2 #3", "56": "a #1 #2 #3"}, "bb": {"7": "a #1 #2 #3", "10": "a #1 #2 #3"}, "op_mob": {"10": "y #1", "11": "y #1", "11.1": "y #1", "11.5": "y #1", "12": "y #1", "12.1": "y #1", "37": "a #1 #2 #3"}, "and_chr": {"59": "a #1 #2 #3"}, "and_ff": {"54": "a #1 #2 #3"}, "ie_mob": {"10": "a #1 #2", "11": "a #1 #2"}, "and_uc": {"11.4": "a #1 #2 #3"}, "samsung": {"4": "a #1 #2 #3", "5": "a #1 #2 #3"}, "and_qq": {"1.2": "a #1 #2 #3"}, "baidu": {"7.12": "a #1 #2 #3"}}, "notes": "Not all mobile browsers offer print support; support listed for these is based on browser engine capability.", "notes_by_num": {"1": "Supports the `page-break-*` alias from the CSS 2.1 specification, but not the `break-*` properties from the latest spec.", "2": "Does not support `avoid` for `page-break-before` & `page-break-after` (only `page-break-inside`).", "3": "Treats the `left` and `right` values like `always`."}, "usage_perc_y": 3.21, "usage_perc_a": 94.87, "ucprefix": false, "parent": "", "keywords": "page-break-before,page-break-after,page-break-inside,always,avoid", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}