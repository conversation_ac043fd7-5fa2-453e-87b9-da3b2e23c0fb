export declare const ExpTypes: {
    Binary(ast: any): boolean;
    Quote(ast: any): boolean;
    EmptyExpr(ast: any): boolean;
    ImplicitReceiver(ast: any): boolean;
    Chain(ast: any): boolean;
    Conditional(ast: any): boolean;
    PropertyRead(ast: any): boolean;
    PropertyWrite(ast: any): boolean;
    SafePropertyRead(ast: any): boolean;
    KeyedRead(ast: any): boolean;
    KeyedWrite(ast: any): boolean;
    BindingPipe(ast: any): boolean;
    LiteralPrimitive(ast: any): boolean;
    LiteralArray(ast: any): boolean;
    LiteralMap(ast: any): boolean;
    Interpolation(ast: any): boolean;
    PrefixNot(ast: any): boolean;
    MethodCall(ast: any): boolean;
    SafeMethodCall(ast: any): boolean;
    FunctionCall(ast: any): boolean;
    ASTWithSource(ast: any): boolean;
};
