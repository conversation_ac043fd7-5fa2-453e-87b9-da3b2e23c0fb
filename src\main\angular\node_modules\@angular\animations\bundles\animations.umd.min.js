/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports):"function"==typeof define&&define.amd?define(["exports"],factory):factory((global.ng=global.ng||{},global.ng.animations=global.ng.animations||{}))}(this,function(exports){"use strict";function trigger(name,definitions){return{type:7,name:name,definitions:definitions,options:{}}}function animate(timings,styles){return void 0===styles&&(styles=null),{type:4,styles:styles,timings:timings}}function group(steps,options){return void 0===options&&(options=null),{type:3,steps:steps,options:options}}function sequence(steps,options){return void 0===options&&(options=null),{type:2,steps:steps,options:options}}function style(tokens){return{type:6,styles:tokens,offset:null}}function state(name,styles){return{type:0,name:name,styles:styles}}function keyframes(steps){return{type:5,steps:steps}}function transition(stateChangeExpr,steps,options){return void 0===options&&(options=null),{type:1,expr:stateChangeExpr,animation:steps,options:options}}function animation(steps,options){return void 0===options&&(options=null),{type:8,animation:steps,options:options}}function animateChild(options){return void 0===options&&(options=null),{type:9,options:options}}function useAnimation(animation,options){return void 0===options&&(options=null),{type:10,animation:animation,options:options}}function query(selector,animation,options){return void 0===options&&(options=null),{type:11,selector:selector,animation:animation,options:options}}function stagger(timings,animation){return{type:12,timings:timings,animation:animation}}/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 * @param {?} cb
 * @return {?}
 */
function scheduleMicroTask(cb){Promise.resolve(null).then(cb)}/**
 * @license Angular v4.2.5
 * (c) 2010-2017 Google, Inc. https://angular.io/
 * License: MIT
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var AnimationBuilder=function(){function AnimationBuilder(){}return AnimationBuilder.prototype.build=function(animation){},AnimationBuilder}(),AnimationFactory=function(){function AnimationFactory(){}return AnimationFactory.prototype.create=function(element,options){},AnimationFactory}(),AUTO_STYLE="*",NoopAnimationPlayer=function(){function NoopAnimationPlayer(){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this.parentPlayer=null,this.totalTime=0}return NoopAnimationPlayer.prototype._onFinish=function(){this._finished||(this._finished=!0,this._onDoneFns.forEach(function(fn){return fn()}),this._onDoneFns=[])},NoopAnimationPlayer.prototype.onStart=function(fn){this._onStartFns.push(fn)},NoopAnimationPlayer.prototype.onDone=function(fn){this._onDoneFns.push(fn)},NoopAnimationPlayer.prototype.onDestroy=function(fn){this._onDestroyFns.push(fn)},NoopAnimationPlayer.prototype.hasStarted=function(){return this._started},NoopAnimationPlayer.prototype.init=function(){},NoopAnimationPlayer.prototype.play=function(){this.hasStarted()||(this.triggerMicrotask(),this._onStart()),this._started=!0},NoopAnimationPlayer.prototype.triggerMicrotask=function(){var _this=this;scheduleMicroTask(function(){return _this._onFinish()})},NoopAnimationPlayer.prototype._onStart=function(){this._onStartFns.forEach(function(fn){return fn()}),this._onStartFns=[]},NoopAnimationPlayer.prototype.pause=function(){},NoopAnimationPlayer.prototype.restart=function(){},NoopAnimationPlayer.prototype.finish=function(){this._onFinish()},NoopAnimationPlayer.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(function(fn){return fn()}),this._onDestroyFns=[])},NoopAnimationPlayer.prototype.reset=function(){},NoopAnimationPlayer.prototype.setPosition=function(p){},NoopAnimationPlayer.prototype.getPosition=function(){return 0},NoopAnimationPlayer}(),AnimationGroupPlayer=function(){function AnimationGroupPlayer(_players){var _this=this;this._players=_players,this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0;var doneCount=0,destroyCount=0,startCount=0,total=this._players.length;0==total?scheduleMicroTask(function(){return _this._onFinish()}):this._players.forEach(function(player){player.parentPlayer=_this,player.onDone(function(){++doneCount>=total&&_this._onFinish()}),player.onDestroy(function(){++destroyCount>=total&&_this._onDestroy()}),player.onStart(function(){++startCount>=total&&_this._onStart()})}),this.totalTime=this._players.reduce(function(time,player){return Math.max(time,player.totalTime)},0)}return AnimationGroupPlayer.prototype._onFinish=function(){this._finished||(this._finished=!0,this._onDoneFns.forEach(function(fn){return fn()}),this._onDoneFns=[])},AnimationGroupPlayer.prototype.init=function(){this._players.forEach(function(player){return player.init()})},AnimationGroupPlayer.prototype.onStart=function(fn){this._onStartFns.push(fn)},AnimationGroupPlayer.prototype._onStart=function(){this.hasStarted()||(this._onStartFns.forEach(function(fn){return fn()}),this._onStartFns=[],this._started=!0)},AnimationGroupPlayer.prototype.onDone=function(fn){this._onDoneFns.push(fn)},AnimationGroupPlayer.prototype.onDestroy=function(fn){this._onDestroyFns.push(fn)},AnimationGroupPlayer.prototype.hasStarted=function(){return this._started},AnimationGroupPlayer.prototype.play=function(){this.parentPlayer||this.init(),this._onStart(),this._players.forEach(function(player){return player.play()})},AnimationGroupPlayer.prototype.pause=function(){this._players.forEach(function(player){return player.pause()})},AnimationGroupPlayer.prototype.restart=function(){this._players.forEach(function(player){return player.restart()})},AnimationGroupPlayer.prototype.finish=function(){this._onFinish(),this._players.forEach(function(player){return player.finish()})},AnimationGroupPlayer.prototype.destroy=function(){this._onDestroy()},AnimationGroupPlayer.prototype._onDestroy=function(){this._destroyed||(this._destroyed=!0,this._onFinish(),this._players.forEach(function(player){return player.destroy()}),this._onDestroyFns.forEach(function(fn){return fn()}),this._onDestroyFns=[])},AnimationGroupPlayer.prototype.reset=function(){this._players.forEach(function(player){return player.reset()}),this._destroyed=!1,this._finished=!1,this._started=!1},AnimationGroupPlayer.prototype.setPosition=function(p){var timeAtPosition=p*this.totalTime;this._players.forEach(function(player){var position=player.totalTime?Math.min(1,timeAtPosition/player.totalTime):1;player.setPosition(position)})},AnimationGroupPlayer.prototype.getPosition=function(){var min=0;return this._players.forEach(function(player){var p=player.getPosition();min=Math.min(p,min)}),min},Object.defineProperty(AnimationGroupPlayer.prototype,"players",{get:function(){return this._players},enumerable:!0,configurable:!0}),AnimationGroupPlayer}(),ɵPRE_STYLE="!";exports.AnimationBuilder=AnimationBuilder,exports.AnimationFactory=AnimationFactory,exports.AUTO_STYLE=AUTO_STYLE,exports.animate=animate,exports.animateChild=animateChild,exports.animation=animation,exports.group=group,exports.keyframes=keyframes,exports.query=query,exports.sequence=sequence,exports.stagger=stagger,exports.state=state,exports.style=style,exports.transition=transition,exports.trigger=trigger,exports.useAnimation=useAnimation,exports.NoopAnimationPlayer=NoopAnimationPlayer,exports.ɵAnimationGroupPlayer=AnimationGroupPlayer,exports.ɵPRE_STYLE=ɵPRE_STYLE,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=animations.umd.min.js.map
