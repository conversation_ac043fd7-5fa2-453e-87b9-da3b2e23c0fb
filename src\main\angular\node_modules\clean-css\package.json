{"_args": [["clean-css@4.1.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "clean-css@4.1.5", "_id": "clean-css@4.1.5", "_inBundle": false, "_integrity": "sha1-0JqHoCpTdRF1iXlq52oGPKzbVBo=", "_location": "/clean-css", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clean-css@4.1.5", "name": "clean-css", "escapedName": "clean-css", "rawSpec": "4.1.5", "saveSpec": null, "fetchSpec": "4.1.5"}, "_requiredBy": ["/html-minifier"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/clean-css/-/clean-css-4.1.5.tgz", "_spec": "4.1.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jakub<PERSON><PERSON>owicz"}, "bugs": {"url": "https://github.com/jakubpawlowicz/clean-css/issues"}, "dependencies": {"source-map": "0.5.x"}, "description": "A well-tested CSS minifier", "devDependencies": {"browserify": "^14.0.0", "http-proxy": "1.x", "jshint": "2.x", "nock": "9.x", "server-destroy": "1.x", "uglify-js": ">=2.6.1", "vows": "0.8.x"}, "engines": {"node": ">= 4.0"}, "files": ["lib", "History.md", "index.js", "LICENSE"], "homepage": "https://github.com/jakubpawlowicz/clean-css", "keywords": ["css", "minifier"], "license": "MIT", "main": "index.js", "name": "clean-css", "repository": {"type": "git", "url": "git+https://github.com/jakubpawlowicz/clean-css.git"}, "scripts": {"bench": "node ./test/bench.js", "browserify": "browserify --standalone CleanCSS index.js | uglifyjs --compress --mangle -o cleancss-browser.js", "check": "jshint .", "prepublish": "npm run check", "test": "vows"}, "version": "4.1.5"}