{"title": "Ruby annotation", "description": "Method of adding pronunciation or other annotations using ruby elements (primarily used in East Asian typography).", "spec": "https://html.spec.whatwg.org/multipage/semantics.html#the-ruby-element", "status": "ls", "links": [{"url": "http://html5doctor.com/ruby-rt-rp-element/", "title": "HTML5 Doctor article"}, {"url": "https://www.webplatform.org/docs/html/elements/ruby", "title": "WebPlatform Docs"}, {"url": "https://addons.mozilla.org/firefox/addon/1935/", "title": "Add-on \"XHTML Ruby Support\" for Firefox"}, {"url": "https://addons.mozilla.org/firefox/addon/6812/", "title": "<PERSON>don \"HTML Ruby\" for Firefox support"}, {"url": "http://www.w3.org/TR/css-ruby-1/", "title": "CSS specification"}], "bugs": [{"description": "IE and Firefox do not appear to have support for nested ruby structures [see testcase](http://jsfiddle.net/tabletguy/49412u7r/)"}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a"}, "edge": {"12": "a", "13": "a", "14": "a", "15": "a", "16": "a"}, "firefox": {"2": "p", "3": "p", "3.5": "p", "3.6": "p", "4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "p", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a"}, "safari": {"3.1": "p", "3.2": "p", "4": "p", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "a", "10.1": "a", "11": "a", "TP": "a"}, "opera": {"9": "p", "9.5-9.6": "p", "10.0-10.1": "p", "10.5": "p", "10.6": "p", "11": "p", "11.1": "p", "11.5": "p", "11.6": "p", "12": "p", "12.1": "p", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "p"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "p", "10": "a"}, "op_mob": {"10": "p", "11": "p", "11.1": "p", "11.5": "p", "12": "p", "12.1": "p", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "Browsers without native support can still simulate support using CSS. Partial support refers to only supporting basic ruby, may still be missing writing-mode, Complex ruby and CSS3 Ruby.", "notes_by_num": {"1": "IE9+ supports [properties](https://msdn.microsoft.com/en-us/library/hh772055%28v=vs.85%29.aspx) of an older version of the CSS Ruby specification."}, "usage_perc_y": 5.59, "usage_perc_a": 88.89, "ucprefix": false, "parent": "", "keywords": "ruby-base,ruby-text,ruby-position,ruby-merge,ruby-align", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}