[{"__symbolic": "module", "version": 3, "metadata": {"LifecycleHooks": {"OnInit": 0, "OnDestroy": 1, "DoCheck": 2, "OnChanges": 3, "AfterContentInit": 4, "AfterContentChecked": 5, "AfterViewInit": 6, "AfterViewChecked": 7}, "LIFECYCLE_HOOKS_VALUES": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnDestroy"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "<PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnChanges"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterContentInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterContentChecked"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterViewInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterViewChecked"}], "hasLifecycleHook": {"__symbolic": "function", "parameters": ["reflector", "hook", "token"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 36, "character": 9, "context": {"name": "getHookName"}}}, "getAllLifecycleHooks": {"__symbolic": "function", "parameters": ["reflector", "token"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 33, "character": 39}}}}, {"__symbolic": "module", "version": 1, "metadata": {"LifecycleHooks": {"OnInit": 0, "OnDestroy": 1, "DoCheck": 2, "OnChanges": 3, "AfterContentInit": 4, "AfterContentChecked": 5, "AfterViewInit": 6, "AfterViewChecked": 7}, "LIFECYCLE_HOOKS_VALUES": [{"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnDestroy"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "<PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "OnChanges"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterContentInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterContentChecked"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterViewInit"}, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "LifecycleHooks"}, "member": "AfterViewChecked"}], "hasLifecycleHook": {"__symbolic": "function", "parameters": ["reflector", "hook", "token"], "value": {"__symbolic": "error", "message": "Reference to a non-exported function", "line": 36, "character": 9, "context": {"name": "getHookName"}}}, "getAllLifecycleHooks": {"__symbolic": "function", "parameters": ["reflector", "token"], "value": {"__symbolic": "error", "message": "Function call not supported", "line": 33, "character": 39}}}}]