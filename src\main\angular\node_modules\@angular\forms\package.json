{"_args": [["@angular/forms@4.2.5", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@angular/forms@4.2.5", "_id": "@angular/forms@4.2.5", "_inBundle": false, "_integrity": "sha1-p+VcjR9aToU37+Ht14NOSh9ZxuQ=", "_location": "/@angular/forms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/forms@4.2.5", "name": "@angular/forms", "escapedName": "@angular%2fforms", "scope": "@angular", "rawSpec": "4.2.5", "saveSpec": null, "fetchSpec": "4.2.5"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@angular/forms/-/forms-4.2.5.tgz", "_spec": "4.2.5", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "dependencies": {"tslib": "^1.7.1"}, "description": "Angular - directives and services for creating forms", "es2015": "./@angular/forms.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/forms.umd.js", "module": "./@angular/forms.es5.js", "name": "@angular/forms", "peerDependencies": {"@angular/core": "4.2.5", "@angular/common": "4.2.5", "@angular/platform-browser": "4.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./forms.d.ts", "version": "4.2.5"}