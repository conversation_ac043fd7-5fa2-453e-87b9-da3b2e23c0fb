[![NPM version](https://img.shields.io/npm/v/clap.svg)](https://www.npmjs.com/package/clap)
[![Dependency Status](https://img.shields.io/david/lahmatiy/clap.svg)](https://david-dm.org/lahmatiy/clap)
[![Build Status](https://travis-ci.org/lahmatiy/clap.svg?branch=master)](https://travis-ci.org/lahmatiy/clap)

# Clap.js

Argument parser for command-line interfaces. It primary target to large tool sets that provides a lot of subcommands. Support for argument coercion and completion makes task run much easer, even if you doesn't use CLI.

Inspired by <PERSON><PERSON> [Commander](https://github.com/visionmedia/commander.js).

[TODO: Complete readme]
