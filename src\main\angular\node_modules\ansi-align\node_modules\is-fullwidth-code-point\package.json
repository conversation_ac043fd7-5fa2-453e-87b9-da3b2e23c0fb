{"_args": [["is-fullwidth-code-point@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "is-fullwidth-code-point@1.0.0", "_id": "is-fullwidth-code-point@1.0.0", "_inBundle": false, "_integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "_location": "/ansi-align/is-fullwidth-code-point", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-fullwidth-code-point@1.0.0", "name": "is-fullwidth-code-point", "escapedName": "is-fullwidth-code-point", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/ansi-align/string-width"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "dependencies": {"number-is-nan": "^1.0.0"}, "description": "Check if the character represented by a given Unicode code point is fullwidth", "devDependencies": {"ava": "0.0.4", "code-point-at": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "license": "MIT", "name": "is-fullwidth-code-point", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}