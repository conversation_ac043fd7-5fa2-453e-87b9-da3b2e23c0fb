{"_args": [["camelcase-keys@2.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "camelcase-keys@2.1.0", "_id": "camelcase-keys@2.1.0", "_inBundle": false, "_integrity": "sha1-MIvur/3ygRkFHvodkyITyRuPkuc=", "_location": "/camelcase-keys", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "camelcase-keys@2.1.0", "name": "camelcase-keys", "escapedName": "camelcase-keys", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/meow"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "_spec": "2.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase-keys/issues"}, "dependencies": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "description": "Convert object keys to camelCase", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase-keys#readme", "keywords": ["map", "obj", "object", "key", "keys", "value", "values", "val", "iterate", "camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase-keys", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase-keys.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}