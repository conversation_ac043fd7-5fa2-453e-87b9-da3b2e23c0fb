{"title": "maxlength attribute for input and textarea elements", "description": "Declares an upper bound on the number of characters the user can input. Normally the UI ignores attempts by the user to type in additional characters beyond this limit.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#attr-input-maxlength", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-maxlength", "title": "Mozilla Developer Network (MDN) documentation - attribute maxlength"}], "bugs": [{"description": "In Opera 12.1 & below `ValidityState.tooLong` is true when the input's initial value exceeds the maxlength. Per the spec, it should instead be false until the user edits the value."}], "categories": ["DOM", "HTML5", "JS API"], "stats": {"ie": {"5.5": "u", "6": "a #1 #2 #3", "7": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "10": "y", "11": "y"}, "edge": {"12": "y #4", "13": "y #4", "14": "y #4", "15": "y #4", "16": "y #4"}, "firefox": {"2": "a #1 #2 #3", "3": "a #1 #2 #3", "3.5": "a #1 #2 #3", "3.6": "a #1 #2 #3", "4": "y #4", "5": "y #4", "6": "y #4", "7": "y #4", "8": "y #4", "9": "y #4", "10": "y #4", "11": "y #4", "12": "y #4", "13": "y #4", "14": "y #4", "15": "y #4", "16": "y #4", "17": "y #4", "18": "y #4", "19": "y #4", "20": "y #4", "21": "y #4", "22": "y #4", "23": "y #4", "24": "y #4", "25": "y #4", "26": "y #4", "27": "y #4", "28": "y #4", "29": "y #4", "30": "y #4", "31": "y #4", "32": "y #4", "33": "y #4", "34": "y #4", "35": "y #4", "36": "y #4", "37": "y #4", "38": "y #4", "39": "y #4", "40": "y #4", "41": "y #4", "42": "y #4", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4", "49": "y #4", "50": "y #4", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "u", "3.2": "a #1 #2 #3", "4": "a #1 #2 #3", "5": "u", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "a #5", "8.1-8.4": "a #5", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "a #1", "12.1": "a #1", "37": "a #6"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.4": "a #6"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "`<textarea>`'s' UI does not prevent the user from typing additional characters beyond the `maxlength` limit.", "2": "Does not support the `HTMLTextAreaElement.maxLength` DOM property.", "3": "Does not support [`ValidityState.tooLong`](https://developer.mozilla.org/en-US/docs/Web/API/ValidityState/tooLong). In some cases, this is because `.validity` is not supported in the first place.", "4": "Does not support `ValidityState.tooLong` correctly in the (unlikely) case of the value being initially set too long, then changed by the user to a still incorrect state. See [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1203844) and [MS Edge bug](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/4678527/).", "5": "`<input>`'s UI does not prevent the user from typing additional characters beyond the `maxlength` limit between two existing characters of the string.", "6": "Allows text beyond maxlength to be entered at first, but removes all characters past the maxlength when focus is lost."}, "usage_perc_y": 85.05, "usage_perc_a": 13, "ucprefix": false, "parent": "form-validation", "keywords": "maximum,length,input,textarea,too,long,validity,state", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}