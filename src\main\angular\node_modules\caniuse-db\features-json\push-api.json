{"title": "Push API", "description": "API to allow messages to be pushed from a server to a browser, even when the site isn't focused or even open in the browser.", "spec": "https://w3c.github.io/push-api/", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Push_API", "title": "Mozilla Developer Network (MDN) documentation - Push API"}, {"url": "https://developers.google.com/web/updates/2015/03/push-notifications-on-the-open-web", "title": "Google Developers article"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "y #2", "45": "y #2 #4", "46": "y #2", "47": "y #2", "48": "y #2", "49": "y #2", "50": "y #2", "51": "y #2", "52": "y #2 #4", "53": "y #2", "54": "y #2", "55": "y #2", "56": "y #2", "57": "y #2"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "a #1 #2", "45": "a #1 #2", "46": "a #1 #2", "47": "a #1 #2", "48": "a #1 #2", "49": "a #1 #2", "50": "y #2", "51": "y #2", "52": "y #2", "53": "y #2", "54": "y #2", "55": "y #2", "56": "y #2", "57": "y #2", "58": "y #2", "59": "y #2", "60": "y #2", "61": "y #2", "62": "y #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n #3", "10": "n #3", "10.1": "n #3", "11": "n #3", "TP": "n #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "u", "38": "u", "39": "u", "40": "u", "41": "u", "42": "y #2", "43": "y #2", "44": "y #2", "45": "y #2", "46": "y #2", "47": "y #2", "48": "y #2"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to not supporting `PushEvent.data` and `PushMessageData`", "2": "Requires full browser to be running to receive messages", "3": "Safari supports a custom implementaion https://developer.apple.com/notifications/safari-push-notifications/. WWDC video by apple : https://developer.apple.com/videos/play/wwdc2013/614/ ", "4": "Disabled on Firefox ESR, but can be re-enabled with the `dom.serviceWorkers.enabled` and `dom.push.enabled` flags"}, "usage_perc_y": 71.68, "usage_perc_a": 1.61, "ucprefix": false, "parent": "", "keywords": "push notifications", "ie_id": "<PERSON><PERSON><PERSON>", "chrome_id": "5416033485586432", "firefox_id": "push", "webkit_id": "", "shown": true}