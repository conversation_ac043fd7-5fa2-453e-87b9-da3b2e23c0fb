[{"__symbolic": "module", "version": 3, "metadata": {"ResolvedStaticSymbol": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}, {"__symbolic": "reference", "name": "any"}]}]}}, "StaticSymbolResolverHost": {"__symbolic": "interface"}, "StaticSymbolResolver": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbolCache"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 74, "character": 30}]}], "resolveSymbol": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "getResourcePath": [{"__symbolic": "method"}], "getTypeArity": [{"__symbolic": "method"}], "fileNameToModuleName": [{"__symbolic": "method"}], "recordImportAs": [{"__symbolic": "method"}], "invalidateFile": [{"__symbolic": "method"}], "_resolveSymbolMembers": [{"__symbolic": "method"}], "_resolveSymbolFromSummary": [{"__symbolic": "method"}], "getStaticSymbol": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "_createSymbolsOf": [{"__symbolic": "method"}], "createResolvedSymbol": [{"__symbolic": "method"}], "createExport": [{"__symbolic": "method"}], "reportError": [{"__symbolic": "method"}], "getModuleMetadata": [{"__symbolic": "method"}], "getSymbolByModule": [{"__symbolic": "method"}], "resolveModule": [{"__symbolic": "method"}]}}, "unescapeIdentifier": {"__symbolic": "function", "parameters": ["identifier"], "value": {"__symbolic": "if", "condition": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "identifier"}, "member": "startsWith"}, "arguments": ["___"]}, "thenExpression": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "identifier"}, "member": "substr"}, "arguments": [1]}, "elseExpression": {"__symbolic": "reference", "name": "identifier"}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ResolvedStaticSymbol": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}, {"__symbolic": "reference", "name": "any"}]}]}}, "StaticSymbolResolverHost": {"__symbolic": "interface"}, "StaticSymbolResolver": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbolCache"}, {"__symbolic": "reference", "module": "../summary_resolver", "name": "SummaryResolver", "arguments": [{"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbol"}]}, {"__symbolic": "error", "message": "Expression form not supported", "line": 74, "character": 30}]}], "resolveSymbol": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "getResourcePath": [{"__symbolic": "method"}], "getTypeArity": [{"__symbolic": "method"}], "fileNameToModuleName": [{"__symbolic": "method"}], "recordImportAs": [{"__symbolic": "method"}], "invalidateFile": [{"__symbolic": "method"}], "_resolveSymbolMembers": [{"__symbolic": "method"}], "_resolveSymbolFromSummary": [{"__symbolic": "method"}], "getStaticSymbol": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "_createSymbolsOf": [{"__symbolic": "method"}], "createResolvedSymbol": [{"__symbolic": "method"}], "createExport": [{"__symbolic": "method"}], "reportError": [{"__symbolic": "method"}], "getModuleMetadata": [{"__symbolic": "method"}], "getSymbolByModule": [{"__symbolic": "method"}], "resolveModule": [{"__symbolic": "method"}]}}, "unescapeIdentifier": {"__symbolic": "function", "parameters": ["identifier"], "value": {"__symbolic": "if", "condition": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "identifier"}, "member": "startsWith"}, "arguments": ["___"]}, "thenExpression": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "identifier"}, "member": "substr"}, "arguments": [1]}, "elseExpression": {"__symbolic": "reference", "name": "identifier"}}}}}]