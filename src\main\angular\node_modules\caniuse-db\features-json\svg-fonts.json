{"title": "SVG fonts", "description": "Method of using fonts defined as SVG shapes. Removed from [SVG 2.0](http://www.w3.org/TR/SVG2/changes.html#fonts) and considered as a deprecated feature with support being removed from browsers.", "spec": "http://www.w3.org/TR/SVG/fonts.html", "status": "unoff", "links": [{"url": "http://jeremie.patonnier.net/post/2011/02/07/Why-are-SVG-Fonts-so-different", "title": "Blog post"}, {"url": "http://opentype.info/blog/2010/04/13/the-ipad-and-svg-fonts-in-mobile-safari/", "title": "Blog post on usage for iPad"}], "bugs": [], "categories": ["SVG"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "n #1", "39": "n #1", "40": "n #1", "41": "n #1", "42": "n #1", "43": "n #1", "44": "n #1", "45": "n #1", "46": "n #1", "47": "n #1", "48": "n #1", "49": "n #1", "50": "n #1", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n"}, "safari": {"3.1": "n", "3.2": "y", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "n #1", "26": "n #1", "27": "n #1", "28": "n #1", "29": "n #1", "30": "n #1", "31": "n #1", "32": "n #1", "33": "n #1", "34": "n #1", "35": "n #1", "36": "n #1", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n #2"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "56": "n"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"59": "n #1"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "n #1"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "n #1"}}, "notes": "", "notes_by_num": {"1": "Chrome 38 and newer support SVG fonts only on Windows Vista and XP.", "2": "Supported in Opera Mini in SVG images only, not in HTML."}, "usage_perc_y": 28.04, "usage_perc_a": 0, "ucprefix": false, "parent": "fontface", "keywords": "", "ie_id": "", "chrome_id": "5930075908210688", "firefox_id": "", "webkit_id": "", "shown": true}