{"version": 3, "file": "ngtools_impl.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/ngtools_impl.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH;;;;;GAKG;AACH,8CAAiF;AACjF,sCAAuC;AAIvC,IAAM,kBAAkB,GAAG,iBAAiB,CAAC;AAC7C,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAY3C,0FAA0F;AAC1F,uEAAuE;AACvE;IACE,kBAAoC,IAAY,EAAkB,SAA6B;QAA7B,0BAAA,EAAA,gBAA6B;QAA3D,SAAI,GAAJ,IAAI,CAAQ;QAAkB,cAAS,GAAT,SAAS,CAAoB;IAC/F,CAAC;IAED,2BAAQ,GAAR;QACE,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC;YAC3D,IAAI,CAAC,IAAI;YACN,IAAI,CAAC,IAAI,SAAI,IAAI,CAAC,SAAW,CAAC;IACvC,CAAC;IAEM,mBAAU,GAAjB,UAAkB,KAAa;QAC7B,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;IAClD,CAAC;IACH,eAAC;AAAD,CAAC,AAdD,IAcC;AAdY,4BAAQ;AAiBrB;;;;GAIG;AACH,gCACI,WAAmB,EAAE,IAAqB,EAAE,SAA0B;IACxE,IAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACvD,IAAM,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACpF,IAAM,UAAU,GAAG,OAAK,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAG,CAAC;IAChE,IAAM,SAAS,GAAG,aAAa,CAAC,SAAW,CAAC;IAE5C,2CAA2C;IAC3C,IAAM,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;IACzF,IAAM,MAAM,GAAG,SAAS,CAAC,eAAe,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;IACxF,IAAM,UAAU,GACZ,kCAAkC,CAAC,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAEjF,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CACnC,sCAAsC,SAAuB,EAAE,SAAoB;QAE7E,IAAM,KAAK,GAAW,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACpD,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACnC,SAAS,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;QAE7B,sFAAsF;QACtF,UAAU;QACV,sFAAsF;QACtF,uEAAuE;QACvE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC;QAED,IAAM,gBAAgB,GAAG,SAAS,CAAC,eAAe,CAC9C,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC;QAE3E,IAAM,SAAS,GACX,kCAAkC,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAElF,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;IACnE,CAAC,EACL,EAAE,CAAC,CAAC;IAER,MAAM,CAAC,aAAa,CAAC;AACvB,CAAC;AAvCD,wDAuCC;AAGD;;;GAGG;AACH,wBAAwB,UAAkB,EAAE,cAAsB,EAAE,IAAqB;IACvF,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACrE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,yBAAsB,UAAU,kBAAW,cAAc,QAAI,CAAC,CAAC;IACjF,CAAC;IACD,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAGD;;;GAGG;AACH,sBAAsB,GAAiB,EAAE,KAAgB;IACvD,IAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IACpC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAChE,MAAM,IAAI,KAAK,CACX,iDAA8C,CAAC,mCAA+B;aAC9E,4CAAyC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,UAAO,CAAA;aACvE,OAAI,KAAK,CAAC,gBAAgB,kEAA8D,CAAA;YACxF,sBAAsB,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAED,iBAA2B,IAAkB;IAC3C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAC,IAAW,EAAE,IAAa;QAC5C,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5D,MAAM,CAAO,IAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AALD,0BAKC;AAED;;;;GAIG;AACH,4CACI,YAA0B,EAAE,SAA0B,EAAE,IAAqB,EAC7E,MAAoB;IACtB,IAAM,cAAc,GAAG,oBAAoB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACrE,IAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IACtD,IAAM,SAAS,GAAQ,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,WAAW,IAAI,CAAC,EAAhB,CAAgB,CAAC,CAAC,MAAM,CAAC,UAAC,GAAY,EAAE,CAAM;QACvF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1E,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,SAAS,IAAI,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAEtE,IAAM,UAAU,GACZ,oBAAoB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,UAAC,GAAgB,EAAE,KAAa;QACrE,IAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAM,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpF,GAAG,CAAC,IAAI,CAAC,EAAC,QAAQ,UAAA,EAAE,gBAAgB,kBAAA,EAAC,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEX,IAAM,eAAe,GAChB,OAAiB;SACb,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,YAAY,uBAAY,IAAI,CAAC,CAAC,QAAQ,YAAY,uBAAY,EAA/D,CAA+D,CAAC;SAC5E,GAAG,CAAC,UAAA,CAAC;QACJ,EAAE,CAAC,CAAC,CAAC,YAAY,uBAAY,CAAC;YAAC,MAAM,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAmB,CAAC;IAE7B,MAAM,CAAC,eAAe;SACjB,MAAM,CACH,UAAC,GAAgB,EAAE,CAAe;QAChC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACpF,CAAC,EACD,EAAE,CAAC;SACN,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1B,CAAC;AAGD;;;GAGG;AACH,8BAA8B,YAA0B,EAAE,SAA0B;IAClF,IAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAC,CAAM,IAAK,OAAA,CAAC,YAAY,eAAQ,EAArB,CAAqB,CAAC,CAAC;IAChG,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAI,YAAY,CAAC,IAAI,wBAAqB,CAAC,CAAC;IAC7D,CAAC;IACD,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AAGD;;;GAGG;AACH,wBACI,SAAgB,EAAE,SAA0B,EAAE,MAAoB;IACpE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,SAAkB,EAAE,CAAM;QACjD,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAGD;;;GAGG;AACH,8BAA8B,MAAe;IAC3C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC;QACxB,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,OAAO,CAAC,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpD,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,CAAC,CAAC;QACX,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This is a private API for the ngtools toolkit.\n *\n * This API should be stable for NG 2. It can be removed in NG 4..., but should be replaced by\n * something else.\n */\nimport {AotCompilerHost, StaticReflector, StaticSymbol} from '@angular/compiler';\nimport {NgModule} from '@angular/core';\n\n// We cannot depend directly to @angular/router.\ntype Route = any;\nconst ROUTER_MODULE_PATH = '@angular/router';\nconst ROUTER_ROUTES_SYMBOL_NAME = 'ROUTES';\n\n\n// LazyRoute information between the extractors.\nexport interface LazyRoute {\n  routeDef: RouteDef;\n  absoluteFilePath: string;\n}\nexport type LazyRouteMap = {\n  [route: string]: LazyRoute\n};\n\n// A route definition. Normally the short form 'path/to/module#ModuleClassName' is used by\n// the user, and this is a helper class to extract information from it.\nexport class RouteDef {\n  private constructor(public readonly path: string, public readonly className: string|null = null) {\n  }\n\n  toString() {\n    return (this.className === null || this.className == 'default') ?\n        this.path :\n        `${this.path}#${this.className}`;\n  }\n\n  static fromString(entry: string): RouteDef {\n    const split = entry.split('#');\n    return new RouteDef(split[0], split[1] || null);\n  }\n}\n\n\n/**\n *\n * @returns {LazyRouteMap}\n * @private\n */\nexport function listLazyRoutesOfModule(\n    entryModule: string, host: AotCompilerHost, reflector: StaticReflector): LazyRouteMap {\n  const entryRouteDef = RouteDef.fromString(entryModule);\n  const containingFile = _resolveModule(entryRouteDef.path, entryRouteDef.path, host);\n  const modulePath = `./${containingFile.replace(/^(.*)\\//, '')}`;\n  const className = entryRouteDef.className !;\n\n  // List loadChildren of this single module.\n  const appStaticSymbol = reflector.findDeclaration(modulePath, className, containingFile);\n  const ROUTES = reflector.findDeclaration(ROUTER_MODULE_PATH, ROUTER_ROUTES_SYMBOL_NAME);\n  const lazyRoutes: LazyRoute[] =\n      _extractLazyRoutesFromStaticModule(appStaticSymbol, reflector, host, ROUTES);\n\n  const allLazyRoutes = lazyRoutes.reduce(\n      function includeLazyRouteAndSubRoutes(allRoutes: LazyRouteMap, lazyRoute: LazyRoute):\n          LazyRouteMap {\n            const route: string = lazyRoute.routeDef.toString();\n            _assertRoute(allRoutes, lazyRoute);\n            allRoutes[route] = lazyRoute;\n\n            // StaticReflector does not support discovering annotations like `NgModule` on default\n            // exports\n            // Which means: if a default export NgModule was lazy-loaded, we can discover it, but,\n            //  we cannot parse its routes to see if they have loadChildren or not.\n            if (!lazyRoute.routeDef.className) {\n              return allRoutes;\n            }\n\n            const lazyModuleSymbol = reflector.findDeclaration(\n                lazyRoute.absoluteFilePath, lazyRoute.routeDef.className || 'default');\n\n            const subRoutes =\n                _extractLazyRoutesFromStaticModule(lazyModuleSymbol, reflector, host, ROUTES);\n\n            return subRoutes.reduce(includeLazyRouteAndSubRoutes, allRoutes);\n          },\n      {});\n\n  return allLazyRoutes;\n}\n\n\n/**\n * Try to resolve a module, and returns its absolute path.\n * @private\n */\nfunction _resolveModule(modulePath: string, containingFile: string, host: AotCompilerHost) {\n  const result = host.moduleNameToFileName(modulePath, containingFile);\n  if (!result) {\n    throw new Error(`Could not resolve \"${modulePath}\" from \"${containingFile}\".`);\n  }\n  return result;\n}\n\n\n/**\n * Throw an exception if a route is in a route map, but does not point to the same module.\n * @private\n */\nfunction _assertRoute(map: LazyRouteMap, route: LazyRoute) {\n  const r = route.routeDef.toString();\n  if (map[r] && map[r].absoluteFilePath != route.absoluteFilePath) {\n    throw new Error(\n        `Duplicated path in loadChildren detected: \"${r}\" is used in 2 loadChildren, ` +\n        `but they point to different modules \"(${map[r].absoluteFilePath} and ` +\n        `\"${route.absoluteFilePath}\"). Webpack cannot distinguish on context and would fail to ` +\n        'load the proper one.');\n  }\n}\n\nexport function flatten<T>(list: Array<T|T[]>): T[] {\n  return list.reduce((flat: any[], item: T | T[]): T[] => {\n    const flatItem = Array.isArray(item) ? flatten(item) : item;\n    return (<T[]>flat).concat(flatItem);\n  }, []);\n}\n\n/**\n * Extract all the LazyRoutes from a module. This extracts all `loadChildren` keys from this\n * module and all statically referred modules.\n * @private\n */\nfunction _extractLazyRoutesFromStaticModule(\n    staticSymbol: StaticSymbol, reflector: StaticReflector, host: AotCompilerHost,\n    ROUTES: StaticSymbol): LazyRoute[] {\n  const moduleMetadata = _getNgModuleMetadata(staticSymbol, reflector);\n  const imports = flatten(moduleMetadata.imports || []);\n  const allRoutes: any = imports.filter(i => 'providers' in i).reduce((mem: Route[], m: any) => {\n    return mem.concat(_collectRoutes(m.providers || [], reflector, ROUTES));\n  }, _collectRoutes(moduleMetadata.providers || [], reflector, ROUTES));\n\n  const lazyRoutes: LazyRoute[] =\n      _collectLoadChildren(allRoutes).reduce((acc: LazyRoute[], route: string) => {\n        const routeDef = RouteDef.fromString(route);\n        const absoluteFilePath = _resolveModule(routeDef.path, staticSymbol.filePath, host);\n        acc.push({routeDef, absoluteFilePath});\n        return acc;\n      }, []);\n\n  const importedSymbols =\n      (imports as any[])\n          .filter(i => i instanceof StaticSymbol || i.ngModule instanceof StaticSymbol)\n          .map(i => {\n            if (i instanceof StaticSymbol) return i;\n            return i.ngModule;\n          }) as StaticSymbol[];\n\n  return importedSymbols\n      .reduce(\n          (acc: LazyRoute[], i: StaticSymbol) => {\n            return acc.concat(_extractLazyRoutesFromStaticModule(i, reflector, host, ROUTES));\n          },\n          [])\n      .concat(lazyRoutes);\n}\n\n\n/**\n * Get the NgModule Metadata of a symbol.\n * @private\n */\nfunction _getNgModuleMetadata(staticSymbol: StaticSymbol, reflector: StaticReflector): NgModule {\n  const ngModules = reflector.annotations(staticSymbol).filter((s: any) => s instanceof NgModule);\n  if (ngModules.length === 0) {\n    throw new Error(`${staticSymbol.name} is not an NgModule`);\n  }\n  return ngModules[0];\n}\n\n\n/**\n * Return the routes from the provider list.\n * @private\n */\nfunction _collectRoutes(\n    providers: any[], reflector: StaticReflector, ROUTES: StaticSymbol): Route[] {\n  return providers.reduce((routeList: Route[], p: any) => {\n    if (p.provide === ROUTES) {\n      return routeList.concat(p.useValue);\n    } else if (Array.isArray(p)) {\n      return routeList.concat(_collectRoutes(p, reflector, ROUTES));\n    } else {\n      return routeList;\n    }\n  }, []);\n}\n\n\n/**\n * Return the loadChildren values of a list of Route.\n * @private\n */\nfunction _collectLoadChildren(routes: Route[]): string[] {\n  return routes.reduce((m, r) => {\n    if (r.loadChildren && typeof r.loadChildren === 'string') {\n      return m.concat(r.loadChildren);\n    } else if (Array.isArray(r)) {\n      return m.concat(_collectLoadChildren(r));\n    } else if (r.children) {\n      return m.concat(_collectLoadChildren(r.children));\n    } else {\n      return m;\n    }\n  }, []);\n}\n"]}