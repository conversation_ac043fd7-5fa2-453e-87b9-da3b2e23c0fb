{"_args": [["clap@1.2.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "clap@1.2.0", "_id": "clap@1.2.0", "_inBundle": false, "_integrity": "sha1-WckP4+E3EEdG/xlGmiemNP9oyFc=", "_location": "/clap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clap@1.2.0", "name": "clap", "escapedName": "clap", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/csso"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/clap/-/clap-1.2.0.tgz", "_spec": "1.2.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "bugs": {"url": "https://github.com/lahmatiy/clap/issues"}, "dependencies": {"chalk": "^1.1.3"}, "description": "Command line argument parser", "devDependencies": {"mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/lahmatiy/clap", "keywords": ["cli", "command", "option", "argument", "completion"], "license": "MIT", "main": "index.js", "name": "clap", "repository": {"type": "git", "url": "git+https://github.com/lahmatiy/clap.git"}, "scripts": {"test": "mocha test -R spec"}, "title": "Command line argument parser", "version": "1.2.0"}