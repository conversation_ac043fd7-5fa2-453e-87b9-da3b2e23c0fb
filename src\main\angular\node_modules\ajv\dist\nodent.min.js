/* nodent 3.0.17: NoDent - Asynchronous Javascript language extensions */
require=function e(t,n,r){function i(o,a){if(!n[o]){if(!t[o]){var u="function"==typeof require&&require;if(!a&&u)return u(o,!0);if(s)return s(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return i(n||e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var s="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){t.exports=function(t){switch(parseInt(t.version)){case 2:case 3:t.plugins.asyncawait=e("./acorn-v3");break;case 4:case 5:t.plugins.asyncawait=e("./acorn-v4");break;default:throw new Error("acorn-es7-plugin requires Acorn v2, 3, 4 or 5")}return t}},{"./acorn-v3":2,"./acorn-v4":3}],2:[function(e,t,n){function r(e,t){return e.lineStart>=t}function i(e,t,n){var r=t.input.slice(t.start);return n&&(r=r.replace(p,"$1 $3")),e.test(r)}function s(e,t,n,r){var i=new e.constructor(e.options,e.input,t);if(n)for(var s in n)i[s]=n[s];var o=e,a=i;return["inFunction","inAsyncFunction","inAsync","inGenerator","inModule"].forEach(function(e){e in o&&(a[e]=o[e])}),r&&(i.options.preserveParens=!0),i.nextToken(),i}function o(e,t){var n=function(){};e.extend("initialContext",function(r){return function(){return this.options.ecmaVersion<7&&(n=function(t){e.raise(t.start,"async/await keywords only available when ecmaVersion>=7")}),this.reservedWords=new RegExp(this.reservedWords.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.reservedWordsStrict=new RegExp(this.reservedWordsStrict.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.reservedWordsStrictBind=new RegExp(this.reservedWordsStrictBind.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.inAsyncFunction=t.inAsyncFunction,t.awaitAnywhere&&t.inAsyncFunction&&e.raise(node.start,"The options awaitAnywhere and inAsyncFunction are mutually exclusive"),r.apply(this,arguments)}}),e.extend("shouldParseExportStatement",function(e){return function(){return!("name"!==this.type.label||"async"!==this.value||!i(c,this))||e.apply(this,arguments)}}),e.extend("parseStatement",function(e){return function(n,r){var s=this.start,o=this.startLoc;if("name"===this.type.label)if(i(c,this,!0)){var a=this.inAsyncFunction;try{return this.inAsyncFunction=!0,this.next(),(l=this.parseStatement(n,r)).async=!0,l.start=s,l.loc&&(l.loc.start=o),l.range&&(l.range[0]=s),l}finally{this.inAsyncFunction=a}}else if("object"==typeof t&&t.asyncExits&&i(u,this)){this.next();var l=this.parseStatement(n,r);return l.async=!0,l.start=s,l.loc&&(l.loc.start=o),l.range&&(l.range[0]=s),l}return e.apply(this,arguments)}}),e.extend("parseIdent",function(e){return function(t){var n=e.apply(this,arguments);return this.inAsyncFunction&&"await"===n.name&&0===arguments.length&&this.raise(n.start,"'await' is reserved within async functions"),n}}),e.extend("parseExprAtom",function(e){return function(i){var o,u=this.start,c=this.startLoc,p=e.apply(this,arguments);if("Identifier"===p.type)if("async"!==p.name||r(this,p.end)){if("await"===p.name){var h=this.startNodeAt(p.start,p.loc&&p.loc.start);if(this.inAsyncFunction)return o=this.parseExprSubscripts(),h.operator="await",h.argument=o,h=this.finishNodeAt(h,"AwaitExpression",o.end,o.loc&&o.loc.end),n(h),h;if(this.input.slice(p.end).match(l))return t.awaitAnywhere||"module"!==this.options.sourceType?p:this.raise(p.start,"'await' is reserved within modules");if("object"==typeof t&&t.awaitAnywhere&&(u=this.start,(o=s(this,u-4).parseExprSubscripts()).end<=u))return o=s(this,u).parseExprSubscripts(),h.operator="await",h.argument=o,h=this.finishNodeAt(h,"AwaitExpression",o.end,o.loc&&o.loc.end),this.pos=o.end,this.end=o.end,this.endLoc=o.endLoc,this.next(),n(h),h;if(!t.awaitAnywhere&&"module"===this.options.sourceType)return this.raise(p.start,"'await' is reserved within modules")}}else{var f=this.inAsyncFunction;try{this.inAsyncFunction=!0;var d=this,y=!1,m={parseFunctionBody:function(e,t){try{var n=y;return y=!0,d.parseFunctionBody.apply(this,arguments)}finally{y=n}},raise:function(){try{return d.raise.apply(this,arguments)}catch(e){throw y?e:a}}};if("SequenceExpression"===(o=s(this,this.start,m,!0).parseExpression()).type&&(o=o.expressions[0]),"CallExpression"===o.type&&(o=o.callee),"FunctionExpression"===o.type||"FunctionDeclaration"===o.type||"ArrowFunctionExpression"===o.type)return"SequenceExpression"===(o=s(this,this.start,m).parseExpression()).type&&(o=o.expressions[0]),"CallExpression"===o.type&&(o=o.callee),o.async=!0,o.start=u,o.loc&&(o.loc.start=c),o.range&&(o.range[0]=u),this.pos=o.end,this.end=o.end,this.endLoc=o.endLoc,this.next(),n(o),o}catch(e){if(e!==a)throw e}finally{this.inAsyncFunction=f}}return p}}),e.extend("finishNodeAt",function(e){return function(t,n,r,i){return t.__asyncValue&&(delete t.__asyncValue,t.value.async=!0),e.apply(this,arguments)}}),e.extend("finishNode",function(e){return function(t,n){return t.__asyncValue&&(delete t.__asyncValue,t.value.async=!0),e.apply(this,arguments)}});e.extend("parsePropertyName",function(e){return function(t){t.key&&t.key.name;var i=e.apply(this,arguments);return"Identifier"!==i.type||"async"!==i.name||r(this,i.end)||this.input.slice(i.end).match(l)||(h.test(this.input.slice(i.end))?(i=e.apply(this,arguments),t.__asyncValue=!0):(n(t),"set"===t.kind&&this.raise(i.start,"'set <member>(value)' cannot be be async"),"Identifier"===(i=e.apply(this,arguments)).type&&"set"===i.name&&this.raise(i.start,"'set <member>(value)' cannot be be async"),t.__asyncValue=!0)),i}}),e.extend("parseClassMethod",function(e){return function(t,n,r){var i;n.__asyncValue&&("constructor"===n.kind&&this.raise(n.start,"class constructor() cannot be be async"),i=this.inAsyncFunction,this.inAsyncFunction=!0);var s=e.apply(this,arguments);return this.inAsyncFunction=i,s}}),e.extend("parseMethod",function(e){return function(t){var n;this.__currentProperty&&this.__currentProperty.__asyncValue&&(n=this.inAsyncFunction,this.inAsyncFunction=!0);var r=e.apply(this,arguments);return this.inAsyncFunction=n,r}}),e.extend("parsePropertyValue",function(e){return function(t,n,r,i,s,o){var a=this.__currentProperty;this.__currentProperty=t;var u;t.__asyncValue&&(u=this.inAsyncFunction,this.inAsyncFunction=!0);var c=e.apply(this,arguments);return this.inAsyncFunction=u,this.__currentProperty=a,c}})}var a={},u=/^async[\t ]+(return|throw)/,c=/^async[\t ]+function/,l=/^\s*[():;]/,p=/([^\n])\/\*(\*(?!\/)|[^\n*])*\*\/([^\n])/g,h=/\s*(get|set)\s*\(/;t.exports=o},{}],3:[function(e,t,n){function r(e,t){return e.lineStart>=t}function i(e,t,n){var r=t.input.slice(t.start);return n&&(r=r.replace(c,"$1 $3")),e.test(r)}function s(e,t,n){var r=new e.constructor(e.options,e.input,t);if(n)for(var i in n)r[i]=n[i];var s=e,o=r;return["inFunction","inAsync","inGenerator","inModule"].forEach(function(e){e in s&&(o[e]=s[e])}),r.nextToken(),r}function o(e,t){t&&"object"==typeof t||(t={}),e.extend("parse",function(n){return function(){return this.inAsync=t.inAsyncFunction,t.awaitAnywhere&&t.inAsyncFunction&&e.raise(node.start,"The options awaitAnywhere and inAsyncFunction are mutually exclusive"),n.apply(this,arguments)}}),e.extend("parseStatement",function(e){return function(n,r){var s=this.start,o=this.startLoc;if("name"===this.type.label&&t.asyncExits&&i(a,this)){this.next();var u=this.parseStatement(n,r);return u.async=!0,u.start=s,u.loc&&(u.loc.start=o),u.range&&(u.range[0]=s),u}return e.apply(this,arguments)}}),e.extend("parseIdent",function(e){return function(n){return"module"===this.options.sourceType&&this.options.ecmaVersion>=8&&t.awaitAnywhere?e.call(this,!0):e.apply(this,arguments)}}),e.extend("parseExprAtom",function(e){var n={};return function(r){var i,o=this.start,a=(this.startLoc,e.apply(this,arguments));if("Identifier"===a.type&&"await"===a.name&&!this.inAsync&&t.awaitAnywhere){var u=this.startNodeAt(a.start,a.loc&&a.loc.start);o=this.start;var c={raise:function(){try{return pp.raise.apply(this,arguments)}catch(e){throw n}}};try{if((i=s(this,o-4,c).parseExprSubscripts()).end<=o)return i=s(this,o,c).parseExprSubscripts(),u.argument=i,u=this.finishNodeAt(u,"AwaitExpression",i.end,i.loc&&i.loc.end),this.pos=i.end,this.end=i.end,this.endLoc=i.endLoc,this.next(),u}catch(e){if(e===n)return a;throw e}}return a}});var n={undefined:!0,get:!0,set:!0,static:!0,async:!0,constructor:!0};e.extend("parsePropertyName",function(e){return function(t){var i=t.key&&t.key.name,s=e.apply(this,arguments);"get"===this.value&&(t.__maybeStaticAsyncGetter=!0);return n[this.value]?s:("Identifier"!==s.type||"async"!==s.name&&"async"!==i||r(this,s.end)||this.input.slice(s.end).match(u)?delete t.__maybeStaticAsyncGetter:"set"===t.kind||"set"===s.name?this.raise(s.start,"'set <member>(value)' cannot be be async"):(this.__isAsyncProp=!0,"Identifier"===(s=e.apply(this,arguments)).type&&"set"===s.name&&this.raise(s.start,"'set <member>(value)' cannot be be async")),s)}}),e.extend("parseClassMethod",function(e){return function(t,n,r){var i=e.apply(this,arguments);return n.__maybeStaticAsyncGetter&&(delete n.__maybeStaticAsyncGetter,"get"!==n.key.name&&(n.kind="get")),i}}),e.extend("parseFunctionBody",function(e){return function(t,n){var r=this.inAsync;this.__isAsyncProp&&(t.async=!0,this.inAsync=!0,delete this.__isAsyncProp);var i=e.apply(this,arguments);return this.inAsync=r,i}})}var a=/^async[\t ]+(return|throw)/,u=/^\s*[):;]/,c=/([^\n])\/\*(\*(?!\/)|[^\n*])*\*\/([^\n])/g;t.exports=o},{}],4:[function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function i(e){return 3*e.length/4-r(e)}function s(e){var t,n,i,s,o,a,u=e.length;o=r(e),a=new p(3*u/4-o),i=o>0?u-4:u;var c=0;for(t=0,n=0;t<i;t+=4,n+=3)s=l[e.charCodeAt(t)]<<18|l[e.charCodeAt(t+1)]<<12|l[e.charCodeAt(t+2)]<<6|l[e.charCodeAt(t+3)],a[c++]=s>>16&255,a[c++]=s>>8&255,a[c++]=255&s;return 2===o?(s=l[e.charCodeAt(t)]<<2|l[e.charCodeAt(t+1)]>>4,a[c++]=255&s):1===o&&(s=l[e.charCodeAt(t)]<<10|l[e.charCodeAt(t+1)]<<4|l[e.charCodeAt(t+2)]>>2,a[c++]=s>>8&255,a[c++]=255&s),a}function o(e){return c[e>>18&63]+c[e>>12&63]+c[e>>6&63]+c[63&e]}function a(e,t,n){for(var r,i=[],s=t;s<n;s+=3)r=(e[s]<<16)+(e[s+1]<<8)+e[s+2],i.push(o(r));return i.join("")}function u(e){for(var t,n=e.length,r=n%3,i="",s=[],o=0,u=n-r;o<u;o+=16383)s.push(a(e,o,o+16383>u?u:o+16383));return 1===r?(t=e[n-1],i+=c[t>>2],i+=c[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=c[t>>10],i+=c[t>>4&63],i+=c[t<<2&63],i+="="),s.push(i),s.join("")}n.byteLength=i,n.toByteArray=s,n.fromByteArray=u;for(var c=[],l=[],p="undefined"!=typeof Uint8Array?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,d=h.length;f<d;++f)c[f]=h[f],l[h.charCodeAt(f)]=f;l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},{}],5:[function(e,t,n){},{}],6:[function(e,t,n){"use strict";function r(e){if(e>H)throw new RangeError("Invalid typed array length");var t=new Uint8Array(e);return t.__proto__=i.prototype,t}function i(e,t,n){if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(e)}return s(e,t,n)}function s(e,t,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return e instanceof ArrayBuffer?p(e,t,n):"string"==typeof e?c(e,t):h(e)}function o(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function a(e,t,n){return o(e),e<=0?r(e):void 0!==t?"string"==typeof n?r(e).fill(t,n):r(e).fill(t):r(e)}function u(e){return o(e),r(e<0?0:0|f(e))}function c(e,t){if("string"==typeof t&&""!==t||(t="utf8"),!i.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var n=0|y(e,t),s=r(n),o=s.write(e,t);return o!==n&&(s=s.slice(0,o)),s}function l(e){for(var t=e.length<0?0:0|f(e.length),n=r(t),i=0;i<t;i+=1)n[i]=255&e[i];return n}function p(e,t,n){if(t<0||e.byteLength<t)throw new RangeError("'offset' is out of bounds");if(e.byteLength<t+(n||0))throw new RangeError("'length' is out of bounds");var r;return r=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n),r.__proto__=i.prototype,r}function h(e){if(i.isBuffer(e)){var t=0|f(e.length),n=r(t);return 0===n.length?n:(e.copy(n,0,0,t),n)}if(e){if(W(e)||"length"in e)return"number"!=typeof e.length||G(e.length)?r(0):l(e);if("Buffer"===e.type&&Array.isArray(e.data))return l(e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function f(e){if(e>=H)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+H.toString(16)+" bytes");return 0|e}function d(e){return+e!=e&&(e=0),i.alloc(+e)}function y(e,t){if(i.isBuffer(e))return e.length;if(W(e)||e instanceof ArrayBuffer)return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return M(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return U(e).length;default:if(r)return M(e).length;t=(""+t).toLowerCase(),r=!0}}function m(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return N(this,t,n);case"utf8":case"utf-8":return C(this,t,n);case"ascii":return P(this,t,n);case"latin1":case"binary":return T(this,t,n);case"base64":return _(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return F(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function g(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function v(e,t,n,r,s){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,G(n)&&(n=s?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(s)return-1;n=e.length-1}else if(n<0){if(!s)return-1;n=0}if("string"==typeof t&&(t=i.from(t,r)),i.isBuffer(t))return 0===t.length?-1:b(e,t,n,r,s);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?s?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):b(e,[t],n,r,s);throw new TypeError("val must be string, number or Buffer")}function b(e,t,n,r,i){function s(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}var o=1,a=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;o=2,a/=2,u/=2,n/=2}var c;if(i){var l=-1;for(c=n;c<a;c++)if(s(e,c)===s(t,-1===l?0:c-l)){if(-1===l&&(l=c),c-l+1===u)return l*o}else-1!==l&&(c-=c-l),l=-1}else for(n+u>a&&(n=a-u),c=n;c>=0;c--){for(var p=!0,h=0;h<u;h++)if(s(e,c+h)!==s(t,h)){p=!1;break}if(p)return c}return-1}function x(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var s=t.length;if(s%2!=0)throw new TypeError("Invalid hex string");r>s/2&&(r=s/2);for(var o=0;o<r;++o){var a=parseInt(t.substr(2*o,2),16);if(G(a))return o;e[n+o]=a}return o}function w(e,t,n,r){return z(M(t,e.length-n),e,n,r)}function E(e,t,n,r){return z(V(t),e,n,r)}function S(e,t,n,r){return E(e,t,n,r)}function k(e,t,n,r){return z(U(t),e,n,r)}function A(e,t,n,r){return z(q(t,e.length-n),e,n,r)}function _(e,t,n){return 0===t&&n===e.length?J.fromByteArray(e):J.fromByteArray(e.slice(t,n))}function C(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var s=e[i],o=null,a=s>239?4:s>223?3:s>191?2:1;if(i+a<=n){var u,c,l,p;switch(a){case 1:s<128&&(o=s);break;case 2:128==(192&(u=e[i+1]))&&(p=(31&s)<<6|63&u)>127&&(o=p);break;case 3:u=e[i+1],c=e[i+2],128==(192&u)&&128==(192&c)&&(p=(15&s)<<12|(63&u)<<6|63&c)>2047&&(p<55296||p>57343)&&(o=p);break;case 4:u=e[i+1],c=e[i+2],l=e[i+3],128==(192&u)&&128==(192&c)&&128==(192&l)&&(p=(15&s)<<18|(63&u)<<12|(63&c)<<6|63&l)>65535&&p<1114112&&(o=p)}}null===o?(o=65533,a=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=a}return L(r)}function L(e){var t=e.length;if(t<=Z)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=Z));return n}function P(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function T(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function N(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",s=t;s<n;++s)i+=D(e[s]);return i}function F(e,t,n){for(var r=e.slice(t,n),i="",s=0;s<r.length;s+=2)i+=String.fromCharCode(r[s]+256*r[s+1]);return i}function O(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function $(e,t,n,r,s,o){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>s||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function B(e,t,n,r,i,s){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function R(e,t,n,r,i){return t=+t,n>>>=0,i||B(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),Y.write(e,t,n,r,23,4),n+4}function I(e,t,n,r,i){return t=+t,n>>>=0,i||B(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),Y.write(e,t,n,r,52,8),n+8}function j(e){if((e=e.trim().replace(Q,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function D(e){return e<16?"0"+e.toString(16):e.toString(16)}function M(e,t){t=t||1/0;for(var n,r=e.length,i=null,s=[],o=0;o<r;++o){if((n=e.charCodeAt(o))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(o+1===r){(t-=3)>-1&&s.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&s.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;s.push(n)}else if(n<2048){if((t-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function V(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function q(e,t){for(var n,r,i,s=[],o=0;o<e.length&&!((t-=2)<0);++o)r=(n=e.charCodeAt(o))>>8,i=n%256,s.push(i),s.push(r);return s}function U(e){return J.toByteArray(j(e))}function z(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function W(e){return"function"==typeof ArrayBuffer.isView&&ArrayBuffer.isView(e)}function G(e){return e!==e}var J=e("base64-js"),Y=e("ieee754");n.Buffer=i,n.SlowBuffer=d,n.INSPECT_MAX_BYTES=50;var H=2147483647;n.kMaxLength=H,i.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}(),i.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),i.poolSize=8192,i.from=function(e,t,n){return s(e,t,n)},i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,i.alloc=function(e,t,n){return a(e,t,n)},i.allocUnsafe=function(e){return u(e)},i.allocUnsafeSlow=function(e){return u(e)},i.isBuffer=function(e){return null!=e&&!0===e._isBuffer},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,s=0,o=Math.min(n,r);s<o;++s)if(e[s]!==t[s]){n=e[s],r=t[s];break}return n<r?-1:r<n?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=i.allocUnsafe(t),s=0;for(n=0;n<e.length;++n){var o=e[n];if(!i.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,s),s+=o.length}return r},i.byteLength=y,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},i.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},i.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},i.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?C(this,0,e):m.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e,t,n,r,s){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===s&&(s=this.length),t<0||n>e.length||r<0||s>this.length)throw new RangeError("out of range index");if(r>=s&&t>=n)return 0;if(r>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,s>>>=0,this===e)return 0;for(var o=s-r,a=n-t,u=Math.min(o,a),c=this.slice(r,s),l=e.slice(t,n),p=0;p<u;++p)if(c[p]!==l[p]){o=c[p],a=l[p];break}return o<a?-1:a<o?1:0},i.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},i.prototype.indexOf=function(e,t,n){return v(this,e,t,n,!0)},i.prototype.lastIndexOf=function(e,t,n){return v(this,e,t,n,!1)},i.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return x(this,e,t,n);case"utf8":case"utf-8":return w(this,e,t,n);case"ascii":return E(this,e,t,n);case"latin1":case"binary":return S(this,e,t,n);case"base64":return k(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,n);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Z=4096;i.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r=this.subarray(e,t);return r.__proto__=i.prototype,r},i.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||O(e,t,this.length);for(var r=this[e],i=1,s=0;++s<t&&(i*=256);)r+=this[e+s]*i;return r},i.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||O(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},i.prototype.readUInt8=function(e,t){return e>>>=0,t||O(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return e>>>=0,t||O(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return e>>>=0,t||O(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return e>>>=0,t||O(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return e>>>=0,t||O(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||O(e,t,this.length);for(var r=this[e],i=1,s=0;++s<t&&(i*=256);)r+=this[e+s]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},i.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||O(e,t,this.length);for(var r=t,i=1,s=this[e+--r];r>0&&(i*=256);)s+=this[e+--r]*i;return i*=128,s>=i&&(s-=Math.pow(2,8*t)),s},i.prototype.readInt8=function(e,t){return e>>>=0,t||O(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){e>>>=0,t||O(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(e,t){e>>>=0,t||O(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(e,t){return e>>>=0,t||O(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return e>>>=0,t||O(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return e>>>=0,t||O(e,4,this.length),Y.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return e>>>=0,t||O(e,4,this.length),Y.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return e>>>=0,t||O(e,8,this.length),Y.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return e>>>=0,t||O(e,8,this.length),Y.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,n,r){e=+e,t>>>=0,n>>>=0,r||$(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,s=0;for(this[t]=255&e;++s<n&&(i*=256);)this[t+s]=e/i&255;return t+n},i.prototype.writeUIntBE=function(e,t,n,r){e=+e,t>>>=0,n>>>=0,r||$(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+n},i.prototype.writeUInt8=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,1,255,0),this[t]=255&e,t+1},i.prototype.writeUInt16LE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeUInt16BE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeUInt32LE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},i.prototype.writeUInt32BE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);$(this,e,t,n,i-1,-i)}var s=0,o=1,a=0;for(this[t]=255&e;++s<n&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+n},i.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);$(this,e,t,n,i-1,-i)}var s=n-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+n},i.prototype.writeInt8=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeInt16BE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeInt32LE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},i.prototype.writeInt32BE=function(e,t,n){return e=+e,t>>>=0,n||$(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeFloatLE=function(e,t,n){return R(this,e,t,!0,n)},i.prototype.writeFloatBE=function(e,t,n){return R(this,e,t,!1,n)},i.prototype.writeDoubleLE=function(e,t,n){return I(this,e,t,!0,n)},i.prototype.writeDoubleBE=function(e,t,n){return I(this,e,t,!1,n)},i.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,s=r-n;if(this===e&&n<t&&t<r)for(i=s-1;i>=0;--i)e[i+t]=this[i+n];else if(s<1e3)for(i=0;i<s;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+s),t);return s},i.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var s=e.charCodeAt(0);s<256&&(e=s)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=i.isBuffer(e)?e:new i(e,r),u=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%u]}return this};var Q=/[^+/0-9A-Za-z-_]/g},{"base64-js":4,ieee754:7}],7:[function(e,t,n){n.read=function(e,t,n,r,i){var s,o,a=8*i-r-1,u=(1<<a)-1,c=u>>1,l=-7,p=n?i-1:0,h=n?-1:1,f=e[t+p];for(p+=h,s=f&(1<<-l)-1,f>>=-l,l+=a;l>0;s=256*s+e[t+p],p+=h,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=r;l>0;o=256*o+e[t+p],p+=h,l-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,r),s-=c}return(f?-1:1)*o*Math.pow(2,s-r)},n.write=function(e,t,n,r,i,s){var o,a,u,c=8*s-i-1,l=(1<<c)-1,p=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:s-1,d=r?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),(t+=o+p>=1?h/u:h*Math.pow(2,1-p))*u>=2&&(o++,u/=2),o+p>=l?(a=0,o=l):o+p>=1?(a=(t*u-1)*Math.pow(2,i),o+=p):(a=t*Math.pow(2,p-1)*Math.pow(2,i),o=0));i>=8;e[n+f]=255&a,f+=d,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;e[n+f]=255&o,f+=d,o/=256,c-=8);e[n+f-d]|=128*y}},{}],8:[function(e,t,n){"use strict";function r(e,t){if(Function.prototype.$asyncspawn||Object.defineProperty(Function.prototype,"$asyncspawn",{value:r,enumerable:!1,configurable:!0,writable:!0}),this instanceof Function){var n=this;return new e(function(e,r){function i(t,n){var o;try{if((o=t.call(s,n)).done){if(o.value!==e){if(o.value&&o.value===o.value.then)return o.value(e,r);e&&e(o.value),e=null}return}o.value.then?o.value.then(function(e){i(s.next,e)},function(e){i(s.throw,e)}):i(s.next,o.value)}catch(e){return r&&r(e),void(r=null)}}var s=n.call(t,e,r);i(s.next)})}}var i=function(e,t){for(var n=t.toString(),r="return "+n,i=n.match(/.*\(([^)]*)\)/)[1],s=/['"]!!!([^'"]*)['"]/g,o=[];;){var a=s.exec(r);if(!a)break;o.push(a)}return o.reverse().forEach(function(t){r=r.slice(0,t.index)+e[t[1]]+r.substr(t.index+t[0].length)}),r=r.replace(/\/\*[^*]*\*\//g," ").replace(/\s+/g," "),new Function(i,r)()}({zousan:e("./zousan").toString(),thenable:e("./thenableFactory").toString()},function e(t,n){function r(){return i.apply(t,arguments)}Function.prototype.$asyncbind||Object.defineProperty(Function.prototype,"$asyncbind",{value:e,enumerable:!1,configurable:!0,writable:!0}),e.trampoline||(e.trampoline=function(e,t,n,r,i){return function s(o){for(;o;){if(o.then)return o=o.then(s,r),i?void 0:o;try{if(o.pop){if(o.length)return o.pop()?t.call(e):o;o=n}else o=o.call(e)}catch(e){return r(e)}}}}),e.LazyThenable||(e.LazyThenable="!!!thenable"(),e.EagerThenable=e.Thenable=(e.EagerThenableFactory="!!!zousan")());var i=this;switch(n){case!0:return new e.Thenable(r);case 0:return new e.LazyThenable(r);case void 0:return r.then=r,r;default:return function(){try{return i.apply(t,arguments)}catch(e){return n(e)}}}});i(),r(),t.exports={$asyncbind:i,$asyncspawn:r}},{"./thenableFactory":9,"./zousan":10}],9:[function(e,t,n){t.exports=function(){function e(e){return e&&e instanceof Object&&"function"==typeof e.then}function t(n,r,i){try{var s=i?i(r):r;if(n===s)return n.reject(new TypeError("Promise resolution loop"));e(s)?s.then(function(e){t(n,e)},function(e){n.reject(e)}):n.resolve(s)}catch(e){n.reject(e)}}function n(){}function r(e){}function i(e,t){this.resolve=e,this.reject=t}function s(r,i){var s=new n;try{this._resolver(function(n){return e(n)?n.then(r,i):t(s,n,r)},function(e){t(s,e,i)})}catch(e){t(s,e,i)}return s}function o(e){this._resolver=e,this.then=s}return n.prototype={resolve:r,reject:r,then:i},o.resolve=function(e){return o.isThenable(e)?e:{then:function(t){return t(e)}}},o.isThenable=e,o}},{}],10:[function(e,t,n){(function(e){"use strict";t.exports=function(t){function n(e){if(e){var t=this;e(function(e){t.resolve(e)},function(e){t.reject(e)})}}function r(e,t){if("function"==typeof e.y)try{var n=e.y.call(void 0,t);e.p.resolve(n)}catch(t){e.p.reject(t)}else e.p.resolve(t)}function i(e,t){if("function"==typeof e.n)try{var n=e.n.call(void 0,t);e.p.resolve(n)}catch(t){e.p.reject(t)}else e.p.reject(t)}t=t||"object"==typeof e&&e.nextTick||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,0)};var s=function(){function e(){for(;n.length-r;){try{n[r]()}catch(e){}n[r++]=void 0,r===i&&(n.splice(0,i),r=0)}}var n=[],r=0,i=1024;return function(i){n.push(i),n.length-r==1&&t(e)}}();return n.prototype={resolve:function(e){if(void 0===this.state){if(e===this)return this.reject(new TypeError("Attempt to resolve promise with self"));var t=this;if(e&&("function"==typeof e||"object"==typeof e))try{var n=0,i=e.then;if("function"==typeof i)return void i.call(e,function(e){n++||t.resolve(e)},function(e){n++||t.reject(e)})}catch(e){return void(n||this.reject(e))}this.state=r,this.v=e,t.c&&s(function(){for(var n=0,i=t.c.length;n<i;n++)r(t.c[n],e)})}},reject:function(e){if(void 0===this.state){this.state=i,this.v=e;var t=this.c;t&&s(function(){for(var n=0,r=t.length;n<r;n++)i(t[n],e)})}},then:function(e,t){var r=new n,i={y:e,n:t,p:r};if(void 0===this.state)this.c?this.c.push(i):this.c=[i];else{var o=this.state,a=this.v;s(function(){o(i,a)})}return r}},n.resolve=function(e){if(e&&e instanceof n)return e;var t=new n;return t.resolve(e),t},n.reject=function(e){if(e&&e instanceof n)return e;var t=new n;return t.reject(e),t},n.version="2.3.3-nodent",n}}).call(this,e("_process"))},{_process:31}],11:[function(e,t,n){t.exports=function(e,t,n,r){var i=[[],[]],s=[/(.*)(<script[^>]*>)(.*)/i,/(.*)(<\/script>)(.*)/i],o=0,a=!0;t=t.split("\n");for(var u=0;u<t.length;){var c=s[o].exec(t[u]);c&&0==o&&c[2].match("src=")&&(c=null),c?(1==o?(i[o].push(c[1]),pr=e.compile(i[1].join("\n"),n,3,r.compiler).code,a&&r.runtime&&(a=!1,r.runtime&&i[0].push("Function.prototype.$asyncbind = "+e.$asyncbind.toString()+";\n")),i[0].push(pr),i[1]=[],i[o=0].push(c[2])):(i[o].push(c[1]),i[o].push(c[2]),o=1),t[u]=c[3]):i[o].push(t[u++])}return i[0].join("\n")}},{}],12:[function(e,t,n){(function(n){"use strict";function r(e){if(Array.isArray(e))return e.map(function(e){return r(e)});var t={};return Object.keys(e).forEach(function(n){t[n]=e[n]}),t}function i(e,t){e!==t&&(e.__proto__=Object.getPrototypeOf(t),Object.keys(e).forEach(function(t){t in g||delete e[t]}),Object.keys(t).forEach(function(n){n in e||(e[n]=t[n])}))}function s(){}function o(e){return e?(b.node=e,b):{}}function a(e,t,n){if(!e)return null;if(t&&"object"==typeof t){var r=Object.keys(t);return a(e,function(e){return r.every(function(n){return e[n]==t[n]})})}var i,s={};if(Array.isArray(e)){for(var u=0;u<e.length;u++)if(i=a(e[u],t))return i;return null}var c=n;"function"!=typeof n&&(c=n?function(e){return!0}:function(e){return!o(e).isScope});try{y.treeWalker(e,function(n,r,i){if(t(n))throw s.path=i,s;(n===e||c(n))&&r()})}catch(e){if(e===s)return s.path;throw e}return null}function u(e){return a(e,function(e){return"AwaitExpression"===e.type&&!e.$hidden})}function c(e){return a(e,function(e){return"AwaitExpression"===e.type&&!e.$hidden},function(e){var t=o(e);return!t.isBlockStatement&&!t.isScope})}function l(e){return a(e,{type:"ThisExpression"})}function p(e){if(null===e)return{type:"NullLiteral",value:null,raw:"null"};if(!0===e||!1===e)return{type:"BooleanLiteral",value:e,raw:JSON.stringify(e)};if(e instanceof RegExp){var t=e.toString(),n=t.split("/");return{type:"RegExpLiteral",value:e,raw:t,pattern:n[1],flags:n[2]}}return"number"==typeof e?{type:"NumericLiteral",value:e,raw:JSON.stringify(e)}:{type:"StringLiteral",value:e,raw:JSON.stringify(e)}}function h(e,t){return{type:"Identifier",name:e,loc:t}}function f(e){var t={};for(var n in e)t[n]="string"==typeof e[n]?h(e[n]):e[n];return t}function d(e,t,n,d){function m(e,t){if(n.es6target&&!e.id&&!t&&0===e.type.indexOf("Function"))return e.type="ArrowFunctionExpression",e;if(n.noRuntime){if(t){if(o(t).isLiteral)throw new Error("Nodent: 'noRuntime' option only compatible with -promise and -engine modes");e.body.body=y.part("try {$:0} catch($2) {return $1($2)}",[r(e.body),t,h("$boundEx")]).body}else if(n.es6target&&!e.id&&0===e.type.indexOf("Function"))return e.type="ArrowFunctionExpression",e;return n.es6target&&!e.id?(e.type="ArrowFunctionExpression",e):y.part("$0.bind(this)",[e]).expr}return t?y.part("$0.$1(this,$2)",[e,me.asyncbind,t]).expr:y.part("$0.$1(this)",[e,me.asyncbind]).expr}function g(e,t,n,r){return y.part("var $0 = $1",[h(e),m({type:"FunctionExpression",id:null,generator:!1,expression:!1,params:n||[],body:Array.isArray(t)?{type:"BlockStatement",body:t}:t},r)]).body[0]}function v(t){return e.filename+(t&&t.loc&&t.loc.start?"("+t.loc.start.line+":"+t.loc.start.column+")\t":"\t")}function b(e){return n.babelTree?p(e):{type:"Literal",value:e,raw:JSON.stringify(e)}}function x(e){return e?!n.babelTree||"ClassMethod"!==e.type&&"ObjectMethod"!==e.type?(!n.babelTree&&"MethodDefinition"===e.type||"Property"===e.type&&(e.method||"get"==e.kind||"set"==e.kind))&&o(e.value).isFunction?e.value:null:e:null}function w(e,t){if(!o(e).isFunction)throw new Error("Can only replace 'arguments' in functions");return"$usesArguments"in e||(y.treeWalker(e,function(t,r,i){"Identifier"===t.type&&"arguments"===t.name?(i[0].parent.shorthand&&(i[0].parent.shorthand=!1,i[0].parent.key=h("arguments"),e.$usesArguments=!0),"key"!==i[0].field&&(t.name=n.$arguments,e.$usesArguments=!0)):t!==e&&o(t).isFunction?"ArrowFunctionExpression"===t.type&&(w(t),e.$usesArguments=e.$usesArguments||t.$usesArguments):r()}),e.$usesArguments=e.$usesArguments||!1),e.$usesArguments&&"ArrowFunctionExpression"!==e.type}function E(e){return"string"!=typeof e&&(e=e.type.replace(/Statement|Expression/g,"")),n.generatedSymbolPrefix+e+"_"+ye++}function S(e,t){return e&&(e.$exit=f({$error:t.$error,$return:t.$return})),e}function k(e){for(var t=0;t<e.length;t++){if(e[t].self.$exit)return e[t].self;if(e[t].parent&&e[t].parent.$exit)return e[t].parent}return null}function A(e,t){var n=k(e);if(n)return n.$exit;if(t)for(var r=0;r<t.length;r++)if(t[r])return f(t[r]);return null}function _(e,t){var r=!(n.promises||n.generators||n.engine)&&n.lazyThenables;ce(e),oe(e),D(e),Q(e),Z(e),(r?Y:s)(e),$(e),O(e),U(e,[q,r?s:J,R,I,B]),z(e,t),le(e),fe(e)}function C(e,t){var n={$continuation:!0,type:e?"FunctionDeclaration":"FunctionExpression",id:e?"string"==typeof e?h(e):e:void 0,params:[],body:{type:"BlockStatement",body:r(t)}};return e&&(de[e]={def:n}),n}function L(e){return{type:"AwaitExpression",argument:Q({type:"FunctionExpression",generator:!1,expression:!1,async:!0,params:[],body:{type:"BlockStatement",body:e}}).body.body[0].argument}}function P(e,t){"string"==typeof e&&(e=h(e));var n=y.part("$0.call($1)",[e,[{type:"ThisExpression"}].concat(t||[])]).expr;return e.$thisCall=n,n.$thisCallName=e.name,n}function T(e,t){return{type:"ReturnStatement",argument:P(e,t)}}function N(e,t){return{type:"CallExpression",callee:h(e.$seh+"Finally"),arguments:t?[t]:[]}}function F(e,t){if(Array.isArray(e))return e.map(function(e){return F(e,t)});var r=0,i=0;return y.treeWalker(e,function(e,t,s){if("ReturnStatement"!==e.type||e.$mapped){if("ThrowStatement"===e.type){var a=o(e).isAsync;if(r>0){if(!a)return t(e);delete e.async}return void(!a&&i?t():(e.type="ReturnStatement",e.$mapped=!0,e.argument={type:"CallExpression",callee:A(s,[n]).$error,arguments:[e.argument]}))}return"TryStatement"===e.type?(i++,t(e),void i--):o(e).isFunction?(r++,t(e),void r--):void t(e)}if(r>0){if(!o(e).isAsync)return t(e);delete e.async}return e.$mapped=!0,void(o(e.argument).isUnaryExpression&&"void"===e.argument.operator?e.argument=e.argument.argument:e.argument={type:"CallExpression",callee:A(s,[n]).$return,arguments:e.argument?[e.argument]:[]})},t)}function O(e,t){return Array.isArray(e)?e.map(function(e){return O(e,t)}):(y.treeWalker(e,function(e,t,n){if(t(),"ConditionalExpression"===e.type&&(u(e.alternate)||u(e.consequent))){h(E("condOp"));i(e,L(y.part("if ($0) return $1 ; return $2",[e.test,e.consequent,e.alternate]).body))}},t),e)}function $(e,t){return Array.isArray(e)?e.map(function(e){return $(e,t)}):(y.treeWalker(e,function(e,t,n){if(t(),"LogicalExpression"===e.type&&u(e.right)){var r,s=h(E("logical"+("&&"===e.operator?"And":"Or")));if("||"===e.operator)r="var $0; if (!($0 = $1)) {$0 = $2} return $0";else{if("&&"!==e.operator)throw new Error(v(e)+"Illegal logical operator: "+e.operator);r="var $0; if ($0 = $1) {$0 = $2} return $0"}i(e,L(y.part(r,[s,e.left,e.right]).body))}},t),e)}function B(e,t,n){if("SwitchCase"!==e.type&&o(e).isBlockStatement)for(var r=0;r<e.body.length;){var i=e.body[r];if("SwitchCase"!==i.type&&o(i).isBlockStatement)if(he(i.body))if(c(i)){var s=E(i),a=e.body.splice(r+1,e.body.length-(r+1));if(a.length){var u=C(s,a);delete de[s],i.body.push(T(s)),e.body.push(u),r++}else r++}else r++;else e.body.splice.apply(e.body,[r,1].concat(i.body));else r++}}function R(e,t,n){if("IfStatement"===e.type&&u([e.consequent,e.alternate])){var s=E(e),a=t[0],c={type:"BlockStatement",body:[e]};if("index"in a){var l=a.index,p=a.parent[a.field].splice(l+1,a.parent[a.field].length-(l+1));if(a.replace(c),p.length){var h=T(s);c.body.push(n(C(s,p))),[e.consequent,e.alternate].forEach(function(e){if(e){var t;(t=o(e).isBlockStatement?e.body[e.body.length-1]:e)&&"ReturnStatement"===t.type||("BlockStatement"!==e.type&&i(e,{type:"BlockStatement",body:[r(e)]}),e.$deferred=!0,e.body.push(r(h))),n(e)}}),e.consequent&&e.alternate&&e.consequent.$deferred&&e.alternate.$deferred||c.body.push(r(h))}}else a.parent[a.field]=c}}function I(e,t,n){if(!e.$switched&&"SwitchStatement"===e.type&&u(e.cases)){e.$switched=!0;var i,s,o,a=t[0];if("index"in a){var c=a.index+1;(o=a.parent[a.field].splice(c,a.parent[a.field].length-c)).length&&"BreakStatement"===o[o.length-1].type&&a.parent[a.field].push(o.pop()),i=E(e),s=T(i),a.parent[a.field].unshift(C(i,o)),a.parent[a.field].push(r(s))}return e.cases.forEach(function(e,t){if("SwitchCase"!==e.type)throw new Error("switch contains non-case/default statement: "+e.type);if(u(e.consequent)){var n=e.consequent[e.consequent.length-1];"BreakStatement"===n.type?e.consequent[e.consequent.length-1]=r(s):"ReturnStatement"===n.type||"ThrowStatement"===n.type||(d(v(e)+"switch-case fall-through not supported - added break. See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification"),e.consequent.push(r(s)))}}),!0}}function j(e){return"ReturnStatement"===e.type||"ThrowStatement"===e.type}function D(t,r){return y.treeWalker(t,function(t,i,s){if("TryStatement"===t.type&&!t.$seh&&(o(s[0].parent).isBlockStatement||(s[0].parent[s[0].field]={type:"BlockStatement",body:[t]}),t.$seh=E("Try")+"_",t.$containedAwait=!!u(t),t.$finallyExit=t.finalizer&&V(s)&&!!a(t.finalizer.body,j),t.$containedAwait||t.$finallyExit)){t.$needsMapping=!r||!t.$finallyExit;var c=A(s,[n]);if(t.finalizer&&!t.handler){var l=h(E("exception"));t.handler={type:"CatchClause",param:l,body:{type:"BlockStatement",body:[{type:"ThrowStatement",argument:l}]}}}if(!t.handler&&!t.finalizer){var p=new SyntaxError(v(t.value)+"try requires catch and/or finally clause",e.filename,t.start);throw p.pos=t.start,p.loc=t.loc.start,p}t.finalizer?(S(t.block,{$error:t.$seh+"Catch",$return:N(t,c.$return)}),S(t.handler,{$error:N(t,c.$error),$return:N(t,c.$return)})):S(t.block,{$error:t.$seh+"Catch",$return:c.$return})}i()}),t}function M(e,t){for(var n=0;n<e.length;n++)if(!o(e[n]).isDirective)return void e.splice.apply(e,[n,0].concat(t));e.splice.apply(e,[e.length,0].concat(t))}function V(e){for(var t=0;t<e.length;t++)if(o(e[t].self).isFunction)return e[t].self.async||e[t].self.$wasAsync;return!1}function q(t,i,s){if(t.$needsMapping){var o,a,u,c=i[0];if(!("index"in c))throw new Error(e.filename+" - malformed try/catch blocks");var l=c.index+1,p=c.parent[c.field].splice(l,c.parent[c.field].length-l);if(p.length){var f=s(g(a=t.$seh+"Post",p,[],A(i,[n]).$error));c.parent[c.field].splice(c.index,0,f),o=y.part("return $0()",[t.finalizer?N(t,h(a)):h(a)]).body[0]}else t.finalizer&&(o=T(N(t)));t.$mapped=!0,o&&(t.block.body.push(r(o)),t.handler.body.body.push(r(o)));var d=A(i,[n]);if(t.handler){var v=h(t.$seh+"Catch");u=r(t.handler.body);var b=g(v.name,u,[r(t.handler.param)],t.finalizer?N(t,d.$error):d.$error);t.handler.body.body=[{type:"CallExpression",callee:v,arguments:[r(t.handler.param)]}],c.parent[c.field].splice(c.index,0,b)}if(t.finalizer){var x={exit:h(t.$seh+"Exit"),value:h(t.$seh+"Value"),body:r(t.finalizer.body)},w=y.part("(function ($value) {                                             {$:body}                                                  return $exit && ($exit.call(this, $value));           })",x).expr,E={type:"VariableDeclaration",kind:"var",declarations:[{type:"VariableDeclarator",id:h(t.$seh+"Finally"),init:m({type:"FunctionExpression",params:[x.exit],id:null,body:{type:"BlockStatement",body:[{type:"ReturnStatement",argument:m(w,d.$error)}]}})}]};M(c.parent[c.field],[E]);var S=y.part("return $0()",[t.finalizer?N(t,h(a)):h(a)]).body[0];u.body[u.length-1]=S,t.block.body[t.block.body.length-1]=S,delete t.finalizer}}}function U(e,t,n){function r(e,n){return y.treeWalker(e,function(e,n,s){function o(e){return r(e,s)}i.indexOf(e)<0&&(i.push(e),t.forEach(function(t){t(e,s,o)})),n()},n)}var i=[];return r(e,n),e}function z(e,t,s){return y.treeWalker(e,function(e,a,c){if("IfStatement"==e.type&&("BlockStatement"!=e.consequent.type&&u(e.consequent)&&(e.consequent={type:"BlockStatement",body:[e.consequent]}),e.alternate&&"BlockStatement"!=e.alternate.type&&u(e.alternate)&&(e.alternate={type:"BlockStatement",body:[e.alternate]})),a(),o(e).isAwait){var l=e.loc;if(!(t=t||c.some(function(e){return e.self&&e.self.$wasAsync}))||"warn"===t){var p=v(e)+"'await' used inside non-async function. ";n.promises?p+="'return' value Promise runtime-specific":p+="'return' value from await is synchronous",d(p+". See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification")}var f=c[0].parent;"LogicalExpression"===f.type&&f.right===e&&d(v(e.argument)+"'"+printNode(f)+"' on right of "+f.operator+" will always evaluate '"+printNode(e.argument)+"'"),"ConditionalExpression"===f.type&&f.test!==e&&d(v(e.argument)+"'"+printNode(f)+"' will always evaluate '"+printNode(e.argument)+"'");var y=h(E("await")),g=r(e.argument);i(e,y);for(var b,x,w=1;w<c.length;w++)if(x=o(c[w].self).isBlockStatement){b=c[w-1];break}if(!b)throw new Error(v(e)+"Illegal await not contained in a statement");var S,k,_=A(c,[s,n]),C=b.index,L=x.splice(C,x.length-C).slice(1);"ReturnStatement"===b.self.type&&"CallExpression"===b.self.argument.type&&1===b.self.argument.arguments.length&&b.self.argument.arguments[0].name===y.name?k=S=b.self.argument.callee:"Identifier"===b.self.type||b.self.name===y.name||"ExpressionStatement"===b.self.type&&"Identifier"===b.self.expression.type&&b.self.expression.name===y.name?S=L.length?{type:"FunctionExpression",params:[r(y)],body:z({type:"BlockStatement",body:r(L)},t,_)}:{type:"FunctionExpression",params:[],body:{type:"BlockStatement",body:[]}}:(L.unshift(b.self),S={type:"FunctionExpression",params:[r(y)],body:z({type:"BlockStatement",body:r(L)},t,_)}),k||(k=S?m(S,_.$error):{type:"FunctionExpression",params:[],body:{type:"BlockStatement",body:[]}}),n.wrapAwait&&(g={type:"CallExpression",arguments:[g],callee:n.promises||n.generators?{type:"MemberExpression",object:h("Promise"),property:h("resolve")}:{type:"MemberExpression",object:h("Object"),property:h("$makeThenable")}});var P={type:"CallExpression",callee:{type:"MemberExpression",object:g,property:h("then",l),computed:!1},arguments:[k,_.$error]};x.push({loc:l,type:"ReturnStatement",argument:P})}return!0}),e}function W(e,t){var n=e.$label;delete e.$label;var r=h(E("idx")),s=h(E("in")),a=y.part("var $0,$1 = [];for ($0 in $2) $1.push($0)",[r,s,e.right]).body,u=y.part("for ($0; $1.length;){ $2 = $1.shift(); $:3 ; }",[e.left,s,"VariableDeclaration"===e.left.type?e.left.declarations[0].id:e.left,e.body]).body[0];u.$label=n;for(var c=0;c<t.length;c++)if(o(t[c].parent).isBlockStatement){t[c].parent[t[c].field].splice(t[c].index,0,a[0],a[1]);break}i(e,u)}function G(e,t){"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[e.body]});var n,r,i=y.part("[$0[Symbol.iterator]()]",[e.right]).expr;if("VariableDeclaration"===e.left.type){"const"===e.left.kind&&(e.left.kind="let"),n=e.left.declarations[0].id;var s=ie(e.left.declarations[0].id);r=h(E("iterator_"+s.join("_"))),e.left.declarations=s.map(function(e){return{type:"VariableDeclarator",id:h(e)}}),e.left.declarations.push({type:"VariableDeclarator",id:r,init:i}),e.init=e.left}else{var o={type:"VariableDeclaration",kind:"var",declarations:[{type:"VariableDeclarator",id:r=h(E("iterator_"+(n=e.left).name)),init:i}]};e.init=o}e.type="ForStatement",e.test=y.part("!($0[1] = $0[0].next()).done && (($1 = $0[1].value) || true)",[r,n]).expr,delete e.left,delete e.right}function J(e,t,r){function i(e){return"AwaitExpression"===e.type&&!e.$hidden||c&&("BreakStatement"===e.type||"ContinueStatement"===e.type)&&e.label}var s=e.$depth;"ForInStatement"===e.type&&u(e)?W(e,t):"ForOfStatement"===e.type&&u(e)&&G(e,t);var c=t.some(function(e){return"$label"in e.self&&"ForStatement"===e.self.type&&e.self.$mapped});if(!e.$mapped&&o(e).isLoop&&a(e,i)){t[0].self.$mapped=!0;var l=[],p=e.init,f=e.test||b(!0),d=e.update,m=e.body;d&&(d={type:"ExpressionStatement",expression:d}),p&&(o(p).isStatement||(p={type:"ExpressionStatement",expression:p}),l.push(p));var g,v;e.$label?(v=e.$label.name,g=t[1]):(v=ye++,g=t[0]);var x,w,E=h((v=n.generatedSymbolPrefix+"Loop_"+v)+"_trampoline"),S=h(v),k=d?h(v+"_step"):S,_=h(v+"_exit");h("q"),h("$exception");if("index"in g){var L=g.index;w=g.parent[g.field].splice(L+1,g.parent[g.field].length-(L+1))}else w=[];x=C(_,w);var P={type:"ReturnStatement",argument:S},T={type:"ReturnStatement",argument:k},N={type:"ReturnStatement",argument:{type:"ArrayExpression",elements:[b(1)]}};y.treeWalker(m,function(e,t,n){if(o(e).isFunction||o(e).isLoop)return!0;if("BreakStatement"===e.type||"ContinueStatement"===e.type)if(e.label)for(var r=(n.filter(function(e){return"$label"in e.self}).map(function(e,t){return e.self.$label&&e.self.$label.name})),i=[],s=0;s<r.length;s++){if(r[s]===e.label.name){"BreakStatement"===e.type&&i.push(b(1)),n[0].replace({type:"ReturnStatement",argument:{type:"ArrayExpression",elements:i.reverse()}});break}i.push(b(0))}else"BreakStatement"===e.type?n[0].replace(N):n[0].replace(T);else t()},t),m="BlockStatement"===m.type?m.body.slice(0):[m],m="DoWhileStatement"===e.type?m.concat({type:"IfStatement",test:{type:"UnaryExpression",argument:f,prefix:!0,operator:"!"},consequent:N,alternate:T}):[{type:"IfStatement",test:f,consequent:{type:"BlockStatement",body:m.concat(T)},alternate:N}],n.noRuntime&&l.push({type:"VariableDeclaration",declarations:[{type:"VariableDeclarator",id:E}],kind:"var"});var F,O=A(t,[n]).$error;F=n.noRuntime?y.part(n.es6target?"($idTrampoline = ((q) => {     $$setMapped: while (q) {          if (q.then)  "+(1===s?"             return void q.then($idTrampoline, $exit); ":"             return q.then($idTrampoline, $exit); ")+"         try {              if (q.pop)                   if (q.length)                   return q.pop() ? $idContinuation.call(this) : q;               else                   q = $idStep;               else                   q = q.call(this)           } catch (_exception) {              return $exit(_exception);           }      } }))($idIter)":"($idTrampoline = (function (q) {     $$setMapped: while (q) {          if (q.then)  "+(1===s?"             return void q.then($idTrampoline, $exit); ":"             return q.then($idTrampoline, $exit); ")+"         try {              if (q.pop)                   if (q.length)                   return q.pop() ? $idContinuation.call(this) : q;               else                   q = $idStep;               else                   q = q.call(this)           } catch (_exception) {              return $exit(_exception);           }      } }).bind(this))($idIter)",{setMapped:function(e){return e.$mapped=!0,e},idTrampoline:E,exit:O,idIter:S,idContinuation:_,idStep:k}).expr:y.part("(Function.$0.trampoline(this,$1,$2,$3,$5)($4))",[me.asyncbind,_,k,O,S,b(1===s)]).expr,l.push({type:"ReturnStatement",argument:F}),l.push({$label:e.$label,type:"FunctionDeclaration",id:S,params:[],body:{type:"BlockStatement",body:m}}),d&&l.push({type:"FunctionDeclaration",id:k,params:[],body:{type:"BlockStatement",body:[d,P]}}),!p||"VariableDeclaration"!==p.type||"let"!==p.kind&&"const"!==p.kind?(l.push(x),t[0].replace(l.map(r))):("const"===p.kind&&(p.kind="let"),t[0].replace([{type:"BlockStatement",body:l.map(r)},r(x)]))}}function Y(e,t){return y.treeWalker(e,function(e,t,s){function a(e){return{type:"ReturnStatement",argument:{type:"UnaryExpression",operator:"void",prefix:!0,argument:P(e||S)}}}function c(e,t){if("BreakStatement"===e.type)i(e,r(A(e.label&&n.generatedSymbolPrefix+"Loop_"+e.label.name+"_exit")));else if("ContinueStatement"===e.type)i(e,r(a(e.label&&n.generatedSymbolPrefix+"Loop_"+e.label.name+"_next")));else if(o(e).isFunction)return!0;t()}"ForInStatement"===e.type&&u(e)?W(e,s):"ForOfStatement"===e.type&&u(e)&&G(e,s),t();var p;if(o(e).isLoop&&u(e)){var f=e.init,d=e.test||b(!0),g=e.update,v=e.body,x=l(v);f&&(o(f).isStatement||(f={type:"ExpressionStatement",expression:f})),g=g&&{type:"ExpressionStatement",expression:g},v=o(v).isBlockStatement?r(v).body:[r(v)];var w=e.$label&&e.$label.name;w="Loop_"+(w||ye++);var E=n.generatedSymbolPrefix+(w+"_exit"),S=n.generatedSymbolPrefix+(w+"_next"),k=h(n.generatedSymbolPrefix+w),A=function(e){return{type:"ReturnStatement",argument:{type:"UnaryExpression",operator:"void",prefix:!0,argument:{type:"CallExpression",callee:h(e||E),arguments:[]}}}},_=C(S,[{type:"ReturnStatement",argument:{type:"CallExpression",callee:x?m(k):k,arguments:[h(E),me.error]}}]);g&&_.body.body.unshift(g);for(var L=0;L<v.length;L++)y.treeWalker(v[L],c);v.push(r(a()));var T={type:"FunctionExpression",id:k,params:[h(E),me.error],body:{type:"BlockStatement",body:[_]}};if("DoWhileStatement"===e.type)_.body.body=[{type:"IfStatement",test:r(d),consequent:{type:"BlockStatement",body:r(_.body.body)},alternate:{type:"ReturnStatement",argument:{type:"CallExpression",callee:h(E),arguments:[]}}}],T.body.body=[_].concat(v);else{var N={type:"IfStatement",test:r(d),consequent:{type:"BlockStatement",body:v},alternate:r(A())};T.body.body.push(N)}var F={type:"ExpressionStatement",expression:{type:"AwaitExpression",argument:m(T,b(0))}};for(!f||"VariableDeclaration"!==f.type||"let"!==f.kind&&"const"!==f.kind||("const"===f.kind&&(f.kind="let"),F={type:"BlockStatement",body:[r(f),F]},f=null),p=0;p<s.length;p++){var O=s[p];if("index"in O)return f?O.parent[O.field].splice(O.index,1,r(f),F):O.parent[O.field][O.index]=F,!0}}return!0},t),e}function H(e){if(!o(e).isFunction)throw new Error("Cannot examine non-Function node types for async exits");return a(e.body,function(e){return"Identifier"===e.type&&(e.name===n.$return||e.name===n.$error)||j(e)&&o(e).isAsync},function(e){return!(o(e).isFunction&&(e.$wasAsync||o(e).isAsync))})}function Z(t){return y.treeWalker(t,function(t,r,i){var s=x(t);if(r(),s&&o(s).isAsync){if("set"==t.kind){var a=new SyntaxError(v(s)+"method 'async set' cannot be invoked",e.filename,t.start);throw a.pos=t.start,a.loc=t.loc.start,a}s.async=!1;var u=w(s);H(s)||0!==s.body.body.length&&"ReturnStatement"===s.body.body[s.body.body.length-1].type||s.body.body.push({type:"ReturnStatement"});var c=m(S({type:"FunctionExpression",params:[me.return,me.error],body:Z(F(s.body,i)),$wasAsync:!0},n),n.promises||n.generators||n.engine?null:b(!n.lazyThenables||0));n.promises?s.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:{type:"NewExpression",callee:h("Promise"),arguments:[c]}}]}:s.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:c}]},u&&M(s.body.body,[ge])}})}function Q(e){return y.treeWalker(e,function(e,t,r){if(t(),o(e).isAsync&&o(e).isFunction){var i;(i=x(r[0].parent))&&o(i).isAsync&&"get"===r[0].parent.kind&&ee(r[0].parent.key),delete e.async;var s,a=w(e);return o(e.body).isBlockStatement?(H(e)||0!==e.body.body.length&&"ReturnStatement"===e.body.body[e.body.body.length-1].type||e.body.body.push({type:"ReturnStatement"}),s={type:"BlockStatement",body:e.body.body.map(function(e){return F(e,r)})}):(s={type:"BlockStatement",body:[F({type:"ReturnStatement",argument:e.body},r)]},e.expression=!1),s=m(S({type:"FunctionExpression",params:[me.return,me.error],body:s,$wasAsync:!0},n),n.promises||n.generators||n.engine?null:b(!n.lazyThenables||0)),n.promises&&(s={type:"NewExpression",callee:h("Promise"),arguments:[s]}),s={type:"BlockStatement",body:[{type:"ReturnStatement",loc:e.loc,argument:s}]},a&&M(s.body,[ge]),void(e.body=s)}}),e}function X(e){if(Array.isArray(e))return e.map(X);var t=0;return y.treeWalker(e,function(e,n,r){if("ThrowStatement"!==e.type&&"ReturnStatement"!==e.type||e.$mapped){if(o(e).isFunction)return t++,n(e),void t--}else if(t>0&&o(e).isAsync)return delete e.async,e.argument={type:"CallExpression",callee:"ThrowStatement"===e.type?me.error:me.return,arguments:e.argument?[e.argument]:[]},void(e.type="ReturnStatement");n(e)})}function K(e,t){if(n.noRuntime)throw new Error("Nodent: 'noRuntime' option only compatible with -promise and -engine modes");return y.part("{ return (function*($return,$error){ $:body }).$asyncspawn(Promise,this) }",{return:me.return,error:me.error,asyncspawn:me.asyncspawn,body:X(e).concat(t?[{type:"ReturnStatement",argument:me.return}]:[])}).body[0]}function ee(e){e.$asyncgetwarninig||(e.$asyncgetwarninig=!0,d(v(e)+"'async get "+printNode(e)+"(){...}' is non-standard. See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification"))}function te(e,t){function s(e,t){y.treeWalker(e,function(n,r,i){n!==e&&o(n).isFunction||(o(n).isAwait?t?(n.$hidden=!0,r()):(delete n.operator,n.delegate=!1,n.type="YieldExpression",r()):r())})}function a(e){var t=n.promises;n.promises=!0,_(e,!0),n.promises=t}function u(e){return"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:e.body}]}),e}function c(e,n){n.$asyncexitwarninig||(n.$asyncexitwarninig=!0,d(v(e)+"'async "+{ReturnStatement:"return",ThrowStatement:"throw"}[e.type]+"' not possible in "+(t?"engine":"generator")+" mode. Using Promises for function at "+v(n)))}y.treeWalker(e,function(e,n,r){n();var l,p,h;if(o(e).isAsync&&o(e).isFunction){var f;(f=x(r[0].parent))&&o(f).isAsync&&"get"===r[0].parent.kind&&ee(r[0].parent.key),(p=H(e))?(c(p,e.body),a(e)):t?"get"!==r[0].parent.kind&&s(e,!0):(delete(l=e).async,h=w(l),s(l,!1),(l=u(l)).body=K(l.body.body,p),h&&M(l.body.body,[ge]),l.id&&"ExpressionStatement"===r[0].parent.type?(l.type="FunctionDeclaration",r[1].replace(l)):r[0].replace(l))}else(l=x(e))&&o(l).isAsync&&((p=H(l))?(c(p,l),a(e)):t&&"get"!==e.kind||(t?a(e):(e.async=!1,h=w(l),s(l,!1),i(l,u(l)),l.body=K(l.body.body,p)),h&&M(l.body.body,[ge])))});var l=r(n);return n.engine=!1,n.generators=!1,ce(e),oe(e),D(e,l.engine),$(e),O(e),U(e,[q,J,R,I,B]),z(e,"warn"),n.engine=l.engine,n.generators=l.generators,e}function ne(e,t,n){var r=[];return y.treeWalker(e,function(i,s,a){if(i===e)return s();t(i,a)?r.push([].concat(a)):n||o(i).isScope||s()}),r}function re(e,t){var n=[],i={};if((e=e.filter(function(e){return"ExportNamedDeclaration"!==e[0].parent.type})).length){var s={};e.forEach(function(e){function t(e){e in s?i[e]=o.declarations[u]:s[e]=o.declarations[u]}for(var n=e[0],o=n.self,a=(o.kind,[]),u=0;u<o.declarations.length;u++){var c=o.declarations[u];if(ie(c.id).forEach(t),c.init){var l={type:"AssignmentExpression",left:r(c.id),operator:"=",right:r(c.init)};a.push(l)}}if(0==a.length)n.remove();else{var p=a.length>1?{type:"SequenceExpression",expressions:a}:a[0];"For"!==n.parent.type.slice(0,3)&&(p={type:"ExpressionStatement",expression:p}),n.replace(p)}});var o=Object.keys(s);o.length&&(o=o.map(function(e){return{type:"VariableDeclarator",id:h(e),loc:s[e].loc,start:s[e].start,end:s[e].end}}),n[0]&&"VariableDeclaration"===n[0].type?n[0].declarations=n[0].declarations.concat(o):n.unshift({type:"VariableDeclaration",kind:t,declarations:o}))}return{decls:n,duplicates:i}}function ie(e){if(!e)return[];if(Array.isArray(e))return e.reduce(function(e,t){return e.concat(ie(t.id))},[]);switch(e.type){case"Identifier":return[e.name];case"AssignmentPattern":return ie(e.left);case"ArrayPattern":return e.elements.reduce(function(e,t){return e.concat(ie(t))},[]);case"ObjectPattern":return e.properties.reduce(function(e,t){return e.concat(ie(t))},[]);case"ObjectProperty":case"Property":return ie(e.value);case"RestElement":case"RestProperty":return ie(e.argument)}}function se(e){function t(e){d(v(e)+"Possible assignment to 'const "+printNode(e)+"'")}function n(e){switch(e.type){case"Identifier":"const"===r[e.name]&&t(e);break;case"ArrayPattern":e.elements.forEach(function(e){"const"===r[e.name]&&t(e)});break;case"ObjectPattern":e.properties.forEach(function(e){"const"===r[e.key.name]&&t(e)})}}var r={};y.treeWalker(e,function(e,t,i){var s=o(e).isBlockStatement;if(s){r=Object.create(r);for(var a=0;a<s.length;a++)if("VariableDeclaration"===s[a].type)for(var u=0;u<s[a].declarations.length;u++)ie(s[a].declarations[u].id).forEach(function(e){r[e]=s[a].kind})}t(),"AssignmentExpression"===e.type?n(e.left):"UpdateExpression"===e.type&&n(e.argument),s&&(r=Object.getPrototypeOf(r))})}function oe(e){function t(e){return a(e,function(e){return"AssignmentExpression"===e.type})}function n(e){return function(t,n){if("VariableDeclaration"===t.type&&(t.kind=t.kind||"var")&&e.indexOf(t.kind)>=0){var r=n[0];return("left"!=r.field||"ForInStatement"!==r.parent.type&&"ForOfStatement"!==r.parent.type)&&("init"!=r.field||"ForStatement"!==r.parent.type||"const"!==t.kind&&"let"!==t.kind)}}}function s(e,t){return!("FunctionDeclaration"!==e.type||!e.id)&&(o(e).isAsync||!e.$continuation)}var c={TemplateLiteral:function(e){return e.expressions},NewExpression:function(e){return e.arguments},CallExpression:function(e){return e.arguments},SequenceExpression:function(e){return e.expressions},ArrayExpression:function(e){return e.elements},ObjectExpression:function(e){return e.properties.map(function(e){return e.value})}};y.treeWalker(e,function(e,n,s){var a;if(n(),e.type in c&&!e.$hoisted){var s,l=c[e.type](e),p=[];for(a=0;a<l.length;a++)if(!o(l[a]).isScope){if((s=u(l[a]))&&function(e){p.length&&(e.argument={type:"SequenceExpression",expressions:p.map(function(e){var t=r(e);return i(e,e.left),t}).concat(e.argument)},p=[])}(s[0].self),!u(l.slice(a+1)))break;(s=t(l[a]))&&p.push(s[0].self)}}else if("VariableDeclaration"===e.type)for(a=e.declarations.length-1;a>0;a--)if(e.declarations[a]&&e.declarations[a].init&&u(e.declarations[a].init)){var h={type:"VariableDeclaration",kind:e.kind,declarations:e.declarations.splice(a)},f=s[0];if(!("index"in f))throw new Error("VariableDeclaration not in a block");f.parent[f.field].splice(f.index+1,0,h)}}),se(e);var l=!1;return y.treeWalker(e,function(e,t,r){var i=l;if(l=l||pe(e),o(e).isBlockStatement){if(u(e)){var a,c,p,f,y;if(!r[0].parent||o(r[0].parent).isScope){var m={},g={};(c=ne(e,n(["const"]),!1)).forEach(function(e){e[0].self.declarations.forEach(function(e){ie(e.id).forEach(function(t){m[t]||g[t]?(delete m[t],g[t]=e):m[t]=e})})}),c.forEach(function(e){for(var t=0;t<e.length&&!o(e[t].parent).isBlockStatement;t++);var n=e[t];n.append({type:"ExpressionStatement",expression:{type:"SequenceExpression",expressions:e[0].self.declarations.map(function(e){var t={type:"AssignmentExpression",operator:"=",left:e.id,right:e.init};return e.init=null,t})}});var r=ie(e[0].self.declarations),i=r.filter(function(e){return e in g});i.length&&e[0].append({type:"VariableDeclaration",kind:"let",declarations:i.map(function(e){return{type:"VariableDeclarator",id:h(e)}})}),e[0].self.kind="var",(i=r.filter(function(e){return e in m})).length?e[0].self.declarations=i.map(function(e){return{type:"VariableDeclarator",id:h(e)}}):n.remove()}),p=ne(e,n(["var"]),!1),f=[]}else f=ne(e,n(["const"]),!0);f=f.concat(ne(e,n(["let"]),!0)),a=ne(e,function(e){return o(e).isDirective},!0),y=ne(e,s,l),p=p?re(p,"var"):{duplicates:{},decls:[]},f=f?re(f,"let"):{duplicates:{},decls:[]},Object.keys(p.duplicates).forEach(function(e){d(v(p.duplicates[e])+"Duplicate declaration '"+printNode(p.duplicates[e])+"'")}),Object.keys(f.duplicates).forEach(function(e){d(v(f.duplicates[e])+"Duplicate declaration '"+printNode(f.duplicates[e])+"'")}),y=y.map(function(e){var t,n=e[0];return o(n.self).isAsync?(t=n.self.id.name,o(n.parent).isBlockStatement?(n.self.type="FunctionDeclaration",n.remove(),n.self):n.replace(h(t))):(t=n.self.id.name,"FunctionDeclaration"===n.self.type?n.remove():n.replace(h(t)))}),((a=a.map(function(e){return e[0].remove()})).length||p.decls.length||f.decls.length||y.length)&&(e.body=a.concat(p.decls).concat(f.decls).concat(y).concat(e.body))}l=i}if(t(),"ForOfStatement"===e.type||"ForInStatement"===e.type||o(e).isLoop){for(var b=0,x=0;x<r.length;x++)if("ForOfStatement"===r[x].self.type||"ForInStatement"===r[x].self.type||o(r[x].self).isLoop)b+=1;else if(o(r[x].self).isFunction)break;e.$depth=b,"LabeledStatement"===r[0].parent.type?e.$label=r[0].parent.label:e.$label=null}return!0}),e}function ae(e,t){function n(){return e.$superID=e.$superID||h("$super$"+ye++)}return function(e){(e=x(e))&&o(e).isAsync&&(!t||"get"===e.kind||a(e,function(e){return o(e).isFunction&&a(e,function(e){return"Super"===e.type})&&a(e,function(e){return e.async&&("ReturnStatement"===e.type||"ThrowStatement"===e.type)})},!0))&&y.treeWalker(e.body,function(e,t,r){var i;o(e).isClass||(t(),"Super"===e.type&&("MemberExpression"===r[0].parent.type?"CallExpression"===r[1].parent.type&&"callee"===r[1].field?(i=y.part("this.$super($field).call(this,$args)",{super:n(),field:r[0].parent.computed?r[0].parent.property:b(r[0].parent.property.name),args:r[1].parent.arguments}).expr,r[2].replace(i)):(i=y.part("this.$super($field)",{super:n(),field:r[0].parent.computed?r[0].parent.property:b(r[0].parent.property.name)}).expr,r[1].replace(i)):d(v(e)+"'super' in async methods must be deferenced. 'async constructor()'/'await super()' not valid.")))})}}function ue(e,t){return y.treeWalker(e,function(e,r,i){if(r(),("ClassDeclaration"===e.type||"ClassExpression"===e.type)&&(e.body.body.forEach(ae(e,t)),e.$superID)){var s=y.part("(function($field) { return super[$field] })",{field:h("$field")}).expr;n.babelTree?(s.type="ClassMethod",s.key=e.$superID,s.kind="method",e.body.body.push(s)):e.body.body.push({type:"MethodDefinition",key:e.$superID,kind:"method",value:s})}})}function ce(e){return y.treeWalker(e,function(e,t,n){return u(e)&&"ArrowFunctionExpression"===e.type&&"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:e.body}]}),t(),!0}),e}function le(e){return y.treeWalker(e,function(e,t,r){t(),"Identifier"===e.type&&"__nodent"===e.name&&i(e,b(n))}),e}function pe(e){if("Program"===e.type&&"module"===e.sourceType)return!0;var t;if("Program"===e.type)t=e.body;else{if(!o(e).isFunction)return!1;t=e.body.body}if(t)for(var n=0;n<t.length;n++)if(o(t[n]).isDirective&&t[n].expression.value.match(/^\s*use\s+strict\s*$/))return!0;return!1}function he(e){for(var t=0;t<e.length;t++){var n=e[t];if("ClassDeclaration"===n.type||"VariableDeclaration"===n.type&&("let"===n.kind||"const"===n.kind)||"FunctionDeclaration"===n.type&&n.id&&n.id.name&&!n.$continuation)return!0}return!1}function fe(e){y.treeWalker(e,function(e,t,n){if(t(),"ArrowFunctionExpression"===e.type&&"BlockStatement"===e.body.type&&1===e.body.body.length&&"ReturnStatement"===e.body.body[0].type)e.body=e.body.body[0].argument;else{var r,i;if(r=o(e).isBlockStatement)for(var s=0;s<r.length;s++)(i=o(r[s]).isBlockStatement)&&!he(i)&&(he(r[s])||[].splice.apply(r,[s,1].concat(i)))}}),y.treeWalker(e,function(e,t,n){if(t(),o(e).isJump){var r=n[0];if("index"in r)for(var i=r.index+1,s=r.parent[r.field];i<s.length;)"VariableDeclaration"===s[i].type||o(s[i]).isFunction&&s[i].id?i+=1:s.splice(i,1)}}),y.treeWalker(e,function(e,t,n){t(),e.$thisCall&&de[e.name]&&(de[e.name].ref?delete de[e.name]:de[e.name].ref=e.$thisCall)});var t=Object.keys(de).map(function(e){return de[e].ref});if(t.length){y.treeWalker(e,function(e,n,i){if(n(),t.indexOf(e)>=0&&"ReturnStatement"===i[1].self.type){var s=e.$thisCallName,a=r(de[s].def.body.body);de[s].$inlined=!0,o(i[1].self).isJump||a.push({type:"ReturnStatement"}),i[1].replace(a)}});var n=Object.keys(de).map(function(e){return de[e].$inlined&&de[e].def});y.treeWalker(e,function(e,t,r){t(),n.indexOf(e)>=0&&r[0].remove()})}if(!("Program"===e.type&&"module"===e.sourceType||a(e,function(e){return o(e).isES6},!0))){var i=pe(e);!function(e){y.treeWalker(e,function(e,t,n){if("Program"===e.type||"FunctionDeclaration"===e.type||"FunctionExpression"===e.type){var r=i;if(i=i||pe(e)){t();var s="Program"===e.type?e:e.body,o=ne(s,function(e,t){if("FunctionDeclaration"===e.type)return t[0].parent!==s});o=o.map(function(e){return e[0].remove()}),[].push.apply(s.body,o)}else t();i=r}else t()})}(e)}return y.treeWalker(e,function(e,t,n){t(),Object.keys(e).filter(function(e){return"$"===e[0]}).forEach(function(t){delete e[t]})}),e}var de={},ye=1,me={};Object.keys(n).filter(function(e){return"$"===e[0]}).forEach(function(e){me[e.slice(1)]=h(n[e])});var ge=y.part("var $0 = arguments",[me.arguments]).body[0];return n.engine?(e.ast=ue(e.ast,!0),e.ast=te(e.ast,n.engine),e.ast=le(e.ast),fe(e.ast)):n.generators?(e.ast=ue(e.ast),e.ast=te(e.ast),e.ast=le(e.ast),fe(e.ast)):(e.ast=ue(e.ast),_(e.ast)),n.babelTree&&y.treeWalker(e.ast,function(e,t,n){t(),"Literal"===e.type&&i(e,b(e.value))}),e}var y=e("./parser"),m=e("./output");n.printNode=function e(t){if(!t)return"";if(Array.isArray(t))return t.map(e).join("|\n");try{return m(t)}catch(e){return e.message+": "+(t&&t.type)}};var g={start:!0,end:!0,loc:!0,range:!0},v={getScope:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type&&"BlockStatement"===this.node.body.type?this.node.body.body:"Program"===this.node.type?this.node.body:null},isScope:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"Program"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type&&"BlockStatement"===this.node.body.type},isFunction:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type},isClass:function(){return"ClassDeclaration"===this.node.type||"ClassExpression"===this.node.type},isBlockStatement:function(){return"ClassBody"===this.node.type||"Program"===this.node.type||"BlockStatement"===this.node.type?this.node.body:"SwitchCase"===this.node.type&&this.node.consequent},isExpressionStatement:function(){return"ExpressionStatement"===this.node.type},isLiteral:function(){return"Literal"===this.node.type||"BooleanLiteral"===this.node.type||"RegExpLiteral"===this.node.type||"NumericLiteral"===this.node.type||"StringLiteral"===this.node.type||"NullLiteral"===this.node.type},isDirective:function(){return"ExpressionStatement"===this.node.type&&("StringLiteral"===this.node.expression.type||"Literal"===this.node.expression.type&&"string"==typeof this.node.expression.value)},isUnaryExpression:function(){return"UnaryExpression"===this.node.type},isAwait:function(){return"AwaitExpression"===this.node.type&&!this.node.$hidden},isAsync:function(){return this.node.async},isStatement:function(){return null!==this.node.type.match(/[a-zA-Z]+Declaration/)||null!==this.node.type.match(/[a-zA-Z]+Statement/)},isExpression:function(){return null!==this.node.type.match(/[a-zA-Z]+Expression/)},isLoop:function(){return"ForStatement"===this.node.type||"WhileStatement"===this.node.type||"DoWhileStatement"===this.node.type},isJump:function(){return"ReturnStatement"===this.node.type||"ThrowStatement"===this.node.type||"BreakStatement"===this.node.type||"ContinueStatement"===this.node.type},isES6:function(){switch(this.node.type){case"ExportNamedDeclaration":case"ExportSpecifier":case"ExportDefaultDeclaration":case"ExportAllDeclaration":case"ImportDeclaration":case"ImportSpecifier":case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ArrowFunctionExpression":case"ForOfStatement":case"YieldExpression":case"Super":case"RestElement":case"RestProperty":case"SpreadElement":case"TemplateLiteral":case"ClassDeclaration":case"ClassExpression":return!0;case"VariableDeclaration":return this.node.kind&&"var"!==this.node.kind;case"FunctionDeclaration":case"FunctionExpression":return!!this.node.generator}}},b={};Object.keys(v).forEach(function(e){Object.defineProperty(b,e,{get:v[e]})}),t.exports={printNode:printNode,babelLiteralNode:p,asynchronize:function(e,t,n,r){try{return d(e,t,n,r)}catch(t){if(t instanceof SyntaxError){var i=e.origCode.substr(t.pos-t.loc.column);i=i.split("\n")[0],t.message+=" (nodent)\n"+i+"\n"+i.replace(/[\S ]/g,"-").substring(0,t.loc.column)+"^",t.stack=""}throw t}}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./output":13,"./parser":14}],13:[function(e,t,n){"use strict";function r(e){var t=y[e.type]||y[e.type+e.operator]||y[e.type+e.operator+(e.prefix?"prefix":"")];return void 0!==t?t:20}function i(e,t,n){var r=this[n||e.type];r?r.call(this,e,t):t.write(e,"/*"+e.type+"?*/ "+t.sourceAt(e.start,e.end))}function s(e,t,n,i){2===i||r(n)<r(t)||r(n)==r(t)&&(i||t.right===n)?(e.write(null,"("),this.out(n,e,n.type),e.write(null,")")):this.out(n,e,n.type)}function o(e,t){var n;if(t.write(null,"("),null!=e&&e.length>0){this.out(e[0],t,e[0].type);for(var r=1,i=e.length;r<i;r++)n=e[r],t.write(n,", "),this.out(n,t,n.type)}t.write(null,") ")}var a,u,c,l,p,h,f=e("source-map").SourceMapGenerator;if("".repeat)h=function(e,t){return t&&e?e.repeat(t):""};else{var d={};h=function(e,t){if(!t||!e)return"";var n=""+e+t;if(!d[n]){for(var r=[];t--;)r.push(e);d[n]=r.join("")}return d[n]}}var y={ExpressionStatement:-1,Identifier:21,Literal:21,BooleanLiteral:21,RegExpLiteral:21,NumericLiteral:21,StringLiteral:21,NullLiteral:21,ThisExpression:21,SuperExpression:21,ObjectExpression:21,ClassExpression:21,MemberExpression:19,CallExpression:18,NewExpression:18,ArrayExpression:17.5,FunctionExpression:17.5,FunctionDeclaration:17.5,ArrowFunctionExpression:17.5,"UpdateExpression++":17,"UpdateExpression--":17,"UpdateExpression++prefix":16,"UpdateExpression--prefix":16,UnaryExpression:16,AwaitExpression:16,"BinaryExpression**":15,"BinaryExpression*":15,"BinaryExpression/":15,"BinaryExpression%":15,"BinaryExpression+":14,"BinaryExpression-":14,"BinaryExpression<<":13,"BinaryExpression>>":13,"BinaryExpression>>>":13,"BinaryExpression<":12,"BinaryExpression<=":12,"BinaryExpression>":12,"BinaryExpression>=":12,BinaryExpressionin:12,BinaryExpressioninstanceof:12,"BinaryExpression==":11,"BinaryExpression===":11,"BinaryExpression!=":11,"BinaryExpression!==":11,"BinaryExpression&":10,"BinaryExpression^":9,"BinaryExpression|":8,"LogicalExpression&&":7,"LogicalExpression||":6,ConditionalExpression:5,AssignmentPattern:4,AssignmentExpression:4,yield:3,YieldExpression:3,SpreadElement:2,"comma-separated-list":1.5,SequenceExpression:1},m={type:"comma-separated-list"},g={out:i,expr:s,formatParameters:o,Program:function(e,t){for(var n,r,i=h(t.indent,t.indentLevel),s=t.lineEnd,o=0,a=(n=e.body).length;o<a;o++)r=n[o],t.write(null,i),this.out(r,t,r.type),t.write(null,s)},BlockStatement:p=function(e,t){var n,r,i=h(t.indent,t.indentLevel++),s=t.lineEnd,o=i+t.indent;if(t.write(e,"{"),null!=(n=e.body)&&n.length>0){t.write(null,s);for(var a=0,u=n.length;a<u;a++)r=n[a],t.write(null,o),this.out(r,t,r.type),t.write(null,s);t.write(null,i)}t.write(e.loc?{loc:{start:{line:e.loc.end.line,column:0}}}:null,"}"),t.indentLevel--},ClassBody:p,EmptyStatement:function(e,t){t.write(e,";")},ParenthesizedExpression:function(e,t){this.expr(t,e,e.expression,2)},ExpressionStatement:function(e,t){"FunctionExpression"===e.expression.type||"ObjectExpression"===e.expression.type?(t.write(null,"("),this.expr(t,e,e.expression),t.write(null,")")):this.expr(t,e,e.expression),t.write(null,";")},IfStatement:function(e,t){t.write(e,"if ("),this.out(e.test,t,e.test.type),t.write(null,") "),"BlockStatement"!==e.consequent.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.consequent,t,e.consequent.type),null!=e.alternate&&("BlockStatement"!==e.consequent.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel)),t.write(null," else "),"BlockStatement"!==e.alternate.type&&"IfStatement"!==e.alternate.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.alternate,t,e.alternate.type))},LabeledStatement:function(e,t){this.out(e.label,t,e.label.type),t.write(null,":"),this.out(e.body,t,e.body.type)},BreakStatement:function(e,t){t.write(e,"break"),e.label&&(t.write(null," "),this.out(e.label,t,e.label.type)),t.write(null,";")},ContinueStatement:function(e,t){t.write(e,"continue"),e.label&&(t.write(null," "),this.out(e.label,t,e.label.type)),t.write(null,";")},WithStatement:function(e,t){t.write(e,"with ("),this.out(e.object,t,e.object.type),t.write(null,") "),this.out(e.body,t,e.body.type)},SwitchStatement:function(e,t){var n,r,i,s=h(t.indent,t.indentLevel++),o=t.lineEnd;t.indentLevel++;var a=s+t.indent,u=a+t.indent;t.write(e,"switch ("),this.out(e.discriminant,t,e.discriminant.type),t.write(null,") {",o);for(var c=e.cases,l=0;l<c.length;l++){(n=c[l]).test?(t.write(n,a,"case "),this.out(n.test,t,n.test.type),t.write(null,":",o)):t.write(n,a,"default:",o),r=n.consequent;for(var p=0;p<r.length;p++)i=r[p],t.write(null,u),this.out(i,t,i.type),t.write(null,o)}t.indentLevel-=2,t.write(null,s,"}")},ReturnStatement:function(e,t){e.async&&t.write(e," async "),t.write(e,"return"),e.argument&&(t.write(null," "),this.out(e.argument,t,e.argument.type)),t.write(null,";")},ThrowStatement:function(e,t){e.async&&t.write(e," async "),t.write(e,"throw "),this.out(e.argument,t,e.argument.type),t.write(null,";")},TryStatement:function(e,t){t.write(e,"try "),this.out(e.block,t,e.block.type),e.handler&&this.out(e.handler,t,e.handler.type),e.finalizer&&(t.write(e.finalizer," finally "),this.out(e.finalizer,t,e.finalizer.type))},CatchClause:function(e,t){t.write(e," catch ("),this.out(e.param,t,e.param.type),t.write(null,") "),this.out(e.body,t,e.body.type)},WhileStatement:function(e,t){t.write(e,"while ("),this.out(e.test,t,e.test.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},DoWhileStatement:function(e,t){t.write(e,"do "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type),t.write(null," while ("),this.out(e.test,t,e.test.type),t.write(null,");")},ForStatement:function(e,t){if(t.write(e,"for ("),null!=e.init){var n=e.init,r=n.type;t.inForInit++,this.out(n,t,r),t.inForInit--,"VariableDeclaration"!==r&&t.write(null,"; ")}else t.write(null,"; ");e.test&&this.out(e.test,t,e.test.type),t.write(null,"; "),e.update&&this.out(e.update,t,e.update.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},ForInStatement:a=function(e,t){t.write(e,"for (");var n=e.left,r=n.type;t.inForInit++,this.out(n,t,r),"V"===r[0]&&19===r.length&&t.back(),t.inForInit--,t.write(null,"I"===e.type[3]?" in ":" of "),this.out(e.right,t,e.right.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},ForOfStatement:a,DebuggerStatement:function(e,t){t.write(e,"debugger;")},Function:function(e,t){e.async&&t.write(e,"async "),t.write(e,e.generator?"function* ":"function "),e.id&&t.write(e.id,e.id.name),this.formatParameters(e.params,t),this.out(e.body,t,e.body.type)},FunctionDeclaration:function(e,t){this.Function(e,t),t.write(null,t.lineEnd,h(t.indent,t.indentLevel))},FunctionExpression:function(e,t){this.Function(e,t)},VariableDeclaration:function(e,t){var n=e.declarations;t.write(e,e.kind," ");var r=n.length;if(r>0){this.out(n[0],t,"VariableDeclarator");for(var i=1;i<r;i++)t.write(null,", "),this.out(n[i],t,"VariableDeclarator")}t.write(null,";")},VariableDeclarator:function(e,t){this.out(e.id,t,e.id.type),null!=e.init&&(t.write(null," = "),this.expr(t,m,e.init))},ClassDeclaration:function(e,t){t.write(e,"class "),e.id&&t.write(e.id,e.id.name+" "),e.superClass&&(t.write(null,"extends "),this.out(e.superClass,t,e.superClass.type),t.write(null," ")),this.out(e.body,t,"BlockStatement")},ImportSpecifier:function(e,t){e.local.name==e.imported.name?this.out(e.local,t,e.local.type):(this.out(e.imported,t,e.imported.type),t.write(null," as "),this.out(e.local,t,e.local.type))},ImportDefaultSpecifier:function(e,t){this.out(e.local,t,e.local.type)},ImportNamespaceSpecifier:function(e,t){t.write(null,"* as "),this.out(e.local,t,e.local.type)},ImportDeclaration:function(e,t){t.write(e,"import ");var n=e.specifiers,r=n.length,i=!0;if(r>0){for(var s=0;s<r;s++)"ImportSpecifier"===n[s].type&&i&&(i=!1,t.write(null,"{")),this.out(n[s],t,n[s].type),s<r-1&&t.write(null,", ");"ImportSpecifier"===n[r-1].type&&t.write(null,"}"),t.write(null," from ")}t.write(e.source,e.source.raw),t.write(null,";")},ExportDefaultDeclaration:function(e,t){t.write(e,"export default "),this.out(e.declaration,t,e.declaration.type)},ExportSpecifier:function(e,t){e.local.name==e.exported.name?this.out(e.local,t,e.local.type):(this.out(e.local,t,e.local.type),t.write(null," as "),this.out(e.exported,t,e.exported.type))},ExportNamedDeclaration:function(e,t){if(t.write(e,"export "),e.declaration)this.out(e.declaration,t,e.declaration.type);else{var n=e.specifiers;if(t.write(e,"{"),n&&n.length>0)for(var r=0;r<n.length;r++)this.out(n[r],t,n[r].type),r<n.length-1&&t.write(null,", ");t.write(null,"}"),e.source&&t.write(e.source," from ",e.source.raw),t.write(null,";")}},ExportAllDeclaration:function(e,t){t.write(e,"export * from "),t.write(e.source,e.source.raw,";")},MethodDefinition:function(e,t){switch(e.value.async&&t.write(e,"async "),e.static&&t.write(e,"static "),e.kind){case"get":case"set":t.write(e,e.kind," ")}e.value.generator&&t.write(null,"*"),e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),this.formatParameters(e.value.params,t),this.out(e.value.body,t,e.value.body.type)},ClassMethod:function(e,t){switch(e.async&&t.write(e,"async "),e.static&&t.write(e,"static "),e.kind){case"get":case"set":t.write(e,e.kind," ")}e.generator&&t.write(null,"*"),e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),this.formatParameters(e.params,t),this.out(e.body,t,e.body.type)},ClassExpression:function(e,t){this.out(e,t,"ClassDeclaration")},ArrowFunctionExpression:function(e,t){e.async&&t.write(e,"async "),1===e.params.length&&"Identifier"===e.params[0].type?(this.out(e.params[0],t,e.params[0].type),t.write(e," => ")):(this.formatParameters(e.params,t),t.write(e,"=> ")),"ObjectExpression"===e.body.type||"SequenceExpression"===e.body.type?(t.write(null,"("),this.out(e.body,t,e.body.type),t.write(null,")")):this.out(e.body,t,e.body.type)},ThisExpression:function(e,t){t.write(e,"this")},Super:function(e,t){t.write(e,"super")},RestElement:u=function(e,t){t.write(e,"..."),this.out(e.argument,t,e.argument.type)},SpreadElement:u,YieldExpression:function(e,t){t.write(e,e.delegate?"yield*":"yield"),e.argument&&(t.write(null," "),this.expr(t,e,e.argument))},AwaitExpression:function(e,t){t.write(e,"await "),this.expr(t,e,e.argument)},TemplateLiteral:function(e,t){var n,r=e.quasis,i=e.expressions;t.write(e,"`");for(var s=0,o=i.length;s<o;s++)n=i[s],t.write(r[s].value,r[s].value.raw),t.write(null,"${"),this.out(n,t,n.type),t.write(null,"}");t.write(r[r.length-1].value,r[r.length-1].value.raw),t.write(e,"`")},TaggedTemplateExpression:function(e,t){this.out(e.tag,t,e.tag.type),this.out(e.quasi,t,e.quasi.type)},ArrayExpression:l=function(e,t){if(t.write(e,"["),e.elements.length>0)for(var n=e.elements,r=n.length,i=0;;){var s=n[i];if(s&&this.expr(t,m,s),((i+=1)<r||null===s)&&t.write(null,","),i>=r)break;t.lineLength()>t.wrapColumn&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1))}t.write(null,"]")},ArrayPattern:l,ObjectExpression:function(e,t){var n,r=h(t.indent,t.indentLevel++),i=t.lineEnd,s=r+t.indent;if(t.write(e,"{"),e.properties.length>0){t.write(null,i);for(var o=e.properties,a=o.length,u=0;n=o[u],t.write(null,s),this.out(n,t,"Property"),++u<a;)t.write(e,",",i),t.lineLength()>t.wrapColumn&&t.write(null,t.lineEnd,h(t.indent,t.indentLevel+1));t.write(null,i,r,"}")}else t.write(null,"}");t.indentLevel--},Property:function(e,t){e.method||"get"===e.kind||"set"===e.kind?this.MethodDefinition(e,t):(e.shorthand||(e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),t.write(null,": ")),this.expr(t,m,e.value))},ObjectPattern:function(e,t){if(t.write(e,"{"),e.properties.length>0)for(var n=e.properties,r=n.length,i=0;this.out(n[i],t,"Property"),++i<r;)t.write(null,", ");t.write(null,"}")},SequenceExpression:function(e,t){var n,r=e.expressions;if(r.length>0)for(var i=r.length,s=0;s<i;s++)n=r[s],s&&t.write(null,", "),this.expr(t,m,n)},UnaryExpression:function(e,t){e.prefix?(t.write(e,e.operator),e.operator.length>1&&t.write(e," "),this.expr(t,e,e.argument,!0)):(this.expr(t,e,e.argument),t.write(e,e.operator))},UpdateExpression:function(e,t){e.prefix?(t.write(e,e.operator),this.out(e.argument,t,e.argument.type)):(this.out(e.argument,t,e.argument.type),t.write(e,e.operator))},BinaryExpression:c=function(e,t){var n=e.operator;"in"===n&&t.inForInit&&t.write(null,"("),this.expr(t,e,e.left),t.write(e," ",n," "),this.expr(t,e,e.right,"ArrowFunctionExpression"===e.right.type?2:0),"in"===n&&t.inForInit&&t.write(null,")")},LogicalExpression:c,AssignmentExpression:function(e,t){"ObjectPattern"===e.left.type&&t.write(null,"("),this.BinaryExpression(e,t),"ObjectPattern"===e.left.type&&t.write(null,")")},AssignmentPattern:function(e,t){this.expr(t,e,e.left),t.write(e," = "),this.expr(t,e,e.right)},ConditionalExpression:function(e,t){this.expr(t,e,e.test,!0),t.write(e," ? "),this.expr(t,e,e.consequent),t.write(null," : "),this.expr(t,e,e.alternate)},NewExpression:function(e,t){t.write(e,"new "),this.out(e,t,"CallExpression")},CallExpression:function(e,t){this.expr(t,e,e.callee,"ObjectExpression"===e.callee.type?2:0),t.write(e,"(");var n=e.arguments;if(n.length>0)for(var r=n.length,i=0;i<r;i++)0!=i&&t.write(null,", "),this.expr(t,m,n[i]);t.write(null,")")},MemberExpression:function(e,t){!("ObjectExpression"===e.object.type||e.object.type.match(/Literal$/)&&e.object.raw.match(/^[0-9]/))&&("ArrayExpression"===e.object.type||"CallExpression"===e.object.type||"NewExpression"===e.object.type||r(e)<=r(e.object))?this.out(e.object,t,e.object.type):(t.write(null,"("),this.out(e.object,t,e.object.type),t.write(null,")")),e.computed?(t.write(e,"["),this.out(e.property,t,e.property.type),t.write(null,"]")):(t.write(e,"."),this.out(e.property,t,e.property.type))},Identifier:function(e,t){t.write(e,e.name)},Literal:function(e,t){t.write(e,e.raw)},NullLiteral:function(e,t){t.write(e,"null")},BooleanLiteral:function(e,t){t.write(e,JSON.stringify(e.value))},StringLiteral:function(e,t){t.write(e,JSON.stringify(e.value))},RegExpLiteral:function(e,t){t.write(e,e.extra.raw||"/"+e.pattern+"/"+e.flags)},NumericLiteral:function(e,t){t.write(e,JSON.stringify(e.value))}};t.exports=function(e,t,n){function r(e){l=arguments[arguments.length-1];for(var n=1;n<arguments.length;n++){if(c&&e&&e.loc&&e.loc.start){c.addMapping({source:t.map.file,original:{line:e.loc.start.line,column:e.loc.start.column},generated:{line:t.map.startLine+u.length+1,column:a.length}})}if(arguments[n]===y.lineEnd){if(d.length&&(d.forEach(function(e){"Line"===e.type?a+=" // "+e.value:((" /*"+e.value+"*/").split("\n").forEach(function(e){a+=e,u.push(a),a=""}),a=u.pop())}),d=[]),u.push(a),a="",p.length){var r=u.pop();p.forEach(function(e){var t=h(y.indent,e.indent);"Line"===e.type?u.push(t+"//"+e.value):(t+"/*"+e.value+"*/").split("\n").forEach(function(e){u.push(e)})}),u.push(r),p=[]}}else a+=arguments[n],e&&e.$comments&&(e.$comments.forEach(function(t){var n=e.loc.start.column<t.loc.start.column;t.indent=y.indentLevel,n?d.push(t):p.push(t)}),e.$comments=null)}}function i(){return a.length}function s(e,t){return n?n.substring(e,t):"/* Omitted Non-standard node */"}function o(){a=a.substring(0,a.length-l.length)}var a="",u=[],c=(t=t||{}).map&&new f(t.map);c&&t.map.sourceContent&&c.setSourceContent(t.map.file,t.map.sourceContent);var l="",p=[],d=[],y={inForInit:0,lineLength:i,sourceAt:s,write:r,back:o,indent:"    ",lineEnd:"\n",indentLevel:0,wrapColumn:80};g.out(e,y),d=e.$comments||[],y.write(e,y.lineEnd);var m=u.join(y.lineEnd);return t&&t.map?{code:m,map:c}:m}},{"source-map":27}],14:[function(e,t,n){"use strict";function r(e,t){["start","end","loc","range"].forEach(function(n){n in e&&!(n in t)&&(t[n]=e[n])})}function i(e,t,n){function r(e){e.replace=l.replace,e.append=l.append,e.index?(Object.defineProperties(e,{index:{enumerable:!0,get:l.index}}),e.remove=l.removeElement):e.remove=l.removeNode,n.unshift(e),i(e.self,t,n),n.shift()}function s(){e.type in c&&c[e.type](e,n,function t(i,s,o){if(i===e)return c[o||e.type](e,n,t);for(var a=Object.keys(e),u=0;u<a.length;u++){var l=e[a[u]];Array.isArray(l)?l.indexOf(i)>=0&&r({self:i,parent:e,field:a[u],index:!0}):l instanceof Object&&i===l&&r({self:i,parent:e,field:a[u]})}})}return n||((n=[{self:e}]).replace=function(e,t){n[e].replace(t)}),t(e,s,n),e}function s(t,n){var r=[],s={ecmaVersion:8,allowHashBang:!0,allowReturnOutsideFunction:!0,allowImportExportEverywhere:!0,locations:!0,onComment:r};if((!n||!n.noNodentExtensions||parseInt(a.version)<4)&&(p||(parseInt(a.version)<4&&console.warn("Nodent: Warning - noNodentExtensions option requires acorn >=v4.x. Extensions installed."),e("acorn-es7-plugin")(a),p=!0),s.plugins=s.plugins||{},s.plugins.asyncawait={asyncExits:!0,awaitAnywhere:!0}),n)for(var o in n)"noNodentExtensions"!==o&&(s[o]=n[o]);var u=a.parse(t,s);return i(u,function(e,t,n){for(t();r.length&&e.loc&&e.loc.start.line>=r[0].loc.start.line&&e.loc.end.line>=r[0].loc.end.line;)e.$comments=e.$comments||[],e.$comments.push(r.shift())}),u}function o(e,t){function n(e,r){if(Array.isArray(r)&&!Array.isArray(e))throw new Error("Can't substitute an array for a node");return r=r||{},Object.keys(e).forEach(function(i){function s(e){return"function"==typeof e&&(e=e()),r=r.concat(e)}function o(e){return"function"==typeof e&&(e=e()),r[i]=e,r}if(!(e[i]instanceof Object))return r[i]=e[i];if(Array.isArray(e[i]))return r[i]=n(e[i],[]);var a;if(a=Array.isArray(r)?s:o,"Identifier"===e[i].type&&"$"===e[i].name[0])return a(t[e[i].name.slice(1)]);if("LabeledStatement"===e[i].type&&"$"===e[i].label.name){var u=e[i].body.expression;return a(t[u.name||u.value])}return a("LabeledStatement"===e[i].type&&"$$"===e[i].label.name.slice(0,2)?t[e[i].label.name.slice(2)](n(e[i]).body):n(e[i]))}),r}h[e]||(h[e]=s(e,{noNodentExtensions:!0,locations:!1,ranges:!1,onComment:null}));var r=n(h[e]);return{body:r.body,expr:"ExpressionStatement"===r.body[0].type?r.body[0].expression:null}}var a=e("acorn"),u={AwaitExpression:function(e,t,n){n(e.argument,t,"Expression")},SwitchStatement:function(e,t,n){n(e.discriminant,t,"Expression");for(var r=0;r<e.cases.length;++r)n(e.cases[r],t)},SwitchCase:function(e,t,n){e.test&&n(e.test,t,"Expression");for(var r=0;r<e.consequent.length;++r)n(e.consequent[r],t,"Statement")},TryStatement:function(e,t,n){n(e.block,t,"Statement"),e.handler&&n(e.handler,t,"Statement"),e.finalizer&&n(e.finalizer,t,"Statement")},CatchClause:function(e,t,n){n(e.param,t,"Pattern"),n(e.body,t,"ScopeBody")},Class:function(e,t,n){e.id&&n(e.id,t,"Pattern"),e.superClass&&n(e.superClass,t,"Expression"),n(e.body,t)},ClassBody:function(e,t,n){for(var r=0;r<e.body.length;r++)n(e.body[r],t)},ClassProperty:function(e,t,n){e.key&&n(e.key,t,"Expression"),e.value&&n(e.value,t,"Expression")},ClassMethod:function(e,t,n){e.key&&n(e.key,t,"Expression"),n(e,t,"Function")},ObjectProperty:function(e,t,n){e.key&&n(e.key,t,"Expression"),e.value&&n(e.value,t,"Expression")},ObjectMethod:function(e,t,n){e.key&&n(e.key,t,"Expression"),n(e,t,"Function")}},c=e("acorn/dist/walk").make(u),l={replace:function(e){return Array.isArray(e)&&1===e.length&&(e=e[0]),"index"in this?(r(this.parent[this.field][this.index],e),Array.isArray(e)?[].splice.apply(this.parent[this.field],[this.index,1].concat(e)):this.parent[this.field][this.index]=e):(r(this.parent[this.field],e),Array.isArray(e)?this.parent[this.field]={type:"BlockStatement",body:e}:this.parent[this.field]=e),this.self},append:function(e){if(Array.isArray(e)&&1===e.length&&(e=e[0]),!("index"in this))throw new Error("Cannot append Element node to non-array");return Array.isArray(e)?[].splice.apply(this.parent[this.field],[this.index+1,0].concat(e)):this.parent[this.field].splice(this.index+1,0,e),this.self},index:function(){return this.parent[this.field].indexOf(this.self)},removeElement:function(){return this.parent[this.field].splice(this.index,1)[0]},removeNode:function(){var e=this.parent[this.field];return delete this.parent[this.field],e}},p=!1,h={};t.exports={part:o,parse:s,treeWalker:i}},{acorn:15,"acorn-es7-plugin":1,"acorn/dist/walk":16}],15:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?r(n):"function"==typeof define&&define.amd?define(["exports"],r):r(e.acorn=e.acorn||{})}(this,function(e){"use strict";function t(e,t){for(var n=65536,r=0;r<t.length;r+=2){if((n+=t[r])>e)return!1;if((n+=t[r+1])>=e)return!0}}function n(e,n){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&k.test(String.fromCharCode(e)):!1!==n&&t(e,_)))}function r(e,n){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&A.test(String.fromCharCode(e)):!1!==n&&(t(e,_)||t(e,C)))))}function i(e,t){return new L(e,{beforeExpr:!0,binop:t})}function s(e,t){return void 0===t&&(t={}),t.keyword=e,N[e]=new L(e,t)}function o(e){return 10===e||13===e||8232===e||8233===e}function a(e,t){return j.call(e,t)}function u(e,t){for(var n=1,r=0;;){$.lastIndex=r;var i=$.exec(e);if(!(i&&i.index<t))return new V(n,t-r);++n,r=i.index+i[0].length}}function c(e){var t={};for(var n in U)t[n]=e&&a(e,n)?e[n]:U[n];if(t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),M(t.onToken)){var r=t.onToken;t.onToken=function(e){return r.push(e)}}return M(t.onComment)&&(t.onComment=l(t,t.onComment)),t}function l(e,t){return function(n,r,i,s,o,a){var u={type:n?"Block":"Line",value:r,start:i,end:s};e.locations&&(u.loc=new q(this,o,a)),e.ranges&&(u.range=[i,s]),t.push(u)}}function p(e){return new RegExp("^("+e.replace(/ /g,"|")+")$")}function h(e,t,n,r){return e.type=t,e.end=n,this.options.locations&&(e.loc.end=r),this.options.ranges&&(e.range[1]=n),e}function f(e,t,n,r){try{return new RegExp(e,t)}catch(e){if(void 0!==n)throw e instanceof SyntaxError&&r.raise(n,"Error parsing regular expression: "+e.message),e}}function d(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}function y(e,t){return new W(t,e).parse()}function m(e,t,n){var r=new W(n,e,t);return r.nextToken(),r.parseExpression()}function g(e,t){return new W(t,e)}function v(t,n,r){e.parse_dammit=t,e.LooseParser=n,e.pluginsLoose=r}var b={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},x="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",w={5:x,6:x+" const class extends export import super"},E="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙա-ևא-תװ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᳩ-ᳬᳮ-ᳱᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿕ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞮꞰ-ꞷꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭥꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",S="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࣔ-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఃా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഁ-ഃാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ංඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ູົຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭ᳲ-᳴᳸᳹᷀-᷵᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱꤀-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",k=new RegExp("["+E+"]"),A=new RegExp("["+E+S+"]");E=S=null;var _=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,17,26,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,26,45,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,785,52,76,44,33,24,27,35,42,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,54,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,86,25,391,63,32,0,449,56,264,8,2,36,18,0,50,29,881,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,881,68,12,0,67,12,65,0,32,6124,20,754,9486,1,3071,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,4149,196,60,67,1213,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,10591,541],C=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,1306,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,52,0,13,2,49,13,10,2,4,9,83,11,7,0,161,11,6,9,7,3,57,0,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,87,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,423,9,838,7,2,7,17,9,57,21,2,13,19882,9,135,4,60,6,26,9,1016,45,17,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,2214,6,110,6,6,9,792487,239],L=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null},P={beforeExpr:!0},T={startsExpr:!0},N={},F={num:new L("num",T),regexp:new L("regexp",T),string:new L("string",T),name:new L("name",T),eof:new L("eof"),bracketL:new L("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new L("]"),braceL:new L("{",{beforeExpr:!0,startsExpr:!0}),braceR:new L("}"),parenL:new L("(",{beforeExpr:!0,startsExpr:!0}),parenR:new L(")"),comma:new L(",",P),semi:new L(";",P),colon:new L(":",P),dot:new L("."),question:new L("?",P),arrow:new L("=>",P),template:new L("template"),ellipsis:new L("...",P),backQuote:new L("`",T),dollarBraceL:new L("${",{beforeExpr:!0,startsExpr:!0}),eq:new L("=",{beforeExpr:!0,isAssign:!0}),assign:new L("_=",{beforeExpr:!0,isAssign:!0}),incDec:new L("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new L("prefix",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:i("||",1),logicalAND:i("&&",2),bitwiseOR:i("|",3),bitwiseXOR:i("^",4),bitwiseAND:i("&",5),equality:i("==/!=",6),relational:i("</>",7),bitShift:i("<</>>",8),plusMin:new L("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:i("%",10),star:i("*",10),slash:i("/",10),starstar:new L("**",{beforeExpr:!0}),_break:s("break"),_case:s("case",P),_catch:s("catch"),_continue:s("continue"),_debugger:s("debugger"),_default:s("default",P),_do:s("do",{isLoop:!0,beforeExpr:!0}),_else:s("else",P),_finally:s("finally"),_for:s("for",{isLoop:!0}),_function:s("function",T),_if:s("if"),_return:s("return",P),_switch:s("switch"),_throw:s("throw",P),_try:s("try"),_var:s("var"),_const:s("const"),_while:s("while",{isLoop:!0}),_with:s("with"),_new:s("new",{beforeExpr:!0,startsExpr:!0}),_this:s("this",T),_super:s("super",T),_class:s("class"),_extends:s("extends",P),_export:s("export"),_import:s("import"),_null:s("null",T),_true:s("true",T),_false:s("false",T),_in:s("in",{beforeExpr:!0,binop:7}),_instanceof:s("instanceof",{beforeExpr:!0,binop:7}),_typeof:s("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:s("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:s("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},O=/\r\n?|\n|\u2028|\u2029/,$=new RegExp(O.source,"g"),B=/[\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff]/,R=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,I=Object.prototype,j=I.hasOwnProperty,D=I.toString,M=Array.isArray||function(e){return"[object Array]"===D.call(e)},V=function(e,t){this.line=e,this.column=t};V.prototype.offset=function(e){return new V(this.line,this.column+e)};var q=function(e,t,n){this.start=t,this.end=n,null!==e.sourceFile&&(this.source=e.sourceFile)},U={ecmaVersion:7,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1,plugins:{}},z={},W=function(e,t,n){this.options=e=c(e),this.sourceFile=e.sourceFile,this.keywords=p(w[e.ecmaVersion>=6?6:5]);var r="";if(!e.allowReserved){for(var i=e.ecmaVersion;!(r=b[i]);i--);"module"==e.sourceType&&(r+=" await")}this.reservedWords=p(r);var s=(r?r+" ":"")+b.strict;this.reservedWordsStrict=p(s),this.reservedWordsStrictBind=p(s+" "+b.strictBind),this.input=String(t),this.containsEsc=!1,this.loadPlugins(e.plugins),n?(this.pos=n,this.lineStart=this.input.lastIndexOf("\n",n-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(O).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=F.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.inFunction=this.inGenerator=this.inAsync=!1,this.yieldPos=this.awaitPos=0,this.labels=[],0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterFunctionScope()};W.prototype.isKeyword=function(e){return this.keywords.test(e)},W.prototype.isReservedWord=function(e){return this.reservedWords.test(e)},W.prototype.extend=function(e,t){this[e]=t(this[e])},W.prototype.loadPlugins=function(e){var t=this;for(var n in e){var r=z[n];if(!r)throw new Error("Plugin '"+n+"' not found");r(t,e[n])}},W.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)};var G=W.prototype,J=/^(?:'((?:[^']|\.)*)'|"((?:[^"]|\.)*)"|;)/;G.strictDirective=function(e){for(var t=this;;){R.lastIndex=e,e+=R.exec(t.input)[0].length;var n=J.exec(t.input.slice(e));if(!n)return!1;if("use strict"==(n[1]||n[2]))return!0;e+=n[0].length}},G.eat=function(e){return this.type===e&&(this.next(),!0)},G.isContextual=function(e){return this.type===F.name&&this.value===e},G.eatContextual=function(e){return this.value===e&&this.eat(F.name)},G.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},G.canInsertSemicolon=function(){return this.type===F.eof||this.type===F.braceR||O.test(this.input.slice(this.lastTokEnd,this.start))},G.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},G.semicolon=function(){this.eat(F.semi)||this.insertSemicolon()||this.unexpected()},G.afterTrailingComma=function(e,t){if(this.type==e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},G.expect=function(e){this.eat(e)||this.unexpected()},G.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var Y=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=-1};G.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var n=t?e.parenthesizedAssign:e.parenthesizedBind;n>-1&&this.raiseRecoverable(n,"Parenthesized pattern")}},G.checkExpressionErrors=function(e,t){var n=e?e.shorthandAssign:-1;if(!t)return n>=0;n>-1&&this.raise(n,"Shorthand property assignments are valid only in destructuring patterns")},G.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},G.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var H=W.prototype;H.parseTopLevel=function(e){var t=this,n={};for(e.body||(e.body=[]);this.type!==F.eof;){var r=t.parseStatement(!0,!0,n);e.body.push(r)}return this.next(),this.options.ecmaVersion>=6&&(e.sourceType=this.options.sourceType),this.finishNode(e,"Program")};var Z={kind:"loop"},Q={kind:"switch"};H.isLet=function(){if(this.type!==F.name||this.options.ecmaVersion<6||"let"!=this.value)return!1;R.lastIndex=this.pos;var e=R.exec(this.input),t=this.pos+e[0].length,i=this.input.charCodeAt(t);if(91===i||123==i)return!0;if(n(i,!0)){for(var s=t+1;r(this.input.charCodeAt(s),!0);)++s;var o=this.input.slice(t,s);if(!this.isKeyword(o))return!0}return!1},H.isAsyncFunction=function(){if(this.type!==F.name||this.options.ecmaVersion<8||"async"!=this.value)return!1;R.lastIndex=this.pos;var e=R.exec(this.input),t=this.pos+e[0].length;return!(O.test(this.input.slice(this.pos,t))||"function"!==this.input.slice(t,t+8)||t+8!=this.input.length&&r(this.input.charAt(t+8)))},H.parseStatement=function(e,t,n){var r,i=this.type,s=this.startNode();switch(this.isLet()&&(i=F._var,r="let"),i){case F._break:case F._continue:return this.parseBreakContinueStatement(s,i.keyword);case F._debugger:return this.parseDebuggerStatement(s);case F._do:return this.parseDoStatement(s);case F._for:return this.parseForStatement(s);case F._function:return!e&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(s,!1);case F._class:return e||this.unexpected(),this.parseClass(s,!0);case F._if:return this.parseIfStatement(s);case F._return:return this.parseReturnStatement(s);case F._switch:return this.parseSwitchStatement(s);case F._throw:return this.parseThrowStatement(s);case F._try:return this.parseTryStatement(s);case F._const:case F._var:return r=r||this.value,e||"var"==r||this.unexpected(),this.parseVarStatement(s,r);case F._while:return this.parseWhileStatement(s);case F._with:return this.parseWithStatement(s);case F.braceL:return this.parseBlock();case F.semi:return this.parseEmptyStatement(s);case F._export:case F._import:return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),i===F._import?this.parseImport(s):this.parseExport(s,n);default:if(this.isAsyncFunction()&&e)return this.next(),this.parseFunctionStatement(s,!0);var o=this.value,a=this.parseExpression();return i===F.name&&"Identifier"===a.type&&this.eat(F.colon)?this.parseLabeledStatement(s,o,a):this.parseExpressionStatement(s,a)}},H.parseBreakContinueStatement=function(e,t){var n=this,r="break"==t;this.next(),this.eat(F.semi)||this.insertSemicolon()?e.label=null:this.type!==F.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var i=0;i<this.labels.length;++i){var s=n.labels[i];if(null==e.label||s.name===e.label.name){if(null!=s.kind&&(r||"loop"===s.kind))break;if(e.label&&r)break}}return i===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,r?"BreakStatement":"ContinueStatement")},H.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},H.parseDoStatement=function(e){return this.next(),this.labels.push(Z),e.body=this.parseStatement(!1),this.labels.pop(),this.expect(F._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(F.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},H.parseForStatement=function(e){if(this.next(),this.labels.push(Z),this.enterLexicalScope(),this.expect(F.parenL),this.type===F.semi)return this.parseFor(e,null);var t=this.isLet();if(this.type===F._var||this.type===F._const||t){var n=this.startNode(),r=t?"let":this.value;return this.next(),this.parseVar(n,!0,r),this.finishNode(n,"VariableDeclaration"),!(this.type===F._in||this.options.ecmaVersion>=6&&this.isContextual("of"))||1!==n.declarations.length||"var"!==r&&n.declarations[0].init?this.parseFor(e,n):this.parseForIn(e,n)}var i=new Y,s=this.parseExpression(!0,i);return this.type===F._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.toAssignable(s),this.checkLVal(s),this.checkPatternErrors(i,!0),this.parseForIn(e,s)):(this.checkExpressionErrors(i,!0),this.parseFor(e,s))},H.parseFunctionStatement=function(e,t){return this.next(),this.parseFunction(e,!0,!1,t)},H.isFunction=function(){return this.type===F._function||this.isAsyncFunction()},H.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement(!this.strict&&this.isFunction()),e.alternate=this.eat(F._else)?this.parseStatement(!this.strict&&this.isFunction()):null,this.finishNode(e,"IfStatement")},H.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(F.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},H.parseSwitchStatement=function(e){var t=this;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(F.braceL),this.labels.push(Q),this.enterLexicalScope();for(var n,r=!1;this.type!=F.braceR;)if(t.type===F._case||t.type===F._default){var i=t.type===F._case;n&&t.finishNode(n,"SwitchCase"),e.cases.push(n=t.startNode()),n.consequent=[],t.next(),i?n.test=t.parseExpression():(r&&t.raiseRecoverable(t.lastTokStart,"Multiple default clauses"),r=!0,n.test=null),t.expect(F.colon)}else n||t.unexpected(),n.consequent.push(t.parseStatement(!0));return this.exitLexicalScope(),n&&this.finishNode(n,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},H.parseThrowStatement=function(e){return this.next(),O.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var X=[];H.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===F._catch){var t=this.startNode();this.next(),this.expect(F.parenL),t.param=this.parseBindingAtom(),this.enterLexicalScope(),this.checkLVal(t.param,"let"),this.expect(F.parenR),t.body=this.parseBlock(!1),this.exitLexicalScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(F._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},H.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},H.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(Z),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,"WhileStatement")},H.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement(!1),this.finishNode(e,"WithStatement")},H.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},H.parseLabeledStatement=function(e,t,n){for(var r=this,i=0;i<this.labels.length;++i)r.labels[i].name===t&&r.raise(n.start,"Label '"+t+"' is already declared");for(var s=this.type.isLoop?"loop":this.type===F._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var a=r.labels[o];if(a.statementStart!=e.start)break;a.statementStart=r.start,a.kind=s}return this.labels.push({name:t,kind:s,statementStart:this.start}),e.body=this.parseStatement(!0),("ClassDeclaration"==e.body.type||"VariableDeclaration"==e.body.type&&"var"!=e.body.kind||"FunctionDeclaration"==e.body.type&&(this.strict||e.body.generator))&&this.raiseRecoverable(e.body.start,"Invalid labeled declaration"),this.labels.pop(),e.label=n,this.finishNode(e,"LabeledStatement")},H.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},H.parseBlock=function(e){var t=this;void 0===e&&(e=!0);var n=this.startNode();for(n.body=[],this.expect(F.braceL),e&&this.enterLexicalScope();!this.eat(F.braceR);){var r=t.parseStatement(!0);n.body.push(r)}return e&&this.exitLexicalScope(),this.finishNode(n,"BlockStatement")},H.parseFor=function(e,t){return e.init=t,this.expect(F.semi),e.test=this.type===F.semi?null:this.parseExpression(),this.expect(F.semi),e.update=this.type===F.parenR?null:this.parseExpression(),this.expect(F.parenR),this.exitLexicalScope(),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,"ForStatement")},H.parseForIn=function(e,t){var n=this.type===F._in?"ForInStatement":"ForOfStatement";return this.next(),e.left=t,e.right=this.parseExpression(),this.expect(F.parenR),this.exitLexicalScope(),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,n)},H.parseVar=function(e,t,n){var r=this;for(e.declarations=[],e.kind=n;;){var i=r.startNode();if(r.parseVarId(i,n),r.eat(F.eq)?i.init=r.parseMaybeAssign(t):"const"!==n||r.type===F._in||r.options.ecmaVersion>=6&&r.isContextual("of")?"Identifier"==i.id.type||t&&(r.type===F._in||r.isContextual("of"))?i.init=null:r.raise(r.lastTokEnd,"Complex binding patterns require an initialization value"):r.unexpected(),e.declarations.push(r.finishNode(i,"VariableDeclarator")),!r.eat(F.comma))break}return e},H.parseVarId=function(e,t){e.id=this.parseBindingAtom(t),this.checkLVal(e.id,t,!1)},H.parseFunction=function(e,t,n,r){this.initFunction(e),this.options.ecmaVersion>=6&&!r&&(e.generator=this.eat(F.star)),this.options.ecmaVersion>=8&&(e.async=!!r),t&&(e.id="nullableID"===t&&this.type!=F.name?null:this.parseIdent(),e.id&&this.checkLVal(e.id,"var"));var i=this.inGenerator,s=this.inAsync,o=this.yieldPos,a=this.awaitPos,u=this.inFunction;return this.inGenerator=e.generator,this.inAsync=e.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,this.enterFunctionScope(),t||(e.id=this.type==F.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,n),this.inGenerator=i,this.inAsync=s,this.yieldPos=o,this.awaitPos=a,this.inFunction=u,this.finishNode(e,t?"FunctionDeclaration":"FunctionExpression")},H.parseFunctionParams=function(e){this.expect(F.parenL),e.params=this.parseBindingList(F.parenR,!1,this.options.ecmaVersion>=8,!0),this.checkYieldAwaitInDefaultParams()},H.parseClass=function(e,t){var n=this;this.next(),this.parseClassId(e,t),this.parseClassSuper(e);var r=this.startNode(),i=!1;for(r.body=[],this.expect(F.braceL);!this.eat(F.braceR);)if(!n.eat(F.semi)){var s=n.startNode(),o=n.eat(F.star),a=!1,u=n.type===F.name&&"static"===n.value;n.parsePropertyName(s),s.static=u&&n.type!==F.parenL,s.static&&(o&&n.unexpected(),o=n.eat(F.star),n.parsePropertyName(s)),n.options.ecmaVersion>=8&&!o&&!s.computed&&"Identifier"===s.key.type&&"async"===s.key.name&&n.type!==F.parenL&&!n.canInsertSemicolon()&&(a=!0,n.parsePropertyName(s)),s.kind="method";var c=!1;if(!s.computed){var l=s.key;o||a||"Identifier"!==l.type||n.type===F.parenL||"get"!==l.name&&"set"!==l.name||(c=!0,s.kind=l.name,l=n.parsePropertyName(s)),!s.static&&("Identifier"===l.type&&"constructor"===l.name||"Literal"===l.type&&"constructor"===l.value)&&(i&&n.raise(l.start,"Duplicate constructor in the same class"),c&&n.raise(l.start,"Constructor can't have get/set modifier"),o&&n.raise(l.start,"Constructor can't be a generator"),a&&n.raise(l.start,"Constructor can't be an async method"),s.kind="constructor",i=!0)}if(n.parseClassMethod(r,s,o,a),c){var p="get"===s.kind?0:1;if(s.value.params.length!==p){var h=s.value.start;"get"===s.kind?n.raiseRecoverable(h,"getter should have no params"):n.raiseRecoverable(h,"setter should have exactly one param")}else"set"===s.kind&&"RestElement"===s.value.params[0].type&&n.raiseRecoverable(s.value.params[0].start,"Setter cannot use rest params")}}return e.body=this.finishNode(r,"ClassBody"),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},H.parseClassMethod=function(e,t,n,r){t.value=this.parseMethod(n,r),e.body.push(this.finishNode(t,"MethodDefinition"))},H.parseClassId=function(e,t){e.id=this.type===F.name?this.parseIdent():!0===t?this.unexpected():null},H.parseClassSuper=function(e){e.superClass=this.eat(F._extends)?this.parseExprSubscripts():null},H.parseExport=function(e,t){var n=this;if(this.next(),this.eat(F.star))return this.expectContextual("from"),e.source=this.type===F.string?this.parseExprAtom():this.unexpected(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(F._default)){this.checkExport(t,"default",this.lastTokStart);var r;if(this.type===F._function||(r=this.isAsyncFunction())){var i=this.startNode();this.next(),r&&this.next(),e.declaration=this.parseFunction(i,"nullableID",!1,r)}else if(this.type===F._class){var s=this.startNode();e.declaration=this.parseClass(s,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(!0),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id.name,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))e.source=this.type===F.string?this.parseExprAtom():this.unexpected();else{for(var o=0;o<e.specifiers.length;o++)(n.keywords.test(e.specifiers[o].local.name)||n.reservedWords.test(e.specifiers[o].local.name))&&n.unexpected(e.specifiers[o].local.start);e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},H.checkExport=function(e,t,n){e&&(a(e,t)&&this.raiseRecoverable(n,"Duplicate export '"+t+"'"),e[t]=!0)},H.checkPatternExport=function(e,t){var n=this,r=t.type;if("Identifier"==r)this.checkExport(e,t.name,t.start);else if("ObjectPattern"==r)for(var i=0;i<t.properties.length;++i)n.checkPatternExport(e,t.properties[i].value);else if("ArrayPattern"==r)for(var s=0;s<t.elements.length;++s){var o=t.elements[s];o&&n.checkPatternExport(e,o)}else"AssignmentPattern"==r?this.checkPatternExport(e,t.left):"ParenthesizedExpression"==r&&this.checkPatternExport(e,t.expression)},H.checkVariableExport=function(e,t){var n=this;if(e)for(var r=0;r<t.length;r++)n.checkPatternExport(e,t[r].id)},H.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},H.parseExportSpecifiers=function(e){var t=this,n=[],r=!0;for(this.expect(F.braceL);!this.eat(F.braceR);){if(r)r=!1;else if(t.expect(F.comma),t.afterTrailingComma(F.braceR))break;var i=t.startNode();i.local=t.parseIdent(!0),i.exported=t.eatContextual("as")?t.parseIdent(!0):i.local,t.checkExport(e,i.exported.name,i.exported.start),n.push(t.finishNode(i,"ExportSpecifier"))}return n},H.parseImport=function(e){return this.next(),this.type===F.string?(e.specifiers=X,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===F.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},H.parseImportSpecifiers=function(){var e=this,t=[],n=!0;if(this.type===F.name){var r=this.startNode();if(r.local=this.parseIdent(),this.checkLVal(r.local,"let"),t.push(this.finishNode(r,"ImportDefaultSpecifier")),!this.eat(F.comma))return t}if(this.type===F.star){var i=this.startNode();return this.next(),this.expectContextual("as"),i.local=this.parseIdent(),this.checkLVal(i.local,"let"),t.push(this.finishNode(i,"ImportNamespaceSpecifier")),t}for(this.expect(F.braceL);!this.eat(F.braceR);){if(n)n=!1;else if(e.expect(F.comma),e.afterTrailingComma(F.braceR))break;var s=e.startNode();s.imported=e.parseIdent(!0),e.eatContextual("as")?s.local=e.parseIdent():(s.local=s.imported,e.isKeyword(s.local.name)&&e.unexpected(s.local.start),e.reservedWordsStrict.test(s.local.name)&&e.raiseRecoverable(s.local.start,"The keyword '"+s.local.name+"' is reserved")),e.checkLVal(s.local,"let"),t.push(e.finishNode(s,"ImportSpecifier"))}return t};var K=W.prototype;K.toAssignable=function(e,t){var n=this;if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Can not use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":break;case"ObjectExpression":e.type="ObjectPattern";for(var r=0;r<e.properties.length;r++){var i=e.properties[r];"init"!==i.kind&&n.raise(i.key.start,"Object pattern can't contain getter or setter"),n.toAssignable(i.value,t)}break;case"ArrayExpression":e.type="ArrayPattern",this.toAssignableList(e.elements,t);break;case"AssignmentExpression":if("="!==e.operator){this.raise(e.left.end,"Only '=' operator can be used for specifying default value.");break}e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);case"AssignmentPattern":break;case"ParenthesizedExpression":e.expression=this.toAssignable(e.expression,t);break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}return e},K.toAssignableList=function(e,t){var n=this,r=e.length;if(r){var i=e[r-1];if(i&&"RestElement"==i.type)--r;else if(i&&"SpreadElement"==i.type){i.type="RestElement";var s=i.argument;this.toAssignable(s,t),"Identifier"!==s.type&&"MemberExpression"!==s.type&&"ArrayPattern"!==s.type&&this.unexpected(s.start),--r}t&&i&&"RestElement"===i.type&&"Identifier"!==i.argument.type&&this.unexpected(i.argument.start)}for(var o=0;o<r;o++){var a=e[o];a&&n.toAssignable(a,t)}return e},K.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},K.parseRest=function(e){var t=this.startNode();return this.next(),t.argument=e?this.type===F.name?this.parseIdent():this.unexpected():this.type===F.name||this.type===F.bracketL?this.parseBindingAtom():this.unexpected(),this.finishNode(t,"RestElement")},K.parseBindingAtom=function(){if(this.options.ecmaVersion<6)return this.parseIdent();switch(this.type){case F.name:return this.parseIdent();case F.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(F.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case F.braceL:return this.parseObj(!0);default:this.unexpected()}},K.parseBindingList=function(e,t,n,r){for(var i=this,s=[],o=!0;!this.eat(e);)if(o?o=!1:i.expect(F.comma),t&&i.type===F.comma)s.push(null);else{if(n&&i.afterTrailingComma(e))break;if(i.type===F.ellipsis){var a=i.parseRest(r);i.parseBindingListItem(a),s.push(a),i.type===F.comma&&i.raise(i.start,"Comma is not permitted after the rest element"),i.expect(e);break}var u=i.parseMaybeDefault(i.start,i.startLoc);i.parseBindingListItem(u),s.push(u)}return s},K.parseBindingListItem=function(e){return e},K.parseMaybeDefault=function(e,t,n){if(n=n||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(F.eq))return n;var r=this.startNodeAt(e,t);return r.left=n,r.right=this.parseMaybeAssign(),this.finishNode(r,"AssignmentPattern")},K.checkLVal=function(e,t,n){var r=this;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(t?"Binding ":"Assigning to ")+e.name+" in strict mode"),n&&(a(n,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),n[e.name]=!0),t&&"none"!==t&&(("var"===t&&!this.canDeclareVarName(e.name)||"var"!==t&&!this.canDeclareLexicalName(e.name))&&this.raiseRecoverable(e.start,"Identifier '"+e.name+"' has already been declared"),"var"===t?this.declareVarName(e.name):this.declareLexicalName(e.name));break;case"MemberExpression":t&&this.raiseRecoverable(e.start,(t?"Binding":"Assigning to")+" member expression");break;case"ObjectPattern":for(var i=0;i<e.properties.length;i++)r.checkLVal(e.properties[i].value,t,n);break;case"ArrayPattern":for(var s=0;s<e.elements.length;s++){var o=e.elements[s];o&&r.checkLVal(o,t,n)}break;case"AssignmentPattern":this.checkLVal(e.left,t,n);break;case"RestElement":this.checkLVal(e.argument,t,n);break;case"ParenthesizedExpression":this.checkLVal(e.expression,t,n);break;default:this.raise(e.start,(t?"Binding":"Assigning to")+" rvalue")}};var ee=W.prototype;ee.checkPropClash=function(e,t){if(!(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var n,r=e.key;switch(r.type){case"Identifier":n=r.name;break;case"Literal":n=String(r.value);break;default:return}var i=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===n&&"init"===i&&(t.proto&&this.raiseRecoverable(r.start,"Redefinition of __proto__ property"),t.proto=!0);else{var s=t[n="$"+n];if(s){("init"===i?this.strict&&s.init||s.get||s.set:s.init||s[i])&&this.raiseRecoverable(r.start,"Redefinition of property")}else s=t[n]={init:!1,get:!1,set:!1};s[i]=!0}}},ee.parseExpression=function(e,t){var n=this,r=this.start,i=this.startLoc,s=this.parseMaybeAssign(e,t);if(this.type===F.comma){var o=this.startNodeAt(r,i);for(o.expressions=[s];this.eat(F.comma);)o.expressions.push(n.parseMaybeAssign(e,t));return this.finishNode(o,"SequenceExpression")}return s},ee.parseMaybeAssign=function(e,t,n){if(this.inGenerator&&this.isContextual("yield"))return this.parseYield();var r=!1,i=-1,s=-1;t?(i=t.parenthesizedAssign,s=t.trailingComma,t.parenthesizedAssign=t.trailingComma=-1):(t=new Y,r=!0);var o=this.start,a=this.startLoc;this.type!=F.parenL&&this.type!=F.name||(this.potentialArrowAt=this.start);var u=this.parseMaybeConditional(e,t);if(n&&(u=n.call(this,u,o,a)),this.type.isAssign){this.checkPatternErrors(t,!0),r||Y.call(t);var c=this.startNodeAt(o,a);return c.operator=this.value,c.left=this.type===F.eq?this.toAssignable(u):u,t.shorthandAssign=-1,this.checkLVal(u),this.next(),c.right=this.parseMaybeAssign(e),this.finishNode(c,"AssignmentExpression")}return r&&this.checkExpressionErrors(t,!0),i>-1&&(t.parenthesizedAssign=i),s>-1&&(t.trailingComma=s),u},ee.parseMaybeConditional=function(e,t){var n=this.start,r=this.startLoc,i=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return i;if(this.eat(F.question)){var s=this.startNodeAt(n,r);return s.test=i,s.consequent=this.parseMaybeAssign(),this.expect(F.colon),s.alternate=this.parseMaybeAssign(e),this.finishNode(s,"ConditionalExpression")}return i},ee.parseExprOps=function(e,t){var n=this.start,r=this.startLoc,i=this.parseMaybeUnary(t,!1);return this.checkExpressionErrors(t)?i:i.start==n&&"ArrowFunctionExpression"===i.type?i:this.parseExprOp(i,n,r,-1,e)},ee.parseExprOp=function(e,t,n,r,i){var s=this.type.binop;if(null!=s&&(!i||this.type!==F._in)&&s>r){var o=this.type===F.logicalOR||this.type===F.logicalAND,a=this.value;this.next();var u=this.start,c=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1),u,c,s,i),p=this.buildBinary(t,n,e,l,a,o);return this.parseExprOp(p,t,n,r,i)}return e},ee.buildBinary=function(e,t,n,r,i,s){var o=this.startNodeAt(e,t);return o.left=n,o.operator=i,o.right=r,this.finishNode(o,s?"LogicalExpression":"BinaryExpression")},ee.parseMaybeUnary=function(e,t){var n,r=this,i=this.start,s=this.startLoc;if(this.inAsync&&this.isContextual("await"))n=this.parseAwait(e),t=!0;else if(this.type.prefix){var o=this.startNode(),a=this.type===F.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),a?this.checkLVal(o.argument):this.strict&&"delete"===o.operator&&"Identifier"===o.argument.type?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):t=!0,n=this.finishNode(o,a?"UpdateExpression":"UnaryExpression")}else{if(n=this.parseExprSubscripts(e),this.checkExpressionErrors(e))return n;for(;this.type.postfix&&!this.canInsertSemicolon();){var u=r.startNodeAt(i,s);u.operator=r.value,u.prefix=!1,u.argument=n,r.checkLVal(n),r.next(),n=r.finishNode(u,"UpdateExpression")}}return!t&&this.eat(F.starstar)?this.buildBinary(i,s,n,this.parseMaybeUnary(null,!1),"**",!1):n},ee.parseExprSubscripts=function(e){var t=this.start,n=this.startLoc,r=this.parseExprAtom(e),i="ArrowFunctionExpression"===r.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd);if(this.checkExpressionErrors(e)||i)return r;var s=this.parseSubscripts(r,t,n);return e&&"MemberExpression"===s.type&&(e.parenthesizedAssign>=s.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=s.start&&(e.parenthesizedBind=-1)),s},ee.parseSubscripts=function(e,t,n,r){for(var i,s=this,o=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd==e.end&&!this.canInsertSemicolon();;)if((i=s.eat(F.bracketL))||s.eat(F.dot)){var a=s.startNodeAt(t,n);a.object=e,a.property=i?s.parseExpression():s.parseIdent(!0),a.computed=!!i,i&&s.expect(F.bracketR),e=s.finishNode(a,"MemberExpression")}else if(!r&&s.eat(F.parenL)){var u=new Y,c=s.yieldPos,l=s.awaitPos;s.yieldPos=0,s.awaitPos=0;var p=s.parseExprList(F.parenR,s.options.ecmaVersion>=8,!1,u);if(o&&!s.canInsertSemicolon()&&s.eat(F.arrow))return s.checkPatternErrors(u,!1),s.checkYieldAwaitInDefaultParams(),s.yieldPos=c,s.awaitPos=l,s.parseArrowExpression(s.startNodeAt(t,n),p,!0);s.checkExpressionErrors(u,!0),s.yieldPos=c||s.yieldPos,s.awaitPos=l||s.awaitPos;var h=s.startNodeAt(t,n);h.callee=e,h.arguments=p,e=s.finishNode(h,"CallExpression")}else{if(s.type!==F.backQuote)return e;var f=s.startNodeAt(t,n);f.tag=e,f.quasi=s.parseTemplate(),e=s.finishNode(f,"TaggedTemplateExpression")}},ee.parseExprAtom=function(e){var t,n=this.potentialArrowAt==this.start;switch(this.type){case F._super:this.inFunction||this.raise(this.start,"'super' outside of function or class");case F._this:var r=this.type===F._this?"ThisExpression":"Super";return t=this.startNode(),this.next(),this.finishNode(t,r);case F.name:var i=this.start,s=this.startLoc,o=this.parseIdent(this.type!==F.name);if(this.options.ecmaVersion>=8&&"async"===o.name&&!this.canInsertSemicolon()&&this.eat(F._function))return this.parseFunction(this.startNodeAt(i,s),!1,!1,!0);if(n&&!this.canInsertSemicolon()){if(this.eat(F.arrow))return this.parseArrowExpression(this.startNodeAt(i,s),[o],!1);if(this.options.ecmaVersion>=8&&"async"===o.name&&this.type===F.name)return o=this.parseIdent(),!this.canInsertSemicolon()&&this.eat(F.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(i,s),[o],!0)}return o;case F.regexp:var a=this.value;return t=this.parseLiteral(a.value),t.regex={pattern:a.pattern,flags:a.flags},t;case F.num:case F.string:return this.parseLiteral(this.value);case F._null:case F._true:case F._false:return t=this.startNode(),t.value=this.type===F._null?null:this.type===F._true,t.raw=this.type.keyword,this.next(),this.finishNode(t,"Literal");case F.parenL:var u=this.start,c=this.parseParenAndDistinguishExpression(n);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(e.parenthesizedAssign=u),e.parenthesizedBind<0&&(e.parenthesizedBind=u)),c;case F.bracketL:return t=this.startNode(),this.next(),t.elements=this.parseExprList(F.bracketR,!0,!0,e),this.finishNode(t,"ArrayExpression");case F.braceL:return this.parseObj(!1,e);case F._function:return t=this.startNode(),this.next(),this.parseFunction(t,!1);case F._class:return this.parseClass(this.startNode(),!1);case F._new:return this.parseNew();case F.backQuote:return this.parseTemplate();default:this.unexpected()}},ee.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),this.next(),this.finishNode(t,"Literal")},ee.parseParenExpression=function(){this.expect(F.parenL);var e=this.parseExpression();return this.expect(F.parenR),e},ee.parseParenAndDistinguishExpression=function(e){var t,n=this,r=this.start,i=this.startLoc,s=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var o,a,u=this.start,c=this.startLoc,l=[],p=!0,h=!1,f=new Y,d=this.yieldPos,y=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==F.parenR;){if(p?p=!1:n.expect(F.comma),s&&n.afterTrailingComma(F.parenR,!0)){h=!0;break}if(n.type===F.ellipsis){o=n.start,l.push(n.parseParenItem(n.parseRest())),n.type===F.comma&&n.raise(n.start,"Comma is not permitted after the rest element");break}n.type!==F.parenL||a||(a=n.start),l.push(n.parseMaybeAssign(!1,f,n.parseParenItem))}var m=this.start,g=this.startLoc;if(this.expect(F.parenR),e&&!this.canInsertSemicolon()&&this.eat(F.arrow))return this.checkPatternErrors(f,!1),this.checkYieldAwaitInDefaultParams(),a&&this.unexpected(a),this.yieldPos=d,this.awaitPos=y,this.parseParenArrowList(r,i,l);l.length&&!h||this.unexpected(this.lastTokStart),o&&this.unexpected(o),this.checkExpressionErrors(f,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=y||this.awaitPos,l.length>1?((t=this.startNodeAt(u,c)).expressions=l,this.finishNodeAt(t,"SequenceExpression",m,g)):t=l[0]}else t=this.parseParenExpression();if(this.options.preserveParens){var v=this.startNodeAt(r,i);return v.expression=t,this.finishNode(v,"ParenthesizedExpression")}return t},ee.parseParenItem=function(e){return e},ee.parseParenArrowList=function(e,t,n){return this.parseArrowExpression(this.startNodeAt(e,t),n)};var te=[];ee.parseNew=function(){var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(F.dot))return e.meta=t,e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is new.target"),this.inFunction||this.raiseRecoverable(e.start,"new.target can only be used in functions"),this.finishNode(e,"MetaProperty");var n=this.start,r=this.startLoc;return e.callee=this.parseSubscripts(this.parseExprAtom(),n,r,!0),this.eat(F.parenL)?e.arguments=this.parseExprList(F.parenR,this.options.ecmaVersion>=8,!1):e.arguments=te,this.finishNode(e,"NewExpression")},ee.parseTemplateElement=function(){var e=this.startNode();return e.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),e.tail=this.type===F.backQuote,this.finishNode(e,"TemplateElement")},ee.parseTemplate=function(){var e=this,t=this.startNode();this.next(),t.expressions=[];var n=this.parseTemplateElement();for(t.quasis=[n];!n.tail;)e.expect(F.dollarBraceL),t.expressions.push(e.parseExpression()),e.expect(F.braceR),t.quasis.push(n=e.parseTemplateElement());return this.next(),this.finishNode(t,"TemplateLiteral")},ee.parseObj=function(e,t){var n=this,r=this.startNode(),i=!0,s={};for(r.properties=[],this.next();!this.eat(F.braceR);){if(i)i=!1;else if(n.expect(F.comma),n.afterTrailingComma(F.braceR))break;var o,a,u,c,l=n.startNode();n.options.ecmaVersion>=6&&(l.method=!1,l.shorthand=!1,(e||t)&&(u=n.start,c=n.startLoc),e||(o=n.eat(F.star))),n.parsePropertyName(l),e||!(n.options.ecmaVersion>=8)||o||l.computed||"Identifier"!==l.key.type||"async"!==l.key.name||n.type===F.parenL||n.type===F.colon||n.canInsertSemicolon()?a=!1:(a=!0,n.parsePropertyName(l,t)),n.parsePropertyValue(l,e,o,a,u,c,t),n.checkPropClash(l,s),r.properties.push(n.finishNode(l,"Property"))}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")},ee.parsePropertyValue=function(e,t,n,r,i,s,o){if((n||r)&&this.type===F.colon&&this.unexpected(),this.eat(F.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,o),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===F.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(n,r);else if(this.options.ecmaVersion>=5&&!e.computed&&"Identifier"===e.key.type&&("get"===e.key.name||"set"===e.key.name)&&this.type!=F.comma&&this.type!=F.braceR){(n||r||t)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var a="get"===e.kind?0:1;if(e.value.params.length!==a){var u=e.value.start;"get"===e.kind?this.raiseRecoverable(u,"getter should have no params"):this.raiseRecoverable(u,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}else this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((this.keywords.test(e.key.name)||(this.strict?this.reservedWordsStrict:this.reservedWords).test(e.key.name)||this.inGenerator&&"yield"==e.key.name||this.inAsync&&"await"==e.key.name)&&this.raiseRecoverable(e.key.start,"'"+e.key.name+"' can not be used as shorthand property"),e.kind="init",t?e.value=this.parseMaybeDefault(i,s,e.key):this.type===F.eq&&o?(o.shorthandAssign<0&&(o.shorthandAssign=this.start),e.value=this.parseMaybeDefault(i,s,e.key)):e.value=e.key,e.shorthand=!0):this.unexpected()},ee.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(F.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(F.bracketR),e.key;e.computed=!1}return e.key=this.type===F.num||this.type===F.string?this.parseExprAtom():this.parseIdent(!0)},ee.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=!1,e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},ee.parseMethod=function(e,t){var n=this.startNode(),r=this.inGenerator,i=this.inAsync,s=this.yieldPos,o=this.awaitPos,a=this.inFunction;return this.initFunction(n),this.options.ecmaVersion>=6&&(n.generator=e),this.options.ecmaVersion>=8&&(n.async=!!t),this.inGenerator=n.generator,this.inAsync=n.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,this.enterFunctionScope(),this.expect(F.parenL),n.params=this.parseBindingList(F.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(n,!1),this.inGenerator=r,this.inAsync=i,this.yieldPos=s,this.awaitPos=o,this.inFunction=a,this.finishNode(n,"FunctionExpression")},ee.parseArrowExpression=function(e,t,n){var r=this.inGenerator,i=this.inAsync,s=this.yieldPos,o=this.awaitPos,a=this.inFunction;return this.enterFunctionScope(),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!n),this.inGenerator=!1,this.inAsync=e.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0),this.inGenerator=r,this.inAsync=i,this.yieldPos=s,this.awaitPos=o,this.inFunction=a,this.finishNode(e,"ArrowFunctionExpression")},ee.parseFunctionBody=function(e,t){var n=t&&this.type!==F.braceL,r=this.strict,i=!1;if(n)e.body=this.parseMaybeAssign(),e.expression=!0,this.checkParams(e,!1);else{var s=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);r&&!s||(i=this.strictDirective(this.end))&&s&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var o=this.labels;this.labels=[],i&&(this.strict=!0),this.checkParams(e,!r&&!i&&!t&&this.isSimpleParamList(e.params)),e.body=this.parseBlock(!1),e.expression=!1,this.labels=o}this.exitFunctionScope(),this.strict&&e.id&&this.checkLVal(e.id,"none"),this.strict=r},ee.isSimpleParamList=function(e){for(var t=0;t<e.length;t++)if("Identifier"!==e[t].type)return!1;return!0},ee.checkParams=function(e,t){for(var n=this,r={},i=0;i<e.params.length;i++)n.checkLVal(e.params[i],"var",t?null:r)},ee.parseExprList=function(e,t,n,r){for(var i=this,s=[],o=!0;!this.eat(e);){if(o)o=!1;else if(i.expect(F.comma),t&&i.afterTrailingComma(e))break;var a;n&&i.type===F.comma?a=null:i.type===F.ellipsis?(a=i.parseSpread(r),r&&i.type===F.comma&&r.trailingComma<0&&(r.trailingComma=i.start)):a=i.parseMaybeAssign(!1,r),s.push(a)}return s},ee.parseIdent=function(e){var t=this.startNode();return e&&"never"==this.options.allowReserved&&(e=!1),this.type===F.name?(!e&&(this.strict?this.reservedWordsStrict:this.reservedWords).test(this.value)&&(this.options.ecmaVersion>=6||-1==this.input.slice(this.start,this.end).indexOf("\\"))&&this.raiseRecoverable(this.start,"The keyword '"+this.value+"' is reserved"),this.inGenerator&&"yield"===this.value&&this.raiseRecoverable(this.start,"Can not use 'yield' as identifier inside a generator"),this.inAsync&&"await"===this.value&&this.raiseRecoverable(this.start,"Can not use 'await' as identifier inside an async function"),t.name=this.value):e&&this.type.keyword?t.name=this.type.keyword:this.unexpected(),this.next(),this.finishNode(t,"Identifier")},ee.parseYield=function(){this.yieldPos||(this.yieldPos=this.start);var e=this.startNode();return this.next(),this.type==F.semi||this.canInsertSemicolon()||this.type!=F.star&&!this.type.startsExpr?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(F.star),e.argument=this.parseMaybeAssign()),this.finishNode(e,"YieldExpression")},ee.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var e=this.startNode();return this.next(),e.argument=this.parseMaybeUnary(null,!0),this.finishNode(e,"AwaitExpression")};var ne=W.prototype;ne.raise=function(e,t){var n=u(this.input,e);t+=" ("+n.line+":"+n.column+")";var r=new SyntaxError(t);throw r.pos=e,r.loc=n,r.raisedAt=this.pos,r},ne.raiseRecoverable=ne.raise,ne.curPosition=function(){if(this.options.locations)return new V(this.curLine,this.pos-this.lineStart)};var re=W.prototype,ie=Object.assign||function(e){for(var t=[],n=arguments.length-1;n-- >0;)t[n]=arguments[n+1];for(var r=0;r<t.length;r++){var i=t[r];for(var s in i)a(i,s)&&(e[s]=i[s])}return e};re.enterFunctionScope=function(){this.scopeStack.push({var:{},lexical:{},childVar:{},parentLexical:{}})},re.exitFunctionScope=function(){this.scopeStack.pop()},re.enterLexicalScope=function(){var e=this.scopeStack[this.scopeStack.length-1],t={var:{},lexical:{},childVar:{},parentLexical:{}};this.scopeStack.push(t),ie(t.parentLexical,e.lexical,e.parentLexical)},re.exitLexicalScope=function(){var e=this.scopeStack.pop(),t=this.scopeStack[this.scopeStack.length-1];ie(t.childVar,e.var,e.childVar)},re.canDeclareVarName=function(e){var t=this.scopeStack[this.scopeStack.length-1];return!a(t.lexical,e)&&!a(t.parentLexical,e)},re.canDeclareLexicalName=function(e){var t=this.scopeStack[this.scopeStack.length-1];return!a(t.lexical,e)&&!a(t.var,e)&&!a(t.childVar,e)},re.declareVarName=function(e){this.scopeStack[this.scopeStack.length-1].var[e]=!0},re.declareLexicalName=function(e){this.scopeStack[this.scopeStack.length-1].lexical[e]=!0};var se=function(e,t,n){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new q(e,n)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},oe=W.prototype;oe.startNode=function(){return new se(this,this.start,this.startLoc)},oe.startNodeAt=function(e,t){return new se(this,e,t)},oe.finishNode=function(e,t){return h.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},oe.finishNodeAt=function(e,t,n,r){return h.call(this,e,t,n,r)};var ae=function(e,t,n,r,i){this.token=e,this.isExpr=!!t,this.preserveSpace=!!n,this.override=r,this.generator=!!i},ue={b_stat:new ae("{",!1),b_expr:new ae("{",!0),b_tmpl:new ae("${",!0),p_stat:new ae("(",!1),p_expr:new ae("(",!0),q_tmpl:new ae("`",!0,!0,function(e){return e.readTmplToken()}),f_expr:new ae("function",!0),f_expr_gen:new ae("function",!0,!1,null,!0),f_gen:new ae("function",!1,!1,null,!0)},ce=W.prototype;ce.initialContext=function(){return[ue.b_stat]},ce.braceIsBlock=function(e){if(e===F.colon){var t=this.curContext();if(t===ue.b_stat||t===ue.b_expr)return!t.isExpr}return e===F._return?O.test(this.input.slice(this.lastTokEnd,this.start)):e===F._else||e===F.semi||e===F.eof||e===F.parenR||e==F.arrow||(e==F.braceL?this.curContext()===ue.b_stat:!this.exprAllowed)},ce.inGeneratorContext=function(){for(var e=this,t=this.context.length-1;t>=0;t--)if(e.context[t].generator)return!0;return!1},ce.updateContext=function(e){var t,n=this.type;n.keyword&&e==F.dot?this.exprAllowed=!1:(t=n.updateContext)?t.call(this,e):this.exprAllowed=n.beforeExpr},F.parenR.updateContext=F.braceR.updateContext=function(){if(1!=this.context.length){var e,t=this.context.pop();t===ue.b_stat&&(e=this.curContext())&&"function"===e.token?(this.context.pop(),this.exprAllowed=!1):this.exprAllowed=t===ue.b_tmpl||!t.isExpr}else this.exprAllowed=!0},F.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?ue.b_stat:ue.b_expr),this.exprAllowed=!0},F.dollarBraceL.updateContext=function(){this.context.push(ue.b_tmpl),this.exprAllowed=!0},F.parenL.updateContext=function(e){var t=e===F._if||e===F._for||e===F._with||e===F._while;this.context.push(t?ue.p_stat:ue.p_expr),this.exprAllowed=!0},F.incDec.updateContext=function(){},F._function.updateContext=function(e){e.beforeExpr&&e!==F.semi&&e!==F._else&&(e!==F.colon&&e!==F.braceL||this.curContext()!==ue.b_stat)&&this.context.push(ue.f_expr),this.exprAllowed=!1},F.backQuote.updateContext=function(){this.curContext()===ue.q_tmpl?this.context.pop():this.context.push(ue.q_tmpl),this.exprAllowed=!1},F.star.updateContext=function(e){e==F._function&&(this.curContext()===ue.f_expr?this.context[this.context.length-1]=ue.f_expr_gen:this.context.push(ue.f_gen)),this.exprAllowed=!0},F.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&("of"==this.value&&!this.exprAllowed||"yield"==this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var le=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new q(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},pe=W.prototype,he="object"==typeof Packages&&"[object JavaPackage]"==Object.prototype.toString.call(Packages);pe.next=function(){this.options.onToken&&this.options.onToken(new le(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},pe.getToken=function(){return this.next(),new le(this)},"undefined"!=typeof Symbol&&(pe[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===F.eof,value:t}}}}),pe.curContext=function(){return this.context[this.context.length-1]},pe.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(F.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},pe.readToken=function(e){return n(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},pe.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);return e<=55295||e>=57344?e:(e<<10)+this.input.charCodeAt(this.pos+1)-56613888},pe.skipBlockComment=function(){var e=this,t=this.options.onComment&&this.curPosition(),n=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(-1===r&&this.raise(this.pos-2,"Unterminated comment"),this.pos=r+2,this.options.locations){$.lastIndex=n;for(var i;(i=$.exec(this.input))&&i.index<this.pos;)++e.curLine,e.lineStart=i.index+i[0].length}this.options.onComment&&this.options.onComment(!0,this.input.slice(n+2,r),n,this.pos,t,this.curPosition())},pe.skipLineComment=function(e){for(var t=this,n=this.pos,r=this.options.onComment&&this.curPosition(),i=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&10!==i&&13!==i&&8232!==i&&8233!==i;)++t.pos,i=t.input.charCodeAt(t.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(n+e,this.pos),n,this.pos,r,this.curPosition())},pe.skipSpace=function(){var e=this;e:for(;this.pos<this.input.length;){var t=e.input.charCodeAt(e.pos);switch(t){case 32:case 160:++e.pos;break;case 13:10===e.input.charCodeAt(e.pos+1)&&++e.pos;case 10:case 8232:case 8233:++e.pos,e.options.locations&&(++e.curLine,e.lineStart=e.pos);break;case 47:switch(e.input.charCodeAt(e.pos+1)){case 42:e.skipBlockComment();break;case 47:e.skipLineComment(2);break;default:break e}break;default:if(!(t>8&&t<14||t>=5760&&B.test(String.fromCharCode(t))))break e;++e.pos}}},pe.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var n=this.type;this.type=e,this.value=t,this.updateContext(n)},pe.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(F.ellipsis)):(++this.pos,this.finishToken(F.dot))},pe.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(F.assign,2):this.finishOp(F.slash,1)},pe.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),n=1,r=42===e?F.star:F.modulo;return this.options.ecmaVersion>=7&&42===t&&(++n,r=F.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(F.assign,n+1):this.finishOp(r,n)},pe.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.finishOp(124===e?F.logicalOR:F.logicalAND,2):61===t?this.finishOp(F.assign,2):this.finishOp(124===e?F.bitwiseOR:F.bitwiseAND,1)},pe.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(F.assign,2):this.finishOp(F.bitwiseXOR,1)},pe.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45==t&&62==this.input.charCodeAt(this.pos+2)&&O.test(this.input.slice(this.lastTokEnd,this.pos))?(this.skipLineComment(3),this.skipSpace(),this.nextToken()):this.finishOp(F.incDec,2):61===t?this.finishOp(F.assign,2):this.finishOp(F.plusMin,1)},pe.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),n=1;return t===e?(n=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+n)?this.finishOp(F.assign,n+1):this.finishOp(F.bitShift,n)):33==t&&60==e&&45==this.input.charCodeAt(this.pos+2)&&45==this.input.charCodeAt(this.pos+3)?(this.inModule&&this.unexpected(),this.skipLineComment(4),this.skipSpace(),this.nextToken()):(61===t&&(n=2),this.finishOp(F.relational,n))},pe.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(F.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(F.arrow)):this.finishOp(61===e?F.eq:F.prefix,1)},pe.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(F.parenL);case 41:return++this.pos,this.finishToken(F.parenR);case 59:return++this.pos,this.finishToken(F.semi);case 44:return++this.pos,this.finishToken(F.comma);case 91:return++this.pos,this.finishToken(F.bracketL);case 93:return++this.pos,this.finishToken(F.bracketR);case 123:return++this.pos,this.finishToken(F.braceL);case 125:return++this.pos,this.finishToken(F.braceR);case 58:return++this.pos,this.finishToken(F.colon);case 63:return++this.pos,this.finishToken(F.question);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(F.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 126:return this.finishOp(F.prefix,1)}this.raise(this.pos,"Unexpected character '"+d(e)+"'")},pe.finishOp=function(e,t){var n=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,n)};var fe=!!f("￿","u");pe.readRegexp=function(){for(var e,t,n=this,r=this.pos;;){n.pos>=n.input.length&&n.raise(r,"Unterminated regular expression");var i=n.input.charAt(n.pos);if(O.test(i)&&n.raise(r,"Unterminated regular expression"),e)e=!1;else{if("["===i)t=!0;else if("]"===i&&t)t=!1;else if("/"===i&&!t)break;e="\\"===i}++n.pos}var s=this.input.slice(r,this.pos);++this.pos;var o=this.readWord1(),a=s,u="";if(o){var c=/^[gim]*$/;this.options.ecmaVersion>=6&&(c=/^[gimuy]*$/),c.test(o)||this.raise(r,"Invalid regular expression flag"),o.indexOf("u")>=0&&(fe?u="u":(a=(a=a.replace(/\\u\{([0-9a-fA-F]+)\}/g,function(e,t,i){return(t=Number("0x"+t))>1114111&&n.raise(r+i+3,"Code point out of bounds"),"x"})).replace(/\\u([a-fA-F0-9]{4})|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x"),u=u.replace("u","")))}var l=null;return he||(f(a,u,r,this),l=f(s,o)),this.finishToken(F.regexp,{pattern:s,flags:o,value:l})},pe.readInt=function(e,t){for(var n=this,r=this.pos,i=0,s=0,o=null==t?1/0:t;s<o;++s){var a,u=n.input.charCodeAt(n.pos);if((a=u>=97?u-97+10:u>=65?u-65+10:u>=48&&u<=57?u-48:1/0)>=e)break;++n.pos,i=i*e+a}return this.pos===r||null!=t&&this.pos-r!==t?null:i},pe.readRadixNumber=function(e){this.pos+=2;var t=this.readInt(e);return null==t&&this.raise(this.start+2,"Expected number in radix "+e),n(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(F.num,t)},pe.readNumber=function(e){var t=this.pos,r=!1,i=48===this.input.charCodeAt(this.pos);e||null!==this.readInt(10)||this.raise(t,"Invalid number"),i&&this.pos==t+1&&(i=!1);var s=this.input.charCodeAt(this.pos);46!==s||i||(++this.pos,this.readInt(10),r=!0,s=this.input.charCodeAt(this.pos)),69!==s&&101!==s||i||(43!==(s=this.input.charCodeAt(++this.pos))&&45!==s||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number"),r=!0),n(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var o,a=this.input.slice(t,this.pos);return r?o=parseFloat(a):i&&1!==a.length?/[89]/.test(a)||this.strict?this.raise(t,"Invalid number"):o=parseInt(a,8):o=parseInt(a,10),this.finishToken(F.num,o)},pe.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.raise(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},pe.readString=function(e){for(var t=this,n="",r=++this.pos;;){t.pos>=t.input.length&&t.raise(t.start,"Unterminated string constant");var i=t.input.charCodeAt(t.pos);if(i===e)break;92===i?(n+=t.input.slice(r,t.pos),n+=t.readEscapedChar(!1),r=t.pos):(o(i)&&t.raise(t.start,"Unterminated string constant"),++t.pos)}return n+=this.input.slice(r,this.pos++),this.finishToken(F.string,n)},pe.readTmplToken=function(){for(var e=this,t="",n=this.pos;;){e.pos>=e.input.length&&e.raise(e.start,"Unterminated template");var r=e.input.charCodeAt(e.pos);if(96===r||36===r&&123===e.input.charCodeAt(e.pos+1))return e.pos===e.start&&e.type===F.template?36===r?(e.pos+=2,e.finishToken(F.dollarBraceL)):(++e.pos,e.finishToken(F.backQuote)):(t+=e.input.slice(n,e.pos),e.finishToken(F.template,t));if(92===r)t+=e.input.slice(n,e.pos),t+=e.readEscapedChar(!0),n=e.pos;else if(o(r)){switch(t+=e.input.slice(n,e.pos),++e.pos,r){case 13:10===e.input.charCodeAt(e.pos)&&++e.pos;case 10:t+="\n";break;default:t+=String.fromCharCode(r)}e.options.locations&&(++e.curLine,e.lineStart=e.pos),n=e.pos}else++e.pos}},pe.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return d(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";default:if(t>=48&&t<=55){var n=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(n,8);return r>255&&(n=n.slice(0,-1),r=parseInt(n,8)),"0"!==n&&(this.strict||e)&&this.raise(this.pos-2,"Octal literal in strict mode"),this.pos+=n.length-1,String.fromCharCode(r)}return String.fromCharCode(t)}},pe.readHexChar=function(e){var t=this.pos,n=this.readInt(16,e);return null===n&&this.raise(t,"Bad character escape sequence"),n},pe.readWord1=function(){var e=this;this.containsEsc=!1;for(var t="",i=!0,s=this.pos,o=this.options.ecmaVersion>=6;this.pos<this.input.length;){var a=e.fullCharCodeAtPos();if(r(a,o))e.pos+=a<=65535?1:2;else{if(92!==a)break;e.containsEsc=!0,t+=e.input.slice(s,e.pos);var u=e.pos;117!=e.input.charCodeAt(++e.pos)&&e.raise(e.pos,"Expecting Unicode escape sequence \\uXXXX"),++e.pos;var c=e.readCodePoint();(i?n:r)(c,o)||e.raise(u,"Invalid Unicode escape"),t+=d(c),s=e.pos}i=!1}return t+this.input.slice(s,this.pos)},pe.readWord=function(){var e=this.readWord1(),t=F.name;return this.keywords.test(e)&&(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+e),t=N[e]),this.finishToken(t,e)};e.version="5.0.3",e.parse=y,e.parseExpressionAt=m,e.tokenizer=g,e.addLooseExports=v,e.Parser=W,e.plugins=z,e.defaultOptions=U,e.Position=V,e.SourceLocation=q,e.getLineInfo=u,e.Node=se,e.TokenType=L,e.tokTypes=F,e.keywordTypes=N,e.TokContext=ae,e.tokContexts=ue,e.isIdentifierChar=r,e.isIdentifierStart=n,e.Token=le,e.isNewLine=o,e.lineBreak=O,e.lineBreakG=$,e.nonASCIIwhitespace=B,Object.defineProperty(e,"__esModule",{value:!0})})},{}],16:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?r(n):"function"==typeof define&&define.amd?define(["exports"],r):r((e.acorn=e.acorn||{},e.acorn.walk=e.acorn.walk||{}))}(this,function(e){"use strict";function t(t,n,r,i,s){r||(r=e.base),function e(t,i,s){var o=s||t.type,a=n[o];r[o](t,i,e),a&&a(t,i)}(t,i,s)}function n(t,n,r,i){r||(r=e.base);var s=[];!function e(t,i,o){var a=o||t.type,u=n[a],c=t!=s[s.length-1];c&&s.push(t),r[a](t,i,e),u&&u(t,i||s,s),c&&s.pop()}(t,i)}function r(t,n,r,i,s){var o=r?e.make(r,i):i;!function e(t,n,r){o[r||t.type](t,n,e)}(t,n,s)}function i(e){return"string"==typeof e?function(t){return t==e}:e||function(){return!0}}function s(t,n,r,s,o,a){s=i(s),o||(o=e.base);try{!function e(t,i,a){var u=a||t.type;if((null==n||t.start<=n)&&(null==r||t.end>=r)&&o[u](t,i,e),(null==n||t.start==n)&&(null==r||t.end==r)&&s(u,t))throw new h(t,i)}(t,a)}catch(e){if(e instanceof h)return e;throw e}}function o(t,n,r,s,o){r=i(r),s||(s=e.base);try{!function e(t,i,o){var a=o||t.type;if(!(t.start>n||t.end<n)&&(s[a](t,i,e),r(a,t)))throw new h(t,i)}(t,o)}catch(e){if(e instanceof h)return e;throw e}}function a(t,n,r,s,o){r=i(r),s||(s=e.base);try{!function e(t,i,o){if(!(t.end<n)){var a=o||t.type;if(t.start>=n&&r(a,t))throw new h(t,i);s[a](t,i,e)}}(t,o)}catch(e){if(e instanceof h)return e;throw e}}function u(t,n,r,s,o){r=i(r),s||(s=e.base);var a;return function e(t,i,o){if(!(t.start>n)){var u=o||t.type;t.end<=n&&(!a||a.node.end<t.end)&&r(u,t)&&(a=new h(t,i)),s[u](t,i,e)}}(t,o),a}function c(t,n){n||(n=e.base);var r=f(n);for(var i in t)r[i]=t[i];return r}function l(e,t,n){n(e,t)}function p(e,t,n){}var h=function(e,t){this.node=e,this.state=t},f=Object.create||function(e){function t(){}return t.prototype=e,new t},d={};d.Program=d.BlockStatement=function(e,t,n){for(var r=0;r<e.body.length;++r)n(e.body[r],t,"Statement")},d.Statement=l,d.EmptyStatement=p,d.ExpressionStatement=d.ParenthesizedExpression=function(e,t,n){return n(e.expression,t,"Expression")},d.IfStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Statement"),e.alternate&&n(e.alternate,t,"Statement")},d.LabeledStatement=function(e,t,n){return n(e.body,t,"Statement")},d.BreakStatement=d.ContinueStatement=p,d.WithStatement=function(e,t,n){n(e.object,t,"Expression"),n(e.body,t,"Statement")},d.SwitchStatement=function(e,t,n){n(e.discriminant,t,"Expression");for(var r=0;r<e.cases.length;++r){var i=e.cases[r];i.test&&n(i.test,t,"Expression");for(var s=0;s<i.consequent.length;++s)n(i.consequent[s],t,"Statement")}},d.ReturnStatement=d.YieldExpression=d.AwaitExpression=function(e,t,n){e.argument&&n(e.argument,t,"Expression")},d.ThrowStatement=d.SpreadElement=function(e,t,n){return n(e.argument,t,"Expression")},d.TryStatement=function(e,t,n){n(e.block,t,"Statement"),e.handler&&n(e.handler,t),e.finalizer&&n(e.finalizer,t,"Statement")},d.CatchClause=function(e,t,n){n(e.param,t,"Pattern"),n(e.body,t,"ScopeBody")},d.WhileStatement=d.DoWhileStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.body,t,"Statement")},d.ForStatement=function(e,t,n){e.init&&n(e.init,t,"ForInit"),e.test&&n(e.test,t,"Expression"),e.update&&n(e.update,t,"Expression"),n(e.body,t,"Statement")},d.ForInStatement=d.ForOfStatement=function(e,t,n){n(e.left,t,"ForInit"),n(e.right,t,"Expression"),n(e.body,t,"Statement")},d.ForInit=function(e,t,n){"VariableDeclaration"==e.type?n(e,t):n(e,t,"Expression")},d.DebuggerStatement=p,d.FunctionDeclaration=function(e,t,n){return n(e,t,"Function")},d.VariableDeclaration=function(e,t,n){for(var r=0;r<e.declarations.length;++r)n(e.declarations[r],t)},d.VariableDeclarator=function(e,t,n){n(e.id,t,"Pattern"),e.init&&n(e.init,t,"Expression")},d.Function=function(e,t,n){e.id&&n(e.id,t,"Pattern");for(var r=0;r<e.params.length;r++)n(e.params[r],t,"Pattern");n(e.body,t,e.expression?"ScopeExpression":"ScopeBody")},d.ScopeBody=function(e,t,n){return n(e,t,"Statement")},d.ScopeExpression=function(e,t,n){return n(e,t,"Expression")},d.Pattern=function(e,t,n){"Identifier"==e.type?n(e,t,"VariablePattern"):"MemberExpression"==e.type?n(e,t,"MemberPattern"):n(e,t)},d.VariablePattern=p,d.MemberPattern=l,d.RestElement=function(e,t,n){return n(e.argument,t,"Pattern")},d.ArrayPattern=function(e,t,n){for(var r=0;r<e.elements.length;++r){var i=e.elements[r];i&&n(i,t,"Pattern")}},d.ObjectPattern=function(e,t,n){for(var r=0;r<e.properties.length;++r)n(e.properties[r].value,t,"Pattern")},d.Expression=l,d.ThisExpression=d.Super=d.MetaProperty=p,d.ArrayExpression=function(e,t,n){for(var r=0;r<e.elements.length;++r){var i=e.elements[r];i&&n(i,t,"Expression")}},d.ObjectExpression=function(e,t,n){for(var r=0;r<e.properties.length;++r)n(e.properties[r],t)},d.FunctionExpression=d.ArrowFunctionExpression=d.FunctionDeclaration,d.SequenceExpression=d.TemplateLiteral=function(e,t,n){for(var r=0;r<e.expressions.length;++r)n(e.expressions[r],t,"Expression")},d.UnaryExpression=d.UpdateExpression=function(e,t,n){n(e.argument,t,"Expression")},d.BinaryExpression=d.LogicalExpression=function(e,t,n){n(e.left,t,"Expression"),n(e.right,t,"Expression")},d.AssignmentExpression=d.AssignmentPattern=function(e,t,n){n(e.left,t,"Pattern"),n(e.right,t,"Expression")},d.ConditionalExpression=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Expression"),n(e.alternate,t,"Expression")},d.NewExpression=d.CallExpression=function(e,t,n){if(n(e.callee,t,"Expression"),e.arguments)for(var r=0;r<e.arguments.length;++r)n(e.arguments[r],t,"Expression")},d.MemberExpression=function(e,t,n){n(e.object,t,"Expression"),e.computed&&n(e.property,t,"Expression")},d.ExportNamedDeclaration=d.ExportDefaultDeclaration=function(e,t,n){e.declaration&&n(e.declaration,t,"ExportNamedDeclaration"==e.type||e.declaration.id?"Statement":"Expression"),e.source&&n(e.source,t,"Expression")},d.ExportAllDeclaration=function(e,t,n){n(e.source,t,"Expression")},d.ImportDeclaration=function(e,t,n){for(var r=0;r<e.specifiers.length;r++)n(e.specifiers[r],t);n(e.source,t,"Expression")},d.ImportSpecifier=d.ImportDefaultSpecifier=d.ImportNamespaceSpecifier=d.Identifier=d.Literal=p,d.TaggedTemplateExpression=function(e,t,n){n(e.tag,t,"Expression"),n(e.quasi,t)},d.ClassDeclaration=d.ClassExpression=function(e,t,n){return n(e,t,"Class")},d.Class=function(e,t,n){e.id&&n(e.id,t,"Pattern"),e.superClass&&n(e.superClass,t,"Expression");for(var r=0;r<e.body.body.length;r++)n(e.body.body[r],t)},d.MethodDefinition=d.Property=function(e,t,n){e.computed&&n(e.key,t,"Expression"),n(e.value,t,"Expression")},e.simple=t,e.ancestor=n,e.recursive=r,e.findNodeAt=s,e.findNodeAround=o,e.findNodeAfter=a,e.findNodeBefore=u,e.make=c,e.base=d,Object.defineProperty(e,"__esModule",{value:!0})})},{}],17:[function(e,t,n){function r(){this._array=[],this._set=Object.create(null)}var i=e("./util"),s=Object.prototype.hasOwnProperty;r.fromArray=function(e,t){for(var n=new r,i=0,s=e.length;i<s;i++)n.add(e[i],t);return n},r.prototype.size=function(){return Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(e,t){var n=i.toSetString(e),r=s.call(this._set,n),o=this._array.length;r&&!t||this._array.push(e),r||(this._set[n]=o)},r.prototype.has=function(e){var t=i.toSetString(e);return s.call(this._set,t)},r.prototype.indexOf=function(e){var t=i.toSetString(e);if(s.call(this._set,t))return this._set[t];throw new Error('"'+e+'" is not in the set.')},r.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},r.prototype.toArray=function(){return this._array.slice()},n.ArraySet=r},{"./util":26}],18:[function(e,t,n){function r(e){return e<0?1+(-e<<1):0+(e<<1)}function i(e){var t=e>>1;return 1==(1&e)?-t:t}var s=e("./base64");n.encode=function(e){var t,n="",i=r(e);do{t=31&i,(i>>>=5)>0&&(t|=32),n+=s.encode(t)}while(i>0);return n},n.decode=function(e,t,n){var r,o,a=e.length,u=0,c=0;do{if(t>=a)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(o=s.decode(e.charCodeAt(t++))))throw new Error("Invalid base64 digit: "+e.charAt(t-1));r=!!(32&o),u+=(o&=31)<<c,c+=5}while(r);n.value=i(u),n.rest=t}},{"./base64":19}],19:[function(e,t,n){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n.encode=function(e){if(0<=e&&e<r.length)return r[e];throw new TypeError("Must be between 0 and 63: "+e)},n.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}},{}],20:[function(e,t,n){function r(e,t,i,s,o,a){var u=Math.floor((t-e)/2)+e,c=o(i,s[u],!0);return 0===c?u:c>0?t-u>1?r(u,t,i,s,o,a):a==n.LEAST_UPPER_BOUND?t<s.length?t:-1:u:u-e>1?r(e,u,i,s,o,a):a==n.LEAST_UPPER_BOUND?u:e<0?-1:e}n.GREATEST_LOWER_BOUND=1,n.LEAST_UPPER_BOUND=2,n.search=function(e,t,i,s){if(0===t.length)return-1;var o=r(-1,t.length,e,t,i,s||n.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&0===i(t[o],t[o-1],!0);)--o;return o}},{}],21:[function(e,t,n){function r(e,t){var n=e.generatedLine,r=t.generatedLine,i=e.generatedColumn,o=t.generatedColumn;return r>n||r==n&&o>=i||s.compareByGeneratedPositionsInflated(e,t)<=0}function i(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}var s=e("./util");i.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},i.prototype.add=function(e){r(this._last,e)?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},i.prototype.toArray=function(){return this._sorted||(this._array.sort(s.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},n.MappingList=i},{"./util":26}],22:[function(e,t,n){function r(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function i(e,t){return Math.round(e+Math.random()*(t-e))}function s(e,t,n,o){if(n<o){var a=n-1;r(e,i(n,o),o);for(var u=e[o],c=n;c<o;c++)t(e[c],u)<=0&&r(e,a+=1,c);r(e,a+1,c);var l=a+1;s(e,t,n,l-1),s(e,t,l+1,o)}}n.quickSort=function(e,t){s(e,t,0,e.length-1)}},{}],23:[function(e,t,n){function r(e){var t=e;return"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,""))),null!=t.sections?new o(t):new i(t)}function i(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=a.getArg(t,"version"),r=a.getArg(t,"sources"),i=a.getArg(t,"names",[]),s=a.getArg(t,"sourceRoot",null),o=a.getArg(t,"sourcesContent",null),u=a.getArg(t,"mappings"),l=a.getArg(t,"file",null);if(n!=this._version)throw new Error("Unsupported version: "+n);r=r.map(String).map(a.normalize).map(function(e){return s&&a.isAbsolute(s)&&a.isAbsolute(e)?a.relative(s,e):e}),this._names=c.fromArray(i.map(String),!0),this._sources=c.fromArray(r,!0),this.sourceRoot=s,this.sourcesContent=o,this._mappings=u,this.file=l}function s(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function o(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=a.getArg(t,"version"),i=a.getArg(t,"sections");if(n!=this._version)throw new Error("Unsupported version: "+n);this._sources=new c,this._names=new c;var s={line:-1,column:0};this._sections=i.map(function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var t=a.getArg(e,"offset"),n=a.getArg(t,"line"),i=a.getArg(t,"column");if(n<s.line||n===s.line&&i<s.column)throw new Error("Section offsets must be ordered and non-overlapping.");return s=t,{generatedOffset:{generatedLine:n+1,generatedColumn:i+1},consumer:new r(a.getArg(e,"map"))}})}var a=e("./util"),u=e("./binary-search"),c=e("./array-set").ArraySet,l=e("./base64-vlq"),p=e("./quick-sort").quickSort;r.fromSourceMap=function(e){return i.fromSourceMap(e)},r.prototype._version=3,r.prototype.__generatedMappings=null,Object.defineProperty(r.prototype,"_generatedMappings",{get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),r.prototype.__originalMappings=null,Object.defineProperty(r.prototype,"_originalMappings",{get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),r.prototype._charIsMappingSeparator=function(e,t){var n=e.charAt(t);return";"===n||","===n},r.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")},r.GENERATED_ORDER=1,r.ORIGINAL_ORDER=2,r.GREATEST_LOWER_BOUND=1,r.LEAST_UPPER_BOUND=2,r.prototype.eachMapping=function(e,t,n){var i,s=t||null;switch(n||r.GENERATED_ORDER){case r.GENERATED_ORDER:i=this._generatedMappings;break;case r.ORIGINAL_ORDER:i=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var o=this.sourceRoot;i.map(function(e){var t=null===e.source?null:this._sources.at(e.source);return null!=t&&null!=o&&(t=a.join(o,t)),{source:t,generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,s)},r.prototype.allGeneratedPositionsFor=function(e){var t=a.getArg(e,"line"),n={source:a.getArg(e,"source"),originalLine:t,originalColumn:a.getArg(e,"column",0)};if(null!=this.sourceRoot&&(n.source=a.relative(this.sourceRoot,n.source)),!this._sources.has(n.source))return[];n.source=this._sources.indexOf(n.source);var r=[],i=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",a.compareByOriginalPositions,u.LEAST_UPPER_BOUND);if(i>=0){var s=this._originalMappings[i];if(void 0===e.column)for(var o=s.originalLine;s&&s.originalLine===o;)r.push({line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++i];else for(var c=s.originalColumn;s&&s.originalLine===t&&s.originalColumn==c;)r.push({line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++i]}return r},n.SourceMapConsumer=r,i.prototype=Object.create(r.prototype),i.prototype.consumer=r,i.fromSourceMap=function(e){var t=Object.create(i.prototype),n=t._names=c.fromArray(e._names.toArray(),!0),r=t._sources=c.fromArray(e._sources.toArray(),!0);t.sourceRoot=e._sourceRoot,t.sourcesContent=e._generateSourcesContent(t._sources.toArray(),t.sourceRoot),t.file=e._file;for(var o=e._mappings.toArray().slice(),u=t.__generatedMappings=[],l=t.__originalMappings=[],h=0,f=o.length;h<f;h++){var d=o[h],y=new s;y.generatedLine=d.generatedLine,y.generatedColumn=d.generatedColumn,d.source&&(y.source=r.indexOf(d.source),y.originalLine=d.originalLine,y.originalColumn=d.originalColumn,d.name&&(y.name=n.indexOf(d.name)),l.push(y)),u.push(y)}return p(t.__originalMappings,a.compareByOriginalPositions),t},i.prototype._version=3,Object.defineProperty(i.prototype,"sources",{get:function(){return this._sources.toArray().map(function(e){return null!=this.sourceRoot?a.join(this.sourceRoot,e):e},this)}}),i.prototype._parseMappings=function(e,t){for(var n,r,i,o,u,c=1,h=0,f=0,d=0,y=0,m=0,g=e.length,v=0,b={},x={},w=[],E=[];v<g;)if(";"===e.charAt(v))c++,v++,h=0;else if(","===e.charAt(v))v++;else{for((n=new s).generatedLine=c,o=v;o<g&&!this._charIsMappingSeparator(e,o);o++);if(r=e.slice(v,o),i=b[r])v+=r.length;else{for(i=[];v<o;)l.decode(e,v,x),u=x.value,v=x.rest,i.push(u);if(2===i.length)throw new Error("Found a source, but no line and column");if(3===i.length)throw new Error("Found a source and line, but no column");b[r]=i}n.generatedColumn=h+i[0],h=n.generatedColumn,i.length>1&&(n.source=y+i[1],y+=i[1],n.originalLine=f+i[2],f=n.originalLine,n.originalLine+=1,n.originalColumn=d+i[3],d=n.originalColumn,i.length>4&&(n.name=m+i[4],m+=i[4])),E.push(n),"number"==typeof n.originalLine&&w.push(n)}p(E,a.compareByGeneratedPositionsDeflated),this.__generatedMappings=E,p(w,a.compareByOriginalPositions),this.__originalMappings=w},i.prototype._findMapping=function(e,t,n,r,i,s){if(e[n]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[n]);if(e[r]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[r]);return u.search(e,t,i,s)},i.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var n=this._generatedMappings[e+1];if(t.generatedLine===n.generatedLine){t.lastGeneratedColumn=n.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},i.prototype.originalPositionFor=function(e){var t={generatedLine:a.getArg(e,"line"),generatedColumn:a.getArg(e,"column")},n=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",a.compareByGeneratedPositionsDeflated,a.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(n>=0){var i=this._generatedMappings[n];if(i.generatedLine===t.generatedLine){var s=a.getArg(i,"source",null);null!==s&&(s=this._sources.at(s),null!=this.sourceRoot&&(s=a.join(this.sourceRoot,s)));var o=a.getArg(i,"name",null);return null!==o&&(o=this._names.at(o)),{source:s,line:a.getArg(i,"originalLine",null),column:a.getArg(i,"originalColumn",null),name:o}}}return{source:null,line:null,column:null,name:null}},i.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&(this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e}))},i.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;if(null!=this.sourceRoot&&(e=a.relative(this.sourceRoot,e)),this._sources.has(e))return this.sourcesContent[this._sources.indexOf(e)];var n;if(null!=this.sourceRoot&&(n=a.urlParse(this.sourceRoot))){var r=e.replace(/^file:\/\//,"");if("file"==n.scheme&&this._sources.has(r))return this.sourcesContent[this._sources.indexOf(r)];if((!n.path||"/"==n.path)&&this._sources.has("/"+e))return this.sourcesContent[this._sources.indexOf("/"+e)]}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},i.prototype.generatedPositionFor=function(e){var t=a.getArg(e,"source");if(null!=this.sourceRoot&&(t=a.relative(this.sourceRoot,t)),!this._sources.has(t))return{line:null,column:null,lastColumn:null};var n={source:t=this._sources.indexOf(t),originalLine:a.getArg(e,"line"),originalColumn:a.getArg(e,"column")},i=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",a.compareByOriginalPositions,a.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(i>=0){var s=this._originalMappings[i];if(s.source===n.source)return{line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},n.BasicSourceMapConsumer=i,o.prototype=Object.create(r.prototype),o.prototype.constructor=r,o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var n=0;n<this._sections[t].consumer.sources.length;n++)e.push(this._sections[t].consumer.sources[n]);return e}}),o.prototype.originalPositionFor=function(e){var t={generatedLine:a.getArg(e,"line"),generatedColumn:a.getArg(e,"column")},n=u.search(t,this._sections,function(e,t){var n=e.generatedLine-t.generatedOffset.generatedLine;return n||e.generatedColumn-t.generatedOffset.generatedColumn}),r=this._sections[n];return r?r.consumer.originalPositionFor({line:t.generatedLine-(r.generatedOffset.generatedLine-1),column:t.generatedColumn-(r.generatedOffset.generatedLine===t.generatedLine?r.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},o.prototype.sourceContentFor=function(e,t){for(var n=0;n<this._sections.length;n++){var r=this._sections[n].consumer.sourceContentFor(e,!0);if(r)return r}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var n=this._sections[t];if(-1!==n.consumer.sources.indexOf(a.getArg(e,"source"))){var r=n.consumer.generatedPositionFor(e);if(r)return{line:r.line+(n.generatedOffset.generatedLine-1),column:r.column+(n.generatedOffset.generatedLine===r.line?n.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},o.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var n=0;n<this._sections.length;n++)for(var r=this._sections[n],i=r.consumer._generatedMappings,s=0;s<i.length;s++){var o=i[s],u=r.consumer._sources.at(o.source);null!==r.consumer.sourceRoot&&(u=a.join(r.consumer.sourceRoot,u)),this._sources.add(u),u=this._sources.indexOf(u);var c=r.consumer._names.at(o.name);this._names.add(c),c=this._names.indexOf(c);var l={source:u,generatedLine:o.generatedLine+(r.generatedOffset.generatedLine-1),generatedColumn:o.generatedColumn+(r.generatedOffset.generatedLine===o.generatedLine?r.generatedOffset.generatedColumn-1:0),originalLine:o.originalLine,originalColumn:o.originalColumn,name:c};this.__generatedMappings.push(l),"number"==typeof l.originalLine&&this.__originalMappings.push(l)}p(this.__generatedMappings,a.compareByGeneratedPositionsDeflated),p(this.__originalMappings,a.compareByOriginalPositions)},n.IndexedSourceMapConsumer=o},{"./array-set":17,"./base64-vlq":18,"./binary-search":20,"./quick-sort":22,"./util":26}],24:[function(e,t,n){function r(e){e||(e={}),this._file=s.getArg(e,"file",null),this._sourceRoot=s.getArg(e,"sourceRoot",null),this._skipValidation=s.getArg(e,"skipValidation",!1),this._sources=new o,this._names=new o,this._mappings=new a,this._sourcesContents=null}var i=e("./base64-vlq"),s=e("./util"),o=e("./array-set").ArraySet,a=e("./mapping-list").MappingList;r.prototype._version=3,r.fromSourceMap=function(e){var t=e.sourceRoot,n=new r({file:e.file,sourceRoot:t});return e.eachMapping(function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=s.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)}),e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&n.setSourceContent(t,r)}),n},r.prototype.addMapping=function(e){var t=s.getArg(e,"generated"),n=s.getArg(e,"original",null),r=s.getArg(e,"source",null),i=s.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,i),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=i&&(i=String(i),this._names.has(i)||this._names.add(i)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:i})},r.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=s.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[s.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[s.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},r.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var i=this._sourceRoot;null!=i&&(r=s.relative(i,r));var a=new o,u=new o;this._mappings.unsortedForEach(function(t){if(t.source===r&&null!=t.originalLine){var o=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=o.source&&(t.source=o.source,null!=n&&(t.source=s.join(n,t.source)),null!=i&&(t.source=s.relative(i,t.source)),t.originalLine=o.line,t.originalColumn=o.column,null!=o.name&&(t.name=o.name))}var c=t.source;null==c||a.has(c)||a.add(c);var l=t.name;null==l||u.has(l)||u.add(l)},this),this._sources=a,this._names=u,e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=s.join(n,t)),null!=i&&(t=s.relative(i,t)),this.setSourceContent(t,r))},this)},r.prototype._validateMapping=function(e,t,n,r){if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},r.prototype._serializeMappings=function(){for(var e,t,n,r,o=0,a=1,u=0,c=0,l=0,p=0,h="",f=this._mappings.toArray(),d=0,y=f.length;d<y;d++){if(t=f[d],e="",t.generatedLine!==a)for(o=0;t.generatedLine!==a;)e+=";",a++;else if(d>0){if(!s.compareByGeneratedPositionsInflated(t,f[d-1]))continue;e+=","}e+=i.encode(t.generatedColumn-o),o=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=i.encode(r-p),p=r,e+=i.encode(t.originalLine-1-c),c=t.originalLine-1,e+=i.encode(t.originalColumn-u),u=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=i.encode(n-l),l=n)),h+=e}return h},r.prototype._generateSourcesContent=function(e,t){return e.map(function(e){if(!this._sourcesContents)return null;null!=t&&(e=s.relative(t,e));var n=s.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null},this)},r.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},r.prototype.toString=function(){return JSON.stringify(this.toJSON())},n.SourceMapGenerator=r},{"./array-set":17,"./base64-vlq":18,"./mapping-list":21,"./util":26}],25:[function(e,t,n){function r(e,t,n,r,i){this.children=[],this.sourceContents={},this.line=null==e?null:e,this.column=null==t?null:t,this.source=null==n?null:n,this.name=null==i?null:i,this[a]=!0,null!=r&&this.add(r)}var i=e("./source-map-generator").SourceMapGenerator,s=e("./util"),o=/(\r?\n)/,a="$$$isSourceNode$$$";r.fromStringWithSourceMap=function(e,t,n){function i(e,t){if(null===e||void 0===e.source)a.add(t);else{var i=n?s.join(n,e.source):e.source;a.add(new r(e.originalLine,e.originalColumn,i,t,e.name))}}var a=new r,u=e.split(o),c=function(){return u.shift()+(u.shift()||"")},l=1,p=0,h=null;return t.eachMapping(function(e){if(null!==h){if(!(l<e.generatedLine)){var t=(n=u[0]).substr(0,e.generatedColumn-p);return u[0]=n.substr(e.generatedColumn-p),p=e.generatedColumn,i(h,t),void(h=e)}i(h,c()),l++,p=0}for(;l<e.generatedLine;)a.add(c()),l++;if(p<e.generatedColumn){var n=u[0];a.add(n.substr(0,e.generatedColumn)),u[0]=n.substr(e.generatedColumn),p=e.generatedColumn}h=e},this),u.length>0&&(h&&i(h,c()),a.add(u.join(""))),t.sources.forEach(function(e){var r=t.sourceContentFor(e);null!=r&&(null!=n&&(e=s.join(n,e)),a.setSourceContent(e,r))}),a},r.prototype.add=function(e){if(Array.isArray(e))e.forEach(function(e){this.add(e)},this);else{if(!e[a]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);e&&this.children.push(e)}return this},r.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else{if(!e[a]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);this.children.unshift(e)}return this},r.prototype.walk=function(e){for(var t,n=0,r=this.children.length;n<r;n++)(t=this.children[n])[a]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},r.prototype.join=function(e){var t,n,r=this.children.length;if(r>0){for(t=[],n=0;n<r-1;n++)t.push(this.children[n]),t.push(e);t.push(this.children[n]),this.children=t}return this},r.prototype.replaceRight=function(e,t){var n=this.children[this.children.length-1];return n[a]?n.replaceRight(e,t):"string"==typeof n?this.children[this.children.length-1]=n.replace(e,t):this.children.push("".replace(e,t)),this},r.prototype.setSourceContent=function(e,t){this.sourceContents[s.toSetString(e)]=t},r.prototype.walkSourceContents=function(e){for(var t=0,n=this.children.length;t<n;t++)this.children[t][a]&&this.children[t].walkSourceContents(e);for(var r=Object.keys(this.sourceContents),t=0,n=r.length;t<n;t++)e(s.fromSetString(r[t]),this.sourceContents[r[t]])},r.prototype.toString=function(){var e="";return this.walk(function(t){e+=t}),e},r.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},n=new i(e),r=!1,s=null,o=null,a=null,u=null;return this.walk(function(e,i){t.code+=e,null!==i.source&&null!==i.line&&null!==i.column?(s===i.source&&o===i.line&&a===i.column&&u===i.name||n.addMapping({source:i.source,original:{line:i.line,column:i.column},generated:{line:t.line,column:t.column},name:i.name}),s=i.source,o=i.line,a=i.column,u=i.name,r=!0):r&&(n.addMapping({generated:{line:t.line,column:t.column}}),s=null,r=!1);for(var c=0,l=e.length;c<l;c++)10===e.charCodeAt(c)?(t.line++,t.column=0,c+1===l?(s=null,r=!1):r&&n.addMapping({source:i.source,original:{line:i.line,column:i.column},generated:{line:t.line,column:t.column},name:i.name})):t.column++}),this.walkSourceContents(function(e,t){n.setSourceContent(e,t)}),{code:t.code,map:n}},n.SourceNode=r},{"./source-map-generator":24,"./util":26}],26:[function(e,t,n){function r(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')}function i(e){var t=e.match(g);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function s(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function o(e){var t=e,r=i(e);if(r){if(!r.path)return e;t=r.path}for(var o,a=n.isAbsolute(t),u=t.split(/\/+/),c=0,l=u.length-1;l>=0;l--)"."===(o=u[l])?u.splice(l,1):".."===o?c++:c>0&&(""===o?(u.splice(l+1,c),c=0):(u.splice(l,2),c--));return""===(t=u.join("/"))&&(t=a?"/":"."),r?(r.path=t,s(r)):t}function a(e,t){""===e&&(e="."),""===t&&(t=".");var n=i(t),r=i(e);if(r&&(e=r.path||"/"),n&&!n.scheme)return r&&(n.scheme=r.scheme),s(n);if(n||t.match(v))return t;if(r&&!r.host&&!r.path)return r.host=t,s(r);var a="/"===t.charAt(0)?t:o(e.replace(/\/+$/,"")+"/"+t);return r?(r.path=a,s(r)):a}function u(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)}function c(e){return e}function l(e){return h(e)?"$"+e:e}function p(e){return h(e)?e.slice(1):e}function h(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function f(e,t,n){var r=e.source-t.source;return 0!==r?r:0!==(r=e.originalLine-t.originalLine)?r:0!==(r=e.originalColumn-t.originalColumn)||n?r:0!==(r=e.generatedColumn-t.generatedColumn)?r:(r=e.generatedLine-t.generatedLine,0!==r?r:e.name-t.name)}function d(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r?r:0!==(r=e.generatedColumn-t.generatedColumn)||n?r:0!==(r=e.source-t.source)?r:0!==(r=e.originalLine-t.originalLine)?r:(r=e.originalColumn-t.originalColumn,0!==r?r:e.name-t.name)}function y(e,t){return e===t?0:e>t?1:-1}function m(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n?n:0!==(n=e.generatedColumn-t.generatedColumn)?n:0!==(n=y(e.source,t.source))?n:0!==(n=e.originalLine-t.originalLine)?n:(n=e.originalColumn-t.originalColumn,0!==n?n:y(e.name,t.name))}n.getArg=r;var g=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.]*)(?::(\d+))?(\S*)$/,v=/^data:.+\,.+$/;n.urlParse=i,n.urlGenerate=s,n.normalize=o,n.join=a,n.isAbsolute=function(e){return"/"===e.charAt(0)||!!e.match(g)},n.relative=u;var b=function(){return!("__proto__"in Object.create(null))}();n.toSetString=b?c:l,n.fromSetString=b?c:p,n.compareByOriginalPositions=f,n.compareByGeneratedPositionsDeflated=d,n.compareByGeneratedPositionsInflated=m},{}],27:[function(e,t,n){n.SourceMapGenerator=e("./lib/source-map-generator").SourceMapGenerator,n.SourceMapConsumer=e("./lib/source-map-consumer").SourceMapConsumer,n.SourceNode=e("./lib/source-node").SourceNode},{"./lib/source-map-consumer":23,"./lib/source-map-generator":24,"./lib/source-node":25}],28:[function(e,t,n){t.exports={_args:[[{raw:"nodent@^3.0.17",scope:null,escapedName:"nodent",name:"nodent",rawSpec:"^3.0.17",spec:">=3.0.17 <4.0.0",type:"range"},"/Users/<USER>/JSON/ajv"]],_from:"nodent@>=3.0.17 <4.0.0",_id:"nodent@3.0.17",_inCache:!0,_location:"/nodent",_nodeVersion:"6.9.1",_npmOperationalInternal:{host:"packages-12-west.internal.npmjs.com",tmp:"tmp/nodent-3.0.17.tgz_1490780005669_0.5196401283610612"},_npmUser:{name:"matatbread",email:"<EMAIL>"},_npmVersion:"3.10.8",_phantomChildren:{},_requested:{raw:"nodent@^3.0.17",scope:null,escapedName:"nodent",name:"nodent",rawSpec:"^3.0.17",spec:">=3.0.17 <4.0.0",type:"range"},_requiredBy:["#DEV:/"],_resolved:"https://registry.npmjs.org/nodent/-/nodent-3.0.17.tgz",_shasum:"22df57d33c5346d6acc3722d9d69fa68bff518e4",_shrinkwrap:null,_spec:"nodent@^3.0.17",_where:"/Users/<USER>/JSON/ajv",author:{name:"Mat At Bread",email:"<EMAIL>"},bin:{nodentjs:"./nodent.js"},bugs:{url:"https://github.com/MatAtBread/nodent/issues"},dependencies:{acorn:">=2.5.2","acorn-es7-plugin":">=1.1.6","nodent-runtime":">=3.0.4",resolve:"^1.2.0","source-map":"^0.5.6"},description:"NoDent - Asynchronous Javascript language extensions",devDependencies:{},directories:{},dist:{shasum:"22df57d33c5346d6acc3722d9d69fa68bff518e4",tarball:"https://registry.npmjs.org/nodent/-/nodent-3.0.17.tgz"},engines:"node >= 0.10.0",gitHead:"1a48bd0e8d0b4df69aa7b4b3cf8483c03c1cfbd5",homepage:"https://github.com/MatAtBread/nodent#readme",keywords:["Javascript","ES7","async","await","language","extensions","Node","callback","generator","Promise","asynchronous"],license:"BSD-2-Clause",main:"nodent.js",maintainers:[{name:"matatbread",email:"<EMAIL>"}],name:"nodent",optionalDependencies:{},readme:"ERROR: No README data found!",repository:{type:"git",url:"git+https://github.com/MatAtBread/nodent.git"},scripts:{cover:"istanbul cover ./nodent.js tests -- --quick --syntax ; open ./coverage/lcov-report/index.html",start:"./nodent.js",test:"cd tests && npm i --prod && cd .. && node --expose-gc ./nodent.js tests --syntax --quick && node --expose-gc ./nodent.js tests --quick --notStrict","test-loader":"cd tests/loader/app && npm test && cd ../../.."},version:"3.0.17"}},{}],29:[function(e,t,n){(function(e){function t(e,t){for(var n=0,r=e.length-1;r>=0;r--){var i=e[r];"."===i?e.splice(r,1):".."===i?(e.splice(r,1),n++):n&&(e.splice(r,1),n--)}if(t)for(;n--;n)e.unshift("..");return e}function r(e,t){if(e.filter)return e.filter(t);for(var n=[],r=0;r<e.length;r++)t(e[r],r,e)&&n.push(e[r]);return n}var i=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,s=function(e){return i.exec(e).slice(1)};n.resolve=function(){for(var n="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o=s>=0?arguments[s]:e.cwd();if("string"!=typeof o)throw new TypeError("Arguments to path.resolve must be strings");o&&(n=o+"/"+n,i="/"===o.charAt(0))}return n=t(r(n.split("/"),function(e){return!!e}),!i).join("/"),(i?"/":"")+n||"."},n.normalize=function(e){var i=n.isAbsolute(e),s="/"===o(e,-1);return(e=t(r(e.split("/"),function(e){return!!e}),!i).join("/"))||i||(e="."),e&&s&&(e+="/"),(i?"/":"")+e},n.isAbsolute=function(e){return"/"===e.charAt(0)},n.join=function(){var e=Array.prototype.slice.call(arguments,0);return n.normalize(r(e,function(e,t){if("string"!=typeof e)throw new TypeError("Arguments to path.join must be strings");return e}).join("/"))},n.relative=function(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var n=e.length-1;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}e=n.resolve(e).substr(1),t=n.resolve(t).substr(1);for(var i=r(e.split("/")),s=r(t.split("/")),o=Math.min(i.length,s.length),a=o,u=0;u<o;u++)if(i[u]!==s[u]){a=u;break}for(var c=[],u=a;u<i.length;u++)c.push("..");return(c=c.concat(s.slice(a))).join("/")},n.sep="/",n.delimiter=":",n.dirname=function(e){var t=s(e),n=t[0],r=t[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):"."},n.basename=function(e,t){var n=s(e)[2];return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},n.extname=function(e){return s(e)[3]};var o="b"==="ab".substr(-1)?function(e,t,n){return e.substr(t,n)}:function(e,t,n){return t<0&&(t=e.length+t),e.substr(t,n)}}).call(this,e("_process"))},{_process:31}],30:[function(e,t,n){(function(e){"use strict";function n(e){var t=s.exec(e),n=(t[1]||"")+(t[2]||""),r=t[3]||"",i=o.exec(r);return[n,i[1],i[2],i[3]]}function r(e){return u.exec(e).slice(1)}var i="win32"===e.platform,s=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,o=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,a={};a.parse=function(e){if("string"!=typeof e)throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var t=n(e);if(!t||4!==t.length)throw new TypeError("Invalid path '"+e+"'");return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};var u=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,c={};c.parse=function(e){if("string"!=typeof e)throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var t=r(e);if(!t||4!==t.length)throw new TypeError("Invalid path '"+e+"'");return t[1]=t[1]||"",t[2]=t[2]||"",t[3]=t[3]||"",{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}},t.exports=i?a.parse:c.parse,t.exports.posix=c.parse,t.exports.win32=a.parse}).call(this,e("_process"))},{_process:31}],31:[function(e,t,n){function r(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function s(e){if(p===setTimeout)return setTimeout(e,0);if((p===r||!p)&&setTimeout)return p=setTimeout,setTimeout(e,0);try{return p(e,0)}catch(t){try{return p.call(null,e,0)}catch(t){return p.call(this,e,0)}}}function o(e){if(h===clearTimeout)return clearTimeout(e);if((h===i||!h)&&clearTimeout)return h=clearTimeout,clearTimeout(e);try{return h(e)}catch(t){try{return h.call(null,e)}catch(t){return h.call(this,e)}}}function a(){m&&d&&(m=!1,d.length?y=d.concat(y):g=-1,y.length&&u())}function u(){if(!m){var e=s(a);m=!0;for(var t=y.length;t;){for(d=y,y=[];++g<t;)d&&d[g].run();g=-1,t=y.length}d=null,m=!1,o(e)}}function c(e,t){this.fun=e,this.array=t}function l(){}var p,h,f=t.exports={};!function(){try{p="function"==typeof setTimeout?setTimeout:r}catch(e){p=r}try{h="function"==typeof clearTimeout?clearTimeout:i}catch(e){h=i}}();var d,y=[],m=!1,g=-1;f.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];y.push(new c(e,t)),1!==y.length||m||s(u)},c.prototype.run=function(){this.fun.apply(null,this.array)},f.title="browser",f.browser=!0,f.env={},f.argv=[],f.version="",f.versions={},f.on=l,f.addListener=l,f.once=l,f.off=l,f.removeListener=l,f.removeAllListeners=l,f.emit=l,f.prependListener=l,f.prependOnceListener=l,f.listeners=function(e){return[]},f.binding=function(e){throw new Error("process.binding is not supported")},f.cwd=function(){return"/"},f.chdir=function(e){throw new Error("process.chdir is not supported")},f.umask=function(){return 0}},{}],32:[function(e,t,n){var r=e("./lib/core"),i=e("./lib/async");i.core=r,i.isCore=function(e){return r[e]},i.sync=e("./lib/sync"),t.exports=i},{"./lib/async":33,"./lib/core":36,"./lib/sync":38}],33:[function(e,t,n){(function(n){var r=e("./core"),i=e("fs"),s=e("path"),o=e("./caller.js"),a=e("./node-modules-paths.js");t.exports=function(e,t,u){function c(t,n,r){t?d(t):n?d(null,n,r):h(w,function(t,n,r){if(t)d(t);else if(n)d(null,n,r);else{var i=new Error("Cannot find module '"+e+"' from '"+x+"'");i.code="MODULE_NOT_FOUND",d(i)}})}function l(e,t,n){function r(e,t,n){function i(n,i,l){if(c=i,n)return o(n);if(l&&c&&y.pathFilter){var p=s.relative(l,u),h=p.slice(0,p.length-e[0].length),f=y.pathFilter(c,t,h);if(f)return r([""].concat(b.slice()),s.resolve(l,f),c)}g(u,a)}function a(n,i){return n?o(n):i?o(null,u,c):void r(e.slice(1),t,c)}if(0===e.length)return o(null,void 0,n);var u=t+e[0],c=n;c?i(null,c):p(s.dirname(u),i)}var i=t,o=n;"function"==typeof i&&(o=i,i=void 0),r([""].concat(b),e,i)}function p(e,t){if(""===e||"/"===e)return t(null);if("win32"===n.platform&&/^\w:[/\\]*$/.test(e))return t(null);if(/[/\\]node_modules[/\\]*$/.test(e))return t(null);var r=s.join(e,"package.json");g(r,function(n,i){if(!i)return p(s.dirname(e),t);v(r,function(n,i){n&&t(n);try{var s=JSON.parse(i)}catch(e){}s&&y.packageFilter&&(s=y.packageFilter(s,r)),t(null,s,e)})})}function h(e,t,n){var r=n,i=t;"function"==typeof i&&(r=i,i=y.package);var o=s.join(e,"package.json");g(o,function(t,n){return t?r(t):n?void v(o,function(t,n){if(t)return r(t);try{var i=JSON.parse(n)}catch(e){}if(y.packageFilter&&(i=y.packageFilter(i,o)),i.main)return"."!==i.main&&"./"!==i.main||(i.main="index"),void l(s.resolve(e,i.main),i,function(t,n,i){return t?r(t):n?r(null,n,i):i?void h(s.resolve(e,i.main),i,function(t,n,i){return t?r(t):n?r(null,n,i):void l(s.join(e,"index"),i,r)}):l(s.join(e,"index"),i,r)});l(s.join(e,"/index"),i,r)}):l(s.join(e,"index"),i,r)})}function f(t,n){function r(n,r,a){return n?t(n):r?t(null,r,a):void h(s.join(o,e),void 0,i)}function i(e,r,i){return e?t(e):r?t(null,r,i):void f(t,n.slice(1))}if(0===n.length)return t(null,void 0);var o=n[0];l(s.join(o,e),void 0,r)}var d=u,y=t||{};if("function"==typeof y&&(d=y,y={}),"string"!=typeof e){var m=new TypeError("Path must be a string.");return n.nextTick(function(){d(m)})}var g=y.isFile||function(e,t){i.stat(e,function(e,n){return e?"ENOENT"===e.code||"ENOTDIR"===e.code?t(null,!1):t(e):t(null,n.isFile()||n.isFIFO())})},v=y.readFile||i.readFile,b=y.extensions||[".js"],x=y.basedir||s.dirname(o());if(y.paths=y.paths||[],/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var w=s.resolve(x,e);".."!==e&&"/"!==e.slice(-1)||(w+="/"),/\/$/.test(e)&&w===x?h(w,y.package,c):l(w,y.package,c)}else!function(e,t,n){f(n,a(t,y))}(0,x,function(t,n,i){if(t)d(t);else if(n)d(null,n,i);else{if(r[e])return d(null,e);var s=new Error("Cannot find module '"+e+"' from '"+x+"'");s.code="MODULE_NOT_FOUND",d(s)}})}}).call(this,e("_process"))},{"./caller.js":34,"./core":36,"./node-modules-paths.js":37,_process:31,fs:5,path:29}],34:[function(e,t,n){t.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t};var t=(new Error).stack;return Error.prepareStackTrace=e,t[2].getFileName()}},{}],35:[function(e,t,n){t.exports={"*":["assert","buffer_ieee754","buffer","child_process","cluster","console","constants","crypto","_debugger","dgram","dns","domain","events","freelist","fs","http","https","_linklist","module","net","os","path","punycode","querystring","readline","repl","stream","string_decoder","sys","timers","tls","tty","url","util","vm","zlib"],.11:["_http_server"],"1.0":["process","v8"]}},{}],36:[function(e,t,n){(function(n){var r=n.versions&&n.versions.node&&n.versions.node.split(".")||[],i=e("./core.json"),s={};for(var o in i)if(Object.prototype.hasOwnProperty.call(i,o)&&function(e){if("*"===e)return!0;for(var t=e.split("."),n=0;n<3;++n)if((r[n]||0)>=(t[n]||0))return!0;return!1}(o))for(var a=0;a<i[o].length;++a)s[i[o][a]]=!0;t.exports=s}).call(this,e("_process"))},{"./core.json":35,_process:31}],37:[function(e,t,n){var r=e("path"),i=r.parse||e("path-parse");t.exports=function(e,t){var n=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"],s=r.resolve(e),o="/";/^([A-Za-z]:)/.test(s)?o="":/^\\\\/.test(s)&&(o="\\\\");for(var a=[s],u=i(s);u.dir!==a[a.length-1];)a.push(u.dir),u=i(u.dir);var c=a.reduce(function(e,t){return e.concat(n.map(function(e){return r.join(o,t,e)}))},[]);return t&&t.paths?c.concat(t.paths):c}},{path:29,"path-parse":30}],38:[function(e,t,n){var r=e("./core"),i=e("fs"),s=e("path"),o=e("./caller.js"),a=e("./node-modules-paths.js");t.exports=function(e,t){function n(e){if(l(e))return e;for(var t=0;t<h.length;t++){var n=e+h[t];if(l(n))return n}}function u(e){var t=s.join(e,"/package.json");if(l(t)){var r=p(t,"utf8");try{var i=JSON.parse(r);if(c.packageFilter&&(i=c.packageFilter(i,e)),i.main){var o=n(s.resolve(e,i.main));if(o)return o;var a=u(s.resolve(e,i.main));if(a)return a}}catch(e){}}return n(s.join(e,"/index"))}if("string"!=typeof e)throw new TypeError("Path must be a string.");var c=t||{},l=c.isFile||function(e){try{var t=i.statSync(e)}catch(e){if(e&&("ENOENT"===e.code||"ENOTDIR"===e.code))return!1;throw e}return t.isFile()||t.isFIFO()},p=c.readFileSync||i.readFileSync,h=c.extensions||[".js"],f=c.basedir||s.dirname(o());if(c.paths=c.paths||[],/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var d=s.resolve(f,e);".."!==e&&"/"!==e.slice(-1)||(d+="/");var y=n(d)||u(d);if(y)return y}else{var m=function(e,t){for(var r=a(t,c),i=0;i<r.length;i++){var o=r[i],l=n(s.join(o,"/",e));if(l)return l;var p=u(s.join(o,"/",e));if(p)return p}}(e,f);if(m)return m}if(r[e])return e;var g=new Error("Cannot find module '"+e+"' from '"+f+"'");throw g.code="MODULE_NOT_FOUND",g}},{"./caller.js":34,"./core":36,"./node-modules-paths.js":37,fs:5,path:29}],nodent:[function(e,t,n){(function(n,r,i,s,o,a,u,c){"use strict";function l(e){var t={};return e.forEach(function(e){if(e&&"object"==typeof e)for(var n in e)t[n]=e[n]}),t}function p(e){throw e}function h(){}function f(e){return"ExpressionStatement"===e.type&&("StringLiteral"===e.expression.type||"Literal"===e.expression.type&&"string"==typeof e.expression.value)}function d(t,n,r){n||(n=console.warn.bind(console));var i,s,o={};if("string"==typeof t)(i=t.match(j))&&(s=i[1]||"default");else for(var a=0;a<t.body.length&&f(t.body[a].type);a++){var u="'"+t.body[a].value+"'";if(i=u.match(j)){s=i[1]||"default";break}}if(!i){if(!R.noUseDirective)return null;s="default",i=[null,null,"{}"]}if(s)try{r?e("fs").lstatSync(r).isDirectory()||(r=e("path").dirname(r)):r=e("path").resolve(".");var c=e("resolve").sync("package.json",{moduleDirectory:[""],extensions:[""],basedir:r}),p=JSON.parse(T.readFileSync(c)).nodent.directive[s]}catch(e){}try{o=l([I[s],p,i[2]&&JSON.parse(i[2])])}catch(e){n("Invalid literal compiler option: "+(i&&i[0]||"<no options found>"))}return o.promises||o.es7||o.generators||o.engine?((o.promises||o.es7)&&o.generators&&(n("No valid 'use nodent' directive, assumed -es7 mode"),o=I.es7),(o.generators||o.engine)&&(o.promises=!0),o.promises&&(o.es7=!0),o):null}function y(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),"#!"===e.substring(0,2)&&(e="//"+e),e}function m(e){return(e instanceof i?e:new i(e.toString(),"binary")).toString("base64")}function g(e,t){return t=t||e.log,function(n,r,i){var s=y(T.readFileSync(r,"utf8")),o=e.parse(s,r,i);i=i||d(o.ast,t,r),e.asynchronize(o,void 0,i,t),e.prettyPrint(o,i),n._compile(o.code,o.filename)}}function v(e){return e=e||q,function(t,n,r){if(Array.isArray(n)){var i=n;n=function(e,t){return i.indexOf(e)>=0}}else n=n||function(e,t){return!(e.match(/Sync$/)&&e.replace(/Sync$/,"")in t)};r||(r="");var s=Object.create(t);for(var o in s)!function(){var i=o;try{"function"!=typeof t[i]||s[i+r]&&s[i+r].isAsync||!n(i,s)||(s[i+r]=function(){var n=Array.prototype.slice.call(arguments);return new e(function(e,r){var s=function(t,n){if(t)return r(t);switch(arguments.length){case 0:return e();case 2:return e(n);default:return e(Array.prototype.slice.call(arguments,1))}};n.length>t[i].length?n.push(s):n[t[i].length-1]=s;t[i].apply(t,n)})},s[i+r].isAsync=!0)}catch(e){}}();return s.super=t,s}}function b(t,n){var r=t.filename.split("/"),i=r.pop(),s=N(t.ast,n&&n.sourcemap?{map:{startLine:n.mapStartLine||0,file:i+"(original)",sourceMapRoot:r.join("/"),sourceContent:t.origCode}}:null,t.origCode);if(n&&n.sourcemap)try{var o="",a=s.map.toJSON();if(a){var u=e("source-map").SourceMapConsumer;t.sourcemap=a,P[t.filename]={map:a,smc:new u(a)},o="\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,"+m(JSON.stringify(a))+"\n"}t.code=s.code+o}catch(e){t.code=s}else t.code=s;return t}function x(e,t,n,r){"object"==typeof n&&void 0===r&&(r=n);var i={origCode:e.toString(),filename:t};try{return i.ast=F.parse(i.origCode,r&&r.parser),r.babelTree&&F.treeWalker(i.ast,function(e,t,n){"Literal"===e.type?n[0].replace(O.babelLiteralNode(e.value)):"Property"===e.type&&("ClassBody"===n[0].parent.type?e.type="ClassProperty":e.type="ObjectProperty"),t()}),i}catch(e){if(e instanceof SyntaxError){var s=i.origCode.substr(e.pos-e.loc.column);s=s.split("\n")[0],e.message+=" "+t+" (nodent)\n"+s+"\n"+s.replace(/[\S ]/g,"-").substring(0,e.loc.column)+"^",e.stack=""}throw e}}function w(t,n){n=n||{};var r=t+"|"+Object.keys(n).sort().reduce(function(e,t){return e+t+JSON.stringify(n[t])},"");return this.covers[r]||(t.indexOf("/")>=0?this.covers[r]=e(t):this.covers[r]=e(c+"/covers/"+t)),this.covers[r](this,n)}function E(e,t,n,r){"object"==typeof n&&void 0===r&&(r=n),r=r||{};for(var i in R)i in r||(r[i]=R[i]);var s=this.parse(e,t,null,r);return this.asynchronize(s,null,r,this.log||h),this.prettyPrint(s,r),s}function S(t,n,r){var i={},s=this;n||(n=/\.njs$/),r?r.compiler||(r.compiler={}):r={compiler:{}};var o=l([B,r.compiler]);return function(a,u,c){function l(e){u.statusCode=500,u.write(e.toString()),u.end()}if(i[a.url])return u.setHeader("Content-Type",i[a.url].contentType),r.setHeaders&&r.setHeaders(u),u.write(i[a.url].output),void u.end();if(!(a.url.match(n)||r.htmlScriptRegex&&a.url.match(r.htmlScriptRegex)))return c&&c();var p=t+a.url;if(r.extensions&&!T.existsSync(p))for(var h=0;h<r.extensions.length;h++)if(T.existsSync(p+"."+r.extensions[h])){p=p+"."+r.extensions[h];break}T.readFile(p,function(t,n){if(t)return l(t);try{var c,p;r.htmlScriptRegex&&a.url.match(r.htmlScriptRegex)?(c=e("./htmlScriptParser")(s,n.toString(),a.url,r),p="text/html"):(r.runtime?(c="Function.prototype."+o.$asyncbind+" = "+M.toString()+";",o.generators&&(c+="Function.prototype."+o.$asyncspawn+" = "+V.toString()+";"),o.wrapAwait&&!o.promises&&(c+="Object."+o.$makeThenable+" = "+q.resolve.toString()+";"),o.mapStartLine=c.split("\n").length,c+="\n"):c="",c+=s.compile(n.toString(),a.url,null,o).code,p="application/javascript"),u.setHeader("Content-Type",p),r.enableCache&&(i[a.url]={output:c,contentType:p}),r.setHeaders&&r.setHeaders(u),u.write(c),u.end()}catch(e){return l(e)}})}}function k(e){this.covers={},this._ident=k.prototype.version+"_"+Math.random(),this.setOptions(e)}function A(e,t){function n(e){var t=e.getFileName();if(t&&P[t]){var n=P[t].smc.originalPositionFor({line:e.getLineNumber(),column:e.getColumnNumber()});if(n&&n.line){var r=e.toString();return"\n    at "+r.substring(0,r.length-1)+" => …"+n.source+":"+n.line+":"+n.column+(e.getFunctionName()?")":"")}}return"\n    at "+e}return e+t.map(n).join("")}function _(e){var t={};t[R.$asyncbind]={value:M,writable:!0,enumerable:!1,configurable:!0},t[R.$asyncspawn]={value:V,writable:!0,enumerable:!1,configurable:!0};try{Object.defineProperties(Function.prototype,t)}catch(t){e.log("Function prototypes already assigned: ",t.messsage)}R[R.$error]in r||(r[R[R.$error]]=p),e.augmentObject&&Object.defineProperties(Object.prototype,{asyncify:{value:function(e,t,n){return v(e)(this,t,n)},writable:!0,configurable:!0},isThenable:{value:function(){return q.isThenable(this)},writable:!0,configurable:!0}}),Object[R.$makeThenable]=q.resolve}function C(t){function n(e,t){e=e.split("."),t=t.split(".");for(var n=0;n<3;n++){if(e[n]<t[n])return-1;if(e[n]>t[n])return 1}return 0}function r(i,s){if(!s.match(/nodent\/nodent\.js$/)){if(s.match(/node_modules\/nodent\/.*\.js$/))return L(i,s);for(var u=0;u<o.length;u++)if(s.slice(0,o[u].path.length)==o[u].path){if(o[u].jsCompiler){if(o[u].jsCompiler===r)break;return o[u].jsCompiler.apply(this,arguments)}return L(i,s)}var c=d(y(T.readFileSync(s,"utf8")),t.log,s);return c?a(i,s,c):L(i,s)}var l={path:s.replace(/\/node_modules\/nodent\/nodent\.js$/,"")};l.path&&(l.version=JSON.parse(T.readFileSync(s.replace(/nodent\.js$/,"package.json"))).version,L(i,s),n(l.version,k.prototype.version)<0&&(l.originalNodentLoader=i.exports,i.exports=function(){var n=e.extensions[".js"],r=l.originalNodentLoader.apply(this,arguments);return l.jsCompiler=e.extensions[".js"],e.extensions[".js"]=n,_(t),r},Object.keys(l.originalNodentLoader).forEach(function(e){i.exports[e]=l.originalNodentLoader[e]}),o.push(l),o=o.sort(function(e,t){return t.path.length-e.path.length})))}function i(n){if(Array.isArray(n))return n.forEach(i);e.extensions[n]&&Object.keys(t).filter(function(e){return U[e]!=t[e]}).length&&t.log("File extension "+n+" already configured for async/await compilation."),e.extensions[n]=g(U,t.log)}if(t){for(var s in t)if("use"!==s&&!$.hasOwnProperty(s))throw new Error("NoDent: unknown option: "+s+"="+JSON.stringify(t[s]))}else t={};U?U.setOptions(t):(Object.keys($).forEach(function(e){e in t||(t[e]=$[e])}),U=new k(t)),t.dontMapStackTraces||(Error.prepareStackTrace=A),_(t);var o=[];if(!t.dontInstallRequireHook){if(!L){L=e.extensions[".js"];var a=g(U,t.log);e.extensions[".js"]=r}t.extension&&i(t.extension)}return t.use&&(Array.isArray(t.use)?(t.log("Warning: nodent({use:[...]}) is deprecated. Use nodent.require(module,options)\n"+(new Error).stack.split("\n")[2]),t.use.length&&t.use.forEach(function(e){U[e]=U.require(e)})):(t.log("Warning: nodent({use:{...}}) is deprecated. Use nodent.require(module,options)\n"+(new Error).stack.split("\n")[2]),Object.keys(t.use).forEach(function(e){U[e]=U.require(e,t.use[e])}))),U}var L,P={},T=e("fs"),N=e("./lib/output"),F=e("./lib/parser"),O=e("./lib/arboriculture"),$={log:function(e){console.warn("Nodent: "+e)},augmentObject:!1,extension:".njs",dontMapStackTraces:!1,asyncStackTrace:!1,babelTree:!1,dontInstallRequireHook:!1},B={noRuntime:!1,lazyThenables:!1,es6target:!1,noUseDirective:!1,wrapAwait:null,mapStartLine:0,sourcemap:!0,engine:!1,parser:{sourceType:"script"},$return:"$return",$error:"$error",$arguments:"$args",$asyncspawn:"$asyncspawn",$asyncbind:"$asyncbind",generatedSymbolPrefix:"$",$makeThenable:"$makeThenable"},R=Object.create(B,{es7:{value:!0,writable:!0,enumerable:!0}}),I={default:R,es7:Object.create(R),promise:Object.create(R,{promises:{value:!0,writable:!0,enumerable:!0}}),generator:Object.create(R,{generators:{value:!0,writable:!0,enumerable:!0},es7:{value:!1,writable:!0,enumerable:!0}}),engine:Object.create(R,{engine:{value:!0,writable:!0,enumerable:!0},promises:{value:!0,writable:!0,enumerable:!0}})};I.promises=I.promise,I.generators=I.generator;var j=/^\s*['"]use\s+nodent-?([a-zA-Z0-9]*)?(\s*.*)?['"]\s*;/,D=e("nodent-runtime"),M=D.$asyncbind,V=D.$asyncspawn,q=M.Thenable;k.prototype.setOptions=function(e){return this.log=!1===e.log?h:e.log||this.log,this.options=l([this.options,e]),delete this.options.log,this},M.call(M),k.prototype.version=e("./package.json").version,k.prototype.Thenable=q,k.prototype.EagerThenable=M.EagerThenableFactory,k.prototype.isThenable=function(e){return e&&e instanceof Object&&"function"==typeof e.then},k.prototype.asyncify=v,k.prototype.require=w,k.prototype.generateRequestHandler=S,k.prototype.$asyncspawn=V,k.prototype.$asyncbind=M,k.prototype.parse=x,k.prototype.compile=E,k.prototype.asynchronize=O.asynchronize,k.prototype.prettyPrint=b,k.prototype.parseCompilerOptions=d,k.prototype.getDefaultCompileOptions=void 0,Object.defineProperty(k.prototype,"Promise",{get:function(){return initOpts.log("Warning: nodent.Promise is deprecated. Use nodent.Thenable instead"),q},enumerable:!1,configurable:!1});var U;C.setDefaultCompileOptions=function(e,t){return e&&Object.keys(e).forEach(function(t){if(!(t in R))throw new Error("NoDent: unknown compiler option: "+t);R[t]=e[t]}),t&&Object.keys(t).forEach(function(e){if(!(e in t))throw new Error("NoDent: unknown configuration option: "+e);$[e]=t[e]}),C},C.setCompileOptions=function(e,t){return optionSet[e]=optionSet[e]||l([R]),t&&Object.keys(t).forEach(function(n){if(!(n in R))throw new Error("NoDent: unknown compiler option: "+n);optionSet[e][n]=t[n]}),C},C.asyncify=v,C.Thenable=M.Thenable,C.EagerThenable=M.EagerThenableFactory,t.exports=C,e.main===t&&n.argv.length>=3&&function(){function t(e,t){try{var n,s;if(o.fromast){if(e=JSON.parse(e),n={origCode:"",filename:i,ast:e},!(s=d(e,a.log))){var u=o.use?'"use nodent-'+o.use+'";':'"use nodent";';s=d(u,a.log),console.warn("/* "+i+": No 'use nodent*' directive, assumed "+u+" */")}}else(s=d(o.use?'"use nodent-'+o.use+'";':e,a.log))||(s=d('"use nodent";',a.log),o.dest||console.warn("/* "+i+": 'use nodent*' directive missing/ignored, assumed 'use nodent;' */")),n=a.parse(e,i,s);if(o.parseast||o.pretty||a.asynchronize(n,void 0,s,a.log),a.prettyPrint(n,s),o.out||o.pretty||o.dest){if(o.dest&&!t)throw new Error("Can't write unknown file to "+o.dest);var c="";o.runtime&&(c+="Function.prototype.$asyncbind = "+Function.prototype.$asyncbind.toString()+";\n",c+="global.$error = global.$error || "+r.$error.toString()+";\n"),c+=n.code,t&&o.dest?(T.writeFileSync(o.dest+t,c),console.log("Compiled",o.dest+t)):console.log(c)}(o.minast||o.parseast)&&console.log(JSON.stringify(n.ast,function(e,t){return"$"===e[0]||e.match(/^(start|end|loc)$/)?void 0:t},2,null)),o.ast&&console.log(JSON.stringify(n.ast,function(e,t){return"$"===e[0]?void 0:t},0)),o.exec&&new Function(n.code)()}catch(e){console.error(e)}}var i,s=e("path"),o=(n.env.NODENT_OPTS&&JSON.parse(n.env.NODENT_OPTS),function(e){for(var t=[],r=e||2;r<n.argv.length;r++)if("--"===n.argv[r].slice(0,2)){var i=n.argv[r].slice(2).split("=");t[i[0]]=i[1]||!0}else t.push(n.argv[r]);return t}());C.setDefaultCompileOptions({sourcemap:o.sourcemap,wrapAwait:o.wrapAwait,lazyThenables:o.lazyThenables,noRuntime:o.noruntime,es6target:o.es6target,parser:o.noextensions?{noNodentExtensions:!0}:void 0});var a=C({augmentObject:!0});if(!(o.fromast||o.parseast||o.pretty||o.out||o.dest||o.ast||o.minast||o.exec))try{var u=s.resolve(o[0]);return e(u)}catch(e){throw e&&(e.message=o[0]+": "+e.message),e}if(0==o.length||"-"===o[0])return i="(stdin)",function(e){return new q(function(t,n){var r=[];e.on("data",function(e){r.push(e)}),e.on("end",function(){var e=r.map(function(e){return e.toString()}).join("");return t(e)}),e.on("error",n)}.$asyncbind(this))}(n.stdin).then(t,p);for(var c=0;c<o.length;c++)i=s.resolve(o[c]),t(y(T.readFileSync(i,"utf8")),o[c])}()}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},e("buffer").Buffer,arguments[3],arguments[4],arguments[5],arguments[6],"/node_modules/nodent")},{"./htmlScriptParser":11,"./lib/arboriculture":12,"./lib/output":13,"./lib/parser":14,"./package.json":28,_process:31,buffer:6,fs:5,"nodent-runtime":8,path:29,resolve:32,"source-map":27}]},{},[]);