{"version": 3, "file": "tsc.spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/tsc.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AACjC,kCAA+B;AAG/B,QAAQ,CAAC,iBAAiB,EAAE;IAE1B,IAAM,UAAU,GAAG,+LASnB,CAAC;IAED,IAAM,GAAG,GAAG,IAAI,SAAG,CAAC,cAAM,OAAA,UAAU,EAAV,CAAU,EAAE,cAAM,OAAA,CAAC,eAAe,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAC/D,IAAM,MAAM,GAAG,EAAC,IAAI,EAAE,wBAAwB,EAAE,QAAQ,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,EAAC,CAAC;IAElF,EAAE,CAAC,2CAA2C,EAAE;QACxC,IAAA,wFAC+E,EAD9E,kBAAM,EAAE,wBAAS,CAC8D;QAEtF,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;YACxB,MAAM,EAAE,UAAU;YAClB,mBAAmB,EAAE,IAAI;YACzB,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;YAC9B,MAAM,EAAE,gBAAgB;YACxB,cAAc,EAAE,SAAS;YACzB,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM;SAC/B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kEAAkE,EAAE;QAC/D,IAAA,8CAA4E,EAA3E,kBAAM,EAAE,wBAAS,CAA2D;QAEnF,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;YACxB,MAAM,EAAE,UAAU;YAClB,mBAAmB,EAAE,IAAI;YACzB,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;YAC9B,MAAM,EAAE,gBAAgB;YACxB,cAAc,EAAE,SAAS;SAC1B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\nimport {Tsc} from '../src/tsc';\nimport {VinylFile} from '../src/vinyl_file';\n\ndescribe('options parsing', () => {\n\n  const configData = `\n{\n    \"angularCompilerOptions\": {\n        \"googleClosureOutput\": true\n    },\n    \"compilerOptions\": {\n        \"module\": \"commonjs\",\n        \"outDir\": \"built\"\n    }\n}`;\n\n  const tsc = new Tsc(() => configData, () => ['tsconfig.json']);\n  const config = {path: 'basePath/tsconfig.json', contents: new Buffer(configData)};\n\n  it('should combine all options into ngOptions', () => {\n    const {parsed, ngOptions} =\n        tsc.readConfiguration('projectDir', 'basePath', {target: ts.ScriptTarget.ES2015});\n\n    expect(ngOptions).toEqual({\n      genDir: 'basePath',\n      googleClosureOutput: true,\n      module: ts.ModuleKind.CommonJS,\n      outDir: 'basePath/built',\n      configFilePath: undefined,\n      target: ts.ScriptTarget.ES2015\n    });\n  });\n\n  it('should combine all options into ngOptions from vinyl like object', () => {\n    const {parsed, ngOptions} = tsc.readConfiguration(config as VinylFile, 'basePath');\n\n    expect(ngOptions).toEqual({\n      genDir: 'basePath',\n      googleClosureOutput: true,\n      module: ts.ModuleKind.CommonJS,\n      outDir: 'basePath/built',\n      configFilePath: undefined\n    });\n  });\n});\n"]}