[{"__symbolic": "module", "version": 3, "metadata": {"Identifiers": {"__symbolic": "class", "statics": {"ANALYZE_FOR_ENTRY_COMPONENTS": {"name": "ANALYZE_FOR_ENTRY_COMPONENTS", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ANALYZE_FOR_ENTRY_COMPONENTS"}}, "ElementRef": {"name": "ElementRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}}, "NgModuleRef": {"name": "NgModuleRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleRef"}}, "ViewContainerRef": {"name": "ViewContainerRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}}, "ChangeDetectorRef": {"name": "ChangeDetectorRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}}, "QueryList": {"name": "QueryList", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "QueryList"}}, "TemplateRef": {"name": "TemplateRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "TemplateRef"}}, "CodegenComponentFactoryResolver": {"name": "ɵCodegenComponentFactoryResolver", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵCodegenComponentFactoryResolver"}}, "ComponentFactoryResolver": {"name": "ComponentFactoryResolver", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver"}}, "ComponentFactory": {"name": "ComponentFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactory"}}, "ComponentRef": {"name": "ComponentRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentRef"}}, "NgModuleFactory": {"name": "NgModuleFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactory"}}, "createModuleFactory": {"name": "ɵcmf", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵcmf"}}, "moduleDef": {"name": "ɵmod", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵmod"}}, "moduleProviderDef": {"name": "ɵmpd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵmpd"}}, "RegisterModuleFactoryFn": {"name": "ɵregisterModuleFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵregisterModuleFactory"}}, "Injector": {"name": "Injector", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}}, "ViewEncapsulation": {"name": "ViewEncapsulation", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation"}}, "ChangeDetectionStrategy": {"name": "ChangeDetectionStrategy", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy"}}, "SecurityContext": {"name": "SecurityContext", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "SecurityContext"}}, "LOCALE_ID": {"name": "LOCALE_ID", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}}, "TRANSLATIONS_FORMAT": {"name": "TRANSLATIONS_FORMAT", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "TRANSLATIONS_FORMAT"}}, "inlineInterpolate": {"name": "ɵinlineInterpolate", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵinlineInterpolate"}}, "interpolate": {"name": "ɵinterpolate", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵinterpolate"}}, "EMPTY_ARRAY": {"name": "ɵEMPTY_ARRAY", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵEMPTY_ARRAY"}}, "EMPTY_MAP": {"name": "ɵEMPTY_MAP", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵEMPTY_MAP"}}, "Renderer": {"name": "<PERSON><PERSON><PERSON>", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}}, "viewDef": {"name": "ɵvid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵvid"}}, "elementDef": {"name": "ɵeld", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵeld"}}, "anchorDef": {"name": "ɵand", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵand"}}, "textDef": {"name": "ɵted", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵted"}}, "directiveDef": {"name": "ɵdid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵdid"}}, "providerDef": {"name": "ɵprd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵprd"}}, "queryDef": {"name": "ɵqud", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵqud"}}, "pureArrayDef": {"name": "ɵpad", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpad"}}, "pureObjectDef": {"name": "ɵpod", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpod"}}, "purePipeDef": {"name": "ɵppd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵppd"}}, "pipeDef": {"name": "ɵpid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpid"}}, "nodeValue": {"name": "ɵnov", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵnov"}}, "ngContentDef": {"name": "ɵncd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵncd"}}, "unwrapValue": {"name": "ɵunv", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵunv"}}, "createRendererType2": {"name": "ɵcrt", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵcrt"}}, "RendererType2": {"name": "RendererType2", "moduleName": "@angular/core", "runtime": null}, "ViewDefinition": {"name": "ɵViewDefinition", "moduleName": "@angular/core", "runtime": null}, "createComponentFactory": {"name": "ɵccf", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵccf"}}}}, "createTokenForReference": {"__symbolic": "function", "parameters": ["reference"], "value": {"identifier": {"reference": {"__symbolic": "reference", "name": "reference"}}}}, "createTokenForExternalReference": {"__symbolic": "function", "parameters": ["reflector", "reference"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "createTokenForReference"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "reflector"}, "member": "resolveExternalReference"}, "arguments": [{"__symbolic": "reference", "name": "reference"}]}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"Identifiers": {"__symbolic": "class", "statics": {"ANALYZE_FOR_ENTRY_COMPONENTS": {"name": "ANALYZE_FOR_ENTRY_COMPONENTS", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ANALYZE_FOR_ENTRY_COMPONENTS"}}, "ElementRef": {"name": "ElementRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}}, "NgModuleRef": {"name": "NgModuleRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleRef"}}, "ViewContainerRef": {"name": "ViewContainerRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}}, "ChangeDetectorRef": {"name": "ChangeDetectorRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}}, "QueryList": {"name": "QueryList", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "QueryList"}}, "TemplateRef": {"name": "TemplateRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "TemplateRef"}}, "CodegenComponentFactoryResolver": {"name": "ɵCodegenComponentFactoryResolver", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵCodegenComponentFactoryResolver"}}, "ComponentFactoryResolver": {"name": "ComponentFactoryResolver", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver"}}, "ComponentFactory": {"name": "ComponentFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactory"}}, "ComponentRef": {"name": "ComponentRef", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentRef"}}, "NgModuleFactory": {"name": "NgModuleFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModuleFactory"}}, "createModuleFactory": {"name": "ɵcmf", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵcmf"}}, "moduleDef": {"name": "ɵmod", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵmod"}}, "moduleProviderDef": {"name": "ɵmpd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵmpd"}}, "RegisterModuleFactoryFn": {"name": "ɵregisterModuleFactory", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵregisterModuleFactory"}}, "Injector": {"name": "Injector", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}}, "ViewEncapsulation": {"name": "ViewEncapsulation", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation"}}, "ChangeDetectionStrategy": {"name": "ChangeDetectionStrategy", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy"}}, "SecurityContext": {"name": "SecurityContext", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "SecurityContext"}}, "LOCALE_ID": {"name": "LOCALE_ID", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}}, "TRANSLATIONS_FORMAT": {"name": "TRANSLATIONS_FORMAT", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "TRANSLATIONS_FORMAT"}}, "inlineInterpolate": {"name": "ɵinlineInterpolate", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵinlineInterpolate"}}, "interpolate": {"name": "ɵinterpolate", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵinterpolate"}}, "EMPTY_ARRAY": {"name": "ɵEMPTY_ARRAY", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵEMPTY_ARRAY"}}, "EMPTY_MAP": {"name": "ɵEMPTY_MAP", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵEMPTY_MAP"}}, "Renderer": {"name": "<PERSON><PERSON><PERSON>", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}}, "viewDef": {"name": "ɵvid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵvid"}}, "elementDef": {"name": "ɵeld", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵeld"}}, "anchorDef": {"name": "ɵand", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵand"}}, "textDef": {"name": "ɵted", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵted"}}, "directiveDef": {"name": "ɵdid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵdid"}}, "providerDef": {"name": "ɵprd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵprd"}}, "queryDef": {"name": "ɵqud", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵqud"}}, "pureArrayDef": {"name": "ɵpad", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpad"}}, "pureObjectDef": {"name": "ɵpod", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpod"}}, "purePipeDef": {"name": "ɵppd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵppd"}}, "pipeDef": {"name": "ɵpid", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵpid"}}, "nodeValue": {"name": "ɵnov", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵnov"}}, "ngContentDef": {"name": "ɵncd", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵncd"}}, "unwrapValue": {"name": "ɵunv", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵunv"}}, "createRendererType2": {"name": "ɵcrt", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵcrt"}}, "RendererType2": {"name": "RendererType2", "moduleName": "@angular/core", "runtime": null}, "ViewDefinition": {"name": "ɵViewDefinition", "moduleName": "@angular/core", "runtime": null}, "createComponentFactory": {"name": "ɵccf", "moduleName": "@angular/core", "runtime": {"__symbolic": "reference", "module": "@angular/core", "name": "ɵccf"}}}}, "createTokenForReference": {"__symbolic": "function", "parameters": ["reference"], "value": {"identifier": {"reference": {"__symbolic": "reference", "name": "reference"}}}}, "createTokenForExternalReference": {"__symbolic": "function", "parameters": ["reflector", "reference"], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "createTokenForReference"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "reflector"}, "member": "resolveExternalReference"}, "arguments": [{"__symbolic": "reference", "name": "reference"}]}]}}}}]