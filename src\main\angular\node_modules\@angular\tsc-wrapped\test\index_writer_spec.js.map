{"version": 3, "file": "index_writer_spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/index_writer_spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,0CAA+C;AAE/C,oDAA0D;AAE1D,+CAAqE;AAErE,QAAQ,CAAC,cAAc,EAAE;IACvB,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,IAAI,GAAG,IAAI,oCAAqB,CAAC,GAAG,EAAE,6BAAc,CAAC,CAAC;QAC5D,IAAM,OAAO,GAAG,IAAI,yBAAe,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACnE,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC3C,IAAM,MAAM,GAAG,oCAAqB,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjE,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACrD,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,kDAA6C,CAAC,CAAC;QACxE,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wDAAmD,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MetadataBundler} from '../src/bundler';\nimport {MetadataCollector} from '../src/collector';\nimport {privateEntriesToIndex} from '../src/index_writer';\n\nimport {MockStringBundlerHost, SIMPLE_LIBRARY} from './bundler_spec';\n\ndescribe('index_writer', () => {\n  it('should be able to write the index of a simple library', () => {\n    const host = new MockStringBundlerHost('/', SIMPLE_LIBRARY);\n    const bundler = new MetadataBundler('/lib/index', undefined, host);\n    const bundle = bundler.getMetadataBundle();\n    const result = privateEntriesToIndex('./index', bundle.privates);\n    expect(result).toContain(`export * from './index';`);\n    expect(result).toContain(`export {PrivateOne as ɵa} from './src/one';`);\n    expect(result).toContain(`export {PrivateTwo as ɵb} from './src/two/index';`);\n  });\n});"]}