!function(n){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.contra=n()}}(function(){return function n(t,e,r){function o(i,c){if(!e[i]){if(!t[i]){var f="function"==typeof require&&require;if(!c&&f)return f(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var s=e[i]={exports:{}};t[i][0].call(s.exports,function(n){var e=t[i][1][n];return o(e?e:n)},s,s.exports,n,t,e,r)}return e[i].exports}for(var u="function"==typeof require&&require,i=0;i<r.length;i++)o(r[i]);return o}({1:[function(n,t,e){t.exports=1/0},{}],2:[function(n,t,e){t.exports=1},{}],3:[function(n,t,e){"use strict";var r=n("./_map");t.exports=function(n){function t(n,t){return function(n){t(n)}}return r(n,t)}},{"./_map":5}],4:[function(n,t,e){"use strict";var r=n("./a"),o=n("./_map");t.exports=function(n){function t(n,t){return function(e,o){function u(n,t){return!!o[t]}function i(){var t={};return Object.keys(n).forEach(function(e){u(null,e)&&(t[e]=n[e])}),t}return e?void t(e):void t(null,r(o)?n.filter(u):i())}}return o(n,t)}},{"./_map":5,"./a":6}],5:[function(n,t,e){"use strict";var r=n("./a"),o=n("./once"),u=n("./concurrent"),i=n("./CONCURRENTLY"),c=n("./SERIAL");t.exports=function f(n,t,e){function a(e,c,f,a){var s=arguments;2===s.length&&(f=c,c=i),3===s.length&&"number"!=typeof c&&(a=f,f=c,c=i);var p=Object.keys(e),l=r(e)?[]:{};p.forEach(function(n){l[n]=function(t){3===f.length?f(e[n],n,t):f(e[n],t)}}),u(l,n||c,t?t(e,o(a)):a)}return e||(a.series=f(c,t,!0)),a}},{"./CONCURRENTLY":1,"./SERIAL":2,"./a":6,"./concurrent":7,"./once":19}],6:[function(n,t,e){"use strict";t.exports=function(n){return"[object Array]"===Object.prototype.toString.call(n)}},{}],7:[function(n,t,e){"use strict";var r=n("atoa"),o=n("./a"),u=n("./once"),i=n("./queue"),c=n("./errored"),f=n("./debounce"),a=n("./CONCURRENTLY");t.exports=function(n,t,e){function s(t,e){function o(){var n=r(arguments);c(n,p)||(v[t]=n.shift(),e())}f(n[t],[o])}"function"==typeof t&&(e=t,t=a);var p=u(e),l=i(s,t),d=Object.keys(n),v=o(n)?[]:{};l.unshift(d),l.on("drain",function(){p(null,v)})}},{"./CONCURRENTLY":1,"./a":6,"./debounce":10,"./errored":13,"./once":19,"./queue":20,atoa:16}],8:[function(n,t,e){"use strict";t.exports={curry:n("./curry"),concurrent:n("./concurrent"),series:n("./series"),waterfall:n("./waterfall"),each:n("./each"),map:n("./map"),filter:n("./filter"),queue:n("./queue"),emitter:n("./emitter")}},{"./concurrent":7,"./curry":9,"./each":11,"./emitter":12,"./filter":14,"./map":15,"./queue":20,"./series":21,"./waterfall":22}],9:[function(n,t,e){"use strict";var r=n("atoa");t.exports=function(){var n=r(arguments),t=n.shift();return function(){var e=r(arguments);t.apply(t,n.concat(e))}}},{atoa:16}],10:[function(n,t,e){"use strict";var r=n("ticky");t.exports=function(n,t,e){n&&r(function(){n.apply(e||null,t||[])})}},{ticky:17}],11:[function(n,t,e){"use strict";t.exports=n("./_each")()},{"./_each":3}],12:[function(n,t,e){"use strict";var r=n("atoa"),o=n("./debounce");t.exports=function(n,t){var e=t||{},u={};return void 0===n&&(n={}),n.on=function(t,e){return u[t]?u[t].push(e):u[t]=[e],n},n.once=function(t,e){return e._once=!0,n.on(t,e),n},n.off=function(t,e){var r=arguments.length;if(1===r)delete u[t];else if(0===r)u={};else{var o=u[t];if(!o)return n;o.splice(o.indexOf(e),1)}return n},n.emit=function(){var t=r(arguments);return n.emitterSnapshot(t.shift()).apply(this,t)},n.emitterSnapshot=function(t){var i=(u[t]||[]).slice(0);return function(){var u=r(arguments),c=this||n;if("error"===t&&e["throws"]!==!1&&!i.length)throw 1===u.length?u[0]:u;return i.forEach(function(r){e.async?o(r,u,c):r.apply(c,u),r._once&&n.off(t,r)}),n}},n}},{"./debounce":10,atoa:16}],13:[function(n,t,e){"use strict";var r=n("./debounce");t.exports=function(n,t,e){var o=n.shift();return o?(e&&e.discard(),r(t,[o]),!0):void 0}},{"./debounce":10}],14:[function(n,t,e){"use strict";t.exports=n("./_filter")()},{"./_filter":4}],15:[function(n,t,e){"use strict";t.exports=n("./_map")()},{"./_map":5}],16:[function(n,t,e){t.exports=function(n,t){return Array.prototype.slice.call(n,t)}},{}],17:[function(n,t,e){var r,o="function"==typeof setImmediate;r=o?function(n){setImmediate(n)}:function(n){setTimeout(n,0)},t.exports=r},{}],18:[function(n,t,e){"use strict";t.exports=function(){}},{}],19:[function(n,t,e){"use strict";var r=n("./noop");t.exports=function(n){function t(){e||(e=!0,(n||r).apply(null,arguments))}var e;return t.discard=function(){e=!0},t}},{"./noop":18}],20:[function(n,t,e){"use strict";var r=n("atoa"),o=n("./a"),u=n("./once"),i=n("./emitter"),c=n("./debounce");t.exports=function(n,t){function e(n,t,e){var r=o(t)?t:[t];r.forEach(function(t){p[n]({t:t,done:e})}),c(f)}function f(){if(!(s||l>=d)){if(!p.length)return void(0===l&&v.emit("drain"));l++;var t=p.pop();n(t.t,u(a.bind(null,t))),c(f)}}function a(n){l--,c(n.done,r(arguments,1)),c(f)}var s,p=[],l=0,d=t||1,v=i({push:e.bind(null,"push"),unshift:e.bind(null,"unshift"),pause:function(){s=!0},resume:function(){s=!1,c(f)},pending:p});return Object.defineProperty&&!Object.definePropertyPartial&&Object.defineProperty(v,"length",{get:function(){return p.length}}),v}},{"./a":6,"./debounce":10,"./emitter":12,"./once":19,atoa:16}],21:[function(n,t,e){"use strict";var r=n("./concurrent"),o=n("./SERIAL");t.exports=function(n,t){r(n,o,t)}},{"./SERIAL":2,"./concurrent":7}],22:[function(n,t,e){"use strict";var r=n("atoa"),o=n("./once"),u=n("./errored"),i=n("./debounce");t.exports=function(n,t){function e(){var t=r(arguments),f=n.shift();if(f){if(u(t,c))return;t.push(o(e)),i(f,t)}else i(c,arguments)}var c=o(t);e()}},{"./debounce":10,"./errored":13,"./once":19,atoa:16}]},{},[8])(8)});