{"version": 3, "file": "extract_i18n_plugin.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/extract_i18n_plugin.ts"], "names": [], "mappings": ";;AAAA,mCAAmC;AACnC,iCAAiC;AACjC,6BAA6B;AAC7B,yBAAyB;AAEzB,MAAM,EAAC,uBAAuB,EAAE,OAAO,EAAC,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAG5E,uDAAwD;AAYxD;IAqBE,YAAY,OAAiC;QAjBrC,cAAS,GAAQ,IAAI,CAAC;QACtB,iBAAY,GAAQ,IAAI,CAAC;QAMzB,qBAAgB,GAAQ,IAAI,CAAC;QAC7B,4BAAuB,GAAQ,IAAI,CAAC;QAU1C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,aAAa,CAAC,OAAiC;QACrD,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,6FAA6F;QAC7F,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAE9D,uBAAuB;QACvB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,QAAQ,GAAG,aAAa,CAAC;QAC7B,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QACD,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,YAAY,GAAQ,IAAI,CAAC;QAC7B,IAAI,CAAC;YACH,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;QACzE,CAAC;QAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,aAAa,UAAU,GAAG,GAAG,CAAC,CAAC;QACzF,CAAC;QACD,MAAM,QAAQ,GAAG,EAAE,CAAC,0BAA0B,CAC5C,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE5D,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QACnC,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,OAAO,GAAa,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ;kBACtD,CAAC,OAAO,CAAC,OAAiB,CAAC,GAAI,OAAO,CAAC,OAAoB,CAAC;YAEhE,OAAO,CAAC,OAAO,CAAC,CAAC,OAAe;gBAC9B,MAAM,eAAe,GAAG,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;qBACrD,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;gBACxD,OAAO,GAAG,OAAO;qBAEd,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;qBAEnB,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;qBAExC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;qBAE1B,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC;qBAE3B,OAAO,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;gBAEjC,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;gBAC3C,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAE/B,oDAAoD;QACpD,IAAI,MAAM,GAAG,QAAQ,CAAC;QAEtB,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAC1C,EAAE,MAAM,EAAE,EACV,IAAI,CAAC,gBAAgB,EACrB,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC,EACtC,EAAE,QAAQ,EAAE,CACb,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,uFAAuF;QACvF,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa,CAC9B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjE,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,CAAC;QACD,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,8DAA8D;sBACvE,8DAA8D,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC;YACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAChC,CAAC;QACD,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,gEAAgE;sBACzE,8DAA8D,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC;YACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAa;QACjB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,WAAgB,EAAE,EAAO,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;QAEpF,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,WAAgB,EAAE,EAAO;YACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,WAAW,CAAC,kCAAkC,GAAG,IAAI,CAAC;YACtD,EAAE,EAAE,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAgB,EAAE,EAAsC;QACpE,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,kCAAkC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,qDAAqD;gBACvE,mBAAmB,CAAC,CAAC,CAAC;QAC1B,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,iDAAiD;gBACnE,sBAAsB,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,kCAAkC,GAAG,IAAI,CAAC;QAE5D,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAqB,CAAC,WAAW,CAAC,CAAC;QAE9D,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE;aAClC,IAAI,CAAC;YACJ,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC;gBACzC,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,eAAe,EAAE,IAAI,CAAC,gBAAgB;gBACtC,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,IAAI,EAAE,IAAI,CAAC,aAAa;gBACxB,sBAAsB,EAAE,IAAI,CAAC,uBAAuB;gBACpD,UAAU,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBAClC,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,OAAO,EAAE,IAAI,CAAC,QAAQ;gBAEtB,YAAY,EAAE,CAAC,IAAY,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC;aAC/D,CAAC,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,GAAQ;YACzB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnC,EAAE,CAAC,GAAG,CAAC,CAAC;QACV,CAAC,CAAC,CAAC;IAEP,CAAC;CACF;AA1KD,8CA0KC"}