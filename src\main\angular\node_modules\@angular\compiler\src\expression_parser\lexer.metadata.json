[{"__symbolic": "module", "version": 3, "metadata": {"TokenType": {"Character": 0, "Identifier": 1, "Keyword": 2, "String": 3, "Operator": 4, "Number": 5, "Error": 6}, "Lexer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"tokenize": [{"__symbolic": "method"}]}}, "Token": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 39, "character": 41, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}], "isCharacter": [{"__symbolic": "method"}], "isNumber": [{"__symbolic": "method"}], "isString": [{"__symbolic": "method"}], "isOperator": [{"__symbolic": "method"}], "isIdentifier": [{"__symbolic": "method"}], "isKeyword": [{"__symbolic": "method"}], "isKeywordLet": [{"__symbolic": "method"}], "isKeywordAs": [{"__symbolic": "method"}], "isKeywordNull": [{"__symbolic": "method"}], "isKeywordUndefined": [{"__symbolic": "method"}], "isKeywordTrue": [{"__symbolic": "method"}], "isKeywordFalse": [{"__symbolic": "method"}], "isKeywordThis": [{"__symbolic": "method"}], "isError": [{"__symbolic": "method"}], "toNumber": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "EOF": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Token"}, "arguments": [-1, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "TokenType"}, "member": "Character"}, 0, ""]}, "isIdentifier": {"__symbolic": "function"}, "isQuote": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$SQ"}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$DQ"}}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$BT"}}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TokenType": {"Character": 0, "Identifier": 1, "Keyword": 2, "String": 3, "Operator": 4, "Number": 5, "Error": 6}, "Lexer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"tokenize": [{"__symbolic": "method"}]}}, "Token": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "number"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 39, "character": 41, "context": {"typeName": "TokenType"}}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}], "isCharacter": [{"__symbolic": "method"}], "isNumber": [{"__symbolic": "method"}], "isString": [{"__symbolic": "method"}], "isOperator": [{"__symbolic": "method"}], "isIdentifier": [{"__symbolic": "method"}], "isKeyword": [{"__symbolic": "method"}], "isKeywordLet": [{"__symbolic": "method"}], "isKeywordAs": [{"__symbolic": "method"}], "isKeywordNull": [{"__symbolic": "method"}], "isKeywordUndefined": [{"__symbolic": "method"}], "isKeywordTrue": [{"__symbolic": "method"}], "isKeywordFalse": [{"__symbolic": "method"}], "isKeywordThis": [{"__symbolic": "method"}], "isError": [{"__symbolic": "method"}], "toNumber": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}]}}, "EOF": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "Token"}, "arguments": [-1, {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "TokenType"}, "member": "Character"}, 0, ""]}, "isIdentifier": {"__symbolic": "function"}, "isQuote": {"__symbolic": "function", "parameters": ["code"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$SQ"}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$DQ"}}}, "right": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "code"}, "right": {"__symbolic": "reference", "module": "../chars", "name": "$BT"}}}}}}]