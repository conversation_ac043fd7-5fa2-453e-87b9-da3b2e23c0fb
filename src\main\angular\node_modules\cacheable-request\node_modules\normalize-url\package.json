{"_args": [["normalize-url@2.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "normalize-url@2.0.1", "_id": "normalize-url@2.0.1", "_inBundle": false, "_integrity": "sha512-D6MUW4K/VzoJ4rJ01JFKxDrtY1v9wrgzCX5f2qj/lzH1m/lW6MhUZFKerVsnyjOhOsYzI9Kqqak+10l4LvLpMw==", "_location": "/cacheable-request/normalize-url", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "normalize-url@2.0.1", "name": "normalize-url", "escapedName": "normalize-url", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/cacheable-request"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/normalize-url/-/normalize-url-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/normalize-url/issues"}, "dependencies": {"prepend-http": "^2.0.0", "query-string": "^5.0.1", "sort-keys": "^2.0.0"}, "description": "Normalize a URL", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/normalize-url#readme", "keywords": ["normalize", "url", "uri", "address", "string", "normalization", "normalisation", "query", "querystring", "unicode", "simplify", "strip", "trim", "canonical"], "license": "MIT", "name": "normalize-url", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/normalize-url.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1"}