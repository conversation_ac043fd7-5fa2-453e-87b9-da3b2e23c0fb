{"title": "MathML", "description": "Special tags that allow mathematical formulas and notations to be written on web pages.", "spec": "http://www.w3.org/TR/MathML/", "status": "rec", "links": [{"url": "http://en.wikipedia.org/wiki/MathML", "title": "Wikipedia"}, {"url": "http://www.mozilla.org/projects/mathml/demo/", "title": "MathML demos"}, {"url": "http://www.mathjax.org", "title": "Cross-browser support script"}, {"url": "https://developer.mozilla.org/en/MathML/Element", "title": "Mozilla Developer Network (MDN) documentation - MathML Element"}, {"url": "https://developer.mozilla.org/en-US/docs/Mozilla/MathML_Project/MathML_Torture_Test", "title": "MathML torture test"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "y #1", "3": "y #1", "3.5": "y #1", "3.6": "y #1", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "y", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p", "53": "p", "54": "p", "55": "p", "56": "p", "57": "p", "58": "p", "59": "p", "60": "p", "61": "p", "62": "p"}, "safari": {"3.1": "a #2", "3.2": "a #2", "4": "a #2", "5": "a #2", "5.1": "a #2", "6": "a #2", "6.1": "a #2", "7": "a #2", "7.1": "a #2", "8": "a #2", "9": "a #2", "9.1": "a #2", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "a", "10.0-10.1": "a", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "a", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "p"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "p", "4": "p", "4.1": "p", "4.2-4.3": "p", "4.4": "p", "4.4.3-4.4.4": "p", "56": "p"}, "bb": {"7": "p", "10": "y"}, "op_mob": {"10": "p", "11": "p", "11.1": "p", "11.5": "p", "12": "p", "12.1": "p", "37": "p"}, "and_chr": {"59": "p"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "p", "5": "p"}, "and_qq": {"1.2": "p"}, "baidu": {"7.12": "p"}}, "notes": "Opera's support is limited to a CSS profile of MathML. Support was added in Chrome 24, but removed afterwards due to instability.", "notes_by_num": {"1": "Before version 4, Firefox only supports the XHTML notation", "2": "Before version 10, Safari had issues rendering significant portions of the MathML torture test"}, "usage_perc_y": 17.81, "usage_perc_a": 9.79, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "mathml", "chrome_id": "5240822173794304", "firefox_id": "", "webkit_id": "", "shown": true}