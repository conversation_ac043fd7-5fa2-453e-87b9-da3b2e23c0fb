{"compilerOptions": {"target": "es5", "lib": ["es5", "es6", "dom"], "module": "commonjs", "declaration": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "pretty": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitUseStrict": false, "allowSyntheticDefaultImports": true, "suppressExcessPropertyErrors": true, "suppressImplicitAnyIndexErrors": true, "skipDefaultLibCheck": true, "noEmitHelpers": false, "isolatedModules": false, "strictNullChecks": false, "outDir": "dist", "baseUrl": "src", "paths": {"@ngui/datetime-picker": ["./index"]}}, "files": ["src/index.ts"], "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false, "angularCompilerOptions": {"strictMetadataEmit": true, "skipTemplateCodegen": true}}