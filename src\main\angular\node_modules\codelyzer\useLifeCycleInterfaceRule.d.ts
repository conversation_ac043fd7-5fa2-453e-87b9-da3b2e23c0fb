import * as <PERSON><PERSON> from 'tslint';
import * as ts from 'typescript';
export declare class Rule extends Lint.Rules.AbstractRule {
    static FAILURE: string;
    static HOOKS_PREFIX: string;
    static LIFE_CYCLE_HOOKS_NAMES: Array<any>;
    apply(sourceFile: ts.SourceFile): Lint.RuleFailure[];
}
export declare class ClassMetadataWalker extends Lint.RuleWalker {
    visitClassDeclaration(node: ts.ClassDeclaration): void;
    private extractInterfaces(node, syntaxKind);
    private validateMethods(methods, interfaces, className);
    private isMethodValidHook(m, interfaces);
}
