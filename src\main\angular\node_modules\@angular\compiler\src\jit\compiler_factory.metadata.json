[{"__symbolic": "module", "version": 3, "metadata": {"COMPILER_PROVIDERS": {"__symbolic": "error", "message": "Function call not supported", "line": 61, "character": 16}, "JitCompilerFactory": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}]}]], "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "CompilerOptions"}]}]}], "createCompiler": [{"__symbolic": "method"}]}}, "platformCoreDynamic": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "platformCore"}, "coreDynamic", [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "CompilerFactory"}, "useClass": {"__symbolic": "reference", "name": "JitCompilerFactory"}}]]}}}, {"__symbolic": "module", "version": 1, "metadata": {"COMPILER_PROVIDERS": {"__symbolic": "error", "message": "Function call not supported", "line": 61, "character": 16}, "JitCompilerFactory": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}]}]], "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "CompilerOptions"}]}]}], "createCompiler": [{"__symbolic": "method"}]}}, "platformCoreDynamic": {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "createPlatformFactory"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "platformCore"}, "coreDynamic", [{"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "CompilerFactory"}, "useClass": {"__symbolic": "reference", "name": "JitCompilerFactory"}}]]}}}]