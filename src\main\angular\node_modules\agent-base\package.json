{"_args": [["agent-base@2.1.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "agent-base@2.1.1", "_id": "agent-base@2.1.1", "_inBundle": false, "_integrity": "sha1-1t4Q1a9hMtW9aSQn1G/FOFOQlMc=", "_location": "/agent-base", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "agent-base@2.1.1", "name": "agent-base", "escapedName": "agent-base", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/https-proxy-agent"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/agent-base/-/agent-base-2.1.1.tgz", "_spec": "2.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "description": "Turn a function into an `http.Agent` instance", "devDependencies": {"mocha": "2", "ws": "0.8.0"}, "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "keywords": ["http", "agent", "base", "barebones", "https"], "license": "MIT", "main": "agent.js", "name": "agent-base", "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "scripts": {"test": "mocha --reporter spec"}, "version": "2.1.1"}