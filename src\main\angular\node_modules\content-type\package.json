{"_args": [["content-type@1.0.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "content-type@1.0.2", "_id": "content-type@1.0.2", "_inBundle": false, "_integrity": "sha1-t9ETrueo3Se9IRM8TcJSnfFyHu0=", "_location": "/content-type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "content-type@1.0.2", "name": "content-type", "escapedName": "content-type", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/content-type/-/content-type-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "description": "Create and parse HTTP Content-Type header", "devDependencies": {"istanbul": "0.4.3", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-type#readme", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "license": "MIT", "name": "content-type", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-type.git"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.0.2"}