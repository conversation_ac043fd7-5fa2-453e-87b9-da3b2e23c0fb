{"version": 3, "file": "webdriverLogger.js", "sourceRoot": "", "sources": ["../../lib/webdriverLogger.ts"], "names": [], "mappings": ";AAAA,yBAAyB;AACzB,6BAA6B;AAG7B,2DAAkE;AAElE,wDAAwD;AACxD;IACE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtF,CAAC;AAED;;GAEG;AACH;IAIE;QACE,IAAI,CAAC,OAAO,GAAG,iBAAiB,QAAQ,EAAE,MAAM,CAAC;IACnD,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,MAAc;QAC7B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,OAAyB;QAClD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACpB,MAAM,CAAC;QACT,CAAC;QACD,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,OAAe,CAAC;QACpB,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxD,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,KAAK,OAAO,KAAK,MAAM,IAAI,CAAC;QAC3D,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,MAAM,IAAI,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,YAAY,CAAC,OAAyB;QACpC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5B,KAAK,+BAAW,CAAC,UAAU;gBACzB,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAClD,MAAM,CAAC,gBAAgB,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;YAC3D,KAAK,+BAAW,CAAC,aAAa;gBAC5B,IAAI,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,MAAM,CAAC,oBAAoB,SAAS,EAAE,CAAC;YACzC,KAAK,+BAAW,CAAC,EAAE;gBACjB,MAAM,CAAC,iBAAiB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChD,KAAK,+BAAW,CAAC,aAAa;gBAC5B,MAAM,CAAC,qBAAqB,CAAC;YAC/B;gBACE,MAAM,CAAC,mBAAmB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACpD,CAAC;IACH,CAAC;IAED,SAAS;QACP,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QAClE,IAAI,OAAO,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;QAC1E,IAAI,OAAO,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;QAC1E,IAAI,MAAM,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC5C,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACpD,MAAM,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,MAAM,GAAG,CAAC;IACtD,CAAC;CACF;AAlED,0CAkEC"}