{"_args": [["constants-browserify@1.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "constants-browserify@1.0.0", "_id": "constants-browserify@1.0.0", "_inBundle": false, "_integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "_location": "/constants-browserify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "constants-browserify@1.0.0", "name": "constants-browserify", "escapedName": "constants-browserify", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/constants-browserify/-/constants-browserify-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/constants-browserify/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://netflix.com"}], "dependencies": {}, "description": "node's constants module for the browser", "homepage": "https://github.com/juliangruber/constants-browserify", "keywords": ["constants", "node", "browser", "browserify"], "license": "MIT", "main": "constants.json", "name": "constants-browserify", "repository": {"type": "git", "url": "git://github.com/juliangruber/constants-browserify.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}