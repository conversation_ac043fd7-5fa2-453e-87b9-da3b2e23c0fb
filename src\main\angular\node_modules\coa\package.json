{"_args": [["coa@1.0.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "coa@1.0.3", "_id": "coa@1.0.3", "_inBundle": false, "_integrity": "sha1-G1Sl4dz3fJkEVdTe6pjFZEFtyJM=", "_location": "/coa", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "coa@1.0.3", "name": "coa", "escapedName": "coa", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/svgo"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/coa/-/coa-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/veged"}, "bugs": {"url": "https://github.com/veged/coa/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/arikon"}], "dependencies": {"q": "^1.1.2"}, "description": "Command-Option-Argument: Yet another parser for command line options.", "devDependencies": {"chai": "~1.7.2", "coveralls": "^2.11.16", "eslint": "^3.15.0", "eslint-config-pedant": "^0.8.0", "mocha": "~1.21.4", "nyc": "^10.1.2"}, "directories": {"lib": "./lib"}, "engines": {"node": ">= 4.0"}, "files": ["lib/", "index.js", "README.ru.md"], "homepage": "http://github.com/veged/coa", "license": "MIT", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/veged"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/arikon"}], "name": "coa", "repository": {"type": "git", "url": "git://github.com/veged/coa.git"}, "scripts": {"clean": "rm -r .nyc_output coverage", "coverage": "nyc --reporter=text --reporter=html mocha; echo; echo 'Open coverage/index.html file in your browser'", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha"}, "version": "1.0.3"}