[{"__symbolic": "module", "version": 3, "metadata": {"equals": {"__symbolic": "function"}, "isDefined": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 55, "character": 11}, "right": "undefined"}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "value"}, "right": null}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"equals": {"__symbolic": "function"}, "isDefined": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "error", "message": "Expression form not supported", "line": 55, "character": 11}, "right": "undefined"}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "value"}, "right": null}}}}}]