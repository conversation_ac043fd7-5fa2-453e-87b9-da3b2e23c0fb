<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Silos Integration Sample</title>
</head>
<body>
    <h1>Silos Integration Sample</h1>
    
    <!-- Your JSP content here -->
    
    <script>
        /**
         * Silos Integration JavaScript for JSP
         * Converted from Angular TypeScript logic
         */

        // User data service - customize this based on your JSP environment
        const userDataService = {
            getUserData: function() {
                // Example: Get data from JSP session or request attributes
                return {
                    username: '<%= session.getAttribute("username") != null ? session.getAttribute("username") : "default_user" %>',
                    profile: '<%= session.getAttribute("profile") != null ? session.getAttribute("profile") : "DEFAULT" %>',
                    branch: '<%= session.getAttribute("branch") != null ? session.getAttribute("branch") : "default_branch" %>'
                };
            }
        };

        // Message service - customize this based on your error handling needs
        const msgService = {
            showError: function(message, title) {
                // You can replace this with your preferred error display method
                alert(title + ': ' + message);
                // Or use a custom modal, toast notification, etc.
            }
        };

        /**
         * Main function to check user profile and redirect to Silos if needed
         */
        function checkAndRedirectToSilos() {
            const userData = userDataService.getUserData();
            console.log(userData);
            console.log(userData.profile);
            console.log(userDataService.getUserData().profile);
           
            if (userData && userData.profile === "BIOSUG") {
                redirectToSilos();
            }
        }

        /**
         * Redirect to Silos with authentication
         */
        async function redirectToSilos() {
            const userData = userDataService.getUserData();
            console.log(userData.username);

            try {
                const response = await getAuthData(
                    userData.username,
                    "ZA0",
                    userData.branch
                );
                console.log(response);
                if (response && response !== null) {
                    dataToSilos(response);
                }
            } catch (error) {
                console.log(error.message);
                console.log(error.status);
                console.log(error.statusText);
                console.error("Authentication failed:", error);
                msgService.showError("Authentication failed", "Error");
            }
        }

        /**
         * Get authentication data from the server
         */
        async function getAuthData(userId, applicationCode, branchCode) {
            // Updated URL for UJ environment testing
            const url = `https://ubz-uj.collaudo.usinet.it/UBZ-ESA-RS/service/userService/v1/users/${userId}/${applicationCode}/${branchCode}`;

            const headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers,
                    cache: 'no-cache'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Error in getAuthData:', error);
                throw error;
            }
        }

        /**
         * Send data to Silos authentication endpoint
         */
        async function dataToSilos(dataSilos) {
            const url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

            const headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            const postData = JSON.stringify(dataSilos);

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: postData,
                    credentials: 'include', // equivalent to withCredentials: true
                    cache: 'no-cache'
                });

                console.log("Response status:", response.status);

                if (response.status === 200) {
                    // Redirect to the response URL or the original URL
                    window.location.replace(response.url || url);
                } else {
                    console.error("Authentication failed with status:", response.status);
                    msgService.showError("Authentication failed", "Error");
                }
            } catch (error) {
                console.error("Authentication error:", error);
                msgService.showError("Authentication failed", "Error");
            }
        }

        // Auto-execute the check when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkAndRedirectToSilos();
        });

        // You can also call it manually with a button click
        function manualCheck() {
            checkAndRedirectToSilos();
        }
    </script>

    <!-- Optional: Manual trigger button for testing -->
    <button onclick="manualCheck()">Check and Redirect to Silos</button>

</body>
</html>