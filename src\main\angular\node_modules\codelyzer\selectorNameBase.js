"use strict";
var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var Lint = require("tslint");
var selectorValidator_1 = require("./util/selectorValidator");
var sprintf_js_1 = require("sprintf-js");
var compiler = require("@angular/compiler");
var SyntaxKind = require("./util/syntaxKind");
var SelectorRule = (function (_super) {
    __extends(SelectorRule, _super);
    function SelectorRule(ruleName, value, disabledIntervals) {
        var _this;
        var type = value[1];
        var prefix = value[2] || [];
        var name = value[3];
        _this = _super.call(this, ruleName, value, disabledIntervals) || this;
        _this.setMultiPrefix(prefix);
        _this.setPrefixArguments(prefix);
        _this.setPrefixValidator(prefix, name);
        _this.setPrefixFailure();
        _this.setTypeValidator(type);
        _this.setNameValidator(name);
        return _this;
    }
    SelectorRule.prototype.getPrefixFailure = function () {
        return this.FAILURE_PREFIX;
    };
    SelectorRule.prototype.validateType = function (selector) {
        return this.typeValidator(selector);
    };
    SelectorRule.prototype.validateName = function (selector) {
        var _this = this;
        if (this.isMultiSelectors) {
            return selector.some(function (a) { return _this.nameValidator(a); });
        }
        else {
            return this.nameValidator(selector);
        }
    };
    SelectorRule.prototype.validatePrefix = function (selector) {
        var _this = this;
        if (this.isMultiSelectors) {
            return selector.some(function (a) { return _this.prefixValidator(a); });
        }
        else {
            return this.prefixValidator(selector);
        }
    };
    SelectorRule.prototype.apply = function (sourceFile) {
        return this.applyWithWalker(new SelectorValidatorWalker(sourceFile, this));
    };
    SelectorRule.prototype.setNameValidator = function (name) {
        if (name === 'camelCase') {
            this.nameValidator = selectorValidator_1.SelectorValidator.camelCase;
        }
        else if (name === 'kebab-case') {
            this.nameValidator = selectorValidator_1.SelectorValidator.kebabCase;
        }
    };
    SelectorRule.prototype.setMultiPrefix = function (prefix) {
        this.isMultiPrefix = typeof prefix === 'string';
    };
    SelectorRule.prototype.setPrefixArguments = function (prefix) {
        this.prefixArguments = this.isMultiPrefix ? prefix : prefix.join(',');
    };
    SelectorRule.prototype.setPrefixValidator = function (prefix, name) {
        var prefixExpression = this.isMultiPrefix ? prefix : (prefix || []).join('|');
        this.prefixValidator = selectorValidator_1.SelectorValidator.prefix(prefixExpression, name);
    };
    SelectorRule.prototype.setPrefixFailure = function () {
        this.FAILURE_PREFIX = this.isMultiPrefix ? this.getSinglePrefixFailure() : this.getManyPrefixFailure();
    };
    SelectorRule.prototype.setTypeValidator = function (type) {
        if (type === 'element') {
            this.typeValidator = selectorValidator_1.SelectorValidator.element;
            this.isMultiSelectors = false;
            this.cssSelectorProperty = 'element';
        }
        else if (type === 'attribute') {
            this.typeValidator = selectorValidator_1.SelectorValidator.attribute;
            this.isMultiSelectors = true;
            this.cssSelectorProperty = 'attrs';
        }
    };
    return SelectorRule;
}(Lint.Rules.AbstractRule));
exports.SelectorRule = SelectorRule;
var SelectorValidatorWalker = (function (_super) {
    __extends(SelectorValidatorWalker, _super);
    function SelectorValidatorWalker(sourceFile, rule) {
        var _this = _super.call(this, sourceFile, rule.getOptions()) || this;
        _this.rule = rule;
        return _this;
    }
    SelectorValidatorWalker.prototype.visitClassDeclaration = function (node) {
        (node.decorators || [])
            .forEach(this.validateDecorator.bind(this, node.name.text));
        _super.prototype.visitClassDeclaration.call(this, node);
    };
    SelectorValidatorWalker.prototype.validateDecorator = function (className, decorator) {
        var baseExpr = decorator.expression || {};
        var expr = baseExpr.expression || {};
        var name = expr.text;
        var args = baseExpr.arguments || [];
        var arg = args[0];
        if (this.rule.handleType === name) {
            this.validateSelector(className, arg);
        }
    };
    SelectorValidatorWalker.prototype.validateSelector = function (className, arg) {
        var _this = this;
        if (arg.kind === SyntaxKind.current().ObjectLiteralExpression) {
            arg.properties.filter(function (prop) { return _this.validateProperty(prop); })
                .map(function (prop) { return prop.initializer; })
                .forEach(function (i) {
                var selectors = _this.extractMainSelector(i);
                var validateSelectors = function (cb) {
                    return !selectors.every(function (selector) {
                        return !cb(selector[_this.rule.cssSelectorProperty]);
                    });
                };
                if (!validateSelectors(_this.rule.validateType.bind(_this.rule))) {
                    var error = sprintf_js_1.sprintf(_this.rule.getTypeFailure(), className, _this.rule.getOptions().ruleArguments[0]);
                    _this.addFailure(_this.createFailure(i.getStart(), i.getWidth(), error));
                }
                else if (!validateSelectors(_this.rule.validateName.bind(_this.rule))) {
                    var name_1 = _this.rule.getOptions().ruleArguments[2];
                    if (name_1 === 'kebab-case') {
                        name_1 += ' and include dash';
                    }
                    var error = sprintf_js_1.sprintf(_this.rule.getNameFailure(), className, name_1);
                    _this.addFailure(_this.createFailure(i.getStart(), i.getWidth(), error));
                }
                else if (!validateSelectors(_this.rule.validatePrefix.bind(_this.rule))) {
                    var error = sprintf_js_1.sprintf(_this.rule.getPrefixFailure(), className, _this.rule.prefixArguments);
                    _this.addFailure(_this.createFailure(i.getStart(), i.getWidth(), error));
                }
            });
        }
    };
    SelectorValidatorWalker.prototype.validateProperty = function (p) {
        return p.name.text === 'selector' && p.initializer && this.isSupportedKind(p.initializer.kind);
    };
    SelectorValidatorWalker.prototype.isSupportedKind = function (kind) {
        var current = SyntaxKind.current();
        return [current.StringLiteral, current.NoSubstitutionTemplateLiteral].some(function (kindType) { return kindType === kind; });
    };
    SelectorValidatorWalker.prototype.extractMainSelector = function (i) {
        return compiler.CssSelector.parse(i.text);
    };
    return SelectorValidatorWalker;
}(Lint.RuleWalker));
exports.SelectorValidatorWalker = SelectorValidatorWalker;
