{"_args": [["builtin-status-codes@3.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "builtin-status-codes@3.0.0", "_id": "builtin-status-codes@3.0.0", "_inBundle": false, "_integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "_location": "/builtin-status-codes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "builtin-status-codes@3.0.0", "name": "builtin-status-codes", "escapedName": "builtin-status-codes", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/stream-http"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "_spec": "3.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "browser": "browser.js", "bugs": {"url": "https://github.com/bendrucker/builtin-status-codes/issues"}, "description": "The map of HTTP status codes from the builtin http module", "devDependencies": {"standard": "^4.0.0", "tape": "^4.0.0"}, "files": ["index.js", "browser.js", "build.js"], "homepage": "https://github.com/bendrucker/builtin-status-codes#readme", "keywords": ["http", "status", "codes", "builtin", "map"], "license": "MIT", "main": "index.js", "name": "builtin-status-codes", "repository": {"type": "git", "url": "git+https://github.com/bendrucker/builtin-status-codes.git"}, "scripts": {"build": "node build.js", "test": "standard && tape test.js"}, "standard": {"ignore": ["browser.js"]}, "version": "3.0.0"}