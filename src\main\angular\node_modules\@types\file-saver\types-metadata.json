{"authors": "<PERSON> <https://github.com/cyrilschumacher>", "definitionFilename": "index.d.ts", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": 0, "libraryMinorVersion": 0, "libraryName": "FileSaver.js", "typingsPackageName": "file-saver", "projectName": "https://github.com/eligrey/FileSaver.js/", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "Mixed", "globals": ["saveAs"], "declaredModules": ["file-saver"], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "cd4f41b0e88ec7b3ec180a92073a606bef75e54e5bdad528218bd630f419465c"}