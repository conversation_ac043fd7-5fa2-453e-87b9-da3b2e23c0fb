{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../lib/client.ts"], "names": [], "mappings": ";AAAA,MAAY,IAAI,WAAM,MAAM,CAAC,CAAA;AAC7B,MAAY,GAAG,WAAM,KAAK,CAAC,CAAA;AAC3B,gCAAwB,iBAAiB,CAAC,CAAA;AAE1C;IAIE,YAAY,UAAkB;QAC5B,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACH,cAAc,CAAC,OAAgB;QAC7B,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;YACjC,IAAI,OAAO,GACP,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,yBAAS,cAAc,EAAC,CAAC;YAE9F,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,QAAQ;gBAC3C,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,QAAO,CAAC,CAAC,CAAC;gBAC9B,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3C,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;oBACjB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,YAAoB;QAChC,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;YACjC,IAAI,OAAO,GACP,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,yBAAS,aAAa,EAAC,CAAC;YAE7F,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,QAAQ;gBAC3C,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,QAAO,CAAC,CAAC,CAAC;gBAC9B,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3C,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;oBACjB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAC,YAAY,EAAE,YAAY,EAAC,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,aAAa;QACX,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG;YACrB,IAAI,OAAO,GAAG,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,yBAAS,cAAc,EAAC,CAAC;YAExF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ;gBACzB,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;oBACvB,IAAI,IAAI,IAAI,CAAC;gBACf,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;oBACjB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAvEY,gBAAQ,WAuEpB,CAAA"}