{"_args": [["buffer-alloc-unsafe@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "buffer-alloc-unsafe@1.1.0", "_id": "buffer-alloc-unsafe@1.1.0", "_inBundle": false, "_integrity": "sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==", "_location": "/buffer-alloc-unsafe", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "buffer-alloc-unsafe@1.1.0", "name": "buffer-alloc-unsafe", "escapedName": "buffer-alloc-unsafe", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/buffer-alloc"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/LinusU/buffer-alloc-unsafe/issues"}, "description": "A [ponyfill](https://ponyfill.com) for `Buffer.allocUnsafe`.", "devDependencies": {"standard": "^7.1.2"}, "files": ["index.js"], "homepage": "https://github.com/LinusU/buffer-alloc-unsafe#readme", "keywords": ["allocUnsafe", "allocate", "buffer allocUnsafe", "buffer unsafe allocate", "buffer", "ponyfill", "unsafe allocate"], "license": "MIT", "name": "buffer-alloc-unsafe", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-alloc-unsafe.git"}, "scripts": {"test": "standard && node test"}, "version": "1.1.0"}