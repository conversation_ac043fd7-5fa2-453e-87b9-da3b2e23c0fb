{"_args": [["@ngui/datetime-picker@0.16.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "@ngui/datetime-picker@0.16.2", "_id": "@ngui/datetime-picker@0.16.2", "_inBundle": false, "_integrity": "sha1-fZAXAqD8yRUn2j+IPAheXtxNhBw=", "_location": "/@ngui/datetime-picker", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@ngui/datetime-picker@0.16.2", "name": "@ngui/datetime-picker", "escapedName": "@ngui%2fdatetime-picker", "scope": "@ngui", "rawSpec": "0.16.2", "saveSpec": null, "fetchSpec": "0.16.2"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/@ngui/datetime-picker/-/datetime-picker-0.16.2.tgz", "_spec": "0.16.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": "", "bugs": {"url": "https://github.com/ng2-ui/datetime-picker/issues"}, "dependencies": {}, "description": "Angular2 DateTime Picker", "devDependencies": {"@angular/common": "2.2.2", "@angular/compiler": "2.2.2", "@angular/compiler-cli": "^2.2.4", "@angular/core": "2.2.2", "@angular/forms": "2.2.2", "@angular/http": "2.2.2", "@angular/material": "^2.0.0-beta.2", "@angular/platform-browser": "2.2.2", "@angular/platform-browser-dynamic": "2.2.2", "@angular/router": "3.2.2", "@angular/upgrade": "2.2.2", "@ngui/utils": "^0.7.0", "@types/google-maps": "^3.1.27", "@types/hammerjs": "^2.0.34", "@types/node": "^6.0.31", "angular2-template-loader": "^0.5.0", "core-js": "^2.4.1", "hammerjs": "^2.0.8", "http-server": "^0.9.0", "npm-check-updates": "^2.8.9", "npm-run-all": "^3.1.0", "raw-loader": "^0.5.1", "reflect-metadata": "^0.1.3", "rimraf": "^2.5.3", "rxjs": "5.0.0-beta.12", "strip-loader": "^0.1.2", "systemjs": "0.19.27", "ts-loader": "^0.8.2", "typescript": "2.0.10", "webpack": "^1.13.3", "webpack-dev-server": "^1.16.2", "webtest": "^0.3.6", "zone.js": "^0.6.21"}, "homepage": "https://github.com/ng2-ui/datetime-picker#readme", "license": "ISC", "main": "dist/datetime-picker.umd.js", "module": "dist/index.js", "name": "@ngui/datetime-picker", "repository": {"type": "git", "url": "git+https://github.com/ng2-ui/datetime-picker.git"}, "scripts": {"build": "npm-run-all --serial clean build:ngc build:umd build:app", "build:app": "NODE_ENV=prod webpack --config app/webpack.config", "build:ngc": "ngc -p tsconfig.ngc.json", "build:umd": "NODE_ENV=prod webpack", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "tslint 'src/**/*.ts' 'app/**/*.ts'", "start": "webpack-dev-server --quiet --port 9002 --content-base app --config app/webpack.config --open", "test": "npm-run-all --serial test:start test:webtest test:stop", "test:start": "forever start --silent node_modules/.bin/webpack-dev-server --quiet --port 9002 --content-base app --config app/webpack.config", "test:stop": "forever stop node_modules/.bin/webpack-dev-server --quiet --port 9002 --content-base app --config app/webpack.config", "test:webtest": "webtest webtest.txt", "upgrade": "npm-check-updates -a/--upgradeAll && npm i"}, "typings": "dist/index.d.ts", "version": "0.16.2"}