{"title": "Search input type", "description": "Search field form input type. Intended to look like the underlying platform's native search field widget (if there is one). Other than its appearance, it's the same as an `<input type=\"text\">`.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#text-(type=text)-state-and-search-state-(type=search)", "status": "ls", "links": [{"url": "https://css-tricks.com/webkit-html5-search-inputs/", "title": "CSS-Tricks article"}, {"url": "http://www.wufoo.com/html5/types/5-search.html", "title": "Wufoo's The Current State of HTML5 Forms: The search Type"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y #1", "11": "y #1"}, "edge": {"12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "u", "11.1": "u", "11.5": "u", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "y #1"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y #1", "3": "y #1", "4": "y #1", "4.1": "y #1", "4.2-4.3": "y #1", "4.4": "y", "4.4.3-4.4.4": "y", "56": "y"}, "bb": {"7": "y", "10": "y #1"}, "op_mob": {"10": "n", "11": "u", "11.1": "u", "11.5": "u", "12": "y", "12.1": "y #1", "37": "y #1"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y #1"}, "ie_mob": {"10": "y #1", "11": "y #1"}, "and_uc": {"11.4": "y"}, "samsung": {"4": "y", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not use a special search-specific UI for the field, just the same UI as a regular `<input type=\"text\">`."}, "usage_perc_y": 97.27, "usage_perc_a": 0, "ucprefix": false, "parent": "forms", "keywords": "input,type,search", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}