{"title": "Web Bluetooth", "description": "Allows web sites to communicate over GATT with nearby user-selected Bluetooth devices in a secure and privacy-preserving way.", "spec": "http://webbluetoothcg.github.io/web-bluetooth/", "status": "unoff", "links": [{"url": "https://developers.google.com/web/updates/2015/07/interact-with-ble-devices-on-the-web", "title": "Intro"}, {"url": "https://googlechrome.github.io/samples/web-bluetooth/", "title": "<PERSON><PERSON>"}, {"url": "https://github.com/WebBluetoothCG/demos", "title": "Demos"}, {"url": "https://github.com/WebBluetoothCG/web-bluetooth/blob/gh-pages/implementation-status.md", "title": "Implementation Status"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n d #1", "46": "n d #1", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "n d #1", "53": "n d #1 #3", "54": "n d #1 #3", "55": "n d #1 #3", "56": "y #4", "57": "y #4", "58": "y #4", "59": "y #4", "60": "y #4", "61": "y #4", "62": "y #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n d #1 #2", "37": "n d #1 #2", "38": "n d #1 #2", "39": "n d #1 #2", "40": "n d #1 #3", "41": "n d #1 #3", "42": "n d #1 #3", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "y #4"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "y #4"}, "and_ff": {"54": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n d #1 #3"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Available by enabling the \"Web Bluetooth\" experimental flag in `about:flags`. Currently support [varies by OS](https://github.com/WebBluetoothCG/web-bluetooth/blob/gh-pages/implementation-status.md)", "2": "Only in Opera Mobile", "3": "Available in [Origin Trials](https://developers.google.com/web/updates/2015/07/interact-with-ble-devices-on-the-web#available-for-origin-trials) for Chrome OS, Android M, and Mac", "4": "Currently support [varies by OS](https://github.com/WebBluetoothCG/web-bluetooth/blob/gh-pages/implementation-status.md)"}, "usage_perc_y": 52.49, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "bluetooth,ble,iot,physicalweb,webbluetooth", "ie_id": "webbluetooth", "chrome_id": "5264933985976320", "firefox_id": "web-bluetooth", "webkit_id": "feature-web-bluetooth", "shown": true}