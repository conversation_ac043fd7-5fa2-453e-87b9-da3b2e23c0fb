[{"__symbolic": "module", "version": 3, "metadata": {"MockSchemaRegistry": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 13, "character": 33}, {"__symbolic": "error", "message": "Expression form not supported", "line": 14, "character": 30}, {"__symbolic": "error", "message": "Expression form not supported", "line": 15, "character": 31}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}], "hasProperty": [{"__symbolic": "method"}], "hasElement": [{"__symbolic": "method"}], "allKnownElementNames": [{"__symbolic": "method"}], "securityContext": [{"__symbolic": "method"}], "getMappedPropName": [{"__symbolic": "method"}], "getDefaultComponentElementName": [{"__symbolic": "method"}], "validateProperty": [{"__symbolic": "method"}], "validateAttribute": [{"__symbolic": "method"}], "normalizeAnimationStyleProperty": [{"__symbolic": "method"}], "normalizeAnimationStyleValue": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MockSchemaRegistry": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 13, "character": 33}, {"__symbolic": "error", "message": "Expression form not supported", "line": 14, "character": 30}, {"__symbolic": "error", "message": "Expression form not supported", "line": 15, "character": 31}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "string"}]}]}], "hasProperty": [{"__symbolic": "method"}], "hasElement": [{"__symbolic": "method"}], "allKnownElementNames": [{"__symbolic": "method"}], "securityContext": [{"__symbolic": "method"}], "getMappedPropName": [{"__symbolic": "method"}], "getDefaultComponentElementName": [{"__symbolic": "method"}], "validateProperty": [{"__symbolic": "method"}], "validateAttribute": [{"__symbolic": "method"}], "normalizeAnimationStyleProperty": [{"__symbolic": "method"}], "normalizeAnimationStyleValue": [{"__symbolic": "method"}]}}}}]