[{"__symbolic": "module", "version": 3, "metadata": {"ResourceLoaderImpl": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"get": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ResourceLoaderImpl": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"get": [{"__symbolic": "method"}]}}}}]