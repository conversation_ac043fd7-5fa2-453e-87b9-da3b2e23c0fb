{"_args": [["query-string@5.1.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "query-string@5.1.1", "_id": "query-string@5.1.1", "_inBundle": false, "_integrity": "sha512-gjWOsm2SoGlgLEdAGt7a6slVOk9mGiXmPFMqrEhLQ68rhQuBnpfs3+EmlvqKyxnCo9/PPlF+9MtY02S1aFg+Jw==", "_location": "/cacheable-request/query-string", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "query-string@5.1.1", "name": "query-string", "escapedName": "query-string", "rawSpec": "5.1.1", "saveSpec": null, "fetchSpec": "5.1.1"}, "_requiredBy": ["/cacheable-request/normalize-url"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/query-string/-/query-string-5.1.1.tgz", "_spec": "5.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/query-string/issues"}, "dependencies": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "description": "Parse and stringify URL query strings", "devDependencies": {"ava": "^0.17.0", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/query-string#readme", "keywords": ["browser", "querystring", "query", "string", "qs", "param", "parameter", "url", "uri", "parse", "stringify", "encode", "decode"], "license": "MIT", "name": "query-string", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/query-string.git"}, "scripts": {"test": "xo && ava"}, "version": "5.1.1"}