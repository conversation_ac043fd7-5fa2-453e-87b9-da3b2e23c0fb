{"title": "Font unicode-range subsetting", "description": "This @font-face descriptor defines the set of Unicode codepoints that may be supported by the font face for which it is declared. The descriptor value is a comma-delimited list of Unicode range (<urange>) values. The union of these ranges defines the set of codepoints that serves as a hint for user agents when deciding whether or not to download a font resource for a given text run.", "spec": "http://dev.w3.org/csswg/css-fonts/#descdef-unicode-range", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/unicode-range", "title": "Mozilla Developer Network (MDN) documentation - CSS unicode-range"}, {"url": "https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariCSSRef/Articles/StandardCSSProperties.html#//apple_ref/css/property/unicode-range", "title": "Safari CSS Reference: unicode-range"}, {"url": "https://www.webplatform.org/docs/css/properties/unicode-range", "title": "Web Platform Docs: unicode-range"}, {"url": "http://jsbin.com/jeqoguzeye/1/edit?html,output", "title": "Demo"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6510254-unicode-range", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [{"description": "Firefox 37- [ignores the descriptor](https://developer.mozilla.org/en-US/docs/Web/CSS/unicode-range#Browser_compatibility) and the at-rule is then applied to the whole range of code points."}, {"description": "IE ignores the unicode-range if the U is lowercase e.g 'u+0061'."}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a", "10": "a", "11": "a"}, "edge": {"12": "a", "13": "a", "14": "a", "15": "a", "16": "a"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y"}, "safari": {"3.1": "a", "3.2": "a", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "y", "10.1": "y", "11": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "y", "10.3": "y", "11": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "a", "2.2": "a", "2.3": "a", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "56": "y"}, "bb": {"7": "n", "10": "a"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a"}, "and_chr": {"59": "y"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.4": "a"}, "samsung": {"4": "a", "5": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "n"}}, "notes": "Partial support indicates that unnecessary code-ranges are downloaded by the browser - see [browser test matrix](https://docs.google.com/a/chromium.org/spreadsheets/d/18h-1gaosu4-KYxH8JUNL6ZDuOsOKmWfauoai3CS3hPY/edit?pli=1#gid=0).", "notes_by_num": {"1": "Can be enabled in Firefox using the `layout.css.unicode-range.enabled` flag"}, "usage_perc_y": 72.04, "usage_perc_a": 21.92, "ucprefix": false, "parent": "", "keywords": "font face,unicode,unicode-range", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}