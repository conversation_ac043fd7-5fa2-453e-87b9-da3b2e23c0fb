{"_args": [["binary-extensions@1.8.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "binary-extensions@1.8.0", "_id": "binary-extensions@1.8.0", "_inBundle": false, "_integrity": "sha1-SOyNFt9Dd+rl+liEaCSAr02Vx3Q=", "_location": "/binary-extensions", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "binary-extensions@1.8.0", "name": "binary-extensions", "escapedName": "binary-extensions", "rawSpec": "1.8.0", "saveSpec": null, "fetchSpec": "1.8.0"}, "_requiredBy": ["/is-binary-path"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/binary-extensions/-/binary-extensions-1.8.0.tgz", "_spec": "1.8.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "description": "List of binary file extensions", "devDependencies": {"ava": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["binary-extensions.json"], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "license": "MIT", "main": "binary-extensions.json", "name": "binary-extensions", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/binary-extensions.git"}, "scripts": {"test": "ava"}, "version": "1.8.0"}