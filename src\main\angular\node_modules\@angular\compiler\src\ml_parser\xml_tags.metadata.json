[{"__symbolic": "module", "version": 3, "metadata": {"XmlTagDefinition": {"__symbolic": "class", "members": {"requireExtraParent": [{"__symbolic": "method"}], "isClosedByChild": [{"__symbolic": "method"}]}}, "getXmlTagDefinition": {"__symbolic": "function", "parameters": ["tagName"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "XmlTagDefinition"}}}}}, {"__symbolic": "module", "version": 1, "metadata": {"XmlTagDefinition": {"__symbolic": "class", "members": {"requireExtraParent": [{"__symbolic": "method"}], "isClosedByChild": [{"__symbolic": "method"}]}}, "getXmlTagDefinition": {"__symbolic": "function", "parameters": ["tagName"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "XmlTagDefinition"}}}}}]