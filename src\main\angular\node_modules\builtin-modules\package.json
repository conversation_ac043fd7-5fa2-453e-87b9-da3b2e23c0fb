{"_args": [["builtin-modules@1.1.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "builtin-modules@1.1.1", "_id": "builtin-modules@1.1.1", "_inBundle": false, "_integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=", "_location": "/builtin-modules", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "builtin-modules@1.1.1", "name": "builtin-modules", "escapedName": "builtin-modules", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/is-builtin-module"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/builtin-modules/-/builtin-modules-1.1.1.tgz", "_spec": "1.1.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/builtin-modules/issues"}, "description": "List of the Node.js builtin modules", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "static.js", "builtin-modules.json"], "homepage": "https://github.com/sindresorhus/builtin-modules#readme", "keywords": ["builtin", "built-in", "builtins", "node", "modules", "core", "bundled", "list", "array", "names"], "license": "MIT", "name": "builtin-modules", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/builtin-modules.git"}, "scripts": {"make": "node make.js", "test": "xo && ava"}, "version": "1.1.1"}