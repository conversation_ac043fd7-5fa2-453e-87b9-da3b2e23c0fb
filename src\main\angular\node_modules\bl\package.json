{"_args": [["bl@1.2.3", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "bl@1.2.3", "_id": "bl@1.2.3", "_inBundle": false, "_integrity": "sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==", "_location": "/bl", "_phantomChildren": {"core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "1.0.0", "safe-buffer": "5.1.1", "util-deprecate": "1.0.2"}, "_requested": {"type": "version", "registry": true, "raw": "bl@1.2.3", "name": "bl", "escapedName": "bl", "rawSpec": "1.2.3", "saveSpec": null, "fetchSpec": "1.2.3"}, "_requiredBy": ["/tar-stream"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/bl/-/bl-1.2.3.tgz", "_spec": "1.2.3", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}, "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "devDependencies": {"faucet": "0.0.1", "hash_file": "~0.1.1", "tape": "~4.9.0"}, "homepage": "https://github.com/rvagg/bl", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "main": "bl.js", "name": "bl", "repository": {"type": "git", "url": "git+https://github.com/rvagg/bl.git"}, "scripts": {"test": "node test/test.js | faucet"}, "version": "1.2.3"}