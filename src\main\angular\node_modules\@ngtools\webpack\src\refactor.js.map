{"version": 3, "file": "refactor.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/refactor.ts"], "names": [], "mappings": ";;AAAA,sCAAsC;AACtC,6BAA6B;AAC7B,iCAAiC;AACjC,2CAAiE;AAEjE,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAS5C,iBAAiB,QAAgB,EAAE,KAAsB,EAAE,OAAmB;IAC5E,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,QAAQ,CAAC;IAClB,CAAC;IACD,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;IACrD,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC;IACpE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,uBAAuB,CAAC,CAAC;IACzE,CAAC;IACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACvC,CAAC;AAGD;IAWE,YAAY,QAAgB,EAChB,KAAsB,EACd,QAAqB,EAC7B,MAAsB;QADd,aAAQ,GAAR,QAAQ,CAAa;QARjC,aAAQ,GAAG,KAAK,CAAC;QAUvB,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACzF,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACjF,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACzD,CAAC;IAvBD,IAAI,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACzC,IAAI,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAC7C,IAAI,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAuB1D;;OAEG;IACH,cAAc,CAAC,SAAS,GAAG,IAAI;QAC7B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,WAAW,GAAoB,EAAE,CAAC;QACtC,kFAAkF;QAClF,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC;YAC3D,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9F,CAAC;QACD,WAAW,GAAG,WAAW,CAAC,MAAM,CAC9B,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,EACvD,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAE3E,MAAM,CAAC,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,IAAoB,EACpB,IAAmB,EACnB,SAAS,GAAG,KAAK,EACjB,MAAc,QAAQ;QACjC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACb,MAAM,CAAC,EAAE,CAAC;QACZ,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACV,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAED,IAAI,GAAG,GAAc,EAAE,CAAC;QACxB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;YACvB,4DAA4D;YAC5D,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;YAED,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,GAAG,EAAE,CAAC;QACR,CAAC;QAED,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC;qBAC3C,OAAO,CAAC,CAAC,IAAa;oBACrB,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;wBACZ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjB,CAAC;oBACD,GAAG,EAAE,CAAC;gBACR,CAAC,CAAC,CAAC;gBAEL,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACb,KAAK,CAAC;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,CAAC,GAAG,CAAC;IACb,CAAC;IAED,gBAAgB,CAAC,IAAoB,EAAE,IAAmB;QACxD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC5D,CAAC;IAED,WAAW,CAAC,IAAa,EAAE,IAAY;QACrC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IACD,MAAM,CAAC,IAAa,EAAE,IAAY;QAChC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,aAAa,CAAC,IAAa,EAAE,IAAY;QACvC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,YAAY,CAAC,UAAkB,EAAE,UAAkB;QACjD,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACxF,MAAM,YAAY,GAAG,UAAU;aAC5B,MAAM,CAAC,CAAC,IAA0B;YACjC,uDAAuD;YACvD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa;mBACvD,IAAI,CAAC,eAAoC,CAAC,IAAI,IAAI,UAAU,CAAC;QACvE,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,IAA0B;YACjC,yFAAyF;YACzF,MAAM,MAAM,GAAG,IAAI,CAAC,YAA+B,CAAC;YACpD,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;gBACpD,MAAM,CAAC,KAAK,CAAC;YACf,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;QACjE,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,IAA0B;YAC9B,iDAAiD;YACjD,MAAM,CAAE,IAAI,CAAC,YAAgC,CAAC,aAAgC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEL,EAAE,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YACxB,mDAAmD;YACnD,qEAAqE;YACrE,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAqB;gBAC/D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAA2B;oBACpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;gBACzC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACrB,MAAM,CAAC;YACT,CAAC;YACD,wEAAwE;YACxE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5E,KAAK,UAAU,EAAE,CAAC,CAAC;QACvB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,yCAAyC;YACzC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAChD,WAAW,UAAU,WAAW,UAAU,IAAI,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,UAAU,CAAC,IAAa;QACtB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,WAAW,CAAC,GAAG,KAAgB;QAC7B,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,WAAW,CAAC,IAAa,EAAE,WAAmB;QAC5C,IAAI,iBAAiB,GAAY,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;QACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAC/B,IAAI,CAAC,MAAM,EAAE,EACb,WAAW,EACX,iBAAiB,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,WAAW,CAAC,EAAU;QACpB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC;IAC7C,CAAC;IAED,SAAS,CAAC,eAAmC;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,MAAM,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE;YACxC,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,EAAE;gBAClD,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,KAAK;gBACtB,UAAU,EAAE,EAAE;aACf,CAAC;YACF,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACvD,aAAa,CAAC,OAAO,GAAG,CAAE,IAAI,CAAC,SAAS,CAAE,CAAC;YAE3C,MAAM,QAAQ,GAAG,IAAI,8BAAiB,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,GAAG,GAAG,+BAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACvD,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;oBAC/C,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC3D,MAAM,EAAE,IAAI,CAAC,SAAS;oBACtB,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBACH,GAAG,CAAC,cAAc,CAAC,IAAI,8BAAiB,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;kBAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;kBACnC,IAAI,CAAC,SAAS,CAAC;YAChC,SAAS,CAAC,OAAO,GAAG,CAAE,QAAQ,CAAE,CAAC;YACjC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;YACxD,SAAS,CAAC,cAAc,GAAG,CAAE,IAAI,CAAC,WAAW,CAAE,CAAC;YAEhD,MAAM,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC;QACtD,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,SAAS,EAAE,IAAI;aAChB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA9ND,wDA8NC"}