{"version": 3, "file": "extractor.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/extractor.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAGH;;GAEG;AACH,oEAAoE;AACpE,4BAA0B;AAE1B,4CAA8C;AAE9C,2BAA6B;AAG7B,iDAA+F;AAC/F,yEAAmE;AAEnE;IACE,mBACY,OAAmC,EAAU,WAA+B,EAC7E,IAAqB,EAAU,cAA4B,EAC1D,OAAmB;QAFnB,YAAO,GAAP,OAAO,CAA4B;QAAU,gBAAW,GAAX,WAAW,CAAoB;QAC7E,SAAI,GAAJ,IAAI,CAAiB;QAAU,mBAAc,GAAd,cAAc,CAAc;QAC1D,YAAO,GAAP,OAAO,CAAY;IAAG,CAAC;IAEnC,2BAAO,GAAP,UAAQ,UAAkB,EAAE,OAAoB;QAAhD,iBAaC;QAZC,8CAA8C;QAC9C,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAE1C,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,UAAA,MAAM;YAC9B,IAAM,OAAO,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACnD,IAAM,OAAO,GAAG,OAAO,IAAI,cAAY,GAAK,CAAC;YAC7C,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACxD,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iCAAa,GAAb;QAAA,iBAKC;QAJC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,GAAG,CAC3C,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC,QAAQ,CAAC,EAArD,CAAqD,CAAC,CAAC;QAEjE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED,6BAAS,GAAT,UAAU,MAA8B,EAAE,UAAkB;QAA5D,iBAoBC;QAnBC,IAAM,MAAM,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,UAA+B,CAAC;QAEpC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACf,KAAK,KAAK;gBACR,UAAU,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;gBAChC,KAAK,CAAC;YACR,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM;gBACT,UAAU,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnC,KAAK,CAAC;YACR,KAAK,KAAK,CAAC;YACX,KAAK,OAAO,CAAC;YACb;gBACE,UAAU,GAAG,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,KAAK,CACf,UAAU,EACV,UAAC,UAAkB,IAAK,OAAA,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAA7D,CAA6D,CAAC,CAAC;IAC7F,CAAC;IAED,gCAAY,GAAZ,UAAa,UAAkB;QAC7B,IAAM,MAAM,GAAG,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAEnD,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACf,KAAK,KAAK;gBACR,MAAM,CAAC,KAAK,CAAC;YACf,KAAK,KAAK,CAAC;YACX,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,QAAQ;gBACX,MAAM,CAAC,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0BAAuB,UAAU,OAAG,CAAC,CAAC;IACxD,CAAC;IAEM,gBAAM,GAAb,UACI,OAAmC,EAAE,OAAmB,EAAE,cAA+B,EACzF,MAAoB,EAAE,mBAAyC,EAC/D,cAA6B;QAC/B,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACpB,IAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACzE,IAAM,OAAO,GAAG,mBAAmB,IAAI,IAAI,2CAA2B,CAAC,cAAc,CAAC,CAAC;YACvF,cAAc,GAAG,cAAc,GAAG,IAAI,kDAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;gBACrD,IAAI,4BAAY,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAChF,CAAC;QAEM,IAAA,iFAAsB,CAA8D;QAE3F,MAAM,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IACH,gBAAC;AAAD,CAAC,AAlFD,IAkFC;AAlFY,8BAAS", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\n/**\n * Extract i18n messages from source code\n */\n// Must be imported first, because Angular decorators throw on load.\nimport 'reflect-metadata';\n\nimport * as compiler from '@angular/compiler';\nimport * as tsc from '@angular/tsc-wrapped';\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nimport {CompilerHost, CompilerHostContext, ModuleResolutionHostAdapter} from './compiler_host';\nimport {PathMappedCompilerHost} from './path_mapped_compiler_host';\n\nexport class Extractor {\n  constructor(\n      private options: tsc.AngularCompilerOptions, private ngExtractor: compiler.Extractor,\n      public host: ts.CompilerHost, private ngCompilerHost: CompilerHost,\n      private program: ts.Program) {}\n\n  extract(formatName: string, outFile: string|null): Promise<string[]> {\n    // Checks the format and returns the extension\n    const ext = this.getExtension(formatName);\n\n    const promiseBundle = this.extractBundle();\n\n    return promiseBundle.then(bundle => {\n      const content = this.serialize(bundle, formatName);\n      const dstFile = outFile || `messages.${ext}`;\n      const dstPath = path.join(this.options.genDir, dstFile);\n      this.host.writeFile(dstPath, content, false);\n      return [dstPath];\n    });\n  }\n\n  extractBundle(): Promise<compiler.MessageBundle> {\n    const files = this.program.getSourceFiles().map(\n        sf => this.ngCompilerHost.getCanonicalFileName(sf.fileName));\n\n    return this.ngExtractor.extract(files);\n  }\n\n  serialize(bundle: compiler.MessageBundle, formatName: string): string {\n    const format = formatName.toLowerCase();\n    let serializer: compiler.Serializer;\n\n    switch (format) {\n      case 'xmb':\n        serializer = new compiler.Xmb();\n        break;\n      case 'xliff2':\n      case 'xlf2':\n        serializer = new compiler.Xliff2();\n        break;\n      case 'xlf':\n      case 'xliff':\n      default:\n        serializer = new compiler.Xliff();\n    }\n    return bundle.write(\n        serializer,\n        (sourcePath: string) => sourcePath.replace(path.join(this.options.basePath, '/'), ''));\n  }\n\n  getExtension(formatName: string): string {\n    const format = (formatName || 'xlf').toLowerCase();\n\n    switch (format) {\n      case 'xmb':\n        return 'xmb';\n      case 'xlf':\n      case 'xlif':\n      case 'xliff':\n      case 'xlf2':\n      case 'xliff2':\n        return 'xlf';\n    }\n\n    throw new Error(`Unsupported format \"${formatName}\"`);\n  }\n\n  static create(\n      options: tsc.AngularCompilerOptions, program: ts.Program, tsCompilerHost: ts.CompilerHost,\n      locale?: string|null, compilerHostContext?: CompilerHostContext,\n      ngCompilerHost?: CompilerHost): Extractor {\n    if (!ngCompilerHost) {\n      const usePathMapping = !!options.rootDirs && options.rootDirs.length > 0;\n      const context = compilerHostContext || new ModuleResolutionHostAdapter(tsCompilerHost);\n      ngCompilerHost = usePathMapping ? new PathMappedCompilerHost(program, options, context) :\n                                        new CompilerHost(program, options, context);\n    }\n\n    const {extractor: ngExtractor} = compiler.Extractor.create(ngCompilerHost, locale || null);\n\n    return new Extractor(options, ngExtractor, tsCompilerHost, ngCompilerHost, program);\n  }\n}\n"]}