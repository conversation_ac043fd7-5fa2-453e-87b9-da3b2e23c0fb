{"$schema": "http://json-schema.org/draft-04/schema#", "id": "CliConfig", "type": "object", "properties": {"requiredKey": {"type": "number"}, "stringKeyDefault": {"type": "string", "default": "defaultValue"}, "stringKey": {"type": "string"}, "booleanKey": {"type": "boolean"}, "numberKey": {"type": "number"}, "objectKey1": {"type": "object", "properties": {"stringKey": {"type": "string"}, "objectKey": {"type": "object", "properties": {"stringKey": {"type": "string"}}}}}, "objectKey2": {"type": "object", "properties": {"stringKey": {"type": "string", "default": "default objectKey2.stringKey"}}, "additionalProperties": true}, "arrayKey1": {"type": "array", "items": {"type": "string"}}, "arrayKey2": {"type": "array", "items": {"type": "object", "properties": {"stringKey": {"type": "string"}}}}}, "required": ["<PERSON><PERSON><PERSON>"]}