{"_args": [["convert-source-map@1.5.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "convert-source-map@1.5.0", "_id": "convert-source-map@1.5.0", "_inBundle": false, "_integrity": "sha1-ms1whRxtXf3ZPZKC5e35SgP/RrU=", "_location": "/convert-source-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "convert-source-map@1.5.0", "name": "convert-source-map", "escapedName": "convert-source-map", "rawSpec": "1.5.0", "saveSpec": null, "fetchSpec": "1.5.0"}, "_requiredBy": ["/istanbul-instrumenter-loader"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/convert-source-map/-/convert-source-map-1.5.0.tgz", "_spec": "1.5.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "bugs": {"url": "https://github.com/thlorenz/convert-source-map/issues"}, "dependencies": {}, "description": "Converts a source-map from/to  different formats and allows adding/changing properties.", "devDependencies": {"inline-source-map": "~0.6.2", "tap": "~9.0.0"}, "engine": {"node": ">=0.6"}, "homepage": "https://github.com/thlorenz/convert-source-map", "keywords": ["convert", "sourcemap", "source", "map", "browser", "debug"], "license": "MIT", "main": "index.js", "name": "convert-source-map", "repository": {"type": "git", "url": "git://github.com/thlorenz/convert-source-map.git"}, "scripts": {"test": "tap test/*.js --color"}, "version": "1.5.0"}