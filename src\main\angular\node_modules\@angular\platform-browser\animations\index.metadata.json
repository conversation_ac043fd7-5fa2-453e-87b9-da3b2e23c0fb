{"__symbolic": "module", "version": 3, "metadata": {"ɵa": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}]}], "destroy": [{"__symbolic": "method"}], "createElement": [{"__symbolic": "method"}], "createComment": [{"__symbolic": "method"}], "createText": [{"__symbolic": "method"}], "appendChild": [{"__symbolic": "method"}], "insertBefore": [{"__symbolic": "method"}], "removeChild": [{"__symbolic": "method"}], "selectRootElement": [{"__symbolic": "method"}], "parentNode": [{"__symbolic": "method"}], "nextSibling": [{"__symbolic": "method"}], "setAttribute": [{"__symbolic": "method"}], "removeAttribute": [{"__symbolic": "method"}], "addClass": [{"__symbolic": "method"}], "removeClass": [{"__symbolic": "method"}], "setStyle": [{"__symbolic": "method"}], "removeStyle": [{"__symbolic": "method"}], "setProperty": [{"__symbolic": "method"}], "setValue": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}]}}, "BrowserAnimationsModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "BrowserModule"}], "providers": {"__symbolic": "reference", "name": "ɵf"}}]}], "members": {}}, "NoopAnimationsModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "BrowserModule"}], "providers": {"__symbolic": "reference", "name": "ɵg"}}]}], "members": {}}, "ɵBrowserAnimationBuilder": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationBuilder"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "RendererFactory2"}]}], "build": [{"__symbolic": "method"}]}}, "ɵBrowserAnimationFactory": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationFactory"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "ɵAnimationRenderer"}]}], "create": [{"__symbolic": "method"}]}}, "ɵAnimationRenderer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "ɵa"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "ɵAnimationRendererFactory"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Renderer2"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}]}], "setProperty": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}]}}, "ɵAnimationRendererFactory": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "RendererFactory2"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}]}], "createRenderer": [{"__symbolic": "method"}], "begin": [{"__symbolic": "method"}], "_scheduleCountTask": [{"__symbolic": "method"}], "scheduleListenerCallback": [{"__symbolic": "method"}], "end": [{"__symbolic": "method"}], "whenRenderingDone": [{"__symbolic": "method"}]}}, "ɵb": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/animations/browser", "name": "AnimationDriver"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationStyleNormalizer"}]}]}}, "ɵc": {"__symbolic": "function"}, "ɵd": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵWebAnimationsStyleNormalizer"}}}, "ɵe": {"__symbolic": "function", "parameters": ["renderer", "engine", "zone"], "value": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ɵAnimationRendererFactory"}, "arguments": [{"__symbolic": "reference", "name": "renderer"}, {"__symbolic": "reference", "name": "engine"}, {"__symbolic": "reference", "name": "zone"}]}}, "ɵf": [{"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "AnimationDriver"}, "useFactory": {"__symbolic": "reference", "name": "ɵc"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationBuilder"}, "useClass": {"__symbolic": "reference", "name": "ɵBrowserAnimationBuilder"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationStyleNormalizer"}, "useFactory": {"__symbolic": "reference", "name": "ɵd"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, "useClass": {"__symbolic": "reference", "name": "ɵb"}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "RendererFactory2"}, "useFactory": {"__symbolic": "reference", "name": "ɵe"}, "deps": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵDomRendererFactory2"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}]}], "ɵg": [{"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "AnimationDriver"}, "useClass": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵNoopAnimationDriver"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations", "name": "AnimationBuilder"}, "useClass": {"__symbolic": "reference", "name": "ɵBrowserAnimationBuilder"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationStyleNormalizer"}, "useFactory": {"__symbolic": "reference", "name": "ɵd"}}, {"provide": {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, "useClass": {"__symbolic": "reference", "name": "ɵb"}}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "RendererFactory2"}, "useFactory": {"__symbolic": "reference", "name": "ɵe"}, "deps": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵDomRendererFactory2"}, {"__symbolic": "reference", "module": "@angular/animations/browser", "name": "ɵAnimationEngine"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone"}]}]}, "origins": {"ɵa": "./src/animation_renderer", "BrowserAnimationsModule": "./src/module", "NoopAnimationsModule": "./src/module", "ɵBrowserAnimationBuilder": "./src/animation_builder", "ɵBrowserAnimationFactory": "./src/animation_builder", "ɵAnimationRenderer": "./src/animation_renderer", "ɵAnimationRendererFactory": "./src/animation_renderer", "ɵb": "./src/providers", "ɵc": "./src/providers", "ɵd": "./src/providers", "ɵe": "./src/providers", "ɵf": "./src/providers", "ɵg": "./src/providers"}, "importAs": "@angular/platform-browser/animations"}