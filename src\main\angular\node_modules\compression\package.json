{"_args": [["compression@1.6.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "compression@1.6.2", "_id": "compression@1.6.2", "_inBundle": false, "_integrity": "sha1-zOsSHsydCcUtetDDNQ6pPd1AK8M=", "_location": "/compression", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "compression@1.6.2", "name": "compression", "escapedName": "compression", "rawSpec": "1.6.2", "saveSpec": null, "fetchSpec": "1.6.2"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/compression/-/compression-1.6.2.tgz", "_spec": "1.6.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/expressjs/compression/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"accepts": "~1.3.3", "bytes": "2.3.0", "compressible": "~2.0.8", "debug": "~2.2.0", "on-headers": "~1.0.1", "vary": "~1.1.0"}, "description": "Node.js compression middleware", "devDependencies": {"eslint": "2.9.0", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.1.0", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.3", "mocha": "2.4.5", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/compression#readme", "license": "MIT", "name": "compression", "repository": {"type": "git", "url": "git+https://github.com/expressjs/compression.git"}, "scripts": {"lint": "eslint **/*.js", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "version": "1.6.2"}