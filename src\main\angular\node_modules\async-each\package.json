{"_args": [["async-each@1.0.1", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "async-each@1.0.1", "_id": "async-each@1.0.1", "_inBundle": false, "_integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0=", "_location": "/async-each", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "async-each@1.0.1", "name": "async-each", "escapedName": "async-each", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/chokidar"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/async-each/-/async-each-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "url": "http://paulmillr.com/"}, "bugs": {"url": "https://github.com/paulmillr/async-each/issues"}, "dependencies": {}, "description": "No-bullshit, ultra-simple, 35-lines-of-code async parallel forEach / map function for JavaScript.", "homepage": "https://github.com/paulmillr/async-each/", "keywords": ["async", "for<PERSON>ach", "each", "map", "asynchronous", "iteration", "iterate", "loop", "parallel", "concurrent", "array", "flow", "control flow"], "license": "MIT", "main": "index.js", "name": "async-each", "repository": {"type": "git", "url": "git://github.com/paulmillr/async-each.git"}, "version": "1.0.1"}