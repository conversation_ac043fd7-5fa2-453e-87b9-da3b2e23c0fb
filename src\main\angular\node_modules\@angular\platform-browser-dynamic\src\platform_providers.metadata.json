[{"__symbolic": "module", "version": 3, "metadata": {"INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {"providers": [{"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "useClass": {"__symbolic": "reference", "module": "./resource_loader/resource_loader_impl", "name": "ResourceLoaderImpl"}}]}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PLATFORM_ID"}, "useValue": {"__symbolic": "reference", "module": "@angular/common", "name": "ɵPLATFORM_BROWSER_ID"}}]}}, {"__symbolic": "module", "version": 1, "metadata": {"INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS"}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "COMPILER_OPTIONS"}, "useValue": {"providers": [{"provide": {"__symbolic": "reference", "module": "@angular/compiler", "name": "Resource<PERSON><PERSON>der"}, "useClass": {"__symbolic": "reference", "module": "./resource_loader/resource_loader_impl", "name": "ResourceLoaderImpl"}}]}, "multi": true}, {"provide": {"__symbolic": "reference", "module": "@angular/core", "name": "PLATFORM_ID"}, "useValue": {"__symbolic": "reference", "module": "@angular/common", "name": "ɵPLATFORM_BROWSER_ID"}}]}}]