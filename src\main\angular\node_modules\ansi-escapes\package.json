{"_args": [["ansi-escapes@2.0.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "ansi-escapes@2.0.0", "_id": "ansi-escapes@2.0.0", "_inBundle": false, "_integrity": "sha1-W65SvkJIeN2Xg+iRDj/Cki6DyBs=", "_location": "/ansi-escapes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ansi-escapes@2.0.0", "name": "ansi-escapes", "escapedName": "ansi-escapes", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/inquirer"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/ansi-escapes/-/ansi-escapes-2.0.0.tgz", "_spec": "2.0.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "description": "ANSI escape codes for manipulating the terminal", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "license": "MIT", "name": "ansi-escapes", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}