[{"__symbolic": "module", "version": 3, "metadata": {"RoundProgressService": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "resolveColor": [{"__symbolic": "method"}], "getTimestamp": [{"__symbolic": "method"}], "getArc": [{"__symbolic": "method"}], "_polarToCartesian": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"RoundProgressService": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/platform-browser", "name": "DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "resolveColor": [{"__symbolic": "method"}], "getTimestamp": [{"__symbolic": "method"}], "getArc": [{"__symbolic": "method"}], "_polarToCartesian": [{"__symbolic": "method"}]}}}}]