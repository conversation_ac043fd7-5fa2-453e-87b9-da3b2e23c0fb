/**
 * Generated bundle index. Do not edit.
 */
export * from './public_api';
export { BaseAnimationRenderer as ɵa } from './src/animation_renderer';
export { BROWSER_ANIMATIONS_PROVIDERS as ɵf, BROWSER_NOOP_ANIMATIONS_PROVIDERS as ɵg, InjectableAnimationEngine as ɵb, instantiateDefaultStyleNormalizer as ɵd, instantiateRendererFactory as ɵe, instantiateSupportedAnimationDriver as ɵc } from './src/providers';
