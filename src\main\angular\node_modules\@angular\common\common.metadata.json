{"__symbolic": "module", "version": 3, "metadata": {"ɵa": [{"__symbolic": "reference", "name": "Ng<PERSON><PERSON>"}, {"__symbolic": "reference", "name": "NgComponentOutlet"}, {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "name": "NgIf"}, {"__symbolic": "reference", "name": "NgTemplateOutlet"}, {"__symbolic": "reference", "name": "NgStyle"}, {"__symbolic": "reference", "name": "NgSwitch"}, {"__symbolic": "reference", "name": "NgSwitchCase"}, {"__symbolic": "reference", "name": "NgSwitchDefault"}, {"__symbolic": "reference", "name": "Ng<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "name": "NgPluralCase"}], "ɵb": [{"__symbolic": "reference", "name": "AsyncPipe"}, {"__symbolic": "reference", "name": "UpperCasePipe"}, {"__symbolic": "reference", "name": "LowerCasePipe"}, {"__symbolic": "reference", "name": "JsonPipe"}, {"__symbolic": "reference", "name": "SlicePipe"}, {"__symbolic": "reference", "name": "DecimalPipe"}, {"__symbolic": "reference", "name": "Percent<PERSON><PERSON>e"}, {"__symbolic": "reference", "name": "TitleCasePipe"}, {"__symbolic": "reference", "name": "C<PERSON><PERSON>cyPipe"}, {"__symbolic": "reference", "name": "DatePipe"}, {"__symbolic": "reference", "name": "I18nPluralPipe"}, {"__symbolic": "reference", "name": "I18nSelectPipe"}], "PlatformLocation": {"__symbolic": "class", "members": {"getBaseHrefFromDOM": [{"__symbolic": "method"}], "onPopState": [{"__symbolic": "method"}], "onHashChange": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}]}}, "LOCATION_INITIALIZED": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["Location Initialized"]}, "LocationChangeEvent": {"__symbolic": "interface"}, "LocationChangeListener": {"__symbolic": "interface"}, "LocationStrategy": {"__symbolic": "class", "members": {"path": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "onPopState": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}]}}, "APP_BASE_HREF": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken"}, "arguments": ["appBaseHref"]}, "HashLocationStrategy": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "LocationStrategy"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "APP_BASE_HREF"}]}]], "parameters": [{"__symbolic": "reference", "name": "PlatformLocation"}, {"__symbolic": "reference", "name": "string"}]}], "onPopState": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}]}}, "PathLocationStrategy": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "LocationStrategy"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "name": "APP_BASE_HREF"}]}]], "parameters": [{"__symbolic": "reference", "name": "PlatformLocation"}, {"__symbolic": "reference", "name": "string"}]}], "onPopState": [{"__symbolic": "method"}], "getBaseHref": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "path": [{"__symbolic": "method"}], "pushState": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}]}}, "PopStateEvent": {"__symbolic": "interface"}, "Location": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "LocationStrategy"}]}], "path": [{"__symbolic": "method"}], "isCurrentPathEqualTo": [{"__symbolic": "method"}], "normalize": [{"__symbolic": "method"}], "prepareExternalUrl": [{"__symbolic": "method"}], "go": [{"__symbolic": "method"}], "replaceState": [{"__symbolic": "method"}], "forward": [{"__symbolic": "method"}], "back": [{"__symbolic": "method"}], "subscribe": [{"__symbolic": "method"}]}, "statics": {"normalizeQueryParams": {"__symbolic": "function", "parameters": ["params"], "value": {"__symbolic": "if", "condition": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "reference", "name": "params"}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "index", "expression": {"__symbolic": "reference", "name": "params"}, "index": 0}, "right": "?"}}, "thenExpression": {"__symbolic": "binop", "operator": "+", "left": "?", "right": {"__symbolic": "reference", "name": "params"}}, "elseExpression": {"__symbolic": "reference", "name": "params"}}}}}, "NgLocaleLocalization": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "NgLocalization"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "getPluralCategory": [{"__symbolic": "method"}]}}, "NgLocalization": {"__symbolic": "class", "members": {"getPluralCategory": [{"__symbolic": "method"}]}}, "CommonModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule"}, "arguments": [{"declarations": [{"__symbolic": "reference", "name": "ɵa"}, {"__symbolic": "reference", "name": "ɵb"}], "exports": [{"__symbolic": "reference", "name": "ɵa"}, {"__symbolic": "reference", "name": "ɵb"}], "providers": [{"provide": {"__symbolic": "reference", "name": "NgLocalization"}, "useClass": {"__symbolic": "reference", "name": "NgLocaleLocalization"}}]}]}], "members": {}}, "NgClass": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngClass]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "@angular/core", "name": "KeyValueDiffers"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}]}], "klass": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}, "arguments": ["class"]}]}], "ngClass": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngDoCheck": [{"__symbolic": "method"}], "_cleanupClasses": [{"__symbolic": "method"}], "_applyKeyValueChanges": [{"__symbolic": "method"}], "_applyIterableChanges": [{"__symbolic": "method"}], "_applyInitialClasses": [{"__symbolic": "method"}], "_applyClasses": [{"__symbolic": "method"}], "_toggleClass": [{"__symbolic": "method"}]}}, "NgFor": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NgForOf": {"__symbolic": "class", "arity": 1, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngFor][ngForOf]"}]}], "members": {"ngForOf": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngForTrackBy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "NgForOfContext"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>"}]}], "ngForTemplate": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngOnChanges": [{"__symbolic": "method"}], "ngDoCheck": [{"__symbolic": "method"}], "_applyChanges": [{"__symbolic": "method"}], "_perViewChange": [{"__symbolic": "method"}]}}, "NgForOfContext": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 15, "character": 24, "context": {"typeName": "T"}, "module": "./src/directives/ng_for_of"}, {"__symbolic": "reference", "name": "NgIterable", "module": "@angular/core", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 15, "character": 54, "context": {"typeName": "T"}, "module": "./src/directives/ng_for_of"}]}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}]}}, "NgIf": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngIf]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "NgIfContext"}]}]}], "ngIf": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngIfThen": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngIfElse": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "_updateView": [{"__symbolic": "method"}]}}, "NgIfContext": {"__symbolic": "class", "members": {}}, "NgPlural": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngPlural]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "NgLocalization"}]}], "ngPlural": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "addCase": [{"__symbolic": "method"}], "_updateView": [{"__symbolic": "method"}], "_clearViews": [{"__symbolic": "method"}], "_activateView": [{"__symbolic": "method"}]}}, "NgPluralCase": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngPluralCase]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Attribute"}, "arguments": ["ngPluralCase"]}], null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Host"}}]], "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "Object"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "name": "Ng<PERSON><PERSON><PERSON>"}]}]}}, "NgStyle": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngStyle]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "KeyValueDiffers"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON><PERSON>"}]}], "ngStyle": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngDoCheck": [{"__symbolic": "method"}], "_applyChanges": [{"__symbolic": "method"}], "_setStyle": [{"__symbolic": "method"}]}}, "NgSwitch": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngSwitch]"}]}], "members": {"ngSwitch": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "_addCase": [{"__symbolic": "method"}], "_addDefault": [{"__symbolic": "method"}], "_matchCase": [{"__symbolic": "method"}], "_updateDefaultCases": [{"__symbolic": "method"}]}}, "NgSwitchCase": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngSwitchCase]"}]}], "members": {"ngSwitchCase": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Host"}}]], "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "Object"}]}, {"__symbolic": "reference", "name": "NgSwitch"}]}], "ngDoCheck": [{"__symbolic": "method"}]}}, "NgSwitchDefault": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngSwitchDefault]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Host"}}]], "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "Object"}]}, {"__symbolic": "reference", "name": "NgSwitch"}]}]}}, "NgTemplateOutlet": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngTemplateOutlet]"}]}], "members": {"ngTemplateOutletContext": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngTemplateOutlet": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}]}], "ngOutletContext": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngOnChanges": [{"__symbolic": "method"}]}}, "NgComponentOutlet": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive"}, "arguments": [{"selector": "[ngComponentOutlet]"}]}], "members": {"ngComponentOutlet": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngComponentOutletInjector": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngComponentOutletContent": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "ngComponentOutletNgModuleFactory": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input"}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef"}]}], "ngOnChanges": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "AsyncPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "async", "pure": false}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "transform": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "_subscribe": [{"__symbolic": "method"}], "_selectStrategy": [{"__symbolic": "method"}], "_dispose": [{"__symbolic": "method"}], "_updateLatestValue": [{"__symbolic": "method"}]}}, "DatePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "date", "pure": true}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "transform": [{"__symbolic": "method"}]}, "statics": {"_ALIASES": {"medium": "yMMMdjms", "short": "yMdjm", "fullDate": "yMMMMEEEEd", "longDate": "yMMMMd", "mediumDate": "yMMMd", "shortDate": "yMd", "mediumTime": "jms", "shortTime": "jm"}}}, "I18nPluralPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "i18nPlural", "pure": true}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "NgLocalization"}]}], "transform": [{"__symbolic": "method"}]}}, "I18nSelectPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "i18nSelect", "pure": true}]}], "members": {"transform": [{"__symbolic": "method"}]}}, "JsonPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "json", "pure": false}]}], "members": {"transform": [{"__symbolic": "method"}]}}, "LowerCasePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "lowercase"}]}], "members": {"transform": [{"__symbolic": "method"}]}}, "CurrencyPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "currency"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "transform": [{"__symbolic": "method"}]}}, "DecimalPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "number"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "transform": [{"__symbolic": "method"}]}}, "PercentPipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "percent"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject"}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "LOCALE_ID"}]}]], "parameters": [{"__symbolic": "reference", "name": "string"}]}], "transform": [{"__symbolic": "method"}]}}, "SlicePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "slice", "pure": false}]}], "members": {"transform": [{"__symbolic": "method"}], "supports": [{"__symbolic": "method"}]}}, "UpperCasePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "uppercase"}]}], "members": {"transform": [{"__symbolic": "method"}]}}, "TitleCasePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "titlecase"}]}], "members": {"transform": [{"__symbolic": "method"}]}}, "ɵPLATFORM_BROWSER_ID": "browser", "ɵPLATFORM_SERVER_ID": "server", "ɵPLATFORM_WORKER_APP_ID": "browserWorkerApp", "ɵPLATFORM_WORKER_UI_ID": "browserWorkerUi", "isPlatformBrowser": {"__symbolic": "function", "parameters": ["platformId"], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "platformId"}, "right": "browser"}}, "isPlatformServer": {"__symbolic": "function", "parameters": ["platformId"], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "platformId"}, "right": "server"}}, "isPlatformWorkerApp": {"__symbolic": "function", "parameters": ["platformId"], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "platformId"}, "right": "browserWorkerApp"}}, "isPlatformWorkerUi": {"__symbolic": "function", "parameters": ["platformId"], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "reference", "name": "platformId"}, "right": "browserWorkerUi"}}, "VERSION": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Version"}, "arguments": ["4.2.5"]}}, "origins": {"ɵa": "./src/directives/index", "ɵb": "./src/pipes/index", "PlatformLocation": "./src/location/platform_location", "LOCATION_INITIALIZED": "./src/location/platform_location", "LocationChangeEvent": "./src/location/platform_location", "LocationChangeListener": "./src/location/platform_location", "LocationStrategy": "./src/location/location_strategy", "APP_BASE_HREF": "./src/location/location_strategy", "HashLocationStrategy": "./src/location/hash_location_strategy", "PathLocationStrategy": "./src/location/path_location_strategy", "PopStateEvent": "./src/location/location", "Location": "./src/location/location", "NgLocaleLocalization": "./src/localization", "NgLocalization": "./src/localization", "CommonModule": "./src/common_module", "NgClass": "./src/directives/ng_class", "NgFor": "./src/directives/ng_for_of", "NgForOf": "./src/directives/ng_for_of", "NgForOfContext": "./src/directives/ng_for_of", "NgIf": "./src/directives/ng_if", "NgIfContext": "./src/directives/ng_if", "NgPlural": "./src/directives/ng_plural", "NgPluralCase": "./src/directives/ng_plural", "NgStyle": "./src/directives/ng_style", "NgSwitch": "./src/directives/ng_switch", "NgSwitchCase": "./src/directives/ng_switch", "NgSwitchDefault": "./src/directives/ng_switch", "NgTemplateOutlet": "./src/directives/ng_template_outlet", "NgComponentOutlet": "./src/directives/ng_component_outlet", "AsyncPipe": "./src/pipes/async_pipe", "DatePipe": "./src/pipes/date_pipe", "I18nPluralPipe": "./src/pipes/i18n_plural_pipe", "I18nSelectPipe": "./src/pipes/i18n_select_pipe", "JsonPipe": "./src/pipes/json_pipe", "LowerCasePipe": "./src/pipes/case_conversion_pipes", "CurrencyPipe": "./src/pipes/number_pipe", "DecimalPipe": "./src/pipes/number_pipe", "PercentPipe": "./src/pipes/number_pipe", "SlicePipe": "./src/pipes/slice_pipe", "UpperCasePipe": "./src/pipes/case_conversion_pipes", "TitleCasePipe": "./src/pipes/case_conversion_pipes", "ɵPLATFORM_BROWSER_ID": "./src/platform_id", "ɵPLATFORM_SERVER_ID": "./src/platform_id", "ɵPLATFORM_WORKER_APP_ID": "./src/platform_id", "ɵPLATFORM_WORKER_UI_ID": "./src/platform_id", "isPlatformBrowser": "./src/platform_id", "isPlatformServer": "./src/platform_id", "isPlatformWorkerApp": "./src/platform_id", "isPlatformWorkerUi": "./src/platform_id", "VERSION": "./src/version"}, "importAs": "@angular/common"}