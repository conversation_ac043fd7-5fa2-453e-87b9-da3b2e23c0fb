{"version": 3, "file": "angular_wait_barrier.js", "sourceRoot": "", "sources": ["../../lib/angular_wait_barrier.ts"], "names": [], "mappings": ";AAEA,mCAA8B,oBAAoB,CAAC,CAAA;AAGnD,MAAM,YAAY,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEzD;;GAEG;AACH;IAME,YAAoB,MAA6B;QAA7B,WAAM,GAAN,MAAM,CAAuB;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,eAAe,CAAC,QAAgB;QAC9B,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAEO,kBAAkB;QACxB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,UAAU,GAAG,YAAY,CAAC,UAAU,GAAG,2BAA2B;YAC1E,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,MAAc;QAC1B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,IAAI,kCAAe,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAuB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEO,sBAAsB,CAAC,OAAyB;QACtD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK;YACvF,4DAA4D;YAC5D,kBAAkB;YAClB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,OAAyB;QAC/C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,sEAAsE;QACtE,0EAA0E;QAC1E,oDAAoD;QACpD,EAAE;QACF,mEAAmE;QACnE,6BAA6B;QAC7B,4DAA4D;QAC5D,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QAED,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM;YAC1F,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK;SAC3E,CAAC;QAEF,EAAE,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QACD,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;IAED,SAAS,CAAC,OAAyB;QACjC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;gBAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC;AAjHY,0BAAkB,qBAiH9B,CAAA"}