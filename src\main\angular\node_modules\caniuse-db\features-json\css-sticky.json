{"title": "CSS position:sticky", "description": "Keeps elements positioned as \"fixed\" or \"relative\" depending on how it appears in the viewport. As a result the element is \"stuck\" when necessary while scrolling.", "spec": "https://drafts.csswg.org/css-position/#sticky-pos", "status": "wd", "links": [{"url": "http://updates.html5rocks.com/2012/08/Stick-your-landings-position-sticky-lands-in-WebKit", "title": "HTML5Rocks"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/position", "title": "Mozilla Developer Network (MDN) documentation - CSS position"}, {"url": "https://www.webplatform.org/docs/css/properties/position", "title": "WebPlatform Docs"}, {"url": "https://github.com/filamentgroup/fixed-sticky", "title": "Polyfill"}, {"url": "https://github.com/wilddeer/stickyfill", "title": "Another polyfill"}, {"url": "http://gedd.ski/post/position-sticky/", "title": "geddski article: <PERSON> and Gotchas"}], "bugs": [{"description": "Chrome, Firefox and Safari 7 & below do not appear to support [sticky table headers](http://jsfiddle.net/Mf4YT/2/). (see also [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=975644))"}, {"description": "A parent with overflow set to `auto` will prevent `position: sticky` from working in Safari"}], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n d #1", "27": "n d #1", "28": "n d #1", "29": "n d #1", "30": "n d #1", "31": "n d #1", "32": "a #3", "33": "a #3", "34": "a #3", "35": "a #3", "36": "a #3", "37": "a #3", "38": "a #3", "39": "a #3", "40": "a #3", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #3", "46": "a #3", "47": "a #3", "48": "a #3", "49": "a #3", "50": "a #3", "51": "a #3", "52": "a #3", "53": "a #3", "54": "a #3", "55": "a #3", "56": "a #3", "57": "a #3"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n d #2", "24": "n d #2", "25": "n d #2", "26": "n d #2", "27": "n d #2", "28": "n d #2", "29": "n d #2", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "n d #2", "35": "n d #2", "36": "n d #2", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n d #2", "53": "n d #2", "54": "n d #2", "55": "n d #2", "56": "a #4", "57": "a #4", "58": "a #4", "59": "a #4", "60": "a #4", "61": "a #4", "62": "a #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "a x #5", "7": "a x #5", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n d #2", "40": "n d #2", "41": "n d #2", "42": "a #4", "43": "a #4", "44": "a #4", "45": "a #4", "46": "a #4", "47": "a #4", "48": "a #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "a x #5", "7.0-7.1": "a x #5", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y x", "9.3": "y x", "10.0-10.2": "y x", "10.3": "y x", "11": "y x"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "a #4"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"59": "a #4"}, "and_ff": {"54": "a #3"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "n", "5": "n"}, "and_qq": {"1.2": "n d #2"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Can be enabled in Firefox by setting the about:config preference layout.css.sticky.enabled to true", "2": "Enabled through the \"experimental Web Platform features\" flag", "3": "Not supported on any `table` parts - See [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=975644)", "4": "Supported on `th` elements, but not `thead` or `tr` - See [Chrome bug](https://crbug.com/417223)"}, "usage_perc_y": 12.21, "usage_perc_a": 58.26, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "positionsticky", "chrome_id": "6190250464378880", "firefox_id": "", "webkit_id": "feature-position:-sticky", "shown": true}