{"title": "Web Animations API", "description": "Lets you create animations that are run in the browser and as well as inspect and manipulate animations created through declarative means like CSS.", "spec": "http://w3c.github.io/web-animations/", "status": "wd", "links": [{"url": "http://updates.html5rocks.com/2014/05/Web-Animations---element-animate-is-now-in-Chrome-36", "title": "HTML5 Rocks"}, {"url": "http://updates.html5rocks.com/2013/12/New-Web-Animations-engine-in-Blink-drives-CSS-Animations-Transitions", "title": "HTML5 Rocks"}, {"url": "https://birtles.github.io/areweanimatedyet/", "title": "Current Firefox status"}, {"url": "https://github.com/web-animations/web-animations-js", "title": "Polyfill"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "a d #3", "34": "a d #3", "35": "a d #3", "36": "a d #3", "37": "a d #3", "38": "a d #3", "39": "a d #3", "40": "a d #3", "41": "a d #3", "42": "a d #3", "43": "a d #3", "44": "a d #3", "45": "a d #3", "46": "a d #3", "47": "a #3", "48": "a #3", "49": "a #3", "50": "a #3", "51": "a #3", "52": "a #3", "53": "a #3", "54": "a #3", "55": "a #3", "56": "a #3", "57": "a #3"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "a #2", "51": "a #2", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2", "62": "a #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "a #2", "37": "a #2", "38": "a #2", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "56": "a #2"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a #2"}, "and_chr": {"59": "a #2"}, "and_ff": {"54": "a #3"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "a #2"}, "samsung": {"4": "a #2", "5": "a #2"}, "and_qq": {"1.2": "a #2"}, "baidu": {"7.12": "a #2"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to basic support of `element.animate()`", "2": "Partial support refers to basic support of `element.animate()` and [playback control of AnimationPlayer](https://www.chromestatus.com/features/5633748733263872)", "3": "Partial support in Firefox is detailed in [Are we animated yet?](https://birtles.github.io/areweanimatedyet/)"}, "usage_perc_y": 0, "usage_perc_a": 74.07, "ucprefix": false, "parent": "", "keywords": "animate,play,pause,reverse,finish,currentTime,startTime,playbackRate,playState", "ie_id": "webanimationsjavascriptapi", "chrome_id": "4854343836631040,5633748733263872", "firefox_id": "web-animations", "webkit_id": "specification-web-animations", "shown": true}