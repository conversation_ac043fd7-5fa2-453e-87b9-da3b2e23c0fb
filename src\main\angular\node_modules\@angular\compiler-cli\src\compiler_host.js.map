{"version": 3, "file": "compiler_host.js", "sourceRoot": "", "sources": ["../../../../packages/compiler-cli/src/compiler_host.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAGH,oDAAiH;AACjH,uBAAyB;AACzB,2BAA6B;AAC7B,+BAAiC;AAEjC,IAAM,GAAG,GAAG,kCAAkC,CAAC;AAC/C,IAAM,GAAG,GAAG,UAAU,CAAC;AACvB,IAAM,YAAY,GAAG,gBAAgB,CAAC;AACtC,IAAM,YAAY,GAAG,kCAAkC,CAAC;AACxD,IAAM,eAAe,GAAG,kDAAkD,CAAC;AAC3E,IAAM,sBAAsB,GAAG,2DAA2D,CAAC;AAC3F,IAAM,cAAc,GAAG,oCAAoC,CAAC;AAO5D;IAYE,sBACc,OAAmB,EAAY,OAA+B,EAC9D,OAA4B,EAAE,gBAAmC;QAF/E,iBA4BC;QA3Ba,YAAO,GAAP,OAAO,CAAY;QAAY,YAAO,GAAP,OAAO,CAAwB;QAC9D,YAAO,GAAP,OAAO,CAAqB;QAbhC,sBAAiB,GAAG,IAAI,+BAAiB,EAAE,CAAC;QAI9C,kBAAa,GAAG,IAAI,GAAG,EAA4B,CAAC;QACpD,yBAAoB,GAAG,IAAI,GAAG,EAAmB,CAAC;QAClD,yBAAoB,GAAG,IAAI,GAAG,EAAU,CAAC;QACzC,iCAA4B,GAAG,IAAI,GAAG,EAAU,CAAC;QACjD,oBAAe,GAAG,IAAI,GAAG,EAAuB,CAAC;QAMvD,qDAAqD;QACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC1F,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAEtF,IAAM,OAAO,GAAW,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,IAAI,CAAC,sBAAsB,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzD,qCAAqC;QACrC,8DAA8D;QAC9D,oCAAoC;QACpC,wEAAwE;QACxE,cAAc;QACd,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,UAAC,QAAgB;YACvD,EAAE,CAAC,CAAC,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC;YACd,CAAC;YACD,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxD,MAAM,CAAC,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAC,CAAC;YAC3D,CAAC;YACD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IAED,8CAA8C;IAC9C,2CAAoB,GAApB,UAAqB,QAAgB,IAAY,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEnE,2CAAoB,GAApB,UAAqB,CAAS,EAAE,cAAsB;QACpD,IAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC;QAC7C,IAAI,MAAM,GAAgB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QAChE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,EAAE,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;gBAC9E,CAAC;gBACD,iEAAiE;gBACjE,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;YACnF,CAAC;YACD,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACvB,IAAM,QAAQ,GACV,EAAE,CAAC,iBAAiB,CACd,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC;iBACjF,cAAc,CAAC;YACxB,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YAChF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACxC,CAAC;QACD,MAAM,CAAC,MAAM,CAAC;IAChB,CAAC;IAAA,CAAC;IAEF;;;;;;;;;;;;;;OAcG;IACH,2CAAoB,GAApB,UAAqB,YAAoB,EAAE,cAAsB;QAC/D,+EAA+E;QAC/E,0DAA0D;QAC1D,EAAE,CAAC,CAAC,YAAY,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACxD,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACnD,iBAAiB;QACjB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAE7C,IAAM,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAM,YAAY,GAAG,gBAAgB,KAAK,CAAC,CAAC;YACxC,IAAI;YACJ,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QACnE,IAAM,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExD,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;YACpB,yBAAyB;YACzB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBACjB,kEAAkE;gBAClE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC;YACpF,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,yCAAyC;gBACzC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBACpD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,mBAAmB;YACnB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBACjB,MAAM,CAAC,YAAY,CAAC;YACtB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;oBACjC,6CAA6C;oBAC7C,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAClE,CAAC;gBACD,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,YAAY,CAAC;gBACtB,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,kCAAW,GAAnB,UAAoB,IAAY,EAAE,EAAU;QAC1C,IAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAClE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,wCAAiB,GAAzB,UAA0B,QAAgB;QACxC,IAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxD,EAAE,CAAC,CAAC,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,4DAA4D;YAC5D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACtE,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,iFAAiF;YACjF,0EAA0E;YAC1E,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAES,oCAAa,GAAvB,UAAwB,QAAgB;QACtC,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACR,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACnD,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACjF,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,iBAAe,QAAQ,6BAA0B,CAAC,CAAC;QACrE,CAAC;QACD,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC;IAED,qCAAc,GAAd,UAAe,QAAgB;QAC7B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvC,0EAA0E;YAC1E,4EAA4E;YAC5E,oDAAoD;YACpD,MAAM,CAAC;QACT,CAAC;QACD,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvB,IAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YAC7D,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YACnD,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,qEAAqE;gBACrE,qEAAqE;gBACrE,sBAAsB;gBACtB,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAChC,EAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxC,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED,mCAAY,GAAZ,UAAa,QAAgB,EAAE,WAAmB;QAChD,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,CAAC;YACH,IAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxE,IAAM,WAAS,GAAqB,mBAAmB;gBACnD,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,mBAAmB,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBAClF,EAAE,CAAC;YACP,IAAM,UAAU,GAAG,WAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,KAAK,CAAC,EAAf,CAAe,CAAC,CAAC;YACxD,IAAI,UAAU,GAAG,WAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,KAAK,CAAC,EAAf,CAAe,CAAC,CAAC;YACtD,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC;gBAC9B,WAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAS,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAS,CAAC;QACnB,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,8BAA4B,QAAU,CAAC,CAAC;YACtD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAEO,8CAAuB,GAA/B,UAAgC,UAA0B,EAAE,WAAmB;QAC7E,uFAAuF;QACvF,sFAAsF;QACtF,yCAAyC;QACzC,IAAI,UAAU,GAAmB,EAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAC,CAAC;QACxF,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACvB,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QAC1C,CAAC;QACD,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC;QACpF,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACZ,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC/B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YACD,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpB,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YACvC,CAAC;QACH,CAAC;QACD,MAAM,CAAC,UAAU,CAAC;IACpB,CAAC;IAED,mCAAY,GAAZ,UAAa,QAAgB;QAC3B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,kCAAW,GAAX,UAAY,QAAgB;QAC1B,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAED,wCAAiB,GAAjB,UAAkB,cAAsB;QACtC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC;IACnD,CAAC;IAED,mCAAY,GAAZ,UAAa,QAAgB;QAC3B,IAAM,YAAY,GACd,IAAI,CAAC,OAAO,CAAC,wBAAwB,KAAK,KAAK,GAAG,sBAAsB,GAAG,eAAe,CAAC;QAC/F,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;QACD,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvB,4BAA4B;YAC5B,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,cAAc,CAAC;oBAChD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAED,wCAAiB,GAAjB,UAAkB,QAAgB;QAChC,+DAA+D;QAC/D,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAU,CAAC;QACnC,GAAG,CAAC,CAAsB,UAA2B,EAA3B,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAA3B,cAA2B,EAA3B,IAA2B;YAAhD,IAAM,WAAW,SAAA;YACpB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,cAAY,QAAQ,mCAA8B,WAAa,CAAC,CAAC;YACjF,CAAC;YACD,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,GAAG,WAAW,CAAC;YACrB,CAAC;SACF;QAED,wDAAwD;QACxD,IAAI,YAAY,GAAW,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzD,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAChD,uFAAuF;YACvF,iBAAiB;YACjB,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAEO,qCAAc,GAAtB,UAAuB,QAAgB;QAAvC,iBAuDC;QAtDC,IAAM,gBAAgB,GAAG,UAAC,SAAiB;YACzC,IAAI,MAAM,GAAG,KAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACtD,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC;oBAC9C,oEAAoE;oBACpE,MAAM,GAAG,KAAK,CAAC;gBACjB,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,kFAAkF;oBAClF,YAAY;oBACZ,IAAI,CAAC;wBACH,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;wBACzD,EAAE,CAAC,CAAC,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;4BACzC,mFAAmF;4BACnF,MAAM,GAAG,KAAK,CAAC;4BACf,IAAM,cAAc,GAAQ,IAAI,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;4BAC3E,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;gCAC3B,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;gCAC7E,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oCACtB,IAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;oCAC5D,EAAE,CAAC,CAAC,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wCAC1C,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;wCACjE,EAAE,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC;4CACrC,KAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;4CAC/C,iCAAiC;4CACjC,iCAAiC;4CACjC,4CAA4C;4CAC5C,gCAAgC;wCAClC,CAAC;wCAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;4CAC7B,KAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;4CACvC,MAAM,GAAG,IAAI,CAAC;wCAChB,CAAC;oCACH,CAAC;gCACH,CAAC;4BACH,CAAC;wBACH,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,IAAM,QAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BACvC,EAAE,CAAC,CAAC,QAAM,IAAI,SAAS,CAAC,CAAC,CAAC;gCACxB,4BAA4B;gCAC5B,MAAM,GAAG,gBAAgB,CAAC,QAAM,CAAC,CAAC;4BACpC,CAAC;4BAAC,IAAI,CAAC,CAAC;gCACN,MAAM,GAAG,KAAK,CAAC;4BACjB,CAAC;wBACH,CAAC;oBACH,CAAC;oBAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,kEAAkE;wBAClE,MAAM,GAAG,KAAK,CAAC;oBACjB,CAAC;gBACH,CAAC;gBACD,KAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;YACD,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IAClD,CAAC;IACH,mBAAC;AAAD,CAAC,AAxVD,IAwVC;AAxVY,oCAAY;AA0VzB;IAAA;QACY,kBAAa,GAAkC,EAAE,CAAC;IAG9D,CAAC;IADC,qDAAgB,GAAhB,UAAiB,QAAgB,IAAU,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACnF,iCAAC;AAAD,CAAC,AAJD,IAIC;AAJY,gEAA0B;AAMvC;IAAiD,+CAA0B;IAIzE,qCAAoB,IAA6B;QAAjD,YACE,iBAAO,SAIR;QALmB,UAAI,GAAJ,IAAI,CAAyB;QAE/C,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YACzB,KAAI,CAAC,eAAe,GAAG,UAAC,aAAqB,IAAK,OAAA,IAAI,CAAC,eAAiB,CAAC,aAAa,CAAC,EAArC,CAAqC,CAAC;QAC1F,CAAC;;IACH,CAAC;IAED,gDAAU,GAAV,UAAW,QAAgB;QACzB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC;IAED,8CAAQ,GAAR,UAAS,QAAgB,IAAY,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE3E,kDAAY,GAAZ,UAAa,CAAS;QACpB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,gEAAgE;YAChE,MAAM,IAAI,KAAK,CAAC,kDAAgD,CAAG,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IACH,kCAAC;AAAD,CAAC,AAxBD,CAAiD,0BAA0B,GAwB1E;AAxBY,kEAA2B;AA0BxC;IAA6C,2CAA0B;IAAvE;;IAuBA,CAAC;IArBC,4CAAU,GAAV,UAAW,QAAgB;QACzB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAED,iDAAe,GAAf,UAAgB,aAAqB;QACnC,IAAI,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAClD,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,0CAAQ,GAAR,UAAS,QAAgB,IAAY,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAEhF,8CAAY,GAAZ,UAAa,CAAS;QACpB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,gEAAgE;YAChE,MAAM,IAAI,KAAK,CAAC,kDAAgD,CAAG,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACH,8BAAC;AAAD,CAAC,AAvBD,CAA6C,0BAA0B,GAuBtE;AAvBY,0DAAuB", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AotCompilerHost, StaticSymbol} from '@angular/compiler';\nimport {AngularCompilerOptions, CollectorOptions, MetadataCollector, ModuleMetadata} from '@angular/tsc-wrapped';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as ts from 'typescript';\n\nconst EXT = /(\\.ts|\\.d\\.ts|\\.js|\\.jsx|\\.tsx)$/;\nconst DTS = /\\.d\\.ts$/;\nconst NODE_MODULES = '/node_modules/';\nconst IS_GENERATED = /\\.(ngfactory|ngstyle|ngsummary)$/;\nconst GENERATED_FILES = /\\.ngfactory\\.ts$|\\.ngstyle\\.ts$|\\.ngsummary\\.ts$/;\nconst GENERATED_OR_DTS_FILES = /\\.d\\.ts$|\\.ngfactory\\.ts$|\\.ngstyle\\.ts$|\\.ngsummary\\.ts$/;\nconst SHALLOW_IMPORT = /^((\\w|-)+|(@(\\w|-)+(\\/(\\w|-)+)+))$/;\n\nexport interface CompilerHostContext extends ts.ModuleResolutionHost {\n  readResource(fileName: string): Promise<string>;\n  assumeFileExists(fileName: string): void;\n}\n\nexport class CompilerHost implements AotCompilerHost {\n  protected metadataCollector = new MetadataCollector();\n  private isGenDirChildOfRootDir: boolean;\n  protected basePath: string;\n  private genDir: string;\n  private resolverCache = new Map<string, ModuleMetadata[]>();\n  private flatModuleIndexCache = new Map<string, boolean>();\n  private flatModuleIndexNames = new Set<string>();\n  private flatModuleIndexRedirectNames = new Set<string>();\n  private moduleFileNames = new Map<string, string|null>();\n  protected resolveModuleNameHost: CompilerHostContext;\n\n  constructor(\n      protected program: ts.Program, protected options: AngularCompilerOptions,\n      protected context: CompilerHostContext, collectorOptions?: CollectorOptions) {\n    // normalize the path so that it never ends with '/'.\n    this.basePath = path.normalize(path.join(this.options.basePath, '.')).replace(/\\\\/g, '/');\n    this.genDir = path.normalize(path.join(this.options.genDir, '.')).replace(/\\\\/g, '/');\n\n    const genPath: string = path.relative(this.basePath, this.genDir);\n    this.isGenDirChildOfRootDir = genPath === '' || !genPath.startsWith('..');\n    this.resolveModuleNameHost = Object.create(this.context);\n\n    // When calling ts.resolveModuleName,\n    // additional allow checks for .d.ts files to be done based on\n    // checks for .ngsummary.json files,\n    // so that our codegen depends on fewer inputs and requires to be called\n    // less often.\n    // This is needed as we use ts.resolveModuleName in reflector_host\n    // and it should be able to resolve summary file names.\n    this.resolveModuleNameHost.fileExists = (fileName: string): boolean => {\n      if (this.context.fileExists(fileName)) {\n        return true;\n      }\n      if (DTS.test(fileName)) {\n        const base = fileName.substring(0, fileName.length - 5);\n        return this.context.fileExists(base + '.ngsummary.json');\n      }\n      return false;\n    };\n  }\n\n  // We use absolute paths on disk as canonical.\n  getCanonicalFileName(fileName: string): string { return fileName; }\n\n  moduleNameToFileName(m: string, containingFile: string): string|null {\n    const key = m + ':' + (containingFile || '');\n    let result: string|null = this.moduleFileNames.get(key) || null;\n    if (!result) {\n      if (!containingFile || !containingFile.length) {\n        if (m.indexOf('.') === 0) {\n          throw new Error('Resolution of relative paths requires a containing file.');\n        }\n        // Any containing file gives the same result for absolute imports\n        containingFile = this.getCanonicalFileName(path.join(this.basePath, 'index.ts'));\n      }\n      m = m.replace(EXT, '');\n      const resolved =\n          ts.resolveModuleName(\n                m, containingFile.replace(/\\\\/g, '/'), this.options, this.resolveModuleNameHost)\n              .resolvedModule;\n      result = resolved ? this.getCanonicalFileName(resolved.resolvedFileName) : null;\n      this.moduleFileNames.set(key, result);\n    }\n    return result;\n  };\n\n  /**\n   * We want a moduleId that will appear in import statements in the generated code.\n   * These need to be in a form that system.js can load, so absolute file paths don't work.\n   *\n   * The `containingFile` is always in the `genDir`, where as the `importedFile` can be in\n   * `genDir`, `node_module` or `basePath`.  The `importedFile` is either a generated file or\n   * existing file.\n   *\n   *               | genDir   | node_module |  rootDir\n   * --------------+----------+-------------+----------\n   * generated     | relative |   relative  |   n/a\n   * existing file |   n/a    |   absolute  |  relative(*)\n   *\n   * NOTE: (*) the relative path is computed depending on `isGenDirChildOfRootDir`.\n   */\n  fileNameToModuleName(importedFile: string, containingFile: string): string {\n    // If a file does not yet exist (because we compile it later), we still need to\n    // assume it exists it so that the `resolve` method works!\n    if (importedFile !== containingFile && !this.context.fileExists(importedFile)) {\n      this.context.assumeFileExists(importedFile);\n    }\n\n    containingFile = this.rewriteGenDirPath(containingFile);\n    const containingDir = path.dirname(containingFile);\n    // drop extension\n    importedFile = importedFile.replace(EXT, '');\n\n    const nodeModulesIndex = importedFile.indexOf(NODE_MODULES);\n    const importModule = nodeModulesIndex === -1 ?\n        null :\n        importedFile.substring(nodeModulesIndex + NODE_MODULES.length);\n    const isGeneratedFile = IS_GENERATED.test(importedFile);\n\n    if (isGeneratedFile) {\n      // rewrite to genDir path\n      if (importModule) {\n        // it is generated, therefore we do a relative path to the factory\n        return this.dotRelative(containingDir, this.genDir + NODE_MODULES + importModule);\n      } else {\n        // assume that import is also in `genDir`\n        importedFile = this.rewriteGenDirPath(importedFile);\n        return this.dotRelative(containingDir, importedFile);\n      }\n    } else {\n      // user code import\n      if (importModule) {\n        return importModule;\n      } else {\n        if (!this.isGenDirChildOfRootDir) {\n          // assume that they are on top of each other.\n          importedFile = importedFile.replace(this.basePath, this.genDir);\n        }\n        if (SHALLOW_IMPORT.test(importedFile)) {\n          return importedFile;\n        }\n        return this.dotRelative(containingDir, importedFile);\n      }\n    }\n  }\n\n  private dotRelative(from: string, to: string): string {\n    const rPath: string = path.relative(from, to).replace(/\\\\/g, '/');\n    return rPath.startsWith('.') ? rPath : './' + rPath;\n  }\n\n  /**\n   * Moves the path into `genDir` folder while preserving the `node_modules` directory.\n   */\n  private rewriteGenDirPath(filepath: string) {\n    const nodeModulesIndex = filepath.indexOf(NODE_MODULES);\n    if (nodeModulesIndex !== -1) {\n      // If we are in node_modulse, transplant them into `genDir`.\n      return path.join(this.genDir, filepath.substring(nodeModulesIndex));\n    } else {\n      // pretend that containing file is on top of the `genDir` to normalize the paths.\n      // we apply the `genDir` => `rootDir` delta through `rootDirPrefix` later.\n      return filepath.replace(this.basePath, this.genDir);\n    }\n  }\n\n  protected getSourceFile(filePath: string): ts.SourceFile {\n    const sf = this.program.getSourceFile(filePath);\n    if (!sf) {\n      if (this.context.fileExists(filePath)) {\n        const sourceText = this.context.readFile(filePath);\n        return ts.createSourceFile(filePath, sourceText, ts.ScriptTarget.Latest, true);\n      }\n      throw new Error(`Source file ${filePath} not present in program.`);\n    }\n    return sf;\n  }\n\n  getMetadataFor(filePath: string): ModuleMetadata[]|undefined {\n    if (!this.context.fileExists(filePath)) {\n      // If the file doesn't exists then we cannot return metadata for the file.\n      // This will occur if the user refernced a declared module for which no file\n      // exists for the module (i.e. jQuery or angularjs).\n      return;\n    }\n    if (DTS.test(filePath)) {\n      const metadataPath = filePath.replace(DTS, '.metadata.json');\n      if (this.context.fileExists(metadataPath)) {\n        return this.readMetadata(metadataPath, filePath);\n      } else {\n        // If there is a .d.ts file but no metadata file we need to produce a\n        // v3 metadata from the .d.ts file as v3 includes the exports we need\n        // to resolve symbols.\n        return [this.upgradeVersion1Metadata(\n            {'__symbolic': 'module', 'version': 1, 'metadata': {}}, filePath)];\n      }\n    } else {\n      const sf = this.getSourceFile(filePath);\n      const metadata = this.metadataCollector.getMetadata(sf);\n      return metadata ? [metadata] : [];\n    }\n  }\n\n  readMetadata(filePath: string, dtsFilePath: string): ModuleMetadata[] {\n    let metadatas = this.resolverCache.get(filePath);\n    if (metadatas) {\n      return metadatas;\n    }\n    try {\n      const metadataOrMetadatas = JSON.parse(this.context.readFile(filePath));\n      const metadatas: ModuleMetadata[] = metadataOrMetadatas ?\n          (Array.isArray(metadataOrMetadatas) ? metadataOrMetadatas : [metadataOrMetadatas]) :\n          [];\n      const v1Metadata = metadatas.find(m => m.version === 1);\n      let v3Metadata = metadatas.find(m => m.version === 3);\n      if (!v3Metadata && v1Metadata) {\n        metadatas.push(this.upgradeVersion1Metadata(v1Metadata, dtsFilePath));\n      }\n      this.resolverCache.set(filePath, metadatas);\n      return metadatas;\n    } catch (e) {\n      console.error(`Failed to read JSON file ${filePath}`);\n      throw e;\n    }\n  }\n\n  private upgradeVersion1Metadata(v1Metadata: ModuleMetadata, dtsFilePath: string): ModuleMetadata {\n    // patch up v1 to v3 by merging the metadata with metadata collected from the d.ts file\n    // as the only difference between the versions is whether all exports are contained in\n    // the metadata and the `extends` clause.\n    let v3Metadata: ModuleMetadata = {'__symbolic': 'module', 'version': 3, 'metadata': {}};\n    if (v1Metadata.exports) {\n      v3Metadata.exports = v1Metadata.exports;\n    }\n    for (let prop in v1Metadata.metadata) {\n      v3Metadata.metadata[prop] = v1Metadata.metadata[prop];\n    }\n\n    const exports = this.metadataCollector.getMetadata(this.getSourceFile(dtsFilePath));\n    if (exports) {\n      for (let prop in exports.metadata) {\n        if (!v3Metadata.metadata[prop]) {\n          v3Metadata.metadata[prop] = exports.metadata[prop];\n        }\n      }\n      if (exports.exports) {\n        v3Metadata.exports = exports.exports;\n      }\n    }\n    return v3Metadata;\n  }\n\n  loadResource(filePath: string): Promise<string>|string {\n    return this.context.readResource(filePath);\n  }\n\n  loadSummary(filePath: string): string|null {\n    if (this.context.fileExists(filePath)) {\n      return this.context.readFile(filePath);\n    }\n    return null;\n  }\n\n  getOutputFileName(sourceFilePath: string): string {\n    return sourceFilePath.replace(EXT, '') + '.d.ts';\n  }\n\n  isSourceFile(filePath: string): boolean {\n    const excludeRegex =\n        this.options.generateCodeForLibraries === false ? GENERATED_OR_DTS_FILES : GENERATED_FILES;\n    if (excludeRegex.test(filePath)) {\n      return false;\n    }\n    if (DTS.test(filePath)) {\n      // Check for a bundle index.\n      if (this.hasBundleIndex(filePath)) {\n        const normalFilePath = path.normalize(filePath);\n        return this.flatModuleIndexNames.has(normalFilePath) ||\n            this.flatModuleIndexRedirectNames.has(normalFilePath);\n      }\n    }\n    return true;\n  }\n\n  calculateEmitPath(filePath: string): string {\n    // Write codegen in a directory structure matching the sources.\n    let root = this.options.basePath !;\n    for (const eachRootDir of this.options.rootDirs || []) {\n      if (this.options.trace) {\n        console.error(`Check if ${filePath} is under rootDirs element ${eachRootDir}`);\n      }\n      if (path.relative(eachRootDir, filePath).indexOf('.') !== 0) {\n        root = eachRootDir;\n      }\n    }\n\n    // transplant the codegen path to be inside the `genDir`\n    let relativePath: string = path.relative(root, filePath);\n    while (relativePath.startsWith('..' + path.sep)) {\n      // Strip out any `..` path such as: `../node_modules/@foo` as we want to put everything\n      // into `genDir`.\n      relativePath = relativePath.substr(3);\n    }\n\n    return path.join(this.options.genDir, relativePath);\n  }\n\n  private hasBundleIndex(filePath: string): boolean {\n    const checkBundleIndex = (directory: string): boolean => {\n      let result = this.flatModuleIndexCache.get(directory);\n      if (result == null) {\n        if (path.basename(directory) == 'node_module') {\n          // Don't look outside the node_modules this package is installed in.\n          result = false;\n        } else {\n          // A bundle index exists if the typings .d.ts file has a metadata.json that has an\n          // importAs.\n          try {\n            const packageFile = path.join(directory, 'package.json');\n            if (this.context.fileExists(packageFile)) {\n              // Once we see a package.json file, assume false until it we find the bundle index.\n              result = false;\n              const packageContent: any = JSON.parse(this.context.readFile(packageFile));\n              if (packageContent.typings) {\n                const typings = path.normalize(path.join(directory, packageContent.typings));\n                if (DTS.test(typings)) {\n                  const metadataFile = typings.replace(DTS, '.metadata.json');\n                  if (this.context.fileExists(metadataFile)) {\n                    const metadata = JSON.parse(this.context.readFile(metadataFile));\n                    if (metadata.flatModuleIndexRedirect) {\n                      this.flatModuleIndexRedirectNames.add(typings);\n                      // Note: don't set result = true,\n                      // as this would mark this folder\n                      // as having a bundleIndex too early without\n                      // filling the bundleIndexNames.\n                    } else if (metadata.importAs) {\n                      this.flatModuleIndexNames.add(typings);\n                      result = true;\n                    }\n                  }\n                }\n              }\n            } else {\n              const parent = path.dirname(directory);\n              if (parent != directory) {\n                // Try the parent directory.\n                result = checkBundleIndex(parent);\n              } else {\n                result = false;\n              }\n            }\n          } catch (e) {\n            // If we encounter any errors assume we this isn't a bundle index.\n            result = false;\n          }\n        }\n        this.flatModuleIndexCache.set(directory, result);\n      }\n      return result;\n    };\n\n    return checkBundleIndex(path.dirname(filePath));\n  }\n}\n\nexport class CompilerHostContextAdapter {\n  protected assumedExists: {[fileName: string]: boolean} = {};\n\n  assumeFileExists(fileName: string): void { this.assumedExists[fileName] = true; }\n}\n\nexport class ModuleResolutionHostAdapter extends CompilerHostContextAdapter implements\n    CompilerHostContext {\n  public directoryExists: ((directoryName: string) => boolean)|undefined;\n\n  constructor(private host: ts.ModuleResolutionHost) {\n    super();\n    if (host.directoryExists) {\n      this.directoryExists = (directoryName: string) => host.directoryExists !(directoryName);\n    }\n  }\n\n  fileExists(fileName: string): boolean {\n    return this.assumedExists[fileName] || this.host.fileExists(fileName);\n  }\n\n  readFile(fileName: string): string { return this.host.readFile(fileName); }\n\n  readResource(s: string) {\n    if (!this.host.fileExists(s)) {\n      // TODO: We should really have a test for error cases like this!\n      throw new Error(`Compilation failed. Resource file not found: ${s}`);\n    }\n    return Promise.resolve(this.host.readFile(s));\n  }\n}\n\nexport class NodeCompilerHostContext extends CompilerHostContextAdapter implements\n    CompilerHostContext {\n  fileExists(fileName: string): boolean {\n    return this.assumedExists[fileName] || fs.existsSync(fileName);\n  }\n\n  directoryExists(directoryName: string): boolean {\n    try {\n      return fs.statSync(directoryName).isDirectory();\n    } catch (e) {\n      return false;\n    }\n  }\n\n  readFile(fileName: string): string { return fs.readFileSync(fileName, 'utf8'); }\n\n  readResource(s: string) {\n    if (!this.fileExists(s)) {\n      // TODO: We should really have a test for error cases like this!\n      throw new Error(`Compilation failed. Resource file not found: ${s}`);\n    }\n    return Promise.resolve(this.readFile(s));\n  }\n}\n"]}