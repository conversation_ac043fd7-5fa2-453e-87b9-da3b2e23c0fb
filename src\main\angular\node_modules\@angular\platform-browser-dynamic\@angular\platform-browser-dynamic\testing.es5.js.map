{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/platform-browser-dynamic/testing/index.ts", "../../../../../packages/platform-browser-dynamic/testing/src/testing.ts", "../../../../../packages/platform-browser-dynamic/testing/src/private_export_testing.ts", "../../../../../packages/platform-browser-dynamic/testing/src/dom_test_component_renderer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {platformCoreDynamicTesting} from '@angular/compiler/testing';\nimport {NgModule, PlatformRef, Provider, createPlatformFactory} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from '@angular/platform-browser-dynamic';\nimport {BrowserTestingModule} from '@angular/platform-browser/testing';\n\nimport {DOMTestComponentRenderer} from './dom_test_component_renderer';\n\nexport * from './private_export_testing'\n\n/**\n * @stable\n */\nexport const platformBrowserDynamicTesting = createPlatformFactory(\n    platformCoreDynamicTesting, 'browserDynamicTesting',\n    INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @stable\n */\n\nexport class BrowserDynamicTestingModule {\nstatic decorators: DecoratorInvocation[] = [\n{ type: NgModule, args: [{\n  exports: [BrowserTestingModule],\n  providers: [\n    {provide: TestComponentRenderer, useClass: DOMTestComponentRenderer},\n  ]\n}, ] },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {DOMTestComponentRenderer as ɵDOMTestComponentRenderer} from './dom_test_component_renderer';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/platform-browser';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\n\nexport class DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor( private _doc: any /** TODO #9100 */) { super(); }\n\n  insertRootElement(rootElId: string) {\n    const rootEl = <HTMLElement>getDOM().firstChild(\n        getDOM().content(getDOM().createTemplate(`<div id=\"${rootElId}\"></div>`)));\n\n    // TODO(juliemr): can/should this be optional?\n    const oldRoots = getDOM().querySelectorAll(this._doc, '[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      getDOM().remove(oldRoots[i]);\n    }\n    getDOM().appendChild(this._doc.body, rootEl);\n  }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n{type: undefined, decorators: [{ type: Inject, args: [DOCUMENT, ] }, ]},\n];\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": ["getDOM"], "mappings": ";;;;;;;;AGAA,OAAA,EAAA,qBAAA,EAAA,MAAA,uBAAA,CAAA;;;;;;;;;;GAiBA;AAAA;;GAAA;AAEA;IAAA,oDAAA;IACA,kCAAA,IAAgB,CAAhB,iBAAA;QAAA,sCAKA;QADI,KAAJ,CAAA,IAAA,GAAA,IAAA,CAAqBA,CAArB,iBAAA;;IACA,CAAA;IACAA,oDAAc,GAAdA,UAAe,QAAfA;QACA,IAAA,MAAA,GAAA,OAAA,EAAA,CAAA,UAAA,CAAA,OAAA,EAAA,CAAA,OAAA,CAAA,OAAA,EAAA,CAAA,cAAA,CAAA,eAAA,QAAA,cAAA,CAAA,CAAA,CAAA,CAAA;QACIA,8CAA6C;QACjD,IAAA,QAAA,GAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,YAAA,CAAA,CAAA;;YACA,OAAA,EAAA,CAAA,MAAA,CAAA,QAAA,CAA2C,CAA3C,CAAA,CAAA,CAAA;QACA,CAAA;QACA,OAAA,EAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;;IAEO,+BAAP;AAAO,CAAP,AAfA,CAAA,qBAAA,GAeA;AACA,wBAAA,CAAA,UAA8B,GAAG;IACjC,EAAA,IAAA,EAAA,UAAA,EAAA;;ADpCA,kBAAA;;;;;;;;;;;;;;;;;;;;;;AD+BA;;;;GAGA;AACA;IAAA;IACA,CAAA;IAAA,kCAAA;AAAA,CAAA,AADA,IACA;AACA,2BAAA,CAAA,UAAA,GAAA;IACA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA;gBACA,OAAA,EAAA,CAAA,oBAAA,CAAA;;oBAEA,EAAA,OAAA,EAAA,qBAAA,EACE,QADF,EAAA,wBAAA,EAAA;;aDzCA,EAAA,EAAA;;;;;;;;;;GAYG;;;;;;;"}