[{"__symbolic": "module", "version": 3, "metadata": {"MockDirectiveResolver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "DirectiveResolver"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileReflector"}]}], "_clearCacheFor": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "setDirective": [{"__symbolic": "method"}], "setProvidersOverride": [{"__symbolic": "method"}], "setViewProvidersOverride": [{"__symbolic": "method"}], "setView": [{"__symbolic": "method"}], "setInlineTemplate": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"MockDirectiveResolver": {"__symbolic": "class", "extends": {"__symbolic": "reference", "module": "@angular/compiler", "name": "DirectiveResolver"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector"}, {"__symbolic": "reference", "module": "@angular/compiler", "name": "CompileReflector"}]}], "_clearCacheFor": [{"__symbolic": "method"}], "resolve": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "setDirective": [{"__symbolic": "method"}], "setProvidersOverride": [{"__symbolic": "method"}], "setViewProvidersOverride": [{"__symbolic": "method"}], "setView": [{"__symbolic": "method"}], "setInlineTemplate": [{"__symbolic": "method"}]}}}}]