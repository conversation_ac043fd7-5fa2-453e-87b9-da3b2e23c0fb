[{"__symbolic": "module", "version": 3, "metadata": {"ViewCompileResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "ViewCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "../compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}]}], "compileComponent": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"ViewCompileResult": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}]}}, "ViewCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "../injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "../config", "name": "CompilerConfig"}, {"__symbolic": "reference", "module": "../compile_reflector", "name": "CompileReflector"}, {"__symbolic": "reference", "module": "../schema/element_schema_registry", "name": "ElementSchemaRegistry"}]}], "compileComponent": [{"__symbolic": "method"}]}}}}]