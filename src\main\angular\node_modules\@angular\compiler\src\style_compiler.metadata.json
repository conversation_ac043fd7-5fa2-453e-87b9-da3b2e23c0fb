[{"__symbolic": "module", "version": 3, "metadata": {"StylesCompileDependency": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 70}]}]}}, "CompiledStylesheet": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./util", "name": "OutputContext"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "StylesCompileDependency"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileStylesheetMetadata"}]}]}}, "StyleCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./url_resolver", "name": "UrlResolver"}]}], "compileComponent": [{"__symbolic": "method"}], "compileStyles": [{"__symbolic": "method"}], "needsStyleShim": [{"__symbolic": "method"}], "_compileStyles": [{"__symbolic": "method"}], "_shimIfNeeded": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"StylesCompileDependency": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 23, "character": 70}]}]}}, "CompiledStylesheet": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./util", "name": "OutputContext"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "StylesCompileDependency"}]}, {"__symbolic": "reference", "name": "boolean"}, {"__symbolic": "reference", "module": "./compile_metadata", "name": "CompileStylesheetMetadata"}]}]}}, "StyleCompiler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "./injectable", "name": "CompilerInjectable"}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./url_resolver", "name": "UrlResolver"}]}], "compileComponent": [{"__symbolic": "method"}], "compileStyles": [{"__symbolic": "method"}], "needsStyleShim": [{"__symbolic": "method"}], "_compileStyles": [{"__symbolic": "method"}], "_shimIfNeeded": [{"__symbolic": "method"}]}}}}]