[{"__symbolic": "module", "version": 3, "metadata": {"AotSummaryResolverHost": {"__symbolic": "interface"}, "AotSummaryResolver": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbolCache"}]}], "isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}], "_loadSummaryFile": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"AotSummaryResolverHost": {"__symbolic": "interface"}, "AotSummaryResolver": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "module": "./static_symbol", "name": "StaticSymbolCache"}]}], "isLibraryFile": [{"__symbolic": "method"}], "getLibraryFileName": [{"__symbolic": "method"}], "resolveSummary": [{"__symbolic": "method"}], "getSymbolsOf": [{"__symbolic": "method"}], "getImportAs": [{"__symbolic": "method"}], "addSummary": [{"__symbolic": "method"}], "_loadSummaryFile": [{"__symbolic": "method"}]}}}}]