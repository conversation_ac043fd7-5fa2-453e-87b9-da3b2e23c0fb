{"version": 3, "file": "symbols.spec.js", "sourceRoot": "", "sources": ["../../../../../tools/@angular/tsc-wrapped/test/symbols.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,+BAAiC;AAEjC,wCAAkE;AAClE,0CAAuC;AAEvC,uDAAwE;AAExE,QAAQ,CAAC,SAAS,EAAE;IAClB,IAAI,OAAgB,CAAC;IACrB,IAAM,SAAS,GAAG,YAAY,CAAC;IAE/B,UAAU,CAAC,cAAM,OAAA,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,EAA3B,CAA2B,CAAC,CAAC;IAE9C,EAAE,CAAC,gCAAgC,EAAE,cAAM,OAAA,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,EAAvC,CAAuC,CAAC,CAAC;IAEpF,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,EAAvC,CAAuC,CAAC,CAAC;IAE1D,EAAE,CAAC,kCAAkC,EAAE,cAAM,OAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,EAAE,EAA9C,CAA8C,CAAC,CAAC;IAC7F,EAAE,CAAC,wCAAwC,EACxC,cAAM,OAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAArD,CAAqD,CAAC,CAAC;IAChE,EAAE,CAAC,wCAAwC,EACxC,cAAM,OAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAArD,CAAqD,CAAC,CAAC;IAChE,EAAE,CAAC,+CAA+C,EAC/C,cAAM,OAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EAAE,EAAhD,CAAgD,CAAC,CAAC;IAC3D,EAAE,CAAC,yDAAyD,EACzD,cAAM,OAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,EAAE,EAAxD,CAAwD,CAAC,CAAC;IAEnE,IAAI,IAA4B,CAAC;IACjC,IAAI,OAA2B,CAAC;IAChC,IAAI,OAAmB,CAAC;IACxB,IAAI,WAA0B,CAAC;IAC/B,IAAI,OAAsB,CAAC;IAE3B,UAAU,CAAC;QACT,IAAI,GAAG,IAAI,uBAAI,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;QACtE,OAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAC/B,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACtD,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE;QACtD,sCAAmB,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,CAAC;QAC7D,GAAG,CAAC,CAAqB,UAAwB,EAAxB,KAAA,OAAO,CAAC,cAAc,EAAE,EAAxB,cAAwB,EAAxB,IAAwB;YAA5C,IAAM,UAAU,SAAA;YACnB,sCAAmB,CAAC,OAAO,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3E;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE;QAC5C,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE;QACvD,IAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAGH,EAAE,CAAC,8CAA8C,EAAE;QACjD,IAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QAC7C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aAC9B,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAC,CAAC,CAAC;QAC9E,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QAC7C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aAC9B,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAC,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE;QACxC,IAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,EAAC,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE;QACxD,IAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE;QAC9C,IAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,CAAC,CAAC;IAC1F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2DAA2D,EAAE;QAC9D,IAAM,IAAI,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAApC,CAAoC,CAAC,CAAC;QAC3F,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3B,IAAM,KAAK,GAAG,UAAC,IAAa;YAC1B,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;gBACrC,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB;oBACxC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACtC,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;oBACpC,IAAM,mBAAmB,GAA2B,IAAI,CAAC;oBACzD,IAAM,QAAQ,GAAkB,mBAAmB,CAAC,IAAI,CAAC;oBACzD,IAAM,MAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;oBAC3B,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAI,CAAC,CAAC;oBACrC,MAAM,CAAC,4CAAmC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAI,CAAC,CAAC;oBAEjF,6DAA6D;oBAC7D,MAAM,CAAC,MAAI,KAAK,cAAc,CAAC;YACnC,CAAC;YACD,MAAM,CAAC,KAAK,CAAC;QACf,CAAC,CAAC;QACF,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAM,KAAK,GAAc;IACvB,WAAW,EAAE,iIAKZ;IACD,gBAAgB,EAAE,kEAEjB;IACD,YAAY,EAAE,2GAKb;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as ts from 'typescript';\n\nimport {isMetadataGlobalReferenceExpression} from '../src/schema';\nimport {Symbols} from '../src/symbols';\n\nimport {Directory, Host, expectNoDiagnostics} from './typescript.mocks';\n\ndescribe('Symbols', () => {\n  let symbols: Symbols;\n  const someValue = 'some-value';\n\n  beforeEach(() => symbols = new Symbols(null));\n\n  it('should be able to add a symbol', () => symbols.define('someSymbol', someValue));\n\n  beforeEach(() => symbols.define('someSymbol', someValue));\n\n  it('should be able to `has` a symbol', () => expect(symbols.has('someSymbol')).toBeTruthy());\n  it('should be able to `get` a symbol value',\n     () => expect(symbols.resolve('someSymbol')).toBe(someValue));\n  it('should be able to `get` a symbol value',\n     () => expect(symbols.resolve('someSymbol')).toBe(someValue));\n  it('should be able to determine symbol is missing',\n     () => expect(symbols.has('missingSymbol')).toBeFalsy());\n  it('should return undefined from `get` for a missing symbol',\n     () => expect(symbols.resolve('missingSymbol')).toBeUndefined());\n\n  let host: ts.LanguageServiceHost;\n  let service: ts.LanguageService;\n  let program: ts.Program;\n  let expressions: ts.SourceFile;\n  let imports: ts.SourceFile;\n\n  beforeEach(() => {\n    host = new Host(FILES, ['consts.ts', 'expressions.ts', 'imports.ts']);\n    service = ts.createLanguageService(host);\n    program = service.getProgram();\n    expressions = program.getSourceFile('expressions.ts');\n    imports = program.getSourceFile('imports.ts');\n  });\n\n  it('should not have syntax errors in the test sources', () => {\n    expectNoDiagnostics(service.getCompilerOptionsDiagnostics());\n    for (const sourceFile of program.getSourceFiles()) {\n      expectNoDiagnostics(service.getSyntacticDiagnostics(sourceFile.fileName));\n    }\n  });\n\n  it('should be able to find the source files', () => {\n    expect(expressions).toBeDefined();\n    expect(imports).toBeDefined();\n  });\n\n  it('should be able to create symbols for a source file', () => {\n    const symbols = new Symbols(expressions);\n    expect(symbols).toBeDefined();\n  });\n\n\n  it('should be able to find symbols in expression', () => {\n    const symbols = new Symbols(expressions);\n    expect(symbols.has('someName')).toBeTruthy();\n    expect(symbols.resolve('someName'))\n        .toEqual({__symbolic: 'reference', module: './consts', name: 'someName'});\n    expect(symbols.has('someBool')).toBeTruthy();\n    expect(symbols.resolve('someBool'))\n        .toEqual({__symbolic: 'reference', module: './consts', name: 'someBool'});\n  });\n\n  it('should be able to detect a * import', () => {\n    const symbols = new Symbols(imports);\n    expect(symbols.resolve('b')).toEqual({__symbolic: 'reference', module: 'b'});\n  });\n\n  it('should be able to detect importing a default export', () => {\n    const symbols = new Symbols(imports);\n    expect(symbols.resolve('d')).toEqual({__symbolic: 'reference', module: 'd', default: true});\n  });\n\n  it('should be able to import a renamed symbol', () => {\n    const symbols = new Symbols(imports);\n    expect(symbols.resolve('g')).toEqual({__symbolic: 'reference', name: 'f', module: 'f'});\n  });\n\n  it('should be able to resolve any symbol in core global scope', () => {\n    const core = program.getSourceFiles().find(source => source.fileName.endsWith('lib.d.ts'));\n    expect(core).toBeDefined();\n    const visit = (node: ts.Node): boolean => {\n      switch (node.kind) {\n        case ts.SyntaxKind.VariableStatement:\n        case ts.SyntaxKind.VariableDeclarationList:\n          return ts.forEachChild(node, visit);\n        case ts.SyntaxKind.VariableDeclaration:\n          const variableDeclaration = <ts.VariableDeclaration>node;\n          const nameNode = <ts.Identifier>variableDeclaration.name;\n          const name = nameNode.text;\n          const result = symbols.resolve(name);\n          expect(isMetadataGlobalReferenceExpression(result) && result.name).toEqual(name);\n\n          // Ignore everything after Float64Array as it is IE specific.\n          return name === 'Float64Array';\n      }\n      return false;\n    };\n    ts.forEachChild(core, visit);\n  });\n});\n\nconst FILES: Directory = {\n  'consts.ts': `\n    export var someName = 'some-name';\n    export var someBool = true;\n    export var one = 1;\n    export var two = 2;\n  `,\n  'expressions.ts': `\n    import {someName, someBool, one, two} from './consts';\n  `,\n  'imports.ts': `\n    import * as b from 'b';\n    import 'c';\n    import d from 'd';\n    import {f as g} from 'f';\n  `\n};\n"]}