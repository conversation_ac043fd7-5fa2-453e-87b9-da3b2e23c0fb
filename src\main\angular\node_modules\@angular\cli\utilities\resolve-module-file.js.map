{"version": 3, "file": "resolve-module-file.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["utilities/resolve-module-file.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,yBAAyB;AACzB,+DAA8E;AAE9E,2BACE,kBAA0B,EAAE,OAAY,EAAE,WAAgB,EAAE,SAAc;IAC1E,IAAI,cAAc,GAAG,kBAAkB,CAAC;IACxC,IAAI,aAAa,GAAG,EAAE,CAAC;IAEvB,qDAAqD;IACrD,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAClF,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC1C,CAAC;IAED,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjD,cAAc,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;QACjC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,wBAAwB,GAAG,GAAG,cAAc,YAAY,CAAC;IAE/D,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,wBAAwB,CAAC,CAAC;IAC9E,IAAI,cAAc,GAAG,aAAa,CAAC,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAExF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,sBAAsB,GAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,wBAAwB,CAAC,CAAC;QACrE,cAAc,GAAG,aAAa,CAAC,OAAO,EAAE,sBAAsB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC1F,CAAC;IAED,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,iCAAiC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,cAAc,CAAC;AACxB,CAAC;AApCD,8CAoCC;AAED,uBAAuB,OAAY,EAAE,kBAA0B,EAAE,SAAc,EAAE,WAAgB;IAC/F,MAAM,kBAAkB,GAAuB;QAC7C,OAAO;QACP,UAAU,EAAE,kBAAkB;QAC9B,SAAS;QACT,MAAM,EAAE,KAAK;KACd,CAAC;IACF,MAAM,UAAU,GAAG,uCAAiB,CAAC,kBAAkB,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;IAE/E,MAAM,CAAC,cAAc,CAAC;AACxB,CAAC"}