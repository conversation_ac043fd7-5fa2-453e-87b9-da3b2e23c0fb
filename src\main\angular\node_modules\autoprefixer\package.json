{"_args": [["autoprefixer@6.7.7", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "autoprefixer@6.7.7", "_id": "autoprefixer@6.7.7", "_inBundle": false, "_integrity": "sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ=", "_location": "/autoprefixer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "autoprefixer@6.7.7", "name": "autoprefixer", "escapedName": "autoprefixer", "rawSpec": "6.7.7", "saveSpec": null, "fetchSpec": "6.7.7"}, "_requiredBy": ["/@angular/cli", "/cssnano"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/autoprefixer/-/autoprefixer-6.7.7.tgz", "_spec": "6.7.7", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/postcss/autoprefixer/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/harianus"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@yandex-team.ru"}, {"name": "<PERSON><PERSON><PERSON>", "email": "shva<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/Andersos"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "aruseni", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/bernig"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brainopia", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> Le", "email": "<EMAIL>"}, {"name": "Cory <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/craigmic<PERSON><PERSON><PERSON>in"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dotch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eitanr", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/badisa"}, {"name": "Google Inc."}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "GU Yiling", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON>", "email": "<EMAIL>"}, {"name": "heady", "email": "https://github.com/heady"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/igoradamenko"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jason<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "jv<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "be<PERSON><EMAIL>"}, {"name": "L.T", "email": "ec.huying<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "martco", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "mi<PERSON><PERSON><PERSON><PERSON>@me.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> Gołębiowski", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "mika<PERSON>@jorhult.se"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "m<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "nickspielgist", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ReadmeCritic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Šime Vidas", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/stevemao"}, {"name": "Stig O<PERSON>nes <PERSON>tad", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sun<PERSON>", "email": "<EMAIL>"}, {"name": "tomdavenport", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "vladkens", "email": "<EMAIL>"}, {"name": "Дани<PERSON><PERSON> Пронин", "email": "<EMAIL>"}, {"name": "一丝", "email": "<EMAIL>"}, {"name": "刘祺", "email": "<EMAIL>"}], "dependencies": {"browserslist": "^1.7.6", "caniuse-db": "^1.0.30000634", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "postcss": "^5.2.16", "postcss-value-parser": "^3.2.3"}, "description": "Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website", "devDependencies": {"browserify": "^14.1.0", "coffee-script": "^1.12.4", "eslint-config-postcss": "^2.0.2", "fs-extra": "^2.0.0", "gulp": "^3.9.1", "gulp-coffee": "^2.3.4", "gulp-eslint": "^3.0.1", "gulp-json-editor": "^2.2.1", "gulp-mocha": "^3.0.1", "gulp-replace": "^0.5.4", "mocha": "^3.2.0", "should": "^11.2.1", "vinyl-source-stream": "^1.1.0"}, "eslintConfig": {"extends": "eslint-config-postcss/es5"}, "homepage": "https://github.com/postcss/autoprefixer#readme", "keywords": ["autoprefixer", "css", "prefix", "postcss", "postcss-plugin"], "license": "MIT", "main": "lib/autoprefixer", "name": "autoprefixer", "repository": {"type": "git", "url": "git+https://github.com/postcss/autoprefixer.git"}, "scripts": {"test": "gulp"}, "version": "6.7.7"}