{"_args": [["blob@0.0.4", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "blob@0.0.4", "_id": "blob@0.0.4", "_inBundle": false, "_integrity": "sha1-vPEwUspURj8w+fx+lbmkdjCpSSE=", "_location": "/blob", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "blob@0.0.4", "name": "blob", "escapedName": "blob", "rawSpec": "0.0.4", "saveSpec": null, "fetchSpec": "0.0.4"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/blob/-/blob-0.0.4.tgz", "_spec": "0.0.4", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/rase-/blob/issues"}, "dependencies": {}, "description": "Abstracts out Blob and uses BlobBulder in cases where it is supported with any vendor prefix.", "devDependencies": {"browserify": "3.30.1", "expect.js": "0.2.0", "mocha": "1.17.1", "zuul": "1.5.4"}, "homepage": "https://github.com/rase-/blob", "name": "blob", "repository": {"type": "git", "url": "git+ssh://**************/rase-/blob.git"}, "scripts": {"test": "make test"}, "version": "0.0.4"}