{"_args": [["content-disposition@0.5.2", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "content-disposition@0.5.2", "_id": "content-disposition@0.5.2", "_inBundle": false, "_integrity": "sha1-DPaLud318r55YcOoUXjLhdunjLQ=", "_location": "/content-disposition", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "content-disposition@0.5.2", "name": "content-disposition", "escapedName": "content-disposition", "rawSpec": "0.5.2", "saveSpec": null, "fetchSpec": "0.5.2"}, "_requiredBy": ["/download", "/express"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/content-disposition/-/content-disposition-0.5.2.tgz", "_spec": "0.5.2", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Create and parse Content-Disposition header", "devDependencies": {"eslint": "3.11.1", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.3.0", "eslint-plugin-standard": "2.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-disposition#readme", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "name": "content-disposition", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-disposition.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "0.5.2"}