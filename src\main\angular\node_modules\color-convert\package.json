{"_args": [["color-convert@1.9.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_from": "color-convert@1.9.0", "_id": "color-convert@1.9.0", "_inBundle": false, "_integrity": "sha1-Gsz5fdc5uYO/mU1W/sj5WFNkG3o=", "_location": "/color-convert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color-convert@1.9.0", "name": "color-convert", "escapedName": "color-convert", "rawSpec": "1.9.0", "saveSpec": null, "fetchSpec": "1.9.0"}, "_requiredBy": ["/color", "/icss-utils/ansi-styles", "/postcss-modules-extract-imports/ansi-styles", "/postcss-modules-local-by-default/ansi-styles", "/postcss-modules-scope/ansi-styles", "/postcss-modules-values/ansi-styles", "/yargs/ansi-styles"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/color-convert/-/color-convert-1.9.0.tgz", "_spec": "1.9.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "dependencies": {"color-name": "^1.1.1"}, "description": "Plain color conversion functions", "devDependencies": {"chalk": "^1.1.1", "xo": "^0.11.2"}, "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "homepage": "https://github.com/Qix-/color-convert#readme", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "license": "MIT", "name": "color-convert", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color-convert.git"}, "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "version": "1.9.0", "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}}