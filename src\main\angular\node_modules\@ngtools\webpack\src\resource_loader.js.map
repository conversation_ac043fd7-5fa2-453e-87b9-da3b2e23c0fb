{"version": 3, "file": "resource_loader.js", "sourceRoot": "/users/hansl/sources/angular-cli/", "sources": ["src/resource_loader.ts"], "names": [], "mappings": ";;AAAA,2BAAgC;AAChC,yBAAyB;AACzB,6BAA6B;AAE7B,MAAM,kBAAkB,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAC1E,MAAM,gBAAgB,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACtE,MAAM,kBAAkB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACrE,MAAM,iBAAiB,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAInE;IAIE,YAAoB,kBAAuB;QAAvB,uBAAkB,GAAlB,kBAAkB,CAAK;QAFnC,cAAS,GAAG,CAAC,CAAC;QAGpB,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;IAC7C,CAAC;IAEO,QAAQ,CAAC,QAAgB,EAAE,QAAgB;QACjD,MAAM,YAAY,GAAG,YAAY,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC;QACrD,MAAM,aAAa,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClE,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAC/F,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QACtC,aAAa,CAAC,KAAK,CACjB,IAAI,kBAAkB,CAAC,aAAa,CAAC,EACrC,IAAI,gBAAgB,EAAE,EACtB,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC9C,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAC/B,CAAC;QAEF,mFAAmF;QACnF,IAAI,uBAAuB,GAAG,MAAM,CAAC,MAAM,CACzC,EAAE,EACF,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CACvD,CAAC;QAEF,2EAA2E;QAC3E,wEAAwE;QACxE,4EAA4E;QAC5E,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE,UAAU,WAAgB;YAC5D,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtB,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACrC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC;gBACD,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;YACjC,aAAa,CAAC,UAAU,CAAC,CAAC,GAAU,EAAE,OAAc,EAAE,gBAAqB;gBACzE,+BAA+B;gBAC/B,EAAE,CAAC,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;oBAClF,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,KAAU;wBACnE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;oBAClE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACd,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,GAAG,YAAY,CAAC,CAAC,CAAC;gBAClE,CAAC;gBAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACf,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,0CAA0C;oBAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,qBAAqB,CAC3E,YAAY,EAAE,aAAa,CAAC,QAAQ,EAAE;wBACtC,IAAI,EAAE,gBAAgB,CAAC,IAAI;wBAC3B,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;qBAClB,CAAC,CAAC;oBAEH,wFAAwF;oBACxF,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ;wBACpD,yEAAyE;wBACzE,EAAE,CAAC,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;4BACjF,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAClD,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,+CAA+C;4BAC/C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAC/E,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,OAAO,CAAC;wBACN,oCAAoC;wBACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;wBACrB,eAAe;wBACf,UAAU,EAAE,UAAU;wBACtB,iBAAiB;wBACjB,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE;qBACtD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,QAAgB,EAAE,MAAc;QAChD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAC,OAAO,EAAE,OAAO,EAAC,EAAE,MAAM,CAAC,CAAC,CAAC;YAC9E,MAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;YAE7D,mCAAmC;YACnC,IAAI,SAAiB,CAAC;YACtB,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAE7C,EAAE,CAAC,CAAC,OAAO,SAAS,IAAI,QAAQ,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,QAAQ,GAAG,4BAA4B,CAAC,CAAC;QAClF,CAAC;QAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,GAAG,CAAC,QAAgB;QAClB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,iBAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;aAC3D,IAAI,CAAC,CAAC,MAAW,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAzGD,sDAyGC"}