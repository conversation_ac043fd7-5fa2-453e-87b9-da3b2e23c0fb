{"_args": [["brorand@1.1.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "brorand@1.1.0", "_id": "brorand@1.1.0", "_inBundle": false, "_integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "_location": "/brorand", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "brorand@1.1.0", "name": "brorand", "escapedName": "brorand", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/elliptic", "/miller-rabin"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/brorand/-/brorand-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"crypto": false}, "bugs": {"url": "https://github.com/indutny/brorand/issues"}, "description": "Random number generator for browsers and node.js", "devDependencies": {"mocha": "^2.0.1"}, "homepage": "https://github.com/indutny/brorand", "keywords": ["Random", "RNG", "browser", "crypto"], "license": "MIT", "main": "index.js", "name": "brorand", "repository": {"type": "git", "url": "git+ssh://**************/indutny/brorand.git"}, "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "version": "1.1.0"}