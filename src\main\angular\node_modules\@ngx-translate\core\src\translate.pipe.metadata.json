[{"__symbolic": "module", "version": 3, "metadata": {"TranslatePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "translate", "pure": false}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./translate.service", "name": "TranslateService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "updateValue": [{"__symbolic": "method"}], "transform": [{"__symbolic": "method"}], "_dispose": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}}}, {"__symbolic": "module", "version": 1, "metadata": {"TranslatePipe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable"}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "<PERSON><PERSON>"}, "arguments": [{"name": "translate", "pure": false}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "./translate.service", "name": "TranslateService"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef"}]}], "updateValue": [{"__symbolic": "method"}], "transform": [{"__symbolic": "method"}], "_dispose": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}}}]