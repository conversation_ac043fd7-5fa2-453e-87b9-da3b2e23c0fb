{"title": "SVG effects for HTML", "description": "Method of using SVG transforms, filters, etc on HTML elements using either CSS or the foreignObject element", "spec": "http://www.w3.org/TR/SVG11/extend.html#ForeignObjectElement", "status": "rec", "links": [{"url": "https://developer.mozilla.org/en/SVG/Tutorial/Other_content_in_SVG", "title": "Mozilla Developer Network (MDN) documentation - Other content in SVG"}, {"url": "https://developer.mozilla.org/En/Applying_SVG_effects_to_HTML_content", "title": "Mozilla Developer Network (MDN) documentation - Applying SVG effects"}, {"url": "http://www.w3.org/TR/filter-effects/", "title": "Filter Effects draft"}], "bugs": [{"description": "Browsers can have differing behavior when HTML CSS properties are set on SVG elements, for example Edge & IE browsers do not respond to the `height` property while Chrome does."}], "categories": ["SVG"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #1 #2", "10": "a #1 #2", "11": "a #1 #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2"}, "firefox": {"2": "n", "3": "a", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a"}, "safari": {"3.1": "n", "3.2": "n", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "a", "7": "a", "7.1": "a", "8": "a", "9": "a", "9.1": "a", "10": "a", "10.1": "a", "11": "a", "TP": "a"}, "opera": {"9": "a", "9.5-9.6": "a", "10.0-10.1": "a", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11": "a"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a", "4.4.3-4.4.4": "a", "56": "a"}, "bb": {"7": "n", "10": "y"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "a", "37": "a"}, "and_chr": {"59": "a"}, "and_ff": {"54": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.4": "n"}, "samsung": {"4": "a", "5": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "Partial support refers to lack of filter support or buggy result from effects. A [CSS Filter Effects](http://www.w3.org/TR/filter-effects/) specification is in the works that would replace this method.", "notes_by_num": {"1": "IE11 and below do not support `<foreignObject>`.", "2": "IE and Edge do not support applying SVG filter effects to HTML elements using CSS. [Bug Report](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/6618695/)"}, "usage_perc_y": 5.91, "usage_perc_a": 78.69, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}