{"_args": [["babel-generator@6.25.0", "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular"]], "_development": true, "_from": "babel-generator@6.25.0", "_id": "babel-generator@6.25.0", "_inBundle": false, "_integrity": "sha1-M6GvcNXyiQrrRlpKd5PB32qeqfw=", "_location": "/babel-generator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-generator@6.25.0", "name": "babel-generator", "escapedName": "babel-generator", "rawSpec": "6.25.0", "saveSpec": null, "fetchSpec": "6.25.0"}, "_requiredBy": ["/istanbul-lib-instrument"], "_resolved": "https://artifactory.devops.internal.unicreditgroup.eu/artifactory/api/npm/npm-group/babel-generator/-/babel-generator-6.25.0.tgz", "_spec": "6.25.0", "_where": "C:\\projects\\ubz_uj\\ubz-efa\\UBZ-EFA-PF\\src\\main\\angular", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-types": "^6.25.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1"}, "description": "Turns an AST into code.", "devDependencies": {"babel-helper-fixtures": "^6.22.0", "babylon": "^6.17.2"}, "files": ["lib"], "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-generator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-generator"}, "version": "6.25.0"}