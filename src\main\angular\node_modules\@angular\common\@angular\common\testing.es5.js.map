{"version": 3, "file": "testing.es5.js", "sources": ["../../../../../packages/common/testing/index.ts", "../../../../../packages/common/testing/src/testing.ts", "../../../../../packages/common/testing/src/mock_location_strategy.ts", "../../../../../packages/common/testing/src/location_mock.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\nexport {SpyLocation} from './location_mock';\nexport {MockLocationStrategy} from './mock_location_strategy';\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @stable\n */\n\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  constructor() { super(); }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n\n  path(includeHash: boolean = false): string { return this.internalPath; }\n\n  prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  pushState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  replaceState(ctx: any, title: string, path: string, query: string): void {\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  onPopState(fn: (value: any) => void): void { this._subject.subscribe({next: fn}); }\n\n  getBaseHref(): string { return this.internalBaseHref; }\n\n  back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  forward(): void { throw 'not implemented'; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @experimental\n */\n\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '')];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _baseHref: string = '';\n  /** @internal */\n  _platformStrategy: LocationStrategy = null !;\n\n  setInitialPath(url: string) { this._history[this._historyIndex].path = url; }\n\n  setBaseHref(url: string) { this._baseHref = url; }\n\n  path(): string { return this._history[this._historyIndex].path; }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath =\n        this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n  }\n\n  simulateUrlPop(pathname: string) { this._subject.emit({'url': pathname, 'pop': true}); }\n\n  simulateHashChange(pathname: string) {\n    // Because we don't prevent the native event, the browser will independently update the path\n    this.setInitialPath(pathname);\n    this.urlChanges.push('hash: ' + pathname);\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._baseHref + url;\n  }\n\n  go(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query));\n    this._historyIndex = this._history.length - 1;\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push(url);\n    this._subject.emit({'url': url, 'pop': false});\n  }\n\n  replaceState(path: string, query: string = '') {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? ('?' + query) : '');\n    this.urlChanges.push('replace: ' + url);\n  }\n\n  forward() {\n    if (this._historyIndex < (this._history.length - 1)) {\n      this._historyIndex++;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({'url': this.path(), 'pop': true});\n    }\n  }\n\n  subscribe(\n      onNext: (value: any) => void, onThrow?: ((error: any) => void)|null,\n      onReturn?: (() => void)|null): Object {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  normalize(url: string): string { return null !; }\nstatic decorators: DecoratorInvocation[] = [\n{ type: Injectable },\n];\n/** @nocollapse */\nstatic ctorParameters: () => ({type: any, decorators?: DecoratorInvocation[]}|null)[] = () => [\n];\n}\n\nclass LocationState {\n  path: string;\n  query: string;\n  constructor(path: string, query: string) {\n    this.path = path;\n    this.query = query;\n  }\n}\n\ninterface DecoratorInvocation {\n  type: Function;\n  args?: any[];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AGkBA;;;;GAGA;;IAEA;;QAEE,IAAF,CAAA,QAAA,GAAA,CAAsB,IAAtB,aAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;;QAEE,gBAAF;QA6FA,IAAA,CAAA,QAAA,GAAA,IAAA,YAAA,EAAA,CAAA;QA3FA,gBAAA;QAEA,IAAA,CAAA,SAAA,GAAA,EAAiC,CAAC;QAE5B,gBAAwB;QAE9B,IAAA,CAAA,iBAAA,GAAA,IAAA,CAAA;IACA,CAAA;IACA,oCAAA,GAAA,UAAA,GAAkB,IAAlB,IACa,CADb,QAAA,CAAA,IAAA,CAC4B,aAAa,CADzC,CAAA,IAC+C,GAD/C,GAAA,CAAA,CAAA,CAAA;IAGA,iCAAW,GAAX,UAAA,GAAA,IAAmB,IAAI,CAAvB,SAAA,GAAoC,GAApC,CAAA,CAAyC,CAAC;IAC1C,0BAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;IAEE,0CAAF,GAAE,UAAF,IAAA,EAAqC,KAArC;QAAqC,sBAAA,EAAA,UAArC;QAEA,IAAA,SAAqB,GAArB,IAAA,CAAqC,QAArC,CAAA,GAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,GAAA,IAAA,CAAA;;QAEI,MAAJ,CAAA,QAAA,IAAuB,SAAS,GAAhC,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;IACA,CAAA;IACA,oCAAA,GAAA,UAAA,QAAuB,IAAvB,IAAA,CAA+B,QAAQ,CAAvC,IAAA,CAAA,EAAA,KAAoD,EAAE,QAAQ,EAA9D,KAAA,EAAA,IAAA,EAA4E,CAAC,CAA7E,CAAA,CAAA;IACA,wCAAA,GAAA,UAAA,QAAA;QAEA,4FAAA;QACI,IAAI,CAAR,cAAA,CAAA,QAAA,CAAA,CAAA;QACA,IAAM,CAAN,UAAA,CAAkB,IAAI,CAAtB,QAAA,GAAA,QAAA,CAAA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,CAAA;IACA,CAAA;IACA,wCAAA,GAAA,UAAA,GAAA;QAEA,EAAA,CAAiB,CAAjB,GAAA,CAAA,MAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACQ,GAAG,GAAX,GAAA,GAAA,GAAA,CAAA;QAEI,CAAJ;QACA,MAAA,CAAA,IAAA,CAAA,SAAA,GAA0B,GAA1B,CAAA;IACA,CAAA;IACA,wBAAA,GAAA,UAAA,IAAA,EAAS,KAAT;QAAS,sBAAA,EAAA,UAAT;QACI,IAAI,GAAR,IAAA,CAAA,kBAAA,CAAA,IAAuC,CAAvC,CAAA;QAEI,EAAJ,CAAA,CAAA,IAAA,CAAA,aAA0B,GAA1B,CAA8B,CAAC,CAA/B,CAAA;YACQ,IAAR,CAAA,QAAqB,CAAC,MAAtB,CAAA,IAAA,CAAkC,aAAlC,GAAA,CAAmD,CAAC,CAApD;QACA,CAAA;QACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,aAAA,GAAA,IAA6B,CAAC,QAA9B,CAAuC,MAAvC,GAAA,CAAA,CAAkD;QAC9C,IAAJ,aAAA,GAAA,IAA8B,CAA9B,QAAA,CAAA,IAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA;QACI,EAAJ,CAAA,CAAQ,aAAR,CAAsB,IAAtB,IAA6B,IAA7B,IAAA,aAAiD,CAAC,KAAlD,IAAA,KAAA,CAAA,CAAA,CAAA;YACA,MAAA,CAAA;QAEA,CAAA;QACI,IAAJ,GAAA,GAAgB,IAAhB,GAAA,CAAA,KAAA,CAAA,MAAA,GAAuC,CAAC,GAAxC,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;QAEI,IAAJ,CAAA,UAAA,CAAoB,IAAI,CAAC,GAAzB,CAAA,CAAA;QACI,IAAI,CAAR,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAgC,GAAhC,EAAA,KAAA,EAAA,KAAiD,EAAjD,CAAA,CAAA;IACA,CAAA;IACA,kCAAA,GAAA,UAAA,IAAA,EAAA,KAAA;QAAA,sBAAA,EAAA,UAAA;QAEI,IAAJ,GAAW,IAAX,CAAgB,kBAAhB,CAAA,IAAA,CAAA,CAAA;QACI,IAAJ,OAAiB,GAAG,IAApB,CAAyB,QAAzB,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;QAEI,EAAJ,CAAA,CAAA,OAAA,CAAgB,IAAI,IAAI,IAAxB,IAAA,OAAuC,CAAC,KAAxC,IAAA,KAAA,CAAuD,CAAvD,CAAA;YACQ,MAAR,CAAA;QACA,CAAA;QAEA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA;QACI,OAAJ,CAAY,KAAZ,GAAA,KAAA,CAA0B;QAC1B,IAAA,GAAW,GAAX,IAAA,GAAA,CAAA,KAA2B,CAA3B,MAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;QACA,IAAM,CAAN,UAAA,CAAA,IAAA,CAAA,WAAiC,GAAjC,GAAA,CAAA,CAAA;IACA,CAAA;IACA,6BAAA,GAAA;QAEM,EAAN,CAAA,CAAA,IAAA,CAAA,aAAA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACQ,IAAI,CAAC,aAAa,EAA1B,CAA6B;YACvB,IAAI,CAAC,QAAX,CAAA,IAAwB,CAAxB,EAA2B,KAA3B,EAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;IACA,0BAAA,GAAA;QAEA,EAAA,CAAA,CAAA,IAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YAGA,IAAA,CAAA,aAAA,EAAA,CAAA;YACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;QAEA,CAAA;;IACA,+BAAA,GAAA,UAAA,MAAA,EAAiB,OAAjB,EAAA,QAAA;QACA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,CAAA;IACA,CAAA;;IAEO,kBAAP;AAAO,CAAP,IAAA;AAIA,WAAA,CAAA,UAAA,GAAA;IAGE,EAAF,IAAA,EAAA,UAA4B,EAA5B;CACA,CAAA;AACA,kBAAc;AACd,WAAA,CAAA,cAAA,GAAA,cAAA,OAAA,EAAA,EAAA,CAAA,CAAA;AACA;;QDjIA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA;;;;CCiIA;;;;;;;;;;;;;GD3GA;AACA;IAAA,gDAAA;IACA;QAAA,sCAUA;QARE,KAAF,CAAA,gBAAA,GAAA,GAAA,CAAA;QACA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA;QAEA,KAAA,CAAA,aAAA,GAAA,EAAA,CAAA;QACI,KAAI,CAAC,UAAT,GAAA,EAAwB,CAAxB;QACI,gBAAJ;QACA,KAAA,CAAA,QAAA,GAAA,IAAA,YAAA,EAAA,CAAA;;IAEE,CAAF;IAEE,+CAAF,GAAE,UAAF,GAAA;QACI,IAAI,CAAR,YAAA,GAAA,GAA2B,CAAC;QAC5B,IAAM,CAAN,QAAA,CAAA,IAAA,CAAA,IAAA,kBAAA,CAAA,IAAA,CAAA,IAAA,EAAuD,CAAC,CAAC,CAAC;IAC1D,CAAA;IACA,mCAAI,GAAJ,UAAA,WAAA;QAAA,4BAAA,EAAA,mBAAA;QAAA,MAAA,CAAmC,IAAnC,CAAA,YAAA,CAAA;IAAA,CAAA;IACA,iDAAA,GAAA,UAAA,QAAA;QAEA,EAAA,CAAA,CAAA,QAAA,CAAA,UAAA,CAAmD,GAAnD,CAAA,IAAA,IAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YACQ,MAAR,CAAA,IAAA,CAAA,gBAAA,GAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;QAEI,CAAJ;QACI,MAAJ,CAAA,IAAA,CAAA,gBAAA,GAAA,QAAA,CAAA;IAEA,CAAA;IACA,wCAAS,GAAT,UAAA,GAAA,EAAA,KAAoB,EAApB,IAAA,EAAA,KAAA;QACA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA;QAEA,IAAA,GAAe,GAAQ,IAAvB,GAAsC,CAAtC,KAAoD,CAApD,MAAmE,GAAnE,CAAA,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;QACI,IAAI,CAAC,YAAT,GAAA,GAAA,CAAA;QAEI,IAAM,WAAV,GAAwB,IAAxB,CAA6B,kBAAkB,CAA/C,GAAA,CAAA,CAAA;QACI,IAAI,CAAC,UAAT,CAAA,IAAwB,CAAxB,WAAA,CAAA,CAAA;IAEA,CAAA;IACA,2CAAA,GAAA,UAAA,GAAA,EAAA,KAAA,EAAyB,IAAzB,EAAA,KAAoC;QACpC,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA;QAEA,IAAY,GAAyB,GAArC,IAAA,GAAA,CAAA,KAAA,CAA4D,MAA5D,GAAA,CAAsE,GAAE,CAAxE,GAA4E,GAA5E,KAAmF,CAAnF,GAAA,EAAA,CAAA,CAAA;QAEA,IAAA,CAAA,YAAA,GAAA,GAAqC,CAAC;QAEhC,IAAN,WAAA,GAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA;QACI,IAAI,CAAR,UAAA,CAAA,IAAwB,CAAxB,WAAoC,GAApC,WAAA,CAAA,CAAA;IACA,CAAA;IACA,yCAAA,GAAA,UAAA,EAAA,IAAA,IAAmB,CAAnB,QAAA,CAAA,SAAsC,CAAtC,EAAA,IAAA,EAA+C,EAA/C,EAAmD,CAAnD,CAAA,CAAA,CAAuD;IACvD,0CAAA,GAAA,cAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;IACA,mCAAA,GAAA;QACA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;YAEA,IAAoB,CAApB,UAAA,CAAA,GAAA,EAAA,CAAA;;YACA,IAAA,CAAA,gBAAA,CAA2C,OAA3C,CAAA,CAAA;QACA,CAAA;IACA,CAAA;;IAEO,2BAAP;AAAO,CAAP,AAzDA,CAAA,gBAAA,GAyDA;AAIA,oBAAA,CAAA,UAAA,GAAA;IAGE,EAAF,IAAA,EAAA,UAAmC,EAAnC;CAAA,CAAA;AAFA,kBAAA;AACA,oBAAiB,CAAjB,cAAA,GAAA,cAAA,OAAA,EAAA,EAAA,CAAA,CAAA;AACA;IACA,4BAAA,MAAA;;QDxFA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA;;;;CCuFA;;;;;;;GD3EG;;;;;;;;;;;;GDAA;;;;;;;"}